<?php

use App\Http\Controllers\FlowEngines\ConfigurableVariableController;
use Illuminate\Support\Facades\Route;

Route::name('flow-engines.')->prefix('flow-engines')->group(function () {
    Route::prefix('/configurable-variables')->controller(ConfigurableVariableController::class)->group(function () {
        Route::get('/', 'getConfigurableVariableData');
        Route::post('/', 'saveConfigurableVariable');
        Route::delete('/{variableId}', 'deleteConfigurableVariable');
    });
});
