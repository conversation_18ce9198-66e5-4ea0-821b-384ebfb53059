<?php

use App\Http\Controllers\AgedLeads\AgedLeadsController;
use App\Http\Controllers\API\Advertising\AdvertisingAPIController;
use App\Http\Controllers\API\Appointments\CompanyCampaignAppointmentsController;
use App\Http\Controllers\API\Calendar\CalendarWebhookController;
use App\Http\Controllers\API\ClientDashboard\AppointmentCalendarIntegrationController;
use App\Http\Controllers\API\CompaniesApiController;
use App\Http\Controllers\API\CompanyRegistration\CompanyRegistrationController;
use App\Http\Controllers\API\CompanyRegistration\CompanyRegistrationV3Controller;
use App\Http\Controllers\API\Discovery\CompanyProfileAPIController;
use App\Http\Controllers\API\ExternalAuthController;
use App\Http\Controllers\API\LeadProcessing\Processing\ProcessingBaseController;
use App\Http\Controllers\API\ConsumerReviewsPublicController;
use App\Http\Controllers\API\PingPostAffiliatesApiController;
use App\Http\Controllers\API\OpportunityNotifications\OpportunityNotificationsApiController;
use App\Http\Controllers\API\PublicAPI\LeadsController as LeadsControllerPublic;
use App\Http\Controllers\API\Webhooks\BillingWebhookController;
use App\Http\Controllers\API\Webhooks\MailboxWebhookController;
use App\Http\Controllers\API\Webhooks\Marketing\MarketingWebhookController;
use App\Http\Controllers\API\Webhooks\TestProductWebhookController;
use App\Http\Controllers\API\Webhooks\Twilio\CompanyProfileCallController;
use App\Http\Controllers\API\Webhooks\Twilio\ConsumerProxyPhoneWebhookController;
use App\Http\Controllers\API\Webhooks\Twilio\TwilioWebhookController;
use App\Http\Controllers\ContractorProfileController;
use App\Http\Controllers\FlowManagementProxyController;
use App\Http\Controllers\GoogleOAuthController;
use App\Http\Controllers\Odin\API\CompanyLocationSiloController;
use App\Http\Controllers\Odin\API\ComponentDataController;
use App\Http\Controllers\Odin\API\Engines\LegacySolarEngineController;
use App\Models\ClientTokenService;
use App\Services\HypnotoadQueueService;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\Webhooks\DocuSignApiWebhookController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/
Route::prefix('webhooks')->group(function() {
    Route::prefix('twilio')->middleware(['authorize_twilio'])->group(function() {
        Route::post('/sms', [TwilioWebhookController::class, 'sms']);
        Route::post('/voice', [TwilioWebhookController::class, 'voice']);
        Route::post('/call-status', [TwilioWebhookController::class, 'callStatus'])->name('twilio-call-status');

        Route::post('/test-product-sms', [TestProductWebhookController::class, 'sms'])->name('test-product-sms');
        Route::post('/test-product-voice', [TestProductWebhookController::class, 'voice'])->name('test-product-voice');
        Route::post('/consumer-sms', [TwilioWebhookController::class, 'consumerSMS']);
        Route::post('/recycled-leads-sms', [TwilioWebhookController::class, 'recycledLeadsSms']);
        Route::post('/consumer-proxy-phone-sms', [ConsumerProxyPhoneWebhookController::class, 'sms'])->name('consumer-proxy-phone-sms');
        Route::post('/consumer-proxy-phone-sms-reply', [ConsumerProxyPhoneWebhookController::class, 'smsReply'])->name('consumer-proxy-phone-sms-reply');
        Route::post('/consumer-proxy-phone-voice', [ConsumerProxyPhoneWebhookController::class, 'voice'])->name('consumer-proxy-phone-voice');
        Route::post('/consumer-proxy-phone-call-status', [ConsumerProxyPhoneWebhookController::class, 'callStatus'])->name('consumer-proxy-phone-call-status');
        Route::post('/company-profile-voice', [CompanyProfileCallController::class, 'voice'])->name('company-profile-voice');
        Route::post('/company-profile-call-status', [CompanyProfileCallController::class, 'callStatus'])->name('company-profile-call-status');
    });

    Route::prefix('mailslurp')->group(function () {
        Route::post('/test-product-email', [TestProductWebhookController::class, 'email'])->name('test-product-email');
    });

    // TODO: Add middleware for authorizing incoming subscriptions.
    Route::prefix('subscriptions')->group(function() {
        Route::post('/', [\App\Http\Controllers\API\Webhooks\Subscriptions\SubscriptionController::class, 'handleEvent']);
    });

    Route::prefix('docusign')->group(function () {
        Route::post('/', [DocuSignApiWebhookController::class, 'handleEvent']);
    });

    Route::prefix('fixr')->middleware('external_website_token')->group(function () {
        Route::get('/consumer/{marketingConsumerReference}', [AgedLeadsController::class, 'getByMarketingCampaignConsumerReference']);
        Route::post('/consumer/allocate/{consumerReference}', [AgedLeadsController::class, 'allocateConsumer']);
        Route::prefix('/v2')->group(function () {
            Route::get('/consumer/{consumerReference}', [AgedLeadsController::class, 'getByConsumerAndCampaignReference']);
        });
        Route::prefix('/companies/{reference}')->controller(CompaniesApiController::class)->group(function () {
            Route::patch('/save-opt-in-name', 'saveCompanyOptInName');
            Route::get('/campaigns', 'getCampaigns');
            Route::post('/campaigns/save-opt-in-names', 'saveOptInNames');
        });
        Route::post('/unsubscribe-company-user', [OpportunityNotificationsApiController::class, 'unsubscribeUser']);
    });

    Route::prefix('mail')->group(function (){
        Route::get('oauth', [MailboxWebhookController::class, 'handleOAuthCallback']);
        Route::post('event', [MailboxWebhookController::class, 'handleEmailEvent']);
    });

    Route::post('google/calendar/event', [CalendarWebhookController::class, 'handleEvent'])->name('calendar-event-handler');
    Route::get('google/oauth', [GoogleOAuthController::class, 'handleOAuthCallback'])->name('google-oauth-callback');

    Route::prefix('billing')->controller(BillingWebhookController::class)->group(function (){
        Route::post('event','handleEvent');
    });

    Route::prefix('marketing')->controller(MarketingWebhookController::class)->group(function () {
        Route::get('/email', [MarketingWebhookController::class, 'handleEmailEvent']);
        Route::post('/email', [MarketingWebhookController::class, 'handleEmailEvent']);
        Route::post('/sms', [MarketingWebhookController::class, 'handleSMSEvent']);
    });
});

Route::prefix('a-leads')->middleware('a-leads-middleware')->controller(PingPostAffiliatesApiController::class)->group(function() {
    Route::post('/new-lead', 'createPingPostAffiliateLead');
});

Route::prefix('/ai-leads')->middleware(['legacy_admin_bearer_token'])->controller(LeadsControllerPublic::class)->group(function () {
    Route::post('/clone', 'clone');
    Route::get('/get-solar-requalification-consumer-products', function () {
        return response()->json(HypnotoadQueueService::getNextConsumerProducts());
    });
});

Route::post('init-lead', [ProcessingBaseController::class, 'createInitialLead'])->middleware(['legacy_admin_bearer_token'])->withoutMiddleware(['throttle:api']);

Route::get('advertising-oauth-redirect/{platform}', [AdvertisingAPIController::class, 'handleTokenAuthRedirect'])->name("advertising-oauth-redirect");

Route::get('/appointments-calendar-integration-oauth-redirect/{platform}', [AppointmentCalendarIntegrationController::class, "handleOauthRedirect"]);

Route::prefix('industries')->middleware('external_website_token')->controller(\App\Http\Controllers\Odin\API\IndustryAPIController::class)->group(function() {
    Route::get('/', 'getIndustries');
    Route::get('/{industry}', 'getIndustryDetail');
    Route::get('/{industry}/companies', 'getIndustryCompanies');
    Route::get('/{industry}/services', 'getIndustryServices');
    Route::get('/{industry}/services/{industryService}/companies', 'getIndustryServicedCompanies');
});

Route::post('/validate-auth-token', [ExternalAuthController::class, 'validateAuthToken']);

Route::prefix('company-discovery')->middleware('authenticate-client-token:' . ClientTokenService::COMPANY_DISCOVERY_API_SERVICE_KEY)->group(function () {
    Route::post('/', [CompanyProfileAPIController::class, 'store']);
});

Route::prefix('company-registration')->name('company-registration.')->group(function() {
    Route::prefix('/v2')->name('v2.')->controller(CompanyRegistrationController::class)->group(function() {
        // Pre-verification routes
        Route::post('/check-similar-companies', 'checkSimilarCompanies');
        Route::post('/create-base-company', 'createBaseCompany');
        Route::get('/verify-email/{id}/{hash}', 'verifyEmailAddress')
            ->middleware(['signed'])
            ->name('verification.verify');

        Route::get('/get-bundles', 'getBundles');
        Route::post('/purchase-bundle/{bundle}', 'purchaseBundle');

        Route::middleware(['registration-reference'])->group(function () {
            Route::get('/send-email-verification', 'sendEmailVerification')
                ->name('verification.notice');
            Route::post('/send-phone-verification', 'sendPhoneVerification');
            Route::post('/verify-phone', 'verifyPhone');

            // Post-verification group
            Route::middleware(['registration-validated'])->group(function () {
                Route::get('/get-company-industries', 'getCompanyIndustries');
                Route::post('/update-company-industries', 'updateCompanyIndustries');
                Route::get('/get-company-services', 'getCompanyServices');
                Route::post('/update-company-services', 'updateCompanyServices');
                Route::post('/get-company-contract', 'getCompanyContract');
                Route::post('/accept-company-contract', 'acceptCompanyContract');
                Route::get('/download-company-contract', 'downloadCompanyContract');
                Route::post('/add-company-location', 'addLocationToCompany');
                Route::post('/add-company-service-area', 'addLServiceAreaToCompany');
                Route::post('/credit-card-added', 'addPaymentMethodToCompany');
                Route::get('/get-company-locations', 'getCompanyLocations');
                Route::post('/update-company-details', 'updateCompanyDetails');

                Route::prefix('lead-prices')->group(function() {
                    Route::post('/price-range-sales-type-default-campaign', 'getPriceRangeSalesTypeForDefaultCampaign');
                });

                Route::put('/set-lead-budget', 'setLeadBudgetOfCreatedCompany');
                Route::post('/set-lead-contact', 'setLeadContactOfCreatedCompany');

                Route::post('/generate-dashboard-token', 'generateDashboardToken');
                Route::patch('/update-company-business-details', 'updateCompanyBusinessDetails');
            });
        });
    });
    Route::prefix('/v3')->controller(CompanyRegistrationV3Controller::class)->group(function () {
        Route::get('/init', 'initRegistration');
        Route::post('/buyer-prospects', 'createBuyerProspect');
        Route::patch('/buyer-prospects/{newBuyerProspect:reference}', 'updateBuyerProspect');
        Route::get('/buyer-prospects/{companyUser:reference}/dashboard-token', 'getDashboardLoginToken');
        Route::post('/buyer-prospects/{newBuyerProspect:reference}/send-phone-verification-code', 'sendPhoneVerificationCode');
        Route::post('/buyer-prospects/{newBuyerProspect:reference}/verify-phone', 'verifyPhone');
        Route::post('/buyer-prospects/{newBuyerProspect:reference}/verify-email', 'verifyEmail');
        Route::get('/buyer-prospects/{newBuyerProspect:reference}/lead-prices', 'getLeadPrices');
    });
});

Route::middleware(['check_appointment_host', 'validate_appointment_key_code'])->group(function() {
    Route::prefix('/consumer-appointment')->middleware(['authenticate_consumer_appointment'])->controller(CompanyCampaignAppointmentsController::class)->group(function() {
        Route::post('cancel', 'cancelConsumerAppointment');
        Route::get('get', 'getConsumerAppointment');
        Route::get('/cancellation-reasons', 'getCancellationReasonOptions');
    });
});

Route::prefix('/v2')->group(function() {
    Route::prefix('/engines')->group(function() {
        Route::prefix('/legacy-solar')->controller(LegacySolarEngineController::class)->group(function () {
            Route::get('/estimate', 'getEstimate');
            Route::get('/utilities/{zipCode}', 'getUtilitiesByZipCode');
            Route::get('/fallback-utilities/{zipCode}', 'getFallbackUtilitiesByZipCode');
        });
    });

    Route::post('/company-location-silos', [CompanyLocationSiloController::class, 'getSiloData']);
    Route::get('/company-location-silos', [CompanyLocationSiloController::class, 'getEntry']);

    Route::get('/contractor-profile', [ContractorProfileController::class, 'getProfileEntry']);
    Route::get('/specialist-profile/{profileSlug}', [CompanyProfileAPIController::class, 'show'])->middleware('external_website_token');
    Route::get('/top-specialists', [CompanyProfileAPIController::class, 'listTopCompanies'])->middleware('external_website_token');

    Route::post('/component-data', [ComponentDataController::class, 'getComponentData']);

    Route::prefix('reviews')->name('consumer-reviews.')->controller(ConsumerReviewsPublicController::class)->group(function() {
        Route::get('/company-details', 'getCompanyDetails');
        Route::get('/search-companies', 'searchCompanies');
        Route::post('/create-review', 'createReview')->middleware('rate-limit');
        Route::post('/create-review-with-attachments', 'createReviewWithAttachments')->middleware('rate-limit');
        Route::post('/verify-zip-code', 'verifyZipCode');

        Route::prefix('/{review:uuid}')->group(function () {
            Route::get('/', 'getReview');
            Route::post('/send-phone-verification', 'sendPhoneVerification');
            Route::post('/verify-phone', 'verifyPhone');
            Route::post('/send-email-verification', 'sendEmailVerification');
            Route::post('/verify-email', 'verifyEmailAddress');
        });
    });
});

Route::get('/flows/options', [FlowManagementProxyController::class, 'getFlowOptions'])
    ->middleware('authenticate-client-token:' . ClientTokenService::HOW_MUCH);
