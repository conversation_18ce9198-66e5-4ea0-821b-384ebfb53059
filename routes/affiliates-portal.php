<?php

use App\Http\Controllers\API\Affiliates\Portal\CategoriesController;
use App\Http\Controllers\API\Affiliates\Portal\AffiliateController;
use App\Http\Controllers\API\Affiliates\Portal\CampaignController;
use App\Http\Controllers\API\Affiliates\Portal\CampaignLeadDetailsController;
use App\Http\Controllers\API\Affiliates\Portal\StatisticsController;
use App\Http\Controllers\API\Affiliates\ShadowAffiliateController;
use Illuminate\Support\Facades\Route;

Route::name('affiliates-portal.v1.')->middleware(['authenticate-affiliates-portal'])
    ->group(function() {
        Route::post('shadow', ShadowAffiliateController::class)->name('shadow');
        Route::prefix('affiliates')->name('affiliates.')->group(function () {
            Route::prefix('{affiliate:uuid}')->group(function () {
                Route::get('/', [AffiliateController::class, 'show'])->name('show');
                Route::prefix('campaigns')->name('campaigns.')->group(function () {
                    Route::get('/', [CampaignController::class, 'index'])->name('index');
                    Route::post('/', [CampaignController::class, 'store'])->name('store');
                    Route::prefix('{campaign}')->group(function () {
                        Route::get('/', [CampaignController::class, 'show'])->name('show');
                        Route::patch('/', [CampaignController::class, 'update'])->name('update');
                        Route::delete('/', [CampaignController::class, 'destroy'])->name('destroy');
                        Route::prefix('/lead-details')->name('lead-details.')->group(function () {
                            Route::get('/', [CampaignLeadDetailsController::class, 'index'])->name('index');
                        });
                    });
                });
                Route::prefix('categories')->name('categories.')->group(function () {
                    Route::get('/', [CategoriesController::class, 'index'])->name('index');
                    Route::post('/', [CategoriesController::class, 'store'])->name('store');
                    Route::prefix('{category}')->group(function () {
                        Route::get('/', [CategoriesController::class, 'show'])->name('show');
                        Route::patch('/', [CategoriesController::class, 'update'])->name('update');
                        Route::delete('/', [CategoriesController::class, 'destroy'])->name('destroy');
                    });
                });
            });
        });
        Route::get('statistics', StatisticsController::class)->name('statistics');
});
