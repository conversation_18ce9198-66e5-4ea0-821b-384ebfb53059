<?php

use App\Http\Controllers\AppointmentController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Public Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register publicly accessible web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "public-web" middleware group. Enjoy building your website!
|
*/

Route::middleware(['check_appointment_host'])->controller(AppointmentController::class)->group(function() {
    Route::get('/ap', 'cancelIndex')->middleware(['check_appointment_cancellable']);
});
