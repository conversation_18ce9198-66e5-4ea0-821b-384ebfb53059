<?php

use App\Http\Controllers\Metrics\BookedDemosController;
use App\Http\Controllers\Metrics\RangeController;
use App\Http\Controllers\Metrics\SuccessfulDemosController;
use App\Http\Controllers\Metrics\TalkTimeController;
use App\Http\Controllers\Metrics\TotalCallsController;
use Illuminate\Support\Facades\Route;

Route::name('metrics.')->prefix('metrics')->group(function () {
    Route::get('/ranges', RangeController::class)->name('ranges');
    Route::get('/calls', TotalCallsController::class)->name('calls');
    Route::get('/talk-time', TalkTimeController::class)->name('talk-time');
    Route::get('/booked-demos', BookedDemosController::class)->name('booked-demos');
    Route::get('/successful-demos', SuccessfulDemosController::class)->name('successful-demos');
});
