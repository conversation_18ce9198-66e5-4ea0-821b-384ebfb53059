<?php
use HeapsGoodServices\PrivacyRequest\Http\Resource\PrivacyRecordResource;
use HeapsGoodServices\PrivacyRequest\Http\Resource\PrivacyRedactResource;
use HeapsGoodServices\PrivacyRequest\Services\PrivacyRequestService;
use Illuminate\Support\Facades\Route;

Route::prefix('privacy-request')->group(function () {
    Route::get('/search', function (\Illuminate\Http\Request $request) {
        $results = PrivacyRequestService::make()->getPrivacyRecords($request->collect('search'));
        return PrivacyRecordResource::collection($results);
    });
    Route::put('/redact', function(\Illuminate\Http\Request $request) {
        $results = PrivacyRequestService::make()->redactPrivacyRecords($request->collect('redact'));
        return PrivacyRedactResource::collection($results);
    });
});
