<?php

use App\Http\Controllers\API\BundleManagement\BundleInvoiceApiController;
use App\Http\Controllers\Odin\ResourceAPI\CompanyLocationController;
use App\Http\Controllers\Odin\ResourceAPI\CompanyRegistrationController;
use App\Http\Controllers\Odin\ResourceAPI\CompanySyncController;
use App\Http\Controllers\Odin\ResourceAPI\CompanyUserSyncController;
use App\Http\Controllers\Odin\ResourceAPI\ConsumerController;
use App\Http\Controllers\Odin\ResourceAPI\ConsumerTcpaController;
use App\Http\Controllers\Odin\ResourceAPI\ConsumerTrackingController;
use App\Http\Controllers\Odin\ResourceAPI\v2\ConsumerController as ConsumerControllerV2;
use App\Http\Controllers\FlowManagementProxyController;
use App\Models\Odin\Company;
use App\Services\Companies\CompanyProfileCallService;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Odin\API\FlowEnginesController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group.
|
|--------------------------------------------------------------------------
| Odin
|--------------------------------------------------------------------------
|
| A place for Admin2.0 routes to gather. The future is here, bury the legacy.
| API versions:
|   /v1 - Legacy is the source of truth, Admin2.0 syncs whatever data is received
|   /v2 - Admin2.0 is the source of truth, and will send sync data back to Legacy
|   /v3 - Grab the shotgun and the shovel, it's buryin' time
|
*/

Route::prefix('/v2')->group(function () {
    Route::prefix('/consumer')->controller(ConsumerControllerV2::class)->group(function () {
        Route::post('/create', 'createConsumer');

        Route::prefix('/{uuid}')->group(function () {
            Route::get('/', 'getConsumerSummary');
            Route::patch('/update', 'updateConsumer');
            Route::patch('/attach-affiliate', 'attachAffiliate');
            Route::patch('/send-calculator-results', 'sendCalculatorResultsToConsumer');
            Route::post('/send-verification-email', 'sendVerificationEmail');
        });

        Route::post('/verify-email', 'verifyConsumerEmail');
    });

    Route::prefix('/engines')->controller(FlowEnginesController::class)->group(function () {
        Route::get('/get-active-campaigns-for-opt-in', 'getActiveCampaignsForOptIn');
    });

    Route::post('/deleted-revision-accessed/{revisionId}', function(FlowManagementProxyController $flowManagementProxyController, string $revisionId) {
        $flowManagementProxyController->handleDeletedRevisionAccess($revisionId);
    });
});

Route::prefix('/v1')->group(function () {
    Route::prefix('/company-registration')->controller(CompanyRegistrationController::class)->group(function () {
        Route::post('/create-base-company', 'createBaseCompany');
        Route::post('/add-locations', 'createCompanyLocation');
        Route::patch('/accept-lead-contract', 'acceptLeadContract');
        Route::patch('/update-company-details', 'updateCompanyDetails');
        Route::patch('/add-logo', 'addCompanyLogo');
    });

    Route::prefix('/company')->controller(CompanySyncController::class)->group(function() {
        Route::patch('/sync-legacy-id', 'syncLegacyId');
        Route::get('/id', 'getCompanyId');
    });


    Route::prefix('/company-user')->controller(CompanyUserSyncController::class)->group(function() {
        Route::patch('/assign-legacy', 'assignLegacyFieldsAfterBackwardSync');
        Route::patch('/sync-legacy-id', 'syncLegacyId');
    });

    Route::prefix('/company-location')->controller(CompanyLocationController::class)->group(function() {
        Route::patch('/assign-address-legacy-id', 'assignLegacyIdToNewAddress');
        Route::patch('/assign-address-legacy-id-from-importer', 'assignLegacyIdToNewAddressFromImporter');
    });

    //TODO: we still have a couple of sites running old calculators, before removing the rest of these
    Route::prefix('/consumer')->controller(ConsumerController::class)->group(function () {
        Route::post('/create', 'createConsumer');
        Route::post('/affiliate-record/create', 'createAffiliateRecord');
        Route::get('/id', 'getConsumerId');
        Route::get('/get-product-details', 'getConsumerProductDetails');
        Route::get('/next-allocation-time/{leadId}', 'getNextAllocationTime');

        Route::prefix('/tcpa')->controller(ConsumerTcpaController::class)->group(function () {
            Route::post('/create', 'createTcpaRecord');
        });

        Route::prefix('/tracking')->controller(ConsumerTrackingController::class)->group(function() {
            Route::post('/create', 'createConsumerTracking');
        });
    });

    Route::prefix('/bundle-invoice')->controller(BundleInvoiceApiController::class)->group(function() {
        Route::get('/', 'index');
    });

    Route::get('/companies/{companyId}/get-proxy-phone', function (int $companyId, CompanyProfileCallService $service) {
        return response()->json([
            'phone' => $service->getProxyPhone(Company::query()->findOrFail($companyId))
        ]);
    });
});
