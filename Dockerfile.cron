# Build Stage
FROM composer:2.7.2 as build
ARG github_auth_api_key

WORKDIR /app
COPY . /app
RUN mkdir -p /app/storage/framework/cache
RUN mkdir -p /app/storage/framework/sessions
RUN mkdir -p /app/storage/framework/views
RUN mkdir -p /app/storage/framework/testing
RUN mkdir -p /var/service-accounts/
RUN mkdir -p /root/.ssh
RUN touch /root/.ssh/known_hosts
RUN ssh-keyscan -t rsa bitbucket.org >> ~/.ssh/known_hosts
RUN composer config -g github-oauth.github.com $github_auth_api_key
RUN composer install  --ignore-platform-req=ext-soap --ignore-platform-req=ext-gd --ignore-platform-req=ext-grpc --ignore-platform-req=ext-pcntl

FROM node:20.12.0-alpine3.19 as frontend
WORKDIR /app
COPY --from=build /app /app
RUN npm ci
RUN npm run build

FROM gcr.io/admin-solarreviews/base:latest
ARG google_ads_service_account=""
COPY --from=frontend /app /app

RUN echo "* * * * * root php /app/artisan schedule:run >> /var/log/cron.log 2>&1" >> /etc/crontab
RUN touch /var/log/cron.log
RUN touch /app/.env
COPY server/export-env.sh /app/script.sh
RUN chmod +x /app/script.sh
RUN chown -R www-data:www-data /app

COPY server/php.ini /usr/local/etc/php/php.ini

RUN mkdir /google
RUN echo $google_ads_service_account | base64 -di > /google/oauth.json
RUN chown -R www-data: /google

RUN apt update
RUN apt install libxml2-dev --no-install-recommends -y
RUN docker-php-ext-install soap

EXPOSE 80
ENTRYPOINT php /app/artisan optimize && cron && /app/script.sh && supervisord -n
