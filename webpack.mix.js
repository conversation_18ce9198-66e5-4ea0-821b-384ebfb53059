const mix = require('laravel-mix');
const tailwindcss = require('tailwindcss');
require("dotenv").config();
/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel applications. By default, we are compiling the CSS
 | file for the application as well as bundling up all the JS files.
 |
 */

mix.js('resources/js/app.js', 'public/js')
    .disableNotifications()
    .vue({version: 3})
    .postCss('resources/css/app.css', 'public/css', [
        require('postcss-import'),
        tailwindcss,
        require('autoprefixer'),
    ])
    .postCss('resources/css/mail.css', 'public/css', [
        require('postcss-import'),
        tailwindcss('tailwind-mail.config.js'),
        require('autoprefixer'),
    ])
    .webpackConfig({
        module: {
            rules: [
                {
                    test: /\.(postcss)$/,
                    use: [
                        'vue-style-loader',
                        { loader: 'css-loader', options: { importLoaders: 1 } },
                        'postcss-loader'
                    ]
                }
            ],
        },
    })
    .sourceMaps();
