server {
    listen 80;
    listen [::]:80;

    error_log  /var/log/nginx/error.log;
    access_log /var/log/nginx/access.log;

    index index.php index.html;
    root /app/public;

    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }
    location / {
        if ($request_method = 'OPTIONS') {
    		add_header 'Access-Control-Allow-Origin' * always;
    		add_header 'Access-Control-Allow-Credentials' true always;
    		add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, PATCH, DELETE' always;
    		add_header 'Access-Control-Max-Age' 1728000 always;
    		add_header 'Access-Control-Allow-Headers' 'DNT,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Content-Range,Range,Authorization,X-Client-Bearer,sentry-trace' always;
    		add_header 'Access-Control-Expose-Headers' 'DNT,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Content-Range,Range,Authorization,X-Client-Bearer,sentry-trace' always;
            add_header 'Content-Type' 'text/plain; charset=utf-8' always;
            add_header 'Content-Length' 0 always;
            return 204;
    	}

        try_files $uri $uri/ /index.php?$query_string;
        gzip_static on;
    }
}
