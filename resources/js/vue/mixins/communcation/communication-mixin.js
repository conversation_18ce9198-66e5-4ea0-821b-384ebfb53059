import {CommunicationApiFactory} from "../../../services/api/communication/factory";
import DispatchesGlobalEventsMixin from "../dispatches-global-events-mixin";
import {mapWritableState} from "pinia";
import {useCallStore} from "../../../stores/call.store";
import{useContactsStore} from "../../../stores/contacts.store.js";
import {WindowService} from "../../../services/window";
import {
    CommunicationPortalLocalStorageService
} from "../../components/Communications/services/communicationLocalStorage";
import {CommunicationPusherService} from "../../components/Communications/services/communicationPusher";
import {
    CallModeEnum,
    CallStatusesEnum,
    CommunicationRelationTypes
} from "../../components/Communications/enums/communication";
import {useContactIdentificationStore} from "../../../stores/communication/contactIdentification";
import CommunicationFactory from "../../../services/communication/factory";
import {DateTime} from "luxon";
import {ApiFactory} from "../../components/LeadProcessing/services/api/factory";
import {waitForCallbackToReturnTrue} from "../../../utilities/loading-utilities";

const INIT_CALL_INFO = {
    mode: null, // COMMUNICATION_MODE
    id: null,
    status: CallStatusesEnum.CONNECTING, // CallStatusesEnum
    contactPhone: '',
    contactName: 'Unknown',
    relationId: null,
    relationType: null,
    comment: null,
    identifiedContact: null,
    contactUrl: null,
}

const INIT_CALL_STATE = {
    muted: false,
    held: false,
    minimized: false,
    active: false,
    isIdentifyingContact: false,
}

const INIT_DIALOGS_VISIBILITY = {
    isContactsSelectionDialogVisible: false,
    isIncomingDialogVisible: false,
}

const INIT_ALERTS_STATE = {
    showMicrophoneAccess: false,
    showUserNoPhoneAvailable: false, // Check if user has a phone number
}

export default {
    data() {
        return {
            deviceService: null,

            alerts: { ...INIT_ALERTS_STATE },

            dialogsVisibility: {
                ...INIT_DIALOGS_VISIBILITY
            },

            callInfo: {
                ...INIT_CALL_INFO,
            },
            callState: {
                ...INIT_CALL_STATE
            },
            isCallActiveInDetachedWindow: false,

            windowService: new WindowService(),
            communicationPortalLocalStorageService: new CommunicationPortalLocalStorageService(),
            communicationPusherService: null,
            communicationApi: null,
            api: null,

            showDialer: false,
            messengerActive: false,
            messagesLoading: false,

            shouldShowWhoTheySpokeWithModal: true,

            incoming: null,
            status: null,
            voicemails: [],
            recordings: []
        }
    },
    mixins: [DispatchesGlobalEventsMixin],
    computed: {
        ...mapWritableState(useCallStore, [
            'callActive'
        ]),
        ...mapWritableState(useContactIdentificationStore, [
            'possibleContacts',
            'isContactIdentificationModalVisible',
            'contactPhone',
            'isCallActive',
            'currentCallInfo',
            'callLogType'
        ])
    },
    props: {
        pusherAppKey: {
            type: String,
            required: true
        },
        pusherAppCluster: {
            type: String,
            required: true
        }
    },
    created() {
        this.api = ApiFactory.makeApiService(this.apiDriver);
        this.communicationApi = CommunicationApiFactory.makeApiService(this.apiDriver);

        this.communicationApi.getVoicemails().then(resp => {
            this.voicemails = resp.data.data;
        });
    },
    async mounted() {
        this.communicationPusherService = CommunicationPusherService.generate(this.pusherAppKey, this.pusherAppCluster)
        this.communicationPusherService.subscribe(this.userId)

        await this.initializeDevice();
        this.registerEvents()
        this.registerBinds()
        this.registerListeners();
    },
    methods: {
        async saveCallLog(payload){
            const method = this.callInfo.mode === CallModeEnum.OUTBOUND ? 'logOutboundCall' : 'logInboundCall'

            // Prevent create feed activity logs for wrong companies
            if (this.showIdentificationModalVisible) {
                payload['relationType'] = null
                payload['relationId'] = null
            }

            const response = await this.communicationApi[method](payload)

            this.currentCallInfo.logId = response.data.data.call_log_id
        },

        /**
         * Handle send sms message
         *
         * @param data
         * @returns {Promise<void>}
         */
        async handleSendMessage(data) {
            const body = data.message;
            const formatted = {
                direction: 'outbound',
                first_name: 'You',
                last_name: '',
                message_body: body,
                timestamp: Math.round(DateTime.now().toSeconds())
            };

            if (!this.messages) this.messages = [];

            this.messages.push(formatted);
            this.scrollMessagesDivDown();

            await this.communicationApi.createOutboundSMS(this.callInfo.contactPhone, body, this.callInfo.relationType, this.callInfo.relationId);
        },

        handleCloseMessenger() {
            this.resetContact()
            this.showDialer = false
            this.messengerActive = false
        },

        /**
         * Ask microphone permission
         *
         * @returns {Promise<void>}
         */
        async askMicrophonePermission() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({audio: true})
                if (stream) window.localStream = stream;
            } catch {
                this.alerts.showMicrophoneAccess = true
            }
        },

        /**
         * Check user has alerts to prevent errors
         *
         * @returns {boolean}
         */
        checkUserHasAlerts(){
            return Object.values(this.alerts).filter(v => v).length > 0
        },

        /**
         * Get contact info by phone number and handle call attempt
         *
         * @param phone
         * @returns {Promise<void>}
         */
        async handleDialerCall(phone) {
            this.callInfo.mode = CallModeEnum.OUTBOUND
            this.resetContact()
            this.showIdentificationModalVisible = true
            this.shouldShowWhoTheySpokeWithModal = true
            // Ask microphone permission and if denied, show error message
            await this.askMicrophonePermission()
            if (this.checkUserHasAlerts()) return

            this.callInfo.mode = CallModeEnum.OUTBOUND
            this.callState.active = true;
            this.callActive = true
            this.showDialer = false
            this.callInfo.contactPhone = phone
            this.callInfo.contactName = phone
            this.callInfo.status = CallStatusesEnum.CONNECTING
            this.callInfo.relationId = null
            this.callInfo.relationType = null
            this.callInfo.comment = ''
            this.callInfo.identifiedContact = {}
            this.currentCallInfo.relationId = null
            this.currentCallInfo.relationType = null

            this.currentCallInfo.otherNumber = phone

            this.communicationPusherService.triggerOnOutgoingCallAttempt(this.callInfo)

            // console.log('Looking up caller...')
            this.identifyNumber(phone).then()

            // console.log('handleAttemptCall...')
            await this.handleAttemptCall();
        },


        /**
         * Handle dialer attempt call
         *
         * @returns {Promise<void>}
         */
        async handleAttemptCall() {
            // Prevent duplicate calls
            if (!this.callState.active) return

            // console.log('initializeCall...')

            this.callInfo.status = CallStatusesEnum.CONNECTING
            this.communicationPusherService.triggerOnOutgoingCallRinging()

            // Create a call
            await this.deviceService.initializeCall(this.callInfo.contactPhone);

            // console.log('waitForCallbackToReturnTrue...')
            try {
                await waitForCallbackToReturnTrue(() => this.deviceService.getCallId(), 500, 20)
            } catch {
                console.error('Error trying to call')
                this.handleCallEnd()
                return
            }
        },

        /**
         * Handle call end
         *
         * @returns {Promise<void>}
         */
        async handleCallEnd() {
            this.currentCallInfo.otherNumber = this.callInfo.contactPhone
            const callId = this.deviceService.getCallId()

            const payload = {
                serviceName: this.deviceService.serviceName(),
                userPhoneNumber: this.deviceService.getUserPhone(),
                otherNumber: this.callInfo.contactPhone,
                reference: callId,
                relationType: this.currentCallInfo.relationType,
                relationId: this.currentCallInfo.relationId,
                result: 'answered'
            }

            const method = this.callInfo.mode === CallModeEnum.OUTBOUND ? 'logOutboundCall' : 'logInboundCall'

            const response = await this.communicationApi[method](payload)

            this.currentCallInfo.logId = response.data.data.call_log_id

            // console.log('Handle call end')
            this.deviceService.hangUp();
            this.currentCallInfo.sid = callId
            this.communicationPusherService.triggerOnCallEnd({ ...this.currentCallInfo, showIdentificationModalVisible: this.showIdentificationModalVisible})
            this.resetState()

            this.callInfo.status = CallStatusesEnum.ENDED
        },

        /**
         * Reset important variables to their initial state and remove call listeners
         *
         */
        resetState() {
            this.callActive = false
            this.callState = { ...INIT_CALL_STATE }
            this.callInfo.contactName = ''
            this.callInfo.relationId = ''
            this.callInfo.relationType = ''
            this.callInfo.comment = ''
            this.alerts = { ...INIT_ALERTS_STATE }
            this.showDialer = true
        },

        async showMessenger(phone) {
            this.messengerActive = true;

            this.showDialer = false
            this.callInfo = {...INIT_CALL_INFO}
            this.messagesLoading = true;

            this.callInfo.contactPhone = phone

            this.identifyNumber(phone).then()

            await this.getSMSMessages(this.callInfo.contactPhone)

            this.messagesLoading = false;
        },

        async getSMSMessages(phone){
            this.messagesLoading = true;

            const response = await this.api.getSMSHistory(phone)
            this.messages = response.data.data.messages;

            this.messagesLoading = false;

            this.scrollMessagesDivDown()
        },

        async getContactLink(phone, relType, relId) {
            this.messagesLoading = true;
            const response = await this.api.getContactLink(phone, relType, relId)
            this.callInfo.contactUrl = response.data.data.url;
            this.messagesLoading = false;
        },

        scrollMessagesDivDown(){
            setTimeout(() => this.$refs.messenger.scrollDown(), 50)
        },

        registerEvents(){
            this.deviceService.onAccept(() => {
                this.callInfo.status = CallStatusesEnum.CONNECTED

                this.saveCallLog({
                    serviceName: this.deviceService.serviceName(),
                    userPhoneNumber: this.deviceService.getUserPhone(),
                    otherNumber: this.callInfo.contactPhone,
                    reference: this.deviceService.getCallId(),
                    relationType: this.callInfo.relationType,
                    relationId: this.callInfo.relationId,
                    result: 'initial'
                }).then()
            })

            this.deviceService.onIncomingCall((call) => {
                this.callInfo.mode = CallModeEnum.INBOUND
                this.resetContact()
                this.incomingCall(call)
                this.contactPhone = call.from
            });
            this.deviceService.onEnd(() => {
                this.isCallActiveInDetachedWindow = false

                this.communicationPusherService.triggerOnCallEnd(this.currentCallInfo)

                this.saveCallLog({
                    serviceName: this.deviceService.serviceName(),
                    userPhoneNumber: this.deviceService.getUserPhone(),
                    otherNumber: this.callInfo.contactPhone,
                    reference: this.deviceService.getCallId(),
                    result: 'answered',
                    relationType: this.callInfo.relationType,
                    relationId: this.callInfo.relationId
                })

                this.currentCallInfo.relationId = this.callInfo.relationId
                this.currentCallInfo.relationType = this.callInfo.relationType
                this.currentCallInfo.otherNumber = this.callInfo.contactPhone
                this.contactPhone = this.callInfo.contactPhone
                this.callLogType = 'call'
                this.isContactIdentificationModalVisible = this.shouldShowWhoTheySpokeWithModal
                this.reset()
            })
            this.deviceService.onMissed(() => {
                // AVOID CANCELING CALL WHEN ACCEPTING IT IN DETACHED DIALER
                if (this.isCallActiveInDetachedWindow) return

                this.missedCall()
                this.callActive = false
                this.dialogsVisibility.isIncomingDialogVisible = false
            });
        },

        registerBinds(){
            // SET THE CALL INFO TO DISPLAY OPTIONS FOR THEM TO CHOOSE WHO THEY SPOKE WITH
            this.communicationPusherService.bindOnCallEnd((currentCallInfo) => {
                if (currentCallInfo.showIdentificationModalVisible) {
                    this.currentCallInfo = currentCallInfo
                    this.callLogType = 'call'
                    this.isContactIdentificationModalVisible = true
                }
            })

            // CLOSE CONTACT IDENTIFICATION MODAL IN REMAINING OPENED PAGES
            this.communicationPusherService.bindOnContactIdentificationClose(() => {
                this.isContactIdentificationModalVisible = false
            })

            // OPEN PAGE ON PARENT WINDOW
            this.communicationPusherService.bindOnOpenRelationPage(({ url }) => {
                window.open(url, '_blank')
                window.focus()
            })
        },

        registerListeners() {
            this.listenForGlobalEvent('clear-lead-data', () => this.clearCallCallRecordings());
            this.listenForGlobalEvent('new-lead', (lead) => this.getCallRecordingsForLeadId(lead.basic.id));
        },

        async initializeDevice() {
            this.deviceService = CommunicationFactory.makeService('twilio');

            const { data } = await this.api.getCommunicationToken()

            if (!data.data.status) throw new Error('Not allowed to create a device')

            if (!data.data.number) {
                this.alerts.showUserNoPhoneAvailable = true
                return
            }

            await this.deviceService.initializeDevice(data.data.token, data.data.number);
        },

        toggleMinimizeCall() {
            this.callState.minimized = !this.callState.minimized;
        },
        handleEndCall() {
            this.callInfo.status = CallStatusesEnum.ENDED
            this.deviceService.hangUp();
            this.reset()
        },
        toggleHold() {
            this.callState.held = !this.callState.held;
            this.deviceService.hold(this.callState.held);
            this.communicationPusherService.triggerOnCallHold({ held: this.callState.held })
        },
        toggleMute() {
            this.callState.muted = !this.callState.muted;
            this.deviceService.mute(this.callState.muted);
            this.communicationPusherService.triggerOnCallMute({ muted: this.callState.muted })
        },

        async identifyNumber(number) {
            this.callState.isIdentifyingContact = true
            // TODO - Maybe we can handle international calls instead of only +1
            const { data:lookupData } = await this.communicationApi.lookupCaller(number);

            const { possibleContacts, identifiedContact } = lookupData.data

            if (identifiedContact) {
                this.callInfo.identifiedContact = identifiedContact
                this.callInfo.contactName = identifiedContact.contactName
                this.callInfo.relationId = identifiedContact.relationId
                this.callInfo.relationType = identifiedContact.relationSubtype
                if(identifiedContact.contactPhone !== undefined && identifiedContact.contactPhone !== null)
                    this.callInfo.contactPhone = identifiedContact.contactPhone
                this.callInfo.comment = identifiedContact.comment
                await this.getContactLink(number, this.callInfo.relationType, this.callInfo.relationId);
            }

            this.callState.isIdentifyingContact = false

            this.possibleContacts = possibleContacts
        },

        resetContact(){
            this.callInfo.contactName = 'Unknown'
            this.callInfo.comment = ''
            this.callInfo.relationId = null
            this.callInfo.relationType = null
            this.callInfo.identifiedContact = {}
            this.callInfo.contactUrl = null
        },


        async incomingCall(incomingCall) {
            this.incoming = incomingCall;
            this.shouldShowWhoTheySpokeWithModal = true

            if (!incomingCall) {
                this.dialogsVisibility.isIncomingDialogVisible = false
            } else {
                this.dialogsVisibility.isIncomingDialogVisible = true;
                this.callInfo.mode = CallModeEnum.INBOUND;

                this.communicationPusherService.triggerOnIncomingCallAttempt()

                this.callInfo.contactPhone = incomingCall.from.replace("+1", "")
                this.callInfo.id = incomingCall.id;
                this.currentCallInfo.sid = incomingCall.id // Store

                const payload = {
                    serviceName: this.deviceService.serviceName(),
                    userPhoneNumber: this.deviceService.getUserPhone(),
                    otherNumber: this.callInfo.contactPhone,
                    reference: this.callInfo.id,
                    relationType: this.callInfo.relationType,
                    relationId: this.callInfo.relationId,
                    result: 'initial'
                }

                this.saveCallLog(payload).then()

                this.identifyNumber(this.callInfo.contactPhone).then(async () => {
                    this.currentCallInfo.otherNumber = this.callInfo.contactPhone
                })
            }
        },
        answerCall() {
            if(this.callInfo.relationType === "lead") {
                this.dispatchGlobalEvent('load-lead', { lead_id: this.callInfo.relationId });
            }
            this.dialogsVisibility.isIncomingDialogVisible = false

            this.communicationPusherService.triggerOnIncomingCallAccept(this.callInfo)

            if (this.communicationPortalLocalStorageService.getCommunicationPortalVisibility()) {
                this.windowService.focusWindow('Communication')
                this.isCallActiveInDetachedWindow = true
            } else {
                this.deviceService.accept();
                this.callActive = true;
                this.callInfo.mode = CallModeEnum.INBOUND
                this.callInfo.status = CallStatusesEnum.ANSWERED;
            }
        },

        handleOpenRelationPage(){
            let url = ''
            const identifiedContact = this.callInfo.identifiedContact

            if (CommunicationRelationTypes.isCompanyRelated(identifiedContact.relationSubtype))
                url = `/companies/${identifiedContact.relationData.id}`


            else if (CommunicationRelationTypes.isLeadRelated(identifiedContact.relationSubtype))
                url = `/lead-processing?lead_id=${identifiedContact.relationData.id}`


            if (this.communicationPortalLocalStorageService.getCommunicationPortalVisibility())
                this.communicationPusherService.triggerOnOpenRelationPage({ url })
            else window.open(url, '_blank')
        },

        declineCall() {
            this.dialogsVisibility.isIncomingDialogVisible = false

            this.communicationPusherService.triggerOnIncomingCallDecline()

            const payload = {
                serviceName: this.deviceService.serviceName(),
                userPhoneNumber: this.deviceService.getUserPhone(),
                otherNumber: this.callInfo.contactPhone,
                reference: this.callInfo.id,
                result: 'busy',
            }

            this.communicationApi.logInboundCall(payload)

            this.deviceService.decline();
            this.reset()
        },

        reset(){
            this.shouldShowWhoTheySpokeWithModal = false
            this.callState = { ...INIT_CALL_STATE }
            this.callInfo = {
                mode: null, // COMMUNICATION_MODE
                id: null,
                status: CallStatusesEnum.CONNECTING, // CallStatusesEnum
                contactPhone: '',
                contactName: 'Unknown',
                relationId: null,
                relationType: null,
                comment: null,
                identifiedContact: null
            }
            this.alerts = { ...INIT_ALERTS_STATE }
            this.dialogsVisibility = { ...INIT_DIALOGS_VISIBILITY }
            this.callActive = false
        },

        async missedCall() {
            // Prevent duplicate calls from multiple tabs
            const callKey = `missed-call-${this.callInfo.id}`;
            const now = Date.now();
            const existing = localStorage.getItem(callKey);
            
            // If we processed this call in the last 5 seconds, skip it
            if (existing && (now - parseInt(existing)) < 5000) {
                console.log('Skipping duplicate missed call for', this.callInfo.id);
                this.reset();
                return;
            }
            
            // Mark this call as being processed
            localStorage.setItem(callKey, now.toString());
            
            try {
                await this.saveCallLog({
                    serviceName: this.deviceService.serviceName(),
                    userPhoneNumber: this.deviceService.getUserPhone(),
                    otherNumber: this.callInfo.contactPhone,
                    reference: this.callInfo.id,
                    result: 'missed'
                })
            } finally {
                this.reset();
                // Clean up old entries (optional cleanup)
                this.cleanupOldMissedCallEntries();
            }
        },

        // Helper method to clean up old localStorage entries
        cleanupOldMissedCallEntries() {
            const cutoff = Date.now() - 300000; // 5 minutes ago
            Object.keys(localStorage).forEach(key => {
                if (key.startsWith('missed-call-')) {
                    const timestamp = parseInt(localStorage.getItem(key));
                    if (timestamp < cutoff) {
                        localStorage.removeItem(key);
                    }
                }
            });
        },
        markVoicemailHeard(voiceMailId) {
            this.communicationApi.markVoicemailHeard(voiceMailId);
        },
        clearCallCallRecordings() {
            this.recordings = [];
        },
        getCallRecordingsForLeadId(leadId) {
            this.communicationApi.getCallRecordingsForLeadId(leadId).then( (resp) => {
                if(resp.status === 200){
                    this.recordings = resp.data.data;
                    const contactsStore = useContactsStore();
                    contactsStore.setRecordings( resp.data.data);
                }
            } );
        },
        generateKeyTone(key) {
            this.deviceService.sendDigitsToCall(key)
        }
    },
}
