<script setup>
import {defineProps, defineEmits} from "vue";
import TagInput from "../../Shared/components/TagInput.vue";

const props = defineProps({
    label: {
        type: String,
        default: 'CC'
    },
    modelValue: {
        type: Array,
        default: []
    },
    darkMode: {
        type: <PERSON><PERSON>an,
        default: false
    },
    contacts: {
        type: Array,
        default: []
    },
    disabled: {
        type: <PERSON>olean,
        default: false
    }
});

const emit = defineEmits(["update:modelValue"]);

const handleTagUpdate = (v) => {
    emit('update:modelValue', v)
}

</script>

<template>
    <div class="relative">
        <div class="flex items-center gap-2">
            <p class="font-semibold w-10">{{ props.label }}:</p>
            <tag-input
                class="w-full"
                :model-value="props.modelValue"
                @update:modelValue="handleTagUpdate"
                placeholder="Enter Email"
                allow-new
                :options="contacts"
                :disabled="disabled"
                :searchable-fields="['email']"
                :option-factory-callback="(email) => ({email})"
            >
                <template v-slot:filtered-result-option="{value}">
                    <p>{{value?.email}}</p>
                </template>
                <template v-slot:selected-tag="{value}">
                    <p>{{value?.email}}</p>
                </template>
            </tag-input>
        </div>
    </div>
</template>
