export default class ApiService {
    constructor(baseUrl, baseEndpoint, version) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    static make() {
        return new ApiService('internal-api', 'emails', 1);
    }

    //todo lets pass in some identifiers so we can get back the specific email content

    //email type will probably be the template type
    getEmailContent(emailTemplateType, companyId = null) {
        return this.axios().get(`${emailTemplateType}/email-content`, {
            params: {
                company_id: companyId
            }
        });
    }

    saveEmailTemplate(emailTemplateType, subject, content, name, templateId = undefined) {
        return this.axios().post(`${emailTemplateType}/save-email-template`, {
            content,
            subject,
            name,
            templateId
        });
    }

    sendEmail(emailTemplateType, emailRecipientId, email, subject, content, cc = undefined, bcc = undefined) {
        return this.axios().post(`${emailTemplateType}/send`, {
            emailRecipientId,
            email,
            content,
            subject,
            cc,
            bcc
        });
    }

    getEmailPreview(emailTemplateType, emailRecipientId, email, subject, content) {
        return this.axios().post(`${emailTemplateType}/preview`, {
            emailRecipientId,
            email,
            content,
            subject
        });
    }
}
