import axios from 'axios';

export default class ApiService {
    constructor(baseUrl, baseEndpoint, version) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    static make() {
        return new ApiService('internal-api', 'reviews', 1);
    }

    getReviews(companyId, params = {}){
        return this.axios().get(`/`, {
            params: {
                company_id: companyId,
                ...params
            }
        });
    }

    getReviewDetails(reviewReference){
        return this.axios().get(`/${reviewReference}/details`);
    }

    postReviewReply(reviewReference, reply){
        return this.axios().post(`/${reviewReference}/reply`, { reply });
    }

    approveReview(reviewReference){
        return this.axios().post(`/${reviewReference}/approve`);
    }

    rejectReview(reviewReference){
        return this.axios().post(`/${reviewReference}/decline`);
    }

    setPendingReview(reviewReference){
        return this.axios().post(`/${reviewReference}/pending`);
    }
}
