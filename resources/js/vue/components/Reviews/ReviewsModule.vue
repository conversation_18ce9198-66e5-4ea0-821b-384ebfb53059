<template>
    <div>
        <div class="pb-10" :class="[darkMode ? 'text-white' : 'text-slate-900']">
            <div class="border rounded-lg" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
                <div class="flex items-center justify-between p-5">
                    <h5 class="text-primary-500 text-sm uppercase font-bold leading-tight">
                        Reviews{{ companyId ? ` for ${companyName}` : '' }}
                    </h5>
                </div>
                <div class="px-5 pb-5 flex flex-wrap items-center gap-3">
                    <div class="w-48">
                        <Dropdown
                            label="Approval Status"
                            :dark-mode="darkMode"
                            v-model="statusFilter"
                            :options="statuses"
                        />
                    </div>
                    <div class="w-48">
                        <Dropdown
                            label="Overall Score"
                            :dark-mode="darkMode"
                            v-model="scoreFilter"
                            :options="scores"
                        />
                    </div>
                    <div class="w-48">
                        <Dropdown
                            label="Verified Status"
                            :dark-mode="darkMode"
                            v-model="verifiedFilter"
                            :options="verifiedStatuses"
                        />
                    </div>
                    <CustomInput class="w-80" v-model="textFilter" search-icon placeholder="Search title or comment" :dark-mode="darkMode"></CustomInput>
                    <button @click="getCompanyReviews" class="inline-flex items-center px-5 h-9 py-1 mx-2 bg-primary-500 hover:bg-blue-500 transition duration-200 rounded-lg text-xs font-medium text-white">
                        <svg class="fill-current text-white" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M5.66667 2C4.69421 2 3.76158 2.38631 3.07394 3.07394C2.38631 3.76158 2 4.69421 2 5.66667C2 6.14818 2.09484 6.62498 2.27911 7.06984C2.46338 7.5147 2.73346 7.91891 3.07394 8.25939C3.41442 8.59987 3.81863 8.86996 4.26349 9.05423C4.70835 9.23849 5.18515 9.33333 5.66667 9.33333C6.14818 9.33333 6.62498 9.23849 7.06984 9.05423C7.5147 8.86996 7.91891 8.59987 8.25939 8.25939C8.59987 7.91891 8.86996 7.5147 9.05423 7.06984C9.23849 6.62498 9.33333 6.14818 9.33333 5.66667C9.33333 4.69421 8.94703 3.76158 8.25939 3.07394C7.57176 2.38631 6.63913 2 5.66667 2ZM1.65973 1.65973C2.72243 0.597022 4.16377 0 5.66667 0C7.16956 0 8.6109 0.597022 9.6736 1.65973C10.7363 2.72243 11.3333 4.16377 11.3333 5.66667C11.3333 6.41082 11.1868 7.14769 10.902 7.83521C10.7458 8.21219 10.5498 8.57029 10.3178 8.90361L13.7071 12.2929C14.0976 12.6834 14.0976 13.3166 13.7071 13.7071C13.3166 14.0976 12.6834 14.0976 12.2929 13.7071L8.90361 10.3178C8.57029 10.5498 8.21219 10.7458 7.83521 10.902C7.14769 11.1868 6.41082 11.3333 5.66667 11.3333C4.92251 11.3333 4.18564 11.1868 3.49813 10.902C2.81062 10.6172 2.18593 10.1998 1.65973 9.6736C1.13353 9.14741 0.716126 8.52272 0.431349 7.83521C0.146573 7.1477 0 6.41082 0 5.66667C0 4.16377 0.597022 2.72243 1.65973 1.65973Z"/>
                        </svg>
                    </button>
                    <button @click="resetSearch" class="transition duration-200 text-sm font-semibold focus:outline-none py-2 rounded-md px-5 mr-3"
                            :class="{'bg-grey-250 hover:bg-light-background text-white': !darkMode, 'bg-grey-500 hover:bg-grey-600 text-white': darkMode}">
                        Reset
                    </button>
                </div>
                <div class="">
                    <div class="grid grid-cols-14 gap-3 mb-2 px-5 text-center">
                        <p v-if="companyId" class="text-slate-500 font-semibold tracking-wide uppercase text-xs col-span-2">Reference</p>
                        <p v-else class="text-slate-500 font-semibold tracking-wide uppercase text-xs col-span-2">Company</p>
                        <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs col-span-2">Reviewer</p>
                        <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs col-span-2">Title</p>
                        <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs col-span-4">Comment</p>
                        <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs text-center col-span-1">Score</p>
                        <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs text-center col-span-1">Replies</p>
                        <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs col-span-1">Added</p>
                        <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs col-span-1">Status</p>
                    </div>
                    <div v-if="loading" class="border-t border-b h-[25rem] overflow-y-auto divide-y flex items-center justify-center" :class="[darkMode ? 'bg-dark-background border-dark-border divide-dark-border' : 'bg-light-background  border-light-border divide-light-border']">
                        <LoadingSpinner />
                    </div>
                    <div v-else class="border-t border-b max-h-[40rem] overflow-y-auto divide-y" :class="[darkMode ? 'bg-dark-background border-dark-border divide-dark-border' : 'bg-light-background  border-light-border divide-light-border']">
                        <div @click="toggleDetails(review)"
                             v-for="review in reviews"
                             :key="review.id"
                             class="grid cursor-pointer grid-cols-14 gap-x-3 py-3 px-5 group relative transition duration-100 items-center text-center"
                             :class="[darkMode ? 'hover:bg-dark-module' : 'hover:bg-light-module']"
                        >
                            <div class="absolute left-0 h-full w-1"
                                 :class="[darkMode ? 'bg-slate-600 invisible group-hover:visible' : 'bg-slate-400 invisible group-hover:visible']">
                            </div>
                            <p v-if="companyId" class="text-sm col-span-2">
                                {{review.reference}}
                            </p>
                            <p v-else class="text-sm col-span-2">
                                {{review.company_name}} ({{review.company_id}})
                            </p>
                            <p class="text-sm truncate col-span-2">
                                {{review.reviewer_name}}
                            </p>
                            <p class="text-sm truncate col-span-2">
                                {{review.review_data.title ?? '-'}}
                            </p>
                            <p class="text-sm truncate col-span-4">
                                {{review.review_data.comments }}
                            </p>
                            <p class="text-sm text-center col-span-1">
                                {{review.review_data.overall_score}}
                            </p>
                            <p class="text-sm text-center col-span-1">
                                {{review.review_replies?.length || ''}}
                            </p>
                            <p class="text-sm col-span-1">
                                {{ $filters.dateFromTimestamp(review.created_at, 'usWithTime') }}
                            </p>
                            <div class="inline-flex flex-wrap items-center justify-center gap-x-2 col-span-1">
                                <p class="text-sm capitalize inline-flex items-center">
                                    <Badge v-if="review.display_status === 'approved'" color="green" :dark-mode="darkMode">
                                        {{review.display_status}}
                                    </Badge>
                                    <Badge v-else-if="review.display_status === 'rejected'" color="red" :dark-mode="darkMode">
                                        {{review.display_status}}
                                    </Badge>
                                    <Badge v-else color="amber" :dark-mode="darkMode">
                                        {{review.display_status}}
                                    </Badge>
                                </p>
                                <div class="mr-2"></div>
                                <div v-if="review.review_is_verified" class="inline-flex flex-wrap items-center font-medium" :class="[darkMode ? 'text-emerald-500' : 'text-emerald-700']">
                                    <svg class="mr-1 fill-current" width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.25 1.75C5.72501 1.75 4.26247 2.3558 3.18414 3.43414C2.1058 4.51247 1.5 5.97501 1.5 7.5C1.5 8.2551 1.64873 9.00281 1.93769 9.70043C2.22666 10.3981 2.6502 11.0319 3.18414 11.5659C3.71807 12.0998 4.35195 12.5233 5.04957 12.8123C5.74719 13.1013 6.4949 13.25 7.25 13.25C8.0051 13.25 8.75281 13.1013 9.45043 12.8123C10.1481 12.5233 10.7819 12.0998 11.3159 11.5659C11.8498 11.0319 12.2733 10.3981 12.5623 9.70043C12.8513 9.00281 13 8.2551 13 7.5C13 5.97501 12.3942 4.51247 11.3159 3.43414C10.2375 2.3558 8.77499 1.75 7.25 1.75ZM2.12348 2.37348C3.48311 1.01384 5.32718 0.25 7.25 0.25C9.17282 0.25 11.0169 1.01384 12.3765 2.37348C13.7362 3.73311 14.5 5.57718 14.5 7.5C14.5 8.45208 14.3125 9.39484 13.9481 10.2745C13.5838 11.1541 13.0497 11.9533 12.3765 12.6265C11.7033 13.2997 10.9041 13.8338 10.0245 14.1981C9.14484 14.5625 8.20208 14.75 7.25 14.75C6.29792 14.75 5.35516 14.5625 4.47554 14.1981C3.59593 13.8338 2.7967 13.2997 2.12348 12.6265C1.45025 11.9533 0.91622 11.1541 0.551873 10.2745C0.187527 9.39484 0 8.45208 0 7.5C0 5.57718 0.763837 3.73311 2.12348 2.37348ZM9.947 5.52523C10.2399 5.81812 10.2399 6.29299 9.947 6.58589L7.05811 9.47477C6.76521 9.76767 6.29034 9.76767 5.99745 9.47477L4.553 8.03033C4.26011 7.73744 4.26011 7.26256 4.553 6.96967C4.8459 6.67678 5.32077 6.67678 5.61366 6.96967L6.52778 7.88378L8.88634 5.52523C9.17923 5.23233 9.6541 5.23233 9.947 5.52523Z"/></svg>
                                    Verified
                                </div>
                                <div v-else class="inline-flex flex-wrap items-center font-medium" :class="[darkMode ? 'text-red-500' : 'text-red-700']">
                                    <svg class="mr-1 fill-current" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7 1.75C5.60761 1.75 4.27226 2.30312 3.28769 3.28769C2.30312 4.27226 1.75 5.60761 1.75 7C1.75 7.68944 1.8858 8.37213 2.14963 9.00909C2.41347 9.64605 2.80018 10.2248 3.28769 10.7123C3.7752 11.1998 4.35395 11.5865 4.99091 11.8504C5.62787 12.1142 6.31056 12.25 7 12.25C7.68944 12.25 8.37213 12.1142 9.00909 11.8504C9.64605 11.5865 10.2248 11.1998 10.7123 10.7123C11.1998 10.2248 11.5865 9.64605 11.8504 9.00909C12.1142 8.37213 12.25 7.68944 12.25 7C12.25 5.60761 11.6969 4.27226 10.7123 3.28769C9.72774 2.30312 8.39239 1.75 7 1.75ZM2.22703 2.22703C3.4929 0.961159 5.20979 0.25 7 0.25C8.79021 0.25 10.5071 0.961159 11.773 2.22703C13.0388 3.4929 13.75 5.20979 13.75 7C13.75 7.88642 13.5754 8.76417 13.2362 9.58311C12.897 10.4021 12.3998 11.1462 11.773 11.773C11.1462 12.3998 10.4021 12.897 9.58311 13.2362C8.76417 13.5754 7.88642 13.75 7 13.75C6.11358 13.75 5.23584 13.5754 4.41689 13.2362C3.59794 12.897 2.85382 12.3998 2.22703 11.773C1.60023 11.1462 1.10303 10.4021 0.763813 9.58311C0.424594 8.76417 0.25 7.88642 0.25 7C0.25 5.20979 0.961159 3.4929 2.22703 2.22703ZM5.13634 5.13634C5.42923 4.84344 5.9041 4.84344 6.197 5.13634L7 5.93934L7.803 5.13634C8.0959 4.84344 8.57077 4.84344 8.86366 5.13634C9.15656 5.42923 9.15656 5.9041 8.86366 6.197L8.06066 7L8.86366 7.803C9.15656 8.0959 9.15656 8.57077 8.86366 8.86366C8.57077 9.15656 8.0959 9.15656 7.803 8.86366L7 8.06066L6.197 8.86366C5.9041 9.15656 5.42923 9.15656 5.13634 8.86366C4.84344 8.57077 4.84344 8.0959 5.13634 7.803L5.93934 7L5.13634 6.197C4.84344 5.9041 4.84344 5.42923 5.13634 5.13634Z"/></svg>
                                    Unverified
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="p-3" v-if="paginationData && paginationData.to">
                        <Pagination :dark-mode="darkMode" :pagination-data="paginationData" :show-pagination="true" @change-page="handlePaginationEvent"></Pagination>
                    </div>
                </div>
            </div>
        </div>

        <ReviewDetailsModal
            v-if="selectedReview"
            @toggle-details="toggleDetails()"
            :dark-mode="darkMode"
            :review="selectedReview"
        />
    </div>
</template>

<script>
import Dropdown from "../Shared/components/Dropdown.vue";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import CustomInput from "../Shared/components/CustomInput.vue";
import Badge from "../Shared/components/Badge.vue";
import Modal from "../Shared/components/Modal.vue";
import DatePicker from '@vuepic/vue-datepicker';
import ApiService from "./services/reviews-api";
import Pagination from "../Shared/components/Pagination.vue";
import {dateFromTimestamp} from "../../../modules/helpers";
import ReviewDetailsModal from "./ReviewDetailsModal.vue";

export default {
    name: "ReviewsModule",
    components: {
        ReviewDetailsModal,
        Pagination, Modal, Badge, CustomInput, LoadingSpinner, Dropdown, DatePicker},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: Number,
            default: null,
        },
        companyName: {
            type: String,
            default: null,
        },
        defaultFilters: {
            type: Object,
            default: null,
        },
    },
    data() {
        return {
            api: ApiService.make(),
            reviews: [],
            loading: false,
            selectedReview: null,
            statusFilter: null,
            statuses: [
                {id: 'all', name: 'All'},
                {id: 1, name: 'Approved'},
                {id: -1, name: 'Rejected'},
                {id: 0, name: 'Pending'},
            ],
            scoreFilter: null,
            scores: [
                {id: 'all', name: 'All'},
                {id: 1, name: '1'},
                {id: 2, name: '2'},
                {id: 3, name: '3'},
                {id: 4, name: '4'},
                {id: 5, name: '5'},
            ],
            verifiedStatuses: [
                { id: 'all', name: 'All' },
                { id: 1, name: 'Verified' },
                { id: 0, name: 'Unverified' },
            ],
            dateFilter: null,
            textFilter: null,
            verifiedFilter: null,
            searchParams: {},
            paginationData: null,
        }
    },
    created: function() {
        if (this.defaultFilters) {
            this.statusFilter = this.defaultFilters.statusFilter ?? this.statusFilter;
            this.verifiedFilter = this.defaultFilters.verifiedFilter ?? this.verifiedFilter;
        }
        this.getCompanyReviews();
    },
    methods: {
        dateFromTimestamp,
        toggleDetails(review) {
            if(this.selectedReview === null) {
                this.selectedReview = review
            }
            else {
                this.selectedReview = null
            }
        },
        updateReview(review) {
            this.selectedReview = review;
        },
        getCompanyReviews() {
            this.loading = true;
            this.setSearchParams();
            this.api.getReviews(this.companyId, this.searchParams).then(resp =>  {
                let {data, ...paginationData} = resp.data.data.company_consumer_reviews;
                this.reviews = data;
                this.paginationData = paginationData;
            }).catch(e => console.error(e)).finally(() => this.loading = false);
        },
        setSearchParams() {
            this.searchParams = {
                'status': this.statusFilter === 'all' ? null : this.statusFilter,
                'score': this.scoreFilter === 'all' ? null : this.scoreFilter,
                'verified': this.verifiedFilter === 'all' ? null : this.verifiedFilter,
                'date': this.dateFilter,
                'text': this.textFilter,
            }
        },
        resetSearch() {
            this.searchParams = {};
            this.statusFilter = 'all';
            this.scoreFilter = 'all';
            this.verifiedFilter = 'all';
            this.dateFilter = null;
            this.textFilter = null;
            this.getCompanyReviews();
        },
        async handlePaginationEvent(newPageUrl) {
            this.loading = true;
            this.setSearchParams();
            await axios.get(newPageUrl.link, {
                params: this.searchParams
            }).then(resp => {
                let {data, ...paginationData} = resp.data.data.company_consumer_reviews;
                this.reviews = data;
                this.paginationData = paginationData;
            }).catch(e => console.error(e)).finally(() => this.loading = false);
        },
    }
}
</script>

<style scoped>

</style>
