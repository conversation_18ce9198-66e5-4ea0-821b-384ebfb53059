<template>
    <div class="px-5 mx-5">
        <Modal
            :dark-mode="darkMode"
            container-classes="px-5"
            header-classes="border-none"
            :restrict-width="false"
            :small="false"
            :no-buttons="true"
            @close="toggleModal()"
        >
            <template v-slot:header>
                <div class="inline-flex items-center gap-4">
                    <h4 class="font-semibold text-lg">Review <span class="italic font-normal text-base text-slate-500">(reference - {{review.reference}})</span></h4>
                    <div>
                        <div class="capitalize">
                            <Badge v-if="review.display_status === 'approved'" color="green" :dark-mode="darkMode">
                                {{review.display_status}}
                            </Badge>
                            <Badge v-else-if="review.display_status === 'rejected'" color="red" :dark-mode="darkMode">
                                {{review.display_status}}
                            </Badge>
                            <Badge v-else color="amber" :dark-mode="darkMode">
                                {{review.display_status}}
                            </Badge>
                        </div>
                    </div>
                    <div v-if="!review.review_is_verified">
                        <Badge color="red" :dark-mode="darkMode">
                            Reviewer Unverified
                        </Badge>
                    </div>
                </div>
            </template>
            <template v-slot:content>
                <div>
                    <div class="flex border-b" :class="[darkMode ? 'border-dark-border text-slate-400' : 'border-light-border text-slate-600']">
                        <div @click="selectedTab = 'Details'" class="px-6 py-3 border-b-2 cursor-pointer" :class="[selectedTab === 'Details' ? 'border-primary-500 font-semibold text-primary-500' : 'hover:text-slate-500 border-transparent font-medium']">
                            Details
                        </div>
                        <div @click="selectedTab = 'IP Address'" class="px-6 py-3 inline-flex items-center border-b-2 cursor-pointer" :class="[selectedTab === 'IP Address' ? 'border-primary-500 font-semibold text-primary-500' : 'hover:text-slate-500 border-transparent font-medium']">
                            Reviews With Same IP Address
                            <span class="ml-2 rounded-full w-5 h-5 text-xs inline-flex items-center justify-center font-bold" :class="[darkMode ? 'bg-dark-border text-red-400' : 'bg-red-100 text-red-600']">{{ related_reviews.length }}</span>
                        </div>
                        <div @click="selectedTab = 'Associated Consumers'" class="px-6 py-3 inline-flex items-center border-b-2 cursor-pointer" :class="[selectedTab === 'Associated Consumers' ? 'border-primary-500 font-semibold text-primary-500' : 'hover:text-slate-500 border-transparent font-medium']">
                            Associated Consumers
                            <span class="ml-2 rounded-full w-5 h-5 text-xs inline-flex items-center justify-center font-bold" :class="[darkMode ? 'bg-dark-border text-red-400' : 'bg-red-100 text-red-600']">{{ associated_consumers.length }}</span>
                        </div>
                    </div>

                    <!-- Details Tab -->
                    <div v-if="selectedTab === 'Details'">
                        <div
                            v-if="review.review_data.data"
                            class="flex border-b divide-x"
                            :class="[darkMode ? 'border-dark-border divide-dark-border' : 'border-light-border divide-light-border']">
                            <div class="p-6 flex-grow">
                                <p class="font-semibold text-xs mb-1 uppercase text-slate-500">
                                    Company Reviewed:
                                </p>
                                <p class="font-semibold text-sm">
                                    {{review.company_name ?? 'Unknown Company'}} ({{ review.company_id }})
                                </p>
                            </div>
                            <div class="p-6 flex-grow">
                                <p class="font-semibold text-xs mb-1 uppercase text-slate-500">
                                    Review By:
                                </p>
                                <p class="font-semibold text-sm">
                                    {{review.reviewer_name ?? '-'}}
                                </p>
                            </div>
                            <div class="p-6 flex-grow">
                                <p class="font-semibold text-xs mb-1 uppercase text-slate-500">
                                    Overall Rating:
                                </p>
                                <div class="inline-flex items-center">
                                    <StarRating :rating="review.review_data.overall_score" star-width="w-24"></StarRating>
                                </div>
                            </div>
                            <div class="p-6 flex-grow">
                                <p class="font-semibold text-xs mb-1 uppercase text-slate-500">
                                    Review Date:
                                </p>
                                <p class="font-semibold text-sm">
                                    {{$filters.dateFromTimestamp(review.created_at, 'usWithTime') ?? '-'}}
                                </p>
                            </div>
                            <div class="p-6 flex-grow">
                                <p class="font-semibold text-xs mb-1 uppercase text-slate-500">
                                    Property Zipcode:
                                </p>
                                <p class="font-semibold text-sm">
                                    {{ review.review_data.data.zip_code }} {{ review.review_data.data?.display_location }}
                                </p>
                            </div>
                            <div class="p-6 flex-grow">
                                <p class="font-semibold text-xs mb-1 uppercase text-slate-500">
                                    IP Address:
                                </p>
                                <p class="font-semibold text-sm">
                                    {{review.review_data.data.user_ip_address ?? '-'}}
                                </p>
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-x-3 my-3 px-5">
                            <!-- Title & Comments -->
                            <div class="border rounded overflow-y-auto h-[20rem] p-6"
                                 :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
                            >
                                <div class="whitespace-pre-line px-3 py-1">
                                    <p class="font-semibold mb-2">
                                        {{review.review_data.title ?? '-'}}
                                    </p>
                                    <p>{{review.review_data.comments}}</p>
                                </div>
                            </div>
                            <div class="grid grid-cols-2 border rounded divide-x text-sm overflow-y-auto h-[20rem]"
                                :class="[darkMode ? 'divide-dark-border border-dark-border' : 'divide-light-border border-light-border']"
                            >
                                <div>
                                    <!-- System Details -->
                                    <div class="border-b"
                                         :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
                                    >
                                        <p class="font-semibold py-3 px-3">System Details</p>
                                    </div>
                                    <div class="flex flex-col"
                                         v-if="review.review_data?.data?.custom"
                                    >
                                        <div v-for="key in orderedCustomKeys">
                                            <div class="flex items-center justify-between py-3 px-5">
                                                <p>{{ review.review_data.data.custom[key].title }}</p>
                                                <div v-if="review.review_data.data.custom[key].type === 'rating'">
                                                    <StarRating
                                                        v-if="review.review_data.data.custom[key].value"
                                                        :rating="review.review_data.data.custom[key].value"
                                                    />
                                                    <p v-else>-</p>
                                                </div>
                                                <div v-else-if="review.review_data.data.custom[key].type === 'boolean'">
                                                    <svg v-if="review.review_data.data.custom[key].value" class="w-4 text-green-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2.0" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                                                    </svg>
                                                    <svg v-else class="w-4 text-slate-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
                                                    </svg>
                                                </div>
                                                <div v-else-if="key === 'system_price'">
                                                    <p>{{ Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD', maximumFractionDigits: 0, }).format(review.review_data.data.custom[key].value ?? 0) ?? '-' }}</p>
                                                </div>
                                                <div v-else>
                                                    <p>{{ review.review_data.data.custom[key].value ?? '-' }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Ratings -->
                                <div>
                                    <div class="border-b"
                                        :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
                                    >
                                        <p class="font-semibold py-3 px-3">Rating Breakdown</p>
                                    </div>
                                    <div v-if="review.review_data?.data?.custom"
                                         class="flex flex-col"
                                    >
                                        <div v-for="key in orderedRatingKeys">
                                            <div class="flex justify-between py-3 px-5">
                                                <p>{{ review.review_data.data.custom[key].title }}</p>
                                                <div v-if="review.review_data.data.custom[key].type === 'rating'"
                                                >
                                                    <StarRating
                                                        v-if="review.review_data.data.custom[key].value"
                                                        :rating="review.review_data.data.custom[key].value"
                                                        star-width="w-[4rem]"
                                                    />
                                                    <p v-else>-</p>
                                                </div>
                                                <div v-else-if="review.review_data.data.custom[key].type === 'boolean'">
                                                    <svg v-if="review.review_data.data.custom[key].value" class="w-4 text-green-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2.0" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                                                    </svg>
                                                    <svg v-else class="w-4 text-slate-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
                                                    </svg>
                                                </div>
                                                <div v-else>
                                                    <p>{{ review.review_data.data.custom[key].value ?? '-' }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Replies & Approval -->
                        <div v-if="saving" class="my-5">
                            <LoadingSpinner />
                        </div>
                        <div v-else class="px-5 my-3 grid grid-cols-4 gap-x-3">
                            <div
                                class="overflow-auto px-8 w-full py-3 border rounded min-h-[10rem] col-span-3"
                                :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
                            >
                                <div>
                                    <div class="flex items-start gap-x-3 mb-4">
                                        <div>
                                            <p v-if="review.review_replies.length === 0" class="text-slate-500 text-sm">No replies yet</p>
                                            <p v-else-if="review.review_replies.length === 1" class="text-slate-500 text-sm">1 Reply</p>
                                            <p v-else class="text-slate-500 text-sm">{{review.review_replies.length}} Replies</p>
                                        </div>
                                        <div class="flex flex-col divide-y"
                                             :class="[darkMode ? 'divide-dark-border' : 'divide-light-border']"
                                        >
                                            <div v-for="reply in review.review_replies" class="border-l pl-4 py-2 opacity-[80%]"
                                                 :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
                                            >
                                                <p class="font-semibold mb-1 text-sm">
                                                    {{ reply.user.name }}
                                                    <span class="font-normal italic">- {{ $filters.dateFromTimestamp(reply.created_at) }}</span>
                                                </p>
                                                <p>{{reply.comments}}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex w-[80%] mx-auto pb-6"
                                        v-if="permissionStore.hasPermission(PERMISSIONS.PERMISSION_CONSUMER_REVIEWS_MANAGE)"
                                    >
                                        <div v-if="!replying">
                                            <CustomButton
                                                @click="toggleReplyBox"
                                                :dark-mode="darkMode"
                                            >
                                                Reply
                                            </CustomButton>
                                        </div>
                                        <div v-else class="w-full">
                                            <p class="text-slate-500 text-sm mb-1">Replying to {{review.reviewer_name}}</p>
                                            <CustomInput
                                                :dark-mode="darkMode"
                                                type="textarea"
                                                v-model="reply"
                                                textarea-height="h-24"
                                                placeholder="Start typing your message here..."
                                            />
                                            <div class="flex items-center mt-3 gap-3">
                                                <CustomButton
                                                    @click="postReply"
                                                    :dark-mode="darkMode"
                                                >
                                                    Post Reply
                                                </CustomButton>
                                                <CustomButton
                                                    @click="toggleReplyBox"
                                                    :dark-mode="darkMode"
                                                    color="slate"
                                                >
                                                    Cancel
                                                </CustomButton>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Approval Control -->
                            <div class="overflow-auto px-3 py-3 border rounded"
                              :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
                            >
                                <div class="flex flex-col items-center justify-center gap-3 h-full"
                                     :class="[permissionStore.hasPermission(PERMISSIONS.PERMISSION_CONSUMER_REVIEWS_MANAGE) ? '' : 'grayscale-[90%] pointer-events-none']"
                                >
                                    <CustomButton
                                        v-if="review.display_status !== 'rejected'"
                                        color="red-outline"
                                        :dark-mode="darkMode"
                                        @click="rejectReview"
                                    >
                                        Reject Review
                                    </CustomButton>
                                    <CustomButton
                                        v-if="review.display_status !== 'pending'"
                                        color="amber-outline"
                                        :dark-mode="darkMode"
                                        @click="pendingReview"
                                    >
                                        Cancel <span class="ml-1 capitalize">{{ review.display_status }}</span>
                                    </CustomButton>
                                    <CustomButton
                                        v-if="review.display_status !== 'approved'"
                                        color="green-outline"
                                        :dark-mode="darkMode"
                                        @click="approveReview"
                                    >
                                        Approve Review
                                    </CustomButton>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Reviews With Same UP Address Tab -->
                    <div v-if="selectedTab === 'IP Address'">
                        <div class="">
                            <p class="px-5 my-3">IP Address: <span class="font-semibold">{{ review.review_data.user_ip_address }}</span> has also posted the following comments:</p>
                            <div class="">
                                <div class="grid grid-cols-6 gap-3 mb-2 px-5">
                                    <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs">ID</p>
                                    <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs">Status</p>
                                    <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs">Date Raised</p>
                                    <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs">Email</p>
                                    <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs">Origin</p>
                                    <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs">Details</p>
                                </div>
                                <div v-if="loading" class="border-t border-b h-[22rem] overflow-y-auto divide-y flex items-center justify-center" :class="[darkMode ? 'bg-dark-background border-dark-border divide-dark-border' : 'bg-light-background  border-light-border divide-light-border']">
                                    <LoadingSpinner />
                                </div>
                                <div v-else class="border-t border-b h-[22rem] overflow-y-auto divide-y" :class="[darkMode ? 'bg-dark-background border-dark-border divide-dark-border' : 'bg-light-background  border-light-border divide-light-border']">
                                    <div v-for="related_review in related_reviews" :key="related_review.reference" class="grid cursor-pointer grid-cols-6 gap-x-3 py-3 px-5 group relative transition duration-100 items-center" :class="[darkMode ? 'hover:bg-dark-module' : 'hover:bg-light-module']">
                                        <p class="text-sm">
                                            <a @click="updateReviewDetails(related_review)" class="text-primary-500 font-bold flex items-center">{{ related_review.reference }}</a>
                                        </p>
                                        <p class="text-sm capitalize inline-flex items-center gap-3">
                                            <Badge v-if="related_review.display_status === 'approved'" color="green" :dark-mode="darkMode">
                                                {{ related_review.display_status }}
                                            </Badge>
                                            <Badge v-else-if="related_review.display_status === 'rejected'" color="red" :dark-mode="darkMode">
                                                {{ related_review.display_status }}
                                            </Badge>
                                            <Badge v-else color="amber" :dark-mode="darkMode">
                                                {{ related_review.display_status }}
                                            </Badge>
                                        </p>
                                        <div class="text-sm">
                                            <p class="font-semibold text-slate-500">{{ $filters.dateFromTimestamp(related_review.created, 'usWithTime') }}</p>
                                        </div>
                                        <div class="text-sm">
                                            <p class="font-semibold text-slate-500">{{ related_review.reviewer_email }}</p>
                                        </div>
                                        <div class="text-sm">
                                            <Badge color="purple" :dark-mode="darkMode" v-if="related_review.website_name">{{ related_review.website_name }}</Badge>
                                        </div>
                                        <div class="text-sm truncate">
                                            {{ related_review.comment  || '-' }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Associated Consumers Tab -->
                    <div v-if="selectedTab === 'Associated Consumers'">
                        <div class="">
                            <p class="px-5 my-3">Reviewer: <span class="font-semibold">{{ review.reviewer_name }}</span> has the following associated consumers:</p>
                            <div class="">
                                <div class="grid grid-cols-6 gap-3 mb-2 px-5">
                                    <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs">ID</p>
                                    <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs">Name</p>
                                    <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs">Email</p>
                                    <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs">Phone</p>
                                    <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs">Origin</p>
                                    <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs">Status</p>
                                </div>
                                <div v-if="loading" class="border-t border-b h-[22rem] overflow-y-auto divide-y flex items-center justify-center" :class="[darkMode ? 'bg-dark-background border-dark-border divide-dark-border' : 'bg-light-background  border-light-border divide-light-border']">
                                    <LoadingSpinner />
                                </div>
                                <div v-else
                                     class="border-t border-b h-[22rem] overflow-y-auto divide-y"
                                     :class="[darkMode ? 'bg-dark-background border-dark-border divide-dark-border' : 'bg-light-background  border-light-border divide-light-border',
                                            saving ? 'grayscale-[50%] opacity-50 pointer-events-none' : '',
                                ]">
                                    <div v-for="associated_consumer in associated_consumers" :key="associated_consumer.id" class="grid cursor-pointer grid-cols-6 gap-x-3 py-3 px-5 group relative transition duration-100 items-center" :class="[darkMode ? 'hover:bg-dark-module' : 'hover:bg-light-module']">
                                        <div class="text-sm">
                                            <a target="_blank" :href="`/consumer-product/?consumer_product_id=${ associated_consumer.lead_id }`" class="text-primary-500 font-bold flex items-center">
                                                <p>{{ associated_consumer.id }}</p>
                                                <p class="text-slate-500 ml-2">(Lead #{{ associated_consumer.lead_id }})</p>
                                            </a>
                                        </div>
                                        <div class="text-sm">
                                            <p class="font-bold">{{ associated_consumer.name }}</p>
                                        </div>
                                        <div class="text-sm">
                                            <p class="font-semibold text-slate-500">{{ associated_consumer.email }}</p>
                                        </div>
                                        <div class="text-sm">
                                            <p class="font-semibold text-slate-500 mr-2">{{ $filters.formatPhoneNumber(associated_consumer.phone) }}</p>
                                            <div v-if="associated_consumer.verified" class="inline-flex flex-wrap items-center font-medium" :class="[darkMode ? 'text-emerald-500' : 'text-emerald-700']">
                                                <svg class="mr-1 fill-current" width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.25 1.75C5.72501 1.75 4.26247 2.3558 3.18414 3.43414C2.1058 4.51247 1.5 5.97501 1.5 7.5C1.5 8.2551 1.64873 9.00281 1.93769 9.70043C2.22666 10.3981 2.6502 11.0319 3.18414 11.5659C3.71807 12.0998 4.35195 12.5233 5.04957 12.8123C5.74719 13.1013 6.4949 13.25 7.25 13.25C8.0051 13.25 8.75281 13.1013 9.45043 12.8123C10.1481 12.5233 10.7819 12.0998 11.3159 11.5659C11.8498 11.0319 12.2733 10.3981 12.5623 9.70043C12.8513 9.00281 13 8.2551 13 7.5C13 5.97501 12.3942 4.51247 11.3159 3.43414C10.2375 2.3558 8.77499 1.75 7.25 1.75ZM2.12348 2.37348C3.48311 1.01384 5.32718 0.25 7.25 0.25C9.17282 0.25 11.0169 1.01384 12.3765 2.37348C13.7362 3.73311 14.5 5.57718 14.5 7.5C14.5 8.45208 14.3125 9.39484 13.9481 10.2745C13.5838 11.1541 13.0497 11.9533 12.3765 12.6265C11.7033 13.2997 10.9041 13.8338 10.0245 14.1981C9.14484 14.5625 8.20208 14.75 7.25 14.75C6.29792 14.75 5.35516 14.5625 4.47554 14.1981C3.59593 13.8338 2.7967 13.2997 2.12348 12.6265C1.45025 11.9533 0.91622 11.1541 0.551873 10.2745C0.187527 9.39484 0 8.45208 0 7.5C0 5.57718 0.763837 3.73311 2.12348 2.37348ZM9.947 5.52523C10.2399 5.81812 10.2399 6.29299 9.947 6.58589L7.05811 9.47477C6.76521 9.76767 6.29034 9.76767 5.99745 9.47477L4.553 8.03033C4.26011 7.73744 4.26011 7.26256 4.553 6.96967C4.8459 6.67678 5.32077 6.67678 5.61366 6.96967L6.52778 7.88378L8.88634 5.52523C9.17923 5.23233 9.6541 5.23233 9.947 5.52523Z"/></svg>
                                                SMS Verified
                                            </div>
                                            <div v-else class="inline-flex flex-wrap items-center font-medium" :class="[darkMode ? 'text-red-500' : 'text-red-700']">
                                                <svg class="mr-1 fill-current" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7 1.75C5.60761 1.75 4.27226 2.30312 3.28769 3.28769C2.30312 4.27226 1.75 5.60761 1.75 7C1.75 7.68944 1.8858 8.37213 2.14963 9.00909C2.41347 9.64605 2.80018 10.2248 3.28769 10.7123C3.7752 11.1998 4.35395 11.5865 4.99091 11.8504C5.62787 12.1142 6.31056 12.25 7 12.25C7.68944 12.25 8.37213 12.1142 9.00909 11.8504C9.64605 11.5865 10.2248 11.1998 10.7123 10.7123C11.1998 10.2248 11.5865 9.64605 11.8504 9.00909C12.1142 8.37213 12.25 7.68944 12.25 7C12.25 5.60761 11.6969 4.27226 10.7123 3.28769C9.72774 2.30312 8.39239 1.75 7 1.75ZM2.22703 2.22703C3.4929 0.961159 5.20979 0.25 7 0.25C8.79021 0.25 10.5071 0.961159 11.773 2.22703C13.0388 3.4929 13.75 5.20979 13.75 7C13.75 7.88642 13.5754 8.76417 13.2362 9.58311C12.897 10.4021 12.3998 11.1462 11.773 11.773C11.1462 12.3998 10.4021 12.897 9.58311 13.2362C8.76417 13.5754 7.88642 13.75 7 13.75C6.11358 13.75 5.23584 13.5754 4.41689 13.2362C3.59794 12.897 2.85382 12.3998 2.22703 11.773C1.60023 11.1462 1.10303 10.4021 0.763813 9.58311C0.424594 8.76417 0.25 7.88642 0.25 7C0.25 5.20979 0.961159 3.4929 2.22703 2.22703ZM5.13634 5.13634C5.42923 4.84344 5.9041 4.84344 6.197 5.13634L7 5.93934L7.803 5.13634C8.0959 4.84344 8.57077 4.84344 8.86366 5.13634C9.15656 5.42923 9.15656 5.9041 8.86366 6.197L8.06066 7L8.86366 7.803C9.15656 8.0959 9.15656 8.57077 8.86366 8.86366C8.57077 9.15656 8.0959 9.15656 7.803 8.86366L7 8.06066L6.197 8.86366C5.9041 9.15656 5.42923 9.15656 5.13634 8.86366C4.84344 8.57077 4.84344 8.0959 5.13634 7.803L5.93934 7L5.13634 6.197C4.84344 5.9041 4.84344 5.42923 5.13634 5.13634Z"/></svg>
                                                SMS Unverified
                                            </div>
                                        </div>
                                        <p class="text-sm">
                                            <Badge color="purple" :dark-mode="darkMode" v-if="associated_consumer.origin">{{ associated_consumer.origin }}</Badge>
                                        </p>
                                        <p class="text-sm">
                                            <Badge v-if="associated_consumer.appointment_status !== null" color="cyan" :dark-mode="darkMode">Appointment {{ associated_consumer.appointment_status }}</Badge>
                                            <Badge v-if="associated_consumer.appointment_status?.toLowerCase() !== 'allocated'" color="cyan" :dark-mode="darkMode">Lead {{ associated_consumer.lead_status }}</Badge>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="p-3 border-t" :class="[darkMode ? 'border-dark-border' : 'border-light-border']"></div>
                </div>
            </template>
        </Modal>
        <AlertsContainer :v-if="alertActive" :text="alertText" :alert-type="alertType" :dark-mode="darkMode" />
    </div>
</template>

<script>
import Tab from "../Shared/components/Tab.vue";
import StarRating from "../Shared/components/StarRating.vue";
import CustomButton from "../Shared/components/CustomButton.vue";
import LoadingSpinner from "../LeadProcessing/components/LoadingSpinner.vue";
import ApiService from "./services/reviews-api";
import Badge from "../Shared/components/Badge.vue";
import Modal from "../Shared/components/Modal.vue";
import CustomInput from "../Shared/components/CustomInput.vue";
import { PERMISSIONS, useRolesPermissions } from "../../../stores/roles-permissions.store.js";
import AlertsContainer from "../Shared/components/AlertsContainer.vue";
import AlertsMixin from "../../mixins/alerts-mixin.js";
export default {
    name: "ReviewDetailsModal",
    components: { AlertsContainer, CustomInput, Modal, Badge, LoadingSpinner, CustomButton, StarRating, Tab},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        review: {
            type: Object,
            default: {}
        },
    },
    data() {
        return {
            api: ApiService.make(),
            selectedTab: 'Details',
            replying: false,
            reply: '',
            related_reviews: [],
            associated_consumers: [],
            loading: false,
            saving: false,
            permissionStore: useRolesPermissions(),
            PERMISSIONS,
        }
    },
    mixins: [AlertsMixin],
    emits: ['toggle-details'],
    computed: {
        orderedCustomKeys() {
            const customData = this.review?.review_data?.data?.custom ?? {};
            const keys = Object.entries(this.review?.review_data?.data?.custom ?? {}).reduce((output, [ key, value ]) =>
                value.type === 'rating' ? output: [ ...output, key ], []);
            return keys.sort((a, b) => customData[a].order > customData[b].order ? 1 : -1);
        },
        orderedRatingKeys() {
            const customData = this.review?.review_data?.data?.custom ?? {};
            const keys = Object.entries(this.review?.review_data?.data?.custom ?? {}).reduce((output, [ key, value ]) =>
                value.type === 'rating' ? [ ...output, key ] : output, []);
            return keys.sort((a, b) => customData[a].order > customData[b].order ? 1 : -1);
        },
    },
    created: function() {
        if(this.review) {
            this.getReviewDetails();
        }
    },
    watch: {
        review() {
            this.getReviewDetails();
        }
    },
    methods: {
        getReviewDetails() {
            this.loading = true;
            this.api.getReviewDetails(this.review.reference).then(resp => {
                this.related_reviews = resp.data.data.related_reviews;
                this.associated_consumers = resp.data.data.associated_consumers;
            }).catch(e => this.showAlert(resp?.data?.message ?? resp.message ?? 'An error occurred'))
                .finally(() => this.loading = false);
        },
        toggleModal() {
            this.$emit('toggle-details');
        },
        toggleReplyBox() {
            this.replying = ! this.replying
            this.reply = ''
        },
        postReply() {
            this.saving = true;
            this.api.postReviewReply(this.review.reference, this.reply).then(resp => {
                this.review.review_replies = resp.data.data.review_replies;
                this.toggleReplyBox()
            }).catch(e => this.showAlert('error', e?.data?.message ?? e.message ?? 'An error occurred'))
                .finally(() => this.saving = false);
        },
        updateReviewDetails(review) {
            this.selectedTab = 'Details';
            this.$emit('updateReview', review);
        },
        approveReview() {
            this.changeReviewStatus(this.api.approveReview.bind(this.api), 'approved');
        },
        rejectReview() {
            this.changeReviewStatus(this.api.rejectReview.bind(this.api), 'rejected');
        },
        pendingReview() {
            this.changeReviewStatus(this.api.setPendingReview.bind(this.api), 'pending');
        },
        async changeReviewStatus(apiFunction, status) {
            this.saving = true;
            const resp = await apiFunction(this.review.reference)
                .catch(e => e)
                .finally(() => this.saving = false);

            if (resp?.data?.data?.status)
                this.review.display_status = status;
            else {
                this.showAlert('error', resp?.data?.message ?? resp?.message ?? 'An error occurred');
            }
        }
    }
}
</script>