<template>
    <div>
        <div class="main-layout font-body">
            <div class="w-full">
                <div class="w-full flex-auto pt-2 relative">
                    <div class="px-10" :class="[darkMode ? 'text-white' : 'text-slate-900']">
                        <div class="flex items-center justify-between flex-wrap py-4">
                            <div class="flex justify-between items-center w-full py-2">
                                <h3 class="text-xl font-medium pb-0 leading-none mr-5">Consumer Reviews</h3>
                            </div>
                        </div>
                        <div>
                            <ReviewsModule
                                :dark-mode="darkMode"
                                :default-filters="{
                                    verifiedFilter: 1,
                                    statusFilter: 0,
                                }"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ReviewsModule from "./ReviewsModule.vue";
import Flows from "../FlowManagement/Flows.vue";

export default {
    name: "CompanyReviews.vue",
    components: { Flows, ReviewsModule },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
    }
}
</script>