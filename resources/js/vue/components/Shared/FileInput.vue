<template>
    <labeled-value :label="label">
        <div class="relative">
            <input
                class="p-1 block w-full text-sm text-gray-900 border border-gray-300 rounded cursor-pointer bg-gray-50 focus:outline-none"
                ref="fileInput"
                type="file"
                :accept="accept"
                :disabled="disabled"
                @change="handleFileChange"
            />
            <div v-if="$refs.fileInput?.value && !disabled" class="absolute right-2 top-1/2 -translate-y-1/2 p-1" @click="clear">
                <simple-icon
                    :dark-mode="darkMode"
                    :icon="simpleIcon.icons.X_MARK"
                    clickable
                />
            </div>
        </div>
        <p class="mt-1 text-sm text-gray-500">
            <slot>
                {{ computedHelpText }}
            </slot>
        </p>
        <p v-if="error" class="text-sm text-red-500 mt-1">{{ error }}</p>
    </labeled-value>
</template>

<script>
import LabeledValue from "./components/LabeledValue.vue"
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue"
import useSimpleIcon from "../../../composables/useSimpleIcon.js"

export default {
    name: "FileInput",
    components: {SimpleIcon, LabeledValue},
    props: {
        label: {
            type: String,
            default: "File",
        },
        accept: {
            type: String,
            default: ".svg,.png,.jpg,.jpeg,.gif",
        },
        maxSize: {
            type: Number,
            default: 10 * 1024 * 1024,
        },
        helpText: {
            type: String,
            default: null,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        modelValue: {
            type: File,
            default: null,
        },
        darkMode: {
            type: Boolean
        }
    },
    emits: ["update:modelValue", "input", "change", "invalid"],
    computed: {
        simpleIcon() {
            return useSimpleIcon()
        },
        computedHelpText() {
            if (this.helpText) return this.helpText

            const types = this.accept
                .split(",")
                .map(type => type.replace(/^\./, "").toUpperCase())
                .join(", ")

            return `Accepts: ${types} — Max size: ${this.formatSize(this.maxSize)}`
        },
    },
    data() {
        return {
            error: null,
        }
    },
    methods: {
        handleFileChange(event) {
            const file = event.target.files[0]
            this.error = null

            if (!file) return

            if (file.size > this.maxSize) {
                this.error = `File size exceeds the limit of ${this.formatSize(this.maxSize)}.`
                this.$emit("invalid", file)
                return
            }

            this.emitEvents(file)
        },
        formatSize(bytes) {
            const sizes = ["Bytes", "KB", "MB", "GB"]
            if (bytes === 0) return "0 Bytes"
            const i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)))
            return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${sizes[i]}`
        },
        emitEvents(file) {
            this.$emit("update:modelValue", file)
            this.$emit("input", file)
            this.$emit("change", file)
        },
        clear() {
            this.$refs.fileInput.value = ""
            this.emitEvents(null)
            this.error = null
        }
    },
}
</script>
