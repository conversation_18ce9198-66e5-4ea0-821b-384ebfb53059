import {ref} from 'vue';

export default function() {
    const alertTypes = {
        error: "error",
        warning: "warning",
        success: "success"
    };

    const alertActive = ref(false);
    const alertType = ref('');
    const alertText = ref('');
    const alertDelay = ref(5000);

    const showAlert = (type, text) => {
        alertActive.value = true;
        alertType.value = type;
        alertText.value = text;

        setTimeout(() => hideAlert(), alertDelay.value);
    };

    const hideAlert = () => {
        alertActive.value = false;
        alertType.value = '';
        alertText.value = '';
    };

    return {
        alertTypes,
        alertActive,
        alertType,
        alertText,
        alertDelay,
        showAlert
    };
}
