import { ref, onUnmounted } from 'vue';

export function useCountdown(start = 3) {
    const count = ref(start);
    let timer = null;

    const startCountdown = (callback = () => {}) => {
        if (timer) return; // Prevent multiple intervals
        count.value = start;
        timer = setInterval(() => {
            if (count.value > 0) {
                count.value--;
            } else {
                clearInterval(timer);
                timer = null;
                callback()
            }
        }, 1000);
    };

    const stopCountdown = () => {
        if (timer) {
            clearInterval(timer);
            timer = null;
        }
    };

    onUnmounted(() => {
        stopCountdown();
    });

    return { count, startCountdown, stopCountdown };
}
