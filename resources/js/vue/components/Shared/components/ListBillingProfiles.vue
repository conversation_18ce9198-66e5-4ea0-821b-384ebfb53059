<template>
    <loading-spinner v-if="loading"/>
    <div v-else class="flex flex-wrap gap-3">
        <simple-card
            v-for="profile in billingProfiles"
            :dark-mode="darkMode"
            class="max-w-[300px]"
        >
            <div class="flex items-center gap-2 p-2">
                <custom-checkbox
                    v-if="selectable"
                    :dark-mode="darkMode"
                    :model-value="modelValue.includes(profile.id)"
                    @click="handleClick(profile)"
                    :input-disabled="disabled"
                />
                <div class="flex justify-start">
                    <div class="flex flex-col mr-1 flex-1">
                        <div>
                            <entity-hyperlink
                                :entity-id="profile.id"
                                :dark-mode="darkMode"
                                type="billing_profile"
                                :suffix="profile.id"
                            >
                            </entity-hyperlink>
                            <p class="line-clamp-2">{{profile.name}}</p>
                        </div>
                        <div class="flex gap-1 items-center">
                            <payment-method-badge
                                :type="profile?.payment_method"
                                :reference="profile?.payment_method_data?.number ? `${profile?.payment_method_data?.number} ${profile?.payment_method_data?.expiry}` : null"
                            />
                            <simple-icon
                                :class="profile.process_auto ? '' : 'opacity-50'"
                                :color="profile.process_auto ? simpleIcon.colors.BLUE : simpleIcon.colors.GRAY"
                                :icon="simpleIcon.icons.HOLDING_MONEY"
                                :tooltip="profile.process_auto ? 'The invoice will be paid as soon as it gets issued' : 'The invoice will NOT be paid as soon as it gets issued'"
                            >
                            </simple-icon>
                            <simple-icon
                                :class="profile.default ? '' : 'opacity-50'"
                                :color="profile.default ? simpleIcon.colors.BLUE : simpleIcon.colors.GRAY"
                                :icon="simpleIcon.icons.FALLBACK"
                                :tooltip="profile.default ? 'Preferred' : 'Not Preferred'"
                            >
                            </simple-icon>
                        </div>
                        <div class="flex flex-wrap gap-3 mt-2">
                            <badge>${{ profile.threshold }}</badge>
                            <p>{{ profile.charge_attempts }} attempts</p>
                            <div v-if="profile?.campaigns?.length > 0"
                                 class="flex items-center gap-1"
                            >
                                <limited-list :show-count="1" :list-items="profile.campaigns"
                                              empty-message="No Associated Campaigns"/>
                            </div>
                        </div>
                    </div>

                    <simple-icon
                        :color="simpleIcon.colors.BLUE"
                        :icon="simpleIcon.icons.ARROW_UP_RIGHT_FROM_SQUARE"
                        tooltip="Open billing profile"
                        :size="simpleIcon.sizes.xs"
                        @click="() => selectedBillingProfileId = profile.id"
                        clickable
                    >
                    </simple-icon>
                </div>
            </div>
        </simple-card>
    </div>
    <edit-billing-profile-modal
        v-if="selectedBillingProfileId"
        :billing-profile-id="selectedBillingProfileId"
        @close="() => selectedBillingProfileId = null"
        readonly
    />
</template>
<script>
import ApiService from "../../BillingManagement/services/billing-profiles-api.js";
import LoadingSpinner from "./LoadingSpinner.vue";
import PaymentMethodBadge from "../../Billing/InvoicePaymentsModule/PaymentMethodBadge.vue";
import Badge from "./Badge.vue";
import SimpleCard from "../../MarketingCampaign/SimpleCard.vue";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import LimitedList from "./Simple/LimitedList.vue";
import EditBillingProfileModal from "../../BillingManagement/components/Modals/EditBillingProfileModal.vue";
import CustomCheckbox from "../SlideWizard/components/CustomCheckbox.vue";
import EntityHyperlink from "../../BillingManagement/components/EntityHyperlink.vue";

export default {
    name: "ListBillingProfiles",
    components: {
        EntityHyperlink,
        CustomCheckbox,
        EditBillingProfileModal, LimitedList, SimpleIcon, SimpleCard, Badge, PaymentMethodBadge, LoadingSpinner
    },
    props: {
        modelValue: {
            type: Array,
            default: []
        },
        companyId: {
            type: Number,
        },
        billingProfileIds: {
            type: Number,
        },
        darkMode: {
            type: Boolean,
            default: false
        },
        disabled: {
            type: Boolean,
            default: false
        },
        selectable: {
            type: Boolean,
            default: false
        },
    },

    data() {
        return {
            billingProfiles: [],
            loading: false,
            billingProfileService: ApiService.make(),
            selectedBillingProfileId: null,
        }
    },

    computed: {
        simpleIcon() {
            return useSimpleIcon()
        },
    },

    mounted() {
        this.getProfiles()
    },

    methods: {
        handleClick(profile) {
            if (this.disabled) return
            const remove = this.modelValue.includes(profile.id)

            if (remove) {
                this.$emit('update:modelValue', this.modelValue.filter(e => e !== profile.id))
            } else {
                this.$emit('update:modelValue', [
                    ...this.modelValue,
                    profile.id
                ])
            }
        },
        async getProfiles() {
            this.loading = true

            const response = await this.billingProfileService.getBillingProfiles({
                company_id: this.companyId,
                ids: this.billingProfileIds,
                show_archived: this.billingProfileIds?.length ? 0 : undefined
            })

            this.billingProfiles = response.data.data

            this.loading = false
        }
    }
}
</script>
