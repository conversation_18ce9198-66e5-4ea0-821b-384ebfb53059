<template>
    <div>
        <slot name="card" v-bind="{item: data}">
            <div :class="cardStyle">
                <labeled-value v-for="header in headers" :label="header.title + ':'" orientation="horizontal" class="flex-1">
                    <slot :name="`row.col.${header.field}`" v-bind="{item: data, value:data[header.field]}">
                        {{data[header.field]}}
                    </slot>
                </labeled-value>
            </div>
        </slot>
    </div>
</template>
<script>
import LabeledValue from "../../LabeledValue.vue";

export default {
    name: "SimpleTableCard",
    components: {LabeledValue},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        headers: {
            type: Array,
            default: [],
        },
        data: {
            type: Object,
            default: {},
        },
        cardStyle: {
            type: String,
            default: 'border rounded-md bg-light-module gap-2 p-2 grid grid-cols-2'
        }
    }
}
</script>
