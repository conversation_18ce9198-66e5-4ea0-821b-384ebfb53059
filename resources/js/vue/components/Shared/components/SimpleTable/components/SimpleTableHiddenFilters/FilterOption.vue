<template>
    <div @click="onSelect" class="capitalize px-3 py-2 flex items-center justify-between cursor-pointer"
         :class="{'text-primary-500 bg-slate-700 bg-opacity-50 font-semibold': darkMode && isSelected, 'text-primary-500 bg-primary-50 font-semibold': !darkMode && isSelected}"
    >
        {{ option.name }}
    </div>
</template>

<script setup>
import {defineEmits, defineProps} from "vue";

/**
 * @typedef {Object} option
 * @property {string} id
 * @property {string} name
 */

const props = defineProps({
    darkMode: {
        type: Boolean,
        default: false
    },
    option: {
        type: Object,
        required: true
    },
    isSelected: {
        type: Boolean,
        required: false,
    }
})

const emit = defineEmits(['select'])

const onSelect = () => {
    emit('select', {
        optionId: props.option.id,
        selecting: !props.isSelected
    })
}
</script>

<script>
export default {
    name: "FilterOption",
}
</script>
