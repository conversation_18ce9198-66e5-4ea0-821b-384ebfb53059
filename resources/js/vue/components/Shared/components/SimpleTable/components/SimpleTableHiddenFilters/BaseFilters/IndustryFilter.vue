<template>
    <MultipleOptionsFilter
        :filter="filter"
        @apply="$emit('apply')"
        @cancel="$emit('cancel')"
        :dark-mode="darkMode"
        :model-value="modelValue"
        @update:modelValue="(...args) => $emit('update:modelValue', ...args)"
    />
</template>

<script>
import SharedApiService from "../../../../../services/api.js";
import MultipleOptionsFilter from "./MultipleOptionsFilter.vue";

export default {
    name: "IndustryFilter",

    components: {
        MultipleOptionsFilter,
    },

    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        filter: {
            type: Object,
            required: true,
        },
        modelValue: {
            type: Array,
            default: () => []
        },
    },

    emits: ['update:modelValue', 'apply', 'cancel'],

    created() {
        this.getIndustryOptions()
    },

    data() {
        return {
            loading: false,
            industryOptions: [],
            sharedApi: SharedApiService.make()
        }
    },

    methods: {
        getIndustryOptions() {
            if (this.filter?.options?.length > 0) return;

            this.filter.loading = true
            this.sharedApi.getOdinIndustries()
                .then(resp => {
                    if (resp.data?.data?.status) {
                        this.filter.options = resp.data.data.industries ?? [];
                    }

                    this.filter.loading = false
                })
        },
    }
}
</script>
