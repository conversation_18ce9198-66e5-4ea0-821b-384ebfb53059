<template>
    <base-filter @apply="$emit('apply')" @cancel="$emit('cancel')" :dark-mode="darkMode">
        <loading-spinner v-if="filter.loading"/>
        <p v-else v-for="(option) in filter.options">
            <filter-option
                :option="option"
                :is-selected="modelValue.includes(option.id)"
                :dark-mode="darkMode"
                @select="onSelect"
            />
        </p>
    </base-filter>
</template>

<script>
import CustomButton from "../../../../CustomButton.vue";
import FilterOption from "../FilterOption.vue";
import BaseFilter from "./BaseFilter.vue";
import LoadingSpinner from "../../../../LoadingSpinner.vue";

export default {
    name: "MultipleOptionsFilter",

    components: {
        LoadingSpinner,
        BaseFilter,
        CustomButton,
        FilterOption
    },

    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        filter: {
            type: Object,
            required: true,
        },
        modelValue: {
            type: Array,
            default: () => []
        },
    },

    emits: ['update:modelValue', 'apply', 'cancel'],

    methods: {
        onSelect(selected) {
            let selectedValue = [...this.modelValue]

            if (selected.selecting) selectedValue.push(selected.optionId)
            else selectedValue = selectedValue.filter(option => option !== selected.optionId)

            this.$emit('update:modelValue', selectedValue);
        }
    }
}
</script>
