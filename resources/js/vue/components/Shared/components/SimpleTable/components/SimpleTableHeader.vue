<template>
    <div class="grid items-end mb-3 gap-x-5 px-5 text-slate-500" :class="[`grid-cols-${gridCols}`]">
        <SimpleTableHeaderCell
            v-for="(header, index) in visibleHeaders"
            :header="header"
            :index="index"
            :check-box-column="checkBoxColumn"
            @sort="(...args) => $emit('sort', ...args)"
            @select-all="handleSelectAll"
            :select-all="selectAll"
            @toggle-timezone="toggleTimezone"
        />
    </div>
</template>

<script>
import SimpleIcon from "../../../../Mailbox/components/SimpleIcon.vue";
import SimpleTableHeaderCell from "./SimpleTableHeaderCell.vue";

export default {
    name: "SimpleTableHeader",
    components: {SimpleTableHeaderCell, SimpleIcon},
    props: {
        headers: {
            type: Array,
            required: true
        },
        gridCols: {
            type: Number,
            required: true
        },
        checkBoxColumn: {
            type: <PERSON>olean,
            default: false,
        },
        headerClasses: {
            type: String
        },
    },
    emits: ['select-all', 'sort', 'toggle-timezone'],
    computed: {
        visibleHeaders() {
            return this.headers.filter(header => header.show);
        },
    },
    data(){
        return {
            selectAll: false
        }
    },
    methods: {
        handleSelectAll() {
            this.selectAll = !this.selectAll
            this.$emit('select-all', this.selectAll)
        },
        toggleTimezone() {
            this.$emit('toggle-timezone');
        },
    }
}
</script>
