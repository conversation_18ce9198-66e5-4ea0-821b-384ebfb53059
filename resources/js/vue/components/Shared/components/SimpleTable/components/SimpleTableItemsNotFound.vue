<template>
    <div class="border-t border-b overflow-y-auto divide-y"
         :class="[darkMode ? 'bg-dark-background  border-dark-border' : 'bg-light-background  border-light-border', wrapperClasses]">
        <p class="w-full h-full flex justify-center items-center text-grey-400">{{ notFoundMessage }}</p>
    </div>
</template>

<script>
export default {
    name: "SimpleTableItemsNotFound",
    props: {
        darkMode: {
            type: Boolean,
            required: true
        },
        notFoundMessage: {
            type: String,
            default: 'No data found'
        },
        wrapperClasses: {
            type: String,
            default: 'h-100'
        }
    },
}
</script>
