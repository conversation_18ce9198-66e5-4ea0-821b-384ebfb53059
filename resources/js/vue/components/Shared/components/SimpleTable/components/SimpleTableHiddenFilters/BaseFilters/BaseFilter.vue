<template>
    <div class="ml-1 rounded-lg border shadow-module text-sm font-medium relative z-0 w-64"
         :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
        <div :class="wrapperClasses">
            <slot></slot>
        </div>
        <div class="flex items-center gap-2 p-2 z-10 border-t w-full"
             :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
            <custom-button @click="$emit('cancel')" :dark-mode="darkMode" color="slate-inverse">
                Cancel
            </custom-button>
            <custom-button
                class="w-full justify-center"
                :dark-mode="darkMode"
                @click="$emit('apply')"
            >
                Apply Filter
            </custom-button>
        </div>
    </div>
</template>

<script>
import CustomButton from "../../../../CustomButton.vue";

export default {
    name: "BaseFilter",

    components: {
        CustomButton,
    },

    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        wrapperClasses: {
            type: String,
            default: 'pb-16 max-h-56 overflow-auto'
        }
    },

    emits: ['apply', 'cancel'],
}
</script>
