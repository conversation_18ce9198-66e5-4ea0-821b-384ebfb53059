<template>
    <div ref="menu">
        <span class="capitalize px-3 py-2 flex items-center justify-between cursor-pointer"
              :class="[
               (isOpen) ? (darkMode ? 'text-primary-500 bg-slate-700 bg-opacity-50 font-semibold' : 'text-primary-500 bg-primary-50 font-semibold') : (darkMode ? 'bg-dark-module hover:bg-dark-background' : 'bg-light-module hover:bg-light-background'),
               isOffscreen ? 'flex-row-reverse' : ''
           ]"
        >
            <p>{{ name }}</p>
            <simple-icon v-if="isOpen" :icon="isOffscreen ? simpleIcon.icons.CHEVRON_LEFT : simpleIcon.icons.CHEVRON_RIGHT"/>
        </span>
        <slot></slot>
        <div
            v-if="isOpen"
            :style="popupStyle"
            class="mx-1"
            ref="popover"
        >
            <component
                v-model="modelValue[this.filter.field]"
                :is="filterComponent"
                :filter="filter"
                :dark-mode="darkMode"
                @apply="$emit('apply')"
                @cancel="$emit('cancel')"
            />
        </div>
    </div>
</template>
<script>
import FilterOptions from "./BaseFilters/MultipleOptionsFilter.vue";
import FilterDateRange from "./BaseFilters/DateRangeFilter.vue";
import SingleOptionFilter from "./BaseFilters/SingleOptionFilter.vue";
import IndustryFilter from "./BaseFilters/IndustryFilter.vue";
import {SimpleTableHiddenFilterTypesEnum} from "../../enum/simpleTableFilterHiddenTypes";
import NumberRangeFilter from "./BaseFilters/NumberRangeFilter.vue";
import usePopoverMenu from "../../../../../../../composables/usePopoverMenu";
import SimpleIcon from "../../../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../../../composables/useSimpleIcon";
import {nextTick} from "vue";

export default {
    name: 'FilterOptionsClickable',
    components: {SimpleIcon, SingleOptionFilter, FilterOptions, FilterDateRange, NumberRangeFilter, IndustryFilter},
    props: {
        modelValue: {
            type: Object,
            required: true
        },
        filter: {
            type: Object,
            required: true
        },
        isOpen: {
            type: Boolean,
            required: true
        },
        darkMode: {
            type: Boolean,
            required: true
        },
        name: {
            type: String,
            required: true
        },
        options: {
            type: Array,
            default: () => [],
            required: false,
        }
    },
    emits: ['apply', 'cancel'],

    computed: {
        filterComponent() {
            const components = {
                [SimpleTableHiddenFilterTypesEnum.SINGLE_OPTION]: SingleOptionFilter,
                [SimpleTableHiddenFilterTypesEnum.MULTIPLE_OPTIONS]: FilterOptions,
                [SimpleTableHiddenFilterTypesEnum.DATE_RANGE]: FilterDateRange,
                [SimpleTableHiddenFilterTypesEnum.NUMBER_RANGE]: NumberRangeFilter,
                [SimpleTableHiddenFilterTypesEnum.INDUSTRY]: IndustryFilter,

                [SimpleTableHiddenFilterTypesEnum.CUSTOM]: this.filter.component,
            }

            return components[this.filter.type] ?? '';
        }
    },

    data() {
        return {
            simpleIcon: useSimpleIcon(),
            popoverMenu: usePopoverMenu(),
            popupStyle: {
                'position': 'absolute',
                'top': 0,
                'left': '100%',
            },
            isOffscreen: false,
        }
    },

    mounted() {
        this.calculateMenuPosition()
    },

    watch: {
        isOpen(val) {
            if (val) this.calculateMenuPosition()
        }
    },

    methods: {
        async calculateMenuPosition() {
            await nextTick()
            const {popover, menu} = this.$refs

            this.isOffscreen = this.popoverMenu.checkIfElementIsOffScreen(popover, menu);

            if (this.isOffscreen) {
                this.popupStyle = {
                    'position': 'absolute',
                    'top': 0,
                    'right': '100%',
                }
            }
        }
    },

}
</script>
