<template>
    <base-filter @apply="$emit('apply')" @cancel="$emit('cancel')" :dark-mode="darkMode" wrapper-classes="pb-16 max-h-56">
        <div class="p-2 gap-2 flex flex-col">
            <div>
                <p class="mb-1">From: </p>
                <date-picker
                    :enable-time-picker="false"
                    :dark="darkMode"
                    v-model="localFrom"
                    @update:model-value="handleUpdate"
                    auto-apply
                    placeholder="mm-dd-yy"
                    format="MM-dd-yy"
                    :timezone="filter.timezone"
                >
                    <template #input-icon>
                        <svg class="ml-2" width="14" height="16" viewBox="0 0 14 16" fill="none"
                             xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M3.11133 7H4.66688V8.55556H3.11133V7ZM3.11133 10.1111H4.66688V11.6667H3.11133V10.1111ZM6.22244 7H7.77799V8.55556H6.22244V7ZM6.22244 10.1111H7.77799V11.6667H6.22244V10.1111ZM9.33355 7H10.8891V8.55556H9.33355V7ZM9.33355 10.1111H10.8891V11.6667H9.33355V10.1111Z"
                                fill="#0081FF"/>
                            <path
                                d="M1.55556 15.5556H12.4444C13.3023 15.5556 14 14.8579 14 14V3.11111C14 2.25322 13.3023 1.55556 12.4444 1.55556H10.8889V0H9.33333V1.55556H4.66667V0H3.11111V1.55556H1.55556C0.697667 1.55556 0 2.25322 0 3.11111V14C0 14.8579 0.697667 15.5556 1.55556 15.5556ZM12.4444 4.66667L12.4452 14H1.55556V4.66667H12.4444Z"
                                fill="#0081FF"/>
                        </svg>
                    </template>
                </date-picker>
            </div>
            <div>
                <p class="mb-1">To: </p>
                <date-picker
                    :enable-time-picker="false"
                    :dark="darkMode"
                    v-model="localTo"
                    @update:model-value="handleUpdate"
                    auto-apply
                    placeholder="mm-dd-yy"
                    format="MM-dd-yy">
                    <template #input-icon>
                        <svg class="ml-2" width="14" height="16" viewBox="0 0 14 16" fill="none"
                             xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M3.11133 7H4.66688V8.55556H3.11133V7ZM3.11133 10.1111H4.66688V11.6667H3.11133V10.1111ZM6.22244 7H7.77799V8.55556H6.22244V7ZM6.22244 10.1111H7.77799V11.6667H6.22244V10.1111ZM9.33355 7H10.8891V8.55556H9.33355V7ZM9.33355 10.1111H10.8891V11.6667H9.33355V10.1111Z"
                                fill="#0081FF"/>
                            <path
                                d="M1.55556 15.5556H12.4444C13.3023 15.5556 14 14.8579 14 14V3.11111C14 2.25322 13.3023 1.55556 12.4444 1.55556H10.8889V0H9.33333V1.55556H4.66667V0H3.11111V1.55556H1.55556C0.697667 1.55556 0 2.25322 0 3.11111V14C0 14.8579 0.697667 15.5556 1.55556 15.5556ZM12.4444 4.66667L12.4452 14H1.55556V4.66667H12.4444Z"
                                fill="#0081FF"/>
                        </svg>
                    </template>
                </date-picker>
            </div>
        </div>
    </base-filter>
</template>

<script>
import CustomButton from "../../../../CustomButton.vue";
import FilterOption from "../FilterOption.vue";
import DatePicker from '@vuepic/vue-datepicker';
import {DateTime} from "luxon";
import BaseFilter from "./BaseFilter.vue";

export default {
    name: "DateRangeFilter",

    components: {
        BaseFilter,
        CustomButton,
        FilterOption,
        DatePicker
    },

    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        filter: {
            type: Object,
            required: true,
        },
        modelValue: {
            type: Object,
            default: () => {}
        },
    },

    emits: ['update:modelValue', 'apply', 'cancel'],

    data(){
        return {
            localFrom: null,
            localTo: null,
        }
    },

    beforeMount() {
        if (this.modelValue?.from) this.localFrom = DateTime.fromISO(this.modelValue.from).toJSDate()
        if (this.modelValue?.to) this.localTo = DateTime.fromISO(this.modelValue.to).toJSDate()
    },

    methods: {
        handleUpdate(){
            this.$emit('update:modelValue', {
                from: this.localFrom ? DateTime.fromJSDate(this.localFrom).toISO() : null,
                to: this.localTo ? DateTime.fromJSDate(this.localTo).set({ hour: 23, minute: 59, second: 59, millisecond:0 }).toISO() : null,
            })
        },
    }
}
</script>
