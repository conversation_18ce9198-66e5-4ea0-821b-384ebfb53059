<template>
    <div class="relative">
        <div v-if="activeMenu === 'filters'" @click="toggleFilters" class="fixed inset-0 z-0"></div>
        <div
            ref="menu"
            class="relative z-10"
        >
            <custom-button v-if="filters.length > 0" id="filters-button" @click="toggleFilters" :dark-mode="darkMode" color="slate-inverse">
                <svg class="fill-current mr-2" width="15" height="14" viewBox="0 0 15 14" fill="none"
                     xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M15 2.33333C15 2.76289 14.6269 3.11111 14.1667 3.11111L6.52369 3.11111C6.40056 3.43615 6.2006 3.73482 5.93443 3.98325C5.46559 4.42083 4.82971 4.66667 4.16667 4.66667C3.50363 4.66667 2.86774 4.42083 2.3989 3.98325C2.13273 3.73482 1.93278 3.43615 1.80964 3.11111L0.833335 3.11111C0.373098 3.11111 1.30974e-06 2.76289 1.32852e-06 2.33333C1.34729e-06 1.90378 0.373098 1.55556 0.833335 1.55556L1.80964 1.55556C1.93278 1.23052 2.13273 0.931843 2.3989 0.683417C2.86774 0.245832 3.50363 -5.02522e-07 4.16667 -4.7354e-07C4.82971 -4.44558e-07 5.46559 0.245832 5.93443 0.683417C6.2006 0.931843 6.40056 1.23052 6.52369 1.55556L14.1667 1.55556C14.6269 1.55556 15 1.90378 15 2.33333ZM15 7C15 7.42956 14.6269 7.77778 14.1667 7.77778L13.1904 7.77778C13.0672 8.10281 12.8673 8.40149 12.6011 8.64992C12.1323 9.0875 11.4964 9.33333 10.8333 9.33333C10.1703 9.33333 9.53441 9.0875 9.06557 8.64992C8.7994 8.40149 8.59944 8.10281 8.47631 7.77778L0.833334 7.77778C0.373098 7.77778 1.10576e-06 7.42956 1.12453e-06 7C1.14331e-06 6.57045 0.373098 6.22222 0.833334 6.22222L8.47631 6.22222C8.59944 5.89719 8.7994 5.59851 9.06557 5.35008C9.53441 4.9125 10.1703 4.66667 10.8333 4.66667C11.4964 4.66667 12.1323 4.9125 12.6011 5.35009C12.8673 5.59851 13.0672 5.89719 13.1904 6.22222L14.1667 6.22222C14.6269 6.22222 15 6.57045 15 7ZM15 11.6667C15 12.0962 14.6269 12.4444 14.1667 12.4444L6.52369 12.4444C6.40056 12.7695 6.2006 13.0682 5.93443 13.3166C5.46559 13.7542 4.82971 14 4.16667 14C3.50363 14 2.86774 13.7542 2.3989 13.3166C2.13273 13.0682 1.93278 12.7695 1.80964 12.4444L0.833334 12.4444C0.373097 12.4444 9.01769e-07 12.0962 9.20545e-07 11.6667C9.39322e-07 11.2371 0.373097 10.8889 0.833334 10.8889L1.80964 10.8889C1.93278 10.5639 2.13273 10.2652 2.3989 10.0168C2.86774 9.57917 3.50363 9.33333 4.16667 9.33333C4.82971 9.33333 5.46559 9.57917 5.93443 10.0168C6.2006 10.2652 6.40056 10.5639 6.52369 10.8889L14.1667 10.8889C14.6269 10.8889 15 11.2371 15 11.6667ZM11.6667 7C11.6667 6.79372 11.5789 6.59589 11.4226 6.45003C11.2663 6.30417 11.0543 6.22222 10.8333 6.22222C10.6123 6.22222 10.4004 6.30417 10.2441 6.45003C10.0878 6.59589 10 6.79372 10 7C10 7.20628 10.0878 7.40411 10.2441 7.54997C10.4004 7.69583 10.6123 7.77778 10.8333 7.77778C11.0543 7.77778 11.2663 7.69583 11.4226 7.54997C11.5789 7.40411 11.6667 7.20628 11.6667 7ZM5 2.33333C5 2.12705 4.9122 1.92922 4.75592 1.78336C4.59964 1.6375 4.38768 1.55556 4.16667 1.55556C3.94565 1.55556 3.73369 1.6375 3.57741 1.78336C3.42113 1.92922 3.33333 2.12705 3.33333 2.33333C3.33333 2.53961 3.42113 2.73744 3.57741 2.88331C3.73369 3.02917 3.94565 3.11111 4.16667 3.11111C4.38768 3.11111 4.59964 3.02917 4.75592 2.88331C4.9122 2.73744 5 2.53961 5 2.33333ZM5 11.6667C5 11.4604 4.9122 11.2626 4.75592 11.1167C4.59964 10.9708 4.38768 10.8889 4.16667 10.8889C3.94565 10.8889 3.73369 10.9708 3.57741 11.1167C3.42113 11.2626 3.33333 11.4604 3.33333 11.6667C3.33333 11.8729 3.42113 12.0708 3.57741 12.2166C3.73369 12.3625 3.94565 12.4444 4.16667 12.4444C4.38768 12.4444 4.59964 12.3625 4.75592 12.2166C4.9122 12.0708 5 11.8729 5 11.6667Z"/>
                </svg>
                Filters
            </custom-button>
            <div ref="popover" v-if="activeMenu === 'filters'" class="z-20 absolute top-10 inline-flex items-start" :class="[...popupStyle]">
                <div class="rounded-lg border w-64 max-h-56 shadow-module text-sm font-medium overflow-auto"
                     :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
                    <filter-options-clickable
                        v-for="(filter, filterIdx) in filters"
                        :model-value="modelValue"
                        @update:modelValue="$emit('update:modelValue', $event)"
                        :isOpen="openedFilterIdx === filterIdx"
                        @click="openedFilterIdx = filterIdx"
                        :dark-mode="darkMode"
                        :id="filter.field"
                        :name="filter.title"
                        :options="filter.options"
                        :filter="filter"
                        @apply="$emit('apply')"
                        @cancel="toggleFilters"
                    />
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import FilterOptionsClickable from "./FilterOptionsClickable.vue";
import FilterOptions from "./BaseFilters/MultipleOptionsFilter.vue";

import CustomButton from "../../../CustomButton.vue";
import {nextTick} from "vue";
import usePopoverMenu from "../../../../../../../composables/usePopoverMenu.js";

export default {
    name: "SimpleTableFilters",
    components: { CustomButton, FilterOptionsClickable, FilterOptions },
    props: {
        modelValue: {
            type: Object,
            required: true
        },
        darkMode: {
            type: Boolean,
            required: true
        },
        filters: {
            type: Array,
            required: true
        },
        activeMenu: String,
    },

    data(){
        return {
            openedFilterIdx: 0,
            popoverMenu: usePopoverMenu(),
            popupStyle: ['left-0'],
        }
    },
    emits: ['active-menu', 'apply'],

    watch: {
        activeMenu(){
            this.calculateMenuPosition()
        }
    },

    methods: {
        async calculateMenuPosition() {
            await nextTick()
            const {popover, menu} = this.$refs

            this.isOffscreen = this.popoverMenu.checkIfElementIsOffScreen(popover, menu);

            if (this.isOffscreen) {
                this.popupStyle = ['right-0']
            }
        },
        reset(){
          this.openedFilterIdx = 0
        },

        toggleFilters(){
            this.reset()
            this.$emit('active-menu', 'filters');
        }
    }
}
</script>
