<template>
    <div
        :class="[`col-span-${header.cols ?? 1}`, headerClasses]"
    >
        <input
            v-if="checkBoxColumn && index === 0"
            :value="selectAll"
            class="mr-3 rounded"
            @click="handleSelectAll()"
            type="checkbox"
        >
        <div @click="handleSort" class="flex items-center gap-1" :class="[sortByStyle]">
            <SimpleIcon
                v-if="showSortIcon"
                :icon="simpleIcon.icons.CHEVRON_UP"
                :color="simpleIcon.colors.CURRENT"
                :size="simpleIcon.sizes.XS"
                class="transform transition-all duration-200"
                :class="{'rotate-180': header.sort_by === 'desc'}"
            />
            <p>
                {{ header.title }}
            </p>
            <div v-if="header.timestamp && header.timezoneField"
                 :class="[header.useLocalTime ? 'text-slate-500' : 'text-primary-500']"
                 class="cursor-pointer hover:text-primary-300 ml-1"
                 @click="toggleTimezone(header)"
            >
                <svg class="size-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="m6.115 5.19.319 1.913A6 6 0 0 0 8.11 10.36L9.75 12l-.387.775c-.217.433-.132.956.21 1.298l1.348 1.348c.21.21.329.497.329.795v1.089c0 .426.24.815.622 1.006l.153.076c.433.217.956.132 1.298-.21l.723-.723a8.7 8.7 0 0 0 2.288-4.042 1.087 1.087 0 0 0-.358-1.099l-1.33-1.108c-.251-.21-.582-.299-.905-.245l-1.17.195a1.125 1.125 0 0 1-.98-.314l-.295-.295a1.125 1.125 0 0 1 0-1.591l.13-.132a1.125 1.125 0 0 1 1.3-.21l.603.302a.809.809 0 0 0 1.086-1.086L14.25 7.5l1.256-.837a4.5 4.5 0 0 0 1.528-1.732l.146-.292M6.115 5.19A9 9 0 1 0 17.18 4.64M6.115 5.19A8.965 8.965 0 0 1 12 3c1.929 0 3.716.607 5.18 1.64" />
                </svg>
            </div>
        </div>
    </div>
</template>

<script>
import useSimpleIcon from "../../../../../../composables/useSimpleIcon.js";
import SimpleIcon from "../../../../Mailbox/components/SimpleIcon.vue";

export default {
    name: "SimpleTableHeaderCell",
    components: {SimpleIcon},
    props: {
        header: {
            type: Object,
            required: true
        },
        index: {
            type: Number,
            required: false
        },
        checkBoxColumn: {
            type: Boolean,
            default: false,
        },
        headerClasses: {
            type: String,
            default: "uppercase text-xs font-medium text-left rounded-lg flex items-center",
        },
        selectAll: {
            type: Boolean,
            default: false
        }
    },
    emits: ['select-all', 'sort', 'toggle-timezone'],
    computed: {
        simpleIcon() {
            return useSimpleIcon()
        },
        sortByStyle() {
            return [
                this.header.sortable ? 'cursor-pointer text-blue-500 font-semibold hover:text-blue-400' : ''
            ]
        },
        icon() {
            return {
                asc: this.simpleIcon.icons.CHEVRON_UP,
                desc: this.simpleIcon.icons.CHEVRON_DOWN,
            }[this.sortBy]
        },
        showSortIcon() {
            return this.header.sortable && this.header.sort_by
        }
    },
    methods: {
        handleSelectAll() {
            this.$emit('select-all')
        },
        handleSort() {
            if (!this.header.sortable) {
                return;
            }

            const currentSortOrientation = this.header.sort_by ?? 'none';

            const sortOrientations = {
                none: 'asc',
                asc: 'desc',
                desc: null,
            };

            const orientation = sortOrientations[currentSortOrientation]

            this.header.sort_by = orientation
            this.header.number = this.header.number || (!orientation ? null : +new Date());
            this.$emit('sort', {orientation, field: this.header.field})
        },
        toggleTimezone(header) {
            header.useLocalTime = !header.useLocalTime;
            this.$emit('toggle-timezone');
        },
    },

}
</script>
