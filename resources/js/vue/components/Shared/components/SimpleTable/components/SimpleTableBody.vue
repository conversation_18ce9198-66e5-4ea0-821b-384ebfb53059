<template>
    <simple-table-loading-items
        v-if="loading"
        :dark-mode="darkMode"
        :class="wrapperClasses"
    />
    <simple-table-items-not-found v-else-if="data.length === 0" :dark-mode="darkMode" :not-found-message="notFoundMessage" :wrapper-classes="notFoundWrapperClasses" />
    <div v-else
         class="border-t border-b overflow-y-auto divide-y"
         :class="[darkMode ? 'bg-dark-background border-dark-border text-slate-200' : 'bg-light-background  border-light-border text-slate-800', wrapperClasses]">
        <slot></slot>
    </div>
</template>

<script>
import SimpleTableItemsNotFound from "./SimpleTableItemsNotFound.vue";
import SimpleTableLoadingItems from "./SimpleTableLoadingItems.vue";

export default {
    name: "SimpleTableBody",
    components: { SimpleTableItemsNotFound, SimpleTableLoadingItems },
    props: {
        darkMode: {
            type: <PERSON>olean,
            required: true
        },
        data: {
            type: Array,
            required: true,
            default: []
        },
        gridCols: {
            type: Number,
            required: true
        },
        notFoundMessage: {
            type: String,
            required: false
        },
        loading: {
            type: Boolean,
            required: false
        },
        wrapperClasses: {
            type: String,
            default: 'h-100'
        },
        notFoundWrapperClasses: {
            type: String,
            default: 'h-100'
        }
    },
    methods: {
        getColSpanByField(field){
            const header = this.formattedHeaders.find(header => header.field === field)

            return header ? header.cols : 1
        },
    },
}
</script>
