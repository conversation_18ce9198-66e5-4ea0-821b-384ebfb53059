<template>
    <simple-card class="p-2">
        <div class="grid grid-cols-4 gap-1">
            <labeled-value v-if="args.billing_profile_id" label="Id">
                <entity-hyperlink
                    :entity-id="args.billing_profile_id"
                    type="billing_profile"
                    :dark-mode="darkMode"
                    :prefix="args.billing_profile_id"
                />
            </labeled-value>
            <labeled-value label="Company">
                <entity-hyperlink
                    type="company" :entity-id="args.company_id"
                    :prefix="args.company_name"
                >
                </entity-hyperlink>
            </labeled-value>
            <labeled-value v-if="args.name?.length > 0" label="Name">
                {{ args.name }}
            </labeled-value>
            <labeled-value v-if="args.default != null" label="Default">
                {{ args.default ? 'Yes' : 'No' }}
            </labeled-value>
            <labeled-value v-if="args.process_auto != null" label="Process auto">
                {{ args.process_auto ? 'Yes' : 'No' }}
            </labeled-value>
            <labeled-value v-if="args.threshold_in_dollars != null" label="Threshold">
                <badge class="h-fit">${{ args.threshold_in_dollars }}</badge>
            </labeled-value>
            <labeled-value v-if="args.max_allowed_charge_attempts != null" label="Max allowed charge attempts">
                <badge class="h-fit">{{ args.max_allowed_charge_attempts }}</badge>
            </labeled-value>
            <labeled-value v-if="args.due_in_days != null" label="Due In Days">
                {{ args.due_in_days }} days
            </labeled-value>
            <labeled-value
                v-if="args.payment_method != null"
                label="Payment Type"
            >
                <payment-method-badge
                    :type="args.payment_method"
                    :reference="args.payment_method_number ? `${args.payment_method_number} ${args.payment_method_expiry_month}/${args.payment_method_expiry_year}` : undefined"
                />
            </labeled-value>
            <labeled-value v-if="args?.associated_campaign_names?.length != null" label="Associated campaigns">
                <limited-list v-if="args.associated_campaign_ids.length > 0" expandable :show-count="3" :list-items="args.associated_campaign_names">
                    <template #list-item="{item}">
                        <badge>{{item}}</badge>
                    </template>
                    <template #hidden-item="{item}">
                        <p>{{item}}</p>
                    </template>
                </limited-list>
                <p v-else>None</p>
            </labeled-value>
            <labeled-value v-if="args?.associated_campaign_ids?.length != null" label="Associated campaigns count">
                <p v-if="args?.associated_campaign_ids?.length > 0">{{ args?.associated_campaign_ids?.length }}</p>
                <p v-else>None</p>
            </labeled-value>
            <labeled-value v-if="args?.invoice_template_name" label="Invoice Template">
                <p>{{ args?.invoice_template_name ?? 'Automatic' }}</p>
            </labeled-value>
            <labeled-value v-if="args?.frequency_type != null" label="Issue frequency" class="col-span-2">
                {{ frequencyHumanText }}
            </labeled-value>
        </div>
    </simple-card>
    <div class="w-full h-0.5 bg-gray-200 rounded my-3">
    </div>
    <labeled-value
        v-if="approval.requested_action_slug === 'update_billing_profile'"
    >
        <template #label>
            <p class="font-semibold text-lg">Current Data</p>
        </template>
       <template #default>
           <simple-card class="p-2">
               <billing-profile-form
                   readonly
                   :billing-profile-id="approval.related_id"
               />
           </simple-card>
       </template>
    </labeled-value>
</template>
<script>
import EntityHyperlink from "../../../../BillingManagement/components/EntityHyperlink.vue";
import Badge from "../../Badge.vue";
import PaymentMethodBadge from "../../../../Billing/InvoicePaymentsModule/PaymentMethodBadge.vue";
import LimitedList from "../../Simple/LimitedList.vue";
import LabeledValue from "../../LabeledValue.vue";
import {useSimpleFrequencyHelper} from "../../../../../../composables/useSimpleFrequencyHelper.js";
import SimpleCard from "../../../../MarketingCampaign/SimpleCard.vue";
import BillingProfileForm from "../../../../BillingManagement/components/Modals/BillingProfileForm.vue";
import IdentifiedContactBadge from "../../../../Mailbox/components/IdentifiedContact/IdentifiedContactBadge.vue";

export default {
    name: "SaveBillingProfileSummary",
    components: {
        IdentifiedContactBadge,
        BillingProfileForm, SimpleCard, LabeledValue, LimitedList, PaymentMethodBadge, EntityHyperlink, Badge},
    props: {
        approval: {
            type: Object,
            default: {}
        }
    },
    computed: {
        args() {
            return this.approval?.payload?.arguments
        },
        simpleFrequencyHelper() {
            return useSimpleFrequencyHelper()
        },
        frequencyHumanText(){
            return this.simpleFrequencyHelper.getHumanReadableText(this.args.frequency_type, this.args.frequency_data)
        }
    }
}
</script>
