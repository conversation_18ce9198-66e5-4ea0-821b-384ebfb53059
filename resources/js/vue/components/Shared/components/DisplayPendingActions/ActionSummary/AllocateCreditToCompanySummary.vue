<template>
    <div class="flex gap-1 flex-wrap">
        <p>Allocate</p>
        <strong>{{ $filters.centsToFormattedDollars(approval?.payload?.arguments?.amount) }}</strong>
        <p>of</p>
        <badge class="h-fit">{{ approval?.payload?.arguments?.name }}</badge>
        <p>to</p>
        <entity-hyperlink
            type="company"
            :entity-id="approval.company?.id"
            :prefix="approval.company?.name"
        >
        </entity-hyperlink>
    </div>
    <labeled-value label="Billing Profiles">
        <list-billing-profiles
            v-if="approval?.payload?.arguments?.billingProfileIds?.length > 0"
            :company-id="approval.company.id"
            :billing-profile-ids="approval?.payload?.arguments?.billingProfileIds"
        />
        <div v-else>
            Available for all billing profiles
        </div>
    </labeled-value>
</template>
<script>
import EntityHyperlink from "../../../../BillingManagement/components/EntityHyperlink.vue";
import Badge from "../../Badge.vue";
import ListBillingProfiles from "../../ListBillingProfiles.vue";
import LabeledValue from "../../LabeledValue.vue";

export default {
    name: "AllocateCreditToCompanySummary",
    components: {LabeledValue, ListBillingProfiles, Badge, EntityHyperlink},
    props: {
        approval: {
            type: Object,
            default: {}
        }
    }
}
</script>
