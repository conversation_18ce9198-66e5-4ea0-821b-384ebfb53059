<template>
    <div class="flex gap-1 flex-wrap">
        Restore Billing Profile
    </div>
    <labeled-value label="Billing Profile">
        <simple-card class="p-2">
            <billing-profile-form readonly :billing-profile-id="approval.related_id" />
        </simple-card>
    </labeled-value>
</template>
<script>
import EntityHyperlink from "../../../../BillingManagement/components/EntityHyperlink.vue";
import Badge from "../../Badge.vue";
import BillingProfileForm from "../../../../BillingManagement/components/Modals/BillingProfileForm.vue";
import SimpleCard from "../../../../MarketingCampaign/SimpleCard.vue";
import LabeledValue from "../../LabeledValue.vue";

export default {
    name: "ArchiveBillingProfileSummary",
    components: {LabeledValue, SimpleCard, BillingProfileForm, Badge, EntityHyperlink},
    props: {
        approval: {
            type: Object,
            default: {}
        }
    }
}
</script>
