<template>
    <loading-spinner v-if="loading"/>
    <div v-else-if="!loading && pendingApprovalCount > 0">
        <div class="flex items-center gap-1">
            <p class="font-semibold">Pending approvals ({{ pendingApprovalCount }})</p>
            <simple-icon
                v-if="!loading"
                @click="toggleShowPendingApprovals"
                :icon="showPendingApprovals ? simpleIcon.icons.CHEVRON_UP : simpleIcon.icons.CHEVRON_DOWN"
                clickable
            />
        </div>
        <div v-if="showPendingApprovals" class="grid gap-2 pr-4 max-h-56 overflow-auto" :class="`grid-cols-${gridCols}`">
            <div v-for="approval in pendingApprovals.data"
                 class="p-4 rounded-lg w-full flex shadow border gap-6 relative"
                 :class="[getCustomColorColor(approval)]"
            >
                <div v-if="cancelling" class="absolute left-0 top-0 w-full h-full flex items-center justify-center">
                    <loading-spinner />
                </div>
                <div class="flex flex-1 gap-3" :class="[cancelling ? 'blur-sm' : '']">
                    <labeled-value
                        label="Action"
                        :class="[
                                approval.reviewed_at ? 'col-span-3' : 'col-span-5',
                                'flex-1'
                            ]"
                    >
                        <component
                            v-if="getApprovalSummaryComponent(approval)"
                            :is="getApprovalSummaryComponent(approval)"
                            :approval="approval"
                        />
                        <div v-else>{{ approval.requested_action }}</div>
                    </labeled-value>
                    <labeled-value label="Request" class="col-span-2">
                        <div class="flex flex-col gap-1">
                            <div class="flex gap-1 items-center">
                                <simple-icon :icon="simpleIcon.icons.REQUESTER" tooltip="Requester"/>
                                <p class="font-semibold truncate">{{ approval.requested_by?.name }}</p>
                            </div>
                            <div class="flex gap-1 items-center">
                                <simple-icon :icon="simpleIcon.icons.CALENDAR" tooltip="Requested at"/>
                                <p>{{ approval.requested_at }}</p>
                            </div>
                        </div>
                    </labeled-value>
                    <labeled-value v-if="approval.reviewed_at" label="Review" class="col-span-2">
                        <div class="flex flex-col gap-1">
                            <div class="flex gap-1 items-center">
                                <simple-icon :icon="simpleIcon.icons.REVIEWER" tooltip="Reviewer"/>
                                <p class="font-semibold truncate">{{ approval.reviewed_by?.name }}</p>
                            </div>
                            <div class="flex gap-1 items-center">
                                <simple-icon :icon="simpleIcon.icons.CALENDAR" tooltip="Reviewed at"/>
                                <p>{{ approval.reviewed_at }}</p>
                            </div>
                        </div>
                    </labeled-value>
                    <invoice-action-request-status-badge class="h-fit w-fit" :status="approval.status"/>
                </div>
                <div class="flex flex-col gap-1" :class="[cancelling ? 'blur-sm' : '']">
                    <simple-icon
                        v-if="isTheAuthor(approval)"
                        :icon="simpleIcon.icons.X_MARK"
                        tooltip="Cancel"
                        @click="() => cancel(approval.id)"
                        :disabled="cancelling"
                        clickable
                    ></simple-icon>
                    <simple-icon
                        :icon="simpleIcon.icons.ARROW_UP_RIGHT_FROM_SQUARE"
                        tooltip="Open approval"
                        @click="openApproval(approval)"
                        :clickable="hasFinanceRole"
                        :class="!hasFinanceRole ? 'cursor-not-allowed' : ''"
                    ></simple-icon>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import LabeledValue from "../LabeledValue.vue";
import LoadingSpinner from "../LoadingSpinner.vue";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import EntityHyperlink from "../../../BillingManagement/components/EntityHyperlink.vue";
import Badge from "../Badge.vue";
import InvoiceActionRequestStatusBadge
    from "../../../BillingManagement/Tabs/InvoceActionRequest/InvoiceActionRequestStatusBadge.vue";
import useSimpleIcon from "../../../../../composables/useSimpleIcon.js";
import AllocateCreditToCompanySummary from "./ActionSummary/AllocateCreditToCompanySummary.vue";
import {markRaw} from "vue";
import ExpireCompanyCreditSummary from "./ActionSummary/ExpireCompanyCreditSummary.vue";
import ExtendCompanyCreditSummary from "./ActionSummary/ExtendCompanyCreditSummary.vue";
import useQueryParams from "../../../../../composables/useQueryParams.js";
import {useRolesPermissions, ROLES} from "../../../../../stores/roles-permissions.store.js";
import ApiService from "../../../BillingManagement/services/invoce-action-requests.js";
import {useUserStore} from "../../../../../stores/user-store.js";
import {useToastNotificationStore} from "../../../../../stores/billing/tost-notification.store.js";

const queryParamsHelper = useQueryParams()
export default {
    name: "DisplayPendingActions",
    components: {
        AllocateCreditToCompanySummary,
        InvoiceActionRequestStatusBadge,
        Badge,
        EntityHyperlink,
        SimpleIcon,
        LoadingSpinner,
        LabeledValue,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        relatedType: {
            type: String,
        },
        relatedId: {
            type: [String, Number],
            required: false
        },
        companyId: {
            type: [String, Number],
        },
        isInitiallyOpen: {
            type: Boolean,
            default: true
        },
        actionTypes: {
            type: Array,
        },
        gridCols: {
            type: Number,
            default: 1
        }
    },
    data() {
        return {
            showPendingApprovals: this.isInitiallyOpen,
            rolesPermissions: useRolesPermissions(),
            userStore: useUserStore(),
            loading: null,
            pendingApprovals: {},
            api: ApiService.make(),
            statuses: {
                approved: 'approved',
                rejected: 'rejected',
                pending: 'pending',
            },
            cancelling: false,
            toastNotification: useToastNotificationStore()
        }
    },

    mounted() {
        this.getApprovals()
    },
    computed: {
        pendingApprovalCount() {
            return this.pendingApprovals.data?.length
        },
        simpleIcon() {
            return useSimpleIcon()
        },
        hasFinanceRole() {
            return this.rolesPermissions.hasAnyRole([
                ROLES.FINANCE_CONTROLLER,
                ROLES.FINANCE_OWNER,
            ])
        }
    },
    methods: {
        getApprovals(){
            this.listPendingApprovals({
                relatedId: this.relatedId,
                relatedType: this.relatedType,
                companyId: this.companyId,
                actionTypes: this.actionTypes
            })
        },
        isTheAuthor(approval){
            return  this.userStore.user.id === approval?.requested_by?.id
        },
        async cancel(id) {
            if (this.cancelling) {
                return
            }

            this.cancelling = true;

            try {
                await this.api.cancel(id)
                this.toastNotification.notifySuccess('Action cancelled')
                this.getApprovals()
            } catch (err) {
                this.toastNotification.notifyError(err.message)
            }

            this.cancelling = false;
        },
        async listPendingApprovals({relatedId, relatedType, companyId, actionTypes}) {
            this.loading = true;

            const response = await this.api.getActionRequests({
                related_ids: [relatedId].filter(e => !!e),
                related_types: [relatedType].filter(e => !!e),
                company_id: companyId,
                action_types: actionTypes,
                status: 'pending',
                all: true,
            })

            this.pendingApprovals = response.data

            this.loading = false;
        },
        getCustomColorColor(approval) {
            return {
                [this.statuses.approved]: 'border-green-500',
                [this.statuses.rejected]: 'border-red-500',
                [this.statuses.pending]: 'border-yellow-500'
            }[approval.status] ?? ''
        },
        toggleShowPendingApprovals() {
            this.showPendingApprovals = !this.showPendingApprovals
        },
        getApprovalSummaryComponent(approval) {
            return {
                apply_credit_to_company: markRaw(AllocateCreditToCompanySummary),
                update_invoice_status: '', // TODO - Create summary components for each action
                issue_invoice_to_collections: '',
                write_off_invoice: '',
                issue_invoice_refund: '',
                expire_company_credit: markRaw(ExpireCompanyCreditSummary),
                extend_company_credit: markRaw(ExtendCompanyCreditSummary),
            }[approval.requested_action_slug]
        },
        openApproval(approval) {
            const url = queryParamsHelper.mountUrlWithSearchParams({
                tab: 'Action Requests',
                id: approval.id
            }, '/billing-management')

            window.open(url, '_blank')
        },
    }
}
</script>
