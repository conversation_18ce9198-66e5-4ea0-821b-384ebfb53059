<template>
    <Badge :color="presentationData.color" :dark-mode="darkMode">
        <div class="flex gap-1 items-center">
            <simple-icon :icon="presentationData.icon" tooltip="Marketing strategy type"></simple-icon>
            <p>{{ presentationData.text }}</p>
        </div>
    </Badge>
</template>

<script>
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import Badge from "./Badge.vue";

export default {
    name: "MarketingStrategyBadge",
    components: {Badge, SimpleIcon},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        type: {
            type: String,
            required: false
        }
    },
    computed: {
        simpleIconHelper(){
            return useSimpleIcon()
        },
        presentationData(){
            return {
                'organic': {
                    color: 'green',
                    icon: this.simpleIconHelper.icons.PLANT,
                    text: 'Organic'
                },
                'paid': {
                    color: 'blue',
                    icon: this.simpleIconHelper.icons.MONEY_DOLLAR,
                    text: 'Paid'
                },
                'affiliate': {
                    color: 'blue',
                    icon: this.simpleIconHelper.icons.MONEY_DOLLAR,
                    text: 'Paid'
                },
                'ping_post_affiliate': {
                    color: 'orange',
                    icon: this.simpleIconHelper.icons.PAPER_AIRPLANE,
                    text: 'Ping Post Affiliate'
                },
            }[this.type]
        },
    }
}
</script>
