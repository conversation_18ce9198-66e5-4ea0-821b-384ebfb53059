<template>
    <autocomplete
        :model-value="modelValue"
        :value="modelValue"
        :dark-mode="darkMode"
        :placeholder="placeholder"
        @search="searchUser"
        :options="options"
        @update:modelValue="handleModelUpdate"
    />
</template>
<script>
import Autocomplete from "../Autocomplete.vue";
import SharedApiService from "../../services/api.js";

export default {
    name: "UserSearchAutocomplete",
    components: {Autocomplete},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        modelValue: {
            type: [Number, String],
            default: 0
        },
        placeholder: {
            type: String,
            default: 'Enter User Name or ID'
        }
    },
    emits: ['update:modelValue'],
    data() {
        return {
            sharedApi: SharedApiService.make(),
            options: [],
        }
    },
    methods: {
        handleModelUpdate(value){
            this.$emit('update:modelValue', value)
        },
        searchUser(query) {
            this.sharedApi.searchUserByNamesAndId(query, {}).then(resp => this.options = resp.data.data.users);
        },
    }
}
</script>
