<template>
    <div>
        <alerts-container v-if="alertActive" :alert-type="alertType" :text="alertText" :dark-mode="darkMode" />
        <div v-if="disabled" ref="viewerContainer"></div>
        <div v-else ref="editorContainer" :class="[{'hide-tabs': hideTabs}]"></div>
    </div>
</template>

<script setup>
    //CSS
    import '@toast-ui/editor/dist/toastui-editor.css';
    import 'tui-color-picker/dist/tui-color-picker.css';
    import '@toast-ui/editor/dist/theme/toastui-editor-dark.css';
    import '@toast-ui/editor-plugin-color-syntax/dist/toastui-editor-plugin-color-syntax.css';
    import '@toast-ui/editor/dist/toastui-editor-viewer.css';

    //JS
    import {ref, defineProps, defineEmits, onMounted, computed, watch} from 'vue';
    import AlertsContainer from "./AlertsContainer.vue";
    import Editor from "@toast-ui/editor"
    import Viewer from "@toast-ui/editor/dist/toastui-editor-viewer";
    import colorSyntax from '@toast-ui/editor-plugin-color-syntax';
    import SharedApiService from "../services/api";
    import { extractMarkdownImageTags, __IMAGE_NAME_REGEX__ } from "../services/markdownImageTags";
    import {slugify} from "../services/strings.js";

    const props = defineProps({
        content: {
            type: String,
            default: ''
        },
        images: {
            type: Object,
            default: () => {}
        },
        darkMode: {
            type: Boolean,
            default: false
        },
        additionalPlugins: {
            type: Array,
            default: () => []
        },
        imageFileBasePath: {
            type: String,
            default: ''
        },
        basicEditor: {
            type: Boolean,
            default: false
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        hideTabs: {
            type: Boolean,
            default: false
        },
        mimeTypes: {
            type: Array,
            default: []
        }
    });

    const emit = defineEmits([
        'update:content',
        'update:images'
    ]);

    const uploadImageName = ref('');

    const alertActive = ref(false);
    const alertType = ref('');
    const alertText = ref('');

    const maxFileSizeMb = 25; //TODO: confirm max file size
    const api = SharedApiService.make();

    const editorContainer = ref();
    const viewerContainer = ref();

    const editorContent = computed({
        get() {
            return props.content;
        },
        set(value) {
            emit('update:content', value);
        }
    });
    const editorImages = computed({
        get() {
            return props.images;
        },
        set(value) {
            emit('update:images', value);
        }
    });

    let editor, viewer;
    onMounted(() => {
        initializeEditor();
    });

    watch(editorContent, (newContent) => {
        if(props.disabled){
            initializeEditor();
        }else {
            if(editor && newContent !== editor.getMarkdown()) {
                editor.setMarkdown(newContent);
            }
        }

    });
    watch(() => props.darkMode, () => {
        setDarkTheme();
    });
    watch(() => props.additionalPlugins, () => {
        initializeEditor();
    });

    const setDarkTheme = () => {
        const editorBody = !props.disabled ? editorContainer.value.querySelector('.toastui-editor-defaultUI') :
            viewerContainer.value.querySelector('.toastui-editor-contents');

        const darkClass = props.disabled ? 'bg-neutral-300' : 'toastui-editor-dark';

        if(props.darkMode) {
            editorBody?.classList.add(darkClass);
        }
        else {
            editorBody?.classList.remove(darkClass);
        }
    }

    const showAlert = (type, text) => {
        alertType.value = type;
        alertText.value = text;

        alertActive.value = true;

        setTimeout(() => {
            alertActive.value = false;
            alertType.value = '';
            alertText.value = '';
        }, 5000);
    }

    const initializeEditor = () => {
        if(editor)
            editor.destroy();
        if(viewer)
            viewer.destroy();
        if(!props.disabled)
            editor = new Editor({
                minHeight: "300px",
                height: "436px",
                el: editorContainer.value,
                initialEditType: 'markdown',
                previewStyle: 'tab',
                events: {
                    change: () => {
                        const newContent = editor.getMarkdown();

                        editorImages.value = extractMarkdownImageTags(newContent, editorImages.value);

                        editorContent.value = newContent;
                    }
                },
                hooks: {
                    addImageBlobHook: (blob, callback) => {
                        const MAX_SIZE = 512 * 1024; // 512 KB

                        if (blob.size > MAX_SIZE) {
                            showAlert('error', 'Attachment is too large, max attachment size is 512 KB');
                            return;
                        }

                        if (!validMimeTypes(blob.type)) {
                            return;
                        }

                        const finalFilename = slugifyFile(props.imageFileBasePath, blob.name)

                        if(__IMAGE_NAME_REGEX__.test(finalFilename)) {
                            const reader = new FileReader();

                            reader.addEventListener("load", () => {
                                editorImages.value[finalFilename] = reader.result;
                                callback(reader.result, finalFilename);
                            });

                            reader.readAsDataURL(blob);
                        }
                        else {
                            showAlert('error', 'Image name may only contain letters, numbers, periods, and underscores');
                        }
                    }
                },
                usageStatistics: false,
                toolbarItems: [
                    ['heading', 'bold', 'italic', 'strike'],
                    ['hr', 'quote'],
                    [],
                    ['ul', 'ol', 'task', 'indent', 'outdent'],
                    ['table', 'image', 'link'],
                    ['scrollSync']
                ],
                plugins: [colorSyntax].concat(props.additionalPlugins),
                initialValue: editorContent.value,
                theme: props.darkMode ? 'dark' : 'default',
                hideModeSwitch:true
            });
        else
            viewer = new Viewer({
                el: viewerContainer.value,
                viewer: true,
                minHeight: "300px",
                height: "436px",
                initialValue: editorContent.value
            })

        setDarkTheme();
    }

    const loadToolBarItems = () => {
        return !props.basicEditor
            ?
                [
                    ['heading', 'bold', 'italic', 'strike'],
                    ['hr', 'quote'],
                    [],
                    ['ul', 'ol', 'task', 'indent', 'outdent'],
                    ['table', 'image', 'link'],
                    ['scrollSync']
                ]
            : null
    }

    const loadPlugins = () => {
        return !props.basicEditor
            ? [colorSyntax].concat(props.additionalPlugins)
            : props.additionalPlugins;
    }

    const slugifyFile = (path, fileName) => {
        // Extract and slugify the filename
        const originalName = fileName || 'image'; // Fallback if no name is provided
        const nameWithoutExt = originalName.replace(/\.[^/.]+$/, ''); // Remove extension
        const extension = originalName.split('.').pop(); // Get file extension
        const slugifiedName = slugify(nameWithoutExt, '_');
        const randomNumber = Math.floor(1000 + Math.random() * 9000); // Random 4-digit number

        return path + `${slugifiedName}_${randomNumber}.${extension}`;
    }

    const validMimeTypes = (type) => {
        if (!props.mimeTypes.length) {
            return true;
        }

        const index = props.mimeTypes.findIndex(t => t.toLowerCase() === type.split('/')[1]?.toLowerCase());

        if (index === -1) {
            showAlert('error', `Invalid image type. Only ${props.mimeTypes.join(', ')} files are accepted`);
            return false;
        }

        return true;
    }

</script>
<style>
.hide-tabs .toastui-editor-tabs {
    display: none !important;
}
</style>
