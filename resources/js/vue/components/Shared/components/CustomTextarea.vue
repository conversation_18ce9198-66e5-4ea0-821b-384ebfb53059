<template>
     <textarea
         class="w-full border rounded pl-4 focus:outline-none outline-none focus:ring-0 focus:border-primary-500 focus:shadow-lg focus:shadow-primary-500/10 pr-4 py-3"
         :placeholder="placeholder"
         type="text"
         :value="modelValue"
         @input="$emit('update:modelValue', $event.target.value)"
         :class="{'border-light-border bg-light-background': !darkMode, 'border-dark-border bg-dark-background text-slate-50': darkMode}"
     />
</template>

<script>
export default {
    name: "CustomTextarea",
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        modelValue: {
            type: String,
            default: ''
        },
        placeholder: {
            type: String,
            default: null
        }
    }
}
</script>
