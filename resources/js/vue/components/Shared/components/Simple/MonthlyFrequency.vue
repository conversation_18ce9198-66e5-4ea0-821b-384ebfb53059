<template>
    <div class="flex gap-2 items-center">
        <span> on the </span>
        <dropdown
            :options="dayOptions"
            v-model="modelValue.month_day"
            :dark-mode="darkMode"
            base-class="w-fit"
            :disabled="disabled"
        />
        <span> day of every month</span>
        <simple-icon
            :dark-mode="darkMode"
            :icon="simpleIcon.icons.INFORMATION_CIRCLE"
            :color="simpleIcon.colors.BLUE"
            tooltip="Invoice will only contain uninvoiced leads for the last completed month"
        />
    </div>
</template>
<script>
import Dropdown from "../Dropdown.vue";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../composables/useSimpleIcon.js";
import useNumberHelper from "../../../../../composables/useNumberHelper.js";
const simpleIcon = useSimpleIcon()

const numberHelper = useNumberHelper();
export default {
    name: "MonthlyFrequency",
    components: {SimpleIcon, Dropdown},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        modelValue: {
            type: Object,
            default: {
                month_day: 1,
            }
        },
        disabled: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            simpleIcon,
            dayOptions: Array.from({length: 31}, (v,i) => ({id: i+1, name: numberHelper.getOrdinalSuffix(i+1)})),
        }
    }
}
</script>
