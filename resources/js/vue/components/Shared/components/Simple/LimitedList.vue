<template>
    <div class="flex flex-1" ref="container">
        <div class="flex flex-wrap items-center gap-1" v-if="listItems.length > 0">
            <div v-for="item in visibleItems">
                <slot name="list-item" :item="item">
                    <badge :dark-mode="darkMode">{{ item.name }}</badge>
                </slot>
            </div>
            <div v-if="visibleItems.length < listItems.length">
                <slot name="list-remaining">
                    <hover-tooltip tool-tip-style="light" right large :dark-mode="darkMode">
                        <template #title>
                            <badge
                                @click="handleShowAll"
                                :dark-mode="darkMode"
                                :class="expandable ? 'cursor-pointer' : ''"
                            >
                                +{{hiddenCount}}
                            </badge>
                        </template>
                        <template #default>
                            <div class="flex flex-wrap gap-1">
                                <div v-for="item in listItems">
                                    <slot name="hidden-item" v-bind="{item}">
                                        <badge :dark-mode="darkMode">{{ item.name }}</badge>
                                    </slot>
                                </div>
                            </div>
                        </template>
                    </hover-tooltip>
                </slot>
            </div>
            <badge
                v-if="showHideItems && expandable"
                @click="handleShowAll"
                :dark-mode="darkMode"
                class="cursor-pointer"
            >
                -{{visibleItems.length - showCount}}
            </badge>
        </div>
        <div v-else>
            {{emptyMessage}}
        </div>
    </div>
</template>
<script>
import Badge from "../Badge.vue";
import HoverTooltip from "../HoverTooltip.vue";

export default {
    name: "LimitedList",
    components: {HoverTooltip, Badge},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        listItems: {
            type: Array,
            required: true,
        },
        showCount: {
            type: Number,
            default: 3
        },
        emptyMessage: {
            type: String,
            default: 'No Items'
        },
        dynamicShow: {
            type: Boolean,
            default: false,
        },
        expandable: {
            type: Boolean,
            default: true,
        }
    },
    data() {
        return {
            visibleItems: [],
            hiddenCount: 0,
            showAll: false,
        }
    },
    mounted() {
        if (this.dynamicShow && this.$refs.container?.clientWidth) {
            this.calculateVisibleItems();
            window.addEventListener("resize", this.calculateVisibleItems);
        } else {
            this.hideItems(this.showCount)
        }
    },
    computed: {
        showHideItems(){
            return this.visibleItems.length > this.showCount && this.visibleItems.length === this.listItems.length
        }
    },
    beforeUnmount() {
        if (this.dynamicShow) {
            window.removeEventListener("resize", this.calculateVisibleItems);
        }
    },
    methods: {
        hideItems(count) {
            this.visibleItems = this.listItems.slice(0,count);
            this.hiddenCount = this.listItems.length - count
        },
        handleShowAll(){
            if (!this.expandable) return;

            this.showAll = !this.showAll
            this.hideItems(!this.showAll ? this.showCount : this.listItems.length)
        },
        calculateVisibleItems() {
            this.$nextTick(() => {
                const containerWidth = this.$refs.container.clientWidth;
                let totalWidth = 0;
                const visibleItems = [];
                for (const item of this.listItems) {
                    const itemWidth = this.getTextWidth(item.name);
                    if (totalWidth + itemWidth <= containerWidth) {
                        visibleItems.push(item);
                        totalWidth += itemWidth;
                    } else {
                        break;
                    }
                }
                this.visibleItems = visibleItems;
                this.hiddenCount = this.listItems.length - visibleItems.length;
            });
        },
        getTextWidth(text) {
            const span = document.createElement("span");
            span.style.position = "absolute";
            span.style.fontSize = "xs"
            span.style.visibility = "hidden";
            span.style.whiteSpace = "nowrap";
            span.className = "inline-block px-2 py-1";
            span.innerText = text;
            document.body.appendChild(span);
            const width = span.offsetWidth;
            document.body.removeChild(span);
            return width;
        },
    }
}
</script>
