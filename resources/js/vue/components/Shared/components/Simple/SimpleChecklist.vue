<template>
    <div :class="classStyle">
        <div v-for="item in modelValue" class="flex gap-1">
            <custom-checkbox
                :dark-mode="darkMode"
                :model-value="item.associated"
                @update:modelValue="e => item.associated = e"
                :input-disabled="disabled"
            />
            <div>
                {{ item.title }}
            </div>
        </div>
    </div>
</template>
<script>
import CustomCheckbox from "../../SlideWizard/components/CustomCheckbox.vue";

export default {
    name: "SimpleChecklist",
    components: {CustomCheckbox},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        modelValue: {
            type: Array,
            required: true
        },
        style: {
            type: String,
            default: 'default'
        },
        disabled: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        classStyle() {
            const styles = {
                default: 'flex flex-col gap-2'
            }

            return styles[this.style] ?? this.style
        }
    },
    methods: {
    }
}
</script>
