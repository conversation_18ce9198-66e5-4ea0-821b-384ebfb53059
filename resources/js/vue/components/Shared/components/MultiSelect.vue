<template>
    <div class="relative">
        <select class="w-full p-2 rounded hidden d-none" v-model="selectedOptionIds" multiple="multiple">
            <option v-for="option in options" :value="option.id">
                {{option.name}}
            </option>
        </select>

        <div>
            <button type="button"
                    :class="[
                             showMultiselect ? (darkMode ? 'bg-dark-module border-primary-500 shadow-lg shadow-primary-500/10' : 'bg-light-module border-primary-500 shadow-lg shadow-primary-500/10') : (darkMode ? 'border-dark-border' : 'border-light-border'),
                             darkMode ? ('hover:bg-dark-module text-slate-200 bg-dark-background') : 'hover:bg-light-module text-slate-700 bg-light-background',
                             disabled ? (darkMode ? 'bg-dark-module' : 'bg-light-module') + ' opacity-75 cursor-not-allowed pointer-events-none' : ''
                         ]"
                    class="z-30 grid w-full truncate cursor-pointer capitalize text-sm font-semibold transition duration-100 border justify-between items-center rounded py-2 px-3 h-9"
            aria-haspopup="true"
                    aria-expanded="false"
                    @click="toggleMultiselect">
                <span class="truncate mr-5">{{ selectedOptionByName.join(', ') || textPlaceHolder }}</span>
                <svg class="fill-current flex-shrink-0 absolute right-4 text-primary-500" width="12" height="7" viewBox="0 0 12 7" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd"
                          clip-rule="evenodd"
                          d="M11.7071 0.292893C12.0976 0.683418 12.0976 1.31658 11.7071 1.70711L6.70711 6.70711C6.31658 7.09763 5.68342 7.09763 5.29289 6.70711L0.292893 1.70711C-0.0976315 1.31658 -0.0976315 0.683417 0.292893 0.292893C0.683417 -0.0976316 1.31658 -0.0976316 1.70711 0.292893L6 4.58579L10.2929 0.292893C10.6834 -0.0976312 11.3166 -0.0976311 11.7071 0.292893Z" />
                </svg>
            </button>

            <div v-if="showMultiselect"
                 class="absolute left-0 max-h-56 z-50 overflow-y-auto mt-10 rounded w-full border shadow-module"
                 :class="[darkMode ? 'border-dark-border bg-dark-background' : 'border-light-border bg-light-module', placement === 'bottom' ? 'top-0' : 'bottom-10']">
                <div v-if="showSearchBox" class="p-1 w-full">
                    <input type="text" class="rounded text-sm font-medium w-full h-9 border focus:outline-none outline-none focus:ring-0"
                           :class="[darkMode ? 'text-slate-100 bg-dark-background border-dark-border' : 'text-slate-900 bg-light-background border-light-border']"
                           v-model.trim="textFilter">
                </div>
                <div
                    v-for="option in optionList"
                    :class="[!option.show ? 'hidden d-none' :  '', !darkMode ? 'hover:bg-grey-120' : 'hover:bg-dark-175 text-white', selectedIds.includes(option.id) ? 'text-primary-500' : '']"
                    class="py-3 px-4 capitalize text-sm font-semibold transition duration-200 cursor-pointer flex items-center gap-1"
                    @click.prevent.stop="handleSelect(option)">
                    <slot name="option" v-bind="{option}">
                        <span>{{option.name}}</span>
                    </slot>
                    <span v-if="selectedIds.includes(option.id)" class="flex flex-1 justify-end">
                        <svg width="12" height="10" viewBox="0 0 12 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M4.27411 6.5197L1.62626 3.69335L0 5.43719L4.27642 10L12 1.74138L10.3714 0L4.27411 6.5197Z" fill="#0081FF"/>
                        </svg>
                    </span>
                </div>
            </div>
        </div>

        <div v-if="showMultiselect" @click="toggleMultiselect" class="fixed inset-0 z-10"></div>
    </div>
</template>

<script>
export default {
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        options: {
            type: Object,
            required: true
        },
        selectedIds: {
            type: Array,
            default: function() {
                return [];
            }
        },
        textPlaceHolder: {
            type: String,
            default: 'Select Options'
        },
        showSearchBox: {
            type: Boolean,
            default: true
        },
        classes: {
            type: String,
            default: ''
        },
        placement: {
            type: String,
            default: 'bottom'
        },
        disabled: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            optionList: [],
            textFilter: '',
            showMultiselect: false
        };
    },
    methods: {
        prepareOptionList(refreshList = false) {
            if ((this.options && this.options.length) || refreshList === true) {
                this.optionList = this.options;
                this.optionList.forEach((opt) => {
                    opt.selected = this.selectedOptionIds.indexOf(opt.id) >= 0;
                    opt['show']  = true;
                })
            }
        },
        handleSelect(option) {
            const optPos = this.selectedOptionIds.indexOf(option.id);

            if (optPos >= 0) {
                this.selectedOptionIds.splice(optPos, 1);
                option.selected = false;
            }
            else {
                option.selected = true;
                this.selectedOptionIds.push(option.id);
            }

            this.$emit('input', this.selectedOptionIds);
        },
        toggleMultiselect() {
            this.showMultiselect = !this.showMultiselect;

            if(!this.showMultiselect) {
                this.$emit('selection-completed', this.selectedOptionIds);
            }
        },
        checkMarkerStatus() {
            if(this.selectedIds.length > 0) {
                return;
            }

            if(this.showMultiselect) {
                this.showMultiselect = false;
            }

            this.resetMarker();
        },
        resetMarker() {
            this.optionList.forEach((opt) => {
                opt.selected = false;
            });
        }
    },
    created: function() {
        this.prepareOptionList();
    },
    computed: {
        selectedOptionByName() {
            const selectedOptionsByName = this.optionList.filter((opt) => {
                return this.selectedOptionIds.indexOf(opt.id) >= 0;
            });
            return selectedOptionsByName.map((opt) => opt.name);
        },
        selectedOptionIds: {
            get() {
                return this.selectedIds;
            },
            set(value) {
                this.$emit('input', value);
            }
        },
        hasSelection() {
            return this.selectedIds.length && this.selectedIds.length > 0;
        }
    },
    watch: {
        textFilter(value) {
            if (value && value.length) {
                this.optionList.forEach((opt) => {
                    opt['show'] = opt.name.toLowerCase().replace(/ /g,'').includes(value.toLowerCase());
                });
            } else {
                this.optionList.forEach((opt) => {
                    opt['show'] = true;
                });
            }
        },
        selectedIds() {
            this.checkMarkerStatus();
        },
        options(newValue, oldValue) {
            if (newValue !== oldValue) {
                this.prepareOptionList(true);
            }
        }
    }
}
</script>

<style>

</style>
