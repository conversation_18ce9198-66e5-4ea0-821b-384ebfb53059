<template>
    <div class="inline-flex items-center">
        <span v-if="label" class="not-sr-only mr-2" :class="[labelClass, darkMode ? 'text-white' : 'text-slate-900']">{{ label }}</span>
        <Switch v-model="enabled"
                style="width: 2.75rem;"
                class="my-auto h-6 w-11 relative inline-flex items-center flex-shrink-0 border-1 rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none"
                :class="[
                    disabled ? 'opacity-50 cursor-not-allowed pointer-events-none' : '',
                    !darkMode ? (enabled ? 'bg-primary-500 border border-primary-400' : 'bg-light-background border border-light-border') : (enabled ? 'bg-primary-500 border border-primary-400' : 'bg-dark-background border border-dark-border'),
                ]">
            <span aria-hidden="true"
                  :class="[
                      !darkMode ? (enabled ? 'translate-x-5 bg-light-module' : 'translate-x-[2px] bg-slate-400') : (enabled ? 'translate-x-5 bg-light-module' : 'translate-x-[2px] bg-slate-400'),

                    'pointer-events-none inline-block h-5 w-5 rounded-full bg-light-module shadow transform ring-0 transition ease-in-out duration-200',
                  ]" />
        </Switch>
    </div>
</template>

<script>
import { Switch } from '@headlessui/vue'

export default {
    components: {
        Switch
    },
    props: {
        darkMode: {
          type: Boolean,
          default: false,
        },
        label: {
            type: String,
            default: ''
        },
        modelValue: {
            type: [Boolean, Number],
            default: false
        },
        labelClass: {
            type: String,
            default: ''
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        value: {
            type: Boolean,
            default: null
        }
    },
    emits: [
        'update:modelValue',
        'change'
    ],
    computed: {
        enabled: {
            get() {
                if (this.value) return this.value

                return Boolean(this.modelValue);
            },
            set(value) {
                this.$emit('update:modelValue', value);
                this.$emit('change', value);
            }
        }
    }
}

</script>
