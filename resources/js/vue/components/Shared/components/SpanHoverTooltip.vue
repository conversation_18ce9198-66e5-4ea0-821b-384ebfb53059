<template>
    <span
        :id="id"
        @click="handleClick"
        @mouseleave="toggleActiveTipFalse"
        class="inline relative cursor-pointer"
        :class="{'active-tip': isActiveTip}">
        <span @mouseover="toggleActiveTipTrue">
            <slot name="title">

            </slot>
        </span>
        <span class="relative" v-if="!hideTooltip">
            <span v-show="isActiveTip"
                 class="shadow-xl absolute transition-all duration-150 bg-light-module p-2 rounded border-grey-200 z-20 mt-2"
                 :class="{
                    'right-0': right || mobileRight,
                    'left-0': mobileLeft,
                    'w-64': large,
                    'w-96': extraLarge,
                    'text-2xl': fontLarger,
                    'bg-primary-700 text-slate-200': darkMode,
                    'bg-primary-50 text-slate-900': !darkMode,
                }"
            >
                <slot></slot>
            </span>
        </span>
    </span>
</template>

<script>
export default {
    name: 'SpanHoverTooltip',
    emits: ['click'],
    props: {
        right: {
            type: Boolean,
            default: false
        },
        mobileLeft: {
            type: Boolean,
            default: false
        },
        mobileRight: {
            type: Boolean,
            default: false
        },
        large: {
            type: Boolean,
            default: false
        },
        extraLarge: {
            type: Boolean,
            default: false
        },
        fontLarger: {
            type: Boolean,
            default: false
        },
        hideTooltip: {
            type: Boolean,
            default: false
        },
        id: {
            type: String,
            required: false,
        },
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    data () {
        return {
            isActiveTip: false,
        }
    },
    methods: {
        toggleActiveTipTrue () {
            this.isActiveTip = true
        },
        toggleActiveTipFalse () {
            this.isActiveTip = false
        },
        handleClick () {
            this.$emit('click')
        }
    }
}
</script>

<style>

</style>
