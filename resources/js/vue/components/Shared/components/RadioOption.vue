<template>
    <div class="pb-3">
        <div class="flex items-center">
            <input :class="[darkMode ? 'bg-dark-module' : 'bg-light-module']" :value="value" :checked="chosen === value"
                   class="mr-1 checked:bg-primary-500 focus:bg-primary-500 focus:ring-0 focus:outline-none"
                   type="radio" @input="updateChosen" :id="`radio-${id}`">
            {{ title }}
        </div>
    </div>
</template>
<script setup>
import { computed } from 'vue'

const props = defineProps({
    darkMode: Boolean,
    chosen: String | Number,
    value: String | Number,
    valueIsNumber: {
        type: Boolean,
        default: false,
    },
    id: String,
    title: String,
    placeholder: String,
})

const emit = defineEmits(['update:chosen'])

const selected = computed(() => {
    return props.chosen === props.value
})

const updateChosen = (event) => {
    const isSelected = event.target.checked

    let value = event.target.value

    if (props.valueIsNumber) {
        value = parseInt(value)
    }

    if (isSelected) {
        emit('update:chosen', value)
    }
}
</script>

<script>
export default {
    name: 'RadioOption'
}
</script>
