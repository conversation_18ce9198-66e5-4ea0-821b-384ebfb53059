<template>
    <div class="cursor-pointer w-min">
        <select
            :name="getKebabName(name ?? '')"
            class="cursor-pointer bg-transparent text-center p-0 mx-0 bg-none appearance-none text-primary-500 hover:text-primary-400 outline-none border-none focus:outline-none focus:ring-0 focus:border-none w-max"
            :class="[classes]"
            :disabled="disabled || options.length < 2"
            :style="`width: ${width + extraWidth}ch; `"
            :value="modelValue ?? '-'"
            @change="handleChange"
        >
            <option v-if="options.length < 1" value="">-</option>
            <option
                v-for="option in options"
                :key="option.value"
                :value="option.value"
            >
                {{ option.name }}
            </option>
        </select>
    </div>
</template>

<script>
export default {
    name: "CustomInlineSelect",
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        options: {
            type: Array,
            default: [{ value: '', name: '-' }],
        },
        modelValue: {
            type: [String, Number],
            default: null,
        },
        name: {
            type: String,
            default: null,
        },
        classes: {
            type: String,
            default: '',
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        extraWidth: {
            type: Number,
            default: 0,
        },
    },
    emits: ['update:modelValue'],
    data() {
        return {
            width: 2,
        }
    },
    mounted() {
        this.updateWidth();
    },
    methods: {
        handleChange({ target }) {
            this.updateWidth(target.value);
            this.$emit('update:modelValue', target.value);
        },
        getKebabName(inputString) {
            const newString = typeof inputString === 'string'
                ? inputString.trim()
                    .replace(/[\s_-]+/g, '-')
                    .replace(/[^0-z-]/g, '')
                : '';

            return newString.length
                ? newString
                : `new-select-${Math.random()}`;
        },
        updateWidth(selectedValue) {
            const option = this.options.find(option => option.value === (selectedValue || this.modelValue));

            if (option) {
                this.width = option.name.split(' ').reduce((prev, curr) => prev + curr.length,0)
            }
        }
    },
    watch: {
        modelValue(newVal){
            this.updateWidth(newVal)
        }
    }
}

</script>
