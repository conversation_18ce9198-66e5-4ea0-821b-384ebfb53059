<template>
    <div class="inline-flex items-center gap-1 px-2 py-1 rounded-md cursor-pointer transition duration-200" :class="[darkMode ? 'hover:bg-dark-border' : 'hover:bg-light-background']">
        <p class="text-sm font-medium">
            {{period}}
        </p>
        <svg class="fill-current w-4" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12.2368 15C13.7605 15 15 13.8225 15 12.375C15 11.19 14.1632 10.1977 13.0263 9.87222L13.0263 4.50222C13.0271 4.36797 13.005 3.16647 12.0995 2.30397C11.5405 1.76997 10.7921 1.49997 9.87631 1.49997L9.87631 -3.10233e-05L6.71842 2.24997L9.87632 4.49997L9.87632 2.99997C11.3155 2.99997 11.4426 4.15047 11.4474 4.49997L11.4474 9.87222C10.3105 10.1977 9.47368 11.19 9.47368 12.375C9.47368 13.8225 10.7139 15 12.2368 15ZM12.2368 11.25C12.8897 11.25 13.4211 11.7547 13.4211 12.375C13.4211 12.9952 12.8897 13.5 12.2368 13.5C11.5839 13.5 11.0526 12.9952 11.0526 12.375C11.0526 11.7547 11.5839 11.25 12.2368 11.25ZM1.97368 5.12772L1.97368 10.5022C1.97763 11.7067 2.82395 13.5 5.13947 13.5L5.13947 15L8.29737 12.75L5.13947 10.5L5.13947 12C3.69553 12 3.55974 10.8457 3.55263 10.5L3.55263 5.12772C4.68947 4.80222 5.52631 3.80997 5.52631 2.62497C5.52631 1.17747 4.28684 -3.05347e-05 2.76316 -3.04015e-05C1.23947 -3.02683e-05 -2.54727e-07 1.17747 -1.28183e-07 2.62497C-2.45866e-08 3.80997 0.836841 4.80222 1.97368 5.12772ZM2.76316 1.49997C3.41605 1.49997 3.94737 2.00472 3.94737 2.62497C3.94737 3.24522 3.41605 3.74997 2.76316 3.74997C2.11026 3.74997 1.57895 3.24522 1.57895 2.62497C1.57895 2.00472 2.11026 1.49997 2.76316 1.49997Z"/></svg>
    </div>
</template>

<script>
export default {
    name: "CompareButton",
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        period: {
            type: String,
            default: ''
        }
    }
}
</script>

<style scoped>

</style>
