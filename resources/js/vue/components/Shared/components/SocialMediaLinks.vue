<template>
    <div v-for="link in links">
        <a class="hover:text-primary-500" :class="[darkMode ? 'text-slate-200' : 'text-slate-600']" :href="link.url" target="_blank">
            <div v-html="link.svg"></div>
        </a>
    </div>
</template>

<script setup>
const props = defineProps({
    links: {
        type: Array,
        validator: (value) => {
            return value.every((link) => {
                return link.svg && link.url;
            });
        },
    },
    darkMode: {
        type: Boolean,
        required: true,
    },
});
</script>
