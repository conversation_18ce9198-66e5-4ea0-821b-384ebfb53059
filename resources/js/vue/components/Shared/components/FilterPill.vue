<template>
    <HoverTooltip :id="id" :dark-mode="darkMode" :hide-tooltip="!isTruncated || dontShowTooltipOnTruncatedText" @click="handleClick">
        <template v-slot:title>
            <div class="px-3 py-1 rounded-md inline-flex items-center justify-between max-w-sm cursor-pointer"
                 :class="[darkMode ? 'bg-primary-700 bg-opacity-50 text-slate-200' : 'bg-primary-50 text-slate-900']">
                <p ref="title" class="text-sm font-semibold" :class="{'truncate' : !dontTruncate}">
                    <slot></slot>
                </p>
                <svg v-if="removable" class="ml-3 mr-1 fill-current flex-shrink-0" width="8" height="8" viewBox="0 0 8 8" fill="none"
                     xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M0.194995 0.203526C0.319888 0.0785449 0.489256 0.00833454 0.665854 0.00833454C0.842453 0.00833454 1.01182 0.0785449 1.13671 0.203526L3.99584 3.06553L6.85496 0.203526C6.9164 0.139852 6.98988 0.0890639 7.07114 0.0541246C7.15239 0.0191852 7.23979 0.000794382 7.32822 2.51714e-05C7.41665 -0.00074404 7.50434 0.0161236 7.58619 0.0496442C7.66804 0.0831648 7.7424 0.132667 7.80493 0.195262C7.86747 0.257857 7.91692 0.332292 7.9504 0.414223C7.98389 0.496154 8.00074 0.58394 7.99997 0.67246C7.99921 0.760979 7.98083 0.848459 7.94593 0.929795C7.91103 1.01113 7.86029 1.08469 7.79668 1.14619L4.93756 4.00819L7.79668 6.87019C7.918 6.99593 7.98512 7.16433 7.98361 7.33913C7.98209 7.51392 7.91205 7.68113 7.78857 7.80474C7.66508 7.92834 7.49804 7.99846 7.32342 7.99997C7.1488 8.00149 6.98057 7.9343 6.85496 7.81286L3.99584 4.95086L1.13671 7.81286C1.01111 7.9343 0.842873 8.00149 0.668251 7.99997C0.493629 7.99846 0.326589 7.92834 0.203108 7.80474C0.0796264 7.68113 0.00958406 7.51392 0.00806664 7.33913C0.00654922 7.16433 0.0736782 6.99593 0.194995 6.87019L3.05412 4.00819L0.194995 1.14619C0.0701398 1.02117 0 0.851635 0 0.674859C0 0.498083 0.0701398 0.328544 0.194995 0.203526Z"/>
                </svg>
            </div>
        </template>

        <template v-slot:default>
            <div class="px-3 py-1 rounded-md inline-flex items-center justify-between max-w-sm cursor-pointer"
                 :class="[darkMode ? 'bg-primary-700 bg-opacity-50 text-slate-200' : 'bg-primary-50 text-slate-900']">
                <p class="text-sm font-semibold">
                    <slot></slot>
                </p>
            </div>
        </template>
    </HoverTooltip>
</template>

<script>
import HoverTooltip from './HoverTooltip.vue'

export default {
    name: 'FilterPill',
    emits: ['click'],
    components: { HoverTooltip },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        dontTruncate: {
            type: Boolean,
            default: false
        },
        dontShowTooltipOnTruncatedText: {
            type: Boolean,
            default: false
        },
        id: {
            type: String,
            required: false
        },
        removable: {
            type: Boolean,
            default: true,
        }
    },
    methods: {
        handleClick () {
            this.$emit('click')
        },
        updateScrollAndClientWidths () {
            this.titleScrollWidth = this.$refs.title.scrollWidth
            this.titleClientWidth = this.$refs.title.clientWidth
        }
    },
    mounted () {
        this.updateScrollAndClientWidths()

        const config = {
            attributes: true,
            childList: true,
            subtree: true
        }
        // this will be triggered for any change in your element
        this.observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation) {
                    this.updateScrollAndClientWidths()
                }
            })
        })

        //observe the referenced element
        this.observer.observe(this.$refs.title, config)
    },
    data () {
        return {
            titleScrollWidth: 0,
            titleClientWidth: 0
        }
    }
    ,
    computed: {
        isTruncated () {
            return this.titleScrollWidth > this.titleClientWidth
        }
    }
}
</script>
