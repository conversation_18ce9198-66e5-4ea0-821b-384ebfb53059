<template>
    <div>
        <loading-spinner v-if="loading"/>
        <timeline-list
            v-else
            :list="activityLogs"
            :dark-mode="darkMode"
            :api="api"
        />
    </div>
</template>

<script>
import Modal from "../Modal.vue";
import LoadingSpinner from "../LoadingSpinner.vue";
import ApiService from "../../services/api.js";
import TimelineList from "./TimelineList.vue";

export default {
    name: 'ActivityLog',
    components: {TimelineList, LoadingSpinner, Modal},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        subjectType: {
            type: String,
            default: null,
        },
        subjectId: {
            type: Number,
            default: null,
        },
        events: {
            type: Array,
            default: ['created', 'updated', 'deleted']
        }
    },
    data() {
        return {
            loading: false,
            api: ApiService.make(),
            activityLogs: [],
        }
    },
    created() {
        this.getActivityLogs()
    },
    methods: {
        getActivityLogs() {
            this.loading = true;
            this.api.getActivityLogs(
                this.subjectType,
                this.subjectId,
                this.events
            ).then(res => {
                this.activityLogs = res.data.data.activity_logs.filter(log => {
                    return this.events.findIndex(event => log.event === event) !== -1;
                });
            }).catch(err => {
                console.log(err)
            }).finally(() => {
                this.loading = false;
            })
        }
    }
}
</script>
