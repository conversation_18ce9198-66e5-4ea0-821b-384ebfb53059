<template>
    <div :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <div v-if="list.length === 0">No logs found</div>
        <div v-else v-for="log in filteredLogs" :key="log.id">
            <li v-if="log.active"  class="relative flex gap-x-4">
                <div class="absolute -bottom-6 left-0 top-0 flex w-6 justify-center">
                    <div class="w-px bg-gray-200"></div>
                </div>
                <div class="relative flex h-6 w-6 flex-none items-center justify-center" :class="{'bg-white' : !darkMode, '': darkMode}">
                    <div class="h-1.5 w-1.5 rounded-full bg-gray-100 ring-1 ring-gray-300"></div>
                </div>
                <div class="flex-auto rounded-md p-3 ring-1 ring-inset ring-gray-200">
                    <div class="flex justify-between gap-x-4 cursor-pointer"
                         @click="log.active = false"
                    >
                        <div class="py-0.5 text-xs leading-5 text-gray-500">
                            <span class="font-medium" :class="[darkMode ? 'text-grey-300' : 'text-grey-600']">{{ getUserName(log) }}</span>
                            {{ log.event ?? '' }} {{ log.log_name ?? '' }}
                        </div>
                        <time datetime="2023-01-23T15:56" class="flex-none py-0.5 text-xs leading-5 text-gray-500">
                            {{ log.display_date ?? '' }}
                        </time>
                    </div>
                    <div v-if="log.batch_uuid"
                        class="text-sm leading-6 text-gray-500"
                    >
                        <div>
                            <div>
                                <span class="font-semibold">Batched logs: </span> {{ log.batch_count }}
                            </div>
                            <div v-if="!batchDetails[log.batch_uuid]"
                                :class="[loadingDetails ? 'pointer-events-none text-slate-500' : 'text-primary-500 cursor-pointer hover:text-primary-300']"
                                @click.prevent="loadBatchDetail(log)"
                            >
                                Load details
                            </div>
                        </div>
                        <div v-if="loadingDetails === log.batch_uuid">
                            <LoadingSpinner :dark-mode="darkMode" />
                        </div>
                        <div v-else-if="batchDetails[log.batch_uuid]"
                            class="ml-2"
                        >
                            <div v-for="batchedLog in batchDetails[log.batch_uuid]">
                                <div>
                                    <span class="font-semibold">{{ batchedLog.event }}:</span> {{ batchedLog.description }}
                                </div>
                                <div class="ml-2">
                                    <div v-if="batchedLog.event === 'created'">
                                        <div v-for="(value, key) in batchedLog.changes.attributes ?? []">
                                            {{ key }} created with value {{ value }}
                                        </div>
                                    </div>
                                    <div v-if="batchedLog.event === 'deleted'">
                                        <div v-for="(value, key) in batchedLog.changes.old ?? []">
                                            {{ key }} with value {{ value }} deleted
                                        </div>
                                    </div>
                                    <div v-else>
                                        <div v-for="(value, key) in batchedLog.changes.old ?? []">
                                            {{ key }} from {{ value }} to {{ batchedLog.changes.attributes?.[key] ?? '-' }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else>
                        <div v-if="log.event === 'updated' && log.changes.old && log.changes.attributes">
                            <p v-for="(value, key) in log.changes.attributes" :key="key" class="text-sm leading-6 text-gray-500">
                                {{ key }} from {{ log.changes.old[key] }} to {{ value }}
                            </p>
                        </div>
                        <div v-else-if="log.event === 'created' && log.changes.attributes">
                            <p v-for="(value, key) in log.changes.attributes" :key="key" class="text-sm leading-6 text-gray-500">
                                {{ key }} set to {{ value }}
                            </p>
                        </div>
                        <div v-else>
                            <p v-for="(value, key) in log.changes.attributes" :key="key" class="text-sm leading-6 text-gray-500">
                                {{ key }}: {{ value }}
                            </p>
                        </div>
                    </div>
                </div>
            </li>
            <li v-else @click="log.active = true" class="relative flex gap-x-4 cursor-pointer">
                <div class="absolute -bottom-6 left-0 top-0 flex w-6 justify-center">
                    <div class="w-px bg-gray-200"></div>
                </div>
                <div class="relative flex h-6 w-6 flex-none items-center justify-center" :class="{'bg-white' : !darkMode, '': darkMode}">
                    <div class="h-1.5 w-1.5 rounded-full bg-gray-100 ring-1 ring-gray-300"></div>
                </div>
                <p class="flex-auto py-0.5 text-xs leading-5 text-gray-500">
                    <span class="font-medium" :class="[darkMode ? 'text-grey-300' : 'text-grey-600']">{{ log.causer?.name ?? 'System' }}</span>
                    {{ log.event ?? '' }} {{ log.log_name ?? '' }} - {{ log.subject?.name ?? '' }}.
                </p>
                <time datetime="2023-01-23T11:24" class="flex-none py-0.5 text-xs leading-5 text-gray-500">
                    {{ log.display_date ?? '' }}
                </time>
            </li>
        </div>
    </div>
</template>

<script>
import LoadingSpinner from "../LoadingSpinner.vue";

export default {
    name: 'TimelineList',
    components: { LoadingSpinner },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        list: {
            type: Array,
            default: []
        },
        api: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            loadingDetails: false,
            batchDetails: {},
        }
    },
    computed: {
        filteredLogs() {
            // Apply filtering logic to the logs list here
            return this.list.filter(log => {
                return true;
            });
        }
    },
    methods: {
        getUserName(log) {
            if (!log.causer) {
                return 'System';
            }

            if (log.causer.name) {
                return log.causer.name;
            }

            if (log.causer.first_name || log.causer.last_name) {
                return `${log.causer.first_name} ${log.causer.last_name}`;
            }

            return 'Unknown';
        },
        async loadBatchDetail(log) {
            this.loadingDetails = log.batch_uuid;
            const resp = await this.api.getBatchedActivityLogDetails(log.batch_uuid).catch(e => e);
            if (resp.data.data.activity_logs) {
                this.batchDetails[log.batch_uuid] = resp.data.data.activity_logs;
            }
            else {
                console.error(resp.response?.message ?? "An error occurred");
            }

            this.loadingDetails = null;
        }
    }
}
</script>
