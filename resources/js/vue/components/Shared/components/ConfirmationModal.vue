<template>
    <Modal
        small
        :dark-mode="darkMode"
        hide-confirm
        hide-cancel
        no-close-button
        no-min-height
    >
        <template #header>
            <slot name="header">
                <h2 class="text-xl font-medium" :class="titleClass">{{ title }}</h2>
            </slot>
        </template>
        <template #content>
            <simple-alert
                v-if="errorHandler.message"
                class="mt-0"
                :content="errorHandler.message"
                :dark-mode="darkMode"
                variant="light-red"
            >
            </simple-alert>
            <slot name="content">
                <p class="text-lg">{{ text }}</p>
            </slot>
        </template>
        <template #buttons>
            <slot name="buttons">
                <CustomButton
                    @click="handleChoice(false)"
                    class="order-last"
                    height="h-10"
                    :color="cancelButtonColor"
                    :disable="disabled"
                >
                    {{ cancelButtonText }}
                </CustomButton>
                <CustomButton
                    @click="handleChoice(true)"
                    class="order-last"
                    height="h-10"
                    :color="confirmButtonColor"
                    :disabled="disabled"
                >
                    {{ confirmButtonText }}
                </CustomButton>
            </slot>
        </template>
    </Modal>
</template>

<script>
import Modal from "./Modal.vue";
import CustomButton from "./CustomButton.vue";
import useErrorHandler from "../../../../composables/useErrorHandler.js";
import SimpleAlert from "./SimpleAlert.vue";

export default {
    name: "ConfirmModal",
    components: {SimpleAlert, CustomButton, Modal},
    props: {
        title: {
            type: String,
            default: "Confirm Action"
        },
        text: {
            type: String,
            default: "Are you sure you want to proceed?"
        },
        titleClass: {
            type: String,
            default: "text-red-400"
        },
        cancelButtonText: {
            type: String,
            default: "Cancel"
        },
        confirmButtonText: {
            type: String,
            default: "Confirm"
        },
        cancelButtonColor: {
            type: String,
            default: "slate-light"
        },
        confirmButtonColor: {
            type: String,
            default: "primary"
        },
        darkMode: {
            type: Boolean,
            default: false
        },
        callback: {
            type: Function,
            default: () => {
            }
        }
    },
    data(){
        return {
            errorHandler: useErrorHandler(),
            disabled: false,
        }
    },
    methods: {
        async handleChoice(choice) {
            this.disabled = true
            try {
                this.errorHandler.resetError()
                this.$emit('choice', choice)
                await this.callback(choice)
            } catch (err) {
                this.errorHandler.handleError(err)
            }

            this.disabled = false
        }
    }
};
</script>
