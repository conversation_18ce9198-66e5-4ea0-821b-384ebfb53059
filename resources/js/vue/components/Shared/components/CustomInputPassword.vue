<template>
    <div class="grid grid-cols-2 gap-x-8 pr-2 gap-y-4 items-start justify-start">
        <div>
            <custom-input
                :dark-mode="darkMode"
                :type="innerInputType"
                @update:model-value="handleChangePassword"
                :model-value="passwordValue"
                :placeholder="placeholder"
                :auto-complete="newPassword ? 'new-password' : 'current-password'"
                label="*Password"
            />
        </div>
        <div class="flex flex-col items-center justify-center h-full text-center text-sm row-span-2">
            <label>Show Password</label>
            <input type="checkbox"
                   v-model="showPassword"
                   value="true"
                   class="mt-2"
                   tabindex="-1"
            />
        </div>
        <div>
            <custom-input
                v-if="newPassword"
                :dark-mode="darkMode"
                :type="innerInputType"
                :placeholder="placeholder"
                label="*Confirm Password"
                auto-complete="new-password"
                @update:model-value="handleChangeConfirmPassword"
                :model-value="confirmPasswordValue"
            />
        </div>
    </div>
</template>

<script>
    import CustomInput from "./CustomInput.vue";

    export default {
        name: 'CustomInputPassword',
        props: {
            darkMode: {
                type: Boolean,
                default: false,
            },
            label: {
                type: String,
                default: '',
            },
            passwordValue: {
                type: String,
                default: '',
            },
            confirmPasswordValue: {
                type: String,
                default: '',
            },
            newPassword: {
                type: Boolean,
                default: false,
            },
            placeholder: {
                type: String,
                default: 'Password...',
            },
        },
        emits: [
            'update:passwordValue',
            'update:confirmPasswordValue',
        ],
        data() {
            return {
                showPassword: false,
                innerInputType: 'password',
            }
        },
        components: {
            CustomInput
        },
        methods: {
            handleChangePassword(newValue) {
                this.$emit('update:passwordValue', newValue);
            },
            handleChangeConfirmPassword(newValue) {
                this.$emit('update:confirmPasswordValue', newValue);
            }
        },
        watch: {
            showPassword(newValue) {
                this.innerInputType = newValue
                    ? 'text'
                    : 'password';
            }
        }
    }
</script>
