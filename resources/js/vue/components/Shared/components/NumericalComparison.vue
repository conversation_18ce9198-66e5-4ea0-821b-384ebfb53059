<template>
    <p class="inline-flex flex-wrap text-xl font-bold gap-x-2 items-center">
        {{ formatAmount }}
        <span v-if="comparedAmount != null" class="inline-flex items-center gap-1 text-sm" :class="[increased ? (darkMode ? 'text-emerald-400' : 'text-emerald-600') : (darkMode ? 'text-rose-400' : 'text-rose-600')]">
            <span>
                <svg class="float-left mt-1 mr-1 fill-current flex-shrink-0" :class="[increased ? '' : 'transform rotate-180']" width="13" height="12" viewBox="0 0 13 12" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6.5 0L12.9952 11.25H0.00480938L6.5 0Z"/></svg>
                {{ getComparisonValue }} ({{increased ? '+' : ''}}{{ getComparisonPercentage }}%) <span :class="[darkMode ? 'text-slate-400' : 'text-slate-500']">{{increased ? 'above' : 'below'}} {{period}}</span>
            </span>
        </span>
    </p>
</template>

<script>
    /** Export constants into module scope so defineProps can access them */
    export const MONETARY = 0;
    export const BASIC = 1;
    export const TIME_SECONDS = 2;
</script>

<script setup>
    import {ref, computed, onMounted} from 'vue';

    const props = defineProps({
        darkMode: {
            type: Boolean,
            default: false
        },
        amount: {
            type: Number,
            default: 0
        },
        comparedAmount: {
            type: Number
        },
        period: {
            type: String,
            default: ''
        },
        type: {
            type: Number,
            default: MONETARY,
            validator(value) {
                return [MONETARY, BASIC, TIME_SECONDS].includes(value);
            }
        }
    });

    const comparisonValue =  ref(0);
    const comparisonPercentage =  ref(0);
    const increased =  ref(false);

    const getComparisonValue = computed(() => {
        let value = props.amount - props.comparedAmount;

        if(props.type === MONETARY) {
            return '$' + value.toLocaleString(undefined, { minimumFractionDigits: 0 }).replace('-', '');
        }
        else if(props.type === BASIC) {
            return value.toLocaleString(undefined, { minimumFractionDigits: 0 }).replace('-', '');
        }
        else if(props.type === TIME_SECONDS) {
            const hours = Math.floor(value / 3600);
            const minutes = Math.floor((value - (hours * 3600)) / 60);
            const seconds = Math.floor((value - (hours * 3600) - (minutes * 60)));

            return `${hours} hours ${minutes} mins ${seconds} secs`;
        }
        else {
            return value
        }
    });

    const getComparisonPercentage = computed(() => {
        if(props.amount + props.comparedAmount === 0) {
            return 0;
        }
        else {
            return Math.round(comparisonValue.value =  (props.amount - props.comparedAmount) / ((props.amount + props.comparedAmount) / 2) * 100)
        }
    });

    const formatAmount = computed(() => {
        if(props.type === 0) {
            return '$' + props.amount.toLocaleString(undefined, { minimumFractionDigits: 0 })
        }
        else if (props.type === 1) {
            return props.amount.toLocaleString(undefined, { minimumFractionDigits: 0 })
        }
        else if(props.type === 2) {
            const hours = Math.floor(props.amount / 3600);
            const minutes = Math.floor((props.amount - (hours * 3600)) / 60);
            const seconds = Math.floor((props.amount - (hours * 3600) - (minutes * 60)));

            return `${hours} hours ${minutes} mins ${seconds} secs`;
        }
        else {
            return props.amount
        }
    });

    const getVariation = () => {
        increased.value = props.amount > props.comparedAmount;
    };

    onMounted(() => {
        getVariation();
    });
</script>
