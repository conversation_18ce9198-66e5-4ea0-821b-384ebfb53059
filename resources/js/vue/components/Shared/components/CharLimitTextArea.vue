<template>
    <div>
        <textarea v-model="text" :placeholder="placeholder" :maxlength="maxLength" class="rounded h-24"
          :class="[
             textareaClass,
             darkMode ? ('hover:bg-dark-module text-slate-200 bg-dark-background') : 'hover:bg-light-module text-slate-700 bg-light-background',
          ]"></textarea>
        <span class="text-sm text-gray-500" v-bind:class="errorColor">{{ charCount }}/{{ maxLength }}</span>
    </div>
</template>

<script>
    export default {
        name: "CharLimitTextArea",
        props: {
            modelValue: {
                type: String,
                default: ''
            },
            maxLength: {
                type: Number,
                default: 250
            },
            placeholder: {
                type: String,
                default: ''
            },
            darkMode: {
                type: Boolean,
                default: false
            },
            textareaClass: {
                type: String,
                default: ''
            }
        },
        emits: [
            'update:modelValue'
        ],
        computed: {
            text: {
                get() {
                    return this.modelValue;
                },
                set(value) {
                    return this.$emit('update:modelValue', value.slice(0, this.maxLength));
                }
            },
            charCount: function () {
                return this.text.length;
            },
            errorColor: function () {
                return {
                    'text-red-500': this.text.length === this.maxLength
                }
            }
        }
    }
</script>
