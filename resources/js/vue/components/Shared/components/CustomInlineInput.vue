<template>
    <div class="relative flex items-center">
        <div
            class="appearance-none bg-transparent text-primary-500 hover:text-primary-400 text-center p-0 font-medium border-none outline-none active:bg-transparent focus:bg-transparent active:border-none active:outline-none focus:border-none focus:outline-none focus:ring-0"
            v-if="prefix">
            {{ prefix }}
        </div>
        <input
            :disabled="disabled"
            class="appearance-none bg-transparent text-primary-500 hover:text-primary-400 text-center p-0 font-medium border-none outline-none active:bg-transparent focus:bg-transparent active:border-none active:outline-none focus:border-none focus:outline-none focus:ring-0"
            :class="[darkMode ? '' : '', disabled ? 'opacity-25' : '', classes]"
            :style="`width: ${width}ch;border-bottom: 1px solid`"
            :type="type"
            :value="modelValue"
            :placeholder="placeholder"
            :name="name"
            @input="handleInput"
            @change="handleChange"
        />
    </div>
</template>

<script>
    export default {
        name: 'CustomInlineInput',
        emits: ['change', 'update:modelValue'],
        props: {
            darkMode: {
                type: Boolean,
                default: false,
            },
            autoWidth: {
                type: Boolean,
                default: true,
            },
            type: {
                type: String,
                default: 'text',
            },
            classes: {
                type: String,
                default: '',
            },
            modelValue: {
                type: [String, Number],
                default: "",
            },
            placeholder: {
                type: String,
                default: '',
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            name: {
                type: String,
                default: null,
            },
            extraWidth: {
                type: Number,
                default: 0,
            },
            prefix: {
                type: String,
                required: false,
            },
            addDefaultValue: {
                type: Boolean,
                default: true
            }
        },
        data() {
            return {
                width: 2,
            }
        },
        mounted() {
            this.updateWidth(this.modelValue ?? this.placeholder ?? null);
            if (!this.modelValue && this.addDefaultValue) this.$emit('update:modelValue', 0);
        },
        methods: {
            handleInput({ target }) {
                this.updateWidth(target.value);
                this.$emit("update:modelValue", target.value);
            },
            handleChange({ target }) {
                this.$emit('change', target.value);
            },
            updateWidth(selectedValue) {
                const value = selectedValue ?? this.modelValue ?? null;
                if (value) this.width = `${value}`.length + this.extraWidth;
            }
        },
        watch: {
            modelValue(newVal){
                this.updateWidth(newVal)
            }
        }

    }
</script>

<style scoped>

</style>
