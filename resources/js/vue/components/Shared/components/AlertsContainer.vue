<template>
    <div class="fixed flex mt-6 top-0 z-100 left-[50%] translate-x-[-50%]">
        <alert :text="text" :alert-type="alertType" :dark-mode="darkMode"></alert>
    </div>
</template>

<script>
import Alert from "./Alert.vue";
export default {
    name: "AlertsContainer",
    components: {
        Alert,
    },
    data() {
        return {
            showAlert: false,
        }
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        alertType: {
            type: String,
            default: null
        },
        text: {
            type: String,
            default: null
        }
    }
}
</script>

<style scoped>

</style>
