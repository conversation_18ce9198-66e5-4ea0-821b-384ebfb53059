<template>
    <div class="flex items-center justify-center relative">
        <div @click="toggleActionsHandle"
             :class="[darkMode ? 'hover:bg-dark-border' : 'hover:bg-primary-100']"
             class="relative inline-flex items-center justify-center cursor-pointer p-2 rounded-full h-8 w-8">
            <slot name="icon">
                <div class="flex-shrink-0 bg-primary-500 h-1 w-1 rounded-full mr-1"></div>
                <div class="flex-shrink-0 bg-primary-500 h-1 w-1 rounded-full mr-1"></div>
                <div class="flex-shrink-0 bg-primary-500 h-1 w-1 rounded-full"></div>
            </slot>
        </div>
        <div v-if="actionsHandle" class="absolute top-5 z-50 border divide-y shadow-module rounded overflow-hidden"
             :class="[darkMode ? 'divide-dark-border border-dark-border hover:bg-dark-module text-slate-200' : 'border-light-border bg-light-background divide-light-border',
              width,
              openRight ? 'left-full' : 'right-0'
          ]"
        >
            <p v-if="!noEditButton" class="text-sm font-semibold py-2 px-3 cursor-pointer transition duration-200"
               :class="{'border-light-border bg-light-background hover:bg-light-module': !darkMode, 'border-dark-border hover:bg-dark-module bg-dark-background text-slate-200': darkMode}"
                @click="actionClicked('edit')"
            >{{ editLabel }}</p>
            <p v-if="!noDeleteButton" class="text-sm font-semibold py-2 px-3 border-b cursor-pointer transition duration-200"
               :class="{'border-light-border bg-light-background hover:bg-light-module': !darkMode, 'border-dark-border hover:bg-dark-module bg-dark-background text-slate-200': darkMode}"
                @click="actionClicked('delete')"
            >Delete</p>
            <p v-if="!noCustomAction" class="text-sm font-semibold flex items-center gap-2 py-2 px-3 cursor-pointer transition duration-200" v-for="customAction in customActions"
               :class="{'border-light-border bg-light-background hover:bg-light-module': !darkMode, 'border-dark-border hover:bg-dark-module bg-dark-background text-slate-200': darkMode}"
               @click="actionClicked(customAction.event)"
            >{{ customAction.name }}</p>
        </div>
        <div v-if="actionsHandle" @click="toggleActionsHandle" class="fixed z-40 inset-0 w-full h-full bg-transparent">

        </div>
    </div>
</template>

<script>
export default {
    name: "ActionsHandle",
    data() {
        return {
            actionsHandle: false,
        }
    },
    props: {
        darkMode: false,
        noEditButton: {
            type: Boolean,
            default: false
        },
        noDeleteButton: {
            type: Boolean,
            default: false
        },
        noCustomAction: {
            type: Boolean,
            default: true,
        },
        customActions: {
            type: Array,
            default: []
        },
        width: {
            type: String,
            default: 'w-40'
        },
        openRight: {
            type: Boolean,
            default: false,
        },
        editLabel: {
            type: String,
            default: 'Edit',
        }
    },
    emits: ['edit', 'delete'],
    methods: {
        toggleActionsHandle() {
            this.actionsHandle = ! this.actionsHandle
        },
        actionClicked(action) {
            this.toggleActionsHandle();
            this.$emit(action);
        }
    }
}
</script>

<style scoped>

</style>
