<template>
    <div v-if="!!filters?.length" v-for="filter in filters">
        <component :key="filter.id"
                   :is="getComponent(filter.type)"
                   :dark-mode="darkMode"
                   :filter-data="filter"
                   @update-defaults="updateFilterDefaults"
                   @update:filterOptions="updateFilterOptions"
                   :name="filter.name"
        >
        </component>
    </div>

    <AlertsContainer
        v-if="alertActive"
        :alert-type="alertType"
        :text="alertText"
        :dark-mode="darkMode"
    />
</template>

<script>
import Dropdown from '../Dropdown.vue'
import ToggleSwitch from '../ToggleSwitch.vue'
import CustomInput from '../CustomInput.vue'
import FilterRow from '../FilterConfigModal/FilterRow.vue'
import MultiSelect from '../MultiSelect.vue'
import {mapWritableState} from 'pinia'
import {useCompaniesServingAreaStore} from '../../modules/CompaniesServingArea.service.js'
import HorizontalDualOperatorOption from '../Filterables/HorizontalDualOperatorOption.vue'
import HorizontalDualOperatorWithDropdownOption from '../Filterables/HorizontalDualOperatorWithDropdownOption.vue'
import HorizontalIsOrIsNotMultiSelectOption from '../Filterables/HorizontalIsOrIsNotMultiSelectOption.vue'
import AlertsMixin from '../../../../mixins/alerts-mixin'
import AlertsContainer from '../AlertsContainer.vue'

const componentMap = {
    'horizontal-dual-operator': HorizontalDualOperatorOption,
    'horizontal-dual-operator-with-dropdown': HorizontalDualOperatorWithDropdownOption,
    'horizontal-is-or-is-not-multi-select': HorizontalIsOrIsNotMultiSelectOption,
}

const defaultComponent = () => {
    const div = document.createElement('div')
    div.innerText = 'MISSING COMPONENT'
    return div
}

export default {
    emits: ['update:filters', 'update:defaults', 'update:filterOptions'],
    name: 'CompanyServicingAreaFilters',
    mixins: [AlertsMixin],
    components: {
        AlertsContainer,
        MultiSelect,
        FilterRow,
        CustomInput,
        Dropdown, ToggleSwitch
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        initialPreset: {
            type: String,
            default: '',
        },
    },

    computed: {
        ...mapWritableState(useCompaniesServingAreaStore, {
            filters: 'filters',
            presets: 'presets',
        }),
    },

    methods: {
        getComponent (filterOptionId) {
            return componentMap[filterOptionId] ?? defaultComponent()
        },
        updateFilterDefaults (filterEvent) {
            this.$emit('update:defaults', filterEvent)
        },
        updateFilterOptions (arg) {
            const filterUpdated = this.filters.filter(x => x.id === arg.id)

            if (filterUpdated.length > 0) {
                filterUpdated[0].data = arg.form
            }
        },
    },
}
</script>

<style scoped>

</style>
