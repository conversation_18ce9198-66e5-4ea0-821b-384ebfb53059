<template>
    <div class="space-y-2">
        <div>
            <div class="flex items-center space-x-4 mb-2">
                <label class="block text-sm font-semibold">Phone</label>
            </div>
            <div class="grid grid-cols-1 gap-3 w-full items-center">
                <div>
                    <Dropdown :options="filters.phone.presence.options"
                              :maxHeight="'max-h-[8rem]'"
                              v-model="filters.phone.presence.selected"
                              :dark-mode="darkMode"
                              placeholder="Select presence"
                              class="col-span-2"></Dropdown>
                </div>
            </div>
        </div>
        <div>
            <div class="flex items-center space-x-4 mb-2">
                <label class="block text-sm font-semibold">Email</label>
            </div>
            <div class="grid grid-cols-1 gap-3 w-full items-center">
                <div>
                    <Dropdown :options="filters.email.presence.options"
                              :maxHeight="'max-h-[8rem]'"
                              v-model="filters.email.presence.selected"
                              :dark-mode="darkMode"
                              placeholder="Select presence"
                              class="col-span-2"></Dropdown>
                </div>
            </div>
        </div>
        <div>
            <div class="flex items-center space-x-4 mb-2">
                <label class="block text-sm font-semibold">Last Contacted (via Phone)</label>
            </div>
            <div class="w-full items-center">
                <div class="flex space-x-4 place-items-center">
                    <div>
                        <toggle-switch v-model="filters.lastContactedAt.active" :dark-mode="darkMode"></toggle-switch>
                    </div>
                    <Dropdown :class="{ 'pointer-events-none': !filters.lastContactedAt.active}"
                              :options="filters.lastContactedAt.operator.options"
                              :maxHeight="'max-h-[8rem]'"
                              v-model="filters.lastContactedAt.operator.selected"
                              :dark-mode="darkMode"
                              placeholder="Select operator"
                              :disabled="!filters.lastContactedAt.active"
                              class="col-span-2"></Dropdown>
                    <CustomInput
                        :disabled="!filters.lastContactedAt.active"
                        :dark-mode="darkMode"
                        type="number"
                        placeholder="Enter number of cadence"
                        v-model:modelValue="filters.lastContactedAt.value"></CustomInput>
                    <Dropdown :class="{ 'pointer-events-none': !filters.lastContactedAt.active}"
                              :options="lastContactedAtCadenceOptions"
                              :maxHeight="'max-h-[8rem]'"
                              v-model="filters.lastContactedAt.cadence.selected"
                              :dark-mode="darkMode"
                              placeholder="Select cadence"
                              :disabled="!filters.lastContactedAt.active"
                              class="col-span-2"></Dropdown>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import CustomInput from "../CustomInput.vue";
import Dropdown from "../Dropdown.vue";
import ToggleSwitch from "../ToggleSwitch.vue";
import {mapState, mapWritableState} from "pinia";
import {useContactsStore} from "../../../../../stores/contacts.store";
import {isObject} from "../../../../../modules/helpers";

export default {
    name: "ContactsFilters",
    methods: {
        isObject,
        parsePresenceObject(type) {
            if(isObject(this.filters?.[type]?.presence?.selected)) {
                this.filters[type].presence.selected = this.filters[type]?.presence?.selected?.id;
            }
        }
    },
    components: {ToggleSwitch, Dropdown, CustomInput},
    props: {
        darkMode: {type: Boolean, required: true},
    },
    computed: {
        ...mapWritableState(useContactsStore, {
            filters: 'contactsFilter'
        }),
        ...mapState(useContactsStore, {
            lastContactedAtCadenceOptions: 'lastContactedAtCadenceOptions'
        })
    },
    created() {
        this.parsePresenceObject('phone');
        this.parsePresenceObject('email');
    }
}
</script>

<style scoped>

</style>
