<template>
    <div class="grid gap-6">
        <div>
            <div class="flex items-center space-x-4 mb-2">
                <label class="block text-sm font-semibold">{{ storeFilters.industry.name }}</label>
            </div>
            <div class="flex items-center space-x-6">
                <toggle-switch v-model="storeFilters.industry.active" :dark-mode="darkMode"></toggle-switch>
                <div class="grid grid-cols-12 gap-3 w-full items-center" :class="[storeFilters.industry.active? '' : 'opacity-25 pointer-events-none']">
                    <Dropdown
                        :options="industryOptions"
                        :selected="storeFilters.industry.firstValue"
                        v-model="storeFilters.industry.firstValue"
                        :dark-mode="darkMode"
                        :maxHeight="'max-h-[8rem]'"
                        class="col-span-2"
                    ></Dropdown>
                </div>
            </div>
        </div>
        <div>
            <div class="flex items-center space-x-4 mb-2">
                <label class="block text-sm font-semibold">{{ storeFilters.status.name }}</label>
            </div>
            <div class="flex items-center space-x-6">
                <toggle-switch v-model="storeFilters.status.active" :dark-mode="darkMode"></toggle-switch>
                <div class="grid grid-cols-12 gap-3 w-full items-center" :class="[storeFilters.status.active? '' : 'opacity-25 pointer-events-none']">
                    <Dropdown
                        :options="statusOptions"
                        :selected="storeFilters.status.firstValue"
                        v-model="storeFilters.status.firstValue"
                        :dark-mode="darkMode"
                        :maxHeight="'max-h-[8rem]'"
                        class="col-span-2"
                    ></Dropdown>
                </div>
            </div>

        </div>
    </div>
</template>

<script>
import Dropdown from "../Dropdown.vue";
import ToggleSwitch from "../ToggleSwitch.vue";
import {mapWritableState} from 'pinia'
import CustomInput from "../CustomInput.vue";
import {useUnsoldLeadsFilters} from "../../../../../stores/unsold-leads-filters.store.js";

export default {
    emits: ['update:filters'],
    name: "UnsoldLeadsFilters",
    components: {
        CustomInput,
        Dropdown,
        ToggleSwitch
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        initialPreset: {
            type: String,
            default: '',
        }
    },
    data() {
        return {
            industryOptions: [
                {id: 'solar', name: 'Solar'},
                {id: 'roofing', name: 'Roofing'},
            ],
            statusOptions: [
                {id: 'unsold', name: 'Unsold'},
                {id: 'undersold', name: 'Undersold'},
            ],
        }
    },
    computed: {
        ...mapWritableState(useUnsoldLeadsFilters, {
            storeFilters: 'filters'
        })
    }
}
</script>

<style scoped>

</style>
