<template id="modal-template">
  <div class="absolute">
    <transition name="modal">
      <div class="fixed inset-0 flex items-center justify-center bg-opacity-75 bg-dark-background"
           :class="{ 'z-100': zIndexHundred, 'z-50': !zIndexHundred }">
        <div class="absolute shadow rounded-lg max-w-screen-sm w-4/6 sm:w-full"
             :class="{ 'bg-light-module': !darkMode, 'bg-dark-module border border-dark-border': darkMode, }">
          <div class="border-b" :class="{'border-light-border': !darkMode, 'border-dark-border': darkMode}">
            <div class="px-6 py-5 flex justify-between items-center">
              <div class="" :class="{'text-black': !darkMode, 'text-grey-120': darkMode}">
                <h4 class="text-xl">Create Tasks</h4>
              </div>
              <button v-if="!noCloseButton" class="modal-default-button" :disabled="inputsDisabled"
                      @click="$emit('close')" :class="{'text-black': !darkMode, 'text-grey-120': darkMode}">
                <svg class="w-4 fill-current" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M5.9035 4.78018L9.32136 1.36237C9.47273 1.21896 9.56036 1.01977 9.56036 0.796696C9.56036 0.358513 9.20184 0 8.76366 0C8.54058 0 8.34139 0.0876191 8.19799 0.231024L4.78018 3.65686L1.36237 0.231024C1.21896 0.0876191 1.01977 0 0.796696 0C0.358513 0 0 0.358513 0 0.796696C0 1.01977 0.0876191 1.21896 0.231024 1.36237L3.65686 4.78018L0.238999 8.19799C0.0876269 8.34139 0 8.54058 0 8.76366C0 9.20184 0.358513 9.56036 0.796696 9.56036C1.01977 9.56036 1.21896 9.47274 1.36237 9.32933L4.78018 5.9035L8.19799 9.32136C8.34139 9.47273 8.54058 9.56036 8.76366 9.56036C9.20184 9.56036 9.56036 9.20184 9.56036 8.76366C9.56036 8.54058 9.47274 8.34139 9.32933 8.19799L5.9035 4.78018Z"/>
                </svg>
              </button>
            </div>
          </div>
          <div :class="containerClasses">
            <div class="" :class="{'text-black': !darkMode, 'text-grey-120': darkMode}">
              <div>
                <div class="text-amber-500 text-center my-8 text-xl">You are about to create multiples tasks</div>
              </div>
            </div>
            <div :class="{'hidden' : noButtons, 'inline-block' : !noButtons}" class="flex justify-center gap-3 pt-5">
              <button class="transition duration-200 text-white font-medium focus:outline-none py-2 rounded-md px-5"
                      :class="{'bg-primary-500 hover:bg-blue-500': !disableConfirm, 'bg-grey-200 text-grey-500': disableConfirm, 'cursor-not-allowed': disableConfirm}"
                      @click.stop="$emit('companies-data', { data: companyData?.paginationData, all: true })">For All
                ({{ companyData?.paginationData.total ?? 0 }}) companies
              </button>

              <button class="transition duration-200 text-white font-medium focus:outline-none py-2 rounded-md px-5"
                      :class="{'bg-gray-500 hover:bg-blue-500': !disableConfirm || !this.disabledField, 'bg-grey-200 hover:bg-grey-200': disableConfirm || this.disabledField, 'cursor-not-allowed': disableConfirm || this.disabledField}"
                      :disabled="this.disabledField"
                      @click.stop="$emit('companies-data', { data: companyData?.companySelected, all: false })">Only
                selected ({{ companyData?.companySelected?.companies?.length ?? 0 }}) companies
              </button>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import Alert from "./Alert.vue";

export default {
  components: {Alert},
  props: {
    confirmText: {
      type: String,
      default: "Save"
    },
    companyData: {
      type: Object,
      default: {
        companySelected: {
            companies: [],
        },
        paginationData: null
      }
    },
    closeText: {
      type: String,
      default: 'Close'
    },
    noButtons: {
      type: Boolean,
      default: false
    },
    small: {
      type: Boolean,
      default: false
    },
    disableConfirm: {
      type: Boolean,
      default: false
    },
    inputsDisabled: {
      type: Boolean,
      default: false
    },
    darkMode: {
      type: Boolean,
      default: false,
    },
    fullWidth: {
      type: Boolean,
      default: true
    },
    restrictWidth: {
      type: Boolean,
      default: true
    },
    containerClasses: {
      type: String,
      default: 'p-8'
    },
    zIndexHundred: {
      type: Boolean,
      default: false
    },
    noCloseButton: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    disabledField() {
      return (this.companyData.companySelected == null || this.companyData.companySelected?.companies?.length <= 0);
    }
  }
}
</script>

<style lang="scss">

</style>
