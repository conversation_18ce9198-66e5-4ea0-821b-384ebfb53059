<template>
    <div id="sentry-report-button"
         :class="buttonClasses"
         class="relative z-0 transition duration-200 w-10 h-10 rounded-full inline-flex justify-center items-center cursor-pointer">
        <svg class="fill-current" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M4.3083 4.31813H13.6917C13.5945 4.17318 13.5108 4.01447 13.4055 3.87687L15.9372 1.29718L14.6646 0L12.1032 2.61088C11.8954 2.45782 11.678 2.3187 11.4525 2.19438C10.698 1.78307 9.85562 1.56787 9 1.56787C8.14438 1.56787 7.30199 1.78307 6.5475 2.19438C6.3225 2.31548 6.1083 2.45767 5.8977 2.60996L3.3363 0L2.0637 1.29718L4.5954 3.87687C4.4892 4.01539 4.4064 4.17318 4.3083 4.31813ZM3.3804 6.1529H0V7.98767H2.0763C2.0178 8.44177 1.98 8.9023 1.98 9.36374C1.98 9.82886 2.0178 10.2931 2.0763 10.7499H0V12.5847H2.4777C2.4894 12.6205 2.4966 12.6581 2.5083 12.6929C2.6775 13.2021 2.8872 13.6956 3.1338 14.1607C3.1734 14.2351 3.2193 14.3029 3.2607 14.3754L1.1637 16.5129L2.4363 17.8101L4.3353 15.8735C4.86487 16.4766 5.49499 16.9795 6.1965 17.3587C6.6276 17.5908 7.0803 17.7707 7.5429 17.8908C7.7265 17.9385 7.9128 17.9688 8.0991 17.9991V12.5746H9.8991V18C10.0862 17.9735 10.2718 17.9374 10.4553 17.8917C10.9231 17.7691 11.375 17.5905 11.8017 17.3597C12.2202 17.134 12.6243 16.8551 13.0014 16.5304C13.2336 16.3304 13.4514 16.1065 13.662 15.8735L15.5619 17.8101L16.8345 16.5129L14.7375 14.3754C14.7798 14.302 14.8257 14.2332 14.8653 14.1589C15.111 13.6956 15.3216 13.2021 15.4899 12.692C15.5016 12.6562 15.5088 12.6195 15.5205 12.5838H18V10.749H15.9228C15.9813 10.2912 16.0191 9.82794 16.0191 9.36283C16.0191 8.90138 15.9813 8.44085 15.9237 7.98675H18V6.1529H3.3804Z"/>
        </svg>
    </div>
</template>

<script>

export default {
    name: "SentryReportButton",
    props: {
      darkMode: {
          type: Boolean,
          default: false,
      }
    },
    computed: {
        buttonClasses() {
            const classes = [];

            if(this.darkMode) {
                classes.push('bg-dark-module', 'text-slate-400', 'border-primary-500', 'hover:border-blue-400', 'hover:bg-dark-background');
            } else {
                classes.push('bg-light-module', 'text-slate-600', 'border-light-border', 'hover:border-primary-500', 'hover:bg-cyan-125');
            }

            return classes;
        },
    }
}

</script>

<style>
    #sentry-feedback {
        --trigger-background: #cccccc;
        --inset: 4rem 0 auto auto;
        --accent-background: #0081FF;
        --background:  #14222F;
        --foreground: #FFFFFF;
        --outline: #20354A;
    }
</style>
