<template>
    <div class="pb-3">
        <div class="flex items-center">
            <input :class="[darkMode ? 'bg-dark-module' : 'bg-light-module']" :value="id" :checked="selected"
                   class="mr-1 checked:bg-primary-500 focus:bg-primary-500 focus:ring-0 focus:outline-none"
                   type="checkbox" @input="updateSelected" :id="`radio-${id}`">
            {{ title }}
        </div>
        <slot v-if="$slots.input" name="input" v-bind:value="input" v-bind:selected="selected"
              v-bind:dark-mode="darkMode"></slot>
        <div v-else>
            <CustomInput :model-value="input" @update:modelValue="updateInput" :placeholder="placeholder"
                         :dark-mode="darkMode"
                         v-if="selected && !dontShowInput" :id="`input-${id}`" class="py-3"></CustomInput>
        </div>
    </div>
</template>
<script setup>
import CustomInput from './CustomInput.vue'

const props = defineProps({
    darkMode: Boolean,
    input: String,
    selected: <PERSON>olean,
    id: String,
    title: String,
    dontShowInput: Boolean,
    placeholder: String,
})

const emit = defineEmits(['update:selected', 'update:input'])

const updateSelected = (event) => {
    emit('update:selected', event.target.checked)
}

const updateInput = (arg) => {
    emit('update:input', arg)
}
</script>

<script>
export default {
    name: 'CheckboxInputFilterOption'
}
</script>
