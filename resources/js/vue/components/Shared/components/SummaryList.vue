<template>
    <div>
        <div
            class="summary-container"
            @mouseover="showTooltip"
            @mouseleave="hideTooltip"
        >
            <div class="summary-items">
                <div v-for="(item, index) in limitedItems" :key="index" class="summary-item mr-1">
                    {{ item }}<span v-if="index < limitedItems.length - 1">, </span>
                </div>
                <div v-if="hasMoreItems" class="more-items">
                    +{{ extraItemCount }} more...
                </div>
            </div>

            <div v-if="isTooltipVisible && hasMoreItems" class="tooltip-content" :class="{ 'bg-light-module text-slate-900': !darkMode, 'bg-dark-module text-slate-900': darkMode }">
                <div v-for="(item, index) in fullItems" :key="index" class="tooltip-item">
                    {{ item }}<span v-if="index < fullItems.length - 1">, </span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        items: {
            type: Array,
            required: true
        },
        limit: {
            type: Number,
            default: 3
        },
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            isTooltipVisible: false
        };
    },
    computed: {
        limitedItems() {
            return this.items.slice(0, this.limit);
        },
        hasMoreItems() {
            return this.items.length > this.limit;
        },
        extraItemCount() {
            return this.items.length - this.limit;
        },
        fullItems() {
            return this.items;
        }
    },
    methods: {
        showTooltip() {
            if (this.hasMoreItems) {
                this.isTooltipVisible = true;
            }
        },
        hideTooltip() {
            this.isTooltipVisible = false;
        }
    }
}
</script>

<style scoped>
.summary-container {
    position: relative;
    display: inline-block;
}

.summary-items {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}

.summary-item {
    display: inline;
}

.more-items {
    display: inline;
}

.tooltip-content {
    position: absolute;
    z-index: 1000;
    background-color: #fff;
    border: 1px solid #ccc;
    padding: 15px;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
    width: 280px;
    max-height: 280px;
    overflow-y: auto;
    white-space: normal;
    top: 100%;
    left: 0;
}

.tooltip-item {
    margin-bottom: 5px;
}
</style>

