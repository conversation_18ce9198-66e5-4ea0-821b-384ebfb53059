<template>
    <div
        :id="id"
        @click="handleClick"
        class="inline relative cursor-pointer"
        :class="{'active-tip': isActiveTip}">
        <div @mouseover="toggleActiveTipTrue" @mouseleave="toggleActiveTipFalse">
            <slot name="title">

            </slot>
        </div>
        <div class="relative" v-if="!hideTooltip">
            <div v-show="isActiveTip"
                 class="shadow-xl absolute transition-all duration-150 p-2 rounded border-grey-200 z-20 mt-2"
                 :class="[
                    backgroundStyle,
                    right || mobileRight ? 'right-0' : '',
                    mobileLeft ? 'left-0' : '',
                    large ? 'w-64' : '',
                    extraLarge ? 'w-96' : '',
                    fontLarger ? 'text-2xl' : '',
                 ]"
            >
                <slot></slot>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'HoverTooltip',
    emits: ['click'],
    props: {
        right: {
            type: Boolean,
            default: false
        },
        mobileLeft: {
            type: Boolean,
            default: false
        },
        mobileRight: {
            type: Boolean,
            default: false
        },
        large: {
            type: Boolean,
            default: false
        },
        extraLarge: {
            type: Boolean,
            default: false
        },
        fontLarger: {
            type: Boolean,
            default: false
        },
        hideTooltip: {
            type: Boolean,
            default: false
        },
        id: {
            type: String,
            required: false,
        },
        darkMode: {
            type: Boolean,
            default: false,
        },
        toolTipStyle: {
            type: String,
            default: 'default'
        }
    },
    data () {
        return {
            isActiveTip: false,
        }
    },
    computed: {
        backgroundStyle() {
            const variants = {
                default: this.darkMode ? 'bg-primary-700 text-slate-200' : 'bg-primary-50 text-slate-900',
                light: this.darkMode ? 'bg-dark-background text-slate-200' : 'bg-light-module text-slate-900',
            }

            return variants[this.toolTipStyle] ?? variants.default
        }
    },
    methods: {
        toggleActiveTipTrue () {
            this.isActiveTip = true
        },
        toggleActiveTipFalse () {
            this.isActiveTip = false
        },
        handleClick () {
            this.$emit('click')
        }
    }
}
</script>

<style>

</style>
