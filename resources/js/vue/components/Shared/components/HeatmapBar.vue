<template>
    <div class="w-full h-2 rounded-md relative inline-flex items-center" style="background: linear-gradient(-90deg, rgba(5, 255, 0, 0.50) 0%, rgba(255, 168, 0, 0.50) 49.5%, rgba(255, 0, 19, 0.50) 100%);">
        <div class="-top-[3px] absolute w-1 h-3.5 rounded-md" :class="[darkMode ? 'bg-white' : 'bg-black']" :style="{left: getPercentage + '%'}">

        </div>
    </div>
</template>

<script>
export default {
    name: "HeatmapBar",
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        value: {
            type: Number,
            default: 0
        },
        maxValue: {
            type: Number,
            default: 0
        }
    },
    computed: {
        getPercentage() {
            let percentage = Math.round((this.value / this.maxValue * 100))

            if(percentage > 100) {
                return 100
            }
            else {
                return percentage
            }
        }
    }
}
</script>

<style scoped>

</style>
