<template>
    <Line
        :chart-options="chartStyles"
        :chart-data="chartData"
        :chart-id="chartId"
        :dataset-id-key="datasetIdKey"
        :plugins="plugins"
        :css-classes="cssClasses"
        :styles="styles"
        :width="width"
        :height="height"
    />
</template>

<script>
import {Line} from 'vue-chartjs'
import 'chart.js/auto'

export default {
    name: 'LineChart',
    components: {Line},
    props: {
        chartId: {
            type: String,
            default: 'line-chart'
        },
        datasetIdKey: {
            type: String,
            default: 'label'
        },
        width: {
            type: Number,
            default: 400
        },
        height: {
            type: Number,
            default: 400
        },
        cssClasses: {
            default: '',
            type: String
        },
        styles: {
            type: Object,
            default: () => {
            }
        },
        plugins: {
            type: Object,
            default: () => {
            }
        },
        chartOptions: {
            type: String,
            default: "default"
        },
        chartData: {
            type: Object,
            default: {
                labels: ['default'],
                datasets: [
                    {
                        label: 'line chart',
                        data: [],
                        backgroundColor: ['red'],
                        borderColor: ['red'],
                        borderWidth: 4,
                        tension: 0.4,
                        pointRadius: 0
                    },
                ]
            }
        },
    },
    computed: {
        chartStyles() {
            const styles = {
                default: {
                    responsive: true,
                    maintainAspectRatio: false
                },
                noGrid: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            display: false,
                            grid: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            align: 'end',
                            labels: {
                                font: {
                                    size: 12,
                                    weight: 'bold',
                                },
                                usePointStyle: true,
                                pointStyle: 'circle',
                            }
                        },
                    }
                },
            }

            return this.chartData?.options ?? styles[this.chartOptions] ?? styles.default
        }
    }
}
</script>
