<template>
    <modal
        :confirm-text="savingTask ? 'Saving...' : 'Create Task'"
        close-text="Cancel"
        :dark-mode="darkMode"
        @close="closeModal"
        @confirm="createTask"
        small
        :disable-confirm="savingTask"
    >
        <template v-slot:header>
            <h4 class="text-xl">Create Task</h4>
        </template>
        <template v-slot:content>
            <div class="mb-6" v-if="alertMessage">
                <alert :text="alertMessage" :alert-type="alertType" :dark-mode="darkMode"></alert>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                <div>
                    <p class="uppercase font-semibold text-xs mb-2"
                       :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                        Company
                    </p>
                    <autocomplete
                        :dark-mode="darkMode"
                        class="col-span-2"
                        v-model="company"
                        :options="companies"
                        placeholder="Company name"
                        :model-value="companyId"
                        :create-user-input-option="true"
                        @search="searchCompanies('companyname', $event)">
                    </autocomplete>
                </div>
                <div>
                    <p class="uppercase font-semibold text-xs mb-2"
                       :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                        Task Type
                    </p>
                    <dropdown :options="taskTypes" :dark-mode="darkMode" :placeholder="'Select Task Type'" v-model="taskType"></dropdown>
                </div>
                <div>
                    <p class="uppercase font-semibold text-xs mb-2"
                       :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                        Task Note
                    </p>
                    <input class="w-full border rounded px-3 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                           placeholder="Task note"
                           v-model="taskNote"
                           :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}">
                </div>
                <div>
                    <p class="uppercase font-semibold text-xs mb-2"
                       :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                        Available At
                    </p>
                    <button class="transition duration-200 text-sm font-semibold focus:outline-none py-3 rounded-lg px-5" @click="showCalender = true"
                            :class="{'bg-grey-475 hover:bg-blue-800 text-white': !darkMode, 'bg-blue-400 hover:bg-blue-500 text-white': darkMode}">
                        Pick Date Time
                    </button>
                    {{ available_at }}
                    <div v-if="showCalender" class="mt-3">
                        <Datepicker :inline="true" @update:modelValue="setDateTime" :format="'yyyy-MM-dd'" :start-time="manualTaskDefaultTime"></Datepicker>
                    </div>
                </div>
                <div>
                    <p class="uppercase font-semibold text-xs mb-2"
                       :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                        Priority
                    </p>
                    <Dropdown :dark-mode="darkMode" class="mr-2" v-model="priority" :options="taskPriorities" placeholder="Priority" :selected="priority"></Dropdown>
                </div>
                <div>
                    <p class="uppercase font-semibold text-xs mb-2"  :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                        Task Category
                    </p>

                    <Dropdown v-model="taskCategory" :options="taskCategories" :dark-mode="darkMode" placeholder="Select Category" :selected="taskCategory"></Dropdown>
                </div>
            </div>
        </template>
    </modal>
</template>

<script>
import Tab from "../components/Tab.vue";
import ApiService from "../../Tasks/services/api";
import LoadingSpinner from "../components/LoadingSpinner.vue";
import Pagination from "../components/Pagination.vue";
import Dropdown from "../components/Dropdown.vue";
import Campaigns from "../../Companies/Campaigns.vue";
import Modal from "../components/Modal.vue";
import Autocomplete from "../components/Autocomplete.vue";
import SharedApiService from "../services/api";
import TaskManagementApiService from "../../TaskManagement/services/api";
import Alert from "../components/Alert.vue";
import BulkComplete from "../../Tasks/BulkComplete.vue";
import DeleteTask from "../../Tasks/DeleteTask.vue";
import ButtonDropdown from "../components/ButtonDropdown.vue";
import _ from "lodash";

export default {
    name: "CreateTaskForCompany",
    components: {
        ButtonDropdown,
        Campaigns,
        Dropdown,
        Tab,
        LoadingSpinner,
        Pagination,
        Modal,
        Autocomplete,
        Alert,
        BulkComplete,
        DeleteTask
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: Number,
            default: null
        },
        companyName: {
            type: String,
            default: null
        },
        taskNote: {
            type: String,
            default: null
        },
    },
    data() {
        return {
            apiService: ApiService.make(),
            sharedApi: SharedApiService.make(),
            taskManagementApi: TaskManagementApiService.make(),
            taskPriorities: [
                { id: 1, name: 'Low'   },
                { id: 2, name: 'Medium'},
                { id: 3, name: 'High'  },
                { id: 4, name: 'Urgent'}
            ],
            companies: [],
            taskTypes: [],
            available_at: null,
            priority: 1,
            taskType: null,
            company: null,
            showCalender: false,
            alertMessage: null,
            savingTask: false,
            alertType: 'success',
            manualTaskDefaultTime: {hours: 8, minutes: 0, seconds: 0},
            taskCategory: 1,
            taskCategories: [],
        }
    },
    created() {
        this.getTaskTypes()
        this.getTaskCategories();

        if (this.companyId && this.companyName) {
            this.companies = [{
                id: this.companyId,
                name: `${this.companyId}: ${this.companyName}`
            }];
        }
    },
    methods: {
        getTaskCategories() {
            this.taskManagementApi.getTaskCategories().then(resp => this.taskCategories = resp.data.data.categories);
        },

        createTask() {
            if (this.savingTask) return;

            this.alertMessage = null;
            this.savingTask = true;

            if (_.isString(this.company)) {
                this.alertType = 'error';
                this.alertMessage = "Please select a company from the dropdown";
                this.savingTask = false;
                return;
            }

            this.apiService.createTask({
                company_id: this.company,
                task_type_id: this.taskType,
                subject: this.taskNote,
                available_at: this.available_at,
                priority: this.priority,
                task_category_id: this.taskCategory
            }).then(() => {
                this.closeModal();
            }).catch(e => {
                this.alertType = 'error';
                this.alertMessage = e.response.data.message;
            }).finally(() => {
                this.savingTask = false;
            });
        },
        searchCompanies(nameType, query) {
            this.sharedApi.searchCompanyNamesAndId(query).then(res => {
                if (res.data.data.status === true) {
                    this.companies = res.data.data.companies;
                }
            })
        },
        getTaskTypes() {
            this.taskManagementApi.getTaskTypes().then(resp => this.taskTypes = resp.data.data.task_types);
        },
        setDateTime(date) {
            this.available_at = this.$filters.dateFromTimestamp(date, 'YYYY-MM-DD HH:mm');
            this.showCalender = false;
        },
        closeModal(){
            this.$emit('close')
        }
    }
}
</script>

<style scoped>

</style>
