<template>
    <div class="main-layout font-body">
        <div class="w-full">
            <div class="w-full flex-auto relative"
                 :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
                <div :class="[darkMode ? 'text-white' : 'text-slate-900']">
                    <div class="flex flex-col px-5 pt-5 border-b mb-5 gap-4"
                         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
                        <div class="flex items-center justify-between flex-wrap pl-4">
                            <h3 class="text-xl font-medium pb-0 leading-none mr-5">{{ title }}</h3>
                        </div>
                        <div class="flex justify-between">
                            <Tab
                                :dark-mode="darkMode"
                                :tabs="tabs"
                                @selected="selectTab"
                                tab-style="fit"
                                background-color="light"
                                :tab-type="'Normal'"
                            />
                        </div>
                    </div>
                    <div :class="tabClasses">
                        <component :is="currentTabComponent" :dark-mode="darkMode"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import AlertsContainer from "./AlertsContainer.vue";
import useQueryParams from "../../../../composables/useQueryParams.js";
import Tab from "./Tab.vue";
export default {
    name: "TabbedPage",
    components: {AlertsContainer, Tab},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: null,
        },
        tabs: {
            type: Array,
            default: []
        },
        tabClasses: {
            type: String,
            default: ""
        }
    },
    data() {
        return {
            selectedTab: null,
            queryParamsHelper: useQueryParams()
        }
    },
    computed: {
        currentTabComponent() {
            return this.tabs.find(e => e.current)?.component
        }
    },
    methods: {
        selectTab(tab) {
            this.queryParamsHelper.setQueryParamsOnCurrentUrl({tab})
            this.setSelectedTab(tab)
        },
        setSelectedTab(tab) {
            this.selectedTab = tab;
            this.tabs.forEach(e => {
                e.current = e.name === this.selectedTab
            })
        },
    }
}
</script>
