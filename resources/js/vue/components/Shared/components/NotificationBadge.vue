<template>
    <div class="absolute inline-flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-red-500 rounded-full border top-0 right-1"
         :class="[exceeded ? 'text-[8px]' : 'text-[10px]', darkMode ? 'border-dark-module' : 'border-light-module']"
    >
        {{local}}
    </div>
</template>

<script>
export default {
    name: "NotificationBadge",
    props: {
        value: {
            type: Number,
            required: true
        },

        maxValue: {
            type: Number,
            default: 99
        },
        darkMode: {
            type: Boolean,
            default: false
        }
    },

    computed:{
        exceeded() {
            return this.value > this.maxValue
        },

        local(){
            return this.exceeded ? `${this.maxValue}+` : this.value
        }
    }
}
</script>

<style scoped>

</style>
