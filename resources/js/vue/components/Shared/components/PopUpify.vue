<template>
    <div class="absolute">
        <transition name="modal">
            <div class="fixed inset-0 flex items-center justify-center bg-opacity-75 bg-dark-background z-50">
                <div
                     :class="{
                    [`bg-light-module`] : !darkMode,
                    [`bg-dark-module border border-dark-border`] : darkMode,
                    [`${classes}`] : true}"
                >
                    <slot></slot>
                </div>
            </div>
        </transition>
    </div>
</template>
<script>
export default {
    name: "PopUpify",
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        classes: {
            type: String,
            default: "absolute shadow rounded-lg"
        }
    }
}
</script>
