<template>
    <div class="main-layout font-body">
        <div class="w-full">
            <div class="w-full flex-auto pt-3 relative" :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
                <div class="px-10" :class="[darkMode ? 'text-white' : 'text-slate-900']">
                    <div v-if="title" class="flex items-center justify-between flex-wrap py-4">
                        <slot name="title">
                            <div class="flex justify-between items-center w-full py-2">
                                <h3 class="text-xl font-medium pb-0 leading-none mr-5">{{ title }}</h3>
                            </div>
                        </slot>
                    </div>
                    <slot name="content"></slot>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    name: "Page",
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: null,
        }
    }
}
</script>
