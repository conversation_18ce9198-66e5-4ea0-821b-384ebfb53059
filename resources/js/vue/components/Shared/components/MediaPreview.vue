<template>
    <Modal
        :dark-mode="darkMode"
        :small="true"
        :no-close-button="true"
        :hide-confirm="true"
        :no-min-height="true"
        @close="hidePreview"
    >
        <template v-slot:header>
            View Asset
        </template>
        <template v-slot:content>
            <div class="flex items-center justify-center relative">
                <div v-if="mediaAsset?.type === mediaAssetType.media">
                    <img
                        :src="getMediaLinkToShowPreview(mediaAsset)"
                        :alt="prepareMediaThumbnailAlt(mediaAsset)"
                    />
                </div>
                <div
                    v-else-if="mediaAsset?.type === mediaAssetType.link"
                    class="h-full"
                >
                    <p
                        v-if="previewError"
                        class="text-red-900 text-center text-sm py-4"
                    >
                        {{ previewError }}
                    </p>
                    <iframe
                        width="625" height="360"
                        class="mx-auto my-auto pt-6"
                        :src="`https://www.youtube.com/embed/${getMediaLinkToShowPreview(mediaAsset)}`"
                        title="Company Video"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                        allowfullscreen
                    />
                </div>
            </div>
        </template>
    </Modal>
</template>

<script>
import Modal from "../components/Modal.vue";

export default {
    name: "MediaPreview",
    components: {
        Modal,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        mediaAsset: {
            type: Object,
            default: null,
        },
    },
    emits: [
        'modalClosureRequested',
    ],
    data() {
        return {
            mediaAssetType: {
                media: 'media',
                link: 'link',
            },
            previewError: null,
        }
    },
    methods: {
        getMediaLinkToShowPreview(mediaAsset) {
            this.previewError = null;

            let link = null;

            if (mediaAsset?.url && mediaAsset?.type) {
                if (mediaAsset.type === this.mediaAssetType.link) {
                    link = this.getYoutubeLink(mediaAsset.url);
                    if (!link) {
                        this.previewError = `There was an error fetching the Youtube video. Video links should be in the following format: https://www.youtube.com/watch?v=CANocXrAyk8`;
                        return;
                    }
                }
                else {
                    link = mediaAsset.url;
                }
            }

            return link;
        },
        getYoutubeLink(url) {
            return url.match(/v=([0-z]+)/)?.[1];
        },
        prepareMediaThumbnailAlt(mediaAsset) {
            const alt = `uploaded ${mediaAsset.type}`;
            return mediaAsset.name.length > 0
                ? `${alt}: ${mediaAsset.name}`
                : alt;
        },
        hidePreview() {
            this.$emit('modalClosureRequested');
        },
    }
}
</script>

<style scoped>

</style>
