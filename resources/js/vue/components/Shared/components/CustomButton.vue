<template>
    <button :type="type" v-if="disabled" disabled class="group relative text-sm rounded-md inline-flex items-center justify-center px-5 py-2 font-semibold transition duration-200 cursor-not-allowed"
            :class="[darkMode ? 'bg-slate-700 text-slate-500' : 'text-slate-400 bg-slate-300', height]"
    >
        <span class="mr-1" v-if="icon">
            <slot name="icon">

            </slot>
        </span>
        <slot></slot>
        <span v-if="tooltip" class="absolute -bottom-8 z-50 left-0 w-auto whitespace-nowrap transition-all duration-200 py-1 px-2 font-medium text-xs rounded-md opacity-0 group-hover:opacity-100 shadow-md" :class="[darkMode ? 'text-slate-400 bg-dark-module border-dark-border' : 'text-slate-600 bg-light-module border-light-border']">{{tooltip}}</span>
    </button>
    <button :type="type" v-else class="text-sm rounded-md inline-flex justify-center items-center px-5 py-2 font-semibold transition duration-200"
            :class="[
                color === 'primary' ? 'bg-primary-500 hover:bg-primary-600 text-white' : '',
                color === 'primary-outline' ? (darkMode ? 'text-primary-500 border border-primary-500 bg-transparent hover:bg-dark-background' : 'text-primary-500 border border-primary-500 bg-transparent hover:bg-primary-100 hover:bg-opacity-50') : '',
                color === 'primary-simple' ? (darkMode ? 'text-primary-500 bg-transparent hover:bg-dark-background' : 'text-primary-500 bg-transparent hover:bg-primary-100 hover:bg-opacity-50') : '',
                color === 'green' ? 'bg-emerald-500 hover:bg-emerald-600 text-white' : '',
                color === 'green-outline' ? 'text-emerald-500 border border-emerald-500 bg-transparent hover:bg-emerald-100 hover:bg-opacity-50' : '',
                color === 'red' ? 'bg-rose-500 hover:bg-rose-600 text-white' : '',
                color === 'red-outline' ? (darkMode ? 'text-rose-500 border border-rose-500 bg-transparent hover:bg-dark-background' : 'text-rose-500 border border-rose-500 bg-transparent hover:bg-rose-100 hover:bg-opacity-50') : '',
                color === 'amber' ? 'bg-amber-500 hover:bg-amber-600 text-white' : '',
                color === 'amber-outline' ? (darkMode ? 'text-amber-500 border border-amber-500 bg-transparent hover:bg-dark-background'  : 'text-amber-500 border border-amber-500 bg-transparent hover:bg-amber-100 hover:bg-opacity-50') : '',
                color === 'slate' ? 'bg-slate-600 hover:bg-slate-700 text-white' : '',
                color === 'slate-outline' ? 'text-slate-500 border border-slate-500 bg-transparent hover:bg-slate-500 hover:bg-opacity-25' : '',
                color === 'slate-light' ? (darkMode ? 'bg-slate-500 hover:bg-slate-600 text-white' : 'bg-slate-400 hover:bg-slate-500 text-white') : '',
                color === 'slate-inverse' ? (darkMode ? 'bg-dark-background hover:bg-dark-border text-slate-300' : 'bg-light-background hover:bg-light-border text-slate-900') : '',
                color === 'gray' ? 'bg-grey-250 hover:bg-grey-400 text-white' : '',
                color === 'orange' ? 'bg-orange-400 hover:bg-orange-500 text-white' : '',
                height
                ]">
        <span class="mr-1" v-if="icon">
            <slot name="icon">

            </slot>
        </span>
        <slot></slot>
    </button>
</template>

<script>
export default {
    name: "CustomButton",
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        color: {
            type: String,
            default: 'primary',
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        icon: {
            type: Boolean,
            default: false,
        },
        type: {
            type: String,
            default: "button"
        },
        height: {
            type: String,
            default: 'h-9'
        },
        tooltip: {
            type: String,
            default: null
        }
    }
}
</script>

<style scoped>

</style>
