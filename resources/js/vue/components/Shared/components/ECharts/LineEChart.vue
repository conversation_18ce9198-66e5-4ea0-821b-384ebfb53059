<template>
    <v-chart
        :class="[height, width]"
        :option="chartOptions"
        :theme="darkMode ? 'a2dark' : 'a2light'"
        :autoresize="true" />
</template>

<script setup>
    import {registerTheme, use} from 'echarts/core';
    import {SVGRenderer} from "echarts/renderers";
    import VChart from "vue-echarts";
    import {LineChart} from "echarts/charts";
    import {TitleComponent, TooltipComponent, LegendComponent, GridComponent, MarkLineComponent} from "echarts/components";
    import {ref, computed} from "vue";
    import a2dark from './themes/a2dark';
    import a2light from './themes/a2light';

    const props = defineProps({
        darkMode: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            default: ''
        },
        legend: {
            type: Object,
            default: () => {
                return {
                    show: true
                };
            }
        },
        seriesData: {
            type: Array,
            default: () => []
        },
        xAxisName: {
            type: String,
            default: ''
        },
        xAxisData: {
            type: Array,
            default: () => []
        },
        yAxisName: {
            type: String,
            default: ''
        },
        yAxisMin: {
            type: Number,
            default: 0
        },
        yAxisMax: {
            type: Number,
            default: 100
        },
        height: {
            type: String,
            default: 'h-100'
        },
        width: {
            type: String,
            default: 'w-full'
        },
        alignWithLabel: {
            type: Boolean,
            default: true
        },
        lineSymbol: {
            type: String,
            default: 'none',
            validator(value) {
                return ['circle', 'rect', 'roundRect', 'triangle', 'diamond', 'pin', 'arrow', 'none'].includes(value);
            }
        }
    });

    registerTheme('a2dark', a2dark);
    registerTheme('a2light', a2light);

    use([
        SVGRenderer,
        TitleComponent,
        TooltipComponent,
        LegendComponent,
        LineChart,
        GridComponent,
        MarkLineComponent
    ]);

    const chartOptions = computed(() => {
        return {
            aria: {
                enabled: true
            },
            title: {
                text: props.title,
                left: "center"
            },
            tooltip: {
                trigger: "item"
            },
            legend: props.legend,
            xAxis: {
                type: 'category',
                data: props.xAxisData,
                name: props.xAxisName,
                nameTextStyle: {
                    padding: 20,
                    fontWeight: 'bold',
                    fontSize: 16
                },
                nameLocation: 'middle',
                axisTick: {
                    alignWithLabel: props.alignWithLabel
                }
            },
            yAxis: {
                type: 'value',
                name: props.yAxisName,
                nameLocation: 'middle',
                nameTextStyle: {
                    padding: 50,
                    fontWeight: 'bold',
                    fontSize: 16
                },
                min: props.yAxisMin,
                max: props.yAxisMax
            },
            series: props.seriesData.map((ds) => {
                let series = {
                    type: 'line',
                    name: ds.name,
                    data: ds.data,
                    symbol: props.lineSymbol,
                    areaStyle: ds.areaStyle || {},
                    ...ds // Spread all properties from the data source
                };

                if(ds.target
                && ds.targetLabel) {
                    series.markLine = {
                        symbol: 'circle',
                            data: [
                            {
                                yAxis: ds.target,
                                label: {
                                    show: true,
                                    position: 'insideStartTop',
                                    formatter: ds.targetLabel
                                }
                            }
                        ]
                    };
                }

                return series;
            })
        };
    });
</script>
