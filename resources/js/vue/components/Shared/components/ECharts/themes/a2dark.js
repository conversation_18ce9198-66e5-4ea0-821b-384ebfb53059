export default
{
    "color": [
        "#007fff",
        "#8fdcff",
        "#fffb4d",
        "#75ff92",
        "#ff7575",
        "#ff8c00"
    ],
    "backgroundColor": "rgba(20,34,47,1)",
    "textStyle": {},
    "title": {
        "textStyle": {
            "color": "#eeeeee"
        },
        "subtextStyle": {
            "color": "#eeeeee"
        }
    },
    "line": {
        "itemStyle": {
            "borderWidth": "2"
        },
        "lineStyle": {
            "width": "3"
        },
        "symbolSize": "8",
        "symbol": "emptyCircle",
        "smooth": false
    },
    "radar": {
        "itemStyle": {
            "borderWidth": "2"
        },
        "lineStyle": {
            "width": "3"
        },
        "symbolSize": "8",
        "symbol": "emptyCircle",
        "smooth": false
    },
    "bar": {
        "itemStyle": {
            "barBorderWidth": "0",
            "barBorderColor": "#ccc"
        }
    },
    "pie": {
        "itemStyle": {
            "borderWidth": "0",
            "borderColor": "#ccc"
        }
    },
    "scatter": {
        "itemStyle": {
            "borderWidth": "0",
            "borderColor": "#ccc"
        }
    },
    "boxplot": {
        "itemStyle": {
            "borderWidth": "0",
            "borderColor": "#ccc"
        }
    },
    "parallel": {
        "itemStyle": {
            "borderWidth": "0",
            "borderColor": "#ccc"
        }
    },
    "sankey": {
        "itemStyle": {
            "borderWidth": "0",
            "borderColor": "#ccc"
        }
    },
    "funnel": {
        "itemStyle": {
            "borderWidth": "0",
            "borderColor": "#ccc"
        }
    },
    "gauge": {
        "itemStyle": {
            "borderWidth": "0",
            "borderColor": "#ccc"
        }
    },
    "candlestick": {
        "itemStyle": {
            "color": "#10b981",
            "color0": "#ff8888",
            "borderColor": "#10b981",
            "borderColor0": "#ff8888",
            "borderWidth": "1"
        }
    },
    "graph": {
        "itemStyle": {
            "borderWidth": "0",
            "borderColor": "#ccc"
        },
        "lineStyle": {
            "width": "1",
            "color": "#eeeeee"
        },
        "symbolSize": "8",
        "symbol": "emptyCircle",
        "smooth": false,
        "color": [
            "#007fff",
            "#8fdcff",
            "#fffb4d",
            "#75ff92",
            "#ff7575",
            "#ff8c00"
        ],
        "label": {
            "color": "#20354a"
        }
    },
    "map": {
        "itemStyle": {
            "areaColor": "#555555",
            "borderColor": "#999999",
            "borderWidth": 0.5
        },
        "label": {
            "color": "#ffffff"
        },
        "emphasis": {
            "itemStyle": {
                "areaColor": "rgba(255,175,81,0.5)",
                "borderColor": "#ffaf51",
                "borderWidth": 1
            },
            "label": {
                "color": "#ffee51"
            }
        }
    },
    "geo": {
        "itemStyle": {
            "areaColor": "#555555",
            "borderColor": "#999999",
            "borderWidth": 0.5
        },
        "label": {
            "color": "#ffffff"
        },
        "emphasis": {
            "itemStyle": {
                "areaColor": "rgba(255,175,81,0.5)",
                "borderColor": "#ffaf51",
                "borderWidth": 1
            },
            "label": {
                "color": "#ffee51"
            }
        }
    },
    "categoryAxis": {
        "axisLine": {
            "show": true,
            "lineStyle": {
                "color": "#324e69"
            }
        },
        "axisTick": {
            "show": false,
            "lineStyle": {
                "color": "#333"
            }
        },
        "axisLabel": {
            "show": true,
            "color": "#eeeeee"
        },
        "splitLine": {
            "show": true,
            "lineStyle": {
                "color": [
                    "#20354a"
                ]
            }
        },
        "splitArea": {
            "show": false,
            "areaStyle": {
                "color": [
                    "rgba(250,250,250,0.05)",
                    "rgba(200,200,200,0.02)"
                ]
            }
        }
    },
    "valueAxis": {
        "axisLine": {
            "show": true,
            "lineStyle": {
                "color": "#324e69"
            }
        },
        "axisTick": {
            "show": false,
            "lineStyle": {
                "color": "#333"
            }
        },
        "axisLabel": {
            "show": true,
            "color": "#eeeeee"
        },
        "splitLine": {
            "show": true,
            "lineStyle": {
                "color": [
                    "#20354a"
                ]
            }
        },
        "splitArea": {
            "show": false,
            "areaStyle": {
                "color": [
                    "rgba(250,250,250,0.05)",
                    "rgba(200,200,200,0.02)"
                ]
            }
        }
    },
    "logAxis": {
        "axisLine": {
            "show": true,
            "lineStyle": {
                "color": "#324e69"
            }
        },
        "axisTick": {
            "show": false,
            "lineStyle": {
                "color": "#333"
            }
        },
        "axisLabel": {
            "show": true,
            "color": "#eeeeee"
        },
        "splitLine": {
            "show": true,
            "lineStyle": {
                "color": [
                    "#20354a"
                ]
            }
        },
        "splitArea": {
            "show": false,
            "areaStyle": {
                "color": [
                    "rgba(250,250,250,0.05)",
                    "rgba(200,200,200,0.02)"
                ]
            }
        }
    },
    "timeAxis": {
        "axisLine": {
            "show": true,
            "lineStyle": {
                "color": "#324e69"
            }
        },
        "axisTick": {
            "show": false,
            "lineStyle": {
                "color": "#333"
            }
        },
        "axisLabel": {
            "show": true,
            "color": "#eeeeee"
        },
        "splitLine": {
            "show": true,
            "lineStyle": {
                "color": [
                    "#20354a"
                ]
            }
        },
        "splitArea": {
            "show": false,
            "areaStyle": {
                "color": [
                    "rgba(250,250,250,0.05)",
                    "rgba(200,200,200,0.02)"
                ]
            }
        }
    },
    "toolbox": {
        "iconStyle": {
            "borderColor": "#999999"
        },
        "emphasis": {
            "iconStyle": {
                "borderColor": "#666666"
            }
        }
    },
    "legend": {
        "textStyle": {
            "color": "#b2b3b5"
        }
    },
    "tooltip": {
        "axisPointer": {
            "lineStyle": {
                "color": "#cccccc",
                "width": 1
            },
            "crossStyle": {
                "color": "#cccccc",
                "width": 1
            }
        }
    },
    "timeline": {
        "lineStyle": {
            "color": "#eeeeee",
            "width": 1
        },
        "itemStyle": {
            "color": "#8dc6ff",
            "borderWidth": 1
        },
        "controlStyle": {
            "color": "#eeeeee",
            "borderColor": "#eeeeee",
            "borderWidth": 0.5
        },
        "checkpointStyle": {
            "color": "#0081ff",
            "borderColor": "#87c7ff"
        },
        "label": {
            "color": "#eeeeee"
        },
        "emphasis": {
            "itemStyle": {
                "color": "#ffaf51"
            },
            "controlStyle": {
                "color": "#eeeeee",
                "borderColor": "#eeeeee",
                "borderWidth": 0.5
            },
            "label": {
                "color": "#eeeeee"
            }
        }
    },
    "visualMap": {
        "color": [
            "#ff715e",
            "#fff04b",
            "#10b981"
        ]
    },
    "dataZoom": {
        "backgroundColor": "rgba(255,255,255,0)",
        "dataBackgroundColor": "rgba(222,222,222,1)",
        "fillerColor": "rgba(255,113,94,0.2)",
        "handleColor": "#cccccc",
        "handleSize": "100%",
        "textStyle": {
            "color": "#999999"
        }
    },
    "markPoint": {
        "label": {
            "color": "#20354a"
        },
        "emphasis": {
            "label": {
                "color": "#20354a"
            }
        }
    }
};
