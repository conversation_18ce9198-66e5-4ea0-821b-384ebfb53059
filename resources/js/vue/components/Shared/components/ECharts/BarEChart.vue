<template>
    <v-chart
        :class="[height, width]"
        :option="chartOptions"
        :theme="darkMode ? 'a2dark' : 'a2light'"
        :autoresize="true" />
</template>

<script setup>
    import {use, registerTheme} from 'echarts/core';
    import {SVGRenderer} from "echarts/renderers";
    import VChart from "vue-echarts";
    import {BarChart} from "echarts/charts";
    import {TitleComponent, TooltipComponent, LegendComponent} from "echarts/components";
    import {ref} from "vue";
    import a2dark from './themes/a2dark';
    import a2light from './themes/a2light';

    const props = defineProps({
        darkMode: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            default: ''
        },
        legend: {
            type: Object,
            default: () => {
                return {
                    show: true
                };
            }
        },
        seriesData: {
            type: Array,
            default: () => []
        },
        xAxisName: {
            type: String,
            default: ''
        },
        xAxisData: {
            type: Array,
            default: () => []
        },
        alignWithLabel: {
            type: Boolean,
            default: true
        },
        yAxisName: {
            type: String,
            default: ''
        },
        yAxisMin: {
            type: Number,
            default: 0
        },
        yAxisMax: {
            type: Number,
            default: 100
        },
        height: {
            type: String,
            default: 'h-100'
        },
        width: {
            type: String,
            default: 'w-full'
        },
    });

    registerTheme('a2dark', a2dark);
    registerTheme('a2light', a2light);

    use([
        SVGRenderer,
        TitleComponent,
        TooltipComponent,
        LegendComponent,
        BarChart
    ]);

    const chartOptions = ref(Object.assign(
        {
            aria: {
                enabled: true
            },
            title: {
                text: props.title,
                left: "center"
            },
            tooltip: {
                trigger: "item"
            },
            legend: props.legend,
            xAxis: {
                type: 'category',
                data: props.xAxisData,
                name: props.xAxisName,
                nameTextStyle: {
                    padding: 20,
                    fontWeight: 'bold',
                    fontSize: 16
                },
                nameLocation: 'middle',
                axisTick: {
                    alignWithLabel: props.alignWithLabel
                }
            },
            yAxis: {
                type: 'value',
                name: props.yAxisName,
                nameLocation: 'middle',
                nameTextStyle: {
                    padding: 20,
                    fontWeight: 'bold',
                    fontSize: 16
                },
                min: props.yAxisMin,
                max: props.yAxisMax
            },
            series: props.seriesData.map((ds) => {
                return {
                    type: 'bar',
                    name: ds.name,
                    data: ds.data
                };
            })
        }
    ));
</script>
