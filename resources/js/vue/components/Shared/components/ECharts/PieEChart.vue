<template>
    <v-chart
        :class="[height, width]"
        :option="chartOptions"
        :theme="darkMode ? 'a2dark' : 'a2light'"
        :autoresize="true" />
</template>

<script setup>
    import {registerTheme, use} from 'echarts/core';
    import {SVGRenderer} from "echarts/renderers";
    import VChart from "vue-echarts";
    import {PieChart} from "echarts/charts";
    import {TitleComponent, TooltipComponent, LegendComponent} from "echarts/components";
    import {ref} from "vue";
    import a2dark from './themes/a2dark';
    import a2light from './themes/a2light';

    const props = defineProps({
        darkMode: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            default: ''
        },
        legend: {
            type: Object,
            default: () => {
                return {
                    show: true
                };
            }
        },
        seriesData: {
            type: Array,
            default: () => []
        },
        showLabelLine: {
            type: Boolean,
            default: false
        },
        showLabels: {
            type: Boolean,
            default: false
        },
        height: {
            type: String,
            default: 'h-100'
        },
        width: {
            type: String,
            default: 'w-full'
        },
    })

    registerTheme('a2dark', a2dark);
    registerTheme('a2light', a2light);

    use([
        SVGRenderer,
        TitleComponent,
        TooltipComponent,
        LegendComponent,
        PieChart
    ]);

    const chartOptions = ref(Object.assign(
        {
            aria: {
                enabled: true
            },
            title: {
                text: props.title,
                left: "center"
            },
            tooltip: {
                trigger: "item"
            },
            labelLine: {
                show: props.showLabelLine
            },
            legend: props.legend,
            series: props.seriesData.map((ds, idx) => {
                return {
                    name: ds.name,
                    type: 'pie',
                    radius: ds.radius,
                    center: ds.center,
                    data: ds.data.map((d) => {
                        return {
                            name: d.name,
                            value: d.value,
                            label: {
                                show: props.showLabels
                            },
                            itemStyle: {
                                opacity: (d.hidden || false) ? 0 : 1
                            }
                        };
                    })
                };
            })
        }
    ));
</script>
