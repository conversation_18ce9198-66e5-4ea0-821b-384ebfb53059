<template>
    <div class="relative">
        <span v-if="searchIcon">
            <svg class="absolute left-3 top-3 fill-current text-grey-400" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M5.66667 2C4.69421 2 3.76158 2.38631 3.07394 3.07394C2.38631 3.76158 2 4.69421 2 5.66667C2 6.14818 2.09484 6.62498 2.27911 7.06984C2.46338 7.5147 2.73346 7.91891 3.07394 8.25939C3.41442 8.59987 3.81863 8.86996 4.26349 9.05423C4.70835 9.23849 5.18515 9.33333 5.66667 9.33333C6.14818 9.33333 6.62498 9.23849 7.06984 9.05423C7.5147 8.86996 7.91891 8.59987 8.25939 8.25939C8.59987 7.91891 8.86996 7.5147 9.05423 7.06984C9.23849 6.62498 9.33333 6.14818 9.33333 5.66667C9.33333 4.69421 8.94703 3.76158 8.25939 3.07394C7.57176 2.38631 6.63913 2 5.66667 2ZM1.65973 1.65973C2.72243 0.597022 4.16377 0 5.66667 0C7.16956 0 8.6109 0.597022 9.6736 1.65973C10.7363 2.72243 11.3333 4.16377 11.3333 5.66667C11.3333 6.41082 11.1868 7.14769 10.902 7.83521C10.7458 8.21219 10.5498 8.57029 10.3178 8.90361L13.7071 12.2929C14.0976 12.6834 14.0976 13.3166 13.7071 13.7071C13.3166 14.0976 12.6834 14.0976 12.2929 13.7071L8.90361 10.3178C8.57029 10.5498 8.21219 10.7458 7.83521 10.902C7.14769 11.1868 6.41082 11.3333 5.66667 11.3333C4.92251 11.3333 4.18564 11.1868 3.49813 10.902C2.81062 10.6172 2.18593 10.1998 1.65973 9.6736C1.13353 9.14741 0.716126 8.52272 0.431349 7.83521C0.146573 7.1477 0 6.41082 0 5.66667C0 4.16377 0.597022 2.72243 1.65973 1.65973Z"/></svg>
        </span>
        <label :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" v-if="label" class="block mb-1" :for="label.toLowerCase()">
            {{ label }}
        </label>
        <textarea
            v-if="type === 'textarea'"
            :id="id"
            :disabled="disabled"
            class="rounded text-sm font-medium w-full border focus:outline-none outline-none focus:ring-0 focus:border-primary-500 focus:shadow-lg focus:shadow-primary-500/10"
            :class="[darkMode ? 'text-slate-100 bg-dark-background border-dark-border' : 'text-slate-900 bg-light-background border-light-border', disabled ? (darkMode ? '!bg-dark-module opacity-75 cursor-not-allowed pointer-events-none' : '!bg-light-module opacity-75 cursor-not-allowed pointer-events-none') : '',searchIcon ? 'pl-8' : '', inputClasses, textareaHeight]"
            :value="modelValue"
            @input="updateInput"
            :placeholder="placeholder ? placeholder : label"
            :autocomplete="autoComplete"
            :name="label || !name ? label.toLowerCase() : name"
        ></textarea>
        <input
            v-else
            :id="id"
            :disabled="disabled"
            class="rounded text-sm font-medium w-full h-9 border focus:outline-none outline-none focus:ring-0 focus:border-primary-500 focus:shadow-lg focus:shadow-primary-500/10"
            :class="[darkMode ? 'text-slate-100 bg-dark-background border-dark-border' : 'text-slate-900 bg-light-background border-light-border', disabled ? (darkMode ? '!bg-dark-module opacity-75 cursor-not-allowed pointer-events-none' : '!bg-light-module opacity-75 cursor-not-allowed pointer-events-none') : '',searchIcon ? 'pl-8' : ' px-2.5', inputClasses]"
            :type="type"
            :value="modelValue"
            @input="updateInput"
            :placeholder="placeholder ? placeholder : label"
            :autocomplete="autoComplete"
            :name="label || !name ? label.toLowerCase() : name"
            @keyup.enter="$emit('keyup.enter', event)"/>
    </div>
</template>

<script>
export default {
    name: 'CustomInput',
    emits: ['update:modelValue', 'input'],
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        type: {
            type: String,
            default: 'text',
        },
        label: {
            type: String,
            default: '',
        },
        modelValue: {
            type: [String, Number],
            default: "",
        },
        placeholder: {
            type: String,
            default: '',
        },
        autoComplete: {
            type: String,
            default: 'off',
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        searchIcon: {
            type: Boolean,
            default: false,
        },
        id: {
            type: String,
            required: false,
        },
        name: {
            type: String,
            required: false,
        },
        inputClasses: {
            type: String,
            default: ''
        },
        textareaHeight: {
            type: String,
            default: 'h-14'
        }
    },
    methods: {
        updateInput(event) {
            this.$emit("update:modelValue", event.target.value);
        }
    },
    computed: {
        model: {
            get() {
                return this.value;
            },
            set(value) {
                this.$emit('input', value);
            },
        },
    }
}
</script>

<style scoped>

</style>
