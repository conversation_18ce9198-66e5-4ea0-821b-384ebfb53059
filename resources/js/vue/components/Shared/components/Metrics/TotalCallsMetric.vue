<script setup>
import Metric from './Metric.vue';

const props = defineProps({
    darkMode: false,
});
</script>

<template>
    <Metric :dark-mode="darkMode" title="Calls" route="calls">
        <template #icon>
            <svg class="w-4 shrink-0" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M17.4873 14.1402L13.4223 10.4442C13.2301 10.2696 12.9776 10.1764 12.7181 10.1845C12.4585 10.1925 12.2123 10.3011 12.0313 10.4872L9.63828 12.9482C9.06228 12.8382 7.90428 12.4772 6.71228 11.2882C5.52028 10.0952 5.15928 8.93424 5.05228 8.36224L7.51128 5.96824C7.69769 5.78737 7.80642 5.54106 7.81444 5.28145C7.82247 5.02183 7.72917 4.76928 7.55428 4.57724L3.85928 0.513239C3.68432 0.320596 3.44116 0.203743 3.18143 0.187499C2.92171 0.171254 2.66588 0.256897 2.46828 0.426239L0.298282 2.28724C0.125393 2.46075 0.0222015 2.69169 0.00828196 2.93624C-0.00671804 3.18624 -0.292718 9.10824 4.29928 13.7022C8.30528 17.7072 13.3233 18.0002 14.7053 18.0002C14.9073 18.0002 15.0313 17.9942 15.0643 17.9922C15.3088 17.9786 15.5396 17.8749 15.7123 17.7012L17.5723 15.5302C17.7423 15.3333 17.8286 15.0776 17.8127 14.8179C17.7968 14.5582 17.68 14.315 17.4873 14.1402Z" fill="#0081FF"/></svg>
        </template>
    </Metric>
</template>
