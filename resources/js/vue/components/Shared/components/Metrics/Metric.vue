<script setup>
import { computed, ref, onMounted, watch } from 'vue';
import { store } from '../../../../../stores/metrics-store.js';
import axios from 'axios';
import LoadingSpinner from '../LoadingSpinner.vue'

const props = defineProps({
    darkMode: false,
    title: '',
    route: '',
    icon: '',
})

const themeBackgroundClasses = computed(() => {
    if(props.darkMode === true) {
        return 'bg-dark-background border-dark-border text-slate-50'
    }
    else {
        return 'bg-light-background border-light-border text-slate-900'
    }
})

const format = ref()
const current = ref(0)
const change = ref(0)
const percentage = ref(0)
const loading = ref(true)

const increased = computed(() => change.value > 0)
const metric = computed(() => formatted(current.value, format.value))
const difference = computed(() => formatted(Math.abs(change.value), format.value))

function formatted(value, type) {
    switch (type) {
        case 'seconds': {
            const hours = Math.floor(value / 3600)
            const minutes = Math.floor((value / 60) - (hours * 60))
            const seconds = Math.floor(value - (minutes * 60) - (hours * 3600))

            if (hours >= 1) {
                return hours + "h " +  minutes + "m " + seconds + "s"
            } else if (minutes >= 1) {
                return minutes + "m " + seconds + "s"
            } else {
                return seconds + "s"
            }
        }
        case 'money': {
            return "$ " + value
        }
        default: {
            return value
        }
    }
}

function load() {
    loading.value = true

    axios.get(store.route + props.route, {
        params: { 'range': store.range }
    }).then(response => {
        format.value = response.data.data.format
        current.value = response.data.data.current
        change.value = response.data.data.change
        percentage.value = response.data.data.percentage
    }).catch(error => {
        console.log(error)
    }).finally(() => {
        loading.value = false
    });
}

onMounted(() => load())

watch(() => store.range, (newValue, oldValue) => load());

</script>

<template>
    <LoadingSpinner v-if="loading"></LoadingSpinner>
    <div v-else class="p-5 border rounded-md flex flex-wrap gap-2" :class="[themeBackgroundClasses]">
        <div class="flex items-center justify-between w-full flex-wrap">
            <div class="flex items-center gap-2">
                <slot name="icon"></slot>
                <h3 class="text-base font-bold">{{ title }}</h3>
            </div>
        </div>
        <p class="inline-flex flex-wrap gap-1 text-xl font-bold gap-x-2 items-center">
            {{ metric }}
            <span v-if="change != null" class="font-bold text-sm" :class="[increased ? (darkMode ? 'text-emerald-400' : 'text-emerald-600') : (darkMode ? 'text-rose-400' : 'text-rose-600')]">
                <svg class="float-left mt-1 mr-1 fill-current flex-shrink-0" :class="[increased ? '' : 'transform rotate-180']" width="13" height="12" viewBox="0 0 13 12" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6.5 0L12.9952 11.25H0.00480938L6.5 0Z"/></svg>
                {{ difference }} ({{ percentage }}%)
            </span>
        </p>
    </div>
</template>

<style scoped>

</style>
