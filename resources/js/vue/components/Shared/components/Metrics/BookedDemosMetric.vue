<script setup>
import Metric from './Metric.vue';

const props = defineProps({
    darkMode: false,
});
</script>

<template>
    <Metric :dark-mode="darkMode" title="Companies In Progress" route="booked-demos">
        <template #icon>
            <svg class="w-4 shrink-0" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2.88286 7.54695C3.28248 6.59696 3.86329 5.73389 4.59286 5.00595C5.32082 4.27593 6.18431 3.69507 7.13486 3.29595C9.09435 2.48188 11.2947 2.46716 13.2649 3.25495C13.2846 3.90242 13.5559 4.5167 14.0212 4.96741C14.4865 5.41812 15.1091 5.66977 15.7569 5.66895C17.1429 5.66895 18.2569 4.55495 18.2569 3.16895C18.2569 1.78295 17.1429 0.668945 15.7569 0.668945C15.0679 0.668945 14.4449 0.944945 13.9939 1.39395C11.5629 0.420945 8.77086 0.435945 6.35886 1.45295C5.16886 1.95295 4.09886 2.67295 3.17886 3.59195C2.24993 4.52115 1.51363 5.62464 1.0122 6.83909C0.510766 8.05354 0.254077 9.35505 0.256858 10.6689H2.25686C2.25686 9.58295 2.46786 8.53295 2.88286 7.54695ZM17.6299 13.7909C17.2289 14.7429 16.6529 15.5989 15.9199 16.3319C15.1869 17.0649 14.3309 17.6409 13.3779 18.0419C11.4184 18.856 9.21806 18.8707 7.24786 18.0829C7.22859 17.4355 6.95757 16.821 6.49241 16.3703C6.02724 15.9195 5.40461 15.6679 4.75686 15.6689C3.37086 15.6689 2.25686 16.7829 2.25686 18.1689C2.25686 19.5549 3.37086 20.6689 4.75686 20.6689C5.44586 20.6689 6.06886 20.3929 6.51986 19.9439C7.70742 20.4233 8.9762 20.6695 10.2569 20.6689C12.2234 20.6727 14.1472 20.0956 15.787 19.0101C17.4267 17.9245 18.7093 16.3788 19.4739 14.5669C19.9933 13.3332 20.2596 12.0076 20.2569 10.6689H18.2569C18.2594 11.7411 18.0461 12.8029 17.6299 13.7909Z" fill="#0081FF"/><path d="M10.2568 6.13086C7.75475 6.13086 5.71875 8.16686 5.71875 10.6689C5.71875 13.1709 7.75475 15.2069 10.2568 15.2069C12.7588 15.2069 14.7948 13.1709 14.7948 10.6689C14.7948 8.16686 12.7588 6.13086 10.2568 6.13086Z" fill="#0081FF"/></svg>
        </template>
    </Metric>
</template>
