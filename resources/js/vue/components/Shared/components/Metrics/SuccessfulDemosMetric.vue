<script setup>
import Metric from './Metric.vue';

const props = defineProps({
    darkMode: false,
});
</script>

<template>
    <Metric :dark-mode="darkMode" title="Closed Companies" route="successful-demos">
        <template #icon>
            <svg class="w-4 shrink-0"  viewBox="0 0 17 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M1 0V18H0V20H4V18H3V13H16C16.2652 13 16.5196 12.8946 16.7071 12.7071C16.8946 12.5196 17 12.2652 17 12V2C17 1.73478 16.8946 1.48043 16.7071 1.29289C16.5196 1.10536 16.2652 1 16 1H3V0H1ZM5 3V5H7V3H9V5H11V3H13V5H15V7H13V9H15V11H13V9H11V11H9V9H7V11H5V9H3V7H5V5H3V3H5Z" fill="#0081FF"/><path d="M5 7H7V9H5V7ZM9 7H11V9H9V7ZM7 5H9V7H7V5ZM11 5H13V7H11V5Z" fill="#0081FF"/></svg>
        </template>
    </Metric>
</template>
