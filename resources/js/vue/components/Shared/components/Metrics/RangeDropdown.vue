<script setup>
import { ref, onMounted } from 'vue';
import { store } from '../../../../../stores/metrics-store.js';
import Dropdown from '../Dropdown.vue';
import axios from 'axios';

const props = defineProps({
    darkMode: false,
});

const loading = ref(true)
const ranges = ref()

function getRanges() {
    axios.get(store.route + 'ranges')
    .then(response => {
        ranges.value = Object.entries(response.data.data).map(([id, name]) => ({ id, name }));
    }).catch(error => {
        console.log(error)
    }).finally(() => {
        loading.value = false
    })
}

function updateRange (range) {
    store.updateRange(range);
}

onMounted(() => getRanges())

</script>

<template>
    <div class="flex flex-col items-center justify-center w-64">
        <p class="sr-only">Range</p>
        <Dropdown :darkMode="darkMode" :loading="loading" :options="ranges" v-model="store.range" @input="updateRange"></Dropdown>
    </div>
</template>
