<template>
    <modal
        :dark-mode="darkMode"
        no-buttons
        @close="handleClose"
        :restrict-width="false"
        :full-width="false"
    >
        <template v-slot:header>
            <span class="font-bold">Leads refund request</span>
        </template>
        <template v-slot:content>
            <loading-spinner v-if="loading"></loading-spinner>
            <div v-else class="flex flex-col gap-2">
                <div class="flex">
                    <simple-alert :variant="alert.variant" :content="alert.content"></simple-alert>
                </div>
                <lead-refund-items-table
                    :items="refundItemsData"
                    @update="handleUpdateList"
                >
                    <template v-slot:actions="{item}">
                        <confirm-delete-button @confirm="removeItem(item)">
                        </confirm-delete-button>
                    </template>
                </lead-refund-items-table>
                <div v-if="!refundRequested" class="flex gap-1 justify-end">
                    <custom-button
                        :disabled="isConfirmRefundDisabled"
                        :dark-mode="darkMode"
                        @click="handleConfirmRefund"
                    >
                        Confirm refund
                    </custom-button>
                </div>
            </div>
        </template>
    </modal>
</template>

<script>
import Modal from "../../../Shared/components/Modal.vue";
import SimpleTable from "../SimpleTable/SimpleTable.vue";
import CustomButton from "../CustomButton.vue";
import ButtonDropdown from "../ButtonDropdown.vue";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import ConfirmDeleteButton
    from "../../../IndustryManagement/WebsiteManagement/ApiKeys/components/ConfirmDeleteButton.vue";
import Alert from "../Alert.vue";
import LeadRefundItemsTable from "./components/LeadRefundItemsTable.vue";
import SimpleAlert, {SIMPLE_ALERT_VARIANTS} from "../SimpleAlert.vue";
import LoadingSpinner from "../LoadingSpinner.vue";
import ApiService from "../../../LeadRefunds/services/api.js";

export default {
    name: "RefundLeadRequestModal",
    components: {
        LoadingSpinner,
        SimpleAlert,
        LeadRefundItemsTable,
        Alert,
        ConfirmDeleteButton,
        SimpleIcon,
        ButtonDropdown,
        CustomButton,
        SimpleTable,
        Modal
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },

        productAssignmentIdsToRefund: {
            type: Array,
            required: false
        },
    },

    mounted() {
        this.getRefundItemsData()
    },

    computed: {
        isConfirmRefundDisabled() {
            return this.refundItemsData.length === 0 || this.loadingConfirmation || this.hasRefundItemWithNoReason
        },

        hasRefundItemWithNoReason() {
            return this.refundItemsData.filter(e => !e.refund_reason || e.refund_reason.length === 0).length > 0
        },
    },

    data() {
        return {
            loading: false,
            refundItemsData: [],
            loadingConfirmation: false,
            refundRequested: false,
            api: ApiService.make(),
            alert: {
                content: 'Please add a reason for each lead selected to place a refund',
                variant: SIMPLE_ALERT_VARIANTS.LIGHT_BLUE,
            }
        }
    },

    methods: {
        async getRefundItemsData() {
            this.loading = true;

            if (this.productAssignmentIdsToRefund?.length > 0) {
                await this.getProductAssignmentRefundData();
            }

            this.loading = false
        },
        async getProductAssignmentRefundData() {
            const res = await this.api.getLeadRefundData(this.productAssignmentIdsToRefund)

            this.refundItemsData = res.data.data.refund_data
        },
        async handleConfirmRefund() {
            this.loadingConfirmation = true

            await this.api.requestLeadsRefund({
                items: this.refundItemsData.map(e => ({
                    "product_assignment_id": e.product_assignment_id,
                    "refund_reason": e.refund_reason,
                    "refund_type": e.refund_type,
                    "cost": e.cost,
                }))
            })

            this.alert = {
                content: 'Refund has been requested successfully',
                variant: SIMPLE_ALERT_VARIANTS.LIGHT_GREEN
            }

            this.refundRequested = true
            this.loadingConfirmation = false
        },
        handleClose() {
            this.$emit('cancel')
        },
        handleUpdateList(list) {
            this.refundItemsData = list
        },
        removeItem(item) {
            this.refundItemsData = this.refundItemsData.filter(e => e.id !== item.id)
        }
    },
}
</script>
