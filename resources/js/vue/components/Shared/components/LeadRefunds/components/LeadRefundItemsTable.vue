<template>
    <div>
        <simple-table
            :dark-mode="darkMode"
            :data="items"
            :headers="headers"
            no-pagination
            has-row-click
            show-all-complements
            body-wrapper-classes="text-sm"
        >
            <template v-slot:row.col.invoice_id="{item}">
                <div class="flex flex-col gap-1">
                    <span>{{ item.invoice_id }}</span>
                    <version-badge :version="item.billing_version" class="w-fit"/>
                </div>
            </template>
            <template v-slot:row.col.id_legacy_id="{item}">
                <div class="flex flex-col">
                    <span>{{ item.id }}</span>
                    <p class="text-xs font-light">{{ item.legacy_id }}</p>
                </div>
            </template>
            <template v-slot:row.col.industry_service="{item}">
                <div class="flex flex-col">
                    <p>{{ item.industry }}</p>
                    <p class="text-xs">{{ item.service }}</p>
                </div>
            </template>
            <template v-slot:row.col.campaign="{item,value}">
                <div>
                    {{value}}
                </div>
            </template>
            <template v-slot:row.col.contact="{item}">
                <div class="flex flex-col">
                    <p class="font-semibold">{{ item.contact }}</p>
                    <p class="text-xs">{{ item.address }}</p>
                </div>
            </template>
            <template v-slot:row.col.cost="{value, item}">
                <div class="flex flex-col">
                    <p>${{ value }}</p>
                    <p class="text-xs font-semibold">{{ item.refund_type }}</p>
                </div>
            </template>
            <template v-slot:row.col.actions="{item, idx}">
                <slot name="actions" :item="item" :idx="idx"></slot>
            </template>
            <template v-slot:row.col.refund_charge_status="{value}">
                <LeadRefundItemRefundStatusBadge :status="value"></LeadRefundItemRefundStatusBadge>
            </template>
            <template v-slot:row.complement="{item}">
                <div class="flex flex-col">
                <textarea
                    :disabled="isReasonDisabled"
                    placeholder="Type a reason..."
                    @input="(event) => handleReasonUpdate(event.target.value, item)"
                    :value="item.refund_reason"
                    class="min-h-88 w-full border rounded px-4 py-2 focus:outline-none focus:border focus:border-primary-500"
                    type="text"
                    :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}"
                />
                </div>
            </template>
        </simple-table>
        <div class="mt-2">
            <div>Total: <span class="font-semibold">${{ total }}</span></div>
            <div>Number of leads: <span class="font-semibold">{{ items.length }}</span></div>
        </div>
    </div>
</template>

<script>
import SimpleTable from "../../SimpleTable/SimpleTable.vue";
import ConfirmDeleteButton
    from "../../../../IndustryManagement/WebsiteManagement/ApiKeys/components/ConfirmDeleteButton.vue";
import Dropdown from "../../Dropdown.vue";
import Badge from "../../Badge.vue";
import LeadRefundItemRefundStatusBadge from "../../../../LeadRefunds/components/LeadRefundItemRefundStatusBadge.vue";
import VersionBadge from "../../../../Billing/VersionBadge.vue";

export default {
    name: "LeadRefundItemsTable",
    components: {VersionBadge, LeadRefundItemRefundStatusBadge, Badge, Dropdown, ConfirmDeleteButton, SimpleTable},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },

        items: {
            type: Array,
            default: [],
            required: true
        },

        isReasonDisabled: {
            type: Boolean,
            default: false,
        },
    },

    computed: {
        total() {
            return this.items.reduce((prev, curr) => prev + curr.cost, 0)
        }
    },

    data() {
        const headers = [
            {title: 'Invoice Id', field: 'invoice_id'},
            {title: 'Id / Legacy Id', field: 'id_legacy_id'},
            {title: 'Industry / Service', field: 'industry_service', cols: 2},
            {title: 'Campaign', field: 'campaign', cols: 3},
            {title: 'Contact', field: 'contact', cols: 3},
            {title: 'Cost', field: 'cost'},
            {title: 'Refund Status', field: 'refund_charge_status', cols: 2}
        ]

        if (this.$slots['actions']) {
            headers.push({title: 'Actions', field: 'actions', cols: 2})
        }

        return {
            headers,
        }
    },

    methods: {
        handleReasonUpdate(reason, item) {
            const newList = this.items
            item.refund_reason = reason
            this.$emit('update', newList)
        },
    },
}
</script>
