<template>
    <div v-if="showPagination"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}"
         class="flex items-center justify-between">
        <div class="flex-1 flex justify-between sm:hidden">
            <div v-if="showPreviousButton" @click="handleChangePage(currentPage - 1)"
                 class="relative inline-flex items-center px-4 py-2 border-y border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-light-module hover:bg-gray-50">
                Previous
            </div>
            <div v-if="showNextButton" @click="handleChangePage(currentPage + 1)"
                 class="ml-3 relative inline-flex items-center px-4 py-2 border-y border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-light-module hover:bg-gray-50">
                Next
            </div>
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
                <p v-if="showTotalRecordsDetail" class="text-sm mr-3 text-slate-500">
                    Showing
                    <span class="font-medium">{{ from }}</span>
                    to
                    <span class="font-medium">{{ to }}</span>
                    of
                    <span class="font-medium">{{ total }}</span>
                    results
                </p>
                <p v-else class="text-sm" :class="{'text-grey-800': !darkMode, 'text-grey-200': darkMode}">
                    Total: <span class="font-medium">{{ total }}</span>
                </p>
            </div>

            <div v-if="lastPage <= pageLinksToDisplay">
                <nav class="relative z-0 cursor-pointer inline-flex divide-x rounded border overflow-hidden" :class="[darkMode ? 'border-dark-border divide-dark-border' : 'border-light-border divide-light-border']" aria-label="Pagination">
                    <div v-for="page in lastPage"
                         @click="handleChangePage(page)"
                         class="relative inline-flex items-center px-4 py-2 text-sm font-medium"
                         :class="[page === currentPage ?? 'z-10 text-primary-500', (darkMode ? 'hover:bg-primary-800' : 'hover:bg-primary-100')]">
                        <p v-html="page"
                           class="pb-0 elect-none"
                           :class="{'text-primary-500': page === currentPage}">
                        </p>
                    </div>
                </nav>
            </div>
            <div v-else class="flex flex-row gap-2 items-center justify-center text-slate-500 text-sm">
                Page
                <dropdown
                    :dark-mode="darkMode"
                    @input="handleChangePage"
                    :options="pageDropdownOptions"
                    :model-value="currentPage"
                />
            </div>
        </div>
    </div>
</template>

<script>
    import Dropdown from "./Dropdown.vue";

    export default {
        components: {
            Dropdown
        },
        props: {
            currentPage: {
                type: Number,
                default: 1
            },
            perPage: {
                type: Number,
                default: 10
            },
            from: {
                type: Number,
                default: 0
            },
            to: {
                type: Number,
                default: 0
            },
            total: {
                type: Number,
                default: 0
            },
            lastPage: {
                type: Number,
                default: 1
            },
            showPagination: {
                type: Boolean,
                default: true
            },
            darkMode: {
                type: Boolean,
                default: false,
            },
            showTotalRecordsDetail: {
                type: Boolean,
                default: true
            },
            pageLinksToDisplay: {
                type: Number,
                default: 10
            }
        },
        data() {
            return {

            }
        },
        computed: {
            showPreviousButton() {
                return this.currentPage !== 1;
            },
            showNextButton() {
                return this.currentPage !== this.lastPage;
            },
            pageDropdownOptions() {
                let options = [];
                for(let i = 1; i <= this.lastPage; i++) {
                    options.push({
                        id: i,
                        name: i
                    });
                }

                return options;
            }
        },
        methods: {
            handleChangePage: function(newPage) {
                this.$emit('change-page', {
                    perPage: this.perPage,
                    newPage: newPage
                });
            },
        }
    };
</script>
