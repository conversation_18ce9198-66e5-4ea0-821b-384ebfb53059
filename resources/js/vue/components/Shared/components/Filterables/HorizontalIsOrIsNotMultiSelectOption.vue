<template>
    <div class="mb-10" :class="[false ? 'opacity-25 pointer-events-none' : '']">
        <div class="flex items-center space-x-4 mb-2">
            <label class="block text-sm font-semibold">{{ name }}</label>
            <p
                class="text-sm font-medium text-slate-500 opacity-50 cursor-default">
                Reset</p>
        </div>
        <div class="flex items-center space-x-6">
            <toggle-switch v-model="active" :dark-mode="darkMode"></toggle-switch>
            <div class="grid grid-cols-12 gap-3 w-full items-center"
                 :class="[active ? '' : 'opacity-25 pointer-events-none']">
                <Dropdown :options="isOrIsNotOptions"
                          :maxHeight="'max-h-[8rem]'"
                          :selected="isOrIsNot" v-model="isOrIsNot"
                          :dark-mode="darkMode"
                          class="col-span-2"></Dropdown>
                <MultiSelect :options="options" :dark-mode="darkMode"
                             :selected-ids="selectedOptions"
                             :text-place-holder="'Select Statuses'"
                             class="col-span-2"></MultiSelect>
            </div>
        </div>
    </div>
</template>
<script setup>
import Dropdown from '../Dropdown.vue'
import ToggleSwitch from '../ToggleSwitch.vue'
import { computed, onMounted, ref, watch } from 'vue'
import MultiSelect from '../MultiSelect.vue'

const props = defineProps({
    darkMode: {
        type: Boolean,
        default: false,
    },
    filterData: {
        type: Object,
        default: {}
    },
    modelValue: {
        type: Object,
        default: {},
    },
    name: {
        type: String,
        default: '',
    },
})

const disabled = computed(() => {
    return false
})

const isOrIsNotOptions = computed(() => {
    return [
        { id: 'is', name: 'is' },
        { id: 'isNot', name: 'is not' },
    ]
})

const active = ref(false)
const isOrIsNot = ref('is')
const selectedOptions = ref([])

const options = computed(() => {
    return props.filterData?.options
})

const form = computed(() => {
    return {
        active: active,
        is_or_is_not: isOrIsNot.value,
        selected_options: selectedOptions,
    }
})

onMounted(() => {
    active.value = props.filterData?.data?.active ?? false
    isOrIsNot.value = props.filterData?.data?.is_or_is_not ?? 'is'
    selectedOptions.value = props.filterData?.data?.selected_options ?? []
})

const emit = defineEmits(['update:filterOptions'])

watch(form, (to) => {
    emit('update:filterOptions', {
        form: to,
        id: props.filterData?.id,
    })
}, {
    deep: true,
})
</script>
