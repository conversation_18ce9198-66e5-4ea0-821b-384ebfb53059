<template>
    <div class="flex items-center gap-2">
        <div
            v-for="activeFilter in cleanedFilters"
            class="px-3 py-1 rounded-md inline-flex items-center justify-between max-w-sm cursor-pointer"
            :class="[darkMode ? 'bg-primary-700 bg-opacity-50 text-slate-200' : pillStyle]"
            @click="handleClick(activeFilter)"
        >
            <p ref="title" class="text-sm font-semibold">
                {{ activeFilter.name }} - {{ activeFilter.value }}
            </p>
            <svg v-if="activeFilter.clearable" class="ml-3 mr-1 fill-current flex-shrink-0" width="8" height="8" viewBox="0 0 8 8" fill="none"
                 xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                      d="M0.194995 0.203526C0.319888 0.0785449 0.489256 0.00833454 0.665854 0.00833454C0.842453 0.00833454 1.01182 0.0785449 1.13671 0.203526L3.99584 3.06553L6.85496 0.203526C6.9164 0.139852 6.98988 0.0890639 7.07114 0.0541246C7.15239 0.0191852 7.23979 0.000794382 7.32822 2.51714e-05C7.41665 -0.00074404 7.50434 0.0161236 7.58619 0.0496442C7.66804 0.0831648 7.7424 0.132667 7.80493 0.195262C7.86747 0.257857 7.91692 0.332292 7.9504 0.414223C7.98389 0.496154 8.00074 0.58394 7.99997 0.67246C7.99921 0.760979 7.98083 0.848459 7.94593 0.929795C7.91103 1.01113 7.86029 1.08469 7.79668 1.14619L4.93756 4.00819L7.79668 6.87019C7.918 6.99593 7.98512 7.16433 7.98361 7.33913C7.98209 7.51392 7.91205 7.68113 7.78857 7.80474C7.66508 7.92834 7.49804 7.99846 7.32342 7.99997C7.1488 8.00149 6.98057 7.9343 6.85496 7.81286L3.99584 4.95086L1.13671 7.81286C1.01111 7.9343 0.842873 8.00149 0.668251 7.99997C0.493629 7.99846 0.326589 7.92834 0.203108 7.80474C0.0796264 7.68113 0.00958406 7.51392 0.00806664 7.33913C0.00654922 7.16433 0.0736782 6.99593 0.194995 6.87019L3.05412 4.00819L0.194995 1.14619C0.0701398 1.02117 0 0.851635 0 0.674859C0 0.498083 0.0701398 0.328544 0.194995 0.203526Z"/>
            </svg>
        </div>
    </div>
</template>

<script>
import { defineComponent } from 'vue';
import { DateTime } from 'luxon';
import {camelCaseConversion} from "../../../../../modules/helpers";

export default defineComponent({
    name: "FilterableActivePills",
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        filters: {
            type: Array,
            default: [],
        },
        activeFilters: {
            type: Object,
            default: {},
        },
        pillStyle: {
            type: String,
            default: 'bg-primary-50 text-slate-900'
        }
    },
    emits: ['resetFilter'],
    data() {
        return {
            cleanedFilters: [],
            typeIs: {}
        }
    },
    created(){
        this.registerTypeFunctions()
        if (this.activeFilters) {
            this.cleanFilters();
        }
    },
    methods: {
        registerTypeFunctions(){
            this.typeIs = {
                input: (targetFilter, input, output, id) => {
                    return input != null
                        ? [...output,
                            {
                                name: targetFilter.name,
                                value: input,
                                id,
                                clearable: targetFilter.clearable ?? true
                            }]
                        : output
                },
                select: (targetFilter, input, output, id) => {
                    return input != null
                        ? [...output,
                            {
                                name: targetFilter.name,
                                value: this.getSelectOptionLabels(targetFilter.options, input)[0],
                                id,
                                clearable: targetFilter.clearable ?? true
                            }]
                        : output
                },
                multiSelect: (targetFilter, input, output, id) => {
                    return input?.length
                        ? [...output,
                            {
                                name: targetFilter.name,
                                value: this.getSelectOptionLabels(targetFilter.options, input).join(', '),
                                id,
                                clearable: targetFilter.clearable ?? true
                            }]
                        : output
                },
                dateRange: (targetFilter, input, output, id) => {
                    const value = input?.preset
                        ? input.preset
                        : input?.from
                            ? input?.to
                                ? `from ${this.$filters.dateFromTimestamp(input.from)} to ${this.$filters.dateFromTimestamp(input.to)}`
                                : `from ${this.$filters.dateFromTimestamp(input.from)}`
                            : null

                    return value
                        ? [...output, { name: targetFilter.name, clearable: targetFilter.clearable ?? true, id, value }]
                        : output
                },
                dualOperator: (targetFilter, input, output, id) => {
                    const getDualOperatorString = (input) => {
                        const firstOperator = input?.first_operator === 'greaterThanOrEqualTo' ? '>=' : input?.first_operator === 'lessThanOrEqualTo' ? '<=' : '???'

                        const secondOperator = input?.second_operator === 'greaterThanOrEqualTo' ? '>=' : input?.second_operator === 'lessThanOrEqualTo' ? '<=' : '???'

                        const firstInput = input?.first_input

                        const secondInput = input?.second_input

                        const logical = input?.logical === 'and' ? 'and' : input?.logical === 'or' ? 'or' : '???'

                        let value = ``

                        if (firstInput && firstOperator) {
                            value += `${firstOperator} ${firstInput}`

                            if (['and', 'or'].includes(logical)) {
                                if (secondInput && secondOperator) {
                                    value += ` ${logical} ${secondOperator} ${secondInput}`
                                }
                            }
                        }

                        return value
                    }

                    const value = getDualOperatorString(input)

                    return value
                        ? [...output, { name: targetFilter.name, clearable: targetFilter.clearable ?? true, id, value }]
                        : output
                },
                dualOperatorWithTimeframe: (targetFilter, input, output, id) => {
                    const firstOperator = input?.first_operator === 'greaterThan' ? 'greater than' : input?.first_operator === 'lessThan' ? 'less than' : input?.first_operator === 'equalTo' ? 'equal to' : '???'

                    const secondOperator = input?.second_operator === 'greaterThan' ? 'greater than' : input?.second_operator === 'lessThan' ? 'less than' : input?.second_operator === 'equalTo' ? 'equal to' : '???'

                    let USDollar = new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: 'USD',
                    })

                    const firstInput = USDollar.format(input?.first_input)

                    const secondInput = USDollar.format(input?.second_input)

                    const logical = input?.logical === 'and' ? 'and' : input?.logical === 'or' ? 'or' : '???'

                    const firstTimeframeActive = input?.first_timeframe_active

                    const secondTimeframeActive = input?.second_timeframe_active

                    const firstFromDate = firstTimeframeActive && input?.first_from_date ? DateTime.fromISO(input?.first_from_date).toLocaleString(DateTime.DATE_FULL) : null

                    const firstToDate = firstTimeframeActive && input?.first_to_date ? DateTime.fromISO(input?.first_to_date).toLocaleString(DateTime.DATE_FULL) : null

                    const secondFromDate = secondTimeframeActive && input?.second_from_date ? DateTime.fromISO(input?.second_from_date).toLocaleString(DateTime.DATE_FULL) : null

                    const secondToDate = secondTimeframeActive && input?.second_to_date ? DateTime.fromISO(input?.second_to_date).toLocaleString(DateTime.DATE_FULL) : null

                    const onlyFirstTimeframeIsActive = firstTimeframeActive && !secondTimeframeActive

                    const onlySecondTimeframeIsActive = secondTimeframeActive && !firstTimeframeActive

                    const bothTimeframesAreActive = firstTimeframeActive && secondTimeframeActive

                    let value = ``

                    if (firstInput && firstOperator !== '???') {
                        value += `${firstOperator} ${firstInput}`

                        if (firstFromDate && firstToDate && bothTimeframesAreActive) {
                            value += ` from ${firstFromDate} to ${firstToDate}`
                        }

                        if (['and', 'or'].includes(logical)) {
                            if (secondInput && secondOperator !== '???') {
                                value += ` ${logical} ${secondOperator} ${secondInput}`
                            }

                            if (secondFromDate && secondToDate && bothTimeframesAreActive) {
                                value += ` from ${secondFromDate} to ${secondToDate}`
                            }
                        }

                        if ((firstFromDate && firstToDate) && (onlyFirstTimeframeIsActive)) {
                            value += ` from ${firstFromDate} to ${firstToDate}`
                        }

                        return value
                            ? [...output, {
                                name: targetFilter.name,
                                clearable: targetFilter.clearable ?? true,
                                id,
                                value
                            }]
                            : output
                    }

                    return value
                        ? [...output, { name: targetFilter.name, clearable: targetFilter.clearable ?? true, id, value }]
                        : output
                },
                checkboxListDualOperator: (targetFilter, input, output, id) => {
                    let value = ``

                    const inputOptions = Object.values(input ?? {})

                    inputOptions?.filter(x => x?.selected)?.forEach(function (option, key) {
                        let constant = `${option.label} Selected`

                        if (option.hasOwnProperty('operator') && option.hasOwnProperty('value')) {
                            const operator = option?.operator === 'greaterThanOrEqualTo' ? '>=' : option?.operator === 'lessThanOrEqualTo' ? '<=' : '???'

                            constant = `${operator ?? '???'} ${option?.value} (${option?.label})`
                        }

                        if (key === 0) {
                            value += constant
                        } else {
                            value += ` AND ${constant})`
                        }
                    })

                    return value
                        ? [...output, { name: targetFilter.name, clearable: targetFilter.clearable ?? true, id, value }]
                        : output
                },
                multiSelectWithLogical: (targetFilter, input, output, id) => {
                    const getValue = () => {
                        const selectedLogical = input?.logical ?? 'and'
                        let selectedOptions = Array.isArray(input?.options) && selectedLogical ? input?.options : []

                        const getKeyByValue = (object, value) => {
                            return Object.keys(object).find(key => object[key] === value)
                        }

                        selectedOptions = selectedOptions.map(function (selectedOption) {
                            return getKeyByValue(targetFilter?.options?.options, selectedOption)
                        })

                        let value

                        if (selectedOptions?.length === 1) {
                            value = `${selectedOptions[0]}`
                        } else if (selectedOptions?.length === 2) {
                            value = `${selectedOptions[0]} ${selectedLogical} ${selectedOptions[1]}`
                        } else if (selectedOptions?.length > 2) {
                            let result = ``

                            const totalLength = selectedOptions?.length

                            selectedOptions.forEach(function (selectedOption, index) {
                                if (totalLength - 1 !== index) {
                                    result += `${selectedOption}, `
                                } else {
                                    result += `${selectedLogical} ${selectedOption}`
                                }
                            })

                            value = result
                        } else {
                            value = null
                        }

                        return value
                    }

                    const value = getValue()

                    return value
                        ? [...output, { name: targetFilter.name, clearable: targetFilter.clearable ?? true, id, value }]
                        : output
                },
                multiSelectWithChildren: (targetFilter, input, output, id) => {
                    if (input === null) return output;
                    if (!input?.[targetFilter.id]?.length && !targetFilter.bypassParentCheckForActiveFilterDisplay) return output;

                    let value = null, name = targetFilter.name;

                    const getComponentName = (componentId) => {
                        if (componentId === targetFilter.id) return targetFilter.name;
                        else {
                            const target = Object.values(targetFilter.children)?.find(child => child.id === componentId);
                            return target.name ?? '';
                        }
                    }

                    if (targetFilter.filterPillDisplayLastChild) {
                        let targetId, targetValues;
                        for (const component in input) {
                            if (input[component]?.length) {
                                targetId = component;
                                targetValues = input[component];
                            }
                        }
                        if (targetId) {
                            name = getComponentName(targetId);
                            value = targetFilter.id === targetId
                                ? this.getSelectOptionLabels(targetFilter.options, targetValues).join(', ')
                                : this.getSelectOptionLabels(targetFilter.children[targetId]?.options, targetValues).join(', ');
                        }
                    }
                    else {
                        const parentValues = this.getSelectOptionLabels(targetFilter.options, input[targetFilter.id]).join(', ');
                        const childValues = Object.entries(input).reduce((outputString, [childId, childValues]) => {
                            if (childId === targetFilter.id) return outputString;

                            const childFilterData = targetFilter.children[childId]

                            if ((childFilterData && (Object.values(input[childId] ?? []).length > 0))) {
                                const camelCaseType = camelCaseConversion(childFilterData.type)
                                const {0: result} = this.typeIs[camelCaseType](childFilterData, input[childId], '', childId)
                                if (result) return outputString + `; ${result.name} - ${result.value}`
                            }

                            return outputString;
                        }, '');

                        value = `${parentValues}${childValues}`;
                    }

                    return value
                        ? [...output, { name: name, clearable: targetFilter.clearable ?? true, id, value }]
                        : output;
                }
            }
        },
        cleanFilters() {
            this.cleanedFilters = Object.entries(this.activeFilters).reduce((output, [id, input]) => {
                const targetFilter = this.filters.find(filter => filter.id === id)
                if (!targetFilter) return output

                switch (targetFilter.type) {
                    case 'input':
                        return this.typeIs.input(targetFilter, input, output, id)
                    case 'select':
                        return this.typeIs.select(targetFilter, input, output, id)
                    case 'multi-select':
                        return this.typeIs.multiSelect(targetFilter, input, output, id)
                    case 'date-range':
                        return this.typeIs.dateRange(targetFilter, input, output, id)
                    case 'dual-operator':
                        return this.typeIs.dualOperator(targetFilter, input, output, id)
                    case 'dual-operator-with-timeframe':
                        return this.typeIs.dualOperatorWithTimeframe(targetFilter, input, output, id)
                    case 'checkbox-list-dual-operator':
                        return this.typeIs.checkboxListDualOperator(targetFilter, input, output, id)
                    case 'multi-select-with-logical':
                        return this.typeIs.multiSelectWithLogical(targetFilter, input, output, id)
                    case 'multi-select-with-children':
                        return this.typeIs.multiSelectWithChildren(targetFilter, input, output, id);
                    default:
                        const value = input?.preset

                        return value
                            ? [...output, {
                                name: targetFilter.name,
                                clearable: targetFilter.clearable ?? true,
                                id,
                                value
                            }]
                            : output
                }
            }, [])
        },
        getSelectOptionLabels(optionsArray, values) {
            values = Array.isArray(values) ? values : [values];
            const output = [];
            for (const option in optionsArray) {
                if (output.length >= values.length) break;
                if (values.includes(optionsArray[option])) output.push(option);
            }

            return output;
        },
        handleClick(filter) {
            if (filter.clearable) {
                this.$emit('resetFilter', filter.id);
            }
        },
    },
    watch: {
        activeFilters: {
            handler() {
                this.cleanFilters();
            },
            deep: true,
        }
    },
})
</script>
