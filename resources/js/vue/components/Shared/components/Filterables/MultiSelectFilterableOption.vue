<template>
    <div v-show="active">
        <div class="min-h-48 h-64 overflow-auto text-sm font-medium pb-2">
            <p v-for="[name, value] in Object.entries(filterData.options)" :key="value"
                @click="selectOption(value)"
                class="capitalize px-3 py-2 flex items-center justify-between cursor-pointer"
                :class="[selectedOptions.includes(value)
                    ? darkMode ? 'text-primary-500 bg-slate-700 bg-opacity-50 font-semibold' : 'text-primary-500 bg-primary-50 font-semibold'
                    : darkMode ? 'bg-dark-module hover:bg-dark-background' : 'bg-light-module hover:bg-light-background'
                ]">
                    {{ name }}
            </p>
        </div>
    </div>
</template>

<script>
import { defineComponent } from 'vue'
import CustomButton from "../CustomButton.vue";

export default defineComponent({
    name: "MultiSelectFilterableOption",
    components: { CustomButton },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        active: {
            type: Boolean,
            default: false,
        },
        filterData: {
            type: Object,
            default: {},
        },
        modelValue: {
            type: Array,
            default: null,
        }
    },
    emits: ['update:modelValue'],
    data() {
        return {
            selectedOptions: [],
        }
    },
    methods: {
        selectOption(optionValue) {
            if (this.selectedOptions.includes(optionValue)) {
                this.selectedOptions = this.selectedOptions.filter(v => v !== optionValue);
            }
            else {
                this.selectedOptions.push(optionValue);
            }
            this.$emit('update:modelValue', this.selectedOptions);
        },
    },
    watch: {
        active(newVal) {
            if (newVal) this.selectedOptions = this.modelValue ?? [];
        }
    }
});

</script>
