<template>
    <div v-if="active">
        <div class="min-h-48 h-64 overflow-auto text-sm font-medium pb-2">
            <div v-for="[name, value] in Object.entries(filterData.options)" :key="value"
                 @mouseenter="updateHoverTarget(value)"
                 @mouseleave="updateHoverTarget(null)"
                 @click="selectOption(value)"
                 class="capitalize px-3 py-2 flex items-center justify-between cursor-pointer"
                 :class="[darkMode ? 'bg-dark-module hover:bg-dark-background' : 'bg-light-module hover:bg-light-background']"
            >
                <div class="w-full"
                >
                    {{ name }}
                </div>
                <div
                    v-if="hoverTarget === value"
                    @click.stop.prevent="deleteOption(value)"
                    class="mr-0 ml-auto text-blue-500 hover:text-blue-300 px-2"
                >
                    <svg class="w-3" viewBox="0 0 9 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1.5 2.75H1V9.9C1 10.1917 1.10536 10.4715 1.29289 10.6778C1.48043 10.8841 1.73478 11 2 11H7C7.26522 11 7.51957 10.8841 7.70711 10.6778C7.89464 10.4715 8 10.1917 8 9.9V2.75H1.5ZM6.809 1.1L6 0H3L2.191 1.1H0V2.2H9V1.1H6.809Z" fill="currentColor"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { defineComponent } from 'vue'
import CustomButton from "../CustomButton.vue";

export default defineComponent({
    name: "CustomPresetFilterableOption",
    components: { CustomButton },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        active: {
            type: Boolean,
            default: false,
        },
        filterData: {
            type: Object,
            default: {},
        },
        modelValue: {
            type: [String, Number, Boolean],
            default: null,
        },
    },
    emits: ['update:modelValue', 'closeFilters', 'custom:deleteOption'],
    data() {
        return {
            hoverTarget: null,
        }
    },
    methods: {
        selectOption(optionValue) {
            this.$emit('update:modelValue', optionValue);
            this.$emit('closeFilters');
        },
        deleteOption(optionValue) {
            this.$emit('custom:deleteOption', optionValue);
        },
        updateHoverTarget(id) {
            this.hoverTarget = id;
        }
    },
});

</script>
