<template>
    <div v-if="active" class="min-h-48 p-2">
        <div>
            <field-label v-if="filterData.input_label" :dark-mode="darkMode"
                class="mb-3 mt-1"
            >
                {{ filterData.input_label }}
            </field-label>
            <text-field
                @update:model-value="v => $emit('update:modelValue', v.length === 0 ? undefined : v)"
                :dark-mode="darkMode"
                :model-value="modelValue"
                :placeholder="filterData.input_placeholder"
                :type="filterData?.input_type ?? 'text'"
            ></text-field>
        </div>
    </div>
</template>

<script>
import {defineComponent} from 'vue'
import CustomButton from "../CustomButton.vue";
import TextField from "../../../IndustryManagement/WebsiteManagement/ApiKeys/components/TextField.vue";
import FieldLabel from "../FieldLabel.vue";

export default defineComponent({
    name: "InputFilterableOption",
    components: {FieldLabel, TextField, CustomButton},
    props: {
        darkMode: {
            type: <PERSON>olean,
            default: false,
        },
        active: {
            type: Boolean,
            default: false,
        },
        filterData: {
            type: Object,
            default: {},
        },
        modelValue: {
            type: String,
            default: null,
        }
    },
    emits: ['update:modelValue'],
});

</script>
