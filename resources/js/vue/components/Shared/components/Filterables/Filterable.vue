<template>
    <div class="relative">
        <CustomButton ref="btn" id="filters-button" :dark-mode="darkMode" @click="toggleFiltersPopup" :color="buttonColor"
            :class="[filters.length || customCategories?.length ? '' : 'pointer-events-none opacity-50']"
        >
            <svg class="fill-current mr-2" width="15" height="14" viewBox="0 0 15 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M15 2.33333C15 2.76289 14.6269 3.11111 14.1667 3.11111L6.52369 3.11111C6.40056 3.43615 6.2006 3.73482 5.93443 3.98325C5.46559 4.42083 4.82971 4.66667 4.16667 4.66667C3.50363 4.66667 2.86774 4.42083 2.3989 3.98325C2.13273 3.73482 1.93278 3.43615 1.80964 3.11111L0.833335 3.11111C0.373098 3.11111 1.30974e-06 2.76289 1.32852e-06 2.33333C1.34729e-06 1.90378 0.373098 1.55556 0.833335 1.55556L1.80964 1.55556C1.93278 1.23052 2.13273 0.931843 2.3989 0.683417C2.86774 0.245832 3.50363 -5.02522e-07 4.16667 -4.7354e-07C4.82971 -4.44558e-07 5.46559 0.245832 5.93443 0.683417C6.2006 0.931843 6.40056 1.23052 6.52369 1.55556L14.1667 1.55556C14.6269 1.55556 15 1.90378 15 2.33333ZM15 7C15 7.42956 14.6269 7.77778 14.1667 7.77778L13.1904 7.77778C13.0672 8.10281 12.8673 8.40149 12.6011 8.64992C12.1323 9.0875 11.4964 9.33333 10.8333 9.33333C10.1703 9.33333 9.53441 9.0875 9.06557 8.64992C8.7994 8.40149 8.59944 8.10281 8.47631 7.77778L0.833334 7.77778C0.373098 7.77778 1.10576e-06 7.42956 1.12453e-06 7C1.14331e-06 6.57045 0.373098 6.22222 0.833334 6.22222L8.47631 6.22222C8.59944 5.89719 8.7994 5.59851 9.06557 5.35008C9.53441 4.9125 10.1703 4.66667 10.8333 4.66667C11.4964 4.66667 12.1323 4.9125 12.6011 5.35009C12.8673 5.59851 13.0672 5.89719 13.1904 6.22222L14.1667 6.22222C14.6269 6.22222 15 6.57045 15 7ZM15 11.6667C15 12.0962 14.6269 12.4444 14.1667 12.4444L6.52369 12.4444C6.40056 12.7695 6.2006 13.0682 5.93443 13.3166C5.46559 13.7542 4.82971 14 4.16667 14C3.50363 14 2.86774 13.7542 2.3989 13.3166C2.13273 13.0682 1.93278 12.7695 1.80964 12.4444L0.833334 12.4444C0.373097 12.4444 9.01769e-07 12.0962 9.20545e-07 11.6667C9.39322e-07 11.2371 0.373097 10.8889 0.833334 10.8889L1.80964 10.8889C1.93278 10.5639 2.13273 10.2652 2.3989 10.0168C2.86774 9.57917 3.50363 9.33333 4.16667 9.33333C4.82971 9.33333 5.46559 9.57917 5.93443 10.0168C6.2006 10.2652 6.40056 10.5639 6.52369 10.8889L14.1667 10.8889C14.6269 10.8889 15 11.2371 15 11.6667ZM11.6667 7C11.6667 6.79372 11.5789 6.59589 11.4226 6.45003C11.2663 6.30417 11.0543 6.22222 10.8333 6.22222C10.6123 6.22222 10.4004 6.30417 10.2441 6.45003C10.0878 6.59589 10 6.79372 10 7C10 7.20628 10.0878 7.40411 10.2441 7.54997C10.4004 7.69583 10.6123 7.77778 10.8333 7.77778C11.0543 7.77778 11.2663 7.69583 11.4226 7.54997C11.5789 7.40411 11.6667 7.20628 11.6667 7ZM5 2.33333C5 2.12705 4.9122 1.92922 4.75592 1.78336C4.59964 1.6375 4.38768 1.55556 4.16667 1.55556C3.94565 1.55556 3.73369 1.6375 3.57741 1.78336C3.42113 1.92922 3.33333 2.12705 3.33333 2.33333C3.33333 2.53961 3.42113 2.73744 3.57741 2.88331C3.73369 3.02917 3.94565 3.11111 4.16667 3.11111C4.38768 3.11111 4.59964 3.02917 4.75592 2.88331C4.9122 2.73744 5 2.53961 5 2.33333ZM5 11.6667C5 11.4604 4.9122 11.2626 4.75592 11.1167C4.59964 10.9708 4.38768 10.8889 4.16667 10.8889C3.94565 10.8889 3.73369 10.9708 3.57741 11.1167C3.42113 11.2626 3.33333 11.4604 3.33333 11.6667C3.33333 11.8729 3.42113 12.0708 3.57741 12.2166C3.73369 12.3625 3.94565 12.4444 4.16667 12.4444C4.38768 12.4444 4.59964 12.3625 4.75592 12.2166C4.9122 12.0708 5 11.8729 5 11.6667Z"/></svg>
            Filters
            <slot name="name"></slot>
        </CustomButton>
        <div v-if="filtersPopup" @click="toggleFiltersPopup" class="fixed inset-0 z-0" id="filterable-overlay"></div>
        <teleport to="#admin-app">
            <div ref="mainmenu" :style="calculatedPosition">
                <div v-show="filtersPopup" class="rounded-lg border w-64 shadow-module text-sm font-medium overflow-auto h-auto max-h-[21rem]"
                     :class="[darkMode ? 'bg-dark-module border-dark-border text-slate-50' : 'text-slate-900 bg-light-module border-light-border']">

                    <!-- Optional Custom Categories supplied by parent component -->
                    <div v-for="(category, idx) in (customCategories ?? [])" :key="category.id">
                        <div class="flex items-center justify-between cursor-pointer px-3 py-2"
                             :class="[selectedFilter === category.id
                            ? darkMode ? 'text-primary-500 bg-slate-700 bg-opacity-50 font-semibold' : 'text-primary-500 bg-primary-50 font-semibold'
                            : darkMode ? 'text-white bg-dark-module hover:bg-dark-background' : 'bg-light-module hover:bg-light-background'
                        ]"
                             @click="selectFilter(category.id, idx)"
                             v-if="Object.keys(category.options ?? {}).length"
                        >
                            <p>{{ category.name }}</p>
                            <svg v-if="selectedFilter === category.id" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2.0" stroke="currentColor"
                                 class="w-4 ml-auto">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                            </svg>
                        </div>
                        <component
                            :key="category.id"
                            :is="getComponent(category.type)"
                            :active="selectedFilter === category.id"
                            :dark-mode="darkMode"
                            :filter-data="category"
                            v-model="customValue[category.id]"
                            @update:modelValue="updateCustomValues"
                            @close-filters="toggleFiltersPopup"
                            @custom:deleteOption="deleteCustomOption"
                        />
                    </div>

                    <!-- Filterables supplied by backend-->
                    <div ref="menu" v-for="(filter, idx) in filters" :key="filter.id">
                        <div :id="filter.id" class="flex gap-1 items-center justify-between cursor-pointer px-3 py-2"
                             :class="[selectedFilter === filter.id
                            ? darkMode ? 'text-primary-500 bg-slate-700 bg-opacity-50 font-semibold' : 'text-primary-500 bg-primary-50 font-semibold'
                            : darkMode ? 'bg-dark-module hover:bg-dark-background' : 'bg-light-module hover:bg-light-background',
                            isOffscreen ? 'flex-row-reverse' : ''
                        ]"
                             @click="selectFilter(filter.id, idx)"
                             v-show="filter?.show"
                        >
                            <p class="flex-1">{{ filter.name }}</p>
                            <simple-icon v-if="selectedFilter === filter.id" :icon="menuItemIcon" />
                        </div>
                        <div
                            v-show="selectedFilter === filter.id"
                            ref="popover"
                            class="absolute mx-1 top-0 rounded-lg border shadow-module min-w-64"
                            :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border', !isOffscreen ? 'left-64' : 'right-64']"
                        >
                            <component
                                :key="filter.id"
                                :is="getComponent(filter.type)"
                                :active="selectedFilter === filter.id"
                                :dark-mode="darkMode"
                                :filter-data="filter"
                                v-model="modelValue[filter.id]"
                                @update-defaults="updateFilterDefaults"
                                @update:filterOptions="updateFilterOptions"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </teleport>
    </div>
</template>

<script>
import {defineComponent, nextTick} from 'vue'
import CustomButton from "../CustomButton.vue";
import InputFilterableOption from "./InputFilterableOption.vue";
import SelectFilterableOption from "./SelectFilterableOption.vue";
import MultiSelectFilterableOption from "./MultiSelectFilterableOption.vue";
import DateRangeFilterableOption from "./DateRangeFilterableOption.vue";
import CustomPresetFilterableOption from "./CustomPresetFilterableOption.vue";
import DualOperatorFilterableOption from './DualOperatorFilterableOption.vue'
import DualOperatorWithTimeframeFilterableOption from './DualOperatorWithTimeframeFilterableOption.vue'
import CheckboxListDualOperatorFilterableOption from './CheckboxListDualOperatorFilterableOption.vue'
import MultiSelectWithLogicalFilterableOption from './MultiSelectWithLogicalFilterableOption.vue'
import MultiSelectWithChildrenFilterableOption from "./MultiSelectWithChildrenFilterableOption.vue";
import usePopoverMenu from "../../../../../composables/usePopoverMenu.js";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../composables/useSimpleIcon.js";

const componentMap = {
    'input'        : InputFilterableOption,
    'select'        : SelectFilterableOption,
    'multi-select'  : MultiSelectFilterableOption,
    'date-range'    : DateRangeFilterableOption,
    'dual-operator' : DualOperatorFilterableOption,
    'dual-operator-with-timeframe': DualOperatorWithTimeframeFilterableOption,
    'checkbox-list-dual-operator': CheckboxListDualOperatorFilterableOption,
    'multi-select-with-logical': MultiSelectWithLogicalFilterableOption,
    'custom-preset' : CustomPresetFilterableOption,
    'multi-select-with-children': MultiSelectWithChildrenFilterableOption,
};

const defaultComponent = () => {
    const div = document.createElement('div');
    div.innerText = 'MISSING COMPONENT';
    return div;
}

export const getComponent = (filterOptionId) => {
    return componentMap[filterOptionId] ?? defaultComponent();
}

export default defineComponent({
    name: "Filterable",
    components: {SimpleIcon, CustomButton },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        // Backend-supplied Filterables, options and component types are controlled by backend Classes
        filters: {
            type: Array,
            default: [],
        },
        modelValue: {
            type: Object,
            default: {},
        },
        // Custom categories can be added by the frontend if required, using a separate customValue object to track reactivity
        customCategories: {
            type: Array,
            default: null,
        },
        customValue: {
            type: Object,
            default: {},
        },
        buttonColor: {
            type: String,
            default: 'slate-inverse'
        }
    },
    data() {
        return {
            filtersPopup: false,
            selectedFilter: null,
            filterInputs: {},
            popoverMenu: usePopoverMenu(),
            isOffscreen: false,
            getComponent,
            calculatedPosition: {
                left: `10px`,
                top: `10px`,
                position: 'fixed',
                'z-index': '1000',
            }
        }
    },
    computed: {
        menuItemIcon(){
            return this.isOffscreen ? this.simpleIconHelper.icons.CHEVRON_LEFT : this.simpleIconHelper.icons.CHEVRON_RIGHT;
        },
        simpleIconHelper(){
            return useSimpleIcon()
        }
    },
    emits: ['update:modelValue', 'update:defaults', 'update:customValue', 'custom:deleteOption', 'update:filterOptions'],
    methods: {
        async toggleFiltersPopup() {
            this.filtersPopup = !this.filtersPopup;
            const button = this.$refs.btn.$el.getBoundingClientRect();

            const position = this.popoverMenu.calculatePosition({
                clientX: button.x - 10,
                clientY: button.y + button.height,
                target: this.$refs.btn.$el
            }, this.$refs.mainmenu)

            this.calculatedPosition = {
                left: `${position.popoverLeft}px`,
                top: `${position.popoverTop}px`,
                position: 'fixed',
                'z-index': '1000',
            }

            if(this.filtersPopup === false) {
                this.selectedFilter = null;
            }
        },
        async selectFilter(filterId, idx) {
            if (this.selectedFilter === filterId) {
                this.selectedFilter = null;
                return;
            }
            this.isOffscreen = false
            this.selectedFilter = filterId;
            await nextTick()
            this.calculateMenuPosition(idx)
        },
        calculateMenuPosition(idx) {
            const {popover, menu} = this.$refs

            const targetPopover = popover[idx]
            const targetMenu = menu[idx]

            this.isOffscreen = this.popoverMenu.checkIfElementIsOffScreen(targetPopover, targetMenu)
        },
        updateFilterDefaults(filterEvent) {
            this.$emit('update:defaults', filterEvent);
        },
        updateCustomValues() {
            this.$emit('update:customValue', this.customValue);
        },
        updateFilterOptions() {
            this.$emit('update:filterOptions');
        },
        deleteCustomOption(option) {
            this.$emit('custom:deleteOption', option);
        },
        test(data, key) {
            console.log('updated component', data);
            this.$emit('update:modelValue', { [key]: data });
        }
    },
});
</script>
