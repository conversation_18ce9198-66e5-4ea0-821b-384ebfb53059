<template>
    <div v-show="active">
        <LogicalSelector :darkMode="darkMode" v-model:logical="logical"></LogicalSelector>
        <div class="min-h-48 h-64 overflow-auto text-sm font-medium pb-2">
            <p v-for="[name, value] in Object.entries(filterData?.options?.options)" :key="value"
               :id="`option-${value}`"
                @click="selectOption(value)"
                class="capitalize px-3 py-2 flex items-center justify-between cursor-pointer"
                :class="[selectedOptions.includes(value)
                    ? darkMode ? 'text-primary-500 bg-slate-700 bg-opacity-50 font-semibold' : 'text-primary-500 bg-primary-50 font-semibold'
                    : darkMode ? 'bg-dark-module hover:bg-dark-background' : 'bg-light-module hover:bg-light-background'
                ]">
                    {{ name }}
            </p>
        </div>
    </div>
</template>

<script>
import { defineComponent } from 'vue'
import CustomButton from "../CustomButton.vue";
import LogicalSelector from '../LogicalSelector.vue'

export default defineComponent({
    name: "MultiSelectWithLogicalFilterableOption",
    components: { LogicalSelector, CustomButton },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        active: {
            type: Boolean,
            default: false,
        },
        filterData: {
            type: Object,
            default: {
                options: {
                    options: [],
                    logical: 'and',
                }
            },
        },
        modelValue: {
            type: Object,
            default: {
                options: [],
                logical: 'and',
            },
        }
    },
    emits: ['update:modelValue'],
    data() {
        return {
            selectedOptions: [],
            logical: 'and',
        }
    },
    mounted () {
        this.logical = this.filterData?.options?.logical ?? 'and'

        this.$emit('update:modelValue', {
            options: this.selectedOptions,
            logical: this.logical,
        })
    },
    methods: {
        selectOption(optionValue) {
            if (this.selectedOptions.includes(optionValue)) {
                this.selectedOptions = this.selectedOptions.filter(v => v !== optionValue);
            }
            else {
                this.selectedOptions.push(optionValue);
            }
            this.$emit('update:modelValue', {
                options: this.selectedOptions,
                logical: this.logical,
            });
        },
    },
    watch: {
        active(newVal) {
            if (newVal) {
                this.selectedOptions = this.modelValue?.options ?? []
                this.logical = this.modelValue?.logical ?? 'and'
            }
        },
        logical(newVal) {
            this.$emit('update:modelValue', {
                options: this.selectedOptions,
                logical: newVal,
            });
        }
    }
});

</script>
