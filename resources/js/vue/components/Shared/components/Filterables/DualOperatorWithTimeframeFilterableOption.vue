<template>
    <div v-show="active">
        <div class="grid gap-3 p-3">

            <!-- First Operator = Greater Than -->
            <div class="flex items-center">
                <input :class="[darkMode ? 'bg-dark-module' : 'bg-light-module']" v-model="form.first_operator"
                       :value="GREATER_THAN_OPERATOR"
                       class="mr-1 checked:bg-primary-500 focus:bg-primary-500 focus:ring-0 focus:outline-none"
                       id="first-greater-than-operator"
                       type="radio">
                Greater Than
            </div>
            <CustomInput v-model="form.first_input" placeholder="$0" :dark-mode="darkMode"
                         v-show="form.first_operator === GREATER_THAN_OPERATOR"
                         id="first-input"></CustomInput>

            <!-- First Operator = Equal To -->
            <div class="flex items-center">
                <input :class="[darkMode ? 'bg-dark-module' : 'bg-light-module']" v-model="form.first_operator"
                       :value="EQUALS_OPERATOR"
                       class="mr-1 checked:bg-primary-500 focus:bg-primary-500 focus:ring-0 focus:outline-none"
                       id="first-equal-to-operator"
                       type="radio">
                Equal To
            </div>
            <CustomInput v-model="form.first_input" placeholder="$0" :dark-mode="darkMode"
                         v-show="form.first_operator === EQUALS_OPERATOR"
                         id="first-input"></CustomInput>

            <!-- First Operator = Less Than -->
            <div class="flex items-center">
                <input :class="[darkMode ? 'bg-dark-module' : 'bg-light-module']" v-model="form.first_operator"
                       :value="LESS_THAN_OPERATOR"
                       class="mr-1 checked:bg-primary-500 focus:bg-primary-500 focus:ring-0 focus:outline-none"
                       id="first-less-than-operator"
                       type="radio">
                Less Than
            </div>
            <CustomInput v-model="form.first_input" placeholder="$0" :dark-mode="darkMode"
                         v-show="form.first_operator === LESS_THAN_OPERATOR"
                         id="first-input"></CustomInput>
            <p @click="toggleFirstDate" v-show="firstSegmentIsSet && !form.first_timeframe_active"
               class="cursor-pointer text-primary-500 inline-flex items-center">
                <svg class="mr-1" width="14" height="16" viewBox="0 0 14 16" fill="none"
                     xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M3.11133 7H4.66688V8.55556H3.11133V7ZM3.11133 10.1111H4.66688V11.6667H3.11133V10.1111ZM6.22244 7H7.77799V8.55556H6.22244V7ZM6.22244 10.1111H7.77799V11.6667H6.22244V10.1111ZM9.33355 7H10.8891V8.55556H9.33355V7ZM9.33355 10.1111H10.8891V11.6667H9.33355V10.1111Z"
                        fill="#0081FF"/>
                    <path
                        d="M1.55556 15.5556H12.4444C13.3023 15.5556 14 14.8579 14 14V3.11111C14 2.25322 13.3023 1.55556 12.4444 1.55556H10.8889V0H9.33333V1.55556H4.66667V0H3.11111V1.55556H1.55556C0.697667 1.55556 0 2.25322 0 3.11111V14C0 14.8579 0.697667 15.5556 1.55556 15.5556ZM12.4444 4.66667L12.4452 14H1.55556V4.66667H12.4444Z"
                        fill="#0081FF"/>
                </svg>
                Add Timeframe
            </p>

            <!-- First Timeframe Options + Custom Date Range -->
            <div v-show="firstSegmentIsSet && form.first_timeframe_active">
                <p @click="toggleFirstDate" class="mb-2 cursor-pointer text-slate-500 inline-flex items-center">
                    Remove Timeframe
                </p>
                <Dropdown is-not-relative :placeholder="DROPDOWN_PLACEHOLDER" class="mb-2" v-model="form.first_timeframe"
                          :options="timeframeOptions"
                          :dark-mode="darkMode"></Dropdown>
                <div v-show="firstTimeframeIsCustom" class="flex items-center gap-2">
                    <DatePicker
                        :enable-time-picker="false"
                        :dark="darkMode"
                        v-model="form.first_from_date"
                        :flow="flow"
                        auto-apply
                        placeholder="From"
                        :format="'MM-dd-yy'">
                        <template #input-icon>
                            <svg class="ml-2" width="14" height="16" viewBox="0 0 14 16" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M3.11133 7H4.66688V8.55556H3.11133V7ZM3.11133 10.1111H4.66688V11.6667H3.11133V10.1111ZM6.22244 7H7.77799V8.55556H6.22244V7ZM6.22244 10.1111H7.77799V11.6667H6.22244V10.1111ZM9.33355 7H10.8891V8.55556H9.33355V7ZM9.33355 10.1111H10.8891V11.6667H9.33355V10.1111Z"
                                    fill="#0081FF"/>
                                <path
                                    d="M1.55556 15.5556H12.4444C13.3023 15.5556 14 14.8579 14 14V3.11111C14 2.25322 13.3023 1.55556 12.4444 1.55556H10.8889V0H9.33333V1.55556H4.66667V0H3.11111V1.55556H1.55556C0.697667 1.55556 0 2.25322 0 3.11111V14C0 14.8579 0.697667 15.5556 1.55556 15.5556ZM12.4444 4.66667L12.4452 14H1.55556V4.66667H12.4444Z"
                                    fill="#0081FF"/>
                            </svg>
                        </template>
                    </DatePicker>
                    <DatePicker
                        :enable-time-picker="false"
                        :dark="darkMode"
                        v-model="form.first_to_date"
                        :flow="flow"
                        auto-apply
                        placeholder="To"
                        :format="'MM-dd-yy'">
                        <template #input-icon>
                            <svg class="ml-2" width="14" height="16" viewBox="0 0 14 16" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M3.11133 7H4.66688V8.55556H3.11133V7ZM3.11133 10.1111H4.66688V11.6667H3.11133V10.1111ZM6.22244 7H7.77799V8.55556H6.22244V7ZM6.22244 10.1111H7.77799V11.6667H6.22244V10.1111ZM9.33355 7H10.8891V8.55556H9.33355V7ZM9.33355 10.1111H10.8891V11.6667H9.33355V10.1111Z"
                                    fill="#0081FF"/>
                                <path
                                    d="M1.55556 15.5556H12.4444C13.3023 15.5556 14 14.8579 14 14V3.11111C14 2.25322 13.3023 1.55556 12.4444 1.55556H10.8889V0H9.33333V1.55556H4.66667V0H3.11111V1.55556H1.55556C0.697667 1.55556 0 2.25322 0 3.11111V14C0 14.8579 0.697667 15.5556 1.55556 15.5556ZM12.4444 4.66667L12.4452 14H1.55556V4.66667H12.4444Z"
                                    fill="#0081FF"/>
                            </svg>
                        </template>
                    </DatePicker>
                </div>
            </div>

            <!-- Logical -->
            <div v-show="firstSegmentIsSet" class="grid grid-cols-2 gap-2 pt-2">
                <CustomButton @click="logicalSelection('and')" class="justify-center uppercase"
                              :color="form.logical === 'and' ? 'primary' : 'primary-outline'">And
                </CustomButton>
                <CustomButton @click="logicalSelection('or')" class="justify-center uppercase"
                              :color="form.logical === 'or' ? 'primary' : 'primary-outline'">Or
                </CustomButton>
            </div>
        </div>
        <div v-show="form.logical && firstSegmentIsSet" class="grid gap-3 px-3 pb-3 pt-2">

            <!-- Second Operator = Less Than -->
            <div class="flex items-center">
                <input :class="[darkMode ? 'bg-dark-module' : 'bg-light-module']" v-model="form.second_operator"
                       :value="LESS_THAN_OPERATOR"
                       class="mr-1 checked:bg-primary-500 focus:bg-primary-500 focus:ring-0 focus:outline-none"
                       type="radio" id="second-less-than-operator">
                Less Than
            </div>
            <CustomInput v-model="form.second_input" placeholder="$0" :dark-mode="darkMode"
                         v-show="form.second_operator === LESS_THAN_OPERATOR"
                         id="second-input"></CustomInput>

            <!-- Second Operator = Equal To -->
            <div class="flex items-center">
                <input :class="[darkMode ? 'bg-dark-module' : 'bg-light-module']" v-model="form.second_operator"
                       :value="EQUALS_OPERATOR"
                       class="mr-1 checked:bg-primary-500 focus:bg-primary-500 focus:ring-0 focus:outline-none"
                       type="radio" id="second-equal-to-operator">
                Equal To
            </div>
            <CustomInput v-model="form.second_input" placeholder="$0" :dark-mode="darkMode"
                         v-show="form.second_operator === EQUALS_OPERATOR"
                         id="second-input"></CustomInput>

            <!-- Second Operator = Greater Than -->
            <div class="flex items-center">
                <input :class="[darkMode ? 'bg-dark-module' : 'bg-light-module']" v-model="form.second_operator"
                       :value="GREATER_THAN_OPERATOR"
                       class="mr-1 checked:bg-primary-500 focus:bg-primary-500 focus:ring-0 focus:outline-none"
                       type="radio" id="second-greater-than-operator">
                Greater Than
            </div>
            <CustomInput v-model="form.second_input" placeholder="$0" :dark-mode="darkMode"
                         v-show="form.second_operator === GREATER_THAN_OPERATOR"
                         id="second-input"></CustomInput>

            <!-- Second Date Toggled -->
            <p @click="toggleSecondDate" v-show="secondSegmentIsSet && !form.second_timeframe_active && form.first_timeframe_active"
               class="cursor-pointer text-primary-500 inline-flex items-center">
                <svg class="mr-1" width="14" height="16" viewBox="0 0 14 16" fill="none"
                     xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M3.11133 7H4.66688V8.55556H3.11133V7ZM3.11133 10.1111H4.66688V11.6667H3.11133V10.1111ZM6.22244 7H7.77799V8.55556H6.22244V7ZM6.22244 10.1111H7.77799V11.6667H6.22244V10.1111ZM9.33355 7H10.8891V8.55556H9.33355V7ZM9.33355 10.1111H10.8891V11.6667H9.33355V10.1111Z"
                        fill="#0081FF"/>
                    <path
                        d="M1.55556 15.5556H12.4444C13.3023 15.5556 14 14.8579 14 14V3.11111C14 2.25322 13.3023 1.55556 12.4444 1.55556H10.8889V0H9.33333V1.55556H4.66667V0H3.11111V1.55556H1.55556C0.697667 1.55556 0 2.25322 0 3.11111V14C0 14.8579 0.697667 15.5556 1.55556 15.5556ZM12.4444 4.66667L12.4452 14H1.55556V4.66667H12.4444Z"
                        fill="#0081FF"/>
                </svg>
                Add Timeframe
            </p>

            <!-- Second Timeframe Options + Custom Date Range -->
            <div v-show="secondSegmentIsSet && form.second_timeframe_active && form.first_timeframe_active">
                <p @click="toggleSecondDate" class="mb-2 cursor-pointer text-slate-500 inline-flex items-center">
                    Remove Timeframe
                </p>
                <Dropdown :placeholder="DROPDOWN_PLACEHOLDER" placement="top" class="mb-2" v-model="form.second_timeframe"
                          :options="timeframeOptions"
                          :dark-mode="darkMode"></Dropdown>
                <div v-show="secondTimeframeIsCustom" class="flex items-center gap-2">
                    <DatePicker
                        :enable-time-picker="false"
                        :dark="darkMode"
                        v-model="form.second_from_date"
                        :flow="flow"
                        auto-apply
                        placeholder="From"
                        :format="'MM-dd-yy'">
                        <template #input-icon>
                            <svg class="ml-2" width="14" height="16" viewBox="0 0 14 16" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M3.11133 7H4.66688V8.55556H3.11133V7ZM3.11133 10.1111H4.66688V11.6667H3.11133V10.1111ZM6.22244 7H7.77799V8.55556H6.22244V7ZM6.22244 10.1111H7.77799V11.6667H6.22244V10.1111ZM9.33355 7H10.8891V8.55556H9.33355V7ZM9.33355 10.1111H10.8891V11.6667H9.33355V10.1111Z"
                                    fill="#0081FF"/>
                                <path
                                    d="M1.55556 15.5556H12.4444C13.3023 15.5556 14 14.8579 14 14V3.11111C14 2.25322 13.3023 1.55556 12.4444 1.55556H10.8889V0H9.33333V1.55556H4.66667V0H3.11111V1.55556H1.55556C0.697667 1.55556 0 2.25322 0 3.11111V14C0 14.8579 0.697667 15.5556 1.55556 15.5556ZM12.4444 4.66667L12.4452 14H1.55556V4.66667H12.4444Z"
                                    fill="#0081FF"/>
                            </svg>
                        </template>
                    </DatePicker>
                    <DatePicker
                        :enable-time-picker="false"
                        :dark="darkMode"
                        v-model="form.second_to_date"
                        :flow="flow"
                        auto-apply
                        placeholder="To"
                        :format="'MM-dd-yy'">
                        <template #input-icon>
                            <svg class="ml-2" width="14" height="16" viewBox="0 0 14 16" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M3.11133 7H4.66688V8.55556H3.11133V7ZM3.11133 10.1111H4.66688V11.6667H3.11133V10.1111ZM6.22244 7H7.77799V8.55556H6.22244V7ZM6.22244 10.1111H7.77799V11.6667H6.22244V10.1111ZM9.33355 7H10.8891V8.55556H9.33355V7ZM9.33355 10.1111H10.8891V11.6667H9.33355V10.1111Z"
                                    fill="#0081FF"/>
                                <path
                                    d="M1.55556 15.5556H12.4444C13.3023 15.5556 14 14.8579 14 14V3.11111C14 2.25322 13.3023 1.55556 12.4444 1.55556H10.8889V0H9.33333V1.55556H4.66667V0H3.11111V1.55556H1.55556C0.697667 1.55556 0 2.25322 0 3.11111V14C0 14.8579 0.697667 15.5556 1.55556 15.5556ZM12.4444 4.66667L12.4452 14H1.55556V4.66667H12.4444Z"
                                    fill="#0081FF"/>
                            </svg>
                        </template>
                    </DatePicker>
                </div>
            </div>

        </div>
    </div>
</template>
<style>
.dp__theme_dark {
  --dp-background-color: #0F1A24;
  --dp-border-color: #20354A;
}
</style>

<script setup>
import Dropdown from '../../components/Dropdown.vue'
import { computed } from 'vue'

const LESS_THAN_OPERATOR = 'lessThan'
const EQUALS_OPERATOR = 'equalTo'
const GREATER_THAN_OPERATOR = 'greaterThan'

const flow = computed(() => {
  return ['year', 'month', 'day']
})

const DROPDOWN_PLACEHOLDER = 'Select timeframe'
</script>

<script>
import CustomInput from '../../components/CustomInput.vue'
import CustomButton from '../../components/CustomButton.vue'
import DatePicker from '@vuepic/vue-datepicker'
import {nextTick, defineComponent} from 'vue'
import {DateTime} from 'luxon'

/**
 * @param options
 * @returns {null|*}
 */
const parseDate = (options) => {
    const usingCustomOption = options?.usingCustomOption

  if (usingCustomOption) {
    if (!options?.date) {
      return null
    }

    return DateTime.fromJSDate(options?.date)
  } else {
    const currentDate = DateTime.local()
    const relativeDateOption = options?.relativeDateOption
    const getRelativeDateMinEnd = options?.getRelativeDateMinEnd

    if (!relativeDateOption) {
      return null
    }

    if (!getRelativeDateMinEnd) {
      return currentDate
    }

    return currentDate.minus({ days: relativeDateOption })
  }
}

export default defineComponent({
  name: 'DualOperatorWithTimeframeFilterableOption',
  components: { CustomButton, CustomInput, DatePicker },
  props: {
    darkMode: {
      type: Boolean,
      default: false,
    },
    active: {
      type: Boolean,
      default: false,
    },
    filterData: {
      type: Object,
      default: {},
    },
    modelValue: {
      type: Object,
      default: {
        first_input: null,
        second_input: null,
        first_operator: null,
        second_operator: null,
        logical: null,
        first_timeframe_active: false,
        second_timeframe_active: false,
        first_from_date: null,
        first_to_date: null,
        second_from_date: null,
        second_to_date: null,
        first_timeframe: null,
        second_timeframe: null,
      },
    },
  },
  emits: ['update:modelValue', 'updateDefaults'],
  data () {
    return {
      customPresetSelected: false,
      defaultPreset: null,
      form: {
        first_input: null,
        second_input: null,
        first_operator: null,
        second_operator: null,
        logical: null,
        first_timeframe_active: false,
        second_timeframe_active: false,
        first_from_date: null,
        first_to_date: null,
        second_from_date: null,
        second_to_date: null,
        first_timeframe: null,
        second_timeframe: null,
      },
    }
  },
  computed: {
    isUsed () {
      return this.form.first_operator && !isNaN(this.form.first_input)
    },
    firstSegmentIsSet () {
      return this.form.first_operator
    },
    secondSegmentIsSet () {
      return this.form.second_operator
    },
    firstTimeframeIsCustom () {
      return this.form.first_timeframe ===
          'custom'
    },
    secondTimeframeIsCustom () {
      return this.form.second_timeframe ===
          'custom'
    },
    timeframeOptions () {
      return [
        {
          name: 'Today',
          id: 0,
        },
        {
          name: 'Yesterday',
          id: 1,
        },
        {
          name: 'Last 7 Days',
          id: 7,
        },
        {
          name: 'Last 30 Days',
          id: 30,
        },
        {
          name: 'Last 90 Days',
          id: 90,
        },
        {
          name: 'Last 180 Days',
          id: 180,
        },
        {
          name: 'Last 365 Days',
          id: 365,
        },
        {
          name: 'Custom',
          id: 'custom',
        }
      ]
    },
    firstFromDate () {
      return parseDate(
          {
            usingCustomOption: this.firstTimeframeIsCustom,
            relativeDateOption: this.form.first_timeframe,
            date: this.form.first_from_date,
            getRelativeDateMinEnd: true,
          }
      )
    },
    firstToDate () {
      return parseDate(
          {
            usingCustomOption: this.firstTimeframeIsCustom,
            relativeDateOption: this.form.first_timeframe,
            date: this.form.first_to_date,
            getRelativeDateMinEnd: false,
          }
      )
    },
    secondFromDate () {
      return parseDate(
          {
            usingCustomOption: this.secondTimeframeIsCustom,
            relativeDateOption: this.form.second_timeframe,
            date: this.form.second_from_date,
            getRelativeDateMinEnd: true,
          }
      )
    },
    secondToDate () {
      return parseDate(
          {
            usingCustomOption: this.secondTimeframeIsCustom,
            relativeDateOption: this.form.second_timeframe,
            date: this.form.second_to_date,
            getRelativeDateMinEnd: false,
          }
      )
    },
    computedForm () {
     if (!this.isUsed) {
       return null
     }

      const form = { ...this.form }

      return {
        ...form,
        first_from_date: this.firstFromDate,
        first_to_date: this.firstToDate,
        second_from_date: this.secondFromDate,
        second_to_date: this.secondToDate,
      }
    }
  },
  methods: {
    async handlePresetChange () {
      await nextTick()

      this.$emit('update:modelValue', this.computedForm)
    },
    updateFilterDefaults () {
      this.$emit('updateDefaults', {
        [this.filterData.id]: this.computedForm,
      })
    },
    logicalSelection (selection) {
      if (this.form.logical === selection) {
        this.form.logical = null
        this.form.second_operator = null
      } else {
        this.form.logical = selection
      }
    },
    toggleFirstDate () {
      this.form.first_timeframe_active = !this.form.first_timeframe_active
    },
    toggleSecondDate () {
      this.form.second_timeframe_active = !this.form.second_timeframe_active
    },
  },
  mounted () {
    this.form = { ...this.modelValue }
  },
  watch: {
    form: {
      handler () {

        this.handlePresetChange()
      },
      deep: true,
    }
  },
})
</script>
