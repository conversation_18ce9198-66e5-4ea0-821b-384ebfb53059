<template>
  <div v-show="active">
      <div class="p-3">
          <CheckboxInputFilterOption
              v-for="(option, key) in options"
              :selected="option?.selected"
              :input="option?.value"
              @update:input="updateInputForOption($event, key)"
              @update:selected="updateSelectedForOption($event, key)"
              :id="option?.key"
              :title="option?.label"
              :dont-show-input="!option.hasOwnProperty('value') || !option.hasOwnProperty('operator')"
              :key="`${option?.key}-option`"
              :dark-mode="darkMode"
          >
              <template v-show="option?.selected" v-if="option.hasOwnProperty('value') && option.hasOwnProperty('operator')"
                        v-slot:input>
                  <RadioOption v-show="option?.selected" :dark-mode="darkMode"
                               title="Greater Than Or Equal To"
                               :value="OPERATOR_GREATER_THAN_OR_EQUAL_TO"
                               :id="`${option.key}-operator-greater-than-or-equal-to-option`"
                               v-model:chosen="option.operator"
                  ></RadioOption>

                  <div class="grid grid-cols-2 space-x-2 items-center" v-show="option.operator === OPERATOR_GREATER_THAN_OR_EQUAL_TO && option?.selected">
                      <CustomInput :id="`${option.key}-input`" v-model="option.value" :dark-mode="darkMode"
                                   v-show="option.operator === OPERATOR_GREATER_THAN_OR_EQUAL_TO && option?.selected"
                                   name="value"></CustomInput>
                      <div v-if="option?.units" :class="{ 'text-gray-300': darkMode, 'text-gray-500': !darkMode }"
                           v-show="option.selected">
                          / {{ option?.units }}
                      </div>
                  </div>

                  <RadioOption v-show="option?.selected" :dark-mode="darkMode"
                               title="Less Than Or Equal To"
                               :value="OPERATOR_LESS_THAN_OR_EQUAL_TO"
                               :id="`${option.key}-operator-less-than-or-equal-to-option`"
                               v-model:chosen="option.operator"
                  ></RadioOption>

                  <div class="grid grid-cols-2 space-x-2 items-center" v-show="option.operator === OPERATOR_LESS_THAN_OR_EQUAL_TO && option?.selected">
                      <CustomInput :id="`${option.key}-input`" v-model="option.value" :dark-mode="darkMode"
                                   v-show="option.operator === OPERATOR_LESS_THAN_OR_EQUAL_TO && option?.selected"
                                   name="value"></CustomInput>
                      <div v-if="option?.units" :class="{ 'text-gray-300': darkMode, 'text-gray-500': !darkMode }"
                           v-show="option.selected">
                          / {{ option?.units }}
                      </div>
                  </div>
              </template>
          </CheckboxInputFilterOption>
      </div>
  </div>
</template>

<script>
import { defineComponent, nextTick } from 'vue'
import LogicalSelector from '../LogicalSelector.vue'
import CustomInput from '../CustomInput.vue'
import MinAndMaxText from '../MinAndMaxText.vue'
import RadioOption from '../RadioOption.vue'
import CheckboxInputFilterOption from '../CheckboxInputFilterOption.vue'

const OPERATOR_GREATER_THAN_OR_EQUAL_TO = 'greaterThanOrEqualTo'
const OPERATOR_LESS_THAN_OR_EQUAL_TO = 'lessThanOrEqualTo'

export default defineComponent({
  name: 'CheckboxListDualOperatorFilterableOption',
  components: { RadioOption, MinAndMaxText, CustomInput, LogicalSelector, CheckboxInputFilterOption, },
  props: {
    darkMode: {
      type: Boolean,
      default: false,
    },
    active: {
      type: Boolean,
      default: false,
    },
    filterData: {
      type: Object,
      default: {}
    },
    modelValue: {
      type: Object,
      default: {},
    },
  },
  emits: ['update:modelValue', 'updateDefaults'],
  data () {
    return {
      customPresetSelected: false,
      defaultPreset: null,
      options: {}
    }
  },
  mounted () {
    this.options = {
      ...this.filterData
          ?.options
    }
  },
  methods: {
    updateInputForOption (input, key) {
      this.options[key].input = input
    },
    updateSelectedForOption (selected, key) {
      this.options[key].selected = selected
    },
    async handlePresetChange () {
      await nextTick()

      this.$emit('update:modelValue', this.isUsed ? this.options : null)
    },
    updateFilterDefaults () {
      this.$emit('updateDefaults', {
        [this.filterData.id]: this.isUsed ? this.options : null,
      })
    },
  },
  watch: {
    options: {
      handler () {
        this.handlePresetChange()
      },
      deep: true,
    }
  },
  computed: {
    isUsed () {
      return Object.values(this.options).some(option => option.selected)
    },
    OPERATOR_GREATER_THAN_OR_EQUAL_TO () {
      return OPERATOR_GREATER_THAN_OR_EQUAL_TO
    },
    OPERATOR_LESS_THAN_OR_EQUAL_TO () {
      return OPERATOR_LESS_THAN_OR_EQUAL_TO
    },
  }
})
</script>
