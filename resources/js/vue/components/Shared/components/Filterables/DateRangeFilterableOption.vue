<template>
    <div class="w-102" v-show="active">
        <div class="text-sm font-medium pb-8 mt-1 px-2 max-h-80">
            <div class="flex items-center gap-2 my-auto"
                 :class="[disableDatePicker ? 'pointer-events-none grayscale-[50%] opacity-50' : '']"
            >
                <DatePicker
                    :enable-time-picker="false"
                    :dark="darkMode"
                    v-model="selectedDates.from"
                    :min-date="filterData.options?.from?.min ?? null"
                    :max-date="modelValue?.to ?? null"
                    auto-apply
                    placeholder="From"
                    :format="'MM-dd-yy'"
                    @update:model-value="handleCustomDate"
                >
                    <template #input-icon>
                        <svg class="ml-2" width="14" height="16" viewBox="0 0 14 16" fill="none"
                             xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M3.11133 7H4.66688V8.55556H3.11133V7ZM3.11133 10.1111H4.66688V11.6667H3.11133V10.1111ZM6.22244 7H7.77799V8.55556H6.22244V7ZM6.22244 10.1111H7.77799V11.6667H6.22244V10.1111ZM9.33355 7H10.8891V8.55556H9.33355V7ZM9.33355 10.1111H10.8891V11.6667H9.33355V10.1111Z"
                                fill="#0081FF"/>
                            <path
                                d="M1.55556 15.5556H12.4444C13.3023 15.5556 14 14.8579 14 14V3.11111C14 2.25322 13.3023 1.55556 12.4444 1.55556H10.8889V0H9.33333V1.55556H4.66667V0H3.11111V1.55556H1.55556C0.697667 1.55556 0 2.25322 0 3.11111V14C0 14.8579 0.697667 15.5556 1.55556 15.5556ZM12.4444 4.66667L12.4452 14H1.55556V4.66667H12.4444Z"
                                fill="#0081FF"/>
                        </svg>
                    </template>
                </DatePicker>
                <DatePicker
                    :enable-time-picker="false"
                    :dark="darkMode"
                    v-model="selectedDates.to"
                    :min-date="modelValue?.from ?? null"
                    :max-date="filterData.options?.to?.max ?? null"
                    auto-apply
                    placeholder="To"
                    :format="'MM-dd-yy'"
                    @update:model-value="handleCustomDate"
                >
                    <template #input-icon>
                        <svg class="ml-2" width="14" height="16" viewBox="0 0 14 16" fill="none"
                             xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M3.11133 7H4.66688V8.55556H3.11133V7ZM3.11133 10.1111H4.66688V11.6667H3.11133V10.1111ZM6.22244 7H7.77799V8.55556H6.22244V7ZM6.22244 10.1111H7.77799V11.6667H6.22244V10.1111ZM9.33355 7H10.8891V8.55556H9.33355V7ZM9.33355 10.1111H10.8891V11.6667H9.33355V10.1111Z"
                                fill="#0081FF"/>
                            <path
                                d="M1.55556 15.5556H12.4444C13.3023 15.5556 14 14.8579 14 14V3.11111C14 2.25322 13.3023 1.55556 12.4444 1.55556H10.8889V0H9.33333V1.55556H4.66667V0H3.11111V1.55556H1.55556C0.697667 1.55556 0 2.25322 0 3.11111V14C0 14.8579 0.697667 15.5556 1.55556 15.5556ZM12.4444 4.66667L12.4452 14H1.55556V4.66667H12.4444Z"
                                fill="#0081FF"/>
                        </svg>
                    </template>
                </DatePicker>
            </div>
            <div v-if="datePresets.length"
                 class="flex items-center justify-center flex-wrap grid-flow-row gap-2 mt-4"
            >
                <CustomButton
                    :dark-mode="darkMode"
                    :color="selectedDates.preset === preset.id ? 'primary' : 'slate-outline'"
                    @click="handlePresetChange(preset.id)"
                    v-for="preset in datePresets"
                >
                    {{ preset.name }}
                </CustomButton>
            </div>
        </div>
    </div>
</template>

<script>
import { defineComponent, nextTick } from 'vue'
import DateRangeFilter from "../SimpleTable/components/SimpleTableHiddenFilters/BaseFilters/DateRangeFilter.vue";
import CustomButton from "../CustomButton.vue";
import DatePicker from '@vuepic/vue-datepicker';
import Dropdown from "../../../LeadProcessing/components/Dropdown.vue";

export default defineComponent({
    name: "DateRangeFilterableOption",
    components: { Dropdown, CustomButton, DateRangeFilter, DatePicker },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        active: {
            type: Boolean,
            default: false,
        },
        filterData: {
            type: Object,
            default: {},
        },
        modelValue: {
            type: Object,
            default: {
                to: null,
                from: null,
                preset: null,
            },
        },
        disableDatePicker: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['update:modelValue', 'updateDefaults'],
    data() {
        return {
            selectedDates: {
                from: null,
                to: null,
                preset: null,
            },
            datePresets: [],
            customPresetSelected: false,
            defaultPreset: null,
        }
    },
    mounted() {
        this.datePresets = Object.entries(this.filterData.options?.presets ?? {}).map(([label, preset])=> {
            if (preset.default) this.defaultPreset = label;
            return ({ name:label, id: label });
        });

        this.resetToDefaultDates();

        if (this.selectedDates.from || this.selectedDates.to) {
            this.updateFilterDefaults();
        }
    },
    methods: {
        async handlePresetChange(newValue) {
            await nextTick();
            const targetPreset = this.filterData.options.presets[newValue];

            if (targetPreset) {
                this.customPresetSelected = targetPreset.custom;
                if (!targetPreset.custom) {
                    this.selectedDates.from = targetPreset.from;
                    this.selectedDates.to = targetPreset.to;
                    this.selectedDates.preset = newValue;
                }
            }

            this.$emit('update:modelValue', { ...this.selectedDates });
        },
        resetToDefaultDates() {
            const presetDefault = Object.entries(this.filterData.options?.presets ?? {}).find(([_label, preset]) => preset.default) ?? null;
            if (presetDefault) {
                this.selectedDates = {
                    from: presetDefault[1]?.from ?? null,
                    to: presetDefault[1]?.to ?? null,
                    preset: presetDefault[0] ?? null,
                };
            }
            else {
                this.selectedDates = {
                    from: this.filterData.options?.from?.default ?? null,
                    to: this.filterData.options?.to?.default ?? null,
                    preset: null,
                };
            }

            this.$emit('update:modelValue', { ...this.selectedDates });
        },
        handleCustomDate() {
            this.selectedDates.preset = null;
            this.$emit('update:modelValue', { ...this.selectedDates });
        },
        updateFilterDefaults() {
            this.$emit('updateDefaults', {
                [this.filterData.id]: { ...this.selectedDates }
            });
        },
    },
    watch: {
        active(newVal) {
            if (newVal) {
                this.selectedDates = { ...this.modelValue ?? null };

                if (!this.selectedDates) {
                    this.resetToDefaultDates();
                }
            }
        }
    }
})
</script>
