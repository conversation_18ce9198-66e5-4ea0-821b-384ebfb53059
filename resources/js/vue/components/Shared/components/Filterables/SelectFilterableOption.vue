<template>
    <div v-if="active">
        <div class="min-h-48 h-64 overflow-auto text-sm font-medium pb-2">
            <p v-for="[name, value] in Object.entries(filterData.options)" :key="value"
               @click="selectOption(value)"
               class="capitalize px-3 py-2 flex items-center justify-between cursor-pointer"
               :class="[selectedOption === value
                        ? darkMode ? 'text-primary-500 bg-slate-700 bg-opacity-50 font-semibold' : 'text-primary-500 bg-primary-50 font-semibold'
                        : darkMode ? 'bg-dark-module hover:bg-dark-background' : 'bg-light-module hover:bg-light-background'
                    ]">
                {{ name }}
            </p>
        </div>
    </div>
</template>

<script>
import { defineComponent } from 'vue'
import CustomButton from "../CustomButton.vue";

export default defineComponent({
    name: "SelectFilterableOption",
    components: { CustomButton },
    props: {
        darkMode: {
            type: <PERSON>olean,
            default: false,
        },
        active: {
            type: Boolean,
            default: false,
        },
        filterData: {
            type: Object,
            default: {},
        },
        modelValue: {
            type: [String, Number, Boolean, Array],
            default: null,
        }
    },
    emits: ['update:modelValue'],
    data() {
        return {
            selectedOption: null,
        }
    },
    methods: {
        selectOption(optionValue) {
            this.selectedOption = this.selectedOption === optionValue
                ? null
                : optionValue;
            this.$emit('update:modelValue', this.selectedOption);
        },
    },
    watch: {
        active(newVal) {
            if (newVal) this.selectedOptions = this.modelValue ?? null;
        }
    },
});

</script>
