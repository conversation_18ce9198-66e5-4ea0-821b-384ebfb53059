<template>
    <div v-show="active">
        <div class="p-3">
            <RadioOption :dark-mode="darkMode"
                         title="Greater Than Or Equal To"
                         :value="OPERATOR_GREATER_THAN_OR_EQUAL_TO"
                         id="first-operator-greater-than-or-equal-to-option"
                         v-model:chosen="form.first_operator"
            ></RadioOption>
            <CustomInput id="first-value-input" v-model="form.first_input" :dark-mode="darkMode"
                         v-show="form.first_operator === OPERATOR_GREATER_THAN_OR_EQUAL_TO"
                         name="first-value"></CustomInput>
            <MinAndMaxText class="mb-2" v-show="form.first_operator === OPERATOR_GREATER_THAN_OR_EQUAL_TO" :min="min"
                           :max="max"/>

            <RadioOption :dark-mode="darkMode"
                         title="Less Than Or Equal To"
                         :value="OPERATOR_LESS_THAN_OR_EQUAL_TO"
                         id="first-operator-less-than-or-equal-to-option"
                         v-model:chosen="form.first_operator"
            ></RadioOption>
            <CustomInput id="first-value-input" v-model="form.first_input" :dark-mode="darkMode"
                         v-show="form.first_operator === OPERATOR_LESS_THAN_OR_EQUAL_TO" name="first-value"></CustomInput>
            <MinAndMaxText class="mb-2" v-show="form.first_operator === OPERATOR_LESS_THAN_OR_EQUAL_TO" :min="min"
                           :max="max"/>

            <LogicalSelector parent-class="items-center gap-2 p-2 z-10 w-full" :dark-mode="darkMode"
                             v-model:logical="form.logical" v-show="firstValueAndOperatorAreSet"/>

            <RadioOption
                v-show="logicalIsSet"
                :dark-mode="darkMode"
                title="Greater Than Or Equal To"
                :value="OPERATOR_GREATER_THAN_OR_EQUAL_TO"
                id="second-operator-greater-than-or-equal-to-option"
                v-model:chosen="form.second_operator"
            ></RadioOption>
            <CustomInput id="second-value-input" v-model="form.second_input" :dark-mode="darkMode"
                         v-show="form.second_operator === OPERATOR_GREATER_THAN_OR_EQUAL_TO && logicalIsSet"
                         name="second-value"></CustomInput>
            <MinAndMaxText class="mb-2" v-show="form.second_operator === OPERATOR_GREATER_THAN_OR_EQUAL_TO && logicalIsSet"
                           :min="min"
                           :max="max"/>

            <RadioOption
                v-show="logicalIsSet"
                :dark-mode="darkMode"
                title="Less Than Or Equal To"
                :value="OPERATOR_LESS_THAN_OR_EQUAL_TO"
                id="second-operator-less-than-or-equal-to-option"
                v-model:chosen="form.second_operator"
            ></RadioOption>
            <CustomInput id="second-value-input" v-model="form.second_input" :dark-mode="darkMode"
                         v-show="form.second_operator === OPERATOR_LESS_THAN_OR_EQUAL_TO && logicalIsSet"
                         name="second-value"></CustomInput>
            <MinAndMaxText class="mb-2" v-show="form.second_operator === OPERATOR_LESS_THAN_OR_EQUAL_TO && logicalIsSet"
                           :min="min"
                           :max="max"/>
        </div>
    </div>
</template>

<script>
import { defineComponent, nextTick } from 'vue'
import LogicalSelector from '../LogicalSelector.vue'
import CustomInput from '../CustomInput.vue'
import MinAndMaxText from '../MinAndMaxText.vue'
import RadioOption from '../RadioOption.vue'

const OPERATOR_GREATER_THAN_OR_EQUAL_TO = 'greaterThanOrEqualTo'
const OPERATOR_LESS_THAN_OR_EQUAL_TO = 'lessThanOrEqualTo'

export default defineComponent({
  name: 'DualOperatorFilterableOption',
  components: { RadioOption, MinAndMaxText, CustomInput, LogicalSelector, },
  props: {
    darkMode: {
      type: Boolean,
      default: false,
    },
    active: {
      type: Boolean,
      default: false,
    },
    filterData: {
      type: Object,
      default: {
        min: null,
        max: null,
      },
    },
    modelValue: {
      type: Object,
      default: {
        first_input: null,
        second_input: null,
        first_operator: null,
        second_operator: null,
        logical: null,
      },
    },
  },
  emits: ['update:modelValue', 'updateDefaults'],
  data () {
    return {
      customPresetSelected: false,
      defaultPreset: null,
      form: {
        first_input: null,
        second_input: null,
        first_operator: null,
        second_operator: null,
        logical: null,
      },
    }
  },
  mounted () {
    this.form = { ...this.modelValue }
  },
  methods: {
    async handlePresetChange () {
      await nextTick()
      this.$emit('update:modelValue', this.firstValueAndOperatorAreSet ? {
          first_input: this.form.first_input,
          second_input: this.isSecondFilterUsed ? this.form.second_input : null,
          first_operator: this.form.first_operator,
          second_operator: this.isSecondFilterUsed ? this.form.second_operator : null,
          logical: this.isSecondFilterUsed ? this.form.logical : null,
      } : null)
    },
    updateFilterDefaults () {
      this.$emit('updateDefaults', {
        [this.filterData.id]: this.form,
      })
    },
  },
  watch: {
    form: {
      handler () {
        this.handlePresetChange()
      },
      deep: true,
    }
  },
  computed: {
      isUsed() {
          return this.form.first_input || this.form.first_operator
      },
      isSecondFilterUsed() {
          return this.logicalIsSet && this.secondValueAndOperatorAreSet
      },
      OPERATOR_GREATER_THAN_OR_EQUAL_TO() {
          return OPERATOR_GREATER_THAN_OR_EQUAL_TO
      },
      OPERATOR_LESS_THAN_OR_EQUAL_TO() {
          return OPERATOR_LESS_THAN_OR_EQUAL_TO
      },
      logicalIsSet() {
          return ['and', 'or'].includes(this.form.logical) && this.firstValueAndOperatorAreSet
      },
      min() {
          return this.filterData?.options?.min
      },
      max() {
          return this.filterData?.options?.max
      },
      firstValueAndOperatorAreSet() {
          return this.form.first_input && this.form.first_operator
      },
      secondValueAndOperatorAreSet() {
          return this.form.second_input && this.form.second_operator
      }
  }
})
</script>
