<template>
    <div v-show="active">
        <div class="min-h-48 h-64 overflow-auto text-sm font-medium pb-2">
            <div class="pb-2 mb-2"
                 :class="[darkMode ? 'border-dark-border' : 'border-light-border', showFilterChildren ? 'border-b' : '']"
            >
                <div v-for="child in filterData.children" :key="child.id">
                    <div :id="child.id" class="flex items-center justify-between cursor-pointer px-3 py-2"
                         :class="[
                             child.show
                                 ? selectedChild === child.id
                                    ? darkMode ? 'text-primary-500 bg-slate-700 bg-opacity-50 font-semibold' : 'text-primary-500 bg-primary-50 font-semibold'
                                    : darkMode ? 'bg-dark-module hover:bg-dark-background' : 'bg-light-module hover:bg-light-background'
                                 : darkMode ? 'bg-dark-module opacity-50 pointer-events-none' : 'bg-light-module opacity-50 pointer-events-none'

                            ]"
                         @click="selectChild(child.id)"
                    >
                        <p>{{ child.name }}</p>
                        <svg v-if="selectedChild === child.id" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2.0" stroke="currentColor"
                             class="w-4 ml-auto">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                        </svg>
                    </div>
                    <div v-if="modelValue">
                        <component
                            :key="child.id"
                            :is="getComponent(child.type)"
                            :active="selectedChild === child.id"
                            :dark-mode="darkMode"
                            :filter-data="child"
                            v-model="modelValue[child.id]"
                            @update-defaults="updateChildDefaults"
                        />
                    </div>
                </div>
            </div>
            <div v-if="showFilterChildren"
                 class="h-1 py-4 border-b"/>
            <div v-for="[name, value] in Object.entries(filterData.options)" :key="value"
               @click="selectOption(value)"
               class="capitalize px-3 py-2 flex items-center justify-between cursor-pointer"
               :class="[selectedOptions.includes(value)
                    ? darkMode ? 'text-primary-500 bg-slate-700 bg-opacity-50 font-semibold' : 'text-primary-500 bg-primary-50 font-semibold'
                    : darkMode ? 'bg-dark-module hover:bg-dark-background' : 'bg-light-module hover:bg-light-background'
                ]">
                {{ name }}
            </div>
        </div>
    </div>
</template>

<script>
import { defineComponent } from 'vue'
import CustomButton from "../CustomButton.vue";
import { getComponent } from "./Filterable.vue";

export default defineComponent({
    name: "MultiSelectWithChildrenFilterableOption",
    components: { CustomButton },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        active: {
            type: Boolean,
            default: false,
        },
        filterData: {
            type: Object,
            default: {},
        },
        modelValue: {
            type: Object,
            default: null,
        }
    },
    emits: ['update:modelValue', 'update:filterOptions'],
    data() {
        return {
            selectedOptions: [],
            selectedChild: null,
            getComponent,
        }
    },
    mounted() {
        this.getBaseValue();
    },
    methods: {
        getBaseValue() {
            const baseValue =  Object.values(this.filterData.children).reduce((output, child) => {
                return { ...output, [child.id]: [] }
            }, { [this.filterData.id]: [] });

            this.$emit('update:modelValue', baseValue);
        },
        selectOption(optionValue) {
            if (this.selectedOptions.includes(optionValue)) {
                this.selectedOptions = this.selectedOptions.filter(v => v !== optionValue);
            }
            else {
                this.selectedOptions.push(optionValue);
            }
            this.$emit('update:modelValue', {
                ...this.modelValue,
                [this.filterData.id]: this.selectedOptions,
            });
            this.$emit('update:filterOptions');
        },
        selectChild(childId) {
            if (this.selectedChild === childId) {
                this.selectedChild = null;
                return;
            }
            this.selectedChild = childId;
        },
        updateChildDefaults() {
            //TODO
        },
    },
    watch: {
        active(newVal) {
            if (newVal) {
                this.selectedOptions = this.modelValue?.[this.filterData.id] ?? [];
                if (!this.modelValue) {
                    this.getBaseValue();
                }
            }
            else {
                this.selectedChild = null;
            }
        }
    },
    computed: {
        showFilterChildren () {
            return Object.values(this.filterData.children).filter(child => child.shown) > 0;
        }
    },
});

</script>
