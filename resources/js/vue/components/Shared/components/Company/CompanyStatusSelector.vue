<template>
    <div class="grid grid-cols-2 gap-6 border-t border-b py-6 "
        :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
    >
        <div>
            <p class="text-sm font-semibold pb-1">Company Basic Status</p>
            <Dropdown
                placement="top"
                :dark-mode="darkMode"
                :disabled="!canEditBasicStatus"
                :options="companyBasicStatusOptions"
                type="number"
                :model-value="basicStatus"
                @update:model-value="updateBasicStatus"
            />
        </div>
        <div>
            <p class="text-sm font-semibold pb-1">Company Admin Status</p>
            <Dropdown
                placement="top"
                :dark-mode="darkMode"
                :disabled="!canEditAdminStatus"
                :options="companyAdminStatusOptions"
                type="number"
                :model-value="adminStatus"
                @update:model-value="updateAdminStatus"
            />
        </div>
        <div>
            <labeled-value class="col-span-2" label="Admin Approved">
                <toggle-switch
                    :dark-mode="darkMode"
                    :disabled="!canEditLockedApproved"
                    :model-value="!!adminApproved"
                    @update:model-value="updateAdminApproved"
                />
            </labeled-value>
            <labeled-value class="col-span-2 mt-3" label="Admin Locked">
                <toggle-switch
                    :dark-mode="darkMode"
                    :disabled="!canEditLockedApproved"
                    :model-value="!!adminLocked"
                    @update:model-value="updateAdminLocked"
                />
            </labeled-value>
        </div>
        <div class="col-span-2">
            <span>{{ statusHasChanged ? 'Expected updated' : 'Company' }} status: {{ consolidatedStatusDisplay }} -</span>
            <span :class="[companyCanPurchaseLeads ? 'text-green-600' : 'text-red-600']">
                Company can {{ companyCanPurchaseLeads ? '' : 'not '}}purchase leads
            </span>
        </div>
    </div>
</template>

<script>
import Dropdown from "../Dropdown.vue";
import LabeledValue from "../LabeledValue.vue";
import ToggleSwitch from "../ToggleSwitch.vue";
import { PERMISSIONS, useRolesPermissions } from "../../../../../stores/roles-permissions.store.js";
import { useUserStore } from "../../../../../stores/user-store.js";
import { nextTick } from "vue";

export default {
    name: "CompanyStatusSelector",
    components: { ToggleSwitch, LabeledValue, Dropdown },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        basicStatus: {
            type: Number,
            default: null,
        },
        adminStatus: {
            type: Number,
            default: null,
        },
        adminApproved: {
            type: Boolean,
            default: false,
        },
        adminLocked: {
            type: Boolean,
            default: false,
        },
        currentConsolidatedStatus: {
            type: Number,
            default: null
        },
        canEditBasicStatus: Boolean,
        canEditAdminStatus: Boolean,
        canEditLockedApproved: Boolean,
    },
    emits: ['update:basicStatus', 'update:adminStatus', 'update:adminApproved', 'update:adminLocked'],
    data() {
        return {
            permissionStore: useRolesPermissions(),
            PERMISSIONS,
            userStore: useUserStore(),
            companyBasicStatusOptions: [
                {id: 1, name:"Campaigns Active"},
                {id: 2, name:"Campaigns Paused"},
                {id: 3, name:"Campaigns Off"},
                {id: 4, name:"Campaigns Off (Never Purchased)"},
                {id: 5, name:"Registering"},
                {id: 6, name:"Pending Approval"},
                {id: 7, name:"Profile Only"},
            ],
            companyAdminStatusOptions: [
                {id: '', name: 'None'},
                {id: -2, name:"Merged into another Company"},
                {id: 2, name:"All Suspended"},
                {id: 3, name:"Collections"},
                {id: 4, name:"Archived"},
                {id: 5, name:"Admin Locked"},
                {id: 0, name:"None"},
            ],
            companyConsolidatedStatuses: [
                {id: 1, name:"Campaigns Active"},
                {id: 2, name:"Campaigns Paused"},
                {id: 3, name:"Campaigns Off"},
                {id: 4, name:"Campaigns Off (Never Purchased)"},
                {id: 5, name:"Registering"},
                {id: 6, name:"Pending Approval"},
                {id: 7, name:"Profile Only"},
                {id: 8, name:"Suspended (All Campaigns)"},
                {id: 9, name:"Collections"},
                {id: 10, name:"Archived"},
                {id: 11, name:"Admin Locked"},
                {id: -2, name:"Merged into another Company"},
            ],
            canPurchaseStatuses: [1,2,3,4],
            consolidatedStatus: null,
            statusHasChanged: false,
        }
    },
    mounted() {
        this.updateConsolidatedStatus(this.currentConsolidatedStatus);
    },
    computed: {
        consolidatedStatusDisplay() {
            return this.companyConsolidatedStatuses.find(status => status.id === this.consolidatedStatus)?.name ?? 'Unknown Status'
        },
        companyCanPurchaseLeads() {
            return this.canPurchaseStatuses.includes(this.consolidatedStatus);
        },
    },
    methods: {
        async updateConsolidatedStatus(ignoreStatusChange = false) {
            await nextTick();
            if (!ignoreStatusChange)
                this.statusHasChanged = true;

            this.consolidatedStatus = this.getConsolidatedId();
        },
        // This matches the behavior of CompanyConsolidatedStatus in the backend for now
        //  to predict the calculated consolidated status if basic or admin is changed
        getConsolidatedId() {
            if (this.adminLocked || !this.adminApproved)
                return 11;

            switch(this.adminStatus) {
                case 2: return 8;
                case 3: return 9;
                case 4: return 10;
                case 5: return 11;
                case -2: return -2;
                default: return this.basicStatus;
            }
        },
        updateBasicStatus(newVal) {
            this.$emit('update:basicStatus', newVal || null);
            this.updateConsolidatedStatus();
        },
        updateAdminStatus(newVal) {
            this.$emit('update:adminStatus', newVal || null);
            this.updateConsolidatedStatus();
        },
        updateAdminApproved(newVal) {
            this.$emit('update:adminApproved', newVal ? 1 : 0);
            this.updateConsolidatedStatus();
        },
        updateAdminLocked(newVal) {
            this.$emit('update:adminLocked', newVal ? 1 : 0);
            this.updateConsolidatedStatus();
        }
    }
}
</script>
