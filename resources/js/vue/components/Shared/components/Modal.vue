<template id="modal-template">
    <div class="absolute">
        <transition name="modal">
            <div class="fixed inset-0 flex items-center justify-center bg-opacity-75 bg-dark-background" :class="{ 'z-100': zIndexHundred, 'z-50': !zIndexHundred }" >
                <div class="absolute shadow rounded-lg"
                     :class="{
                        'bg-light-module': !darkMode,
                        'bg-dark-module border border-dark-border': darkMode,
                        'max-w-screen-lg' : restrictWidth && !small,
                        'max-w-screen-md': restrictWidth && small,
                        'w-3/4': !restrictWidth && !small,
                        'w-full': fullWidth,
                        [customWidth]: !!customWidth,
                     }">
                    <div :class="[headerClasses, darkMode ? 'border-dark-border' : 'border-light-border']">
                        <div class="px-6 py-5 flex justify-between items-center">
                            <div class="" :class="{'text-black': !darkMode, 'text-grey-120': darkMode}">
                                <slot name="header">

                                </slot>
                            </div>
                            <button v-if="!noCloseButton" class="modal-default-button" :disabled="inputsDisabled" @click="$emit('close')"
                                    :class="{'text-black': !darkMode, 'text-grey-120': darkMode}">
                                <svg class="w-4 fill-current" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M5.9035 4.78018L9.32136 1.36237C9.47273 1.21896 9.56036 1.01977 9.56036 0.796696C9.56036 0.358513 9.20184 0 8.76366 0C8.54058 0 8.34139 0.0876191 8.19799 0.231024L4.78018 3.65686L1.36237 0.231024C1.21896 0.0876191 1.01977 0 0.796696 0C0.358513 0 0 0.358513 0 0.796696C0 1.01977 0.0876191 1.21896 0.231024 1.36237L3.65686 4.78018L0.238999 8.19799C0.0876269 8.34139 0 8.54058 0 8.76366C0 9.20184 0.358513 9.56036 0.796696 9.56036C1.01977 9.56036 1.21896 9.47274 1.36237 9.32933L4.78018 5.9035L8.19799 9.32136C8.34139 9.47273 8.54058 9.56036 8.76366 9.56036C9.20184 9.56036 9.56036 9.20184 9.56036 8.76366C9.56036 8.54058 9.47274 8.34139 9.32933 8.19799L5.9035 4.78018Z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div :class="containerClasses">
                        <div
                            :class="[{
                            'min-h-[45vh]': !noMinHeight,
                            'text-black': !darkMode,
                            'text-grey-120': darkMode,
                            'overflow-y-auto': !noScroll,
                            }, contentMaxHeight]"
                        >
                            <slot name="content">

                            </slot>
                        </div>
                        <div :class="[noButtons ? 'hidden' : 'inline-block', buttonWrapperClasses]" class="flex justify-end gap-3 pt-5">
                            <slot name="buttons"></slot>
                            <button class="transition duration-200 bg-grey-250 hover:bg-gray-400 font-medium focus:outline-none py-2 rounded-md px-5"
                                    :class="{'bg-grey-250 hover:bg-bg-gray-400 text-white': !darkMode, 'bg-grey-500 hover:bg-grey-600 text-white': darkMode}"
                                    :disabled="inputsDisabled"
                                    v-if="!hideCancel"
                                    @click.stop="$emit('close')">
                                {{ closeText }}
                            </button>
                            <button class="transition duration-200 text-white font-medium focus:outline-none py-2 rounded-md px-5 flex gap-1"
                                    :class="{'bg-primary-500 hover:bg-blue-500': !disableConfirm, 'bg-grey-200 text-grey-500': disableConfirm, 'cursor-not-allowed': disableConfirm}"
                                    @click.stop="$emit('confirm')"
                                    :disabled="disableConfirm"
                                    v-if="!hideConfirm"
                            >
                                <svg aria-hidden="true" v-if="loadingConfirmation" class="w-6 h-6 mr-2 text-gray-200 animate-spin fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/><path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/></svg>
                                <span>{{ confirmText }}</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </transition>
    </div>
</template>

<script>
import CustomButton from "./CustomButton.vue";

export default {
    components: {CustomButton},
    props: {
        confirmText: {
            type: String,
            default: "Save"
        },
        closeText: {
            type: String,
            default: 'Close'
        },
        noButtons: {
            type: Boolean,
            default: false
        },
        small: {
            type: Boolean,
            default: false
        },
        disableConfirm: {
            type: Boolean,
            default: false
        },
        loadingConfirmation: {
            type: Boolean,
            default: false
        },
        hideConfirm: {
            type: Boolean,
            default: false
        },
        hideCancel: {
            type: Boolean,
            default: false
        },
        inputsDisabled: {
            type: Boolean,
            default: false
        },
        darkMode: {
            type: Boolean,
            default: false,
        },
        fullWidth: {
            type: Boolean,
            default: true
        },
        restrictWidth: {
            type: Boolean,
            default: true
        },
        containerClasses: {
            type: String,
            default: 'p-8'
        },
        zIndexHundred: {
            type: Boolean,
            default: false
        },
        noCloseButton: {
            type: Boolean,
            default: false
        },
        buttonWrapperClasses : {
            type: String,
            default: ''
        },
        noMinHeight: {
            type: Boolean,
            default: false
        },
        headerClasses: {
            type: String,
            default: 'border-b'
        },
        noScroll: {
            type: Boolean,
            default: false,
        },
        customWidth: {
            type: String,
            default: null,
        },
        contentMaxHeight: {
            type: String,
            default: 'max-h-[65vh]'
        }
    },
    emits: ['confirm', 'close']
}
</script>

<style lang="scss">

</style>
