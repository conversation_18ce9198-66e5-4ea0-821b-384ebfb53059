<template>
    <div class="text-sm">
        <template v-if="!compact">
            <p class="text-lg font-semibold">{{ formattedDate }}</p>
            <div class="text-xs" v-html="formattedTimeRange"></div>
            <p class="text-xs">{{ timezone }}</p>
        </template>
        <template v-else>
            <p>{{ compactTimeRange }}</p>
        </template>
    </div>
</template>

<script>
import useDateHelper from "../../../../composables/useDateHelper.js";

export default {
    props: {
        startDate: {
            type: String,
            required: true,
        },
        endDate: {
            type: String,
            required: false,
        },
        timezone: {
            type: String,
            default: "MST",
        },
        compact: {
            type: Boolean,
            default: false,
        },
    },
    computed: {
        dateHelper(){
            return new useDateHelper()
        },
        startDateInTimezone() {
            return this.dateHelper.parseDate(this.startDate)
        },
        endDateInTimezone() {
            return this.dateHelper.parseDate(this.endDate)
        },
        formattedDate() {
            if (!this.startDate) return "";

            return this.startDateInTimezone.toFormat("MMM d, yyyy");
        },
        formattedTimeRange() {
            if (!this.startDate) return "";

            if (!this.endDate) {
                return this.startDateInTimezone.toFormat("h:mm a");
            }

            if (!this.startDateInTimezone.isValid || !this.endDateInTimezone.isValid) return "Invalid date";

            if (this.startDateInTimezone.hasSame(this.endDateInTimezone, "day")) {
                return `<p>${this.startDateInTimezone.toFormat("h:mm a")} - ${this.endDateInTimezone.toFormat("h:mm a")}</p>`;
            } else {
                return `<p>${this.startDateInTimezone.toFormat("MMM d, yyyy h:mm a")} <br/> ${this.endDateInTimezone.toFormat("MMM d, yyyy h:mm a")}</p>`;
            }
        },
        compactTimeRange() {
            if (!this.startDate || !this.endDate) return "";

            if (!this.startDateInTimezone.isValid || !this.endDateInTimezone.isValid) return "Invalid date";

            if (this.startDateInTimezone.hasSame(this.endDateInTimezone, "day")) {
                return `${this.startDateInTimezone.toFormat("h:mm a")} - ${this.endDateInTimezone.toFormat("h:mm a")}`;
            } else {
                return `${this.startDateInTimezone.toFormat("MMM d, yyyy h:mm a")} -> ${this.endDateInTimezone.toFormat("MMM d, yyyy h:mm a")}`;
            }
        },
    }
};
</script>
