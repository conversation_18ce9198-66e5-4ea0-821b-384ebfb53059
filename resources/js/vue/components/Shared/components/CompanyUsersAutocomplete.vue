<template>
    <simple-autocomplete
        v-model="modelValue"
        :options="options"
        :fetch-options="searchCompanyUsers"
        :get-option-display-string="getOptionDisplayString"
        placeholder="Name, phone or email"
        :label="label"
        :disabled="!companyId"
    >
    </simple-autocomplete>
</template>
<script>
import ApiService from "../../CompanyUsers/services/api.js";
import SimpleAutocomplete from "./SimpleAutocomplete.vue";

export default {
    name: 'CompanyUsersAutocomplete',
    components: {SimpleAutocomplete},
    props: {
        label: {
            type: String,
            default: null
        },
        modelValue: {
            type: Object,
            default: null
        },
        companyId: {
            type: Number,
            default: null
        }
    },
    data() {
        return {
            companyUsersApiService: ApiService.make(),
            options: []
        }
    },
    watch: {
        companyId: {
            handler(){
                this.searchCompanyUsers({page: 1})
            },
        },
        modelValue: {
            handler(){
                this.$emit('update:modelValue', this.modelValue)
            },
            deep: true,
            immediate: true,
        }
    },
    methods: {
        async searchCompanyUsers({page, query}){
            const response = await this.companyUsersApiService.getCompanyUsers({
                company_id: this.companyId,
                page,
                perPage: 10,
                user_details: query
            })

            this.options = response.data.data
        },
        getOptionDisplayString(option){
            return `${option.title_name} #${option.id} - ${option.status}`
        }
    }
}
</script>
