<template>
    <div class="flex gap-1 text-sm" :class="[orientationStyle]">
        <div v-if="label || $slots.label" class="flex gap-2">
            <slot name="label">
                <p class="font-semibold">{{ label }}</p>
                <simple-icon
                    v-if="tooltip"
                    :icon="simpleIcon.icons.INFORMATION_CIRCLE"
                    :tooltip="tooltip"
                >
                </simple-icon>
            </slot>

            <slot name="action">
            </slot>
        </div>
        <loading-spinner v-if="loading" size="w-4 h-4"/>
        <div v-else-if="value || $slots.default" :class="contentStyle || ''">
            <slot class="text-slate-700">
                <p>{{ value }}</p>
            </slot>
        </div>
    </div>
</template>

<script>
import LoadingSpinner from "./LoadingSpinner.vue";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";

export default {
    name: "LabeledValue",
    components: {SimpleIcon, LoadingSpinner},
    props: {
        label: {
            type: String,
        },
        value: {
            type: String,
        },
        orientation: {
            type: String,
            default: 'vertical'
        },
        contentStyle: {
            type: String,
            default: '',
        },
        loading: {
            type: Boolean,
            default: false,
        },
        tooltip: {
            type: String
        }
    },

    computed: {
        orientationStyle() {
            return {
                'vertical': "flex-col",
                'horizontal': "flex-row",
            }[this.orientation]
        },
        simpleIcon() {
            return useSimpleIcon()
        }
    }
}
</script>
