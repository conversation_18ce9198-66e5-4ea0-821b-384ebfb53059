<template>
  <div class="flex text-gray-500 space-x-6" v-if="min || max">
    <div v-if="min">Min: {{ min }}</div>
    <div v-if="max">Max: {{ max }}</div>
  </div>
</template>
<script setup>
import { computed } from 'vue'

const props = defineProps({
  min: { required: false },
  max: { required: false },
})

const min = computed(() => {
  if (!props.min && isNaN(props.min)) {
    return null
  }

  return props.min.toLocaleString()
})

const max = computed(() => {
  if (!props.max && isNaN(props.max)) {
    return null
  }

  return props.max.toLocaleString()
})
</script>
