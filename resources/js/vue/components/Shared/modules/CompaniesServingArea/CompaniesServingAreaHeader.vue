<template>
    <div class="px-5 pt-5 pb-1">
        <div class=" flex items-center">
            <h5 class="text-blue-550 text-sm uppercase font-semibold leading-tight mr-4">
                Companies Servicing Area</h5>
            <div @click="toggleFilterModal" class="inline-flex items-center cursor-pointer"
                 :class="[darkMode ? 'text-slate-100 hover:text-white' : 'text-slate-700 hover:text-slate-900', disableFiltersButton ? 'pointer-events-none opacity-25' : '']">
                <svg class="mr-1 fill-current w-4" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M11 1.83333C11 2.17084 10.7264 2.44444 10.3889 2.44444L4.78404 2.44444C4.69374 2.69983 4.54711 2.9345 4.35192 3.1297C4.0081 3.47351 3.54179 3.66667 3.05556 3.66667C2.56933 3.66667 2.10301 3.47351 1.75919 3.1297C1.564 2.9345 1.41737 2.69983 1.32707 2.44444L0.611112 2.44444C0.273605 2.44444 1.0972e-06 2.17084 1.11196e-06 1.83333C1.12671e-06 1.49583 0.273605 1.22222 0.611112 1.22222L1.32707 1.22222C1.41737 0.966836 1.564 0.732162 1.75919 0.53697C2.10301 0.193154 2.56933 -3.68516e-07 3.05556 -3.47263e-07C3.54179 -3.26009e-07 4.0081 0.193154 4.35192 0.536971C4.54711 0.732162 4.69374 0.966837 4.78404 1.22222L10.3889 1.22222C10.7264 1.22222 11 1.49583 11 1.83333ZM11 5.5C11 5.83751 10.7264 6.11111 10.3889 6.11111L9.67293 6.11111C9.58263 6.3665 9.436 6.60117 9.24081 6.79636C8.89699 7.14018 8.43067 7.33333 7.94444 7.33333C7.45821 7.33333 6.9919 7.14018 6.64808 6.79636C6.45289 6.60117 6.30626 6.3665 6.21596 6.11111L0.611112 6.11111C0.273605 6.11111 9.36927e-07 5.83751 9.5168e-07 5.5C9.66433e-07 5.16249 0.273605 4.88889 0.611112 4.88889L6.21596 4.88889C6.30626 4.6335 6.45289 4.39883 6.64808 4.20364C6.9919 3.85982 7.45821 3.66667 7.94444 3.66667C8.43068 3.66667 8.89699 3.85982 9.24081 4.20364C9.436 4.39883 9.58263 4.6335 9.67293 4.88889L10.3889 4.88889C10.7264 4.88889 11 5.16249 11 5.5ZM11 9.16667C11 9.50418 10.7264 9.77778 10.3889 9.77778L4.78404 9.77778C4.69374 10.0332 4.54711 10.2678 4.35192 10.463C4.0081 10.8068 3.54179 11 3.05556 11C2.56933 11 2.10301 10.8068 1.75919 10.463C1.564 10.2678 1.41737 10.0332 1.32707 9.77778L0.611112 9.77778C0.273605 9.77778 7.76652e-07 9.50417 7.91405e-07 9.16667C8.06158e-07 8.82916 0.273605 8.55556 0.611112 8.55556L1.32707 8.55556C1.41737 8.30017 1.564 8.0655 1.75919 7.8703C2.10301 7.52649 2.56933 7.33333 3.05556 7.33333C3.54179 7.33333 4.0081 7.52649 4.35192 7.8703C4.54711 8.0655 4.69374 8.30017 4.78404 8.55556L10.3889 8.55556C10.7264 8.55556 11 8.82916 11 9.16667ZM8.55556 5.5C8.55556 5.33792 8.49117 5.18249 8.37657 5.06788C8.26196 4.95327 8.10652 4.88889 7.94444 4.88889C7.78237 4.88889 7.62693 4.95327 7.51232 5.06788C7.39772 5.18249 7.33333 5.33792 7.33333 5.5C7.33333 5.66208 7.39772 5.81751 7.51232 5.93212C7.62693 6.04673 7.78237 6.11111 7.94444 6.11111C8.10652 6.11111 8.26196 6.04673 8.37657 5.93212C8.49117 5.81752 8.55556 5.66208 8.55556 5.5ZM3.66667 1.83333C3.66667 1.67126 3.60228 1.51582 3.48768 1.40121C3.37307 1.28661 3.21763 1.22222 3.05556 1.22222C2.89348 1.22222 2.73804 1.28661 2.62344 1.40121C2.50883 1.51582 2.44445 1.67126 2.44445 1.83333C2.44445 1.99541 2.50883 2.15085 2.62344 2.26545C2.73804 2.38006 2.89348 2.44444 3.05556 2.44444C3.21763 2.44444 3.37307 2.38006 3.48768 2.26545C3.60228 2.15085 3.66667 1.99541 3.66667 1.83333ZM3.66667 9.16667C3.66667 9.00459 3.60228 8.84915 3.48768 8.73455C3.37307 8.61994 3.21763 8.55556 3.05556 8.55556C2.89348 8.55556 2.73804 8.61994 2.62344 8.73455C2.50883 8.84915 2.44444 9.00459 2.44444 9.16667C2.44444 9.32874 2.50883 9.48418 2.62344 9.59879C2.73804 9.71339 2.89348 9.77778 3.05556 9.77778C3.21763 9.77778 3.37307 9.71339 3.48768 9.59879C3.60228 9.48418 3.66667 9.32874 3.66667 9.16667Z" />
                </svg>
                <p>Filters</p>
                <div class="ml-1 text-primary-500 text-sm font-semibold" v-if="activeFilters > 0">
                    ({{ activeFilters }})
                </div>
            </div>
        </div>
        <div class="grid gap-x-3 mt-2 items-end"
             :class="[darkMode ? 'text-slate-400' : 'text-slate-500', selectedCompany === null ? 'grid-cols-8' : 'grid-cols-4']">
            <p class="uppercase text-xs col-span-2">Name</p>
            <p class="uppercase text-xs text-center">Status</p>
            <p class="uppercase text-xs text-center">Sales Status</p>
            <p @click="toggleActiveSort('amountOfPurchasedLeads')"
               v-if="amountOfPurchasedLeads && selectedCompany === null"
               class="uppercase text-xs text-center inline-flex items-center justify-center cursor-pointer"
               :class="[sortCompanies['amountOfPurchasedLeads'].active ? 'text-primary-500 font-medium' : '']">
                Leads Purchased
                <br/>
                {{ getConditionName }}
                <svg v-if="sortCompanies['amountOfPurchasedLeads'].active"
                     :class="[sortCompanies['amountOfPurchasedLeads'].ascending ? 'rotate-180' : '']"
                     class="ml-2 transform transition-all duration-200 w-6 fill-current"
                     width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M9.70711 0.292893C10.0976 0.683418 10.0976 1.31658 9.70711 1.70711L5.70711 5.70711C5.51957 5.89464 5.26522 6 5 6C4.73478 6 4.48043 5.89464 4.29289 5.70711L0.292893 1.70711C-0.0976315 1.31658 -0.0976315 0.683417 0.292893 0.292893C0.683417 -0.0976315 1.31658 -0.0976314 1.70711 0.292893L5 3.58579L8.29289 0.292893C8.68342 -0.0976311 9.31658 -0.0976311 9.70711 0.292893Z"/>
                </svg>
            </p>
            <p
                @click="toggleActiveSort('estRevenue')"
                v-if="selectedCompany === null"
                class="uppercase text-xs text-center inline-flex items-center justify-center cursor-pointer"
                :class="[sortCompanies['estRevenue'].active ? 'text-primary-500 font-medium' : '']">
                Est. Revenue
                <svg v-if="sortCompanies['estRevenue'].active"
                     :class="[sortCompanies['estRevenue'].ascending ? 'rotate-180' : '']"
                     class="ml-2 transform transition-all duration-200 w-6 fill-current"
                     width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M9.70711 0.292893C10.0976 0.683418 10.0976 1.31658 9.70711 1.70711L5.70711 5.70711C5.51957 5.89464 5.26522 6 5 6C4.73478 6 4.48043 5.89464 4.29289 5.70711L0.292893 1.70711C-0.0976315 1.31658 -0.0976315 0.683417 0.292893 0.292893C0.683417 -0.0976315 1.31658 -0.0976314 1.70711 0.292893L5 3.58579L8.29289 0.292893C8.68342 -0.0976311 9.31658 -0.0976311 9.70711 0.292893Z"/>
                </svg>
            </p>
            <p
                @click="toggleActiveSort('employeeCount')"
                v-if="selectedCompany === null"
                class="uppercase text-xs text-center inline-flex items-center justify-center cursor-pointer"
                :class="[sortCompanies['employeeCount'].active ? 'text-primary-500 font-medium' : '']">
                Employees Count
                <svg v-if="sortCompanies['employeeCount'].active"
                     :class="[sortCompanies['employeeCount'].ascending ? 'rotate-180' : '']"
                     class="ml-2 transform transition-all duration-200 w-6 fill-current"
                     width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M9.70711 0.292893C10.0976 0.683418 10.0976 1.31658 9.70711 1.70711L5.70711 5.70711C5.51957 5.89464 5.26522 6 5 6C4.73478 6 4.48043 5.89464 4.29289 5.70711L0.292893 1.70711C-0.0976315 1.31658 -0.0976315 0.683417 0.292893 0.292893C0.683417 -0.0976315 1.31658 -0.0976314 1.70711 0.292893L5 3.58579L8.29289 0.292893C8.68342 -0.0976311 9.31658 -0.0976311 9.70711 0.292893Z"/>
                </svg>
            </p>
            <p
                @click="toggleActiveSort('googleReviewCount')"
                v-if="selectedCompany === null"
                class="uppercase text-xs text-center inline-flex items-center justify-center cursor-pointer"
                :class="[sortCompanies['googleReviewCount'].active ? 'text-primary-500 font-medium' : '']">
                Google Review Count
                <svg v-if="sortCompanies['googleReviewCount'].active"
                     :class="[sortCompanies['googleReviewCount'].ascending ? 'rotate-180' : '']"
                     class="ml-2 transform transition-all duration-200 w-6 fill-current"
                     width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M9.70711 0.292893C10.0976 0.683418 10.0976 1.31658 9.70711 1.70711L5.70711 5.70711C5.51957 5.89464 5.26522 6 5 6C4.73478 6 4.48043 5.89464 4.29289 5.70711L0.292893 1.70711C-0.0976315 1.31658 -0.0976315 0.683417 0.292893 0.292893C0.683417 -0.0976315 1.31658 -0.0976314 1.70711 0.292893L5 3.58579L8.29289 0.292893C8.68342 -0.0976311 9.31658 -0.0976311 9.70711 0.292893Z"/>
                </svg>
            </p>
            <p
                @click="toggleActiveSort('googleRating')"
                v-if="selectedCompany === null"
                class="uppercase text-xs text-center inline-flex items-center justify-center cursor-pointer"
                :class="[sortCompanies['googleRating'].active ? 'text-primary-500 font-medium' : '']">
                Google Rating
                <svg v-if="sortCompanies['googleRating'].active"
                     :class="[sortCompanies['googleRating'].ascending ? 'rotate-180' : '']"
                     class="ml-2 transform transition-all duration-200 w-6 fill-current"
                     width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M9.70711 0.292893C10.0976 0.683418 10.0976 1.31658 9.70711 1.70711L5.70711 5.70711C5.51957 5.89464 5.26522 6 5 6C4.73478 6 4.48043 5.89464 4.29289 5.70711L0.292893 1.70711C-0.0976315 1.31658 -0.0976315 0.683417 0.292893 0.292893C0.683417 -0.0976315 1.31658 -0.0976314 1.70711 0.292893L5 3.58579L8.29289 0.292893C8.68342 -0.0976311 9.31658 -0.0976311 9.70711 0.292893Z"/>
                </svg>
            </p>
        </div>
        <FilterConfigModal :filter-component="filterComponent['companyServicingAreaFilters']" :dark-mode="darkMode"
                           @close="toggleFilterModal" v-if="filterModal" @apply="apply" :filter="companiesServingAreaFilters"
                           :user="user" @update:filter="updateCompaniesServingAreaFilters">
        </FilterConfigModal>
    </div>
</template>
<script>
import ToggleSwitch from "../../components/ToggleSwitch.vue";
import FilterConfigModal from "../../components/FilterConfigModal.vue";
import {useCompanyServicingAreaSorterStore} from "../../../../../stores/company-servicing-area-sorter.store";
import {mapWritableState} from "pinia";
import { useCompaniesServingAreaStore } from '../CompaniesServingArea.service'

export default {
    emits: ['update:selected-company', 'apply', 'toggle-sort'],
    components: {
        FilterConfigModal,
        ToggleSwitch
    },
    props: {
        selectedCompany: {type: [null, Number], required: true},
        darkMode: {type: Boolean, required: true, default: false},
        amountOfPurchasedLeads: {type: Boolean, default: false},
        activeFilters: {type: Number, default: 0},
        purchasedLeadsCondition: {type: String, default: ''},
        user:{type: Object, required: true},
        companies: {type: Array, required: true}
    },
    watch: {
        selectedCompany(to, from) {
            this.changeSelectedCompany(to);
        },
    },
    methods: {
        /**
         * Emits the update:selected-company event.
         * @param to
         */
        changeSelectedCompany(to) {
            this.$emit("update:selected-company", to);
        },
        toggleFilterModal() {
            if (this.disableFiltersButton) {
                return;
            }

            this.filterModal = !this.filterModal;
        },
        /**
         * Emits apply event.
         */
        apply() {
            this.$emit('apply');
        },
        toggleActiveSort(object) {
            Object.entries(this.sortCompanies).forEach((index) => {
                index[1].active = false
            })
            this.sortCompanies[object].active = true;
            if (this.sortCompanies[object].active === true) {
                this.sortCompanies[object].ascending = !this.sortCompanies[object].ascending;
            }

            this.$emit("toggle-sort", object);
        },
        updateCompaniesServingAreaFilters(newValue) {
            this.companiesServingAreaFilters = newValue
        }
    },
    data() {
        return {
            filterModal: false,
            filterComponent: {
                "companyServicingAreaFilters": {
                    "id": "CompanyServicingAreaFilters",
                    "name": "Company Servicing Area",
                    "userPreset": "default",
                    "userPresetList": [
                        {"id": "default", "name": "Default"},
                        {"id": "test", "name": "Test"},
                    ]
                }
            },
        }
    },
    computed: {
        ...mapWritableState(useCompanyServicingAreaSorterStore, {
            sortCompanies: 'sorters'
        }),
        ...mapWritableState(useCompaniesServingAreaStore, {
            companiesServingAreaFilters: 'filters'
        }),
        disableFiltersButton() {
            return !this.companiesServingAreaFilters?.length;
        },
        getConditionName() {
            let conditionName;
            switch (this.purchasedLeadsCondition) {
                case "allTime":
                    conditionName = "All Time";
                    break;
                case "last30Days":
                    conditionName = "Last 30 Days";
                    break;
                case "last60Days":
                    conditionName = "Last 60 Days";
                    break;
                case "last90Days":
                    conditionName = "Last 90 Days";
                    break;
                case "lastSixMonths":
                    conditionName = "Last Six Months";
                    break;
                case "lastYear":
                    conditionName = "Last Year";
                    break;
                case "lastTwoYears":
                    conditionName = "Last Two Years";
                    break;
            }
            return conditionName;
        }
    }
}
</script>
