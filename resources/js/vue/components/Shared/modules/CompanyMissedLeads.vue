<template>
    <div>
        <div class="border rounded-lg"
             :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']"
        >
            <AlertsContainer
                v-if="alertActive"
                :alert-type="alertType"
                :text="alertText"
                :dark-mode="darkMode"
            />
            <div class="p-5 flex items-center">
                <h5 class="text-blue-550 text-sm uppercase font-semibold leading-tight">Missed Leads</h5>
            </div>
            <div class="flex items-center flex-wrap gap-2 px-5 pb-5">
                <CustomInput
                    :dark-mode="darkMode"
                    class="w-full max-w-sm"
                    search-icon placeholder="Search by zipcode"
                    v-model="filter.zipcode"
                    @keyup.enter="submitSearch"
                />
                <multi-select
                    class="w-full max-w-sm"
                    :options="timezones"
                    :dark-mode="darkMode"
                    text-place-holder="Search by timezone"
                    :show-search-box="false"
                    @input="(v) => filter.timezones = v"
                    :selected-ids="filter.timezones"
                />
                <multi-select
                    class="w-full max-w-sm"
                    :options="states"
                    :dark-mode="darkMode"
                    text-place-holder="Search by states"
                    :show-search-box="false"
                    @input="(v) => filter.states = v"
                    :selected-ids="filter.states"
                />
                <div class="w-full max-w-sm" :class="!filter?.states?.length && counties.length === 0 ? 'opacity-50 grayscale pointer-events-none' : ''">
                    <multi-select
                        :options="counties"
                        :dark-mode="darkMode"
                        text-place-holder="Search by counties"
                        :show-search-box="false"
                        @input="(v) => filter.counties = v"
                        :selected-ids="filter.counties"
                    />
                </div>
                <CustomButton
                    type="submit"
                    :dark-mode="darkMode"
                    @click="submitSearch"
                >
                    Search
                </CustomButton>
                <CustomButton
                    :dark-mode="darkMode"
                    color="slate-inverse"
                    type="reset"
                    @click="resetFilters"
                >
                    Reset
                </CustomButton>
                <Filterable
                    :dark-mode="darkMode"
                    :custom-categories="customFilters"
                    v-model="filter"
                    @update:customValue="handleCustomUpdate"
                />
            </div>
            <FilterableActivePills
                class="px-5 mb-6"
                :filters="customFilters"
                :active-filters="filter"
                :dark-mode="darkMode"
                @reset-filter="resetFilters"
            />
            <CompanyMissedLeadsSearchTableHeader
                :missed-products="missedProducts"
                :dark-mode="darkMode"
            />
            <LoadingSpinner v-if="loading"/>
            <CompanyMissedLeadsSearchTableBody
                v-else-if="missedProducts.length > 0"
                :missed-products="missedProducts"
                :dark-mode="darkMode"
            />
            <div v-else class="flex items-center justify-center p-2">
                <p :class="[darkMode ? 'text-white': '']">No data found</p>
            </div>
            <div class="p-3 flex items-center justify-end gap-2">
                <div>
                    <span class="text-sm text-slate-500">Results Per Page</span>
                </div>
                <div>
                    <Dropdown
                        v-model="paginationData.per_page"
                        :dark-mode="darkMode"
                        placement="top"
                        :options="perPageOptions"
                        :selected="paginationData.per_page"
                    />
                </div>
                <Pagination
                    :dark-mode="darkMode"
                    :pagination-data="paginationData ?? {}"
                    show-pagination
                    @change-page="handlePaginationEvent"
                />
            </div>
        </div>
    </div>
</template>

<script>
import CustomButton from "../../Shared/components/CustomButton.vue";
import CustomInput from "../../Shared/components/CustomInput.vue";
import Filterable from "../../Shared/components/Filterables/Filterable.vue";
import ApiService from "../../Companies/services/api.js";
import Dropdown from "../../../components/LeadProcessing/components/Dropdown.vue";
import Pagination from "../../Shared/components/Pagination.vue";
import CompanyMissedLeadsSearchTableHeader from "../../CompanyMissedLeads/components/CompanyMissedLeadsSearchTableHeader.vue";
import CompanyMissedLeadsSearchTableBody from "../../CompanyMissedLeads/components/CompanyMissedLeadsSearchTableBody.vue";
import LoadingSpinner from "../../../components/LeadProcessing/components/LoadingSpinner.vue";
import AlertsContainer from "../../../components/LeadProcessing/components/AlertsContainer.vue";
import AlertsMixin from "../../../mixins/alerts-mixin.js";
import MultiSelect from "../components/MultiSelect.vue";
import SharedApiService from "../services/api";
import FilterableActivePills from "../components/Filterables/FilterableActivePills.vue";
import { DateTime } from "luxon";

export default {
    name: "CompanyMissedLeads",
    components: {
        FilterableActivePills,
        MultiSelect,
        AlertsContainer,
        LoadingSpinner,
        CompanyMissedLeadsSearchTableBody,
        CompanyMissedLeadsSearchTableHeader,
        Pagination,
        Dropdown,
        Filterable,
        CustomInput,
        CustomButton
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        apiDriver: {
            type: String,
            default: 'api',
        },
        companyId: {
            type: Number,
            required: true
        }
    },
    mixins: [AlertsMixin],
    data() {
        return {
            companiesAPI: ApiService.make(),
            sharedApi: SharedApiService.make(),

            filter: {},

            states: [],
            counties: [],
            timezones: [],

            loading: false,
            missedProducts: [],
            paginationData: {
                per_page: 25
            },
            perPageOptions:  [ { id: 25, name: "25" }, { id: 50, name: "50" }, { id: 100, name: "100" } ],
        }
    },
    computed: {
        //TODO: replace with filterable service
        customFilters() {
            const currentDate = DateTime.now();
            return [{
                "type": "date-range",
                "name": "Date Range",
                "id": "date-range",
                "clearable": false,
                "options": {
                    "from": {
                        "min": null,
                        "max": currentDate.toISO(),
                        "default": currentDate.toISO()
                    },
                    "to": {
                        "min": null,
                        "max": currentDate.toISO(),
                        "default": currentDate.toISO()
                    },
                    "presets": {
                        "Last Hour": {
                            from: currentDate.minus({ hours: 1 }).toISO(),
                            to: currentDate.toISO(),
                        },
                        "Last 4 Hours": {
                            from: currentDate.minus({ hours: 4 }).toISO(),
                            to: currentDate.toISO(),
                        },
                        "Last 8 Hours": {
                            from: currentDate.minus({ hours: 8 }).toISO(),
                            to: currentDate.toISO(),
                        },
                        "Today Only": {
                            from: currentDate.startOf("day").toISO(),
                            to: currentDate.toISO(),
                        },
                        "Yesterday Only": {
                            from: currentDate.minus({ days: 1 }).startOf("day").toISO(),
                            to: currentDate.startOf("day").toISO(),
                        },
                        "Last 24 Hours": {
                            from: currentDate.minus({ hours: 24 }).toISO(),
                            to: currentDate.toISO(),
                            default: true,
                        },
                        "Last Week": {
                            from: currentDate.minus({ weeks: 1 }).toISO(),
                            to: currentDate.toISO(),
                        },
                        "Last Month": {
                            from: currentDate.minus({ months: 1 }).toISO(),
                            to: currentDate.toISO(),
                        },
                        "This Calendar Month": {
                            from: currentDate.startOf("month").toISO(),
                            to: currentDate.toISO(),
                        },
                    }
                },
                "show": true
            }];
        }
    },
    mounted() {
        this.getMissedProducts({perPage: this.paginationData.per_page});
        this.getFilterOptions()
    },
    methods: {
        handleCustomUpdate(filter){
            Object.assign(this.filter, filter)
        },
        getFilterOptions(){
            this.sharedApi.getStates().then(resp => this.states = resp.data.data.map(state => Object.assign({id: state.stateAbbr, name: state.name})));
            this.sharedApi.getTimezones().then(resp => {
                this.timezones = Object.entries(resp.data.data.timezones)
                    .map(([timezoneName, timezoneVal]) => {
                        const date = new Date();
                        date.setUTCHours(date.getUTCHours() + timezoneVal);

                        return Object.assign({
                                id: timezoneVal,
                                name: `${timezoneName} (UTC ${timezoneVal >= 0 ? '+' : ''}${timezoneVal}:00)`
                            }
                        )
                    })
            })
        },
        formatFilter({ page = 1, perPage = 25 } = {}){
            const formattedFilter = {
                ...this.filter,
                page,
                perPage
            }

            if (this.filter['date-range']) {
                Object.assign(formattedFilter, {
                    'date_range': [this.filter['date-range']['from'] ?? null, this.filter['date-range']['to'] ?? null],
                    'date-range': undefined
                })
            }

            return formattedFilter
        },
        async getMissedProducts({ page = 1, perPage = 25 } = {}){
            this.loading = true;

            const searchFilter = this.formatFilter({page,perPage})

            try {
                const {data} = await this.companiesAPI.getCompanyMissedProducts(this.companyId, searchFilter)
                this.missedProducts = data.data.products.data
                this.paginationData = {
                    current_page: data.data.products.current_page,
                    first_page_url: data.data.products.first_page_url,
                    from: data.data.products.from,
                    last_page: data.data.products.last_page,
                    last_page_url: data.data.products.last_page_url,
                    links: data.data.products.links,
                    next_page_url: data.data.products.next_page_url,
                    path: data.data.products.path,
                    per_page: data.data.products.per_page,
                    prev_page_url: data.data.products.prev_page_url,
                    to: data.data.products.to,
                    total: data.data.products.total,
                }
            } catch (err){
                console.error(err)
            }

            this.loading = false
        },
        submitSearch() {
            this.getMissedProducts({page: 1, perPage: this.paginationData.per_page})
        },
        handlePaginationEvent({newPage}) {
            this.getMissedProducts({page: newPage, perPage: this.paginationData.per_page})
        },
        resetFilters() {
            this.filter = {
                states: [],
                counties: [],
                timezones: [],
                zipcode: null,
            }
            this.getMissedProducts({page: 1, perPage: 25})
        },

        getCounties(){
            if (this.filter?.states?.length > 0) {
                this.sharedApi.getCountiesByStates(this.filter.states)
                    .then(resp => {
                        this.counties = resp.data.data.counties.map(county => Object.assign({county, id: county.id, name: `${county.name} - ${county.state_abbr}`}));
                    })
            }
        }
    },
    watch: {
        'paginationData.per_page'(){
            this.getMissedProducts({page: 1, perPage: this.paginationData.per_page})
        },

        'filter.states': {
            handler() {
                this.getCounties()
            },
            deep: true,
        }
    }
}
</script>
