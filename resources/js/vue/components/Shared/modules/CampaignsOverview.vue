<template>
    <div class="border rounded-lg"  :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
        <div class="p-5">
            <div class="flex items-center justify-between">
                <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Campaigns Overview</h5>
            </div>
        </div>
        <company-campaign-budget-usage-table
            v-if="companyId"
            :company-id="companyId"
            :dark-mode="darkMode"
        />
        <div class="p-3"></div>
    </div>
</template>

<script>
import CompanyCampaignBudgetUsageTable from "./CompanyCampaignBudgetUsageTable.vue";

export default {
    name: "CampaignsOverview",
    components: {CompanyCampaignBudgetUsageTable},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        tableHeight: {
            type: String,
            default: 'h-100'
        },
        companyId: {
            type: Number,
            default: null
        },
        legacyCompanyId: {
            type: Number,
            default: null,
        },
    },
    data() {
        return {}
    }
}
</script>

<style scoped>

</style>
