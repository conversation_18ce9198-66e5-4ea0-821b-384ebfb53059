<template>
    <div class="grid grid-cols-12 gap-x-3 p-5 items-center relative"
         :class="{'text-grey-800 hover:bg-light-module border-light-border': !darkMode, 'text-grey-200 hover:bg-dark-module border-dark-border': darkMode }">
        <p class="text-slate-400 font-medium tracking-wide uppercase text-xs flex col-span-1">
            <input
                @click="toggleTaskSelected($event, task)"
                :class="[!darkMode ? 'hover:bg-grey-50 border-grey-200' : 'bg-dark-background hover:bg-dark-175 border-blue-400']"
                class="rounded-sm w-5 h-5 cursor-pointer border"
                type="checkbox"
                v-if="!readOnly"
            >
            <svg
                :class="{
                            'text-grey-200': !darkMode && !task.completed,
                            'text-grey-400': darkMode && !task.completed,
                            'text-green-450': !darkMode && task.completed,
                            'text-green-400': darkMode && task.completed,
                        }"
                class="fill-current flex-1" width="20" height="20" viewBox="0 0 20 20" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M10 0C4.486 0 0 4.486 0 10C0 15.514 4.486 20 10 20C15.514 20 20 15.514 20 10C20 4.486 15.514 0 10 0ZM8.001 14.413L4.288 10.708L5.7 9.292L7.999 11.587L13.293 6.293L14.707 7.707L8.001 14.413Z"/>
            </svg>
        </p>
        <p class="text-sm col-span-2" v-if="task?.name && task?.id"><a class="underline text-blue-550" :href="taskUrl">{{ task?.name ?? 'N/A' }}</a></p>
        <p class="text-sm col-span-2" v-else>{{ task?.name ?? 'N/A' }}</p>
        <p class="text-sm col-span-1">{{ task?.assigned_user ?? 'N/A' }}</p>
        <p class="text-sm col-span-1">{{ task?.category ?? 'N/A' }}</p>
        <p class="text-sm col-span-1">{{ task?.timezone ?? 'N/A' }}</p>
        <div class="text-sm col-span-1">
            <a v-if="getCompanyId && taskRelationIsNotEmpty" target="_blank" :href="`/companies/${getCompanyId}`"
               class="text-primary-500 font-bold flex items-center">
                {{ getTaskRelation }}
            </a>
            <a v-else-if="getCompanyId && !taskRelationIsNotEmpty" target="_blank" :href="`/companies/${getCompanyId}`"
               class="text-primary-500 font-bold flex items-center">
                Company Name Cannot Be Displayed
            </a>
            <a v-else-if="!getCompanyId && taskRelationIsNotEmpty" target="_blank" :href="`/companies/${getTaskRelation}?by_name=1`"
               class="text-primary-500 font-bold flex items-center">
                {{ getTaskRelation }}
            </a>
            <p v-else>N/A</p>
        </div>
        <p class="text-sm col-span-1">{{ getStateCityForDisplay }}</p>
        <p class="text-sm col-span-1">{{ task?.queue ?? 'N/A' }}</p>
        <p class="text-sm col-span-1">
            <span v-if="task.priority<=1"
                  class="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-green-100 text-green-800">Low</span>
            <span v-else-if="task.priority===2"
                  class="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-blue-100 text-blue-800">Medium</span>
            <span v-else-if="task.priority===3"
                  class="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-orange-100 text-orange-800">High</span>
            <span v-else
                  class="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-red-100 text-red-800">Urgent</span>
        </p>
        <p class="text-sm col-span-1">
            {{
                displayDate ? $filters.dateFromTimestamp(displayDate, "usWithTime") : "N/A"
            }}</p>

        <button v-if="task.action_id" @click="onMuteClick" class="mute-button cursor-pointer" :title="task.muted ? 'Unmute Task' : 'Mute Task'">
            <svg v-if="!task.muted" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="size-5">
                <path d="M10.047 3.062a.75.75 0 0 1 .453.688v12.5a.75.75 0 0 1-1.264.546L5.203 13H2.667a.75.75 0 0 1-.7-.48A6.985 6.985 0 0 1 1.5 10c0-.887.165-1.737.468-2.52a.75.75 0 0 1 .7-.48h2.535l4.033-3.796a.75.75 0 0 1 .811-.142ZM13.78 7.22a.75.75 0 1 0-1.06 1.06L14.44 10l-1.72 1.72a.75.75 0 0 0 1.06 1.06l1.72-1.72 1.72 1.72a.75.75 0 1 0 1.06-1.06L16.56 10l1.72-1.72a.75.75 0 0 0-1.06-1.06L15.5 8.94l-1.72-1.72Z" fill="#339AFF"/>
            </svg>

            <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="size-5">
                <path d="M10.5 3.75a.75.75 0 0 0-1.264-.546L5.203 7H2.667a.75.75 0 0 0-.7.48A6.985 6.985 0 0 0 1.5 10c0 .887.165 1.737.468 2.52.111.29.39.48.7.48h2.535l4.033 3.796a.75.75 0 0 0 1.264-.546V3.75ZM16.45 5.05a.75.75 0 0 0-1.06 1.061 5.5 5.5 0 0 1 0 7.778.75.75 0 0 0 1.06 1.06 7 7 0 0 0 0-9.899Z" fill="#339AFF"/>
                <path d="M14.329 7.172a.75.75 0 0 0-1.061 1.06 2.5 2.5 0 0 1 0 3.536.75.75 0 0 0 1.06 1.06 4 4 0 0 0 0-5.656Z" />
            </svg>
        </button>

        <div :id="`delete_button_${task?.id}`" class="py-2" v-if="showDelete" :class="{'cursor-pointer': showDelete}">
            <delete-task :task="task" :dark-mode="darkMode" @task-deleted="taskDeleted">
                <template v-slot:button>
                    <svg width="12" viewBox="0 0 9 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M1.5 2.75H1V9.9C1 10.1917 1.10536 10.4715 1.29289 10.6778C1.48043 10.8841 1.73478 11 2 11H7C7.26522 11 7.51957 10.8841 7.70711 10.6778C7.89464 10.4715 8 10.1917 8 9.9V2.75H1.5ZM6.809 1.1L6 0H3L2.191 1.1H0V2.2H9V1.1H6.809Z"
                            fill="#339AFF"/>
                    </svg>
                </template>
            </delete-task>
        </div>
        <a :href="taskUrl"
             class="absolute h-full mr-6 w-8 flex items-center justify-center right-0 inset-y-0" v-if="showArrow"
             :class="{'cursor-pointer': showArrow}">
            <svg width="7" height="12" viewBox="0 0 7 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                      d="M0.292893 0.292893C0.683417 -0.0976311 1.31658 -0.0976311 1.70711 0.292893L6.70711 5.29289C7.09763 5.68342 7.09763 6.31658 6.70711 6.70711L1.70711 11.7071C1.31658 12.0976 0.683417 12.0976 0.292893 11.7071C-0.0976311 11.3166 -0.0976311 10.6834 0.292893 10.2929L4.58579 6L0.292893 1.70711C-0.0976311 1.31658 -0.0976311 0.683417 0.292893 0.292893Z"
                      fill="#0081FF"/>
            </svg>
        </a>
    </div>
</template>

<script>
import DeleteTask from "../../../Tasks/DeleteTask.vue";

/**
 * @typedef task
 * @property {number} id
 * @property {string} name
 * @property {string} assigned_user
 * @property {string} category
 * @property {string} timezone
 * @property {string} relation
 * @property {boolean} auth_user_is_assigned
 * @property {string} completed_at
 * @property {string} due
 * @property {number} priority
 * @property {string} queue
 * @property {string} event.lead.address.state
 * @property {string} event.lead.address.city
 * @property {string} event.company.company_name
 * @property {string} event.campaign.name
 */

export default {
    name: "Task.vue",
    emits: ['delete', 'click', 'select', 'deselect', 'mute-task', 'unmute-task'],
    components: {DeleteTask},
    props: {
        task: {
            type: Object,
            required: true,
        },
        filterTab: {
            type: String,
            required: true
        },
        darkMode: {
            type: Boolean,
            required: true
        },
        readOnly: {
            type: Boolean,
            required: true
        }
    },
    computed: {
        showDelete() {
            return this.taskIsClickable && !this.readOnly && (this.task?.manual ?? false) && !(this.task?.completed ?? false);
        },
        showArrow() {
            return this.taskIsClickable && !this.readOnly;
        },
        taskIsClickable() {
            return (this.task?.auth_user_is_assigned ?? false) && this.task.hasOwnProperty('id');
        },
        /**
         *
         * @return {null|string|string|*}
         */
        displayDate() {
            if (this.filterTab === 'Completed') {
                if (typeof this.task?.completed_at === 'number' || typeof this.task?.completed_at === 'string') {
                    return this.task?.completed_at;
                } else if (typeof this.task?.due === 'number' || typeof this.task?.due === 'string') {
                    return this.task?.due;
                }
            }

            return this.task?.due;
        },
        getCompanyId() {
            return this.task.payload?.company_id ?? this.task.event?.company?.id ?? null;
        },
        getTaskRelation() {
            if (this.task.manual) return this.task.payload?.company_name ?? 'N/A';

            return this.task?.event?.company?.name ?? this.task?.event?.campaign?.name ?? 'N/A';
        },
        taskRelationIsNotEmpty() {
            return this.getTaskRelation !== 'N/A';
        },
        getStateCityForDisplay() {
            if (!this.task.event?.lead?.address) return '';

            return `${this.task.event.lead.address.state} : ${this.task.event.lead.address.city}`;
        },
        taskUrl() {
            return `/tasks/queue?tasks=${this.task.id}`;
        }
    },
    methods: {
        taskDeleted() {
            this.$emit('delete');
        },
        toggleTaskSelected(event, task) {
            if (event.target.checked) {
                this.$emit('select', task);
            } else {
                this.$emit('deselect', task);
            }

            this.taskSelected = this.selectedTasks.length === this.tasks.length;
        },
        onMuteClick() {
            if (this.task.muted) {
                this.$emit('unmute-task', this.task.action_id);
            } else {
                this.$emit('mute-task', this.task.action_id);
            }
        },
    }
}
</script>

<style scoped>

</style>
