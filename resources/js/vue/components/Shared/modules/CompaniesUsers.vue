<template>
    <div :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}"
         class="border rounded-lg">
        <div class="px-5 pt-5 pb-3 flex items-center justify-between">
            <h5 class="text-primary-500 text-sm uppercase font-bold leading-tight">Users</h5>
            <ActionsHandle
                :dark-mode="darkMode"
                :no-custom-action="false"
                no-edit-button
                no-delete-button
                :custom-actions="usersActions"
                @add-user="toggleAddUserModal(true)"></ActionsHandle>
        </div>
        <div class="px-5 pb-5">
            <div class="w-48">
                <Dropdown v-model="selectedFilterUserOption" :dark-mode="darkMode" :options="filterUsersOptions"
                          :selected="selectedFilterUserOption"/>
            </div>
        </div>
        <div class="grid grid-cols-11 gap-x-3 mb-2 px-5">
            <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-3">Name / Email</p>
            <p class="text-slate-400 font-medium tracking-wide uppercase text-xs text-center">Status</p>
            <p class="text-slate-400 font-medium tracking-wide uppercase text-xs text-center">Register Date</p>
            <p class="text-slate-400 font-medium tracking-wide uppercase text-xs text-center">ID</p>
            <p class="text-slate-400 font-medium tracking-wide uppercase text-xs text-center col-span-2">Created By</p>
            <p class="text-slate-400 font-medium tracking-wide uppercase text-xs text-center col-span-2">Updated By</p>
            <p class="text-slate-400 font-medium tracking-wide uppercase text-xs"></p>
        </div>
        <div v-if="loading" class="my-6 py-6">
            <LoadingSpinner></LoadingSpinner>
        </div>
        <div v-else
             :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-40 border-dark-border': darkMode}"
             class="border-t border-b h-80 overflow-y-auto">
            <div>
                <div v-for="user in filteredUsers"
                     :key="user.id" :class="{'text-slate-900 hover:bg-light-module border-light-border': !darkMode, 'text-slate-100 hover:bg-dark-module border-dark-border': darkMode}"
                     class="grid grid-cols-11 gap-x-3 border-b px-5 py-3 items-center">
                    <div class="col-span-3 gap-2 flex flex-col truncate">
                        <p class="text-sm">
                            <a :href="`/company-users/${user.id}`" class="text-primary-500 font-bold flex items-center" target="_blank">
                                {{ user.name }}
                            </a>
                        </p>
                        <div class="flex items-center gap-1">
                            <simple-icon
                                v-if="user.email && user.email.trim().length > 0"
                                @click="$emit('email', {email: user.email ,id: user.id})"
                                :icon="simpleIcon.icons.ENVELOPE"
                                clickable
                                :color="simpleIcon.colors.BLUE"
                            />
                            <a
                                @click="$emit('email', {email: user.email ,id: user.id})"
                                class="text-primary-500 font-bold flex items-center text-sm" href="javascript:void(0)">
                                {{ user.email }}
                            </a>
                        </div>
                    </div>
                    <div class="text-sm truncate text-center">
                        <Badge v-if="user.status === 1" :dark-mode="darkMode" color="green">Active</Badge>
                        <Badge v-else :dark-mode="darkMode" color="red">Inactive</Badge>
                    </div>
                    <p class="text-sm truncate text-center">
                        {{ $filters.dateFromTimestamp(user.date_registered) }}
                    </p>
                    <p class="text-sm truncate text-center">
                        {{ user.id }}
                    </p>
                    <div class="col-span-2 truncate text-center">
                        <p class="text-sm">{{ user.created_by_name }}</p>
                        <p class="text-sm truncate text-slate-400">{{ user.created_by_email }}</p>
                    </div>
                    <div class="col-span-2 truncate text-center">
                        <p class="text-sm">{{ user.updated_by_name }}</p>
                        <p class="text-sm truncate text-slate-400">{{ user.updated_by_email }}</p>
                    </div>
                    <!-- todo disable editing basic on user permissions -->
                    <ActionsHandle :dark-mode="darkMode" @delete="openDeleteUserModal(user)"
                                   @edit="openEditUserModal(user)"/>
                </div>
            </div>
        </div>
        <div class="p-3"></div>
        <!--    Edit/Create User modal    -->
        <Modal
            v-if="editUserModal || addUserModal"
            :close-text="'Cancel'"
            :confirm-text="confirmText"
            :dark-mode="darkMode"
            :disable-confirm="disableConfirm"
            @close="closeEditUserModal"
            @confirm="addUserModal ? createUser() : updateUser()"
        >
            <template v-slot:header>
                <h4 class="text-xl font-medium">{{ addUserModal ? 'Create' : 'Edit' }} User</h4>
            </template>
            <template v-slot:content>
                <div class="grid grid-cols-2 gap-3 mb-4">
                    <div>
                        <custom-input
                            v-model="user.first_name"
                            :dark-mode="darkMode"
                            label="*First Name"
                        />
                    </div>
                    <div>
                        <custom-input
                            v-model="user.last_name"
                            :dark-mode="darkMode"
                            label="Last Name"
                        />
                    </div>
                    <div>
                        <custom-input
                            v-model="user.cell_phone"
                            :dark-mode="darkMode"
                            label="Cell Phone"
                        />
                    </div>
                    <div>
                        <custom-input
                            v-model="user.office_phone"
                            :dark-mode="darkMode"
                            label="Office Phone"
                        />
                    </div>
                    <div>
                        <label
                            :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="block mb-1">
                            Notes
                        </label>
                        <textarea
                            v-model="user.notes"
                            :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}"
                            class="min-h-88 w-full border rounded pl-4 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                            placeholder="Enter notes..."
                            type="text"/>
                    </div>
                    <div>
                        <label class="font-medium mb-1 block">Status</label>
                        <Dropdown v-model="user.status" :dark-mode="darkMode" :options="statusOptions"
                                  :selected="user.status"/>
                    </div>
                    <div>
                        <p class="uppercase font-semibold text-xs mb-2"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            User Type
                        </p>
                        <Dropdown
                            disabled
                            v-model="user.is_contact"
                            :dark-mode="darkMode"
                            :options="isContactOptions"
                            :placeholder="'User'"
                        />
                    </div>
                    <div v-if="!addUserModal" class="flex items-center justify-center gap-x-4">
                        <custom-button
                            color="primary-outline"
                            @click="openResetPasswordModal(user.userid)"
                        >
                            Reset Password
                        </custom-button>
                        <custom-button
                            color="primary-outline"
                            @click="openUpdatePasswordModal(user.userid)"
                        >
                            Update Password
                        </custom-button>
                    </div>
                    <div v-if="addUserModal" class="col-span-2">
                        <!--    Hidden fields to catch auto-complete scanning    -->
                        <input style="display:none" tabindex="-1" type="email"/>
                        <input style="display:none" tabindex="-1" type="password"/>
                        <!--     -->
                        <custom-input
                            v-model="user.email"
                            :dark-mode="darkMode"
                            auto-complete="new-password"
                            label="*Email"
                            type="email"
                        />
                    </div>
                    <div v-if="!addUserModal" class="col-span-full">
                        <p class="uppercase font-semibold text-xs mb-2"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Must reset password
                        </p>
                        <custom-checkbox
                            :dark-mode="darkMode"
                            name="Force user to reset password on login"
                            v-model="user.must_reset_password"
                        />
                    </div>
                    <div v-if="addUserModal" class="col-span-2">
                        <custom-input-password
                            v-model:confirm-password-value="user.confirmPassword"
                            v-model:password-value="user.password"
                            :dark-mode="darkMode"
                            :new-password="true"
                        />
                    </div>
                    <div class="col-span-2 mt-4">
                        <p class="uppercase font-semibold text-xs mb-2"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Configurable Fields
                        </p>
                    </div>
                    <div v-for="(field, id) in user.user_fields['global']">
                        <p class="uppercase font-semibold text-xs mb-2"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            {{ field.name }}
                        </p>
                        <custom-input
                            v-if="field.type !== 'Boolean'"
                            v-model="user.user_fields['global'][id].value"
                            :dark-mode="darkMode"
                            :type="field.type"
                        />
                        <toggle-switch
                            v-if="field.type ==='Boolean'"
                            v-model="user.user_fields['global'][id].value"
                            :dark-mode="darkMode" />
                    </div>
                </div>
            </template>
        </Modal>
        <!--    Delete User modal    -->
        <Modal
            v-if="deleteUserModal"
            :close-text="'Cancel'"
            :confirm-text="deletingUser ? 'Deleting...' : 'Delete'"
            :dark-mode="darkMode"
            :disable-confirm="deletingUser"
            :small="true"
            @close="closeDeleteUserModal"
            @confirm="deleteUser"
        >
            <template v-slot:header>
                <h4 class="text-xl font-medium inline-flex items-center">
                    <svg class="mr-2 fill-current text-red-500" fill="none" height="19" viewBox="0 0 20 19" width="20"
                         xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M10.8849 0.4905C10.5389 -0.1635 9.46294 -0.1635 9.11694 0.4905L0.116935 17.4905C0.0359187 17.6429 -0.00424573 17.8136 0.00035519 17.9861C0.00495611 18.1586 0.0541655 18.327 0.143189 18.4749C0.232212 18.6227 0.358012 18.7449 0.508333 18.8297C0.658653 18.9145 0.828366 18.9588 1.00094 18.9585H19.0009C19.1734 18.9589 19.343 18.9145 19.4932 18.8298C19.6434 18.7451 19.7691 18.6229 19.8581 18.4752C19.947 18.3274 19.9961 18.1591 20.0007 17.9867C20.0052 17.8144 19.965 17.6437 19.8839 17.4915L10.8849 0.4905ZM11.0009 15.9585H9.00094V13.9585H11.0009V15.9585ZM9.00094 11.9585V6.9585H11.0009L11.0019 11.9585H9.00094Z"/>
                    </svg>
                    Delete User
                </h4>
            </template>
            <template v-slot:content>
                Are you sure you want to delete this user?
            </template>
        </Modal>
        <!--    Reset User password modal    -->
        <Modal
            v-if="resetUserPasswordModal"
            :close-text="'Cancel'"
            :confirm-text="passwordResetResponse ? 'OK' : resettingPassword ? 'Resetting...' : 'Reset'"
            :dark-mode="darkMode"
            :disable-confirm="resettingPassword"
            :small="true"
            @close="closeResetPasswordModal"
            @confirm="passwordResetResponse ? closeResetPasswordModal() : resetCompanyUserPassword()"
        >
            <template v-slot:header>
                <h4 class="text-xl font-medium inline-flex items-center">
                    <svg class="mr-2 fill-current text-red-500" fill="none" height="19" viewBox="0 0 20 19" width="20"
                         xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M10.8849 0.4905C10.5389 -0.1635 9.46294 -0.1635 9.11694 0.4905L0.116935 17.4905C0.0359187 17.6429 -0.00424573 17.8136 0.00035519 17.9861C0.00495611 18.1586 0.0541655 18.327 0.143189 18.4749C0.232212 18.6227 0.358012 18.7449 0.508333 18.8297C0.658653 18.9145 0.828366 18.9588 1.00094 18.9585H19.0009C19.1734 18.9589 19.343 18.9145 19.4932 18.8298C19.6434 18.7451 19.7691 18.6229 19.8581 18.4752C19.947 18.3274 19.9961 18.1591 20.0007 17.9867C20.0052 17.8144 19.965 17.6437 19.8839 17.4915L10.8849 0.4905ZM11.0009 15.9585H9.00094V13.9585H11.0009V15.9585ZM9.00094 11.9585V6.9585H11.0009L11.0019 11.9585H9.00094Z"/>
                    </svg>
                    Reset User Password
                </h4>
            </template>
            <template v-slot:content>
                {{
                    passwordResetResponse || `Are you sure you want to reset this User's password? An email will be sent to ${user.email}`
                }}
            </template>
        </Modal>
        <!--    Update User password modal    -->
        <Modal
            v-if="updateUserPasswordModal"
            :close-text="'Cancel'"
            :confirm-text="savingUser ? 'Saving...' : 'Update'"
            :dark-mode="darkMode"
            :disable-confirm="savingUser"
            :small="true"
            @close="closeUpdatePasswordModal"
            @confirm="updateCompanyUserPassword()"
        >
            <template v-slot:header>
                <h4 class="text-xl font-medium inline-flex items-center">
                    <svg class="mr-2 fill-current text-red-500" fill="none" height="19" viewBox="0 0 20 19" width="20"
                         xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M10.8849 0.4905C10.5389 -0.1635 9.46294 -0.1635 9.11694 0.4905L0.116935 17.4905C0.0359187 17.6429 -0.00424573 17.8136 0.00035519 17.9861C0.00495611 18.1586 0.0541655 18.327 0.143189 18.4749C0.232212 18.6227 0.358012 18.7449 0.508333 18.8297C0.658653 18.9145 0.828366 18.9588 1.00094 18.9585H19.0009C19.1734 18.9589 19.343 18.9145 19.4932 18.8298C19.6434 18.7451 19.7691 18.6229 19.8581 18.4752C19.947 18.3274 19.9961 18.1591 20.0007 17.9867C20.0052 17.8144 19.965 17.6437 19.8839 17.4915L10.8849 0.4905ZM11.0009 15.9585H9.00094V13.9585H11.0009V15.9585ZM9.00094 11.9585V6.9585H11.0009L11.0019 11.9585H9.00094Z"/>
                    </svg>
                    Update User Password
                </h4>
            </template>
            <template v-slot:content>
                <custom-input-password
                    v-model:confirm-password-value="user.confirmPassword"
                    v-model:password-value="user.password"
                    :dark-mode="darkMode"
                    :new-password="true"
                />
            </template>
        </Modal>
        <alerts-container v-if="error !== null" :alert-type="'error'" :dark-mode="darkMode"
                          :text="error"></alerts-container>
    </div>
</template>

<script>
import Dropdown from "../../Shared/components/Dropdown.vue";
import ActionsHandle from "../../Shared/components/ActionsHandle.vue";
import Modal from "../components/Modal.vue";
import SharedApiService from "../services/api";
import LoadingSpinner from "../components/LoadingSpinner.vue";
import AlertsContainer from "../components/AlertsContainer.vue";
import CustomInput from "../components/CustomInput.vue";
import CustomInputPassword from "../components/CustomInputPassword.vue";
import CustomButton from "../components/CustomButton.vue";
import ToggleSwitch from "../components/ToggleSwitch.vue";
import CustomCheckbox from "../SlideWizard/components/CustomCheckbox.vue";
import EmailLink from "../../Mailbox/EmailLink.vue";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import Badge from "../components/Badge.vue";

export default {
    name: "CompaniesUsers",
    components: {
        Badge,
        SimpleIcon,
        EmailLink,
        CustomCheckbox,
        ToggleSwitch, CustomButton, CustomInputPassword, CustomInput, Modal, ActionsHandle, Dropdown, LoadingSpinner, AlertsContainer},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        hasEditRights: {
            type: Boolean,
            default: false,
        },
        companyId: {
            type: Number,
            default: null
        },
        companyDashboardType: {
            type: String,
            default: null,
        },
        configurableFields: {
            type: Array,
            default: null,
        }
    },
    emits: ['email'],
    data() {
        return {
            api: SharedApiService.make(),
            statusOptions: [
                { id: 1, name: 'Active'},
                { id: 0, name: 'Inactive'}
            ],
            selectedStatusOption: 1,
            selectedFilterUserOption: 0,
            filterUsersOptions: [
                {id: 0, name: 'All'},
                {id: 1, name: 'Active'},
                {id: 2, name: 'Inactive'}
            ],
            users: [],
            user: {
                userid: undefined,
                first_name: undefined,
                last_name: '',
                email: '',
                status: 1,
                cell_phone: undefined,
                office_phone: undefined,
                notes: null,
                password: '',
                confirmPassword: '',
                is_contact: false,
                user_fields: { global: [] },
            },
            deleteUserId: null,
            modifyUserPasswordId: null,
            editUserModal: false,
            deleteUserModal: false,
            loading: false,
            savingUser: false,
            deletingUser: false,
            addUserModal: false,
            resetUserPasswordModal: false,
            resettingPassword: false,
            passwordResetResponse: null,
            updateUserPasswordModal: false,
            error: null,
            isContactOptions: [ { id: 1, name: 'Contact' }, { id: 0, name: 'User' } ],
            usersActions: [
                {event: 'add-user', name: 'Add User'},
            ]
        }
    },
    created() {
        if (this.companyId) this.getCompanyUsers();
        // this.fetchAllUsers();
    },
    computed: {
        simpleIcon() {
            return useSimpleIcon()
        },
        filteredUsers: function () {
            switch (this.selectedFilterUserOption) {
                case 1:
                    return this.users.filter(user => user.status === 1);
                case 2:
                    return this.users.filter(user => user.status === 0);
                case 0:
                default:
                    return {...this.users};
            }
        },
        confirmText: function () {
            return this.savingUser ?
                'Saving...'
                : this.addUserModal
                    ? 'Create'
                    : 'Update';
        },
        disableConfirm: function () {
            return this.savingUser || !this.user.first_name || (!this.user.cell_phone && !this.user.office_phone && !this.user.email) || (this.addUserModal && (!this.user.password || !this.user.confirmPassword));
        },
        userMapping() {
            const mapping = {};
            for (const user of this.allUsers) {
                mapping[user.id] = user;
            }
            return mapping;
        }
    },
    methods: {
        getCompanyUsers() {
            this.loading = true;
            this.api.getCompanyUsers(this.companyId)
                .then(resp => this.users = resp.data.data.users)
                .finally(() => this.loading = false);
        },
        openEditUserModal(user) {
            this.user.userid = user.id;
            this.user.first_name = user.first_name;
            this.user.last_name = user.last_name;
            this.user.status = user.status;
            this.user.cell_phone = user.cell_phone;
            this.user.office_phone = user.office_phone;
            this.user.email = user.email;
            this.user.notes = user.notes;
            this.user.is_contact = false;
            this.user.user_fields = user.user_fields;
            this.user.must_reset_password = user.must_reset_password;
            this.editUserModal = true;
            this.addUserModal = false;
        },
        closeEditUserModal() {
            this.editUserModal = false;
            this.addUserModal = false;
            this.user = {
                userid: undefined,
                first_name: undefined,
                last_name: undefined,
                status: 1,
                cell_phone: undefined,
                office_phone: undefined,
                notes: null,
                is_contact: false,
                must_reset_password: false,
                user_fields: { global: this.configurableFields }
            };
            this.clearError();
        },
        updateUser() {
            this.savingUser = true;
            this.clearError();
            this.api.updateCompanyUser(this.companyId, this.user.userid, this.user)
                .then(() => {
                    this.closeEditUserModal();
                    this.getCompanyUsers();
                })
                .catch(e => this.error = e.response.data.message + '. Remove the user from all campaign delivery contacts and retry.')
                .finally(() => this.savingUser = false);
        },
        createUser() {
            if (this.validateNewUser()) {
                this.savingUser = true;
                this.clearError();
                this.api.createCompanyUser(this.companyId, this.user).then(response => {
                    if (response.data.data.status) {
                        this.closeEditUserModal();
                        this.getCompanyUsers();
                    }
                    else this.error = `An unknown error occurred.`;
                }).catch(err => {
                    this.error = err.response.data?.message || err.message || `An unknown error occurred.`;
                }).finally(() => {
                    this.savingUser = false;
                })
            }
        },
        validateNewUser() {
            const invalidMessages = [];
            const requiredFields = [ 'first_name', 'password', 'confirmPassword', 'email' ];
            if (!this.user.cell_phone && !this.user.office_phone && !this.user.email) invalidMessages.push(`User must have one valid form of contact.`);
            requiredFields.forEach(field => {
                if (!this.user[field]) invalidMessages.push(`The ${this.$filters.toProperCase(field)} field is required.`);
            });
            if (this.user.password !== this.user.confirmPassword) invalidMessages.push(`Password fields do not match.`);
            if (invalidMessages.length) {
                this.error = invalidMessages.join('\n');
                return false;
            }
            else return true;
        },
        deleteUser() {
            this.deletingUser = true;

            this.clearError();
            this.api.deleteCompanyUser(this.companyId, this.deleteUserId)
                .then(() => {
                    this.getCompanyUsers();
                    this.closeDeleteUserModal();
                })
                .catch(e => this.error = e.response.data.message + '. Remove the user from all campaign delivery contacts and retry.')
                .finally(() => this.deletingUser = false);
        },
        openDeleteUserModal(user) {
            this.deleteUserModal = true;
            this.deleteUserId = user.id;
        },
        closeDeleteUserModal() {
            this.deleteUserModal = false;
            this.deleteUserId = null;
            this.clearError();
        },
        toggleAddUserModal(showModal) {
            this.addUserModal = showModal == null
                ? !this.addUserModal
                : !!showModal;
            this.editUserModal = !this.addUserModal;
        },
        openResetPasswordModal(userId) {
            this.modifyUserPasswordId = userId;
            this.resetUserPasswordModal = true;
            this.passwordResetResponse = null;
            this.clearError();
        },
        closeResetPasswordModal() {
            this.resetUserPasswordModal = false;
            this.modifyUserPasswordId = null;
            this.passwordResetResponse = null;
            this.clearError();
        },
        resetCompanyUserPassword() {
            this.resettingPassword = true;
            this.api.resetCompanyUserPassword(this.companyId, this.modifyUserPasswordId).then(response => {
                if (response.data.data.status != null) {
                    if (response.data.data.message) {
                        this.passwordResetResponse = response.data.data.message;
                    } else {
                        this.closeResetPasswordModal();
                    }
                }
                else this.error = `An error occurred resetting the User's password.`;
            }).catch(err => {
                this.error = err.response?.data?.message || err.message || `An unknown error occurred resetting the User's password.`;
            }).finally(() => {
                this.resettingPassword = false;
            });
        },
        openUpdatePasswordModal(userId) {
            this.modifyUserPasswordId = userId;
            this.updateUserPasswordModal = true;
            this.clearError();
        },
        closeUpdatePasswordModal() {
            this.modifyUserPasswordId = null;
            this.updateUserPasswordModal = false;
            this.user.password = '';
            this.user.confirmPassword = '';
            this.clearError();
        },
        updateCompanyUserPassword() {
            if (!this.user.password || this.user.password !== this.user.confirmPassword) {
                this.error = "Password is required, and must match confirm password.";
                return;
            }
            this.savingUser = true;
            this.api.updateCompanyUserPassword(this.companyId, this.modifyUserPasswordId, {
                'password': this.user.password,
            }).then(response => {
                if (response.data.data.status) {
                    this.closeUpdatePasswordModal();
                }
                else this.error = `An error occurred updating the User's password.`;
            }).catch(err => {
                this.error = err.response?.data?.message || err.message || `An unknown error occurred resetting the User's password.`;
            }).finally(() => {
                this.savingUser = false;
            });
        },
        clearError() {
            this.error = null;
        },
    },
    watch: {
        configurableFields(newVal) {
            this.user.user_fields.global = newVal
        }
    }
}
</script>

<style scoped>

</style>
