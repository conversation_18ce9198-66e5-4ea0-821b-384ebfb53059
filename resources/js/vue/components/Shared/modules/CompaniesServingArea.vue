<template>
    <div id="companies-servicing-area-section" class="grid gap-4" :class="[selectedCompany !== null ? 'grid-cols-3' : 'grid-cols-4']">
        <div id="companies-servicing-area-box" class="border rounded-md h-full"
             :class="[selectedCompany !== null ? 'col-span-1' : 'col-span-3',darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
            <CompaniesServingAreaHeader :selected-company="selectedCompany" :dark-mode="darkMode"
                                        @update:selected-company="selectedCompany = $event"
                                        @apply="getCompanies"
                                        :amount-of-purchased-leads="amountOfPurchasedLeads"
                                        :purchased-leads-condition="purchasedLeadsCondition"
                                        :active-filters="activeFiltersCount"
                                        @toggle-sort="sortCompanies($event)"
                                        :user="user"
                                        :companies="filteredCompanies"
            ></CompaniesServingAreaHeader>
            <CompaniesServingAreaBody v-model:loading="loading" :companies="filteredCompanies" :dark-mode="darkMode"
                                      @update:call-office="callOffice"
                                      :selected-company="selectedCompany"
                                      @update:selected-company="selectedCompany = $event"
                                      :amount-of-purchased-leads="amountOfPurchasedLeads"
                                      @update:activities="activityReload++"
                                      :height="height"
                                      @refresh="load"
            ></CompaniesServingAreaBody>
            <div class="p-3"></div>
        </div>
        <GooglePlacesSearch v-if="location && (selectedCompany === null) && industry" :dark-mode="darkMode" :location="location" :zip-code="zipCode" :industry="industry"></GooglePlacesSearch>
        <company-contacts :get-contacts-on-mounted="false" :table-height="'h-[37rem]'" v-if="selectedCompany !== null"
                          :has-edit-rights="true" :dark-mode="darkMode" :show-add-contact-button="true"
                          :company-id="selectedCompany"></company-contacts>
        <activity-page v-if="selectedCompany !== null" :dark-mode="darkMode"
                       :company-id="selectedCompany" :key="activityReload">
            <template v-slot:extra-filters>
                <div class="col-span-3 flex items-center">
                    <toggle-switch :dark-mode="darkMode" v-model="onlyShowCallsLongerThanOneMinute"></toggle-switch>
                    <label :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="text-sm font-medium ml-2">
                        Only Show Calls Longer Than 1 Minute</label>
                </div>
            </template>
        </activity-page>

        <AlertsContainer
            v-if="alertActive"
            :alert-type="alertType"
            :text="alertText"
            :dark-mode="darkMode"
        />
    </div>
</template>

<script>
import SharedApiService from "../services/api";
import DispatchesGlobalEventsMixin from "../../../mixins/dispatches-global-events-mixin";
import Activity from "./Activity.vue";
import CompanyContacts from "./Contacts.vue";
import ActivityPage from "../../Companies/components/ActivityPage.vue";
import ToggleSwitch from "../components/ToggleSwitch.vue";
import CompaniesServingAreaHeader from "./CompaniesServingArea/CompaniesServingAreaHeader.vue";
import CompaniesServingAreaBody from "./CompaniesServingArea/CompaniesServingAreaBody.vue";
import {sortCompanies as globalSortCompanies} from "../../../../modules/company-servicing-area-sorter";
import {mapState, mapWritableState} from "pinia";
import {useCompanyServicingAreaSorterStore} from "../../../../stores/company-servicing-area-sorter.store";
import {useActivityTabStore} from "../../../../stores/activity-tab.store";
import Api from "../../Companies/services/api";
import {useActivityPageStore} from "../../../../stores/activity-page.store";
import GooglePlacesSearch from "./GooglePlacesSearch.vue";
import {useContactsStore} from "../../../../stores/contacts.store";
import {slugify} from "../services/strings";
import {CommunicationRelationTypes} from "../../Communications/enums/communication";
import { useCompaniesServingAreaStore } from './CompaniesServingArea.service.js'
import { ApiFactory } from '../../CompaniesServicingArea/services/api/factory'
import { ApiFactory as LeadApiFactory } from '../../LeadProcessing/services/api/factory'
import AlertsMixin from '../../../mixins/alerts-mixin'
import AlertsContainer from '../components/AlertsContainer.vue'

/**
 * @typedef company
 * @property {number} companyid
 * @property {number} mi_company.id
 */

export default {
    name: "CompaniesServingArea",
    components: {
        AlertsContainer,
        GooglePlacesSearch,
        CompaniesServingAreaBody,
        CompaniesServingAreaHeader,
        ToggleSwitch,
        ActivityPage,
        CompanyContacts,
        Activity
    },
    props: {
        location: {
            type: Object,
            default: null
        },
        missedRevenueIndustry: {
            type: String,
            default: null
        },
        leadId: {
            type: Number,
            default: null
        },
        darkMode: {
            type: Boolean,
            default: false,
        },
        task: {
            type: Object,
            default: {},
            validator: (o) => {
                return o.hasOwnProperty('id') && _.isInteger(o.id);
            }
        },
        height: {
            type: String,
            default: 'h-[33rem]',
        },
        user: {
            type: Object,
            default: {}
        }
    },
    computed: {
        companiesServicingAreaApi() {
            return ApiFactory.makeApiService("api");
        },
        ...mapState(useCompanyServicingAreaSorterStore, {
            storeSortObject: 'sorters'
        }),
        ...mapWritableState(useActivityTabStore, {
            activityTabFilters: 'filters'
        }),
        ...mapWritableState(useContactsStore, {
            contacts: 'contacts'
        }),
        ...mapWritableState(useCompaniesServingAreaStore, {
            companiesServingAreaFilters: 'filters',
            companiesServingAreaPresets: 'presets',
        }),
        ...mapState(useCompaniesServingAreaStore, {
            keyDataFilters: 'keyDataFilters',
            activeFiltersCount: 'activeFiltersCount',
        }),
        amountOfPurchasedLeads() {
            const filters = this.keyDataFilters;

            let leadsPurchasedFilterIsActive = false;

            if (filters) {
                leadsPurchasedFilterIsActive = filters['company-servicing-area-amount-of-leads-purchased']?.active ?? false;
            }

            return leadsPurchasedFilterIsActive;
        },
    },
    mixins: [DispatchesGlobalEventsMixin, AlertsMixin],
    data() {
        return {
            companies: [],
            filteredCompanies: [],
            sharedApi: SharedApiService.make(),
            api: Api.make(),
            selectedCompany: null,
            lead: null,
            loading: false,
            leadApi: LeadApiFactory.makeApiService("api"),
            zipCode: null,
            industry: null,
            expandedContacts: [],
            gettingCompanies: false,
            purchasedLeadsCondition: null,
            activityReload: 0,
            onlyShowCallsLongerThanOneMinute: true,
        }
    },
    mounted() {
        this.loading = true;

        this.getFilters().then(() => {
            this.load().then(() => {
                const check = localStorage.getItem('only-show-calls-longer-than-one-minute-in-activity-feed')

                this.onlyShowCallsLongerThanOneMinute = check ? check.toLowerCase() === 'true' : true

                this.buildQueryParamsForCallsOverAMinuteFilter(this.onlyShowCallsLongerThanOneMinute)

                this.loading = false
            }).catch(e => {
                this.showAlert('error', e.message)
                this.loading = false
            })
        }).catch(e => {
            this.showAlert('error', e.message)
            this.loading = false
        })
    },
    watch: {
        leadId(newVal, oldVal) {
            if (newVal !== oldVal)
                this.load();
        },
        location(newVal, oldVal) {
            if (newVal !== oldVal)
                this.load();
        },
        selectedCompany(to, from) {
            if (to) {
                if (!_.isInteger(to)) {
                    throw new TypeError(`"to" must be an integer. ${to} passed.`)
                }

                this.buildQueryParamsForCallsOverAMinuteFilter(this.onlyShowCallsLongerThanOneMinute);
            }
        },
        onlyShowCallsLongerThanOneMinute(to, from) {
            if (_.isBoolean(to) && this.selectedCompany) {
                this.buildQueryParamsForCallsOverAMinuteFilter(to);

                localStorage.setItem('only-show-calls-longer-than-one-minute-in-activity-feed', to)

                this.updateActivityPageAndActivityTab();
            } else {
                if (this.selectedCompany) {
                    console.error(`Could not toggle call list. "onlyShowCallsLongerThanOneMinute" must be a boolean value. ${to} passed.`);
                }
            }
        }
    },
    methods: {
         sortPresets (presetArray) {
            return presetArray.sort((a, b) => a.name > b.name ? 1 : 0)
        },
        async getFilters () {
            await this.companiesServicingAreaApi.getFilterOptions().then(resp => {
                console.log('test', resp)
                if (resp.data?.data?.status) {
                    this.companiesServingAreaFilters = resp?.data?.data?.filter_options
                    this.companiesServingAreaPresets = this.sortPresets(resp?.data?.data?.presets ?? [])
                }
            }).catch(err => {
                this.showAlert('error', err.message)
            })
        },
        /**
         * @param {boolean} toggle
         */
        buildQueryParamsForCallsOverAMinuteFilter(toggle) {

            if (!_.isBoolean(toggle)) {
                throw new TypeError(`"toggle" must be a boolean. ${toggle} passed.`)
            }

            let filters;

            if (toggle) {
                filters = {
                    query: null,
                    start_date: null,
                    end_date: null,
                    user_id: null,
                    sort_by: 'updated_at',
                    min_call_duration_in_seconds: 60,
                    min_call_duration_in_seconds_exclusive: true
                }
            } else {
                filters = {
                    query: null,
                    start_date: null,
                    end_date: null,
                    user_id: null,
                    sort_by: 'updated_at',
                    min_call_duration_in_seconds: 0,
                    min_call_duration_in_seconds_exclusive: false
                }
            }

            this.activityTabFilters = filters;
            return filters;
        },
        updateActivityPageAndActivityTab() {
            try {
                useActivityPageStore().getActivitiesOverview(this.selectedCompany, this.activityTabFilters);
            } catch (e) {
                console.error(e);
            }

            try {
                useActivityTabStore().getActivities(this.selectedCompany, this.api).catch(e => {
                    console.error(e);
                })
            } catch (e) {
                console.error(e);
            }
        },
        /**
         * @param {string} field
         */
        sortCompanies(field) {
            /**
             * @type {sortObject}
             */
            const sortObject = this.storeSortObject;

            this.filteredCompanies = globalSortCompanies(this.filteredCompanies, sortObject, field)
        },
        async load() {
            this.companies = [];
            this.contacts = [];
            this.expandedContacts = [];
            if(this.location) {
                this.industry = this.missedRevenueIndustry;
                await this.getCompanies();
            } else {
                await this.getLead().then(() => this.getCompanies());
            }
        },
        getLead() {
            this.loading = true;
            if (this.leadId) {
                return new Promise((resolve) => {
                    this.leadApi.getConsumerProductByLegacyId(this.leadId).then(resp => {
                        this.lead = resp.data.data;
                        this.zipCode = resp.data.data.basic.address.zip_code;
                        this.industry = this?.lead?.basic.industry ? slugify(this?.lead?.basic.industry) : null
                    }).catch(e => console.error("Problem getting consumer product data: " + e)).finally(() => {
                        this.loading = false;
                        resolve();
                    });
                })
            } else {
                console.error("No lead id.")
            }
        },
        async getCompanies() {
            this.loading = true;
            this.companies = [];
            this.filteredCompanies = [];
            this.contacts = [];
            this.expandedContacts = [];
            this.selectedCompany = null;
            this.gettingCompanies = true;

            const taskId = this.task?.id;

            if (this.location) {
                await this.sharedApi.getNonPurchasingCompaniesByCounty(this.industry, this.location.county_key, this.location.state_key, this.keyDataFilters)
                    .then(resp => {
                        this.companies = resp.data.data.companies;

                        const activeSort = Object.entries(this.storeSortObject).filter(x => x.active)[0]?.value;

                        if (activeSort) {
                            this.sortCompanies(activeSort);
                        }

                        this.filteredCompanies = this.companies;
                    })
                    .catch(e => {
                        console.error(e);
                        this.showAlert('error', e.message)
                    })
                    .finally(() => {
                        this.gettingCompanies = false;
                        this.loading = false;
                    });
            } else if (this.industry && this.zipCode && taskId) {
                await this.sharedApi.getNonPurchasingCompaniesInCountyByZipCode(this.industry, this.zipCode, this.task?.id, this.keyDataFilters)
                    .then(resp => {
                        this.companies = resp.data.data.companies;

                        const activeSort = Object.entries(this.storeSortObject).filter(x => x.active)[0]?.value;

                        if (activeSort) {
                            this.sortCompanies(activeSort);
                        }

                        this.filteredCompanies = this.companies;
                    })
                    .catch(e => {
                        console.error(e);
                        this.showAlert('error', e.message)
                    })
                    .finally(() => {
                        this.gettingCompanies = false;
                        this.loading = false;
                    });
            } else {
                if (!this.industry) {
                    console.error("Missing industry.")
                }

                if (!this.zipCode) {
                    console.error("Missing zip code.")
                }

                if (!taskId) {
                    console.error("Missing task id.")
                }

                this.loading = false;
            }
        },
        call(contact) {
            this.dispatchGlobalEvent('call', {
                phone: contact.phone ?? contact.mobile,
                name: contact.name,
                id: contact.id,
                relType: CommunicationRelationTypes.COMPANY_USER,
                relId: contact.id
            });
        },
        callOffice(office) {
            this.dispatchGlobalEvent('call', {
                phone: office.phone,
                name: `${office.city}, ${office.state}`,
                id: office.addressid,
                relType: CommunicationRelationTypes.COMPANY_LOCATIONS,
                relId: office.id
            });
        },
        sms(contact) {
            this.dispatchGlobalEvent('sms', {
                phone: contact.phone ?? contact.mobile,
                name: contact.name,
                id: contact.id,
                relType: CommunicationRelationTypes.COMPANY_USER,
                relId: contact.id
            });
        },
    }
}
</script>
