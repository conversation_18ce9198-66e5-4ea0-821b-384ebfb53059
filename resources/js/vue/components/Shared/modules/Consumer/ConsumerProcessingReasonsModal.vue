<template>
    <Modal :dark-mode="darkMode"
           @close="closeModal"
           @confirm="confirmSelection"
    >
        <template v-slot:header>
            <h4 class="text-xl font-medium">{{ modalConfiguration.title }}</h4>
        </template>

        <template v-slot:content>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 my-4">
                <button
                    v-for="option in modalConfiguration.options" :key="option.id ? option.id : option" @click="() => selectModalOption(option)"
                    class="transition duration-200 text-sm focus:outline-none py-2 rounded-md px-5 font-bold"
                    :class="optionClasses(option)">
                    {{ option.value ? option.value : option }}
                </button>
            </div>
            <div v-if="modalConfiguration?.options?.length" class="mt-4 px-5 w-full">
                <p class="mb-1 ml-1">Additional comments:</p>
                <CustomInput
                    :dark-mode="darkMode"
                    v-model="additionalComments"
                    type="textarea"
                />
            </div>
            <div v-if="showPublicCommentToggle"
                class="flex items-center gap-x-2 px-12 mt-2 mb-1"
            >
                <p>
                    Send to Company:
                </p>
                <ToggleSwitch
                    :dark-mode="darkMode"
                    v-model="makeCommentPublic"
                />
                <div v-if="makeCommentPublic" class="px-15 text-sm ml-6">
                    <p class="italic">This comment will be attached to lead delivery when allocated.</p>
                </div>
            </div>
            <div class="my-2 font-bold text-red-500" v-if="modalConfiguration.warning">
                {{ modalConfiguration.warning }}
            </div>
            <div v-if="showPublicCommentSummary && publicComments?.length"
                class="flex flex-col gap-3 px-12 py-3 mt-6 mx-auto overflow-y-auto max-h-[20rem] border-t w-[90%]"
                 :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
            >
                <p class="text-sm">
                    The following comment{{ publicComments.length > 1 ? 's' : '' }} have previously been marked as public.
                    You may de-select any messages you no longer wish to be sent to companies who buy the lead.
                    The comments will still be visible to internal staff.
                </p>
                <div v-for="comment in publicComments"
                    class="flex items-center gap-x-5 px-5 mt-4"
                >
                    <CustomCheckbox
                        :dark-mode="darkMode"
                        v-model="comment.public"
                    />
                    <p class="text-sm">
                        {{ comment.comment }}
                    </p>
                </div>
            </div>
        </template>
    </Modal>
</template>

<script>
import Modal from "../../../LeadProcessing/components/Modal.vue";
import CustomInput from "../../components/CustomInput.vue";
import ToggleSwitch from "../../components/ToggleSwitch.vue";
import CustomCheckbox from "../../SlideWizard/components/CustomCheckbox.vue";

export default {
    name: "ConsumerProcessingReasonsModal",
    components: { CustomCheckbox, ToggleSwitch, CustomInput, Modal },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        modalConfiguration: {
            type: Object,
            required: true,
        },
        comments: {
            type: [Array,null],
            default: null,
        },
    },
    emits: ['close-modal', 'confirm-selection'],
    data() {
        return {
            additionalComments: '',
            makeCommentPublic: false,
            publicComments: null,
            disableComments: {},
        }
    },
    mounted() {
        if (this.modalConfiguration.type === 'allocate')
            this.publicComments = (this.comments ?? []).filter(comment => comment.public && comment.comment);
    },
    computed: {
        showPublicCommentToggle() {
            return this.modalConfiguration.type !== 'cancel'
        },
        showPublicCommentSummary() {
            return this.modalConfiguration.type === 'allocate'
        },
    },
    methods: {
        getAdditionalComments() {
            const trimmed = this.additionalComments.trim();
            return trimmed || null;
        },
        getDeselectedComments() {
            const excludeIds = (this.publicComments ?? []).reduce((output, comment) => comment.public ? output : [...output, comment.id], []);

            return excludeIds.length
                ? { remove_public_comments: excludeIds }
                : null;
        },
        closeModal() {
            this.$emit('close-modal');
        },
        confirmSelection() {
            this.$emit('confirm-selection', this.getAdditionalComments(), this.makeCommentPublic, this.getDeselectedComments());
        },
        selectModalOption(value) {
            if(this.modalConfiguration.multiSelect === false && !this.modalConfiguration.selectedValues.includes(value))
                this.modalConfiguration.selectedValues = [];
            if(!this.modalConfiguration.selectedValues.includes(value))
                this.modalConfiguration.selectedValues.push(value);
            else
                this.modalConfiguration.selectedValues = [...this.modalConfiguration.selectedValues].filter(val => val !== value);

            this.modalConfiguration.warning = null;
        },
        optionSelected(value) {
            return this.modalConfiguration.selectedValues.includes(value);
        },
        optionClasses(option) {
            const classes = [];
            if(this.optionSelected(option)) {
                if(this.darkMode)
                    classes.push('bg-primary-500', 'text-white');
                else
                    classes.push('bg-cyan-150', 'hover:bg-blue-200', 'text-blue-550');
            }
            else {
                if(this.darkMode)
                    classes.push('bg-dark-module', 'border', 'border-primary-500', 'hover:bg-blue-800', 'text-grey-300');
                else
                    classes.push('bg-grey-100', 'hover:bg-grey-200', 'text-grey-475');
            }

            return classes;
        },
    }
}
</script>