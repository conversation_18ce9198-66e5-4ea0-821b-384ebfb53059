<template>
    <div class="border rounded-lg p-5 mb-5" v-if="selectedCampaigns.length" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
        <div v-for="campaign in selectedCampaigns" class="grid grid-cols-5 gap-4 items-center border-b" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
            <p class="text-sm py-3">{{ campaign.company_name }}</p>
            <p class="text-sm py-3">{{ campaign.campaign_name }}</p>
            <p class="text-sm py-3">{{ saleType }}</p>
            <p class="text-sm py-3">{{ getPrice(campaign.campaign_id) }}</p>
            <p class="text-xs py-3">
                <custom-button @click="$emit('campaign-removed', campaign.campaign_id)" :disabled="loading || disable" :color="'slate-outline'" :height="'h-7'"
                               :dark-mode="darkMode">
                    Remove
                </custom-button>
            </p>
        </div>
        <div class="mt-5">
            <custom-button @click="allocate" :disabled="loading || disable" :dark-mode="darkMode">Allocate Selected</custom-button>
        </div>
    </div>
</template>

<script setup>
import {computed, onMounted, ref, watch} from "vue";
import CustomButton from "../../components/CustomButton.vue";
import ConsumerApiService from "../../services/consumer_api.js";

const props = defineProps({
    darkMode: {
        type: Boolean,
        default: false
    },
    consumerProductId: {
        type: Number,
        required: true
    },
    selectedCampaigns: {
        type: Array,
        default: []
    },
    assigned: {
        type: Number,
        default: 0
    },
    disable: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['campaign-removed', 'allocate', 'processing', 'error']);

const loading = ref(false);
const prices = ref([]);
const saleType = computed(() => {
    switch (props.selectedCampaigns.length + props.assigned) {
        case 1:
            return 'Exclusive';
        case 2:
            return 'Duo';
        case 3:
            return 'Trio';
        case 4:
            return 'Quad';
        default:
            return '';
    }
});
const api = ConsumerApiService.make();

onMounted(() => {
    if (props.selectedCampaigns.length) {
        fetchPrices();
    }
});

const getPrice = campaignId => {
    const price = prices.value.find(price => price.campaign_id === campaignId);

    return price ? `$${price.price}` : '....';
}

const fetchPrices = () => {
    prices.value = [];

    if (!saleType.value || !props.selectedCampaigns.length) {
        return;
    }

    loading.value = true;

    api.getPricesForCampaigns(props.consumerProductId, {
        campaign_ids: props.selectedCampaigns.map(campaign => campaign.campaign_id),
        sale_type: saleType.value
    }).then(rsp => {
        prices.value = rsp.data.data.prices ?? [];
    }).catch(e => {
        console.error(e);
    }).finally(() => loading.value = false);
}

const allocate = () => {
    if (isDuplicateAllocation()) {
        emit("error", 'Duplicate companies selected for allocation');
        return;
    }

    emit('allocate', saleType.value);
}

const isDuplicateAllocation = () => {
    const companiesCount = props.selectedCampaigns.reduce((companies, campaign) => {
        companies[campaign.company_id] = (companies[campaign.company_id] || 0) + 1;
        return companies;
    }, {});

    return !! Object.keys(companiesCount).filter(companyId => companiesCount[companyId] > 1).length;
}

watch(saleType, () => fetchPrices());
watch(loading, () => emit('processing', loading.value));

</script>
