<template>
    <div class="row-span-3 border rounded-lg"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <Tab
            :dark-mode="darkMode"
            :tabs="tabs"
            @selected="switchTab"
            :tabs-classes="'w-full'"
            :tab-type="'Normal'"
            :default-tab-index="0"
        />
        <ip-quality-score-verification v-if="selectedTab === 'IP Quality Score'" :consumer-product-id="consumerProductId" :dark-mode="darkMode"/>
        <ip-quality-score-experimental-identity v-if="selectedTab === 'Identities (Experimental)'" :consumer-product-id="consumerProductId" :dark-mode="darkMode"></ip-quality-score-experimental-identity>
    </div>
</template>

<script>
import IpQualityScoreVerification from "../../modules/Consumer/IpQualityScoreVerification.vue";
import Tab from "../../components/Tab.vue";
import IpQualityScoreExperimentalIdentity from "./IpQualityScoreExperimentalIdentity.vue";

export default {
    name: "ConsumerProductVerification",
    components: {
        IpQualityScoreExperimentalIdentity,
        IpQualityScoreVerification,
        Tab
    },
    props: {
        consumerProductId: {
            type: Number,
            default: null
        },
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    data: function() {
        return {
            showIpQualityScoreVerification: false,
            selectedTab: 'IP Quality Score',

            tabs: [
                { name: 'IP Quality Score', current: true  },
                { name: 'Identities (Experimental)', current: false  }
            ]
        };
    },
    methods: {
        switchTab(tab) {
            this.selectedTab = tab;
        },
    }
};
</script>
