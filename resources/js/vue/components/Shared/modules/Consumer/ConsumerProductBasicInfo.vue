<template>
    <!-- Product Basic Info -->
    <div id="basic-info" class="row-span-4 border rounded-lg"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <div class="p-5">
            <h5 class="text-primary-500 text-sm uppercase font-semibold pb-4 leading-tight">Lead Info</h5>
            <div class="flex items-center truncate pb-3">
                <svg @click="editField(fields.contactRequests.name)" class="cursor-pointer mr-2 flex-shrink-0"
                     width="14" height="12" viewBox="0 0 14 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M9.33333 2.33333C9.33333 2.95217 9.0875 3.54566 8.64992 3.98325C8.21233 4.42083 7.61884 4.66667 7 4.66667C6.38116 4.66667 5.78767 4.42083 5.35008 3.98325C4.9125 3.54566 4.66667 2.95217 4.66667 2.33333C4.66667 1.71449 4.9125 1.121 5.35008 0.683418C5.78767 0.245833 6.38116 0 7 0C7.61884 0 8.21233 0.245833 8.64992 0.683418C9.0875 1.121 9.33333 1.71449 9.33333 2.33333V2.33333ZM13.2222 3.88889C13.2222 4.30145 13.0583 4.69711 12.7666 4.98883C12.4749 5.28056 12.0792 5.44444 11.6667 5.44444C11.2541 5.44444 10.8584 5.28056 10.5667 4.98883C10.275 4.69711 10.1111 4.30145 10.1111 3.88889C10.1111 3.47633 10.275 3.08067 10.5667 2.78894C10.8584 2.49722 11.2541 2.33333 11.6667 2.33333C12.0792 2.33333 12.4749 2.49722 12.7666 2.78894C13.0583 3.08067 13.2222 3.47633 13.2222 3.88889V3.88889ZM10.1111 9.33333C10.1111 8.50822 9.78333 7.71689 9.19989 7.13345C8.61644 6.55 7.82512 6.22222 7 6.22222C6.17488 6.22222 5.38356 6.55 4.80011 7.13345C4.21667 7.71689 3.88889 8.50822 3.88889 9.33333V11.6667H10.1111V9.33333ZM3.88889 3.88889C3.88889 4.30145 3.725 4.69711 3.43328 4.98883C3.14155 5.28056 2.74589 5.44444 2.33333 5.44444C1.92077 5.44444 1.52511 5.28056 1.23339 4.98883C0.941666 4.69711 0.777778 4.30145 0.777778 3.88889C0.777778 3.47633 0.941666 3.08067 1.23339 2.78894C1.52511 2.49722 1.92077 2.33333 2.33333 2.33333C2.74589 2.33333 3.14155 2.49722 3.43328 2.78894C3.725 3.08067 3.88889 3.47633 3.88889 3.88889V3.88889ZM11.6667 11.6667V9.33333C11.6678 8.54255 11.467 7.76458 11.0833 7.07311C11.4282 6.98487 11.7886 6.97655 12.1371 7.04878C12.4856 7.12102 12.8131 7.2719 13.0944 7.48993C13.3758 7.70796 13.6036 7.98736 13.7605 8.30683C13.9175 8.6263 13.9994 8.9774 14 9.33333V11.6667H11.6667ZM2.91667 7.07311C2.53303 7.76459 2.33224 8.54256 2.33333 9.33333V11.6667H2.06671e-07V9.33333C-0.000149697 8.97714 0.0812493 8.62564 0.237959 8.30578C0.394668 7.98591 0.622529 7.70617 0.904076 7.48799C1.18562 7.26981 1.51339 7.11898 1.86224 7.04707C2.2111 6.97516 2.57179 6.98406 2.91667 7.07311V7.07311Z"
                        fill="#0081FF"/>
                </svg>
                <edit-field :dark-mode="darkMode" :type='fields.contactRequests.editType'
                            :options="fields.contactRequests.editOptions" @update-field="updateField"
                            @cancel-edit="cancelEdit" v-if="editingField === fields.contactRequests.name"
                            :value="consumerProduct[fields.contactRequests.databaseName]"
                            :field-name="fields.contactRequests.name"/>
                <p v-else class="pb-0 ">{{ consumerProduct[fields.contactRequests.databaseName] }}
                    {{ consumerProduct[fields.contactRequests.databaseName] === 1 ? 'Company' : 'Companies' }}
                    Requested</p>
            </div>
            <div class="flex items-center truncate pb-3" v-if="consumerProductType">
                <svg v-if="consumerProductType == 'Residential'" xmlns="http://www.w3.org/2000/svg" fill="none"
                     viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                     class="w-4 h-4 mr-2 flex-shrink-0 text-primary-500">
                    <path stroke-linecap="round" stroke-linejoin="round"
                          d="M8.25 21v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21m0 0h4.5V3.545M12.75 21h7.5V10.75M2.25 21h1.5m18 0h-18M2.25 9l4.5-1.636M18.75 3l-1.5.545m0 6.205l3 1m1.5.5l-1.5-.5M6.75 7.364V3h-3v18m3-13.636l10.5-3.819"/>
                </svg>
                <svg v-else xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                     stroke="currentColor" class="w-4 h-4 mr-2 flex-shrink-0 text-primary-500">
                    <path stroke-linecap="round" stroke-linejoin="round"
                          d="M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008zm0 3h.008v.008h-.008v-.008zm0 3h.008v.008h-.008v-.008z"/>
                </svg>
                <p class="pb-0 "> {{ consumerProductType }} </p>
            </div>
            <div class="flex items-center truncate pb-3">
                <svg @click="editField(fields.appointmentsRequested.name)" class="cursor-pointer mr-2 flex-shrink-0"
                     width="14" height="13" viewBox="0 0 14 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M12.6 0H1.4C1.0287 0 0.672601 0.1475 0.41005 0.41005C0.1475 0.672601 0 1.0287 0 1.4V11.2C0 11.5713 0.1475 11.9274 0.41005 12.19C0.672601 12.4525 1.0287 12.6 1.4 12.6H12.6C12.9713 12.6 13.3274 12.4525 13.5899 12.19C13.8525 11.9274 14 11.5713 14 11.2V1.4C14 1.0287 13.8525 0.672601 13.5899 0.41005C13.3274 0.1475 12.9713 0 12.6 0ZM6.3 9.8H2.1V8.4H6.3V9.8ZM11.9 7H2.1V5.6H11.9V7ZM11.9 4.2H2.1V2.8H11.9V4.2Z"
                        fill="#0081FF"/>
                </svg>
                <edit-field :dark-mode="darkMode" @update-field="updateField" @cancel-edit="cancelEdit"
                            v-if="editingField === fields.appointmentsRequested.name"
                            :value="consumerProduct[fields.appointmentsRequested.databaseName]"
                            :field-name="fields.appointmentsRequested.name"></edit-field>
                <p v-else class="pb-0 ">
                    {{ consumerProduct[fields.appointmentsRequested.databaseName] || 'No Appointments Requested' }} </p>
            </div>
            <div class="flex items-center truncate pb-3">
                <svg @click="editField(fields.bestTimeToCall.name)" class="cursor-pointer mr-2 flex-shrink-0" width="14"
                     height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M7 14C8.85652 14 10.637 13.2625 11.9497 11.9497C13.2625 10.637 14 8.85652 14 7C14 5.14348 13.2625 3.36301 11.9497 2.05025C10.637 0.737498 8.85652 0 7 0C5.14348 0 3.36301 0.737498 2.05025 2.05025C0.737498 3.36301 0 5.14348 0 7C0 8.85652 0.737498 10.637 2.05025 11.9497C3.36301 13.2625 5.14348 14 7 14V14ZM7.875 3.5C7.875 3.26794 7.78281 3.04538 7.61872 2.88128C7.45462 2.71719 7.23206 2.625 7 2.625C6.76794 2.625 6.54538 2.71719 6.38128 2.88128C6.21719 3.04538 6.125 3.26794 6.125 3.5V7C6.12505 7.23205 6.21727 7.45457 6.38138 7.61862L8.85588 10.094C8.93717 10.1753 9.03368 10.2398 9.1399 10.2838C9.24612 10.3278 9.35997 10.3504 9.47494 10.3504C9.58991 10.3504 9.70375 10.3278 9.80997 10.2838C9.91619 10.2398 10.0127 10.1753 10.094 10.094C10.1753 10.0127 10.2398 9.91619 10.2838 9.80997C10.3278 9.70375 10.3504 9.58991 10.3504 9.47494C10.3504 9.35997 10.3278 9.24612 10.2838 9.1399C10.2398 9.03368 10.1753 8.93717 10.094 8.85588L7.875 6.63775V3.5Z"
                          fill="#0081FF"/>
                </svg>
                <edit-field :dark-mode="darkMode" @update-field="updateField" @cancel-edit="cancelEdit"
                            v-if="editingField === fields.bestTimeToCall.name"
                            :value="consumerProduct[fields.bestTimeToCall.databaseName]"
                            :field-name="fields.bestTimeToCall.name"></edit-field>
                <p v-else class="pb-0 "> {{
                        consumerProduct[fields.bestTimeToCall.databaseName] || 'Not Specified'
                    }} </p>
            </div>
            <div class="flex items-center truncate pb-3" :class="colorCode(consumerProduct[fields.color_code.name])">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                     stroke="currentColor" class="mr-2 flex-shrink-0 w-4 h-4">
                    <path stroke-linecap="round" stroke-linejoin="round"
                          d="M12 21a9.004 9.004 0 008.716-6.747M12 21a9.004 9.004 0 01-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 017.843 4.582M12 3a8.997 8.997 0 00-7.843 4.582m15.686 0A11.953 11.953 0 0112 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0121 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0112 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 013 12c0-1.605.42-3.113 1.157-4.418"/>
                </svg>
                <p class="pb-0 "> {{ consumerProduct[fields.origin.databaseName] || 'Unknown' }} </p>
            </div>
            <ConfigurablePayloadFields
                :consumer-payload-data="consumerProductData"
                category-to-fetch="Lead Information"
                :classes="'flex items-center truncate pb-3'" />
            <div class="flex items-center truncate pb-3">
                <!--                todo: icon for the link-->
                <span style="width: 14px; height: 14px" class="mr-2"></span>
                <p class="pb-0 text-primary-500 cursor-pointer" @click="showAppointmentModal = true">Appointments
                    ({{ consumerProduct.appointments.length }})</p>
            </div>
            <div class="flex items-center truncate pb-3" v-if="consumerProductData?.display_payload_data?.roof_type">
                <span style="width: 14px; height: 14px" class="mr-2"></span>
                <p class="pb-0 text-primary-500 ">Roof type: {{
                        consumerProductData?.display_payload_data?.roof_type
                    }} </p>
            </div>
            <div v-if="consumerProduct[fields.opt_in_companies.databaseName]?.length > 0" class="my-4">
                <p class="pb-2 text-slate-500 text-sm uppercase font-semibold">Companies opted into</p>
                <div class="flex flex-wrap">
                    <a class="text-primary-500 mr-3"
                       v-for="company in consumerProduct[fields.opt_in_companies.databaseName]" target="_blank" :href="`/companies/${company.company_id}`">
                        {{  company.company_name }}
                    </a>
                </div>
            </div>
            <div class="flex items-center truncate pb-3">
                <svg class="mr-2 flex-shrink-0 h-4 w-4 text-primary-500 stroke-none fill-current" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path v-if="this.watchdogVideoUrl" d="M4.5 4.5a3 3 0 0 0-3 3v9a3 3 0 0 0 3 3h8.25a3 3 0 0 0 3-3v-9a3 3 0 0 0-3-3H4.5ZM19.94 18.75l-2.69-2.69V7.94l2.69-2.69c.944-.945 2.56-.276 2.56 1.06v11.38c0 1.336-1.616 2.005-2.56 1.06Z" />
                    <path v-else stroke-linecap="round" stroke-linejoin="round" d="m15.75 10.5 4.72-4.72a.75.75 0 0 1 1.28.53v11.38a.75.75 0 0 1-1.28.53l-4.72-4.72M12 18.75H4.5a2.25 2.25 0 0 1-2.25-2.25V9m12.841 9.091L16.5 19.5m-1.409-1.409c.407-.407.659-.97.659-1.591v-9a2.25 2.25 0 0 0-2.25-2.25h-9c-.621 0-1.184.252-1.591.659m12.182 12.182L2.909 5.909M1.5 4.5l1.409 1.409" />
                </svg>
                <a
                    v-if="this.watchdogVideoUrl" class="pb-0 font-medium text-primary-500 flex items-center underline underline-offset-2" :href="this.watchdogVideoUrl" target="_blank">
                    View Compliance Recording
                </a>
                <p v-else class="pb-0 font-medium cursor-not-allowed text-gray-400">No Compliance Recording Found</p>
            </div>
        </div>
        <loading-spinner v-if="loading" :dark-mode="darkMode" />
        <alert :dark-mode="darkMode" v-if="alertActive" :alert-type="alertType" :text="alertText" />
        <modal v-if="showAppointmentModal" @close="showAppointmentModal = false" :small="true" :no-buttons="true" :dark-mode="darkMode">
            <template v-slot:header>
                <h4 class="text-xl inline-flex items-center" :class="{'text-grey-900': !darkMode, 'text-white': darkMode}">Appointments</h4>
            </template>
            <template v-slot:content>
                <product-appointments
                    :consumer-product-id="consumerProduct.id"
                    :dark-mode="darkMode"
                    @appointment-deleted="appointmentDeleted"
                    @appointment-added="appointmentAdded">
                </product-appointments>
            </template>
        </modal>
    </div>
</template>

<script>
import EditField from "../../components/EditField.vue";
import ConsumerApiService from "../../services/consumer_api";
import AlertsMixin from "../../../../mixins/alerts-mixin";
import Alert from "../../components/Alert.vue";
import LoadingSpinner from "../../components/LoadingSpinner.vue";
import ProductAppointments from "./ProductAppointments.vue";
import Modal from "../../components/Modal.vue";
import {colorCode} from "../../services/strings"
import ConfigurablePayloadFields from "../../../LeadProcessing/components/ConfigurablePayloadFields.vue";

export default {
    name: "ConsumerProductBasicInfo",
    components: {
        ConfigurablePayloadFields,
        LoadingSpinner,
        Alert,
        EditField,
        ProductAppointments,
        Modal
    },
    props: {
        consumerProductData: {
            type: Object
        },
        consumerProductId: {
            type: Number,
            default: null,
        },
        consumerProductType: {
            type: String,
            default: ""
        },
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    mixins: [
        AlertsMixin
    ],
    data() {
        return {
            consumerProduct: {},
            responseKey: `consumer_product_basic_info`,
            fields: {
                createdAt: {
                    name: 'createdAt',
                    databaseName: 'created_at',
                },
                contactRequests: {
                    name: 'contactRequests',
                    databaseName: 'contact_requests',
                    editType: 'select',
                    editOptions: [
                        {id: 0, value: 0},
                        {id: 1, value: 1},
                        {id: 2, value: 2},
                        {id: 3, value: 3},
                        {id: 4, value: 4},
                    ],
                },
                appointmentsRequested: {
                    name: 'appointmentsRequested',
                    databaseName: 'best_time_to_call_other',
                },
                bestTimeToCall: {
                    name: 'bestTimeToCall',
                    databaseName: 'best_time_to_call',
                },
                origin: {
                    name: 'origin',
                    databaseName: 'origin',
                },
                color_code: {
                    name: 'color_code',
                    databaseName: 'color_code'
                },
                opt_in_companies: {
                    name: 'opt_in_companies',
                    databaseName: 'opt_in_companies'
                }
            },
            api: ConsumerApiService.make(),
            colorCode: colorCode,
            editingField: null,
            loading: false,
            messages: {
                updateFailed: `Server error updating Consumer Product information.`,
                getFailed: `Error fetching Consumer Product information.`,
            },
            showAppointmentModal: false,
            watchdogVideoUrl: null,
        }
    },
    created() {
        this.initialize();
    },
    watch: {
        consumerProductId(newVal, oldVal) {
            if (newVal !== oldVal) this.getConsumerProductBasicInfo();
        },
        consumerProductData: {
            handler() {
                this.initialize();
            },
            deep: true,
        }
    },
    methods: {
        initialize() {
            this.consumerProduct.appointments = [];

            if (this.consumerProductData && typeof this.consumerProductData[this.responseKey] !== 'undefined') {
                this.consumerProduct['id'] = this.consumerProductData.product_id;
                this.setExpectedFields(this.consumerProductData[this.responseKey]);
            } else if (!this.consumerProductData && this.consumerProductId) {
                this.consumerProduct['id'] = this.consumerProductId;
                this.getConsumerProductBasicInfo();
            }

            this.api.getWatchdogVideoUrl(this.consumerProduct['id']).then(r => {
                this.watchdogVideoUrl = r.data.data.url;
            }).catch(e => {
                console.log(e.message);
            })
        },
        setExpectedFields(basicInfo) {
            for (const field in this.fields) {
                if (basicInfo[this.fields[field].databaseName] === undefined) {
                    this.showAlert('warning', `Response missing expected data for "${field}"`);
                } else {
                    this.consumerProduct[this.fields[field].databaseName] = basicInfo[this.fields[field].databaseName];
                }
            }
            this.consumerProduct.appointments = basicInfo.appointments;
        },
        async getConsumerProductBasicInfo() {
            this.loading = true;
            this.api.getConsumerProductBasicInfo(this.consumerProduct.id).then(resp => {
                if (resp.data.data.status) {
                    const basicInfo = resp.data.data[this.responseKey];
                    this.setExpectedFields(basicInfo);
                }
                else this.showAlert('error', this.messages.getFailed);
            }).catch(() => {
                this.showAlert('error', this.messages.getFailed);
            }).finally(() => this.loading = false);
        },
        editField(fieldName) {
            this.editingField = fieldName;
        },
        updateField(fieldName, newValue) {
            this.loading = true;
            if (this.fields[fieldName]) {
                const dataKey = this.fields[fieldName].databaseName;
                const updatePayload = {[dataKey]: newValue};
                this.api.updateConsumerProductBasicInfo(this.consumerProduct.id, updatePayload).then(resp => {
                    const status = resp.data.data.status;
                    if (status) {
                        this.consumerProduct[dataKey] = newValue;
                        this.editingField = null;
                    }
                    else {
                        this.showAlert('error', this.messages.updateFailed);
                    }
                }).catch(err => {
                    const errorMessage = err.response?.data?.message ?? this.messages.updateFailed
                    this.showAlert('error', errorMessage);
                }).finally(() => {
                    this.loading = false;
                });
            }
        },
        cancelEdit() {
            this.editingField = null;
        },
        appointmentDeleted(appointmentId) {
            this.consumerProduct.appointments = this.consumerProduct.appointments.filter(appointment => appointment.id !== appointmentId);
        },
        appointmentAdded(appointment) {
            this.consumerProduct.appointments.push(appointment);
        }
    },
}
</script>

<style scoped>

</style>
