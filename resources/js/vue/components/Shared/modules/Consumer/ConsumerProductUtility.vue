<template>
    <!-- Utility -->
    <div id="utility" class="row-span-3 border rounded-lg"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <div class="p-5">
            <h5 class="text-primary-500 text-sm uppercase font-semibold pb-4 leading-tight">Utility</h5>
            <div class="flex items-center mb-3">
                <svg class="mr-2 flex-shrink-0" width="14" height="18" viewBox="0 0 14 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M8.30045 0.0460013C8.50345 0.109843 8.6808 0.236782 8.80669 0.408357C8.93258 0.579932 9.00046 0.787194 9.00045 1V6H13.0004C13.1834 5.99992 13.3628 6.05001 13.5192 6.14483C13.6757 6.23964 13.8031 6.37554 13.8876 6.53774C13.9722 6.69995 14.0106 6.88223 13.9988 7.06477C13.9869 7.2473 13.9253 7.42309 13.8204 7.573L6.82044 17.573C6.69862 17.7475 6.52428 17.8787 6.32278 17.9473C6.12129 18.0159 5.90317 18.0184 5.70014 17.9545C5.49711 17.8906 5.31978 17.7635 5.19394 17.5919C5.06809 17.4202 5.00031 17.2129 5.00044 17V12H1.00045C0.817527 12.0001 0.63809 11.95 0.481663 11.8552C0.325235 11.7604 0.19781 11.6245 0.113255 11.4623C0.0286992 11.3001 -0.00974701 11.1178 0.00209983 10.9352C0.0139467 10.7527 0.0756328 10.5769 0.180445 10.427L7.18045 0.427001C7.30246 0.252788 7.47683 0.122014 7.67825 0.0536774C7.87966 -0.0146589 8.09761 -0.0169975 8.30045 0.0470013V0.0460013Z" fill="#0081FF"/>
                </svg>
                <p class="pb-0 ">{{ consumerProductUtility[fields.utilityName.dataName] ?? 'No Utility' }}</p>
            </div>
            <div class="flex items-center">
                <svg @click="editField(fields.electricCost.name)" class="cursor-pointer mr-2 flex-shrink-0" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M7 14C10.866 14 14 10.866 14 7C14 3.13401 10.866 0 7 0C3.13401 0 0 3.13401 0 7C0 10.866 3.13401 14 7 14ZM6.75803 10.7183V11.5294H7.34161V10.7188C7.81225 10.6943 8.22266 10.6083 8.57284 10.4608C8.99511 10.2815 9.31774 10.0302 9.54074 9.70703C9.76611 9.38148 9.87998 8.99695 9.88235 8.55345C9.87998 8.25149 9.82423 7.98373 9.71511 7.75018C9.60835 7.51664 9.45771 7.31376 9.26318 7.14154C9.06865 6.96933 8.83854 6.82425 8.57284 6.7063C8.30714 6.58834 8.01535 6.49398 7.69746 6.42321L7.34161 6.33859V4.57542C7.58218 4.60644 7.78145 4.67978 7.93943 4.79545C8.15531 4.95351 8.27749 5.17526 8.30596 5.46071H9.78272C9.7756 5.029 9.65936 4.64919 9.43399 4.32128C9.20862 3.99337 8.8931 3.73741 8.48744 3.5534C8.15285 3.40074 7.77091 3.31141 7.34161 3.2854V2.47059H6.75803V3.28851C6.35157 3.31804 5.98149 3.40633 5.64779 3.5534C5.23026 3.73741 4.90169 3.99337 4.66209 4.32128C4.42486 4.64919 4.30743 5.03254 4.3098 5.47132C4.30743 6.00683 4.48417 6.43264 4.84001 6.74876C5.19586 7.06487 5.681 7.29724 6.29542 7.44586L6.75803 7.56138V9.4259C6.60101 9.40738 6.45511 9.37253 6.32033 9.32132C6.11157 9.23876 5.94432 9.11726 5.81859 8.95685C5.69523 8.79407 5.62525 8.59119 5.60864 8.34821H4.11765C4.12951 8.86956 4.25643 9.30717 4.4984 9.66103C4.74275 10.0125 5.08199 10.2779 5.51612 10.4572C5.87793 10.6058 6.2919 10.6928 6.75803 10.7183ZM7.34161 9.42228C7.47799 9.40397 7.60254 9.37268 7.71525 9.3284C7.90978 9.25291 8.06042 9.14793 8.16718 9.01347C8.27393 8.879 8.32731 8.72448 8.32731 8.54991C8.32731 8.38713 8.27867 8.25031 8.18141 8.13943C8.08652 8.02855 7.94655 7.93419 7.76151 7.85634C7.63994 7.80453 7.49997 7.75585 7.34161 7.71031V9.42228ZM6.75803 6.19354V4.57796C6.63379 4.59531 6.5223 4.62416 6.42353 4.66452C6.25035 4.73294 6.1175 4.82848 6.02498 4.95115C5.93483 5.07382 5.88976 5.21301 5.88976 5.3687C5.88502 5.49845 5.9123 5.61169 5.9716 5.70841C6.03329 5.80513 6.1175 5.88888 6.22426 5.95965C6.33101 6.02806 6.45437 6.08822 6.59434 6.14012C6.64753 6.15895 6.70209 6.17675 6.75803 6.19354Z" fill="#0081FF"/>
                </svg>
                <edit-field :dark-mode="darkMode" @update-field="updateField" @cancel-edit="cancelEdit" v-if="editingField === fields.electricCost.name" :value="consumerProductUtility[fields.electricCost.dataName]" :field-name="fields.electricCost.name"></edit-field>
                <p v-else class="pb-0 " :class="{'font-bold text-red-500': cleanCostExpression(consumerProductUtility[fields.electricCost.dataName]) <= 50}">${{ cleanCostExpression(consumerProductUtility[fields.electricCost.dataName]) ?? '' }}</p>
            </div>
        </div>
        <loading-spinner v-if="loading" :dark-mode="darkMode" />
        <alert :dark-mode="darkMode" v-if="alertActive" :alert-type="alertType" :text="alertText" />
    </div>
</template>

<script>
import LoadingSpinner from "../../components/LoadingSpinner.vue";
import Alert from "../../components/Alert.vue";
import AlertsMixin from "../../../../mixins/alerts-mixin";
import ConsumerApiService from "../../services/consumer_api";
import EditField from "../../components/EditField.vue";

export default {
    name: "ConsumerProductUtility",
        components: {
        LoadingSpinner,
        Alert,
        EditField
    },
    props: {
        consumerProductData: {
            type: Object
        },
        consumerProductId: {
            type: Number,
            default: null,
        },
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    mixins: [
        AlertsMixin
    ],
    data() {
        return {
            consumerProductUtility: {},
            consumerProduct: {},
            responseKey: `consumer_product_utility`,
            api: ConsumerApiService.make(),
            loading: false,
            editingField: null,
            messages: {
                getFailed: `Failed to fetch Utility data.`,
                updateFailed: `Server error while updating utility information.`,
            },
            fields: {
                utilityName: {
                    name: 'utilityName',
                    dataName: 'utility_name',
                },
                electricCost: {
                    name: 'electricCost',
                    dataName: 'electric_cost',
                }
            },
        }
    },
    created() {
        this.initialize();
    },
    watch: {
        consumerProductId(newVal, oldVal) {
            if (newVal && newVal !== oldVal) this.getConsumerProductUtilityInfo();
        },
        consumerProductData: {
            handler() {
                this.initialize();
            },
            deep: true,
        }
    },
    methods: {
        initialize() {
            if (this.consumerProductData && typeof this.consumerProductData[this.responseKey] !== 'undefined') {
                this.consumerProduct['id'] = this.consumerProductData.product_id;
                this.setExpectedFields(this.consumerProductData[this.responseKey]);
            } else if (!this.consumerProductData && this.consumerProductId) {
                this.consumerProduct['id'] = this.consumerProductId;
                this.getConsumerProductUtilityInfo();
            }
        },
        setExpectedFields (utilityData) {
            for (const field in this.fields) {
                if (utilityData[this.fields[field].dataName] === undefined) {
                    this.showAlert('warning', `Expected key "${this.fields[field].dataName}" not found.`);
                } else {
                    this.consumerProductUtility[this.fields[field].dataName] = String(utilityData[this.fields[field].dataName]);
                }
            }
        },
        getConsumerProductUtilityInfo() {
            this.loading = true;
            this.api.getConsumerProductUtilityInfo(this.consumerProduct.id).then(resp => {
                if (resp.data.data.status) {
                    const utilityData = resp.data.data[this.responseKey];
                    this.setExpectedFields(utilityData);
                }
                else this.showAlert('error', this.messages.getFailed);
            }).catch(err => {
                const errorMessage = err.response?.data?.message ?? this.messages.getFailed;
                this.showAlert('error', errorMessage);
            }).finally(() => {
                this.loading = false;
            });
        },
        cleanCostExpression(inputString) {
            if (typeof(inputString) !== 'string') return null;
            return inputString.replace(/[^\d.-]/g, '');
        },
        editField(fieldName) {
            this.editingField = fieldName;
        },
        cancelEdit() {
            this.editingField = null;
        },
        updateField(fieldName, newValue) {
            this.loading = true;

            if (this.fields[fieldName]) {
                const dataKey = this.fields[fieldName].dataName;
                const updatePayload = {[dataKey]: newValue};
                this.api.updateConsumerProductUtilityInfo(this.consumerProduct.id, updatePayload).then(resp => {
                    const status = resp.data.data.status;
                    if (status) {
                        this.consumerProductUtility[dataKey] = newValue;
                        this.editingField = null;
                    }
                    else {
                        this.showAlert('error', this.messages.updateFailed);
                    }
                }).catch(err => {
                    const errorMessage = err.response?.data?.message ?? this.messages.updateFailed
                    this.showAlert('error', errorMessage);
                }).finally(() => {
                    this.loading = false;
                });
            }
        }
    },
}
</script>
