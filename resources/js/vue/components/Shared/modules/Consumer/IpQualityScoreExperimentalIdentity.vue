<template>
    <div>
        <div v-if="loading">
            <loading-spinner/>
        </div>
        <div v-else-if="identityData && !loading">
            <div class="grid grid-cols-1 max-h-[30rem] overflow-y-scroll">
                <div v-for="(identity, index) in identityData" class="border-b py-4"
                     :class="{'border-light-border' : !darkMode, 'border-dark-border' : darkMode}">
                    <div class="px-5 mb-2 font-semibold text-sm">
                        <badge color="primary" :dark-mode="darkMode">Identity {{index + 1}}</badge>
                    </div>
                    <div class="grid gap-2 px-5 text-sm mb-2 relative">
                        <div>
                            <div class="flex flex-wrap gap-2">
                                <p :class="[darkMode ? 'text-slate-400' : 'text-slate-500']">Name:</p>
                                <span v-for="name in identity?.names" class="font-medium" :class="{'text-slate-900' : !darkMode, 'text-slate-50' : darkMode}">
                                    {{name?.first_name}} {{name?.last_name}}
                                </span>
                            </div>
                        </div>
                        <div class="flex flex-wrap gap-2">
                            <p :class="[darkMode ? 'text-slate-400' : 'text-slate-500']">Age:</p>
                            <span class="font-medium" :class="{'text-slate-900' : !darkMode, 'text-slate-50' : darkMode}" v-if="identity?.age?.human">{{identity?.age?.human ?? "N/A"}}</span>
                        </div>
                        <div class="flex flex-wrap gap-2">
                            <p :class="[darkMode ? 'text-slate-400' : 'text-slate-500']">Address:</p>
                            <span v-for="address in identity?.addresses" class="font-medium" :class="{'text-slate-900' : !darkMode, 'text-slate-50' : darkMode}">
                                {{address?.address}}, {{address?.city}}, {{address?.region}}, {{address?.postal_code}}
                            </span>
                        </div>
                        <div>
                            <div class="flex flex-wrap gap-2">
                                <p :class="[darkMode ? 'text-slate-400' : 'text-slate-500']">Phone Numbers:</p>
                                <span v-for="(phone_number, index) in identity?.phone_numbers" class="font-medium" :class="{'text-slate-900' : !darkMode, 'text-slate-50' : darkMode}">
                                    {{phone_number?.formatted_phone_number}}{{ index < identity?.phone_numbers.length - 1 ? ',' : '' }}
                                </span>
                            </div>
                        </div>
                        <div>
                            <div class="flex flex-wrap gap-2">
                                <p :class="[darkMode ? 'text-slate-400' : 'text-slate-500']">Email Addresses:</p>
                                <span v-for="(email, index) in identity?.email_addresses" class="font-medium" :class="{'text-slate-900' : !darkMode, 'text-slate-50' : darkMode}">
                                    {{email?.email}}{{ index < identity?.email_addresses.length - 1 ? ',' : '' }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div v-else class="border-t border-l border-r h-auto max-h-48 overflow-y-auto"
             :class="{'bg-light-background border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}">
            <div class="px-5 py-2 text-s border-b"
                 :class="{'text-grey-800 border-light-border': !darkMode, 'text-grey-200 border-dark-border': darkMode}">
                {{ error }}
            </div>
        </div>
    </div>
</template>

<script>
import LoadingSpinner from "../../components/LoadingSpinner.vue";
import ApiService from "../../services/consumer_api";
import Badge from "../../components/Badge.vue";

export default {
    name: "IpQualityScoreExperimentalIdentity",
    components: {
        Badge,
        LoadingSpinner
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        consumerProductId: {
            type: Number,
            default: null
        },
    },
    data: function() {
        return {
            loading: true,
            identityData: null,
            api: ApiService.make(),
            error: "Problem retrieving IP Quality Score. Please try later!"
        };
    },
    created: function() {
        if(this.phoneData == null && this.consumerProductId !== null) {
            this.getConsumerProductVerification();
        }
    },
    watch: {
        consumerProductId(newVal, oldVal) {
            if(newVal !== oldVal) {
                this.getConsumerProductVerification();
            }
        }
    },
    methods: {
        async getConsumerProductVerification() {
            this.api.getConsumerProductVerification(this.consumerProductId, 'ip_quality_score').then(res => {
                if (res.data.data.status === true) {
                    this.identityData = res.data.data.product_verification.phone.identity_data;
                    console.log(this.identityData);
                }
            }).catch(err => console.log(err)).finally(() => {
                this.loading = false;
            });
        }
    }
}
</script>
