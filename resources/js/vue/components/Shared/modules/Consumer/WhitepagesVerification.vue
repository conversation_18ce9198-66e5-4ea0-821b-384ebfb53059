<template>
    <div id="" class="row-span-3 border rounded-lg"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <loading-spinner v-if="loading" :dark-mode="darkMode" />
        <div v-else>
            <!-- Confidence -->
            <div class="flex items-center justify-between p-5">
                <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Lead Verification</h5>
                <div class="text-sm rounded-full flex items-center"
                     :class="{'text-grey-400': !darkMode, 'text-blue-400': darkMode}">
                    Confidence:
                    <div class="rounded-full text-sm pl-2 h-full font-medium">
                        {{ Math.round(product.confidence) }}%</div>
                </div>
            </div>

            <!-- Phone -->
            <div class="border-b"
                 :class="{'border-light-border' : !darkMode, 'border-dark-border' : darkMode}">
                <div class="flex items-center px-5 pb-4 flex-wrap gap-2">
                    <h5 class="text-sm font-medium mr-2"
                        :class="{'text-grey-475' : !darkMode, 'text-grey-120' : darkMode}">Phone:</h5>
                    <div>
                        <div v-if="darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
                             :class="{'text-green-550' : product.phone.is_valid === true, 'text-red-350' : product.phone.is_valid !== true}">
                            <svg v-if="product.phone.is_valid === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                            </svg>
                            <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <p v-if="product.phone.is_valid === true" class="text-xs">Valid</p>
                            <p v-else class="text-xs">Invalid</p>
                        </div>
                        <div v-if="!darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
                             :class="{'text-green-550 bg-green-150' : product.phone.is_valid === true, 'text-red-350 bg-red-75' : product.phone.is_valid !== true}">
                            <svg v-if="product.phone.is_valid === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                            </svg>
                            <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <p v-if="product.phone.is_valid === true" class="text-xs">Valid</p>
                            <p v-else class="text-xs">Invalid</p>
                        </div>
                    </div>
                    <div class="mr-2">
                        <div v-if="darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium"
                             :class="{'text-green-550' : product.phone.name_match === true, 'text-red-350' : product.phone.name_match !== true}">
                            <svg v-if="product.phone.name_match === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                            </svg>
                            <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <p v-if="product.phone.name_match === true" class="text-xs">Match: <span class="font-medium truncate">{{ product.phone.subscriber_name !== null ? product.phone.subscriber_name : '' }}</span></p>
                            <p v-else class="text-xs">No Name Match</p>
                        </div>
                        <div v-if="!darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium"
                             :class="{'text-green-550 bg-green-150' : product.phone.name_match === true, 'text-red-350 bg-red-75' : product.phone.name_match !== true}">
                            <svg v-if="product.phone.name_match === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                            </svg>
                            <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <p v-if="product.phone.name_match === true" class="text-xs">Match: <span class="font-medium truncate">{{ product.phone.subscriber_name !== null ? product.phone.subscriber_name : '' }}</span></p>
                            <p v-else class="text-xs">No Name Match</p>
                        </div>
                    </div>
                    <div>
                        <div v-if="darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium"
                             :class="{'text-blue-550' : product.phone.line_type, 'text-red-350' : product.phone.line_type == null || product.phone.line_type == ''}">
                            <svg v-if="product.phone.line_type" class="mr-1 flex-shrink-0" width="11" height="10" viewBox="0 0 11 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M0 0H11V1.66667H0V0ZM0 4.16667H11V5.83333H0V4.16667ZM0 8.33333H11V10H0V8.33333Z" fill="#0081FF"/>
                            </svg>
                            <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <p v-if="product.phone.line_type" class="text-xs">Line Type: {{ product.phone.line_type }}</p>
                            <p v-else class="text-xs">Line Type: Unknown</p>
                        </div>
                        <div v-if="!darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium"
                             :class="{'text-blue-550 bg-cyan-125' : product.phone.line_type, 'text-red-350 bg-red-75' : product.phone.line_type == null || product.phone.line_type == ''}">
                            <svg v-if="product.phone.line_type" class="mr-1 flex-shrink-0" width="11" height="10" viewBox="0 0 11 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M0 0H11V1.66667H0V0ZM0 4.16667H11V5.83333H0V4.16667ZM0 8.33333H11V10H0V8.33333Z" fill="#0081FF"/>
                            </svg>
                            <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <p v-if="product.phone.line_type" class="text-xs">Line Type: {{ product.phone.line_type }}</p>
                            <p v-else class="text-xs">Line Type: Unknown</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Address -->
            <div class="border-b"
                 :class="{'border-light-border' : !darkMode, 'border-dark-border' : darkMode}">
                <div class="flex items-center px-5 py-4">
                    <h5 class="text-sm font-medium mr-2"
                        :class="{'text-grey-475' : !darkMode, 'text-grey-120' : darkMode}">Address:</h5>
                    <div v-if="darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
                         :class="{'text-green-550' : product.address.is_valid === true, 'text-red-350' : product.address.is_valid !== true}">
                        <svg v-if="product.address.is_valid === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                        </svg>
                        <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <p v-if="product.address.is_valid === true" class="text-xs">Valid</p>
                        <p v-else class="text-xs">Invalid</p>
                    </div>
                    <div v-if="!darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
                         :class="{'text-green-550 bg-green-150' : product.address.is_valid === true, 'text-red-350 bg-red-75' : product.address.is_valid !== true}">
                        <svg v-if="product.address.is_valid === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                        </svg>
                        <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <p v-if="product.address.is_valid === true" class="text-xs">Valid</p>
                        <p v-else class="text-xs">Invalid</p>
                    </div>
                </div>
            </div>

            <!-- Email -->
            <div class="border-b"
                 :class="{'border-light-border' : !darkMode, 'border-dark-border' : darkMode}">
                <div class="flex items-center px-5 py-4">
                    <h5 class="text-sm font-medium mr-2"
                        :class="{'text-grey-475' : !darkMode, 'text-grey-120' : darkMode}">Email:</h5>
                    <div v-if="darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
                         :class="{'text-green-550' : product.email.is_valid === true, 'text-red-350' : product.email.is_valid !== true}">
                        <svg v-if="product.email.is_valid === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                        </svg>
                        <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <p v-if="product.email.is_valid === true" class="text-xs">Valid</p>
                        <p v-else class="text-xs">Invalid</p>
                    </div>
                    <div v-if="!darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
                         :class="{'text-green-550 bg-green-150' : product.email.is_valid === true, 'text-red-350 bg-red-75' : product.email.is_valid !== true}">
                        <svg v-if="product.email.is_valid === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                        </svg>
                        <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <p v-if="product.email.is_valid === true" class="text-xs">Valid</p>
                        <p v-else class="text-xs">Invalid</p>
                    </div>
                </div>
            </div>

            <!-- IP Address -->
            <div>
                <div class="flex items-center px-5 py-4">
                    <h5 class="text-sm font-medium mr-2"
                        :class="{'text-grey-475' : !darkMode, 'text-grey-120' : darkMode}">IP Address:</h5>
                    <div v-if="darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
                         :class="{'text-green-550' : product.ip.is_valid === true, 'text-red-350' : product.ip.is_valid !== true}">
                        <svg v-if="product.ip.is_valid === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                        </svg>
                        <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <p v-if="product.ip.is_valid === true" class="text-xs">Valid</p>
                        <p v-else class="text-xs">Invalid</p>
                    </div>
                    <div v-if="!darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
                         :class="{'text-green-550 bg-green-150' : product.ip.is_valid === true, 'text-red-350 bg-red-75' : product.ip.is_valid !== true}">
                        <svg v-if="product.ip.is_valid === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                        </svg>
                        <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <p v-if="product.ip.is_valid === true" class="text-xs">Valid</p>
                        <p v-else class="text-xs">Invalid</p>
                    </div>
                </div>
            </div>
        </div>
        <alert :dark-mode="darkMode" v-if="alertActive" :alert-type="alertType" :text="alertText" />
    </div>
</template>

<script>
import ApiService from "../../services/consumer_api";
import LoadingSpinner from "../../components/LoadingSpinner.vue";
import AlertsMixin from "../../../../mixins/alerts-mixin";
import Alert from "../../components/Alert.vue";

export default {
    name: "WhitepagesVerification",
    components: {LoadingSpinner, Alert},
    props: {
        consumerProductId: {
            type: Number,
            default: null
        },
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    mixins: [AlertsMixin],
    data() {
        return {
            loading: false,
            product: null,
            api: ApiService.make(),
            error: "Couldn't retrieve verification details."
        }
    },
    created() {
        if(this.product === null && this.consumerProductId !== null) {
            this.getConsumerProductVerification();
        }
    },
    watch: {
        consumerProductId(newVal, oldVal) {
            if(newVal !== oldVal) {
                this.getConsumerProductVerification();
            }
        }
    },
    methods: {
        async getConsumerProductVerification() {
            this.loading = true;
            this.product = null;

            this.api.getConsumerProductVerification(this.consumerProductId, 'identity_check').then(resp => {
                if (resp.data.data.status === true) {
                    this.product = resp.data.data.product_verification;
                    return;
                }
                this.showAlert("error", this.error)
            })
                .catch(()   => this.showAlert("error", this.error))
                .finally(() => this.loading = false);
        }
    }
}
</script>

<style scoped>

</style>
