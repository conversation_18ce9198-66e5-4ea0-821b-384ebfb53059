<template>
    <Modal
        confirm-text="Confirm"
        :dark-mode="darkMode"
        close-text="Cancel"
        :small="true"
        no-buttons
        @close="$emit('close')"
    >
        <template v-slot:header>
            <span class="font-bold">Extend credit</span>
        </template>
        <template v-slot:content>
            <div class="flex flex-col gap-2">
                <div class="grid grid-cols-4">
                    <labeled-value label="Credit Type">
                        {{ companyCredit.credit_type }}
                    </labeled-value>
                    <labeled-value label="Initial value">
                        ${{ companyCredit.initial_value }}
                    </labeled-value>
                    <labeled-value label="Remaining value">
                        ${{ companyCredit.remaining_value }}
                    </labeled-value>
                    <labeled-value label="Expires at">
                        <div v-if="companyCredit.expires_at" class="flex flex-col">
                            <p>{{ companyCredit.expires_at }}</p>
                            <p>({{ companyCredit.expires_at_human }})</p>
                        </div>
                        <p v-else>Not set</p>
                    </labeled-value>
                </div>
                <div class="flex items-end gap-1 mx-auto">
                    <labeled-value label="New Expiry Date">
                        <div class="flex gap-1 items-center">
                            <DatePicker
                                v-model="newDate"
                                teleport="body"
                                format="MM-dd-yyyy"
                                :min-date="minDate"
                                :clearable="false"
                                :time-picker="false"
                                disable
                            />
                            <custom-button @click="handleSave" :disabled="isSaveDisabled">
                                Save
                            </custom-button>
                        </div>
                        <p v-if="computedInDays">The credit will be available for {{ computedInDays }} </p>
                    </labeled-value>
                </div>
            </div>
        </template>
    </Modal>
</template>

<script>
import Modal from '../../components/Modal.vue'
import LabeledValue from "../../components/LabeledValue.vue";
import CustomInlineInput from "../../components/CustomInlineInput.vue";
import DatePicker from "@vuepic/vue-datepicker";
import CustomButton from "../../components/CustomButton.vue";
import {DateTime} from "luxon";
import useDateHelper from "../../../../../composables/useDateHelper.js";

export default {
    name: "ExpireExtendCreditModal",
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },

        companyCredit: {
            type: Object,
            required: true
        }
    },

    components: {
        CustomButton,
        CustomInlineInput,
        LabeledValue,
        Modal,
        DatePicker
    },

    methods: {
        handleSave() {
            this.saving = true
            this.$emit('save', new Date(this.newDate).toISOString())
        },
    },

    computed: {
        computedInDays() {
            if (!this.newDate) return null;

            return this.dateHelper.calculateDateDifference(this.newDate.toISOString())
        },

        isSaveDisabled() {
            const dt1 = DateTime.fromISO(this.companyCredit.expires_at);
            const dt2 = DateTime.fromJSDate(this.newDate);
            const hasChanged = !dt1.hasSame(dt2, 'day', {});

            return !hasChanged || this.saving;
        },
        dateHelper() {
            return useDateHelper()
        }
    },

    data() {
        return {
            newDate: new Date(this.companyCredit.expires_at),
            minDate: DateTime.now().startOf('day').plus({days: 1}).toISO(),
            saving: false,
        }
    },

    emits: ['choice']
}
</script>
