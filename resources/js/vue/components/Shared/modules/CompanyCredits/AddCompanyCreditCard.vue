<template>
    <div class="flex flex-col gap-2">
        <span v-if="creditStore.formErrorHandler.message" class="text-red-500">{{creditStore.formErrorHandler.message}}</span>
        <div v-if="canApplyCompanyCredits">
            <div class="font-semibold">
                Add Credit to company
            </div>
            <div class="border p-4 gap-2 rounded-lg flex flex-col"
                 :class="[
                 !darkMode ? 'bg-light-background border-light-border' : 'bg-dark-background border-dark-border'
             ]"
            >
                <div class="grid grid-cols-3 gap-3">
                    <labeled-value label="Amount">
                        <div class="flex items-center gap-1 w-full">
                            <p>$</p>
                            <text-field
                                prefix="$"
                                v-model="creditStore.newCredit.value"
                                :dark-mode="darkMode"
                                type="number"
                                height="h-9"
                            ></text-field>
                        </div>
                    </labeled-value>
                    <labeled-value label="Type" class="w-full">
                        <Dropdown
                            :options="creditTypes"
                            :dark-mode="darkMode"
                            v-model="creditStore.newCredit.type"
                            placeholder="Select Credit Type"
                        >
                        </Dropdown>
                    </labeled-value>
                    <labeled-value label="Company" class="w-full">
                        <autocomplete
                            :dark-mode="darkMode"
                            :disable-input="company !== null"
                            v-model="creditStore.companyId"
                            class="flex flex-1"
                            wrapper-classes="flex flex-1"
                            :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}"
                            :options="companies"
                            :placeholder="'Company name'"
                            :model-value="company.id"
                            :create-user-input-option="true"
                            @search="searchCompanies('companyname', $event)"
                        />
                    </labeled-value>
                </div>
                <labeled-value label="Billing Profiles" tooltip="The credit will only be used for invoices generated by the selected profiles. If none are selected, all profiles are eligible.">
                    <div class="grid grid-cols-1 gap-2">
                        <div v-for="profile in billingProfiles" class="flex items-center gap-1">
                            <custom-checkbox :model-value="creditStore.newCredit.billing_profile_ids?.includes(profile.id)" @click="handleBillingProfileClick(profile)"/>
                            <div class="flex flex-col gap-1 rounded p-2">
                                <div class="flex items-center gap-1">
                                    <entity-hyperlink
                                        :entity-id="profile.id"
                                        :dark-mode="darkMode"
                                        type="billing_profile"
                                        :suffix="profile.id"
                                    >
                                    </entity-hyperlink>
                                    <p v-if="profile?.name" class="max-w-56 truncate text-ellipsis">{{profile?.name}}</p>
                                </div>
                                <div class="flex items-center gap-1">
                                    <payment-method-badge
                                        :type="profile?.payment_method"
                                        :reference="profile?.payment_method_data?.number ? `${profile?.payment_method_data?.number} ${profile?.payment_method_data?.expiry}` : null"
                                    />
                                    <badge>${{profile.threshold}}</badge>
                                    <p>{{profile.charge_attempts}} attempts</p>
                                    <simple-icon
                                        :class="profile.process_auto ? '' : 'opacity-50'"
                                        :color="profile.process_auto ? simpleIcon.colors.BLUE : simpleIcon.colors.GRAY"
                                        :icon="simpleIcon.icons.HOLDING_MONEY"
                                        :tooltip="profile.process_auto ? 'The invoice will be paid as soon as it gets issued' : 'The invoice will NOT be paid as soon as it gets issued'"
                                    >
                                    </simple-icon>
                                    <simple-icon
                                        :class="profile.default ? '' : 'opacity-50'"
                                        :color="profile.default ? simpleIcon.colors.BLUE : simpleIcon.colors.GRAY"
                                        :icon="simpleIcon.icons.FALLBACK"
                                        :tooltip="profile.default ? 'Preferred' : 'Not Preferred'"
                                    >
                                    </simple-icon>
                                </div>
                                <div v-if="profile?.campaigns?.length > 0"
                                     class="flex items-center gap-1"
                                >
                                    <limited-list :show-count="2" :list-items="profile.campaigns" empty-message="No Associated Campaigns"/>
                                </div>
                            </div>
                            <simple-icon
                                :icon="simpleIcon.icons.EYE"
                                :dark-mode="darkMode"
                                tooltip="View Billing Profile"
                                clickable
                                @click="toggleBillingProfileModal(profile)"
                            >

                            </simple-icon>
                        </div>
                    </div>
                </labeled-value>
                <labeled-value class="col-span-full" label="Reason">
                    <CustomTextarea v-model="creditStore.newCredit.note" placeholder="Reason..."/>
                </labeled-value>
                <custom-button
                    :disabled="loading"
                    @click="handleApplyCredit"
                    :dark-mode="darkMode"
                    class="w-fit self-end"
                >
                    Apply Credit
                </custom-button>
            </div>
        </div>
        <modal
            v-if="showModal"
            small
            :dark-mode="darkMode"
            @close="toggleBillingProfileModal()"
            hide-cancel
            hide-confirm
        >
            <template v-slot:header>
                Billing Profile
            </template>
            <template v-slot:content>
                <billing-profile-form
                    :dark-mode="darkMode"
                    :company-id="company?.id"
                    :billing-profile-id="selectedProfileId"
                    readonly
                >
                </billing-profile-form>
            </template>
        </modal>
    </div>
</template>
<script>
import {useCompanyCreditManagementStore} from "../../../../../stores/credit/company-credit-management.store";
import CustomButton from "../../components/CustomButton.vue";
import CustomInlineInput from "../../components/CustomInlineInput.vue";
import Autocomplete from "../../components/Autocomplete.vue";
import Dropdown from "../../components/Dropdown.vue";
import SharedApiService from "../../services/api";
import {useCreditManagementStore} from "../../../../../stores/credit/credit-management.store";
import {PERMISSIONS, useRolesPermissions} from "../../../../../stores/roles-permissions.store";
import CustomTextarea from "../../components/CustomTextarea.vue";
import LabeledValue from "../../components/LabeledValue.vue";
import TextField from "../../../IndustryManagement/WebsiteManagement/ApiKeys/components/TextField.vue";
import PaymentMethodBadge from "../../../Billing/InvoicePaymentsModule/PaymentMethodBadge.vue";
import CustomCheckbox from "../../SlideWizard/components/CustomCheckbox.vue";
import Badge from "../../components/Badge.vue";
import ApiService from "../../../BillingManagement/services/billing-profiles-api.js";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../composables/useSimpleIcon.js";
import LimitedList from "../../components/Simple/LimitedList.vue";
import Alert from "../../components/Alert.vue";
import Modal from "../../components/Modal.vue";
import BillingProfileForm from "../../../BillingManagement/components/Modals/BillingProfileForm.vue";
import EntityHyperlink from "../../../BillingManagement/components/EntityHyperlink.vue";

export default {
    name: "AddCompanyCreditCard",
    components: {
        EntityHyperlink,
        BillingProfileForm,
        Modal, Alert,
        LimitedList,
        SimpleIcon,
        Badge,
        CustomCheckbox,
        PaymentMethodBadge,
        TextField, LabeledValue, CustomTextarea, Dropdown, Autocomplete, CustomInlineInput, CustomButton},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        company: {
            type: Object,
            require: false,
            default: null
        }
    },
    emits: ['creditRequested'],
    data () {
        return {
            sharedApi: SharedApiService.make(),
            creditStore: useCompanyCreditManagementStore(),
            creditTypeStore: useCreditManagementStore(),
            permissionStore: useRolesPermissions(),
            creditTypes: [],
            companies: [],
            creditValue: 100,
            selectedCreditType: null,
            loading: false,
            billingProfiles: [],
            billingProfileService: ApiService.make(),
            selectedProfileId: null,
            showModal: false,
        }
    },
    created() {
        if (this.companies !== null) {
            this.companies = [{
                id: this.company.id,
                name: `${this.company.id}: ${this.company.name}`
            }];
        }
        this.getCreditTypes();
        this.getBillingProfiles();
    },
    computed: {
        canApplyCompanyCredits() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_BILLING_CREDITS_COMPANY_APPLY);
        },
        simpleIcon() {
            return useSimpleIcon()
        }
    },
    methods: {
        handleBillingProfileClick(profile){
            if (!this.creditStore.newCredit.billing_profile_ids?.length) {
                this.creditStore.newCredit.billing_profile_ids = []
            }

            const shouldAdd = !this.creditStore.newCredit.billing_profile_ids.includes(profile.id)
            if (shouldAdd) {
                this.creditStore.newCredit.billing_profile_ids.push(profile.id)
            } else {
                this.creditStore.newCredit.billing_profile_ids = this.creditStore.newCredit.billing_profile_ids.filter(e => e !== profile.id)
            }
        },
        async getBillingProfiles(){
            const response = await this.billingProfileService.getBillingProfiles({
                company_id: this.company.id,
                all: true,
                show_archived: 0
            })
            this.billingProfiles = response.data.data
        },
        toggleBillingProfileModal(profile){
            this.showModal = !this.showModal
            this.selectedProfileId = profile?.id
        },
        async handleApplyCredit() {
            this.loading = true;
            const message = await this.creditStore.applyCredit();
            this.$emit('creditRequested', message)
            this.loading = false;
        },
        async getCreditTypes() {
            try {
                const response = await this.creditTypeStore.api.getCreditTypes();
                this.creditTypes = response.data.data.map((element) => {
                    return {
                        id: element.slug,
                        name: element.name,
                    }

                });
            } catch (error) {
                console.error(error)
            }
        },
        async searchCompanies(nameType, query) {
            this.sharedApi.searchCompanyNamesAndId(query).then(res => {
                if (res.data.data.status === true) {
                    this.companies = res.data.data.companies;
                }
            })
        },
    }
}
</script>
