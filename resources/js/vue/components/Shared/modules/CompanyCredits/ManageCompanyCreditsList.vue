<template>
    <div
        class="flex flex-col border rounded-lg relative overflow-scroll"
        :class="{
        'bg-light-module border-light-border':!darkMode,
            'bg-dark-module border-dark-border':darkMode}"
    >
        <div class="flex items-center border-b sticky top-0 z-20 p-4 bg-white">
            <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight flex-1"
                :class="
            {'bg-light-module border-light-border':!darkMode,
            'bg-dark-module border-dark-border':darkMode}"
            >
                Manage Company Credits
            </h5>
            <simple-icon
                :icon="isAllCreditsVisible ? simpleIcon.icons.EYE_SLASH : simpleIcon.icons.EYE"
                :tooltip="isAllCreditsVisible ? 'Hide drained credits'  : 'View all credits'"
                clickable
                @click="isAllCreditsVisible = !isAllCreditsVisible"
            />
        </div>
        <table>
            <tr class="text-xs text-slate-500 uppercase font-medium border-b bg-light-module border-light-border text-left">
                <th class="py-2 px-2">Type</th>
                <th class="py-2 px-2">Billing Profile</th>
                <th class="py-2 px-2">Initial Balance</th>
                <th class="py-2 px-2">Remaining Balance</th>
                <th class="py-2 px-2">Expiry</th>
                <th class="py-2 px-2">Actions</th>
            </tr>
            <tr v-for="credit in credits" class="text-xs border-b border-light-border last:border-b-0 ">
                <td class="py-2 px-2">{{ credit.credit_type }}</td>
                <td class="py-2 px-2 flex flex-col gap-2">
                    <div v-for="profile in credit.billing_profiles">
                        <div class="flex flex-col mr-1">
                            <div class="flex gap-1 items-center flex-wrap">
                                <entity-hyperlink
                                    :entity-id="profile.id"
                                    :dark-mode="darkMode"
                                    type="billing_profile"
                                    :suffix="profile.id"
                                >
                                </entity-hyperlink>
                                <simple-icon
                                    :class="profile.process_auto ? '' : 'opacity-50'"
                                    :color="profile.process_auto ? simpleIcon.colors.BLUE : simpleIcon.colors.GRAY"
                                    :icon="simpleIcon.icons.HOLDING_MONEY"
                                    :tooltip="profile.process_auto ? 'The invoice will be paid as soon as it gets issued' : 'The invoice will NOT be paid as soon as it gets issued'"
                                >
                                </simple-icon>
                                <simple-icon
                                    :class="profile.default ? '' : 'opacity-50'"
                                    :color="profile.default ? simpleIcon.colors.BLUE : simpleIcon.colors.GRAY"
                                    :icon="simpleIcon.icons.FALLBACK"
                                    :tooltip="profile.default ? 'Preferred' : 'Not Preferred'"
                                >
                                </simple-icon>
                                <payment-method-badge
                                    :type="profile?.payment_method"
                                    :reference="profile?.payment_method_data?.number ? `${profile?.payment_method_data?.number} ${profile?.payment_method_data?.expiry}` : null"
                                />
                                <badge>${{ profile.threshold }}</badge>
                                <p>{{ profile.charge_attempts }} attempts</p>
                                <div v-if="profile?.campaigns?.length > 0"
                                     class="flex items-center gap-1"
                                >
                                    <limited-list :show-count="1" :list-items="profile.campaigns"
                                                  empty-message="No Associated Campaigns"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </td>
                <td class="py-2 px-2">{{ $filters.currency(credit.initial_value) }}</td>
                <td class="py-2 px-2">{{ $filters.currency(credit.remaining_value) }}</td>
                <td class="py-2 px-2">
                    <div v-if="!credit.is_expired" class="col-span-3 flex items-center">
                        <badge class="flex items-center gap-1" :dark-mode="darkMode" color="green">
                            <simple-icon
                                :icon="simpleIcon.icons.CHECK_CIRCLE"
                                :color="simpleIcon.colors.GREEN"/>
                            {{ getCreditActiveMessage(credit) }}
                        </badge>
                    </div>
                    <div v-else class="col-span-3 flex items-center">
                        <badge class="flex items-center gap-1" :dark-mode="darkMode" color="red">
                            <simple-icon
                                :icon="simpleIcon.icons.X_CIRCLE"
                                :color="simpleIcon.colors.RED"
                            />
                            Expired on {{ $filters.dateFromTimestamp(credit.expires_at) }}
                        </badge>
                    </div>
                </td>
                <td class="py-2 px-2">
                    <div class="flex flex-col justify-between gap-2">
                        <custom-button
                            :disabled="loading"
                            v-if="!credit.is_expired && canExpireCredit"
                            color="red"
                            :dark-mode="darkMode"
                            @click="handleExpireCredit(credit.id)"
                            class="text-xs"
                        >
                            Expire now
                        </custom-button>
                        <custom-button
                            v-if="canExtendCredit"
                            :disabled="loading"
                            @click.stop="toggleShowExpireExtendCreditModal(credit, 'extend')"
                            :dark-mode="darkMode"
                            class="px-0 text-xs"
                        >
                            Set Expiry Date
                        </custom-button>
                    </div>
                </td>
            </tr>
        </table>
        <p v-if="!credits?.length" class="text-center">
            No data
        </p>
        <ExpireExtendCreditModal
            v-if="isExpireExtendCreditModalVisible"
            :company-credit="selectCreditItem"
            @close="toggleShowExpireExtendCreditModal(null)"
            @save="handleSave"
        />
    </div>
</template>
<script>
import {useCompanyCreditManagementStore} from "../../../../../stores/credit/company-credit-management.store";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../composables/useSimpleIcon";
import CustomButton from "../../components/CustomButton.vue";
import Badge from "../../components/Badge.vue";
import {PERMISSIONS, useRolesPermissions} from "../../../../../stores/roles-permissions.store";
import ExpireExtendCreditModal from "./ExpireExtendCreditModal.vue";
import PaymentMethodBadge from "../../../Billing/InvoicePaymentsModule/PaymentMethodBadge.vue";
import LimitedList from "../../components/Simple/LimitedList.vue";
import EntityHyperlink from "../../../BillingManagement/components/EntityHyperlink.vue";

const simpleIcon = useSimpleIcon();
export default {
    name: "ManageCompanyCreditsList",
    components: {
        EntityHyperlink,
        LimitedList, PaymentMethodBadge, ExpireExtendCreditModal, Badge, CustomButton, SimpleIcon},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        companyId: {
            type: Number,
            required: true,
        },
    },
    data() {
        return {
            simpleIcon,
            creditStore: useCompanyCreditManagementStore(),
            permissionStore: useRolesPermissions(),
            loading: false,
            selected: null,
            isAllCreditsVisible: false,
            selectCreditItem: null,
            isExpireExtendCreditModalVisible: null,
        }
    },
    created() {
        this.getCompanyCredits();
    },
    computed: {
        credits() {
            return Object.values(this.creditStore.credits).map(e => e.list)
                .flat()
                .sort((a, b) => +(new Date(a.applied_at)) > +(new Date(b.applied_at)))
        },
        canExtendCredit() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_BILLING_CREDITS_COMPANY_EXTEND)
        },
        canExpireCredit() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_BILLING_CREDITS_COMPANY_EXPIRE)
        },
    },
    methods: {
        toggleShowExpireExtendCreditModal(creditItem, action) {
            if (!creditItem) {
                this.selectCreditItem = null
            } else {
                this.selectCreditItem = {...creditItem, _action: action}
            }

            this.isExpireExtendCreditModalVisible = !!creditItem
        },
        async getCompanyCredits() {
            this.loading = true;
            await this.creditStore.getCompanyCredits(this.companyId);
            this.loading = false;
        },
        async handleCreditAction(action, creditId) {
            this.loading = true;
            try {
                await this.creditStore[action](this.companyId, creditId);
            } finally {
                this.loading = false;
            }
        },

        async handleSave(date) {
            await this.creditStore.extendCredit(
                this.selectCreditItem.company_id,
                this.selectCreditItem.id,
                date
            )

            this.toggleShowExpireExtendCreditModal()
            this.$emit("credit-action")
        },

        async handleExpireCredit(creditId) {
            await this.creditStore.expireCredit(this.companyId, creditId)
            this.$emit("credit-action")
        },

        getCreditActiveMessage(creditItem) {
            if (creditItem.expires_at) {
                return `Active until ${this.$filters.dateFromTimestamp(creditItem.expires_at)} (${creditItem.expires_at_human})`;
            }

            return `Active no expiry`;
        },
    }
}
</script>
