<template>
    <div class="grid md:flex gap-3" v-if="!loading && company !== null">
        <div class="hidden w-32 h-32 flex-shrink-0 xl:block border rounded-lg overflow-hidden p-2 relative"
             :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
            <div class="w-full h-full flex-shrink-0  bg-center bg-no-repeat bg-contain"
                 :style="{ backgroundImage: 'url(' + company.image + ')' }">
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 flex-grow">
            <div>
                <h3 class="text-lg font-semibold mb-1 text-blue-550">
                    <a :href="'/companies/' + company.mi_id" target="_blank">{{ company.name }}</a>
                </h3>

                <div class="flex items-center mb-2">
                    <svg class="w-4 mr-2 flex-shrink-0 fill-current text-slate-500" width="12" height="12"
                         viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd"
                              d="M1.56225 5.25H3.02175C3.0885 4.0905 3.309 3.0225 3.6495 2.1615C3.10126 2.49744 2.63131 2.94689 2.27128 3.47963C1.91125 4.01236 1.66949 4.61602 1.56225 5.25ZM6 0C4.4087 0 2.88258 0.632141 1.75736 1.75736C0.632141 2.88258 0 4.4087 0 6C0 7.5913 0.632141 9.11742 1.75736 10.2426C2.88258 11.3679 4.4087 12 6 12C7.5913 12 9.11742 11.3679 10.2426 10.2426C11.3679 9.11742 12 7.5913 12 6C12 4.4087 11.3679 2.88258 10.2426 1.75736C9.11742 0.632141 7.5913 0 6 0ZM6 1.5C5.943 1.5 5.826 1.524 5.65125 1.6965C5.47275 1.872 5.2785 2.16375 5.0985 2.583C4.80675 3.26325 4.59375 4.1895 4.524 5.25H7.476C7.40625 4.1895 7.19325 3.26325 6.9015 2.583C6.7215 2.163 6.5265 1.872 6.34875 1.6965C6.174 1.524 6.057 1.5 6 1.5ZM8.97825 5.25C8.9115 4.0905 8.691 3.0225 8.3505 2.1615C8.89874 2.49744 9.36869 2.94689 9.72872 3.47963C10.0887 4.01236 10.3305 4.61602 10.4377 5.25H8.97825ZM7.476 6.75H4.524C4.59375 7.8105 4.80675 8.73675 5.0985 9.417C5.2785 9.837 5.4735 10.128 5.65125 10.3035C5.826 10.476 5.943 10.5 6 10.5C6.057 10.5 6.174 10.476 6.34875 10.3035C6.52725 10.128 6.72225 9.83625 6.9015 9.417C7.19325 8.73675 7.40625 7.8105 7.476 6.75ZM8.3505 9.8385C8.691 8.97825 8.9115 7.9095 8.97825 6.75H10.4377C10.3305 7.38398 10.0887 7.98764 9.72872 8.52037C9.36869 9.05311 8.89874 9.50256 8.3505 9.8385ZM3.6495 9.8385C3.309 8.9775 3.0885 7.9095 3.0225 6.75H1.56225C1.66949 7.38398 1.91125 7.98764 2.27128 8.52037C2.63131 9.05311 3.10126 9.50256 3.6495 9.8385Z"/>
                    </svg>
                    <a :href="companyWebsite" target="_blank" class="text-primary-500 text-sm truncate">
                        {{ company.website }}
                    </a>
                </div>

                <div class="block xl:flex items-center mb-3">
                    <div class="flex items-center">
                        <svg class="w-4 mr-2 flex-shrink-0 fill-current text-slate-500" width="12" height="12"
                             viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M11.7796 9.3999L9.04134 6.91027C8.91191 6.79263 8.74182 6.72988 8.56699 6.73529C8.39216 6.74069 8.22628 6.81382 8.10435 6.93923L6.49241 8.59697C6.10441 8.52287 5.32438 8.2797 4.52144 7.47879C3.7185 6.67518 3.47533 5.89313 3.40325 5.50783L5.05965 3.89523C5.18522 3.77339 5.25846 3.60748 5.26386 3.4326C5.26927 3.25773 5.20642 3.08761 5.08862 2.95825L2.59964 0.220734C2.48179 0.0909691 2.31799 0.0122571 2.14304 0.00131473C1.96808 -0.00962763 1.79575 0.0480617 1.66265 0.162131L0.200925 1.4157C0.0844657 1.53258 0.0149551 1.68814 0.00557879 1.85287C-0.00452532 2.02127 -0.197177 6.01034 2.89603 9.10486C5.59449 11.8026 8.97465 12 9.90558 12C10.0416 12 10.1252 11.996 10.1474 11.9946C10.3121 11.9854 10.4676 11.9156 10.5839 11.7986L11.8368 10.3362C11.9513 10.2035 12.0094 10.0313 11.9987 9.8564C11.988 9.68146 11.9094 9.51763 11.7796 9.3999V9.3999Z"/>
                        </svg>
                        <p class="text-sm cursor-pointer text-blue-550" @click="dialCompanyPhone">
                            {{ $filters.formatPhoneNumber(company.phone) || '-' }}
                        </p>
                    </div>
                </div>

                <div class="flex items-center mb-1">
                    <loading-spinner v-if="loadingSaleStatus" size="xs" :dark-mode="darkMode"/>
                    <button
                        v-else-if="!editingSalesStatus"
                        @click="toggleEditStatus"
                        class=" px-5 cursor-pointer flex items-center py-1 text-xs font-semibold rounded-full capitalize  hover:shadow-lg"
                        :class="{
            'bg-orange-100 border-orange-700 text-orange-700': !darkMode,
            'bg-dark-background border-transparent text-orange-500': darkMode
        }"
                    >
                        <svg class="mr-2" width="11" height="13" viewBox="0 0 11 13" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8.25 0L10.3125 1.95117L8.74019 3.43926L6.67769 1.48809L8.25 0ZM0 7.79688V9.74805H2.0625L7.76806 4.35827L5.70556 2.40709L0 7.79688ZM0 11.6992H11V13H0V11.6992Z" fill="currentColor"></path></svg>  {{ company.sales_status }}
                    </button>
                    <div class="flex" v-else>
                        <Dropdown
                            class="min-w-48"
                            :dark-mode="darkMode"
                            placeholder="Change the sales status"
                            :options="salesStatusOptions"
                            v-model="editing.sales_status"
                            :selected="salesStatusOptions.find(i => i.name === editing.sales_status) || 0"
                        ></Dropdown>
                        <button
                            @click="saveSalesStatus"
                            class="text-white h-9 bg-emerald-500 ml-1 hover:bg-emerald-600 mt-5 sm:mt-0 px-5 py-2 font-medium text-sm rounded-md inline-flex items-center justify-center"
                        >
                            Save
                        </button>
                        <button
                            v-if="editingSalesStatus"
                            @click="toggleEditStatus"
                            class="ml-2 text-white h-9 bg-slate-400 hover:bg-slate-500 mt-5 sm:mt-0 px-5 py-2 font-medium text-sm rounded-md inline-flex items-center justify-center"
                        >
                            Cancel
                        </button>
                    </div>
                    <div
                        class="px-5 inline-flex items-center py-1 text-xs font-semibold rounded-full capitalize"
                        :class="{
            'bg-orange-100 border-orange-700 text-orange-700': !darkMode,
            'bg-dark-background border-transparent text-orange-500': darkMode
        }"
                    >
                        {{ company.status ?? 'Unknown' }}
                    </div>
                    <div
                        class="px-5 inline-flex items-center py-1 text-xs font-semibold rounded-full capitalize"
                        :class="{
            'bg-orange-100 border-orange-700 text-orange-700': !darkMode,
            'bg-dark-background border-transparent text-orange-500': darkMode
        }"
                    >
                        {{ company.type ? company.type : 'Unknown' }}
                    </div>
                    <div v-for="industry in company.industries"
                         class="mr-3 px-5 inline-flex items-center py-1 text-xs font-semibold rounded-full capitalize"
                         :class="{'bg-cyan-150 border-cyan-150 text-blue-550': !darkMode, 'bg-dark-background border-transparent text-blue-550': darkMode}">
                        {{ industry.name }}
                    </div>
                </div>
            </div>

            <div class="md:col-span-2 columns-1 md:columns-2 2xl:columns-3 space-y-1"
                 :class="[darkMode ? 'text-slate-400' : 'text-slate-700']">
                <p class="font-semibold text-sm inline-flex items-center">
                    Rejection:
                    <span class="font-normal ml-2">{{ company.rejection_percentage }}%</span>
                </p>

                <div>
                    <div class="inline-flex items-center ">

                        <p class="font-semibold text-sm mr-2">Prescreened:</p>
                        <div>
                            <svg v-if="company.prescreened" width="12" height="10" viewBox="0 0 12 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M4.27411 6.5197L1.62626 3.69335L0 5.43719L4.27642 10L12 1.74138L10.3714 0L4.27411 6.5197Z" fill="#00AE07"/>
                            </svg>
                            <svg v-else width="9" height="9" viewBox="0 0 9 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M0.219369 0.228966C0.359874 0.088363 0.550413 0.00937635 0.749086 0.00937635C0.947759 0.00937635 1.1383 0.088363 1.2788 0.228966L4.49532 3.44872L7.71183 0.228966C7.78095 0.157334 7.86362 0.100197 7.95503 0.0608901C8.04644 0.0215834 8.14476 0.00089368 8.24424 2.83178e-05C8.34373 -0.********* 8.44239 0.0181391 8.53447 0.0558497C8.62655 0.0935604 8.7102 0.14925 8.78055 0.21967C8.8509 0.290089 8.90653 0.373828 8.94421 0.466001C8.98188 0.558173 9.00084 0.656933 8.99997 0.756517C8.99911 0.856102 8.97844 0.954516 8.93917 1.04602C8.8999 1.13752 8.84282 1.22028 8.77126 1.28947L5.55475 4.50922L8.77126 7.72897C8.90774 7.87042 8.98326 8.05987 8.98156 8.25652C8.97985 8.45316 8.90105 8.64127 8.76214 8.78033C8.62322 8.91939 8.4353 8.99826 8.23885 8.99997C8.0424 9.00168 7.85314 8.92608 7.71183 8.78947L4.49532 5.56972L1.2788 8.78947C1.13749 8.92608 0.948232 9.00168 0.751782 8.99997C0.555332 8.99826 0.367412 8.91939 0.228496 8.78033C0.0895797 8.64127 0.0107821 8.45316 0.******** 8.25652C0.******** 8.05987 0.082888 7.87042 0.219369 7.72897L3.43588 4.50922L0.219369 1.28947C0.0789073 1.14882 0 0.958089 0 0.759216C0 0.560343 0.0789073 0.369612 0.219369 0.228966V0.228966Z" fill="#E13131"/>
                            </svg>
                        </div>
                    </div>
                </div>

                <div>
                    <div class="inline-flex items-center ">
                        <p class="font-semibold text-sm mr-2">Account Manager:</p>
                        <div class="inline-flex items-center">
                            <p class="text-sm"
                               :class="[company.account_manager.active ? '' : 'text-gray-500 cursor-help' ]"
                               :title="[company.account_manager.active === false ? 'Inactive Account Manager' : '' ]"
                            >
                                {{ company.account_manager.name || 'None Assigned' }}
                            </p>
                        </div>
                    </div>
                </div>

                <div>
                    <div class="inline-flex items-center ">
                        <p class="font-semibold text-sm mr-2">Business Development Manager:</p>
                        <p class=" text-sm">{{ company.business_development_manager.name || 'None Assigned' }}</p>
                    </div>
                </div>

                <div>
                    <div class="inline-flex items-center ">
                        <p class="font-semibold text-sm mr-2">Onboarding Manager:</p>
                        <p class=" text-sm">{{ company.onboarding_manager.name || 'None Assigned' }}</p>
                    </div>
                </div>

                <div>
                    <div class="inline-flex items-center ">
                        <p class="font-semibold text-sm mr-2">Sales Development Representative:</p>
                        <p class=" text-sm">{{ company.sales_development_representative.name || 'None Assigned' }}</p>
                    </div>
                </div>

                <div>
                    <div class="text-sm inline-flex items-center truncate w-full">
                        <span class="font-semibold mr-2">Office:</span>
                        <span class="truncate" >{{ company.main_office_location}}</span>
                    </div>
                </div>

                <div>
                    <div  class="text-sm inline-flex items-center  w-full">
                        <span class="font-semibold mr-2">Employees:</span>
                        <span>{{ company.employees  ?? '-' }}</span>
                    </div>
                </div>

                <div>
                    <div class="text-sm inline-flex items-center   w-full">
                        <span class="font-semibold mr-2">Revenue:</span>
                        <span>{{ company.revenue ?? '-' }}</span>
                    </div>
                </div>

                <div>
                    <div class="text-sm inline-flex items-center  w-full">
                        <span class="font-semibold mr-2">Legacy:</span>
                        <span v-if="company.legacy_id" class="text-primary-500 truncate">
                            <a :href="getLegacyAdminCompanyUrl(company.legacy_id)" target="_blank">
                                                {{ company.name }}
                                            </a>
                        </span>
                    </div>
                </div>

            </div>
        </div>
    </div>
</template>

<script>
import ApiService from "../services/api";
import LoadingSpinner from "../components/LoadingSpinner.vue";
import LegacyAdminMixin from "../mixins/legacy-admin-mixin";
import Dropdown from "../components/Dropdown.vue";
import DispatchesGlobalEventsMixin from "../../../mixins/dispatches-global-events-mixin";
import {AlertTypes} from "../mixins/has-alerts-mixin";
import {CommunicationRelationTypes} from "../../Communications/enums/communication.js";

export default {
    name: "CompanyOverview",
    components: {Dropdown, LoadingSpinner},
    mixins: [LegacyAdminMixin, DispatchesGlobalEventsMixin],
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: Number,
            default: null
        },
    },
    data() {
        return {
          loading: false,
          api: ApiService.make(),
          company: null,
          editingSalesStatus: false,
          editing: {
            sales_status: null,
          },
          salesStatusOptions: [],
          saving: false,
          errorMessage: "Something went wrong while fetching company overview.",
          loadingSaleStatus: false
        }
    },
    mounted() {
        this.loadCompanyOverview();
        this.loadingSaleStatus = true;
        this.api.getSaleStatusTypes().then(resp => {
            if(resp?.data?.data?.status === true) {
                this.salesStatusOptions = resp?.data?.data?.statuses_as_select_array ?? [];
            } else {
                console.error(resp?.data?.data?.message || this.errorMessage)
            }
        }).catch(e => console.error("Get Sales Status Options Error: ", e))
          .finally(()=> {
          this.loadingSaleStatus = false;
        });
    },
    computed: {
        companyWebsite() {
            if(this.company.website.includes('http', 'https')) {
                return this.company.website
            }
            else {
                return '//' + this.company.website
            }
        },
    },
    methods: {
        loadCompanyOverview() {
            if(this.companyId) {
                this.loading = true;
                this.api.getCompanyOverview(this.companyId)
                    .then(resp => {
                        if(resp?.data?.data?.status === true) {
                            this.company = resp?.data?.data?.overview ?? null;
                        } else {
                            console.error(resp?.data?.data?.message || this.errorMessage)
                        }
                    })
                    .catch(e => console.error("Get Company Overview Error: ", e))
                    .finally(() => this.loading = false);
            } else {
                console.error("No company ID.");
            }
        },
        dialCompanyPhone() {
            this.dispatchGlobalEvent('call', {phone: this.company.phone, name: this.company.name, id: this.company.phone_location_id, relType: CommunicationRelationTypes.COMPANY_LOCATIONS, relId: this.company.phone_location_id})
        },
        toggleEditStatus(){
            this.editingSalesStatus = !this.editingSalesStatus
        },
        saveSalesStatus() {
            this.saving = true;
            const payload = {
                sales_status: this.editing.sales_status,
                company_id: this.company.mi_id
            };

            this.api.updateSalesStatus(this.company.mi_id, payload).then(resp => {
                if(resp.data.data.status) {
                    const companyRefresh = resp.data.data.company;
                    if (companyRefresh) {
                        Object.assign(this.company, companyRefresh);
                    }
                }
                else {
                    throw new Error("Status was not successful");
                }
            }).catch((e) => {
                console.error("Saving Company Error:", e); // Log to console for bug reports if needed.
                this.showAlert(AlertTypes.error, e.response.data.message);
            }).finally(() => {
                this.saving = false;
                this.editingSalesStatus = false;
            });
        }
    }
}
</script>

<style lang="scss">
</style>
