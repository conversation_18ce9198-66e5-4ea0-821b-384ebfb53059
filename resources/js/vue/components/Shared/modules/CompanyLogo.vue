<template>
    <div
        class="flex items-center font-bold mb-3 ml-4"
        :class="[darkMode ? 'text-grey-50' : 'text-grey-600']"
    >
        <svg
            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
            stroke="currentColor" class="w-6 mr-2">
            <path
                stroke-linecap="round" stroke-linejoin="round"
                d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z"/>
        </svg>

        <h5 class="font-semibold">Logo</h5>
    </div>

    <div class="relative">
        <loading-spinner
            v-if="loading"
            :dark-mode="darkMode"
        />
        <div
            v-else
            :class="[darkMode ? 'text-grey-300' : 'text-grey-600']"
        >
            <div
                v-if="isLogoSetup()"
                class="flex flex-col md:flex-row md:items-center overflow-hidden mx-4 my-5"
            >
                <div
                    class="h-48 w-48 flex-shrink-0 xl:block border rounded-lg p-2 relative"
                    :class="[darkMode ? 'bg-dark-background border-dark-border' : 'bg-light-background  border-light-border']"
                >
                    <div
                        class="w-full h-full flex-shrink-0 bg-center bg-no-repeat bg-contain"
                        :style="{ backgroundImage: 'url(' + logoUrl + ')' }"
                    />
                </div>
            </div>
            <div class="mt-3 ml-4">
                <button
                    v-if="hasEditRights"
                    class="text-primary-500 text-sm font-semibold rounded-md px-5 py-2.5"
                    :class="[darkMode ? 'bg-dark-background' : 'bg-primary-50 hover:bg-primary-100']"
                    @click="initiateUpdate()"
                >
                    {{ getActionButtonLabel() }}
                </button>
            </div>
        </div>
        <Modal
            v-if="logoModal"
            :small="true"
            @confirm="saveLogo"
            @close="haltUpdate"
            :dark-mode="darkMode"
            :close-text="'Cancel'"
            :confirm-text="getModalConfirmationText()"
        >
            <template v-slot:header>
                <p class="font-medium">
                    {{ getModalHeading() }}
                </p>
            </template>
            <template v-slot:content>
                <div class="grid grid-cols-1 gap-3">
                    <div>
                        <div class="flex flex-col md:flex-row md:items-center">
                            <div
                                v-if="isLogoSetup()"
                                class="hidden h-32 w-32 flex-shrink-0 xl:block border rounded-lg p-2 relative"
                                :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
                            >
                                <div
                                    class="w-full h-full flex-shrink-0  bg-center bg-no-repeat bg-contain"
                                    :style="{ backgroundImage: 'url(' + logoUrl + ')' }"
                                />
                            </div>

                            <div class="md:ml-6 my-5 md:my-0">
                                <p class="font-medium text-md">
                                    Please note that logo cannot contain contact details.
                                </p>

                                <ul
                                    class="list-disc list-inside font-light text-sm ml-2 mt-2"
                                    :class="[darkMode ? 'text-grey-400' : 'text-grey-600']"
                                >
                                    <li>Recommended size: 200 x 200 pixels</li>
                                    <li>2MB file size maximum</li>
                                    <li>Logo image cannot contain details e.g. phone number, email address, address.</li>
                                    <li>Logos will appear on the site profile after a review period.</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="mt-10">
                        <file-upload
                            :dark-mode="darkMode"
                            :accept="permittedLogoTypes"
                            @file-uploaded="getFile"
                            @file-upload-error="fileUploadError"
                        />
                    </div>
                </div>
            </template>
        </Modal>
        <alerts-container
            v-if="alertActive"
            :dark-mode="darkMode"
            :alert-type="alertType"
            :text="alertText"
        />
    </div>
</template>

<script>
import CustomButton from "../components/CustomButton.vue";
import Modal from "../components/Modal.vue";
import LoadingSpinner from "../components/LoadingSpinner.vue";
import SharedApiService from "../services/api";
import AlertsMixin from "../../../mixins/alerts-mixin";
import AlertsContainer from "../components/AlertsContainer.vue";
import FileUpload from "../../Inputs/FileUpload.vue";
import {REQUEST} from "../../../../constants/APIRequestKeys";

export default {
    name: "CompanyLogo",
    components: {
        Modal,
        FileUpload,
        CustomButton,
        LoadingSpinner,
        AlertsContainer,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        hasEditRights: {
            type: Boolean,
            default: false,
        },
        companyId: {
            type: Number,
            default: null,
        },
    },
    mixins: [
        AlertsMixin,
    ],
    data() {
        return {
            apiService: SharedApiService.make(),
            logoModal: false,
            loading: false,
            updating: false,
            saving: false,
            file: null,
            logoUrl: null,
            errorMessages: {
                retrievalError: `An error occurred while fetching the company logo.`,
                updateError: `An error occurred while updating the company logo.`,
            },
            permittedLogoTypes: '.jpg,.jpeg,.png',
        }
    },
    created() {
        if(this.companyId) {
            this.getLogo();
        }
    },
    methods: {
        initiateUpdate() {
            this.logoModal = true;
        },
        haltUpdate() {
            this.logoModal = false;
            this.loading = false;
            this.saving = false;
            this.file = null;
        },
        getLogo () {
            if(this.loading) {
                return;
            }

            this.loading = true;
            this.apiService.getCompanyLogo(this.companyId)
                .then(resp => {
                    if (resp.data?.data?.status === true) {
                        this.logoUrl = resp.data.data[REQUEST.LOGO];
                        return;
                    }
                    this.handleError(resp?.data?.data?.msg || this.errorMessages.retrievalError);
                })
                .catch(e => this.handleError(e.response.data.message || this.errorMessages.retrievalError, e))
                .finally(() => this.loading = false);

        },
        saveLogo() {
            if(this.file === null || this.saving === true) {
                return;
            }

            this.saving = true;
            const formData = new FormData();

            formData.append(REQUEST.LOGO, this.file);

            this.apiService.saveCompanyLogo(this.companyId, formData)
                .then(resp => {
                    if (resp.data?.data?.status === true) {
                        this.logoUrl = resp.data.data[REQUEST.LOGO];
                        return;
                    }
                    this.handleError(resp?.data?.data?.msg || this.errorMessages.updateError);
                })
                .catch(e => this.handleError(e.response.data.message || this.errorMessages.updateError, e))
                .finally(() => this.haltUpdate());
        },
        getFile(payload) {
            this.file = payload[0];
        },
        getActionButtonLabel() {
            return (this.isLogoSetup()
                    ? 'Update'
                    : '+ Add'
            ) + ' Logo';
        },
        getModalHeading() {
            return (this.isLogoSetup()
                ? 'Update'
                : 'Add'
            ) + ' Logo';
        },
        getModalConfirmationText() {
            return !this.saving
                ? this.isLogoSetup()
                    ? 'Update'
                    : 'Create'
                : 'Saving...'
        },
        isLogoSetup() {
            return this.logoUrl && this.logoUrl.trim().length > 0;
        },
        handleError(errorMessage, caughtException = null) {
            this.showAlert('error', errorMessage);

            if(caughtException) {
                console.error(errorMessage, caughtException);
            }
        },
        fileUploadError(errorMessage) {
            this.handleError(errorMessage)
        },
    },
}
</script>

<style scoped>

</style>
