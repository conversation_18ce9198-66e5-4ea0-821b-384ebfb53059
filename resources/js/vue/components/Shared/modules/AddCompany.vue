<template xmlns="http://www.w3.org/1999/html">
    <alerts-container v-if="alertActive" :text="alertText" :alert-type="alertType" :dark-mode="darkMode"/>
    <Modal
        :small="true"
        :confirm-text="confirmText"
        :close-text="'Cancel'"
        :dark-mode="darkMode"
        @close="handleModalClosure"
        @confirm="addCompany"
    >
        <template v-slot:header>
            <h4 class="text-xl font-medium">Add New Company</h4>
        </template>
        <template v-slot:content>
            <div class="grid grid-cols-1 gap-5">
                <div>
                    <span class="block mb-1 text-sm font-medium">Name*</span>
                    <CustomInput
                        :dark-mode="darkMode"
                        ref="inputElement"
                        placeholder="Enter Name"
                        type="text"
                        v-model="requestedCompanyName"
                    />
                </div>
                <div>
                    <span class="block mb-1 text-sm font-medium">Entity Name*</span>
                    <CustomInput
                        :dark-mode="darkMode"
                        ref="inputElement"
                        placeholder="Enter Entity Name"
                        type="text"
                        v-model="requestedCompanyEntityName"
                    />
                </div>
                <div>
                    <span class="block mb-1 text-sm font-medium">Website</span>
                    <CustomInput
                        :dark-mode="darkMode"
                        ref="inputElement"
                        placeholder="Enter Website"
                        type="text"
                        v-model="requestedCompanyWebsite"
                    />
                </div>
                <span class="block mt-4 mb-1 text-sm font-medium">Company Type</span>
                <div class="pb-2 flex items-center justify-around">
                    <div v-for="[companyType, companyLabel] in Object.entries(companyTypes)">
                        <custom-button
                            :dark-mode="darkMode"
                            :key="companyType"
                            :color="selectedCompanyType === companyLabel ? 'primary' : 'slate'"
                            @click="handleCompanyTypeChange(companyLabel)"
                        >
                            {{ companyLabel }}
                        </custom-button>
                    </div>
                </div>
                <div v-if="selectedCompanyType === companyTypes.multiIndustry">
                    <span class="block mb-1 text-sm font-medium">Industries</span>
                    <multi-select
                        :options="industryOptions"
                        :dark-mode="darkMode"
                        :text-place-holder="'Select Industries'"
                        :show-search-box="false"
                        :selected-ids="requestedCompanyIndustries"
                        @selection-completed="handleIndustrySelectionCompletion"
                        :classes="'w-full'"
                        placement="top"
                    />
                </div>
                <div v-if="selectedCompanyType">
                    <span class="block mb-1 text-sm font-medium">Services</span>
                    <multi-select
                        :options="industryServicesOptions"
                        :dark-mode="darkMode"
                        :text-place-holder="'Select Services'"
                        :show-search-box="false"
                        :selected-ids="requestedCompanyIndustryServices"
                        :classes="'w-full'"
                        placement="top"
                    />
                </div>
            </div>
        </template>
    </Modal>
</template>

<script>
import AlertsContainer from "../components/AlertsContainer.vue";
import MultiSelect from "../components/MultiSelect.vue";
import Modal from "../components/Modal.vue";
import SharedApiService from "../services/api";
import AlertsMixin from "../../../mixins/alerts-mixin";
import Dropdown from "../components/Dropdown.vue";
import DropdownSelector from "../components/DropdownSelector.vue";
import CustomButton from "../components/CustomButton.vue";
import CustomInput from "../components/CustomInput.vue";

export default {
    name: "AddCompany",
    components: {
        CustomInput,
        CustomButton,
        DropdownSelector,
        Dropdown,
        MultiSelect,
        AlertsContainer,
        Modal
    },
    mixins: [AlertsMixin],
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        industries: {
            type: Array,
            default: () => [],
        },
    },
    emits: ['close-add-company-module'],
    data() {
        const companyTypes = {
            solar: 'Solar',
            roofing: 'Roofing',
            multiIndustry: 'Multi-Industry',
        };
        return {
            api: SharedApiService.make(),
            confirmText: 'Add',
            addingCompany: false,
            requestedCompanyName: null,
            requestedCompanyEntityName: null,
            requestedCompanyWebsite: null,
            requestedCompanyIndustries: [],
            requestedCompanyIndustryServices: [],

            allIndustries: [],
            allIndustryServiceOptions: [],
            industryOptions: [],
            industryServicesOptions: [],

            message: {
                error: 'An error occurred, please try again!',
                success: `The company '_COMPANY_' is successfully created!`,
            },

            companyTypes,
            selectedCompanyType: null,
        }
    },
    created() {
        this.fetchIndustryServices();
        if(!this.industries || !this.industries.length) {
            this.fetchIndustries();
        }
        else {
            this.allIndustries = this.industries;
            this.filterIndustriesBySlug(this.industries);
        }
    },
    methods: {
        addCompany() {
            if(this.addingCompany) {
                return;
            }

            this.confirmText = 'Adding...';
            this.addingCompany = true;

            this.api.createNewCompany(this.requestedCompanyName, this.requestedCompanyEntityName, this.requestedCompanyWebsite, this.requestedCompanyIndustries, this.requestedCompanyIndustryServices)
                .then(resp => {
                    if(resp?.data?.data?.status) {
                        this.showAlert('success', this.message.success.replace('_COMPANY_', this.requestedCompanyName));
                        window.location.href = `/companies/${resp.data.data.company_id}`;
                    } else {
                        this.showAlert('error', this.message.error);
                    }
                })
                .catch(e => this.showAlert('error', e?.response?.data?.message || this.message.error))
                .finally(() => {
                    this.addingCompany = false;
                    this.confirmText = 'Add';
                })
        },
        handleModalClosure() {
            this.$emit('close-add-company-module', true);
        },
        async fetchIndustries() {
            await this.api.getOdinIndustries()
                .then(resp => {
                    if(resp?.data?.data?.status === true) {
                        this.allIndustries = resp.data.data.industries ?? [];
                        this.filterIndustriesBySlug(this.allIndustries);
                    }
                })
                .catch(e => console.log(e?.response?.data?.message || this.message.error))
        },
        async fetchIndustryServices() {
            await this.api.allServicesByIndustry().then(resp => {
                if (resp.data?.data?.status) {
                    this.allIndustryServiceOptions = resp.data.data.industry_services;
                }
            }).catch(e => console.log(e?.response?.data?.message || this.message.error));
        },
        filterIndustryServices() {
            this.industryServicesOptions = Object.entries(this.allIndustryServiceOptions).reduce((output, [industryId, services]) => {
                return this.requestedCompanyIndustries.includes(parseInt(industryId))
                    ? [...output, ...services]
                    : output;
            }, []);
        },
        filterIndustriesBySlug(industryArray, removeSlugs = ['solar', 'roofing']) {
            this.industryOptions = industryArray.reduce((output, industry) => {
                return removeSlugs.includes(industry.slug)
                    ? output
                    : [...output, { id: industry.id, name: industry.name }];
            }, []);
        },
        handleIndustrySelectionCompletion() {
            if(this.requestedCompanyIndustries.length > 0) {
                this.filterIndustryServices();
            }
        },
        handleCompanyTypeChange(newType) {
            if (this.selectedCompanyType === newType) return;
            this.selectedCompanyType = newType;
            this.requestedCompanyIndustryServices = [];
            this.requestedCompanyIndustries = [];
            if (this.selectedCompanyType === this.companyTypes.solar) {
                const solarIndustry = this.allIndustries.find(industry => industry.slug === 'solar');
                this.requestedCompanyIndustries = [ solarIndustry.id ];
                this.filterIndustryServices();
            }
            else if (this.selectedCompanyType === this.companyTypes.roofing) {
                const roofingIndustry = this.allIndustries.find(industry => industry.slug === 'roofing');
                this.requestedCompanyIndustries = [ roofingIndustry.id ];
                this.filterIndustryServices();
            }
        }
    },
}
</script>
