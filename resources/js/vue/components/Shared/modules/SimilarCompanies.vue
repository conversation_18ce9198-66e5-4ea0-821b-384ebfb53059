<template>
    <!-- Similar Companies Registered -->
    <div class="border rounded-lg"  :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
        <div class="p-5">
            <div class="flex items-center justify-between pb-4">
                <h5 class="text-sm uppercase text-primary-500 font-bold leading-tight">Similar/Duplicate Companies</h5>
            </div>
            <div class="row-span-3 border rounded-lg"
                 :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
                <div class="pb-6">
                    <div class="grid grid-cols-11 gap-2 pt-2 mb-2 px-5 ml-1">
                        <p class="text-slate-400 font-medium tracking-wide uppercase text-xs">Id</p>
                        <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">Date</p>
                        <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">Status</p>
                        <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-3">Name</p>
                        <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-3">Source</p>
                    </div>
                    <loading-spinner v-if="loading" :dark-mode="darkMode" />
                    <div v-else class="border-t border-l border-r h-full overflow-y-auto"
                         :class="{'bg-light-background border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}">
                        <div class="border-b" :class="{ 'border-light-border': !darkMode, 'border-dark-border': darkMode }" v-if="similarCompanies.length > 0">
                            <div class="grid grid-cols-11 gap-2 items-center px-5 py-2 border-b" v-for="company in similarCompanies"
                                 :class="{ 'text-grey-800 hover:bg-light-module border-light-border': !darkMode, 'text-grey-200 hover:bg-dark-module border-dark-border': darkMode }">
                                <p class="text-xs">
                                    <a :href="'/companies/' + company.id" class="text-primary-500">
                                        {{ company.id }}
                                    </a>
                                </p>
                                <p class="text-xs col-span-2">
                                    {{ company.created_at ? $filters.dateFromTimestamp(company.created_at) : '-' }}
                                </p>
                                <p class="text-xs col-span-2">
                                    {{ company.status }}
                                </p>
                                <p class="text-xs col-span-3">
                                    {{ company.name }}
                                </p>
                                <p class="text-xs col-span-3">
                                    {{ company.source }}
                                </p>
                            </div>
                        </div>
                        <div v-else>
                            <div class="px-5 py-2 text-s border-b"
                                 :class="{'text-grey-800 border-light-border': !darkMode, 'text-grey-200 border-dark-border': darkMode}">
                                No potential duplicate companies found
                            </div>
                        </div>
                    </div>
                    <div v-if="paginationData" class="px-3 pt-5">
                        <Pagination :dark-mode="darkMode" :pagination-data="paginationData" :show-pagination="true" @change-page="handlePaginationEvent"></Pagination>
                    </div>
                    <alert :dark-mode="darkMode" v-if="alertActive" :alert-type="alertType" :text="alertText" />
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import SharedApiService from "../services/consumer_api";
import AlertsMixin from "../../../mixins/alerts-mixin";
import Alert from "../components/Alert.vue";
import LoadingSpinner from "../components/LoadingSpinner.vue";
import Pagination from "../components/Pagination.vue";

export default {
    name: "SimilarCompanies",
    components: {
        Alert,
        LoadingSpinner,
        Pagination,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: Number,
            default: null
        }
    },
    mixins: [
        AlertsMixin
    ],
    data() {
        return {
            api: SharedApiService.make(),
            similarCompanies: [],
            responseKey: 'similar_companies',
            paginationData: null,
            loading: false,
            errorMessage: `Error fetching companies from server.`,
        }
    },
    methods: {
        async getSimilarCompanies() {
            this.loading = true;
            this.api.getSimilarCompanies(this.companyId)
            .then(resp => {
                if (resp.data.data.status) {
                    this.addPaginatedData(resp.data.data[this.responseKey]);
                    return;
                }
                this.showAlert('error', this.errorMessage);})
            .catch(() => this.showAlert('error', this.errorMessage))
            .finally(() => this.loading = false);
        },
        async handlePaginationEvent(newPageUrl) {
            this.loading = true;
            await axios.get(newPageUrl.link)
            .then(resp => {
                if (resp.data.data.status) {
                    this.addPaginatedData(resp.data.data[this.responseKey]);
                    return;
                }
                this.showAlert('error', this.errorMessage);})
            .catch(() => this.showAlert('error', this.errorMessage))
            .finally(() => this.loading = false);
        },
        addPaginatedData({ data, ...paginationData }) {
            if (data && paginationData) {
                this.similarCompanies = data;
                this.paginationData = paginationData;
                return;
            }
            this.showAlert('error', this.errorMessage);
        }
    },
    created() {
        if (this.companyId) this.getSimilarCompanies();
    },
    watch: {
        companyId(newVal, oldVal) {
            if (newVal !== oldVal) this.getSimilarCompanies();
        }
    },
}
</script>

<style scoped>

</style>
