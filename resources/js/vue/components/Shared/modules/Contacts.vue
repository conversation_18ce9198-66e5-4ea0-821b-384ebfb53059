<template>
    <div class="border rounded-lg h-full"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <ContactsHeader :dark-mode="darkMode"
                        :show-add-contact-button="showAddContactButton"
                        @show:modal="showContactAddModal"></ContactsHeader>
        <div class="border-t border-b overflow-y-auto"
             :class="[darkMode ? 'bg-dark-background border-dark-border' : 'bg-light-background  border-light-border', loading ? 'flex flex-col items-center justify-center' : '',tableHeight]">
            <div v-if="!loading">
                <div v-if="Array.isArray(clonedContacts) && clonedContacts.length > 0" class="grid grid-cols-1 gap-x-3 border-b px-5 relative" v-for="contact in clonedContacts"
                     :class="{'text-slate-900 hover:bg-light-module border-light-border': !darkMode, 'text-slate-100 hover:bg-dark-module border-dark-border': darkMode}">
                    <div class="py-4 ">
                        <div class="absolute top-1 right-2 select-none text-xs uppercase"
                             :class="{ 'text-primary-300': darkMode, 'text-primary-700': !darkMode }">
                            {{
                                contact.pinned ? ('Pinned' + (contact.category && contact.category.length > 0 ? ' - ' : '')) : ''
                            }}
                            {{ contact.category ?? '' }}
                        </div>

                        <div class="flex items-center justify-between ">
                            <Contact :dark-mode="darkMode" :expanded-contacts="expandedContacts" :contact="contact"
                                     @toggle:expand="toggleExpandContact" :company-id="companyId"
                                     @update:contact="getContacts"></Contact>

                            <div class="flex items-center">
                                <ContactButtons
                                    :dark-mode="darkMode"
                                    :contact="contact"
                                    :company-name="companyName"
                                    :company-website="companyWebsite"
                                    :has-edit-rights="hasEditRights"
                                    @edit:contact="editContact"
                                    @email="email"
                                    @delete:contact="openDeleteUserModal"/>

                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-3 px-5 pt-4" v-if="expandedContacts.includes(contact.id)">
                            <ContactDetails :dark-mode="darkMode" :contact="contact" @email="email"></ContactDetails>
                            <div class="text-xs col-span-2">
                                <strong>Notes: </strong>
                                {{ contact.notes && contact.notes.trim().length > 0 ? contact.notes : '-' }}
                            </div>
                            <div v-if="contact.user_fields['global']" class="text-xs col-span-2 grid grid-cols-4 gap-2">
                                <div v-for="(field) in contact.user_fields['global'].filter(f => f.value !== '-')">
                                    <strong>{{field.name}}: </strong>
                                    <p v-if="field.type !== 'URL'">
                                        {{ field.value }}
                                    </p>
                                    <a :href="field.value" target="_blank" v-if="field.type === 'URL'" class="text-blue-550">
                                        {{ field.value }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-else id="no-contacts-returned" class="flex justify-center h-full items-center mt-10">
                    <span class="text-sm font-semibold text-gray-400">No Contacts Returned</span>
                </div>
            </div>
            <div v-if="loading">
                <loading-spinner :dark-mode="darkMode"></loading-spinner>
            </div>
        </div>
        <div class="p-3"></div>
        <CreateEditContactModal :company-id="companyId" :dark-mode="darkMode" :editing-contact="editingContact"
                                :show-modal="showModal"
                                @close:modal="closeContactAddModal"
                                @get:contacts="getContacts"
        ></CreateEditContactModal>
        <Modal
            v-if="deleteUserModal"
            :close-text="'Cancel'"
            :confirm-text="deletingUser ? 'Deleting...' : 'Delete'"
            :dark-mode="darkMode"
            :disable-confirm="deletingUser"
            :small="true"
            @close="closeDeleteUserModal"
            @confirm="deleteUser"
        >
            <template v-slot:header>
                <h4 class="text-xl font-medium inline-flex items-center">
                    <svg class="mr-2 fill-current text-red-500" fill="none" height="19" viewBox="0 0 20 19" width="20"
                         xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M10.8849 0.4905C10.5389 -0.1635 9.46294 -0.1635 9.11694 0.4905L0.116935 17.4905C0.0359187 17.6429 -0.00424573 17.8136 0.00035519 17.9861C0.00495611 18.1586 0.0541655 18.327 0.143189 18.4749C0.232212 18.6227 0.358012 18.7449 0.508333 18.8297C0.658653 18.9145 0.828366 18.9588 1.00094 18.9585H19.0009C19.1734 18.9589 19.343 18.9145 19.4932 18.8298C19.6434 18.7451 19.7691 18.6229 19.8581 18.4752C19.947 18.3274 19.9961 18.1591 20.0007 17.9867C20.0052 17.8144 19.965 17.6437 19.8839 17.4915L10.8849 0.4905ZM11.0009 15.9585H9.00094V13.9585H11.0009V15.9585ZM9.00094 11.9585V6.9585H11.0009L11.0019 11.9585H9.00094Z"/>
                    </svg>
                    Delete User
                </h4>
            </template>
            <template v-slot:content>
                Are you sure you want to delete this user?
            </template>
        </Modal>
    </div>
</template>

<script>
import SharedApiService from "../services/api";
import LoadingSpinner from "../components/LoadingSpinner.vue";
import DispatchesGlobalEventsMixin from "../../../mixins/dispatches-global-events-mixin";
import Modal from "../components/Modal.vue";
import Alert from "../components/Alert.vue";
import Contact from "./Contacts/Contact.vue";
import {useContactsStore} from "../../../../stores/contacts.store";
import {validateContacts} from "../../../../modules/contacts/helpers";
import ContactButtons from "./Contacts/ContactButtons.vue";
import ContactDetails from "./Contacts/ContactDetails.vue";
import CreateEditContactModal from "./Contacts/CreateEditContactModal.vue";
import ContactsHeader from "./Contacts/Header.vue";
import {mapActions, mapState, mapWritableState} from "pinia";
import AlertsMixin from "../../../mixins/alerts-mixin";

export default {
    name: "CompanyContacts",
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: Number,
            default: null
        },
        tableHeight: {
            type: String,
            default: 'h-100'
        },
        showAddContactButton: {
            type: Boolean,
            default: false,
        },
        hasEditRights: {
            type: Boolean,
            default: false,
        },
        getContactsOnMounted: {
            type: Boolean,
            default: true
        },
        configurableFields: {
            type: Array,
            default: null,
        },
        companyName: {
            type: String,
            default: null
        },
        companyWebsite: {
            type: String,
            default: null
        },
    },
    components: {
        Contact,
        ContactsHeader, ContactButtons, ContactDetails, CreateEditContactModal, Modal, LoadingSpinner, Alert
    },
    mixins: [DispatchesGlobalEventsMixin, AlertsMixin],
    emits: ['email', 'flash-alert'],
    data() {
        return {
            api: SharedApiService.make(),
            expandedContacts: [],
            loading: false,
            editingContact: false,
            currentlyEditing: [],
            showModal: false,
            saving: false,
            saveError: null,
            deleteUserModal: false,
            deletingUser: false,
            errorMessages: {
                pinAction: 'An unknown error occurred while pinning an Action.'
            },
        }
    },
    mounted() {
        if (this.getContactsOnMounted) {
            this.getContacts();
        }
    },
    watch: {
        companyId(newVal, oldVal) {
            if (newVal !== oldVal) {
                this.getContacts();
            }
        },
        configurableFields(newVal) {
            if (newVal) {
                try {
                    this.contact.user_fields.global = newVal
                } catch (err) {
                    console.error(err)
                }
            }
        }
    },
    computed: {
        confirmText: function () {
            if (this.saving) return 'Saving...';

            return this.editingContact ? "Save" : 'Create';
        },
        ...mapWritableState(useContactsStore, {
            contacts: 'contacts',
            contact: 'contact'
        }),
        ...mapState(useContactsStore, {
            clonedContacts: 'clonedContacts',
        })
    },
    methods: {
        ...mapActions(useContactsStore, ['applyFilterToContacts']),
        editContact(contact) {
            this.showModal = true;
            this.editingContact = true;

            this.contact = contact;
        },
        /**
         * @param id
         */
        toggleExpandContact(id) {
            if (this.expandedContacts.includes(id))
                this.expandedContacts.splice(this.expandedContacts.indexOf(id), 1);
            else
                this.expandedContacts.push(id);
        },
        getContacts() {
            this.loading = true;
            this.api.getCompanyContacts(this.companyId).then(resp => {
                const contacts = resp?.data?.data?.contacts ?? [];

                try {
                    validateContacts(contacts);
                } catch (e) {
                    console.warn(e);
                }

                this.contacts = contacts;
                this.applyFilterToContacts();
            }).catch(err => {
                console.error(err);

                this.contacts = [];
                this.applyFilterToContacts();
            }).finally(() => this.loading = false);
        },
        showContactAddModal() {
            this.showModal = true;
            this.contact = {
                first_name: null,
                last_name: null,
                title: null,
                email: null,
                office_phone: null,
                cell_phone: null,
                status: 1,
                is_contact: 1,
                user_fields: { global: this.configurableFields }
            }
        },
        closeContactAddModal() {
            this.showModal = false;
            this.editingContact = false;
            this.saveError = null;
            this.contact = {
                first_name: undefined,
                last_name: undefined,
                title: undefined,
                email: undefined,
                office_phone: undefined,
                cell_phone: undefined,
                status: true,
                is_contact: true,
                user_fields: { global: this.configurableFields }
            }
        },
        email(data) {
            this.$emit('email', data);
        },
        deleteUser() {
            this.deletingUser = true;
            this.api.deleteCompanyUser(this.companyId, this.deleteUserId)
                .then(() => {
                    this.getContacts();
                    this.closeDeleteUserModal();
                })
                .catch(e => {
                    this.error = e.response.data.message;
                    this.$emit('flash-alert','error', e.response.data.message + '. Remove the user from all campaign delivery contacts and retry.');
                })
                .finally(() => this.deletingUser = false);
        },
        openDeleteUserModal(user) {
            this.deleteUserModal = true;
            this.deleteUserId = user.id;
        },
        closeDeleteUserModal() {
            this.deleteUserModal = false;
            this.deleteUserId = null;
        },
    },
}
</script>

<style scoped>

</style>
