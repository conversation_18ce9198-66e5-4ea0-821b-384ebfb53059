<template>
    <div class="col-span-3 border rounded-lg p-5"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <alerts-container v-if="alertActive" :text="alertText" :alert-type="alertType" :dark-mode="darkMode"/>
        <div class="p-5 flex items-center justify-between">
            <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">
                Contracts for {{ companyName }}
            </h5>
            <div v-if="permissionStore.hasPermission(PERMISSIONS.PERMISSION_CONTRACT_MANAGEMENT_SEND)">
                Bypass Contract Signing
                <toggle-switch :dark-mode="darkMode" v-model="companyContractBypassFlag" @change="updateCompanyContractBypassFlag"/>
            </div>
        </div>
        <div class="px-5 pb-5">
            <div class="flex items-center space-x-4">
                <Dropdown class="w-48 mr-2" v-model="selectedFilterContractOption" :dark-mode="darkMode" :options="filterContractsOptions"
                          :selected="selectedFilterContractOption" />
                <CustomButton type="submit" :dark-mode="darkMode"
                              @click="getCompanyContracts()"
                >
                    Search
                </CustomButton>
                <CustomButton :dark-mode="darkMode" color="slate-inverse" type="reset"
                              @click="resetSearch"
                >
                    Reset
                </CustomButton>
                <CustomButton
                    v-if="permissionStore.hasPermission(PERMISSIONS.PERMISSION_CONTRACT_MANAGEMENT_SEND)"
                    @click="showSendSignatureRequestModal = true"
                    :dark-mode="darkMode"
                    icon
                    color="slate-inverse"
                    class="text-nowrap"
                >
                    <template v-slot:icon>
                        <svg class="fill-current" :class="darkMode ? 'text-slate-200' : 'text-slate-800'" width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M8.25 0L10.3125 1.95117L8.74019 3.43926L6.67769 1.48809L8.25 0ZM0 7.79688V9.74805H2.0625L7.76806 4.35827L5.70556 2.40709L0 7.79688ZM0 11.6992H11V13H0V11.6992Z"/>
                        </svg>
                    </template>
                    Send Signature Request
                </CustomButton>
                <CustomButton
                    v-if="permissionStore.hasPermission(PERMISSIONS.PERMISSION_CONTRACT_MANAGEMENT_UPLOAD)"
                    @click="showUploadContactModal = true"
                    :dark-mode="darkMode"
                    icon
                    color="slate-inverse"
                    class="text-nowrap"
                >
                    <template v-slot:icon>
                        <svg class="fill-current" :class="darkMode ? 'text-slate-200' : 'text-slate-800'" width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M8.25 0L10.3125 1.95117L8.74019 3.43926L6.67769 1.48809L8.25 0ZM0 7.79688V9.74805H2.0625L7.76806 4.35827L5.70556 2.40709L0 7.79688ZM0 11.6992H11V13H0V11.6992Z"/>
                        </svg>
                    </template>
                    Upload Contract
                </CustomButton>
            </div>
        </div>

        <div class="border rounded-lg"
             :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']"
        >
            <div class="grid grid-cols-6 gap-x-3 mb-2 px-5 mt-4">
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs text-center">Contract</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs text-center">Signing Status</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs text-center">Signer name</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs text-center">Date signed</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs text-center">Download</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs text-center">Actions</p>
            </div>
            <div v-if="loading" class="my-6 py-6">
                <LoadingSpinner></LoadingSpinner>
            </div>
            <div v-else>
                <div class="border-y overflow-y-auto h-100 divide-y" :class="[darkMode ? 'border-dark-border divide-dark-border bg-dark-background' : 'border-light-border divide-slate-200 bg-light-background']">
                    <div>
                        <div v-if="contracts.length === 0">
                            <p class="text-sm truncate text-center mt-8">No contracts available.</p>
                        </div>
                        <div v-else>
                            <div v-for="companyContract in filteredContracts"
                                 :key="companyContract.id"
                                 :class="{'text-slate-900 hover:bg-light-module border-light-border': !darkMode, 'text-slate-100 hover:bg-dark-module border-dark-border': darkMode}"
                                 class="grid grid-cols-6 gap-x-3 border-b px-5 py-3 items-center">
                                <div v-if="companyContract.contract" class="flex flex-col items-center gap-1">
                                    <p class="inline-flex flex-wrap items-center text-sm">{{ companyContract.contract.description }}</p>
                                    <p class="inline-flex flex-wrap items-center text-sm">{{ companyContract.contract.origin_name }} - {{ companyContract.contract.key_name }}</p>
                                    <div v-if="companyContract.contract.status" class="inline-flex flex-wrap items-center text-sm" :class="[darkMode ? 'text-emerald-500' : 'text-emerald-700']">
                                        <svg class="mr-1 fill-current" width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.25 1.75C5.72501 1.75 4.26247 2.3558 3.18414 3.43414C2.1058 4.51247 1.5 5.97501 1.5 7.5C1.5 8.2551 1.64873 9.00281 1.93769 9.70043C2.22666 10.3981 2.6502 11.0319 3.18414 11.5659C3.71807 12.0998 4.35195 12.5233 5.04957 12.8123C5.74719 13.1013 6.4949 13.25 7.25 13.25C8.0051 13.25 8.75281 13.1013 9.45043 12.8123C10.1481 12.5233 10.7819 12.0998 11.3159 11.5659C11.8498 11.0319 12.2733 10.3981 12.5623 9.70043C12.8513 9.00281 13 8.2551 13 7.5C13 5.97501 12.3942 4.51247 11.3159 3.43414C10.2375 2.3558 8.77499 1.75 7.25 1.75ZM2.12348 2.37348C3.48311 1.01384 5.32718 0.25 7.25 0.25C9.17282 0.25 11.0169 1.01384 12.3765 2.37348C13.7362 3.73311 14.5 5.57718 14.5 7.5C14.5 8.45208 14.3125 9.39484 13.9481 10.2745C13.5838 11.1541 13.0497 11.9533 12.3765 12.6265C11.7033 13.2997 10.9041 13.8338 10.0245 14.1981C9.14484 14.5625 8.20208 14.75 7.25 14.75C6.29792 14.75 5.35516 14.5625 4.47554 14.1981C3.59593 13.8338 2.7967 13.2997 2.12348 12.6265C1.45025 11.9533 0.91622 11.1541 0.551873 10.2745C0.187527 9.39484 0 8.45208 0 7.5C0 5.57718 0.763837 3.73311 2.12348 2.37348ZM9.947 5.52523C10.2399 5.81812 10.2399 6.29299 9.947 6.58589L7.05811 9.47477C6.76521 9.76767 6.29034 9.76767 5.99745 9.47477L4.553 8.03033C4.26011 7.73744 4.26011 7.26256 4.553 6.96967C4.8459 6.67678 5.32077 6.67678 5.61366 6.96967L6.52778 7.88378L8.88634 5.52523C9.17923 5.23233 9.6541 5.23233 9.947 5.52523Z"/></svg>
                                        Active
                                    </div>
                                    <div v-else class="inline-flex flex-wrap items-center text-sm" :class="[darkMode ? 'text-red-500' : 'text-red-700']">
                                        <svg class="mr-1 fill-current" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7 1.75C5.60761 1.75 4.27226 2.30312 3.28769 3.28769C2.30312 4.27226 1.75 5.60761 1.75 7C1.75 7.68944 1.8858 8.37213 2.14963 9.00909C2.41347 9.64605 2.80018 10.2248 3.28769 10.7123C3.7752 11.1998 4.35395 11.5865 4.99091 11.8504C5.62787 12.1142 6.31056 12.25 7 12.25C7.68944 12.25 8.37213 12.1142 9.00909 11.8504C9.64605 11.5865 10.2248 11.1998 10.7123 10.7123C11.1998 10.2248 11.5865 9.64605 11.8504 9.00909C12.1142 8.37213 12.25 7.68944 12.25 7C12.25 5.60761 11.6969 4.27226 10.7123 3.28769C9.72774 2.30312 8.39239 1.75 7 1.75ZM2.22703 2.22703C3.4929 0.961159 5.20979 0.25 7 0.25C8.79021 0.25 10.5071 0.961159 11.773 2.22703C13.0388 3.4929 13.75 5.20979 13.75 7C13.75 7.88642 13.5754 8.76417 13.2362 9.58311C12.897 10.4021 12.3998 11.1462 11.773 11.773C11.1462 12.3998 10.4021 12.897 9.58311 13.2362C8.76417 13.5754 7.88642 13.75 7 13.75C6.11358 13.75 5.23584 13.5754 4.41689 13.2362C3.59794 12.897 2.85382 12.3998 2.22703 11.773C1.60023 11.1462 1.10303 10.4021 0.763813 9.58311C0.424594 8.76417 0.25 7.88642 0.25 7C0.25 5.20979 0.961159 3.4929 2.22703 2.22703ZM5.13634 5.13634C5.42923 4.84344 5.9041 4.84344 6.197 5.13634L7 5.93934L7.803 5.13634C8.0959 4.84344 8.57077 4.84344 8.86366 5.13634C9.15656 5.42923 9.15656 5.9041 8.86366 6.197L8.06066 7L8.86366 7.803C9.15656 8.0959 9.15656 8.57077 8.86366 8.86366C8.57077 9.15656 8.0959 9.15656 7.803 8.86366L7 8.06066L6.197 8.86366C5.9041 9.15656 5.42923 9.15656 5.13634 8.86366C4.84344 8.57077 4.84344 8.0959 5.13634 7.803L5.93934 7L5.13634 6.197C4.84344 5.9041 4.84344 5.42923 5.13634 5.13634Z"/></svg>
                                        Not Active
                                    </div>
                                </div>
                                <div v-else>
                                    {{ companyContract.contract_type }}
                                </div>
                                <div class="text-sm truncate text-center">
                                    <div v-if="companyContract.agreed_at !== null"
                                         :class="[darkMode ? 'border-green-400 text-green-600 bg-transparent' : 'border-transparent bg-green-100 text-green-600']"
                                         class="px-5 inline-flex items-center py-1 text-sm font-semibold rounded-full border space-x-2">
                                        <svg fill="none" height="10" viewBox="0 0 12 10" width="12"
                                             xmlns="http://www.w3.org/2000/svg">
                                            <g id="priority_high_24px">
                                                <path
                                                    d="M4.77585 6.10601L2.34865 3.5623L0.85791 5.13175L4.77796 9.23828L11.8579 1.80552L10.3651 0.238281L4.77585 6.10601Z"
                                                    fill="#00AE07"/>
                                            </g>
                                        </svg>
                                        <p class="text-sm truncate text-center">Signed</p>
                                    </div>
                                    <div v-else
                                         :class="[darkMode ? 'border-yellow-400 text-yellow-600 bg-transparent' : 'border-transparent bg-yellow-100 text-yellow-600']"
                                         class="px-5 inline-flex items-center py-1 text-sm font-semibold rounded-full border space-x-2">
                                        <svg fill="none" height="17" viewBox="0 0 17 17" width="17"
                                             xmlns="http://www.w3.org/2000/svg">
                                            <g id="priority_high_24px">
                                                <path id="icon/notification/priority_high_24px" clip-rule="evenodd"
                                                      d="M7.11816 3.73128C7.11816 2.99795 7.71816 2.39795 8.4515 2.39795C9.18483 2.39795 9.78483 2.99795 9.78483 3.73128V9.06462C9.78483 9.79795 9.18483 10.3979 8.4515 10.3979C7.71816 10.3979 7.11816 9.79795 7.11816 9.06462V3.73128ZM7.11816 13.0646C7.11816 12.3282 7.71512 11.7313 8.4515 11.7313C9.18788 11.7313 9.78483 12.3282 9.78483 13.0646C9.78483 13.801 9.18788 14.3979 8.4515 14.3979C7.71512 14.3979 7.11816 13.801 7.11816 13.0646Z"
                                                      fill="#D97706"
                                                      fill-rule="evenodd"/>
                                            </g>
                                        </svg>
                                        <p class="text-sm truncate text-center text-yellow-600">Awaiting signing</p>
                                    </div>
                                </div>
                                <p class="text-sm truncate text-center">
                                    {{ companyContract.company_user_name }}</p>
                                <p class="text-sm truncate text-center">{{ companyContract.agreed_at ? $filters.dateFromTimestamp(companyContract.agreed_at, 'usWithTime') : 'N/A' }}</p>
                                <div class="text-sm truncate text-center flex justify-center">
                                    <div v-if="companyContract.agreed_at !== null" class="flex items-center">
                                        <a class="cursor-pointer flex items-center space-x-2" @click="downloadContract(companyContract.file_url)">
                                            <svg class="ml-4" fill="none" height="20" viewBox="0 0 20 20" width="20"
                                                 xmlns="http://www.w3.org/2000/svg">
                                                <g id="download_24px">
                                                    <path id="icon/file/download_24px" clip-rule="evenodd" d="M12.4998 7.91663H13.8248C14.5665 7.91663 14.9332 8.81663 14.4082 9.34163L10.5832 13.1666C10.2582 13.4916 9.73317 13.4916 9.40817 13.1666L5.58317 9.34163C5.05817 8.81663 5.43317 7.91663 6.17484 7.91663H7.49984V3.74996C7.49984 3.29163 7.87484 2.91663 8.33317 2.91663H11.6665C12.1248 2.91663 12.4998 3.29163 12.4998 3.74996V7.91663ZM4.99984 17.0833C4.5415 17.0833 4.1665 16.7083 4.1665 16.25C4.1665 15.7916 4.5415 15.4166 4.99984 15.4166H14.9998C15.4582 15.4166 15.8332 15.7916 15.8332 16.25C15.8332 16.7083 15.4582 17.0833 14.9998 17.0833H4.99984Z"
                                                          fill="#0081FF"
                                                          fill-rule="evenodd"/>
                                                </g>
                                            </svg>
                                            <p class="text-sm truncate text-center">Download</p>
                                        </a>
                                    </div>
                                    <div v-else>
                                        N/A
                                    </div>
                                </div>
                                <div class="flex flex-col justify-start gap-1">
                                    <ActionsHandle :dark-mode="darkMode" :custom-actions="[{event: 'log', name: 'View Activity Log'}]" :noCustomAction="false"
                                                   no-delete-button no-edit-button
                                                   @log="showContractActivityLogModal(companyContract.id)"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <Pagination :dark-mode="darkMode" :pagination-data="paginationData.meta ?? {}" :show-pagination="true"
                            @change-page="handlePaginationEvent"
                />
            </div>
        </div>

        <!-- Activity Log -->
        <Modal v-if="showModal" :dark-mode="darkMode" :hideConfirm="true" :small=true @close="showModal = false">
            <template v-slot:header>
                <p class="font-semibold">Activity Log</p>
            </template>
            <template v-slot:content class="flex items-center justify-center">
                <div v-if="loading" class="h-80 flex items-center justify-center">
                    <loading-spinner/>
                </div>
                <div v-else>
                    <template v-if="activityLogs.length > 0">
                        <table class="min-w-full divide-y" :class="[!darkMode ? 'divide-gray-300' : 'divide-dark-175']">
                            <thead>
                                <tr class="divide-x" :class="[!darkMode ? 'divide-gray-200' : 'divide-dark-175']" >
                                    <th scope="col"
                                        :class="[!darkMode ? 'border-gray-300 bg-light-module text-gray-900' : 'border-dark-border bg-dark-module text-grey-100']"
                                        class="px-6 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">
                                        User
                                    </th>
                                    <th scope="col"
                                        :class="[!darkMode ? 'border-gray-300 bg-light-module text-gray-900' : 'border-dark-border bg-dark-module text-grey-100']"
                                        class="px-6 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">
                                        Date
                                    </th>
                                    <th scope="col"
                                        :class="[!darkMode ? 'border-gray-300 bg-light-module text-gray-900' : 'border-dark-border bg-dark-module text-grey-100']"
                                        class="px-6 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">
                                        Action
                                    </th>
                                    <th scope="col"
                                        :class="[!darkMode ? 'border-gray-300 bg-light-module text-gray-900' : 'border-dark-border bg-dark-module text-grey-100']"
                                        class="px-6 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">
                                        Notes
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="divide-y" :class="[!darkMode ? 'divide-gray-200 bg-light-background' : 'divide-dark-175 bg-dark-background']">
                                <tr v-for="(item, index) in activityLogs" :key="index">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm" :class="[!darkMode ? 'text-gray-900' : 'text-grey-100']">
                                        {{ item.actor }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm" :class="[!darkMode ? 'text-gray-900' : 'text-grey-100']">
                                        {{ $filters.dateFromTimestamp(item.updated_at) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm" :class="[!darkMode ? 'text-gray-900' : 'text-grey-100']">
                                        {{ item.audit_event }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm" :class="[!darkMode ? 'text-gray-900' : 'text-grey-100']">
                                        {{item.description }}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </template>
                    <template v-else>
                        <p class="text-sm mt-8 text-center">No activity logs</p>
                    </template>
                </div>
            </template>
        </Modal>
        <Modal v-if="showSendSignatureRequestModal" :dark-mode="darkMode" :small="true" :confirm-text="'Send'" @confirm="sendSignatureRequest" @close="closeModal">
            <template v-slot:header>
                <p class="font-semibold">Send Bulk Signature Requests</p>
            </template>
            <template v-slot:content>
                <div class="grid grid-cols-1 gap-8">
                    <div>
                        <p class="mb-2 uppercase font-semibold text-xs" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Which User</p>
                        <Dropdown
                            v-model="selectedUserId"
                            :dark-mode="darkMode"
                            :options="userOptions"
                        ></Dropdown>
                    </div>
                    <div>
                        <Dropdown
                            v-model="selectedContractId"
                            :dark-mode="darkMode"
                            :options="contractOptions"
                            placeholder="Select Active Contract"
                        ></Dropdown>
                    </div>
                </div>
            </template>
        </Modal>
        <Modal v-if="showUploadContactModal" :dark-mode="darkMode" :small="true" :confirm-text="'Send'" @confirm="uploadContract" @close="closeModal">
            <template v-slot:header>
                <p class="font-semibold">Upload a Signed Contract</p>
            </template>
            <template v-slot:content>
                <div class="grid grid-cols-1 gap-8">
                    <div>
                        <p class="mb-2 uppercase font-semibold text-xs" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Which User</p>
                        <Dropdown
                            v-model="selectedUserId"
                            :dark-mode="darkMode"
                            :options="userOptions"
                        ></Dropdown>
                    </div>
                    <div>
                        <file-upload
                            :dark-mode="darkMode"
                            :accept="'.pdf'"
                            @file-uploaded="getFile"
                            @file-upload-error="fileUploadError"
                        />
                    </div>
                </div>
            </template>
        </Modal>
    </div>
</template>
<script>
import LoadingSpinner from "../components/LoadingSpinner.vue";
import SharedApiService from "../services/api";
import Dropdown from "../components/Dropdown.vue";
import Pagination from "../components/Pagination.vue";
import Modal from "../components/Modal.vue";
import contractApiService from "../../ContractManagement/Services/api";
import ActionsHandle from "../components/ActionsHandle.vue";
import CustomButton from "../components/CustomButton.vue";
import { PERMISSIONS, useRolesPermissions } from "../../../../stores/roles-permissions.store.js"
import AlertsContainer from "../components/AlertsContainer.vue";
import AlertsMixin from "../../../mixins/alerts-mixin";
import ToggleSwitch from "../components/ToggleSwitch.vue";
import FileUpload from "../../Inputs/FileUpload.vue";
import {REQUEST} from "../../../../constants/APIRequestKeys.js";

export default {
    name: "CompanyContracts",
    components: {
        FileUpload,
        ToggleSwitch, AlertsContainer, CustomButton, ActionsHandle, Modal, Pagination, Dropdown, LoadingSpinner},
    mixins: [AlertsMixin],
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: Number,
            default: null,
        },
        companyName: {
            type: String,
            default: null
        }
    },
    data() {
        return {
            api: SharedApiService.make(),
            loading: false,
            contracts: [],
            selectedFilterContractOption: 0,
            filterContractsOptions: [
                { id: 0, name: 'All' },
                { id: 1, name: 'Awaiting signing' },
                { id: 2, name: 'Signed'}
            ],
            paginationData: null,
            showModal: false,
            activityLogs: [],
            searchParams: {},
            contractOptions: [],
            userOptions: [],
            showSendSignatureRequestModal: false,
            selectedContractId: null,
            selectedUserId: null,
            companyContractBypassFlag: false,
            permissionStore: useRolesPermissions(),
            PERMISSIONS: PERMISSIONS,
            showUploadContactModal: false,
            file: null,
        }
    },
    created() {
        if (this.companyId) this.getCompanyContracts();
        this.getActiveContracts()
        this.getActiveUsers()
    },
    computed: {
        filteredContracts: function () {
            switch (this.selectedFilterContractOption) {
                case 1:
                    return this.contracts.filter(contract => contract.agreed_at === null);
                case 2:
                    return this.contracts.filter(contract => contract.agreed_at !== null);
                default:
                    return {...this.contracts};
            }
        }
    },
    methods: {
        getCompanyContracts() {
            this.loading = true;
            this.api.getCompanyContracts(this.companyId).then(resp => {
                this.companyContractBypassFlag = resp.data.data.company_contract_bypass;
                const paginationData = resp.data.data.company_contracts;
                this.contracts = paginationData.data;
                this.paginationData = paginationData;
            }).catch(e => this.showAlert('error', e?.response?.data?.message || this.message.error)).finally(() => this.loading = false);
        },
        downloadContract(url) {
            if(url)
                window.open(url, '_blank')
        },
        async handlePaginationEvent(newPageEvent) {
            this.loading = true;
            this.api.getCompanyContracts(this.companyId, {page: newPageEvent.newPage}).then(resp => {
                const paginationData = resp.data.data.company_contracts;
                this.contracts = paginationData.data;
                this.paginationData = paginationData;
            }).catch(e => this.showAlert('error', e?.response?.data?.message || this.message.error)).finally(() => this.loading = false);
        },
        showContractActivityLogModal(companyContractId) {
            this.showModal = true;

            this.loading = true;
            this.api.getCompanyUserContractAuditLogs(this.companyId, companyContractId)
                .then(response => {
                    this.activityLogs = response.data.data.audit_logs;
                    this.loading = false;
                }).catch(e => this.showAlert('error', e?.response?.data?.message || this.message.error))
        },
        sendSignatureRequest() {
            contractApiService.sendContractSignatureRequest(this.selectedContractId, this.selectedUserId)
                .then(resp => {
                    if(resp.status)
                        this.showAlert('success', 'Signature Request Sent')
                })
                .catch(e => this.showAlert('error', e?.response?.data?.message || this.message.error))
                .finally(() => {
                    this.closeModal()
                    this.getCompanyContracts()
                });
        },
        resetSearch() {
            this.searchParams = {};
            this.selectedFilterContractOption = 0;
            this.getCompanyContracts();
        },
        getActiveContracts() {
            contractApiService.getContracts({'status': 1}).then(resp => {
                this.contractOptions = resp.data.data.contracts.map((key) => {
                    return {
                        id: key.id,
                        //we want the type of contract and what domains is serves
                        name: key.description + ' ( ' + key.website?.name + ': ' + key.contract_key?.name + ' )'
                    };
                });

            }).catch(e => this.showAlert('error', e?.response?.data?.message || this.message.error))
        },
        getActiveUsers() {
            this.api.getCompanyUsers(this.companyId).then(resp => {
                this.userOptions = resp.data.data.users.filter((key) => key.status = 1).map((key) => {
                    return {
                        id: key.id,
                        name: key.first_name + ' ' + key.last_name
                    };
                });
            }).catch(e => this.showAlert('error', e?.response?.data?.message || this.message.error))
        },
        closeModal() {
            this.selectedUserId = null;
            this.selectedContractId = null;
            this.showSendSignatureRequestModal = false;
            this.showUploadContactModal = false;
        },
        async updateCompanyContractBypassFlag() {
            await this.api.companyContractBypassFlag(this.companyId, this.companyContractBypassFlag);
        },
        uploadContract() {
            const formData = new FormData();
            formData.append('file', this.file);
            contractApiService.uploadCompanyContract(this.selectedUserId, formData)
                .then(resp => {this.showAlert('success', 'Uploaded');})
                .catch(e => this.showAlert('error', e?.response?.data?.message || this.message.error))
                .finally(() => {
                    this.closeModal()
                    this.getCompanyContracts()
                });
        },
        getFile(payload) {
            this.file = payload[0];
        },
        handleError(errorMessage, caughtException = null) {
            this.showAlert('error', errorMessage);

            if(caughtException) {
                console.error(errorMessage, caughtException);
            }
        },
        fileUploadError(errorMessage) {
            this.handleError(errorMessage)
        },
    }
}
</script>
