<template>
    <modal
        :small="true"
        @confirm="saveContact"
        :close-text="'Cancel'"
        :confirm-text="confirmText"
        :dark-mode="darkMode"
        v-if="showModal"
        @close="closeContactAddModal"
        :disable-confirm="disableConfirm"
    >
        <template v-slot:header>
            <h3>{{ editingContact ? "Edit" : "Add" }} Contact</h3>
        </template>
        <template v-slot:content>
            <div v-if="saveError !== null" class="mb-6">
                <alert :dark-mode="darkMode" :text="saveError" :alert-type="'error'"></alert>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <div class="flex flex-col">
                    <div class="uppercase font-semibold text-xs mb-2"
                       :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                        Status
                    </div>
                    <div class="flex flex-grow">
                        <div class="flex mr-2 items-center text-center text-xs font-semibold uppercase">
                            <input
                                class="mr-2 checked:bg-primary-500 focus:bg-primary-500 focus:ring-0 focus:outline-none"
                                :class="[darkMode ? 'bg-dark-module' : 'bg-light-module']"
                                type="radio"
                                v-model="contact.status"
                                :value="1">
                            <p>Active</p>
                        </div>
                        <div class="flex mr-2 items-center text-center text-xs font-semibold uppercase">
                            <input
                                class="mr-2 checked:bg-primary-500 focus:bg-primary-500 focus:ring-0 focus:outline-none"
                                :class="[darkMode ? 'bg-dark-module' : 'bg-light-module']"
                                type="radio"
                                v-model="contact.status"
                                :value="0">
                            <p>Inactive</p>
                        </div>
                    </div>
                </div>
                <div>
                    <p class="uppercase font-semibold text-xs mb-2"
                       :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                        User Type
                    </p>
                    <Dropdown
                        disabled
                        v-model="contact.is_contact"
                        :dark-mode="darkMode"
                        :options="isContactOptions"
                        :placeholder="'Contact'"
                    />
                </div>
                <div>
                    <p class="uppercase font-semibold text-xs mb-2"
                       :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                        First Name
                    </p>
                    <input
                        class="w-full border rounded px-3 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                        placeholder="First Name" v-model="contact.first_name"
                        :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}">
                </div>
                <div>
                    <p class="uppercase font-semibold text-xs mb-2"
                       :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                        Last Name
                    </p>
                    <input
                        class="w-full border rounded px-3 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                        placeholder="Last Name" v-model="contact.last_name"
                        :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}">
                </div>
                <div>
                    <p class="uppercase font-semibold text-xs mb-2"
                       :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                        Title
                    </p>
                    <input
                        class="w-full border rounded px-3 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                        placeholder="Title" v-model="contact.title"
                        :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}">
                </div>
                <div>
                    <p class="uppercase font-semibold text-xs mb-2"
                       :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                        Email
                    </p>
                    <input
                        class="w-full border rounded px-3 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                        placeholder="Email" v-model="contact.email"
                        :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}">
                </div>
                <div>
                    <p class="uppercase font-semibold text-xs mb-2"
                       :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                        Office Phone
                    </p>
                    <input
                        class="w-full border rounded px-3 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                        placeholder="Office Phone" v-model="contact.office_phone"
                        :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}">
                </div>
                <div>
                    <p class="uppercase font-semibold text-xs mb-2"
                       :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                        Mobile
                    </p>
                    <input
                        class="w-full border rounded px-3 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                        placeholder="Mobile" v-model="contact.cell_phone"
                        :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}">
                </div>
                <div class="col-span-2">
                    <label
                        :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="block mb-1">
                        Notes
                    </label>
                    <textarea
                        v-model="contact.notes"
                        :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}"
                        class="min-h-88 w-full border rounded pl-4 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                        placeholder="Enter notes..."
                        type="text"/>
                </div>
                <div class="flex col-span-2">
                    <div class="flex gap-2 items-center text-center mr-8">
                        <toggle-switch
                            v-model="contact.is_decision_maker"
                            :dark-mode="darkMode" />
                        <p class="uppercase font-semibold text-xs"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Decision Maker
                        </p>
                    </div>
                    <div class="flex gap-2">
                        <toggle-switch
                            v-model="contact.can_receive_promotions"
                            :dark-mode="darkMode" />
                        <div>
                            <p class="uppercase font-semibold text-xs mt-4"
                               :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                                Can Receive Promotions
                            </p>
                            <span style="font-size: 0.65rem;" class="block"
                                  :class="[darkMode ? 'text-slate-400' : 'text-slate-500']">Default is on. Check with this contact before enabling.</span>
                        </div>
                    </div>
                    <div class="flex gap-2 items-center text-center mr-8">
                        <toggle-switch
                            v-model="contact.dnc_contact"
                            :dark-mode="darkMode"/>
                        <p class="uppercase font-semibold text-xs"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            DNC Contact
                        </p>
                    </div>
                </div>
                <div v-if="contact.user_fields.length > 0" class="col-span-2">
                    <p class="uppercase font-semibold text-xs mb-2"
                       :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                        Configurable Fields
                    </p>
                </div>
                <div v-for="(field, id) in contact.user_fields['global']">
                    <p class="uppercase font-semibold text-xs mb-2"
                       :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                        {{ field.name }}
                    </p>

                    <custom-input
                        v-if="field.type !== 'Boolean'"
                        v-model="contact.user_fields['global'][id].value"
                        :dark-mode="darkMode"
                        :type="field.type"
                    />
                    <toggle-switch
                        v-if="field.type ==='Boolean'"
                        v-model="contact.user_fields['global'][id].value"
                        :dark-mode="darkMode" />
                </div>
            </div>
        </template>
    </modal>
</template>

<script>
import SharedApiService from "../../services/api";
import Modal from "../../components/Modal.vue";
import Alert from "../../components/Alert.vue";
import {mapWritableState} from "pinia";
import {useContactsStore} from "../../../../../stores/contacts.store";
import ToggleSwitch from "../../components/ToggleSwitch.vue";
import Dropdown from "../../components/Dropdown.vue";
import CustomInput from "../../components/CustomInput.vue";
import Inputs from "../../../Reports/HistoricalAvailableBudgetsReport/components/Inputs.vue";

export default {
    name: "CreateEditContactModal",
    components: {ToggleSwitch, Inputs, CustomInput, Dropdown, Alert, Modal},
    emits: ["close:modal", "get:contacts"],
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        editingContact: {
            type: Boolean,
            default: false
        },
        showModal: {
            type: Boolean,
            required: true
        },
        companyId: {
            type: Number,
            required: true
        }
    },
    data() {
        return {
            saving: false,
            saveError: null,
            api: SharedApiService.make(),
            isContactOptions: [ { id: 1, name: 'Contact' }, { id: 0, name: 'User' } ],
        }
    },
    methods: {
        saveContact() {
            if (this.saving) return;

            this.saving = true;
            this.saveError = null;

            const method = this.editingContact ? this.api.updateCompanyContact.bind(this.api) : this.api.createCompanyContact.bind(this.api);

            method(this.companyId, this.contact)
                .then(() => {
                    this.$emit('get:contacts');
                    this.closeContactAddModal();
                })
                .catch(e => this.saveError = e.response.data.message + '. Remove the user from all campaign delivery contacts and retry.')
                .finally(() => this.saving = false);
        },
        closeContactAddModal() {
            this.$emit('close:modal')
        }
    },
    computed: {
        disableConfirm: function () {
            return this.saving || (!this.contact.first_name && !this.contact.last_name) || (!this.contact.email && !this.contact.office_phone && !this.contact.cell_phone);
        },
        confirmText: function () {
            if (this.saving) return 'Saving...';

            return this.editingContact ? "Save" : 'Create';
        },
        ...mapWritableState(useContactsStore, [
            'contact'
        ])
    }
}
</script>

<style scoped>

</style>
