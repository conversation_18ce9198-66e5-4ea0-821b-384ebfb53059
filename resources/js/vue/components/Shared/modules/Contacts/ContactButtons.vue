<template>
    <alerts-container :dark-mode="darkMode" v-if="alertActive" :alert-type="alertType" :text="alertText"/>
    <div @click="editContact(contact)" v-if="hasEditRights"
         class="cursor-pointer mr-5">
        <svg class="w-3.5" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9.05781 0.522434C9.21567 0.358989 9.4045 0.22862 9.61329 0.138933C9.82207 0.0492468 10.0466 0.00203912 10.2738 6.46124e-05C10.5011 -0.00190989 10.7264 0.0413885 10.9367 0.127433C11.147 0.213478 11.3381 0.340546 11.4988 0.501223C11.6595 0.6619 11.7865 0.852967 11.8726 1.06328C11.9586 1.27359 12.0019 1.49893 11.9999 1.72615C11.998 1.95337 11.9508 2.17793 11.8611 2.38671C11.7714 2.5955 11.641 2.78433 11.4776 2.94219L10.799 3.62071L8.37929 1.20096L9.05781 0.522434ZM7.16941 2.41083L0 9.58025V12H2.41975L9.59002 4.83059L7.16941 2.41083Z" fill="#0081FF"/>
        </svg>

    </div>
    <simple-icon class="mr-5" v-if="hasDeleteRights" @click="deleteContact(contact)" :icon="simpleIcon.icons.BIN" color="blue" clickable/>
    <div class="relative">
        <div v-if="actionsHandle" @click="toggleActionsHandle" class="fixed inset-0 w-full h-full z-10"></div>
        <div>
            <div @click="toggleActionsHandle" class="inline-flex items-center justify-center h-7 w-7 rounded-full cursor-pointer hover:bg-primary-100">
                <div class="bg-primary-500 h-1 w-1 rounded-full mr-1"></div>
                <div class="bg-primary-500 h-1 w-1 rounded-full mr-1"></div>
                <div class="bg-primary-500 h-1 w-1 rounded-full"></div>
            </div>
            <div v-if="actionsHandle" class="z-50 absolute top-7 right-0 rounded-lg border overflow-hidden shadow-lg w-64 divide-y cursor-pointer" :class="[darkMode ? 'bg-dark-background border-dark-border divide-dark-border text-slate-50' : 'bg-light-background border-light-border divide-light-border text-slate-900']">
                <!-- CALL -->
                <div  v-if="contactIsValid(contact)" :class="[darkMode ? 'hover:bg-dark-module' : 'hover:bg-light-module']">
                    <div @click="callOptions = ! callOptions" class="flex px-4 py-2  text-sm font-semibold items-center">
                        <svg
                             class="flex-shrink-0 inline w-3.5 mr-1" viewBox="0 0 16 16"
                             fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M15.7061 12.5332L12.0551 9.21369C11.8825 9.05684 11.6558 8.97318 11.4227 8.98038C11.1895 8.98759 10.9684 9.0851 10.8058 9.25231L8.65655 11.4626C8.13922 11.3638 7.09917 11.0396 6.02859 9.97172C4.958 8.90024 4.63377 7.85751 4.53767 7.34378L6.7462 5.19364C6.91363 5.03119 7.01128 4.80998 7.01848 4.57681C7.02569 4.34364 6.94189 4.11681 6.78482 3.94433L3.46619 0.294312C3.30905 0.121292 3.09065 0.0163428 2.85738 0.00175297C2.62411 -0.0128368 2.39434 0.0640823 2.21687 0.216174L0.2679 1.8876C0.112621 2.04344 0.0199401 2.25085 0.00743837 2.47049C-0.00603376 2.69503 -0.262902 8.01378 3.86137 12.1398C7.45933 15.7368 11.9662 16 13.2074 16C13.3889 16 13.5002 15.9946 13.5299 15.9928C13.7495 15.9805 13.9568 15.8874 14.1119 15.7315L15.7824 13.7816C15.9351 13.6047 16.0126 13.3751 15.9983 13.1419C15.9841 12.9086 15.8792 12.6902 15.7061 12.5332V12.5332Z"
                                fill="#0081FF"/>
                        </svg>
                        Call
                    </div>
                    <div v-if="phoneNumberOptionsHasAtLeastOneOption"  class="transform duration-200 transition-all grid" :style="{gridTemplateRows: callOptions ? '1fr' : '0fr'}">
                        <div class="z-50 overflow-hidden">
                            <p @click="call(contact, option.type)" class="px-4 text-sm hover:text-primary-500 pb-2" v-for="option in phoneNumberOptions">{{option.name}}</p>
                        </div>
                    </div>
                </div>
                <!-- TEXT -->
                <div v-if="contactIsValid(contact)" :class="[darkMode ? 'hover:bg-dark-module' : 'hover:bg-light-module']">
                    <div @click="textOptions = ! textOptions" class="px-4 py-2  text-sm font-semibold flex items-center">
                        <svg
                             class="flex-shrink-0 inline w-3.5 mr-1" viewBox="0 0 16 16"
                             fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M1.66667 12.9547H3.33333V16L7.58417 12.9547H11.6667C12.5858 12.9547 13.3333 12.2853 13.3333 11.4622V5.49244C13.3333 4.66936 12.5858 4 11.6667 4H1.66667C0.7475 4 0 4.66936 0 5.49244V11.4622C0 12.2853 0.7475 12.9547 1.66667 12.9547Z"
                                fill="#0081FF"/>
                            <path
                                d="M14.3332 0H4.33317C3.414 0 2.6665 0.797333 2.6665 1.77778H12.6665C13.5857 1.77778 14.3332 2.57511 14.3332 3.55556V10.6667C15.2523 10.6667 15.9998 9.86933 15.9998 8.88889V1.77778C15.9998 0.797333 15.2523 0 14.3332 0Z"
                                fill="#0081FF"/>
                        </svg>
                        Text Message
                    </div>
                    <div v-if="phoneNumberOptionsHasAtLeastOneOption" class="transform duration-200 transition-all grid" :style="{gridTemplateRows: textOptions ? '1fr' : '0fr'}">
                        <div class="z-50 overflow-hidden">
                            <p @click="sms(contact, option.type)" class="text-sm hover:text-primary-500 pb-2 px-4" v-for="option in phoneNumberOptions">{{option.name}}</p>
                        </div>
                    </div>
                </div>
                <!-- EMAIL -->
                <div v-if="contact.email && contact.email.trim().length > 0" class="px-4 py-2" :class="[darkMode ? 'hover:bg-dark-module' : 'hover:bg-light-module']" @click="$emit('email', {email: contact.email, id: contact.id})">
                    <div class=" text-sm font-semibold inline-flex items-center">
                        <svg class="w-3.5 mr-2 inline" viewBox="0 0 16 14" fill="none"
                             xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M0.00244141 2.09333L7.99944 6.53555L15.9964 2.09333C15.9668 1.52716 15.7435 0.994985 15.3723 0.605807C15.001 0.216628 14.5099 -0.000103983 13.9994 3.7426e-08H1.99944C1.48903 -0.000103983 0.997888 0.216628 0.626619 0.605807C0.25535 0.994985 0.0320455 1.52716 0.00244141 2.09333V2.09333Z"
                                fill="#0081FF"/>
                            <path
                                d="M16 4.57556L8 9.02L0 4.57556V11.1111C0 11.7005 0.210714 12.2657 0.585786 12.6825C0.960859 13.0992 1.46957 13.3333 2 13.3333H14C14.5304 13.3333 15.0391 13.0992 15.4142 12.6825C15.7893 12.2657 16 11.7005 16 11.1111V4.57556Z"
                                fill="#0081FF"/>
                        </svg>
                        Email
                    </div>
                </div>
                <!-- BOOK DEMO -->
                <div v-if="canBookDemo" @click="openCalendly" class="px-4 py-2 " :class="[darkMode ? 'hover:bg-dark-module' : 'hover:bg-light-module']">
                    <div class=" text-sm font-semibold inline-flex items-center">
                        <svg class="w-3.5 mr-2 inline" viewBox="0 0 15 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M2.34521 12.4579H14.0152V1.55724C14.0152 1.14424 13.8511 0.748145 13.5591 0.456106C13.267 0.164066 12.8709 0 12.4579 0H2.33586C1.39685 0 0 0.622118 0 2.33586V13.2366C0 14.9503 1.39685 15.5724 2.33586 15.5724H14.0152V14.0152H2.34521C1.98548 14.0058 1.55724 13.8633 1.55724 13.2366C1.55724 12.6098 1.98548 12.4673 2.34521 12.4579ZM3.8931 3.11448H10.9007V4.67173H3.8931V3.11448Z" fill="#0081FF"/>
                        </svg>
                        Book Demo
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import DispatchesGlobalEventsMixin from "../../../../mixins/dispatches-global-events-mixin";
import {contactIsValid, formatPhoneNumber, getPhoneNumberByType, phoneIsValid} from "../../../../../modules/contacts/helpers";
import Dropdown from "../../components/Dropdown.vue";
import ButtonDropdown from "../../components/ButtonDropdown.vue";
import AlertsMixin from "../../../../mixins/alerts-mixin";
import {mapState} from "pinia";
import {useCallStore} from "../../../../../stores/call.store";
import Alert from "../../components/Alert.vue";
import AlertsContainer from "../../components/AlertsContainer.vue";
import {CommunicationRelationTypes} from "../../../Communications/enums/communication";
import {useUserStore} from "../../../../../stores/user-store.js";
import {PERMISSIONS, ROLES, useRolesPermissions} from "../../../../../stores/roles-permissions.store";

import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../composables/useSimpleIcon.js";

/**
 * @typedef contact
 * @property {number} id
 * @property {string} name
 * @property {string} title
 * @property {string} first_name
 * @property {string} last_name
 * @property {string} email
 * @property {number|boolean} status
 * @property {string|null} date_registered
 * @property {string} cell_phone
 * @property {string} office_phone
 * @property {number|boolean} is_contact
 * @property {number|null} total_calls_count
 * @property {number|null} total_calls_over_one_minute_count
 * @property {number|null} latest_call_timestamp
 */
const simpleIcon = useSimpleIcon()

export default {
    name: "ContactButtons",
    components: {SimpleIcon, AlertsContainer, Alert, ButtonDropdown, Dropdown},
    emits: ['edit:contact', 'delete:contact', 'email'],
    props: {
        contact: {
            type: Object,
            required: true
        },
        darkMode: {
            type: Boolean,
            default: false,
        },
        companyName: {
            type: String,
            default: null
        },
        companyWebsite: {
            type: String,
            default: null
        },
        hasEditRights: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            showPhoneCallDropdown: false,
            showPhoneSmsDropdown: false,
            actionsHandle: false,
            callOptions: false,
            textOptions: false,
            userStore: useUserStore(),
            permissionStore: useRolesPermissions(),
        }
    },
    computed: {
        simpleIcon() {
            return useSimpleIcon()
        },
        phoneNumberOptions() {
            let result = [];

            if (this.contact.hasOwnProperty('office_phone') && this.contact.office_phone && phoneIsValid(this.contact.office_phone)) {
                result.push({
                    name: formatPhoneNumber(this.contact.office_phone) + ' (Office)',
                    value: this.contact.office_phone,
                    type: "office_phone"
                });
            }

            if (this.contact.hasOwnProperty('cell_phone') && this.contact.cell_phone && phoneIsValid(this.contact.cell_phone)) {
                result.push({
                    name: formatPhoneNumber(this.contact.cell_phone) + ' (Mobile)',
                    value: this.contact.cell_phone,
                    type: "cell_phone"
                });
            }

            return result;
        },
        phoneNumberOptionsHasAtLeastOneOption() {
            return this.phoneNumberOptions.length > 0;
        },
        ...mapState(useCallStore, [
            'callActive'
        ]),
        canBookDemo() {
            return (this.permissionStore.hasRole(ROLES.PROSPECTOR) || this.permissionStore.hasRole(ROLES.BUSINESS_DEVELOPMENT_MANAGER))
        },
        hasDeleteRights() {
            return this.hasEditRights;
        },
    },
    mixins: [DispatchesGlobalEventsMixin, AlertsMixin],
    methods: {
        contactIsValid,
        /**
         * @param contact
         */
        editContact(contact) {
            this.$emit('edit:contact', contact)
        },
        deleteContact(contact) {
            this.$emit('delete:contact', contact)
        },
        /**
         * @param contact
         * @param phoneType
         */
        call(contact, phoneType) {
            if (!this.callActive) {
                const targetNumber = getPhoneNumberByType(contact, phoneType);
                this.dispatchGlobalEvent('call', {phone: targetNumber, name: contact.name, id: contact.id, relId: contact.id, relType: CommunicationRelationTypes.COMPANY_USER});
            } else {
                console.warn('Please end your current call before making another one.')
                this.showAlert('warning', 'Please end your current call before making another one.')
            }
            this.toggleActionsHandle();
        },
        /**
         * @param {contact} contact
         * @param phoneType
         */
        sms(contact, phoneType) {
            const targetNumber = getPhoneNumberByType(contact, phoneType);
            this.dispatchGlobalEvent('sms', {phone: targetNumber, name: contact.name, id: contact.id, relId: contact.id, relType: CommunicationRelationTypes.COMPANY_USER});
            this.toggleActionsHandle();
        },
        toggleActionsHandle() {
            this.actionsHandle = ! this.actionsHandle;
            this.callOptions = false;
            this.textOptions = false;
        },
        async openCalendly() {
            try {
                Calendly.initPopupWidget({
                    url: this.userStore.user?.meeting_url,
                    prefill: {
                        name: this.contact.name ?? '',
                        email: this.contact.email ?? '',
                        customAnswers: {
                            a1: this.contact.cell_phone ?? this.contact.office_phone ?? '',
                            a2: this.companyName ?? '',
                            a3: this.companyWebsite ?? '',
                        }
                    },
                });
            } catch (error) {
                flashAlert('error', 'Error opening Calendly')
            }
            this.toggleActionsHandle();
        }
    }
}
</script>

<style scoped>

</style>
