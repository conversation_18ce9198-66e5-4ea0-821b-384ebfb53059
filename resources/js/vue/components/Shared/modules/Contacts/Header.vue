<template>
    <div class="px-5 pt-5 pb-4">
        <div class="flex items-center justify-between mb-3">
            <div class="items-center space-x-3">
                <h5 class="text-primary-500 text-sm uppercase font-bold leading-tight">
                    Contacts</h5>
            </div>
            <ActionsHandle
                :dark-mode="darkMode"
                :no-custom-action="false"
                no-edit-button
                no-delete-button
                :custom-actions="contactActions"
                @add-contact="showContactAddModal"></ActionsHandle>
        </div>
        <div class="flex gap-3 mb-2">
            <CustomInput class="flex-grow" search-icon placeholder="Search contacts..." v-model="contactMultiSearch" :dark-mode="darkMode"></CustomInput>
            <ContactsFilterButton :dark-mode="darkMode"></ContactsFilterButton>
        </div>

    </div>
</template>

<script>
import CustomInput from "../../components/CustomInput.vue";
import WithoutUserPresetFilterConfigModal from "../../components/WithoutUserPresetFilterConfigModal.vue";
import ContactsFilterButton from "./Header/ContactsFilterButton.vue";
import {mapActions, mapWritableState} from "pinia";
import {useContactsStore} from "../../../../../stores/contacts.store";
import ActionsHandle from "../../components/ActionsHandle.vue";

export default {
    name: "ContactsHeader",
    components: {ActionsHandle, ContactsFilterButton, WithoutUserPresetFilterConfigModal, CustomInput},
    emits: ['show:modal'],
    props: {
        showAddContactButton: {
            required: false,
            default: true
        },
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            contactActions: [
                {event: 'add-contact', name: 'Add Contact'},
            ]
        }
    },
    methods: {
        showContactAddModal() {
            this.$emit('show:modal')
        },
        ...mapActions(useContactsStore, ['applyFilterToContacts']),
    },
    watch: {
        contactMultiSearch(to, from) {
            this.applyFilterToContacts()
        }
    },
    computed: {
        ...mapWritableState(useContactsStore, {
            contactMultiSearch: 'contactMultiSearch'
        }),
    }
}
</script>

<style scoped>

</style>
