<template>
    <div class="absolute top-1 left-1 select-none cursor-pointer w-6 scale-x-75 "
         :class="{ 'text-primary-500': contact.pinned, 'text-slate-300 hover:text-primary-200': !contact.pinned && !darkMode, 'text-slate-600 hover:text-primary-200': !contact.pinned && darkMode }"
         @click="toggleContactPinned(contact.id)"
         @mouseover="mouseOverPin = contact.id"
         @mouseleave="mouseOverPin = null"
    >
        <svg v-if="contact.pinned && mouseOverPin === contact.id" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
             fill="currentColor" class="w-6 h-6">
            <path
                d="M3.53 2.47a.75.75 0 00-1.06 1.06l18 18a.75.75 0 101.06-1.06l-18-18zM20.25 5.507v11.561L5.853 2.671c.15-.043.306-.075.467-.094a49.255 49.255 0 0111.36 0c1.497.174 2.57 1.46 2.57 2.93zM3.75 21V6.932l14.063 14.063L12 18.088l-7.165 3.583A.75.75 0 013.75 21z"/>
        </svg>
        <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6">
            <path fill-rule="evenodd"
                  d="M6.32 2.577a49.255 49.255 0 0111.36 0c1.497.174 2.57 1.46 2.57 2.93V21a.75.75 0 01-1.085.67L12 18.089l-7.165 3.583A.75.75 0 013.75 21V5.507c0-1.47 1.073-2.756 2.57-2.93z"
                  clip-rule="evenodd"/>
        </svg>
    </div>
    <div @click="toggleExpandContact(contact.id)"
         class="flex items-center relative" :class="{ 'cursor-pointer': !contactHasNoDetails }">
        <div v-if="!contactHasNoDetails" class="w-4 p-1 absolute -left-3 top-6">
            <svg class="w-4"
                 :class="{'transform transition duration-200 rotate-90' : expandedContacts.includes(contact.id) }"
                 width="6" height="10" viewBox="0 0 6 10" fill="none"
                 xmlns="http://www.w3.org/2000/svg">
                <path d="M1 9L5 5L1 1" stroke="#0081FF" stroke-width="2" stroke-linecap="round"
                      stroke-linejoin="round"/>
            </svg>
        </div>
        <div class="ml-3">
            <p class="text-sm truncate pb-0 font-medium">
                <a :href="`/company-users/${contact.id}`" class="text-primary-500 font-bold flex items-center" target="_blank">
                    {{ contact.name }}
                </a>
                <Badge class="mr-2 my-2" :dark-mode="darkMode" color="red" v-if="!contact.status">Inactive</Badge>
                <Badge class="mr-2 my-2" :dark-mode="darkMode" color="green" v-if="contact.is_decision_maker">Decision Maker</Badge>
                <Badge class="my-2" :dark-mode="darkMode" color="green" v-if="contact.dnc_contact">DNC Contact</Badge>
                <Badge class="my-2" :dark-mode="darkMode" color="purple" v-if="contact.import_source === 'salesintel'">Sales Intel</Badge>
            </p>
            <p class="text-grey-400 text-sm font-medium">{{ contact.title }}</p>
            <div :class="[darkMode ? 'text-slate-400' : 'text-slate-500']">
                <p v-if="typeof contact?.latest_call_timestamp == 'number'" class=" text-sm">
                    Last
                    called on {{
                        DateTime.fromSeconds(contact.latest_call_timestamp).toLocaleString(DateTime.DATETIME_FULL)
                    }}</p>
                <p v-else>This contact was not called yet.</p>
                <div class="inline-flex items-center text-grey-600">
                    <p class=" text-sm mr-6">Total calls: {{
                            contact?.total_calls_count ?? "N/A"
                        }}</p>
                    <p class=" text-sm">Total calls over a minute: {{
                            contact?.total_calls_over_one_minute_count ?? "N/A"
                        }}</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {DateTime} from "luxon";
import SharedApiService from "../../services/api";
import Badge from "../../components/Badge.vue";

/**
 * @typedef contact
 * @property {number} id
 * @property {boolean} pinned
 */

export default {
    name: "Contact",
    components: {Badge},
    data() {
        return {
            mouseOverPin: null,
            api: SharedApiService.make(),
        }
    },
    computed: {
        DateTime() {
            return DateTime
        },
        contactHasValidEmail() {
            return this.contact.email && this.contact.email.length > 0;
        },
        contactHasValidOfficePhone() {
            return this.contact.office_phone && this.contact.office_phone.length > 0;
        },
        contactHasValidCellPhone() {
            return this.contact.cell_phone && this.contact.cell_phone.length > 0;
        },
        contactHasNoDetails() {
            return !this.contactHasValidEmail && !this.contactHasValidOfficePhone && !this.contactHasValidCellPhone
        }
    },
    emits: ['toggle:expand', 'update:contact'],
    props: {
        contact: {
            type: Object,
            required: true
        },
        expandedContacts: {
            type: Array,
            required: true
        },
        darkMode: {
            type: Boolean,
            required: true
        },
        companyId: {
            type: Number,
            required: true
        }
    },
    methods: {
        toggleExpandContact(id) {
            if (this.contactHasNoDetails) return;
            this.$emit('toggle:expand', id)
        },
        toggleContactPinned() {
            this.api.toggleContactPin(this.contact.id, this.companyId).then(resp => {
                if (!resp.data?.data?.status) throw new Error();
            }).catch(err => {
                this.showAlert(err.response?.data?.message || this.errorMessages.pinAction);
            }).finally(() => this.$emit('update:contact'));
        }
    },
}
</script>

<style scoped>

</style>
