<template>
    <div>
        <alerts-container :dark-mode="darkMode" v-if="alertActive" :alert-type="alertType" :text="alertText" />

        <div class="grid grid-cols-4 gap-5 mb-4 px-8 pt-5">
            <div class="text-base text-xs font-medium text-grey-400 uppercase">ID</div>
            <div class="text-base text-xs font-medium text-grey-400 uppercase">Full Name</div>
            <div class="text-base text-xs font-medium text-grey-400 uppercase">Email</div>
            <div class="text-base text-xs font-medium text-grey-400 uppercase">Phone</div>
        </div>
        <template v-if="!loading && paginationData.total > 0">
            <div class="border-t border-b divide-y" :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}">
                <div v-for="user in users" class="grid grid-cols-4 gap-5 items-center py-2 rounded px-8"
                     :class="{'border-light-border hover:bg-light-module': !darkMode, 'border-dark-border hover:bg-dark-module': darkMode}">
                    <div class="truncate">{{ user.id }}</div>
                    <div class="truncate">{{ user.name }}</div>
                    <div class="truncate">{{ user.email }}</div>
                    <div class="truncate">{{ user.cell_phone }}</div>
                </div>
            </div>
            <div class="p-3">
                <Pagination :dark-mode="darkMode" :pagination-data="paginationData" :show-pagination="true" @change-page="handlePaginationEvent"></Pagination>
            </div>
        </template>
        <template v-else-if="!loading">
            <div class="text-center w-full my-10">
                No data
            </div>
        </template>
        <template v-else-if="loading">
            <div class="text-center w-full my-10">
                <loading-spinner/>
            </div>
        </template>
    </div>
</template>

<script>
    import SharedApiService from "../services/api";
    import Pagination from "../components/Pagination.vue";
    import LoadingSpinner from "../components/LoadingSpinner.vue";
    import AlertsContainer from "../components/AlertsContainer.vue";

    export default {
        name: "UsersTable",
        components: {
            AlertsContainer,
            LoadingSpinner,
            Pagination
        },
        props: {
            darkMode: {
                type: Boolean,
                default: false
            },
            companyId: {
                type: Number,
                default: 0,
                required: true
            }
        },
        created: function() {
            this.api = SharedApiService.make();
            this.getCompanyUsers();
        },
        data: function() {
            return {
                api: null,
                users: null,
                paginationData: null,
                loading: false,
                alertType: '',
                alertActive: false,
                alertText: '',
            };
        },
        methods: {
            getCompanyUsers(page = 1) {
                this.loading = true;

                this.api.getPaginatedCompanyUsers(this.companyId, page).then(res => {
                    if(res.data.data.status === true) {
                        let {data, ...paginationData} = res.data.data.users;
                        this.users = data;
                        this.paginationData = paginationData;
                    }
                    else {
                        this.alertType = 'error';
                        this.alertText = 'Failed to retrieve users';
                        this.alertActive = true;

                        setTimeout(() => {
                            this.alertActive = false;
                        }, 5000);
                    }
                }).catch(err => {
                    this.alertType = 'error';
                    this.alertText = 'Error retrieving users';
                    this.alertActive = true;

                    setTimeout(() => {
                        this.alertActive = false;
                    }, 5000);
                }).finally(() => {
                    this.loading = false;
                });
            },
            async handlePaginationEvent(newPageUrl) {
                this.loading = true;
                await axios.get(newPageUrl.link).then(resp => {
                    let {data, ...paginationData} = resp.data.data.users;
                    this.users = data;
                    this.paginationData = paginationData;
                }).catch(e => console.error(e)).finally(() => this.loading = false);
            }
        }
    }
</script>

<style scoped>

</style>
