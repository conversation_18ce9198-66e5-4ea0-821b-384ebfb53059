<template>
    <div>
        <div class="border rounded-lg" :class="[darkMode ? 'bg-dark-module border-dark-border text-white' : 'bg-light-module border-light-border text-slate-900']">
            <div class="px-5 pt-5">
                <div class="flex items-center justify-between h-6">
                    <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Linked Companies</h5>
                    <button v-if="canCreate"
                            class="ml-2 transition duration-200 inline-flex items-center font-semibold bg-grey-475 hover:bg-grey-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5"
                            @click="showAddCompanyLinkModal">
                        Add Company Link
                    </button>
                </div>
            </div>
            <div class="grid gap-x3 px-5 pt-5 pb-3" :class="{'grid-cols-9': canDelete, 'grid-cols-8': !canDelete}">
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs">ID</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs text-center col-span-2">Name</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs text-center">Status</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs text-center col-span-2">Industry</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs text-center col-span-2">Link Description</p>
                <div v-if="canDelete" class="text-slate-400 font-medium tracking-wide uppercase text-xs text-center">Delete Link</div>
            </div>
            <template v-if="!loading && paginationData?.total > 0">
                <div class="border-t overflow-y-auto"
                     :class="[darkMode ? 'bg-dark-background border-dark-border' : 'bg-light-background  border-light-border']">
                    <div>
                        <div class="grid gap-x-3 border-b px-5 py-3 items-center"
                             v-for="companyLink in companyLinks" :key="companyLink.id"
                             :class="{
                                 'text-slate-900 hover:bg-light-module border-light-border grid-cols-9': !darkMode && canDelete,
                                 'text-slate-900 hover:bg-light-module border-light-border grid-cols-8': !darkMode && !canDelete,
                                 'text-slate-100 hover:bg-dark-module border-dark-border grid-cols-9': darkMode && canDelete,
                                 'text-slate-100 hover:bg-dark-module border-dark-border grid-cols-8': darkMode && !canDelete,
                             }">
                            <a @click="openCompanyPage(companyLink.linked_company.id)" class="text-sm truncate cursor-pointer text-primary-500">{{  companyLink.linked_company.id }}</a>
                            <a @click="openCompanyPage(companyLink.linked_company.id)" class="text-sm col-span-2 truncate text-center cursor-pointer text-primary-500">{{ companyLink.linked_company.name }}</a>
                            <a  class="text-sm truncate text-center">{{ companyLink.linked_company.status }}</a>
                            <div class="col-span-2 flex justify-center" v-if="companyLink.linked_company.industries.length > 0">
                                <div class=" text-sm truncate text-center" v-for="(industry, index ) in companyLink.linked_company.industries">
                                    <div v-if="!(index === companyLink.linked_company.industries.length-1)">{{industry.name}}&nbsp;/&nbsp;</div>
                                    <div v-else>{{industry.name}}</div>
                                </div>
                            </div>
                            <div class="text-sm truncate text-center col-span-2" v-else>
                                No Industry
                            </div>
                            <a  class="text-sm truncate col-span-2 text-center">{{  companyLink.comment }}</a>
                            <div v-if="canDelete" class="flex justify-center">
                                <svg @click="deleteCompanyLink(companyLink.id)" class="w-6 cursor-pointer text-red-700"  xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <template v-else-if="!loading">
                <div class="text-center w-full my-10">
                    No Company Links
                </div>
            </template>
            <template v-else-if="loading">
                <div class="text-center w-full my-10">
                    <loading-spinner/>
                </div>
            </template>
            <div class="p-3">
                <Pagination :dark-mode="darkMode" :pagination-data="paginationData" :show-pagination="true" @change-page="handlePaginationEvent"></Pagination>
            </div>
        </div>
        <Modal :dark-mode="darkMode" v-if="showModal" :small = true
               @confirm="addCompanyLink" @close="showModal = false"
               :confirm-text="savingLink ? 'Saving...' : 'Create Task'"
               :disable-confirm="savingLink">
            <template v-slot:header>
                <p class="font-semibold">Add Company Link</p>
            </template>
            <template v-slot:content>
                <div class="mb-6" v-if="alertMessage">
                    <alert :text="alertMessage" :alert-type="alertType" :dark-mode="darkMode"></alert>
                </div>
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    <div class="col-span-2">
                        <p class="uppercase font-semibold text-xs mb-2"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Company Name
                            <span style="font-size: 0.65rem;" class="block"
                                  :class="[darkMode ? 'text-slate-400' : 'text-slate-500']"></span>
                        </p>
                        <autocomplete
                            :dark-mode="darkMode"
                            class="col-span-2"
                            v-model="company"
                            :options="companies"
                            :placeholder="'Company name'"
                            :create-user-input-option="true"
                            @search="searchCompanies('companyname', $event)">
                        </autocomplete>
                    </div>
                    <div class="col-span-2">
                        <p class="uppercase font-semibold text-xs mb-2"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Link Description
                        </p>
                        <textarea
                            class="min-h-88 w-full border rounded pl-4 outline-none focus:outline-none focus:ring-0 focus:border-primary-500 focus:shadow-lg focus:shadow-primary-500/10 transition duration-200 pr-4"
                            placeholder="Enter link description..." type="text" v-model="linkDescription"
                            :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}"/>
                    </div>
                </div>
            </template>
        </Modal>
    </div>
</template>

<script>
import LoadingSpinner from "../../LeadProcessing/components/LoadingSpinner.vue";
import SharedApiService from "../services/api";
import Pagination from "../components/Pagination.vue";
import Modal from "../components/Modal.vue";
import Dropdown from "../components/Dropdown.vue";
import ToggleSwitch from "../components/ToggleSwitch.vue";
import Autocomplete from "../components/Autocomplete.vue";
import Alert from "../components/Alert.vue";
import {PERMISSIONS, useRolesPermissions} from "../../../../stores/roles-permissions.store";

export default {
    name: "CompanyLinks",
    components: {Alert, Autocomplete, ToggleSwitch, Dropdown, Modal, Pagination, LoadingSpinner},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        companyId: {
            type: Number,
            default: null,
        },
        currentUserId: {
            type: Number,
            default: null,
        },
    },
    created: function () {
        this.api = SharedApiService.make();
        this.getCompanyLinks(1);
    },
    computed: {
        canCreate() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_COMPANY_LINK_CREATE);
        },
        canDelete() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_COMPANY_LINK_DELETE);
        }
    },
    data() {
        return {
            api: null,
            companyLinks: null,
            paginationData: [],
            loading: false,
            showAddCompanyLinkButton: true,
            showModal: false,
            company: null,
            companies: [],
            linkDescription: null,
            linkCompanyId: null,
            savingLink: false,
            alertMessage: null,
            alertType: 'success',
            permissionStore: useRolesPermissions(),
        }
    },
    methods: {
        openCompanyPage(companyId) {
            window.open('/companies/'+companyId, '_blank');
        },
        getCompanyLinks(page) {
            this.loading = true;

            this.api.getCompanyLinks(this.companyId, page).then(res => {
                if(res) {
                    const { data, links,  meta } = res.data;
                    this.companyLinks = data;
                    this.paginationData = { links, ...meta }
                }
                else {
                    this.showAlert('error', 'Failed to retrieve company links');
                }
            }).catch(() => {
                this.showAlert('error', 'Failed to retrieve company links');
            }).finally(() => {
                this.loading = false;
            });
        },

        async handlePaginationEvent(newPageUrl, perPage, newPage) {
            await axios.get(newPageUrl.link).then(resp => {
                let {data, ...paginationData} = resp.data.data.company_links;
                this.companyLinks = data;
                this.paginationData = paginationData;
            }).catch(e => console.error(e)).finally(() => this.loading = false);
        },

        async deleteCompanyLink(linkId) {
            this.loading = true

            await this.api.deleteCompanyLink(this.companyId, linkId);
            this.getCompanyLinks(1);
        },

        showAddCompanyLinkModal() {
            this.showModal = true;
        },

        searchCompanies(nameType, query) {
            this.api.searchCompanyNamesAndId(query).then(res => {
                if (res.data.data.status === true) {
                    this.companies = res.data.data.companies;
                }
            })
        },

        async addCompanyLink() {
            this.alertMessage = null;
            this.savingLink = true;

            const otherCompany = this.company;
            const companyComment = this.linkDescription;

            const resp = await this.api.addCompanyLink(this.companyId, otherCompany, companyComment).catch(e => {
                this.alertType = 'error';
                this.alertMessage = "The company name field is invalid.";
                this.savingLink = false
            })

            if (!resp.data.data.status === false){
                this.company = null;
                this.linkDescription = null;
                this.getCompanyLinks(1);
                this.showModal = false;
            }

            this.alertType = 'error'
            this.alertMessage = resp.data.data.error
            this.savingLink = false
        }
    }
}
</script>

<style scoped>

</style>
