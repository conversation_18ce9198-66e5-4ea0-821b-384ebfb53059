<template>
    <div class="border rounded-lg relative overflow-hidden"
        :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <div class="px-5 pt-5 pb-4">
            <div class="flex items-center justify-between">
                <h5 class="text-primary-500 text-sm uppercase font-bold leading-tight">Activity</h5>
                <ActionsHandle
                    :dark-mode="darkMode"
                    :no-custom-action="false"
                    no-edit-button
                    no-delete-button
                    :custom-actions="activityActions"
                    @add-action="toggleNoteModal(true)"></ActionsHandle>

            </div>
            <!--        <div class="relative mb-3" v-if="showFilters">
                <input class="w-full border rounded pl-8  focus:outline-none focus:border focus:border-primary-500 pr-4 py-2 h-9" placeholder="Search activity" type="text" v-model="filterEventName"
                       :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}"/>
                <div class="absolute top-0 left-0 w-8 h-full flex justify-center items-center">
                    <svg class="inset-y-0 fill-current text-slate-500" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M5.66667 2C4.69421 2 3.76158 2.38631 3.07394 3.07394C2.38631 3.76158 2 4.69421 2 5.66667C2 6.14818 2.09484 6.62498 2.27911 7.06984C2.46338 7.5147 2.73346 7.91891 3.07394 8.25939C3.41442 8.59987 3.81863 8.86996 4.26349 9.05423C4.70835 9.23849 5.18515 9.33333 5.66667 9.33333C6.14818 9.33333 6.62498 9.23849 7.06984 9.05423C7.5147 8.86996 7.91891 8.59987 8.25939 8.25939C8.59987 7.91891 8.86996 7.5147 9.05423 7.06984C9.23849 6.62498 9.33333 6.14818 9.33333 5.66667C9.33333 4.69421 8.94703 3.76158 8.25939 3.07394C7.57176 2.38631 6.63913 2 5.66667 2ZM1.65973 1.65973C2.72243 0.597022 4.16377 0 5.66667 0C7.16956 0 8.6109 0.597022 9.6736 1.65973C10.7363 2.72243 11.3333 4.16377 11.3333 5.66667C11.3333 6.41082 11.1868 7.14769 10.902 7.83521C10.7458 8.21219 10.5498 8.57029 10.3178 8.90361L13.7071 12.2929C14.0976 12.6834 14.0976 13.3166 13.7071 13.7071C13.3166 14.0976 12.6834 14.0976 12.2929 13.7071L8.90361 10.3178C8.57029 10.5498 8.21219 10.7458 7.83521 10.902C7.14769 11.1868 6.41082 11.3333 5.66667 11.3333C4.92251 11.3333 4.18564 11.1868 3.49813 10.902C2.81062 10.6172 2.18593 10.1998 1.65973 9.6736C1.13353 9.14741 0.716126 8.52272 0.431349 7.83521C0.146573 7.1477 0 6.41082 0 5.66667C0 4.16377 0.597022 2.72243 1.65973 1.65973Z"/>
                    </svg>
                </div>
            </div>-->
            <div class="flex gap-3 items-center mt-3 mb-2" v-if="showSearch">
                <CustomInput class="flex-grow" :dark-mode="darkMode" placeholder="Search activity..." :search-icon="true" @keyup.enter="search" v-model="searchValue" />
                <CustomButton color="slate-inverse" :dark-mode="darkMode" @click="showFilterConfigModal = true" class="inline-flex items-center cursor-pointer"
                              :class="[darkMode ? 'text-slate-100 hover:text-white' : 'text-slate-700 hover:text-slate-900']">
                    <svg class="mr-2 fill-current w-3.5" viewBox="0 0 11 11" fill="none"
                         xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd"
                              d="M11 1.83333C11 2.17084 10.7264 2.44444 10.3889 2.44444L4.78404 2.44444C4.69374 2.69983 4.54711 2.9345 4.35192 3.1297C4.0081 3.47351 3.54179 3.66667 3.05556 3.66667C2.56933 3.66667 2.10301 3.47351 1.75919 3.1297C1.564 2.9345 1.41737 2.69983 1.32707 2.44444L0.611112 2.44444C0.273605 2.44444 1.0972e-06 2.17084 1.11196e-06 1.83333C1.12671e-06 1.49583 0.273605 1.22222 0.611112 1.22222L1.32707 1.22222C1.41737 0.966836 1.564 0.732162 1.75919 0.53697C2.10301 0.193154 2.56933 -3.68516e-07 3.05556 -3.47263e-07C3.54179 -3.26009e-07 4.0081 0.193154 4.35192 0.536971C4.54711 0.732162 4.69374 0.966837 4.78404 1.22222L10.3889 1.22222C10.7264 1.22222 11 1.49583 11 1.83333ZM11 5.5C11 5.83751 10.7264 6.11111 10.3889 6.11111L9.67293 6.11111C9.58263 6.3665 9.436 6.60117 9.24081 6.79636C8.89699 7.14018 8.43067 7.33333 7.94444 7.33333C7.45821 7.33333 6.9919 7.14018 6.64808 6.79636C6.45289 6.60117 6.30626 6.3665 6.21596 6.11111L0.611112 6.11111C0.273605 6.11111 9.36927e-07 5.83751 9.5168e-07 5.5C9.66433e-07 5.16249 0.273605 4.88889 0.611112 4.88889L6.21596 4.88889C6.30626 4.6335 6.45289 4.39883 6.64808 4.20364C6.9919 3.85982 7.45821 3.66667 7.94444 3.66667C8.43068 3.66667 8.89699 3.85982 9.24081 4.20364C9.436 4.39883 9.58263 4.6335 9.67293 4.88889L10.3889 4.88889C10.7264 4.88889 11 5.16249 11 5.5ZM11 9.16667C11 9.50418 10.7264 9.77778 10.3889 9.77778L4.78404 9.77778C4.69374 10.0332 4.54711 10.2678 4.35192 10.463C4.0081 10.8068 3.54179 11 3.05556 11C2.56933 11 2.10301 10.8068 1.75919 10.463C1.564 10.2678 1.41737 10.0332 1.32707 9.77778L0.611112 9.77778C0.273605 9.77778 7.76652e-07 9.50417 7.91405e-07 9.16667C8.06158e-07 8.82916 0.273605 8.55556 0.611112 8.55556L1.32707 8.55556C1.41737 8.30017 1.564 8.0655 1.75919 7.8703C2.10301 7.52649 2.56933 7.33333 3.05556 7.33333C3.54179 7.33333 4.0081 7.52649 4.35192 7.8703C4.54711 8.0655 4.69374 8.30017 4.78404 8.55556L10.3889 8.55556C10.7264 8.55556 11 8.82916 11 9.16667ZM8.55556 5.5C8.55556 5.33792 8.49117 5.18249 8.37657 5.06788C8.26196 4.95327 8.10652 4.88889 7.94444 4.88889C7.78237 4.88889 7.62693 4.95327 7.51232 5.06788C7.39772 5.18249 7.33333 5.33792 7.33333 5.5C7.33333 5.66208 7.39772 5.81751 7.51232 5.93212C7.62693 6.04673 7.78237 6.11111 7.94444 6.11111C8.10652 6.11111 8.26196 6.04673 8.37657 5.93212C8.49117 5.81752 8.55556 5.66208 8.55556 5.5ZM3.66667 1.83333C3.66667 1.67126 3.60228 1.51582 3.48768 1.40121C3.37307 1.28661 3.21763 1.22222 3.05556 1.22222C2.89348 1.22222 2.73804 1.28661 2.62344 1.40121C2.50883 1.51582 2.44445 1.67126 2.44445 1.83333C2.44445 1.99541 2.50883 2.15085 2.62344 2.26545C2.73804 2.38006 2.89348 2.44444 3.05556 2.44444C3.21763 2.44444 3.37307 2.38006 3.48768 2.26545C3.60228 2.15085 3.66667 1.99541 3.66667 1.83333ZM3.66667 9.16667C3.66667 9.00459 3.60228 8.84915 3.48768 8.73455C3.37307 8.61994 3.21763 8.55556 3.05556 8.55556C2.89348 8.55556 2.73804 8.61994 2.62344 8.73455C2.50883 8.84915 2.44444 9.00459 2.44444 9.16667C2.44444 9.32874 2.50883 9.48418 2.62344 9.59879C2.73804 9.71339 2.89348 9.77778 3.05556 9.77778C3.21763 9.77778 3.37307 9.71339 3.48768 9.59879C3.60228 9.48418 3.66667 9.32874 3.66667 9.16667Z"/>
                    </svg>
                    Filters
                </CustomButton>
            </div>
        </div>
        <div class="border-t border-b overflow-y-auto"
            :class="[darkMode ? 'bg-dark-background border-dark-border' : 'bg-light-background  border-light-border', tableHeight, paginationData && paginationData.to ? 'pb-10' : '']">
            <template v-if="!loading">
                <AlertsContainer :dark-mode="darkMode" v-if="alertActive" :alert-type="alertType" :text="alertText" />
                <template v-for="(action, idx) in actions" :key="action.id">
                    <div v-if="checkFilters(action)" class="relative"
                        :class="{ 'border-l-primary-500 border-l-4': action.pinned }">
                        <div class="absolute top-1 select-none cursor-pointer w-6 scale-x-75 "
                            :class="{ 'text-primary-500': action.pinned, 'text-slate-300 hover:text-primary-200': !action.pinned && !darkMode, 'text-slate-600 hover:text-primary-200': !action.pinned && darkMode }"
                            @click="toggleActionPinned(action.id)" @mouseover="mouseOverPin = action.id"
                            @mouseleave="mouseOverPin = null">
                            <svg v-if="action.pinned && mouseOverPin == action.id" xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6">
                                <path
                                    d="M3.53 2.47a.75.75 0 00-1.06 1.06l18 18a.75.75 0 101.06-1.06l-18-18zM20.25 5.507v11.561L5.853 2.671c.15-.043.306-.075.467-.094a49.255 49.255 0 0111.36 0c1.497.174 2.57 1.46 2.57 2.93zM3.75 21V6.932l14.063 14.063L12 18.088l-7.165 3.583A.75.75 0 013.75 21z" />
                            </svg>
                            <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                                class="w-6 h-6">
                                <path fill-rule="evenodd"
                                    d="M6.32 2.577a49.255 49.255 0 0111.36 0c1.497.174 2.57 1.46 2.57 2.93V21a.75.75 0 01-1.085.67L12 18.089l-7.165 3.583A.75.75 0 013.75 21V5.507c0-1.47 1.073-2.756 2.57-2.93z"
                                    clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="absolute top-1 right-2 select-none text-xs uppercase"
                            :class="{ 'text-primary-300': darkMode, 'text-primary-700': !darkMode }">
                            {{ action.pinned ? ('Pinned' + (action.category && action.category.length > 0 ? ' - ' : ''))
                            : '' }}
                            {{ action.category ?? '' }}
                        </div>
                        <div @click="toggleExpandActivity(action.id)" class="grid grid-cols-1 gap-x-3 border-b px-5"
                            :class="{
                                'bg-primary-50 hover:bg-primary-100': action.pinned && !darkMode,
                                'bg-dark-module hover:bg-dark-module': action.pinned && darkMode,
                                'hover:bg-light-module': !action.pinned && !darkMode,
                                'hover:bg-dark-module': !action.pinned && darkMode,
                                'text-slate-900 border-light-border': !darkMode,
                                'text-slate-100 hover:bg-dark-module border-dark-border': darkMode
                            }">
                            <div class="py-4 cursor-pointer">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center w-full">
                                        <svg :class="{'transform transition duration-200 rotate-90' : openedActivities.includes(action.id) }"
                                            class="mr-4" width="6" height="10" viewBox="0 0 6 10" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path d="M1 9L5 5L1 1" stroke="#0081FF" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round" />
                                        </svg>
                                        <div class="w-full">
                                            <div class="flex justify-start items-center">
                                                <p class="text-xs text-slate-500 mr-2"
                                                    v-if="action.display_date && action.display_date.length > 0">
                                                    For: {{ action.display_date ?
                                                    $filters.dateFromTimestamp(action.display_date) : 'No Date Set' }}
                                                </p>
                                                <p class="text-xs text-slate-500">
                                                    Created: {{ $filters.dateFromTimestamp(action.created_timestamp,
                                                    'usWithTime') }}
                                                </p>
                                                <p v-if="action.updated_timestamp && action.updated_timestamp > action.created_timestamp"
                                                    class="text-xs text-slate-500 ml-2">
                                                    Last Updated: {{
                                                    $filters.dateFromTimestamp(action.updated_timestamp, 'usWithTime')
                                                    }}
                                                </p>
                                            </div>
                                            <div
                                                v-if="action.previous_sales_status !== null && action.updated_sales_status !== null">
                                                <p class="text-xs text-slate-500">
                                                    <strong>Sales Status Change:</strong> from {{
                                                    action.previous_sales_status }} to {{ action.updated_sales_status }}
                                                </p>
                                            </div>

                                            <div>
                                                <p class="pb-0 text-sm font-semibold">
                                                    {{ action.subject }}
                                                </p>
                                                <p v-if="action.from" class="text-sm text-slate-500">
                                                    from {{ action.from }}
                                                </p>
                                            </div>
                                            <div v-if="action.to" class="flex items-center">
                                                <p class="text-sm text-slate-500">
                                                    to {{ action.to }}
                                                </p>
                                            </div>
                                        </div>
                                        <div>
                                            <actions-handle :dark-mode="darkMode" :no-delete-button="true"
                                                @edit="toggleNoteModal(true, action)" @click.stop />
                                        </div>
                                    </div>
                                </div>
                                <div class="px-5 pt-2" v-if="openedActivities.includes(action.id)">
                                    <p class="text-sm border-l border-primary-500 pl-3"
                                        :class="{'text-grey-500': !darkMode, 'text-grey-300': darkMode}"
                                        v-html="$filters.scrubHtml(action.message)">
                                    </p>
                                    <p class="text-sm mt-4" v-if="action.tags.length > 0">
                                        <span class="font-semibold">Tagged:</span>
                                        <span v-for="(tagId, index) in action.tags" :key="index" class="text-slate-500">
                                            @{{ getTaggedUserById(tagId) }}
                                            <span v-if="index < action.tags.length - 1">, </span>
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </template>
            <div v-else class="flex items-center justify-center h-full">
                <loading-spinner :dark-mode="darkMode"></loading-spinner>
            </div>
        </div>
        <div v-if="paginationData && paginationData.to" class="absolute bottom-0 inset-x-0 p-3 border-t" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
            <Pagination :dark-mode="darkMode" :pagination-data="paginationData" :show-pagination="true"
                @change-page="handlePaginationEvent"></Pagination>
        </div>

        <action-note-modal v-if="showNoteModal" :dark-mode="darkMode" :action="editAction" :company-id="companyId"
            :company-name="companyName" :task-id="task?.id || 0" @close="toggleNoteModal(false)"
            @reload-actions="getActions" />

        <Modal :dark-mode="darkMode" v-if="showFilterConfigModal" @close="showFilterConfigModal = false" @confirm="search" small close-text="Cancel" confirm-text="Search">
            <template #header>
                Activity Filters
            </template>
            <template #content>
                <div class="flex items-center mb-4" v-if="showCategoryFilter">
                    <div class="grid grid-cols-2 w-full gap-5">
                        <div v-if="showCategoryFilter">
                            <p class="font-semibold text-sm mb-2">Categories</p>
                            <MultiSelect :dark-mode="darkMode" :options="categoryOptions" :selected-ids="categoryIds"
                                         :text-place-holder="'Select Category'" />
                        </div>
                    </div>
                </div>
                <div class="flex items-center" v-if="showFilters">
                    <div class="grid grid-cols-2 w-full gap-5">
                        <div>
                            <p class="font-semibold text-sm mb-2">Users</p>
                            <Dropdown :dark-mode="darkMode" class="mr-2" :options="users" v-model="selectedFilterUser" />
                        </div>
                        <div>
                            <p class="font-semibold text-sm mb-2">Contacts</p>
                            <Dropdown :dark-mode="darkMode" class="mr-2" :options="companyContactsWithActions"
                                      v-model="selectedFilterContact" />
                        </div>
                    </div>
                </div>
            </template>
        </Modal>
    </div>
</template>

<script>
import Dropdown from "../components/Dropdown.vue";
import SharedApiService from "../services/api";
import LoadingSpinner from "../components/LoadingSpinner.vue";
import Modal from "../components/Modal.vue";
import ApiService from "../../Tasks/services/api";
import Pagination from "../components/Pagination.vue";
import MarkdownEditorMixin from "../mixins/markdown-editor-mixin";
import AlertsMixin from "../../../mixins/alerts-mixin";
import AlertsContainer from "../components/AlertsContainer.vue";
import ActionsHandle from "../components/ActionsHandle.vue";
import ActionNoteModal from "../components/ActionNoteModal.vue";
import CustomInput from "../components/CustomInput.vue";
import MultiSelect from "../components/MultiSelect.vue";
import CustomButton from "../components/CustomButton.vue";

export default {
    name: "Activity",
    components: {
        ActionNoteModal,
        ActionsHandle,
        AlertsContainer,
        Dropdown,
        LoadingSpinner,
        Modal,
        Pagination,
        CustomInput,
        MultiSelect,
        CustomButton,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: Number,
            default: null
        },
        showFilters: {
            type: Boolean,
            default: false
        },
        showSearch: {
            type: Boolean,
            default: false
        },
        showCategoryFilter: {
            type: Boolean,
            default: false
        },
        task: {
            type: Object,
            default: {}
        },
        tableHeight: {
            type: String,
            default: 'h-100'
        },
        companyName: {
            type: String,
            default: ''
        },
        categoryOptions: {
            type: Array,
            default: () => [{id: '', name: 'All'}, {id: 'company', name: 'Company'}]
        }
    },
    mixins: [ MarkdownEditorMixin, AlertsMixin ],
    data() {
        return {
            expandActivity: false,
            openedActivities: [],
            api: SharedApiService.make(),
            taskApi: ApiService.make(),
            paginationData: null,
            actions: [],
            activityActions: [
                {event: 'add-action', name: 'Add Action'},
            ],
            showFilterConfigModal: false,
            loading: false,
            companyContacts: [],
            users: [],
            contactsFilterOptions: [
                {id: 'all', name: 'All Contacts'},
                {id: 'notes', name: 'Contacts w/ Notes'}
            ],

            showNoteModal: false,

            selectedFilterUser: 0,
            selectedFilterContact: '',

            editAction: null,
            saving: false,
            mouseOverPin: null,

            errorMessages: {
                pinAction: 'An unknown error occurred while pinning an Action.'
            },

            searchValue: "",
            categoryIds: [],
        }
    },
    created() {
        this.getUsers();

        if (this.companyId) {
            this.getActions();
            this.getCompanyContacts();
        }
    },
    watch: {
        companyId(newVal, oldVal) {
            if(newVal !== oldVal) {
                this.getActions();
                this.getCompanyContacts();
            }
        },
        selectedFilterUser(newVal, oldVal) {
            if(newVal !== oldVal) {
                this.$forceUpdate();
            }
        },
        selectedFilterContact(newVal, oldVal) {
            if(newVal !== oldVal) {
                this.$forceUpdate();
            }
        }
    },
    computed: {
        companyContactsWithActions: function() {
            let companyContactsWithActions = [];
            let added = [];

            this.actions.forEach((action) => {
                if(!action.to_company
                && !added.includes(action.to)) {
                    companyContactsWithActions.push({
                        id: action.to,
                        name: action.to
                    });

                    added.push(action.to);
                }
            });

            return [{id: '', name: 'All'}, {id: 'company', name: 'Company'}, ...companyContactsWithActions];
        }
    },
    methods: {
        toggleExpandActivity(id) {
            if(this.openedActivities.includes(id))
                this.openedActivities.splice(this.openedActivities.indexOf(id), 1);
            else
                this.openedActivities.push(id);
        },
        getActions(params = {}) {
            this.loading = true;
            this.api.getCompanyActions(this.companyId, params).then(resp => this.addPaginatedData(resp))
                .finally(() => this.loading = false);
        },
        getCompanyContacts() {
            this.api.getCompanyContacts(this.companyId).then(resp => this.companyContacts = resp.data.data.contacts);
        },
        getUsers() {
            this.api.getUsers().then(resp => {
                this.users.push({
                    id: 0,
                    name: 'All'
                });

                resp.data.data.users.forEach((user) => {
                    this.users.push({
                        id: user.id,
                        name: user.name
                    });
                });
            });
        },
        checkFilters(action) {
            if(this.selectedFilterUser
            && action.from_user_id !== this.selectedFilterUser) {
                return false;
            }

            if(this.selectedFilterContact.length) {
                if(this.selectedFilterContact === 'company'
                && !action.to_company) {
                    return false;
                }
                else if(this.selectedFilterContact !== 'company'
                    && action.to !== this.selectedFilterContact) {
                    return false;
                }
            }

            return true;
        },
        toggleNoteModal(show, action = null) {
            this.editAction = show && action ? action : null;
            this.showNoteModal = show;
        },
        async handlePaginationEvent(newPageUrl, params = {}) {
            this.loading = true;

            if (this.searchValue) {
                params.search = this.searchValue;
            }

            if (this.categoryIds.length) {
                params.categories = this.categoryIds;
            }

            await axios.get(newPageUrl.link, params).then(resp => this.addPaginatedData(resp))
                .finally(() => this.loading = false);
        },
        addPaginatedData(resp) {
            if(resp.data.data.status === true) {
                let {data, ...paginationData} = resp.data.data.actions;
                this.actions = data;
                this.paginationData = paginationData;
            }
        },
        toggleActionPinned(actionId) {
            this.api.toggleActionPin(actionId, this.companyId).then(resp => {
                if (!resp.data?.data?.status) throw new Error();
            }).catch(err => {
                this.showAlert(err.response?.data?.message || this.errorMessages.pinAction);
            }).finally(() => this.getActions());
        },
        getTaggedUserById(userId) {
            const user = this.users.find(user => user.id === userId);
            return user ? user.name : "Unknown User";
        },
        search() {
            const params = {};

            if (this.searchValue) {
                params.search = this.searchValue;
            }

            if (this.categoryIds.length) {
                params.categories = this.categoryIds;
            }

            this.getActions(params);

            if(this.showFilterConfigModal)
                this.showFilterConfigModal = false;
        }
    }
}
</script>

<style scoped>

</style>
