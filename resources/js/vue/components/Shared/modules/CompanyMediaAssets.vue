<template>
    <div
        class="flex items-center font-bold mb-3 ml-4"
        :class="[darkMode ? 'text-grey-50' : 'text-grey-600']"
    >
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
             stroke="currentColor" class="w-6 mr-2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6.827 6.175A2.31 2.31 0 015.186 7.23c-.38.054-.757.112-1.134.175C2.999 7.58 2.25 8.507 2.25 9.574V18a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9.574c0-1.067-.75-1.994-1.802-2.169a47.865 47.865 0 00-1.134-.175 2.31 2.31 0 01-1.64-1.055l-.822-1.316a2.192 2.192 0 00-1.736-1.039 48.774 48.774 0 00-5.232 0 2.192 2.192 0 00-1.736 1.039l-.821 1.316z" />
            <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 12.75a4.5 4.5 0 11-9 0 4.5 4.5 0 019 0zM18.75 10.5h.008v.008h-.008V10.5z" />
        </svg>

        <h5 class="font-semibold">Profile Media</h5>
    </div>

    <div
        v-if="loading"
        class="h-full flex items-center justify-center"
    >
        <loading-spinner :dark-mode="darkMode" />
    </div>
    <div
        v-if="isMediaSetup()"
        class="relative grid grid-cols-2 md:grid-cols-4 gap-2 min-h-[10rem] overflow-hidden mx-4 my-5"
        :class="[darkMode ? 'text-grey-300' : 'text-grey-600']"
    >
        <div
            v-for="mediaAsset in mediaAssets"
            class="relative aspect-square border rounded-lg p-4 bg-cover bg-center flex items-center justify-center"
            :class="[darkMode ? 'bg-dark-background border-dark-border' : 'bg-light-background  border-light-border']"
        >
            <img
                class="font-semibold text-sm truncate"
                :src="prepareMediaThumbnailImage(mediaAsset)"
                :alt="prepareMediaThumbnailAlt(mediaAsset)"
            />
            <div class="absolute right-0 top-0 mt-2 mr-1">
                <ActionsHandle
                    :dark-mode="darkMode"
                    :custom-actions="customActions"
                    :no-custom-action="false"
                    :no-edit-button="true"
                    :no-delete-button="true"
                    @preview-media="initiatePreviewMediaAsset(mediaAsset)"
                    @delete-media="initiateDeletingMediaAsset(mediaAsset)"
                />
            </div>
        </div>
    </div>
    <div
        v-if="!loading"
        class="mt-3 ml-4"
    >
        <button
            v-if="hasEditRights"
            class="text-primary-500 text-sm font-semibold rounded-md px-5 py-2.5"
            :class="[darkMode ? 'bg-dark-background' : 'bg-primary-50 hover:bg-primary-100']"
            @click="initiateUploadingMediaAsset()"
        >
            + Add Media Asset
        </button>
        <button
            v-if="hasEditRights"
            class="text-primary-500 text-sm font-semibold rounded-md px-5 py-2.5 ml-4"
            :class="[darkMode ? 'bg-dark-background' : 'bg-primary-50 hover:bg-primary-100']"
            @click="initiateAddingYoutubeAsset()"
        >
            +  Add YouTube Link
        </button>
    </div>
    <Modal
        v-if="showDeleteMediaAssetModal"
        :small="true"
        @confirm="deleteMediaAsset"
        @close="haltDelete"
        :dark-mode="darkMode"
        close-text="No, get back"
        :confirm-text="getDeleteModalConfirmationText()"
    >
        <template v-slot:header>
            <p class="font-medium">
                Delete {{ getMediaTypeMapping(deleteMedia.type) }}
            </p>
        </template>
        <template v-slot:content>
            Are you sure you wish to delete the {{ getMediaTypeMapping(deleteMedia.type) }}?
        </template>
    </Modal>
    <Modal
        v-if="showUploadMediaAssetModal"
        :small="true"
        @confirm="saveMediaAssets"
        @close="haltUploadingMediaAsset(true)"
        :dark-mode="darkMode"
        :close-text="'Cancel'"
        :confirm-text="getUploadModalConfirmationText()"
    >
        <template v-slot:header>
            <p class="font-medium">
                Add Media Asset
            </p>
        </template>
        <template v-slot:content>
            <p>
                Media files will be displayed on the company profile page. However, all uploaded media
                will be reviewed by the team before being displayed.
            </p>

            <p
                class="mt-6 italic text-sm"
                :class="[darkMode ? 'text-grey-400' : 'text-grey-600']"
            >
                Permitted file format: jpg, jpeg or png. Files must be less than 8MB.<br>
                Please note that media cannot contain contact details.
            </p>

            <div class="mt-10">
                <file-upload
                    :dark-mode="darkMode"
                    :accept="permittedFileTypes"
                    :multi="true"
                    @file-uploaded="getFiles"
                    @file-upload-error="fileUploadError"
                />
            </div>

            <div class="mt-10">
                <p
                    class="text-sm"
                    :class="[darkMode ? 'text-grey-400' : 'text-grey-600']"
                >
                    By uploading files, you ensure that the company is either the owner of the
                    copyright of the files or has authorization from the owner of the copyright to use these files to
                    display on their website.
                </p>
            </div>
        </template>
    </Modal>
    <Modal
        v-if="showAddYoutubeLinkModal"
        :small="true"
        @confirm="saveYoutubeLink"
        @close="haltAddingYoutubeLink(true)"
        :dark-mode="darkMode"
        :close-text="'Cancel'"
        :confirm-text="getAddLinkModalConfirmationText()"
    >
        <template v-slot:header>
            <p class="font-medium">
                Add Youtube Link
            </p>
        </template>
        <template v-slot:content>
            <p
                class="mt-6 italic text-sm"
                :class="[darkMode ? 'text-grey-400' : 'text-grey-600']"
            >
                Permitted link format: https://youtube.com/watch?v=
            </p>
            <div class="mt-6">
                <input
                    class="rounded border font-medium text-sm h-9 w-full"
                    :class="[darkMode ? 'bg-dark-background border-dark-border text-slate-200' : 'bg-light-background border-light-border text-slate-900']"
                    placeholder="https://youtube.com/watch?v="
                    type="text"
                    v-model="youtubeLink"
                />
            </div>
            <div class="mt-10">
                <p
                    class="text-sm"
                    :class="[darkMode ? 'text-grey-400' : 'text-grey-600']"
                >
                    By adding link, you ensure that the company is either the owner of the
                    copyright of the video/link or has authorization from the owner of the copyright to use this link to
                    display on their website.
                </p>
            </div>
        </template>
    </Modal>
    <MediaPreview
        v-if="showPreview"
        :dark-mode="darkMode"
        :media-asset="previewMedia"
        @modalClosureRequested="haltPreview"
    />
    <alerts-container
        v-if="alertActive"
        :dark-mode="darkMode"
        :alert-type="alertType"
        :text="alertText"
    />
</template>

<script>
import CustomButton from "../components/CustomButton.vue";
import Modal from "../components/Modal.vue";
import LoadingSpinner from "../components/LoadingSpinner.vue";
import SharedApiService from "../services/api";
import AlertsMixin from "../../../mixins/alerts-mixin";
import AlertsContainer from "../components/AlertsContainer.vue";
import FileUpload from "../../Inputs/FileUpload.vue";
import ActionsHandle from "../components/ActionsHandle.vue";
import DeleteButtonConfirmation from "../components/DeleteButtonConfirmation.vue";
import { REQUEST } from "../../../../constants/APIRequestKeys";
import MediaPreview from "../components/MediaPreview.vue";

export default {
    name: "CompanyMediaAssets",
    components: {
        MediaPreview,
        Modal,
        FileUpload,
        CustomButton,
        LoadingSpinner,
        AlertsContainer,
        ActionsHandle,
        DeleteButtonConfirmation,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        hasEditRights: {
            type: Boolean,
            default: false,
        },
        companyId: {
            type: Number,
            default: null,
        },
    },
    mixins: [
        AlertsMixin,
    ],
    data() {
        return {
            apiService: SharedApiService.make(),
            showUploadMediaAssetModal: false,
            showAddYoutubeLinkModal: false,
            showDeleteMediaAssetModal: false,
            showPreview: false,
            loading: false,
            updating: false,
            saving: false,
            files: [],
            youtubeLink: null,
            mediaAssets: [],
            errorOccurred: false,
            errorMessages: {
                retrievalError: `An error occurred while fetching the media assets.`,
                savingError: `An error occurred while adding the media asset.`,
                removalError: `An error occurred while deleting the media asset.`,
            },
            permittedFileTypes: '.jpg,.jpeg,.png',
            deleteMedia: null,
            mediaAssetType: {
                media: 'media',
                link: 'link',
            },
            defaultImage: '',
            previewMedia: null,
            customActions: [
                {event: 'preview-media', name: 'View'},
                {event: 'delete-media', name: 'Delete'},
            ],
        }
    },
    created() {
        if(this.companyId) {
            this.getMediaAssets();
        }
    },
    methods: {
        initiateUploadingMediaAsset() {
            this.showUploadMediaAssetModal = true;
            this.errorOccurred = false;
        },
        initiateAddingYoutubeAsset() {
            this.showAddYoutubeLinkModal = true;
            this.errorOccurred = false;
        },
        initiateDeletingMediaAsset(mediaAsset) {
            this.showDeleteMediaAssetModal = true;
            this.deleteMedia = mediaAsset;
        },
        initiatePreviewMediaAsset(mediaAsset) {
            this.showPreview = true;
            this.previewMedia = mediaAsset;
        },
        haltDelete() {
            this.showDeleteMediaAssetModal = false;
            this.deleteMedia = null;
            this.saving = false;
        },
        haltUploadingMediaAsset(modalClosureRequested = false) {
            this.loading = false;
            this.saving = false;
            this.files = [];

            if(modalClosureRequested === true || this.errorOccurred === false) {
                this.showUploadMediaAssetModal = false;
            } else {
                this.showUploadMediaAssetModal = true;
            }
        },
        haltAddingYoutubeLink(modalClosureRequested = false) {
            this.loading = false;
            this.saving = false;
            this.youtubeLink = null;

            if(modalClosureRequested === true || this.errorOccurred === false) {
                this.showAddYoutubeLinkModal = false;
            } else {
                this.showAddYoutubeLinkModal = true;
            }
        },
        haltPreview() {
            this.showPreview = false;
            this.previewMedia = null;
        },
        getMediaAssets() {
            if(this.loading) {
                return;
            }

            this.loading = true;
            this.apiService.getCompanyMediaAssets(this.companyId)
                .then(resp => {
                    if (resp.data?.data?.status === true) {
                        this.mediaAssets = resp.data.data[REQUEST.MEDIA_ASSETS];
                        return;
                    }
                    this.handleError(resp?.data?.data?.msg || this.errorMessages.retrievalError);
                })
                .catch(e => this.handleError(e.response.data.message || this.errorMessages.retrievalError, e))
                .finally(() => this.loading = false);

        },
        saveMediaAssets() {
            if(this.files.length === 0 || this.saving === true) {
                return;
            }

            this.errorOccurred = false;
            this.saving = true;
            const formData = new FormData();

            for(const file of this.files) {
                formData.append([REQUEST.MEDIA_ASSETS]+'[]', file);
            }

            this.apiService.saveCompanyMediaAssets(this.companyId, formData)
                .then(resp => {
                    if (resp.data?.data?.status === true) {
                        for(const mediaAsset of resp.data.data[REQUEST.MEDIA_ASSETS]) {
                            if(mediaAsset !== null && mediaAsset.id !== undefined) {
                                this.mediaAssets.push(mediaAsset);
                            }
                        }
                        return;
                    }
                    this.handleError(resp?.data?.data?.msg || this.errorMessages.savingError);
                })
                .catch(e => this.handleError(e.response.data.message || this.errorMessages.savingError, e))
                .finally(() => this.haltUploadingMediaAsset());
        },
        saveYoutubeLink() {
            if(this.youtubeLink === null) {
                this.handleError('Permitted link format: "https://www.youtube.com/watch?v=CANocXrAyk8"');
                return;
            }

            if(this.saving === true) {
                return;
            }

            this.errorOccurred = false;
            this.saving = true;

            this.apiService.saveCompanyYoutubeLink(this.companyId, this.youtubeLink)
                .then(resp => {
                    if (resp.data?.data?.status === true && resp.data.data[REQUEST.YOUTUBE_ASSET]?.id) {
                        this.mediaAssets.push(resp.data.data[REQUEST.YOUTUBE_ASSET]);
                        return;
                    }
                    this.handleError(resp?.data?.data?.error || resp?.data?.data?.msg || this.errorMessages.savingError);
                })
                .catch(e => this.handleError(e.response.data.message || this.errorMessages.savingError, e))
                .finally(() => this.haltAddingYoutubeLink());
        },
        deleteMediaAsset() {
            if(this.deleteMedia?.id === null || this.saving === true) {
                return;
            }

            this.saving = true;
            this.apiService.deleteCompanyMediaAsset(this.companyId, this.deleteMedia.id)
                .then(resp => {
                    if (resp.data?.data?.status === true) {
                        this.getMediaAssets();
                        return;
                    }
                    this.handleError(resp?.data?.data?.msg || this.errorMessages.removalError);
                })
                .catch(e => this.handleError(e.response.data.message || this.errorMessages.removalError, e))
                .finally(() => this.haltDelete());
        },
        getFiles(payload) {
            this.files = payload;
        },
        isMediaSetup() {
            return this.mediaAssets && this.mediaAssets.length > 0;
        },
        getUploadModalConfirmationText() {
            return !this.saving
                ? 'Create'
                : 'Uploading...'
        },
        getAddLinkModalConfirmationText() {
            return !this.saving
                ? 'Add'
                : 'Adding...'
        },
        getDeleteModalConfirmationText() {
            return !this.saving
                ? 'Yes, continue'
                : 'Deleting...'
        },
        getYoutubeLink(url) {
            return url.match(/v=([0-z]+)/)?.[1];
        },
        getYoutubeThumbnail(url) {
            const videoId = this.getYoutubeLink(url);
            return videoId
                ? `https://img.youtube.com/vi/${videoId}/0.jpg`
                : this.defaultImage;
        },
        prepareMediaThumbnailImage(mediaAsset) {
            return mediaAsset.type === this.mediaAssetType.link
                ? this.getYoutubeThumbnail(mediaAsset.url)
                : mediaAsset.url;
        },
        prepareMediaThumbnailAlt(mediaAsset) {
            const alt = `uploaded ${mediaAsset.type}`;
            return mediaAsset.name.length > 0
                ? `${alt} - ${mediaAsset.name}`
                : alt;
        },
        getMediaTypeMapping(type) {
            return type === this.mediaAssetType.link
                ? 'link'
                : 'file';
        },
        handleError(errorMessage, caughtException = null) {
            this.errorOccurred = true;

            this.showAlert('error', errorMessage);

            if(caughtException) {
                console.error(errorMessage, caughtException);
            }
        },
        fileUploadError(errorMessage) {
            this.handleError(errorMessage);
        },
    },
}
</script>

<style scoped>

</style>
