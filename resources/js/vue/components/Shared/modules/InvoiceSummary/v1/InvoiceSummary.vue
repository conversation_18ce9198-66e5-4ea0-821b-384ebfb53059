<template>
    <div class="flex gap-4">
        <div class="flex flex-col">
            <company-credits-card
                class="min-w-[20rem]"
                v-if="companyBilling.version === companyBilling.VERSIONS.V2"
                :company-id="companyId"
                :dark-mode="darkMode"
            />
        </div>
        <div class="border rounded-lg"
             :class="{'bg-light-module border-light-border text-slate-900': !darkMode, 'bg-dark-module border-dark-border text-slate-100': darkMode}">
            <div class="grid grid-cols-1 xl:grid-cols-3 2xl:grid-cols-7 xl:divide-x"
                 :class="[darkMode ? 'divide-dark-border' : 'divide-light-border']">
                <div class="2xl:col-span-2">
                    <div class="relative p-4">
                        <div class="flex gap-2 items-center">
                            <div>Total Available Credit</div>
                            <version-badge version="v1" v-if="companyBilling.version === companyBilling.VERSIONS.V2" :dark-mode="darkMode"/>
                        </div>
                        <p class="text-lg font-semibold text-green-500">{{ $filters.currency(availableCredit) }}</p>
                        <div class="absolute right-0 top-0 mt-2 mr-3">
                            <ActionsHandle :dark-mode="darkMode" @edit="toggleCreditModal" @delete="toggleVoucherModal"
                                           :action-text1="'Manage Credit'" :action-text2="'Manage Voucher'"
                                           :width="'w-64'">
                                <template #icon>
                                    <simple-icon
                                        clickable
                                        :dark-mode="darkMode"
                                        :icon="simpleIcon.icons.PENCIL_SQUARE"
                                        :color="simpleIcon.colors.BLUE"
                                    />
                                </template>
                            </ActionsHandle>
                        </div>
                    </div>
                    <div class="flex border-t gap-2 flex-wrap p-2">
                        <badge color="green" :dark-mode="darkMode">
                            <div class="flex">
                                Credit: {{ $filters.currency(amountCredit) }}
                            </div>
                        </badge>
                        <badge color="green" :dark-mode="darkMode">
                            <div class="flex">
                                Vouchers: {{ $filters.currency(amountVouchers) }}
                            </div>
                        </badge>
                        <badge color="green" :dark-mode="darkMode">
                            <div class="flex">
                                Signup Bonus: {{ $filters.currency(amountSignupBonus) }}
                            </div>
                        </badge>
                    </div>
                </div>
                <div v-for="summaryField in summaryFields" class="p-4 flex items-center">
                    <div>
                        <p class="">{{ summaryField.title }}</p>
                        <div class="flex gap-2 items-center flex-wrap">
                            <div class="font-semibold" :class="summaryField.valueClass">
                                {{ $filters.currency(legacyInvoiceData[summaryField.legacyField] ?? 0) }}
                            </div>
                            <VersionBadge version="v1" v-if="companyBilling.version === companyBilling.VERSIONS.V2" :dark-mode="darkMode"/>
                        </div>
                        <div v-if="companyBilling.version === companyBilling.VERSIONS.V2" class="flex gap-2 items-center mt-1 flex-wrap">
                            <div class="font-semibold" :class="summaryField.valueClass">
                                {{ $filters.centsToFormattedDollars(invoiceData[summaryField.field] ?? 0) }}
                            </div>
                            <VersionBadge :dark-mode="darkMode"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <Modal :dark-mode="darkMode" :no-buttons="true" :small="true" v-if="creditModal" @close="toggleCreditModal">
            <template v-slot:header>
                <p class="font-semibold">Manage Credit</p>
            </template>
            <template v-slot:content>
                <div v-if="!showCancelCredit" class="grid grid-cols-1 min-w-full max-w-sm gap-4">
                    <button @click="toggleCancelCredit"
                            class="transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-semibold focus:outline-none py-2 rounded-md px-5">
                        Cancel Credit
                    </button>
                </div>
                <div v-if="showCancelCredit" class="grid grid-cols-1 min-w-full max-w-sm gap-4">
                    <div class="border-b" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
                        <div class="relative p-4">
                            <p class="">Available Credit</p>
                            <p class="text-lg font-semibold text-green-500">{{ $filters.currency(availableCredit) }}</p>
                        </div>
                        <div class="w-full grid grid-cols-3 md:divide-x md:border-t"
                             :class="[darkMode ? 'divide-dark-border border-dark-border' : 'divide-light-border border-light-border']">
                            <div class="px-4 py-2">
                                <p class="text-slate-500 text-sm">Credit: {{ $filters.currency(amountCredit) }}</p>
                            </div>
                            <div class="px-4 py-2">
                                <p class="text-slate-500 text-sm">Vouchers: {{ $filters.currency(amountVouchers) }}</p>
                            </div>
                            <div class="px-4 py-2">
                                <p class="text-slate-500 text-sm">Signup Bonus: {{ $filters.currency(amountSignupBonus) }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="p-4">
                        <p class="font-semibold mb-4 text-lg">Cancel Credit</p>
                        <div class="space-y-4 sm:flex sm:items-center sm:space-y-0 sm:space-x-10 pb-4"
                             :class="[!darkMode ? 'text-grey-900' : 'text-white']">
                            <div class="flex gap-5 pr-5 items-center">
                                <div class="flex items-center mt-3">
                                    <input id="cancel-credit-date" name="cancel-credit-date" type="radio"
                                           :checked="dateToCancelCredit === 'today'" value="today"
                                           class="focus:ring-blue-550 cursor-pointer h-5 w-5 text-blue-550 border-gray-300"/>
                                    <label :class="[!darkMode ? 'text-grey-900' : 'text-white']"
                                           for="report-mode-single" class="ml-2 block font-medium">
                                        Today
                                    </label>
                                </div>
                                <div class="flex items-center mt-3">
                                    <input id="cancel-credit-date" name="cancel-credit-date" type="radio"
                                           :checked="dateToCancelCredit === 'schedule'" value="scheduled"
                                           class="focus:ring-blue-550 cursor-pointer h-5 w-5 text-blue-550 border-gray-300"/>
                                    <label :class="[!darkMode ? 'text-grey-900' : 'text-white']"
                                           class="ml-2 block font-medium">
                                        Schedule Date
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div v-if="dateToCancelCredit === 'schedule'">
                            <div class="w-80">
                                <Datepicker :dark="darkMode"/>
                            </div>
                        </div>
                        <div class="flex items-center mt-4">
                            <p class="font-semibold mr-2 text-lg">$</p>
                            <div class="grid grid-cols-2 gap-3">
                                <input
                                    class="w-full border rounded px-3 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                                    placeholder="Amount"
                                    :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}"/>
                                <button
                                    class="w-full transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-semibold focus:outline-none py-2 rounded-md px-5">
                                    Apply Credit Cancellation
                                </button>
                            </div>
                        </div>

                    </div>
                </div>
            </template>
        </Modal>
        <Modal :dark-mode="darkMode" :no-buttons="true" :small="true" v-if="voucherModal" @close="toggleVoucherModal">
            <template v-slot:header>
                <p class="font-semibold">Manage Voucher</p>
            </template>
            <template v-slot:content>
                <div v-if="!showCancelVoucher && !showAddVoucher" class="grid grid-cols-1 min-w-full max-w-sm gap-4">
                    <button @click="toggleCancelVoucher"
                            class="transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-semibold focus:outline-none py-2 rounded-md px-5">
                        Cancel Voucher
                    </button>
                    <button @click="toggleAddVoucher"
                            class="transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-semibold focus:outline-none py-2 rounded-md px-5">
                        Add Voucher
                    </button>
                </div>
                <div v-if="showCancelVoucher" class="grid grid-cols-1 min-w-full max-w-sm gap-4">
                    <div class="border-b" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
                        <div class="relative p-4">
                            <p class="">Available Credit</p>
                            <p class="text-lg font-semibold text-green-500">{{ $filters.currency(availableCredit) }}</p>
                        </div>
                        <div class="w-full grid grid-cols-3 md:divide-x md:border-t"
                             :class="[darkMode ? 'divide-dark-border border-dark-border' : 'divide-light-border border-light-border']">
                            <div class="px-4 py-2">
                                <p class="text-slate-500 text-sm">Credit: {{ $filters.currency(amountCredit) }}</p>
                            </div>
                            <div class="px-4 py-2">
                                <p class="text-slate-500 text-sm">Vouchers: {{ $filters.currency(amountVouchers) }}</p>
                            </div>
                            <div class="px-4 py-2">
                                <p class="text-slate-500 text-sm">Signup Bonus: {{ $filters.currency(amountSignupBonus) }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="p-4">
                        <p class="font-semibold mb-4 text-lg">Cancel Voucher</p>
                        <div class="space-y-4 sm:flex sm:items-center sm:space-y-0 sm:space-x-10 pb-4"
                             :class="[!darkMode ? 'text-grey-900' : 'text-white']">
                            <div class="flex gap-5 pr-5 items-center">
                                <div class="flex items-center mt-3">
                                    <input id="cancel-credit-date" name="cancel-credit-date" type="radio"
                                           :checked="dateToCancelCredit === 'today'" value="today"
                                           class="focus:ring-blue-550 cursor-pointer h-5 w-5 text-blue-550 border-gray-300"/>
                                    <label :class="[!darkMode ? 'text-grey-900' : 'text-white']"
                                           for="report-mode-single" class="ml-2 block font-medium">
                                        Today
                                    </label>
                                </div>
                                <div class="flex items-center mt-3">
                                    <input id="cancel-credit-date" name="cancel-credit-date" type="radio"
                                           :checked="dateToCancelCredit === 'schedule'" value="scheduled"
                                           class="focus:ring-blue-550 cursor-pointer h-5 w-5 text-blue-550 border-gray-300"/>
                                    <label :class="[!darkMode ? 'text-grey-900' : 'text-white']"
                                           class="ml-2 block font-medium">
                                        Schedule Date
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div v-if="dateToCancelCredit === 'schedule'">
                            <div class="w-80">
                                <Datepicker :dark="darkMode"/>
                            </div>
                        </div>
                        <div class="flex items-center mt-4">
                            <p class="font-semibold mr-2 text-lg">$</p>
                            <div class="grid grid-cols-2 gap-3">
                                <input
                                    class="w-full border rounded px-3 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                                    placeholder="Amount"
                                    :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}"/>
                                <button
                                    class="w-full transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-semibold focus:outline-none py-2 rounded-md px-5">
                                    Apply Voucher Cancellation
                                </button>
                            </div>
                        </div>

                    </div>
                </div>
                <div v-if="showAddVoucher" class="grid grid-cols-1 min-w-full max-w-sm gap-4">
                    <div class="border-b" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
                        <div class="relative p-4">
                            <p class="">Available Credit</p>
                            <p class="text-lg font-semibold text-green-500">{{ $filters.currency(availableCredit) }}</p>
                        </div>
                        <div class="w-full grid grid-cols-3 md:divide-x md:border-t"
                             :class="[darkMode ? 'divide-dark-border border-dark-border' : 'divide-light-border border-light-border']">
                            <div class="px-4 py-2">
                                <p class="text-slate-500 text-sm">Credit: {{ $filters.currency(amountCredit) }}</p>
                            </div>
                            <div class="px-4 py-2">
                                <p class="text-slate-500 text-sm">Vouchers: {{ $filters.currency(amountVouchers) }}</p>
                            </div>
                            <div class="px-4 py-2">
                                <p class="text-slate-500 text-sm">Signup Bonus: {{ $filters.currency(amountSignupBonus) }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="p-4">
                        <p class="font-semibold mb-4 text-lg">Add Voucher</p>
                        <div class="space-y-4 sm:flex sm:items-center sm:space-y-0 sm:space-x-10 pb-4"
                             :class="[!darkMode ? 'text-grey-900' : 'text-white']">
                            <div class="flex gap-5 pr-5 items-center">
                                <div class="flex items-center mt-3">
                                    <input id="cancel-credit-date" name="cancel-credit-date" type="radio"
                                           :checked="dateToCancelCredit === 'today'" value="today"
                                           class="focus:ring-blue-550 cursor-pointer h-5 w-5 text-blue-550 border-gray-300"/>
                                    <label :class="[!darkMode ? 'text-grey-900' : 'text-white']"
                                           for="report-mode-single" class="ml-2 block font-medium">
                                        Today
                                    </label>
                                </div>
                                <div class="flex items-center mt-3">
                                    <input id="cancel-credit-date" name="cancel-credit-date" type="radio"
                                           :checked="dateToCancelCredit === 'schedule'" value="scheduled"
                                           class="focus:ring-blue-550 cursor-pointer h-5 w-5 text-blue-550 border-gray-300"/>
                                    <label :class="[!darkMode ? 'text-grey-900' : 'text-white']"
                                           class="ml-2 block font-medium">
                                        Schedule Date
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div v-if="dateToCancelCredit === 'schedule'">
                            <div class="w-80">
                                <Datepicker :dark="darkMode"/>
                            </div>
                        </div>
                        <div class="flex items-center mt-4">
                            <p class="font-semibold mr-2 text-lg">$</p>
                            <div class="grid grid-cols-2 gap-3">
                                <input
                                    class="w-full border rounded px-3 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                                    placeholder="Amount"
                                    :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}"/>
                                <button
                                    class="w-full transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-semibold focus:outline-none py-2 rounded-md px-5">
                                    Apply Voucher
                                </button>
                            </div>
                        </div>

                    </div>
                </div>
            </template>
        </Modal>
    </div>
</template>

<script>
import ActionsHandle from "../../../../Shared/components/ActionsHandle.vue";
import Modal from "../../../../Shared/components/Modal.vue";
import Datepicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';
import ApiService from "../../../services/api.js";
import CompanyCreditsCard from "../../CompanyCredits/CompanyCreditsCard.vue";
import {useCompanyBillingStore} from "../../../../../../stores/billing/company-billing.js";
import VersionBadge from "../../../../Billing/VersionBadge.vue";
import Badge from "../../../components/Badge.vue";
import SimpleIcon from "../../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../../composables/useSimpleIcon.js";
const simpleIcon = useSimpleIcon();
const summaryFields = [
    {
        title: 'Total Outstanding',
        valueClass: 'text-red-500',
        legacyField: 'unpaid',
        field: 'unpaid'
    },
    {
        title: 'Total Paid',
        valueClass: '',
        legacyField: 'paid',
        field: 'paid'
    },
    {
        title: 'Total To Date',
        valueClass: '',
        legacyField: 'total',
        field: 'total'
    },
    {
        title: 'Outstanding Leads',
        valueClass: '',
        legacyField: 'nonrejectable',
        field: 'non_rejectable_outstanding_leads'
    },
    {
        title: 'Total To Be Invoiced',
        valueClass: '',
        legacyField: 'tobeinvoiced',
        field: 'outstanding_leads'
    },
]

export default {
    name: "InvoiceSummary",
    components: {SimpleIcon, Badge, VersionBadge, CompanyCreditsCard, ActionsHandle, Modal, Datepicker},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        companyId: {
            type: Number
        }
    },
    data() {
        return {
            apiService: ApiService.make(),
            availableCredit: '0.00',
            amountCredit: '0.00',
            amountVouchers: '0.00',
            amountSignupBonus: '0.00',
            creditModal: false,
            voucherModal: false,
            showCancelCredit: false,
            showCancelVoucher: false,
            showAddVoucher: false,
            dateToCancelCredit: 'today',
            companyBilling: useCompanyBillingStore(),
            legacyInvoiceData: [],
            invoiceData: [],
            summaryFields,
            simpleIcon
        }
    },
    mounted() {
        this.fetchInvoicesSummary();
        this.companyBilling.getBillingVersion(this.companyId)
    },
    methods: {
        async fetchInvoicesSummary() {
            const res = await this.apiService.getCompanyInvoicesSummary(this.companyId);
            const invoicesSummary = res.data.data.invoices_summary.v1;

            this.legacyInvoiceData = invoicesSummary;
            this.invoiceData = res.data.data.invoices_summary?.v2

            this.availableCredit = invoicesSummary.available_credit;
            this.amountCredit = invoicesSummary.credit_amount;
            this.amountVouchers = invoicesSummary.voucher_amount;
            this.amountSignupBonus = invoicesSummary.signup_bonus_amount;
        },
        toggleCreditModal() {
            this.creditModal = !this.creditModal;
            this.showCancelCredit = false;
        },
        toggleVoucherModal() {
            this.voucherModal = !this.voucherModal;
            this.showCancelVoucher = false;
            this.showAddVoucher = false;
        },
        toggleCancelCredit() {
            this.showCancelCredit = !this.showCancelCredit;
        },
        toggleCancelVoucher() {
            this.showCancelVoucher = !this.showCancelVoucher;
        },
        toggleAddVoucher() {
            this.showAddVoucher = !this.showAddVoucher;
        }
    }
}
</script>

<style scoped>

</style>
