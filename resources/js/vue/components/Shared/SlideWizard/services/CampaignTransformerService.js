import { WizardTransformerService } from "./WizardTransformerService.js";
import { CampaignSlide } from "../../../Campaigns/Wizard/stores/future-campaigns.js";

/**
 * Where the payload structure for A2 is not convenient for use in the Wizard slide, add a transformer here
 * These transforms are called when a slide is saved, and stored in futureCampaignStore
 * The wizardStore will retain the data structure used in the Slide
 */
export class CampaignTransformerService extends WizardTransformerService {

    transformers = {
        [CampaignSlide.Location]: this.locationTransformer,
    }

    locationTransformer(inputKey, payload) {
        if (inputKey === 'zip_codes') {
            return Object.values(payload).reduce((output, zipCode) => {
                return zipCode?.id ? [...output, zipCode.id] : output;
            }, []);
        }
        else {
            return payload;
        }
    }

}
