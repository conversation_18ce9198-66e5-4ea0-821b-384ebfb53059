export const ValidTypes = {
    Array: 'array',
    Object: 'object',
    String: 'string',
    Numeric: 'numeric',
    Boolean: 'boolean',
    None: 'none',
}

export const ValidTypeConditions  = {
    Minimum: 'min',
    Maximum: 'max',
    HasKeys: 'hasKeys',
    Nullable: 'nullable',
}

/**
 * Basic frontend validator to mimic Laravel notation
 */
export class WizardValidationService {

    validTypeConditions = {
        [ValidTypes.Array]: {
            [ValidTypeConditions.Minimum]: ValidTypes.Numeric,
            [ValidTypeConditions.Maximum]: ValidTypes.Numeric,
            [ValidTypeConditions.Nullable]: null,
        },
        [ValidTypes.Object]: {
            [ValidTypeConditions.Minimum]: ValidTypes.Numeric,
            [ValidTypeConditions.Maximum]:ValidTypes.Numeric,
            [ValidTypeConditions.Nullable]: null,
            [ValidTypeConditions.HasKeys]: ValidTypes.Array,
        },
        [ValidTypes.String]: {
            [ValidTypeConditions.Minimum]: ValidTypes.Numeric,
            [ValidTypeConditions.Maximum]: ValidTypes.Numeric,
            [ValidTypeConditions.Nullable]: null,
        },
        [ValidTypes.Numeric]: {
            [ValidTypeConditions.Minimum]: ValidTypes.Numeric,
            [ValidTypeConditions.Maximum]: ValidTypes.Numeric,
            [ValidTypeConditions.Nullable]: null,
        },
        [ValidTypes.Boolean]: {
            [ValidTypeConditions.Nullable]: null,
        },
        [ValidTypes.None]: {
            [ValidTypeConditions.Nullable]: null,
        }
    }

    validate(input, inputValue) {
        if (!input.validation) return { valid: true }
        const { name, validation } = input;
        const errorBag = [];
        const validator = this.processValidator(validation);

        if (inputValue == null && !validator.nullable)
            errorBag.push(this.getErrorMessage('generic', 'nullable', name));
        else if (!this.typeCheck(this.cleanType(inputValue, validator.type), validator.type))
            errorBag.push(this.getErrorMessage('generic', 'badType', name, validator.type));
        else {
            for (const condition of validator.conditions) {
                const validationFunction = this.typeConditionValidators[validator.type][condition.type] ?? null;
                if (validationFunction && !validationFunction(inputValue, ...condition.args)) errorBag.push(this.getErrorMessage(validator.type, condition.type, name, condition.args));
            }
        }

        return errorBag.length
            ? { valid: false, errorBag, message: this.getErrorBagSummary(errorBag) }
            : { valid: true };
    }

    typeConditionValidators= {
        [ValidTypes.Array]: {
            [ValidTypeConditions.Minimum]: (value, compareValue) => value.length >= compareValue,
            [ValidTypeConditions.Maximum]: (value, compareValue) => value.length <= compareValue,
        },
        [ValidTypes.Object]: {
            [ValidTypeConditions.Minimum]: (value, compareValue) => Object.keys(value).length >= compareValue,
            [ValidTypeConditions.Maximum]: (value, compareValue) => Object.keys(value).length <= compareValue,
            [ValidTypeConditions.HasKeys]: (value, compareKeys) => {
                for (const key of compareKeys) {
                    if (!(key in value)) return false;
                }
                return true;
            }
        },
        [ValidTypes.String]: {
            [ValidTypeConditions.Minimum]: (value, compareValue) => value.length >= compareValue,
            [ValidTypeConditions.Maximum]: (value, compareValue) => value.length <= compareValue,
        },
        [ValidTypes.Numeric]: {
            [ValidTypeConditions.Minimum]: (value, compareValue) => value >= compareValue,
            [ValidTypeConditions.Maximum]: (value, compareValue) => value <= compareValue,
        },
        [ValidTypes.Boolean]: {},
        [ValidTypes.None]: {},
    }

    errorMessages = {
        generic: {
            default: '%input% is not a valid value',
            nullable: 'Please supply a value for %input%',
            badType: '%input% must be of type "%arg0%"',
        },
        [ValidTypes.Array]: {
            [ValidTypeConditions.Minimum]: 'Please supply at least %arg0% values for %input%',
            [ValidTypeConditions.Maximum]: 'A maximum of %arg0% values can be supplied for %input%',
        },
        [ValidTypes.Object]: {
            [ValidTypeConditions.Minimum]: 'Please supply at least %arg0% values for %input%',
            [ValidTypeConditions.Maximum]: 'A maximum of %arg0% values can be supplied for %input%',
            [ValidTypeConditions.HasKeys]: '%input% requires the following keys: %arg0%',
        },
        [ValidTypes.String]: {
            [ValidTypeConditions.Minimum]: '%input% must contain at least %arg0% characters',
            [ValidTypeConditions.Maximum]: '%input% must contain %arg0% or less characters',
        },
        [ValidTypes.Numeric]: {
            [ValidTypeConditions.Minimum]: '%input% must be %arg0% or greater',
            [ValidTypeConditions.Maximum]: '%input% must be %arg0% or less',
        },
    }

    getErrorMessage(validatorType, conditionType, inputName, ...args) {
        const errorMessage = this.errorMessages[validatorType]?.[conditionType] ?? this.errorMessages.default;
        inputName = inputName ?? 'the input';

        let baseMessage = errorMessage.replace(/%input%/g, inputName);
        for (let i = 0; i < 10; i++) {
            const rx = new RegExp(`%arg${i}%`, 'g');
            if (!rx.test(baseMessage) || !args[i]) break;
            else baseMessage = baseMessage.replace(rx, args[i]);
        }

        return baseMessage;
    }

    processValidator(validatorString) {
        const validatorOutput = {
            type: ValidTypes.None,
            nullable: false,
            conditions: [],
        };

        const parts = Array.isArray(validatorString)
            ? validatorString
            : validatorString.split(/\s*\|\s*/g);

        validatorOutput.type = parts.find(part => Object.values(ValidTypes).includes(part)) ?? ValidTypes.None;

        parts.forEach(part => {
            if (!Object.values(ValidTypes).includes(part)) {
                const validCondition = this.processValidatorCondition(part, validatorOutput.type);
                if (validCondition) {
                    if (validCondition.type === ValidTypeConditions.Nullable)
                        validatorOutput.nullable = true;
                    else
                        validatorOutput.conditions.push(validCondition);
                }
            }
        });

        return validatorOutput;
    }

    processValidatorCondition(conditionString, type) {
        const conditionOutput = {
            type: null,
            args: [],
        };

        const parts = conditionString.split(/\s*:\s*/g);
        const condition = parts.shift() ?? '';

        if (Object.values(ValidTypeConditions).includes(condition)) {
            const argumentType = this.validTypeConditions[type][condition] ?? null;
            if (argumentType === null) {
                conditionOutput.type = condition;
            }
            else {
                const validInputs = this.cleanAndTypeCheckValues(parts, argumentType, true, true);
                if (validInputs?.length) {
                    conditionOutput.type = condition;
                    conditionOutput.args = validInputs;
                }
            }
        }

        if (conditionOutput.type && Object.values(ValidTypeConditions).includes(conditionOutput.type)) {
            return conditionOutput;
        }
        else {
            return null;
        }
    }

    cleanAndTypeCheckValues(values, type, allValuesMustPass = true, conditionOperatorValues = false) {
        const validValues = [];
        values.forEach(value => {
            const cleanValue = this.cleanType(value, type, conditionOperatorValues);
            if (this.typeCheck(cleanValue, type)) validValues.push(cleanValue);
        });

        return !allValuesMustPass || validValues.length === values.length
            ? validValues
            : null;
    }

    cleanType(value, type, isConditionOperator = false) {
        switch(type) {
            case ValidTypes.Numeric: {
                return !isNaN(Number(value))
                    ? Number(value)
                    : undefined;
            }
            case ValidTypes.String: {
                return typeof(value) === 'string'
                    ? value
                    : typeof(value) === 'number'
                        ? `${value}`
                        : undefined;
            }
            case ValidTypes.Boolean: {
                return (value === 'true' || value === 1 || value === true)
                    ? true
                    : (value === 'false' || value === 0 || value === false)
                        ? false
                        : undefined;
            }
            case ValidTypes.Array: {
                if (Array.isArray(value)) return value;
                else if (isConditionOperator && /\s*\[[^\]]+]\s*/.test(value)) {
                    return value.replace(/^\s*\[/, '')
                        .replace(/]\s*$/, '')
                        .split(/\s*,\s*/g);
                }
                else return null;
            }
            default: {
                return value;
            }
        }
    }

    typeCheck(value, type) {
        switch(type) {
            case ValidTypes.Array: {
                return Array.isArray(value);
            }
            case ValidTypes.Object: {
                return value && typeof(value) === 'object' && !Array.isArray(value);
            }
            case ValidTypes.String: {
                return typeof(value) === 'string';
            }
            case ValidTypes.Numeric: {
                return typeof(value) === 'number';
            }
            case ValidTypes.Boolean: {
                return typeof(value) === 'boolean';
            }
            case ValidTypes.None:
            default: {
                return true;
            }
        }
    }

    getErrorBagSummary(errorBag) {
        return !errorBag?.length
            ? ''
            : errorBag.length > 1
                ? `${errorBag[0]} (and ${errorBag.length - 1} other errors).`
                : `${errorBag[0]}.`;
    }

    getDefaultTypeValue(input) {
        if (!input.validation) return null;
        const validator = this.processValidator(input.validation);
        switch(validator.type) {
            case ValidTypes.Array: {
                return [];
            }
            case ValidTypes.Object: {
                return {};
            }
            case ValidTypes.String: {
                return '';
            }
            case ValidTypes.Numeric: {
                return 0;
            }
            case ValidTypes.Boolean: {
                return false;
            }
            case ValidTypes.None:
            default: {
                return null;
            }
        }
    }

    getExpectedType(input) {
        if (!input.validation) return ValidTypes.None;
        const parts = Array.isArray(input.validation)
            ? input.validation
            : input.validation.split(/\s*\|\s*/g);

        return parts.find(part => Object.values(ValidTypes).includes(part)) ?? ValidTypes.None;
    }
}
