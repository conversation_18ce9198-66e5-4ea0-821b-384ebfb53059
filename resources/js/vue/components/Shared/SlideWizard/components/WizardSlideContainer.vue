<template>
    <div class="w-full h-full">
        <AlertsContainer :dark-mode="darkMode" :alert-type="alertType" :text="alertText" v-if="alertActive" />
        <!--   HEADER     -->
        <div class="flex overflow-x-auto w-max">
            <div v-for="[slideKey, slideName] in Object.entries(this.wizardStore.slideNameMap)"
                 :key="slideKey"
                 class="inline-flex items-center"
            >
                <div @click="saveAndProgressSlide(slideKey)"
                     class="px-2 py-1 rounded-md"
                     :class="[this.wizardStore.currentSlideKey === slideKey ? 'text-primary-500 bg-primary-500 bg-opacity-10' : this.wizardStore.isSlideValid(slideKey) ? 'text-gray-500 cursor-pointer' : 'text-slate-300 cursor-pointer' ]"
                >
                    <span class="font-medium text-md">
                        {{ slideName }}
                    </span>
                </div>
                <svg class="inline w-2 mx-3" v-if="!this.wizardStore.isFinalSlide(slideKey)" viewBox="0 0 10 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M0.417527 0.677659C0.808051 0.287135 1.44122 0.287135 1.83174 0.677659L8.83174 7.67766C9.22227 8.06818 9.22227 8.70135 8.83174 9.09187L1.83174 16.0919C1.44122 16.4824 0.808051 16.4824 0.417527 16.0919C0.0270027 15.7013 0.0270027 15.0682 0.417527 14.6777L6.71042 8.38477L0.417527 2.09187C0.0270027 1.70135 0.0270027 1.06818 0.417527 0.677659Z" fill="#64748B"/>
                </svg>
            </div>
        </div>
        <div class="overflow-y-auto max-h-[60vh] mt-3">
            <div v-if="readonly"
                 class="flex py-4 gap-x-3 items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z" />
                </svg>
                <p>
                    This Campaign is readonly and cannot be saved.
                </p>
            </div>

            <!--   CURRENT SLIDE   -->
            <Transition name="slide" mode="out-in">
                <component
                    ref="currentSlideInstance"
                    v-if="this.wizardStore.initialized"
                    :is="this.wizardStore.currentSlideComponent"
                    :initial-slide-data="this.wizardStore.getInitialSlideData"
                    @update:slide-input="handleInputUpdate"
                    :dark-mode="darkMode"
                />
            </Transition>
        </div>
        <!--    FOOTER    -->
        <div class="relative bottom-0 border-t flex justify-between pt-6 mt-3"
             :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
        >
            <CustomButton @click="cancelWizard"
                          :dark-mode="darkMode"
                          color="slate"
            >
                Cancel
            </CustomButton>
            <div class="flex items-center gap-16">
                <CustomButton v-if="!this.wizardStore.isFinalSlide()"
                              @click="saveAndProgressSlide()"
                              :dark-mode="darkMode"
                >
                    Next
                </CustomButton>
                <CustomButton
                    v-if="readonly"
                    :dark-mode="darkMode"
                    @click="cancelWizard(true)"
                    color="slate"
                    title="This Campaign cannot be saved"
                >
                    Exit
                </CustomButton>
                <div class="flex items-center gap-x-4"
                     v-else-if="this.wizardStore.onFinalIncompleteSlide"
                >
                    <CustomButton @click="saveWizard()"
                                  color="green-outline"
                                  :dark-mode="darkMode"
                                  :disabled = "savingCampaign || readonly"
                    >
                        Save
                    </CustomButton>
                    <CustomButton @click="saveWizard(true)"

                                  color="green"
                                  :dark-mode="darkMode"
                                  :disabled = "savingCampaign || readonly"
                    >
                        Save & Exit
                    </CustomButton>
                </div>

            </div>
        </div>
    </div>
</template>

<script>
import { useWizardStore } from "../stores/wizard.js";
import CustomButton from "../../components/CustomButton.vue";
import AlertsContainer from "../../components/AlertsContainer.vue";
import AlertsMixin from "../../../../mixins/alerts-mixin.js";

export default {
    name: 'WizardSlideContainer',
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        configuration: {
            type: Object,
            required: true,
        },
        slideMap: {
            type: Object,
            required: true,
        },
        savingCampaign: {
            type: Boolean,
            default: false
        },
        readonly: {
            type: Boolean,
            default: false,
        }
    },
    components: {
        AlertsContainer,
        CustomButton,
    },
    mixins: [AlertsMixin],
    emits: [
        'save:slide',
        'close:wizard',
        'save:wizard',
    ],
    data() {
        return {
            wizardStore: useWizardStore(),
        }
    },
    mounted() {
        this.wizardStore.initialize(this.configuration, this.slideMap);
    },
    methods: {
        validateSlide() {
            if (this.$refs.currentSlideInstance && 'customValidation' in this.$refs.currentSlideInstance) {
                const validator = this.$refs.currentSlideInstance.customValidation;
                if (typeof (validator) === "function") {
                    const { valid, errorBag, message } = validator();
                    if (!valid) {
                        const errorMessage = errorBag?.length
                            ? this.wizardStore.validationService.getErrorBagSummary(errorBag)
                            : message ?? 'Validation error';

                        this.showAlert('error', errorMessage);

                        return { valid };
                    }
                }
            }
            const { valid, errorBag, data } = this.wizardStore.validateSlide();
            if (!valid) {
                if (errorBag) this.showAlert('error', this.wizardStore.validationService.getErrorBagSummary(errorBag));
            }

            return { valid, data };
        },
        handleInputUpdate(newValue, inputKey) {
            this.wizardStore.updateWizardInput(newValue, inputKey);
        },
        cancelWizard(skipConfirmation = false) {
            this.$emit('close:wizard', skipConfirmation);
        },
        saveAndProgressSlide(goToSlideKey) {
            const validateSlideData = this.validateSlide();
            if (validateSlideData.valid) {
                this.emitSlideUpdate(validateSlideData.data);
                if (goToSlideKey)
                    this.wizardStore.goToSlide(goToSlideKey);
                else
                    this.wizardStore.nextSlide();
            }
        },
        emitSlideUpdate(validatedData) {
            const payloadKey = this.wizardStore.getPayloadKey();
            const slidePayload = validatedData ?? this.wizardStore.fetchSlideInputValues();
            if (payloadKey) {
                const newPayload = { [payloadKey]: {} };
                for (const input in slidePayload) {
                    newPayload[payloadKey][input] = slidePayload[input];
                    this.$emit('save:slide', newPayload, this.wizardStore.currentSlideKey);
                }
            }
            else {
                this.$emit('save:slide', slidePayload, this.wizardStore.currentSlideKey);
            }
        },
        saveWizard(exitOnSuccess) {
            if (this.readonly) {
                this.showAlert('error', "This Campaign is readonly, and cannot be saved.");
                return;
            }

            const currentSlideValid = this.validateSlide();
            if (!currentSlideValid.valid) return;
            else {
                this.emitSlideUpdate(currentSlideValid.data);
            }

            const { valid, message, errorBag } = this.wizardStore.validateWizard();
            if (!valid) {
                this.showAlert('error', message ?? this.wizardStore.validationService.getErrorBagSummary(errorBag ?? []) ?? "Unable to save the Slide Wizard.");
            }
            else this.$emit('save:wizard', exitOnSuccess);
        },
    }
}

</script>

<style scoped>
.slide-enter-active,
.slide-leave-active {
    opacity: 1;
    transition: opacity 0.25s ease-in 0s;
}

.slide-enter-from,
.slide-leave-to {
    opacity: 0;
    transition: opacity 0.25s 0s;
}
</style>
