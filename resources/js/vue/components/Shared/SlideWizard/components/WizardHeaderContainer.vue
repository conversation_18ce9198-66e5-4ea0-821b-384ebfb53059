<template>
    <div v-if="wizardStore.headerComponent"
        class="pb-3"
    >
        <component
            :is="wizardStore.headerComponent"
            :initial-header-data="wizardStore.getInitialHeaderData()"
            :dark-mode="darkMode"
            @update:header-input="handleInputUpdate"
        />
    </div>
</template>

<script>
import { useWizardStore } from "../stores/wizard.js";

export default {
    name: 'WizardHeaderContainer',
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            wizardStore: useWizardStore(),
        }
    },
    methods: {
        handleInputUpdate(newValue, inputKey) {
            this.wizardStore.updateWizardHeader(newValue, inputKey);
        },
    }
}


</script>
