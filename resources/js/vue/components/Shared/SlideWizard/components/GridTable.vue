<template>
    <div class="py-4">
        <h4 class="font-semibold pb-1">
            {{ tableSetup.title }}
        </h4>
        <p class="text-sm text-gray-500 pb-4">
            {{ tableSetup.subTitle }}
        </p>
        <div class="hidden md:grid text-xs uppercase px-4 font-bold py-3 items-center border-b"
             :class="[
                darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border',
                gridColumnClasses
        ]">
            <div v-for="(item) in tableSetup?.columns"
                :key="getColumnKey(item)"
            >
                <div v-if="item.tooltip" class="pb-2 ml-2">
                    <Tooltip
                        :align-left="false"
                        :dark-mode="darkMode"
                    >{{ item.tooltip }}</Tooltip>
                </div>
                <div :class="item.headerClass ?? ''">
                    {{ item.label }}
                </div>
                <div v-if="item.selectAll">
                    <CustomCheckbox
                        :dark-mode="darkMode"
                        v-model="selectAll"
                    />
                </div>
            </div>
        </div>
        <div class="relative border-b text-sm items-start md:items-center md:grid grid-cols-1 gap-4 md:gap-0 px-4 py-6 md:py-4 md:px-4"
             :class="[
                 gridColumnClasses,
                 darkMode ? 'odd:bg-dark-175 border-dark-border' : 'odd:bg-gray-200 border-light-border']"
			 v-for="(tableItem, index) in tableData"
        >
			<div v-for="(columnItem, columnIndex) in tableSetup?.columns"
			   :key="tableItem.key ?? index"
			>
				<p class="font-bold text-sm md:hidden">{{ columnItem.label }}</p>
                <!--     TOGGLE SWITCH       -->
                <div v-if="columnItem.toggle">
                    <ToggleSwitch
                        :dark-mode="darkMode"
                        v-model="tableItem[getColumnKey(columnItem)]"
                        :small="true"
                        color="green"
                        @update:model-value="(newValue) => handleToggleSwitch(newValue, tableItem, columnItem)"
                    />
                </div>

                <!--    CUSTOM CHECKBOX      -->
				<div v-else-if="columnItem.checkbox"
					 class="flex items-center gap-x-3"
					 :class="[checkboxDisabled(tableItem, columnItem) && 'pointer-events-none grayscale-[50%] opacity-50']"
				>
					<CustomCheckbox v-model="tableItem[columnItem.emitKey ?? getColumnKey(columnItem)]"
						@update:model-value="emitCheckboxEvent(tableItem, columnItem.emitKey)"
						:input-disabled="checkboxDisabled(tableItem, columnItem)"
                        :dark-mode="darkMode"
					/>
					<div class="whitespace-pre-line overflow-ellipsis overflow-x-hidden"
                        :class="tailwindClasses[columnIndex]"
                         @click="handleClick(tableItem, columnItem)"
                         v-if="getColumnKey(columnItem)"
                    >
						{{ getTableValue(index, columnItem.key, columnItem) }}
					</div>
				</div>

                <!--    SELECT-ALL COLUMN      -->
                <div v-else-if="columnItem.selectAll"
                     class="flex items-center gap-x-3"
                >
                    <CustomCheckbox
                        v-model="selection[tableItem[getColumnEmitKey(columnItem)]]"
                    />
                </div>

				<div v-else-if="columnItem.editDelete"
					class="flex justify-start items-start text-left"
				>
					<ActionsHandle
                        :dark-mode="darkMode"
                        @edit="emitActionEvent(ActionType.Edit, tableItem, columnItem)"
                        @delete="emitActionEvent(ActionType.Delete, tableItem, columnItem)"
                    />
				</div>
                <div v-else-if="columnItem.button"
                     class="flex justify-start items-start text-left"
                >
                    <custom-button :dark-mode="darkMode" @click="handleButtonClick(tableItem, columnItem)">{{ columnItem.buttonText ?? 'Button' }}</custom-button>
                </div>
				<div v-else class="whitespace-pre-line"
                     :class="tailwindClasses[columnIndex]"
                     @click="handleClick(tableItem, columnItem)"
                >
					<p>{{ getTableValue(index, columnItem.key, columnItem) }}</p>
				</div>
			</div>
        </div>
    </div>
</template>

<script>
import { nextTick } from "vue";
import Tooltip from "../../components/Tooltip.vue";
import CustomCheckbox from "./CustomCheckbox.vue";
import ToggleSwitch from "../../components/ToggleSwitch.vue";
import ActionsHandle from "../../components/ActionsHandle.vue";
import CustomButton from "../../components/CustomButton.vue";

/**
 * type HeaderItem = {
 *     label: string,
 *     key: string | string[], // Key to look up on table items to retrieve values. Can be an array for multiple keys.
 *     class?: string, // Override class of value displays
 *     headerClass?: string, // Override class of header display
 *     text?: string, // Text for display in addition to key values. Use %0 placeholders to replace with key value(s)
 *     itemClickable?: boolean, // Make value clickable, cursor-pointer, emit event
 *     emitKey?: string, // Any emitted events will emit this key instead of the main key if provided
 *     defaultValue?: string, // Can be set per-column where value is falsy
 *     trueValue?: string, // Display this string if the provided key is falsy
 *     checkbox?: boolean|string, // set to true to enable checkbox, or use a data key to disable if that key is falsy
 *     editDelete?: boolean, // Action handle
 *     tooltip?: string, // Will display tooltip if provided
 *     toggle?: boolean, // Toggle switch with emitter
 *     selectAll?: boolean, // Column will become a selection column, including Header item for select-all
 * }
 *
 * interface TableSetup {
 *     title?: string,
 *     subTitle?: string,
 *     columns: HeaderItem[],
 *     columnClasses?: string, // Override auto Tailwind column classes
 * }
 *
 */

const ActionType = {
    Edit: 'edit',
    Delete: 'delete',
}

export default {
    name: 'GridTable',
    components: {
        CustomButton,
        ActionsHandle,
        ToggleSwitch,
        CustomCheckbox,
        Tooltip

    },
    props: {
        tableSetup: {
            type: Object,
            required: true,
        },
        tableData: {
            type: Array,
            default: [],
        },
        defaultValue: {
            type: String,
            default: 'N/A',
        },
        darkMode: {
            type: Boolean,
            default: false,
        },
    },
    emits: [
        'change:checkbox',
        'action:edit',
        'action:delete',
        'update:selection',
        'update:toggle-switch',
        'click:item',
        'click:button',
    ],
    data() {
        return {
            tailwindClasses: [],
            selectAll: false,
            selection: {},
        }
    },
    computed: {
        ActionType() {
            return ActionType
        },
        gridColumnClasses() {
            return this.tableSetup?.columnClasses ?? `grid-cols-${this.tableSetup?.columns.length ?? 4}`;
        },
    },
    mounted() {
        this.initialize();
    },
    methods: {
        initialize() {
            this.initializeSelectAll();
            this.compileTailwindClasses();
        },
        initializeSelectAll(column) {
            column = column ?? this.tableSetup.columns.find(column => column.selectAll);
            if (!column) return;

            const selectAllKey = getColumnEmitKey(column);
            props.tableData.forEach(item => {
                this.selection[item[selectAllKey]] = false;
            });
        },
        compileTailwindClasses() {
            this.tableSetup.columns.forEach(column => {
                let classes = '';
                if (column.itemClickable) classes += 'cursor-pointer ';
                if (column.class) classes += column.class;

                this.tailwindClasses.push(classes);
            });
        },
        checkboxDisabled(tableItem, columnItem) {
            return typeof(columnItem.checkbox) === 'string'
                ? !tableItem[columnItem.checkbox]
                : false;
        },
        processReplacers(keyValues, text) {
            if (keyValues.length > 1) {
                keyValues.forEach((keyValue, index) => {
                    const rx = new RegExp(`%${index}`, 'g');
                    text = text.replace(rx, keyValue);
                });
            }
            else
                text = text.replace(/%\d+/g, keyValues[0]);

            return text;
        },
        getTableValue(index, key, column) {
            const keyValues = [];
            if (Array.isArray(key))
                keyValues.push(...key.map(k => this.tableData[index]?.[k]));
            else if (column.trueValue)
                keyValues.push(!!this.tableData[index]?.[key] ? column.trueValue : column.defaultValue);
            else
                keyValues.push(this.tableData[index]?.[key]);

            return keyValues.length
                ? column.text
                    ? this.processReplacers(keyValues, column.text)
                    : keyValues.join("\n")
                : column.defaultValue ?? props.defaultValue;
        },
        getColumnKey(column) {
            return Array.isArray(column.key)
                ? column.key[0]
                : column.key;
        },
        getColumnEmitKey(column) {
            return column.emitKey || this.getColumnKey(column);
        },
        emitCheckboxEvent(tableItem, emitKey) {
            this.$emit('change:checkbox', emitKey ? tableItem[emitKey] : tableItem);
        },
        emitActionEvent(actionType, tableItem, column) {
            const emitKey = this.getColumnEmitKey(column);
            if (actionType === ActionType.Edit)
                this.$emit('action:edit', tableItem[emitKey]);
            else if (actionType === ActionType.Delete)
                this.$emit('action:delete', tableItem[emitKey]);
            else
                console.error(`Unknown action in GridTable: ${actionType}`);
        },
        async emitUpdateSelection() {
            await nextTick();
            const selected = Object.entries(this.selection).reduce((output, [key, checked]) => {
                return checked
                    ? [...output, key]
                    : output;
            }, []);

            this.$emit('update:selection', selected);
        },
        handleSelectAll(checked) {
            for (const key in this.selection) {
                this.selection[key] = checked;
            }

            this.emitUpdateSelection();
        },
        handleToggleSwitch(newValue, tableItem, column) {
            const emitKey = this.getColumnEmitKey(column);
            const keyValue = tableItem[emitKey];

            this.$emit('update:toggle-switch', newValue, keyValue);
        },
        handleButtonClick(item, column) {
            const emitKey = this.getColumnEmitKey(column);
            this.$emit('click:button', item[emitKey]);
        },
        handleClick(item, column) {
            const emitKey = this.getColumnEmitKey(column);
            this.$emit('click:item', item[emitKey]);
        },
    },
    watch: {
        selectAll: {
            deep: true,
            handler(newValue) {
                this.handleSelectAll(newValue);
            }
        },
        selection: {
            deep: true,
            handler() {
                this.emitUpdateSelection();
            }
        },
        tableData: {
            deep: true,
            handler() {
                this.initializeSelectAll();
            },
        }
    },
}
</script>
