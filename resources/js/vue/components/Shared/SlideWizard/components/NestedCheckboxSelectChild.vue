<template>
    <div class="relative hidden z-20 md:sticky top-0 md:grid text-xs uppercase font-bold py-2 px-6 items-center border-b"
        :class="[`md:${getGridColumns}`, darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']"
    >
        <p class=""
            v-for="(column, index) in childTableMap"
           :key="column.key ?? index"
        >
            {{ column.header ?? '' }}
        </p>
    </div>
    <div v-for="(item, index) in childTableData"
         :key="item.key ?? index"
         class="relative border-b items-center grid gap-4 md:gap-0 py-4 px-4 md:px-6"
         :class="[`md:${getGridColumns}`, darkMode ? 'odd:bg-dark-background border-dark-border' : 'odd:bg-light-background border-light-border']"
    >
        <div v-for="(column, index) in childTableMap"
            :key="column.key ?? index"
         >
            <div v-if="column.type">
                <div v-if="showColumn(PresetType.Checkbox, column, item)">
                    <input type="checkbox"
                           class="form-checkbox h-4 w-4 text-primary-500 rounded"
                           :checked="!!item[column.key]"
                           @change="({ target }) => handleCheckboxClick(item[column.emitKey ?? column.key], target.checked)"
                    />
                </div>
                <div v-else-if="showColumn(PresetType.SelectAll, column, item)"
                     class="flex gap-x-6 text-sm"
                >
                    <CustomButton
                        color="primary"
                        @click="toggleAll(item[column.key], true)"
                        :dark-mode="darkMode"
                    >
                        Add All
                    </CustomButton>
                    <CustomButton
                        @click="toggleAll(item[column.key], false)"
                        color="red"
                        :dark-mode="darkMode"
                    >
                        Remove All
                    </CustomButton>
                </div>
            </div>
            <div v-else>
                <div v-if="column.emitClick"
                     @click="selectItem(item[column.emitKey || column.key])"
                     class="cursor-pointer"
                >
                    {{ item[column.key] ?? 'N/A' }}
                </div>
                <p v-else class="">
                    {{ item[column.key] ?? 'N/A' }}
                </p>
            </div>
        </div>
    </div>
</template>

<script>
import CustomButton from "../../components/CustomButton.vue";
import NestedCheckboxSelectNavigation from "./NestedCheckboxSelectNavigation.vue";

export default {
    name: 'NestedCheckboxSelectChild',
    components: {
        NestedCheckboxSelectNavigation,
        CustomButton,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        childTableMap: {
            type: Object,
            default: () => [],
        },
        childTableData: {
            type: Array,
            default: () => [],
        },
        gridColumns: {
            type: String,
            default: null,
        },
    },
    emits: [
        'update:selection',
        'toggleAll',
        'selectItem',
        'toggleCheckbox',
    ],
    computed: {
        getGridColumns() {
            return this.gridColumns ?? `grid-cols-${this.childTableMap.length ?? 4}`;
        },
    },
    data() {
        return {
            PresetType: {
                SelectAll: 'selectAll',
                Checkbox: 'checkbox',
            },
        }
    },
    methods: {
        toggleAll(itemKey, addItems) {
            this.$emit('toggleAll', itemKey, addItems);
        },
        selectItem(itemKey) {
            this.$emit('selectItem', itemKey);
        },
        handleCheckboxClick(itemKey, addItem) {
            this.$emit('toggleCheckbox', itemKey, addItem);
        },
        showColumn(columnType, column, item) {
            return columnType === column.type
                ? typeof(column.show) === 'string'
                    ? this.handleStringConditional(column.show, item)
                    : !!column.show
                : false;
        },
        handleStringConditional(keyString, item) {
            const [_, negation, key] = (keyString.match(/^\s*(!)?(.*)/) ?? []);
            const truthyKey = !!item[key];

            return negation ? !truthyKey : truthyKey;
        },
    },
}
</script>
