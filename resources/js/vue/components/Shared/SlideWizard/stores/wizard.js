import { defineStore } from "pinia";
import { computed, reactive, ref, shallowRef } from "vue";
import { ValidTypes, WizardValidationService } from "../services/WizardValidationService.js";
import { WizardTransformerService } from "../services/WizardTransformerService.js";


export const ReservedComponent = {
    Header: 'header',
}

/**
 * This store should be kept generic. It can be referenced by Campaign specific components to get Slide info,
 * but should not contain any Campaign-specific logic
 */
export const useWizardStore = defineStore('slideWizard', () => {
    const initialized = ref(false);

    const configuration = ref([]);

    const currentSlideIndex = ref(0);
    const currentSlideKey = computed(() => configuration.value?.[currentSlideIndex.value]?.key ?? null);
    const currentSlideComponent = computed(() => slideMap.value?.[currentSlideKey.value] ?? null);

    const slideNameMap = computed(() => configuration.value?.reduce((output, slide) => {
        return { ...output, [slide.key]: slide.name ?? slide.key }
    }, {}))

    const slideMap = ref({});
    const wizardInputMap = reactive({});
    const slideCompletionMap = ref({});

    const headerInputMap = reactive({});
    const headerComponent = shallowRef(null);
    const headerConfiguration = ref({});

    const validationService = new WizardValidationService();
    const transformerService = ref(new WizardTransformerService());

    const onFinalIncompleteSlide = computed(() => {
        for (const slideKey in slideMap.value) {
            if (slideKey !== currentSlideKey.value && !slideCompletionMap.value[slideKey]) return false;
        }
        return true;
    });

    const initialize = (wizardConfiguration, wizardSlideMap, wizardTransformerService = null) => {
        if (initialized.value) return { status: true }
        if (!wizardConfiguration || !Array.isArray(wizardConfiguration) || !wizardSlideMap) {
            return { status: false, message: `Configuration error in Slide Wizard` };
        }

        if (wizardTransformerService != null) {
            transformerService.value = new wizardTransformerService();
        }

        processConfiguration(wizardConfiguration);
        processSlideComponentMap(wizardSlideMap);

        initializeInputs()
        setToFirstSlide();
        initialized.value = true;

        return { status: true }
    }

    /**
     * Currently only extracts Custom Header inputs from modules
     */
    const processConfiguration = (wizardConfiguration) => {
        wizardConfiguration.forEach(config => {
            if (config[ReservedComponent.Header]?.inputs) {
                Object.entries(config[ReservedComponent.Header].inputs).forEach(([ inputKey, inputConfig]) => {
                    headerConfiguration.value[inputKey] = inputConfig;
                });
            }
        });
        configuration.value = wizardConfiguration;
    }

    /**
     * Any reserved components (like custom header) should be processed here before being removed from the main slide map
     */
    const processSlideComponentMap = (wizardSlideMap) => {
        if (wizardSlideMap[ReservedComponent.Header]) {
            headerComponent.value = { ...wizardSlideMap[ReservedComponent.Header] };
            for (const componentName of Object.values(ReservedComponent)) {
                if (wizardSlideMap[componentName]) delete wizardSlideMap[componentName];
            }
        }
        slideMap.value = wizardSlideMap;
    }

    const updateCurrentSlide = (index) => {
        if (index >= 0 && index <= configuration.value.length - 1) {
            currentSlideIndex.value = index;
        }
    }

    const nextSlide = () => {
        if (configuration.value.length > currentSlideIndex.value)
            updateCurrentSlide(currentSlideIndex.value + 1);
    }

    const previousSlide = () => {
        if (currentSlideIndex.value > 0)
            updateCurrentSlide(currentSlideIndex.value - 1);
    }

    const goToSlide = (slideKey) => {
        const targetIndex = configuration.value.findIndex(slide => slide.key === slideKey);
        if (targetIndex >= 0)
            updateCurrentSlide(targetIndex);
    }

    const setToFirstSlide = () => {
        updateCurrentSlide(0);
    }

    const getCurrentSlideComponent = () => {
        return slideMap.value[currentSlideKey.value];
    }

    const processInputLevel = (currentInputs, parentObject) => {
        Object.entries(currentInputs ?? {}).forEach(([inputKey, inputConfig]) => {
            if (inputConfig.containedKeys) {
                const expectedType = validationService.getExpectedType(inputConfig);
                if (expectedType === ValidTypes.Array) {
                    parentObject[inputKey] = [];
                    parentObject[inputKey].push({});
                    processInputLevel(inputConfig.containedKeys, parentObject[inputKey][parentObject[inputKey].length - 1]);
                }
                else if (expectedType === ValidTypes.Object) {
                    parentObject[inputKey] = {};
                    processInputLevel(inputConfig.containedKeys, parentObject[inputKey]);
                }
            }
            else {
                parentObject[inputKey] = inputConfig.initialData ?? validationService.getDefaultTypeValue(inputConfig);
            }
        });
    }

    const initializeInputs = () => {
        configuration.value.forEach(slideConfig => {
            if (!slideConfig.inputs) return;

            wizardInputMap[slideConfig.key] = {};
            processInputLevel(slideConfig.inputs, wizardInputMap[slideConfig.key]);

            if (slideConfig[ReservedComponent.Header]) {
                processInputLevel(slideConfig[ReservedComponent.Header].inputs, headerInputMap)
            }
        });
    }

    const getInitialSlideData = computed(() => {
        return wizardInputMap[currentSlideKey.value] ?? {};
    });

    const getInitialHeaderData = () => {
        return headerInputMap;
    }

    const checkInputsAreMapped = (mappedKeys, expectedInputs) => {
        const unmappedKeys = [];
        for (const key in expectedInputs) {
            if (!mappedKeys.includes(key)) unmappedKeys.push(key);
        }

        return unmappedKeys.length
            ? { status: false, message: `The current slide fails to map the keys: ${unmappedKeys.join(', ')}`}
            : { status: true }
    }

    const updateWizardInput = (newValue, inputKey) => {
        if (inputKey in wizardInputMap[currentSlideKey.value]) {
            wizardInputMap[currentSlideKey.value][inputKey] = newValue;
        }
    }

    const updateWizardHeader = (newValue, inputKey) => {
        if (inputKey in headerInputMap) {
            headerInputMap[inputKey] = newValue;
        }
    }

    const getInputName = (inputKey, slideKey) => {
        slideKey = slideKey ?? currentSlideKey.value;
        const targetInputs = slideKey === ReservedComponent.Header
            ? headerConfiguration.value
            : configuration.value.find(config => config.key === currentSlideKey.value)?.inputs;

        return targetInputs?.[inputKey]?.name ?? 'Input';
    }

    const getSlideName = (slideKey) => {
        slideKey = slideKey ?? currentSlideKey.value;
        return configuration.value.find(slide => slide.key === slideKey)?.name ?? "Slide";
    }

    const markSlideValid = (isValid, slideKey) => {
        slideKey = slideKey ?? currentSlideKey.value;
        slideCompletionMap.value[slideKey] = isValid;
    }

    const markAllSlidesValid = (isValid) => {
        for (const slideKey in slideMap.value) {
            slideCompletionMap.value[slideKey] = !!isValid;
        }
    }

    //TODO validation of nested objects and arrays as per initializeInputs
    const validateSlide = (slideKey = null) => {
        slideKey = slideKey ?? currentSlideKey.value;
        const validationResult = { valid: true };

        const data = slideKey === ReservedComponent.Header
            ? headerInputMap
            : JSON.parse(JSON.stringify(wizardInputMap[slideKey]));
        const slideInputs = slideKey === ReservedComponent.Header
            ? headerConfiguration.value
            : configuration.value.find(slide => slide.key === slideKey)?.inputs;

        if (!slideInputs) return validationResult;

        for (const inputKey in slideInputs) {
            const transformedData = transformerService.value.transformPayload(slideKey, inputKey, data[inputKey]);
            const { valid, errorBag } = validationService.validate(slideInputs[inputKey], transformedData ?? null);
            if (!valid) {
                validationResult.errorBag = validationResult.errorBag ?? [];
                validationResult.errorBag.push(...(errorBag || []));
                validationResult.valid = false;
            }
            else {
                validationResult.data = validationResult.data ?? {};
                validationResult.data[inputKey] = transformedData;
            }

            markSlideValid(valid, slideKey);
        }

        return validationResult;
    }

    const isSlideValid = (slideKey) => {
        slideKey = slideKey || currentSlideKey.value;
        return !!slideCompletionMap.value[slideKey];
    }

    /**
     * Final validation for all slides and reserved components
     */
    const validateWizard = () => {
        const headerValidation = validateSlide(ReservedComponent.Header);
        if (!headerValidation.valid)
            return headerValidation;

        const runningValidation = {};
        for (const slideKey in slideMap.value) {
            if (slideKey !== currentSlideKey.value) {
                const { valid, errorBag, message } = validateSlide(slideKey);
                if (!valid && message) runningValidation[slideKey] = errorBag ?? [message];
            }
        }

        if (Object.keys(runningValidation).length) {
            const errorMessage = Object.entries(runningValidation).reduce((outputString, [slideKey, errorBag]) => {
                const slideName = getSlideName(slideKey);
                return outputString + `${slideName}: ${errorBag.join("\n")}\n`;
            }, 'The following errors were found:\n');
            return { valid: false, message: errorMessage }
        }
        else {
            return { valid: true }
        }
    }

    const fetchReservedComponents = () => {
        return {
            [ReservedComponent.Header]: JSON.parse(JSON.stringify(headerInputMap))
        }
    }

    const isFinalSlide = (slideKey) => {
        slideKey = slideKey || currentSlideKey.value;
        const index = configuration.value.findIndex(slide => slide.key === slideKey);
        return index + 1 === configuration.value.length;
    }

    const fetchInputValue = (slideKey, inputKey) => {
        if (slideKey === ReservedComponent.Header) {
            return headerInputMap[inputKey] ?? null
        }

        return wizardInputMap[slideKey]?.[inputKey] ?? null;
    }

    const fetchSlideInputValues = (slideKey) => {
        slideKey = slideKey ?? currentSlideKey.value;
        if (slideKey === ReservedComponent.Header) {
            return headerInputMap;
        }

        return wizardInputMap[slideKey];
    }

    const setSlideInputValue = (slideKey, inputKey, newValue, allowCreateMissing = false) => {
        if (slideKey === ReservedComponent.Header) {
            if (inputKey in headerInputMap || allowCreateMissing) {
                headerInputMap[inputKey] = newValue;
            }
        }
        else {
            if (wizardInputMap[slideKey] && (inputKey in wizardInputMap[slideKey] || allowCreateMissing)) {
                return wizardInputMap[slideKey][inputKey] = newValue;
            }
        }
    }

    const getPayloadKey = (slideKey = null) => {
        slideKey = slideKey ?? currentSlideKey.value;

        return configuration.value.find(slide => slide.key === slideKey)?.payload ?? null;
    }

    const resetInputs = () => {
        initializeInputs();
        markAllSlidesValid(false);
        setToFirstSlide();
    }

    const loadInputs = (loadInputValues) => {
        resetInputs();
        const validSlideKeys = Object.keys(slideMap.value);
        for (const slideKey of validSlideKeys) {
            if (slideKey in loadInputValues) {
                wizardInputMap[slideKey] = loadInputValues[slideKey];
            }
        }
        for (const baseKey in loadInputValues) {
            if (baseKey in headerInputMap) {
                headerInputMap[baseKey] = loadInputValues[baseKey];
            }
        }

        markAllSlidesValid(true);
    }

    const resetWizard = () => {
        slideCompletionMap.value = {};
        resetInputs();
    }

    return {
        initialized,
        currentSlideKey,
        currentSlideIndex,
        currentSlideComponent,
        headerInputMap,
        wizardInputMap,
        validationService,
        slideNameMap,
        headerComponent,
        headerConfiguration,
        onFinalIncompleteSlide,
        transformerService,

        initialize,
        getCurrentSlideComponent,
        getInitialSlideData,
        getInitialHeaderData,
        checkInputsAreMapped,
        updateWizardInput,
        updateWizardHeader,
        getInputName,
        getSlideName,
        validateSlide,
        setToFirstSlide,
        nextSlide,
        previousSlide,
        goToSlide,
        isSlideValid,
        isFinalSlide,
        fetchInputValue,
        fetchSlideInputValues,
        getPayloadKey,
        validateWizard,
        fetchReservedComponents,
        setSlideInputValue,
        resetInputs,
        loadInputs,
        resetWizard,
    }
});
