import axios from 'axios';

export default class Api {
    constructor(baseUrl, baseEndpoint, baseVersion) {
        this.baseUrl = baseUrl;
        this.baseEndpoint = baseEndpoint;
        this.baseVersion = baseVersion;
    }
    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/${this.baseVersion}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }
    static make() {
        return new Api('internal-api','billing/credits/', 'v1');
    }

    getCreditBalances(companyId, billingProfileId) {
        return this.axios().get(`${companyId}/balances`, {
            params: {
                billing_profile_id: billingProfileId
            }
        })
    }

    getCredits(companyId) {
        return this.axios().get(`${companyId}/all`)
    }

    expireCredit(companyId, creditId) {
        return this.axios().patch(`${companyId}/expire/${creditId}`, {
            company_id: companyId,
            credit_id: creditId
        })
    }

    extendCredit(companyId, creditId, newDate) {
        return this.axios().patch(`${companyId}/extend/${creditId}`, {
            company_id: companyId,
            credit_id: creditId,
            new_date: newDate
        })
    }
    applyCredit(companyId, newCredit) {

        return this.axios().post(`${companyId}`, {
            ...newCredit,
            company_id: companyId,
        })
    }



}
