import axios from 'axios';
import {REQUEST} from "../../../../constants/APIRequestKeys";

const VERSION = 1
const BASE_URL = 'internal-api'
const BASE_ENDPOINT =
    {
        DEFAULT: '',
        LEAD_PROCESSING: 'lead-processing',
        SALES_BAIT_MANAGEMENT: 'sales-bait-management',
        COMPANIES: 'companies',
        TASKS: 'tasks',
        NON_PURCHASING_SERVICE_AREA: 'non-purchasing-service-areas',
        ROLE_PERMISSION_MANAGEMENT: 'roles-permissions-management',
        GLOBAL_CONFIGURATION_MANAGEMENT: 'global-configurations-management',
        LEADS: 'leads',
        SEARCH: 'search',
        EDITORS: 'editors',
        WYSIWYG: 'wysiwyg',
        USERS: 'users',
        EXTERNAL_AUTH: 'external-auth',
        BUNDLES: 'bundles',
        BUNDLE_INVOICES: 'bundle-invoices',
        OPPORTUNITY_NOTIFICATIONS: 'opportunity-notifications',
        OPPORTUNITY_NOTIFICATIONS_CONFIG: 'opportunity-notifications-config',
        RULESETS: 'rulesets',
        MISSED_PRODUCTS: 'missed-products',
        EXPERT_REVIEWS: 'expert-reviews',
        PHONES: 'phones',
        ACTIVITY_LOGS: 'activity-logs',
    }

export default class SharedApiService {

    constructor(baseUrl, baseEndpoint, version) {
        this.baseUrl = baseUrl;
        this.baseEndpoint = baseEndpoint;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    static make(version) {
        return new SharedApiService(BASE_URL, BASE_ENDPOINT.DEFAULT, version ?? VERSION);
    }

    static getBaseUrl() {
        return BASE_URL;
    }

    static getVersion() {
        return VERSION;
    }

    static getBaseEndpoints() {
        return BASE_ENDPOINT;
    }

    getIndustries() {
        this.baseEndpoint = BASE_ENDPOINT.DEFAULT;
        return this.axios().get('/reference/industries');
    }

    getOdinIndustries() {
        this.baseEndpoint = BASE_ENDPOINT.DEFAULT;
        return this.axios().get('/reference/odin-industries');
    }

    getEmailTemplateTypes() {
        this.baseEndpoint = BASE_ENDPOINT.DEFAULT;
        return this.axios().get(`/reference/email-template-types`);
    }

    getCountries() {
        this.baseEndpoint = BASE_ENDPOINT.DEFAULT;
        return this.axios().get(`/reference/countries`);
    }

    getStates() {
        this.baseEndpoint = BASE_ENDPOINT.DEFAULT;
        return this.axios().get(`/reference/states`);
    }

    getSaleStatusTypes() {
        this.baseEndpoint = BASE_ENDPOINT.DEFAULT;
        return this.axios().get(`/reference/sale-statuses`);
    }

    getCompanyMetrics(companyId) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().get(`/${companyId}/metrics/ppc-spend-data`)
    }

    getBudgetUsage(companyId) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().get(`/${companyId}/budget`)
    }

    resetCrmRejections(companyId) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;

        return this.axios().get(`/${companyId}/reset-crm-rejections`);
    }

    getCompanyOptInNames(companyId)  {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().get(`/${companyId}/opt-in-names`);
    }

    updateCompanyOptInNames(companyId, optInNames)  {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().post(`/${companyId}/opt-in-names`, {
            opt_in_names: optInNames
        });
    }

    setActiveOptInName(companyId, optInNameId)  {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().patch(`/${companyId}/opt-in-names/${optInNameId}/set-active-opt-in-name`);
    }

    deleteActiveOptInName(companyId)  {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().patch(`/${companyId}/opt-in-names/delete-active-opt-in-name`);
    }

    setCampaignActiveOptInName(companyId, campaignId, optInNameId)  {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().patch(`/${companyId}/campaigns/${campaignId}/opt-in-names/${optInNameId}/set-active-opt-in-name`);
    }

    deleteCampaignActiveOptInName(companyId, campaignId)  {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().patch(`/${companyId}/campaigns/${campaignId}/opt-in-names/delete-active-opt-in-name`);
    }

    getTimezones(dst = false) {
        this.baseEndpoint = BASE_ENDPOINT.DEFAULT;
        return this.axios().get(`/reference/timezones`, {params: {dst: dst}});
    }

    getCounties(stateKey) {
        this.baseEndpoint = BASE_ENDPOINT.DEFAULT;
        return this.axios().get(`/reference/states/${stateKey}/counties`);
    }

    getCountiesByStates(states) {
        this.baseEndpoint = BASE_ENDPOINT.DEFAULT;
        return this.axios().get(`/reference/counties`, {params: { states: states}});
    }

    getCitiesByState(stateKey) {
        this.baseEndpoint= BASE_ENDPOINT.DEFAULT;
        return this.axios().get(`reference/states/${stateKey}/cities`);
    }

    getIndustryServices({ industryId } = {}) {
        this.baseEndpoint = BASE_ENDPOINT.DEFAULT;
        return this.axios().get('/reference/industry-services', { params: { industry_id: industryId } });
    }

    /**
     * @param {object} industries
     * @returns {Promise<AxiosResponse<any>>}
     */
    getServicesAgainstIndustries(industries) {
        this.baseEndpoint = BASE_ENDPOINT.DEFAULT;
        return this.axios().get('/reference/industries-services', {
            params: {
                industries: industries
            }
        });
    }

    /**
     * @returns {Promise<AxiosResponse<any>>}
     */
    allServicesByIndustry() {
        this.baseEndpoint = BASE_ENDPOINT.DEFAULT;
        return this.axios().get('/reference/all-services-by-industry');
    }

    getGlobalTypes() {
        this.baseEndpoint = BASE_ENDPOINT.DEFAULT;
        return this.axios().get('/reference/global-types');
    }

    getStatistics(queryString = '') {
        this.baseEndpoint = BASE_ENDPOINT.SALES_BAIT_MANAGEMENT;
        return this.axios().get(`/customstatistics${queryString}`);
    }


    /**
     * Get company contacts by companyId.
     * @param {number} companyId
     * @return Promise<AxiosResponse>
     * @throws Error
     */
    getCompanyContacts(companyId) {
        if (typeof companyId != "number") {
            throw new TypeError(`"companyId" must be a number. ${companyId} passed.`);
        }

        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().get(`/${companyId}/contacts`).catch(e => {
            throw `Could not get contacts from ${companyId}. Error: ${e.message}`;
        });
    }

    createCompanyContact(companyId, payload) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().post(`/${companyId}/contacts`, payload);
    }


    getCompanyActions(companyId, params = {}) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().get(`/${companyId}/actions`, {
            params
        });
    }

    toggleActionPin(actionId, companyId) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().patch(`${companyId}/actions/pin/${actionId}`);
    }

    toggleContactPin(contactId, companyId) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().patch(`${companyId}/contacts/pin/${contactId}`);
    }

    getCompanyInvoicesSummary(companyId) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().get(`/${companyId}/invoices-summary`)
    }

    getCompanyInvoices(companyId) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().get(`/${companyId}/invoices`)
    }

    getMinimalTaskCounts(companyId) {
        this.baseEndpoint = BASE_ENDPOINT.TASKS;
        return this.axios().get('/minimal-counts', {params: {
            company_id: companyId
        }});
    }

    getAssociatedProducts(companyId, invoiceId) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().get(`${companyId}/invoice/products/${invoiceId}`)
    }

    getCompanyLeads(companyId, params = {}) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().get(`/${companyId}/leads`, {
            params: params
        });
    }

    getCompanyOverview(companyId) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().get(`/${companyId}/overview`);
    }

    getCompanyStatuses() {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().get(`/company-statuses`);
    }

    searchCompanies(page = 1, params = {}) {
        params.page = page;

        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;

        return this.axios().get('/search', {
            params: params
        })
    }

    getNonPurchasingCompaniesInCountyByZipCode(industry, zipCode, taskId = null, filters = []) {
        this.baseEndpoint = BASE_ENDPOINT.NON_PURCHASING_SERVICE_AREA;

        let params = {
            zip_code: zipCode,
            industry: industry,
            filters,
        };

        if (taskId) params.task_id = taskId;

        return this.axios().get(`/companies/zip-code`, {
            params: params
        });
    }

    getNonPurchasingCompaniesByCounty(industry, countyKey, stateKey, filters = []) {
        this.baseEndpoint = BASE_ENDPOINT.NON_PURCHASING_SERVICE_AREA;

        let params = {
            county_key: countyKey,
            state_key: stateKey,
            industry: industry,
            filters,
        };

        return this.axios().get(`/companies/county`, {
            params: params
        });
    }
    getNonPurchasingCompaniesCount(industry, countyKey, stateKey) {
        this.baseEndpoint = BASE_ENDPOINT.NON_PURCHASING_SERVICE_AREA;

        let params = {
            county_key: countyKey,
            state_key: stateKey,
            industry: industry
        };

        return this.axios().get(`/companies/count/county`, {
            params: params
        });
    }

    nonPurchasingCompaniesFilteredByAmountOfLeadsPurchased = {
        /**
         * Builds params for getNonPurchasingCompaniesFilteredByAmountOfLeadsPurchased.
         * @param {string} industry
         * @param {string} zipCode
         * @param {number} taskId
         * @param {number} filterByLeadPurchasedAmountTimestamp
         * @param {number} filterByLeadPurchasedAmountFirstValue
         * @param {?number} filterByLeadPurchasedAmountSecondValue
         * @param {string} filterByLeadPurchasedAmountFirstOperator
         * @param {?string} filterByLeadPurchasedAmountSecondOperator
         * @param {?"and"|"or"} filterByLeadPurchasedAmountLogical
         * @return {{filterByLeadPurchasedAmountFirstValue: number, filterByLeadPurchasedAmountFirstOperator: "greaterThan"|"lessThan"|"greaterThanOrEqualTo"|"lessThanOrEqualTo"|"equalTo", filterByLeadPurchasedAmountActive: boolean}}
         */
        buildParams: (industry, zipCode, taskId, filterByLeadPurchasedAmountTimestamp, filterByLeadPurchasedAmountFirstValue, filterByLeadPurchasedAmountFirstOperator, filterByLeadPurchasedAmountLogical = null, filterByLeadPurchasedAmountSecondValue = null, filterByLeadPurchasedAmountSecondOperator = null) => {

            let params = {
                filterByLeadPurchasedAmountActive: true,
                filterByLeadPurchasedAmountFirstValue,
                filterByLeadPurchasedAmountFirstOperator,
                filterByLeadPurchasedAmountTimestamp,
                industry: industry,
                zip_code: zipCode
            };

            if (filterByLeadPurchasedAmountLogical) {
                params.filterByLeadPurchasedAmountLogical = filterByLeadPurchasedAmountLogical;

                if (filterByLeadPurchasedAmountSecondValue) {
                    params.filterByLeadPurchasedAmountSecondValue = filterByLeadPurchasedAmountSecondValue;
                }

                if (filterByLeadPurchasedAmountSecondOperator) {
                    params.filterByLeadPurchasedAmountSecondOperator = filterByLeadPurchasedAmountSecondOperator;
                }
            }

            if (taskId) params.task_id = taskId

            return params;
        },
        /**
         * Get non-purchasing companies and filter by amount of leads purchased.
         * @param {string} industry
         * @param {string} zipCode
         * @param {number} taskId
         * @param {number} filterByLeadPurchasedAmountTimestamp
         * @param {number} filterByLeadPurchasedAmountFirstValue
         * @param {?number} filterByLeadPurchasedAmountSecondValue
         * @param {string} filterByLeadPurchasedAmountFirstOperator
         * @param {?string} filterByLeadPurchasedAmountSecondOperator
         * @param {?"and"|"or"} filterByLeadPurchasedAmountLogical
         * @return Promise<AxiosResponse>
         */
        get: (industry, zipCode, taskId, filterByLeadPurchasedAmountTimestamp, filterByLeadPurchasedAmountFirstValue, filterByLeadPurchasedAmountFirstOperator, filterByLeadPurchasedAmountLogical = null, filterByLeadPurchasedAmountSecondValue = null, filterByLeadPurchasedAmountSecondOperator = null) => {
            this.baseEndpoint = BASE_ENDPOINT.NON_PURCHASING_SERVICE_AREA;

            try {
                const params = this.nonPurchasingCompaniesFilteredByAmountOfLeadsPurchased.buildParams(industry, zipCode, taskId, filterByLeadPurchasedAmountTimestamp, filterByLeadPurchasedAmountFirstValue, filterByLeadPurchasedAmountFirstOperator, filterByLeadPurchasedAmountLogical, filterByLeadPurchasedAmountSecondValue, filterByLeadPurchasedAmountSecondOperator);

                return this.axios().get(`/companies/zip-code`, {
                    params: params
                });
            } catch (e) {
                console.error(e);
            }
        },
        buildParamsWithoutZipCode: (industry, countyKey, stateKey, taskId, filterByLeadPurchasedAmountTimestamp, filterByLeadPurchasedAmountFirstValue, filterByLeadPurchasedAmountFirstOperator, filterByLeadPurchasedAmountLogical = null, filterByLeadPurchasedAmountSecondValue = null, filterByLeadPurchasedAmountSecondOperator = null) => {
            let params = {
                filterByLeadPurchasedAmountActive: true,
                filterByLeadPurchasedAmountFirstValue,
                filterByLeadPurchasedAmountFirstOperator,
                filterByLeadPurchasedAmountTimestamp,
                industry: industry,
                county_key: countyKey,
                state_key: stateKey,
            };

            if (filterByLeadPurchasedAmountLogical) {
                params.filterByLeadPurchasedAmountLogical = filterByLeadPurchasedAmountLogical;

                if (filterByLeadPurchasedAmountSecondValue) {
                    params.filterByLeadPurchasedAmountSecondValue = filterByLeadPurchasedAmountSecondValue;
                }

                if (filterByLeadPurchasedAmountSecondOperator) {
                    params.filterByLeadPurchasedAmountSecondOperator = filterByLeadPurchasedAmountSecondOperator;
                }
            }

            if (taskId) params.task_id = taskId

            return params;
        },
        getWithoutZipCode: (industry, countyKey, stateKey, taskId, filterByLeadPurchasedAmountTimestamp, filterByLeadPurchasedAmountFirstValue, filterByLeadPurchasedAmountFirstOperator, filterByLeadPurchasedAmountLogical = null, filterByLeadPurchasedAmountSecondValue = null, filterByLeadPurchasedAmountSecondOperator = null) => {
            this.baseEndpoint = BASE_ENDPOINT.NON_PURCHASING_SERVICE_AREA

            try {
                const params = this.nonPurchasingCompaniesFilteredByAmountOfLeadsPurchased.buildParamsWithoutZipCode(industry, countyKey, stateKey, taskId, filterByLeadPurchasedAmountTimestamp, filterByLeadPurchasedAmountFirstValue, filterByLeadPurchasedAmountFirstOperator, filterByLeadPurchasedAmountLogical, filterByLeadPurchasedAmountSecondValue, filterByLeadPurchasedAmountSecondOperator)

                return this.axios().get(`/companies/county`, {
                    params: params
                })
            } catch (e) {
                console.error(e)
            }
        },
    }

    initCompaniesSearchOptions() {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().get('/search/init');
    }

    searchCompanyNames(nameType, query) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;

        return this.axios().get('/search/company-names', {
            params: {
                nameType: nameType,
                query: query
            }
        });
    }

    searchCompanyNamesAndId(query) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;

        return this.axios().get('/search/company-names-id', {
            params: {
                query: query
            }
        });
    }

    searchUserByNamesAndId(query, filterBasicInfo = true) {
        this.baseEndpoint = BASE_ENDPOINT.USERS;

        return this.axios().get('/search', {
            params: {
                query: query,
                filter_basic_info: filterBasicInfo ? 1 : 0
            }
        });
    }

    getCompanyBudgetUsage(companyId, page = 1, params = {}) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;

        return this.axios().get(`/${companyId}/campaigns/budget-usage?page=${page}`, { params })
    }

    getCompanyLinks(companyId, page = 1 ) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES
        return this.axios().get(`/${companyId}/company-links?page=${page}`)
    }

    deleteCompanyLink(companyId, linkId) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES
        return this.axios().delete(`/${companyId}/delete-link/${linkId}`)
    }

    addCompanyLink(companyId, otherCompany, linkComment) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES
        return this.axios().post(`/${companyId}/new-link`, {
            company_id: companyId,
            other_company_id: otherCompany,
            comment: linkComment
        });
    }

    getPaginatedCompanyUsers(companyId, page = 1) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;

        return this.axios().get(`/${companyId}/users/paginated?page=${page}`);
    }

    getCompanyUsers(companyId) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;

        return this.axios().get(`/${companyId}/users`);
    }

    updateCompanyContact(companyId, payload) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().patch(`/${companyId}/contacts/${payload.id}`, payload);
    }

    getCompanyContact(companyId, contactId) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().get(`/${companyId}/contacts/${contactId}`)
    }

    updateCompanyUser(companyId, userId, payload) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;

        return this.axios().patch(`/${companyId}/users/${userId}`, payload);
    }

    createCompanyUser(companyId, payload) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().post(`/${companyId}/users/`, {
            ...payload,
            new_user: true
        });
    }

    deleteCompanyUser(companyId, userId) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;

        return this.axios().delete(`/${companyId}/users/${userId}`);
    }

    resetCompanyUserPassword(companyId, userId) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().post(`/${companyId}/users/${userId}/reset-password`);
    }

    updateCompanyUserPassword(companyId, userId, payload) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().patch(`/${companyId}/users/${userId}/update-password`, payload);
    }

    getPermissions() {
        this.baseEndpoint = BASE_ENDPOINT.ROLE_PERMISSION_MANAGEMENT;
        return this.axios().get('/permissions');
    }

    createPermission(permission) {
        this.baseEndpoint = BASE_ENDPOINT.ROLE_PERMISSION_MANAGEMENT;
        return this.axios().post('/permissions', {
            name: permission.name
        });
    }

    updatePermission(permission) {
        this.baseEndpoint = BASE_ENDPOINT.ROLE_PERMISSION_MANAGEMENT;
        return this.axios().patch(`/permissions/${permission.id}`, {
            name: permission.name
        });
    }

    getRoles() {
        this.baseEndpoint = BASE_ENDPOINT.ROLE_PERMISSION_MANAGEMENT;
        return this.axios().get('/roles');
    }

    search(query) {
        this.baseEndpoint = BASE_ENDPOINT.SEARCH;
        return this.axios().get(`/${query}`);
    }

    createRole(role) {
        this.baseEndpoint = BASE_ENDPOINT.ROLE_PERMISSION_MANAGEMENT;
        return this.axios().post('/roles', {
            name: role.name,
            permissions: role.permissions
        });
    }

    updateRole(role) {
        this.baseEndpoint = BASE_ENDPOINT.ROLE_PERMISSION_MANAGEMENT;
        return this.axios().patch(`/roles/${role.id}`, {
            name: role.name,
            permissions: role.permissions
        });

    }

    syncPermissions(role) {
        this.baseEndpoint = BASE_ENDPOINT.ROLE_PERMISSION_MANAGEMENT;
        return this.axios().patch(`/roles/${role.id}/sync-permissions`, {
            name: role.name,
            permissions: role.permissions
        });
    }

    getRoleOptions() {
        this.baseEndpoint = BASE_ENDPOINT.ROLE_PERMISSION_MANAGEMENT;
        return this.axios().get(`/role-options`);
    }

    getPermissionOptions() {
        this.baseEndpoint = BASE_ENDPOINT.ROLE_PERMISSION_MANAGEMENT;
        return this.axios().get(`/permission-options`);
    }

    getUserRolesAndPermissions() {
        this.baseEndpoint = BASE_ENDPOINT.USERS;
        return this.axios().get('/roles-permissions');
    }

    getCompaniesSoldTo(leadId) {
        this.baseEndpoint = BASE_ENDPOINT.LEADS;
        return this.axios().get(`/${leadId}/companies-sold-to`);
    }

    getUnsoldLeadsInCounty(countyKey, stateKey, industry, days) {
        this.baseEndpoint = BASE_ENDPOINT.LEADS;
        return this.axios().get('/unsold-leads-in-county', {
            params: {
                county_key: countyKey,
                state_key: stateKey,
                industry: industry,
                days: days
            }
        });
    }

    getUndersoldLeadsInCounty(countyKey, stateKey, industry, days) {
        this.baseEndpoint = BASE_ENDPOINT.LEADS;
        return this.axios().get('/undersold-leads-in-county', {
            params: {
                county_key: countyKey,
                state_key: stateKey,
                industry: industry,
                days: days
            }
        });
    }

    getUnsoldLeadsInCountyByZipCode(zipCode, industry, days) {
        this.baseEndpoint = BASE_ENDPOINT.LEADS;
        return this.axios().get('/unsold-leads-in-county-by-zip-code', {
            params: {
                zip_code: zipCode,
                industry: industry,
                days: days
            }
        });
    }

    getUndersoldLeadsInCountyByZipCode(zipCode, industry, days) {
        this.baseEndpoint = BASE_ENDPOINT.LEADS;
        return this.axios().get('/undersold-leads-in-county-by-zip-code', {
            params: {
                zip_code: zipCode,
                industry: industry,
                days: days
            }
        });
    }

    getLeadStatuses() {
        this.baseEndpoint = BASE_ENDPOINT.LEADS;
        return this.axios().get('/statuses')
    }

    getLeadCategories() {
        this.baseEndpoint = BASE_ENDPOINT.LEADS;
        return this.axios().get('/categories')
    }

    getUsers(filterBasicInfo = true) {
        this.baseEndpoint = BASE_ENDPOINT.DEFAULT;
        return this.axios().get('/users', {
            params: {
                filter_basic_info: !!filterBasicInfo
            }
        });
    }

    getActionCategories() {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().get(`/action-categories`);
    }

    updateCompanyBasicInfo(companyId, payload) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;

        return this.axios().patch(`/${companyId}/basic-info`, payload);
    }

    updateSalesStatus(companyId, payload) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;

        return this.axios().patch(`/${companyId}/sales-status`, payload);
    }

    recalculateStatus(companyId) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;

        return this.axios().patch(`/${companyId}/consolidated-status`);
    }

    getProfile(companyId) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;

        return this.axios().get(`/${companyId}/profile`);
    }

    getCompanyLocations(companyId) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().get(`/${companyId}/locations`);
    }

    updateCompanyLocation(companyId, locationId, payload) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().patch(`/${companyId}/locations/${locationId}`, payload);
    }

    createCompanyLocation(companyId, payload) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().post(`/${companyId}/locations/`, payload);
    }

    deleteCompanyLocation(companyId, locationId) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().delete(`/${companyId}/locations/${locationId}`);
    }

    wysiwygEditorUploadFile(payload) {
        this.baseEndpoint = `${BASE_ENDPOINT.EDITORS}/${BASE_ENDPOINT.WYSIWYG}`;
        return this.axios().post('/upload-file', payload, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
    }

    getWebsites() {
        this.baseEndpoint = BASE_ENDPOINT.DEFAULT;

        return this.axios().get('/reference/websites');
    }

    getContractKeys() {
        this.baseEndpoint = BASE_ENDPOINT.DEFAULT;

        return this.axios().get('/reference/contract-keys');
    }

    getAdminCompanies(query, additionalQuery = {}) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().get('/search/admin-company', {
            params: {
                companyName: query,
                company_name: query,
                ...additionalQuery,
            }
        });
    }

    /**
     * @param {number} companyId
     * @returns {Promise<AxiosResponse<any>>}
     * @throws Error
     */
    getSimilarCompanies(companyId)  {
        if (typeof companyId != "number") {
            throw new TypeError(`"companyId" must be a number. ${companyId} passed.`);
        }

        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().get(`/${companyId}/similar-companies`);
    }

    getAccountManagers() {
        this.baseEndpoint = BASE_ENDPOINT.DEFAULT;
        return this.axios().get('/sales/info/account-managers');
    }

    getCampaignStatuses() {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().get('/search/campaign-statuses');
    }

    getPaymentMethods() {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().get('/search/payment-methods');
    }

    getAuthToken() {
        this.baseEndpoint = BASE_ENDPOINT.EXTERNAL_AUTH;

        return this.axios().get('/get-token');
    }

    getBundles() {
        this.baseEndpoint = BASE_ENDPOINT.BUNDLES;
        return this.axios().get('/');
    }

    createBundle(payload) {
        this.baseEndpoint = BASE_ENDPOINT.BUNDLES;
        return this.axios().post(`/`, payload);
    }

    updateBundle(payload, bundleId) {
        this.baseEndpoint = BASE_ENDPOINT.BUNDLES;
        return this.axios().patch(`/${bundleId}`, payload);
    }

    searchBundles(page = 1, params = {}) {
        params.page = page;

        this.baseEndpoint = BASE_ENDPOINT.BUNDLES;

        return this.axios().get('/search', {
            params: params
        })
    }


    searchBundleInvoices(page = 1, params = {}) {
        params.page = page;

        this.baseEndpoint = BASE_ENDPOINT.BUNDLE_INVOICES;

        return this.axios().get('/search', {
            params: params
        })
    }

    getBundleInvoices(bundleId = null) {
        this.baseEndpoint = BASE_ENDPOINT.BUNDLE_INVOICES;
        return this.axios().get(`/`, {
            params: {
                bundleId: bundleId
            }
        });
    }

    getBundleInvoiceHistory(bundleInvoiceId = null) {
        this.baseEndpoint = BASE_ENDPOINT.BUNDLE_INVOICES;
        return this.axios().get(`/history/${bundleInvoiceId}`);
    }

    createBundleInvoice(payload) {
        this.baseEndpoint = BASE_ENDPOINT.BUNDLE_INVOICES;
        return this.axios().post(`/`, payload);
    }

    updateBundleInvoice(payload, bundleInvoiceId) {
        this.baseEndpoint = BASE_ENDPOINT.BUNDLE_INVOICES;
        return this.axios().patch(`/${bundleInvoiceId}`, payload);
    }

    executeBundleInvoiceTransition(transitionName, bundleInvoiceId, options = {}) {
        this.baseEndpoint = BASE_ENDPOINT.BUNDLE_INVOICES;
        return this.axios().patch(`/transition/${bundleInvoiceId}`, {transition: transitionName, ...options});
    }

    /**
     * @param {string} companyName
     * @param {string} companyEntityName
     * @param {string} companyWebsite
     * @param {object} companyIndustries
     * @param {object} companyIndustryServices
     * @returns {Promise<AxiosResponse<any>>}
     */
    createNewCompany(companyName, companyEntityName, companyWebsite, companyIndustries, companyIndustryServices) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;

        return this.axios().post('/', {
            company_name        : companyName,
            company_entity_name : companyEntityName,
            company_website     : companyWebsite,
            industries          : companyIndustries,
            services            : companyIndustryServices,
        });
    }

    getConfigurations(params = {}) {
        this.baseEndpoint = BASE_ENDPOINT.GLOBAL_CONFIGURATION_MANAGEMENT;
        return this.axios().get('/configurations', {params});
    }
    createConfiguration(configuration) {
        this.baseEndpoint = BASE_ENDPOINT.GLOBAL_CONFIGURATION_MANAGEMENT;
        return this.axios().post('/configurations', {
            configuration_key: configuration.configuration_key,
            configuration_payload: configuration.configuration_payload
        });
    }
    updateConfiguration(configuration) {
        this.baseEndpoint = BASE_ENDPOINT.GLOBAL_CONFIGURATION_MANAGEMENT;
        return this.axios().patch(`/configurations/${configuration.id}`, {
            configuration_key: configuration.configuration_key,
            configuration_payload: configuration.configuration_payload
        });
    }
    deleteConfiguration(configurationId) {
        this.baseEndpoint = BASE_ENDPOINT.GLOBAL_CONFIGURATION_MANAGEMENT;
        return this.axios().delete(`/configurations/${configurationId}`);
    }

    getFlowRevisionMetadata() {
        return this.axios().get('/flow-management');
    }

    searchOpportunityNotifications(page = 1, params = {}) {
        params.page = page;

        this.baseEndpoint = BASE_ENDPOINT.OPPORTUNITY_NOTIFICATIONS;

        return this.axios().get('/', {
            params: params
        })
    }

    testOpportunityNotifications(ruleset) {
        this.baseEndpoint = BASE_ENDPOINT.OPPORTUNITY_NOTIFICATIONS;

        return this.axios().post('/test', { ruleset })
    }


    searchOpportunityNotificationConfigs(page = 1, params = {}) {
        params.page = page;

        this.baseEndpoint = BASE_ENDPOINT.OPPORTUNITY_NOTIFICATIONS_CONFIG;

        return this.axios().get('/', {
            params: params
        })
    }

    searchConfigurationsOptions(name) {
        this.baseEndpoint = BASE_ENDPOINT.OPPORTUNITY_NOTIFICATIONS_CONFIG;

        return this.axios().get('/options', {
            params: { name }
        })
    }

    createOpportunityNotificationConfig(payload) {
        this.baseEndpoint = BASE_ENDPOINT.OPPORTUNITY_NOTIFICATIONS_CONFIG;
        return this.axios().post(`/`, payload);
    }

    updateOpportunityNotificationConfig(payload, opNotificationConfigId) {
        this.baseEndpoint = BASE_ENDPOINT.OPPORTUNITY_NOTIFICATIONS_CONFIG;
        return this.axios().patch(`/${opNotificationConfigId}`, payload);
    }

    deleteOpportunityNotificationConfig(opNotificationConfigId) {
        this.baseEndpoint = BASE_ENDPOINT.OPPORTUNITY_NOTIFICATIONS_CONFIG;
        return this.axios().delete(`/${opNotificationConfigId}`);
    }

    dispatchOpNotificationEmails(opNotificationConfigId, companyId) {
        this.baseEndpoint = BASE_ENDPOINT.OPPORTUNITY_NOTIFICATIONS_CONFIG;
        return this.axios().post(`/send/${opNotificationConfigId}`, {
            company_id: companyId,
        });
    }

    getCompanyCountPreview(filterPresetId) {
        this.baseEndpoint = BASE_ENDPOINT.OPPORTUNITY_NOTIFICATIONS_CONFIG;
        return this.axios().post(`/${filterPresetId}/preview-company-count`);
    }

    /**
     * Fetches a list of defined rulesets from the system.
     *
     * @returns {Promise<AxiosResponse<any>>}
     */
    getRulesets(type = undefined) {
        this.baseEndpoint = BASE_ENDPOINT.RULESETS;

        return this.axios().get('/', {
            params: {
                type
            }
        });
    }

    getEmailTemplateOptions() {
        this.baseEndpoint = BASE_ENDPOINT.OPPORTUNITY_NOTIFICATIONS_CONFIG;
        return this.axios().get('/email-template-options');
    }

    getMissedProductsConfig() {
        this.baseEndpoint = BASE_ENDPOINT.MISSED_PRODUCTS;
        return this.axios().get('/configuration');
    }

    saveMissedProductsConfig(data) {
        this.baseEndpoint = BASE_ENDPOINT.MISSED_PRODUCTS;
        return this.axios().post('/configuration', data);
    }

    getEmailTemplatePreview(companyUserId, opportunityConfigurationId) {
        this.baseEndpoint = BASE_ENDPOINT.OPPORTUNITY_NOTIFICATIONS_CONFIG;
        return this.axios().get(`/email-template-preview`, {
            params: {
                company_user_id: companyUserId,
                opportunity_configuration_id: opportunityConfigurationId,
            }
        });
    }

    getTestEmailRecipients() {
        this.baseEndpoint = BASE_ENDPOINT.OPPORTUNITY_NOTIFICATIONS_CONFIG;
        return this.axios().get(`/test-email-recipients`);
    }

    searchConsumerProducts(query) {
        this.baseEndpoint = BASE_ENDPOINT.SEARCH;

        return this.axios().get(`consumer-products`, {
            params: {
                query: query
            }
        });
    }

    searchCompanyCampaigns(companyId, query) {
        this.baseEndpoint = BASE_ENDPOINT.SEARCH;

        return this.axios().get(`company-campaigns/${companyId}`, {
            params: {
                query: query
            }
        });
    }

    /**
     * Fetches logo for the given company.
     *
     * @param {number} companyId
     * @return {Promise<AxiosResponse<any>>}
     */
    getCompanyLogo(companyId) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;

        return this.axios().get(`/${companyId}/logo`);
    }

    /**
     * Handles storing the given payload (file) as a logo against the requested company.
     *
     * @param {number} companyId
     * @param {object} payload
     * @return {Promise<AxiosResponse<any>>}
     */
    saveCompanyLogo(companyId, payload) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;

        return this.axios().post(`${companyId}/logo`, payload, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
    }

    /**
     * Fetches media assets for the given company.
     *
     * @param {number} companyId
     * @return {Promise<AxiosResponse<any>>}
     */
    getCompanyMediaAssets(companyId) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;

        return this.axios().get(`/${companyId}/media-assets`);
    }

    /**
     * Handles storing the given payload (files) as the media assets against the requested company.
     *
     * @param {number} companyId
     * @param {object} payload
     * @return {Promise<AxiosResponse<any>>}
     */
    saveCompanyMediaAssets(companyId, payload) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;

        return this.axios().post(`/${companyId}/media-assets`, payload, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
    }

    /**
     * Handles storing the given YouTube link for the requested company.
     *
     * @param {number} companyId
     * @param {String} url
     * @return {Promise<AxiosResponse<any>>}
     */
    saveCompanyYoutubeLink(companyId, url) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;

        return this.axios().post(`/${companyId}/youtube-asset`, {
            [REQUEST.YOUTUBE_ASSET] : url
        });
    }

    /**
     * Handles removing the given media asset from the requested company.
     *
     * @param {number} companyId
     * @param {number} assetId
     * @return {Promise<AxiosResponse<any>>}
     */
    deleteCompanyMediaAsset(companyId, assetId) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;

        return this.axios().delete(`/${companyId}/media-assets/${assetId}`);
    }

    getExpertReviews(companyId) {
        this.baseEndpoint = BASE_ENDPOINT.EXPERT_REVIEWS;

        return this.axios().get(`${companyId}`)
    }

    getExpertReviewHistory(companyId) {
        this.baseEndpoint = BASE_ENDPOINT.EXPERT_REVIEWS;

        return this.axios().get(`${companyId}/history`)
    }

    addExpertReview(companyId, payload) {
        this.baseEndpoint = BASE_ENDPOINT.EXPERT_REVIEWS;

        return this.axios().post(`${companyId}`, payload)
    }

    updateExpertReview(companyId, expertReviewId, payload) {
        this.baseEndpoint = BASE_ENDPOINT.EXPERT_REVIEWS;

        return this.axios().patch(`${companyId}/${expertReviewId}`, payload)
    }

    deleteExpertReview(companyId, expertReviewId) {
        this.baseEndpoint = BASE_ENDPOINT.EXPERT_REVIEWS;

        return this.axios().delete(`${companyId}/${expertReviewId}`)
    }

    getProducts(companyId) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().get(`/${companyId}/products`);
    }

    getAvailablePhones() {
        this.baseEndpoint = BASE_ENDPOINT.PHONES;

        return this.axios().get(`available`);
    }

    saveUserAction(actionId, userId, subject, message, displayDate = null, tags = null, tagByEmail = false) {
        this.baseEndpoint = BASE_ENDPOINT.USERS;
        return this.axios().post(`user-activity/${userId}/actions`, {
            action_id: actionId,
            subject: subject,
            message: message,
            display_date: displayDate,
            tags: tags,
            tag_by_email: tagByEmail
        });
    }

    getUserActions(userId) {
        this.baseEndpoint = BASE_ENDPOINT.USERS;
        return this.axios().get(`/${userId}/actions`);
    }

    getCompanyConfigurations(companyId) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().get(`/${companyId}/configurations`);
    }
    getCompanyContractApprovals(companyId) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().get(`/${companyId}/contracts-approvals`);
    }

    saveCompanyConfiguration(companyId, payload) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().post(`/${companyId}/configurations`, payload);
    }

    getCompanyUserActions(companyId, companyUserId) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().get(`/${companyId}/company-user-actions/${companyUserId}`);
    }

    getZipCodeTargetingExceptions(companyId) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().get(`/${companyId}/zip-code-targeting-exceptions`);
    }

    updateZipCodeTargetingExceptions(companyId, payload) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().patch(`/${companyId}/zip-code-targeting-exceptions`, {payload});
    }

    getCompanyContracts(companyId, params = {}) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().get(`/${companyId}/company-contracts`, {
            params: params
        });
    }

    companyContractBypassFlag(companyId, newValue) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().patch(`/${companyId}/bypass-contract_signing`, {
            bypass_contract_signing: newValue ? 1 : 0,
        });
    }

    getCompanyUserContractAuditLogs(companyId, companyContractId) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().get(`/${companyId}/company-contracts/${companyContractId}/audit-logs`);
    }

    toggleUnrestrictedZipCodeTargeting(companyId, newValue) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().patch(`/${companyId}/unrestricted-zip-code-targeting`, {
            unrestricted_zip_code_targeting: newValue ? 1 : 0,
        });
    }

    getShadowToken(companyId, isFixr) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().get(`/${companyId}/shadow-token`, {
            params: { is_fixr: isFixr ? 1 : 0 }
        });
    }

    getCompanyCustomerSuccessManagers(companyId) {
        this.baseEndpoint = BASE_ENDPOINT.COMPANIES;
        return this.axios().get(`/${companyId}/customer-success-managers/`);
    }

    getActivityLogs(subjectType, subjectId, events) {
        this.baseEndpoint = BASE_ENDPOINT.ACTIVITY_LOGS;
        return this.axios().get('/', {
            params: {
                subject_type: subjectType,
                subject_id: subjectId,
                events
            }
        });
    }

    createActivityLog(
        logName,
        description,
        subjectType,
        subjectId,
        properties,
    ) {
        this.baseEndpoint = BASE_ENDPOINT.ACTIVITY_LOGS;
        return this.axios().post('/', {
            log_name: logName,
            description: description,
            subject_type: subjectType,
            subject_id: subjectId,
            properties: properties
        })
    }

    getBatchedActivityLogDetails(batchUuid) {
        return this.axios().get(`/${batchUuid}/batch-details`);
    }

    getCompanyBillingVersion(companyId) {
        this.baseEndpoint = '';

        return this.axios().get(`/companies/${companyId}/billing-version`);
    }

    getCompanyDefaultBillingProfile(companyId) {
        this.baseEndpoint = '';

        return this.axios().get(`/companies/${companyId}/default-billing-profile`);
    }

    getOpportunityNotificationConfigTypes() {
        this.baseEndpoint = '';

        return this.axios().get(`opportunity-notifications-config/config-types`);
    }

    updateAmPreAssignment(companyId, amId) {
        return this.axios().patch(`/companies/${companyId}/update-am-pre-assignment`, {amId});
    }

    updateCompanyManagerAssignment(companyId, role, payload) {
        return this.axios().patch(`/companies/${companyId}/company-manager/assignment/${role}`, payload);
    }

    getStatesWithCounties(){
        this.baseEndpoint = '';

        return this.axios().get('locations/states-with-counties')
    }
}
