import SharedApiService from "./api";

const ENDPOINT_CONSUMER_PRODUCTS = 'consumer-products';
const ENDPOINT_PRODUCTS = 'products';

export default class ConsumerApiService extends SharedApiService {

    static make() {
        return new ConsumerApiService(
            SharedApiService.getBaseUrl(),
            `${SharedApiService.getBaseEndpoints().LEAD_PROCESSING}`,
            SharedApiService.getVersion()
        );
    }

    getConsumerProductContactInfo(consumerProductId) {
        return this.axios().get(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/contact-info`);
    }

    getConsumerProductBasicInfo(consumerProductId) {
        return this.axios().get(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/basic-info`);
    }

    getConsumerProductsByConsumerProductId(consumerProductId) {
        return this.axios().get(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/other-consumer-products`);
    }

    getConsumerProductUtilityInfo(consumerProductId) {
        return this.axios().get(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/utility-info`);
    }

    getReviewsByConsumerProductId(consumerProductId) {
        return this.axios().get(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/reviews`);
    }

    updateConsumerProductBasicInfo(consumerProductId, updatePayload) {
        return this.axios().patch(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/update-basic-info`, updatePayload);
    }

    updateConsumerProductContactInfo(consumerProductId, updatePayload) {
        return this.axios().patch(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/update-contact-info`, updatePayload);
    }

    updateConsumerProductUtilityInfo(consumerProductId, updatePayload) {
        return this.axios().patch(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/update-utility-info`, updatePayload);
    }

    cancelConsumerProduct(consumerProductId, reason, additionalComments, publicComment) {
        return this.axios().post(`${ENDPOINT_PRODUCTS}/${consumerProductId}/cancel`, {
            reason,
            public_comment: publicComment ? 1 : 0,
            comment: additionalComments,
        });
    }

    markConsumerProductAsPendingReview(consumerProductId, reason, additionalComments, publicComment) {
        return this.axios().post(`${ENDPOINT_PRODUCTS}/${consumerProductId}/mark-as-pending-review`, {
            reason,
            comment: additionalComments,
            public_comment: publicComment ? 1 : 0,
        });
    }

    markConsumerProductAsUnderReview(consumerProductId, reason, additionalComments, publicComment) {
        return this.axios().post(`${ENDPOINT_PRODUCTS}/${consumerProductId}/mark-as-under-review`, {
            reason,
            comment: additionalComments,
            public_comment: publicComment ? 1 : 0,
        });
    }

    skipLeadInAgedQueue(consumerProductId, reason) {
        return this.axios().post(`${ENDPOINT_PRODUCTS}/${consumerProductId}/skip-in-aged-queue`, {reason});
    }

    removeFromQueue(consumerProductId) {
        return this.axios().post(`${ENDPOINT_PRODUCTS}/${consumerProductId}/remove-from-queue`);
    }

    removeFromAffiliateQueue(consumerProductId) {
        return this.axios().post(`${ENDPOINT_PRODUCTS}/${consumerProductId}/remove-from-affiliate-queue`);
    }

    getAgedQueueSkipReasons(consumerProductId) {
        return this.axios().get(`${ENDPOINT_PRODUCTS}/${consumerProductId}/aged-queue-skip-reasons`);
    }

    approveConsumerProduct(consumerProductId, reason, additionalComments, bestTimeToContact, publicComment, data = null) {
        return this.axios().post(`${ENDPOINT_PRODUCTS}/${consumerProductId}/approve`, {
            reason: reason,
            best_time_to_contact: bestTimeToContact,
            public_comment: publicComment ? 1 : 0,
            comment: additionalComments,
            ...(data ?? {})
        });
    }

    getConsumerProductVerification(consumerProductId, service) {
        return this.axios().get(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/${service}/verification`);
    }

    getConsumerProductDateInfo(consumerProductId) {
        return this.axios().get(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/date-info`);
    }

    getConsumerProductLead(consumerProductId) {
        return this.axios().get(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/lead-data`);
    }

    getCombinedConsumerProductLeadByLegacyId(leadId) {
        return this.axios().get(`${ENDPOINT_CONSUMER_PRODUCTS}/${leadId}/combined-lead-data-by-legacy-id`);
    }

    heartbeat(consumerProductId) {
        return this.axios().post(`${ENDPOINT_PRODUCTS}/${consumerProductId}/heartbeat`);
    }

    getConsumerComments(consumerProductId) {
        return this.axios().get(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/comments`)
    }

    addAdminComment(consumerProductId, payload) {
        return this.axios().post(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/comments`, payload)
    }

    getAppointments(consumerProductId) {
        return this.axios().get(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/appointments`);
    }

    deleteAppointment(consumerProductId, appointmentId) {
        return this.axios().delete(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/appointments/${appointmentId}`);
    }

    createAppointment(consumerProductId, type, date, time) {
        return this.axios().post(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/appointments`, {
            appointment_type: type,
            appointment_date: date,
            appointment_time: time,
        });
    }

    getPingPostLogs(consumerProductId) {
        return this.axios().get(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/ping-post/logs`)
    }

    pingPostPingOnly(consumerProductId) {
        return this.axios().get(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/ping-post/ping`)
    }

    pingPost(consumerProductId) {
        return this.axios().get(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/ping-post`)
    }

    getUnsoldProductsInCountyByZipCode(params) {
        console.info("unsold")
        return this.axios().get(`${ENDPOINT_CONSUMER_PRODUCTS}/unsold-products-in-county-by-zip-code`, { params });
    }

    getUndersoldProductsInCountyByZipCode(params) {
        console.info("undersold")
        return this.axios().get(`${ENDPOINT_CONSUMER_PRODUCTS}/undersold-products-in-county-by-zip-code`, { params });
    }

    /**
     * Fetches product assignment for the given consumer product.
     *
     * @param {number} consumerProductId
     * @return {Promise<AxiosResponse<any>>}
     */
    getProductAssignments(consumerProductId) {
        return this.axios().get(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/assignments`);
    }

    /**
     * Fetches product assignments (existing and proposed) for the given consumer product of future campaign
     * @param consumerProductId
     * @returns {Promise<AxiosResponse<any>>}
     */
    getAssignments(consumerProductId) {
        return this.axios().get(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/future-campaigns/assignments`);
    }

    getPricesForCampaigns(consumerProductId, params) {
        return this.axios().get(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/future-campaigns/get-price-for-campaigns`, {params});
    }

    allocate(consumerProductId, data) {
        return this.axios().post(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/future-campaigns/allocate`, data);
    }

    saveSaleType(consumerProductId, productAssignmentId, saleType) {
        return this.axios().patch(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/assignments/${productAssignmentId}/sale-type`, {
            sale_type: saleType
        });
    }

    getAffiliateLeadInfo(consumerProductId) {
        return this.axios().get(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/affiliate-info`);
    }

    saveAffiliateManualTrackingInfo(consumerProductId, params) {
        return this.axios().post(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/affiliate-manual-tracking`, params);
    }

    getWatchdogVideoUrl(consumerProductId) {
        return this.axios().get(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/watchdog`);
    }

    lockConsumerProduct(consumerProductId) {
        return this.axios().patch(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/lock`);
    }

    updateComment(consumerProductId, payload) {
        return this.axios().patch(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/comments`, payload);
    }
    getRelatedActivityForComment(consumerProductId, commentId) {
        return this.axios().get(`${ENDPOINT_CONSUMER_PRODUCTS}/${consumerProductId}/comments/related-activity`, {
            params: { id: commentId }
        });
    }
}
