<template>
    <div
        class="border-y px-8 py-4 flex flex-col"
        :class="{'bg-light-background ': !darkMode, 'bg-dark-background': darkMode}"
    >
        <div class="flex justify-between mb-2">
            <div class="text-lg font-bold ">
                Chargebacks
            </div>
        </div>
        <div class="grid grid-cols-6 border-b pb-1 font-semibold text-xs text-slate-500 uppercase">
            <div>Id</div>
            <div>Reason</div>
            <div>Status</div>
            <div>Amount</div>
            <div>Created at</div>
            <div>Updated at</div>
        </div>
        <div v-if="invoiceChargebackStore.invoiceChargebacks.length === 0"
             class="flex justify-center items-center py-2 text-xs font-semibold text-slate-500"
        >
            No chargebacks
        </div>
        <div v-else v-for="item in invoiceChargebackStore.invoiceChargebacks"
             class="grid grid-cols-6 text-xs font-semibold border-b items-center py-4">
            <div>{{ item.id }}</div>
            <div>{{ item.reason }}</div>
            <div>{{ item.status }}</div>
            <div>{{ item.amount }}</div>
            <div>{{ item.created_at }}</div>
            <div>{{ item.updated_at }}</div>
        </div>
        <div class="flex uppercase text-sm font-semibold mt-2">
            <div class="mr-2">
                Total Value:
            </div>
            <div class="text-red-500">
                ${{ invoiceChargebackStore.invoiceChargebacksAggregate.total_chargeback / 100 }}
            </div>
        </div>
    </div>
</template>
<script>
import {useInvoiceChargebackStore} from "../../../stores/invoice/invoice-chargeback.js";
import CustomButton from "../Shared/components/CustomButton.vue";
import CustomInput from "../Shared/components/CustomInput.vue";
import Dropdown from "../Shared/components/Dropdown.vue";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import InvoiceItem from "./components/CreateNewInvoice/InvoiceItem.vue";

export default {
    name: 'InvoiceChargebackList',
    components: {InvoiceItem, SimpleIcon, Dropdown, CustomInput, CustomButton},
    props: {
        invoiceId: {
            type: Number,
            required: true
        },
        darkMode: {
            type: Boolean,
            required: false
        }
    },
    data() {
        return {
            invoiceChargebackStore: useInvoiceChargebackStore()
        }
    },
    mounted() {
        this.invoiceChargebackStore.getInvoiceChargebacks(this.invoiceId)
    }
}
</script>
