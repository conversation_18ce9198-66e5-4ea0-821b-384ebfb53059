<template>
    <div v-for="status in transactionStatuses" class="flex flex-wrap gap-2">
        <badge class="truncate">
            {{status.consolidated_title}}
        </badge>
    </div>
</template>
<script>
import Badge from "../../../Shared/components/Badge.vue";

export default {
    name: "DisplayInvoiceTransactionStatuses",
    components: {Badge},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        transactionStatuses: {
            type: Array,
            default: []
        }
    }
}
</script>
