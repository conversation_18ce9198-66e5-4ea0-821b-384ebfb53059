<template>
    <div
        class="flex items-center w-full max-w-xs p-4 rounded-lg shadow text-white gap-2"
        :class="[colorStyle]"
    >
        <simple-icon
            v-if="notification?.leftIcon"
            :icon="notification.leftIcon.icon"
            :color="notification.leftIcon.color"
            :size="notification.leftIcon.size"
            @click="notification.leftIcon.callback"
            :clickable="!!notification.leftIcon.callback"
        />
        <div class="text-sm flex-1 font-semibold">{{ notification.message }}</div>
        <simple-icon
            v-if="notification?.rightIcon"
            :icon="notification.rightIcon.icon"
            :color="notification.rightIcon.color"
            :size="notification.rightIcon.size"
            @click="notification.rightIcon.callback"
            :clickable="!!notification.rightIcon.callback"
        />
    </div>
</template>
<script>
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";

export default {
    name: 'ToastNotification',
    components: {
        SimpleIcon,
    },
    props: {
        notification: {
            type: Object,
            required: true,
            default: {}
        }
    },
    computed: {
        colorStyle() {
            return {
                green: 'bg-green-500',
                blue: 'bg-blue-500',
                gray: 'bg-gray-500',
                yellow: 'bg-yellow-500',
                red: 'bg-red-500',
            }[this.notification?.color ?? 'blue']
        }
    }
}
</script>
