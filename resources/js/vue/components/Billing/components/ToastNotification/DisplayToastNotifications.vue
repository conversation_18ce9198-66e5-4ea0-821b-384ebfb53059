<template>
    <transition-group
        name="slide-fade"
    >
        <ToastNotification
            v-for="(notification, idx) in toastNotificationStore.notifications"
            class="fixed right-0 m-2 z-50 transition-opacity ease-in duration-500"
            :style="[calculatePosition(idx)]"
            :notification="notification"
        />
    </transition-group>
</template>

<script>

import ToastNotification from "./ToastNotification.vue";
import {useToastNotificationStore} from "../../../../../stores/billing/tost-notification.store.js";

export default {
    name: 'DisplayToastNotifications',
    components: {
        ToastNotification
    },

    data() {
        return {
            toastNotificationStore: useToastNotificationStore()
        }
    },

    methods: {
        calculatePosition(idx) {
            return `bottom: ${idx * 75}px`
        }
    }
}
</script>

<style scoped>
.slide-fade-enter-active {
    transition: all .3s ease;
}
.slide-fade-leave-active {
    transition: all .3s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}
.slide-fade-enter, .slide-fade-leave-to {
    transform: translateX(100px);
    opacity: 0;
}
</style>
