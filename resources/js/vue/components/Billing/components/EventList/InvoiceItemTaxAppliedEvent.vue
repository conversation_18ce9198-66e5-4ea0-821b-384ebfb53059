<template>
    <event-side-line-icon :dark-mode="darkMode"/>
    <p class="text-xs font-semibold text-gray-400">{{totalEvents}}</p>
    <p class="text-xs font-semibold text-gray-400">x</p>
    <p class="flex-auto py-0.5 text-xs leading-5 text-gray-500">
        <span class="font-medium text-gray-900"> {{ title }} </span>
        <time class="flex-none py-0.5 text-xs leading-5 text-gray-500 ml-2">{{ daysAgo + ' (' + createdAt + ')' }}</time>
    </p>
</template>
<script>

import EventSideLineIcon from "./EventSideLineIcon.vue";

export default {
    name: "InvoiceItemTaxAppliedEvent",
    components: {EventSideLineIcon},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        createdAt: {
            type: String,
            default: null
        },
        daysAgo: {
            type: String,
            default: ""
        },
        title: {
            type: String,
            default: ""
        },
        payload: {
            type: Object,
            default: {}
        },
        icon: {
            type: String,
            default: null
        },
        iconColor: {
            type: String,
            default: null
        },
        eventType: {
            type: String,
            default: null,
        },
        totalEvents: {
            type: Number,
            default: 0
        }
    }
}
</script>
