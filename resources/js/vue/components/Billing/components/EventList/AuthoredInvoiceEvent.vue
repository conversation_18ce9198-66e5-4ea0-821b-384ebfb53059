<template>
    <event-side-line-icon :icon="invoiceHelper.EVENT_STYLES?.[this.eventType]?.icon" :icon-color="invoiceHelper.EVENT_STYLES?.[this.eventType]?.color" :dark-mode="darkMode"/>
    <p class="text-xs font-semibold text-gray-400">{{totalEvents}}</p>
    <p class="text-xs font-semibold text-gray-400">x</p>
    <div class="flex gap-1 items-center py-0.5 text-xs leading-5 text-gray-500">
        <span class="font-medium text-gray-900"> {{ title }} </span> by
        <badge
            class="flex items-center"
            color="gray"
            :dark-mode="darkMode"
        >
            <simple-icon
                :dark-mode="darkMode"
                :icon="invoiceHelper.AUTHOR_STYLES[payload.author_details.author_type]?.icon"
                :tooltip="invoiceHelper.AUTHOR_STYLES[payload.author_details.author_type]?.title"
            />
            <div class="ml-1">
                {{payload.author_details.name}}
            </div>
            <div v-if="payload.author_details.author_type !== invoiceHelper.AUTHOR_TYPES.SYSTEM" class="ml-1">
                ({{ payload.author_details.author_id }})
            </div>
        </badge>
        <time class="flex-none py-0.5 text-xs leading-5 text-gray-500">{{ daysAgo + ' (' + createdAt + ')' }}</time>
    </div>
</template>
<script>

import EventSideLineIcon from "./EventSideLineIcon.vue";
import useInvoiceHelper from "../../../../../composables/useInvoiceHelper";
import Badge from "../../../Shared/components/Badge.vue";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../composables/useSimpleIcon";
const invoiceHelper = useInvoiceHelper();
const simpleIcon = useSimpleIcon();

export default {
    name: "AuthoredInvoiceEvent",
    components: {SimpleIcon, Badge, EventSideLineIcon},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        createdAt: {
            type: String,
            default: null
        },
        daysAgo: {
            type: String,
            default: ""
        },
        title: {
            type: String,
            default: ""
        },
        payload: {
            type: Object,
            default: {}
        },
        icon: {
            type: String,
            default: null
        },
        iconColor: {
            type: String,
            default: null
        },
        eventType: {
            type: String,
            default: null,
        },
        totalEvents: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            invoiceHelper,
            simpleIcon,
        }
    }
}
</script>
