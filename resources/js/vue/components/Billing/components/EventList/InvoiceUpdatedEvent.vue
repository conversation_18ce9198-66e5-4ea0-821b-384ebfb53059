<template>
    <event-side-line-icon :dark-mode="darkMode"/>
    <p class="text-xs font-semibold text-gray-400">{{ totalEvents }}</p>
    <p class="text-xs font-semibold text-gray-400">x</p>
    <div class="flex-auto">
        <div class="flex justify-between gap-x-4">
            <div class="flex items-center gap-1 py-0.5 text-xs leading-5 text-gray-500">
                <span class="font-medium text-gray-900">{{ title }}</span> by
                <badge
                    class="flex items-center"
                    color="gray"
                    :dark-mode="darkMode"
                >
                    <simple-icon
                        :dark-mode="darkMode"
                        :icon="invoiceHelper.AUTHOR_STYLES[payload.author_details.author_type]?.icon"
                        :tooltip="invoiceHelper.AUTHOR_STYLES[payload.author_details.author_type]?.title"
                    />
                    <div class="ml-1">
                        {{ payload.author_details.name }}
                    </div>
                    <div v-if="payload.author_details.author_type !== invoiceHelper.AUTHOR_TYPES.SYSTEM" class="ml-1">
                        ({{ payload.author_details.author_id }})
                    </div>
                </badge>
                <time class="flex-none py-0.5 text-xs leading-5 text-gray-500">{{
                        daysAgo + ' (' + createdAt + ')'
                    }}
                </time>
            </div>
            <div @click="show = !show" class="text-xs cursor-pointer font-semibold text-primary-500">
                {{ show ? "Show less" : "Show more" }}
            </div>
        </div>
        <div v-if="show" class="flex gap-2 text-xs leading-6 ">
            <div class="flex flex-col gap-1">
                <div class="flex gap-1" v-for="change in payload.changes">
                    <span>Field <strong>{{ change.key }}</strong> changed from <strong>{{change.old}}</strong> to <strong>{{change.new}}</strong></span>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import EventSideLineIcon from "./EventSideLineIcon.vue";
import useInvoiceHelper from "../../../../../composables/useInvoiceHelper";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import Badge from "../../../Shared/components/Badge.vue";

const invoiceHelper = useInvoiceHelper()
export default {
    name: "InvoiceUpdatedEvent",
    components: {Badge, SimpleIcon, EventSideLineIcon},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        createdAt: {
            type: String,
            default: null
        },
        daysAgo: {
            type: String,
            default: ""
        },
        title: {
            type: String,
            default: ""
        },
        payload: {
            type: Object,
            default: {}
        },
        icon: {
            type: String,
            default: null
        },
        iconColor: {
            type: String,
            default: null
        },
        eventType: {
            type: String,
            default: null,
        },
        totalEvents: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            invoiceHelper,
            show: false,
        }
    },
}
</script>
