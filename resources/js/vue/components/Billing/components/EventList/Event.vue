<template>
    <div :class="[isLast ? 'h-6' : '-bottom-6', 'absolute left-0 top-0 flex w-6 justify-center']">
        <div class="w-px bg-gray-200" />
    </div>
    <slot>
    </slot>
</template>
<script>
import {CheckCircleIcon} from "@heroicons/vue/solid";
import {DateTime} from "luxon";

export default {
    name: "Event",
    components: {CheckCircleIcon},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        isLast: {
            type: Boolean,
            default: false
        },
    }
}
</script>
