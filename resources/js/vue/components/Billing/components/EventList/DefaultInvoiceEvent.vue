<template>
    <div class="relative flex h-6 w-6 flex-none items-center justify-center bg-white">
        <div class="h-1.5 w-1.5 rounded-full bg-gray-100 ring-1 ring-gray-300" />
    </div>
    <p class="text-xs font-semibold text-gray-400">{{totalEvents}}</p>
    <p class="text-xs font-semibold text-gray-400">x</p>
    <p class="flex-auto py-0.5 text-xs leading-5 text-gray-500">
        <span class="font-medium text-gray-900"> {{ title }} </span>
    </p>
    <time class="flex-none py-0.5 text-xs leading-5 text-gray-500">{{ createdAt }}</time>
</template>
<script>

export default {
    name: "DefaultInvoiceEvent",
    components: {},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        createdAt: {
            type: String,
            default: null
        },
        daysAgo: {
            type: String,
            default: ""
        },
        title: {
            type: String,
            default: ""
        },
        payload: {
            type: Object,
            default: {}
        },
        icon: {
            type: String,
            default: null
        },
        iconColor: {
            type: String,
            default: null
        },
        eventType: {
            type: String,
            default: null,
        },
        totalEvents: {
            type: Number,
            default: 0
        }
    }
}
</script>
