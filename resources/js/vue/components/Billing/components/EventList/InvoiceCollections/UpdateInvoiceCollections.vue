<template>
    <event-side-line-icon :icon="simpleIcon.icons.INFORMATION_CIRCLE" :icon-color="simpleIcon.colors.YELLOW" :dark-mode="darkMode"/>
    <div class="flex-auto">
        <div class="flex justify-between gap-x-4">
            <div class="flex items-center gap-1 py-0.5 text-xs leading-5 text-gray-500">
                Invoice collections update by
                <badge
                    class="flex items-center"
                    color="gray"
                    :dark-mode="darkMode"
                >
                    <simple-icon
                        :dark-mode="darkMode"
                        :icon="invoiceHelper.AUTHOR_STYLES[payload.author_details.author_type]?.icon"
                        :tooltip="invoiceHelper.AUTHOR_STYLES[payload.author_details.author_type]?.title ?? 'Item added'"
                    />
                    <div class="ml-1">
                        {{payload.author_details.name}}
                    </div>
                    <div v-if="payload.author_details.author_type !== invoiceHelper.AUTHOR_TYPES.SYSTEM" class="ml-1">
                        ({{ payload.author_details.author_id }})
                    </div>
                </badge>
                <time class="flex-none py-0.5 text-xs leading-5 text-gray-500">{{ daysAgo + ' (' + createdAt + ')' }}</time>
            </div>
            <div @click="show = !show" class="text-xs cursor-pointer font-semibold text-primary-500">{{show ? "Show less" : "Show more"}}</div>
        </div>
        <div v-if="show" class="grid grid-cols-6 text-xs leading-6 text-gray-500">
            <labeled-value label="Status">
                {{payload.recovery_status}}
            </labeled-value>
            <labeled-value label="Recovered">
                {{$filters.centsToFormattedDollars(payload.amount_recovered)}}
            </labeled-value>
        </div>
    </div>
</template>
<script>
import EventSideLineIcon from "../EventSideLineIcon.vue";
import useInvoiceHelper from "../../../../../../composables/useInvoiceHelper";
import SimpleIcon from "../../../../Mailbox/components/SimpleIcon.vue";
import Badge from "../../../../Shared/components/Badge.vue";
import useSimpleIcon from "../../../../../../composables/useSimpleIcon.js";
import LabeledValue from "../../../../Shared/components/LabeledValue.vue";
const invoiceHelper = useInvoiceHelper()
export default {
    name: "UpdateInvoiceCollections",
    components: {LabeledValue, Badge, SimpleIcon, EventSideLineIcon},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        createdAt: {
            type: String,
            default: null
        },
        daysAgo: {
            type: String,
            default: ""
        },
        title: {
            type: String,
            default: ""
        },
        payload: {
            type: Object,
            default: {}
        },
        icon: {
            type: String,
            default: null
        },
        iconColor: {
            type: String,
            default: null
        },
        eventType: {
            type: String,
            default: null,
        },
        totalEvents: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            simpleIcon: useSimpleIcon(),
            invoiceHelper,
            show: false,
        }
    },
}
</script>
