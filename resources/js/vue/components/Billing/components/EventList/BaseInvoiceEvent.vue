<template>
    <div class="w-full flex items-center gap-2">
        <component
            :is="invoiceEvent.component"
            :icon="invoiceEvent.icon"
            :icon-color="invoiceEvent.icon_color"
            :dark-mode="darkMode"
            :payload="eventData.payload"
            :title="eventData.title"
            :days-ago="eventData.days_ago"
            :created-at="eventData.created_at"
            :event-type="eventData.event_type"
            :total-events="eventData.total"
        />
    </div>
</template>
<script>
import useSimpleIcon from "../../../../../composables/useSimpleIcon";
import {markRaw} from "vue";
import DefaultInvoiceEvent from "./DefaultInvoiceEvent.vue";
import InvoiceItemAddedEvent from "./InvoiceItemAddedEvent/InvoiceItemAddedEvent.vue";
import CreateInvoicePdfEvent from "./CreateInvoicePdfEvent.vue";
import InvoiceItemTaxAppliedEvent from "./InvoiceItemTaxAppliedEvent.vue";
import InvoiceStatusUpdated from "./InvoiceStatusUpdated.vue";
import AuthoredInvoiceEvent from "./AuthoredInvoiceEvent.vue";
import InvoiceUpdatedEvent from "./InvoiceUpdatedEvent.vue";
import InvoiceItemUpdatedEvent from "./InvoiceItemUpdatedEvent.vue";
import CreditAppliedToCompanyEvent from "./CreditAppliedToCompanyEvent.vue";
import ActionReviewedEvent from "./ActionApprovalEvent/ActionReviewedEvent.vue";
import ActionRequestedEvent from "./ActionApprovalEvent/ActionRequestedEvent.vue";
import IssueInvoiceToCollections from "./InvoiceCollections/IssueInvoiceToCollections.vue";
import UpdateInvoiceCollections from "./InvoiceCollections/UpdateInvoiceCollections.vue";
import InvoiceChargebackCreated from "./InvoiceChargebacks/InvoiceChargebackCreated.vue";
import InvoiceChargebackUpdated from "./InvoiceChargebacks/InvoiceChargebackUpdated.vue";
const simpleIcon = useSimpleIcon();


export default {
    name: "BaseInvoiceEvent",
    components: {},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        eventData: {
            type: Object,
            required: true,
        }
    },
    computed: {
        invoiceEvent() {
            const invoiceEvents = {
                default: {
                    icon: null,
                    icon_color: null,
                    component: markRaw(DefaultInvoiceEvent)
                },
                invoice_initialized: {
                    icon: null,
                    icon_color: null,
                    component: markRaw(AuthoredInvoiceEvent)
                },
                invoice_item_added: {
                    icon: null,
                    icon_color: simpleIcon.colors.BLACK,
                    component: markRaw(InvoiceItemAddedEvent)
                },
                invoice_created: {
                    icon: null,
                    icon_color: null,
                    component: markRaw(AuthoredInvoiceEvent)
                },
                request_invoice_tax: {
                    icon: null,
                    icon_color: null,
                    component: markRaw(AuthoredInvoiceEvent)
                },
                create_invoice_pdf: {
                    icon: null,
                    icon_color: null,
                    component: markRaw(CreateInvoicePdfEvent)
                },
                invoice_item_tax_applied: {
                    icon: null,
                    icon_color: null,
                    component: markRaw(InvoiceItemTaxAppliedEvent)
                },
                invoice_status_updated: {
                    icon: null,
                    icon_color: null,
                    component: markRaw(InvoiceStatusUpdated)
                },
                invoice_charge_request: {
                    icon: null,
                    icon_color: null,
                    component: markRaw(AuthoredInvoiceEvent)
                },
                invoice_charge_request_attempted: {
                    icon: null,
                    icon_color: null,
                    component: markRaw(AuthoredInvoiceEvent)
                },
                invoice_charge_request_success: {
                    icon: null,
                    icon_color: null,
                    component: markRaw(AuthoredInvoiceEvent)
                },
                invoice_charge_request_failed: {
                    icon: null,
                    icon_color: null,
                    component: markRaw(AuthoredInvoiceEvent)
                },
                invoice_updated: {
                    icon: null,
                    icon_color: null,
                    component: markRaw(InvoiceUpdatedEvent)
                },
                invoice_item_updated: {
                    icon: null,
                    icon_color: null,
                    component: markRaw(InvoiceItemUpdatedEvent)
                },
                invoice_item_deleted: {
                    icon: null,
                    icon_color: null,
                    component: markRaw(AuthoredInvoiceEvent)
                },
                credit_applied_to_company: {
                    icon: null,
                    icon_color: null,
                    component: markRaw(CreditAppliedToCompanyEvent)
                },
                invoice_action_requested: {
                    icon: null,
                    icon_color: null,
                    component: markRaw(ActionRequestedEvent)
                },
                invoice_action_request_reviewed: {
                    icon: null,
                    icon_color: null,
                    component: markRaw(ActionReviewedEvent)
                },
                issue_invoice_to_collections: {
                    icon: null,
                    icon_color: null,
                    component: markRaw(IssueInvoiceToCollections)
                },
                update_invoice_collections: {
                    icon: null,
                    icon_color: null,
                    component: markRaw(UpdateInvoiceCollections)
                },
                invoice_charge_dispute_created: {
                    icon: null,
                    icon_color: null,
                    component: markRaw(InvoiceChargebackCreated)
                },
                invoice_charge_dispute_updated: {
                    icon: null,
                    icon_color: null,
                    component: markRaw(InvoiceChargebackUpdated)
                },
            }

            return invoiceEvents[this.eventData.event_type] ?? invoiceEvents.default
        }
    }
}
</script>
