<template>
    <div class="flex items-center gap-1">
        <p @click="handleClick" class="font-medium text-blue-500 cursor-pointer">Lead {{itemDetails.consumer_product_id}}</p>
        <p>added</p>
    </div>
</template>
<script>
export default {
    name: "ProductAddedEventTitle",
    props: {
        itemDetails: {
            type: Object,
            default: {}
        }
    },
    methods: {
        handleClick(){
            window.open(`/consumer-product?consumer_product_id=${this.itemDetails.consumer_product_id}`)
        }
    },
}
</script>
