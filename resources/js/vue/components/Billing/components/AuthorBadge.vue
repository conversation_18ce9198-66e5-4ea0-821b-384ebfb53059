<template>
    <badge
        class="flex items-center"
        color="gray"
        :dark-mode="darkMode"
    >
        <simple-icon
            :dark-mode="darkMode"
            :icon="authorStyles.icon"
            :tooltip="authorStyles.title"
        />
        <div class="ml-1">
            {{ author.name }}
        </div>
        <div v-if="author.type !== invoiceHelper.AUTHOR_TYPES.SYSTEM" class="ml-1">
            ({{ author.id }})
        </div>
    </badge>
</template>
<script>
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import Badge from "../../Shared/components/Badge.vue";
import useInvoiceHelper from "../../../../composables/useInvoiceHelper.js";

export default {
    name: "AuthorBadge",
    components: {Badge, SimpleIcon},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        author: {
            type: Object,
            default: {}
        }
    },
    computed: {
        invoiceHelper() {
            return useInvoiceHelper()
        },
        authorStyles() {
            return this.invoiceHelper.AUTHOR_STYLES[this.author.type]
        }
    }
}
</script>


<style scoped>

</style>
