import axios from 'axios';

export default class Api {
    constructor(baseUrl, baseEndpoint, baseVersion) {
        this.baseUrl = baseUrl;
        this.baseEndpoint = baseEndpoint;
        this.baseVersion = baseVersion;
    }
    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/${this.baseVersion}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }
    static make() {
        return new Api('internal-api','billing/invoices', 'v1');
    }

    getInvoiceChargebacks(invoiceId){
        return this.axios().get(`/${invoiceId}/chargebacks`)
    }

    getChargebacks(params){
        return this.axios().get('/chargebacks', {
            params
        })
    }

    getInvoicePayments(invoiceId){
        return this.axios().get(`/${invoiceId}/payments`)
    }
}
