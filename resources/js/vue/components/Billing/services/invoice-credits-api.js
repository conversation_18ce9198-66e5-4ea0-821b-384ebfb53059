import axios from 'axios';

export default class Api {
    constructor(baseUrl, baseEndpoint, baseVersion) {
        this.baseUrl = baseUrl;
        this.baseEndpoint = baseEndpoint;
        this.baseVersion = baseVersion;
    }
    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/${this.baseVersion}/${this.baseEndpoint}/`
        }

        return axios.create(axiosConfig);
    }
    static make() {
        return new Api('internal-api','billing/invoices', 'v1');
    }

    getInvoiceCredits(invoiceId) {
        return this.axios().get(`/${invoiceId}/credits`)
    }

    applyCredit(invoiceId, payload) {
        return this.axios().post(`/${invoiceId}/credits`, payload)
    }
}
