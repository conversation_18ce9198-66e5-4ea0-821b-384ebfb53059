import axios from 'axios';

export default class Api {
    constructor(baseUrl, baseEndpoint, baseVersion) {
        this.baseUrl = baseUrl;
        this.baseEndpoint = baseEndpoint;
        this.baseVersion = baseVersion;
    }
    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/${this.baseVersion}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }
    static make() {
        return new Api('internal-api','billing', 'v1');
    }

    createUpdateInvoice(invoiceData) {
        return this.axios().post('/invoices/create-update', invoiceData)
    }

    getUninvoicedProducts(companyId) {
        return this.axios().get(`/invoices/uninvoiced-products`, {
            params: {
                company_id: companyId
            }
        })
    }

    getInvoice(invoiceId) {
        return this.axios().get(`/invoices/invoice/`, {
            params: {
                invoice_id: invoiceId
            }
        })
    }

    getInvoices(params = {}) {
        return this.axios().get(`/invoices/`, {
            params
        })
    }

    getFilterOptions() {
        return this.axios().get(`/invoices/filters/`)
    }

    getInvoiceEvents(invoiceId) {
        return this.axios().get(`/invoices/invoice/events`, {
            params: {
                invoice_id: invoiceId
            }
        })
    }

    issueInvoiceToCollections(invoiceId) {
        return this.axios().post(`/invoices/collections`, {
            invoice_id: invoiceId
        })
    }

    updateInvoiceCollectionsData(invoiceId, {
        amountRecovered,
        recoveryStatus,
    }) {
        return this.axios().patch(`/invoices/collections`, {
            invoice_id: invoiceId,
            amount_recovered: amountRecovered,
            recovery_status: recoveryStatus,
        })
    }

    listCollectionInvoices(params){
        return this.axios().get(`/invoices/collections`, {
            params
        })
    }

    listWrittenOffInvoices(params){
        return this.axios().get(`/invoices/write-offs`, {
            params
        })
    }

    getCompanyAvailableCredits(companyId){
        return this.axios().get(`/`)
    }

    generateSignedUrl(invoiceId){
        return this.axios().post(`/invoices/${invoiceId}/pdf`)
    }

    getInvoiceLeadsImportData(data){
        return this.axios().post(`/invoices/leads-upload`, data)
    }

    importLeadsViaCsv(data, config){
        return this.axios().post(`/invoices/leads-import`, data, config)
    }
}
