import axios from 'axios';

export default class Api {
    constructor(baseUrl, baseEndpoint, baseVersion) {
        this.baseUrl = baseUrl;
        this.baseEndpoint = baseEndpoint;
        this.baseVersion = baseVersion;
    }
    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/${this.baseVersion}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }
    static make() {
        return new Api('internal-api','companies', 'v2');
    }

    getRevenueGraph(companyId, params) {
        return this.axios().get(`/${companyId}/revenue-graph`, {
            params
        })
    }

    getRevenueSummary(companyId, params) {
        return this.axios().get(`/${companyId}/revenue-summary`, {
            params
        })
    }
}
