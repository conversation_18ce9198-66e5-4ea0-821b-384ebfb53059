<template>
    <div class="flex flex-col gap-2">
        <div class="font-semibold"> Apply credit</div>
        <loading-spinner v-if="loading"/>
        <div v-else class="grid grid-cols-6 gap-3 items-center">
            <labeled-value label="Type">
                <dropdown
                    :options="creditOptions"
                    :dark-mode="darkMode"
                    v-model="invoiceCredit.type"
                    @update:modelValue="handleCreditTypeSelect"
                    placeholder="Type"
                >
                    <template v-slot:option="{option}">
                        <div class="flex items-center gap-1">
                            <p class="flex-1 truncate">{{ option.name }}</p>
                            <badge color="green">{{ $filters.centsToFormattedDollars(option.balance) }}</badge>
                        </div>
                    </template>
                </dropdown>
            </labeled-value>
            <labeled-value label="Amount">
                <div class="flex items-center gap-1">
                    <custom-inline-input
                        v-model="invoiceCredit.amount_in_dollars"
                        @update:modelValue="(val) => invoiceCredit.amount = val * 100"
                        prefix="$"
                    />
                    <p v-if="selectedCreditType" class="text-base">/</p>
                    <p v-if="selectedCreditType" class="text-base">
                        {{ $filters.centsToFormattedDollars(selectedCreditType.balance) }}
                    </p>
                </div>
            </labeled-value>

            <div>
                <CustomButton v-if="showApplyButton" @click="handleApplyCredit" class="mt-2" :disabled="isApplyDisabled">
                    Apply
                </CustomButton>
            </div>
        </div>
        <div class="flex uppercase text-sm font-semibold mt-2">
            <div class="mr-2"> Total outstanding:</div>
            <div class="text-red-500">{{
                    $filters.centsToFormattedDollars(invoiceStore.invoiceTotals.outstanding)
                }}
            </div>
        </div>
    </div>
</template>
<script>
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import {useInvoiceCreditsStore} from "../../../../stores/invoice/invoice-credits.store.js";
import CustomButton from "../../Shared/components/CustomButton.vue";
import CustomInlineInput from "../../Shared/components/CustomInlineInput.vue";
import CustomInlineSelect from "../../Shared/components/CustomInlineSelect.vue";
import DropdownSelector from "../../Shared/components/DropdownSelector.vue";
import Autocomplete from "../../Shared/components/Autocomplete.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import Badge from "../../Shared/components/Badge.vue";
import LabeledValue from "../../Shared/components/LabeledValue.vue";
import {useInvoiceModalStore} from "../../../../stores/invoice/invoice-modal.store.js";
import {useCompanyCreditManagementStore} from "../../../../stores/credit/company-credit-management.store.js";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import {ROLES, useRolesPermissions} from "../../../../stores/roles-permissions.store.js";

const simpleIcon = useSimpleIcon()

export default {
    name: "ApplyCreditToInvoiceModal",
    components: {
        LoadingSpinner,
        LabeledValue,
        Badge,
        Dropdown,
        Autocomplete,
        DropdownSelector,
        CustomInlineSelect,
        CustomInlineInput,
        CustomButton,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        invoiceId: {
            type: Number,
            required: true
        },
        companyId: {
            type: Number,
            required: true
        }
    },
    data() {
        return {
            simpleIcon,
            invoiceCreditsStore: useInvoiceCreditsStore(),
            applyingCredit: false,
            showApplyCreditForm: false,
            selectedCreditType: null,
            invoiceStore: useInvoiceModalStore(),
            creditStore: useCompanyCreditManagementStore(),
            loading: false,
            invoiceCredit: {},
            rolesPermissions: useRolesPermissions()
        }
    },
    methods: {
        handleCreditTypeSelect(type) {
            this.selectedCreditType = type ? this.creditOptions.find(e => e.id === type) : null
        },
        async handleApplyCredit() {
            await this.invoiceCreditsStore.applyCredit(this.invoiceId, this.invoiceCredit)
            await this.invoiceStore.refreshInvoice();
        }
    },

    async mounted() {
        this.loading = true
        await this.creditStore.getBalances(this.companyId)
        this.loading = false
    },

    computed: {
        showApplyButton(){
            return this.rolesPermissions.hasRole(ROLES.FINANCE_OWNER)
        },
        creditOptions() {
            return this.creditStore.balances.map(c => ({
                ...c,
                id: c.type,
                name: c.name
            }))
        },
        isApplyDisabled() {
            const isLoading = this.invoiceCreditsStore.loadingApplyCredit;

            const isGreaterThanOutstanding = this.invoiceCredit.amount
                > this.invoiceStore.invoiceTotals.outstanding;

            const isGreaterThanTotalAvailable = this.invoiceCredit.amount
                > this.selectedCreditType?.balance ?? 0;

            const isAmountLessThanZero = (this.invoiceCredit.amount ?? 0) <= 0

            const noTypeSelected = !this.invoiceCredit.type

            return isLoading
                || isGreaterThanOutstanding
                || isGreaterThanTotalAvailable
                || isAmountLessThanZero
                || noTypeSelected
        }
    }
}
</script>
