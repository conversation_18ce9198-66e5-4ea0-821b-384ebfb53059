<template>
    <div
        class="border-y px-8 py-4 flex flex-col"
        :class="{'bg-light-background ': !darkMode, 'bg-dark-background': darkMode}"
    >
        <div class="flex justify-between mb-2">
            <div class="text-lg font-bold ">
                Credits
            </div>
            <CustomButton v-if="showApplyButton" @click="toggleApplyCreditForm">
                {{ showApplyCreditForm ? 'Cancel' : 'Show apply credit form' }}
            </CustomButton>
        </div>
        <div>
            <apply-credit-to-invoice-modal
                v-if="showApplyCreditForm"
                class="mb-10"
                :dark-mode="darkMode"
                :invoice-id="invoiceId"
                :company-id="companyId"
            />
            <div
                class="text-xs grid border-b pb-1 font-semibold text-slate-500 uppercase grid-cols-4"
            >
                <div>Type</div>
                <div>Amount</div>
                <div>Author</div>
                <div>Date</div>
            </div>
            <loading-spinner v-if="invoiceCreditsStore.loadingCreditsApplied"></loading-spinner>
            <div v-else-if="invoiceCreditsStore.creditsApplied.length === 0"
                 class="flex justify-center items-center py-2 text-xs font-semibold text-slate-500"
            >
                No credits applied
            </div>
            <div v-else v-for="invoiceCredit in invoiceCreditsStore.creditsApplied"
                 class="grid grid-cols-4 text-xs font-semibold border-b items-center py-4">
                <div>{{ invoiceCredit.credit_type }}</div>
                <div>{{ $filters.centsToFormattedDollars(invoiceCredit.amount_applied) }}</div>
                <div>
                    <author-badge :dark-mode="darkMode" :author="invoiceCredit.author" />
                </div>
                <div>{{ invoiceCredit.applied_at }}</div>
            </div>
            <div class="flex uppercase text-sm font-semibold mt-2">
                <div class="mr-2">
                    Total Value:
                </div>
                <div class="text-red-500">
                    {{$filters.centsToFormattedDollars(invoiceCreditsStore.totalCreditsApplied)}}
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import {useInvoiceCreditsStore} from "../../../../stores/invoice/invoice-credits.store.js";
import CustomButton from "../../Shared/components/CustomButton.vue";
import CustomInlineInput from "../../Shared/components/CustomInlineInput.vue";
import CustomInlineSelect from "../../Shared/components/CustomInlineSelect.vue";
import DropdownSelector from "../../Shared/components/DropdownSelector.vue";
import Autocomplete from "../../Shared/components/Autocomplete.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import Badge from "../../Shared/components/Badge.vue";
import LabeledValue from "../../Shared/components/LabeledValue.vue";
import ApplyCreditToInvoiceModal from "./ApplyCreditToInvoiceForm.vue";
import {useCompanyCreditManagementStore} from "../../../../stores/credit/company-credit-management.store.js";
import {useInvoiceModalStore} from "../../../../stores/invoice/invoice-modal.store.js";
import AuthorBadge from "../components/AuthorBadge.vue";
import {ROLES, useRolesPermissions} from "../../../../stores/roles-permissions.store.js";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";

const simpleIcon = useSimpleIcon()

export default {
    name: "InvoiceCreditsModule",
    components: {
        LoadingSpinner,
        AuthorBadge,
        ApplyCreditToInvoiceModal,
        LabeledValue,
        Badge,
        Dropdown,
        Autocomplete,
        DropdownSelector,
        CustomInlineSelect,
        CustomInlineInput,
        CustomButton,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        invoiceId: {
            type: Number,
            required: true
        },
        companyId: {
            type: Number,
            required: true
        },
    },
    data() {
        return {
            simpleIcon,
            invoiceCreditsStore: useInvoiceCreditsStore(),
            applyingCredit: false,
            showApplyCreditForm: false,
            selectedCreditType: null,
            loading: false,
            creditStore: useCompanyCreditManagementStore(),
            invoiceStore: useInvoiceModalStore(),
            rolesPermissions: useRolesPermissions()
        }
    },
    mounted() {
        this.invoiceCreditsStore.resetStore();
        this.invoiceCreditsStore.getCreditsApplied(this.invoiceId)
    },
    computed: {
        showApplyButton(){
            return this.rolesPermissions.hasRole(ROLES.FINANCE_OWNER)
                && this.invoiceStore?.invoiceTotals?.outstanding > 0
                && !this.invoiceStore?.isProcessingPayment
                && !this.invoiceCreditsStore?.loadingCreditsApplied
        },
    },
    methods: {
        toggleApplyCreditForm() {
            this.showApplyCreditForm = !this.showApplyCreditForm

            if (!this.showApplyCreditForm) {
                this.selectedCreditType = null
                this.invoiceCreditsStore.invoiceCredit = {}
            }
        }
    }
}
</script>
