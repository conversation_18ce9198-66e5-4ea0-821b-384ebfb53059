<template>
    <div
        class="border-y px-8 py-4 flex flex-col"
        :class="{'bg-light-background ': !darkMode, 'bg-dark-background': darkMode}"
    >
        <div class="flex justify-between mb-2">
            <div class="text-lg font-bold ">
                Write offs
            </div>
        </div>
        <div
            class="text-xs"
        >
            <div
                class="grid border-b pb-1 font-semibold text-slate-500 uppercase grid-cols-6"
            >
                <div>Date</div>
                <div>User</div>
                <div>Amount</div>
            </div>
            <div class="grid uppercase grid-cols-6 mt-1 items-center">
                <div>{{ invoiceStore.invoiceWriteOffs.date }}</div>
                <div>{{ invoiceStore.invoiceWriteOffs.user_name }}</div>
                <div>{{ $filters.centsToFormattedDollars(invoiceStore.invoiceWriteOffs.amount) }}</div>
            </div>
        </div>
    </div>
</template>
<script>
import {useInvoiceModalStore} from "../../../../stores/invoice/invoice-modal.store.js";

export default {
    name: "InvoiceWriteOff",
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            invoiceStore: useInvoiceModalStore(),
        }
    },
}
</script>
