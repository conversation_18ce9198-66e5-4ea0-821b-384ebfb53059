<template>
    <modal
        no-buttons
        :dark-mode="darkMode"
        :container-classes="`min-h-[65vh]`"
        @close="handleClose"
        no-min-height
    >
        <template v-slot:header>
            <div class="flex items-center gap-2">
                <simple-icon
                    v-if="invoiceId"
                    :clickable="!refreshingInvoiceData"
                    size="w-4 h-4"
                    :class="refreshingInvoiceData ? 'animate-spin' : ''"
                    :dark-mode="darkMode"
                    :icon="simpleIcon.icons.ARROW_PATH"
                    :color="simpleIcon.colors.BLUE"
                    :tooltip="refreshingInvoiceData ? 'Refreshing...' : 'Refresh Invoice Data'"
                    @click="handleRefresh"
                />
                <div class="font-bold">
                    {{ !invoiceId ? "Create New Invoice" : "Invoice #" + invoiceId }}
                </div>
                <simple-icon
                    v-if="invoiceId"
                    clickable
                    size="w-4 h-4"
                    :dark-mode="darkMode"
                    :icon="simpleIcon.icons.LINK"
                    :color="simpleIcon.colors.BLUE"
                    tooltip="Copy Link"
                    @click="handleCopyLink"
                />
            </div>
        </template>
        <template v-slot:content>
            <view-create-invoice-modal-content
                :company-id="companyId"
                :company-name="companyName"
                @invoice-created-updated="handleInvoiceCreated"
                :dark-mode="darkMode"
                :readonly="readonly"
            />
        </template>
    </modal>
</template>
<script>
import Modal from "../Shared/components/Modal.vue";
import {useInvoiceModalStore} from "../../../stores/invoice/invoice-modal.store";
import ViewCreateInvoiceModalContent from "./ViewCreateInvoiceModalContent.vue";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../composables/useSimpleIcon.js";
import useClipboard from "../../../composables/useClipboard.js";
import {useToastNotificationStore} from "../../../stores/billing/tost-notification.store.js";
import useQueryParams from "../../../composables/useQueryParams.js";

const simpleIcon = useSimpleIcon()
export default {
    name: "ViewCreateInvoiceModal",
    components: {SimpleIcon, ViewCreateInvoiceModalContent, Modal},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: Number,
            default: null,
        },
        companyName: {
            type: String,
            default: null
        },
        invoiceId: {
            type: Number,
            default: null,
        },
        readonly: {
            type: Boolean,
            default: null
        }
    },
    created() {
        if (this.invoiceId !== null) {
            this.getInvoiceData()
        }
    },
    data () {
        return {
            queryParamsHelper: useQueryParams(),
            invoiceStore: useInvoiceModalStore(),
            clipboard: useClipboard(),
            toastNotificationStore: useToastNotificationStore(),
            simpleIcon,
            refreshingInvoiceData: false
        }
    },
    emits: ['close','invoice-created-updated'],
    methods: {
        async handleRefresh(){
            if (this.refreshingInvoiceData) {
                return
            }

            this.refreshingInvoiceData = true
            await this.invoiceStore.refreshInvoice()
            this.refreshingInvoiceData = false
        },
        handleInvoiceCreated(invoiceUuid, message) {
            this.invoiceStore.$reset();
            this.$emit('invoice-created-updated', invoiceUuid, message);
        },
        handleClose() {
            this.invoiceStore.$reset();
            this.$emit('close');
        },
        async getInvoiceData() {
            await this.invoiceStore.retrieveInvoiceData(this.invoiceId);
        },
        handleCopyLink() {
            const url = this.queryParamsHelper.mountUrlWithSearchParams({
                invoice_id: this.invoiceId,
                tab: 'Invoices'
            }, this.queryParamsHelper.getCurrentUrl())

            this.clipboard.addToClipboard(url)
            this.toastNotificationStore.notifySuccess(
                "Invoice Link Copied to clipboard"
            )
        }
    }
}
</script>
