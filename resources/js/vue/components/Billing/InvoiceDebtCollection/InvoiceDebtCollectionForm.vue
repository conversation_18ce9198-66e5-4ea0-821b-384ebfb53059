<template>
    <div v-if="totalOutstanding > 0">
        <loading-spinner v-if="loading"/>
        <simple-alert
            v-if="errorMessages.length > 0"
            dismissible
            @dismiss="handleAlertDismiss"
            variant="light-red"
        >
            <template #content>
                {{errorMessages[0]}}
            </template>
        </simple-alert>
        <div class="flex gap-2" v-show="!loading">
            <labeled-value label="Total outstanding">
                <div class="flex items-center gap-1">
                    <p class="text-red-500">{{ $filters.centsToFormattedDollars(totalOutstanding) }}</p>
                </div>
            </labeled-value>
            <labeled-value label="Due on">
                <div class="flex items-center gap-1">
                    <p>{{invoiceStore.dueDate}}</p>
                </div>
            </labeled-value>
            <custom-button
                class="mt-1"
                @click="submitCollectionRequest"
                :disabled="isSubmitDisabled"
            >
                Create
            </custom-button>
        </div>
    </div>
</template>
<script>
import CustomButton from "../../Shared/components/CustomButton.vue";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import LabeledValue from "../../Shared/components/LabeledValue.vue";
import SimpleAlert from "../../Shared/components/SimpleAlert.vue";
import ApiService from "../services/api.js";
import {useInvoiceModalStore} from "../../../../stores/invoice/invoice-modal.store.js";

export default {
    name: 'InvoiceDebtCollectionForm',
    components: {SimpleAlert, LabeledValue, LoadingSpinner, CustomButton},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        invoiceId: {
            type: Number,
            default: false
        },
        totalOutstanding: {
            type: Number,
            required: true,
        },
    },
    data() {
        return {
            loading: false,
            saving: false,
            errorMessages: [],
            api: ApiService.make(),
            invoiceStore: useInvoiceModalStore()
        }
    },
    computed: {
        isSubmitDisabled() {
            return this.saving || this.loading
        }
    },

    methods: {
        handleAlertDismiss() {
            this.errorMessages = []
        },

        async submitCollectionRequest() {
            this.saving = true

            try {
                await this.api.issueInvoiceToCollections(this.invoiceId)
                this.$emit('created');
            } catch (err) {
                console.error(err)
            }

            this.saving = false
        }
    }
}
</script>
