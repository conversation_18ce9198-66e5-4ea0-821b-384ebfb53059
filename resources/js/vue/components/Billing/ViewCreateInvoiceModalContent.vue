<template>
    <div>
        <loading-spinner v-if="invoiceStore.loadingInvoiceRetrieve"/>
        <div v-else
             class="flex flex-col gap-4 relative"
             :class="[
                 loading ? 'blur' : ''
             ]"
        >
            <simple-alert
                v-if="showAlert"
                class="z-100 border shadow-md fixed left-1/2 transform -translate-x-1/2 mt-4"
                variant="light-red"
                :dark-mode="darkMode"
                :content="errorHandler?.message"
                dismissible
                @dismiss="showAlert = false"
            >
                <template v-slot:icon>
                    <simple-icon :dark-mode="darkMode" :icon="simpleIcon.icons.EXCLAMATION_CIRCLE"></simple-icon>
                </template>
            </simple-alert>
            <create-invoice-modal-content-company-data
                :company-name="companyName ?? invoiceStore.companyName"
                :company-id="companyId ?? invoiceStore.company"
                :dark-mode="darkMode"
                :future-invoice-data="futureInvoiceData"
            />
            <create-invoice-modal-content-item-list :dark-mode="darkMode"/>
            <invoice-payments-module
                v-if="invoiceStore.invoiceId"
                :invoice-id="invoiceStore.invoiceId"
                :company-id="invoiceStore.company"
                :dark-mode="darkMode"
            />
            <invoice-credits-module
                v-if="showInvoiceCreditsModule"
                :invoice-id="invoiceStore.invoiceId"
                :company-id="invoiceStore.company"
                :dark-mode="darkMode"
            />
            <invoice-refund-module
                v-if="showInvoiceRefundModule"
                :invoice-id="invoiceStore.invoiceId"
                :dark-mode="darkMode"
                :editable="!invoiceStore.hasPendingAction"
            />
            <invoice-collection v-if="invoiceCollectionsStore.shouldShowInvoiceCollections" :dark-mode="darkMode" />
            <invoice-write-off v-if="invoiceCollectionsStore.shouldShowInvoiceWriteOffs" :dark-mode="darkMode" />
            <invoice-chargeback-list v-if="invoiceStore.invoiceId" :invoice-id="invoiceStore.invoiceId" :dark-mode="darkMode" />
            <invoice-transaction-list v-if="invoiceStore.invoiceId" :invoice-id="invoiceStore.invoiceId" :dark-mode="darkMode" />
            <div class="flex flex-col gap-2 px-8">
                <div class="font-bold text-slate-500 text-sm">Notes</div>
                <textarea
                    :disabled="!invoiceStore.isEditable"
                    class="min-h-88 w-full border rounded pl-4  focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                    placeholder="Enter notes..." type="text" v-model="invoiceStore.invoiceNote"
                    :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}"/>
            </div>
            <div class="flex flex-col gap-2 px-8">
                <div class="font-bold text-slate-500 text-sm">Tags</div>
                <tag-input
                    v-model="invoiceStore.invoiceTags"
                    placeholder="Add tags"
                    :options="tags"
                    :searchable-fields="['name']"
                    :dark-mode="darkMode"
                    allow-new
                    :disabled="!invoiceStore.isEditable"
                />
            </div>
            <invoice-event-list v-if="invoiceStore.invoiceId !== null" class="m-8" :invoice-id="invoiceStore.invoiceId" :dark-mode="darkMode"/>
            <div
                v-if="(!readonly && !invoiceStore.hasPendingAction && !invoiceStore.locked) || invoiceStore?.status?.id === 'failed'"
                class="rounded-b-lg flex justify-end px-8 py-6 gap-4 sticky bottom-0"
                :class="{'bg-light-module': !darkMode, 'bg-dark-module': darkMode}"
            >
                <custom-button @click="createUpdateInvoice(action.id)" v-for="action in invoiceStore.status.actions" :dark-mode="darkMode">{{action.title}}</custom-button>
                <custom-button @click="createUpdateInvoice(invoiceHelper.STATUSES.DRAFT)" v-if="invoiceStore.status.id === invoiceHelper.STATUSES.DRAFT" :dark-mode="darkMode">Save</custom-button>
            </div>
        </div>
        <ConfirmModal
            v-show="confirmModal?.show"
            :title="confirmModal?.title"
            :dark-mode="darkMode"
            :text="confirmModal.text"
            @choice="confirmModal.callback"
        />
    </div>
</template>
<script>

import CustomButton from "../Shared/components/CustomButton.vue";
import {useInvoiceModalStore} from "../../../stores/invoice/invoice-modal.store";
import CreateInvoiceModalContentCompanyData from "./CreateInvoiceModalContentCompanyData/CreateInvoiceModalContentCompanyData.vue";
import CreateInvoiceModalContentItemList from "./CreateInvoiceModalContentItemList.vue";
import SimpleAlert from "../Shared/components/SimpleAlert.vue";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../composables/useSimpleIcon";
import useErrorHandler from "../../../composables/useErrorHandler";
import InvoiceEventList from "./InvoiceEventList.vue";
import useInvoiceHelper from "../../../composables/useInvoiceHelper";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import TagInput from "../Shared/components/TagInput.vue";
import TagApiService from "../BillingManagement/services/tags-api";
import InvoiceCollection from "./InvoiceDebtCollection/InvoiceCollection.vue";
import {useInvoiceCollectionsStore} from "../../../stores/invoice/invoice-collections-store.js";
import InvoiceChargebackList from "./InvoiceChargebackList.vue";
import InvoiceTransactionList from "./InvoiceTransactionsList.vue";
import InvoiceRefundModule from "./refund/InvoiceRefundModule.vue";
import InvoiceWriteOff from "./InvoiceWriteOffs/InvoiceWriteOff.vue";
import {ROLES, useRolesPermissions} from "../../../stores/roles-permissions.store.js";
import InvoicePaymentsModule from "./InvoicePaymentsModule/InvoicePaymentsModule.vue";
import InvoiceCreditsModule from "./credits/InvoiceCreditsModule.vue";
import {useCompanyCreditManagementStore} from "../../../stores/credit/company-credit-management.store.js";
import ConfirmModal from "../Shared/components/ConfirmationModal.vue";
const simpleIcon = useSimpleIcon();
const invoiceHelper = useInvoiceHelper();

export default {
    name: "ViewCreateInvoiceModalContent",
    components: {
        ConfirmModal,
        InvoiceCreditsModule,
        InvoicePaymentsModule,
        InvoiceWriteOff,
        TagInput,
        InvoiceRefundModule,
        InvoiceTransactionList,
        InvoiceCollection,
        InvoiceChargebackList,
        LoadingSpinner,
        InvoiceEventList,
        SimpleIcon,
        SimpleAlert,
        CreateInvoiceModalContentItemList,
        CreateInvoiceModalContentCompanyData,
        CustomButton
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: Number,
            default: null,
        },
        companyName: {
            type: String,
            default: null,
        },
        invoiceId: {
            type: Number,
            default: null,
        },
        readonly: {
            type: Boolean,
            default: null
        },
        futureInvoiceData: {
            type: Object,
            default: null
        },
    },
    data() {
        return {
            invoiceStore: useInvoiceModalStore(),
            invoiceCollectionsStore: useInvoiceCollectionsStore(),
            rolesPermissions: useRolesPermissions(),
            companyCreditManagementStore: useCompanyCreditManagementStore(),
            loading: false,
            confirmModal: {},
            simpleIcon,
            invoiceHelper,
            errorHandler: useErrorHandler(),
            showAlert: false,
            tags: null,
            tagsApiService: TagApiService.make(),
        }
    },
    created() {
        if (this.readonly !== null) {
            this.invoiceStore.readonly = this.readonly
        }

        if (this.invoiceId) {
            this.invoiceStore.retrieveInvoiceData(this.invoiceId)
        }

        this.getTags()
    },
    emits: ['invoice-created-updated'],
    computed: {
        showInvoiceRefundModule(){
            return this.invoiceStore.invoiceId && this.rolesPermissions.hasRole(ROLES.FINANCE_OWNER)
        },
        showInvoiceCreditsModule(){
            return this.invoiceStore.invoiceId && ![this.invoiceHelper.STATUSES.DRAFT].includes(this.invoiceStore.status?.id)
        }
    },
    methods: {
        useInvoiceHelper,
        async getTags() {
            const res = await this.tagsApiService.getAll();
            this.tags = res.data.data?.tags ?? []
        },
        async createUpdateInvoice(status, skip = false) {
            if (!skip && this.companyCreditManagementStore.totalCreditsAvailable > 0 && status === 'issued') {
                this.confirmModal = {
                    show: true,
                    title: 'Confirm the use of credits',
                    text: `Issuing this invoice will apply ${this.$filters.centsToFormattedDollars(this.invoiceStore.creditsAppliedToInvoice)} as credit.`,
                    callback: (choice) => {
                        if (choice) {
                            this.createUpdateInvoice(status, true)
                        }

                        this.confirmModal = {}
                    },
                }
                return;
            }

            this.loading = true;
            this.invoiceStore.locked = true

            try {
                const resp = await this.invoiceStore.updateInvoiceStatus(status);

                if (resp.data.data.status === true) {
                    this.$emit('invoice-created-updated', resp.data.data.invoice_uuid, resp.data.data.message);
                }
            } catch (err) {
                this.invoiceStore.locked = false

                this.errorHandler.handleError(err, 'Validation error')

                console.error(err)
                this.showAlert = true;
                setTimeout(() => {
                    this.showAlert = false;
                }, 4000)
            }
            this.loading = false;
        },
    }
}
</script>
