<template>
    <main>
        <div class="flex flex-col mb-5 text-sm">
            <span class="font-semibold">To:</span>
            <span class="font-semibold">{{ getValue(viewData, 'billedCompany.company_name') }}</span>
            <span>{{ getValue(viewData, 'billedCompany.full_address') }}</span>
        </div>
        <InvoiceOverviewTable
            :title="summaryTitle"
            class="mt-10"
            :rows="summaryRows"
            :headers="summaryHeaders"
            :totals="summaryTotals"
        >
        </InvoiceOverviewTable>
        <InvoiceInfo
            class="mb-5 bottom-of-page"
            :view-data="viewData"
        >
        </InvoiceInfo>
        <div class="page-break-after-always"></div>
        <InvoiceOverviewTable
            v-for="data in lineItemTables"
            class="mt-10"
            :title="data.title"
            :meta="data.meta"
            :rows="data.rows"
            :headers="data.headers"
            :totals="data.totals"
        >
        </InvoiceOverviewTable>
    </main>
</template>

<script>
import InvoiceOverviewTable from './components/InvoiceOverviewTable.vue'
import Badge from "../../Shared/components/Badge.vue";
import useObjectHelper from "../../../../composables/useObjectHelper";
import InvoiceInfo from "./components/InvoiceInfo.vue";

export default {
    components: {
        InvoiceInfo,
        Badge,
        InvoiceOverviewTable,
    },
    props: {
        viewData: {
            type: Object,
            required: true,
            default: {}
        },
        componentsProps: {
            type: Object,
            required: false,
            default: {}
        }
    },

    computed: {
        summaryTitle() {
            return this.getValue(this.viewData, 'summary.title')
        },

        summaryHeaders() {
            return this.getValue(this.viewData, 'summary.headers')
        },

        summaryRows() {
            return this.getValue(this.viewData, 'summary.rows')
        },

        summaryTotals() {
            return this.getValue(this.viewData, 'summary.totals')
        },

        lineItemTables(){
            return this.getValue(this.viewData, 'lineItemTables')
        }
    },

    created() {
        // Change this title to the filename
        document.title = 'Solarreviews'
    },

    methods: {
        ...useObjectHelper(),
    }
}
</script>
