<template>
    <div class="flex flex-col">
        <div v-if="note" class="flex flex-col">
            {{ note }}
        </div>
        <div v-if="isManualPayment">
            <span class="font-semibold">Bank Details:</span>
            <div v-if="accountName" class="flex text-sm">
                <span class="text-xs font-medium mr-2">{{ accountName }}</span>
            </div>
            <div v-if="accountNumber" class="flex text-sm">
                <span class="text-xs font-medium mr-2">Account Number:</span>
                <span class="text-xs">{{ accountNumber }}</span>
            </div>
            <div v-if="routingNumber" class="flex text-sm">
                <span class="text-xs font-medium mr-2">Routing Number:</span>
                <span class="text-xs">{{ routingNumber }}</span>
            </div>
        </div>
    </div>
</template>

<script>
import useObjectHelper from "../../../../../composables/useObjectHelper";

export default {
    name: 'InvoiceInfo',
    props: {
        viewData: {
            type: Object,
            required: true,
            default: {}
        },
    },

    computed: {
        routingNumber() {
            return this.getValue(this.viewData, 'billing_account.routing_number')
        },

        accountNumber() {
            return this.getValue(this.viewData, 'billing_account.account_number')
        },

        accountName() {
            return this.getValue(this.viewData, 'billing_account.account_name')
        },

        note() {
            return this.getValue(this.viewData, 'note')
        },

        billingProfilePaymentMethod() {
            return this.getValue(this.viewData, 'billingProfilePaymentMethod')
        },

        isManualPayment() {
            return this.billingProfilePaymentMethod === 'manual'
        }
    },

    methods: {
        ...useObjectHelper(),
    }
}
</script>
