<template>
    <div class="flex flex-col">
        <div class="mb-3 flex gap-2">
            <p class="font-semibold text-lg">{{ title}}</p>
            <p class="font-semibold text-lg" v-if="meta">{{ '(' + meta + ')' }}</p>
        </div>
        <table class="min-w-full">
            <thead>
            <tr class="bg-gray-100">
                <th v-for="(header, headerIdx) in headers" :key="headerIdx"
                    class="px-5 py-3 text-left last:text-right uppercase text-xs font-semibold first:rounded-tl-lg first:rounded-bl-lg  last:rounded-tr-lg last:rounded-br-lg"
                >
                    {{ header.title }}
                </th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(item, idx) in rows" :key="idx" class="border-b border-slate-200 last:border-slate-400" style="page-break-inside: avoid">
                <td
                    v-for="(visibleHeader, visibleHeaderIndex) in headers"
                    :key="visibleHeaderIndex"
                    class="px-5 py-3 last:text-right align-middle"
                >
                    <slot :name="visibleHeader.field"
                          v-bind="{ rowIndex: idx, item, value:item[visibleHeader.field] }">
                        <p class="text-sm align-middle">{{ getValue(item, visibleHeader.field) }}</p>
                    </slot>
                </td>
            </tr>
            </tbody>
        </table>
        <div class="flex justify-end mt-1 page-break-inside-avoid">
            <div class="flex flex-col px-5 py-3 gap-2">
                <div v-if="totals?.subtotal" class="grid grid-cols-2 uppercase gap-5">
                    <span class="text-right text-xs">Subtotal</span>
                    <span class="text-right text-xs">{{ totals?.subtotal?.formatted }}</span>
                </div>
                <div v-if="totals?.tax" class="grid grid-cols-2 justify-end text-xs uppercase gap-5">
                    <span class="text-right text-xs">Tax</span>
                    <span class="text-right text-xs">{{ totals?.tax?.formatted }}</span>
                </div>
                <div v-if="totals?.total" class="grid grid-cols-2 justify-end text-xs uppercase gap-5">
                    <span class="text-sm text-right font-semibold">Total</span>
                    <span class="text-right">{{ totals?.total?.formatted }}</span>
                </div>
                <div v-if="totals.custom_totals.length > 0" v-for="total in totals.custom_totals" class="grid grid-cols-2 uppercase gap-5">
                    <span class="text-right text-xs">{{ total.title }}</span>
                    <span class="text-right text-xs">{{ total.value }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import useObjectHelper from "../../../../../composables/useObjectHelper";

export default {
    name: 'InvoiceOverviewTable',
    props: {
        title: {
            type: String,
            required: true,
            default: ''
        },
        meta: {
            type: String,
            default: null
        },
        headers: {
            default: []
        },

        rows: {
            type: Array,
            default: []
        },

        totals: {
            type: Object,
            default: {
                subtotal: 0,
                tax: 0,
                total: 0,
            }
        }
    },

    methods: {
        ...useObjectHelper(),
    }
}
</script>
