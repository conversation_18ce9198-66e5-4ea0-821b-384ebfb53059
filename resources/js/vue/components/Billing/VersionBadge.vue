<template>
    <div class="flex flex-col">
        <badge :dark-mode="darkMode" :color="versionColor">{{ version }}</badge>
    </div>
</template>
<script>
import Badge from "../Shared/components/Badge.vue";

export default {
    name: "VersionBadge" ,
    components: {Badge},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        version: {
            type: String,
            default: 'v2'
        }
    },
    computed: {
        versionColor() {
            const versions = {
                v1: 'green',
                v2: 'blue'
            }

            return versions[this.version] ?? versions['v2']
        }
    }
}
</script>
