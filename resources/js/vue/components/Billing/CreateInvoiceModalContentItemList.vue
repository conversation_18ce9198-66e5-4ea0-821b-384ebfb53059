<template>
    <div class="p-8">
        <div
            class="grid border-b pb-1 font-semibold text-xs text-slate-500 uppercase"
            :class="{'grid-cols-9': actionable, 'grid-cols-8': !actionable}"
        >
            <div class="col-span-4">
                Description
            </div>
            <div>
                Quantity
            </div>
            <div>
                Item Price
            </div>
            <div>
                Added By
            </div>
            <div :class="{'flex justify-end': !actionable}">
                Amount
            </div>
        </div>
        <div v-if="invoiceStore.invoiceItems.length === 0"
             class="flex justify-center items-center py-2 text-xs font-semibold text-slate-500">
            No Items Added
        </div>
        <div v-else class="flex flex-col gap-1" v-for="(item, index) in invoiceStore.invoiceItems">
            <invoice-item
                :dark-mode="darkMode"
                :model-value="item"
                @update:modelValue="(val) => handleUpdate(val, index)"
                :disabled="!invoiceStore.isEditable"
                :actionable="actionable"
            >
                <template v-slot:actions v-if="invoiceStore.isEditable || invoiceStore.refunding">
                    <div class="flex justify-end items-center">
                        <simple-icon v-if="invoiceStore.isEditable" :icon="simpleIcon.icons.X_MARK" clickable
                                     @click="invoiceStore.removeItem(index)"/>
                        <simple-icon v-if="invoiceStore.refunding && !inRefundList(item) && invoiceStore.refundEditable"
                                     :icon="simpleIcon.icons.PLUS" clickable @click="invoiceStore.refundItem(index)"/>
                    </div>
                </template>
            </invoice-item>
        </div>
        <div class="flex justify-between gap-2 mt-2 items-center">
            <div class="flex flex-col uppercase text-sm font-semibold">
                <div>
                    <div class="mr-2">
                        Sub total:
                    </div>
                    <div class="text-red-500">
                        {{ $filters.centsToFormattedDollars(invoiceStore.subTotal) }}
                    </div>
                </div>
                <div>
                    <div class="mr-2">
                        Credits applied:
                    </div>
                    <div class="text-red-500">
                        {{ $filters.centsToFormattedDollars(invoiceStore.creditsAppliedToInvoice) }}
                    </div>
                </div>
                <div>
                    <div class="mr-2">
                        Total Value:
                    </div>
                    <div class="text-red-500">
                        {{ $filters.centsToFormattedDollars(totalIssuable) }}
                    </div>
                </div>
            </div>
            <div v-show="invoiceStore.isEditable" class="flex gap-2">
                <button-dropdown :dark-mode="darkMode" @selected="(option) => invoiceStore.invoiceItemAdded(option.id)"
                                 :options="invoiceItemTypes" options-list-placement="left">
                    <custom-button :dark-mode="darkMode">+ Add an item</custom-button>
                </button-dropdown>
                <custom-button
                    :disabled="invoiceStore.uninvoicedItems.length === 0"
                    @click="invoiceStore.addRemoveUninvoiced" :dark-mode="darkMode">
                    {{ invoiceStore.uninvoicedAdded ? "Remove Uninvoiced" : "+ Uninvoiced Products" }}
                    ({{ invoiceStore?.uninvoicedItems?.length }})
                </custom-button>
            </div>
        </div>
    </div>
</template>
<script>

import CustomButton from "../Shared/components/CustomButton.vue";
import {useInvoiceModalStore} from "../../../stores/invoice/invoice-modal.store";
import CustomInlineInput from "../Shared/components/CustomInlineInput.vue";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../composables/useSimpleIcon";
import InvoiceItem from "./components/CreateNewInvoice/InvoiceItem.vue";
import ButtonDropdown from "../Shared/components/ButtonDropdown.vue";
import {useCreditManagementStore} from "../../../stores/credit/credit-management.store.js";
import {useCompanyCreditManagementStore} from "../../../stores/credit/company-credit-management.store.js";

const simpleIcon = useSimpleIcon();

export default {
    name: "CreateInvoiceModalContentItemList",
    components: {
        ButtonDropdown,
        InvoiceItem,
        SimpleIcon, CustomInlineInput, CustomButton
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            simpleIcon,
            invoiceStore: useInvoiceModalStore(),
            creditStore: useCompanyCreditManagementStore(),
            loading: false,
            invoiceItemTypes: []
        }
    },
    computed: {
        totalIssuable() {
            return Math.max(0, this.invoiceStore.subTotal - this.invoiceStore.creditsAppliedToInvoice);
        },
        actionable() {
            return !!(this.invoiceStore.isEditable || (this.invoiceStore.refunding && this.invoiceStore.refundEditable));
        }
    },
    mounted() {
        this.invoiceItemTypes.push(...Object.values(this.invoiceStore.itemTypes))
    },
    methods: {
        handleUpdate(val, index){
            this.invoiceStore.invoiceItems[index] = val
        },
        inRefundList(item) {
            return !!this.invoiceStore.refundItems.find(function (refundItem) {
                return refundItem.invoice_item_id === item.invoice_item_id
            })
        }
    }

}
</script>
