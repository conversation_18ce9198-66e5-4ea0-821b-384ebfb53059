<template>
    <div class="flex flex-col gap-2">
        <div class="font-semibold"> New Invoice Payment</div>
        <simple-alert v-if="errorHandler.message" variant="light-red" :content="errorHandler.message" />
        <loading-spinner v-if="loading"/>
        <div v-else class="grid grid-cols-6 gap-3 items-center">
            <labeled-value label="Billing Profile" class="col-span-2">
                <div class="flex flex-col gap-1">
                    <div v-for="option in billingProfileOptions"
                         class="flex items-center gap-1">
                        <custom-checkbox check :model-value="invoicePayment.billingProfileId === option.id"
                                         @update:modelValue="toggleBillingProfile(option)"/>
                        <div class="flex items-center gap-1">
                            <payment-method-badge :type="option.payment_method"/>
                            <badge v-if="option.payment_method !== 'manual'">
                                <p>
                                    Charge attempt limit <strong>{{ option.charge_attempts }}</strong>
                                </p>
                            </badge>
                        </div>
                    </div>
                </div>
            </labeled-value>
            <labeled-value label="Amount">
                <div class="flex items-center gap-1">
                    <custom-inline-input
                        v-model="invoicePayment.amount_in_dollars"
                        @update:modelValue="(val) => invoicePayment.amount = val * 100"
                        prefix="$"
                        :add-default-value="false"
                    />
                </div>
            </labeled-value>
            <labeled-value v-if="selectedBillingProfile?.payment_method === 'manual'" label="Date" class="col-span-2">
                <DatePicker
                    v-model="invoicePayment.date"
                    format="PP"
                    :max-date="new Date()"
                    :disabled="selectedBillingProfile?.payment_method !== 'manual'"
                    timezone="America/Denver"
                />
            </labeled-value>

            <CustomButton class="mt-2 flex justify-center" @click="payInvoice" :disabled="loadingPayment">
                <p>Create</p>
            </CustomButton>
        </div>
        <div class="flex uppercase text-sm font-semibold mt-2">
            <div class="mr-2">
                Total Outstanding:
            </div>
            <div class="text-red-500">
                {{ $filters.centsToFormattedDollars(invoiceStore.invoiceTotals.outstanding) }}
            </div>
        </div>
    </div>
</template>
<script>
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import LabeledValue from "../../Shared/components/LabeledValue.vue";
import CustomInlineInput from "../../Shared/components/CustomInlineInput.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import Badge from "../../Shared/components/Badge.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import Api from "../services/company-billing-profiles.js";
import PaymentMethodBadge from "./PaymentMethodBadge.vue";
import {useInvoiceModalStore} from "../../../../stores/invoice/invoice-modal.store.js";
import SimpleAutocomplete from "../../Shared/components/SimpleAutocomplete.vue";
import CustomCheckbox from "../../Shared/SlideWizard/components/CustomCheckbox.vue";
import DatePicker from "@vuepic/vue-datepicker";
import ApiService from "../../BillingManagement/services/invoice-payments.js";
import useErrorHandler from "../../../../composables/useErrorHandler.js";
import SimpleAlert from "../../Shared/components/SimpleAlert.vue";
import {useToastNotificationStore} from "../../../../stores/billing/tost-notification.store.js";
import {DateTime} from "luxon";
import {defaultDocument} from "@vueuse/core";

export default {
    name: "RequestInvoiceChargeForm",
    components: {
        SimpleAlert,
        CustomCheckbox,
        SimpleAutocomplete,
        PaymentMethodBadge, Dropdown, Badge, CustomButton, CustomInlineInput, LabeledValue, LoadingSpinner, DatePicker
    },
    data() {
        return {
            loading: false,
            loadingPayment: false,
            invoicePayment: {
                billingProfileId: null
            },
            billingProfileOptions: [],
            companyBillingProfilesApi: Api.make(),
            invoiceStore: useInvoiceModalStore(),
            invoicePaymentsApi: ApiService.make(),
            errorHandler: useErrorHandler(),
            toastNotificationStore: useToastNotificationStore()
        }
    },
    async mounted() {
        this.loading = true
        await this.getBillingProfiles()
        this.loading = false
    },
    computed: {
        selectedBillingProfile() {
            return this.billingProfileOptions.find(e => e.id === this.invoicePayment.billingProfileId)
        }
    },
    methods: {
        async getBillingProfiles() {
            const response = await this.companyBillingProfilesApi.getCompanyBillingProfiles(this.companyId)
            this.billingProfileOptions = response.data.data.billing_profiles.map(bp => ({
                ...bp,
                name: `${bp.payment_method} Charge attempt limit ${bp.charge_attempts}`
            }))
        },
        toggleBillingProfile(option) {
            this.invoicePayment.billingProfileId = option.id
        },
        async payInvoice() {
            if (this.loadingPayment) return;

            this.loadingPayment = true;

            try {
                await this.invoicePaymentsApi.makeInvoicePayment(this.invoiceId, {
                    billing_profile_id: this.invoicePayment.billingProfileId,
                    amount: this.invoicePayment.amount,
                    date: DateTime.fromJSDate(this.invoicePayment.date).setZone('America/Denver').toISO(),
                })

                this.toastNotificationStore.notifySuccess(
                    'Action requested'
                )

                this.$emit('refresh')
            } catch (err) {
                this.errorHandler.handleError(err)
                console.error(err)
            }

            this.loadingPayment = false;
        },
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: Number,
            required: true
        },
        invoiceId: {
            type: Number,
            required: true
        }
    }
}
</script>
