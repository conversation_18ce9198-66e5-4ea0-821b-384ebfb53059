<template>
    <div>
        <p class="font-semibold mt-2">Attempts</p>
        <div class="grid grid-cols-12 border-b py-2 font-semibold text-xs text-slate-500 uppercase">
            <div>Id</div>
            <div class="col-span-2">Type</div>
            <div class="col-span-2">Status</div>
            <div class="col-span-3">Message</div>
            <div class="col-span-3">Attempted at</div>
        </div>
        <div v-for="charge in paymentCharges">
            <div class="grid grid-cols-12 text-xs font-semibold border-b items-center py-2 gap-2">
                <div>{{charge.id}}</div>
                <div class="col-span-2">
                    <payment-method-badge
                        :type="charge.payment_method_type"
                        :reference="charge.payment_method_number"
                    >
                    </payment-method-badge>
                </div>
                <div class="col-span-2">
                    <invoice-payment-charge-status-badge :status="charge.status" />
                </div>
                <div class="col-span-3">{{ charge.error_message }}</div>
                <div class="col-span-3">{{ charge.created_at }}</div>
            </div>
        </div>
    </div>
</template>
<script>
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import InvoiceItem from "../components/CreateNewInvoice/InvoiceItem.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import InvoicePaymentStatusBadge from "./InvoicePaymentStatusBadge.vue";
import PaymentMethodBadge from "./PaymentMethodBadge.vue";
import InvoicePaymentChargeStatusBadge from "./InvoicePaymentChargeStatusBadge.vue";

export default {
    name: 'InvoicePaymentChargesTable',
    methods: {useSimpleIcon},
    components: {InvoicePaymentChargeStatusBadge, PaymentMethodBadge, InvoicePaymentStatusBadge, InvoiceItem, SimpleIcon},
    props: {
        paymentCharges: {
            type: Array,
            required: true
        },
    },
}
</script>
