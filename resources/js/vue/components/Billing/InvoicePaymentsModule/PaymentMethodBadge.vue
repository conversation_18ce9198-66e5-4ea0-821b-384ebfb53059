<template>
    <div>
        <badge class="flex items-center gap-1" :color="componentConfig.color">
            <simple-icon :icon="componentConfig.icon"></simple-icon>
            <p v-if="date">{{ date }}</p>
            <p v-if="!onlyIcon">
                {{ componentConfig.title }}
                {{reference ? `* * * ${reference}` : ''}}
            </p>
        </badge>
    </div>
</template>
<script>
import Badge from "../../Shared/components/Badge.vue";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";

export default {
    name: 'PaymentMethodBadge',
    components: {SimpleIcon, Badge},
    props: {
        type: {
            type: String,
        },
        reference: {
            type: [String, Number]
        },
        onlyIcon: {
            type: Boolean,
            default: false
        },
        date: {
            type: String,
        }
    },

    computed: {
        simpleIcon() {
            return useSimpleIcon()
        },

        configs() {
            return {
                stripe: {
                    icon: this.simpleIcon.icons.CREDIT_CARD,
                    title: 'Card',
                    color: 'indigo'
                },
                manual: {
                    icon: this.simpleIcon.icons.BANK,
                    title: 'Bank',
                    color: 'cyan'
                },
                default: {}
            }
        },

        componentConfig() {
            return this.configs[this.type] ?? this.configs.default
        }
    }
}
</script>
