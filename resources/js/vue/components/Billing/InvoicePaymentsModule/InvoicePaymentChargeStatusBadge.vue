<template>
    <badge :color="componentConfig.color">
        {{ componentConfig.title }}
    </badge>
</template>
<script>
import Badge from "../../Shared/components/Badge.vue";

const config = {
    requested: {
        title: 'Requested',
        color: 'green'
    },
    pending: {
        title: 'Pending',
        color: 'blue'
    },
    failed: {
        title: 'Failed',
        color: 'red'
    },
    default: {
        title: 'Unknown',
        color: 'gray'
    },
}

export default {
    name: 'InvoicePaymentChargeStatusBadge',
    components: {Badge},
    props: {
        status: {
            type: String,
            required: true,
        }
    },
    computed: {
        componentConfig() {
            return config[this.status] ?? config.default
        },
    }
}
</script>
