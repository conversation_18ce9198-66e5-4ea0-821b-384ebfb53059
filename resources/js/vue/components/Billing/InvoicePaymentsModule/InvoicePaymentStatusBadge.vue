<template>
    <badge :color="componentConfig.color">
        {{ componentConfig.title }}
    </badge>
</template>
<script>
import Badge from "../../Shared/components/Badge.vue";

const config = {
    charged: {
        title: 'Charged',
        color: 'green'
    },
    pending: {
        title: 'Pending',
        color: 'gray'
    },
    requested: {
        title: 'Requested',
        color: 'blue'
    },
    failed: {
        title: 'Failed',
        color: 'red'
    },
    rescheduled: {
        title: 'Rescheduled',
        color: 'blue'
    },
    canceled: {
        title: 'Canceled',
        color: 'gray'
    },
    default: {
        title: 'Unknown',
        color: 'gray'
    },
}

export default {
    name: 'InvoicePaymentStatusBadge',
    components: {Badge},
    props: {
        status: {
            type: String,
            required: true,
        }
    },
    computed: {
        componentConfig() {
            return config[this.status] ?? config.default
        },
    }
}
</script>
