<template>
    <div class="grid grid-cols-1 gap-4 mt-5">
        <div class="rounded-lg p-5"
             :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
            <div class="p-5">
                <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">
                    Permit Metrics for {{ companyName }}
                </h5>
                <LoadingSpinner :dark-mode="darkMode" v-if="loadingMetrics"/>
                <div v-else class="mt-7 border rounded-lg" :class="{'border-light-border text-slate-700': !darkMode, 'border-dark-border text-slate-400': darkMode}">
                    <div class="grid items-center-center border-b p-3"
                         :class="[`grid-cols-${industries.length}`, {'border-light-border': !darkMode, 'border-dark-border': darkMode}]">
                        <div v-for="industry in industries" class="text-sm border-r last:border-none text-center"
                             :class="{'border-light-border': !darkMode, 'border-dark-border': darkMode}">
                            {{ industry.toUpperCase() }}
                        </div>
                    </div>
                    <div class="grid items-center-center p-3 " :class="[`grid-cols-${industries.length}`, {'bg-light-background': !darkMode, 'bg-dark-background': darkMode}]">
                        <div v-for="industry in industries" class="text-sm border-r last:border-none px-3"
                             :class="{'border-light-border': !darkMode, 'border-dark-border': darkMode}">
                            <div class="grid grid-cols-2 font-semibold" v-if="metrics[industry]">
                                <p>State</p>
                                <p>Permits</p>
                            </div>
                            <div v-for="(permits, state) in metrics[industry]" :key="state" class="text-xs my-2" v-if="metrics[industry]">
                                <div class="grid grid-cols-2">
                                    <p>{{ state.toUpperCase() }}</p>
                                    <p>{{ permits }}</p>
                                </div>
                            </div>
                            <div v-else class="text-center">
                                Not Available
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ApiService from "./services/api.js";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";

export default {
    name: "CompanyPermitMetrics",
    components: {LoadingSpinner},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: Number,
            required: true
        },
        companyName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            api: ApiService.make(this.companyId),
            metrics: {},
            loadingMetrics: false,
            industries: [
                'bathroom',
                'hvac',
                'kitchen',
                'roofing',
                'remodel',
                'siding',
                'solar',
                'windows',
            ]
        }
    },
    mounted() {
        this.getPermitMetrics();
    },
    methods: {
        getPermitMetrics() {
            this.loadingMetrics = true;
            this.api.getPermitMetrics()
                .then(resp => this.metrics = resp.data.data.metrics)
                .catch(e => console.error(e))
                .finally(() => this.loadingMetrics = false);
        }
    }
}
</script>
