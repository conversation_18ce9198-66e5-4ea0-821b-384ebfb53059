import axios from 'axios';

export default class ApiService {
    constructor(baseUrl, baseEndpoint, version, companyId) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
        this.companyId = companyId;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    static make(companyId) {
        return new ApiService('internal-api', `companies/${companyId}/metrics`, 1, companyId);
    }

    getCompanyMetricsSummary() {
        return this.axios().get('/summary')
    }

    sendCompanyMetricsUpdateSummary() {
        return this.axios().post('/summary')
    }

    getCompanyMetricsApiRequestCount() {
        return this.axios().get('/api/count')
    }

    getPermitMetrics() {
        return this.axios().get('/permit-metrics')
    }

}
