<template>
    <div class="grid grid-cols-1 gap-4">
        <div class="col-span-3 border rounded-lg p-5"
             :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
            <alerts-container v-if="alertActive" :text="alertText" :alert-type="alertType" :dark-mode="darkMode"/>
            <div class="p-5 flex items-center justify-between">
                <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">
                    Domain Visit Metrics for {{ companyName }}
                </h5>
                <CustomButton
                    v-if="permissionStore.hasPermission(PERMISSIONS.PERMISSION_COMPANY_METRICS_SEND)"
                    @click="openModal"
                    :dark-mode="darkMode"
                    color="slate-inverse"
                    class="text-nowrap"
                >
                    {{ metrics?.length > 0 ? 'Update Metrics' : 'Get Metrics'}}
                </CustomButton>
            </div>
            <div v-if="metrics?.length > 0" :class="darkMode ? 'text-slate-200' : 'text-slate-800'" class="p-5 text-center">
                <div>
                    {{ companyName }} had <span class="font-weight-bold">{{ totalVisits }}</span> website visits between {{ dataFromDate }} - {{ dataToDate }}
                </div>
            </div>
            <div v-if="metrics?.length > 0" class="grid grid-cols-2">
                <PieEChart class="col-span-1" :title="'Traffic Sources'" :dark-mode="darkMode" :series-data="graphDataPayload" :legend="graphLegend"/>
                <div class="col-span-1">
                    <simple-table class="col-span-1" :dark-mode="darkMode" :headers="headers" :data="metrics" :no-pagination="true" :loading="loading"></simple-table>
                </div>
            </div>
            <!-- Activity Log -->
            <Modal v-if="showSendModal" :dark-mode="darkMode" confirm-text="Get company metrics summary"
                   @confirm="sendCompanyMetricsRequest" :small=true @close="showSendModal = false">
                <template v-slot:header>
                    <p class="font-semibold">Get Metrics</p>
                </template>
                <template v-slot:content class="flex items-center justify-center">
                    <loading-spinner v-if="loading"/>
                    <div v-else>
                        <div>
                            We have {{ apiCount }} request left for this month. Are you sure you want to spend one on this company?
                        </div>
                        <div>
                            The data will be obtained using the company domain, please make sure it is current and in the correct format.
                        </div>
                        <div>
                            {{ companyWebsite }}
                        </div>
                    </div>
                </template>
            </Modal>
        </div>
    </div>
</template>
<script>
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import ApiService from "./services/api.js";
import Modal from "../../../Shared/components/Modal.vue";
import { PERMISSIONS, useRolesPermissions} from "../../../../../stores/roles-permissions.store.js";
import AlertsContainer from "../../../Shared/components/AlertsContainer.vue";
import AlertsMixin from "../../../../mixins/alerts-mixin.js";
import SimpleTable from "../../../Shared/components/SimpleTable/SimpleTable.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import PieEChart from "../../../Shared/components/ECharts/PieEChart.vue";

export default {
    name: "CompanyMetrics",
    components: {
        PieEChart,
        CustomButton,
        SimpleTable,
        AlertsContainer, Modal, LoadingSpinner
    },
    mixins: [AlertsMixin],
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: Number,
            default: null,
        },
        companyName: {
            type: String,
            default: null
        },
        companyWebsite: {
            type: String,
            default: null
        }
    },
    data() {
        return {
            api: ApiService.make(this.companyId),
            loading: false,
            permissionStore: useRolesPermissions(),
            PERMISSIONS: PERMISSIONS,
            showSendModal: false,
            headers: [
                {title: "Name", field: "name", show: true, sortable: true},
                {title: "value", field: "value", show: true, sortable: true},
                {title: "share", field: "share", show: true, sortable: true},
            ],
            dataFromDate: null,
            dataToDate: null,
            metrics: [],
            totalVisits: null,
            apiCount: 0,
            graphLegend: null,
            graphDataPayload: null,
        }
    },
    created() {
        if (this.companyId) this.getCompanyMetrics();
    },
    computed: {

    },
    methods: {
        getCompanyMetrics() {
            this.loading = true;
            this.api.getCompanyMetricsSummary().then(resp => {
                resp = resp.data?.data?.metrics
                if(resp) {
                    this.dataFromDate = resp.metric_from_date
                    this.dataToDate = resp.metric_to_date
                    this.metrics = resp.visits_data_share
                    this.totalVisits = resp.total_visits
                    this.populateGraph()
                }
            }).finally(() => this.loading = false);
        },
        sendCompanyMetricsRequest() {
            this.api.sendCompanyMetricsUpdateSummary().finally(() =>  {
                this.showSendModal = false
                this.getCompanyMetrics()
                this.getMetricsApiRequestCount()
            })
        },
        getMetricsApiRequestCount() {
            this.loading = true;
            this.api.getCompanyMetricsApiRequestCount().then(resp => {
                this.apiCount = resp.data.data.count;
            }).finally(() => this.loading = false);
        },
        openModal() {
            this.showSendModal = true;
            this.getMetricsApiRequestCount();
        },
        populateGraph() {
            this.graphLegend = {
                orient: 'vertical',
                left: 'left',
                data: this.metrics?.map(item => item.name),
            }
            this.graphDataPayload = [
                {
                    name: this.totalVisits + ' Website Visits',
                    type: 'pie',
                    radius: '55%',
                    center: ['50%', '60%'],
                    data: this.metrics
                }
            ]
        }
    }
}
</script>
