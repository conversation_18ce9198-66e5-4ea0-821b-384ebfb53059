<template>
    <div class="grid grid-cols-2 gap-4 pb-10">
        <CustomerSuccessManagerList :dark-mode="darkMode" :company-id="companyId"/>
    </div>
</template>
<script>
import CustomerSuccessManagerList from "./components/CustomerSuccessManagerList.vue";
export default {
    name: "SalesRole",
    components: {CustomerSuccessManagerList},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: Number,
            default: null
        },
    }
}
</script>

<style scoped>

</style>
