<template>
    <div class="border rounded-lg pb-3"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <div class="p-5 flex items-center justify-between">
            <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Customer Success Managers List</h5>
        </div>
        <div class="grid grid-cols-5 gap-x-3 mb-2 px-5">
            <p class="text-slate font-medium-400 uppercase text-xs">ID</p>
            <p class="text-slate font-medium-400 uppercase text-xs col-span-2">Name</p>
            <p class="text-slate font-medium-400 uppercase text-xs col-span-2">Email</p>
        </div>
        <div v-if="loading" class="my-6 py-6">
            <LoadingSpinner></LoadingSpinner>
        </div>

        <div class="border-t border-b overflow-y-auto text-sm"
             :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}">
            <div class="flex justify-center p-4" v-if="customerSuccessManagers.length === 0">
                No Data
            </div>
            <div v-else class="grid grid-cols-5 gap-x-3 border-b last:border-b-0 px-5 py-3"
                 v-for="customerSuccessManager in customerSuccessManagers" :key="customerSuccessManager.id"
                 :class="{'text-slate-900 hover:bg-light-module border-light-border': !darkMode, 'text-slate-100 hover:bg-dark-module border-dark-border': darkMode}">
                <p class="truncate">
                    {{ customerSuccessManager.id }}
                </p>
                <p class="col-span-2 pl-1 truncate">
                   {{ customerSuccessManager.name }}
                </p>
                <p class="col-span-2 pl-1 truncate">
                    {{ customerSuccessManager.email }}
                </p>
            </div>
        </div>
    </div>
</template>
<script>
import SharedApiService from "../../../../Shared/services/api.js";
import LoadingSpinner from "../../../../Shared/components/LoadingSpinner.vue";
export default {
    name: "CustomerSuccessManagerList",
    components: {LoadingSpinner},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        companyId: {
            type: Number,
            default: null
        }
    },
    data() {
        return {
            api: SharedApiService.make(),
            loading: false,
            customerSuccessManagers: []
        }
    },
    created() {
        if (this.companyId) this.getCustomerSuccessManagers();
    },
    computed: {},
    methods: {
        getCustomerSuccessManagers() {
            this.loading = true;
            this.api.getCompanyCustomerSuccessManagers(this.companyId)
                .then(resp => {
                    this.customerSuccessManagers = resp.data.data.customer_success_managers;
                }).finally(() => this.loading = false)
        }
    }
}
</script>

<style scoped>

</style>
