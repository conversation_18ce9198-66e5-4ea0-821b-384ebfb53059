import axios from 'axios';

export default class ApiService {
    constructor(baseUrl, baseEndpoint, version, companyId) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
        this.companyId = companyId;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    static make(companyId) {
        return new ApiService('internal-api', `companies/${companyId}/history/`, 1, companyId);
    }

    getCompanyRoleHistory(params) {
        return this.axios().get('role', {params})
    }

}
