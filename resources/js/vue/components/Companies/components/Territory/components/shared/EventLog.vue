<template>
    <div class="flex flex-col overflow-auto pl-3 py-2 text-sm" :class="[darkMode ? 'text-slate-100' : '']">
        <div class="flex justify-center text-center text-slate-500 text-sm font-medium py-3"
             v-if="data.length === 0">
            No Events
        </div>
        <ul v-else role="list" class="space-y-6">
            <li v-for="(event, eventIdx) in data" class="relative flex flex-1 gap-x-4 items-center">
                <div
                    class="absolute left-0 top-0 flex w-6 justify-center"
                    :class="[eventIdx === data.length - 1 ? 'h-6' : '-bottom-6']"
                >
                    <div class="w-px" :class="[darkMode ? 'bg-primary-500' : 'bg-gray-200']"/>
                </div>
                <div class="relative flex h-6 w-6 flex-none items-center justify-center "
                     :class="[darkMode ? 'bg-dark-module' : 'bg-light-module']">
                    <slot name="event-icon" v-bind="{event}">
                        <simple-icon v-if="event.icon" :icon="event.icon" :color="event.icon_color"/>
                        <div v-else class="h-1.5 w-1.5 rounded-full ring-1" :class="[darkMode ? 'bg-primary-500 ring-primary-500' : 'bg-gray-100 ring-gray-300']"/>
                    </slot>
                </div>
                <div class="flex flex-1 flex-col">
                    <div class="flex justify-between">
                        <slot name="title" v-bind="{event, title: event[titleKey]}">
                            <div class="font-semibold">
                                {{ event[titleKey] }}
                            </div>
                        </slot>

                        <div class="text-slate-500 mr-4">
                            {{ event[dateKey]}}
                        </div>
                    </div>
                    <slot name="payload" v-bind="{event: event, payload: event.payload}">
                        <div v-if="event.payload?.length && event.payload?.length > 0" v-for="note in event.payload">
                            <div class="text-xs" :class="[darkMode ? 'text-slate-400' : '']">
                                {{note}}
                            </div>
                        </div>
                    </slot>
                </div>
            </li>
        </ul>
    </div>
</template>
<script>
import SimpleIcon from "../../../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../../../composables/useSimpleIcon.js";
import Badge from "../../../../../Shared/components/Badge.vue";

export default {
    name: "EventLog",
    methods: {useSimpleIcon},
    components: {Badge, SimpleIcon},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        data: {
            type: Array,
            default: []
        },
        titleKey: {
            type: String,
            default: 'title',
        },
        dateKey: {
            type: String,
            default: 'date'
        }
    }
}
</script>
