<template>
    <card-wrapper :dark-mode="darkMode" :title="friendlyRole" :loading="loading">
        <event-log class="max-h-80" :dark-mode="darkMode" :data="data" />
    </card-wrapper>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import CardWrapper from "./shared/CardWrapper.vue";
import EventLog from "./shared/EventLog.vue";
import ApiService from "../services/api.js";

const props = defineProps({
    darkMode: {
        type: Boolean,
        default: false,
    },
    companyId: {
        type: Number,
        required: true,
    },
    role: {
        type: String,
        required: true,
    }
})

const loading = ref(false)
const data = ref([])

onMounted(() => {
    getRoleHistory()
})

function getRoleHistory() {
    loading.value = true;

    ApiService.make(props.companyId).getCompanyRoleHistory({
        role: props.role
    }).then(res => data.value = res.data.data)
    .catch(error => console.log(error.response.data.message))
    .finally(() => loading.value = false)
}

const friendlyRole = computed(() => props.role.toUpperCase().replaceAll('-', ' ') + ' Assignments')

</script>
