<template>
    <div>
        <CompanyRoleTable
            class="max-w-6xl"
            :dark-mode="darkMode"
            :scope="{company_id: companyId}"
        />
    </div>
</template>
<script>
import CompanyRoleTable from "./CompanyRoleTable.vue";

export default {
    name: "CommissionManagement",
    components: {CompanyRoleTable},
    props: {
        companyId: {
            type: Number,
            required: true,
        },
        darkMode: {
            type: Boolean,
            default: false,
        }
    }
}
</script>
