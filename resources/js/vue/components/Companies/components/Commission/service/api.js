import axios from 'axios';

export default class ApiService {
    constructor(baseUrl, baseEndpoint, version) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    static make() {
        return new ApiService('internal-api', 'company-manager', 1);
    }

    list(params) {
        return this.axios().get('/', {params})
    }

    getCompanyUserRelation(id) {
        return this.axios().get(`/${id}`)
    }

    listRoles() {
        return this.axios().get('/roles')
    }

    updateCompanyUserRelation(relation) {
        return this.axios().post(`/${relation.id}`, relation);
    }
}
