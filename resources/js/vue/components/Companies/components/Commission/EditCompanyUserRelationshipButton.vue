<template>
    <div>
        <alerts-container :dark-mode="darkMode" v-if="alertActive" :alert-type="alertType" :text="alertText" />
        <SimpleIcon
            v-if="canEdit"
            :dark-mode="darkMode"
            :icon="simpleIcon.icons.PENCIL_SQUARE"
            :color="simpleIcon.colors.BLUE"
            clickable
            @click="toggleModal"
            tooltip="Edit"
        />
        <EditCompanyUserRelationshipModal
            v-if="modal"
            @close="handleClose"
            :company-user-relation-id="companyUserRelationId"
            :dark-mode="darkMode"
        />
    </div>
</template>
<script>
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import EditCompanyUserRelationshipModal from "./EditCompanyUserRelationshipModal.vue";
import useSimpleIcon from "../../../../../composables/useSimpleIcon.js";
import {PERMISSIONS, useRolesPermissions} from "../../../../../stores/roles-permissions.store.js";
import AlertsContainer from "../../../Shared/components/AlertsContainer.vue";
import AlertsMixin from "../../../../mixins/alerts-mixin.js";
const simpleIcon = useSimpleIcon();
export default {
    name: "EditCompanyUserRelationshipButton",
    components: {AlertsContainer, EditCompanyUserRelationshipModal, SimpleIcon},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        companyUserRelationId: {
            type: Number,
            required: true,
        }
    },
    emits: ['save'],
    data() {
        return {
            simpleIcon,
            modal: false,
            rolePermissionStore: useRolesPermissions(),
        }
    },
    mixins: [AlertsMixin],
    computed: {
        canEdit() {
            return this.rolePermissionStore.hasPermission(PERMISSIONS.COMPANY_USER_RELATIONSHIPS_EDIT)
        }
    },
    methods: {
        toggleModal() {
            this.modal = !this.modal;
        },
        handleClose(message) {
            if (message) {
                this.showAlert('success', message)
                this.$emit('save')
            }

            this.toggleModal();
        }
    }
}
</script>
