import axios from 'axios';

export default class ApiService {
    constructor(baseUrl, baseEndpoint, version, companyId) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
        this.companyId = companyId;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    static make(companyId) {
        return new ApiService('internal-api', `companies/${companyId}/delete`, 1, companyId);
    }

    getPrerequisites() {
        return this.axios().get('/validate');
    }

    getImpact() {
        return this.axios().get('/impact');
    }

    getInfo() {
        return this.axios().get('/info');
    }

    deleteCompany(reason) {
        return this.axios().delete('/dispatch', {params: {reason}});
    }

    cancelDelete() {
        return this.axios().post('/cancel');
    }

}
