<template>
    <Modal
        @close="$emit('close')"
        :dark-mode="darkMode"
        no-buttons
    >
        <template #header>
            <div class="font-semibold">
                Delete Company ID: {{ companyId }}
            </div>
        </template>
        <template #content>
            <div class="grid grid-cols-2 gap-2">
                <div class="flex flex-col gap-2">
                    <simple-card :loading="loadingPrerequisites" :dark-mode="darkMode" class="p-3 gap-2">
                        <template #title>
                            <div class="flex items-center gap-2">
                                <simple-icon
                                    :icon="prerequisites.deletable ? simpleIcon.icons.CHECK_CIRCLE : simpleIcon.icons.X_CIRCLE"
                                    :color="prerequisites.deletable ? simpleIcon.colors.GREEN : simpleIcon.colors.RED"
                                    :dark-mode="darkMode"
                                />
                                <div class="font-semibold">Prerequisites</div>
                                <simple-icon
                                    :icon="simpleIcon.icons.INFORMATION_CIRCLE"
                                    :dark-mode="darkMode"
                                    :color="simpleIcon.colors.BLUE"
                                    tooltip="All must be met for a company to be deletable"
                                />
                            </div>
                        </template>
                        <div>
                            <div class="flex items-center gap-2" v-for="prerequisite in prerequisites.data">
                                <simple-icon
                                    :dark-mode="darkMode"
                                    :icon="prerequisite.data ? simpleIcon.icons.CHECK_CIRCLE : simpleIcon.icons.X_CIRCLE"
                                    :color="prerequisite.data ? simpleIcon.colors.GREEN : simpleIcon.colors.RED"
                                />
                                <div>{{ prerequisite.title }}</div>
                            </div>
                        </div>
                    </simple-card>
                    <simple-card :loading="loadingInfo" :dark-mode="darkMode" class="p-3 gap-2">
                        <template #title>
                            <div class="font-semibold">Information</div>
                        </template>
                        <Accordion
                            v-for="(data, type) in info"
                            :dark-mode="darkMode"
                            :total="data.total"
                            container-classes="relative flex justify-between items-center cursor-pointer gap-x-2 py-2 px-4 rounded-lg"
                            class="border rounded-lg"
                        >
                            <template #title>
                                <span class="flex items-center gap-1">
                                    {{ data.title }}
                                </span>
                            </template>
                            <template #default>
                                <billing-card-paginated-content
                                    :dark-mode="darkMode"
                                    :headers="headers[type]"
                                    :data="data.data"
                                >
                                    <template #status="{value}">
                                        <badge
                                            :dark-mode="darkMode"
                                            :color="value ? 'green' : 'red'"
                                            class="normal-case"
                                        >
                                            {{ value ? 'Active' : 'Inactive' }}
                                        </badge>
                                    </template>
                                    <template #content="{value}">
                                        <div class="relative group overflow-visible max-w-full">
                                            <!-- Truncated text -->
                                            <span class="block truncate text-ellipsis overflow-hidden whitespace-nowrap normal-case max-w-xs">
                                                {{ value }}
                                            </span>

                                            <!-- Tooltip -->
                                            <div
                                                class="normal-case absolute z-10 left-1/2 -translate-x-1/2 mt-2 w-64 rounded bg-gray-800 text-white text-sm px-3 py-2 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity"
                                            >
                                                {{ value }}
                                            </div>
                                        </div>
                                    </template>
                                    <template #recording="{value}">
                                        {{ value?.duration_seconds ? humanReadableFromSeconds(value?.duration_seconds) : 'N/A' }}
                                    </template>
                                    <template #timestamp="{value}">
                                        {{ $filters.dateFromTimestamp(value) }}
                                    </template>
                                </billing-card-paginated-content>
                            </template>
                        </Accordion>
                    </simple-card>
                    <simple-card :dark-mode="darkMode" class="p-3 gap-2">
                        <labeled-value>
                            <template #label>
                                <div class="font-semibold">Reason</div>
                            </template>
                            <custom-input
                                class="flex-1"
                                :dark-mode="darkMode"
                                type="textarea"
                                placeholder="Reason for company deletion"
                                input-classes="bg-light-module h-20"
                                v-model="reason"
                            />
                        </labeled-value>
                    </simple-card>
                </div>
                <div class="flex flex-col gap-2">
                    <simple-card :loading="loadingImpact" :dark-mode="darkMode" class="p-3 gap-2">
                        <template #title>
                            <div class="flex gap-2 items-center">
                                <div class="font-semibold">Impact</div>
                                <simple-icon
                                    :icon="simpleIcon.icons.INFORMATION_CIRCLE"
                                    :dark-mode="darkMode"
                                    :color="simpleIcon.colors.BLUE"
                                    tooltip="Rows that will be deleted"
                                />
                            </div>
                        </template>
                        <div class="max-h-[45vh] overflow-y-auto">
                            <table class="w-full table-auto border-collapse">
                                <tr class="sticky top-0 bg-light-background border-b">
                                    <th class="text-left p-2">Table</th>
                                    <th class="text-left p-2">Records To Be Deleted</th>
                                </tr>
                                <tbody>
                                <tr class="border-b" v-for="item in impact">
                                    <td class="p-2">{{ item.title }}</td>
                                    <td class="p-2">{{ item.data }}</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </simple-card>
                </div>
            </div>
            <div
                class="rounded-b-lg flex justify-end pt-4 gap-4 sticky bottom-0"
                :class="{'bg-light-module': !darkMode, 'bg-dark-module': darkMode}"
            >
                <custom-button
                    @click="$emit('close')"
                    color="gray"
                    :dark-mode="darkMode"
                >
                    Cancel
                </custom-button>
                <custom-button
                    @click="deleteCompany"
                    :dark-mode="darkMode"
                    color="red"
                    :disabled="!canDelete"
                >
                    Delete
                </custom-button>
            </div>
        </template>
    </Modal>
</template>
<script>
import Modal from "../../../Shared/components/Modal.vue";
import ApiService from "./services/api.js";
import {PERMISSIONS, useRolesPermissions} from "../../../../../stores/roles-permissions.store.js";
import LabeledValue from "../../../Shared/components/LabeledValue.vue";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../composables/useSimpleIcon.js";
import CustomInput from "../../../Shared/components/CustomInput.vue";
import SimpleCard from "../../../MarketingCampaign/SimpleCard.vue";
import Badge from "../../../Shared/components/Badge.vue";
import Accordion from "../../../Shared/components/Accordion.vue";
import BillingCardPaginatedContent
    from "../../../BillingManagement/components/BillingCardBuilders/BillingCardPaginatedContent.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import {humanReadableFromSeconds} from "../../../../../modules/helpers.js";

const simpleIcon = useSimpleIcon();

export default {
    name: "DeleteCompanyModal",
    components: {
        CustomButton,
        BillingCardPaginatedContent,
        Accordion,
        Badge,
        SimpleCard, CustomInput, SimpleIcon, LabeledValue, Modal
    },
    emits: ['close'],
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        companyId: {
            type: Number,
            required: true,
        }
    },
    data() {
        return {
            permissionStore: useRolesPermissions(),
            api: ApiService.make(this.companyId),
            loadingPrerequisites: false,
            loadingImpact: false,
            loadingInfo: false,
            info: [],
            prerequisites: [],
            impact: [],
            headers: {
                contacts: [
                    {name: 'Name', field: 'name'},
                    {name: 'Status', field: 'status'},
                ],
                calls: [
                    {name: 'To', field: 'to'},
                    {name: 'From', field: 'from'},
                    {name: 'Call Duration', field: 'recording'},
                    {name: 'Date', field: 'timestamp'},
                ],
                texts: [
                    {name: 'To', field: 'to'},
                    {name: 'From', field: 'from'},
                    {name: 'Content', field: 'content'},
                    {name: 'Date', field: 'timestamp'},
                ],
            },
            reason: "",
            simpleIcon,
        }
    },
    created() {
        this.getPrerequisites();
        this.getImpact();
        this.getInfo();
    },
    computed: {
        canDelete() {
            return this.permissionStore.hasPermission(PERMISSIONS.COMPANY_DELETE) && this.prerequisites.deletable;
        }
    },
    methods: {
        humanReadableFromSeconds,
        async getPrerequisites() {
            this.loadingPrerequisites = true;
            const response = await this.api.getPrerequisites();
            this.prerequisites = response.data.data;
            this.loadingPrerequisites = false;
        },
        async getImpact() {
            this.loadingImpact = true;
            const response = await this.api.getImpact();
            this.impact = response.data.data.data;
            this.loadingImpact = false;
        },
        async getInfo() {
            this.loadingInfo = true;
            const response = await this.api.getInfo();
            this.info = response.data.data;
            this.loadingInfo = false;
        },
        async deleteCompany() {
            this.api.deleteCompany(this.reason)
                .then((resp) => this.$emit('close',
                    resp.data.data.status ? 'success' : 'error',
                    resp.data.data.status ? 'Company Queued for Deletion' : 'Failed to Queue Deletion'
                ))
                .catch(() => this.$emit('close', 'error', 'Failed to Queue Deletion'))
        }
    }
}
</script>
