<template>
    <custom-button
        v-if="canDelete && deletable && !queued"
        @click="deleteCompanyId = companyId"
        :dark-mode="darkMode"
        color="red"
    >
        Delete
    </custom-button>
    <div
        v-else-if="queued"
    >
        Delete Scheduled
        <simple-icon
            v-if="canDelete"
            :dark-mode="darkMode"
            :icon="simpleIcon.icons.ARROW_UTURN_LEFT"
            :color="simpleIcon.colors.BLUE"
            tooltip="Cancel Deletion"
            @click="cancelDeletion(companyId)"
            clickable
        />
    </div>
    <DeleteCompanyModal
        v-if="deleteCompanyId"
        :company-id="deleteCompanyId"
        @close="handleClose"
    />
    <AlertsContainer v-if="alertActive" :dark-mode="darkMode" :alert-type="alertType" :text="alertText"/>
</template>

<script >
import CustomButton from "../../../Shared/components/CustomButton.vue";
import LabeledValue from "../../../Shared/components/LabeledValue.vue";
import Tooltip from "../../../Shared/components/Tooltip.vue";
import AlertsContainer from "../../../Shared/components/AlertsContainer.vue";
import DeleteCompanyModal from "./DeleteCompanyModal.vue";
import {PERMISSIONS, useRolesPermissions} from "../../../../../stores/roles-permissions.store.js";
import ApiService from "./services/api.js";
import {useAlerts} from "../../../../composables/useAlerts.js";
import useSimpleIcon from "../../../../../composables/useSimpleIcon.js";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";

const {alertType, alertText, alertActive, displayAlert} = useAlerts();
const simpleIcon = useSimpleIcon();

export default {
    name: 'DeleteCompanyButton',
    components: {
        LabeledValue,
        Tooltip,
        SimpleIcon, AlertsContainer, DeleteCompanyModal, CustomButton
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: Number,
            default: 0
        },
        deletable: {
            type: Boolean,
            default: false,
        },
        queued: {
            type: Boolean,
            default: false
        }
    },
    emits: ['finished'],
    data () {
        return {
            deleteCompanyId: null,
            rolesPermissions: useRolesPermissions(),
            alertType,
            alertText,
            alertActive,
            displayAlert,
            simpleIcon,
        }
    },
    methods: {
        handleClose(status, message) {
            if (status) {
                this.displayAlert(message, status)
            }

            if (status === 'success' || status === undefined) {
                this.deleteCompanyId = null;
            }

            if (status === 'success') {
                this.$emit('finished');
            }
        },
        async cancelDeletion(companyId) {
            const deleteApi = ApiService.make(companyId)

            await deleteApi.cancelDelete()
                .then((resp) => {
                    const {status, message} = resp.data.data.status
                        ? {status: 'success', message: 'Deletion Cancelled'}
                        : {status: 'error', message: 'Failed to cancel Deletion'};

                    this.displayAlert(message, status)

                    if (status === 'success') {
                        this.$emit('finished');
                    }
                }).catch(() => this.displayAlert('Failed to cancel Deletion', 'error'));
        }
    },
    computed: {
        canDelete() {
            return this.rolesPermissions.hasPermission(PERMISSIONS.COMPANY_DELETE);
        }
    }
}
</script>

