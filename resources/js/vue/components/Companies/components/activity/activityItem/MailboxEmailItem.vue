<template>
    <div class="border rounded-lg"
         :class="[darkMode ? 'text-white bg-dark-module border-dark-border' : 'bg-light-module border-light-border text-slate-900']"
    >
        <email-data-display :email="itemData" />
    </div>
</template>

<script>
import EmailDataDisplay from "../../../../Mailbox/components/EmailDataDisplay.vue";

export default {
    name: "EmailItem",
    components: {EmailDataDisplay},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        itemData: {
            type: Object,
            default: {}
        },
    },
}

</script>

<style scoped>

</style>
