<template>
    <div class="mt-3">
        <div class="flex flex-auto flex-wrap gap-4 items-end">
            <div>
                <div class="flex items-center gap-3 justify-between mb-1">
                    <label for="date-range" class="block font-medium text-sm">Date Range</label>
                    <div class="inline-flex gap-2 items-center">
                        <label for="use-no-limit" class="block font-medium text-sm text-slate-500">Include Weekends?</label>
                        <toggle-switch :dark-mode="darkMode" name="use-no-limit" id="use-no-limit" v-model="includeWeekends"></toggle-switch>
                    </div>
                </div>
                <Datepicker
                    :dark="darkMode"
                    v-model="secondsDatepickerModel"
                    :enable-time-picker="false"
                    :auto-apply="true"
                    :range="{partialRange: false, showLastInRange: false}"
                    :model-type="datePickerFormat"
                    :clearable="false"
                    :year-range="yearRange"
                    @range-end="setPeriod('')"
                    :preset-dates="datePickerPresetPeriods">
                    <template v-for="period in ['week', 'month']" #[getPeriodSlotName(period)]="{label, value, presetDate}">
                        <div
                            role="button"
                            class="whitespace-nowrap p-2 hover:bg-gray-100"
                            @click="setPeriod(period); presetDate(value)">
                            {{ label }}
                        </div>
                    </template>
                </Datepicker>
            </div>
            <div>
                <label for="industry" class="block font-medium text-sm mb-1">Industry</label>
                <Dropdown class="min-w-64" :dark-mode="darkMode" :options="industries" v-model="industry" :selected="industry" id="industry" name="industry"></Dropdown>
            </div>
            <div>
                <label for="lead-types" class="block font-medium text-sm mb-1">Lead Types</label>
                <multi-select class="min-w-64" :dark-mode="darkMode" :selected-ids="leadTypeSelections" :options="leadTypeOptions"></multi-select>
            </div>
        </div>
        <div class="grid md:flex gap-10 py-6">
            <div class="md:w-[35%]">
                <div class="grid gap-3 pb-6">
                    <div>
                        <h3 class="text-lg font-semibold">Campaign Options</h3>
                        <p class="text-sm text-slate-500">You may use an existing campaign or add more auxiliary campaigns to the simulation.</p>
                    </div>
                    <div @click="(campaignSelection ? (campaignSelection = null) : (campaignSelectionModal = true))" class="cursor-pointer inline-flex gap-2 items-center group">
                        <div class="cursor-pointer h-9 w-9 rounded-md font-bold border-2 text-primary-500 inline-flex items-center justify-center"
                            :class="[campaignSelection ? 'border-red-400 text-red-400 group-hover:bg-red-100' : 'group-hover:bg-primary-100 border-primary-500 text-primary-500' , darkMode ? 'group-hover:bg-opacity-25' : '']"
                        >
                            <span v-if="campaignSelection">
                                <svg width="13" height="15" viewBox="0 0 13 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2.48591 4.05029H1.81072V12.8278C1.81072 13.186 1.95299 13.5295 2.20624 13.7827C2.45949 14.0359 2.80296 14.1782 3.16111 14.1782H9.91306C10.2712 14.1782 10.6147 14.0359 10.8679 13.7827C11.1212 13.5295 11.2635 13.186 11.2635 12.8278V4.05029H2.48591ZM9.65514 2.02471L8.56267 0.674316H4.5115L3.41903 2.02471H0.460327V3.3751H12.6138V2.02471H9.65514Z" fill="currentColor"/></svg>
                            </span>
                            <span v-else>
                                +
                            </span>
                        </div>
                        <p v-if="campaignSelection" class="text-sm">
                            <span class="font-semibold">{{campaignSelection.companyName}}</span>
                            <br/>
                            <span class="text-primary-500 font-semibold">
                                {{campaignSelection.campaignName}}
                            </span>
                            </p>
                        <p v-else class="font-medium  group-hover:text-primary-500">Use Existing Campaign</p>
                    </div>
                    <div @click="auxCampaignModal = true" class="cursor-pointer inline-flex gap-2 items-center group">
                        <div class="cursor-pointer h-9 w-9 rounded-md font-bold border-2 border-amber-500 group-hover:bg-amber-100 text-amber-500 inline-flex items-center justify-center"
                            :class="[darkMode ? 'group-hover:bg-opacity-25' : '']"
                        >
                            +
                        </div>
                        <p class="font-medium  group-hover:text-amber-600">Optional Auxiliary Campaigns <badge class="border border-amber-600" color="amber" :dark-mode="darkMode" v-if="Object.keys(auxCampaigns).length > 0">{{ Object.keys(auxCampaigns).length > 0 ? Object.keys(auxCampaigns).length : ''}}</badge></p>
                    </div>
                </div>
                <CampaignSelectionModal :dark-mode="darkMode" :hasLocationsSelected="this.assumptions.location_ids && this.assumptions.location_ids.length > 0" @setCampaign="setCampaign" @clearCampaign="campaignSelection = null" :campaign="campaignSelection" v-if="campaignSelectionModal" @close="campaignSelectionModal = false"></CampaignSelectionModal>
                <AuxiliaryCampaignModal :dark-mode="darkMode" :campaigns="auxCampaigns" v-if="auxCampaignModal" @close="auxCampaignModal = false"></AuxiliaryCampaignModal>
                <div class="mb-2">
                    <h3 class="text-lg font-semibold">Budget</h3>
                    <p class="text-sm text-slate-500">Configure <span class="font-semibold text-primary-500">{{campaignSelection ? campaignSelection.campaignName : "the main campaigns"}}</span> budget.</p>
                </div>
                <div class="grid grid-cols-2 gap-3 mb-6">
                    <div>
                        <label for="minimum-budget" class="block font-medium text-sm mb-1">Minimum Budget</label>
                        <custom-input :dark-mode="darkMode" name="minimum-budget" id="minimum-budget" v-model="assumptions.budget.min" min="50" max="10000" type="number"/>
                    </div>
                    <div>
                        <label for="maximum-budget" class="block font-medium text-sm mb-1">Maximum Budget</label>
                        <custom-input :dark-mode="darkMode" name="maximum-budget" id="maximum-budget" v-model="assumptions.budget.max" min="100" max="10000" type="number"/>
                    </div>
                    <div>
                        <label for="budget-interval" class="block font-medium text-sm mb-1">Budget Interval</label>
                        <custom-input :dark-mode="darkMode" name="budget-interval" id="budget-interval" v-model="assumptions.budget.interval" min="50" max="1000" type="number"/>
                    </div>
                    <div class="inline-flex gap-2 items-center mt-5">
                        <label for="use-no-limit" class="block font-medium text-sm">Include No Limit?</label>
                        <toggle-switch :dark-mode="darkMode" ame="use-no-limit" id="use-no-limit" v-model="assumptions.budget.noLimit"></toggle-switch>
                    </div>
                </div>
                <div class="mb-2">
                    <h3 class="text-lg font-semibold">Pricing</h3>
                    <p class="text-sm text-slate-500">Configure <span class="font-semibold text-primary-500">{{campaignSelection ? campaignSelection.campaignName : "the main campaigns"}}</span> pricing.</p>
                </div>
                <div class="mb-3">
                    <label for="price-discount-type" class="block font-medium text-sm mb-1">Type</label>
                    <Dropdown :dark-mode="darkMode" :options="priceDiscountTypes" v-model="assumptions.priceDiscount.type" :selected="assumptions.priceDiscount.type" id="price-discount-type" name="price-discount-type"></Dropdown>
                </div>

                <div>
                    <div class="grid grid-cols-3 gap-3 mb-4" v-if="assumptions.priceDiscount.type !== 'Flat'">
                        <div class="col-span-1">
                            <label for="minimum-discount" class="block font-medium text-sm mb-1"><span>Minimum Discount</span></label>
                            <custom-input :dark-mode="darkMode" name="minimum-discount" id="minimum-discount" v-model="assumptions.priceDiscount.min" min="1" max="95" type="number"/>
                        </div>
                        <div class="col-span-1">
                            <label for="maximum-discount" class="block font-medium text-sm mb-1"><span>Maximum Discount</span></label>
                            <custom-input :dark-mode="darkMode" name="maximum-discount" id="maximum-discount" v-model="assumptions.priceDiscount.max" min="2" max="100" type="number"/>
                        </div>
                        <div class="col-span-1">
                            <label for="discount-interval" class="block font-medium text-sm mb-1"><span>Discount Interval</span></label>
                            <custom-input :dark-mode="darkMode" name="discount-interval" id="discount-interval" v-model="assumptions.priceDiscount.interval" min="1" max="10" type="number"/>
                        </div>
                    </div>
                    <div v-else class="grid grid-cols-5 gap-3 mb-4">
                        <custom-input label="Exclusive" :dark-mode="darkMode" name="discount-interval" id="discount-interval" v-model="assumptions.priceDiscount.flatPrices.exclusive" type="number"/>
                        <custom-input label="Duo" :dark-mode="darkMode" name="discount-interval" id="discount-interval" v-model="assumptions.priceDiscount.flatPrices.duo" type="number"/>
                        <custom-input label="Trio" :dark-mode="darkMode" name="discount-interval" id="discount-interval" v-model="assumptions.priceDiscount.flatPrices.trio" type="number"/>
                        <custom-input label="Quad" :dark-mode="darkMode" name="discount-interval" id="discount-interval" v-model="assumptions.priceDiscount.flatPrices.quad" type="number"/>
                        <custom-input label="Unverified" :dark-mode="darkMode" name="discount-interval" id="discount-interval" v-model="assumptions.priceDiscount.flatPrices.unverified" type="number"/>
                    </div>
                </div>
                <div>
                    <CustomButton @click="submit">Generate Simulation</CustomButton>
                </div>
            </div>
            <div class="flex-grow">
                <h3 class="text-lg font-semibold">Service Area</h3>
                <p class="text-sm text-slate-500">Select the areas you wish to include in your simulation report.</p>
                <Locations :forced-initial-locations="campaignLocations" :dark-mode="darkMode" @update:locations="handleLocationsUpdate" />
            </div>
        </div>
        <div class="flex items-end">

        </div>
    </div>

</template>

<script>

import Locations from "./Locations.vue";
import CustomInput from "../../../Shared/components/CustomInput.vue";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import CustomCheckbox from "../../../Shared/SlideWizard/components/CustomCheckbox.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import {slugify} from "../../../Shared/services/strings.js";
import {DateTime, Duration} from "luxon";
import MultiSelect from "../../../Shared/components/MultiSelect.vue";
import Modal from "../../../Shared/components/Modal.vue";
import CampaignSelectionModal from "./CampaignSelectionModal.vue";
import AuxiliaryCampaignModal from "./AuxiliaryCampaignModal.vue";
import ToggleSwitch from "../../../Shared/components/ToggleSwitch.vue";
import Badge from "../../../Shared/components/Badge.vue";

export default {
    name: "Inputs",
    components: {
        Badge,
        ToggleSwitch,
        AuxiliaryCampaignModal,
        CampaignSelectionModal,
        Modal, MultiSelect, CustomButton, CustomCheckbox, Dropdown, CustomInput, Locations},
    data: function () {
        return {
            industry: 'Solar',
            industries: [
                'Solar',
                'Roofing',
                'Siding',
                'Windows',
                'HVAC',
            ],
            locationType: 'State',
            locationTypes: ['State', 'County'],
            priceDiscountTypes: ['Percentage Discount', 'Dollar Discount', 'Flat'],
            includeWeekends: true,
            assumptions: {
                budget: {
                    min     : 500,
                    max     : 1000,
                    noLimit : true,
                    interval: 100,
                },
                priceDiscount: {
                    min: 5,
                    max: 20,
                    interval: 5,
                    type: 'Percentage Discount',
                    flatPrices: {
                        exclusive: 100,
                        duo: 80,
                        trio: 60,
                        quad: 40,
                        unverified: 10,
                    },

                },
                zipCodes: []
            },
            timezoneOffset: DateTime.local().toFormat('Z'),
            datePickerFormat: 'yyyy-M-d',
            period: 'week',
            periodSeconds: [null,null],
            leadTypeOptions: [{id: 'verified', name: 'Verified'}, {id: 'unverified', name:'Unverified'}],
            leadTypeSelections: ['verified'],
            campaignSelectionModal: false,
            campaignSelection: null,
            auxCampaignModal: false,
            auxCampaigns: {},
            campaignLocations: [],
        }
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        apiService: null,
    },
    created() {
        this.secondsDatepickerModel = [
            this.now.startOf('week').minus(Duration.fromObject({weeks: 1})).toFormat(this.datePickerFormat),
            this.now.endOf('week').minus(Duration.fromObject({weeks: 1})).toFormat(this.datePickerFormat)
        ];
    },
    methods: {
        submit() {
            this.$emit('update-loading', true);
            this.apiService.getProfitability({
                'industry'    : slugify(this.industry),
                'location_ids': this.assumptions.location_ids,
                'start_date'  : this.secondsDatepickerModel[0],
                'end_date'    : this.secondsDatepickerModel[1],
                'lead_types'  : this.leadTypeSelections,

                'campaign': {
                    'origin_campaign_id' : this.campaign?.campaignId ?? null,
                    'include_weekends'   : this.includeWeekends,
                    'budget_min'         : this.assumptions.budget.min,
                    'budget_max'         : this.assumptions.budget.max,
                    'budget_use_no_limit': this.assumptions.budget.noLimit,
                    'budget_interval'    : this.assumptions.budget.interval,
                    'price_min'          : this.assumptions.priceDiscount.min,
                    'price_max'          : this.assumptions.priceDiscount.max,
                    'price_interval'     : this.assumptions.priceDiscount.interval,
                    'price_type'         : slugify(this.assumptions.priceDiscount.type),
                    'flat_prices'        : this.assumptions.priceDiscount.flatPrices,
                },

                'aux_campaigns': this.auxCampaigns
            }).then(resp => {
                this.$emit('update-data', resp.data.data);
            }).catch(e =>
                this.$emit('alert', 'error', this.getMessageFromError(e))
            ).finally(() => this.$emit('update-loading', false));
        },
        getMessageFromError(e){
            if(!e?.response?.data?.message)
                return e.message

            switch (e?.response?.data?.message){
                case 'The location ids field is required.':
                    return 'Locations cannot be empty'
                default:
                    return e?.response?.data?.message
            }
        },
        handleLocationsUpdate(newValue) {
            this.assumptions.location_ids = newValue;
        },
        convertDatePickerStringsToTimestamps(datePickerStrings) {
            return datePickerStrings.map((date) => {
                const dateParts = date.split('-');

                return DateTime.fromObject({year: dateParts[0], month: dateParts[1], day: dateParts[2]}, {zone: this.luxonTimezone}).toUnixInteger();
            });
        },
        setPeriod(period) {
            this.period = period;
        },
        getPeriodSlotName(p) {
            return p + '-button';
        },
        setCampaign(campaign){
            this.campaignSelectionModal = false
            this.campaignSelection = campaign
            if(campaign.useCampaignLocations){
                this.apiService.getCampaignData(campaign.campaignId).then(resp => {
                    this.campaignLocations = resp.data.data.locations
                    this.industry = resp.data.data.industry
                })
            }
        },
    },
    computed: {
        now() {
            return DateTime.now().setZone(this.luxonTimezone).startOf('day');
        },
        luxonTimezone() {
            return `UTC${this.timezoneOffset}`;
        },
        secondsDatepickerModel: {
            get() {
                return [
                    DateTime.fromSeconds(this.periodSeconds[0], {zone: this.luxonTimezone}).startOf('day').toFormat(this.datePickerFormat),
                    DateTime.fromSeconds(this.periodSeconds[1], {zone: this.luxonTimezone}).startOf('day').toFormat(this.datePickerFormat)
                ];
            },
            set(value) {
                this.periodSeconds = this.convertDatePickerStringsToTimestamps(value);
            }
        },
        yearRange() {
            return [2024, this.now.year]
        },
        datePickerPresetPeriods() {
            return [
                {
                    label: "Last Week",
                    value: (() => {
                        return [
                            this.now.startOf('week').minus(Duration.fromObject({weeks: 1})).toFormat(this.datePickerFormat),
                            this.now.endOf('week').minus(Duration.fromObject({weeks: 1})).toFormat(this.datePickerFormat)
                        ];
                    })(),
                    noTz: true,
                    slot: this.getPeriodSlotName('week')
                },
                {
                    label: "Last Month",
                    value: (() => {
                        return [
                            this.now.startOf('month').minus(Duration.fromObject({months: 1})).toFormat(this.datePickerFormat),
                            this.now.endOf('month').minus(Duration.fromObject({months: 1})).toFormat(this.datePickerFormat)
                        ];
                    })(),
                    noTz: true,
                    slot: this.getPeriodSlotName('month')
                },
            ]
        }
    }
}
</script>

<style scoped>

</style>
