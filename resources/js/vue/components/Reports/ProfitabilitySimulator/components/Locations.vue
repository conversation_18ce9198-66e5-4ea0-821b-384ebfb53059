<template>
    <div>
        <!--    HEADER BUTTONS    -->
        <div class="flex justify-end items-center gap-x-6">
            <CustomButton
                color="primary-outline"
                @click="toggleZipCodeUploadModal(true)"
                :dark-mode="darkMode"
            >
                Upload File
            </CustomButton>
            <CustomButton
                color="primary-outline"
                @click="toggleZipCodeRadiusModal(true)"
                :dark-mode="darkMode"
            >
                Add Zip Codes By Radius
            </CustomButton>
        </div>

        <LoadingSpinner v-if="loading" :dark-mode="darkMode" />
        <div v-else>
            <!--    MAIN LOCATION NAVIGATION    -->
            <LocationCheckboxSelect
                v-if="!loading"
                :stores-initialized="storesInitialized"
                :model-value="slideInputs.zip_codes"
                :imported-zip-codes="importedCodes"
                input-key="zip_codes"
                @update:model-value="handleInputUpdate"
                :dark-mode="darkMode"
                force-allow-zip-codes="true"
                :forced-initial-locations="forcedInitialLocations"
            />


            <!--    Zip Radius modal    -->
            <ZipCodeRadiusModal
                v-if="showZipCodeRadiusModal"
                @hideZipRadiusModal="toggleZipCodeRadiusModal"
                @addZipCodesFromModal="addZipCodesFromModal"
                :dark-mode="darkMode"
            />
            <!--    Zip Upload modal    -->
            <ZipCodeUploadModal
                v-if="showZipCodeUploadModal"
                @hideZipUploadModal="toggleZipCodeUploadModal(false)"
                @addZipCodesFromModal="addZipCodesFromModal"
                :dark-mode="darkMode"
            />
        </div>
    </div>
</template>

<script>
import { useLocalityDataStore } from "../stores/locality-data.js";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import ZipCodeRadiusModal from "../../../Campaigns/Wizard/components/ZipCodeRadiusModal.vue";
import ZipCodeUploadModal from "../../../Campaigns/Wizard/components/ZipCodeUploadModal.vue";
import LocationCheckboxSelect from "../../../Campaigns/Wizard/components/LocationCheckboxSelect.vue";
export default {
    name: "Locations",
    components: {
        LocationCheckboxSelect,
        ZipCodeUploadModal,
        ZipCodeRadiusModal,
        LoadingSpinner,
        CustomButton

    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        forcedInitialLocations: {
            type: Array,
            default: null,
        },
    },
    emits: ['update:locations'],
    data() {
        return {
            activeZipCodes: null,
            localityStore: useLocalityDataStore(),
            storesInitialized: false,
            slideInputs: { zip_codes: [], },
            importedCodes: { append: true, zipCodes: [] },
            loading: true,
            showZipCodeRadiusModal: false,
            showZipCodeUploadModal: false,
        }
    },
    mounted() {
        this.initialize();
    },
    methods: {
        async initialize() {
            await this.localityStore.initialize();
            if (!this.localityStore.storeInitialized) {
                console.error('Failed to fetch location data.');
            }
            this.storesInitialized = true;
            this.loading = false;
        },
        toggleZipCodeUploadModal(show) {
            if (show === undefined)
                this.showZipCodeUploadModal = !this.showZipCodeUploadModal;
            else
                this.showZipCodeUploadModal = show;
        },
        toggleZipCodeRadiusModal(show) {
            if (show === undefined)
                this.showZipCodeRadiusModal = !this.showZipCodeRadiusModal;
            else
                this.showZipCodeRadiusModal = show;
        },
        addZipCodesFromModal(payload, appendZipCodes) {
            this.importedCodes = {
                append: !!appendZipCodes,
                zipCodes: payload,
            };

            this.toggleZipCodeUploadModal(false);
            this.toggleZipCodeRadiusModal(false);
        },
        handleInputUpdate(newValue) {
            this.slideInputs.zip_codes = newValue;

            let locationIds = [];

            for(const zip of Object.entries(newValue)) {
                locationIds.push(zip[1].id);
            }

            this.$emit('update:locations', locationIds);
        },
    },
}
</script>
