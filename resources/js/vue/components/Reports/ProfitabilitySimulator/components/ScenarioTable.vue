<template>
    <table class="w-full border rounded-md" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
        <thead class="border-b" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
            <tr>
                <th
                    class="text-left text-xs uppercase p-2 text-slate-500 font-bold">
                    Scenario
                </th>
                <th
                    class="p-2 text-xs uppercase font-bold"
                    v-for="(data, price) in Object.values(scenarios)[0]">
                    {{ priceType === 'flat' ? '' : price }}
                </th>
            </tr>
        </thead>
        <tbody class="divide-y" :class="[darkMode ? 'divide-dark-border' : 'divide-light-border']">
            <slot name="tbody">
            </slot>
        </tbody>
    </table>
</template>

<script>
export default {
    name: "ScenarioTable",
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        scenarios: {
            type: Object,
            default: null
        },
        priceType: {
            type: String,
            default: null
        },
    },
}
</script>

<style scoped>

</style>
