<template>
    <div class="main-layout font-body">
        <alerts-container :text="alertText" :alert-type="alertType" v-if="alertActive" :dark-mode="darkMode"/>
        <div class="w-full">
            <div class="w-full flex-auto px-6 py-5 relative" :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
                <div class="border rounded-lg px-8 pb-8 "
                     :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border text-grey-120': darkMode}">
                    <div class="flex items-center justify-between flex-wrap pt-6 pb-4">
                        <div class="flex justify-between items-center w-full">
                            <div class="inline-flex items-center" :class="{'text-grey-120': darkMode}">
                                <a href="/reports" class="text-xl font-medium text-grey-300 leading-none hover:text-grey-400 transition duration-200 cursor-pointer">Reports</a>
                                <h3 class="text-xl inline-flex font-medium leading-none items-center" :class="{'text-black': !darkMode, 'text-grey-120': darkMode}">
                                    <svg class="mx-4 fill-current" width="7" height="13" viewBox="0 0 7 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M0.327334 0.260067C0.735992 -0.11144 1.36844 -0.0813234 1.73995 0.327334L6.73995 5.82733C7.08669 6.20876 7.08669 6.79126 6.73995 7.17268L1.73995 12.6727C1.36844 13.0813 0.735992 13.1115 0.327334 12.7399C-0.0813234 12.3684 -0.11144 11.736 0.260067 11.3273L4.64855 6.50001L0.260067 1.67268C-0.11144 1.26402 -0.0813234 0.631574 0.327334 0.260067Z"/>
                                    </svg>
                                    Profitability Simulator
                                </h3>
                            </div>
                        </div>
                    </div>
                    <inputs :dark-mode="darkMode" @update-data="updateData" @update-loading="updateLoading" @alert="activateAlert" :api-service="apiService"></inputs>
                    <loading-spinner v-if="loading" :dark-mode="darkMode"/>
                    <div id="results" class="grid lg:grid-cols-2 gap-10 mt-6" style="scroll-margin-top: 200px;" v-if="scenarios">
                        <div>
                            <h3 class="font-semibold mb-2">
                                New Revenue
                            </h3>
                            <ScenarioTable
                                :dark-mode="darkMode"
                                :scenarios="scenarios"
                                :price-type="priceType"
                            >
                                <template v-slot:tbody>
                                    <tr v-for="(priceScenarios, scenario) in scenarios"
                                        :class="[darkMode ? 'text-slate-100 hover:bg-dark-module border-dark-border bg-dark-background' : 'text-slate-900 hover:bg-light-module border-light-border bg-light-background']">
                                        <td
                                            class="p-2"
                                        >{{scenario}}</td>
                                        <td
                                            class="text-center p-2"
                                            v-for="results in priceScenarios">
                                            ${{ formatNumber(Math.round(results.new_revenue)) }}
                                        </td>
                                    </tr>
                                </template>
                            </ScenarioTable>
                        </div>
                        <div>
                            <h3 class="font-semibold mb-2">
                                New Buyer Spend
                            </h3>
                            <ScenarioTable
                                :dark-mode="darkMode"
                                :scenarios="scenarios"
                                :price-type="priceType"
                            >
                                <template v-slot:tbody>
                                    <tr v-for="(priceScenarios, scenario) in scenarios"
                                        :class="[darkMode ? 'text-slate-100 hover:bg-dark-module border-dark-border bg-dark-background' : 'text-slate-900 hover:bg-light-module border-light-border bg-light-background']">
                                        <td
                                            class="p-2"
                                        >{{scenario}}</td>
                                        <td
                                            class="text-center p-2"
                                            v-for="results in priceScenarios">
                                            ${{ formatNumber(Math.round(results.spend)) }}
                                        </td>
                                    </tr>
                                </template>
                            </ScenarioTable>
                        </div>
                        <div>
                            <h3 class="font-semibold mb-2">
                                Leads Sold to Buyer
                            </h3>
                            <ScenarioTable
                                :dark-mode="darkMode"
                                :scenarios="scenarios"
                                :price-type="priceType"
                            >
                                <template v-slot:tbody>
                                    <tr v-for="(priceScenarios, scenario) in scenarios"
                                        :class="[darkMode ? 'text-slate-100 hover:bg-dark-module border-dark-border bg-dark-background' : 'text-slate-900 hover:bg-light-module border-light-border bg-light-background']">
                                        <td
                                            class="p-2"
                                        >{{scenario}}</td>
                                        <td
                                            class="text-center p-2"
                                            v-for="results in priceScenarios">
                                            {{ formatNumber(results.new_legs_sold_count) }}
                                        </td>
                                    </tr>
                                </template>
                            </ScenarioTable>
                        </div>
                        <div>
                            <h3 class="font-semibold mb-2">
                                Previously Unsold Leads Now Sold
                            </h3>
                            <ScenarioTable
                                :dark-mode="darkMode"
                                :scenarios="scenarios"
                                :price-type="priceType"
                            >
                                <template v-slot:tbody>
                                    <tr v-for="(priceScenarios, scenario) in scenarios"
                                        :class="[darkMode ? 'text-slate-100 hover:bg-dark-module border-dark-border bg-dark-background' : 'text-slate-900 hover:bg-light-module border-light-border bg-light-background']">
                                        <td
                                            class="p-2"
                                        >{{scenario}}</td>
                                        <td
                                            class="text-center p-2"
                                            v-for="results in priceScenarios">
                                            {{ formatNumber(results.new_leads_sold_count) }}
                                        </td>
                                    </tr>
                                </template>
                            </ScenarioTable>
                        </div>
                        <div>
                            <h3 class="font-semibold mb-2">
                                Lead Type Counts - Exclusive/Duo/Trio/Quad/Unverified
                            </h3>
                            <ScenarioTable
                                :dark-mode="darkMode"
                                :scenarios="scenarios"
                                :price-type="priceType"
                            >
                                <template v-slot:tbody>
                                    <tr v-for="(priceScenarios, scenario) in scenarios"
                                        :class="[darkMode ? 'text-slate-100 hover:bg-dark-module border-dark-border bg-dark-background' : 'text-slate-900 hover:bg-light-module border-light-border bg-light-background']">
                                        <td
                                            class="p-2"
                                        >{{scenario}}</td>
                                        <td
                                            class="text-center p-2"
                                            v-for="results in priceScenarios">
                                            {{ formatNumber(results.lead_type_counts['1']) }} /
                                            {{ formatNumber(results.lead_type_counts['2']) }} /
                                            {{ formatNumber(results.lead_type_counts['3']) }} /
                                            {{ formatNumber(results.lead_type_counts['4']) }} /
                                            {{ formatNumber(results.lead_type_counts['6']) }}
                                        </td>
                                    </tr>
                                </template>
                            </ScenarioTable>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
import Inputs from "./components/Inputs.vue";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import ApiService from "./services/api/api";
import {formatNumber} from "chart.js/helpers";
import ScenarioTable from "./components/ScenarioTable.vue";

export default {
    name: "ProfitabilitySimulator",
    components: {
        ScenarioTable,
        Inputs,
        AlertsContainer,
        LoadingSpinner,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
    },
    data: function() {
        return {
            scenarios: null,
            loading: false,
            alertActive: false,
            alertType: '',
            alertText: '',
            apiService: null,
            priceType: null,
        }
    },
    created() {
        this.apiService = ApiService.make();
    },
    methods: {
        formatNumber,
        activateAlert(type, text) {
            this.alertActive = true;
            this.alertType = type;
            this.alertText = text;
            setTimeout(()=>{
                this.alertActive = false
            },6000)
        },
        updateData(data) {
            this.priceType = data.price_type;
            this.scenarios = data.scenarios;
            this.activateAlert('success', 'Analysis results updated');
        },
        updateLoading(value) {
            this.loading = value;

            if(!value) {
                const el = document.getElementById('results');
                el.scrollIntoView({behavior: 'smooth'});
            }
        },
    }
}
</script>

<style scoped>

</style>
