import axios from 'axios';

export default class ApiService {
    constructor(baseUrl, baseEndpoint, version) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    static make() {
        return new ApiService('internal-api', 'county-coverage-report', 1);
    }

    getReportOptions() {
        return this.axios().get('/options');
    }

    getFilterOptionUpdates(filterInputs) {
        return this.axios().post('/filter-option-updates', filterInputs);
    }

    getReport(params) {
        return this.axios().get('/', {params});
    }

    saveUserPreset(filterPayload) {
        return this.axios().put('/save-preset', filterPayload);
    }

    deleteUserPreset(filterName) {
        return this.axios().delete('/delete-preset', {
            params: {
                name: filterName
            }
        });
    }

    downloadErplZipCodes(industry) {
        return this.axios().get(`/erpl-zip-codes/${industry}`, {
            responseType: 'blob'
        });
    }
}
