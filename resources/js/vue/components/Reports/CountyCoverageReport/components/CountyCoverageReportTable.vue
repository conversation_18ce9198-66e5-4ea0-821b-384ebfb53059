<template>
    <div class="mt-5 flex flex-col">
        <div class="my-2 -mx-4 sm:-mx-6 lg:-mx-8">
            <div class="w-full overflow-x-auto overflow-y-auto" :style="{ maxHeight: fullScreen ? '75vh' : '60vh', maxWidth: '100%'}">
                <table class="min-w-full divide-y table-container" :class="[!darkMode ? 'divide-gray-300 light-table-bg-1 light-table-bg-2' : 'divide-dark-175 dark-table-bg-1 dark-table-bg-2']">
                    <thead>
                        <tr class="divide-x" :class="[!darkMode ? 'divide-gray-200 light-table-background-even light-table-background-odd' : 'divide-dark-175']">
                            <th v-for="colKey in columnOrder" @click="sortTable(colKey)" scope="col"
                                class="sticky top-0 whitespace-nowrap z-10 border-b bg-opacity-75 backdrop-blur-2xl py-2 pl-8 pr-3 text-sm font-medium backdrop-filter" :class="[!darkMode ? 'border-gray-300 bg-light-module text-gray-900' : 'border-dark-border bg-dark-module text-grey-100']">
                                <div class="cursor-pointer group inline-flex formatted-text text-center" :class="[(sortColumn === colKey) ? 'text-blue-550' : '']">
                                    {{ columns[colKey]['name'] }}
                                    <span v-if="sortColumn === colKey && sortDirection === 'desc'" class="ml-2 flex-none rounded text-blue-550">
                                        <ChevronDownIcon class="h-5 w-5" aria-hidden="false" />
                                    </span>
                                    <span v-else-if="sortColumn === colKey && sortDirection === 'asc'" class="ml-2 flex-none rounded text-blue-550">
                                        <ChevronUpIcon class="h-5 w-5" aria-hidden="false" />
                                    </span>
                                    <span v-else class="invisible ml-2 flex-none rounded text-gray-400 group-hover:visible group-focus:visible">
                                        <ChevronDownIcon class="h-5 w-5" aria-hidden="true" />
                                    </span>
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="divide-y styled-table" :class="[!darkMode ? 'divide-gray-200' : 'divide-dark-175']">
                        <tr class="divide-x cursor-pointer" :class="[!darkMode ? 'divide-gray-200 light-table-background-even light-table-background-odd' : 'divide-dark-175']" v-for="(row, group, index) in this.sortedData" @click="selectRow(index)">
                            <td v-for="colKey in columnOrder" class="overflow-x-auto overflow-y-auto whitespace-nowrap left-0 py-2 bg-opacity-50 text-sm text-center font-medium cursor-pointer" :class="[!darkMode ? 'text-gray-600 hover:text-gray-900 bg-light-module' : 'text-white hover:text-gray-300 bg-dark-module']" :style="{maxWidth: '500px'}">
                                <div v-if="columns[colKey].type === 'info_string'" :class="columns[colKey]['class']">
                                    <span v-for="(line, lineIndex) in parseLineInfoStringObject(getTableValue(row, colKey, columns[colKey]))" :key="lineIndex">
                                        <span v-for="(segment, segIndex) in parseSegmentInfoStringObject(line, row, colKey, columns[colKey])" :key="segIndex">
                                          <template v-if="segment['link']">
                                            <a :href="segment['link']" :class="segment['class']" target="_blank" rel="noopener noreferrer">{{ segment.text }}</a>
                                          </template>
                                          <template v-else>
                                            <span :class="segment.class">{{ segment.text }}</span>
                                          </template>
                                        </span>
                                        <br>
                                    </span>
                                </div>
                                <div v-else>{{ formatTableValue(getTableValue(row, colKey, columns[colKey]), columns[colKey]) }}</div>
                            </td>
                        </tr>
                        <tr class="divide-x bottom-border" :class="[!darkMode ? 'divide-gray-200 light-table-background-even light-table-background-odd' : 'divide-dark-175']">
                            <td class="sticky left-0 text-center z-20 whitespace-nowrap py-2 text-sm font-bold backdrop-filter" :class="[!darkMode ? 'text-black bg-light-module' : 'text-white bg-dark-module']">
                                Total
                            </td>
                            <td v-for="colKey in valueColumns" class="whitespace-nowrap group left-0 py-2 bg-opacity-50 text-sm text-center font-bold cursor-pointer" :class="[!darkMode ? 'text-black hover:text-gray-900 bg-light-module' : 'text-white hover:text-gray-300 bg-dark-module']">
                               <div class="inline-flex w-full justify-between">
                                   <span @click="moveColumn(colKey, -1)" class="invisible flex-none rounded text-gray-400 group-hover:visible group-focus:visible">
                                            <ChevronLeftIcon class="h-5 w-5" aria-hidden="true" />
                                   </span>
                                   <div>
                                       <div>{{ getPrefix(columns[colKey]) }} {{ formatTableValue(getTotalValue(colKey, data, columns[colKey]), columns[colKey], true) }}</div>
                                   </div>
                                   <span @click="moveColumn(colKey, 1)" class="invisible flex-none rounded text-gray-400 group-hover:visible group-focus:visible">
                                            <ChevronRightIcon class="h-5 w-5" aria-hidden="true" />
                                   </span>
                               </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</template>

<script>
import {ChevronDownIcon, ChevronUpIcon, ChevronLeftIcon, ChevronRightIcon} from "@heroicons/vue/solid";
import { downloadCsvString } from "../../../../../composables/exportToCsv.js";
import CustomButton from "../../../Shared/components/CustomButton.vue";

export default {
    name: "ReportTable",
    components: {
        CustomButton,
        ChevronDownIcon, ChevronUpIcon, ChevronLeftIcon, ChevronRightIcon},
    data: function() {
        return {
            selectedRow: null,
            sortedData: null,
            sortColumn: null,
            sortDirection: null,
            columnOrder: null,
        }
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        columns: {
            type: Object,
            default: null,
        },
        allColumnOrder: {
            type: Array,
            default: null,
        },
        returnedColumns: {
            type: Object,
            default: null,
        },
        data: {
            type: Object,
            default: null,
        },
        compareData: {
            type: Object,
            default: null,
        },
        reportsPage: {
            type: Boolean,
            default: false,
        },
        fullScreen: {
            type: Boolean,
            default: false,
        },
        baseStart: {
            type: String,
            default: '',
        },
        baseEnd: {
            type: String,
            default: '',
        },
        industry: {
            type: String,
            default: '',
        }
    },
    created() {
        this.columnOrder = this.allColumnOrder.filter(col => Object.keys(this.columns).includes(String(col)));
        this.sortedData = this.data;
        if (!this.sortColumn || (this.sortColumn === this.groupKey)) {
            this.sortTable(this.sortColumn, true);
        }
    },
    computed: {
        valueColumns() {
            let copiedColumns = { ...this.columnOrder };
            delete copiedColumns[0];
            return copiedColumns;
        }
    },
    methods: {
        sortTable(sortColumn, initial = false) {
            const firstKey = this.columnOrder[0];

            if (sortColumn !== this.sortColumn) {
                this.sortColumn = sortColumn;
                this.sortDirection = 'desc';
            } else if (sortColumn === this.sortColumn && this.sortDirection === 'desc' && !initial) {
                this.sortColumn = sortColumn;
                this.sortDirection = 'asc';
            } else {
                this.sortColumn = firstKey;
                this.sortDirection = 'desc';
            }

            if (this.sortColumn === undefined || this.sortColumn === null)
                return;

            // Sorting by other column value
            if (this.columns[this.sortColumn]?.type === 'string' || this.columns[this.sortColumn]?.type === 'info_string') {
                if (this.sortDirection === 'desc') {
                    this.sortedData = Object.values(this.data).sort((a, b) => a[this.sortColumn]?.localeCompare(b[this.sortColumn]));
                } else {
                    this.sortedData = Object.values(this.data).sort((a, b) => b[this.sortColumn]?.localeCompare(a[this.sortColumn]));
                }
            } else {
                if (this.sortDirection === 'desc') {
                    this.sortedData = Object.values(this.data).sort((a, b) => parseFloat(b[this.sortColumn] ? b[this.sortColumn] : 0) - parseFloat(a[this.sortColumn] ? a[this.sortColumn] : 0));
                } else {
                    this.sortedData = Object.values(this.data).sort((a, b) => parseFloat(a[this.sortColumn] ? a[this.sortColumn] : 0) - parseFloat(b[this.sortColumn] ? b[this.sortColumn] : 0));
                }
            }

        },
        getTableValue(row, colKey, colData) {
            if (!row) {
                return 0;
            }

            // Column data included in sql query here
            if (!colData['calculated']) {
                return colKey in row ? row[colKey] : 0;
            }

            if (colData['calculated']['operator'] === 'CONCAT') {
                return this.getConcatTableValue(row, colKey, colData);
            }

            // Calculated column value
            const FIRST_COLUMN = colData['calculated']['first_column'];
            const OPERATOR = colData['calculated']['operator'];
            const SECOND_COLUMN = colData['calculated']['second_column'];

            // If calculated value depends on other calculated values
            const FIRST_COLUMN_VALUE = (FIRST_COLUMN in row) ? row[FIRST_COLUMN] :
                this.getTableValue(row, colData['calculated']['first_column'], this.returnedColumns[colData['calculated']['first_column']]);
            const SECOND_COLUMN_VALUE = (SECOND_COLUMN in row) ? row[SECOND_COLUMN] :
                this.getTableValue(row, colData['calculated']['second_column'], this.returnedColumns[colData['calculated']['second_column']]);

            // Apply calculation
            if (OPERATOR === '+') {
                row[colKey] = FIRST_COLUMN_VALUE + SECOND_COLUMN_VALUE;
            } else if (OPERATOR === '-') {
                row[colKey] = FIRST_COLUMN_VALUE - SECOND_COLUMN_VALUE;
            } else if (OPERATOR === '*') {
                row[colKey] = FIRST_COLUMN_VALUE * SECOND_COLUMN_VALUE;
            } else if (OPERATOR === '/') {
                if (SECOND_COLUMN_VALUE !== 0) {
                    row[colKey] = FIRST_COLUMN_VALUE / SECOND_COLUMN_VALUE;
                } else {
                    row[colKey] = 0;
                }
            }
            return row[colKey];
        },
        getConcatTableValue(row, colKey, colData) {
            const CONCAT_FIELDS = colData['calculated']['concat_fields'];
            let colValue = "";

            for (const field in CONCAT_FIELDS) {
                if (CONCAT_FIELDS[field] === 'col') {
                    colValue += this.formatTableValue(this.getTableValue(row, field, this.returnedColumns[field]), this.returnedColumns[field], false, 0);
                } else {
                    colValue += field;
                }
            }

            return colValue;
        },
        formatTableValue(value, colData, total = false, fractionDigits = 2) {
            if (colData['type'] === 'dollar') {
                const formatter = new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD'
                });
                return formatter.format(value);
            } else if (colData['type'] === 'decimal') {
                return value ? Number(value).toFixed(2) : '0.00';
            } else if (colData['type'] === 'percent') {
                return ((value*100).toFixed(fractionDigits)) + '%';
            } else if (total || colData['type'] === 'string' || colData['type'] === 'info_string') {
                return value ? value.toLocaleString() : '';
            } else if (total || colData['type'] === 'count') {
                return value ? Intl.NumberFormat().format(value) : '';
            }
            return value ? value.toLocaleString() : 0;
        },
        getTotalValue(colKey, data, colData, keyFilter = null) {
            if (colData['total'] === 'avg') {
                return this.avgColumn(colKey, data, keyFilter);
            } else if (colData['total'] === 'none') {
                return ' ';
            }
            return this.sumColumn(colKey, data, keyFilter);
        },
        sumColumn(colKey, data, keyFilter = null) {
            let sum = 0;
            const filterGroups = keyFilter ? Object.keys(keyFilter) : [];

            for (const group in data) {
                // Key filter used to only sum compare totals that have group key present in the report data
                if (keyFilter === null || filterGroups.indexOf(group) > -1) {
                    const groupData = group in data ? data[group] : [];
                    const value = colKey in groupData ? groupData[colKey] : 0;
                    sum += parseFloat(value ? value : 0);
                }
            }
            return sum;
        },
        avgColumn(colKey, data, keyFilter = null) {
            let sum = 0;
            let dividend = 0;
            const filterGroups = keyFilter ? Object.keys(keyFilter) : [];

            for (const group in data) {
                // Key filter used to only sum compare totals that have group key present in the report data
                if (keyFilter === null || filterGroups.indexOf(group) > -1) {
                    const groupData = group in data ? data[group] : [];
                    const value = colKey in groupData ? groupData[colKey] : 0;
                    sum += parseFloat(value ? value : 0);
                    dividend++;
                }
            }
            if (dividend > 0) {
                return Math.round((sum / dividend)*100) / 100;
            }
            return 0;
        },
        getPrefix(colData) {
            if (colData['total'] === 'avg') {
                return 'Avg: '
            }
            return '';
        },
        selectRow(index) {
            // Toggle the selection
            this.selectedRow = this.selectedRow === index ? null : index;
        },
        getCurrentDate() {
            const now = new Date();

            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');

            return `${year}-${month}-${day}`;
        },
        exportToCsv() {
            const fileName = 'county-coverage-report_' +this.industry + '_' + this.getCurrentDate();

            const colNames = [];
            for (const colKey in this.columnOrder) {
                const col = this.columnOrder[colKey];
                colNames.push(this.columns[col].name);
            }

            let rows = [];
            for (const dataKey in this.sortedData) {
                let row = [];
                for (const colKey in this.columnOrder) {
                    const col = this.columnOrder[colKey];
                    const value = this.getTableValue(this.sortedData[dataKey], col, this.columns[col]);
                    if (this.columns[col]['type'] === 'info_string'){
                        const formattedValue = value ? this.formatInfoStringForCsv(value, this.sortedData[dataKey], col, this.columns[col]) : "";
                        row.push(formattedValue);
                    } else {
                        const formattedValue = value ? value.toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 2 }) : 0;
                        row.push(formattedValue);
                    }
                }
                rows.push(row);
            }
            downloadCsvString(colNames, rows, fileName);
        },
        formatInfoStringForCsv(text, row, colKey, colData) {
            const lines = this.parseLineInfoStringObject(text);
            let value = "";
            for (const line in lines) {
                const infoObject = this.parseSegmentInfoStringObject(lines[line], row, colKey, colData);
                for (const segment in infoObject) {
                    if (infoObject[segment]['link']) {
                        value += "HYPERLINK(\"\"" + window.location.origin + infoObject[segment]['link'] + "\"\",\"\"" + infoObject[segment]['text'] + "\"\")";
                    } else {
                        value += infoObject[segment]['text'];
                    }
                }
                value += "; ";
            }

            return value;
        },
        moveColumn(colKey, distance) {
            const oldIndex = this.columnOrder.indexOf(colKey);
            const newIndex = oldIndex + distance;

            if (newIndex < 0 || newIndex >= this.columnOrder.length)
                return;

            const element = this.columnOrder.splice(oldIndex, 1)[0];

            // Insert the element at the next index
            this.columnOrder.splice(newIndex, 0, element);
        },
        parseLineInfoStringObject(text) {
            if (!text)
                return '';

            return text.split("\n");
        },
        parseSegmentInfoStringObject(text, row, colKey, colData) {
            if (!text)
                return '';
            // Turns: $[text[Test Name],link[/companies/6811],class[text-blue]]$ $[text[: Campaign],class[text-green]]$ Name... into:
            // [
            //     { text: 'Test Name', link: '/companies/6811', class: 'text-blue'},
            //     { text: ': Campaign', link: '', class: 'text-green'},
            //     { text: ' Name...', link: '', class: ''},
            // ];

            const result = [];

            // Define regex patterns
            const segmentRegex = /\$\[(.*?)\]\$/g; // Regex to match each segment between $[ ... ] and $
            const attributeRegex = /(text|link|class|if)\[([^\]]*)\]/g; // Regex to match attributes text, link, and class

            let match;
            let lastIndex = 0;

            // Find all segments
            while ((match = segmentRegex.exec(text)) !== null) {
                const segment = match[1]; // Extract the content inside $[ ... ]$

                const attributes = {};
                let attrMatch;

                while ((attrMatch = attributeRegex.exec(segment)) !== null) {
                    const [fullMatch, key, value] = attrMatch;
                    attributes[key] = value;
                }

                if (attributes.if) {
                    const compares = attributes.if.split(',');
                    if (compares.length === 3) {
                        const COLUMN_VALUE = parseInt(this.getTableValue(row, compares[0], this.returnedColumns[compares[0]]));
                        const COMPARE_VALUE = COLUMN_VALUE ? COLUMN_VALUE : 0;
                        const OPERATOR = compares[1];
                        const VALUE = parseInt(compares[2]);

                        if (OPERATOR === '>') {
                            if (COMPARE_VALUE <= VALUE)
                                return '';
                        } else if (OPERATOR === '<') {
                            if (COMPARE_VALUE >= VALUE)
                                return '';
                        } else if (OPERATOR === '==') {
                            if (COMPARE_VALUE !== VALUE)
                                return '';
                        }
                    }
                }

                result.push({
                    text: attributes.text || '',
                    link: attributes.link || '',
                    class: attributes.class || ''
                });

                lastIndex = match.index + match[0].length;
            }

            // Add remaining text after the last segment
            if (lastIndex < text.length) {
                result.push({
                    text: text.slice(lastIndex).trim(),
                    link: '',
                    class: ''
                });
            }
            return result;
        }
    },
}
</script>

<style scoped>
.light-table-bg-1 tr:nth-child(odd) {
    background-color: #e0e0e0;
}

.light-table-bg-2 tr:nth-child(even) {
    background-color: #ffffff;
}

.dark-table-bg-1 tr:nth-child(odd) {
    background-color: #1A2530;
}

.dark-table-bg-2 tr:nth-child(even) {
    background-color: #0c214e;
}

.styled-table tr:hover {
    background-color: rgba(93, 125, 206, 0.25);
}

.formatted-text {
    white-space: pre-line;
}

th:nth-child(1), td:nth-child(1) {
    min-width: 165px;
}

td:first-child {
    position: sticky;
    left: 0;
    z-index: 11;
    font-weight: bold;
}

th:first-child {
    position: sticky;
    left: 0;
    z-index: 12;
    font-weight: bold;
}

td:nth-child(2) {
    position: sticky;
    left: 165px;
    z-index: 11;
    font-weight: bold;
}

th:nth-child(2) {
    position: sticky;
    left: 165px;
    z-index: 12;
    font-weight: bold;
}

.table-container {
    border-top: 1px solid #D1D5DB;
    border-bottom: 1px solid #D1D5DB;
    padding: 0;
    margin: 0;
}
</style>
