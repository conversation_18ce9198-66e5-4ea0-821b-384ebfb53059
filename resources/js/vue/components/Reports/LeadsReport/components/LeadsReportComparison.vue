<template>
    <p v-if="type !== INFO_STRING" class="text-sm text-center font-medium gap-x-2 items-center">
        {{ prefix }} {{ formatAmount }}
        <br>
        <span v-if="compare" class="inline-flex text-center items-center gap-1 text-sm" :style="{ fontSize: '10px' }" :class="[increased ? (darkMode ? 'text-emerald-400' : 'text-emerald-600') : (darkMode ? 'text-rose-400' : 'text-rose-600')]">
            <span :class="[equal ? 'text-gray-500' : increased ? (darkMode ? 'text-emerald-400' : 'text-emerald-600') : (darkMode ? 'text-rose-400' : 'text-rose-600')]">
                {{ prefix }} {{ formatCompare }}
            </span>
            <span v-if="checkDiff" class="items-center text-center inline-flex z-1">
                [
                <svg class="fill-current" :class="[increased ? '' : 'transform rotate-180']" width="12" height="8" viewBox="0 0 12 8" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6.5 0L12.9952 11.25H0.00480938L6.5 0Z"/></svg>
                {{ getComparisonPercent }}%
                ]
            </span>
        </span>
    </p>
    <div v-else class="text-sm text-center font-medium gap-x-2 items-center">
        <span v-if="compare" class="inline-flex text-center items-center gap-1 text-sm" :style="{ fontSize: '10px' }" :class="[increased ? (darkMode ? 'text-emerald-400' : 'text-emerald-600') : (darkMode ? 'text-rose-400' : 'text-rose-600')]">
            <span v-for="(line, lineIndex) in parseLineInfoStringObject(amount)" :key="lineIndex" :class="columnData['class']">
                <span v-for="(segment, segIndex) in parseSegmentInfoStringObject(line)" :key="segIndex">
                  <template v-if="segment['link']">
                    <a :href="segment['link']" :class="segment['class']" target="_blank" rel="noopener noreferrer">{{ segment.text }}</a>
                  </template>
                  <template v-else>
                    <span :class="segment.class">{{ segment.text }}</span>
                  </template>
                </span>
                <br>
            </span>
        </span>
        <br>
        <span v-if="compare" class="inline-flex text-center items-center gap-1 text-sm" :style="{ fontSize: '10px' }" :class="[increased ? (darkMode ? 'text-emerald-400' : 'text-emerald-600') : (darkMode ? 'text-rose-400' : 'text-rose-600')]">
            <div :class="columnData['class']">
                <span v-for="(line, lineIndex) in parseLineInfoStringObject(comparedAmount)" :key="lineIndex">
                    <span v-for="(segment, segIndex) in parseSegmentInfoStringObject(line)" :key="segIndex">
                      <template v-if="segment['link']">
                        <a :href="segment['link']" :class="segment['class']" target="_blank" rel="noopener noreferrer">{{ segment.text }}</a>
                      </template>
                      <template v-else>
                        <span :class="segment.class">{{ segment.text }}</span>
                      </template>
                    </span>
                    <br>
                </span>
            </div>
            <span v-if="checkDiff" class="items-center text-center inline-flex z-1">
                [
                <svg class="fill-current" :class="[increased ? '' : 'transform rotate-180']" width="12" height="8" viewBox="0 0 12 8" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6.5 0L12.9952 11.25H0.00480938L6.5 0Z"/></svg>
                {{ getComparisonPercent }}%
                ]
            </span>
        </span>
    </div>
</template>

<script>
    /** Export constants into module scope so defineProps can access them */
    export const MONETARY = 0;
    export const BASIC = 1;
    export const TIME_SECONDS = 2;
    export const PERCENT = 3;
    export const STRING = 4;
    export const INFO_STRING = 5;
</script>

<script setup>
    import {ref, computed} from 'vue';

    const props = defineProps({
        darkMode: {
            type: Boolean,
            default: false
        },
        amount: {
            type: [Number, String],
            default: 0
        },
        comparedAmount: {
            type: [Number, String],
            default: null
        },
        period: {
            type: String,
            default: ''
        },
        compare: {
            type: Boolean,
            default: true
        },
        prefix: {
            type: String,
            default: '',
        },
        total: {
            type: Boolean,
            default: false
        },
        type: {
            type: Number,
            default: MONETARY,
            validator(value) {
                return [MONETARY, BASIC, TIME_SECONDS, PERCENT, STRING, INFO_STRING].includes(value);
            }
        },
        columnData: {
            type: Object,
            default: {}
        }
    });

    const comparisonValue =  ref(0);

    const increased = computed(() => {
        return parseFloat(props.amount) > parseFloat(props.comparedAmount);
    })

    const equal = computed(() => {
        if (props.type === STRING)
            return true;
        return parseFloat(props.amount) === parseFloat(props.comparedAmount);
    })

    const getComparisonValue = computed(() => {
        if (props.comparedAmount === null)
            return props.amount;
        if (props.amount === null)
            return props.comparedAmount;

        let value = props.amount - props.comparedAmount;

        if(props.type === MONETARY) {
            return '$' + value.toLocaleString(undefined, { minimumFractionDigits: 0 }).replace('-', '');
        }
        else if(props.type === BASIC) {
            return value.toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 2 }).replace('-', '');
        }
        else if(props.type === TIME_SECONDS) {
            const hours = Math.floor(value / 3600);
            const minutes = Math.floor((value - (hours * 3600)) / 60);
            const seconds = Math.floor((value - (hours * 3600) - (minutes * 60)));

            return `${hours} hours ${minutes} mins ${seconds} secs`;
        } else if (props.type === PERCENT) {
            return (value * 100).toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 2 }) + '%';
        }
        else {
            return value
        }
    });

    const getComparisonPercent = computed(() => {
        if (props.amount === null || props.comparedAmount === null)
            return null;

        return Math.abs(((props.amount - props.comparedAmount) / props.comparedAmount * 100)).toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 2 });
    });

    const checkDiff = computed(() => {
        return Boolean(parseFloat(props.amount) - parseFloat(props.comparedAmount));
    });

    const getComparisonPercentage = computed(() => {
        if(props.amount + props.comparedAmount === 0) {
            return 0;
        }
        else {
            return Math.round(comparisonValue.value =  (props.amount - props.comparedAmount) / ((props.amount + props.comparedAmount) / 2) * 100)
        }
    });

    const parseLineInfoStringObject = (text) => {
        if (!text)
            return '';

        return text.split("\n");
    }

    const parseSegmentInfoStringObject = (text) => {
        if (!text)
            return '';
        // Turns: $[text[Test Name],link[/companies/6811],class[text-blue]]$ $[text[: Campaign],class[text-green]]$ Name... into:
        // [
        //     { text: 'Test Name', link: '/companies/6811', class: 'text-blue'},
        //     { text: ': Campaign', link: '', class: 'text-green'},
        //     { text: ' Name...', link: '', class: ''},
        // ];

        const result = [];

        // Define regex patterns
        const segmentRegex = /\$\[(.*?)\]\$/g; // Regex to match each segment between $[ ... ] and $
        const attributeRegex = /(text|link|class|if)\[([^\]]*)\]/g; // Regex to match attributes text, link, and class

        let match;
        let lastIndex = 0;

        // Find all segments
        while ((match = segmentRegex.exec(text)) !== null) {
            const segment = match[1]; // Extract the content inside $[ ... ]$

            const attributes = {};
            let attrMatch;

            while ((attrMatch = attributeRegex.exec(segment)) !== null) {
                const [fullMatch, key, value] = attrMatch;
                attributes[key] = value;
            }

            result.push({
                text: attributes.text || '',
                link: attributes.link || '',
                class: attributes.class || ''
            });

            lastIndex = match.index + match[0].length;
        }

        // Add remaining text after the last segment
        if (lastIndex < text.length) {
            result.push({
                text: text.slice(lastIndex).trim(),
                link: '',
                class: ''
            });
        }
        return result;
    }

    const formatValue = (value) => {
        if (props.total && (value === null || value === undefined || value === '' || isNaN(value))) {
            return '';
        }
        if (value === null) {
            return 0;
        }
        if(props.type === MONETARY) {
            return '$' + parseFloat(value).toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 2 })
        }
        else if (props.type === BASIC) {
            return parseFloat(value).toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 2 })
        }
        else if(props.type === TIME_SECONDS) {
            const hours = Math.floor(value / 3600);
            const minutes = Math.floor((value - (hours * 3600)) / 60);
            const seconds = Math.floor((value - (hours * 3600) - (minutes * 60)));

            return `${hours} hours ${minutes} mins ${seconds} secs`;
        }
        else if (props.type === PERCENT) {
            return (parseFloat(value) * 100).toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 2 }) + '%';
        }
        else {
            return value;
        }
    }

    const formatAmount = computed(() => {
        return formatValue(props.amount);
    });

    const formatCompare = computed(() => {
        return formatValue(props.comparedAmount);
    });
</script>
