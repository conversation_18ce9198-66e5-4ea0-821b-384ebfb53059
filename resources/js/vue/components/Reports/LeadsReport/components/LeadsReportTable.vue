<template>
    <div class="mt-5 flex flex-col">
        <div class="my-2 -mx-4 sm:-mx-6 lg:-mx-8">
            <div class="w-full overflow-x-auto overflow-y-auto" :style="{ maxHeight: fullScreen ? '75vh' : '60vh', maxWidth: '100%'}">
                <table class="min-w-full divide-y table-container" :class="[!darkMode ? 'divide-gray-300 light-table-bg-1 light-table-bg-2' : 'divide-dark-175 dark-table-bg-1 dark-table-bg-2']">
                    <thead>
                        <tr class="divide-x" :class="[!darkMode ? 'divide-gray-200 light-table-background-even light-table-background-odd' : 'divide-dark-175']">
                            <th @click="sortTable(groupKey)" scope="col"
                                class="sticky left-0 top-0 whitespace-nowrap z-30 border-b backdrop-blur-2xl py-2 pl-8 pr-3 text-center text-sm font-semibold backdrop-filter"
                                :class="[!darkMode ? 'border-gray-300 bg-light-module text-gray-900 bg-opacity-75' : 'border-dark-border bg-dark-module text-grey-100 bg-opacity-75']">
                                <div class="cursor-pointer group inline-flex" :class="[(sortColumn === groupKey) ? 'text-blue-550' : '']">
                                    {{ formatGroupKey }}
                                    <span v-if="sortColumn === groupKey && sortDirection === 'desc'" class="ml-2 flex-none rounded text-blue-550">
                                        <ChevronDownIcon class="h-5 w-5" aria-hidden="false" />
                                    </span>
                                    <span v-else-if="sortColumn === groupKey && sortDirection === 'asc'" class="ml-2 flex-none rounded text-blue-550">
                                        <ChevronUpIcon class="h-5 w-5" aria-hidden="false" />
                                    </span>
                                    <span v-else class="invisible ml-2 flex-none rounded text-gray-400 group-hover:visible group-focus:visible">
                                        <ChevronDownIcon class="h-5 w-5" aria-hidden="true" />
                                    </span>
                                </div>
                            </th>
                            <th v-for="colKey in columnOrder" @click="sortTable(colKey)" scope="col"
                                class="sticky top-0 whitespace-nowrap z-10 border-b bg-opacity-75 backdrop-blur-2xl py-2 pl-8 pr-3 text-sm font-medium backdrop-filter" :class="[!darkMode ? 'border-gray-300 bg-light-module text-gray-900' : 'border-dark-border bg-dark-module text-grey-100']">
                                <div class="cursor-pointer group inline-flex formatted-text text-center" :class="[(sortColumn === colKey) ? 'text-blue-550' : '']">
                                    {{ columns[colKey]['name'] }}
                                    <span v-if="sortColumn === colKey && sortDirection === 'desc'" class="ml-2 flex-none rounded text-blue-550">
                                        <ChevronDownIcon class="h-5 w-5" aria-hidden="false" />
                                    </span>
                                    <span v-else-if="sortColumn === colKey && sortDirection === 'asc'" class="ml-2 flex-none rounded text-blue-550">
                                        <ChevronUpIcon class="h-5 w-5" aria-hidden="false" />
                                    </span>
                                    <span v-else class="invisible ml-2 flex-none rounded text-gray-400 group-hover:visible group-focus:visible">
                                        <ChevronDownIcon class="h-5 w-5" aria-hidden="true" />
                                    </span>
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="divide-y styled-table" :class="[!darkMode ? 'divide-gray-200' : 'divide-dark-175']">
                    <tr class="divide-x cursor-pointer" :class="[!darkMode ? 'divide-gray-200 light-table-background-even light-table-background-odd' : 'divide-dark-175']" v-for="(row, group, index) in this.sortedData" @click="selectRow(index)">
                        <td class="overflow-x-auto sticky left-0 text-center z-20 whitespace-nowrap  py-2 text-sm backdrop-blur-2xl font-semibold backdrop-filter" :class="[!darkMode ? 'text-black' : 'text-white']" :style="{maxWidth: '300px'}">
                            <p class="ml-1 mr-1">{{ formatGroup(group) }}</p>
                            <p v-if="row['compare_group'] && row['compare_group'] !== group" class="z-20 text-gray-600" :style="{ fontSize: '10px' }">vs {{ row['compare_group'] }}</p>
                        </td>
                        <td v-for="colKey in columnOrder" class="overflow-x-auto whitespace-nowrap left-0 py-2 bg-opacity-50 text-sm text-center font-medium cursor-pointer" :class="[!darkMode ? 'text-gray-600 hover:text-gray-900 bg-light-module' : 'text-white hover:text-gray-300 bg-dark-module']" :style="{maxWidth: '300px'}">
                            <LeadsReportComparison v-if="!!this.compareData" :type="getCompareType(columns[colKey])" :amount="getTableValue(row, colKey, columns[colKey])" :compared-amount="getTableValue(compareData[group], colKey, columns[colKey])" :column-data="columns[colKey]" :dark-mode="darkMode"/>
                            <div v-else-if="columns[colKey].type === 'info_string'" :class="columns[colKey]['class']">
                                <span v-for="(line, lineIndex) in parseLineInfoStringObject(getTableValue(row, colKey, columns[colKey]))" :key="lineIndex">
                                    <span v-for="(segment, segIndex) in parseSegmentInfoStringObject(line, row)" :key="segIndex">
                                      <template v-if="segment['link']">
                                        <a :href="segment['link']" :class="segment['class']" target="_blank" rel="noopener noreferrer">{{ segment.text }}</a>
                                      </template>
                                      <template v-else>
                                        <span :class="segment.class">{{ segment.text }}</span>
                                      </template>
                                    </span>
                                    <br>
                                </span>
                            </div>
                            <div v-else :style="tableValueStyle(row, colKey, columns[colKey])">{{ formatTableValue(getTableValue(row, colKey, columns[colKey]), columns[colKey]) }}</div>
                        </td>
                    </tr>
                    <tr class="divide-x bottom-border" :class="[!darkMode ? 'divide-gray-200 light-table-background-even light-table-background-odd' : 'divide-dark-175']">
                        <td class="sticky left-0 text-center z-20 whitespace-nowrap py-2 text-sm font-bold backdrop-filter" :class="[!darkMode ? 'text-black bg-light-module' : 'text-white bg-dark-module']">
                            Total
                        </td>
                        <td v-for="colKey in columnOrder" class="whitespace-nowrap group left-0 py-2 bg-opacity-50 text-sm text-center font-bold cursor-pointer" :class="[!darkMode ? 'text-black hover:text-gray-900 bg-light-module' : 'text-white hover:text-gray-300 bg-dark-module']">
                           <div class="inline-flex w-full justify-between">
                               <span @click="moveColumn(colKey, -1)" class="invisible flex-none rounded text-gray-400 group-hover:visible group-focus:visible">
                                        <ChevronLeftIcon class="h-5 w-5" aria-hidden="true" />
                               </span>
                               <div>
                                   <LeadsReportComparison v-if="!!this.compareData" :type="getCompareType(columns[colKey])" :total="true" :amount="getTotalValue(colKey, data, columns[colKey])" :compared-amount="getTotalValue(colKey, compareData, columns[colKey], data)" :prefix="getPrefix(columns[colKey])" :dark-mode="darkMode"/>
                                   <div v-else>{{ getPrefix(columns[colKey]) }} {{ formatTableValue(getTotalValue(colKey, data, columns[colKey]), columns[colKey], true) }}</div>
                               </div>
                               <span @click="moveColumn(colKey, 1)" class="invisible flex-none rounded text-gray-400 group-hover:visible group-focus:visible">
                                        <ChevronRightIcon class="h-5 w-5" aria-hidden="true" />
                               </span>
                           </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</template>

<script>
import {ChevronDownIcon, ChevronUpIcon, ChevronLeftIcon, ChevronRightIcon} from "@heroicons/vue/solid";
import LeadsReportComparison from "./LeadsReportComparison.vue";
import { downloadCsvString } from "../../../../../composables/exportToCsv.js";

export default {
    name: "LeadsReportTable",
    components: {ChevronDownIcon, ChevronUpIcon, ChevronLeftIcon, ChevronRightIcon, LeadsReportComparison},
    data: function() {
        return {
            selectedRow: null,
            sortedData: null,
            sortColumn: null,
            sortDirection: null,
            columnOrder: null,
        }
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        groupKey: {
            type: String,
            default: ''
        },
        columns: {
            type: Object,
            default: null
        },
        allColumnOrder: {
            type: Array,
            default: null
        },
        returnedColumns: {
            type: Object,
            default: null
        },
        data: {
            type: Object,
            default: null
        },
        compareData: {
            type: Object,
            default: null
        },
        reportsPage: {
            type: Boolean,
            default: false
        },
        fullScreen: {
            type: Boolean,
            default: false
        },
        baseStart: {
            type: String,
            default: '',
        },
        baseEnd: {
            type: String,
            default: '',
        },
        filterInputs: {
            type: Object,
            default: {}
        },
    },
    created() {
        this.columnOrder = this.allColumnOrder.filter(col => Object.keys(this.columns).includes(String(col)));
        this.parseUrlParams();
        this.sortedData = this.data;
        if (!this.sortColumn || (this.sortColumn === this.groupKey)) {
            this.sortTable(this.sortColumn, true);
        }
    },
    computed: {
        formatGroupKey() {
            return this.groupKey.replace('_', ' ').split(' ').map(word =>
                    word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
                ).join(' ');
        },
    },
    methods: {
        parseUrlParams() {
            const params = new URLSearchParams(window.location.search);
            const paramsObject = {};
            params.forEach((value, key) => {
                paramsObject[key] = value;
            });

            this.basePeriod = [];
            if ('sd' in paramsObject) {
                this.sortDirection = paramsObject['sd']
            } else {
                this.sortDirection = 'desc';
            }
            if ('sc' in paramsObject) {
                this.sortColumn = paramsObject['sc']
            } else {
                this.sortColumn = this.groupKey;
            }
        },
        sortTable(sortColumn, initial = false) {
            if (sortColumn !== this.sortColumn) {
                this.sortColumn = sortColumn;
                this.sortDirection = 'desc';
            } else if (sortColumn === this.sortColumn && this.sortDirection === 'desc' && !initial) {
                this.sortColumn = sortColumn;
                this.sortDirection = 'asc';
            } else {
                this.sortColumn = this.groupKey;
                this.sortDirection = 'desc';
            }

            if (this.sortColumn === this.groupKey) {
                // Sorting by grouping
                if (this.sortColumn === 'Day') {
                    if (this.sortDirection === 'desc') {
                        this.sortedData = Object.fromEntries(Object.entries(this.data).sort(([dateA], [dateB]) => new Date(dateB) - new Date(dateA)));
                    } else {
                        this.sortedData = Object.fromEntries(Object.entries(this.data).sort(([dateA], [dateB]) => new Date(dateA) - new Date(dateB)));
                    }
                } else if (this.sortColumn === 'Week') {
                    if (this.sortDirection === 'desc') {
                        this.sortedData = Object.fromEntries(Object.entries(this.data).sort(([dateA], [dateB]) => new Date(dateB.split(" to ")[0]) - new Date(dateA.split(" to ")[0])));
                    } else {
                        this.sortedData = Object.fromEntries(Object.entries(this.data).sort(([dateA], [dateB]) => new Date(dateA.split(" to ")[0]) - new Date(dateB.split(" to ")[0])));
                    }
                } else if (this.sortColumn === 'Month') {
                    if (this.sortDirection === 'desc') {
                        this.sortedData = Object.fromEntries(Object.entries(this.data).sort(([dateA], [dateB]) => new Date(dateB) - new Date(dateA)));
                    } else {
                        this.sortedData = Object.fromEntries(Object.entries(this.data).sort(([dateA], [dateB]) => new Date(dateA) - new Date(dateB)));
                    }
                } else {
                    if (this.sortDirection === 'desc') {
                        this.sortedData = Object.fromEntries(Object.entries(this.data).sort());
                    } else {
                        this.sortedData = Object.fromEntries(Object.entries(this.data).sort().reverse());
                    }
                }
            } else {
                // Sorting by other column value
                if (this.columns[this.sortColumn].type === 'string') {
                    if (this.sortDirection === 'desc') {
                        this.sortedData = Object.fromEntries(Object.entries(this.data).sort(([, valueA], [, valueB]) => valueA[this.sortColumn]?.localeCompare(valueB[this.sortColumn])));
                    } else {
                        this.sortedData = Object.fromEntries(Object.entries(this.data).sort(([, valueA], [, valueB]) => valueB[this.sortColumn]?.localeCompare(valueA[this.sortColumn])));
                    }
                } else {
                    if (this.sortDirection === 'desc') {
                        this.sortedData = Object.fromEntries(Object.entries(this.data).sort(([, valueA], [, valueB]) => parseFloat(valueB[this.sortColumn] ? valueB[this.sortColumn] : 0) - parseFloat(valueA[this.sortColumn] ? valueA[this.sortColumn] : 0)));
                    } else {
                        this.sortedData = Object.fromEntries(Object.entries(this.data).sort(([, valueA], [, valueB]) => parseFloat(valueA[this.sortColumn] ? valueA[this.sortColumn] : 0) - parseFloat(valueB[this.sortColumn] ? valueB[this.sortColumn] : 0)));
                    }
                }
            }
        },
        updateUrlParam(param, value) {
            const currentUrl = new URL(window.location.href);

            if (currentUrl.searchParams.has(param)) {
                currentUrl.searchParams.set(param, value);
            } else {
                currentUrl.searchParams.append(param, value);
            }

            window.history.replaceState({}, '', currentUrl.toString()); // To not add back set
        },
        parseLineInfoStringObject(text) {
            if (!text)
                return '';

            return text.split("\n");
        },
        parseSegmentInfoStringObject(text, row) {
            if (!text)
                return '';
            // Turns: $[text[Test Name],link[/companies/6811],class[text-blue]]$ $[text[: Campaign],class[text-green]]$ Name... into:
            // [
            //     { text: 'Test Name', link: '/companies/6811', class: 'text-blue'},
            //     { text: ': Campaign', link: '', class: 'text-green'},
            //     { text: ' Name...', link: '', class: ''},
            // ];

            const result = [];

            // Define regex patterns
            const segmentRegex = /\$\[(.*?)\]\$/g; // Regex to match each segment between $[ ... ] and $
            const attributeRegex = /(text|link|class|if)\[([^\]]*)\]/g; // Regex to match attributes text, link, and class

            let match;
            let lastIndex = 0;

            // Find all segments
            while ((match = segmentRegex.exec(text)) !== null) {
                const segment = match[1]; // Extract the content inside $[ ... ]$

                const attributes = {};
                let attrMatch;

                while ((attrMatch = attributeRegex.exec(segment)) !== null) {
                    const [fullMatch, key, value] = attrMatch;
                    attributes[key] = value;
                }

                if (attributes.if) {
                    const compares = attributes.if.split(',');
                    if (compares.length === 3) {
                        const COLUMN_VALUE = parseInt(this.getTableValue(row, compares[0], this.returnedColumns[compares[0]]));
                        const COMPARE_VALUE = COLUMN_VALUE ? COLUMN_VALUE : 0;
                        const OPERATOR = compares[1];
                        const VALUE = parseInt(compares[2]);

                        if (OPERATOR === '>') {
                            if (COMPARE_VALUE <= VALUE)
                                return '';
                        } else if (OPERATOR === '<') {
                            if (COMPARE_VALUE >= VALUE)
                                return '';
                        } else if (OPERATOR === '==') {
                            if (COMPARE_VALUE !== VALUE)
                                return '';
                        }
                    }
                }

                result.push({
                    text: attributes.text || '',
                    link: attributes.link || '',
                    class: attributes.class || ''
                });

                lastIndex = match.index + match[0].length;
            }

            // Add remaining text after the last segment
            if (lastIndex < text.length) {
                result.push({
                    text: text.slice(lastIndex).trim(),
                    link: '',
                    class: ''
                });
            }
            return result;
        },
        getTableValue(row, colKey, colData) {
            if (!row) {
                return 0;
            }
            // Column data included in sql query here
            if (!colData['calculated']) {
                return colKey in row ? row[colKey] : 0;
            }

            // Calculated column value
            const FIRST_COLUMN = colData['calculated']['first_column'];
            const OPERATOR = colData['calculated']['operator'];
            const SECOND_COLUMN = colData['calculated']['second_column'];

            // If calculated value depends on other calculated values
            const FIRST_COLUMN_VALUE = this.calculatedColumnValue(FIRST_COLUMN, row, colData);
            const SECOND_COLUMN_VALUE = this.calculatedColumnValue(SECOND_COLUMN, row, colData, 'second_column');

            // Apply calculation
            if (OPERATOR === '+') {
                row[colKey] = FIRST_COLUMN_VALUE + SECOND_COLUMN_VALUE;
            } else if (OPERATOR === '-') {
                row[colKey] = FIRST_COLUMN_VALUE - SECOND_COLUMN_VALUE;
            } else if (OPERATOR === '*') {
                row[colKey] = FIRST_COLUMN_VALUE * SECOND_COLUMN_VALUE;
            } else if (OPERATOR === '/') {
                if (SECOND_COLUMN_VALUE !== 0) {
                    row[colKey] = FIRST_COLUMN_VALUE / SECOND_COLUMN_VALUE;
                } else {
                    row[colKey] = 0;
                }
            } else if (OPERATOR === 'PERCENT_DIFF') {
                row[colKey] = ((FIRST_COLUMN_VALUE - SECOND_COLUMN_VALUE) / FIRST_COLUMN_VALUE) * -1;
            }
            return row[colKey];
        },
        calculatedColumnValue(firstColumn, row, colData, column = 'first_column') {
            if (typeof firstColumn === 'string' && firstColumn.startsWith('CONSTANT_INT:')) {
                return parseInt(firstColumn.replace(/\D/g, ''), 10);
            }
            return (firstColumn in row) ? row[firstColumn] :
                this.getTableValue(row, colData['calculated'][column], this.returnedColumns[colData['calculated'][column]]);
        },
        formatTableValue(value, colData, total = false) {
            if (colData['type'] === 'dollar') {
                const formatter = new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD'
                });
                return formatter.format(value);
            } else if (colData['type'] === 'decimal') {
                return value.toFixed(2);
            } else if (colData['type'] === 'percent') {
                return ((value*100).toFixed(2)) + '%';
            } else if (total) {
                return value ? value.toLocaleString() : '';
            }
            return value ? value.toLocaleString() : 0;
        },
        tableValueStyle(row, colKey, colData) {
            if (colData['calculated'] && colData['calculated']['operator'] === 'PERCENT_DIFF') {
                const FIRST_COLUMN_VALUE = this.calculatedColumnValue(colData['calculated']['first_column'], row, colData);
                const SECOND_COLUMN_VALUE = this.calculatedColumnValue(colData['calculated']['second_column'], row, colData);
                if (FIRST_COLUMN_VALUE < SECOND_COLUMN_VALUE) {
                    return {
                        color: 'green',
                    };
                }
                if (FIRST_COLUMN_VALUE > SECOND_COLUMN_VALUE) {
                    return {
                        color: 'red',
                    };
                }
            }
            return {};
        },
        formatGroup(group) {
            const pattern = /hide|\[.*?\]/g;
            return group.replace(pattern, '').trim();
        },
        getTotalValue(colKey, data, colData, keyFilter = null) {
            if (colData['total'] === 'avg') {
                return this.avgColumn(colKey, data, keyFilter);
            } else if (colData['total'] === 'none') {
                return ' ';
            }
            return this.sumColumn(colKey, data, keyFilter);
        },
        sumColumn(colKey, data, keyFilter = null) {
            let sum = 0;
            const filterGroups = keyFilter ? Object.keys(keyFilter) : [];

            for (const group in data) {
                // Key filter used to only sum compare totals that have group key present in the report data
                if (keyFilter === null || filterGroups.indexOf(group) > -1) {
                    const groupData = group in data ? data[group] : [];
                    const value = colKey in groupData ? groupData[colKey] : 0;
                    sum += parseFloat(value ? value : 0);
                }
            }
            return sum;
        },
        avgColumn(colKey, data, keyFilter = null) {
            let sum = 0;
            let dividend = 0;
            const filterGroups = keyFilter ? Object.keys(keyFilter) : [];

            for (const group in data) {
                // Key filter used to only sum compare totals that have group key present in the report data
                if (keyFilter === null || filterGroups.indexOf(group) > -1) {
                    const groupData = group in data ? data[group] : [];
                    const value = colKey in groupData ? groupData[colKey] : 0;
                    sum += parseFloat(value ? value : 0);
                    dividend++;
                }
            }
            if (dividend > 0) {
                return Math.round((sum / dividend)*100) / 100;
            }
            return 0;
        },
        getPrefix(colData) {
            if (colData['total'] === 'avg') {
                return 'Avg: '
            }
            return '';
        },
        getCompareType(colData) {
            if (colData['type'] === 'dollar') {
                return 0;
            } else if (colData['type'] === 'percent') {
                return 3;
            } else if (colData['type'] === 'string') {
                return 4;
            } else if (colData['type'] === 'info_string') {
                return 5;
            }

            return 1;
        },
        selectRow(index) {
            // Toggle the selection
            this.selectedRow = this.selectedRow === index ? null : index;
        },
        exportToCsv() {
            const fileName = 'leads-report_' +this.groupKey.toLowerCase() + '_' + this.baseStart + '_to_' + this.baseEnd;

            const colNames = [this.groupKey];
            if (!!this.compareData) {
                colNames.push('Compared ' + this.groupKey);
            }

            for (const colKey in this.columnOrder) {
                const col = this.columnOrder[colKey];
                colNames.push(this.columns[col].name);
                if (!!this.compareData) {
                    colNames.push('Compared ' + this.columns[col].name);
                }
            }
            colNames.push('Filters');

            let rows = [];
            let firstRow = true;
            for (const group in this.sortedData) {
                let row = [group];
                if (!!this.compareData) {
                    row.push(this.sortedData[group].compare_group);
                }
                for (const colKey in this.columnOrder) {
                    const col = this.columnOrder[colKey];
                    const value = this.getTableValue(this.sortedData[group], col, this.columns[col]);
                    const formattedValue = value ? value.toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 2 }) : 0;
                    row.push(formattedValue);
                    if (!!this.compareData) {
                        const value = this.getTableValue(this.compareData[group], col, this.columns[col]);
                        const formattedValue = value ? value.toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 2 }) : 0;
                        row.push(formattedValue);
                    }
                }
                if (firstRow) {
                    row.push(JSON.stringify(this.filterInputs).replace(new RegExp('"', 'g'), ''));
                    firstRow = false;
                } else {
                    row.push('');
                }
                rows.push(row);
            }
            downloadCsvString(colNames, rows, fileName);
        },
        moveColumn(colKey, distance) {
            const oldIndex = this.columnOrder.indexOf(colKey);
            const newIndex = oldIndex + distance;

            if (newIndex < 0 || newIndex >= this.columnOrder.length)
                return;

            const element = this.columnOrder.splice(oldIndex, 1)[0];

            // Insert the element at the next index
            this.columnOrder.splice(newIndex, 0, element);
        }
    },
    watch: {
        sortDirection(newValue, oldValue) {
            if (this.reportsPage)
                this.updateUrlParam('sd', newValue);
        },
        sortColumn(newValue, oldValue) {
            if (this.reportsPage)
                this.updateUrlParam('sc', newValue);
        },
        groupKey: {
            immediate: true,
            handler(newValue, oldValue) {
                this.sortTable(newValue);
            }
        }
    }
}
</script>

<style scoped>
.light-table-bg-1 tr:nth-child(odd) {
    background-color: #e0e0e0;
}

.light-table-bg-2 tr:nth-child(even) {
    background-color: #ffffff;
}

.dark-table-bg-1 tr:nth-child(odd) {
    background-color: #1A2530;
}

.dark-table-bg-2 tr:nth-child(even) {
    background-color: #0c214e;
}

.styled-table tr:hover {
    background-color: rgba(93, 125, 206, 0.25);
}

.formatted-text {
    white-space: pre-line;
}

.table-container {
    border-top: 1px solid #D1D5DB;
    border-bottom: 1px solid #D1D5DB;
    padding: 0;
    margin: 0;
}
</style>
