<template>
    <div class="flex flex-wrap justify-between items-end w-full">
        <div class="mt-6 inline-flex gap-3 mr-10">
            <div class="z-5" style="min-width: 150px">
                <p class="mb-1 font-medium text-sm">Base Period</p>
                <Datepicker
                    v-model="basePeriod"
                    :enable-time-picker="false"
                    :auto-apply="true"
                    :range="{partialRange: false, showLastInRange: false}"
                    :model-type="datePickerFormat"
                    :clearable="false"
                    :year-range="yearRange"
                    @range-end="setBasePeriod('')"
                    :dark="darkMode"
                    :preset-dates="datepickerPresetBasePeriods">
                    <template v-for="period in ['week', 'month', 'three_months', 'year']" #[getPeriodSlotName(period)]="{label, value, presetDate}">
                        <div
                            role="button"
                            class="whitespace-nowrap p-2"
                            :class="[darkMode ? 'hover:bg-gray-100 hover:text-black' : 'hover:bg-gray-100']"
                            @click="setBasePeriod(period); presetDate(value)">
                            {{ label }}
                        </div>
                    </template>
                </Datepicker>
            </div>
            <div class="z-5" style="min-width: 150px">
                <p class="mb-1 font-medium text-sm">Comparison Period</p>
                <Datepicker
                    v-model="comparisonPeriod"
                    :key="basePeriod"
                    :enable-time-picker="false"
                    :auto-apply="true"
                    :range="{partialRange: false, autoRange: customBasePeriodDayCount, showLastInRange: false}"
                    :model-type="datepickerFormat"
                    :clearable="true"
                    :year-range="yearRange"
                    :dark="darkMode"
                    :preset-dates="datepickerPresetComparisonPeriods">
                    <template #comparison-period="{label, value, presetDate}">
                        <div
                            role="button"
                            class="whitespace-nowrap p-2"
                            :class="[darkMode ? 'hover:bg-gray-100 hover:text-black' : 'hover:bg-gray-100']"
                            @click="presetDate(value)">
                            {{ label }}
                        </div>
                    </template>
                </Datepicker>
            </div>
            <div class="z-5">
                <p class="mb-1 font-medium text-sm">Grouping</p>
                <Dropdown style="min-width: 110px" :dark-mode="darkMode" :options="groups" v-model="group"/>
            </div>
            <div class="flex items-end z-40">
                <Filterable
                    class="z-40"
                    :dark-mode="darkMode"
                    :filters="filters"
                    :custom-categories="customCategories"
                    v-model="filterInputs"
                    @update:defaults="updateFilterDefaults"
                    @update:filterOptions="getFilterOptionUpdates"
                    @update:customValue="handleCustomUpdate"
                    @custom:delete-option="openDeletePresetModal"
                />
            </div>
            <div class="flex items-end">
                <div class="relative">
                    <div v-if="columnsPopup" @click="toggleColumnsPopup" class="fixed inset-0 z-0"></div>
                    <div class="relative z-4">
                        <CustomButton @click="toggleColumnsPopup" :dark-mode="darkMode" color="slate-inverse">
                            <svg class="fill-current mr-2" width="17" height="16" viewBox="0 0 17 16" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                      d="M2.66667 2C2.55481 2 2.40141 2.05193 2.25638 2.22597C2.10691 2.40533 2 2.6818 2 3V13C2 13.3182 2.10691 13.5947 2.25638 13.774C2.40141 13.9481 2.55481 14 2.66667 14H4.33333C4.44519 14 4.59859 13.9481 4.74362 13.774C4.89309 13.5947 5 13.3182 5 13V3C5 2.6818 4.89309 2.40533 4.74362 2.22597C4.59859 2.05193 4.44519 2 4.33333 2H2.66667ZM6 0.654839C5.54679 0.248166 4.96618 0 4.33333 0H2.66667C1.89447 0 1.20002 0.369497 0.719934 0.945602C0.244279 1.51639 0 2.25734 0 3V13C0 13.7427 0.24428 14.4836 0.719934 15.0544C1.20002 15.6305 1.89446 16 2.66667 16H4.33333C4.96618 16 5.54679 15.7518 6 15.3452C6.45321 15.7518 7.03382 16 7.66667 16H9.33333C9.96618 16 10.5468 15.7518 11 15.3452C11.4532 15.7518 12.0338 16 12.6667 16H14.3333C15.1055 16 15.8 15.6305 16.2801 15.0544C16.7557 14.4836 17 13.7427 17 13V3C17 2.25734 16.7557 1.51639 16.2801 0.945602C15.8 0.369497 15.1055 0 14.3333 0H12.6667C12.0338 0 11.4532 0.248166 11 0.654839C10.5468 0.248166 9.96618 0 9.33333 0H7.66667C7.03382 0 6.45321 0.248166 6 0.654839ZM10 3C10 2.6818 9.89309 2.40533 9.74362 2.22597C9.59859 2.05193 9.44519 2 9.33333 2H7.66667C7.55481 2 7.40141 2.05193 7.25638 2.22597C7.10691 2.40533 7 2.6818 7 3V13C7 13.3182 7.10691 13.5947 7.25638 13.774C7.40141 13.9481 7.55481 14 7.66667 14H9.33333C9.44519 14 9.59859 13.9481 9.74362 13.774C9.89309 13.5947 10 13.3182 10 13V3ZM12 13C12 13.3182 12.1069 13.5947 12.2564 13.774C12.4014 13.9481 12.5548 14 12.6667 14H14.3333C14.4452 14 14.5986 13.9481 14.7436 13.774C14.8931 13.5947 15 13.3182 15 13V3C15 2.6818 14.8931 2.40533 14.7436 2.22597C14.5986 2.05193 14.4452 2 14.3333 2H12.6667C12.5548 2 12.4014 2.05193 12.2564 2.22597C12.1069 2.40533 12 2.6818 12 3V13Z"/>
                            </svg>
                            Columns
                        </CustomButton>
                        <div v-if="columnsPopup"
                             class="z-40 absolute rounded-lg border w-64 grid p-3 top-10 left-0 shadow-module text-sm font-medium max-h-72 overflow-y-scroll"
                             :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
                            <div class="mb-1">
                                <CustomButton color="slate" @click="toggleAllColumnsShown(false)" :dark-mode="darkMode"
                                              v-if="allColumnsSelected">
                                    Deselect All
                                </CustomButton>
                                <CustomButton @click="toggleAllColumnsShown(true)" :dark-mode="darkMode" v-else>
                                    Select All
                                </CustomButton>
                            </div>
                            <div v-for="key in columnOrder">
                                <div v-if="checkColDisabled(key)" class="inline-flex items-center mb-2">
                                    <input type="checkbox" @input="updateColumnShown(key)" :checked="selectedColumns[key]"
                                           class="mr-2 checked:bg-primary-500 rounded cursor-pointer focus:outline-none focus:ring-0">
                                    <p class="cursor-pointer" @click="updateColumnShown(key)">{{ columns[key].name }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex items-end z-50">
                <CustomButton type="submit" :dark-mode="darkMode"
                               @click="generateReport()">
                Generate
                </CustomButton>
            </div>
        </div>
        <div class="inline-flex items-center gap-3">
            <div class="items-end z-5 mt-3">
                <CustomButton
                    color="primary-outline"
                    @click="exportToCsv"
                    style="min-width: 90px"
                    :disabled="loading"
                    :dark-mode="darkMode">
                    Export to CSV
                </CustomButton>
            </div>
            <div class="items-end z-5 mt-3">
                <CustomButton
                    color="slate-inverse"
                    @click="openSavePresetModal"
                    :dark-mode="darkMode">
                    Save Config
                </CustomButton>
            </div>
            <div class="items-end z-5 mt-3">
                <CustomButton :dark-mode="darkMode" color="slate-inverse" type="reset"
                              @click="resetFilters()">
                    Reset Filters
                </CustomButton>
            </div>
        </div>
    </div>
    <div class="inline-flex align-center gap-3 mt-3">
        <autocomplete
            search-icon
            :dark-mode="darkMode"
            class="w-full max-w-sm"
            v-model="selectedCompany"
            :model-value="Number(selectedCompany)"
            :options="companies"
            :style="{width: '245px'}"
            :loading="companySearchLoading"
            :placeholder="'Search by Company'"
            :create-user-input-option="false"
            @search="searchCompanies($event)"
            @input="handleAutocompleteCompanyInput"
        ></autocomplete>
        <div v-if="showCampaignFilter">
            <MultiSelect
                :dark-mode="darkMode"
                :options="companyCampaigns"
                :style="{width: '175px'}"
                :show-search-box="false"
                text-place-holder="Select Campaign"
                @input="selectedCampaigns = $event"
                :selected-ids="selectedCampaigns"
            />
        </div>
        <div v-if="selectedCompany" class="items-end">
            <CustomButton :dark-mode="darkMode" color="slate-inverse"
                          @click="clearCompany()">
                <svg class="fill-current cursor-pointer" fill="#000000" height="15px" width="15px" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 460.775 460.775" xml:space="preserve"><g stroke-width="0"></g><g stroke-linecap="round" stroke-linejoin="round"></g><g> <path d="M285.08,230.397L456.218,59.27c6.076-6.077,6.076-15.911,0-21.986L423.511,4.565c-2.913-2.911-6.866-4.55-10.992-4.55 c-4.127,0-8.08,1.639-10.993,4.55l-171.138,171.14L59.25,4.565c-2.913-2.911-6.866-4.55-10.993-4.55 c-4.126,0-8.08,1.639-10.992,4.55L4.558,37.284c-6.077,6.075-6.077,15.909,0,21.986l171.138,171.128L4.575,401.505 c-6.074,6.077-6.074,15.911,0,21.986l32.709,32.719c2.911,2.911,6.865,4.55,10.992,4.55c4.127,0,8.08-1.639,10.994-4.55 l171.117-171.12l171.118,171.12c2.913,2.911,6.866,4.55,10.993,4.55c4.128,0,8.081-1.639,10.992-4.55l32.709-32.719 c6.074-6.075,6.074-15.909,0-21.986L285.08,230.397z"></path> </g></svg>
            </CustomButton>
        </div>

    </div>
    <FilterableActivePills
        class="px-8 mb-6 mt-6"
        v-if="filters.length"
        :filters="filters"
        :active-filters="filterInputs"
        :dark-mode="darkMode"
        @reset-filter="clearFilter"
    />
    <Modal
        v-if="showPresetModal"
        @close="closePresetModal"
        @confirm="confirmPresetModal"
        :dark-mode="darkMode"
        :small="true"
        :confirm-text="deletingPreset ? 'Delete' : 'Save'"
    >
        <template v-slot:header>
            {{ deletingPreset ? 'Delete' : 'Save' }} Filter Preset
        </template>
        <template v-slot:content>
            <LoadingSpinner
                v-if="loading || saving"
            />
            <div v-else>
                <div v-if="!deletingPreset">
                    <div @keyup.enter="confirmPresetModal">
                        <CustomInput
                            label="Filter Preset Name"
                            :dark-mode="darkMode"
                            v-model="presetName"
                            placeholder="Enter a unique name..."
                        />
                    </div>
                </div>
                <div v-else>
                    <p>Are you sure you wish to delete the preset '{{ deletingPreset }}'?</p>
                </div>
                <div class="my-2 text-center text-red-500" v-if="modalError">
                    {{ modalError }}
                </div>
            </div>
        </template>
    </Modal>
</template>

<script>

import CustomInput from "../../../Shared/components/CustomInput.vue";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import CustomCheckbox from "../../../Shared/SlideWizard/components/CustomCheckbox.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import {DateTime, Duration} from "luxon";
import ToggleSwitch from "../../../Shared/components/ToggleSwitch.vue";
import Filterable from "../../../Shared/components/Filterables/Filterable.vue";
import FilterableActivePills from "../../../Shared/components/Filterables/FilterableActivePills.vue";
import LoadingSpinner from "../../../LeadProcessing/components/LoadingSpinner.vue";
import Modal from "../../../LeadProcessing/components/Modal.vue";
import Autocomplete from "../../../Shared/components/Autocomplete.vue";
import MultiSelect from "../../../Shared/components/MultiSelect.vue";
import SharedApiService from "../../../Shared/services/api.js";
import Datepicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';

export default {
    name: "Inputs",
    components: {
        MultiSelect,
        Datepicker,
        Autocomplete,
        Modal,
        LoadingSpinner,
        FilterableActivePills, Filterable, ToggleSwitch, CustomButton, CustomCheckbox, Dropdown, CustomInput},
    data: function () {
        return {
            timezoneOffset: DateTime.local().toFormat('Z'),
            datePickerFormat: 'yyyy-M-d',
            period: 'week',
            basePeriod: [],
            comparisonPeriod: null,
            basePeriodSelection: 'week',
            datepickerFormat: 'yyyy-M-d',
            groups: [],
            group: 'day',
            filters: [],
            filterInputs: {},
            filterDefaults: {},
            urlFilters: null,
            urlFilterStatus: null,
            columns: {},
            columnOrder: [],
            selectedColumns: [],
            columnsPopup: false,
            presets: [],
            deletingPreset: false,
            showPresetModal: false,
            presetName: "",
            modalError: null,
            loading: false,
            saving: false,
            selectedCompany: '',
            companySearchLoading: false,
            companies: [],
            showCampaignFilter: false,
            companyCampaigns: [],
            selectedCampaigns: [],
            urlCampaigns: null,
            sharedApi: SharedApiService.make(),
        }
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        reportsPage: {
            type: Boolean,
            default: false,
        },
        apiService: null,
    },
    emits: ['locations-set', 'error-message', 'update-data', 'update-loading', 'export-to-csv'],
    created() {
        this.getReportOptions();
        this.parseUrlParams();
        this.generateReport(true);
    },
    computed: {
        customCategories () {
            const options = {}
            this.presets.forEach(preset => Object.assign(options, { [preset.name]: preset.name }));

            return [{
                type: 'custom-preset',
                name: 'User Presets',
                id: 'presets',
                options
            }]
        },
        luxonTimezone() {
            return `UTC${DateTime.local().toFormat('Z')}`;
        },
        now() {
            return DateTime.now().setZone(this.luxonTimezone.value).startOf('day');
        },
        yearRange() {
            return [2012, this.now.year + 1];
        },
        datepickerPresetBasePeriods() {
            return [
                {
                    label: 'This Week',
                    value: (() => {
                        return [
                            this.now.startOf('week').toFormat(this.datepickerFormat),
                            this.now.endOf('week').toFormat(this.datepickerFormat)
                        ];
                    })(),
                    noTz: true,
                    slot: this.getPeriodSlotName('week')
                },
                {
                    label: 'This Month',
                    value: (() => {
                        return [
                            this.now.startOf('month').toFormat(this.datepickerFormat),
                            this.now.endOf('month').toFormat(this.datepickerFormat)
                        ];
                    })(),
                    noTz: true,
                    slot: this.getPeriodSlotName('month')
                },
                {
                    label: 'Last Three Months',
                    value: (() => {
                        return [
                            this.now.startOf('month').minus(Duration.fromObject({months: 2})).toFormat(this.datepickerFormat),
                            this.now.endOf('month').toFormat(this.datepickerFormat)
                        ];
                    })(),
                    noTz: true,
                    slot: this.getPeriodSlotName('three_months')
                },
                {
                    label: 'This Year',
                    value: (() => {
                        return [
                            this.now.startOf('year').toFormat(this.datepickerFormat),
                            this.now.endOf('year').toFormat(this.datepickerFormat)
                        ];
                    })(),
                    noTz: true,
                    slot: this.getPeriodSlotName('year')
                }
            ];
        },
        datepickerPresetComparisonPeriods() {
            if(this.basePeriod.length) {
                const options = [
                    {
                        label: "Same Time Last Year",
                        value: (() => {
                            let startDate = new Date(this.basePeriod[0]);
                            let endDate = new Date(this.basePeriod[1]);

                            startDate.setDate(startDate.getDate() + 1);
                            endDate.setDate(endDate.getDate() + 1);
                            startDate.setFullYear(startDate.getFullYear() - 1);
                            endDate.setFullYear(endDate.getFullYear() - 1);

                            return [
                                startDate.toISOString().split('T')[0],
                                endDate.toISOString().split('T')[0],
                            ];
                        })(),
                        noTz: true,
                        slot: 'comparison-period'
                    }
                ]
                const comparisonPeriods = {
                    week: {
                        label: "Last Week",
                        value: (() => {
                            return [
                                this.now.startOf('week').minus(Duration.fromObject({weeks: 1})).toFormat(this.datepickerFormat),
                                this.now.endOf('week').minus(Duration.fromObject({weeks: 1})).toFormat(this.datepickerFormat)
                            ];
                        })(),
                        noTz: true,
                        slot: 'comparison-period'
                    },
                    month: {
                        label: "Last Month",
                        value: (() => {
                            return [
                                this.now.startOf('month').minus(Duration.fromObject({months: 1})).toFormat(this.datepickerFormat),
                                this.now.endOf('month').minus(Duration.fromObject({months: 1})).toFormat(this.datepickerFormat)
                            ];
                        })(),
                        noTz: true,
                        slot: 'comparison-period'
                    },
                    three_months: {
                        label: "Last Year Same Three Months",
                        value: (() => {
                            return [
                                this.now.startOf('month').minus(Duration.fromObject({months: 2, years: 1})).toFormat(this.datepickerFormat),
                                this.now.endOf('month').minus(Duration.fromObject({years: 1})).toFormat(this.datepickerFormat)
                            ];
                        })(),
                        noTz: true,
                        slot: 'comparison-period'
                    },
                    year: {
                        label: "Last Year",
                        value: (() => {
                            return [
                                this.now.minus(Duration.fromObject({years: 1})).startOf('year').toFormat(this.datepickerFormat),
                                this.now.minus(Duration.fromObject({years: 1})).endOf('year').toFormat(this.datepickerFormat)
                            ];
                        })(),
                        noTz: true,
                        slot: 'comparison-period'
                    }
                };

                if (this.basePeriodSelection in comparisonPeriods) {
                    options.push(comparisonPeriods[this.basePeriodSelection]);
                }
                return options;
            }

            return [];
        },
        customBasePeriodDayCount() {
            return this.getDifferenceInDays(this.basePeriod[0] ?? null, this.basePeriod[1] ?? null);
        },
        filteredColumns() {
            return Object.fromEntries(
                Object.entries(this.columns).filter(([key]) => this.selectedColumns[key])
            );
        },
        allColumnsSelected() {
            for (const column in this.columns) {
                if (!this.selectedColumns[column]) {
                    return false;
                }
            }
            return true;
        }
    },
    methods: {
        getReportOptions() {
            this.apiService.getReportOptions().then(resp => {
                this.initGroups(resp.data.data.groups);
                this.initColumns(resp.data.data.columns, resp.data.data.column_order);
                this.filters = resp.data.data.filters;
                this.initLocations(resp.data.data.leads_locations);
                this.presets = this.sortPresets(resp.data.data.presets ?? []);
            }).catch(e => console.log(e));
        },
        parseUrlParams() {
            const params = new URLSearchParams(window.location.search);
            const paramsObject = {};
            params.forEach((value, key) => {
                paramsObject[key] = value;
            });

            this.basePeriod = [];
            if ('base_start' in paramsObject && 'base_end' in paramsObject) {
                this.basePeriod.push(paramsObject['base_start']);
                this.basePeriod.push(paramsObject['base_end']);
            } else {
                this.setDefaultBasePeriod();
            }
            this.updateUrlParam('base_start', this.basePeriod[0]);
            this.updateUrlParam('base_end', this.basePeriod[1]);

            this.comparisonPeriod = [];
            if ('compare_start' in paramsObject && 'compare_end' in paramsObject) {
                this.comparisonPeriod.push(paramsObject['compare_start']);
                this.comparisonPeriod.push(paramsObject['compare_end']);
                this.updateUrlParam('compare_start', this.comparisonPeriod[0]);
                this.updateUrlParam('compare_end', this.comparisonPeriod[1]);
            }

            if ('group' in paramsObject) {
                this.group = paramsObject['group'];
            } else {
                this.group = 'day';
            }

            if ('columns' in paramsObject) {
                for (const column of paramsObject['columns'].split(',')) {
                    this.selectedColumns[column] = true;
                }
            }

            if ('filters' in paramsObject) {
                this.urlFilters = decodeURIComponent(paramsObject['filters']);
                this.filterInputs = JSON.parse(decodeURIComponent(paramsObject['filters']));
                this.apiService.getFilterOptionUpdates({
                    filters: this.filterInputs,
                }).then(resp => {
                    if (resp.data?.data?.status) {
                        this.updateFilterOptions(resp.data.data.filter_updates ?? null);
                        this.filterInputs = JSON.parse(decodeURIComponent(paramsObject['filters']));
                    }
                }).catch(err => {
                    this.$emit('error-message', err.message);
                });
            }

            if ('company' in paramsObject && 'company_options' in paramsObject) {
                this.companies = JSON.parse(decodeURIComponent(paramsObject['company_options']));
                this.selectedCompany = paramsObject['company'];
            }

            if ('campaigns' in paramsObject) {
                this.urlCampaigns = decodeURIComponent(paramsObject['campaigns']);
                setTimeout(() => {
                    this.selectedCampaigns = JSON.parse(this.urlCampaigns);
                }, 100);
            }
        },
        initGroups(groupsArray) {
            this.groups = [];
            for (const groupKey of groupsArray) {
                this.groups.push({
                    'id': groupKey,
                    'name': groupKey.replace('_', ' ').split(' ').map(word =>
                        word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
                    ).join(' '),
                })
            }
        },
        initColumns(columnsArray, columnOrder) {
            this.columns = columnsArray;
            this.columnOrder = columnOrder;
            if (this.selectedColumns.length < 1) {
                for (const column of Object.keys(this.columns)) {
                    this.selectedColumns[column] = this.columns[column].default;
                }
            }
        },
        searchCompanies(query) {
            if (!query) {
                this.selectedCompany = '';
                this.companyCampaigns = [];
                this.showCampaignFilter = false;
                this.companySearchLoading = false;
                return;
            }

            this.companySearchLoading = true;
            this.sharedApi.searchCompanyNamesAndId(query).then(res => {
                if (res.data.data.status === true) {
                    this.companies = res.data.data.companies;
                    this.companySearchLoading = false;

                    if (this.companies.length === 0) {
                        this.companyCampaigns = [];
                        this.showCampaignFilter = false;
                    }else{
                        this.showCampaignFilter = false;
                    }
                }
            }).catch(err => {
                this.$emit('error-message',  'Error retrieving companies. ' + err.message);
                this.companySearchLoading = false;
                console.error(err);
            });
        },
        handleAutocompleteCompanyInput(value) {
            if (!value) {
                this.selectedCompany = '';
                this.companySearchLoading = false;
                this.companyCampaigns = [];
                this.showCampaignFilter = false;
            }
        },
        searchCompanyCampaigns(companyId, query) {
            if (!companyId || companyId === 'undefined' || companyId === 'null') {
                this.companyCampaigns = [];
                this.showCampaignFilter = false;
                this.selectedCompany = null;
                return;
            }

            this.sharedApi.searchCompanyCampaigns(companyId, query).then(res => {
                this.companyCampaigns = res.data.data.results;
                for (let i = 0; i < this.companyCampaigns.length; i++) {
                    this.companyCampaigns[i].name = this.companyCampaigns[i].id + ": " + this.companyCampaigns[i].name;
                }
                this.showCampaignFilter = this.companyCampaigns.length > 0;
            }).catch(err => {
                this.$emit('error-message',  'Error retrieving campaigns. ' + err.message);
                console.error(err);
            });
        },
        clearCompany() {
            this.selectedCompany = '';
            this.companyCampaigns = [];
            this.showCampaignFilter = false;
        },
        checkColDisabled(colKey) {
            return !(this.columns[colKey]?.disabled_for.includes(this.group));
        },
        initLocations(locations) {
            if (locations) {
                this.$emit('locations-set', locations);
            }
        },
        deleteFilterPreset() {
            if (this.saving || !this.deletingPreset) return;
            this.saving = true;

            this.apiService.deleteUserPreset(this.deletingPreset).then(resp => {
                if (resp.data?.data?.status) {
                    this.presets = resp.data.data.presets;
                }
            }).catch(err => {
                console.error(err);
            }).finally(() => {
                this.saving = false;
                this.deletingPreset = null;
                this.showPresetModal = false;
            });
        },
        updatePresets(payload) {
            if (this.saving) return;
            this.saving = true;

            this.apiService.saveUserPreset(payload).then(resp => {
                if (resp.data?.data?.status) {
                    this.presets = this.sortPresets(resp.data.data.presets);
                }
            }).catch(err => {
                this.$emit('error-message',  err.message);
            }).finally(() => {
                this.saving = false;
                this.closePresetModal();
            });
        },
        sortPresets(presetArray) {
            return presetArray.sort((a,b) => a.name > b.name ? 1 : 0);
        },
        openSavePresetModal() {
            this.deletingPreset = false;
            this.showPresetModal = true;
        },
        openDeletePresetModal(deleteOption) {
            this.deletingPreset = deleteOption;
            this.showPresetModal = true;
        },
        closePresetModal() {
            this.deletingPreset = false;
            this.showPresetModal = false;
            this.presetName = "";
            this.modalError = null;
        },
        confirmPresetModal() {
            if (this.deletingPreset) {
                this.deleteFilterPreset();
            }
            else {
                if (!this.validatePresetName()) return;
                this.saveFilterPreset();
            }
        },
        saveFilterPreset() {
            const payload = {
                name:  this.presetName.trim(),
                value: {
                    basePeriod: this.basePeriod,
                    comparisonPeriod: this.comparisonPeriod,
                    group: this.group,
                    filters: this.filters,
                    filterInputs: this.filterInputs,
                    selectedColumns: this.selectedColumns,
                    companies: this.companies,
                    selectedCompany: this.selectedCompany,
                    selectedCampaigns: this.selectedCampaigns,
                },
            }
            this.updatePresets(payload);
        },
        validatePresetName() {
            this.modalError = null;
            const errors = [];
            const currentName = this.presetName.trim().toLowerCase();
            const invalidNames = this.presets.map(preset => preset.name.toLowerCase());
            if (invalidNames.includes(currentName)) {
                errors.push('That name is already in use. Please enter a unique name.');
            }

            if (errors.length) {
                this.modalError = errors.join("\n");
                return false;
            }
            else return true;
        },
        async handlePresetChange() {
            if (this.selectedPreset) {
                const targetPreset = this.presets.find(preset => preset.name === this.selectedPreset);
                if (targetPreset) {
                    this.basePeriod = [...targetPreset.value.basePeriod];
                    this.group = targetPreset.value.group;
                    for (const filterKey in targetPreset.value.filters) {
                        if (targetPreset.value.filters[filterKey]) {
                            for (let searchFilterKey = 0; searchFilterKey < this.filters.length; searchFilterKey++) {
                                if (this.filters[searchFilterKey]['id'] === targetPreset.value.filters[filterKey]['id']) {
                                    this.filters[searchFilterKey] = JSON.parse(JSON.stringify(targetPreset.value.filters[filterKey]));
                                    break;
                                }
                            }
                        }
                    }
                    this.filterInputs = JSON.parse(JSON.stringify(targetPreset.value.filterInputs));
                    this.getFilterOptionUpdates();
                    this.selectedColumns = [...targetPreset.value.selectedColumns];
                    this.companies = targetPreset.value.companies ? JSON.parse(JSON.stringify(targetPreset.value.companies)) : [];
                    this.selectedCompany = targetPreset.value.selectedCompany;

                    setTimeout(() => {
                        this.comparisonPeriod = targetPreset.value.comparisonPeriod ? [...targetPreset.value.comparisonPeriod] : [];
                        this.selectedCampaigns = targetPreset.value.selectedCampaigns ? JSON.parse(JSON.stringify(targetPreset.value.selectedCampaigns)) : [];
                        this.generateReport();
                    }, 100);
                }
            }
        },
        handleCustomUpdate(newVal) {
            if ('presets' in newVal) {
                this.selectedPreset = newVal.presets;
                this.handlePresetChange();
            }
        },
        generateReport(initial = false) {
            this.setLoading(true);
            const columnSelected = value => value;
            this.cleanSelectedColumns();

            this.apiService.getLeadsReport({
                'base_start': this.basePeriod[0],
                'base_end': this.basePeriod[1],
                'compare_start': this.comparisonPeriod ? this.comparisonPeriod[0] : null,
                'compare_end': this.comparisonPeriod ? this.comparisonPeriod[1] : null,
                'group': this.group,
                'columns': Object.keys(this.selectedColumns).filter(key => columnSelected(this.selectedColumns[key])),
                'filters': initial ? JSON.parse(this.urlFilters) : this.filterInputs,
                'company': this.selectedCompany && this.selectedCompany !== 'undefined' && this.selectedCompany !== 'null' ? this.selectedCompany : null,
                'campaigns': initial ? JSON.parse(this.urlCampaigns) : this.selectedCampaigns,
            }).then(resp => {
                this.$emit('update-data',
                    resp.data.data.report,
                    resp.data.data.compare,
                    this.group.charAt(0).toUpperCase() + this.group.slice(1),
                    this.filteredColumns,
                    resp.data.data.columns,
                    resp.data.data.column_order,
                    this.basePeriod[0],
                    this.basePeriod[1],
                    this.transformFilterInputs());
            }).catch(e => this.$emit('error-message', 'Failed to generate report. '+e)).finally(() => this.setLoading(false));
        },
        cleanSelectedColumns() {
            for (const colKey in this.selectedColumns) {
                if (!this.checkColDisabled(colKey)) {
                    this.selectedColumns[colKey] = false;
                }
            }
        },
        setLoading(loading) {
            this.loading = loading;
            this.$emit('update-loading', loading);
        },
        resetFilters() {
            this.setDefaultBasePeriod();
            this.comparisonPeriod = null;
            this.group = 'day';
            this.selectedColumns = [];
            this.initColumns(this.columns);
            this.filterInputs = {};
        },
        setDefaultBasePeriod() {
            this.basePeriod = [];
            this.basePeriod.push(this.now.minus(Duration.fromObject({days: 7})).toFormat(this.datepickerFormat));
            this.basePeriod.push(this.now.toFormat(this.datepickerFormat));
        },
        clearFilter (filterId) {
            delete this.filterInputs[filterId]
        },
        updateFilterDefaults (filterChange) {
            Object.assign(this.filterDefaults, { ...filterChange })
        },
        getFilterOptionUpdates() {
            this.apiService.getFilterOptionUpdates({
                filters: this.filterInputs,
            }).then(resp => {
                if (resp.data?.data?.status) {
                    this.updateFilterOptions(resp.data.data.filter_updates ?? null);
                }
            }).catch(err => {
                this.$emit('error-message', err.message);
            });
        },
        updateFilterOptions(updatedFilterOptions) {
            if (updatedFilterOptions) {
                for (const filterKey in updatedFilterOptions) {
                    const targetIndex = this.filters.findIndex(filter => {
                        return filter.id === filterKey;
                    });
                    if (targetIndex >= 0) this.filters[targetIndex] = updatedFilterOptions[filterKey];
                }
            }
        },
        exportToCsv() {
            this.$emit('export-to-csv');
        },
        setBasePeriod(period) {
            this.basePeriodSelection = period;
        },
        getPeriodSlotName(p) {
            return p + '-button';
        },
        getDifferenceInDays(date1, date2) {
            if (!date1 ?? !date2)
                return;
            const startDate = new Date(date1);
            const endDate = new Date(date2);
            const differenceInMilliseconds = endDate - startDate;
            const millisecondsInDay = 1000 * 60 * 60 * 24;
            return Math.round(differenceInMilliseconds / millisecondsInDay);
        },
        updateUrlParam(param, value) {
            if (this.reportsPage) {
                const currentUrl = new URL(window.location.href);

                if (currentUrl.searchParams.has(param)) {
                    currentUrl.searchParams.set(param, value);
                } else {
                    currentUrl.searchParams.append(param, value);
                }

                window.history.replaceState({}, '', currentUrl.toString()); // To not add back set
            }
        },
        removeUrlParam(param) {
            if (this.reportsPage) {
                const currentUrl = new URL(window.location.href);

                if (currentUrl.searchParams.has(param)) {
                    currentUrl.searchParams.delete(param);
                    window.history.replaceState({}, '', currentUrl.toString()); // To not add back set
                }
            }
        },
        toggleColumnsPopup() {
            this.columnsPopup = !this.columnsPopup;
        },
        updateColumnShown(colKey) {
            this.selectedColumns[colKey] = !this.selectedColumns[colKey];
        },
        toggleAllColumnsShown(show = false) {
            for (const colKey in Object.keys(this.columns)) {
                this.selectedColumns[colKey] = show;
            }
        },
        transformFilterInputs() {
            let industries = [];
            let products = [];
            for (const filter in this.filters) {
                if (this.filters[filter].id === 'leads-industry' && this.filterInputs['leads-industry']) {
                    const available = Object.keys(this.filters[filter].options);
                    const selectedIds = Object.values(this.filterInputs['leads-industry']);
                    industries = available.filter(ind => selectedIds.includes(this.filters[filter].options[ind]));
                } else if (this.filters[filter].id === 'leads-product' && this.filterInputs['leads-product']) {
                    const available = Object.keys(this.filters[filter].options);
                    const selectedIds = Object.values(this.filterInputs['leads-product']);
                    products = available.filter(ind => selectedIds.includes(this.filters[filter].options[ind]));
                }
            }

            return {
                'States': this.filterInputs['leads-state'] ? this.filterInputs['leads-state']['leads-state'] : '',
                'Counties': this.filterInputs['leads-state'] ? this.filterInputs['leads-state']['leads-county'] : '',
                'Industries': industries,
                'Products': products,
                'Territory': this.filterInputs['leads-territory'] ? this.filterInputs['leads-territory']['leads-territory'] : '',
                'Territory Locations': this.filterInputs['leads-territory'] ? this.filterInputs['leads-territory']['leads-territory-locations'] : '',
                'Company': this.selectedCompany,
                'Campaigns': this.selectedCampaigns
            };
        },
        setDefaultColumnsForGroup() {
            for (const colKey in Object.keys(this.columns)) {
                if (this.columns[colKey].default_for.includes(this.group)) {
                    this.selectedColumns[colKey] = true;
                }
            }
        }
    },
    watch: {
        basePeriod(newValue, oldValue) {
            this.updateUrlParam('base_start', newValue[0]);
            this.updateUrlParam('base_end', newValue[1]);
            this.comparisonPeriod = [];
        },
        comparisonPeriod(newValue, oldValue) {
            if (newValue && newValue.length) {
                this.updateUrlParam('compare_start', newValue[0]);
                this.updateUrlParam('compare_end', newValue[1]);
            } else {
                this.removeUrlParam('compare_start');
                this.removeUrlParam('compare_end');
            }
        },
        group(newValue, oldValue) {
            this.setDefaultColumnsForGroup();
            this.updateUrlParam('group', newValue);
        },
        filterInputs: {
            handler(newVal, oldVal) {
                this.updateUrlParam('filters', encodeURIComponent(JSON.stringify(this.filterInputs)));
            },
            deep: true
        },
        selectedColumns: {
            handler(newVal, oldVal) {
                const columnSelected = value => value;
                this.updateUrlParam('columns', Object.keys(this.selectedColumns).filter(key => columnSelected(this.selectedColumns[key])),);
            },
            deep: true
        },
        selectedCompany: function (newVal, oldVal) {
            if (newVal !== oldVal) {
                this.updateUrlParam('company', newVal);
                this.selectedCampaigns = [];
                this.showCampaignFilter = false;
                if (newVal) {
                    this.searchCompanyCampaigns(newVal);
                } else {
                    this.companyCampaigns = [];
                    this.showCampaignFilter = false;
                }
            }
        },
        companies: {
            handler(newVal, oldVal) {
                this.updateUrlParam('company_options', encodeURIComponent(JSON.stringify(newVal)));
            },
            deep: true
        },
        selectedCampaigns: {
            handler(newVal, oldVal) {
                this.updateUrlParam('campaigns', encodeURIComponent(JSON.stringify(newVal)));
            },
            deep: true
        },
    }
}
</script>

<style scoped>

</style>
