<template>
    <div ref="fullscreenElement" class="main-layout font-body" :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
        <alerts-container :text="alertText" :alert-type="alertType" v-if="alertActive" :dark-mode="darkMode"/>
        <div class="w-full">
            <div class="w-full flex-auto relative" :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
                <div class="border rounded-lg px-8 pb-8 "
                     :style="{ maxWidth: (reportsPage || isFullscreen) ? '100vw' : '65vw'}"
                     :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border text-grey-120': darkMode}">
                    <div class="flex items-center justify-between flex-wrap pt-6 pb-4">
                        <div class="flex justify-between items-center w-full">
                            <div class="inline-flex items-center" :class="{'text-grey-120': darkMode}">
                                <a v-if="reportsPage" href="/reports" class="text-xl font-medium text-grey-300 leading-none hover:text-grey-400 transition duration-200 cursor-pointer">Reports</a>
                                <h3 class="text-xl inline-flex font-medium leading-none items-center" :class="{'text-black': !darkMode, 'text-grey-120': darkMode}">
                                    <svg v-if="reportsPage" class="mx-4 fill-current" width="7" height="13" viewBox="0 0 7 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M0.327334 0.260067C0.735992 -0.11144 1.36844 -0.0813234 1.73995 0.327334L6.73995 5.82733C7.08669 6.20876 7.08669 6.79126 6.73995 7.17268L1.73995 12.6727C1.36844 13.0813 0.735992 13.1115 0.327334 12.7399C-0.0813234 12.3684 -0.11144 11.736 0.260067 11.3273L4.64855 6.50001L0.260067 1.67268C-0.11144 1.26402 -0.0813234 0.631574 0.327334 0.260067Z"/>
                                    </svg>
                                    Leads Report
                                </h3>
                            </div>
                            <svg v-if="isFullscreen" @click="toggleFullscreen" class="cursor-pointer" :fill="!darkMode ? '#000000' : '#FFFFFF'" height="20px" width="20px" id="Capa_1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 385.331 385.331" xml:space="preserve"><g stroke-width="0"></g><g stroke-linecap="round" stroke-linejoin="round"></g><g> <g> <g id="Fullscreen_Exit"> <path d="M264.943,156.665h108.273c6.833,0,11.934-5.39,11.934-12.211c0-6.833-5.101-11.85-11.934-11.838h-96.242V36.181 c0-6.833-5.197-12.03-12.03-12.03s-12.03,5.197-12.03,12.03v108.273c0,0.036,0.012,0.06,0.012,0.084 c0,0.036-0.012,0.06-0.012,0.096C252.913,151.347,258.23,156.677,264.943,156.665z"></path> <path d="M120.291,24.247c-6.821,0-11.838,5.113-11.838,11.934v96.242H12.03c-6.833,0-12.03,5.197-12.03,12.03 c0,6.833,5.197,12.03,12.03,12.03h108.273c0.036,0,0.06-0.012,0.084-0.012c0.036,0,0.06,0.012,0.096,0.012 c6.713,0,12.03-5.317,12.03-12.03V36.181C132.514,29.36,127.124,24.259,120.291,24.247z"></path> <path d="M120.387,228.666H12.115c-6.833,0.012-11.934,5.39-11.934,12.223c0,6.833,5.101,11.85,11.934,11.838h96.242v96.423 c0,6.833,5.197,12.03,12.03,12.03c6.833,0,12.03-5.197,12.03-12.03V240.877c0-0.036-0.012-0.06-0.012-0.084 c0-0.036,0.012-0.06,0.012-0.096C132.418,233.983,127.1,228.666,120.387,228.666z"></path> <path d="M373.3,228.666H265.028c-0.036,0-0.06,0.012-0.084,0.012c-0.036,0-0.06-0.012-0.096-0.012 c-6.713,0-12.03,5.317-12.03,12.03v108.273c0,6.833,5.39,11.922,12.223,11.934c6.821,0.012,11.838-5.101,11.838-11.922v-96.242 H373.3c6.833,0,12.03-5.197,12.03-12.03S380.134,228.678,373.3,228.666z"></path> </g> <g> </g> <g> </g> <g> </g> <g> </g> <g> </g> <g> </g> </g> </g></svg>
                            <svg v-if="!isFullscreen" @click="toggleFullscreen" class="cursor-pointer" :fill="!darkMode ? '#000000' : '#FFFFFF'" height="20px" width="20px" id="Capa_1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384.97 384.97" xml:space="preserve"><g stroke-width="0"></g><g stroke-linecap="round" stroke-linejoin="round"></g><g> <g> <g id="Fullscreen"> <path d="M384.97,12.03c0-6.713-5.317-12.03-12.03-12.03H264.847c-6.833,0-11.922,5.39-11.934,12.223 c0,6.821,5.101,11.838,11.934,11.838h96.062l-0.193,96.519c0,6.833,5.197,12.03,12.03,12.03c6.833-0.012,12.03-5.197,12.03-12.03 l0.193-108.369c0-0.036-0.012-0.06-0.012-0.084C384.958,12.09,384.97,12.066,384.97,12.03z"></path> <path d="M120.496,0H12.403c-0.036,0-0.06,0.012-0.096,0.012C12.283,0.012,12.247,0,12.223,0C5.51,0,0.192,5.317,0.192,12.03 L0,120.399c0,6.833,5.39,11.934,12.223,11.934c6.821,0,11.838-5.101,11.838-11.934l0.192-96.339h96.242 c6.833,0,12.03-5.197,12.03-12.03C132.514,5.197,127.317,0,120.496,0z"></path> <path d="M120.123,360.909H24.061v-96.242c0-6.833-5.197-12.03-12.03-12.03S0,257.833,0,264.667v108.092 c0,0.036,0.012,0.06,0.012,0.084c0,0.036-0.012,0.06-0.012,0.096c0,6.713,5.317,12.03,12.03,12.03h108.092 c6.833,0,11.922-5.39,11.934-12.223C132.057,365.926,126.956,360.909,120.123,360.909z"></path> <path d="M372.747,252.913c-6.833,0-11.85,5.101-11.838,11.934v96.062h-96.242c-6.833,0-12.03,5.197-12.03,12.03 s5.197,12.03,12.03,12.03h108.092c0.036,0,0.06-0.012,0.084-0.012c0.036-0.012,0.06,0.012,0.096,0.012 c6.713,0,12.03-5.317,12.03-12.03V264.847C384.97,258.014,379.58,252.913,372.747,252.913z"></path> </g> <g> </g> <g> </g> <g> </g> <g> </g> <g> </g> <g> </g> </g> </g></svg>
                        </div>
                        <p v-if="!reportsPage && locationString.length > 0" class="text-sm mt-2">Territories: {{ locationString }}</p>
                    </div>
                    <Inputs :dark-mode="darkMode" :reports-page="reportsPage" @locations-set="locationsSet" @update-data="updateData" @error-message="errorMessage" @update-loading="updateLoading" @export-to-csv="exportToCsv" :api-service="apiService"></Inputs>
                    <loading-spinner v-if="loading" class="h-80 flex items-center justify-center" :dark-mode="darkMode"/>
                    <LeadsReportTable v-else ref="table" :full-screen="isFullscreen" :group-key="groupKey" :reports-page="reportsPage" :columns="columns" :all-column-order="columnOrder" :returned-columns="returnedColumns" :data="data" :compare-data="compareData" :filter-inputs="filterInputs" :base-start="baseStart" :base-end="baseEnd" :dark-mode="darkMode"></LeadsReportTable>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
import Inputs from "./components/LeadsReportInputs.vue";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import ApiService from "./services/api/api";
import LeadsReportTable from "./components/LeadsReportTable.vue";
import {ChevronDownIcon, ChevronRightIcon} from "@heroicons/vue/solid";

export default {
    name: "LeadsReport",
    components: {
        ChevronDownIcon,
        ChevronRightIcon,
        Inputs,
        AlertsContainer,
        LoadingSpinner,
        LeadsReportTable,
    },
    props: {
        reportsPage: {
            type: Boolean,
            default: false
        },
        darkMode: {
            type: Boolean,
            default: false
        },
    },
    data: function() {
        return {
            scenarios: null,
            loading: false,
            alertActive: false,
            alertType: '',
            alertText: '',
            apiService: null,
            groupKey: 'Day',
            columns: {},
            columnOrder: [],
            returnedColumns: {},
            data: {},
            compareData: {},
            locationString: '',
            isFullscreen: false,
            fullscreenHtml: '',
            baseStart: '',
            baseEnd: '',
            filterInputs: {},
        }
    },
    created() {
        this.apiService = ApiService.make();
    },
    mounted() {
        document.addEventListener('fullscreenchange', this.handleFullscreenChange);
        document.addEventListener('mozfullscreenchange', this.handleFullscreenChange);
        document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange);
        document.addEventListener('msfullscreenchange', this.handleFullscreenChange);
    },
    beforeDestroy() {
        document.removeEventListener('fullscreenchange', this.handleFullscreenChange);
        document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange);
        document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange);
        document.removeEventListener('msfullscreenchange', this.handleFullscreenChange);
    },
    methods: {
        activateAlert(type, text) {
            this.alertActive = true;
            this.alertType = type;
            this.alertText = text;
            setTimeout(()=>{
                this.alertActive = false
            },6000)
        },
        toggleFullscreen() {
            if (this.isFullscreen) {
                this.exitFullscreen();
            } else {
                this.requestFullscreen();
            }
        },
        requestFullscreen() {
            const element = this.$refs.fullscreenElement;
            if (element.requestFullscreen) {
                element.requestFullscreen();
            } else if (element.mozRequestFullScreen) {
                // Firefox fullscreen API
                element.mozRequestFullScreen();
            } else if (element.webkitRequestFullscreen) {
                // Chrome and Safari Fullscreen API
                element.webkitRequestFullscreen();
            } else if (element.msRequestFullscreen) {
                // Edge Fullscreen API
                element.msRequestFullscreen();
            }
            this.isFullscreen = true;
        },
        exitFullscreen() {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if (document.mozCancelFullScreen) {
                document.mozCancelFullScreen();
            } else if (document.webkitExitFullscreen) {
                document.webkitExitFullscreen();
            } else if (document.msExitFullscreen) {
                document.msExitFullscreen();
            }
            this.isFullscreen = false;
        },
        handleFullscreenChange() {
            this.isFullscreen = !!(document.fullscreenElement ||
                document.mozFullScreenElement ||
                document.webkitFullscreenElement ||
                document.msFullscreenElement);
        },
        updateData(data, compareData, groupKey, columns, returnedColumns, columnOrder, baseStart, baseEnd, filterInputs) {
            this.data = data;
            this.compareData = compareData;
            this.groupKey = groupKey;
            this.columns = columns;
            this.columnOrder = columnOrder;
            this.returnedColumns = returnedColumns;
            this.baseStart = baseStart;
            this.baseEnd = baseEnd;
            this.filterInputs = filterInputs;
        },
        updateLoading(value) {
            this.loading = value;
        },
        errorMessage(message) {
            this.data = [];
            this.compareData = [];
            this.updateLoading(false);
            this.activateAlert('error', message);
        },
        locationsSet(locations) {
            this.locationString = locations.join(', ');
        },
        exportToCsv() {
            this.$refs.table.exportToCsv();
        }
    }
}
</script>

<style scoped>

</style>
