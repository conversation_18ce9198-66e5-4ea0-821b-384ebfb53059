<template>
    <div class="main-layout font-body">
        <div class="w-full flex-auto px-2 py-5 relative" :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
            <div class="border rounded-lg px-4 pb-8 "
                 :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border text-grey-120': darkMode}">
                <div class="flex items-center justify-between flex-wrap pt-6 pb-4">
                    <div class="flex justify-between items-center w-full">
                        <div class="inline-flex items-center" :class="{'text-grey-120': darkMode}">
                            <a href="/reports" class="text-xl font-medium text-grey-300 leading-none hover:text-grey-400 transition duration-200 cursor-pointer">Reports</a>
                            <h3 class="text-xl inline-flex font-medium leading-none items-center" :class="{'text-black': !darkMode, 'text-grey-120': darkMode}">
                                <svg class="mx-4 fill-current" width="7" height="13" viewBox="0 0 7 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M0.327334 0.260067C0.735992 -0.11144 1.36844 -0.0813234 1.73995 0.327334L6.73995 5.82733C7.08669 6.20876 7.08669 6.79126 6.73995 7.17268L1.73995 12.6727C1.36844 13.0813 0.735992 13.1115 0.327334 12.7399C-0.0813234 12.3684 -0.11144 11.736 0.260067 11.3273L4.64855 6.50001L0.260067 1.67268C-0.11144 1.26402 -0.0813234 0.631574 0.327334 0.260067Z"/>
                                </svg>
                                Revenue Streams Report
                            </h3>
                        </div>
                    </div>
                </div>

                <!-- Inputs -->
                <div class="inline-flex">
                    <date-picker
                        v-model="dateFilters.date_range"
                        :enable-time-picker="false"
                        :dark="darkMode"
                        class="max-w-64"
                        range
                        auto-apply
                        placeholder="mm-dd-yy"
                        format="PP"
                        timezone="America/Denver"
                    >
                        <template #input-icon>
                            <svg class="ml-2" width="14" height="16" viewBox="0 0 14 16" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M3.11133 7H4.66688V8.55556H3.11133V7ZM3.11133 10.1111H4.66688V11.6667H3.11133V10.1111ZM6.22244 7H7.77799V8.55556H6.22244V7ZM6.22244 10.1111H7.77799V11.6667H6.22244V10.1111ZM9.33355 7H10.8891V8.55556H9.33355V7ZM9.33355 10.1111H10.8891V11.6667H9.33355V10.1111Z"
                                    fill="#0081FF"/>
                                <path
                                    d="M1.55556 15.5556H12.4444C13.3023 15.5556 14 14.8579 14 14V3.11111C14 2.25322 13.3023 1.55556 12.4444 1.55556H10.8889V0H9.33333V1.55556H4.66667V0H3.11111V1.55556H1.55556C0.697667 1.55556 0 2.25322 0 3.11111V14C0 14.8579 0.697667 15.5556 1.55556 15.5556ZM12.4444 4.66667L12.4452 14H1.55556V4.66667H12.4444Z"
                                    fill="#0081FF"/>
                            </svg>
                        </template>
                    </date-picker>
                    <Filterable
                        class="ml-3 z-40"
                        :dark-mode="darkMode"
                        :filters="filters"
                        v-model="filterInputs"
                        @update:defaults="updateFilterDefaults"
                        @update:filterOptions="getFilterOptionUpdates"
                    />
                    <CustomButton
                        type="submit"
                        class="ml-3"
                        :dark-mode="darkMode"
                        @click="getReport()">
                        Generate
                    </CustomButton>
                </div>
                <div>
                    <FilterableActivePills
                        class="px-8 mb-6 mt-6"
                        v-if="filters.length"
                        :filters="filters"
                        :active-filters="filterInputs"
                        :dark-mode="darkMode"
                        @reset-filter="clearFilter"
                    />
                </div>

                <!-- Tab Navigation -->
                <div class="flex border-b mb-6" :class="{'border-light-border': !darkMode, 'border-dark-border': darkMode}">
                    <button
                        v-for="tab in tabs"
                        :key="tab.id"
                        @click="activeTab = tab.id"
                        class="px-6 py-3 font-medium transition-colors duration-200"
                        :class="[
                            activeTab === tab.id
                                ? darkMode
                                    ? 'text-blue-400 border-b-2 border-blue-400'
                                    : 'text-blue-600 border-b-2 border-blue-600'
                                : darkMode
                                    ? 'text-grey-300 hover:text-grey-120'
                                    : 'text-grey-500 hover:text-grey-700'
                        ]"
                    >
                        {{ tab.label }}
                    </button>
                </div>

                <!-- Tab Content -->
                <div v-show="activeTab === 'table'" class="-mx-4">
                    <div class="flex justify-end mb-4 px-4">
                        <CustomButton
                            type="button"
                            :dark-mode="darkMode"
                            @click="copyTableDataAsCSV"
                            :disabled="!sortedData || sortedData.length === 0"
                        >
                            Copy as CSV
                        </CustomButton>
                    </div>
                    <simple-table
                    v-model="dateFilters"
                    :dark-mode="darkMode"
                    :data="sortedData"
                    :headers="headers"
                    :loading="loading"
                    title="Revenue Streams Report"
                    :no-pagination="true"
                    @page-change="handlePageChange"
                    @sort="handleTableSort"
                    :table-filters="tableFilters"
                    row-classes="gap-5 grid items-center py-2 rounded px-5 text-sm"
                >
                    <template v-slot:row.col.revenue="{item}">
                        <div class="relative">
                            {{ $filters.currency(item?.revenue) }}
                        </div>
                    </template>
                    <template v-slot:row.col.cost="{item}">
                        <div class="relative">
                            {{ $filters.currency(item?.cost) }}
                        </div>
                    </template>
                    <template v-slot:row.col.gross_profit="{item}">
                        <div class="relative">
                            {{ $filters.currency(item?.gross_profit) }}
                        </div>
                    </template>
                    </simple-table>
                </div>

                <!-- Chart Tab Content -->
                <div v-show="activeTab === 'chart'">
                    <!-- Y-Axis Selector -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium mb-2 min-w-52" :class="{'text-grey-120': darkMode, 'text-grey-700': !darkMode}">
                            Select Metric:
                        </label>
                        <select
                            v-model="selectedMetric"
                            class="px-4 py-2 border rounded-md w-64"
                            :class="{
                                'bg-dark-module border-dark-border text-grey-120': darkMode,
                                'bg-white border-light-border text-grey-700': !darkMode
                            }"
                        >
                            <option value="total_leads">Total Leads</option>
                            <option value="gts_leads">GTS Leads</option>
                            <option value="sold_leads">Sold Leads</option>
                            <option value="revenue">Revenue</option>
                            <option value="cost">Cost</option>
                            <option value="gross_profit">Gross Profit</option>
                        </select>
                    </div>

                    <!-- Chart -->
                    <div v-if="loading" class="flex items-center justify-center h-96">
                        <LoadingSpinner :dark-mode="darkMode" />
                    </div>
                    <LineEChart
                        v-else-if="chartData.seriesData.length > 0"
                        :dark-mode="darkMode"
                        :x-axis-data="chartData.xAxisData"
                        :series-data="chartData.seriesData"
                        :y-axis-name="getMetricLabel(selectedMetric)"
                        :x-axis-name="'Date'"
                        :y-axis-min="0"
                        :y-axis-max="null"
                        height="h-96"
                        :line-symbol="'circle'"
                        :legend="{ show: true, type: 'scroll' }"
                    />
                    <div v-else class="text-center" :class="{'text-grey-120': darkMode, 'text-grey-600': !darkMode}">
                        <p class="text-lg">No data available for the selected date range.</p>
                    </div>
                </div>

                <!-- Area Chart Tab Content -->
                <div v-show="activeTab === 'area'">
                    <!-- Y-Axis Selector -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium mb-2 min-w-52" :class="{'text-grey-120': darkMode, 'text-grey-700': !darkMode}">
                            Select Metric:
                        </label>
                        <select
                            v-model="selectedAreaMetric"
                            class="px-4 py-2 border rounded-md w-64"
                            :class="{
                                'bg-dark-module border-dark-border text-grey-120': darkMode,
                                'bg-white border-light-border text-grey-700': !darkMode
                            }"
                        >
                            <option value="total_leads">Total Leads</option>
                            <option value="gts_leads">GTS Leads</option>
                            <option value="sold_leads">Sold Leads</option>
                            <option value="revenue">Revenue</option>
                            <option value="cost">Cost</option>
                            <option value="gross_profit">Gross Profit</option>
                        </select>
                    </div>

                    <!-- Area Chart -->
                    <div v-if="loading" class="flex items-center justify-center h-96">
                        <LoadingSpinner :dark-mode="darkMode" />
                    </div>
                    <div v-else-if="areaChartData.seriesData.length > 0" class="h-96">
                        <v-chart
                            :option="areaChartOptions"
                            :theme="darkMode ? 'a2dark' : 'a2light'"
                            :autoresize="true"
                            class="h-full w-full"
                        />
                    </div>
                    <div v-else class="text-center" :class="{'text-grey-120': darkMode, 'text-grey-600': !darkMode}">
                        <p class="text-lg">No data available for the selected date range.</p>
                    </div>
                </div>

                <!-- Metric Table Tab Content -->
                <div v-show="activeTab === 'metricTable'">
                    <!-- Metric Selector and Copy Button on same line -->
                    <div class="flex justify-between items-end mb-6">
                        <div>
                            <label class="block text-sm font-medium mb-1" :class="{'text-grey-120': darkMode, 'text-grey-700': !darkMode}">
                                Select Metric:
                            </label>
                            <select
                                v-model="selectedMetricTableMetric"
                                class="px-4 py-2 border rounded-md w-64"
                                :class="{
                                    'bg-dark-module border-dark-border text-grey-120': darkMode,
                                    'bg-white border-light-border text-grey-700': !darkMode
                                }"
                            >
                                <option value="total_leads">Total Leads</option>
                                <option value="gts_leads">GTS Leads</option>
                                <option value="sold_leads">Sold Leads</option>
                                <option value="revenue">Revenue</option>
                                <option value="cost">Cost</option>
                                <option value="gross_profit">Gross Profit</option>
                            </select>
                        </div>
                        <CustomButton
                            type="button"
                            :dark-mode="darkMode"
                            @click="copyMetricTableAsCSV"
                            :disabled="!metricTableData.rows || metricTableData.rows.length === 0"
                        >
                            Copy as CSV
                        </CustomButton>
                    </div>

                    <!-- Metric Table -->
                    <div class="-mx-4">
                        <simple-table
                            v-model="metricTableFilters"
                            :dark-mode="darkMode"
                            :data="metricTableData.rows"
                            :headers="metricTableData.headers"
                            :loading="loading"
                            title="Metric Table"
                            :no-pagination="true"
                            row-classes="gap-5 grid items-center py-2 rounded px-5 text-sm"
                        >
                            <template v-for="header in metricTableData.headers.slice(1)" v-slot:[`row.col.${header.field}`]="{item}">
                                <div class="relative">
                                    {{ formatMetricValue(item[header.field]) }}
                                </div>
                            </template>
                        </simple-table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import RevenueStreamsApiService from "./services/api/api.js";
import DatePicker from "@vuepic/vue-datepicker";
import UserSearchAutocomplete from "../../Shared/components/User/UserSearchAutocomplete.vue";
import LabeledValue from "../../Shared/components/LabeledValue.vue";
import Autocomplete from "../../Shared/components/Autocomplete.vue";
import SimpleTable from "../../Shared/components/SimpleTable/SimpleTable.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import InvoiceStatusBadge from "../../BillingManagement/components/InvoiceStatusBadge.vue";
import EntityHyperlink from "../../BillingManagement/components/EntityHyperlink.vue";
import Badge from "../../Shared/components/Badge.vue";
import {DateTime} from "luxon";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import AvailableBudgetTable from "../HistoricalAvailableBudgetsReport/components/AvailableBudgetTable.vue";
import Inputs from "../HistoricalAvailableBudgetsReport/components/Inputs.vue";
import { useToastNotificationStore } from "../../../../stores/billing/tost-notification.store.js";
import LineEChart from "../../Shared/components/ECharts/LineEChart.vue";
import VChart from "vue-echarts";
import {registerTheme, use} from 'echarts/core';
import {SVGRenderer} from "echarts/renderers";
import {LineChart} from "echarts/charts";
import {TitleComponent, TooltipComponent, LegendComponent, GridComponent} from "echarts/components";
import a2dark from '../../Shared/components/ECharts/themes/a2dark';
import a2light from '../../Shared/components/ECharts/themes/a2light';
import FilterableActivePills from "../../Shared/components/Filterables/FilterableActivePills.vue";
import Filterable from "../../Shared/components/Filterables/Filterable.vue";

export default {
    name: "RevenueStreams",
    components: {
        Filterable,
        FilterableActivePills,
        Inputs, AvailableBudgetTable, LoadingSpinner,
        Badge,
        EntityHyperlink,
        InvoiceStatusBadge,
        CustomButton,
        SimpleTable,
        Autocomplete,
        LabeledValue,
        UserSearchAutocomplete,
        DatePicker,
        LineEChart,
        VChart
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            api: RevenueStreamsApiService.make(),
            headers: [
                {title: 'stream', field: 'stream'},
                {title: 'Day', field: 'day'},
                {title: 'Total Leads', field: 'total_leads', sortable: true},
                {title: 'GTS Leads', field: 'gts_leads', sortable: true},
                {title: 'Sold Leads', field: 'sold_leads', sortable: true},
                {title: 'Revenue', field: 'revenue', sortable: true},
                {title: 'Cost', field: 'cost', sortable: true},
                {title: 'Gross Profit', field: 'gross_profit', sortable: true},
                {title: 'ROAS', field: 'roas', sortable: true},
            ],
            tableFilters: [],
            dateFilters: {
                date_range: [],
            },
            data: [],
            loading: false,
            paginationData: {},
            toastNotificationStore: useToastNotificationStore(),
            activeTab: 'chart',
            tabs: [
                { id: 'chart', label: 'Line Chart' },
                { id: 'area', label: 'Area Chart' },
                { id: 'table', label: 'Table' },
                { id: 'metricTable', label: 'Metric Table' },
            ],
            selectedMetric: 'gross_profit',
            selectedAreaMetric: 'gross_profit',
            selectedMetricTableMetric: 'gross_profit',
            metricTableFilters: {},
            metricTableHeaders: [
                { title: 'Stream', field: 'stream' }
            ],
            filters: [],
            filterInputs: {},
            filterDefaults: {},
        }
    },
    created() {
        // Register ECharts themes and components for area chart
        registerTheme('a2dark', a2dark);
        registerTheme('a2light', a2light);
        use([
            SVGRenderer,
            TitleComponent,
            TooltipComponent,
            LegendComponent,
            LineChart,
            GridComponent
        ]);

        this.getReportOptions();
        this.handleReset()
    },
    computed: {
        chartData() {
            if (!this.data || this.data.length === 0) {
                return { xAxisData: [], seriesData: [] };
            }

            // Group data by stream
            const streamData = {};
            const allDates = new Set();

            this.data.forEach(item => {
                if (!streamData[item.stream]) {
                    streamData[item.stream] = {};
                }
                streamData[item.stream][item.day] = item[this.selectedMetric];
                allDates.add(item.day);
            });

            // Sort dates chronologically
            const sortedDates = Array.from(allDates).sort();

            // Create series data for each stream
            const seriesData = Object.keys(streamData).map(stream => {
                return {
                    name: stream,
                    data: sortedDates.map(date => streamData[stream][date] || 0)
                };
            });

            return {
                xAxisData: sortedDates,
                seriesData: seriesData
            };
        },
        areaChartData() {
            if (!this.data || this.data.length === 0) {
                return { xAxisData: [], seriesData: [] };
            }

            // Group data by stream
            const streamData = {};
            const allDates = new Set();

            this.data.forEach(item => {
                if (!streamData[item.stream]) {
                    streamData[item.stream] = {};
                }
                streamData[item.stream][item.day] = item[this.selectedAreaMetric];
                allDates.add(item.day);
            });

            // Sort dates chronologically
            const sortedDates = Array.from(allDates).sort();

            // Create series data for each stream with area style and stacking
            const seriesData = Object.keys(streamData).map(stream => {
                const data = sortedDates.map(date => {
                    const value = streamData[stream][date] || 0;
                    // For area chart display only: convert negative values to 0 to prevent crossover lines
                    return Math.max(0, value);
                });

                return {
                    name: stream,
                    data: data,
                    firstDayValue: data[0] || 0,
                    areaStyle: {},
                    stack: 'total',
                    emphasis: {
                        focus: 'series'
                    }
                };
            }).sort((a, b) => a.firstDayValue - b.firstDayValue);

            return {
                xAxisData: sortedDates,
                seriesData: seriesData
            };
        },
        areaChartOptions() {
            if (!this.areaChartData.seriesData.length) {
                return {};
            }

            return {
                aria: {
                    enabled: true
                },
                tooltip: {
                    trigger: 'item',
                    formatter: (params) => {
                        return `<strong>${params.name}</strong><br/>${params.marker} ${params.seriesName}: ${this.formatValue(params.value)}`;
                    }
                },
                legend: {
                    show: true,
                    type: 'scroll'
                },
                xAxis: {
                    type: 'category',
                    data: this.areaChartData.xAxisData,
                    name: 'Date',
                    nameTextStyle: {
                        padding: 20,
                        fontWeight: 'bold',
                        fontSize: 16
                    },
                    nameLocation: 'middle',
                    boundaryGap: false
                },
                yAxis: {
                    type: 'value',
                    name: this.getMetricLabel(this.selectedAreaMetric),
                    nameLocation: 'middle',
                    nameTextStyle: {
                        padding: 50,
                        fontWeight: 'bold',
                        fontSize: 16
                    },
                    min: 0
                },
                series: this.areaChartData.seriesData.map(series => ({
                    ...series,
                    type: 'line',
                    smooth: true
                }))
            };
        },
        metricTableData() {
            if (!this.data || this.data.length === 0) {
                return { headers: this.metricTableHeaders, rows: [] };
            }

            // Get all unique streams
            const allStreams = [...new Set(this.data.map(item => item.stream))].sort();

            // Create a map of stream -> date -> value
            const pivotData = {};
            this.data.forEach(item => {
                if (!pivotData[item.stream]) {
                    pivotData[item.stream] = {};
                }
                pivotData[item.stream][item.day] = item[this.selectedMetricTableMetric];
            });

            // Create rows for the table (using header fields to know which dates to include)
            let rows = allStreams.map(stream => {
                const row = { stream };
                // Skip the first header (Stream) and process the date columns
                this.metricTableHeaders.slice(1).forEach(header => {
                    row[header.field] = pivotData[stream][header.field] || 0;
                });
                return row;
            });

            // Apply sorting if specified
            if (this.metricTableFilters.sort_by && this.metricTableFilters.sort_by.length > 0) {
                const [field, direction] = this.metricTableFilters.sort_by[0].split(':');
                rows = [...rows].sort((a, b) => {
                    const aVal = a[field] || 0;
                    const bVal = b[field] || 0;
                    return direction === 'desc' ? bVal - aVal : aVal - bVal;
                });
            }

            return { headers: this.metricTableHeaders, rows };
        },
        sortedData() {
            if (!this.data || this.data.length === 0) {
                return [];
            }

            let sorted = [...this.data];

            // Apply sorting if specified
            if (this.dateFilters.sort_by && this.dateFilters.sort_by.length > 0) {
                const [field, direction] = this.dateFilters.sort_by[0].split(':');
                sorted = sorted.sort((a, b) => {
                    const aVal = a[field] || 0;
                    const bVal = b[field] || 0;

                    // Handle numeric sorting for these fields
                    if (['total_leads', 'gts_leads', 'sold_leads', 'revenue', 'cost', 'gross_profit', 'roas'].includes(field)) {
                        return direction === 'desc' ? bVal - aVal : aVal - bVal;
                    }

                    // Handle string sorting for other fields
                    const aStr = String(aVal).toLowerCase();
                    const bStr = String(bVal).toLowerCase();
                    if (direction === 'desc') {
                        return bStr.localeCompare(aStr);
                    } else {
                        return aStr.localeCompare(bStr);
                    }
                });
            }

            return sorted;
        }
    },
    methods: {
        formatValue(value) {
            if (this.selectedAreaMetric === 'revenue' || this.selectedAreaMetric === 'cost' || this.selectedAreaMetric === 'gross_profit') {
                return new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD'
                }).format(value);
            }
            return value.toLocaleString();
        },
        async retrieveRevenueReport(params = {}) {
            const dateRange = this.dateFilters.date_range?.map(date =>
                date ? DateTime.fromJSDate(date).setZone('America/Denver').toISO() : undefined
            );

            return await this.api.getRevenueStreamsData({
                date_range: dateRange,
                filters: this.filterInputs,
            }).catch(
                error => this.toastNotificationStore.notifyError(error.message)
            ).finally(() => {
                this.loading = false
            });
        },

        async getReport() {
            this.loading = true

            const res = await this.retrieveRevenueReport();

            this.data = res.data.data

            this.loading = false
        },
        handlePageChange({ newPage }) {
            this.dateFilters.page = newPage
            this.getReport()
        },
        getReportOptions() {
            this.api.getReportOptions().then(resp => {
                this.filters = resp.data.filters;
            }).catch(e => console.log(e));
        },
        handleReset() {
            const today = new Date();
            const lastWeek = new Date();
            lastWeek.setDate(today.getDate() - 7);

            this.dateFilters = {
                date_range: [
                    lastWeek,
                    today,
                ],
            }

            this.getReport()
        },
        getMetricLabel(metric) {
            const labels = {
                total_leads: 'Total Leads',
                gts_leads: 'GTS Leads',
                sold_leads: 'Sold Leads',
                revenue: 'Revenue ($)',
                cost: 'Cost ($)',
                gross_profit: 'Gross Profit ($)'
            };
            return labels[metric] || metric;
        },
        formatMetricValue(value) {
            if (this.selectedMetricTableMetric === 'revenue' || this.selectedMetricTableMetric === 'cost' || this.selectedMetricTableMetric === 'gross_profit') {
                return new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD'
                }).format(value);
            }
            return value.toLocaleString();
        },
        generateCSV() {
            if (!this.sortedData || this.sortedData.length === 0) {
                return '';
            }

            // Create header row
            const headers = this.headers.map(header => header.title);
            const csvContent = [headers.join(',')];

            // Add data rows
            this.sortedData.forEach(row => {
                const csvRow = this.headers.map(header => {
                    let value = row[header.field];

                    // Format currency values
                    if (['revenue', 'cost', 'gross_profit'].includes(header.field) && value !== null && value !== undefined) {
                        value = this.$filters.currency(value);
                    }

                    // Escape values that contain commas or quotes
                    if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
                        value = `"${value.replace(/"/g, '""')}"`;
                    }

                    return value || '';
                });
                csvContent.push(csvRow.join(','));
            });

            return csvContent.join('\n');
        },
        async copyTableDataAsCSV() {
            try {
                const csvData = this.generateCSV();
                await navigator.clipboard.writeText(csvData);
                this.toastNotificationStore.notifySuccess('Table data copied to clipboard as CSV');
            } catch (error) {
                this.toastNotificationStore.notifyError('Failed to copy data to clipboard');
            }
        },
        generateMetricTableCSV() {
            if (!this.metricTableData.rows || this.metricTableData.rows.length === 0) {
                return '';
            }

            // Create header row
            const headers = this.metricTableData.headers.map(header => header.title);
            const csvContent = [headers.join(',')];

            // Add data rows
            this.metricTableData.rows.forEach(row => {
                const csvRow = this.metricTableData.headers.map(header => {
                    let value = row[header.field];

                    // Format the value
                    if (header.field !== 'stream') {
                        value = this.formatMetricValue(value);
                    }

                    // Escape values that contain commas or quotes
                    if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
                        value = `"${value.replace(/"/g, '""')}"`;
                    }

                    return value || '';
                });
                csvContent.push(csvRow.join(','));
            });

            return csvContent.join('\n');
        },
        async copyMetricTableAsCSV() {
            try {
                const csvData = this.generateMetricTableCSV();
                await navigator.clipboard.writeText(csvData);
                this.toastNotificationStore.notifySuccess('Metric table data copied to clipboard as CSV');
            } catch (error) {
                this.toastNotificationStore.notifyError('Failed to copy data to clipboard');
            }
        },
        handleTableSort() {
            // Sorting is handled by the sortedData computed property
            // No need to refetch data - just let the computed property update
        },
        updateMetricTableHeaders() {
            if (!this.data || this.data.length === 0) {
                this.metricTableHeaders = [
                    { title: 'Stream', field: 'stream' }
                ];
                return;
            }

            // Get all unique dates and sort them
            const allDates = [...new Set(this.data.map(item => item.day))].sort();

            // Preserve any existing sort state from current headers
            const currentSortStates = {};
            this.metricTableHeaders.forEach(header => {
                if (header.sort_by) {
                    currentSortStates[header.field] = {
                        sort_by: header.sort_by,
                        number: header.number
                    };
                }
            });

            // Create new headers with date columns
            this.metricTableHeaders = [
                { title: 'Stream', field: 'stream' },
                ...allDates.map(date => {
                    const header = { title: date, field: date, sortable: true };
                    // Restore sort state if it existed
                    if (currentSortStates[date]) {
                        header.sort_by = currentSortStates[date].sort_by;
                        header.number = currentSortStates[date].number;
                    }
                    return header;
                })
            ];
        },
        updateFilterDefaults (filterChange) {
            Object.assign(this.filterDefaults, { ...filterChange })
        },
        clearFilter (filterId) {
            delete this.filterInputs[filterId]
        },
        getFilterOptionUpdates() {
            this.apiService.getFilterOptionUpdates({
                filters: this.filterInputs,
            }).then(resp => {
                if (resp.data?.data?.status) {
                    this.updateFilterOptions(resp.data.data.filter_updates ?? null);
                }
            }).catch(err => {
                this.$emit('error-message', err.message);
            });
        },
        updateFilterOptions(updatedFilterOptions) {
            if (updatedFilterOptions) {
                for (const filterKey in updatedFilterOptions) {
                    const targetIndex = this.filters.findIndex(filter => {
                        return filter.id === filterKey;
                    });
                    if (targetIndex >= 0) this.filters[targetIndex] = updatedFilterOptions[filterKey];
                }
            }
        },
    },
    watch: {
        data: {
            handler() {
                this.updateMetricTableHeaders();
            },
            immediate: true
        },
        selectedMetricTableMetric() {
            // Headers don't change when metric changes, only the data values
        }
    }
}
</script>
