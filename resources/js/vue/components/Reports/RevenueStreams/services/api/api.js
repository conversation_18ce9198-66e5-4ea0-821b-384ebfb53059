import axios from 'axios';

export default class ApiService {
    constructor(baseUrl, baseEndpoint, version) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    static make() {
        return new ApiService('internal-api', 'reports', 1);
    }

    getReportOptions() {
        return this.axios().get('/revenue-streams/options');
    }

    getFilterOptionUpdates() {
        return this.axios().get('/revenue-streams/option-updates');
    }

    getRevenueStreamsData(params) {
        return this.axios().get('/revenue-streams', {
            params: params
        });
    }
}
