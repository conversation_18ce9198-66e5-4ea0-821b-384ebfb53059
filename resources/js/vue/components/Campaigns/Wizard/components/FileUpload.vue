<template>
    <div class="w-full border p-4 rounded cursor-pointer"
         :class="[darkMode ? 'text-slate-100 bg-dark-background border-dark-border' : 'text-slate-900 bg-light-background border-light-border']"
         @dragover="dragOver"
         @drop="drop">
        <div
            class="flex flex-col items-center justify-center w-full text-center relative"
            :class="{'py-6': fileList?.length === 0, 'py-2': fileList?.length > 2}"
        >

            <div v-if="error">
                <p class="text-red-500 text-sm text-center py-4 whitespace-pre">{{ error }}</p>
            </div>

            <label class="block cursor-pointer">
                <input
                    type="file"
                    multiple
                    class="w-px h-px opacity-0 overflow-hidden absolute"
                    ref="fileInput"
                    @change="onChange"
                    :accept="accept"
                >

                <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1 text-cyan-500">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5" />
                    </svg>

                    <p>
                        <span class="text-primary-500">Add {{ multi ? "files" : "file" }}</span> or drop files here
                    </p>
                </div>
            </label>

            <ul class="mt-4" v-if="fileList.length > 0">
                <li class="text-sm p-1" v-for="(file, i) in fileList">
                    {{ file.name }} <button @click="remove(i)" class="pl-2 text-red-500" type="button" title="Remove file">x</button>
                </li>
            </ul>
        </div>
    </div>
</template>

<script>
export default {
    name: 'FileUpload',
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        accept: {
            type: String,
            default: ".pdf,.jpg,.jpeg,.png"
        },
        multi: {
            type: Boolean,
            default: false
        },
    },
    emits: ["file-uploaded"],
    data() {
        return {
            fileList: [],
            error: null,
        }
    },
    methods: {
        validateMimetype(type)  {
            return this.accept.split(',').includes("." + type.split("/").pop());
        },
        remove(i) {
            this.fileList.splice(i, 1);
        },
        dragOver(e) {
            e.preventDefault();
        },
        drop(e) {
            e.preventDefault();
            this.$refs.fileInput.files = e.dataTransfer.files;
            this.onChange();
        },
        onChange() {
            this.error = null;
            this.fileList = !this.multi ? [this.$refs.fileInput.files[0]] : [...this.fileList, ...this.$refs.fileInput.files];

            for(const file of this.fileList) {
                if(!this.validateMimetype(file.type)) {
                    this.error = "Please upload a file of type: " + this.accept
                }
            }

            if(this.error === null)
                this.$emit('file-uploaded', this.fileList);
        },
        clearFileList() {
            this.fileList = [];
        }
    },
    expose: ['clearFileList'],
}
</script>
