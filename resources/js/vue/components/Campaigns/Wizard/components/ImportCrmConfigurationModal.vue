<template>
    <div>
        <Modal
            :dark-mode="darkMode"
            @confirm="importCrmConfiguration"
            @close="closeModal"
            confirm-text="Import"
            key="crmImportModal"
        >
            <template v-slot:header>
                <div class="flex items-center">
                    <svg  class="w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
                    </svg>
                    <h5 class="text-md">Import CRM Delivery</h5>
                </div>
            </template>
            <template v-slot:content>
                <LoadingSpinner v-if="loading" :small="true" :dark-mode="darkMode" />
                <div v-else>
                    <div v-if="!deliveryStore.crmImportOptions?.length">
                        No importable CRM configurations were found.
                    </div>
                    <div v-else class="items-start gap-4">
                        <p :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="block mb-1">
                            Available CRM configurations
                        </p>
                        <Dropdown
                            :dark-mode="darkMode"
                            :options="crmOptions"
                            v-model="selectedCrmId"
                        />
                    </div>
                    <div v-if="selectedCrm">
                        <!--    SYSTEM FIELDS     -->
                        <div v-if="selectedCrm.payload.system_fields" class="border-t border-gray-200 pt-6 mt-8">
                            <h2 class="font-semibold pb-3">System Fields</h2>
                            <div class="grid grid-cols-2 items-start gap-x-4">
                                <div v-for="systemField in selectedCrm.payload.system_fields"
                                     :key="systemField.key"
                                >
                                    <CustomInput
                                        :dark-mode="darkMode"
                                        :disabled="true"
                                        :model-value="systemField.value"
                                        :label="systemField.display_name"
                                    />
                                </div>
                            </div>
                        </div>
                        <!--   CUSTOM HEADERS   -->
                        <div v-if="selectedCrm.payload.headers?.length" class="border-t border-gray-200 pt-6 mt-8">
                            <h2 class="font-semibold pb-3">Custom Headers</h2>
                            <div class="grid grid-cols-2 items-start gap-4 gap-y-6">
                                <div v-for="(customHeader, index) in selectedCrm.payload.headers"
                                     :key="index"
                                >
                                    <div class="grid grid-cols-2 gap-x-2">
                                        <CustomInput
                                            :dark-mode="darkMode"
                                            label="Key"
                                            v-model="customHeader.key"
                                            :disabled="true"
                                        />
                                        <div class="relative">
                                            <CustomInput
                                                :dark-mode="darkMode"
                                                label="Value"
                                                v-model="customHeader.value"
                                                :disabled="true"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--   ADDITIONAL FIELDS   -->
                        <div v-if="selectedCrm.payload.additional_fields?.length && showPresetFields" class="border-t border-gray-200 pt-6 mt-8">
                            <h2 class="font-semibold pb-3">Preset Fields</h2>
                            <div class="grid grid-cols-2 items-start gap-4 gap-y-6">
                                <div v-for="additionalField in selectedCrm.payload.additional_fields"
                                     :key="additionalField.key"
                                >
                                    <CustomInput
                                        :dark-mode="darkMode"
                                        :disabled="true"
                                        :model-value="additionalField.value"
                                        :label="additionalField.display_name"
                                    />
                                </div>
                            </div>
                        </div>
                        <!--   CUSTOM FIELDS   -->
                        <div v-if="selectedCrm.payload.custom_fields?.length" class="border-t border-gray-200 pt-6 mt-8">
                            <h2 class="font-semibold pb-3">Fields</h2>
                            <div class="grid grid-cols-2 items-start gap-4 gap-y-6">
                                <div v-for="customField in selectedCrm.payload.custom_fields"
                                     :key="customField.key"
                                >
                                    <div class="grid grid-cols-2 gap-x-2">
                                        <CustomInput
                                            :dark-mode="darkMode"
                                            label="Key"
                                            v-model="customField.key"
                                            :disabled="true"
                                        />
                                        <div class="relative">
                                            <CustomInput
                                                :dark-mode="darkMode"
                                                label="Value"
                                                v-model="customField.value"
                                                :disabled="true"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--   JSON FIELDS   -->
                        <div v-if="selectedCrm.payload.json_fields?.length" class="border-t border-gray-200 pt-6 mt-8">
                            <h2 class="font-semibold pb-3">Fields</h2>
                            <div class="flex flex-col gap-y-6">
                                <div v-for="jsonField in selectedCrm.payload.json_fields"
                                     :key="jsonField.key"
                                >
                                    <div class="flex items-start gap-x-2">
                                        <CustomInput
                                            :dark-mode="darkMode"
                                            class="w-64"
                                            label="Key"
                                            :model-value="jsonField.key"
                                            :disabled="true"
                                        />
                                        <div class="relative w-full">
                                            <textarea
                                                class="rounded text-sm font-medium w-full border focus:outline-none outline-none focus:ring-0 focus:border-primary-500 focus:shadow-lg focus:shadow-primary-500/10"
                                                :value="jsonField.value"
                                                :name="jsonField.key"
                                                rows="4"
                                                :disabled="true"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </Modal>
    </div>
</template>

<script>
import Modal from "../../../Shared/components/Modal.vue";
import CustomInput from "../../../Shared/components/CustomInput.vue";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import { CrmType, useDeliveryStore } from "../stores/delivery.js";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";

export default {
    name: "CampaignDeliverySlide",
    components: {
        LoadingSpinner,
        CustomInput,
        Dropdown,
        Modal,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['close:modal', 'import:crm'],
    data() {
        return {
            deliveryStore: useDeliveryStore(),
            loading: false,
            selectedCrmId: null,
        }
    },
    computed: {
        selectedCrm() {
            return this.selectedCrmId
                ? this.deliveryStore.crmImportOptions.find(crm => crm.id === this.selectedCrmId)
                : null;
        },
        crmOptions() {
            return [
                { name: '-', id: null },
                ...this.deliveryStore.crmImportOptions.map(config => ({ name: `${config.display_name}${config.campaign_name ? ` (${config.campaign_name})` : ''}`, id: config.id })),
            ];
        },
        showPresetFields() {
            if (this.selectedCrm?.crm_type === CrmType.StandardWebForm) {
                const filledFields = this.selectedCrm?.payload.additional_fields?.filter(v => v.value);
                if (!filledFields?.length)
                    return false;
            }
            return true;
        },
    },
    mounted() {
        this.initialize();
    },
    methods: {
        async initialize() {
            this.loading = true;
            await this.deliveryStore.getCrmImportOptions().catch(e => e);
            this.loading = false;
        },
        importCrmConfiguration() {
            this.$emit('import:crm', this.selectedCrmId);
            this.closeModal();
        },
        closeModal() {
            this.deliveryStore.clearEditingConfiguration();
            this.$emit('close:modal');
        },
    },
}
</script>
