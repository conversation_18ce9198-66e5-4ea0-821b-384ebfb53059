<template>
    <div>
        <Modal
            :small="true"
            :dark-mode="darkMode"
            @close="closeModal"
            @confirm="addZipCodes"
            :close-text="'Cancel'"
            :confirm-text="'Add'"
            :disable-confirm="!allowSave"
            :class="saving && ['pointer-events-none']"
        >
            <template v-slot:header>
                <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 6.75V15m6-6v8.25m.503 3.498l4.875-2.437c.381-.19.622-.58.622-1.006V4.82c0-.836-.88-1.38-1.628-1.006l-3.869 1.934c-.317.159-.69.159-1.006 0L9.503 3.252a1.125 1.125 0 00-1.006 0L3.622 5.689C3.24 5.88 3 6.27 3 6.695V19.18c0 .836.88 1.38 1.628 1.006l3.869-1.934c.317-.159.69-.159 1.006 0l4.994 2.497c.317.158.69.158 1.006 0z" />
                    </svg>
                    <h5 class="text-md ml-2">Upload Zip Code CSV</h5>
                </div>
            </template>
            <template v-slot:content>
                <div v-if="uploadResults?.length" class="w-[100%] max-h-[30rem] overflow-y-auto">
                    <div class="grid grid-cols-1 gap-5 p-3 mb-5 border rounded-md"
                        :class="[darkMode ? 'border-dark-border bg-dark-background' : 'border-light-border bg-light-background']"
                    >
                        <label class="inline-flex items-center">
                            <input type="checkbox"
                               class="form-checkbox h-4 w-4 text-primary-500 rounded"
                               :value="uploadOptions.append"
                               :checked="uploadOptions.append"
                               @change="({ target }) => updateUploadOption('append', target.checked)"
                            />
                            <span class="ml-2">Append Locations</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="checkbox"
                               class="form-checkbox h-4 w-4 text-primary-500 rounded"
                               :value="uploadOptions.replace"
                               :checked="uploadOptions.replace"
                               @change="({ target }) => updateUploadOption('replace', target.checked)"                            />
                            <span class="ml-2">Replace Locations</span>
                        </label>
                    </div>
                    <div class="grid justify-center text-center grid-cols-3 text-sm text-gray-500 capitalize font-bold py-2 px-6 items-center border-b"
                        :class="[darkMode ? 'bg-dark-background border-dark-border' : 'bg-light-background border-light-border']"
                    >
                        <div class="flex items-center">
                            <input type="checkbox"
                               class="form-checkbox h-4 w-4 text-primary-500 rounded"
                               :checked="true"
                                @change="({ target }) => selectAllZipCodes(target.checked)"
                            />
                            <p class="ml-4">Selected</p>
                        </div>
                        <p>Zip Code</p>
                        <p>City</p>
                    </div>
                    <div v-for="zipCode in uploadResults"
                         :key="zipCode.id"
                         class="text-center relative border-b text-gray-600 items-center grid md:grid-cols-3 gap-4 md:gap-0 py-4 px-4 md:px-6"
                         :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']"
                    >
                        <div class="flex text-sm justify-self-start">
                            <input type="checkbox"
                               class="form-checkbox h-4 w-4 text-primary-500 rounded"
                               v-model="selectedZipCodes[zipCode.id]"
                            />
                        </div>
                        <p class="text-sm">{{ zipCode.zip_code }}</p>
                        <p class="text-sm">{{ zipCode.city_name }}</p>
                    </div>
                </div>
                <div v-else
                     :class="[ loading ? 'pointer-events-none grayscale-[50%] opacity-50' : '' ]"
                >
                    <p class="pb-6 text-center">Add zip codes by comma-separated value files (CSV):</p>
                    <FileUpload
                        accept=".csv"
                        :dark-mode="darkMode"
                        @file-uploaded="handleFileUpload"
                    />
                </div>
                <div v-if="errorMessage">
                    <p class="text-red-900 text-sm text-center py-4 whitespace-pre">{{ errorMessage }}</p>
                </div>
            </template>
        </Modal>
    </div>
</template>

<script>
import Modal from "../../../Shared/components/Modal.vue";
import FileUpload from "./FileUpload.vue";
import { useLocalityDataStore } from "../stores/locality-data.js";

export default {
    name: 'ZipCodeUploadModal',
    components: {
        Modal,
        FileUpload,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        saving: {
            type: Boolean,
            default: false,
        }
    },
    emits: [
        'hideZipUploadModal',
        'addZipCodesFromModal'
    ],
    data() {
        return {
            localityDataStore: useLocalityDataStore(),

            smallModal: false,
            loading: false,

            errorMessage: null,

            uploadResults: [],
            selectedZipCodes: {},

            uploadOptions: {
                append: true,
                replace: false
            },
        }
    },
    methods: {
        async handleFileUpload(fileList) {
            this.errorMessage = null;
            const targetFile = fileList[0];
            this.loading = true;

            try {
                const textContent = await targetFile.text();
                const zipStringArray = textContent.trim().replace(/['"]+/g, '').split(/\s*[\n,]+\s*/g);
                const { status, message, zip_codes } = await this.localityDataStore.getZipCodesByStringArray(zipStringArray);
                if (!status) {
                    this.errorMessage = message;
                }
                else {
                    this.uploadResults = zip_codes;
                    zip_codes.forEach(zip => this.selectedZipCodes[zip.id] = true);
                }
            } catch (e) {
                this.errorMessage = `There was an error uploading the file:\n${e.message}`;
            }

            this.loading = false;
        },
        selectAllZipCodes(checked) {
            for (const id in this.selectedZipCodes) {
                this.selectedZipCodes[id] = !!checked;
            }
        },
        addZipCodes() {
            const payload = this.uploadResults.filter(zipCode => this.selectedZipCodes[zipCode.id]);
            this.$emit('addZipCodesFromModal', payload, this.uploadOptions.append);
        },
        closeModal() {
            this.$emit('hideZipUploadModal');
        },
        updateUploadOption(option, checked) {
            switch (option) {
                case 'append':
                    this.uploadOptions.append = checked;
                    this.uploadOptions.replace = !checked;
                    break
                case 'replace':
                    this.uploadOptions.replace = checked;
                    this.uploadOptions.append = !checked;
                    break;
            }
        },
    },
    computed: {
        allowSave() {
            return (Object.keys(this.selectedZipCodes).filter(zip => this.selectedZipCodes[zip])).length > 0
        }
    }
}
</script>
