<template>
    <div :class="baseStyle">
        <button class="w-5 h-5 rounded-full  border-2 cursor-pointer"
            :class="[modelValue <= min ? 'grayscale-[60%] opacity-50 cursor-default' : '',
                darkMode ? 'text-red-500 border-red-500' : 'text-red-700 border-red-700'
            ]"
            @click="spinnerDown"
            @mousedown="holdSpinnerDown"
            :disabled="modelValue <= min"
        >
            <svg class="w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2.5" stroke="currentColor" >
                <path stroke-linecap="round" stroke-linejoin="round" d="M18 12H6" />
            </svg>
        </button>
        <div class="flex justify-center items-center mx-4 h-6">
            <span v-if="prefix" class="cursor-default">
                {{ prefix }}
            </span>
            <input
                class="w-[5ch] text-center bg-transparent border-0 font-normal p-0"
                :class="[darkMode ? 'text-slate-100' : 'text-slate-900']"
                :value="modelValue"
                @input="$emit('update:modelValue', isNaN($event.target.value) || $event.target.value < min ? min : +$event.target.value)"
                :disabled="disabled"
            >
            <span  class="cursor-default">
                {{ suffix }}
            </span>
        </div>
        <button class="w-5 h-5 rounded-full border-2 cursor-pointer"
            :class="[max > 0 && modelValue >= max ? 'grayscale-[60%] opacity-50 cursor-default' : '',
                darkMode ? 'border-green-500 text-green-500' : 'border-green-700 text-green-700']"
            @click="spinnerUp"
            :disabled="max > 0 && modelValue >= max"
            @mousedown="holdSpinnerUp"
        >
            <svg  class="w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
            </svg>
        </button>
    </div>
</template>

<script setup>
import { ref } from "vue";

const props = defineProps({
    modelValue: {
        type: Number,
        default: 0,
    },
    prefix: {
        type: String,
        default: "$",
    },
    suffix: {
        type: String,
        default: null,
    },
    numberStep: {
        type: Number,
        default: 1,
    },
    min: {
        type: Number,
        default: null
    },
    max: {
        type: Number,
        default: null,
    },
    darkMode: {
        type: Boolean,
        default: false,
    },
    holdDelay: {
        type: Number,
        default: 250,
    },
    holdSpeed: {
        type: Number,
        default: 50,
    },
    baseStyle: {
        type: String,
        default: 'flex justify-center items-center'
    },
    disabled: {
        type: Boolean,
        default: true
    }
});

const emit = defineEmits([ 'update:modelValue' ]);

const holdingSpinnerDown = ref(false);
const holdingSpinnerUp = ref(false);

const holdIntervalInstance = ref(null);

const updateModel = (newValue) => {
    emit('update:modelValue', newValue);
}

const spinnerUp = () => {
    const newValue = props.max == null
        ? props.modelValue + props.numberStep
        : Math.min(props.modelValue + props.numberStep, props.max);
    updateModel(newValue);
}

const spinnerDown = () => {
    const newValue = props.min == null
        ? props.modelValue - props.numberStep
        : Math.max(props.modelValue - props.numberStep, props.min);
    updateModel(newValue);
}

const holdSpinnerDown = async () => {
    holdingSpinnerDown.value = true;
    document.addEventListener('mouseup', releaseSpinnerDown);

    await new Promise(res => setTimeout(res, props.holdDelay));
    if (!holdingSpinnerDown.value)
        return;

    holdIntervalInstance.value = setInterval(() => {
        if (holdingSpinnerDown.value && props.modelValue > (props.min ?? 0)) {
            spinnerDown();
        }
        else {
            clearInterval(holdIntervalInstance.value);
            holdIntervalInstance.value = null;
        }
    }, props.holdSpeed);
}

const releaseSpinnerDown = () => {
    holdingSpinnerDown.value = false;
    document.removeEventListener('mouseup', releaseSpinnerDown);
}

const holdSpinnerUp = async () => {
    holdingSpinnerUp.value = true;
    document.addEventListener('mouseup', releaseSpinnerUp);

    await new Promise(res => setTimeout(res, props.holdDelay));
    if (!holdingSpinnerUp.value)
        return;

    holdIntervalInstance.value = setInterval(() => {
        if (holdingSpinnerUp.value && (props.max == null || props.modelValue < props.max)) {
            spinnerUp();
        }
        else {
            clearInterval(holdIntervalInstance.value);
            holdIntervalInstance.value = null;
        }
    }, props.holdSpeed);
}

const releaseSpinnerUp = () => {
    holdingSpinnerUp.value = false;
    document.removeEventListener('mouseup', releaseSpinnerUp);
}

</script>
