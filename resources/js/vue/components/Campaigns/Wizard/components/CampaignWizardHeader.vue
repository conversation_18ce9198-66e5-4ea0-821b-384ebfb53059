<template>
    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-5 md:gap-12">
        <CustomInput
            :dark-mode="darkMode"
            :label="wizardStore.getInputName('name', ReservedComponent.Header)"
            :model-value="headerInputs.name"
            @update:model-value="(newValue) => handleInputUpdate(newValue, 'name')"
            placeholder="My Campaign..."
        />
        <div class="xl:col-span-2">
            <p class="block mb-3 text-sm font-medium">{{ wizardStore.getInputName('property_types', ReservedComponent.Header) }}</p>
            <div class="flex gap-3 flex-wrap">
                <label v-for="name in Object.keys(propertyTypeInputs)"
                       class="inline-flex items-center mr-4"
                >
                    <CustomCheckbox
                        :dark-mode="darkMode"
                        :model-value="propertyTypeInputs[name]"
                        @update:model-value="(newValue) => updatePropertyTypes(newValue, name)"
                        :input-disabled="propertyTypeInputs[name] && selectedInputs.length === 1"
                    />
                    <span class="ml-2">{{ name }}</span>
                </label>
            </div>
        </div>
    </div>
</template>

<script>

import { ReservedComponent, useWizardStore } from "../../../Shared/SlideWizard/stores/wizard.js";
import { useProductConfigurationStore } from "../stores/product-configuration.js";
import CustomInput from "../../../Shared/components/CustomInput.vue";
import CustomCheckbox from "../../../Shared/SlideWizard/components/CustomCheckbox.vue";

export default {
    name: 'CampaignWizardHeader',
    components: {
        CustomCheckbox,
        CustomInput

    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        initialHeaderData: {
            type: Object,
            default: {},
        },
    },
    emits: ['update:headerInput'],
    data() {
        return {
            wizardStore: useWizardStore(),
            productConfigurationStore: useProductConfigurationStore(),

            propertyTypeInputs: {},

            headerInputs: {
                name: '',
                property_types: [],
            },
            ReservedComponent,
        }
    },
    computed: {
        selectedInputs() {
            return Object.entries(this.propertyTypeInputs).reduce((output, [ key, checked ]) => {
                return checked
                    ? [ ...output, key ]
                    : output;
            }, []);
        },
    },
    mounted() {
        Object.assign(this.headerInputs, this.initialHeaderData);

        this.initializePropertyInputs();
    },
    methods: {
        initializePropertyInputs() {
            const types = this.productConfigurationStore.getPropertyTypes();
            types.forEach(type => {
                this.propertyTypeInputs[type] = this.headerInputs.property_types?.includes(type) ?? false;
            });
            if (!this.headerInputs.property_types.length)
                this.setDefaultPropertyType();

            this.handleInputUpdate(this.selectedInputs, 'property_types');
        },
        setDefaultPropertyType() {
            this.propertyTypeInputs[this.productConfigurationStore.getPropertyTypes()[0] ?? null] = true;
        },
        updatePropertyTypes(checked, propertyKey) {
            this.propertyTypeInputs[propertyKey] = checked;

            this.headerInputs.property_types = this.selectedInputs;
            this.handleInputUpdate(this.headerInputs.property_types, 'property_types');
        },
        handleInputUpdate(newValue, inputKey) {
            if (inputKey in this.headerInputs) {
                this.headerInputs[inputKey] = newValue;
                this.$emit('update:headerInput', newValue, inputKey);
            }
        }
    }
}
</script>
