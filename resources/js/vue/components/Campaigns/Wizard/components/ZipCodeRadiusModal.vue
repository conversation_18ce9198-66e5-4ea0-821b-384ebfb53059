<template>
    <div>
        <Modal
            :small="true"
            :dark-mode="darkMode"
            @close="closeModal"
            @confirm="addZipCodes"
            :close-text="'Cancel'"
            :confirm-text="'Add'"
            :class="saving && ['pointer-events-none']"
        >
            <template v-slot:header>
                <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 6.75V15m6-6v8.25m.503 3.498l4.875-2.437c.381-.19.622-.58.622-1.006V4.82c0-.836-.88-1.38-1.628-1.006l-3.869 1.934c-.317.159-.69.159-1.006 0L9.503 3.252a1.125 1.125 0 00-1.006 0L3.622 5.689C3.24 5.88 3 6.27 3 6.695V19.18c0 .836.88 1.38 1.628 1.006l3.869-1.934c.317-.159.69-.159 1.006 0l4.994 2.497c.317.158.69.158 1.006 0z" />
                    </svg>
                    <h5 class="text-md ml-2">Add Zip Codes by Radius</h5>
                </div>
            </template>
            <template v-slot:content>
                <div v-if="loading">
                    <p class="text-gray-500 text-center">Loading...</p>
                </div>
                <div :class="[ loading ? 'hidden' : '', searching ? 'pointer-events-none opacity-50' : '' ]">
                    <div class="pb-4 border-b border-slate-300 mb-4">
                        <div class="flex items-end gap-x-6">
                            <CustomInput
                                type="number"
                                v-model="radius"
                                label="Radius (miles)"
                                :dark-mode="darkMode"
                            />
                            <CustomInput
                                v-model="centralZip"
                                label="Central Zip Code"
                                :dark-mode="darkMode"
                            />
                            <CustomButton
                                @click="submitSearch"
                                classes="py-2.5 px-[4rem]"
                                :dark-mode="darkMode"
                            >
                                Search
                            </CustomButton>
                        </div>
                        <div v-if="errorMessage" class="pt-4">
                            <p class="text-red-900 text-center whitespace-pre">{{ errorMessage }}</p>
                        </div>
                    </div>
                    <div v-if="googleMapsError" class="py-8">
                        <p class="text-red-800 text-center">{{ googleMapsError }}</p>
                    </div>
                    <div class="flex gap-x-4">
                        <div class="google-map-container w-full h-[30rem]">
                            <div ref="mapElement" class="w-full h-full"></div>
                        </div>
                        <div v-if="searchResults?.length" class="w-[100%] max-h-[30rem] overflow-y-auto">
                            <div class="grid justify-center text-center grid-cols-3 text-sm text-gray-700 capitalize bg-cyan-25 font-bold py-2 px-6 items-center border-b border-gray-200">
                                <div class="flex items-center justify-self-center">
                                    <input type="checkbox"
                                       class="form-checkbox h-4 w-4 text-primary-500 rounded"
                                       @change="({ target }) => selectAllZipCodes(target.checked)"
                                    />
                                    <p class="ml-4">Selected</p>
                                </div>
                                <p>Zip Code</p>
                                <p>City</p>
                            </div>
                            <div v-for="zipCode in searchResults"
                                 :key="zipCode.id"
                                 class="text-center relative border-b odd:bg-gray-50 text-gray-600 items-center grid md:grid-cols-3 gap-4 md:gap-0 py-4 px-4 md:px-6"
                            >
                                <div class="flex text-sm justify-self-center">
                                    <input type="checkbox"
                                       class="form-checkbox h-4 w-4 text-primary-500 rounded"
                                       v-model="selectedZipCodes[zipCode.id]"
                                    />
                                </div>
                                <p class="text-sm">{{ zipCode.zip_code }}</p>
                                <p class="text-sm">{{ zipCode.city_name }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </Modal>
    </div>
</template>

<script>
import Modal from "../../../Shared/components/Modal.vue";
import { useLocalityDataStore } from "../stores/locality-data.js";
import CustomInput from "../../../Shared/components/CustomInput.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import NestedCheckboxSelectNavigation from "../../../Shared/SlideWizard/components/NestedCheckboxSelectNavigation.vue";

export default {
    name: 'ZipCodeRadiusModal',
    components: {
        NestedCheckboxSelectNavigation,
        CustomButton,
        CustomInput,
        Modal
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        initialLat: {
            type: Number,
            default: 40.6047217
        },
        initialLng: {
            type: Number,
            default: -122.35249
        },
        saving: {
            type: Boolean,
            default: false,
        }
    },
    emits: [
        'hideZipRadiusModal',
        'addZipCodesFromModal'
    ],
    data() {
        return {
            localityStore: useLocalityDataStore(),
            loading: false,
            searching: false,
            selectedZipCodes: {},

            googleMapsError: null,
            errorMessage: null,

            radius: null,
            centralZip: null,

            searchResults: [],

            googleMapInstance: null,
            mapRadiusCircle: null,
            mapMarker: null,
        }
    },
    mounted() {
        this.initialize();
    },
    methods: {
        async initialize() {
            this.loading = true;
            if (this.googleMapsLoaded()) {
                await this.loadMapElement();
            } else {
                this.googleMapsError = `The was an error loading Google Maps.`;
            }
            this.loading = false;
        },
        googleMapsLoaded() {
            return !!window.google?.maps;
        },
        async loadMapElement() {
            this.googleMapInstance = new window.google.maps.Map(this.$refs.mapElement, {
                center: {
                    lat: this.initialLat,
                    lng: this.initialLng,
                },
                zoom: 10,
            });
        },
        clearMapMarkers() {
            this.mapMarker = null;
            this.mapRadiusCircle = null;
        },
        async asyncGeocode(geoCoder, searchValue) {
            return new Promise(res => {
                geoCoder.geocode?.({ address: searchValue }, (results, status) => {
                    res({ results, status });
                });
            });
        },
        async showRadius() {
            if (!this.googleMapsLoaded() || !this.googleMapInstance) return;
            const geoCoder = new window.google.maps.Geocoder();
            const { results } = await this.asyncGeocode(geoCoder, this.centralZip);
            if (results) {
                this.clearMapMarkers();
                this.googleMapInstance.setCenter(results[0].geometry.location);

                this.mapMarker = new window.google.maps.Marker({
                    position: results[0].geometry.location,
                    map: this.googleMapInstance
                });
                this.mapRadiusCircle = new window.google.maps.Circle({
                    strokeColor: '#0082CC',
                    strokeOpacity: 0.8,
                    strokeWeight: 2,
                    fillColor: '#00A3FF',
                    fillOpacity: 0.35,
                    map: this.googleMapInstance,
                    center: results[0].geometry.location,
                    radius: this.radius * 1609.344
                });

                this.mapRadiusCircle.bindTo('center', this.mapMarker, 'position');
            }
        },
        async submitSearch() {
            this.errorMessage = null;
            if (!this.validateSearch()) return;
            this.showRadius();
            this.searching = true;
            const { status, message, zip_codes } = await this.localityStore.getZipCodesByRadius(this.centralZip, this.radius);
            if (!status) {
                this.errorMessage = message;
            }
            else {
                zip_codes.forEach(zipCode => {
                    this.selectedZipCodes[zipCode.id] = false
                });
                this.searchResults = zip_codes;
            }
            this.searching = false;
        },
        validateSearch() {
            const errors = [];
            const zipNumbers = `${this.centralZip}`.replace(/\D/g, '');
            if (!(this.radius > 0)) errors.push(`Please enter a number for the radius`);
            if (zipNumbers.length !== 5) errors.push(`Please enter a valid zip code`);
            if (errors) this.errorMessage = errors.join('\n');
            return !errors.length;
        },
        addZipCodes() {
            if (!(Object.values(this.selectedZipCodes).filter(v=>v).length)) {
                this.errorMessage = "No zip codes are selected!"
            }
            else {
                const payload = this.searchResults.filter(zipCode => this.selectedZipCodes[zipCode.id]);
                this.$emit('addZipCodesFromModal', payload);
            }
        },
        selectAllZipCodes(checked) {
            for (const id in this.selectedZipCodes) {
                this.selectedZipCodes[id] = !!checked;
            }
        },
        clearData() {
            this.selectedZipCodes = [];
        },
        closeModal() {
            this.mapElement = null;
            this.clearData();
            this.$emit('hideZipRadiusModal');
        },
    },
}





</script>
