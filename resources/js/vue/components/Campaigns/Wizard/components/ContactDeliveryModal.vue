<template>
    <div>
        <Modal
            :dark-mode="darkMode"
            @confirm="addContactDeliveries"
            @close="closeModal"
            confirm-text="Add Selected"
        >
            <template v-slot:header>
                <div class="flex items-center">
                    <svg  class="w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
                    </svg>
                    <h5 class="text-md">Add Contact Delivery</h5>
                </div>
            </template>
            <template v-slot:content>
                <div class="relative min-h-[16rem]"
                     :class="[ darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']"
                >
                    <LoadingSpinner v-if="loading || saving" :dark-mode="darkMode" />
                    <div v-if="!loading" class="overflow-x-hidden">
                        <div class="hidden md:grid md:grid-cols-4 text-xs uppercase px-4 font-bold py-3 items-center border-b"
                            :class="[ darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']"
                        >
                            <div>Select</div>
                            <div>Name</div>
                            <div>Email</div>
                            <div>Cell Phone</div>
                        </div>
                        <div v-for="user in availableContacts"
                             :key="user.id"
                             class="relative border-b text-sm items-start md:items-center grid grid-cols-1 md:grid-cols-4 gap-4 md:gap-0 px-4 py-6 md:py-4 md:px-4"
                             :class="[darkMode ? 'odd:bg-dark-175 border-dark-border' : 'odd:bg-gray-200 border-light-border']"
                        >
                            <div>
                                <p class="font-bold text-sm md:hidden">Select</p>
                                <CustomCheckbox v-if="futureCampaigns" v-model="selectedOdinContacts[user.id]" />
                                <CustomCheckbox v-else v-model="selectedContacts[user.legacy_id]" />
                            </div>
                            <div>
                                <p class="font-bold text-sm md:hidden">Name</p>

                                <p>{{ user.first_name }} {{user.last_name}}</p>
                            </div>
                            <div>
                                <p class="font-bold text-sm md:hidden">Email</p>

                                <p>{{ user.email }}</p>
                            </div>
                            <div>
                                <p class="font-bold text-sm md:hidden">Cell Phone</p>

                                <p>{{ user.cell_phone }}</p>
                            </div>
                        </div>
                        <div class="mt-4">
                            <CustomButton
                                class="mb-4"
                                @click="addNewContact"
                            >
                                Create New Contact
                            </CustomButton>
                        </div>
                    </div>
                </div>
            </template>
        </Modal>

        <!--    Add contact modal    -->
        <CreateEditContactModal
            :dark-mode="darkMode"
            :company-id="campaignStore.companyScope"
            v-if="showAddContactModal"
            :show-modal="true"
            @close:modal="closeAddUserModal"
            @get:contacts="reloadContacts"
        />
    </div>
</template>

<script>
import { useCompanyUsersStore } from "../stores/company-users.js";
import Modal from "../../../Shared/components/Modal.vue";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import CustomCheckbox from "../../../Shared/SlideWizard/components/CustomCheckbox.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import CreateEditContactModal from "../../../Shared/modules/Contacts/CreateEditContactModal.vue";
import { useFutureCampaignStore } from "../stores/future-campaigns.js";
import { useWizardStore } from "../../../Shared/SlideWizard/stores/wizard.js";

export default {
    name: 'ContactDeliveryModal',
    components: {
        CreateEditContactModal,
        CustomButton,
        CustomCheckbox,
        LoadingSpinner,
        Modal
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        futureCampaigns: {
            type: Boolean,
            default: false,
        },
        dataKey: {
            type: String,
            default: '',
        }
    },
    data() {
        return {
            loading: true,
            saving: false,

            showAddContactModal: false,

            availableContacts: [],
            selectedContacts: {},
            selectedOdinContacts: {},

            companyUserStore: useCompanyUsersStore(),
            campaignStore: useFutureCampaignStore(),
            wizardStore: useWizardStore(),
        }
    },
    emits: [
        'hideContactDeliveryModal',
        'update:deliveries',
    ],
    mounted() {
        this.initialize();
    },
    methods: {
        async initialize() {
            await this.companyUserStore.initialize(true);
            this.updateAvailableContacts();
            this.loading = false;
        },
        updateAvailableContacts() {
            this.availableContacts = [];
            this.selectedContacts = {};
            this.selectedOdinContacts = {};
            const currentLegacyIds = this.campaignStore.editingCampaign.delivery?.contact_deliveries?.map(delivery => delivery.id ?? null).filter(v=>v);
            const currentContactIds = (this.wizardStore.fetchInputValue('delivery', 'contact_deliveries') ?? [])
                ?.map(delivery => delivery.contact_id)
                .filter(v=>v);

            this.companyUserStore.companyUsers.forEach(user => {
                if (this.futureCampaigns) {
                    if (!currentContactIds.includes(user.id) && (user.email || user.cell_phone || user.office_phone)) {
                        this.availableContacts.push(user);
                        this.selectedOdinContacts[user.id] = false;
                    }
                }
                else {
                    if (user.is_contact
                        && user.legacy_id
                        && !currentLegacyIds.includes(user.id)
                        && (user.email || user.cell_phone)
                    ) {
                        this.availableContacts.push(user);
                        this.selectedContacts[user.legacy_id] = false;
                    }
                }
            });
        },
        async addContactDeliveries() {
            if (this.futureCampaigns && Object.keys(this.selectedOdinContacts)?.length) {
                this.saving = true;
                const payload = Object.entries(this.selectedOdinContacts).reduce((output, [ userId, checked ]) => {
                    if (checked) {
                        const contact = this.companyUserStore.companyUsers.find(user => userId && parseInt(user.id) === parseInt(userId));
                        if (contact) {
                            output.push({
                                contact_id: contact.id,
                                name: `${contact.first_name} ${contact.last_name}`,
                                email: contact.email,
                                cell_phone: contact.cell_phone,
                                sms_active: false,
                                email_active: false,
                                new: true,
                            });
                        }
                    }
                    return output;
                }, []);

                this.selectedOdinContacts = {};
                this.$emit('update:deliveries', payload, this.dataKey);
            }
            else if (Object.keys(this.selectedContacts).length) {
                this.saving = true;
                Object.keys(this.selectedContacts).forEach(legacyId => {
                    const contact = this.companyUserStore.companyUsers.find(user => legacyId && parseInt(user.legacy_id) === parseInt(legacyId));
                    if (contact) {
                        this.campaignStore.editingCampaign.delivery.contact_deliveries.push({
                            id: contact.id,
                            legacy_id: legacyId,
                            name: `${contact.first_name} ${contact.last_name}`,
                            email: contact.email,
                            cell_phone: contact.cell_phone,
                            sms_active: false,
                            email_active: false,
                            new: true,
                        });
                    }
                });
                this.selectedContacts = {};
            }
            this.updateAvailableContacts();
            this.saving = false;
            this.$emit('hideContactDeliveryModal');
        },
        addNewContact() {
            this.companyUserStore.editingUser.user_type = 'contact';
            this.companyUserStore.editingUser.is_contact = 1;
            this.showAddContactModal = true;
        },
        async closeAddUserModal() {
            this.loading = true;
            this.showAddContactModal = false;
            this.updateAvailableContacts();
            this.loading = false;
        },
        closeModal() {
            this.$emit('hideContactDeliveryModal');
        },
        async reloadContacts() {
            this.loading = true;
            this.companyUserStore.initialize(true).catch(e => e)
                .finally(() => {
                    this.closeAddUserModal();
                    this.updateAvailableContacts();
                    this.loading = false;
                });
        },
    }
}



</script>
