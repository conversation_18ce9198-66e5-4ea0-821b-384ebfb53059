<template>
    <div
        :class="[darkMode
            ? 'bg-dark-module text-slate-100 '
            : 'bg-light-module text-slate-900 ']"
    >
        <LoadingSpinner v-if="loading || saving" :dark-mode="darkMode" />
        <div v-if="!loading" class="gap-y-3 flex flex-col justify-center"
             :class="saving ? 'pointer-events-none opacity-50' : ''"
        >
            <div class="flex gap-x-4 items-center text-sm">
                <div class="w-64">
                    <p class="pb-1">State</p>
                    <Dropdown
                        :options="stateOptions"
                        v-model="selectedState"
                        @change="(_newValue, oldValue) => getCountyOptions(oldValue)"
                        :dark-mode="darkMode"
                    />
                </div>
                <div class="w-64">
                    <p class="pb-1">County</p>
                    <Dropdown
                        :options="countyOptions"
                        v-model="selectedCounty"
                        @change="getPricingSetup"
                        :dark-mode="darkMode"
                    />
                </div>
                <div class="w-64">
                    <p class="pb-1">Property Type</p>
                    <Dropdown
                        :options="propertyTypeOptions"
                        :dropdown-disabled="propertyTypeOptions.length < 2"
                        v-model="selectedPropertyType"
                        @change="getPricingSetup"
                        :dark-mode="darkMode"
                    />
                </div>
            </div>
            <!--     Active bidding counties display for campaign bidding      -->
            <div v-if="!selectedCounty && !customFloorPricing" class="mt-2">
                <div v-if="campaignStore.customFloorPricingActive">
                    <p class="whitespace-pre-line">
                        Note: This campaign is using custom floor pricing. Unlike normal pricing, the default State price will not enforce a limit on County prices.
                        <br>Both State and County bids may be set as low as their respective custom floor price.
                    </p>
                    <p v-if="biddingStore.scopedActiveBidCounties?.length" class="mt-4">
                        <span class="text-primary-500">Counties with explicit bids set</span>
                        <span class="italic"> - {{ activeBidCountyDisplay }}</span>
                    </p>
                </div>
                <div v-else>
                    <p class="whitespace-pre-line">
                        Note: Setting a State-level bid will raise your minimum bid price in all Counties in that State. Any County with their bid price set higher than the State bid will retain their higher price.
                        <br>You can always lower your State bid at a later time - only Counties with a higher set bid price will remain at a higher rate.
                    </p>
                    <p v-if="biddingStore.scopedActiveBidCounties?.length" class="mt-4">
                        <span class="text-primary-500">Counties with bids higher than State default</span>
                        <span class="italic"> - {{ activeBidCountyDisplay }}</span>
                    </p>
                </div>
            </div>
            <!--     Active bidding counties display for custom floor pricing      -->
            <div v-else-if="customFloorPricing" class="mt-2">
                <div v-if="biddingStore.scopedActiveBidCounties?.length || biddingStore.stateHasActiveBids" class="mt-4">
                    <div class="whitespace-pre-line mb-4">
                        <p class="bold">Note: This Campaign has active bids in this State.</p>
                        <p>If lower floor prices are saved, the Campaign's bids may need to be lowered to take advantage of the new prices.</p>
                    </div>
                    <span class="text-primary-500">Locations which may be affected</span>
                    <span class="italic"> - {{ activeBidCountyDisplay }}</span>
                </div>
            </div>

            <div class="justify-center gap-y-12">
                <div class="relative" v-if="!loading && Object.keys(activePrices).length">
                    <div
                        v-for="[ qualityTier, saleType ] in Object.entries(activePrices)"
                        class="mb-8"
                        :key="qualityTier"
                    >
                        <div class="flex items-center gap-x-2">
                            <h4 class="font-semibold py-4">{{ qualityTier }}</h4>
                            <div v-if="!customFloorPricing" class="flex items-center gap-x-2">
                                <Tooltip :dark-mode="darkMode" v-if="tooltips[qualityTier]" class="mx-2">{{ tooltips[qualityTier] }}</Tooltip>
                                <p>Last Month</p>
                            </div>
                        </div>
                        <div v-if="saleType">
                            <div class="text-xs text-center justify-center uppercase font-bold items-center grid max-w-5xl overflow-x-auto divide-x divide-inherit border"
                                 :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border',
                                        customFloorPricing ? 'grid-cols-3' : 'grid-cols-4',
                                 ]"
                            >
                                <div class="py-4">
                                    Type
                                </div>
                                <div v-if="!customFloorPricing"
                                    class="flex items-center justify-center">
                                    <p>Available</p>
                                    <Tooltip :dark-mode="darkMode" class="ml-2">{{ tooltips.Available }}</Tooltip>
                                </div>
                                <div v-else>
                                    Standard Floor
                                </div>
                                <div v-if="!customFloorPricing">Purchased</div>
                                <div>
                                    {{ customFloorPricing ? 'Custom Floor' : 'Bid' }}
                                </div>
                            </div>
                            <div v-for="saleTypeKey in Object.keys(saleType ?? {})"
                                 class="text-sm text-center justify-center font-bold items-center grid max-w-5xl overflow-x-auto divide-x divide-inherit border border-t-0 py-0"
                                 :class="[darkMode ? 'odd:bg-dark-175 border-dark-border' : 'odd:bg-gray-200 border-light-border',
                                            customFloorPricing ? 'grid-cols-3' : 'grid-cols-4',
                                 ]"
                            >
                                <div class="py-4">
                                    {{ $filters.toProperCase(saleTypeKey) }}
                                </div>
                                <div v-if="!customFloorPricing"
                                    :key="`${saleTypeKey}-available`"
                                >
                                    {{ getAvailable(qualityTier, saleTypeKey) }}
                                </div>
                                <!--   Purchased leads    -->
                                <div v-if="!customFloorPricing"
                                    class="flex items-center justify-center"
                                    :key="`${saleTypeKey}-purchased`"
                                >
                                    <p class="inline">{{ getPurchased(qualityTier, saleTypeKey) }}</p>
                                    <p class="inline ml-2">[{{ getPercentagePurchased(qualityTier, saleTypeKey) }}%]</p>
                                </div>
                                <!--   Standard floor price    -->
                                <div v-else
                                     class="flex items-center justify-center"
                                     :key="`${saleTypeKey}-standard-floor`"
                                >
                                    <p class="inline">$ {{ saleType[saleTypeKey].county_floor_price ?? saleType[saleTypeKey].state_floor_price }}</p>
                                </div>
                                <!--   Bid   -->
                                <div :key="`${saleTypeKey}-bid`" class="relative">
                                    <div v-if="saleTypeIsActive(saleTypeKey) || this.customFloorPricing">
                                        <div v-if="customFloorPricing && saleType[saleTypeKey].modified"
                                            class="absolute left-6 top-[50%] translate-y-[-50%] text-primary-500"
                                             title="This value has been modified"
                                        >
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 18 9 11.25l4.306 4.306a11.95 11.95 0 0 1 5.814-5.518l2.74-1.22m0 0-5.94-2.281m5.94 2.28-2.28 5.941" />
                                            </svg>
                                        </div>
                                        <NumberWithSpinners
                                            v-if="customFloorPricing"
                                            :dark-mode="darkMode"
                                            :model-value="saleType[saleTypeKey].custom_floor_price ?? '-'"
                                            :number-step="1"
                                            :min="1"
                                            @update:modelValue="(newValue) => updateFloorPrice(saleType[saleTypeKey], newValue, qualityTier, saleTypeKey)"
                                        />
                                        <NumberWithSpinners
                                            v-else
                                            :dark-mode="darkMode"
                                            :min="saleType[saleTypeKey].minimum_price ?? saleType[saleTypeKey].floor_price ?? '-'"
                                            :model-value="saleType[saleTypeKey].bid_price ?? '-'"
                                            :number-step="5"
                                            @update:modelValue="(newValue) => updateBidPrice(saleType[saleTypeKey], newValue, qualityTier, saleTypeKey)"
                                        />
                                    </div>
                                    <div v-else>
                                        Inactive
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { useLocalityDataStore } from "../stores/locality-data.js";
import { ReservedComponent, useWizardStore } from "../../../Shared/SlideWizard/stores/wizard.js";
import { useProductConfigurationStore } from "../stores/product-configuration.js";
import { defaultPropertyType, useBiddingStore } from "../stores/bidding.js";
import { useFutureCampaignStore } from "../stores/future-campaigns.js";
import { nextTick } from "vue";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import Tooltip from "../../../Shared/components/Tooltip.vue";
import NumberWithSpinners from "./NumberWithSpinners.vue";

const tooltipStore = {
    lead: {
        Standard: `Delivered to a maximum of 4 installers (the owner's choice through the calculator), name, address, phone number, email, etc.`,
        Premium: `When the owner has requested 'appointment times', 'best time to contact', their 'monthly electric spend is over $300', or the lead is phone qualified by our QA team.`,
        Available: `It’s possible for you to change your mix of lead types through bidding. If the customer selects they want bids from 2 installers, we will generally attempt to match them with 2 installers (Duo), but with bidding it’s possible to override this preference and begin winning the Duo leads as Exclusive if your bid is higher than the next 2 Duo bids. `
    },
    appointment: {
        ['In-Home Appointment']: `Tooltip goes here`,
        ['Online Consultation']: `Tooltip goes here`,
        Available: `Tooltip goes here`,
    },
}

export default {
    name: 'BiddingTable',
    components: {
        NumberWithSpinners,
        Tooltip,
        Dropdown,
        LoadingSpinner

    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        initialData: {
            type: Object,
            default: () => ({}),
        },
        customFloorPricing: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['update:biddingInput'],
    data() {
        return {
            localityStore: useLocalityDataStore(),
            wizardStore: useWizardStore(),
            biddingStore: useBiddingStore(),
            productConfiguration: useProductConfigurationStore(),
            campaignStore: useFutureCampaignStore(),

            activePrices: {},

            stateOptions: [],
            countyOptions: [],
            selectedState: null,
            selectedCounty: 0,
            defaultPropertyType: {},
            selectedPropertyType: defaultPropertyType,

            locationBids: {},
            customFloorPrices: {},
            budgetData: {},
            optionalSaleTypes: [],

            loading: false,
            saving: false,
        }
    },
    computed: {
        propertyTypeOptions() {
            const properties = this.campaignStore.fetchModuleInputValue(null, 'property_types') || [];
            if (properties.length && !properties.includes(this.selectedPropertyType)) {
                this.selectedPropertyType = properties[0];
                this.getPricingSetup();
            }
            return properties?.map(item => ({ name: item, id: item })) ?? [];
        },
        selectedStateKey() {
            return this.localityStore.states.find(state => state.id === this.selectedState)?.state_key ?? null;
        },
        selectedCountyKey() {
            return this.selectedCounty === 0
                ? 'state'
                : this.localityStore.countyList[this.selectedStateKey].find(county => county.id === this.selectedCounty)?.county_key ?? null;
        },
        usingCustomFloorPrices() {
            return this.campaignStore.editingCampaign.uses_custom_floor_prices;
        },
        tooltips() {
            if (this.customFloorPricing) return "";
            return tooltipStore[this.campaignStore.productScope] ?? {};
        },
        activeBidCountyDisplay() {
            const counties = this.biddingStore.scopedActiveBidCounties?.map(key => this.$filters.toProperCase(key)) ?? [];
            if (this.customFloorPricing && this.biddingStore.stateHasActiveBids)
                counties.push('State-level bids');

            return counties.join(', ');
        },
    },
    mounted() {
        this.initialize();
    },

    methods: {
        async initialize() {
            this.loading = true;
            this.biddingStore.$reset();
            this.getStateOptions();
            await this.getCountyOptions();
            this.optionalSaleTypes = this.productConfiguration.getOptionalBudgetKeys();
            this.checkPropertyTypeSelected();

            if (this.customFloorPricing) {
                this.initialiseCustomFloorPricing();
                return;
            }

            this.budgetData = this.campaignStore.fetchModuleInputValue('budget', 'budgets');


            Object.assign(this.locationBids, this.initialData ?? {});

            for (const saleType in this.locationBids) {
                this.locationBids[saleType] = this.locationBids[saleType].filter(bid => bid.location_id > 0);
            }
            this.handleInputUpdate();

            this.loadStatistics();

            this.loading = false;
        },
        async initialiseCustomFloorPricing() {
            this.customFloorPrices = this.initialData;

            this.loading = false;
        },
        loadStatistics() {
            if (this.selectedState && this.selectedPropertyType) {
                this.biddingStore.loadBiddingLocationStatistics(this.selectedState, this.selectedCounty, this.selectedPropertyType);
            }
        },
        checkPropertyTypeSelected() {
            const inputs = this.campaignStore.fetchModuleInputValue(ReservedComponent.Header, 'property_types') ?? [];
            if (!inputs?.length) {
                this.wizardStore.setSlideInputValue(ReservedComponent.Header, 'property_types', [defaultPropertyType]);
                this.campaignStore.updateModulePayload(ReservedComponent.Header, [defaultPropertyType]);
            }
        },
        saleTypeIsActive(saleTypeKey) {
            return this.optionalSaleTypes.includes(saleTypeKey)
                ? this.budgetData?.[saleTypeKey]?.status ?? false
                : true;
        },
        async getPricingSetup() {
            await nextTick();
            this.loading = true;
            if (this.selectedState) {
                if (this.customFloorPricing) { // Load data for custom floor prices instead of bids
                    const { status, data, message } = this.selectedCounty
                        ? await this.biddingStore.loadCustomCountyFloorPrices(this.selectedPropertyType, this.selectedStateKey, this.selectedCountyKey)
                        : await this.biddingStore.loadCustomStateFloorPrices(this.selectedPropertyType, this.selectedStateKey);

                    if (!status || !data)
                        console.error(message ?? "An error occurred fetching floor prices");

                    this.activePrices = data;
                } else {
                    const { status, data, message } = this.selectedCounty
                        ? await this.biddingStore.loadCountyFloorPrices(this.selectedPropertyType, this.selectedStateKey, this.selectedCountyKey).catch(e => e)
                        : await this.biddingStore.loadStateFloorPrices(this.selectedPropertyType, this.selectedStateKey).catch(e => e);
                    if (!status || !data)
                        console.error(message ?? "An error occurred fetching floor prices");

                    this.activePrices = data;
                    if (this.selectedCounty) {
                        this.setMinimumBids();
                    }
                }
            }

            this.loading = false;
        },
        getStateOptions() {
            const validStates = Object.values(this.campaignStore.getActiveZipCodes()).reduce((output, zipCode) => {
                return output.includes(zipCode.state_key) ? output : [...output, zipCode.state_key];
            }, []);

            this.stateOptions = validStates.reduce((output, stateKey) => {
                const stateEntry = this.localityStore.states.find(state => state.state_key === stateKey);
                return stateEntry
                    ? [...output, { name: stateEntry.state_name, id: stateEntry.id } ]
                    : output;
            }, []).sort((a, b) => a.name > b.name ? 1 : -1);

            this.selectedState = this.selectedState ?? this.stateOptions[0]?.id ?? null;
        },
        async getCountyOptions(oldValue = null) {
            await nextTick();

            if (oldValue === this.selectedState) return;
            if (!this.selectedStateKey) {
                this.countyOptions = [];
                return;
            }

            const validCounties = Object.values(this.campaignStore.getActiveZipCodes()).reduce((output, zipCode) => {
                return output.includes(zipCode.county_key) || zipCode.state_key !== this.selectedStateKey
                    ? output
                    : [...output, zipCode.county_key];
            }, []);

            this.countyOptions = validCounties.reduce((output, countyKey) => {
                const countyEntry = this.localityStore.countyList[this.selectedStateKey]?.find(county => county.county_key === countyKey);
                return countyEntry
                    ? [...output, { name: countyEntry.county ?? '', id: countyEntry.id }]
                    : output;
            }, []).sort((a, b) => a.name > b.name ? 1 : -1);

            this.countyOptions.unshift({ name: 'Default', id: 0 });
            this.selectedCounty = 0;

            await this.getPricingSetup();
        },
        updateBidPrice(prices, newBid, qualityTier, saleType) {
            prices.bid_price = newBid;
            const locationId = this.selectedCounty || this.selectedState;
            if (!locationId) return;

            this.pushLocationBid({
                bid: newBid,
                location_id: locationId,
                property_type: this.selectedPropertyType,
                quality_tier: qualityTier,
                is_state_bid: this.selectedCounty === 0,
                state_location_id: this.selectedState,
            }, saleType);
        },
        pushLocationBid(newBid, saleType) {
            if (!this.locationBids[saleType])
                this.locationBids[saleType] = [];

            const existingBid = this.locationBids[saleType].find(bid => (
                bid.quality_tier === newBid.quality_tier
                && bid.location_id === newBid.location_id
                && bid.property_type === newBid.property_type
            ));

            if (existingBid)
                Object.assign(existingBid, newBid);
            else
                this.locationBids[saleType].push(newBid);
        },
        // Custom floor price functions
        updateFloorPrice(prices, newPrice, qualityTier, saleType) {
            prices.custom_floor_price = newPrice;
            prices.modified = true;

            const locationId = this.selectedCounty || this.selectedState;
            if (!locationId) return;

            this.pushFloorPrice({
                custom_floor_price: newPrice,
                location_id: locationId,
                property_type: this.selectedPropertyType,
                quality_tier: qualityTier,
                is_state_price: this.selectedCounty === 0,
                state_location_id: this.selectedState,
            }, saleType);
        },
        pushFloorPrice(newPrice, saleType) {
            if (!this.customFloorPrices[saleType])
                this.customFloorPrices[saleType] = [];

            const existingPrice = this.customFloorPrices[saleType].find(price => (
                price.quality_tier === newPrice.quality_tier
                && price.location_id === newPrice.location_id
                && price.property_type === newPrice.property_type
            ));

            if (existingPrice)
                Object.assign(existingPrice, newPrice);
            else
                this.customFloorPrices[saleType].push(newPrice);
        },
        /**
         * This method sets the minimum county bids to
         *      - active state bid, if it is higher than the county floor price
         *      - county floor, if it exists
         *      - state floor otherwise
         * It will also adjust the active bid if required, to keep it valid after the minimum price is calculated
         */
        setMinimumBids() {
            if (!this.selectedStateKey || !this.selectedCounty) return;
            for (const qualityTier in this.activePrices) {
                for (const saleTypeKey in this.activePrices[qualityTier]) {
                    // Custom floor pricing does not enforce state bids as minimum county bids
                    if (this.usingCustomFloorPrices) {
                        this.activePrices[qualityTier][saleTypeKey].minimum_price = this.activePrices[qualityTier][saleTypeKey].floor_price;
                    }
                    else {
                        // If there is an active state bid (above the state floor) that is also higher than the county floor
                        //  set it as the minimum price for the county. Otherwise, the county floor is the minimum (state floor if no county floor exists)
                        const activeStateBid = this.biddingStore.getStateBid(this.selectedStateKey, this.selectedPropertyType, qualityTier, saleTypeKey);
                        this.activePrices[qualityTier][saleTypeKey].minimum_price = Math.max(this.activePrices[qualityTier][saleTypeKey].floor_price, activeStateBid);

                        const liveCountyBid = this.locationBids[saleTypeKey]?.find(locationBid => locationBid.location_id === this.selectedCounty)?.bid ?? 0;
                        const highestStateOrStoredCountyBid = Math.max(activeStateBid ?? 0, this.activePrices[qualityTier][saleTypeKey].initial_bid_price);

                        // Set the current displayed price as the first valid price found from
                        //   - live county bid (user has changed bid)
                        //   - highest value out of any active parent state bid, and the initial stored price (either an explicit county bid, or the county floor, or the state floor)
                        //   - the current price
                        if (liveCountyBid >= this.activePrices[qualityTier][saleTypeKey].minimum_price)
                            this.activePrices[qualityTier][saleTypeKey].bid_price = liveCountyBid
                        else if (highestStateOrStoredCountyBid >= this.activePrices[qualityTier][saleTypeKey].minimum_price)
                            this.activePrices[qualityTier][saleTypeKey].bid_price = highestStateOrStoredCountyBid;
                        else
                            this.activePrices[qualityTier][saleTypeKey].bid_price = Math.max(this.activePrices[qualityTier][saleTypeKey].minimum_price, this.activePrices[qualityTier][saleTypeKey].bid_price);
                    }
                }
            }
        },
        handleInputUpdate() {
            this.$emit('update:biddingInput', this.locationBids);
        },
        getAvailable(qualityTier, saleTypeKey) {
            if (!this.biddingStore.statistics[qualityTier] || !this.biddingStore.statistics[qualityTier][saleTypeKey])
                return 0

            return this.biddingStore.statistics[qualityTier][saleTypeKey]['available'] ?? 0;
        },
        getPurchased(qualityTier, saleTypeKey) {
            if (!this.biddingStore.statistics[qualityTier] || !this.biddingStore.statistics[qualityTier][saleTypeKey])
                return 0

            return this.biddingStore.statistics[qualityTier][saleTypeKey]['purchased'] ?? 0;
        },
        getPercentagePurchased(qualityTier, saleTypeKey) {
            const available = this.getAvailable(qualityTier, saleTypeKey) > 0 ? this.getAvailable(qualityTier, saleTypeKey) : 1;
            const purchased = this.getPurchased(qualityTier, saleTypeKey);

            return ((purchased/available) * 100).toFixed(1);
        },
    },
    watch: {
        locationBids: {
            deep: true,
            handler() {
                this.handleInputUpdate();
            }
        },
        selectedState: {
            deep: true,
            handler() { if (!this.customFloorPricing) this.loadStatistics(); }
        },
        selectedCounty: {
            deep: true,
            handler() { if (!this.customFloorPricing) this.loadStatistics(); }
        },
        selectedPropertyType: {
            deep: true,
            handler() { if (!this.customFloorPricing) this.loadStatistics(); }
        },
    },
    expose: [
        'customFloorPrices',
    ],
}
</script>
