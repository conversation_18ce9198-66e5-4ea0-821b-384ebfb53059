<template>
	<div>
		<Modal
			@confirm="saveCrmDeliverer"
			@close="closeModal"
			:confirm-text="newConfiguration ? 'Create' : 'Update'"
			key="crmDeliveryModal"
            :dark-mode="darkMode"
            :no-scroll="true"
            :restrict-width="!showIntegrationHelp && !showReplacerDocumentation"
            :full-width="false"
		>
			<template v-slot:header>
                <div class="flex items-center">
                    <svg  class="w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
                    </svg>
                    <h5 class="text-md">{{ newConfiguration ? 'Add' : 'Edit'}} {{ editingTemplate ? 'CRM Template' : 'Campaign CRM Delivery' }}</h5>
                </div>
			</template>
			<template v-slot:content>
				<LoadingSpinner v-if="loading" />
				<div v-else
                     class="flex gap-x-6 overflow-y-none"
                    :class="[saving ? 'pointer-events-none grayscale-[50%] opacity-50' : '']"
                >
                    <div class="w-full overflow-y-auto max-h-[65vh] pr-2">
                        <div v-if="editingTemplate"
                             class="pb-4 mb-4 border-b border-gray-200"
                        >
                            <div class="flex items-center gap-x-4 mb-2">
                                <ToggleSwitch
                                    :dark-mode="darkMode"
                                    :small="true"
                                    v-model="useCampaignSync"
                                />
                                <p class="italic">
                                    Quick Sync Campaign Deliveries
                                </p>
                                <Tooltip :large="true"
                                    :dark-mode="darkMode"
                                >
                                    {{ tooltips.sync }}
                                </Tooltip>
                            </div>
                            <div v-if="useCampaignSync" class="mt-4">
                                <div class="flex items-center gap-x-3 pb-2 ml-2">
                                    <CustomCheckbox
                                        :dark-mode="darkMode"
                                        v-model="quickSyncSelectAll"
                                        @update:model-value="toggleQuickSyncSelectAll"
                                    />
                                    <p class="font-semibold uppercase">Select All</p>
                                </div>
                                <div class="border-t border-b max-h-[30vh] overflow-y-auto px-5 py-3 w-[97%] mx-auto"
                                    :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
                                >
                                    <div v-for="campaignOption in campaignOptions"
                                         class="flex items-center gap-x-3 ml-2"
                                    >
                                        <CustomCheckbox
                                            :dark-mode="darkMode"
                                            :model-value="campaignOption.active"
                                            @update:model-value="toggleCampaignActive(campaignOption)"
                                        />
                                        <div>
                                            {{ campaignOption.name }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else-if="deliveryStore.templateHasActiveCampaigns()" class="border-t border-b max-h-[30vh] overflow-y-auto px-5 py-3 w-[97%] mx-auto"
                                 :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
                            >
                                <p class="italic mb-1">This Template is currently in use by these Campaigns:</p>
                                <p class="whitespace-pre-wrap ml-2">{{ deliveryStore.getTemplateCampaignNames().join("\n") }}</p>
                            </div>
                        </div>
                        <div v-else-if="!editingTemplate"
                             class="flex items-start gap-x-4 pb-4 mb-4 border-b border-gray-200 px-5"
                        >
                            <div class="flex items-center gap-2 w-full">
                                <div class="w-full">
                                    <p :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="block mb-1">
                                        Campaign CRM Delivery Source:
                                    </p>
                                    <Dropdown
                                        :dark-mode="darkMode"
                                        :options="crmSourceOptions"
                                        v-model="selectedCrmSource"
                                        @update:model-value="loadTemplateValues"
                                    />
                                </div>
                                <Tooltip :dark-mode="darkMode" :large="true">
                                    <template v-slot:default>
                                        <p class="whitespace-pre-wrap">{{ tooltips.source[selectedCrmSource] }}</p>
                                    </template>
                                </Tooltip>
                            </div>
                            <div class="w-full">
                                <div v-if="selectedCrmSource === CrmSource.FromTemplate">
                                    <p :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="block mb-1">
                                        Create from Template:
                                    </p>
                                    <Dropdown
                                        :dark-mode="darkMode"
                                        :options="crmTemplateOptions"
                                        v-model="selectedCrmTemplate"
                                        @update:model-value="loadTemplateValues"
                                    />
                                </div>
                            </div>
                        </div>
                        <div :class="[(selectedCrmSource === CrmSource.FromTemplate || deliveryStore.editingCrmConfiguration.template_id) && 'pointer-events-none opacity-50']">
                            <div class="flex items-start gap-4 w-full px-3">
                                <div class="w-full">
                                    <CustomInput
                                        :dark-mode="darkMode"
                                        label="* Name"
                                        v-model="deliveryStore.editingCrmConfiguration.display_name"
                                        :placeholder="`${editingTemplate ? 'Template' : 'Delivery'} name...`"
                                    />
                                </div>
                                <div class="w-full">
                                    <p :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="block mb-1">
                                        Crm Integrations
                                    </p>
                                    <Dropdown
                                        class="ml-1"
                                        :dark-mode="darkMode"
                                        :disabled="!newConfiguration"
                                        :options="crmConfigurationOptions"
                                        v-model="deliveryStore.editingCrmConfiguration.crm_type"
                                        @change="deliveryStore.loadDefaultFields()"
                                    />
                                </div>
                            </div>
                            <div class="flex items-start gap-x-4 w-full px-3 mt-3"
                                v-if="newConfiguration && deliveryStore.editingCrmConfiguration.crm_type === CrmType.StandardWebForm"
                            >
                                <div class="w-1/2 pr-3">
                                    <p :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="block mb-1">
                                        Platform Prefill
                                    </p>
                                    <Dropdown
                                        class="ml-1"
                                        :dark-mode="darkMode"
                                        :options="prefillOptions"
                                        v-model="selectedWebformPrefill"
                                        @change="handlePrefillSelection"
                                    />
                                </div>
                            </div>
                            <div v-if="deliveryStore.editingCrmType >= 0"
                                class="overflow-y-none px-3"
                            >
                                <div v-if="deliveryStore.editingCrmConfiguration.payload.system_fields" class="border-t border-gray-500 pt-6 mt-8">
                                    <div class="flex items-start justify-between">
                                        <h2 class="font-semibold pb-3">System Fields</h2>
                                        <div class="flex items-center gap-x-3 cursor-pointer mr-4"
                                             v-if="helpComponent"
                                             @click="toggleIntegrationHelp()"
                                        >
                                            <div class="italic">{{ showIntegrationHelp? 'Hide' : 'Show'}} {{ selectedCrmConfigurationName }} help</div>
                                            <svg class="size-6 text-primary-500 hover:text-primary-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                                                <path fill-rule="evenodd" d="M15.75 1.5a6.75 6.75 0 0 0-6.651 7.906c.067.39-.032.717-.221.906l-6.5 6.499a3 3 0 0 0-.878 2.121v2.818c0 .414.336.75.75.75H6a.75.75 0 0 0 .75-.75v-1.5h1.5A.75.75 0 0 0 9 19.5V18h1.5a.75.75 0 0 0 .53-.22l2.658-2.658c.19-.189.517-.288.906-.22A6.75 6.75 0 1 0 15.75 1.5Zm0 3a.75.75 0 0 0 0 1.5A2.25 2.25 0 0 1 18 8.25a.75.75 0 0 0 1.5 0 3.75 3.75 0 0 0-3.75-3.75Z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="grid grid-cols-2 items-start gap-x-4">
                                        <div v-for="systemField in deliveryStore.editingCrmConfiguration.payload.system_fields"
                                             :key="systemField.key"
                                             class="mb-4"
                                        >
                                            <div v-if="systemField.display_flag">
                                                <div v-if="systemField.type === CrmFieldType.Dropdown">
                                                    <p :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="block mb-1">
                                                        {{ `${systemField.required ? '* ' : ''}${systemField.display_name}` }}
                                                    </p>
                                                    <Dropdown
                                                        :dark-mode="darkMode"
                                                        :options="getCrmDropdownOptions(systemField.payload.options)"
                                                        v-model="systemField.value"
                                                    />
                                                </div>
                                                <CustomInput
                                                    v-else
                                                    :dark-mode="darkMode"
                                                    v-model="systemField.value"
                                                    :label="`${systemField.required ? '* ' : ''}${systemField.display_name}`"
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div v-if="deliveryStore.currentInteractables?.length" class="flex gap-x-4 mt-6">
                                    <div v-for="interactable in deliveryStore.currentInteractables">
                                        <component
                                            v-if="interactable.display_on_front_end"
                                            :is="deliveryStore.getCrmInteractableComponentMap(interactable.type)"
                                            @click="executeInteractable(interactable.method)"
                                        >
                                            {{ interactable.display_name }}
                                        </component>
                                        <div v-if="interactable.description"
                                             class="flex items-center justify-center gap-1 py-2 px-4 rounded font-display text-xs whitespace-pre-line"
                                        >
                                            {{ interactable.description }}
                                        </div>
                                    </div>
                                </div>
                                <div v-if="deliveryStore.usesCustomHeaders"
                                     class="border-t border-gray-500 pt-6 mt-8"
                                >
                                    <h2 class="font-semibold pb-3">Custom Headers</h2>
                                    <div class="grid grid-cols-2 items-start gap-4 gap-y-6">
                                        <div v-for="(customHeader, index) in deliveryStore.editingCrmConfiguration.payload.headers"
                                             :key="index"
                                        >
                                            <div class="grid grid-cols-2 gap-x-2">
                                                <CustomInput
                                                    :dark-mode="darkMode"
                                                    label="Key"
                                                    v-model="customHeader.key"
                                                    @change="validateCustomHeaders(index)"
                                                />
                                                <div class="relative">
                                                    <ShortcodeInputCached
                                                        :dark-mode="darkMode"
                                                        v-model="customHeader.value"
                                                        label="Value"
                                                        open-tag="["
                                                        close-tag="]"
                                                        placeholder=""
                                                        :all-shortcodes="deliveryStore.crmShortcodes"
                                                    />
                                                    <div class="absolute top-[0.25rem] right-2 z-10 cursor-pointer"
                                                         :class="[darkMode ? 'text-red-600 hover:text-red-400' : 'text-red-900 hover:text-red-700']"
                                                         @click="deleteCustomHeader(index)"
                                                         title="Delete Field"
                                                    >
                                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4">
                                                            <path fill-rule="evenodd" d="M16.5 4.478v.227a48.816 48.816 0 013.878.512.75.75 0 11-.256 1.478l-.209-.035-1.005 13.07a3 3 0 01-2.991 2.77H8.084a3 3 0 01-2.991-2.77L4.087 6.66l-.209.035a.75.75 0 01-.256-1.478A48.567 48.567 0 017.5 4.705v-.227c0-1.564 1.213-2.9 2.816-2.951a52.662 52.662 0 013.369 0c1.603.051 2.815 1.387 2.815 2.951zm-6.136-1.452a51.196 51.196 0 013.273 0C14.39 3.05 15 3.684 15 4.478v.113a49.488 49.488 0 00-6 0v-.113c0-.794.609-1.428 1.364-1.452zm-.355 5.945a.75.75 0 10-1.5.058l.347 9a.75.75 0 101.499-.058l-.346-9zm5.48.058a.75.75 0 10-1.498-.058l-.347 9a.75.75 0 001.5.058l.345-9z" clip-rule="evenodd" />
                                                        </svg>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <CustomButton
                                        class="my-4"
                                        color="primary-outline"
                                        @click="addCustomHeader"
                                    >
                                        <p>Add Custom Header</p>
                                    </CustomButton>
                                </div>
                                <div v-if="deliveryStore.editingCrmConfiguration.payload.additional_fields?.length || deliveryStore.hasCachedFields" class="border-t border-gray-500 pt-6 mt-8 overflow-y-visible">
                                    <div class="flex items-center justify-between pb-3">
                                        <div class="flex items-center gap-x-3">
                                            <h2 class="font-semibold">Preset Fields</h2>
                                            <div class="flex items-center ml-12 gap-x-3"
                                                 v-if="deliveryStore.editingCrmType === CrmType.StandardWebForm && (usePresetFields || deliveryStore.hasCachedFields)"
                                            >
                                                <ToggleSwitch
                                                    :dark-mode="darkMode"
                                                    v-model="usePresetFields"
                                                    @update:model-value="handlePresetToggle"
                                                />
                                                <p class="italic">
                                                    Use Preset Fields
                                                </p>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-x-3 cursor-pointer mr-4"
                                            @click="toggleReplacerDocumentation()"
                                        >
                                            <div class="italic">{{ showReplacerDocumentation ? 'Hide' : 'Show'}} field help</div>
                                            <svg class="size-6 text-primary-500 hover:text-primary-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                                                <path fill-rule="evenodd" d="M15.75 1.5a6.75 6.75 0 0 0-6.651 7.906c.067.39-.032.717-.221.906l-6.5 6.499a3 3 0 0 0-.878 2.121v2.818c0 .414.336.75.75.75H6a.75.75 0 0 0 .75-.75v-1.5h1.5A.75.75 0 0 0 9 19.5V18h1.5a.75.75 0 0 0 .53-.22l2.658-2.658c.19-.189.517-.288.906-.22A6.75 6.75 0 1 0 15.75 1.5Zm0 3a.75.75 0 0 0 0 1.5A2.25 2.25 0 0 1 18 8.25a.75.75 0 0 0 1.5 0 3.75 3.75 0 0 0-3.75-3.75Z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="grid grid-cols-2 items-start gap-4 gap-y-6">
                                        <div v-for="additionalField in deliveryStore.editingCrmConfiguration.payload.additional_fields"
                                             :key="additionalField.key"
                                        >
                                            <div v-if="additionalField.display_flag">
                                                <div v-if="additionalField.type === CrmFieldType.Dropdown">
                                                    <div class="flex items-center gap-x-4 mb-1 justify-between px-2">
                                                        <p :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="">
                                                            {{ `${additionalField.required ? '* ' : ''}${additionalField.display_name}` }}
                                                        </p>
                                                        <p :class="[darkMode ? 'text-slate-400' : 'text-slate-600']" class="text-sm">
                                                            key: {{ `${additionalField.key}` }}
                                                        </p>
                                                    </div>
                                                    <Dropdown
                                                        :dark-mode="darkMode"
                                                        placeholder="-"
                                                        :options="(additionalField.payload.options ?? []).map(option => ({ name: option, id: option }))"
                                                        v-model="additionalField.value"
                                                    />
                                                </div>
                                                <div v-else>
                                                    <div class="flex items-center gap-x-4 mb-1 justify-between px-2">
                                                        <p :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="">
                                                            {{ `${additionalField.required ? '* ' : ''}${additionalField.display_name}` }}
                                                        </p>
                                                        <p :class="[darkMode ? 'text-slate-400' : 'text-slate-600']" class="text-sm">
                                                            key: {{ `${additionalField.key}` }}
                                                        </p>
                                                    </div>
                                                    <ShortcodeInputCached
                                                        :dark-mode="darkMode"
                                                        v-model="additionalField.value"
                                                        open-tag="["
                                                        close-tag="]"
                                                        placeholder=""
                                                        :all-shortcodes="deliveryStore.crmShortcodes"
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div v-if="Object.keys(deliveryStore.editingCrmConfiguration.payload.interactable_fields ?? {}).length" class="border-t border-gray-500 pt-6 mt-8 overflow-y-auto">
                                    <div v-for="[interactableGroupName, interactableFieldGroup] in Object.entries(deliveryStore.editingCrmConfiguration.payload.interactable_fields)"
                                        class="pb-6"
                                    >
                                        <h2 class="font-semibold pb-3">{{ $filters.toProperCase(interactableGroupName) }}</h2>
                                        <div class="grid grid-cols-2 items-start gap-4 gap-y-6 px-2">
                                            <div v-for="interactableField in interactableFieldGroup"
                                                 :key="interactableField.key"
                                            >
                                                <div v-if="interactableField.type === CrmFieldType.Dropdown">
                                                    <p :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="block mb-1">
                                                        {{ interactableField.display_name }}
                                                    </p>
                                                    <Dropdown
                                                        :dark-mode="darkMode"
                                                        placeholder="-"
                                                        :options="[{ name: '-', value: null }, ...(interactableField.payload.options ?? []).map(option => ({ name: option, id: option }))]"
                                                        v-model="interactableField.value"
                                                    />
                                                </div>
                                                <ShortcodeInputCached
                                                    v-else
                                                    :dark-mode="darkMode"
                                                    v-model="interactableField.value"
                                                    :label="interactableField.display_name"
                                                    open-tag="["
                                                    close-tag="]"
                                                    placeholder=""
                                                    :all-shortcodes="deliveryStore.crmShortcodes"
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div v-if="deliveryStore.usesJsonFields" class="border-t border-gray-500 pt-6 mt-8">
                                    <h2 class="font-semibold pb-3">Custom JSON keys</h2>
                                    <div class="flex flex-col gap-y-6">
                                        <div v-for="(jsonField, index) in deliveryStore.editingCrmConfiguration.payload.json_fields"
                                             :key="index"
                                        >
                                            <div class="flex items-start gap-x-2">
                                                <CustomInput
                                                    class="w-64"
                                                    :dark-mode="darkMode"
                                                    label="Key"
                                                    v-model="jsonField.key"
                                                    @change="validateJsonKey(index)"
                                                />
                                                <div class="relative w-full">
                                                    <textarea
                                                        class="rounded text-sm font-medium w-full border focus:outline-none outline-none focus:ring-0 focus:border-primary-500 focus:shadow-lg focus:shadow-primary-500/10"
                                                        :class="[darkMode ? 'text-slate-100 bg-dark-background border-dark-border' : 'text-slate-900 bg-light-background border-light-border']"
                                                        v-model="jsonField.value"
                                                        @blur="validateJsonField(index)"
                                                        :name="jsonField.key"
                                                        rows="4"
                                                    />
                                                    <div class="absolute top-[0.25rem] right-4 z-10 cursor-pointer"
                                                         :class="[darkMode ? 'text-red-600 hover:text-red-400' : 'text-red-900 hover:text-red-700']"
                                                         @click="deleteJsonKey(index)"
                                                         title="Delete Field"
                                                    >
                                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4">
                                                            <path fill-rule="evenodd" d="M16.5 4.478v.227a48.816 48.816 0 013.878.512.75.75 0 11-.256 1.478l-.209-.035-1.005 13.07a3 3 0 01-2.991 2.77H8.084a3 3 0 01-2.991-2.77L4.087 6.66l-.209.035a.75.75 0 01-.256-1.478A48.567 48.567 0 017.5 4.705v-.227c0-1.564 1.213-2.9 2.816-2.951a52.662 52.662 0 013.369 0c1.603.051 2.815 1.387 2.815 2.951zm-6.136-1.452a51.196 51.196 0 013.273 0C14.39 3.05 15 3.684 15 4.478v.113a49.488 49.488 0 00-6 0v-.113c0-.794.609-1.428 1.364-1.452zm-.355 5.945a.75.75 0 10-1.5.058l.347 9a.75.75 0 101.499-.058l-.346-9zm5.48.058a.75.75 0 10-1.498-.058l-.347 9a.75.75 0 001.5.058l.345-9z" clip-rule="evenodd" />
                                                        </svg>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <CustomButton
                                        class="my-4"
                                        color="primary-outline"
                                        @click="addJsonKey"
                                    >
                                        <p>Add JSON Key</p>
                                    </CustomButton>
                                </div>
                                <div v-if="deliveryStore.usesCustomFields" class="border-t border-gray-200 pt-6 mt-8">
                                    <div class="flex items-start justify-between">
                                        <h2 class="font-semibold pb-3">Custom Fields</h2>
                                        <div class="flex items-center gap-x-3 cursor-pointer mr-4"
                                             v-if="!usePresetFields"
                                             @click="toggleReplacerDocumentation()"
                                        >
                                            <div class="italic">{{ showReplacerDocumentation ? 'Hide' : 'Show'}} field help</div>
                                            <svg class="size-6 text-primary-500 hover:text-primary-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                                                <path fill-rule="evenodd" d="M15.75 1.5a6.75 6.75 0 0 0-6.651 7.906c.067.39-.032.717-.221.906l-6.5 6.499a3 3 0 0 0-.878 2.121v2.818c0 .414.336.75.75.75H6a.75.75 0 0 0 .75-.75v-1.5h1.5A.75.75 0 0 0 9 19.5V18h1.5a.75.75 0 0 0 .53-.22l2.658-2.658c.19-.189.517-.288.906-.22A6.75 6.75 0 1 0 15.75 1.5Zm0 3a.75.75 0 0 0 0 1.5A2.25 2.25 0 0 1 18 8.25a.75.75 0 0 0 1.5 0 3.75 3.75 0 0 0-3.75-3.75Z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="grid grid-cols-2 items-start gap-4 gap-y-6">
                                        <div v-if="deliveryStore.editingCrmConfiguration.crm_type === CrmType.StandardWebForm && deliveryStore.editingCrmConfiguration.payload.custom_fields?.length === 0 && newConfiguration"
                                             class="inline col-span-2 text-sm my-3 w-[600px] ml-4"
                                        >
                                            Standard WebForm fields can either be added manually with the 'Add Custom Field' button, or prefilled for a target platform using the 'Platform Prefill' dropdown in the top section.
                                            <br>The 'Legacy Admin' prefill option contains the entries that were previously found in the 'Preset Fields' list.
                                        </div>
                                        <div v-for="(customField, index) in deliveryStore.editingCrmConfiguration.payload.custom_fields"
                                             :key="index"
                                        >
                                            <div class="grid grid-cols-2 gap-x-2">
                                                <CustomInput
                                                    :dark-mode="darkMode"
                                                    label="Key"
                                                    v-model="customField.key"
                                                    @change="validateCustomFields(index)"
                                                />
                                                <div class="relative">
                                                    <ShortcodeInputCached
                                                        :dark-mode="darkMode"
                                                        v-model="customField.value"
                                                        label="Value"
                                                        open-tag="["
                                                        close-tag="]"
                                                        placeholder=""
                                                        :all-shortcodes="deliveryStore.crmShortcodes"
                                                    />
                                                    <div class="absolute top-[0.25rem] right-2 z-10 cursor-pointer"
                                                         :class="[darkMode ? 'text-red-600 hover:text-red-400' : 'text-red-900 hover:text-red-700']"
                                                         @click="deleteCustomField(index)"
                                                         title="Delete Field"
                                                    >
                                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4">
                                                            <path fill-rule="evenodd" d="M16.5 4.478v.227a48.816 48.816 0 013.878.512.75.75 0 11-.256 1.478l-.209-.035-1.005 13.07a3 3 0 01-2.991 2.77H8.084a3 3 0 01-2.991-2.77L4.087 6.66l-.209.035a.75.75 0 01-.256-1.478A48.567 48.567 0 017.5 4.705v-.227c0-1.564 1.213-2.9 2.816-2.951a52.662 52.662 0 013.369 0c1.603.051 2.815 1.387 2.815 2.951zm-6.136-1.452a51.196 51.196 0 013.273 0C14.39 3.05 15 3.684 15 4.478v.113a49.488 49.488 0 00-6 0v-.113c0-.794.609-1.428 1.364-1.452zm-.355 5.945a.75.75 0 10-1.5.058l.347 9a.75.75 0 101.499-.058l-.346-9zm5.48.058a.75.75 0 10-1.498-.058l-.347 9a.75.75 0 001.5.058l.345-9z" clip-rule="evenodd" />
                                                        </svg>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <CustomButton
                                        class="my-4"
                                        color="primary-outline"
                                        @click="addCustomField"
                                    >
                                        <p>Add Custom Field</p>
                                    </CustomButton>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-if="showReplacerDocumentation && documentationLoaded"
                         class="w-full py-3"
                    >
                        <div class="py-2 px-3 overflow-y-auto border rounded-lg max-h-[63vh]"
                            :class="[darkMode ? 'text-slate-100 bg-dark-background border-dark-border' : 'text-slate-900 bg-light-background border-light-border']"
                        >
                            <p class="whitespace-pre-line text-sm px-2">{{ deliveryStore.replacerReference.instructions }}</p>
                            <div class="mt-6 mb-3 grid grid-cols-4 w-full gap-x-4 gap-y-2 uppercase font-semibold">
                                <div>Field</div>
                                <div>Replacer Key</div>
                                <div class="col-span-2">Example Data Value</div>
                            </div>
                            <div v-for="replacer in deliveryStore.replacerReference.examples"
                                 class="grid grid-cols-4 w-full gap-x-2 mb-3 text-sm"
                            >
                                <div>{{ replacer.label }}</div>
                                <div class="break-all">[{{ replacer.value }}]</div>
                                <div class="col-span-2 whitespace-pre-line">{{ getExampleText(replacer.example) }}</div>
                            </div>
                        </div>
                    </div>
                    <div v-else-if="showIntegrationHelp && helpComponent"
                         class="w-full py-3"
                    >
                        <component :is="helpComponent" class="py-2 px-3 overflow-y-auto border rounded-lg max-h-[63vh] w-full"
                            :class="[darkMode ? 'text-slate-100 bg-dark-background border-dark-border' : 'text-slate-900 bg-light-background border-light-border']"
                        />
                    </div>
                </div>
			</template>
		</Modal>
        <AlertsContainer :dark-mode="darkMode" :alert-type="alertType" :text="alertText" v-if="alertActive" />
	</div>
</template>

<script>

import Modal from "../../../Shared/components/Modal.vue";
import CustomInput from "../../../Shared/components/CustomInput.vue";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import { CrmFieldType, CrmSource, CrmType, DeliveryType, useDeliveryStore } from "../stores/delivery.js";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import AlertsMixin from "../../../../mixins/alerts-mixin.js";
import AlertsContainer from "../../../Shared/components/AlertsContainer.vue";
import ToggleSwitch from "../../../Shared/components/ToggleSwitch.vue";
import { useFutureCampaignStore } from "../stores/future-campaigns.js";
import { nextTick } from "vue";
import Tooltip from "../../../Shared/components/Tooltip.vue";
import CustomCheckbox from "../../../Shared/SlideWizard/components/CustomCheckbox.vue";
import ShortcodeInputCached from "../../../Shared/components/ShortcodeInputCached.vue";
import ZohoOAuthHelp from "../help/ZohoOAuthHelp.vue";

const integrationHelpMap = {
    [CrmType.ZohoOAuth]: ZohoOAuthHelp,
}

export default {
    name: 'CreateUpdateCrmModal',
    components: {
        ShortcodeInputCached,
        CustomCheckbox,
        Tooltip,
        ToggleSwitch,
        AlertsContainer,
        LoadingSpinner,
        CustomButton,
        Dropdown,
        CustomInput,
        Modal
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        editCrmId: {
            type: Number,
            default: null,
        },
        jsonKey: {
            type: Object,
            default: null,
        }
    },
    emits: [
        'update:delivery',
        'update:template-delivery',
        'close:modal'
    ],
    mixins: [AlertsMixin],
    data() {
        return {
            deliveryStore: useDeliveryStore(),
            campaignStore: useFutureCampaignStore(),

            saving: false,
            loading: true,

            selectedCrmConfiguration: null,

            crmHasCustomFields: false,
            crmCustomFields: [],

            CrmFieldType,
            CrmType,

            usePresetFields: true,

            CrmSource,
            crmSourceOptions: [
                { name: 'Standalone', id: CrmSource.Standalone },
                { name: 'From Template', id: CrmSource.FromTemplate }
            ],
            selectedCrmSource: CrmSource.Standalone,
            selectedCrmTemplate: null,
            useCampaignSync: false,
            showReplacerDocumentation: false,
            showIntegrationHelp: false,
            documentationLoaded: false,
            selectedWebformPrefill: '',

            quickSyncSelectAll: false,

            tooltips: {
                source: {
                    [CrmSource.Standalone]: 'This Campaign CRM Integration is unique to this Campaign, and may be freely edited.',
                    [CrmSource.FromTemplate]: `This Campaign CRM Integration is linked to the CRM Template with the same name. This cannot be edited directly, changes must be made to the parent CRM Template as required.
                    \nChanges to the parent Template will affect ALL Campaigns with an integration linked to the same Template.
                    \nYou may unlink this integration from its parent Template by selecting Standalone from the Delivery Source options.`,

                },
                sync: `This CRM Template will have an active CRM Delivery attached to each selected Campaign, and removed from any Campaign which is de-selected.`
            },
        }
    },
    computed: {
        newConfiguration() {
            return !this.editCrmId;
        },
        crmConfigurationOptions() {
            return [
                { name: '-', id: -1 },
                ...this.deliveryStore.availableCrmConfigurations.map(config => {
                    return { name: config.name, id: config.id }
                }),
            ];
        },
        helpComponent() {
            return integrationHelpMap[this.deliveryStore.editingCrmConfiguration.crm_type] ?? null;
        },
        prefillOptions() {
            return [
                { id: '', name: '-' },
                ...Object.keys(this.deliveryStore.availablePrefills).map(key => ({ id: key, name: key }))
            ];
        },
        editingTemplate() {
            return this.deliveryStore.editingDeliveryType === DeliveryType.Template;
        },
        crmTemplateOptions() {
            return [
                { name: '-', id: 0 },
                ...this.deliveryStore.companyCrmTemplates.map(template => ({
                    name: template.display_name,
                    id: template.id
                })),
            ];
        },
        campaignOptions() {
            return this.campaignStore.getFullCampaignList().map(campaign => {
                const campaignReferences = this.deliveryStore.getCampaignReferences();
                return {
                    name: campaign.name,
                    reference: campaign.reference,
                    active: campaignReferences.includes(campaign.reference),
                }
            });
        },
        selectedCrmConfigurationName() {
            return this.deliveryStore.availableCrmConfigurations.find(crm => crm.id === this.deliveryStore.editingCrmConfiguration.crm_type)?.name ?? 'CRM Integration';
        },
    },
    mounted() {
        this.initialize();
        this.prettifyJson();
    },
    methods: {
        async initialize() {
            if (this.newConfiguration) {
                this.deliveryStore.clearEditingConfiguration(false);
                await this.deliveryStore.loadDefaultFields();
            }
            else {
                await this.deliveryStore.loadDefaultFields(true);
                if (this.deliveryStore.editingCrmConfiguration.template_id) {
                    this.selectedCrmTemplate = this.deliveryStore.editingCrmConfiguration.template_id;
                    this.selectedCrmSource = CrmSource.FromTemplate;
                }
            }
            this.checkAdditionalFieldsShouldDisplay();
            this.setQuickSyncToggleState();

            this.loading = false;
        },
        setQuickSyncToggleState() {
            for (const campaign of this.campaignOptions) {
                if (!campaign.active) {
                    this.quickSyncSelectAll = false;
                    return;
                }
            }

            this.quickSyncSelectAll = true;
        },
        checkAdditionalFieldsShouldDisplay() {
            if (this.deliveryStore.editingCrmType === CrmType.StandardWebForm) {
                const additionalFieldsWithValues = this.deliveryStore.editingCrmConfiguration.payload.additional_fields?.filter(v => v.value);
                if (!additionalFieldsWithValues?.length || this.newConfiguration) {
                    this.usePresetFields = false;
                    this.deliveryStore.toggleAdditionalFields(false, true);
                }
            }
        },
        async executeInteractable(methodName) {
            if (this.saving) return;
            this.saving = true;

            const { status, message, data } = await this.deliveryStore.executeInteractable(methodName, this.editingTemplate);
            if (!status) {
                this.showAlert('error', message ?? "CRM interaction failed.");
            }
            else {
                if (!data || !data.status) {
                    this.showAlert('error', "An error occurred.");
                }
                else {
                    if (data.interactable_fields) {
                        this.deliveryStore.editingCrmConfiguration.payload.interactable_fields = data.interactable_fields;
                    }
                    if (Object.keys(data.system_fields ?? {})?.length) {
                        this.deliveryStore.updateFieldValues(data.system_fields, 'system_fields');
                    }
                    if (Object.keys(data.additional_fields ?? {})?.length) {
                        this.deliveryStore.updateFieldValues(data.additional_fields, 'additional_fields');
                    }
                    if (data.interactables?.length) {
                        this.deliveryStore.editingCrmConfiguration.interactables = data.interactables;
                    }
                }
            }

            this.saving = false;
        },
        getDefaultCustomField() {
            return {
                key: '',
                value: '',
            }
        },
        addCustomField() {
            this.deliveryStore.editingCrmConfiguration.payload.custom_fields = this.deliveryStore.editingCrmConfiguration.payload.custom_fields ?? [];
            this.deliveryStore.editingCrmConfiguration.payload.custom_fields.push(this.getDefaultCustomField());
        },
        addCustomHeader() {
            this.deliveryStore.editingCrmConfiguration.payload.headers = this.deliveryStore.editingCrmConfiguration.payload.headers ?? [];
            this.deliveryStore.editingCrmConfiguration.payload.headers.push(this.getDefaultCustomField());
        },
        addJsonKey() {
            this.deliveryStore.editingCrmConfiguration.payload.json_fields = this.deliveryStore.editingCrmConfiguration.payload.json_fields ?? [];
            this.deliveryStore.editingCrmConfiguration.payload.json_fields.push(this.getDefaultCustomField());
        },
        validateCustomFields(validateIndex) {
            const systemKeys = this.deliveryStore.editingCrmConfiguration.payload.system_fields.map(field => field.key);
            const customKeys = this.deliveryStore.editingCrmConfiguration.payload.custom_fields.map(field => field.key);
            const errors = [];

            const validateField = (customField, fieldIndex) => {
                if (!customField.key)
                    errors.push(`Custom field ${fieldIndex + 1} has no Key name.`);
                else if (systemKeys.includes(customField.key))
                    errors.push(`"${customField.key}" is a System Field key and cannot be used as a Custom Field key.`);
                else {
                    customKeys.forEach((key, index) => {
                        if (key === customField.key && index !== fieldIndex)
                            errors.push(`"${customField.key}" is used as more than one Custom Field key name. Keys must be unique`);
                    });
                }
            }

            if (!validateIndex) {
                for (let i = 0; i < this.deliveryStore.editingCrmConfiguration.payload.custom_fields.length; i++) {
                    validateField(this.deliveryStore.editingCrmConfiguration.payload.custom_fields[i], i);
                }
            }
            else {
                const target = this.deliveryStore.editingCrmConfiguration.payload.custom_fields[validateIndex];
                validateField(target, validateIndex);
            }
            for (let i = 0; i < this.deliveryStore.editingCrmConfiguration.payload.headers?.length; i++) {
                if (!this.validateCustomHeaders(i))
                    return false;
            }

            if (errors.length)
                this.showAlert('error', errors.join("\n"));

            return !errors.length;
        },
        validateCustomHeaders(validateIndex) {
            const errors = [];
            const target = this.deliveryStore.editingCrmConfiguration.payload.headers[validateIndex];
            if (!target.key)
                errors.push("Key must have a value");
            if (/\s/.test(target.key))
                errors.push("Header names must not contain spaces");

            if (errors.length) {
                this.showAlert('error', errors.join("\n"));
                return false
            }

            return true;
        },
        validateJsonKey(validateIndex) {
            this.showAlert('error', '', 1);
            const errors = [];
            const target = this.deliveryStore.editingCrmConfiguration.payload.json_fields[validateIndex];
            if (!target.key)
                errors.push("Key must have a value");

            if (errors.length) {
                this.showAlert('error', errors.join("\n"));
                return false
            }

            return true;
        },
        validateJsonField(validateIndex) {
            const errors = [];
            const target = this.deliveryStore.editingCrmConfiguration.payload.json_fields[validateIndex];
            if (target.value) {
                try {
                    this.prettifyJson();
                    JSON.parse(target.value);
                }
                catch(e) {
                    const message = e?.message ?? '';
                    errors.push(`Invalid JSON content in key: ${target.key}. ${message}`);
                }
            }

            if (errors.length) {
                this.showAlert('error', errors.join("\n"));
                return false
            }

            return true;
        },
        async prettifyJson() {
            await nextTick();
            this.deliveryStore.editingCrmConfiguration.payload.json_fields.forEach((jsonField) => {
                if (jsonField.value) {
                    try {
                        const parsedJson = JSON.parse(jsonField.value);
                        jsonField.value = JSON.stringify(parsedJson, null, 4);
                    } catch {}

                    const rowCount = jsonField.value.split(/\n/g)?.length;
                    const element = document.querySelector(`textarea[name="${jsonField.key}"]`) ?? null;
                    if (element)
                        element.rows = Math.max(rowCount, 4);
                }
            });
        },
        validateRequiredFields() {
            const errors = [];
            const validFieldValue = (inputValue) => {
                return inputValue != null
                    && (typeof(inputValue) === 'number'
                        || !!`${inputValue}`.trim());
            }
            this.deliveryStore.editingCrmConfiguration.payload.system_fields.forEach(field => {
                if (field.required && !validFieldValue(field.value))
                    errors.push(`The System Field "${field.display_name}" must have a value.`);
            });
            this.deliveryStore.editingCrmConfiguration.payload.additional_fields.forEach(field => {
                if (field.required && !validFieldValue(field.value))
                    errors.push(`The Additional Field "${field.display_name}" must have a value.`);
            });
            for (let i = 0; i < this.deliveryStore.editingCrmConfiguration.payload.json_fields?.length; i++) {
                if (!this.validateJsonKey(i) || !this.validateJsonField(i))
                    return false;
            }

            if (errors.length) this.showAlert('error', errors.join("\n"));

            return !errors.length;
        },
        validateCrm() {
            const errors = [];
            if (this.selectedCrmSource === CrmSource.FromTemplate && !this.selectedCrmTemplate)
                errors.push("A Template must be selected when the 'From Template' source is used.");
            if (!this.deliveryStore.editingCrmConfiguration.display_name)
                errors.push("The CRM Configuration must have a name.");
            else if (!this.validateRequiredFields()) return false;
            else if (!this.validateCustomFields()) return false;

            if (errors.length) this.showAlert('error', errors.join("\n"));

            return !errors.length;
        },
        deleteCustomField(index) {
            this.deliveryStore.editingCrmConfiguration.payload.custom_fields.splice(index, 1);
        },
        deleteCustomHeader(index) {
            this.deliveryStore.editingCrmConfiguration.payload.headers.splice(index, 1);
        },
        deleteJsonKey(index) {
            this.deliveryStore.editingCrmConfiguration.payload.json_fields.splice(index, 1);
        },
        async saveCrmDeliverer() {
            if (!this.validateCrm()) return;
            if (this.deliveryStore.editingDeliveryType === DeliveryType.Template) {
                this.saving = true;
                const { status, message, data } = await this.deliveryStore.saveCompanyCrmTemplate(this.useCampaignSync);
                if (status) {
                    this.showAlert('success', "Successfully saved Template.");
                    if (data?.syncSelf != null) {
                        const syncPayload = data.syncSelf
                            ? this.deliveryStore.convertTemplateToDeliverer(null, this.newConfiguration)
                            : this.deliveryStore.editingCrmConfiguration.id;
                        if (syncPayload != null)
                            this.$emit('update:template-delivery', data.syncSelf, syncPayload, this.dataKey);
                    }
                    this.closeModal();
                }
                else
                    this.showAlert('error', message ?? "An error occurred saving the Template");

                this.saving = false;
            }
            else {
                const payload = JSON.parse(JSON.stringify(this.deliveryStore.editingCrmConfiguration));
                payload.crm_type_display = this.deliveryStore.getCrmDisplayNameById(payload.crm_type);

                this.$emit('update:delivery', payload, this.dataKey);
                this.closeModal();
            }
        },
        async loadTemplateValues() {
            await nextTick();
            if (this.selectedCrmTemplate) {
                this.deliveryStore.loadTemplateValues(this.selectedCrmTemplate);
                this.checkAdditionalFieldsShouldDisplay();
            }
            if (this.selectedCrmSource === CrmSource.Standalone) {
                this.deliveryStore.editingCrmConfiguration.template_id = null;
            }
        },
        toggleCampaignActive(campaignOption) {
            if (!('campaigns' in this.deliveryStore.editingCrmConfiguration)) return;
            if (this.deliveryStore.editingCrmConfiguration.campaigns?.includes(campaignOption.reference)) {
                campaignOption.active = false;
                this.deliveryStore.editingCrmConfiguration.campaigns = this.deliveryStore.editingCrmConfiguration.campaigns?.filter(reference => reference !== campaignOption.reference) ?? [];
            }
            else {
                campaignOption.active = true;
                this.deliveryStore.editingCrmConfiguration.campaigns.push(campaignOption.reference);
            }

            this.setQuickSyncToggleState();
        },
        toggleQuickSyncSelectAll(newValue) {
            this.deliveryStore.editingCrmConfiguration.campaigns = newValue
                ? this.campaignOptions.map(campaign => campaign.reference)
                : [];
        },
        getCrmDropdownOptions(options) {
            if (Array.isArray(options))
                return options.map(option => ({ name: option, id: option }));
            else if (typeof(options) === 'object')
                return Object.entries(options ?? {}).reduce((output, [key, value]) => [...output, { name: key, id: value }], []);
            else
                return [];
        },
        closeModal() {
            this.deliveryStore.clearEditingConfiguration();
            this.$emit('close:modal');
        },
        handlePresetToggle(newValue) {
            this.deliveryStore.toggleAdditionalFields(newValue);
        },
        async toggleReplacerDocumentation(show) {
            await this.getReplacerDocumentation();
            if (!this.documentationLoaded)
                return;

            if (show == null)
                this.showReplacerDocumentation = !this.showReplacerDocumentation;
            else
                this.showReplacerDocumentation = !!this.showReplacerDocumentation;

            if (this.showIntegrationHelp)
                this.showIntegrationHelp = false;
        },
        async toggleIntegrationHelp(show) {
            if (show == null)
                this.showIntegrationHelp = !this.showIntegrationHelp;
            else
                this.showIntegrationHelp = !!this.showIntegrationHelp;

            if (this.showReplacerDocumentation)
                this.showReplacerDocumentation = false;
        },
        async getReplacerDocumentation() {
            if (this.loading || this.documentationLoaded)
                return;

            this.loading = true;
            const { status, message } = await this.deliveryStore.getReplacerReference();
            if (status) {
                this.documentationLoaded = true;
            }
            else {
                this.showAlert('error', message);
            }

            this.loading = false;
        },
        getExampleText(example) {
            if (Array.isArray(example)) {
                return example.reduce((output, example) => {
                    return output + `\n- ${example}`;
                }, `Example values:`)
            }
            else
                return example;
        },
        async handlePrefillSelection(newValue) {
            if (newValue?.id) {
                this.deliveryStore.loadStandardWebformPrefillFields(newValue.id);
                this.prettifyJson();
            }
        },
    },
}
</script>
