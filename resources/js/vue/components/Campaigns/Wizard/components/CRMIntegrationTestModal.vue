<template>
    <div>
        <modal :dark-mode="darkMode" @close="closeModal" :hide-confirm="true">
            <template v-slot:header>
                <h5 class="text-md">Test Integration</h5>
            </template>
            <template v-slot:content>
                <alert :alert-type="'info'"
                       :text="'Please ensure the CRM is saved by saving the campaign whenever the CRM is new or has been updated, before sending a test lead.'"
                       :container-classes="'border p-4 rounded-lg max-w-screen-md w-full'"
                       :dark-mode="darkMode"
                >
                </alert>
                <alert :alert-type="'info'"
                       :text="'Fields with detected shortcodes will be filled by the delivery service.\nFields with manual input will be sent as raw text (no shortcode replacement will be performed).'"
                       :container-classes="'border p-4 rounded-lg max-w-screen-md w-full'"
                       class="mt-4"
                       v-if="crmFields.length"
                       :dark-mode="darkMode"
                >
                </alert>
                <div class="grid grid-cols-2 gap-4 mt-4 px-2">
                    <div v-for="field in crmFields">
                        <div class="flex justify-between text-sm mb-1">
                            <p class="ml-1">{{ field.display_name ?? field.key }}</p>
                            <p class="mr-1">Data Key: {{ field.key }}</p>
                        </div>
                        <CustomInput
                            :dark-mode="darkMode"
                            :placeholder="field.shortcode ? `Using shortcode(s): ${field.shortcode}` : null"
                            v-model="field.value"
                        />
                    </div>
                </div>
                <div class="flex items-center gap-4 h-16 mt-5">
                    <custom-button :dark-mode="darkMode" @click="sendTestLead" :disabled="isNaN(crmId)">Send Test Lead</custom-button>
                    <loading-spinner v-if="loading"></loading-spinner>
                    <div class="flex items-center gap-3 ml-5 text-sm" v-if="!loading && testResult">
                        <p>Scroll down for result</p>
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-4">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 13.5 12 21m0 0-7.5-7.5M12 21V3" />
                        </svg>
                    </div>
                </div>
                <div class="mt-5">
                    <div v-if="!loading && testResult" class="border" :class="[displayClasses]">
                        <div class="flex border-b">
                            <p class="flex-none w-32 border-r p-3 text-right">Status</p>
                            <p class="grow p-3 text-sm">{{ testResult.success ? 'Success' : 'Failed' }}</p>
                        </div>
                        <div class="flex border-b">
                            <p class="flex-none w-32 p-3 border-r text-right">Url</p>
                            <p class="grow p-3 text-sm">{{ testResult.url }}</p>
                        </div>
                        <div class="flex border-b">
                            <p class="flex-none w-32 p-3 border-r text-right">Header</p>
                            <p class="grow p-3 text-sm">{{ headersDisplay }}</p>
                        </div>
                        <div class="flex border-b">
                            <p class="flex-none w-32 p-3 border-r text-right">Request</p>
                            <p class="grow p-3 text-sm">{{ testResult.request_body }}</p>
                        </div>
                        <div class="flex">
                            <p class="flex-none w-32 p-3 border-r text-right">Response</p>
                            <p class="grow p-3 text-sm">{{ testResult.response_body }}</p>
                        </div>
                    </div>
                </div>
            </template>
        </modal>
    </div>
</template>

<script>
import Modal from "../../../Shared/components/Modal.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import {useDeliveryStore} from "../stores/delivery.js";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import Alert from "../../../Shared/components/Alert.vue";
import {useFutureCampaignStore} from "../stores/future-campaigns.js";
import CustomInput from "../../../Shared/components/CustomInput.vue";

export default {
    name: "CRMIntegrationTestModal",
    components: {CustomInput, Alert, LoadingSpinner, CustomButton, Modal},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        crmId: {
            type: Number,
            require: true
        }
    },
    data() {
        return {
            deliveryStore: useDeliveryStore(),
            campaignStore: useFutureCampaignStore(),
            crmFields: [],
            loading: false,
            testResult: null
        }
    },
    emits: ['modal:close'],
    created() {
        const crm = this.campaignStore.editingCampaign?.delivery?.crm_deliveries?.find(crm => crm.id === this.crmId);

        if (crm) {
            this.crmFields = [...crm.payload.additional_fields, ...crm.payload.custom_fields]
                .filter(field => !!field.value)
                .map(field => Object.assign({}, field));

            this.crmFields.forEach(field => {
                const shortcodes = Array.from(field.value.matchAll(/\[[\w_]+?]/g));
                const shortcodeText = shortcodes.length
                    ? shortcodes.join(', ')
                    : null;

                field.shortcode = shortcodeText;
                field.value = shortcodeText ? null : field.value;
            });
        }
    },
    methods: {
        closeModal() {
            this.$emit('modal:close');
        },
        sendTestLead() {
            this.loading = true;
            this.testResult = null;
            this.deliveryStore.testCrmIntegration(this.crmId, this.prepareTestData()).then(resp => {
                this.testResult = resp.data.data.test_result;
            }).catch(e => {
                this.testResult = {
                    success: false,
                    response_body: e.message ?? "An error occurred",
                }
            }).finally(() => this.loading = false);
        },
        prepareTestData() {
            return this.crmFields.map(field => {
                return {
                    key: field.key,
                    value: field.value
                }
            }).filter(field => field.value !== null && field.value.length);
        }
    },
    computed: {
        displayClasses () {
            if (!this.testResult) return '';

            if (this.testResult.success) {
                if (this.darkMode)
                    return 'bg-green-900 text-white';
                else
                    return 'bg-green-100 text-grey-900';
            }

            if (this.darkMode)
                return 'bg-red-900 text-white';
            else
                return 'bg-red-100 text-grey-900';
        },
        headersDisplay() {
            if (Array.isArray(this.testResult.headers) && this.testResult.headers.length)
                return this.testResult.headers;
            else if (this.testResult.headers && typeof(this.testResult.headers) === 'object') {
                return Object.entries(this.testResult.headers).map(([key, value]) =>
                    `${key}: ${value}`
                ).join('; ');
            }
            else
                return 'No headers';
        }
    }
}
</script>
