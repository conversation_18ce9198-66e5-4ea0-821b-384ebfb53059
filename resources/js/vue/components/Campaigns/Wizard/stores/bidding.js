import { defineStore } from "pinia";
import { computed, reactive, ref } from "vue";
import { useFutureCampaignStore } from "./future-campaigns.js";

export const QualityTier = {
    Standard: 'Standard',
    Premium: 'Premium',
}

export const SaleType = {
    Exclusive: 'Exclusive',
    Duo: 'Duo',
    Trio: 'Trio',
    Quad: 'Quad',
    EmailOnly: 'Email Only',
    Unverified: 'Unverified',
}

export const SaleTypeKey = {
    Exclusive: 'exclusive',
    Duo: 'duo',
    Trio: 'trio',
    Quad: 'quad',
    EmailOnly: 'email_only',
    Unverified: 'unverified',
}

export const PropertyType = {
    Residential: 'Residential',
    Commercial: 'Commercial',
}
export const defaultPropertyType = PropertyType.Residential;

export const useBiddingStore = defineStore('bidding', () => {
    const campaignStore = useFutureCampaignStore();

    const bidPrices = reactive({});
    const floorPrices = reactive({});

    const activeBidCounties = ref({});
    const activeFilters = reactive({
        propertyType: PropertyType.Residential,
        stateLocationKey: null,
        countyLocationKey: null,
    });

    const statistics = ref({});

    const scopedActiveBidCounties = computed(() => {
        const stateLocationKey = activeFilters.stateLocationKey ?? null;
        return stateLocationKey
            ? (activeBidCounties.value[stateLocationKey] ?? []).filter(key => key !== 'state-bids')
            : [];
    });

    const stateHasActiveBids = computed(() => {
        const stateLocationKey = activeFilters.stateLocationKey ?? null;
        return stateLocationKey
            ? (activeBidCounties.value[stateLocationKey] ?? []).includes('state-bids')
            : false;
    })

    const loadStateFloorPrices = async (propertyType, stateLocationKey) => {
        setActiveFilters(propertyType, stateLocationKey);
        const cached = fetchBidPrices(propertyType, stateLocationKey, 'state');
        if (Object.keys(cached).length) {
            checkActiveBidCounties();

            return { status: true, data: cached };
        }

        const campaignReference = campaignStore.editingCampaign.reference;
        const resp = campaignReference
            ? await campaignStore.apiService.getStateBidPrices(stateLocationKey, campaignReference).catch(e => e)
            : await campaignStore.apiService.getStateFloorPrices(
                stateLocationKey,
                campaignStore.productScope,
                campaignStore.serviceScope,
                campaignStore.editingCampaign.type,
            ).catch(e => e);
        if (resp.data?.data?.status) {
            transformAndStoreBidPrices(resp.data.data.prices);
            activeBidCounties.value[stateLocationKey] = filterActiveCountyKeys(resp.data.data.active_bid_counties ?? []);
            return { status: true, data: fetchBidPrices(propertyType, stateLocationKey, 'state') }
        }
        else
            return transformErrorResponse(resp);
    }

    const loadCountyFloorPrices = async (propertyType, stateLocationKey, countyLocationKey) => {
        setActiveFilters(propertyType, stateLocationKey, countyLocationKey);
        const cached = fetchBidPrices(propertyType, stateLocationKey, countyLocationKey);

        if (Object.keys(cached).length) return { status: true, data: cached };

        const campaignReference = campaignStore.editingCampaign.reference;
        const resp = campaignReference
            ? await campaignStore.apiService.getCountyBidPrices(stateLocationKey, countyLocationKey, campaignReference, propertyType).catch(e => e)
            : await campaignStore.apiService.getCountyFloorPrices(
                stateLocationKey,
                countyLocationKey,
                campaignStore.productScope,
                campaignStore.serviceScope,
                campaignStore.editingCampaign.type,
            ).catch(e => e);
        if (resp.data?.data?.status) {
            transformAndStoreBidPrices(resp.data.data.prices);
            return { status: true, data: fetchBidPrices(propertyType, stateLocationKey, countyLocationKey) }
        }
        else
            return transformErrorResponse(resp);
    }

    /**
     * Custom floor pricing functions
     */
    const loadCustomStateFloorPrices = async (propertyType, stateLocationKey) => {
        setActiveFilters(propertyType, stateLocationKey);

        const cached = fetchFloorPrices(propertyType, stateLocationKey, 'state');
        if (Object.keys(cached).length) return { status: true, data: cached };

        const campaignReference = campaignStore.editingCampaign.reference;
        const resp = await campaignStore.apiService.getCustomStateFloorPrices(stateLocationKey, campaignReference).catch(e => e);
        if (resp.data?.data?.status) {
            transformAndStoreFloorPrices(resp.data.data.prices);
            activeBidCounties.value[stateLocationKey] = filterActiveCountyKeys(resp.data.data.active_bid_counties ?? []);

            return { status: true, data: fetchFloorPrices(propertyType, stateLocationKey, 'state') }
        }
        else
            return transformErrorResponse(resp);
    }

    const loadCustomCountyFloorPrices = async (propertyType, stateLocationKey, countyLocationKey) => {
        setActiveFilters(propertyType, stateLocationKey, countyLocationKey);

        const cached = fetchFloorPrices(propertyType, stateLocationKey, countyLocationKey);
        if (Object.keys(cached).length) return { status: true, data: cached };

        const campaignReference = campaignStore.editingCampaign.reference;
        const resp = await campaignStore.apiService.getCustomCountyFloorPrices(stateLocationKey, countyLocationKey, campaignReference).catch(e => e);
        if (resp.data?.data?.status) {
            transformAndStoreFloorPrices(resp.data.data.prices);
            return { status: true, data: fetchFloorPrices(propertyType, stateLocationKey, countyLocationKey) }
        }
        else
            return transformErrorResponse(resp);
    }

    const fetchBidPrices = (propertyType, stateLocationKey, countyLocationKey) => {
        const productName = campaignStore.getProductLabel(false);
        return bidPrices[productName]?.[propertyType]?.[stateLocationKey]?.[countyLocationKey] ?? {};
    }

    const fetchFloorPrices = (propertyType, stateLocationKey, countyLocationKey) => {
        return floorPrices[propertyType]?.[stateLocationKey]?.[countyLocationKey] ?? {};
    }

    const loadBiddingLocationStatistics = (stateLocationId, countyLocationId, propertyType) => {
        const params = {
            company_id: campaignStore.companyScope,
            service: campaignStore.serviceScope,
            product: campaignStore.productScope,
            state_location_id: stateLocationId,
            county_location_id: countyLocationId || null,
            property_type: propertyType,
        };

        campaignStore.apiService.getStatisticsByLocation(params)
            .then(resp => statistics.value = resp.data.data?.statistics ?? {})
            .catch(e => transformErrorResponse(e))
    };

    const transformAndStoreBidPrices = (prices) => {
        for (const productName in prices) {
            bidPrices[productName] = bidPrices[productName] ?? {};
            for (const propertyType in prices[productName]) {
                bidPrices[productName][propertyType] = bidPrices[productName][propertyType] ?? {};
                for (const stateKey in prices[productName][propertyType]) {
                    bidPrices[productName][propertyType][stateKey] = bidPrices[productName][propertyType][stateKey] ?? {};
                    const statePrices = prices[productName][propertyType][stateKey].state ?? bidPrices[productName][propertyType][stateKey].state;
                    for (const countyKey in prices[productName][propertyType][stateKey]) {
                        bidPrices[productName][propertyType][stateKey][countyKey] = bidPrices[productName][propertyType][stateKey][countyKey] ?? {};
                        for (const qualityTier in prices[productName][propertyType][stateKey][countyKey]) {
                            bidPrices[productName][propertyType][stateKey][countyKey][qualityTier] = bidPrices[productName][propertyType][stateKey][countyKey][qualityTier] ?? {};
                            for (const saleTypeKey in prices[productName][propertyType][stateKey][countyKey][qualityTier]) {
                                bidPrices[productName][propertyType][stateKey][countyKey][qualityTier][saleTypeKey] = prices[productName][propertyType][stateKey][countyKey][qualityTier][saleTypeKey] ?? {};

                                // Record the initial bid price to track changes from Bidding Table
                                // Also set a minimum bid for loaded counties - minimum bid is the highest number from
                                //  the county's floor price, and the parent state-level bid
                                for (const saleTypeKey in bidPrices[productName][propertyType][stateKey][countyKey][qualityTier]) {
                                    bidPrices[productName][propertyType][stateKey][countyKey][qualityTier][saleTypeKey].initial_bid_price = bidPrices[productName][propertyType][stateKey][countyKey][qualityTier][saleTypeKey].bid_price;
                                    if (statePrices && countyKey !== 'state') {
                                        bidPrices[productName][propertyType][stateKey][countyKey][qualityTier][saleTypeKey].minimum_price = statePrices[qualityTier][saleTypeKey].bid_price ?? 0;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * These are custom floor pricing function, not bidding
     */
    const transformAndStoreFloorPrices = (prices) => {
        for (const propertyType in prices) {
            floorPrices[propertyType] = floorPrices[propertyType] ?? {};
            for (const stateKey in prices[propertyType]) {
                floorPrices[propertyType][stateKey] = floorPrices[propertyType][stateKey] ?? {};
                for (const countyKey in prices[propertyType][stateKey]) {
                    floorPrices[propertyType][stateKey][countyKey] = floorPrices[propertyType][stateKey][countyKey] ?? {};
                    for (const qualityTier in prices[propertyType][stateKey][countyKey]) {
                        floorPrices[propertyType][stateKey][countyKey][qualityTier] = floorPrices[propertyType][stateKey][countyKey][qualityTier] ?? {};
                        for (const saleTypeKey in prices[propertyType][stateKey][countyKey][qualityTier]) {
                            floorPrices[propertyType][stateKey][countyKey][qualityTier][saleTypeKey] = prices[propertyType][stateKey][countyKey][qualityTier][saleTypeKey] ?? {};

                            for (const saleTypeKey in floorPrices[propertyType][stateKey][countyKey][qualityTier]) {
                                floorPrices[propertyType][stateKey][countyKey][qualityTier][saleTypeKey].maximum_floor_price = floorPrices[propertyType][stateKey][countyKey][qualityTier][saleTypeKey].state_floor_price ?? null;
                                floorPrices[propertyType][stateKey][countyKey][qualityTier][saleTypeKey].initial_floor_price
                                    = getInitialFloorPrice(floorPrices[propertyType][stateKey][countyKey][qualityTier][saleTypeKey]);
                                floorPrices[propertyType][stateKey][countyKey][qualityTier][saleTypeKey].custom_floor_price
                                    = getActiveFloorPrice(floorPrices[propertyType][stateKey][countyKey][qualityTier][saleTypeKey]);
                            }
                        }
                    }
                }
            }
        }
    }
    const getActiveFloorPrice = (priceCollection) => {
        return priceCollection.custom_county_floor_price
            ?? priceCollection.custom_state_floor_price
            ?? priceCollection.county_floor_price
            ?? priceCollection.state_floor_price;
    }
    const getInitialFloorPrice = (priceCollection) => {
        return priceCollection.custom_county_floor_price
            ?? priceCollection.custom_state_floor_price
            ?? null;
    }

    // Fetch a state-level bid price if it is active and valid (ie higher than the floor price)
    const getStateBid = (stateKey, propertyType, qualityTier, saleType) => {
        const targetPrices = bidPrices[campaignStore.getProductLabel(false)]?.[propertyType]?.[stateKey]?.state?.[qualityTier]?.[saleType];

        return targetPrices?.bid_price && targetPrices?.bid_price > targetPrices?.floor_price
            ? targetPrices?.bid_price
            : null;
    }

    const getCustomFloorPrices = async () => {
        const reference = campaignStore.editingCampaign.reference;

        if (reference) {
            const resp = campaignStore.apiService.getCustomCampaignFloorPrices(reference).catch(e => e);
            if (resp.data.data.status) {
                return { status: true, data: resp.data.data.custom_floor_prices };
            }
            else
                return transformErrorResponse(resp);
        }
    }

    /** Update counties with active high bids when returning to cached state-level bids */
    const checkActiveBidCounties = () => {
        const productScope = campaignStore.getProductLabel();
        for (const countyKey in bidPrices[productScope]?.[activeFilters.propertyType]?.[activeFilters.stateLocationKey] ?? {}) {
            if (countyKey === 'state') continue;
            let activeBid = false;

            const statePrices = bidPrices[productScope][activeFilters.propertyType][activeFilters.stateLocationKey].state;
            for (const qualityTier in bidPrices[productScope][activeFilters.propertyType][activeFilters.stateLocationKey][countyKey]) {
                if (activeBid) break;
                for (const saleType in bidPrices[productScope][activeFilters.propertyType][activeFilters.stateLocationKey][countyKey][qualityTier]) {
                    const targetPrices = bidPrices[productScope][activeFilters.propertyType][activeFilters.stateLocationKey][countyKey][qualityTier][saleType];
                    if (targetPrices.bid_price > targetPrices.floor_price && targetPrices.bid_price > statePrices[qualityTier][saleType].bid_price) {
                        if (!activeBidCounties.value[activeFilters.stateLocationKey].includes(countyKey))
                            activeBidCounties.value[activeFilters.stateLocationKey].push(countyKey);

                        activeBid = true;
                        break;
                    }
                }
            }

            if (!activeBid) {
                activeBidCounties.value[activeFilters.stateLocationKey] = activeBidCounties.value[activeFilters.stateLocationKey].filter(key => key !== countyKey);
            }
        }
    }

    const setActiveFilters = (propertyType, stateLocationKey, countyLocationKey) => {
        activeFilters.propertyType = propertyType ?? activeFilters.propertyType;
        activeFilters.stateLocationKey = stateLocationKey ?? activeFilters.stateLocationKey;
        activeFilters.countyLocationKey = countyLocationKey ?? activeFilters.countyLocationKey;
    }

    // Bids may exist for counties which have been deactivated as locations
    const filterActiveCountyKeys = (countKeyArray = []) => {
        const validCounties = Object.values(campaignStore.getActiveZipCodes()).map(zipCode => zipCode.county_key);

        return countKeyArray.filter(key => key === 'state-bids' || validCounties.includes(key));
    }

    const transformErrorResponse = (response) => {
        return (response instanceof Error)
            ? ({ status: false, message: response.message || 'An unknown error occurred.' })
            : ({ status: false, message: response?.data?.data?.message || 'An unknown error occurred.' });
    }

    const $reset = () => {
        for (const key in bidPrices)
            delete bidPrices[key];
        for (const key in floorPrices)
            delete floorPrices[key];
        activeBidCounties.value = {};
    }

    return {
        bidPrices,
        floorPrices,
        scopedActiveBidCounties,
        stateHasActiveBids,
        statistics,
        activeBidCounties,

        loadStateFloorPrices,
        loadCountyFloorPrices,
        fetchBidPrices,
        getStateBid,
        loadBiddingLocationStatistics,
        getCustomFloorPrices,
        transformAndStoreBidPrices,
        loadCustomStateFloorPrices,
        loadCustomCountyFloorPrices,
        getActiveFloorPrice,

        $reset,
    }
});
