import { defineStore } from "pinia";
import { computed, ref } from "vue";
import { CampaignStatus, ProductLabel, ProductType, useProductConfigurationStore } from "./product-configuration.js";
import { CampaignWizardStoreApi } from "../services/campaign-wizard-store-api.js";
import { ReservedComponent } from "../../../Shared/SlideWizard/stores/wizard.js";
import { useBiddingStore } from "./bidding.js";
import { useDeliveryStore } from "./delivery.js";
import { useCompanyUsersStore } from "./company-users.js";

export const CampaignSlide = {
    Location: 'location',
    Budget: 'budget',
    Bidding: 'bidding',
    Delivery: 'delivery',
}

const defaultPerPage = 20;

export const useFutureCampaignStore = defineStore('futureCampaigns', () => {
    const apiService = new CampaignWizardStoreApi();
    const productConfigurationStore = useProductConfigurationStore();
    const biddingStore = useBiddingStore();
    const deliveryStore = useDeliveryStore();
    const companyUserStore = useCompanyUsersStore();

    const initialized = ref(false);

    const campaigns = ref([]);
    const campaignPaginationData = ref({ per_page: defaultPerPage });
    const fullCampaignList = ref([]);
    const companyOptInNames = ref([]);

    const editingCampaign = ref({});

    const productScope = computed(() => productConfigurationStore.productScope);
    const industryScope = computed(() => productConfigurationStore.industryScope);
    const serviceScope = computed(() => productConfigurationStore.serviceScope);
    const companyScope = ref(null);

    const activeZipCodes = ref({});

    const zipCodeCountyExceptions = ref({});
    const zipCodeTargetingEnabled = computed(() => {
        for (const state in zipCodeCountyExceptions.value) {
            if (zipCodeCountyExceptions.value[state].length)
                return true;
        }
        return false;
    });
    const zipCodeCampaign = computed(() => !!editingCampaign.value.zip_code_targeted);
    const unrestrictedZipCodes = ref(false);
    const customFloorPricingActive = computed(() => !!editingCampaign.value.uses_custom_floor_prices);

    const statusConfiguration = computed(() => productConfigurationStore.statusConfiguration);

    const initialize = async (companyId, queryFilters = {}) => {
        updateCompanyScope(companyId);

        if (initialized.value) {
            await getCampaignList(queryFilters).catch(e => e);
            return { status: true }
        }
        const productConfigResponse = await productConfigurationStore.initialize(companyId).catch(e => e);
        const getCampaignsResponse = await getCampaignList(queryFilters, null, true).catch(e => e);
        if (getCampaignsResponse.status && productConfigResponse.status) {
            initialized.value = true;

            return { status: true }
        }

        return { status: false, message: getCampaignsResponse.message ?? 'Failed to initialize Campaign Store.'};
    }

    const getCampaignList = async (filters = {}, page = null, initialLoad = false) => {
        page = page ?? campaignPaginationData.value.current_page ?? 1;
        const perPage = campaignPaginationData.value.per_page ?? defaultPerPage;

        const response = await apiService.getCampaignList(page, perPage, filters, initialLoad).catch(err => err);
        if (response.data?.data?.status) {
            const { data, ...paginationData } = response.data.data.campaigns;
            campaigns.value = data;
            campaignPaginationData.value = paginationData;
            zipCodeCountyExceptions.value = response.data.data.zip_code_exceptions;
            unrestrictedZipCodes.value = response.data.data.unrestricted_zip_code_targeting ?? false;
            fullCampaignList.value = response.data.data.all_campaigns_list ?? fullCampaignList.value;
            companyOptInNames.value = response.data.data.company_opt_in_names;

            return { status: true }
        }
        else {
            return transformErrorResponse(response);
        }
    }

    const handlePaginationEvent = async (pageLink, filterOptions) => {
        if (!pageLink)
            return;

        const response = await apiService.getCampaignPage(pageLink, filterOptions).catch(e => e);
        if (response.data?.data?.campaigns) {
            const { data, ...paginationData } = response.data.data.campaigns;
            campaigns.value = data;
            campaignPaginationData.value = paginationData;
            return { status: true }
        }

        return { status:false, message: transformErrorResponse(response) }
    }

    const updateModulePayload = (payload, slideKey) => {
        editingCampaign.value[slideKey] = payload;
    }

    const updateActiveZipCodes = (zipCodes) => {
        activeZipCodes.value = zipCodes;
    }

    const getActiveZipCodes = () => {
        return JSON.parse(JSON.stringify(activeZipCodes.value ?? []));
    }

    const saveCampaign = async (payloadModules) => {
        const payload = prepareCampaignPayload(payloadModules ?? undefined);
        const resp = editingCampaign.value.reference
            ? await apiService.updateCampaign(payload, editingCampaign.value.reference, companyScope.value).catch(e => e)
            : await apiService.saveNewCampaign(payload, companyScope.value).catch((e)  => e);

        if (resp.data?.data?.status) {
            biddingStore.$reset();
            return { status: true }
        }
        else {
            return transformErrorResponse(resp);
        }
    }

    const saveCampaignModule = async (moduleName) => {
        return await saveCampaign([moduleName]);
    }

    const prepareCampaignPayload = (includeModules) => {
        const basePayload = {};
        includeModules = includeModules ?? Object.values(CampaignSlide);

        for (const slideKey in editingCampaign.value) {
            if (includeModules.includes(slideKey)) {
                basePayload[slideKey] = editingCampaign.value[slideKey];
            }
        }

        return {
            status: editingCampaign.value.status ?? CampaignStatus.Active,
            name: editingCampaign.value.name,
            type: editingCampaign.value.type,
            property_types: editingCampaign.value.property_types,
            payload: JSON.parse(JSON.stringify(basePayload)),
            company_id: companyScope.value,
            zip_code_targeted: editingCampaign.value.zip_code_targeted ?? false,
            product: editingCampaign.value.product ?? productScope.value,
            industry: editingCampaign.value.industry ?? industryScope.value,
            service: editingCampaign.value.service ?? serviceScope.value,
            reference: editingCampaign.value.reference ?? null,
            uses_custom_floor_prices: editingCampaign.value.uses_custom_floor_prices ?? false,
        }
    }

    /**
     * This will load a campaign to store, but not initialise the slide wizard
     * Currently used for Admin2.0 custom floor pricing
     */
    const editCampaign = async (campaignReference) => {
        const resp = await apiService.editCampaign(campaignReference).catch(e => e);
        if (resp.data.data.status) {
            editingCampaign.value = resp.data.data.campaign;

            return { status: true }
        }
        else
            return transformErrorResponse(resp);
    }

    /**
     * This currently appends header data from the Wizard (campaign name and property types)
     */
    const appendAdditionalCampaignComponents = (payload) => {
        const headerData = payload[ReservedComponent.Header];
        if (headerData)
            Object.assign(editingCampaign.value, headerData);
    }

    const fetchModuleInputValue = (payloadKey, inputKey) => {
        return payloadKey
            ? editingCampaign.value[payloadKey]?.[inputKey] ?? null
            : editingCampaign.value[inputKey] ?? null;
    }

    const getProductLabel = (plural = false) => {
        if (productScope.value.toLowerCase() === ProductType.DirectLeads) {
            // singular/plural transformation is not required for direct leads product
            return ProductLabel.DirectLeads;
        }

        const label = `${productScope.value[0].toUpperCase()}${productScope.value.slice(1)}`;
        return plural
            ? `${label}s`
            : label;
    }

    const updateCompanyScope = (newId) => {
        if (companyScope.value && companyScope.value !== newId)
            $resetAll();

        companyScope.value = newId;
        apiService.setCompanyId(newId);
    }

    const updateContactDeliveries = (companyUser) => {
        const targetContactDelivery = editingCampaign.value?.delivery?.contact_deliveries?.find(contact => contact.id === companyUser.id);
        if (targetContactDelivery) {
            Object.assign(targetContactDelivery, {
                cell_phone: companyUser.cell_phone,
                email: companyUser.email,
                name: `${companyUser.first_name} ${companyUser.last_name ?? ''}`
            });
        }
    }

    // This can only be set on a fresh campaign
    const setZipCodeTargeting = (newSetting) => {
        if (editingCampaign.value.reference)
            console.error("Cannot convert a standard campaign to zip code targeting");
        else
            editingCampaign.value.zip_code_targeted = !!newSetting;
    }

    /**
     * Returns a complete, non-paginated list of campaign names and references if available
     */
    const getFullCampaignList = () => {
        return fullCampaignList.value?.length
            ? fullCampaignList.value
            : campaigns.value;
    }

    const $reset = () => {
        initialized.value = false;
        campaigns.value = [];
        campaignPaginationData.value = {};
        editingCampaign.value = {};

        companyScope.value = null;
        activeZipCodes.value = {};
    }

    /**
     * Reset all stores with company-scoped data
     */
    const $resetAll = () => {
        biddingStore.$reset();
        deliveryStore.$reset();
        companyUserStore.$reset();
        $reset();
    }

    const transformErrorResponse = (response) => {
        return { status: false, message: response?.response?.data?.message || response.data?.data?.message || response.message || 'An unknown error occurred.' };
    }

    return {
        apiService,
        productScope,
        serviceScope,
        companyScope,
        campaigns,
        paginationData: campaignPaginationData,
        editingCampaign,
        productConfiguration: productConfigurationStore.configuration,
        statusConfiguration,
        activeZipCodes,
        zipCodeCountyExceptions,
        zipCodeTargetingEnabled,
        zipCodeCampaign,
        unrestrictedZipCodes,
        customFloorPricingActive,
        companyOptInNames,

        initialize,
        updateModulePayload,
        fetchModuleInputValue,
        getProductLabel,
        saveCampaign,
        appendAdditionalCampaignComponents,
        getActiveZipCodes,
        updateActiveZipCodes,
        saveCampaignModule,
        updateCompanyScope,
        updateContactDeliveries,
        getCampaignList,
        handlePaginationEvent,
        transformErrorResponse,
        setZipCodeTargeting,
        editCampaign,
        getFullCampaignList,

        $reset,
        $resetAll,
    }
});
