import { defineStore } from "pinia";
import { computed, reactive, ref } from "vue";
import { useFutureCampaignStore } from "./future-campaigns.js";

export const BudgetType = {
    NO_LIMIT: 0,
    TYPE_DAILY_UNITS: 1,
    TYPE_DAILY_SPEND: 2,
}

export const ProductType = {
    Lead: 'lead',
    Appointment: 'appointment',
    DirectLeads: 'direct leads',
}

export const ProductLabel = {
    Lead: 'Lead',
    Appointment: 'Appointment',
    DirectLeads: 'Direct Leads',
}

export const CampaignStatus = {
    PausedPermanently: 0,
    PausedTemporarily: 1,
    Active: 2,
}

export const useProductConfigurationStore = defineStore('productConfiguration', () => {
    const campaignStore = useFutureCampaignStore();

    const productStore = reactive({
        [ProductType.Lead]: { property_types: [], budget_types: [], budget_options: {} },
        [ProductType.Appointment]: { property_types: [], budget_types: [], budget_options: {} },
        [ProductType.DirectLeads]: { property_types: [], budget_types: [], budget_options: {} },
    });

    const statusConfiguration = ref({});
    const budgetConfiguration = ref({});

    const productScope = ref(ProductType.Lead);
    const industryScope = ref('');
    const serviceScope = ref('');

    const currentConfiguration = computed(() => {
        return productStore[productScope.value] ?? {};
    });

    const initialized = ref(false);

    const defaultBudgetKey= computed(() => getRequiredBudgetKeys()[0] ?? getOptionalBudgetKeys()[0] ?? '');
    const usesLinkedBudgets = computed(() => currentConfiguration.value.budget_options?.link_optional_budget_types ?? false);
    const showPricing = computed(() => currentConfiguration.value.budget_options?.show_pricing ?? false);
    const minimumDailySpend = computed(() => currentConfiguration.value.budget_options.minimum_daily_spend ?? 0);
    const minimumDailyProducts = computed(() => currentConfiguration.value.budget_options.minimum_daily_products ?? 0);
    const showSchedules = computed(() => productScope.value === 'appointment');

    const setProductScope = (productKey) => {
        productScope.value = productKey;
    }

    const setIndustryScope = (industryKey) => {
        industryScope.value = industryKey;
    }

    const setServiceScope = (serviceKey) => {
        serviceScope.value = serviceKey;
    }

    const initialize = async (companyId, productKey) => {
        if (initialized.value) return { status: true }
        setProductScope(productKey ?? ProductType.Lead);

        const resp = await campaignStore.apiService.getProductConfigurations(companyId);
        if (resp.data?.data?.status) {
            for (const product in resp.data.data.configurations) {
                productStore[product] = resp.data.data.configurations[product];
            }
            statusConfiguration.value = resp.data.data.status_config;
            initialized.value = true;

            return { status: true }
        }
        else return transformErrorResponse(resp);
    }

    const getBudgetTypeOptions = () => {
        return Object.entries(currentConfiguration.value.budget_types ?? {}).map(([budgetKey, budgetLabel]) => ({ id: parseInt(budgetKey), name: budgetLabel }));
    }

    const getBudgetTypeLabel = (type) => {
        return currentConfiguration.value?.budget_types[type] ?? '';
    }

    const getDefaultBudgetType = () => {
        return 0; // Implement properly if a product needs something other than 'No Limit' as default budget type
    }

    const getRequiredBudgets = () => {
        return budgetConfiguration.value.required ?? [];
    }

    const getRequiredBudgetKeys = () => {
        return getRequiredBudgets().map(budget => budget.key);
    }

    const getOptionalBudgets = () => {
        return budgetConfiguration.value.optional ?? [];
    }

    const getOptionalBudgetKeys = () => {
        return getOptionalBudgets().map(budget => budget.key);
    }

    const getSalesTypes = (budgetKey, requiredBudget = false, qualityTier = null) => {
        const budgetTypeKey = requiredBudget ? 'required' : 'optional';
        const targetBudgetConfig = budgetConfiguration.value?.[budgetTypeKey]?.find(budget => budget.key === budgetKey)?.product_configuration;

        if (targetBudgetConfig) {
            const tiers = qualityTier
                ? targetBudgetConfig[qualityTier] ?? []
                : Object.values(targetBudgetConfig)[0];

            return tiers.reduce((output, enumName) => {
                return [...output, { key: useSnakeCase(enumName), name: enumName }];
            }, []);

        }
        else
            return [];
    }

    const getPropertyTypes = () => {
        return currentConfiguration.value?.property_types ?? [];
    }

    const getDefaultPropertyType = () => {
        return currentConfiguration.value?.property_types[0] ?? 'Residential';
    }

    const getBudgetDefault = (budgetKey, requiredBudget = false) => {
        const budgetTypeKey = requiredBudget ? 'required' : 'optional';
        const targetBudget = budgetConfiguration.value[budgetTypeKey]?.find(budget => budget.key === budgetKey);

        return targetBudget ?
            {
                display_name: targetBudget.display_name,
                key: targetBudget.key,
                type: targetBudget.type,
                value: targetBudget.value,
                status: targetBudget.status,
                required: targetBudget.required ?? false,
            }
            : null;
    }

    const getBudgetName = (budgetKey, required = false) => {
        const requiredKey = required ? 'required' : 'optional';
        const targetBudget = budgetConfiguration.value[requiredKey].find(budget => budget.key === budgetKey);

        return targetBudget?.display_name ?? useProperCase(targetBudget?.key ?? '');
    }

    const getProductLabel = (plural = false) => {
        if (productScope.value.toLowerCase() === ProductType.DirectLeads) {
            // singular/plural transformation is not required for direct leads product
            return ProductLabel.DirectLeads;
        }

        const label = useProperCase(productScope.value);
        return plural
            ? `${label}s`
            : label;
    }

    const transformErrorResponse = (response) => {
        return (response instanceof Error)
            ? ({ status: false, message: response.message || 'An unknown error occurred.' })
            : ({ status: false, message: response?.data?.data?.message || 'An unknown error occurred.' });
    }

    const useProperCase = (inputString) => {
        if (typeof(inputString) !== 'string') return '';
        return inputString.trim()
            .replace(/([a-z])([A-Z])/g, '$1_$2')
            .replace(/([A-Z])([A-Z])/, '$1_$2')
            .replace(/-+/g, '_')
            .replace(/\s+/g, '_')
            .split(/_+/)
            .map(word => word ? `${word[0].toUpperCase()}${word.slice(1)}` : '')
            .join(' ');
    }

    const useSnakeCase = (inputString) => {
        if (typeof(inputString) !== 'string') return '';
        const words = inputString.toLowerCase().trim().split(/[\s_-]+/g);
        return words.join('_');
    }

    return {
        configuration: currentConfiguration,
        statusConfiguration,
        defaultBudgetKey,
        usesLinkedBudgets,
        showPricing,
        minimumDailySpend,
        minimumDailyProducts,
        productScope,
        industryScope,
        serviceScope,
        showSchedules,
        budgetConfiguration,

        initialize,
        setProductScope,
        setIndustryScope,
        setServiceScope,

        getBudgetTypeOptions,
        getBudgetTypeLabel,
        getDefaultBudgetType,
        getRequiredBudgets,
        getOptionalBudgets,
        getRequiredBudgetKeys,
        getOptionalBudgetKeys,
        getSalesTypes,
        getPropertyTypes,
        getDefaultPropertyType,
        getBudgetDefault,
        getBudgetName,
        getProductLabel,
    }
});
