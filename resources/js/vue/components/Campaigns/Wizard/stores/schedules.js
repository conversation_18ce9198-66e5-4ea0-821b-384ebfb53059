import { defineStore } from "pinia";

import { ref } from "vue";
import { useFutureCampaignStore } from "./future-campaigns.js";


export const useSchedulesStore = defineStore('schedules', () => {
    const campaignStore = useFutureCampaignStore();

    const schedules = ref([]);
    const timezones = ref({});
    const shadowingUser = ref({});
    const storeInitialized = ref(false);

    const initialize = async () => {
        if (storeInitialized.value) return { status: true }

        const [timezoneResponse, scheduleResponse] = await Promise.all([
            campaignStore.apiService.getTimezones(),
            getAllSchedules(),
        ]);

        if (timezoneResponse.data?.data?.status && scheduleResponse.status) {
            timezones.value = timezoneResponse.data.data.timezones;
            storeInitialized.value = true;

            return { status: true }
        }
        else {
            return { status: false, message: 'Failed to load initial data.' }
        }
    }

    const getAllSchedules = async (initialLoad = false) => {
        if (initialLoad && schedules.value.length)
            return { status: true }

        const resp = await campaignStore.apiService.getAllCompanySchedules().catch(e=>e);
        if (resp.data?.data?.status) {
            schedules.value = resp.data.data.schedules;
            shadowingUser.value = resp.data.data.shadowing_user;

            return { status: true }
        }
        else {
            return genericErrorResponse(resp);
        }
    }

    const createSchedule = async (schedulePayload) => {
        const resp = await campaignStore.apiService.createInternalCalendar(schedulePayload).catch(e=>e);
        if (resp.data?.data?.status) {
            await getAllSchedules();
            return { status: true }
        }
        else {
            return genericErrorResponse(resp);
        }
    }

    const updateSchedule = async (schedulePayload) => {
        const resp = await campaignStore.apiService.updateSchedule(schedulePayload).catch(e=>e);
        if (resp.data?.data?.status) {
            await getAllSchedules();
            return { status: true }
        }
        else {
            return genericErrorResponse(resp);
        }
    }

    const deleteSchedule = async (scheduleId) => {
        const resp = await campaignStore.apiService.deleteSchedule(scheduleId).catch(e=>e);
        if (resp.data?.data?.status) {
            await getAllSchedules();
            return { status: true }
        }
        else {
            return genericErrorResponse(resp);
        }
    }

    const getShadowUserName = () => {
        return shadowingUser.value?.first_name
            ? `${shadowingUser.value.first_name} ${shadowingUser.value.last_name ?? ''}`
            : 'Unknown User';
    }

    const genericErrorResponse = (resp) => ({ status: false, message: resp.response?.data?.message || resp.message || 'An unknown error occurred.' });

    return {
        timezones,
        schedules,
        shadowingUser,

        initialize,
        getAllSchedules,
        createSchedule,
        updateSchedule,
        deleteSchedule,
        getShadowUserName,
    }
});
