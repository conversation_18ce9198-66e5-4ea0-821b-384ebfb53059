import { defineStore } from "pinia";
import { computed, nextTick, ref } from "vue";
import { useFutureCampaignStore } from "./future-campaigns.js";
import { ProductType } from "./product-configuration.js";
import CustomButton from "../../../Shared/components/CustomButton.vue";

export const DeliveryType = {
    Crm: 'crm',
    Contact: 'contact',
    Template: 'template',
    Schedule: 'schedule',
}

export const CrmSource = {
    Standalone: 1,
    FromTemplate: 2,
}

const getDefaultCrmDeliverer = () => structuredClone({
    id: `temp-${Date.now()}`, // temp timestamp-as-id for unsaved deliverers, in case they need deleting before saving
    crm_type: 0,
    crm_type_display: 'Standard Web Form',
    active: true,
    display_name: '',
    payload: {
        system_fields: [],
        additional_fields: [],
        custom_fields: [],
        interactable_fields: {},
        headers: [],
        json_fields: [],
    },
    campaigns: [],
});

export const CrmFieldType = {
    Text: 0,
    Dropdown: 1,
}

export const CrmInteractableType = {
    Button: 0,
}

export const CrmInteractableTypeMap = {
    [CrmInteractableType.Button]: CustomButton,
}

export const CrmType = {
    StandardWebForm: 0,
    LeadConduit: 1,
    JobNimbus: 2,
    Pipedrive: 3,
    ZohoOAuth: 4,
}

export const useDeliveryStore = defineStore('delivery', () => {
    const campaignStore = useFutureCampaignStore();

    const availableCrmConfigurations = ref([]);
    const availablePrefills = ref({});

    const editingCrmConfiguration= ref(getDefaultCrmDeliverer());

    const currentInteractables = computed(() =>
        mergeInteractables(availableCrmConfigurations.value.find(config => config.id === editingCrmConfiguration.value.crm_type)?.interactables ?? [], editingCrmConfiguration.value.interactables ?? []));

    const initialized = ref(false);
    const showSchedules = computed(() => {
        return campaignStore.productScope === ProductType.Appointment;
    });

    const crmConfigurationOptions = computed(() => {
        return availableCrmConfigurations.value.map(config => ({ name: config.name, id: config.id }));
    });

    const usesCustomFields = computed(() => {
        return editingCrmConfiguration.value.crm_type !== CrmType.Pipedrive;
    });

    const usesCustomHeaders = computed(() => {
        const targetHeaders = availableCrmConfigurations.value.find(crm => crm.id === editingCrmConfiguration.value.crm_type)?.headers;

        return targetHeaders != null;
    });

    const usesJsonFields = computed(() => {
        const targetFields = availableCrmConfigurations.value.find(crm => crm.id === editingCrmConfiguration.value.crm_type)?.json_fields;

        if (targetFields) {
            const formatField = editingCrmConfiguration.value.payload.system_fields.find(field => field.key === 'send_format');
            return /json/i.test(formatField?.value ?? "");
        }

        return false;
    });

    const editingCrmType = computed(() => editingCrmConfiguration.value?.crm_type ?? null);

    const crmImportOptions = ref([]);
    const additionalFieldsCache = ref(null);

    const editingDeliveryType = ref(DeliveryType.Crm);

    const companyCrmTemplates = ref([]);
    const crmShortcodes = ref([]);
    const replacerReference = ref(null);

    const initialize = async () => {
        if (initialized.value) return { status: true };
        const resp = await campaignStore.apiService.getCrmConfigurations().catch(e => e);

        if (resp.data?.data?.status) {
            availableCrmConfigurations.value = resp.data.data.crm_configurations;
            companyCrmTemplates.value = resp.data.data.crm_templates;
            crmShortcodes.value = resp.data.data.crm_shortcodes;
            availablePrefills.value = resp.data.data.crm_prefills;

            initialized.value = true;
            return { status: true }
        }
        else
            return campaignStore.transformErrorResponse(resp);
    }

    const getCrmDisplayNameById = (id) => {
        const targetConfig = availableCrmConfigurations.value.find(config => config.id === id);
        return targetConfig ? targetConfig.name : 'N/A';
    }

    const getCrmInteractableComponentMap = (crmType) => {
        return CrmInteractableTypeMap[crmType] ?? CustomButton
    }

    const executeInteractable = async (methodName, isTemplate = false) => {
        const crmId = editingCrmConfiguration.value.crm_type;
        const resp = await campaignStore.apiService.executeCrmInteractable(crmId, methodName, editingCrmConfiguration.value.id, editingCrmConfiguration.value.payload, isTemplate).catch(e => e);
        if (resp.data?.data?.status) {
            if (resp.data.data.crm_deliverer) {
                if (resp.data.data.crm_deliverer.status)
                    return ({ status: true, data: resp.data.data.crm_deliverer ?? {} });
                else
                    return campaignStore.transformErrorResponse(resp.data.data.crm_deliverer);
            }
            else
                return { status: true, data: {} }
        }
        else {
            return campaignStore.transformErrorResponse(resp);
        }
    }

    const clearEditingConfiguration = (resetType = true) => {
        editingCrmConfiguration.value = getDefaultCrmDeliverer();
        if (resetType)
            editingDeliveryType.value = DeliveryType.Crm;
    }

    const loadDefaultFields = async (interactablesOnly = false) => {
        await nextTick();
        const targetConfiguration = availableCrmConfigurations.value.find(config => config.id === editingCrmConfiguration.value.crm_type);
        if (targetConfiguration) {
            if (!interactablesOnly) {
                Object.assign(editingCrmConfiguration.value.payload, {
                    system_fields: targetConfiguration.system_fields ?? [],
                    additional_fields: editingCrmConfiguration.value.crm_type === CrmType.StandardWebForm
                        ? []
                        : targetConfiguration.additional_fields ?? [],
                });
            }

            currentInteractables.value = targetConfiguration.interactables ?? [];
        }
    }

    const getCrmImportOptions = async () => {
        const currentCampaignReference = campaignStore.editingCampaign.reference;
        const resp = await campaignStore.apiService.getCrmImportOptions(currentCampaignReference).catch(e => e);
        if (resp.data?.data?.status) {
            crmImportOptions.value = resp.data.data.crm_deliverers;
            return { status: true }
        }
        else
            return campaignStore.transformErrorResponse(resp);
    }

    const updateFieldValues = (keyValues, fieldType) => {
        if (fieldType in editingCrmConfiguration.value.payload) {
            if (!Array.isArray(editingCrmConfiguration.value.payload[fieldType])) return;
            Object.entries(keyValues).forEach(([key, value]) => {
                const targetArray = editingCrmConfiguration.value.payload[fieldType];
                if (Array.isArray(targetArray)) {
                    const targetField = targetArray.find(field => field.key === key);
                    if (targetField) {
                        targetField.value = value;
                    }
                }
            });
        }
    }

    const mergeInteractables = (fieldArrayOne, fieldArrayTwo) => {
        const outputArray = [...fieldArrayOne];
        fieldArrayTwo.forEach(field => {
            const targetIndex = outputArray.findIndex(targetField => targetField.method === field.method);
            if (targetIndex !== -1) outputArray[targetIndex] = field;
            else outputArray.push(field);
        });

        return outputArray;
    }

    const testCrmIntegration = (crmId, testData) => {
        return campaignStore.apiService.testCrmIntegration(crmId, {
            test_data: testData
        });
    }

    /**
     * Allow disabling additional_fields for standard webform
     */
    const toggleAdditionalFields = (useFields, doNotCache = false) => {
        if (editingCrmConfiguration.value.crm_type !== CrmType.StandardWebForm) return;
        if (useFields) {
            if (!additionalFieldsCache.value) {
                const targetConfiguration = availableCrmConfigurations.value.find(config => config.id === editingCrmConfiguration.value.crm_type);
                additionalFieldsCache.value = targetConfiguration?.additional_fields ?? [];
            }
            editingCrmConfiguration.value.payload.additional_fields = additionalFieldsCache.value;
            additionalFieldsCache.value = null;
        }
        else {
            additionalFieldsCache.value = null;
            if (!doNotCache)
                additionalFieldsCache.value = [...editingCrmConfiguration.value.payload.additional_fields];

            editingCrmConfiguration.value.payload.additional_fields = [];
        }
    }

    /**
     * Check if the current CRM Template has active deliverers in any Campaign
     */
    const templateHasActiveCampaigns = (templateId) => {
        const id = templateId ?? editingCrmConfiguration.value.id;
        return !!(companyCrmTemplates.value.find(template => id === template.id)?.campaigns?.length);
    }

    /**
     * Get the Campaign names currently linked to the target CRM Template
     */
    const getTemplateCampaignNames = (templateId) => {
        const id = templateId ?? editingCrmConfiguration.value.id;
        if (typeof id !== 'number')
            return [];
        const references = companyCrmTemplates.value.find(template => template.id === id)?.campaigns ?? [];
        return campaignStore.getFullCampaignList().reduce((output, campaign) => {
            return references.includes(campaign.reference)
                ? [...output, `- ${campaign.name}`]
                : output;
        }, []);
    }

    /**
     * Load a CRM template's values to current editing context
     */
    const loadTemplateValues = (templateId) => {
        const clone = convertTemplateToDeliverer(templateId);
        const baseConfig = editingCrmConfiguration.value
            ?? getDefaultCrmDeliverer();

        Object.assign(baseConfig, clone);
        editingCrmConfiguration.value = baseConfig;
    }

    /**
     * Transform a CRM Template payload to a Deliverer payload
     */
    const convertTemplateToDeliverer = (templateId, newTemplate) => {
        const id = templateId ?? editingCrmConfiguration.value.id;
        const targetTemplate = newTemplate
            ? companyCrmTemplates.value[companyCrmTemplates.value.length - 1]
            : companyCrmTemplates.value.find(template => template.id === id);
        if (!targetTemplate) return null;

        const clone = JSON.parse(JSON.stringify(targetTemplate));
        clone.template_id = targetTemplate.id;
        clone.active = true;
        clone.id = getTempId();

        return clone;
    }

    const getTempId = () => {
        return `temp-${Date.now()}`;
    }

    /**
     * Update a CRM Template. If syncCampaigns is true, the [campaigns] reference array will be used to
     * attach/detach Deliverers to this Company's campaigns
     */
    const saveCompanyCrmTemplate = async (syncCampaigns = false) => {
        const payload = JSON.parse(JSON.stringify(editingCrmConfiguration.value));
        if (syncCampaigns) {
            payload.sync_campaigns = true;
            payload.campaigns = makeCampaignSyncPayload();
        }
        if (/^temp/i.test(payload.id))
            payload.id = null;

        const resp = await campaignStore.apiService.saveCompanyCrmTemplate(payload).catch(e => e);
        if (resp.data?.data?.status) {
            companyCrmTemplates.value = resp.data.data.crm_templates;
            const data = {
                syncSelf: syncCampaigns
                    ? !!payload.campaigns[campaignStore.editingCampaign.reference]
                    : null,
            }

            return { status: true, data }
        }
        else
            return campaignStore.transformErrorResponse(resp);
    }

    /**
     * Compile the template/campaign sync list from scoped campaigns (e.g. pagination) to ensure we don't
     * desync campaign deliveries outside the current scope
     */
    const makeCampaignSyncPayload = () => {
        return campaignStore.getFullCampaignList().reduce((output, campaign) => {
            output[campaign.reference] = editingCrmConfiguration.value.campaigns.includes(campaign.reference);
            return output;
        }, {});
    }

    /**
     * Delete a Template along with every Delivery using it
     */
    const deleteCompanyCrmTemplate = async (templateId) => {
        const resp = await campaignStore.apiService.deleteCompanyCrmTemplate(templateId).catch(e => e);
        if (resp.data?.data?.status) {
            companyCrmTemplates.value = resp.data.data.crm_templates;
            return { status: true }
        }
        else
            return campaignStore.transformErrorResponse(resp);
    }

    const removeCampaignFromTemplate = (templateId) => {
        const template = companyCrmTemplates.value.find(template => template.id === templateId);
        if (template) template.campaigns = template.campaigns.filter(reference => reference !== campaignStore.editingCampaign.reference);
    }

    const getCampaignReferences = (templateId) => {
        const id = templateId ?? editingCrmConfiguration.value.id;
        const targetTemplate = companyCrmTemplates.value.find(template => template.id === id);

        return targetTemplate?.campaigns ?? [];
    }

    const getReplacerReference = async() => {
        if (!replacerReference.value) {
            const resp = await campaignStore.apiService.getFieldReplacerReference().catch(e => e);
            if (resp.data?.data?.status)
                replacerReference.value = {
                    instructions: resp.data.data.replacer_instructions,
                    examples: resp.data.data.replacer_examples,
                };
            else
                return campaignStore.transformErrorResponse(resp);
        }

        return { status: true }
    }

    const loadStandardWebformPrefillFields = (prefillName) => {
        if (prefillName in availablePrefills.value) {
            const targetPrefill = availablePrefills.value[prefillName];
            editingCrmConfiguration.value.payload.system_fields.forEach(field => {
                field.value = targetPrefill.system_fields?.[field.key] ?? '';
            });
            fillKeyValues(editingCrmConfiguration.value.payload.json_fields, targetPrefill.json_fields);
            fillKeyValues(editingCrmConfiguration.value.payload.headers, targetPrefill.headers);
            fillKeyValues(editingCrmConfiguration.value.payload.custom_fields, targetPrefill.custom_fields);
        }
    }

    const fillKeyValues = (targetArray, prefillObject) => {
        targetArray.length = 0;
        if (prefillObject)
            Object.entries(prefillObject).forEach(([key, value]) => targetArray.push({ key, value }));
    }

    const hasCachedFields = computed(() => !!additionalFieldsCache.value?.length);

    return {
        availableCrmConfigurations,
        availablePrefills,
        initialized,
        showSchedules,
        crmConfigurationOptions,
        editingCrmConfiguration,
        crmImportOptions,
        editingCrmType,
        editingDeliveryType,
        companyCrmTemplates,
        crmShortcodes,
        replacerReference,

        initialize,
        currentInteractables,
        getCrmDisplayNameById,
        getCrmInteractableComponentMap,
        executeInteractable,
        clearEditingConfiguration,
        loadDefaultFields,
        getCrmImportOptions,
        usesCustomFields,
        usesCustomHeaders,
        usesJsonFields,
        updateFieldValues,
        testCrmIntegration,
        toggleAdditionalFields,
        templateHasActiveCampaigns,
        getTemplateCampaignNames,
        loadTemplateValues,
        saveCompanyCrmTemplate,
        getCampaignReferences,
        convertTemplateToDeliverer,
        removeCampaignFromTemplate,
        deleteCompanyCrmTemplate,
        getReplacerReference,
        loadStandardWebformPrefillFields,
        hasCachedFields,
    }
});
