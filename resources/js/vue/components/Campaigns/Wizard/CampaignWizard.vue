<template>
    <div class="absolute">
        <AlertsContainer v-if="alertActive" :text="alertText" :alert-type="alertType" :dark-mode="darkMode" />
        <Transition name="modal">
            <div class="fixed inset-0 flex items-center justify-center bg-opacity-75 bg-dark-background z-50" >
                <div
                    class="absolute shadow rounded-lg w-[70vw] px-5 py-6"
                    :class="[darkMode ? 'text-slate-100 bg-dark-module border border-dark-border' : 'text-slate-900 bg-light-module']"
                 >
                    <LoadingSpinner v-if="loading || savingCampaign" class="my-16"/>
                    <div v-else-if="showNewCampaignMenu">
                        <div class="pb-4 mb-4 border-b bold">
                            Create New Campaign
                        </div>
                        <div v-if="!Object.keys(futureIndustryServices ?? {}).length" class="text-red-600">
                            No Industries are flagged to use Future Campaigns, or this Company is not attached to any Industries flagged for Future Campaigns - cannot create a new Campaign.
                        </div>
                        <div class="flex items-center gap-x-4 py-8">
                            <div class="w-48">
                                <p class="text-sm pb-1">Select Industry</p>
                                <Dropdown
                                    :dark-mode="darkMode"
                                    :options="industryOptions"
                                    v-model="selectedIndustry"
                                />
                            </div>
                            <div class="w-48">
                                <p class="text-sm pb-1">Select Service</p>
                                <Dropdown
                                    :disabled="!selectedIndustry"
                                    :dark-mode="darkMode"
                                    :options="serviceOptions"
                                    v-model="selectedService"
                                />
                            </div>
                            <div class="w-48">
                                <p class="text-sm pb-1">Select Product</p>
                                <Dropdown
                                    :disabled="!selectedService"
                                    :dark-mode="darkMode"
                                    :options="productOptions"
                                    v-model="selectedProduct"
                                />
                            </div>
                            <div v-if="campaignStore.unrestrictedZipCodes || campaignStore.zipCodeTargetingEnabled"
                                class="w-48"
                             >
                                <div class="flex items-center gap-x-2">
                                    <p class="text-sm pb-1">Location Type</p>
                                    <Tooltip
                                        :dark-mode="darkMode"
                                    >
                                        <template v-slot:default>
                                            <div v-if="campaignStore.unrestrictedZipCodes"
                                                 class="whitespace-pre-line"
                                            >
                                                This company has unrestricted zip code targeting enabled in their configuration
                                            </div>
                                            <div v-else class="whitespace-pre-line">This company has the option to create a zip code targeted campaign for certain counties.
                                                <br>If you select zip code targeting for this campaign, the following constraint will be applied:
                                                <br>- Only counties defined in the company's configuration can be targeted
                                            </div>
                                        </template>
                                    </Tooltip>
                                </div>
                                <Dropdown
                                    :dark-mode="darkMode"
                                    :options="zipCodeTargetingOptions"
                                    v-model="useZipCodeTargeting"
                                    :disabled="campaignStore.unrestrictedZipCodes"
                                />
                            </div>
                            <div class="w-48">
                                <p class="text-sm pb-1">Select Custom Budget</p>
                                <Dropdown
                                    :disabled="!selectedProduct"
                                    :dark-mode="darkMode"
                                    :options="customBudgetOptions"
                                    v-model="selectedCustomBudget"
                                />
                            </div>
                        </div>
                        <div class="flex items-center pt-4 mt-4 border-t gap-4">
                            <CustomButton
                                :dark-mode="darkMode"
                                color="slate"
                                @click="cancelWizard()"
                            >
                                Cancel
                            </CustomButton>
                            <CustomButton
                                :dark-mode="darkMode"
                                @click="getNewWizardConfiguration()"
                            >
                                Confirm Campaign Scope
                            </CustomButton>
                        </div>
                    </div>
                    <div v-else>
                        <!--    CUSTOM HEADER    -->
                        <WizardHeaderContainer
                            :dark-mode="darkMode"
                        />
                        <!--    SLIDES    -->
                        <WizardSlideContainer
                            v-if="configuration && campaignStore.editingCampaign"
                            :slide-map="slideMap"
                            :configuration="configuration"
                            :dark-mode="darkMode"
                            :saving-campaign="savingCampaign"
                            :readonly="readonlyMode"
                            @save:slide="updateSlidePayload"
                            @save:wizard="saveCampaign"
                            @close:wizard="confirmCancel"
                        />
                    </div>
                </div>
            </div>
        </Transition>
        <Modal
            :small="true"
            :dark-mode="darkMode"
            v-if="showConfirmCancel"
            @close="() => this.showConfirmCancel = false"
            @confirm="this.cancelWizard()"
            :close-text="'Cancel'"
            :confirm-text="'Close'"
        >
            <template v-slot:header>
                Confirm Exit
            </template>
            <template v-slot:content>
                Are you sure you wish to cancel changes to this Campaign?
            </template>
        </Modal>
    </div>
</template>

<script>
import CampaignLocationSlide from "./slides/CampaignLocationSlide.vue";
import CampaignBudgetSlide from "./slides/CampaignBudgetSlide.vue";
import CampaignBiddingSlide from "./slides/CampaignBiddingSlide.vue";
import CampaignDeliverySlide from "./slides/CampaignDeliverySlide.vue";
import WizardSlideContainer from "../../Shared/SlideWizard/components/WizardSlideContainer.vue";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import AlertsMixin from "../../../mixins/alerts-mixin.js";
import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
import { useWizardStore } from "../../Shared/SlideWizard/stores/wizard.js";
import { CampaignTransformerService } from "../../Shared/SlideWizard/services/CampaignTransformerService.js";
import Modal from "../../Shared/components/Modal.vue";
import Alert from "../../Shared/components/Alert.vue";
import { ProductType, useProductConfigurationStore } from "./stores/product-configuration.js";
import { useFutureCampaignStore } from "./stores/future-campaigns.js";
import WizardHeaderContainer from "../../Shared/SlideWizard/components/WizardHeaderContainer.vue";
import CampaignWizardHeader from "./components/CampaignWizardHeader.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import Tooltip from "../../Shared/components/Tooltip.vue";

const slideMap = {
    'location': CampaignLocationSlide,
    'budget': CampaignBudgetSlide,
    'bidding': CampaignBiddingSlide,
    'delivery': CampaignDeliverySlide,
    'header': CampaignWizardHeader,
}

export default {
    name: "CampaignWizard",
    components: {
        Tooltip,
        CustomButton,
        Dropdown,
        WizardHeaderContainer,
        Alert,
        Modal,
        AlertsContainer,
        LoadingSpinner,
        WizardSlideContainer,
        CampaignLocationSlide,
        CampaignBudgetSlide,
        CampaignBiddingSlide,
        CampaignDeliverySlide,
    },
    mixins: [AlertsMixin],
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        apiDriver: {
            type: String,
            default: "api",
        },
        companyId: {
            type: Number,
            required: true,
        },
        editingCampaignReference: {
            type: String,
            default: null,
        },
        futureIndustryServices: {
            type: Array,
            default: [],
        },
        readonly: {
            type: Boolean,
            default: false,
        },
        customBudgetTypes: {
            type: Object,
            default: {},
        },
    },
    emits: [
        'close:wizard',
        'refresh-and-close:wizard',
    ],
    data() {
        return {
            slideMap,
            configuration: null,
            loading: false,

            wizardStore: useWizardStore(),
            productConfigurationStore: useProductConfigurationStore(),
            campaignStore: useFutureCampaignStore(),

            wizardConfiguration: null,

            showNewCampaignMenu: false,
            showConfirmCancel: false,

            selectedIndustry: null,
            selectedService: null,
            selectedProduct: null,
            selectedCustomBudget: 0,
            useZipCodeTargeting: -1,
            savingCampaign: false,

            zipCodeTargetingOptions: [
                { name: 'Standard', id: -1 },
                { name: 'Zip Code Targeted', id: 1 },
            ],

            customBudgetOptions: [
                { name: 'None', id: 0 },
            ]
        }
    },
    mounted() {
        this.campaignStore.updateCompanyScope(this.companyId);

        if (this.editingCampaignReference)
            this.openCampaignForEditing();
        else {
            this.getNewCampaignOptions();
        }
    },
    computed: {
        industryOptions() {
            return Object.entries(this.futureIndustryServices).map(([industryKey, servicesArray]) => ({ name: servicesArray[0].industry_name, id: industryKey }));
        },
        serviceOptions() {
            return this.selectedIndustry
                ? this.futureIndustryServices[this.selectedIndustry].map(service => ({ name: service.name, id: service.slug }))
                : [];
        },
        productOptions() {
            const options = this.selectedIndustry && this.selectedService
                ? this.futureIndustryServices[this.selectedIndustry]?.find(service => service.slug === this.selectedService)?.products.reduce((output, product) =>
                        /^appointment$/i.test(product.name) ? output : [...output, ({ name: product.name, id: product.name.toLowerCase() })]
                    , [])
                : [];
            this.selectedProduct = options[0]?.id ?? null;

            return options;
        },
        customBudgetOptions() {
            return Object.entries(this.customBudgetTypes[this.selectedProduct] ?? {}).reduce((output, [key, value]) => {
                output.push({ name: value, id: key });
                return output;
            }, [
                { name: 'None', id: 0 }
            ]);
        },
        readonlyMode() {
            return this.readonly || this.campaignStore.editingCampaign.product === 'Appointment';
        },
    },
    methods: {
        getNewCampaignOptions() {
            if (this.campaignStore.unrestrictedZipCodes)
                this.useZipCodeTargeting = 1;

            this.showNewCampaignMenu = true;
        },
        openCampaignForEditing(skipToSlide = null) {
            this.loading = true;

            this.campaignStore.apiService.editCampaign(this.editingCampaignReference).then(resp => {
                if (resp.data?.data?.status) {
                    this.campaignStore.editingCampaign = resp.data.data.campaign;
                    this.productConfigurationStore.budgetConfiguration = resp.data.data.campaign_configuration?.budgets
                        ?? this.productConfigurationStore.budgetConfiguration;
                    this.configuration = resp.data.data.wizard;
                    this.wizardStore.initialize(this.configuration, this.slideMap, CampaignTransformerService);
                }
                else {
                    this.showAlert('error', 'Failed to initialize Slide Wizard.');
                }
            }).catch(e => {
                this.showAlert('error', e.response?.data?.message ?? 'Failed to initialize the Slide Wizard');
            }).finally(async () => {
                this.wizardStore.loadInputs(this.campaignStore.editingCampaign);
                await this.productConfigurationStore.initialize(this.companyId, this.campaignStore.editingCampaign.product?.toLowerCase() ?? ProductType.Lead);
                this.productConfigurationStore.setIndustryScope(this.campaignStore.editingCampaign.industry);
                this.productConfigurationStore.setServiceScope(this.campaignStore.editingCampaign.service);
                this.productConfigurationStore.setProductScope(this.campaignStore.editingCampaign.product?.toLowerCase());
                if (skipToSlide)
                    this.wizardStore.goToSlide(skipToSlide);

                this.loading = false;
            });
        },
        getNewWizardConfiguration() {
            if (this.selectedIndustry && this.selectedService && this.selectedProduct) {
                this.loading = true;

                this.productConfigurationStore.initialize(this.selectedProduct).then(() => {
                    this.productConfigurationStore.setIndustryScope(this.selectedIndustry);
                    this.productConfigurationStore.setServiceScope(this.selectedService);
                    this.productConfigurationStore.setProductScope(this.selectedProduct);
                    this.campaignStore.apiService.getNewWizardConfiguration(this.selectedProduct, this.selectedIndustry, this.selectedService, this.selectedCustomBudget).then(resp => {
                        if (resp.data?.data?.status) {
                            this.configuration = resp.data.data.wizard;
                            this.wizardStore.resetInputs();
                            this.campaignStore.editingCampaign = resp.data.data.campaign;
                            this.productConfigurationStore.budgetConfiguration = resp.data.data.campaign_configuration?.budgets
                                ?? this.productConfigurationStore.budgetConfiguration;
                            this.wizardStore.initialize(this.configuration, this.slideMap, CampaignTransformerService);
                            this.campaignStore.setZipCodeTargeting(this.useZipCodeTargeting === 1);
                            this.showNewCampaignMenu = false;
                            this.loading = false;
                        }
                    }).catch(e => {
                        console.error(e);
                    });
                }).catch(e => {
                    console.error(e)
                });
            }
        },
        saveCampaign(exitOnSuccess) {
            this.savingCampaign = true;
            this.campaignStore.saveCampaign().then(resp => {
                if (resp.status) {
                    this.showAlert('success', "The Campaign was saved successfully");
                    if (exitOnSuccess) {
                        this.wizardStore.resetWizard();
                        this.$emit('refresh-and-close:wizard');
                    }
                    else {
                        const currentSlide = this.wizardStore.currentSlideKey;
                        this.openCampaignForEditing(currentSlide);
                    }
                }
                else {
                    this.showAlert('error', resp.message ?? "There was an error saving the Campaign.");
                }
            }).finally(() => this.savingCampaign = false);
        },
        updateSlidePayload(slidePayload, slideKey) {
            this.campaignStore.updateModulePayload(slidePayload, slideKey);
            this.campaignStore.appendAdditionalCampaignComponents(this.wizardStore.fetchReservedComponents());
        },
        confirmCancel(skipConfirmation = false) {
            if (skipConfirmation)
                this.cancelWizard();
            else
                this.showConfirmCancel = true;
        },
        cancelWizard() {
            this.wizardStore.resetWizard();
            this.$emit('close:wizard');
        }
    },
}
</script>
