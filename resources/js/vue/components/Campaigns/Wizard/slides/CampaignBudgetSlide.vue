<template>
    <div class="min-h-[48rem] overflow-y-auto">
        <LoadingSpinner v-if="loading" />
        <div v-else>
            <div>
                <div class="py-4">
                    <h4 class="font-semibold pb-4">
                        Required {{ productConfiguration.getProductLabel(true) }}
                    </h4>
                    <div v-for="budget in productConfiguration.getRequiredBudgets()">
                        <div class="flex items-end gap-x-4">
                            <div class="w-64 flex items-center gap-x-4">
                                <Dropdown
                                    :options="productConfiguration.getBudgetTypeOptions()"
                                    v-model="slideInputs.budgets[budget.key].type"
                                    @update:model-value="handleBudgetTypeChange"
                                    :dark-mode="darkMode"
                                />
                            </div>
                            <CustomInput
                                v-if="slideInputs.budgets[budget.key].type === BudgetType.TYPE_DAILY_SPEND"
                                input-type="number"
                                prefix-text="$"
                                v-model="slideInputs.budgets[budget.key].value"
                                label="Spend Limit"
                                @change:modelValue="customValidation"
                                :dark-mode="darkMode"
                            />
                            <CustomInput
                                v-if="slideInputs.budgets[budget.key].type === BudgetType.TYPE_DAILY_UNITS"
                                input-type="number"
                                v-model="slideInputs.budgets[budget.key].value"
                                :label="`${productConfiguration.getProductLabel()} Limit`"
                                @change:modelValue="customValidation"
                                :dark-mode="darkMode"
                            />
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="productConfiguration.getOptionalBudgetKeys().length" class="max-w-5xl py-4">
                <h4 class="font-semibold pb-4">
                    Optional {{ productConfiguration.getProductLabel(true) }}
                </h4>
                <div class="relative md:grid md:grid-cols-6 text-center font-bold py-2 items-center border-b text-xs uppercase"
                    :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']"
                >
                    <p>Active</p>
                    <p>Name</p>
                    <p class="col-span-2">{{ productConfiguration.usesLinkedBudgets ? budgetTypeLabel : "Avg. Daily Limit" }}</p>
                    <p class="col-span-2">{{ productConfiguration.getProductLabel() }} Price</p>
                </div>
                <div v-for="budgetKey in productConfiguration.getOptionalBudgetKeys()"
                     :key="budgetKey"
                     :class="[darkMode ? 'odd:bg-dark-175' : 'odd:bg-gray-200']"
                >
                    <div v-if="slideInputs.budgets[budgetKey]"
                         class="relative text-sm md:text-center items-center grid md:grid-cols-6 gap-4 md:gap-0 p-4 md:py-4 md:px-0"
                    >
                        <div>
                            <p class="font-bold text-sm md:hidden">Active</p>
                            <ToggleSwitch
                                v-model="slideInputs.budgets[budgetKey].status"
                                :small="true"
                                color="green"
                                @change="() => customValidation"
                                :dark-mode="darkMode"
                            />
                        </div>
                        <div>
                            <p class="font-bold text-sm md:hidden">Name</p>
                            <div class="flex items-center md:justify-center gap-x-3">
                                <p class="inline">
                                    {{ $filters.toProperCase(budgetKey) ?? '' }}
                                </p>
                                <Tooltip class="inline" v-if="tooltips[budgetKey]"
                                     :dark-mode="darkMode"
                                >
                                    {{ tooltips[budgetKey] }}
                                </Tooltip>
                            </div>
                        </div>
                        <div class="col-span-2">
                            <!--  Optional budgets have their type linked to required budget type e.g. Leads -->
                            <div v-if="productConfiguration.usesLinkedBudgets">
                                <p class="font-bold text-sm md:hidden text-gray-900">{{ budgetTypeLabel }}</p>
                                <div v-if="slideInputs.budgets[budgetKey].status"
                                     class="flex md:justify-center items-center"
                                >
                                    <CustomInput
                                        v-if="slideInputs.budgets[budgetKey]?.type === BudgetType.TYPE_DAILY_SPEND"
                                        type="text"
                                        prefix-text="$"
                                        classes="max-w-[10rem]"
                                        v-model="slideInputs.budgets[budgetKey].value"
                                        @change:modelValue="customValidation"
                                        :dark-mode="darkMode"
                                    />
                                    <CustomInput
                                        v-if="slideInputs.budgets[budgetKey].type === BudgetType.TYPE_DAILY_UNITS"
                                        type="text"
                                        input-pattern="\d+"
                                        classes="max-w-[10rem]"
                                        v-model="slideInputs.budgets[budgetKey].value"
                                        @change:modelValue="customValidation"
                                        :dark-mode="darkMode"
                                    />
                                </div>
                            </div>
                            <!--  Optional budgets are independent e.g. Appointments -->
                            <div v-else>
                                <p class="font-bold text-sm md:hidden text-gray-900">Budget settings</p>
                                <div v-if="slideInputs.budgets[budgetKey].status"
                                     class="flex md:justify-center items-start gap-x-2"
                                >
                                    <div class="w-full">
                                        <Dropdown
                                            :options="productConfiguration.getBudgetTypeOptions()"
                                            v-model="slideInputs.budgets[budgetKey].type"
                                            :dark-mode="darkMode"
                                        />
                                    </div>
                                    <div class="w-24">
                                        <CustomInput
                                            v-if="slideInputs.budgets[budgetKey]?.type === BudgetType.TYPE_DAILY_SPEND"
                                            type="text"
                                            prefix-text="$"
                                            classes="max-w-[10rem]"
                                            v-model="slideInputs.budgets[budgetKey].value"
                                            @change:modelValue="customValidation"
                                            :dark-mode="darkMode"
                                        />
                                        <CustomInput
                                            v-if="slideInputs.budgets[budgetKey].type === BudgetType.TYPE_DAILY_UNITS"
                                            type="text"
                                            input-pattern="\d+"
                                            classes="max-w-[10rem]"
                                            v-model="slideInputs.budgets[budgetKey].value"
                                            @change:modelValue="customValidation"
                                            :dark-mode="darkMode"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-span-2">
                            <p v-if="prices[budgetKey]?.max">
                                ${{ prices[budgetKey].min }} - ${{ prices[budgetKey].max }}
                            </p>
                            <p v-else-if="prices[budgetKey]?.min">
                                ${{ prices[budgetKey].min }}
                            </p>
                            <div v-else>
                                <p v-if="loading">Searching prices...</p>
                                <p v-else>Could not fetch price.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-if="Object.keys(slideInputs.options).length" class="col-start-1 my-10 mx-2 md:mx-0">
                    <h4 class="font-semibold pb-2">
                        Other Options
                    </h4>
                    <div class="flex items-center gap-x-2">
                        <input type="checkbox"
                           class="form-checkbox h-4 w-4 text-primary-500 rounded"
                           v-model="slideInputs.options.allow_non_budget_premium_leads"
                        />
                        <p class="text-sm md:text-base">Accept Qualified Premium Leads When Over Budget</p>
                    </div>
                </div>

            </div>
            <div v-if="productConfiguration.getRequiredBudgetKeys().length && productConfiguration.showPricing"
                 class="justify-center flex flex-col max-w-5xl pt-6"
            >
                <div class="flex flex-row items-center pb-4 gap-x-3">
                    <h4 class="font-semibold">
                        Required {{ productConfiguration.getProductLabel() }} Prices
                    </h4>
                    <Tooltip :dark-mode="darkMode" v-if="tooltips.required">{{ tooltips.required[productConfiguration.defaultBudgetKey] ?? tooltips.required.default }}</Tooltip>
                </div>
                <table class="w-full text-sm text-left">
                    <thead class="text-center font-bold border-b text-xs uppercase"
                           :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']"
                    >
                    <tr>
                        <th class="px-6 py-3">Name</th>
                        <th class="px-6 py-3">{{ productConfiguration.getProductLabel() }} Price</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-for="{ key, name } in productConfiguration.getSalesTypes(productConfiguration.defaultBudgetKey, true)"
                        :key="key"
                        class="border-b text-center h-20"
                        :class="[darkMode ? 'odd:bg-dark-175 border-dark-border' : 'odd:bg-gray-200 border-light-border']"
                    >
                        <td class="px-6 py-3">
                            <div class="flex items-center justify-center relative gap-x-3">
                                <p class="inline">
                                    {{ name }}
                                </p>
                                <Tooltip v-if="tooltips[key]" :dark-mode="darkMode">
                                    {{ tooltips[key] }}
                                </Tooltip>
                            </div>
                        </td>
                        <td class="px-6 py-3">
                            <p v-if="prices[key]?.max">
                                ${{ prices[key].min }} - ${{ prices[key].max }}
                            </p>
                            <p v-else-if="prices[key]?.min">
                                ${{ prices[key].min }}
                            </p>
                            <div v-else>
                                <p v-if="loading">Searching prices...</p>
                                <p v-else>Could not fetch price.</p>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</template>

<script>
import { useWizardStore } from "../../../Shared/SlideWizard/stores/wizard.js";
import { usePricingStore } from "../stores/pricing.js";
import {BudgetType, ProductType, useProductConfigurationStore} from "../stores/product-configuration.js";
import { useLocalityDataStore } from "../stores/locality-data.js";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import CustomInput from "../../../Shared/components/CustomInput.vue";
import ToggleSwitch from "../../../Shared/components/ToggleSwitch.vue";
import HoverTooltip from "../../../Shared/components/HoverTooltip.vue";
import Tooltip from "../../../Shared/components/Tooltip.vue";
import { CampaignSlide, useFutureCampaignStore } from "../stores/future-campaigns.js";

const tooltipStore = {
    lead: {
        required: {
            default: 'Exclusive, Duo, Trio and Quad are required lead types and must be selected.',
            exclusive_only: 'This Campaign only receives Exclusive leads.',
            unverified_only: 'This Campaign only receives Unverified leads.',
        },
        exclusive: 'Matched only to you',
        duo: 'Matched with 2 installers',
        trio: 'Matched with 3 installers',
        quad: 'Matched with 4 installers',
        email_only: 'Communication by email only',
        unverified: 'Phone number not verified',
    },
    direct_leads: {
        required: 'Exclusive, Duo, and Trio are required lead types and must be selected ',
        exclusive: 'Matched only to you',
        duo: 'Matched with 2 companies',
        trio: 'Matched with 3 companies',
        quad: 'Matched with 4 companies',
        email_only: 'Communication by email only',
        unverified: 'Phone number not verified',
    },
}

export default {
    name: "CampaignBudgetSlide",
    components: {
        Tooltip,
        HoverTooltip,
        ToggleSwitch,
        CustomInput,
        Dropdown,
        LoadingSpinner

    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        initialSlideData: {
            type: Object,
            default: () => ({}),
        },
    },
    emits: ['update:slideInput'],
    data() {
        return {
            wizardStore: useWizardStore(),
            priceStore: usePricingStore(),
            productConfiguration: useProductConfigurationStore(),
            localityStore: useLocalityDataStore(),
            campaignStore: useFutureCampaignStore(),

            slideInputs: {
                budgets: {},
                options: {},
            },
            prices: {},
            loading: true,

            BudgetType,
        }
    },
    computed: {
        budgetTypeLabel() {
            return this.productConfiguration.getBudgetTypeLabel(this.slideInputs.budgets[this.productConfiguration.defaultBudgetKey]?.type);
        },
        tooltips() {
            if(this.productConfiguration.productScope === ProductType.DirectLeads) {
                return tooltipStore["direct_leads"] ?? {}
            }

            return tooltipStore[this.productConfiguration.productScope] ?? {}
        },
    },
    mounted() {
        this.initialize();
    },
    methods: {
        async initialize() {
            this.loading = true;

            Object.assign(this.slideInputs, this.initialSlideData);

            this.productConfiguration.getRequiredBudgets().forEach(budget => {
                this.slideInputs.budgets[budget.key] = this.slideInputs.budgets[budget.key]?.key
                    ? this.slideInputs.budgets[budget.key]
                    : this.productConfiguration.getBudgetDefault(budget.key, true);
                this.slideInputs.budgets[budget.key].status = true;
            });
            this.productConfiguration.getOptionalBudgets().forEach(budget => {
                this.slideInputs.budgets[budget.key] = this.slideInputs.budgets[budget.key]?.key
                    ? this.slideInputs.budgets[budget.key]
                    : this.productConfiguration.getBudgetDefault(budget.key, false);
            });

            const currentBudgetType = this.slideInputs.budgets[this.productConfiguration.defaultBudgetKey]?.type;
            if (currentBudgetType > 0)
                this.handleBudgetTypeChange(currentBudgetType);

            const zipCodes = this.campaignStore.fetchModuleInputValue(CampaignSlide.Location, 'zip_codes');

            const { status } = await this.priceStore.getPriceRangeForZipCodes(
                zipCodes ?? [],
                this.productConfiguration.productScope,
                this.productConfiguration.industryScope,
                this.productConfiguration.serviceScope,
                this.campaignStore.editingCampaign.property_types ?? []
            );

            if (status) {
                this.prices = this.priceStore.getFetchedPriceRangesForProductV4();
            }

            this.loading = false;
        },
        handleBudgetTypeChange(newValue) {
            if (!this.productConfiguration.usesLinkedBudgets) return;

            for (const budgetKey in this.slideInputs.budgets) {
                if (!this.slideInputs.budgets[budgetKey].required) {
                    this.slideInputs.budgets[budgetKey].type = newValue;
                }
            }
        },
        handleInputUpdate(newValue, inputKey) {
            this.$emit('update:slideInput', newValue, inputKey);
        },
        customValidation() {
            const budgetErrors = [];
            const minimumDailySpend = this.productConfiguration.minimumDailySpend;
            const minimumDailyProducts = this.productConfiguration.minimumDailyProducts;
            const defaultBudgetName = this.productConfiguration.getBudgetName(this.productConfiguration.defaultBudgetKey, true);

            if (this.slideInputs.budgets[this.productConfiguration.defaultBudgetKey].type === BudgetType.TYPE_DAILY_SPEND) {
                const maxDailySpend = this.productConfiguration.usesLinkedBudgets ? this.slideInputs.budgets[this.productConfiguration.defaultBudgetKey].value : null;
                if (maxDailySpend != null && maxDailySpend < minimumDailySpend)
                    budgetErrors.push(`The ${defaultBudgetName} ${this.productConfiguration.getProductLabel(true)} spend must be at least $${minimumDailySpend}.`);
                else {
                    for (const budgetKey of this.productConfiguration.getOptionalBudgetKeys()) {
                        if (this.slideInputs.budgets[budgetKey].status) {
                            if (this.slideInputs.budgets[budgetKey].value < minimumDailySpend) {
                                budgetErrors.push(`The ${this.productConfiguration.getBudgetName(budgetKey)} ${this.productConfiguration.getProductLabel(true)} spend must be at least $${minimumDailySpend}`);
                            } else if (maxDailySpend != null && this.slideInputs.budgets[budgetKey].value > maxDailySpend) {
                                budgetErrors.push(`The ${this.productConfiguration.getBudgetName(budgetKey)} ${this.productConfiguration.getProductLabel(true)} spend cannot be more than the ${defaultBudgetName} daily spend`);
                            }
                        }
                    }
                }
            }
            else if (this.slideInputs.budgets[this.productConfiguration.defaultBudgetKey].type === BudgetType.TYPE_DAILY_UNITS) {
                const maxDailyProducts = this.productConfiguration.usesLinkedBudgets ? this.slideInputs.budgets[this.productConfiguration.defaultBudgetKey].value : null;
                if (maxDailyProducts != null && maxDailyProducts < minimumDailyProducts)
                    budgetErrors.push(`The ${this.productConfiguration.getBudgetName(this.productConfiguration.defaultBudgetKey, true)} ${this.productConfiguration.getProductLabel(true)} limit must be at least $${minimumDailyProducts}`);
                else {
                    for (const budgetKey of this.productConfiguration.getOptionalBudgetKeys()) {
                        if (this.slideInputs.budgets[budgetKey]?.status) {
                            if (this.slideInputs.budgets[budgetKey].value < minimumDailyProducts) {
                                budgetErrors.push(`The ${this.productConfiguration.getBudgetName(budgetKey)} ${this.productConfiguration.getProductLabel(true)} limit must be at least ${minimumDailyProducts}`);
                            }
                            else if (maxDailyProducts != null && this.slideInputs.budgets[budgetKey].value > maxDailyProducts) {
                                budgetErrors.push(`The ${this.productConfiguration.getBudgetName(budgetKey)} ${this.productConfiguration.getProductLabel(true)} limit cannot be more than the ${defaultBudgetName} daily limit`);
                            }
                        }
                    }
                }
            }

            return budgetErrors.length
                ? {
                    valid: false,
                    message: this.wizardStore.validationService.getErrorBagSummary(budgetErrors),
                    errorBag: budgetErrors
                }
                : { valid: true };
        }
    },
    watch: {
        ['slideInputs.budgets']: {
            deep: true,
            handler(newValue) {
                this.handleInputUpdate(newValue, 'budgets')
            },
        },
        ['slideInputs.options']: {
            deep: true,
            handler(newValue) {
                this.handleInputUpdate(newValue, 'options')
            },
        },
    },
    expose: ['customValidation'],
}
</script>
