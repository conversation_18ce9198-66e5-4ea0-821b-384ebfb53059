<template>
    <div>
        <AlertsContainer v-if="alertActive" :alert-type="alertType" :text="alertText" :dark-mode="darkMode" />
        <!--    NEW DELIVERY METHOD BUTTONS    -->
        <div class="grid grid-cols-1 gap-y-12"
             :class="[saving ? 'pointer-events-none grayscale-[50%] opacity-50' : '']"
        >
            <div class="flex items-center pt-4 gap-2">
                <CustomButton @click="toggleContactDeliveryModal(true)"
                    color="primary-outline"
                    :dark-mode="darkMode"
                >
                    New Contact Delivery
                </CustomButton>
                <CustomButton @click="toggleCrmDeliveryModal(true)"
                    color="primary-outline"
                    :dark-mode="darkMode"
                >
                    New CRM Delivery
                </CustomButton>
                <CustomButton @click="toggleShowImportCrmModal(true)"
                              color="primary-outline"
                              :dark-mode="darkMode"
                >
                    Import CRM Delivery
                </CustomButton>
                <CustomButton @click="createTemplate"
                              color="primary"
                              :dark-mode="darkMode"
                >
                    New CRM Template
                </CustomButton>
            </div>
        </div>

        <LoadingSpinner v-if="loading || saving" :dark-mode="darkMode" />
        <div v-else>
            <!--    SCHEDULES TABLE    -->
            <div v-if="productConfigurationStore.showSchedules" class="py-4">
                <GridTable
                    :dark-mode="darkMode"
                    :tableSetup="schedulesTableSetup"
                    :tableData="campaignSchedules"
                    @change:checkbox="updateActiveSchedules"
                    @action:edit="(id) => editSchedule(id)"
                    @action:delete="(id) => deleteSchedule(id)"
                />
            </div>
            <!--    CONTACT TABLE    -->
            <div class="py-4">
                <GridTable
                    :dark-mode="darkMode"
                    :tableSetup="contactTableSetup"
                    :tableData="slideInputs.contact_deliveries"
                    @action:edit="editContact"
                    @action:delete="(id) => deleteDelivery(DeliveryType.Contact, id)"
                />
            </div>

            <!--	CRM TABLE	-->
            <div class="py-4 border-t" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
                <GridTable
                    :dark-mode="darkMode"
                    :tableSetup="campaignCrmTableSetup"
                    :tableData="slideInputs.crm_deliveries"
                    @action:edit="editCrm"
                    @action:delete="(id) => deleteDelivery(DeliveryType.Crm, id)"
                    @click:button="handleCRMIntegrationTest"
                />
            </div>

            <!--	CRM TEMPLATE TABLE	-->
            <div class="py-4 border-t" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
                <GridTable
                    :dark-mode="darkMode"
                    :tableSetup="crmTemplateTableSetup"
                    :tableData="deliveryStore.companyCrmTemplates"
                    @action:edit="editTemplate"
                    @action:delete="(id) => deleteTemplate(id)"
                />
            </div>
        </div>

        <!--    Add Contact Delivery Modal  -->
        <ContactDeliveryModal
            :dark-mode="darkMode"
            v-if="showContactDeliveryModal"
            data-key="contact_deliveries"
            :future-campaigns="true"
            @hideContactDeliveryModal="toggleContactDeliveryModal(false)"
            @update:deliveries="handleContactModalUpdate"
        />

        <!--    Add CRM Delivery Modal      -->
        <CreateUpdateCrmModal
            :dark-mode="darkMode"
            :edit-crm-id="editingCrmId"
            v-if="showCrmDeliveryModal"
            @close:modal="closeCrmModal()"
            @update:delivery="handleCrmModalUpdate"
            @update:template-delivery="handleCrmTemplateUpdate"
        />

        <ImportCrmConfigurationModal
            v-if="showImportCrmModal"
            :dark-mode="darkMode"
            @import:crm="handleImportCrmConfiguration"
            @close:modal="toggleShowImportCrmModal(false)"
        />

        <!--    Delete confirm modal    -->
        <Modal
            v-if="showDeleteConfirmModal"
            :dark-mode="darkMode"
            @confirm="confirmDelete"
            @close="cancelConfirm"
            confirm-text="Delete"
            :small="true"
        >
            <template v-slot:header>
                <p>Delete Delivery Method</p>
            </template>
            <template v-slot:content>
                <p class="whitespace-pre-wrap">
                    {{ getDeleteMessage() }}
                </p>
            </template>
        </Modal>

        <!-- Test CRM Integration Modal -->
        <CRMIntegrationTestModal
            :dark-mode="darkMode"
            :crm-id="testCRMId"
            v-if="showTestIntegrationModal"
            @modal:close="closeTestIntegrationModal"
        >
        </CRMIntegrationTestModal>
    </div>

</template>

<script>
import CustomButton from "../../../Shared/components/CustomButton.vue";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import GridTable from "../../../Shared/SlideWizard/components/GridTable.vue";
import { useWizardStore } from "../../../Shared/SlideWizard/stores/wizard.js";
import CreateEditContactModal from "../../../Shared/modules/Contacts/CreateEditContactModal.vue";
import { useFutureCampaignStore } from "../stores/future-campaigns.js";
import Modal from "../../../Shared/components/Modal.vue";
import ContactDeliveryModal from "../components/ContactDeliveryModal.vue";
import CreateUpdateCrmModal from "../components/CreateUpdateCrmModal.vue";
import { DeliveryType, useDeliveryStore } from "../stores/delivery.js";
import { useCompanyUsersStore } from "../stores/company-users.js";
import ImportCrmConfigurationModal from "../components/ImportCrmConfigurationModal.vue";
import { useProductConfigurationStore } from "../stores/product-configuration.js";
import { useSchedulesStore } from "../stores/schedules.js";
import CRMIntegrationTestModal from "../components/CRMIntegrationTestModal.vue";
import AlertsMixin from "../../../../mixins/alerts-mixin.js";
import AlertsContainer from "../../../Shared/components/AlertsContainer.vue";

export default {
    name: "CampaignDeliverySlide",
    computed: {
        DeliveryType() {
            return DeliveryType
        }
    },
    components: {
        AlertsContainer,
        CRMIntegrationTestModal,
        CreateUpdateCrmModal,
        ContactDeliveryModal,
        Modal,
        CreateEditContactModal,
        GridTable,
        LoadingSpinner,
        CustomButton,
        ImportCrmConfigurationModal,
    },
    mixins: [AlertsMixin],
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        initialSlideData: {
            type: Object,
            default: false,
        },
    },
    emits: ['update:slideInput'],
    data() {
        return {
            deliveryStore: useDeliveryStore(),
            campaignStore: useFutureCampaignStore(),
            wizardStore: useWizardStore(),
            companyUserStore: useCompanyUsersStore(),
            productConfigurationStore: useProductConfigurationStore(),
            scheduleStore: useSchedulesStore(),

            slideInputs: {
                contact_deliveries: [],
                crm_deliveries: [],
                schedules: [],
            },

            loading: false,
            saving: false,

            showContactDeliveryModal: false,
            showCrmDeliveryModal: false,
            showDeleteConfirmModal: false,
            showEditContactModal: false,
            showViewScheduleModal: false,
            showImportCrmModal: false,

            viewingSchedule: null,

            deleting: {
                type: null,
                id: null,
                name: '',
            },

            editingCrmId: null,

            campaignSchedules: [],

            contactTableSetup: {
                title: 'Company Contacts',
                subTitle: 'Decide who receives emails or texts for this campaign.',
                columns: [
                    { label: 'Name', key: 'name' },
                    { label: 'Email', key: 'email', checkbox: 'email', emitKey: 'email_active' },
                    { label: 'Cell (SMS)', key: 'cell_phone', checkbox: 'cell_phone', emitKey: 'sms_active' },
                    { label: 'Actions', key: '', emitKey: 'contact_id', editDelete: true },
                ],
            },
            campaignCrmTableSetup: {
                title: 'Campaign CRM Integrations',
                columns: [
                    { label: 'Active', key: null, checkbox: true, emitKey: 'active' },
                    { label: 'Name', key: 'display_name' },
                    { label: 'CRM', key: 'crm_type_display' },
                    { label: 'Template', key: 'template_id', defaultValue: '-', trueValue: 'Yes'  },
                    { label: 'Actions', key: '', emitKey: 'id',  editDelete: true },
                    { label: '', key: '', emitKey: 'id',  button: true, buttonText: 'Test Integration' },
                ],
            },
            crmTemplateTableSetup: {
                title: 'Company CRM Templates',
                columns: [
                    {label: 'Name', key: 'display_name'},
                    {label: 'CRM', key: 'crm_type_display'},
                    {label: 'Actions', key: '', editDelete: true, emitKey: 'id'},
                ],
            },
            schedulesTableSetup: {
                title: `Schedules`,
                columns: [
                    {label: 'Active', key: '', checkbox: true, emitKey: 'active'},
                    {label: 'Name', key: 'name'},
                ],
            },
            testCRMId: null,
            showTestIntegrationModal: false
        }
    },
    mounted() {
        this.initialize();
    },
    methods: {
        async initialize() {
            this.loading = true;

            Object.assign(this.slideInputs, this.initialSlideData);
            const initializeSchedules = this.productConfigurationStore.showSchedules
                ? this.scheduleStore.initialize
                : async () => true;

            const [companyUserResult, deliveryResult, schedulesResult] = await Promise.all([
                this.companyUserStore.initialize(),
                this.deliveryStore.initialize(),
                initializeSchedules(),
            ]);
            const errorMessage = companyUserResult.message || deliveryResult?.message || schedulesResult?.message || null;
            if (errorMessage) console.error(errorMessage);

            this.slideInputs.crm_deliveries = this.slideInputs.crm_deliveries.filter(crm => crm.display_name);
            this.slideInputs.contact_deliveries = this.slideInputs.contact_deliveries.filter(contact => contact.name);
            this.updateCampaignSchedules();
            this.schedulesTableSetup.title = `Schedules - Shadowing ${this.scheduleStore.getShadowUserName()}`

            this.loading = false;
        },
        deleteDelivery(type, deliveryId) {
            this.deleting = {
                type: type,
                id: deliveryId,
                name: this.getDeliveryName(type, deliveryId),
            }
            this.toggleShowDeleteConfirmModal(true);
        },
        async confirmDelete() {
            if (this.deleting.type === DeliveryType.Crm) {
                const targetIndex = this.slideInputs.crm_deliveries.findIndex(crm => crm.id === this.deleting.id);
                if (targetIndex !== -1) {
                    const usesTemplateId = this.slideInputs.crm_deliveries[targetIndex].template_id;
                    this.slideInputs.crm_deliveries.splice(targetIndex, 1);
                    if (usesTemplateId) {
                        this.deliveryStore.removeCampaignFromTemplate(usesTemplateId);
                    }
                }
            }
            else if (this.deleting.type === DeliveryType.Schedule) {
                this.saving = true;
                this.scheduleStore.deleteSchedule(this.deleting.id).catch(e => {
                    console.error(e);
                }).finally(() => {
                    this.saving = false;
                });
            }
            else if (this.deleting.type === DeliveryType.Template) {
                this.saving = true;
                const { status, message } = await this.deliveryStore.deleteCompanyCrmTemplate(this.deleting.id);
                if (!status)
                    this.showAlert('error', message ?? "An error occurred deleting the Template.");
                else
                    this.showAlert('success', "The template was successfully removed");

                // Remove any Deliverers from the UI that were using this template
                this.handleCrmTemplateUpdate(false, this.deleting.id);
                this.saving = false;
            }
            else {
                const targetIndex = this.slideInputs.contact_deliveries.findIndex(contact => contact.contact_id === this.deleting.id);
                if (targetIndex !== -1) this.slideInputs.contact_deliveries.splice(targetIndex, 1);
            }

            this.cancelConfirm();
        },
        deleteTemplate(templateId) {
            this.deleting = {
                type: DeliveryType.Template,
                id: templateId,
                name: this.getTemplateName(templateId),
            };
            this.toggleShowDeleteConfirmModal(true);
        },
        cancelConfirm()  {
            this.deleting = {
                type: null,
                id: null,
                name: '',
            }
            this.toggleShowDeleteConfirmModal(false);
        },
        editCrm(crmId) {
            this.deliveryStore.editingDeliveryType = DeliveryType.Crm;
            const targetCrm = this.slideInputs.crm_deliveries.find(crm => crm.id === crmId);
            if (targetCrm) {
                this.deliveryStore.editingCrmConfiguration = targetCrm;
                this.editingCrmId = crmId;
                this.toggleCrmDeliveryModal(true);
            }
        },
        createTemplate() {
            this.deliveryStore.editingDeliveryType = DeliveryType.Template;
            this.toggleCrmDeliveryModal(true);
        },
        editTemplate(templateId) {
            this.deliveryStore.editingDeliveryType = DeliveryType.Template;
            const targetTemplate = this.deliveryStore.companyCrmTemplates.find(template => template.id === templateId);
            if (targetTemplate) {
                this.deliveryStore.editingCrmConfiguration = targetTemplate;
                this.editingCrmId = templateId;
                this.toggleCrmDeliveryModal(true);
            }
        },
        handleCrmTemplateUpdate(sync, payload) {
            if (sync && typeof(payload) === 'object')
                this.handleCrmModalUpdate(payload);
            else if (typeof payload === 'number')
                this.slideInputs.crm_deliveries = this.slideInputs.crm_deliveries.filter(delivery => delivery.template_id !== payload);
        },
        closeCrmModal() {
            this.editingCrmId = null;
            this.toggleCrmDeliveryModal(false);
        },
        editContact(editContactId) {
            this.companyUserStore.editCompanyUser(editContactId);
            this.toggleShowEditContactModal(true);
        },
        closeEditContactModal() {
            this.companyUserStore.clearEditing();
            this.toggleShowEditContactModal(false);
        },
        handleContactModalUpdate(newValues) {
            this.slideInputs.contact_deliveries.push(...newValues);
        },
        handleCrmModalUpdate(newValue) {
            const targetCrmIndex = newValue.id
                ? this.slideInputs.crm_deliveries.findIndex(crm => crm.id === newValue.id)
                : -1;

            if (targetCrmIndex >= 0)
                this.slideInputs.crm_deliveries[targetCrmIndex] = { ...newValue }
            else
                this.slideInputs.crm_deliveries.push({ ...newValue });

            this.toggleCrmDeliveryModal(false);
        },
        handleInputUpdate(newValue, inputKey) {
            this.$emit('update:slideInput', newValue, inputKey);
        },
        toggleModal(modalToggle, show = false) {
            this[modalToggle] = show === undefined
                ? !this[modalToggle]
                : show;
        },
        handleImportCrmConfiguration(importId) {
            const target = this.deliveryStore.crmImportOptions.find(crm => crm.id === importId);
            if (target) {
                const clone = JSON.parse(JSON.stringify(target));
                clone.id = `import-${Date.now()}`;
                this.slideInputs.crm_deliveries.push(clone);
            }
        },
        getDeliveryName(type, id) {
            return type === DeliveryType.Crm
                ? this.slideInputs.crm_deliveries.find(crm => crm.id === id)?.display_name ?? 'this CRM delivery'
                : this.slideInputs.contact_deliveries.find(contact => contact.contact_id === id)?.name ?? 'this contact delivery';
        },
        updateCampaignSchedules() {
            this.campaignSchedules = (this.scheduleStore.schedules ?? []).map(schedule => {
                return {
                    ...schedule,
                    active: (this.slideInputs.schedules ?? []).includes(schedule.id),
                }
            });
        },
        updateActiveSchedules() {
            this.slideInputs.schedules = this.campaignSchedules.reduce((output, schedule) => {
                return schedule.active
                    ? [...output, schedule.id]
                    : output;
            }, []);
        },
        editSchedule(id) {
            const targetSchedule = this.scheduleStore.schedules.find(schedule => schedule.id === id);
            if (targetSchedule) {
                this.viewingSchedule = JSON.parse(JSON.stringify(targetSchedule));
                this.toggleShowViewScheduleModal(true)
            }
        },
        getDeleteMessage() {
            if (this.deleting.type === DeliveryType.Crm) {
                const deliveryUsesTemplateId = this.slideInputs.crm_deliveries.find(delivery => delivery.id === this.deleting.id)?.template_id ?? null;
                return deliveryUsesTemplateId
                    ? `Are you sure you wish to delete the CRM delivery method "${this.deleting.name}"?\n\nThe CRM Template will not be removed. It is used in ${Math.max(this.getTemplateCampaignCount(deliveryUsesTemplateId) - 1, 0)} other Campaign(s).`
                    : `Are you sure you wish to delete the CRM Delivery method "${this.deleting.name}"?\n\nThis will completely remove this Campaign CRM delivery and its settings.`;
            }
            else if (this.deleting.type === DeliveryType.Contact)
                return `Are you sure you wish to delete the Contact delivery method for "${this.deleting.name}"?\nThis will remove them as a delivery option for this Campaign only - it will not delete the contact.`;
            else if (this.deleting.type === DeliveryType.Template)
                return `Are you sure you wish to delete ${this.deleting.name}?
                \nThis will remove the template, and all Campaign deliveries linked to this template.
                \nThere are ${this.getTemplateCampaignCount(this.deleting.id)} Campaign(s) using this template.`;
        },
        getTemplateName(id) {
            return this.deliveryStore.companyCrmTemplates.find(template => template.id === id)?.display_name
                ?? 'this CRM Template';
        },
        getTemplateCampaignCount(id) {
            return this.deliveryStore.companyCrmTemplates.find(template => template.id === id)?.campaigns?.length ?? 0;
        },
        deleteSchedule(id) {
            const targetSchedule = this.scheduleStore.schedules.find(schedule => schedule.id === id);
            if (targetSchedule) {
                this.deleting = {
                    type: DeliveryType.Schedule,
                    id: targetSchedule.id,
                    name: targetSchedule.name,
                }
                this.toggleShowDeleteConfirmModal(true)
            }
        },
        newSchedule() {
            this.viewingSchedule = null;
            this.toggleShowViewScheduleModal(true);
        },
        customValidation() {
            const activeContacts = this.slideInputs.contact_deliveries.reduce((total, contact) => total + (contact.sms_active || contact.email_active) ? 1 : 0, 0);
            const activeCrm = this.slideInputs.crm_deliveries.reduce((total, crm) => total + (crm.active ? 1 : 0), 0);
            let message = "At least one delivery method must be active.";

            if (this.productConfigurationStore.showSchedules && !this.slideInputs.schedules.length) {
                message = 'At least one schedule must be selected. If you no longer wish to receive appointments through this campaign, you can pause or delete it.';
            }

            return (activeContacts + activeCrm) > 0
                ? { valid: true }
                : { valid: false, errorBag: [ message ], message }
        },
        toggleContactDeliveryModal(show) { this.toggleModal('showContactDeliveryModal', show); },
        toggleCrmDeliveryModal(show) { this.toggleModal('showCrmDeliveryModal', show); },
        toggleShowDeleteConfirmModal(show) { this.toggleModal('showDeleteConfirmModal', show); },
        toggleShowEditContactModal(show) { this.toggleModal('showEditContactModal', show); },
        toggleShowViewScheduleModal(show) { this.toggleModal('showViewScheduleModal', show); },
        toggleShowImportCrmModal(show) { this.toggleModal('showImportCrmModal', show); },
        closeTestIntegrationModal() {
            this.showTestIntegrationModal = false;
            this.testCRMId = null;
        },
        handleCRMIntegrationTest(id) {
            this.showTestIntegrationModal = true;
            this.testCRMId = id;
        }
    },
    watch: {
        ['slideInputs.contact_deliveries']: {
            deep: true,
            handler(newValue) {
                this.handleInputUpdate(newValue, 'contact_deliveries');
            },
        },
        ['slideInputs.crm_deliveries']: {
            deep: true,
            handler(newValue) {
                this.handleInputUpdate(newValue, 'crm_deliveries');
            },
        },
        ['slideInputs.schedules']: {
            deep: true,
            handler(newValue) {
                this.handleInputUpdate(newValue, 'schedules');
            }
        },
        ['scheduleStore.schedules']: {
            deep: true,
            handler() {
                this.updateCampaignSchedules();
            },
        },
    },
    expose: ['customValidation'],
}
</script>
