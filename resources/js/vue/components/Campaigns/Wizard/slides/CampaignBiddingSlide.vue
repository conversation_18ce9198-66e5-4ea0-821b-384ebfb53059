<template>
    <div class="relative min-h-[48rem]">
        <LoadingSpinner v-if="loading" :dark-mode="darkMode" />
        <BiddingTable
            v-else
            :initial-data="slideInputs.location_bids"
            @update:bidding-input="(newValue) => handleInputUpdate(newValue, 'location_bids')"
            :dark-mode="darkMode"
        />
    </div>
</template>

<script>
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import BiddingTable from "../components/BiddingTable.vue";

export default {
    name: "CampaignBiddingSlide",
    components: {
        BiddingTable,
        LoadingSpinner
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        initialSlideData: {
            type: Object,
            default: null,
        },
    },
    emits: ['update:slideInput'],
    data() {
        return {
            slideInputs: {
                location_bids: {},
            },
            loading: false,
        }
    },
    mounted() {
        this.initialize();
    },
    methods: {
        async initialize() {
            this.loading = true;
            Object.assign(this.slideInputs.location_bids, this.initialSlideData.location_bids ?? {});
            this.loading = false;
        },
        handleInputUpdate(newValue, inputKey) {
            this.$emit('update:slideInput', newValue, inputKey);
        },
    },
    watch: {
        slideInputs: {
            deep: true,
            handler(newValue) {
                this.handleInputUpdate(newValue, 'key');
            }
        }
    }
}
</script>
