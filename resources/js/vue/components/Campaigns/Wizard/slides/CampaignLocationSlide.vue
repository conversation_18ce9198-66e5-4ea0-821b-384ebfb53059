<template>
    <div>
        <div v-if="campaignStore.zipCodeCampaign || campaignStore.unrestrictedZipCodes">
            <!--    HEADER BUTTONS    -->
            <div class="flex items-center gap-x-6 pb-6">
                <CustomButton
                    color="primary-outline"
                    @click="toggleZipCodeUploadModal(true)"
                    :dark-mode="darkMode"
                >
                    Upload File
                </CustomButton>
                <CustomButton
                    color="primary-outline"
                    @click="toggleZipCodeRadiusModal(true)"
                    :dark-mode="darkMode"
                >
                    Add Zip Codes By Radius
                </CustomButton>
            </div>
        </div>

        <LoadingSpinner v-if="loading" :dark-mode="darkMode" />
        <div v-else>
            <!--    MAIN LOCATION NAVIGATION    -->
            <LocationCheckboxSelect
                v-if="!loading && slideInputs.zip_codes"
                :stores-initialized="storesInitialized"
                :model-value="slideInputs.zip_codes"
                :imported-zip-codes="importedCodes"
                input-key="zip_codes"
                @update:model-value="handleInputUpdate"
                :dark-mode="darkMode"
            />

            <!--    Zip Radius modal    -->
            <ZipCodeRadiusModal
                v-if="showZipCodeRadiusModal"
                @hideZipRadiusModal="toggleZipCodeRadiusModal"
                @addZipCodesFromModal="addZipCodesFromModal"
                :dark-mode="darkMode"
            />
            <!--    Zip Upload modal    -->
            <ZipCodeUploadModal
                v-if="showZipCodeUploadModal"
                @hideZipUploadModal="toggleZipCodeUploadModal(false)"
                @addZipCodesFromModal="addZipCodesFromModal"
                :dark-mode="darkMode"
            />
            <Modal
                v-if="zipCodeErrorMessage"
                :dark-mode="darkMode"
                :small="true"
                :hide-confirm="true"
                close-text="Ok"
                @close="() => zipCodeErrorMessage = null"
            >
                <template v-slot:header>
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 6.75V15m6-6v8.25m.503 3.498l4.875-2.437c.381-.19.622-.58.622-1.006V4.82c0-.836-.88-1.38-1.628-1.006l-3.869 1.934c-.317.159-.69.159-1.006 0L9.503 3.252a1.125 1.125 0 00-1.006 0L3.622 5.689C3.24 5.88 3 6.27 3 6.695V19.18c0 .836.88 1.38 1.628 1.006l3.869-1.934c.317-.159.69-.159 1.006 0l4.994 2.497c.317.158.69.158 1.006 0z" />
                        </svg>
                        <h5 class="text-md ml-2">Zip Code alert</h5>
                    </div>
                </template>
                <template v-slot:content>
                    <div class="px-5 p-3 whitespace-pre-wrap">
                        {{ zipCodeErrorMessage }}
                    </div>
                </template>
            </Modal>
        </div>
    </div>
</template>

<script>
import { useLocalityDataStore } from "../stores/locality-data.js";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import ZipCodeRadiusModal from "../components/ZipCodeRadiusModal.vue";
import ZipCodeUploadModal from "../components/ZipCodeUploadModal.vue";
import LocationCheckboxSelect from "../components/LocationCheckboxSelect.vue";
import NestedCheckboxSelectNavigation from "../../../Shared/SlideWizard/components/NestedCheckboxSelectNavigation.vue";
import { useFutureCampaignStore } from "../stores/future-campaigns.js";
import Modal from "../../../Shared/components/Modal.vue";

export default {
    name: "CampaignLocationSlide",
    components: {
        Modal,
        NestedCheckboxSelectNavigation,
        LocationCheckboxSelect,
        ZipCodeUploadModal,
        ZipCodeRadiusModal,
        LoadingSpinner,
        CustomButton

    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        initialSlideData: {
            type: Object,
            default: () => ({}),
        },
    },
    emits: ['update:slideInput'],
    data() {
        return {
            localityStore: useLocalityDataStore(),
            campaignStore: useFutureCampaignStore(),
            storesInitialized: false,
            slideInputs: { zip_codes: null, },
            importedCodes: { append: true, zipCodes: [] },
            loading: true,
            saving: false,
            showZipCodeRadiusModal: false,
            showZipCodeUploadModal: false,
            zipCodeErrorMessage: null,
        }
    },
    mounted() {
        this.initialize();
    },
    methods: {
        async initialize() {
            Object.assign(this.slideInputs, this.initialSlideData);

            await this.localityStore.initialize();
            if (!this.localityStore.storeInitialized) {
                console.error('Failed to fetch location data.');
            }
            this.storesInitialized = true;
            this.loading = false;
            this.handleInputUpdate(this.slideInputs.zip_codes, 'location');
        },
        toggleZipCodeUploadModal(show) {
            if (show === undefined)
                this.showZipCodeUploadModal = !this.showZipCodeUploadModal;
            else
                this.showZipCodeUploadModal = show;
        },
        toggleZipCodeRadiusModal(show) {
            if (show === undefined)
                this.showZipCodeRadiusModal = !this.showZipCodeRadiusModal;
            else
                this.showZipCodeRadiusModal = show;
        },
        async addZipCodesFromModal(payload, appendZipCodes) {
            if (this.saving) return;

            if (!this.campaignStore.zipCodeCampaign) {
                this.zipCodeErrorMessage = "Zip codes can only be added to zip-targeted campaigns."
            }

            this.saving = true;
            const zipCodeIds = payload.map(zipCode => zipCode.id);
            const { status, data, message } = await this.localityStore.validateImportedZipCodes(zipCodeIds);
            if (!status || !data) {
                this.zipCodeErrorMessage = message;
            }
            else {
                const { valid_zip_codes, invalid_zip_codes} = data;
                const validLocations = payload.filter(location => valid_zip_codes.includes(location.id));
                const validLocationCount = validLocations.length;
                const invalidLocationCount = invalid_zip_codes.length;

                if (validLocationCount) {
                    this.importedCodes = {
                        append: !!appendZipCodes,
                        zipCodes: validLocations,
                    };

                    if (invalidLocationCount) {
                        this.zipCodeErrorMessage = `${validLocationCount} zip code(s) successfully added.\n${invalidLocationCount} zip code(s) ignored (invalid or outside the approved counties for this campaign).
                        \nThe following zip codes were not added:
                        \n${invalid_zip_codes.join("\n")}`;
                    }
                }
                else {
                    this.zipCodeErrorMessage = "The supplied zip codes were invalid, or not in an approved county for this campaign.";
                }
            }
            this.saving = false;

            this.toggleZipCodeUploadModal(false);
            this.toggleZipCodeRadiusModal(false);
        },
        handleInputUpdate(newValue, inputKey) {
            this.campaignStore.updateActiveZipCodes({ ...newValue });
            this.$emit('update:slideInput', newValue, inputKey);
        },
    },
}
</script>
