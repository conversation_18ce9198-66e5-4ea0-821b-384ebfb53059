<template>
    <simple-card :dark-mode="darkMode">
        <div class="flex flex-col gap-1 p-4">
            <div class="flex gap-2">
                <p class="text-lg font-semibold flex-1 line-clamp-2 h-14">{{ event?.title }}</p>
                <display-date-range
                    v-if="event?.start_time || event?.end_time"
                    :start-date="event?.start_time"
                    :end-date="event?.end_time"
                ></display-date-range>
            </div>
            <div class="flex flex-col gap-1">
                <div class="flex items-center">
                    <p class="font-semibold flex-1">Participants Invited ({{event.attendees.length}})</p>
                    <simple-icon
                        :icon="showParticipantsVisibility ? simpleIcon.icons.EYE_SLASH : simpleIcon.icons.EYE"
                        clickable
                        @click="toggleParticipantsVisibility"
                        :tooltip="showParticipantsVisibility ? 'Show invited participants' : 'Hide invited participants'"
                    />
                </div>
                <div v-show="showParticipantsVisibility" v-for="invitedParticipant in event.attendees" class="flex gap-1 pl-2 items-center">
                    <p class="flex-1">{{invitedParticipant.email}}</p>
                    <badge>{{event.status}}</badge>
                </div>
            </div>
            <div v-if="event?.conferences?.length === 0">
                <p>No conference found</p>
            </div>
            <div v-else v-for="conference in event?.conferences" class="flex flex-col gap-1">
                <div class="flex items-center gap-1 font-semibold">
                    <p>Session</p>
                    <display-date-range
                        :start-date="conference?.start_time"
                        :end-date="conference?.end_time"
                        compact
                    ></display-date-range>
                </div>
                <div class="flex flex-col pl-2 gap-1">
                    <div>
                        <p class="font-semibold">Participants</p>
                        <div class="flex flex-col gap-1" v-for="participant in conference.participants">
                            <div class="flex items-center gap-2">
                                <div class="flex items-center gap-2">
                                    <simple-icon :icon="simpleIcon.icons.USER_CIRCLE"/>
                                    <p>{{ participant.name }}</p>
                                </div>
                                <display-date-range
                                    :start-date="participant.earliest_start_time"
                                    :end-date="participant.latest_end_time"
                                    compact
                                />
                                <badge>
                                    {{ timeHelper.formatDuration(participant.duration_in_seconds) }}
                                </badge>
                            </div>
                        </div>
                    </div>
                    <div>
                        <p class="font-semibold">Artifacts</p>
                        <div v-if="conference?.transcripts?.length > 0">
                            <div v-for="transcript in conference.transcripts" class="flex gap-1 flex-col">
                                <a class="cursor-pointer text-blue-500" @click="toggleTranscriptModal(conference, transcript)">
                                    Show Transcript
                                </a>
                                <a class="cursor-pointer text-blue-500" @click="downloadTranscript(event, transcript)">
                                    Download Transcript
                                </a>
                            </div>
                        </div>
                        <div v-else>No transcript found</div>
                    </div>
                </div>
            </div>
        </div>
        <display-conference-transcript
            v-if="showTranscriptModal"
            :transcript="selectEvent?.transcript"
            :participants="selectEvent?.participants"
            @close="toggleTranscriptModal()"
        />
    </simple-card>
</template>

<script>
import DisplayDateRange from "../Shared/components/DisplayDateRange.vue";
import Badge from "../Shared/components/Badge.vue";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import SimpleCard from "../MarketingCampaign/SimpleCard.vue";
import useTimeHelper from "../../../composables/useTime.js";
import useSimpleIcon from "../../../composables/useSimpleIcon.js";
import useDateHelper from "../../../composables/useDateHelper.js";
import {useFileDownloader} from "../../../composables/fileDownloader.js";
import {slugify} from "../Shared/services/strings.js";
import DisplayConferenceTranscript from "./DisplayConferenceTranscript.vue";

const dateHelper = useDateHelper()
const fileDownloader = useFileDownloader();

export default {
    name: "DemoCard",
    components: {DisplayConferenceTranscript, SimpleCard, SimpleIcon, Badge, DisplayDateRange},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        event: {
            type: Object,
            default: {}
        }
    },
    data() {
        return {
            downloadingTranscript: {},
            showTranscriptModal: false,
            selectEvent: {},
            showParticipantsVisibility: false,
        }
    },
    computed: {
        simpleIcon() {
            return useSimpleIcon()
        },
        timeHelper() {
            return useTimeHelper()
        }
    },
    methods: {
        toggleParticipantsVisibility(){
            this.showParticipantsVisibility = !this.showParticipantsVisibility
        },
        parseTranscript(transcript) {
            return transcript.entries
                .sort((a, b) => new Date(a.start_time) - new Date(b.start_time))
                .map(entry => ({
                    ...entry,
                    start_time: dateHelper.parseDate(entry.start_time).toFormat("MMM d, yyyy h:mm a"),
                    end_time: dateHelper.parseDate(entry.end_time).toFormat("MMM d, yyyy h:mm a"),
                }))
                .map(entry => `${entry.participant_name}\n${entry.start_time} --> ${entry.end_time}\n${entry.text.trim()}`)
                .join('\n'.repeat(2))
        },

        initTranscriptString(event) {
            return [
                `${event.title} - ${dateHelper.parseDate(event.start_time).toFormat("MMM d, yyyy h:mm a")} - ${dateHelper.parseDate(event.end_time).toFormat("MMM d, yyyy h:mm a")}`,
            ].join('\n'.repeat(2))
        },

        toggleTranscriptModal(event = null, transcript = null) {
            this.selectEvent = {transcript, participants: event?.participants}
            this.showTranscriptModal = !this.showTranscriptModal
        },

        downloadTranscript(event, transcript) {
            this.downloadingTranscript[transcript.id] = true

            const transcriptText = this.initTranscriptString(event)

            const parsedTranscript = this.parseTranscript(transcript)

            const content = `${transcriptText}\n\n${parsedTranscript}`;

            fileDownloader.downloadFile(content, `transcript-${slugify(event.title)}.txt`)

            this.downloadingTranscript[transcript.id] = false
        }
    }
}
</script>
