<template>
    <CustomButton>
        Show Transcript
    </CustomButton>
</template>

<script>
import DisplayDateRange from "../Shared/components/DisplayDateRange.vue";
import Badge from "../Shared/components/Badge.vue";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import SimpleCard from "../MarketingCampaign/SimpleCard.vue";
import useTimeHelper from "../../../composables/useTime.js";
import useSimpleIcon from "../../../composables/useSimpleIcon.js";
import CustomButton from "../Shared/components/CustomButton.vue";

export default {
    name: "DemoCard",
    components: {CustomButton, SimpleCard, SimpleIcon, Badge, DisplayDateRange},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        demo: {
            type: Object,
            default: {}
        }
    },
    computed: {
        simpleIcon() {
            return useSimpleIcon()
        }
    },
    methods: {
        ...useTimeHelper(),
    }
}
</script>
