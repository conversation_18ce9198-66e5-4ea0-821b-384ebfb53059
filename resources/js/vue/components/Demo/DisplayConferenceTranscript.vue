<template>
    <Modal
        :restrict-width="false"
        hide-confirm
        :dark-mode="darkMode"
        :full-width="false"
        @close="$emit('close-modal')"
    >
        <template v-slot:header>
            Transcript
        </template>
        <template v-slot:content>
            <div class="flex flex-col gap-2">
                <event-log :data="messages"/>
            </div>
        </template>
    </Modal>
</template>
<script>
import Modal from "../Shared/components/Modal.vue";
import IncomingMessage from "../LeadProcessingNotifications/components/IncomingMessage.vue";
import OutgoingMessage from "../LeadProcessingNotifications/components/OutgoingMessage.vue";
import EventLog from "../Companies/components/Territory/components/shared/EventLog.vue";
import useSimpleIcon from "../../../composables/useSimpleIcon.js";

export default {
    name: "DisplayConferenceTranscript",
    components: {EventLog, OutgoingMessage, IncomingMessage, Modal},
    props: {
        darkMode: {
            type: <PERSON><PERSON><PERSON>
        },
        transcript: {
            type: Object,
            default: {}
        },
        participants: {
            type: Array,
            default: []
        },
    },
    computed: {
        simpleIcon() {
            return useSimpleIcon()
        },
        messages() {
            const sessionLogs = this.participants.map(e => {
                return [
                    {
                        title: `${e.name} joined the conference`,
                        date: e.earliest_start_time,
                        icon: this.simpleIcon.icons.ARROW_RIGHT_END_ON_RECTANGLE,
                        icon_color: this.simpleIcon.colors.BLUE
                    },
                    {
                        title: `${e.name} left the conference`,
                        date: e.latest_end_time,
                        icon: this.simpleIcon.icons.ARROW_LEFT_END_ON_RECTANGLE,
                        icon_color: this.simpleIcon.colors.RED
                    },
                ]
            }).flat();

            const messages = this.transcript.entries.map(e => ({
                ...e,
                title: e.participant_name,
                date: e.start_time,
                payload: [e.text],
            }))

            return [
                ...sessionLogs,
                ...messages
            ].sort((a, b) => new Date(a.date) - new Date(b.date))
                .map((a) => ({...a, date: this.$filters.dateFromTimestamp(a.date, 'usWithTime')}))
        }
    }
}
</script>
