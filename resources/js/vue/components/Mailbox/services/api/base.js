class BaseApiService {
    constructor(serviceName = "BaseApiService") {
        this.serviceName = serviceName;
    }

    _throwUnimplementedError(fn) {
        throw new Error("Not implemented error. `" + this.serviceName + "::" + fn + "()`");
    }

    listEmails() {
        this._throwUnimplementedError(this.listEmails.name);

        return new Promise((resolve, reject) => reject());
    }

    modifyEmail() {
        this._throwUnimplementedError(this.modifyEmail.name);

        return new Promise((resolve, reject) => reject());
    }

    sendEmail() {
        this._throwUnimplementedError(this.sendEmail.name);

        return new Promise((resolve, reject) => reject());
    }

    getPermission() {
        this._throwUnimplementedError(this.getPermission.name);

        return new Promise((resolve, reject) => reject());
    }

    syncMailbox() {
        this._throwUnimplementedError(this.syncMailbox.name);

        return new Promise((resolve, reject) => reject());
    }

    replyEmail() {
        this._throwUnimplementedError(this.replyEmail.name);

        return new Promise((resolve, reject) => reject());
    }

    forwardEmail() {
        this._throwUnimplementedError(this.forwardEmail.name);

        return new Promise((resolve, reject) => reject());
    }
    deleteEmail() {
        this._throwUnimplementedError(this.deleteEmail.name);

        return new Promise((resolve, reject) => reject());
    }

    getDetailedEmail() {
        this._throwUnimplementedError(this.getDetailedEmail.name);

        return new Promise((resolve, reject) => reject());
    }

    getUserEmailSignature(){
        this._throwUnimplementedError(this.getUserEmailSignature.name);

        return new Promise((resolve, reject) => reject());
    }
}

export { BaseApiService };
