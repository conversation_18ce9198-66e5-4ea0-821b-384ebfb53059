<template>
    <div class="w-full h-full">
            <div class="flex-auto pt-3 relative p-3 border rounded-lg h-full"
             :class="{'bg-light-module text-slate-900': !darkMode, 'bg-dark-background text-white': darkMode}">
            <div class="grid grid-cols-12 gap-5 h-full">
                <div class="col-span-2 h-fit">
                    <menu-wrapper>
                        <menu-option
                            v-for="option in menuOptions"
                            :label="option.name"
                            :selected="isTabSelected(option.id)"
                            @option-clicked="handleMenuOptionSelected(option.id)"
                            :disabled="isOptionsDisabled"
                        >
                            <template v-slot:icon-left>
                                <simple-icon :icon="option.icon"></simple-icon>
                            </template>
                            <template v-slot:icon-right >
                                <simple-icon :class="isTabSelected(option.id) ? 'visible' : 'invisible'" :color="simpleIcon.colors.BLUE"  :icon="simpleIcon.icons.CHEVRON_RIGHT"></simple-icon>
                            </template>
                        </menu-option>
                    </menu-wrapper>
                    <menu-wrapper v-if="customLabels.length > 0">
                        <menu-option
                            v-for="label in customLabels"
                            :label="label.name"
                            :selected="isTabSelected(label.id)"
                            @option-clicked="handleMenuOptionSelected(label.id)"
                            :disabled="isOptionsDisabled"
                        >
                            <template v-slot:icon-left>
                                <simple-icon :icon="label.icon"></simple-icon>
                            </template>
                            <template v-slot:icon-right >
                                <simple-icon :class="isTabSelected(label.id) ? 'visible' : 'invisible'"  :icon="simpleIcon.icons.CHEVRON_RIGHT"></simple-icon>
                            </template>
                        </menu-option>
                    </menu-wrapper>
                </div>
                <email-list :api-driver="apiDriver"/>
                <CreateTaskForCompanyModal
                    v-if="mailboxStore.showCreateTaskModal"
                    @close="mailboxStore.handleCreateTaskModalClose"
                    :dark-mode="darkMode"
                    :task-note="mailboxStore.taskNote"
                />
                <AddNoteModal
                    v-if="mailboxStore.showAddNoteModal"
                    @close="mailboxStore.handleAddNoteModalClose"
                    :dark-mode="darkMode"
                    :api-driver="apiDriver"
                    :relation-id="mailboxStore.noteRelationId"
                    :relation-type="note.relationTypes.MAILBOX_EMAILS"
                />
                <Modal
                    v-if="mailboxStore.showEmailDeletionModal"
                    small
                    confirm-text="Delete"
                    close-text="Cancel"
                    :dark-mode="darkMode"
                    @close="mailboxStore.showEmailDeletionModal = false"
                    @confirm="mailboxStore.handleDeleteEmailConfirm"
                >
                    <template v-slot:header>
                        <h4 class="text-xl font-medium">Email deletion</h4>
                    </template>
                    <template v-slot:content>
                        <p>Are you sure you want to delete ?</p>
                        <div v-html="mailboxStore?.selectedEmail?.email?.subject"></div>
                    </template>
                </Modal>
            </div>
        </div>
    </div>
</template>

<script>

import EmailList from "./components/EmailList.vue";
import SimpleIcon from "./components/SimpleIcon.vue";
import {useMailboxStore} from "../../../stores/mailbox/mailbox";
import useSimpleIcon from "../../../composables/useSimpleIcon";
import MenuWrapper from "./components/MenuWrapper.vue";
import MenuOption from "./components/MenuOption.vue";
import AddNoteModal from "../Notes/components/AddNoteModal.vue";
import CreateTaskForCompanyModal from "../Shared/components/CreateTaskForCompanyModal.vue";
import useNote from "../Notes/composables/useNote";
import Modal from "../Shared/components/Modal.vue";
import {useMailboxComposeEmailStore} from "../../../stores/mailbox/composeEmail";

const simpleIcon = useSimpleIcon()
const note = useNote()

export default {
    components: {
        Modal,
        CreateTaskForCompanyModal,
        AddNoteModal,
        MenuOption,
        MenuWrapper,
        SimpleIcon,
        EmailList
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        apiDriver: {
            type: String,
        }
    },

    computed: {
        isOptionsDisabled(){
            return this.mailboxStore.isMailboxSyncing && this.mailboxStore.paginatedEmailsResponse?.data?.length === 0
        }
    },

    data() {
        return {
            mailboxStore: useMailboxStore(),
            composeEmailStore: useMailboxComposeEmailStore(),
            simpleIcon,
            note,
            menuOptions: [
                {icon: simpleIcon.icons.INBOX,          name: "Inbox",      id:"inbox"},
                {icon: simpleIcon.icons.PAPER_AIRPLANE, name: "Sent",       id:"sent"},
                {icon: simpleIcon.icons.STAR,           name: "Starred",    id:"starred"},
                {icon: simpleIcon.icons.FLAG,           name: "Important",  id:"important"},
                {icon: simpleIcon.icons.ARCHIVE_BOX,    name: "Archived",   id:"archived"},
                {icon: simpleIcon.icons.ENVELOPE,       name: "All Mail",   id:"all-mail"},
            ],
            customLabels: [],
        }
    },

    async created() {
        await this.checkUserHasGrantedAccess();
        await this.mailboxStore.getUserEmailSignature();
        await this.getUserCustomLabels();
    },

    methods: {
        isTabSelected(tab) {
            return this.mailboxStore.selectedTab === tab
        },

        handleMenuOptionSelected(labelId) {
            this.mailboxStore.setCompactListFalse();
            if (this.composeEmailStore.getPosition() === this.composeEmailStore.positions.EMBEDDED) {
              this.composeEmailStore.resetComposeEmailData()
            }
            this.mailboxStore.selectedTab = labelId
        },

        async checkUserHasGrantedAccess() {
            const response = await this.mailboxStore.api.getPermission();
            if (!response.data.data.has_token && response.data.data.url) {
                window.location.replace(response.data.data.url)
            }
        },

        async getUserCustomLabels() {
            const response = await this.mailboxStore.api.getCustomLabels();
            this.customLabels = response.data.data.labels.map(function (label) {
                return {icon: simpleIcon.icons.TAG, name: label.name, id: label.id}
            });
        },
    },

    watch: {
        selectedTab() {
            this.mailboxStore.setSelectedEmail(null)
        }
    }
};

</script>
