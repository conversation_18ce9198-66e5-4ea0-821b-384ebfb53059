<template>
    <div class="flex p-6 border rounded-lg flex-col gap-6" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
        <h5 class="text-xl font-medium pb-0 leading-none mr-5">Mailbox</h5>
        <div v-if="loading" class="flex-1 justify-center items-center flex">
            <loading-spinner/>
        </div>
        <div v-else-if="!loading && !errorMessage" class="flex-1 gap-6 flex flex-col">
            <CustomButton v-if="tokenUrl" @click="openSyncPage">
                Click to enroll
            </CustomButton>
            <p v-else>
                Enrolled to mailbox
            </p>
        </div>
        <p v-else class="text-red-500">
            {{ errorMessage }}
        </p>
    </div>
</template>
<script>
import CustomButton from "../Shared/components/CustomButton.vue";
import {useMailboxStore} from "../../../stores/mailbox/mailbox.js";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";

export default {
    name: 'MailboxSyncCard',
    components: {LoadingSpinner, CustomButton},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            loading: true,
            mailboxStore: useMailboxStore(),
            tokenUrl: null,
            errorMessage: null,
        }
    },
    mounted() {
        this.checkUserHasGrantedPermission()
    },
    methods: {
        openSyncPage() {
            if (!this.tokenUrl) {
                this.errorMessage = 'No URL found'
                return
            }

            window.open(this.tokenUrl, '_blank')
        },
        handleError(err){
            if (err?.response?.status === 403) {
                this.errorMessage = 'Feature unavailable while impersonating'
            } else {
                this.errorMessage = 'Server Error'
            }
        },
        async checkUserHasGrantedPermission() {
            this.loading = true
            try {
                this.tokenUrl = await this.mailboxStore.getUrlToUserGrantAccess()
            } catch (err) {
                this.handleError(err)
            }
            this.loading = false
        }
    }
}
</script>
