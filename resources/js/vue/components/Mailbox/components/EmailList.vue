<template>
    <div class="col-span-10 h-full flex overflow-scroll flex-col" :class="{'bg-light-module': !darkMode, 'bg-dark-module': darkMode}">
        <div class="flex flex-col mb-2 relative h-full">
            <EmailSearchBar />
            <EmailListToolbar v-if="!mailboxStore.compactList"/>
            <LoadingSpinner v-if="mailboxStore.loadingEmailsPaginated && !mailboxStore.compactList" />
            <EmailListCompact v-else-if="mailboxStore.compactList"></EmailListCompact>
            <div
                v-else
                class="overflow-scroll border rounded-lg h-full"
            >
                <div
                    v-if="mailboxStore.paginatedEmailsResponse?.data?.length > 0"
                    v-for="(userEmail, idx) in mailboxStore.paginatedEmailsResponse?.data"
                    @click="this.mailboxStore.handleEmailSelected(userEmail, idx)"
                    class="grid grid-cols-12 gap-3 px-4 py-2 cursor-pointer items-center border-b border-gray-300 hover:border-b-2 relative bg-gray-50"
                    :class="[!userEmail.is_read ? 'font-bold' : '']"
                    @mouseenter="showActionButtons = idx"
                    @mouseleave="showActionButtons = null"
                >
                    <div class="col-span-1 flex items-center gap-4">
                        <input
                            :class="[!darkMode ? 'hover:bg-grey-50 border-grey-200' : 'bg-dark-background hover:bg-dark-175 border-blue-400']"
                            class="rounded-sm w-5 h-5 cursor-pointer border"
                            @click.stop
                            :id="userEmail.uuid"
                            :value="userEmail.uuid"
                            v-model="mailboxStore.bulkSelect"
                            type="checkbox"
                        >
                        <SimpleIcon
                            :icon="userEmail.is_starred ? simpleIcon.icons.STAR_FILL : simpleIcon.icons.STAR"
                            :color="simpleIcon.colors.YELLOW"
                            :tooltip="userEmail.is_starred ? 'Starred' : 'Not starred'"
                            @click.stop="mailboxStore.toggleStar(userEmail, userEmail.is_starred)"
                        />
                        <SimpleIcon
                            :icon="userEmail.is_important ? simpleIcon.icons.BOOKMARK_FILL : simpleIcon.icons.BOOKMARK"
                            :color="simpleIcon.colors.ORANGE"
                            :tooltip="userEmail.is_important ? 'Mark as not important' : 'Mark as important'"
                            @click.stop="mailboxStore.toggleImportant(userEmail, userEmail.is_important)"
                        />
                    </div>
                    <div class="col-span-2 flex flex-wrap items-center text-sm relative truncate">
                        <SimpleIcon
                            v-if="!userEmail.is_read"
                            class="mr-2"
                            :size="simpleIcon.sizes.XS"
                            :icon="simpleIcon.icons.CIRCLE"
                            :color="simpleIcon.colors.RED"
                            tooltip="Email not read"
                        />
                        <IdentifiedContactBadge v-if="!userEmail.relevant_email?.length" :identified-contact="userEmail.relevant_email" />
                        <IdentifiedContactBadge v-else v-for="contact in userEmail.relevant_email" :identified-contact="contact" />
                    </div>
                    <div :title="userEmail.snippet" class="flex-wrap col-span-6 text-sm break-all gap-1 flex items-center">
                        <Badge v-for="label in userEmail.labels" class="whitespace-nowrap" color="gray" :dark-mode="darkMode">
                            {{label.name}}
                        </Badge>
                        <div v-html="`${userEmail.subject} - ${userEmail.snippet}`"></div>
                    </div>

                    <div class="col-span-1 flex">
                        <p class="flex-1 text-sm"> {{userEmail.thread_count > 1 ? userEmail.thread_count + " replies" : ''}}</p>
                    </div>
                    <div v-if="userEmail?.attachments?.length > 0" class="flex items-center gap-1">
                        <Badge v-for="attachment in userEmail?.attachments" class="whitespace-nowrap cursor-pointer opacity-60 hover:opacity-100" color="cyan" @click.stop="downloadAttachment(attachment)" :dark-mode="darkMode">
                            {{attachment.filename}}
                        </Badge>
                    </div>
                    <div class="text-sm">
                        {{friendlyDate(userEmail.sent_at)}}
                    </div>
                    <div class="flex gap-3 col-span-1">
                        <SimpleIcon @click.stop="composeEmailStore.reply(userEmail, 'fixed')" :color="simpleIcon.colors.BLACK" :icon="simpleIcon.icons.ARROW_UTURN_LEFT" tooltip="Reply"></SimpleIcon>
                        <SimpleIcon @click.stop="composeEmailStore.replyAll(userEmail, 'fixed')" :color="simpleIcon.colors.BLACK" :icon="simpleIcon.icons.CURVED_DOUBLE_ARROW_LEFT" tooltip="Reply All"></SimpleIcon>
                        <SimpleIcon @click.stop="composeEmailStore.forward(userEmail, 'fixed')" :color="simpleIcon.colors.BLACK" :icon="simpleIcon.icons.ARROW_UTURN_RIGHT" tooltip="Forward"></SimpleIcon>
                        <DropdownMenu :dark-mode="darkMode" :options="mailboxStore.getEmailActions(userEmail, true)" menu-placement="left">
                            <SimpleIcon clickable :size="simpleIcon.sizes.SM" :color="simpleIcon.colors.BLUE" :icon="simpleIcon.icons.ELLIPSIS_HORIZONTAL" tooltip="Actions" tooltip-placement="right"></SimpleIcon>
                        </DropdownMenu>
                    </div>
                </div>
				<div v-else-if="mailboxStore.errorMessage" class="flex text-center h-full justify-center items-center py-4 text-red-500">
					{{mailboxStore.errorMessage}}
				</div>
                <div v-else class="flex text-center h-full justify-center items-center py-4 flex-col gap-2">
                    <p>{{emptyMailboxMessage}}</p>
                    <CustomButton v-if="mailboxStore.isMailboxSyncing" @click="() => mailboxStore.listEmails(1)">Refresh</CustomButton>
                </div>
            </div>

            <Pagination v-if="!mailboxStore.loadingEmailsPaginated" :pagination-data="paginationData" @change-page="handlePageChange" class="py-2"/>
        </div>
    </div>
</template>


<script>
import EmailSearchBar from "./EmailSearchBar.vue";
import EmailListToolbar from "./EmailListToolbar.vue";
import Pagination from "../../Shared/components/Pagination.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import {useMailboxStore} from "../../../../stores/mailbox/mailbox";
import SimpleIcon from "./SimpleIcon.vue";
import {DateTime} from "luxon";
import Badge from "../../Shared/components/Badge.vue";
import useQueryParams from "../../../../composables/useQueryParams";
import useSimpleIcon from "../../../../composables/useSimpleIcon";
import {useMailboxComposeEmailStore} from "../../../../stores/mailbox/composeEmail";
import EmailListCompact from "./EmailListCompactView.vue";
import DropdownMenu from "./DropdownMenu.vue";
import useIdentifiedContact from "../../../../composables/useIdentifedContact";
import IdentifiedContactBadge from "./IdentifiedContact/IdentifiedContactBadge.vue";
import mailbox from "../Mailbox.vue";

const {getBadgeData} = useIdentifiedContact()

export default {
    name: 'EmailList',
    components: {
        CustomButton,
        IdentifiedContactBadge,
        DropdownMenu,
        EmailListCompact,
        Badge,
        SimpleIcon,
        LoadingSpinner,
        Pagination,
        EmailListToolbar,
        EmailSearchBar
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        currentTab: {
            type: String
        },
        apiDriver: {
            type: String,
            required: true
        }
    },
    data(){
        return {
            mailboxStore: useMailboxStore(),
            selectedEmail: null,
            defaultPerPage: 100,
            showActionButtons: false,

            queryParams: useQueryParams(),
            composeEmailStore: useMailboxComposeEmailStore(),
            simpleIcon: useSimpleIcon(),
			showContactData: null,
        }
    },
    methods: {
        getBadgeData,
         async listEmails(page = 1, perPage = null){
            await this.mailboxStore.listEmails(page, perPage ?? this.defaultPerPage)
        },

        handlePageChange({newPage}){
            this.listEmails(newPage)
        },

        friendlyDate(date){
            return DateTime.fromSQL(date).toLocaleString(DateTime.DATE_MED)
        },

        downloadAttachment(attachment){
            window.open(attachment.url, '__blank')
        },

        async getEmailByUuid(uuid){
            const response = await this.mailboxStore.api.getDetailedEmail(uuid)
            if (response.data.data.emails.length > 0) {
                this.mailboxStore.setSelectedEmail(response.data.data.emails[response.data.data.emails.length - 1])
            }
        },
    },


    async mounted(){
        const queryParams = this.queryParams.parseQueryParams(window.location.href)

        if (queryParams?.uuid) {
            try {
                await this.getEmailByUuid(queryParams?.uuid)
            } catch (err) {
                console.error(err)
            }
            delete queryParams.uuid
            window.history.replaceState(null, null, this.queryParams.mountUrlWithSearchParams(queryParams));
        } else {
            await this.listEmails()
        }
    },

    computed: {
        emptyMailboxMessage(){
            return this.mailboxStore.isMailboxSyncing ? "Mailbox syncing" : "Sync mailbox to begin"
        },
        paginationData() {
            return {
                total: this.mailboxStore?.paginatedEmailsResponse?.total,
                to: this.mailboxStore?.paginatedEmailsResponse?.to,
                per_page: this.mailboxStore?.paginatedEmailsResponse?.per_page,
                prev_page_url: this.mailboxStore?.paginatedEmailsResponse?.prev_page_url,
                next_page_url: this.mailboxStore?.paginatedEmailsResponse?.next_page_url,
                links: this.mailboxStore?.paginatedEmailsResponse?.links,
                path: this.mailboxStore?.paginatedEmailsResponse?.path,
                last_page_url: this.mailboxStore?.paginatedEmailsResponse?.last_page_url,
                last_page: this.mailboxStore?.paginatedEmailsResponse?.last_page,
                from: this.mailboxStore?.paginatedEmailsResponse?.from,
                first_page_url: this.mailboxStore?.paginatedEmailsResponse?.first_page_url,
                data: this.mailboxStore?.paginatedEmailsResponse?.data,
                current_page: this.mailboxStore?.paginatedEmailsResponse?.current_page,
            }
        }
    },

    watch: {
        'mailboxStore.selectedTab'(){
            this.listEmails()
        },
    }
};

</script>
