<template>
    <div class="grid grid-cols-10 flex-1 overflow-hidden">
        <div class="col-span-3  overflow-scroll">
			<div v-if="mailboxStore.loadingEmailsPaginated" class="h-full flex flex-col items-center rounded-lg shadow-inner bg-gray-50">
				<loading-spinner :dark-mode="darkMode" ></loading-spinner>
			</div>
            <email-list-compact-list-wrapper v-else :dark-mode="darkMode" class="gap-2">
                <email-list-compact-email
                    class="cursor-pointer"
                    v-for="(userEmail, idx) in mailboxStore.paginatedEmailsResponse?.data"
                    :dark-mode="darkMode"
                    :user-email="userEmail"
                    :selected="isSelectedEmail(idx)"
                    @click="this.mailboxStore.handleEmailSelected(userEmail, idx)"
                >
                </email-list-compact-email>
            </email-list-compact-list-wrapper>
        </div>
        <div class="col-span-7 flex flex-col border rounded-lg bg-grey-50 ml-5 overflow-scroll">
            <email-detail-tab class="flex-1" :dark-mode="darkMode"/>
        </div>
    </div>
</template>
<script>
import {useMailboxStore} from "../../../../stores/mailbox/mailbox";
import EmailListCompactListWrapper from "./EmailListCompactListWrapper.vue";
import EmailListCompactEmail from "./EmailListCompactEmail.vue";
import EmailDetailTab from "./EmailDetailTab.vue";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";

export default {
    name: "EmailListCompact",
    components: {
		LoadingSpinner,
        EmailDetailTab, EmailListCompactEmail, EmailListCompactListWrapper
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            mailboxStore: useMailboxStore(),
            selectedEmailIdx: null,
        }
    },
    methods: {
        isSelectedEmail(idx) {
            return this.mailboxStore.selectedEmailIdx === idx;
        },

        handleEmailSelected(userEmail, idx) {
            this.mailboxStore.selectedEmailIdx = idx;
            this.mailboxStore.setSelectedEmail(userEmail)
        },
    }
}
</script>
