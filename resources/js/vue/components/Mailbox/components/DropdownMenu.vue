<template>
    <div @mouseleave="showMenu = false" @click.stop="toggleMenu" class="relative w-fit">
        <slot ref="reference"></slot>
        <teleport to="#admin-app" :disabled="disabledDynamicMenuPlacement">
            <div v-if="showMenu" class="absolute border rounded  text-sm text-black font-normal z-100 w-40 shadow-md" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']" :style="popoverComputedStyle"  @mouseenter="showMenu = true" ref="menu" @mouseleave="showMenu = false">
                <div @click.stop="handleOptionClick(option)" v-for="option in options" class="flex items-center gap-1  cursor-pointer p-2" :class="[darkMode ? 'hover:bg-dark-40' : 'hover:bg-gray-200']">
                    <div>
                        <simple-icon :dark-mode="darkMode" v-if="option?.icon" :size="option.icon.size" :color="option.icon.color" :icon="option.icon.icon" />
                    </div>
                    <p class="truncate" :class="[darkMode ? 'text-white' : '']">{{option.name}}</p>
                </div>
            </div>
        </teleport>
    </div>
</template>


<script>
import useSimpleIcon from "../../../../composables/useSimpleIcon";
import SimpleIcon from "./SimpleIcon.vue";

import usePopoverMenu from "../../../../composables/usePopoverMenu";

export default {
    name: 'DropdownMenu',
    components: {SimpleIcon},

    props: {
        darkMode: {
            type: Boolean,
            required: true
        },
        options: {
            type: Array,
            required: true,
            default: []
        },
        menuPlacement: {
            type: String,
            required: false,
            default: 'right'
        },
        disabledDynamicMenuPlacement: {
            type: Boolean,
            required: false,
            default: false,
        }
    },

    data(){
        return {
            popoverMenu: usePopoverMenu(),
            simpleIcon: useSimpleIcon(),
            showMenu: false,
            popoverTop: 0,
            popoverLeft: 0,
        }
    },

    emits: ['option-click'],

    computed: {
        popoverComputedStyle() {
            return !this.disabledDynamicMenuPlacement ? {
                top: `${this.popoverTop}px`,
                left: `${this.popoverLeft}px`,
            } : {}
        }
    },

    methods: {
        calculatePosition(event) {
            // Display on the original position if is disabled
            if (this.disabledDynamicMenuPlacement) {
                this.popoverTop = '100%';
                this.popoverLeft = 0;
            } else {
                const menuElement = this.$refs.menu;
                const {popoverTop, popoverLeft} = this.popoverMenu.calculatePosition(event, menuElement, 5);
                this.popoverTop = popoverTop
                this.popoverLeft = popoverLeft
            }
        },

        async toggleMenu(event){
            // Create composable for popover menu
            if (this.showMenu) return;

            this.showMenu = !this.showMenu;

            await this.$nextTick()

            this.calculatePosition(event)
        },
        handleOptionClick(option){
            this.$emit('option-click', option)
            this.showMenu = false
            option?.callback(option)
        }
    }
};

</script>
