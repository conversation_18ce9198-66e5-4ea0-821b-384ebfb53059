<template>
    <div class="flex flex-col gap-4 relative">
        <email-data-display
            :email="email"
            :dark-mode="darkMode"
            :idx="idx"
            show-actions
            :total="total"
            @action="handleAction"
        />
        <div ref="scrollTarget" @click.stop v-if="composeEmailStore.getPosition() === composeEmailStore.positions.EMBEDDED && showContent && composeEmailStore.uniqueIdx === idx">
            <compose-email @close="handleComposeClose"  @sent="handleEmailSent"/>
        </div>
    </div>
</template>


<script>
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import Badge from "../../Shared/components/Badge.vue";
import SimpleIcon from "./SimpleIcon.vue";
import {useMailboxStore} from "../../../../stores/mailbox/mailbox";
import useSimpleIcon from "../../../../composables/useSimpleIcon";
import {useMailboxComposeEmailStore} from "../../../../stores/mailbox/composeEmail";
import DropdownMenu from "./DropdownMenu.vue";
import ComposeEmail from "./ComposeEmail.vue";
import {DateTime} from "luxon";
import IdentifiedContactBadge from "./IdentifiedContact/IdentifiedContactBadge.vue";
import EmailDataDisplay from "./EmailDataDisplay.vue";

export default {
    components: {
        EmailDataDisplay,
        IdentifiedContactBadge,
        DropdownMenu,
        ComposeEmail,
        SimpleIcon,
        Badge,
        LoadingSpinner,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        email: {
            type: Object,
            required: true
        },
        idx: {
            type: Number,
            required: true
        },
        total: {
            type: Number,
            required: true,
        },
    },
    data() {
        return {
            loadingContent: true,
            showContent: false,
            mailboxStore: useMailboxStore(),
            composeEmailStore: useMailboxComposeEmailStore(),
            simpleIcon: useSimpleIcon(),
            showAdditionalActions: false,
        }
    },

    methods: {
        async handleAction({action}){
            const actions = {
                reply: this.handleReply(),
                replyAll: this.handleReplyAll(),
                forward: this.handleForward(),
            }

            if (actions[action]) {
                await actions[action]()
                this.scrollToTarget()
            }
        },
        async handleReply() {
            await this.composeEmailStore.reply(this.email, 'embedded', this.idx);
        },

        async handleReplyAll() {
            await this.composeEmailStore.replyAll(this.email, 'embedded', this.idx);
        },

        async handleForward() {
            await this.composeEmailStore.forward(this.email, 'embedded', this.idx);
        },

        scrollToTarget() {
            this.$refs.scrollTarget.scrollIntoView({behavior: 'smooth'})
        },
        handleComposeClose() {
            this.composeEmailStore.updatePosition(this.composeEmailStore.positions.FIXED);
            this.composeEmailStore.type = 'send'
        },
        handleEmailSent() {
            if (this.composeEmailStore.getPosition() === this.composeEmailStore.positions.EMBEDDED) {
                this.handleComposeClose()
            } else {
              this.mailboxStore.selectedEmail = null;
              this.mailboxStore.selectedTab = 'sent';
            }
        },

        formatDate(inputDate) {
            const dateObj = new Date(inputDate);

            const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
            const dayIndex = dateObj.getDay();
            const dayName = daysOfWeek[dayIndex];

            // Get the day, month, and year components
            const day = dateObj.getDate();
            const month = months[dateObj.getMonth()];
            const year = dateObj.getFullYear();

            // Get the hours and minutes components
            const hours = ('0' + dateObj.getHours()).slice(-2);
            const minutes = ('0' + dateObj.getMinutes()).slice(-2);

            // Format the date in the desired format
            return `${dayName}, ${day} ${month} ${year} at ${hours}:${minutes}`;
        },
        resizeIframe() {
            this.loadingContent = true;
            const iframe = document.getElementById(`email-body-${this.email.uuid}`);
            iframe.height = iframe.contentWindow.document.body.scrollHeight + 100;
            this.loadingContent = false;
        },
        toggleContent(){
            if (this.isTheLastEmail) return true;

            this.resizeIframe();
            this.showAdditionalActions = false
            this.showContent = !this.showContent;
        },
        downloadAttachment(attachment){
            window.open(attachment.url, '_blank')
        },
    },
};

</script>
