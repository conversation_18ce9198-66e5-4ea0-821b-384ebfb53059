<template>
    <div class="flex justify-between border rounded-lg bg-white p-4">
        <div v-if="identifiedContactHelper.isNoResult(contactIdentified)" class="flex gap-4 items-center">
			<div class="font-semibold pr-4">
				{{ contactIdentified.identifier_value }} (unidentified)
			</div>
		</div>
		<div v-if="identifiedContactHelper.isIdentifying(contactIdentified)" class="flex flex-grow justify-between gap-4 items-center">
			<div class="flex">
				<p class="mr-1">Identifying: </p>
				<p class="font-semibold pr-4">
					{{ contactIdentified.identifier_value }}
				</p>
			</div>
			<div class="mr-2">
				<loading-spinner :dark-mode="darkMode" size="w-6 h-6" margin=""></loading-spinner>
			</div>
		</div>
		<div v-else-if="identifiedContactHelper.isSingleResult(contactIdentified)" class="flex gap-4 items-center">
			<div class="font-semibold border-r pr-4">
				{{ contactIdentified.nominated_contact.contact.name }}
			</div>
			<div class="text-sm font-semibold border-r pr-4">
				{{ contactIdentified.nominated_contact.contact.email }}
			</div>
			<component :is="getMailboxContactDataComponent(contactIdentified)" :contact="contactIdentified.nominated_contact" :dark-mode="darkMode"/>
		</div>
		<div v-else-if="identifiedContactHelper.isMultipleResult(contactIdentified)">
			<div class="font-semibold border-r pr-4">
				{{ contactIdentified.identifier_value }} matched with {{contactIdentified.possible_contacts.length}} possible contacts.
			</div>
		</div>
        <div v-if="!identifiedContactHelper.isIdentifying(contactIdentified)" class="flex flex-col ml-2 items-end gap-2">
            <slot name="actions">
            </slot>
            <div class="text-primary-500 text-sm cursor-pointer"  @click="handleCreateContact('company_user')">
                Create New Contact
            </div>
            <div v-if="identifiedContactHelper.isSingleResult(contactIdentified) && contactIdentified?.nominated_contact?.model_relation_type === 'company_user'" class="text-primary-500 text-sm cursor-pointer"  @click="handleEditContact('company_user')">
                Edit Nominated Contact
            </div>
			<div v-if="!identifiedContactHelper.isNoResult(contactIdentified)" class="text-primary-500 text-sm cursor-pointer"  @click="handleChangeContact('select_contact')">
				Select Contact
			</div>
        </div>
    </div>
    <modal v-show="showModal" :dark-mode="darkMode" no-buttons @close="showModal = false">
        <template v-slot:header>
            <div v-if="selectOrCreate === 'select'">
                Nominate Contact
            </div>
            <div v-else-if="contactToBeEditedOrCreated?.nominated_contact !== null">
                Edit Company Contact
            </div>
			<div v-else>
				Create Company Contact
			</div>
        </template>
        <template v-slot:content>
			<select-contact v-if="selectOrCreate === 'select'" :dark-mode="darkMode" :contact="contactIdentified" @new-contact-selected="handleContactSelected"></select-contact>
            <component v-else :is="showModal" :dark-mode="darkMode" @contact-saved="handleContactCreatedEvent" :contact-data="contactToBeEditedOrCreated"></component>
        </template>
    </modal>
</template>
<script>

import CreateCompanyUser
    from "../../../Communications/components/ContactIdentificationModal/CreateNewRecord/CreateCompanyUser/CreateCompanyUser.vue";
import Modal from "../../../Shared/components/Modal.vue";

import {markRaw} from "vue";
import useIdentifiedContact from "../../../../../composables/useIdentifedContact"
import UserContactEmailAdditionalData from "./AdditionalData/UserContactEmailAdditionalData.vue";
import CompanyLocationContactEmailAdditionalData
	from "./AdditionalData/CompanyLocationContactEmailAdditionalData.vue";
import ConsumerContactEmailAdditionalData from "./AdditionalData/ConsumerContactEmailAdditionalData.vue";
import CompanyUserContactEmailAdditionalData
	from "./AdditionalData/CompanyUserContactEmailAdditionalData.vue";
import SelectContact from "./IdentifiedContactModalContent/SelectContact.vue";
import ApiService from "./services/api";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";


export default {
    name: "ViewEditCreateEmailContact",
    components: {LoadingSpinner, SelectContact, Modal},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        contact: {
            type: Object,
            default: null
        },
        modelValue: {
            type: Object,
            default: null
        },
    },
    emits: ['contact-created', 'contact-changed'],
    data ()  {
        return {
            showModal: false,
            modalMap: {
				"company_user": markRaw(CreateCompanyUser),
				"select_contact": markRaw(SelectContact),
			},
			selectOrCreate: null,
			contactIdentified: null,
			contactToBeEditedOrCreated: null,
			apiService: null,
            identifiedContactHelper: useIdentifiedContact()
        }
    },
	created() {
		this.apiService = ApiService.make();
		this.contactIdentified = this.contact
	},
	methods: {
		async handleContactSelected(contactIdentified) {

			const nominatedContact = {
				contact_relation_id: contactIdentified.model_relation_id,
				contact_relation_type: contactIdentified.model_relation_type,
				identifier_value: contactIdentified.contact.email,
				identifier_field_type: 'email',
				model_type: this.contact.related_model_type,
				model_id: this.contact.related_model_id
			}

			const resp = await  this.apiService.identifyAndNominateContact(nominatedContact)
			this.contactIdentified = resp.data.data.entity
			this.$emit('contact-changed')
			this.showModal = false

		},
        handleEditContact(modalKey) {
			this.selectOrCreate = 'create'
			this.contactToBeEditedOrCreated = {...this.contactIdentified}
			this.showModal = this.modalMap[modalKey] ?? null
        },
		handleCreateContact(modalKey) {
			this.selectOrCreate = 'create'
			this.contactToBeEditedOrCreated = {...this.contactIdentified}
			this.contactToBeEditedOrCreated.nominated_contact = null
			this.showModal = this.modalMap[modalKey] ?? null
		},
        async handleContactCreatedEvent({contact, company}) {

			const nominatedContact = {
				contact_relation_id: contact.id,
				contact_relation_type: 'company_user',
				identifier_value: contact.email,
				identifier_field_type: 'email',
				model_type: this.contact.related_model_type,
				model_id: this.contact.related_model_id
			}

			const resp = await this.apiService.identifyAndNominateContact(nominatedContact)

			if (resp.data.data.status) {
				this.contactIdentified = resp.data.data.entity
				this.$emit('contact-changed')
			}

            this.showModal = false
        },

		handleChangeContact(modalKey) {
			this.selectOrCreate = 'select'
			this.showModal = this.modalMap[modalKey] ?? null
		},

		getMailboxContactDataComponent (identifiedContact) {
			if (!identifiedContact.nominated_contact) return null;

			switch (identifiedContact.nominated_contact.model_relation_type) {
				case useIdentifiedContact().contactTypes.USER:
					return markRaw(UserContactEmailAdditionalData)
				case useIdentifiedContact().contactTypes.COMPANY_LOCATION:
					return markRaw(CompanyLocationContactEmailAdditionalData)
				case useIdentifiedContact().contactTypes.CONSUMER:
					return markRaw(ConsumerContactEmailAdditionalData)
				case useIdentifiedContact().contactTypes.COMPANY_USER:
					return markRaw(CompanyUserContactEmailAdditionalData)
			}
		}
    }
}
</script>
