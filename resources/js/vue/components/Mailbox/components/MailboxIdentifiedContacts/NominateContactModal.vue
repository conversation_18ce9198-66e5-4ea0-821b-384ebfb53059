<template>
    <modal :dark-mode="darkMode" no-buttons @close="$emit('close')">
        <template v-slot:header>
            Nominate Contact
        </template>
        <template v-slot:content>
            <select-contact
                :dark-mode="darkMode"
                :contact="contactIdentified"
                @new-contact-selected="handleContactSelected"
            />
        </template>
    </modal>
</template>
<script>
import Modal from "../../../Shared/components/Modal.vue";
import SelectContact from "./IdentifiedContactModalContent/SelectContact.vue";
import ApiService from "./services/api";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";

export default {
    name: "NominateContactModal",
    components: {LoadingSpinner, SelectContact, Modal},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        contact: {
            type: Object,
            default: null
        },
        modelValue: {
            type: Object,
            default: null
        },
    },
    emits: ['contact-created', 'contact-changed'],
    data() {
        return {
            contactIdentified: null,
            contactToBeEditedOrCreated: null,
            apiService: null,
        }
    },
    created() {
        this.apiService = ApiService.make();
        this.contactIdentified = this.contact
    },
    methods: {
        async handleContactSelected(contactIdentified) {

            const nominatedContact = {
                contact_relation_id: contactIdentified.model_relation_id,
                contact_relation_type: contactIdentified.model_relation_type,
                identifier_value: contactIdentified.contact.email,
                identifier_field_type: 'email',
                model_type: this.contact.related_model_type,
                model_id: this.contact.related_model_id
            }

            const resp = await this.apiService.identifyAndNominateContact(nominatedContact)
            this.contactIdentified = resp.data.data.entity
            this.$emit('contact-changed')
            this.showModal = false

        },
    }
}
</script>
