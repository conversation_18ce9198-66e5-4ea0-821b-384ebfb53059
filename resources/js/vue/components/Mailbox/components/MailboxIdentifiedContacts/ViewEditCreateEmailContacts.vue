<template>
    <div>
        <div v-if="loading" class="flex border rounded-lg bg-white p-4 items-center text-center">
            <div class="mr-4">
                <loading-spinner :dark-mode="darkMode" size="w-4 h-4"></loading-spinner>
            </div>
            <div>
                identifying contacts . . .
            </div>
        </div>
        <div v-else class="flex flex-col gap-2">
            <p v-if="contacts.length > 3" class="text-sm">Listing {{contacts.length}} contacts</p>
            <div class="flex flex-col gap-2 overflow-scroll" :class="showMore ? '' : 'max-h-48'">
                <view-edit-create-email-contact
                    v-for="recipient in contacts"
                    :dark-mode="darkMode"
                    :contact="recipient"
                    @contact-changed="handleContactChanged"
                />
            </div>
            <p v-if="contacts.length > 3" @click.stop="() => showMore = !showMore" class="text-sm text-blue-500 cursor-pointer">{{showMore ? 'Show less' : 'Show more'}}</p>
        </div>
    </div>
</template>
<script>
import ViewEditCreateEmailContact from "./ViewEditCreateEmailContact.vue";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";

export default {
    name: "ViewEditCreateEmailContacts",
    components: {LoadingSpinner, ViewEditCreateEmailContact},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        contacts: {
            type: Array,
            default: []
        },
		loading: {
			type: Boolean,
			default: false
		}
    },
	emits: ['contact-changed'],
    data(){
        return {
            showMore: false
        }
    },
	methods: {
		handleContactChanged() {
			this.$emit('contact-changed')
		}
	}
}
</script>
