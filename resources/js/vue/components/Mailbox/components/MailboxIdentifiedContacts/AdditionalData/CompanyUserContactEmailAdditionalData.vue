<template>
	<div class="flex">
        <a class="text-blue-500 text-sm font-semibold cursor-pointer" :href="'/companies/' + contact.contact.company.id" target="_blank">Company: {{contact.contact.company.name}} ({{contact.contact.company.id}})</a>
	</div>
</template>
<script>
export default {
	name: "UserContactEmailAdditionalData",
	props: {
		darkMode: {
			type: Boolean,
			default: false
		},
		contact: {
			type: Object,
			required: true
		}
	}
}
</script>
