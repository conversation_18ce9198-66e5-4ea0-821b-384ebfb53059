<template>
	<div class="w-full h-full flex flex-col flex-grow">
		<div class="w-full mb-4 text-center text-sm">
			<p>Please nominate the correct contact for: <strong>{{ contact.identifier_value }}</strong>. <br>
				If you don't find that person below, you can create a new company contact.
			</p>
		</div>
		<div class="grid grid-cols-3 gap-x-3 px-5 text-slate-400 font-medium tracking-wide uppercase text-sm items-center text-center">
			<p v-for="header in headers">{{ header }}</p>
		</div>
		<div v-if="loading">
			<loading-spinner></loading-spinner>
		</div>
		<div v-else>
			<div v-if="contact.possible_contacts.length === 0" class="my-4 text-sm font-semibold">
				<p>No contacts found</p>
			</div>
			<div class="max-h-72 overflow-y-scroll">
				<div
					v-for="(possibleContact, idx) in contact.possible_contacts"
					class="font-medium text-sm border-2 px-5 py-3 mt-3 cursor-pointer rounded-lg"
					:class="[darkMode ? 'hover:bg-dark-background border-dark-border' : 'hover:bg-light-background border-light-border text-black',
      				selectedContactIdx === idx ? darkMode ? 'bg-dark-background border-primary-300' : 'bg-light-background border-primary-300' : '']"
					@click="handlePossibleContactSelected(possibleContact, idx)"
				>
					<div class="grid grid-cols-3 items-center text-center">
                        <div class="flex flex-col">
                            <p class="text-sm">{{ identifiedContactHelper.getContactTypeDescription(possibleContact?.model_relation_type) }}</p>
                            <p class="text-xs">{{ possibleContact?.model_relation_id }}</p>
                        </div>
						<div class="flex flex-col">
                            <p class="text-sm">{{ possibleContact.contact?.name }}</p>
                            <p class="text-xs">{{ possibleContact.contact?.email }}</p>
                        </div>
						<span v-if="possibleContact?.contact?.company">Company ({{ possibleContact?.contact?.company?.id }}) {{ possibleContact?.contact?.company?.name }}</span>
					</div>
				</div>
			</div>
		</div>
		<div class="w-full flex self-end justify-between mt-4">
			<custom-button :dark-mode="darkMode" :disabled="selectedContactIdx === null" @click="handleContactConfirmed">
				Confirm
			</custom-button>
		</div>
	</div>
</template>
<script>
import CustomButton from "../../../../Shared/components/CustomButton.vue";
import ButtonDropdown from "../../../../Shared/components/ButtonDropdown.vue";
import LoadingSpinner from "../../../../Shared/components/LoadingSpinner.vue";
import ContactsTableItem
	from "../../../../Communications/components/ContactIdentificationModal/PossibleContactSelection/ContactsTableItem.vue";
import useIdentifiedContact from "../../../../../../composables/useIdentifedContact";

export default {
    name: "SelectContact",
    components: {ContactsTableItem, LoadingSpinner, CustomButton, ButtonDropdown},

    props: {
        darkMode: {
            type: Boolean,
            required: true,
        },
		contact: {
			type: Object,
			default: {}
		}
    },
	data() {
		return {
			selectedContact: null,
			selectedContactIdx: null,
			headers: [
				'Type',
				'Name',
				'Details'
			],
            identifiedContactHelper: useIdentifiedContact()
		}
	},
	emits: ['new-contact-selected'],
	methods: {
		handlePossibleContactSelected(contact, idx) {
			if (this.selectedContactIdx === idx) {
				this.selectedContact = null;
				this.selectedContactIdx = null;
			} else {
				this.selectedContact = contact
				this.selectedContactIdx = idx
			}
		},

		handleContactConfirmed() {
			this.$emit('new-contact-selected', this.selectedContact)
		},
	}
}
</script>

