<template>
    <div
        @mouseleave="showAdditionalInfo = false"
        @click.stop @mouseenter="e => toggleAdditionalInfoPopup(e, true)"
        class="relative cursor-default"
    >
        <Badge :color="badgeColor" class="flex items-center gap-1">
            <email-link
                v-if="identifiedContact.identifier_field_type === 'email'"
                :email="identifiedContact.identifier_value"
            >
                {{identifiedContactHelper.getBadgeData(identifiedContact)}}
            </email-link>

            <p v-else>
                {{ identifiedContactHelper.getBadgeData(identifiedContact) }}
            </p>
        </Badge>
        <teleport to="#admin-app">
            <div
                @click.stop
                v-if="showAdditionalInfo"
                @mouseleave="showAdditionalInfo = false"
                @mouseenter="showAdditionalInfo = true" class="fixed min-h-20 max-h-40 overflow-auto bg-white shadow-lg rounded-lg p-2 z-10 flex flex-col gap-1"
                :style="popoverComputedStyle"
                ref="menu"
            >
                <div class="flex gap-1 items-center">
                    <p class="text-xs font-semibold">Identification Status: </p>
                    <p class="text-xs">{{identifiedContactHelper.getIdentificationStatusDescription(identifiedContact?.identification_status)}}</p>
                    <simple-icon
                        v-if="identifiedContactHelper.isMultipleResult(identifiedContact)"
                        :icon="useSimpleIcon().icons.PENCIL_SQUARE"
                        tooltip="Nominate Contact"
                        @click="() => showModal = !showModal"
                    />
                </div>
                <div v-if="identifiedContact.nominated_contact" class="flex gap-1">
                    <p class="text-xs font-semibold">Type: </p>
                    <p class="text-xs">{{identifiedContactHelper.getContactTypeDescription(identifiedContact?.nominated_contact?.model_relation_type)}} ({{identifiedContact?.nominated_contact.model_relation_id}})</p>
                </div>
                <component v-if="additionalDataComponent" :is="additionalDataComponent" :contact="identifiedContact.nominated_contact"></component>
            </div>
        </teleport>
    </div>
</template>


<script>

import Badge from "../../../Shared/components/Badge.vue";

import useIdentifiedContact from "../../../../../composables/useIdentifedContact";
import usePopoverMenu from "../../../../../composables/usePopoverMenu";
import SimpleIcon from "../SimpleIcon.vue";
import EmailLink from "../../EmailLink.vue";
import useSimpleIcon from "../../../../../composables/useSimpleIcon.js";
import NominateContactModal from "../MailboxIdentifiedContacts/NominateContactModal.vue";

export default {
    name: 'IdentifiedContactEmailBadge',
    components: {NominateContactModal, EmailLink, SimpleIcon, Badge},
    props: {
        identifiedContact: {
            type: Object,
            required: true,
            default: {}
        },
        darkMode: {
            type: Boolean,
            default: false
        }
    },

    data(){
        return {
            identifiedContactHelper: useIdentifiedContact(),
            showAdditionalInfo: false,
            popoverMenu: usePopoverMenu(),
            popoverTop: 0,
            popoverLeft: 0,
            showModal: false
        }
    },

    methods: {
        useSimpleIcon,
        async toggleAdditionalInfoPopup(event){
            if (this.showAdditionalInfo) return;

            this.showAdditionalInfo = !this.showAdditionalInfo;

            await this.$nextTick()

            this.calculatePosition(event)
        },

        calculatePosition(event){
            const menuElement = this.$refs.menu;

            const { popoverTop, popoverLeft } = this.popoverMenu.calculatePosition(event, menuElement);

            this.popoverTop = popoverTop
            this.popoverLeft = popoverLeft
        },
    },

    computed: {
        popoverComputedStyle(){
            return {
                top: `${this.popoverTop}px`,
                left: `${this.popoverLeft}px`,
            }
        },


        additionalDataComponent(){
            return this.identifiedContactHelper.getAdditionalDataComponent(this.identifiedContact)
        },
        badgeColor(){
            if (!this.identifiedContact) return 'red';

            switch (this.identifiedContact.identification_status) {
                case this.identifiedContactHelper.identificationStatus.NO_RESULT:
                    return 'gray'
                case this.identifiedContactHelper.identificationStatus.SINGLE_RESULT:
                    return 'blue'
                case this.identifiedContactHelper.identificationStatus.MULTIPLE_RESULTS:
                    return 'amber'
                case this.identifiedContactHelper.identificationStatus.IDENTIFYING:
                    return 'gray'
            }
        }
    }
};

</script>
