<template>
    <div class="flex gap-1 items-center">
        <p class="text-xs font-semibold">Name: </p>
        <p class="text-xs">{{contact.contact.name}}</p>
    </div>

    <div class="flex gap-1 items-center">
        <p class="text-xs font-semibold">Email: </p>
        <p class="text-xs">{{contact.contact.email}}</p>
    </div>

    <div class="flex gap-1 items-center">
        <p class="text-xs font-semibold">Role: </p>
        <p class="text-xs">{{contact.contact.role}}</p>
    </div>
</template>


<script>
export default {
    name: 'UserContactAdditionalData',
    props: {
        contact: {
            type: Object,
            required: true,
            default: {}
        }
    }
};

</script>
