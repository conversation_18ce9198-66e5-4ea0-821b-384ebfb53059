<template>
    <div class="flex gap-1 items-center">
        <p class="text-xs font-semibold">Name: </p>
        <p class="text-xs">{{contact.contact.name}}</p>
    </div>

    <div class="flex gap-1 items-center">
        <p class="text-xs font-semibold">Email: </p>
        <p class="text-xs">{{contact.contact.email}}</p>
    </div>

    <div class="flex gap-1 items-center">
        <p class="text-xs font-semibold">Department: </p>
        <p class="text-xs">{{contact.contact.department}}</p>
    </div>


    <div class="flex gap-1 items-center">
        <p class="text-xs font-semibold">Phones: </p>
        <p class="text-xs">{{contact.contact.phone}} / {{contact.contact.office_phone}}</p>
    </div>

    <div class="flex gap-1 items-center">
        <p class="text-xs font-semibold">Company: </p>
        <a class="text-blue-500 text-xs cursor-pointer" :href="`/companies/${contact.contact.company.id}`" target="_blank">{{contact.contact.company.name}} ({{contact.contact.company.id}})</a>
    </div>

</template>


<script>
export default {
    name: 'UserContactAdditionalData',
    props: {
        contact: {
            type: Object,
            required: true,
            default: {}
        }
    }
};

</script>
