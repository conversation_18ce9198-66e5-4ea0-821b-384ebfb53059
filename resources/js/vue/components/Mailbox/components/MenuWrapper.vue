<template>
    <div
        class="menu-wrapper flex flex-col border rounded-r-lg mt-4 mb-4 overflow-hidden"
    >
        <slot></slot>
    </div>
</template>

<script>
export default {
    name: "MenuWrapper",
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        label: {
            type: String,
            default: null,
        }
    }
}

</script>

<style scoped>
.menu-wrapper {}
.menu-wrapper > :first-child {
    border-top: none;
}
.menu-wrapper > :last-child {
    border-bottom: none;
}
</style>