<template>
    <div
        class="flex flex-col p-2 rounded-lg shadow-inner bg-gray-50"
        :class="[{'bg-cyan-200': selected}]"
    >
        <div class="flex justify-between mb-2">
            <div class="flex gap-1 text-xs items-center text-center font-semibold">
					<SimpleIcon
						:size="simpleIcon.sizes.SM"
						:icon="userEmail.is_starred ? simpleIcon.icons.STAR_FILL : simpleIcon.icons.STAR"
						color="yellow"
						:tooltip="userEmail.is_starred ? 'Starred' : 'Not starred'"
						@click.stop="mailboxStore.toggleStar(userEmail, userEmail.is_starred)"
					/>
					<SimpleIcon
						:size="simpleIcon.sizes.SM"
						:icon="userEmail.is_important ? simpleIcon.icons.BOOKMARK_FILL : simpleIcon.icons.BOOKMARK"
						color="orange"
						:tooltip="userEmail.is_important ? 'Mark as not important' : 'Mark as important'"
						@click.stop="mailboxStore.toggleImportant(userEmail, userEmail.is_important)"
					/>
				<div class="flex flex-wrap gap-1 text-xs items-center text-center font-semibold">
                    <SimpleIcon
                        :class="!userEmail.is_read ? 'visible' : 'invisible'"
                        :size="simpleIcon.sizes.XS"
                        :icon="simpleIcon.icons.CIRCLE"
                        :color="simpleIcon.colors.RED"
                        tooltip="Email not read"
                    />
					<IdentifiedContactBadge v-if="!userEmail.relevant_email?.length" :identified-contact="userEmail.relevant_email" />
					<IdentifiedContactBadge v-else v-for="contact in userEmail.relevant_email" :identified-contact="contact" />
				</div>
            </div>
            <div class="flex items-center text-xs text-center gap-1">
                <div class="mr-5 font-semibold">
                    {{friendlyDate(userEmail.sent_at)}}
                </div>
                <SimpleIcon @click.stop="composeEmailStore.reply(userEmail, 'fixed')" :size="simpleIcon.sizes.SM" :color="simpleIcon.colors.BLACK" :icon="simpleIcon.icons.ARROW_UTURN_LEFT" tooltip="Reply" clickable></SimpleIcon>
                <DropdownMenu :dark-mode="darkMode" :options="emailActions" menu-placement="left">
                    <SimpleIcon clickable :size="simpleIcon.sizes.SM" :color="simpleIcon.colors.BLUE" :icon="simpleIcon.icons.ELLIPSIS_HORIZONTAL" tooltip="Actions" tooltip-placement="right"></SimpleIcon>
                </DropdownMenu>
            </div>
        </div>
        <div class="flex text-xs overflow-ellipsis mb-2 break-all">
            <div v-html="`${userEmail.subject} - ${userEmail.snippet}`"></div>
        </div>
        <div class="flex justify-between">
            <div class="flex text-xs font-semibold text-primary-500">
                <p v-if="userEmail.thread_count > 1">{{userEmail.thread_count}} replies</p>
            </div>
        </div>
    </div>
</template>
<script>
import SimpleIcon from "./SimpleIcon.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon";
import {DateTime} from "luxon";
import {useMailboxStore} from "../../../../stores/mailbox/mailbox";
import {useMailboxComposeEmailStore} from "../../../../stores/mailbox/composeEmail";
import DropdownMenu from "./DropdownMenu.vue";
import Badge from "../../Shared/components/Badge.vue";
import IdentifiedContactBadge from "./IdentifiedContact/IdentifiedContactBadge.vue";


export default {
    name: "EmailListCompactEmail",
    components: {IdentifiedContactBadge, Badge, DropdownMenu, SimpleIcon},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        userEmail: {
            type: Object,
            default: {}
        },
        selected: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            simpleIcon: useSimpleIcon(),
            mailboxStore: useMailboxStore(),
            composeEmailStore: useMailboxComposeEmailStore()
        }
    },
    methods: {
        handleOpenTasks(){
            // TODO - Implement
        },

        friendlyDate(date){
            return DateTime.fromSQL(date).toLocaleString(DateTime.DATE_MED)
        },
    },
    computed: {
        emailActions(){
            return this.mailboxStore.getEmailActions(this.userEmail, true)
        },
    }
}
</script>
