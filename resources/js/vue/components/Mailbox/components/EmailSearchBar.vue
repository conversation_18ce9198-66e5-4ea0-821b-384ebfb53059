<template>
    <div class="grid grid-cols-12 gap-3 py-4">
        <div class="col-span-12 flex justify-between">
            <div class="flex items-center gap-2">
                <custom-input v-model="mailboxStore.listFilters.query" placeholder="Search emails" class="flex-1 min-w-[16rem]" search-icon />
                <custom-button
                    type="submit"
                    :dark-mode="darkMode"
                    @click="mailboxStore.listEmails(1)"
                    :disabled="mailboxStore.loadingEmailsPaginated"
                >
                    Search
                </custom-button>
<!--                <custom-button :dark-mode="darkMode" color="slate-inverse" type="reset" @click="mailboxStore.resetSearch(1)">Reset</custom-button>-->
                <custom-button @click="syncMailbox" :disabled="mailboxStore.isMailboxSyncing">
                    Sync mailbox
                </custom-button>
              <div class="flex" v-if="mailboxStore.bulkSelect.length > 0">
                <div v-for="batchAction in mailboxStore.getBatchEmailActions()"
                    class="flex rounded p-2 text-sm items-center text-center gap-2 hover:bg-cyan-100 cursor-pointer"
                    @click="batchAction.callback()"
                >
                  <simple-icon :icon="batchAction.icon.icon" :dark-mode="darkMode" :size="simpleIcon.sizes.SM"></simple-icon>
                  <p>{{batchAction.name}}</p>
                </div>
                <div class="flex font-medium rounded p-2 text-sm items-center text-center gap-2">
                  {{ mailboxStore.bulkSelect.length }} selected
                </div>
              </div>
            </div>
            <div class="flex flex-row-reverse items-center gap-2">
                <custom-button
                    @click="openComposeEmailModal"
                    icon
                    class="p-4 w-full flex items-center justify-center"
                >
                    <template v-slot:icon>
                        <SimpleIcon :icon="simpleIcon.icons.PENCIL" :color="simpleIcon.colors.WHITE"/>
                    </template>
                    New Email
                </custom-button>
            </div>
        </div>

        <alerts-container :text="alertText" :alert-type="alertType" v-if="alertActive" :dark-mode="darkMode"/>
    </div>
</template>


<script>

import CustomInput from "../../Shared/components/CustomInput.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import AlertsContainer from "../../LeadProcessing/components/AlertsContainer.vue";
import {useMailboxStore} from "../../../../stores/mailbox/mailbox";
import SimpleIcon from "./SimpleIcon.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon";
import {useMailboxComposeEmailStore} from "../../../../stores/mailbox/composeEmail";
import DropdownMenu from "./DropdownMenu.vue";

export default {
    components: {DropdownMenu, SimpleIcon, AlertsContainer, CustomButton, CustomInput},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
    },

    data(){
        return {
            mailboxStore: useMailboxStore(),
            alertActive: false,
            alertType: '',
            alertText: '',
            simpleIcon: useSimpleIcon(),
            composeEmailStore: useMailboxComposeEmailStore()
        }
    },

    methods: {
        async syncMailbox(){
            this.mailboxStore.isMailboxSyncing = true
            await this.mailboxStore.api.syncMailbox();
            this.alertActive = true
            this.alertType = 'success'
            this.alertText = 'Your mailbox will be synced within minutes'
            setTimeout(() => this.alertActive = false, 2000)
        },

        openComposeEmailModal(){
            this.composeEmailStore.resetComposeEmailData()
            this.composeEmailStore.updatePosition(this.composeEmailStore.positions.FIXED);
        },
    }
};

</script>
