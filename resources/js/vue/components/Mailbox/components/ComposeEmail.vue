<template>
    <div v-if="composeEmailStore.getPosition() !== composeEmailStore.positions.CLOSED" :class="[{'fixed inset-0 w-full h-full bg-opacity-75 bg-dark-background z-100': composeEmailStore.position === composeEmailStore.positions.FULL}]">
        <div :class="[
            {'border rounded-lg shadow-md m-4 overflow-auto' : composeEmailStore.position === composeEmailStore.positions.EMBEDDED},
            {'shadow-md bg-white border fixed bottom-0 right-5 w-[700px] rounded-t-lg overflow-auto': composeEmailStore.position === composeEmailStore.positions.FIXED},
            {'shadow-md rounded-lg bg-white border fixed w-10/12 h-3/4 top-[50%] left-[50%] transform translate-x-[-50%] translate-y-[-50%] overflow-auto flex flex-col': composeEmailStore.position === composeEmailStore.positions.FULL},
            {'shadow-md bg-white border fixed bottom-0 right-5 w-[700px] rounded-t-lg overflow-auto': composeEmailStore.position === composeEmailStore.positions.MINIMISED}
         ]"
        >
            <div class="flex items-center bg-gray-100 p-2 py-4">
                <span class="font-medium flex-1">New message</span>
                <SimpleIcon v-if="composeEmailStore.position === composeEmailStore.positions.EMBEDDED || composeEmailStore.position === composeEmailStore.positions.MINIMISED" :size="simpleIcon.sizes.SM" class="mr-2" :dark-mode="darkMode" :icon="simpleIcon.icons.ARROW_TOP_RIGHT_ON_SQUARE" @click="handleFix" clickable/>
                <SimpleIcon v-if="composeEmailStore.position === composeEmailStore.positions.FIXED" :size="simpleIcon.sizes.SM" class="mr-2" :icon="simpleIcon.icons.ARROWS_POINTING_OUT" @click="handleExpand" clickable/>
                <SimpleIcon v-if="composeEmailStore.position === composeEmailStore.positions.FIXED" :size="simpleIcon.sizes.SM" class="mr-2" :icon="simpleIcon.icons.MINUS" @click="handleMinimise" clickable/>
                <SimpleIcon v-if="composeEmailStore.position === composeEmailStore.positions.FULL"  :size="simpleIcon.sizes.SM" :icon="simpleIcon.icons.ARROWS_POINTING_IN" @click="handleFix" clickable/>
                <SimpleIcon :size="simpleIcon.sizes.MD" :icon="simpleIcon.icons.X_MARK" @click="handleClose" clickable/>
            </div>
            <div v-if="composeEmailStore.position !== composeEmailStore.positions.MINIMISED" class="p-2 flex flex-col flex-grow relative justify-center">
                <div v-if="errorHandler.message" class="p-2 text-center">
                    <p class="text-red-500">{{errorHandler.message}}</p>
                </div>
                <div class="absolute top-0 left-0 w-full h-full bg-black/30 z-10 flex justify-center items-center" v-if="sending">
                    <loading-spinner />
                </div>
                <tag-input
					class="w-full"
					v-model="composeEmailStore.to"
					clean placeholder="To:"
					allow-new
					:options="composeEmailStore.possibleContacts"
					:searchable-fields="['identifier_value']"
					:option-factory-callback="optionFactoryCallback"
				>
					<template v-slot:filtered-result-option="{value}">
						<p>{{value?.contact?.name}} {{value.identifier_value}}</p>
					</template>
					<template v-slot:selected-tag="{value}">
						<p>{{value?.contact?.name}} {{value.identifier_value}}</p>
					</template>
				</tag-input>
				<tag-input
                    v-if="inputVisibility['cc'] || composeEmailStore.cc.length > 0"
                    v-model="composeEmailStore.cc"
                    clean
                    class="w-full"
                    placeholder="CC:"
                    allow-new
					:options="composeEmailStore.possibleContacts"
					:searchable-fields="['identifier_value']"
					:option-factory-callback="optionFactoryCallback"
                >
					<template v-slot:filtered-result-option="{value}">
						<p>{{value?.contact?.name}} {{value.identifier_value}}</p>
					</template>
					<template v-slot:selected-tag="{value}">
						<p>{{value?.contact?.name}} {{value.identifier_value}}</p>
					</template>
				</tag-input>
                <tag-input
                    v-if="inputVisibility['bcc'] || composeEmailStore.bcc.length > 0"
                    v-model="composeEmailStore.bcc"
                    clean
                    class="w-full"
                    placeholder="BCC:"
					:options="composeEmailStore.possibleContacts"
                    allow-new
					:searchable-fields="['identifier_value']"
					:option-factory-callback="optionFactoryCallback"
                >
					<template v-slot:filtered-result-option="{value}">
						<p>{{value?.contact?.name}} {{value.identifier_value}}</p>
					</template>
					<template v-slot:selected-tag="{value}">
						<p>{{value?.contact?.name}} {{value.identifier_value}}</p>
					</template>
				</tag-input>
                <div class="flex flex-col items-end">
                    <div class="flex gap-1">
                    <span
                        v-if="!inputVisibility['cc']"
                        class="p-1 rounded hover:shadow cursor-pointer"
                        @click="showInput('cc')">
                        CC
                    </span>
                        <span
                            v-if="!inputVisibility['bcc']"
                            class="p-1 rounded hover:shadow cursor-pointer"
                            @click="showInput('bcc')">
                        Bcc
                    </span>
                    </div>
                </div>
                <custom-input v-if="!['reply', 'forward'].includes(composeEmailStore.type)" placeholder="Subject:"
                              v-model="composeEmailStore.subject" class="w-full h-10 mt-1"/>

                <wysiwyg-editor
                    v-if="flag"
                    v-model="composeEmailStore.content"
                    :dark-mode="darkMode"
                    class="w-full mt-1 flex flex-grow"
                    :auto-width="editorWidth"
                    :editor-height="editorHeight"
                >
                </wysiwyg-editor>
                <div class="mt-1 sticky bottom-0 flex flex-col justify-end">
                    <div class="flex">
                        <custom-button :dark-mode="darkMode" @click="handleSend" :disabled="sending">
                            <div class="relative">
                                <span class="min-w-[6rem]" :class="sending ? 'invisible' : ''">Send</span>
                                <div v-if="sending" role="status" class="absolute -translate-x-1/2 -translate-y-1/2 top-2/4 left-1/2">
                                    <svg aria-hidden="true"  class="w-6 h-6 animate-spin fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/><path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/></svg>
                                </div>
                            </div>
                        </custom-button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>


<script>

import CustomButton from "../../Shared/components/CustomButton.vue";
import SimpleIcon from "./SimpleIcon.vue";
import TagInput from "../../Shared/components/TagInput.vue";
import CustomInput from "../../Shared/components/CustomInput.vue";
import {ApiFactory} from "../services/api/factory";
import WysiwygEditor from "../../Shared/components/WysiwygEditor.vue";
import {useMailboxComposeEmailStore} from "../../../../stores/mailbox/composeEmail";
import {useMailboxStore} from "../../../../stores/mailbox/mailbox";
import useSimpleIcon from "../../../../composables/useSimpleIcon";
import Modal from "../../Shared/components/Modal.vue";
import {nextTick} from "vue";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import useErrorHandler from "../../../../composables/useErrorHandler";

export default {
     components: {LoadingSpinner, Modal, WysiwygEditor, CustomInput, TagInput, SimpleIcon, CustomButton},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
    },

    emits: ['expand', 'close', 'sent'],

    data() {
        return {
            api: ApiFactory.makeApiService('api'),
            mailboxStore: useMailboxStore(),
            composeEmailStore: useMailboxComposeEmailStore(),
            sending: false,
            inputVisibility: {
                cc: false,
                bcc: false,
            },

            flag: true,

            errorHandler: useErrorHandler(),
            simpleIcon: useSimpleIcon()
        }
    },

    mounted(){
        if (this.mailboxStore.userEmailSignature) {
            this.composeEmailStore.content = `${this.mailboxStore.userEmailSignature}${this.composeEmailStore.content}`
        }
    },

    computed: {
        editorWidth(){
            return this.composeEmailStore.getPositionStyle(this.composeEmailStore.getPosition()).AUTO_WIDTH
        },
        editorHeight(){
            return this.composeEmailStore.getPositionStyle(this.composeEmailStore.getPosition()).EDITOR_HEIGHT
        }
    },

    watch: {
        async editorWidth(){
            this.flag = false
            await this.$nextTick()
            this.flag = true
        },
        async editorHeight(){
            this.flag = false
            await this.$nextTick()
            this.flag = true
        },
    },

    methods: {
        handleExpand() {
            this.composeEmailStore.updatePosition(this.composeEmailStore.positions.FULL);
        },
        handleFix() {
            this.composeEmailStore.updatePosition(this.composeEmailStore.positions.FIXED);
        },
        handleMinimise() {
            this.composeEmailStore.updatePosition(this.composeEmailStore.positions.MINIMISED);
        },
        handleClose() {
            this.errorHandler.resetError()

            this.composeEmailStore.updatePosition(this.composeEmailStore.positions.CLOSED);
        },
        showInput(id) {
            this.inputVisibility[id] = true;
        },

		optionFactoryCallback(rawEmail) {
			return {
				"identifier_value": rawEmail,
			}
		},

		formatEmailsForSending(emails) {
			let emailAddresses = []
			emails.forEach((recipient) => {
				emailAddresses.push(recipient.identifier_value)
			})

			return emailAddresses
		},

        async sendEmail(){
            let toEmails = this.formatEmailsForSending(this.composeEmailStore.to);
            let bccEmails = this.formatEmailsForSending(this.composeEmailStore.bcc);
            let ccEmails = this.formatEmailsForSending(this.composeEmailStore.cc);

            if (this.composeEmailStore.type === 'reply') {
                await this.api.replyEmail(
                    this.composeEmailStore.replyTo.uuid, {
                        to: toEmails,
                        bcc: bccEmails,
                        cc: ccEmails,
                        content: this.composeEmailStore.content,
                    })
            }
            else if (this.composeEmailStore.type === 'forward') {
                await this.api.forwardEmail(
                    this.composeEmailStore.replyTo.uuid, {
                        to: toEmails,
                        bcc: bccEmails,
                        cc: ccEmails,
                        content: this.composeEmailStore.content,
                    })
            }
            else {
                await this.api.sendEmail(
                    {
                        to: toEmails,
                        bcc: bccEmails,
                        cc: ccEmails,
                        subject: this.composeEmailStore.subject,
                        content: this.composeEmailStore.content,
                    }
                )
            }
        },

        async handleSend() {
            this.errorHandler.resetError()
            this.sending = true;

            try {
                await this.sendEmail()
                this.composeEmailStore.resetComposeEmailData();
                this.mailboxStore.selectedTab = 'sent';

                if (this.composeEmailStore.position === this.composeEmailStore.positions.EMBEDDED) {
                    const selectedEmail = this.mailboxStore.selectedEmail
                    this.mailboxStore.selectedEmail = null;
                    this.mailboxStore.setSelectedEmail(selectedEmail)
                }

                this.$emit('sent')
                this.handleClose();
                await this.api.listEmails({page: 1})
            } catch (err) {
                this.errorHandler.handleError(err, 'An error occurred while sending the email')
            }

            this.sending = false;
        }
    }
};

</script>
