<template>
    <div class="flex flex-col p-4 gap-4">
        <view-edit-create-email-contact @contact-changed="handleContactChanged" :dark-mode="darkMode" :contacts="recipients" :loading="loading"></view-edit-create-email-contact>
        <loading-spinner v-if="loading"/>
        <div v-else class="overflow-auto border rounded-lg bg-white flex-1">
            <email-data v-for="(email, idx) in emails" :email="email" :idx="idx" :total="emails.length"/>
        </div>
    </div>
</template>


<script>

import CustomButton from "../../Shared/components/CustomButton.vue";
import SimpleIcon from "./SimpleIcon.vue";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import {useMailboxStore} from "../../../../stores/mailbox/mailbox";
import ComposeEmail from "./ComposeEmail.vue";
import Modal from "../../Shared/components/Modal.vue";
import EmailData from "./EmailData.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon";
import ViewEditCreateEmailContact from "./MailboxIdentifiedContacts/ViewEditCreateEmailContacts.vue";
import {useMailboxComposeEmailStore} from "../../../../stores/mailbox/composeEmail";

export default {
    components: {
        ViewEditCreateEmailContact,
        EmailData,
        Modal,
        ComposeEmail,
        LoadingSpinner,
        SimpleIcon,
        CustomButton
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            loading: false,
            mailboxStore: useMailboxStore(),
            emails: [],
            recipients: [],
            simpleIcon: useSimpleIcon()
        }
    },
    mounted(){
        this.getEmailData()
        if (!this.mailboxStore.selectedEmail.is_read) {
            this.mailboxStore.toggleRead(this.mailboxStore.selectedEmail, true)
        }
    },
    methods: {
        async getEmailData(){
            this.loading = true;
            const res = await this.mailboxStore.api.getDetailedEmail(this.mailboxStore.selectedEmail.uuid)
            this.emails = res.data.data.emails;
			this.recipients = res.data.data.identified_recipients;
            this.loading = false;
        },

        resizeIframe() {
            const iframe = document.getElementById("emailBodyFrame");
            iframe.height = iframe.contentWindow.document.body.scrollHeight + 100;
        },
		handleContactChanged() {
			this.getEmailData()
			this.mailboxStore.listEmails()
		}
    },
    watch: {
        'mailboxStore.selectedEmail'() {
            this.getEmailData()
        }
    }
};

</script>
