<template>
    <div class="grid grid-cols-12 gap-3 py-3 px-4">
        <div class="flex gap-10 items-center">
            <input
                :class="[!darkMode ? 'hover:bg-grey-50 border-grey-200' : 'bg-dark-background hover:bg-dark-175 border-blue-400']"
                class="rounded-sm w-5 h-5 cursor-pointer border"
                @click="mailboxStore.selectAllToggle()"
                :checked="mailboxStore.bulkSelect.length === mailboxStore.paginatedEmailsResponse?.data.length"
                type="checkbox"
            >
        </div>
        <div class="text-sm text-slate-500 col-span-2">
            {{mailboxStore.selectedTab === 'inbox' ? 'From' : 'To'}}
        </div>
        <div class="text-sm text-slate-500 col-span-6">
            Comment
        </div>
        <div class="text-sm text-slate-500">
            Replies
        </div>
        <div class="col-span-1 text-sm text-slate-500">
            Date
        </div>
		<div class="text-sm text-slate-500">
			Actions
		</div>
    </div>
</template>


<script>

import CustomButton from "../../Shared/components/CustomButton.vue";
import {useMailboxStore} from "../../../../stores/mailbox/mailbox";

export default {
    components: {CustomButton},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        emails: {
            type: Array,
            default: []
        }
    },
    data() {
        return {
            mailboxStore: useMailboxStore()
        }
    }
};

</script>
