<template>
    <div class="flex flex-col p-4 relative" :class="containerClasses" @click="toggleContent">
        <div class="flex justify-between mb-4">
            <div class="sticky">
                <div class="flex">
                    <p>{{ email.subject }}</p>
                    <Badge v-for="label in email.labels" class="whitespace-nowrap" color="gray">
                        {{label.name}}
                    </Badge>
                </div>
                <div v-if="!showContent" class="text-sm">
                    {{ email.snippet}}
                </div>
                <div v-if="showContent" class="flex flex-wrap gap-1 text-sm mt-1 items-center">
                    <p class="text-#475569 mr-1"> To:</p>
                    <limited-list expandable :show-count="6" :list-items="email.to">
                        <template #list-item="{item}">
                            <IdentifiedContactBadge :dark-mode="darkMode" :identified-contact="item" />
                        </template>
                        <template #hidden-item="{item}">
                            <IdentifiedContactBadge :dark-mode="darkMode" :identified-contact="item" />
                        </template>
                    </limited-list>
                </div>
                <div v-if="showContent" class="flex flex-wrap gap-1 text-sm mt-1 items-center">
                    <p class="text-#475569 mr-1">From: </p>
                    <IdentifiedContactBadge :dark-mode="darkMode" :identified-contact="email.from" />
                </div>
                <div v-if="showContent && email.cc.length > 0" class="flex flex-wrap gap-1 text-sm mt-1 items-center">
                    <p class="text-#475569 mr-1">CC: </p>
                    <limited-list expandable :show-count="6" :list-items="email.cc">
                        <template #list-item="{item}">
                            <IdentifiedContactBadge :dark-mode="darkMode" :identified-contact="item" />
                        </template>
                        <template #hidden-item="{item}">
                            <IdentifiedContactBadge :dark-mode="darkMode" :identified-contact="item" />
                        </template>
                    </limited-list>
                </div>
                <div v-if="showContent && email.bcc.length > 0" class="flex flex-wrap gap-1 text-sm mt-1 items-center">
                    <p class="text-#475569 mr-1">BCC: </p>
                    <limited-list expandable :show-count="6" :list-items="email.bcc">
                        <template #list-item="{item}">
                            <IdentifiedContactBadge :dark-mode="darkMode" :identified-contact="item" />
                        </template>
                        <template #hidden-item="{item}">
                            <IdentifiedContactBadge :dark-mode="darkMode" :identified-contact="item" />
                        </template>
                    </limited-list>
                </div>
            </div>
            <div class="flex-end shrink-0">
                <div class="flex gap-2">
                    <div class="text-sm">
                        {{ friendlyDate(email.sent_at) }}
                    </div>
                    <div v-if="showContent && showActions" class="flex justify-end">
                        <simple-icon clickable @click.stop="handleEmailEvent('forward')" :size="simpleIcon.sizes.SM" :color="simpleIcon.colors.BLACK" :icon="simpleIcon.icons.ARROW_UTURN_RIGHT" tooltip="Forward"></simple-icon>
                        <simple-icon clickable @click.stop="handleEmailEvent('replyAll')" :size="simpleIcon.sizes.SM" :color="simpleIcon.colors.BLACK" :icon="simpleIcon.icons.CURVED_DOUBLE_ARROW_LEFT" tooltip="Reply All"></simple-icon>
                        <simple-icon clickable @click.stop="handleEmailEvent('reply')" :size="simpleIcon.sizes.SM" :color="simpleIcon.colors.BLACK" :icon="simpleIcon.icons.ARROW_UTURN_LEFT" tooltip="Reply"></simple-icon>
                    </div>
                    <div v-if="showActions" class="cursor-pointer relative flex justify-end">
                        <DropdownMenu :dark-mode="darkMode" :options="emailActions" menu-placement="left">
                            <SimpleIcon clickable :size="simpleIcon.sizes.SM" :color="simpleIcon.colors.BLUE" :icon="simpleIcon.icons.ELLIPSIS_HORIZONTAL" tooltip="Actions" tooltip-placement="right"></SimpleIcon>
                        </DropdownMenu>
                    </div>
                </div>
            </div>
        </div>
        <div class="border-b mb-4" v-if="showContent"></div>
        <div :class="[showContent ? 'h-full' : 'h-0', 'overflow-hidden']">
            <loading-spinner v-if="loadingContent"/>
            <iframe width="100%" :class="loadingContent ? 'invisible' : 'visible'" :id="`email-body-${email.uuid}`" @load="resizeIframe" :srcdoc="email.content"></iframe>
        </div>
        <div v-if="email?.attachments?.length > 0" class="flex items-center gap-1 flex-wrap mt-2">
            <Badge v-for="attachment in email.attachments" class="whitespace-nowrap cursor-pointer opacity-60 hover:opacity-100" color="cyan" @click.stop="downloadAttachment(attachment)">
                {{attachment.filename}}
            </Badge>
        </div>
    </div>
</template>


<script>
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import Badge from "../../Shared/components/Badge.vue";
import SimpleIcon from "./SimpleIcon.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon";
import DropdownMenu from "./DropdownMenu.vue";
import ComposeEmail from "./ComposeEmail.vue";
import {DateTime} from "luxon";
import IdentifiedContactBadge from "./IdentifiedContact/IdentifiedContactBadge.vue";
import {useMailboxStore} from "../../../../stores/mailbox/mailbox.js";
import LimitedList from "../../Shared/components/Simple/LimitedList.vue";

export default {
    components: {
        LimitedList,
        IdentifiedContactBadge,
        DropdownMenu,
        ComposeEmail,
        SimpleIcon,
        Badge,
        LoadingSpinner,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        email: {
            type: Object,
            required: true
        },
        idx: {
            type: Number,
            default: 0
        },
        total: {
            type: Number,
            default: 1
        },
        showActions: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            loadingContent: true,
            showContent: false,
            simpleIcon: useSimpleIcon(),
            showAdditionalActions: false,
            mailboxStore: useMailboxStore()
        }
    },

    mounted() {
        this.showContent = this.isTheLastEmail
        this.email.uuid = this.email.uuid ? this.email.uuid : crypto.randomUUID()
    },

    computed: {
        emailActions() {
            return this.mailboxStore.getEmailActions(this.email, false, true);
        },
        isTheLastEmail() {
            return this.idx + 1 === this.total
        },

        containerClasses() {
            return [this.idx > 0 ? 'border-t' : '', this.total > 1 && !this.isTheLastEmail ? 'cursor-pointer' : '']
        }
    },

    methods: {
        async handleEmailEvent(eventType) {
            this.$emit('action', {
                action: eventType,
                email: this.email,
                from: 'embedded',
                idx: this.idx
            })
        },
        friendlyDate(date) {
            return DateTime.fromSQL(date).toLocaleString(DateTime.DATE_MED)
        },
        resizeIframe() {
            this.loadingContent = true;
            const iframe = document.getElementById(`email-body-${this.email.uuid}`);
            if (iframe) {
                iframe.height = iframe.contentWindow.document.body.scrollHeight + 100;
            }
            this.loadingContent = false;
        },
        toggleContent() {
            if (this.isTheLastEmail) return true;

            this.resizeIframe();
            this.showAdditionalActions = false
            this.showContent = !this.showContent;
        },
        downloadAttachment(attachment) {
            window.open(attachment.url, '_blank')
        },
    },
};

</script>
