<template>
    <div
        @click="handleClick"
         class="flex p-4 flex items-center"
         :class="[
             {'border-l-4 border-l-blue-550 border-y bg-cyan-100 text-primary-500': selected && !darkMode},
             {'border-l-4 border-l-blue-550 border-y bg-cyan-100 text-primary-500': selected && darkMode},
             {'': !selected && !darkMode},
             {'': !selected && darkMode},
             {'bg-gray-100 text-gray-500': disabled},
             {'cursor-pointer': !disabled},
             ]"
    >
        <div class="mr-2">
            <slot name="icon-left"></slot>
        </div>
        <div class="flex-1">
            {{label}}
        </div>
        <div>
            <slot name="icon-right"></slot>
        </div>
    </div>
</template>

<script>
export default {
    name: "MenuOption",
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        label: {
            type: String,
            default: "Menu Option"
        },
        selected: {
            type: Boolean,
            default: false
        },
        disabled: {
            type: Boolean,
            default: false,
        }
    },

    methods:{
        handleClick(){
            if (!this.disabled) {
                this.$emit('option-clicked')
            }
        }
    }
}

</script>
