<template>
    <div class="flex items-center gap-1">
        <slot v-if="showEmail">
            <p>{{ email }}</p>
        </slot>
        <simple-icon
            :icon="simpleIcon.icons.ENVELOPE"
            @click="handleClick"
            clickable
            :color="simpleIcon.colors.BLUE"
        />
    </div>
</template>
<script>
import {useGmailLink} from "../../../composables/useGmailLink.js";
import SimpleIcon from "./components/SimpleIcon.vue";
import useSimpleIcon from "../../../composables/useSimpleIcon.js";

const gmailLink = useGmailLink()

export default {
    name: 'EmailLink',
    computed: {
        simpleIcon() {
            return useSimpleIcon()
        }
    },
    components: {SimpleIcon},
    props: {
        email: {
            type: String,
        },
        showEmail: {
            type: Boolean,
            default: true
        }
    },
    methods: {
        useSimpleIcon,
        handleClick() {
            gmailLink.composeMail(this.email)
        }
    }
}
</script>
