<template>
    <div class="flex max-w-sm p-6 border border-gray-200 rounded-lg  flex-col gap-4"
         :class="{'bg-white-background bg-white': !darkMode, 'bg-gray-800 border-gray-700 hover:bg-gray-700': darkMode}"
    >
        <h5 class="text-xl font-medium pb-0 leading-none mr-5">Import Emails</h5>
        <div v-if="loading" class="flex-1 justify-center items-center flex">
            <loading-spinner/>
        </div>
        <div v-else-if="!loading && !errorMessage" class="flex-1 gap-3 flex flex-col">
            <simple-alert style="margin-bottom: 0;" content="The import might take several minutes" :dark-mode="darkMode"/>
            <p>Click to import emails from all mailbox users related to this company</p>
            <CustomButton @click="openSyncPage">
                Import
            </CustomButton>
        </div>
        <p v-else class="text-red-500">
            {{ errorMessage }}
        </p>
    </div>
</template>
<script>
import CustomButton from "../Shared/components/CustomButton.vue";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import SimpleAlert from "../Shared/components/SimpleAlert.vue";
import ApiService from "../Companies/services/api.js";
import {useToastNotificationStore} from "../../../stores/billing/tost-notification.store.js";

export default {
    name: 'ImportCompanyEmailsCard',
    components: {SimpleAlert, LoadingSpinner, CustomButton},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: Number,
            default: true
        }
    },
    data() {
        return {
            loading: false,
            api: ApiService.make(),
            errorMessage: null,
            toastNotification: useToastNotificationStore()
        }
    },
    methods: {
        async openSyncPage() {
            try {
                this.loading = true
                await this.api.importEmails(this.companyId)
                this.toastNotification.notifySuccess('Importing emails...')
            } catch (err) {
                this.errorMessage = err
                this.toastNotification.notifySuccess('Error trying to import emails.')
            }

            this.loading = false
        },
    }
}
</script>
