<template>
    <Modal
        :dark-mode="darkMode"
        :loading-confirmation="isConfirmationLoading"
        @confirm="handleConfirm"
        @close="handleClose"
        small
    >
        <template v-slot:header>
            Add note
        </template>
        <template v-slot:content>
            <div>
                <LoadingSpinner v-if="loadingNotes" />
                <div v-else class="flex flex-col gap-5 overflow-scroll m-h-32">
                    <div v-for="note in notes">
                        <div class="flex gap-1">
                            <p class="font-medium">{{note.author}}</p>
                            <p>-</p>
                            <p>
                                {{ $filters.dateFromTimestamp(note.created_at) }}
                            </p>
                        </div>
                        <div class="p-2 bg-gray-50 rounded h-full max-h-40 min-h-10 border">
                            <div v-html="note.content"></div>
                        </div>
                    </div>
                </div>
                <wysiwyg-editor
                    class="mt-4"
                    v-model="content"
                    :dark-mode="darkMode"
                    auto-width="100%"
                    :editor-height="200"
                >
                </wysiwyg-editor>
            </div>
        </template>
    </Modal>
</template>

<script>
import Modal from "../../Shared/components/Modal.vue";
import WysiwygEditor from "../../Shared/components/WysiwygEditor.vue";
import useNote from "../composables/useNote";
import {ApiFactory} from "../services/api/factory";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";

const note = useNote();

export default {
    name: 'AddNoteModal',
    components: {LoadingSpinner, WysiwygEditor, Modal},
    props: {
        darkMode: {
            type: Boolean,
            required: true,
        },
        apiDriver: {
            type: String,
            required: true,
        },
        relationId: {
            type: Number,
            required: true,
        },
        relationType: {
            type: String,
            required: true,
            validator: (value) => {
                return Object.values(note.relationTypes).includes(value)
            }
        }
    },

    data(){
        return {
            isConfirmationLoading: false,
            api: ApiFactory.makeApiService(this.apiDriver),

            loadingNotes: false,
            content: null,
            notes: [],
        }
    },

    mounted(){
        this.getNotes();
    },

    methods: {
        async getNotes(){
            this.loadingNotes = true;
            const response = await this.api.getNotes(this.relationId, this.relationType)
            this.notes = response.data.data.notes
            this.loadingNotes = false;
        },

        async handleConfirm(){
            this.isConfirmationLoading = true;
            const response = await this.api.addNote({
                content: this.content,
                relation_id: this.relationId,
                relation_type: this.relationType
            })
            this.notes = response.data.data.notes
            this.isConfirmationLoading = false;
            this.handleClose();
        },

        handleClose(){
            this.$emit('close');
        },
    }
}
</script>

<style scoped>

</style>
