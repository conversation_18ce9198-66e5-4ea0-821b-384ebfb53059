import axios from 'axios';
import {BaseApiService} from "./base";

export class ApiService extends BaseApiService {
    constructor(baseUrl, baseEndpoint, version) {
        super("ApiService");

        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    getNotes(relationId, relationType) {
        return this.axios().get('/', {
            params: {
                relation_id: relationId,
                relation_type: relationType
            }
        })
    }

    addNote({content, relation_id, relation_type, parent_id}) {
        return this.axios().post('/', {
            content,
            relation_id,
            relation_type,
            parent_id
        })
    }
}
