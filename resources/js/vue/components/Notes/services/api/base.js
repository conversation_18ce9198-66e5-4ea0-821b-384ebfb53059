class BaseApiService {
    constructor(serviceName = "BaseApiService") {
        this.serviceName = serviceName;
    }

    _throwUnimplementedError(fn) {
        throw new Error("Not implemented error. `" + this.serviceName + "::" + fn + "()`");
    }

    addNote() {
        this._throwUnimplementedError(this.addNote.name);

        return new Promise((resolve, reject) => reject());
    }
}

export { BaseApiService };
