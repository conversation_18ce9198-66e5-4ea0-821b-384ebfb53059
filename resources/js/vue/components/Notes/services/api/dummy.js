import {BaseApiService} from "./base";
import {REQUEST} from "../../../../../constants/APIRequestKeys";

class DummyApiService extends BaseApiService {
    constructor(delay = 150) {
        super("DummyApiService");

        this.delay = delay;
    }

    _makeResponse(data) {
        return new Promise((resolve, reject) => {
            setTimeout(() => resolve({ data: { data } }), this.delay);
        });
    }

    addNote() {
        return this._makeResponse({
            [REQUEST.STATUS]  : true,
        });
    }
}

export { DummyApiService };

