<x-app-layout>
    <div class="main-layout font-body" style="height: calc(100vh - 150px)">
        <div class="w-full h-full">
            <div class="w-full flex-auto pt-3 relative h-full"
                 :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
                <div class="px-10 h-full" :class="[darkMode ? 'text-white' : 'text-slate-900']" >
                    <div class="flex items-center justify-between flex-wrap py-5">
                        <h3 class="text-xl font-medium pb-0 leading-none mr-5">Mailbox</h3>
                    </div>
                    <mailbox :dark-mode="darkMode" api-driver="{{ config('services.mailbox.api_driver') }}"></mailbox>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
