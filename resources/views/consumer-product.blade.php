<x-app-layout title="Consumer Product">
    @if(isset($error))
        <div style="text-align: center; margin-top: 40px; color: red; font-weight: bold;">
            {{ $error }}
        </div>
    @else
        <div>
            <consumer-product
                communication-driver="{{ config('services.communication.driver') }}"
                api-driver="{{ config('services.lead_processing.api_driver') }}"
                future-campaign="{{ $futureCampaign ?? false }}"
                lead-basic="{{ json_encode($leadBasic) }}"
                @if(config('services.communication.driver') == 'twilio')
                    communication-token="{{ config('services.twilio.token') }}"
                @endif
                :dark-mode="darkMode"

                @if(isset($consumerProduct))
                    initial-lead-id="{{ $consumerProduct->id }}"
                @endif
            />
        </div>
    @endif
</x-app-layout>
