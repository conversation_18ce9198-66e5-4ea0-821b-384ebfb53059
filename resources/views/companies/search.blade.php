<x-app-layout title="Companies">
    @if (session('status'))
        <div id="flash-message" class="fixed top-4 left-1/2 transform -translate-x-1/2 bg-green-100 text-green-800 px-4 py-2 rounded shadow z-50">
            {{ session('status') }}
        </div>
        <script>
            setTimeout(() => {
                const flash = document.getElementById('flash-message');
                if (flash) flash.style.display = 'none';
            }, 4000);
        </script>
    @endif
    <div class="main-layout font-body">
        <div class="w-full">
            <div class="w-full flex-auto pt-3 relative"
                 :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
                <div class="px-10" :class="[darkMode ? 'text-white' : 'text-slate-900']">
                    <div class="flex items-center justify-between flex-wrap py-5">
                        <h3 class="text-xl font-medium pb-0 leading-none mr-5">Companies</h3>
                    </div>
                    <div>
                        <companies-search :dark-mode="darkMode" :restrict-account-manager="false"
                                          :industries="{{ json_encode($industries) }}"
                                          :account-managers="{{ json_encode($account_managers) }}"
                                          :success-managers="{{ json_encode($success_managers) }}"
                                          :us-states="{{ json_encode($us_states) }}"
                                          :campaign-statuses="{{ json_encode($campaign_statuses) }}"
                                          api-driver="{{ config('services.filters.api_driver') }}"
                                          user-id="{{ $user_id }}"
                                          :user-permissions="{{ json_encode($user_permissions) }}"
                        ></companies-search>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
