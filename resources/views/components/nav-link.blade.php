@if(request()->path() == $route)
    <p @click="toggleNavigationMenu" class="app-header-link cursor-pointer block cursor-pointer py-3 pl-5 pr-3 font-semibold transition duration-200 text-blue-550 border-l-4 border-primary-500 text-sm capitalize"
       :class="{'bg-dark-175 bg-opacity-25' : darkMode, 'bg-cyan-100 bg-opacity-75' : !darkMode}">
       {{ $linkName ?? str_replace('-', ' ', $name) }}
    </p>
@else
    <a @click="toggleNavigationMenu" class="app-header-link block cursor-pointer py-3 pl-5 pr-3 font-semibold transition duration-200 text-sm capitalize"
       :class="{'text-slate-400 hover:text-grey-200 hover:bg-blue-800 hover:bg-opacity-25 border-l-4 border-transparent' : darkMode, 'text-grey-400 hover:text-black hover:bg-grey-50 border-l-4 border-transparent' : !darkMode}"
       href="{{'/' . $route }}">
        {{ $linkName ?? str_replace('-', ' ', $name) }}
    </a>
@endif
