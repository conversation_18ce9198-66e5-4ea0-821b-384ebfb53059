<x-app-layout title="Dashboard">
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('DashboardModules') }}
        </h2>
    </x-slot>
    <div class="main-layout font-body">
        <div class="w-full">
            <div class="w-full flex-auto pt-3 relative"
                 :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
                <div class="px-10" :class="[darkMode ? 'text-white' : 'text-slate-900']">
                    <div class="flex items-center justify-between flex-wrap py-5">
                        <h3 class="text-xl font-medium pb-0 leading-none mr-5">Dashboard</h3>
                    </div>
                    <div>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-4 pb-16">
                            {{-- Will be re-deployed --}}
                            {{--@include('dashboard.modules.commission', ['gridClass' => 'col-span-3'])
                            @include('dashboard.modules.performance', ['gridClass' => 'col-span-3'])--}}
                            {{-- todo: Improve login to CMS --}}
                            @if(\Illuminate\Support\Facades\Auth::user()->hasPermissionTo('cms'))
                                <a class="border rounded-lg block p-6"
                                   :class="[darkMode ? 'bg-dark-module text-slate-50 border-dark-border' : 'bg-light-module text-slate-900 border-light-border']">
                                    <h5 class="text-xl font-medium pb-0 leading-none mr-5">Fixr.com<br><br></h5>
                                    <p class="font-normal text-sm text-slate-500 mb-6">Login to the Fixr CMS.
                                        <br>You must have a Fixr account setup.<br></p>
                                    <div class="grid gap-2">
                                        <external-auth title="WWW" auth-url="{{ config('app.fixr_domain') }}"></external-auth>
                                        @if(config('app.fixr_origin_domain'))
                                            <external-auth title="Origin (vpn required)" auth-url="{{ config('app.fixr_origin_domain') }}"></external-auth>
                                        @endif
                                    </div>
                                </a>
                                <a class="border rounded-lg block p-6"
                                   :class="[darkMode ? 'bg-dark-module text-slate-50 border-dark-border' : 'bg-light-module text-slate-900 border-light-border']">
                                    <h5 class="text-xl font-medium pb-0 leading-none mr-5">SolarReviews.com<br><br></h5>
                                    <p class="font-normal text-sm text-slate-500 mb-6">Login to the SolarReviews CMS.
                                        <br>You must have a SolarReviews account setup.<br></p>
                                    <external-auth title="WWW" class="col-span-6" auth-url="{{ config('app.solarreviews_frontend_domain') }}"></external-auth>
                                </a>
                            @endif
                            @if(\Illuminate\Support\Facades\Auth::user()->uses_mailbox)
                                <mailbox-sync-card :dark-mode="darkMode"></mailbox-sync-card>
                            @endif

                            @if(\Illuminate\Support\Facades\Auth::user()->uses_mailbox)
                                <google-services-integration-card :dark-mode="darkMode"></google-services-integration-card>
                            @endif
                            @if(\Illuminate\Support\Facades\Auth::user()->isBdm())
                                <registrations-log :dark-mode="darkMode" grid-class="col-span-1 md:col-span-2 lg:col-span-4"></registrations-log>
                            @endif
                            @if(\Illuminate\Support\Facades\Auth::user()->hasRole('account-manager'))
                                <inactive-campaigns :dark-mode="darkMode" grid-class="col-span-1 md:col-span-2 lg:col-span-4"></inactive-campaigns>
                            @endif
                            @if(\Illuminate\Support\Facades\Auth::user()->primaryPhone())
                                <communication-logs-overview :dark-mode="darkMode" class="md:col-span-2 lg:col-span-4"></communication-logs-overview>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
