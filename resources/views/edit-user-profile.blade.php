<x-app-layout title="Edit User Profile">
    <div class="main-layout font-body">
        <div class="w-full">
            <div class="w-full flex-auto pt-3 relative"
                 :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
                <div class="px-10" :class="[darkMode ? 'text-white' : 'text-slate-900']">
                    <div class="flex items-center justify-between flex-wrap py-5">
                        <h3 class="text-xl font-medium pb-0 leading-none mr-5">Edit User Profile</h3>
                    </div>
                    <div class="">
                        <edit-user-profile
                            :dark-mode="darkMode"
                            :user="{{ $user }}"
                            :user-roles-and-permissions="{{ json_encode($userRoleAndPermissions) }}"
                            api-driver="{{ config('services.user_management.api_driver') }}"
                        ></edit-user-profile>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
