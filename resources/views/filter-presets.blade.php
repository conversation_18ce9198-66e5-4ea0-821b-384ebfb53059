<x-app-layout title="Filter Presets">
    <div class="main-layout font-body">
        <div class="w-full">
            <div class="w-full flex-auto pt-3 relative"
                 :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
                <div class="px-10" :class="[darkMode ? 'text-white' : 'text-slate-900']">
                    <div class="flex items-center flex-wrap py-5">
                        <a href="/user-settings" class="text-xl font-medium text-slate-500 cursor-pointer pb-0 leading-none">User Settings</a>
                        <svg class="mx-2 stroke-current" width="7" height="12" viewBox="0 0 7 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M1 1L6 6L1 11" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <h3 class="text-xl font-medium pb-0 leading-none">Configure Filter Presets</h3>
                    </div>
                    <div class="">
                        <filter-preset-config
                            :dark-mode="darkMode"
                            :user="{{$user}}"
                            :filter-preset="{{$filterPreset}}"
                        ></filter-preset-config>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
