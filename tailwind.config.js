import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';

export default {
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
        './resources/js/vue/**/*.vue',
        './resources/js/vue/**/*.js',
    ],
    safelist: [
        'col-span-6',
        'col-span-5',
        'col-span-4',
        'col-span-3',
        'col-span-2',
        'top-14',
        'top-12',
        {
            pattern: /grid-cols-\d+/,
            variants: ['sm', 'md', 'lg', 'xl', '2xl'],
        },
    ],

    theme: {
        extend: {
            gridTemplateColumns: {
                '13': 'repeat(13, minmax(0, 1fr))',
                '14': 'repeat(14, minmax(0, 1fr))',
                '15': 'repeat(15, minmax(0, 1fr))',
                '16': 'repeat(16, minmax(0, 1fr))',
            },
            zIndex: {
                '100': 100,
            },
            opacity: {
              '90': '0.9',
            },
            fontFamily: {
                sans: ['Inter', ...defaultTheme.fontFamily.sans],
            },
            fontSize: {
                '2xs': '0.6rem',
            },
            boxShadow: {
                'module': '1px 6px 15px rgba(0, 0, 0, 0.06)',
                'menu': '3px 6px 10px rgba(0, 0, 0, 0.03)'
            },
            height: {
                '13': '3.2rem',
                '60': '15.2rem',
                '80': '24rem',
                '88': '26rem',
                '100': '30rem',
                '120': '40rem',
            },
            width: {
                '100': '30rem',
                '102': '25.5rem',
                '200': '60rem',
                '300': '90rem',
                '80': '23rem',
            },
            maxWidth: {
                '7xl': '89rem',
            },
            colors: {
                transparent: 'transparent',
                current: 'currentColor',

                black: '#000',
                white: '#fff',

                dark: {
                    'background': '#0F1A24',
                    'module': '#14222F',
                    'border': '#20354A',

                    // Get rid of these once colors are upto date on latest builds.
                    0: '#0F2030',
                    30: '#0D1A26',
                    40: '#0E1B28',
                    90: '#0D1B28',
                    175: '#192E43',
                },
                light: {
                    'background': '#F5F6F6',
                    'module': '#FFFFFF',
                    'border': '#D5DDE5',
                },
                primary: {
                    50: '#ECF6FF',
                    100: '#DDEEFF',
                    200: '#B6DBFF',
                    300: '#72B9FF',
                    400: '#3B9BF9',
                    500: '#0081FF',
                    600: '#0054A6',
                    700: '#004385',
                    800: '#003060',
                    900: '#00203F',
                },
                grey: {
                    25: '#FAFAFA',
                    30: '#FAFBFF',
                    40: '#FBFBFB',
                    50: '#F9F9F9',
                    75: '#F3F5FA',
                    90: '#F9FAFB',
                    100: '#EAECEE',
                    120: '#ECECEC',
                    125: '#E7E7E7',
                    150: '#EBEEF0',
                    175: '#E4E4E4',
                    200: '#CACFD3',
                    250: '#BCC6CA',
                    300: '#ABB2B9',
                    350: '#ACACAC',
                    400: '#6B7885',
                    450: '#526372',
                    475: '#3C5670',
                    500: '#2C3E50',
                    600: '#283848',
                    700: '#1A2530',
                    800: '#141C24',
                    900: '#0D1318',
                },
                red: {
                    50: '#FEFBFB',
                    75: '#FFE6E6',
                    100: '#FBE6E6',
                    200: '#F5BFBF',
                    300: '#EE9999',
                    350: '#E25555',
                    400: '#E24D4D',
                    450: '#FF5B5B',
                    500: '#D50000',
                    600: '#C00000',
                    700: '#800000',
                    800: '#600000',
                    900: '#400000',
                },
                orange: {
                    50: '#FFFDFB',
                    100: '#FFF0E6',
                    200: '#FFD9BF',
                    300: '#FFC299',
                    400: '#FF944D',
                    500: '#FF6600',
                    600: '#E65C00',
                    700: '#993D00',
                    800: '#732E00',
                    900: '#4D1F00',
                },
                yellow: {
                    50: '#FFFEFB',
                    100: '#FFFAE7',
                    200: '#FFF2C4',
                    300: '#FFEAA1',
                    400: '#FFDB5A',
                    500: '#FFCB13',
                    600: '#E6B711',
                    700: '#997A0B',
                    800: '#735B09',
                    900: '#4D3D06',
                },
                green: {
                    50: '#FBFDFC',
                    100: '#E6F3EA',
                    150: '#EAFFE7',
                    200: '#BFE2CA',
                    300: '#99D0AA',
                    400: '#4DAD6B',
                    450: '#43D449',
                    500: '#008A2B',
                    550: '#00AE07',
                    600: '#007C27',
                    700: '#00531A',
                    800: '#003E13',
                    900: '#00290D',
                },
                cyan: {
                    50: '#FCFEFF',
                    100: '#E8F7FD',
                    125: '#ECF8FE',
                    150: '#D7F4FF',
                    200: '#C4ECF9',
                    300: '#A1E0F5',
                    400: '#5BC8EE',
                    500: '#14B1E7',
                    600: '#129FD0',
                    700: '#0C6A8B',
                    800: '#095068',
                    900: '#063545',
                },
                blue: {
                    50: '#FBFCFE',
                    100: '#E6EEF6',
                    200: '#BFD4E9',
                    300: '#99BBDB',
                    400: '#72B9FF',
                    500: '#0054A6',
                    550: '#0081FF',
                    600: '#004C95',
                    700: '#00375F',
                    800: '#00264B',
                    900: '#001C30',
                }
            },
        },
    },
    plugins: [forms],
    darkMode: ['selector', '.bg-dark-background'],
}
