import fs from 'fs';
import path from 'path';
import mjml from 'mjml';

const mjmlFolder = path.join(__dirname, "/resources/views/emails/mjml/js/templates");
const buildFolder = path.join(__dirname, "/resources/views/emails/mjml/js/build");


/**
 * This script will compile any MJML files in the resources/views/emails/mjml/js/templates
 * directory and convert them to .html files.
 */
fs.readdir(mjmlFolder, (err, files) => {
    if (err) {
        return console.error(err);
    }
    let html;
    let fileContent;

    files.forEach(file => {
        console.warn("Template: " + file);
        fileContent = fs.readFileSync(path.join(mjmlFolder, file));
        fileContent = mjml(fileContent.toString());
        html = path.join(buildFolder, file.replace(".mjml", ".html"));
        fs.writeFileSync(html, fileContent.html);
    });
});
