# SolarReviews Admin  
Project for SolarReviews' Admin 2.0.

# Setting up
### How to get leads into your queue
- To create leads run the following command from your project root
  - ./artisan create:multi-industry-lead --leads=10 --service=8 --zip=90080 --state=CA
  - It will generate 10 leads in total
- Make sure the jobs are running
  - check the jobs table in database
  - Run the queues ./artisan queue:work 
  - To run the specific queue: ./artisan queue:work --queue=appointment_allocation_queue 
  - To run multiple queues: ./artisan queue:work --queue=myJobQueue, myJobQueue1, myJobQueue2,..myJobQueue7
- Inorder to see the leads
  - Add the user to the processor list for the specific service that your lead has been created for
  - This can be done in lead-processing-management page in Admin 2.0
  - Go to lead-processing page, you should now have list of leads for processing
- If nothing comes up,
  - double check that your jobs are running as expected
  - check for the queue restrictions queries in associated repository
  
   
### Running tests locally
1. Copy the `.env.testing.example` file to `env.testing`:

   ``
   cp .env.testing.example .env.testing
   ``
2. Use the variables in the `.env.testing` file on LastPass, ask your manager to share this with you if you don't have it.
3. Create the local databases `test_admin_solarreviews` and `test_solarreviews`
   
    ``
    mysql -u root -p -e "CREATE DATABASE test_admin_solarreviews; CREATE DATABASE test_solarreviews;
    ``
4. Run the tests from your project root:

    ``phpunit``

## Adding Environment Variables to CI/CD Pipeline

When adding a new environment variable that needs to be available across all deployment environments, follow these steps to ensure it's properly configured in all CI/CD files.

### Prerequisites
**IMPORTANT**: Before adding the variable to any CI files, you must first add it to the Bitbucket deployment environment variables in the Bitbucket repository settings. This ensures the variable is available during the pipeline execution.

### Required Files to Update

Follow the pattern established by existing variables like `AUTH_TOKEN_SINGING_KEY`. You need to update these files in the following order:

#### 1. bitbucket-pipelines.yml
Add the variable in **two locations**:

**a) Initialize Environment Step** (around line 205):
```yaml
- echo export YOUR_NEW_VAR=${YOUR_NEW_VAR} | tee -a .deployment-env
```

**b) Deploy Step** (around line 584):
```yaml
--from-literal=YOUR_NEW_VAR=$YOUR_NEW_VAR \
```

#### 2. server/export-env.sh
Add the export line (around line 123):
```bash
echo YOUR_NEW_VAR=${YOUR_NEW_VAR} | tee -a /app/.env && \
```

#### 3. kubernetes/patch-secrets.yml
Add the Kubernetes secret configuration (around line 656):
```yaml
- name: YOUR_NEW_VAR
  valueFrom:
    secretKeyRef:
      name: sr-admin-example-secret
      key: YOUR_NEW_VAR
```

### Verification Checklist

After making the changes, verify:

- [ ] Variable is added to Bitbucket deployment environment variables
- [ ] Variable is in `bitbucket-pipelines.yml` initialize-environment step
- [ ] Variable is in `bitbucket-pipelines.yml` deploy step kubectl create secret command
- [ ] Variable is in `server/export-env.sh`
- [ ] Variable is in `kubernetes/patch-secrets.yml`
- [ ] Variable follows the same pattern as existing variables (e.g., `AUTH_TOKEN_SINGING_KEY`)

### Coverage

These changes ensure the environment variable is available in:
- All Docker containers (main app, cron, horizon workers) via `export-env.sh`
- All Kubernetes deployments via `patch-secrets.yml`
- All Bitbucket pipeline deployments via the pipeline configuration

