<?php

namespace Database\Factories\Cadence;

use App\Models\Cadence\CompanyCadenceRoutine;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyCadenceRoutine>
 */
class CompanyCadenceRoutineFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            CompanyCadenceRoutine::FIELD_COMPANY_ID                   => fake()->randomNumber(),
            CompanyCadenceRoutine::FIELD_CADENCE_ROUTINE_ID           => fake()->randomNumber(),
            CompanyCadenceRoutine::FIELD_CONTACT_DECISION_MAKERS_ONLY => fake()->boolean(),
            CompanyCadenceRoutine::FIELD_CONTACT_ON_WEEKDAYS_ONLY     => fake()->boolean(),
            CompanyCadenceRoutine::FIELD_DELETED_AT                   => null,
            CompanyCadenceRoutine::FIELD_STATUS                       => fake()->randomElement([CompanyCadenceRoutine::STATUS_PENDING, CompanyCadenceRoutine::STATUS_CONCLUDED]),
            CompanyCadenceRoutine::FIELD_DOMAIN                       => fake()->word(),
            CompanyCadenceRoutine::FIELD_USE_ACCOUNT_MANAGER          => fake()->boolean(),
            CompanyCadenceRoutine::FIELD_USER_ID                      => fake()->randomNumber(),
        ];
    }
}
