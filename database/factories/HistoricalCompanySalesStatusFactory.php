<?php

namespace Database\Factories;

use App\Database\Casts\AsHistoricalCompanySalesStatus;
use App\DataModels\HistoricalCompanySalesStatusDataModel;
use App\Models\HistoricalCompanySalesStatus;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<HistoricalCompanySalesStatus>
 */
class HistoricalCompanySalesStatusFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            HistoricalCompanySalesStatus::FIELD_COMPANY_ID   => fake()->randomNumber(),
            HistoricalCompanySalesStatus::FIELD_YEAR         => fake()->year(),
            HistoricalCompanySalesStatus::FIELD_SALES_STATUS => new HistoricalCompanySalesStatusDataModel(),
        ];
    }
}
