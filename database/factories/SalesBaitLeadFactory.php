<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\LeadCampaign;
use App\Models\SalesBaitLead;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<\App\Models\SalesBaitLead>
 */
final class SalesBaitLeadFactory extends Factory
{
    /**
    * The name of the factory's corresponding model.
    *
    * @var  string
    */
    protected $model = SalesBaitLead::class;

    /**
    * Define the model's default state.
    *
    * @return  array
    */
    public function definition(): array
    {
        $leadCampaignUuid = LeadCampaign::factory()->create()->{LeadCampaign::UUID};

        $leadCampaign = LeadCampaign::query()
            ->where(LeadCampaign::UUID, $leadCampaignUuid)
            ->firstOrFail();

        return [
            SalesBaitLead::FIELD_LEAD_ID       => 0,
            SalesBaitLead::FIELD_COMPANY_ID    => $leadCampaign->{LeadCampaign::COMPANY_ID},
            SalesBaitLead::FIELD_CAMPAIGN_ID   => $leadCampaign->{LeadCampaign::ID},
            SalesBaitLead::FIELD_CLICKS        => $this->faker->numberBetween(0, 5),
            SalesBaitLead::FIELD_ACTIONS_TAKEN => $this->faker->numberBetween(0, 5),
            SalesBaitLead::FIELD_CREATED_AT    => $this->faker->dateTime()->format('Y-m-d H:i:s')
        ];
    }

    /**
     * @inheritDoc
     */
    public function configure()
    {
        parent::configure();

        return $this->afterCreating(function (SalesBaitLead $salesBaitLead) {
            if ($salesBaitLead->lead_id === 0) {
                $salesBaitLead->lead_id = EloquentQuote::query()
                    ->latest(EloquentQuote::TIMESTAMP_ADDED)
                    ->take(100)
                    ->get()
                    ->shuffle()
                    ->first()?->quoteid ?? 0;

                $salesBaitLead->save();
            }
        });
    }
}
