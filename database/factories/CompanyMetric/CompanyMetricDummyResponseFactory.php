<?php

namespace Database\Factories\CompanyMetric;
use App\Contracts\Services\CompanyMetricsServiceContract;
use App\Enums\CompanyMetrics\CompanyMetricSources;
use App\Services\CompanyMetrics\SimilarWebDummyMetricsService;
use App\Services\CompanyMetrics\SpyFuDummyMetricsService;

class CompanyMetricDummyResponseFactory
{
    /**
     * @param CompanyMetricSources $source
     * @return CompanyMetricsServiceContract
     */
    public static function make(CompanyMetricSources $source): CompanyMetricsServiceContract
    {
        return match ($source) {
            CompanyMetricSources::SIMILAR_WEB   => new SimilarWebDummyMetricsService(),
            default                             => new SpyFuDummyMetricsService(),
        };
    }
}

