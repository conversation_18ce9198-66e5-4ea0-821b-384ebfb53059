<?php

namespace Database\Factories;

use App\Models\Legacy\EloquentAddress;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class EloquentAddressFactory extends Factory
{
    protected $model = EloquentAddress::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            EloquentAddress::ID => $this->faker->numberBetween(1)
        ];
    }
}
