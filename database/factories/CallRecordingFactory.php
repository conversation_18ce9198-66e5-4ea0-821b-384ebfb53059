<?php

namespace Database\Factories;

use App\Models\CallRecording;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CallRecording>
 */
class CallRecordingFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        $testAudioLink = "https://www.kozco.com/tech/organfinale.wav";

        return [
            CallRecording:: FIELD_CALL_ID          => $this->faker->numberBetween(1, 19),
            CallRecording:: FIELD_DURATION_SECONDS => 13,
            CallRecording:: FIELD_RECORDING_LINK   => $testAudioLink,
            CallRecording:: FIELD_SID              => '',
        ];
    }
}
