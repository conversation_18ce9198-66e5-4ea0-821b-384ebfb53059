<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\Odin\Company;
use Illuminate\Support\Carbon;
use Spatie\Permission\Models\Role;
use App\Models\CompanyUserRelationship;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyUserRelationship>
 */
class CompanyUserRelationshipFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            CompanyUserRelationship::FIELD_USER_ID => User::factory(),
            CompanyUserRelationship::FIELD_COMPANY_ID => Company::factory(),
            CompanyUserRelationship::FIELD_ROLE_ID => Role::inRandomOrder()->first()?->id ?? Role::findOrCreate('test')->id,
            CompanyUserRelationship::FIELD_CONCLUSION => $this->faker->sentence(),
            CompanyUserRelationship::FIELD_PAYLOAD => [],
            CompanyUserRelationship::FIELD_COMMISIONABLE_AT => $this->faker->dateTimeThisYear(),
        ];
    }

    public function quietCompanyCreation($data = []): Factory
    {
        return $this->state(function (array $attributes) use ($data) {
            return [
                'company_id' => Company::factory()->createQuietly($data),
            ];
        });
    }

    public function accountManager(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'role_id' => Role::findOrCreate('account-manager')->id,
            ];
        });
    }

    public function businessDevelopmentManager(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'role_id' => Role::findOrCreate('business-development-manager')->id,
            ];
        });
    }

    public function past90Days(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'created_at' => now()->subDay(90)
            ];
        });
    }

    public function inactive(?Carbon $deleted_at = null): Factory
    {
        if ($deleted_at === null) {
            $deleted_at = now();
        }

        return $this->state(function (array $attributes) use ($deleted_at) {
            return compact('deleted_at');
        });
    }
}
