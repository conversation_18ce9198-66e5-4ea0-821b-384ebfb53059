<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Model;

abstract class BaseFactory extends Factory
{
    /**
     * @param string $model
     * @return Model|null
     */
    public function getRandomModel(string $model): ?Model
    {
        return $model::query()->inRandomOrder()->first();
    }

    /**
     * @param string $model
     * @return int
     */
    public function getRandomId(string $model): int
    {
        return $this->getRandomModel($model)?->id ?? $this->faker->randomNumber(1,100);
    }

    abstract public function definition();
}
