<?php

namespace Database\Factories;

use App\Models\LeadProcessingTeam;
use App\Models\LeadProcessor;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\LeadProcessor>
 */
class LeadProcessorFactory extends Factory
{
    protected $model = LeadProcessor::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            LeadProcessor::FIELD_USER_ID => $this->faker->randomNumber(),
            LeadProcessor::FIELD_LEAD_PROCESSING_TEAM_ID => LeadProcessingTeam::factory(),
            LeadProcessor::FIELD_CREATED_AT => $this->faker->dateTime(),
            LeadProcessor::FIELD_UPDATED_AT => $this->faker->dateTime(),
            LeadProcessor::FIELD_DELETED_AT => null
        ];
    }
}
