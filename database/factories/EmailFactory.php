<?php

namespace Database\Factories;

use App\Models\Odin\CompanyUser;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Email>
 */
class EmailFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'to_address' => fake()->email(),
            'from_address' => fake()->email(),
            'subject' => fake()->sentence(),
            'body' => fake()->sentence(),
            'to_company_user_id' => CompanyUser::factory(),
            'from_user_id' => User::factory(),
        ];
    }
}
