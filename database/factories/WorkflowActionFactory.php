<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Enums\ActionType;
use App\Models\Workflow;
use App\Models\WorkflowAction;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<\App\Models\WorkflowAction>
 */
final class WorkflowActionFactory extends Factory
{
    /**
    * The name of the factory's corresponding model.
    *
    * @var  string
    */
    protected $model = WorkflowAction::class;

    /**
    * Define the model's default state.
    *
    * @return  array
    */
    public function definition(): array
    {
        return [
            'previous_node_id' => 0,
            'previous_node_type' => '',
            'display_name' => 'Test Notification',
            'action_type' => ActionType::ADD_NOTIFICATION,
            'payload' => json_decode(
                '{"message": "Testing Notifications", "subject": "Test Notification", "targets": [{"target_type": "staff", "target_relation": "company-relationship-manager"}]}',
                true
            ),
            'workflow_id' => Workflow::factory(),
        ];
    }
}
