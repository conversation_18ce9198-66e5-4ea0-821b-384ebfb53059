<?php

namespace Database\Factories;

use App\Enums\PrivacyManagement\PrivacyRequestStatuses;
use App\Models\PrivacyRequest;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<PrivacyRequest>
 */
class PrivacyRequestFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            PrivacyRequest::FIELD_UUID           => $this->faker->uuid,
            PrivacyRequest::FIELD_APPROVED_BY_ID => $this->faker->randomElement(User::all()),
            PrivacyRequest::FIELD_PAYLOAD        => $this->createPayload(),
            PrivacyRequest::FIELD_STATUS         => $this->faker->randomElement(PrivacyRequestStatuses::cases()),
            PrivacyRequest::FIELD_SOURCE         => $this->faker->url,
        ];
    }

    private function createPayload(): array
    {
        return [
            'first_name'  => $this->faker->name,
            'last_name'   => $this->faker->name,
            'email'       => $this->faker->email,
            'phone'       => $this->faker->phoneNumber,
            'description' => $this->faker->paragraph,
            'address'     => $this->faker->address,
        ];
    }
}
