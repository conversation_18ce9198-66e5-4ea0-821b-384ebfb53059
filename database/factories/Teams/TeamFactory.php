<?php

namespace Database\Factories\Teams;

use App\Enums\Team\TeamName;
use App\Models\Teams\Team;
use App\Models\Teams\TeamType;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Team>
 */
class TeamFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            Team::FIELD_NAME            => $this->faker->randomElement(TeamName::cases()),
            Team::FIELD_DESCRIPTION     => $this->faker->realText(32),
            Team::FIELD_TEAM_TYPE_ID    => TeamType::factory(),
            Team::FIELD_PARENT_TEAM_ID  => null,
        ];
    }
}
