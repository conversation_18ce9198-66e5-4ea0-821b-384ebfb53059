<?php

namespace Database\Factories\Teams;

use App\Enums\Team\TeamType as TeamTypeEnum;
use App\Models\Teams\Team;
use App\Models\Teams\TeamType;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<TeamType>
 */
class TeamTypeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            Team::FIELD_NAME        => $this->faker->randomElement(TeamTypeEnum::cases()),
            Team::FIELD_DESCRIPTION => $this->faker->sentence(),
        ];
    }
}
