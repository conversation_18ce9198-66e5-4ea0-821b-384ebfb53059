<?php

namespace Database\Factories\Teams;

use App\Models\Teams\Team;
use App\Models\Teams\TeamMember;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<TeamMember>
 */
class TeamMemberFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            TeamMember::FIELD_TITLE     => $this->faker->jobTitle(),
            TeamMember::FIELD_USER_ID   => User::factory(),
            TeamMember::FIELD_TEAM_ID   => Team::factory(),
        ];
    }
}
