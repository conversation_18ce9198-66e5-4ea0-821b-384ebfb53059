<?php

namespace Database\Factories\CompanyProfile;

use App\Models\CompanyProfile\CompanyProfile;
use App\Models\CompanyProfile\CompanyProfileLocation;
use App\Models\Legacy\Location;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyProfileLocation>
 */
class CompanyProfileLocationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            CompanyProfileLocation::FIELD_COMPANY_PROFILE_ID => CompanyProfile::factory(),
            CompanyProfileLocation::FIELD_RAW                => $this->faker->address,
            CompanyProfileLocation::FIELD_LOCATION_ID        => Location::factory(),
        ];
    }
}
