<?php

namespace Database\Factories\CompanyProfile;

use App\Models\CompanyProfile\CompanyProfile;
use App\Models\CompanyProfile\CompanyProfileIndustryService;
use App\Models\Odin\IndustryService;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyProfileIndustryService>
 */
class CompanyProfileIndustryServiceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            CompanyProfileIndustryService::FIELD_COMPANY_PROFILE_ID  => CompanyProfile::factory(),
            CompanyProfileIndustryService::FIELD_INDUSTRY_SERVICE_ID => IndustryService::factory(),
        ];
    }
}
