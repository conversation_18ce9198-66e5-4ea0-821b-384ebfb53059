<?php

namespace Database\Factories\CompanyProfile;

use App\Enums\Odin\StateAbbreviation;
use App\Http\Requests\Discovery\StoreCompanyProfileRequest;
use App\Models\CompanyProfile\CompanyProfile;
use App\Models\Odin\Company;
use App\Models\Odin\IndustryService;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyProfile>
 */
class CompanyProfileFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            CompanyProfile::FIELD_NAME              => $this->faker->name,
            CompanyProfile::FIELD_PROFILE_SLUG      => $this->faker->slug,
            CompanyProfile::FIELD_CATEGORIES        => array_map(fn() => $this->faker->word, range(1, 5)),
            CompanyProfile::FIELD_DESCRIPTION       => $this->faker->sentence,
            CompanyProfile::FIELD_GENERAL_INFO      => $this->faker->sentence,
            CompanyProfile::FIELD_HOURS             => array_map(fn() => $this->faker->time, range(1, 5)),
            CompanyProfile::FIELD_WEBSITES          => array_map(fn() => $this->faker->url, range(1, 5)),
            CompanyProfile::FIELD_PHONES            => array_map(fn() => $this->faker->phoneNumber, range(1, 5)),
            CompanyProfile::FIELD_ADDRESSES         => array_map(fn() => [
                'zip'   => $this->faker->postcode,
                'state' => $this->faker->randomElement(StateAbbreviation::class)->value,
                'raw'   => $this->faker->address
            ], range(1, 5)),
            CompanyProfile::FIELD_YEARS_IN_BUSINESS => $this->faker->numberBetween(1, 1000),
            CompanyProfile::FIELD_RATING            => $this->faker->randomFloat(1, 1, 5),
            CompanyProfile::FIELD_PAID              => $this->faker->boolean,
            CompanyProfile::FIELD_LOGO              => $this->faker->imageUrl,
            CompanyProfile::FIELD_IMAGES            => array_map(fn() => $this->faker->imageUrl, range(1, 5)),
            CompanyProfile::FIELD_EMAIL             => $this->faker->email,
            CompanyProfile::FIELD_OTHER_LINKS       => array_map(fn() => $this->faker->url, range(1, 5)),
            CompanyProfile::FIELD_SOCIAL_LINKS      => array_map(fn() => $this->faker->url, range(1, 5)),
            CompanyProfile::FIELD_DISCOVERY_SCORE   => $this->faker->numberBetween(1, 100),
            CompanyProfile::FIELD_PAYLOAD           => array_map(fn() => $this->faker->word, range(1, 5)),
            CompanyProfile::FIELD_CREATED_AT        => $this->faker->dateTime,
            CompanyProfile::FIELD_UPDATED_AT        => $this->faker->dateTime,
            CompanyProfile::FIELD_COMPANY_ID        => null,
            CompanyProfile::FIELD_SUMMARY_POSITIVES => $this->faker->text,
            CompanyProfile::FIELD_SUMMARY_NEGATIVES => $this->faker->text,
            CompanyProfile::FIELD_SUMMARY_OVERVIEW  => $this->faker->text,
            CompanyProfile::FIELD_SUMMARY_SOURCE    => $this->faker->url,
            CompanyProfile::FIELD_PUBLISHED         => $this->faker->boolean,
        ];
    }

    public function toApi()
    {
        return [
            StoreCompanyProfileRequest::NAME                  => $this->faker->name,
            StoreCompanyProfileRequest::CATEGORIES            => array_map(fn() => $this->faker->word, range(1, 5)),
            StoreCompanyProfileRequest::DESCRIPTION           => $this->faker->sentence,
            StoreCompanyProfileRequest::GENERAL_INFO          => $this->faker->sentence,
            StoreCompanyProfileRequest::HOURS                 => array_map(fn() => $this->faker->time, range(1, 5)),
            StoreCompanyProfileRequest::WEBSITES              => array_map(fn() => $this->faker->url, range(1, 5)),
            StoreCompanyProfileRequest::PHONES                => array_map(fn() => $this->faker->phoneNumber, range(1, 5)),
            StoreCompanyProfileRequest::ADDRESSES             => array_map(fn() => [
                StoreCompanyProfileRequest::ADDRESS_RAW       => $this->faker->address,
                StoreCompanyProfileRequest::ADDRESS_STATE     => $this->faker->randomElement(StateAbbreviation::class)->value,
                StoreCompanyProfileRequest::ADDRESS_POST_CODE => $this->faker->postcode
            ], range(1, 5)),
            StoreCompanyProfileRequest::YEARS_IN_BUSINESS     => $this->faker->numberBetween(1, 1000),
            StoreCompanyProfileRequest::RATING                => $this->faker->randomFloat(1, 1, 10),
            StoreCompanyProfileRequest::PAID                  => $this->faker->boolean,
            StoreCompanyProfileRequest::LOGO                  => $this->faker->imageUrl,
            StoreCompanyProfileRequest::EMAIL                 => $this->faker->email,
            StoreCompanyProfileRequest::OTHER_LINKS           => array_map(fn() => $this->faker->url, range(1, 5)),
            StoreCompanyProfileRequest::SOCIAL_LINKS          => array_map(fn() => $this->faker->url, range(1, 5)),
            StoreCompanyProfileRequest::DISCOVERY_SCORE       => $this->faker->numberBetween(1, 100),
            StoreCompanyProfileRequest::REVIEWS               => array_map(fn() => [
                StoreCompanyProfileRequest::REVIEW_TEXT               => $this->faker->text,
                StoreCompanyProfileRequest::REVIEW_RATING             => $this->faker->randomFloat(1, 1, 10),
                StoreCompanyProfileRequest::REVIEW_DATE               => $this->faker->date,
                StoreCompanyProfileRequest::REVIEW_AUTHOR             => $this->faker->name,
                StoreCompanyProfileRequest::REVIEW_HELPFUL            => $this->faker->numberBetween(1, 100),
                StoreCompanyProfileRequest::REVIEW_ADDITIONAL_RATINGS => array_map(fn() => ['rating' => $this->faker->numberBetween(1, 10), 'category' => $this->faker->word], range(1, 5)),
                StoreCompanyProfileRequest::REVIEW_SOURCE             => $this->faker->url,
            ], range(1, 5)),
            StoreCompanyProfileRequest::SLOGAN                => $this->faker->sentence,
            StoreCompanyProfileRequest::BRANDS                => array_map(fn() => $this->faker->word, range(1, 5)),
            StoreCompanyProfileRequest::PAYMENT_METHODS       => array_map(fn() => $this->faker->word, range(1, 5)),
            StoreCompanyProfileRequest::AKA                   => $this->faker->word,
            StoreCompanyProfileRequest::CATEGORY_SERVICES     => array_map(fn() => $this->faker->word, range(1, 5)),
            StoreCompanyProfileRequest::CLAIMED               => $this->faker->boolean,
            StoreCompanyProfileRequest::BBB_DATA              => ['link' => $this->faker->url, 'rating' => $this->faker->word],
            StoreCompanyProfileRequest::INDUSTRY_SERVICE_SLUG => IndustryService::factory()->create()->slug,
            StoreCompanyProfileRequest::DOMAINS => array_map(fn() => $this->faker->word, range(1, 5)),
        ];
    }
}
