<?php

namespace Database\Factories\CompanyProfile;

use App\Models\CompanyProfile\CompanyProfile;
use App\Models\CompanyProfile\CompanyProfileReview;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyProfileReview>
 */
class CompanyProfileReviewFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            CompanyProfileReview::FIELD_TEXT               => $this->faker->paragraph(),
            CompanyProfileReview::FIELD_RATING             => $this->faker->numberBetween(1, 5),
            CompanyProfileReview::FIELD_DATE               => $this->faker->dateTime,
            CompanyProfileReview::FIELD_AUTHOR             => $this->faker->name,
            CompanyProfileReview::FIELD_HELPFUL            => $this->faker->numberBetween(0, 1000),
            CompanyProfileReview::FIELD_ADDITIONAL_RATINGS => array_map(fn() => $this->faker->randomFloat(), range(1,5)),
            CompanyProfileReview::FIELD_SOURCE             => $this->faker->url,
            CompanyProfileReview::FIELD_COMPANY_PROFILE_ID => CompanyProfile::factory(),
            CompanyProfileReview::FIELD_CREATED_AT         => $this->faker->dateTime,
            CompanyProfileReview::FIELD_UPDATED_AT         => $this->faker->dateTime,
        ];
    }
}
