<?php

namespace Database\Factories\CompanyProfile;

use App\Models\CompanyProfile\CompanyProfile;
use App\Models\CompanyProfile\CompanyProfileIndustryService;
use App\Models\CompanyProfile\CompanyProfileLocation;
use App\Models\CompanyProfile\CompanyProfileServiceArea;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyProfileServiceArea>
 */
class CompanyProfileServiceAreaFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            CompanyProfileServiceArea::FIELD_COMPANY_PROFILE_ID                  => CompanyProfile::factory(),
            CompanyProfileServiceArea::FIELD_COMPANY_PROFILE_LOCATION_ID         => CompanyProfileLocation::factory(),
            CompanyProfileServiceArea::FIELD_COMPANY_PROFILE_INDUSTRY_SERVICE_ID => CompanyProfileIndustryService::factory(),
        ];
    }
}
