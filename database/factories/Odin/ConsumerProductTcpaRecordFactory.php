<?php

declare(strict_types=1);

namespace Database\Factories\Odin;

use App\Abstracts\ResetUniqueFactory;
use App\Models\Odin\ConsumerProductTcpaRecord;

final class ConsumerProductTcpaRecordFactory extends ResetUniqueFactory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var  string
     */
    protected $model = ConsumerProductTcpaRecord::class;

    /** @inheritDoc */
    public function getData(bool $reset = false): array
    {
        return [
            ConsumerProductTcpaRecord::FIELD_TCPA_ID => $this->faker->unique($reset)->randomNumber(4),
            ConsumerProductTcpaRecord::FIELD_TCPA_SERVICE_TYPE => $this->faker->unique($reset)->word()
        ];
    }
}
