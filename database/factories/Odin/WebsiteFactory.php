<?php

namespace Database\Factories\Odin;

use App\Models\Odin\Website;
use Illuminate\Database\Eloquent\Factories\Factory;

class WebsiteFactory extends Factory
{
    protected $model = Website::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            Website::FIELD_NAME  => $this->faker->company(),
            Website::FIELD_URL   => $this->faker->url()
        ];
    }
}
