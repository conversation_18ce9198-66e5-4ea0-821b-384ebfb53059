<?php

namespace Database\Factories\Odin;

use App\Abstracts\ResetUniqueFactory;
use App\Models\Odin\CompanyConfiguration;

class CompanyConfigurationFactory extends ResetUniqueFactory
{
    /** @inheritDoc */
    public function getData(bool $reset = false): array
    {
        return [
            CompanyConfiguration::FIELD_COMPANY_ID                    => 0,
            CompanyConfiguration::FIELD_ALLOW_LEADS_NO_CC             => 0,
            CompanyConfiguration::FIELD_ENABLE_TCPA_PLAYBACK          => 0,
            CompanyConfiguration::FIELD_NEVER_EXCEED_BUDGET           => 0,
            CompanyConfiguration::FIELD_DISALLOW_RANKING              => 0,
            CompanyConfiguration::FIELD_RECEIVE_OFF_HOUR_LEADS        => 0,
            CompanyConfiguration::FIELD_APPOINTMENTS_ACTIVE           => 1,
            CompanyConfiguration::FIELD_MI_APPOINTMENTS_ACTIVE        => 0,
            CompanyConfiguration::FIELD_REQUIRE_APPOINTMENTS_CALENDAR => 1
        ];
    }
}
