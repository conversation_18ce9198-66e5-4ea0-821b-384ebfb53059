<?php

namespace Database\Factories\Odin;

use App\Enums\MissedProducts\MissedProductReasonEventType;
use App\Models\MissedProducts\MissedProductReasonEvent;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<MissedProductReasonEvent>
 */
class MissedProductReasonEventFactory extends Factory
{
    protected $model = MissedProductReasonEvent::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            MissedProductReasonEvent::FIELD_COMPANY_ID          => $this->faker->randomNumber(),
            MissedProductReasonEvent::FIELD_COMPANY_CAMPAIGN_ID => $this->faker->randomNumber(),
            MissedProductReasonEvent::FIELD_EVENT_TYPE          => $this->faker->randomElement(MissedProductReasonEventType::class),
            MissedProductReasonEvent::FIELD_STARTED_AT          => $this->faker->dateTime(),
            MissedProductReasonEvent::FIELD_ENDED_AT            => $this->faker->dateTime(),
            MissedProductReasonEvent::FIELD_TOTAL               => $this->faker->randomNumber(),
        ];
    }
}
