<?php

namespace Database\Factories\Odin;

use App\Models\Odin\WebsiteApiKeyOrigin;
use Illuminate\Database\Eloquent\Factories\Factory;

class WebsiteApiKeyOriginFactory extends Factory
{
    protected $model = WebsiteApiKeyOrigin::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            WebsiteApiKeyOrigin::FIELD_ID      => 1,
            WebsiteApiKeyOrigin::FIELD_ORIGIN    => '*'
        ];
    }
}
