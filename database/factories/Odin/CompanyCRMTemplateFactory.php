<?php

namespace Database\Factories\Odin;

use App\Campaigns\Delivery\CRM\Enums\CRMType;
use App\Models\Campaigns\Modules\Delivery\CompanyCRMTemplate;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyCRMTemplate>
 */
class CompanyCRMTemplateFactory extends Factory
{
    protected $model = CompanyCRMTemplate::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            CompanyCRMTemplate::FIELD_COMPANY_ID   => $this->faker->randomNumber(),
            CompanyCRMTemplate::FIELD_CRM_TYPE     => $this->faker->randomElement(CRMType::class),
            CompanyCRMTemplate::FIELD_DISPLAY_NAME => $this->faker->word(),
            CompanyCRMTemplate::FIELD_PAYLOAD      => [],
        ];
    }
}
