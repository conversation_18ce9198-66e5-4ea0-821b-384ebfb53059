<?php

declare(strict_types=1);

namespace Database\Factories\Odin;

use App\Enums\Advertising\AdvertisingTrackType;
use App\Models\Affiliates\Affiliate;
use App\Models\Affiliates\Campaign;
use App\Models\Odin\ConsumerProductAffiliateRecord;
use Illuminate\Database\Eloquent\Factories\Factory;

class ConsumerProductAffiliateRecordFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var  string
     */
    protected $model = ConsumerProductAffiliateRecord::class;

    /**
     * @inheritDoc
     */
    public function definition()
    {
        return [
            ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID => Affiliate::factory(),
            ConsumerProductAffiliateRecord::FIELD_CAMPAIGN_ID => function(array $attributes) {
                return Campaign::factory()
                    ->create([
                        Campaign::FIELD_AFFILIATE_ID => $attributes[ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID]
                    ]);
            },
            ConsumerProductAffiliateRecord::FIELD_TRACK_NAME => $this->faker->randomElement(AdvertisingTrackType::cases()),
            ConsumerProductAffiliateRecord::FIELD_TRACK_CODE => $this->faker->randomNumber(6)
        ];
    }
}
