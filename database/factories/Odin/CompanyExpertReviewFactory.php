<?php

namespace Database\Factories\Odin;

use App\Enums\CompanyMediaAssetType;
use App\Models\Odin\CompanyExpertReview;
use App\Models\Odin\CompanyMediaAsset;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyExpertReview>
 */
class CompanyExpertReviewFactory extends Factory
{
    protected $model = CompanyExpertReview::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            CompanyExpertReview::FIELD_COMPANY_ID     => $this->faker->randomNumber(),
            CompanyExpertReview::FIELD_LEGACY_ID      => $this->faker->randomNumber(),
            CompanyExpertReview::FIELD_USER_ID        => $this->faker->randomNumber(),
            CompanyExpertReview::FIELD_LEGACY_USER_ID => $this->faker->randomNumber(),
            CompanyExpertReview::FIELD_BODY           => $this->faker->paragraph,
        ];
    }
}
