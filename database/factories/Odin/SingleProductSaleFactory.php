<?php

namespace Database\Factories\Odin;

use App\Models\Billing\Invoice;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\SingleProductSale;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<SingleProductSale>
 */
class SingleProductSaleFactory extends Factory
{
    protected $model = SingleProductSale::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            SingleProductSale::FIELD_COMPANY_ID            => Company::factory(),
            SingleProductSale::FIELD_PRICE                 => $this->faker->randomFloat(),
            SingleProductSale::FIELD_STATUS                => $this->faker->randomElement([
                SingleProductSale::FIELD_STATUS_FAILED,
                SingleProductSale::FIELD_STATUS_PAID,
                SingleProductSale::FIELD_STATUS_INITIAL,
            ]),
            SingleProductSale::FIELD_INVOICE_ID            => Invoice::factory(),
            SingleProductSale::FIELD_CONSUMER_PRODUCT_ID   => ConsumerProduct::factory(),
            SingleProductSale::FIELD_PRODUCT_ASSIGNMENT_ID => ProductAssignment::factory(),
        ];
    }
}
