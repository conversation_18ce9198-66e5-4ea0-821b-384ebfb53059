<?php

namespace Database\Factories\Odin;

use App\DataModels\HistoricalCompanyDailyStatusesDataModel;
use App\DataModels\HistoricalCompanyMonthlyStatusesDataModel;
use App\Enums\CompanyConsolidatedStatus;
use App\Models\Odin\Company;
use App\Models\Odin\HistoricalCompanyStatuses;
use Carbon\CarbonImmutable;
use Carbon\CarbonPeriod;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class HistoricalCompanyStatusesFactory extends Factory
{
    protected $model = HistoricalCompanyStatuses::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $now = CarbonImmutable::now('UTC');

        $period = CarbonPeriod::since($now->subDays(30))->days(1)->until($now);

        return [
            HistoricalCompanyStatuses::FIELD_COMPANY_ID => Company::factory(),
            HistoricalCompanyStatuses::FIELD_YEAR => $now->year,
            HistoricalCompanyStatuses::FIELD_DAILY_STATUSES => HistoricalCompanyDailyStatusesDataModel::fromArray((function() use ($period): array {
                $statuses = [];
                foreach($period as $date) {
                    $statuses[$date->format(HistoricalCompanyDailyStatusesDataModel::DATE_FORMAT)] = $this->faker->randomElement(CompanyConsolidatedStatus::cases())->value;
                }

                return $statuses;
            })()),
            HistoricalCompanyStatuses::FIELD_MONTHLY_STATUSES => HistoricalCompanyMonthlyStatusesDataModel::fromArray([
                "{$now->year}-1" => $this->faker->randomElement(CompanyConsolidatedStatus::cases())->value,
                "{$now->year}-2" => $this->faker->randomElement(CompanyConsolidatedStatus::cases())->value,
                "{$now->year}-3" => $this->faker->randomElement(CompanyConsolidatedStatus::cases())->value,
                "{$now->year}-4" => $this->faker->randomElement(CompanyConsolidatedStatus::cases())->value,
                "{$now->year}-5" => $this->faker->randomElement(CompanyConsolidatedStatus::cases())->value,
                "{$now->year}-6" => $this->faker->randomElement(CompanyConsolidatedStatus::cases())->value,
                "{$now->year}-7" => $this->faker->randomElement(CompanyConsolidatedStatus::cases())->value,
                "{$now->year}-8" => $this->faker->randomElement(CompanyConsolidatedStatus::cases())->value,
                "{$now->year}-9" => $this->faker->randomElement(CompanyConsolidatedStatus::cases())->value,
                "{$now->year}-10" => $this->faker->randomElement(CompanyConsolidatedStatus::cases())->value,
                "{$now->year}-11" => $this->faker->randomElement(CompanyConsolidatedStatus::cases())->value,
                "{$now->year}-12" => $this->faker->randomElement(CompanyConsolidatedStatus::cases())->value
            ])
        ];
    }
}
