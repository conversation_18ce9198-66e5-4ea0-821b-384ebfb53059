<?php

namespace Database\Factories\Odin;

use App\Models\Odin\CompanyReviewResponse;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyReviewResponse>
 */
class CompanyReviewResponseFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            CompanyReviewResponse::FIELD_USER_ID    => $this->faker->randomNumber(1, 7),
            CompanyReviewResponse::FIELD_BODY       => $this->faker->realText(),
            CompanyReviewResponse::FIELD_FIRST_NAME => $this->faker->firstName(),
            CompanyReviewResponse::FIELD_LAST_NAME  => $this->faker->lastName(),
            CompanyReviewResponse::FIELD_EMAIL      => $this->faker->safeEmail(),
            CompanyReviewResponse::FIELD_IP_ADDRESS => $this->faker->ipv4(),
            CompanyReviewResponse::FIELD_STATUS     => $this->faker->boolean(),
        ];
    }
}
