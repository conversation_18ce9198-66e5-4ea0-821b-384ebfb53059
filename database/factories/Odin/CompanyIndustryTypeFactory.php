<?php

namespace Database\Factories\Odin;

use App\Models\Odin\CompanyIndustryType;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyIndustryType>
 */
class CompanyIndustryTypeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            CompanyIndustryType::FIELD_COMPANY_ID       => fake()->randomNumber(),
            CompanyIndustryType::FIELD_INDUSTRY_TYPE_ID => fake()->randomNumber(),
        ];
    }
}
