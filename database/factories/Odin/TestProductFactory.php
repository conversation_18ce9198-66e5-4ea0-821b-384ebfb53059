<?php

namespace Database\Factories\Odin;

use App\Enums\TestProductStatus;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\Company;
use App\Models\Odin\Product;
use App\Models\Odin\ProductCampaign;
use App\Models\TestProduct;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<TestProduct>
 */
class TestProductFactory extends Factory
{
    protected $model = TestProduct::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            TestProduct::FIELD_PRODUCT_ID              => Product::factory(),
            TestProduct::FIELD_COMPANY_ID              => Company::factory(),
            TestProduct::FIELD_CAMPAIGN_ID             => ProductCampaign::factory(),
            TestProduct::FIELD_RELATION_ID             => CompanyCampaign::factory(),
            TestProduct::FIELD_RELATION_TYPE           => CompanyCampaign::class,
            TestProduct::FIELD_LEGACY_LEAD_CAMPAIGN_ID => $this->faker->randomNumber(),
            TestProduct::FIELD_EMAIL                   => $this->faker->email(),
            TestProduct::FIELD_PHONE                   => $this->faker->phoneNumber(),
            TestProduct::FIELD_STATUS                  => $this->faker->randomElement(TestProductStatus::class),
            TestProduct::FIELD_CONTACTED               => $this->faker->boolean(),
            TestProduct::FIELD_EXPIRE_AT               => $this->faker->dateTime(),
            TestProduct::FIELD_REVEAL_AT               => $this->faker->dateTime(),
            TestProduct::FIELD_CREATED_AT              => $this->faker->dateTime(),
            TestProduct::FIELD_CREATED_BY_ID           => User::factory()
        ];
    }
}
