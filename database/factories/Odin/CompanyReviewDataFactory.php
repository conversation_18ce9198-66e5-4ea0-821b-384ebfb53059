<?php

namespace Database\Factories\Odin;

use App\Models\Odin\CompanyReviewData;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyReviewData>
 */
class CompanyReviewDataFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        $ratings = [
            "5-Excellent",
            "4-Very Good",
            "3-Acceptable",
            "2-Poor",
            "1-Very Poor"
        ];
        $recommend = [
            "1-Yes",
            "0-No",
        ];
        $payloadData = [
            "zip_code"                      => $this->faker->postcode(),
            "system_size"                   => "{$this->faker->numberBetween(1,20)}kW",
            "installed_year"                => $this->faker->year(),
            "installation_type"             => $this->faker->randomElement($ratings),
            "rating_on_schedule"            => $this->faker->randomElement($ratings),
            "recommend_installer"           => $this->faker->randomElement($recommend),
            "rating_price_changed"          => $this->faker->randomElement($ratings),
            "rating_sales_process"          => $this->faker->randomElement($ratings),
            "rating_professionalism"        => $this->faker->randomElement($ratings),
            "rating_quality_of_advise"      => $this->faker->randomElement($ratings),
            "rating_after_sales_support"    => $this->faker->randomElement($ratings),
            "rating_quality_of_products"    => $this->faker->randomElement($ratings),
            "rating_quality_of_installation"=> $this->faker->randomElement($ratings),
        ];
        return [
            CompanyReviewData::FIELD_PAYLOAD    => $payloadData,
        ];
    }
}
