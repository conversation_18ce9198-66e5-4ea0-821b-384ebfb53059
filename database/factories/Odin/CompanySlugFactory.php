<?php

namespace Database\Factories\Odin;

use App\Models\CompanySlug;
use App\Models\Odin\Company;
use Illuminate\Database\Eloquent\Factories\Factory;

class CompanySlugFactory extends Factory
{
    protected $model = CompanySlug::class;

    public function definition(): array
    {
        return [
            CompanySlug::FIELD_COMPANY_ID                     => Company::factory(),
            CompanySlug::FIELD_SLUG                           => $this->faker->slug(),
            CompanySlug::FIELD_REDIRECT_COMPANY_SLUG_ID       => $this->faker->randomElement([null, CompanySlug::factory()]),
        ];
    }
}
