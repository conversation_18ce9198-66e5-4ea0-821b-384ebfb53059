<?php

namespace Database\Factories\Odin;

use App\Models\Odin\CompanyUser;
use Illuminate\Database\Eloquent\Factories\Factory;
use Ramsey\Uuid\Uuid;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class CompanyUserFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            CompanyUser::FIELD_COMPANY_ID => $this->faker->randomNumber(3),
            CompanyUser::FIELD_FIRST_NAME => $this->faker->firstName(),
            CompanyUser::FIELD_LAST_NAME => $this->faker->lastName(),
            CompanyUser::FIELD_TITLE => $this->faker->title(),
            CompanyUser::FIELD_EMAIL => "<EMAIL>",
            CompanyUser::FIELD_CELL_PHONE => 5555555555,
            CompanyUser::FIELD_OFFICE_PHONE => 5555555555,
            CompanyUser::FIELD_CAN_LOG_IN => true,
            CompanyUser::FIELD_FAILED_LOGIN_COUNT => $this->faker->randomNumber(),
            CompanyUser::FIELD_PASSWORD => $this->faker->password(),
            CompanyUser::FIELD_STATUS => CompanyUser::STATUS_ACTIVE,
            CompanyUser::FIELD_REFERENCE => Uuid::uuid4(),
            CompanyUser::FIELD_IS_CONTACT => false,
            CompanyUser::FIELD_EMAIL_VERIFIED_AT => $this->faker->dateTime()->format('Y-m-d H:i:s'),
            CompanyUser::FIELD_PHONE_VERIFIED_AT => $this->faker->dateTime()->format('Y-m-d H:i:s')
        ];
    }

    public function contact()
    {
        return $this->state(function() {
            return [
                CompanyUser::FIELD_CAN_LOG_IN         => false,
                CompanyUser::FIELD_IS_CONTACT         => true,
                CompanyUser::FIELD_FAILED_LOGIN_COUNT => 0,
                CompanyUser::FIELD_PASSWORD           => "",
                CompanyUser::FIELD_EMAIL_VERIFIED_AT  => null,
                CompanyUser::FIELD_PHONE_VERIFIED_AT  => null,
            ];
        });
    }
}
