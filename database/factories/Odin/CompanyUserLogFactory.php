<?php

namespace Database\Factories\Odin;

use App\Models\Odin\CompanyUserLog;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyUserLog>
 */
class CompanyUserLogFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            CompanyUserLog::FIELD_COMPANY_USER_ID   => $this->faker->randomNumber(),
            CompanyUserLog::FIELD_IP_ADDRESS        => $this->faker->ipv4(),
            CompanyUserLog::FIELD_TYPE              => 1,
        ];
    }
}
