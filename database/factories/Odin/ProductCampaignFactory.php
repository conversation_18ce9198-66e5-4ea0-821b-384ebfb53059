<?php

namespace Database\Factories\Odin;

use App\Models\Odin\ProductCampaign;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProductCampaign>
 */
class ProductCampaignFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            ProductCampaign::FIELD_PRODUCT_ID => 1,
            ProductCampaign::FIELD_COMPANY_ID => 1,
            ProductCampaign::FIELD_NAME => "Test Campaign",
            ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID => 0,
            ProductCampaign::FIELD_STATUS => true
        ];
    }
}
