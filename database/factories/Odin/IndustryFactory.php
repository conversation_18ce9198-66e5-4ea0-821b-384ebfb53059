<?php

namespace Database\Factories\Odin;

use App\Models\Odin\Industry;
use App\Enums\Odin\Industry as IndustryEnum;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class IndustryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = fake()->randomElement(IndustryEnum::cases())->value . '_' . fake()->randomNumber(3);

        return [
            Industry::FIELD_NAME => $name,
            Industry::FIELD_SLUG => Str::slug($name),
            Industry::FIELD_COLOR_DARK => fake()->hexColor(),
            Industry::FIELD_COLOR_LIGHT => fake()->hexColor(),
        ];
    }
}
