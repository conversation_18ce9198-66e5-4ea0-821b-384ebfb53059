<?php

namespace Database\Factories\Odin;

use App\Models\Odin\GenericProfitabilityAssumptionConfiguration;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<GenericProfitabilityAssumptionConfiguration>
 */
class GenericProfitabilityAssumptionConfigurationFactory extends Factory
{
    /**
     * Define the model's default state.
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            GenericProfitabilityAssumptionConfiguration::FIELD_COMPANY_ID                   => $this->faker->numberBetween(1, 20),
            GenericProfitabilityAssumptionConfiguration::FIELD_INDUSTRY_SERVICE_ID          => $this->faker->numberBetween(1, 6),
            GenericProfitabilityAssumptionConfiguration::FIELD_AVERAGE_JOB_COST             => $this->faker->numberBetween(200, 2500),
            GenericProfitabilityAssumptionConfiguration::FIELD_AVERAGE_LEAD_REVENUE         => $this->faker->numberBetween(50, 1000),
            GenericProfitabilityAssumptionConfiguration::FIELD_LABOUR_MATERIALS_COST        => $this->faker->numberBetween(50, 1000),
            GenericProfitabilityAssumptionConfiguration::FIELD_PERCENTAGE_LEADS_SUCCESSFUL  => $this->faker->numberBetween(25, 75),
        ];
    }
}
