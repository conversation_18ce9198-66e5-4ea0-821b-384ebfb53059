<?php

namespace Database\Factories\Odin;

use App\Models\PaymentCollection;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<PaymentCollection>
 */
class PaymentCollectionFactory extends Factory
{
    protected $model = PaymentCollection::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            PaymentCollection::FIELD_COMPANY_ID => $this->faker->randomNumber(),
            PaymentCollection::FIELD_PAID       => $this->faker->randomFloat(),
            PaymentCollection::FIELD_PAID_DATE  => $this->faker->dateTime(),
            PaymentCollection::FIELD_AMOUNT     => $this->faker->randomFloat(),
            PaymentCollection::FIELD_NOTES      => $this->faker->sentence(),
        ];
    }
}
