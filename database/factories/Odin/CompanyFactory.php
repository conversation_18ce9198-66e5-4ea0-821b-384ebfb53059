<?php

namespace Database\Factories\Odin;

use App\Abstracts\ResetUniqueFactory;
use App\Enums\Company\CompanyAdminStatus;
use App\Enums\CompanyConsolidatedStatus;
use App\Models\Legacy\EloquentCompany;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyIndustry;
use App\Models\Odin\CompanyService;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use Illuminate\Database\Eloquent\Factories\Factory;

class CompanyFactory extends ResetUniqueFactory
{
    /** @inheritDoc */
    public function getData(bool $reset = false): array
    {
        $domainName = "www.".$this->faker->unique($reset)->domainName();
        $companyName = $this->faker->company();

        return [
            Company::FIELD_LEGACY_ID => null,
            Company::FIELD_REFERENCE => $this->faker->uuid(),
            Company::FIELD_NAME => $companyName,
            Company::FIELD_ENTITY_NAME => $companyName,
            Company::FIELD_WEBSITE => $domainName,
            Company::FIELD_LINK_TO_LOGO => $domainName."/logo",
            Company::FIELD_STATUS => $this->faker->optional(0.25, Company::STATUS_LEADS_ACTIVE)->numberBetween(Company::STATUS_LEADS_ACTIVE, Company::STATUS_PROFILE_ONLY),
            Company::FIELD_ADMIN_STATUS => $this->faker->randomElement(array_column(CompanyAdminStatus::cases(), 'value')),
            Company::FIELD_ADMIN_LOCKED => $this->faker->boolean(5),
            Company::FIELD_ADMIN_APPROVED => $this->faker->boolean(90),
            Company::FIELD_ARCHIVED => $this->faker->boolean(5),
            Company::FIELD_IMPORTED => $this->faker->boolean(10),
            Company::FIELD_PRESCREENED_AT => $this->faker->unixTime(),
            Company::FIELD_CONSOLIDATED_STATUS => $this->faker->randomElement([
                CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS,
                CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CANNOT_RECEIVE_LEADS
            ])->value
        ];
    }

    /**
     * @return Factory
     */
    public function legacyCompany(): Factory
    {
        return $this->state(function(array $attributes) {
            return [
                Company::FIELD_LEGACY_ID => EloquentCompany::factory(),
            ];
        });
    }

    /**
     * Configure the model factory.
     *
     * @return $this
     */
    public function configure()
    {
        return $this->afterCreating(function (Company $company) {
            if(blank(IndustryService::all())) {
                IndustryService::factory()->create();
            }

            $iterator = 1;
            do {
                CompanyService::query()->firstOrCreate([
                    CompanyService::FIELD_COMPANY_ID => $company->id,
                    CompanyService::FIELD_INDUSTRY_SERVICE_ID => $this->faker->randomElement(IndustryService::all())
                ]);
                $iterator++;
            } while ($iterator <= rand(1, 4));

            do {
                CompanyIndustry::query()->firstOrCreate([
                    CompanyIndustry::FIELD_COMPANY_ID => $company->id,
                    CompanyIndustry::FIELD_INDUSTRY_ID => $this->faker->randomElement(Industry::all()),
                ]);
            } while ($iterator <= rand(1, 4));
        });
    }

    public function withLegacyCompany(int|null $legacyCompanyId = null) {
        return $this->state(function (array $attributes) use ($legacyCompanyId) {
            return [
                Company::FIELD_LEGACY_ID => EloquentCompany::factory()->withCompanyId($legacyCompanyId)
            ];
        });
    }
}
