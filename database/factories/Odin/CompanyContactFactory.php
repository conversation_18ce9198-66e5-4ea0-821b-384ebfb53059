<?php

namespace Database\Factories\Odin;

use App\Models\Odin\CompanyUser;
use Illuminate\Database\Eloquent\Factories\Factory;
use Ramsey\Uuid\Uuid;

/**
 * @extends Factory<CompanyUser>
 */
class CompanyContactFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            CompanyUser::FIELD_COMPANY_ID         => $this->faker->randomNumber(),
            CompanyUser::FIELD_FIRST_NAME         => $this->faker->firstName(),
            CompanyUser::FIELD_LAST_NAME          => $this->faker->lastName(),
            CompanyUser::FIELD_TITLE              => $this->faker->title(),
            CompanyUser::FIELD_DEPARTMENT         => $this->faker->word(),
            CompanyUser::FIELD_EMAIL              => $this->faker->email(),
            CompanyUser::FIELD_CELL_PHONE         => $this->faker->phoneNumber(),
            CompanyUser::FIELD_OFFICE_PHONE       => $this->faker->phoneNumber(),
            CompanyUser::FIELD_CAN_LOG_IN         => 0,
            CompanyUser::FIELD_STATUS             => 1,
            CompanyUser::FIELD_REFERENCE          => Uuid::uuid4(),
            CompanyUser::FIELD_IS_CONTACT         => 1,
        ];
    }
}
