<?php

namespace Database\Factories\Odin;

use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryLog;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyCampaignDeliveryLog>
 */
class CompanyCampaignDeliveryLogFactory extends Factory
{
    protected $model = CompanyCampaignDeliveryLog::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            CompanyCampaignDeliveryLog::FIELD_CAMPAIGN_ID => $this->faker->randomNumber(),
            CompanyCampaignDeliveryLog::FIELD_MODULE_ID => $this->faker->randomNumber(),
            CompanyCampaignDeliveryLog::FIELD_CRM_ID => $this->faker->randomNumber(),
            CompanyCampaignDeliveryLog::FIELD_CONSUMER_PRODUCT_ID => $this->faker->randomNumber(),
            CompanyCampaignDeliveryLog::FIELD_SUCCESS => $this->faker->boolean(),
            CompanyCampaignDeliveryLog::FIELD_PAYLOAD => [],
        ];
    }
}
