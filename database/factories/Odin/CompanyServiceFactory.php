<?php

namespace Database\Factories\Odin;

use App\Models\Odin\CompanyData;
use App\Models\Odin\CompanyService;
use App\Models\Odin\IndustryService;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyData>
 */
class CompanyServiceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            CompanyService::FIELD_INDUSTRY_SERVICE_ID => $this->faker->randomElement(IndustryService::all()),
            CompanyService::FIELD_COMPANY_ID => $this->faker->randomElement(IndustryService::all()),
        ];
    }
}
