<?php

namespace Database\Factories\Odin;

use App\Enums\LeadRefundStatus;
use App\Models\LeadRefund;
use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<LeadRefund>
 */
class LeadRefundFactory extends Factory
{
    protected $model = LeadRefund::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return array(
            LeadRefund::FIELD_COMPANY_ID   => Company::factory(),
            LeadRefund::FIELD_STATUS       => $this->faker->randomElement(LeadRefundStatus::class),
            LeadRefund::FIELD_TOTAL        => $this->faker->randomNumber(),
            LeadRefund::FIELD_CREATED_AT   => $this->faker->dateTime,
            LeadRefund::FIELD_UPDATED_AT   => $this->faker->dateTime,
            LeadRefund::FIELD_REVIEWED_AT  => $this->faker->dateTime,
            LeadRefund::FIELD_REQUESTED_BY => User::factory(),
            LeadRefund::FIELD_REVIEWED_BY  => User::factory(),
        );
    }
}
