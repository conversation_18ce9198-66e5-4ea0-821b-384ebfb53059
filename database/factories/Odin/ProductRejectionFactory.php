<?php

namespace Database\Factories\Odin;

use App\Models\Odin\ProductRejection;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<ProductRejection>
 */
class ProductRejectionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $rejectionReasons = [ "ak", "em", "ph", "bp", "nc", "du", "dug", "ni", "ot", "oa", "sc", "si", "sh", "sp", "tw", "no", "ah", "ro" ];

        return [
            ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID => $this->faker->numberBetween(1, 19),
            ProductRejection::FIELD_COMPANY_USER_ID       => $this->faker->numberBetween(1, 19),
            ProductRejection::FIELD_REASON                => "{$this->faker->randomElement($rejectionReasons)}|{$this->faker->realText(24)}",
        ];
    }
}
