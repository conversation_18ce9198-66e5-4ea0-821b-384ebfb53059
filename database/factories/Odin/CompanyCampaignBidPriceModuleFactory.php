<?php

namespace Database\Factories\Odin;

use App\Models\Campaigns\Modules\Bidding\CompanyCampaignBidPriceModule;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyCampaignBidPriceModule>
 */
class CompanyCampaignBidPriceModuleFactory extends Factory
{
    protected $model = CompanyCampaignBidPriceModule::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            CompanyCampaignBidPriceModule::FIELD_CAMPAIGN_ID => $this->faker->randomNumber(),
        ];
    }
}
