<?php

namespace Database\Factories\Odin;

use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use Illuminate\Database\Eloquent\Factories\Factory;
use Ramsey\Uuid\Uuid;

/**
 * @extends Factory<CompanyLocation>
 */
class CompanyLocationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            CompanyLocation::FIELD_COMPANY_ID => Company::factory(),
            CompanyLocation::FIELD_ADDRESS_ID => Address::factory(),
            CompanyLocation::FIELD_REFERENCE => Uuid::uuid4()->toString(),
            CompanyLocation::FIELD_NAME => "{$this->faker->city} Office",
            CompanyLocation::FIELD_PHONE => $this->faker->phoneNumber(),
            CompanyLocation::FIELD_IMPORTED => 0,
        ];
    }
}
