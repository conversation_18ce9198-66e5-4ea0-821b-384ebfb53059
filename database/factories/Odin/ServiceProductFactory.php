<?php

namespace Database\Factories\Odin;

use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ServiceProduct;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class ServiceProductFactory extends Factory
{
    protected $model = ServiceProduct::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            ServiceProduct::FIELD_INDUSTRY_SERVICE_ID => IndustryService::factory(),
            ServiceProduct::FIELD_PRODUCT_ID => Product::factory()
        ];
    }
}
