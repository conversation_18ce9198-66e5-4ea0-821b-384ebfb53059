<?php

namespace Database\Factories\Odin;

use App\Models\Odin\AppointmentOffering;
use Exception;
use Illuminate\Database\Eloquent\Factories\Factory;
use Ramsey\Uuid\Uuid;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class AppointmentOfferingFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     * @throws Exception
     */
    public function definition()
    {
        return [
            AppointmentOffering::FIELD_CONSUMER_PRODUCT_ID => $this->faker->randomNumber(3),
            AppointmentOffering::FIELD_PRODUCT_CAMPAIGN_BUDGET_ID => $this->faker->randomNumber(3),
            AppointmentOffering::FIELD_BEST_REVENUE_SCENARIO_LOGS_RUN_ID => $this->faker->randomNumber(3),
            AppointmentOffering::FIELD_STATUS => AppointmentOffering::STATUS_NOT_RECEIVED,
            AppointmentOffering::FIELD_LINK_KEY => Uuid::uuid4(),
            AppointmentOffering::FIELD_LINK_CODE => 123456,
            AppointmentOffering::FIELD_ATTEMPT => 1,
            AppointmentOffering::FIELD_SALE_TYPE_ID => 4,
            AppointmentOffering::FIELD_PRICE => 100,
            AppointmentOffering::FIELD_START_TIME => null,
            AppointmentOffering::FIELD_EXPIRY_TIME => null
        ];
    }
}
