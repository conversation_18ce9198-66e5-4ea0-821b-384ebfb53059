<?php

namespace Database\Factories\Odin;

use App\Models\TopCompanyByCounty;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<TopCompanyByCounty>
 */
class TopCompanyByCountyFactory extends Factory
{
    protected $model = TopCompanyByCounty::class;
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            TopCompanyByCounty::FIELD_COMPANY_ID                => $this->faker->randomNumber(),
            TopCompanyByCounty::FIELD_INDUSTRY_SERVICE_ID       => $this->faker->randomNumber(),
            TopCompanyByCounty::FIELD_COUNTY_LOCATION_ID        => $this->faker->randomNumber(),
            TopCompanyByCounty::FIELD_COMPANY_NAME              => $this->faker->name(),
            TopCompanyByCounty::FIELD_COMPANY_REFERENCE         => $this->faker->uuid(),
            TopCompanyByCounty::FIELD_COMPANY_REVIEW_COUNT      => $this->faker->randomNumber(),
            TopCompanyByCounty::FIELD_COMPANY_BAYESIAN_ALL_TIME => $this->faker->randomFloat(),
        ];
    }
}
