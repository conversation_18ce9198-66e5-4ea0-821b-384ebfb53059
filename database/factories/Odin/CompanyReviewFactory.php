<?php

namespace Database\Factories\Odin;

use App\Models\Odin\CompanyReview;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyReview>
 */
class CompanyReviewFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            CompanyReview::FIELD_COMPANY_ID      => $this->faker->numberBetween(1, 19),
            CompanyReview::FIELD_REL_ID          => $this->faker->numberBetween(1, 19),
            CompanyReview::FIELD_CONSUMER_ID     => $this->faker->numberBetween(1, 19),
            CompanyReview::FIELD_REL_TYPE        => 2,
            CompanyReview::FIELD_FIRST_NAME      => $this->faker->firstName(),
            CompanyReview::FIELD_LAST_NAME       => $this->faker->lastName(),
            CompanyReview::FIELD_EMAIL           => $this->faker->safeEmail(),
            CompanyReview::FIELD_PHONE           => $this->faker->phoneNumber(),
            CompanyReview::FIELD_EMAIL_VALIDATED => $this->faker->boolean(),
            CompanyReview::FIELD_PHONE_VALIDATED => $this->faker->boolean(),
            CompanyReview::FIELD_TITLE           => $this->faker->realText(32),
            CompanyReview::FIELD_BODY            => $this->faker->realText(128),
            CompanyReview::FIELD_OVERALL_SCORE   => 'overall_score',
            CompanyReview::FIELD_STATUS          => $this->faker->numberBetween(-2, 1),
            CompanyReview::FIELD_IP_ADDRESS      => $this->faker->ipv4(),
        ];
    }

    /**
     * Configure the model factory.
     * @return Factory
     */
    public function configure(): Factory
    {
        return $this->afterCreating(fn(CompanyReview $review) =>
            $review->{CompanyReview::FIELD_REL_ID} = $review->{CompanyReview::FIELD_COMPANY_ID}
        );
    }
}
