<?php

namespace Database\Factories\Odin;

use App\Models\CompanyContract;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyContract>
 */
class CompanyContractFactory extends Factory
{
    protected $model = CompanyContract::class;
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            CompanyContract::FIELD_UUID => $this->faker->uuid,
            CompanyContract::FIELD_COMPANY_ID => $this->faker->randomNumber(),
            CompanyContract::FIELD_COMPANY_USER_ID => $this->faker->randomNumber(),
            CompanyContract::FIELD_CONTRACT_TYPE => $this->faker->word,
            CompanyContract::FIELD_CONTRACT => $this->faker->word,
            CompanyContract::FIELD_AGREED_AT => $this->faker->dateTime,
            CompanyContract::FIELD_IP_ADDRESS => $this->faker->ipv4,
            CompanyContract::FIELD_CONTRACT_ID => $this->faker->randomNumber(),
            CompanyContract::FIELD_SIGNATURE_ID => $this->faker->randomNumber(),
            CompanyContract::FIELD_FILE_URL => $this->faker->url(),
        ];
    }
}
