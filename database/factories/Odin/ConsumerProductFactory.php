<?php

declare(strict_types=1);

namespace Database\Factories\Odin;

use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductAffiliateRecord;
use App\Models\Odin\ConsumerProductData;
use App\Models\Odin\ConsumerProductTcpaRecord;
use App\Models\Odin\ConsumerProductTracking;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ServiceProduct;
use Illuminate\Database\Eloquent\Factories\Factory;

class ConsumerProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ConsumerProduct::class;

    /**
     * @inheritDoc
     */
    public function definition()
    {
        return [
            ConsumerProduct::FIELD_CONSUMER_ID => Consumer::factory(),
            ConsumerProduct::FIELD_SERVICE_PRODUCT_ID => ServiceProduct::factory(),
            ConsumerProduct::FIELD_ADDRESS_ID => Address::factory(),
            ConsumerProduct::FIELD_GOOD_TO_SELL => $this->faker->boolean(),
            ConsumerProduct::FIELD_STATUS => $this->faker->randomElement(ConsumerProduct::STATUSES),
            ConsumerProduct::FIELD_CONTACT_REQUESTS => $this->faker->numberBetween(1, 4),
            ConsumerProduct::FIELD_CONSUMER_PRODUCT_DATA_ID => ConsumerProductData::factory(),
            ConsumerProduct::FIELD_CONSUMER_PRODUCT_TCPA_RECORD_ID => ConsumerProductTcpaRecord::factory(),
            ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID => ConsumerProductTracking::factory(),
            ConsumerProduct::FIELD_CONSUMER_PRODUCT_AFFILIATE_RECORD_ID => ConsumerProductAffiliateRecord::factory(),
            ConsumerProduct::FIELD_CREATED_AT => $this->faker->dateTime()->format('Y-m-d H:i:s')
        ];
    }

    public function goodToSell(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                ConsumerProduct::FIELD_GOOD_TO_SELL => true
            ];
        });
    }

    /**
     * Generate a consumer product specifically for a lead
     */
    public function lead(): Factory
    {
        return $this->state(function (array $attributes) {
            $industryService = IndustryService::factory()->for(Industry::factory()->create([
                'name' => 'test',
                'slug' => 'test',
            ]))->create();

            $product = Product::firstOrCreate([
                'name' => \App\Enums\Odin\Product::LEAD->value,
            ]);

            return [
                'service_product_id' => ServiceProduct::firstOrcreate([
                    'industry_service_id' => $industryService->id,
                    'product_id' => $product->id,
                ]),
            ];
        });
    }
}
