<?php

namespace Database\Factories\Odin;

use App\Campaigns\Delivery\CRM\Enums\CRMType;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleCRM;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyCampaignDeliveryModuleCRM>
 */
class CompanyCampaignDeliveryModuleCRMFactory extends Factory
{
    protected $model = CompanyCampaignDeliveryModuleCRM::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            CompanyCampaignDeliveryModuleCRM::FIELD_MODULE_ID => $this->faker->randomNumber(),
            CompanyCampaignDeliveryModuleCRM::FIELD_CRM_TYPE => $this->faker->randomElement(CRMType::class),
            CompanyCampaignDeliveryModuleCRM::FIELD_ACTIVE => $this->faker->boolean(),
            CompanyCampaignDeliveryModuleCRM::FIELD_DISPLAY_NAME => $this->faker->word(),
            CompanyCampaignDeliveryModuleCRM::FIELD_PAYLOAD => [],
            CompanyCampaignDeliveryModuleCRM::FIELD_TEMPLATE_ID => $this->faker->randomNumber(),
        ];
    }
}
