<?php

namespace Database\Factories\Odin;

use App\Enums\Odin\BudgetCategory;
use App\Enums\Odin\QualityTier;
use App\Models\Odin\ProductCampaignBudget;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProductCampaignBudget>
 */
class ProductCampaignBudgetFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            ProductCampaignBudget::FIELD_PRODUCT_CAMPAIGN_ID => 1,
            ProductCampaignBudget::FIELD_QUALITY_TIER => QualityTier::IN_HOME->value,
            ProductCampaignBudget::FIELD_CATEGORY => BudgetCategory::VERIFIED->value,
            ProductCampaignBudget::FIELD_VALUE => 100,
            ProductCampaignBudget::FIELD_VALUE_TYPE => ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_SPEND,
            ProductCampaignBudget::FIELD_STATUS => true,
            ProductCampaignBudget::FIELD_BUDGET_START_TIMESTAMP => Carbon::now()->subWeek(),
            ProductCampaignBudget::FIELD_MAX_BUDGET_USAGE => 115
        ];
    }
}
