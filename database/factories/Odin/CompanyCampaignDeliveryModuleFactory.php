<?php

namespace Database\Factories\Odin;

use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModule;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyCampaignDeliveryModule>
 */
class CompanyCampaignDeliveryModuleFactory extends Factory
{
    protected $model = CompanyCampaignDeliveryModule::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            CompanyCampaignDeliveryModule::FIELD_CAMPAIGN_ID => $this->faker->randomNumber(),
        ];
    }
}
