<?php

declare(strict_types=1);

namespace Database\Factories\Odin;

use App\Abstracts\ResetUniqueFactory;
use App\Models\Legacy\Location;
use App\Models\Odin\Address;

final class AddressFactory extends ResetUniqueFactory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var  string
     */
    protected $model = Address::class;

    /** @inheritDoc */
    public function getData(bool $reset = false): array
    {
        return [
            Address::FIELD_ADDRESS_1 => $this->faker->unique($reset)->streetAddress(),
            Address::FIELD_ADDRESS_2 => $this->faker->unique($reset)->secondaryAddress(),
            Address::FIELD_CITY      => $this->faker->unique($reset)->city(),
            Address::FIELD_STATE     => $this->faker->stateAbbr(),
            Address::FIELD_ZIP_CODE  => Location::factory()->zipcode()->create()->{Location::ZIP_CODE},
            Address::FIELD_COUNTRY   => 'US',
            Address::FIELD_LATITUDE  => $this->faker->unique($reset)->latitude(),
            Address::FIELD_LONGITUDE => $this->faker->unique($reset)->longitude(),
            Address::FIELD_UTC       => '-'.$this->faker->numberBetween(4, 10), //-4 is Eastern Daylight Time and -10 is Hawaii Standard Time
            Address::FIELD_PLACE_ID => $this->faker->unique($reset)->uuid()
        ];
    }
}
