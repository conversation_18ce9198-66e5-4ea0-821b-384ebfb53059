<?php

namespace Database\Factories\Odin;

use App\Enums\Odin\Product as ProductEnum;
use App\Models\Odin\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class ProductFactory extends Factory
{
    protected $model = Product::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            Product::FIELD_NAME => $this->faker->randomElement(ProductEnum::cases())->value
        ];
    }
}
