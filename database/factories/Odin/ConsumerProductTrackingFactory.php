<?php

declare(strict_types=1);

namespace Database\Factories\Odin;

use App\Abstracts\ResetUniqueFactory;
use App\DataModels\Odin\ConsumerProductTrackingPayloadDataModel;
use App\Enums\Advertising\AdvertisingTrackType;
use App\Models\Odin\ConsumerProductTracking;
use App\Models\Odin\Website;

final class ConsumerProductTrackingFactory extends ResetUniqueFactory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var  string
     */
    protected $model = ConsumerProductTracking::class;

    /** @inheritDoc */
    public function getData(bool $reset = false): array
    {
        return [
            ConsumerProductTracking::URL_START => 'www.solarreviews.com', //Specify at creation
            ConsumerProductTracking::URL_CONVERT => 'www.solarreviews.com/convert', //Specify at creation
            ConsumerProductTracking::CALCULATOR_SOURCE => 'open-calculator.closedcalculatorflow'.$this->faker->randomDigit(),
            ConsumerProductTracking::WEBSITE_ID => Website::factory(),
            ConsumerProductTracking::AD_TRACK_TYPE => $this->faker->randomElement(AdvertisingTrackType::cases()),
            ConsumerProductTracking::AD_TRACK_CODE => $this->faker->unique($reset)->randomNumber(6),
            ConsumerProductTracking::CONVERSION_UPLOADED => false,
            ConsumerProductTracking::ESTIMATED_REVENUE => $this->faker->randomFloat(2, 1, 100),
            ConsumerProductTracking::PAYLOAD => ConsumerProductTrackingPayloadDataModel::fromJson('{}'),
            ConsumerProductTracking::CREATED_AT => $this->faker->dateTime()->format('Y-m-d H:i:s')
        ];
    }
}
