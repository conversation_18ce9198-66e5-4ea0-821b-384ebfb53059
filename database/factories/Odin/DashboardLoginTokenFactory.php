<?php

namespace Database\Factories\Odin;

use App\Models\Odin\DashboardLoginToken;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<DashboardLoginToken>
 */
class DashboardLoginTokenFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            DashboardLoginToken::FIELD_COMPANY_ID      => fake()->randomNumber(),
            DashboardLoginToken::FIELD_COMPANY_USER_ID => fake()->randomNumber(),
            DashboardLoginToken::FIELD_SHADOWER_ID     => fake()->randomNumber(),
            DashboardLoginToken::FIELD_TOKEN           => fake()->uuid(),
            DashboardLoginToken::FIELD_EXPIRED         => fake()->boolean(),
            DashboardLoginToken::FIELD_EXPIRES_AT      => fake()->dateTime(),
        ];
    }
}
