<?php

namespace Database\Factories\Odin;

use App\Models\Odin\WebsiteApiKey;
use App\Services\Odin\ApiKeyGeneratorService;
use Illuminate\Database\Eloquent\Factories\Factory;

class WebsiteApiKeyFactory extends Factory
{
    protected $model = WebsiteApiKey::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        $apiKeyGeneratorService = new ApiKeyGeneratorService();
        return [
            WebsiteApiKey::FIELD_ID      => 1,
            WebsiteApiKey::FIELD_NAME    => 'test key',
            WebsiteApiKey::FIELD_STATUS  => 1,
            WebsiteApiKey::FIELD_KEY     => $apiKeyGeneratorService->generateKey()
        ];
    }
}
