<?php

namespace Database\Factories\Odin;

use App\Models\Odin\GlobalType;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class GlobalTypeFactory extends Factory
{
    protected $model = GlobalType::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            GlobalType::FIELD_NAME => 'Installer',
            GlobalType::FIELD_KEY => 'installer'
        ];
    }
}
