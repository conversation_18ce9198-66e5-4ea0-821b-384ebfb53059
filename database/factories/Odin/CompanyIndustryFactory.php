<?php

namespace Database\Factories\Odin;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyData;
use App\Models\Odin\CompanyIndustry;
use App\Models\Odin\Industry;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyData>
 */
class CompanyIndustryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            CompanyIndustry::FIELD_INDUSTRY_ID => Industry::factory(),
            CompanyIndustry::FIELD_COMPANY_ID => Company::factory(),
        ];
    }
}
