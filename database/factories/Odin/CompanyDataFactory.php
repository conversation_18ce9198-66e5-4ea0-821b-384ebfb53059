<?php

namespace Database\Factories\Odin;

use App\Models\Odin\CompanyData;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyData>
 */
class CompanyDataFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        $payload = [
            "office_in_usa" => $this->faker->numberBetween(1, 5),
            "employee_count" => $this->faker->numberBetween(2, 100),
            "family_business" => $this->faker->numberBetween(0, 1),
            "disallow_ranking" => $this->faker->numberBetween(0, 1),
            "year_started_solar" => $this->faker->year(),
            "never_exceed_budget" => $this->faker->numberBetween(0, 1),
            "revenue_in_thousands" => $this->faker->numberBetween(1, 250),
            "year_started_business" => $this->faker->year(),
            "allow_lead_sales_without_cc" => $this->faker->numberBetween(0, 1),
            "enable_lead_compliance_jornaya" => $this->faker->numberBetween(0, 1),
            "enable_watchdog_compliance_links" => $this->faker->numberBetween(0, 1),
        ];
        return [
            CompanyData::FIELD_PAYLOAD      => json_encode($payload),
        ];
    }
}
