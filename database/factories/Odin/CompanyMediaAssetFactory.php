<?php

namespace Database\Factories\Odin;

use App\Enums\CompanyMediaAssetType;
use App\Models\Odin\CompanyMediaAsset;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyMediaAsset>
 */
class CompanyMediaAssetFactory extends Factory
{
    protected $model = CompanyMediaAsset::class;
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            CompanyMediaAsset::FIELD_COMPANY_ID => $this->faker->randomNumber(),
            CompanyMediaAsset::FIELD_NAME => $this->faker->word(),
            CompanyMediaAsset::FIELD_URL => $this->faker->url(),
            CompanyMediaAsset::FIELD_TYPE => $this->faker->randomElement(CompanyMediaAssetType::class),
        ];
    }
}
