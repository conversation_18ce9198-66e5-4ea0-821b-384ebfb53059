<?php

declare(strict_types=1);

namespace Database\Factories\Odin;

use App\Abstracts\ResetUniqueFactory;
use App\Models\Odin\ConsumerConfigurableFieldCategory;

final class ConsumerConfigurableFieldCategoryFactory extends ResetUniqueFactory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var  string
     */
    protected $model = ConsumerConfigurableFieldCategory::class;

    /** @inheritDoc */
    public function getData(bool $reset = false): array
    {
        return [
            ConsumerConfigurableFieldCategory::FIELD_SLUG => $this->faker->unique($reset)->name(),
            ConsumerConfigurableFieldCategory::FIELD_NAME => $this->faker->unique($reset)->name(),
        ];
    }
}
