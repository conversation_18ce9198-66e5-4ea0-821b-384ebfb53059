<?php

namespace Database\Factories\Odin;

use App\Enums\Odin\CompanyChangeLogType;
use App\Models\Odin\CompanyChangeLog;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyChangeLog>
 */
class CompanyChangeLogFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            CompanyChangeLog::FIELD_COMPANY_ID => fake()->randomNumber(),
            CompanyChangeLog::FIELD_LOG        => fake()->randomElement(CompanyChangeLogType::class),
            CompanyChangeLog::FIELD_PAYLOAD    => [],
        ];
    }
}
