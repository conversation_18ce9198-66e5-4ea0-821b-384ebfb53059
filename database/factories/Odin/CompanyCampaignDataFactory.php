<?php

namespace Database\Factories\Odin;

use App\Models\CompanyCampaignData;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyCampaignData>
 */
class CompanyCampaignDataFactory extends Factory
{
    protected $model = CompanyCampaignData::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            CompanyCampaignData::FIELD_CAMPAIGN_ID => $this->faker->randomNumber(),
            CompanyCampaignData::FIELD_PAYLOAD => [],
        ];
    }
}
