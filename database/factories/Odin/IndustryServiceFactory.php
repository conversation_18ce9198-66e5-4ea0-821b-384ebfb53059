<?php

namespace Database\Factories\Odin;

use App\Abstracts\ResetUniqueFactory;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;

class IndustryServiceFactory extends ResetUniqueFactory
{
    protected $model = IndustryService::class;

    /** @inheritDoc */
    public function getData(bool $reset = false): array
    {
        return [
            IndustryService::FIELD_INDUSTRY_ID => Industry::query()->inRandomOrder()->first()?->{Industry::FIELD_ID} ?? Industry::factory(),
            IndustryService::FIELD_NAME => $this->faker->word,
            IndustryService::FIELD_SLUG => $this->faker->slug,
            IndustryService::FIELD_SHOW_ON_WEBSITE => 'show_on_website'
        ];
    }
}
