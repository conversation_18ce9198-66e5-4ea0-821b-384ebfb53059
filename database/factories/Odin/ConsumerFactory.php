<?php

declare(strict_types=1);

namespace Database\Factories\Odin;

use App\Models\Legacy\EloquentQuote;
use App\Models\Odin\Consumer;
use App\Models\Odin\Website;
use Illuminate\Database\Eloquent\Factories\Factory;

class ConsumerFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var  string
     */
    protected $model = Consumer::class;

    /**
     * @inheritDoc
     */
    public function definition()
    {
        return [
            Consumer::FIELD_REFERENCE            => $this->faker->uuid(),
            Consumer::FIELD_LEGACY_ID            => EloquentQuote::factory(),
            Consumer::FIELD_WEBSITE_ID           => Website::factory(),
            Consumer::FIELD_PHONE                => $this->faker->phoneNumber(),
            Consumer::FIELD_EMAIL                => $this->faker->safeEmail(),
            Consumer::FIELD_STATUS               => $this->faker->optional(0.25, Consumer::STATUS_COMPLETED)->numberBetween(Consumer::STATUS_CANCELLED, Consumer::STATUS_COMPLETED),
            Consumer::FIELD_CLASSIFICATION       => $this->faker->optional(0.5, Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_SMS)->numberBetween(Consumer::CLASSIFICATION_EMAIL_ONLY, Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_CALL),
            Consumer::FIELD_FIRST_NAME           => $this->faker->firstName(),
            Consumer::FIELD_LAST_NAME            => $this->faker->lastName(),
            Consumer::FIELD_MAX_CONTACT_REQUESTS => $this->faker->numberBetween(1, 8),
            Consumer::FIELD_STATUS_REASON        => $this->faker->words(3, true) //todo
        ];
    }
}
