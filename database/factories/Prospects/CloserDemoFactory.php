<?php

namespace Database\Factories\Prospects;

use App\Enums\Prospects\CloserDemoResolution;
use App\Enums\Prospects\CloserDemoStatus;
use App\Models\Prospects\NewBuyerProspect;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CloserDemo>
 */
class CloserDemoFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'new_buyer_prospect_id' => NewBuyerProspect::factory(),
            'company_id' => fake()->randomNumber(), // TODO: Fix this due to company factory issues
            'demo_at' => now(),
            'calendly_event_url' => fake()->url(),
            'calendly_payload' => [],
        ];
    }

    /**
     * Indicate that the demo was booked.
     */
    public function booked(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => CloserDemoStatus::BOOKED,
            ];
        });
    }

    /**
     * Indicate that the demo was completed.
     */
    public function cancelled(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => CloserDemoStatus::CANCELLED,
            ];
        });
    }

    /**
     * Indicate that the demo was successful.
     */
    public function successful(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => CloserDemoStatus::COMPLETED,
                'resolution' => CloserDemoResolution::SUCCESSFUL,
            ];
        });
    }

    /**
     * Indicate that the demo was unsuccessful.
     */
    public function unsuccessful(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => CloserDemoStatus::COMPLETED,
                'resolution' => CloserDemoResolution::UNSUCCESSFUL,
            ];
        });
    }
}
