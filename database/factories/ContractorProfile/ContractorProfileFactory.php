<?php

namespace Database\Factories\ContractorProfile;

use App\Models\ContractorProfile\ContractorProfile;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<ContractorProfile>
 */
class ContractorProfileFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            ContractorProfile::FIELD_COMPANY_ID              => fake()->randomNumber(),
            ContractorProfile::FIELD_INTRODUCTION            => fake()->word(),
            ContractorProfile::FIELD_CUSTOMERS_LIKE          => fake()->word(),
            ContractorProfile::FIELD_CUSTOMERS_DISLIKE       => fake()->word(),
            ContractorProfile::FIELD_RATING                  => fake()->randomFloat(),
            ContractorProfile::FIELD_STATUS                  => fake()->boolean(),
            ContractorProfile::FIELD_FORCE_DISPLAY           => fake()->boolean(),
            ContractorProfile::FIELD_BUSINESS_HOURS_TIMEZONE => fake()->timezone(),
            ContractorProfile::FIELD_BUSINESS_HOURS          => [],
            ContractorProfile::FIELD_BRANDS_SOLD             => [],
            ContractorProfile::FIELD_LICENSES                => [],
            ContractorProfile::FIELD_CERTIFICATIONS          => [],
            ContractorProfile::FIELD_SERVICES                => [],
        ];
    }
}
