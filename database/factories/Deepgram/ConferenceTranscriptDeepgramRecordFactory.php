<?php

namespace Database\Factories\Deepgram;

use App\Models\Conference\ConferenceTranscript;
use App\Models\Deepgram\ConferenceTranscriptDeepgramRecord;
use Faker\Generator;
use Illuminate\Database\Eloquent\Factories\Factory;

class ConferenceTranscriptDeepgramRecordFactory extends Factory
{
    protected $model = ConferenceTranscriptDeepgramRecord::class;

    public function definition(): array
    {
        $sentiment = self::getSentiment($this->faker);

        $sentimentScore = self::getSentimentScore($this->faker, $sentiment);

        return [
            'conference_transcript_id' => ConferenceTranscript::factory(),
            'deepgram_request_id' => $this->faker->uuid(),
            'deepgram_requested_at' => now(),
            'deepgram_language' => 'en',
            'deepgram_average_sentiment' => $sentiment,
            'deepgram_average_sentiment_score' => $sentimentScore,
            'deepgram_summary' => $this->faker->paragraphs(10, true),
        ];
    }

    public static function getSentiment(Generator $faker): string
    {
        return $faker->randomElement(['positive', 'negative', 'neutral']);
    }

    public static function getSentimentScore(Generator $faker, string $sentiment): string
    {
        $sentimentScore = $faker->randomFloat(20, 0, 1);

        if ($sentiment === 'negative') {
            $sentimentScore *= -1;
        }

        if ($sentiment === 'neutral') {
            $sentimentScore *= $faker->boolean ? -1 : 1;
        }

        return $sentimentScore;
    }
}
