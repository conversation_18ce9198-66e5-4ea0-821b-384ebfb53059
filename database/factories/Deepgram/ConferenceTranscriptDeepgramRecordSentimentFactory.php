<?php

namespace Database\Factories\Deepgram;

use App\Models\Conference\ConferenceTranscript;
use App\Models\Deepgram\ConferenceTranscriptDeepgramRecord;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Deepgram\ConferenceTranscriptDeepgramRecordSentiment>
 */
class ConferenceTranscriptDeepgramRecordSentimentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $sentiment = ConferenceTranscriptDeepgramRecordFactory::getSentiment($this->faker);

        $sentimentScore = ConferenceTranscriptDeepgramRecordFactory::getSentimentScore($this->faker, $sentiment);

        return [
            'conference_transcript_deepgram_record_id' => ConferenceTranscriptDeepgramRecord::factory(),
            'conference_transcript_id' => ConferenceTranscript::factory(),
            'sentiment' => $sentiment,
            'sentiment_score' => $sentimentScore,
            'text' => $this->faker->text(),
        ];
    }
}
