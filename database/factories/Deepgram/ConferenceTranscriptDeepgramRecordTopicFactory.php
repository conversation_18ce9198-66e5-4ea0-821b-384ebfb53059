<?php

namespace Database\Factories\Deepgram;

use App\Models\Conference\ConferenceTranscript;
use App\Models\Deepgram\ConferenceTranscriptDeepgramRecord;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Deepgram\ConferenceTranscriptDeepgramRecordTopic>
 */
class ConferenceTranscriptDeepgramRecordTopicFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'conference_transcript_deepgram_record_id' => ConferenceTranscriptDeepgramRecord::factory(),
            'conference_transcript_id' => ConferenceTranscript::factory(),
            'topic' => $this->faker->words(asText: true),
            'confidence_score' => $this->faker->randomFloat(10, 0, 1),
            'text' => $this->faker->text(),
        ];
    }
}
