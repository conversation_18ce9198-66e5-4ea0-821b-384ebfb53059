<?php

namespace Database\Factories;

use App\Enums\Advertising\AdvertisingTrackType;
use App\Enums\Odin\OriginDomain;
use App\Models\Legacy\EloquentQuote;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class EloquentQuoteFactory extends Factory
{
    protected $model = EloquentQuote::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            EloquentQuote::ID => $this->faker->numberBetween(1),
            EloquentQuote::TRACK_NAME => $this->faker->randomElement(AdvertisingTrackType::cases())->value,
            EloquentQuote::TRACK_CODE => $this->faker->randomNumber(),
            EloquentQuote::ORIGIN => $this->faker->randomElement(OriginDomain::getAbbreviations()),
        ];
    }
}
