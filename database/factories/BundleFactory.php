<?php

namespace Database\Factories;

use App\Models\Bundle;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Bundle>
 */
class BundleFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            Bundle::FIELD_NAME => $this->faker->text(20),
            Bundle::FIELD_NOTE => $this->faker->text(),
            Bundle::FIELD_TITLE => $this->faker->realText(20),
            Bundle::FIELD_DESCRIPTION => $this->faker->realText(),
            Bundle::FIELD_COST => $this->getRandomCost(),
            Bundle::FIELD_CREDIT => $this->getRandomCredit(),
            Bundle::FIELD_CREATED_BY => User::all()->random()->id,
            Bundle::FIELD_ACTIVATED_AT => $this->faker->dateTimeThisMonth,
        ];
    }

    /**
     * Indicate that the bundle is inactive.
     */
    public function inactive(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'activated_at' => null,
                // 'active' => false,
            ];
        });
    }

    /**
     * Set cost to be lower than the credit's random range
     */
    public function getRandomCost(): float
    {
        return $this->faker->randomFloat(2, 100, 500);
    }

    /**
     * Set credit to be higher than the cost's random range
     */
    public function getRandomCredit(): float
    {
        return $this->faker->randomFloat(2, 600, 1000);
    }
}
