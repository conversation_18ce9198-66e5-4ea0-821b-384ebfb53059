<?php

namespace Database\Factories;

use App\Models\CompanyManagerAssignmentRequest;
use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;
use Spatie\Permission\Models\Role;

class CompanyManagerAssignmentRequestFactory extends Factory
{
    protected $model = CompanyManagerAssignmentRequest::class;

    public function definition(): array
    {
        return [
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
            'is_unread' => $this->faker->boolean(),
            'status' => 'pending',

            'user_id' => User::factory(),
            'company_id' => Company::factory(),
            'role_id' => Role::findOrCreate('account-manager')->id,
        ];
    }

    public function decided(User|null $decidingUser = null, Carbon|null $decidedAt = null, string|null $status = null): Factory
    {
        if (is_null($decidingUser)) {
            $decidingUser = User::factory()->create();
        }

        if (is_null($decidedAt)) {
            $decidedAt = $this->faker->dateTime();
        }

        if (is_null($status)) {
            $status = $this->faker->randomElement(['approved', 'denied']);
        }

        return $this->state(function () use ($decidingUser, $decidedAt, $status) {
            return [
                'status' => $status,
                'deciding_user_id' => $decidingUser,
                'decided_at' => $decidedAt
            ];
        });
    }
}
