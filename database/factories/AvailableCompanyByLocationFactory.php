<?php

namespace Database\Factories;

use App\Models\AvailableCompanyByLocation;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<AvailableCompanyByLocation>
 */
class AvailableCompanyByLocationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            AvailableCompanyByLocation::FIELD_COMPANY_ID         => fake()->randomNumber(),
            AvailableCompanyByLocation::FIELD_LEGACY_COMPANY_ID  => fake()->randomNumber(),
            AvailableCompanyByLocation::FIELD_LOCATION_ID        => fake()->randomNumber(),
            AvailableCompanyByLocation::FIELD_COUNTY_LOCATION_ID => fake()->randomNumber(),
            AvailableCompanyByLocation::FIELD_INDUSTRY_SLUG      => fake()->slug(),
            AvailableCompanyByLocation::FIELD_UNLIMITED_BUDGETS  => fake()->randomNumber(),
        ];
    }


}
