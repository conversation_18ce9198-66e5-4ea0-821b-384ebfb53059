<?php

namespace Database\Factories;

use App\Models\GlobalConfiguration;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class GlobalConfigurationFactory extends Factory
{
    protected $model = GlobalConfiguration::class;

    public function definition(): array
    {
        return [
            'configuration_key'     => $this->faker->randomNumber(),
            'configuration_payload' => $this->faker->words(),
            'created_at'            => Carbon::now(),
            'updated_at'            => Carbon::now(),

            'created_by_id' => User::factory(),
            'updated_by_id' => User::factory(),
        ];
    }
}
