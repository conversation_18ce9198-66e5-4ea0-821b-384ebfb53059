<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\AccountManager;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Spatie\Permission\Models\Role;

/**
 * @extends Factory<\App\Models\AccountManager>
 */
final class AccountManagerFactory extends Factory
{
    /**
    * The name of the factory's corresponding model.
    *
    * @var  string
    */
    protected $model = AccountManager::class;

    /**
    * Define the model's default state.
    *
    * @return  array
    */
    public function definition(): array
    {
        return [
            'user_id' => User::factory()->create([
                    User::FIELD_LEGACY_USER_ID  => 1,
                ])->assignRole(Role::all()->random()),
            'type' => $this->faker->numberBetween(0, 1),
            'include_in_sales_round_robin' => $this->faker->boolean,
        ];
    }
}
