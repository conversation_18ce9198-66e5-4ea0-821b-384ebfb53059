<?php

namespace Database\Factories;

use App\Models\Odin\CompanyExternalReview;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyExternalReview>
 */
class CompanyExternalReviewFactory extends Factory
{
    protected $model = CompanyExternalReview::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            //
        ];
    }
}
