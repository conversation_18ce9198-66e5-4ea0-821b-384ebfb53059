<?php

namespace Database\Factories;

use App\Models\EmailTemplateBackground;
use App\Models\Odin\Industry;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class EmailTemplateBackgroundFactory extends Factory
{
    protected $model = EmailTemplateBackground::class;

    public function definition(): array
    {
        return [
            'name'       => $this->faker->name(),
            'header'     => $this->faker->word(),
            'footer'     => $this->faker->word(),
            'personal'   => $this->faker->boolean(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),

            'owner_user_id' => User::factory(),
            'industry_id'   => Industry::factory(),
        ];
    }
}
