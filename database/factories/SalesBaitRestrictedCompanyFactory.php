<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Legacy\EloquentCompany;
use App\Models\SalesBaitRestrictedCompany;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<\App\Models\SalesBaitRestrictedCompany>
 */
final class SalesBaitRestrictedCompanyFactory extends Factory
{
    /**
    * The name of the factory's corresponding model.
    *
    * @var  string
    */
    protected $model = SalesBaitRestrictedCompany::class;

    /**
    * Define the model's default state.
    *
    * @return  array
    */
    public function definition(): array
    {
        return [
            'company_id' => EloquentCompany::all()->random(),
        ];
    }
}
