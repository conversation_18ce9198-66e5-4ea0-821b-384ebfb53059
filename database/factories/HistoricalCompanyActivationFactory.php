<?php

namespace Database\Factories;

use App\Models\HistoricalCompanyActivation;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<HistoricalCompanyActivation>
 */
class HistoricalCompanyActivationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            HistoricalCompanyActivation::FIELD_COMPANY_ID  => fake()->randomNumber(),
            HistoricalCompanyActivation::FIELD_STATUS_DATE => fake()->date(),
            HistoricalCompanyActivation::FIELD_TYPE        => fake()->randomElement([
                HistoricalCompanyActivation::REACTIVATION,
                HistoricalCompanyActivation::DEACTIVATION,
                HistoricalCompanyActivation::ACTIVATION
            ]),
        ];
    }
}
