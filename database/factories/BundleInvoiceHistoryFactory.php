<?php

namespace Database\Factories;

use App\Models\BundleInvoiceHistory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<BundleInvoiceHistory>
 */
class BundleInvoiceHistoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            BundleInvoiceHistory::FIELD_DESCRIPTION => $this->faker->realText(40),
            BundleInvoiceHistory::FIELD_NOTE => $this->faker->realText(20),
        ];
    }
}
