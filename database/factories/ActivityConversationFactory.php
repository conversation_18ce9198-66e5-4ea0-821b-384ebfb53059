<?php

namespace Database\Factories;

use App\Models\ActivityConversation;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<ActivityConversation>
 */
class ActivityConversationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            ActivityConversation::FIELD_ACTIVITY_ID  => $this->faker->numberBetween(1, 19),
            ActivityConversation::FIELD_PARENT_ID    => $this->faker->numberBetween(1, 19),
            ActivityConversation::FIELD_USER_ID      => $this->faker->numberBetween(1, 19),
            ActivityConversation::FIELD_COMMENT      => $this->faker->realText(64),
        ];
    }
}
