<?php

namespace Database\Factories;

use App\Models\LeadProcessingTeam;
use App\Models\Odin\Industry;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\LeadProcessingTeam>
 */
class LeadProcessingTeamFactory extends Factory
{
    protected $model = LeadProcessingTeam::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $industry = Industry::factory()->raw();

        $update = [Industry::FIELD_SLUG => $industry[Industry::FIELD_SLUG]];

        $create = [Industry::FIELD_NAME => $industry[Industry::FIELD_NAME], Industry::FIELD_COLOR_DARK => $industry[Industry::FIELD_COLOR_DARK], Industry::FIELD_COLOR_LIGHT => $industry[Industry::FIELD_COLOR_LIGHT]];

        return [
            LeadProcessingTeam::FIELD_NAME => $this->faker->name(),
            LeadProcessingTeam::FIELD_PRIMARY_QUEUE_CONFIGURATION_ID => $this->faker->randomNumber(),
            LeadProcessingTeam::FIELD_PRIMARY_UTC_OFFSET => -5,
            LeadProcessingTeam::FIELD_INDUSTRY_ID => Industry::query()->updateOrCreate($update, $create),
            LeadProcessingTeam::FIELD_INDUSTRY => function (array $attributes) {
                return Industry::find($attributes[LeadProcessingTeam::FIELD_INDUSTRY_ID])->name;
            },
        ];
    }
}
