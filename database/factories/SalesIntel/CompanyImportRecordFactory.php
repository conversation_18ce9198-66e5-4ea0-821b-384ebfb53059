<?php

namespace Database\Factories\SalesIntel;

use App\Models\Prospects\NewBuyerProspect;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SalesIntel\CompanyImportRecord>
 */
class CompanyImportRecordFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'page' => 1, // assume page 1
        ];
    }

    public function forProspect(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'importable_id' => NewBuyerProspect::factory(),
                'importable_type' => function (array $attributes) {
                    return NewBuyerProspect::find($attributes['importable_id'])->getMorphClass();
                },
            ];
        });
    }

    public function zipcodeFilter(string $zipcode): Factory
    {
        return $this->state(fn (array $attributes) => ['filter' => 'zipcode', 'value' => $zipcode]);
    }

    public function stateFilter(string $state): Factory
    {
        return $this->state(fn (array $attributes) => ['filter' => 'state', 'value' => $state]);
    }

    public function domainFilter(string $domain): Factory
    {
        return $this->state(fn (array $attributes) => ['filter' => 'domain', 'value' => $domain]);
    }
}
