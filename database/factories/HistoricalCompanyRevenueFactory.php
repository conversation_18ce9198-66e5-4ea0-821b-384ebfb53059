<?php

namespace Database\Factories;

use App\DataModels\HistoricalCompanyDailyRevenueDataModel;
use App\DataModels\HistoricalCompanyMonthlyRevenueDataModel;
use App\Models\HistoricalCompanyRevenue;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<HistoricalCompanyRevenue>
 */
class HistoricalCompanyRevenueFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            HistoricalCompanyRevenue::FIELD_COMPANY_ID      => fake()->randomNumber(),
            HistoricalCompanyRevenue::FIELD_YEAR            => fake()->year(),
            HistoricalCompanyRevenue::FIELD_DAILY_REVENUE   => new HistoricalCompanyDailyRevenueDataModel(),
            HistoricalCompanyRevenue::FIELD_MONTHLY_REVENUE => new HistoricalCompanyMonthlyRevenueDataModel(),
            HistoricalCompanyRevenue::FIELD_YEARLY_REVENUE  => fake()->randomFloat(),
        ];
    }
}
