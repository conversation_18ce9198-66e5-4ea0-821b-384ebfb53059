<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Enums\EventCategory;
use App\Models\WorkflowEvent;
use App\Repositories\PubSubEventRepository;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<\App\Models\WorkflowEvent>
 */
final class WorkflowEventFactory extends Factory
{
    /**
    * The name of the factory's corresponding model.
    *
    * @var  string
    */
    protected $model = WorkflowEvent::class;

    /**
     * Define the model's default state.
     *
     * @return  array
     * @throws BindingResolutionException
     */
    public function definition(): array
    {
        $category = $this->faker->randomElement([EventCategory::CAMPAIGNS->value, EventCategory::COMPANIES->value, EventCategory::LEADS->value]);

        return [
            'event_category' => $category,
            'event_name' => $this->faker->randomElement(
                app()->make(PubSubEventRepository::class)->getAllEvents()[$category]
            ),
        ];
    }
}
