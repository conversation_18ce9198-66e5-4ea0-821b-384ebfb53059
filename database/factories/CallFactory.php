<?php

namespace Database\Factories;

use App\Models\Call;
use App\Models\CallRecording;
use App\Models\Phone;
use Illuminate\Database\Eloquent\Factories\Factory;
use Ramsey\Uuid\Uuid;

/**
 * @extends Factory<Call>
 */
final class CallFactory extends Factory
{
    /**
    * The name of the factory's corresponding model.
    *
    * @var  string
    */
    protected $model = Call::class;

    /**
    * Define the model's default state.
    *
    * @return  array
    */
    public function definition(): array
    {
        $callResults = [ Call::RESULT_INITIAL, Call::RESULT_BUSY, Call::RESULT_ANSWERED, Call::RESULT_VOICEMAIL, Call::RESULT_MISSED ];
        return [
            Call::FIELD_EXTERNAL_REFERENCE => Uuid::uuid4(),
            Call::FIELD_EXTERNAL_TYPE      => Call::EXTERNAL_TYPE_TWILIO,
            Call::FIELD_PHONE_ID           => Phone::factory(),
            Call::FIELD_OTHER_NUMBER       => $this->faker->phoneNumber(),
            Call::FIELD_DIRECTION          => $this->faker->randomElement([Call::DIRECTION_INBOUND, Call::DIRECTION_OUTBOUND]),
            Call::FIELD_RESULT             => $this->faker->randomElement($callResults),
            Call::FIELD_CALL_START         => $this->faker->dateTime(),
            Call::FIELD_CALL_END           => $this->faker->dateTime(),
            Call::FIELD_RELATION_TYPE      => $this->faker->randomElement([ 'company', 'lead' ]),
            Call::FIELD_RELATION_ID        => $this->faker->numberBetween(1, 19),
        ];
    }

    /**
     * Attach a fake recording to any answered calls
     * @return Factory
     */
    public function configure(): Factory
    {
        return $this->afterCreating(function(Call $call) {
            if ($call->{Call::FIELD_RESULT} === Call::RESULT_ANSWERED) {
                CallRecording::factory()
                    ->for($call, 'call')
                    ->create();
            }
        });
    }
}
