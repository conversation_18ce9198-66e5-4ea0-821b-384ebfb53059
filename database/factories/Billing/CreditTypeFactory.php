<?php

namespace Database\Factories\Billing;

use App\Abstracts\ResetUniqueFactory;
use App\Models\Billing\Credit;
use App\Models\Billing\CreditType;

class CreditTypeFactory extends ResetUniqueFactory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function getData(bool $reset = false): array
    {
        return [
            CreditType::FIELD_NAME              => $this->faker->word,
            CreditType::FIELD_SLUG              => $this->faker->slug,
            CreditType::FIELD_DESCRIPTION       => $this->faker->paragraph,
            CreditType::FIELD_LINE_ITEM_TEXT    => $this->faker->word,
            CreditType::FIELD_EXPIRES_IN_DAYS   => $this->faker->numberBetween(7, 30),
            CreditType::FIELD_CONSUMPTION_ORDER => $this->faker->numberBetween(1, 100),
            CreditType::FIELD_CASH              => $this->faker->boolean,
            CreditType::FIELD_ACTIVE            => $this->faker->boolean,
            CreditType::FIELD_INVOICEABLE       => $this->faker->boolean,
        ];
    }
}
