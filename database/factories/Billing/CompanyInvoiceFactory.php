<?php

namespace Database\Factories\Billing;

use App\Abstracts\ResetUniqueFactory;
use App\Models\Billing\CompanyInvoice;

class CompanyInvoiceFactory extends ResetUniqueFactory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function getData(bool $reset = false): array
    {
        return [
            CompanyInvoice::FIELD_UUID           => $this->faker->uuid,
            CompanyInvoice::FIELD_TOTAL          => $this->faker->randomFloat(2, 100, 10000),
            CompanyInvoice::FIELD_COMPANY_ID     => $this->faker->randomDigitNot(0),
            CompanyInvoice::FIELD_INVOICE_ID     => $this->faker->numberBetween(0, 1000),
            CompanyInvoice::FIELD_STATUS         => 0, // TODO - Random from enum
            CompanyInvoice::FIELD_GENERATED_AT   => $this->faker->dateTimeThisYear(),
            CompanyInvoice::FIELD_DUE_AT         => now()->addDays($this->faker->numberBetween(7, 30)),
            CompanyInvoice::FIELD_PAYMENT_METHOD => '', // TODO - Enum
            CompanyInvoice::FIELD_INVOICE_URL    => '', // TODO
        ];
    }
}
