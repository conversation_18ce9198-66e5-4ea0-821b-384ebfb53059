<?php

namespace Database\Factories\Billing;

use App\Abstracts\ResetUniqueFactory;
use App\Enums\Billing\InvoiceStates;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\Invoice;
use App\Models\Odin\Company;
use App\Models\User;
use App\States\InvoiceState;

class InvoiceFactory extends ResetUniqueFactory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function getData(bool $reset = false): array
    {
        return [
            Invoice::FIELD_UUID          => $this->faker->uuid(),
            Invoice::FIELD_COMPANY_ID    => Company::factory(),
            Invoice::FIELD_INVOICE_URL   => $this->faker->url(),
            Invoice::FIELD_NOTES         => $this->faker->paragraph(),
            Invoice::FIELD_CREATED_BY_ID => User::factory(),
            Invoice::FIELD_STATUS        => $this->faker->randomElement(InvoiceStates::class)->value,
            Invoice::FIELD_BILLING_PROFILE_ID => BillingProfile::factory(),
        ];
    }
}
