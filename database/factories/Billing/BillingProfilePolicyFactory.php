<?php

namespace Database\Factories\Billing;

use App\Abstracts\ResetUniqueFactory;
use App\Enums\Billing\BillingPolicyActionType;
use App\Enums\Billing\BillingPolicyEventType;
use App\Models\Billing\BillingProfilePolicy;

class BillingProfilePolicyFactory extends ResetUniqueFactory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function getData(bool $reset = false): array
    {
        /** @var BillingPolicyEventType $event */
        $event = $this->faker->randomElement(BillingPolicyEventType::cases());
        $action = $this->faker->randomElement($event->getClass()::getPossibleActions());

        return [
            BillingProfilePolicy::FIELD_EVENT_CLASS        => $event->getClass(),
            BillingProfilePolicy::FIELD_ACTION_CLASS       => $action->getClass(),
            BillingProfilePolicy::FIELD_SORT_ORDER         => $this->faker->numberBetween(1, 100),
        ];
    }

    /**
     * @return BillingProfilePolicyFactory
     */
    public function withTypesInsteadOfClasses(): BillingProfilePolicyFactory
    {
        return $this->state(function ($attr) {
            return array_merge($attr, [
                BillingProfilePolicy::FIELD_EVENT_CLASS  => BillingPolicyEventType::fromClass($attr[BillingProfilePolicy::FIELD_EVENT_CLASS])->value,
                BillingProfilePolicy::FIELD_ACTION_CLASS => BillingPolicyActionType::fromClass($attr[BillingProfilePolicy::FIELD_ACTION_CLASS])->value,
            ]);
        });
    }
}
