<?php

namespace Database\Factories\Billing;

use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceSnapshot;
use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<InvoiceSnapshot>
 */
class InvoiceSnapshotFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'invoice_id'                      => Invoice::factory(),
            'company_id'                      => Company::factory(),
            'account_manager_id'              => null,
            'success_manager_id'              => null,
            'business_development_manager_id' => null,
            'status'                          => $this->faker->word,
        ];
    }

    public function accountManager(): Factory|InvoiceSnapshotFactory
    {
        return $this->state(function (array $attributes) {
            return [
                InvoiceSnapshot::FIELD_ACCOUNT_MANAGER_ID => User::factory(),
            ];
        });
    }

    public function successManager(): Factory|InvoiceSnapshotFactory
    {
        return $this->state(function (array $attributes) {
            return [
                InvoiceSnapshot::FIELD_SUCCESS_MANAGER_ID => User::factory(),
            ];
        });
    }

    public function businessDevelopmentManager(): Factory|InvoiceSnapshotFactory
    {
        return $this->state(function (array $attributes) {
            return [
                InvoiceSnapshot::FIELD_BUSINESS_DEVELOPMENT_MANAGER_ID => User::factory(),
            ];
        });
    }
}
