<?php

namespace Database\Factories\Billing;

use App\Abstracts\ResetUniqueFactory;
use App\Enums\Billing\InvoiceTransactionScenario;
use App\Enums\Billing\InvoiceTransactionType;
use App\Enums\Billing\PaymentMethodServices;
use App\Models\Billing\InvoiceTransaction;

class InvoiceTransactionFactory extends ResetUniqueFactory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function getData(bool $reset = false): array
    {
        $transactionType = $this->faker->randomElement(InvoiceTransactionType::cases());
        $scenario = $this->faker->randomElement(InvoiceTransactionScenario::cases());

        return [
            InvoiceTransaction::FIELD_INVOICE_UUID       => $this->faker->uuid(),
            InvoiceTransaction::FIELD_EXTERNAL_REFERENCE => $this->faker->uuid(),
            InvoiceTransaction::FIELD_AMOUNT             => $this->faker->randomFloat(2, 10, 10000),
            InvoiceTransaction::FIELD_CURRENCY           => 'USD',
            InvoiceTransaction::FIELD_TYPE               => $transactionType,
            InvoiceTransaction::FIELD_ORIGIN             => PaymentMethodServices::STRIPE->value,
            InvoiceTransaction::FIELD_PAYLOAD            => [],
            InvoiceTransaction::FIELD_SCENARIO           => $scenario
        ];
    }
}
