<?php

namespace Database\Factories\Billing;

use App\Abstracts\ResetUniqueFactory;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceItem;
use App\States\InvoiceState;

class InvoiceItemFactory extends ResetUniqueFactory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function getData(bool $reset = false): array
    {
        return [
            InvoiceItem::FIELD_INVOICE_ID    => $this->faker->numberBetween(1, 100),
            InvoiceItem::FIELD_DESCRIPTION   => $this->faker->paragraph,
            InvoiceItem::FIELD_BILLABLE_ID   => $this->faker->numberBetween(1, 100),
            InvoiceItem::FIELD_BILLABLE_TYPE => $this->faker->slug,
            InvoiceItem::FIELD_UNIT_PRICE    => $this->faker->numberBetween(10, 200),
            InvoiceItem::FIELD_QUANTITY      => $this->faker->numberBetween(1, 3),
//            InvoiceItem::FIELD_BILLABLE_ITEM => $this->faker->slug,
        ];
    }
}
