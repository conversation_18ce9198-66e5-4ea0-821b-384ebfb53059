<?php

namespace Database\Factories\Billing;

use App\Abstracts\ResetUniqueFactory;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceTemplate;
use App\Models\User;
use App\States\InvoiceState;

class InvoiceTemplateFactory extends ResetUniqueFactory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function getData(bool $reset = false): array
    {
        return [
            InvoiceTemplate::FIELD_NAME          => $this->faker->word,
            InvoiceTemplate::FIELD_MODEL_TYPE    => $this->faker->word,
            InvoiceTemplate::FIELD_MODEL_ID      => $this->faker->numberBetween(1, 100),
            InvoiceTemplate::FIELD_PROPS         => [],
            InvoiceTemplate::FIELD_CREATED_BY_ID => User::factory(),
        ];
    }
}
