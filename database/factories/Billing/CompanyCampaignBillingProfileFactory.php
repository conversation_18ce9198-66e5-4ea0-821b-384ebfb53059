<?php

namespace Database\Factories\Billing;

use App\Abstracts\ResetUniqueFactory;
use App\Models\Billing\CompanyCampaignBillingProfile;

class CompanyCampaignBillingProfileFactory extends ResetUniqueFactory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function getData(bool $reset = false): array
    {
        return [
            CompanyCampaignBillingProfile::FIELD_BILLING_PROFILE_ID  => $this->faker->randomNumber(1, 1000),
            CompanyCampaignBillingProfile::FIELD_COMPANY_CAMPAIGN_ID => $this->faker->randomNumber(1, 1000),
        ];
    }
}
