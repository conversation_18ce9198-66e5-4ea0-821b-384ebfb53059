<?php

namespace Database\Factories\Billing;

use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Enums\Billing\PaymentMethodServices;
use App\Models\Billing\CompanyPaymentMethod;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyPaymentMethod>
 */
class CompanyPaymentMethodFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            CompanyPaymentMethod::FIELD_PAYMENT_GATEWAY_CLIENT_CODE         => fake()->randomNumber(),
            CompanyPaymentMethod::FIELD_PAYMENT_GATEWAY_PAYMENT_METHOD_CODE => fake()->randomNumber(),
            CompanyPaymentMethod::FIELD_TYPE                                => fake()->randomElement(PaymentMethodServices::class)->value,
            CompanyPaymentMethod::FIELD_COMPANY_ID                          => fake()->randomNumber(),
            CompanyPaymentMethod::FIELD_IS_DEFAULT                          => fake()->boolean(),
            CompanyPaymentMethod::FIELD_ADDED_BY_ID                         => fake()->randomNumber(),
            CompanyPaymentMethod::FIELD_ADDED_BY_TYPE                       => fake()->randomElement(InvoiceEventAuthorTypes::class)->value,
            CompanyPaymentMethod::FIELD_EXPIRY_MONTH                        => fake()->month(),
            CompanyPaymentMethod::FIELD_EXPIRY_YEAR                         => fake()->year(),
            CompanyPaymentMethod::FIELD_NUMBER                              => fake()->creditCardNumber(),
        ];
    }
}
