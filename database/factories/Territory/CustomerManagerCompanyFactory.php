<?php

namespace Database\Factories\Territory;

use App\Models\Odin\Company;
use App\Models\Territory\CustomerManagerCompany;
use App\Models\Territory\CustomerSuccessManager;
use Database\Factories\BaseFactory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CustomerManagerCompany>
 */
class CustomerManagerCompanyFactory extends BaseFactory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            CustomerManagerCompany::FIELD_CUSTOMER_SUCCESS_MANAGER_ID => $this->getRandomId(CustomerSuccessManager::class),
            CustomerManagerCompany::FIELD_COMPANY_ID                  => $this->getRandomId(Company::class),
            CustomerManagerCompany::FIELD_ACTIVE_FROM                 => $this->faker->dateTimeBetween('-2 years'),
        ];
    }
}
