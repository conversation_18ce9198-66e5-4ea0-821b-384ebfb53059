<?php

namespace Database\Factories\Territory;

use App\Models\Territory\RelationshipManager;
use App\Models\User;
use Database\Factories\BaseFactory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<RelationshipManager>
 */
class RelationshipManagerFactory extends BaseFactory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            RelationshipManager::FIELD_USER_ID     => $this->getRandomId(User::class),
            RelationshipManager::FIELD_ACTIVE_FROM => $this->faker->dateTimeBetween('-2 years'),
            RelationshipManager::FIELD_ACTIVE_TO   => null,
        ];
    }
}
