<?php

namespace Database\Factories\Territory;

use App\Models\Territory\CustomerSuccessManager;
use App\Models\User;
use Database\Factories\BaseFactory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CustomerSuccessManager>
 */
class CustomerSuccessManagerFactory extends BaseFactory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        /** @var User $user */
        $user = $this->getRandomModel(User::class);

        return [
            CustomerSuccessManager::FIELD_NAME          => $user?->name ?? $this->faker->name,
            CustomerSuccessManager::FIELD_USER_ID       => $user?->id ?? $this->faker->numberBetween(1, 100),
            CustomerSuccessManager::FIELD_CREATED_BY_ID => $this->getRandomId(User::class),
            CustomerSuccessManager::FIELD_ACTIVE_FROM   => $this->faker->dateTimeBetween('-2 years'),
        ];
    }
}
