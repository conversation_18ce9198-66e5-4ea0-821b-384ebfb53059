<?php

declare(strict_types=1);

namespace Database\Factories\Sales;

use App\DataModels\Workflows\TaskResultDataModel;
use App\Enums\DynamicPriorityTypes;
use App\Enums\TaskResultType;
use App\Models\Odin\Company;
use App\Models\Sales\Task;
use App\Models\TaskCategory;
use App\Models\TaskType;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<\App\Models\Sales\Task>
 */
final class TaskFactory extends Factory
{
    /**
    * The name of the factory's corresponding model.
    *
    * @var  string
    */
    protected $model = Task::class;

    /**
    * Define the model's default state.
    *
    * @return  array
    */
    public function definition(): array
    {
        $userId = User::query()->where(User::FIELD_EMAIL, '<EMAIL>')->first()?->id;

        return [
            'assigned_user_id' => $userId ?? User::all()->random(),
            'available_at' => $this->faker->randomElement([now()->addHours(5), now()->subHours(5)]),
            'subject' => $this->faker->text(20),
            'results' => collect([
                new TaskResultDataModel(0, 'Acknowledge - No Further Action', TaskResultType::VOID, []),
                new TaskResultDataModel(0, 'Add CRM Entry', TaskResultType::CRM_ENTRY, []),
                new TaskResultDataModel(0, 'Add Task Note', TaskResultType::TEXT, [
                    'text' => 'Add Task Note'
                ])
            ]),
            'running_workflow_id' => null,
            'priority' => $this->faker->numberBetween(1, 100),
            'completed' => false,
            'task_type_id' => TaskType::factory(),
            'allows_rescheduling' => $this->faker->boolean,
            'reschedule_count' => $this->faker->numberBetween(1, 10),
            'payload' => null,
            'manual' => true,
            'completed_at' => null,
            'task_category_id' => TaskCategory::factory(),
            'uses_odin_id' => $this->faker->boolean,
            'dynamic_priority' => $this->faker->boolean,
            'dynamic_priority_type' => $this->faker->randomElement(array_keys(DynamicPriorityTypes::getMapping())),
        ];
    }
}
