<?php

namespace Database\Factories;

use App\Models\AppointmentProcessingAllocation;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AppointmentProcessingAllocation>
 */
class AppointmentProcessingAllocationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            AppointmentProcessingAllocation::FIELD_LEAD_CONSUMER_PRODUCT_ID => 0,
            AppointmentProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID => 0,
            AppointmentProcessingAllocation::FIELD_LEAD_PROCESSOR_ID => 0,
            AppointmentProcessingAllocation::FIELD_PROCESSING_SCENARIO => '',
            AppointmentProcessingAllocation::FIELD_ALLOCATED => 0,
            AppointmentProcessingAllocation::FIELD_DELIVERED => 0,
            AppointmentProcessingAllocation::FIELD_ALLOCATE_AT => Carbon::now('UTC'),
            AppointmentProcessingAllocation::FIELD_FAILED_APPT_ALLOCATION => 0,
            AppointmentProcessingAllocation::FIELD_CANCELLED_REJECTED => 0,
            AppointmentProcessingAllocation::FIELD_ERROR => '',
            AppointmentProcessingAllocation::FIELD_ALLOCATED_AS_LEAD => 0
        ];
    }
}
