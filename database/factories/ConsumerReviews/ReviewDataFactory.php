<?php

namespace Database\Factories\ConsumerReviews;

use App\Models\ConsumerReviews\ReviewData;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Model;

/**
 * @extends Factory<Model>
 */
class ReviewDataFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            ReviewData::FIELD_OVERALL_SCORE => $this->faker->numberBetween(1, 5),
            ReviewData::FIELD_TITLE         => $this->faker->sentence(),
            ReviewData::FIELD_COMMENTS      => $this->faker->paragraph(),
            ReviewData::FIELD_DATA          => $this->makeDataPayload(),
        ];
    }

    protected function makeDataPayload(): array
    {
        return [
            ReviewData::DATA_KEY_ZIP_CODE         => strval($this->faker->randomNumber(5, true)),
            ReviewData::DATA_KEY_USER_IP          => $this->faker->ipv4(),
            ReviewData::DATA_KEY_DISPLAY_LOCATION => $this->faker->city() . ", " . $this->faker->stateAbbr(),
            ReviewData::DATA_KEY_CUSTOM           => [
                "system_size"                    => [
                    "type"  => "float",
                    "value" => $this->faker->numberBetween(10, 200),
                ],
                "system_price"                   => [
                    "type"  => "integer",
                    "value" => $this->faker->numberBetween(1000, 10000),
                ],
                "installed_year"                 => [
                    "type"  => "integer",
                    "value" => $this->faker->numberBetween(2017, 2025),
                ],
                "panel_brand_id"                 => [
                    "type"  => "company_id",
                    "value" => $this->faker->numberBetween(1, 10000),
                ],
                "inverter_brand_id"              => [
                    "type"  => "company_id",
                    "value" => $this->faker->numberBetween(1, 10000),
                ],
                "rating_on_schedule"             => [
                    "type"  => "rating",
                    "value" => $this->faker->numberBetween(1, 5),
                ],
                "rating_price_charged"           => [
                    "type"  => "rating",
                    "value" => $this->faker->numberBetween(1, 5),
                ],
                "rating_sales_process"           => [
                    "type"  => "rating",
                    "value" => $this->faker->numberBetween(1, 5),
                ],
                "price_after_incentives"         => [
                    "type"  => "boolean",
                    "value" => $this->faker->boolean(),
                ],
                "rating_after_sales_support"     => [
                    "type"  => "rating",
                    "value" => $this->faker->numberBetween(1, 5),
                ],
                "rating_quality_of_installation" => [
                    "type"  => "rating",
                    "value" => $this->faker->numberBetween(1, 5),
                ],
            ],
        ];
    }
}
