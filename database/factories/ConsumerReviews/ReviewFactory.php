<?php

namespace Database\Factories\ConsumerReviews;

use App\Models\ConsumerReviews\Review;
use App\Models\ConsumerReviews\ReviewData;
use App\Models\ConsumerReviews\Reviewer;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Model;

/**
 * @extends Factory<Model>
 */
class ReviewFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $status = $this->faker->randomElement(Review::STATUSES);
        $approved = $status === Review::STATUS_APPROVED;

        return [
            Review::FIELD_UUID                => $this->faker->uuid(),
            Review::FIELD_INDUSTRY_ID         => 1,
            Review::FIELD_INDUSTRY_SERVICE_ID => 1,
            Review::FIELD_WEBSITE_ID          => 1,
            Review::FIELD_STATUS              => $status,
            Review::FIELD_IS_VERIFIED         => $approved ?: $this->faker->boolean(),
            Review::FIELD_APPROVED_AT         => $this->faker->dateTime(),
            Review::FIELD_APPROVER_USER_ID    => 1,
            Review::FIELD_REVIEW_DATA_ID      => ReviewData::factory()->create()?->id ?? 0,
            Review::FIELD_REVIEWER_ID         => Reviewer::factory()->create()?->id ?? 0,
        ];
    }
}
