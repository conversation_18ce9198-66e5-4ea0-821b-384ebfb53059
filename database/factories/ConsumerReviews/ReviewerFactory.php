<?php

namespace Database\Factories\ConsumerReviews;

use App\Models\ConsumerReviews\Reviewer;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Model;

/**
 * @extends Factory<Model>
 */
class ReviewerFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            Reviewer::FIELD_REFERENCE         => $this->faker->uuid(),
            Reviewer::FIELD_NAME              => $this->faker->name(),
            Reviewer::FIELD_EMAIL             => $this->faker->unique()->safeEmail(),
            Reviewer::FIELD_PHONE             => null,
            Reviewer::FIELD_ADDRESS_ID        => null,
            Reviewer::FIELD_IS_EMAIL_VERIFIED => true,
            Reviewer::FIELD_IS_PHONE_VERIFIED => false,
        ];
    }
}
