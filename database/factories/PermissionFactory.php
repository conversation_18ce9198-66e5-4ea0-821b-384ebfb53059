<?php

namespace Database\Factories;

use App\Enums\PermissionType;
use App\Models\Permission;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Permission>
 */
class PermissionFactory extends Factory
{
    protected $model = Permission::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            Permission::FIELD_NAME => $this->faker->randomElement(PermissionType::cases())->value,
            Permission::FIELD_GUARD_NAME => 'web'
        ];
    }
}
