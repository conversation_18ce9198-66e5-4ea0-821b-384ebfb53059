<?php

namespace Database\Factories;

use App\Models\Advertiser;
use Illuminate\Database\Eloquent\Factories\Factory;
use App\Enums\Advertising\Advertiser as AdvertiserEnum;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Advertiser>
 */
class AdvertiserFactory extends Factory
{
    protected $model = Advertiser::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $advertiser = $this->faker->randomElement(AdvertiserEnum::cases());

        return [
            Advertiser::FIELD_NAME => $advertiser->getDisplayName(),
            Advertiser::FIELD_KEY => $advertiser->value
        ];
    }
}
