<?php

namespace Database\Factories\Campaigns;

use App\Abstracts\ResetUniqueFactory;
use App\Enums\Campaigns\CampaignStatus;
use App\Enums\Campaigns\CampaignType;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModule;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\Company;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAssignment;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class CompanyCampaignFactory extends ResetUniqueFactory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function getData(bool $reset = false): array
    {
        return [
            CompanyCampaign::FIELD_COMPANY_ID => Company::factory(),
            CompanyCampaign::FIELD_PRODUCT_ID => Product::factory(),
            CompanyCampaign::FIELD_SERVICE_ID => IndustryService::factory(),
            CompanyCampaign::FIELD_TYPE => $this->faker->randomElement(CampaignType::cases())->value,
            CompanyCampaign::FIELD_STATUS => $this->faker->randomElement(CampaignStatus::cases())->value,
            CompanyCampaign::FIELD_NAME => $this->faker->word,
            CompanyCampaign::FIELD_REFERENCE => $this->faker->uuid,
            CompanyCampaign::FIELD_MAXIMUM_BUDGET_USAGE => $this->faker->numberBetween(100, 1000),
        ];
    }

    public function location(array $locationData = []): Factory
    {
        return $this->state(function (array $attributes) {
            return [];
        })->afterCreating(function (CompanyCampaign $companyCampaign) use ($locationData) {
            $companyCampaignLocationModule = CompanyCampaignLocationModule::create([
                'company_campaign_id' => $companyCampaign->id,
            ]);

            $locationTypes = [
                Location::TYPE_ZIP_CODE,
                Location::TYPE_COUNTY,
                Location::TYPE_STATE,
            ];

            $locationData['county_key'] = $locationData['county_key'] ?? 'Monmouth';
            $locationData['state_key'] = $locationData['state_key'] ?? 'NJ';
            $locationData['zip_code'] = $locationData['zip_code'] ?? '07701';
            $locationData['county'] = $locationData['county'] ?? 'Monmouth';
            $locationData['state'] = $locationData['state'] ?? 'NJ';
            $locationData['city'] = $locationData['city'] ?? 'Red Bank';

            foreach ($locationTypes as $locationType) {
                $location = Location::factory()->create([
                    'type' => $locationType,
                    ...$locationData,
                ]);

                CompanyCampaignLocationModuleLocation::create([
                    'module_id' => $companyCampaignLocationModule->id,
                    'location_id' => $location->id,
                ]);
            }

            BudgetContainer::create([
                'company_campaign_id' => $companyCampaign->id,
            ]);
        });
    }

    public function dateOfLastLeadPurchased(int $countOfProductAssignments = 1, Carbon $dateOfLatest = null): Factory
    {
        return $this->state(function (array $attributes) {
            return [];
        })->afterCreating(function (CompanyCampaign $companyCampaign) use ($countOfProductAssignments, $dateOfLatest) {
            if ($dateOfLatest === null) {
                $dateOfLatest = now();
            }

            $dateOfLatest->subDays($countOfProductAssignments);

            for ($count = 1; $count <= $countOfProductAssignments; $count++) {
                ProductAssignment::factory()->createQuietly([
                    'company_id' => $companyCampaign->company_id,
                    'created_at' => $dateOfLatest
                ]);

                $dateOfLatest->addDays($count);
            }
        });
    }
}
