<?php

namespace Database\Factories\Campaigns\Modules\Delivery;

use App\Campaigns\Delivery\Contacts\Enums\ContactDeliveryLogType;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ConsumerProduct;
use Illuminate\Database\Eloquent\Factories\Factory;

class CompanyCampaignContactDeliveryLogFactory extends Factory
{
    public function definition(): array
    {
        return [
            'campaign_id' => CompanyCampaign::factory(),
            'consumer_product_id' => ConsumerProduct::factory(),
            'type' => ContactDeliveryLogType::EMAIL,
            'success' => $this->faker->boolean(),
            'payload' => []
        ];
    }
}
