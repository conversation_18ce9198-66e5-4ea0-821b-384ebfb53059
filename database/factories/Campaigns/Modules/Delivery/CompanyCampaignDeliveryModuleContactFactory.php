<?php

namespace Database\Factories\Campaigns\Modules\Delivery;

use App\Campaigns\Delivery\Contacts\Enums\ContactDeliveryLogType;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModule;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleContact;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\ConsumerProduct;
use Illuminate\Database\Eloquent\Factories\Factory;

class CompanyCampaignDeliveryModuleContactFactory extends Factory
{
    public function definition(): array
    {
        return [
            'module_id' =>  CompanyCampaignDeliveryModule::factory(),
            'contact_id' => CompanyUser::factory(),
            'active' => $this->faker->boolean(),
            'email_active' => $this->faker->boolean(),
            'sms_active' => $this->faker->boolean(),
        ];
    }
}
