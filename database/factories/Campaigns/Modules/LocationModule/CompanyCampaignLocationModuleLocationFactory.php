<?php

namespace Database\Factories\Campaigns\Modules\LocationModule;

use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyCampaignLocationModuleLocation>
 */
class CompanyCampaignLocationModuleLocationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            CompanyCampaignLocationModuleLocation::FIELD_COMPANY_ID  => fake()->randomNumber(),
            CompanyCampaignLocationModuleLocation::FIELD_MODULE_ID   => fake()->randomNumber(),
            CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID => fake()->randomNumber(),
            CompanyCampaignLocationModuleLocation::FIELD_ZIP_CODE    => fake()->randomNumber(),
        ];
    }
}
