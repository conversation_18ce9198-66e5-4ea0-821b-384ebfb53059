<?php

namespace Database\Factories\Legacy;

use App\Models\Legacy\EloquentCompany;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class EloquentCompanyFactory extends Factory
{
    protected $model = EloquentCompany::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            EloquentCompany::COMPANY_ID => EloquentCompany::max(EloquentCompany::COMPANY_ID) + 1,
            EloquentCompany::REFERENCE => Str::random(10),
            EloquentCompany::TIMESTAMP_ADDED => $this->faker->dateTime()->getTimestamp(),
            EloquentCompany::TYPE => $this->faker->randomElement(EloquentCompany::COMPANY_TYPES),
            EloquentCompany::COMPANY_NAME => $this->faker->company(),
            EloquentCompany::STATUS => EloquentCompany::STATUS_ACTIVE
        ];
    }

    /**
     * @return Factory
     */
    public function affiliate(): Factory
    {
        return $this->state(function(array $attributes) {
            return [
                EloquentCompany::TYPE => EloquentCompany::TYPE_AFFILIATE
            ];
        });
    }

    public function withCompanyId(int|null $companyId = null): Factory
    {
        return $this->state(function (array $attributes) use ($companyId) {
            return [
                EloquentCompany::COMPANY_ID => $companyId ?? $this->faker->numberBetween(1)
            ];
        });
    }
}
