<?php

namespace Database\Factories\Legacy;

use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentUser;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class EloquentUserFactory extends Factory
{
    protected $model = EloquentUser::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            EloquentUser::DISPLAY_NAME => $this->faker->name(),
            EloquentUser::FIRST_NAME => $this->faker->firstName(),
            EloquentUser::LAST_NAME => $this->faker->lastName(),
            EloquentUser::TITLE => $this->faker->title(),
            EloquentUser::STATUS => true,
            EloquentUser::PHONE => $this->faker->phoneNumber(),
            EloquentUser::EMAIL => $this->faker->unique()->email(),
            EloquentUser::SECURITY_LEVEL => 99,
            EloquentUser::COMPANY_ID => EloquentCompany::factory(),
            EloquentUser::FAILED_LOGIN_COUNT => 0,
            EloquentUser::PASSWORD => 'password',
            EloquentUser::DELETED_AT => null,
            EloquentUser::DATE_REGISTERED => $this->faker->dateTime->format('Y-m-d H:i:s')
        ];
    }
}
