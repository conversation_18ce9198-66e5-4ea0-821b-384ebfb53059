<?php

namespace Database\Factories\Legacy;

use App\Models\Legacy\EloquentCampaign;
use App\Models\Legacy\EloquentCompany;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class EloquentCampaignFactory extends Factory
{
    protected $model = EloquentCampaign::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            EloquentCampaign::COMPANY_ID => EloquentCompany::factory(),
            EloquentCampaign::CATEGORY => $this->faker->word(),
            EloquentCampaign::NAME => $this->faker->name(),
            EloquentCampaign::STATUS => true,
            EloquentCampaign::TIMESTAMP_ADDED => $this->faker->dateTime()->getTimestamp()
        ];
    }
}
