<?php

namespace Database\Factories\Legacy;

use App\Abstracts\ResetUniqueFactory;
use App\Enums\Odin\StateAbbreviation;
use App\Models\Legacy\Location;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class LocationFactory extends ResetUniqueFactory
{
    /**
     * @inheritDoc
     */
    public function getData(bool $reset = false): array
    {
        $states = StateAbbreviation::getAsKeyValueSelectArray();

        $state = $this->faker->unique($reset)->randomKey($states);

        return [
            Location::TYPE => Location::TYPE_STATE,
            Location::CITY => null,
            Location::CITY_KEY => null,
            Location::STATE => $state,
            Location::STATE_ABBREVIATION => $states[$state],
            Location::STATE_KEY => Str::slug($state),
            Location::ZIP_CODE => null,
            Location::COUNTY => null,
            Location::COUNTY_KEY => null
        ];
    }

    /**
     * @return Factory
     */
    public function county(): Factory
    {
        return $this->state(function (array $attributes) {
            $county = $this->faker->word();

            return [
                Location::TYPE => Location::TYPE_COUNTY,
                Location::COUNTY => $county,
                Location::COUNTY_KEY => Str::slug($county)
            ];
        });
    }

    /**
     * @return Factory
     */
    public function city(): Factory
    {
        return $this->state(function (array $attributes) {
            $county = $this->faker->word();

            $city = $this->faker->city();

            return [
                Location::TYPE => Location::TYPE_CITY,
                Location::CITY => $city,
                Location::CITY_KEY => Str::slug($city),
                Location::COUNTY => $county,
                Location::COUNTY_KEY => Str::slug($county)
            ];
        });
    }

    /**
     * @return Factory
     */
    public function zipcode(): Factory
    {
        return $this->state(function (array $attributes) {
            $county = $this->faker->word();

            $city = $this->faker->city();

            return [
                Location::TYPE => Location::TYPE_ZIP_CODE,
                Location::CITY => $city,
                Location::CITY_KEY => Str::slug($city),
                Location::ZIP_CODE => Str::substr($this->faker->unique()->postcode(), 0, 5),
                Location::COUNTY => $county,
                Location::COUNTY_KEY => Str::slug($county)
            ];
        });
    }
}
