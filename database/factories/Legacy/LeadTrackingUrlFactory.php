<?php

namespace Database\Factories\Legacy;

use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\LeadTrackingUrl;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class LeadTrackingUrlFactory extends Factory
{
    protected $model = LeadTrackingUrl::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $url = $this->faker->url();

        return [
            LeadTrackingUrl::LEAD_ID => EloquentQuote::factory(),
            LeadTrackingUrl::URL_CONVERT => $url,
            LeadTrackingUrl::URL_START => $url,
            LeadTrackingUrl::SOURCE => $url
        ];
    }
}
