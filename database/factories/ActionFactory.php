<?php

namespace Database\Factories;

use App\Models\Action;
use App\Models\ActionCategory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Action>
 */
class ActionFactory extends Factory
{
    /**
     * @var string
     */
    protected $model = Action::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition(): array
    {
        $relationTypes = [ Action::RELATION_TYPE_COMPANY, Action::RELATION_TYPE_COMPANY_USER, Action::RELATION_TYPE_COMPANY_CONTACT ];
        return [
            Action::FIELD_FROM_USER_ID      => $this->faker->numberBetween(1, 19),
            Action::FIELD_FOR_ID            => $this->faker->numberBetween(1, 19),
            Action::FIELD_FOR_RELATION_TYPE => $this->faker->randomElement($relationTypes),
            Action::FIELD_SUBJECT           => $this->faker->realText(24),
            Action::FIELD_MESSAGE           => $this->faker->realText(64),
            Action::FIELD_RELATION_TASK_ID  => null,
            Action::FIELD_PINNED            => $this->faker->boolean(25),
            Action::FIELD_CATEGORY_ID       => $this->faker->randomElement(ActionCategory::all()->pluck(ActionCategory::FIELD_ID)->toArray()),
        ];
    }
}

