<?php

namespace Database\Factories;

use App\Models\ComputedRejectionStatistic;
use App\Models\ContractorProfile\ContractorProfile;
use App\Models\Odin\Company;
use App\Models\Odin\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<ComputedRejectionStatistic>
 */
class ComputedRejectionStatisticFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            ComputedRejectionStatistic::FIELD_COMPANY_ID                   => Company::factory(),
            ComputedRejectionStatistic::FIELD_LEGACY_COMPANY_ID            => fake()->randomNumber(),
            ComputedRejectionStatistic::FIELD_PRODUCT_ID                   => Product::factory(),
            ComputedRejectionStatistic::FIELD_MANUAL_REJECTION_PERCENTAGE  => fake()->randomNumber(),
            ComputedRejectionStatistic::FIELD_CRM_REJECTION_PERCENTAGE     => fake()->randomNumber(),
            ComputedRejectionStatistic::FIELD_OVERALL_REJECTION_PERCENTAGE => fake()->randomNumber(),
            ComputedRejectionStatistic::FIELD_ASSIGNED_PRODUCT_COST        => fake()->randomNumber(),
            ComputedRejectionStatistic::FIELD_MANUAL_REJECTED_PRODUCT_COST => fake()->randomNumber(),
            ComputedRejectionStatistic::FIELD_CRM_REJECTED_PRODUCT_COST    => fake()->randomNumber(),
            ComputedRejectionStatistic::FIELD_CRM_RESET_TIME               => fake()->randomNumber(),
        ];
    }
}
