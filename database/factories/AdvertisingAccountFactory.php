<?php

namespace Database\Factories;

use App\Enums\Advertising\AdvertisingPlatform;
use App\Models\AdvertisingAccount;
use App\Models\Advertiser;
use App\Models\Odin\Industry;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AdvertisingAccount>
 */
class AdvertisingAccountFactory extends Factory
{
    protected $model = AdvertisingAccount::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            AdvertisingAccount::FIELD_PLATFORM => $this->faker->randomElement(array_keys(AdvertisingPlatform::all())),
            AdvertisingAccount::FIELD_ADVERTISER_ID => Advertiser::factory(),
            AdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID => $this->faker->randomNumber(),
            AdvertisingAccount::FIELD_NAME => $this->faker->name(),
            AdvertisingAccount::FIELD_INDUSTRY => Industry::factory(),
            AdvertisingAccount::FIELD_TRACKS_CONVERSIONS => true,
            AdvertisingAccount::FIELD_UPLOAD_CONVERSIONS_INTERVAL_HOURS => $this->faker->randomNumber(),
            AdvertisingAccount::FIELD_UPLOAD_CONVERSIONS_LAST_RUN_TIMESTAMP => $this->faker->dateTime()->getTimestamp()
        ];
    }
}
