<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Spatie\Permission\Models\Role;

class RoleConfigurationFactory extends Factory
{
    public function definition(): array
    {
        $roles = [
            Role::findOrCreate('sales-development-representative'),
            Role::findOrCreate('business-development-manager'),
        ];

        return [
            'user_id' => User::factory(),
            'role_id' => $this->faker->randomElement($roles),
            'data' => [
                'user_processing_time_limit_days' => $this->faker->numberBetween(1, 365),
            ],
        ];
    }
}
