<?php

namespace Database\Factories;

use App\Models\AvailableCampaignByLocation;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<AvailableCampaignByLocation>
 */
class AvailableCampaignByLocationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            AvailableCampaignByLocation::FIELD_COMPANY_ID         => fake()->randomNumber(),
            AvailableCampaignByLocation::FIELD_LOCATION_ID        => fake()->randomNumber(),
            AvailableCampaignByLocation::FIELD_COUNTY_LOCATION_ID => fake()->randomNumber(),
            AvailableCampaignByLocation::FIELD_CAMPAIGN_ID        => fake()->randomNumber(),
            AvailableCampaignByLocation::FIELD_INDUSTRY_ID        => fake()->randomNumber(),
        ];
    }


}
