<?php

namespace Database\Factories;

use App\Enums\EmailTemplateType;
use App\Models\EmailTemplate;
use App\Models\EmailTemplateBackground;
use App\Models\Odin\Industry;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class EmailTemplateFactory extends Factory
{
    protected $model = EmailTemplate::class;

    public function definition(): array
    {
        return [
            'background_id'                  => EmailTemplateBackground::factory(),
            'name'                           => $this->faker->name(),
            'subject'                        => $this->faker->word(),
            'content'                        => $this->faker->word(),
            'personal'                       => $this->faker->boolean(),
            'default_lead_delivery_template' => $this->faker->boolean(),
            'created_at'                     => Carbon::now(),
            'updated_at'                     => Carbon::now(),
            'type'                           => $this->faker->randomElement(EmailTemplateType::cases()),
            'active'                         => $this->faker->boolean(),
            'engine'                         => $this->faker->word(),

            'owner_user_id' => User::factory(),
            'industry_id'   => Industry::factory(),
        ];
    }
}
