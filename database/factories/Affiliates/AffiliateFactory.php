<?php

namespace Database\Factories\Affiliates;

use App\Models\Affiliates\Affiliate;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Affiliates\Affiliate>
 */
class AffiliateFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            Affiliate::FIELD_NAME => $this->faker->name(),
            Affiliate::FIELD_UUID => $this->faker->uuid()
        ];
    }
}
