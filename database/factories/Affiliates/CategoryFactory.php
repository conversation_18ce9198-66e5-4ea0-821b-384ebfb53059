<?php

namespace Database\Factories\Affiliates;

use App\Models\Affiliates\Affiliate;
use App\Models\Affiliates\Category;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Affiliates\Category>
 */
class CategoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            Category::FIELD_NAME => $this->faker->domainWord(),
            Category::FIELD_AFFILIATE_ID => Affiliate::factory()
        ];
    }
}
