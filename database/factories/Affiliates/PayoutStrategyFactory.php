<?php

namespace Database\Factories\Affiliates;

use App\Enums\Affiliate\PayoutStrategyTypeEnum;
use App\Models\Affiliates\PayoutStrategy;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Affiliates\PayoutStrategy>
 */
class PayoutStrategyFactory extends Factory
{
    protected $model = PayoutStrategy::class;
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        /** @var PayoutStrategyTypeEnum $strategyType */
        $strategyType = $this->faker->randomElement(PayoutStrategyTypeEnum::class);

        return [
            PayoutStrategy::FIELD_TYPE => $strategyType,
            PayoutStrategy::FIELD_VALUE => $strategyType->getClass()->defaultValue(),
            PayoutStrategy::FIELD_ACTIVE_FROM => $this->faker->date(),
        ];
    }
}
