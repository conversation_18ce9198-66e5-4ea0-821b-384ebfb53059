<?php

namespace Database\Factories\Affiliates;

use App\Models\Affiliates\Affiliate;
use App\Models\Affiliates\Campaign;
use App\Models\Affiliates\Category;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Affiliates\Campaign>
 */
class CampaignFactory extends Factory
{
    protected $model = Campaign::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            Campaign::FIELD_AFFILIATE_ID => Affiliate::factory(),
            Campaign::FIELD_NAME => $this->faker->name(),
            Campaign::FIELD_STATUS => $this->faker->boolean(),
            Campaign::FIELD_CATEGORY_ID => Category::factory()
        ];
    }
}
