<?php

namespace Database\Factories;

use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Company;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Model;
use Random\RandomException;

/**
 * @extends Factory<Model>
 */
class LeadCampaignFactory extends Factory
{
    protected $model = LeadCampaign::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     * @throws RandomException
     */
    public function definition()
    {
        $companyId = EloquentCompany::query()
            ->where(EloquentCompany::REFERENCE, EloquentCompany::factory()->create()->{EloquentCompany::REFERENCE})
            ->firstOrFail()
            ->{EloquentCompany::ID};

        $this->faker->seed(random_int(0, time()));

        return [
            LeadCampaign::COMPANY_ID => $companyId,
            LeadCampaign::UUID => $this->faker->uuid()
        ];
    }
}
