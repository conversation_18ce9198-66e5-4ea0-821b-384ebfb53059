<?php

namespace Database\Factories;

use App\Models\ContactSubscription;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ContactSubscription>
 */
class ContactSubscriptionFactory extends Factory
{
    protected $model = ContactSubscription::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            ContactSubscription::FIELD_CONTACT_ID => $this->faker->randomNumber(4),
            ContactSubscription::FIELD_CONTACT_TYPE => $this->faker->randomElement([ContactSubscription::CONTACT_TYPE_CONTACT, ContactSubscription::CONTACT_TYPE_USER]),
            ContactSubscription::FIELD_CONTACT_METHOD => $this->faker->randomElement([ContactSubscription::CONTACT_METHOD_EMAIL, ContactSubscription::CONTACT_METHOD_SMS]),
            ContactSubscription::FIELD_NOTIFICATION_TYPE => ContactSubscription::NOTIFICATION_TYPE_SALESBAIT,
            ContactSubscription::FIELD_UNSUBSCRIBED => false
        ];
    }
}
