<?php

namespace Database\Factories\MissedProducts;

use App\Enums\OpportunityNotifications\OpportunityNotificationDeliveryMethods;
use App\Models\MissedProducts\OpportunityNotification;
use App\Models\MissedProducts\OpportunityNotificationConfig;
use App\Models\Odin\Company;
use Illuminate\Database\Eloquent\Factories\Factory;
use Random\RandomException;

/**
 * @extends Factory<OpportunityNotification>
 */
class OpportunityNotificationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     * @throws RandomException
     */
    public function definition(): array
    {
        $recipients = array_map(function () {
            return $this->faker->email;
        }, array_fill(0, random_int(1, 4), null));

        return [
            OpportunityNotification::FIELD_COMPANY_ID => Company::exists() ? Company::all()->random()->id : Company::factory(),
            OpportunityNotification::FIELD_CONFIG_ID => OpportunityNotificationConfig::all()->random()->{OpportunityNotificationConfig::FIELD_ID},
            OpportunityNotification::FIELD_CONTENT => $this->faker->text(75),
            OpportunityNotification::FIELD_RECIPIENTS => implode(', ', $recipients),
            OpportunityNotification::FIELD_DELIVERY_METHOD => OpportunityNotificationDeliveryMethods::EMAIL, // replace with enum "Email" methods
            OpportunityNotification::FIELD_SENT_AT => $this->faker->dateTimeThisMonth()->format('Y-m-d H:i:s'),
            OpportunityNotification::FIELD_VIEW_COUNT => random_int(0, 3),
        ];
    }

}
