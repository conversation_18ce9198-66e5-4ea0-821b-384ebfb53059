<?php

namespace Database\Factories;

use App\Models\Phone;
use App\Models\Text;
use Illuminate\Database\Eloquent\Factories\Factory;
use Ramsey\Uuid\Uuid;

/**
 * @extends Factory<Text>
 */
final class TextFactory extends Factory
{
    /**
    * The name of the factory's corresponding model.
    *
    * @var  string
    */
    protected $model = Text::class;

    /**
    * Define the model's default state.
    *
    * @return  array
    */
    public function definition(): array
    {
        return [
            Text::FIELD_EXTERNAL_REFERENCE => Uuid::uuid4(),
            Text::FIELD_EXTERNAL_TYPE      => Text::EXTERNAL_TYPE_TWILIO,
            Text::FIELD_PHONE_ID           => Phone::factory(),
            Text::FIELD_OTHER_NUMBER       => $this->faker->phoneNumber(),
            Text::FIELD_DIRECTION          => $this->faker->randomElement([Text::DIRECTION_INBOUND, Text::DIRECTION_OUTBOUND]),
            Text::FIELD_MESSAGE_BODY       => $this->faker->realText(64),
            Text::FIELD_RELATION_TYPE      => $this->faker->randomElement([Text::RELATION_COMPANY, Text::RELATION_PHONE]),
            Text::FIELD_RELATION_ID        => $this->faker->numberBetween(1, 19),
        ];
    }
}
