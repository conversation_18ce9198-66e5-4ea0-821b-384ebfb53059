<?php

namespace Database\Factories\Conference;

use App\Models\Conference\Conference;
use Illuminate\Database\Eloquent\Factories\Factory;

class ConferenceTranscriptFactory extends Factory
{
    public function definition(): array
    {
        return [
            'conference_id' => Conference::factory(),
            'external_id' => $this->faker->uuid,
            'start_time' => $this->faker->dateTime(),
            'end_time' => $this->faker->dateTime(),
            'duration_in_seconds' => $this->faker->randomNumber(2),
            'docs_destination_document_id' => $this->faker->uuid,
            'docs_destination_document_url' => $this->faker->url,
        ];
    }
}
