<?php

namespace Database\Factories\Conference;

use App\Models\Conference\Conference;
use Illuminate\Database\Eloquent\Factories\Factory;

class ConferenceParticipantFactory extends Factory
{
    public function definition(): array
    {
        return [
            'conference_id' => Conference::factory(),
            'external_id' => $this->faker->uuid,
            'name' => $this->faker->name,
            'earliest_start_time' => $this->faker->dateTime,
            'latest_end_time' => $this->faker->dateTime,
            'duration_in_seconds' => $this->faker->randomNumber()
        ];
    }
}
