<?php

namespace Database\Factories\Conference;

use App\Models\Conference\Conference;
use App\Models\Conference\ConferenceParticipant;
use App\Models\Conference\ConferenceTranscript;
use Illuminate\Database\Eloquent\Factories\Factory;

class ConferenceTranscriptEntryFactory extends Factory
{
    public function definition(): array
    {
        return [
            'conference_transcript_id' => ConferenceTranscript::factory(),
            'conference_participant_id' => ConferenceParticipant::factory(),
            'external_id' => $this->faker->uuid(),
            'external_participant_id' => $this->faker->uuid(),
            'start_time' => $this->faker->dateTime(),
            'end_time' => $this->faker->dateTime(),
            'text' => $this->faker->paragraph(),
        ];
    }
}
