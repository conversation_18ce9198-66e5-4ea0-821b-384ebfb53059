<?php

namespace Database\Factories\MarketingCampaigns;

use App\Enums\MarketingCampaigns\MarketingCampaignConsumerStatus;
use App\Models\MarketingCampaign;
use App\Models\MarketingCampaignConsumer;
use App\Models\Odin\Consumer;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<MarketingCampaignConsumer>
 */
class MarketingCampaignConsumerFactory extends Factory
{
    protected $model = MarketingCampaignConsumer::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $status = $this->faker->randomElement(MarketingCampaignConsumerStatus::class);

        $related = [
            MarketingCampaignConsumerStatus::INITIALISED->value => [],
            MarketingCampaignConsumerStatus::UPLOADED->value    => [],
            MarketingCampaignConsumerStatus::ARCHIVED->value    => [],
            MarketingCampaignConsumerStatus::ERROR->value       => [
                MarketingCampaignConsumer::FIELD_SENT_AT => $this->faker->randomElement([null, $this->faker->dateTime()]),
            ],
            MarketingCampaignConsumerStatus::SENT->value        => [
                MarketingCampaignConsumer::FIELD_SENT_AT => $this->faker->dateTime(),
            ],
            MarketingCampaignConsumerStatus::QUEUED->value      => [],
            MarketingCampaignConsumerStatus::DELIVERED->value   => [
                MarketingCampaignConsumer::FIELD_SENT_AT        => $this->faker->dateTime(),
                MarketingCampaignConsumer::FIELD_DELIVERED_AT   => $this->faker->dateTime(),
                MarketingCampaignConsumer::FIELD_OPENED_AT      => $this->faker->randomElement([null, $this->faker->dateTime()]),
                MarketingCampaignConsumer::FIELD_CLICKED_AT     => $this->faker->randomElement([null, $this->faker->dateTime()]),
                MarketingCampaignConsumer::FIELD_REVALIDATED_AT => $this->faker->randomElement([null, $this->faker->dateTime()]),
            ],
        ];

        return [
            MarketingCampaignConsumer::FIELD_CONSUMER_REFERENCE           => $this->faker->unique()->uuid(),
            MarketingCampaignConsumer::FIELD_EXTERNAL_REFERENCE           => $this->faker->uuid(),
            MarketingCampaignConsumer::FIELD_MARKETING_CAMPAIGN_REFERENCE => $this->faker->uuid(),
            MarketingCampaignConsumer::FIELD_STATUS                       => $status,
            ...$related[$status->value],
        ];
    }
}
