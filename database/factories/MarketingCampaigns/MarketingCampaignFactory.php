<?php

namespace Database\Factories\MarketingCampaigns;

use App\Enums\MarketingCampaigns\MarketingCampaignStatus;
use App\Enums\MarketingCampaigns\MarketingCampaignType;
use App\Models\MarketingCampaign;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<MarketingCampaign>
 */
class MarketingCampaignFactory extends Factory
{
    protected $model = MarketingCampaign::class;
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            MarketingCampaign::FIELD_NAME => $this->faker->words(2, true),
            MarketingCampaign::FIELD_DESCRIPTION => $this->faker->sentence(),
            MarketingCampaign::FIELD_EXTERNAL_REFERENCE => $this->faker->uuid(),
            MarketingCampaign::FIELD_CODE => $this->faker->uuid(),
            MarketingCampaign::FIELD_TYPE => $this->faker->randomElement(MarketingCampaignType::class),
            MarketingCampaign::FIELD_STATUS => $this->faker->randomElement(MarketingCampaignStatus::class),
        ];
    }
}
