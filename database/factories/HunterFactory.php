<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Hunter;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Spatie\Permission\Models\Role;

/**
 * @extends Factory<\App\Models\Hunter>
 */
final class HunterFactory extends Factory
{
    /**
    * The name of the factory's corresponding model.
    *
    * @var  string
    */
    protected $model = Hunter::class;

    /**
    * Define the model's default state.
    *
    * @return  array
    */
    public function definition(): array
    {
        return [
            'user_id' => User::factory()->create()->assignRole(Role::all()->random()),
            'include_in_round_robin' => $this->faker->boolean,
        ];
    }
}
