<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Phone;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Phone>
 */
final class PhoneFactory extends Factory
{
    /**
    * The name of the factory's corresponding model.
    *
    * @var  string
    */
    protected $model = Phone::class;

    /**
    * Define the model's default state.
    *
    * @return  array
    */
    public function definition(): array
    {
        return [
            Phone::FIELD_PHONE              => preg_replace("/\D/", "", $this->faker->e164PhoneNumber()),
            Phone::FIELD_STATUS             => Phone::STATUS_ACTIVE,
            Phone::FIELD_EXTERNAL_REFERENCE => $this->faker->uuid(),
            Phone::FIELD_EXTERNAL_TYPE      => Phone::EXTERNAL_TYPE_TWILIO,
        ];
    }
}
