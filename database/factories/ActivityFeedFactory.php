<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Enums\ActivityType;
use App\Models\ActivityFeed;
use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<\App\Models\ActivityFeed>
 */
final class ActivityFeedFactory extends Factory
{
    /**
    * The name of the factory's corresponding model.
    *
    * @var  string
    */
    protected $model = ActivityFeed::class;

    /**
    * Define the model's default state.
    *
    * @return  array
    */
    public function definition(): array
    {
        return [
            'company_id' => Company::factory()->createQuietly(),
            'item_type' => $this->faker->randomElement(ActivityType::cases()),
            'item_id' => $this->faker->randomNumber(),
            'user_id'=> User::factory(),
        ];
    }
}
