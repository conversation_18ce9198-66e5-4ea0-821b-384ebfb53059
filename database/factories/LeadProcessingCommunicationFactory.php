<?php

namespace Database\Factories;

use App\Models\LeadProcessingCommunication;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\LeadProcessingCommunication>
 */
class LeadProcessingCommunicationFactory extends Factory
{
    protected $model = LeadProcessingCommunication::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            LeadProcessingCommunication::FIELD_TYPE => $this->faker->randomElement([LeadProcessingCommunication::TYPE_ACTION, LeadProcessingCommunication::TYPE_SMS, LeadProcessingCommunication::TYPE_CALL]),
            LeadProcessingCommunication::FIELD_RELATION_ID => $this->faker->randomNumber(),
            LeadProcessingCommunication::FIELD_LEAD_PROCESSOR_ID => $this->faker->randomNumber(),
            LeadProcessingCommunication::FIELD_MOST_RECENT => false,
            LeadProcessingCommunication::CREATED_AT => $this->faker->dateTime()
        ];
    }

    /**
     * @return Factory
     */
    public function typeAction(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                LeadProcessingCommunication::FIELD_TYPE => LeadProcessingCommunication::TYPE_ACTION
            ];
        });
    }
}
