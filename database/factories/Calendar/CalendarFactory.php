<?php

namespace Database\Factories\Calendar;

use App\Enums\Calendar\CalendarProviderType;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class CalendarFactory extends Factory
{
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'external_id' => Str::random(10),
            'provider' => CalendarProviderType::GOOGLE,
            'name' => $this->faker->company(),
            'timezone' => $this->faker->timezone(),
        ];
    }
}
