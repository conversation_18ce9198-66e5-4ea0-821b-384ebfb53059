<?php

namespace Database\Factories\Calendar;

use App\Enums\Calendar\CalendarEventStatus;
use App\Models\Calendar\Calendar;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class CalendarEventFactory extends Factory
{
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'calendar_id' => Calendar::factory(),
            'external_id' => Str::random(10),
            'title' => $this->faker->sentence,
            'description' => $this->faker->sentence,
            'payload' => [],
            'location' => $this->faker->streetAddress(),
            'status' => CalendarEventStatus::CONFIRMED,
            'start_time' => $this->faker->dateTime(),
            'end_time' => $this->faker->dateTime(),
        ];
    }
}
