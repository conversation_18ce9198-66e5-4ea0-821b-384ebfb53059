<?php

namespace Database\Factories\Calendar;

use App\Models\Calendar\CalendarEvent;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class DemoFactory extends Factory
{
    public function definition(): array
    {
        return [
            'calendar_event_id' => CalendarEvent::factory(),
            'status' => 'completed',
            'note' => $this->faker->sentence(),
            'user_id' => User::factory(),
        ];
    }
}
