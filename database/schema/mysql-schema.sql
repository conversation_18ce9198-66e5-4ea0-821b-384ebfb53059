/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
DROP TABLE IF EXISTS `account_manager_clients`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `account_manager_clients` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_manager_id` bigint unsigned NOT NULL,
  `company_reference` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `total_spend` double(11,2) NOT NULL,
  `status` tinyint NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `account_manager_clients_account_manager_id_index` (`account_manager_id`),
  KEY `account_manager_clients_company_reference_index` (`company_reference`),
  KEY `idx_id_reference` (`account_manager_id`,`company_reference`),
  CONSTRAINT `account_manager_clients_account_manager_id_foreign` FOREIGN KEY (`account_manager_id`) REFERENCES `account_managers` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `account_managers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `account_managers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `type` tinyint NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `include_in_sales_round_robin` tinyint(1) NOT NULL DEFAULT '0',
  `status` tinyint NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `account_managers_user_id_index` (`user_id`),
  CONSTRAINT `account_managers_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `action_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `action_categories` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `action_tags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `action_tags` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `action_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `action_tags_user_id_foreign` (`user_id`),
  KEY `action_tags_action_id_foreign` (`action_id`),
  CONSTRAINT `action_tags_action_id_foreign` FOREIGN KEY (`action_id`) REFERENCES `actions` (`id`),
  CONSTRAINT `action_tags_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `actions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `actions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `from_user_id` bigint unsigned DEFAULT NULL,
  `for_id` bigint unsigned DEFAULT NULL,
  `legacy_for_id` int DEFAULT NULL,
  `for_relation_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `subject` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `related_task_id` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `pinned` tinyint(1) NOT NULL DEFAULT '0',
  `action_category_id` bigint unsigned DEFAULT NULL,
  `display_date` date DEFAULT NULL,
  `tag_by_email` tinyint(1) DEFAULT '0',
  `previous_sales_status` int DEFAULT NULL,
  `updated_sales_status` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `activity_conversations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `activity_conversations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `activity_id` bigint unsigned NOT NULL,
  `parent_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `comment` mediumtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `activity_feeds`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `activity_feeds` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `item_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `item_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `company_cadence_group_action_id` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `activity_feeds_rel_type_rel_id_index` (`item_type`,`item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `address_discovery_statuses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `address_discovery_statuses` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `address_id` bigint unsigned NOT NULL,
  `discovery_reference` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `checked` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `addresses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `addresses` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `legacy_id` int DEFAULT NULL,
  `address_1` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `address_2` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `city` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `state` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `zip_code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `country` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `latitude` double(8,2) NOT NULL,
  `longitude` double(8,2) NOT NULL,
  `place_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `utc` int DEFAULT NULL,
  `imported` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `addresses_legacy_id_index` (`legacy_id`),
  KEY `addresses_zip_code_index` (`zip_code`),
  KEY `addresses_place_id_index` (`place_id`),
  KEY `addresses_state_index` (`state`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `advertising_account_websites`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `advertising_account_websites` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `platform` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `platform_account_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `website_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `advertising_accounts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `advertising_accounts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `platform` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `platform_account_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `industry` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tracks_conversions` tinyint(1) NOT NULL DEFAULT '0',
  `upload_conversions_interval_hours` int NOT NULL DEFAULT '4',
  `upload_conversions_last_run_timestamp` bigint NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `advertising_campaign_automation_parameters`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `advertising_campaign_automation_parameters` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `platform` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `platform_account_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `platform_campaign_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `strategy` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `parameter` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `operator` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `threshold` int NOT NULL,
  `threshold_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `advertising_campaign_history_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `advertising_campaign_history_logs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `platform` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `platform_account_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `platform_campaign_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `advertising_campaigns`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `advertising_campaigns` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `industry` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `platform` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `platform_account_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `platform_campaign_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_run_timestamp` int NOT NULL DEFAULT '0',
  `run_interval_secs` int NOT NULL DEFAULT '0',
  `run_interval_display_unit` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `appointment_deliveries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `appointment_deliveries` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `appointment_offering_id` bigint unsigned DEFAULT NULL,
  `product_campaign_id` bigint unsigned NOT NULL,
  `product_assignment_id` bigint unsigned NOT NULL,
  `consumer_delivered` tinyint(1) NOT NULL DEFAULT '0',
  `company_delivered` tinyint(1) NOT NULL DEFAULT '0',
  `consumer_token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `consumer_code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `attempts` int NOT NULL DEFAULT '0',
  `next_attempt_timestamp` int unsigned NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `delivery_status_idx` (`consumer_delivered`,`company_delivered`),
  KEY `appointment_offering_id_idx` (`appointment_offering_id`),
  KEY `product_assignment_id_idx` (`product_assignment_id`),
  KEY `product_assignment_appt_offer_idx` (`appointment_offering_id`,`product_assignment_id`),
  KEY `product_campaign_id_idx` (`product_campaign_id`),
  KEY `appointment_deliveries_next_attempt_timestamp_index` (`next_attempt_timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `appointment_offerings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `appointment_offerings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `consumer_product_id` bigint unsigned NOT NULL,
  `product_campaign_budget_id` bigint unsigned NOT NULL,
  `sale_type_id` bigint unsigned NOT NULL,
  `best_revenue_scenario_logs_run_id` bigint unsigned NOT NULL,
  `status` int NOT NULL,
  `attempt` int NOT NULL,
  `price` double(8,2) NOT NULL,
  `link_key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `link_code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `start_time` timestamp NULL DEFAULT NULL,
  `expiry_time` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `specific_offering_idx` (`consumer_product_id`,`product_campaign_budget_id`,`link_key`,`status`,`start_time`,`expiry_time`,`attempt`),
  KEY `status_cp_id_idx` (`status`,`consumer_product_id`),
  KEY `status_time_cp_id_idx` (`status`,`expiry_time`,`start_time`,`consumer_product_id`,`attempt`),
  KEY `specific_brs_attempt_idx` (`best_revenue_scenario_logs_run_id`,`consumer_product_id`,`product_campaign_budget_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `appointment_processing_allocations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `appointment_processing_allocations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `lead_consumer_product_id` bigint unsigned NOT NULL,
  `consumer_product_id` bigint unsigned NOT NULL,
  `lead_processor_id` bigint unsigned NOT NULL,
  `processing_scenario` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `allocated` tinyint(1) NOT NULL DEFAULT '0',
  `delivered` tinyint(1) NOT NULL DEFAULT '0',
  `allocate_at` timestamp NOT NULL,
  `failed_appt_allocation` tinyint(1) NOT NULL DEFAULT '0',
  `error` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `allocated_as_lead` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `cancelled_rejected` tinyint(1) NOT NULL DEFAULT '0',
  `ran_lead_allocation` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `available_budgets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `available_budgets` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `budget_available_dollars` double(8,2) NOT NULL DEFAULT '0.00',
  `budget_available_volume` int NOT NULL DEFAULT '0',
  `location_id` int NOT NULL,
  `county_location_id` int DEFAULT NULL,
  `available_campaign_count` int NOT NULL DEFAULT '0',
  `unlimited_budget_count` int NOT NULL DEFAULT '0',
  `budget_type` tinyint NOT NULL,
  `industry_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `available_budgets_location_id_index` (`location_id`),
  KEY `available_budgets_county_location_id_index` (`county_location_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `available_company_by_locations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `available_company_by_locations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_id` bigint unsigned NOT NULL,
  `legacy_company_id` bigint unsigned DEFAULT NULL,
  `location_id` bigint unsigned NOT NULL,
  `county_location_id` int DEFAULT NULL,
  `industry_slug` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `available_company_by_locations_county_location_id_index` (`county_location_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `available_leads`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `available_leads` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `location_id` bigint unsigned NOT NULL,
  `status` int NOT NULL,
  `utc_date` timestamp NOT NULL,
  `count` bigint unsigned NOT NULL,
  `verified` tinyint(1) NOT NULL,
  `service_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `best_revenue_scenario_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `best_revenue_scenario_logs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `run_id` int NOT NULL,
  `consumer_product_id` bigint unsigned NOT NULL,
  `sale_type_id` bigint unsigned NOT NULL,
  `product_campaign_id` bigint unsigned NOT NULL,
  `step` int NOT NULL,
  `message` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `budget_containers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `budget_containers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_campaign_id` int unsigned NOT NULL,
  `type` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `budget_containers_company_campaign_id_index` (`company_campaign_id`),
  KEY `budget_containers_type_index` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `budgets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `budgets` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `budget_container_id` int unsigned NOT NULL,
  `display_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` tinyint unsigned NOT NULL DEFAULT '0',
  `type` int NOT NULL DEFAULT '0',
  `value` int unsigned NOT NULL DEFAULT '0',
  `last_modified_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by_company_user_id` int unsigned DEFAULT NULL,
  `product_configuration` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `budgets_budget_container_id_index` (`budget_container_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `bundle_invoice_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `bundle_invoice_history` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `description` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `note` mediumtext COLLATE utf8mb4_unicode_ci,
  `bundle_invoice_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `bundle_invoice_history_bundle_invoice_id_foreign` (`bundle_invoice_id`),
  CONSTRAINT `bundle_invoice_history_bundle_invoice_id_foreign` FOREIGN KEY (`bundle_invoice_id`) REFERENCES `bundle_invoices` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `bundle_invoices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `bundle_invoices` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `status` int NOT NULL DEFAULT '0',
  `cost` decimal(8,2) NOT NULL,
  `credit` decimal(8,2) NOT NULL,
  `note` mediumtext COLLATE utf8mb4_unicode_ci,
  `issued_by` bigint unsigned DEFAULT NULL,
  `approved_by` bigint unsigned DEFAULT NULL,
  `denied_by` bigint unsigned DEFAULT NULL,
  `issued_at` timestamp NULL DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `denied_at` timestamp NULL DEFAULT NULL,
  `paid_at` timestamp NULL DEFAULT NULL,
  `bundle_id` bigint unsigned NOT NULL,
  `company_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `cancelled_at` timestamp NULL DEFAULT NULL,
  `cancelled_by` bigint unsigned DEFAULT NULL,
  `payable_invoice_id` bigint unsigned DEFAULT NULL,
  `payable_invoice_url` mediumtext COLLATE utf8mb4_unicode_ci,
  `failed_at` timestamp NULL DEFAULT NULL,
  `fail_reason` mediumtext COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`),
  KEY `bundle_invoices_issued_by_foreign` (`issued_by`),
  KEY `bundle_invoices_approved_by_foreign` (`approved_by`),
  KEY `bundle_invoices_denied_by_foreign` (`denied_by`),
  KEY `bundle_invoices_bundle_id_foreign` (`bundle_id`),
  KEY `bundle_invoices_company_id_foreign` (`company_id`),
  KEY `bundle_invoices_cancelled_by_foreign` (`cancelled_by`),
  CONSTRAINT `bundle_invoices_approved_by_foreign` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`),
  CONSTRAINT `bundle_invoices_bundle_id_foreign` FOREIGN KEY (`bundle_id`) REFERENCES `bundles` (`id`),
  CONSTRAINT `bundle_invoices_cancelled_by_foreign` FOREIGN KEY (`cancelled_by`) REFERENCES `users` (`id`),
  CONSTRAINT `bundle_invoices_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`),
  CONSTRAINT `bundle_invoices_denied_by_foreign` FOREIGN KEY (`denied_by`) REFERENCES `users` (`id`),
  CONSTRAINT `bundle_invoices_issued_by_foreign` FOREIGN KEY (`issued_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `bundles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `bundles` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `note` mediumtext COLLATE utf8mb4_unicode_ci,
  `description` mediumtext COLLATE utf8mb4_unicode_ci,
  `cost` decimal(8,2) NOT NULL,
  `credit` decimal(8,2) NOT NULL,
  `created_by` bigint unsigned NOT NULL,
  `activated_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `industry_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `bundles_created_by_foreign` (`created_by`),
  KEY `bundles_industry_id_foreign` (`industry_id`),
  CONSTRAINT `bundles_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `bundles_industry_id_foreign` FOREIGN KEY (`industry_id`) REFERENCES `industries` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `cache`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cache` (
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `cache_locks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cache_locks` (
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `owner` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `cadence_email_headers_and_footers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cadence_email_headers_and_footers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned NOT NULL,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `cadence_email_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cadence_email_templates` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `subject` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `body` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `global` tinyint(1) NOT NULL DEFAULT '0',
  `user_id` int unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `cadence_routines`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cadence_routines` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `contact_decision_makers_only` tinyint(1) NOT NULL DEFAULT '1',
  `contact_on_weekdays_only` tinyint(1) NOT NULL DEFAULT '1',
  `user_id` int unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `global` tinyint(1) NOT NULL DEFAULT '0',
  `domain` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'fixr.com',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `cadence_scheduled_group_actions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cadence_scheduled_group_actions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `cadence_scheduled_group_id` int unsigned NOT NULL,
  `action_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `task_template_id` int unsigned DEFAULT NULL,
  `sms_template_id` int unsigned DEFAULT NULL,
  `email_template_id` int unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `cadence_scheduled_groups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cadence_scheduled_groups` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `cadence_routine_id` int unsigned NOT NULL,
  `ordinal_value` int unsigned DEFAULT NULL,
  `execution_delay_minutes` int unsigned NOT NULL DEFAULT '0',
  `execution_time_exact` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `execution_time_window_start` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `execution_time_window_end` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `cadence_sms_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cadence_sms_templates` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `body` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `global` tinyint(1) NOT NULL DEFAULT '0',
  `user_id` int unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `cadence_task_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cadence_task_templates` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `global` tinyint(1) NOT NULL DEFAULT '0',
  `user_id` int unsigned NOT NULL,
  `task_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `cadence_user_contact_exclusions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cadence_user_contact_exclusions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_cadence_routine_id` int unsigned DEFAULT NULL,
  `user_id` int unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `call_recordings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `call_recordings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `call_id` int NOT NULL,
  `duration_seconds` int NOT NULL,
  `recording_link` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `sid` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `call_id_index` (`call_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `calls`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `calls` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `external_reference` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `external_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone_id` int NOT NULL,
  `other_number` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `formatted_other_number` bigint unsigned DEFAULT NULL,
  `direction` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `result` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `call_start` timestamp NULL DEFAULT NULL,
  `call_end` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `relation_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `relation_id` int DEFAULT NULL,
  `note` text COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`),
  KEY `fon_index` (`formatted_other_number`),
  KEY `relation_type_and_id_index` (`relation_type`,`relation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `campaign_reactivations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `campaign_reactivations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `campaign_id` bigint unsigned NOT NULL,
  `reactivate_at` timestamp NULL DEFAULT NULL,
  `reason` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `campaign_reactivations_campaign_id_index` (`campaign_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `client_token_services`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `client_token_services` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `service_key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `service_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `client_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `client_tokens` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `service_id` bigint unsigned NOT NULL,
  `client_token` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `refresh_token` text COLLATE utf8mb4_unicode_ci,
  `expires_in` bigint DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `client_tokens_service_id_foreign` (`service_id`),
  CONSTRAINT `client_tokens_service_id_foreign` FOREIGN KEY (`service_id`) REFERENCES `client_token_services` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `communications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `communications` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `relation_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `companies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `companies` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `legacy_id` int DEFAULT NULL,
  `reference` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `entity_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `website` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `website_verified_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `website_verified_at` timestamp NULL DEFAULT NULL,
  `consolidated_status` int DEFAULT NULL,
  `sales_status` int NOT NULL DEFAULT '1',
  `campaigns_are_partially_suspended` tinyint(1) NOT NULL DEFAULT '0',
  `status` int NOT NULL,
  `admin_status` int DEFAULT NULL,
  `admin_locked` tinyint(1) NOT NULL DEFAULT '0',
  `admin_approved` tinyint(1) NOT NULL DEFAULT '0',
  `archived` tinyint(1) NOT NULL DEFAULT '0',
  `imported` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `link_to_logo` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `prescreened_at` timestamp NULL DEFAULT NULL,
  `uuid` char(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `watchdog_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `never_exceed_budget` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `companies_statuses_idx` (`status`,`admin_locked`,`admin_approved`,`admin_status`),
  KEY `idx_legacy_id` (`legacy_id`),
  KEY `reference_idx` (`reference`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_cadence_routines`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_cadence_routines` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `cadence_routine_id` int unsigned NOT NULL,
  `company_id` int unsigned NOT NULL,
  `contact_decision_makers_only` tinyint(1) NOT NULL,
  `contact_on_weekdays_only` tinyint(1) NOT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `domain` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'fixr.com',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_cadence_scheduled_group_actions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_cadence_scheduled_group_actions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_cadence_scheduled_group_id` int unsigned NOT NULL,
  `skip` tinyint(1) NOT NULL DEFAULT '0',
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `resolution_notes` json NOT NULL,
  `action_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `task_template_id` int unsigned DEFAULT NULL,
  `sms_template_id` int unsigned DEFAULT NULL,
  `email_template_id` int unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `preview` longtext COLLATE utf8mb4_unicode_ci,
  `task_id` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_cadence_scheduled_groups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_cadence_scheduled_groups` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_cadence_routine_id` int unsigned NOT NULL,
  `skip` tinyint(1) NOT NULL DEFAULT '0',
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'not_started',
  `ordinal_value` int unsigned DEFAULT NULL,
  `execution_delay_minutes` int unsigned NOT NULL,
  `execution_time_exact` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `execution_time_window_start` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `execution_time_window_end` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `resolution_notes` json NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `target_execution_timestamp` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `success_count` int unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_campaign_bid_price_modules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_campaign_bid_price_modules` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_campaign_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_campaign_crm_delivery_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_campaign_crm_delivery_logs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `campaign_id` bigint unsigned NOT NULL,
  `module_id` bigint unsigned NOT NULL,
  `crm_id` bigint unsigned NOT NULL,
  `consumer_product_id` bigint unsigned NOT NULL,
  `success` tinyint(1) NOT NULL,
  `payload` json NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `company_campaign_crm_delivery_logs_campaign_id_index` (`campaign_id`),
  KEY `company_campaign_crm_delivery_logs_module_id_index` (`module_id`),
  KEY `company_campaign_crm_delivery_logs_crm_id_index` (`crm_id`),
  KEY `company_campaign_crm_delivery_logs_consumer_product_id_index` (`consumer_product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_campaign_delivery_module_contacts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_campaign_delivery_module_contacts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `module_id` bigint unsigned NOT NULL,
  `contact_id` bigint unsigned NOT NULL,
  `active` tinyint(1) NOT NULL,
  `email_active` tinyint(1) NOT NULL,
  `sms_active` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `company_campaign_delivery_module_contacts_module_id_index` (`module_id`),
  KEY `company_campaign_delivery_module_contacts_contact_id_index` (`contact_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_campaign_delivery_module_crms`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_campaign_delivery_module_crms` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `module_id` bigint unsigned NOT NULL,
  `crm_type` int NOT NULL,
  `active` tinyint(1) NOT NULL,
  `display_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` json NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `company_campaign_delivery_module_crms_module_id_index` (`module_id`),
  KEY `company_campaign_delivery_module_crms_crm_type_index` (`crm_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_campaign_delivery_modules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_campaign_delivery_modules` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_campaign_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `company_campaign_delivery_modules_company_campaign_id_index` (`company_campaign_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_campaign_location_module_locations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_campaign_location_module_locations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `module_id` bigint unsigned NOT NULL,
  `location_id` bigint unsigned NOT NULL,
  `zip_code` char(5) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `company_campaign_location_module_locations_module_id_index` (`module_id`),
  KEY `company_campaign_location_module_locations_location_id_index` (`location_id`),
  KEY `company_campaign_location_module_locations_zip_code_index` (`zip_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_campaign_location_modules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_campaign_location_modules` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_campaign_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `company_campaign_location_modules_company_campaign_id_index` (`company_campaign_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_campaign_property_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_campaign_property_types` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_campaign_id` bigint unsigned NOT NULL,
  `property_type_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_campaign_relations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_campaign_relations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_campaign_id` bigint unsigned NOT NULL,
  `relation_id` bigint unsigned NOT NULL,
  `relation_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `company_campaign_relations_relation_id_relation_type_index` (`relation_id`,`relation_type`),
  KEY `company_campaign_relations_company_campaign_id_index` (`company_campaign_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_campaigns`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_campaigns` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_id` bigint unsigned NOT NULL,
  `product_id` bigint unsigned NOT NULL,
  `service_id` bigint unsigned NOT NULL,
  `type` int NOT NULL,
  `status` int NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `reference` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `maximum_budget_usage` int unsigned NOT NULL DEFAULT '115',
  PRIMARY KEY (`id`),
  KEY `company_campaigns_company_id_index` (`company_id`),
  KEY `company_campaigns_product_id_index` (`product_id`),
  KEY `company_campaigns_service_id_index` (`service_id`),
  KEY `company_campaigns_type_index` (`type`),
  KEY `company_campaigns_status_index` (`status`),
  KEY `company_campaigns_deleted_at_index` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_change_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_change_logs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_id` bigint unsigned NOT NULL,
  `log` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` json NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `company_id_idx` (`company_id`),
  KEY `company_log_idx` (`company_id`,`log`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_configurations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_configurations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_id` bigint unsigned NOT NULL,
  `allow_leads_no_cc` tinyint(1) NOT NULL DEFAULT '0',
  `enable_tcpa_playback` tinyint(1) NOT NULL DEFAULT '0',
  `never_exceed_budget` tinyint(1) NOT NULL DEFAULT '0',
  `disallow_ranking` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `receive_off_hour_leads` tinyint(1) NOT NULL,
  `appointments_active` tinyint(1) NOT NULL,
  `require_appointments_calendar` tinyint(1) NOT NULL DEFAULT '0',
  `missed_products_active` tinyint(1) NOT NULL DEFAULT '0',
  `mi_appointments_active` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `appointments_idx` (`appointments_active`,`require_appointments_calendar`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_contracts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_contracts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `company_id` bigint unsigned NOT NULL,
  `company_user_id` bigint unsigned NOT NULL,
  `contract` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `contract_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `agreed_at` timestamp NULL DEFAULT NULL,
  `ip_address` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `uuid` char(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_data` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_id` bigint unsigned NOT NULL,
  `payload` json NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_expert_reviews`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_expert_reviews` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL,
  `legacy_id` int DEFAULT NULL,
  `user_id` int DEFAULT NULL,
  `legacy_user_id` int DEFAULT NULL,
  `body` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_external_reviews`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_external_reviews` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_location_id` int NOT NULL,
  `reference` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `agg_count` int NOT NULL,
  `agg_value` double NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_industries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_industries` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `industry_id` bigint unsigned NOT NULL,
  `company_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `company_industries_company_id_industry_id_unique` (`company_id`,`industry_id`),
  KEY `company_industries_company_id_idx` (`company_id`),
  KEY `company_industries_industry_id_idx` (`industry_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_industry_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_industry_types` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `industry_type_id` bigint unsigned NOT NULL,
  `company_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_links`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_links` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_id_one` bigint unsigned NOT NULL,
  `company_id_two` bigint unsigned NOT NULL,
  `created_by_user` bigint unsigned NOT NULL,
  `comment` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `company_links_company_id_one_foreign` (`company_id_one`),
  KEY `company_links_company_id_two_foreign` (`company_id_two`),
  KEY `company_links_created_by_user_foreign` (`created_by_user`),
  CONSTRAINT `company_links_company_id_one_foreign` FOREIGN KEY (`company_id_one`) REFERENCES `companies` (`id`),
  CONSTRAINT `company_links_company_id_two_foreign` FOREIGN KEY (`company_id_two`) REFERENCES `companies` (`id`),
  CONSTRAINT `company_links_created_by_user_foreign` FOREIGN KEY (`created_by_user`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_locations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_locations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `reference` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `company_id` bigint unsigned NOT NULL,
  `address_id` bigint unsigned NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `imported` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `is_primary` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `company_locations_company_id_index` (`company_id`),
  KEY `company_locations_phone_index` (`phone`),
  KEY `company_locations_company_id_address_id_index` (`company_id`,`address_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_media_assets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_media_assets` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_id` bigint unsigned NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `company_media_assets_company_id_index` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_metrics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_metrics` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_id` bigint unsigned NOT NULL,
  `source` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `request_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `request_url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `request_response` json NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_quality_score_industry_configurations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_quality_score_industry_configurations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `global` tinyint(1) NOT NULL DEFAULT '0',
  `industry_id` bigint unsigned DEFAULT NULL,
  `company_quality_score_rule_id` bigint unsigned DEFAULT NULL,
  `test_company_ids` json DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_quality_score_rules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_quality_score_rules` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `industry_id` bigint unsigned DEFAULT NULL,
  `is_production` tinyint(1) NOT NULL,
  `total_points` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `rule_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `company_quality_score_rules_industry_id_foreign` (`industry_id`),
  KEY `company_quality_score_rules_rule_id_foreign` (`rule_id`),
  CONSTRAINT `company_quality_score_rules_industry_id_foreign` FOREIGN KEY (`industry_id`) REFERENCES `industries` (`id`),
  CONSTRAINT `company_quality_score_rules_rule_id_foreign` FOREIGN KEY (`rule_id`) REFERENCES `rulesets` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_review_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_review_data` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_review_id` int NOT NULL,
  `payload` json NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_review_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_review_logs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `rel_id` int NOT NULL,
  `rel_type` int NOT NULL,
  `type` int NOT NULL,
  `value` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_review_responses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_review_responses` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `review_id` int NOT NULL,
  `user_id` int DEFAULT NULL,
  `body` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `first_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `last_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ip_address` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_review_type_fields`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_review_type_fields` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `global_type_id` int NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` int NOT NULL,
  `payload` json NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_reviews`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_reviews` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `legacy_id` int DEFAULT NULL,
  `company_id` int NOT NULL,
  `rel_id` int NOT NULL,
  `consumer_id` int DEFAULT NULL,
  `rel_type` int NOT NULL,
  `first_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email_validated` tinyint(1) NOT NULL DEFAULT '0',
  `phone_validated` tinyint(1) NOT NULL DEFAULT '0',
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `body` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `overall_score` double(8,2) NOT NULL,
  `status` int NOT NULL,
  `ip_address` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_services`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_services` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `industry_service_id` bigint unsigned NOT NULL,
  `company_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `company_services_company_id_idx` (`company_id`),
  KEY `company_services_industry_service_id_idx` (`industry_service_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_slugs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_slugs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_id` bigint unsigned NOT NULL,
  `slug` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `redirect_company_slug_id` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `company_slugs_slug_unique` (`slug`),
  KEY `company_slugs_company_id_index` (`company_id`),
  KEY `company_slugs_redirect_company_slug_id_index` (`redirect_company_slug_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_user_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_user_data` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_user_id` bigint unsigned NOT NULL,
  `payload` json NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_user_fields`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_user_fields` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` int NOT NULL,
  `hidden` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_user_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_user_logs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_user_id` bigint unsigned NOT NULL,
  `ip_address` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `legacy_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `reference` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` int NOT NULL,
  `company_id` bigint unsigned NOT NULL,
  `first_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `department` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `cell_phone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `formatted_cell_phone` bigint unsigned DEFAULT NULL,
  `office_phone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `formatted_office_phone` bigint unsigned DEFAULT NULL,
  `zoom_info_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `can_log_in` tinyint(1) NOT NULL DEFAULT '0',
  `password` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `failed_login_count` tinyint NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `is_contact` tinyint(1) NOT NULL DEFAULT '0',
  `imported` tinyint(1) NOT NULL DEFAULT '0',
  `scheduling_user_reference` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `phone_verified_at` timestamp NULL DEFAULT NULL,
  `authentication_type` tinyint NOT NULL DEFAULT '1',
  `pinned` tinyint(1) NOT NULL DEFAULT '0',
  `is_decision_maker` tinyint(1) NOT NULL DEFAULT '0',
  `can_receive_promotions` tinyint(1) NOT NULL DEFAULT '1',
  `created_by_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `updated_by_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `unsubscribed_from_promotions` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `company_users_company_id_index` (`company_id`),
  KEY `fcp_index` (`formatted_cell_phone`),
  KEY `fop_index` (`formatted_office_phone`),
  KEY `fcpfop_index` (`formatted_cell_phone`,`formatted_office_phone`),
  KEY `company_users_cell_phone_index` (`cell_phone`),
  KEY `company_users_office_phone_index` (`office_phone`),
  KEY `company_users_email_index` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `completed_workflows`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `completed_workflows` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `workflow_id` bigint unsigned NOT NULL,
  `payload` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `running_workflow_id` bigint unsigned DEFAULT NULL,
  `event_lead_id` int GENERATED ALWAYS AS (json_unquote(json_extract(`payload`,_utf8mb4'$.event.event_data.lead_id'))) VIRTUAL,
  PRIMARY KEY (`id`),
  KEY `completed_workflows_workflow_id_index` (`workflow_id`),
  KEY `completed_workflows_running_workflow_id_index` (`running_workflow_id`),
  KEY `completed_workflows_event_lead_id_index` (`event_lead_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `computed_campaign_statistics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `computed_campaign_statistics` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `legacy_company_id` bigint unsigned DEFAULT NULL,
  `product_campaign_id` bigint unsigned NOT NULL,
  `budget_usage` double NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `computed_campaign_statistics_product_campaign_id_foreign` (`product_campaign_id`),
  CONSTRAINT `computed_campaign_statistics_product_campaign_id_foreign` FOREIGN KEY (`product_campaign_id`) REFERENCES `product_campaigns` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `computed_rejection_statistics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `computed_rejection_statistics` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_id` bigint unsigned NOT NULL,
  `legacy_company_id` bigint unsigned DEFAULT NULL,
  `product_id` bigint unsigned NOT NULL,
  `overall_rejection_percentage` double NOT NULL DEFAULT '0',
  `manual_rejection_percentage` double NOT NULL DEFAULT '0',
  `crm_rejection_percentage` double NOT NULL DEFAULT '0',
  `assigned_product_cost` int NOT NULL DEFAULT '0',
  `manual_rejected_product_cost` int NOT NULL DEFAULT '0',
  `crm_rejected_product_cost` int NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `crm_reset_time` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_company_product` (`company_id`,`product_id`),
  KEY `computed_rejection_statistics_product_id_foreign` (`product_id`),
  KEY `idx_legacy_company_id` (`legacy_company_id`),
  CONSTRAINT `computed_rejection_statistics_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `computed_rejection_statistics_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `configurable_field_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `configurable_field_types` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `label` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `consumer_common_fields`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `consumer_common_fields` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `category` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `consumer_configurable_field_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `consumer_configurable_field_categories` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `consumer_configurable_field_categories_name_unique` (`name`),
  UNIQUE KEY `consumer_configurable_field_categories_slug_unique` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `consumer_field_module_visibility`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `consumer_field_module_visibility` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `consumer_field_category` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `consumer_field_category_id` bigint NOT NULL,
  `consumer_field_id` bigint NOT NULL,
  `consumer_field_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `module_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `feature_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_visible` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `consumer_field_category_category_id_idx` (`consumer_field_category`,`consumer_field_category_id`),
  KEY `module_feature_idx` (`module_type`,`feature_type`),
  KEY `consumer_field_id_idx` (`consumer_field_id`,`consumer_field_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `consumer_product_affiliate_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `consumer_product_affiliate_records` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `affiliate_id` bigint unsigned DEFAULT NULL,
  `campaign_id` bigint unsigned DEFAULT NULL,
  `track_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `track_code` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `consumer_product_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `consumer_product_data` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `payload` json NOT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci GENERATED ALWAYS AS (json_unquote(json_extract(`payload`,_utf8mb4'$.ip_address'))) STORED,
  `other_interests` varchar(255) COLLATE utf8mb4_unicode_ci GENERATED ALWAYS AS (json_unquote(json_extract(`payload`,_utf8mb4'$.other_interests'))) VIRTUAL,
  PRIMARY KEY (`id`),
  KEY `consumer_product_data_ip_address_index` (`ip_address`),
  KEY `idx_id_ip_address` (`id`,`ip_address`),
  KEY `consumer_product_data_other_interests_index` (`other_interests`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `consumer_product_tcpa_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `consumer_product_tcpa_records` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `tcpa_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tcpa_service_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `consumer_product_tracking`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `consumer_product_tracking` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `legacy_id` bigint unsigned NOT NULL,
  `url_start` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `url_convert` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `calculator_source` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `website_id` bigint unsigned DEFAULT NULL,
  `ad_track_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ad_track_code` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `consumer_product_tracking_website_id_index` (`website_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `consumer_products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `consumer_products` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `consumer_id` bigint unsigned NOT NULL,
  `service_product_id` bigint unsigned NOT NULL,
  `address_id` bigint unsigned NOT NULL,
  `good_to_sell` tinyint(1) NOT NULL DEFAULT '0',
  `status` int NOT NULL,
  `contact_requests` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `consumer_product_data_id` bigint unsigned DEFAULT NULL,
  `consumer_product_tcpa_record_id` bigint unsigned DEFAULT NULL,
  `consumer_product_tracking_id` bigint unsigned DEFAULT NULL,
  `consumer_product_affiliate_record_id` bigint unsigned DEFAULT NULL,
  `property_type_id` bigint unsigned NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `consumer_products_consumer_id_index` (`consumer_id`),
  KEY `cpd_idx` (`consumer_product_data_id`),
  KEY `consumer_products_address_id_index` (`address_id`),
  KEY `idx_spi_c` (`service_product_id`,`created_at`),
  KEY `created_at_idx` (`created_at`),
  KEY `consumer_products_consumer_product_tracking_id_index` (`consumer_product_tracking_id`),
  KEY `consumer_products_service_product_id_index` (`service_product_id`),
  KEY `creat_id_cpd_idx` (`created_at`,`consumer_id`,`consumer_product_data_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `consumers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `consumers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `legacy_id` int DEFAULT NULL,
  `reference` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `website_id` bigint unsigned NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `formatted_phone` char(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `first_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` int NOT NULL,
  `classification` int NOT NULL,
  `status_reason` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `max_contact_requests` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `consumers_legacy_id_index` (`legacy_id`),
  KEY `consumers_id_index` (`id`),
  KEY `consumers_email_index` (`email`),
  KEY `consumers_reference_index` (`reference`),
  KEY `consumers_formatted_phone_index` (`formatted_phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `contact_subscriptions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `contact_subscriptions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `contact_id` bigint unsigned NOT NULL,
  `contact_type` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `contact_method` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `notification_type` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `unsubscribed` bigint NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `dashboard_login_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dashboard_login_tokens` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_id` bigint unsigned NOT NULL,
  `company_user_id` bigint unsigned NOT NULL,
  `token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `expired` tinyint(1) NOT NULL DEFAULT '0',
  `expires_at` timestamp NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `dashboard_login_tokens_company_id_index` (`company_id`),
  KEY `dashboard_login_tokens_company_user_id_index` (`company_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `email_template_backgrounds`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `email_template_backgrounds` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `owner_user_id` bigint unsigned NOT NULL,
  `industry_id` bigint unsigned DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `header` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `footer` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `personal` tinyint(1) NOT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `email_template_backgrounds_owner_user_id_foreign` (`owner_user_id`),
  KEY `email_template_backgrounds_industry_id_foreign` (`industry_id`),
  CONSTRAINT `email_template_backgrounds_industry_id_foreign` FOREIGN KEY (`industry_id`) REFERENCES `industries` (`id`),
  CONSTRAINT `email_template_backgrounds_owner_user_id_foreign` FOREIGN KEY (`owner_user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `email_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `email_templates` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `owner_user_id` bigint unsigned NOT NULL,
  `background_id` bigint unsigned DEFAULT NULL,
  `industry_id` bigint unsigned DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `subject` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `personal` tinyint(1) NOT NULL DEFAULT '1',
  `default_lead_delivery_template` tinyint(1) NOT NULL DEFAULT '0',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `email_templates_background_id_foreign` (`background_id`),
  KEY `email_templates_industry_id_foreign` (`industry_id`),
  CONSTRAINT `email_templates_background_id_foreign` FOREIGN KEY (`background_id`) REFERENCES `email_template_backgrounds` (`id`),
  CONSTRAINT `email_templates_industry_id_foreign` FOREIGN KEY (`industry_id`) REFERENCES `industries` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `emails`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `emails` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `to_address` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `from_address` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `subject` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `body` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `to_company_user_id` int unsigned DEFAULT NULL,
  `from_user_id` int unsigned DEFAULT NULL,
  `company_cadence_group_action_id` int unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `estimated_revenue_per_lead_by_locations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `estimated_revenue_per_lead_by_locations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `location_id` int NOT NULL,
  `estimated_revenue` double(8,2) NOT NULL DEFAULT '0.00',
  `available_companies` int NOT NULL,
  `future_available_no_limit_campaigns` int NOT NULL,
  `industry_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `estimated_revenue_per_lead_by_locations_location_id_index` (`location_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `failed_jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `failed_jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `floor_price_formulas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `floor_price_formulas` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `quality_tier` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `sales_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `product` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `industry` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `property_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `location_id` int NOT NULL,
  `multiplier_value` double(8,2) NOT NULL,
  `multiplier_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `multiplier_quality_tier` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `multiplier_sales_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `future_available_budgets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `future_available_budgets` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `location_id` int NOT NULL,
  `county_location_id` int DEFAULT NULL,
  `available_no_limit_campaign_count` int NOT NULL DEFAULT '0',
  `industry_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `future_available_budgets_location_id_index` (`location_id`),
  KEY `future_available_budgets_county_location_id_index` (`county_location_id`),
  KEY `future_available_budgets_industry_id_index` (`industry_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `future_available_company_by_locations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `future_available_company_by_locations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL,
  `legacy_company_id` int DEFAULT NULL,
  `location_id` int NOT NULL,
  `county_location_id` int DEFAULT NULL,
  `industry_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `future_available_company_by_locations_county_location_id_index` (`county_location_id`),
  KEY `future_available_company_by_locations_industry_id_index` (`industry_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `generic_profitability_assumption_configurations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `generic_profitability_assumption_configurations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `percentage_leads_successful` smallint NOT NULL DEFAULT '0',
  `average_lead_revenue` int NOT NULL DEFAULT '0',
  `average_job_cost` int NOT NULL DEFAULT '0',
  `labour_materials_cost` int NOT NULL DEFAULT '0',
  `company_id` bigint unsigned DEFAULT NULL,
  `industry_service_id` bigint unsigned NOT NULL,
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `global_company_fields`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `global_company_fields` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` int NOT NULL,
  `show_on_profile` tinyint(1) NOT NULL DEFAULT '0',
  `show_on_dashboard` tinyint(1) NOT NULL DEFAULT '0',
  `payload` json NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `category` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Basic Info',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `global_company_review_fields`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `global_company_review_fields` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` int NOT NULL,
  `payload` json NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `global_configurations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `global_configurations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `configuration_key` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `configuration_payload` json NOT NULL,
  `created_by_id` bigint unsigned NOT NULL,
  `updated_by_id` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `global_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `global_types` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `google_ads_geo_targets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `google_ads_geo_targets` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `location_id` bigint unsigned NOT NULL,
  `criteria_id` int NOT NULL,
  `parent_id` int NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `canonical_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `target_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `version_date` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `historical_available_campaign_budgets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `historical_available_campaign_budgets` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `budget_available_dollars` double(8,2) NOT NULL DEFAULT '0.00',
  `budget_available_volume` int NOT NULL DEFAULT '0',
  `budget_available_dollars_omit_rejection` double(8,2) NOT NULL DEFAULT '0.00',
  `budget_available_volume_omit_rejection` int NOT NULL DEFAULT '0',
  `location_id` int NOT NULL,
  `campaign_id` int NOT NULL,
  `unlimited` tinyint(1) NOT NULL DEFAULT '0',
  `budget_type` tinyint NOT NULL,
  `industry_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `historical_available_campaign_budgets_location_id_index` (`location_id`),
  KEY `historical_available_campaign_budgets_campaign_id_index` (`campaign_id`),
  KEY `idx_type_budget_time` (`industry_type`,`budget_type`,`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `historical_available_location_budgets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `historical_available_location_budgets` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `budget_available_dollars` double(8,2) NOT NULL DEFAULT '0.00',
  `budget_available_volume` int NOT NULL DEFAULT '0',
  `budget_available_dollars_omit_rejection` double(8,2) NOT NULL DEFAULT '0.00',
  `budget_available_volume_omit_rejection` int NOT NULL DEFAULT '0',
  `available_campaign_count` int NOT NULL DEFAULT '0',
  `unlimited_budget_count` int NOT NULL DEFAULT '0',
  `budget_type` tinyint NOT NULL,
  `industry_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `location_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `historical_available_location_budgets_location_id_index` (`location_id`),
  KEY `idx_loc_type` (`location_id`,`industry_type`),
  KEY `idx_loc_type_budget` (`location_id`,`industry_type`,`budget_type`),
  KEY `idx_loc_type_budget_time` (`location_id`,`industry_type`,`budget_type`,`created_at`),
  KEY `historical_available_location_budgets_created_at_index` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `historical_company_rejection_percentages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `historical_company_rejection_percentages` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL,
  `year` int NOT NULL,
  `product_id` bigint unsigned NOT NULL,
  `rejection_percentages` json NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `historical_company_rejection_percentages_company_id_index` (`company_id`),
  KEY `company_year_idx` (`company_id`,`year`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `historical_company_revenue`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `historical_company_revenue` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_id` bigint unsigned NOT NULL,
  `year` int NOT NULL,
  `daily_revenue` json NOT NULL,
  `monthly_revenue` json NOT NULL,
  `yearly_revenue` decimal(13,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `company_year_idx` (`company_id`,`year`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `historical_company_sales_status`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `historical_company_sales_status` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_id` bigint unsigned NOT NULL,
  `year` int NOT NULL,
  `sales_status` json NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `company_year_idx` (`company_id`,`year`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `historical_company_statuses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `historical_company_statuses` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_id` bigint unsigned NOT NULL,
  `year` int NOT NULL,
  `daily_statuses` json NOT NULL,
  `monthly_statuses` json NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `company_year_idx` (`company_id`,`year`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `hunter_industries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `hunter_industries` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `hunter_id` bigint unsigned NOT NULL,
  `industry` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `hunter_states`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `hunter_states` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `hunter_id` bigint unsigned NOT NULL,
  `state_abbr` varchar(2) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `hunters`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `hunters` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `include_in_round_robin` tinyint(1) NOT NULL DEFAULT '0',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `identified_contacts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `identified_contacts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `identification_status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `identifier_value` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `identifier_field_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `nominated_contact_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `identified_contacts_identifier_value_index` (`identifier_value`),
  KEY `identified_contacts_identifier_field_type_index` (`identifier_field_type`),
  KEY `identified_contacts_nominated_contact_id_foreign` (`nominated_contact_id`),
  CONSTRAINT `identified_contacts_nominated_contact_id_foreign` FOREIGN KEY (`nominated_contact_id`) REFERENCES `possible_contacts` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `impersonation_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `impersonation_logs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `impersonating_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `industries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `industries` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `light_mode_color` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `dark_mode_color` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `delivery_email_template_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `industries_name_unique` (`name`),
  KEY `industries_name_idx` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `industry_company_fields`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `industry_company_fields` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `industry_id` bigint unsigned NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` int NOT NULL,
  `show_on_profile` tinyint(1) NOT NULL DEFAULT '0',
  `show_on_dashboard` tinyint(1) NOT NULL DEFAULT '0',
  `payload` json NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `category` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Basic Info',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `industry_company_review_fields`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `industry_company_review_fields` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `industry_id` int NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` int NOT NULL,
  `payload` json NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `industry_configurations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `industry_configurations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `industry_id` bigint unsigned NOT NULL,
  `future_campaigns_active` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `industry_consumer_fields`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `industry_consumer_fields` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `industry_id` bigint unsigned NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` int NOT NULL,
  `send_to_company` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `category_id` bigint unsigned NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `industry_consumer_fields_category_id_foreign` (`category_id`),
  CONSTRAINT `industry_consumer_fields_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `consumer_configurable_field_categories` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `industry_products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `industry_products` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `industry_id` bigint unsigned NOT NULL,
  `product_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `industry_services`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `industry_services` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `industry_id` bigint unsigned NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `show_on_website` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `industry_services_industry_id_index` (`industry_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `industry_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `industry_types` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `global_type_id` bigint unsigned NOT NULL,
  `industry_id` bigint unsigned NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `industry_websites`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `industry_websites` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `industry_id` bigint unsigned NOT NULL,
  `website_id` bigint unsigned NOT NULL,
  `slug` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `job_batches`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `job_batches` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `total_jobs` int NOT NULL,
  `pending_jobs` int NOT NULL,
  `failed_jobs` int NOT NULL,
  `failed_job_ids` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `options` mediumtext COLLATE utf8mb4_unicode_ci,
  `cancelled_at` int DEFAULT NULL,
  `created_at` int NOT NULL,
  `finished_at` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `job_tracking`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `job_tracking` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `relation` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `relation_id` bigint unsigned NOT NULL,
  `job_uuid` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` int NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `queue` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `attempts` tinyint unsigned NOT NULL,
  `reserved_at` int unsigned DEFAULT NULL,
  `available_at` int unsigned NOT NULL,
  `created_at` int unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `jobs_queue_index` (`queue`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `lead_processing_allocations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lead_processing_allocations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `lead_id` int NOT NULL,
  `delivered` tinyint(1) NOT NULL,
  `queue_configurations_id` int NOT NULL,
  `lead_processor_id` int NOT NULL,
  `deliver_at` datetime NOT NULL,
  `processing_scenario` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `final_attempt` tinyint(1) NOT NULL DEFAULT '0',
  `consumer_product_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `lead_processing_budget_statuses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lead_processing_budget_statuses` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `priority` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `lead_processing_calling_time_zone_configurations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lead_processing_calling_time_zone_configurations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `standard_utc_offset` int NOT NULL,
  `local_open_hour` int unsigned NOT NULL,
  `local_close_hour` int unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `lead_processing_communications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lead_processing_communications` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `lead_id` bigint unsigned DEFAULT NULL,
  `lead_processor_id` bigint unsigned NOT NULL,
  `relation_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `lead_processing_communications_lead_id_index` (`lead_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `lead_processing_configurations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lead_processing_configurations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `time_zone_opening_delay_in_minutes` int NOT NULL,
  `recency_threshold_in_seconds` int NOT NULL,
  `minimum_review_time` int NOT NULL DEFAULT '5',
  `lead_processable_delay_seconds` int NOT NULL DEFAULT '60',
  `check_next_lead_interval_seconds` int NOT NULL DEFAULT '60',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `last_lead_created_interval_min` int NOT NULL,
  `next_lead_logging_team_id` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `lead_processing_constraints`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lead_processing_constraints` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `lead_processing_failed_leads`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lead_processing_failed_leads` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `lead_id` int NOT NULL,
  `reason` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `errors` json NOT NULL,
  `lead_processor_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `consumer_product_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `lead_processing_heartbeats`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lead_processing_heartbeats` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `lead_id` int NOT NULL,
  `lead_processor_id` int NOT NULL,
  `last_heartbeat` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `consumer_product_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `lead_processing_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lead_processing_history` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `lead_id` int NOT NULL,
  `lead_processor_id` int NOT NULL,
  `queue_configuration_id` int NOT NULL,
  `action` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `consumer_product_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `lead_processing_initials`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lead_processing_initials` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `lead_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `consumer_product_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `lead_processing_initials_lead_id_index` (`lead_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `lead_processing_pending_reviews`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lead_processing_pending_reviews` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `lead_id` bigint unsigned NOT NULL,
  `reason` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `lead_processor_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `consumer_product_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `lead_processing_pending_reviews_consumer_product_id_index` (`consumer_product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `lead_processing_queue_configurations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lead_processing_queue_configurations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `primary_status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_round` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `lead_processing_queue_constraints`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lead_processing_queue_constraints` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `queue_id` bigint unsigned NOT NULL,
  `constraint_id` bigint unsigned NOT NULL,
  `order` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `lead_processing_queue_constraints_queue_id_foreign` (`queue_id`),
  KEY `lead_processing_queue_constraints_constraint_id_foreign` (`constraint_id`),
  CONSTRAINT `lead_processing_queue_constraints_constraint_id_foreign` FOREIGN KEY (`constraint_id`) REFERENCES `lead_processing_constraints` (`id`),
  CONSTRAINT `lead_processing_queue_constraints_queue_id_foreign` FOREIGN KEY (`queue_id`) REFERENCES `lead_processing_queue_configurations` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `lead_processing_queue_constraints_bucket_flags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lead_processing_queue_constraints_bucket_flags` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `lead_id` bigint unsigned NOT NULL,
  `budget_status_id` bigint unsigned NOT NULL,
  `timeframe_id` bigint unsigned NOT NULL,
  `super_premium` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `consumer_product_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `lead_id_idx` (`lead_id`),
  KEY `timeframe_id_idx` (`timeframe_id`),
  KEY `budget_status_id_idx` (`budget_status_id`),
  KEY `cpi_index` (`consumer_product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `lead_processing_reserved_leads`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lead_processing_reserved_leads` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `lead_id` int NOT NULL,
  `processor_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `consumer_product_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `lead_processing_reserved_leads_lead_id_index` (`lead_id`),
  KEY `lead_processing_reserved_leads_processor_id_index` (`processor_id`),
  KEY `lead_processing_reserved_leads_lead_id_processor_id_index` (`lead_id`,`processor_id`),
  KEY `lead_processing_reserved_leads_consumer_product_id_index` (`consumer_product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `lead_processing_team_industries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lead_processing_team_industries` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `lead_processing_team_id` bigint unsigned NOT NULL,
  `industry_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `lead_processing_team_industries_lead_processing_team_id_index` (`lead_processing_team_id`),
  KEY `lead_processing_team_industries_industry_id_index` (`industry_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `lead_processing_teams`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lead_processing_teams` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `primary_queue_configuration_id` bigint unsigned NOT NULL,
  `primary_utc_offset` int NOT NULL,
  `industry_id` bigint unsigned DEFAULT NULL,
  `industry_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `lead_processing_time_zone_configurations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lead_processing_time_zone_configurations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `standard_utc_offset` int NOT NULL,
  `local_open_hour` int unsigned NOT NULL,
  `local_close_hour` int unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `lead_processing_timeframes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lead_processing_timeframes` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `priority` tinyint NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `lead_processing_under_reviews`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lead_processing_under_reviews` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `lead_id` int DEFAULT NULL,
  `reason` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `lead_processor_id` bigint unsigned NOT NULL,
  `existing_reason` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `location_id` int DEFAULT NULL,
  `possible_revenue` double(8,2) DEFAULT NULL,
  `checked_unverified_budget_at` timestamp NULL DEFAULT NULL,
  `consumer_product_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `lead_processing_under_reviews_lead_id_unique` (`lead_id`),
  KEY `lead_processing_under_reviews_lead_processor_id_foreign` (`lead_processor_id`),
  KEY `lead_processing_under_reviews_location_id_index` (`location_id`),
  KEY `lead_processing_under_reviews_lead_id_index` (`lead_id`),
  KEY `lead_processing_under_reviews_consumer_product_id_index` (`consumer_product_id`),
  CONSTRAINT `lead_processing_under_reviews_lead_processor_id_foreign` FOREIGN KEY (`lead_processor_id`) REFERENCES `lead_processors` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `lead_processor_notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lead_processor_notifications` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `lead_processor_id` bigint unsigned NOT NULL,
  `lead_id` bigint unsigned DEFAULT NULL,
  `subject` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `body` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `read` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `lead_processors`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lead_processors` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `lead_processing_team_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `lead_processors_user_id_foreign` (`user_id`),
  KEY `lead_processors_lead_processing_team_id_foreign` (`lead_processing_team_id`),
  CONSTRAINT `lead_processors_lead_processing_team_id_foreign` FOREIGN KEY (`lead_processing_team_id`) REFERENCES `lead_processing_teams` (`id`),
  CONSTRAINT `lead_processors_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `licenses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `licenses` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `company_id` bigint unsigned NOT NULL,
  `industry_id` bigint unsigned DEFAULT NULL,
  `issued_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `country` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `state` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `license_number` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `location_silo_pages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `location_silo_pages` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `parent_location_id` int unsigned DEFAULT NULL,
  `location_id` int unsigned DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `entry_slug` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `silo_id` int unsigned NOT NULL,
  `location_type` int NOT NULL,
  `relative_path` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_location_id` (`location_id`),
  KEY `idx_parent_location_id` (`parent_location_id`),
  KEY `location_silo_pages_silo_id_index` (`silo_id`),
  KEY `location_silo_pages_location_type_index` (`location_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `locked_advertising_campaign_locations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `locked_advertising_campaign_locations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `platform` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `platform_account_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `platform_campaign_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `location_id` bigint unsigned NOT NULL,
  `targeted` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `mailbox_email_attachments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mailbox_email_attachments` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `email_id` bigint unsigned DEFAULT NULL,
  `external_id` text COLLATE utf8mb4_unicode_ci,
  `external_attachment_id` text COLLATE utf8mb4_unicode_ci,
  `external_filename` text COLLATE utf8mb4_unicode_ci,
  `external_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `url` text COLLATE utf8mb4_unicode_ci,
  `size` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `mailbox_email_attachments_email_id_foreign` (`email_id`),
  CONSTRAINT `mailbox_email_attachments_email_id_foreign` FOREIGN KEY (`email_id`) REFERENCES `mailbox_emails` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `mailbox_email_labels`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mailbox_email_labels` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `email_id` bigint unsigned NOT NULL,
  `label_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `mailbox_email_labels_email_id_foreign` (`email_id`),
  KEY `mailbox_email_labels_label_id_foreign` (`label_id`),
  CONSTRAINT `mailbox_email_labels_email_id_foreign` FOREIGN KEY (`email_id`) REFERENCES `mailbox_emails` (`id`) ON DELETE CASCADE,
  CONSTRAINT `mailbox_email_labels_label_id_foreign` FOREIGN KEY (`label_id`) REFERENCES `mailbox_user_labels` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `mailbox_email_recipients`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mailbox_email_recipients` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `email_id` bigint unsigned DEFAULT NULL,
  `email_address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `type` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `identified_contact_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `mailbox_email_recipients_email_id_foreign` (`email_id`),
  KEY `mailbox_email_recipients_identified_contact_id_foreign` (`identified_contact_id`),
  CONSTRAINT `mailbox_email_recipients_email_id_foreign` FOREIGN KEY (`email_id`) REFERENCES `mailbox_emails` (`id`) ON DELETE CASCADE,
  CONSTRAINT `mailbox_email_recipients_identified_contact_id_foreign` FOREIGN KEY (`identified_contact_id`) REFERENCES `identified_contacts` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `mailbox_emails`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mailbox_emails` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `from_user_id` bigint unsigned DEFAULT NULL,
  `from_user_email` text COLLATE utf8mb4_unicode_ci,
  `direction` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `subject` longtext COLLATE utf8mb4_unicode_ci,
  `snippet` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT 'send',
  `external_message_id` text COLLATE utf8mb4_unicode_ci,
  `content` longtext COLLATE utf8mb4_unicode_ci,
  `from_identified_contact_id` bigint unsigned DEFAULT NULL,
  `from_a20` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `mailbox_emails_from_user_id_foreign` (`from_user_id`),
  KEY `mailbox_emails_from_identified_contact_id_foreign` (`from_identified_contact_id`),
  CONSTRAINT `mailbox_emails_from_identified_contact_id_foreign` FOREIGN KEY (`from_identified_contact_id`) REFERENCES `identified_contacts` (`id`) ON DELETE SET NULL,
  CONSTRAINT `mailbox_emails_from_user_id_foreign` FOREIGN KEY (`from_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `mailbox_user_email_histories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mailbox_user_email_histories` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `history_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `mailbox_user_email_histories_user_id_foreign` (`user_id`),
  CONSTRAINT `mailbox_user_email_histories_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `mailbox_user_email_listeners`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mailbox_user_email_listeners` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `expires_at` timestamp NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `mailbox_user_email_listeners_user_id_foreign` (`user_id`),
  CONSTRAINT `mailbox_user_email_listeners_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `mailbox_user_emails`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mailbox_user_emails` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `email_id` bigint unsigned NOT NULL,
  `is_inbox` tinyint(1) NOT NULL DEFAULT '0',
  `is_sent` tinyint(1) NOT NULL DEFAULT '0',
  `is_starred` tinyint(1) NOT NULL DEFAULT '0',
  `is_important` tinyint(1) NOT NULL DEFAULT '0',
  `is_archived` tinyint(1) NOT NULL DEFAULT '0',
  `is_read` tinyint(1) NOT NULL DEFAULT '0',
  `external_id` text COLLATE utf8mb4_unicode_ci,
  `external_thread_id` text COLLATE utf8mb4_unicode_ci,
  `external_history_id` text COLLATE utf8mb4_unicode_ci,
  `external_references` text COLLATE utf8mb4_unicode_ci,
  `read_at` timestamp NULL DEFAULT NULL,
  `sent_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `uuid` char(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `mailbox_user_emails_user_id_foreign` (`user_id`),
  KEY `mailbox_user_emails_email_id_foreign` (`email_id`),
  CONSTRAINT `mailbox_user_emails_email_id_foreign` FOREIGN KEY (`email_id`) REFERENCES `mailbox_emails` (`id`) ON DELETE CASCADE,
  CONSTRAINT `mailbox_user_emails_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `mailbox_user_labels`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mailbox_user_labels` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `slug` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `external_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `mailbox_user_labels_user_id_foreign` (`user_id`),
  KEY `mailbox_user_labels_slug_index` (`slug`),
  CONSTRAINT `mailbox_user_labels_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `mailbox_user_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mailbox_user_tokens` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `token` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `refresh_token` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `mailbox_user_tokens_user_id_foreign` (`user_id`),
  CONSTRAINT `mailbox_user_tokens_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `meta_ads_locations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `meta_ads_locations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `location_id` bigint unsigned NOT NULL,
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `migrations` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `missed_product_configs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `missed_product_configs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `expiration_quantity` int DEFAULT NULL,
  `expiration_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `notification_cooldown_in_days` int DEFAULT NULL,
  `purchase_fail_email_template_id` bigint unsigned DEFAULT NULL,
  `override_can_receive_promotions` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `missed_products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `missed_products` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `price` double(8,2) NOT NULL,
  `is_reserved` tinyint(1) NOT NULL DEFAULT '0',
  `sellable_legs_count` int NOT NULL,
  `consumer_product_id` bigint unsigned NOT NULL,
  `industry_service_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `missed_products_consumer_product_id_foreign` (`consumer_product_id`),
  KEY `missed_products_industry_service_id_foreign` (`industry_service_id`),
  CONSTRAINT `missed_products_consumer_product_id_foreign` FOREIGN KEY (`consumer_product_id`) REFERENCES `consumer_products` (`id`),
  CONSTRAINT `missed_products_industry_service_id_foreign` FOREIGN KEY (`industry_service_id`) REFERENCES `industry_services` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `model_has_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `model_has_permissions` (
  `permission_id` bigint unsigned NOT NULL,
  `model_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`permission_id`,`model_id`,`model_type`),
  KEY `model_has_permissions_model_id_model_type_index` (`model_id`,`model_type`),
  CONSTRAINT `model_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `model_has_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `model_has_roles` (
  `role_id` bigint unsigned NOT NULL,
  `model_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`role_id`,`model_id`,`model_type`),
  KEY `model_has_roles_model_id_model_type_index` (`model_id`,`model_type`),
  CONSTRAINT `model_has_roles_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `notes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `notes` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `content` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `relation_id` bigint unsigned NOT NULL,
  `relation_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `author_id` bigint unsigned DEFAULT NULL,
  `parent_id` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `notes_author_id_foreign` (`author_id`),
  KEY `notes_parent_id_foreign` (`parent_id`),
  KEY `notes_relation_index` (`relation_id`,`relation_type`),
  CONSTRAINT `notes_author_id_foreign` FOREIGN KEY (`author_id`) REFERENCES `users` (`id`),
  CONSTRAINT `notes_parent_id_foreign` FOREIGN KEY (`parent_id`) REFERENCES `notes` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `notifications` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `from_id` int NOT NULL DEFAULT '0',
  `subject` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `body` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` int NOT NULL DEFAULT '0',
  `link` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `link_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `read` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `payload` json NOT NULL,
  PRIMARY KEY (`id`),
  KEY `notifications_user_id_index` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `opportunity_notification_configs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `opportunity_notification_configs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `rule_id` bigint unsigned DEFAULT NULL,
  `product_type` int NOT NULL,
  `frequency` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `send_day` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `send_time` time NOT NULL,
  `content` longtext COLLATE utf8mb4_unicode_ci,
  `last_sent_at` timestamp NULL DEFAULT NULL,
  `lead_threshold` int DEFAULT NULL,
  `activated_at` timestamp NULL DEFAULT NULL,
  `created_by` bigint unsigned DEFAULT NULL,
  `updated_by` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `email_template_id` bigint unsigned DEFAULT NULL,
  `is_queued` tinyint(1) NOT NULL DEFAULT '0',
  `uuid` char(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `filter_preset_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `opportunity_notification_configs_created_by_foreign` (`created_by`),
  KEY `opportunity_notification_configs_updated_by_foreign` (`updated_by`),
  KEY `opportunity_notification_configs_rule_id_foreign` (`rule_id`),
  KEY `opportunity_notification_configs_email_template_id_foreign` (`email_template_id`),
  KEY `opportunity_notification_configs_product_type_index` (`product_type`),
  KEY `opportunity_notification_configs_filter_preset_id_foreign` (`filter_preset_id`),
  CONSTRAINT `opportunity_notification_configs_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `opportunity_notification_configs_email_template_id_foreign` FOREIGN KEY (`email_template_id`) REFERENCES `email_templates` (`id`),
  CONSTRAINT `opportunity_notification_configs_filter_preset_id_foreign` FOREIGN KEY (`filter_preset_id`) REFERENCES `user_presets` (`id`),
  CONSTRAINT `opportunity_notification_configs_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `opportunity_notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `opportunity_notifications` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `recipients` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `sent_at` timestamp NOT NULL,
  `delivery_method` int NOT NULL,
  `content` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `view_count` int NOT NULL DEFAULT '0',
  `company_id` bigint unsigned DEFAULT NULL,
  `config_id` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `opportunity_notifications_company_id_foreign` (`company_id`),
  KEY `opportunity_notifications_config_id_foreign` (`config_id`),
  CONSTRAINT `opportunity_notifications_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`),
  CONSTRAINT `opportunity_notifications_config_id_foreign` FOREIGN KEY (`config_id`) REFERENCES `opportunity_notification_configs` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `password_resets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `password_resets` (
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  KEY `password_resets_email_index` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `payment_collections`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `payment_collections` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_id` bigint unsigned NOT NULL,
  `paid` tinyint(1) NOT NULL DEFAULT '0',
  `paid_date` timestamp NULL DEFAULT NULL,
  `amount` double(8,2) NOT NULL,
  `notes` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `permissions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `guard_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `permissions_name_guard_name_unique` (`name`,`guard_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `personal_access_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `personal_access_tokens` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint unsigned NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text COLLATE utf8mb4_unicode_ci,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `phones`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `phones` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `phone` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `external_reference` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `external_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `friendly_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `region` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `type` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `possible_contacts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `possible_contacts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `relation_id` bigint unsigned NOT NULL,
  `relation_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `identified_contact_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `possible_contact_relation_identified_unique_index` (`relation_id`,`relation_type`,`identified_contact_id`),
  KEY `possible_contacts_identified_contact_id_foreign` (`identified_contact_id`),
  CONSTRAINT `possible_contacts_identified_contact_id_foreign` FOREIGN KEY (`identified_contact_id`) REFERENCES `identified_contacts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `product_appointments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_appointments` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `legacy_id` bigint unsigned DEFAULT NULL,
  `consumer_product_id` bigint unsigned NOT NULL,
  `lead_consumer_product_id` bigint unsigned NOT NULL,
  `appointment_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `appointment_date` date NOT NULL,
  `appointment_time` time DEFAULT NULL,
  `allocated_as_lead` tinyint(1) NOT NULL DEFAULT '0',
  `original_appointment_id` bigint unsigned DEFAULT NULL,
  `related_appt_sold_and_unsellable` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `product_appointments_consumer_product_id_index` (`consumer_product_id`),
  KEY `product_appointments_appointment_type_index` (`appointment_type`),
  KEY `rasau_idx` (`related_appt_sold_and_unsellable`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `product_assignments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_assignments` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `legacy_id` int DEFAULT NULL,
  `company_id` bigint unsigned NOT NULL,
  `consumer_product_id` bigint unsigned NOT NULL,
  `cost` double(8,2) NOT NULL,
  `chargeable` tinyint(1) NOT NULL,
  `delivered` tinyint(1) NOT NULL,
  `exclude_budget` int NOT NULL,
  `sale_type_id` int NOT NULL,
  `campaign_id` bigint unsigned NOT NULL,
  `product_campaign_budget_id` bigint unsigned NOT NULL,
  `delivered_at` timestamp NOT NULL,
  `rejection_expiry` timestamp NOT NULL,
  `payload` json DEFAULT NULL,
  `conversion_uploaded` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `off_hour_sale` tinyint(1) NOT NULL,
  `parent_product_id` bigint unsigned DEFAULT NULL,
  `affect_rejection_percentage` bigint unsigned NOT NULL DEFAULT '1',
  `budget_id` int NOT NULL DEFAULT '0',
  `quality_tier_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `product_assignments_legacy_id_index` (`legacy_id`),
  KEY `product_assignments_consumer_product_id_index` (`consumer_product_id`),
  KEY `product_assignments_company_id_idx` (`company_id`),
  KEY `product_assignments_product_campaign_budget_id_idx` (`product_campaign_budget_id`),
  KEY `product_assignments_sold_idx` (`chargeable`,`delivered`,`delivered_at`,`exclude_budget`),
  KEY `idx_cpi_ci` (`consumer_product_id`,`company_id`),
  KEY `conversion_uploaded_idx` (`conversion_uploaded`),
  KEY `budget_id_idx` (`budget_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `product_campaign_budgets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_campaign_budgets` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `product_campaign_id` int NOT NULL,
  `quality_tier` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `category` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` double(8,2) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '0',
  `budget_start_timestamp` timestamp NOT NULL,
  `max_budget_usage` int NOT NULL DEFAULT '115',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `product_campaign_budgets_campaign_id_idx` (`product_campaign_id`),
  KEY `product_campaign_budgets_campaign_status_idx` (`product_campaign_id`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `product_campaign_schedules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_campaign_schedules` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `product_campaign_id` bigint unsigned NOT NULL,
  `schedule_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `product_campaign_id_idx` (`product_campaign_id`),
  KEY `schedule_id_idx` (`schedule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `product_campaigns`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_campaigns` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `product_id` bigint unsigned NOT NULL,
  `industry_service_id` bigint unsigned DEFAULT NULL,
  `company_id` bigint unsigned NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '0',
  `reactivate_at_timestamp` int unsigned NOT NULL DEFAULT '0',
  `parent_legacy_lead_campaign_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `product_campaigns_company_id_idx` (`company_id`),
  KEY `product_campaigns_product_id_idx` (`product_id`),
  KEY `product_campaigns_pllc_id_idx` (`parent_legacy_lead_campaign_id`),
  KEY `product_campaigns_service_id_index` (`industry_service_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `product_cancellations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_cancellations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `product_assignment_id` bigint unsigned NOT NULL,
  `reason` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `note` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `product_assignment_id_idx` (`product_assignment_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `product_county_bid_prices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_county_bid_prices` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `county_location_id` bigint unsigned NOT NULL,
  `state_location_id` bigint unsigned NOT NULL,
  `product_campaign_id` bigint unsigned NOT NULL,
  `service_product_id` bigint unsigned NOT NULL,
  `sale_type_id` bigint unsigned NOT NULL,
  `quality_tier_id` bigint unsigned NOT NULL,
  `property_type_id` bigint unsigned NOT NULL,
  `price` double(8,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `module_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `product_county_floor_prices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_county_floor_prices` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `county_location_id` bigint unsigned NOT NULL,
  `state_location_id` bigint unsigned NOT NULL,
  `service_product_id` bigint unsigned NOT NULL,
  `sale_type_id` bigint unsigned NOT NULL,
  `quality_tier_id` bigint unsigned NOT NULL,
  `property_type_id` bigint unsigned NOT NULL,
  `price` double(8,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_floor_price_index` (`service_product_id`,`county_location_id`,`property_type_id`,`quality_tier_id`,`sale_type_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `product_rejections`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_rejections` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `product_assignment_id` bigint unsigned NOT NULL,
  `company_user_id` bigint unsigned NOT NULL,
  `reason` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `conversion_uploaded` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `conversion_uploaded_idx` (`conversion_uploaded`),
  KEY `product_assignment_id_idx` (`product_assignment_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `product_state_bid_prices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_state_bid_prices` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `state_location_id` bigint unsigned NOT NULL,
  `product_campaign_id` bigint unsigned NOT NULL,
  `service_product_id` bigint unsigned NOT NULL,
  `sale_type_id` bigint unsigned NOT NULL,
  `quality_tier_id` bigint unsigned NOT NULL,
  `property_type_id` bigint unsigned NOT NULL,
  `price` double(8,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `module_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `product_state_floor_prices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_state_floor_prices` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `state_location_id` bigint unsigned NOT NULL,
  `service_product_id` bigint unsigned NOT NULL,
  `sale_type_id` bigint unsigned NOT NULL,
  `quality_tier_id` bigint unsigned NOT NULL,
  `property_type_id` bigint unsigned NOT NULL,
  `price` double(8,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_floor_price_index` (`service_product_id`,`state_location_id`,`property_type_id`,`quality_tier_id`,`sale_type_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `products` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `products_name_idx` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `property_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `property_types` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `quality_tiers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `quality_tiers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `role_has_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `role_has_permissions` (
  `permission_id` bigint unsigned NOT NULL,
  `role_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`permission_id`,`role_id`),
  KEY `role_has_permissions_role_id_foreign` (`role_id`),
  CONSTRAINT `role_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `role_has_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roles` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `guard_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `roles_name_guard_name_unique` (`name`,`guard_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `round_robins`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `round_robins` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_id` int NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `filter` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `ruleset_scores`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ruleset_scores` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `model_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint unsigned NOT NULL,
  `ruleset_id` bigint unsigned DEFAULT NULL,
  `data` json NOT NULL,
  `score` decimal(8,2) NOT NULL,
  `calculated_at` timestamp NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ruleset_scores_ruleset_id_foreign` (`ruleset_id`),
  CONSTRAINT `ruleset_scores_ruleset_id_foreign` FOREIGN KEY (`ruleset_id`) REFERENCES `rulesets` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `rulesets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `rulesets` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `rules` json NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `source` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `filter` json NOT NULL,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `running_workflows`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `running_workflows` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `workflow_id` bigint unsigned NOT NULL,
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'initial',
  `current_action_id` bigint unsigned DEFAULT NULL,
  `payload` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `event_company_id` int GENERATED ALWAYS AS (json_unquote(json_extract(`payload`,_utf8mb4'$.event.event_data.company_id'))) VIRTUAL,
  `event_lead_id` int GENERATED ALWAYS AS (json_unquote(json_extract(`payload`,_utf8mb4'$.event.event_data.lead_id'))) VIRTUAL,
  PRIMARY KEY (`id`),
  KEY `running_workflows_workflow_id_index` (`workflow_id`),
  KEY `running_workflows_event_company_id_index` (`event_company_id`),
  KEY `running_workflows_event_lead_id_index` (`event_lead_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `sale_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sale_types` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `default_type` tinyint(1) NOT NULL,
  `sale_limit` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `sales_bait_configurations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sales_bait_configurations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `industry` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` int NOT NULL,
  `location_id` int DEFAULT NULL,
  `enabled` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `sales_bait_leads`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sales_bait_leads` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `lead_id` int NOT NULL,
  `company_id` int NOT NULL,
  `campaign_id` int DEFAULT NULL,
  `clicks` int NOT NULL DEFAULT '0',
  `actions_taken` int NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `sales_bait_leads_lead_id_index` (`lead_id`),
  KEY `sales_bait_leads_company_id_index` (`company_id`),
  KEY `sales_bait_leads_campaign_id_index` (`campaign_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `sales_bait_registered_interests`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sales_bait_registered_interests` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `sales_bait_id` int NOT NULL,
  `relation_type` int NOT NULL,
  `relation_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `sales_bait_registered_interests_sales_bait_id_index` (`sales_bait_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `sales_bait_restricted_companies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sales_bait_restricted_companies` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `sales_bait_restricted_companies_company_id_index` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `service_company_fields`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `service_company_fields` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `industry_service_id` bigint unsigned NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` int NOT NULL,
  `show_on_profile` tinyint(1) NOT NULL DEFAULT '0',
  `show_on_dashboard` tinyint(1) NOT NULL DEFAULT '0',
  `payload` json NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `category` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Basic Info',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `service_company_review_fields`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `service_company_review_fields` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `industry_service_id` int NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` int NOT NULL,
  `payload` json NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `service_consumer_fields`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `service_consumer_fields` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `industry_service_id` bigint unsigned NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` int NOT NULL,
  `show_on_profile` tinyint(1) NOT NULL DEFAULT '0',
  `show_on_dashboard` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `category_id` bigint unsigned NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `service_consumer_fields_category_id_foreign` (`category_id`),
  CONSTRAINT `service_consumer_fields_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `consumer_configurable_field_categories` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `service_products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `service_products` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `industry_service_id` bigint unsigned NOT NULL,
  `product_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `service_products_industry_service_id_index` (`industry_service_id`),
  KEY `is_id_product_id_index` (`industry_service_id`,`product_id`),
  KEY `service_products_product_id_index` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sessions` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `payload` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_activity` int NOT NULL,
  PRIMARY KEY (`id`),
  KEY `sessions_user_id_index` (`user_id`),
  KEY `sessions_last_activity_index` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `silos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `silos` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `root_path` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `collection_handle` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `website_id` int unsigned NOT NULL,
  `industry_id` int unsigned NOT NULL,
  `industry_service_id` int unsigned DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `flow_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `revision_id` char(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `single_product_sales`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `single_product_sales` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `price` double(8,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `company_id` bigint unsigned NOT NULL,
  `consumer_product_id` bigint unsigned NOT NULL,
  `product_assignment_id` bigint unsigned DEFAULT NULL,
  `status` int NOT NULL DEFAULT '1',
  `invoice_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `single_product_sales_company_id_foreign` (`company_id`),
  KEY `single_product_sales_consumer_product_id_foreign` (`consumer_product_id`),
  KEY `single_product_sales_product_assignment_id_foreign` (`product_assignment_id`),
  CONSTRAINT `single_product_sales_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`),
  CONSTRAINT `single_product_sales_consumer_product_id_foreign` FOREIGN KEY (`consumer_product_id`) REFERENCES `consumer_products` (`id`),
  CONSTRAINT `single_product_sales_product_assignment_id_foreign` FOREIGN KEY (`product_assignment_id`) REFERENCES `product_assignments` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `sms_verifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sms_verifications` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `reference` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `sent_reference` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `sent_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `success_manager_clients`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `success_manager_clients` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `success_manager_id` bigint unsigned NOT NULL,
  `company_reference` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `total_spend` double(11,2) NOT NULL,
  `status` tinyint NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `success_manager_clients_success_manager_id_index` (`success_manager_id`),
  KEY `success_manager_clients_company_reference_index` (`company_reference`),
  KEY `idx_id_reference` (`success_manager_id`,`company_reference`),
  CONSTRAINT `success_manager_clients_success_manager_id_foreign` FOREIGN KEY (`success_manager_id`) REFERENCES `success_managers` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `success_managers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `success_managers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `type` tinyint NOT NULL,
  `include_in_sales_round_robin` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `success_managers_user_id_index` (`user_id`),
  CONSTRAINT `success_managers_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `support_officers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `support_officers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `include_in_round_robin` tinyint(1) NOT NULL DEFAULT '0',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `task_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `task_categories` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `task_categories_name_unique` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `task_notes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `task_notes` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `task_id` int NOT NULL,
  `user_id` int NOT NULL,
  `note` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `task_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `task_types` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `modules` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `tasks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tasks` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `assigned_user_id` bigint unsigned NOT NULL,
  `available_at` timestamp NOT NULL,
  `subject` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `results` json NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `running_workflow_id` bigint unsigned DEFAULT NULL,
  `priority` int NOT NULL DEFAULT '1',
  `completed` tinyint(1) NOT NULL DEFAULT '0',
  `task_type_id` int DEFAULT NULL,
  `allows_rescheduling` tinyint(1) NOT NULL DEFAULT '1',
  `reschedule_count` int NOT NULL DEFAULT '0',
  `payload` json DEFAULT NULL,
  `manual` tinyint(1) NOT NULL DEFAULT '0',
  `manual_company_id` int GENERATED ALWAYS AS (json_unquote(json_extract(`payload`,_utf8mb4'$.company_id'))) VIRTUAL,
  `completed_at` timestamp NULL DEFAULT NULL,
  `task_category_id` bigint unsigned NOT NULL DEFAULT '1',
  `uses_odin_id` tinyint(1) NOT NULL DEFAULT '0',
  `dynamic_priority` tinyint(1) NOT NULL DEFAULT '0',
  `dynamic_priority_type` int DEFAULT NULL,
  `timezone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `county_location_id` bigint unsigned DEFAULT NULL,
  `completed_by_user_id` int unsigned DEFAULT NULL,
  `completed_by_impersonator_user_id` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `workflow_tasks_assigned_user_id_foreign` (`assigned_user_id`),
  KEY `tasks_task_type_id_index` (`task_type_id`),
  KEY `tasks_manual_company_id_index` (`manual_company_id`),
  CONSTRAINT `workflow_tasks_assigned_user_id_foreign` FOREIGN KEY (`assigned_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `team_leaders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `team_leaders` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `team_id` bigint unsigned NOT NULL,
  `reports_to_user_id` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `team_members`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `team_members` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `team_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `team_type_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `team_type_role` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `is_leader_role` tinyint(1) NOT NULL DEFAULT '0',
  `team_type_id` bigint unsigned NOT NULL,
  `role_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `team_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `team_types` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `teams`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `teams` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `team_type_id` bigint unsigned NOT NULL,
  `parent_team_id` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `telescope_entries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `telescope_entries` (
  `sequence` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch_id` char(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `family_hash` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `should_display_on_index` tinyint(1) NOT NULL DEFAULT '1',
  `type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`sequence`),
  UNIQUE KEY `telescope_entries_uuid_unique` (`uuid`),
  KEY `telescope_entries_batch_id_index` (`batch_id`),
  KEY `telescope_entries_family_hash_index` (`family_hash`),
  KEY `telescope_entries_created_at_index` (`created_at`),
  KEY `telescope_entries_type_should_display_on_index_index` (`type`,`should_display_on_index`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `telescope_entries_tags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `telescope_entries_tags` (
  `entry_uuid` char(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tag` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`entry_uuid`,`tag`),
  KEY `telescope_entries_tags_tag_index` (`tag`),
  CONSTRAINT `telescope_entries_tags_entry_uuid_foreign` FOREIGN KEY (`entry_uuid`) REFERENCES `telescope_entries` (`uuid`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `telescope_monitoring`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `telescope_monitoring` (
  `tag` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`tag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `test_product_communications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `test_product_communications` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `test_product_id` bigint unsigned NOT NULL,
  `communication_type` int NOT NULL,
  `fraud_score` int unsigned DEFAULT NULL,
  `from` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `to` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `test_products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `test_products` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `product_id` bigint unsigned NOT NULL,
  `company_id` bigint unsigned NOT NULL,
  `campaign_id` bigint unsigned DEFAULT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` int NOT NULL,
  `reveal_at` timestamp NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `contacted` tinyint(1) NOT NULL DEFAULT '0',
  `expire_at` timestamp NULL DEFAULT NULL,
  `legacy_lead_campaign_id` bigint unsigned DEFAULT NULL,
  `created_by_id` bigint unsigned DEFAULT NULL,
  `relation_id` bigint unsigned DEFAULT NULL,
  `relation_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `test_products_company_id_index` (`company_id`),
  KEY `test_products_campaign_id_index` (`campaign_id`),
  KEY `test_products_email_index` (`email`),
  KEY `test_products_phone_index` (`phone`),
  KEY `test_products_legacy_lead_campaign_id_index` (`legacy_lead_campaign_id`),
  KEY `test_products_created_by_id_foreign` (`created_by_id`),
  KEY `test_products_relation_index` (`relation_id`,`relation_type`),
  CONSTRAINT `test_products_created_by_id_foreign` FOREIGN KEY (`created_by_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `text_media_assets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `text_media_assets` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `text_id` bigint unsigned NOT NULL,
  `url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `text_media_assets_text_id_index` (`text_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `texts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `texts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `external_reference` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `external_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone_id` int NOT NULL,
  `other_number` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `direction` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `message_body` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `relation_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `relation_id` int DEFAULT NULL,
  `note` text COLLATE utf8mb4_unicode_ci,
  `company_cadence_group_action_id` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `et_er_index` (`external_type`,`external_reference`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `timeframe_contact_buffers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `timeframe_contact_buffers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `timeframe_id` bigint unsigned NOT NULL,
  `contact_attempt_buffer_hrs` int NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `timeframe_contact_buffers_timeframe_id_foreign` (`timeframe_id`),
  CONSTRAINT `timeframe_contact_buffers_timeframe_id_foreign` FOREIGN KEY (`timeframe_id`) REFERENCES `lead_processing_timeframes` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `unsubscribed_contacts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `unsubscribed_contacts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `us_zip_code_discovery_statuses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `us_zip_code_discovery_statuses` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `zip_code` char(5) COLLATE utf8mb4_unicode_ci NOT NULL,
  `latitude` decimal(8,6) NOT NULL,
  `longitude` decimal(9,6) NOT NULL,
  `radius` bigint unsigned NOT NULL DEFAULT '8000',
  `place_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `place_keyword` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `industry_id` bigint unsigned NOT NULL,
  `discovery_reference` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `checked` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `us_zip_codes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `us_zip_codes` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `zip_code` char(5) COLLATE utf8mb4_unicode_ci NOT NULL,
  `zip_type` char(1) COLLATE utf8mb4_unicode_ci NOT NULL,
  `city_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `city_type` char(1) COLLATE utf8mb4_unicode_ci NOT NULL,
  `county_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `county_fips` varchar(5) COLLATE utf8mb4_unicode_ci NOT NULL,
  `state_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `state_abbr` char(2) COLLATE utf8mb4_unicode_ci NOT NULL,
  `state_fips` varchar(2) COLLATE utf8mb4_unicode_ci NOT NULL,
  `msa_code` varchar(4) COLLATE utf8mb4_unicode_ci NOT NULL,
  `area_code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `time_zone` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL,
  `utc` decimal(3,1) NOT NULL,
  `dst` char(1) COLLATE utf8mb4_unicode_ci NOT NULL,
  `latitude` decimal(8,6) NOT NULL,
  `longitude` decimal(9,6) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `user_action_tags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_action_tags` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `action_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_action_tags_user_id_foreign` (`user_id`),
  KEY `user_action_tags_action_id_foreign` (`action_id`),
  CONSTRAINT `user_action_tags_action_id_foreign` FOREIGN KEY (`action_id`) REFERENCES `user_actions` (`id`),
  CONSTRAINT `user_action_tags_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `user_actions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_actions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned DEFAULT NULL,
  `subject` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `display_date` date DEFAULT NULL,
  `message` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `pinned` tinyint(1) NOT NULL DEFAULT '0',
  `tag_by_email` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `user_call_forwarding`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_call_forwarding` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `status` tinyint(1) NOT NULL,
  `forward_to_number` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `user_filter_presets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_filter_presets` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `data` json NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_filter_presets_user_id_foreign` (`user_id`),
  CONSTRAINT `user_filter_presets_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `user_phones`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_phones` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `phone_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `user_presets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_presets` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `user_id` bigint unsigned NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` int NOT NULL,
  `category` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` json DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_presets_user_id_index` (`user_id`),
  KEY `user_presets_type_index` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `user_search_filters`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_search_filters` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `category` int NOT NULL,
  `data` json NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_search_filters_user_id_foreign` (`user_id`),
  CONSTRAINT `user_search_filters_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `legacy_user_id` bigint unsigned DEFAULT NULL,
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `timezone` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '-07:00',
  `meeting_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `force_two_factor_auth` tinyint(1) NOT NULL DEFAULT '0',
  `verified_2fa` tinyint(1) NOT NULL DEFAULT '0',
  `two_factor_auth_secret_key` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_by_id` bigint unsigned DEFAULT NULL,
  `updated_by_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`),
  KEY `users_created_by_id_foreign` (`created_by_id`),
  KEY `users_updated_by_id_foreign` (`updated_by_id`),
  CONSTRAINT `users_created_by_id_foreign` FOREIGN KEY (`created_by_id`) REFERENCES `users` (`id`),
  CONSTRAINT `users_updated_by_id_foreign` FOREIGN KEY (`updated_by_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `utility_to_zip_code_occurrences`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `utility_to_zip_code_occurrences` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `utility_id` int NOT NULL,
  `location_id` int NOT NULL,
  `lead_count` int NOT NULL,
  PRIMARY KEY (`id`),
  KEY `utility_to_zip_code_occurrences_utility_id_index` (`utility_id`),
  KEY `utility_to_zip_code_occurrences_location_id_index` (`location_id`),
  KEY `utility_to_zip_code_occurrences_location_id_utility_id_index` (`location_id`,`utility_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `voicemails`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `voicemails` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `from_number` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `from_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `from_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `from_id` int DEFAULT NULL,
  `voicemail_link` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `duration` double(8,2) NOT NULL DEFAULT '0.00',
  `call_sid` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `read` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `website_api_key_origins`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `website_api_key_origins` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `origin` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `website_api_key_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `website_api_key_origins_website_api_key_id_foreign` (`website_api_key_id`),
  CONSTRAINT `website_api_key_origins_website_api_key_id_foreign` FOREIGN KEY (`website_api_key_id`) REFERENCES `website_api_keys` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `website_api_keys`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `website_api_keys` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `website_id` bigint unsigned NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '0',
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `website_api_keys_key_unique` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `websites`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `websites` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `abbreviation` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `cp_domain` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `workflow_actions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `workflow_actions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `previous_node_id` bigint unsigned NOT NULL,
  `previous_node_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `display_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `action_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `payload` json DEFAULT NULL,
  `workflow_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `workflow_actions_workflow_id_index` (`workflow_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `workflow_events`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `workflow_events` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `event_category` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `event_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `workflows`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `workflows` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `workflow_event_id` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `entry_action_id` bigint unsigned DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `generic` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (1,'2014_10_12_000000_create_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (2,'2014_10_12_100000_create_password_resets_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (3,'2018_08_08_100000_create_telescope_entries_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (4,'2019_08_19_000000_create_failed_jobs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (5,'2019_12_14_000001_create_personal_access_tokens_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (6,'2022_05_01_235255_create_cache_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (7,'2022_05_06_155449_create_sessions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (8,'2022_05_06_155456_create_jobs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (9,'2022_05_09_191949_create_permission_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (10,'2022_05_10_174533_create_lead_processing_allocations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (11,'2022_05_11_115338_create_lead_processing_configurations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (12,'2022_05_11_115920_create_lead_processing_failed_leads_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (13,'2022_05_11_120126_create_lead_processing_heartbeats_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (14,'2022_05_11_122059_create_lead_processing_history_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (15,'2022_05_11_122334_create_lead_processing_pending_reviews_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (16,'2022_05_11_122613_create_lead_processing_queue_configurations',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (17,'2022_05_11_162224_create_lead_processing_teams_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (18,'2022_05_11_163127_create_lead_processing_timeframes_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (19,'2022_05_11_190000_create_lead_processing_time_zone_configurations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (20,'2022_05_11_195959_create_lead_processors_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (21,'2022_05_11_196000_create_lead_processing_under_reviews_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (22,'2022_05_11_203950_create_lead_processor_notifications_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (23,'2022_05_11_204804_create_lead_processor_phones_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (24,'2022_05_13_142106_create_lead_processing_budget_statuses_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (25,'2022_05_13_142355_create_lead_processing_communications_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (26,'2022_05_13_142724_create_lead_processing_constraints_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (27,'2022_05_13_142954_create_lead_processing_initials_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (28,'2022_05_13_143108_create_lead_processing_queue_constraints_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (29,'2022_05_13_143342_create_lead_processing_calls_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (30,'2022_05_13_143801_create_timeframe_contact_buffers_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (31,'2022_05_13_143952_create_lead_processing_phones_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (32,'2022_05_13_144017_create_lead_processing_queue_constraints_bucket_flags_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (33,'2022_05_13_145151_create_lead_processing_texts_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (34,'2022_05_16_160623_create_client_token_services_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (35,'2022_05_16_160624_create_client_tokens_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (36,'2022_05_16_234757_add_lead_id_to_lead_processing_under_reviews_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (37,'2022_05_17_130536_update_lead_processor_phones_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (38,'2022_05_19_131759_add_timestamps_to_lead_processor_notifications_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (39,'2022_05_20_173718_create_utility_to_zip_code_occurrences_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (40,'2022_05_23_153645_add_possible_revenue_to_lead_processing_under_reviews_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (41,'2022_05_23_165140_create_available_budgets_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (42,'2022_05_24_184637_create_lead_processing_reserved_leads_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (43,'2022_05_26_012130_add_indexes_for_under_review_queue',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (44,'2022_05_26_132909_create_lead_processing_calling_time_zone_configurations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (45,'2022_05_27_192431_add_deleted_at_column_lead_processors_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (46,'2022_05_30_201407_create_sales_bait_leads_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (47,'2022_05_31_123719_add_indexes_to_queue_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (48,'2022_05_31_124602_create_unsubscribed_contacts_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (49,'2022_05_31_155813_create_tables_for_am_dashboard',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (50,'2022_05_31_195551_create_sales_bait_configurations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (51,'2022_06_01_131822_create_contact_subscriptions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (52,'2022_06_01_143453_create_historical_company_rejection_percentages',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (53,'2022_06_01_145857_create_sales_bait_registered_interests_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (54,'2022_06_01_193422_create_historical_available_budget_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (55,'2022_06_15_144524_create_workflow_actions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (56,'2022_06_15_173932_create_workflow_tasks_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (57,'2022_06_17_065243_create_notifications_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (58,'2022_06_20_041211_create_workflows_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (59,'2022_06_20_042043_create_running_workflows_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (60,'2022_06_21_051129_create_workflow_events_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (61,'2022_06_21_150512_create_completed_workflows_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (62,'2022_06_22_000604_add_payload_to_workflow_actions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (63,'2022_06_23_025145_add_workflow_id_to_workflow_actions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (64,'2022_06_23_032051_add_workflow_entry_event_id_column_to_workflows',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (65,'2022_06_27_012235_create_estimated_revenue_per_lead_by_locations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (66,'2022_06_27_044029_rename_fields_in_workflow_actions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (67,'2022_06_28_221748_add_index_to_available_budgets_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (68,'2022_06_29_021518_remove_current_action_id_from_completed_workflows_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (69,'2022_06_29_064019_add_running_workflow_id_to_tasks_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (70,'2022_06_29_070735_add_priority_to_workflow_tasks_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (71,'2022_06_29_075518_add_completed_to_workflow_tasks_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (72,'2022_07_01_012219_create_task_types_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (73,'2022_07_01_015411_rename_workflow_tasks_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (74,'2022_07_01_023228_add_task_id_to_tasks_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (75,'2022_07_05_003835_create_actions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (76,'2022_07_06_043154_add_generic_to_workflows_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (77,'2022_07_06_060503_create_task_notes_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (78,'2022_07_08_021745_create_round_robins_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (79,'2022_07_13_174957_create_email_templates_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (80,'2022_07_14_060838_add_allows_rescheduling_column_to_tasks_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (81,'2022_07_15_011757_add_reschedule_count_to_tasks_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (82,'2022_07_20_042239_update_email_templates_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (83,'2022_07_20_072216_add_payload_and_manual_fields_to_tasks_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (84,'2022_07_25_201808_update_phones_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (85,'2022_07_25_203010_update_user_phones_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (86,'2022_07_25_205039_update_calls_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (87,'2022_07_25_205518_update_texts_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (88,'2022_07_26_150842_create_email_template_backgrounds_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (89,'2022_07_26_192852_add_background_column_to_email_templates_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (90,'2022_07_27_154519_add_indexes_to_historical_available_location_budgets_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (91,'2022_07_28_190214_create_communications_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (92,'2022_08_02_190033_update_calls_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (93,'2022_08_03_170303_create_voicemails_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (94,'2022_08_08_170656_create_call_recordings_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (95,'2022_08_08_192645_update_calls_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (96,'2022_08_16_135812_add_company_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (97,'2022_08_16_153804_add_industry_service_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (98,'2022_08_16_170226_add_product_and_consumer_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (99,'2022_08_16_203756_add_review_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (100,'2022_08_17_174754_add_legacy_id_columns',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (101,'2022_08_18_183450_add_include_in_sales_round_robin_to_account_managers_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (102,'2022_08_22_155643_add_admin_status_to_companies',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (103,'2022_08_22_162558_create_payment_collections_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (104,'2022_08_22_174620_create_sale_types_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (105,'2022_08_23_160839_create_impersonation_logs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (106,'2022_08_24_041808_add_deleted_at_to_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (107,'2022_08_24_142244_add_indexes_to_account_managers',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (108,'2022_09_08_174406_create_configurable_field_types_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (109,'2022_09_09_020348_add_virtual_columns_to_tasks_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (110,'2022_09_09_020640_add_virtual_columns_to_running_workflows_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (111,'2022_09_12_161211_add_last_lead_created_interval_column_to_lead_processing_configurations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (112,'2022_09_13_065559_update_industry_consumer_field_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (113,'2022_09_14_002306_create_support_officers_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (114,'2022_09_14_052431_add_key_to_industry_types_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (115,'2022_09_15_152644_create_advertising_campaigns_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (116,'2022_09_15_164912_create_google_ads_geo_targets_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (117,'2022_09_19_205214_create_advertising_campaign_automation_parameters_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (118,'2022_09_26_162313_create_advertising_campaign_history_logs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (119,'2022_09_26_171234_create_user_call_forwarding_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (120,'2022_09_27_150729_add_companies_available_column_to_estimated_revenue_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (121,'2022_09_27_160426_add_running_workflow_id_to_completed_workflows_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (122,'2022_09_27_191456_add_leadid_virtual_column_to_running_workflow_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (123,'2022_09_27_191837_add_leadid_virtual_column_to_completed_workflow_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (124,'2022_09_28_155648_add_industry_column_to_advertising_campaigns_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (125,'2022_10_03_135144_add_last_run_and_run_interval_columns_to_advertising_campaigns_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (126,'2022_10_05_175824_update_actions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (127,'2022_10_06_170246_create_advertising_accounts_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (128,'2022_10_06_185119_remove_industry_from_advertising_campaigns_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (129,'2022_10_10_173532_move_automation_columns_to_advertising_campaigns_automation_parameters_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (130,'2022_10_11_022701_add_completed_at_to_tasks',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (131,'2022_10_11_050832_create_task_categories_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (132,'2022_10_11_053225_add_task_category_id_column_to_tasks_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (133,'2022_10_12_153557_update_lead_procesing_allocations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (134,'2022_10_12_225700_create_sales_bait_restricted_companies_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (135,'2022_10_14_015236_add_pinned_to_actions',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (136,'2022_10_20_020509_create_available_leads_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (137,'2022_10_20_063632_add_link_to_logo_to_companies',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (138,'2022_10_20_154920_create_job_batches_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (139,'2022_10_20_234239_add_category_field_to_configurable_company_fields',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (140,'2022_10_24_003720_add_legacy_ids_to_actions',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (141,'2022_10_24_055716_add_uses_odin_id_column_to_tasks',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (142,'2022_10_26_033037_create_tables_for_success_managers',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (143,'2022_10_26_235010_add_index_to_company_locations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (144,'2022_10_26_235915_add_index_to_company_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (145,'2022_10_27_154336_create_locked_advertising_campaign_locations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (146,'2022_10_27_184847_update_lead_processing_under_reviews_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (147,'2022_10_28_040357_remove_good_to_sell_and_address_id_from_consumers_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (148,'2022_10_28_040656_create_consumer_product_data_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (149,'2022_10_28_041011_add_good_to_sell_and_address_id_to_consumer_products_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (150,'2022_10_28_043324_add_payload_field_to_product_assignments_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (151,'2022_10_31_232614_create_consumer_product_tcpa_records_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (152,'2022_10_31_232622_create_consumer_product_tracking_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (153,'2022_10_31_233309_create_consumer_product_affiliate_records_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (154,'2022_11_01_001707_add_fields_to_consumer_products_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (155,'2022_11_03_045915_add_consumer_product_id_to_lead_processing_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (156,'2022_11_07_013848_add_utc_field_to_addresses_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (157,'2022_11_10_213650_add_ad_track_columns_to_consumer_product_tracking',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (158,'2022_11_11_045822_change_longitute_and_latitude_field_type_to_addresses_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (159,'2022_11_16_203842_change_expires_in_to_integer_on_client_tokens_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (160,'2022_11_17_225735_add_index_to_consumers_addresses_product_assignments_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (161,'2022_11_18_033051_create_hunters_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (162,'2022_11_18_191126_expand_client_token_fields_length',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (163,'2022_11_21_220201_add_prescreened_column_to_companies',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (164,'2022_11_22_184634_create_websites_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (165,'2022_11_22_192001_add_tracks_conversions_column_to_advertising_accounts',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (166,'2022_11_22_192713_create_advertising_account_websites_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (167,'2022_11_29_170747_create_meta_ads_locations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (168,'2022_12_05_213422_create_floor_price_formulas_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (169,'2022_12_07_001055_add_is_contact_field_to_company_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (170,'2022_12_09_015240_create_activity_feeds_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (171,'2022_12_09_020133_create_activity_conversations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (172,'2022_12_12_002952_add_user_id_to_activity_feeds_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (173,'2022_12_12_224233_create_company_action_categories_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (174,'2022_12_12_224541_add_action_category_id_to_actions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (175,'2022_12_13_035258_rename_relation_attributes_in_activity_feeds_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (176,'2022_12_13_043005_change_item_type_in_activity_feeds_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (177,'2022_12_14_230328_add_call_recording_id_column_to_calls',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (178,'2022_12_15_162437_add_run_interval_display_unit_to_advertising_campaigns',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (179,'2022_12_19_052148_add_dynamic_priority_fields_to_tasks_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (180,'2022_12_21_175138_create_us_zip_codes_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (181,'2022_12_27_204641_create_us_zip_code_discovery_statuses_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (182,'2022_12_28_001558_add_imported_column_to_addresses_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (183,'2023_01_03_042846_add_relation_type_to_texts',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (184,'2023_01_10_154954_create_product_campaigns_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (185,'2023_01_10_155023_create_product_campaign_budgets_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (186,'2023_01_12_042521_drop_call_recording_id_column_from_calls',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (187,'2023_01_13_034947_add_uuid_column_to_companies_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (188,'2023_01_15_224526_add_email_verification_column_to_company_users',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (189,'2023_01_19_012512_create_sms_verifications_table_and_add_phone_verified_at_column_to_company_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (190,'2023_01_19_234752_add_indexes_to_product_assignments_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (191,'2023_01_19_235255_add_indexes_to_lead_processing_under_reviews_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (192,'2023_01_19_235335_add_indexes_to_lead_processing_pending_reviews_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (193,'2023_01_31_003046_create_company_contracts_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (194,'2023_02_01_010217_add_index_id_to_consumers_id',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (195,'2023_02_01_030306_add_consumer_id_index_on_consumer_products_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (196,'2023_02_01_055750_add_indices_to_consumer_product_and_lead_processing_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (197,'2023_02_01_151125_add_next_lead_logging_team_col_to_lead_processing_configurations',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (198,'2023_02_01_153009_create_product_prices_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (199,'2023_02_01_154842_create_quality_tiers_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (200,'2023_02_01_154931_create_property_types_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (201,'2023_02_03_203348_create_best_revenue_scenario_logs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (202,'2023_02_05_202346_add_campaign_id_col_to_product_assignments',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (203,'2023_02_06_033048_add_payload_to_notifications_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (204,'2023_02_06_155326_add_product_campaign_budget_id_col_to_product_assignments_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (205,'2023_02_06_163156_add_budget_start_timestamp_max_budget_usage_and_category_to_product_campaign_budgets_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (206,'2023_02_06_213218_change_type_to_value_type_in_product_campaign_budgets',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (207,'2023_02_07_234823_add_status_column_to_account_managers_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (208,'2023_02_08_005254_add_timezone_to_tasks_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (209,'2023_02_08_012634_add_timezone_to_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (210,'2023_02_08_225350_create_teams_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (211,'2023_02_10_210649_best_revenue_scenario_indexes',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (212,'2023_02_12_233414_create_product_appointments',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (213,'2023_02_15_151822_create_appointment_offerings_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (214,'2023_02_21_220710_add_indexes_to_appointment_offerings',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (215,'2023_02_23_224525_create_appointment_deliveries_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (216,'2023_02_24_002932_add_authentication_type_column_to_company_users',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (217,'2023_02_27_235453_add_meeting_url_column_to_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (218,'2023_02_28_012444_create_hunter_states_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (219,'2023_02_28_012457_create_hunter_industries_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (220,'2023_02_28_023032_create_generic_profitability_assumption_configurations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (221,'2023_02_28_230301_add_filter_field_to_round_robins_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (222,'2023_03_01_165736_add_allocate_off_hour_leads_column_to_company_configurations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (223,'2023_03_02_005142_add_county_location_id_field_to_tasks_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (224,'2023_03_02_153000_rename_priority_to_attempt_on_appointment_offerings',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (225,'2023_03_02_155114_add_brs_indexes_to_appointment_offerings',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (226,'2023_03_06_210205_add_consumer_token_to_appointment_deliveries',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (227,'2023_03_09_151746_add_lead_consumer_product_id_to_product_appointments',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (228,'2023_03_09_190439_add_off_hour_sale_column_to_product_assignments_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (229,'2023_03_13_192708_add_allocated_as_lead_to_product_appointments',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (230,'2023_03_14_033502_add_is_primary_location_column_to_company_locations',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (231,'2023_03_15_015949_add_covering_indexes',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (232,'2023_03_15_190545_add_formatted_other_number_column_to_calls_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (233,'2023_03_15_190836_add_formatted_cell_phone_and_formatted_office_phone_columns_to_company_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (234,'2023_03_16_181804_add_link_key_code_to_appointment_offerings',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (235,'2023_03_17_202130_change_agg_value_type_to_double_in_company_external_reviews_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (236,'2023_03_27_192618_index_company_users_phone_columns',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (237,'2023_03_27_193416_index_calls_formatted_other_number_column',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (238,'2023_03_28_022737_create_dashboard_login_tokens_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (239,'2023_03_30_004938_add_nullable_to_campaign_id_for_sales_bait_leads_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (240,'2023_04_03_002721_create_company_media_assets_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (241,'2023_04_03_201344_add_slug_column_to_industries_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (242,'2023_04_03_232743_make_company_id_nullable_on_profitability_assumptions',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (243,'2023_04_04_181726_make_unique_name_column_in_industries_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (244,'2023_04_06_030212_add_watchdog_id_column_to_companies_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (245,'2023_04_10_172652_add_consolidated_status_and_campaigns_are_partially_suspended_columns_to_companies_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (246,'2023_04_12_011743_create_licenses_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (247,'2023_04_12_183817_add_unique_index_to_company_industries_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (248,'2023_04_13_045047_create_user_filter_presets_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (249,'2023_04_13_122917_add_pinned_column_to_company_contacts',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (250,'2023_04_17_052334_add_timestamp_columns_to_company_industries',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (251,'2023_04_17_054545_add_industry_id_column_to_lead_processing_teams',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (252,'2023_04_17_070034_convert_industry_name_to_id_on_lead_processing_teams',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (253,'2023_04_18_052334_index_place_id_column',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (254,'2023_04_19_023227_create_consumer_configurable_field_categories',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (255,'2023_04_19_023546_add_category_id_to_consumer_field_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (256,'2023_04_20_014238_add_deleted_at_to_consumer_configurable_field_categories',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (257,'2023_04_21_040100_create_website_api_key_origins_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (258,'2023_04_25_200020_add_appointments_active_to_company_configurations',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (259,'2023_04_25_221238_change_product_campaign_budgets_status_to_integer',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (260,'2023_04_26_014609_add_industry_color_picker',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (261,'2023_04_26_150825_add_status_reactivate_at_to_product_campaigns',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (262,'2023_05_04_030825_make_communication_relation_columns_nullable',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (263,'2023_05_05_050856_create_bundle_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (264,'2023_05_10_064645_add_service_id_to_product_campaigns_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (265,'2023_05_15_021152_add_key_column_to_sale_types_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (266,'2023_05_15_190211_update_texts_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (267,'2023_05_16_005923_rename_column_service_id_to_industry_service_id_in_product_campaigns_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (268,'2023_05_17_054610_add_property_type_id_to_consumer_products',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (269,'2023_05_18_030508_add_soft_deletes_to_companies_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (270,'2023_05_18_050508_add_sales_status_to_companies_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (271,'2023_05_18_072439_update_bundle_invoice_cancellation_fields',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (272,'2023_05_19_050508_add_decision_maker_to_company_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (273,'2023_05_25_030403_add_note_column_to_calls_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (274,'2023_05_26_000519_add_bundle_invoice_payable_columns',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (275,'2023_05_31_142509_create_product_cancellations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (276,'2023_06_01_155112_add_original_product_appointment_id_to_product_appointments',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (277,'2023_06_02_061923_add_bundles_table_industry_columns',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (278,'2023_06_06_185242_create_computed_rejection_statistics_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (279,'2023_06_06_185302_create_computed_campaign_statistics_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (280,'2023_06_06_191534_add_2fa_secret_key_to_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (281,'2023_06_07_021958_add_index_to_company_locations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (282,'2023_06_07_022549_add_indexes_to_company_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (283,'2023_06_07_025845_add_index_to_consumers_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (284,'2023_06_07_030403_add_rejection_to_product_assignments_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (285,'2023_06_08_040804_add_deleted_at_to_user_phones',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (286,'2023_06_14_014612_add_note_column_to_texts_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (287,'2023_06_16_192257_add_legacy_id_index_on_companies_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (288,'2023_06_21_194506_add_scheduling_user_reference_to_company_users',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (289,'2023_06_22_152322_create_lead_processing_team_industries_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (290,'2023_06_26_020700_add_quality_score_to_companies',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (291,'2023_06_27_001828_create_company_quality_score_rules',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (292,'2023_06_30_063326_drop_quality_score_from_companies_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (293,'2023_06_30_063820_create_company_quality_scores_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (294,'2023_07_02_231343_create_company_quality_score_industry_configurations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (295,'2023_07_03_014432_create_opportunity_notifications_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (296,'2023_07_03_182849_create_product_campaign_schedules_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (297,'2023_07_07_051004_add_enable_promotions_to_company_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (298,'2023_07_07_211345_add_appointments_calendar_to_company_configurations',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (299,'2023_07_10_213227_add_related_appt_sold_and_unsellable_to_product_appointments',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (300,'2023_07_11_064543_create_ruleset_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (301,'2023_07_11_071630_update_opportunity_notification_config_table_to_refer_to_ruleset_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (302,'2023_07_11_190226_change_appointments_calendar_to_require_appointments_calendar_on_company_configurations',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (303,'2023_07_11_195023_add_product_campaign_id_to_appointment_deliveries',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (304,'2023_07_19_032514_add_soft_deletes_to_rulesets_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (305,'2023_07_19_033412_add_soft_deletes_to_company_quality_score_rules_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (306,'2023_07_26_072337_create_missed_products_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (307,'2023_07_26_213227_add_index_to_computed_rejection_statistics_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (308,'2023_07_26_223227_cost_values_to_computed_rejection_statistics_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (309,'2023_07_27_185118_create_cadence_routines_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (310,'2023_07_27_185354_create_company_cadence_routines_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (311,'2023_07_27_185442_create_cadence_scheduled_groups_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (312,'2023_07_27_185504_create_company_cadence_scheduled_groups_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (313,'2023_07_27_185656_create_cadence_scheduled_group_actions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (314,'2023_07_27_185735_create_company_cadence_scheduled_group_actions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (315,'2023_07_27_185826_create_cadence_user_contact_exclusions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (316,'2023_07_31_195156_create_appointment_processing_allocations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (317,'2023_08_03_034338_add_fields_to_rulesets_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (318,'2023_08_03_062806_drop_company_quality_scores_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (319,'2023_08_03_153018_create_user_search_filters_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (320,'2023_08_04_040507_update_email_templates_table_to_refer_to_industries_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (321,'2023_08_04_050224_update_email_template_backgrounds_table_to_refer_to_industries_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (322,'2023_08_07_022232_add_email_template_foreign_key_to_opportunity_notification_config_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (323,'2023_08_08_042449_drop_is_production_column_from_rulesets_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (324,'2023_08_09_000039_drop_batch_id_column_from_ruleset_scores_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (325,'2023_08_09_150956_create_cadence_task_templates_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (326,'2023_08_09_151018_create_cadence_sms_templates_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (327,'2023_08_09_151040_create_cadence_email_templates_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (328,'2023_08_09_181720_add_soft_delete_to_cadence_email_templates_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (329,'2023_08_09_181743_add_soft_delete_to_cadence_sms_templates_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (330,'2023_08_09_181806_add_soft_delete_to_cadence_task_templates_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (331,'2023_08_09_185827_add_soft_delete_to_cadence_routines_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (332,'2023_08_09_234533_add_type_column_to_rulesets_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (333,'2023_08_10_004221_make_company_quality_score_rules_industry_relation_nullable',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (334,'2023_08_15_055555_add_is_queued_column_to_opportunity_notification_configs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (335,'2023_08_15_151239_add_attempt_count_and_time_to_appointment_deliveries',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (336,'2023_08_16_191529_update_times_on_cadence_scheduled_groups_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (337,'2023_08_18_125456_add_user_id_and_global_to_cadence_email_templates_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (338,'2023_08_18_125522_add_user_id_and_global_to_cadence_sms_templates_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (339,'2023_08_18_125545_add_user_id_and_global_to_cadence_task_templates_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (340,'2023_08_18_125645_add_global_to_cadence_routines_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (341,'2023_08_21_125645_create_location_silo_pages_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (342,'2023_08_21_165453_update_company_cadence_scheduled_groups_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (343,'2023_08_21_212842_add_status_to_company_cadence_routines_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (344,'2023_08_22_144616_add_preview_and_task_id_to_company_cadence_scheduled_group_actions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (345,'2023_08_23_185217_add_domain_to_cadence_routines_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (346,'2023_08_23_185239_add_domain_to_company_cadence_routines_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (347,'2023_08_23_214649_add_target_execution_time_stamp_to_company_cadence_scheduled_groups_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (348,'2023_08_24_125645_add_indexes_to_company_services_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (349,'2023_08_24_153704_update_cadence_email_templates_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (350,'2023_08_24_153800_update_cadence_sms_templates_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (351,'2023_08_24_153826_update_cadence_task_templates_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (352,'2023_08_24_153907_update_company_cadence_scheduled_group_actions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (353,'2023_08_24_175906_create_cadence_email_headers_and_footers_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (354,'2023_08_28_010516_add_subject_to_email_templates_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (355,'2023_08_29_052901_add_product_type_to_opportunity_notification_configs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (356,'2023_08_29_104400_create_global_configurations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (357,'2023_08_30_051210_create_single_product_sales_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (358,'2023_08_30_135504_add_task_name_to_cadence_task_templates_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (359,'2023_08_31_035504_add_collection_handle_to_location_silo_pages_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (360,'2023_09_01_055342_create_user_presets_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (361,'2023_09_06_005443_add_uuid_to_opportunity_notification_configs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (362,'2023_09_06_031822_create_silos_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (363,'2023_09_06_031906_update_location_silo_pages_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (364,'2023_09_09_135504_add_cp_domain_to_websites_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (365,'2023_09_11_021437_add_missed_products_active_to_company_configurations',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (366,'2023_09_12_003551_add_is_active_column_to_silos',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (367,'2023_09_12_021803_add_revisions_and_flows_to_silos',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (368,'2023_09_15_045513_update_single_product_sales_table_with_payment_statuses',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (369,'2023_09_18_011953_add_mi_appointments_active_to_company_configurations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (370,'2023_09_19_012346_add_label_to_configurable_field_types',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (371,'2023_09_20_061900_create_missed_product_configs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (372,'2023_09_21_020629_add_invoice_id_to_single_product_sales_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (373,'2023_09_24_231215_migrate_old_ruleset_structure',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (374,'2023_09_25_071558_add_ruleset_id_to_company_quality_score_rules_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (375,'2023_09_26_053248_add_notification_cooldown_in_days_to_missed_product_configs',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (376,'2023_09_26_134006_add_succesful_count_column_to_company_cadence_scheduled_groups_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (377,'2023_09_26_155347_add_verified_website_to_companies_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (378,'2023_09_27_155349_add_cancelled_rejected_to_appointment_processing_allocations',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (379,'2023_09_27_165048_add_ran_lead_allocation_to_appointment_processing_allocations',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (380,'2023_09_27_180859_add_display_date_to_actions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (381,'2023_09_28_235557_create_action_tags_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (382,'2023_09_29_150308_add_company_cadence_group_action_id_column_to_texts_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (383,'2023_09_29_150309_add_company_cadence_group_action_id_column_to_activity_feeds_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (384,'2023_10_03_153440_create_emails_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (385,'2023_10_04_174932_add_index_to_service_products',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (386,'2023_10_04_180237_add_index_to_company_locations',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (387,'2023_10_10_010357_add_purchase_fail_email_template_id_field_to_missed_product_configs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (388,'2023_10_11_151245_add_notes_to_company_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (389,'2023_10_18_140800_create_company_campaigns_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (390,'2023_10_18_141835_create_campaign_reactivations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (391,'2023_10_18_152118_create_company_campaign_location_modules_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (392,'2023_10_18_153320_create_company_campaign_location_module_locations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (393,'2023_10_19_000652_add_created_and_updated_by_id_to_company_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (394,'2023_10_20_190310_create_budgets_and_budget_containers_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (395,'2023_10_23_052712_add_soft_deletes_to_user_presets',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (396,'2023_10_29_035028_add_sale_limit_to_sale_types',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (397,'2023_11_15_010715_create_company_links_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (398,'2023_11_22_213833_add_module_id_to_product_county_bid_prices_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (399,'2023_11_22_234740_add_module_id_to_product_state_bid_prices_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (400,'2023_12_06_095222_create_test_products_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (401,'2023_12_06_115432_add_friendly_name_and_type_to_phones_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (402,'2023_12_06_222219_create_company_expert_reviews_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (403,'2023_12_07_205802_create_company_campaign_property_types_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (404,'2023_12_13_015858_create_test_product_communications_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (405,'2024_01_03_071246_add_name_column_to_company_media_assets_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (406,'2024_01_11_192613_index_consumer_products_created_at',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (407,'2024_01_11_232441_create_consumer_field_module_visibility_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (408,'2024_01_14_185952_remove_company_user_id_from_company_user_fields_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (409,'2024_01_14_202632_add_sales_status_tracking_to_actions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (410,'2024_01_15_022759_create_user_actions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (411,'2024_01_15_052007_create_user_action_tags_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (412,'2024_01_19_043916_create_consumer_common_fields',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (413,'2024_01_19_192037_create_company_campaign_delivery_modules_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (414,'2024_01_19_192216_create_company_campaign_delivery_module_contacts_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (415,'2024_01_19_202543_create_company_campaign_delivery_module_crms_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (416,'2024_01_20_012615_create_company_campaign_crm_delivery_logs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (417,'2024_01_22_202306_add_budget_id_to_product_assignment',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (418,'2024_01_22_220354_add_maximum_budget_usage_to_company_campaigns',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (419,'2024_01_22_221154_add_never_exceed_budget_to_companies',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (420,'2024_01_23_191631_create_industry_configurations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (421,'2024_01_24_172009_create_company_campaign_relations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (422,'2024_01_27_194937_add_indexes_to_optimize_consumer_search',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (423,'2024_01_27_200712_add_indexes_to_optimize_consumer_search_split',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (424,'2024_01_30_004105_add_user_preset_id_to_opportunity_notification_configs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (425,'2024_01_30_031959_drop_ruleset_constraint_from_opportunity_notification_configs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (426,'2024_02_01_235624_add_expire_date_and_contact_column_to_test_products_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (427,'2024_02_02_011808_update_users_table_with_created_by_and_updated_by',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (428,'2024_02_07_011808_add_crm_rejection_columns_to_computed_rejection_statistics_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (429,'2024_02_07_160451_create_available_company_by_locations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (430,'2024_02_08_001455_change_header_footer_column_type_to_longtext_on_email_template_backgrounds_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (431,'2024_02_08_011033_add_override_can_receive_promotions_to_missed_product_configs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (432,'2024_02_08_041222_add_unsubscribed_from_promotions_to_company_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (433,'2024_02_12_232652_add_index_to_consumer_email_field',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (434,'2024_02_13_011848_create_mailbox_user_tokens_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (435,'2024_02_13_013703_create_mailbox_emails_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (436,'2024_02_13_020417_create_mailbox_email_recipients_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (437,'2024_02_13_035352_modify_content_column_on_test_product_communications_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (438,'2024_02_14_231628_create_company_campaign_bid_price_module_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (439,'2024_02_15_011808_remove_columns_from_computed_rejection_statistics_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (440,'2024_02_16_031400_create_mailbox_email_parts_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (441,'2024_02_16_223104_add_conversion_uploaded_to_product_assignments',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (442,'2024_02_16_234030_add_conversion_uploaded_to_product_rejections',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (443,'2024_02_18_224127_create_mailbox_user_email_listener_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (444,'2024_02_18_224158_create_mailbox_email_tags_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (445,'2024_02_19_003327_add_virtual_column_to_consumer_product_data_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (446,'2024_02_19_003434_create_mailbox_email_user_histories_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (447,'2024_02_19_005801_add_composite_index_to_consumer_products_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (448,'2024_02_21_231348_add_delivery_email_template_id_to_industries_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (449,'2024_02_21_234030_add_indexes_to_consumers_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (450,'2024_02_22_161151_create_company_slugs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (451,'2024_02_22_222723_add_indexes_to_consumer_product_data_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (452,'2024_02_23_001838_add_lead_campaign_id_to_test_products_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (453,'2024_02_24_184512_add_upload_conversions_interval_hours_to_advertising_accounts',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (454,'2024_02_25_225648_add_missing_columns_to_mailbox_emails_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (455,'2024_02_27_055017_add_administration_columns_to_mailbox_emails_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (456,'2024_02_28_000537_add_default_to_email_templates',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (457,'2024_03_03_231554_remove_mailbox_email_tags_table_and_create_mailbox_email_labels_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (458,'2024_03_06_010302_add_quality_tier_id_column_in_product_assignments_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (459,'2024_03_06_050112_add_content_column_to_mailbox_emails_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (460,'2024_03_06_050200_create_mailbox_email_attachments_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (461,'2024_03_07_010904_create_mailbox_user_emails_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (462,'2024_03_11_173300_add_expires_at_column_to_personal_access_tokens_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (463,'2024_03_12_042025_add_uuid_to_mailbox_user_emails_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (464,'2024_03_13_002940_create_notes_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (465,'2024_03_13_050324_add_indexes_on_company_campaigns_budgets_budget_containers_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (466,'2024_03_14_232312_create_identified_contacts_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (467,'2024_03_19_050617_create_possible_contacts_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (468,'2024_03_19_205507_create_company_change_logs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (469,'2024_03_19_212857_create_historical_aggregate_company_statuses_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (470,'2024_03_19_232324_add_from_identified_contact_id_to_mailbox_emails_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (471,'2024_03_19_233704_add_identified_contact_id_to_mailbox_email_recipients_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (472,'2024_03_21_043659_add_budget_id_index_to_product_assignments',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (473,'2024_03_21_145736_add_external_type_and_reference_index_to_texts_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (474,'2024_03_21_151504_add_call_id_index_to_call_recordings_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (475,'2024_03_21_151838_add_relation_type_and_id_index_to_calls_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (476,'2024_03_25_030439_add_from_a20_to_mailbox_emails_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (477,'2024_03_25_223953_change_from_user_email_subject_and_snippet_to_nullable',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (478,'2024_03_26_212511_add_reference_index_on_companies',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (479,'2024_03_27_021521_add_job_tracking_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (480,'2024_03_27_145736_add_user_tracking_to_tasks_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (481,'2024_04_02_154214_add_year_product_to_historical_company_rejection_percentages_and_change_rejection_percentage_to_json',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (482,'2024_04_04_031704_add_unique_constraint_to_possible_contacts_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (483,'2024_04_05_041353_add_created_by_field_to_test_product',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (484,'2024_04_08_040251_add_soft_deletes_to_campaign_reactivations',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (485,'2024_04_10_040251_add_more_indexes_to_historical_budgets_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (486,'2024_04_12_001831_create_company_metrics_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (487,'2024_04_12_013319_add_polymorphic_columns_to_test_products_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (488,'2024_04_15_040551_create_future_available_budgets_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (489,'2024_04_15_174410_create_historical_company_revenue_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (490,'2024_04_15_211215_add_index_to_product_assignment_rejection_cancellations_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (491,'2024_04_16_190506_add_monthly_yearly_revenue_to_historical_company_revenue',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (492,'2024_04_17_220002_remove_company_change_log_statuses_and_add_log',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (493,'2024_04_17_225059_create_historical_company_sales_status_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (494,'2024_04_18_204622_add_ipscore_to_test_product_communications_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (495,'2024_04_19_012431_add_virtual_column_to_consumer_product_data_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (496,'2024_04_22_164536_repurpose_historical_aggregate_company_statuses_to_be_non_aggregate',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (497,'2024_04_23_025526_create_text_media_assets_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (498,'2024_05_03_010611_add_email_index_to_company_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (499,'2024_05_08_010611_add_formatted_phone_to_consumers_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (500,'2024_05_15_021459_add_composite_index_to_floor_pricing_tables',1);
