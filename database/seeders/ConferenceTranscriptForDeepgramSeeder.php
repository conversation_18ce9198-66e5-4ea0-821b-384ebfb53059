<?php

namespace Database\Seeders;

use App\Models\Calendar\Calendar;
use App\Models\Calendar\CalendarEvent;
use App\Models\Conference\Conference;
use App\Models\Conference\ConferenceParticipant;
use App\Models\Conference\ConferenceTranscript;
use App\Models\Conference\ConferenceTranscriptEntry;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Carbon;

/**
 * @note This is for seeding a conference transcript intended to manually test being sent to Deepgram for analysis.
 *
 * @see \App\Jobs\Deepgram\AnalyzeConferenceTranscriptWithDeepgram
 */
class ConferenceTranscriptForDeepgramSeeder extends Seeder
{
    use WithoutModelEvents;

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $user = User::factory()->create();

        $conferenceTranscript = ConferenceTranscript::factory()->create([
            'conference_id' => Conference::factory()->create([
                'user_id' => $user->id,
                'calendar_event_id' => CalendarEvent::factory()->create([
                    'user_id' => $user->id,
                    'calendar_id' => Calendar::factory()->create([
                        'user_id' => $user->id,
                    ]),
                ]),
            ]),
        ]);

        $firstParticipant = ConferenceParticipant::factory()->create([
            'conference_id' => $conferenceTranscript->conference_id,
            'name' => 'John Doe',
            'earliest_start_time' => Carbon::create(2025, 5, 1, 9),
            'latest_end_time' => Carbon::create(2025, 5, 1, 9, 30),
        ]);

        $secondParticipant = ConferenceParticipant::factory()->create([
            'conference_id' => $conferenceTranscript->conference_id,
            'name' => 'Jane Doe',
            'earliest_start_time' => Carbon::create(2025, 5, 1, 9),
            'latest_end_time' => Carbon::create(2025, 5, 1, 9, 30),
        ]);

        ConferenceTranscriptEntry::factory()
            ->count(4)
            ->sequence(
                [
                    'conference_participant_id' => $secondParticipant->id,
                    'start_time' => Carbon::create(2025, 5, 1, 9, 5, 5),
                    'end_time' => Carbon::create(2025, 5, 1, 9, 15),
                    'text' => 'Good, good.',
                ], // This happened second according to the start and end time
                [
                    'conference_participant_id' => $firstParticipant->id,
                    'start_time' => Carbon::create(2025, 5, 1, 9),
                    'end_time' => Carbon::create(2025, 5, 1, 9, 5),
                    'text' => 'Hey man, how are you?',
                ], // This happened first according to the start and end time
                [
                    'conference_participant_id' => $firstParticipant->id,
                    'start_time' => Carbon::create(2025, 5, 1, 9, 15, 5),
                    'end_time' => Carbon::create(2025, 5, 1, 9, 30),
                    'text' => 'Glad to hear.',
                ], // This happened last according to the start and end time
                [
                    'conference_participant_id' => $secondParticipant->id,
                    'start_time' => Carbon::create(2025, 5, 1, 9, 40),
                    'end_time' => Carbon::create(2025, 5, 1, 9, 45),
                    'text' => '',
                ], // This is an invalid entry because the text is blank
            )
            ->createQuietly([
                'conference_transcript_id' => $conferenceTranscript->id,
            ]);
    }
}
