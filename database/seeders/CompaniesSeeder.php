<?php

namespace Database\Seeders;

use App\Models\Odin\Company;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CompaniesSeeder extends Seeder
{
    use WithoutModelEvents;

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Company::factory(100)->create();

        Company::factory()->state([Company::FIELD_ID => 1517])->create(); //Lakes Solar
    }
}
