<?php

namespace Database\Seeders;

use App\Models\LeadProcessingTeam;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LeadProcessingTeamsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table(LeadProcessingTeam::TABLE)->insert([
            [
                LeadProcessingTeam::FIELD_ID => LeadProcessingTeam::NO_TEAM_ASSIGNED_ID,
                LeadProcessingTeam::FIELD_NAME => 'None',
                LeadProcessingTeam::FIELD_PRIMARY_QUEUE_CONFIGURATION_ID => 0,
                LeadProcessingTeam::FIELD_PRIMARY_UTC_OFFSET => 0,
                LeadProcessingTeam::FIELD_INDUSTRY_ID => 1
            ],
            [
                LeadProcessingTeam::FIELD_ID => 2,
                LeadProcessingTeam::FIELD_NAME => 'Initial',
                LeadProcessingTeam::FIELD_PRIMARY_QUEUE_CONFIGURATION_ID => 1,
                LeadProcessingTeam::FIELD_PRIMARY_UTC_OFFSET => -5,
                LeadProcessingTeam::FIELD_INDUSTRY => 1
            ],
            [
                LeadProcessingTeam::FIELD_ID => 3,
                LeadProcessingTeam::FIELD_NAME => 'Pending Last Round',
                LeadProcessingTeam::FIELD_PRIMARY_QUEUE_CONFIGURATION_ID => 2,
                LeadProcessingTeam::FIELD_PRIMARY_UTC_OFFSET => -5,
                LeadProcessingTeam::FIELD_INDUSTRY => 1
            ],
            [
                LeadProcessingTeam::FIELD_ID => 4,
                LeadProcessingTeam::FIELD_NAME => 'Pending BRS',
                LeadProcessingTeam::FIELD_PRIMARY_QUEUE_CONFIGURATION_ID => 3,
                LeadProcessingTeam::FIELD_PRIMARY_UTC_OFFSET => -5,
                LeadProcessingTeam::FIELD_INDUSTRY => 1
            ],
            [
                LeadProcessingTeam::FIELD_ID => 5,
                LeadProcessingTeam::FIELD_NAME => 'Pacific Under Review',
                LeadProcessingTeam::FIELD_PRIMARY_QUEUE_CONFIGURATION_ID => 4,
                LeadProcessingTeam::FIELD_PRIMARY_UTC_OFFSET => -8,
                LeadProcessingTeam::FIELD_INDUSTRY => 1
            ],
            [
                LeadProcessingTeam::FIELD_ID => 6,
                LeadProcessingTeam::FIELD_NAME => 'Under Review Super Premium',
                LeadProcessingTeam::FIELD_PRIMARY_QUEUE_CONFIGURATION_ID => 5,
                LeadProcessingTeam::FIELD_PRIMARY_UTC_OFFSET => -5,
                LeadProcessingTeam::FIELD_INDUSTRY => 1
            ],
            [
                LeadProcessingTeam::FIELD_ID => 7,
                LeadProcessingTeam::FIELD_NAME => 'Roofing Initial',
                LeadProcessingTeam::FIELD_PRIMARY_QUEUE_CONFIGURATION_ID => 1,
                LeadProcessingTeam::FIELD_PRIMARY_UTC_OFFSET => -5,
                LeadProcessingTeam::FIELD_INDUSTRY => 2
            ],
        ]);
    }
}
