<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductTracking;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use App\Models\Odin\Website;


class FilteredInConsumerSeeder extends Seeder
{
    use WithoutModelEvents;

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $rangeOfDates = collect();

        for ($i = 0; $i < 10; $i++) {
            $rangeOfDates->push(now()->subDays($i));
        }

        foreach ($rangeOfDates as $date) {
            $this->createFilteredInConsumer([
                'created_at' => $date,
            ]);
        }
    }

    private function createFilteredInConsumer(array $consumerData = []): Consumer
    {
        $consumer = Consumer::factory()->create($consumerData);

        $industryService = IndustryService::factory()->create();

        $serviceProduct = ServiceProduct::factory()->create([
            'product_id' => Product::first()->id,
            'industry_service_id' => $industryService->id,
        ]);

        $address = Address::factory()->create();

        $website = Website::factory()->create();

        $consumerProductTracking = ConsumerProductTracking::factory()->create([
            'website_id' => $website->id,
        ]);

        $consumerProduct = ConsumerProduct::factory()->create([
            'consumer_id' => $consumer->id,
            'service_product_id' => $serviceProduct->id,
            'address_id' => $address->id,
            'consumer_product_tracking_id' => $consumerProductTracking->id,
            'created_at' => now()->subDays(1),
        ]);

        ProductAssignment::factory()->create([
            'consumer_product_id' => $consumerProduct->id,
        ]);

        return $consumer;
    }
}
