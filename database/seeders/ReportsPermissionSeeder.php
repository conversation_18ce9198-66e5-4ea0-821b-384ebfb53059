<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class ReportsPermissionSeeder extends Seeder
{
    /**
     * Create the initial roles and permissions.
     *
     * @return void
     */
    public function run()
    {
        Permission::findOrCreate('reports');

        // create roles and assign existing permissions
        $role1 = Role::findOr<PERSON><PERSON>('data-analyst');
        $role1->givePermissionTo('dashboard');
        $role1->givePermissionTo('reports');

        $adminRole = Role::findByName('admin');
        $adminRole->givePermissionTo('reports');
    }
}
