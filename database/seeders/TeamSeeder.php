<?php

namespace Database\Seeders;

use App\Enums\Team\TeamName;
use App\Enums\Team\TeamType as TeamTypeEnum;
use App\Models\Teams\Team;
use App\Models\Teams\TeamType;
use Illuminate\Database\Seeder;
class TeamSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        $this->seedTeamTypes();
        $this->seedTeams();
    }

    public function seedTeamTypes(): void
    {
        foreach (TeamTypeEnum::cases() as $teamType) {
            if (!TeamType::query()->where(TeamType::FIELD_NAME, $teamType)->first()) {
                TeamType::query()->create([
                    TeamType::FIELD_NAME => $teamType,
                    TeamType::FIELD_DESCRIPTION => $teamType->defaultDescription()
                ]);
            }
        }
    }

    public function seedTeams(): void
    {
        $salesTeamType = TeamType::query()->where(TeamType::FIELD_NAME, TeamTypeEnum::SALES)->firstOrFail();

        foreach (TeamName::cases() as $teamName) {
            if (!Team::query()->where(Team::FIELD_NAME, $teamName)->first()) {
                Team::query()->create([
                    Team::FIELD_NAME => $teamName,
                    Team::FIELD_DESCRIPTION => $teamName->defaultDescription(),
                    Team::FIELD_TEAM_TYPE_ID => $salesTeamType->id
                ]);
            }
        }
    }
}
