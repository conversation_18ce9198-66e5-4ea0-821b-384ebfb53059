<?php

namespace Database\Seeders;

use App\DataModels\Odin\ConfigurableFieldDataModel;
use App\Enums\Odin\SolarConfigurableFields;
use App\Models\Odin\ConfigurableFieldType;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryCompanyField;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class IndustryCompanyFieldsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $industries = Industry::all()->pluck(Industry::FIELD_ID, Industry::FIELD_NAME)->toArray();

        $model = new IndustryCompanyField();

        $model->{IndustryCompanyField::FIELD_INDUSTRY_ID} = $industries['Solar'];
        $model->{IndustryCompanyField::FIELD_NAME} = 'Year Started Solar';
        $model->{IndustryCompanyField::FIELD_KEY} = SolarConfigurableFields::YEAR_STARTED_SOLAR;
        $model->{IndustryCompanyField::FIELD_TYPE} = ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_INTEGER)->first()?->id;
        $model->{IndustryCompanyField::FIELD_PAYLOAD} = ConfigurableFieldDataModel::fromArray([]);

        $model->save();
    }
}
