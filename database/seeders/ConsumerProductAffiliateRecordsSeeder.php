<?php

namespace Database\Seeders;

use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProductAffiliateRecord;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ConsumerProductAffiliateRecordsSeeder extends Seeder
{
    use WithoutModelEvents;

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        ConsumerProductAffiliateRecord::factory(Consumer::query()->count())->create();
    }
}
