<?php

namespace Database\Seeders;

use App\Models\LeadProcessingConstraint;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LeadProcessingConstraintsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table(LeadProcessingConstraint::TABLE)->insert([
            [
                LeadProcessingConstraint::FIELD_ID => 1,
                LeadProcessingConstraint::FIELD_TYPE => LeadProcessingConstraint::CONSTRAINT_TYPE_SORT,
                LeadProcessingConstraint::FIELD_KEY => LeadProcessingConstraint::CONSTRAINT_REVENUE,
                LeadProcessingConstraint::FIELD_NAME => 'Best Revenue Scenario'
            ],
            [
                LeadProcessingConstraint::FIELD_ID => 2,
                LeadProcessingConstraint::FIELD_TYPE => LeadProcessingConstraint::CONSTRAINT_TYPE_BUCKET,
                LeadProcessingConstraint::FIELD_KEY => LeadProcessingConstraint::CONSTRAINT_BUDGET,
                LeadProcessingConstraint::FIELD_NAME => 'Budget Status'
            ],
            [
                LeadProcessingConstraint::FIELD_ID => 3,
                LeadProcessingConstraint::FIELD_TYPE => LeadProcessingConstraint::CONSTRAINT_TYPE_SORT,
                LeadProcessingConstraint::FIELD_KEY => LeadProcessingConstraint::CONSTRAINT_LAST_ROUND,
                LeadProcessingConstraint::FIELD_NAME => 'Last Round'
            ],
            [
                LeadProcessingConstraint::FIELD_ID => 4,
                LeadProcessingConstraint::FIELD_TYPE => LeadProcessingConstraint::CONSTRAINT_TYPE_SORT,
                LeadProcessingConstraint::FIELD_KEY => LeadProcessingConstraint::CONSTRAINT_OLDEST_FIRST,
                LeadProcessingConstraint::FIELD_NAME => 'Oldest First'
            ],
            [
                LeadProcessingConstraint::FIELD_ID => 5,
                LeadProcessingConstraint::FIELD_TYPE => LeadProcessingConstraint::CONSTRAINT_TYPE_BUCKET,
                LeadProcessingConstraint::FIELD_KEY => LeadProcessingConstraint::CONSTRAINT_TIMEFRAME,
                LeadProcessingConstraint::FIELD_NAME => 'Lead Timeframe'
            ],
            [
                LeadProcessingConstraint::FIELD_ID => 6,
                LeadProcessingConstraint::FIELD_TYPE => LeadProcessingConstraint::CONSTRAINT_TYPE_FILTER,
                LeadProcessingConstraint::FIELD_KEY => LeadProcessingConstraint::CONSTRAINT_SUPER_PREMIUM,
                LeadProcessingConstraint::FIELD_NAME => 'Super Premium'
            ],
            [
                LeadProcessingConstraint::FIELD_ID => 7,
                LeadProcessingConstraint::FIELD_TYPE => LeadProcessingConstraint::CONSTRAINT_TYPE_FILTER,
                LeadProcessingConstraint::FIELD_KEY => LeadProcessingConstraint::CONSTRAINT_CONTACT_BUFFERS,
                LeadProcessingConstraint::FIELD_NAME => 'Contact Attempt Buffers'
            ],
            [
                LeadProcessingConstraint::FIELD_ID => 8,
                LeadProcessingConstraint::FIELD_TYPE => LeadProcessingConstraint::CONSTRAINT_TYPE_FILTER,
                LeadProcessingConstraint::FIELD_KEY => LeadProcessingConstraint::CONSTRAINT_LOCAL_HOURS,
                LeadProcessingConstraint::FIELD_NAME => 'Local Hour Restrictions'
            ],
            [
                LeadProcessingConstraint::FIELD_ID => 9,
                LeadProcessingConstraint::FIELD_TYPE => LeadProcessingConstraint::CONSTRAINT_TYPE_FILTER,
                LeadProcessingConstraint::FIELD_KEY => LeadProcessingConstraint::CONSTRAINT_AVAILABLE_BUDGET,
                LeadProcessingConstraint::FIELD_NAME => 'Available Budget'
            ],
            [
                LeadProcessingConstraint::FIELD_ID => 10,
                LeadProcessingConstraint::FIELD_TYPE => LeadProcessingConstraint::CONSTRAINT_TYPE_FILTER,
                LeadProcessingConstraint::FIELD_KEY => LeadProcessingConstraint::CONSTRAINT_AVAILABLE_NO_LIMIT_BUDGET,
                LeadProcessingConstraint::FIELD_NAME => 'Available No Limit Budget'
            ]
        ]);
    }
}
