<?php

namespace Database\Seeders;

use App\Models\Odin\ProductCountyBidPrice;
use App\Models\Odin\ProductStateBidPrice;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProductStateBidPricesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $stateBidPriceCol = "state_bid_price";

        $highestCountyBids = ProductCountyBidPrice::query()
            ->selectRaw(implode(',', [
                "MAX(".ProductCountyBidPrice::FIELD_PRICE.") AS {$stateBidPriceCol}",
                ProductCountyBidPrice::FIELD_PRODUCT_CAMPAIGN_ID,
                ProductCountyBidPrice::FIELD_STATE_LOCATION_ID,
                ProductCountyBidPrice::FIELD_SERVICE_PRODUCT_ID,
                ProductCountyBidPrice::FIELD_SALE_TYPE_ID,
                ProductCountyBidPrice::FIELD_QUALITY_TIER_ID,
                ProductCountyBidPrice::FIELD_PROPERTY_TYPE_ID
            ]))
            ->groupBy([
                ProductCountyBidPrice::FIELD_PRODUCT_CAMPAIGN_ID,
                ProductCountyBidPrice::FIELD_STATE_LOCATION_ID,
                ProductCountyBidPrice::FIELD_SERVICE_PRODUCT_ID,
                ProductCountyBidPrice::FIELD_SALE_TYPE_ID,
                ProductCountyBidPrice::FIELD_QUALITY_TIER_ID,
                ProductCountyBidPrice::FIELD_PROPERTY_TYPE_ID
            ])
            ->get();

        $insertRows = [];
        foreach($highestCountyBids as $stateBid) {
            $insertRows[] = [
                ProductStateBidPrice::FIELD_STATE_LOCATION_ID   => $stateBid[ProductCountyBidPrice::FIELD_STATE_LOCATION_ID],
                ProductStateBidPrice::FIELD_PRODUCT_CAMPAIGN_ID => $stateBid[ProductCountyBidPrice::FIELD_PRODUCT_CAMPAIGN_ID],
                ProductStateBidPrice::FIELD_SALE_TYPE_ID        => $stateBid[ProductCountyBidPrice::FIELD_SALE_TYPE_ID],
                ProductStateBidPrice::FIELD_PROPERTY_TYPE_ID    => $stateBid[ProductCountyBidPrice::FIELD_PROPERTY_TYPE_ID],
                ProductStateBidPrice::FIELD_QUALITY_TIER_ID     => $stateBid[ProductCountyBidPrice::FIELD_QUALITY_TIER_ID],
                ProductStateBidPrice::FIELD_SERVICE_PRODUCT_ID  => $stateBid[ProductCountyBidPrice::FIELD_SERVICE_PRODUCT_ID],
                ProductStateBidPrice::FIELD_PRICE               => $stateBid[$stateBidPriceCol]
            ];

            if(count($insertRows) >= 1000) {
                DB::table(ProductStateBidPrice::TABLE)->insert($insertRows);

                $insertRows = [];
            }
        }

        if(!empty($insertRows)) {
            DB::table(ProductStateBidPrice::TABLE)->insert($insertRows);
        }
    }
}
