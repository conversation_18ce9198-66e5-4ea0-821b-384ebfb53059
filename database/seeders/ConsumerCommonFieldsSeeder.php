<?php

namespace Database\Seeders;

use App\Models\Odin\ConsumerCommonField;
use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\ConsumerCommonGetter\ConsumerAddressCityStateZipGetter;
use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\ConsumerCommonGetter\ConsumerAddressStateCountyZipGetter;
use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\ConsumerCommonGetter\ConsumerAddressStreetNumberStreetNameGetter;
use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\ConsumerCommonGetter\ConsumerFullAddressGetter;
use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\ConsumerCommonGetter\ConsumerFullNameGetter;
use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\ConsumerCommonGetter\ConsumerRoofImageGetter;
use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\Enums\ConsumerFieldCategory;
use Illuminate\Database\Seeder;

class ConsumerCommonFieldsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $fields = [
            [
                ConsumerCommonField::FIELD_KEY => "id",
                ConsumerCommonField::FIELD_NAME => "ID",
                ConsumerCommonField::FIELD_CATEGORY => ConsumerFieldCategory::TEXT->value
            ],
            [
                ConsumerCommonField::FIELD_KEY => "email",
                ConsumerCommonField::FIELD_NAME => "Email",
                ConsumerCommonField::FIELD_CATEGORY => ConsumerFieldCategory::TEXT->value
            ],
            [
                ConsumerCommonField::FIELD_KEY => "phone",
                ConsumerCommonField::FIELD_NAME => "Phone",
                ConsumerCommonField::FIELD_CATEGORY => ConsumerFieldCategory::TEXT->value
            ],
            [
                ConsumerCommonField::FIELD_KEY => ConsumerFullNameGetter::ID,
                ConsumerCommonField::FIELD_NAME => "Full Name",
                ConsumerCommonField::FIELD_CATEGORY => ConsumerFieldCategory::TEXT->value
            ],
            [
                ConsumerCommonField::FIELD_KEY => ConsumerFullAddressGetter::ID,
                ConsumerCommonField::FIELD_NAME => "Full Address",
                ConsumerCommonField::FIELD_CATEGORY => ConsumerFieldCategory::TEXT->value
            ],
            [
                ConsumerCommonField::FIELD_KEY => ConsumerAddressStreetNumberStreetNameGetter::ID,
                ConsumerCommonField::FIELD_NAME => "Street",
                ConsumerCommonField::FIELD_CATEGORY => ConsumerFieldCategory::TEXT->value
            ],
            [
                ConsumerCommonField::FIELD_KEY => ConsumerAddressCityStateZipGetter::ID,
                ConsumerCommonField::FIELD_NAME => "City / State",
                ConsumerCommonField::FIELD_CATEGORY => ConsumerFieldCategory::TEXT->value
            ],
            [
                ConsumerCommonField::FIELD_KEY => ConsumerAddressStateCountyZipGetter::ID,
                ConsumerCommonField::FIELD_NAME => "State / County",
                ConsumerCommonField::FIELD_CATEGORY => ConsumerFieldCategory::TEXT->value
            ],
            [
                ConsumerCommonField::FIELD_KEY => ConsumerRoofImageGetter::ID,
                ConsumerCommonField::FIELD_NAME => "Roof",
                ConsumerCommonField::FIELD_CATEGORY => ConsumerFieldCategory::IMAGE->value
            ],
        ];

        foreach ($fields as $field) {
            ConsumerCommonField::query()->firstOrCreate(
                [
                    ConsumerCommonField::FIELD_KEY => $field[ConsumerCommonField::FIELD_KEY]
                ],
                $field
            );
        }
    }
}
