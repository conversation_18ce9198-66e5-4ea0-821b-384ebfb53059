<?php

namespace Database\Seeders;

use App\Enums\PermissionType;
use App\Enums\RoleType;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class LeadsReportPermissionSeeder extends Seeder
{
    /**
     * Create the initial roles and permissions.
     *
     * @return void
     */
    public function run()
    {
        Permission::findOrCreate(PermissionType::LEADS_REPORT_VIEW->value);

        $financeRole = Role::findOrCreate(RoleType::FINANCIAL_ANALYST->value);
        $financeRole->givePermissionTo(PermissionType::LEADS_REPORT_VIEW->value);

        $adminRole = Role::findByName(RoleType::ADMIN->value);
        $adminRole->givePermissionTo(PermissionType::LEADS_REPORT_VIEW->value);
    }
}
