<?php

namespace Database\Seeders;

use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductAffiliateRecord;
use App\Models\Odin\ConsumerProductData;
use App\Models\Odin\ConsumerProductTcpaRecord;
use App\Models\Odin\ConsumerProductTracking;
use App\Models\Odin\ServiceProduct;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ConsumerProductSeeder extends Seeder
{
    use WithoutModelEvents;

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $consumerIds = Consumer::query()->pluck(Consumer::FIELD_ID)->toArray();

        ConsumerProduct::factory(count($consumerIds))
            ->sequence(fn($sequence) => [ConsumerProduct::FIELD_CONSUMER_ID => $consumerIds[$sequence->index]])
            ->create();
    }
}
