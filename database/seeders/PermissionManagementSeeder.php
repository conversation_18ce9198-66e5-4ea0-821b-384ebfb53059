<?php

namespace Database\Seeders;

use App\Enums\PermissionType;
use App\Enums\RoleType;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionManagementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        Permission::findOrCreate(PermissionType::PERMISSION_MANAGEMENT_VIEW->value);
        Permission::findOrCreate(PermissionType::PERMISSION_MANAGEMENT_EDIT->value);

        $admin = Role::findOrCreate(RoleType::ADMIN->value);

        $admin->givePermissionTo([
            PermissionType::PERMISSION_MANAGEMENT_VIEW->value,
            PermissionType::PERMISSION_MANAGEMENT_EDIT->value,
        ]);
    }
}
