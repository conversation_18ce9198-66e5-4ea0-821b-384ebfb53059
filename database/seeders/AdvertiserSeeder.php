<?php

namespace Database\Seeders;

use App\Enums\Advertising\Advertiser as AdvertiserEnum;
use App\Models\Advertiser;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AdvertiserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $insertRows = [];
        foreach(AdvertiserEnum::advertisersByKey() as $key => $name) {
            $insertRows[] = [
                Advertiser::FIELD_KEY => $key,
                Advertiser::FIELD_NAME => $name
            ];
        }

        Advertiser::query()->insert($insertRows);
    }
}
