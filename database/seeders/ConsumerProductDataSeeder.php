<?php

namespace Database\Seeders;

use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProductData;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ConsumerProductDataSeeder extends Seeder
{
    use WithoutModelEvents;

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $factory = ConsumerProductData::factory();

        $recordsCount = Consumer::query()->count();

        $insertRows = [];
        for($i = 0; $i < $recordsCount; $i++) {
            $data = $factory->definition();
            $data[ConsumerProductData::FIELD_PAYLOAD] = json_encode($data[ConsumerProductData::FIELD_PAYLOAD]);
            $insertRows[] = $data;

            if(count($insertRows) >= 500) {
                DB::table(ConsumerProductData::TABLE)->insert($insertRows);
                $insertRows = [];
            }
        }

        if(!empty($insertRows)) {
            DB::table(ConsumerProductData::TABLE)->insert($insertRows);
        }
    }
}
