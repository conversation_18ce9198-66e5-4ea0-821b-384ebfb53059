<?php

namespace Database\Seeders;

use App\Models\ActionCategory;
use Illuminate\Database\Seeder;

class ActionCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        ActionCategory::query()->create([ ActionCategory::FIELD_NAME => 'Call' ]);
        ActionCategory::query()->create([ ActionCategory::FIELD_NAME => 'Email' ]);
        ActionCategory::query()->create([ ActionCategory::FIELD_NAME => 'Meeting' ]);
        ActionCategory::query()->create([ ActionCategory::FIELD_NAME => 'Demo' ]);
        ActionCategory::query()->create([ ActionCategory::FIELD_NAME => 'Note' ]);
        ActionCategory::query()->create([ ActionCategory::FIELD_NAME => 'Other' ]);
        ActionCategory::query()->create([ ActionCategory::FIELD_NAME => 'Text' ]);
    }

}
