<?php

namespace Database\Seeders;

use App\DTO\Billing\Credit\CreditTypePayload;
use App\Enums\Billing\CreditType;
use App\Services\Billing\CreditService;
use Illuminate\Database\Seeder;

class CreditTypesSeeder extends Seeder
{
    public function __construct(protected CreditService $creditService)
    {

    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $data = collect([
            new CreditTypePayload(
                name            : "Sign Up Bonus",
                slug            : CreditType::SIGNUP_BONUS->value,
                description     : "",
                lineItemText    : "Sign Up Bonus",
                expiry          : true,
                expiresInDays   : 30,
                consumptionOrder: 1,
                cash            : true,
                active          : true,
            ),
            new CreditTypePayload(
                name            : "Voucher",
                slug            : CreditType::VOUCHER->value,
                description     : "",
                lineItemText    : "Voucher",
                expiry          : true,
                expiresInDays   : 30,
                consumptionOrder: 2,
                cash            : true,
                active          : true,
            )
        ]);

        $this->creditService->updateOrCreateCreditTypes($data);
    }
}
