<?php

namespace Database\Seeders;

use App\Enums\RoleType;
use App\Models\Phone;
use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;

class TestUsersSeeder extends Seeder
{
    /**
     * Create test users
     *
     * @return void
     */
    public function run()
    {
        // Fetch roles
        $basicRole = Role::findByName('basic');
        $adminRole = Role::findByName('admin');
        $bundleAdminRole = Role::findByName('bundle-admin');
        $bundleIssuerRole = Role::findByName('bundle-issuer');
        $leadProcessorRole = Role::findByName('lead-processor');
        $leadProcessingManagerRole = Role::findByName('lead-processing-management');
        $accountManagerRole = Role::findByName('account-manager');
        $salesBaitManagerRole = Role::findByName('sales-bait-management');
        $salesManagerRole = Role::findByName('sales-manager');
        $hrManagerRole = Role::findByName(RoleType::HR_MANAGER->value);
        $alertProcessorRole = Role::findByName(RoleType::ALERT_PROCESSOR->value);
        $contractManagerRole = Role::findByName(RoleType::CONTRACT_MANAGER->value);

        // create sample users and assign roles
        $user = User::factory()
            ->has(Phone::factory()->count(1))
            ->create([
                'name' => 'Example User',
                'email' => '<EMAIL>',
                'legacy_user_id' => 1,
            ]);
        $user->assignRole($basicRole);

        $user = User::factory()
            ->has(Phone::factory()->count(1))
            ->create([
                'name' => 'Example Admin User',
                'email' => '<EMAIL>',
                'legacy_user_id' => 1,
            ]);
        $user->assignRole($adminRole);
        $user->assignRole($bundleAdminRole);
        $user->assignRole($bundleIssuerRole);
        $user->assignRole($alertProcessorRole);
        $user->assignRole($contractManagerRole);

        $user = User::factory()
            ->has(Phone::factory()->count(1))
            ->create([
                'name' => 'Lead Processing User',
                'email' => '<EMAIL>',
                'legacy_user_id' => 1,
            ]);

        $user->assignRole($leadProcessorRole);

        $user = User::factory()
            ->has(Phone::factory()->count(1))
            ->create([
                'name' => 'Lead Processing Manager',
                'email' => '<EMAIL>',
                'legacy_user_id' => 1,
            ]);
        $user->assignRole($leadProcessingManagerRole);

        $user = User::factory()
            ->has(Phone::factory()->count(1))
            ->create([
                'name' => 'Account Manager',
                'email' => '<EMAIL>',
                'legacy_user_id' => 1,
            ]);
        $user->assignRole($accountManagerRole);

        $user = User::factory()
            ->has(Phone::factory()->count(1))
            ->create([
                'name' => 'Sales Manager',
                'email' => '<EMAIL>',
                'legacy_user_id' => 1,
            ]);
        $user->assignRole($salesManagerRole);

        $user = User::factory()
            ->has(Phone::factory()->count(1))
            ->create([
                'name'           => 'HR Manager',
                'email'          => '<EMAIL>',
                'legacy_user_id' => 1,
            ]);
        $user->assignRole($hrManagerRole);

        $user = User::factory()
            ->has(Phone::factory()->count(1))
            ->create([
                'name' => 'Sales Bait Manager',
                'email' => '<EMAIL>',
                'legacy_user_id' => 1,
            ]);
        $user->assignRole($salesBaitManagerRole);

        $user = User::factory()
            ->has(Phone::factory()->count(1))
            ->create([
                'name' => 'Bundle Manager',
                'email' => '<EMAIL>',
                'legacy_user_id' => 1,
            ]);
        $user->assignRole($bundleAdminRole);

        $user = User::factory()
            ->has(Phone::factory()->count(1))
            ->create([
                'name' => 'Bundle Issuer',
                'email' => '<EMAIL>',
                'legacy_user_id' => 1,
            ]);
        $user->assignRole($bundleIssuerRole);
    }
}
