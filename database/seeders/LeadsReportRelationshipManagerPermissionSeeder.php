<?php

namespace Database\Seeders;

use App\Enums\PermissionType;
use App\Enums\RoleType;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class LeadsReportRelationshipManagerPermissionSeeder extends Seeder
{
    /**
     * Create the initial roles and permissions.
     *
     * @return void
     */
    public function run()
    {
        Permission::findOrCreate(PermissionType::LEADS_REPORT_VIEW_RM->value);
        Permission::findOrCreate(PermissionType::LEADS_REPORT_VIEW_SM->value);

        $rmRole = Role::findOrCreate(RoleType::RELATIONSHIP_MANAGER_VIEWER->value);
        $rmRole->givePermissionTo(PermissionType::LEADS_REPORT_VIEW_RM->value);

        $smRole = Role::findOrCreate(RoleType::SUCCESS_MANAGER_VIEWER->value);
        $smRole->givePermissionTo(PermissionType::LEADS_REPORT_VIEW_SM->value);

        $adminRole = Role::findByName(RoleType::ADMIN->value);
        $adminRole->givePermissionTo(PermissionType::LEADS_REPORT_VIEW_RM->value);
        $adminRole->givePermissionTo(PermissionType::LEADS_REPORT_VIEW_SM->value);



    }
}
