<?php

namespace Database\Seeders;

use App\Enums\PermissionType;
use App\Enums\RoleType;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Spatie\Permission\PermissionRegistrar;

class CompanyCampaignDeliveryLogPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        app(PermissionRegistrar::class)->forgetCachedPermissions();

        Permission::findOrCreate(PermissionType::COMPANY_CAMPAIGN_DELIVERY_LOGS_VIEW->value);

        $admin = Role::findOrCreate(RoleType::ADMIN->value);

        $admin->givePermissionTo(
            [
                PermissionType::COMPANY_CAMPAIGN_DELIVERY_LOGS_VIEW->value
            ]);
    }
}
