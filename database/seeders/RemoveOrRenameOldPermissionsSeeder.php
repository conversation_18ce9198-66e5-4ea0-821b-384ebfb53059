<?php

namespace Database\Seeders;

use App\Enums\PermissionType;
use App\Enums\RoleType;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RemoveOrRenameOldPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * The purpose of this seeder is to remove permissions that are no longer needed or used
     *
     * @return void
     */
    public function run(): void
    {
        //dont use findByName as it will error if not found
        // changed floor-pricing to MINIMUM_PRICE_MANAGEMENT => 'minimum-price-management
        Permission::query()->where('name', 'floor-pricing/view')->first()?->delete();
        Permission::query()->where('name', 'floor-pricing/edit')->first()?->delete();

        // edit-configurations to global-configurations
        Permission::query()->where('name', 'edit-configurations')->first()?->delete();
        Permission::query()->where('name', 'view-configurations')->first()?->delete();
    }
}