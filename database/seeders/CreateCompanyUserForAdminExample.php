<?php

namespace Database\Seeders;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class CreateCompanyUserForAdminExample extends Seeder
{
    use WithoutModelEvents;

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $existing = CompanyUser::query()->where(CompanyUser::FIELD_EMAIL, '<EMAIL>');

        if ($existing->exists()) {
            $existing->delete();
        }

        CompanyUser::factory()->create([
            CompanyUser::FIELD_COMPANY_ID          => Company::factory()->createQuietly()->id,
            CompanyUser::FIELD_EMAIL               => '<EMAIL>',
            CompanyUser::FIELD_STATUS              => CompanyUser::STATUS_ACTIVE,
            CompanyUser::FIELD_CAN_LOG_IN          => true,
            CompanyUser::FIELD_AUTHENTICATION_TYPE => CompanyUser::AUTHENTICATION_TYPE_ADMIN2,
            CompanyUser::FIELD_PASSWORD            => Hash::make('password'),
        ]);
    }
}
