<?php

namespace Database\Seeders;

use App\DataModels\Odin\ConfigurableFieldDataModel;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\Odin\SolarConfigurableFields;
use App\Models\Odin\ConfigurableFieldType;
use App\Models\Odin\GlobalCompanyField;
use Illuminate\Database\Seeder;

class GlobalCompanyFieldsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $payload = ConfigurableFieldDataModel::fromArray([]);

        $model = new GlobalCompanyField();

        $model->{GlobalCompanyField::FIELD_NAME} = 'Year Started Business';
        $model->{GlobalCompanyField::FIELD_KEY} = SolarConfigurableFields::YEAR_STARTED_BUSINESS;
        $model->{GlobalCompanyField::FIELD_TYPE} = ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_INTEGER)->first()?->id;
        $model->{GlobalCompanyField::FIELD_PAYLOAD} = $payload;

        $model->save();

        $model = new GlobalCompanyField();

        $model->{GlobalCompanyField::FIELD_NAME} = 'Family Business';
        $model->{GlobalCompanyField::FIELD_KEY} = SolarConfigurableFields::FAMILY_BUSINESS;
        $model->{GlobalCompanyField::FIELD_TYPE} = ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_BOOLEAN)->first()?->id;
        $model->{GlobalCompanyField::FIELD_PAYLOAD} = $payload;

        $model->save();

        $model = new GlobalCompanyField();

        $model->{GlobalCompanyField::FIELD_NAME} = 'Enable Watchdog Compliance links';
        $model->{GlobalCompanyField::FIELD_KEY} = SolarConfigurableFields::ENABLE_WATCHDOG_COMPLIANCE_LINKS;
        $model->{GlobalCompanyField::FIELD_TYPE} = ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_BOOLEAN)->first()?->id;
        $model->{GlobalCompanyField::FIELD_PAYLOAD} = $payload;

        $model->save();

        $model = new GlobalCompanyField();

        $model->{GlobalCompanyField::FIELD_NAME} = 'Allow Leads Sales Without CC';
        $model->{GlobalCompanyField::FIELD_KEY} = SolarConfigurableFields::ALLOW_LEAD_SALES_WITHOUT_CC;
        $model->{GlobalCompanyField::FIELD_TYPE} = ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_BOOLEAN)->first()?->id;
        $model->{GlobalCompanyField::FIELD_PAYLOAD} = $payload;

        $model->save();

        $model = new GlobalCompanyField();

        $model->{GlobalCompanyField::FIELD_NAME} = 'Never Exceed Budget';
        $model->{GlobalCompanyField::FIELD_KEY} = SolarConfigurableFields::NEVER_EXCEED_BUDGET;
        $model->{GlobalCompanyField::FIELD_TYPE} = ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_BOOLEAN)->first()?->id;
        $model->{GlobalCompanyField::FIELD_PAYLOAD} = $payload;

        $model->save();

        $model = new GlobalCompanyField();

        $model->{GlobalCompanyField::FIELD_NAME} = 'Disallow Ranking';
        $model->{GlobalCompanyField::FIELD_KEY} = SolarConfigurableFields::DISALLOW_RANKING;
        $model->{GlobalCompanyField::FIELD_TYPE} = ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_BOOLEAN)->first()?->id;
        $model->{GlobalCompanyField::FIELD_PAYLOAD} = $payload;

        $model->save();

        $model = new GlobalCompanyField();

        $model->{GlobalCompanyField::FIELD_NAME} = 'Revenue in Thousands';
        $model->{GlobalCompanyField::FIELD_KEY} = SolarConfigurableFields::REVENUE_IN_THOUSANDS;
        $model->{GlobalCompanyField::FIELD_TYPE} = ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_INTEGER)->first()?->id;
        $model->{GlobalCompanyField::FIELD_PAYLOAD} = $payload;

        $model->save();

        $model = new GlobalCompanyField();

        $model->{GlobalCompanyField::FIELD_NAME} = 'Employee Count';
        $model->{GlobalCompanyField::FIELD_KEY} = SolarConfigurableFields::EMPLOYEE_COUNT;
        $model->{GlobalCompanyField::FIELD_TYPE} = ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_INTEGER)->first()?->id;
        $model->{GlobalCompanyField::FIELD_PAYLOAD} = $payload;

        $model->save();

        $model = new GlobalCompanyField();

        $model->{GlobalCompanyField::FIELD_NAME} = 'Office in The USA';
        $model->{GlobalCompanyField::FIELD_KEY} = SolarConfigurableFields::OFFICE_IN_USA;
        $model->{GlobalCompanyField::FIELD_TYPE} = ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_BOOLEAN)->first()?->id;
        $model->{GlobalCompanyField::FIELD_PAYLOAD} = $payload;

        $model->save();

        $model = new GlobalCompanyField();

        $model->{GlobalCompanyField::FIELD_NAME} = 'Enable Lead Compliance Jornaya';
        $model->{GlobalCompanyField::FIELD_KEY} = SolarConfigurableFields::ENABLE_LEAD_COMPLIANCE_JORNAYA;
        $model->{GlobalCompanyField::FIELD_TYPE} = ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_BOOLEAN)->first()?->id;
        $model->{GlobalCompanyField::FIELD_PAYLOAD} = $payload;

        $model->save();

        $model = new GlobalCompanyField();

        $model->{GlobalCompanyField::FIELD_NAME} = 'Description';
        $model->{GlobalCompanyField::FIELD_KEY} = GlobalConfigurableFields::DESCRIPTION;
        $model->{GlobalCompanyField::FIELD_TYPE} = ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id;
        $model->{GlobalCompanyField::FIELD_PAYLOAD} = $payload;

        $model->save();

        $model = new GlobalCompanyField();

        $model->{GlobalCompanyField::FIELD_NAME} = 'Sales Email';
        $model->{GlobalCompanyField::FIELD_KEY} = GlobalConfigurableFields::SALES_EMAIL;
        $model->{GlobalCompanyField::FIELD_TYPE} = ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id;
        $model->{GlobalCompanyField::FIELD_PAYLOAD} = $payload;

        $model->save();

        $model = new GlobalCompanyField();

        $model->{GlobalCompanyField::FIELD_NAME} = 'Tech Support Email';
        $model->{GlobalCompanyField::FIELD_KEY} = GlobalConfigurableFields::TECH_SUPPORT_EMAIL;
        $model->{GlobalCompanyField::FIELD_TYPE} = ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id;
        $model->{GlobalCompanyField::FIELD_PAYLOAD} = $payload;

        $model->save();
    }
}
