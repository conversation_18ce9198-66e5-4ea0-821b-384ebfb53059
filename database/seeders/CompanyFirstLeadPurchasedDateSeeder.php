<?php

namespace Database\Seeders;

use App\Models\Odin\Company;
use App\Models\Odin\ProductAssignment;
use App\Services\Filterables\Company\CompanyFirstLeadPurchasedDateRangeFilterable;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Carbon;
use Tests\Feature\Http\Controllers\API\CompanySearchControllerTest;

/**
 * @see CompanyFirstLeadPurchasedDateRangeFilterable
 * @see CompanySearchControllerTest::search_with_first_lead_purchased_date_filterable()
 * @see CompanySearchControllerTest::search_with_first_lead_purchased_date_filterable_and_missing_onboarding_manager()
 */
class CompanyFirstLeadPurchasedDateSeeder extends Seeder
{
    use WithoutModelEvents;

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Company::factory()->has(ProductAssignment::factory(state: [
            'created_at' => Carbon::now()->subDays(3),
            'chargeable' => true,
            'delivered' => true,
        ]))->createQuietly(); // Three days ago

        Company::factory()->has(ProductAssignment::factory(state: [
            'created_at' => Carbon::now()->subWeek(),
            'chargeable' => true,
            'delivered' => true,
        ]))->createQuietly(); // 1 week ago

        Company::factory()->has(ProductAssignment::factory(state: [
            'created_at' => Carbon::now()->subMonth(),
            'chargeable' => true,
            'delivered' => true,
        ]))->createQuietly(); // 1 month ago

        Company::factory()->has(ProductAssignment::factory(state: [
            'created_at' => Carbon::parse('2021-06-07'),
            'chargeable' => true,
            'delivered' => true,
        ]))->createQuietly(); // Fixed date
    }
}
