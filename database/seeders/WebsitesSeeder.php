<?php

namespace Database\Seeders;

use App\Enums\Odin\OriginDomain;
use App\Models\Odin\Website;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class WebsitesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table(Website::TABLE)->insert([
            [
                Website::FIELD_NAME => 'SolarReviews',
                Website::FIELD_ABBREVIATION => 'sr',
                Website::FIELD_URL => OriginDomain::SOLAR_REVIEWS->value
            ],
            [
                Website::FIELD_NAME => 'SolarEstimate',
                Website::FIELD_ABBREVIATION => 'se',
                Website::FIELD_URL => OriginDomain::SOLAR_ESTIMATE->value
            ],
            [
                Website::FIELD_NAME => 'Roofing Calculator',
                Website::FIELD_ABBREVIATION => 'rc',
                Website::FIELD_URL => OriginDomain::ROOFING_CALCULATOR->value
            ],
            [
                Website::FIELD_NAME => 'Solar Power Rocks',
                Website::FIELD_ABBREVIATION => 'spr',
                Website::FIELD_URL => OriginDomain::SOLAR_POWER_ROCKS->value
            ],
            [
                Website::FIELD_NAME => 'Sun Number',
                Website::FIELD_ABBREVIATION => 'sn',
                Website::FIELD_URL => OriginDomain::SUN_NUMBER->value
            ],
            [
                Website::FIELD_NAME => 'Cut My Bill',
                Website::FIELD_ABBREVIATION => 'cmb',
                Website::FIELD_URL => OriginDomain::CUT_MY_BILL->value
            ],
            [
                Website::FIELD_NAME => 'Fixr',
                Website::FIELD_ABBREVIATION => 'fixr',
                Website::FIELD_URL => OriginDomain::FIXR->value
            ],
            [
                Website::FIELD_NAME => 'MySolar',
                Website::FIELD_ABBREVIATION => 'ws',
                Website::FIELD_URL => OriginDomain::MYSOLAR->value
            ],
            [
                Website::FIELD_NAME => 'Solar Quotes',
                Website::FIELD_ABBREVIATION => 'sq',
                Website::FIELD_URL => OriginDomain::SOLAR_QUOTES->value
            ],
            [
                Website::FIELD_NAME => 'Roofing Estimate',
                Website::FIELD_ABBREVIATION => 're',
                Website::FIELD_URL => OriginDomain::ROOFING_ESTIMATE->value
            ],
            [
                Website::FIELD_NAME => 'Bathroom Estimate',
                Website::FIELD_ABBREVIATION => 'be',
                Website::FIELD_URL => OriginDomain::BATHROOM_ESTIMATE->value
            ],
            [
                Website::FIELD_NAME => 'Kitchen Estimate',
                Website::FIELD_ABBREVIATION => 'ke',
                Website::FIELD_URL => OriginDomain::KITCHEN_ESTIMATE->value
            ]
        ]);
    }
}
