<?php

namespace Database\Seeders;

use App\Models\LeadProcessingBudgetStatus;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LeadProcessingBudgetStatusesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table(LeadProcessingBudgetStatus::TABLE)->insert([
            [
                LeadProcessingBudgetStatus::FIELD_ID => 1,
                LeadProcessingBudgetStatus::FIELD_KEY => LeadProcessingBudgetStatus::KEY_WITHIN_BUDGET,
                LeadProcessingBudgetStatus::FIELD_NAME => 'Within Budget',
                LeadProcessingBudgetStatus::FIELD_PRIORITY => 1
            ],
            [
                LeadProcessingBudgetStatus::FIELD_ID => 2,
                LeadProcessingBudgetStatus::FIELD_KEY => LeadProcessingBudgetStatus::KEY_OVER_BUDGET,
                LeadProcessingBudgetStatus::FIELD_NAME => 'Over Budget',
                LeadProcessingBudgetStatus::FIELD_PRIORITY => 2
            ],
            [
                LeadProcessingBudgetStatus::FIELD_ID => 3,
                LeadProcessingBudgetStatus::FIELD_KEY => LeadProcessingBudgetStatus::KEY_NO_COMPANIES,
                LeadProcessingBudgetStatus::FIELD_NAME => 'No Companies',
                LeadProcessingBudgetStatus::FIELD_PRIORITY => 3
            ]
        ]);
    }
}
