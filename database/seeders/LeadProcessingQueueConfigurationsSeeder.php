<?php

namespace Database\Seeders;

use App\Models\LeadProcessingQueueConfiguration;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LeadProcessingQueueConfigurationsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table(LeadProcessingQueueConfiguration::TABLE)->insert([
            [
                LeadProcessingQueueConfiguration::FIELD_ID => 1,
                LeadProcessingQueueConfiguration::FIELD_NAME => 'Initial',
                LeadProcessingQueueConfiguration::FIELD_PRIMARY_STATUS => 'initial',
                LeadProcessingQueueConfiguration::FIELD_LAST_ROUND => 0
            ],
            [
                LeadProcessingQueueConfiguration::FIELD_ID => 2,
                LeadProcessingQueueConfiguration::FIELD_NAME => 'Pending Review',
                LeadProcessingQueueConfiguration::FIELD_PRIMARY_STATUS => 'pending_review',
                LeadProcessingQueueConfiguration::FIELD_LAST_ROUND => 0
            ],
            [
                LeadProcessingQueueConfiguration::FIELD_ID => 3,
                LeadProcessingQueueConfiguration::FIELD_NAME => 'Pending Review - BRS',
                LeadProcessingQueueConfiguration::FIELD_PRIMARY_STATUS => 'pending_review',
                LeadProcessingQueueConfiguration::FIELD_LAST_ROUND => 0
            ],
            [
                LeadProcessingQueueConfiguration::FIELD_ID => 4,
                LeadProcessingQueueConfiguration::FIELD_NAME => 'Under Review',
                LeadProcessingQueueConfiguration::FIELD_PRIMARY_STATUS => 'under_review',
                LeadProcessingQueueConfiguration::FIELD_LAST_ROUND => 0
            ],
            [
                LeadProcessingQueueConfiguration::FIELD_ID => 5,
                LeadProcessingQueueConfiguration::FIELD_NAME => 'Under Review - Super Premium',
                LeadProcessingQueueConfiguration::FIELD_PRIMARY_STATUS => 'under_review',
                LeadProcessingQueueConfiguration::FIELD_LAST_ROUND => 0
            ]
        ]);

        $this->call(AgedQueueConfigurationSeeder::class);
    }
}
