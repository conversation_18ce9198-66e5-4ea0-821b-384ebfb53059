<?php

namespace Database\Seeders;

use App\Models\Odin\ConfigurableFieldType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ConfigurableFieldTypesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table(ConfigurableFieldType::TABLE)->insert([
            [
                ConfigurableFieldType::FIELD_TYPE => ConfigurableFieldType::TYPE_INTEGER
            ],
            [
                ConfigurableFieldType::FIELD_TYPE => ConfigurableFieldType::TYPE_FLOAT
            ],
            [
                ConfigurableFieldType::FIELD_TYPE => ConfigurableFieldType::TYPE_STRING
            ],
            [
                ConfigurableFieldType::FIELD_TYPE => ConfigurableFieldType::TYPE_BOOLEAN
            ],
            [
                ConfigurableFieldType::FIELD_TYPE => ConfigurableFieldType::TYPE_ARRAY
            ],
            [
                ConfigurableFieldType::FIELD_TYPE => ConfigurableFieldType::TYPE_URL
            ],
        ]);
    }
}
