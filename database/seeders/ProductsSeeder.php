<?php

namespace Database\Seeders;

use App\Enums\Odin\Product as ProductEnum;
use App\Models\Odin\Product;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProductsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table(Product::TABLE)->insert([
            [
                Product::FIELD_NAME => ProductEnum::LEAD
            ],
            [
                Product::FIELD_NAME => ProductEnum::APPOINTMENT
            ]
        ]);
    }
}
