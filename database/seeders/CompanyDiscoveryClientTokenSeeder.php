<?php

namespace Database\Seeders;

use App\Models\ClientToken;
use App\Models\ClientTokenService;
use Illuminate\Database\Seeder;
use Random\RandomException;

class CompanyDiscoveryClientTokenSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     * @throws RandomException
     */
    public function run(): void
    {
        $tokenService = ClientTokenService::query()
            ->updateOrCreate([
                ClientTokenService::FIELD_SERVICE_KEY  => ClientTokenService::COMPANY_DISCOVERY_API_SERVICE_KEY,
                ClientTokenService::FIELD_SERVICE_NAME => 'Company Discovery',
            ]);

        if (!$tokenService->refresh()->clientToken) {
            $tokenService->clientToken()->create([
                ClientToken::FIELD_CLIENT_TOKEN => bin2hex(random_bytes(64)),
            ]);
        }
    }
}
