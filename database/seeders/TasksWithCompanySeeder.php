<?php

namespace Database\Seeders;

use App\Models\Odin\CompanyConfiguration;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Odin\Company;
use App\Models\Sales\Task;
use App\Models\User;

class TasksWithCompanySeeder extends Seeder
{
    use WithoutModelEvents;

    protected Task $task;

    protected User $user;

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->user = User::where('email', '<EMAIL>')->first();

        $this->task = Task::factory()->create([
            'assigned_user_id' => $this->user->id,
            'payload' => [
                'company_id' => Company::factory()->has(CompanyConfiguration::factory(), 'configuration')->createQuietly()->id,
            ]
        ]);
    }
}
