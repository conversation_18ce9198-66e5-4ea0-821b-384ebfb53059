<?php

namespace Database\Seeders;

use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ServiceProduct;
use Illuminate\Database\Seeder;

class ServiceProductsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $industryServiceIds = IndustryService::query()->pluck(IndustryService::FIELD_ID)->toArray();
        $productIds = Product::query()->pluck(Product::FIELD_ID)->toArray();

        $serviceProducts = [];
        foreach($industryServiceIds as $industryServiceId) {
            foreach($productIds as $productId) {
                $serviceProducts[] = [
                    ServiceProduct::FIELD_PRODUCT_ID => $productId,
                    ServiceProduct::FIELD_INDUSTRY_SERVICE_ID => $industryServiceId
                ];
            }
        }

        ServiceProduct::query()->insert($serviceProducts);
    }
}
