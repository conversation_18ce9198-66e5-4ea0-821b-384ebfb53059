<?php

namespace Database\Seeders;

use App\Enums\ContractKeys;
use App\Models\ContractKey;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ContractKeysSeeder extends Seeder
{
    public function run(): void
    {
        $data = [];

        foreach(ContractKeys::cases() as $case) {
            $data[] = [
                ContractKey::FIELD_KEY  => $case->value,
                ContractKey::FIELD_NAME => $case->toDisplayString(),
            ];
        }

        DB::table(ContractKey::TABLE)->insert($data);
    }

}