<?php

namespace Database\Seeders;

use App\Models\Odin\WebsiteApiKey;
use Illuminate\Database\Seeder;
use App\Services\Odin\ApiKeyGeneratorService;

class TestWebsiteApiKeySeeder extends Seeder
{
    /**
    * @return void
    */
    public function run()
    {
        $apiKeyGeneratorService = new ApiKeyGeneratorService();
        // create a key for solarReviews
        $websiteApiKey = WebsiteApiKey::factory([
            'website_id' => 1,
            'name' => 'Test Api Key',
            'status' => 1,
            'key' => $apiKeyGeneratorService->generateKey()
        ]);
    }
}
