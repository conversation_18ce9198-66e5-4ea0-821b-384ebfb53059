dma_code,dma_name,state_key,state_fips,county_name,county_fips
743,Anchorage,AK,02,Anchorage Borough,02020
743,Anchorage,AK,02,Kenai Peninsula Borough,02122
743,Anchorage,AK,02,Matanuska-Susitna Borough,02170
745,Fairbanks,AK,02,Fairbanks North Star Borough,02090
747,Juneau,AK,02,Juneau Borough,02110
745,Fairbanks,AK,02,Juneau Borough,02290
524,Atlanta,AL,01,Cleburne,01029
524,Atlanta,AL,01,<PERSON>,01111
630,Birmingham (Ann And Tusc),AL,01,<PERSON><PERSON><PERSON>,01007
630,Birmingham (Ann And Tusc),AL,01,<PERSON><PERSON><PERSON>,01009
630,Birmingham (Ann And Tusc),AL,01,Calhoun,01015
630,Birmingham (Ann And Tusc),AL,01,Cherokee,01019
630,Birmingham (Ann And Tusc),AL,01,<PERSON>lton,01021
630,Birmingham (Ann And Tusc),AL,01,<PERSON>,01027
630,Birmingham (Ann And Tusc),AL,01,<PERSON><PERSON><PERSON>,01043
630,Birmingham (Ann And Tusc),AL,01,Etowah,01055
630,Birmingham (Ann And Tusc),AL,01,<PERSON><PERSON>,01057
630,Birmingham (Ann And Tusc),AL,01,Greene,01063
630,Birmingham (Ann And Tusc),AL,01,Hale,01065
630,Birmingham (<PERSON> And <PERSON>sc),AL,01,<PERSON>,01073
630,Birmingham (Ann And <PERSON>sc),AL,01,Marion,01093
630,Birmingham (Ann And <PERSON>sc),AL,01,<PERSON><PERSON>,01107
630,Birmingham (Ann <PERSON> <PERSON><PERSON>c),AL,01,St. Clair,01115
630,Birmingham (Ann And <PERSON>sc),AL,01,<PERSON>,01117
630,Birmingham (Ann And Tusc),AL,01,Talladega,01121
630,Birmingham (Ann And Tusc),AL,01,Tuscaloosa,01125
630,Birmingham (Ann And Tusc),AL,01,Walker,01127
630,Birmingham (Ann And Tusc),AL,01,Winston,01133
522,Columbus (GA),AL,01,Barbour,01005
522,Columbus (GA),AL,01,Chambers,01017
522,Columbus (GA),AL,01,Lee,01081
522,Columbus (GA),AL,01,Russell,01113
673,Columbus – Tupelo – West Point – Houston,AL,01,Lamar,01075
606,Dothan,AL,01,Coffee,01031
606,Dothan,AL,01,Dale,01045
606,Dothan,AL,01,Geneva,01061
606,Dothan,AL,01,Henry,01067
606,Dothan,AL,01,Houston,01069
691,Huntsville – Decatur (Florence),AL,01,Colbert,01033
691,Huntsville – Decatur (Florence),AL,01,Dekalb,01049
691,Huntsville – Decatur (Florence),AL,01,Franklin,01059
691,Huntsville – Decatur (Florence),AL,01,Jackson,01071
691,Huntsville – Decatur (Florence),AL,01,Lauderdale,01077
691,Huntsville – Decatur (Florence),AL,01,Lawrence,01079
691,Huntsville – Decatur (Florence),AL,01,Limestone,01083
691,Huntsville – Decatur (Florence),AL,01,Madison,01089
691,Huntsville – Decatur (Florence),AL,01,Marshall,01095
691,Huntsville – Decatur (Florence),AL,01,Morgan,01103
711,Meridian,AL,01,Choctaw,01023
711,Meridian,AL,01,Sumter,01119
686,Mobile – Pensacola (Fort Walt),AL,01,Baldwin,01003
686,Mobile – Pensacola (Fort Walt),AL,01,Clarke,01025
686,Mobile – Pensacola (Fort Walt),AL,01,Conecuh,01035
686,Mobile – Pensacola (Fort Walt),AL,01,Escambia,01053
686,Mobile – Pensacola (Fort Walt),AL,01,Mobile,01097
686,Mobile – Pensacola (Fort Walt),AL,01,Monroe,01099
686,Mobile – Pensacola (Fort Walt),AL,01,Washington,01129
698,Montgomery – Selma,AL,01,Autauga,01001
698,Montgomery – Selma,AL,01,Bullock,01011
698,Montgomery – Selma,AL,01,Butler,01013
698,Montgomery – Selma,AL,01,Coosa,01037
698,Montgomery – Selma,AL,01,Covington,01039
698,Montgomery – Selma,AL,01,Crenshaw,01041
698,Montgomery – Selma,AL,01,Dallas,01047
698,Montgomery – Selma,AL,01,Elmore,01051
698,Montgomery – Selma,AL,01,Lowndes,01085
698,Montgomery – Selma,AL,01,Macon,01087
698,Montgomery – Selma,AL,01,Marengo,01091
698,Montgomery – Selma,AL,01,Montgomery,01101
698,Montgomery – Selma,AL,01,Perry,01105
698,Montgomery – Selma,AL,01,Pike,01109
698,Montgomery – Selma,AL,01,Tallapoosa,01123
698,Montgomery – Selma,AL,01,Wilcox,01131
670,Fort Smith – Fayetteville – Springdale – Rogers,AR,05,Benton,05007
670,Fort Smith – Fayetteville – Springdale – Rogers,AR,05,Crawford,05033
670,Fort Smith – Fayetteville – Springdale – Rogers,AR,05,Franklin,05047
670,Fort Smith – Fayetteville – Springdale – Rogers,AR,05,Johnson,05071
670,Fort Smith – Fayetteville – Springdale – Rogers,AR,05,Logan,05083
670,Fort Smith – Fayetteville – Springdale – Rogers,AR,05,Madison,05087
670,Fort Smith – Fayetteville – Springdale – Rogers,AR,05,Scott,05127
670,Fort Smith – Fayetteville – Springdale – Rogers,AR,05,Sebastian,05131
670,Fort Smith – Fayetteville – Springdale – Rogers,AR,05,Washington,05143
647,Greenwood – Greenville,AR,05,Chicot,05017
734,Jonesboro,AR,05,Clay,05021
734,Jonesboro,AR,05,Craighead,05031
734,Jonesboro,AR,05,Greene,05055
734,Jonesboro,AR,05,Izard,05065
734,Jonesboro,AR,05,Lawrence,05075
734,Jonesboro,AR,05,Randolph,05121
734,Jonesboro,AR,05,Sharp,05135
693,Little Rock – Pine Bluff,AR,05,Arkansas,05001
693,Little Rock – Pine Bluff,AR,05,Bradley,05011
693,Little Rock – Pine Bluff,AR,05,Calhoun,05013
693,Little Rock – Pine Bluff,AR,05,Clark,05019
693,Little Rock – Pine Bluff,AR,05,Cleburne,05023
693,Little Rock – Pine Bluff,AR,05,Cleveland,05025
693,Little Rock – Pine Bluff,AR,05,Conway,05029
693,Little Rock – Pine Bluff,AR,05,Dallas,05039
693,Little Rock – Pine Bluff,AR,05,Desha,05041
693,Little Rock – Pine Bluff,AR,05,Drew,05043
693,Little Rock – Pine Bluff,AR,05,Faulkner,05045
693,Little Rock – Pine Bluff,AR,05,Garland,05051
693,Little Rock – Pine Bluff,AR,05,Grant,05053
693,Little Rock – Pine Bluff,AR,05,Hot Spring,05059
693,Little Rock – Pine Bluff,AR,05,Independence,05063
693,Little Rock – Pine Bluff,AR,05,Jackson,05067
693,Little Rock – Pine Bluff,AR,05,Jefferson,05069
693,Little Rock – Pine Bluff,AR,05,Lincoln,05079
693,Little Rock – Pine Bluff,AR,05,Lonoke,05085
693,Little Rock – Pine Bluff,AR,05,Monroe,05095
693,Little Rock – Pine Bluff,AR,05,Montgomery,05097
693,Little Rock – Pine Bluff,AR,05,Nevada,05099
693,Little Rock – Pine Bluff,AR,05,Ouachita,05103
693,Little Rock – Pine Bluff,AR,05,Perry,05105
693,Little Rock – Pine Bluff,AR,05,Pike,05109
693,Little Rock – Pine Bluff,AR,05,Polk,05113
693,Little Rock – Pine Bluff,AR,05,Pope,05115
693,Little Rock – Pine Bluff,AR,05,Prairie,05117
693,Little Rock – Pine Bluff,AR,05,Pulaski,05119
693,Little Rock – Pine Bluff,AR,05,Saline,05125
693,Little Rock – Pine Bluff,AR,05,Searcy,05129
693,Little Rock – Pine Bluff,AR,05,Stone,05137
693,Little Rock – Pine Bluff,AR,05,Van Buren,05141
693,Little Rock – Pine Bluff,AR,05,White,05145
693,Little Rock – Pine Bluff,AR,05,Woodruff,05147
693,Little Rock – Pine Bluff,AR,05,Yell,05149
640,Memphis,AR,05,Crittenden,05035
640,Memphis,AR,05,Cross,05037
640,Memphis,AR,05,Lee,05077
640,Memphis,AR,05,Mississippi,05093
640,Memphis,AR,05,Phillips,05107
640,Memphis,AR,05,Poinsett,05111
640,Memphis,AR,05,St. Francis,05123
628,Monroe – El Dorado,AR,05,Ashley,05003
628,Monroe – El Dorado,AR,05,Union,05139
612,Shreveport,AR,05,Columbia,05027
612,Shreveport,AR,05,Hempstead,05057
612,Shreveport,AR,05,Howard,05061
612,Shreveport,AR,05,Lafayette,05073
612,Shreveport,AR,05,Little River,05081
612,Shreveport,AR,05,Miller,05091
612,Shreveport,AR,05,Sevier,05133
619,"Springfield, MO",AR,05,Baxter,05005
619,"Springfield, MO",AR,05,Boone,05009
619,"Springfield, MO",AR,05,Carroll,05015
619,"Springfield, MO",AR,05,Fulton,05049
619,"Springfield, MO",AR,05,Marion,05089
619,"Springfield, MO",AR,05,Newton,05101
790,Albuquerque – Santa Fe,AZ,04,Apache,04001
753,Phoenix (Prescott),AZ,04,Coconino,04005
753,Phoenix (Prescott),AZ,04,Gila,04007
753,Phoenix (Prescott),AZ,04,Graham,04009
753,Phoenix (Prescott),AZ,04,Greenlee,04011
753,Phoenix (Prescott),AZ,04,La Paz,04012
753,Phoenix (Prescott),AZ,04,Maricopa,04013
753,Phoenix (Prescott),AZ,04,Mohave,04015
753,Phoenix (Prescott),AZ,04,Navajo,04017
753,Phoenix (Prescott),AZ,04,Pinal,04021
753,Phoenix (Prescott),AZ,04,Yavapai,04025
789,Tucson (Sierra Vista),AZ,04,Cochise,04003
789,Tucson (Sierra Vista),AZ,04,Pima,04019
789,Tucson (Sierra Vista),AZ,04,Santa Cruz,04023
771,Yuma – El Centro,AZ,04,Yuma,04027
800,Bakersfield,CA,06,Kern,06029
868,Chico – Redding,CA,06,Butte,06007
868,Chico – Redding,CA,06,Glenn,06021
868,Chico – Redding,CA,06,Modoc,06049
868,Chico – Redding,CA,06,Shasta,06089
868,Chico – Redding,CA,06,Tehama,06103
868,Chico – Redding,CA,06,Trinity,06105
802,Eureka,CA,06,Del Norte,06015
802,Eureka,CA,06,Humboldt,06023
866,Fresno – Visalia,CA,06,Fresno,06019
866,Fresno – Visalia,CA,06,Kings,06031
866,Fresno – Visalia,CA,06,Madera,06039
866,Fresno – Visalia,CA,06,Mariposa,06043
866,Fresno – Visalia,CA,06,Merced,06047
866,Fresno – Visalia,CA,06,Tulare,06107
803,Los Angeles,CA,06,Inyo,06027
803,Los Angeles,CA,06,Los Angeles,06037
803,Los Angeles,CA,06,Orange,06059
803,Los Angeles,CA,06,Riverside,06065
803,Los Angeles,CA,06,San Bernardino,06071
803,Los Angeles,CA,06,Ventura,06111
813,Medford – Klamath Falls,CA,06,Siskiyou,06093
828,Monterey – Salinas,CA,06,Monterey,06053
828,Monterey – Salinas,CA,06,San Benito,06069
828,Monterey – Salinas,CA,06,Santa Cruz,06087
811,Reno,CA,06,Alpine,06003
811,Reno,CA,06,Lassen,06035
811,Reno,CA,06,Mono,06051
862,Sacramento – Stockton – Modesto,CA,06,Amador,06005
862,Sacramento – Stockton – Modesto,CA,06,Calaveras,06009
862,Sacramento – Stockton – Modesto,CA,06,Colusa,06011
862,Sacramento – Stockton – Modesto,CA,06,El Dorado,06017
862,Sacramento – Stockton – Modesto,CA,06,Nevada,06057
862,Sacramento – Stockton – Modesto,CA,06,Placer,06061
862,Sacramento – Stockton – Modesto,CA,06,Plumas,06063
862,Sacramento – Stockton – Modesto,CA,06,Sacramento,06067
862,Sacramento – Stockton – Modesto,CA,06,San Joaquin,06077
862,Sacramento – Stockton – Modesto,CA,06,Sierra,06091
862,Sacramento – Stockton – Modesto,CA,06,Solano,06095
862,Sacramento – Stockton – Modesto,CA,06,Stanislaus,06099
862,Sacramento – Stockton – Modesto,CA,06,Sutter,06101
862,Sacramento – Stockton – Modesto,CA,06,Tuolumne,06109
862,Sacramento – Stockton – Modesto,CA,06,Yolo,06113
862,Sacramento – Stockton – Modesto,CA,06,Yuba,06115
825,San Diego,CA,06,San Diego,06073
807,San Francisco – Oak – San Jose,CA,06,Alameda,06001
807,San Francisco – Oak – San Jose,CA,06,Contra Costa,06013
807,San Francisco – Oak – San Jose,CA,06,Lake,06033
807,San Francisco – Oak – San Jose,CA,06,Marin,06041
807,San Francisco – Oak – San Jose,CA,06,Mendocino,06045
807,San Francisco – Oak – San Jose,CA,06,Napa,06055
807,San Francisco – Oak – San Jose,CA,06,San Francisco,06075
807,San Francisco – Oak – San Jose,CA,06,San Mateo,06081
807,San Francisco – Oak – San Jose,CA,06,Santa Clara,06085
807,San Francisco – Oak – San Jose,CA,06,Sonoma,06097
855,Santa Barbara – San Mar – San Luob,CA,06,San Luis Obispo,06079
855,Santa Barbara – San Mar – San Luob,CA,06,Santa Barbara,06083
771,Yuma – El Centro,CA,06,Imperial,06025
790,Albuquerque – Santa Fe,CO,08,La Plata,08067
790,Albuquerque – Santa Fe,CO,08,Montezuma,08083
752,Colorado Springs – Pueblo,CO,08,Baca,08009
752,Colorado Springs – Pueblo,CO,08,Bent,08011
752,Colorado Springs – Pueblo,CO,08,Crowley,08025
752,Colorado Springs – Pueblo,CO,08,El Paso,08041
752,Colorado Springs – Pueblo,CO,08,Fremont,08043
752,Colorado Springs – Pueblo,CO,08,Huerfano,08055
752,Colorado Springs – Pueblo,CO,08,Kiowa,08061
752,Colorado Springs – Pueblo,CO,08,Las Animas,08071
752,Colorado Springs – Pueblo,CO,08,Otero,08089
752,Colorado Springs – Pueblo,CO,08,Pueblo,08101
752,Colorado Springs – Pueblo,CO,08,Teller,08119
751,Denver,CO,08,Adams,08001
751,Denver,CO,08,Alamosa,08003
751,Denver,CO,08,Arapahoe,08005
751,Denver,CO,08,Archuleta,08007
751,Denver,CO,08,Boulder,08013
751,Denver,CO,08,Chaffee,08015
751,Denver,CO,08,Cheyenne,08017
751,Denver,CO,08,Clear Creek,08019
751,Denver,CO,08,Conejos,08021
751,Denver,CO,08,Costilla,08023
751,Denver,CO,08,Custer,08027
751,Denver,CO,08,Denver,08031
751,Denver,CO,08,Douglas,08035
751,Denver,CO,08,Eagle,08037
751,Denver,CO,08,Elbert,08039
751,Denver,CO,08,Garfield,08045
751,Denver,CO,08,Gilpin,08047
751,Denver,CO,08,Grand,08049
751,Denver,CO,08,Gunnison,08051
751,Denver,CO,08,Hinsdale,08053
751,Denver,CO,08,Jackson,08057
751,Denver,CO,08,Jefferson,08059
751,Denver,CO,08,Kit Carson,08063
751,Denver,CO,08,Lake,08065
751,Denver,CO,08,Larimer,08069
751,Denver,CO,08,Lincoln,08073
751,Denver,CO,08,Logan,08075
751,Denver,CO,08,Mineral,08079
751,Denver,CO,08,Moffat,08081
751,Denver,CO,08,Morgan,08087
751,Denver,CO,08,Ouray,08091
751,Denver,CO,08,Park,08093
751,Denver,CO,08,Phillips,08095
751,Denver,CO,08,Pitkin,08097
751,Denver,CO,08,Prowers,08099
751,Denver,CO,08,Rio Blanco,08103
751,Denver,CO,08,Rio Grande,08105
751,Denver,CO,08,Routt,08107
751,Denver,CO,08,Saguache,08109
751,Denver,CO,08,San Juan,08111
751,Denver,CO,08,San Miguel,08113
751,Denver,CO,08,Sedgwick,08115
751,Denver,CO,08,Summit,08117
751,Denver,CO,08,Washington,08121
751,Denver,CO,08,Weld,08123
751,Denver,CO,08,Yuma,08125
773,Grand Junction – Montrose,CO,08,Delta,08029
773,Grand Junction – Montrose,CO,08,Mesa,08077
773,Grand Junction – Montrose,CO,08,Montrose,08085
770,Salt Lake City,CO,08,Dolores,08033
533,Hartford & New Haven,CT,09,Hartford,09003
533,Hartford & New Haven,CT,09,Litchfield,09005
533,Hartford & New Haven,CT,09,Middlesex,09007
533,Hartford & New Haven,CT,09,New Haven,09009
533,Hartford & New Haven,CT,09,New London,09011
533,Hartford & New Haven,CT,09,Tolland,09013
533,Hartford & New Haven,CT,09,Windham,09015
501,New York,CT,09,Fairfield,09001
511,"Washington, DC (Hagerstown)",DC,11,District Of Columbia,11001
504,Philadelphia,DE,10,Kent,10001
504,Philadelphia,DE,10,New Castle,10003
576,Salisbury,DE,10,Sussex,10005
571,Fort Myers – Naples,FL,12,Charlotte,12015
571,Fort Myers – Naples,FL,12,Collier,12021
571,Fort Myers – Naples,FL,12,Desoto,12027
571,Fort Myers – Naples,FL,12,Glades,12043
571,Fort Myers – Naples,FL,12,Hendry,12051
571,Fort Myers – Naples,FL,12,Lee,12071
592,Gainesville,FL,12,Alachua,12001
592,Gainesville,FL,12,Dixie,12029
592,Gainesville,FL,12,Gilchrist,12041
592,Gainesville,FL,12,Levy,12075
561,Jacksonville,FL,12,Baker,12003
561,Jacksonville,FL,12,Bradford,12007
561,Jacksonville,FL,12,Clay,12019
561,Jacksonville,FL,12,Columbia,12023
561,Jacksonville,FL,12,Duval,12031
561,Jacksonville,FL,12,Nassau,12089
561,Jacksonville,FL,12,Putnam,12107
561,Jacksonville,FL,12,St. Johns,12109
561,Jacksonville,FL,12,Union,12125
528,Miami – Fort Lauderdale,FL,12,Broward,12011
528,Miami – Fort Lauderdale,FL,12,Miami-Dade,12086
528,Miami – Fort Lauderdale,FL,12,Monroe,12087
686,Mobile – Pensacola (Fort Walt),FL,12,Escambia,12033
686,Mobile – Pensacola (Fort Walt),FL,12,Okaloosa,12091
686,Mobile – Pensacola (Fort Walt),FL,12,Santa Rosa,12113
534,Orlando – Daytona Beach – Melbourne,FL,12,Brevard,12009
534,Orlando – Daytona Beach – Melbourne,FL,12,Flagler,12035
534,Orlando – Daytona Beach – Melbourne,FL,12,Lake,12069
534,Orlando – Daytona Beach – Melbourne,FL,12,Marion,12083
534,Orlando – Daytona Beach – Melbourne,FL,12,Orange,12095
534,Orlando – Daytona Beach – Melbourne,FL,12,Osceola,12097
534,Orlando – Daytona Beach – Melbourne,FL,12,Seminole,12117
534,Orlando – Daytona Beach – Melbourne,FL,12,Sumter,12119
534,Orlando – Daytona Beach – Melbourne,FL,12,Volusia,12127
656,Panama City,FL,12,Bay,12005
656,Panama City,FL,12,Calhoun,12013
656,Panama City,FL,12,Franklin,12037
656,Panama City,FL,12,Gulf,12045
656,Panama City,FL,12,Holmes,12059
656,Panama City,FL,12,Jackson,12063
656,Panama City,FL,12,Liberty,12077
656,Panama City,FL,12,Walton,12131
656,Panama City,FL,12,Washington,12133
530,Tallahassee – Thomasville,FL,12,Gadsden,12039
530,Tallahassee – Thomasville,FL,12,Hamilton,12047
530,Tallahassee – Thomasville,FL,12,Jefferson,12065
530,Tallahassee – Thomasville,FL,12,Lafayette,12067
530,Tallahassee – Thomasville,FL,12,Leon,12073
530,Tallahassee – Thomasville,FL,12,Madison,12079
530,Tallahassee – Thomasville,FL,12,Suwannee,12121
530,Tallahassee – Thomasville,FL,12,Taylor,12123
530,Tallahassee – Thomasville,FL,12,Wakulla,12129
539,Tampa – St. Pete (Sarasota),FL,12,Citrus,12017
539,Tampa – St. Pete (Sarasota),FL,12,Hardee,12049
539,Tampa – St. Pete (Sarasota),FL,12,Hernando,12053
539,Tampa – St. Pete (Sarasota),FL,12,Highlands,12055
539,Tampa – St. Pete (Sarasota),FL,12,Hillsborough,12057
539,Tampa – St. Pete (Sarasota),FL,12,Manatee,12081
539,Tampa – St. Pete (Sarasota),FL,12,Pasco,12101
539,Tampa – St. Pete (Sarasota),FL,12,Pinellas,12103
539,Tampa – St. Pete (Sarasota),FL,12,Polk,12105
539,Tampa – St. Pete (Sarasota),FL,12,Sarasota,12115
548,West Palm Beach – Fort Pierce,FL,12,Indian River,12061
548,West Palm Beach – Fort Pierce,FL,12,Martin,12085
548,West Palm Beach – Fort Pierce,FL,12,Okeechobee,12093
548,West Palm Beach – Fort Pierce,FL,12,Palm Beach,12099
548,West Palm Beach – Fort Pierce,FL,12,St. Lucie,12111
525,"Albany, GA",GA,13,Atkinson,13003
525,"Albany, GA",GA,13,Baker,13007
525,"Albany, GA",GA,13,Ben Hill,13017
525,"Albany, GA",GA,13,Berrien,13019
525,"Albany, GA",GA,13,Calhoun,13037
525,"Albany, GA",GA,13,Coffee,13069
525,"Albany, GA",GA,13,Colquitt,13071
525,"Albany, GA",GA,13,Cook,13075
525,"Albany, GA",GA,13,Crisp,13081
525,"Albany, GA",GA,13,Dougherty,13095
525,"Albany, GA",GA,13,Irwin,13155
525,"Albany, GA",GA,13,Lee,13177
525,"Albany, GA",GA,13,Mitchell,13205
525,"Albany, GA",GA,13,Terrell,13273
525,"Albany, GA",GA,13,Tift,13277
525,"Albany, GA",GA,13,Turner,13287
525,"Albany, GA",GA,13,Worth,13321
524,Atlanta,GA,13,Banks,13011
524,Atlanta,GA,13,Barrow,13013
524,Atlanta,GA,13,Bartow,13015
524,Atlanta,GA,13,Butts,13035
524,Atlanta,GA,13,Carroll,13045
524,Atlanta,GA,13,Cherokee,13057
524,Atlanta,GA,13,Clarke,13059
524,Atlanta,GA,13,Clayton,13063
524,Atlanta,GA,13,Cobb,13067
524,Atlanta,GA,13,Coweta,13077
524,Atlanta,GA,13,Dawson,13085
524,Atlanta,GA,13,Dekalb,13089
524,Atlanta,GA,13,Douglas,13097
524,Atlanta,GA,13,Fayette,13113
524,Atlanta,GA,13,Floyd,13115
524,Atlanta,GA,13,Forsyth,13117
524,Atlanta,GA,13,Fulton,13121
524,Atlanta,GA,13,Gilmer,13123
524,Atlanta,GA,13,Gordon,13129
524,Atlanta,GA,13,Greene,13133
524,Atlanta,GA,13,Gwinnett,13135
524,Atlanta,GA,13,Habersham,13137
524,Atlanta,GA,13,Hall,13139
524,Atlanta,GA,13,Haralson,13143
524,Atlanta,GA,13,Heard,13149
524,Atlanta,GA,13,Henry,13151
524,Atlanta,GA,13,Jackson,13157
524,Atlanta,GA,13,Jasper,13159
524,Atlanta,GA,13,Lamar,13171
524,Atlanta,GA,13,Lumpkin,13187
524,Atlanta,GA,13,Madison,13195
524,Atlanta,GA,13,Meriwether,13199
524,Atlanta,GA,13,Morgan,13211
524,Atlanta,GA,13,Newton,13217
524,Atlanta,GA,13,Oconee,13219
524,Atlanta,GA,13,Oglethorpe,13221
524,Atlanta,GA,13,Paulding,13223
524,Atlanta,GA,13,Pickens,13227
524,Atlanta,GA,13,Pike,13231
524,Atlanta,GA,13,Polk,13233
524,Atlanta,GA,13,Putnam,13237
524,Atlanta,GA,13,Rabun,13241
524,Atlanta,GA,13,Rockdale,13247
524,Atlanta,GA,13,Spalding,13255
524,Atlanta,GA,13,Towns,13281
524,Atlanta,GA,13,Troup,13285
524,Atlanta,GA,13,Union,13291
524,Atlanta,GA,13,Upson,13293
524,Atlanta,GA,13,Walton,13297
524,Atlanta,GA,13,White,13311
520,Augusta – Aiken,GA,13,Burke,13033
520,Augusta – Aiken,GA,13,Columbia,13073
520,Augusta – Aiken,GA,13,Emanuel,13107
520,Augusta – Aiken,GA,13,Glascock,13125
520,Augusta – Aiken,GA,13,Jefferson,13163
520,Augusta – Aiken,GA,13,Jenkins,13165
520,Augusta – Aiken,GA,13,Lincoln,13181
520,Augusta – Aiken,GA,13,Mcduffie,13189
520,Augusta – Aiken,GA,13,Richmond,13245
520,Augusta – Aiken,GA,13,Taliaferro,13265
520,Augusta – Aiken,GA,13,Warren,13301
520,Augusta – Aiken,GA,13,Wilkes,13317
575,Chattanooga,GA,13,Catoosa,13047
575,Chattanooga,GA,13,Chattooga,13055
575,Chattanooga,GA,13,Dade,13083
575,Chattanooga,GA,13,Fannin,13111
575,Chattanooga,GA,13,Murray,13213
575,Chattanooga,GA,13,Walker,13295
575,Chattanooga,GA,13,Whitfield,13313
522,Columbus (GA),GA,13,Chattahoochee,13053
522,Columbus (GA),GA,13,Clay,13061
522,Columbus (GA),GA,13,Harris,13145
522,Columbus (GA),GA,13,Marion,13197
522,Columbus (GA),GA,13,Muscogee,13215
522,Columbus (GA),GA,13,Quitman,13239
522,Columbus (GA),GA,13,Randolph,13243
522,Columbus (GA),GA,13,Schley,13249
522,Columbus (GA),GA,13,Stewart,13259
522,Columbus (GA),GA,13,Sumter,13261
522,Columbus (GA),GA,13,Talbot,13263
522,Columbus (GA),GA,13,Taylor,13269
522,Columbus (GA),GA,13,Webster,13307
606,Dothan,GA,13,Early,13099
606,Dothan,GA,13,Seminole,13253
567,Greenville – Spartanburg – Asheville – Anderson,GA,13,Elbert,13105
567,Greenville – Spartanburg – Asheville – Anderson,GA,13,Franklin,13119
567,Greenville – Spartanburg – Asheville – Anderson,GA,13,Hart,13147
567,Greenville – Spartanburg – Asheville – Anderson,GA,13,Stephens,13257
561,Jacksonville,GA,13,Brantley,13025
561,Jacksonville,GA,13,Camden,13039
561,Jacksonville,GA,13,Charlton,13049
561,Jacksonville,GA,13,Glynn,13127
561,Jacksonville,GA,13,Pierce,13229
561,Jacksonville,GA,13,Ware,13299
503,Macon,GA,13,Baldwin,13009
503,Macon,GA,13,Bibb,13021
503,Macon,GA,13,Bleckley,13023
503,Macon,GA,13,Crawford,13079
503,Macon,GA,13,Dodge,13091
503,Macon,GA,13,Dooly,13093
503,Macon,GA,13,Hancock,13141
503,Macon,GA,13,Houston,13153
503,Macon,GA,13,Johnson,13167
503,Macon,GA,13,Jones,13169
503,Macon,GA,13,Laurens,13175
503,Macon,GA,13,Macon,13193
503,Macon,GA,13,Monroe,13207
503,Macon,GA,13,Peach,13225
503,Macon,GA,13,Pulaski,13235
503,Macon,GA,13,Telfair,13271
503,Macon,GA,13,Treutlen,13283
503,Macon,GA,13,Twiggs,13289
503,Macon,GA,13,Washington,13303
503,Macon,GA,13,Wheeler,13309
503,Macon,GA,13,Wilcox,13315
503,Macon,GA,13,Wilkinson,13319
507,Savannah,GA,13,Appling,13001
507,Savannah,GA,13,Bacon,13005
507,Savannah,GA,13,Bryan,13029
507,Savannah,GA,13,Bulloch,13031
507,Savannah,GA,13,Candler,13043
507,Savannah,GA,13,Chatham,13051
507,Savannah,GA,13,Effingham,13103
507,Savannah,GA,13,Evans,13109
507,Savannah,GA,13,Jeff Davis,13161
507,Savannah,GA,13,Liberty,13179
507,Savannah,GA,13,Long,13183
507,Savannah,GA,13,Mcintosh,13191
507,Savannah,GA,13,Montgomery,13209
507,Savannah,GA,13,Screven,13251
507,Savannah,GA,13,Tattnall,13267
507,Savannah,GA,13,Toombs,13279
507,Savannah,GA,13,Wayne,13305
530,Tallahassee – Thomasville,GA,13,Brooks,13027
530,Tallahassee – Thomasville,GA,13,Clinch,13065
530,Tallahassee – Thomasville,GA,13,Decatur,13087
530,Tallahassee – Thomasville,GA,13,Echols,13101
530,Tallahassee – Thomasville,GA,13,Grady,13131
530,Tallahassee – Thomasville,GA,13,Lanier,13173
530,Tallahassee – Thomasville,GA,13,Lowndes,13185
530,Tallahassee – Thomasville,GA,13,Miller,13201
530,Tallahassee – Thomasville,GA,13,Thomas,13275
744,Honolulu,HI,15,Hawaii,15001
744,Honolulu,HI,15,Honolulu,15003
744,Honolulu,HI,15,Kauai,15007
744,Honolulu,HI,15,Maui,15009
637,Cedar Rapids – Waterloo – Iowa City – Dubuque,IA,19,Allamakee,19005
637,Cedar Rapids – Waterloo – Iowa City – Dubuque,IA,19,Benton,19011
637,Cedar Rapids – Waterloo – Iowa City – Dubuque,IA,19,Black Hawk,19013
637,Cedar Rapids – Waterloo – Iowa City – Dubuque,IA,19,Bremer,19017
637,Cedar Rapids – Waterloo – Iowa City – Dubuque,IA,19,Buchanan,19019
637,Cedar Rapids – Waterloo – Iowa City – Dubuque,IA,19,Butler,19023
637,Cedar Rapids – Waterloo – Iowa City – Dubuque,IA,19,Cedar,19031
637,Cedar Rapids – Waterloo – Iowa City – Dubuque,IA,19,Chickasaw,19037
637,Cedar Rapids – Waterloo – Iowa City – Dubuque,IA,19,Clayton,19043
637,Cedar Rapids – Waterloo – Iowa City – Dubuque,IA,19,Delaware,19055
637,Cedar Rapids – Waterloo – Iowa City – Dubuque,IA,19,Dubuque,19061
637,Cedar Rapids – Waterloo – Iowa City – Dubuque,IA,19,Fayette,19065
637,Cedar Rapids – Waterloo – Iowa City – Dubuque,IA,19,Grundy,19075
637,Cedar Rapids – Waterloo – Iowa City – Dubuque,IA,19,Iowa,19095
637,Cedar Rapids – Waterloo – Iowa City – Dubuque,IA,19,Johnson,19103
637,Cedar Rapids – Waterloo – Iowa City – Dubuque,IA,19,Jones,19105
637,Cedar Rapids – Waterloo – Iowa City – Dubuque,IA,19,Keokuk,19107
637,Cedar Rapids – Waterloo – Iowa City – Dubuque,IA,19,Linn,19113
637,Cedar Rapids – Waterloo – Iowa City – Dubuque,IA,19,Tama,19171
637,Cedar Rapids – Waterloo – Iowa City – Dubuque,IA,19,Washington,19183
637,Cedar Rapids – Waterloo – Iowa City – Dubuque,IA,19,Winneshiek,19191
682,Davenport – Rock Island – Moline,IA,19,Clinton,19045
682,Davenport – Rock Island – Moline,IA,19,Des Moines,19057
682,Davenport – Rock Island – Moline,IA,19,Henry,19087
682,Davenport – Rock Island – Moline,IA,19,Jackson,19097
682,Davenport – Rock Island – Moline,IA,19,Louisa,19115
682,Davenport – Rock Island – Moline,IA,19,Muscatine,19139
682,Davenport – Rock Island – Moline,IA,19,Scott,19163
679,Des Moines – Ames,IA,19,Adair,19001
679,Des Moines – Ames,IA,19,Adams,19003
679,Des Moines – Ames,IA,19,Appanoose,19007
679,Des Moines – Ames,IA,19,Audubon,19009
679,Des Moines – Ames,IA,19,Boone,19015
679,Des Moines – Ames,IA,19,Calhoun,19025
679,Des Moines – Ames,IA,19,Carroll,19027
679,Des Moines – Ames,IA,19,Clarke,19039
679,Des Moines – Ames,IA,19,Dallas,19049
679,Des Moines – Ames,IA,19,Decatur,19053
679,Des Moines – Ames,IA,19,Franklin,19069
679,Des Moines – Ames,IA,19,Greene,19073
679,Des Moines – Ames,IA,19,Guthrie,19077
679,Des Moines – Ames,IA,19,Hamilton,19079
679,Des Moines – Ames,IA,19,Hardin,19083
679,Des Moines – Ames,IA,19,Humboldt,19091
679,Des Moines – Ames,IA,19,Jasper,19099
679,Des Moines – Ames,IA,19,Kossuth,19109
679,Des Moines – Ames,IA,19,Lucas,19117
679,Des Moines – Ames,IA,19,Madison,19121
679,Des Moines – Ames,IA,19,Mahaska,19123
679,Des Moines – Ames,IA,19,Marion,19125
679,Des Moines – Ames,IA,19,Marshall,19127
679,Des Moines – Ames,IA,19,Monroe,19135
679,Des Moines – Ames,IA,19,Pocahontas,19151
679,Des Moines – Ames,IA,19,Polk,19153
679,Des Moines – Ames,IA,19,Poweshiek,19157
679,Des Moines – Ames,IA,19,Ringgold,19159
679,Des Moines – Ames,IA,19,Story,19169
679,Des Moines – Ames,IA,19,Taylor,19173
679,Des Moines – Ames,IA,19,Union,19175
679,Des Moines – Ames,IA,19,Warren,19181
679,Des Moines – Ames,IA,19,Wayne,19185
679,Des Moines – Ames,IA,19,Webster,19187
679,Des Moines – Ames,IA,19,Wright,19197
652,Omaha,IA,19,Cass,19029
652,Omaha,IA,19,Crawford,19047
652,Omaha,IA,19,Fremont,19071
652,Omaha,IA,19,Harrison,19085
652,Omaha,IA,19,Mills,19129
652,Omaha,IA,19,Montgomery,19137
652,Omaha,IA,19,Page,19145
652,Omaha,IA,19,Pottawattamie,19155
652,Omaha,IA,19,Shelby,19165
631,Ottumwa – Kirksville,IA,19,Davis,19051
631,Ottumwa – Kirksville,IA,19,Jefferson,19101
631,Ottumwa – Kirksville,IA,19,Van Buren,19177
631,Ottumwa – Kirksville,IA,19,Wapello,19179
717,Quincy – Hannibal – Keokuk,IA,19,Lee,19111
611,Rochester – Mason City – Austin,IA,19,Cerro Gordo,19033
611,Rochester – Mason City – Austin,IA,19,Floyd,19067
611,Rochester – Mason City – Austin,IA,19,Hancock,19081
611,Rochester – Mason City – Austin,IA,19,Howard,19089
611,Rochester – Mason City – Austin,IA,19,Mitchell,19131
611,Rochester – Mason City – Austin,IA,19,Winnebago,19189
611,Rochester – Mason City – Austin,IA,19,Worth,19195
624,Sioux City,IA,19,Buena Vista,19021
624,Sioux City,IA,19,Cherokee,19035
624,Sioux City,IA,19,Clay,19041
624,Sioux City,IA,19,Dickinson,19059
624,Sioux City,IA,19,Emmet,19063
624,Sioux City,IA,19,Ida,19093
624,Sioux City,IA,19,Monona,19133
624,Sioux City,IA,19,O'Brien,19141
624,Sioux City,IA,19,Palo Alto,19147
624,Sioux City,IA,19,Plymouth,19149
624,Sioux City,IA,19,Sac,19161
624,Sioux City,IA,19,Sioux,19167
624,Sioux City,IA,19,Woodbury,19193
725,Sioux Falls (Mitchell),IA,19,Lyon,19119
725,Sioux Falls (Mitchell),IA,19,Osceola,19143
757,Boise,ID,16,Ada,16001
757,Boise,ID,16,Adams,16003
757,Boise,ID,16,Boise,16015
757,Boise,ID,16,Camas,16025
757,Boise,ID,16,Canyon,16027
757,Boise,ID,16,Elmore,16039
757,Boise,ID,16,Gem,16045
757,Boise,ID,16,Owyhee,16073
757,Boise,ID,16,Payette,16075
757,Boise,ID,16,Valley,16085
757,Boise,ID,16,Washington,16087
758,Idaho Falls – Pocatello,ID,16,Bannock,16005
758,Idaho Falls – Pocatello,ID,16,Bingham,16011
758,Idaho Falls – Pocatello,ID,16,Bonneville,16019
758,Idaho Falls – Pocatello,ID,16,Butte,16023
758,Idaho Falls – Pocatello,ID,16,Caribou,16029
758,Idaho Falls – Pocatello,ID,16,Clark,16033
758,Idaho Falls – Pocatello,ID,16,Custer,16037
758,Idaho Falls – Pocatello,ID,16,Fremont,16043
758,Idaho Falls – Pocatello,ID,16,Jefferson,16051
758,Idaho Falls – Pocatello,ID,16,Lemhi,16059
758,Idaho Falls – Pocatello,ID,16,Madison,16065
758,Idaho Falls – Pocatello,ID,16,Power,16077
758,Idaho Falls – Pocatello,ID,16,Teton,16081
770,Salt Lake City,ID,16,Bear Lake,16007
770,Salt Lake City,ID,16,Franklin,16041
770,Salt Lake City,ID,16,Oneida,16071
881,Spokane,ID,16,Benewah,16009
881,Spokane,ID,16,Bonner,16017
881,Spokane,ID,16,Boundary,16021
881,Spokane,ID,16,Clearwater,16035
881,Spokane,ID,16,Idaho,16049
881,Spokane,ID,16,Kootenai,16055
881,Spokane,ID,16,Latah,16057
881,Spokane,ID,16,Lewis,16061
881,Spokane,ID,16,Nez Perce,16069
881,Spokane,ID,16,Shoshone,16079
760,Twin Falls,ID,16,Blaine,16013
760,Twin Falls,ID,16,Cassia,16031
760,Twin Falls,ID,16,Gooding,16047
760,Twin Falls,ID,16,Jerome,16053
760,Twin Falls,ID,16,Lincoln,16063
760,Twin Falls,ID,16,Minidoka,16067
760,Twin Falls,ID,16,Twin Falls,16083
648,Champaign – Springfield – Decatur,IL,17,Champaign,17019
648,Champaign – Springfield – Decatur,IL,17,Christian,17021
648,Champaign – Springfield – Decatur,IL,17,Coles,17029
648,Champaign – Springfield – Decatur,IL,17,Cumberland,17035
648,Champaign – Springfield – Decatur,IL,17,De Witt,17039
648,Champaign – Springfield – Decatur,IL,17,Douglas,17041
648,Champaign – Springfield – Decatur,IL,17,Edgar,17045
648,Champaign – Springfield – Decatur,IL,17,Effingham,17049
648,Champaign – Springfield – Decatur,IL,17,Ford,17053
648,Champaign – Springfield – Decatur,IL,17,Iroquois,17075
648,Champaign – Springfield – Decatur,IL,17,Logan,17107
648,Champaign – Springfield – Decatur,IL,17,Macon,17115
648,Champaign – Springfield – Decatur,IL,17,Menard,17129
648,Champaign – Springfield – Decatur,IL,17,Morgan,17137
648,Champaign – Springfield – Decatur,IL,17,Moultrie,17139
648,Champaign – Springfield – Decatur,IL,17,Piatt,17147
648,Champaign – Springfield – Decatur,IL,17,Sangamon,17167
648,Champaign – Springfield – Decatur,IL,17,Shelby,17173
648,Champaign – Springfield – Decatur,IL,17,Vermilion,17183
602,Chicago,IL,17,Cook,17031
602,Chicago,IL,17,Dekalb,17037
602,Chicago,IL,17,Dupage,17043
602,Chicago,IL,17,Grundy,17063
602,Chicago,IL,17,Kane,17089
602,Chicago,IL,17,Kankakee,17091
602,Chicago,IL,17,Kendall,17093
602,Chicago,IL,17,Lake,17097
602,Chicago,IL,17,La Salle,17099
602,Chicago,IL,17,Mchenry,17111
602,Chicago,IL,17,Will,17197
682,Davenport – Rock Island – Moline,IL,17,Bureau,17011
682,Davenport – Rock Island – Moline,IL,17,Carroll,17015
682,Davenport – Rock Island – Moline,IL,17,Henderson,17071
682,Davenport – Rock Island – Moline,IL,17,Henry,17073
682,Davenport – Rock Island – Moline,IL,17,Jo Daviess,17085
682,Davenport – Rock Island – Moline,IL,17,Knox,17095
682,Davenport – Rock Island – Moline,IL,17,Mercer,17131
682,Davenport – Rock Island – Moline,IL,17,Rock Island,17161
682,Davenport – Rock Island – Moline,IL,17,Warren,17187
682,Davenport – Rock Island – Moline,IL,17,Whiteside,17195
649,Evansville,IL,17,Edwards,17047
649,Evansville,IL,17,Wabash,17185
649,Evansville,IL,17,Wayne,17191
649,Evansville,IL,17,White,17193
632,Paducah – Cape Girardeau – Harrisburg,IL,17,Alexander,17003
632,Paducah – Cape Girardeau – Harrisburg,IL,17,Franklin,17055
632,Paducah – Cape Girardeau – Harrisburg,IL,17,Gallatin,17059
632,Paducah – Cape Girardeau – Harrisburg,IL,17,Hamilton,17065
632,Paducah – Cape Girardeau – Harrisburg,IL,17,Hardin,17069
632,Paducah – Cape Girardeau – Harrisburg,IL,17,Jackson,17077
632,Paducah – Cape Girardeau – Harrisburg,IL,17,Jefferson,17081
632,Paducah – Cape Girardeau – Harrisburg,IL,17,Johnson,17087
632,Paducah – Cape Girardeau – Harrisburg,IL,17,Massac,17127
632,Paducah – Cape Girardeau – Harrisburg,IL,17,Perry,17145
632,Paducah – Cape Girardeau – Harrisburg,IL,17,Pope,17151
632,Paducah – Cape Girardeau – Harrisburg,IL,17,Pulaski,17153
632,Paducah – Cape Girardeau – Harrisburg,IL,17,Saline,17165
632,Paducah – Cape Girardeau – Harrisburg,IL,17,Union,17181
632,Paducah – Cape Girardeau – Harrisburg,IL,17,Williamson,17199
675,Peoria – Bloomington,IL,17,Fulton,17057
675,Peoria – Bloomington,IL,17,Livingston,17105
675,Peoria – Bloomington,IL,17,Mclean,17113
675,Peoria – Bloomington,IL,17,Marshall,17123
675,Peoria – Bloomington,IL,17,Mason,17125
675,Peoria – Bloomington,IL,17,Peoria,17143
675,Peoria – Bloomington,IL,17,Putnam,17155
675,Peoria – Bloomington,IL,17,Stark,17175
675,Peoria – Bloomington,IL,17,Tazewell,17179
675,Peoria – Bloomington,IL,17,Woodford,17203
717,Quincy – Hannibal – Keokuk,IL,17,Adams,17001
717,Quincy – Hannibal – Keokuk,IL,17,Brown,17009
717,Quincy – Hannibal – Keokuk,IL,17,Cass,17017
717,Quincy – Hannibal – Keokuk,IL,17,Hancock,17067
717,Quincy – Hannibal – Keokuk,IL,17,Mcdonough,17109
717,Quincy – Hannibal – Keokuk,IL,17,Pike,17149
717,Quincy – Hannibal – Keokuk,IL,17,Schuyler,17169
717,Quincy – Hannibal – Keokuk,IL,17,Scott,17171
610,Rockford,IL,17,Boone,17007
610,Rockford,IL,17,Lee,17103
610,Rockford,IL,17,Ogle,17141
610,Rockford,IL,17,Stephenson,17177
610,Rockford,IL,17,Winnebago,17201
609,St. Louis,IL,17,Bond,17005
609,St. Louis,IL,17,Calhoun,17013
609,St. Louis,IL,17,Clinton,17027
609,St. Louis,IL,17,Fayette,17051
609,St. Louis,IL,17,Greene,17061
609,St. Louis,IL,17,Jersey,17083
609,St. Louis,IL,17,Macoupin,17117
609,St. Louis,IL,17,Madison,17119
609,St. Louis,IL,17,Marion,17121
609,St. Louis,IL,17,Monroe,17133
609,St. Louis,IL,17,Montgomery,17135
609,St. Louis,IL,17,Randolph,17157
609,St. Louis,IL,17,St. Clair,17163
609,St. Louis,IL,17,Washington,17189
581,Terre Haute,IL,17,Clark,17023
581,Terre Haute,IL,17,Clay,17025
581,Terre Haute,IL,17,Crawford,17033
581,Terre Haute,IL,17,Jasper,17079
581,Terre Haute,IL,17,Lawrence,17101
581,Terre Haute,IL,17,Richland,17159
648,Champaign – Springfield – Decatur,IN,18,Warren,18171
602,Chicago,IN,18,Jasper,18073
602,Chicago,IN,18,Lake,18089
602,Chicago,IN,18,La Porte,18091
602,Chicago,IN,18,Newton,18111
602,Chicago,IN,18,Porter,18127
515,Cincinnati,IN,18,Dearborn,18029
515,Cincinnati,IN,18,Franklin,18047
515,Cincinnati,IN,18,Ohio,18115
515,Cincinnati,IN,18,Ripley,18137
515,Cincinnati,IN,18,Switzerland,18155
515,Cincinnati,IN,18,Union,18161
542,Dayton,IN,18,Wayne,18177
649,Evansville,IN,18,Dubois,18037
649,Evansville,IN,18,Gibson,18051
649,Evansville,IN,18,Perry,18123
649,Evansville,IN,18,Pike,18125
649,Evansville,IN,18,Posey,18129
649,Evansville,IN,18,Spencer,18147
649,Evansville,IN,18,Vanderburgh,18163
649,Evansville,IN,18,Warrick,18173
509,Fort Wayne,IN,18,Adams,18001
509,Fort Wayne,IN,18,Allen,18003
509,Fort Wayne,IN,18,De Kalb,18033
509,Fort Wayne,IN,18,Huntington,18069
509,Fort Wayne,IN,18,Jay,18075
509,Fort Wayne,IN,18,Noble,18113
509,Fort Wayne,IN,18,Steuben,18151
509,Fort Wayne,IN,18,Wabash,18169
509,Fort Wayne,IN,18,Wells,18179
509,Fort Wayne,IN,18,Whitley,18183
527,Indianapolis,IN,18,Bartholomew,18005
527,Indianapolis,IN,18,Blackford,18009
527,Indianapolis,IN,18,Boone,18011
527,Indianapolis,IN,18,Brown,18013
527,Indianapolis,IN,18,Carroll,18015
527,Indianapolis,IN,18,Cass,18017
527,Indianapolis,IN,18,Clinton,18023
527,Indianapolis,IN,18,Decatur,18031
527,Indianapolis,IN,18,Delaware,18035
527,Indianapolis,IN,18,Fayette,18041
527,Indianapolis,IN,18,Fountain,18045
527,Indianapolis,IN,18,Grant,18053
527,Indianapolis,IN,18,Hamilton,18057
527,Indianapolis,IN,18,Hancock,18059
527,Indianapolis,IN,18,Hendricks,18063
527,Indianapolis,IN,18,Henry,18065
527,Indianapolis,IN,18,Howard,18067
527,Indianapolis,IN,18,Johnson,18081
527,Indianapolis,IN,18,Lawrence,18093
527,Indianapolis,IN,18,Madison,18095
527,Indianapolis,IN,18,Marion,18097
527,Indianapolis,IN,18,Miami,18103
527,Indianapolis,IN,18,Monroe,18105
527,Indianapolis,IN,18,Montgomery,18107
527,Indianapolis,IN,18,Morgan,18109
527,Indianapolis,IN,18,Owen,18119
527,Indianapolis,IN,18,Putnam,18133
527,Indianapolis,IN,18,Randolph,18135
527,Indianapolis,IN,18,Rush,18139
527,Indianapolis,IN,18,Shelby,18145
527,Indianapolis,IN,18,Tipton,18159
527,Indianapolis,IN,18,White,18181
582,"Lafayette, IN",IN,18,Benton,18007
582,"Lafayette, IN",IN,18,Tippecanoe,18157
529,Louisville,IN,18,Clark,18019
529,Louisville,IN,18,Crawford,18025
529,Louisville,IN,18,Floyd,18043
529,Louisville,IN,18,Harrison,18061
529,Louisville,IN,18,Jackson,18071
529,Louisville,IN,18,Jefferson,18077
529,Louisville,IN,18,Jennings,18079
529,Louisville,IN,18,Orange,18117
529,Louisville,IN,18,Scott,18143
529,Louisville,IN,18,Washington,18175
588,South Bend – Elkhart,IN,18,Elkhart,18039
588,South Bend – Elkhart,IN,18,Fulton,18049
588,South Bend – Elkhart,IN,18,Kosciusko,18085
588,South Bend – Elkhart,IN,18,Lagrange,18087
588,South Bend – Elkhart,IN,18,Marshall,18099
588,South Bend – Elkhart,IN,18,Pulaski,18131
588,South Bend – Elkhart,IN,18,St. Joseph,18141
588,South Bend – Elkhart,IN,18,Starke,18149
581,Terre Haute,IN,18,Clay,18021
581,Terre Haute,IN,18,Daviess,18027
581,Terre Haute,IN,18,Greene,18055
581,Terre Haute,IN,18,Knox,18083
581,Terre Haute,IN,18,Martin,18101
581,Terre Haute,IN,18,Parke,18121
581,Terre Haute,IN,18,Sullivan,18153
581,Terre Haute,IN,18,Vermillion,18165
581,Terre Haute,IN,18,Vigo,18167
603,Joplin – Pittsburg,KS,20,Allen,20001
603,Joplin – Pittsburg,KS,20,Bourbon,20011
603,Joplin – Pittsburg,KS,20,Cherokee,20021
603,Joplin – Pittsburg,KS,20,Crawford,20037
603,Joplin – Pittsburg,KS,20,Labette,20099
603,Joplin – Pittsburg,KS,20,Neosho,20133
603,Joplin – Pittsburg,KS,20,Wilson,20205
603,Joplin – Pittsburg,KS,20,Woodson,20207
616,Kansas City,KS,20,Anderson,20003
616,Kansas City,KS,20,Atchison,20005
616,Kansas City,KS,20,Douglas,20045
616,Kansas City,KS,20,Franklin,20059
616,Kansas City,KS,20,Johnson,20091
616,Kansas City,KS,20,Leavenworth,20103
616,Kansas City,KS,20,Linn,20107
616,Kansas City,KS,20,Miami,20121
616,Kansas City,KS,20,Wyandotte,20209
722,Lincoln & Hastings – Kearney,KS,20,Jewell,20089
722,Lincoln & Hastings – Kearney,KS,20,Phillips,20147
722,Lincoln & Hastings – Kearney,KS,20,Republic,20157
722,Lincoln & Hastings – Kearney,KS,20,Smith,20183
638,St. Joseph,KS,20,Doniphan,20043
605,Topeka,KS,20,Brown,20013
605,Topeka,KS,20,Clay,20027
605,Topeka,KS,20,Cloud,20029
605,Topeka,KS,20,Coffey,20031
605,Topeka,KS,20,Geary,20061
605,Topeka,KS,20,Jackson,20085
605,Topeka,KS,20,Jefferson,20087
605,Topeka,KS,20,Lyon,20111
605,Topeka,KS,20,Marshall,20117
605,Topeka,KS,20,Morris,20127
605,Topeka,KS,20,Nemaha,20131
605,Topeka,KS,20,Osage,20139
605,Topeka,KS,20,Pottawatomie,20149
605,Topeka,KS,20,Riley,20161
605,Topeka,KS,20,Shawnee,20177
605,Topeka,KS,20,Wabaunsee,20197
605,Topeka,KS,20,Washington,20201
671,Tulsa,KS,20,Chautauqua,20019
671,Tulsa,KS,20,Montgomery,20125
678,Wichita – Hutchinson Plus,KS,20,Barber,20007
678,Wichita – Hutchinson Plus,KS,20,Barton,20009
678,Wichita – Hutchinson Plus,KS,20,Butler,20015
678,Wichita – Hutchinson Plus,KS,20,Chase,20017
678,Wichita – Hutchinson Plus,KS,20,Cheyenne,20023
678,Wichita – Hutchinson Plus,KS,20,Clark,20025
678,Wichita – Hutchinson Plus,KS,20,Comanche,20033
678,Wichita – Hutchinson Plus,KS,20,Cowley,20035
678,Wichita – Hutchinson Plus,KS,20,Decatur,20039
678,Wichita – Hutchinson Plus,KS,20,Dickinson,20041
678,Wichita – Hutchinson Plus,KS,20,Edwards,20047
678,Wichita – Hutchinson Plus,KS,20,Elk,20049
678,Wichita – Hutchinson Plus,KS,20,Ellis,20051
678,Wichita – Hutchinson Plus,KS,20,Ellsworth,20053
678,Wichita – Hutchinson Plus,KS,20,Finney,20055
678,Wichita – Hutchinson Plus,KS,20,Ford,20057
678,Wichita – Hutchinson Plus,KS,20,Gove,20063
678,Wichita – Hutchinson Plus,KS,20,Graham,20065
678,Wichita – Hutchinson Plus,KS,20,Grant,20067
678,Wichita – Hutchinson Plus,KS,20,Gray,20069
678,Wichita – Hutchinson Plus,KS,20,Greeley,20071
678,Wichita – Hutchinson Plus,KS,20,Greenwood,20073
678,Wichita – Hutchinson Plus,KS,20,Hamilton,20075
678,Wichita – Hutchinson Plus,KS,20,Harper,20077
678,Wichita – Hutchinson Plus,KS,20,Harvey,20079
678,Wichita – Hutchinson Plus,KS,20,Haskell,20081
678,Wichita – Hutchinson Plus,KS,20,Hodgeman,20083
678,Wichita – Hutchinson Plus,KS,20,Kearny,20093
678,Wichita – Hutchinson Plus,KS,20,Kingman,20095
678,Wichita – Hutchinson Plus,KS,20,Kiowa,20097
678,Wichita – Hutchinson Plus,KS,20,Lane,20101
678,Wichita – Hutchinson Plus,KS,20,Lincoln,20105
678,Wichita – Hutchinson Plus,KS,20,Logan,20109
678,Wichita – Hutchinson Plus,KS,20,Mcpherson,20113
678,Wichita – Hutchinson Plus,KS,20,Marion,20115
678,Wichita – Hutchinson Plus,KS,20,Meade,20119
678,Wichita – Hutchinson Plus,KS,20,Mitchell,20123
678,Wichita – Hutchinson Plus,KS,20,Morton,20129
678,Wichita – Hutchinson Plus,KS,20,Ness,20135
678,Wichita – Hutchinson Plus,KS,20,Norton,20137
678,Wichita – Hutchinson Plus,KS,20,Osborne,20141
678,Wichita – Hutchinson Plus,KS,20,Ottawa,20143
678,Wichita – Hutchinson Plus,KS,20,Pawnee,20145
678,Wichita – Hutchinson Plus,KS,20,Pratt,20151
678,Wichita – Hutchinson Plus,KS,20,Rawlins,20153
678,Wichita – Hutchinson Plus,KS,20,Reno,20155
678,Wichita – Hutchinson Plus,KS,20,Rice,20159
678,Wichita – Hutchinson Plus,KS,20,Rooks,20163
678,Wichita – Hutchinson Plus,KS,20,Rush,20165
678,Wichita – Hutchinson Plus,KS,20,Russell,20167
678,Wichita – Hutchinson Plus,KS,20,Saline,20169
678,Wichita – Hutchinson Plus,KS,20,Scott,20171
678,Wichita – Hutchinson Plus,KS,20,Sedgwick,20173
678,Wichita – Hutchinson Plus,KS,20,Seward,20175
678,Wichita – Hutchinson Plus,KS,20,Sheridan,20179
678,Wichita – Hutchinson Plus,KS,20,Sherman,20181
678,Wichita – Hutchinson Plus,KS,20,Stafford,20185
678,Wichita – Hutchinson Plus,KS,20,Stanton,20187
678,Wichita – Hutchinson Plus,KS,20,Stevens,20189
678,Wichita – Hutchinson Plus,KS,20,Sumner,20191
678,Wichita – Hutchinson Plus,KS,20,Thomas,20193
678,Wichita – Hutchinson Plus,KS,20,Trego,20195
678,Wichita – Hutchinson Plus,KS,20,Wallace,20199
678,Wichita – Hutchinson Plus,KS,20,Wichita,20203
736,Bowling Green,KY,21,Adair,21001
736,Bowling Green,KY,21,Barren,21009
736,Bowling Green,KY,21,Butler,21031
736,Bowling Green,KY,21,Cumberland,21057
736,Bowling Green,KY,21,Edmonson,21061
736,Bowling Green,KY,21,Hart,21099
736,Bowling Green,KY,21,Metcalfe,21169
736,Bowling Green,KY,21,Warren,21227
564,Charleston – Huntington,KY,21,Boyd,21019
564,Charleston – Huntington,KY,21,Carter,21043
564,Charleston – Huntington,KY,21,Elliott,21063
564,Charleston – Huntington,KY,21,Floyd,21071
564,Charleston – Huntington,KY,21,Greenup,21089
564,Charleston – Huntington,KY,21,Johnson,21115
564,Charleston – Huntington,KY,21,Lawrence,21127
564,Charleston – Huntington,KY,21,Lewis,21135
564,Charleston – Huntington,KY,21,Magoffin,21153
564,Charleston – Huntington,KY,21,Martin,21159
564,Charleston – Huntington,KY,21,Pike,21195
515,Cincinnati,KY,21,Boone,21015
515,Cincinnati,KY,21,Bracken,21023
515,Cincinnati,KY,21,Campbell,21037
515,Cincinnati,KY,21,Gallatin,21077
515,Cincinnati,KY,21,Grant,21081
515,Cincinnati,KY,21,Kenton,21117
515,Cincinnati,KY,21,Mason,21161
515,Cincinnati,KY,21,Owen,21187
515,Cincinnati,KY,21,Pendleton,21191
515,Cincinnati,KY,21,Robertson,21201
649,Evansville,KY,21,Daviess,21059
649,Evansville,KY,21,Hancock,21091
649,Evansville,KY,21,Henderson,21101
649,Evansville,KY,21,Hopkins,21107
649,Evansville,KY,21,Mclean,21149
649,Evansville,KY,21,Muhlenberg,21177
649,Evansville,KY,21,Ohio,21183
649,Evansville,KY,21,Union,21225
649,Evansville,KY,21,Webster,21233
531,"Tri-Cities, TN-VA",KY,21,Leslie,21131
531,"Tri-Cities, TN-VA",KY,21,Letcher,21133
557,Knoxville,KY,21,Bell,21013
557,Knoxville,KY,21,Harlan,21095
557,Knoxville,KY,21,Mccreary,21147
541,Lexington,KY,21,Anderson,21005
541,Lexington,KY,21,Bath,21011
541,Lexington,KY,21,Bourbon,21017
541,Lexington,KY,21,Boyle,21021
541,Lexington,KY,21,Breathitt,21025
541,Lexington,KY,21,Casey,21045
541,Lexington,KY,21,Clark,21049
541,Lexington,KY,21,Clay,21051
541,Lexington,KY,21,Estill,21065
541,Lexington,KY,21,Fayette,21067
541,Lexington,KY,21,Fleming,21069
541,Lexington,KY,21,Franklin,21073
541,Lexington,KY,21,Garrard,21079
541,Lexington,KY,21,Harrison,21097
541,Lexington,KY,21,Jackson,21109
541,Lexington,KY,21,Jessamine,21113
541,Lexington,KY,21,Knott,21119
541,Lexington,KY,21,Knox,21121
541,Lexington,KY,21,Laurel,21125
541,Lexington,KY,21,Lee,21129
541,Lexington,KY,21,Lincoln,21137
541,Lexington,KY,21,Madison,21151
541,Lexington,KY,21,Menifee,21165
541,Lexington,KY,21,Mercer,21167
541,Lexington,KY,21,Montgomery,21173
541,Lexington,KY,21,Morgan,21175
541,Lexington,KY,21,Nicholas,21181
541,Lexington,KY,21,Owsley,21189
541,Lexington,KY,21,Perry,21193
541,Lexington,KY,21,Powell,21197
541,Lexington,KY,21,Pulaski,21199
541,Lexington,KY,21,Rockcastle,21203
541,Lexington,KY,21,Rowan,21205
541,Lexington,KY,21,Russell,21207
541,Lexington,KY,21,Scott,21209
541,Lexington,KY,21,Wayne,21231
541,Lexington,KY,21,Whitley,21235
541,Lexington,KY,21,Wolfe,21237
541,Lexington,KY,21,Woodford,21239
529,Louisville,KY,21,Breckinridge,21027
529,Louisville,KY,21,Bullitt,21029
529,Louisville,KY,21,Carroll,21041
529,Louisville,KY,21,Grayson,21085
529,Louisville,KY,21,Green,21087
529,Louisville,KY,21,Hardin,21093
529,Louisville,KY,21,Henry,21103
529,Louisville,KY,21,Jefferson,21111
529,Louisville,KY,21,Larue,21123
529,Louisville,KY,21,Marion,21155
529,Louisville,KY,21,Meade,21163
529,Louisville,KY,21,Nelson,21179
529,Louisville,KY,21,Oldham,21185
529,Louisville,KY,21,Shelby,21211
529,Louisville,KY,21,Spencer,21215
529,Louisville,KY,21,Taylor,21217
529,Louisville,KY,21,Trimble,21223
529,Louisville,KY,21,Washington,21229
659,Nashville,KY,21,Allen,21003
659,Nashville,KY,21,Christian,21047
659,Nashville,KY,21,Clinton,21053
659,Nashville,KY,21,Logan,21141
659,Nashville,KY,21,Monroe,21171
659,Nashville,KY,21,Simpson,21213
659,Nashville,KY,21,Todd,21219
659,Nashville,KY,21,Trigg,21221
632,Paducah – Cape Girardeau – Harrisburg,KY,21,Ballard,21007
632,Paducah – Cape Girardeau – Harrisburg,KY,21,Caldwell,21033
632,Paducah – Cape Girardeau – Harrisburg,KY,21,Calloway,21035
632,Paducah – Cape Girardeau – Harrisburg,KY,21,Carlisle,21039
632,Paducah – Cape Girardeau – Harrisburg,KY,21,Crittenden,21055
632,Paducah – Cape Girardeau – Harrisburg,KY,21,Fulton,21075
632,Paducah – Cape Girardeau – Harrisburg,KY,21,Graves,21083
632,Paducah – Cape Girardeau – Harrisburg,KY,21,Hickman,21105
632,Paducah – Cape Girardeau – Harrisburg,KY,21,Livingston,21139
632,Paducah – Cape Girardeau – Harrisburg,KY,21,Lyon,21143
632,Paducah – Cape Girardeau – Harrisburg,KY,21,Mccracken,21145
632,Paducah – Cape Girardeau – Harrisburg,KY,21,Marshall,21157
644,"Alexandria, LA",LA,22,Avoyelles Parish,22009
644,"Alexandria, LA",LA,22,Grant Parish,22043
644,"Alexandria, LA",LA,22,Rapides Parish,22079
644,"Alexandria, LA",LA,22,Vernon Parish,22115
716,Baton Rouge,LA,22,Ascension Parish,22005
716,Baton Rouge,LA,22,Assumption Parish,22007
716,Baton Rouge,LA,22,East Baton Rouge Parish,22033
716,Baton Rouge,LA,22,East Feliciana Parish,22037
716,Baton Rouge,LA,22,Iberville Parish,22047
716,Baton Rouge,LA,22,Livingston Parish,22063
716,Baton Rouge,LA,22,Pointe Coupee Parish,22077
716,Baton Rouge,LA,22,St. Helena Parish,22091
716,Baton Rouge,LA,22,St. Mary Parish,22101
716,Baton Rouge,LA,22,West Baton Rouge Parish,22121
716,Baton Rouge,LA,22,West Feliciana Parish,22125
642,"Lafayette, LA",LA,22,Acadia Parish,22001
642,"Lafayette, LA",LA,22,Evangeline Parish,22039
642,"Lafayette, LA",LA,22,Iberia Parish,22045
642,"Lafayette, LA",LA,22,Jefferson Davis Parish,22053
642,"Lafayette, LA",LA,22,Lafayette Parish,22055
642,"Lafayette, LA",LA,22,St. Landry Parish,22097
642,"Lafayette, LA",LA,22,St. Martin Parish,22099
642,"Lafayette, LA",LA,22,Vermilion Parish,22113
643,Lake Charles,LA,22,Allen Parish,22003
643,Lake Charles,LA,22,Beauregard Parish,22011
643,Lake Charles,LA,22,Calcasieu Parish,22019
643,Lake Charles,LA,22,Cameron Parish,22023
628,Monroe – El Dorado,LA,22,Caldwell Parish,22021
628,Monroe – El Dorado,LA,22,Catahoula Parish,22025
628,Monroe – El Dorado,LA,22,Concordia Parish,22029
628,Monroe – El Dorado,LA,22,East Carroll Parish,22035
628,Monroe – El Dorado,LA,22,Franklin Parish,22041
628,Monroe – El Dorado,LA,22,Jackson Parish,22049
628,Monroe – El Dorado,LA,22,La Salle Parish,22059
628,Monroe – El Dorado,LA,22,Lincoln Parish,22061
628,Monroe – El Dorado,LA,22,Madison Parish,22065
628,Monroe – El Dorado,LA,22,Morehouse Parish,22067
628,Monroe – El Dorado,LA,22,Ouachita Parish,22073
628,Monroe – El Dorado,LA,22,Richland Parish,22083
628,Monroe – El Dorado,LA,22,Tensas Parish,22107
628,Monroe – El Dorado,LA,22,Union Parish,22111
628,Monroe – El Dorado,LA,22,West Carroll Parish,22123
628,Monroe – El Dorado,LA,22,Winn Parish,22127
622,New Orleans,LA,22,Jefferson Parish,22051
622,New Orleans,LA,22,Lafourche Parish,22057
622,New Orleans,LA,22,Orleans Parish,22071
622,New Orleans,LA,22,Plaquemines Parish,22075
622,New Orleans,LA,22,St. Bernard Parish,22087
622,New Orleans,LA,22,St. Charles Parish,22089
622,New Orleans,LA,22,St. James Parish,22093
622,New Orleans,LA,22,St. John The Baptist Parish,22095
622,New Orleans,LA,22,St. Tammany Parish,22103
622,New Orleans,LA,22,Tangipahoa Parish,22105
622,New Orleans,LA,22,Terrebonne Parish,22109
622,New Orleans,LA,22,Washington Parish,22117
612,Shreveport,LA,22,Bienville Parish,22013
612,Shreveport,LA,22,Bossier Parish,22015
612,Shreveport,LA,22,Caddo Parish,22017
612,Shreveport,LA,22,Claiborne Parish,22027
612,Shreveport,LA,22,De Soto Parish,22031
612,Shreveport,LA,22,Natchitoches Parish,22069
612,Shreveport,LA,22,Red River Parish,22081
612,Shreveport,LA,22,Sabine Parish,22085
612,Shreveport,LA,22,Webster Parish,22119
532,Albany – Schenectady – Troy,MA,25,Berkshire,25003
506,Boston (Manchester),MA,25,Barnstable,25001
506,Boston (Manchester),MA,25,Dukes,25007
506,Boston (Manchester),MA,25,Essex,25009
506,Boston (Manchester),MA,25,Middlesex,25017
506,Boston (Manchester),MA,25,Nantucket,25019
506,Boston (Manchester),MA,25,Norfolk,25021
506,Boston (Manchester),MA,25,Plymouth,25023
506,Boston (Manchester),MA,25,Suffolk,25025
506,Boston (Manchester),MA,25,Worcester,25027
521,Providence – New Bedford,MA,25,Bristol,25005
543,Springfield – Holyoke,MA,25,Franklin,25011
543,Springfield – Holyoke,MA,25,Hampden,25013
543,Springfield – Holyoke,MA,25,Hampshire,25015
512,Baltimore,MD,24,Anne Arundel,24003
512,Baltimore,MD,24,Baltimore,24005
512,Baltimore,MD,24,Caroline,24011
512,Baltimore,MD,24,Carroll,24013
512,Baltimore,MD,24,Cecil,24015
512,Baltimore,MD,24,Harford,24025
512,Baltimore,MD,24,Howard,24027
512,Baltimore,MD,24,Kent,24029
512,Baltimore,MD,24,Queen Anne'S,24035
512,Baltimore,MD,24,Talbot,24041
512,Baltimore,MD,24,Baltimore City,24510
508,Pittsburgh,MD,24,Garrett,24023
576,Salisbury,MD,24,Dorchester,24019
576,Salisbury,MD,24,Somerset,24039
576,Salisbury,MD,24,Wicomico,24045
576,Salisbury,MD,24,Worcester,24047
511,"Washington, DC (Hagerstown)",MD,24,Allegany,24001
511,"Washington, DC (Hagerstown)",MD,24,Calvert,24009
511,"Washington, DC (Hagerstown)",MD,24,Charles,24017
511,"Washington, DC (Hagerstown)",MD,24,Frederick,24021
511,"Washington, DC (Hagerstown)",MD,24,Montgomery,24031
511,"Washington, DC (Hagerstown)",MD,24,Prince George'S,24033
511,"Washington, DC (Hagerstown)",MD,24,St. Mary'S,24037
511,"Washington, DC (Hagerstown)",MD,24,Washington,24043
537,Bangor,ME,23,Hancock,23009
537,Bangor,ME,23,Penobscot,23019
537,Bangor,ME,23,Piscataquis,23021
537,Bangor,ME,23,Somerset,23025
537,Bangor,ME,23,Waldo,23027
537,Bangor,ME,23,Washington,23029
500,Portland – Auburn,ME,23,Androscoggin,23001
500,Portland – Auburn,ME,23,Cumberland,23005
500,Portland – Auburn,ME,23,Franklin,23007
500,Portland – Auburn,ME,23,Kennebec,23011
500,Portland – Auburn,ME,23,Knox,23013
500,Portland – Auburn,ME,23,Lincoln,23015
500,Portland – Auburn,ME,23,Oxford,23017
500,Portland – Auburn,ME,23,Sagadahoc,23023
500,Portland – Auburn,ME,23,York,23031
552,Presque Isle,ME,23,Aroostook,23003
583,Alpena,MI,26,Alcona,26001
583,Alpena,MI,26,Alpena,26007
505,Detroit,MI,26,Lapeer,26087
505,Detroit,MI,26,Livingston,26093
505,Detroit,MI,26,Macomb,26099
505,Detroit,MI,26,Monroe,26115
505,Detroit,MI,26,Oakland,26125
505,Detroit,MI,26,St. Clair,26147
505,Detroit,MI,26,Sanilac,26151
505,Detroit,MI,26,Washtenaw,26161
505,Detroit,MI,26,Wayne,26163
676,Duluth – Superior,MI,26,Gogebic,26053
513,Flint – Saginaw – Bay City,MI,26,Arenac,26011
513,Flint – Saginaw – Bay City,MI,26,Bay,26017
513,Flint – Saginaw – Bay City,MI,26,Genesee,26049
513,Flint – Saginaw – Bay City,MI,26,Gladwin,26051
513,Flint – Saginaw – Bay City,MI,26,Gratiot,26057
513,Flint – Saginaw – Bay City,MI,26,Huron,26063
513,Flint – Saginaw – Bay City,MI,26,Iosco,26069
513,Flint – Saginaw – Bay City,MI,26,Isabella,26073
513,Flint – Saginaw – Bay City,MI,26,Midland,26111
513,Flint – Saginaw – Bay City,MI,26,Ogemaw,26129
513,Flint – Saginaw – Bay City,MI,26,Saginaw,26145
513,Flint – Saginaw – Bay City,MI,26,Shiawassee,26155
513,Flint – Saginaw – Bay City,MI,26,Tuscola,26157
563,Grand Rapids – Kalamazoo – Battle Creek,MI,26,Allegan,26005
563,Grand Rapids – Kalamazoo – Battle Creek,MI,26,Barry,26015
563,Grand Rapids – Kalamazoo – Battle Creek,MI,26,Branch,26023
563,Grand Rapids – Kalamazoo – Battle Creek,MI,26,Calhoun,26025
563,Grand Rapids – Kalamazoo – Battle Creek,MI,26,Ionia,26067
563,Grand Rapids – Kalamazoo – Battle Creek,MI,26,Kalamazoo,26077
563,Grand Rapids – Kalamazoo – Battle Creek,MI,26,Kent,26081
563,Grand Rapids – Kalamazoo – Battle Creek,MI,26,Montcalm,26117
563,Grand Rapids – Kalamazoo – Battle Creek,MI,26,Muskegon,26121
563,Grand Rapids – Kalamazoo – Battle Creek,MI,26,Newaygo,26123
563,Grand Rapids – Kalamazoo – Battle Creek,MI,26,Oceana,26127
563,Grand Rapids – Kalamazoo – Battle Creek,MI,26,Ottawa,26139
563,Grand Rapids – Kalamazoo – Battle Creek,MI,26,St. Joseph,26149
563,Grand Rapids – Kalamazoo – Battle Creek,MI,26,Van Buren,26159
658,Green Bay – Appleton,MI,26,Menominee,26109
551,Lansing,MI,26,Clinton,26037
551,Lansing,MI,26,Eaton,26045
551,Lansing,MI,26,Hillsdale,26059
551,Lansing,MI,26,Ingham,26065
551,Lansing,MI,26,Jackson,26075
553,Marquette,MI,26,Alger,26003
553,Marquette,MI,26,Baraga,26013
553,Marquette,MI,26,Delta,26041
553,Marquette,MI,26,Dickinson,26043
553,Marquette,MI,26,Houghton,26061
553,Marquette,MI,26,Iron,26071
553,Marquette,MI,26,Keweenaw,26083
553,Marquette,MI,26,Marquette,26103
553,Marquette,MI,26,Ontonagon,26131
553,Marquette,MI,26,Schoolcraft,26153
588,South Bend – Elkhart,MI,26,Berrien,26021
588,South Bend – Elkhart,MI,26,Cass,26027
547,Toledo,MI,26,Lenawee,26091
540,Traverse City – Cadillac,MI,26,Antrim,26009
540,Traverse City – Cadillac,MI,26,Benzie,26019
540,Traverse City – Cadillac,MI,26,Charlevoix,26029
540,Traverse City – Cadillac,MI,26,Cheboygan,26031
540,Traverse City – Cadillac,MI,26,Chippewa,26033
540,Traverse City – Cadillac,MI,26,Clare,26035
540,Traverse City – Cadillac,MI,26,Crawford,26039
540,Traverse City – Cadillac,MI,26,Emmet,26047
540,Traverse City – Cadillac,MI,26,Grand Traverse,26055
540,Traverse City – Cadillac,MI,26,Kalkaska,26079
540,Traverse City – Cadillac,MI,26,Lake,26085
540,Traverse City – Cadillac,MI,26,Leelanau,26089
540,Traverse City – Cadillac,MI,26,Luce,26095
540,Traverse City – Cadillac,MI,26,Mackinac,26097
540,Traverse City – Cadillac,MI,26,Manistee,26101
540,Traverse City – Cadillac,MI,26,Mason,26105
540,Traverse City – Cadillac,MI,26,Mecosta,26107
540,Traverse City – Cadillac,MI,26,Missaukee,26113
540,Traverse City – Cadillac,MI,26,Montmorency,26119
540,Traverse City – Cadillac,MI,26,Osceola,26133
540,Traverse City – Cadillac,MI,26,Oscoda,26135
540,Traverse City – Cadillac,MI,26,Otsego,26137
540,Traverse City – Cadillac,MI,26,Presque Isle,26141
540,Traverse City – Cadillac,MI,26,Roscommon,26143
540,Traverse City – Cadillac,MI,26,Wexford,26165
676,Duluth – Superior,MN,27,Carlton,27017
676,Duluth – Superior,MN,27,Cook,27031
676,Duluth – Superior,MN,27,Itasca,27061
676,Duluth – Superior,MN,27,Koochiching,27071
676,Duluth – Superior,MN,27,Lake,27075
676,Duluth – Superior,MN,27,St. Louis,27137
724,Fargo – Valley City,MN,27,Becker,27005
724,Fargo – Valley City,MN,27,Clay,27027
724,Fargo – Valley City,MN,27,Clearwater,27029
724,Fargo – Valley City,MN,27,Kittson,27069
724,Fargo – Valley City,MN,27,Lake Of The Woods,27077
724,Fargo – Valley City,MN,27,Mahnomen,27087
724,Fargo – Valley City,MN,27,Marshall,27089
724,Fargo – Valley City,MN,27,Norman,27107
724,Fargo – Valley City,MN,27,Otter Tail,27111
724,Fargo – Valley City,MN,27,Pennington,27113
724,Fargo – Valley City,MN,27,Polk,27119
724,Fargo – Valley City,MN,27,Red Lake,27125
724,Fargo – Valley City,MN,27,Roseau,27135
724,Fargo – Valley City,MN,27,Wilkin,27167
702,La Crosse – Eau Claire,MN,27,Houston,27055
702,La Crosse – Eau Claire,MN,27,Winona,27169
737,Mankato,MN,27,Blue Earth,27013
737,Mankato,MN,27,Brown,27015
737,Mankato,MN,27,Martin,27091
737,Mankato,MN,27,Watonwan,27165
613,Minneapolis – St. Paul,MN,27,Aitkin,27001
613,Minneapolis – St. Paul,MN,27,Anoka,27003
613,Minneapolis – St. Paul,MN,27,Beltrami,27007
613,Minneapolis – St. Paul,MN,27,Benton,27009
613,Minneapolis – St. Paul,MN,27,Big Stone,27011
613,Minneapolis – St. Paul,MN,27,Carver,27019
613,Minneapolis – St. Paul,MN,27,Cass,27021
613,Minneapolis – St. Paul,MN,27,Chippewa,27023
613,Minneapolis – St. Paul,MN,27,Chisago,27025
613,Minneapolis – St. Paul,MN,27,Cottonwood,27033
613,Minneapolis – St. Paul,MN,27,Crow Wing,27035
613,Minneapolis – St. Paul,MN,27,Dakota,27037
613,Minneapolis – St. Paul,MN,27,Douglas,27041
613,Minneapolis – St. Paul,MN,27,Faribault,27043
613,Minneapolis – St. Paul,MN,27,Goodhue,27049
613,Minneapolis – St. Paul,MN,27,Grant,27051
613,Minneapolis – St. Paul,MN,27,Hennepin,27053
613,Minneapolis – St. Paul,MN,27,Hubbard,27057
613,Minneapolis – St. Paul,MN,27,Isanti,27059
613,Minneapolis – St. Paul,MN,27,Jackson,27063
613,Minneapolis – St. Paul,MN,27,Kanabec,27065
613,Minneapolis – St. Paul,MN,27,Kandiyohi,27067
613,Minneapolis – St. Paul,MN,27,Lac Qui Parle,27073
613,Minneapolis – St. Paul,MN,27,Le Sueur,27079
613,Minneapolis – St. Paul,MN,27,Lyon,27083
613,Minneapolis – St. Paul,MN,27,Mcleod,27085
613,Minneapolis – St. Paul,MN,27,Meeker,27093
613,Minneapolis – St. Paul,MN,27,Mille Lacs,27095
613,Minneapolis – St. Paul,MN,27,Morrison,27097
613,Minneapolis – St. Paul,MN,27,Nicollet,27103
613,Minneapolis – St. Paul,MN,27,Pine,27115
613,Minneapolis – St. Paul,MN,27,Pope,27121
613,Minneapolis – St. Paul,MN,27,Ramsey,27123
613,Minneapolis – St. Paul,MN,27,Redwood,27127
613,Minneapolis – St. Paul,MN,27,Renville,27129
613,Minneapolis – St. Paul,MN,27,Rice,27131
613,Minneapolis – St. Paul,MN,27,Scott,27139
613,Minneapolis – St. Paul,MN,27,Sherburne,27141
613,Minneapolis – St. Paul,MN,27,Sibley,27143
613,Minneapolis – St. Paul,MN,27,Stearns,27145
613,Minneapolis – St. Paul,MN,27,Steele,27147
613,Minneapolis – St. Paul,MN,27,Stevens,27149
613,Minneapolis – St. Paul,MN,27,Swift,27151
613,Minneapolis – St. Paul,MN,27,Todd,27153
613,Minneapolis – St. Paul,MN,27,Traverse,27155
613,Minneapolis – St. Paul,MN,27,Wabasha,27157
613,Minneapolis – St. Paul,MN,27,Wadena,27159
613,Minneapolis – St. Paul,MN,27,Waseca,27161
613,Minneapolis – St. Paul,MN,27,Washington,27163
613,Minneapolis – St. Paul,MN,27,Wright,27171
613,Minneapolis – St. Paul,MN,27,Yellow Medicine,27173
611,Rochester – Mason City – Austin,MN,27,Dodge,27039
611,Rochester – Mason City – Austin,MN,27,Fillmore,27045
611,Rochester – Mason City – Austin,MN,27,Freeborn,27047
611,Rochester – Mason City – Austin,MN,27,Mower,27099
611,Rochester – Mason City – Austin,MN,27,Olmsted,27109
725,Sioux Falls (Mitchell),MN,27,Lincoln,27081
725,Sioux Falls (Mitchell),MN,27,Murray,27101
725,Sioux Falls (Mitchell),MN,27,Nobles,27105
725,Sioux Falls (Mitchell),MN,27,Pipestone,27117
725,Sioux Falls (Mitchell),MN,27,Rock,27133
604,Columbia – Jefferson City,MO,29,Audrain,29007
604,Columbia – Jefferson City,MO,29,Boone,29019
604,Columbia – Jefferson City,MO,29,Callaway,29027
604,Columbia – Jefferson City,MO,29,Chariton,29041
604,Columbia – Jefferson City,MO,29,Cole,29051
604,Columbia – Jefferson City,MO,29,Cooper,29053
604,Columbia – Jefferson City,MO,29,Howard,29089
604,Columbia – Jefferson City,MO,29,Maries,29125
604,Columbia – Jefferson City,MO,29,Miller,29131
604,Columbia – Jefferson City,MO,29,Moniteau,29135
604,Columbia – Jefferson City,MO,29,Montgomery,29139
604,Columbia – Jefferson City,MO,29,Morgan,29141
604,Columbia – Jefferson City,MO,29,Osage,29151
604,Columbia – Jefferson City,MO,29,Randolph,29175
603,Joplin – Pittsburg,MO,29,Barton,29011
603,Joplin – Pittsburg,MO,29,Jasper,29097
603,Joplin – Pittsburg,MO,29,Mcdonald,29119
603,Joplin – Pittsburg,MO,29,Newton,29145
603,Joplin – Pittsburg,MO,29,Vernon,29217
616,Kansas City,MO,29,Bates,29013
616,Kansas City,MO,29,Caldwell,29025
616,Kansas City,MO,29,Carroll,29033
616,Kansas City,MO,29,Cass,29037
616,Kansas City,MO,29,Clay,29047
616,Kansas City,MO,29,Clinton,29049
616,Kansas City,MO,29,Daviess,29061
616,Kansas City,MO,29,Gentry,29075
616,Kansas City,MO,29,Grundy,29079
616,Kansas City,MO,29,Harrison,29081
616,Kansas City,MO,29,Henry,29083
616,Kansas City,MO,29,Jackson,29095
616,Kansas City,MO,29,Johnson,29101
616,Kansas City,MO,29,Lafayette,29107
616,Kansas City,MO,29,Linn,29115
616,Kansas City,MO,29,Livingston,29117
616,Kansas City,MO,29,Mercer,29129
616,Kansas City,MO,29,Pettis,29159
616,Kansas City,MO,29,Platte,29165
616,Kansas City,MO,29,Ray,29177
616,Kansas City,MO,29,Saline,29195
652,Omaha,MO,29,Atchison,29005
631,Ottumwa – Kirksville,MO,29,Adair,29001
631,Ottumwa – Kirksville,MO,29,Macon,29121
631,Ottumwa – Kirksville,MO,29,Putnam,29171
631,Ottumwa – Kirksville,MO,29,Schuyler,29197
631,Ottumwa – Kirksville,MO,29,Scotland,29199
631,Ottumwa – Kirksville,MO,29,Sullivan,29211
632,Paducah – Cape Girardeau – Harrisburg,MO,29,Bollinger,29017
632,Paducah – Cape Girardeau – Harrisburg,MO,29,Butler,29023
632,Paducah – Cape Girardeau – Harrisburg,MO,29,Cape Girardeau,29031
632,Paducah – Cape Girardeau – Harrisburg,MO,29,Carter,29035
632,Paducah – Cape Girardeau – Harrisburg,MO,29,Dunklin,29069
632,Paducah – Cape Girardeau – Harrisburg,MO,29,Madison,29123
632,Paducah – Cape Girardeau – Harrisburg,MO,29,Mississippi,29133
632,Paducah – Cape Girardeau – Harrisburg,MO,29,New Madrid,29143
632,Paducah – Cape Girardeau – Harrisburg,MO,29,Pemiscot,29155
632,Paducah – Cape Girardeau – Harrisburg,MO,29,Perry,29157
632,Paducah – Cape Girardeau – Harrisburg,MO,29,Reynolds,29179
632,Paducah – Cape Girardeau – Harrisburg,MO,29,Ripley,29181
632,Paducah – Cape Girardeau – Harrisburg,MO,29,Scott,29201
632,Paducah – Cape Girardeau – Harrisburg,MO,29,Stoddard,29207
632,Paducah – Cape Girardeau – Harrisburg,MO,29,Wayne,29223
717,Quincy – Hannibal – Keokuk,MO,29,Clark,29045
717,Quincy – Hannibal – Keokuk,MO,29,Knox,29103
717,Quincy – Hannibal – Keokuk,MO,29,Lewis,29111
717,Quincy – Hannibal – Keokuk,MO,29,Marion,29127
717,Quincy – Hannibal – Keokuk,MO,29,Monroe,29137
717,Quincy – Hannibal – Keokuk,MO,29,Ralls,29173
717,Quincy – Hannibal – Keokuk,MO,29,Shelby,29205
619,"Springfield, MO",MO,29,Barry,29009
619,"Springfield, MO",MO,29,Benton,29015
619,"Springfield, MO",MO,29,Camden,29029
619,"Springfield, MO",MO,29,Cedar,29039
619,"Springfield, MO",MO,29,Christian,29043
619,"Springfield, MO",MO,29,Dade,29057
619,"Springfield, MO",MO,29,Dallas,29059
619,"Springfield, MO",MO,29,Dent,29065
619,"Springfield, MO",MO,29,Douglas,29067
619,"Springfield, MO",MO,29,Greene,29077
619,"Springfield, MO",MO,29,Hickory,29085
619,"Springfield, MO",MO,29,Howell,29091
619,"Springfield, MO",MO,29,Laclede,29105
619,"Springfield, MO",MO,29,Lawrence,29109
619,"Springfield, MO",MO,29,Oregon,29149
619,"Springfield, MO",MO,29,Ozark,29153
619,"Springfield, MO",MO,29,Phelps,29161
619,"Springfield, MO",MO,29,Polk,29167
619,"Springfield, MO",MO,29,Pulaski,29169
619,"Springfield, MO",MO,29,St. Clair,29185
619,"Springfield, MO",MO,29,Shannon,29203
619,"Springfield, MO",MO,29,Stone,29209
619,"Springfield, MO",MO,29,Taney,29213
619,"Springfield, MO",MO,29,Texas,29215
619,"Springfield, MO",MO,29,Webster,29225
619,"Springfield, MO",MO,29,Wright,29229
638,St. Joseph,MO,29,Andrew,29003
638,St. Joseph,MO,29,Buchanan,29021
638,St. Joseph,MO,29,Dekalb,29063
638,St. Joseph,MO,29,Holt,29087
638,St. Joseph,MO,29,Nodaway,29147
638,St. Joseph,MO,29,Worth,29227
609,St. Louis,MO,29,Crawford,29055
609,St. Louis,MO,29,Franklin,29071
609,St. Louis,MO,29,Gasconade,29073
609,St. Louis,MO,29,Iron,29093
609,St. Louis,MO,29,Jefferson,29099
609,St. Louis,MO,29,Lincoln,29113
609,St. Louis,MO,29,Pike,29163
609,St. Louis,MO,29,St. Charles,29183
609,St. Louis,MO,29,Ste. Genevieve,29186
609,St. Louis,MO,29,St. Francois,29187
609,St. Louis,MO,29,St. Louis,29189
609,St. Louis,MO,29,Warren,29219
609,St. Louis,MO,29,Washington,29221
609,St. Louis,MO,29,St. Louis City,29510
716,Baton Rouge,MS,28,Amite,28005
716,Baton Rouge,MS,28,Wilkinson,28157
746,Biloxi – Gulfport,MS,28,George,28039
746,Biloxi – Gulfport,MS,28,Harrison,28047
746,Biloxi – Gulfport,MS,28,Jackson,28059
746,Biloxi – Gulfport,MS,28,Stone,28131
673,Columbus – Tupelo – West Point – Houston,MS,28,Calhoun,28013
673,Columbus – Tupelo – West Point – Houston,MS,28,Chickasaw,28017
673,Columbus – Tupelo – West Point – Houston,MS,28,Choctaw,28019
673,Columbus – Tupelo – West Point – Houston,MS,28,Clay,28025
673,Columbus – Tupelo – West Point – Houston,MS,28,Itawamba,28057
673,Columbus – Tupelo – West Point – Houston,MS,28,Lee,28081
673,Columbus – Tupelo – West Point – Houston,MS,28,Lowndes,28087
673,Columbus – Tupelo – West Point – Houston,MS,28,Monroe,28095
673,Columbus – Tupelo – West Point – Houston,MS,28,Montgomery,28097
673,Columbus – Tupelo – West Point – Houston,MS,28,Noxubee,28103
673,Columbus – Tupelo – West Point – Houston,MS,28,Oktibbeha,28105
673,Columbus – Tupelo – West Point – Houston,MS,28,Pontotoc,28115
673,Columbus – Tupelo – West Point – Houston,MS,28,Prentiss,28117
673,Columbus – Tupelo – West Point – Houston,MS,28,Tishomingo,28141
673,Columbus – Tupelo – West Point – Houston,MS,28,Union,28145
673,Columbus – Tupelo – West Point – Houston,MS,28,Webster,28155
673,Columbus – Tupelo – West Point – Houston,MS,28,Winston,28159
673,Columbus – Tupelo – West Point – Houston,MS,28,Yalobusha,28161
647,Greenwood – Greenville,MS,28,Bolivar,28011
647,Greenwood – Greenville,MS,28,Carroll,28015
647,Greenwood – Greenville,MS,28,Grenada,28043
647,Greenwood – Greenville,MS,28,Leflore,28083
647,Greenwood – Greenville,MS,28,Sunflower,28133
647,Greenwood – Greenville,MS,28,Tallahatchie,28135
647,Greenwood – Greenville,MS,28,Washington,28151
710,Hattiesburg – Laurel,MS,28,Covington,28031
710,Hattiesburg – Laurel,MS,28,Forrest,28035
710,Hattiesburg – Laurel,MS,28,Jasper,28061
710,Hattiesburg – Laurel,MS,28,Jones,28067
710,Hattiesburg – Laurel,MS,28,Lamar,28073
710,Hattiesburg – Laurel,MS,28,Marion,28091
710,Hattiesburg – Laurel,MS,28,Perry,28111
710,Hattiesburg – Laurel,MS,28,Wayne,28153
718,"Jackson, MS",MS,28,Adams,28001
718,"Jackson, MS",MS,28,Attala,28007
718,"Jackson, MS",MS,28,Claiborne,28021
718,"Jackson, MS",MS,28,Copiah,28029
718,"Jackson, MS",MS,28,Franklin,28037
718,"Jackson, MS",MS,28,Hinds,28049
718,"Jackson, MS",MS,28,Holmes,28051
718,"Jackson, MS",MS,28,Humphreys,28053
718,"Jackson, MS",MS,28,Issaquena,28055
718,"Jackson, MS",MS,28,Jefferson,28063
718,"Jackson, MS",MS,28,Jefferson Davis,28065
718,"Jackson, MS",MS,28,Lawrence,28077
718,"Jackson, MS",MS,28,Leake,28079
718,"Jackson, MS",MS,28,Lincoln,28085
718,"Jackson, MS",MS,28,Madison,28089
718,"Jackson, MS",MS,28,Pike,28113
718,"Jackson, MS",MS,28,Rankin,28121
718,"Jackson, MS",MS,28,Scott,28123
718,"Jackson, MS",MS,28,Sharkey,28125
718,"Jackson, MS",MS,28,Simpson,28127
718,"Jackson, MS",MS,28,Smith,28129
718,"Jackson, MS",MS,28,Walthall,28147
718,"Jackson, MS",MS,28,Warren,28149
718,"Jackson, MS",MS,28,Yazoo,28163
640,Memphis,MS,28,Alcorn,28003
640,Memphis,MS,28,Benton,28009
640,Memphis,MS,28,Coahoma,28027
640,Memphis,MS,28,Desoto,28033
640,Memphis,MS,28,Lafayette,28071
640,Memphis,MS,28,Marshall,28093
640,Memphis,MS,28,Panola,28107
640,Memphis,MS,28,Quitman,28119
640,Memphis,MS,28,Tate,28137
640,Memphis,MS,28,Tippah,28139
640,Memphis,MS,28,Tunica,28143
711,Meridian,MS,28,Clarke,28023
711,Meridian,MS,28,Kemper,28069
711,Meridian,MS,28,Lauderdale,28075
711,Meridian,MS,28,Neshoba,28099
711,Meridian,MS,28,Newton,28101
686,Mobile – Pensacola (Fort Walt),MS,28,Greene,28041
622,New Orleans,MS,28,Hancock,28045
622,New Orleans,MS,28,Pearl River,28109
756,Billings,MT,30,Big Horn,30003
756,Billings,MT,30,Carbon,30009
756,Billings,MT,30,Custer,30017
756,Billings,MT,30,Garfield,30033
756,Billings,MT,30,Golden Valley,30037
756,Billings,MT,30,Meagher,30059
756,Billings,MT,30,Musselshell,30065
756,Billings,MT,30,Park,30067
756,Billings,MT,30,Petroleum,30069
756,Billings,MT,30,Powder River,30075
756,Billings,MT,30,Rosebud,30087
756,Billings,MT,30,Stillwater,30095
756,Billings,MT,30,Sweet Grass,30097
756,Billings,MT,30,Treasure,30103
756,Billings,MT,30,Wheatland,30107
756,Billings,MT,30,Yellowstone,30111
754,Butte – Bozeman,MT,30,Beaverhead,30001
754,Butte – Bozeman,MT,30,Deer Lodge,30023
754,Butte – Bozeman,MT,30,Gallatin,30031
754,Butte – Bozeman,MT,30,Jefferson,30043
754,Butte – Bozeman,MT,30,Madison,30057
754,Butte – Bozeman,MT,30,Powell,30077
754,Butte – Bozeman,MT,30,Silver Bow,30093
798,Glendive,MT,30,Dawson,30021
798,Glendive,MT,30,Fallon,30025
798,Glendive,MT,30,Prairie,30079
755,Great Falls,MT,30,Blaine,30005
755,Great Falls,MT,30,Cascade,30013
755,Great Falls,MT,30,Chouteau,30015
755,Great Falls,MT,30,Fergus,30027
755,Great Falls,MT,30,Glacier,30035
755,Great Falls,MT,30,Hill,30041
755,Great Falls,MT,30,Judith Basin,30045
755,Great Falls,MT,30,Liberty,30051
755,Great Falls,MT,30,Phillips,30071
755,Great Falls,MT,30,Pondera,30073
755,Great Falls,MT,30,Teton,30099
755,Great Falls,MT,30,Toole,30101
755,Great Falls,MT,30,Valley,30105
766,Helena,MT,30,Broadwater,30007
766,Helena,MT,30,Lewis And Clark,30049
687,Minot – Bismarck – Dickinson (Williston),MT,30,Daniels,30019
687,Minot – Bismarck – Dickinson (Williston),MT,30,Mccone,30055
687,Minot – Bismarck – Dickinson (Williston),MT,30,Richland,30083
687,Minot – Bismarck – Dickinson (Williston),MT,30,Roosevelt,30085
687,Minot – Bismarck – Dickinson (Williston),MT,30,Sheridan,30091
687,Minot – Bismarck – Dickinson (Williston),MT,30,Wibaux,30109
762,Missoula,MT,30,Flathead,30029
762,Missoula,MT,30,Granite,30039
762,Missoula,MT,30,Lake,30047
762,Missoula,MT,30,Mineral,30061
762,Missoula,MT,30,Missoula,30063
762,Missoula,MT,30,Ravalli,30081
762,Missoula,MT,30,Sanders,30089
764,Rapid City,MT,30,Carter,30011
881,Spokane,MT,30,Lincoln,30053
524,Atlanta,NC,37,Clay,37043
517,Charlotte,NC,37,Alexander,37003
517,Charlotte,NC,37,Anson,37007
517,Charlotte,NC,37,Ashe,37009
517,Charlotte,NC,37,Avery,37011
517,Charlotte,NC,37,Burke,37023
517,Charlotte,NC,37,Cabarrus,37025
517,Charlotte,NC,37,Caldwell,37027
517,Charlotte,NC,37,Catawba,37035
517,Charlotte,NC,37,Cleveland,37045
517,Charlotte,NC,37,Gaston,37071
517,Charlotte,NC,37,Iredell,37097
517,Charlotte,NC,37,Lincoln,37109
517,Charlotte,NC,37,Mecklenburg,37119
517,Charlotte,NC,37,Richmond,37153
517,Charlotte,NC,37,Rowan,37159
517,Charlotte,NC,37,Stanly,37167
517,Charlotte,NC,37,Union,37179
517,Charlotte,NC,37,Watauga,37189
575,Chattanooga,NC,37,Cherokee,37039
570,Florence (SC) - Myrtle Beach (SC),NC,37,Robeson,37155
570,Florence (SC) - Myrtle Beach (SC),NC,37,Scotland,37165
518,Greensboro – H. Point – W. Salem,NC,37,Alamance,37001
518,Greensboro – H. Point – W. Salem,NC,37,Alleghany,37005
518,Greensboro – H. Point – W. Salem,NC,37,Caswell,37033
518,Greensboro – H. Point – W. Salem,NC,37,Davidson,37057
518,Greensboro – H. Point – W. Salem,NC,37,Davie,37059
518,Greensboro – H. Point – W. Salem,NC,37,Forsyth,37067
518,Greensboro – H. Point – W. Salem,NC,37,Guilford,37081
518,Greensboro – H. Point – W. Salem,NC,37,Montgomery,37123
518,Greensboro – H. Point – W. Salem,NC,37,Randolph,37151
518,Greensboro – H. Point – W. Salem,NC,37,Rockingham,37157
518,Greensboro – H. Point – W. Salem,NC,37,Stokes,37169
518,Greensboro – H. Point – W. Salem,NC,37,Surry,37171
518,Greensboro – H. Point – W. Salem,NC,37,Wilkes,37193
518,Greensboro – H. Point – W. Salem,NC,37,Yadkin,37197
545,Greenville – New Bern – Washington,NC,37,Beaufort,37013
545,Greenville – New Bern – Washington,NC,37,Bertie,37015
545,Greenville – New Bern – Washington,NC,37,Carteret,37031
545,Greenville – New Bern – Washington,NC,37,Craven,37049
545,Greenville – New Bern – Washington,NC,37,Duplin,37061
545,Greenville – New Bern – Washington,NC,37,Greene,37079
545,Greenville – New Bern – Washington,NC,37,Hyde,37095
545,Greenville – New Bern – Washington,NC,37,Jones,37103
545,Greenville – New Bern – Washington,NC,37,Lenoir,37107
545,Greenville – New Bern – Washington,NC,37,Martin,37117
545,Greenville – New Bern – Washington,NC,37,Onslow,37133
545,Greenville – New Bern – Washington,NC,37,Pamlico,37137
545,Greenville – New Bern – Washington,NC,37,Pitt,37147
545,Greenville – New Bern – Washington,NC,37,Tyrrell,37177
545,Greenville – New Bern – Washington,NC,37,Washington,37187
567,Greenville – Spartanburg – Asheville – Anderson,NC,37,Buncombe,37021
567,Greenville – Spartanburg – Asheville – Anderson,NC,37,Graham,37075
567,Greenville – Spartanburg – Asheville – Anderson,NC,37,Haywood,37087
567,Greenville – Spartanburg – Asheville – Anderson,NC,37,Henderson,37089
567,Greenville – Spartanburg – Asheville – Anderson,NC,37,Jackson,37099
567,Greenville – Spartanburg – Asheville – Anderson,NC,37,Mcdowell,37111
567,Greenville – Spartanburg – Asheville – Anderson,NC,37,Macon,37113
567,Greenville – Spartanburg – Asheville – Anderson,NC,37,Madison,37115
567,Greenville – Spartanburg – Asheville – Anderson,NC,37,Mitchell,37121
567,Greenville – Spartanburg – Asheville – Anderson,NC,37,Polk,37149
567,Greenville – Spartanburg – Asheville – Anderson,NC,37,Rutherford,37161
567,Greenville – Spartanburg – Asheville – Anderson,NC,37,Swain,37173
567,Greenville – Spartanburg – Asheville – Anderson,NC,37,Transylvania,37175
567,Greenville – Spartanburg – Asheville – Anderson,NC,37,Yancey,37199
544,Norfolk – Portsmouth – Newport News,NC,37,Camden,37029
544,Norfolk – Portsmouth – Newport News,NC,37,Chowan,37041
544,Norfolk – Portsmouth – Newport News,NC,37,Currituck,37053
544,Norfolk – Portsmouth – Newport News,NC,37,Dare,37055
544,Norfolk – Portsmouth – Newport News,NC,37,Gates,37073
544,Norfolk – Portsmouth – Newport News,NC,37,Hertford,37091
544,Norfolk – Portsmouth – Newport News,NC,37,Northampton,37131
544,Norfolk – Portsmouth – Newport News,NC,37,Pasquotank,37139
544,Norfolk – Portsmouth – Newport News,NC,37,Perquimans,37143
560,Raleigh – Durham (Fayetteville),NC,37,Chatham,37037
560,Raleigh – Durham (Fayetteville),NC,37,Cumberland,37051
560,Raleigh – Durham (Fayetteville),NC,37,Durham,37063
560,Raleigh – Durham (Fayetteville),NC,37,Edgecombe,37065
560,Raleigh – Durham (Fayetteville),NC,37,Franklin,37069
560,Raleigh – Durham (Fayetteville),NC,37,Granville,37077
560,Raleigh – Durham (Fayetteville),NC,37,Halifax,37083
560,Raleigh – Durham (Fayetteville),NC,37,Harnett,37085
560,Raleigh – Durham (Fayetteville),NC,37,Hoke,37093
560,Raleigh – Durham (Fayetteville),NC,37,Johnston,37101
560,Raleigh – Durham (Fayetteville),NC,37,Lee,37105
560,Raleigh – Durham (Fayetteville),NC,37,Moore,37125
560,Raleigh – Durham (Fayetteville),NC,37,Nash,37127
560,Raleigh – Durham (Fayetteville),NC,37,Orange,37135
560,Raleigh – Durham (Fayetteville),NC,37,Person,37145
560,Raleigh – Durham (Fayetteville),NC,37,Sampson,37163
560,Raleigh – Durham (Fayetteville),NC,37,Vance,37181
560,Raleigh – Durham (Fayetteville),NC,37,Wake,37183
560,Raleigh – Durham (Fayetteville),NC,37,Warren,37185
560,Raleigh – Durham (Fayetteville),NC,37,Wayne,37191
560,Raleigh – Durham (Fayetteville),NC,37,Wilson,37195
550,Wilmington,NC,37,Bladen,37017
550,Wilmington,NC,37,Brunswick,37019
550,Wilmington,NC,37,Columbus,37047
550,Wilmington,NC,37,New Hanover,37129
550,Wilmington,NC,37,Pender,37141
724,Fargo – Valley City,ND,38,Barnes,38003
724,Fargo – Valley City,ND,38,Benson,38005
724,Fargo – Valley City,ND,38,Cass,38017
724,Fargo – Valley City,ND,38,Cavalier,38019
724,Fargo – Valley City,ND,38,Dickey,38021
724,Fargo – Valley City,ND,38,Eddy,38027
724,Fargo – Valley City,ND,38,Foster,38031
724,Fargo – Valley City,ND,38,Grand Forks,38035
724,Fargo – Valley City,ND,38,Griggs,38039
724,Fargo – Valley City,ND,38,Lamoure,38045
724,Fargo – Valley City,ND,38,Nelson,38063
724,Fargo – Valley City,ND,38,Pembina,38067
724,Fargo – Valley City,ND,38,Ramsey,38071
724,Fargo – Valley City,ND,38,Ransom,38073
724,Fargo – Valley City,ND,38,Richland,38077
724,Fargo – Valley City,ND,38,Sargent,38081
724,Fargo – Valley City,ND,38,Steele,38091
724,Fargo – Valley City,ND,38,Stutsman,38093
724,Fargo – Valley City,ND,38,Towner,38095
724,Fargo – Valley City,ND,38,Traill,38097
724,Fargo – Valley City,ND,38,Walsh,38099
687,Minot – Bismarck – Dickinson (Williston),ND,38,Adams,38001
687,Minot – Bismarck – Dickinson (Williston),ND,38,Billings,38007
687,Minot – Bismarck – Dickinson (Williston),ND,38,Bottineau,38009
687,Minot – Bismarck – Dickinson (Williston),ND,38,Bowman,38011
687,Minot – Bismarck – Dickinson (Williston),ND,38,Burke,38013
687,Minot – Bismarck – Dickinson (Williston),ND,38,Burleigh,38015
687,Minot – Bismarck – Dickinson (Williston),ND,38,Divide,38023
687,Minot – Bismarck – Dickinson (Williston),ND,38,Dunn,38025
687,Minot – Bismarck – Dickinson (Williston),ND,38,Emmons,38029
687,Minot – Bismarck – Dickinson (Williston),ND,38,Golden Valley,38033
687,Minot – Bismarck – Dickinson (Williston),ND,38,Grant,38037
687,Minot – Bismarck – Dickinson (Williston),ND,38,Hettinger,38041
687,Minot – Bismarck – Dickinson (Williston),ND,38,Kidder,38043
687,Minot – Bismarck – Dickinson (Williston),ND,38,Logan,38047
687,Minot – Bismarck – Dickinson (Williston),ND,38,Mchenry,38049
687,Minot – Bismarck – Dickinson (Williston),ND,38,Mcintosh,38051
687,Minot – Bismarck – Dickinson (Williston),ND,38,Mckenzie,38053
687,Minot – Bismarck – Dickinson (Williston),ND,38,Mclean,38055
687,Minot – Bismarck – Dickinson (Williston),ND,38,Mercer,38057
687,Minot – Bismarck – Dickinson (Williston),ND,38,Morton,38059
687,Minot – Bismarck – Dickinson (Williston),ND,38,Mountrail,38061
687,Minot – Bismarck – Dickinson (Williston),ND,38,Oliver,38065
687,Minot – Bismarck – Dickinson (Williston),ND,38,Pierce,38069
687,Minot – Bismarck – Dickinson (Williston),ND,38,Renville,38075
687,Minot – Bismarck – Dickinson (Williston),ND,38,Rolette,38079
687,Minot – Bismarck – Dickinson (Williston),ND,38,Sheridan,38083
687,Minot – Bismarck – Dickinson (Williston),ND,38,Sioux,38085
687,Minot – Bismarck – Dickinson (Williston),ND,38,Slope,38087
687,Minot – Bismarck – Dickinson (Williston),ND,38,Stark,38089
687,Minot – Bismarck – Dickinson (Williston),ND,38,Ward,38101
687,Minot – Bismarck – Dickinson (Williston),ND,38,Wells,38103
687,Minot – Bismarck – Dickinson (Williston),ND,38,Williams,38105
759,Cheyenne – Scottsbluff,NE,31,Scotts Bluff,31157
751,Denver,NE,31,Box Butte,31013
751,Denver,NE,31,Cheyenne,31033
751,Denver,NE,31,Dawes,31045
751,Denver,NE,31,Deuel,31049
751,Denver,NE,31,Garden,31069
751,Denver,NE,31,Keith,31101
751,Denver,NE,31,Kimball,31105
722,Lincoln & Hastings – Kearney,NE,31,Adams,31001
722,Lincoln & Hastings – Kearney,NE,31,Antelope,31003
722,Lincoln & Hastings – Kearney,NE,31,Boone,31011
722,Lincoln & Hastings – Kearney,NE,31,Boyd,31015
722,Lincoln & Hastings – Kearney,NE,31,Brown,31017
722,Lincoln & Hastings – Kearney,NE,31,Buffalo,31019
722,Lincoln & Hastings – Kearney,NE,31,Chase,31029
722,Lincoln & Hastings – Kearney,NE,31,Clay,31035
722,Lincoln & Hastings – Kearney,NE,31,Custer,31041
722,Lincoln & Hastings – Kearney,NE,31,Dawson,31047
722,Lincoln & Hastings – Kearney,NE,31,Fillmore,31059
722,Lincoln & Hastings – Kearney,NE,31,Franklin,31061
722,Lincoln & Hastings – Kearney,NE,31,Frontier,31063
722,Lincoln & Hastings – Kearney,NE,31,Furnas,31065
722,Lincoln & Hastings – Kearney,NE,31,Gage,31067
722,Lincoln & Hastings – Kearney,NE,31,Garfield,31071
722,Lincoln & Hastings – Kearney,NE,31,Gosper,31073
722,Lincoln & Hastings – Kearney,NE,31,Greeley,31077
722,Lincoln & Hastings – Kearney,NE,31,Hall,31079
722,Lincoln & Hastings – Kearney,NE,31,Hamilton,31081
722,Lincoln & Hastings – Kearney,NE,31,Harlan,31083
722,Lincoln & Hastings – Kearney,NE,31,Hayes,31085
722,Lincoln & Hastings – Kearney,NE,31,Hitchcock,31087
722,Lincoln & Hastings – Kearney,NE,31,Holt,31089
722,Lincoln & Hastings – Kearney,NE,31,Howard,31093
722,Lincoln & Hastings – Kearney,NE,31,Jefferson,31095
722,Lincoln & Hastings – Kearney,NE,31,Kearney,31099
722,Lincoln & Hastings – Kearney,NE,31,Keya Paha,31103
722,Lincoln & Hastings – Kearney,NE,31,Lancaster,31109
722,Lincoln & Hastings – Kearney,NE,31,Loup,31115
722,Lincoln & Hastings – Kearney,NE,31,Merrick,31121
722,Lincoln & Hastings – Kearney,NE,31,Nance,31125
722,Lincoln & Hastings – Kearney,NE,31,Nuckolls,31129
722,Lincoln & Hastings – Kearney,NE,31,Pawnee,31133
722,Lincoln & Hastings – Kearney,NE,31,Perkins,31135
722,Lincoln & Hastings – Kearney,NE,31,Phelps,31137
722,Lincoln & Hastings – Kearney,NE,31,Polk,31143
722,Lincoln & Hastings – Kearney,NE,31,Red Willow,31145
722,Lincoln & Hastings – Kearney,NE,31,Rock,31149
722,Lincoln & Hastings – Kearney,NE,31,Saline,31151
722,Lincoln & Hastings – Kearney,NE,31,Seward,31159
722,Lincoln & Hastings – Kearney,NE,31,Sherman,31163
722,Lincoln & Hastings – Kearney,NE,31,Thayer,31169
722,Lincoln & Hastings – Kearney,NE,31,Valley,31175
722,Lincoln & Hastings – Kearney,NE,31,Webster,31181
722,Lincoln & Hastings – Kearney,NE,31,Wheeler,31183
722,Lincoln & Hastings – Kearney,NE,31,York,31185
740,North Platte,NE,31,Arthur,31005
740,North Platte,NE,31,Blaine,31009
740,North Platte,NE,31,Hooker,31091
740,North Platte,NE,31,Lincoln,31111
740,North Platte,NE,31,Logan,31113
740,North Platte,NE,31,Mcpherson,31117
740,North Platte,NE,31,Thomas,31171
652,Omaha,NE,31,Burt,31021
652,Omaha,NE,31,Butler,31023
652,Omaha,NE,31,Cass,31025
652,Omaha,NE,31,Colfax,31037
652,Omaha,NE,31,Cuming,31039
652,Omaha,NE,31,Dodge,31053
652,Omaha,NE,31,Douglas,31055
652,Omaha,NE,31,Johnson,31097
652,Omaha,NE,31,Nemaha,31127
652,Omaha,NE,31,Otoe,31131
652,Omaha,NE,31,Platte,31141
652,Omaha,NE,31,Richardson,31147
652,Omaha,NE,31,Sarpy,31153
652,Omaha,NE,31,Saunders,31155
652,Omaha,NE,31,Washington,31177
764,Rapid City,NE,31,Banner,31007
764,Rapid City,NE,31,Grant,31075
764,Rapid City,NE,31,Morrill,31123
764,Rapid City,NE,31,Sheridan,31161
764,Rapid City,NE,31,Sioux,31165
624,Sioux City,NE,31,Cedar,31027
624,Sioux City,NE,31,Dakota,31043
624,Sioux City,NE,31,Dixon,31051
624,Sioux City,NE,31,Knox,31107
624,Sioux City,NE,31,Madison,31119
624,Sioux City,NE,31,Pierce,31139
624,Sioux City,NE,31,Stanton,31167
624,Sioux City,NE,31,Thurston,31173
624,Sioux City,NE,31,Wayne,31179
725,Sioux Falls (Mitchell),NE,31,Cherry,31031
678,Wichita – Hutchinson Plus,NE,31,Dundy,31057
506,Boston (Manchester),NH,33,Belknap,33001
506,Boston (Manchester),NH,33,Cheshire,33005
506,Boston (Manchester),NH,33,Hillsborough,33011
506,Boston (Manchester),NH,33,Merrimack,33013
506,Boston (Manchester),NH,33,Rockingham,33015
506,Boston (Manchester),NH,33,Strafford,33017
523,Burlington – Plattsburgh,NH,33,Grafton,33009
523,Burlington – Plattsburgh,NH,33,Sullivan,33019
500,Portland – Auburn,NH,33,Carroll,33003
500,Portland – Auburn,NH,33,Coos,33007
501,New York,NJ,34,Bergen,34003
501,New York,NJ,34,Essex,34013
501,New York,NJ,34,Hudson,34017
501,New York,NJ,34,Hunterdon,34019
501,New York,NJ,34,Middlesex,34023
501,New York,NJ,34,Monmouth,34025
501,New York,NJ,34,Morris,34027
501,New York,NJ,34,Ocean,34029
501,New York,NJ,34,Passaic,34031
501,New York,NJ,34,Somerset,34035
501,New York,NJ,34,Sussex,34037
501,New York,NJ,34,Union,34039
501,New York,NJ,34,Warren,34041
504,Philadelphia,NJ,34,Atlantic,34001
504,Philadelphia,NJ,34,Burlington,34005
504,Philadelphia,NJ,34,Camden,34007
504,Philadelphia,NJ,34,Cape May,34009
504,Philadelphia,NJ,34,Cumberland,34011
504,Philadelphia,NJ,34,Gloucester,34015
504,Philadelphia,NJ,34,Mercer,34021
504,Philadelphia,NJ,34,Salem,34033
790,Albuquerque – Santa Fe,NM,35,Bernalillo,35001
790,Albuquerque – Santa Fe,NM,35,Catron,35003
790,Albuquerque – Santa Fe,NM,35,Chaves,35005
790,Albuquerque – Santa Fe,NM,35,Cibola,35006
790,Albuquerque – Santa Fe,NM,35,Colfax,35007
790,Albuquerque – Santa Fe,NM,35,Debaca,35011
790,Albuquerque – Santa Fe,NM,35,Eddy,35015
790,Albuquerque – Santa Fe,NM,35,Grant,35017
790,Albuquerque – Santa Fe,NM,35,Guadalupe,35019
790,Albuquerque – Santa Fe,NM,35,Harding,35021
790,Albuquerque – Santa Fe,NM,35,Hidalgo,35023
790,Albuquerque – Santa Fe,NM,35,Lea,35025
790,Albuquerque – Santa Fe,NM,35,Lincoln,35027
790,Albuquerque – Santa Fe,NM,35,Los Alamos,35028
790,Albuquerque – Santa Fe,NM,35,Luna,35029
790,Albuquerque – Santa Fe,NM,35,Mckinley,35031
790,Albuquerque – Santa Fe,NM,35,Mora,35033
790,Albuquerque – Santa Fe,NM,35,Otero,35035
790,Albuquerque – Santa Fe,NM,35,Rio Arriba,35039
790,Albuquerque – Santa Fe,NM,35,Sandoval,35043
790,Albuquerque – Santa Fe,NM,35,San Juan,35045
790,Albuquerque – Santa Fe,NM,35,San Miguel,35047
790,Albuquerque – Santa Fe,NM,35,Santa Fe,35049
790,Albuquerque – Santa Fe,NM,35,Sierra,35051
790,Albuquerque – Santa Fe,NM,35,Socorro,35053
790,Albuquerque – Santa Fe,NM,35,Taos,35055
790,Albuquerque – Santa Fe,NM,35,Torrance,35057
790,Albuquerque – Santa Fe,NM,35,Valencia,35061
634,Amarillo,NM,35,Curry,35009
634,Amarillo,NM,35,Quay,35037
634,Amarillo,NM,35,Roosevelt,35041
634,Amarillo,NM,35,Union,35059
765,El Paso (TX),NM,35,Dona Ana,35013
839,Las Vegas,NV,32,Clark,32003
839,Las Vegas,NV,32,Lincoln,32017
839,Las Vegas,NV,32,Nye,32023
811,Reno,NV,32,Churchill,32001
811,Reno,NV,32,Douglas,32005
811,Reno,NV,32,Esmeralda,32009
811,Reno,NV,32,Humboldt,32013
811,Reno,NV,32,Lander,32015
811,Reno,NV,32,Lyon,32019
811,Reno,NV,32,Mineral,32021
811,Reno,NV,32,Pershing,32027
811,Reno,NV,32,Storey,32029
811,Reno,NV,32,Washoe,32031
811,Reno,NV,32,Carson City,32510
770,Salt Lake City,NV,32,Elko,32007
770,Salt Lake City,NV,32,Eureka,32011
770,Salt Lake City,NV,32,White Pine,32033
532,Albany – Schenectady – Troy,NY,36,Albany,36001
532,Albany – Schenectady – Troy,NY,36,Columbia,36021
532,Albany – Schenectady – Troy,NY,36,Fulton,36035
532,Albany – Schenectady – Troy,NY,36,Greene,36039
532,Albany – Schenectady – Troy,NY,36,Hamilton,36041
532,Albany – Schenectady – Troy,NY,36,Montgomery,36057
532,Albany – Schenectady – Troy,NY,36,Rensselaer,36083
532,Albany – Schenectady – Troy,NY,36,Saratoga,36091
532,Albany – Schenectady – Troy,NY,36,Schenectady,36093
532,Albany – Schenectady – Troy,NY,36,Schoharie,36095
532,Albany – Schenectady – Troy,NY,36,Warren,36113
532,Albany – Schenectady – Troy,NY,36,Washington,36115
502,Binghamton,NY,36,Broome,36007
502,Binghamton,NY,36,Chenango,36017
502,Binghamton,NY,36,Delaware,36025
502,Binghamton,NY,36,Tioga,36107
514,Buffalo,NY,36,Allegany,36003
514,Buffalo,NY,36,Cattaraugus,36009
514,Buffalo,NY,36,Chautauqua,36013
514,Buffalo,NY,36,Erie,36029
514,Buffalo,NY,36,Genesee,36037
514,Buffalo,NY,36,Niagara,36063
514,Buffalo,NY,36,Orleans,36073
514,Buffalo,NY,36,Wyoming,36121
523,Burlington – Plattsburgh,NY,36,Clinton,36019
523,Burlington – Plattsburgh,NY,36,Essex,36031
523,Burlington – Plattsburgh,NY,36,Franklin,36033
565,Elmira (Corning),NY,36,Chemung,36015
565,Elmira (Corning),NY,36,Schuyler,36097
565,Elmira (Corning),NY,36,Steuben,36101
501,New York,NY,36,Bronx,36005
501,New York,NY,36,Dutchess,36027
501,New York,NY,36,Kings,36047
501,New York,NY,36,Nassau,36059
501,New York,NY,36,New York,36061
501,New York,NY,36,Orange,36071
501,New York,NY,36,Putnam,36079
501,New York,NY,36,Queens,36081
501,New York,NY,36,Richmond,36085
501,New York,NY,36,Rockland,36087
501,New York,NY,36,Suffolk,36103
501,New York,NY,36,Sullivan,36105
501,New York,NY,36,Ulster,36111
501,New York,NY,36,Westchester,36119
538,"Rochester, NY",NY,36,Livingston,36051
538,"Rochester, NY",NY,36,Monroe,36055
538,"Rochester, NY",NY,36,Ontario,36069
538,"Rochester, NY",NY,36,Wayne,36117
538,"Rochester, NY",NY,36,Yates,36123
555,Syracuse,NY,36,Cayuga,36011
555,Syracuse,NY,36,Cortland,36023
555,Syracuse,NY,36,Madison,36053
555,Syracuse,NY,36,Onondaga,36067
555,Syracuse,NY,36,Oswego,36075
555,Syracuse,NY,36,Seneca,36099
555,Syracuse,NY,36,Tompkins,36109
526,Utica,NY,36,Herkimer,36043
526,Utica,NY,36,Oneida,36065
526,Utica,NY,36,Otsego,36077
549,Watertown,NY,36,Jefferson,36045
549,Watertown,NY,36,Lewis,36049
549,Watertown,NY,36,St. Lawrence,36089
564,Charleston – Huntington,OH,39,Athens,39009
564,Charleston – Huntington,OH,39,Gallia,39053
564,Charleston – Huntington,OH,39,Jackson,39079
564,Charleston – Huntington,OH,39,Lawrence,39087
564,Charleston – Huntington,OH,39,Meigs,39105
564,Charleston – Huntington,OH,39,Scioto,39145
564,Charleston – Huntington,OH,39,Vinton,39163
515,Cincinnati,OH,39,Adams,39001
515,Cincinnati,OH,39,Brown,39015
515,Cincinnati,OH,39,Butler,39017
515,Cincinnati,OH,39,Clermont,39025
515,Cincinnati,OH,39,Clinton,39027
515,Cincinnati,OH,39,Hamilton,39061
515,Cincinnati,OH,39,Highland,39071
515,Cincinnati,OH,39,Warren,39165
510,Cleveland – Akron (Canton),OH,39,Ashland,39005
510,Cleveland – Akron (Canton),OH,39,Ashtabula,39007
510,Cleveland – Akron (Canton),OH,39,Carroll,39019
510,Cleveland – Akron (Canton),OH,39,Cuyahoga,39035
510,Cleveland – Akron (Canton),OH,39,Erie,39043
510,Cleveland – Akron (Canton),OH,39,Geauga,39055
510,Cleveland – Akron (Canton),OH,39,Holmes,39075
510,Cleveland – Akron (Canton),OH,39,Huron,39077
510,Cleveland – Akron (Canton),OH,39,Lake,39085
510,Cleveland – Akron (Canton),OH,39,Lorain,39093
510,Cleveland – Akron (Canton),OH,39,Medina,39103
510,Cleveland – Akron (Canton),OH,39,Portage,39133
510,Cleveland – Akron (Canton),OH,39,Richland,39139
510,Cleveland – Akron (Canton),OH,39,Stark,39151
510,Cleveland – Akron (Canton),OH,39,Summit,39153
510,Cleveland – Akron (Canton),OH,39,Tuscarawas,39157
510,Cleveland – Akron (Canton),OH,39,Wayne,39169
535,Columbus (OH),OH,39,Coshocton,39031
535,Columbus (OH),OH,39,Crawford,39033
535,Columbus (OH),OH,39,Delaware,39041
535,Columbus (OH),OH,39,Fairfield,39045
535,Columbus (OH),OH,39,Fayette,39047
535,Columbus (OH),OH,39,Franklin,39049
535,Columbus (OH),OH,39,Guernsey,39059
535,Columbus (OH),OH,39,Hardin,39065
535,Columbus (OH),OH,39,Hocking,39073
535,Columbus (OH),OH,39,Knox,39083
535,Columbus (OH),OH,39,Licking,39089
535,Columbus (OH),OH,39,Madison,39097
535,Columbus (OH),OH,39,Marion,39101
535,Columbus (OH),OH,39,Morgan,39115
535,Columbus (OH),OH,39,Morrow,39117
535,Columbus (OH),OH,39,Perry,39127
535,Columbus (OH),OH,39,Pickaway,39129
535,Columbus (OH),OH,39,Pike,39131
535,Columbus (OH),OH,39,Ross,39141
535,Columbus (OH),OH,39,Union,39159
542,Dayton,OH,39,Champaign,39021
542,Dayton,OH,39,Clark,39023
542,Dayton,OH,39,Darke,39037
542,Dayton,OH,39,Greene,39057
542,Dayton,OH,39,Logan,39091
542,Dayton,OH,39,Mercer,39107
542,Dayton,OH,39,Miami,39109
542,Dayton,OH,39,Montgomery,39113
542,Dayton,OH,39,Preble,39135
542,Dayton,OH,39,Shelby,39149
509,Fort Wayne,OH,39,Paulding,39125
509,Fort Wayne,OH,39,Van Wert,39161
558,Lima,OH,39,Allen,39003
558,Lima,OH,39,Auglaize,39011
597,Parkersburg,OH,39,Washington,39167
547,Toledo,OH,39,Defiance,39039
547,Toledo,OH,39,Fulton,39051
547,Toledo,OH,39,Hancock,39063
547,Toledo,OH,39,Henry,39069
547,Toledo,OH,39,Lucas,39095
547,Toledo,OH,39,Ottawa,39123
547,Toledo,OH,39,Putnam,39137
547,Toledo,OH,39,Sandusky,39143
547,Toledo,OH,39,Seneca,39147
547,Toledo,OH,39,Williams,39171
547,Toledo,OH,39,Wood,39173
547,Toledo,OH,39,Wyandot,39175
554,Wheeling – Steubenville,OH,39,Belmont,39013
554,Wheeling – Steubenville,OH,39,Harrison,39067
554,Wheeling – Steubenville,OH,39,Jefferson,39081
554,Wheeling – Steubenville,OH,39,Monroe,39111
554,Wheeling – Steubenville,OH,39,Noble,39121
536,Youngstown,OH,39,Columbiana,39029
536,Youngstown,OH,39,Mahoning,39099
536,Youngstown,OH,39,Trumbull,39155
596,Zanesville,OH,39,Muskingum,39119
634,Amarillo,OK,40,Beaver,40007
634,Amarillo,OK,40,Cimarron,40025
634,Amarillo,OK,40,Texas,40139
670,Fort Smith – Fayetteville – Springdale – Rogers,OK,40,Le Flore,40079
670,Fort Smith – Fayetteville – Springdale – Rogers,OK,40,Sequoyah,40135
603,Joplin – Pittsburg,OK,40,Ottawa,40115
650,Oklahoma City,OK,40,Alfalfa,40003
650,Oklahoma City,OK,40,Beckham,40009
650,Oklahoma City,OK,40,Blaine,40011
650,Oklahoma City,OK,40,Caddo,40015
650,Oklahoma City,OK,40,Canadian,40017
650,Oklahoma City,OK,40,Cleveland,40027
650,Oklahoma City,OK,40,Custer,40039
650,Oklahoma City,OK,40,Dewey,40043
650,Oklahoma City,OK,40,Ellis,40045
650,Oklahoma City,OK,40,Garfield,40047
650,Oklahoma City,OK,40,Garvin,40049
650,Oklahoma City,OK,40,Grady,40051
650,Oklahoma City,OK,40,Grant,40053
650,Oklahoma City,OK,40,Greer,40055
650,Oklahoma City,OK,40,Harmon,40057
650,Oklahoma City,OK,40,Harper,40059
650,Oklahoma City,OK,40,Hughes,40063
650,Oklahoma City,OK,40,Kay,40071
650,Oklahoma City,OK,40,Kingfisher,40073
650,Oklahoma City,OK,40,Kiowa,40075
650,Oklahoma City,OK,40,Lincoln,40081
650,Oklahoma City,OK,40,Logan,40083
650,Oklahoma City,OK,40,Mcclain,40087
650,Oklahoma City,OK,40,Major,40093
650,Oklahoma City,OK,40,Murray,40099
650,Oklahoma City,OK,40,Noble,40103
650,Oklahoma City,OK,40,Okfuskee,40107
650,Oklahoma City,OK,40,Oklahoma,40109
650,Oklahoma City,OK,40,Payne,40119
650,Oklahoma City,OK,40,Pottawatomie,40125
650,Oklahoma City,OK,40,Roger Mills,40129
650,Oklahoma City,OK,40,Seminole,40133
650,Oklahoma City,OK,40,Washita,40149
650,Oklahoma City,OK,40,Woods,40151
650,Oklahoma City,OK,40,Woodward,40153
657,Sherman – Ada,OK,40,Atoka,40005
657,Sherman – Ada,OK,40,Bryan,40013
657,Sherman – Ada,OK,40,Carter,40019
657,Sherman – Ada,OK,40,Choctaw,40023
657,Sherman – Ada,OK,40,Coal,40029
657,Sherman – Ada,OK,40,Johnston,40069
657,Sherman – Ada,OK,40,Love,40085
657,Sherman – Ada,OK,40,Marshall,40095
657,Sherman – Ada,OK,40,Pontotoc,40123
657,Sherman – Ada,OK,40,Pushmataha,40127
612,Shreveport,OK,40,Mccurtain,40089
671,Tulsa,OK,40,Adair,40001
671,Tulsa,OK,40,Cherokee,40021
671,Tulsa,OK,40,Craig,40035
671,Tulsa,OK,40,Creek,40037
671,Tulsa,OK,40,Delaware,40041
671,Tulsa,OK,40,Haskell,40061
671,Tulsa,OK,40,Latimer,40077
671,Tulsa,OK,40,Mcintosh,40091
671,Tulsa,OK,40,Mayes,40097
671,Tulsa,OK,40,Muskogee,40101
671,Tulsa,OK,40,Nowata,40105
671,Tulsa,OK,40,Okmulgee,40111
671,Tulsa,OK,40,Osage,40113
671,Tulsa,OK,40,Pawnee,40117
671,Tulsa,OK,40,Pittsburg,40121
671,Tulsa,OK,40,Rogers,40131
671,Tulsa,OK,40,Tulsa,40143
671,Tulsa,OK,40,Wagoner,40145
671,Tulsa,OK,40,Washington,40147
627,Wichita Falls & Lawton,OK,40,Comanche,40031
627,Wichita Falls & Lawton,OK,40,Cotton,40033
627,Wichita Falls & Lawton,OK,40,Jackson,40065
627,Wichita Falls & Lawton,OK,40,Jefferson,40067
627,Wichita Falls & Lawton,OK,40,Stephens,40137
627,Wichita Falls & Lawton,OK,40,Tillman,40141
821,"Bend, OR",OR,41,Deschutes,41017
757,Boise,OR,41,Grant,41023
757,Boise,OR,41,Malheur,41045
801,Eugene,OR,41,Benton,41003
801,Eugene,OR,41,Coos,41011
801,Eugene,OR,41,Douglas,41019
801,Eugene,OR,41,Lane,41039
813,Medford – Klamath Falls,OR,41,Curry,41015
813,Medford – Klamath Falls,OR,41,Jackson,41029
813,Medford – Klamath Falls,OR,41,Josephine,41033
813,Medford – Klamath Falls,OR,41,Klamath,41035
813,Medford – Klamath Falls,OR,41,Lake,41037
820,"Portland, OR",OR,41,Baker,41001
820,"Portland, OR",OR,41,Clackamas,41005
820,"Portland, OR",OR,41,Clatsop,41007
820,"Portland, OR",OR,41,Columbia,41009
820,"Portland, OR",OR,41,Crook,41013
820,"Portland, OR",OR,41,Gilliam,41021
820,"Portland, OR",OR,41,Harney,41025
820,"Portland, OR",OR,41,Hood River,41027
820,"Portland, OR",OR,41,Jefferson,41031
820,"Portland, OR",OR,41,Lincoln,41041
820,"Portland, OR",OR,41,Linn,41043
820,"Portland, OR",OR,41,Marion,41047
820,"Portland, OR",OR,41,Morrow,41049
820,"Portland, OR",OR,41,Multnomah,41051
820,"Portland, OR",OR,41,Polk,41053
820,"Portland, OR",OR,41,Sherman,41055
820,"Portland, OR",OR,41,Tillamook,41057
820,"Portland, OR",OR,41,Union,41061
820,"Portland, OR",OR,41,Wasco,41065
820,"Portland, OR",OR,41,Washington,41067
820,"Portland, OR",OR,41,Wheeler,41069
820,"Portland, OR",OR,41,Yamhill,41071
881,Spokane,OR,41,Wallowa,41063
810,Yakima – Pasco – Richland – Kennewick,OR,41,Umatilla,41059
514,Buffalo,PA,42,Mc Kean,42083
514,Buffalo,PA,42,Potter,42105
565,Elmira (Corning),PA,42,Tioga,42117
516,Erie,PA,42,Crawford,42039
516,Erie,PA,42,Erie,42049
516,Erie,PA,42,Warren,42123
566,Harrisburg – Lancaster – Lebanon – York,PA,42,Adams,42001
566,Harrisburg – Lancaster – Lebanon – York,PA,42,Cumberland,42041
566,Harrisburg – Lancaster – Lebanon – York,PA,42,Dauphin,42043
566,Harrisburg – Lancaster – Lebanon – York,PA,42,Juniata,42067
566,Harrisburg – Lancaster – Lebanon – York,PA,42,Lancaster,42071
566,Harrisburg – Lancaster – Lebanon – York,PA,42,Lebanon,42075
566,Harrisburg – Lancaster – Lebanon – York,PA,42,Mifflin,42087
566,Harrisburg – Lancaster – Lebanon – York,PA,42,Perry,42099
566,Harrisburg – Lancaster – Lebanon – York,PA,42,York,42133
574,Johnstown (PA) - Altoona (PA),PA,42,Bedford,42009
574,Johnstown (PA) - Altoona (PA),PA,42,Blair,42013
574,Johnstown (PA) - Altoona (PA),PA,42,Cambria,42021
574,Johnstown (PA) - Altoona (PA),PA,42,Cameron,42023
574,Johnstown (PA) - Altoona (PA),PA,42,Centre,42027
574,Johnstown (PA) - Altoona (PA),PA,42,Clearfield,42033
574,Johnstown (PA) - Altoona (PA),PA,42,Elk,42047
574,Johnstown (PA) - Altoona (PA),PA,42,Huntingdon,42061
574,Johnstown (PA) - Altoona (PA),PA,42,Jefferson,42065
574,Johnstown (PA) - Altoona (PA),PA,42,Somerset,42111
501,New York,PA,42,Pike,42103
504,Philadelphia,PA,42,Berks,42011
504,Philadelphia,PA,42,Bucks,42017
504,Philadelphia,PA,42,Chester,42029
504,Philadelphia,PA,42,Delaware,42045
504,Philadelphia,PA,42,Lehigh,42077
504,Philadelphia,PA,42,Montgomery,42091
504,Philadelphia,PA,42,Northampton,42095
504,Philadelphia,PA,42,Philadelphia,42101
508,Pittsburgh,PA,42,Allegheny,42003
508,Pittsburgh,PA,42,Armstrong,42005
508,Pittsburgh,PA,42,Beaver,42007
508,Pittsburgh,PA,42,Butler,42019
508,Pittsburgh,PA,42,Clarion,42031
508,Pittsburgh,PA,42,Fayette,42051
508,Pittsburgh,PA,42,Forest,42053
508,Pittsburgh,PA,42,Greene,42059
508,Pittsburgh,PA,42,Indiana,42063
508,Pittsburgh,PA,42,Lawrence,42073
508,Pittsburgh,PA,42,Venango,42121
508,Pittsburgh,PA,42,Washington,42125
508,Pittsburgh,PA,42,Westmoreland,42129
511,"Washington, DC (Hagerstown)",PA,42,Franklin,42055
511,"Washington, DC (Hagerstown)",PA,42,Fulton,42057
577,Wilkes-Barre – Scranton – Hazelton,PA,42,Bradford,42015
577,Wilkes-Barre – Scranton – Hazelton,PA,42,Carbon,42025
577,Wilkes-Barre – Scranton – Hazelton,PA,42,Clinton,42035
577,Wilkes-Barre – Scranton – Hazelton,PA,42,Columbia,42037
577,Wilkes-Barre – Scranton – Hazelton,PA,42,Lackawanna,42069
577,Wilkes-Barre – Scranton – Hazelton,PA,42,Luzerne,42079
577,Wilkes-Barre – Scranton – Hazelton,PA,42,Lycoming,42081
577,Wilkes-Barre – Scranton – Hazelton,PA,42,Monroe,42089
577,Wilkes-Barre – Scranton – Hazelton,PA,42,Montour,42093
577,Wilkes-Barre – Scranton – Hazelton,PA,42,Northumberland,42097
577,Wilkes-Barre – Scranton – Hazelton,PA,42,Schuylkill,42107
577,Wilkes-Barre – Scranton – Hazelton,PA,42,Snyder,42109
577,Wilkes-Barre – Scranton – Hazelton,PA,42,Sullivan,42113
577,Wilkes-Barre – Scranton – Hazelton,PA,42,Susquehanna,42115
577,Wilkes-Barre – Scranton – Hazelton,PA,42,Union,42119
577,Wilkes-Barre – Scranton – Hazelton,PA,42,Wayne,42127
577,Wilkes-Barre – Scranton – Hazelton,PA,42,Wyoming,42131
536,Youngstown,PA,42,Mercer,42085
521,Providence – New Bedford,RI,44,Bristol,44001
521,Providence – New Bedford,RI,44,Kent,44003
521,Providence – New Bedford,RI,44,Newport,44005
521,Providence – New Bedford,RI,44,Providence,44007
521,Providence – New Bedford,RI,44,Washington,44009
520,Augusta – Aiken,SC,45,Aiken,45003
520,Augusta – Aiken,SC,45,Allendale,45005
520,Augusta – Aiken,SC,45,Bamberg,45009
520,Augusta – Aiken,SC,45,Barnwell,45011
520,Augusta – Aiken,SC,45,Edgefield,45037
520,Augusta – Aiken,SC,45,Mccormick,45065
519,"Charleston, SC",SC,45,Berkeley,45015
519,"Charleston, SC",SC,45,Charleston,45019
519,"Charleston, SC",SC,45,Colleton,45029
519,"Charleston, SC",SC,45,Dorchester,45035
519,"Charleston, SC",SC,45,Georgetown,45043
519,"Charleston, SC",SC,45,Williamsburg,45089
517,Charlotte,SC,45,Chester,45023
517,Charlotte,SC,45,Chesterfield,45025
517,Charlotte,SC,45,Lancaster,45057
517,Charlotte,SC,45,York,45091
546,"Columbia, SC",SC,45,Calhoun,45017
546,"Columbia, SC",SC,45,Clarendon,45027
546,"Columbia, SC",SC,45,Fairfield,45039
546,"Columbia, SC",SC,45,Kershaw,45055
546,"Columbia, SC",SC,45,Lee,45061
546,"Columbia, SC",SC,45,Lexington,45063
546,"Columbia, SC",SC,45,Newberry,45071
546,"Columbia, SC",SC,45,Orangeburg,45075
546,"Columbia, SC",SC,45,Richland,45079
546,"Columbia, SC",SC,45,Saluda,45081
546,"Columbia, SC",SC,45,Sumter,45085
570,Florence (SC) - Myrtle Beach (SC),SC,45,Darlington,45031
570,Florence (SC) - Myrtle Beach (SC),SC,45,Dillon,45033
570,Florence (SC) - Myrtle Beach (SC),SC,45,Florence,45041
570,Florence (SC) - Myrtle Beach (SC),SC,45,Horry,45051
570,Florence (SC) - Myrtle Beach (SC),SC,45,Marion,45067
570,Florence (SC) - Myrtle Beach (SC),SC,45,Marlboro,45069
567,Greenville – Spartanburg – Asheville – Anderson,SC,45,Abbeville,45001
567,Greenville – Spartanburg – Asheville – Anderson,SC,45,Anderson,45007
567,Greenville – Spartanburg – Asheville – Anderson,SC,45,Cherokee,45021
567,Greenville – Spartanburg – Asheville – Anderson,SC,45,Greenville,45045
567,Greenville – Spartanburg – Asheville – Anderson,SC,45,Greenwood,45047
567,Greenville – Spartanburg – Asheville – Anderson,SC,45,Laurens,45059
567,Greenville – Spartanburg – Asheville – Anderson,SC,45,Oconee,45073
567,Greenville – Spartanburg – Asheville – Anderson,SC,45,Pickens,45077
567,Greenville – Spartanburg – Asheville – Anderson,SC,45,Spartanburg,45083
567,Greenville – Spartanburg – Asheville – Anderson,SC,45,Union,45087
507,Savannah,SC,45,Beaufort,45013
507,Savannah,SC,45,Hampton,45049
507,Savannah,SC,45,Jasper,45053
687,Minot – Bismarck – Dickinson (Williston),SD,46,Campbell,46021
687,Minot – Bismarck – Dickinson (Williston),SD,46,Corson,46031
764,Rapid City,SD,46,Bennett,46007
764,Rapid City,SD,46,Butte,46019
764,Rapid City,SD,46,Custer,46033
764,Rapid City,SD,46,Fall River,46047
764,Rapid City,SD,46,Haakon,46055
764,Rapid City,SD,46,Harding,46063
764,Rapid City,SD,46,Jackson,46071
764,Rapid City,SD,46,Lawrence,46081
764,Rapid City,SD,46,Meade,46093
764,Rapid City,SD,46,Pennington,46103
764,Rapid City,SD,46,Perkins,46105
764,Rapid City,SD,46,Oglala,46102
764,Rapid City,SD,46,Ziebach,46137
624,Sioux City,SD,46,Union,46127
725,Sioux Falls (Mitchell),SD,46,Aurora,46003
725,Sioux Falls (Mitchell),SD,46,Beadle,46005
725,Sioux Falls (Mitchell),SD,46,Bon Homme,46009
725,Sioux Falls (Mitchell),SD,46,Brookings,46011
725,Sioux Falls (Mitchell),SD,46,Brown,46013
725,Sioux Falls (Mitchell),SD,46,Brule,46015
725,Sioux Falls (Mitchell),SD,46,Buffalo,46017
725,Sioux Falls (Mitchell),SD,46,Charles Mix,46023
725,Sioux Falls (Mitchell),SD,46,Clark,46025
725,Sioux Falls (Mitchell),SD,46,Clay,46027
725,Sioux Falls (Mitchell),SD,46,Codington,46029
725,Sioux Falls (Mitchell),SD,46,Davison,46035
725,Sioux Falls (Mitchell),SD,46,Day,46037
725,Sioux Falls (Mitchell),SD,46,Deuel,46039
725,Sioux Falls (Mitchell),SD,46,Dewey,46041
725,Sioux Falls (Mitchell),SD,46,Douglas,46043
725,Sioux Falls (Mitchell),SD,46,Edmunds,46045
725,Sioux Falls (Mitchell),SD,46,Faulk,46049
725,Sioux Falls (Mitchell),SD,46,Grant,46051
725,Sioux Falls (Mitchell),SD,46,Gregory,46053
725,Sioux Falls (Mitchell),SD,46,Hamlin,46057
725,Sioux Falls (Mitchell),SD,46,Hand,46059
725,Sioux Falls (Mitchell),SD,46,Hanson,46061
725,Sioux Falls (Mitchell),SD,46,Hughes,46065
725,Sioux Falls (Mitchell),SD,46,Hutchinson,46067
725,Sioux Falls (Mitchell),SD,46,Hyde,46069
725,Sioux Falls (Mitchell),SD,46,Jerauld,46073
725,Sioux Falls (Mitchell),SD,46,Jones,46075
725,Sioux Falls (Mitchell),SD,46,Kingsbury,46077
725,Sioux Falls (Mitchell),SD,46,Lake,46079
725,Sioux Falls (Mitchell),SD,46,Lincoln,46083
725,Sioux Falls (Mitchell),SD,46,Lyman,46085
725,Sioux Falls (Mitchell),SD,46,Mccook,46087
725,Sioux Falls (Mitchell),SD,46,Mcpherson,46089
725,Sioux Falls (Mitchell),SD,46,Marshall,46091
725,Sioux Falls (Mitchell),SD,46,Mellette,46095
725,Sioux Falls (Mitchell),SD,46,Miner,46097
725,Sioux Falls (Mitchell),SD,46,Minnehaha,46099
725,Sioux Falls (Mitchell),SD,46,Moody,46101
725,Sioux Falls (Mitchell),SD,46,Potter,46107
725,Sioux Falls (Mitchell),SD,46,Roberts,46109
725,Sioux Falls (Mitchell),SD,46,Sanborn,46111
725,Sioux Falls (Mitchell),SD,46,Spink,46115
725,Sioux Falls (Mitchell),SD,46,Stanley,46117
725,Sioux Falls (Mitchell),SD,46,Sully,46119
725,Sioux Falls (Mitchell),SD,46,Todd,46121
725,Sioux Falls (Mitchell),SD,46,Tripp,46123
725,Sioux Falls (Mitchell),SD,46,Turner,46125
725,Sioux Falls (Mitchell),SD,46,Walworth,46129
725,Sioux Falls (Mitchell),SD,46,Yankton,46135
575,Chattanooga,TN,47,Bledsoe,47007
575,Chattanooga,TN,47,Bradley,47011
575,Chattanooga,TN,47,Grundy,47061
575,Chattanooga,TN,47,Hamilton,47065
575,Chattanooga,TN,47,Mcminn,47107
575,Chattanooga,TN,47,Marion,47115
575,Chattanooga,TN,47,Meigs,47121
575,Chattanooga,TN,47,Polk,47139
575,Chattanooga,TN,47,Rhea,47143
575,Chattanooga,TN,47,Sequatchie,47153
691,Huntsville – Decatur (Florence),TN,47,Lincoln,47103
639,"Jackson, TN",TN,47,Carroll,47017
639,"Jackson, TN",TN,47,Chester,47023
639,"Jackson, TN",TN,47,Decatur,47039
639,"Jackson, TN",TN,47,Hardin,47071
639,"Jackson, TN",TN,47,Henderson,47077
639,"Jackson, TN",TN,47,Madison,47113
531,"Tri-Cities, TN-VA",TN,47,Carter,47019
531,"Tri-Cities, TN-VA",TN,47,Greene,47059
531,"Tri-Cities, TN-VA",TN,47,Hawkins,47073
531,"Tri-Cities, TN-VA",TN,47,Johnson,47091
531,"Tri-Cities, TN-VA",TN,47,Sullivan,47163
531,"Tri-Cities, TN-VA",TN,47,Unicoi,47171
531,"Tri-Cities, TN-VA",TN,47,Washington,47179
557,Knoxville,TN,47,Anderson,47001
557,Knoxville,TN,47,Blount,47009
557,Knoxville,TN,47,Campbell,47013
557,Knoxville,TN,47,Claiborne,47025
557,Knoxville,TN,47,Cocke,47029
557,Knoxville,TN,47,Cumberland,47035
557,Knoxville,TN,47,Fentress,47049
557,Knoxville,TN,47,Grainger,47057
557,Knoxville,TN,47,Hamblen,47063
557,Knoxville,TN,47,Hancock,47067
557,Knoxville,TN,47,Jefferson,47089
557,Knoxville,TN,47,Knox,47093
557,Knoxville,TN,47,Loudon,47105
557,Knoxville,TN,47,Monroe,47123
557,Knoxville,TN,47,Morgan,47129
557,Knoxville,TN,47,Roane,47145
557,Knoxville,TN,47,Scott,47151
557,Knoxville,TN,47,Sevier,47155
557,Knoxville,TN,47,Union,47173
640,Memphis,TN,47,Crockett,47033
640,Memphis,TN,47,Dyer,47045
640,Memphis,TN,47,Fayette,47047
640,Memphis,TN,47,Gibson,47053
640,Memphis,TN,47,Hardeman,47069
640,Memphis,TN,47,Haywood,47075
640,Memphis,TN,47,Lauderdale,47097
640,Memphis,TN,47,Mcnairy,47109
640,Memphis,TN,47,Shelby,47157
640,Memphis,TN,47,Tipton,47167
659,Nashville,TN,47,Bedford,47003
659,Nashville,TN,47,Benton,47005
659,Nashville,TN,47,Cannon,47015
659,Nashville,TN,47,Cheatham,47021
659,Nashville,TN,47,Clay,47027
659,Nashville,TN,47,Coffee,47031
659,Nashville,TN,47,Davidson,47037
659,Nashville,TN,47,Dekalb,47041
659,Nashville,TN,47,Dickson,47043
659,Nashville,TN,47,Franklin,47051
659,Nashville,TN,47,Giles,47055
659,Nashville,TN,47,Henry,47079
659,Nashville,TN,47,Hickman,47081
659,Nashville,TN,47,Houston,47083
659,Nashville,TN,47,Humphreys,47085
659,Nashville,TN,47,Jackson,47087
659,Nashville,TN,47,Lawrence,47099
659,Nashville,TN,47,Lewis,47101
659,Nashville,TN,47,Macon,47111
659,Nashville,TN,47,Marshall,47117
659,Nashville,TN,47,Maury,47119
659,Nashville,TN,47,Montgomery,47125
659,Nashville,TN,47,Moore,47127
659,Nashville,TN,47,Overton,47133
659,Nashville,TN,47,Perry,47135
659,Nashville,TN,47,Pickett,47137
659,Nashville,TN,47,Putnam,47141
659,Nashville,TN,47,Robertson,47147
659,Nashville,TN,47,Rutherford,47149
659,Nashville,TN,47,Smith,47159
659,Nashville,TN,47,Stewart,47161
659,Nashville,TN,47,Sumner,47165
659,Nashville,TN,47,Trousdale,47169
659,Nashville,TN,47,Van Buren,47175
659,Nashville,TN,47,Warren,47177
659,Nashville,TN,47,Wayne,47181
659,Nashville,TN,47,White,47185
659,Nashville,TN,47,Williamson,47187
659,Nashville,TN,47,Wilson,47189
632,Paducah – Cape Girardeau – Harrisburg,TN,47,Lake,47095
632,Paducah – Cape Girardeau – Harrisburg,TN,47,Obion,47131
632,Paducah – Cape Girardeau – Harrisburg,TN,47,Weakley,47183
662,Abilene – Sweetwater,TX,48,Brown,48049
662,Abilene – Sweetwater,TX,48,Callahan,48059
662,Abilene – Sweetwater,TX,48,Coleman,48083
662,Abilene – Sweetwater,TX,48,Eastland,48133
662,Abilene – Sweetwater,TX,48,Fisher,48151
662,Abilene – Sweetwater,TX,48,Haskell,48207
662,Abilene – Sweetwater,TX,48,Jones,48253
662,Abilene – Sweetwater,TX,48,Knox,48275
662,Abilene – Sweetwater,TX,48,Mitchell,48335
662,Abilene – Sweetwater,TX,48,Nolan,48353
662,Abilene – Sweetwater,TX,48,Runnels,48399
662,Abilene – Sweetwater,TX,48,Scurry,48415
662,Abilene – Sweetwater,TX,48,Shackelford,48417
662,Abilene – Sweetwater,TX,48,Stephens,48429
662,Abilene – Sweetwater,TX,48,Stonewall,48433
662,Abilene – Sweetwater,TX,48,Taylor,48441
634,Amarillo,TX,48,Armstrong,48011
634,Amarillo,TX,48,Briscoe,48045
634,Amarillo,TX,48,Carson,48065
634,Amarillo,TX,48,Castro,48069
634,Amarillo,TX,48,Childress,48075
634,Amarillo,TX,48,Collingsworth,48087
634,Amarillo,TX,48,Cottle,48101
634,Amarillo,TX,48,Dallam,48111
634,Amarillo,TX,48,Deaf Smith,48117
634,Amarillo,TX,48,Donley,48129
634,Amarillo,TX,48,Gray,48179
634,Amarillo,TX,48,Hall,48191
634,Amarillo,TX,48,Hansford,48195
634,Amarillo,TX,48,Hartley,48205
634,Amarillo,TX,48,Hemphill,48211
634,Amarillo,TX,48,Hutchinson,48233
634,Amarillo,TX,48,Lipscomb,48295
634,Amarillo,TX,48,Moore,48341
634,Amarillo,TX,48,Ochiltree,48357
634,Amarillo,TX,48,Oldham,48359
634,Amarillo,TX,48,Parmer,48369
634,Amarillo,TX,48,Potter,48375
634,Amarillo,TX,48,Randall,48381
634,Amarillo,TX,48,Roberts,48393
634,Amarillo,TX,48,Sherman,48421
634,Amarillo,TX,48,Swisher,48437
634,Amarillo,TX,48,Wheeler,48483
635,Austin,TX,48,Bastrop,48021
635,Austin,TX,48,Blanco,48031
635,Austin,TX,48,Burnet,48053
635,Austin,TX,48,Caldwell,48055
635,Austin,TX,48,Fayette,48149
635,Austin,TX,48,Gillespie,48171
635,Austin,TX,48,Hays,48209
635,Austin,TX,48,Lee,48287
635,Austin,TX,48,Llano,48299
635,Austin,TX,48,Mason,48319
635,Austin,TX,48,Travis,48453
635,Austin,TX,48,Williamson,48491
692,Beaumont – Port Arthur,TX,48,Hardin,48199
692,Beaumont – Port Arthur,TX,48,Jasper,48241
692,Beaumont – Port Arthur,TX,48,Jefferson,48245
692,Beaumont – Port Arthur,TX,48,Newton,48351
692,Beaumont – Port Arthur,TX,48,Orange,48361
692,Beaumont – Port Arthur,TX,48,Tyler,48457
600,Corpus Christi,TX,48,Aransas,48007
600,Corpus Christi,TX,48,Bee,48025
600,Corpus Christi,TX,48,Brooks,48047
600,Corpus Christi,TX,48,Duval,48131
600,Corpus Christi,TX,48,Jim Hogg,48247
600,Corpus Christi,TX,48,Jim Wells,48249
600,Corpus Christi,TX,48,Kenedy,48261
600,Corpus Christi,TX,48,Kleberg,48273
600,Corpus Christi,TX,48,Live Oak,48297
600,Corpus Christi,TX,48,Nueces,48355
600,Corpus Christi,TX,48,Refugio,48391
600,Corpus Christi,TX,48,San Patricio,48409
623,Dallas – Fort Worth,TX,48,Anderson,48001
623,Dallas – Fort Worth,TX,48,Bosque,48035
623,Dallas – Fort Worth,TX,48,Collin,48085
623,Dallas – Fort Worth,TX,48,Comanche,48093
623,Dallas – Fort Worth,TX,48,Cooke,48097
623,Dallas – Fort Worth,TX,48,Dallas,48113
623,Dallas – Fort Worth,TX,48,Delta,48119
623,Dallas – Fort Worth,TX,48,Denton,48121
623,Dallas – Fort Worth,TX,48,Ellis,48139
623,Dallas – Fort Worth,TX,48,Erath,48143
623,Dallas – Fort Worth,TX,48,Fannin,48147
623,Dallas – Fort Worth,TX,48,Freestone,48161
623,Dallas – Fort Worth,TX,48,Hamilton,48193
623,Dallas – Fort Worth,TX,48,Henderson,48213
623,Dallas – Fort Worth,TX,48,Hill,48217
623,Dallas – Fort Worth,TX,48,Hood,48221
623,Dallas – Fort Worth,TX,48,Hopkins,48223
623,Dallas – Fort Worth,TX,48,Hunt,48231
623,Dallas – Fort Worth,TX,48,Jack,48237
623,Dallas – Fort Worth,TX,48,Johnson,48251
623,Dallas – Fort Worth,TX,48,Kaufman,48257
623,Dallas – Fort Worth,TX,48,Lamar,48277
623,Dallas – Fort Worth,TX,48,Navarro,48349
623,Dallas – Fort Worth,TX,48,Palo Pinto,48363
623,Dallas – Fort Worth,TX,48,Parker,48367
623,Dallas – Fort Worth,TX,48,Rains,48379
623,Dallas – Fort Worth,TX,48,Red River,48387
623,Dallas – Fort Worth,TX,48,Rockwall,48397
623,Dallas – Fort Worth,TX,48,Somervell,48425
623,Dallas – Fort Worth,TX,48,Tarrant,48439
623,Dallas – Fort Worth,TX,48,Van Zandt,48467
623,Dallas – Fort Worth,TX,48,Wise,48497
765,El Paso (TX),TX,48,Culberson,48109
765,El Paso (TX),TX,48,El Paso,48141
765,El Paso (TX),TX,48,Hudspeth,48229
636,Harlingen – Weslaco – Brownsville – McAllen,TX,48,Cameron,48061
636,Harlingen – Weslaco – Brownsville – McAllen,TX,48,Hidalgo,48215
636,Harlingen – Weslaco – Brownsville – McAllen,TX,48,Starr,48427
636,Harlingen – Weslaco – Brownsville – McAllen,TX,48,Willacy,48489
618,Houston,TX,48,Austin,48015
618,Houston,TX,48,Brazoria,48039
618,Houston,TX,48,Calhoun,48057
618,Houston,TX,48,Chambers,48071
618,Houston,TX,48,Colorado,48089
618,Houston,TX,48,Fort Bend,48157
618,Houston,TX,48,Galveston,48167
618,Houston,TX,48,Grimes,48185
618,Houston,TX,48,Harris,48201
618,Houston,TX,48,Jackson,48239
618,Houston,TX,48,Liberty,48291
618,Houston,TX,48,Matagorda,48321
618,Houston,TX,48,Montgomery,48339
618,Houston,TX,48,Polk,48373
618,Houston,TX,48,San Jacinto,48407
618,Houston,TX,48,Walker,48471
618,Houston,TX,48,Waller,48473
618,Houston,TX,48,Washington,48477
618,Houston,TX,48,Wharton,48481
749,Laredo,TX,48,Webb,48479
749,Laredo,TX,48,Zapata,48505
651,Lubbock,TX,48,Bailey,48017
651,Lubbock,TX,48,Borden,48033
651,Lubbock,TX,48,Cochran,48079
651,Lubbock,TX,48,Crosby,48107
651,Lubbock,TX,48,Dawson,48115
651,Lubbock,TX,48,Dickens,48125
651,Lubbock,TX,48,Floyd,48153
651,Lubbock,TX,48,Gaines,48165
651,Lubbock,TX,48,Garza,48169
651,Lubbock,TX,48,Hale,48189
651,Lubbock,TX,48,Hockley,48219
651,Lubbock,TX,48,Kent,48263
651,Lubbock,TX,48,Lamb,48279
651,Lubbock,TX,48,Lubbock,48303
651,Lubbock,TX,48,Lynn,48305
651,Lubbock,TX,48,Motley,48345
651,Lubbock,TX,48,Terry,48445
651,Lubbock,TX,48,Yoakum,48501
633,Odessa – Midland,TX,48,Andrews,48003
633,Odessa – Midland,TX,48,Brewster,48043
633,Odessa – Midland,TX,48,Crane,48103
633,Odessa – Midland,TX,48,Ector,48135
633,Odessa – Midland,TX,48,Glasscock,48173
633,Odessa – Midland,TX,48,Howard,48227
633,Odessa – Midland,TX,48,Jeff Davis,48243
633,Odessa – Midland,TX,48,Loving,48301
633,Odessa – Midland,TX,48,Martin,48317
633,Odessa – Midland,TX,48,Midland,48329
633,Odessa – Midland,TX,48,Pecos,48371
633,Odessa – Midland,TX,48,Presidio,48377
633,Odessa – Midland,TX,48,Reagan,48383
633,Odessa – Midland,TX,48,Reeves,48389
633,Odessa – Midland,TX,48,Terrell,48443
633,Odessa – Midland,TX,48,Upton,48461
633,Odessa – Midland,TX,48,Ward,48475
633,Odessa – Midland,TX,48,Winkler,48495
661,San Angelo,TX,48,Coke,48081
661,San Angelo,TX,48,Concho,48095
661,San Angelo,TX,48,Crockett,48105
661,San Angelo,TX,48,Irion,48235
661,San Angelo,TX,48,Kimble,48267
661,San Angelo,TX,48,Mcculloch,48307
661,San Angelo,TX,48,Menard,48327
661,San Angelo,TX,48,Schleicher,48413
661,San Angelo,TX,48,Sterling,48431
661,San Angelo,TX,48,Sutton,48435
661,San Angelo,TX,48,Tom Green,48451
641,San Antonio,TX,48,Atascosa,48013
641,San Antonio,TX,48,Bandera,48019
641,San Antonio,TX,48,Bexar,48029
641,San Antonio,TX,48,Comal,48091
641,San Antonio,TX,48,Dewitt,48123
641,San Antonio,TX,48,Dimmit,48127
641,San Antonio,TX,48,Edwards,48137
641,San Antonio,TX,48,Frio,48163
641,San Antonio,TX,48,Goliad,48175
641,San Antonio,TX,48,Gonzales,48177
641,San Antonio,TX,48,Guadalupe,48187
641,San Antonio,TX,48,Karnes,48255
641,San Antonio,TX,48,Kendall,48259
641,San Antonio,TX,48,Kerr,48265
641,San Antonio,TX,48,Kinney,48271
641,San Antonio,TX,48,La Salle,48283
641,San Antonio,TX,48,Lavaca,48285
641,San Antonio,TX,48,Mcmullen,48311
641,San Antonio,TX,48,Maverick,48323
641,San Antonio,TX,48,Medina,48325
641,San Antonio,TX,48,Real,48385
641,San Antonio,TX,48,Uvalde,48463
641,San Antonio,TX,48,Val Verde,48465
641,San Antonio,TX,48,Wilson,48493
641,San Antonio,TX,48,Zavala,48507
657,Sherman – Ada,TX,48,Grayson,48181
612,Shreveport,TX,48,Bowie,48037
612,Shreveport,TX,48,Cass,48067
612,Shreveport,TX,48,Harrison,48203
612,Shreveport,TX,48,Marion,48315
612,Shreveport,TX,48,Morris,48343
612,Shreveport,TX,48,Panola,48365
612,Shreveport,TX,48,Shelby,48419
612,Shreveport,TX,48,Titus,48449
709,Tyler – Longview (Lufkin & Nacogdoches),TX,48,Angelina,48005
709,Tyler – Longview (Lufkin & Nacogdoches),TX,48,Camp,48063
709,Tyler – Longview (Lufkin & Nacogdoches),TX,48,Cherokee,48073
709,Tyler – Longview (Lufkin & Nacogdoches),TX,48,Franklin,48159
709,Tyler – Longview (Lufkin & Nacogdoches),TX,48,Gregg,48183
709,Tyler – Longview (Lufkin & Nacogdoches),TX,48,Houston,48225
709,Tyler – Longview (Lufkin & Nacogdoches),TX,48,Nacogdoches,48347
709,Tyler – Longview (Lufkin & Nacogdoches),TX,48,Rusk,48401
709,Tyler – Longview (Lufkin & Nacogdoches),TX,48,Sabine,48403
709,Tyler – Longview (Lufkin & Nacogdoches),TX,48,San Augustine,48405
709,Tyler – Longview (Lufkin & Nacogdoches),TX,48,Smith,48423
709,Tyler – Longview (Lufkin & Nacogdoches),TX,48,Trinity,48455
709,Tyler – Longview (Lufkin & Nacogdoches),TX,48,Upshur,48459
709,Tyler – Longview (Lufkin & Nacogdoches),TX,48,Wood,48499
626,Victoria,TX,48,Victoria,48469
625,Waco – Temple – Bryan,TX,48,Bell,48027
625,Waco – Temple – Bryan,TX,48,Brazos,48041
625,Waco – Temple – Bryan,TX,48,Burleson,48051
625,Waco – Temple – Bryan,TX,48,Coryell,48099
625,Waco – Temple – Bryan,TX,48,Falls,48145
625,Waco – Temple – Bryan,TX,48,Lampasas,48281
625,Waco – Temple – Bryan,TX,48,Leon,48289
625,Waco – Temple – Bryan,TX,48,Limestone,48293
625,Waco – Temple – Bryan,TX,48,Mclennan,48309
625,Waco – Temple – Bryan,TX,48,Madison,48313
625,Waco – Temple – Bryan,TX,48,Milam,48331
625,Waco – Temple – Bryan,TX,48,Mills,48333
625,Waco – Temple – Bryan,TX,48,Robertson,48395
625,Waco – Temple – Bryan,TX,48,San Saba,48411
627,Wichita Falls & Lawton,TX,48,Archer,48009
627,Wichita Falls & Lawton,TX,48,Baylor,48023
627,Wichita Falls & Lawton,TX,48,Clay,48077
627,Wichita Falls & Lawton,TX,48,Foard,48155
627,Wichita Falls & Lawton,TX,48,Hardeman,48197
627,Wichita Falls & Lawton,TX,48,King,48269
627,Wichita Falls & Lawton,TX,48,Montague,48337
627,Wichita Falls & Lawton,TX,48,Throckmorton,48447
627,Wichita Falls & Lawton,TX,48,Wichita,48485
627,Wichita Falls & Lawton,TX,48,Wilbarger,48487
627,Wichita Falls & Lawton,TX,48,Young,48503
770,Salt Lake City,UT,49,Beaver,49001
770,Salt Lake City,UT,49,Box Elder,49003
770,Salt Lake City,UT,49,Cache,49005
770,Salt Lake City,UT,49,Carbon,49007
770,Salt Lake City,UT,49,Daggett,49009
770,Salt Lake City,UT,49,Davis,49011
770,Salt Lake City,UT,49,Duchesne,49013
770,Salt Lake City,UT,49,Emery,49015
770,Salt Lake City,UT,49,Garfield,49017
770,Salt Lake City,UT,49,Grand,49019
770,Salt Lake City,UT,49,Iron,49021
770,Salt Lake City,UT,49,Juab,49023
770,Salt Lake City,UT,49,Kane,49025
770,Salt Lake City,UT,49,Millard,49027
770,Salt Lake City,UT,49,Morgan,49029
770,Salt Lake City,UT,49,Piute,49031
770,Salt Lake City,UT,49,Rich,49033
770,Salt Lake City,UT,49,Salt Lake,49035
770,Salt Lake City,UT,49,San Juan,49037
770,Salt Lake City,UT,49,Sanpete,49039
770,Salt Lake City,UT,49,Sevier,49041
770,Salt Lake City,UT,49,Summit,49043
770,Salt Lake City,UT,49,Tooele,49045
770,Salt Lake City,UT,49,Uintah,49047
770,Salt Lake City,UT,49,Utah,49049
770,Salt Lake City,UT,49,Wasatch,49051
770,Salt Lake City,UT,49,Washington,49053
770,Salt Lake City,UT,49,Wayne,49055
770,Salt Lake City,UT,49,Weber,49057
559,Bluefield – Beckley – Oak Hill,VA,51,Tazewell,51185
584,Charlottesville,VA,51,Charlottesville City,51540
584,Charlottesville,VA,51,Albemarle,51003
584,Charlottesville,VA,51,Fluvanna,51065
584,Charlottesville,VA,51,Greene,51079
584,Charlottesville,VA,51,Madison,51113
518,Greensboro – H. Point – W. Salem,VA,51,Patrick,51141
569,Harrisonburg,VA,51,Harrisonburg City,51660
569,Harrisonburg,VA,51,Staunton City,51790
569,Harrisonburg,VA,51,Waynesboro City,51820
569,Harrisonburg,VA,51,Augusta,51015
569,Harrisonburg,VA,51,Rockingham,51165
531,"Tri-Cities, TN-VA",VA,51,Buchanan,51027
531,"Tri-Cities, TN-VA",VA,51,Dickenson,51051
531,"Tri-Cities, TN-VA",VA,51,Lee,51105
531,"Tri-Cities, TN-VA",VA,51,Russell,51167
531,"Tri-Cities, TN-VA",VA,51,Scott,51169
531,"Tri-Cities, TN-VA",VA,51,Smyth,51173
531,"Tri-Cities, TN-VA",VA,51,Washington,51191
531,"Tri-Cities, TN-VA",VA,51,Wise,51195
544,Norfolk – Portsmouth – Newport News,VA,51,Accomack,51001
544,Norfolk – Portsmouth – Newport News,VA,51,Gloucester,51073
544,Norfolk – Portsmouth – Newport News,VA,51,Isle Of Wight,51093
544,Norfolk – Portsmouth – Newport News,VA,51,James City,51095
544,Norfolk – Portsmouth – Newport News,VA,51,Mathews,51115
544,Norfolk – Portsmouth – Newport News,VA,51,Northampton,51131
544,Norfolk – Portsmouth – Newport News,VA,51,Southampton,51175
544,Norfolk – Portsmouth – Newport News,VA,51,Surry,51181
544,Norfolk – Portsmouth – Newport News,VA,51,York,51199
544,Norfolk – Portsmouth – Newport News,VA,51,Chesapeake City,51550
544,Norfolk – Portsmouth – Newport News,VA,51,Hampton City,51650
544,Norfolk – Portsmouth – Newport News,VA,51,Newport News City,51700
544,Norfolk – Portsmouth – Newport News,VA,51,Norfolk City,51710
544,Norfolk – Portsmouth – Newport News,VA,51,Portsmouth City,51740
544,Norfolk – Portsmouth – Newport News,VA,51,Suffolk City,51800
544,Norfolk – Portsmouth – Newport News,VA,51,Virginia Beach City,51810
544,Norfolk – Portsmouth – Newport News,VA,51,Franklin City,51620
544,Norfolk – Portsmouth – Newport News,VA,51,Norton City,51720
544,Norfolk – Portsmouth – Newport News,VA,51,Radford City,51750
544,Norfolk – Portsmouth – Newport News,VA,51,Williamsburg City,51830
560,Raleigh – Durham (Fayetteville),VA,51,Mecklenburg,51117
556,Richmond – Petersburg,VA,51,Amelia,51007
556,Richmond – Petersburg,VA,51,Brunswick,51025
556,Richmond – Petersburg,VA,51,Buckingham,51029
556,Richmond – Petersburg,VA,51,Caroline,51033
556,Richmond – Petersburg,VA,51,Charles City,51036
556,Richmond – Petersburg,VA,51,Chesterfield,51041
556,Richmond – Petersburg,VA,51,Cumberland,51049
556,Richmond – Petersburg,VA,51,Dinwiddie,51053
556,Richmond – Petersburg,VA,51,Essex,51057
556,Richmond – Petersburg,VA,51,Goochland,51075
556,Richmond – Petersburg,VA,51,Greensville,51081
556,Richmond – Petersburg,VA,51,Hanover,51085
556,Richmond – Petersburg,VA,51,Henrico,51087
556,Richmond – Petersburg,VA,51,King And Queen,51097
556,Richmond – Petersburg,VA,51,King William,51101
556,Richmond – Petersburg,VA,51,Lancaster,51103
556,Richmond – Petersburg,VA,51,Louisa,51109
556,Richmond – Petersburg,VA,51,Lunenburg,51111
556,Richmond – Petersburg,VA,51,Middlesex,51119
556,Richmond – Petersburg,VA,51,New Kent,51127
556,Richmond – Petersburg,VA,51,Northumberland,51133
556,Richmond – Petersburg,VA,51,Nottoway,51135
556,Richmond – Petersburg,VA,51,Orange,51137
556,Richmond – Petersburg,VA,51,Powhatan,51145
556,Richmond – Petersburg,VA,51,Prince Edward,51147
556,Richmond – Petersburg,VA,51,Prince George,51149
556,Richmond – Petersburg,VA,51,Richmond,51159
556,Richmond – Petersburg,VA,51,Sussex,51183
556,Richmond – Petersburg,VA,51,Richmond City,51760
556,Richmond – Petersburg,VA,51,Colonial Heights City,51570
556,Richmond – Petersburg,VA,51,Emporia City,51595
556,Richmond – Petersburg,VA,51,Hopewell City,51670
556,Richmond – Petersburg,VA,51,Poquoson City,51735
573,Roanoke – Lynchburg,VA,51,Alleghany,51005
573,Roanoke – Lynchburg,VA,51,Amherst,51009
573,Roanoke – Lynchburg,VA,51,Appomattox,51011
573,Roanoke – Lynchburg,VA,51,Bath,51017
573,Roanoke – Lynchburg,VA,51,Bedford,51019
573,Roanoke – Lynchburg,VA,51,Bland,51021
573,Roanoke – Lynchburg,VA,51,Botetourt,51023
573,Roanoke – Lynchburg,VA,51,Campbell,51031
573,Roanoke – Lynchburg,VA,51,Carroll,51035
573,Roanoke – Lynchburg,VA,51,Charlotte,51037
573,Roanoke – Lynchburg,VA,51,Craig,51045
573,Roanoke – Lynchburg,VA,51,Floyd,51063
573,Roanoke – Lynchburg,VA,51,Franklin,51067
573,Roanoke – Lynchburg,VA,51,Giles,51071
573,Roanoke – Lynchburg,VA,51,Grayson,51077
573,Roanoke – Lynchburg,VA,51,Halifax,51083
573,Roanoke – Lynchburg,VA,51,Henry,51089
573,Roanoke – Lynchburg,VA,51,Highland,51091
573,Roanoke – Lynchburg,VA,51,Montgomery,51121
573,Roanoke – Lynchburg,VA,51,Nelson,51125
573,Roanoke – Lynchburg,VA,51,Pittsylvania,51143
573,Roanoke – Lynchburg,VA,51,Pulaski,51155
573,Roanoke – Lynchburg,VA,51,Roanoke,51161
573,Roanoke – Lynchburg,VA,51,Rockbridge,51163
573,Roanoke – Lynchburg,VA,51,Wythe,51197
573,Roanoke – Lynchburg,VA,51,Bedford County,51019
573,Roanoke – Lynchburg,VA,51,Buena Vista City,51530
573,Roanoke – Lynchburg,VA,51,Covington City,51580
573,Roanoke – Lynchburg,VA,51,Danville City,51590
573,Roanoke – Lynchburg,VA,51,Galax City,51640
573,Roanoke – Lynchburg,VA,51,Lexington City,51678
573,Roanoke – Lynchburg,VA,51,Lynchburg City,51680
573,Roanoke – Lynchburg,VA,51,Martinsville City,51690
573,Roanoke – Lynchburg,VA,51,Roanoke City,51770
573,Roanoke – Lynchburg,VA,51,Salem City,51775
531,"Tri-Cities, TN-VA",VA,51,Bristol City,51520
531,"Tri-Cities, TN-VA",VA,51,Petersburg City,51730
511,"Washington, DC (Hagerstown)",VA,51,Arlington,51013
511,"Washington, DC (Hagerstown)",VA,51,Clarke,51043
511,"Washington, DC (Hagerstown)",VA,51,Culpeper,51047
511,"Washington, DC (Hagerstown)",VA,51,Fairfax,51059
511,"Washington, DC (Hagerstown)",VA,51,Fauquier,51061
511,"Washington, DC (Hagerstown)",VA,51,Frederick,51069
511,"Washington, DC (Hagerstown)",VA,51,King George,51099
511,"Washington, DC (Hagerstown)",VA,51,Loudoun,51107
511,"Washington, DC (Hagerstown)",VA,51,Page,51139
511,"Washington, DC (Hagerstown)",VA,51,Prince William,51153
511,"Washington, DC (Hagerstown)",VA,51,Rappahannock,51157
511,"Washington, DC (Hagerstown)",VA,51,Shenandoah,51171
511,"Washington, DC (Hagerstown)",VA,51,Spotsylvania,51177
511,"Washington, DC (Hagerstown)",VA,51,Stafford,51179
511,"Washington, DC (Hagerstown)",VA,51,Warren,51187
511,"Washington, DC (Hagerstown)",VA,51,Westmoreland,51193
511,"Washington, DC (Hagerstown)",VA,51,Alexandria City,51510
511,"Washington, DC (Hagerstown)",VA,51,Fairfax City,51600
511,"Washington, DC (Hagerstown)",VA,51,Falls Church City,51610
511,"Washington, DC (Hagerstown)",VA,51,Fredericksburg City,51630
511,"Washington, DC (Hagerstown)",VA,51,Manassas City,51683
511,"Washington, DC (Hagerstown)",VA,51,Manassas Park City,51685
511,"Washington, DC (Hagerstown)",VA,51,Winchester City,51840
532,Albany – Schenectady – Troy,VT,50,Bennington,50003
506,Boston (Manchester),VT,50,Windham,50025
523,Burlington – Plattsburgh,VT,50,Addison,50001
523,Burlington – Plattsburgh,VT,50,Caledonia,50005
523,Burlington – Plattsburgh,VT,50,Chittenden,50007
523,Burlington – Plattsburgh,VT,50,Essex,50009
523,Burlington – Plattsburgh,VT,50,Franklin,50011
523,Burlington – Plattsburgh,VT,50,Grand Isle,50013
523,Burlington – Plattsburgh,VT,50,Lamoille,50015
523,Burlington – Plattsburgh,VT,50,Orange,50017
523,Burlington – Plattsburgh,VT,50,Orleans,50019
523,Burlington – Plattsburgh,VT,50,Rutland,50021
523,Burlington – Plattsburgh,VT,50,Washington,50023
523,Burlington – Plattsburgh,VT,50,Windsor,50027
820,"Portland, OR",WA,53,Clark,53011
820,"Portland, OR",WA,53,Cowlitz,53015
820,"Portland, OR",WA,53,Klickitat,53039
820,"Portland, OR",WA,53,Skamania,53059
820,"Portland, OR",WA,53,Wahkiakum,53069
819,Seattle – Tacoma,WA,53,Chelan,53007
819,Seattle – Tacoma,WA,53,Clallam,53009
819,Seattle – Tacoma,WA,53,Douglas,53017
819,Seattle – Tacoma,WA,53,Grays Harbor,53027
819,Seattle – Tacoma,WA,53,Island,53029
819,Seattle – Tacoma,WA,53,Jefferson,53031
819,Seattle – Tacoma,WA,53,King,53033
819,Seattle – Tacoma,WA,53,Kitsap,53035
819,Seattle – Tacoma,WA,53,Lewis,53041
819,Seattle – Tacoma,WA,53,Mason,53045
819,Seattle – Tacoma,WA,53,Pacific,53049
819,Seattle – Tacoma,WA,53,Pierce,53053
819,Seattle – Tacoma,WA,53,San Juan,53055
819,Seattle – Tacoma,WA,53,Skagit,53057
819,Seattle – Tacoma,WA,53,Snohomish,53061
819,Seattle – Tacoma,WA,53,Thurston,53067
819,Seattle – Tacoma,WA,53,Whatcom,53073
881,Spokane,WA,53,Adams,53001
881,Spokane,WA,53,Asotin,53003
881,Spokane,WA,53,Columbia,53013
881,Spokane,WA,53,Ferry,53019
881,Spokane,WA,53,Garfield,53023
881,Spokane,WA,53,Grant,53025
881,Spokane,WA,53,Lincoln,53043
881,Spokane,WA,53,Okanogan,53047
881,Spokane,WA,53,Pend Oreille,53051
881,Spokane,WA,53,Spokane,53063
881,Spokane,WA,53,Stevens,53065
881,Spokane,WA,53,Whitman,53075
810,Yakima – Pasco – Richland – Kennewick,WA,53,Benton,53005
810,Yakima – Pasco – Richland – Kennewick,WA,53,Franklin,53021
810,Yakima – Pasco – Richland – Kennewick,WA,53,Kittitas,53037
810,Yakima – Pasco – Richland – Kennewick,WA,53,Walla Walla,53071
810,Yakima – Pasco – Richland – Kennewick,WA,53,Yakima,53077
676,Duluth – Superior,WI,55,Ashland,55003
676,Duluth – Superior,WI,55,Bayfield,55007
676,Duluth – Superior,WI,55,Douglas,55031
676,Duluth – Superior,WI,55,Iron,55051
676,Duluth – Superior,WI,55,Sawyer,55113
658,Green Bay – Appleton,WI,55,Brown,55009
658,Green Bay – Appleton,WI,55,Calumet,55015
658,Green Bay – Appleton,WI,55,Door,55029
658,Green Bay – Appleton,WI,55,Fond Du Lac,55039
658,Green Bay – Appleton,WI,55,Green Lake,55047
658,Green Bay – Appleton,WI,55,Kewaunee,55061
658,Green Bay – Appleton,WI,55,Manitowoc,55071
658,Green Bay – Appleton,WI,55,Marinette,55075
658,Green Bay – Appleton,WI,55,Menominee,55078
658,Green Bay – Appleton,WI,55,Oconto,55083
658,Green Bay – Appleton,WI,55,Outagamie,55087
658,Green Bay – Appleton,WI,55,Shawano,55115
658,Green Bay – Appleton,WI,55,Waupaca,55135
658,Green Bay – Appleton,WI,55,Waushara,55137
658,Green Bay – Appleton,WI,55,Winnebago,55139
702,La Crosse – Eau Claire,WI,55,Buffalo,55011
702,La Crosse – Eau Claire,WI,55,Chippewa,55017
702,La Crosse – Eau Claire,WI,55,Clark,55019
702,La Crosse – Eau Claire,WI,55,Crawford,55023
702,La Crosse – Eau Claire,WI,55,Dunn,55033
702,La Crosse – Eau Claire,WI,55,Eau Claire,55035
702,La Crosse – Eau Claire,WI,55,Jackson,55053
702,La Crosse – Eau Claire,WI,55,La Crosse,55063
702,La Crosse – Eau Claire,WI,55,Monroe,55081
702,La Crosse – Eau Claire,WI,55,Pepin,55091
702,La Crosse – Eau Claire,WI,55,Rusk,55107
702,La Crosse – Eau Claire,WI,55,Trempealeau,55121
702,La Crosse – Eau Claire,WI,55,Vernon,55123
669,Madison,WI,55,Columbia,55021
669,Madison,WI,55,Dane,55025
669,Madison,WI,55,Grant,55043
669,Madison,WI,55,Green,55045
669,Madison,WI,55,Iowa,55049
669,Madison,WI,55,Juneau,55057
669,Madison,WI,55,Lafayette,55065
669,Madison,WI,55,Marquette,55077
669,Madison,WI,55,Richland,55103
669,Madison,WI,55,Rock,55105
669,Madison,WI,55,Sauk,55111
553,Marquette,WI,55,Florence,55037
617,Milwaukee,WI,55,Dodge,55027
617,Milwaukee,WI,55,Jefferson,55055
617,Milwaukee,WI,55,Kenosha,55059
617,Milwaukee,WI,55,Milwaukee,55079
617,Milwaukee,WI,55,Ozaukee,55089
617,Milwaukee,WI,55,Racine,55101
617,Milwaukee,WI,55,Sheboygan,55117
617,Milwaukee,WI,55,Walworth,55127
617,Milwaukee,WI,55,Washington,55131
617,Milwaukee,WI,55,Waukesha,55133
613,Minneapolis – St. Paul,WI,55,Barron,55005
613,Minneapolis – St. Paul,WI,55,Burnett,55013
613,Minneapolis – St. Paul,WI,55,Pierce,55093
613,Minneapolis – St. Paul,WI,55,Polk,55095
613,Minneapolis – St. Paul,WI,55,St. Croix,55109
613,Minneapolis – St. Paul,WI,55,Washburn,55129
705,Wausau – Rhinelander,WI,55,Adams,55001
705,Wausau – Rhinelander,WI,55,Forest,55041
705,Wausau – Rhinelander,WI,55,Langlade,55067
705,Wausau – Rhinelander,WI,55,Lincoln,55069
705,Wausau – Rhinelander,WI,55,Marathon,55073
705,Wausau – Rhinelander,WI,55,Oneida,55085
705,Wausau – Rhinelander,WI,55,Portage,55097
705,Wausau – Rhinelander,WI,55,Price,55099
705,Wausau – Rhinelander,WI,55,Taylor,55119
705,Wausau – Rhinelander,WI,55,Vilas,55125
705,Wausau – Rhinelander,WI,55,Wood,55141
559,Bluefield – Beckley – Oak Hill,WV,54,Fayette,54019
559,Bluefield – Beckley – Oak Hill,WV,54,Greenbrier,54025
559,Bluefield – Beckley – Oak Hill,WV,54,Mcdowell,54047
559,Bluefield – Beckley – Oak Hill,WV,54,Mercer,54055
559,Bluefield – Beckley – Oak Hill,WV,54,Monroe,54063
559,Bluefield – Beckley – Oak Hill,WV,54,Pocahontas,54075
559,Bluefield – Beckley – Oak Hill,WV,54,Raleigh,54081
559,Bluefield – Beckley – Oak Hill,WV,54,Summers,54089
559,Bluefield – Beckley – Oak Hill,WV,54,Wyoming,54109
564,Charleston – Huntington,WV,54,Boone,54005
564,Charleston – Huntington,WV,54,Braxton,54007
564,Charleston – Huntington,WV,54,Cabell,54011
564,Charleston – Huntington,WV,54,Calhoun,54013
564,Charleston – Huntington,WV,54,Clay,54015
564,Charleston – Huntington,WV,54,Jackson,54035
564,Charleston – Huntington,WV,54,Kanawha,54039
564,Charleston – Huntington,WV,54,Lincoln,54043
564,Charleston – Huntington,WV,54,Logan,54045
564,Charleston – Huntington,WV,54,Mason,54053
564,Charleston – Huntington,WV,54,Mingo,54059
564,Charleston – Huntington,WV,54,Nicholas,54067
564,Charleston – Huntington,WV,54,Putnam,54079
564,Charleston – Huntington,WV,54,Roane,54087
564,Charleston – Huntington,WV,54,Wayne,54099
564,Charleston – Huntington,WV,54,Wirt,54105
598,Clarksburg – Weston,WV,54,Barbour,54001
598,Clarksburg – Weston,WV,54,Doddridge,54017
598,Clarksburg – Weston,WV,54,Gilmer,54021
598,Clarksburg – Weston,WV,54,Harrison,54033
598,Clarksburg – Weston,WV,54,Lewis,54041
598,Clarksburg – Weston,WV,54,Marion,54049
598,Clarksburg – Weston,WV,54,Randolph,54083
598,Clarksburg – Weston,WV,54,Ritchie,54085
598,Clarksburg – Weston,WV,54,Taylor,54091
598,Clarksburg – Weston,WV,54,Tucker,54093
598,Clarksburg – Weston,WV,54,Upshur,54097
598,Clarksburg – Weston,WV,54,Webster,54101
569,Harrisonburg,WV,54,Pendleton,54071
597,Parkersburg,WV,54,Pleasants,54073
597,Parkersburg,WV,54,Wood,54107
508,Pittsburgh,WV,54,Monongalia,54061
508,Pittsburgh,WV,54,Preston,54077
511,"Washington, DC (Hagerstown)",WV,54,Berkeley,54003
511,"Washington, DC (Hagerstown)",WV,54,Grant,54023
511,"Washington, DC (Hagerstown)",WV,54,Hampshire,54027
511,"Washington, DC (Hagerstown)",WV,54,Hardy,54031
511,"Washington, DC (Hagerstown)",WV,54,Jefferson,54037
511,"Washington, DC (Hagerstown)",WV,54,Mineral,54057
511,"Washington, DC (Hagerstown)",WV,54,Morgan,54065
554,Wheeling – Steubenville,WV,54,Brooke,54009
554,Wheeling – Steubenville,WV,54,Hancock,54029
554,Wheeling – Steubenville,WV,54,Marshall,54051
554,Wheeling – Steubenville,WV,54,Ohio,54069
554,Wheeling – Steubenville,WV,54,Tyler,54095
554,Wheeling – Steubenville,WV,54,Wetzel,54103
756,Billings,WY,56,Big Horn,56003
756,Billings,WY,56,Park,56029
767,Casper – Riverton,WY,56,Converse,56009
767,Casper – Riverton,WY,56,Fremont,56013
767,Casper – Riverton,WY,56,Hot Springs,56017
767,Casper – Riverton,WY,56,Natrona,56025
767,Casper – Riverton,WY,56,Washakie,56043
759,Cheyenne – Scottsbluff,WY,56,Goshen,56015
759,Cheyenne – Scottsbluff,WY,56,Laramie,56021
751,Denver,WY,56,Albany,56001
751,Denver,WY,56,Campbell,56005
751,Denver,WY,56,Carbon,56007
751,Denver,WY,56,Johnson,56019
751,Denver,WY,56,Niobrara,56027
751,Denver,WY,56,Platte,56031
758,Idaho Falls – Pocatello,WY,56,Teton,56039
764,Rapid City,WY,56,Crook,56011
764,Rapid City,WY,56,Sheridan,56033
764,Rapid City,WY,56,Weston,56045
770,Salt Lake City,WY,56,Lincoln,56023
770,Salt Lake City,WY,56,Sublette,56035
770,Salt Lake City,WY,56,Sweetwater,56037
770,Salt Lake City,WY,56,Uinta,56041
804,Palm Springs,CA,06,Riverside,06065
751,Denver,CO,08,Broomfield,08014
