<?php

namespace Database\Seeders\data\PingPostPublishers;

use App\Enums\Company\CompanyAdminStatus;
use App\Enums\CompanyConsolidatedStatus;
use App\Enums\CompanySalesStatus;
use App\Enums\LegacyCompanyAdminStatus;
use App\Models\Odin\Company;
use App\Models\Odin\Industry;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Models\Odin\IndustryService;
use App\Models\PingPostPublishers\PingPostPublisher;
use App\Models\PingPostPublishers\PingPostPublisherApiKey;
use App\Models\PingPostPublishers\PingPostPublisherIndustryService;
use App\Models\PingPostPublishers\PingPostPublisherTcpaLanguage;
use App\Services\Odin\PingPostPublishing\PingPostPublisherAPIs\PingPostPublisherApiAdopt;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AdoptPingPostPublisherSeeder extends Seeder
{
    use WithoutModelEvents;

    /**
     * Run the database seeds
     *
     * @return void
     */
    public function run()
    {
        // Confirm that publisher does not already exist
        $publisherModel = PingPostPublisher::query()->where(PingPostPublisher::FIELD_KEY, PingPostPublisherApiAdopt::KEY)->get()->first();

        // Do not create if already exists
        if ($publisherModel) {
            print "Adopt ping post publisher model already exists.\n";
            return;
        }

        // Find adopt company ID
        $companyModel = Company::query()->where(Company::FIELD_NAME, "AdoptAContractor")->get()->first();

        if (!$companyModel) {
            // Create company record
            print "Creating company record for AdoptAContractor\n";
            $companyModel = Company::create([
                Company::FIELD_NAME => "AdoptAContractor",
                Company::FIELD_ENTITY_NAME => "AdoptAContractor",
                Company::FIELD_WEBSITE => 'https://adopt-a-contractor.com/',
                Company::FIELD_CONSOLIDATED_STATUS => CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CANNOT_RECEIVE_LEADS->value,
                Company::FIELD_SALES_STATUS => CompanySalesStatus::PARTNER->value,
                Company::FIELD_ADMIN_STATUS => CompanyAdminStatus::ADMIN_LOCKED,
            ]);
        } else {
            print "Found Adopt company with ID {$companyModel->id}\n";
        }

        // Adopt specific data
        $data = [
            PingPostPublisherApiAdopt::DATA_PARTNER_ID => 'RFC',
            PingPostPublisherApiAdopt::DATA_SUB_ID => 'match',
            PingPostPublisherApiAdopt::DATA_RESP_TYPE => 'JSON',
        ];

        $publisherModel = PingPostPublisher::create([
            PingPostPublisher::FIELD_NAME => 'Adopt',
            PingPostPublisher::FIELD_KEY => PingPostPublisherApiAdopt::KEY,
            PingPostPublisher::FIELD_ACTIVE => false,
            PingPostPublisher::FIELD_COMPANY_ID => $companyModel->{Company::FIELD_ID},
            PingPostPublisher::FIELD_PING_URL => "http://api.letsmakealead.com/Ping_Partner.php",
            PingPostPublisher::FIELD_POST_URL => "http://api.letsmakealead.com/Post_Partner.php",
            PingPostPublisher::FIELD_DATA => $data,
            PingPostPublisher::FIELD_CREATED_AT => now(),
            PingPostPublisher::FIELD_UPDATED_AT => now(),
        ]);

        $apiKey = PingPostPublisherApiKey::create([
            PingPostPublisherApiKey::FIELD_PING_POST_PUBLISHER_ID => $publisherModel->id,
            PingPostPublisherApiKey::FIELD_KEY => 'test_mode'
            ,PingPostPublisherApiKey::FIELD_TYPE => PingPostPublisherApiKey::TYPE_MAIN,
            PingPostPublisherApiKey::FIELD_ACTIVE => true,
            PingPostPublisherApiKey::FIELD_CREATED_AT => now(),
            PingPostPublisherApiKey::FIELD_UPDATED_AT => now(),
        ]);

        // Industry Service mapping
        $isMap = [
            'roof-replacement' => [
                'roof_type' => [
                    'Shingle' => '271',
                    'Fibre Cement' => '272',
                    'FibreGlass' => '272',
                    'Metal' => '273',
                    'Tile' => '275',
                ],
                'default' => '271',
            ],
            'siding-replacement' => [
                'siding_material' => [
                    'vinyl' => '483',
                    'fibre_cement' => '480',
                    'wood' => '484',
                    'stone_veneer' => '479',
                    'stucco' => '482',
                    'brick' => '479',
                    'insulated_vinyl_siding' => '483',
                    'wood_siding' => '484',
                    'fibre_cement_siding' => '480',
                ],
                'default' => '483',
            ],
            'bathroom-remodeling' => '193',
            'windows-install-replace' => '232',
            'fence-installation' => [
                'fencing-type-install-repair' => [
                    'wood' => '108',
                    'vinyl or PVC' => '106',
                    'chain link' => '102',
                    'aluminum or steel' => '98',
                    'barbed wire' => '100',
                ],
                'default' => '108',
            ],
            'handyman' => '295',
            'appliance-installation' => '243',
            'kitchen-remodeling' => '199',
            'gardener' => '153',
            'central-air-install-replace' => '136',
            'exterminator' => '91',
            'build-new-structure' => '34',
            'driveway-installation' => '56',
            'cleaning-service' => '39',
            'foundation-install' => '314',
            'pool-installation' => '250',
            'flooring-install' => [
                'floor-type' => [
                    'vinyl' => '115',
                    'other' => '115',
                    'carpet' => '253',
                    'hardwood' => '112',
                    'laminate' => '113',
                    'stone' => '113',
                    'tile' => '113',
                ],
                'default' => '115',
            ],
            'plumbing-installation' => '184',
            'electrical-work' => '73',
            'remodel' => '200',
            'septic-tank-install' => '441',
            'sunroom-install' => '492',
            'paint-exterior' => '168',
            'porch-install' => '29',
            'land-survey' => '301',
            'carport-install' => '390',
            'insulation-installation' => '256',
            'foundation-repair' => '314',
            'basement-remodel' => '192',
            'awning-install' => '322',
            'retaining-wall-install' => '52',
            'doors-install-replace' => '31',
            'gutter-installation' => '206',
            'disability-remodel' => '203',
            'tank-install' => '186',
            'fireplace-installation' => '386',
            'solar-system-service' => '486',
            'sports-court-installation' => '34',
            'security-system-install' => '310',
            'dock-installation' => '507',
            'heat-pump-installation' => '146',
            'home-automation-install' => '435',
            'asbestos-testing' => '378',
            'shed-installation' => '36',
        ];

        $industryServices = IndustryService::query()
            ->join(Industry::TABLE, Industry::TABLE.'.'.Industry::FIELD_ID, '=', IndustryService::TABLE.'.'.IndustryService::FIELD_INDUSTRY_ID)
            ->get([
                IndustryService::TABLE.'.'.IndustryService::FIELD_ID,
                IndustryService::TABLE.'.'.IndustryService::FIELD_INDUSTRY_ID,
                IndustryService::TABLE.'.'.IndustryService::FIELD_SLUG,
                IndustryService::TABLE.'.'.IndustryService::FIELD_NAME,
                Industry::TABLE.'.'.Industry::FIELD_SLUG.' as industry_slug',
            ])->groupBy(IndustryService::FIELD_SLUG);

        foreach ($isMap as $is => $mappingData) {
            $industryService = $industryServices[$is]->first();

            if (is_array($mappingData)) {
                PingPostPublisherIndustryService::create([
                    PingPostPublisherIndustryService::FIELD_INDUSTRY_SERVICE_ID => $industryService->id,
                    PingPostPublisherIndustryService::FIELD_PING_POST_PUBLISHER_ID => $publisherModel->id,
                    PingPostPublisherIndustryService::FIELD_KEY => null,
                    PingPostPublisherIndustryService::FIELD_ACTIVE => true,
                    PingPostPublisherIndustryService::FIELD_DATA => $mappingData,
                    PingPostPublisherIndustryService::FIELD_CREATED_AT => now(),
                    PingPostPublisherIndustryService::FIELD_UPDATED_AT => now(),
                ]);
            } else {
                PingPostPublisherIndustryService::create([
                    PingPostPublisherIndustryService::FIELD_INDUSTRY_SERVICE_ID => $industryService->id,
                    PingPostPublisherIndustryService::FIELD_PING_POST_PUBLISHER_ID => $publisherModel->id,
                    PingPostPublisherIndustryService::FIELD_KEY => $mappingData,
                    PingPostPublisherIndustryService::FIELD_ACTIVE => true,
                    PingPostPublisherIndustryService::FIELD_DATA => null,
                    PingPostPublisherIndustryService::FIELD_CREATED_AT => now(),
                    PingPostPublisherIndustryService::FIELD_UPDATED_AT => now(),
                ]);
            }
        }

        // Seed the TCPA language
        PingPostPublisherTcpaLanguage::create([
            PingPostPublisherTcpaLanguage::FIELD_TCPA_TEXT => "Terms and Conditions, Privacy and TCPA Opt-In: By clicking the \"Continue\" button you agree to our Terms and Privacy Policy and authorize Fixr or our chosen __industry__ contractors to use the phone number/s entered. Some installers may use auto-dialers or send automated text messages. If they cannot contact you these may result in charges to you. You consent to receiving these communications even if the phone number entered above is on the \"Do Not Call\" register. All information is collected and used in accordance with our Privacy Policy and our Terms and Conditions.",
        ]);

        $roofingModel = IndustryEnum::ROOFING->model();
        $roofingServices = IndustryService::query()
            ->where(IndustryService::FIELD_INDUSTRY_ID, $roofingModel->id)
            ->get();

        foreach ($roofingServices as $service) {
            PingPostPublisherTcpaLanguage::create([
                PingPostPublisherTcpaLanguage::FIELD_INDUSTRY_SERVICE_ID => $service->id,
                PingPostPublisherTcpaLanguage::FIELD_TCPA_TEXT => "Terms and Conditions, Privacy and TCPA Opt-In: By clicking the \"Continue\" button you agree to our Terms and Privacy Policy and authorize Roofing Calculator or our chosen roofing installers to use the phone number/s entered. Some installers may use auto-dialers or send automated text messages. If they cannot contact you these may result in charges to you. You consent to receiving these communications even if the phone number entered above is on the \"Do Not Call\" register. All information is collected and used in accordance with our Privacy Policy and our Terms and Conditions.",
            ]);
        }
    }
}
