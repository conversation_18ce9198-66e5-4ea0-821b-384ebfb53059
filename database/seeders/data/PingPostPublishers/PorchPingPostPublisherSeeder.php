<?php

namespace Database\Seeders\data\PingPostPublishers;

use App\Enums\CompanyConsolidatedStatus;
use App\Enums\CompanySalesStatus;
use App\Enums\LegacyCompanyAdminStatus;
use App\Models\Odin\Company;
use App\Models\Odin\Industry;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Models\Odin\IndustryService;
use App\Models\PingPostPublishers\PingPostPublisher;
use App\Models\PingPostPublishers\PingPostPublisherApiKey;
use App\Models\PingPostPublishers\PingPostPublisherIndustryService;
use App\Models\PingPostPublishers\PingPostPublisherTcpaLanguage;
use App\Services\Odin\PingPostPublishing\PingPostPublisherAPIs\PingPostPublisherApiAdopt;
use App\Services\Odin\PingPostPublishing\PingPostPublisherAPIs\PingPostPublisherApiPorch;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PorchPingPostPublisherSeeder extends Seeder
{
    use WithoutModelEvents;

    /**
     * Run the database seeds
     *
     * @return void
     */
    public function run()
    {
        // Confirm that publisher does not already exist
        $publisherModel = PingPostPublisher::query()->where(PingPostPublisher::FIELD_KEY, PingPostPublisherApiPorch::KEY)->get()->first();

        // Do not create if already exists
        if ($publisherModel) {
            print "Porch ping post publisher model already exists.\n";
            return;
        }

        // Find adopt company ID
        $companyModel = Company::query()->where(Company::FIELD_NAME, "Porch")->get()->first();

        if (!$companyModel) {
            print "Porch company not found, please create then run this again.\n";
            return;
        } else {
            print "Found Porch company with ID {$companyModel->id}\n";
        }

        // Porch specific data
        $data = [
            PingPostPublisherApiPorch::PROXY_IP => '*************',
            PingPostPublisherApiPorch::PROXY_PORT => '80',
            PingPostPublisherApiPorch::PROXY_USERNAME => '',
            PingPostPublisherApiPorch::PROXY_PASSWORD => '',
        ];

        $publisherModel = PingPostPublisher::create([
            PingPostPublisher::FIELD_NAME => 'Porch',
            PingPostPublisher::FIELD_KEY => PingPostPublisherApiPorch::KEY,
            PingPostPublisher::FIELD_ACTIVE => false,
            PingPostPublisher::FIELD_COMPANY_ID => $companyModel->{Company::FIELD_ID},
            PingPostPublisher::FIELD_PING_URL => "https://api.qa.porch.com/v2/lead/ping/",
            PingPostPublisher::FIELD_POST_URL => "https://api.qa.porch.com/v2/lead/callback/",
            PingPostPublisher::FIELD_DATA => $data,
            PingPostPublisher::FIELD_CREATED_AT => now(),
            PingPostPublisher::FIELD_UPDATED_AT => now(),
        ]);

        $apiKey = PingPostPublisherApiKey::create([
            PingPostPublisherApiKey::FIELD_PING_POST_PUBLISHER_ID => $publisherModel->id,
            PingPostPublisherApiKey::FIELD_KEY => 'NzNlNmI5ZTAtZDcxMi00YThiLTljYWQtYmU1ZjNhNTgwODMy'
            ,PingPostPublisherApiKey::FIELD_TYPE => PingPostPublisherApiKey::TYPE_MAIN,
            PingPostPublisherApiKey::FIELD_ACTIVE => true,
            PingPostPublisherApiKey::FIELD_CREATED_AT => now(),
            PingPostPublisherApiKey::FIELD_UPDATED_AT => now(),
        ]);

        // Industry Service mapping
        $isMap = [
            'roof-replacement' => [
                'roof_type' => [
                    'Shingle' => 'Shingle',
                    'Fibre Cement' => 'Fibre Cement',
                    'FibreGlass' => 'FibreGlass',
                    'Metal' => 'Metal',
                    'Tile' => 'Tile',
                    '__default__' => 'Shingle',
                    '__key__' => 'material',
                ],
                '__service_name__' => 'Roofing',
                '__service_sub_type__' => 'roof-replacement',
            ],
            'siding-replacement' => [
                'siding_material' => [
                    'vinyl' => 'vinyl',
                    'fibre_cement' => 'fibre_cement',
                    'wood' => 'wood',
                    'stone_veneer' => 'stone_veneer',
                    'stucco' => 'stucco',
                    'brick' => 'brick',
                    'insulated_vinyl_siding' => 'insulated_vinyl_siding',
                    'wood_siding' => 'wood_siding',
                    'fibre_cement_siding' => 'fibre_cement_siding',
                    '__default__' => 'vinyl',
                    '__key__' => 'material',
                ],
                '__service_name__' => 'Siding',
                '__service_sub_type__' => 'siding-replacement',
            ],
            'bathroom-remodeling' => [
                '__service_name__' => 'Bathrooms',
                '__service_sub_type__' => 'bathroom-remodeling',
            ],
            'windows-install-replace' => [
                '__service_name__' => 'Windows',
                '__service_sub_type__' => 'windows-install-replace',
            ],
            'fence-installation' => [
                'fencing-type-install-repair' => [
                    'wood' => 'wood',
                    'vinyl or PVC' => 'vinyl or PVC',
                    'chain link' => 'chain link',
                    'aluminum or steel' => 'aluminum or steel',
                    'barbed wire' => 'barbed wire',
                    '__default__' => 'wood',
                    '__key__' => 'material',
                ],
                '__service_name__' => 'Fencing',
                '__service_sub_type__' => 'fence-installation',
            ],
            'handyman' => [
                '__service_name__' => 'Handyman',
                '__service_sub_type__' => 'handyman',
            ],
            'appliance-installation' => [
                '__service_name__' => 'Appliances',
                '__service_sub_type__' => 'appliance-installation',
            ],
            'appliance-repair' => [
                '__service_name__' => 'Appliances',
                '__service_sub_type__' => 'appliance-repair',
            ],
            'kitchen-remodeling' => [
                '__service_name__' => 'Kitchens',
                '__service_sub_type__' => 'kitchen-remodeling',
            ],
            'gardener' => [
                '__service_name__' => 'Gardener',
                '__service_sub_type__' => 'gardener',
            ],
            'central-air-install-replace' => [
                '__service_name__' => 'HVAC',
                '__service_sub_type__' => 'central-air-install-replace',
            ],
            'exterminator' => [
                '__service_name__' => 'Exterminator',
                '__service_sub_type__' => 'exterminator',
            ],
            'build-new-structure' => [
                '__service_name__' => 'Build New Structure',
                '__service_sub_type__' => 'build-new-structure',
            ],
            'driveway-installation' => [
                '__service_name__' => 'Driveway',
                '__service_sub_type__' => 'driveway-installation',
            ],
            'cleaning-service' => [
                '__service_name__' => 'Cleaning Service',
                '__service_sub_type__' => 'cleaning-service',
            ],
            'foundation-install' => [
                '__service_name__' => 'Concrete Foundation',
                '__service_sub_type__' => 'foundation-install',
            ],
            'pool-installation' => [
                '__service_name__' => 'Pool Installation',
                '__service_sub_type__' => 'pool-installation',
            ],
            'flooring-install' => [
                'floor-type' => [
                    'vinyl' => 'vinyl',
                    'other' => 'other',
                    'carpet' => 'carpet',
                    'hardwood' => 'hardwood',
                    'laminate' => 'laminate',
                    'stone' => 'stone',
                    'tile' => 'tile',
                    '__default__' => 'vinyl',
                    '__key__' => 'material',
                ],
                '__service_name__' => 'Flooring',
                '__service_sub_type__' => 'flooring-install',
            ],
            'plumbing-installation' => [
                '__service_name__' => 'Plumbing',
                '__service_sub_type__' => 'plumbing-installation',
            ],
            'electrical-work' => [
                '__service_name__' => 'Electrical',
                '__service_sub_type__' => 'electrical-work',
            ],
            'remodel' => [
                '__service_name__' => 'Living Room',
                '__service_sub_type__' => 'remodel',
            ],
            'septic-tank-install' => [
                '__service_name__' => 'Septic Tank System',
                '__service_sub_type__' => 'septic-tank-install',
            ],
            'sunroom-install' => [
                '__service_name__' => 'Sunroom',
                '__service_sub_type__' => 'sunroom-install',
            ],
            'paint-exterior' => [
                '__service_name__' => 'Exterior Painting',
                '__service_sub_type__' => 'paint-exterior',
            ],
            'porch-install' => [
                '__service_name__' => 'Porch',
                '__service_sub_type__' => 'porch-install',
            ],
            'carport-install' => [
                '__service_name__' => 'Carport',
                '__service_sub_type__' => 'carport-install',
            ],
            'insulation-installation' => [
                '__service_name__' => 'Insulation',
                '__service_sub_type__' => 'insulation-installation',
            ],
            'foundation-repair' => [
                '__service_name__' => 'Concrete Foundation',
                '__service_sub_type__' => 'foundation-repair',
            ],
            'basement-remodel' => [
                '__service_name__' => 'Basement Remodeling',
                '__service_sub_type__' => 'basement-remodel',
            ],
            'awning-install' => [
                '__service_name__' => 'Awnings',
                '__service_sub_type__' => 'awning-install',
            ],
            'retaining-wall-install' => [
                '__service_name__' => 'Retaining Wall',
                '__service_sub_type__' => 'retaining-wall-install',
            ],
            'doors-install-replace' => [
                '__service_name__' => 'Doors',
                '__service_sub_type__' => 'doors-install-replace',
            ],
            'gutter-installation' => [
                '__service_name__' => 'Gutter Installation',
                '__service_sub_type__' => 'gutter-installation',
            ],
            'disability-remodel' => [
                '__service_name__' => 'Disability Remodel',
                '__service_sub_type__' => 'disability-remodel',
            ],
            'tank-install' => [
                '__service_name__' => 'Tank Install',
                '__service_sub_type__' => 'tank-install',
            ],
            'fireplace-installation' => [
                '__service_name__' => 'Fireplace',
                '__service_sub_type__' => 'fireplace-installation',
            ],
            'solar-system-service' => [
                '__service_name__' => 'Solar System Servicing',
                '__service_sub_type__' => 'solar-system-service',
            ],
            'security-system-install' => [
                '__service_name__' => 'Home Security System',
                '__service_sub_type__' => 'security-system-install',
            ],
            'dock-installation' => [
                '__service_name__' => 'Docks',
                '__service_sub_type__' => 'dock-installation',
            ],
            'heat-pump-installation' => [
                '__service_name__' => 'Heat Pumps',
                '__service_sub_type__' => 'heat-pump-installation',
            ],
            'home-automation-install' => [
                '__service_name__' => 'Home Automation',
                '__service_sub_type__' => 'home-automation-install',
            ],
            'asbestos-testing' => [
                '__service_name__' => 'Asbestos Removal',
                '__service_sub_type__' => 'asbestos-testing',
            ],
            'shed-installation' => [
                '__service_name__' => 'Storage Shed',
                '__service_sub_type__' => 'shed-installation',
            ],
        ];

        $industryServices = IndustryService::query()
            ->join(Industry::TABLE, Industry::TABLE.'.'.Industry::FIELD_ID, '=', IndustryService::TABLE.'.'.IndustryService::FIELD_INDUSTRY_ID)
            ->get([
                IndustryService::TABLE.'.'.IndustryService::FIELD_ID,
                IndustryService::TABLE.'.'.IndustryService::FIELD_INDUSTRY_ID,
                IndustryService::TABLE.'.'.IndustryService::FIELD_SLUG,
                IndustryService::TABLE.'.'.IndustryService::FIELD_NAME,
                Industry::TABLE.'.'.Industry::FIELD_SLUG.' as industry_slug',
            ])->groupBy(IndustryService::FIELD_SLUG);

        foreach ($isMap as $is => $mappingData) {
            $industryService = $industryServices[$is]->first();

            if (is_array($mappingData)) {
                PingPostPublisherIndustryService::create([
                    PingPostPublisherIndustryService::FIELD_INDUSTRY_SERVICE_ID => $industryService->id,
                    PingPostPublisherIndustryService::FIELD_PING_POST_PUBLISHER_ID => $publisherModel->id,
                    PingPostPublisherIndustryService::FIELD_KEY => null,
                    PingPostPublisherIndustryService::FIELD_ACTIVE => true,
                    PingPostPublisherIndustryService::FIELD_DATA => $mappingData,
                    PingPostPublisherIndustryService::FIELD_CREATED_AT => now(),
                    PingPostPublisherIndustryService::FIELD_UPDATED_AT => now(),
                ]);
            } else {
                PingPostPublisherIndustryService::create([
                    PingPostPublisherIndustryService::FIELD_INDUSTRY_SERVICE_ID => $industryService->id,
                    PingPostPublisherIndustryService::FIELD_PING_POST_PUBLISHER_ID => $publisherModel->id,
                    PingPostPublisherIndustryService::FIELD_KEY => $mappingData,
                    PingPostPublisherIndustryService::FIELD_ACTIVE => true,
                    PingPostPublisherIndustryService::FIELD_DATA => null,
                    PingPostPublisherIndustryService::FIELD_CREATED_AT => now(),
                    PingPostPublisherIndustryService::FIELD_UPDATED_AT => now(),
                ]);
            }
        }
    }
}
