<?php

namespace Database\Seeders;

use App\Models\MissedProducts\OpportunityNotificationConfig;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class UpdateUuidColumnInOpportunityNotificationConfig extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        OpportunityNotificationConfig::query()
            ->whereNull(OpportunityNotificationConfig::FIELD_UUID)
            ->chunk(100, function ($configs) {
            /** @var Collection<OpportunityNotificationConfig> $configs */
            foreach ($configs as $config) {
                $config->update([
                    OpportunityNotificationConfig::FIELD_UUID => Str::uuid()
                ]);
            }
        });
    }
}
