<?php

namespace Database\Seeders;

use App\Models\SaleType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SaleTypesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table(SaleType::TABLE)->insert([
            [
                SaleType::FIELD_NAME => "Exclusive",
                SaleType::FIELD_KEY  => "exclusive",
                SaleType::FIELD_DEFAULT_TYPE => true,
                SaleType::FIELD_SALE_LIMIT => 1
            ],
            [
                SaleType::FIELD_NAME => "Duo",
                SaleType::FIELD_KEY  => "duo",
                SaleType::FIELD_DEFAULT_TYPE => true,
                SaleType::FIELD_SALE_LIMIT => 2
            ],
            [
                SaleType::FIELD_NAME => "Trio",
                SaleType::FIELD_KEY  => "trio",
                SaleType::FIELD_DEFAULT_TYPE => true,
                SaleType::FIELD_SALE_LIMIT => 3
            ],
            [
                SaleType::FIELD_NAME => "Quad",
                SaleType::FIELD_KEY  => "quad",
                SaleType::FIELD_DEFAULT_TYPE => true,
                SaleType::FIELD_SALE_LIMIT => 4
            ],
            [
                SaleType::FIELD_NAME => "Email Only",
                SaleType::FIELD_KEY  => "email_only",
                SaleType::FIELD_DEFAULT_TYPE => false,
                SaleType::FIELD_SALE_LIMIT => 4
            ],
            [
                SaleType::FIELD_NAME => "Unverified",
                SaleType::FIELD_KEY  => "unverified",
                SaleType::FIELD_DEFAULT_TYPE => false,
                SaleType::FIELD_SALE_LIMIT => 4
            ]
        ]);
    }
}
