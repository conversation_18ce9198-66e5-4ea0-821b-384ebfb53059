<?php

namespace Database\Seeders;

use App\Enums\PermissionType;
use App\Enums\RoleType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
class PermissionCompanyLeadUpdateChargeableStatusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        Permission::findOrCreate(PermissionType::COMPANY_LEAD_UPDATE_CHARGEABLE_STATUS->value);

        $admin = Role::findOrCreate(RoleType::ADMIN->value);

        $admin->givePermissionTo([
            PermissionType::COMPANY_LEAD_UPDATE_CHARGEABLE_STATUS->value,
        ]);
    }
}
