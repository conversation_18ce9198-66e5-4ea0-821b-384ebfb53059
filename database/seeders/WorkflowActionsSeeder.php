<?php

namespace Database\Seeders;

use App\Enums\ActionType;
use App\Models\Workflow;
use App\Models\WorkflowAction;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class WorkflowActionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $workflowIds = Workflow::query()->pluck(Workflow::FIELD_ID);

        $insertRows = [];
        foreach($workflowIds as $workflowId) {
            $insertRows[] = [
                WorkflowAction::FIELD_WORKFLOW_ID => $workflowId,
                WorkflowAction::FIELD_ACTION_TYPE => ActionType::ADD_NOTIFICATION->value,
                WorkflowAction::FIELD_DISPLAY_NAME => "Test Notification",
                WorkflowAction::FIELD_PAYLOAD => json_encode([
                    "message" => null,
                    "subject" => "{company-name} {company-id} {company-type} {company-status}",
                    "targets" => [
                        [
                            "target_type" => "staff",
                            "target_relation" => "sales-manager"
                        ]
                    ]
                ])
            ];

            if(count($insertRows) >= 250) {
                WorkflowAction::query()->insert($insertRows);

                $insertRows = [];
            }
        }

        if(!empty($insertRows)) {
            WorkflowAction::query()->insert($insertRows);
        }

        $workflowActionIds = WorkflowAction::query()->pluck(WorkflowAction::FIELD_WORKFLOW_ID, WorkflowAction::FIELD_ID);

        $caseStatements = [];
        foreach($workflowActionIds as $workflowActionId => $workflowId) {
            $caseStatements[$workflowId] = sprintf(
                "WHEN %s = %s THEN %s",
                Workflow::FIELD_ID,
                $workflowId,
                $workflowActionId
            );

            if(count($caseStatements) >= 200) {
                $caseStatementsQuery = implode(' ', $caseStatements);

                Workflow::query()
                    ->whereIn(Workflow::FIELD_ID, array_keys($caseStatements))
                    ->update([
                        Workflow::FIELD_ENTRY_ACTION_ID => DB::raw("(CASE {$caseStatementsQuery} END)")
                    ]);

                $caseStatements = [];
            }
        }

        if(!empty($caseStatements)) {
            $caseStatementsQuery = implode(' ', $caseStatements);

            Workflow::query()
                ->whereIn(Workflow::FIELD_ID, array_keys($caseStatements))
                ->update([
                    Workflow::FIELD_ENTRY_ACTION_ID => DB::raw("(CASE {$caseStatementsQuery} END)")
                ]);
        }
    }
}
