<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Spatie\Permission\PermissionRegistrar;

class SalesRolesSeeder extends Seeder
{
    /**
     * Create the initial roles and permissions.
     *
     * @return void
     */
    public function run()
    {
        // Reset cached roles and permissions
        app(PermissionRegistrar::class)->forgetCachedPermissions();

        // create permissions
        Permission::findOrCreate('sales-management');
        Permission::findOrCreate('sales-summary-dashboard');

        $role1 = Role::findOrCreate('account-manager');
        $role1->givePermissionTo('dashboard');
        $role1->givePermissionTo('companies');
        $role1->givePermissionTo('sales-summary-dashboard');

        $role2 = Role::findOrCreate('sales-manager');
        $role2->givePermissionTo('dashboard');
        $role2->givePermissionTo('sales-management');
        $role2->givePermissionTo('companies');
        $role1->givePermissionTo('sales-summary-dashboard');

        $adminRole = Role::findByName('admin');
        $adminRole->givePermissionTo('sales-management');
        $role1->givePermissionTo('sales-summary-dashboard');
    }
}
