<?php

namespace Database\Seeders;

use App\Models\Odin\Consumer;
use App\Models\Odin\Website;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ConsumerSeeder extends Seeder
{
    use WithoutModelEvents;

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Consumer::factory(50)->create();
    }
}
