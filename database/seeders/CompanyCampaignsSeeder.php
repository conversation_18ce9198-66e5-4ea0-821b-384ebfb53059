<?php

namespace Database\Seeders;

use App\Enums\Campaigns\CampaignStatus;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModule;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\Company;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

/**
 * @note This is for the experimental campaigns index
 */
class CompanyCampaignsSeeder extends Seeder
{
    use WithoutModelEvents;

    protected Company $company;

    protected CompanyCampaign $companyCampaign;

    protected CompanyCampaignLocationModule $companyCampaignLocationModule;

    protected CompanyCampaignLocationModuleLocation $companyCampaignLocationModuleLocation;

    protected Location $location;

    protected BudgetContainer $budgetContainer;

    protected User $user;

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->company = Company::first() ?? Company::factory()->createQuietly();

        $this->companyCampaign = CompanyCampaign::factory()
            ->location([
                'zip_code' => '07701',
            ])
            ->dateOfLastLeadPurchased(
                countOfProductAssignments: 2,
                dateOfLatest: now(),
            )
            ->createQuietly([
                'company_id' => $this->company->id,
                'status' => CampaignStatus::ACTIVE,
                'product_id' => Product::first()->id,
                'service_id' => IndustryService::first()->id,
            ]);
    }
}
