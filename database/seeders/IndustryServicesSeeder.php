<?php

namespace Database\Seeders;

use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class IndustryServicesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $industries = Industry::all()->pluck(Industry::FIELD_ID, Industry::FIELD_NAME)->toArray();

        DB::table(IndustryService::TABLE)->insert([
            [
                IndustryService::FIELD_NAME => "Installation",
                IndustryService::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG => 'solar-installation'
            ],
            [
                IndustryService::FIELD_NAME => "Repair",
                IndustryService::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG => 'solar-repair'
            ],
            [
                IndustryService::FIELD_NAME => 'Aggregator',
                IndustryService::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG => 'solar-aggregator'
            ],
            [
                IndustryService::FIELD_NAME => "Gutter Replacement",
                IndustryService::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG => 'gutter-replacement'
            ],
            [
                IndustryService::FIELD_NAME => "Roof Repair",
                IndustryService::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG => 'roof-repair'
            ],
            [
                IndustryService::FIELD_NAME => "Roof Installation",
                IndustryService::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG => 'roof-installation'
            ],
            [
                IndustryService::FIELD_NAME => "Roof Replacement",
                IndustryService::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG => 'roof-replacement'
            ],
            [
                IndustryService::FIELD_NAME => "AC Repair",
                IndustryService::FIELD_INDUSTRY_ID => $industries["HVAC"],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG => 'ac-repair'
            ]
        ]);
    }
}
