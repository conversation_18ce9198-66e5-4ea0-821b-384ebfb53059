<?php

namespace Database\Seeders;

use App\Models\LeadProcessingCallingTimeZoneConfiguration;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LeadProcessingCallingTimeZoneConfigurationsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table(LeadProcessingCallingTimeZoneConfiguration::TABLE)->insert([
            [
                LeadProcessingCallingTimeZoneConfiguration::FIELD_NAME => 'Eastern',
                LeadProcessingCallingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET => -5,
                LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR => 8,
                LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR => 20
            ],
            [
                LeadProcessingCallingTimeZoneConfiguration::FIELD_NAME => 'Central',
                LeadProcessingCallingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET => -6,
                LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR => 8,
                LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR => 20
            ],
            [
                LeadProcessingCallingTimeZoneConfiguration::FIELD_NAME => 'Mountain',
                LeadProcessingCallingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET => -7,
                LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR => 8,
                LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR => 20
            ],
            [
                LeadProcessingCallingTimeZoneConfiguration::FIELD_NAME => 'Pacific',
                LeadProcessingCallingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET => -8,
                LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR => 8,
                LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR => 20
            ],
            [
                LeadProcessingCallingTimeZoneConfiguration::FIELD_NAME => 'Alaska',
                LeadProcessingCallingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET => -9,
                LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR => 8,
                LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR => 20
            ],
            [
                LeadProcessingCallingTimeZoneConfiguration::FIELD_NAME => 'Hawaii',
                LeadProcessingCallingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET => -10,
                LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR => 8,
                LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR => 20
            ]
        ]);
    }
}
