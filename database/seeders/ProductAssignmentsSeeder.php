<?php

namespace Database\Seeders;

use App\Models\Odin\Company;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProductAssignmentsSeeder extends Seeder
{
    use WithoutModelEvents;

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $companyIds = Company::query()->limit(100)->pluck(Company::FIELD_ID)->toArray();
        $consumerProducts = ConsumerProduct::query()
            ->whereHas(ConsumerProduct::RELATION_CONSUMER, function($has) {
                $has->where(Consumer::FIELD_STATUS, Consumer::STATUS_COMPLETED);
            })
            ->where(ConsumerProduct::FIELD_GOOD_TO_SELL, true)
            ->where(ConsumerProduct::FIELD_STATUS, ConsumerProduct::STATUS_ALLOCATED)
            ->limit(100)
            ->select([
                ConsumerProduct::FIELD_ID,
                ConsumerProduct::FIELD_CONTACT_REQUESTS
            ])
            ->pluck(ConsumerProduct::FIELD_CONTACT_REQUESTS, ConsumerProduct::FIELD_ID)
            ->toArray();

        $consumerProductIds = [];
        foreach($consumerProducts as $id => $contactRequests) {
            for($i = 0; $i < $contactRequests; $i++) {
                $consumerProductIds[] = $id;
            }
        }

        sort($consumerProductIds, SORT_NUMERIC);

        $companyIdsCount = count($companyIds);

        $factory = ProductAssignment::factory();

        $recordsCount = count($consumerProductIds);

        $insertRows = [];
        for($i = 0; $i < $recordsCount; $i++) {
            $data = $factory->definition();

            $data[ProductAssignment::FIELD_COMPANY_ID] = $companyIds[$i % $companyIdsCount];
            $data[ProductAssignment::FIELD_CONSUMER_PRODUCT_ID] = $consumerProductIds[$i];
            $data[ProductAssignment::FIELD_SALE_TYPE_ID] = $consumerProducts[$consumerProductIds[$i]];
            $data[ProductAssignment::FIELD_PAYLOAD] = json_encode($data[ProductAssignment::FIELD_PAYLOAD]);

            $insertRows[] = $data;

            if(count($insertRows) >= 500) {
                DB::table(ProductAssignment::TABLE)->insert($insertRows);
                $insertRows = [];
            }
        }

        if(!empty($insertRows)) {
            DB::table(ProductAssignment::TABLE)->insert($insertRows);
        }
    }
}
