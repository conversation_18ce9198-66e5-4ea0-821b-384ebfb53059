<?php

namespace Database\Seeders;

use App\Factories\RulesetFactory;
use App\Models\Ruleset;
use Illuminate\Database\Seeder;
use Exception;

class RulesetsSeeder extends Seeder
{
    const DEFAULT_RULESET_ID = 1;
    /**
     * Run the database seeds.
     *
     * @return void
     * @throws Exception
     */
    public function run(): void
    {
        $found = Ruleset::query()->where(Ruleset::FIELD_ID, self::DEFAULT_RULESET_ID)->first();

        if (!$found) {
            $template = RulesetFactory::generateTemplate();
            $ruleset = new Ruleset();
            $ruleset->fill($template);
            $ruleset->save();
        }
    }
}
