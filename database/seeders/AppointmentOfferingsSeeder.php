<?php

namespace Database\Seeders;

use App\Enums\Odin\Product as ProductEnum;
use App\Models\Odin\AppointmentOffering;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Product;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ProductCampaignBudget;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AppointmentOfferingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $offeringDuration = (int) config('sales.appointments.offering_available_duration_min');

        $consumerProductIds = ConsumerProduct::query()->limit(3)->pluck(ConsumerProduct::FIELD_ID)->toArray();

        $productCampaignBudgetIds = ProductCampaignBudget::query()
            ->whereHas(ProductCampaignBudget::RELATION_PRODUCT_CAMPAIGN.'.'.ProductCampaign::RELATION_PRODUCT, function($has) {
                $has->where(Product::FIELD_NAME, ProductEnum::APPOINTMENT->value);
            })
            ->pluck(ProductCampaignBudget::FIELD_ID)
            ->toArray();

        $factory = AppointmentOffering::factory();

        $insertRows = [];
        for($i = 0; $i < 12; $i++) {
            $data = $factory->definition();

            $attempt = ($i % 4) + 1;

            $data[AppointmentOffering::FIELD_CONSUMER_PRODUCT_ID] = $consumerProductIds[$i % 3];
            $data[AppointmentOffering::FIELD_ATTEMPT] = $attempt;
            $data[AppointmentOffering::FIELD_PRODUCT_CAMPAIGN_BUDGET_ID] = $productCampaignBudgetIds[$i % 4];

            if($attempt < 4) {
                $data[AppointmentOffering::FIELD_STATUS] = AppointmentOffering::STATUS_EXPIRED;
                $data[AppointmentOffering::FIELD_START_TIME] = Carbon::now();
                $data[AppointmentOffering::FIELD_EXPIRY_TIME] = Carbon::now()->addMinutes($offeringDuration);
            }

            $insertRows[] = $data;
        }

        DB::table(AppointmentOffering::TABLE)->insert($insertRows);
    }
}
