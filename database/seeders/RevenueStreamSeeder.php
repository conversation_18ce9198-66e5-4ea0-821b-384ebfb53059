<?php

namespace Database\Seeders;

use App\Enums\Advertising\Advertiser;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Enums\Odin\Industry;
use App\Models\RevenueStream;
use Illuminate\Database\Seeder;

class RevenueStreamSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'ping_post',
            ],
            [
                RevenueStream::FIELD_NAME => 'Ping Post',
                RevenueStream::FIELD_INDUSTRY_ID => null,
                RevenueStream::FIELD_ADVERTISER => null,
                RevenueStream::FIELD_PLATFORM => null,
                RevenueStream::FIELD_CP_CASE => "ppal.id IS NOT NULL",
                RevenueStream::FIELD_DAC_CASE => null,
                RevenueStream::FIELD_PRIORITY => 5,
            ]
        );

        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'aged',
            ],
            [
                RevenueStream::FIELD_NAME => 'Aged',
                RevenueStream::FIELD_INDUSTRY_ID => null,
                RevenueStream::FIELD_ADVERTISER => null,
                RevenueStream::FIELD_PLATFORM => 'organic',
                RevenueStream::FIELD_CP_CASE => "cp.cloned_from_id IS NOT NULL AND mcc.id IS NULL",
                RevenueStream::FIELD_DAC_CASE => null,
                RevenueStream::FIELD_PRIORITY => 4,
            ]
        );

        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'email_marketing',
            ],
            [
                RevenueStream::FIELD_NAME => 'Email Marketing',
                RevenueStream::FIELD_INDUSTRY_ID => null,
                RevenueStream::FIELD_ADVERTISER => null,
                RevenueStream::FIELD_PLATFORM => 'organic',
                RevenueStream::FIELD_CP_CASE => "mcc.id IS NOT NULL",
                RevenueStream::FIELD_DAC_CASE => null,
                RevenueStream::FIELD_PRIORITY => 3,
            ]
        );

        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'google_solar',
            ],
            [
                RevenueStream::FIELD_NAME => 'Google Solar',
                RevenueStream::FIELD_INDUSTRY_ID => Industry::SOLAR->model()->id,
                RevenueStream::FIELD_ADVERTISER => Advertiser::WILL->value,
                RevenueStream::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE->value,
                RevenueStream::FIELD_CP_CASE => "cpar.track_name in ('adwords','gbraid','google_ads_ios') AND i.name = 'Solar'",
                RevenueStream::FIELD_DAC_CASE => "i.name = 'Solar' and dac.platform = 1",
                RevenueStream::FIELD_PRIORITY => 0,
            ]
        );

        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'google_roofing',
            ],
            [
                RevenueStream::FIELD_NAME => 'Google Roofing',
                RevenueStream::FIELD_INDUSTRY_ID => Industry::ROOFING->model()->id,
                RevenueStream::FIELD_ADVERTISER => Advertiser::WILL->value,
                RevenueStream::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE->value,
                RevenueStream::FIELD_CP_CASE => "cpar.track_name in ('adwords','gbraid','google_ads_ios') AND i.name = 'Roofing'",
                RevenueStream::FIELD_DAC_CASE => "i.name = 'Roofing' and dac.platform = 1",
                RevenueStream::FIELD_PRIORITY => 0,
            ]
        );

        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'google_windows',
            ],
            [
                RevenueStream::FIELD_NAME => 'Google Windows',
                RevenueStream::FIELD_INDUSTRY_ID => Industry::WINDOWS->model()->id,
                RevenueStream::FIELD_ADVERTISER => Advertiser::GABE_NEW->value,
                RevenueStream::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE->value,
                RevenueStream::FIELD_CP_CASE => "cpar.track_name in ('adwords','gbraid','google_ads_ios') AND i.name = 'Windows'",
                RevenueStream::FIELD_DAC_CASE => "i.name = 'Windows' and dac.platform = 1",
                RevenueStream::FIELD_PRIORITY => 0,
            ]
        );

        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'google_siding',
            ],
            [
                RevenueStream::FIELD_NAME => 'Google Siding',
                RevenueStream::FIELD_INDUSTRY_ID => Industry::SIDING->model()->id,
                RevenueStream::FIELD_ADVERTISER => Advertiser::GABE_NEW->value,
                RevenueStream::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE->value,
                RevenueStream::FIELD_CP_CASE => "cpar.track_name in ('adwords','gbraid','google_ads_ios') AND i.name = 'Siding'",
                RevenueStream::FIELD_DAC_CASE => "i.name = 'Siding' and dac.platform = 1",
                RevenueStream::FIELD_PRIORITY => 0,
            ]
        );

        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'google_bathrooms',
            ],
            [
                RevenueStream::FIELD_NAME => 'Google Bathrooms',
                RevenueStream::FIELD_INDUSTRY_ID => Industry::BATHROOMS->model()->id,
                RevenueStream::FIELD_ADVERTISER => Advertiser::GABE_NEW->value,
                RevenueStream::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE->value,
                RevenueStream::FIELD_CP_CASE => "cpar.track_name in ('adwords','gbraid','google_ads_ios') AND i.name = 'Bathrooms'",
                RevenueStream::FIELD_DAC_CASE => "i.name = 'Bathrooms' and dac.platform = 1",
                RevenueStream::FIELD_PRIORITY => 0,
            ]
        );

        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'google_kitchens',
            ],
            [
                RevenueStream::FIELD_NAME => 'Google Kitchens',
                RevenueStream::FIELD_INDUSTRY_ID => Industry::KITCHENS->model()->id,
                RevenueStream::FIELD_ADVERTISER => Advertiser::GABE_NEW->value,
                RevenueStream::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE->value,
                RevenueStream::FIELD_CP_CASE => "cpar.track_name in ('adwords','gbraid','google_ads_ios') AND i.name = 'Kitchens'",
                RevenueStream::FIELD_DAC_CASE => "i.name = 'Kitchens' and dac.platform = 1",
                RevenueStream::FIELD_PRIORITY => 0,
            ]
        );

        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'meta_solar_main',
            ],
            [
                RevenueStream::FIELD_NAME => 'Meta Solar Main',
                RevenueStream::FIELD_INDUSTRY_ID => Industry::SOLAR->model()->id,
                RevenueStream::FIELD_ADVERTISER => Advertiser::WILL->value,
                RevenueStream::FIELD_PLATFORM => AdvertisingPlatform::META->value,
                RevenueStream::FIELD_CP_CASE => "cpar.track_name = 'facebook' and (cpar.affiliate_id = 209744 or cpar.affiliate_id = 4713) AND i.name = 'Solar'",
                RevenueStream::FIELD_DAC_CASE => "i.name = 'Solar' and dac.platform = 2 and (dac.advertiser = 1 or dac.advertiser = 5)",
                RevenueStream::FIELD_PRIORITY => 0,
            ]
        );
        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'meta_roofing_main',
            ],
            [
                RevenueStream::FIELD_NAME => 'Meta Roofing Main',
                RevenueStream::FIELD_INDUSTRY_ID => Industry::ROOFING->model()->id,
                RevenueStream::FIELD_ADVERTISER => Advertiser::WILL->value,
                RevenueStream::FIELD_PLATFORM => AdvertisingPlatform::META->value,
                RevenueStream::FIELD_CP_CASE => "cpar.track_name = 'facebook' and cpar.affiliate_id = 209744 AND i.name = 'Roofing'",
                RevenueStream::FIELD_DAC_CASE => "i.name = 'Roofing' and dac.platform = 2 and (dac.advertiser = 1 or dac.advertiser = 5)",
                RevenueStream::FIELD_PRIORITY => 0,
            ]
        );
        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'meta_solar_wade',
            ],
            [
                RevenueStream::FIELD_NAME => 'Meta Solar Wade',
                RevenueStream::FIELD_INDUSTRY_ID => Industry::SOLAR->model()->id,
                RevenueStream::FIELD_ADVERTISER => Advertiser::WADE->value,
                RevenueStream::FIELD_PLATFORM => AdvertisingPlatform::META->value,
                RevenueStream::FIELD_CP_CASE => "cpar.track_name = 'facebook' and cpar.affiliate_id = 8910 AND i.name = 'Solar'",
                RevenueStream::FIELD_DAC_CASE => "i.name = 'Solar' and dac.platform = 2 and dac.advertiser = 2",
                RevenueStream::FIELD_PRIORITY => 0,
            ]
        );

        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'meta_roofing_wade',
            ],
            [
                RevenueStream::FIELD_NAME => 'Meta Roofing Wade',
                RevenueStream::FIELD_INDUSTRY_ID => Industry::ROOFING->model()->id,
                RevenueStream::FIELD_ADVERTISER => Advertiser::WADE->value,
                RevenueStream::FIELD_PLATFORM => AdvertisingPlatform::META->value,
                RevenueStream::FIELD_CP_CASE => "cpar.track_name = 'facebook' and cpar.affiliate_id = 8910 AND i.name = 'Roofing'",
                RevenueStream::FIELD_DAC_CASE => "i.name = 'Roofing' and dac.platform = 2 and dac.advertiser = 2",
                RevenueStream::FIELD_PRIORITY => 0,
            ]
        );
        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'meta_siding_wade',
            ],
            [
                RevenueStream::FIELD_NAME => 'Meta Siding Wade',
                RevenueStream::FIELD_INDUSTRY_ID => Industry::SIDING->model()->id,
                RevenueStream::FIELD_ADVERTISER => Advertiser::WADE->value,
                RevenueStream::FIELD_PLATFORM => AdvertisingPlatform::META->value,
                RevenueStream::FIELD_CP_CASE => "cpar.track_name = 'facebook' and cpar.affiliate_id = 8910 AND i.name = 'Siding'",
                RevenueStream::FIELD_DAC_CASE => "i.name = 'Siding' and dac.platform = 2 and dac.advertiser = 2",
                RevenueStream::FIELD_PRIORITY => 0,
            ]
        );

        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'meta_solar_thomas',
            ],
            [
                RevenueStream::FIELD_NAME => 'Meta Solar Thomas',
                RevenueStream::FIELD_INDUSTRY_ID => Industry::SOLAR->model()->id,
                RevenueStream::FIELD_ADVERTISER => Advertiser::THOMAS->value,
                RevenueStream::FIELD_PLATFORM => AdvertisingPlatform::META->value,
                RevenueStream::FIELD_CP_CASE => "cpar.track_name = 'facebook' and cpar.affiliate_id = 209732 AND i.name = 'Solar'",
                RevenueStream::FIELD_DAC_CASE => "i.name = 'Solar' and dac.platform = 2 and dac.advertiser = 3",
                RevenueStream::FIELD_PRIORITY => 0,
            ]
        );

        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'meta_roofing_thomas',
            ],
            [
                RevenueStream::FIELD_NAME => 'Meta Roofing Thomas',
                RevenueStream::FIELD_INDUSTRY_ID => Industry::ROOFING->model()->id,
                RevenueStream::FIELD_ADVERTISER => Advertiser::THOMAS->value,
                RevenueStream::FIELD_PLATFORM => AdvertisingPlatform::META->value,
                RevenueStream::FIELD_CP_CASE => "cpar.track_name = 'facebook' and cpar.affiliate_id = 209732 AND i.name = 'Roofing'",
                RevenueStream::FIELD_DAC_CASE => "i.name = 'Roofing' and dac.platform = 2 and dac.advertiser = 3",
                RevenueStream::FIELD_PRIORITY => 0,
            ]
        );


        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'microsoft_solar',
            ],
            [
                RevenueStream::FIELD_NAME => 'Microsoft Solar',
                RevenueStream::FIELD_INDUSTRY_ID => Industry::SOLAR->model()->id,
                RevenueStream::FIELD_ADVERTISER => Advertiser::GABE_NEW->value,
                RevenueStream::FIELD_PLATFORM => AdvertisingPlatform::MICROSOFT->value,
                RevenueStream::FIELD_CP_CASE => "cpar.track_name = 'bing_ads' AND i.name = 'Solar'",
                RevenueStream::FIELD_DAC_CASE => "i.name = 'Solar' and dac.platform = 3",
                RevenueStream::FIELD_PRIORITY => 0,
            ]
        );


        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'microsoft_roofing',
            ],
            [
                RevenueStream::FIELD_NAME => 'Microsoft Roofing',
                RevenueStream::FIELD_INDUSTRY_ID => Industry::ROOFING->model()->id,
                RevenueStream::FIELD_ADVERTISER => Advertiser::GABE_NEW->value,
                RevenueStream::FIELD_PLATFORM => AdvertisingPlatform::MICROSOFT->value,
                RevenueStream::FIELD_CP_CASE => "cpar.track_name = 'bing_ads' AND i.name = 'Roofing'",
                RevenueStream::FIELD_DAC_CASE => "i.name = 'Roofing' and dac.platform = 3",
                RevenueStream::FIELD_PRIORITY => 0,
            ]
        );

        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'microsoft_siding',
            ],
            [
                RevenueStream::FIELD_NAME => 'Microsoft Siding',
                RevenueStream::FIELD_INDUSTRY_ID => Industry::SIDING->model()->id,
                RevenueStream::FIELD_ADVERTISER => Advertiser::GABE_NEW->value,
                RevenueStream::FIELD_PLATFORM => AdvertisingPlatform::MICROSOFT->value,
                RevenueStream::FIELD_CP_CASE => "cpar.track_name = 'bing_ads' AND i.name = 'Siding'",
                RevenueStream::FIELD_DAC_CASE => "i.name = 'Siding' and dac.platform = 3",
                RevenueStream::FIELD_PRIORITY => 0,
            ]
        );

        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'microsoft_bathrooms',
            ],
            [
                RevenueStream::FIELD_NAME => 'Microsoft Bathrooms',
                RevenueStream::FIELD_INDUSTRY_ID => Industry::BATHROOMS->model()->id,
                RevenueStream::FIELD_ADVERTISER => Advertiser::GABE_NEW->value,
                RevenueStream::FIELD_PLATFORM => AdvertisingPlatform::MICROSOFT->value,
                RevenueStream::FIELD_CP_CASE => "cpar.track_name = 'bing_ads' AND i.name = 'Bathrooms'",
                RevenueStream::FIELD_DAC_CASE => "i.name = 'Bathrooms' and dac.platform = 3",
                RevenueStream::FIELD_PRIORITY => 0,
            ]
        );

        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'meta_siding_main',
            ],
            [
                RevenueStream::FIELD_NAME => 'Meta Siding Main',
                RevenueStream::FIELD_INDUSTRY_ID => Industry::SIDING->model()->id,
                RevenueStream::FIELD_ADVERTISER => Advertiser::GABE_NEW->value,
                RevenueStream::FIELD_PLATFORM => AdvertisingPlatform::META->value,
                RevenueStream::FIELD_CP_CASE => "cpar.track_name = 'facebook' AND i.name = 'Siding'",
                RevenueStream::FIELD_DAC_CASE => "i.name = 'Siding' and dac.platform = 2",
                RevenueStream::FIELD_PRIORITY => -1,
            ]
        );

        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'meta_windows_main',
            ],
            [
                RevenueStream::FIELD_NAME => 'Meta Windows Main',
                RevenueStream::FIELD_INDUSTRY_ID => Industry::WINDOWS->model()->id,
                RevenueStream::FIELD_ADVERTISER => Advertiser::GABE_NEW->value,
                RevenueStream::FIELD_PLATFORM => AdvertisingPlatform::META->value,
                RevenueStream::FIELD_CP_CASE => "cpar.track_name = 'facebook' AND i.name = 'Windows'",
                RevenueStream::FIELD_DAC_CASE => "i.name = 'Windows' and dac.platform = 2",
                RevenueStream::FIELD_PRIORITY => -1,
            ]
        );

        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'meta_bathrooms_main',
            ],
            [
                RevenueStream::FIELD_NAME => 'Meta Bathrooms Main',
                RevenueStream::FIELD_INDUSTRY_ID => Industry::BATHROOMS->model()->id,
                RevenueStream::FIELD_ADVERTISER => Advertiser::GABE_NEW->value,
                RevenueStream::FIELD_PLATFORM => AdvertisingPlatform::META->value,
                RevenueStream::FIELD_CP_CASE => "cpar.track_name = 'facebook' AND i.name = 'Bathrooms'",
                RevenueStream::FIELD_DAC_CASE => "i.name = 'Bathrooms' and dac.platform = 2",
                RevenueStream::FIELD_PRIORITY => -1,
            ]
        );

        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'nextdoor',
            ],
            [
                RevenueStream::FIELD_NAME => 'Nextdoor',
                RevenueStream::FIELD_INDUSTRY_ID => null,
                RevenueStream::FIELD_ADVERTISER => Advertiser::GABE_NEW->value,
                RevenueStream::FIELD_PLATFORM => null,
                RevenueStream::FIELD_CP_CASE => "cpt.url_start LIKE '%nextdoor%'",
                RevenueStream::FIELD_DAC_CASE => null,
                RevenueStream::FIELD_PRIORITY => -2,
            ]
        );

        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'quora',
            ],
            [
                RevenueStream::FIELD_NAME => 'Quora',
                RevenueStream::FIELD_INDUSTRY_ID => null,
                RevenueStream::FIELD_ADVERTISER => Advertiser::GABE_NEW->value,
                RevenueStream::FIELD_PLATFORM => null,
                RevenueStream::FIELD_CP_CASE => "cpt.url_start LIKE '%quora%'",
                RevenueStream::FIELD_DAC_CASE => null,
                RevenueStream::FIELD_PRIORITY => -2,
            ]
        );

        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'affiliate',
            ],
            [
                RevenueStream::FIELD_NAME => 'Affiliate',
                RevenueStream::FIELD_INDUSTRY_ID => null,
                RevenueStream::FIELD_ADVERTISER => null,
                RevenueStream::FIELD_PLATFORM => null,
                RevenueStream::FIELD_CP_CASE => "cpar.track_code is null and cpar.track_name is null and cpar.affiliate_id is not null",
                RevenueStream::FIELD_DAC_CASE => null,
                RevenueStream::FIELD_PRIORITY => -3,
            ]
        );

        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'organic_solar_estimate',
            ],
            [
                RevenueStream::FIELD_NAME => 'Organic Solar Estimate',
                RevenueStream::FIELD_INDUSTRY_ID => Industry::SOLAR->model()->id,
                RevenueStream::FIELD_ADVERTISER => null,
                RevenueStream::FIELD_PLATFORM => 'organic',
                RevenueStream::FIELD_CP_CASE => "cpar.track_code is null and cpar.track_name is null and cpar.affiliate_id is null and w.abbreviation = 'se'",
                RevenueStream::FIELD_DAC_CASE => null,
                RevenueStream::FIELD_PRIORITY => -3,
            ]
        );

        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'organic_solar_reviews',
            ],
            [
                RevenueStream::FIELD_NAME => 'Organic Solar Reviews',
                RevenueStream::FIELD_INDUSTRY_ID => Industry::SOLAR->model()->id,
                RevenueStream::FIELD_ADVERTISER => null,
                RevenueStream::FIELD_PLATFORM => 'organic',
                RevenueStream::FIELD_CP_CASE => "cpar.track_code is null and cpar.track_name is null and cpar.affiliate_id is null and w.abbreviation = 'sr'",
                RevenueStream::FIELD_DAC_CASE => null,
                RevenueStream::FIELD_PRIORITY => -3,
            ]
        );

        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'organic_roofing_calculator',
            ],
            [
                RevenueStream::FIELD_NAME => 'Organic Roofing Calculator',
                RevenueStream::FIELD_INDUSTRY_ID => Industry::ROOFING->model()->id,
                RevenueStream::FIELD_ADVERTISER => null,
                RevenueStream::FIELD_PLATFORM => 'organic',
                RevenueStream::FIELD_CP_CASE => "cpar.track_code is null and cpar.track_name is null and cpar.affiliate_id is null and w.abbreviation = 'rc'",
                RevenueStream::FIELD_DAC_CASE => null,
                RevenueStream::FIELD_PRIORITY => -3,
            ]
        );

        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'organic_kitchen_estimate',
            ],
            [
                RevenueStream::FIELD_NAME => 'Organic Kitchen Estimate',
                RevenueStream::FIELD_INDUSTRY_ID => Industry::KITCHENS->model()->id,
                RevenueStream::FIELD_ADVERTISER => null,
                RevenueStream::FIELD_PLATFORM => 'organic',
                RevenueStream::FIELD_CP_CASE => "cpar.track_code is null and cpar.track_name is null and cpar.affiliate_id is null and w.abbreviation = 'ke'",
                RevenueStream::FIELD_DAC_CASE => null,
                RevenueStream::FIELD_PRIORITY => -3,
            ]
        );

        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'organic_bathroom_estimate',
            ],
            [
                RevenueStream::FIELD_NAME => 'Organic Bathroom Estimate',
                RevenueStream::FIELD_INDUSTRY_ID => Industry::BATHROOMS->model()->id,
                RevenueStream::FIELD_ADVERTISER => null,
                RevenueStream::FIELD_PLATFORM => 'organic',
                RevenueStream::FIELD_CP_CASE => "cpar.track_code is null and cpar.track_name is null and cpar.affiliate_id is null and w.abbreviation = 'be'",
                RevenueStream::FIELD_DAC_CASE => null,
                RevenueStream::FIELD_PRIORITY => -3,
            ]
        );

        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'organic_my_solar',
            ],
            [
                RevenueStream::FIELD_NAME => 'Organic My Solar',
                RevenueStream::FIELD_INDUSTRY_ID => Industry::SOLAR->model()->id,
                RevenueStream::FIELD_ADVERTISER => null,
                RevenueStream::FIELD_PLATFORM => 'organic',
                RevenueStream::FIELD_CP_CASE => "cpar.track_code is null and cpar.track_name is null and cpar.affiliate_id is null and w.abbreviation = 'ws'",
                RevenueStream::FIELD_DAC_CASE => null,
                RevenueStream::FIELD_PRIORITY => -3,
            ]
        );

        RevenueStream::updateOrCreate(
            [
                RevenueStream::FIELD_SLUG => 'organic_fixr',
            ],
            [
                RevenueStream::FIELD_NAME => 'Organic Fixr',
                RevenueStream::FIELD_INDUSTRY_ID => null,
                RevenueStream::FIELD_ADVERTISER => null,
                RevenueStream::FIELD_PLATFORM => 'organic',
                RevenueStream::FIELD_CP_CASE => "cpar.track_code is null and cpar.track_name is null and cpar.affiliate_id is null and w.abbreviation = 'fixr'",
                RevenueStream::FIELD_DAC_CASE => null,
                RevenueStream::FIELD_PRIORITY => -3,
            ]
        );
    }
}
