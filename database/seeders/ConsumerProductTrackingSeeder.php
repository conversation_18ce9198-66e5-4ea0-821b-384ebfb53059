<?php

namespace Database\Seeders;

use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProductTracking;
use App\Models\Odin\Website;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ConsumerProductTrackingSeeder extends Seeder
{
    use WithoutModelEvents;

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $factory = ConsumerProductTracking::factory();

        $websites = Website::query()
            ->select([
                Website::FIELD_ID,
                Website::FIELD_URL
            ])
            ->get()
            ->toArray();

        $websitesCount = count($websites);

        $recordsCount = Consumer::query()->count();

        $insertRows = [];
        for($i = 0; $i < $recordsCount; $i++) {
            $data = $factory->definition();

            $websiteIdx = $i % $websitesCount;

            $data[ConsumerProductTracking::WEBSITE_ID] = $websites[$websiteIdx][Website::FIELD_ID];
            $data[ConsumerProductTracking::URL_START] = $websites[$websiteIdx][Website::FIELD_URL];
            $data[ConsumerProductTracking::URL_CONVERT] = $websites[$websiteIdx][Website::FIELD_URL]."/convert";

            $insertRows[] = $data;

            if(count($insertRows) >= 500) {
                DB::table(ConsumerProductTracking::TABLE)->insert($insertRows);
                $insertRows = [];
            }
        }

        if(!empty($insertRows)) {
            DB::table(ConsumerProductTracking::TABLE)->insert($insertRows);
        }
    }
}
