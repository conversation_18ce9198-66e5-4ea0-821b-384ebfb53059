<?php

namespace Database\Seeders;

use App\Enums\AffiliateKey;
use App\Http\Requests\PingPostAffiliatesCreateLeadRequest;
use App\Models\PingPostAffiliates\PingPostAffiliate;
use App\Models\PingPostAffiliates\PingPostAffiliateApiKey;
use App\Services\Odin\PingPostAffiliateService;
use Illuminate\Database\Seeder;

class PingPostAffiliateSolarSurgeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        // Verify ping post affiliate Solar Surge does not already exist to prevent duplicate creation
        $solarSurgeSearch = PingPostAffiliate::query()->where(PingPostAffiliate::FIELD_NAME, 'Solar Surge')->get();
        if ($solarSurgeSearch->count() > 0) {
            print "Solar Surge Ping Post Affiliate Already exists.";
            return;
        }

        $rules = [
            AffiliateKey::FIRST_NAME->value         => ['required', 'string'],
            AffiliateKey::LAST_NAME->value          => ['required', 'string'],
            AffiliateKey::EMAIL->value              => ['required', 'string'],
            AffiliateKey::PHONE->value              => ['required', 'string'],
            AffiliateKey::CREATION_DATE->value      => ['required', 'string'],
            AffiliateKey::ORIGIN_URL->value         => ['nullable', 'string'],
            AffiliateKey::TRUSTED_FORM_URL->value   => ['required', 'string'],
            AffiliateKey::ADDRESS_1->value          => ['required', 'string'],
            AffiliateKey::ADDRESS_2->value          => ['nullable', 'string'],
            AffiliateKey::CITY->value               => ['required', 'string'],
            AffiliateKey::STATE->value              => ['required', 'string'],
            AffiliateKey::ZIP_CODE->value           => ['required', 'string'],
            AffiliateKey::COUNTRY->value            => ['nullable', 'string'],
            AffiliateKey::UTILITY->value            => ['nullable', 'string'],
            AffiliateKey::ELECTRIC_BILL->value      => ['nullable', 'numeric'],
            AffiliateKey::IP_ADDRESS->value         => ['nullable', 'string'],
            AffiliateKey::ROOF_TYPE->value          => ['nullable', 'string'],
            AffiliateKey::AFFILIATE_CAMPAIGN_ID->value => ['nullable', 'numeric'],
        ];

        $solarSurgeDefaultKeyValues = [
            AffiliateKey::INDUSTRY->value => 'solar',
            AffiliateKey::COUNTRY->value => 'US',
            AffiliateKey::OWN_PROPERTY->value => 'yes',
        ];

        // Create affiliate entry
        $solarSurgeAffiliate = PingPostAffiliate::query()->create([
            PingPostAffiliate::FIELD_NAME => 'Solar Surge',
            PingPostAffiliate::FIELD_TYPE => PingPostAffiliate::TYPE_POST,
            PingPostAffiliate::FIELD_STATUS => PingPostAffiliate::STATUS_ACTIVE,
            PingPostAffiliate::FIELD_REQUEST_RULES => $rules,
            PingPostAffiliate::FIELD_KEY_MAP => AffiliateKey::getDefaultKeyMap(),
            PingPostAffiliate::FIELD_DEFAULT_KEY_VALUES => $solarSurgeDefaultKeyValues,
            PingPostAffiliate::FIELD_KEY_VALUE_CONVERSIONS => [],
            PingPostAffiliate::FIELD_DATA => [],
            PingPostAffiliate::FIELD_AFFILIATE_ID => 67819,
            PingPostAffiliate::FIELD_CREATED_AT => now(),
            PingPostAffiliate::FIELD_UPDATED_AT => now(),
        ]);

        // Create API Key
        $apiKey = PingPostAffiliateApiKey::query()->create([
            PingPostAffiliateApiKey::FIELD_PING_POST_AFFILIATE_ID => $solarSurgeAffiliate->id,
            PingPostAffiliateApiKey::FIELD_KEY => 'r5U9nGp2Wq8tF6yJz1L0mBv3K4hE7XjNzV0sP9K2cH5R1A8TqS',
            PingPostAffiliateApiKey::FIELD_TYPE => PingPostAffiliateApiKey::TYPE_POST,
            PingPostAffiliateApiKey::FIELD_STATUS => PingPostAffiliateApiKey::STATUS_ACTIVE,
            PingPostAffiliateApiKey::FIELD_CREATED_AT => now(),
            PingPostAffiliateApiKey::FIELD_UPDATED_AT => now(),
        ]);
    }
}
