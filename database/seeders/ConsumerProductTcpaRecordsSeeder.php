<?php

namespace Database\Seeders;

use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProductTcpaRecord;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ConsumerProductTcpaRecordsSeeder extends Seeder
{
    use WithoutModelEvents;

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $factory = ConsumerProductTcpaRecord::factory();

        $recordsCount = Consumer::query()->count();

        $insertRows = [];
        for($i = 0; $i < $recordsCount; $i++) {
            $insertRows[] = $factory->definition();

            if(count($insertRows) >= 500) {
                DB::table(ConsumerProductTcpaRecord::TABLE)->insert($insertRows);
                $insertRows = [];
            }
        }

        if(!empty($insertRows)) {
            DB::table(ConsumerProductTcpaRecord::TABLE)->insert($insertRows);
        }
    }
}
