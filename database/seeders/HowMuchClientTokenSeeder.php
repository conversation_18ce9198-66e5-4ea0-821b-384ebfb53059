<?php

namespace Database\Seeders;

use App\Models\ClientToken;
use App\Models\ClientTokenService;
use Illuminate\Database\Seeder;
use Random\RandomException;

class HowMuchClientTokenSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     * @throws RandomException
     */
    public function run(): void
    {
        $tokenService = ClientTokenService::query()
            ->updateOrCreate([
                ClientTokenService::FIELD_SERVICE_KEY  => ClientTokenService::HOW_MUCH,
                ClientTokenService::FIELD_SERVICE_NAME => 'How Much',
            ]);

        if (!$tokenService->refresh()->clientToken) {
            $tokenService->clientToken()->create([
                ClientToken::FIELD_CLIENT_TOKEN => bin2hex(random_bytes(64)),
            ]);
        }
    }
}
