<?php

namespace Database\Seeders;

use App\Models\TimeframeContactBuffers;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TimeframeContactBuffersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table(TimeframeContactBuffers::TABLE)->insert([
            [
                TimeframeContactBuffers::FIELD_TIMEFRAME_ID => 1,
                TimeframeContactBuffers::FIELD_CONTACT_ATTEMPT_BUFFER_HRS => 8
            ],
            [
                TimeframeContactBuffers::FIELD_TIMEFRAME_ID => 2,
                TimeframeContactBuffers::FIELD_CONTACT_ATTEMPT_BUFFER_HRS => 8
            ],
            [
                TimeframeContactBuffers::FIELD_TIMEFRAME_ID => 3,
                TimeframeContactBuffers::FIELD_CONTACT_ATTEMPT_BUFFER_HRS => 24
            ],
            [
                TimeframeContactBuffers::FIELD_TIMEFRAME_ID => 4,
                TimeframeContactBuffers::FIELD_CONTACT_ATTEMPT_BUFFER_HRS => 168
            ],
            [
                TimeframeContactBuffers::FIELD_TIMEFRAME_ID => 5,
                TimeframeContactBuffers::FIELD_CONTACT_ATTEMPT_BUFFER_HRS => 336
            ]
        ]);
    }
}
