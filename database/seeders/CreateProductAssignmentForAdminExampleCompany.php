<?php

namespace Database\Seeders;

use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Seeder;

class CreateProductAssignmentForAdminExampleCompany extends Seeder
{
    use WithoutModelEvents;

    protected Company $company;

    protected Product $product;

    protected Industry $industry;

    protected IndustryService $industryService;

    protected ConsumerProduct $consumerProduct;

    protected ServiceProduct $serviceProduct;

    protected EloquentQuoteCompany $legacyProductAssignment;

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->company = CompanyUser::query()->where('email', '<EMAIL>')->first()->company;

        $this->industryService = IndustryService::query()->where('slug', 'roof-replacement')->first();

        $this->serviceProduct = ServiceProduct::query()->where('industry_service_id', $this->industryService->id)->first();

        $this->consumerProduct = ConsumerProduct::factory()->createQuietly([
            'service_product_id' => $this->serviceProduct->id,
        ]);

        $this->legacyProductAssignment = app(EloquentQuoteCompany::class);

        $this->legacyProductAssignment->soldstatus = EloquentQuoteCompany::VALUE_LEAD_COST_TYPE_PREMIUM;

        $this->legacyProductAssignment->save();

        ProductAssignment::factory()->createQuietly([
            'company_id'          => $this->company->id,
            'delivered'           => true,
            'legacy_id'           => $this->legacyProductAssignment->quotecompanyid,
            'consumer_product_id' => $this->consumerProduct->id,
            'delivered_at'        => now()->subDay(),
        ]);
    }
}
