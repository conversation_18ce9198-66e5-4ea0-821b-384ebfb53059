<?php

namespace Database\Seeders;

use App\Models\WorkflowEvent;
use App\Repositories\PubSubEventRepository;
use Illuminate\Database\Seeder;

class WorkflowEventsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /** @var PubSubEventRepository $pubSubEventRepository */
        $pubSubEventRepository = app(PubSubEventRepository::class);

        $allEvents = $pubSubEventRepository->getAllEvents();

        $insertRows = [];
        foreach($allEvents as $category => $categoryEvents) {
            foreach($categoryEvents as $categoryEvent) {
                $insertRows[] = [
                    WorkflowEvent::FIELD_EVENT_CATEGORY => $category,
                    WorkflowEvent::FIELD_EVENT_NAME => $categoryEvent
                ];

                if(count($insertRows) >= 250) {
                    WorkflowEvent::query()->insert($insertRows);

                    $insertRows = [];
                }
            }
        }

        if(!empty($insertRows)) {
            WorkflowEvent::query()->insert($insertRows);
        }
    }
}
