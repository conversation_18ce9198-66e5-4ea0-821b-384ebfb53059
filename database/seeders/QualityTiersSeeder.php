<?php

namespace Database\Seeders;

use App\Enums\Odin\QualityTier as QualityTierEnum;
use App\Models\Odin\QualityTier;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class QualityTiersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $insertRows = [];
        foreach(QualityTierEnum::cases() as $case) {
            $insertRows[] = [
                QualityTier::FIELD_NAME => $case->value
            ];
        }

        DB::table(QualityTier::TABLE)->insert($insertRows);
    }
}
