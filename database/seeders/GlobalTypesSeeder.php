<?php

namespace Database\Seeders;

use App\Models\Odin\GlobalType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class GlobalTypesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table(GlobalType::TABLE)->insert([
            [
                GlobalType::FIELD_NAME => 'Installer',
                GlobalType::FIELD_KEY => 'installer'
            ]
        ]);
    }
}
