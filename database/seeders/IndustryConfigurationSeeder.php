<?php

namespace Database\Seeders;

use App\Models\Odin\Industry as IndustryModel;
use App\Models\Odin\IndustryConfiguration;
use Illuminate\Database\Seeder;
use Illuminate\Support\Collection;

class IndustryConfigurationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /** @var Collection<IndustryModel> $industries */
        $industries = IndustryModel::query()->get();

        $insertRows = [];
        $now = now();

        foreach($industries as $industry) {
            $insertRows[] = [
                IndustryConfiguration::FIELD_INDUSTRY_ID => $industry->id,
                IndustryConfiguration::FIELD_FUTURE_CAMPAIGNS_ACTIVE => false,
                IndustryConfiguration::CREATED_AT => $now,
                IndustryConfiguration::UPDATED_AT => $now
            ];
        }

        IndustryConfiguration::query()->insert($insertRows);
    }
}
