<?php

namespace Database\Seeders;

use App\Enums\PermissionType;
use App\Enums\RoleType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionConsumerProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        Permission::findOrCreate(PermissionType::CONSUMER_PRODUCT_VIEW->value);
        Permission::findOrCreate(PermissionType::CONSUMER_PRODUCT_VIEW_LONGER_THAN_90_DAYS->value);

        $admin = Role::findOrCreate(RoleType::ADMIN->value);

        $admin->givePermissionTo([
            PermissionType::CONSUMER_PRODUCT_VIEW->value,
            PermissionType::CONSUMER_PRODUCT_VIEW_LONGER_THAN_90_DAYS->value,
        ]);
    }
}
