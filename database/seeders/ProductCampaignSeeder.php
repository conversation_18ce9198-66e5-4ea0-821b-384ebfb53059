<?php

namespace Database\Seeders;

use App\Models\Odin\Company;
use App\Models\Odin\Product;
use App\Models\Odin\ProductCampaign;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProductCampaignSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $companyIds = Company::query()->limit(4)->orderBy(Company::FIELD_ID, 'asc')->pluck(Company::FIELD_ID)->toArray();
        $productIds = Product::query()->limit(2)->orderBy(Product::FIELD_ID, 'asc')->pluck(Product::FIELD_ID)->toArray();

        $companyIds[] = 1517;

        $factory = ProductCampaign::factory();

        $insertRows = [];
        foreach($companyIds as $companyId) {
            foreach($productIds as $productId) {
                $data = $factory->definition();

                $data[ProductCampaign::FIELD_COMPANY_ID] = $companyId;
                $data[ProductCampaign::FIELD_PRODUCT_ID] = $productId;

                $insertRows[] = $data;
            }
        }

        DB::table(ProductCampaign::TABLE)->insert($insertRows);
    }
}
