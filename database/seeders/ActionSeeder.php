<?php

namespace Database\Seeders;

use App\Models\Action;
use App\Models\ActionCategory;
use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Database\Seeder;

class ActionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $company = Company::first();

        if (ActionCategory::count() === 0) {
            $this->call(ActionCategorySeeder::class);
        }

        Action::factory(20)->create([
            'from_user_id' => User::get()->random()->id,
            'for_id' => $company->id,
            'for_relation_type' => Action::RELATION_TYPE_COMPANY,
        ]);
    }
}
