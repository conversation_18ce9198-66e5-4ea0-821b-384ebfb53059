<?php

namespace Database\Seeders;

use App\Enums\Odin\PropertyType as PropertyTypeEnum;
use App\Enums\Odin\QualityTier as QualityTierEnum;
use App\Models\Odin\Product as ProductModel;
use App\Enums\Odin\Product as ProductEnum;
use App\Models\Legacy\Location;
use App\Models\Odin\ProductStateFloorPrice;
use App\Models\Odin\PropertyType;
use App\Models\Odin\QualityTier;
use App\Models\Odin\ServiceProduct;
use App\Models\SaleType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProductStateFloorPricesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $stateLocationIds = Location::query()
            ->where(Location::TYPE, Location::TYPE_STATE)
            ->pluck(Location::ID)
            ->toArray();

        $saleTypeIds = SaleType::query()->pluck(SaleType::FIELD_ID)->toArray();

        $residentialPropertyTypeId = PropertyType::query()->where(PropertyType::FIELD_NAME, PropertyTypeEnum::RESIDENTIAL->value)->first()->{PropertyType::FIELD_ID};

        $leadQualityTierIds = QualityTier::query()
            ->whereIn(QualityTier::FIELD_NAME, [QualityTierEnum::STANDARD->value, QualityTierEnum::PREMIUM->value])
            ->pluck(QualityTier::FIELD_ID)
            ->toArray();

        $appointmentQualityTierIds = QualityTier::query()
            ->whereIn(QualityTier::FIELD_NAME, [QualityTierEnum::IN_HOME->value, QualityTierEnum::ONLINE->value])
            ->pluck(QualityTier::FIELD_ID)
            ->toArray();

        $productQualityTiers = [
            ProductEnum::APPOINTMENT->value => $appointmentQualityTierIds,
            ProductEnum::LEAD->value => $leadQualityTierIds
        ];

        $serviceProducts = ServiceProduct::query()
            ->join(ProductModel::TABLE, function($join) {
                $join->on(ProductModel::TABLE.'.'.ProductModel::FIELD_ID, '=', ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_PRODUCT_ID);
            })
            ->select([
                ProductModel::TABLE.'.'.ProductModel::FIELD_NAME,
                ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_ID
            ])
            ->get()
            ->mapToGroups(function($row) {
                return [$row[ProductModel::FIELD_NAME] => $row[ServiceProduct::FIELD_ID]];
            })
            ->toArray();

        $serviceQualityTiers = [];
        foreach($serviceProducts as $product => $serviceProductIds) {
            foreach($serviceProductIds as $serviceProductId) {
                $serviceQualityTiers[$serviceProductId] = $productQualityTiers[$product];
            }
        }

        $insertRows = [];
        foreach($stateLocationIds as $stateLocationId) {
            foreach($saleTypeIds as $saleTypeId) {
                foreach($serviceQualityTiers as $serviceProductId => $qualityTiers) {
                    foreach($qualityTiers as $qualityTierId) {
                        $insertRows[] = [
                            ProductStateFloorPrice::FIELD_STATE_LOCATION_ID  => $stateLocationId,
                            ProductStateFloorPrice::FIELD_SALE_TYPE_ID       => $saleTypeId,
                            ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID   => $residentialPropertyTypeId,
                            ProductStateFloorPrice::FIELD_QUALITY_TIER_ID    => $qualityTierId,
                            ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID => $serviceProductId,
                            ProductStateFloorPrice::FIELD_PRICE              => 100
                        ];

                        if(count($insertRows) >= 500) {
                            DB::table(ProductStateFloorPrice::TABLE)->insert($insertRows);

                            $insertRows = [];
                        }
                    }
                }
            }
        }

        if(!empty($insertRows)) {
            DB::table(ProductStateFloorPrice::TABLE)->insert($insertRows);
        }
    }
}
