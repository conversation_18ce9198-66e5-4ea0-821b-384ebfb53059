<?php

namespace Database\Seeders;

use App\Enums\AffiliateKey;
use App\Models\PingPostAffiliates\PingPostAffiliate;
use App\Models\PingPostAffiliates\PingPostAffiliateApiKey;
use App\Services\Odin\PingPostAffiliateService;
use Illuminate\Database\Seeder;

class PingPostAffiliateCEESeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        // Verify ping post affiliate CEE does not already exist to prevent duplicate creation
        $pxSearch = PingPostAffiliate::query()->where(PingPostAffiliate::FIELD_NAME, 'Clean Energy Experts')->get();
        if ($pxSearch->count() > 0) {
            print "Clean Energy Experts Ping Post Affiliate Already exists.";
            return;
        }

        $ceeRules = [
            AffiliateKey::FIRST_NAME->value         => ['required', 'string'],
            AffiliateKey::LAST_NAME->value          => ['required', 'string'],
            AffiliateKey::EMAIL->value              => ['required', 'string'],
            AffiliateKey::PHONE->value              => ['required', 'string'],
            AffiliateKey::INDUSTRY->value           => ['nullable', 'string'],
            AffiliateKey::CREATION_DATE->value      => ['required', 'string'],
            AffiliateKey::ORIGIN_URL->value         => ['nullable', 'string'],
            AffiliateKey::TRUSTED_FORM_URL->value   => ['nullable', 'string'],
            AffiliateKey::COST->value               => ['nullable', 'numeric'],
            AffiliateKey::ADDRESS_1->value          => ['required', 'string'],
            AffiliateKey::ADDRESS_2->value          => ['nullable', 'string'],
            AffiliateKey::CITY->value               => ['required', 'string'],
            AffiliateKey::STATE->value              => ['required', 'string'],
            AffiliateKey::ZIP_CODE->value           => ['required', 'string'],
            AffiliateKey::COUNTRY->value            => ['nullable', 'string'],
            AffiliateKey::UTILITY->value            => ['required', 'string'],
            AffiliateKey::ELECTRIC_BILL->value      => ['nullable', 'numeric'],
            AffiliateKey::ROOF_TYPE->value          => ['nullable', 'string'],
            'home_owner'                            => ['required', 'string'],
            AffiliateKey::COMMENTS->value           => ['nullable', 'string'],
            AffiliateKey::PLATFORM_CAMPAIGN->value  => ['nullable', 'string'],
            AffiliateKey::UNIVERSAL_LEAD_ID->value  => ['nullable', 'string'],
            AffiliateKey::IP_ADDRESS->value         => ['nullable', 'string'],
        ];

        $ceeKeyValueConversions = [
            AffiliateKey::OWN_PROPERTY->value => [
                'OWN'       => 'yes',
                'NOT OWN'   => 'no',
                PingPostAffiliateService::DEFAULT_CONVERSION => 'no',
            ],
        ];

        $ceeDefaultKeyValues = [
            AffiliateKey::INDUSTRY->value => 'solar',
            AffiliateKey::COUNTRY->value => 'US',
        ];

        $ceeKeyMap = AffiliateKey::getDefaultKeyMap();
        $ceeKeyMap['home_owner'] = AffiliateKey::OWN_PROPERTY->value;

        // Create affiliate entry
        $pxAffiliate = PingPostAffiliate::query()->create([
            PingPostAffiliate::FIELD_NAME => 'Clean Energy Experts',
            PingPostAffiliate::FIELD_TYPE => PingPostAffiliate::TYPE_POST,
            PingPostAffiliate::FIELD_STATUS => PingPostAffiliate::STATUS_ACTIVE,
            PingPostAffiliate::FIELD_REQUEST_RULES => $ceeRules,
            PingPostAffiliate::FIELD_KEY_MAP => $ceeKeyMap,
            PingPostAffiliate::FIELD_DEFAULT_KEY_VALUES => $ceeDefaultKeyValues,
            PingPostAffiliate::FIELD_KEY_VALUE_CONVERSIONS => $ceeKeyValueConversions,
            PingPostAffiliate::FIELD_DATA => [],
            PingPostAffiliate::FIELD_CREATED_AT => now(),
            PingPostAffiliate::FIELD_UPDATED_AT => now(),
        ]);

        // Create API Key
        $apiKey = PingPostAffiliateApiKey::query()->create([
            PingPostAffiliateApiKey::FIELD_PING_POST_AFFILIATE_ID => $pxAffiliate->id,
            PingPostAffiliateApiKey::FIELD_KEY => 'IiAi8K4FtwaXhGPJeGVSxJLSaELcAjpYwawemDNKcV1ZD5pexmzp',
            PingPostAffiliateApiKey::FIELD_TYPE => PingPostAffiliateApiKey::TYPE_POST,
            PingPostAffiliateApiKey::FIELD_STATUS => PingPostAffiliateApiKey::STATUS_ACTIVE,
            PingPostAffiliateApiKey::FIELD_CREATED_AT => now(),
            PingPostAffiliateApiKey::FIELD_UPDATED_AT => now(),
        ]);
    }
}
