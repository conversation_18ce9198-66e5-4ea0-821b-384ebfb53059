<?php

namespace Database\Seeders;

use App\Models\LeadProcessingConfiguration;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LeadProcessingConfigurationsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table(LeadProcessingConfiguration::TABLE)->insert([
            LeadProcessingConfiguration::FIELD_TIME_ZONE_OPENING_DELAY_IN_MINUTES => LeadProcessingConfiguration::TIME_ZONE_OPENING_DELAY_DEFAULT,
            LeadProcessingConfiguration::FIELD_LEAD_RECENCY_THRESHOLD_IN_SECONDS => LeadProcessingConfiguration::LEAD_RECENCY_THRESHOLD_DEFAULT,
            LeadProcessingConfiguration::FIELD_MINIMUM_REVIEW_TIME => LeadProcessingConfiguration::MINIMUM_REVIEW_TIME_DEFAULT,
            LeadProcessingConfiguration::FIELD_LEAD_PROCESSABLE_DELAY_SECONDS => LeadProcessingConfiguration::LEAD_PROCESSABLE_DELAY_DEFAULT,
            LeadProcessingConfiguration::FIELD_CHECK_NEXT_LEAD_INTERVAL_SECONDS => LeadProcessingConfiguration::CHECK_NEXT_LEAD_INTERVAL_DEFAULT
        ]);
    }
}
