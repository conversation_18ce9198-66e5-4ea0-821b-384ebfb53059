<?php

namespace Database\Seeders;

use App\Models\Odin\CompanyQualityScoreIndustryConfiguration;
use App\Models\Odin\CompanyQualityScoreRule;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CompanyQualityScoreGlobalConfigurationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     * @throws \Exception
     */
    public function run(): void
    {
        $rulesetId = CompanyQualityScoreRule::query()
            ->where(CompanyQualityScoreRule::FIELD_IS_PRODUCTION, true)
            ->first()
            ?->id ?? null;

        $globalConfig = [
            CompanyQualityScoreIndustryConfiguration::FIELD_COMPANY_QUALITY_SCORE_RULE_ID   => $rulesetId,
            CompanyQualityScoreIndustryConfiguration::FIELD_GLOBAL                          => true,
            CompanyQualityScoreIndustryConfiguration::FIELD_INDUSTRY_ID                     => null,
            Model::CREATED_AT                                                               => now(),
        ];

        DB::table(CompanyQualityScoreIndustryConfiguration::TABLE)->truncate();
        DB::table(CompanyQualityScoreIndustryConfiguration::TABLE)->insert($globalConfig);
    }

}
