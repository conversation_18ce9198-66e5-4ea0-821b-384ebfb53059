<?php

namespace Database\Seeders;

use App\Models\LeadProcessor;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LeadProcessorsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $insertRows = [];
        User::all()->each(function ($user) use (&$insertRows) {
             $insertRows[] = [
                 LeadProcessor::FIELD_USER_ID => $user->{User::FIELD_ID},
                 LeadProcessor::FIELD_LEAD_PROCESSING_TEAM_ID => 1
             ];
        });

        LeadProcessor::query()->insert($insertRows);
    }
}
