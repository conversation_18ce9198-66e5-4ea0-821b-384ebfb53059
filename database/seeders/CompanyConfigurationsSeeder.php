<?php

namespace Database\Seeders;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyConfiguration;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CompanyConfigurationsSeeder extends Seeder
{
    use WithoutModelEvents;

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $insertRows = [];

        foreach(Company::query()->pluck(Company::FIELD_ID) as $companyId) {
            $cc = CompanyConfiguration::factory()->definition();

            $cc[CompanyConfiguration::FIELD_COMPANY_ID] = $companyId;

            $insertRows[] = $cc;

            if(count($insertRows) > 100) {
                CompanyConfiguration::query()->insert($insertRows);

                $insertRows = [];
            }
        }

        if(!empty($insertRows)) {
            CompanyConfiguration::query()->insert($insertRows);
        }
    }
}
