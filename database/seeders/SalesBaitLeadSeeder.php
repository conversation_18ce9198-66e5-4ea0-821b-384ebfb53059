<?php

namespace Database\Seeders;

use App\Models\SalesBaitLead;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SalesBaitLeadSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table(SalesBaitLead::TABLE)->insert([
            [
                SalesBaitLead::FIELD_ID => 1,
                SalesBaitLead::FIELD_LEAD_ID => 1,
                SalesBaitLead::FIELD_COMPANY_ID => 1,
                SalesBaitLead::FIELD_CAMPAIGN_ID => 1,
                SalesBaitLead::FIELD_CLICKS => 1,
                SalesBaitLead::FIELD_ACTIONS_TAKEN => 1,
            ]
        ]);
    }
}
