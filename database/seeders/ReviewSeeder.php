<?php

namespace Database\Seeders;

use App\Enums\Odin\Industry;
use App\Models\ConsumerReviews\Review;
use App\Models\Odin\Company;
use App\Models\Odin\Industry as IndustryModel;
use App\Models\Odin\Website;
use Illuminate\Database\Seeder;

class ReviewSeeder extends Seeder
{
    /**
     * Creates 50 SR reviews among the 10 most recently updated companies
     *
     * @return void
     */
    public function run(): void
    {
        $industry = Industry::SOLAR->model() ?? IndustryModel::query()->first();
        $service = $industry?->services()->first();

        $companyIdSequence = Company::query()
            ->select(Company::FIELD_ID)
            ->orderBy(Company::UPDATED_AT, 'desc')
            ->limit(10)
            ->get()
            ->reduce(fn($output, $company) => [...$output, [ Review::FIELD_COMPANY_ID => $company->id ]], []);

        $website = Website::query()
            ->where(Website::FIELD_ABBREVIATION, 'sr')
            ->first()
            ?? Website::query()->first();

        Review::factory()
            ->count(50)
            ->state([
                Review::FIELD_WEBSITE_ID          => $website?->id ?? 1,
                Review::FIELD_INDUSTRY_ID         => $industry?->id ?? 1,
                Review::FIELD_INDUSTRY_SERVICE_ID => $service?->id ?? 1,
            ])->sequence(...$companyIdSequence)
            ->create();
    }
}