<?php

namespace Database\Seeders;

use App\Enums\Odin\LocationSiloPageLocationType;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\LocationSiloPage;
use App\Models\Odin\Silo;
use App\Models\Odin\Website;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PJDemoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table(Industry::TABLE)->insert([
            [
                Industry::FIELD_NAME        => "Plomberie",
                Industry::FIELD_SLUG        => "plomberie",
                Industry::FIELD_COLOR_LIGHT => "#248cdb",
                Industry::FIELD_COLOR_DARK  => "#79e1ff",
            ]
        ]);

        /** @var Industry $industry */
        $industry = Industry::query()->where(Industry::FIELD_SLUG, 'plomberie')->firstOrFail();

        DB::table(IndustryService::TABLE)->insert([
            [
                IndustryService::FIELD_NAME            => "Fosses septiques",
                IndustryService::FIELD_INDUSTRY_ID     => $industry->id,
                IndustryService::FIELD_SHOW_ON_WEBSITE => true,
                IndustryService::FIELD_SLUG            => 'fosses-septiques'
            ],
            [
                IndustryService::FIELD_NAME            => "Entretien de chaudières",
                IndustryService::FIELD_INDUSTRY_ID     => $industry->id,
                IndustryService::FIELD_SHOW_ON_WEBSITE => true,
                IndustryService::FIELD_SLUG            => 'entretien-de-chaudières'
            ],
            [
                IndustryService::FIELD_NAME            => "Dépannage plomberie",
                IndustryService::FIELD_INDUSTRY_ID     => $industry->id,
                IndustryService::FIELD_SHOW_ON_WEBSITE => true,
                IndustryService::FIELD_SLUG            => 'dépannage-plomberie'
            ],
            [
                IndustryService::FIELD_NAME            => "Assainissement",
                IndustryService::FIELD_INDUSTRY_ID     => $industry->id,
                IndustryService::FIELD_SHOW_ON_WEBSITE => true,
                IndustryService::FIELD_SLUG            => 'assainissement'
            ]
        ]);

        DB::table(Website::TABLE)->insert([
            [
                Website::FIELD_NAME         => "PagesJaunes",
                Website::FIELD_ABBREVIATION => "pj",
                Website::FIELD_URL          => "https://pjdemo.fixr.com"
            ]
        ]);

        /** @var Website $website */
        $website = Website::query()->where(Website::FIELD_ABBREVIATION, "pj")->firstOrFail();

        /**
         * @TODO: Upgrade to use new models.
         * @see Silo
         * @see LocationSiloPage
         */
        Silo::query()->create([
            Silo::FIELD_NAME                => "Plomberie",
            Silo::FIELD_ROOT_PATH           => "/annuaire",
            Silo::FIELD_COLLECTION_HANDLE   => "plomberie_silo",
            Silo::FIELD_WEBSITE_ID          => $website->id,
            Silo::FIELD_INDUSTRY_ID         => $industry->id,
            Silo::FIELD_INDUSTRY_SERVICE_ID => null,
        ]);

        /** @var Silo $silo */
        $silo = Silo::query()->where(Silo::FIELD_ROOT_PATH, '/annuaire')->firstOrFail();

        LocationSiloPage::query()->create([
            LocationSiloPage::FIELD_SILO_ID            => $silo->id,
            LocationSiloPage::FIELD_RELATIVE_PATH      => "/france/plomberie",
            LocationSiloPage::FIELD_ENTRY_SLUG         => "national",
            LocationSiloPage::FIELD_LOCATION_TYPE      => LocationSiloPageLocationType::NATIONAL,
            LocationSiloPage::FIELD_PARENT_LOCATION_ID => null,
            LocationSiloPage::FIELD_LOCATION_ID        => null,
            LocationSiloPage::FIELD_IS_ACTIVE          => true,
        ]);
    }
}
