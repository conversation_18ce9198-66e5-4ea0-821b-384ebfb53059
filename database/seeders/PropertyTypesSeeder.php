<?php

namespace Database\Seeders;

use App\Models\Odin\PropertyType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Enums\Odin\PropertyType as PropertyTypeEnum;

class PropertyTypesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $insertRows = [];
        foreach(PropertyTypeEnum::cases() as $case) {
            $insertRows[] = [
                PropertyType::FIELD_NAME => $case->value
            ];
        }

        DB::table(PropertyType::TABLE)->insert($insertRows);
    }
}
