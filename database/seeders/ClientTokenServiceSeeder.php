<?php

namespace Database\Seeders;

use App\Models\ClientTokenService;
use Illuminate\Database\Seeder;

class ClientTokenServiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $tokens = [
             [
                 ClientTokenService::FIELD_SERVICE_KEY  => ClientTokenService::LEGACY_API_SERVICE_KEY,
                 ClientTokenService::FIELD_SERVICE_NAME => 'Legacy Admin API',
             ],
             [
                 ClientTokenService::FIELD_SERVICE_KEY => ClientTokenService::MICROSOFT_ADS_API_SERVICE_KEY,
                 ClientTokenService::FIELD_SERVICE_NAME => 'Microsoft Ads API',
             ],
             [
                 ClientTokenService::FIELD_SERVICE_KEY => ClientTokenService::SCHEDULING_API_SERVICE_KEY,
                 ClientTokenService::FIELD_SERVICE_NAME => 'Scheduling API',
             ],
             [
                 ClientTokenService::FIELD_SERVICE_KEY => ClientTokenService::META_ADS_API_SERVICE_KEY,
                 ClientTokenService::FIELD_SERVICE_NAME => 'Meta Ads API',
             ],
        ];

        foreach($tokens as $token)
            $this->createKey($token[ClientTokenService::FIELD_SERVICE_KEY], $token[ClientTokenService::FIELD_SERVICE_NAME]);

        $this->call(FlowEnginesClientTokenSeeder::class);
    }
    
    private function createKey(string $key, string $name): void
    {
        ClientTokenService::query()
            ->firstOrCreate([
                ClientTokenService::FIELD_SERVICE_KEY => $key,
            ], [
                ClientTokenService::FIELD_SERVICE_NAME => $name,
            ]);
    }
}
