<?php

namespace Database\Seeders\Populators;

use App\Models\Odin\Company;
use App\Models\Odin\GenericProfitabilityAssumptionConfiguration;

class GenericProfitabilityAssumptionConfigurationPopulator extends BasePopulator
{
    /**
     * Run the database seeds.
     * @return void
     */
    public function run(): void
    {
        $companyIds = $this->getModelIdsOrDummyIds(Company::class);
        $companyCount = count($companyIds);
        print("Adding dummy GenericProfitabilityAssumptionConfigurations for $companyCount Companies...\n");
        foreach ($companyIds as $companyId) {
            $serviceIds = Company::query()->find($companyId)?->{Company::RELATION_SERVICES} ?? [];
                foreach ($serviceIds as $serviceId) {
                    GenericProfitabilityAssumptionConfiguration::factory()->create([
                        GenericProfitabilityAssumptionConfiguration::FIELD_COMPANY_ID           => $companyId,
                        GenericProfitabilityAssumptionConfiguration::FIELD_INDUSTRY_SERVICE_ID  => $serviceId,
                    ]);
                }
        }
    }
}
