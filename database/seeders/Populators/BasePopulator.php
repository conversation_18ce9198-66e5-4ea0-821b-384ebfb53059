<?php

namespace Database\Seeders\Populators;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Seeder;
use Faker;

class BasePopulator extends Seeder
{
    /** @var Faker\Generator */
    protected Faker\Generator $faker;

    public function __construct()
    {
        $this->faker = Faker\Factory::create();
    }

    /**
     * Get an array of existing ids for a given model class.
     * Provide the primary key column name if it is non-standard
     *
     * @param string $modelClass
     * @param null|string $idColumn
     * @return array
     */
    protected function getModelIdsOrDummyIds(string $modelClass, ?string $idColumn = 'id'): array
    {
        /** @var $modelClass Model */
        return $modelClass::query()?->first()
            ? $modelClass::all()->pluck($idColumn)->toArray()
            : range(1, 9);
    }
}
