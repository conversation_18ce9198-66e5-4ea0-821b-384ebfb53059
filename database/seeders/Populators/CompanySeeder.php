<?php

namespace Database\Seeders\Populators;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use Illuminate\Database\Seeder;

class CompanySeeder extends Seeder
{

    /**
     * Run the database seeds.
     * @return void
     */
    public function run(): void
    {
        $companies = Company::factory()
            ->hasLocations(3)
            ->hasData(1)
            ->hasUsers(5)
            ->hasUsers(5, [
                CompanyUser::FIELD_CAN_LOG_IN         => false,
                CompanyUser::FIELD_IS_CONTACT         => true,
                CompanyUser::FIELD_FAILED_LOGIN_COUNT => 0,
                CompanyUser::FIELD_PASSWORD           => "",
                CompanyUser::FIELD_EMAIL_VERIFIED_AT  => null,
                CompanyUser::FIELD_PHONE_VERIFIED_AT  => null,
            ])
            ->hasReviews(10)
            ->count(20)
            ->create();
    }
}
