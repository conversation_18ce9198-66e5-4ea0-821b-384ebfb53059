<?php

namespace Database\Seeders\Populators;

use App\Console\Commands\MigrateLegacyAddresses;
use App\Console\Commands\MigrateLegacyCompanies;
use App\Console\Commands\MigrateLegacyCompanyContacts;
use App\Console\Commands\MigrateLegacyCompanyUsers;
use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Legacy\EloquentUser;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Artisan;

class CompanyMigrateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * @return void
     */
    public function run(): void
    {
        $start = Carbon::now();
        $companyLimit = 1000;

        $eloquentCompanies = EloquentCompany::query()
            ->inRandomOrder()
            ->limit($companyLimit)
            ->get();

        $requiredIds = [
            'companies'  => [],
            'addresses'  => [],
            'users'      => [],
            'contacts'   => [],
        ];

        $eloquentCompanies->each(function(EloquentCompany $company) use (&$requiredIds) {
            $requiredIds['companies'][] = $company->{EloquentCompany::ID};
            array_push($requiredIds['addresses'], ...$company->addresses()->pluck(EloquentAddress::TABLE.".".EloquentAddress::ID));
            array_push($requiredIds['contacts'], ...$company->contacts(true)->pluck(EloquentCompanyContact::FIELD_CONTACT_ID));
            array_push($requiredIds['users'], ...$company->users(true)->pluck(EloquentUser::ID));
        });

        $total = array_reduce($requiredIds, fn($acc, $val) => $acc += count($val) ?? 0, 0);
        print("Total rows required for {$companyLimit} base Companies: {$total}...\n");

        $addressesCreated = Artisan::call(MigrateLegacyAddresses::class, [ '--ids' => $requiredIds['addresses']]);
        print("\t...migrated {$addressesCreated} addresses...\n");

        $companiesCreated = Artisan::call(MigrateLegacyCompanies::class, [ '--ids' => $requiredIds['companies']]);
        print("\t...migrated {$companiesCreated} companies...\n");

        $contactsCreated = Artisan::call(MigrateLegacyCompanyContacts::class, [ '--ids' => $requiredIds['contacts']]);
        print("\t...migrated {$contactsCreated} company contacts...\n");

        $usersCreated = Artisan::call(MigrateLegacyCompanyUsers::class, [ '--ids' => $requiredIds['users']]);
        print("\t...migrated {$usersCreated} company users...\n");

        $finish = Carbon::now()->diffInSeconds($start, true);
        print("Migration completed - {$companyLimit} Companies migrated in {$finish} seconds.\n\nCreating dummy data...\n\n");
    }
}
