<?php

namespace Database\Seeders\Populators;

use App\Models\Teams\Team;
use App\Models\Teams\TeamLeader;
use App\Models\Teams\TeamMember;
use App\Models\User;
use Spatie\Permission\Models\Role;

class TeamPopulator extends BasePopulator
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        $userIds = $this->getModelIdsOrDummyIds(User::class);
        $salesManagerRole = Role::query()->where('name', 'sales-manager')->first();
        $accountManagerRole = Role::query()->where('name', 'account-manager')->first();

        // Create a fresh sales team
        if ($salesManagerRole && $accountManagerRole) {
            $salesTeam = Team::factory()->create([
                Team::FIELD_NAME            => 'Sales Team Alpha',
                Team::FIELD_DESCRIPTION     => 'Special team for the greater prosperity of overwhelming success',
                Team::FIELD_TEAM_TYPE_ID    => 2,
            ]);
            $teamLeadUser = User::factory()->create([
                User::FIELD_LEGACY_USER_ID => 1,
            ]);
            $teamLeadUser->assignRole($salesManagerRole);
            $teamMemberUsers = User::factory()
                ->count(3)
                ->create([
                    User::FIELD_LEGACY_USER_ID => 1,
                ]);
            $teamMemberUsers->each(fn(User $user) => $user->assignRole($accountManagerRole));
            $teamMemberIds = $teamMemberUsers->pluck(TeamMember::FIELD_ID);

            TeamLeader::factory()
                ->count(1)
                ->create([
                TeamLeader::FIELD_TEAM_ID => $salesTeam->{Team::FIELD_ID},
                TeamLeader::FIELD_USER_ID => $teamLeadUser->{User::FIELD_ID},
                TeamLeader::FIELD_REPORTS_TO => $this->faker->randomElement($userIds),
            ]);

            $teamMemberIds->each(fn(int $id) => TeamMember::factory()->create([
                TeamMember::FIELD_TEAM_ID   => $salesTeam->{Team::FIELD_ID},
                TeamMember::FIELD_USER_ID   => $id,
            ]));
        }

        // Create a random team
        $customTeam = Team::factory()->create([
            Team::FIELD_TEAM_TYPE_ID        => 1,
        ]);
        TeamLeader::factory()->create([
            TeamLeader::FIELD_TEAM_ID    => $customTeam->{Team::FIELD_ID},
            TeamLeader::FIELD_USER_ID    => $this->faker->randomElement($userIds),
            TeamLeader::FIELD_REPORTS_TO => $this->faker->randomElement($userIds)
        ]);
        $newMemberCount = min(count($userIds), 3);
        $newMembers = TeamMember::factory()
            ->for($customTeam)
            ->count($newMemberCount)
            ->create();
        $randomIds = $this->faker->unique()->randomElements($userIds, $newMemberCount);
        $newMembers->each(fn(TeamMember $member, $index) => $member->update([ TeamMember::FIELD_USER_ID => $randomIds[$index] ]));
    }
}
