<?php

namespace Database\Seeders\Populators;

use App\Models\ActivityConversation;
use App\Models\ActivityFeed;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Seeder;
use Faker;

class ActivityConversationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $activityFeeds = ActivityFeed::all();
        $faker = Faker\Factory::create();
        $userIds = $this->getModelIdsOrDummyIds(User::class);
        print("Adding dummy Comments to {$activityFeeds->count()} Company Activities...\n");

        $activityFeeds->each(function(ActivityFeed $activity) use ($faker, $userIds) {
            $commentCount = $faker->numberBetween(0, 2);
            ActivityConversation::factory()
                ->count($commentCount)
                ->create([
                    ActivityConversation::FIELD_ACTIVITY_ID => $activity->{ActivityFeed::FIELD_ID},
                    ActivityConversation::FIELD_PARENT_ID   => 0,
                    ActivityConversation::FIELD_USER_ID     => $faker->randomElement($userIds),
                ]);
        });
    }

    /**
     * @param string $modelClass
     * @param null|string $idColumn
     * @return array
     */
    protected function getModelIdsOrDummyIds(string $modelClass, ?string $idColumn = 'id'): array
    {
        /** @var $modelClass Model */
        return $modelClass::query()?->first()
            ? $modelClass::all()->pluck($idColumn)->toArray()
            : [ 1, 2, 3, 4, 5, 6, 7, 8, 9 ];
    }
}
