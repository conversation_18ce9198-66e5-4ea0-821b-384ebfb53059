<?php

namespace Database\Seeders\Populators;

use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Seeder;
use Faker;

class ProductAssignmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        $faker = Faker\Factory::create();
        $companyIds = collect($this->getModelIdsOrDummyIds(Company::class));
        $consumerProductIds = ConsumerProduct::query()
            ->where(ConsumerProduct::FIELD_STATUS, ConsumerProduct::STATUS_ALLOCATED)
            ->pluck(ConsumerProduct::FIELD_ID);
        $consumerProductIds = $consumerProductIds->count() > 10
            ? $consumerProductIds
            : $this->getModelIdsOrDummyIds(ConsumerProduct::class);
        print("Adding dummy Product Assignments for {$consumerProductIds->count()} Consumer Products...\n");

        $consumerProductIds->each(function(int $consumerProductId) use ($faker, $companyIds) {
            $assignmentCount = $faker->numberBetween(1, 2);
            ProductAssignment::factory()
                ->count($assignmentCount)
                ->create([
                    ProductAssignment::FIELD_COMPANY_ID             => $faker->randomElement($companyIds),
                    ProductAssignment::FIELD_CONSUMER_PRODUCT_ID    => $consumerProductId,
                ]);

        });
    }

    /**
     * @param string $modelClass
     * @param null|string $idColumn
     * @return array
     */
    protected function getModelIdsOrDummyIds(string $modelClass, ?string $idColumn = 'id'): array
    {
        /** @var $modelClass Model */
        return $modelClass::query()?->first()
            ? $modelClass::all()->pluck($idColumn)->toArray()
            : [ 1, 2, 3, 4, 5, 6, 7, 8, 9 ];
    }
}
