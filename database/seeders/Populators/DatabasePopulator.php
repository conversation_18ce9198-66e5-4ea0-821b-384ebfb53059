<?php

namespace Database\Seeders\Populators;

use App\Models\Odin\GenericProfitabilityAssumptionConfiguration;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\App;

class DatabasePopulator extends Seeder
{
    /**
     * Populate the application's database with dummy data
     *
     * @return void
     */
    public function run(): void
    {
        $startTime = Carbon::now();
        print("\n\nPopulating fresh database at {$startTime}...\n\n");

        if (!App::environment('production')) {
            $this->call([
                CompanyMigrateSeeder::class,
                ConsumerSeeder::class,
                CompanyReviewSeeder::class,
                ProductAssignmentSeeder::class,
                CompanyActivitiesSeeder::class,
                ActivityConversationSeeder::class,
                GenericProfitabilityAssumptionConfigurationPopulator::class,
            ]);

            $elapsed = Carbon::now()->diffForHumans($startTime);
            print("Completed database populating {$elapsed} starting.\n");
        }
    }
}
