<?php

namespace Database\Seeders\Populators;

use App\Models\CompanyProfile\CompanyProfile;
use App\Models\CompanyProfile\CompanyProfileIndustryService;
use App\Models\CompanyProfile\CompanyProfileLocation;
use App\Models\CompanyProfile\CompanyProfileReview;
use App\Models\CompanyProfile\CompanyProfileServiceArea;
use App\Models\Legacy\Location;
use App\Models\Odin\IndustryService;
use Illuminate\Database\Seeder;

class CompanyProfileSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $profile = CompanyProfile::factory()->create([
            CompanyProfile::FIELD_PUBLISHED => true, //so it shows on fixr
        ]);

        CompanyProfileReview::factory()->count(10)->create([
            CompanyProfileReview::FIELD_COMPANY_PROFILE_ID => $profile->id,
        ]);

        $profileService = CompanyProfileIndustryService::factory()->create([
            CompanyProfileIndustryService::FIELD_COMPANY_PROFILE_ID => $profile->id,
            CompanyProfileIndustryService::FIELD_INDUSTRY_SERVICE_ID => IndustryService::query()->inRandomOrder()->first()->id,
        ]);


        $locations = Location::query()->where(Location::TYPE, Location::TYPE_STATE)->limit(10)->get();

        foreach ($locations as $location) {
            $profileLocation = CompanyProfileLocation::factory()->create([
                CompanyProfileLocation::FIELD_COMPANY_PROFILE_ID => $profile->id,
                CompanyProfileLocation::FIELD_LOCATION_ID => $location->id,
            ]);

            CompanyProfileServiceArea::factory()->create([
                CompanyProfileServiceArea::FIELD_COMPANY_PROFILE_ID => $profile->id,
                CompanyProfileServiceArea::FIELD_COMPANY_PROFILE_INDUSTRY_SERVICE_ID => $profileService->id,
                CompanyProfileServiceArea::FIELD_COMPANY_PROFILE_LOCATION_ID => $profileLocation->id,
            ]);
        }

        print("Created Company Profile $profile->name and associated models...\n");
    }
}
