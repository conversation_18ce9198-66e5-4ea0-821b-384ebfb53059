<?php

namespace Database\Seeders;

use App\Enums\PermissionType;
use App\Enums\RoleType;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class TemplateManagementAndSmsTemplatePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Role::findOrCreate(RoleType::ADMIN->value)->givePermissionTo([
            Permission::findOrCreate(PermissionType::TEMPLATE_MANAGEMENT->value),
            Permission::findOrCreate(PermissionType::SMS_TEMPLATE_VIEW->value),
            Permission::findOrCreate(PermissionType::SMS_TEMPLATE_EDIT->value),
        ]);
    }
}
