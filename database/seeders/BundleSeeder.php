<?php

namespace Database\Seeders;

use App\Models\Bundle;
use App\Models\BundleInvoice;
use App\Models\BundleInvoiceHistory;
use Database\Factories\BundleFactory;
use Database\Factories\BundleInvoiceHistoryFactory;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BundleSeeder extends Seeder
{
    use WithoutModelEvents;

    /**
     * Create 3 set bundles and generate 5 invoices for each of them, setting the cost and credit from the bundle.
     *
     * @return void
     */
    public function run()
    {
        Bundle::factory(3)->create()->each(function ($bundle) {
            BundleInvoice::factory(10)->create([
                BundleInvoice::FIELD_BUNDLE_ID => $bundle->id,
                BundleInvoice::FIELD_COST => $bundle->cost,
                BundleInvoice::FIELD_CREDIT => $bundle->credit
            ])->each(function ($invoice) {
                BundleInvoiceHistory::factory(4)->create([
                    BundleInvoiceHistory::FIELD_BUNDLE_INVOICE_ID => $invoice->id,
                ]);
            });
        });
    }
}
