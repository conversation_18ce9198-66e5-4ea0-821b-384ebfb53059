<?php

namespace Database\Seeders;

use App\Models\Odin\Industry;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Enums\Odin\Industry as IndustryEnum;

class IndustriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        $data = [];
        $cases = IndustryEnum::cases();

        foreach($cases as $case) {
            $data[] = [
                Industry::FIELD_NAME    => $case->value,
                Industry::FIELD_SLUG    => $case->getSlug(),
            ];
        }

        DB::table(Industry::TABLE)->insert($data);
    }
}
