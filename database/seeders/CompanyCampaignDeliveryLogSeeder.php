<?php

namespace Database\Seeders;

use App\Models\Campaigns\Modules\Delivery\CompanyCampaignContactDeliveryLog;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryLog;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

/**
 * @see \Feature\Http\Controllers\CompanyCampaignContactDeliveryLogControllerTest
 * @see \Tests\Feature\Http\Controllers\CompanyCampaignDeliveryLogControllerTest
 */
class CompanyCampaignDeliveryLogSeeder extends Seeder
{
    use WithoutModelEvents;

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        CompanyCampaignDeliveryLog::factory()->createQuietly();
        CompanyCampaignContactDeliveryLog::factory()->createQuietly();
    }
}
