<?php

namespace Database\Seeders;

use App\Models\Workflow;
use App\Models\WorkflowEvent;
use Illuminate\Database\Seeder;

class WorkflowSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $workflowEventIds = WorkflowEvent::query()->pluck(WorkflowEvent::FIELD_ID);

        $insertRows = [];
        foreach($workflowEventIds as $workflowEventId) {
            $insertRows[] = [
                Workflow::FIELD_WORKFLOW_EVENT_ID => $workflowEventId,
                Workflow::FIELD_NAME => "Test"
            ];

            if(count($insertRows) >= 250) {
                Workflow::query()->insert($insertRows);

                $insertRows = [];
            }
        }

        if(!empty($insertRows)) {
            Workflow::query()->insert($insertRows);
        }
    }
}
