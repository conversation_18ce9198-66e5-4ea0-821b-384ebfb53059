<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Enums\CompanyMetrics\CompanyMetricRequestTypes;
use App\Models\CompanyMetric;
use App\Models\Odin\Company;

class CompanyMetricsSeeder extends Seeder
{
    use WithoutModelEvents;

    protected Company $company;

    protected CompanyMetric $companyMetric;

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->company = Company::first() ?? Company::factory()->createQuietly();

        $this->companyMetric = CompanyMetric::factory()->create([
            'company_id' => $this->company->id,
            'request_type' => CompanyMetricRequestTypes::PPC_SPEND->value,
            'request_response' => [
                'monthlySpend' => 1000,
                'year' => 2021,
                'month' => 1
            ]
        ]);
    }
}
