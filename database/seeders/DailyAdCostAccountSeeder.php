<?php

namespace Database\Seeders;

use App\Enums\Advertising\Advertiser;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Enums\Odin\Industry;
use App\Models\DailyAdCostAccount;
use App\Models\Odin\IndustryService;
use Illuminate\Database\Seeder;

class DailyAdCostAccountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        $solarIndustryServiceId = IndustryService::query()
            ->where(IndustryService::FIELD_SLUG, 'solar-installation')
            ->get()->first()->{IndustryService::FIELD_ID};

        $solarRepairsIndustryServiceId = IndustryService::query()
            ->where(IndustryService::FIELD_SLUG, 'solar-repair')
            ->get()->first()->{IndustryService::FIELD_ID};

        $bathroomsIndustryServiceId = IndustryService::query()
            ->where(IndustryService::FIELD_SLUG, 'bathroom-remodeling')
            ->get()->first()->{IndustryService::FIELD_ID};

        $concreteFoundationRepairIndustryServiceId = IndustryService::query()
            ->where(IndustryService::FIELD_SLUG, 'foundation-repair')
            ->get()->first()->{IndustryService::FIELD_ID};

        $hvacReplaceIndustryServiceId = IndustryService::query()
            ->where(IndustryService::FIELD_SLUG, 'hvac-replace')
            ->get()->first()->{IndustryService::FIELD_ID};

        $kitchenRemodelIndustryServiceId = IndustryService::query()
            ->where(IndustryService::FIELD_SLUG, 'kitchen-remodeling')
            ->get()->first()->{IndustryService::FIELD_ID};

        $roofReplacementIndustryServiceId = IndustryService::query()
            ->where(IndustryService::FIELD_SLUG, 'roof-replacement')
            ->get()->first()->{IndustryService::FIELD_ID};

        $sidingReplacementIndustryServiceId = IndustryService::query()
            ->where(IndustryService::FIELD_SLUG, 'siding-replacement')
            ->get()->first()->{IndustryService::FIELD_ID};

        $windowsIndustryServiceId = IndustryService::query()
            ->where(IndustryService::FIELD_SLUG, 'windows-install-replace')
            ->get()->first()->{IndustryService::FIELD_ID};

        // GOOGLE ACCOUNTS
        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '**********',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Google Solar Estimate Will',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::WILL,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '**********',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::SOLAR->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $solarIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );

        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '**********',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Google Bathroom Estimate Gabe',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::GABE_NEW,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '**********',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::BATHROOMS->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $bathroomsIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );

        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '**********',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Google Bathroom Remodel Fixr Gabe',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::GABE_NEW,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '**********',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::BATHROOMS->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $bathroomsIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );

        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '**********',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Google Concrete Foundation Repair Will',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::WILL,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '**********',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::CONCRETE_FOUNDATION->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $concreteFoundationRepairIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );

        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '**********',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Google HVAC Will',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::WILL,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '**********',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::HVAC->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $hvacReplaceIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );

        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '**********',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Google Kitchen Estimate Gabe',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::GABE_NEW,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '**********',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::KITCHENS->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $kitchenRemodelIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );

        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '**********',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Google Kitchen Remodel Fixr Gabe',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::GABE_NEW,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '**********',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::KITCHENS->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $kitchenRemodelIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );

        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '**********',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Google Roofing Calculator Will',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::WILL,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '**********',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::ROOFING->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $roofReplacementIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );

        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '**********',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Google Siding Gabe',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::GABE_NEW,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '**********',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::SIDING->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $sidingReplacementIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );

        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '**********',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Google Solar Repairs Old',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::WILL,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '**********',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::SOLAR->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $solarRepairsIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );

        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '**********',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Google Solar Reviews Old',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::WILL,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '**********',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::SOLAR->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $solarIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );

        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '**********',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Google Windows Gabe',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::GABE_NEW,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '**********',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::WINDOWS->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $windowsIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );

        // Excluded accounts
        // Solar Wholesalers Ad Cost is not ours
        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '**********',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Google Solar Wholesalers Exclude',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '**********',
                DailyAdCostAccount::FIELD_EXCLUDE => true,
            ]
        );

        // Decking account throws permissions error with google, it may be deleted
        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '**********',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Google Decking Old',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '**********',
                DailyAdCostAccount::FIELD_EXCLUDE => true,
            ]
        );


        // META ACCOUNTS
        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => 'act_1728995343981261',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::META,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Meta Solar Investments Will',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::META,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::WILL,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => 'act_1728995343981261',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::SOLAR->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $solarIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );

        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => 'act_1454971044535254',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::META,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Meta Windows Thomas',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::META,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::THOMAS,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => 'act_1454971044535254',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::WINDOWS->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $windowsIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );

        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => 'act_2736348503137214',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::META,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Meta Solar Estimate Old',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::META,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => 'act_2736348503137214',
                DailyAdCostAccount::FIELD_EXCLUDE => true,
            ]
        );

        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => 'act_1083108348837041',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::META,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Meta Solar Reviews Old',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::META,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => 'act_1083108348837041',
                DailyAdCostAccount::FIELD_EXCLUDE => true,
            ]
        );

        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => 'act_2926312304309745',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::META,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Meta Roofing Calculator Will',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::META,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::WILL,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => 'act_2926312304309745',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::ROOFING->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $roofReplacementIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );

        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => 'act_672568924813904',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::META,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Meta Windows Gabe',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::META,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::GABE_NEW,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => 'act_672568924813904',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::WINDOWS->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $windowsIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );

        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => 'act_673028481531001',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::META,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Meta Siding Gabe',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::META,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::GABE_NEW,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => 'act_673028481531001',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::SIDING->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $sidingReplacementIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );

        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => 'act_544521077949531',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::META,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Meta Solar Estimate Thomas',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::META,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::THOMAS,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => 'act_544521077949531',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::SOLAR->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $solarIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );

        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => 'act_1280959096274882',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::META,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Meta Roofing Calculator Thomas',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::META,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::THOMAS,
                    DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => 'act_1280959096274882',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::ROOFING->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $roofReplacementIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );

        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => 'act_1108617150794533',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::META,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Meta Bathroom Estimate Gabe',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::META,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::GABE_NEW,
                    DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => 'act_1108617150794533',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::BATHROOMS->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $bathroomsIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );

        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => 'act_1133799671557246',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::META,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Meta Roofing Calculator Wade',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::META,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::WADE,
                    DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => 'act_1133799671557246',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::ROOFING->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $roofReplacementIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );

        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => 'act_1226558655603766',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::META,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Meta Windows Wade',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::META,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::WADE,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => 'act_1226558655603766',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::WINDOWS->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $windowsIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );

        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => 'act_1309622269429042',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::META,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Meta Solar Wade',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::META,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::WADE,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => 'act_1309622269429042',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::SOLAR->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $solarIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );


        // MICROSOFT ACCOUNTS
        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '*********',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::MICROSOFT,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Microsoft Roofing Calculator Gabe',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::MICROSOFT,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::GABE_NEW,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '*********',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::ROOFING->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $roofReplacementIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );

        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '*********',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::MICROSOFT,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Microsoft Cut My Bill Gabe',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::MICROSOFT,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::GABE_NEW,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '*********',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::SOLAR->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $solarIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );

        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '*********',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::MICROSOFT,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Microsoft Bathroom Estimate Gabe',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::MICROSOFT,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::GABE_NEW,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '*********',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::BATHROOMS->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $bathroomsIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );

        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '********',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::MICROSOFT,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Microsoft Solar Estimate Gabe',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::MICROSOFT,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::GABE_NEW,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '********',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::SOLAR->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $solarIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );

        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '*********',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::MICROSOFT,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Microsoft Siding Gabe',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::MICROSOFT,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::GABE_NEW,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '*********',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::SIDING->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $sidingReplacementIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );

        DailyAdCostAccount::updateOrCreate(
            [
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '*********',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::MICROSOFT,
            ],
            [
                DailyAdCostAccount::FIELD_NAME => 'Microsoft Solar Reviews Gabe',
                DailyAdCostAccount::FIELD_PLATFORM => AdvertisingPlatform::MICROSOFT,
                DailyAdCostAccount::FIELD_ADVERTISER => Advertiser::GABE_NEW,
                DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID => '*********',
                DailyAdCostAccount::FIELD_INDUSTRY_ID => Industry::SOLAR->model()->id,
                DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID => $solarIndustryServiceId,
                DailyAdCostAccount::FIELD_EXCLUDE => false,
                DailyAdCostAccount::FIELD_DATA => [],
            ]
        );
    }
}
