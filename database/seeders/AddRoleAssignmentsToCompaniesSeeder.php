<?php

namespace Database\Seeders;

use App\Models\Odin\Company;
use App\Models\User;
use App\Services\Filterables\Company\CompanyMissingRoleAssignmentsFilterable;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Tests\Feature\Http\Controllers\API\CompanySearchControllerTest;
use Throwable;

/**
 * @see CompanyMissingRoleAssignmentsFilterable
 * @see CompanySearchControllerTest::search_with_missing_role_assignment_filterables
 */
class AddRoleAssignmentsToCompaniesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @throws Throwable
     */
    public function run(): void
    {
        $this->createRoleAndAssignToCompany('account-manager');
        $this->createRoleAndAssignToCompany('business-development-manager');
        $this->createRoleAndAssignToCompany('sales-development-representative');
        $this->createRoleAndAssignToCompany('customer-success-manager');
        $this->createRoleAndAssignToCompany('onboarding-manager');
    }

    /**
     * @throws Throwable
     */
    private function createRoleAndAssignToCompany(string $roleName): void
    {
        $role = Role::findOrCreate($roleName);

        $company = Company::factory()->createQuietly();

        $company->assign(User::factory()->create())->as($role->name);
    }
}
