<?php

namespace Database\Seeders;

use App\Enums\Odin\RoofingConfigurableFields;
use App\Models\Odin\ConfigurableFieldType;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryConsumerField;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Enums\Odin\SolarConfigurableFields;
use App\Enums\Odin\GlobalConfigurableFields;

class IndustryConsumerFieldsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $industries = Industry::all()->pluck(Industry::FIELD_ID, Industry::FIELD_NAME)->toArray();

        DB::table(IndustryConsumerField::TABLE)->insert([
            //Solar
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'System Type',
                IndustryConsumerField::FIELD_KEY => SolarConfigurableFields::SYSTEM_TYPE,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'System Size',
                IndustryConsumerField::FIELD_KEY => SolarConfigurableFields::SYSTEM_SIZE,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_INTEGER)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'System Size Other',
                IndustryConsumerField::FIELD_KEY => SolarConfigurableFields::SYSTEM_SIZE_OTHER,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Storeys',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::STOREYS,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_INTEGER)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Electric Cost',
                IndustryConsumerField::FIELD_KEY => SolarConfigurableFields::ELECTRIC_COST,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_FLOAT)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Roof Direction',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::ROOF_DIRECTION,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_FLOAT)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Roof Shading',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::ROOF_SHADING,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Roof Pitch',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::ROOF_PITCH,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_FLOAT)->first()?->id,

            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Roof Type',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::ROOF_TYPE,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Roof Type Other',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::ROOF_TYPE_OTHER,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,

            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Roof Condition',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::ROOF_CONDITION,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Panel Tier',
                IndustryConsumerField::FIELD_KEY => SolarConfigurableFields::PANEL_TIER,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Best Time to Call',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::BEST_TIME_TO_CALL,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Best Time to Call Other',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::BEST_TIME_TO_CALL_OTHER,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Utility Name',
                IndustryConsumerField::FIELD_KEY => SolarConfigurableFields::UTILITY_NAME,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Utility ID',
                IndustryConsumerField::FIELD_KEY => SolarConfigurableFields::UTILITY_ID,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_INTEGER)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Own Property',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::OWN_PROPERTY,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_BOOLEAN)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Property Type',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::PROPERTY_TYPE,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Project Type',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::PROJECT_TYPE,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Payment Type',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::PAYMENT_TYPE,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'IP Address',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::IP_ADDRESS,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Origin',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::ORIGIN,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Email Validated',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::EMAIL_VALIDATED,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_BOOLEAN)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Phone Validated',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::PHONE_VALIDATED,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_BOOLEAN)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Phone Validation Method',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::PHONE_VALIDATION_METHOD,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Affiliate Id',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::AFFILIATE_ID,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_INTEGER)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Campaign Id',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::CAMPAIGN_ID,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_INTEGER)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Track Code',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::TRACK_CODE,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Track Name',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::TRACK_NAME,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Track Lead Id',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::TRACK_LEAD_ID,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'TCPA Lead Id',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::TCPA_LEAD_ID,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Lead Id Service Type',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::LEAD_ID_SERVICE_TYPE,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Contact Method',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::CONTACT_METHOD,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'TCPA Disclosure Captured',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::TCPA_DISCLOSURE_CAPTURED,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_BOOLEAN)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Auto Assigned Status',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::AUTO_ASSIGNED_STATUS,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_BOOLEAN)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'UTC',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::UTC,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_INTEGER)->first()?->id,
            ],

            //Roofing
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Storeys',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::STOREYS,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_INTEGER)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Roof Direction',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::ROOF_DIRECTION,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_FLOAT)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Roof Shading',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::ROOF_SHADING,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Roof Pitch',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::ROOF_PITCH,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_FLOAT)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Roof Type',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::ROOF_TYPE,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Roof Type Other',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::ROOF_TYPE_OTHER,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Roof Condition',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::ROOF_CONDITION,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Best Time to Call',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::BEST_TIME_TO_CALL,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Best Time to Call Other',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::BEST_TIME_TO_CALL_OTHER,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Own Property',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::OWN_PROPERTY,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_BOOLEAN)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Roof Skylights',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::ROOF_SKYLIGHTS,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_BOOLEAN)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Roof Complexity',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::ROOF_COMPLEXITY,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_INTEGER)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Roof Vents',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::ROOF_VENTS,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_INTEGER)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Roof Dormers',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::ROOF_DORMERS,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_INTEGER)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Roof Chimneys',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::ROOF_CHIMNEYS,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_INTEGER)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Roof HVAC Units',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::ROOF_HVAC_UNITS,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_INTEGER)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Roof Other Obstructions',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::ROOF_OTHER_OBSTRUCTIONS,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_INTEGER)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Requested New Roof Gutters',
                IndustryConsumerField::FIELD_KEY => RoofingConfigurableFields::ROOF_REQUESTED_NEW_GUTTERS,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_BOOLEAN)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Requested Roof Tear Off',
                IndustryConsumerField::FIELD_KEY => RoofingConfigurableFields::ROOF_REQUESTED_ROOF_TEAR_OFF,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_BOOLEAN)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Requested Roof Disposal',
                IndustryConsumerField::FIELD_KEY => RoofingConfigurableFields::ROOF_REQUESTED_ROOF_DISPOSAL,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_BOOLEAN)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Roof Estimate Low',
                IndustryConsumerField::FIELD_KEY => RoofingConfigurableFields::ROOF_ESTIMATE_LOW,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_FLOAT)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Roof Estimate Median',
                IndustryConsumerField::FIELD_KEY => RoofingConfigurableFields::ROOF_ESTIMATE_MEDIAN,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_FLOAT)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
               IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
               IndustryConsumerField::FIELD_NAME => 'Roof Estimate High',
               IndustryConsumerField::FIELD_KEY => RoofingConfigurableFields::ROOF_ESTIMATE_HIGH,
               IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_FLOAT)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Roof Replacement Area',
                IndustryConsumerField::FIELD_KEY => RoofingConfigurableFields::ROOF_REPLACEMENT_AREA,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_FLOAT)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Roof Trusted Form Cert',
                IndustryConsumerField::FIELD_KEY => RoofingConfigurableFields::ROOF_TRUSTED_FORM_CERT,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Roof Trusted Form Token',
                IndustryConsumerField::FIELD_KEY => RoofingConfigurableFields::ROOF_TRUSTED_FORM_TOKEN,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Roof Trusted Form Ping',
                IndustryConsumerField::FIELD_KEY => RoofingConfigurableFields::ROOF_TRUSTED_FORM_PING,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Property Type',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::PROPERTY_TYPE,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Project Type',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::PROJECT_TYPE,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Payment Type',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::PAYMENT_TYPE,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'IP Address',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::IP_ADDRESS,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Origin',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::ORIGIN,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Email Validated',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::EMAIL_VALIDATED,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_BOOLEAN)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Phone Validated',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::PHONE_VALIDATED,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_BOOLEAN)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Phone Validation Method',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::PHONE_VALIDATION_METHOD,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Affiliate Id',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::AFFILIATE_ID,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_INTEGER)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Campaign Id',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::CAMPAIGN_ID,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_INTEGER)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Track Code',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::TRACK_CODE,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Track Name',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::TRACK_NAME,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Track Lead Id',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::TRACK_LEAD_ID,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'TCPA Lead Id',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::TCPA_LEAD_ID,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Lead Id Service Type',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::LEAD_ID_SERVICE_TYPE,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Contact Method',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::CONTACT_METHOD,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'TCPA Disclosure Captured',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::TCPA_DISCLOSURE_CAPTURED,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_BOOLEAN)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Auto Assigned Status',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::AUTO_ASSIGNED_STATUS,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_BOOLEAN)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'UTC',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::UTC,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_INTEGER)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Comments',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::COMMENTS,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryConsumerField::FIELD_NAME => 'Comments',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::COMMENTS,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
            [
                IndustryConsumerField::FIELD_CATEGORY_ID => 1,
                IndustryConsumerField::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryConsumerField::FIELD_NAME => 'Estimate Permalink',
                IndustryConsumerField::FIELD_KEY => GlobalConfigurableFields::ESTIMATE_PERMALINK,
                IndustryConsumerField::FIELD_TYPE => ConfigurableFieldType::query()->where(ConfigurableFieldType::FIELD_TYPE, ConfigurableFieldType::TYPE_STRING)->first()?->id,
            ],
        ]);
    }
}
