<?php

namespace Database\Seeders;

use App\Models\MissedProducts\OpportunityNotification;
use App\Models\MissedProducts\OpportunityNotificationConfig;
use Illuminate\Database\Seeder;

class OpportunityNotificationsSeeder extends Seeder
{

    /**
     * Create Op Notifications and configurations
     *
     * @return void
     */
    public function run(): void
    {
        OpportunityNotificationConfig::factory(8)->create();
        OpportunityNotification::factory(8)->create();
    }

}
