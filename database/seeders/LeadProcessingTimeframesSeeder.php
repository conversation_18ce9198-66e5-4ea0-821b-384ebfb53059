<?php

namespace Database\Seeders;

use App\Models\LeadProcessingTimeframe;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LeadProcessingTimeframesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table(LeadProcessingTimeframe::TABLE)->insert([
            [
                LeadProcessingTimeframe::FIELD_ID => 1,
                LeadProcessingTimeframe::FIELD_KEY => LeadProcessingTimeframe::KEY_LESS_THAN_TWENTY_FOUR_HRS,
                LeadProcessingTimeframe::FIELD_NAME => 'Less Than 24 Hours',
                LeadProcessingTimeframe::FIELD_PRIORITY => 1
            ],
            [
                LeadProcessingTimeframe::FIELD_ID => 2,
                LeadProcessingTimeframe::FIELD_KEY => LeadProcessingTimeframe::KEY_TWENTY_FOUR_TO_FORTY_EIGHT_HRS,
                LeadProcessingTimeframe::FIELD_NAME => '24 to 48 Hours',
                LeadProcessingTimeframe::FIELD_PRIORITY => 2
            ],
            [
                LeadProcessingTimeframe::FIELD_ID => 3,
                LeadProcessingTimeframe::FIELD_KEY => LeadProcessingTimeframe::KEY_TWO_TO_SEVEN_DAYS,
                LeadProcessingTimeframe::FIELD_NAME => 'Two to Seven Days',
                LeadProcessingTimeframe::FIELD_PRIORITY => 3
            ],
            [
                LeadProcessingTimeframe::FIELD_ID => 4,
                LeadProcessingTimeframe::FIELD_KEY => LeadProcessingTimeframe::KEY_SEVEN_TO_THIRTY_DAYS,
                LeadProcessingTimeframe::FIELD_NAME => 'Seven to Thirty Days',
                LeadProcessingTimeframe::FIELD_PRIORITY => 3
            ],
            [
                LeadProcessingTimeframe::FIELD_ID => 5,
                LeadProcessingTimeframe::FIELD_KEY => LeadProcessingTimeframe::KEY_THIRTY_TO_NINETY_DAYS,
                LeadProcessingTimeframe::FIELD_NAME => 'Thirty to Ninety Days',
                LeadProcessingTimeframe::FIELD_PRIORITY => 4
            ]
        ]);
    }
}
