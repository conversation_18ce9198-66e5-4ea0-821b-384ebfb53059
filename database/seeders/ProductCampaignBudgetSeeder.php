<?php

namespace Database\Seeders;

use App\Enums\Odin\QualityTier;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ProductCampaignBudget;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProductCampaignBudgetSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $productCampaignIds = ProductCampaign::query()->pluck(ProductCampaign::FIELD_ID)->toArray();

        $factory = ProductCampaignBudget::factory();

        $insertRows = [];
        foreach($productCampaignIds as $productCampaignId) {
            $data = $factory->definition();

            $data[ProductCampaignBudget::FIELD_PRODUCT_CAMPAIGN_ID] = $productCampaignId;
            $data[ProductCampaignBudget::FIELD_QUALITY_TIER] = QualityTier::IN_HOME->value;

            $insertRows[] = $data;

            $data = $factory->definition();

            $data[ProductCampaignBudget::FIELD_PRODUCT_CAMPAIGN_ID] = $productCampaignId;
            $data[ProductCampaignBudget::FIELD_QUALITY_TIER] = QualityTier::ONLINE->value;

            $insertRows[] = $data;
        }

        DB::table(ProductCampaignBudget::TABLE)->insert($insertRows);
    }
}
