<?php

namespace Database\Seeders;

use App\Models\LeadProcessingQueueConfiguration;
use Illuminate\Database\Seeder;

class AffiliateQueueInitialSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        // Verify ping post affiliate PX does not already exist to prevent duplicate creation
        $affiliateQueue = LeadProcessingQueueConfiguration::query()->where(LeadProcessingQueueConfiguration::FIELD_NAME, 'Affiliate')->get();
        if ($affiliateQueue->count() > 0) {
            print "Affiliate Queue already exists.";
            return;
        }

        $affiliateQueue = LeadProcessingQueueConfiguration::query()->create([
            LeadProcessingQueueConfiguration::FIELD_NAME => 'Affiliate',
            LeadProcessingQueueConfiguration::FIELD_PRIMARY_STATUS => LeadProcessingQueueConfiguration::STATUS_AFFILIATE,
            LeadProcessingQueueConfiguration::FIELD_LAST_ROUND => 0,
            LeadProcessingQueueConfiguration::CREATED_AT => now(),
            LeadProcessingQueueConfiguration::UPDATED_AT => now(),
        ]);
    }
}
