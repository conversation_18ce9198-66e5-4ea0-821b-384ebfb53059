<?php

namespace Database\Seeders;

use App\Models\Prospects\Contact;
use App\Models\Prospects\NewBuyerProspect;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ProspectAndContactsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $prospect = NewBuyerProspect::factory()->sourcedFromSalesIntel()->create([
            'user_id' => 0,
            'company_id' => 0,
            'decision_maker_first_name' => null,
            'decision_maker_last_name' => null,
            'decision_maker_email' => null,
            'decision_maker_phone' => null,
        ]);

        Contact::factory(5)->for($prospect, 'prospect')->create();
    }
}
