<?php

namespace Database\Seeders;

use App\Models\Odin\Product;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class ProductSlugSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        $productSlugs = Product::query()
            ->select([
                Product::FIELD_ID,
                Product::FIELD_NAME,
            ])->get()
            ->reduce(function(array $output, Product $product) {
                $output[] = [
                    Product::FIELD_ID => $product->id,
                    Product::FIELD_SLUG => $this->slugginate($product->name),
                ];
                return $output;
            }, []);

        Product::query()
            ->upsert($productSlugs, [], [Product::FIELD_SLUG]);
    }

    /**
     * @param string $name
     * @return string
     */
    protected function slugginate(string $name): string
    {
        return Str::slug($name);
    }
}