<?php

namespace Database\Seeders;

use App\Models\Odin\Address;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class BadDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table(Address::TABLE)->insert([
            [
                Address::FIELD_ADDRESS_1 => Address::getBadDataUuid(),
                Address::FIELD_ADDRESS_2 => 'BAD DATA',
                Address::FIELD_CITY => 'Redding',
                Address::FIELD_STATE => 'CA',
                Address::FIELD_ZIP_CODE => '96003',
                Address::FIELD_COUNTRY => 'US',
                Address::FIELD_LATITUDE => '40.60',
                Address::FIELD_LONGITUDE => '-122.35',
            ]
        ]);
    }
}
