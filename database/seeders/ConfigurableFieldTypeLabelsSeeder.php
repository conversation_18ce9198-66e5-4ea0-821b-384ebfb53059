<?php

namespace Database\Seeders;

use App\Models\Odin\ConfigurableFieldType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ConfigurableFieldTypeLabelsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        $fieldTypes = [
            "Integer"   => "Number",
            "Float"     => "Decimal Number (Currency)",
            "String"    => "Text",
            "Boolean"   => "Yes/No",
            "Array"     => "Group of values (Array)"
        ];
        foreach ($fieldTypes as $fieldTypeKey => $fieldTypeFriendlyName) {
            ConfigurableFieldType::where(ConfigurableFieldType::FIELD_TYPE, '=', $fieldTypeKey)->update([ConfigurableFieldType::FIELD_LABEL => $fieldTypeFriendlyName]);
        }
    }
}
