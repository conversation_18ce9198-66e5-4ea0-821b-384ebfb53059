<?php

namespace Database\Seeders;

use App\Models\LeadProcessingQueueConstraint;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LeadProcessingQueueConstraintsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table(LeadProcessingQueueConstraint::TABLE)->insert([
            //Initial
            [
                LeadProcessingQueueConstraint::FIELD_QUEUE_ID => 1,
                LeadProcessingQueueConstraint::FIELD_ORDER => 1,
                LeadProcessingQueueConstraint::FIELD_CONSTRAINT_ID => 2
            ],
            [
                LeadProcessingQueueConstraint::FIELD_QUEUE_ID => 1,
                LeadProcessingQueueConstraint::FIELD_ORDER => 2,
                LeadProcessingQueueConstraint::FIELD_CONSTRAINT_ID => 3
            ],

            //Pending Review
            [
                LeadProcessingQueueConstraint::FIELD_QUEUE_ID => 2,
                LeadProcessingQueueConstraint::FIELD_ORDER => 1,
                LeadProcessingQueueConstraint::FIELD_CONSTRAINT_ID => 8
            ],
            [
                LeadProcessingQueueConstraint::FIELD_QUEUE_ID => 2,
                LeadProcessingQueueConstraint::FIELD_ORDER => 2,
                LeadProcessingQueueConstraint::FIELD_CONSTRAINT_ID => 2
            ],
            [
                LeadProcessingQueueConstraint::FIELD_QUEUE_ID => 2,
                LeadProcessingQueueConstraint::FIELD_ORDER => 3,
                LeadProcessingQueueConstraint::FIELD_CONSTRAINT_ID => 3
            ],

            //Pending Review - Best Revenue Scenario
            [
                LeadProcessingQueueConstraint::FIELD_QUEUE_ID => 3,
                LeadProcessingQueueConstraint::FIELD_ORDER => 1,
                LeadProcessingQueueConstraint::FIELD_CONSTRAINT_ID => 8
            ],
            [
                LeadProcessingQueueConstraint::FIELD_QUEUE_ID => 3,
                LeadProcessingQueueConstraint::FIELD_ORDER => 2,
                LeadProcessingQueueConstraint::FIELD_CONSTRAINT_ID => 1
            ],
            [
                LeadProcessingQueueConstraint::FIELD_QUEUE_ID => 3,
                LeadProcessingQueueConstraint::FIELD_ORDER => 3,
                LeadProcessingQueueConstraint::FIELD_CONSTRAINT_ID => 2
            ],

            //Under Review
            [
                LeadProcessingQueueConstraint::FIELD_QUEUE_ID => 4,
                LeadProcessingQueueConstraint::FIELD_ORDER => 1,
                LeadProcessingQueueConstraint::FIELD_CONSTRAINT_ID => 9
            ],
            [
                LeadProcessingQueueConstraint::FIELD_QUEUE_ID => 4,
                LeadProcessingQueueConstraint::FIELD_ORDER => 2,
                LeadProcessingQueueConstraint::FIELD_CONSTRAINT_ID => 7
            ],
            [
                LeadProcessingQueueConstraint::FIELD_QUEUE_ID => 4,
                LeadProcessingQueueConstraint::FIELD_ORDER => 3,
                LeadProcessingQueueConstraint::FIELD_CONSTRAINT_ID => 8
            ],
            [
                LeadProcessingQueueConstraint::FIELD_QUEUE_ID => 4,
                LeadProcessingQueueConstraint::FIELD_ORDER => 4,
                LeadProcessingQueueConstraint::FIELD_CONSTRAINT_ID => 5
            ],
            [
                LeadProcessingQueueConstraint::FIELD_QUEUE_ID => 4,
                LeadProcessingQueueConstraint::FIELD_ORDER => 5,
                LeadProcessingQueueConstraint::FIELD_CONSTRAINT_ID => 1
            ],

            //Under Review - Super Premium
            [
                LeadProcessingQueueConstraint::FIELD_QUEUE_ID => 5,
                LeadProcessingQueueConstraint::FIELD_ORDER => 1,
                LeadProcessingQueueConstraint::FIELD_CONSTRAINT_ID => 9
            ],
            [
                LeadProcessingQueueConstraint::FIELD_QUEUE_ID => 5,
                LeadProcessingQueueConstraint::FIELD_ORDER => 2,
                LeadProcessingQueueConstraint::FIELD_CONSTRAINT_ID => 6
            ],
            [
                LeadProcessingQueueConstraint::FIELD_QUEUE_ID => 5,
                LeadProcessingQueueConstraint::FIELD_ORDER => 3,
                LeadProcessingQueueConstraint::FIELD_CONSTRAINT_ID => 7
            ],
            [
                LeadProcessingQueueConstraint::FIELD_QUEUE_ID => 5,
                LeadProcessingQueueConstraint::FIELD_ORDER => 4,
                LeadProcessingQueueConstraint::FIELD_CONSTRAINT_ID => 8
            ],
            [
                LeadProcessingQueueConstraint::FIELD_QUEUE_ID => 5,
                LeadProcessingQueueConstraint::FIELD_ORDER => 5,
                LeadProcessingQueueConstraint::FIELD_CONSTRAINT_ID => 1
            ],
            [
                LeadProcessingQueueConstraint::FIELD_QUEUE_ID => 5,
                LeadProcessingQueueConstraint::FIELD_ORDER => 6,
                LeadProcessingQueueConstraint::FIELD_CONSTRAINT_ID => 5
            ],
        ]);
    }
}
