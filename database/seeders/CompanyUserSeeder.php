<?php

namespace Database\Seeders;

use App\Models\Odin\CompanyUser;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CompanyUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        CompanyUser::factory(4)
            ->sequence(function($sequence) {
                return [
                    CompanyUser::FIELD_COMPANY_ID => $sequence->index + 1
                ];
            })
            ->create();

        CompanyUser::factory(4)
            ->sequence(function($sequence) {
                return [
                    CompanyUser::FIELD_COMPANY_ID => $sequence->index + 1
                ];
            })
            ->contact()
            ->create();
    }
}
