<?php

namespace Database\Seeders;

use App\DataModels\Odin\ConfigurableFieldDataModel;
use App\Enums\GlobalConfigurationContactField;
use App\Enums\GlobalConfigurationKey;
use App\Models\GlobalConfiguration;
use Illuminate\Database\Seeder;

class GlobalConfigurationSeeder extends Seeder
{
    public function run(): void
    {
        $solarreviewsDetails = [
            GlobalConfigurationContactField::EMAIL->value     => '<EMAIL>',
            GlobalConfigurationContactField::PHONE->value     => '+****************',
            GlobalConfigurationContactField::ADDRESS_1->value => '1401 17th Street',
            GlobalConfigurationContactField::ADDRESS_2->value => 'Suite 525 Denver. CO. 80202',
            GlobalConfigurationContactField::WEBSITE->value   => 'solarreviews.com',
            GlobalConfigurationContactField::NAME->value      => 'SolarReviews',
        ];

        $fixrDetails = [
            GlobalConfigurationContactField::EMAIL->value     => '<EMAIL>',
            GlobalConfigurationContactField::PHONE->value     => '+****************',
            GlobalConfigurationContactField::ADDRESS_1->value => '1401 17th Street',
            GlobalConfigurationContactField::ADDRESS_2->value => 'Suite 525 Denver. CO. 80202',
            GlobalConfigurationContactField::WEBSITE->value   => 'fixr.com',
            GlobalConfigurationContactField::NAME->value      => 'Fixr',
        ];

        $roofingCalculatorDetails = [
            GlobalConfigurationContactField::EMAIL->value     => '<EMAIL>',
            GlobalConfigurationContactField::PHONE->value     => '+****************',
            GlobalConfigurationContactField::ADDRESS_1->value => '1401 17th Street',
            GlobalConfigurationContactField::ADDRESS_2->value => 'Suite 525 Denver. CO. 80202',
            GlobalConfigurationContactField::WEBSITE->value   => 'roofingcalculator.com',
            GlobalConfigurationContactField::NAME->value      => 'Roofing Calculator',
        ];

        $defaultPricing = [
            "Lead" => [
                "Premium" => [
                    "Duo" => ["price" => "150",],
                    "Trio" => ["price" => "100",],
                    "Exclusive" => ["price" => "180",],
                    "Email Only" => ["price" => "5",],
                    "Unverified" => ["price" => "15",],
                ],
                "Standard" => [
                    "Duo" => ["price" => "100",],
                    "Trio" => ["price" => "80",],
                    "Exclusive" => ["price" => "150",],
                    "Email Only" => ["price" => "5",],
                    "Unverified" => ["price" => "15",],
                ],
            ],
            "Appointment" => [
                "Online" => [
                    "Duo" => ["price" => "150",],
                    "Trio" => ["price" => "100",],
                    "Exclusive" => ["price" => "300",],
                ],
                "In-Home" => [
                    "Duo" => ["price" => "200",],
                    "Trio" => ["price" => "150",],
                    "Exclusive" => ["price" => "450",],
                ],
            ],
        ];


        $this->createOrMergeConfiguration(GlobalConfigurationKey::SOLARREVIEWS, $solarreviewsDetails);
        $this->createOrMergeConfiguration(GlobalConfigurationKey::FIXR, $fixrDetails);
        $this->createOrMergeConfiguration(GlobalConfigurationKey::ROOFING_CALCULATOR, $roofingCalculatorDetails);
        $this->createOrMergeConfiguration(GlobalConfigurationKey::TEST_PRODUCTS_EXPIRATION_GAP_IN_DAYS, [
            'days' => 14
        ]);
        $this->createOrMergeConfiguration(GlobalConfigurationKey::DEFAULT_PRICING, $defaultPricing);
    }

    /**
     * @param GlobalConfigurationKey $configurationKey
     * @param array $dataPayload
     * @return void
     */
    private function createOrMergeConfiguration(GlobalConfigurationKey $configurationKey, array $dataPayload): void
    {
        $existingConfiguration = GlobalConfiguration::query()
            ->where(GlobalConfiguration::FIELD_CONFIGURATION_KEY, $configurationKey->value)
            ->first();

        $existingPayload = $existingConfiguration
            ?->{GlobalConfiguration::FIELD_CONFIGURATION_PAYLOAD}
            ->toArray()['data']
            ?? [];

        $mergedPayload = ConfigurableFieldDataModel::fromArray([
            ...$dataPayload,
            ...$existingPayload
        ]);

        if ($existingConfiguration) {
            $existingConfiguration->update([GlobalConfiguration::FIELD_CONFIGURATION_PAYLOAD => $mergedPayload]);
        }
        else {
            GlobalConfiguration::query()->create([
                GlobalConfiguration::FIELD_CONFIGURATION_KEY     => $configurationKey->value,
                GlobalConfiguration::FIELD_CONFIGURATION_PAYLOAD => $mergedPayload,
                GlobalConfiguration::FIELD_CREATED_BY_ID         => 1
            ]);
        }
    }
}
