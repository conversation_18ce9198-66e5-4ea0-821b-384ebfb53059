<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\App;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        // only run these seeders if environment is NOT production
        if(!App::environment('production')) {
            $this->call([
                // Create permissions and test users
                PermissionsSeeder::class,
                SalesRolesSeeder::class,
                TestUsersSeeder::class,
                ClientTokenServiceSeeder::class,
                LeadProcessingQueueConfigurationsSeeder::class,
                LeadProcessingConstraintsSeeder::class,
                LeadProcessingQueueConstraintsSeeder::class,
                LeadProcessingBudgetStatusesSeeder::class,
                LeadProcessingTimeframesSeeder::class,
                LeadProcessingTimeZoneConfigurationsSeeder::class,
                LeadProcessingCallingTimeZoneConfigurationsSeeder::class,
                LeadProcessingConfigurationsSeeder::class,
                LeadProcessingTeamsSeeder::class,
                LeadProcessorsSeeder::class,
                TimeframeContactBuffersSeeder::class,
                ContactSubscriptionsSeeder::class,
                SalesBaitLeadSeeder::class,
                TaskCategorySeeder::class,
                TeamSeeder::class,

                // Create "bad data" for migration failures
                BadDataSeeder::class,

                LocationsSeeder::class,

                //Odin Models
                AddressesSeeder::class,
                ProductsSeeder::class,
                IndustriesSeeder::class,
                IndustryServicesSeeder::class,
                ServiceProductsSeeder::class,
                WebsitesSeeder::class,
                IndustryWebsitesSeeder::class,
                ConfigurableFieldTypesSeeder::class,
                ConsumerConfigurableFieldCategoriesSeeder::class,
                IndustryConsumerFieldsSeeder::class,
                GlobalCompanyFieldsSeeder::class,
                IndustryCompanyFieldsSeeder::class,
                GlobalTypesSeeder::class,
                SaleTypesSeeder::class,
                TestWebsiteApiKeySeeder::class,
                TaskSeeder::class,
                WorkflowEventsSeeder::class,
                WorkflowSeeder::class,
                WorkflowActionsSeeder::class,
                SalesBaitManagementSeeder::class,
                CompaniesSeeder::class,
                ConsumerSeeder::class,
                ConsumerProductSeeder::class,
                ProductAssignmentsSeeder::class,
                PropertyTypesSeeder::class,
                QualityTiersSeeder::class,
                CompanyUserSeeder::class,
                ProductCampaignSeeder::class,
                ProductCampaignBudgetSeeder::class,
                AppointmentOfferingsSeeder::class,
                RulesetsSeeder::class,
                CompanyQualityScoreGlobalConfigurationSeeder::class,
                OpportunityNotificationsSeeder::class,
                CompanyConfigurationsSeeder::class,
                GlobalConfigurationSeeder::class,
                ContractKeysSeeder::class,
                CompanyConfigurationsSeeder::class,
                IndustryConfigurationSeeder::class,
                LeadProcessingCommunicationSeeder::class,
                BundleSeeder::class,

                //seeder for Direct Leads product
                DirectLeadsProductSeeder::class,

                //seeder for CompanyCampaignCRMDeliveryLog permission
                CompanyCampaignDeliveryLogPermissionSeeder::class,
            ]);
        }
    }
}
