<?php

namespace Database\Seeders;

use App\Models\Odin\ConsumerConfigurableFieldCategory;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use App\Enums\Odin\ConsumerConfigurableFieldCategory as ConsumerConfigurableFieldCategoryEnum;
use DateTime;
use Illuminate\Support\Facades\DB;

class ConsumerConfigurableFieldCategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $categoriesSlugs = ConsumerConfigurableFieldCategoryEnum::allCategoriesSlugs();

        foreach ($categoriesSlugs as $categorySlug) {

            $found = DB::table(ConsumerConfigurableFieldCategory::TABLE)
                ->where(ConsumerConfigurableFieldCategory::FIELD_SLUG, $categorySlug)
                ->first();


            if (!$found) ConsumerConfigurableFieldCategory::query()->insert(
                [
                    ConsumerConfigurableFieldCategory::FIELD_NAME => ConsumerConfigurableFieldCategoryEnum::getCategoryName($categorySlug),
                    ConsumerConfigurableFieldCategory::FIELD_SLUG => $categorySlug,
                    ConsumerConfigurableFieldCategory::FIELD_CREATED_AT => Carbon::now()->format('Y-m-d H:i:s')
                ]
            );
        }
    }
}
