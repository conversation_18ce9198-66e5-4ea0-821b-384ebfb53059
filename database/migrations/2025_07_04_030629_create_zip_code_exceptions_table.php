<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('company_zip_code_exceptions', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->foreignId('county_location_id')->constrained('us_locations');
            $table->foreignId('company_id')->index()->constrained()->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('company_zip_code_exceptions');
    }
};
