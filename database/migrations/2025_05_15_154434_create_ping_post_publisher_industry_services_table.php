<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ping_post_publisher_industry_services', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('ping_post_publisher_id');
            $table->unsignedBigInteger('industry_service_id');
            $table->string('key')->nullable();
            $table->boolean('active')->default(true);
            $table->json('data')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ping_post_publisher_industry_services');
    }
};
