<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            $table->dateTime('issue_at');
            $table->dateTime('due_at');
        });
        Schema::table('invoice_items', function (Blueprint $table) {
            $table->unsignedBigInteger('added_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            $table->dropColumn('issue_at');
            $table->dropColumn('due_at');
        });
        Schema::table('invoice_items', function (Blueprint $table) {
            $table->dropColumn('added_by');
        });
    }
};
