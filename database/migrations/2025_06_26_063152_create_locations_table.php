<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('us_locations', function (Blueprint $table) {
            $table->id();
            $table->unsignedTinyInteger('type')->index();
            $table->unsignedBigInteger('parent_location_id')->nullable()->index();
            $table->string('state');
            $table->string('state_abbr');
            $table->string('state_key');
            $table->string('county')->nullable();
            $table->string('county_key')->nullable();
            $table->string('city')->nullable();
            $table->string('city_key')->nullable();
            $table->string('zip_code')->nullable()->index();
            // Copied compound string indexing from legacy
            // We should replace these with type/parent_location_id index
            //   and write new methods for finding child locations by those columns
            $table->index(['type', 'state_abbr']);
            $table->index(['type', 'county_key', 'state_abbr']);
            $table->index(['type', 'city_key', 'state_abbr']);
            $table->index(['type', 'zip_code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::disableForeignKeyConstraints();
        Schema::dropIfExists('us_locations');
        Schema::enableForeignKeyConstraints();
    }
};
