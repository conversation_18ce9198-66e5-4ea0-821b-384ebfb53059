<?php

use Illuminate\Database\Migrations\Migration;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        //todo: move to seeder
//        $admin = Role::findOrCreate('admin');
//
//        $admin->givePermissionTo(Permission::findOrCreate('consumer-search'));
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Permission::findByName('consumer-search')?->delete();
    }
};
