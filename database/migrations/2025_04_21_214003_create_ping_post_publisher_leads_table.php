<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ping_post_publisher_leads', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('consumer_product_id');
            $table->unsignedBigInteger('product_assignment_id');
            $table->unsignedBigInteger('ping_post_publisher_id');
            $table->integer('status');
            $table->float('cost');
            $table->json('data')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ping_post_publisher_leads');
    }
};
