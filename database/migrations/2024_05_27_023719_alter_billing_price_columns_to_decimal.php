<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('company_invoices', function (Blueprint $table) {
            $table->decimal('total', 10)->change();
        });

        Schema::table('credits', function (Blueprint $table) {
            $table->decimal('initial_value', 10)->change();
            $table->decimal('remaining_value', 10)->change();
        });

        Schema::table('invoice_items', function (Blueprint $table) {
            $table->decimal('unit_price', 10)->change();
            $table->decimal('tax', 10)->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('company_invoices', function (Blueprint $table) {
            $table->integer('total')->change();
        });

        Schema::table('credits', function (Blueprint $table) {
            $table->double('initial_value', 8, 2)->change();
            $table->double('remaining_value', 8, 2)->change();
        });

        Schema::table('invoice_items', function (Blueprint $table) {
            $table->integer('unit_price')->change();
            $table->double('tax', 8, 2)->change();
        });
    }
};
