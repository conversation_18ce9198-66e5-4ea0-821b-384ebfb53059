<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice_refunds', function (Blueprint $table) {
            $table->dropForeign('invoice_refunds_transaction_id_foreign');
            $table->dropColumn('transaction_id'); //now refund_transaction_id in invoice_refund_charges
            $table->dropColumn('external_charge_id'); //now refunded_payment_id in invoice_refund_charges
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_refunds', function (Blueprint $table) {
            $table->unsignedBigInteger('transaction_id')->nullable();
            $table->string('external_charge_id');
            $table->foreign('transaction_id')->references('id')->on('invoice_transactions');
        });
    }
};
