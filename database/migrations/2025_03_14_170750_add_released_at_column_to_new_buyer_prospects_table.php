<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('new_buyer_prospects', function (Blueprint $table) {
            $table->datetime('released_at')->nullable()->after('updated_at');
            $table->unsignedBigInteger('released_by_user_id')->nullable()->after('released_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('new_buyer_prospects', function (Blueprint $table) {
            $table->dropColumn('released_at');
            $table->dropColumn('released_by_user_id');
        });
    }
};
