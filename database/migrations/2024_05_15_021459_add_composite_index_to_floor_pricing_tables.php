<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_state_floor_prices', function (Blueprint $table) {
            $table->unique(['service_product_id', 'state_location_id', 'property_type_id', 'quality_tier_id',  'sale_type_id'], 'unique_floor_price_index');
        });

        Schema::table('product_county_floor_prices', function (Blueprint $table) {
            $table->unique([ 'service_product_id', 'county_location_id', 'property_type_id', 'quality_tier_id', 'sale_type_id'], 'unique_floor_price_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_county_floor_prices', function (Blueprint $table) {
            $table->dropUnique('unique_floor_price_index');
        });

        Schema::table('product_state_floor_prices', function (Blueprint $table) {
            $table->dropUnique('unique_floor_price_index');
        });
    }
};
