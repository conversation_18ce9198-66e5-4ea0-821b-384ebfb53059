<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tiered_advertising_counties', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('industry_id');
            $table->unsignedBigInteger('location_id');
            $table->string('platform');
            $table->unsignedBigInteger('tiered_advertising_campaign_id')->nullable();
            $table->unsignedInteger('tier')->nullable();
            $table->float('tcpa_bid');
            $table->json('negative_zip_codes');
            $table->json('data')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tiered_advertising_counties');
    }
};
