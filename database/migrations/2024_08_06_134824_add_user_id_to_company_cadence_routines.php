<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('company_cadence_routines', function (Blueprint $table) {
            $table->unsignedBigInteger('user_id')->nullable();
            $table->boolean('use_account_manager')->default(true);
        });
    }

    public function down(): void
    {
        Schema::table('company_cadence_routines', function (Blueprint $table) {
            $table->dropColumn('user_id');
            $table->dropColumn('use_account_manager');
        });
    }
};
