<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_configurations', function (Blueprint $table) {
            $table->boolean('pause_campaign_on_threshold_exceeded')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_configurations', function (Blueprint $table) {
            $table->dropColumn('pause_campaign_on_threshold_exceeded');
        });
    }
};
