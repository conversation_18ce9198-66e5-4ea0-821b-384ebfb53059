<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('recycled_leads', function (Blueprint $table) {
            $table->dropColumn('contacted_at');
            $table->dropColumn('opted_out');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('recycled_leads', function (Blueprint $table) {
            $table->timestamp('contacted_at')->nullable();
            $table->boolean('opted_out')->nullable();
        });
    }
};
