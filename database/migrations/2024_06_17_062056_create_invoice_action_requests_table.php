<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_action_requests', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('invoice_id')->nullable();
            $table->unsignedBigInteger('requested_by');
            $table->string('requested_action');
            $table->json('payload')->nullable();
            $table->unsignedBigInteger('reviewed_by')->nullable();
            $table->string('status')->nullable();
            $table->string('reason')->nullable();
            $table->dateTime('reviewed_at')->nullable();

            $table->foreign('invoice_id')->references('id')->on('invoices')->noActionOnDelete();
            $table->foreign('requested_by')->references('id')->on('users')->noActionOnDelete();
            $table->foreign('reviewed_by')->references('id')->on('users')->noActionOnDelete();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_action_requests');
    }
};
