<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('recycled_leads', function (Blueprint $table) {
            $table->smallInteger('status')->nullable()->default(null)->index();
            $table->index('created_at', 'recycled_leads_create_at_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('recycled_leads', function (Blueprint $table) {
            $table->dropColumn('status');
            $table->dropIndex('recycled_leads_create_at_index');
        });
    }
};
