<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('company_profile_locations', function (Blueprint $table) {
            $table->id();
            $table->string('raw');
            $table->foreignId('company_profile_id')->constrained()->cascadeOnDelete();
            $table->unsignedInteger('location_id');
            $table->index('location_id', 'location_id_index');
            $table->timestamps();
            $table->unique(['company_profile_id', 'location_id'], 'company_profile_location_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('company_profile_locations');
    }
};
