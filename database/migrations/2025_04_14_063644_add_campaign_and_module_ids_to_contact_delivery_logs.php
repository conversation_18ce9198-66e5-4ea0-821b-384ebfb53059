<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_campaign_contact_delivery_logs', function (Blueprint $table) {
            $table->unsignedBigInteger('campaign_id')->after('id')->index();
            $table->unsignedBigInteger('module_id')->after('campaign_id');
            $table->renameColumn('contact_module_id', 'contact_module_contact_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_campaign_contact_delivery_logs', function (Blueprint $table) {
            $table->dropColumn('campaign_id');
            $table->dropColumn('module_id');
            $table->renameColumn('contact_module_contact_id', 'contact_module_id');
        });
    }
};
