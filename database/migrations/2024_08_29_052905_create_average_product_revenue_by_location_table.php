<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('average_product_revenue_by_locations', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->unsignedBigInteger('county_location_id');
            $table->unsignedBigInteger('zip_code_location_id')->nullable();
            $table->unsignedBigInteger('industry_id');
            $table->unsignedBigInteger('product_id');
            $table->float('average_revenue');
            $table->unique(['industry_id', 'product_id', 'county_location_id'], 'county_industry_product_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('average_product_revenue_by_locations');
    }
};
