<?php

use App\Models\Prospects\NewBuyerProspect;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('prospect_contacts', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(NewBuyerProspect::class, 'prospect_id')->constrained()->onDelete('cascade');
            $table->string('first_name');
            $table->string('last_name');
            $table->string('email');
            $table->string('title')->nullable();
            $table->string('department')->nullable();
            $table->string('cell_phone')->nullable();
            $table->string('office_phone')->nullable();
            $table->timestamps();
        });
    }
};
