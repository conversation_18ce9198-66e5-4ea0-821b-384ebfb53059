<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('new_buyer_prospects', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('company_id');
            $table->enum('status', ['initial', 'active', 'closed'])->default('initial');
            $table->enum('resolution', ['decision_maker_identified', 'demo_booked', 'cancelled'])->nullable();
            $table->json('industry_service_ids');
            $table->enum('source', ['alist'])->default('alist');
            $table->json('source_data');
            $table->string('company_name');
            $table->string('company_website')->nullable();
            $table->string('address_street')->nullable();
            $table->string('address_city_key')->nullable();
            $table->string('address_state_abbr')->nullable();
            $table->string('decision_maker_first_name')->nullable();
            $table->string('decision_maker_last_name')->nullable();
            $table->string('decision_maker_email')->nullable();
            $table->string('notes')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('new_buyer_prospects');
    }
};
