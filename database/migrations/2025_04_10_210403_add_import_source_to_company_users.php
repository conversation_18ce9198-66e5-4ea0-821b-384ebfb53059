<?php

use App\Models\Odin\CompanyUser;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('company_users', function (Blueprint $table) {
            $table->enum('import_source', [
                CompanyUser::IMPORT_SOURCE_MANUAL,
                CompanyUser::IMPORT_SOURCE_SALES_INTEL,
                CompanyUser::IMPORT_SOURCE_ZOOM,
            ])->default(CompanyUser::IMPORT_SOURCE_MANUAL);
        });
    }
};
