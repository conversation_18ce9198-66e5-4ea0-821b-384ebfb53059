<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::drop('affiliate_users');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::create('affiliate_users', function (Blueprint $table) {
            $table->id();

            $table->foreignId('affiliate_id');
            $table->string('name');
            $table->string('email');
            $table->string('password');

            $table->timestamps();
        });
    }
};
