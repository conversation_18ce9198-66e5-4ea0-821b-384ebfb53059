<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_campaign_delivery_module_crms', function (Blueprint $table) {
            $table->unsignedBigInteger('template_id')->nullable()->default(null);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_campaign_delivery_module_crms', function (Blueprint $table) {
            $table->dropColumn('template_id');
        });
    }
};
