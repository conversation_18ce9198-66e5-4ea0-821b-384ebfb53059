<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('marketing_campaign_consumers', function (Blueprint $table) {
            $table->id();
            $table->uuid('external_reference');
            $table->uuid('marketing_campaign_reference');
            $table->dateTime('revalidated_at')->nullable();
            $table->uuid('consumer_reference');
            $table->unsignedBigInteger('marketing_campaign_id');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('marketing_campaign_consumers');
    }
};
