<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('conferences', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('calendar_event_id');
            $table->unsignedBigInteger('user_id');
            $table->string('external_id');
            $table->timestamp('start_time')->nullable();
            $table->timestamp('end_time')->nullable();
            $table->timestamp('expire_time')->nullable();
            $table->integer('duration_in_seconds')->nullable()->default(0);
            $table->string('status')->nullable();

            $table->foreign('calendar_event_id')->references('id')->on('calendar_events')->noActionOnDelete();
            $table->foreign('user_id')->references('id')->on('users')->noActionOnDelete();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('conferences');
    }
};
