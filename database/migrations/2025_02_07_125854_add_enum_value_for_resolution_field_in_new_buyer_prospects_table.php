<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('new_buyer_prospects', function (Blueprint $table) {
            $table->enum('resolution', ['decision_maker_identified', 'demo_booked', 'cancelled', 'duplicate_company', 'registration_complete'])->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('new_buyer_prospects', function (Blueprint $table) {
            $table->enum('resolution', ['decision_maker_identified', 'demo_booked', 'cancelled', 'duplicate_company'])->nullable()->change();
        });
    }
};
