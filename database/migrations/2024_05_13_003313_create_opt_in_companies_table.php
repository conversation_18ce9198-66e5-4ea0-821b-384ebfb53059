<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('opt_in_companies', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('consumer_product_id');
            $table->foreign('consumer_product_id', 'consumer_products_foreign_key')->references('id')->on('consumer_products');
            $table->unsignedBigInteger('company_id');
            $table->foreign('company_id', 'companies_foreign_key')->references('id')->on('companies');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('opt_in_companies');
    }
};
