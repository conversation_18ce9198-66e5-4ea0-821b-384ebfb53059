<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice_snapshots', function (Blueprint $table) {
            $table->bigInteger('relationship_manager_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_snapshots', function (Blueprint $table) {
            $table->dropColumn('relationship_manager_id');
        });
    }
};
