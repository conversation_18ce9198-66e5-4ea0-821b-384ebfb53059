<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('company_profile_reviews', function (Blueprint $table) {
            $table->id();
            $table->string('text');
            $table->float('rating');
            $table->dateTime('date')->nullable();
            $table->string('author');
            $table->integer('helpful');
            $table->json('additional_ratings')->nullable();
            $table->string('source');
            $table->foreignId('company_profile_id')->constrained()->cascadeOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('company_profile_reviews');
    }
};
