<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('company_manager_assignment_requests', static function (Blueprint $table) {
            $table->foreignId('deciding_user_id')->after('role_id')->nullable()->constrained('users')->restrictOnDelete();
        });

        Schema::table('company_manager_assignment_requests', static function (Blueprint $table) {
            $table->dateTime('decided_at')->after('deciding_user_id')->nullable();
        });
    }
};
