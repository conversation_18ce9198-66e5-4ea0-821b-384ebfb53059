<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice_action_requests', function (Blueprint $table) {
            $table->rename('action_approvals');
        });

        Schema::table('action_approvals', function (Blueprint $table) {
            $table->dropForeign('invoice_action_requests_invoice_id_foreign');
            $table->dropColumn('invoice_id');
            $table->morphs('approvable');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('action_approvals', function (Blueprint $table) {
            $table->dropMorphs('approvable');
        });

        Schema::table('action_approvals', function (Blueprint $table) {
            $table->rename('invoice_action_requests');
        });

        Schema::table('invoice_action_requests', function (Blueprint $table) {
            $table->unsignedBigInteger('invoice_id')->nullable();
            $table->foreign('invoice_id')->references('id')->on('invoices')->noActionOnDelete();
        });
    }
};
