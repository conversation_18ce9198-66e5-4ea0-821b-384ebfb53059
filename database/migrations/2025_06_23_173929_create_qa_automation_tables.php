<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('qa_automation_industry_services', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('industry_service_id');
            $table->integer('type')->default(1);
            $table->boolean('enabled')->default(true);
            $table->timestamps();
        });

        Schema::create('qa_automation_rules', function (Blueprint $table) {
            $table->id();
            $table->integer('type')->default(1);
            $table->string('expression');
            $table->json('fields');
            $table->boolean('match_success');
            $table->boolean('enabled')->default(true);
            $table->json('data')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('qa_automation_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('consumer_product_id');
            $table->unsignedBigInteger('qa_automation_rule_id')->nullable();
            $table->string('entry');
            $table->string('error_message')->nullable();
            $table->integer('log_level')->default(1);
            $table->json('data')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('qa_automation_industry_services');
        Schema::dropIfExists('qa_automation_rules');
        Schema::dropIfExists('qa_automation_logs');
    }
};
