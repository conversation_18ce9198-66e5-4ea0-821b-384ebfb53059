<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('estimated_revenue_per_lead_by_locations', function (Blueprint $table) {
            $table->dropColumn('future_available_no_limit_campaigns');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('estimated_revenue_per_lead_by_locations', function (Blueprint $table) {
            $table->unsignedMediumInteger('future_available_no_limit_campaigns')->nullable();
        });
    }
};
