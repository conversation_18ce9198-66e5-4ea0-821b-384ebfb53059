<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('privacy_request_redacted_records', function (Blueprint $table) {
            $table->id();
            $table->nullableMorphs('model', 'model');
            $table->string('website');
            $table->string('status');
            $table->unsignedBigInteger('initiator_id');
            $table->unsignedBigInteger('privacy_request_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('privacy_request_redacted_records');
    }
};
