<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice_snapshots', function (Blueprint $table) {
            $table->decimal('total_collections');
            $table->decimal('total_collections_recovered');
            $table->decimal('total_collections_lost');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_snapshots', function (Blueprint $table) {
            $table->dropColumn('total_collections');
            $table->dropColumn('total_collections_recovered');
        });
    }
};
