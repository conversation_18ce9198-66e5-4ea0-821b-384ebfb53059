<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('review_replies', function (Blueprint $table) {
            $table->string('custom_admin_name')->nullable()->after('admin_user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('review_replies', function (Blueprint $table) {
            $table->dropColumn('custom_admin_name');
        });
    }
};
