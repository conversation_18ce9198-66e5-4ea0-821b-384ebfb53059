<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('credit_types', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('description')->nullable();
            $table->string('line_item_text');
            $table->integer('expires_in_days')->nullable();
            $table->integer('consumption_order');
            $table->boolean('invoiceable')->default(true); //if we, in admin 2 can add them to an invoice for a company.
            $table->boolean('cash');
            $table->boolean('active'); //if the company that purchases the credit can use them.
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('credit_types');
    }
};
