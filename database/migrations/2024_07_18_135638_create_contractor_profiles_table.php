<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    public function up(): void
    {
        Schema::create('contractor_profiles', function (Blueprint $table) {
            $table->id();

            $table->unsignedInteger('company_id');
            $table->string('introduction', length: 500)->nullable();
            $table->string('customers_like', length: 500)->nullable();
            $table->string('customers_dislike', length: 500)->nullable();
            $table->float('rating')->nullable();
            $table->boolean('status')->default(0);
            $table->boolean('force_display')->default(0);

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('contractor_profiles');
    }
};
