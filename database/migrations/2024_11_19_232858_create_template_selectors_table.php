<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('template_selectors', function (Blueprint $table) {
            $table->id();
            $table->string('relation', 50);
            $table->string('purpose_key', 50);
            $table->morphs('template');
            $table->unsignedBigInteger('industry_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('template_selectors');
    }
};
