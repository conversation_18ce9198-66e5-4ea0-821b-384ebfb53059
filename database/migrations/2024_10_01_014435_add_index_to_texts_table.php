<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('texts', function (Blueprint $table) {
            $table->string('other_number', 20)->change();
            $table->index('other_number'); // Add an index to the column
        });

        Schema::table('calls', function (Blueprint $table) {
            $table->string('external_reference', 50)->change();
            $table->index('external_reference');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('texts', function (Blueprint $table) {
            $table->dropIndex(['other_number']);
            $table->string('other_number')->change();

        });

        Schema::table('calls', function (Blueprint $table) {
            $table->dropIndex(['external_reference']);
            $table->string('external_reference')->change();
        });
    }
};
