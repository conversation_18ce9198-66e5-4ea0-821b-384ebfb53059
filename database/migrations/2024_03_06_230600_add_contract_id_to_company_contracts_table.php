<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('company_contracts', function (Blueprint $table) {
            $table->unsignedBigInteger('contract_id')->nullable();
            $table->string('signature_id')->nullable();
            $table->string('file_url')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('company_contracts', function (Blueprint $table) {
            $table->dropColumn('contract_id');
            $table->dropColumn('signature_id');
            $table->dropColumn('file_url');
        });
    }
};
