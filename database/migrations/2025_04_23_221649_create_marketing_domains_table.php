<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('marketing_domains', function (Blueprint $table) {
            $table->id();
            $table->string('status');
            $table->string('domain');
            $table->timestamps();

            $table->unique('domain');
        });

        Schema::table('marketing_campaign_consumers', function (Blueprint $table) {
            $table->unsignedBigInteger('sent_from_domain_id')->nullable();
            $table->foreign('sent_from_domain_id')->references('id')->on('marketing_domains');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('marketing_campaign_consumers', function (Blueprint $table) {
            $table->dropForeign('marketing_campaign_consumers_sent_from_domain_id_foreign');
            $table->dropColumn('sent_from_domain_id');
        });
        Schema::dropIfExists('marketing_domains');
    }
};
