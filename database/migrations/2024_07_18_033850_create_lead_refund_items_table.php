<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lead_refund_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('lead_refund_id');
            $table->unsignedBigInteger('product_assignment_id');
            $table->decimal('value');
            $table->string('status')->nullable(false);
            $table->string('refund_reason');
            $table->string('refund_type');

            $table->foreign('lead_refund_id')->references('id')->on('lead_refunds')->noActionOnDelete();
            $table->foreign('product_assignment_id')->references('id')->on('product_assignments')->noActionOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lead_refund_items');
    }
};
