<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ping_post_affiliate_leads', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('consumer_product_id');
            $table->unsignedBigInteger('ping_post_affiliate_id');
            $table->unsignedBigInteger('ping_post_affiliate_request_id');
            $table->float('cost')->nullable();
            $table->string('url_origin')->nullable();
            $table->string('trusted_form_url')->nullable();
            $table->string('universal_lead_id')->nullable();
            $table->date('lead_creation_date')->nullable();
            $table->string('campaign_name')->nullable();
            $table->json('data')->nullable();
            $table->json('manual_fields')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ping_post_affiliate_leads');
    }
};
