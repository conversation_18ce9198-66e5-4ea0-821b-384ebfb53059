<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('conference_transcript_deepgram_records', static function (Blueprint $table) {
            $table->renameColumn('id', 'uuid');
        });

        Schema::table('conference_transcript_deepgram_records', static function (Blueprint $table) {
            $table->id();
        });

        Schema::table('conference_transcript_deepgram_records', static function (Blueprint $table) {
            $table->removeColumn('response_json_path');
        });

        Schema::table('conference_transcript_deepgram_records', static function (Blueprint $table) {
            $table->uuid('deepgram_request_id')->after('id');
        });

        Schema::table('conference_transcript_deepgram_records', static function (Blueprint $table) {
            $table->dateTime('deepgram_requested_at')->after('deepgram_request_id');
        });

        Schema::table('conference_transcript_deepgram_records', static function (Blueprint $table) {
            $table->string('deepgram_language')->after('deepgram_requested_at');
        });

        Schema::table('conference_transcript_deepgram_records', static function (Blueprint $table) {
            $table->string('deepgram_average_sentiment')->after('deepgram_language');
        });

        Schema::table('conference_transcript_deepgram_records', static function (Blueprint $table) {
            $table->float('deepgram_average_sentiment_score')->after('deepgram_average_sentiment');
        });

        Schema::table('conference_transcript_deepgram_records', static function (Blueprint $table) {
            $table->longText('deepgram_summary')->after('deepgram_average_sentiment_score');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('conference_transcript_deepgram_records', static function (Blueprint $table) {
            $table->text('response_json_path')->change();

            $table->dropColumn('deepgram_request_id');

            $table->dropColumn('deepgram_requested_at');

            $table->dropColumn('deepgram_language');

            $table->dropColumn('deepgram_summary');
        });

        Schema::table('conference_transcript_deepgram_records', static function (Blueprint $table) {
            $table->dropColumn('id');
        });

        Schema::table('conference_transcript_deepgram_records', static function (Blueprint $table) {
            $table->renameColumn('uuid', 'id');
        });
    }
};
