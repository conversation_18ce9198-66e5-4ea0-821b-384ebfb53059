<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('company_profile_service_areas', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_profile_id')->constrained()->cascadeOnDelete();
            $table->unsignedBigInteger('company_profile_location_id');
            $table->unsignedBigInteger('company_profile_industry_service_id');
            $table->foreign('company_profile_location_id', 'cpl_id_foreign')->references('id')->on('company_profile_locations')->cascadeOnDelete();
            $table->foreign('company_profile_industry_service_id', 'cpis_id_foreign')->references('id')->on('company_profile_industry_services')->cascadeOnDelete();
            $table->timestamps();
            $table->unique(['company_profile_id', 'company_profile_location_id', 'company_profile_industry_service_id'], 'cpl_id_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('company_profile_service_areas');
    }
};
