<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_configurations', function (Blueprint $table) {
            $table->boolean('consumer_proxy_phone_enabled')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_configurations', function (Blueprint $table) {
            $table->dropColumn('consumer_proxy_phone_enabled');
        });
    }
};
