<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_profiles', function (Blueprint $table) {
            $table->dropForeign('company_profiles_company_id_foreign');
            $table->dropForeign('company_profiles_new_buyer_prospect_id_foreign');
        });

        Schema::table('company_profiles', function (Blueprint $table) {
            $table->foreign('company_id')
                ->references('id')->on('companies')
                ->nullOnDelete();

            $table->foreign('new_buyer_prospect_id')
                ->references('id')->on('new_buyer_prospects')
                ->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_profiles', function (Blueprint $table) {
            $table->dropForeign('company_profiles_company_id_foreign');
            $table->dropForeign('company_profiles_new_buyer_prospect_id_foreign');
        });

        Schema::table('company_profiles', function (Blueprint $table) {
            $table->foreign('company_id')
                ->references('id')->on('companies')
                ->nullOnDelete();

            $table->foreign('new_buyer_prospect_id')
                ->references('id')->on('new_buyer_prospects')
                ->nullOnDelete();
        });
    }
};
