<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('calendar_event_attendees', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('calendar_event_id');
            $table->string('name')->nullable();
            $table->string('email');
            $table->string('status');
            $table->unsignedBigInteger('identified_contact_id')->nullable();
            $table->string('relation_type')->nullable();
            $table->unsignedBigInteger('relation_id')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(["relation_type", "relation_id"]);

            $table->foreign('calendar_event_id')->references('id')->on('calendar_events')->noActionOnDelete();
            $table->foreign('identified_contact_id')->references('id')->on('identified_contacts')->noActionOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('calendar_event_attendees');
    }
};
