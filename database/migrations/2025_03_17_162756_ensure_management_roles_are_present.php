<?php

use Illuminate\Database\Migrations\Migration;
use Spatie\Permission\Models\Role;

return new class extends Migration
{
    public function up(): void
    {
        collect([
            'basic',
            'admin',
            'lead-processor',
            'lead-processing-management',
            'sales-bait-management',
            'industry-management',
            'bundle-admin',
            'bundle-issuer',
            'email-template-admin',
            'opportunity-notification-admin',
            'sales-manager',
            'hr-manager',
            'alert-processor',
            'mailbox-user',
            'account-manager',
            'privacy-officer',
            'privacy-admin',
            'territory-manager',
            'relationship-manager-admin',
            'relationship-manager-viewer',
            'success-manager-viewer',
            'lead-refunds-viewer',
            'lead-refunds-requester',
            'lead-refunds-reviewer',
            'looker-admin',
            'finance-owner',
            'finance-manager',
            'finance-controller',
            'finance-viewer',
            'financial-analyst',
            'affiliates-admin',
            'deactivated',
            'marketing-manager',
            'contract-manager',
            'business-development-manager',
        ])->each(fn ($role) => Role::findOrCreate($role));

        Role::findOrCreate('account-manager');
        Role::findOrCreate('business-development-manager');
        Role::findOrCreate('customer-success-manager');
        Role::findOrCreate('sales-development-representative');
        Role::findOrCreate('onboarding-manager');
    }
};
