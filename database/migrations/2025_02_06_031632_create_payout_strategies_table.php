<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('affiliate_payout_strategies', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('affiliate_id');
            $table->string('type');
            $table->integer('value');
            $table->timestamp('active_from');
            $table->timestamp('active_to')->nullable();
            $table->timestamps();

            $table->unsignedBigInteger('author_id')->nullable();
            $table->foreign('affiliate_id')->references('id')->on('affiliates');
            $table->foreign('author_id')->references('id')->on('users');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('affiliate_payout_strategies');
    }
};
