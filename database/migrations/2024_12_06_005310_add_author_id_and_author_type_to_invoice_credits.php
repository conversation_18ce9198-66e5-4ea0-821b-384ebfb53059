<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice_credits', function (Blueprint $table) {
            $table->unsignedBigInteger('author_id')->nullable();
            $table->string('author_type')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_credits', function (Blueprint $table) {
            $table->dropColumn('author_id');
            $table->dropColumn('author_type');
        });
    }
};
