<?php

use App\Models\CompanyUserRelationship;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Role;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('company_user_relationships', function (Blueprint $table) {
            $table->foreignIdFor(Role::class)->after('type');
        });

        collect([
            'account_manager' => Role::whereName('account-manager')->first(),
            'business_development_manager' => Role::whereName('business-development-manager')->first(),
            'customer_success_manager' => Role::whereName('customer-success-manager')->first(),
        ])->each(function ($role, $type) {
            CompanyUserRelationship::whereType($type)
                ->update(['role_id' => $role->id]);
        });

        Schema::table('company_user_relationships', static function (Blueprint $table) {
            $table->dropColumn('type');
        });
    }
};
