<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_disputes', function (Blueprint $table) {
            $table->id();
            $table->uuid();

            $table->unsignedBigInteger('invoice_id');
            $table->string('external_id');
            $table->unsignedBigInteger('transaction_charge_id');
            $table->string('external_charge_id');
            $table->string('reason');
            $table->string('status');
            $table->decimal('amount');
            $table->decimal('currency');
            $table->decimal('source');

            $table->foreign('invoice_id')->references('id')->on('invoices')->noActionOnDelete();
            $table->foreign('transaction_charge_id')->references('id')->on('invoice_transactions')->noActionOnDelete();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_disputes');
    }
};
