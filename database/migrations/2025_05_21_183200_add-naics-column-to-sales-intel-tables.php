<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        collect([
            'sales_intel_company_import_records',
            'sales_intel_failed_company_import_records',
        ])->each(fn ($table) => Schema::table($table, function ($table) {
            $table->string('naics')->nullable();
        }));
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        collect([
            'sales_intel_company_import_records',
            'sales_intel_failed_company_import_records',
        ])->each(fn ($table) => Schema::table($table, function ($table) {
            $table->dropColumn('naics');
        }));
    }
};
