<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('opt_in_companies', function (Blueprint $table) {
            $table->unsignedBigInteger('company_campaign_id')->nullable();
            $table->unsignedBigInteger('company_opt_in_name_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropColumns('opt_in_companies', ['company_campaign_id', 'company_opt_in_name_id']);
    }
};
