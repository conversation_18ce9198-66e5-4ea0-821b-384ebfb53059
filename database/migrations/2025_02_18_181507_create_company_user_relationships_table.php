<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('company_user_relationships', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('company_id');
            $table->enum('type', ['account_manager', 'business_development_manager', 'customer_success_manager', 'retention_manager']);
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->string('conclusion')->nullable();
            $table->json('payload')->nullable();
            $table->timestamp('commissionable_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('company_user_relationships');
    }
};
