<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('dashboard_login_tokens', function (Blueprint $table) {
            $table->unsignedBigInteger('shadower_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('dashboard_login_tokens', function (Blueprint $table) {
            $table->dropColumn('shadower_id');
        });
    }
};
