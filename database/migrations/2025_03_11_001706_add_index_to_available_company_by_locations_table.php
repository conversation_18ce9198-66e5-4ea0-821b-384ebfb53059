<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('available_company_by_locations', function (Blueprint $table) {
            $table->index('location_id');
            $table->index('company_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('available_company_by_locations', function (Blueprint $table) {
            $table->dropIndex('available_company_by_locations_location_id_index');
            $table->dropIndex('available_company_by_locations_company_id_index');
        });
    }
};
