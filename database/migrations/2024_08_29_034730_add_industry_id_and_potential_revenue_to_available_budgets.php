<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('available_budgets', function (Blueprint $table) {
            $table->float('potential_queued_revenue');
            $table->unsignedBigInteger('industry_id')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('available_budgets', function (Blueprint $table) {
            $table->dropColumn('potential_queued_revenue');
            $table->dropColumn('industry_id');
        });
    }
};
