<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('billing_profiles', function (Blueprint $table) {
            $table->string('payment_gateway_payment_method_code')->nullable()->change();
            $table->string('payment_gateway_client_code')->nullable()->change();
            $table->unsignedBigInteger('created_by_id')->nullable()->change();
            $table->string('billing_contact')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('billing_profiles', function (Blueprint $table) {
            //
        });
    }
};
