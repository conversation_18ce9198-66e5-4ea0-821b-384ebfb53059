<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('appointment_deliveries', function (Blueprint $table) {
            $table->foreignId('product_campaign_id')->nullable()->change();
            $table->unsignedBigInteger('company_campaign_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('appointment_deliveries', function (Blueprint $table) {
            $table->dropColumn('company_campaign_id');
        });
    }
};
