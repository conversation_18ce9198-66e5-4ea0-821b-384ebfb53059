<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('closer_demos', function (Blueprint $table) {
            $table->boolean('upcoming_notification_sent')->default(false);
            $table->boolean('follow_up_notification_sent')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('closer_demos', function (Blueprint $table) {
            $table->dropColumn('upcoming_notification_sent');
            $table->dropColumn('follow_up_notification_sent');
        });
    }
};
