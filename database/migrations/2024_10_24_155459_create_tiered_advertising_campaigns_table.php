<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tiered_advertising_campaigns', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('industry_id');
            $table->string('platform');
            $table->string('name');
            $table->unsignedInteger('tier');
            $table->unsignedBigInteger('tiered_advertising_account_id');
            $table->string('platform_campaign_id');
            $table->float('tcpa_bid');
            $table->float('upper_bound')->nullable();
            $table->float('lower_bound');
            $table->unsignedBigInteger('covered_population');
            $table->json('data')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tiered_advertising_campaigns');
    }
};
