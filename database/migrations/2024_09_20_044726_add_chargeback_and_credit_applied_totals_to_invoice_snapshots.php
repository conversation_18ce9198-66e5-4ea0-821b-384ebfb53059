<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice_snapshots', function (Blueprint $table) {
            $table->decimal('total_credit_applied');
            $table->decimal('total_chargeback');
            $table->decimal('total_chargeback_won');
            $table->decimal('total_chargeback_lost');

            $table->dropColumn('total_lost');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_snapshots', function (Blueprint $table) {
            $table->decimal('total_lost');

            $table->dropColumn('total_credit_applied');
            $table->dropColumn('total_chargeback');
            $table->dropColumn('total_chargeback_won');
            $table->dropColumn('total_chargeback_lost');
        });
    }
};
