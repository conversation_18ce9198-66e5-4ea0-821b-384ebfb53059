<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('consumer_product_tracking', function (Blueprint $table) {
            $table->boolean('conversion_uploaded')->default(false)->after('ad_track_code');

            $table->index(['conversion_uploaded']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('consumer_product_tracking', function (Blueprint $table) {
            $table->dropColumn(['conversion_uploaded']);
        });
    }
};
