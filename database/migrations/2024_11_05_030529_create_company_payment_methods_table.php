<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('company_payment_methods', function (Blueprint $table) {
            $table->id();

            $table->string('payment_gateway_client_code')->nullable();
            $table->string('payment_gateway_payment_method_code')->nullable();
            $table->string('type');
            $table->boolean('is_default')->nullable();
            $table->unsignedBigInteger('company_id');
            $table->unsignedBigInteger('added_by_id')->nullable();
            $table->string('added_by_type')->nullable();

            $table->foreign('company_id')->references('id')->on('companies');

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('company_payment_methods');
    }
};
