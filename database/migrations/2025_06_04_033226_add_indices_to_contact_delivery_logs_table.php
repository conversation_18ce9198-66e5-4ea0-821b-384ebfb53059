<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_campaign_contact_delivery_logs', function (Blueprint $table) {
            $table->index('consumer_product_id');
            $table->index('module_id');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_campaign_contact_delivery_logs', function (Blueprint $table) {
            //
        });
    }
};
