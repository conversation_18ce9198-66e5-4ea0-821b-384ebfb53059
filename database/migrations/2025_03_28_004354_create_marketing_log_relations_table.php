<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('marketing_log_relations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('marketing_log_id');
            $table->morphs('relation');
            $table->timestamps();
            $table->index(['relation_id', 'relation_type'], 'relation_index');
            $table->foreign('marketing_log_id')->references('id')->on('marketing_logs')->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('marketing_log_relations');
    }
};
