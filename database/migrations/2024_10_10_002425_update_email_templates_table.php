<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\EmailTemplate;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table(EmailTemplate::TABLE, function (Blueprint $table) {
            $table->unsignedInteger('type')->default(0);
            $table->boolean('active')->default(0);
            $table->string('engine')->nullable()->default(null);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table(EmailTemplate::TABLE, function (Blueprint $table) {
            $table->dropColumn('type');
            $table->dropColumn('active');
            $table->dropColumn('engine');
        });
    }
};
