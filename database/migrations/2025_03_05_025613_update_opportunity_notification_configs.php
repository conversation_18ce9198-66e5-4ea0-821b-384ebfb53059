<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('opportunity_notification_configs', function (Blueprint $table) {
            $table->unsignedSmallInteger('maximum_days_since_last_lead')->nullable();
            $table->unsignedTinyInteger('days_to_query')->default(7);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('opportunity_notification_configs', function (Blueprint $table) {
            $table->dropColumn('maximum_days_since_last_lead');
            $table->dropColumn('days_to_query');
        });
    }
};
