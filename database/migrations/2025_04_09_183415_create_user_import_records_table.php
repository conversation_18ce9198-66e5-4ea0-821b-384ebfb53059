<?php

use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('sales_intel_user_import_records', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Company::class);
            $table->foreignIdFor(CompanyUser::class);
            $table->timestamps();
        });
    }
};
