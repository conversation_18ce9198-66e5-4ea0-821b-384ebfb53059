<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * This is to keep a temporary record of migrations to make it easier to add, remove, or redo
 * any CRMTemplate migrations for Solar if needed
 *
 * Once Solar migration looks good, this table can safely be dropped
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('crm_template_migrations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id');
            $table->unsignedBigInteger('legacy_crm_integration_id');
            $table->unsignedBigInteger('company_crm_template_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('crm_template_migrations');
    }
};
