<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('affiliate_payouts', function (Blueprint $table) {
            $table->id();
            $table->integer('cent_value');
            $table->timestamps();

            $table->unsignedBigInteger('payout_strategy_id');
            $table->unsignedBigInteger('affiliate_id');
            $table->unsignedBigInteger('consumer_product_id');

            $table->unique(['payout_strategy_id', 'affiliate_id', 'consumer_product_id'], 'affiliate_payouts_unique');

            $table->foreign('payout_strategy_id')->references('id')->on('affiliate_payout_strategies');
            $table->foreign('affiliate_id')->references('id')->on('affiliates');
            $table->foreign('consumer_product_id')->references('id')->on('consumer_products');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('affiliate_payouts');
    }
};
