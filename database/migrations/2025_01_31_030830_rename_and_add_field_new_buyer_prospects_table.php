<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('new_buyer_prospects', function (Blueprint $table) {
            $table->renameColumn('reference', 'external_reference');
            $table->string('reference')->after('id')->default('');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('new_buyer_prospects', function (Blueprint $table) {
            $table->dropColumn('reference');
            $table->renameColumn('external_reference', 'reference');
        });
    }
};
