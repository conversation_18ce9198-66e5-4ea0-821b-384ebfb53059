<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_assignment_communications', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('product_assignment_id')->index();
            $table->string('sid')->index();
            $table->unsignedTinyInteger('communication_type');
            $table->json('content')->nullable()->default(null);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_assignment_communications');
    }
};
