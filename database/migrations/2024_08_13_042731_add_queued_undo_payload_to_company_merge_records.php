<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_merge_records', function (Blueprint $table) {
            $table->json('queued_undo_payload')->nullable()->default(null);
            $table->timestamp('completed_at')->nullable()->default(null);
            $table->json('undo_payload')->nullable()->default(null)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_merge_records', function (Blueprint $table) {
            $table->dropColumn('queued_undo_payload');
            $table->dropColumn('completed_at');
        });
    }
};
