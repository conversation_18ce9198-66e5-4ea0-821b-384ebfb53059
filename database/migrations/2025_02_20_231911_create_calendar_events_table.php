<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('calendar_events', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('calendar_id');
            $table->string('external_id');
            $table->unsignedBigInteger('user_id');
            $table->json('payload')->nullable();
            $table->string('title');
            $table->string('conference_url')->nullable();
            $table->string('description')->nullable();
            $table->string('location')->nullable();;
            $table->string('status');
            $table->string('recurrence_rule')->nullable();
            $table->json('recurrence_data')->nullable();
            $table->string('timezone')->nullable();
            $table->timestamp('start_time')->nullable();
            $table->timestamp('end_time')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('calendar_id')->references('id')->on('calendars')->noActionOnDelete();
            $table->foreign('user_id')->references('id')->on('users')->noActionOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('calendar_events');
    }
};
