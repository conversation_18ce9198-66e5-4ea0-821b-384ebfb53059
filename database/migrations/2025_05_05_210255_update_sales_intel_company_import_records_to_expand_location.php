<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('sales_intel_company_import_records', function (Blueprint $table) {
            $table->string('location_type');
            $table->string('location');
        });

        DB::statement("UPDATE sales_intel_company_import_records SET location_type = 'zipcode', location = zipcode where zipcode is not null");

        Schema::table('sales_intel_company_import_records', function (Blueprint $table) {
            $table->dropColumn('zipcode');
        });
    }
};
