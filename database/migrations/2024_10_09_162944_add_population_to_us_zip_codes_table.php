<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('us_zip_codes', function (Blueprint $table) {
            $table->unsignedBigInteger('population')->default(0);
            $table->float('density')->default(0);
            $table->json('county_weights')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('us_zip_codes', function (Blueprint $table) {
            $table->dropColumn('population');
            $table->dropColumn('density');
            $table->dropColumn('county_weights');
        });
    }
};
