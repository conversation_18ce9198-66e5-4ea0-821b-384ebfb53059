<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('missed_product_reason_events', function (Blueprint $table) {
            $table->unsignedBigInteger('company_campaign_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('missed_product_reason_events', function (Blueprint $table) {
            //
        });
    }
};
