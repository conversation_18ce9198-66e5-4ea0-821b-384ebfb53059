<?php

use App\Models\Odin\Company;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('sales_intel_failed_import_records', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Company::class);
            $table->timestamps();
        });
    }
};
