<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('recycled_leads', function (Blueprint $table) {
            $table->index('opted_out', 'recycled_leads_opted_out_index');
            $table->index('contacted_at', 'recycled_leads_contacted_at_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('recycled_leads', function (Blueprint $table) {
            $table->dropIndex('recycled_leads_opted_out_index');
            $table->dropIndex('recycled_leads_contacted_at_index');
        });
    }
};
