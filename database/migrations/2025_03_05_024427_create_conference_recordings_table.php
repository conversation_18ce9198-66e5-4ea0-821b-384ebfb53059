<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('conference_recordings', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger("conference_id");
            $table->string("external_id");
            $table->string("external_destination_file_id");
            $table->string("bucket_path")->nullable();
            $table->timestamp("start_time");
            $table->timestamp("end_time");
            $table->integer("duration_in_seconds")->nullable()->default(0);

            $table->foreign('conference_id')->references('id')->on('conferences')->noActionOnDelete();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('conference_recordings');
    }
};
