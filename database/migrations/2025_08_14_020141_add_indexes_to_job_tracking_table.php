<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('job_tracking', function (Blueprint $table) {
            $table->unique('job_uuid');
            $table->index(['relation', 'relation_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('job_tracking', function (Blueprint $table) {
            $table->dropIndex('job_tracking_job_uuid_unique');
            $table->dropIndex('job_tracking_relation_relation_id_index');
        });
    }
};
