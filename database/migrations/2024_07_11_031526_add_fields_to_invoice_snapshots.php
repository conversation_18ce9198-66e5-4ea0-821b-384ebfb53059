<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice_snapshots', function (Blueprint $table) {
            $table->uuid();
            $table->decimal('total_outstanding');
            $table->decimal('total_refunded');
            $table->decimal('total_lost');
            $table->decimal('total_paid');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_snapshots', function (Blueprint $table) {
            $table->uuid();
            $table->dropColumn('total_outstanding');
            $table->dropColumn('total_refunded');
            $table->dropColumn('total_lost');
            $table->dropColumn('total_paid');
        });
    }
};
