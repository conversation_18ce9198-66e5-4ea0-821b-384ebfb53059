<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('billing_profiles', function (Blueprint $table) {
            $table->string('payment_gateway_payment_method_code')->after('payment_gateway_client_code');
            $table->boolean('default')->default(false);
            $table->boolean('verified')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('billing_profiles', function (Blueprint $table) {
            $table->dropColumn('payment_gateway_payment_method_code');
            $table->dropColumn('default');
            $table->dropColumn('verified');
        });
    }
};
