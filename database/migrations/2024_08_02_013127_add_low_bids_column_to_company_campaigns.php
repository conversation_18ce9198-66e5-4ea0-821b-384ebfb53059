<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_campaigns', function (Blueprint $table) {
            $table->boolean('has_low_bids')->default(false)->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_campaigns', function (Blueprint $table) {
            $table->dropColumn('has_low_bids');
        });
    }
};
