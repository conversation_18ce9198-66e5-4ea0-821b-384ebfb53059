<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('upsell_automation_log_entries', function (Blueprint $table) {
            $table->index('consumer_product_id', 'upsell_automation_log_entries_consumer_product_id_index');
            $table->index('status', 'upsell_automation_log_entries_status_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('upsell_automation_log_entries', function (Blueprint $table) {
            $table->dropIndex('upsell_automation_log_entries_consumer_product_id_index');
            $table->dropIndex('upsell_automation_log_entries_status_index');
        });
    }
};
