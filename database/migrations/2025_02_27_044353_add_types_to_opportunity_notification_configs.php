<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('opportunity_notification_configs', function (Blueprint $table) {
            $table->unsignedTinyInteger('type')->default(0);
            $table->unsignedSmallInteger('maximum_promo_products')->default(null);
            $table->unsignedSmallInteger('campaign_threshold')->default(null);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('opportunity_notification_configs', function (Blueprint $table) {
            $table->dropColumn('type');
            $table->dropColumn('max_promo_missed_products');
            $table->dropColumn('campaign_threshold');
        });
    }
};
