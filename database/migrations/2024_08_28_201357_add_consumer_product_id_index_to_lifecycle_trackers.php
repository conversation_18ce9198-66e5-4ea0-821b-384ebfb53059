<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('consumer_product_lifecycle_trackers', function (Blueprint $table) {
            $table->index('consumer_product_id', 'consumer_product_id_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('consumer_product_lifecycle_trackers', function (Blueprint $table) {
            $table->dropIndex('consumer_product_id_index');
        });
    }
};
