<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('non_purchasing_company_locations', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->foreignId('company_id')->index()->constrained()->cascadeOnDelete();
            $table->foreignId('company_location_id')->nullable()->index()->constrained()->cascadeOnDelete();
            $table->unsignedBigInteger('location_id')->index();
            $table->boolean('is_set_by_radius')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('non_purchasing_company_locations');
    }
};
