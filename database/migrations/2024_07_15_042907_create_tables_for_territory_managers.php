<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('territory_managers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('user_id')->references('id')->on('users');

            $table->index('user_id');
        });

        Schema::create('territory_manager_clients', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('territory_manager_id');
            $table->string('company_reference');
            $table->tinyInteger('status');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('territory_manager_id')->references('id')->on('territory_managers');

            $table->index('territory_manager_id');
            $table->index('company_reference');
            $table->index(['territory_manager_id', 'company_reference'], 'idx_id_reference');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('territory_manager_clients');
        Schema::dropIfExists('territory_managers');
    }
};
