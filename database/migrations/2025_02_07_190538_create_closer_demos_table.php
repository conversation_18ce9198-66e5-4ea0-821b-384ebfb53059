<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('closer_demos', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('new_buyer_prospect_id');
            $table->unsignedBigInteger('company_id')->nullable();
            $table->timestamp('demo_at')->nullable();
            $table->string('calendly_event_url');
            $table->json('calendly_payload')->nullable();
            $table->enum('status', ['booked', 'completed', 'cancelled'])->default('booked');
            $table->enum('resolution', ['unsuccessful', 'follow_up_required', 'successful'])->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('closer_demos');
    }
};
