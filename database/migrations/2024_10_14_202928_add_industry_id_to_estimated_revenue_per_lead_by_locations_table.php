<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('estimated_revenue_per_lead_by_locations', function (Blueprint $table) {
            $table->unsignedBigInteger('industry_id')->index();
            $table->string('county_fips')->default('');
            $table->json('negative_zip_codes')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('estimated_revenue_per_lead_by_locations', function (Blueprint $table) {
            $table->dropColumn('industry_id');
            $table->dropColumn('county_fips');
            $table->dropColumn('negative_zip_codes');
        });
    }
};
