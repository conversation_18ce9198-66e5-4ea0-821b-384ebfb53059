<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('top_companies_by_counties', function (Blueprint $table) {
            $table->id();
            $table->unsignedSmallInteger('industry_service_id');
            $table->integer('county_location_id');
            $table->integer('company_id');
            $table->string('company_name');
            $table->string('company_reference');
            $table->unsignedSmallInteger('company_review_count');
            $table->float('company_bayesian_all_time');
            $table->timestamps();

            $table->index(['county_location_id', 'industry_service_id'], 'county_service_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('top_companies_by_counties');
    }
};
