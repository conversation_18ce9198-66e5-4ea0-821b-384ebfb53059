<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('appointment_deliveries', function (Blueprint $table) {
            $table->boolean('consumer_delivered_sms')->default(false);
            $table->boolean('consumer_delivered_email')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('appointment_deliveries', function (Blueprint $table) {
            $table->dropColumn('consumer_delivered_sms');
            $table->dropColumn('consumer_delivered_email');
        });
    }
};
