<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('conference_transcript_deepgram_records', static function (Blueprint $table) {
            $table->ulid('id');
            $table->foreignId('conference_transcript_id')->constrained(indexName: 'ct_deepgram_records_ct_id_foreign')->cascadeOnDelete();
            $table->text('response_json_path');
            $table->timestamps();

            $table->unique('conference_transcript_id', 'conference_transcript_deepgram_records_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('conference_transcript_deepgram_records');
    }
};
