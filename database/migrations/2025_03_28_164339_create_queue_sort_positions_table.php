<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('queue_sort_positions', function (Blueprint $table) {
            $table->id();
            $table->string('class_name');
            $table->enum('queue_name', ['existing_companies']);
            $table->unsignedBigInteger('model_id');
            $table->integer('ordinal_value');
            $table->datetime('released_at')->nullable();
            $table->unsignedBigInteger('released_by_user_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('queue_sort_positions');
    }
};
