<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('conference_recordings', function (Blueprint $table) {
            $table->string('external_destination_file_id')->nullable()->change();
            $table->timestamp('start_time')->nullable()->change();
            $table->timestamp('end_time')->nullable()->change();
        });

        Schema::table('conference_participants', function (Blueprint $table) {
            $table->string('name')->nullable()->change();
            $table->timestamp('earliest_start_time')->nullable()->change();
            $table->timestamp('latest_end_time')->nullable()->change();
        });

        Schema::table('conference_participants', function (Blueprint $table) {
            $table->string('name')->nullable()->change();
            $table->timestamp('earliest_start_time')->nullable()->change();
            $table->timestamp('latest_end_time')->nullable()->change();
        });

        Schema::table('conference_transcript_entries', function (Blueprint $table) {
            $table->timestamp('start_time')->nullable()->change();
            $table->timestamp('end_time')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
