<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('marketing_campaigns', function (Blueprint $table) {
            $table->uuid('external_reference')->nullable()->change();
            $table->string('external_code')->nullable()->change();
        });

        Schema::table('marketing_campaign_consumers', function (Blueprint $table) {
            $table->uuid('external_reference')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('marketing_campaigns', function (Blueprint $table) {
            $table->uuid('external_reference')->change();
            $table->string('external_code')->change();
        });

        Schema::table('marketing_campaign_consumers', function (Blueprint $table) {
            $table->uuid('external_reference')->change();
        });
    }
};
