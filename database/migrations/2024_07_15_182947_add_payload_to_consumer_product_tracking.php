<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('consumer_product_tracking', function (Blueprint $table) {
            $table->json('payload')->after('ad_track_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('consumer_product_tracking', function (Blueprint $table) {
            $table->dropColumn(['payload']);
        });
    }
};
