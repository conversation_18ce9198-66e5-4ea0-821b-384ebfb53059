<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('watchdog_videos', function (Blueprint $table) {
            $table->renameColumn('id', 'watchdog_video_id');
        });

        Schema::table('watchdog_videos', function (Blueprint $table) {
            $table->string('watchdog_video_id')->change();
        });

        Schema::table('watchdog_videos', function (Blueprint $table) {
            $table->dropPrimary();
        });

        Schema::table('watchdog_videos', function (Blueprint $table) {
            $table->id()->first();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('watchdog_videos', function (Blueprint $table) {
            $table->dropColumn('id');
        });

        Schema::table('watchdog_videos', function (Blueprint $table) {
            $table->string('watchdog_video_id')->unique()->primary()->change();
        });

        Schema::table('watchdog_videos', function (Blueprint $table) {
            $table->renameColumn('watchdog_video_id', 'id');
        });
    }
};
