<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('company_invoice', function (Blueprint $table) {
            $table->uuid('uuid');
            $table->integer('total')->default(0);
            $table->foreignId('company_id');
            $table->foreignId('invoice_id');
            $table->string('status')->nullable();
            $table->dateTime('generated_at')->nullable();
            $table->dateTime('due_at')->nullable();
            $table->string('payment_method')->nullable();
            $table->string('invoice_url')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('company_invoice');
    }
};
