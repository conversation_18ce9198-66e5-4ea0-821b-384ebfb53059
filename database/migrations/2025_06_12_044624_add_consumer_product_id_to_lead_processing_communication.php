<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lead_processing_communications', function (Blueprint $table) {
            $table->unsignedBigInteger('consumer_product_id')->after('id')->default(0)->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lead_processing_communications', function (Blueprint $table) {
            $table->dropColumn('consumer_product_id');
        });
    }
};
