<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lead_processing_aged', function (Blueprint $table) {
            $table->integer('recency_points')->default(0)->index();
            $table->dropColumn('priority');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lead_processing_aged', function (Blueprint $table) {
            $table->dropColumn('recency_points');
            $table->integer('priority')->default(0);
        });
    }
};
