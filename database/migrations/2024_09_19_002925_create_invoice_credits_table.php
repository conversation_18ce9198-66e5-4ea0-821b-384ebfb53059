<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_credits', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('invoice_id');
            $table->unsignedBigInteger('credit_id');
            $table->decimal('amount_applied');
            $table->timestamp('applied_at')->nullable(false);

            $table->foreign('invoice_id')->references('id')->on('invoices')->noActionOnDelete();
            $table->foreign('credit_id')->references('id')->on('credits')->noActionOnDelete();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_credits');
    }
};
