<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('billing_profile_policies', function (Blueprint $table) {
            $table->id();
            $table->string('event_class')->nullable(false);
            $table->string('action_class')->nullable(false);
            $table->integer('sort_order')->nullable(false);
            $table->unsignedBigInteger('billing_profile_id')->nullable();
            $table->foreign('billing_profile_id')->references('id')->on('billing_profiles');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('billing_profile_policies');
    }
};
