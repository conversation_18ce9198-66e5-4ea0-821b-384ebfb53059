<?php

use App\Models\GlobalConfiguration;
use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Role;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('role_configurations', static function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(User::class)->constrained()->cascadeOnDelete();
            $table->foreignIdFor(Role::class)->constrained()->cascadeOnDelete();
            $table->json('data')->nullable();
            $table->timestamps();
        });

        if (blank(GlobalConfiguration::whereConfigurationKey('role_configurations')->first())) {
            GlobalConfiguration::insert([
                'configuration_key' => 'role_configurations',
                'configuration_payload' => json_encode([
                    'data' => [
                        'processing_time_limit_days' => 42,
                        'industries' => [],
                    ],
                ], JSON_THROW_ON_ERROR),
            ]);
        }
    }
};
