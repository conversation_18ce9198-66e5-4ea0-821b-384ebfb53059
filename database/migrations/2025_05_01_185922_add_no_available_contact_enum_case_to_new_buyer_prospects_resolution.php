<?php

use App\Enums\Prospects\ProspectResolution;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('new_buyer_prospects', function (Blueprint $table) {
            $table->enum('resolution', ['decision_maker_identified', 'demo_booked', 'cancelled', 'duplicate_company', 'registration_complete', 'no_available_contacts'])->nullable()->change();
        });
    }
};
