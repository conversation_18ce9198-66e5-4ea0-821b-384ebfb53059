<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * @return void
     */
    public function up(): void
    {
        Schema::create('billing_logs', function (Blueprint $table) {
            $table->id();
            $table->string('related_type')->nullable();
            $table->string('related_id')->nullable();
            $table->string('namespace')->nullable();
            $table->json('context')->nullable();
            $table->text('message');
            $table->string('trace')->nullable();
            $table->string('level')->default('error');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('billing_logs');
    }
};
