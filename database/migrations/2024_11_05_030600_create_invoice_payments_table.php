<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_payments', function (Blueprint $table) {
            $table->id();
            $table->uuid();
            $table->unsignedBigInteger('invoice_id');
            $table->unsignedBigInteger('total');
            $table->string('status');
            $table->unsignedBigInteger('author_id')->nullable();
            $table->string('author_type');
            $table->timestamp('requested_at')->nullable();
            $table->timestamp('charged_at')->nullable();
            $table->timestamp('next_attempt_at')->nullable();
            $table->integer('max_attempts_per_payment_method')->nullable();
            $table->integer('attempt_number')->nullable();

            $table->foreign('invoice_id')->references('id')->on('invoices');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_payments');
    }
};
