<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('industry_consumer_fields', function (Blueprint $table) {
            $table->boolean('show_on_dashboard')->after('send_to_company')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('industry_consumer_fields', function (Blueprint $table) {
            $table->dropColumn('show_on_dashboard');
        });
    }
};
