<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('company_profiles', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->json('categories')->nullable();
            $table->text('description')->nullable();
            $table->text('general_info')->nullable();
            $table->json('hours')->nullable();
            $table->json('websites')->nullable();
            $table->json('phones')->nullable();
            $table->json('addresses')->nullable();
            $table->integer('years_in_business')->nullable();
            $table->float('rating')->nullable();
            $table->boolean('paid')->nullable();
            $table->string('logo')->nullable();
            $table->string('email')->nullable();
            $table->json('other_links')->nullable();
            $table->json('social_links')->nullable();
            $table->text('summary_positives')->nullable();
            $table->text('summary_negatives')->nullable();
            $table->text('summary_overview')->nullable();
            $table->text('summary_source')->nullable();
            $table->string('profile_slug');
            $table->json('images')->nullable();
            $table->integer('discovery_score')->default(0);
            $table->boolean('published')->default(true);
            $table->json('payload')->nullable();
            $table->foreignId('company_id')->nullable()->constrained()->cascadeOnDelete();
            $table->unique('profile_slug');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('company_profiles');
    }
};
