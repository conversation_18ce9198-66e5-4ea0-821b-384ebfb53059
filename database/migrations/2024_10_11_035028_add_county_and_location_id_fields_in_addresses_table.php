<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('addresses', function (Blueprint $table) {
            $table->string('county', 50)->nullable()->after('city');
            $table->unsignedInteger('zip_code_location_id')->nullable();
            $table->unsignedInteger('county_location_id')->nullable();
            $table->unsignedInteger('state_location_id')->nullable();

            $table->index('zip_code_location_id', 'zip_code_location_id_index');
            $table->index('county_location_id', 'county_location_id_index');
            $table->index('state_location_id', 'state_location_id_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('addresses', function (Blueprint $table) {
            $table->dropIndex('zip_code_location_id_index');
            $table->dropIndex('county_location_id_index');
            $table->dropIndex('state_location_id_index');

            $table->dropColumn('county');
            $table->dropColumn('zip_code_location_id');
            $table->dropColumn('county_location_id');
            $table->dropColumn('state_location_id');
        });
    }
};
