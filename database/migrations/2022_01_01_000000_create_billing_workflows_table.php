<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('billing_workflows', function (Blueprint $table) {
            $table->id();
            $table->text('class');
            $table->text('arguments')->nullable();
            $table->text('output')->nullable();
            $table->string('status')->default('pending')->index();
            $table->timestamps(6);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('billing_workflows');
    }
};
