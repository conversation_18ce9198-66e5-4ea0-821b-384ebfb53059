<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lead_processing_communications', function (Blueprint $table) {
            $table->boolean('most_recent')->default(false)->after('type');

            $table->index(['most_recent'], 'most_recent_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lead_processing_communications', function (Blueprint $table) {
            $table->dropIndex('most_recent_index');
            $table->dropColumn(['most_recent']);
        });
    }
};
