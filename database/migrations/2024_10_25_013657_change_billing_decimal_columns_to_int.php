<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_invoices', function (Blueprint $table) {
            $table->bigInteger('total')->change();
        });

        Schema::table('credits', function (Blueprint $table) {
            $table->bigInteger('initial_value')->change();
            $table->bigInteger('remaining_value')->change();
        });

        Schema::table('invoice_collections', function (Blueprint $table) {
            $table->bigInteger('amount_collected')->change();
            $table->bigInteger('amount_recovered')->change();
        });

        Schema::table('invoice_credits', function (Blueprint $table) {
            $table->bigInteger('amount_applied')->change();
        });

        Schema::table('invoice_disputes', function (Blueprint $table) {
            $table->bigInteger('amount')->change();
        });

        Schema::table('invoice_items', function (Blueprint $table) {
            $table->bigInteger('unit_price')->change();
            $table->bigInteger('tax')->change();
        });

        Schema::table('invoice_refund_charges', function (Blueprint $table) {
            $table->bigInteger('amount')->change();
        });

        Schema::table('invoice_refund_items', function (Blueprint $table) {
            $table->bigInteger('value')->change();
        });

        Schema::table('invoice_refunds', function (Blueprint $table) {
            $table->bigInteger('total')->change();
        });

        Schema::table('invoice_snapshots', function (Blueprint $table) {
            if (!Schema::hasColumn('invoice_snapshots', 'total_paid')) {
                $table->bigInteger('total_paid')->default(0);
            }
        });

        Schema::table('invoice_snapshots', function (Blueprint $table) {
            $table->bigInteger('total_chargeback')->change();
            $table->bigInteger('total_chargeback_lost')->change();
            $table->bigInteger('total_chargeback_won')->change();
            $table->bigInteger('total_collections')->change();
            $table->bigInteger('total_collections_lost')->change();
            $table->bigInteger('total_collections_recovered')->change();
            $table->bigInteger('total_credit_applied')->change();
            $table->bigInteger('total_outstanding')->change();
            $table->bigInteger('total_paid')->change();
            $table->bigInteger('total_refunded')->change();
            $table->bigInteger('total_value')->change();
            $table->bigInteger('total_written_off')->change();
        });

        Schema::table('invoice_transactions', function (Blueprint $table) {
            $table->bigInteger('amount')->change();
        });

        Schema::table('invoice_write_offs', function (Blueprint $table) {
            $table->bigInteger('amount')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_invoices', function (Blueprint $table) {
            $table->decimal('total', 10, 2)->change();
        });

        Schema::table('credits', function (Blueprint $table) {
            $table->decimal('initial_value', 10, 2)->change();
            $table->decimal('remaining_value', 10, 2)->change();
        });

        Schema::table('invoice_collections', function (Blueprint $table) {
            $table->decimal('amount_collected', 10, 2)->change();
            $table->decimal('amount_recovered', 10, 2)->change();
        });

        Schema::table('invoice_credits', function (Blueprint $table) {
            $table->decimal('amount_applied', 10, 2)->change();
        });

        Schema::table('invoice_disputes', function (Blueprint $table) {
            $table->decimal('amount', 10, 2)->change();
        });

        Schema::table('invoice_items', function (Blueprint $table) {
            $table->decimal('unit_price', 10, 2)->change();
            $table->decimal('tax', 10, 2)->change();
        });

        Schema::table('invoice_refund_charges', function (Blueprint $table) {
            $table->decimal('amount', 10, 2)->change();
        });

        Schema::table('invoice_refund_items', function (Blueprint $table) {
            $table->decimal('value', 10, 2)->change();
        });

        Schema::table('invoice_refunds', function (Blueprint $table) {
            $table->decimal('total', 10, 2)->change();
        });

        Schema::table('invoice_snapshots', function (Blueprint $table) {
            $table->decimal('total_chargeback', 10, 2)->change();
            $table->decimal('total_chargeback_lost', 10, 2)->change();
            $table->decimal('total_chargeback_won', 10, 2)->change();
            $table->decimal('total_collections', 10, 2)->change();
            $table->decimal('total_collections_lost', 10, 2)->change();
            $table->decimal('total_collections_recovered', 10, 2)->change();
            $table->decimal('total_credit_applied', 10, 2)->change();
            $table->decimal('total_outstanding', 10, 2)->change();
            $table->decimal('total_paid', 10, 2)->change();
            $table->decimal('total_refunded', 10, 2)->change();
            $table->decimal('total_value', 10, 2)->change();
            $table->decimal('total_written_off', 10, 2)->change();
        });

        Schema::table('invoice_transactions', function (Blueprint $table) {
            $table->decimal('amount', 10, 2)->change();
        });

        Schema::table('invoice_write_offs', function (Blueprint $table) {
            $table->decimal('amount', 10, 2)->change();
        });
    }
};
