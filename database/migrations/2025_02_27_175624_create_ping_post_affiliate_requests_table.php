<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ping_post_affiliate_requests', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('ping_post_affiliate_id');
            $table->unsignedBigInteger('ping_post_affiliate_api_key_id');
            $table->string('source_ip');
            $table->json('headers');
            $table->json('request');
            $table->json('response')->nullable();
            $table->boolean('processed')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ping_post_affiliate_requests');
    }
};
