<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('contractor_profiles', function (Blueprint $table) {
            $table->json('brands_sold')->nullable();
            $table->json('licenses')->nullable();
            $table->json('certifications')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('contractor_profiles', function (Blueprint $table) {
            $table->dropColumn('brands_sold');
            $table->dropColumn('licenses');
            $table->dropColumn('certifications');
        });
    }
};
