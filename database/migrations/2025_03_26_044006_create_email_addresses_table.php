<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_addresses', function (Blueprint $table) {
            $table->id();
            $table->string('email');
            $table->string('sanitized_email')->nullable();
            $table->boolean('valid');
            $table->boolean('disposable')->nullable();
            $table->boolean('frequent_complainer')->nullable();
            $table->string('spam_trap_score')->nullable();
            $table->float('fraud_score')->nullable();
            $table->json('payload')->nullable();
            $table->timestamps();

            $table->index('sanitized_email');
            $table->unique('email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_addresses');
    }
};
