<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::dropIfExists('missed_product_configs');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::create('missed_product_configs', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->string('expiration_type');
            $table->unsignedInteger('expiration_quantity');
            $table->unsignedSmallInteger('notification_cooldown_in_days');
            $table->unsignedInteger('purchase_fail_email_template_id');
            $table->boolean('override_can_receive_promotions');
        });
    }
};
