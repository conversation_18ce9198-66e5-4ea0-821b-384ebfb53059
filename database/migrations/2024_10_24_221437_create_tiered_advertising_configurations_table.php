<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tiered_advertising_configurations', function (Blueprint $table) {
            $table->id();
            $table->string('platform');
            $table->unsignedBigInteger('industry_id');
            $table->boolean('enabled')->default(false);
            $table->dateTime('last_location_update')->nullable();
            $table->integer('update_frequency_minutes');
            $table->float('roas');
            $table->json('configs');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tiered_advertising_configurations');
    }
};
