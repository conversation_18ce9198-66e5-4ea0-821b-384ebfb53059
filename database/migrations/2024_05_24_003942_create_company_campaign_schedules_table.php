<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('company_campaign_schedules', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_campaign_id');
            $table->unsignedBigInteger('schedule_id');
            $table->unique(['company_campaign_id', 'schedule_id'], 'campaign_schedule_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('company_campaign_schedules');
    }
};
