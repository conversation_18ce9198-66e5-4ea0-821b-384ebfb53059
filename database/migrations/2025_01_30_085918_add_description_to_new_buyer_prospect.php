<?php

use App\Models\Prospects\NewBuyerProspect;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('new_buyer_prospects', function (Blueprint $table) {
            $table->string(NewBuyerProspect::FIELD_COMPANY_DESCRIPTION)->after(NewBuyerProspect::FIELD_COMPANY_WEBSITE)->nullable();
        });
    }
    public function down(): void
    {
        Schema::table('new_buyer_prospects', function (Blueprint $table) {
            $table->dropColumn(NewBuyerProspect::FIELD_COMPANY_DESCRIPTION);
        });
    }
};
