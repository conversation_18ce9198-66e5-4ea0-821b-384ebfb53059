<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('billing_workflow_relationships', function (Blueprint $table) {
            $table->id();
            $table->foreignId('parent_billing_workflow_id')->nullable()->index();
            $table->unsignedBigInteger('parent_index');
            $table->timestamp('parent_now');
            $table->foreignId('child_billing_workflow_id')->nullable()->index();
            $table->foreign('parent_billing_workflow_id', 'parent_billing_workflow_foreign')->references('id')->on('billing_workflows');
            $table->foreign('child_billing_workflow_id', 'child_billing_workflow_foreign')->references('id')->on('billing_workflows');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('billing_workflow_relationships');
    }
};
