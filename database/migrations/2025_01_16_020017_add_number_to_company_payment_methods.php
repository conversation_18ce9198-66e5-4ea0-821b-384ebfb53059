<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_payment_methods', function (Blueprint $table) {
            $table->string('number')->nullable();
            $table->string('expiry_month')->nullable();
            $table->string('expiry_year')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_payment_methods', function (Blueprint $table) {
            $table->dropColumn('number');
            $table->dropColumn('expiry_month');
            $table->dropColumn('expiry_year');
        });
    }
};
