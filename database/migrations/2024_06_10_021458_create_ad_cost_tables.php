<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('daily_ad_costs', function (Blueprint $table) {
            $table->id();
            $table->unsignedMediumInteger('industry_id')->index();
            $table->unsignedMediumInteger('location_id')->index();
            $table->unsignedTinyInteger('advertiser')->index();
            $table->unsignedTinyInteger('platform')->index();
            $table->date('date')->index();
            $table->decimal('cost');

            $table->index(['industry_id', 'location_id', 'date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('daily_ad_costs');
    }
};
