<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lead_processing_aged', function (Blueprint $table) {
            $table->timestamp('skipped_at')->nullable();
            $table->integer('skip_count')->default(0);
            $table->json('skip_reasons')->nullable();
            $table->index('skipped_at', 'lead_processing_aged_skipped_at_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lead_processing_aged', function (Blueprint $table) {
            $table->dropIndex('lead_processing_aged_skipped_at_index');
            $table->dropColumn('skipped_at');
            $table->dropColumn('skip_count');
            $table->dropColumn('skip_reasons');
        });
    }
};
