<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('estimated_revenue_per_lead_by_locations', function (Blueprint $table) {
            $table->unsignedBigInteger('industry_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('estimated_revenue_per_lead_by_locations', function (Blueprint $table) {
            $table->unsignedBigInteger('industry_id')->change();
        });
    }
};
