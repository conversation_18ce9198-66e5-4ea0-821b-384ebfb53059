<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('new_buyer_prospects', function (Blueprint $table) {
            $table->string('decision_maker_phone')->nullable()->after('decision_maker_email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('new_buyer_prospects', function (Blueprint $table) {
            $table->dropColumn('decision_maker_phone');
        });
    }
};
