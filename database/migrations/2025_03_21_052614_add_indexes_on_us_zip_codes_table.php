<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('us_zip_codes', function (Blueprint $table) {
            $table->index('zip_code', 'us_zip_codes_zip_code_index');
            $table->index(['zip_code', 'zip_type', 'city_type'], 'us_zip_codes_zip_code_zip_city_type_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('us_zip_codes', function (Blueprint $table) {
            $table->dropIndex('us_zip_codes_zip_code_index');
            $table->dropIndex('us_zip_codes_zip_code_zip_city_type_index');
        });
    }
};
