<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('credits', function (Blueprint $table) {
            $table->dropForeign('credits_company_id_foreign');
            $table->dropUnique('company_type_unique');
            $table->dropColumn('type');
            $table->dropColumn('amount');
            $table->float('initial_value');
            $table->float('remaining_value');
            $table->string('credit_type');
            $table->foreign('credit_type')->references('slug')->on('credit_types');
            $table->dateTime('expires_at');
            $table->foreign('company_id')->references('id')->on('companies')->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('credits', function (Blueprint $table) {
            $table->dropColumn('expires_at');
            $table->dropForeign('credits_credit_type_foreign');
            $table->dropColumn('credit_type');
            $table->string('type');
            $table->unique(['company_id', 'type'], 'company_type_unique');
            $table->float('amount');
            $table->dropColumn('initial_value');
            $table->dropColumn('remaining_value');
        });
    }
};
