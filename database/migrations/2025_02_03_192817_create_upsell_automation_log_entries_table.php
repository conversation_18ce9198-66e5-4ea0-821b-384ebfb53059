<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('upsell_automation_log_entries', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('log_id');
            $table->string('type');
            $table->string('status')->nullable();
            $table->longText('entry')->nullable();
            $table->unsignedBigInteger('consumer_product_id')->nullable();
            $table->float('old_revenue')->nullable();
            $table->float('new_revenue')->nullable();
            $table->float('difference')->nullable();
            $table->json('data')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('upsell_automation_log_entries');
    }
};
