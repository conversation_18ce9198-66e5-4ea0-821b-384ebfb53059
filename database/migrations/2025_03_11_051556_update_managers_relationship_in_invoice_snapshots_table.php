<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice_snapshots', function (Blueprint $table) {
            if (Schema::hasIndex('invoice_snapshots', 'invoice_snapshots_account_manager_id_foreign')) {
                $table->dropForeign('invoice_snapshots_account_manager_id_foreign');
            }

            if (Schema::hasIndex('invoice_snapshots', 'invoice_snapshots_success_manager_id_foreign')) {
                $table->dropForeign('invoice_snapshots_success_manager_id_foreign');
            }

            $table->unsignedBigInteger('business_development_manager_id')->nullable();

            $table->index('business_development_manager_id');
            $table->index('account_manager_id');
            $table->index('success_manager_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_snapshots', function (Blueprint $table) {
            $table->dropIndex('invoice_snapshots_business_development_manager_id_index');
            $table->dropIndex('invoice_snapshots_account_manager_id_index');
            $table->dropIndex('invoice_snapshots_success_manager_id_index');

            $table->dropColumn('business_development_manager_id');
        });
    }
};
