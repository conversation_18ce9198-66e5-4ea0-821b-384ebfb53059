<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bundle_invoices', function (Blueprint $table) {
            $table->string('billing_version')->default('v1');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bundle_invoices', function (Blueprint $table) {
            $table->dropColumn('billing_version');
        });
    }
};
