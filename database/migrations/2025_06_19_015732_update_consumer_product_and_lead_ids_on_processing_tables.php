<?php

use App\Console\Commands\ZeroNullValuesInProcessingTables;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        foreach ($this->getTables() as $table) {
            Schema::table($table, function (Blueprint $table) {
                $table->unsignedBigInteger('lead_id')->nullable()->change();
                $table->unsignedBigInteger('consumer_product_id')->change();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        foreach ($this->getTables() as $table) {
            Schema::table($table, function (Blueprint $table) {
                $table->unsignedBigInteger('lead_id')->change();
                $table->unsignedBigInteger('consumer_product_id')->nullable()->change();
            });
        }
    }

    private function getTables(): array
    {
        return ZeroNullValuesInProcessingTables::MODIFY_TABLES;
    }
};
