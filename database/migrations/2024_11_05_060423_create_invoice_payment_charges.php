<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_payment_charges', function (Blueprint $table) {
            $table->id();
            $table->uuid();
            $table->unsignedBigInteger('invoice_payment_id');
            $table->unsignedBigInteger('payment_method_id');
            $table->string('transaction_uuid')->nullable();
            $table->unsignedBigInteger('total');
            $table->string('status');
            $table->string('error_message')->nullable();

            $table->foreign('invoice_payment_id')->references('id')->on('invoice_payments');
            $table->foreign('payment_method_id')->references('id')->on('company_payment_methods');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_payment_charges');
    }
};
