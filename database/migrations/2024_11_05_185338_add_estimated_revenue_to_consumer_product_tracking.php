<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('consumer_product_tracking', function (Blueprint $table) {
            $table->decimal('estimated_revenue')->default(0)->after('conversion_uploaded');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('consumer_product_tracking', function (Blueprint $table) {
            $table->dropColumn('estimated_revenue');
        });
    }
};
