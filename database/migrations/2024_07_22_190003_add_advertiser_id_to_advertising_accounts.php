<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('advertising_accounts', function (Blueprint $table) {
            $table->foreignId('advertiser_id')->after('platform');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('advertising_accounts', function (Blueprint $table) {
            $table->dropColumn(['advertiser_id']);
        });
    }
};
