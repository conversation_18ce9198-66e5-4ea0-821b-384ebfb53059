<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice_refund_charges', function (Blueprint $table) {
            $table->string('refund_transaction_uuid')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_refund_charges', function (Blueprint $table) {
            $table->dropColumn('refund_transaction_uuid');
        });
    }
};
