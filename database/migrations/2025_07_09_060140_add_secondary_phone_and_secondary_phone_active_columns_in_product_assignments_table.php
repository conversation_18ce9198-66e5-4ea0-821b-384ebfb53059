<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_assignments', function (Blueprint $table) {
            $table->string('proxy_phone')->index()->nullable()->default(null);
            $table->boolean('proxy_phone_active')->index()->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_assignments', function (Blueprint $table) {
            $table->dropColumn('proxy_phone');
            $table->dropColumn('proxy_phone_active');
        });
    }
};
