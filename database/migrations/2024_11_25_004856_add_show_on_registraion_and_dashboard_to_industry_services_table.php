<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Odin\IndustryService;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table(IndustryService::TABLE, function (Blueprint $table) {
            $table->boolean(IndustryService::FIELD_SHOW_ON_REGISTRATION)->default(true);
            $table->boolean(IndustryService::FIELD_SHOW_ON_DASHBOARD)->default(true);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table(IndustryService::TABLE, function (Blueprint $table) {
            $table->dropColumn(IndustryService::FIELD_SHOW_ON_REGISTRATION);
            $table->dropColumn(IndustryService::FIELD_SHOW_ON_DASHBOARD);
        });
    }
};
