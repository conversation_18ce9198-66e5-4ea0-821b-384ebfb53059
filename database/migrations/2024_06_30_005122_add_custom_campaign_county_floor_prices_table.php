<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('custom_campaign_county_floor_prices', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_campaign_id');
            $table->unsignedBigInteger('state_location_id');
            $table->unsignedBigInteger('county_location_id');
            $table->unsignedBigInteger('sale_type_id');
            $table->unsignedBigInteger('quality_tier_id');
            $table->unsignedBigInteger('property_type_id');
            $table->float('price');
            $table->timestamps();
            $table->index(['state_location_id', 'county_location_id', 'company_campaign_id', 'quality_tier_id', 'property_type_id', 'sale_type_id'], 'custom_county_floor_price_compound_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('custom_campaign_county_floor_prices');
    }
};
