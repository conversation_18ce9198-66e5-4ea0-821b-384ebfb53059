<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lead_refund_item_refunds', function (Blueprint $table) {
            $table->uuid('internal_refund_uuid')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lead_refund_item_refunds', function (Blueprint $table) {
            $table->dropColumn('internal_refund_uuid');
        });
    }
};
