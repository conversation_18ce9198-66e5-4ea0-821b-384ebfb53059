<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        collect([
            'sales_intel_company_import_records',
            'sales_intel_failed_company_import_records',
        ])->each(fn ($table) => Schema::table($table, function ($table) {
            $table->renameColumn('location_type', 'filter');
            $table->renameColumn('location', 'value');
        }));
    }
};
