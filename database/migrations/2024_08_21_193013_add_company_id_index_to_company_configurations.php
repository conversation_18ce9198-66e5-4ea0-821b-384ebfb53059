<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_configurations', function (Blueprint $table) {
            $table->index(['company_id', 'never_exceed_budget'], 'company_configurations_company_id_never_exceed_budget_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_configurations', function (Blueprint $table) {
            $table->dropIndex('company_configurations_company_id_never_exceed_budget_index');
        });
    }
};
