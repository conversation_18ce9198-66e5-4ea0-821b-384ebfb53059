<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('billing_profiles', function (Blueprint $table) {
            $table->id();
            $table->string('payment_gateway_client_code');
            $table->string('payment_method');
            $table->string('billing_contact');
            $table->string('billing_frequency_cron');
            $table->unsignedBigInteger('campaign_id')->nullable();
            $table->unsignedBigInteger('company_id')->nullable();
            $table->unsignedBigInteger('created_by_id');
            $table->integer('threshold_in_dollars');
            $table->integer('max_allowed_charge_attempts');
            $table->timestamp('last_billed_at')->nullable();

            $table->foreign('campaign_id')->references('id')->on('company_campaigns');
            $table->foreign('company_id')->references('id')->on('companies');
            $table->foreign('created_by_id')->references('id')->on('company_users');

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('billing_profiles');
    }
};
