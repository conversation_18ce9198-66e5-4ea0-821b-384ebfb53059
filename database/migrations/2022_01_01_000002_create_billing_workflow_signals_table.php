<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('billing_workflow_signals', function (Blueprint $table) {
            $table->id();
            $table->foreignId('stored_billing_workflow_id')->index();
            $table->text('method');
            $table->text('arguments')->nullable();
            $table->timestamp('created_at', 6)->nullable();
            $table->index(['stored_billing_workflow_id', 'created_at'], 'stored_workflow_id_created_at_index');
            $table->foreign('stored_billing_workflow_id')->references('id')->on('billing_workflows');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('billing_workflow_signals');
    }
};

