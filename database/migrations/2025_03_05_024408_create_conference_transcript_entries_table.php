<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('conference_transcript_entries', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('conference_transcript_id');
            $table->unsignedBigInteger('conference_participant_id')->nullable();
            $table->string('external_id');
            $table->string('external_participant_id');
            $table->timestamp('start_time');
            $table->timestamp('end_time');
            $table->string('text')->nullable();

            $table->foreign('conference_transcript_id')->references('id')->on('conference_transcripts')->noActionOnDelete();
            $table->foreign('conference_participant_id')->references('id')->on('conference_participants')->noActionOnDelete();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('conference_transcript_entries');
    }
};
