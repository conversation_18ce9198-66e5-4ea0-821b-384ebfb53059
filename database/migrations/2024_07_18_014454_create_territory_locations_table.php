<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('territory_locations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('territory_id');
            $table->unsignedBigInteger('location_id')->index();
            $table->unsignedBigInteger('created_by_id');
            $table->dateTime('active_from')->useCurrent();
            $table->dateTime('active_to')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('territory_id')->references('id')->on('territories');
            //$table->foreign('location_id')->references('id')->on('locations');//legacy model??
            $table->foreign('created_by_id')->references('id')->on('users');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('territory_locations');
    }
};
