<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ping_post_publishers', function (Blueprint $table) {
            $table->renameColumn('ping_post_url', 'ping_url');
            $table->string('post_url')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ping_post_publishers', function (Blueprint $table) {
            $table->renameColumn('ping_url', 'ping_post_url');
            $table->dropColumn('post_url');
        });
    }
};
