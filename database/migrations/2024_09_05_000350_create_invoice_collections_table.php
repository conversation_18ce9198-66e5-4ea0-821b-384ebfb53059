<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_collections', function (Blueprint $table) {
            $table->id();
            $table->uuid();

            $table->foreignId('invoice_id');
            $table->foreignId('user_id');
            $table->timestamp('sent_date');
            $table->string('recovery_status');
            $table->decimal('amount_recovered')->default(0.0);
            $table->decimal('amount_collected');
            $table->timestamp('recovery_date')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_collections');
    }
};
