<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('daily_ad_costs', function (Blueprint $table) {
            $table->unsignedBigInteger('industry_service_id')->nullable();
            $table->unsignedBigInteger('daily_ad_cost_account_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('daily_ad_costs', function (Blueprint $table) {
            $table->dropColumn('industry_service_id');
            $table->dropColumn('daily_ad_cost_account_id');
        });
    }
};
