<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('billing_workflow_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('stored_billing_workflow_id')->index();
            $table->unsignedBigInteger('index');
            $table->timestamp('now', 6);
            $table->text('class');
            $table->text('result')->nullable();
            $table->timestamp('created_at', 6)->nullable();
            $table->unique(['stored_billing_workflow_id', 'index']);
            $table->foreign('stored_billing_workflow_id')->references('id')->on('billing_workflows');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('billing_workflow_logs');
    }
};

