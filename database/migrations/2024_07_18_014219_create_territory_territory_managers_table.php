<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('territory_territory_managers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('territory_id');
            $table->unsignedBigInteger('territory_manager_id');
            $table->dateTime('active_from')->useCurrent();
            $table->dateTime('active_to')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('territory_id')->references('id')->on('territories');
            $table->foreign('territory_manager_id')->references('id')->on('territory_managers');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('territory_territory_managers');
    }
};
