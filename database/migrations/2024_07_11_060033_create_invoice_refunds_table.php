<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_refunds', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid');
            $table->unsignedBigInteger('invoice_id');
            $table->unsignedBigInteger('transaction_id')->nullable();
            $table->decimal('total');
            $table->string('external_charge_id');
            $table->string('status');
            $table->text('reason');
            $table->timestamps();

            $table->foreign('invoice_id')->references('id')->on('invoices');
            $table->foreign('transaction_id')->references('id')->on('invoice_transactions');

        });

        Schema::create('invoice_refund_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('invoice_item_id')->nullable();
            $table->unsignedBigInteger('invoice_refund_id');
            $table->decimal('value');

            $table->foreign('invoice_item_id')->references('id')
                ->on('invoice_items')->onDelete('set null');
            $table->timestamps();

            $table->foreign('invoice_refund_id')->references('id')->on('invoice_refunds');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_refund_items');//gotta drop this one first to remove the constraint on refunds
        Schema::dropIfExists('invoice_refunds');
    }
};
