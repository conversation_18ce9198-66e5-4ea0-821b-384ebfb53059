<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('app_log_relations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('app_log_id');
            $table->morphs('relation');
            $table->timestamps();
            $table->index(['relation_id', 'relation_type'], 'relation_index');
            $table->foreign('app_log_id')->references('id')->on('app_logs')->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('app_log_relations');
    }
};
