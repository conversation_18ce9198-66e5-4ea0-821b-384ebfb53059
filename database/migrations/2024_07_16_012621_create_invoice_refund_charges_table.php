<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_refund_charges', function (Blueprint $table) {
            $table->id();
            $table->uuid()->unique();
            $table->unsignedBigInteger('invoice_refund_id');
            $table->unsignedBigInteger('refunded_payment_id');
            $table->decimal('amount');
            $table->string('request_status');
            $table->unsignedBigInteger('refund_transaction_id')->nullable();
            $table->timestamps();

            $table->foreign('invoice_refund_id')->references('id')->on('invoice_refunds');
            $table->foreign('refunded_payment_id')->references('id')->on('invoice_transactions');
            $table->foreign('refund_transaction_id')->references('id')->on('invoice_transactions');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_refund_charges');
    }
};
