<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_snapshots', function (Blueprint $table) {
            $table->id();
            $table->foreignId('invoice_id');
            $table->foreignId('company_id');
            $table->foreignId('account_manager_id')->nullable();
            $table->foreignId('success_manager_id')->nullable();
            $table->string('status');
            $table->decimal('total_value', 10);

            $table->foreign('invoice_id')->references('id')->on('invoices')->noActionOnDelete();
            $table->foreign('company_id')->references('id')->on('companies')->noActionOnDelete();
            $table->foreign('account_manager_id')->references('id')->on('account_managers')->noActionOnDelete();
            $table->foreign('success_manager_id')->references('id')->on('success_managers')->noActionOnDelete();
            $table->timestamps();
        });

        Schema::dropIfExists('receivable_invoice_snapshots');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_snapshots');
        Schema::create('receivable_invoice_snapshots', function (Blueprint $table) {
            $table->id();
            $table->foreignId('invoice_id');
            $table->foreignId('industry_id')->nullable();
            $table->foreignId('company_id');
            $table->foreignId('account_manager_id')->nullable();
            $table->foreignId('success_manager_id')->nullable();
            $table->string('status');
            $table->timestamp('transition_date');
            $table->decimal('total_outstanding', 10);

            $table->foreign('invoice_id')->references('id')->on('invoices')->noActionOnDelete();
            $table->foreign('industry_id')->references('id')->on('industries')->noActionOnDelete();
            $table->foreign('company_id')->references('id')->on('companies')->noActionOnDelete();
            $table->foreign('account_manager_id')->references('id')->on('account_managers')->noActionOnDelete();
            $table->foreign('success_manager_id')->references('id')->on('success_managers')->noActionOnDelete();
            $table->timestamps();
        });
    }
};
