<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ping_post_affiliates', function (Blueprint $table) {
            $table->unsignedBigInteger('affiliate_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ping_post_affiliates', function (Blueprint $table) {
            $table->dropColumn('affiliate_id');
        });
    }
};
