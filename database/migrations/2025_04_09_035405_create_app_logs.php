<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('app_logs', function (Blueprint $table) {
            $table->id();

            $table->text('message');
            $table->string('level');
            $table->string('feature')->nullable();
            $table->string('function')->nullable();
            $table->text('stack_trace')->nullable();
            $table->string('line')->nullable();
            $table->string('file')->nullable();
            $table->json('context')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('app_logs');
    }
};
