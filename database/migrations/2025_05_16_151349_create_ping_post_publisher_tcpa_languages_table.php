<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ping_post_publisher_tcpa_languages', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('industry_service_id')->nullable();
            $table->unsignedBigInteger('ping_post_publisher_id')->nullable();
            $table->longText('tcpa_text');
            $table->json('data')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ping_post_publisher_tcpa_languages');
    }
};
