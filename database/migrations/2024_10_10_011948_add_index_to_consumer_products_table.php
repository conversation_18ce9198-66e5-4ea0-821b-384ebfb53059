<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('consumer_products', function (Blueprint $table) {
            $table->index('cloned_from_id', 'cloned_from_id_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('consumer_products', function (Blueprint $table) {
            $table->dropIndex('cloned_from_id_index');
        });
    }
};
