<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('marketing_campaign_consumers', function (Blueprint $table) {
            $table->dateTime('delivered_at')->nullable(); //email delivered event
            $table->dateTime('opened_at')->nullable(); //email opened event
            $table->dateTime('clicked_at')->nullable(); //email clicked event
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('marketing_campaign_consumers', function (Blueprint $table) {
            $table->dropColumn('delivered_at');
            $table->dropColumn('opened_at');
            $table->dropColumn('clicked_at');
        });
    }
};
