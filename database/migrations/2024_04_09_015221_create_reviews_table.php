<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reviews', function (Blueprint $table) {
            $table->id();
            $table->uuid();
            $table->unsignedBigInteger('reviewer_id');
            $table->unsignedBigInteger('company_id');
            $table->unsignedBigInteger('review_data_id');
            $table->unsignedBigInteger('industry_id')->nullable();
            $table->unsignedBigInteger('industry_service_id')->nullable();
            $table->unsignedBigInteger('website_id');
            $table->integer('status');
            $table->unsignedBigInteger('approver_user_id');
            $table->dateTime('approved_at')->nullable();
            $table->boolean('is_verified')->default(false);
            $table->timestamps();
            $table->softDeletes()->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reviews');
    }
};
