<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('consumer_processing_activities', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('consumer_id')->index();
            $table->unsignedBigInteger('consumer_product_id');
            $table->unsignedBigInteger('user_id');
            $table->string('summary');
            $table->string('comment')->nullable();
            $table->string('activity_type');
            $table->unsignedBigInteger('activity_id')->nullable();
            $table->unsignedTinyInteger('scope')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('consumer_processing_activities');
    }
};
