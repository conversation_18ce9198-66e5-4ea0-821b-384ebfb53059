<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ping_post_publisher_requests', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('ping_post_publisher_id');
            $table->unsignedBigInteger('ping_post_publisher_api_key_id')->nullable();
            $table->unsignedBigInteger('consumer_product_id')->nullable();
            $table->string('destination');
            $table->json('headers');
            $table->json('request');
            $table->boolean('processed')->default(false);
            $table->json('response')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ping_post_publisher_requests');
    }
};
