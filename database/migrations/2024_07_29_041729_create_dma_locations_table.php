<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('dma_locations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('dma_id');
            $table->unsignedBigInteger('location_id');
            $table->string('state_key')->nullable();
            $table->string('state_fips')->nullable();
            $table->string('county_name')->nullable();
            $table->string('county_fips')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dma_locations');
    }
};
