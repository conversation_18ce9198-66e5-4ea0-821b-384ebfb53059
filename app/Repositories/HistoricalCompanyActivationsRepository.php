<?php

namespace App\Repositories;

use App\Enums\CompanyConsolidatedStatus;
use App\Models\HistoricalCompanyActivation;
use App\Models\Odin\Company;
use App\Models\Odin\HistoricalCompanyStatuses;
use App\Models\Odin\ProductAssignment;
use Carbon\CarbonImmutable;
use Carbon\CarbonPeriodImmutable;

class HistoricalCompanyActivationsRepository
{
    /**
     * @return void
     */
    public function determineReactivations(): void
    {
        $now = CarbonImmutable::now('UTC');

        $lastNinetyDays = $now->subDays(90);

        $period = CarbonPeriodImmutable::since($lastNinetyDays)->days(1)->until($now->subDay());

        $dates = [];
        foreach($period as $date) {
            if(empty($dates[$date->year])) {
                $dates[$date->year] = [];
            }

            $dates[$date->year][] = $date->format('Y-m-d');
        }

        $activeWithin90Days = [];
        $currentStatuses = [];
        foreach($dates as $year => $yearDates) {
            HistoricalCompanyStatuses::query()
                ->with([
                    HistoricalCompanyStatuses::RELATION_COMPANY => function($with) {
                        $with->select([
                            Company::TABLE.'.'.Company::FIELD_ID,
                            Company::TABLE.'.'.Company::FIELD_CONSOLIDATED_STATUS
                        ]);
                    },
                    HistoricalCompanyStatuses::RELATION_PRODUCT_ASSIGNMENTS => function($with) {
                        $with
                            ->selectRaw(implode(',', [
                                ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_COMPANY_ID,
                                "COUNT(".ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_ID.") AS count",
                                "MIN(".ProductAssignment::TABLE.'.'.ProductAssignment::CREATED_AT.") AS first_purchase_date",
                            ]))
                            ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CHARGEABLE, true)
                            ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_DELIVERED, true);
                    }
                ])
                ->where(HistoricalCompanyStatuses::FIELD_YEAR, $year)
                ->chunk(1000, function ($chunk) use ($year, $yearDates, &$activeWithin90Days, &$currentStatuses) {
                    foreach($chunk as $companyData) {
                        $companyId = $companyData->{HistoricalCompanyStatuses::FIELD_COMPANY_ID};

                        $companyStatus = $companyData
                            ->{HistoricalCompanyStatuses::RELATION_COMPANY}
                            ->{Company::FIELD_CONSOLIDATED_STATUS}
                            ->value;

                        $purchases = $companyData
                            ->{Company::RELATION_PRODUCT_ASSIGNMENTS}
                            ->first()
                            ?->count ?? 0;

                        $firstPurchaseDate = $companyData
                            ->{Company::RELATION_PRODUCT_ASSIGNMENTS}
                            ->first()
                            ?->first_purchase_date;

                        $currentStatuses[$companyId] = [
                            'active' => $companyStatus === CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS->value,
                            'purchases' => $purchases
                        ];

                        if(!empty($activeWithin90Days[$companyId])) {
                            continue;
                        }

                        $activeWithin90Days[$companyId] = $companyData
                            ->{HistoricalCompanyStatuses::FIELD_DAILY_STATUSES}
                            ->toCollection()
                            ->only($yearDates)
                            ->filter(function(int $status, string $statusDate) use ($firstPurchaseDate) {
                                return $statusDate >= $firstPurchaseDate;
                            })
                            ->contains(CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS->value);
                    }
                });
        }

        $dateTime = $now->format('Y-m-d H:i:s');

        $deactivations = HistoricalCompanyActivation::query()
            ->whereIntegerInRaw(HistoricalCompanyActivation::FIELD_COMPANY_ID, array_keys($currentStatuses))
            ->where(HistoricalCompanyActivation::FIELD_TYPE, HistoricalCompanyActivation::DEACTIVATION)
            ->where(HistoricalCompanyActivation::FIELD_STATUS_DATE, $dateTime)
            ->pluck(HistoricalCompanyActivation::FIELD_COMPANY_ID)
            ->toArray();

        $insertRows = [];
        foreach($currentStatuses as $companyId => $status) {
            if($status['active']
            && empty($activeWithin90Days[$companyId])
            && $status['purchases'] > 1) {
                $insertRows[] = [
                    HistoricalCompanyActivation::FIELD_COMPANY_ID => $companyId,
                    HistoricalCompanyActivation::FIELD_STATUS_DATE => $dateTime,
                    HistoricalCompanyActivation::FIELD_TYPE => HistoricalCompanyActivation::REACTIVATION
                ];
            }
            else if($status['active']
                && empty($activeWithin90Days[$companyId])
                && $status['purchases'] === 1) {
                $insertRows[] = [
                    HistoricalCompanyActivation::FIELD_COMPANY_ID => $companyId,
                    HistoricalCompanyActivation::FIELD_STATUS_DATE => $dateTime,
                    HistoricalCompanyActivation::FIELD_TYPE => HistoricalCompanyActivation::ACTIVATION
                ];
            }
            else if(empty($status['active'])
                && $activeWithin90Days[$companyId]
                && !in_array($companyId, $deactivations, true)) {
                $insertRows[] = [
                    HistoricalCompanyActivation::FIELD_COMPANY_ID => $companyId,
                    HistoricalCompanyActivation::FIELD_STATUS_DATE => $dateTime,
                    HistoricalCompanyActivation::FIELD_TYPE => HistoricalCompanyActivation::DEACTIVATION
                ];
            }

            if(count($insertRows) >= 500) {
                HistoricalCompanyActivation::query()->insert($insertRows);

                $insertRows = [];
            }
        }

        if(!empty($insertRows)) {
            HistoricalCompanyActivation::query()->insert($insertRows);
        }
    }
}
