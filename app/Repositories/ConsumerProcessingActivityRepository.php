<?php

namespace App\Repositories;

use App\Enums\ConsumerProcessing\ConsumerProcessingActivityType;
use App\Models\ConsumerProcessingActivity;
use Exception;

class ConsumerProcessingActivityRepository
{
    protected array $fieldsCanBeUpdated = [
        ConsumerProcessingActivity::FIELD_SUMMARY,
        ConsumerProcessingActivity::FIELD_COMMENT,
        ConsumerProcessingActivity::FIELD_SCOPE,
    ];

    /**
     * @param ConsumerProcessingActivityType $type
     * @param array $payload
     * @return ConsumerProcessingActivity
     * @throws Exception
     */
    public function create(ConsumerProcessingActivityType $type, array $payload): ConsumerProcessingActivity
    {
        if ($type->hasRelatedActivity())
            return $this->createWithRelatedActivity($type, $payload);

        return ConsumerProcessingActivity::query()
            ->create([
                ...$payload,
                ConsumerProcessingActivity::FIELD_ACTIVITY_TYPE => $type,
                ConsumerProcessingActivity::CREATED_AT          => now(),
            ]);
    }

    /**
     * @param ConsumerProcessingActivityType $type
     * @param array $payload
     * @return ConsumerProcessingActivity
     * @throws Exception
     */
    public function createWithRelatedActivity(ConsumerProcessingActivityType $type, array $payload): ConsumerProcessingActivity
    {
        if (!$type->hasRelatedActivity())
            return $this->create($type, $payload);

        if (!$payload[ConsumerProcessingActivity::FIELD_ACTIVITY_ID] ?? false)
            throw new Exception("Error creating " . ConsumerProcessingActivity::class . ": type '$type->value' requires a related activity_id");

        return ConsumerProcessingActivity::query()
            ->create([
                ...$payload,
                ConsumerProcessingActivity::FIELD_ACTIVITY_TYPE => $type,
                ConsumerProcessingActivity::CREATED_AT          => now(),
            ]);
    }

    /**
     * @param ConsumerProcessingActivity $consumerProcessingActivity
     * @param array $payload
     * @return bool
     */
    public function update(ConsumerProcessingActivity $consumerProcessingActivity, array $payload): bool
    {
        $validFields = array_filter($payload, fn($v) => in_array($v, $this->fieldsCanBeUpdated), ARRAY_FILTER_USE_KEY);

        return $consumerProcessingActivity->update($validFields);
    }
}