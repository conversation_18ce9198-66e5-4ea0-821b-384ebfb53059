<?php

namespace App\Repositories\PrivacyManagement;

use App\Enums\PrivacyManagement\PrivacyRequestStatuses;
use App\Models\PrivacyRequest;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

class PrivacyManagementRepository
{
    const string SEARCH_FILTER = 'search';
    const string STATUS_FILTER = 'status';

    /**
     * @param array $filters
     * @return Builder
     */
    public function getPrivacyRequests(array $filters): Builder
    {
        $query = PrivacyRequest::query();

        if (isset($filters[self::SEARCH_FILTER])) {
            $query->whereAny([
                PrivacyRequest::FIELD_ID,
                PrivacyRequest::FIELD_PAYLOAD . '->' . PrivacyRequest::JSON_PAYLOAD_FIELD_EMAIL
            ], 'LIKE', '%'. $filters[self::SEARCH_FILTER] . '%');
        }

        if (isset($filters[self::STATUS_FILTER])) {
            $query->where(PrivacyRequest::FIELD_STATUS, $filters[self::STATUS_FILTER]);
        }

        return $query;
    }

    /**
     * @param int $id
     * @return PrivacyRequest
     */
    public function getPrivacyRequest(int $id): PrivacyRequest
    {
        /** @var PrivacyRequest */
        return PrivacyRequest::query()->findOrFail($id);
    }

    /**
     * @param array $fields
     * @return bool
     */
    public function createPrivacyRequest(array $fields): bool
    {
        $payload = $this->buildPrivacyRequestPayload($fields);

        $privacyRequest = new PrivacyRequest([
            PrivacyRequest::FIELD_UUID => Str::uuid(),
            PrivacyRequest::FIELD_APPROVED_BY_ID => null,
            PrivacyRequest::FIELD_PAYLOAD => $payload,
            PrivacyRequest::FIELD_STATUS => PrivacyRequestStatuses::INITIAL,
            PrivacyRequest::FIELD_SOURCE => $fields['source'],
        ]);

        return $privacyRequest->save();
    }

    /**
     * @param array $fields
     * @return bool
     */
    public function updatePrivacyRequest(PrivacyRequest $privacyRequest, array $fields): bool
    {
        $payload = $this->buildPrivacyRequestPayload($fields, $privacyRequest->getAttribute(PrivacyRequest::FIELD_PAYLOAD));

        $privacyRequest->setAttribute(PrivacyRequest::FIELD_PAYLOAD, $payload);

        if (isset($fields['source'])) {
            $privacyRequest->setAttribute(PrivacyRequest::FIELD_SOURCE, $fields['source']);
        }

        return $privacyRequest->save();
    }

    protected function buildPrivacyRequestPayload(array $fields, array $payload = []): array
    {
        foreach ($fields as $key => $value) {
            if(in_array($key, [
                PrivacyRequest::JSON_PAYLOAD_FIELD_FIRST_NAME,
                PrivacyRequest::JSON_PAYLOAD_FIELD_LAST_NAME,
                PrivacyRequest::JSON_PAYLOAD_FIELD_EMAIL,
                PrivacyRequest::JSON_PAYLOAD_FIELD_PHONE,
                PrivacyRequest::JSON_PAYLOAD_FIELD_DESCRIPTION,
                PrivacyRequest::JSON_PAYLOAD_FIELD_ADDRESS
            ])) {
                $payload[$key] = $value;
            }
        }

        return $payload;
    }
}
