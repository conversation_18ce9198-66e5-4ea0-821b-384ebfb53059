<?php

namespace App\Repositories;


use App\Models\Legacy\LeadCampaignDeliveryMethod;
use App\Models\Legacy\LeadDeliveryMethod;
use App\Repositories\Odin\CompanyRepository;
use Illuminate\Support\Collection;

class LeadCampaignDeliveryMethodRepository
{

    public function __construct(
        protected CompanyRepository $companyRepository,
        protected LeadCampaignRepository $leadCampaignRepository,
    ) {}


    /**
     * @param int $campaignId
     * @return Collection
     */
    public function getDeliveryMethodsForCampaign(int $campaignId): Collection
    {
        return LeadCampaignDeliveryMethod::query()
            ->where(LeadCampaignDeliveryMethod::FIELD_LEAD_CAMPAIGN_ID, $campaignId)
            ->get();
    }

    /**
     * @param int $campaignId
     * @return Collection
     */
    public function getDeliveryMethodsWithContactsForCampaign(int $campaignId): Collection
    {
        return LeadCampaignDeliveryMethod::query()
            ->with([LeadCampaignDeliveryMethod::RELATION_LEAD_DELIVERY_METHOD, LeadCampaignDeliveryMethod::RELATION_LEAD_DELIVERY_METHOD.".".LeadDeliveryMethod::RELATION_COMPANY_CONTACT])
            ->where(LeadCampaignDeliveryMethod::FIELD_LEAD_CAMPAIGN_ID, $campaignId)
            ->get();
    }

    /**
     * Fetch current contacts for a company who are registered as delivery methods for one or more campaigns
     * Use as base front-end delivery options for a new campaign
     * @param int $companyId
     * @return Collection
     */
    public function getBaseDeliveryMethodsForCompany(int $companyId): Collection
    {
        return LeadDeliveryMethod::query()
            ->with(LeadDeliveryMethod::RELATION_COMPANY_CONTACT)
            ->where(LeadDeliveryMethod::FIELD_COMPANY_ID, $companyId)
            ->get();
    }

}
