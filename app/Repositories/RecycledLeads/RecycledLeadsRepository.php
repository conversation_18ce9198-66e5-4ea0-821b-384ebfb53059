<?php

namespace App\Repositories\RecycledLeads;

use App\Enums\ConsumerProductChannel;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\RecycledLeads\RecycledLead;
use App\Services\HelperService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

class RecycledLeadsRepository
{
    /**
     * @param ConsumerProduct $consumerProduct
     * @param ConsumerProductChannel $channel
     *
     * @return ConsumerProduct
     */
    public function cloneConsumerProduct(
        ConsumerProduct $consumerProduct,
        ConsumerProductChannel $channel,
    ): ConsumerProduct
    {
        $clonedConsumer = $consumerProduct->consumer->replicate();
        $clonedConsumer->reference = Str::uuid()->toString();
        $clonedConsumer->created_at = now();
        $clonedConsumer->updated_at = now();
        $clonedConsumer->cloned_from_id = $consumerProduct->consumer->id;
        $clonedConsumer->save();
        $clonedConsumer->refresh();

        $consumerProductData = $consumerProduct->consumerProductData->replicate();
        $consumerProductData->created_at = now();
        $consumerProductData->updated_at = now();
        unset($consumerProductData->ip_address);
        unset($consumerProductData->other_interests);
        $consumerProductData->save();
        $consumerProductData->refresh();

        $clonedConsumerProduct = $consumerProduct->replicate();
        $clonedConsumerProduct->consumer_id = $clonedConsumer->id;
        $clonedConsumerProduct->consumer_product_data_id = $consumerProductData->id;
        $clonedConsumerProduct->created_at = now();
        $clonedConsumerProduct->updated_at = now();
        $clonedConsumerProduct->cloned_from_id = $consumerProduct->id;
        $clonedConsumerProduct->channel = $channel;
        $clonedConsumerProduct->save();
        $clonedConsumerProduct->refresh();

        return $clonedConsumerProduct;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param int $status
     *
     * @return RecycledLead
     */
    public function createRecycledLeadForConsumerProduct(ConsumerProduct $consumerProduct, int $status): RecycledLead
    {
        /** @var RecycledLead */
        return $consumerProduct->recycledLeads()->create([
            RecycledLead::FIELD_STATUS => $status
        ]);
    }
}
