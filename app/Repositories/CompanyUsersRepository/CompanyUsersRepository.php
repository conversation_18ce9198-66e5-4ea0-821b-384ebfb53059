<?php

namespace App\Repositories\CompanyUsersRepository;

use App\Models\Call;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Text;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class CompanyUsersRepository
{
    const int SOLAR_REVIEWS_COMPANY_ID = 1;
    /**
     * Returns paginated company users based on filters applied.
     *
     * @param array $filters
     * @return LengthAwarePaginator
     */
    public function getCompanyUsers(array $filters): LengthAwarePaginator
    {
        $query = CompanyUser::query();

        $query->whereIn(CompanyUser::FIELD_STATUS, [
            CompanyUser::STATUS_ACTIVE, CompanyUser::STATUS_INACTIVE
        ]);

        $query->where(CompanyUser::FIELD_COMPANY_ID, '!=', self::SOLAR_REVIEWS_COMPANY_ID);

        if (isset($filters['company_id'])) {
            $query = $query->where(CompanyUser::FIELD_COMPANY_ID, '=', $filters['company_id']);
        }

        if (isset($filters['decision_maker'])) {
            $query->where(CompanyUser::FIELD_IS_DECISION_MAKER, '=', $filters['decision_maker']);
        }

        if (isset($filters['user_status'])) {
            $query->where(CompanyUser::FIELD_STATUS, '=', $filters['user_status']);
        }

        if (isset($filters['user_details'])) {
            $query->where(function ($query) use ($filters) {
                $query->whereRaw("CONCAT( " . CompanyUser::FIELD_FIRST_NAME .", ' '," . CompanyUser::FIELD_LAST_NAME . ") LIKE ?", ["%{$filters['user_details']}%"])
                    ->orWhere(CompanyUser::FIELD_EMAIL, 'LIKE', '%'. $filters['user_details'] . '%')
                    ->orWhere(CompanyUser::FIELD_FORMATTED_CELL_PHONE, 'LIKE', '%'. $filters['user_details'] . '%')
                    ->orWhere(CompanyUser::FIELD_FORMATTED_OFFICE_PHONE, 'LIKE', '%'. $filters['user_details'] . '%');
            });
        }

        if (isset($filters['user_title'])) {
            $query->whereIn(CompanyUser::FIELD_TITLE, $filters['user_title']);
            if (in_array('no_title', $filters['user_title'])) {
                $query->orWhereNull(CompanyUser::FIELD_TITLE);
            }
        }

        if (isset($filters['last_contacted'])) {

            $finalCalls = Call::query()
                ->select(Call::FIELD_OTHER_NUMBER, DB::raw('MAX(call_end) as latest_contact'))
                ->groupBy(Call::FIELD_OTHER_NUMBER)
                ->get()
                ->filter(function ($call) {
                    return $call->latest_contact !== null;
                });

            $finalTexts = Text::query()
                ->select(Text::FIELD_OTHER_NUMBER, DB::raw('MAX(created_at) as latest_contact'))
                ->groupBy(Text::FIELD_OTHER_NUMBER)
                ->get()
                ->filter(function ($call) {
                    return $call->latest_contact !== null;
                });

            $validContactNumbers = $finalCalls->mergeRecursive($finalTexts);

            $groupedBy = $validContactNumbers->groupBy('other_number');

            $mapped = $groupedBy->map(function ($group) {
                $latest = $group->max('latest_contact');
                return Carbon::parse($latest);
            });

            $carbonFrom = Carbon::parse($filters['last_contacted']['from'])->startOfDay();
            $carbonTo = Carbon::parse($filters['last_contacted']['to'])->endOfDay();

            $filteredByDate = $mapped->filter(function ($key, $value) use ($carbonFrom, $carbonTo) {
                return $key->between($carbonFrom, $carbonTo);
            });

            $keys = $filteredByDate->keys();

            $query->where(function ($query) use ($keys) {
                $query->whereIn(CompanyUser::FIELD_FORMATTED_CELL_PHONE, $keys)
                    ->orWhereIn(CompanyUser::FIELD_FORMATTED_OFFICE_PHONE, $keys);
            });

        }

        if (isset($filters['total_calls_range'])) {
            $callCount = Call::query()
                ->select(Call::FIELD_OTHER_NUMBER, DB::raw('COUNT(*) as call_count'))
                ->groupBy(Call::FIELD_OTHER_NUMBER)
                ->get()
                ->whereBetween('call_count', [$filters['total_calls_range']['min'], $filters['total_calls_range']['max']])
                ->pluck('other_number');

            $query->where(function ($query) use ($callCount) {
                $query->whereIn(CompanyUser::FIELD_FORMATTED_CELL_PHONE, $callCount)
                    ->orWhereIn(CompanyUser::FIELD_FORMATTED_OFFICE_PHONE, $callCount);
            });
        }

        return $query->paginate($filters['perPage'],['*'],'page',$filters['page']);
    }

    /**
     * Grabs every unique company user title.
     *
     * @return Collection
     */
    public function getAllDistinctCompanyUserTitles(): Collection
    {
        return CompanyUser::query()->distinct()->pluck(CompanyUser::FIELD_TITLE)->filter();
    }

}
