<?php

namespace App\Repositories;

use App\Http\Controllers\ContractorProfileController;
use App\Models\ContractorProfile\ContractorProfile;

class ContractorProfileRepository
{

    const array EMPTY_HOURS_ARRAY = [
        'sunday'    => ['open' => null, 'close' => null],
        'monday'    => ['open' => null, 'close' => null],
        'tuesday'   => ['open' => null, 'close' => null],
        'wednesday' => ['open' => null, 'close' => null],
        'thursday'  => ['open' => null, 'close' => null],
        'friday'    => ['open' => null, 'close' => null],
        'saturday'  => ['open' => null, 'close' => null],
    ];

    public function firstOrCreate(int $companyId): ContractorProfile
    {
        return ContractorProfile::firstOrCreate([
            ContractorProfile::FIELD_COMPANY_ID => $companyId
        ]);
    }

    /**
     * @param int $companyId
     * @return array
     */
    public function getBusinessHours(int $companyId): array
    {
        $profile = $this->firstOrCreate($companyId);
        return [
            ContractorProfileController::PARAM_TIMEZONE => $profile->business_hours_timezone,
            ContractorProfileController::PARAM_TIMES    => $profile->business_hours ?? self::EMPTY_HOURS_ARRAY,
        ];
    }

    /**
     * @param int $companyId
     * @return array
     */
    public function getBrandsSold(int $companyId): array
    {
        return $this->firstOrCreate($companyId)->brands_sold ?? [];
    }

    /**
     * @param int $companyId
     * @param array $brands
     * @return void
     */
    public function updateBrandsSold(int $companyId, array $brands): void
    {
        $this->firstOrCreate($companyId)->update([
            ContractorProfile::FIELD_BRANDS_SOLD => $brands
        ]);
    }

    /**
     * @param int $companyId
     * @return array
     */
    public function getCredentials(int $companyId): array
    {
        $profile = $this->firstOrCreate($companyId);
        return [
            ContractorProfile::FIELD_LICENSES       => $profile->licenses ?? [],
            ContractorProfile::FIELD_CERTIFICATIONS => $profile->certifications ?? [],
        ];
    }

    /**
     * @param int $companyId
     * @param array $licenses
     * @param array $certifications
     * @return void
     */
    public function updateCredentials(int $companyId, array $licenses, array $certifications): void
    {
        $this->firstOrCreate($companyId)->update([
            ContractorProfile::FIELD_LICENSES       => $licenses,
            ContractorProfile::FIELD_CERTIFICATIONS => $certifications
        ]);
    }

    /**
     * @param int $companyId
     * @return ContractorProfile
     */
    public function findByCompanyId(int $companyId): ContractorProfile
    {
        return $this->firstOrCreate($companyId);
    }

    /**
     * @param int $companyId
     * @return array
     */
    public function getServicesOffered(int $companyId): array
    {
        return $this->findByCompanyId($companyId)->services ?? [];
    }

    /**
     * @param int $companyId
     * @param array $services
     * @return void
     */
    public function updateServicesOffered(int $companyId, array $services): void
    {
        $this->findByCompanyId($companyId)->update([
            ContractorProfile::FIELD_SERVICES => $services
        ]);
    }

}
