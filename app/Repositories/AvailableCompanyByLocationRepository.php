<?php

namespace App\Repositories;

use App\Enums\Odin\Industry;
use App\Models\AvailableCompanyByLocation;
use App\Models\Legacy\Location;
use App\Repositories\Odin\IndustryRepository;
use App\Services\DatabaseHelperService;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class AvailableCompanyByLocationRepository
{
    const string COLUMN_AVAILABLE_COMPANY_ID = 'available_company_id';
    const string COLUMN_UNLIMITED_BUDGETS = 'unlimited_budgets';

    const string COMPANIES_COUNT = 'companies_count';
    const string UNLIMITED_BUDGETS = 'unlimited_budgets';

    public function __construct(protected IndustryRepository $industryRepository)
    {
    }

    /**
     * @param Industry $industry
     * @param array $countyLocationIds
     * @return Collection
     */
    public function getAvailableRevenueByCounty(Industry $industry, array $countyLocationIds): Collection
    {
        return Location::query()
            ->leftJoin(DatabaseHelperService::database().'.'.AvailableCompanyByLocation::TABLE, function($join) use ($industry) {
                $join
                    ->on(
                        DatabaseHelperService::database().'.'.AvailableCompanyByLocation::TABLE.'.'.AvailableCompanyByLocation::FIELD_COUNTY_LOCATION_ID,
                        '=',
                        Location::TABLE.'.'.Location::ID
                    )
                    ->where(DatabaseHelperService::database().'.'.AvailableCompanyByLocation::TABLE.'.'.AvailableCompanyByLocation::FIELD_INDUSTRY_SLUG, $industry->getSlug());
            })
            ->whereIntegerInRaw(Location::TABLE.'.'.Location::ID, $countyLocationIds)
            ->select([
                Location::TABLE.'.'.Location::ID,
                DatabaseHelperService::database().'.'.AvailableCompanyByLocation::TABLE.'.'.AvailableCompanyByLocation::FIELD_COMPANY_ID.' AS '.self::COLUMN_AVAILABLE_COMPANY_ID,
                DatabaseHelperService::database().'.'.AvailableCompanyByLocation::TABLE.'.'.AvailableCompanyByLocation::FIELD_UNLIMITED_BUDGETS.' AS '.self::COLUMN_UNLIMITED_BUDGETS
            ])
            ->get()
            ->groupBy(Location::ID)
            ->map(function($locations) {
                $companyIds = [];
                $unlimitedBudgets = 0;

                foreach($locations as $location) {
                    $companyIds[] = Arr::get($location, self::COLUMN_AVAILABLE_COMPANY_ID);

                    if($companyUnlimitedBudgets = Arr::get($location, self::COLUMN_UNLIMITED_BUDGETS)) {
                        $unlimitedBudgets += $companyUnlimitedBudgets;
                    }
                }

                return [
                    self::COMPANIES_COUNT => count(array_unique(array_filter($companyIds))),
                    self::UNLIMITED_BUDGETS => $unlimitedBudgets
                ];
            });
    }
}
