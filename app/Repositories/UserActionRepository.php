<?php

namespace App\Repositories;

use App\Models\UserAction;
//use App\Models\ActionTag;
use App\Models\UserActionTag;
use App\Services\Odin\UserActionService;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class UserActionRepository
{

    public function __construct(protected UserActionService $actionService)
    {

    }

    /**
     * @param int $userId
     * @param string $subject
     * @param string $message
     * @param string|null $displayDate
     * @param bool|null $tagByEmail
     * @return UserAction
     */
    public function createAction(
        int $userId,
        string $subject,
        string $message,
        ?string $displayDate = null,
        ?bool $tagByEmail = null
    ): UserAction {
        /** @var UserAction $action */
        $action = UserAction::query()->create([
            UserAction::FIELD_USER_ID      => $userId,
            UserAction::FIELD_SUBJECT           => $subject,
            UserAction::FIELD_MESSAGE           => $message,
            UserAction::FIELD_DISPLAY_DATE      => strlen($displayDate) > 0 ? Carbon::createFromDate($displayDate) : null,
            UserAction::FIELD_TAG_BY_EMAIL      => $tagByEmail
        ]);

        return $action;
    }

    /**
     * @param int $userId
     * @param string $subject
     * @param string $message
     * @param int|null $actionId
     * @param string|null $displayDate
     * @param bool|null $tagByEmail
     * @return UserAction
     */
    public function updateOrCreate(
        int $userId,
        string $subject,
        string $message,
        ?int $actionId = null,
        ?string $displayDate = null,
        ?bool $tagByEmail = null
    ): UserAction {
        /** @var UserAction $action */
        $action = UserAction::query()->updateOrCreate(
            [
                UserAction::FIELD_ID => $actionId
            ],
            [
                UserAction::FIELD_USER_ID => $userId,
                UserAction::FIELD_SUBJECT => $subject,
                UserAction::FIELD_MESSAGE => $message,
                UserAction::FIELD_DISPLAY_DATE => strlen($displayDate) > 0 ? Carbon::createFromDate($displayDate) : null,
                UserAction::FIELD_TAG_BY_EMAIL => $tagByEmail
            ]
        );

        return $action;
    }

    /**
     * @param int $actionId
     * @return bool
     */
    public function toggleActionPin(int $actionId): bool
    {
        $targetAction = UserAction::query()->findOrFail($actionId);
        return $targetAction?->update([UserAction::FIELD_PINNED => !$targetAction->{UserAction::FIELD_PINNED}]) ?? false;
    }

    /**
     * @param UserAction $action
     * @param array $userIds
     */
    public function editActionTags(UserAction $action, array $userIds): void
    {
        /** @var Collection $actionTags */
        $actionTags = $action->{UserAction::RELATION_TAGS}()->get();
        $newTags = [];

        foreach ($userIds as $userId) {
            /** @var UserActionTag|null $existingTag */
            $existingTag = $actionTags->first(function($tag) use ($userId) {
                return $tag->{UserActionTag::FIELD_USER_ID} === $userId;
            });

            if (!$existingTag) {
                $tag = $this->createTag($action->{UserAction::FIELD_ID}, $userId);
                $newTags[] = $tag->toArray();
            }
        }
        if (!empty($newTags)) {
            $this->actionService->tagUsers($action, array_column($newTags, UserActionTag::FIELD_USER_ID));
        }

        foreach ($actionTags as $tag) {
            if (!in_array($tag->{UserActionTag::FIELD_USER_ID}, $userIds)) {
                $this->removeTag($tag);
            }
        }
    }

    /**
     * @param int $actionId
     * @param int $userId
     * @return UserActionTag
     */
    public function createTag(int $actionId, int $userId): UserActionTag
    {
        /** @var UserActionTag $tag */
        $tag = UserActionTag::query()->create([
            UserActionTag::FIELD_USER_ID      => $userId,
            UserActionTag::FIELD_ACTION_ID    => $actionId,
        ]);

        return $tag;
    }

    /**
     * @param UserActionTag $tag
     * @return bool
     */
    public function removeTag(UserActionTag $tag): bool
    {
        return $tag->delete();
    }


}
