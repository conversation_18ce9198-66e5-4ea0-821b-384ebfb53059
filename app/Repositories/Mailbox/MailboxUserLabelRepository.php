<?php
namespace App\Repositories\Mailbox;

use App\Models\Mailbox\MailboxUserEmail;
use App\Models\Mailbox\MailboxUserLabel;
use Illuminate\Database\Eloquent\Collection;

class MailboxUserLabelRepository
{
    /**
     * @param int $userId
     * @return Collection
     */
    public function getMailboxLabels(int $userId): Collection
    {
        return MailboxUserLabel::query()
            ->where(MailboxUserLabel::FIELD_USER_ID, $userId)
            ->get();
    }
}