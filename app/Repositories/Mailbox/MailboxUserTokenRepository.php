<?php

namespace App\Repositories\Mailbox;

use App\Models\Mailbox\MailboxUserToken;
use App\Models\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class MailboxUserTokenRepository
{

    /**
     * @param User $user
     * @return ?MailboxUserToken
     */
    public function getLatestUserEmailToken(User $user): ?MailboxUserToken
    {
        /** @var MailboxUserToken $mailboxUserToken */
        $mailboxUserToken = $user->mailboxTokens()->latest()->first();

        return $mailboxUserToken;
    }

    /**
     * @param array|null $userIds
     * @return Collection
     */
    public function getAllMailboxUsers(array $userIds = null): Collection
    {
        return MailboxUserToken::query()
            ->when(filled($userIds), fn ($query) => $query->where(MailboxUserToken::FIELD_USER_ID, $userIds))
            ->whereHas(MailboxUserToken::RELATION_USER, fn ($query) => $query->where(User::FIELD_USES_MAILBOX, true))
            ->with(MailboxUserToken::RELATION_USER)
            ->get()
            ->pluck(MailboxUserToken::RELATION_USER);
    }

    /**
     * @param array $payload
     * @return MailboxUserToken
     */
    public function createOne(array $payload): MailboxUserToken
    {
        $mailboxUserToken = new MailboxUserToken();

        $mailboxUserToken->fill([
            MailboxUserToken::FIELD_USER_ID         => Arr::get($payload, MailboxUserToken::FIELD_USER_ID),
            MailboxUserToken::FIELD_TOKEN           => Arr::get($payload, MailboxUserToken::FIELD_TOKEN),
            MailboxUserToken::FIELD_REFRESH_TOKEN   => Arr::get($payload, MailboxUserToken::FIELD_REFRESH_TOKEN),
        ]);

        $mailboxUserToken->save();

        return $mailboxUserToken;
    }
}
