<?php

namespace App\Repositories\Mailbox;

use App\Builders\MailboxUserEmailBuilder;
use App\DTO\Mailbox\ListUserEmailsParam;
use App\Enums\ContactIdentification\SearchableFieldType;
use App\Enums\Mailbox\EmailRecipientType;
use App\Models\ContactIdentification\IdentifiedContact;
use App\Models\Mailbox\MailboxEmail;
use App\Models\Mailbox\MailboxEmailRecipient;
use App\Models\User;
use App\Services\ContactIdentification\ContactIdentificationService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;

class MailboxEmailRepository
{
    public function __construct(protected ContactIdentificationService $contactIdentificationService)
    {

    }

    /**
     * @param int $userId
     * @param ListUserEmailsParam $listUserEmailsParam
     * @return Builder
     * @throws \Exception
     */
    public function getListUserEmailsQuery(int $userId, ListUserEmailsParam $listUserEmailsParam): Builder
    {
        $user = User::query()->findOrFail($userId);

        return MailboxUserEmailBuilder::query()
            ->setTextQuery($listUserEmailsParam->getTextQuery())
            ->setForUser($user)
            ->setForCategory($listUserEmailsParam->getTab())
            ->setForEmailUuid($listUserEmailsParam->getUuid())
            ->forRelatedCompanyId($listUserEmailsParam->getCompanyId())
            ->getQuery();
    }

    /**
     * @param array $payload
     * @param array $to
     * @param array|null $bcc
     * @param array|null $cc
     * @return MailboxEmail
     */
    public function createEmailAndRecipients(
        array $payload,
        array $to,
        ?array $bcc = [],
        ?array $cc = [],
    ): MailboxEmail
    {
        $mailboxEmail = new MailboxEmail();

        $identifiedContact = $this->contactIdentificationService->createIdentifiedContactAndDispatchJob(
            Arr::get($payload, MailboxEmail::FIELD_FROM_USER_EMAIL),
            SearchableFieldType::EMAIL
        );

        $mailboxEmail->{MailboxEmail::FIELD_CONTENT}                    = Arr::get($payload, MailboxEmail::FIELD_CONTENT);
        $mailboxEmail->{MailboxEmail::FIELD_FROM_USER_ID}               = Arr::get($payload, MailboxEmail::FIELD_FROM_USER_ID);
        $mailboxEmail->{MailboxEmail::FIELD_FROM_USER_EMAIL}            = Arr::get($payload, MailboxEmail::FIELD_FROM_USER_EMAIL);
        $mailboxEmail->{MailboxEmail::FIELD_DIRECTION}                  = Arr::get($payload, MailboxEmail::FIELD_DIRECTION);
        $mailboxEmail->{MailboxEmail::FIELD_SUBJECT}                    = Arr::get($payload, MailboxEmail::FIELD_SUBJECT);
        $mailboxEmail->{MailboxEmail::FIELD_SNIPPET}                    = Arr::get($payload, MailboxEmail::FIELD_SNIPPET);
        $mailboxEmail->{MailboxEmail::FIELD_TYPE}                       = Arr::get($payload, MailboxEmail::FIELD_TYPE);
        $mailboxEmail->{MailboxEmail::FIELD_EXTERNAL_MESSAGE_ID}        = Arr::get($payload, MailboxEmail::FIELD_EXTERNAL_MESSAGE_ID);
        $mailboxEmail->{MailboxEmail::FIELD_CREATED_AT}                 = Arr::get($payload, MailboxEmail::FIELD_CREATED_AT);
        $mailboxEmail->{MailboxEmail::FIELD_UPDATED_AT}                 = Arr::get($payload, MailboxEmail::FIELD_UPDATED_AT);
        $mailboxEmail->{MailboxEmail::FIELD_FROM_IDENTIFIED_CONTACT_ID} = $identifiedContact->{IdentifiedContact::FIELD_ID};
        $mailboxEmail->{MailboxEmail::FIELD_FROM_A20}                   = Arr::get($payload, MailboxEmail::FIELD_FROM_A20);

        $mailboxEmail->save();

        $this->createEmailRecipients($mailboxEmail, $to, $bcc, $cc);

        return $mailboxEmail;
    }

    /**
     * @param MailboxEmail $mailboxEmail
     * @param array $to
     * @param array $bcc
     * @param array $cc
     * @return void
     */
    public function createEmailRecipients(MailboxEmail $mailboxEmail, array $to = [], array $bcc = [], array $cc = []): void
    {
        $recipientTypes = [
            EmailRecipientType::BCC->value  => $bcc,
            EmailRecipientType::TO->value   => $to,
            EmailRecipientType::CC->value   => $cc,
        ];

        foreach ($recipientTypes as $type => $addresses) {
            foreach ($addresses as $emailAddress) {
                $identifiedContact = $this->contactIdentificationService->createIdentifiedContactAndDispatchJob(
                    $emailAddress,
                    SearchableFieldType::EMAIL
                );

                $exists = MailboxEmailRecipient::query()
                    ->where(MailboxEmailRecipient::FIELD_IDENTIFIED_CONTACT_ID, $identifiedContact->{IdentifiedContact::FIELD_ID})
                    ->where(MailboxEmailRecipient::FIELD_EMAIL_ID, $mailboxEmail->{MailboxEmail::FIELD_ID})
                    ->first();

                // TODO - Use first or create/upsert
                if (!isset($exists)) {
                    MailboxEmailRecipient::query()->create([
                        MailboxEmailRecipient::FIELD_EMAIL_ID               => $mailboxEmail->{MailboxEmail::FIELD_ID},
                        MailboxEmailRecipient::FIELD_EMAIL_ADDRESS          => $emailAddress,
                        MailboxEmailRecipient::FIELD_TYPE                   => $type,
                        MailboxEmailRecipient::FIELD_UPDATED_AT             => now(),
                        MailboxEmailRecipient::FIELD_CREATED_AT             => now(),
                        MailboxEmailRecipient::FIELD_IDENTIFIED_CONTACT_ID  => $identifiedContact->{IdentifiedContact::FIELD_ID},
                    ]);
                }
            }
        }
    }
}
