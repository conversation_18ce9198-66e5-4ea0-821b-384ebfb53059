<?php

namespace App\Repositories;

use App\Builders\CompanyRevenueBuilder;
use App\Enums\TimePeriod;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentInvoice;
use App\Models\Legacy\EloquentInvoiceItem;
use App\Models\Legacy\EloquentQuoteCompany;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class RevenueRepository
{
    /**
     * @param EloquentCompany $company
     *
     * @return array
     */
    public function getRevenueOverview(EloquentCompany $company): array
    {
        return
            [
                'outstanding_invoice'   => round($this->getTotalOutstanding($company), 2),
                'last_lead'             => $this->getLastLead($company)?->timestampdelivered,
                'revenue_all_time'      => round($this->getTotalPaid($company), 2),
                'revenue_last_month'    => round($this->getTotalPaid($company, Carbon::today()->startOfMonth()->subMonth()), 2),
                'revenue_last_6_months' => round($this->getTotalPaid($company, Carbon::today()->startOfMonth()->subMonths(6)), 2),
                'graph_data'            => $this->getRevenueGraphData($company)
            ];
    }

    /**
     * Gets the data for the revenue graph.
     *
     * @param EloquentCompany $company
     * @param string $period
     * @param int $duration
     * @return array
     */
    public function getRevenueGraphData(EloquentCompany $company, string $period = CompanyRevenueBuilder::PERIOD_ALL_TIME, int $duration = 1): array
    {
        return CompanyRevenueBuilder::query()
            ->forCompany($company->companyid)
            ->setPeriod($period)
            ->setPeriodDuration($duration)
            ->groupBy($duration === 1 && $period === CompanyRevenueBuilder::GROUP_MONTHLY ? CompanyRevenueBuilder::GROUP_DAILY : CompanyRevenueBuilder::GROUP_MONTHLY)
            ->get()
            ->toArray();
    }

    /**
     * @param EloquentCompany $company
     *
     * @return EloquentQuoteCompany|null
     */
    public function getLastLead(EloquentCompany $company): ?EloquentQuoteCompany
    {
        /** @var EloquentQuoteCompany|null $quoteCompany */
        $quoteCompany = $company->quoteCompanies()
            ->where(EloquentQuoteCompany::DELIVERED, EloquentQuoteCompany::IS_DELIVERED)
            ->orderBy(EloquentQuoteCompany::TIMESTAMP_DELIVERED, 'DESC')
            ->first();

        return $quoteCompany;
    }

    /**
     * @param EloquentCompany $company
     *
     * @return mixed
     */
    public function getTotalOutstanding(EloquentCompany $company): mixed
    {
        return $this->getBaseQuery()
            ->selectRaw('SUM((' . EloquentInvoiceItem::QUANTITY . ' * ' . EloquentInvoiceItem::ITEM_EX_TAX_PRICE . ') + ' . EloquentInvoiceItem::TOTAL_TAX_AMOUNT . ') AS total')
            ->where(EloquentInvoice::COMPANY_ID, $company->companyid)
            ->where(EloquentInvoice::TABLE . '.' . EloquentInvoice::STATUS, EloquentInvoice::VALUE_STATUS_INITIAL)
            ->first()->total;
    }

    /**
     * @param EloquentCompany $company
     * @param Carbon|null $time
     * @return mixed
     */
    public function getTotalPaid(EloquentCompany $company, ?Carbon $time = null): mixed
    {
        $query = $this->getBaseQuery()
            ->selectRaw('SUM((' . EloquentInvoiceItem::QUANTITY . ' * ' . EloquentInvoiceItem::ITEM_EX_TAX_PRICE . ') + ' . EloquentInvoiceItem::TOTAL_TAX_AMOUNT . ') AS total')
            ->where(EloquentInvoice::COMPANY_ID, $company->companyid)
            ->where(EloquentInvoice::TABLE . '.' . EloquentInvoice::STATUS, EloquentInvoice::VALUE_STATUS_PAID);

        if($time !== null)
            $query->where(EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::TIMESTAMP_ADDED, '>=', $time->getTimestamp());

        return $query->first()?->total;
    }

    /**
     * @param EloquentCompany $company
     * @param TimePeriod $timePeriod
     * @return Collection
     */
    public function getRevenueInsights(EloquentCompany $company, TimePeriod $timePeriod): Collection
    {
        switch ($timePeriod){
            case TimePeriod::NO_LIMIT:
                return $this->getYearlyRevenue($company);
            case TimePeriod::YEAR:
                return $this->getMonthlyRevenue($company, Carbon::today()->startOfDay()->subMonths(12));
            case TimePeriod::HALF_YEAR:
                return $this->getMonthlyRevenue($company, Carbon::today()->startOfDay()->subMonths(6));
            case TimePeriod::MONTH:
                return $this->getDailyRevenue($company, Carbon::today()->startOfDay()->subMonth());
        }
    }

    /**
     * @param EloquentCompany $company
     * @param Carbon|null $time
     * @return Collection
     */
    public function getMonthlyRevenue(EloquentCompany $company, ?Carbon $time): Collection
    {
        $query = $this->getBaseQuery()
            ->selectRaw(
                'SUM((' . EloquentInvoiceItem::QUANTITY . ' * ' . EloquentInvoiceItem::ITEM_EX_TAX_PRICE . ') + ' . EloquentInvoiceItem::TOTAL_TAX_AMOUNT . ') AS total,
                CONCAT(MONTH(FROM_UNIXTIME(' . EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::TIMESTAMP_ADDED.')), " ", YEAR(FROM_UNIXTIME(' . EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::TIMESTAMP_ADDED . '))) AS month')
            ->where(EloquentInvoice::COMPANY_ID, $company->companyid)
            ->where(EloquentInvoice::TABLE . '.' . EloquentInvoice::STATUS, EloquentInvoice::VALUE_STATUS_PAID);

        if($time !== null)
            $query->where(EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::TIMESTAMP_ADDED, '>=', $time->getTimestamp());

        $query->groupBy("month");
        return $query->get();
    }

    /**
     * @param EloquentCompany $company
     * @param Carbon|null $time
     * @return Collection
     */
    public function getDailyRevenue(EloquentCompany $company, ?Carbon $time): Collection
    {
        $query = $this->getBaseQuery()
            ->selectRaw(
                'SUM((' . EloquentInvoiceItem::QUANTITY . ' * ' . EloquentInvoiceItem::ITEM_EX_TAX_PRICE . ') + ' . EloquentInvoiceItem::TOTAL_TAX_AMOUNT . ') AS total,
                DATE(FROM_UNIXTIME('.EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::TIMESTAMP_ADDED.')) AS date')
            ->where(EloquentInvoice::COMPANY_ID, $company->companyid)
            ->where(EloquentInvoice::TABLE . '.' . EloquentInvoice::STATUS, EloquentInvoice::VALUE_STATUS_PAID);

        if($time !== null)
            $query->where(EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::TIMESTAMP_ADDED, '>=', $time->getTimestamp());

        $query->groupBy("date");
        return $query->get();
    }

    /**
     * @param EloquentCompany $company
     * @return Collection
     */
    public function getYearlyRevenue(EloquentCompany $company): Collection
    {
        return $this->getBaseQuery()
            ->selectRaw(
                'SUM((' . EloquentInvoiceItem::QUANTITY . ' * ' . EloquentInvoiceItem::ITEM_EX_TAX_PRICE . ') + ' . EloquentInvoiceItem::TOTAL_TAX_AMOUNT . ') AS total,
                YEAR(FROM_UNIXTIME('.EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::TIMESTAMP_ADDED.')) AS date')
            ->where(EloquentInvoice::COMPANY_ID, $company->companyid)
            ->where(EloquentInvoice::TABLE . '.' . EloquentInvoice::STATUS, EloquentInvoice::VALUE_STATUS_PAID)
            ->groupBy("date")
            ->get();
    }

    /**
     * @return Builder
     */
    protected function getBaseQuery(): Builder
    {
        return EloquentInvoice::query()
            ->join(EloquentInvoiceItem::TABLE, EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::INVOICE_ID, '=', EloquentInvoice::TABLE . '.' . EloquentInvoice::INVOICE_ID);
    }
}
