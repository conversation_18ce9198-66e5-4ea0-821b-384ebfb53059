<?php

namespace App\Repositories;

use App\Models\ImpersonationLog;

class ImpersonateRepository
{
    /**
     * Logs an impersonation.
     *
     * @param int $userId
     * @param int $impersonationId
     * @return ImpersonationLog
     */
    public function log(int $userId, int $impersonationId): ImpersonationLog
    {
        $log = new ImpersonationLog();
        $log->user_id = $userId;
        $log->impersonating_id = $impersonationId;
        $log->save();

        return $log;
    }
}
