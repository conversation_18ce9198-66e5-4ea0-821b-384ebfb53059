<?php

namespace App\Repositories;

use App\Builders\TaskBuilder;
use App\DataModels\Workflows\TaskResultDataModel;
use App\Enums\SupportedTimezones;
use App\Enums\TaskResultType;
use App\Events\Workflows\TaskRescheduledEvent;
use App\Http\Controllers\API\Workflows\TaskController;
use App\Http\Requests\SearchCompaniesRequest;
use App\Http\Requests\Workflows\WorkflowTaskRequest;
use App\Jobs\CalculateTaskTimezoneInBulkJob;
use App\Jobs\CalculateTaskTimezoneJob;
use App\Jobs\OutreachCadence\ResetLastFailedGroupForReattempt;
use App\Models\CompletedWorkflow;
use App\Models\RunningWorkflow;
use App\Models\Sales\Task;
use App\Models\TaskCategory;
use App\Models\TaskNote;
use App\Models\TaskType;
use App\Models\User;
use App\Models\WorkflowAction;
use App\Repositories\OutreachCadence\CompanyCadenceRoutineRepository;
use App\Services\ImpersonateService;
use App\Services\Tasks\TaskTimezoneService;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\ValidationException;
use Ramsey\Uuid\Uuid;
use Throwable;

class TaskRepository
{
    public function __construct(protected TaskTimezoneService $timezoneService) {}

    /**
     * Gets all the tasks for a user.
     *
     * @param int|null $id
     * @param string|null $subject
     * @param int|null $priority
     * @param string|null $sortCol
     * @param string|null $sortDir
     * @param int|null $companyId
     * @param int|null $categoryId
     * @param SupportedTimezones|null $timezone
     * @param Carbon|null $date
     * @return Builder
     */
    public function getAllTasksForUser(
        ?int $id = null,
        ?string $subject = null,
        ?int $priority = null,
        ?string $sortCol = null,
        ?string $sortDir = null,
        ?int $companyId = null,
        ?int $categoryId = null,
        ?SupportedTimezones $timezone = null,
        ?Carbon $date = null,
    ): Builder
    {
        return TaskBuilder::query()
            ->for($id)
            ->setQuery($subject)
            ->setPriority($priority)
            ->setCompanyId($companyId)
            ->setCategoryId($categoryId)
            ->setTimezone($timezone)
            ->setDate(!is_null($date) ? $date : null)
            ->allowNames()
            ->allowCompanies()
            ->sortBy($sortCol, $sortDir)
            ->setIncludeMuted(false)
            ->getQuery();
    }

    /**
     * Gets tasks for today for a user.
     *
     * @param int|null $id
     * @param string|null $subject
     * @param int|null $priority
     * @param string|null $sortCol
     * @param string|null $sortDir
     * @param int|null $companyId
     * @param int|null $categoryId
     * @param SupportedTimezones|null $timezone
     * @return Builder
     */
    public function getTodayTasksForUser(
        ?int $id = null,
        ?string $subject = null,
        ?int $priority = null,
        ?string $sortCol = null,
        ?string $sortDir = null,
        ?int $companyId = null,
        ?int $categoryId = null,
        ?SupportedTimezones $timezone = null,
    ): Builder
    {
        return TaskBuilder::query()
            ->for($id)
            ->setQuery($subject)
            ->setPriority($priority)
            ->setCompanyId($companyId)
            ->setCategoryId($categoryId)
            ->setTimezone($timezone)
            ->setDate(Carbon::now()->startOfDay(), "=")
            ->allowNames()
            ->allowCompanies()
            ->sortBy($sortCol, $sortDir)
            ->setIncludeMuted(false)
            ->getQuery();
    }

    /**
     * Gets the overdue tasks for a user.
     *
     * @param int|null $id
     * @param string|null $subject
     * @param int|null $priority
     * @param string|null $sortCol
     * @param string|null $sortDir
     * @param int|null $companyId
     * @param int|null $categoryId
     * @param SupportedTimezones|null $timezone
     * @return Builder
     */
    public function getOverdueTasksForUser(
        ?int $id = null,
        ?string $subject = null,
        ?int $priority = null,
        ?string $sortCol = null,
        ?string $sortDir = null,
        ?int $companyId = null,
        ?int $categoryId = null,
        ?SupportedTimezones $timezone = null,
        ?Carbon $date = null,
    ): Builder
    {
        return TaskBuilder::query()
            ->for($id)
            ->setQuery($subject)
            ->setPriority($priority)
            ->setCompanyId($companyId)
            ->setCategoryId($categoryId)
            ->setTimezone($timezone)
            ->setDate(!is_null($date) ? $date : Carbon::now()->startOfDay(), !is_null($date) ? '=' : "<")
            ->allowNames()
            ->allowCompanies()
            ->sortBy($sortCol, $sortDir)
            ->setIncludeMuted(false)
            ->getQuery();
    }

    /**
     * Gets the upcoming tasks for a user.
     *
     * @param int|null $id
     * @param string|null $subject
     * @param int|null $priority
     * @param string|null $sortCol
     * @param string|null $sortDir
     * @param int|null $companyId
     * @param int|null $categoryId
     * @param SupportedTimezones|null $timezone
     * @return Builder
     */
    public function getUpcomingTasksForUser(
        ?int $id = null,
        ?string $subject = null,
        ?int $priority = null,
        ?string $sortCol = null,
        ?string $sortDir = null,
        ?int $companyId = null,
        ?int $categoryId = null,
        ?SupportedTimezones $timezone = null,
    ): Builder
    {
        return TaskBuilder::query()
            ->for($id)
            ->setQuery($subject)
            ->setPriority($priority)
            ->setCompanyId($companyId)
            ->setCategoryId($categoryId)
            ->setTimezone($timezone)
            ->setDate(Carbon::now()->endOfDay(), ">")
            ->allowNames()
            ->allowCompanies()
            ->sortBy($sortCol, $sortDir)
            ->setIncludeMuted(false)
            ->getQuery();
    }

    /**
     * Gets the completed tasks for a user.
     *
     * @param int|null $id
     * @param string|null $subject
     * @param int|null $priority
     * @param string|null $sortCol
     * @param string|null $sortDir
     * @param int|null $companyId
     * @param int|null $categoryId
     * @param SupportedTimezones|null $timezone
     * @return Builder
     */
    public function getCompletedTasksForUser(
        ?int $id = null,
        ?string $subject = null,
        ?int $priority = null,
        ?string $sortCol = null,
        ?string $sortDir = null,
        ?int $companyId = null,
        ?int $categoryId = null,
        ?SupportedTimezones $timezone = null,
    ): Builder
    {
        return TaskBuilder::query()
            ->for($id)
            ->setQuery($subject)
            ->setPriority($priority)
            ->setCompanyId($companyId)
            ->setCategoryId($categoryId)
            ->setTimezone($timezone)
            ->allowCompleted()
            ->allowNames()
            ->allowCompanies()
            ->sortBy($sortCol, $sortDir)
            ->setIncludeMuted(false)
            ->getQuery();
    }

    /**
     * @param int|null $id
     * @param string|null $subject
     * @param int|null $priority
     * @param string|null $sortCol
     * @param string|null $sortDir
     * @param int|null $companyId
     * @param int|null $categoryId
     * @param SupportedTimezones|null $timezone
     * @return Builder
     */
    public function getMutedTasksForUser(
        ?int $id = null,
        ?string $subject = null,
        ?int $priority = null,
        ?string $sortCol = null,
        ?string $sortDir = null,
        ?int $companyId = null,
        ?int $categoryId = null,
        ?SupportedTimezones $timezone = null,
    ): Builder
    {
        return TaskBuilder::query()
            ->for($id)
            ->setQuery($subject)
            ->setPriority($priority)
            ->setCompanyId($companyId)
            ->setCategoryId($categoryId)
            ->setTimezone($timezone)
            ->allowNames()
            ->allowCompanies()
            ->sortBy($sortCol, $sortDir)
            ->setIncludeMuted(true)
            ->getQuery()
            ->where(Task::FIELD_MUTED, true);
    }

    /**
     * Gets an overview of count of tasks.
     *
     * @param int|null $id
     * @param int|null $companyId
     *
     * @return array
     */
    public function getCountOverview(?int $id = null, ?int $companyId = null): array
    {
        return [
            "All"       => $this->getAllTasksForUser($id, null, null, null,'asc', $companyId)->count(),
            "Due Today" => $this->getTodayTasksForUser($id, null, null, null, 'asc', $companyId)->count(),
            "Overdue"   => $this->getOverdueTasksForUser($id, null, null, null, 'asc', $companyId)->count(),
            "Upcoming"  => $this->getUpcomingTasksForUser($id, null, null, null, 'asc', $companyId)->count(),
            "Completed" => $this->getCompletedTasksForUser($id, null, null, null, 'asc', $companyId)->count(),
            "Muted"     => $this->getMutedTasksForUser($id, null, null, null, 'asc', $companyId)->count(),
        ];
    }

    /**
     * Gets a minimal overview of count of tasks.
     *
     * @param int|null $id
     * @param int|null $companyId
     *
     * @return array
     */
    public function getMinimalCounts(?int $id = null, ?int $companyId = null): array
    {
        return [
            "due_today" => $this->getTodayTasksForUser($id, null, null, null, 'asc', $companyId)->count(),
            "overdue"   => $this->getOverdueTasksForUser($id, null, null, null, 'asc', $companyId)->count(),
        ];
    }


    /**
     * Needed to fetch task after creation, since createTask return null.
     * Using a temporary unique subject.
     *
     * @param int $userId
     * @param string $subject
     * @return Builder|Task|null
     */
    public function getTaskByAssigneeAndSubject(int $userId, string $subject): Builder|Task|null
    {
        return Task::query()
            ->where(Task::FIELD_ASSIGNED_USER_ID, $userId)
            ->where(Task::FIELD_SUBJECT, $subject)
            ->orderBy(Model::CREATED_AT, 'desc')
            ->first();
    }

    /**
     * @param string $string
     * @return Builder|TaskType
     */
    public function getTaskTypeByName(string $string): Builder|TaskType
    {
        return TaskType::query()->where(TaskType::FIELD_NAME, $string)->first();
    }

    /**
     * @param string $string
     * @return Builder|TaskCategory
     */
    public function getTaskCategoryByName(string $string): Builder|TaskCategory
    {
        return TaskCategory::query()->where(TaskCategory::NAME, $string)->first();
    }

    /**
     * @param Task $task
     * @return void
     */
    public function terminateAssociatedCadenceRoutine(Task $task): void
    {
        /** @var CompanyCadenceRoutineRepository $companyCadenceRepository */
        $companyCadenceRepository = app(CompanyCadenceRoutineRepository::class);
        $routineId                = $task->companyCadenceAction?->group?->routine?->id;
        if ($routineId) {
            $companyCadenceRepository->deleteRoutineById($routineId);
        }
    }

    /**
     * @param Task $task
     * @return void
     */
    public function resetLastFailedGroupForAssociatedCadenceRoutine(Task $task): void
    {
        $routineId = $task->companyCadenceAction?->group?->routine?->id;
        if ($routineId) {
            ResetLastFailedGroupForReattempt::dispatch($routineId);
        }
    }

    /**
     * @param int $id
     * @param string|null $operator
     * @param Carbon|null $time
     * @param bool|null $completed
     * @param string|null $subject
     * @param int|null $priority
     * @param string|null $sortCol
     * @param string|null $sortDir
     * @param int|null $companyId
     * @return Builder
     */
    protected function buildTaskQuery(
        int $id,
        ?string $operator = null,
        ?Carbon $time = null,
        ?bool $completed = null,
        ?string $subject = null,
        ?int $priority = null,
        ?string $sortCol = null,
        ?string $sortDir = null,
        ?int $companyId = null
    ): Builder
    {
        $query = Task::query()->where(Task::FIELD_ASSIGNED_USER_ID, $id);

        if ($operator !== null && $time !== null)
            $query = $query->whereDate(Task::FIELD_AVAILABLE_AT, $operator, $time);

        if ($completed !== null)
            $query = $query->where(Task::FIELD_COMPLETED, $completed ? Task::TASK_COMPLETED : Task::TASK_NOT_COMPLETED);

        if ($subject !== null)
            $query->where(Task::FIELD_SUBJECT,'LIKE', '%'.$subject.'%');

        if ($priority !== null)
            $query->where(Task::FIELD_PRIORITY, $priority);

        if ($sortCol !== null && $sortDir !== null)
            $query->orderBy($sortCol, $sortDir);
        else
            $query->orderBy(Task::FIELD_AVAILABLE_AT, 'asc');

        if($companyId !== null)
            $query->whereJsonContains(Task::FIELD_PAYLOAD.'->company_id', $companyId);

        return $query;
    }

    /**
     * Handles rescheduling a task.
     *
     * @param Task $task
     * @param int $minutes
     * @return bool
     */
    public function rescheduleTask(Task $task, int $minutes): bool
    {
        $task->available_at = Carbon::now()->addMinutes($minutes);
        $task->reschedule_count++;

        return $task->save();
    }

    /**
     * Handles creating a task note.
     *
     * @param Task $task
     * @param int $id
     * @param string $note
     * @return bool
     */
    public function addTaskNote(Task $task, int $id, string $note): bool
    {
        return $task->taskNotes()->create([
            TaskNote::FIELD_NOTE => $note,
            TaskNote::FIELD_USER_ID => $id
        ]) !== null;
    }

    /**
     * Update task status to completed
     *
     * @param Task $task
     *
     * @return bool
     */
    public function completeTask(Task $task): bool
    {
        return $task->update([
            Task::FIELD_COMPLETED                         => Task::TASK_COMPLETED,
            Task::FIELD_COMPLETED_AT                      => Carbon::now(),
            Task::FIELD_COMPLETED_BY_USER_ID              => Auth::user()?->{User::FIELD_ID} ?? null,
            Task::FIELD_COMPLETED_BY_IMPERSONATOR_USER_ID => Session::get(ImpersonateService::SESSION_KEY)
        ]);
    }

    /**
     * @param  Builder  $query
     * @return void
     */
    public function completeTasks(Builder $query): void
    {
        $query->update([
            Task::FIELD_COMPLETED                         => Task::TASK_COMPLETED,
            Task::FIELD_COMPLETED_AT                      => Carbon::now(),
            Task::FIELD_COMPLETED_BY_USER_ID              => Auth::user()?->{User::FIELD_ID} ?? null,
            Task::FIELD_COMPLETED_BY_IMPERSONATOR_USER_ID => Session::get(ImpersonateService::SESSION_KEY)
        ]);
    }

    /**
     * Returns a collection of tasks associated with a given leadID
     *
     * @param int $leadId
     * @return Collection<Task>
     */
    public function getAssociatedTasksForLead(int $leadId): Collection
    {
        return Task::query()
            ->whereHas(Task::RELATION_RUNNING_WORKFLOW, function($query) use($leadId){
                return $query
                    ->where(RunningWorkflow::VIRTUAL_FIELD_EVENT_LEAD_ID, $leadId)
                    ->where(Task::FIELD_COMPLETED, Task::TASK_NOT_COMPLETED);
            })->orWhereHas(Task::RELATION_COMPLETED_WORKFLOW, function($query) use($leadId){
                return $query
                    ->where(CompletedWorkflow::VIRTUAL_FIELD_EVENT_LEAD_ID, $leadId)
                    ->where(Task::FIELD_COMPLETED, Task::TASK_COMPLETED);
            })->get();
    }

    /**
     * @param int $taskId
     *
     * @return Task
     */
    public function findByTaskIdOrFail(int $taskId): Task
    {
        /** @var Task $task */
        $task = Task::query()->findOrFail($taskId);

        return $task;
    }

    /**
     * Find Tasks by batch.
     *
     * @param string $batch
     * @return Collection|array
     */
    public function findCompanyTaskByBatch(string $batch): Collection|array
    {
        return Task::query()->whereJsonContains(Task::FIELD_PAYLOAD . '->batch', $batch)->get();
    }

    /**
     * Calculates and stores the timezone in bulk.
     *
     * @param Collection $tasks
     * @param SupportedTimezones|null $timezone
     * @return Bool
     */
    public function storeTimezoneForTaskInBulk(Collection $tasks, ?SupportedTimezones $timezone = null): bool
    {
        $tasksUpdate = [];
        foreach ($tasks as $task) {
            $task->timezone = $timezone ?: $this->timezoneService->calculateTimezoneForTask($task);
            $tasksUpdate[] = [Task::FIELD_TIMEZONE => $task->timezone, Task::FIELD_ID => $task->id];
        }

        try {
            Task::query()->upsert($tasksUpdate, [Task::FIELD_ID], [Task::FIELD_TIMEZONE]);

        } catch (Throwable $err) {
            logger()->error("Failed to create timezone for bulk task" . $err->getMessage());
        }

        return true;
    }

    /**
     * Calculates and stores the timezone for a given task.
     *
     * @param Task $task
     * @param SupportedTimezones|null $timezone
     * @return Task
     */
    public function storeTimezoneForTask(Task $task, ?SupportedTimezones $timezone = null): Task
    {
        $task->timezone = $timezone ?: $this->timezoneService->calculateTimezoneForTask($task);
        $task->save();

        return $task;
    }

    /**
     * @param int $countyLocationId
     * @param Carbon $startTime
     * @param int|null $taskCategoryId
     * @param bool $count
     *
     * @return Collection|int
     */
    public function getTasksForCountyLocation(int $countyLocationId, Carbon $startTime, ?int $taskCategoryId = null, bool $count = true): Collection|int
    {
        $query = Task::query()
            ->where(Task::CREATED_AT, '>=', $startTime)
            ->where(Task::FIELD_COUNTY_LOCATION_ID, $countyLocationId);

        if ($taskCategoryId) $query->where(Task::FIELD_TASK_CATEGORY_ID, $taskCategoryId);

        if ($count) return $query->count();

        return $query->get();
    }

    /**
     * @param string $subject
     * @param int $typeId
     * @param string $priority
     * @param int $categoryId
     * @param string $availableAt
     * @param int $companyId
     * @param int|null $assignedUserId
     * @return void
     * @throws Exception
     */
    public function createTask(
        string $subject,
        int    $typeId,
        string $priority,
        int    $categoryId,
        string $availableAt,
        int    $companyId,
        int|null $assignedUserId = null
    ): void
    {
        $this->createTasks($subject, $typeId, $priority, $categoryId, $availableAt, [$companyId], $assignedUserId);
    }

    /**
     * Create Task in Bulk
     *
     * @param string $subject
     * @param int $typeId
     * @param string $priority
     * @param int $categoryId
     * @param string $availableAt
     * @param array $allCompanyIds
     * @param int|null $assignedUserId
     * @throws Exception
     */
    public function createTasks(
        string $subject,
        int    $typeId,
        string $priority,
        int    $categoryId,
        string $availableAt,
        array  $allCompanyIds,
        int|null $assignedUserId = null,
    ): void
    {
        $tasks = [];
        $batch = (string)Uuid::uuid4();

        $assignedUserId = $assignedUserId ?? Auth::id();

        if (count($allCompanyIds) > 0) {
            foreach(array_chunk($allCompanyIds, 500) as $companyIds) {
                foreach($companyIds as $companyId) {
                    $tasks[] = [
                        Task::FIELD_ASSIGNED_USER_ID => $assignedUserId,
                        Task::FIELD_AVAILABLE_AT => Carbon::createFromFormat('Y-m-d H:i', $availableAt),
                        Task::FIELD_SUBJECT => $subject,
                        Task::FIELD_TASK_TYPE_ID => $typeId,
                        Task::FIELD_PRIORITY => $priority,
                        Task::FIELD_MANUAL => true,
                        Task::FIELD_PAYLOAD => json_encode([TaskController::COMPANY_ID => $companyId, 'batch' => $batch]),
                        Task::FIELD_RESULTS => collect([
                            new TaskResultDataModel(0, 'Acknowledge - No Further Action', TaskResultType::VOID, []),
                            new TaskResultDataModel(0, 'Add CRM Entry', TaskResultType::CRM_ENTRY, []),
                            new TaskResultDataModel(0, 'Add Task Note', TaskResultType::TEXT, [
                                'text' => 'Add Task Note'
                            ])
                        ]),
                        Task::FIELD_TASK_CATEGORY_ID => $categoryId,
                        Task::FIELD_USES_ODIN_ID => true,
                    ];
                }
            }

            try {
                /** @var Task $tasks */
                /** Create tasks in bulk */
                Task::query()->insert($tasks);

                /** @var array $companiesIds */
                /** Dispatch on a queue to calculate, as we don't want to block the request/create a slow request for making a task */
                CalculateTaskTimezoneInBulkJob::dispatch($batch);

            } catch (Throwable $err) {
                logger()->error("Failed to create tasks" . $err->getMessage());
            }

        } else {
            throw new Exception("No companies found for the given criteria.");
        }
    }

    /**
     *
     * @param Collection $tasks
     * @param Carbon|string $available_at
     * @param string|null $note
     * @return void
     */
    public function bulkRescheduleTasks(Collection $tasks, Carbon|string $available_at, string|null $note): void
    {
        $data = [];

        foreach($tasks as $task) {
            $data[] = [
                Task::FIELD_ID               => $task->{Task::FIELD_ID},
                Task::FIELD_AVAILABLE_AT     => $available_at,
                Task::FIELD_RESCHEDULE_COUNT => $task->{Task::FIELD_RESCHEDULE_COUNT} + 1
            ];
        }

        Task::query()->upsert($data, [Task::FIELD_ID]);

        $noteData = [];
        $userId = Auth::id();

        foreach($tasks as $task) {
            TaskRescheduledEvent::dispatch($task->{Task::FIELD_ID}, $task->{Task::FIELD_AVAILABLE_AT});

            if(!empty($note)) {
                $noteData[] = [
                    TaskNote::FIELD_TASK_ID => $task->{Task::FIELD_ID},
                    TaskNote::FIELD_USER_ID => $userId,
                    TaskNote::FIELD_NOTE    => $note
                ];
            }
        }

        if(!empty($noteData)) {
            TaskNote::query()->insert($noteData);
        }
    }

    /**
     *
     * @param Task $task
     * @param Carbon|string $available_at
     * @param string|null $note
     * @return void
     */
    public function updateRescheduleTask(Task $task, Carbon|string $available_at, string|null $note): void
    {
        $task->available_at = $available_at;
        $task->reschedule_count = $task->reschedule_count + 1;
        $task->save();

        TaskRescheduledEvent::dispatch($task->id, $task->available_at);

        if(!empty($note)) {
            $taskNote = new TaskNote;
            $taskNote->task_id = $task->{Task::FIELD_ID};
            $taskNote->user_id = Auth::id();
            $taskNote->note = $note;
            $taskNote->save();
        }
    }

    /**
     * @param int $actionId
     * @return Collection
     */
    public function getRunningWorkflowIdsByActionId(int $actionId): Collection
    {
        $workflowActionRecords = DB::table(WorkflowAction::TABLE)
            ->whereRaw("JSON_EXTRACT(payload, '$.action_id') = ?", [$actionId])
            ->get();

        $workflowActionIds = $workflowActionRecords->pluck(WorkflowAction::FIELD_WORKFLOW_ID);

        $runningWorkflowRecords = DB::table(RunningWorkflow::TABLE)
            ->whereIn(RunningWorkflow::FIELD_WORKFLOW_ID, $workflowActionIds)
            ->get();

        return $runningWorkflowRecords->pluck(RunningWorkflow::FIELD_ID);
    }

    /**
     * @param Collection $runningWorkflowIds
     * @param bool $mute
     * @return int
     */
    public function updateTasksMuteStatus(Collection $runningWorkflowIds, bool $mute): int
    {
        return DB::table(Task::TABLE)
            ->whereIn(Task::FIELD_RUNNING_WORKFLOW_ID, $runningWorkflowIds)
            ->update([Task::FIELD_MUTED => $mute]);
    }

}
