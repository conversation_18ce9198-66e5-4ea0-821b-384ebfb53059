<?php

namespace App\Repositories;

use App\DataModels\HistoricalCompanySalesStatusDataModel;
use App\Models\HistoricalCompanySalesStatus;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class HistoricalCompanySalesStatusRepository
{
    public function recordCurrentCompanySalesStatuses(int $chunkSize = 1000): bool
    {
        $now = CarbonImmutable::now('UTC');
        $year = $now->year;
        $nowFormatted = $now->format('Y-m-d H:i:s');

        $missingCompanies = $this->getCompaniesNotInHistoricalCompanySalesStatus($year);

        Company::query()
            ->with([
                Company::RELATION_USERS => function($with) {
                    $with->where(CompanyUser::TABLE.'.'.CompanyUser::FIELD_IS_DECISION_MAKER, true);
                }
            ])
            ->select([
                Company::TABLE.'.'.Company::FIELD_ID,
                Company::TABLE.'.'.Company::FIELD_SALES_STATUS
            ])
            ->distinct()
            ->chunk($chunkSize, function($chunk) use (&$missingCompanies, $year, $now, $nowFormatted) {
                $this->processCompaniesSalesStatusChunk(
                    $chunk,
                    $missingCompanies,
                    $year,
                    $now,
                    $nowFormatted
                );
            });

        return true;
    }

    private function processCompaniesSalesStatusChunk(
        Collection $companiesChunk,
        array &$missingCompanies,
        int $year,
        CarbonImmutable|Carbon $statusDate,
        string $databaseTimestamp
    ): bool
    {
        DB::transaction(function() use ($companiesChunk, &$missingCompanies, $year, $statusDate, $databaseTimestamp) {
            $insertRows = [];
            $updateRows = [];

            foreach($companiesChunk as $companyInfo) {
                if(in_array($companyInfo->{Company::FIELD_ID}, $missingCompanies)) {
                    $hcssdm = new HistoricalCompanySalesStatusDataModel();

                    $hcssdm->setDaySalesStatus(
                        $statusDate,
                        $companyInfo->{Company::FIELD_SALES_STATUS},
                        $companyInfo->{Company::RELATION_USERS}->count() > 0
                    );

                    $insertRows[] = [
                        HistoricalCompanySalesStatus::FIELD_COMPANY_ID => $companyInfo[Company::FIELD_ID],
                        HistoricalCompanySalesStatus::FIELD_YEAR => $year,
                        HistoricalCompanySalesStatus::FIELD_SALES_STATUS => $hcssdm->toJson(),
                        HistoricalCompanySalesStatus::CREATED_AT => $databaseTimestamp
                    ];
                }
                else {
                    $updateRows[$companyInfo->{Company::FIELD_ID}] = sprintf(
                        "WHEN %s THEN JSON_SET(%s, '%s', %s)",
                        $companyInfo->{Company::FIELD_ID},
                        HistoricalCompanySalesStatus::TABLE.'.'.HistoricalCompanySalesStatus::FIELD_SALES_STATUS,
                        sprintf('$."%s"', $statusDate->format(HistoricalCompanySalesStatusDataModel::DATE_FORMAT)),
                        sprintf(
                            "JSON_OBJECT('%s', %s, '%s', %s)",
                            HistoricalCompanySalesStatusDataModel::SALES_STATUS,
                            $companyInfo->{Company::FIELD_SALES_STATUS}->value,
                            HistoricalCompanySalesStatusDataModel::HAS_DECISION_MAKER,
                            (int) ($companyInfo->{Company::RELATION_USERS}->count() > 0)
                        )
                    );
                }
            }

            if(!empty($insertRows)) {
                HistoricalCompanySalesStatus::query()->insert($insertRows);
            }

            if(!empty($updateRows)) {
                $updates = sprintf(
                    "(CASE %s %s END)",
                    HistoricalCompanySalesStatus::FIELD_COMPANY_ID,
                    implode(' ', array_values($updateRows))
                );

                HistoricalCompanySalesStatus::query()
                    ->where(HistoricalCompanySalesStatus::FIELD_YEAR, $year)
                    ->whereIntegerInRaw(HistoricalCompanySalesStatus::FIELD_COMPANY_ID, array_keys($updateRows))
                    ->update([
                        HistoricalCompanySalesStatus::FIELD_SALES_STATUS => DB::raw($updates),
                        HistoricalCompanySalesStatus::UPDATED_AT => $databaseTimestamp
                    ]);
            }
        });

        return true;
    }

    public function getCompaniesNotInHistoricalCompanySalesStatus(int $year): array
    {
        return Company::query()
            ->leftJoin(HistoricalCompanySalesStatus::TABLE, function($join) use ($year) {
                $join
                    ->on(
                        HistoricalCompanySalesStatus::TABLE.'.'.HistoricalCompanySalesStatus::FIELD_COMPANY_ID,
                        '=',
                        Company::TABLE.'.'.Company::FIELD_ID
                    )
                    ->where(HistoricalCompanySalesStatus::TABLE.'.'.HistoricalCompanySalesStatus::FIELD_YEAR, $year);
            })
            ->whereNull(HistoricalCompanySalesStatus::TABLE.'.'.HistoricalCompanySalesStatus::FIELD_ID)
            ->distinct()
            ->select(Company::TABLE.'.'.Company::FIELD_ID)
            ->pluck(Company::FIELD_ID)
            ->toArray();
    }
}
