<?php

namespace App\Repositories\CompanyUserRelationship;

use App\Builders\CompanyUserRelationship\CompanyUserRelationshipBuilder;
use App\Models\CompanyUserRelationship;
use App\Models\Role;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class CompanyUserRelationshipRepository
{
    /**
     * @param int|null $companyId
     * @param string|null $name
     * @param array|null $roles
     * @param bool|null $active
     * @return Builder
     */
    public function list(
        ?int $companyId = null,
        ?string $name = null,
        ?array $roles = null,
        ?bool $active = null,
    ): Builder
    {

        return (new CompanyUserRelationshipBuilder())
            ->withTrashed()
            ->forCompanyId(companyId: $companyId)
            ->forName(name: $name)
            ->forRoles(roles: $roles)
            ->forActive(active: $active)
            ->query();
    }

    /**
     * @return Collection
     */
    public function listRoles(): Collection
    {
        return Role::query()
            ->select(
                Role::FIELD_NAME,
            )
            ->join(
                CompanyUserRelationship::TABLE,
                CompanyUserRelationship::TABLE .'.'. CompanyUserRelationship::FIELD_ROLE_ID,
                '=',
                Role::TABLE .'.'. Role::FIELD_ID
            )
            ->distinct()
            ->get();
    }

    /**
     * @param CompanyUserRelationship $companyUserRelationship
     * @param Carbon|null $commissionableAt
     * @param Carbon|null $commissionableTo
     *
     * @return bool
     */
    public function update(
        CompanyUserRelationship $companyUserRelationship,
        ?Carbon                 $commissionableAt = null,
        ?Carbon                 $commissionableTo = null,
    ): bool
    {
        return $companyUserRelationship->update([
            CompanyUserRelationship::FIELD_COMMISIONABLE_AT => $commissionableAt,
            CompanyUserRelationship::FIELD_COMMISIONABLE_TO => $commissionableTo,
        ]);
    }

}
