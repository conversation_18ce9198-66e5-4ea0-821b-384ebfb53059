<?php

namespace App\Repositories;

use App\Enums\EventCategory;
use App\Enums\EventName;

class PubSubEventRepository
{
    /**
     * Returns all events, grouped by category.
     *
     * @return array
     */
    public function getAllEvents(): array
    {
        $events = [];

        foreach (EventCategory::workFlowCategories() as $category) {
            $events[$category] = match ($category) {
                EventCategory::CAMPAIGNS->value => EventName::getAllCampaignEvents(),
                EventCategory::COMPANIES->value => [
                    EventName::COMPLETED_REGISTRATION->value,
                    EventName::STARTED_REGISTRATION->value,
                    EventName::LEFT_REGISTRATION->value,
                    EventName::SALES_BAIT_RECEIVED->value,
                    EventName::SALES_BAIT_REGISTERED_INTEREST->value,
                    EventName::SALES_BAIT_UNSUBSCRIBED->value,
                    EventName::GLOBAL_STATUS_UPDATED->value,
                    EventName::PAYMENT_METHOD_FAILED->value,
                    EventName::PAYMENT_METHOD_ADDED->value,
                    EventName::PAYMENT_METHOD_DELETED->value,
                    EventName::USER_CREATED->value,
                    EventName::USER_ASSOCIATED->value,
                    EventName::USER_DISASSOCIATED->value,
                    EventName::ALERT_ASSIGNED->value,
                    EventName::ALERT_REMOVED->value,
// DISABLED: temporary - need to stop this firing for every contact every time a campaign is updated
//                    EventName::CONTACT_ASSIGNED->value
                    EventName::CONTACT_REMOVED->value,
                    EventName::CONTACT_SUPPRESSED->value,
                    EventName::CONTACT_UNSUPPRESSED->value,
                    EventName::SURVEY_COMPLETED->value,
                    EventName::SURVEY_ANSWERED_QUESTION->value,
                    EventName::PROFILE_MEDIA_IMAGES_CREATED->value,
                    EventName::PROFILE_MEDIA_IMAGES_DELETED->value,
                    EventName::PROFILE_MEDIA_IMAGES_UPDATED->value,
                    EventName::PROFILE_YOUTUBE_LINK_ADDED->value,
                    EventName::PROFILE_MEDIA_LOGO_UPDATED->value,
                    EventName::PROFILE_DESCRIPTION_UPDATED->value,
                    EventName::PROFILE_YEAR_STARTED_UPDATED->value,
                    EventName::PROFILE_LICENSE_CREATED->value,
                    EventName::PROFILE_LICENSE_UPDATED->value,
                    EventName::PROFILE_LICENSE_DELETED->value,
                    EventName::PROFILE_ADDRESS_CREATED->value,
                    EventName::PROFILE_ADDRESS_UPDATED->value,
                    EventName::PROFILE_ADDRESS_DELETED->value,
                    EventName::PROFILE_SERVICES_UPDATED->value,
                    EventName::SUSPENDED_FOR_FAILED_PAYMENT->value,
                    EventName::STATUS_UPDATED->value,
                    EventName::PROFITABILITY_ASSUMPTION_UPDATED->value,
                    EventName::REJECTION_PERCENTAGE_EXCEEDED->value,
                    EventName::CRM_REJECTION_PERCENTAGE_EXCEEDED->value,
                    EventName::APPOINTMENT_REJECTION_PERCENTAGE_EXCEEDED->value,
                    EventName::APPOINTMENT_CRM_REJECTION_PERCENTAGE_EXCEEDED->value,
                    EventName::MONTHLY_AD_SPEND_UPDATED->value,
                ],
                EventCategory::REVIEWS->value => [
                    EventName::CREATED->value,
                    EventName::RESPONDED->value
                ],
                EventCategory::LEADS->value => [
                    EventName::LEAD_UNDERSOLD->value,
                    EventName::LEAD_UNSOLD_NO_BUDGET->value,
                    EventName::LEAD_UNSOLD_NO_COMPANIES->value,
                    EventName::DELIVERY_SUCCESS->value,
                    EventName::DELIVERY_FAILURE->value,
                    EventName::STATUS_UPDATED->value,
                    EventName::OPPORTUNITY_PRODUCT_PURCHASED->value,
                ],
                EventCategory::INTERNAL->value => [
                    EventName::TASK_REASSIGNED->value,
                    EventName::ACCOUNT_MANAGER_ASSIGNED->value,
                    EventName::ASSIGN_UNASSIGNED_ACCOUNT->value,
                    EventName::INBOUND_SMS->value,
                    EventName::MISSED_CALL->value,
                ],
                EventCategory::TEST_LEADS->value => [
                    EventName::TEST_LEAD_GENERATED->value,
                    EventName::TEST_LEAD_DELIVERED->value,
                    EventName::TEST_LEAD_REVEALED->value
                ],
                EventCategory::MAILBOX->value => [
                    EventName::MAILBOX_EMAIL_SEND_SUCCESS,
                    EventName::MAILBOX_EMAIL_SEND_ERROR,
                    EventName::MAILBOX_EMAIL_RECEIVED,
                    EventName::MAILBOX_SYNC_FINISHED,
                    EventName::MAILBOX_SYNC_ERROR,
                    EventName::MAILBOX_RENEW_MAILBOX_LISTENER_ERROR,
                    EventName::MAILBOX_RENEW_MAILBOX_LISTENER_SUCCESS
                ],
                EventCategory::BILLING->value => [
                    EventName::CHARGEBACK_ISSUED->value,
                    EventName::CHARGE_FAILED->value,
                    EventName::CARD_EXPIRING_SOON->value,
                    EventName::CARD_EXPIRED->value,
                    EventName::CARD_ADDED->value,
                    EventName::INVOICE_OVERDUE->value,
                ],
                default => [],
            };
        }

        return $events;
    }

    /**
     * Returns all events for a given category.
     *
     * @param EventCategory $category
     * @return array
     */
    public function getEventsForCategory(EventCategory $category): ?array
    {
        if(!isset($this->getAllEvents()[$category->value]))
            return null;

        return $this->getAllEvents()[$category->value];
    }
}
