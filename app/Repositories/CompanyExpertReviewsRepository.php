<?php

namespace App\Repositories;

use App\Models\Odin\CompanyExpertReview;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;

class CompanyExpertReviewsRepository
{
    /**
     * @param int $id
     * @return CompanyExpertReview
     */
    public function findOrFail(int $id): CompanyExpertReview
    {
        /** @var CompanyExpertReview $companyExpertReview */
        $companyExpertReview = CompanyExpertReview::query()->findOrFail($id);

        return $companyExpertReview;
    }

    /**
     * @param int $companyId
     * @return Collection
     */
    public function getExpertActiveReviewsForCompany(int $companyId): Collection
    {
        return CompanyExpertReview::query()
            ->with(CompanyExpertReview::RELATION_USER)
            ->where(CompanyExpertReview::FIELD_COMPANY_ID, $companyId)
            ->whereNull(CompanyExpertReview::FIELD_DELETED_AT)
            ->get();
    }

    /**
     * @param int $companyId
     * @return Collection
     */
    public function getExpertReviewHistoryForCompany(int $companyId): Collection
    {
        return CompanyExpertReview::withTrashed()
            ->with(CompanyExpertReview::RELATION_USER)
            ->where(CompanyExpertReview::FIELD_COMPANY_ID, $companyId)
            ->whereNotNull(CompanyExpertReview::FIELD_DELETED_AT)
            ->orderBy(CompanyExpertReview::UPDATED_AT, 'DESC')
            ->get();
    }

    /**
     * @param int $companyId
     * @param string $body
     * @param int|null $id
     * @return bool
     */
    public function createOrUpdateCompanyExpertReview(
        int $companyId,
        string $body,
        ?int $id = null
    ): Bool
    {
        return CompanyExpertReview::query()->updateOrCreate(
                [
                    CompanyExpertReview::FIELD_ID => $id
                ],
                [
                    CompanyExpertReview::FIELD_COMPANY_ID => $companyId,
                    CompanyExpertReview::FIELD_BODY => $body,
                    CompanyExpertReview::FIELD_USER_ID => Auth::user()?->id,
                ]
            ) !== null;
    }
}