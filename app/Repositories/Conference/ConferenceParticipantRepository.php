<?php

namespace App\Repositories\Conference;

use App\Models\Conference\ConferenceParticipant;

class ConferenceParticipantRepository
{
    /**
     * @param int $conferenceId
     * @param string $externalId
     * @param string|null $name
     * @param string|null $earliestStartTime
     * @param string|null $latestEndTime
     * @param int|null $durationInSeconds
     * @return ConferenceParticipant
     */
    public function updateOrCreate(
        int $conferenceId,
        string $externalId,
        ?string $name = null,
        ?string $earliestStartTime = null,
        ?string $latestEndTime = null,
        ?int $durationInSeconds = null,
    ): ConferenceParticipant
    {
        return ConferenceParticipant::query()
            ->updateOrCreate([
                ConferenceParticipant::FIELD_EXTERNAL_ID   => $externalId,
                ConferenceParticipant::FIELD_CONFERENCE_ID => $conferenceId,
            ],
                [
                    ConferenceParticipant::FIELD_NAME                => $name,
                    ConferenceParticipant::FIELD_EARLIEST_START_TIME => $earliestStartTime,
                    ConferenceParticipant::FIELD_LATEST_END_TIME     => $latestEndTime,
                    ConferenceParticipant::FIELD_DURATION_IN_SECONDS => $durationInSeconds,
                ]);
    }
}
