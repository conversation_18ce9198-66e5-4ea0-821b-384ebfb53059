<?php

namespace App\Repositories\Conference;

use App\Models\Conference\ConferenceTranscript;

class ConferenceTranscriptRepository
{
    /**
     * @param int $conferenceId
     * @param string $externalId
     * @param string|null $endTime
     * @param string|null $startTime
     * @param int|null $durationInSeconds
     * @param string|null $docsDestinationDocumentId
     * @param string|null $docsDestinationDocumentUrl
     * @return ConferenceTranscript
     */
    public function updateOrCreate(
        int $conferenceId,
        string $externalId,
        ?string $endTime = null,
        ?string $startTime = null,
        ?int $durationInSeconds = null,
        ?string $docsDestinationDocumentId = null,
        ?string $docsDestinationDocumentUrl = null,
    ): ConferenceTranscript
    {
        return ConferenceTranscript::query()
            ->updateOrCreate([
                ConferenceTranscript::FIELD_CONFERENCE_ID => $conferenceId,
                ConferenceTranscript::FIELD_EXTERNAL_ID   => $externalId,
            ], [
                ConferenceTranscript::FIELD_END_TIME                      => $endTime,
                ConferenceTranscript::FIELD_START_TIME                    => $startTime,
                ConferenceTranscript::FIELD_DURATION_IN_SECONDS           => $durationInSeconds,
                ConferenceTranscript::FIELD_DOCS_DESTINATION_DOCUMENT_ID  => $docsDestinationDocumentId,
                ConferenceTranscript::FIELD_DOCS_DESTINATION_DOCUMENT_URL => $docsDestinationDocumentUrl,
            ]);
    }
}
