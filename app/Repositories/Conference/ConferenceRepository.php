<?php

namespace App\Repositories\Conference;

use App\Models\Conference\Conference;

class ConferenceRepository
{
    /**
     * @param int $calendarEventId
     * @param int $userId
     * @param string $externalId
     * @param string|null $startTime
     * @param string|null $endTime
     * @param string|null $expireTime
     * @param int|null $durationInSeconds
     * @param string|null $status
     * @return Conference
     */
    public function updateOrCreate(
        int $calendarEventId,
        int $userId,
        string $externalId,
        ?string $startTime = null,
        ?string $endTime = null,
        ?string $expireTime = null,
        ?int $durationInSeconds = null,
        ?string $status = null,
    ): Conference
    {
        return Conference::query()
            ->updateOrCreate([
                Conference::FIELD_CALENDAR_EVENT_ID => $calendarEventId,
                Conference::FIELD_EXTERNAL_ID       => $externalId,
            ],
                [
                    Conference::FIELD_USER_ID             => $userId,
                    Conference::FIELD_START_TIME          => $startTime,
                    Conference::FIELD_END_TIME            => $endTime,
                    Conference::FIELD_EXPIRE_TIME         => $expireTime,
                    Conference::FIELD_DURATION_IN_SECONDS => $durationInSeconds,
                    Conference::FIELD_STATUS              => $status,
                ]);
    }

}
