<?php

namespace App\Repositories\Conference;

use App\Models\Conference\ConferenceTranscriptEntry;

class ConferenceTranscriptEntryRepository
{
    /**
     * @param int $conferenceTranscriptId
     * @param int $conferenceParticipantId
     * @param string $externalId
     * @param string $externalParticipantId
     * @param string $startTime
     * @param string $endTime
     * @param string|null $text
     * @return ConferenceTranscriptEntry
     */
    public function updateOrCreate(
        int $conferenceTranscriptId,
        int $conferenceParticipantId,
        string $externalId,
        ?string $externalParticipantId = null,
        ?string $startTime = null,
        ?string $endTime = null,
        ?string $text = null,
    ): ConferenceTranscriptEntry
    {
        return ConferenceTranscriptEntry::query()
            ->updateOrCreate([
                ConferenceTranscriptEntry::FIELD_CONFERENCE_TRANSCRIPT_ID  => $conferenceTranscriptId,
                ConferenceTranscriptEntry::FIELD_CONFERENCE_PARTICIPANT_ID => $conferenceParticipantId,
                ConferenceTranscriptEntry::FIELD_EXTERNAL_ID               => $externalId,
            ],
                [
                    ConferenceTranscriptEntry::FIELD_EXTERNAL_PARTICIPANT_ID => $externalParticipantId,
                    ConferenceTranscriptEntry::FIELD_START_TIME              => $startTime,
                    ConferenceTranscriptEntry::FIELD_END_TIME                => $endTime,
                    ConferenceTranscriptEntry::FIELD_TEXT                    => $text,
                ]);
    }
}
