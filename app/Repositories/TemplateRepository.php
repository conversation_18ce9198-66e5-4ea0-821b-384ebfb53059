<?php

namespace App\Repositories;

use App\Enums\TemplateManagement\TemplatePurposeKey;
use App\Enums\TemplateManagement\TemplateRelation;
use App\Enums\TemplateManagement\TemplateType;
use App\Models\EmailTemplate;
use App\Models\Template;
use App\Models\TemplateSelector;
use App\Models\User;
use Illuminate\Support\Collection;

class TemplateRepository
{
    /**
     * @param TemplateType $type
     * @param array $data
     *
     * @return Template
     */
    public function createTemplate(TemplateType $type, array $data): Template
    {
        $owner = $this->getTemplateOwner();

        return Template::query()->create([
            Template::FIELD_TYPE => $type,
            Template::FIELD_OWNER_TYPE => $owner::class,
            Template::FIELD_OWNER_ID => $owner->id,
            ...$data
        ]);
    }

    /**
     * @param Template $template
     * @param array $data
     *
     * @return bool
     */
    public function updateTemplate(Template $template, array $data): bool
    {
        return $template->update($data);
    }

    /**
     * @param TemplateType $type
     *
     * @return Collection<Template>
     */
    public function getTemplatesByType(TemplateType $type): Collection
    {
        return Template::query()->where(Template::FIELD_TYPE, $type)->latest()->get();
    }

    /**
     * @param TemplateRelation $relation
     * @param TemplatePurposeKey $purposeKey
     * @param EmailTemplate|Template $template
     * @param int|null $industryId
     *
     * @return TemplateSelector
     */
    public function createTemplateSelector(
        TemplateRelation $relation,
        TemplatePurposeKey $purposeKey,
        EmailTemplate|Template $template,
        ?int $industryId = null
    ): TemplateSelector
    {
        return TemplateSelector::query()->create([
            TemplateSelector::FIELD_RELATION => $relation,
            TemplateSelector::FIELD_PURPOSE_KEY => $purposeKey,
            TemplateSelector::FIELD_TEMPLATE_TYPE => $template::class,
            TemplateSelector::FIELD_TEMPLATE_ID => $template->id,
            TemplateSelector::FIELD_INDUSTRY_ID => $industryId
        ]);
    }

    /**
     * @param string $type
     * @param int $id
     *
     * @return EmailTemplate|Template
     */
    public function getTemplateModelFromTypeAndId(string $type, int $id): EmailTemplate|Template
    {
        return match ($type) {
            'email' => EmailTemplate::query()->findOrFail($id),
            default => Template::query()->findOrFail($id)
        };
    }

    /**
     * @param TemplateRelation $templateRelation
     * @param TemplatePurposeKey|null $templatePurposeKey
     * @param int|null $industryId
     * @param string|null $templateType
     * @param int|null $templateId
     *
     * @return Collection<TemplateSelector>
     */
    public function getTemplateSelectors(
        TemplateRelation $templateRelation,
        ?TemplatePurposeKey $templatePurposeKey = null,
        ?int $industryId = null,
        ?string $templateType = null,
        ?int $templateId = null
    ): Collection
    {
        $query = TemplateSelector::query()->where(TemplateSelector::FIELD_RELATION, $templateRelation);

        if ($templatePurposeKey) {
            $query->where(TemplateSelector::FIELD_PURPOSE_KEY, $templatePurposeKey);
        }

        if ($industryId) {
            $query->where(TemplateSelector::FIELD_INDUSTRY_ID, $industryId);
        }

        if ($templateType) {
            $query->where(TemplateSelector::FIELD_TEMPLATE_TYPE, $templateType);
        }

        if ($templateId) {
            $query->where(TemplateSelector::FIELD_TEMPLATE_ID, $templateId);
        }

        return $query->get();
    }

    /**
     * @return User
     */
    protected function getTemplateOwner(): User
    {
        /** @var User */
        return auth()->user();
    }
}
