<?php

namespace App\Repositories\Territory;

use App\Models\Odin\Company;
use App\Models\Territory\RelationshipManager;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class RelationshipManagerRepository
{
    /**
     * @param string|null $userName
     * @param string|null $companyName
     * @return Builder
     */
    public function listRelationshipManagers(
        ?string $userName = null,
        ?string $companyName = null
    ): Builder
    {
        $query = RelationshipManager::query();

        if (isset($userName)) {
            $query->whereHas(RelationshipManager::RELATION_USER, function ($builder) use ($userName) {
                $builder->where(User::FIELD_NAME, 'LIKE', '%' . $userName . '%');
            });
        }

        if (isset($companyName)) {
            $query->whereHas(RelationshipManager::RELATION_COMPANIES, function ($builder) use ($companyName) {
                $builder->where(Company::FIELD_NAME, 'LIKE', '%' . $companyName .'%');
            });
        }

        return $query;
    }

    /**
     * @param int $id
     * @return RelationshipManager|null
     */
    public function getRelationshipManager(int $id): ?RelationshipManager
    {
        /** @var ?RelationshipManager */
        return RelationshipManager::query()->find($id);
    }

    /**
     * Handles fetching the RM for workflow/task management
     * @param string $companyReference
     * @return RelationshipManager|null
     */
    public function getRelationshipManagerByCompanyReference(string $companyReference): ?\Illuminate\Support\Collection
    {
        $relationshipManager = Company::query()
            ->where(Company::FIELD_REFERENCE, $companyReference)
            ->first()
            ?->{Company::RELATION_RELATIONSHIP_MANAGER}
            ?->{RelationshipManager::RELATION_USER};

        return $relationshipManager
            ? collect([$relationshipManager])
            : collect();
    }

    /**
     * @param bool|null $includeInactive
     * @return Collection
     */
    public function getRelationshipManagers(?bool $includeInactive = false): Collection
    {
        $query = RelationshipManager::query();

        if (!$includeInactive) {
            $query->where(function ($builder) {
                $builder->where(RelationshipManager::FIELD_ACTIVE_TO, '=', null)
                    ->orWhere(RelationshipManager::FIELD_ACTIVE_TO, '>', now());
            });
        }

        return $query->get();

    }

    /**
     * @param RelationshipManager $relationshipManager
     * @return bool|null
     */
    public function deleteRelationshipManager(RelationshipManager $relationshipManager): ?bool
    {
        return $relationshipManager->delete();
    }

    /**
     * @param int $userId
     * @return RelationshipManager
     */
    public function createRelationshipManager(int $userId): RelationshipManager
    {
        $relationshipManager = new RelationshipManager([
            RelationshipManager::FIELD_USER_ID => $userId
        ]);

        $relationshipManager->save();

        return $relationshipManager;
    }

    /**
     * @param Company $company
     * @param int|null $relationshipManagerId
     * @return bool
     */
    public function assignRelationshipManagerToCompany(
        Company $company,
        ?int $relationshipManagerId = null
    ): bool
    {
        $company->{Company::FIELD_RELATIONSHIP_MANAGER_ID} = empty($relationshipManagerId) ? null : $relationshipManagerId;

        return $company->save();
    }
}
