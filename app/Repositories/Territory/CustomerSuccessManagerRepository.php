<?php

namespace App\Repositories\Territory;

use App\Models\Odin\Company;
use App\Models\Territory\CustomerSuccessManager;
use Illuminate\Database\Eloquent\Builder;

class CustomerSuccessManagerRepository
{
    /**
     * @param string|null $customerSuccessManagerName
     * @param string|null $companyName
     * @param bool|null $active
     * @return Builder
     */
    public function listCustomerSuccessManagers(
        ?string $customerSuccessManagerName = null,
        ?string $companyName = null,
        ?bool   $active = null,
    ): Builder
    {
        $query = CustomerSuccessManager::query();

        if (isset($customerSuccessManagerName)) {
            $query->where(CustomerSuccessManager::FIELD_NAME, 'LIKE', '%' . $customerSuccessManagerName . '%');
        }

        if (isset($companyName)) {
            $query->whereHas(CustomerSuccessManager::RELATION_COMPANIES, function ($builder) use ($companyName) {
                $builder->where(Company::FIELD_NAME, 'LIKE', '%' . $companyName . '%');
            });
        }

        if (isset($active)) {
            if ($active) {
                $query->where(function ($query) {
                    $query->where(CustomerSuccessManager::FIELD_ACTIVE_TO, '>=', now())
                        ->orWhere(CustomerSuccessManager::FIELD_ACTIVE_TO, '=', null);
                });
            } else {
                $query->where(CustomerSuccessManager::FIELD_ACTIVE_TO, '<=', now());
            }
        }

        return $query;
    }

    /**
     * @param int $id
     * @return CustomerSuccessManager|null
     */
    public function getCustomerSuccessManager(int $id): ?CustomerSuccessManager
    {
        /** @var ?CustomerSuccessManager */
        return CustomerSuccessManager::query()->find($id);
    }

}