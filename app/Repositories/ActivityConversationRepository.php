<?php

namespace App\Repositories;

use App\Contracts\Repositories\ActivityConversationRepositoryContract;
use App\Models\ActivityConversation;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class ActivityConversationRepository implements ActivityConversationRepositoryContract
{
    public function __construct(protected ActivityFeedRepository $activityFeedRepository) {}

    /**
     * @inheritDoc
     */
    public function addActivityConversation(
        int    $activityId,
        int    $parentId,
        int    $userId,
        string $comment,
        ?int   $id = null
    ): ActivityConversation|Model
    {
        $newConversation = ActivityConversation::query()->updateOrCreate(
            [
                ActivityConversation::FIELD_ID => $id
            ],
            [
                ActivityConversation::FIELD_ACTIVITY_ID => $activityId,
                ActivityConversation::FIELD_PARENT_ID   => $parentId ?? 0,
                ActivityConversation::FIELD_USER_ID     => $userId,
                ActivityConversation::FIELD_COMMENT     => $comment
            ]
        );

        if ($newConversation) {
            $this->activityFeedRepository->activityWasUpdated($activityId);
        }

        return $newConversation;
    }

    /**
     * @inheritDoc
     */
    public function getActivityConversations(int $activityId, $ignoreChildren = true): ?Collection
    {
        /** @var Builder $query */
        $query = ActivityConversation::query()
            ->where(ActivityConversation::FIELD_ACTIVITY_ID, $activityId)
            ->whereNull(ActivityConversation::FIELD_DELETED_AT)
            ->where(function(Builder $query) use($ignoreChildren){
                if($ignoreChildren === true)
                    return $query->where(ActivityConversation::FIELD_PARENT_ID, 0);
            })
            ->orderByDesc(ActivityConversation::FIELD_CREATED_AT);

        return $query->get();
    }
}
