<?php

namespace App\Repositories;

use App\Enums\UserPresetType;
use App\Models\User;
use App\Models\UserPreset;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class UserPresetRepository
{
    /**
     * @param int $userId
     * @return Collection<UserPreset>
     */
    public function getPresets(int $userId): Collection
    {
        /** @var User $user */
        $user = User::query()->findOrFail($userId);

        return $user->presets;
    }

    /**
     * @param UserPresetType $type
     * @param string|null $category
     * @param int|null $userId
     * @return Collection
     */
    public function getPresetsByTypeAndCategory(UserPresetType $type, ?string $category = null, ?int $userId = null): Collection
    {
        return UserPreset::query()
            ->with(UserPreset::RELATION_USER)
            ->when($userId, fn(Builder $query) => $query->where(UserPreset::FIELD_USER_ID, $userId))
            ->when($category, fn(Builder $query) => $query->where(UserPreset::FIELD_CATEGORY, $category))
            ->where(UserPreset::FIELD_TYPE, $type)
            ->get();
    }

    /**
     * @param int $userId
     * @param UserPresetType $type
     * @param string $name
     * @param array $payload
     * @param string|null $category
     * @return UserPreset
     */
    public function savePreset(int $userId, UserPresetType $type, string $name, array $payload, ?string $category = null): UserPreset
    {
        /** @var User $user */
        $user = User::query()->findOrFail($userId);

        /** @var UserPreset $savedPreset */
        $savedPreset = $user->presets()
            ->updateOrCreate([
                UserPreset::FIELD_TYPE          => $type,
                UserPreset::FIELD_CATEGORY      => $category,
                UserPreset::FIELD_NAME          => $name,
            ], [
                UserPreset::FIELD_VALUE         => $payload,
            ]);

        return $savedPreset;
    }

    /**
     * @param int $userId
     * @param UserPresetType $type
     * @param string $name
     * @param string|null $category
     * @return bool
     */
    public function deletePreset(int $userId, UserPresetType $type, string $name, ?string $category = null): bool
    {
        return !!UserPreset::query()
            ->where(UserPreset::FIELD_USER_ID, $userId)
            ->where(UserPreset::FIELD_TYPE, $type)
            ->where(UserPreset::FIELD_NAME, $name)
            ->when($category, fn(Builder $query) => $query->where(UserPreset::FIELD_CATEGORY, $category))
            ->delete();
    }
}
