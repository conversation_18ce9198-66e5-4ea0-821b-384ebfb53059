<?php

namespace App\Repositories;

use App\Contracts\Repositories\SuccessManagerRepositoryContract;
use App\Models\Odin\Company;
use App\Models\SuccessManagerClient;
use App\Models\SuccessManager;
use Illuminate\Support\Collection;

class SuccessManagerRepository implements SuccessManagerRepositoryContract
{
    /**
     * @inheritDoc
     */
    public function getSuccessManagers(): Collection
    {
        return SuccessManager::query()->latest()->get();
    }

    /**
     * @inheritDoc
     */
    public function getSuccessManagerByIdOrFail(int $id): Collection
    {
        return SuccessManager::query()->findOrFail($id);
    }

    /**
     * @inheritDoc
     */
    public function getSuccessManagerTypes(): array
    {
        return SuccessManager::TYPES;
    }

    /**
     * @inheritDoc
     */
    public function updateOrCreateSuccessManager(
        int  $user,
        int  $type,
        bool $includeInSalesRoundRobin,
        ?int $id = null
    ): bool
    {
        return !! SuccessManager::query()->updateOrCreate(
            [
                SuccessManager::FIELD_ID => $id
            ],
            [
                SuccessManager::FIELD_USER_ID                       => $user,
                SuccessManager::FIELD_TYPE                          => $type,
                SuccessManager::FIELD_INCLUDE_IN_SALES_ROUND_ROBIN  => $includeInSalesRoundRobin,
            ]
        );
    }

    /**
     * @inheritDoc
     */
    public function deleteSuccessManagers(SuccessManager $successManager): bool
    {
        $successManager->clients()->delete();

        return $successManager->delete();
    }

    /**
     * @inheritDoc
     */
    public function assignSuccessManager(int $companyId, ?int $successManagerId): ?SuccessManagerClient
    {
        /** @var Company|null $company */
        $company = Company::query()->find($companyId);
        if (!$company) {
            return null;
        }

        SuccessManagerClient::query()
            ->where(SuccessManagerClient::FIELD_COMPANY_REFERENCE, $company->reference)
            ->where(SuccessManagerClient::FIELD_STATUS, '=', SuccessManagerClient::STATUS_ACTIVE)
            ->update([SuccessManagerClient::FIELD_STATUS => SuccessManagerClient::STATUS_INACTIVE]);

        if (empty($successManagerId)) {
            return null;
        }

        /** @var SuccessManagerClient */
        return SuccessManagerClient::query()
            ->updateOrCreate(
                [
                    SuccessManagerClient::FIELD_COMPANY_REFERENCE  => $company->reference,
                    SuccessManagerClient::FIELD_SUCCESS_MANAGER_ID => $successManagerId
                ],
                [
                    SuccessManagerClient::FIELD_COMPANY_REFERENCE  => $company->reference,
                    SuccessManagerClient::FIELD_SUCCESS_MANAGER_ID => $successManagerId,
                    SuccessManagerClient::FIELD_STATUS             => SuccessManagerClient::STATUS_ACTIVE
                ]
            );
    }


    /**
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAlphabetizedSuccessManagersWithClients(): Collection
    {
        $successManagers = SuccessManager::query()->whereHas(SuccessManager::RELATION_CLIENTS)->with([
            SuccessManager::RELATION_USER => fn(
                $q
            ) => $q->withTrashed()
        ])->get();

        $successManagers->sortBy(function (SuccessManager $successManager) {
            return $successManager->user?->name;
        });

        return $successManagers;
    }
}
