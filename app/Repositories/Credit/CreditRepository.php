<?php

namespace App\Repositories\Credit;

use App\Models\Billing\BillingProfile;
use App\Models\Billing\Credit;
use App\Models\Billing\CreditBillingProfile;
use App\Models\Billing\CreditType;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class CreditRepository
{

    /**
     * @param int $creditId
     * @return Credit
     */
    public function getCredit(int $creditId): Credit
    {
        /* @var Credit */
        return Credit::query()->findOrFail($creditId);
    }

    /**
     * @param int $companyId
     * @return Collection
     */
    public function getCompanyCredits(int $companyId): Collection
    {
        return Credit::query()
            ->with(Credit::RELATION_BILLING_PROFILES)
            ->where(Credit::FIELD_COMPANY_ID, $companyId)
            ->where(Credit::FIELD_REMAINING_VALUE, '>', 0)
            ->orderBy(Credit::FIELD_CREDIT_TYPE)
            ->orderBy(Credit::FIELD_EXPIRES_AT, 'desc')
            ->get();
    }

    /**
     * @param int $companyId
     * @param string|null $type
     * @param int|null $billingProfileId
     * @return Collection
     */
    public function getAvailableActiveCredits(
        int $companyId,
        ?string $type = null,
        ?int $billingProfileId = null,
    ): Collection
    {
        return $this->buildBaseCreditQuery(
            companyId          : $companyId,
            type               : $type,
            billingProfileId   : $billingProfileId,
            expireDateReference: now()
        )
            ->select([
                Credit::TABLE . '.' . Credit::FIELD_CREDIT_TYPE,
                CreditType::TABLE . '.' . CreditType::FIELD_NAME,
                DB::raw('SUM(' . Credit::TABLE . '.' . Credit::FIELD_REMAINING_VALUE . ') as balance')
            ])
            ->having('balance', '>', 0)
            ->groupBy(Credit::TABLE . '.' . Credit::FIELD_CREDIT_TYPE)
            ->orderByDesc(Credit::TABLE . '.' . Credit::FIELD_CREATED_AT)
            ->get();
    }

    /**
     * @param int $companyId
     * @param int|null $billingProfileId
     * @param string|null $type
     * @param Carbon|null $expireDateReference
     * @return Collection
     */
    public function getEligibleCompanyCredits(
        int $companyId,
        ?int $billingProfileId = null,
        ?string $type = null,
        ?Carbon $expireDateReference = null
    ): Collection
    {
        $expireDateReference ??= now();

        $credits = $this->buildBaseCreditQuery(
            companyId          : $companyId,
            type               : $type,
            billingProfileId   : $billingProfileId,
            expireDateReference: $expireDateReference
        )
            ->select([
                Credit::TABLE . '.*',
                CreditType::TABLE . '.' . CreditType::FIELD_SLUG,
                CreditType::TABLE . '.' . CreditType::FIELD_ACTIVE,
                CreditType::TABLE . '.' . CreditType::FIELD_CONSUMPTION_ORDER,
                DB::raw('GROUP_CONCAT(' . CreditBillingProfile::TABLE . '.billing_profile_id) as billing_profile_ids'),
            ])
            ->orderBy(CreditType::TABLE . '.' . CreditType::FIELD_CONSUMPTION_ORDER)
            ->groupBy(Credit::TABLE . '.' . Credit::FIELD_ID)
            ->get();

        if ($billingProfileId) {
            $credits = $this->prioritizeBillingProfileCredits($credits, $billingProfileId);
        }

        return $credits->values();
    }

    /**
     * Build the base credit query with common filters
     *
     * @param int $companyId
     * @param string|null $type
     * @param int|null $billingProfileId
     * @param Carbon|null $expireDateReference
     * @return Builder
     */
    private function buildBaseCreditQuery(
        int $companyId,
        ?string $type = null,
        ?int $billingProfileId = null,
        ?Carbon $expireDateReference = null
    ): Builder
    {
        $expireDateReference ??= now();

        return Credit::query()
            ->join(
                CreditType::TABLE,
                CreditType::TABLE . '.' . CreditType::FIELD_SLUG,
                Credit::TABLE . '.' . Credit::FIELD_CREDIT_TYPE
            )
            ->leftJoin(
                CreditBillingProfile::TABLE,
                CreditBillingProfile::TABLE . '.' . CreditBillingProfile::FIELD_CREDIT_ID,
                Credit::TABLE . '.' . Credit::FIELD_ID
            )
            ->where(Credit::TABLE . '.' . Credit::FIELD_COMPANY_ID, $companyId)
            ->where(Credit::TABLE . '.' . Credit::FIELD_REMAINING_VALUE, '>', 0)
            ->where(CreditType::TABLE . '.' . CreditType::FIELD_ACTIVE, true)
            ->where(function ($query) use ($expireDateReference) {
                $query->where(Credit::TABLE . '.' . Credit::FIELD_EXPIRES_AT, '>=', $expireDateReference)
                    ->orWhereNull(Credit::TABLE . '.' . Credit::FIELD_EXPIRES_AT);
            })
            ->where(function ($query) use ($billingProfileId) {
                $query->where(function ($subQuery) use ($billingProfileId) {
                    if ($billingProfileId) {
                        $subQuery->whereNull(CreditBillingProfile::TABLE . '.' . CreditBillingProfile::FIELD_ID);

                        $subQuery->orWhereHas(Credit::RELATION_BILLING_PROFILES, function ($relationQuery) use ($billingProfileId) {
                            $relationQuery->where(BillingProfile::TABLE . '.' . BillingProfile::FIELD_ID, $billingProfileId);
                        });
                    }
                });
            })
            ->when($type, fn($query) => $query->where(Credit::TABLE . '.' . Credit::FIELD_CREDIT_TYPE, $type));
    }

    /**
     * Prioritize billing profile credits over company credits
     */
    private function prioritizeBillingProfileCredits(Collection $credits, int $billingProfileId): Collection
    {
        [$billingProfileCredits, $companyCredits] = $credits->partition(
            function ($credit) use ($billingProfileId) {
                if (empty($credit->billing_profile_ids)) {
                    return false;
                }

                return collect(explode(',', $credit->billing_profile_ids))
                    ->contains((string)$billingProfileId);
            }
        );

        return $billingProfileCredits
            ->sortBy('consumption_order')
            ->concat($companyCredits->sortBy('consumption_order'))
            ->values();
    }
}
