<?php

namespace App\Repositories\Credit;

use App\Models\Billing\CreditType;
use Illuminate\Database\Eloquent\Collection;

class CreditTypeRepository
{
    /**
     * @param bool|null $isCash
     * @return Collection
     */
    public function getCreditTypes(
        ?bool $isCash = null
    ): Collection
    {
        $query = CreditType::query()
            ->orderBy(CreditType::FIELD_CONSUMPTION_ORDER);

        if (!is_null($isCash)) {
            $query->where(CreditType::FIELD_CASH, $isCash);
        }

        return $query->get();
    }

    /**
     * @param string $slug
     * @return ?CreditType
     */
    public function getCreditTypeBySlug(string $slug): ?CreditType
    {
        /** @var ?CreditType $creditType */
        $creditType = CreditType::query()->where(CreditType::FIELD_SLUG, $slug)->first();

        return $creditType;
    }

    /**
     * @param string $name
     * @param string $slug
     * @param string $description
     * @param string $line_item_text
     * @param int|null $expires_in_days
     * @param int $consumption_order
     * @param bool $cash
     * @param bool $active
     * @param bool $invoiceable
     * @param int|null $id
     * @return CreditType
     */
    public function updateOrCreate(
        string $name,
        string $slug,
        string $description,
        string $line_item_text,
        ?int $expires_in_days,
        int $consumption_order,
        bool $cash,
        bool $active,
        ?int $id = null,
    ): CreditType
    {
        /* @var CreditType $credit */
        $credit = CreditType::query()->updateOrCreate([
            CreditType::FIELD_ID => $id,
        ], [
            CreditType::FIELD_NAME              => $name,
            CreditType::FIELD_SLUG              => $slug,
            CreditType::FIELD_DESCRIPTION       => $description,
            CreditType::FIELD_LINE_ITEM_TEXT    => $line_item_text,
            CreditType::FIELD_EXPIRES_IN_DAYS   => $expires_in_days,
            CreditType::FIELD_CONSUMPTION_ORDER => $consumption_order,
            CreditType::FIELD_CASH              => $cash,
            CreditType::FIELD_ACTIVE            => $active,
            CreditType::FIELD_INVOICEABLE       => false, // TODO - Remove
            CreditType::FIELD_ID                => $id,
        ]);

        return $credit;
    }

}
