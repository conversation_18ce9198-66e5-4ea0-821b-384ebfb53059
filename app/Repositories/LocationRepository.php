<?php

namespace App\Repositories;

use App\Models\Legacy\EloquentZipCode;
use App\Models\Legacy\Location;
use App\Services\DatabaseHelperService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class LocationRepository
{
    const string COUNTY_LOCATION_IDS = 'county_location_ids';
    const string STATE_LOCATION_IDS  = 'state_location_ids';

    /**
     * Gets locations where the column is in the values.
     *
     * @param string $column
     * @param array $values
     * @return Collection
     */
    public function getLocationsWhereIn(string $column, array $values): Collection
    {
        return Location::query()->whereIn($column, $values)->get();
    }

    /**
     * Handles returning collection of states.
     *
     * @return Collection
     */
    public function getStates(): Collection
    {
        return Location::query()
            ->where(Location::TYPE, Location::TYPE_STATE)
            ->get();
    }

    /**
     * Handles returning a state.
     *
     * @param string $stateKey
     * @return Location|null
     */
    public function getState(string $stateKey): ?Location
    {
        /** @var Location|null $location */
        $location = Location::query()
            ->where(Location::TYPE, Location::TYPE_STATE)
            ->where(Location::STATE_KEY, $stateKey)->first();

        return $location;
    }

    /**
     * @param string $stateAbbr
     *
     * @return Location|null
     */
    public function getStateByStateAbbr(string $stateAbbr): ?Location
    {
        /** @var Location|null */
        return Location::query()
            ->where(Location::TYPE, Location::TYPE_STATE)
            ->where(Location::STATE_ABBREVIATION, $stateAbbr)->first();
    }

    /**
     * @param int $locationId
     *
     * @return Location
     */
    public function getStateByIdOrFail(int $locationId): Location
    {
        /** @var Location */
        return Location::query()
            ->where(Location::TYPE, Location::TYPE_STATE)
            ->where(Location::ID, $locationId)->firstOrFail();
    }

    /**
     * Handles returning a county.
     *
     * @param string $stateKey
     * @param string $countyKey
     * @return Location|null
     */
    public function getCounty(string $stateKey, string $countyKey): ?Location
    {
        /** @var Location|null $location */
        $location = Location::query()
            ->where(Location::TYPE, Location::TYPE_COUNTY)
            ->where(Location::STATE_KEY, $stateKey)
            ->where(Location::COUNTY_KEY, $countyKey)->first();

        return $location;
    }

    /**
     * Handles getting a location by the zip code.
     *
     * @param string $zipCode
     * @return Location|null
     */
    public function getZipCode(string $zipCode): ?Location
    {
        /** @var Location|null $location */
        $location = Location::query()
            ->where(Location::TYPE, Location::TYPE_ZIP_CODE)
            ->where(Location::ZIP_CODE, $zipCode)->first();

        return $location;
    }

    /**
     * @param array $zipCodes
     * @return Collection <int, Location>
     */
    public function getZipCodes(array $zipCodes): Collection
    {
        return Location::query()
            ->where(Location::TYPE, Location::TYPE_ZIP_CODE)
            ->whereIn(Location::ZIP_CODE, $zipCodes)
            ->get();
    }

    /**
     * Returns the counties in a given state.
     *
     * @param string $stateKey
     * @return Collection
     */
    public function getCountiesInState(string $stateKey): Collection
    {
        return Location::query()->where(Location::TYPE, Location::TYPE_COUNTY)
            ->where(Location::STATE_KEY, $stateKey)
            ->get();
    }

    /**
     * @param array $stateAbbreviations
     * @return Collection
     */
    public function getCountiesInStatesList(array $stateAbbreviations): Collection
    {
        return Location::query()
            ->whereIn(Location::STATE_ABBREVIATION, $stateAbbreviations)
            ->where(Location::TYPE, Location::TYPE_COUNTY)
            ->get();
    }

    /**
     * @param string $zipCode
     *
     * @return Collection<Location>
     */
    public function getZipCodesInCountyByZipCode(string $zipCode): Collection
    {
        $zipCodeLocation = $this->getZipCode($zipCode);

        if (!$zipCodeLocation) return collect();

        return Location::query()->where(Location::TYPE, Location::TYPE_ZIP_CODE)
            ->where(Location::COUNTY_KEY, $zipCodeLocation->county_key)
            ->where(Location::STATE_KEY, $zipCodeLocation->state_key)
            ->get();
    }

    /**
     * @param array $zipCodes
     * @return Collection <int, Location>
     */
    public function getSiblingZipcodes(array $zipCodes): Collection
    {
        $zipCodeLocations = $this->getZipCodes($zipCodes);
        $stateCountyKeys = collect();
        foreach ($zipCodeLocations as $location){$stateCountyKeys[] = $location->state_abbr.':'.$location->county_key;}

        return Location::query()
            ->where(Location::TYPE, Location::TYPE_ZIP_CODE)
            ->whereRaw('CONCAT('.Location::STATE_ABBREVIATION.',":",'.Location::COUNTY_KEY.') IN ("'.$stateCountyKeys->unique()->implode('","').'")')
            ->get();
    }

    /**
     * @param string $countyKey
     * @param string $stateKey
     * @return Collection
     */
    public function getZipCodesInCounty(string $countyKey, string $stateKey): Collection
    {
        return Location::query()->where(Location::TYPE, Location::TYPE_ZIP_CODE)
            ->where(Location::COUNTY_KEY, $countyKey)
            ->where(Location::STATE_KEY, $stateKey)
            ->get();
    }

    /**
     * @param string $zipCode
     *
     * @return Location|null
     */
    public function getCountyFromZipcode(string $zipCode): ?Location
    {
        $zipCodeLocation = $this->getZipCode($zipCode);

        if (!$zipCodeLocation) return null;

        return $this->getCounty($zipCodeLocation->state_key, $zipCodeLocation->county_key);
    }

    /**
     * @param string $zipCode
     * @return Location|null
     */
    public function getStateFromZipcode(string $zipCode): ?Location
    {
        $zipCodeLocation = $this->getZipCode($zipCode);

        if (!$zipCodeLocation) return null;

        return $this->getState($zipCodeLocation->state_key);
    }

    /**
     * @return Collection<int, Location>
     */
    public function getCounties(): Collection
    {
        return Location::query()->where(Location::TYPE, Location::TYPE_COUNTY)->get();
    }

    /**
     * @param string $stateKey
     * @param string $cityKey
     * @return Location|null
     */
    public function getCityByStateAndCityKey(string $stateKey, string $cityKey): ?Location
    {
        /** @var Location */
        return Location::query()
            ->where(Location::TYPE, Location::TYPE_CITY)
            ->where(Location::STATE_KEY, $stateKey)
            ->where(Location::CITY_KEY, $cityKey)
            ->whereNull(Location::COUNTY_KEY)
            ->first();
    }

    /**
     * @param $stateAbbr
     * @return Collection
     */
    public function getCitiesInState($stateAbbr): Collection
    {
        return Location::query()
            ->where(Location::TYPE, Location::TYPE_CITY)
            ->where(Location::STATE_ABBREVIATION, $stateAbbr)
            ->whereNull(Location::COUNTY_KEY)
            ->orderBy(Location::CITY_KEY)
            ->get()
            ->keyBy(Location::CITY_KEY);
    }

    /**
     * Implement properly when required
     * @return Collection
     */
    public function getCountries(): Collection
    {
        return collect([
            [
                'display_name'  => 'United States of America',
                'code'          => 'US',
            ],
        ]);

    }

    /**
     * @param string $stateAbbr
     * @return Collection
     */
    public function totalZipCodesPerCounty(string $stateAbbr) : Collection
    {
        $results = Location::query()->toBase()->select([
            Location::COUNTY_KEY,
            DB::connection()->raw('COUNT(*) AS `number_of_zip_codes`'),
        ])
            ->where(Location::TYPE, Location::TYPE_ZIP_CODE)
            ->where(Location::STATE_ABBREVIATION, $stateAbbr)
            ->groupBy(Location::COUNTY_KEY)
            ->orderBy(Location::COUNTY_KEY)
            ->get()
            ->toArray();

        $counties = array_column($results, Location::COUNTY_KEY);

        $numberOfZipCodes = array_map(function($value) {
            return intval($value);
        }, array_column($results, "number_of_zip_codes"));

        return collect(array_combine(
            $counties,
            $numberOfZipCodes
        ));
    }

    /**
     * @param string $stateAbbreviation
     * @return Collection
     */
    public function getCountiesInStateByAbbreviation(string $stateAbbreviation): Collection
    {
        return Location::query()->where(Location::TYPE, Location::TYPE_COUNTY)
            ->where(Location::STATE_ABBREVIATION, $stateAbbreviation)
            ->get();
    }

    /**
     * @param string $countyKey
     * @return Collection
     */
    public function getZipCodesByCounty(string $stateKey, string $countyKey): Collection
    {
        return Location::query()->where(Location::TYPE, Location::TYPE_ZIP_CODE)
            ->where(Location::COUNTY_KEY, $countyKey)
            ->where(Location::STATE_ABBREVIATION, $stateKey)
            ->get();
    }

    /**
     * @param string $stateAbbreviation
     * @return Collection
     */
    public function getAllZipCodesInState(string $stateAbbreviation): Collection
    {
        return Location::query()->where(Location::TYPE, Location::TYPE_ZIP_CODE)
            ->where(Location::STATE_ABBREVIATION, $stateAbbreviation)
            ->get();
    }

    /**
     * @return Collection
     */
    public function getAllCounties(): Collection
    {
        return Location::query()->where(Location::TYPE, Location::TYPE_COUNTY)
            ->get();
    }

    /**
     * @param array $zipStrings
     * @return Collection
     */
    public function getZipCodesByZipCodeStrings(array $zipStrings): Collection
    {
        return Location::query()
            ->where(Location::TYPE, Location::TYPE_ZIP_CODE)
            ->whereIn(Location::ZIP_CODE, $zipStrings)
            ->get();
    }

    /**
     * @param string $zipCode
     * @param int $radius
     * @return Collection
     */
    public function getZipCodesByRadius(string $zipCode, int $radius): Collection
    {
        $realZip = EloquentZipCode::query()
            ->where(EloquentZipCode::FIELD_ZIP_CODE, $zipCode)
            ->whereIn(EloquentZipCode::FIELD_ZIP_TYPE, ['S', 'P', 'U'])
            ->first();
        if (!$realZip) return collect();

        $zipTypes = ['S', 'P'];

        $zipCodesInRadius = EloquentZipCode::query()->select('*')
            ->selectRaw("3959 * acos(cos(radians({$realZip->latitude})) * cos(radians(latitude))
                                                          * cos(radians(longitude) - radians({$realZip->longitude})) +
                                                        sin(radians({$realZip->latitude})) * sin(radians(latitude))) as distance")
            ->where('citytype', '<>', 'N')
            ->whereIn(EloquentZipCode::FIELD_ZIP_TYPE, $zipTypes)
            ->whereRaw("3959 * acos(cos(radians({$realZip->latitude})) * cos(radians(latitude))
                                                          * cos(radians(longitude) - radians({$realZip->longitude})) +
                                                        sin(radians({$realZip->latitude})) * sin(radians(latitude))) < $radius")
            ->groupBy(EloquentZipCode::FIELD_ZIP_CODE)
            ->orderBy('distance')
            ->pluck('zipcode');

        return Location::query()
            ->where(Location::TYPE, Location::TYPE_ZIP_CODE)
            ->whereIn(Location::ZIP_CODE, $zipCodesInRadius)
            ->get();
    }

    /**
     * Handles returning a state.
     *
     * @param string $stateAbbr
     * @return Location|null
     */
    public function getStateFromAbbr(string $stateAbbr): ?Location
    {
        /** @var Location|null $location */
        $location = Location::query()
            ->where(Location::TYPE, Location::TYPE_STATE)
            ->where(Location::STATE_ABBREVIATION, $stateAbbr)->first();

        return $location;
    }

    /**
     * Return an array of contained zipcode location_ids given a parent location_id
     * @param int $keyLocationId
     * @return array
     */
    public function getLocationIdsByParentLocationId(int $keyLocationId): array
    {
        /** @var Location $keyLocation */
        $keyLocation = Location::query()->findOrFail($keyLocationId);

        $baseQuery = Location::query()
            ->select(Location::ID)
            ->where(Location::TYPE, Location::TYPE_ZIP_CODE);

        $query =  match($keyLocation->type) {
            Location::TYPE_STATE    => $baseQuery->where(Location::STATE_ABBREVIATION, $keyLocation->state_abbr),
            Location::TYPE_CITY     => $baseQuery->where(Location::CITY_KEY, $keyLocation->city_key),
            Location::TYPE_COUNTY   => $baseQuery->where(Location::COUNTY_KEY, $keyLocation->county_key),
            default                 => $baseQuery->where(Location::ID, $keyLocation->id)
        };

        return $query->get()->toArray();
    }

    /**
     * Return a list of zip codes that belong to the county of the given zip codes
     * @param array $zipCodes
     * @return array
     */
    public function getCountyZipCodesByZipCodes(array $zipCodes): array
    {
        if (count($zipCodes) === 0) return [];

        $codes = [];

        foreach ($zipCodes as $zipCode){
            if (in_array($zipCode, $codes)) continue;

            $locationZipCodes = Location::query()
                ->select('l2.' . Location::ZIP_CODE)
                ->distinct()
                ->join(Location::TABLE . ' as l2', 'l2.' . Location::COUNTY_KEY, Location::TABLE . '.' . Location::COUNTY_KEY)
                ->where(Location::TABLE . '.' . Location::TYPE, Location::TYPE_ZIP_CODE)
                ->whereNotNull('l2.' . Location::ZIP_CODE)
                ->whereIn(Location::TABLE . '.' . Location::ZIP_CODE, $zipCodes)
                ->get()
                ->pluck(Location::ZIP_CODE)
                ->toArray();

            $codes = [...$codes, ...$locationZipCodes];
        }

        return $codes;
    }

    public function getAllZipCodesInCountyByCountyLocationIds(array $countyLocationIds): Collection
    {
        if (count($countyLocationIds) === 0) collect();

        return Location::query()
            ->select('l2.' . Location::ZIP_CODE)
            ->distinct()
            ->join(Location::TABLE . ' as l2', function ($query) {
                $query->on('l2.' . Location::STATE_KEY, Location::TABLE . '.' . Location::STATE_KEY)
                    ->on('l2.' . Location::COUNTY_KEY, Location::TABLE . '.' . Location::COUNTY_KEY);
            })
            ->whereIn(Location::TABLE . '.' . Location::ID, $countyLocationIds)
            ->whereNotNull('l2.' . Location::ZIP_CODE)
            ->where('l2.' . Location::TYPE, Location::TYPE_ZIP_CODE)
            ->pluck(Location::ZIP_CODE);
    }

    /**
     * @param array $countyLocationIds
     * @return array
     */
    public function getCountyStateKeys(array $countyLocationIds): array
    {
        return Location::query()
            ->whereIn(Location::ID, $countyLocationIds)
            ->where(Location::TYPE, Location::TYPE_COUNTY)
            ->pluck(Location::STATE_KEY, Location::ID)
            ->toArray();
    }

    /**
     * @param array $locationIds
     * @param int[] $filterByLocationIds
     * @return Collection
     */
    public function getZipCodesByLocationIds(array $locationIds, array $filterByLocationIds = null): Collection
    {
        return Location::query()
            ->where(Location::TYPE, Location::TYPE_ZIP_CODE)
            ->when($filterByLocationIds, fn(Builder $query) =>
                $query->whereIn(Location::ID, $filterByLocationIds)
            )->select([Location::ID, Location::ZIP_CODE])
            ->findMany($locationIds);
    }

    /**
     * @param int[] $zipCodeLocationIds
     * @param bool|null $countyIdsOnly
     * @return Collection
     */
    public function getCountyLocationsFromZipCodeLocations(array $zipCodeLocationIds, ?bool $countyIdsOnly = false): Collection
    {
        $query = Location::query()
            ->when($countyIdsOnly, fn(Builder $query) =>
                $query->distinct('l2.' . Location::ID)
            )->select($countyIdsOnly
                ? 'l2.' . Location::ID
                : [Location::TABLE . '.' . Location::ID . ' as location_id', 'l2.' . Location::ID . ' as county_location_id']
            )->whereIntegerInRaw(Location::TABLE . '.' . Location::ID, $zipCodeLocationIds)
            ->join(Location::TABLE . " AS l2", function ($join) {
                $join->on(
                        'l2.' . Location::STATE_ABBREVIATION,
                        '=',
                        Location::TABLE . '.' . Location::STATE_ABBREVIATION
                    )->on(
                        'l2.' . Location::COUNTY_KEY,
                        '=',
                        Location::TABLE . '.' . Location::COUNTY_KEY
                    )->where('l2.' . Location::TYPE, Location::TYPE_COUNTY);
            });

            return $countyIdsOnly
                ? $query->pluck(Location::ID)
                : $query->get();
    }

    /**
     * @param array $zipCodeLocationIds
     * @return array
     */
    public function getStateAndCountyLocationIdsFromZipCodeLocations(array $zipCodeLocationIds): array
    {
        return Location::query()
            ->join(Location::TABLE . ' as county_locations', fn(JoinClause $join) =>
                $join->on('county_locations.' . Location::COUNTY_KEY, '=', Location::TABLE .'.'. Location::COUNTY_KEY)
                    ->on('county_locations.' . Location::STATE_KEY, '=', Location::TABLE .'.'. Location::STATE_KEY)
                    ->where('county_locations.' . Location::TYPE, '=', Location::TYPE_COUNTY)
            )->join(Location::TABLE . ' as state_locations', fn(JoinClause $join) =>
                $join->on('state_locations.' . Location::STATE_KEY, '=', Location::TABLE .'.'. Location::STATE_KEY)
                ->where('state_locations.' . Location::TYPE, '=', Location::TYPE_STATE)
            )->whereIn(Location::TABLE .'.'. Location::ID, $zipCodeLocationIds)
            ->select(['county_locations.' . Location::ID . ' as county_location_id', 'state_locations.' . Location::ID . ' as state_location_id'])
            ->get()
            ->reduce(function(array $output, Location $location) {
                if ($location->county_location_id && !in_array($location->county_location_id, $output[self::COUNTY_LOCATION_IDS]))
                    $output[self::COUNTY_LOCATION_IDS][] = $location->county_location_id;
                if ($location->state_location_id && !in_array($location->state_location_id, $output[self::STATE_LOCATION_IDS]))
                    $output[self::STATE_LOCATION_IDS][] = $location->state_location_id;
                return $output;
            }, [
                self::COUNTY_LOCATION_IDS => [],
                self::STATE_LOCATION_IDS  => [],
            ]);
    }

    /**
     * @param string $zipcode
     * @return array|null
     */
    public function getLocationIdsByZipcode(string $zipcode): ?array
    {
        return Location::query()
            ->select([
                DB::raw('l.id as zip_location_id'),
                DB::raw('l.zip_code as zip_code'),
                DB::raw('counties.id as county_location_id'),
                DB::raw('counties.county as county_name'),
                DB::raw('counties.county_key'),
                DB::raw('states.id as state_location_id'),
                DB::raw('states.state as state_name'),
                DB::raw('states.state_key'),
                DB::raw('cities.id as city_location_id'),
                DB::raw('cities.city as city_name'),
                DB::raw('cities.city_key')
            ])
            ->from(Location::TABLE . ' as l')
            ->leftJoin(Location::TABLE . ' as counties', function($join) {
                $join->on('counties.county_key', '=', 'l.county_key')
                    ->on('counties.state_key', '=', 'l.state_key')
                    ->where('counties.type', '=', 'co');
            })
            ->leftJoin(Location::TABLE . ' as states', function($join) {
                $join->on('states.state_key', '=', 'counties.state_key')
                    ->where('states.type', '=', 'st');
            })
            ->leftJoin(Location::TABLE . ' as cities', function($join) {
                $join->on('cities.state_key', '=', 'states.state_key')
                    ->on('cities.county_key', '=', 'counties.county_key')
                    ->on('cities.city_key', '=', 'l.city_key')
                    ->where('cities.type', '=', 'ci');
            })
            ->where('l.type', 'zi')
            ->where('l.zip_code', $zipcode)
            ->first()
            ?->toArray();
    }
}
