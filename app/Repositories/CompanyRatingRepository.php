<?php

namespace App\Repositories;

use App\Http\Requests\ConsumerReviews\CompanyConsumerReviewsRequest;
use App\Models\ConsumerReviews\CompanyRating;
use App\Models\ConsumerReviews\ReviewData;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class CompanyRatingRepository
{
    /**
     * @param $companyId
     * @return Builder
     */
    public function getCompanyRatings($companyId): Builder
    {
        $query = $this->getBaseQuery();
        $query->where(CompanyRating::FIELD_COMPANY_ID, $companyId);
        return $query;
    }

    /**
     * @return Builder
     */
    public function getBaseQuery(): Builder
    {
        return CompanyRating::query()
                        ->with([
                            CompanyRating::RELATION_COMPANY,
                            CompanyRating::RELATION_COMPANY_LOCATION,
                        ]);
    }

    /**
     * @param int $companyId
     * @return CompanyRating|null
     */
    public function getLatestCompanyRating(int $companyId): ?CompanyRating
    {
        /** @var CompanyRating|null */
        return CompanyRating::query()
            ->where(CompanyRating::FIELD_COMPANY_ID, $companyId)
            ->latest(CompanyRating::FIELD_CREATED_AT)
            ->first();
    }

    /**
     * @param int $companyId
     * @param float $rating
     * @param Collection $reviews
     * @param int|null $companyLocationId
     * @return CompanyRating
     */
    public function createCompanyRating(int $companyId, float $rating, Collection $reviews, int $companyLocationId = null): CompanyRating
    {
        $companyRatingData = [
            CompanyRating::FIELD_DATA_COUNT_BREAKDOWN => $this->buildCompanyRatingData($reviews),
        ];

        $companyRating = new CompanyRating([
            CompanyRating::FIELD_COMPANY_ID             => $companyId,
            CompanyRating::FIELD_OVERALL_SCORE          => $rating,
            CompanyRating::FIELD_NUM_REVIEWS            => $reviews->count(),
            CompanyRating::FIELD_COMPANY_LOCATION_ID    => $companyLocationId,
            CompanyRating::FIELD_DATA                   => $companyRatingData,
        ]);

        $companyRating->save();

        return $companyRating;
    }

    /**
     * @param Collection $reviews
     * @return array
     */
    protected function buildCompanyRatingData(Collection $reviews): array
    {
        $reviewCounts = $this->getReviewCounts($reviews);
        return [
            '5 Star' => $reviewCounts[5],
            '4 Star' => $reviewCounts[4],
            '3 Star' => $reviewCounts[3],
            '2 Star' => $reviewCounts[2],
            '1 Star' => $reviewCounts[1],
        ];
    }

    /**
     * @param Collection $reviews
     * @return array
     */
    protected function getReviewCounts(Collection $reviews): array
    {
        $reviewCounts = array_fill(1, 5, 0);
        foreach ($reviews as $review) {
            $score = intval($review->reviewData->{ReviewData::FIELD_OVERALL_SCORE});
            if (1 <= $score && $score <= 5) {
                $reviewCounts[$score]++;
            }
        }
        return $reviewCounts;
    }

    public function getCompanyConsumerReviewsPaginated(CompanyConsumerReviewsRequest $companyConsumerReviewsRequest, $company): LengthAwarePaginator
    {
        $filterData = $this->getCompanyConsumerFilterByRequest($companyConsumerReviewsRequest);
    }

    /**
     * @param CompanyConsumerReviewsRequest $companyConsumerReviewsRequest
     * @return array
     */
    public function getCompanyConsumerFilterByRequest(CompanyConsumerReviewsRequest $companyConsumerReviewsRequest): array
    {
        $data = $companyConsumerReviewsRequest->safe()->collect();
        $filterData = [];
        return [
            ...$data->toArray(),
            ...$filterData,
        ];
    }


}
