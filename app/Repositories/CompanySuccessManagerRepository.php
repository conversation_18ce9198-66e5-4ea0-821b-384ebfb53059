<?php

namespace App\Repositories;

use App\Models\SuccessManagerClient;
use App\Models\Odin\Company;

class CompanySuccessManagerRepository
{
    /**
     * Handles assigning an account manager to a company.
     *
     * @param int $companyId
     * @param int|null $successManagerId
     * @return SuccessManagerClient|null
     */
    public function assignSuccessManager(int $companyId, ?int $successManagerId): ?SuccessManagerClient
    {
        /** @var Company|null $company */
        $company = Company::query()->find($companyId);
        if (!$company || !$successManagerId)
            return null;

        SuccessManagerClient::query()
            ->where(SuccessManagerClient::FIELD_COMPANY_REFERENCE, $company->{Company::FIELD_REFERENCE})
            ->update([SuccessManagerClient::FIELD_STATUS => SuccessManagerClient::STATUS_INACTIVE]);

        /** @var SuccessManagerClient */
        return SuccessManagerClient::query()
            ->updateOrCreate(
                [
                    SuccessManagerClient::FIELD_COMPANY_REFERENCE => $company->reference,
                    SuccessManagerClient::FIELD_SUCCESS_MANAGER_ID => $successManagerId,
                ],
                [
                    SuccessManagerClient::FIELD_COMPANY_REFERENCE => $company->reference,
                    SuccessManagerClient::FIELD_SUCCESS_MANAGER_ID => $successManagerId,
                    SuccessManagerClient::FIELD_STATUS => SuccessManagerClient::STATUS_ACTIVE,
                ]
            );
    }

}
