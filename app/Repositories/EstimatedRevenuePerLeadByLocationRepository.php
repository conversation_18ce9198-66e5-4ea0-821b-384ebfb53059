<?php

namespace App\Repositories;

use App\Models\EstimatedRevenuePerLeadByLocation;
use App\Models\Legacy\Location;
use App\Services\DatabaseHelperService;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class EstimatedRevenuePerLeadByLocationRepository
{
    const LOW_REVENUE_UPPER_LIMIT    = 74;
    const MEDIUM_REVENUE_UPPER_LIMIT = 199;

    /**
     * @return void
     */
    public function truncate(): void
    {
        EstimatedRevenuePerLeadByLocation::truncate();
    }

    /**
     * @param string $industry
     * @param Location|null $zipLocation
     * @return Collection
     */
    public function getDataByIndustry(
        string $industry = EstimatedRevenuePerLeadByLocation::INDUSTRY_TYPE_SOLAR,
        ?Location $zipLocation = null,
    ): Collection
    {
        return EstimatedRevenuePerLeadByLocation::query()
            ->select([
                Location::TABLE . '.' . Location::STATE,
                Location::TABLE . '.' . Location::ZIP_CODE,
                EstimatedRevenuePerLeadByLocation::FIELD_ESTIMATED_REVENUE,
                EstimatedRevenuePerLeadByLocation::FIELD_CREATED_AT
            ])
            ->join(Location::TABLE, Location::TABLE . '.' . Location::ID, '=', EstimatedRevenuePerLeadByLocation::TABLE . '.' . EstimatedRevenuePerLeadByLocation::FIELD_LOCATION_ID)
            ->where(EstimatedRevenuePerLeadByLocation::FIELD_INDUSTRY_TYPE, $industry)
            ->when($zipLocation, fn($query) => $query->where(Location::TABLE.'.'.Location::ID, $zipLocation->id))
            ->get();
    }

    /**
     * @param string $industry
     * @return Collection
     */
    public function getDataGroupedByStateAndRevenue(
        string $industry = EstimatedRevenuePerLeadByLocation::INDUSTRY_TYPE_SOLAR
    ): Collection
    {
        $data = $this->getDataByIndustry($industry)->mapToGroups(function ($item) {
            return [$item['state'] => $item];
        });

        return $data->map(function ($zips, $stateName) {
            $count = $zips->count();

            $noRevenueZips = $zips->filter(function ($zip) {
                return $zip->estimated_revenue < 1;
            })->flatten()->pluck('zip_code');

            $lowRevenueZips = $zips->filter(function ($zip) {
                return $zip->estimated_revenue > 0 && $zip->estimated_revenue <= self::LOW_REVENUE_UPPER_LIMIT;
            })->flatten()->pluck('zip_code');

            $mediumRevenueZips = $zips->filter(function ($zip) {
                return $zip->estimated_revenue > self::LOW_REVENUE_UPPER_LIMIT && $zip->estimated_revenue < self::MEDIUM_REVENUE_UPPER_LIMIT;
            })->flatten()->pluck('zip_code');

            $highRevenueZips = $zips->filter(function ($zip) {
                return $zip->estimated_revenue > self::MEDIUM_REVENUE_UPPER_LIMIT;
            })->flatten()->pluck('zip_code');

            return [
                'summary'        => [
                    'zip_count'            => $count,
                    'no_revenue_count'     => count($noRevenueZips),
                    'low_revenue_count'    => count($lowRevenueZips),
                    'medium_revenue_count' => count($mediumRevenueZips),
                    'high_revenue_count'   => count($highRevenueZips),
                    'no_revenue_zips'      => $noRevenueZips,
                    'low_revenue_zips'     => $lowRevenueZips,
                    'medium_revenue_zips'  => $mediumRevenueZips,
                    'high_revenue_zips'    => $highRevenueZips,
                    'name'                 => $stateName,
                    'timestamp'            => $zips->first()->{EstimatedRevenuePerLeadByLocation::FIELD_CREATED_AT}->timestamp
                ],
                'revenue_groups' => $zips->mapToGroups(function ($item) {
                    return [$item['estimated_revenue'] => $item['zip_code']];
                })
            ];
        });
    }

    /**
     * @param array $industryToData
     * @param Carbon $time
     * @return void
     */
    public function createData(array $industryToData, Carbon $time): void
    {
        foreach ($industryToData as $industry => $locations) {
            $columns = [
                EstimatedRevenuePerLeadByLocation::FIELD_LOCATION_ID,
                EstimatedRevenuePerLeadByLocation::FIELD_ESTIMATED_REVENUE,
                EstimatedRevenuePerLeadByLocation::FIELD_AVAILABLE_COMPANIES,
                EstimatedRevenuePerLeadByLocation::FIELD_INDUSTRY_TYPE,
                EstimatedRevenuePerLeadByLocation::FIELD_CREATED_AT,
                EstimatedRevenuePerLeadByLocation::FIELD_UPDATED_AT
            ];

            $columns = collect($columns)->join(',');

            foreach (collect($locations)->chunk(500) as $data) {
                $values = collect($data)->map(function ($data) use ($industry, $time) {

                    return "({$data[0]}, '{$data[1]}', '{$data[2]}', '{$industry}', '$time', '$time')";
                })->join(", ");

                $query = "INSERT INTO " . DatabaseHelperService::database() . '.' . EstimatedRevenuePerLeadByLocation::TABLE . " ({$columns}) VALUES " . $values;

                DB::insert($query);
            }
        }
    }

    /**
     * Returns a collection of county location ID's and zipcode location ID's that have revenue
     *
     * @param string $industry
     * @param array $locationIds
     * @return Collection
     */
    public function getAvailableRevenueByCountyZip(string $industry, array $locationIds): Collection
    {
        $stateCounties = Location::query()
                            ->select([
                                Location::ID,
                                Location::STATE_KEY,
                                Location::COUNTY_KEY
                            ])
                            ->where(Location::TYPE, Location::TYPE_COUNTY)
                            ->whereIntegerInRaw(Location::ID, $locationIds)
                            ->get()
                            ->groupBy(Location::STATE_KEY);

        foreach($stateCounties as $state => $counties) {
            $stateCounties->put($state, $counties->pluck(Location::ID, Location::COUNTY_KEY));
        }

        $zipcodes = Location::query()
                        ->select([
                            Location::ID,
                            Location::STATE_KEY,
                            Location::COUNTY_KEY,
                            Location::ZIP_CODE
                        ])
                        ->distinct()
                        ->where(Location::TYPE, Location::TYPE_ZIP_CODE)
                        ->whereNotNull(Location::STATE_KEY)
                        ->whereNotNull(Location::COUNTY_KEY)
                        ->whereNotNull(Location::ZIP_CODE)
                        ->whereIn(Location::COUNTY_KEY, $stateCounties->values()->collapse()->keys()->toArray())
                        ->whereIn(Location::STATE_KEY, $stateCounties->keys()->toArray())
                        ->get()
                        ->keyBy(Location::ID);

        $estimatedRevenueCol         = EstimatedRevenuePerLeadByLocation::FIELD_ESTIMATED_REVENUE;
        $availableCompaniesCol       = EstimatedRevenuePerLeadByLocation::FIELD_AVAILABLE_COMPANIES;
        $hasRevenue                  = 'has_revenue';

        $revenueByLocation = EstimatedRevenuePerLeadByLocation::query()
                                ->selectRaw(sprintf(
                                    "%s, %s, %s, %s, %s, %s, %s",
                                    EstimatedRevenuePerLeadByLocation::TABLE.'.'.EstimatedRevenuePerLeadByLocation::FIELD_LOCATION_ID,
                                    "IF($estimatedRevenueCol > 0 OR $availableCompaniesCol > 0, TRUE, FALSE) AS $hasRevenue",
                                    $estimatedRevenueCol,
                                    "$availableCompaniesCol",
                                    Location::TABLE.'.'.Location::STATE_KEY,
                                    Location::TABLE.'.'.Location::COUNTY_KEY,
                                    Location::TABLE.'.'.Location::ZIP_CODE
                                ))
                                ->join(Location::TABLE, function($join) {
                                    $join->on(Location::TABLE.'.'.Location::ID, '=', DatabaseHelperService::database().'.'.EstimatedRevenuePerLeadByLocation::TABLE.'.'.EstimatedRevenuePerLeadByLocation::FIELD_LOCATION_ID);
                                })
                                ->where(EstimatedRevenuePerLeadByLocation::FIELD_INDUSTRY_TYPE, $industry)
                                ->whereIntegerInRaw(EstimatedRevenuePerLeadByLocation::FIELD_LOCATION_ID, $zipcodes->keys()->toArray())
                                ->get()
                                ->keyBy(EstimatedRevenuePerLeadByLocation::FIELD_LOCATION_ID);

        $revenueByCountyZips = collect();
        foreach($zipcodes as $zipCodeId => $zipcode) {
            $countyId = $stateCounties->get($zipcode[Location::STATE_KEY])->get($zipcode[Location::COUNTY_KEY]);

            if($countyId === null) continue;

            if(empty($revenueByCountyZips->get($countyId))) {
                $revenueByCountyZips->put($countyId, collect());
            }

            $zipcodeRevenue = $revenueByLocation->get($zipCodeId);

            $revenueByCountyZips->get($countyId)->put(
                $zipCodeId,
                [
                    Location::ZIP_CODE => $zipcode[Location::ZIP_CODE],
                    $hasRevenue => !empty($zipcodeRevenue?->{$hasRevenue}),
                    $estimatedRevenueCol => $zipcodeRevenue?->{$estimatedRevenueCol},
                    $availableCompaniesCol => $zipcodeRevenue?->{$availableCompaniesCol}
                ]
            );
        }

        return $revenueByCountyZips;
    }

    /**
     * Returns zip code erpl data as:
     * [
     *     ['zip_code', 'estimated_revenue_per_lead'],
     *     ['98166', 150],
     *     ['80202', '350'],
     * ];
     *
     * @param int $industryId
     * @return array
     */
    public function getErplZipData(int $industryId): array
    {
        $erplZipCollection = EstimatedRevenuePerLeadByLocation::query()
            ->join(Location::TABLE, Location::TABLE.'.'.Location::ID, '=', EstimatedRevenuePerLeadByLocation::TABLE.'.'.EstimatedRevenuePerLeadByLocation::FIELD_LOCATION_ID)
            ->where(Location::TABLE.'.'.Location::TYPE, Location::TYPE_ZIP_CODE)
            ->where(EstimatedRevenuePerLeadByLocation::TABLE.'.'.EstimatedRevenuePerLeadByLocation::FIELD_INDUSTRY_ID, $industryId)
            ->get([
                Location::TABLE.'.'.Location::ZIP_CODE,
                EstimatedRevenuePerLeadByLocation::FIELD_ESTIMATED_REVENUE,
            ]);

        $erplArray = [['zip_code', 'estimated_revenue_per_lead']];
        foreach ($erplZipCollection as $erplZip) {
            $erplArray[] = [$erplZip->{Location::ZIP_CODE}, $erplZip->{EstimatedRevenuePerLeadByLocation::FIELD_ESTIMATED_REVENUE}];
        }

        return $erplArray;
    }
}
