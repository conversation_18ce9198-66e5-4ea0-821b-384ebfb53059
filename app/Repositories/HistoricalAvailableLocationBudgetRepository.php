<?php

namespace App\Repositories;

use App\DataModels\CampaignValueModel;
use App\Enums\CompanyConsolidatedStatus;
use App\Models\AccountManager;
use App\Models\AccountManagerClient;
use App\Models\HistoricalAvailableCampaignBudget;
use App\Models\HistoricalAvailableLocationBudget;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\Location;
use App\Models\Odin\Company;
use App\Models\Odin\ProductAssignment;
use App\Models\User;
use App\Services\DatabaseHelperService;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class HistoricalAvailableLocationBudgetRepository
{
    const STANDARD       = 'standard';
    const OMIT_REJECTION = 'omitRejection';

    const SUMMARY                                 = 'summary';
    const AVERAGE_DOLLARS                         = 'avg_dollars';
    const AVERAGE_VOLUME                          = 'avg_volume';
    const AVERAGE_DOLLARS_NO_REJECT               = 'avg_dollars_no_reject';
    const AVERAGE_VOLUME_NO_REJECT                = 'avg_volume_no_reject';
    const ZIPS_WITH_NO_LIMIT_BUDGET               = 'zips_w_no_limit_budget';
    const ZIPS_WITH_NO_ACTIVE_CAMPAIGN            = 'zips_w_no_active_cam';
    const ZIPS_WITH_ACTIVE_CAMPAIGN               = 'zips_w_active_cam';
    const ZIPS_WITH_ONE_CAMPAIGN                  = 'zips_w_1_cam';
    const ZIPS_WITH_TWO_CAMPAIGNS                 = 'zips_w_2_cams';
    const ZIPS_WITH_THREE_CAMPAIGNS               = 'zips_w_3_cams';
    const ZIPS_WITH_FOUR_OR_MORE_CAMPAIGNS        = 'zips_w_4_plus_cams';
    const ZIP_COUNT                               = 'zip_count';
    const ZIP_CODE                                = 'zip_code';
    const ZIPS                                    = 'zips';
    const ZIP                                     = 'zip';
    const COUNTY                                  = 'county';
    const COUNTIES                                = 'counties';
    const STATE                                   = 'state';
    const STATES                                  = 'states';
    const LOCATION_IDS                            = 'location_ids';
    const CAMPAIGN_IDS                            = 'cam_ids';
    const COMPANY_ID                              = 'company_id';
    const COMPANY_IDS                             = 'company_ids';
    const NO_LIMIT_CAMPAIGN_IDS                   = 'no_limit_cam_ids';
    const NO_LIMIT_COMPANY_IDS                    = 'no_limit_company_ids';
    const CAMPAIGNS                               = 'cams';
    const DOLLARS                                 = 'dollars';
    const VOLUME                                  = 'volume';
    const DOLLARS_NO_REJECT                       = 'dollars_no_reject';
    const VOLUME_NO_REJECT                        = 'volume_no_reject';
    const CAMPAIGN_COUNT                          = 'cam_count';
    const NO_LIMIT_COUNT                          = 'no_limit_count';
    const ACTIVE                                  = 'active';
    const AVAILABLE_CAMPAIGN_COUNT                = 'available_campaign_count';
    const UNLIMITED_BUDGET_COUNT                  = 'unlimited_budget_count';
    const NAME                                    = 'name';
    const STATE_NAME                              = 'state_name';
    const AVAILABLE_BUDGET_COMPANY_COUNT          = 'avail_budget_company_count';
    const COMPANY_COUNT                           = 'company_count';
    const NO_LIMIT_COMPANY_COUNT                  = 'no_limit_company_count';
    const AVAILABLE_BUDGET_COMPANY_IDS            = 'avail_budget_company_ids';
    const AVAILABLE_BUDGET_CAMPAIGN_IDS           = 'avail_budget_cam_ids';
    const AVERAGE_DOLLARS_NO_REJECT_CHANGE        = 'avg_dollars_no_reject_change';
    const AVERAGE_VOLUME_NO_REJECT_CHANGE         = 'avg_volume_no_reject_change';

    const NEW_COMPANY_IDS    = 'new_company_ids';
    const LOST_COMPANY_IDS   = 'lost_company_ids';
    const NEW_COMPANY_COUNT  = 'new_company_count';
    const LOST_COMPANY_COUNT = 'lost_company_count';

    const NEW_AVAIL_BUDGET_COMPANY_IDS    = 'new_avail_budget_company_ids';
    const LOST_AVAIL_BUDGET_COMPANY_IDS   = 'lost_avail_budget_company_ids';
    const NEW_AVAIL_BUDGET_COMPANY_COUNT  = 'new_avail_budget_company_count';
    const LOST_AVAIL_BUDGET_COMPANY_COUNT = 'lost_avail_budget_company_count';

    const NEW_NO_LIMIT_COMPANY_IDS    = 'new_no_limit_company_ids';
    const LOST_NO_LIMIT_COMPANY_IDS   = 'lost_no_limit_company_ids';
    const NEW_NO_LIMIT_COMPANY_COUNT  = 'new_no_limit_company_count';
    const LOST_NO_LIMIT_COMPANY_COUNT = 'lost_no_limit_company_count';

    protected $csvContent;

    /**
     * Handles setting the global memory limit for this repository.
     */
    public function __construct()
    {
        ini_set('memory_limit', "2048M");
    }

    /**
     * @param array $companyIds
     * @return Collection|array
     */
    public function getCompanyDetails(array $companyIds): Collection|array
    {
        $companies = $this->getCompanyData($companyIds);

        $thisMonth      = $this->getCompanyRevenueForMonthsAgo($companyIds);
        $lastMonth      = $this->getCompanyRevenueForMonthsAgo($companyIds, 1);
        $twoMonthsAgo   = $this->getCompanyRevenueForMonthsAgo($companyIds, 2);
        $threeMonthsAgo = $this->getCompanyRevenueForMonthsAgo($companyIds, 3);
        $fourMonthsAgo  = $this->getCompanyRevenueForMonthsAgo($companyIds, 4);

        $thisMonthCarbon      = Carbon::today()->startOfMonth();
        $lastMonthCarbon      = Carbon::today()->startOfMonth()->subMonth();
        $twoMonthsAgoCarbon   = Carbon::today()->startOfMonth()->subMonth(2);
        $threeMonthsAgoCarbon = Carbon::today()->startOfMonth()->subMonth(3);
        $fourMonthsAgoCarbon  = Carbon::today()->startOfMonth()->subMonth(4);

        return $companies->map(function ($company) use (
            $thisMonth, $lastMonth, $twoMonthsAgo, $threeMonthsAgo, $fourMonthsAgo, $thisMonthCarbon, $lastMonthCarbon, $twoMonthsAgoCarbon, $threeMonthsAgoCarbon, $fourMonthsAgoCarbon
        ) {
            return [
                'id'                  => $company->{Company::FIELD_ID},
                'name'                => $company->{Company::FIELD_NAME},
                'status'              => CompanyConsolidatedStatus::label($company->{Company::FIELD_CONSOLIDATED_STATUS}),
                'last_lead_purchased' => $this->getLastLeadPurchasedByCompanyId($company->{Company::FIELD_ID})?->{ProductAssignment::FIELD_DELIVERED_AT}?->format('M d, Y') ?? null,
                'campaigns'           => [
                    'active_count' => $company->campaigns->where(LeadCampaign::STATUS, LeadCampaign::STATUS_ACTIVE)->count(),
                    'paused_count' => $company->campaigns->whereNotNull(LeadCampaign::REACTIVATE_DATE)->count(),
                    'off_count'    => $company->campaigns->where(LeadCampaign::STATUS, LeadCampaign::STATUS_INACTIVE)->whereNull(LeadCampaign::REACTIVATE_DATE)->count()
                ],
                'revenue_groups'      => [
                    $this->getArrayDataForMonth($thisMonth->where(Company::FIELD_ID, $company->{Company::FIELD_ID})->first(), $thisMonthCarbon, true),
                    $this->getArrayDataForMonth($lastMonth->where(Company::FIELD_ID, $company->{Company::FIELD_ID})->first(), $lastMonthCarbon),
                    $this->getArrayDataForMonth($twoMonthsAgo->where(Company::FIELD_ID, $company->{Company::FIELD_ID})->first(), $twoMonthsAgoCarbon),
                    $this->getArrayDataForMonth($threeMonthsAgo->where(Company::FIELD_ID, $company->{Company::FIELD_ID})->first(), $threeMonthsAgoCarbon),
                    $this->getArrayDataForMonth($fourMonthsAgo->where(Company::FIELD_ID, $company->{Company::FIELD_ID})->first(), $fourMonthsAgoCarbon),
                ]
            ];
        });
    }

    private function getCompanyWeeklyDetails(array $companyIds): Collection|array
    {
        $companies = $this->getCompanyDataForCsv($companyIds);

        $end = Carbon::today()->weekOfYear;

        $weeks = [];

        for($i = 1; $i < $end; $i++) {
            $weeks[] = $this->getCompanyRevenueForWeek($companyIds, $i);
        }

        return $companies->map(function ($company) use ($weeks) {
            return [
                'id'              => $company->{Company::FIELD_ID},
                'name'            => $company->{Company::FIELD_NAME},
                'account_manager' => $company->account_manager,
                'average_daily_revenue_by_week' => Arr::map($weeks, function ($week) use ($company) {
                    return round(($week->where(Company::FIELD_ID, $company->{Company::FIELD_ID})->first()?->{ProductAssignment::FIELD_COST}) ?? 0);
                })
            ];
        });
    }

    /**
     * @return string
     */
    public function getCsvOfWeeklyDetails(): string
    {
        $end = Carbon::today()->weekOfYear;

        $weeks = [];

        for($i = 1; $i < $end; $i++) {
            $weeks[] = [
                Carbon::today()->startOfYear()->addWeeks($i-1)->format('M-d'),
                Carbon::today()->startOfYear()->addWeeks($i-1)->endOfWeek()->format('M-d')
            ];
        }

        $companyIds = EloquentQuoteCompany::query()
            ->select(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::COMPANY_ID)
            ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::TIMESTAMP_DELIVERED, '>', Carbon::today()->startOfYear()->timestamp)
            ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::CHARGEABLE, true)
            ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::DELIVERED, true)
            ->groupBy(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::COMPANY_ID)
            ->pluck(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::COMPANY_ID)
            ->toArray();

        $this->csvContent = "ID|Name|Account Manager|Revenue\n";
        $details = $this->getCompanyWeeklyDetails($companyIds);

        $details->map(function ($detail) {
            $id = $detail['id'];
            $name = $detail['name'];
            $accountManager = $detail['account_manager'];
            $revenue = implode(",", $detail['average_daily_revenue_by_week']);

            $this->csvContent .= "$id|$name|$accountManager|$revenue\n";
        });

        return $this->csvContent;
    }

    /**
     * @param array $companyIds
     * @return Collection
     */
    private function getCompanyDataForCsv(array $companyIds): Collection
    {
        return Company::query()
            ->with([
                Company::RELATION_CAMPAIGNS
            ])
            ->select(
                Company::TABLE.'.'.Company::FIELD_ID,
                Company::TABLE.'.'.Company::FIELD_NAME,
                Company::TABLE.'.'.Company::FIELD_CONSOLIDATED_STATUS,
                Company::TABLE.'.'.Company::FIELD_LEGACY_ID,
                User::TABLE.'.'.User::FIELD_NAME . ' as account_manager'
            )
            ->join(AccountManagerClient::TABLE, function($join) {
                $join->on(
                    AccountManagerClient::TABLE.'.'.AccountManagerClient::FIELD_COMPANY_REFERENCE,
                    '=',
                    Company::TABLE.'.'.Company::FIELD_REFERENCE
                );

                $join
                    ->where(AccountManagerClient::TABLE.'.'.AccountManagerClient::FIELD_STATUS, AccountManagerClient::STATUS_ACTIVE)
                    ->whereNull(AccountManagerClient::TABLE.'.'.AccountManagerClient::FIELD_DELETED_AT)
                    ->orderByDesc(AccountManagerClient::TABLE.'.'.AccountManagerClient::CREATED_AT);
            })
            ->join(
                AccountManager::TABLE,
                AccountManager::TABLE.'.'.AccountManager::FIELD_ID,
                '=',
                AccountManagerClient::TABLE.'.'.AccountManagerClient::FIELD_ACCOUNT_MANAGER_ID
            )
            ->join(
                User::TABLE,
                User::TABLE.'.'.User::FIELD_ID,
                '=',
                AccountManager::TABLE.'.'.AccountManager::FIELD_USER_ID
            )
            ->whereIn(Company::TABLE.'.'.Company::FIELD_LEGACY_ID, $companyIds)
            ->groupBy(Company::TABLE.'.'.Company::FIELD_ID)
            ->get();
    }

    /**
     * @param array $companyIds
     * @return Collection
     */
    private function getCompanyData(array $companyIds): Collection
    {
        return Company::query()
            ->with([
                Company::RELATION_CAMPAIGNS
            ])
            ->select(
                Company::TABLE.'.'.Company::FIELD_ID,
                Company::TABLE.'.'.Company::FIELD_NAME,
                Company::TABLE.'.'.Company::FIELD_CONSOLIDATED_STATUS,
                Company::TABLE.'.'.Company::FIELD_LEGACY_ID
            )
            ->whereIn(Company::TABLE.'.'.Company::FIELD_LEGACY_ID, $companyIds)
            ->get();
    }

    private function getLastLeadPurchasedByCompanyId(int $companyId) {
        return ProductAssignment::query()
            ->select(ProductAssignment::FIELD_DELIVERED_AT)
            ->where(ProductAssignment::FIELD_COMPANY_ID, $companyId)
            ->where(ProductAssignment::FIELD_CHARGEABLE, true)
            ->where(ProductAssignment::FIELD_DELIVERED, true)
            ->orderByDesc(ProductAssignment::FIELD_DELIVERED_AT)
            ->first();
    }

    /**
     * @param $monthData
     * @param Carbon $carbonMonth
     * @param bool $isCurrentMonth
     * @return array
     */
    private function getArrayDataForMonth($monthData, Carbon $carbonMonth, $isCurrentMonth = false): array
    {
        $revenue = $monthData->cost ?? 0;
        $count = $monthData->count ?? 0;
        $days = $isCurrentMonth ? (Carbon::today()->day - 1) : $carbonMonth->daysInMonth;

        $avgRevenuePerLead = $count > 0 ? $revenue / $count : 0;
        $avgRevenuePerDay  = $days > 0 ? $revenue / $days : 0;

        return [
            'revenue'              => number_format($revenue),
            'count'                => $count,
            'avg_revenue_per_day'  => number_format($avgRevenuePerDay),
            'avg_revenue_per_lead' => number_format($avgRevenuePerLead),
            'month_name'           => "{$carbonMonth->shortMonthName} {$carbonMonth->year}"
        ];
    }

    /**
     * @param array $companyIds
     * @param int $week
     * @return Collection
     */
    private function getCompanyRevenueForWeek(array $companyIds, int $week): Collection
    {
        return Company::query()
            ->select(
                Company::TABLE.'.'.Company::FIELD_ID
            )
            ->selectRaw('sum(cost) as cost')
            ->leftJoin(ProductAssignment::TABLE, function($join) use ($week) {
                $join->on(
                    Company::TABLE.'.'.Company::FIELD_ID,
                    '=',
                    ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_COMPANY_ID
                );

                $join->where(ProductAssignment::TABLE.'.'.ProductAssignment::CREATED_AT, '>', Carbon::today()->startOfYear()->addWeeks($week-1))
                    ->where(ProductAssignment::TABLE.'.'.ProductAssignment::CREATED_AT, '<', Carbon::today()->startOfYear()->addWeeks($week-1)->endOfWeek())
                    ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CHARGEABLE, true)
                    ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_DELIVERED, true);
            })
            ->whereIn(Company::TABLE.'.'.Company::FIELD_LEGACY_ID, $companyIds)
            ->groupBy(Company::TABLE.'.'.Company::FIELD_ID)
            ->get();
    }

    /**
     * @param array $companyIds
     * @param int $monthsAgo
     * @return Collection
     */
    private function getCompanyRevenueForMonthsAgo(array $companyIds, int $monthsAgo = 0): Collection
    {
        return Company::query()
            ->select(
                Company::TABLE.'.'.Company::FIELD_ID,
                Company::TABLE.'.'.Company::FIELD_NAME,
                Company::TABLE.'.'.Company::FIELD_CONSOLIDATED_STATUS
            )
            ->selectRaw('sum(cost) as cost, count(product_assignments.id) as count')
            ->leftJoin(ProductAssignment::TABLE, function($join) use ($monthsAgo) {
                $join->on(
                    Company::TABLE.'.'.Company::FIELD_ID,
                    '=',
                    ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_COMPANY_ID
                );

                if($monthsAgo === 0) {
                    $join->where(ProductAssignment::TABLE.'.'.ProductAssignment::CREATED_AT, '>', Carbon::today()->startOfMonth())
                        ->where(ProductAssignment::TABLE.'.'.ProductAssignment::CREATED_AT, '<', Carbon::now());
                } else {
                    $join->where(ProductAssignment::TABLE.'.'.ProductAssignment::CREATED_AT, '>', Carbon::today()->startOfMonth()->subMonths($monthsAgo))
                        ->where(ProductAssignment::TABLE.'.'.ProductAssignment::CREATED_AT, '<', Carbon::today()->startOfMonth()->subMonths($monthsAgo - 1));
                }

                $join->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CHARGEABLE, true)
                    ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_DELIVERED, true);
            })
            ->whereIn(Company::TABLE.'.'.Company::FIELD_LEGACY_ID, $companyIds)
            ->groupBy(Company::TABLE.'.'.Company::FIELD_ID)
            ->get();
    }

    /**
     * @param Collection $data
     * @return Collection
     */
    private function formatCountyLevelLocationData(Collection $data): Collection
    {
        return $data->map(function ($counties) {
            return $counties->map(function ($zipCodes) {

                $count = $zipCodes->count();

                $totalAvailableDollars = $zipCodes->sum(function ($zipCode) {
                    return $zipCode->budget_available_dollars;
                });

                $totalAvailableVolume = $zipCodes->sum(function ($zipCode) {
                    return $zipCode->budget_available_volume;
                });

                $totalAvailableDollarsOmitRejection = $zipCodes->sum(function ($zipCode) {
                    return $zipCode->budget_available_dollars_omit_rejection;
                });

                $totalAvailableVolumeOmitRejection = $zipCodes->sum(function ($zipCode) {
                    return $zipCode->budget_available_volume_omit_rejection;
                });

                $locationIds = $zipCodes->map(function ($zipCode) {
                    return $zipCode->location_id;
                });


                return [
                    self::SUMMARY => [
                        self::NAME                                    => $zipCodes->first()->county,
                        self::STATE_NAME                              => $zipCodes->first()->state,
                        self::AVERAGE_DOLLARS                         => $count !== 0 ? round($totalAvailableDollars / $count) : 0,
                        self::AVERAGE_VOLUME                          => $count !== 0 ? round($totalAvailableVolume / $count) : 0,
                        self::AVERAGE_DOLLARS_NO_REJECT               => $count !== 0 ? round($totalAvailableDollarsOmitRejection / $count) : 0,
                        self::AVERAGE_VOLUME_NO_REJECT                => $count !== 0 ? round($totalAvailableVolumeOmitRejection / $count) : 0,
                        self::ZIPS_WITH_NO_ACTIVE_CAMPAIGN            => $zipCodes->whereNull(self::AVAILABLE_CAMPAIGN_COUNT)->count(),
                        self::ZIPS_WITH_ACTIVE_CAMPAIGN               => $zipCodes->where(self::AVAILABLE_CAMPAIGN_COUNT, '>', 0)->count(),
                        self::ZIPS_WITH_ONE_CAMPAIGN                  => $zipCodes->where(self::AVAILABLE_CAMPAIGN_COUNT, '=', 1)->count(),
                        self::ZIPS_WITH_TWO_CAMPAIGNS                 => $zipCodes->where(self::AVAILABLE_CAMPAIGN_COUNT, '=', 2)->count(),
                        self::ZIPS_WITH_THREE_CAMPAIGNS               => $zipCodes->where(self::AVAILABLE_CAMPAIGN_COUNT, '=', 3)->count(),
                        self::ZIPS_WITH_FOUR_OR_MORE_CAMPAIGNS        => $zipCodes->where(self::AVAILABLE_CAMPAIGN_COUNT, '>=', 4)->count(),
                        self::ZIPS_WITH_NO_LIMIT_BUDGET               => $zipCodes->where(self::UNLIMITED_BUDGET_COUNT, '>', 0)->count(),
                        self::ZIP_COUNT                               => $count,
                        self::LOCATION_IDS                            => $locationIds,
                        self::CAMPAIGN_IDS                            => null,
                        self::COMPANY_IDS                             => null,
                        self::COMPANY_COUNT                           => null,
                        self::AVAILABLE_BUDGET_COMPANY_IDS            => null,
                        self::AVAILABLE_BUDGET_CAMPAIGN_IDS           => null,
                        self::AVAILABLE_BUDGET_COMPANY_COUNT          => null,
                        self::NO_LIMIT_CAMPAIGN_IDS                   => null,
                        self::NO_LIMIT_COMPANY_IDS                    => null,
                        self::NO_LIMIT_COMPANY_COUNT                  => null
                    ],
                    self::ZIPS    => $zipCodes->map(function ($zipCode) {
                        return [
                            self::ZIP                           => $zipCode->zip_code,
                            self::DOLLARS                       => isset($zipCode->budget_available_dollars) ? round($zipCode->budget_available_dollars) : null,
                            self::VOLUME                        => $zipCode->budget_available_volume,
                            self::DOLLARS_NO_REJECT             => isset($zipCode->budget_available_dollars_omit_rejection) ? round($zipCode->budget_available_dollars_omit_rejection) : null,
                            self::VOLUME_NO_REJECT              => $zipCode->budget_available_volume_omit_rejection,
                            self::CAMPAIGN_COUNT                => $zipCode->available_campaign_count,
                            self::NO_LIMIT_COUNT                => $zipCode->unlimited_budget_count,
                            self::ACTIVE                        => (isset($zipCode->available_campaign_count) && $zipCode->available_campaign_count) > 0,
                            self::CAMPAIGN_IDS                  => null,
                            self::COMPANY_IDS                   => null,
                            self::CAMPAIGNS                     => null,
                            self::AVAILABLE_BUDGET_COMPANY_IDS  => null,
                            self::AVAILABLE_BUDGET_CAMPAIGN_IDS => null,
                            self::NO_LIMIT_CAMPAIGN_IDS         => null,
                            self::NO_LIMIT_COMPANY_IDS          => null,
                        ];
                    })
                ];
            });
        });
    }

    /**
     * @param Collection $data
     * @return Collection
     */
    private function formatStateLevelLocationData(Collection $data): Collection
    {
        return $data->map(function ($counties) {
            $count = $counties->count();

            $zipsInState = $counties->sum(function ($county) {
                return $county[self::SUMMARY][self::ZIP_COUNT];
            });

            $zipsWithNoActiveCampaigns = $counties->sum(function ($county) {
                return $county[self::SUMMARY][self::ZIPS_WITH_NO_ACTIVE_CAMPAIGN];
            });

            $zipsWithActiveCampaigns = $counties->sum(function ($county) {
                return $county[self::SUMMARY][self::ZIPS_WITH_ACTIVE_CAMPAIGN];
            });

            $zipsWith1Campaign = $counties->sum(function ($county) {
                return $county[self::SUMMARY][self::ZIPS_WITH_ONE_CAMPAIGN];
            });

            $zipsWith2Campaigns = $counties->sum(function ($county) {
                return $county[self::SUMMARY][self::ZIPS_WITH_TWO_CAMPAIGNS];
            });

            $zipsWith3Campaigns = $counties->sum(function ($county) {
                return $county[self::SUMMARY][self::ZIPS_WITH_THREE_CAMPAIGNS];
            });

            $zipsWith4OrMoreCampaigns = $counties->sum(function ($county) {
                return $county[self::SUMMARY][self::ZIPS_WITH_FOUR_OR_MORE_CAMPAIGNS];
            });

            $zipsWithNoLimitBudget = $counties->sum(function ($county) {
                return $county[self::SUMMARY][self::ZIPS_WITH_NO_LIMIT_BUDGET];
            });

            $totalAvailableDollars = $counties->sum(function ($county) {
                return $county[self::SUMMARY][self::AVERAGE_DOLLARS];
            });

            $totalAvailableVolume = $counties->sum(function ($county) {
                return $county[self::SUMMARY][self::AVERAGE_VOLUME];
            });

            $totalAvailableDollarsOmitRejection = $counties->sum(function ($county) {
                return $county[self::SUMMARY][self::AVERAGE_DOLLARS_NO_REJECT];
            });

            $totalAvailableVolumeOmitRejection = $counties->sum(function ($county) {
                return $county[self::SUMMARY][self::AVERAGE_VOLUME_NO_REJECT];
            });

            return [
                self::SUMMARY  => [
                    self::NAME                                    => $counties->first()[self::SUMMARY][self::STATE_NAME],
                    self::AVERAGE_DOLLARS                         => $count !== 0 ? round($totalAvailableDollars / $count) : 0,
                    self::AVERAGE_VOLUME                          => $count !== 0 ? round($totalAvailableVolume / $count) : 0,
                    self::AVERAGE_DOLLARS_NO_REJECT               => $count !== 0 ? round($totalAvailableDollarsOmitRejection / $count) : 0,
                    self::AVERAGE_VOLUME_NO_REJECT                => $count !== 0 ? round($totalAvailableVolumeOmitRejection / $count) : 0,
                    self::ZIPS_WITH_NO_ACTIVE_CAMPAIGN            => $zipsWithNoActiveCampaigns,
                    self::ZIPS_WITH_ACTIVE_CAMPAIGN               => $zipsWithActiveCampaigns,
                    self::ZIPS_WITH_ONE_CAMPAIGN                  => $zipsWith1Campaign,
                    self::ZIPS_WITH_TWO_CAMPAIGNS                 => $zipsWith2Campaigns,
                    self::ZIPS_WITH_THREE_CAMPAIGNS               => $zipsWith3Campaigns,
                    self::ZIPS_WITH_FOUR_OR_MORE_CAMPAIGNS        => $zipsWith4OrMoreCampaigns,
                    self::ZIPS_WITH_NO_LIMIT_BUDGET               => $zipsWithNoLimitBudget,
                    self::ZIP_COUNT                               => $zipsInState,
                    self::AVAILABLE_BUDGET_CAMPAIGN_IDS           => null,
                    self::AVAILABLE_BUDGET_COMPANY_IDS            => null,
                    self::AVAILABLE_BUDGET_COMPANY_COUNT          => null,
                    self::NO_LIMIT_CAMPAIGN_IDS                   => null,
                    self::NO_LIMIT_COMPANY_IDS                    => null,
                    self::NO_LIMIT_COMPANY_COUNT                  => null,
                    self::COMPANY_IDS                             => null,
                    self::COMPANY_COUNT                           => null,
                    self::CAMPAIGN_IDS                            => null
                ],
                self::COUNTIES => $counties
            ];
        });
    }

    /**
     * @param Collection $data
     * @return Collection
     */
    private function formatLocationData(Collection $data): Collection
    {
        $data = $this->getDataGroupedToCounty($data);
        $data = $this->formatCountyLevelLocationData($data);
        return $this->formatStateLevelLocationData($data);
    }

    /**
     * @param Collection $data
     * @return Collection
     */
    private function getDataGroupedToState(Collection $data): Collection
    {
        return $data->mapToGroups(function ($item) {
            return [$item[self::STATE] => $item];
        });
    }

    /**
     * @param Collection $data
     * @return Collection
     */
    private function getDataGroupedToCounty(Collection $data): Collection
    {
        return $this->getDataGroupedToState($data)->map(function ($state) {
            return $state->mapToGroups(function ($item) {
                return [$item[self::COUNTY] => $item];
            });
        });
    }


    /**
     * @param Collection $data
     * @return Collection
     */
    private function getDataGroupedToZip(Collection $data): Collection
    {
        return $this->getDataGroupedToCounty($data)->map(function ($state) {
            return $state->map(function ($county) {
                return $county->mapToGroups(function ($item) {
                    return [$item[self::ZIP_CODE] => $item];
                });
            });
        });
    }

    /**
     * @param Collection $data
     * @return Collection
     */
    private function formatZipLevelCampaignData(Collection $data): Collection
    {
        return $data->map(function ($counties) {
            return $counties->map(function ($zips) {
                return $zips->map(function ($zip) {
                    $camIds = $zip->map(function ($campaign) {
                        return $campaign->campaign_id;
                    })->toArray();

                    $camsAvailableBudget = $zip->filter(function ($campaign) {
                        return ($campaign->budget_available_dollars > 0 || $campaign->budget_available_volume > 0) || $campaign->unlimited;
                    });

                    $camIdsAvailableBudget = $camsAvailableBudget->map(function ($campaign) {
                        return $campaign->campaign_id;
                    })->toArray();

                    $companyIdsAvailableBudget = $camsAvailableBudget->map(function ($campaign) {
                        return $campaign->company_id;
                    })->toArray();

                    $companyIds = $zip->map(function ($campaign) {
                        return $campaign->company_id;
                    })->toArray();

                    $noLimitCams = $zip->filter(function ($campaign) {
                        return $campaign->unlimited === 1;
                    });

                    $noLimitCamIds = $noLimitCams->map(function ($campaign) {
                        return $campaign->campaign_id;
                    })->flatten()->unique()->values()->all();

                    $noLimitCompanyIds = $noLimitCams->map(function ($campaign) {
                        return $campaign->company_id;
                    })->flatten()->unique()->values()->all();

                    return [
                        self::CAMPAIGN_IDS                   => $camIds,
                        self::COMPANY_IDS                    => $companyIds,
                        self::COMPANY_COUNT                  => count($companyIds),
                        self::AVAILABLE_BUDGET_COMPANY_IDS   => $companyIdsAvailableBudget,
                        self::AVAILABLE_BUDGET_CAMPAIGN_IDS  => $camIdsAvailableBudget,
                        self::AVAILABLE_BUDGET_COMPANY_COUNT => count($companyIdsAvailableBudget),
                        self::NO_LIMIT_CAMPAIGN_IDS          => $noLimitCamIds,
                        self::NO_LIMIT_COMPANY_IDS           => $noLimitCompanyIds,
                        self::NO_LIMIT_COMPANY_COUNT         => count($noLimitCompanyIds),
                    ];
                });
            });
        });
    }

    /**
     * @param Collection $data
     * @return Collection
     */
    private function formatCountyLevelCampaignData(Collection $data): Collection
    {
        return $data->map(function ($counties) {
            return $counties->map(function ($zips) {
                $camIds = $zips->map(function ($zip) {
                    return $zip[self::CAMPAIGN_IDS];
                })->flatten()->unique()->values()->all();

                $companyIds = $zips->map(function ($zip) {
                    return $zip[self::COMPANY_IDS];
                })->flatten()->unique()->values()->all();

                $camIdsAvailableBudget = $zips->map(function ($zip) {
                    return $zip[self::AVAILABLE_BUDGET_CAMPAIGN_IDS];
                })->flatten()->unique()->values()->all();

                $companyIdsAvailableBudget = $zips->map(function ($zip) {
                    return $zip[self::AVAILABLE_BUDGET_COMPANY_IDS];
                })->flatten()->unique()->values()->all();

                $noLimitCamIds = $zips->map(function ($zip) {
                    return $zip[self::NO_LIMIT_CAMPAIGN_IDS];
                })->flatten()->unique()->values()->all();

                $noLimitCompanyIds = $zips->map(function ($zip) {
                    return $zip[self::NO_LIMIT_COMPANY_IDS];
                })->flatten()->unique()->values()->all();

                return [
                    self::SUMMARY => [
                        self::AVAILABLE_BUDGET_COMPANY_IDS   => $companyIdsAvailableBudget,
                        self::AVAILABLE_BUDGET_COMPANY_COUNT => count($companyIdsAvailableBudget),
                        self::AVAILABLE_BUDGET_CAMPAIGN_IDS  => $camIdsAvailableBudget,
                        self::CAMPAIGN_IDS                   => $camIds,
                        self::COMPANY_IDS                    => $companyIds,
                        self::COMPANY_COUNT                  => count($companyIds),
                        self::NO_LIMIT_CAMPAIGN_IDS          => $noLimitCamIds,
                        self::NO_LIMIT_COMPANY_IDS           => $noLimitCompanyIds,
                        self::NO_LIMIT_COMPANY_COUNT         => count($noLimitCompanyIds),
                    ],
                    self::ZIPS    => $zips
                ];
            });
        });
    }

    /**
     * @param Collection $data
     * @return Collection
     */
    private function formatStateLevelCampaignData(Collection $data): Collection
    {
        return $data->map(function ($counties) {
            $camIds = $counties->map(function ($county) {
                return $county[self::SUMMARY][self::CAMPAIGN_IDS];
            })->flatten()->unique()->values()->all();

            $companyIds = $counties->map(function ($county) {
                return $county[self::SUMMARY][self::COMPANY_IDS];
            })->flatten()->unique()->values()->all();

            $camIdsAvailableBudget = $counties->map(function ($county) {
                return $county[self::SUMMARY][self::AVAILABLE_BUDGET_CAMPAIGN_IDS];
            })->flatten()->unique()->values()->all();

            $companyIdsAvailableBudget = $counties->map(function ($county) {
                return $county[self::SUMMARY][self::AVAILABLE_BUDGET_COMPANY_IDS];
            })->flatten()->unique()->values()->all();

            $noLimitCamIds = $counties->map(function ($county) {
                return $county[self::SUMMARY][self::NO_LIMIT_CAMPAIGN_IDS];
            })->flatten()->unique()->values()->all();

            $noLimitCompanyIds = $counties->map(function ($county) {
                return $county[self::SUMMARY][self::NO_LIMIT_COMPANY_IDS];
            })->flatten()->unique()->values()->all();

            return [
                self::SUMMARY  => [
                    self::AVAILABLE_BUDGET_COMPANY_IDS   => $companyIdsAvailableBudget,
                    self::AVAILABLE_BUDGET_CAMPAIGN_IDS  => $camIdsAvailableBudget,
                    self::CAMPAIGN_IDS                   => $camIds,
                    self::COMPANY_IDS                    => $companyIds,
                    self::NO_LIMIT_CAMPAIGN_IDS          => $noLimitCamIds,
                    self::NO_LIMIT_COMPANY_IDS           => $noLimitCompanyIds,
                    self::AVAILABLE_BUDGET_COMPANY_COUNT => count($companyIdsAvailableBudget),
                    self::NO_LIMIT_COMPANY_COUNT         => count($noLimitCompanyIds),
                    self::COMPANY_COUNT                  => count($companyIds),
                ],
                self::COUNTIES => $counties
            ];
        });
    }

    /**
     * @param Collection $data
     * @return Collection
     */
    private function formatCampaignData(Collection $data): Collection
    {
        $data = $this->getDataGroupedToZip($data);
        $data = $this->formatZipLevelCampaignData($data);
        $data = $this->formatCountyLevelCampaignData($data);
        return $this->formatStateLevelCampaignData($data);
    }

    /**
     * @return array
     */
    public function getDateOptions(): array
    {
        $dates = HistoricalAvailableLocationBudget::query()
            ->select(HistoricalAvailableLocationBudget::FIELD_CREATED_AT)
            ->groupBy(HistoricalAvailableLocationBudget::FIELD_CREATED_AT)
            ->orderByDesc(HistoricalAvailableLocationBudget::FIELD_CREATED_AT)
            ->get()
            ->pluck(HistoricalAvailableLocationBudget::FIELD_CREATED_AT);

        $dateOptions = [];

        /** @var Carbon $date */
        foreach ($dates as $date) {
            $dateOptions[] = $date->timestamp;
        }

        return $dateOptions;
    }

    /**
     * @return string|int|float
     */
    private function getMostRecentTimestamp(): mixed
    {
        return HistoricalAvailableLocationBudget::query()
                   ->select(HistoricalAvailableLocationBudget::FIELD_CREATED_AT)
                   ->groupBy(HistoricalAvailableLocationBudget::FIELD_CREATED_AT)
                   ->orderByDesc(HistoricalAvailableLocationBudget::FIELD_CREATED_AT)
                   ->get()
                   ->pluck(HistoricalAvailableLocationBudget::FIELD_CREATED_AT)[0]->timestamp;
    }

    /**
     * @param string $locationTable
     * @param string $locationBudgetTable
     * @param string $industry
     * @param string $date
     * @return Collection
     */
    private function getLocationData(
        string $locationTable,
        string $locationBudgetTable,
        string $industry,
        string $date
    ): Collection
    {
        $data = Location::query()
            ->select([
                $locationTable . '.' . Location::STATE,
                $locationTable . '.' . Location::COUNTY,
                $locationTable . '.' . Location::ZIP_CODE,
                $locationBudgetTable . '.*'
            ])
            ->leftJoin($locationBudgetTable, function (
                $leftJoin
            ) use ($industry, $date, $locationBudgetTable, $locationTable) {
                $leftJoin->on($locationTable . '.' . Location::ID, '=', $locationBudgetTable . '.' . HistoricalAvailableLocationBudget::FIELD_LOCATION_ID)
                    ->where($locationBudgetTable . '.' . HistoricalAvailableLocationBudget::FIELD_INDUSTRY_TYPE, $industry)
                    ->where($locationBudgetTable . '.' . HistoricalAvailableLocationBudget::FIELD_BUDGET_TYPE, HistoricalAvailableLocationBudget::BUDGET_TYPE_VERIFIED)
                    ->where($locationBudgetTable . '.' . HistoricalAvailableLocationBudget::FIELD_CREATED_AT, $date);
            })
            ->where($locationTable . '.' . Location::TYPE, Location::TYPE_ZIP_CODE)
            ->get();

        return $this->formatLocationData($data);
    }

    /**
     * @param string $locationTable
     * @param string $campaignBudgetTable
     * @param string $campaignTable
     * @param string $industry
     * @param string $date
     * @return Collection
     */
    private function getCampaignData(
        string $locationTable,
        string $campaignBudgetTable,
        string $campaignTable,
        string $industry,
        string $date
    ): Collection
    {
        $data = HistoricalAvailableCampaignBudget::query()
            ->select([
                $locationTable . '.' . Location::STATE,
                $locationTable . '.' . Location::COUNTY,
                $locationTable . '.' . Location::ZIP_CODE,
                $campaignBudgetTable . '.' . HistoricalAvailableCampaignBudget::FIELD_UNLIMITED,
                $campaignBudgetTable . '.' . HistoricalAvailableCampaignBudget::FIELD_CAMPAIGN_ID,
                $campaignBudgetTable . '.' . HistoricalAvailableCampaignBudget::FIELD_BUDGET_AVAILABLE_DOLLARS,
                $campaignBudgetTable . '.' . HistoricalAvailableCampaignBudget::FIELD_BUDGET_AVAILABLE_VOLUME,
                $campaignTable . '.' . LeadCampaign::COMPANY_ID
            ])
            ->join($locationTable, $locationTable . '.' . Location::ID, '=', $campaignBudgetTable . '.' . HistoricalAvailableCampaignBudget::FIELD_LOCATION_ID)
            ->join($campaignTable, $campaignTable . '.' . LeadCampaign::ID, '=', $campaignBudgetTable . '.' . HistoricalAvailableCampaignBudget::FIELD_CAMPAIGN_ID)
            ->where($campaignBudgetTable . '.' . HistoricalAvailableCampaignBudget::FIELD_INDUSTRY_TYPE, $industry)
            ->where($campaignBudgetTable . '.' . HistoricalAvailableCampaignBudget::FIELD_BUDGET_TYPE, HistoricalAvailableCampaignBudget::BUDGET_TYPE_VERIFIED)
            ->where($campaignBudgetTable . '.' . HistoricalAvailableCampaignBudget::FIELD_CREATED_AT, $date)
            ->get();

        return $this->formatCampaignData($data);
    }

    /**
     * @param string $industry
     * @param string $primaryTimestamp
     * @param string $comparisonTimestamp
     * @return Collection
     */
    public function getComparisonTimeData(
        string $industry,
        string $primaryTimestamp,
        string $comparisonTimestamp
    ): Collection
    {
        return $this->compareData(
            $this->getSingleTimeData($industry, $primaryTimestamp),
            $this->getSingleTimeData($industry, $comparisonTimestamp)
        );
    }


    /**
     * @param Collection $primaryData
     * @param Collection $comparisonData
     * @return Collection
     */
    private function compareData(Collection $primaryData, Collection $comparisonData): Collection
    {
        $newCompanyIds             = array_diff($primaryData[self::SUMMARY][self::COMPANY_IDS] ?? [], $comparisonData[self::SUMMARY][self::COMPANY_IDS] ?? []);
        $lostCompanyIds            = array_diff($comparisonData[self::SUMMARY][self::COMPANY_IDS] ?? [], $primaryData[self::SUMMARY][self::COMPANY_IDS] ?? []);
        $newAvailBudgetCompanyIds  = array_diff($primaryData[self::SUMMARY][self::AVAILABLE_BUDGET_COMPANY_IDS] ?? [], $comparisonData[self::SUMMARY][self::AVAILABLE_BUDGET_COMPANY_IDS] ?? []);
        $lostAvailBudgetCompanyIds = array_diff($comparisonData[self::SUMMARY][self::AVAILABLE_BUDGET_COMPANY_IDS] ?? [], $primaryData[self::SUMMARY][self::AVAILABLE_BUDGET_COMPANY_IDS] ?? []);
        $newNoLimitCompanyIds      = array_diff($primaryData[self::SUMMARY][self::NO_LIMIT_COMPANY_IDS] ?? [], $comparisonData[self::SUMMARY][self::NO_LIMIT_COMPANY_IDS] ?? []);
        $lostNoLimitCompanyIds     = array_diff($comparisonData[self::SUMMARY][self::NO_LIMIT_COMPANY_IDS] ?? [], $primaryData[self::SUMMARY][self::NO_LIMIT_COMPANY_IDS] ?? []);

        return collect([
            self::SUMMARY => [
                self::NEW_COMPANY_IDS                 => $newCompanyIds,
                self::LOST_COMPANY_IDS                => $lostCompanyIds,
                self::NEW_COMPANY_COUNT               => count($newCompanyIds),
                self::LOST_COMPANY_COUNT              => count($lostCompanyIds),
                self::NEW_AVAIL_BUDGET_COMPANY_IDS    => $newAvailBudgetCompanyIds,
                self::LOST_AVAIL_BUDGET_COMPANY_IDS   => $lostAvailBudgetCompanyIds,
                self::NEW_AVAIL_BUDGET_COMPANY_COUNT  => count($newAvailBudgetCompanyIds),
                self::LOST_AVAIL_BUDGET_COMPANY_COUNT => count($lostAvailBudgetCompanyIds),
                self::NEW_NO_LIMIT_COMPANY_IDS        => $newNoLimitCompanyIds,
                self::LOST_NO_LIMIT_COMPANY_IDS       => $lostNoLimitCompanyIds,
                self::NEW_NO_LIMIT_COMPANY_COUNT      => count($newNoLimitCompanyIds),
                self::LOST_NO_LIMIT_COMPANY_COUNT     => count($lostNoLimitCompanyIds),
            ],
            self::STATES  => $primaryData[self::STATES]->map(function ($state, $stateName) use ($comparisonData) {
                $comparisonStateData = $comparisonData[self::STATES][$stateName];

                $newCompanyIds             = array_diff($state[self::SUMMARY][self::COMPANY_IDS] ?? [], $comparisonStateData[self::SUMMARY][self::COMPANY_IDS] ?? []);
                $lostCompanyIds            = array_diff($comparisonStateData[self::SUMMARY][self::COMPANY_IDS] ?? [], $state[self::SUMMARY][self::COMPANY_IDS] ?? []);
                $newAvailBudgetCompanyIds  = array_diff($state[self::SUMMARY][self::AVAILABLE_BUDGET_COMPANY_IDS] ?? [], $comparisonStateData[self::SUMMARY][self::AVAILABLE_BUDGET_COMPANY_IDS] ?? []);
                $lostAvailBudgetCompanyIds = array_diff($comparisonStateData[self::SUMMARY][self::AVAILABLE_BUDGET_COMPANY_IDS] ?? [], $state[self::SUMMARY][self::AVAILABLE_BUDGET_COMPANY_IDS] ?? []);
                $newNoLimitCompanyIds      = array_diff($state[self::SUMMARY][self::NO_LIMIT_COMPANY_IDS] ?? [], $comparisonStateData[self::SUMMARY][self::NO_LIMIT_COMPANY_IDS] ?? []);
                $lostNoLimitCompanyIds     = array_diff($comparisonStateData[self::SUMMARY][self::NO_LIMIT_COMPANY_IDS] ?? [], $state[self::SUMMARY][self::NO_LIMIT_COMPANY_IDS] ?? []);

                $avgDollarsChange = ($state[self::SUMMARY][self::AVERAGE_DOLLARS_NO_REJECT] ?? 0) - ($comparisonStateData[self::SUMMARY][self::AVERAGE_DOLLARS_NO_REJECT] ?? 0);
                $avgVolumeChange  = ($state[self::SUMMARY][self::AVERAGE_VOLUME_NO_REJECT] ?? 0) - ($comparisonStateData[self::SUMMARY][self::AVERAGE_VOLUME_NO_REJECT] ?? 0);

                return collect([
                    self::SUMMARY  => [
                        self::NEW_COMPANY_IDS                  => $newCompanyIds,
                        self::LOST_COMPANY_IDS                 => $lostCompanyIds,
                        self::NEW_COMPANY_COUNT                => count($newCompanyIds),
                        self::LOST_COMPANY_COUNT               => count($lostCompanyIds),
                        self::NEW_AVAIL_BUDGET_COMPANY_IDS     => $newAvailBudgetCompanyIds,
                        self::LOST_AVAIL_BUDGET_COMPANY_IDS    => $lostAvailBudgetCompanyIds,
                        self::NEW_AVAIL_BUDGET_COMPANY_COUNT   => count($newAvailBudgetCompanyIds),
                        self::LOST_AVAIL_BUDGET_COMPANY_COUNT  => count($lostAvailBudgetCompanyIds),
                        self::NEW_NO_LIMIT_COMPANY_IDS         => $newNoLimitCompanyIds,
                        self::LOST_NO_LIMIT_COMPANY_IDS        => $lostNoLimitCompanyIds,
                        self::NEW_NO_LIMIT_COMPANY_COUNT       => count($newNoLimitCompanyIds),
                        self::LOST_NO_LIMIT_COMPANY_COUNT      => count($lostNoLimitCompanyIds),
                        self::AVERAGE_DOLLARS_NO_REJECT_CHANGE => $avgDollarsChange,
                        self::AVERAGE_VOLUME_NO_REJECT_CHANGE  => $avgVolumeChange,
                        self::NAME                             => $stateName
                    ],
                    self::COUNTIES => $state[self::COUNTIES]->map(function ($county, $countyName) use (
                        $comparisonStateData,
                        $stateName
                    ) {
                        $comparisonCountyData = $comparisonStateData[self::COUNTIES][$countyName];

                        $newCompanyIds             = array_diff($county[self::SUMMARY][self::COMPANY_IDS] ?? [], $comparisonCountyData[self::SUMMARY][self::COMPANY_IDS] ?? []);
                        $lostCompanyIds            = array_diff($comparisonCountyData[self::SUMMARY][self::COMPANY_IDS] ?? [], $county[self::SUMMARY][self::COMPANY_IDS] ?? []);
                        $newAvailBudgetCompanyIds  = array_diff($county[self::SUMMARY][self::AVAILABLE_BUDGET_COMPANY_IDS] ?? [], $comparisonCountyData[self::SUMMARY][self::AVAILABLE_BUDGET_COMPANY_IDS] ?? []);
                        $lostAvailBudgetCompanyIds = array_diff($comparisonCountyData[self::SUMMARY][self::AVAILABLE_BUDGET_COMPANY_IDS] ?? [], $county[self::SUMMARY][self::AVAILABLE_BUDGET_COMPANY_IDS] ?? []);
                        $newNoLimitCompanyIds      = array_diff($county[self::SUMMARY][self::NO_LIMIT_COMPANY_IDS] ?? [], $comparisonCountyData[self::SUMMARY][self::NO_LIMIT_COMPANY_IDS] ?? []);
                        $lostNoLimitCompanyIds     = array_diff($comparisonCountyData[self::SUMMARY][self::NO_LIMIT_COMPANY_IDS] ?? [], $county[self::SUMMARY][self::NO_LIMIT_COMPANY_IDS] ?? []);

                        $avgDollarsChange = ($county[self::SUMMARY][self::AVERAGE_DOLLARS_NO_REJECT] ?? 0) - ($comparisonCountyData[self::SUMMARY][self::AVERAGE_DOLLARS_NO_REJECT] ?? 0);
                        $avgVolumeChange  = ($county[self::SUMMARY][self::AVERAGE_VOLUME_NO_REJECT] ?? 0) - ($comparisonCountyData[self::SUMMARY][self::AVERAGE_VOLUME_NO_REJECT] ?? 0);

                        return collect([
                            self::SUMMARY => [
                                self::NEW_COMPANY_IDS                  => $newCompanyIds,
                                self::LOST_COMPANY_IDS                 => $lostCompanyIds,
                                self::NEW_COMPANY_COUNT                => count($newCompanyIds),
                                self::LOST_COMPANY_COUNT               => count($lostCompanyIds),
                                self::NEW_AVAIL_BUDGET_COMPANY_IDS     => $newAvailBudgetCompanyIds,
                                self::LOST_AVAIL_BUDGET_COMPANY_IDS    => $lostAvailBudgetCompanyIds,
                                self::NEW_AVAIL_BUDGET_COMPANY_COUNT   => count($newAvailBudgetCompanyIds),
                                self::LOST_AVAIL_BUDGET_COMPANY_COUNT  => count($lostAvailBudgetCompanyIds),
                                self::NEW_NO_LIMIT_COMPANY_IDS         => $newNoLimitCompanyIds,
                                self::LOST_NO_LIMIT_COMPANY_IDS        => $lostNoLimitCompanyIds,
                                self::NEW_NO_LIMIT_COMPANY_COUNT       => count($newNoLimitCompanyIds),
                                self::LOST_NO_LIMIT_COMPANY_COUNT      => count($lostNoLimitCompanyIds),
                                self::AVERAGE_DOLLARS_NO_REJECT_CHANGE => $avgDollarsChange,
                                self::AVERAGE_VOLUME_NO_REJECT_CHANGE  => $avgVolumeChange,
                                self::NAME                             => $countyName,
                                self::STATE_NAME                       => $stateName
                            ],
                        ]);
                    })
                ]);
            })
        ]);
    }

    /**
     * @param string $industry
     * @param string|null $timestamp
     * @return Collection
     */
    public function getSingleTimeData(string $industry = 'solar', string $timestamp = null): Collection
    {
        if (!$timestamp) {
            $timestamp = $this->getMostRecentTimestamp();
        }

        $date                = Carbon::createFromTimestamp($timestamp)->toDateTimeString();
        $locationTable       = Location::TABLE;
        $campaignBudgetTable = DatabaseHelperService::database() . '.' . HistoricalAvailableCampaignBudget::TABLE;
        $locationBudgetTable = DatabaseHelperService::database() . '.' . HistoricalAvailableLocationBudget::TABLE;
        $campaignTable       = DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaign::TABLE;

        $locationData = $this->getLocationData($locationTable, $locationBudgetTable, $industry, $date);
        $campaignData = $this->getCampaignData($locationTable, $campaignBudgetTable, $campaignTable, $industry, $date);

        $states = $this->consolidateSingleTimeData($locationData, $campaignData);

        $companyIds = $states->map(function ($state) {
            return $state[self::SUMMARY][self::COMPANY_IDS];
        })->flatten()->unique()->values()->all();

        $camIds = $states->map(function ($state) {
            return $state[self::SUMMARY][self::CAMPAIGN_IDS];
        })->flatten()->unique()->values()->all();

        $noLimitCamIds = $states->map(function ($state) {
            return $state[self::SUMMARY][self::NO_LIMIT_CAMPAIGN_IDS];
        })->flatten()->unique()->values()->all();

        $noLimitCompanyIds = $states->map(function ($state) {
            return $state[self::SUMMARY][self::NO_LIMIT_COMPANY_IDS];
        })->flatten()->unique()->values()->all();

        $camIdsAvailableBudget = $states->map(function ($state) {
            return $state[self::SUMMARY][self::AVAILABLE_BUDGET_CAMPAIGN_IDS];
        })->flatten()->unique()->values()->all();

        $companyIdsAvailableBudget = $states->map(function ($state) {
            return $state[self::SUMMARY][self::AVAILABLE_BUDGET_COMPANY_IDS];
        })->flatten()->unique()->values()->all();

        return collect([
            self::SUMMARY => [
                self::AVAILABLE_BUDGET_COMPANY_IDS   => $companyIdsAvailableBudget,
                self::AVAILABLE_BUDGET_CAMPAIGN_IDS  => $camIdsAvailableBudget,
                self::AVAILABLE_BUDGET_COMPANY_COUNT => count($companyIdsAvailableBudget),
                self::COMPANY_IDS                    => $companyIds,
                self::CAMPAIGN_IDS                   => $camIds,
                self::COMPANY_COUNT                  => count($companyIds),
                self::NO_LIMIT_CAMPAIGN_IDS          => $noLimitCamIds,
                self::NO_LIMIT_COMPANY_IDS           => $noLimitCompanyIds,
                self::NO_LIMIT_COMPANY_COUNT         => count($noLimitCompanyIds),
            ],
            self::STATES  => $states
        ]);
    }

    /**
     * @param Collection $locationData
     * @param Collection $campaignData
     * @return Collection
     */
    private function consolidateSingleTimeData(Collection $locationData, Collection $campaignData): Collection
    {
        return $locationData->map(function ($state, $stateName) use ($campaignData) {

            if (isset($campaignData[$stateName])) {
                $state[self::SUMMARY][self::COMPANY_IDS]                    = $campaignData[$stateName][self::SUMMARY][self::COMPANY_IDS];
                $state[self::SUMMARY][self::CAMPAIGN_IDS]                   = $campaignData[$stateName][self::SUMMARY][self::CAMPAIGN_IDS];
                $state[self::SUMMARY][self::COMPANY_COUNT]                  = $campaignData[$stateName][self::SUMMARY][self::COMPANY_COUNT];
                $state[self::SUMMARY][self::NO_LIMIT_CAMPAIGN_IDS]          = $campaignData[$stateName][self::SUMMARY][self::NO_LIMIT_CAMPAIGN_IDS];
                $state[self::SUMMARY][self::NO_LIMIT_COMPANY_IDS]           = $campaignData[$stateName][self::SUMMARY][self::NO_LIMIT_COMPANY_IDS];
                $state[self::SUMMARY][self::NO_LIMIT_COMPANY_COUNT]         = $campaignData[$stateName][self::SUMMARY][self::NO_LIMIT_COMPANY_COUNT];
                $state[self::SUMMARY][self::AVAILABLE_BUDGET_COMPANY_IDS]   = $campaignData[$stateName][self::SUMMARY][self::AVAILABLE_BUDGET_COMPANY_IDS];
                $state[self::SUMMARY][self::AVAILABLE_BUDGET_CAMPAIGN_IDS]  = $campaignData[$stateName][self::SUMMARY][self::AVAILABLE_BUDGET_CAMPAIGN_IDS];
                $state[self::SUMMARY][self::AVAILABLE_BUDGET_COMPANY_COUNT] = $campaignData[$stateName][self::SUMMARY][self::AVAILABLE_BUDGET_COMPANY_COUNT];
            }

            $counties = $state[self::COUNTIES]->map(function ($county, $countyName) use ($campaignData, $stateName) {

                if (isset($campaignData[$stateName][self::COUNTIES][$countyName])) {
                    $county[self::SUMMARY][self::COMPANY_IDS]                    = $campaignData[$stateName][self::COUNTIES][$countyName][self::SUMMARY][self::COMPANY_IDS];
                    $county[self::SUMMARY][self::CAMPAIGN_IDS]                   = $campaignData[$stateName][self::COUNTIES][$countyName][self::SUMMARY][self::CAMPAIGN_IDS];
                    $county[self::SUMMARY][self::COMPANY_COUNT]                  = $campaignData[$stateName][self::COUNTIES][$countyName][self::SUMMARY][self::COMPANY_COUNT];
                    $county[self::SUMMARY][self::NO_LIMIT_CAMPAIGN_IDS]          = $campaignData[$stateName][self::COUNTIES][$countyName][self::SUMMARY][self::NO_LIMIT_CAMPAIGN_IDS];
                    $county[self::SUMMARY][self::NO_LIMIT_COMPANY_IDS]           = $campaignData[$stateName][self::COUNTIES][$countyName][self::SUMMARY][self::NO_LIMIT_COMPANY_IDS];
                    $county[self::SUMMARY][self::NO_LIMIT_COMPANY_COUNT]         = $campaignData[$stateName][self::COUNTIES][$countyName][self::SUMMARY][self::NO_LIMIT_COMPANY_COUNT];
                    $county[self::SUMMARY][self::AVAILABLE_BUDGET_COMPANY_IDS]   = $campaignData[$stateName][self::COUNTIES][$countyName][self::SUMMARY][self::AVAILABLE_BUDGET_COMPANY_IDS];
                    $county[self::SUMMARY][self::AVAILABLE_BUDGET_CAMPAIGN_IDS]  = $campaignData[$stateName][self::COUNTIES][$countyName][self::SUMMARY][self::AVAILABLE_BUDGET_CAMPAIGN_IDS];
                    $county[self::SUMMARY][self::AVAILABLE_BUDGET_COMPANY_COUNT] = $campaignData[$stateName][self::COUNTIES][$countyName][self::SUMMARY][self::AVAILABLE_BUDGET_COMPANY_COUNT];
                }

                $zips = $county[self::ZIPS]->map(function ($zip) use ($campaignData, $stateName, $countyName) {
                    if (isset($campaignData[$stateName][self::COUNTIES][$countyName][self::ZIPS][$zip[self::ZIP]])) {
                        $zip[self::CAMPAIGN_IDS]                  = $campaignData[$stateName][self::COUNTIES][$countyName][self::ZIPS][$zip[self::ZIP]][self::CAMPAIGN_IDS];
                        $zip[self::COMPANY_IDS]                   = $campaignData[$stateName][self::COUNTIES][$countyName][self::ZIPS][$zip[self::ZIP]][self::COMPANY_IDS];
                        $zip[self::NO_LIMIT_CAMPAIGN_IDS]         = $campaignData[$stateName][self::COUNTIES][$countyName][self::ZIPS][$zip[self::ZIP]][self::NO_LIMIT_CAMPAIGN_IDS];
                        $zip[self::NO_LIMIT_COMPANY_IDS]          = $campaignData[$stateName][self::COUNTIES][$countyName][self::ZIPS][$zip[self::ZIP]][self::NO_LIMIT_COMPANY_IDS];
                        $zip[self::AVAILABLE_BUDGET_COMPANY_IDS]  = $campaignData[$stateName][self::COUNTIES][$countyName][self::ZIPS][$zip[self::ZIP]][self::AVAILABLE_BUDGET_COMPANY_IDS];
                        $zip[self::AVAILABLE_BUDGET_CAMPAIGN_IDS] = $campaignData[$stateName][self::COUNTIES][$countyName][self::ZIPS][$zip[self::ZIP]][self::AVAILABLE_BUDGET_CAMPAIGN_IDS];
                    }
                    return $zip;
                });

                return [
                    self::SUMMARY => $county[self::SUMMARY],
                    self::ZIPS    => $zips
                ];
            });

            return [
                self::SUMMARY  => $state[self::SUMMARY],
                self::COUNTIES => $counties
            ];
        });
    }

    /**
     * @param array $industryToData
     * @param Carbon $time
     * @return void
     */
    public function createLocationData(array $industryToData, Carbon $time): void
    {
        foreach ($industryToData as $industry => $locationIdToData) {
            $columns = [
                HistoricalAvailableLocationBudget::FIELD_LOCATION_ID,
                HistoricalAvailableLocationBudget::FIELD_INDUSTRY_TYPE,
                HistoricalAvailableLocationBudget::FIELD_BUDGET_TYPE,
                HistoricalAvailableLocationBudget::FIELD_AVAILABLE_CAMPAIGN_COUNT,
                HistoricalAvailableLocationBudget::FIELD_UNLIMITED_BUDGET_COUNT,
                HistoricalAvailableLocationBudget::FIELD_BUDGET_AVAILABLE_DOLLARS,
                HistoricalAvailableLocationBudget::FIELD_BUDGET_AVAILABLE_VOLUME,
                HistoricalAvailableLocationBudget::FIELD_BUDGET_AVAILABLE_DOLLARS_OMIT_REJECTION,
                HistoricalAvailableLocationBudget::FIELD_BUDGET_AVAILABLE_VOLUME_OMIT_REJECTION,
                HistoricalAvailableLocationBudget::FIELD_CREATED_AT,
                HistoricalAvailableLocationBudget::FIELD_UPDATED_AT,
            ];
            $columns = collect($columns)->join(',');

            foreach (collect($locationIdToData)->chunk(500) as $data) {
                $values = collect($data)->map(function ($data, $locationId) use ($time, $industry) {

                    $omitRejectionDollars = isset($data[self::OMIT_REJECTION]) ? $data[self::OMIT_REJECTION]->availableDollars : 0;
                    $omitRejectionVolume  = isset($data[self::OMIT_REJECTION]) ? $data[self::OMIT_REJECTION]->availableVolume : 0;

                    return "({$locationId}, '{$industry}', {$data[self::STANDARD]->budgetTypeId}, {$data[self::STANDARD]->campaignCount}, {$data[self::STANDARD]->unlimitedCampaignCount}, {$data[self::STANDARD]->availableDollars}, {$data[self::STANDARD]->availableVolume}, {$omitRejectionDollars}, {$omitRejectionVolume}, '$time', '$time')";
                })->join(", ");

                $query = "INSERT INTO " . DatabaseHelperService::database() . '.' . HistoricalAvailableLocationBudget::TABLE . " ({$columns}) VALUES " . $values;

                DB::insert($query);
            }
        }
    }

    /**
     * @param array $industryToData
     * @param Carbon $time
     * @return void
     */
    public function createCampaignData(array $industryToData, Carbon $time): void
    {
        foreach ($industryToData as $industry => $locationIdToData) {
            $columns = [
                HistoricalAvailableCampaignBudget::FIELD_LOCATION_ID,
                HistoricalAvailableCampaignBudget::FIELD_CAMPAIGN_ID,
                HistoricalAvailableCampaignBudget::FIELD_INDUSTRY_TYPE,
                HistoricalAvailableCampaignBudget::FIELD_BUDGET_TYPE,
                HistoricalAvailableCampaignBudget::FIELD_UNLIMITED,
                HistoricalAvailableCampaignBudget::FIELD_BUDGET_AVAILABLE_DOLLARS,
                HistoricalAvailableCampaignBudget::FIELD_BUDGET_AVAILABLE_VOLUME,
                HistoricalAvailableCampaignBudget::FIELD_BUDGET_AVAILABLE_DOLLARS_OMIT_REJECTION,
                HistoricalAvailableCampaignBudget::FIELD_BUDGET_AVAILABLE_VOLUME_OMIT_REJECTION,
                HistoricalAvailableCampaignBudget::FIELD_CREATED_AT,
                HistoricalAvailableCampaignBudget::FIELD_UPDATED_AT,
            ];

            $columns = collect($columns)->join(',');

            foreach (collect($locationIdToData)->chunk(100) as $data) {
                $values = collect($data)->map(function ($data, $locationId) use ($time, $industry) {
                    $omitRejectionCampaigns = null;

                    if (isset($data[self::OMIT_REJECTION])) {
                        $omitRejectionCampaigns = $data[self::OMIT_REJECTION]->campaigns->map(function ($campaign) {
                            return collect([
                                HistoricalAvailableCampaignBudget::FIELD_CAMPAIGN_ID                             => $campaign->campaignId,
                                HistoricalAvailableCampaignBudget::FIELD_UNLIMITED                               => $campaign->campaignType === CampaignValueModel::TYPE_UNLIMITED,
                                HistoricalAvailableCampaignBudget::FIELD_BUDGET_AVAILABLE_DOLLARS_OMIT_REJECTION => $campaign->campaignType === CampaignValueModel::TYPE_DOLLAR ? $campaign->value : 0,
                                HistoricalAvailableCampaignBudget::FIELD_BUDGET_AVAILABLE_VOLUME_OMIT_REJECTION  => $campaign->campaignType === CampaignValueModel::TYPE_VOLUME ? $campaign->value : 0
                            ]);
                        });
                    }

                    return $data[self::STANDARD]->campaigns->map(function ($campaign) use (
                        $omitRejectionCampaigns,
                        $locationId,
                        $industry,
                        $data,
                        $time
                    ) {

                        return $this->getCampaignQueryLine($campaign, $omitRejectionCampaigns, $locationId, $industry, $data, $time);

                    })->join(", ");
                })->join(", ");

                $query = "INSERT INTO " . DatabaseHelperService::database() . '.' . HistoricalAvailableCampaignBudget::TABLE . " ({$columns}) VALUES " . $values;

                DB::insert($query);
            }
        }
    }

    /**
     * @param $campaign
     * @param $omitRejectionCampaigns
     * @param $locationId
     * @param $industry
     * @param $data
     * @param $time
     * @return string
     */
    private function getCampaignQueryLine(
        $campaign,
        $omitRejectionCampaigns,
        $locationId,
        $industry,
        $data,
        $time
    ): string
    {
        $omitRejectionDollars = 0;
        $omitRejectionVolume  = 0;

        if ($omitRejectionCampaigns) {
            $omitRejectionCampaign = $omitRejectionCampaigns->where(HistoricalAvailableCampaignBudget::FIELD_CAMPAIGN_ID, $campaign->campaignId)->first();

            $omitRejectionDollars = $omitRejectionCampaign ? $omitRejectionCampaign[HistoricalAvailableCampaignBudget::FIELD_BUDGET_AVAILABLE_DOLLARS_OMIT_REJECTION] : 0;
            $omitRejectionVolume  = $omitRejectionCampaign ? $omitRejectionCampaign[HistoricalAvailableCampaignBudget::FIELD_BUDGET_AVAILABLE_VOLUME_OMIT_REJECTION] : 0;
        }

        $unlimited        = $campaign->campaignType === CampaignValueModel::TYPE_UNLIMITED ? 1 : 0;
        $availableVolume  = $campaign->campaignType === CampaignValueModel::TYPE_VOLUME ? $campaign->value : 0;
        $availableDollars = $campaign->campaignType === CampaignValueModel::TYPE_VOLUME ? $campaign->value : 0;


        return "({$locationId}, {$campaign->campaignId}, '{$industry}', {$data[self::STANDARD]->budgetTypeId}, {$unlimited}, {$availableDollars}, {$availableVolume}, {$omitRejectionDollars}, {$omitRejectionVolume}, '$time', '$time')";
    }
}
