<?php

namespace App\Repositories;

use App\Abstracts\Workflows\Task;
use App\Models\CompletedWorkflow;
use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Legacy\EloquentQuote;
use App\Models\Odin\Company;
use App\Models\RunningWorkflow;
use App\Repositories\Legacy\QuoteRepository;
use App\Repositories\Odin\ConsumerProductRepository;
use Illuminate\Support\Collection;
use JetBrains\PhpStorm\ArrayShape;

class IdentificationRepository
{
    const IDENTIFY_AS_LEAD    = 'identifyCallerAsLead';
    const IDENTIFY_AS_CONTACT = 'identifyCallerAsCompanyContact';
    const IDENTIFY_AS_USER    = 'identifyCallerAsCompanyUser';

    const IDENTIFICATION_METHODS             = [self::IDENTIFY_AS_LEAD, self::IDENTIFY_AS_CONTACT, self::IDENTIFY_AS_USER];
    const PRIORITIZED_IDENTIFICATION_METHODS = [self::IDENTIFY_AS_USER, self::IDENTIFY_AS_CONTACT, self::IDENTIFY_AS_LEAD];

    public function __construct(
        protected QuoteRepository          $leadRepository,
        protected CompanyContactRepository $contactRepository,
        protected ConsumerProductRepository $consumerProductRepository,
    ) {}

    /**
     * Attempts to identify a phone number
     *
     * @param string $phoneNumber
     * @param bool $byPriority Whether to use the latest results, or the internal company priority.
     * @return array
     */
    #[ArrayShape(["name" => "string", "relation_id" => "null", "relation_type" => "null"])]
    public function identifyCaller(string $phoneNumber, bool $byPriority = true): array
    {
        $result = $byPriority ? $this->findByPriority($phoneNumber) : $this->findByLatest($phoneNumber);

        return $result ?? [
            "name"          => $phoneNumber,
            "relation_id"   => null,
            "relation_type" => null
        ];
    }

    /**
     * Finds the latest result matching a phone number.
     *
     * @param string $phoneNumber
     * @return array|null
     */
    protected function findByLatest(string $phoneNumber): ?array
    {
        return collect(self::IDENTIFICATION_METHODS)->map(fn($method) => $this->{$method}($phoneNumber))->filter()->sortByDesc("timestamp")->first();
    }

    /**
     * Using the priority order in the constant defined above, return the first non-null result.
     *
     * @see self::PRIORITIZED_IDENTIFICATION_METHODS
     * @param string $phoneNumber
     * @return array|null
     */
    protected function findByPriority(string $phoneNumber): ?array
    {
        return collect(self::PRIORITIZED_IDENTIFICATION_METHODS)->map(fn($method) => $this->{$method}($phoneNumber))->filter()->first();
    }

    /**
     * Looks-up a phone number by lead
     *
     * @param string $phoneNumber
     * @return array|null
     */
    protected function identifyCallerAsLead(string $phoneNumber): ?array
    {
        $consumerProduct = $this->consumerProductRepository->getConsumerProductFromPhoneNumber($phoneNumber);

        return $consumerProduct
            ? [
                "name"          => $consumerProduct->consumer->first_name . ' ' . $consumerProduct->consumer->last_name,
                "relation_id"   => $consumerProduct->consumer_id,
                "relation_type" => $consumerProduct->testProduct ? "test-lead" : "lead",
                "timestamp"     => $consumerProduct->created_at->timestamp
            ]
            : null;
    }

    /**
     * Looks-up a phone number by a company contact
     *
     * @param string $phoneNumber
     * @return array|null
     */
    protected function identifyCallerAsCompanyContact(string $phoneNumber): ?array
    {
        $contact = $this->contactRepository->findContactByPhoneNumber($phoneNumber);
        $company = null;

        if ($contact)
            /** @var Company|null $company */
            $company = Company::query()->where(Company::FIELD_LEGACY_ID, $contact->companyid)->first();

        return $contact ?
            [
                "name"          => $contact->firstname . ' ' . $contact->lastname . "(" . $contact->company?->companyname . ")",
                "relation_id"   => $company ? $company->id : $contact->companyid,
                "relation_type" => $company ? "company" : "legacy-company",
                "timestamp"     => $contact->timestampadded
            ] :
            null;
    }

    /**
     * Looks-up a phone number by a company user
     *
     * @param string $phoneNumber
     * @return array|null
     */
    protected function identifyCallerAsCompanyUser(string $phoneNumber): ?array
    {
        $user = $this->contactRepository->findUserByPhoneNumber($phoneNumber);
        $company = null;

        if ($user)
            /** @var Company|null $company */
            $company = Company::query()->where(Company::FIELD_LEGACY_ID, $user->companyid)->first();

        return $user ?
            [
                "name"          => $user->firstname . ' ' . $user->lastname . "(" . $user->company?->companyname . ")",
                "relation_id"   => $company ? $company->id : $user->companyid,
                "relation_type" => $company ? "company" : "legacy-company",
                "timestamp"     => $user->timestampadded
            ] :
            null;
    }
}
