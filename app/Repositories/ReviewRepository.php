<?php

namespace App\Repositories;

use App\DTO\ConsumerReviews\CreateReviewParam;
use App\Enums\Reviews\ReviewQuestion;
use App\Enums\Reviews\ReviewQuestionDataType;
use App\Models\ConsumerReviews\Review;
use App\Models\ConsumerReviews\ReviewData;
use App\Models\ConsumerReviews\Reviewer;
use App\Models\Odin\Company;
use App\Models\Odin\Website;
use Illuminate\Database\Eloquent\Collection;
use App\Models\Odin\Consumer;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\Query\JoinClause;
use Ramsey\Uuid\Uuid;

class ReviewRepository
{

    /**
     * @param Company $company
     * @param CreateReviewParam $createReviewParam
     * @param Website $website
     * @return Review
     */
    public function create(Company $company, CreateReviewParam $createReviewParam, Website $website): Review
    {
        $reviewerModel = $this->createReviewer($createReviewParam);
        $reviewDataModel = $this->createReviewData($createReviewParam);
        return $this->createReviewEntry($company, $reviewerModel, $reviewDataModel, $website, $createReviewParam);
    }

    /**
     * @param CreateReviewParam $createReviewParam
     * @return Reviewer
     */
    public function createReviewer(CreateReviewParam $createReviewParam): Reviewer
    {
        return Reviewer::create([
            Reviewer::FIELD_REFERENCE   => Uuid::uuid4()->toString(),
            Reviewer::FIELD_NAME        => $createReviewParam->getReviewerName(),
            Reviewer::FIELD_EMAIL       => $createReviewParam->getReviewerEmail(),
            Reviewer::FIELD_PHONE       => $createReviewParam->getReviewerPhone(),
        ]);
    }

    /**
     * @param CreateReviewParam $createReviewParam
     * @return ReviewData
     */
    public function createReviewData(CreateReviewParam $createReviewParam): ReviewData
    {
        $payload = [
            ReviewData::DATA_KEY_USER_IP          => $createReviewParam->getReviewerIp(),
            ReviewData::DATA_KEY_ZIP_CODE         => $createReviewParam->getZipCode(),
            ReviewData::DATA_KEY_DISPLAY_LOCATION => $createReviewParam->getDisplayLocation(),
            ReviewData::DATA_KEY_CUSTOM           => $this->makeCustomDataPayload($createReviewParam->getCustomData()),
        ];

        return ReviewData::query()->create([
            ReviewData::FIELD_OVERALL_SCORE => $createReviewParam->getReviewOverallScore(),
            ReviewData::FIELD_TITLE         => $createReviewParam->getTitle(),
            ReviewData::FIELD_COMMENTS      => $createReviewParam->getReviewComments(),
            ReviewData::FIELD_DATA          => $payload,
        ]);
    }

    /**
     * @param ReviewData $reviewData
     * @param array $payload
     * @param bool|null $overwrite
     * @return bool
     */
    public function updateReviewDataPayload(ReviewData $reviewData, array $payload, ?bool $overwrite = false): bool
    {
        $updatePayload = $overwrite
            ? $payload
            : [...$reviewData->data, ...$payload];

        return $reviewData->update([ReviewData::FIELD_DATA => $updatePayload]);
    }

    /**
     * @param Company $company
     * @param Reviewer $reviewerModel
     * @param ReviewData $reviewDataModel
     * @param Website $website
     * @return Review
     */
    public function createReviewEntry(Company $company, Reviewer $reviewerModel, ReviewData $reviewDataModel, Website $website, CreateReviewParam $createReviewParam): Review
    {
        return Review::query()->create([
            Review::FIELD_UUID                => Uuid::uuid4()->toString(),
            Review::FIELD_REVIEWER_ID         => $reviewerModel->id,
            Review::FIELD_COMPANY_ID          => $company->id,
            Review::FIELD_REVIEW_DATA_ID      => $reviewDataModel->id,
            Review::FIELD_WEBSITE_ID          => $website->id,
            Review::FIELD_COMPANY_LOCATION_ID => $createReviewParam->getCompanyLocationId(),
            Review::FIELD_INDUSTRY_ID         => $createReviewParam->getIndustryId(),
            Review::FIELD_INDUSTRY_SERVICE_ID => $createReviewParam->getIndustryServiceId(),
        ]);
    }

    /**
     * @param string $uuid
     * @return Review|null
     * @throws ModelNotFoundException
     */
    public function getReviewByUUID(string $uuid): ?Review
    {
        /** @var Review $review */
        $review =  Review::with([Review::RELATION_REVIEWER, Review::RELATION_REVIEW_DATA])
                ->where(Review::FIELD_UUID, $uuid)
                ->firstOrFail();
        return $review;
    }

    /**
     * @param int $companyId
     * @param string $contactValue
     * @param string $contactMethod
     * @return \Illuminate\Support\Collection<Review>
     */
    public function findReviewsByCompanyAndContactDetails(int $companyId, string $contactValue, string $contactMethod = 'email'): \Illuminate\Support\Collection
    {
        return Review::query()
            ->join(Reviewer::TABLE, fn(JoinClause $join) =>
                $join->on(Reviewer::TABLE .'.'. Reviewer::FIELD_ID, '=', Review::TABLE .'.'. Review::FIELD_REVIEWER_ID)
                    ->when($contactMethod === 'email', fn (JoinClause $join) =>
                        $join->where(Reviewer::FIELD_EMAIL, $contactValue)
                    )->when($contactMethod !== 'email', fn (JoinClause $join) =>
                        $join->where(Reviewer::FIELD_PHONE, preg_replace("/\D/", '', $contactValue))
                )
            )->where(Review::FIELD_COMPANY_ID, $companyId)
            ->get();
    }

    /**
     * @param string $reference
     * @return  Reviewer|null
     * @throws ModelNotFoundException
     */
    public function findReviewerByReference(string $reference): ?Reviewer
    {
        /** @var Reviewer $reviewer */
        $reviewer = Reviewer::query()->where(Reviewer::FIELD_REFERENCE, $reference)->firstOrFail();
        return $reviewer;
    }

    /**
     * @param string $email
     * @return Reviewer|null
     */
    public function findReviewerByEmail(string $email): ?Reviewer
    {
        /** @var Reviewer $reviewer */
        $reviewer = Reviewer::query()->where(Reviewer::FIELD_EMAIL, $email)->firstOrFail();
        return $reviewer;
    }

    /**
     * Sets a reviewer's related consumers to the given collection
     * @param Reviewer $reviewer
     * @param Collection $consumers
     * @return bool
     */
    public function associateReviewerWithConsumers(Reviewer $reviewer, Collection $consumers): bool
    {
        return $this->updateReviewerDataKey($reviewer, Reviewer::FIELD_DATA_CONSUMERS, $consumers->pluck(Consumer::FIELD_ID));
    }

    /**
     * @param Reviewer $reviewer
     * @param string $key
     * @param mixed $value
     * @return bool
     */
    public function updateReviewerDataKey(Reviewer $reviewer, string $key, mixed $value): bool
    {
        $data = $reviewer->{Reviewer::FIELD_DATA};
        $data[$key] = $value;
        $reviewer->{Reviewer::FIELD_DATA} = $data;
        return $reviewer->save();
    }

    /**
     * @param array $customData
     * @return array
     */
    public function makeCustomDataPayload(array $customData): array
    {
        $payload = [];
        foreach ($customData as $customDataKey => $customDataValue) {
            $question = ReviewQuestion::tryFrom($customDataKey);
            $dataType = $question?->getDataType() ?? ReviewQuestionDataType::STRING;
            $key = ReviewQuestion::getStorageKeyMapping($customDataKey);
            $payload[$key] = [
                ReviewData::DATA_KEY_TYPE  => $question->getStorageTypeMapping() ?? ReviewQuestionDataType::STRING,
                ReviewData::DATA_KEY_VALUE => $this->processAnswerValue($dataType, $customDataValue),
            ];
        }

        return $payload;
    }

    /**
     * @param ReviewQuestionDataType $type
     * @param mixed $value
     * @return mixed
     */
    protected function processAnswerValue(ReviewQuestionDataType $type, mixed $value): mixed
    {
        if ($value === null || $value === '')
            return null;

        return match ($type) {
            ReviewQuestionDataType::BOOL              => $this->handleBoolean($value),
            ReviewQuestionDataType::COMPANY_ID,
            ReviewQuestionDataType::INT               => $this->handleInteger($value),
            ReviewQuestionDataType::FLOAT             => $this->handleFloat($value),
            ReviewQuestionDataType::RATING            => $this->handleRating($value),
            ReviewQuestionDataType::STRING            => $this->handleString($value),
            ReviewQuestionDataType::COMPANY_REFERENCE => $this->handleCompanyReference($value),
        };
    }

    /**
     * @param int|string $value
     * @return int
     */
    protected function handleInteger(int|string $value): int
    {
        if (gettype($value) === 'string')
            $value = preg_replace("/\D/", '', $value);

        return (int) $value;
    }

    /**
     * @param int|bool $value
     * @return bool
     */
    protected function handleBoolean(int|bool|string $value): bool
    {
        if (gettype($value) === 'string')
            $value = preg_match("/(yes|true|1)/i", $value);

        return !!$value;
    }

    /**
     * @param float|string|int $value
     * @return float
     */
    protected function handleFloat(float|string|int $value): float
    {
        if (gettype($value) === 'string')
            $value = preg_replace("/[^\d.]/", '', $value);

        return (float) $value;
    }

    /**
     * @param mixed $value
     * @return string
     */
    protected function handleString(mixed $value): string
    {
        return strval($value);
    }

    /**
     * @param mixed $value
     * @return int
     */
    protected function handleRating(mixed $value): int
    {
        $int = $this->handleInteger($value);

        return max(0, min($int, 5));
    }

    /**
     * Transform refs to primary keys for storage
     * @param string $value
     * @return int|null
     */
    protected function handleCompanyReference(string $value): ?int
    {
        return Company::query()
            ->where(Company::FIELD_REFERENCE, $value)
            ->first()
            ?->id;
    }
}
