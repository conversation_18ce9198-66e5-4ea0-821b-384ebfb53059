<?php

namespace App\Repositories\ConsumerProductAffiliates;

use App\Builders\Affiliates\ConsumerProductAffiliatesBuilder;
use App\Models\Odin\ConsumerProduct;
use Illuminate\Database\Eloquent\Builder;

class ConsumerProductAffiliatesRepository
{
    public function getConsumerProductAffiliateQuery(
        ?int $affiliateId = null,
        ?int $campaignId = null,
        ?int $timestampFrom = null,
        ?int $timestampTo = null,
        ?string $consumerName = null,
    ): Builder
    {
        return ConsumerProductAffiliatesBuilder::query()
            ->forAffiliateId($affiliateId)
            ->forCampaignId($campaignId)
            ->betweenTimestamps($timestampFrom, $timestampTo)
            ->forConsumerName($consumerName)
            ->getQuery()
            ->orderByDesc(ConsumerProduct::FIELD_ID);
    }

}
