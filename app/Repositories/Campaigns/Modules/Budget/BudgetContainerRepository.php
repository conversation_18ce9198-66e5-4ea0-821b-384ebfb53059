<?php

namespace App\Repositories\Campaigns\Modules\Budget;

use App\Enums\Campaigns\Modules\Budget\ContainerType;
use App\Models\Campaigns\Modules\BaseCompanyCampaignModule;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;

class BudgetContainerRepository
{

    /**
     * @param int $campaignId
     * @param ContainerType $containerType
     * @param array $budgetSchemas
     * @return BudgetContainer
     */
    public function firstOrInitializeByCampaignId(int $campaignId, ContainerType $containerType, array $budgetSchemas): BudgetContainer
    {
        /** @var BudgetContainer $container */
        $container = BudgetContainer::firstOrCreate([
            BaseCompanyCampaignModule::FIELD_CAMPAIGN_ID => $campaignId,
            BudgetContainer::FIELD_TYPE                  => $containerType,
        ]);

        foreach ($budgetSchemas as $schema) {
            if ($schema[Budget::FIELD_KEY] !== null) {
                $container->budgets()->firstOrCreate(
                    [Budget::FIELD_KEY => $schema[Budget::FIELD_KEY]],
                    $schema
                );
            }
        }

        return $container->load([BudgetContainer::RELATION_BUDGETS]);
    }
}
