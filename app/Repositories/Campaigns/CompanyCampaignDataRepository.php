<?php

namespace App\Repositories\Campaigns;

use App\DTO\CompanyCampaignData\CompanyCampaignLeadAggregatesDTO;
use App\Models\CompanyCampaignData;
use Illuminate\Support\Carbon;
use Illuminate\Support\Carbon as SupportCarbon;

class CompanyCampaignDataRepository
{
    /**
     * @param int $companyCampaignId
     * @return CompanyCampaignData
     */
    public function firstOrNew(int $companyCampaignId): CompanyCampaignData
    {
        /** @var CompanyCampaignData */
        return CompanyCampaignData::query()->firstOrNew([CompanyCampaignData::FIELD_CAMPAIGN_ID => $companyCampaignId]);
    }

    /**
     * @param CompanyCampaignData $campaignData
     * @param Carbon $leadLastSoldAt
     * @param float|null $averageCost
     * @param Carbon|null $leadCostLastCalculatedAt
     * @param int|null $leadCount
     * @return bool
     */
    public function saveData(
        CompanyCampaignData $campaignData,
        ?Carbon             $leadLastSoldAt = null,
        ?float              $averageCost = null,
        ?Carbon             $leadCostLastCalculatedAt = null,
        ?int                $leadCount = null,
    ): bool
    {
        $payload = [
            CompanyCampaignLeadAggregatesDTO::AVERAGE_LEAD_COST                    =>
                empty($averageCost)
                    ? $campaignData->payload[CompanyCampaignLeadAggregatesDTO::AVERAGE_LEAD_COST] ?? null
                    : $averageCost,
            CompanyCampaignLeadAggregatesDTO::AVERAGE_LEAD_COST_LAST_CALCULATED_AT =>
                empty($leadCostLastCalculatedAt)
                    ? $campaignData->payload[CompanyCampaignLeadAggregatesDTO::AVERAGE_LEAD_COST_LAST_CALCULATED_AT] ?? null
                    : $leadCostLastCalculatedAt,
            CompanyCampaignLeadAggregatesDTO::AVERAGE_LEAD_COST_LEAD_COUNT         =>
                empty($leadCount)
                    ? $campaignData->payload[CompanyCampaignLeadAggregatesDTO::AVERAGE_LEAD_COST_LEAD_COUNT] ?? null
                    : $leadCount,
            CompanyCampaignLeadAggregatesDTO::LEAD_LAST_SOLD_AT                    =>
                empty($leadLastSoldAt)
                    ? $campaignData->payload[CompanyCampaignLeadAggregatesDTO::LEAD_LAST_SOLD_AT] ?? null
                    : $leadLastSoldAt,
        ];

        $campaignData->payload = $payload;

        return $campaignData->save();
    }

}