<?php

namespace App\Repositories\Workflows;

use App\Models\Workflow;
use App\Models\WorkflowAction;
use Illuminate\Support\Collection;

class WorkflowRepository
{
    /**
     * Returns the action tree for a given action.
     *
     * @param Workflow $workflow
     * @return WorkflowAction[]
     */
    public function getActionTree(Workflow $workflow): array
    {
        $actions = $this->traverseAction($workflow->entry_action);

        return $actions;
    }

    /**
     * Traverses an action and returns the siblings/children.
     *
     * @param WorkflowAction $action
     * @return WorkflowAction[]
     */
    protected function traverseAction(?WorkflowAction $action): array
    {
        if($action == null)
            return [];

        $actions  = [["action" => $action, "children" => $this->getActionChildren($action)]];
        $siblings = $action->siblings;

        while (count($siblings) > 0) {
            $sibling   = $siblings[0];
            $actions[] = ["action" => $sibling, "children" => $this->getActionChildren($sibling)];
            $siblings->push(...$sibling->siblings);
            $siblings->shift();
        }

        return $actions;
    }

    /**
     * Returns the children for a given action.
     *
     * @param WorkflowAction $action
     * @return array
     */
    protected function getActionChildren(WorkflowAction $action): array
    {
        return $action->children->map(fn(WorkflowAction $action) => $this->traverseAction($action))
                                ->flatten(1)
                                ->toArray();
    }

    /**
     * Returns a list of workflows for a given event.
     *
     * @param int $eventId
     * @return Collection
     */
    public function getWorkflowsForEvent(int $eventId): Collection
    {
        return Workflow::query()->where(Workflow::FIELD_WORKFLOW_EVENT_ID, $eventId)->get();
    }

    /**
     * Returns a list of the generic workflows.
     *
     * @return Collection
     */
    public function getGenericWorkflows(): Collection
    {
        return Workflow::query()->where(Workflow::FIELD_GENERIC, true)
                                ->where(Workflow::FIELD_WORKFLOW_EVENT_ID, null)
                                ->get();
    }
}
