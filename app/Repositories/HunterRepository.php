<?php

namespace App\Repositories;

use App\Models\Hunter;
use App\Models\HunterIndustry;
use App\Models\HunterState;

class HunterRepository
{
    /**
     * @param int $userId
     * @param bool $includedInRoundRobin
     *
     * @return Hunter
     */
    public function createHunter(int $userId, bool $includedInRoundRobin): Hunter
    {
        /** @var Hunter $hunter */
        $hunter = Hunter::query()->create([
            Hunter::FIELD_USER_ID                 => $userId,
            Hunter::FIELD_INCLUDED_IN_ROUND_ROBIN => $includedInRoundRobin
        ]);

        return $hunter;
    }

    /**
     * @param Hunter $hunter
     * @param bool $includedInRoundRobin
     *
     * @return bool
     */
    public function updateHunter(Hunter $hunter, bool $includedInRoundRobin): bool
    {
        $hunter->include_in_round_robin = $includedInRoundRobin;

        return $hunter->save();
    }

    /**
     * @param Hunter $hunter
     * @param string[] $states
     *
     * @return void
     */
    public function saveStates(Hunter $hunter, array $states): void
    {
        $hunter->states()->saveMany(collect($states)->map(fn($state) => new HunterState([HunterState::STATE_ABBR => $state])));
    }

    /**
     * @param Hunter $hunter
     * @param string[] $industries
     *
     * @return void
     */
    public function saveIndustries(Hunter $hunter, array $industries): void
    {
        $hunter->industries()->saveMany(collect($industries)->map(fn($industry) => new HunterIndustry([HunterIndustry::INDUSTRY => $industry])));
    }
}
