<?php

namespace App\Repositories\Reports;

use App\Enums\Advertising\AdvertisingPlatform;
use App\Enums\Campaigns\CampaignStatus;
use App\Enums\Campaigns\Modules\Budget\BudgetType;
use App\Enums\Company\CompanyCampaignStatus;
use App\Enums\CompanyConsolidatedStatus;
use App\Enums\Odin\BudgetCategory;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Operator;
use App\Enums\Reports\CountyCoverageReport\CountyCoverageReportColumnEnum;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModule;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\EstimatedRevenuePerLeadByLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyConfiguration;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\ReportData;
use App\Models\TieredAdvertisingCampaign;
use App\Models\TieredAdvertisingCounty;
use App\Models\TieredAdvertisingInstance;
use App\Models\USZipCode;
use App\Services\DatabaseHelperService;
use App\Services\Filterables\Reports\CountyCoverageReport\CountyCoverageReportCampaignBudgetFilterable;
use App\Services\Filterables\Reports\CountyCoverageReport\CountyCoverageReportCampaignStatusFilterable;
use App\Services\Filterables\Reports\CountyCoverageReport\CountyCoverageReportCampaignTargetingFilterable;
use App\Services\Filterables\Reports\CountyCoverageReport\CountyCoverageReportEmptyLocationsFilterable;
use App\Services\Filterables\Reports\CountyCoverageReport\CountyCoverageReportLocationFilterable;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;

class CountyCoverageReportRepository
{
    const string ZIP_COUNT_QUERY           = 'zq';
    const string COUNTY_CAMPAIGNS_QUERY    = 'cc';
    const string ALL_CAMPAIGNS_QUERY       = 'ac';
    const string ZIP_TO_COUNTY_QUERY       = 'ztc';
    const string COUNTY_TO_COUNTY_QUERY    = 'ctc';
    const string CAMPAIGN_ZIP_COUNTS_QUERY = 'caz';
    const string CALCULATED_QUERY          = 'caq';
    const string TIERED_ADS_QUERY          = 'taq';


    const string CAMPAIGN_ID           = 'ca_id';
    const string CAMPAIGN_REFERENCE    = 'ca_ref';
    const string CAMPAIGN_NAME         = 'ca_name';
    const string CAMPAIGN_STATUS       = 'ca_status';
    const string CAMPAIGN_ZIP_TARGETED = 'ca_targeted';
    const string COMPANY_ID            = 'co_id';
    const string COMPANY_NAME          = 'co_name';
    const string COMPANY_STATUS        = 'co_status';
    const string INDUSTRY_ID           = 'i_id';
    const string BUDGET_TYPE           = 'b_type';
    const string BUDGET_VALUE          = 'b_value';

    const string UNLIMITED_ZIP_COUNT = 'count_unlimited';
    const string LIMITED_ZIP_COUNT   = 'count_limited';

    const string CAMPAIGN_ID_ZIPS = 'ca_id_zips';
    const string CAMPAIGN_ZIPS    = 'ca_zips';

    /**
     * @param IndustryEnum $industry
     * @param mixed $filters
     * @param array $columns
     * @param int|null $companyId
     * @param array|null $campaigns
     * @return Builder
     */
    public function getCountyQuery(
        IndustryEnum $industry,
        mixed $filters,
        array $columns,
        ?int $companyId,
        ?array $campaigns,
    ): Builder
    {
        $db = DatabaseHelperService::database();
        $industryFilterModel = $industry->model();
        $campaignStatuses = $filters?->{CountyCoverageReportCampaignStatusFilterable::ID} ?? null;
        $campaignBudgets = $filters?->{CountyCoverageReportCampaignBudgetFilterable::ID} ?? null;
        $campaignTargeting = $filters?->{CountyCoverageReportCampaignTargetingFilterable::ID} ?? null;
        DB::statement('SET SESSION group_concat_max_len = 10000');

        $query = DB::table(Location::TABLE)
            ->select([
                Location::TABLE.'.'.Location::STATE,
                Location::TABLE.'.'.Location::COUNTY
            ])
            ->where(Location::TYPE, Location::TYPE_COUNTY);

        // Estimated revenue per lead join
        $query->leftJoin(EstimatedRevenuePerLeadByLocation::TABLE, fn (JoinClause $join) =>
            $join->on(EstimatedRevenuePerLeadByLocation::TABLE.'.'.EstimatedRevenuePerLeadByLocation::FIELD_LOCATION_ID, '=', Location::TABLE.'.'.Location::ID)
                ->where(EstimatedRevenuePerLeadByLocation::TABLE.'.'.EstimatedRevenuePerLeadByLocation::FIELD_INDUSTRY_ID, $industryFilterModel->{Industry::FIELD_ID})
        )->addSelect(DB::raw(EstimatedRevenuePerLeadByLocation::TABLE.'.'.EstimatedRevenuePerLeadByLocation::FIELD_ESTIMATED_REVENUE.' as '.CountyCoverageReportColumnEnum::ESTIMATED_REVENUE_PER_LEAD->value));


        if (in_array(CountyCoverageReportColumnEnum::TOTAL_ZIP_CODES, $columns))
            $query = $this->addZipCodeCount($query);

        $allCampaigns = $this->getAllCampaignsByZipCodes(
            db: $db,
            industry: $industryFilterModel,
            companyId: $companyId,
            campaigns: $campaigns,
            campaignStatuses: $campaignStatuses,
            campaignBudgets: $campaignBudgets,
            campaignTargeting: $campaignTargeting,
        );

        $zipCodeCounts = DB::table(DB::raw("({$allCampaigns->toSql()}) AS ".self::ALL_CAMPAIGNS_QUERY))
            ->setBindings($allCampaigns->getBindings())
            ->select([
                DB::raw('COUNT(DISTINCT CASE WHEN '.self::BUDGET_TYPE.' = '.BudgetType::NO_LIMIT->value.' THEN '.self::COMPANY_ID.' END) AS '.self::UNLIMITED_ZIP_COUNT),
                DB::raw('COUNT(DISTINCT CASE WHEN '.self::BUDGET_TYPE.' = '.BudgetType::TYPE_DAILY_UNITS->value.' OR '.self::BUDGET_TYPE.' = '.BudgetType::TYPE_DAILY_SPEND->value.' THEN '.self::COMPANY_ID.' END) AS '.self::LIMITED_ZIP_COUNT),
                self::ALL_CAMPAIGNS_QUERY.'.'.Location::STATE_KEY,
                self::ALL_CAMPAIGNS_QUERY.'.'.Location::COUNTY_KEY,
                self::ALL_CAMPAIGNS_QUERY.'.'.Location::ZIP_CODE,
            ])
            ->groupBy(Location::ZIP_CODE);

        $countyZipCounts = DB::table(DB::raw("({$zipCodeCounts->toSql()}) AS ".self::ZIP_COUNT_QUERY))
            ->setBindings($allCampaigns->getBindings())
            ->select([
                DB::raw('COUNT(CASE WHEN '.self::UNLIMITED_ZIP_COUNT.' > 2 THEN 1 END) as '.CountyCoverageReportColumnEnum::THREE_UNLIMITED->value),
                DB::raw('COUNT(CASE WHEN '.self::UNLIMITED_ZIP_COUNT.' = 2 THEN 1 END) as '.CountyCoverageReportColumnEnum::TWO_UNLIMITED->value),
                DB::raw('COUNT(CASE WHEN '.self::UNLIMITED_ZIP_COUNT.' = 1 THEN 1 END) as '.CountyCoverageReportColumnEnum::ONE_UNLIMITED->value),
                DB::raw('COUNT(CASE WHEN '.self::UNLIMITED_ZIP_COUNT.' > 0 THEN 1 END) as '.CountyCoverageReportColumnEnum::SOME_UNLIMITED->value),
                DB::raw('COUNT(CASE WHEN '.self::LIMITED_ZIP_COUNT.' > 2 THEN 1 END) as '.CountyCoverageReportColumnEnum::THREE_LIMITED->value),
                DB::raw('COUNT(CASE WHEN '.self::LIMITED_ZIP_COUNT.' = 2 THEN 1 END) as '.CountyCoverageReportColumnEnum::TWO_LIMITED->value),
                DB::raw('COUNT(CASE WHEN '.self::LIMITED_ZIP_COUNT.' = 1 THEN 1 END) as '.CountyCoverageReportColumnEnum::ONE_LIMITED->value),
                DB::raw('COUNT(CASE WHEN '.self::LIMITED_ZIP_COUNT.' > 0 THEN 1 END) as '.CountyCoverageReportColumnEnum::SOME_LIMITED->value),
                self::ZIP_COUNT_QUERY.'.'.Location::STATE_KEY,
                self::ZIP_COUNT_QUERY.'.'.Location::COUNTY_KEY,
            ])
            ->groupBy(Location::STATE_KEY, Location::COUNTY_KEY);


        $campaignZipCodeCounts = DB::table(DB::raw("({$allCampaigns->toSql()}) AS ".self::ALL_CAMPAIGNS_QUERY))
            ->setBindings($allCampaigns->getBindings())
            ->select([
                Location::STATE_KEY,
                Location::COUNTY_KEY,
                DB::raw(self::CAMPAIGN_ID.' as '.self::CAMPAIGN_ID_ZIPS),
                DB::raw('COUNT('.Location::ZIP_CODE.') as '.self::CAMPAIGN_ZIPS),
            ])
            ->groupBy(self::CAMPAIGN_ID, Location::STATE_KEY, Location::COUNTY_KEY);

        $countyOverallCounts = DB::table(DB::raw("({$allCampaigns->toSql()}) AS ".self::ALL_CAMPAIGNS_QUERY))
            ->setBindings($allCampaigns->getBindings())
            ->select([
                DB::raw('COUNT(DISTINCT '.self::CAMPAIGN_ID.') AS '.CountyCoverageReportColumnEnum::TOTAL_CAMPAIGNS->value),
                DB::raw(CountyCoverageReportColumnEnum::CAMPAIGN_INFO->getQuery().' AS '.CountyCoverageReportColumnEnum::CAMPAIGN_INFO->value),
                self::ALL_CAMPAIGNS_QUERY.'.'.Location::STATE_KEY,
                self::ALL_CAMPAIGNS_QUERY.'.'.Location::COUNTY_KEY,
            ])
            ->joinSub($campaignZipCodeCounts, self::CAMPAIGN_ZIP_COUNTS_QUERY, function ($join) {
                $join->on(self::CAMPAIGN_ZIP_COUNTS_QUERY.'.'.self::CAMPAIGN_ID_ZIPS, '=', self::ALL_CAMPAIGNS_QUERY.'.'.self::CAMPAIGN_ID)
                    ->on(self::CAMPAIGN_ZIP_COUNTS_QUERY.'.'.Location::COUNTY_KEY, '=', self::ALL_CAMPAIGNS_QUERY.'.'.Location::COUNTY_KEY)
                    ->on(self::CAMPAIGN_ZIP_COUNTS_QUERY.'.'.Location::STATE_KEY, '=', self::ALL_CAMPAIGNS_QUERY.'.'.Location::STATE_KEY);
            })
            ->groupBy(Location::STATE_KEY, Location::COUNTY_KEY);

        $query = $query->leftJoinSub($countyZipCounts, self::ZIP_TO_COUNTY_QUERY, function ($join) {
            $join->on(self::ZIP_TO_COUNTY_QUERY.'.'.Location::STATE_KEY, '=', Location::TABLE.'.'.Location::STATE_KEY)
                ->on(self::ZIP_TO_COUNTY_QUERY.'.'.Location::COUNTY_KEY, '=', Location::TABLE.'.'.Location::COUNTY_KEY);
        })
            ->addSelect(CountyCoverageReportColumnEnum::THREE_UNLIMITED->value)
            ->addSelect(CountyCoverageReportColumnEnum::TWO_UNLIMITED->value)
            ->addSelect(CountyCoverageReportColumnEnum::ONE_UNLIMITED->value)
            ->addSelect(CountyCoverageReportColumnEnum::SOME_UNLIMITED->value)
            ->addSelect(CountyCoverageReportColumnEnum::THREE_LIMITED->value)
            ->addSelect(CountyCoverageReportColumnEnum::TWO_LIMITED->value)
            ->addSelect(CountyCoverageReportColumnEnum::ONE_LIMITED->value)
            ->addSelect(CountyCoverageReportColumnEnum::SOME_LIMITED->value);

        $query = $query->leftJoinSub($countyOverallCounts, self::COUNTY_TO_COUNTY_QUERY, function ($join) {
            $join->on(self::COUNTY_TO_COUNTY_QUERY.'.'.Location::STATE_KEY, '=', Location::TABLE.'.'.Location::STATE_KEY)
                ->on(self::COUNTY_TO_COUNTY_QUERY.'.'.Location::COUNTY_KEY, '=', Location::TABLE.'.'.Location::COUNTY_KEY);
        })
            ->addSelect(CountyCoverageReportColumnEnum::TOTAL_CAMPAIGNS->value)
            ->addSelect(CountyCoverageReportColumnEnum::CAMPAIGN_INFO->value);

        $calculatedQuery = ReportData::query()
            ->select([
                ReportData::FIELD_RELATION_ID,
                ReportData::FIELD_DATA,
            ])
            ->where(ReportData::FIELD_REPORT, ReportData::REPORT_COUNTY_COVERAGE)
            ->where(ReportData::FIELD_RELATION_TYPE, ReportData::RELATION_TYPE_COUNTY_LOCATION)
            ->where(ReportData::FIELD_DATA.'->'.CountyCoverageReportColumnEnum::INDUSTRY_ID, $industryFilterModel->{Industry::FIELD_ID});

        $query = $query->leftJoinSub($calculatedQuery, self::CALCULATED_QUERY, function ($query) {
            return $query->on(self::CALCULATED_QUERY.'.'.ReportData::FIELD_RELATION_ID, Location::TABLE.'.'.Location::ID);
        });

        $tieredAdsStatusQuery = TieredAdvertisingCounty::query()
            ->join(TieredAdvertisingCampaign::TABLE, TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_ID, '=', TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_TIERED_ADVERTISING_CAMPAIGN_ID)
            ->leftJoin(TieredAdvertisingInstance::TABLE, TieredAdvertisingInstance::TABLE.'.'.TieredAdvertisingInstance::FIELD_ID, '=', TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_INSTANCE_ID)
            ->select([
                TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_LOCATION_ID,
                DB::raw('GROUP_CONCAT(DISTINCT CASE '.
                    'WHEN '.TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_PLATFORM. ' = \''.AdvertisingPlatform::GOOGLE->value. '\' '.
                        'THEN CASE WHEN '.TieredAdvertisingInstance::TABLE.'.'.TieredAdvertisingInstance::FIELD_ID.' IS NOT NULL '.
                            'THEN CONCAT('.TieredAdvertisingInstance::TABLE.'.'.TieredAdvertisingInstance::FIELD_NAME.', \': \', '.TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_TIER.') '.
                            'ELSE '.TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_TIER.' '.
                        'END '.
                    'END SEPARATOR \'\n\') as '.CountyCoverageReportColumnEnum::GOOGLE_TIERED_ADVERTISING_TIER->value),
                DB::raw('GROUP_CONCAT(DISTINCT CASE '.
                    'WHEN '.TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_PLATFORM. ' = \''.AdvertisingPlatform::META->value. '\' '.
                        'THEN CASE WHEN '.TieredAdvertisingInstance::TABLE.'.'.TieredAdvertisingInstance::FIELD_ID.' IS NOT NULL '.
                            'THEN CONCAT('.TieredAdvertisingInstance::TABLE.'.'.TieredAdvertisingInstance::FIELD_NAME.', \': \', '.TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_TIER.') '.
                            'ELSE '.TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_TIER.' '.
                        'END '.
                    'END SEPARATOR \'\n\') as '.CountyCoverageReportColumnEnum::META_TIERED_ADVERTISING_TIER->value),
                DB::raw('GROUP_CONCAT(DISTINCT CASE '.
                        'WHEN '.TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_PLATFORM. ' = \''.AdvertisingPlatform::MICROSOFT->value. '\' '.
                            'THEN CASE WHEN '.TieredAdvertisingInstance::TABLE.'.'.TieredAdvertisingInstance::FIELD_ID.' IS NOT NULL '.
                            'THEN CONCAT('.TieredAdvertisingInstance::TABLE.'.'.TieredAdvertisingInstance::FIELD_NAME.', \': \', '.TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_TIER.') '.
                            'ELSE '.TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_TIER.' '.
                        'END '.
                    'END SEPARATOR \'\n\') as '.CountyCoverageReportColumnEnum::MICROSOFT_TIERED_ADVERTISING_TIER->value),
                DB::raw('GROUP_CONCAT(DISTINCT CASE '.
                    'WHEN '.TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_PLATFORM. ' = \''.AdvertisingPlatform::GOOGLE->value. '\' '.
                        'THEN CASE WHEN '.TieredAdvertisingInstance::TABLE.'.'.TieredAdvertisingInstance::FIELD_ID.' IS NOT NULL '.
                            'THEN CONCAT('.TieredAdvertisingInstance::TABLE.'.'.TieredAdvertisingInstance::FIELD_NAME.', \': \', '.TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_TCPA_BID.') '.
                            'ELSE '.TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_TCPA_BID.' '.
                        'END '.
                    'END SEPARATOR \'\n\') as '.CountyCoverageReportColumnEnum::GOOGLE_TIERED_ADVERTISING_BID->value),
                DB::raw('GROUP_CONCAT(DISTINCT CASE '.
                    'WHEN '.TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_PLATFORM. ' = \''.AdvertisingPlatform::META->value. '\' '.
                        'THEN CASE WHEN '.TieredAdvertisingInstance::TABLE.'.'.TieredAdvertisingInstance::FIELD_ID.' IS NOT NULL '.
                            'THEN CONCAT('.TieredAdvertisingInstance::TABLE.'.'.TieredAdvertisingInstance::FIELD_NAME.', \': \', '.TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_TCPA_BID.') '.
                            'ELSE '.TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_TCPA_BID.' '.
                        'END '.
                    'END SEPARATOR \'\n\') as '.CountyCoverageReportColumnEnum::META_TIERED_ADVERTISING_BID->value),
                DB::raw('GROUP_CONCAT(DISTINCT CASE '.
                    'WHEN '.TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_PLATFORM. ' = \''.AdvertisingPlatform::MICROSOFT->value. '\' '.
                        'THEN CASE WHEN '.TieredAdvertisingInstance::TABLE.'.'.TieredAdvertisingInstance::FIELD_ID.' IS NOT NULL '.
                            'THEN CONCAT('.TieredAdvertisingInstance::TABLE.'.'.TieredAdvertisingInstance::FIELD_NAME.', \': \', '.TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_TCPA_BID.') '.
                            'ELSE '.TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_TCPA_BID.' '.
                        'END '.
                    'END) as '.CountyCoverageReportColumnEnum::MICROSOFT_TIERED_ADVERTISING_BID->value),
                DB::raw('MAX(CASE '.
                    'WHEN '.TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_PLATFORM. ' = \''.AdvertisingPlatform::GOOGLE->value. '\' '.
                    'THEN '.TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_TCPA_BID.' ELSE 0 END) as '.CountyCoverageReportColumnEnum::TIERED_ADS_CALCULATED_TCPA->value),
            ])
            ->where(TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_INDUSTRY_ID, $industryFilterModel->{Industry::FIELD_ID})
            ->groupBy(TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_LOCATION_ID);

        $query = $query->leftJoinSub($tieredAdsStatusQuery, self::TIERED_ADS_QUERY, function ($query) {
            return $query->on(self::TIERED_ADS_QUERY.'.'.TieredAdvertisingCounty::FIELD_LOCATION_ID, Location::TABLE.'.'.Location::ID);
        })
            ->addSelect(self::TIERED_ADS_QUERY.'.'.CountyCoverageReportColumnEnum::GOOGLE_TIERED_ADVERTISING_TIER->value)
            ->addSelect(self::TIERED_ADS_QUERY.'.'.CountyCoverageReportColumnEnum::GOOGLE_TIERED_ADVERTISING_BID->value)
            ->addSelect(self::TIERED_ADS_QUERY.'.'.CountyCoverageReportColumnEnum::META_TIERED_ADVERTISING_TIER->value)
            ->addSelect(self::TIERED_ADS_QUERY.'.'.CountyCoverageReportColumnEnum::META_TIERED_ADVERTISING_BID->value)
            ->addSelect(self::TIERED_ADS_QUERY.'.'.CountyCoverageReportColumnEnum::MICROSOFT_TIERED_ADVERTISING_TIER->value)
            ->addSelect(self::TIERED_ADS_QUERY.'.'.CountyCoverageReportColumnEnum::MICROSOFT_TIERED_ADVERTISING_BID->value)
            ->addSelect(self::TIERED_ADS_QUERY.'.'.CountyCoverageReportColumnEnum::TIERED_ADS_CALCULATED_TCPA->value);

        foreach ($columns as $column) {
            if ($column->isReportDataColumn()) {
                $query->addSelect(ReportData::jsonDataSelect($column->value));
            }
        }

        if ($filters)
            $query = $this->addFilters($query, $filters);

        return $query;
    }


    /**
     * @param Builder $query
     * @return Builder
     */
    protected function addZipCodeCount(Builder $query): Builder
    {
        $zipCodes = Location::query()
            ->leftJoin(USZipCode::TABLE, fn (JoinClause $join) =>
                $join->on(USZipCode::TABLE.'.'.USZipCode::FIELD_ZIP_CODE, '=', Location::TABLE.'.'.Location::ZIP_CODE)
                    ->where(USZipCode::TABLE.'.'.USZipCode::FIELD_CITY_TYPE, USZipCode::DEFAULT_CITY_TYPE)
            )
            ->select(
                Location::TABLE.'.'.Location::STATE_KEY,
                Location::TABLE.'.'.Location::COUNTY_KEY,
                DB::raw('COUNT(DISTINCT '.Location::TABLE.'.'.Location::ZIP_CODE.') as '.CountyCoverageReportColumnEnum::TOTAL_ZIP_CODES->value),
                DB::raw('SUM('.USZipCode::TABLE.'.'.USZipCode::FIELD_POPULATION.') as '.CountyCoverageReportColumnEnum::POPULATION->value),
            )
            ->where(Location::TYPE, Location::TYPE_ZIP_CODE)
            ->groupBy(Location::STATE_KEY, Location::COUNTY_KEY);

        $query->leftJoinSub($zipCodes, self::ZIP_COUNT_QUERY, function ($join) {
            $join->on(self::ZIP_COUNT_QUERY.'.'.Location::COUNTY_KEY, '=', Location::TABLE.'.'.Location::COUNTY_KEY)
                ->on(self::ZIP_COUNT_QUERY.'.'.Location::STATE_KEY, '=', Location::TABLE.'.'.Location::STATE_KEY);
        })
            ->addSelect(self::ZIP_COUNT_QUERY.'.'.CountyCoverageReportColumnEnum::TOTAL_ZIP_CODES->value)
            ->addSelect(self::ZIP_COUNT_QUERY.'.'.CountyCoverageReportColumnEnum::POPULATION->value);

        return $query;
    }

    /**
     * @param Builder $query
     * @param mixed $filters
     * @return Builder
     */
    protected function addFilters(Builder $query, mixed $filters): Builder
    {
        $states = $filters?->{CountyCoverageReportLocationFilterable::ID}?->{CountyCoverageReportLocationFilterable::ID} ?? null;
        $counties = $filters?->{CountyCoverageReportLocationFilterable::ID}?->{CountyCoverageReportLocationFilterable::CHILD_FILTERABLE_COUNTY_ID} ?? null;
        $emptyLocations =  $filters?->{CountyCoverageReportEmptyLocationsFilterable::ID} ?? null;

        if ($states)
            $query->whereIn(Location::TABLE.'.'.Location::STATE, $states);
        if ($counties)
            $query->whereIn(Location::TABLE.'.'.Location::COUNTY, $counties);
        if ($emptyLocations && in_array(CountyCoverageReportEmptyLocationsFilterable::EXCLUDE_KEY, $emptyLocations))
            $query = $query->where(CountyCoverageReportColumnEnum::TOTAL_CAMPAIGNS->value, '>', 0);

        return $query;
    }

    /**
     * Builds a query for all active/paused campaigns joined to zip codes in their service areas
     * @param string $db
     * @param Industry $industry
     * @param int|null $companyId
     * @param array|null $campaigns
     * @param array|null $campaignStatuses
     * @param mixed $campaignBudgets
     * @param array|null $campaignTargeting
     * @return Builder
     */
    protected function getAllCampaignsByZipCodes(
        string $db,
        Industry $industry,
        ?int $companyId,
        ?array $campaigns,
        ?array $campaignStatuses,
        mixed $campaignBudgets,
        ?array $campaignTargeting,
    ): Builder
    {
        $baseCampaignSelect = [
            CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_ID.' AS '.self::CAMPAIGN_ID,
            CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_REFERENCE.' AS '.self::CAMPAIGN_REFERENCE,
            CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_NAME.' AS '.self::CAMPAIGN_NAME,
            CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_STATUS.' AS '.self::CAMPAIGN_STATUS,
            CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_ZIP_CODE_TARGETED.' AS '.self::CAMPAIGN_ZIP_TARGETED,
            Company::TABLE.'.'.Company::FIELD_ID.' AS '.self::COMPANY_ID,
            Company::TABLE.'.'.Company::FIELD_NAME.' AS '.self::COMPANY_NAME,
            Company::TABLE.'.'.Company::FIELD_CONSOLIDATED_STATUS.' AS '.self::COMPANY_STATUS,
            CompanyConfiguration::TABLE.'.'.CompanyConfiguration::FIELD_UNRESTRICTED_ZIP_CODE_TARGETING,
            Industry::TABLE.'.'.Industry::FIELD_ID.' AS '.self::INDUSTRY_ID,
            Budget::TABLE.'.'.Budget::FIELD_TYPE.' AS '.self::BUDGET_TYPE,
            Budget::TABLE.'.'.Budget::FIELD_VALUE.' AS '.self::BUDGET_VALUE,
            Location::TABLE.'.'.Location::STATE_KEY,
            Location::TABLE.'.'.Location::COUNTY_KEY,
        ];

        $countyCampaigns = DB::table($db.'.'.CompanyCampaign::TABLE)
            ->select([...$baseCampaignSelect])
            ->where(CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_ZIP_CODE_TARGETED, false)
            ->where(CompanyConfiguration::TABLE.'.'.CompanyConfiguration::FIELD_UNRESTRICTED_ZIP_CODE_TARGETING, false)
            ->groupBy(Location::TABLE.'.'.Location::STATE_KEY, Location::TABLE.'.'.Location::COUNTY_KEY, CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_ID);

        $countyCampaigns = $this->addBaseCampaignJoin($countyCampaigns, $db, $industry, $companyId, $campaigns, $campaignStatuses, $campaignBudgets, $campaignTargeting);
        $countyCampaignsToZip = $this->convertCountyCampaignsToZip($countyCampaigns);

        $zipCampaigns = DB::table($db.'.'.CompanyCampaign::TABLE)
            ->select([...$baseCampaignSelect, Location::TABLE.'.'.Location::ZIP_CODE])
            ->where(function ($query) {
                return $query->orWhere(CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_ZIP_CODE_TARGETED, true)
                    ->orWhere(CompanyConfiguration::TABLE.'.'.CompanyConfiguration::FIELD_UNRESTRICTED_ZIP_CODE_TARGETING, true);
            });
        $zipCampaigns = $this->addBaseCampaignJoin($zipCampaigns, $db, $industry, $companyId, $campaigns, $campaignStatuses, $campaignBudgets, $campaignTargeting);

        // This query is all active or temporarily paused campaigns by zip code
        return $countyCampaignsToZip->unionAll($zipCampaigns);
    }

    /**
     * @param Builder $query
     * @param string $db
     * @param Industry $industry
     * @param int|null $companyId
     * @param array|null $campaigns
     * @param array|null $campaignStatuses
     * @param mixed $campaignBudgets
     * @param array|null $campaignTargeting
     * @return Builder
     */
    protected function addBaseCampaignJoin(
        Builder $query,
        string $db,
        Industry $industry,
        ?int $companyId,
        ?array $campaigns,
        ?array $campaignStatuses,
        mixed $campaignBudgets,
        ?array $campaignTargeting,
    ): Builder
    {
        $query = $query
            ->join($db.'.'.Company::TABLE, Company::TABLE.'.'.Company::FIELD_ID, '=', CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_COMPANY_ID)
            ->join($db.'.'.IndustryService::TABLE, IndustryService::TABLE.'.'.IndustryService::FIELD_ID, '=', CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_SERVICE_ID)
            ->join($db.'.'.Industry::TABLE, Industry::TABLE.'.'.Industry::FIELD_ID, '=', IndustryService::TABLE.'.'.IndustryService::FIELD_INDUSTRY_ID)
            ->join($db.'.'.BudgetContainer::TABLE, BudgetContainer::TABLE.'.'.BudgetContainer::FIELD_COMPANY_CAMPAIGN_ID, '=', CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_ID)
            ->join($db.'.'.Budget::TABLE, Budget::TABLE.'.'.Budget::FIELD_BUDGET_CONTAINER_ID, '=', BudgetContainer::TABLE.'.'.BudgetContainer::FIELD_ID)
            ->join($db.'.'.CompanyCampaignLocationModule::TABLE, CompanyCampaignLocationModule::TABLE.'.'.CompanyCampaignLocationModule::FIELD_CAMPAIGN_ID, '=', CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_ID)
            ->join($db.'.'.CompanyCampaignLocationModuleLocation::TABLE, CompanyCampaignLocationModuleLocation::TABLE.'.'.CompanyCampaignLocationModuleLocation::FIELD_MODULE_ID, '=', CompanyCampaignLocationModule::TABLE.'.'.CompanyCampaignLocationModule::FIELD_ID)
            ->join(Location::TABLE, Location::TABLE.'.'.Location::ID, '=', CompanyCampaignLocationModuleLocation::TABLE.'.'.CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID)
            ->join($db.'.'.CompanyConfiguration::TABLE, CompanyConfiguration::TABLE.'.'.CompanyConfiguration::FIELD_COMPANY_ID, '=', Company::TABLE.'.'.Company::FIELD_ID)
            ->whereNull(CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_DELETED_AT)
            ->where(Budget::TABLE.'.'.Budget::FIELD_KEY, BudgetCategory::VERIFIED->value)
            ->where(Industry::TABLE.'.'.Industry::FIELD_ID, $industry->{Industry::FIELD_ID})
            ->where(Budget::TABLE.'.'.Budget::FIELD_STATUS, Budget::STATUS_ENABLED);

        if ($companyId)
            $query->where(Company::TABLE.'.'.Company::FIELD_ID, $companyId);
        if ($campaigns)
            $query->whereIn(CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_ID, $campaigns);
        if ($campaignStatuses) {
            $query->whereIn(CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_STATUS, $campaignStatuses)
                ->where(function($query) {
                    $query->where(Company::TABLE.'.'.Company::FIELD_CONSOLIDATED_STATUS, CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS->value)
                        ->orWhere(Company::TABLE.'.'.Company::FIELD_CAMPAIGN_STATUS, CompanyCampaignStatus::CAMPAIGNS_PAUSED->value)
                        ->orWhere(Company::TABLE.'.'.Company::FIELD_CAMPAIGN_STATUS, CompanyCampaignStatus::CAMPAIGNS_OFF->value);
                });
        } else {
            $query->where(CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_STATUS, CampaignStatus::ACTIVE->value)
                ->where(Company::TABLE.'.'.Company::FIELD_CONSOLIDATED_STATUS, CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS->value);
        }
        if ($campaignBudgets) {
            $query->where(function($query) use ($campaignBudgets) {
                foreach ($campaignBudgets as $campaignBudget) {
                    if ($campaignBudget->selected) {
                        switch ($campaignBudget->key) {
                            case CountyCoverageReportCampaignBudgetFilterable::KEY_COST:
                                $query->orWhere(function($query) use ($campaignBudget) {
                                    $query->where(Budget::TABLE.'.'.Budget::FIELD_TYPE, BudgetType::TYPE_DAILY_SPEND->value);
                                    if ($campaignBudget->operator === Operator::GREATER_THAN_OR_EQUAL_TO->value && $campaignBudget->value !== null) {
                                        $query->where(Budget::TABLE.'.'.Budget::FIELD_VALUE, '>=', $campaignBudget->value);
                                    } else if ($campaignBudget->value !== null) {
                                        $query->where(Budget::TABLE.'.'.Budget::FIELD_VALUE, '<=', $campaignBudget->value);
                                    }
                                    return $query;
                                });
                                break;
                            case CountyCoverageReportCampaignBudgetFilterable::KEY_VOLUME:
                                $query->orWhere(function($query) use ($campaignBudget) {
                                    $query->where(Budget::TABLE.'.'.Budget::FIELD_TYPE, BudgetType::TYPE_DAILY_UNITS->value);
                                    if ($campaignBudget->operator === Operator::GREATER_THAN_OR_EQUAL_TO->value && $campaignBudget->value !== null) {
                                        $query->where(Budget::TABLE.'.'.Budget::FIELD_VALUE, '>=', $campaignBudget->value);
                                    } else if ($campaignBudget->value !== null) {
                                        $query->where(Budget::TABLE.'.'.Budget::FIELD_VALUE, '<=', $campaignBudget->value);
                                    }
                                    return $query;
                                });
                                break;
                            case CountyCoverageReportCampaignBudgetFilterable::KEY_NO_LIMIT:
                                $query->orWhere(Budget::TABLE.'.'.Budget::FIELD_TYPE, BudgetType::NO_LIMIT->value);
                                break;
                        }
                    }
                }
                return $query;
            });
        }
        if ($campaignTargeting)
            $query->where(function ($query) use ($campaignTargeting) {
                if (in_array(true, $campaignTargeting))
                    $query->orWhere(function ($inner_query) use ($campaignTargeting) {
                        return $inner_query->orWhere(CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_ZIP_CODE_TARGETED, true)
                            ->orWhere(CompanyConfiguration::TABLE.'.'.CompanyConfiguration::FIELD_UNRESTRICTED_ZIP_CODE_TARGETING, true);
                    });
                if (in_array(false, $campaignTargeting))
                    $query->orWhere(function ($inner_query) {
                        return $inner_query->where(CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_ZIP_CODE_TARGETED, false)
                            ->where(CompanyConfiguration::TABLE.'.'.CompanyConfiguration::FIELD_UNRESTRICTED_ZIP_CODE_TARGETING, false);
                    });
                return $query;
            });

        return $query;
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    protected function convertCountyCampaignsToZip(Builder $query): Builder
    {
        return DB::table(Location::TABLE)
            ->select([
                self::COUNTY_CAMPAIGNS_QUERY.'.'.self::CAMPAIGN_ID,
                self::COUNTY_CAMPAIGNS_QUERY.'.'.self::CAMPAIGN_REFERENCE,
                self::COUNTY_CAMPAIGNS_QUERY.'.'.self::CAMPAIGN_NAME,
                self::COUNTY_CAMPAIGNS_QUERY.'.'.self::CAMPAIGN_STATUS,
                self::COUNTY_CAMPAIGNS_QUERY.'.'.self::CAMPAIGN_ZIP_TARGETED,
                self::COUNTY_CAMPAIGNS_QUERY.'.'.self::COMPANY_ID,
                self::COUNTY_CAMPAIGNS_QUERY.'.'.self::COMPANY_NAME,
                self::COUNTY_CAMPAIGNS_QUERY.'.'.self::COMPANY_STATUS,
                self::COUNTY_CAMPAIGNS_QUERY.'.'.CompanyConfiguration::FIELD_UNRESTRICTED_ZIP_CODE_TARGETING,
                self::COUNTY_CAMPAIGNS_QUERY.'.'.self::INDUSTRY_ID,
                self::COUNTY_CAMPAIGNS_QUERY.'.'.self::BUDGET_TYPE,
                self::COUNTY_CAMPAIGNS_QUERY.'.'.self::BUDGET_VALUE,
                Location::TABLE.'.'.Location::STATE_KEY,
                Location::TABLE.'.'.Location::COUNTY_KEY,
                Location::TABLE.'.'.Location::ZIP_CODE,
            ])
            ->joinSub($query, self::COUNTY_CAMPAIGNS_QUERY, function ($join) {
                $join->on(self::COUNTY_CAMPAIGNS_QUERY.'.'.Location::STATE_KEY, '=', Location::TABLE.'.'.Location::STATE_KEY)
                    ->on(self::COUNTY_CAMPAIGNS_QUERY.'.'.Location::COUNTY_KEY, '=', Location::TABLE.'.'.Location::COUNTY_KEY);
            })
            ->where(Location::TABLE.'.'.Location::TYPE, Location::TYPE_ZIP_CODE);
    }
}
