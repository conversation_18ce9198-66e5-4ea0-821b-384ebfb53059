<?php

namespace App\Repositories\SalesBait;

use App\Builders\SalesBaits\AvailablePuntersBuilder;
use App\DataModels\SalesBait\SalesBaitPunter;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\LeadCampaign;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;

class AvailablePuntersRepository
{

    /**
     * @param LeadCampaign $campaign
     * @return Collection<int, SalesBaitPunter>
     * @throws BindingResolutionException
     */
    public function getPuntersThatCanReceiveSMS(LeadCampaign $campaign): Collection
    {
        return AvailablePuntersBuilder::query($campaign)
                                      ->includeUsersIfNoContactsAvailable()
                                      ->hasPhone()
                                      ->get();
    }

    /**
     * @param LeadCampaign $campaign
     * @return Collection<int, SalesBaitPunter>
     * @throws BindingResolutionException
     */
    public function getPuntersThatCanReceiveEmail(LeadCampaign $campaign): Collection
    {
        return AvailablePuntersBuilder::query($campaign)
                                      ->includeUsersIfNoContactsAvailable()
                                      ->hasEmail()
                                      ->get();
    }

    /**
     * @param EloquentCompany $company
     * @return Collection
     * @throws BindingResolutionException
     */
    public function getPuntersThatCanReceiveEmailInCompany(EloquentCompany $company): Collection
    {
        return AvailablePuntersBuilder::query()
            ->forCompany($company)
            ->includeUsersIfNoContactsAvailable()
            ->hasEmail()
            ->get();
    }
}
