<?php

namespace App\Repositories\SalesBait;

use App\Builders\SalesBaits\NonPurchasingLocationsBuilder;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\LeadCampaign;
use App\Models\SalesBaitLead;
use App\Models\SalesBaitRestrictedCompany;
use Illuminate\Support\Collection;

class SalesBaitRepository
{


    /**
     * @param int $salesBaitId
     * @return SalesBaitLead|null
     */
    public function find(int $salesBaitId): ?SalesBaitLead
    {
        return SalesBaitLead::find($salesBaitId);
    }

    /**
     * @param EloquentQuote $lead
     * @param LeadCampaign $campaign
     * @return SalesBaitLead
     */
    public function create(EloquentQuote $lead, LeadCampaign $campaign): SalesBaitLead
    {
        return SalesBaitLead::create([
            SalesBaitLead::FIELD_LEAD_ID       => $lead->quoteid,
            SalesBaitLead::FIELD_COMPANY_ID    => $campaign->company_id,
            SalesBaitLead::FIELD_CAMPAIGN_ID   => $campaign->id
        ]);
    }

    public function createForCompany(EloquentQuote $lead, EloquentCompany $company): SalesBaitLead
    {
        return SalesBaitLead::create([
            SalesBaitLead::FIELD_LEAD_ID       => $lead->quoteid,
            SalesBaitLead::FIELD_COMPANY_ID    => $company->companyid,
            SalesBaitLead::FIELD_CAMPAIGN_ID   => null
        ]);
    }

    /**
     * Returns all the restricted companies.
     *
     * @return Collection<SalesBaitRestrictedCompany>
     */
    public function getRestrictedCompanies(): Collection
    {
        return SalesBaitRestrictedCompany::query()->get();
    }

    /**
     * Handles restricting a company.
     *
     * @param int $companyId
     * @return bool
     */
    public function restrictCompany(int $companyId): bool
    {
        return !!SalesBaitRestrictedCompany::query()->updateOrCreate(
            [SalesBaitRestrictedCompany::FIELD_COMPANY_ID => $companyId],
            [SalesBaitRestrictedCompany::FIELD_COMPANY_ID => $companyId],
        );
    }

    /**
     * Returns companies that have NPL's setup in a leads area for the purposes of sales baiting.
     *
     * @param EloquentQuote $lead
     * @return Collection<EloquentQuote>
     */
    public function getNonPurchasingCompaniesForLead(EloquentQuote $lead): Collection
    {
        $builder = NonPurchasingLocationsBuilder::query();

        return $builder->forLead($lead)
            ->excludeCompaniesThatHaveCampaigns()
            ->excludeRestrictedCompanies()
            ->includeActiveBuyersWithPausedCampaigns(false)
            ->get();
    }
}
