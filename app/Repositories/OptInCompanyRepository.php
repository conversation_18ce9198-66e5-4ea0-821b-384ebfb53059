<?php

namespace App\Repositories;

use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\OptInCompany;
use Illuminate\Support\Collection;

class OptInCompanyRepository
{
    /**
     * @param ConsumerProduct $consumerProduct
     * @param Collection $companyIds
     */
    public function addOptInCompanies(
        ConsumerProduct $consumerProduct,
        Collection $companyIds
    )
    {
        $mapped = $companyIds->map(function ($companyId) use ($consumerProduct) {
            return [
                OptInCompany::FIELD_COMPANY_ID => $companyId,
                OptInCompany::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
                OptInCompany::FIELD_CREATED_AT => now(),
                OptInCompany::FIELD_UPDATED_AT => now(),
            ];
        });

        return OptInCompany::query()->insert($mapped->toArray());
    }

}