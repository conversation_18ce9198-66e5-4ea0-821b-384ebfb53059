<?php

namespace App\Repositories\Billing\InvoiceDispute;

use App\Helpers\CarbonHelper;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceDispute;
use Illuminate\Database\Eloquent\Builder;

class InvoiceDisputeRepository
{
    /**
     * @param int|null $invoiceId
     * @param array|null $status
     * @param int|null $companyId
     * @param string|null $dateFrom
     * @param string|null $dateTo
     * @return Builder
     */
    public function list(
        ?int $invoiceId = null,
        ?array $status = null,
        ?int $companyId = null,
        ?string $dateFrom = null,
        ?string $dateTo = null,
    ): Builder
    {
        return InvoiceDispute::query()
            ->with([
                InvoiceDispute::RELATION_INVOICE . '.' . Invoice::RELATION_COMPANY
            ])
            ->when($invoiceId, function ($query) use ($invoiceId) {
                $query->where(InvoiceDispute::FIELD_INVOICE_ID, $invoiceId);
            })
            ->when(filled($status), function ($query) use ($status) {
                $query->whereIn(InvoiceDispute::FIELD_STATUS, $status);
            })
            ->when($companyId, function (Builder $query) use ($status, $companyId) {
                $query->whereHas(InvoiceDispute::RELATION_INVOICE, function ($query) use ($companyId) {
                    return $query->where(Invoice::FIELD_COMPANY_ID, $companyId);
                });
            })->when($dateFrom, function ($query) use ($dateFrom) {
                $query->where(InvoiceDispute::FIELD_CREATED_AT, '>=', CarbonHelper::parseWithTimezone($dateFrom)->startOfDayUTC());
            })->when($dateTo, function ($query) use ($dateTo) {
                $query->where(InvoiceDispute::FIELD_CREATED_AT, '<=', CarbonHelper::parseWithTimezone($dateTo)->endOfDayUTC());
            })->when(filled($status), function ($query) use ($status) {
                $query->whereIn(InvoiceDispute::FIELD_STATUS, $status);
            });
    }

    /**
     * @param string $externalId
     * @return InvoiceDispute|null
     */
    public function findByExternalId(string $externalId): ?InvoiceDispute
    {
        /** @var ?InvoiceDispute */
        return InvoiceDispute::query()->where(InvoiceDispute::FIELD_EXTERNAL_ID, $externalId);
    }

    /**
     * @param int $invoiceId
     * @param string $externalId
     * @param string $reason
     * @param string $status
     * @param float $amount
     * @param string $transactionChargeId
     * @param string $externalChargeId
     * @param string $currency
     * @param string $source
     * @return InvoiceDispute
     */
    public function updateOrCreate(
        int $invoiceId,
        string $externalId,
        string $transactionChargeId,
        string $externalChargeId,
        string $reason,
        string $status,
        float $amount,
        string $currency,
        string $source,
    ): InvoiceDispute
    {
        $found = InvoiceDispute::query()
            ->where(InvoiceDispute::FIELD_EXTERNAL_ID, $externalId)
            ->where(InvoiceDispute::FIELD_INVOICE_ID, $invoiceId)
            ->first();

        $payload = [
            InvoiceDispute::FIELD_INVOICE_ID            => $invoiceId,
            InvoiceDispute::FIELD_EXTERNAL_ID           => $externalId,
            InvoiceDispute::FIELD_REASON                => $reason,
            InvoiceDispute::FIELD_STATUS                => $status,
            InvoiceDispute::FIELD_AMOUNT                => $amount,
            InvoiceDispute::FIELD_EXTERNAL_CHARGE_ID    => $externalChargeId,
            InvoiceDispute::FIELD_TRANSACTION_CHARGE_ID => $transactionChargeId,
            InvoiceDispute::FIELD_CURRENCY              => $currency,
            InvoiceDispute::FIELD_SOURCE                => $source,
        ];

        if ($found) {
            $found->update($payload);
        } else {
            $found = InvoiceDispute::query()->create($payload);
        }

        return $found;
    }
}
