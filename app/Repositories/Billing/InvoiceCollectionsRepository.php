<?php

namespace App\Repositories\Billing;

use App\Helpers\CarbonHelper;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceCollections;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

class InvoiceCollectionsRepository
{
    /**
     * @param string $uuid
     * @param int $invoiceId
     * @param string|Carbon $sentDate
     * @param string $recoveryStatus
     * @param string $amountCollected
     * @param int $userId
     * @return InvoiceCollections
     */
    public function create(
        string $uuid,
        int $invoiceId,
        string|Carbon $sentDate,
        string $recoveryStatus,
        string $amountCollected,
        int $userId
    ): InvoiceCollections
    {
        /** @var InvoiceCollections */
        return InvoiceCollections::query()
            ->create([
                InvoiceCollections::FIELD_UUID             => $uuid,
                InvoiceCollections::FIELD_INVOICE_ID       => $invoiceId,
                InvoiceCollections::FIELD_SENT_DATE        => $sentDate,
                InvoiceCollections::FIELD_RECOVERY_STATUS  => $recoveryStatus,
                InvoiceCollections::FIELD_AMOUNT_COLLECTED => $amountCollected,
                InvoiceCollections::FIELD_USER_ID          => $userId,
            ]);
    }

    /**
     * @param int $invoiceId
     * @param string $recoveryStatus
     * @param string $amountRecovered
     * @param int $userId
     * @param string|null $recoveryDate
     * @return bool
     */
    public function update(
        int $invoiceId,
        string $recoveryStatus,
        string $amountRecovered,
        int $userId,
        ?string $recoveryDate = null
    ): bool
    {
        return InvoiceCollections::query()
            ->where(InvoiceCollections::FIELD_INVOICE_ID, $invoiceId)
            ->update([
                InvoiceCollections::FIELD_RECOVERY_DATE    => $recoveryDate,
                InvoiceCollections::FIELD_RECOVERY_STATUS  => $recoveryStatus,
                InvoiceCollections::FIELD_AMOUNT_RECOVERED => $amountRecovered,
                InvoiceCollections::FIELD_USER_ID          => $userId,
            ]);
    }

    /**
     * @param int|null $invoiceId
     * @param int|null $companyId
     * @param string|null $dateFrom
     * @param string|null $dateTo
     * @param array|null $status
     * @return Builder
     */
    public function list(
        ?int $invoiceId = null,
        ?int $companyId = null,
        ?string $dateFrom = null,
        ?string $dateTo = null,
        ?array $status = null
    ): Builder
    {
        return InvoiceCollections::query()
            ->with([
                InvoiceCollections::RELATION_INVOICE . '.' . Invoice::RELATION_COMPANY,
                InvoiceCollections::RELATION_INVOICE . '.' . Invoice::RELATION_INVOICE_ITEMS,
                InvoiceCollections::RELATION_USER
            ])
            ->when($invoiceId, function ($query) use ($invoiceId) {
                $query->where(InvoiceCollections::FIELD_INVOICE_ID, $invoiceId);
            })
            ->when(filled($status), function ($query) use ($status) {
                $query->whereIn(InvoiceCollections::FIELD_RECOVERY_STATUS, $status);
            })
            ->when($companyId, function (Builder $query) use ($companyId) {
                $query->whereHas(InvoiceCollections::RELATION_INVOICE, function ($query) use ($companyId) {
                    return $query->where(Invoice::FIELD_COMPANY_ID, $companyId);
                });
            })
            ->when($dateFrom, function ($query) use ($dateFrom) {
                $query->where(InvoiceCollections::FIELD_SENT_DATE, '>=', CarbonHelper::parseWithTimezone($dateFrom)->startOfDayUTC());
            })->when($dateTo, function ($query) use ($dateTo) {
                $query->where(InvoiceCollections::FIELD_SENT_DATE, '<=', CarbonHelper::parseWithTimezone($dateTo)->endOfDayUTC());
            });
    }
}
