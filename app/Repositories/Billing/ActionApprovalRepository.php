<?php

namespace App\Repositories\Billing;

use App\Enums\ApprovableAction\ApprovableActionRelationTypes;
use App\Models\Billing\ActionApproval;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\Invoice;
use App\Models\Odin\Company;
use Illuminate\Database\Eloquent\Builder;

class ActionApprovalRepository
{
    /**
     * @param string $uuid
     * @param int $modelId
     * @param string $modelClass
     * @param string $action
     * @param array $payload
     * @param string $status
     * @param int|null $requestedBy
     * @return ActionApproval
     */
    public function create(
        string $uuid,
        int $modelId,
        string $modelClass,
        string $action,
        array $payload,
        string $status,
        ?int $requestedBy = null,
        ?string $note = null
    ): ActionApproval
    {
        /** @var ActionApproval $invoiceActionRequest */
        $invoiceActionRequest = ActionApproval::query()->create([
            ActionApproval::FIELD_UUID             => $uuid,
            ActionApproval::FIELD_APPROVABLE_ID    => $modelId,
            ActionApproval::FIELD_APPROVABLE_TYPE  => $modelClass,
            ActionApproval::FIELD_REQUESTED_BY     => $requestedBy,
            ActionApproval::FIELD_REQUESTED_ACTION => $action,
            ActionApproval::FIELD_PAYLOAD          => $payload,
            ActionApproval::FIELD_STATUS           => $status,
            ActionApproval::FIELD_IS_PROCESSING    => true,
            ActionApproval::FIELD_NOTE             => $note,
        ]);

        return $invoiceActionRequest;
    }

    /**
     * @param int|null $reviewedBy
     * @param int|null $requestedBy
     * @param string|null $status
     * @param array $relatedTypes
     * @param array $relatedIds
     * @param array $orderBy
     * @return Builder
     */
    public function getListQuery(
        ?int $reviewedBy = null,
        ?int $requestedBy = null,
        ?string $status = null,
        array $relatedTypes = [],
        array $relatedIds = [],
        array $orderBy = [],
        ?int $companyId = null,
        array $requestedActionTypes = [],
    ): Builder
    {
        $orderBy = empty($orderBy) ? [ActionApproval::FIELD_ID . ':' . 'desc'] : $orderBy;

        $query = ActionApproval::query()
            ->when(filled($relatedTypes), function ($query) use ($relatedTypes) {
                $modelClasses = array_map(fn(string $type) => ApprovableActionRelationTypes::tryFrom($type)->getModelClass(), $relatedTypes);

                $query->whereIn(
                    ActionApproval::FIELD_APPROVABLE_TYPE,
                    $modelClasses
                );
            })
            ->when(filled($relatedIds), function ($query) use ($relatedIds) {
                $query->whereIn(
                    ActionApproval::FIELD_APPROVABLE_ID,
                    $relatedIds
                );
            })
            ->when($reviewedBy, function ($query) use ($reviewedBy) {
                $query->whereHas('reviewedBy', function ($query) use ($reviewedBy) {
                    $query->where('id', $reviewedBy);
                });
            })
            ->when($requestedBy, function ($query) use ($requestedBy) {
                $query->whereHas('requestedBy', function ($query) use ($requestedBy) {
                    $query->where('id', $requestedBy);
                });
            })
            ->when($status, function ($query) use ($status) {
                $query->where('status', $status);
            })
            ->when(filled($requestedActionTypes), function ($query) use ($requestedActionTypes) {
                $query->whereIn(ActionApproval::FIELD_REQUESTED_ACTION, $requestedActionTypes);
            });

        if ($companyId) {
            $query->where(function ($query) use ($companyId) {
                $query->whereHasMorph(ActionApproval::RELATION_APPROVABLE, [
                    Invoice::class,
                    BillingProfile::class,
                ], function ($query) use ($companyId) {
                    $query->whereHas('company', function ($query) use ($companyId) {
                        $query->where(Company::FIELD_ID, $companyId);
                    });
                })->orWhereHasMorph(ActionApproval::RELATION_APPROVABLE, [
                    Company::class,
                ], function ($query) use ($companyId) {
                    $query->where(ActionApproval::FIELD_APPROVABLE_ID, $companyId);
                });
            });
        }


        foreach ($orderBy as $item) {
            [$col, $order] = explode(':', $item);

            $query->orderBy($col, $order);
        }

        return $query;
    }


    /**
     * @param int $id
     * @return ActionApproval
     */
    public function getInvoiceActionRequest(
        int $id,
    ): ActionApproval
    {
        /** @var ActionApproval $invoiceActionRequest */
        $invoiceActionRequest = ActionApproval::query()->findOrFail($id);

        return $invoiceActionRequest;
    }
}
