<?php

namespace App\Repositories\Billing;

use App\Helpers\CarbonHelper;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceWriteOff;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

class InvoiceWriteOffsRepository
{
    /**
     * @param string $uuid
     * @param int $invoiceId
     * @param string|Carbon $date
     * @param string $amount
     * @param int $userId
     * @return InvoiceWriteOff
     */
    public function create(
        string $uuid,
        int $invoiceId,
        string|Carbon $date,
        string $amount,
        int $userId
    ): InvoiceWriteOff
    {
        /** @var InvoiceWriteOff */
        return InvoiceWriteOff::query()
            ->create([
                InvoiceWriteOff::FIELD_UUID       => $uuid,
                InvoiceWriteOff::FIELD_INVOICE_ID => $invoiceId,
                InvoiceWriteOff::CREATED_AT       => $date,
                InvoiceWriteOff::FIELD_AMOUNT     => $amount,
                InvoiceWriteOff::FIELD_USER_ID    => $userId,
            ]);
    }

    /**
     * @param int|null $invoiceId
     * @param int|null $companyId
     * @param string|null $dateFrom
     * @param string|null $dateTo
     * @return Builder
     */
    public function list(
        ?int $invoiceId = null,
        ?int $companyId = null,
        ?string $dateFrom = null,
        ?string $dateTo = null,
    ): Builder
    {
        return InvoiceWriteOff::query()
            ->with([
                InvoiceWriteOff::RELATION_INVOICE . '.' . Invoice::RELATION_COMPANY,
                InvoiceWriteOff::RELATION_INVOICE . '.' . Invoice::RELATION_INVOICE_ITEMS,
                InvoiceWriteOff::RELATION_USER
            ])
            ->when($invoiceId, function ($query) use ($invoiceId) {
                $query->where(InvoiceWriteOff::FIELD_INVOICE_ID, $invoiceId);
            })
            ->when($companyId, function (Builder $query) use ($companyId) {
                $query->whereHas(InvoiceWriteOff::RELATION_INVOICE, function ($query) use ($companyId) {
                    return $query->where(Invoice::FIELD_COMPANY_ID, $companyId);
                });
            })
            ->when($dateFrom, function ($query) use ($dateFrom) {
                $query->where(InvoiceWriteOff::FIELD_CREATED_AT, '>=', CarbonHelper::parseWithTimezone($dateFrom)->startOfDayUTC());
            })->when($dateTo, function ($query) use ($dateTo) {
                $query->where(InvoiceWriteOff::FIELD_CREATED_AT, '<=', CarbonHelper::parseWithTimezone($dateTo)->endOfDayUTC());
            });
    }
}
