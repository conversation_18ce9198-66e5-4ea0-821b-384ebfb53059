<?php

namespace App\Repositories\Billing;

use App\Enums\Billing\BillingPolicyActionType;
use App\Enums\Billing\BillingPolicyEventType;
use App\Models\Billing\BillingProfilePolicy;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Sentry\Event;

class BillingProfilePolicyRepository
{
    /**
     * @param string $billingProfileId
     * @param BillingPolicyEventType $eventClass
     * @param BillingPolicyActionType $actionClass
     * @param string $sortOrder
     * @param int|null $id
     * @return BillingProfilePolicy
     * @throws \Exception
     */
    public function createOrUpdate(
        string $billingProfileId,
        BillingPolicyEventType $eventClass,
        BillingPolicyActionType $actionClass,
        string $sortOrder,
        ?int $id = null,
    ): BillingProfilePolicy
    {
        /** @var BillingProfilePolicy $policy */
        $policy = BillingProfilePolicy::query()->updateOrCreate([
            BillingProfilePolicy::FIELD_ID => $id,
        ], [
            BillingProfilePolicy::FIELD_EVENT_CLASS        => $eventClass->getClass(),
            BillingProfilePolicy::FIELD_ACTION_CLASS       => $actionClass->getClass(),
            BillingProfilePolicy::FIELD_SORT_ORDER         => $sortOrder,
            BillingProfilePolicy::FIELD_BILLING_PROFILE_ID => $billingProfileId,
        ]);

        return $policy;
    }

    /**
     * Return a collection of global and constrained policies ordered by global policies first, followed by constrained policies
     * @param string $eventClass
     * @param string|null $billingProfileId
     * @return Collection
     */
    public function getPoliciesForEventClass(
        string $eventClass,
        ?string $billingProfileId = null,
    ): Collection
    {
        /** @var BillingProfilePolicy $policy */

        $specificPolicies = collect();

        if (isset($billingProfileId)) {
            $specificPolicies = BillingProfilePolicy::query()
                ->where(BillingProfilePolicy::FIELD_EVENT_CLASS, $eventClass)
                ->where(BillingProfilePolicy::FIELD_BILLING_PROFILE_ID, $billingProfileId)
                ->orderByDesc(BillingProfilePolicy::FIELD_SORT_ORDER)
                ->get();
        }

        $globalPolicies = BillingProfilePolicy::query()
            ->where(BillingProfilePolicy::FIELD_EVENT_CLASS, $eventClass)
            ->whereNull(BillingProfilePolicy::FIELD_BILLING_PROFILE_ID)
            ->orderByDesc(BillingProfilePolicy::FIELD_SORT_ORDER)
            ->get();

        return $globalPolicies->merge($specificPolicies);
    }


    /**
     * @return Collection
     * @throws \Exception
     */
    public function listGlobalPolicies(): Collection
    {
        $policies = BillingProfilePolicy::query()
            ->whereNull(BillingProfilePolicy::FIELD_BILLING_PROFILE_ID)
            ->orderBy(BillingProfilePolicy::FIELD_SORT_ORDER)
            ->get();

        $result = collect();

        foreach ($policies as $policy) {
            $event = BillingPolicyEventType::fromClass($policy->{BillingProfilePolicy::FIELD_EVENT_CLASS});

            if (!$result->has($event->value)) {
                $result->put($event->value, [
                    'event_slug' => $event->value,
                    'actions'    => collect(),
                ]);
            }

            $result->get($event->value)['actions']->push($policy);
        }


        return $result->values();
    }

    /**
     * @param string $eventClass
     * @param string $actionClass
     * @param int $sortOrder
     * @param array|null $actionData
     * @param int|null $billingProfileId
     * @param int|null $id
     * @return BillingProfilePolicy
     */
    public function saveProfilePolicy(
        string $eventClass,
        string $actionClass,
        int $sortOrder,
        ?int $billingProfileId,
        ?int $id,
        ?array $actionData = [],
    ): BillingProfilePolicy
    {
        /** @var BillingProfilePolicy $billingProfilePolicy */
        $billingProfilePolicy = BillingProfilePolicy::query()->updateOrCreate([
            BillingProfilePolicy::FIELD_ID => $id
        ], [
            BillingProfilePolicy::FIELD_ACTION_DATA        => $actionData,
            BillingProfilePolicy::FIELD_ACTION_CLASS       => $actionClass,
            BillingProfilePolicy::FIELD_EVENT_CLASS        => $eventClass,
            BillingProfilePolicy::FIELD_SORT_ORDER         => $sortOrder,
            BillingProfilePolicy::FIELD_BILLING_PROFILE_ID => $billingProfileId,
        ]);

        return $billingProfilePolicy;
    }
}
