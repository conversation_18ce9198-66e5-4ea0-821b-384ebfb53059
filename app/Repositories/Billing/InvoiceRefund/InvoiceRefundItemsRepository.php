<?php

namespace App\Repositories\Billing\InvoiceRefund;

use App\Models\Billing\InvoiceRefundItem;
use Illuminate\Database\Eloquent\Collection;

class InvoiceRefundItemsRepository
{
    public function getRefundItemsByInvoiceItemId(array $invoiceItemIds): Collection|array
    {
        return InvoiceRefundItem::query()->whereIn(InvoiceRefundItem::FIELD_INVOICE_ITEM_ID, $invoiceItemIds)->get();
    }

    public function createRefundItem(
        int   $invoiceRefundId,
        float $value,
        ?int  $invoiceItemId,
    ): InvoiceRefundItem
    {
        $refundItem = new InvoiceRefundItem([
            InvoiceRefundItem::FIELD_INVOICE_ITEM_ID   => $invoiceItemId,
            InvoiceRefundItem::FIELD_VALUE             => $value,
            InvoiceRefundItem::FIELD_INVOICE_REFUND_ID => $invoiceRefundId,
        ]);

        $refundItem->save();

        return $refundItem;
    }

}
