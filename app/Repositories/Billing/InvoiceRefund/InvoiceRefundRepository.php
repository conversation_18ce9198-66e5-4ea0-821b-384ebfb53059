<?php

namespace App\Repositories\Billing\InvoiceRefund;

use App\Enums\InvoiceRefundStatus;
use App\Models\Billing\InvoiceRefund;
use App\Models\Billing\InvoiceRefundCharge;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

class InvoiceRefundRepository
{
    /**
     * @param int $invoiceId
     * @return Collection|array
     */
    public function getInvoiceRefunds(
        int $invoiceId,
    ): Collection|array
    {
        return InvoiceRefund::query()->where(InvoiceRefund::FIELD_INVOICE_ID, $invoiceId)->get();
    }

    /**
     * @param int $page
     * @param int $perPage
     * @param array|null $refundStatus
     * @param int|null $invoiceId
     * @param int|null $minimumValue
     * @param int|null $maximumValue
     * @return LengthAwarePaginator
     */
    public function listInvoiceRefunds(
        int     $page,
        int     $perPage,
        ?array  $refundStatus = null,
        ?int    $invoiceId = null,
        ?int    $minimumValue = null,
        ?int    $maximumValue = null,
    ): LengthAwarePaginator
    {
        $query = InvoiceRefund::query();

        if (isset($refundStatus)) {
            $query->whereIn(InvoiceRefund::FIELD_STATUS, $refundStatus);
        }

        if (isset($invoiceId)) {
            $query->where(InvoiceRefund::FIELD_INVOICE_ID, $invoiceId);
        }

        if (isset($minimumValue)) {
            $query->where(InvoiceRefund::FIELD_TOTAL, '>=', $minimumValue);
        }

        if (isset($maximumValue)) {
            $query->where(InvoiceRefund::FIELD_TOTAL, '<=', $maximumValue);
        }

        return $query
            ->orderByDesc(InvoiceRefund::FIELD_ID)
            ->paginate($perPage, ['*'], 'page', $page);
    }

    /**
     * @param string $uuid
     * @param int $invoiceId
     * @param float $total
     * @param InvoiceRefundStatus $status
     * @param string|null $reason
     * @return InvoiceRefund
     */
    public function createRefund(
        string              $uuid,
        int                 $invoiceId,
        float               $total,
        InvoiceRefundStatus $status,
        ?string $reason = null,
    ): InvoiceRefund
    {
        $refund = new InvoiceRefund([
            InvoiceRefund::FIELD_UUID       => $uuid,
            InvoiceRefund::FIELD_INVOICE_ID => $invoiceId,
            InvoiceRefund::FIELD_TOTAL      => $total,
            InvoiceRefund::FIELD_STATUS     => $status,
            InvoiceRefund::FIELD_REASON     => $reason ?? '',
        ]);

        $refund->save();

        return $refund;
    }

    /**
     * @param InvoiceRefund $refund
     * @return void
     */
    public function updateInvoiceRefundStatus(InvoiceRefund $refund): void
    {
        $invoiceRefundCharges = $refund->{InvoiceRefund::RELATION_REFUND_CHARGES};

        $chargeStatuses = $invoiceRefundCharges->pluck(InvoiceRefundCharge::FIELD_REQUEST_STATUS);

        $updatedStatus = InvoiceRefundStatus::PENDING;

        if (!$chargeStatuses->contains(InvoiceRefundStatus::PENDING)) {
            $updatedStatus = InvoiceRefundStatus::REQUESTED;

            if (!$chargeStatuses->contains(InvoiceRefundStatus::REQUESTED)) {
                $updatedStatus = InvoiceRefundStatus::REFUNDED;
            }
        }

        if ($refund->status !== $updatedStatus) {
            $refund->update([
                InvoiceRefund::FIELD_STATUS => $updatedStatus
            ]);
        }
    }

}
