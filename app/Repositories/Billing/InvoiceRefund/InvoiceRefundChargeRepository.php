<?php

namespace App\Repositories\Billing\InvoiceRefund;

use App\Enums\InvoiceRefundStatus;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceRefund;
use App\Models\Billing\InvoiceRefundCharge;
use App\Models\Billing\InvoiceTransaction;
use Illuminate\Database\Eloquent\Builder;

class InvoiceRefundChargeRepository
{
    /**
     * @param string $refundedTransactionReference
     * @param string $invoiceUuid
     * @param float $refundAmount
     * @param InvoiceRefundStatus[]|null $invoiceRefundChargeStatus
     * @return ?InvoiceRefundCharge
     */
    public function getInvoiceRefundChargeByAssociatedValues(
        string $refundedTransactionReference,
        string $invoiceUuid,
        float $refundAmount,
        ?array $invoiceRefundChargeStatus = [],
    ): ?InvoiceRefundCharge
    {
        $query = InvoiceRefundCharge::query();

        $query->whereHas(InvoiceRefundCharge::RELATION_REFUNDED_PAYMENT, function (Builder $builder) use ($refundedTransactionReference) {
            $builder->where(InvoiceTransaction::FIELD_EXTERNAL_REFERENCE, $refundedTransactionReference);
        });

        $query->whereHas(InvoiceRefundCharge::RELATION_INVOICE_REFUND, function (Builder $builder) use ($invoiceUuid) {
            $builder->whereHas(InvoiceRefund::RELATION_INVOICE, function (Builder $builder) use ($invoiceUuid) {
                $builder->where(Invoice::FIELD_UUID, $invoiceUuid);
            });
        });

        $query->where(InvoiceRefundCharge::FIELD_AMOUNT, $refundAmount);

        if (filled($invoiceRefundChargeStatus)) {
            $query->whereIn(InvoiceRefundCharge::FIELD_REQUEST_STATUS, $invoiceRefundChargeStatus);
        }

        /** @var InvoiceRefundCharge */
        return $query->first();
    }

    public function createRefundCharge(
        string $uuid,
        float $amount,
        InvoiceRefundStatus $requestStatus,
        int $invoiceRefundId,
        int $refundedPaymentId,
        ?string $refundTransactionUuid = null
    ): InvoiceRefundCharge
    {
        $refundCharge = new InvoiceRefundCharge([
            InvoiceRefundCharge::FIELD_UUID                    => $uuid,
            InvoiceRefundCharge::FIELD_AMOUNT                  => $amount,
            InvoiceRefundCharge::FIELD_REQUEST_STATUS          => $requestStatus,
            InvoiceRefundCharge::FIELD_INVOICE_REFUND_ID       => $invoiceRefundId,
            InvoiceRefundCharge::FIELD_REFUNDED_PAYMENT_ID     => $refundedPaymentId,
            InvoiceRefundCharge::FIELD_REFUND_TRANSACTION_UUID => $refundTransactionUuid,
        ]);

        $refundCharge->save();

        return $refundCharge;
    }

    /**
     * Total refunded is the total amount refunded so far. This function calculates the refunded amount in each refund request.
     * @param float $totalRefunded
     * @param string $externalReference
     * @return array
     */
    public function getNetRefundAmount(float $totalRefunded, string $externalReference): array
    {
        $refundedAlready = InvoiceRefundCharge::query()
            ->whereHas(InvoiceRefundCharge::RELATION_REFUNDED_PAYMENT, function ($query) use ($externalReference) {
                $query->where(InvoiceTransaction::FIELD_EXTERNAL_REFERENCE, $externalReference);
            })
            ->where(InvoiceRefundCharge::FIELD_REQUEST_STATUS, InvoiceRefundStatus::REFUNDED)
            ->sum(InvoiceRefundCharge::FIELD_AMOUNT);

        return [
            'total_refunded'  => $totalRefunded,
            'refunded_amount' => $totalRefunded - $refundedAlready,
        ];
    }

}
