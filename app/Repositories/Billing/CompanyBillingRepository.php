<?php

namespace App\Repositories\Billing;

use App\Models\Legacy\EloquentCompany;
use App\Models\Odin\Company;

class CompanyBillingRepository
{
    const string BILLING_VERSION_V1 = 'v1';
    const string BILLING_VERSION_V2 = 'v2';
    /**
     * @param Company $company
     * @return string
     */
    public function getBillingVersion(Company $company): string
    {
        return $company->{Company::RELATION_LEGACY_COMPANY}?->{EloquentCompany::BILLING_VERSION} ?? self::BILLING_VERSION_V2;
    }

    /**
     * @param Company $company
     * @return bool
     */
    public function isV2(Company $company): bool
    {
        if (empty($company->{Company::FIELD_LEGACY_ID})) {
            return true;
        }
        return  $this->getBillingVersion($company) === self::BILLING_VERSION_V2;
    }

}
