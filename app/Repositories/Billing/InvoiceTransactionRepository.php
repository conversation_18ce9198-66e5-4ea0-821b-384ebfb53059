<?php

namespace App\Repositories\Billing;

use App\Enums\Billing\InvoiceTransactionScenario;
use App\Enums\Billing\InvoiceTransactionType;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceTransaction;
use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class InvoiceTransactionRepository
{
    /**
     * @param string $externalTransactionId
     * @return InvoiceTransaction|null
     */
    public function getInvoiceTransactionByExternalId(string $externalTransactionId): ?InvoiceTransaction
    {
        /** @var ?InvoiceTransaction */
        return InvoiceTransaction::query()
            ->where(InvoiceTransaction::FIELD_EXTERNAL_REFERENCE, $externalTransactionId)
            ->orderByDesc(InvoiceTransaction::FIELD_DATE)
            ->first();
    }

    /**
     * @param string $invoiceUuid
     * @param InvoiceTransactionType|null $type
     * @return Collection|array
     */
    public function getInvoiceTransactionsByInvoiceId(string $invoiceUuid, ?InvoiceTransactionType $type = null): Collection|array
    {
        $query = InvoiceTransaction::query()->where(InvoiceTransaction::FIELD_INVOICE_UUID, $invoiceUuid);

        if (isset($type)) {
            $query->where(InvoiceTransaction::FIELD_TYPE, $type);
        }

        return $query->orderByDesc(InvoiceTransaction::FIELD_ID)->get();
    }

    /**
     * @param string $externalTransactionId
     * @return Invoice|null
     */
    public function getInvoiceByExternalReference(string $externalTransactionId): ?Invoice
    {
        /** @var ?Invoice */
        return InvoiceTransaction::query()
            ->where(InvoiceTransaction::FIELD_EXTERNAL_REFERENCE, $externalTransactionId)
            ->orderByDesc(InvoiceTransaction::FIELD_DATE)
            ->first()?->{InvoiceTransaction::RELATION_INVOICE};
    }

    /**
     * @param int $page
     * @param int $perPage
     * @param array|null $types
     * @param array|null $scenarios
     * @param string|null $externalReference
     * @param int|null $invoiceId
     * @param int|null $minimumValue
     * @param int|null $maximumValue
     * @return LengthAwarePaginator
     */
    public function listInvoiceTransactions(
        int $page,
        int $perPage,
        ?array $types = null,
        ?array $scenarios = null,
        ?string $externalReference = null,
        ?int $invoiceId = null,
        ?int $minimumValue = null,
        ?int $maximumValue = null,
    ): LengthAwarePaginator
    {
        $query = InvoiceTransaction::query();

        if (!empty($types)) {
            $query->whereIn(InvoiceTransaction::FIELD_TYPE, $types);
        }

        if (!empty($scenarios)) {
            $query->whereIn(InvoiceTransaction::FIELD_SCENARIO, $scenarios);
        }

        if (!empty($externalReference)) {
            $query->where(InvoiceTransaction::FIELD_EXTERNAL_REFERENCE, 'LIKE', '%' . $externalReference . '%');
        }

        if (!empty($invoiceId)) {
            $query->whereHas(InvoiceTransaction::RELATION_INVOICE, function (Builder $builder) use ($invoiceId) {
                $builder->where(Invoice::FIELD_ID, $invoiceId);
            });
        }

        if (isset($minimumValue)) {
            $query->where(InvoiceTransaction::FIELD_AMOUNT, '>=', $minimumValue);
        }

        if (isset($maximumValue)) {
            $query->where(InvoiceTransaction::FIELD_AMOUNT, '<=', $maximumValue);
        }

        return $query
            ->orderByDesc(InvoiceTransaction::FIELD_ID)
            ->paginate($perPage, ['*'], 'page', $page);
    }

    /**
     * @param string $invoiceUuid
     * @param InvoiceTransactionType $type
     * @return float
     */
    public function calculateTypeTotal(
        string $invoiceUuid,
        InvoiceTransactionType $type,
        ?InvoiceTransactionScenario $scenario = null
    ): float
    {
        return $this->getInvoiceTransactions(
            invoiceUuid: $invoiceUuid,
            type       : $type,
            scenario   : $scenario
        )->sum(InvoiceTransaction::FIELD_AMOUNT) ?? 0.0;
    }

    /**
     * @param int|null $invoiceId
     * @param string|null $invoiceUuid
     * @param InvoiceTransactionType|null $type
     * @param array|null $relations
     * @param string|null $externalReference
     * @param InvoiceTransactionScenario|null $scenario
     * @return Collection
     * @throws Exception
     */
    public function getInvoiceTransactions(
        string $invoiceUuid,
        ?InvoiceTransactionType $type = null,
        ?array $relations = [],
        ?string $externalReference = null,
        ?InvoiceTransactionScenario $scenario = null
    ): Collection
    {
        return InvoiceTransaction::query()
            ->where(InvoiceTransaction::FIELD_INVOICE_UUID, $invoiceUuid)
            ->when($type, fn (Builder $builder) => $builder->where(InvoiceTransaction::FIELD_TYPE, $type->value))
            ->when($scenario, fn (Builder $builder) => $builder->where(InvoiceTransaction::FIELD_SCENARIO, $scenario->value))
            ->when($externalReference, fn (Builder $builder) => $builder->where(InvoiceTransaction::FIELD_EXTERNAL_REFERENCE, $externalReference))
            ->with($relations)
            ->get();
    }
}
