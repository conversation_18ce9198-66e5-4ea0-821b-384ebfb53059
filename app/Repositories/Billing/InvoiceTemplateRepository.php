<?php

namespace App\Repositories\Billing;

use App\Models\Billing\InvoiceTemplate;
use Illuminate\Database\Eloquent\Builder;

class InvoiceTemplateRepository
{
    /**
     * @return Builder
     */
    public function getListInvoiceTemplatesQuery(): Builder
    {
        return InvoiceTemplate::query();
    }

    /**
     * @param string $name
     * @param bool $isGlobal
     * @param array $props
     * @param int $createdById
     * @param string|null $modelType
     * @param string|null $modelId
     * @return InvoiceTemplate
     */
    public function createInvoiceTemplate(
        string $name,
        bool $isGlobal,
        array $props,
        int $createdById,
        ?string $modelType = null,
        ?string $modelId = null,
    ): InvoiceTemplate
    {
        /** @var InvoiceTemplate $invoiceTemplate */
        $invoiceTemplate = InvoiceTemplate::query()->create([
            InvoiceTemplate::FIELD_NAME          => $name,
            InvoiceTemplate::FIELD_IS_GLOBAL     => $isGlobal,
            InvoiceTemplate::FIELD_MODEL_TYPE    => $modelType,
            InvoiceTemplate::FIELD_MODEL_ID      => $modelId,
            InvoiceTemplate::FIELD_PROPS         => $props,
            InvoiceTemplate::FIELD_CREATED_BY_ID => $createdById,
        ]);

        return $invoiceTemplate;
    }


    /**
     * @param int $id
     * @param string $name
     * @param bool $isGlobal
     * @param array $props
     * @param string|null $modelType
     * @param string|null $modelId
     * @return InvoiceTemplate
     */
    public function updateInvoiceTemplate(
        int $id,
        string $name,
        bool $isGlobal,
        array $props,
        ?string $modelType = null,
        ?string $modelId = null,
    ): InvoiceTemplate
    {
        /** @var InvoiceTemplate $invoiceTemplate */
        $invoiceTemplate = InvoiceTemplate::query()->findOrFail($id);

        $invoiceTemplate
            ->update([
                InvoiceTemplate::FIELD_NAME       => $name,
                InvoiceTemplate::FIELD_IS_GLOBAL  => $isGlobal,
                InvoiceTemplate::FIELD_MODEL_TYPE => $modelType,
                InvoiceTemplate::FIELD_MODEL_ID   => $modelId,
                InvoiceTemplate::FIELD_PROPS      => $props,
            ]);

        return $invoiceTemplate;
    }
}
