<?php

namespace App\Repositories\Billing;

use App\Enums\Billing\GraphGroupByOptions;
use App\Enums\Billing\InvoiceStates;
use App\Helpers\CarbonHelper;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceSnapshot;
use App\Models\Odin\Company;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection as SupportCollection;
use Illuminate\Support\Facades\DB;

class InvoiceSnapshotRepository
{
    const string FILTER_STATUS = 'status';
    const string FILTER_FROM   = 'startDate';
    const string FILTER_TO     = 'endDate';

    /**
     * @param array $filters
     * @param bool|null $paginate
     * @return Collection|LengthAwarePaginator
     */
    public function getInvoiceSnapshots(array $filters, ?bool $paginate = false): Collection|LengthAwarePaginator
    {
        $query = InvoiceSnapshot::mostRecentByInvoice();

        if (isset($filters[self::FILTER_STATUS])) {
            // hacky to make it match the revenue report
            if ($filters[self::FILTER_STATUS] === InvoiceStates::PAID->value) {
                $query->where(function ($query) {
                    $query->where(InvoiceSnapshot::FIELD_TOTAL_PAID, '>', 0)
                        ->orWhere(InvoiceSnapshot::FIELD_TOTAL_CREDIT_APPLIED, '>', 0);
                })->whereNot('status', InvoiceStates::CHARGEBACK);
            } else {
                $query->whereIn(InvoiceSnapshot::FIELD_STATUS, (array)$filters[self::FILTER_STATUS]);
            }
        }

        if (isset($filters[self::FILTER_FROM]) && isset($filters[self::FILTER_TO])) {
            $query->whereBetween(InvoiceSnapshot::FIELD_DATE,
                [
                    CarbonHelper::parseWithTimezone($filters[self::FILTER_FROM])->startOfDayUTC(),
                    CarbonHelper::parseWithTimezone($filters[self::FILTER_TO])->endOfDayUTC(),
                ]);
        }

        if ($paginate) {
            return $query->paginate($filters['perPage'], ['*'], 'page', $filters['page']);
        } else {
            return $query->get();
        }
    }

    /**
     * @param Carbon $start
     * @param Carbon $end
     * @param GraphGroupByOptions $groupedBy
     * @return SupportCollection
     */
    public function getInvoiceRevenueGrouped(Carbon $start, Carbon $end, GraphGroupByOptions $groupedBy): SupportCollection
    {
        $query = $this->getInvoiceSnapshots(['startDate' => $start, 'endDate' => $end, 'status' => InvoiceStates::PAID->value]);

        $firstDate = $start->startOfDay();

        $grouped = [];

        // Initialise keys
        while ($end->gt($firstDate)) {
            $key = $groupedBy->getCarbonTimeGrouped($firstDate)->toString();

            if (!isset($grouped[$key])) {
                $grouped[$key] = [];
            }

            $firstDate = $firstDate->add($groupedBy->getUnit(), 1);
        }

        foreach ($grouped as $key => &$value) {
            $value = $query->filter(function ($e) use ($key, $groupedBy) {
                $startOfKey = Carbon::parse($key);
                $endOfKey = Carbon::parse($key)->add($groupedBy->getUnit(), 1);

                return $e->created_at->gte($startOfKey) && $e->created_at->lte($endOfKey);
            })->values();
        }

        $total = 0;

        return collect($grouped)->map(function ($items, $hour) use (&$total, $groupedBy) {
            $total += $items->sum('total_paid');
            return [
                'period' => $hour,
                'total'  => $total / 100
            ];
        });
    }

    /**
     * @param Company $company
     * @return object|null
     */
    public function getCompanyInvoiceSummary(Company $company): ?object
    {
        return InvoiceSnapshot::mostRecentByInvoice()
            ->select([
                DB::raw('SUM(' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_VALUE . ') as ' . InvoiceSnapshot::FIELD_TOTAL_VALUE),
                DB::raw('SUM(' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_PAID . ') as ' . InvoiceSnapshot::FIELD_TOTAL_PAID),
                DB::raw('SUM(' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_OUTSTANDING . ') as ' . InvoiceSnapshot::FIELD_TOTAL_OUTSTANDING),
            ])
            ->whereHas(InvoiceSnapshot::RELATION_INVOICE, function ($query) {
                $query->whereIn(Invoice::FIELD_STATUS, [
                    InvoiceStates::ISSUED->value,
                    InvoiceStates::PAID->value,
                    InvoiceStates::FAILED->value,
                ]);
            })
            ->where(InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_COMPANY_ID, '=', $company->id)
            ->first();
    }

}
