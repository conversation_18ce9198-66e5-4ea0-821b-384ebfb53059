<?php

namespace App\Repositories\Billing;

use App\Builders\Billing\InvoiceBuilder;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoicePayment;
use App\Models\Billing\InvoicePaymentCharge;
use App\Models\Odin\Company;
use App\Repositories\UserRepository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class InvoiceRepository
{
    const string SEARCH_FILTER_COMPANY_ID     = 'companyId';
    const string SEARCH_FILTER_INVOICE_ID     = 'invoiceId';
    const string SEARCH_FILTER_INVOICE_UUID   = 'invoiceUuid';
    const string SEARCH_FILTER_START_DATE     = 'startDate';
    const string SEARCH_FILTER_END_DATE       = 'endDate';
    const string SEARCH_FILTER_STATUS         = 'status';
    const string SEARCH_FILTER_EXCLUDE_STATUS = 'exclude_status';

    public function __construct(protected UserRepository $userRepository)
    {

    }

    /**
     * @param array $filters
     * @return Builder
     */
    public function getInvoicesQuery(array $filters): Builder
    {
        $builder = InvoiceBuilder::query()
            ->select([
                Invoice::TABLE . '.*',
                Company::TABLE . '.' . Company::FIELD_NAME . ' as company_name',
                BillingProfile::TABLE . '.' . BillingProfile::FIELD_PAYMENT_METHOD,
                'latest_snapshot.total_outstanding',
                'invoice_items_total.total_items_price',
                DB::raw('CASE WHEN pending_action.id is not null THEN 1 ELSE 0 END as has_pending_action'),
            ])
            ->joinCompany()
            ->leftJoinApprovalsInProcessingStatus()
            ->joinMostRecentInvoiceSnapshot()
            ->joinItemItemTotals()
            ->joinBillingProfile()
            ->with(Invoice::RELATION_PAYMENTS . '.' . InvoicePayment::RELATION_CHARGES . '.' . InvoicePaymentCharge::RELATION_PAYMENT_METHOD, function ($query) {
                return $query->withTrashed();
            })
            ->with(Invoice::RELATION_CREDITS_APPLIED)
            ->forCompanyId(Arr::get($filters, self::SEARCH_FILTER_COMPANY_ID))
            ->forInvoiceId(Arr::get($filters, self::SEARCH_FILTER_INVOICE_ID))
            ->forInvoiceUuid(Arr::get($filters, self::SEARCH_FILTER_INVOICE_UUID))
            ->betweenDates(
                startDate: Arr::get($filters, self::SEARCH_FILTER_START_DATE),
                endDate  : Arr::get($filters, self::SEARCH_FILTER_END_DATE),
            )
            ->inStatuses(
                statuses: Arr::get($filters, self::SEARCH_FILTER_STATUS),
            )
            ->inStatuses(
                statuses: Arr::get($filters, self::SEARCH_FILTER_EXCLUDE_STATUS),
                not     : true
            )
            ->withAnyTags(
                tags: Arr::get($filters, 'tags'),
            )
            ->sortBy(
                sortBy: Arr::get($filters, 'sort_by') ?? ['id:desc'],
            );

        if (empty(Arr::get($filters, self::SEARCH_FILTER_COMPANY_ID))) {
            $builder->filterByRole(
                user: Arr::get($filters, 'filter_by_role')
            );
        }

        return $builder
            ->getQuery()
            ->distinct();
    }

    /**
     * @param array $filters
     * @param bool $paginate
     * @return LengthAwarePaginator|Collection
     */
    public function getInvoices(array $filters, bool $paginate = true): LengthAwarePaginator|Collection
    {
        $query = $this->getInvoicesQuery($filters);


        if ($paginate) {
            return
                $query->paginate($filters['perPage'], ['*'], 'page', $filters['page']);
        } else {
            /** @var Collection */
            return $query->get();
        }

    }

    /**
     * @param int $invoiceId
     * @return Invoice
     */
    public function getInvoice(int $invoiceId): Invoice
    {
        /** @var Invoice */
        return Invoice::query()->where(Invoice::FIELD_ID, $invoiceId)->first();
    }

    /**
     * @param array $filters
     * @return Collection
     */
    public function getInvoiceCountGroupedByType(array $filters): Collection
    {
        return $this->getInvoicesQuery($filters)
            ->select([
                DB::raw('count(*) as invoice_count'),
                Invoice::TABLE . '.' . Invoice::FIELD_STATUS,
            ])
            ->groupBy(Invoice::TABLE . '.' . Invoice::FIELD_STATUS)
            ->get();
    }
}
