<?php
namespace App\Repositories;

use App\Enums\AuditLogType;
use App\Models\AuditLog;
use App\Models\CompanyContract;
use App\Models\Odin\CompanyUser;
use App\Models\User;
use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Model;

class AuditLogRepository
{
    /**
     * @param array $data
     * @return AuditLog
     */
    public function createAuditLog(array $data = []): AuditLog
    {
        $newAuditLog = new AuditLog();
        $newAuditLog->fill($data);
        $newAuditLog->save();
        return $newAuditLog;
    }

    /**
     * @param CompanyContract $model
     * @param CompanyUser|User $actor
     * @param AuditLogType $auditLogType
     * @return AuditLog
     */
    public function createAuditLogForActorAndModel(Model $model, CompanyUser|User $actor, AuditLogType $auditLogType): AuditLog
    {
        return $this->createAuditLog([
            'audit_event'   => $auditLogType->value,
            'actor'         => $actor instanceof CompanyUser ? $actor->completeName() : $actor->name,
            'actor_id'      => $actor->id,
            'actor_type'    => get_class($actor),
            'model_type'    => get_class($model),
            'model_id'      => $model->id,
            'description'   => $auditLogType->getDescription([
                'username'      => $actor instanceof CompanyUser ? $actor->completeName() : $actor->name,
                'contract_uuid' => $model->uuid,
            ]),
        ]);
    }

    /**
     * @param int $companyContractId
     * @return Collection
     */
    public function getAuditLogsForCompanyContract(int $companyContractId): Collection
    {
        return AuditLog::query()
            ->where(AuditLog::FIELD_MODEL_TYPE, CompanyContract::class)
            ->where(AuditLog::FIELD_MODEL_ID, $companyContractId)
            ->get();
    }

}
