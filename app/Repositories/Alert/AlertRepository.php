<?php

namespace App\Repositories\Alert;

use App\Enums\Alert\AlertType;
use App\Models\Alert;
use App\Models\AlertRecipient;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\LeadProcessingQueueConfiguration;
use App\Models\Odin\CompanyUser;
use App\Models\User;
use Illuminate\Support\Arr;

class AlertRepository
{
    /**
     * @param LeadProcessingQueueConfiguration $queue
     * @param AlertType $type
     * @param bool $active
     * @param array $recipients
     * @param array $payload
     * @param Alert|null $alert
     *
     * @return Alert
     */
    public function createOrUpdateQueueAlert(
        LeadProcessingQueueConfiguration $queue,
        AlertType $type,
        bool $active,
        array $recipients,
        array $payload = [],
        ?Alert $alert = null
    ): Alert
    {
        if ($alert) {
            $alert->update([
                Alert::FIELD_TYPE    => $type,
                Alert::FIELD_PAYLOAD => [...($alert->payload ?? []), ...$payload],
                Alert::FIELD_ACTIVE  => $active
            ]);
        } else {
            $alert = $queue->alerts()->create([
                Alert::FIELD_TYPE    => $type,
                Alert::FIELD_PAYLOAD => $payload,
                Alert::FIELD_ACTIVE  => $active
            ]);
        }

        $alert->recipients()->delete();
        $alert->recipients()->createMany(
            User::query()
                ->whereIn(User::FIELD_ID, $recipients)
                ->get()
                ->map(fn(User $user) => [
                    AlertRecipient::FIELD_NOTIFIABLE_TYPE => $user::class,
                    AlertRecipient::FIELD_NOTIFIABLE_ID => $user->id,
                    AlertRecipient::FIELD_CHANNEL => AlertRecipient::CHANNEL_EMAIL
                ])
        );

        return $alert;
    }

    /**
     * @param string $campaignReference
     * @param int $alertId
     *
     * @return Alert
     */
    public function findAlertForCampaignOrFail(string $campaignReference, int $alertId): Alert
    {
        /** @var  Alert */
        return CompanyCampaign::query()
            ->where(CompanyCampaign::FIELD_REFERENCE, $campaignReference)
            ->firstOrFail()
            ->alerts()
            ->findOrFail($alertId);
    }

    /**
     * @param CompanyCampaign $companyCampaign
     * @param AlertType $alertType
     * @param array $payload
     * @param bool $active
     * @param array $recipients
     * @param Alert|null $alert
     *
     * @return Alert
     */
    public function createOrUpdateCampaignAlert(CompanyCampaign $companyCampaign, AlertType $alertType, array $payload, bool $active, array $recipients, ?Alert $alert = null): Alert
    {
        if ($alert) {
            $alert->update([
                Alert::FIELD_TYPE => $alertType,
                Alert::FIELD_PAYLOAD => [...($alert->payload ?? []), ...$payload],
                Alert::FIELD_ACTIVE => $active
            ]);
        } else {
            $alert = $companyCampaign->alerts()->create([
                Alert::FIELD_TYPE => $alertType,
                Alert::FIELD_PAYLOAD => $payload,
                Alert::FIELD_ACTIVE => $active
            ]);
        }

        $alert->recipients()->delete();
        $alert->recipients()->createMany(
            collect($recipients)->map(function (array $recipient) {
                $companyUser = CompanyUser::query()->findOrFail(Arr::get($recipient, 'id'));

                return [
                    AlertRecipient::FIELD_NOTIFIABLE_TYPE => $companyUser::class,
                    AlertRecipient::FIELD_NOTIFIABLE_ID => $companyUser->id,
                    AlertRecipient::FIELD_CHANNEL => Arr::get($recipient, 'channel', AlertRecipient::CHANNEL_EMAIL)
                ];
            })
        );

        return $alert;
    }
}
