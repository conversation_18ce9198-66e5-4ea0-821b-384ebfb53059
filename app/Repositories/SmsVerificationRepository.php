<?php

namespace App\Repositories;

use App\Models\SmsVerification;

class SmsVerificationRepository
{
    /**
     * @param string $code
     * @param string $reference
     * @param string $twilioReference
     * @return void
     */
    public function createSmsVerification(string $code, string $reference, string $twilioReference): void
    {
        SmsVerification::query()->create([
            SmsVerification::REFERENCE => $reference,
            SmsVerification::CODE => $code,
            SmsVerification::SENT_REFERENCE => $twilioReference,
            SmsVerification::EXPIRES_AT => now()->addHour()
        ]);
    }
}
