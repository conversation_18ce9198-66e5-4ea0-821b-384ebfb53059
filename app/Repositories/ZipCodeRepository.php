<?php

namespace App\Repositories;

use App\Models\USZipCode;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ZipCodeRepository
{
    /**
     * @param string $zipCode
     * @return USZipCode|null
     */
    public function getByZipCodeString(string $zipCode): ?USZipCode
    {
        return USZipCode::query()
            ->where('zip_code', $zipCode)
            ->first();
    }

    /**
     * @param USZipCode $zipCode
     * @param int $radius
     * @return Collection
     */
    public function getZipCodesByRadius(USZipCode $zipCode, int $radius): Collection
    {
        return $this->getZipCodesByRadiusQuery($zipCode, $radius)
            ->get()
            ->pluck(USZipCode::FIELD_ZIP_CODE);
    }

    /**
     * @param USZipCode $zipCode
     * @param int $radius
     * @return Builder
     */
    protected function getZipCodesByRadiusQuery(USZipCode $zipCode, int $radius): Builder
    {
        $distanceAlias = 'distance';

        return USZipCode::query()
            ->select(DB::raw(USZipCode::FIELD_ZIP_CODE . ", 3959 * acos(cos(radians({$zipCode->latitude})) * cos(radians(latitude))
                          * cos(radians(longitude) - radians({$zipCode->longitude})) +
                        sin(radians({$zipCode->latitude})) * sin(radians(latitude))) as $distanceAlias"),
            )->where(USZipCode::FIELD_ZIP_TYPE, USZipCode::DEFAULT_ZIP_TYPE)
            ->where(USZipCode::FIELD_CITY_TYPE, USZipCode::DEFAULT_CITY_TYPE)
            ->groupBy(USZipCode::FIELD_ZIP_CODE)
            ->having($distanceAlias, '<', $radius);
    }
}