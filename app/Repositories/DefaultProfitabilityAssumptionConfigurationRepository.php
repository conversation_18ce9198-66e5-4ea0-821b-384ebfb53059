<?php

namespace App\Repositories;

use App\Models\Odin\GenericProfitabilityAssumptionConfiguration;
use App\Models\Odin\IndustryService;
use Illuminate\Support\Collection;

class DefaultProfitabilityAssumptionConfigurationRepository
{

    /**
     * @return Collection<GenericProfitabilityAssumptionConfiguration>
     */
    public function getDefaultProfitabilityAssumptions(): Collection
    {
        return GenericProfitabilityAssumptionConfiguration::query()
            ->where(GenericProfitabilityAssumptionConfiguration::FIELD_IS_DEFAULT, true)
            ->get();
    }

    /**
     * @return Collection<GenericProfitabilityAssumptionConfiguration>
     */
    public function getGenericProfitabilityAssumptions(): Collection
    {
        return GenericProfitabilityAssumptionConfiguration::query()
            ->where(GenericProfitabilityAssumptionConfiguration::FIELD_IS_DEFAULT, false)
            ->where(GenericProfitabilityAssumptionConfiguration::FIELD_COMPANY_ID, '>', 0)
            ->get();
    }

    /**
     * @param int $industryServiceId
     * @param array $updateData
     * @return GenericProfitabilityAssumptionConfiguration|null
     */
    public function updateOrCreateDefaultProfitabilityAssumption(int $industryServiceId, array $updateData): ?GenericProfitabilityAssumptionConfiguration
    {
        IndustryService::query()->findOrFail($industryServiceId);

        /** @var GenericProfitabilityAssumptionConfiguration $updatedProfitability */
        $updatedProfitability = GenericProfitabilityAssumptionConfiguration::query()
            ->updateOrCreate([
                GenericProfitabilityAssumptionConfiguration::FIELD_INDUSTRY_SERVICE_ID  => $industryServiceId,
                GenericProfitabilityAssumptionConfiguration::FIELD_IS_DEFAULT           => true,
            ], [
                GenericProfitabilityAssumptionConfiguration::FIELD_PERCENTAGE_LEADS_SUCCESSFUL => $updateData[GenericProfitabilityAssumptionConfiguration::FIELD_PERCENTAGE_LEADS_SUCCESSFUL],
                GenericProfitabilityAssumptionConfiguration::FIELD_AVERAGE_LEAD_REVENUE        => $updateData[GenericProfitabilityAssumptionConfiguration::FIELD_AVERAGE_LEAD_REVENUE],
                GenericProfitabilityAssumptionConfiguration::FIELD_AVERAGE_JOB_COST            => $updateData[GenericProfitabilityAssumptionConfiguration::FIELD_AVERAGE_JOB_COST],
                GenericProfitabilityAssumptionConfiguration::FIELD_LABOUR_MATERIALS_COST       => $updateData[GenericProfitabilityAssumptionConfiguration::FIELD_LABOUR_MATERIALS_COST],
        ]);
        return $updatedProfitability;
    }

    /**
     * @param int $industryServiceId
     * @return bool
     */
    public function deleteDefaultProfitabilityAssumption(int $industryServiceId): bool
    {
        return GenericProfitabilityAssumptionConfiguration::query()
            ->where(GenericProfitabilityAssumptionConfiguration::FIELD_INDUSTRY_SERVICE_ID, $industryServiceId)
            ->where(GenericProfitabilityAssumptionConfiguration::FIELD_IS_DEFAULT, true)
            ->delete();
    }

    /**
     * @param Collection $updatesByIndustryServiceId
     * @return Collection
     */
    public function updateAllDefaultProfitabilityConfigurations(Collection $updatesByIndustryServiceId): Collection
    {
        return $updatesByIndustryServiceId->map(function (array $data) {
            return $this->updateOrCreateDefaultProfitabilityAssumption($data[GenericProfitabilityAssumptionConfiguration::FIELD_INDUSTRY_SERVICE_ID], $data);
        });
    }
}
