<?php

namespace App\Repositories\GlobalConfiguration;

use App\DataModels\Odin\ConfigurableFieldDataModel;
use App\Enums\GlobalConfigurationKey;
use App\Models\GlobalConfiguration;
use App\Models\User;
use Carbon\Carbon;

class GlobalConfigurationRepository
{
    /**
     * @param GlobalConfigurationKey $key
     * @return GlobalConfiguration|null
     */
    public function getConfigurationPayload(GlobalConfigurationKey $key): ?ConfigurableFieldDataModel
    {
        /** @var ?GlobalConfiguration $config */
        $config = GlobalConfiguration::query()
            ->where(GlobalConfiguration::FIELD_CONFIGURATION_KEY, $key->value)
            ->first();

        return $config?->{GlobalConfiguration::FIELD_CONFIGURATION_PAYLOAD} ?? null;
    }

    /**
     * @param GlobalConfiguration $configuration
     * @param string $configuration_key
     * @param array $configuration_payload
     * @param User|null $user
     * @return GlobalConfiguration
     */
    public function updateConfiguration(
        GlobalConfiguration $configuration,
        string $configuration_key,
        array $configuration_payload,
        ?User $user = null
    ): GlobalConfiguration
    {
        // Get payload from data object to create a ConfigurableFieldDataModel object
        $filter_payload = $configuration_payload['data'];

        $configuration->update([
            GlobalConfiguration::FIELD_CONFIGURATION_KEY => $configuration_key,
            GlobalConfiguration::FIELD_CONFIGURATION_PAYLOAD => ConfigurableFieldDataModel::fromArray($filter_payload),
            GlobalConfiguration::FIELD_UPDATED_BY_ID => $user?->{User::FIELD_ID}
        ]);

        return $configuration;
    }

    public function deleteConfiguration(
        $configurationId,
        ?User $user = null
    ): GlobalConfiguration
    {
        $configuration = GlobalConfiguration::find($configurationId);

        $configuration->update([
            GlobalConfiguration::FIELD_DELETED_AT => Carbon::now(),
            GlobalConfiguration::FIELD_UPDATED_BY_ID => $user?->{User::FIELD_ID}
        ]);

        return $configuration;
    }

    /**
     * @param string $configuration_key
     * @param array $configuration_payload
     * @param User|null $user
     * @return GlobalConfiguration
     */
    public function createConfiguration(
        string $configuration_key,
        array $configuration_payload,
        ?User $user = null
    ): GlobalConfiguration
    {
        // Get payload from data object to create a ConfigurableFieldDataModel object
        $filter_payload = $configuration_payload['data'];

        $configuration = GlobalConfiguration::create([
            GlobalConfiguration::FIELD_CONFIGURATION_KEY => $configuration_key,
            GlobalConfiguration::FIELD_CONFIGURATION_PAYLOAD => ConfigurableFieldDataModel::fromArray($filter_payload),
            GlobalConfiguration::FIELD_UPDATED_BY_ID => $user?->{User::FIELD_ID}
        ]);

        return $configuration;
    }

}
