<?php

namespace App\Repositories\Flows;

use App\Enums\Flows\RevisionType;
use App\Models\Firestore\Flows\Flow;
use App\Models\Firestore\Flows\Revision;
use App\Services\GoogleFirestoreService;
use Exception;
use Google\Cloud\Firestore\DocumentSnapshot;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Validation\UnauthorizedException;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Collection;

class RevisionRepository
{
    const COLLECTION_FLOWS          = 'Flows';
    const COLLECTION_REVISIONS      = 'Revisions';
    const COLLECTION_REVISION_META  = 'RevisionMeta';

    const UPDATE_MINOR = 'minor';
    const UPDATE_MAJOR = 'major';
    const UPDATE_RESET = 'reset';

    public function __construct(
        protected GoogleFirestoreService $firestoreService
    ) {}

    /**
     * @param string $flowId
     * @param string $revisionUuid
     * @param bool $currentVersion - set to 'false' to retrieve the Working copy with unpublished changes
     * @return array|null
     */
    public function getRevisionPayload(string $flowId, string $revisionUuid, bool $currentVersion = true): ?array
    {
        $revisionPath = self::COLLECTION_FLOWS ."/$flowId/". self::COLLECTION_REVISIONS;
        $metaPath = self::COLLECTION_FLOWS ."/$flowId/". self::COLLECTION_REVISION_META;

        $revision = $this->firestoreService->getDocumentByReference($revisionPath, $revisionUuid);
        $meta = $this->firestoreService->getDocumentByReference($metaPath, $revisionUuid);

        if (!$revision || !$meta) {
            throw new ModelNotFoundException("The requested Revision does not exist.");
        }

        $meta[Revision::REVISION_DATA] = $currentVersion || !$revision[Revision::DATA_WORKING]
            ? $revision[Revision::DATA_CURRENT]
            : $revision[Revision::DATA_WORKING];

        return $meta;
    }

    /**
     * @param string $parentFlowId
     * @param array $data
     * @param ?string $uuid
     * @return bool
     */
    public function create(string $parentFlowId, array $data, ?string $uuid = null): bool
    {
        $newUuid = $uuid ?? Uuid::uuid4()->toString();
        $payload = $data[Revision::REVISION_DATA] ?? null;

        // Create the RevisionMeta data
        $metaResult = $this->firestoreService->addDocumentToCollection(self::COLLECTION_FLOWS . "/$parentFlowId/" . self::COLLECTION_REVISION_META, $newUuid, [
            Revision::REVISION_ID       => $newUuid,
            Revision::ENVIRONMENT       => config('services.google.firestore.collection_prefix'),
            Revision::DESCRIPTION       => $data[Revision::DESCRIPTION] ?? "",
            Revision::NAME              => $data[Revision::NAME],
            Revision::PARENT_REVISION   => $data[Revision::PARENT_REVISION] ?? null,
            Revision::TYPE              => $data[Revision::TYPE],
            Revision::CREATED_AT        => now(),
            Revision::UPDATED_AT        => null,
            Revision::ACTIONED_BY       => $data[Revision::ACTIONED_BY] ?? null,
            Revision::VERSION           => $data[Revision::VERSION] ?? "0.1",
        ]);

        // Create the Revision payload if required
        $flowResult = !$payload || $this->firestoreService->addDocumentToCollection(
            self::COLLECTION_FLOWS . "/$parentFlowId/" . self::COLLECTION_REVISIONS,
            $newUuid,
                [
                    Revision::REVISION_ID => $newUuid,
                    Revision::ENVIRONMENT => config('services.google.firestore.collection_prefix'),
                    'current'             => $payload,
                    'working'             => null
                ]
        );

        return $metaResult && $flowResult;
    }

    /**
     * Update meta data of a Revision
     * @param string $parentFlowId
     * @param string $revisionUuid
     * @param array $data
     * @return bool
     */
    public function update(string $parentFlowId, string $revisionUuid, array $data): bool
    {
        $updatePayload = array_filter([
            Revision::REVISION_ID => $revisionUuid,
            Revision::ENVIRONMENT => config('services.google.firestore.collection_prefix'),
            Revision::DESCRIPTION => $data[Revision::DESCRIPTION] ?? null,
            Revision::UPDATED_AT  => now(),
            Revision::ACTIONED_BY => $data[Revision::ACTIONED_BY] ?? null,
            Revision::TYPE        => $data[Revision::TYPE] ?? null,
            Revision::VERSION     => $data[Revision::VERSION] ?? null,
        ], fn($v) => $v !== null);

        return $this->firestoreService->updateDocument(
            self::COLLECTION_FLOWS . "/$parentFlowId/" . self::COLLECTION_REVISION_META,
            $revisionUuid,
            $updatePayload
        );
    }

    /**
     * @param string $flowId
     * @param string $revisionUuid
     * @param array $metadata
     * @return array|null
     */
    public function publish(string $flowId, string $revisionUuid, array $metadata): ?array
    {
        $metaPath = self::COLLECTION_FLOWS ."/$flowId/". self::COLLECTION_REVISION_META;
        $revisionPath = self::COLLECTION_FLOWS ."/$flowId/". self::COLLECTION_REVISIONS;

        /** @var array $baseMetadata */
        $baseMetadata = $this->firestoreService->getDocumentByReference($metaPath, $revisionUuid);
        if ($baseMetadata[Revision::TYPE] === RevisionType::HISTORY->value) {
            throw new UnauthorizedException("History Revisions may not be published from.");
        }

        if (!$baseMetadata) throw new ModelNotFoundException("The requested Flow Revision does not exist.");

        $newVersionUuid = Uuid::uuid4()->toString();
        $newVersionData = [
            ...$baseMetadata,
            Revision::DESCRIPTION       => $metadata[Revision::DESCRIPTION],
            Revision::ACTIONED_BY       => $metadata[Revision::ACTIONED_BY],
            Revision::PARENT_REVISION   => $revisionUuid,
            Revision::REVISION_DATA     => $metadata[Revision::REVISION_DATA],
        ];

        if (
            $this->create($flowId, $newVersionData, $newVersionUuid)
            && $this->incrementVersion($flowId, $newVersionUuid, $metadata, self::UPDATE_MAJOR)
            && $this->incrementVersion($flowId, $revisionUuid, $baseMetadata, self::UPDATE_RESET,  true)
        )
        {
            return $this->firestoreService->getDocumentByReference($metaPath, $newVersionUuid);
        }
        else {
            return null;
        }
    }

    /**
     * @param string $flowId
     * @param string $revisionUuid
     * @param string $payload
     * @return ?array
     */
    public function save(string $flowId, string $revisionUuid, string $payload): ?array
    {
        $revisionPath = self::COLLECTION_FLOWS ."/$flowId/". self::COLLECTION_REVISIONS;
        $metaPath = self::COLLECTION_FLOWS ."/$flowId/". self::COLLECTION_REVISION_META;

        $currentMeta = $this->firestoreService->getDocumentByReference($metaPath, $revisionUuid);

        if ($currentMeta[Revision::TYPE] === RevisionType::HISTORY->value) {
            throw new UnauthorizedException("History Revisions may not be saved to.");
        }

        $saved = $this->firestoreService->updateDocument($revisionPath, $revisionUuid, [Revision::DATA_WORKING => $payload, Revision::REVISION_ID => $revisionUuid, Revision::ENVIRONMENT => config('services.google.firestore.collection_prefix'),])
            && $this->incrementVersion($flowId, $revisionUuid, $currentMeta, self::UPDATE_MINOR);

        return $saved
            ? $this->firestoreService->getDocumentByReference($metaPath, $revisionUuid)
            : null;
    }

    /**
     * @param string $flowId
     * @param array $data
     * @return array|null
     * @throws Exception
     */
    public function saveNewVariant(string $flowId, array $data): ?array
    {
        if (!$this->newVariantNameIsUnique($flowId, $data[Revision::NAME])) {
            throw new Exception("The name '".$data[Revision::NAME]."' is already in use");
        }

        $newUuid = Uuid::uuid4()->toString();

        return $this->create($flowId, $data, $newUuid)
            ? $this->getRevisionMetadata($flowId, $newUuid)
            : null;
    }

    /**
     * @return Collection
     */
    public function getAllRevisionMetaData(): Collection
    {
        $querySnapshot = $this->firestoreService->getCollectionGroup(self::COLLECTION_REVISION_META, true);

        $flowFilter = [
            'environment' => config('services.google.firestore.collection_prefix'),
        ];
        $groupedCollection = $this->firestoreService->groupLaravelCollectionByPathRegex($querySnapshot, "/Flows\/([^\/]+)\/.*/", false, $flowFilter);

        $flowMeta = $this->firestoreService->getDocumentsByCollection(self::COLLECTION_FLOWS, true);

        return $flowMeta->map(function($group, $key) use (&$groupedCollection) {
            $group['revisions'] = $groupedCollection->get($key) ?? collect();
            return $group;
        });
    }

    public function getRevisionMetadata(string $flowId, string $revisionUuid): array
    {
        $revisionMetaPath = self::COLLECTION_FLOWS ."/$flowId/". self::COLLECTION_REVISION_META;
        return $this->firestoreService->getDocumentByReference($revisionMetaPath, $revisionUuid);
    }

    public function delete(string $flowId, string $revisionUuid): bool
    {
        $revisionPath = self::COLLECTION_FLOWS ."/$flowId/". self::COLLECTION_REVISIONS;
        $revisionMetaPath = self::COLLECTION_FLOWS ."/$flowId/". self::COLLECTION_REVISION_META;

        return $this->firestoreService->deleteDocument($revisionPath, $revisionUuid)
            && $this->firestoreService->deleteDocument($revisionMetaPath, $revisionUuid);
    }

    /**
     * Check the revision is not a live/active version which should not be written to
     * @param string $flowId
     * @param string $revisionUuid
     * @return bool
     */
    private function checkRevisionCanBePublished(string $flowId, string $revisionUuid): bool
    {
        $flow = $this->firestoreService->getDocumentByReference(self::COLLECTION_FLOWS, $flowId);
        $productionVersion = $flow[Flow::VERSIONS][Flow::PRODUCTION] ?? null;

        return $revisionUuid !== $productionVersion;
    }

    /**
     * @param string $flowId
     * @param string $revisionUuid
     * @param array $updatedMeta
     * @param string $updateType
     * @param bool $flagAsHistory
     * @return bool|null
     */
    private function incrementVersion(string $flowId, string $revisionUuid, array $updatedMeta, string $updateType, bool $flagAsHistory = false): bool
    {
        $metaPath = self::COLLECTION_FLOWS ."/$flowId/". self::COLLECTION_REVISION_META;
        $revisionPath = self::COLLECTION_FLOWS ."/$flowId/". self::COLLECTION_REVISIONS;

        /** @var DocumentSnapshot $currentMeta */
        $currentMeta = $this->firestoreService->getDocumentByReference($metaPath, $revisionUuid);

        $currentVersion = $currentMeta[Revision::VERSION];
        $parts = preg_split("/\./", $currentVersion);

        if (count($parts) !== 2) {
            $major = 0;
            $minor = 1;
        }
        else if ($updateType === self::UPDATE_RESET && intval($parts[0]) === 0) { // Don't save v0.0 histories
            return $this->firestoreService->deleteDocument($metaPath, $revisionUuid)
                    && $this->firestoreService->deleteDocument($revisionPath, $revisionUuid);
        }
        else {
            $major = match ($updateType) {
                self::UPDATE_MAJOR => $parts[0] + 1,
                default => $parts[0],
            };
            $minor = match ($updateType) {
                self::UPDATE_MINOR => $parts[1] + 1,
                default => 0,
            };
        }

        $updateData = [
            ...$updatedMeta,
            Revision::VERSION => "$major.$minor",
            Revision::TYPE    => $flagAsHistory ? RevisionType::HISTORY->value : $currentMeta[Revision::TYPE],
        ];

        if ($updateType === self::UPDATE_RESET) {
            $this->firestoreService->updateDocument($revisionPath, $revisionUuid, [Revision::DATA_WORKING => null]);
        }

        return $this->update($flowId, $revisionUuid, $updateData);
    }

    /**
     * Ensure a new Revision name is unique inside its parent Flow
     * @param string $flowId
     * @param string $name
     * @return bool
     */
    public function newVariantNameIsUnique(string $flowId, string $name): bool
    {
        $metaPath = self::COLLECTION_FLOWS ."/$flowId/". self::COLLECTION_REVISION_META;
        $metaCollection = $this->firestoreService->getDocumentsByCollection($metaPath);

        foreach($metaCollection as $meta) {
            if (trim(strtolower($meta[Revision::NAME])) === trim(strtolower($name))) {
                return false;
            }
        }

        return true;
    }

}
