<?php

namespace App\Repositories\Flows;

use App\Models\Firestore\Flows\Flow;
use App\Repositories\Flows\v2\RevisionService;
use App\Services\GoogleFirestoreService;
use App\Repositories\Flows\v2\RevisionRepository;
use Exception;

class FlowRepository
{
    const LEGACY_COLLECTION_FLOWS          = 'Flows';
    const LEGACY_COLLECTION_REVISIONS      = 'Revisions';
    const LEGACY_COLLECTION_REVISION_META  = 'RevisionMeta';

    const COLLECTION_FLOWS          = RevisionRepository::COLLECTION_FLOWS;
    const COLLECTION_REVISIONS      = RevisionRepository::COLLECTION_PUBLISHED_REVISIONS;

    public function __construct(
        protected GoogleFirestoreService $firestoreService,
        protected RevisionService $revisionService,
    ) {}

    /**
     * @param array $data
     * @return bool
     */
    public function create(array $data): bool
    {
        $documentName = strtolower(preg_replace("/\s+/", "-", preg_replace("/[^0-z-\s]/", "", $data[Flow::NAME] ?? [])));
        if (!$documentName) return false;

        return $this->firestoreService->addDocumentToCollection(self::COLLECTION_FLOWS, $documentName, [
            'name'          => $data[Flow::NAME],
            'description'   => $data[Flow::DESCRIPTION] ?? "",
            'created_at'    => now(),
            'versions'      => [
                'production'    => null,
            ],
        ]);
    }

    /**
     * @param string $flowDocumentId
     * @param array $data
     * @return bool
     */
    public function update(string $flowDocumentId, array $data): bool
    {
        $versions = $data[Flow::VERSIONS] ?? [];

        $updatePayload = array_filter([
            'name'                  => $data[Flow::NAME],
            'description'           => $data[Flow::DESCRIPTION] ?? null,
            'updated_at'            => now(),
            'versions.production'   => $versions[Flow::PRODUCTION] ?? null,
        ], fn($v) => $v !== null);

        return $this->firestoreService->updateDocument(self::COLLECTION_FLOWS, $flowDocumentId, $updatePayload);
    }

    /**
     * @param string $flowId
     * @return bool
     */
    public function checkFlowNameExists(string $flowId): bool
    {
        $flows = $this->firestoreService->getDocumentsByCollection(self::COLLECTION_FLOWS, true);

        return $flows->has($flowId);
    }

    /**
     * @param string $flowId
     * @param string $revisionUuid
     * @return bool
     * @throws Exception
     */
    public function setProductionRevision(string $flowId, string $revisionUuid): bool
    {
        $isLegacyRevision = $this->revisionService->revisionIdIsLegacy($revisionUuid);
        $revisionPath = $isLegacyRevision
            ? self::LEGACY_COLLECTION_FLOWS ."/$flowId/". self::LEGACY_COLLECTION_REVISIONS
            : self::COLLECTION_FLOWS ."/$flowId/". self::COLLECTION_REVISIONS;

        $revision = $this->firestoreService->getDocumentByReference($revisionPath, $revisionUuid);

        if (!$revision || ($isLegacyRevision && !$revision['current'])) {
            throw new Exception("The requested Revision cannot be set as a Production flow, as it has no published branch or it could not be found.");
        }
        else {
            $flow = $this->firestoreService->getDocumentByReference(self::COLLECTION_FLOWS, $flowId);
            $versions = $flow['versions'];
            $versions['production'] = $revision['id'];

            return $this->firestoreService->updateDocument(self::COLLECTION_FLOWS, $flowId, [
                'versions' => $versions
            ]);
        }
    }

    /**
     * @param string $flowId
     * @return bool
     */
    public function flowExists(string $flowId): bool
    {
        return !!$this->firestoreService->getDocumentByReference(RevisionRepository::COLLECTION_FLOWS, $flowId);
    }
}
