<?php

namespace App\Repositories\Flows\v2;

use App\Enums\Flows\RevisionType;
use App\Models\Firestore\Flows\Revision;
use App\Services\GoogleFirestoreService;
use Exception;
use Google\Cloud\Firestore\DocumentSnapshot;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Collection;
use Illuminate\Validation\UnauthorizedException;
use Ramsey\Uuid\Uuid;
use App\Repositories\Flows\RevisionRepository as LegacyRevisionRepository;
use function logger;

class RevisionRepository
{
    const COLLECTION_FLOWS = 'v2_flows';
    const COLLECTION_PUBLISHED_REVISIONS = 'revisions_published';
    const COLLECTION_WORKING_REVISIONS = 'revisions_working';
    const COLLECTION_SLIDES = 'slides';

    const UPDATE_MINOR = 'minor';
    const UPDATE_MAJOR = 'major';

    const DEFAULT_SCHEMA = 2.0;

    const SLIDE_ID_MAP = 'slide_ids';

    public function __construct(
        protected GoogleFirestoreService $firestoreService
    ) {}

    /**
     * @param bool|null $withLegacy
     * @return Collection
     */
    public function getRevisionMetadata(?bool $withLegacy = true): Collection
    {
        $flowPath = self::COLLECTION_FLOWS;
        $pathRegex = "/$flowPath\/([^\/]+)\/.*/";
        $environment = config('services.google.firestore.collection_prefix');

        $publishedSnapshot = $this->firestoreService->db->collectionGroup(self::COLLECTION_PUBLISHED_REVISIONS)
            ->where(Revision::ENVIRONMENT, '==', $environment)
            ->documents();
        $workingSnapshot = $this->firestoreService->db->collectionGroup(self::COLLECTION_WORKING_REVISIONS)
            ->where(Revision::ENVIRONMENT, '==', $environment)
            ->documents();


        $flowMeta = $this->firestoreService->getDocumentsByCollection(self::COLLECTION_FLOWS, true);

        $publishedMeta = $this->firestoreService->groupLaravelCollectionByPathRegex($publishedSnapshot, $pathRegex);
        $publishedAndWorkingMeta = $this->firestoreService->groupLaravelCollectionByPathRegex($workingSnapshot, $pathRegex, false, null, $publishedMeta);

        $metadata = $flowMeta->map(function($group, $key) use (&$publishedAndWorkingMeta) {
            $group['revisions'] = $publishedAndWorkingMeta->get($key) ?? collect();
            return $group;
        });

        return $withLegacy
            ? $this->combineLegacyMetadata($metadata, $environment)
            : $metadata;
    }

    /**
     * @param Collection $v2Metadata
     * @param string $environment
     * @return Collection
     */
    public function combineLegacyMetadata(Collection $v2Metadata, string $environment): Collection
    {
        $legacySnapshot = $this->firestoreService->db->collectionGroup(LegacyRevisionRepository::COLLECTION_REVISION_META)
            ->where(Revision::ENVIRONMENT, '==', $environment)
            ->documents();
        $v1Metadata = $this->firestoreService->groupLaravelCollectionByPathRegex($legacySnapshot, "/Flows\/([^\/]+)\/.*/", false);

        return $v2Metadata->map(function(array $group, $key) use ($v1Metadata) {
            $group['revisions']->push(...$v1Metadata->get($key) ?? collect());
            return $group;
        });
    }

    /**
     * @param string $flowId
     * @param string $revisionId
     * @return array|null
     */
    public function getRevisionPayload(string $flowId, string $revisionId, bool $currentVersion = true): ?array
    {
        $revisionPath = $currentVersion
            ? self::COLLECTION_FLOWS . "/$flowId/" . self::COLLECTION_PUBLISHED_REVISIONS
            : self::COLLECTION_FLOWS . "/$flowId/" . self::COLLECTION_WORKING_REVISIONS;

        return $this->firestoreService->getDocumentByReferenceWithSubCollections(
            $revisionPath,
            $revisionId,
            [self::COLLECTION_SLIDES]
        );
    }

    /**
     * @param string $parentFlowId
     * @param array $revisionData
     * @param string|null $uuid
     * @param bool|null $publishRevision
     * @return ?string
     */
    public function create(string $parentFlowId, array $revisionData, ?string $uuid = null, ?bool $publishRevision = false): ?string
    {
        $newUuid = $uuid ?? Uuid::uuid4()->toString();
        $newDocumentId = "v2_$newUuid";

        $revisionPath = $publishRevision
            ? self::COLLECTION_FLOWS . "/$parentFlowId/" . self::COLLECTION_PUBLISHED_REVISIONS
            : self::COLLECTION_FLOWS . "/$parentFlowId/" . self::COLLECTION_WORKING_REVISIONS;

        if (!$revisionData) return false;

        $created = $this->firestoreService->addDocumentToCollection($revisionPath, $newDocumentId, [
            Revision::REVISION_ID       => $newDocumentId,
            Revision::ENVIRONMENT       => config('services.google.firestore.collection_prefix'),
            Revision::DESCRIPTION       => $revisionData[Revision::DESCRIPTION] ?? "",
            Revision::NAME              => $revisionData[Revision::NAME],
            Revision::PARENT_REVISION   => $revisionData[Revision::PARENT_REVISION] ?? null,
            Revision::TYPE              => $revisionData[Revision::TYPE] ?? RevisionType::VARIANT->value,
            Revision::FLOW_DATA         => $revisionData[Revision::FLOW_DATA],
            Revision::CREATED_AT        => now(),
            Revision::UPDATED_AT        => null,
            Revision::ACTIONED_BY       => $revisionData[Revision::ACTIONED_BY] ?? null,
            Revision::VERSION           => $revisionData[Revision::VERSION] ?? "0.1",
            Revision::SCHEMA            => floatVal($revisionData[Revision::SCHEMA] ?? self::DEFAULT_SCHEMA),
            Revision::IS_PUBLISHED      => $publishRevision,
        ]);

        return $created ? $newDocumentId : null;
    }

    /**
     * Save a working copy of the current Flow Revision
     *
     * @param string $flowId
     * @param string $revisionUuid
     * @param array $data
     * @return array|null
     * @throws Exception
     */
    public function save(string $flowId, string $revisionUuid, array $data): ?array
    {
        $workingRevisionPath = self::COLLECTION_FLOWS . "/$flowId/" . self::COLLECTION_WORKING_REVISIONS;
        $transformedData = $this->transformPayloadForStorage($data);
        $this->incrementVersion($transformedData[Revision::REVISION_DATA], self::UPDATE_MINOR);
        $convertFromV1 = $transformedData[Revision::REVISION_DATA][Revision::CONVERT_V1] ?? false;

        if ($convertFromV1) {
            if ($this->checkRevisionAlreadyMigrated($flowId, $transformedData[Revision::REVISION_DATA])) {
                throw new Exception("This Revision has already been migrated to v2.");
            }
            $transformedData[Revision::REVISION_DATA][Revision::PARENT_REVISION] = $revisionUuid;
            $newRevisionUuid = $this->create($flowId, $transformedData[Revision::REVISION_DATA]);
            $targetRevisionUuid = $newRevisionUuid;
        }
        else {
            $currentRevision = $this->firestoreService->getDocumentByReference($workingRevisionPath, $revisionUuid);
            unset($currentRevision[Revision::CONVERT_V1]);

            $targetRevisionUuid = $this->firestoreService->updateDocument($workingRevisionPath, $revisionUuid, $transformedData[Revision::REVISION_DATA])
                ? $revisionUuid
                : null;
        }

        if (!$targetRevisionUuid) {
            throw new Exception("Could not complete the requested Save.");
        }

        $slidePath = $workingRevisionPath . "/$targetRevisionUuid/" . self::COLLECTION_SLIDES;
        $slidesUpdated = $this->firestoreService->updateCollection(
            $slidePath,
            $transformedData[Revision::SLIDE_DATA],
            $transformedData[self::SLIDE_ID_MAP]
        );

        // If this was a v1 => v2 conversion, set the source v1 revision meta to 'history'
        if ($slidesUpdated && $convertFromV1) {
            $legacyRevisionPath = LegacyRevisionRepository::COLLECTION_FLOWS . "/$flowId/" . LegacyRevisionRepository::COLLECTION_REVISION_META;
            $this->firestoreService->updateDocument($legacyRevisionPath, $revisionUuid, [
                Revision::TYPE  => RevisionType::HISTORY->value,
            ]);
        }

        return $slidesUpdated
            ? $this->firestoreService->getDocumentByReference($workingRevisionPath, $targetRevisionUuid)
            : null;
    }

    /**
     * Save a Flow Revision as a new Variant
     *
     * @param string $flowId
     * @param string $sourceRevisionId
     * @param array $data
     * @return array|null
     * @throws Exception
     */
    public function saveAsVariant(string $flowId, string $sourceRevisionId, array $data, ?bool $importV1 = false): ?array
    {
        $transformedData = $this->transformPayloadForStorage($data);
        $sourceRevisionType = $this->firestoreService->getDocumentByReference(self::COLLECTION_FLOWS . "/$flowId/" . self::COLLECTION_WORKING_REVISIONS , $sourceRevisionId)
            ? self::COLLECTION_WORKING_REVISIONS
            : self::COLLECTION_PUBLISHED_REVISIONS;
        $sourceRevisionPath = self::COLLECTION_FLOWS . "/$flowId/" . $sourceRevisionType;
        $targetRevisionPath = self::COLLECTION_FLOWS . "/$flowId/" . self::COLLECTION_WORKING_REVISIONS;

        $transformedData[Revision::REVISION_DATA][Revision::VERSION] = 0.1;
        $transformedData[Revision::REVISION_DATA][Revision::TYPE] = RevisionType::VARIANT->value;

        $newDocumentId = $this->create($flowId, $transformedData[Revision::REVISION_DATA]);

        if (!$newDocumentId) {
            throw new Exception("Could not create New Variant");
        }

        // Clone the original slides into the new Variant revision (skip for v1 as the import contains all slide data)
        if (!$importV1) {
            $this->cloneSlides(
                $sourceRevisionPath . "/$sourceRevisionId",
                $targetRevisionPath . "/$newDocumentId",
            );
        }

        // Update the cloned slides
        $slidesUpdated = $this->firestoreService->updateCollection(
            $targetRevisionPath . "/$newDocumentId/" . self::COLLECTION_SLIDES,
            $transformedData[Revision::SLIDE_DATA],
            $transformedData[self::SLIDE_ID_MAP]
        );

        return $slidesUpdated
            ? $this->firestoreService->getDocumentByReference($targetRevisionPath, $newDocumentId)
            : null;
    }

    /**
     * Publish the current Flow Revision to make it accessible to Client software
     *
     * @param string $flowId
     * @param string $revisionUuid
     * @param array $data
     * @return array|null
     * @throws Exception
     */
    public function publish(string $flowId, string $revisionUuid, array $data): ?array
    {
        $transformedData = $this->transformPayloadForStorage($data);

        $newUuid = Uuid::uuid4()->toString();
        $newRevisionPath = self::COLLECTION_FLOWS . "/$flowId/" . self::COLLECTION_PUBLISHED_REVISIONS;
        $oldRevisionPath = self::COLLECTION_FLOWS . "/$flowId/" . self::COLLECTION_WORKING_REVISIONS;

        $this->incrementVersion($transformedData[Revision::REVISION_DATA], self::UPDATE_MAJOR);
        $transformedData[Revision::REVISION_DATA][Revision::PARENT_REVISION] = $revisionUuid;

        $newRevisionId = $this->create($flowId, $transformedData[Revision::REVISION_DATA], $newUuid, true);

        if (!$newRevisionId) {
            throw new UnauthorizedException("Cannot Publish this Revision.");
        }

        // Update the working revision first.
        $transformedData[Revision::REVISION_DATA][Revision::PARENT_REVISION] = $newRevisionId;
        $oldRevisionUpdated = $this->firestoreService->updateDocument($oldRevisionPath, $revisionUuid, $transformedData[Revision::REVISION_DATA]);
        $slidesUpdated = $this->firestoreService->updateCollection(
            $oldRevisionPath . "/$revisionUuid/" . self::COLLECTION_SLIDES,
            $transformedData[Revision::SLIDE_DATA],
            $transformedData[self::SLIDE_ID_MAP]
        );

        // Clone the updated slides into the Published revision
        $this->cloneSlides(
            $oldRevisionPath . "/$revisionUuid",
            $newRevisionPath . "/$newRevisionId",
        );

        if ($slidesUpdated && $oldRevisionUpdated) {
            $this->markOldRevisionsAsHistory($flowId, $transformedData[Revision::REVISION_DATA][Revision::VERSION]);

            return $this->firestoreService->getDocumentByReference($oldRevisionPath, $revisionUuid);
        }

        else return null;
    }

    /**
     * @param string $sourceFlowId
     * @param string $targetFlowId
     * @param string $sourceRevisionId
     * @param bool $workingRevision
     * @param array $revisionData
     * @return bool
     * @throws Exception
     */
    public function copyRevision(string $sourceFlowId, string $targetFlowId, string $sourceRevisionId, bool $workingRevision, array $revisionData): bool
    {
        $revisionTypePath = $workingRevision
            ? self::COLLECTION_WORKING_REVISIONS
            : self::COLLECTION_PUBLISHED_REVISIONS;
        $sourcePath = self::COLLECTION_FLOWS . "/$sourceFlowId/$revisionTypePath";
        $targetPath = self::COLLECTION_FLOWS . "/$targetFlowId/" . self::COLLECTION_WORKING_REVISIONS;

        $sourceRevision = $this->firestoreService->getDocumentByReference($sourcePath, $sourceRevisionId);

        if (!$sourceRevision) throw new ModelNotFoundException("The requested Revision does not exist");

        $targetRevisionData = [
            ...$sourceRevision,
            ...$revisionData,
            Revision::VERSION   => 0.1,
        ];

        $newRevisionId = $this->create($targetFlowId, $targetRevisionData);
        if (!$newRevisionId) throw new Exception("Failed to copy Revision");

        $slidesCopied = $this->cloneSlides(
            $sourcePath . "/$sourceRevisionId",
            $targetPath . "/$newRevisionId"
        );

        return !!$slidesCopied;
    }

    /**
     * Ensure a new Revision name is unique inside its parent Flow
     * @param string $flowId
     * @param string $name
     * @return bool
     */
    public function newVariantNameIsUnique(string $flowId, string $name): bool
    {
        $revisionPath = self::COLLECTION_FLOWS ."/$flowId/". self::COLLECTION_WORKING_REVISIONS;
        $revisionCollection = $this->firestoreService->getDocumentsByCollection($revisionPath);

        foreach($revisionCollection as $revision) {
            if (trim(strtolower($revision[Revision::NAME])) === trim(strtolower($name))) {
                return false;
            }
        }

        return true;
    }

    /**
     * Clone a Slide subCollection
     *
     * @param string $sourceRevision
     * @param string $targetRevision
     * @return bool
     */
    protected function cloneSlides(string $sourceRevision, string $targetRevision): bool
    {
        $sourceCollection = $sourceRevision . "/" . self::COLLECTION_SLIDES;
        $targetCollection = $targetRevision . "/" . self::COLLECTION_SLIDES;

        return $this->firestoreService->cloneCollection($sourceCollection, $targetCollection);
    }

    /**
     * Transform a Flow Revision payload for Firestore.
     * Compiles a Slide map to handle Deleting redundant slide
     * Stringifies the Slide's Hierarchy
     *
     * @param array $data
     * @return array
     * @throws Exception
     */
    protected function transformPayloadForStorage(array &$data): array
    {
        $revisionData = $data[Revision::REVISION_DATA] ?? null;
        $flowData = $data[Revision::FLOW_DATA] ?? null;
        $slides = $data[Revision::SLIDE_DATA] ?? [];
        $slideData = [];

        if (!$revisionData || !$flowData) throw new Exception("Bad Revision storage request: missing required Flow/Revision data.");

        $slideIds = $this->getIdMapFromFlow($flowData);
        $revisionData[Revision::FLOW_DATA] = $flowData;
        $revisionData[Revision::UPDATED_AT] = now();

        foreach($slides as $slide) {
            $slideData[$slide['id']] = $slide;
        }

        return [
            Revision::REVISION_DATA  => $revisionData,
            Revision::SLIDE_DATA     => $slideData,
            self::SLIDE_ID_MAP       => $slideIds,
        ];
    }

    /**
     * @param array $revisionData
     * @param string $updateType
     * @return void
     */
    protected function incrementVersion(array &$revisionData, string $updateType): void
    {
        $currentVersion = $revisionData[Revision::VERSION];
        $parts = preg_split("/\./", $currentVersion);

        if (count($parts) !== 2) {
            $major = 0;
            $minor = 1;
        }
        else {
            $major = match ($updateType) {
                self::UPDATE_MAJOR => $parts[0] + 1,
                default => $parts[0],
            };
            $minor = match ($updateType) {
                self::UPDATE_MINOR => $parts[1] + 1,
                default => 0,
            };
        }

        $revisionData[Revision::VERSION] = "$major.$minor";
    }

    /**
     *
     * @param array $flowData
     * @return array
     */
    protected function getIdMapFromFlow(array $flowData): array
    {
        $idMap = $flowData['slides'] ?? [];
        array_push($idMap, ...$flowData['templates']);
        $branches = $flowData['branches'] ?? [];

        foreach($branches as $branch) {
            $branchSlides = $branch['slides'] ?? [];
            array_push($idMap, ...$branchSlides);
        }

        return $idMap;
    }

    /**
     * @param string $flowId
     * @param string $currentVersion
     * @return void
     */
    protected function markOldRevisionsAsHistory(string $flowId, string $currentVersion): void
    {
        $revisionPath = self::COLLECTION_FLOWS ."/$flowId/". self::COLLECTION_PUBLISHED_REVISIONS;
        $versionParts = preg_split("/\./", $currentVersion);
        $previousMajorVersion = intval($versionParts[0]) - 1;

        if ($previousMajorVersion < 1) return;

        $findAndSetAsHistory = function(string $revisionPath, int $targetMajorVersion): bool
        {
            $targetVersion = "$targetMajorVersion.0";
            $previousPublishedVersion = $this->firestoreService->getCollectionReference($revisionPath)
                ->where(Revision::VERSION, '==', $targetVersion)
                ->documents();

            /** @var DocumentSnapshot $document */
            foreach ($previousPublishedVersion as $document) {
                if ($document->exists()) {
                    $document->reference()->update([
                        ['path' => Revision::TYPE, 'value' => RevisionType::HISTORY->value],
                    ]);

                    return true;
                }
            }
            return false;
        };

        if ($findAndSetAsHistory($revisionPath, $previousMajorVersion)) return;

        // Try v1 if no v2 previous version was found
        $v1RevisionPath = LegacyRevisionRepository::COLLECTION_FLOWS ."/$flowId/" . LegacyRevisionRepository::COLLECTION_REVISION_META;
        $findAndSetAsHistory($v1RevisionPath, $previousMajorVersion);
    }

    /**
     * Check if a Revision has already been migrated
     * @param string $flowId
     * @param array $revisionMeta
     * @return bool
     */
    protected function checkRevisionAlreadyMigrated(string $flowId, array $revisionMeta): bool
    {
        $revisionPath = self::COLLECTION_FLOWS . "/$flowId/" . self::COLLECTION_WORKING_REVISIONS;

        $existingRevisions = $this->firestoreService->getCollectionReference($revisionPath)
            ->where(Revision::NAME, '==', $revisionMeta[Revision::NAME])
            ->documents();
        foreach($existingRevisions as $document) {
            if ($document->exists()) return true;
        }

        return false;
    }
}
