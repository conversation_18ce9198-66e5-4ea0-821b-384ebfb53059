<?php

namespace App\Repositories;

use Exception;
use Google\Cloud\Logging\Logger;
use Google\Cloud\Logging\LoggingClient;

class LogMonitoringRepository
{
    const LOGGER_MONITORING = 'monitoring';
    const LOGGER_ADVERTISING = 'advertising';
    const LOGGER_APPOINTMENTS = 'appointments';
    const LOGGER_MODEL_EVENTS = 'model_events';
    const LOGGER_SR_LEGACY_API = 'sr_legacy_api';

    const LOGGERS = [
        self::LOGGER_MONITORING,
        self::LOGGER_ADVERTISING,
        self::LOGGER_APPOINTMENTS,
        self::LOGGER_MODEL_EVENTS,
        self::LOGGER_SR_LEGACY_API
    ];

    protected Logger $logger;
    protected LoggingClient $client;

    /**
     * Handles the monitoring.
     *
     * @param LoggingClient $client
     */
    public function __construct(LoggingClient $client)
    {
        $this->client = $client;
        $this->logger = $this->client->logger("monitoring");
    }

    /**
     * @param string $logger
     * @return bool
     * @throws Exception
     */
    public function setLogger(string $logger): bool
    {
        if(!in_array($logger, self::LOGGERS)) {
            throw new Exception(__METHOD__.": Invalid logger $logger");
        }

        $this->logger = $this->client->logger($logger);

        return true;
    }

    /**
     * Records a message.
     *
     * @param string $message
     * @param array $payload
     * @return void
     */
    public function record(string $message, array $payload = []): void
    {
        $entry = $this->logger->entry([
            "message" => $message,
            "data"    => $payload,
        ]);

        $this->logger->write($entry);
    }
}
