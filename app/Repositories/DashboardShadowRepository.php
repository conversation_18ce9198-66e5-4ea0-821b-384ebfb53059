<?php

namespace App\Repositories;

use App\Builders\DashboardShadowBuilder;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentUser;
use Illuminate\Support\Facades\Auth;

class DashboardShadowRepository
{
    /**
     * Returns a token for shadowing for a given company.
     *
     * @param EloquentCompany $company
     * @return string|null
     */
    public function getShadowToken(EloquentCompany $company): ?string
    {
        return DashboardShadowBuilder::query()
            ->forUser($this->getUserToShadow($company)?->userid)
            ->setShadower(Auth::user()->legacy_user_id ?? Auth::id())
            ->setExpiration(config('app.client_api.expiration'))
            ->setSigningKey(config('app.client_api.signing_key'))
            ->getToken();
    }

    /**
     * Returns the url for shadowing.
     *
     * @param EloquentCompany $company
     * @return string
     */
    public function getShadowUrl(EloquentCompany $company): string
    {
        return match ($company->{EloquentCompany::TYPE}) {
            EloquentCompany::TYPE_ROOFER => config('app.dashboard.roofing_url') . "/shadow",
            EloquentCompany::TYPE_PING_POST_AGGREGATOR => config('app.dashboard.aggregator_url') . "/shadow",
            default => config('app.dashboard.url') . "/shadow",
        };
    }

    /**
     * Returns the first active user for a company to shadow.
     *
     * @param EloquentCompany $company
     * @return EloquentUser|null
     */
    protected function getUserToShadow(EloquentCompany $company): ?EloquentUser
    {
        /** @var EloquentUser|null $user */
        $user = $company->users()
            ->where(EloquentUser::STATUS, EloquentUser::FIELD_STATUS_VALUE_ACTIVE)
            ->first();

        return $user;
    }
}
