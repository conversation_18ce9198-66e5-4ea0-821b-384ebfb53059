<?php

namespace App\Repositories;

use App\Builders\ActivityBuilder;
use App\Contracts\Repositories\ActivityFeedRepositoryContract;
use App\Enums\ActivityType;
use App\Models\ActivityFeed;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class ActivityFeedRepository implements ActivityFeedRepositoryContract
{
    /**
     * @inheritDoc
     */
    public function getActivityFeeds(
        ?int          $companyId = null,
        ?int          $userId = null,
        ?string       $searchQuery = null,
        ?ActivityType $type = null,
        ?Carbon       $startDate = null,
        ?Carbon       $endDate = null,
        ?string       $sortByColumn = null,
        ?string       $sortOrder = null,
        bool          $cadence = true,
    ): ?Builder
    {
        /** @var ActivityBuilder|Builder $query */
        $query = ActivityBuilder::query()
            ->setCompanyId($companyId)
            ->setUserId($userId)
            ->setSearchQuery($searchQuery)
            ->setActivityType($type)
            ->setPeriodStartingDate($startDate)
            ->setPeriodEndingDate($endDate)
            ->sortBy($sortByColumn, $sortOrder)
            ->includeCadenceActivity($cadence)
            ->getQuery();
        return $query;
    }

    /**
     * @inheritDoc
     */
    public function findActivityByCompanyIdAndActivityId(int $companyId, int $activityId): ?ActivityFeed
    {
        /** @var ActivityFeed|null $activity */
        $activity = ActivityFeed::query()
            ->where(ActivityFeed::FIELD_COMPANY_ID, $companyId)
            ->where(ActivityFeed::FIELD_ID, $activityId)
            ->whereNull(ActivityFeed::FIELD_DELETED_AT)
            ->first();

        return $activity;
    }

    /**
     * @inheritDoc
     */
    public function getActivityFeedOverview(
        ?int $companyId = null,
        ?int $userId = null,
        ?int $minCallDurationInSeconds = null,
        ?int $minCallDurationInSecondsExclusive = null
    ): array
    {
        return collect(ActivityType::cases())
            ->reduce(function ($overview, ActivityType $type) use (
                $companyId,
                $userId,
                $minCallDurationInSeconds,
                $minCallDurationInSecondsExclusive
            ) {
                /** @var ActivityBuilder $query */
                $query = ActivityBuilder::query()
                    ->setCompanyId($companyId)
                    ->setUserId($userId)
                    ->setActivityType($type !== ActivityType::ALL ? $type : null)
                    ->getQuery();

                $overview[ucfirst($type->value)] = $query->count() ?? 0;
                return $overview;
            });
    }

    /**
     * @param int $itemId
     * @param ActivityType $type
     * @param ?bool $withTrashed
     * @return ?ActivityFeed
     */
    public function findActivityByItemIdAndType(
        int          $itemId,
        ActivityType $type,
        ?bool        $withTrashed = false
    ): ?ActivityFeed
    {
        $base = $withTrashed
            ? ActivityFeed::withTrashed()
            : ActivityFeed::query();
        /** @var ActivityFeed|null */
        return $base
            ->where(ActivityFeed::FIELD_ITEM_ID, $itemId)
            ->where(ActivityFeed::FIELD_ITEM_TYPE, $type->value)
            ->first();
    }

    /**
     * @param int $itemId
     * @param ActivityType $itemType
     * @param int $companyId
     * @param int $userId
     * @param int|null $companyCadenceActionId
     * @param Carbon|null $createdAt
     * @return ?ActivityFeed
     */
    public function createActivity(
        int $itemId,
        ActivityType $itemType,
        int $companyId,
        int $userId,
        ?int $companyCadenceActionId = null,
        ?Carbon $createdAt = null,
    ): ?ActivityFeed
    {
        /** @var ActivityFeed|null */
        return ActivityFeed::query()->create([
            ActivityFeed::FIELD_ITEM_ID                         => $itemId,
            ActivityFeed::FIELD_ITEM_TYPE                       => $itemType->value,
            ActivityFeed::FIELD_COMPANY_ID                      => $companyId,
            ActivityFeed::FIELD_USER_ID                         => $userId,
            ActivityFeed::FIELD_COMPANY_CADENCE_GROUP_ACTION_ID => $companyCadenceActionId,
            ActivityFeed::FIELD_CREATED_AT                      => $createdAt ?? now()
        ]);
    }

    /**
     * @param int $itemId
     * @param ActivityType $itemType
     * @param int $companyId
     * @param int $userId
     * @param int|null $companyCadenceActionId
     * @param Carbon|null $createdAt
     * @return ActivityFeed|null
     */
    public function firstOrCreateByItemAndCompany(
        int $itemId,
        ActivityType $itemType,
        int $companyId,
        int $userId,
        ?int $companyCadenceActionId = null,
        ?Carbon $createdAt = null
    ): ?ActivityFeed
    {
        $activity = ActivityFeed::query()
            ->where(ActivityFeed::TABLE . '.' . ActivityFeed::FIELD_ITEM_ID, $itemId)
            ->where(ActivityFeed::TABLE . '.' . ActivityFeed::FIELD_ITEM_TYPE, $itemType)
            ->where(ActivityFeed::TABLE . '.' . ActivityFeed::FIELD_COMPANY_ID, $companyId)
            ->first();

        return $activity ?? $this->createActivity(
            itemId                : $itemId,
            itemType              : $itemType,
            companyId             : $companyId,
            userId                : $userId,
            companyCadenceActionId: $companyCadenceActionId,
            createdAt             : $createdAt
        );
    }

    public function activityWasUpdated(int $activityId): void
    {
        ActivityFeed::query()
            ->find($activityId)
            ?->update([
                Model::UPDATED_AT => Carbon::now(),
            ]);
    }

}
