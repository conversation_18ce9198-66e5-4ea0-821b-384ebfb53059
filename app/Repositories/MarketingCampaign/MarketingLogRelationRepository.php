<?php

namespace App\Repositories\MarketingCampaign;


use App\Models\MarketingLogRelation;

class MarketingLogRelationRepository
{
    /**
     * @param int $marketingLogId
     * @param string $relationType
     * @param int $relationId
     * @return MarketingLogRelation
     */
    public static function create(
        int $marketingLogId,
        string $relationType,
        int $relationId,
    ): MarketingLogRelation
    {
        return MarketingLogRelation::query()->create([
            MarketingLogRelation::FIELD_MARKETING_LOG_ID => $marketingLogId,
            MarketingLogRelation::FIELD_RELATION_TYPE => $relationType,
            MarketingLogRelation::FIELD_RELATION_ID => $relationId,
        ]);
    }
}
