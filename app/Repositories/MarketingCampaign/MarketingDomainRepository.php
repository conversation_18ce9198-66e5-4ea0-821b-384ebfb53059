<?php

namespace App\Repositories\MarketingCampaign;

use App\Enums\Emails\DomainStatus;
use App\Models\MarketingCampaign;
use App\Models\MarketingCampaignConsumer;
use App\Models\MarketingDomain;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;

class MarketingDomainRepository
{
    /**
     * @param DomainStatus|null $status
     * @param string|null $search
     * @param int|null $maxSentCount
     * @param Carbon|null $maxSentCutoff
     * @param int|null $marketingCampaignId
     * @return Builder
     */
    public function list(
        ?DomainStatus $status = null,
        ?string $search = null,
        ?int $maxSentCount = null,
        ?Carbon $maxSentCutoff = null,
        ?int $marketingCampaignId = null,
        ?array $domains = null,
    ): Builder
    {
        return MarketingDomain::query()
            ->select(
                MarketingDomain::TABLE .'.*',
                DB::raw("COUNT(".MarketingCampaignConsumer::TABLE .'.'. MarketingCampaignConsumer::FIELD_ID.") as sent_count"),
            )
            ->leftJoin(
                MarketingCampaignConsumer::TABLE,
                function (JoinClause $query) use ($maxSentCutoff, $marketingCampaignId) {
                    $query->on(
                        MarketingCampaignConsumer::TABLE.'.'.MarketingCampaignConsumer::FIELD_SENT_FROM_DOMAIN_ID,
                        '=',
                        MarketingDomain::TABLE .'.'. MarketingDomain::FIELD_ID
                    )->when($maxSentCutoff, function (JoinClause $query) use ($maxSentCutoff) {
                        return $query->where(MarketingCampaignConsumer::TABLE . '.' . MarketingCampaignConsumer::FIELD_SENT_AT, '>', $maxSentCutoff);
                    })->when($marketingCampaignId, function (JoinClause $query) use ($marketingCampaignId) {
                        return $query->where(MarketingCampaignConsumer::TABLE . '.' . MarketingCampaignConsumer::FIELD_MARKETING_CAMPAIGN_ID, '=', $marketingCampaignId);
                    });

                }
            )
            ->when($status, fn(Builder $query) => $query->where(MarketingDomain::TABLE .'.'. MarketingDomain::FIELD_STATUS, $status))
            ->when($search, fn(Builder $query) => $query->where(MarketingDomain::TABLE .'.'. MarketingDomain::FIELD_DOMAIN, 'like', '%'.$search.'%'))
            ->when($maxSentCount, fn(Builder $query) => $query->having('sent_count', '<', $maxSentCount))
            ->when(filled($domains), function (Builder $query) use ($domains) {
                $query->whereIn(MarketingDomain::TABLE .'.'. MarketingDomain::FIELD_DOMAIN, $domains);
            })
            ->groupBy(MarketingDomain::TABLE .'.'. MarketingDomain::FIELD_ID);
    }

    /**
     * @param string $name
     * @param DomainStatus $status
     * @return MarketingDomain
     */
    public function create(
        string $name,
        DomainStatus $status,
    ): MarketingDomain
    {
        return MarketingDomain::query()->create([
            MarketingDomain::FIELD_DOMAIN => $name,
            MarketingDomain::FIELD_STATUS => $status,
        ]);
    }

    /**
     * @param string $name
     * @return ?MarketingDomain
     */
    public function findByName(
        string $name,
    ): ?MarketingDomain
    {
        return MarketingDomain::query()
            ->where(MarketingDomain::FIELD_DOMAIN, $name)
            ->first();
    }

}
