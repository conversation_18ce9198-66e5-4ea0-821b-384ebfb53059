<?php

namespace App\Repositories\MarketingCampaign;


use App\DTO\MarketingCampaign\AddUserToEmailCampaignResponse;
use App\Enums\MarketingCampaigns\MarketingCampaignConsumerStatus;
use App\Models\MarketingCampaign;
use App\Models\MarketingCampaignConsumer;
use App\Models\Odin\Consumer;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCancellation;
use App\Models\Odin\ProductRejection;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class MarketingCampaignConsumerRepository
{
    /**
     * @param array $consumerReferences
     * @param MarketingCampaignConsumerStatus $status
     * @return int
     */
    public function batchUpdateMarketingCampaignConsumerStatus(array $consumerReferences, MarketingCampaignConsumerStatus $status): int
    {
        return MarketingCampaignConsumer::query()
            ->whereIn(MarketingCampaignConsumer::FIELD_CONSUMER_REFERENCE, $consumerReferences)
            ->update([MarketingCampaignConsumer::FIELD_STATUS => $status]);
    }

    /**
     * @param Collection $collection
     * @param int $campaignId
     * @return int
     */
    public function updateMarketingCampaignConsumers(Collection $collection, int $campaignId): int
    {
        $mapped = $collection->map(fn(AddUserToEmailCampaignResponse $item) => [
            MarketingCampaignConsumer::FIELD_MARKETING_CAMPAIGN_ID => $campaignId,
            MarketingCampaignConsumer::FIELD_CONSUMER_REFERENCE    => $item->getConsumerReference(),
            MarketingCampaignConsumer::FIELD_EXTERNAL_REFERENCE    => $item->getExternalReference(),
            MarketingCampaignConsumer::FIELD_STATUS                => $item->getStatus(),
        ]);

        return MarketingCampaignConsumer::query()->upsert($mapped->toArray(), [
            MarketingCampaignConsumer::FIELD_MARKETING_CAMPAIGN_ID,
            MarketingCampaignConsumer::FIELD_CONSUMER_REFERENCE
        ]);
    }

    /**
     * @param int $marketingCampaignId
     * @param Collection $consumers
     * @return bool
     */
    public function batchCreateMarketingCampaignConsumers(int $marketingCampaignId, Collection $consumers): bool
    {
        $mapped = $consumers->map(function (Consumer $consumer) use ($marketingCampaignId) {
            return [
                MarketingCampaignConsumer::FIELD_MARKETING_CAMPAIGN_ID        => $marketingCampaignId,
                MarketingCampaignConsumer::FIELD_CONSUMER_REFERENCE           => $consumer->reference,
                MarketingCampaignConsumer::FIELD_EXTERNAL_REFERENCE           => null,
                MarketingCampaignConsumer::FIELD_MARKETING_CAMPAIGN_REFERENCE => Str::uuid()->toString(),
                MarketingCampaignConsumer::FIELD_CREATED_AT                   => now(),
                MarketingCampaignConsumer::FIELD_UPDATED_AT                   => now(),
                MarketingCampaignConsumer::FIELD_STATUS                       => MarketingCampaignConsumerStatus::INITIALISED
            ];
        });

        return MarketingCampaignConsumer::query()->insert($mapped->toArray());
    }

    /**
     * @param MarketingCampaignConsumer $campaignConsumer
     * @param Carbon|null $revalidatedAt
     * @param MarketingCampaignConsumerStatus|null $status
     * @param Carbon|null $clickedAt
     * @param Carbon|null $deliveredAt
     * @param Carbon|null $openedAt
     * @return bool
     */
    public function updateMarketingCampaignConsumer(
        MarketingCampaignConsumer        $campaignConsumer,
        ?MarketingCampaignConsumerStatus $status = null,
        ?Carbon                          $deliveredAt = null,
        ?Carbon                          $openedAt = null,
        ?Carbon                          $clickedAt = null,
        ?Carbon                          $revalidatedAt = null,
    ): bool
    {
        return $campaignConsumer->update([
            MarketingCampaignConsumer::FIELD_STATUS         => $status ?? $campaignConsumer->{MarketingCampaignConsumer::FIELD_STATUS},
            MarketingCampaignConsumer::FIELD_DELIVERED_AT   => $deliveredAt ?? $campaignConsumer->{MarketingCampaignConsumer::FIELD_DELIVERED_AT},
            MarketingCampaignConsumer::FIELD_OPENED_AT      => $openedAt ?? $campaignConsumer->{MarketingCampaignConsumer::FIELD_OPENED_AT},
            MarketingCampaignConsumer::FIELD_CLICKED_AT     => $clickedAt ?? $campaignConsumer->{MarketingCampaignConsumer::FIELD_CLICKED_AT},
            MarketingCampaignConsumer::FIELD_REVALIDATED_AT => $revalidatedAt ?? $campaignConsumer->{MarketingCampaignConsumer::FIELD_REVALIDATED_AT},
        ]);
    }

    public function updateToSent(array $mccIds): int
    {
        return MarketingCampaignConsumer::query()
            ->whereIntegerInRaw(MarketingCampaignConsumer::FIELD_ID, $mccIds)
            ->update([
                MarketingCampaignConsumer::FIELD_STATUS  => MarketingCampaignConsumerStatus::SENT,
                MarketingCampaignConsumer::FIELD_SENT_AT => now(),
            ]);

    }

    public function updateSentFromDomain(array $mccIds, int $domainId): int
    {
        return MarketingCampaignConsumer::query()
            ->whereIntegerInRaw(MarketingCampaignConsumer::FIELD_ID, $mccIds)
            ->update([
                MarketingCampaignConsumer::FIELD_SENT_FROM_DOMAIN_ID  => $domainId,
            ]);
    }

    public function updateStatus(array $mccIds, MarketingCampaignConsumerStatus $status): int
    {
        return MarketingCampaignConsumer::query()
            ->whereIntegerInRaw(MarketingCampaignConsumer::FIELD_ID, $mccIds)
            ->update([
                MarketingCampaignConsumer::FIELD_STATUS => $status,
            ]);
    }

    public function get(int $id): MarketingCampaignConsumer
    {
        return MarketingCampaignConsumer::query()->findOrFail($id);
    }

    /**
     * @param string $marketingCampaignConsumerReference
     * @return MarketingCampaignConsumer
     * @throws ModelNotFoundException
     */
    public function getByMarketingCampaignConsumerReference(string $marketingCampaignConsumerReference): MarketingCampaignConsumer
    {
        /** @var MarketingCampaignConsumer */
        return MarketingCampaignConsumer::query()->where(MarketingCampaignConsumer::FIELD_MARKETING_CAMPAIGN_REFERENCE, $marketingCampaignConsumerReference)->firstOrFail();
    }

    /**
     * Because we can't pin down the exact marketing campaign based on the consumer reference,
     * we try to get any that haven't been revalidated first, then the most recently created
     *
     * @param string $consumerReference
     * @return MarketingCampaignConsumer
     */
    public function getMarketingCampaignConsumerByConsumerReference(string $consumerReference): MarketingCampaignConsumer
    {
        return MarketingCampaignConsumer::query()
            ->where(MarketingCampaignConsumer::FIELD_CONSUMER_REFERENCE, $consumerReference)
            ->orderBy(MarketingCampaignConsumer::FIELD_REVALIDATED_AT)
            ->orderByDesc(MarketingCampaignConsumer::FIELD_CREATED_AT)
            ->firstOrFail();
    }

    /**
     * @param string $consumerReference
     * @param string $externalMarketingReference
     * @return MarketingCampaignConsumer
     */
    public function getByConsumerExternalMarketingReference(string $consumerReference, string $externalMarketingReference): MarketingCampaignConsumer
    {
        return MarketingCampaignConsumer::query()
            ->where(MarketingCampaignConsumer::FIELD_CONSUMER_REFERENCE, '=', $consumerReference)
            ->whereHas(MarketingCampaignConsumer::RELATION_MARKETING_CAMPAIGN, function (Builder $builder) use ($externalMarketingReference) {
                $builder->where(MarketingCampaign::FIELD_EXTERNAL_REFERENCE, '=', $externalMarketingReference);
            })
            ->firstOrFail();
    }

    /**
     * @param int|null $marketingCampaignId
     * @param string|null $consumerName
     * @param MarketingCampaignConsumerStatus|null $status
     * @return Builder
     */
    public function listMarketingCampaignConsumers(
        ?int                             $marketingCampaignId = null,
        ?string                          $consumerName = null,
        ?MarketingCampaignConsumerStatus $status = null,
        ?bool                            $revalidated = null,
    ): Builder
    {
        $subQuery = MarketingCampaignConsumer::query()->select([
            MarketingCampaignConsumer::TABLE . '.' . MarketingCampaignConsumer::FIELD_ID . ' as mcc_id',
            DB::raw('COALESCE(SUM(' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COST . '), 0) as revenue'),
        ])->join(
            ProductAssignment::TABLE,
            ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CONSUMER_PRODUCT_ID,
            MarketingCampaignConsumer::TABLE . '.' . MarketingCampaignConsumer::FIELD_CLONED_CONSUMER_PRODUCT_ID,
        )
            ->leftJoin(ProductRejection::TABLE, function ($join) {
                $join
                    ->on(
                        ProductRejection::TABLE . '.' . ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID,
                        '=',
                        ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID
                    )
                    ->whereNull(ProductRejection::TABLE . '.' . ProductRejection::FIELD_DELETED_AT);
            })->leftJoin(ProductCancellation::TABLE, function ($join) {
                $join
                    ->on(
                        ProductCancellation::TABLE . '.' . ProductCancellation::FIELD_PRODUCT_ASSIGNMENT_ID,
                        '=',
                        ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID
                    )
                    ->whereNull(ProductCancellation::TABLE . '.' . ProductCancellation::FIELD_DELETED_AT);
            })
            ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED, true)
            ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CHARGEABLE, true)
            ->whereNull(ProductRejection::TABLE . '.' . ProductRejection::FIELD_ID)
            ->whereNull(ProductCancellation::TABLE . '.' . ProductCancellation::FIELD_ID)
            ->groupBy(MarketingCampaignConsumer::TABLE . '.' . MarketingCampaignConsumer::FIELD_ID);

        return MarketingCampaignConsumer::query()
            ->leftJoinSub($subQuery, 'sub',
                MarketingCampaignConsumer::TABLE . '.' . MarketingCampaignConsumer::FIELD_ID,
                '=',
                'sub.mcc_id'
            )
            ->when($marketingCampaignId, fn($builder) => $builder->where(MarketingCampaignConsumer::FIELD_MARKETING_CAMPAIGN_ID, $marketingCampaignId))
            ->when($status, fn($builder) => $builder->where(MarketingCampaignConsumer::FIELD_STATUS, $status))
            ->when(filled($revalidated), function ($builder) use ($revalidated) {
                if ($revalidated === true) {
                    $builder->whereNotNull(MarketingCampaignConsumer::FIELD_CLONED_CONSUMER_PRODUCT_ID);
                } else {
                    $builder->whereNull(MarketingCampaignConsumer::FIELD_CLONED_CONSUMER_PRODUCT_ID);
                }
            })
            ->when($consumerName, function (Builder $builder) use ($consumerName) {
                $builder->whereHas(MarketingCampaignConsumer::RELATION_CONSUMER, function (Builder $builder) use ($consumerName) {
                    $builder->where(DB::raw("CONCAT(" . Consumer::FIELD_FIRST_NAME . ", ' ', " . Consumer::FIELD_LAST_NAME . ")"), 'LIKE', '%' . $consumerName . '%');
                });
            });
    }

    /**
     * @param int $marketingCampaignConsumerId
     * @param array|null $relations
     * @return MarketingCampaignConsumer
     */
    public function findOrFail(int $marketingCampaignConsumerId, ?array $relations = null): MarketingCampaignConsumer
    {
        return MarketingCampaignConsumer::query()
            ->when($relations, function (Builder $builder) use ($relations) {
                $builder->with($relations);
            })
            ->findOrFail($marketingCampaignConsumerId);
    }

    /**
     * @param string $marketingCampaignId
     * @param string $consumerReference
     * @param string|null $externalReference
     * @return MarketingCampaignConsumer
     */
    public function createMarketingCampaignConsumer(
        string  $marketingCampaignId,
        string  $consumerReference,
        ?string $externalReference = null,
    ): MarketingCampaignConsumer
    {
        return MarketingCampaignConsumer::query()->create([
            MarketingCampaignConsumer::FIELD_MARKETING_CAMPAIGN_ID        => $marketingCampaignId,
            MarketingCampaignConsumer::FIELD_CONSUMER_REFERENCE           => $consumerReference,
            MarketingCampaignConsumer::FIELD_EXTERNAL_REFERENCE           => $externalReference,
            MarketingCampaignConsumer::FIELD_MARKETING_CAMPAIGN_REFERENCE => Str::uuid()->toString(),
            MarketingCampaignConsumer::FIELD_CREATED_AT                   => now(),
            MarketingCampaignConsumer::FIELD_UPDATED_AT                   => now(),
            MarketingCampaignConsumer::FIELD_REVALIDATED_AT               => null,
        ]);
    }

    /**
     * @param MarketingCampaign $campaign
     * @return int
     */
    public function deleteMarketingCampaignConsumers(MarketingCampaign $campaign): int
    {
        return $campaign->marketingCampaignConsumers()->delete();
    }
}
