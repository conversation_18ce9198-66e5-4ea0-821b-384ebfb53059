<?php

namespace App\Repositories\MarketingCampaign;

use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\MarketingLogRelationType;
use App\Enums\MarketingCampaigns\MarketingLogType;
use App\Models\MarketingLog;
use App\Models\MarketingLogRelation;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class MarketingLogRepository
{
    /**
     * @param string|null $message
     * @param array<LogLevel>|null $levels
     * @param int|null $relationId
     * @param MarketingLogRelationType|null $relationType
     * @return Builder
     */
    public function listLogs(
        ?string $message = null,
        ?array $levels = [],
        ?int $relationId = null,
        ?MarketingLogRelationType $relationType = null,
    ): Builder
    {
        return MarketingLog::query()
            ->with(MarketingLog::RELATION_RELATIONS .'.'. MarketingLogRelation::RELATION_RELATION)
            ->when(filled($levels), function (Builder $builder) use ($levels) {
                $builder->whereIn(MarketingLog::FIELD_LEVEL, $levels);
            })
            ->when(($relationId && $relationType), function (Builder $builder) use ($relationId, $relationType) {
                $builder->whereHas(MarketingLog::RELATION_RELATIONS, function (Builder $builder) use ($relationId, $relationType) {
                    $builder->where(
                        MarketingLogRelation::FIELD_RELATION_ID, '=', $relationId,
                    )->where(
                        MarketingLogRelation::FIELD_RELATION_TYPE, '=', $relationType->toClass(),
                    );
                });
            })
            ->when($message, function (Builder $builder) use ($message) {
                $builder->where(MarketingLog::FIELD_MESSAGE, 'LIKE', '%'.$message.'%');
            })
            ->orderByDesc(MarketingLog::FIELD_ID);
    }

    /**
     * @param string $message
     * @param MarketingLogType $namespace
     * @param LogLevel $level
     * @param string|null $stackTrace
     * @param array|null $context
     * @return MarketingLog
     */
    public static function log(
        string           $message,
        MarketingLogType $namespace,
        LogLevel         $level,
        ?string          $stackTrace = null,
        ?array           $context = null,
    ): MarketingLog
    {
        return MarketingLog::query()
            ->create([
                MarketingLog::FIELD_MESSAGE     => $message,
                MarketingLog::FIELD_NAMESPACE   => $namespace,
                MarketingLog::FIELD_LEVEL       => $level,
                MarketingLog::FIELD_STACK_TRACE => $stackTrace,
                MarketingLog::FIELD_CONTEXT     => $context,
            ]);
    }

    /**
     * @param int $logId
     * @return MarketingLog
     * @throws ModelNotFoundException
     */
    public function getLog(
        int $logId
    ): MarketingLog
    {
        return MarketingLog::query()->findOrFail($logId);
    }

    /**
     * @param Carbon $cutoffDate
     * @return mixed
     */
    public function clearLogs(Carbon $cutoffDate): mixed
    {
        return MarketingLog::query()->where(MarketingLog::FIELD_CREATED_AT, '<', $cutoffDate)->delete();
    }

}
