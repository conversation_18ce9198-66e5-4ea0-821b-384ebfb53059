<?php

namespace App\Repositories;

use App\DataModels\Campaigns\ConsumerProject;
use App\Enums\Odin\QualityTier;
use App\Models\Odin\Address;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAppointment;
use App\Models\Odin\ServiceProduct;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Repositories\Odin\ServiceProductRepository;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use App\Enums\Odin\Product as ProductEnum;

class ProductAppointmentRepository
{

    /**
     * @param ConsumerProject $consumerProject
     * @return bool
     */
    public function appointmentsExistForConsumerProject(ConsumerProject $consumerProject): bool
    {
        $productAppointments = $this->getAllProductAppointmentsForConsumerProject($consumerProject);
        return $productAppointments->count() > 0;
    }

    /**
     * @param ConsumerProject $consumerProject
     * @return bool
     */
    public function inHomeAppointmentsExistForConsumerProject(ConsumerProject $consumerProject): bool
    {
        return $this->getInHomeAppointmentsForConsumerProject($consumerProject)->count() > 0;
    }

    /**
     * @param ConsumerProject $consumerProject
     * @return bool
     */
    public function onlineAppointmentsExistForConsumerProject(ConsumerProject $consumerProject): bool
    {
        return $this->getInOnlineAppointmentsForConsumerProject($consumerProject)->count() > 0;
    }

    /**
     * @param ConsumerProject $consumerProject
     * @return Collection<int, ProductAppointment>
     */
    public function getInHomeAppointmentsForConsumerProject(ConsumerProject $consumerProject): Collection
    {
        return $this->queryAllProductAppointmentsForConsumerProject($consumerProject)
            ->where(ProductAppointment::APPOINTMENT_TYPE, QualityTier::IN_HOME->value)
            ->get();
    }

    /**
     * @param ConsumerProject $consumerProject
     * @return Collection<int, ProductAppointment>
     */
    public function getInOnlineAppointmentsForConsumerProject(ConsumerProject $consumerProject): Collection
    {
        return $this->queryAllProductAppointmentsForConsumerProject($consumerProject)
            ->where(ProductAppointment::APPOINTMENT_TYPE, QualityTier::ONLINE->value)
            ->get();
    }

    /**
     * @param ConsumerProject $consumerProject
     * @return array
     */
    public function createAppointmentConsumerProductsForConsumerProject(ConsumerProject $consumerProject): array
    {
        $appointments = $consumerProject->appointments();
        if (!$appointments->count())
            return [];

        $attemptedAppointmentTimesArray = [];

        /** @var ConsumerProductRepository $consumerProductRepository */
        $consumerProductRepository = app(ConsumerProductRepository::class);
        /** @var ServiceProductRepository $serviceProductRepository */
        $serviceProductRepository = app(ServiceProductRepository::class);

        $leadConsumerProduct = $consumerProject->leadConsumerProduct();
        $now = now();

        $appointmentServiceProductId = $serviceProductRepository->getServiceProductByProductAndIndustryService(
            $leadConsumerProduct->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::FIELD_INDUSTRY_SERVICE_ID},
            ProductEnum::APPOINTMENT
        )?->{ServiceProduct::FIELD_ID};

        $consumerProductAttributes = [
            ...array_filter($leadConsumerProduct->getAttributes(), fn($key) => !in_array($key, $leadConsumerProduct->getGuarded()), ARRAY_FILTER_USE_KEY),
            ConsumerProduct::FIELD_STATUS             => ConsumerProduct::STATUS_PENDING_ALLOCATION,
            ConsumerProduct::FIELD_GOOD_TO_SELL       => true,
            ConsumerProduct::FIELD_SERVICE_PRODUCT_ID => $appointmentServiceProductId,
            ConsumerProduct::FIELD_CONTACT_REQUESTS   => 1,
            ConsumerProduct::CREATED_AT               => $now,
            ConsumerProduct::UPDATED_AT               => null
        ];

        foreach ($appointments as $appointment) {
            $timezoneOffset = $leadConsumerProduct->{ConsumerProduct::RELATION_ADDRESS}->{Address::FIELD_UTC};
            $appointmentTime = Carbon::createFromFormat('Y-m-d H:i:s', $appointment->{ProductAppointment::APPOINTMENT}, $timezoneOffset);

            if ($now->greaterThan($appointmentTime) || in_array($appointmentTime, $attemptedAppointmentTimesArray))
                continue;

            $attemptedAppointmentTimesArray[] = $appointmentTime;

            $newConsumerProduct = $consumerProductRepository->createConsumerProduct($consumerProject->consumer, $consumerProductAttributes);
            $appointment->update([ProductAppointment::CONSUMER_PRODUCT_ID => $newConsumerProduct->id]);
        }

        return $attemptedAppointmentTimesArray;
    }

    /**
     * @param ConsumerProject $consumerProject
     * @return Collection<int, ProductAppointment>
     */
    private function getAllProductAppointmentsForConsumerProject(ConsumerProject $consumerProject): Collection
    {
        return $this->queryAllProductAppointmentsForConsumerProject($consumerProject)->get();
    }

    /**
     * @param ConsumerProject $consumerProject
     * @return Builder
     */
    private function queryAllProductAppointmentsForConsumerProject(ConsumerProject $consumerProject): Builder
    {
        $leadConsumerProduct = $consumerProject->leadConsumerProduct();
        return ProductAppointment::query()->where(ProductAppointment::LEAD_CONSUMER_PRODUCT_ID, $leadConsumerProduct->id);
    }
}
