<?php

namespace App\Repositories;

use App\Models\ConsumerReviews\Review;
use App\Models\ConsumerReviews\ReviewData;
use App\Models\ConsumerReviews\Reviewer;
use Illuminate\Pagination\LengthAwarePaginator;

class CompanyReviewsRepository
{
    /**
     * @param array $filters
     * @return LengthAwarePaginator
     */
    public function getCompanyReviews(array $filters): LengthAwarePaginator
    {
        $query = Review::query();

        if (isset($filters['company_id'])) {
            $query = $query->where(Review::FIELD_COMPANY_ID, '=', $filters['company_id']);
        }

        if (isset($filters['reviewer_details'])) {
            $query->where(function ($query) use ($filters) {
                $query->whereHas(Review::RELATION_REVIEWER, function ($query) use ($filters) {
                    $query->where(Reviewer::FIELD_NAME, 'like', '%' . $filters['reviewer_details'] . '%');
                })->orWhereHas(Review::RELATION_REVIEWER, function ($query) use ($filters) {
                    $query->where(Reviewer::FIELD_EMAIL, 'like', '%' . $filters['reviewer_details'] . '%');
                })->orWhereHas(Review::RELATION_REVIEWER, function ($query) use ($filters) {
                    $query->where(Reviewer::FIELD_PHONE, 'like', '%' . $filters['reviewer_details'] . '%');
                });
            });
        }

        return $query->paginate($filters['perPage'],['*'],'page',$filters['page']);
    }
}
