<?php

namespace App\Repositories;

use App\Models\BaseModel;
use App\Models\ConsumerProductLifecycleTracker;
use App\Services\ConsumerProductLifecycleTrackingService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class ConsumerProductLifecycleTrackingRepository
{
    const int PRUNE_AFTER_DAYS = 90;

    /**
     * @param int $consumerProductId
     * @return ConsumerProductLifecycleTracker|BaseModel|null
     */
    public function getTracker(int $consumerProductId): null|ConsumerProductLifecycleTracker|BaseModel

    {
        return ConsumerProductLifecycleTracker::where(ConsumerProductLifecycleTracker::FIELD_CONSUMER_PRODUCT_ID, $consumerProductId)->first();
    }

    /**
     * @param array $attributes
     * @return ConsumerProductLifecycleTracker|BaseModel
     */
    public function createTracker(array $attributes): ConsumerProductLifecycleTracker|BaseModel
    {
        return ConsumerProductLifecycleTracker::create($attributes);
    }

    /**
     * @param int $consumerProductId
     * @param string $status
     * @param Carbon $updatedAt
     * @return void
     */
    public function appendStatusUpdate(int $consumerProductId, string $status, Carbon $updatedAt): void
    {
        $tracker = $this->findOrCreate($consumerProductId);
        $statuses = $tracker->status_updates ?? [];

        $statuses[] = [
            'updated_at' => $updatedAt->toDateTimeString(),
            'status'     => $status,
        ];

        $tracker->status_updates = $statuses;
        $tracker->save();
    }

    /**
     * @param int $consumerProductId
     * @return ConsumerProductLifecycleTracker
     */
    private function findOrCreate(int $consumerProductId): ConsumerProductLifecycleTracker
    {
        $tracker = $this->getTracker($consumerProductId);
        if(!$tracker){
            // this should always exist at this point - an empty 'consumer_product_created_as' indicates an issue
            $tracker = $this->createTracker([ConsumerProductLifecycleTracker::FIELD_CONSUMER_PRODUCT_ID => $consumerProductId]);
        }
        return $tracker;
    }

    /**
     * @param int $consumerProductId
     * @param Carbon $updatedAt
     * @param string|null $processorName
     * @param string $newQueue
     * @param string|null $reason
     * @param string|null $comment
     * @param bool|null $automatic
     * @return void
     */
    public function appendQueueUpdate(
        int $consumerProductId,
        Carbon $updatedAt,
        ?string $processorName,
        string $newQueue,
        ?string $reason,
        ?string $comment,
        ?bool $automatic
    ): void
    {
        $tracker = $this->findOrCreate($consumerProductId);
        $queues = $tracker->queue_updates ?? [];

        $queues[] = [
            'updated_at' => $updatedAt->toDateTimeString(),
            'queue' => $newQueue,
            'processor' => $processorName,
            'reason' => $reason,
            'comment' => $comment,
            'automatic' => $automatic,
        ];

        $tracker->queue_updates = $queues;
        $tracker->save();
    }

    /**
     * @param int $consumerProductId
     * @param Carbon $updatedAt
     * @return void
     */
    public function addGoodToSellAt(int $consumerProductId, Carbon $updatedAt): void
    {
        $this->findOrCreate($consumerProductId)->update([
            ConsumerProductLifecycleTracker::FIELD_FLAGGED_GOOD_TO_SELL_AT => $updatedAt
        ]);
    }

    /**
     * @param int $consumerProductId
     * @param Carbon $scheduleCalculatedAt
     * @param int|null $delayInSeconds
     * @return void
     */
    public function appendAllocationAttemptSchedule(int $consumerProductId, Carbon $scheduleCalculatedAt, ?int $delayInSeconds): void
    {
        $tracker = $this->findOrCreate($consumerProductId);
        $schedules = $tracker->allocation_attempts_scheduled ?? [];

        $schedules[] = [
            'schedule_calculated_at' => $scheduleCalculatedAt->toDateTimeString(),
            'delay_in_seconds' => $delayInSeconds
        ];

        $tracker->allocation_attempts_scheduled = $schedules;
        $tracker->save();
    }

    /**
     * @param int $consumerProductId
     * @param string $allocationJobUuid
     * @param int $allocationJobAttemptNumber
     * @param array $attemptData
     * @param Carbon $updatedAt
     * @return void
     */
    public function updateAttemptData(int $consumerProductId, string $allocationJobUuid, int $allocationJobAttemptNumber, array $attemptData, Carbon $updatedAt): void
    {
        $tracker = $this->findOrCreate($consumerProductId);
        $allocationAttempts = $tracker->allocation_attempts ?? [];

        $allocationAttempt = current(array_filter($allocationAttempts, fn($allocationAttempt) => $allocationAttempt['job_uuid'] == $allocationJobUuid));
        if(!$allocationAttempt){
            $allocationAttempt = [
                'job_uuid' => $allocationJobUuid,
                'global_started_at' => $updatedAt->toDateTimeString(),
                'global_completed_at' => null,
                'job_attempts' => []
            ];
        }else{
            $allocationAttempts = array_filter($allocationAttempts, fn($allocationAttempt) => $allocationAttempt['job_uuid'] != $allocationJobUuid);
        }

        if($attemptData['conclusion'] !== ConsumerProductLifecycleTrackingService::CONCLUSION_RELEASED_BACK_TO_QUEUE && $attemptData['completed_at']){
            $allocationAttempt['global_completed_at'] = $attemptData['completed_at'];
        }

        $allocationAttempt['job_attempts'][$allocationJobAttemptNumber] = $attemptData;

        $allocationAttempts[] = $allocationAttempt;
        $tracker->allocation_attempts = $allocationAttempts;
        $tracker->save();
    }

    public function pruneOldTrackers(): void
    {
        ConsumerProductLifecycleTracker::query()->whereDate(Model::CREATED_AT, '<', Carbon::now()->subDays(self::PRUNE_AFTER_DAYS))->delete();
    }
}
