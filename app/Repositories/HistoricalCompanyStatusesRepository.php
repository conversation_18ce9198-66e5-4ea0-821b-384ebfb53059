<?php

namespace App\Repositories;

use App\DataModels\HistoricalCompanyDailyStatusesDataModel;
use App\DataModels\HistoricalCompanyMonthlyStatusesDataModel;
use App\Models\Odin\Company;
use App\Models\Odin\HistoricalCompanyStatuses;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\LazyCollection;

class HistoricalCompanyStatusesRepository
{
    /**
     * @param Carbon $date
     * @param bool $lazy
     * @return Collection|LazyCollection
     */
    public function getStatusesForDate(Carbon $date, bool $lazy = false): Collection|LazyCollection
    {
        $query = HistoricalCompanyStatuses::query()
            ->where(HistoricalCompanyStatuses::FIELD_YEAR, $date->year)
            ->selectRaw(implode(',', [
                HistoricalCompanyStatuses::FIELD_COMPANY_ID,
                sprintf(
                    "COALESCE(%s->>'%s', 0) AS %s",
                    HistoricalCompanyStatuses::FIELD_DAILY_STATUSES,
                    sprintf('$."%s"', $date->format(HistoricalCompanyDailyStatusesDataModel::DATE_FORMAT)),
                    HistoricalCompanyStatuses::FIELD_DAILY_STATUSES
                )
            ]))
            ->orderBy(HistoricalCompanyStatuses::FIELD_COMPANY_ID)
            ->distinct();

        return $lazy ? $query->cursor() : $query->get();
    }

    public function recordMonthStatuses(CarbonImmutable $now, int $chunkSize = 1000): bool
    {
        $updateTimestamp = $now->format('Y-m-d H:i:s');

        if($now->endOfMonth()->dayOfYear > Carbon::now('UTC')->dayOfYear) {
            $dateToCheck = $now;
        }
        else {
            $dateToCheck = $now->endOfMonth();
        }

        $dateToSet = $now->format(HistoricalCompanyMonthlyStatusesDataModel::DATE_FORMAT);

        HistoricalCompanyStatuses::query()
            ->where(HistoricalCompanyStatuses::FIELD_YEAR, $now->year)
            ->selectRaw(implode(',', [
                HistoricalCompanyStatuses::FIELD_COMPANY_ID,
                HistoricalCompanyStatuses::FIELD_YEAR,
                HistoricalCompanyStatuses::FIELD_DAILY_STATUSES
            ]))
            ->chunk($chunkSize, function($chunk) use ($updateTimestamp, $now, $dateToCheck, $dateToSet) {
                DB::transaction(function() use ($chunk, $updateTimestamp, $now, $dateToCheck, $dateToSet) {
                    $updateRows = [];

                    foreach($chunk as $historicalCompanyData) {
                        $updateRows[$historicalCompanyData[HistoricalCompanyStatuses::FIELD_COMPANY_ID]] = sprintf(
                            "WHEN %s THEN JSON_SET(%s, '%s', %s)",
                            $historicalCompanyData[HistoricalCompanyStatuses::FIELD_COMPANY_ID],
                            HistoricalCompanyStatuses::FIELD_MONTHLY_STATUSES,
                            sprintf('$."%s"', $dateToSet),
                            $historicalCompanyData[HistoricalCompanyStatuses::FIELD_DAILY_STATUSES]->getDayStatus($dateToCheck) ?? 0
                        );
                    }

                    if(!empty($updateRows)) {
                        $updates = sprintf(
                            "(CASE %s %s END)",
                            HistoricalCompanyStatuses::FIELD_COMPANY_ID,
                            implode(' ', array_values($updateRows))
                        );

                        HistoricalCompanyStatuses::query()
                            ->where(HistoricalCompanyStatuses::FIELD_YEAR, $now->year)
                            ->whereIntegerInRaw(HistoricalCompanyStatuses::FIELD_COMPANY_ID, array_keys($updateRows))
                            ->update([
                                HistoricalCompanyStatuses::FIELD_MONTHLY_STATUSES => DB::raw($updates),
                                HistoricalCompanyStatuses::UPDATED_AT => $updateTimestamp
                            ]);
                    }
                });
            });

        return true;
    }

    public function recordDayStatuses(CarbonImmutable $now, int $chunkSize = 1000): bool
    {
        $year = $now->year;
        $databaseTimestamp = $now->format('Y-m-d H:i:s');

        $missingCompanies = $this->getCompaniesNotInHistoricalCompanyStatuses($year);

        Company::query()
            ->whereNotNull(Company::FIELD_CONSOLIDATED_STATUS)
            ->select([
                Company::FIELD_ID,
                Company::FIELD_CONSOLIDATED_STATUS
            ])
            ->distinct()
            ->chunk($chunkSize, function($chunk) use ($now, $year, $databaseTimestamp, &$missingCompanies) {
                $this->processCompanyStatusesChunk(
                    $chunk,
                    $missingCompanies,
                    $now,
                    $year,
                    $databaseTimestamp
                );
            });

        return true;
    }

    private function processCompanyStatusesChunk(
        Collection $companyStatusesChunk,
        array &$missingCompanies,
        CarbonImmutable $statusDate,
        int $year,
        string $databaseTimestamp
    ): bool
    {
        DB::transaction(function() use ($companyStatusesChunk, $statusDate, $year, $databaseTimestamp, $missingCompanies) {
            $insertRows = [];
            $updateRows = [];

            foreach($companyStatusesChunk as $company) {
                if(in_array($company->{Company::FIELD_ID}, $missingCompanies)) {
                    $hcsdm = new HistoricalCompanyDailyStatusesDataModel();

                    $hcsdm->setDayStatus($statusDate, $company->{Company::FIELD_CONSOLIDATED_STATUS});

                    $insertRows[] = [
                        HistoricalCompanyStatuses::FIELD_COMPANY_ID => $company->{Company::FIELD_ID},
                        HistoricalCompanyStatuses::FIELD_YEAR => $year,
                        HistoricalCompanyStatuses::FIELD_DAILY_STATUSES => $hcsdm->toJson(),
                        HistoricalCompanyStatuses::FIELD_MONTHLY_STATUSES => json_encode([], JSON_FORCE_OBJECT),
                        HistoricalCompanyStatuses::CREATED_AT => $databaseTimestamp
                    ];
                }
                else {
                    $updateRows[$company->{Company::FIELD_ID}] = sprintf(
                        "WHEN %s THEN JSON_SET(%s, '%s', %s)",
                        $company->{Company::FIELD_ID},
                        HistoricalCompanyStatuses::TABLE.'.'.HistoricalCompanyStatuses::FIELD_DAILY_STATUSES,
                        sprintf('$."%s"', $statusDate->format(HistoricalCompanyDailyStatusesDataModel::DATE_FORMAT)),
                        $company->{Company::FIELD_CONSOLIDATED_STATUS}->value
                    );
                }
            }

            if(!empty($insertRows)) {
                HistoricalCompanyStatuses::query()->insert($insertRows);
            }

            if(!empty($updateRows)) {
                $updates = sprintf(
                    "(CASE %s %s END)",
                    HistoricalCompanyStatuses::FIELD_COMPANY_ID,
                    implode(' ', array_values($updateRows))
                );

                HistoricalCompanyStatuses::query()
                    ->where(HistoricalCompanyStatuses::FIELD_YEAR, $year)
                    ->whereIntegerInRaw(HistoricalCompanyStatuses::FIELD_COMPANY_ID, array_keys($updateRows))
                    ->update([
                        HistoricalCompanyStatuses::FIELD_DAILY_STATUSES => DB::raw($updates),
                        HistoricalCompanyStatuses::UPDATED_AT => $databaseTimestamp
                    ]);
            }
        });

        return true;
    }

    public function getCompaniesNotInHistoricalCompanyStatuses(int $year): array
    {
        return Company::query()
            ->leftJoin(HistoricalCompanyStatuses::TABLE, function($join) use ($year) {
                $join
                    ->on(
                        HistoricalCompanyStatuses::TABLE.'.'.HistoricalCompanyStatuses::FIELD_COMPANY_ID,
                        '=',
                        Company::TABLE.'.'.Company::FIELD_ID
                    )
                    ->where(HistoricalCompanyStatuses::TABLE.'.'.HistoricalCompanyStatuses::FIELD_YEAR, $year);
            })
            ->whereNull(HistoricalCompanyStatuses::TABLE.'.'.HistoricalCompanyStatuses::FIELD_ID)
            ->distinct()
            ->select(Company::TABLE.'.'.Company::FIELD_ID)
            ->pluck(Company::FIELD_ID)
            ->toArray();
    }
}
