<?php

namespace App\Repositories;

use App\Models\CompanySlug;
use App\Models\Odin\Company;

class CompanySlugRepository
{
    /**
     * @param  array  $data
     * @return CompanySlug
     */
    public function createCompanySlug(array $data): CompanySlug
    {
        return CompanySlug::query()->create($data);
    }

    /**
     * @param  Company  $company
     * @param  array  $data
     * @return CompanySlug
     */
    public function createCompanySlugForCompany(Company $company, array $data): CompanySlug
    {
        return $company->companySlugs()->create($data);
    }

    /**
     * @param  CompanySlug  $model
     * @param  int  $id
     * @return void
     */
    public function setSlugRedirect(CompanySlug $model, int $id): void
    {
        $model->{CompanySlug::FIELD_REDIRECT_COMPANY_SLUG_ID} = $id;
        $model->save();
    }

    /**
     * @param  CompanySlug  $model
     * @return Company
     */
    public function getCompanyForCompanySlug(CompanySlug $model): Company
    {
        return $model->{CompanySlug::RELATION_COMPANY};
    }

    /**
     * @param  Company  $company
     * @return CompanySlug|null
     */
    public function getActiveCompanySlugForCompany(Company $company): ?CompanySlug
    {
        return $company->{Company::RELATION_ACTIVE_COMPANY_SLUG};
    }

    /**
     * @param  string  $slug
     * @return bool
     */
    public function checkIfSlugExists(string $slug): bool
    {
        return CompanySlug::query()->where(CompanySlug::FIELD_SLUG, $slug)->exists();
    }

    /**
     * @param  Company  $company
     * @param  array  $data
     * @return CompanySlug
     */
    public function createCompanySlugForCompanyQuietly(Company $company, array $data): CompanySlug
    {
        return $company->companySlugs()->create($data);
    }

    /**
     * @param string $slug
     * @return CompanySlug|null
     */
    public function getCompanySlug(string $slug): ?CompanySlug
    {
        return CompanySlug::where(CompanySlug::FIELD_SLUG, $slug)->first();
    }

    public function find(int $id): ?CompanySlug
    {
        return CompanySlug::find($id);
    }
}
