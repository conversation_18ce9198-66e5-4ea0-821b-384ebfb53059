<?php

namespace App\Repositories\CompanyConsumerReviewRepository;

use App\Builders\CompanyConsumerReviewBuilder;
use App\Enums\CompanyConsumerReviewStatus;
use App\Models\ConsumerReviews\Review;
use App\Models\ConsumerReviews\ReviewData;
use App\Models\ConsumerReviews\Reviewer;
use App\Models\ConsumerReviews\ReviewReply;
use App\Models\Odin\Consumer;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
class CompanyConsumerReviewRepository
{
    /**
     * @param int $id
     * @return Review
     */
    public function findOrFail(int $id): Review
    {
        /** @var Review $review */
        $review = Review::query()->findOrFail($id);

        return $review;
    }

    /**
     * @param int $companyId
     * @param $offset
     * @param $limit
     * @return Collection|array
     */
    public function getRecentApprovedConsumerReviews(int $companyId, $offset, $limit): Collection|array
    {
        $query = $this->getBaseQuery();

        $reviews = $query
            ->where(Review::FIELD_COMPANY_ID, $companyId)
            ->where(Review::FIELD_STATUS, CompanyConsumerReviewStatus::APPROVED)
            ->orderBy(Review::FIELD_CREATED_AT , 'DESC')
            ->offset($offset)
            ->limit($limit);

        return $reviews->get();
    }

    /**
     * @param $companyId
     * @return Builder
     */
    public function getApprovedConsumerReviews($companyId): Builder
    {
        $query = $this->getBaseQuery();
        return $query
            ->where(Review::FIELD_COMPANY_ID, $companyId)
            ->where(Review::FIELD_STATUS, CompanyConsumerReviewStatus::APPROVED);
    }

    /**
     * @return Builder
     */
    public function getBaseQuery(): Builder
    {
        return Review::query()
            ->with([
                Review::RELATION_REVIEWER,
                Review::RELATION_REVIEW_DATA,
            ]);
    }

    /**
     * @param int $companyId
     * @param int|null $status
     * @param int|null $score
     * @param $date
     * @param string|null $text
     * @return Builder
     */
    public function getConsumerReviewsByCompanyIdWithSearchFilters(
        int $companyId,
        int|null $status = null,
        int|null $score = null,
        $date = null,
        string|null $text = null,
    ): Builder
    {
        return CompanyConsumerReviewBuilder::query()
                    ->forCompanyId($companyId)
                    ->forStatus($status)
                    ->forScore($score)
                    ->forDate($date)
                    ->forText($text)
                    ->getQuery();
    }

    /**
     * @param int $reviewId
     * @return Builder
     */
    public function getRelatedConsumerReviews(
        int $reviewId,
    ): Builder
    {
        $review = Review::findOrFail($reviewId);

        return CompanyConsumerReviewBuilder::query()
            ->forNotId($reviewId)
            ->forIp($review?->reviewData?->{ReviewData::FIELD_DATA}[ReviewData::DATA_KEY_USER_IP] ?? null)
            ->getQuery();
    }

    /**
     * @param Review $review
     * @return Collection
     */
    public function getConsumerReviewReplies(Review $review): Collection
    {
        return ReviewReply::query()
            ->where(ReviewReply::FIELD_REVIEW_ID, $review->id)
            ->get();
    }


    /**
     * @param int $reviewId
     * @return Collection
     */
    public function getAssociatedConsumers(
        int $reviewId,
    ): Collection
    {
        $review = Review::findOrFail($reviewId);
        $consumerIds = $review?->reviewer?->{Reviewer::FIELD_DATA}[Reviewer::FIELD_DATA_CONSUMERS] ?? null;

        if (!empty($consumerIds)) {
            return Consumer::query()
                ->whereIn(Consumer::FIELD_ID, $consumerIds)
                ->get();
        } else {
            return new Collection();
        }
    }

    /**
     * @param Review $review
     * @return Review
     */
    public function approveReview(Review $review): Review
    {
        $review->{Review::FIELD_STATUS} = Review::STATUS_APPROVED;
        $review->{Review::FIELD_APPROVER_USER_ID} = auth()->id();
        $review->{Review::FIELD_APPROVED_AT} = Carbon::now();
        $review->save();

        return $review;
    }

    /**
     * @param Review $review
     * @return Review
     */
    public function declineReview(Review $review): Review
    {
        $review->{Review::FIELD_STATUS} = Review::STATUS_REJECTED;
        $review->{Review::FIELD_APPROVER_USER_ID} = null;
        $review->{Review::FIELD_APPROVED_AT} = null;
        $review->save();

        return $review;
    }

    /**
     * @param Review $review
     * @return Review
     */
    public function setPendingReview(Review $review): Review
    {
        $review->{Review::FIELD_STATUS} = Review::STATUS_PENDING_APPROVAL;
        $review->{Review::FIELD_APPROVER_USER_ID} = null;
        $review->{Review::FIELD_APPROVED_AT} = null;
        $review->save();

        return $review;
    }

    /**
     * @param Review $review
     * @param string $reply
     * @return ReviewReply
     */
    public function postAdminComment(Review $review, string $reply): ReviewReply
    {
        $reviewReply = new ReviewReply();
        $reviewReply->{ReviewReply::FIELD_REVIEW_ID} = $review->id;
        $reviewReply->{ReviewReply::FIELD_COMMENTS} = $reply;
        $reviewReply->{ReviewReply::FIELD_ADMIN_USER_ID} = auth()->id();
        $reviewReply->save();

        return $reviewReply;
    }

    /**
     * @param Review $review
     * @param string $reply
     * @param int $companyUserId
     * @return ReviewReply
     */
    public function postCompanyUserComment(Review $review, string $reply, int $companyUserId): ReviewReply
    {
        $reviewReply = new ReviewReply();
        $reviewReply->{ReviewReply::FIELD_REVIEW_ID} = $review->id;
        $reviewReply->{ReviewReply::FIELD_COMMENTS} = $reply;
        $reviewReply->{ReviewReply::FIELD_COMPANY_USER_ID} = $companyUserId;
        $reviewReply->save();

        return $reviewReply;
    }

    /**
     * @param int $companyId
     * @return int
     */
    public function getReviewsCount(int $companyId): int
    {
        return Review::where(Review::FIELD_COMPANY_ID, $companyId)
            ->where(Review::FIELD_IS_VERIFIED, 1)
            ->count();

    }

    /**
     * @param int|null $companyId
     * @param string|null $sortBy
     * @param int|null $status
     * @param string|null $score
     * @param string|null $text
     * @param bool|null $verified
     * @return Builder
     */
    public function getAllConsumerReviews(
        ?int $companyId     = null,
        ?string $sortBy     = null,
        ?int $status        = null,
        ?string $score      = null,
        ?string $text       = null,
        ?bool $verified     = null,
    ): Builder
    {
        return CompanyConsumerReviewBuilder::query()
            ->forCompanyId($companyId)
            ->forSortBy($sortBy)
            ->forStatus($status)
            ->forScore($score)
            ->forText($text)
            ->forVerifiedStatus($verified)
            ->getQuery();
    }
}
