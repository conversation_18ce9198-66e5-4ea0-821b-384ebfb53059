<?php

namespace App\Repositories;

use App\DataModels\HistoricalCompanyDailyRevenueDataModel;
use App\DataModels\HistoricalCompanyMonthlyRevenueDataModel;
use App\Models\HistoricalCompanyRevenue;
use App\Models\Odin\Company;
use App\Models\Odin\ProductAssignment;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Illuminate\Support\Facades\DB;

class HistoricalCompanyRevenueRepository
{
    const REVENUE = 'revenue';

    public function recordYearRevenue(CarbonImmutable $now, int $chunkSize = 1000): bool
    {
        $year = $now->year;

        HistoricalCompanyRevenue::query()
            ->where(HistoricalCompanyRevenue::FIELD_YEAR, $year)
            ->select([
                HistoricalCompanyRevenue::FIELD_COMPANY_ID,
                HistoricalCompanyRevenue::FIELD_MONTHLY_REVENUE
            ])
            ->chunk($chunkSize, function($chunk) use ($now) {
                $updates = [];

                foreach($chunk as $companyRow) {
                    $updates[$companyRow[HistoricalCompanyRevenue::FIELD_COMPANY_ID]] = sprintf(
                        "WHEN %s THEN %s",
                        $companyRow[HistoricalCompanyRevenue::FIELD_COMPANY_ID],
                        $companyRow[HistoricalCompanyRevenue::FIELD_MONTHLY_REVENUE]->getRevenueBetween($now->startOfYear(), $now->endOfYear())->sum()
                    );
                }

                $caseQuery = sprintf(
                    "(CASE %s %s END)",
                    HistoricalCompanyRevenue::FIELD_COMPANY_ID,
                    implode(' ', array_values($updates))
                );

                HistoricalCompanyRevenue::query()
                    ->whereIntegerInRaw(HistoricalCompanyRevenue::FIELD_COMPANY_ID, array_keys($updates))
                    ->update([
                        HistoricalCompanyRevenue::FIELD_YEARLY_REVENUE => DB::raw($caseQuery),
                        HistoricalCompanyRevenue::FIELD_UPDATED_AT => $now->format("Y-m-d H:i:s")
                    ]);
            });

        return true;
    }

    public function recordMonthRevenue(CarbonImmutable $now, int $chunkSize = 1000): bool
    {
        $year = $now->year;
        $revenueDate = $now->format(HistoricalCompanyMonthlyRevenueDataModel::DATE_FORMAT);

        HistoricalCompanyRevenue::query()
            ->where(HistoricalCompanyRevenue::FIELD_YEAR, $year)
            ->select([
                HistoricalCompanyRevenue::FIELD_COMPANY_ID,
                HistoricalCompanyRevenue::FIELD_DAILY_REVENUE
            ])
            ->chunk($chunkSize, function($chunk) use ($now, $revenueDate) {
                $updates = [];

                foreach($chunk as $companyRow) {
                    $updates[$companyRow[HistoricalCompanyRevenue::FIELD_COMPANY_ID]] = sprintf(
                        "WHEN %s THEN JSON_SET(%s, '%s', %s)",
                        $companyRow[HistoricalCompanyRevenue::FIELD_COMPANY_ID],
                        HistoricalCompanyRevenue::FIELD_MONTHLY_REVENUE,
                        sprintf(
                            '$."%s"',
                            $revenueDate
                        ),
                        $companyRow[HistoricalCompanyRevenue::FIELD_DAILY_REVENUE]->getRevenueBetween($now->startOfMonth(), $now->endOfMonth())->sum()
                    );
                }

                $caseQuery = sprintf(
                    "(CASE %s %s END)",
                    HistoricalCompanyRevenue::FIELD_COMPANY_ID,
                    implode(' ', array_values($updates))
                );

                HistoricalCompanyRevenue::query()
                    ->whereIntegerInRaw(HistoricalCompanyRevenue::FIELD_COMPANY_ID, array_keys($updates))
                    ->update([
                        HistoricalCompanyRevenue::FIELD_MONTHLY_REVENUE => DB::raw($caseQuery),
                        HistoricalCompanyRevenue::FIELD_UPDATED_AT => $now->format("Y-m-d H:i:s")
                    ]);
            });

        return true;
    }

    public function recordDayRevenue(CarbonImmutable $now, int $chunkSize = 1000): bool
    {
        $today = $now->startOfDay();
        $tomorrow = $today->addDay();
        $year = $today->year;
        $nowFormatted = $now->format("Y-m-d H:i:s");

        $productAssignmentCostCol = ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_COST;

        $missingCompanies = $this->getCompaniesNotInHistoricalCompanyRevenue($year);

        ProductAssignment::query()
            ->whereDoesntHave(ProductAssignment::RELATION_PRODUCT_REJECTIONS)
            ->whereDoesntHave(ProductAssignment::RELATION_PRODUCT_CANCELLATIONS)
            ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_DELIVERED_AT, '>=', $today)
            ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_DELIVERED_AT, '<', $tomorrow)
            ->groupBy(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_COMPANY_ID)
            ->selectRaw(implode(',', [
                ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_COMPANY_ID,
                "SUM({$productAssignmentCostCol}) AS ".self::REVENUE
            ]))
            ->distinct()
            ->chunk($chunkSize, function($chunk) use (&$missingCompanies, $now, $year, $nowFormatted) {
                $this->processCompaniesChunk(
                    $chunk->toArray(),
                    $missingCompanies,
                    $now,
                    $year,
                    $nowFormatted
                );
            });

        $missingCompanies = $this->getCompaniesNotInHistoricalCompanyRevenue($year);

        $notUpdatedCompanies = HistoricalCompanyRevenue::query()
            ->whereNull(
                sprintf(
                    "%s->%s",
                    HistoricalCompanyRevenue::FIELD_DAILY_REVENUE,
                    $now->format(HistoricalCompanyDailyRevenueDataModel::DATE_FORMAT)
                )
            )
            ->select(HistoricalCompanyRevenue::FIELD_COMPANY_ID)
            ->pluck(HistoricalCompanyRevenue::FIELD_COMPANY_ID)
            ->toArray();

        $remainingCompanyIds = array_merge($notUpdatedCompanies, $missingCompanies);
        asort($remainingCompanyIds);

        foreach(array_chunk($remainingCompanyIds, $chunkSize) as $companyIds) {
            $this->processCompaniesChunk(
                array_map(fn($cid) => [ProductAssignment::FIELD_COMPANY_ID => $cid, self::REVENUE => 0], $companyIds),
                $missingCompanies,
                $now,
                $year,
                $nowFormatted
            );
        }

        return true;
    }

    /**
     * @param array $companiesChunk
     * @param array $missingCompanies
     * @param Carbon|CarbonImmutable $revenueDate
     * @param int $year
     * @param string $nowFormatted
     * @return bool
     */
    private function processCompaniesChunk(
        array $companiesChunk,
        array &$missingCompanies,
        Carbon|CarbonImmutable $revenueDate,
        int $year,
        string $nowFormatted
    ): bool
    {
        DB::transaction(function() use ($missingCompanies, $revenueDate, $year, $nowFormatted, $companiesChunk) {
            $insertRows = [];
            $updateRows = [];

            $historicalCompanyDailyRevenueCol = HistoricalCompanyRevenue::TABLE.'.'.HistoricalCompanyRevenue::FIELD_DAILY_REVENUE;

            foreach($companiesChunk as $companyData) {
                if(in_array($companyData[ProductAssignment::FIELD_COMPANY_ID], $missingCompanies)) {
                    $hcrdrdm = new HistoricalCompanyDailyRevenueDataModel();

                    $hcrdrdm->setDayRevenue($revenueDate, $companyData[self::REVENUE]);

                    $insertRows[] = [
                        HistoricalCompanyRevenue::FIELD_COMPANY_ID => $companyData[ProductAssignment::FIELD_COMPANY_ID],
                        HistoricalCompanyRevenue::FIELD_YEAR => $year,
                        HistoricalCompanyRevenue::FIELD_DAILY_REVENUE => $hcrdrdm->toJson(),
                        HistoricalCompanyRevenue::FIELD_MONTHLY_REVENUE => json_encode([], JSON_FORCE_OBJECT), //mysql needs defaults for json columns
                        HistoricalCompanyRevenue::FIELD_CREATED_AT => $nowFormatted,
                        HistoricalCompanyRevenue::FIELD_UPDATED_AT => $nowFormatted
                    ];
                }
                else {
                    $updateRows[$companyData[ProductAssignment::FIELD_COMPANY_ID]] = sprintf(
                        "WHEN %s THEN JSON_SET(%s, '%s', %s)",
                        $companyData[ProductAssignment::FIELD_COMPANY_ID],
                        $historicalCompanyDailyRevenueCol,
                        sprintf(
                            '$."%s"',
                            $revenueDate->format(HistoricalCompanyDailyRevenueDataModel::DATE_FORMAT)
                        ),
                        $companyData[self::REVENUE]
                    );
                }
            }

            if(!empty($insertRows)) {
                HistoricalCompanyRevenue::query()->insert($insertRows);
            }

            if(!empty($updateRows)) {
                $caseQuery = sprintf(
                    "(CASE %s %s END)",
                    HistoricalCompanyRevenue::FIELD_COMPANY_ID,
                    implode(' ', $updateRows)
                );

                HistoricalCompanyRevenue::query()
                    ->whereIn(HistoricalCompanyRevenue::FIELD_COMPANY_ID, array_keys($updateRows))
                    ->update([
                        HistoricalCompanyRevenue::FIELD_DAILY_REVENUE => DB::raw($caseQuery),
                        HistoricalCompanyRevenue::UPDATED_AT => $nowFormatted
                    ]);
            }
        });

        return true;
    }

    public function getCompaniesNotInHistoricalCompanyRevenue(int $year): array
    {
        return Company::query()
            ->leftJoin(HistoricalCompanyRevenue::TABLE, function($join) use ($year) {
                $join
                    ->on(
                        HistoricalCompanyRevenue::TABLE.'.'.HistoricalCompanyRevenue::FIELD_COMPANY_ID,
                        '=',
                        Company::TABLE.'.'.Company::FIELD_ID
                    )
                    ->where(HistoricalCompanyRevenue::TABLE.'.'.HistoricalCompanyRevenue::FIELD_YEAR, $year);
            })
            ->whereNull(HistoricalCompanyRevenue::TABLE.'.'.HistoricalCompanyRevenue::FIELD_ID)
            ->distinct()
            ->select(Company::TABLE.'.'.Company::FIELD_ID)
            ->pluck(Company::FIELD_ID)
            ->toArray();
    }
}
