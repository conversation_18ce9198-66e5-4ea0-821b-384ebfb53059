<?php

namespace App\Repositories;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\CustomCampaignCountyFloorPrice;
use App\Models\Campaigns\CustomCampaignStateFloorPrice;
use App\Models\FloorPriceActivityLog;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class CompanyCampaignCustomPricingLogRepository
{
    const string CUSTOM_CAMPAIGN_STATE_FLOOR_PRICE = 'custom_campaign_state_floor_price';
    const string CUSTOM_CAMPAIGN_COUNTY_FLOOR_PRICE = 'custom_campaign_county_floor_price';

    /**
     * @param string|null $campaign
     * @param int|null $companyId
     * @return Builder
     */
    public function getCustomStatePricingLogs(
        ?string $campaign = null,
        ?int    $companyId = null,
    ): Builder
    {
        return FloorPriceActivityLog::query()
            ->where(FloorPriceActivityLog::FIELD_LOG_NAME, self::CUSTOM_CAMPAIGN_STATE_FLOOR_PRICE)
            ->whereHasMorph(FloorPriceActivityLog::RELATION_SUBJECT, CustomCampaignStateFloorPrice::class, function ($query) use (
                $campaign,
                $companyId
            ) {
                $query->whereHas(CustomCampaignStateFloorPrice::RELATION_COMPANY_CAMPAIGN, function ($query) use ($campaign, $companyId) {
                    if (isset($campaign)) {
                        $query->whereAny([CompanyCampaign::FIELD_ID, CompanyCampaign::FIELD_NAME], 'LIKE', '%' . $campaign . '%');
                    }

                    if (isset($companyId)) {
                        $query->where(CompanyCampaign::FIELD_COMPANY_ID, $companyId);
                    }
                });
            });
    }

    /**
     * @param string|null $campaign
     * @param int|null $companyId
     * @return Builder
     */
    public function getCustomCountyPricingLogs(
        ?string $campaign = null,
        ?int    $companyId = null,
    ): Builder
    {
        return FloorPriceActivityLog::query()
            ->where(FloorPriceActivityLog::FIELD_LOG_NAME, self::CUSTOM_CAMPAIGN_COUNTY_FLOOR_PRICE)
            ->whereHasMorph(FloorPriceActivityLog::RELATION_SUBJECT, CustomCampaignCountyFloorPrice::class, function ($query) use (
                $campaign,
                $companyId
            ) {
                $query->whereHas(CustomCampaignCountyFloorPrice::RELATION_COMPANY_CAMPAIGN, function ($query) use ($campaign, $companyId) {
                    if (isset($campaign)) {
                        $query->whereAny([CompanyCampaign::FIELD_ID, CompanyCampaign::FIELD_NAME], 'LIKE', '%' . $campaign . '%');
                    }

                    if (isset($companyId)) {
                        $query->where(CompanyCampaign::FIELD_COMPANY_ID, $companyId);
                    }
                });
            });
    }
}
