<?php

namespace App\Repositories;

use App\Enums\RoundRobinType;
use App\Enums\TargetRelation;
use App\Models\AccountManager;
use App\Models\Hunter;
use App\Models\SupportOfficer;
use App\Models\User;
use App\Repositories\Legacy\CompanySuccessManagerRepository;
use App\Repositories\Territory\RelationshipManagerRepository;
use App\Services\RoundRobins\RoundRobinService;
use App\Workflows\WorkflowPayload;
use Illuminate\Support\Collection;
use App\Contracts\Workflows\HasTargetContract;

class TargetRepository
{
    public function __construct(
        protected CompanyAccountManagerRepository $accountManagerRepository,
        protected CompanySuccessManagerRepository $successManagerRepository,
        protected RelationshipManagerRepository   $relationshipManagerRepository,
    ) {}

    /**
     * @param HasTargetContract $target
     * @return Collection<User>
     */
    public function getTargets(HasTargetContract $target, ?WorkflowPayload $payload = null): Collection
    {
        switch($target->getRelationType())
        {
            case TargetRelation::COMPANY_ACCOUNT_MANAGER:
                return $this->accountManagerRepository->getAccountManager($target->getTarget());

            case TargetRelation::COMPANY_RELATIONSHIP_MANAGER:
                return $this->relationshipManagerRepository->getRelationshipManagerByCompanyReference($target->getTarget());

            case TargetRelation::COMPANY_SUCCESS_MANAGER:
                return $this->successManagerRepository->getSuccessManager($target->getTarget());

            case TargetRelation::SUPPORT_OFFICER_ROUND_ROBIN:
                /** @var RoundRobinService $roundRobinService */
                $roundRobinService = app()->make(RoundRobinService::class);

                return collect([SupportOfficer::query()->find($roundRobinService->executeRoundRobin(RoundRobinType::SUPPORT_OFFICER))?->user]);

            case TargetRelation::HUNTER_ROUND_ROBIN:
                /** @var RoundRobinService $roundRobinService */
                $roundRobinService = app()->make(RoundRobinService::class);

                return collect([Hunter::query()->find($roundRobinService->setWorkflowPayload($payload)->executeRoundRobin(RoundRobinType::HUNTER))?->user]);

            case TargetRelation::USER_ACTIONING:
                return User::query()->where('id', $payload->get('user_actioning'))->get();

            case TargetRelation::SALES_TOOL_ROUND_ROBIN:
                /** @var RoundRobinService $roundRobinService */
                $roundRobinService = app()->make(RoundRobinService::class);

                return collect([AccountManager::query()->find($roundRobinService->executeRoundRobin(RoundRobinType::SALES_TOOL))?->user]);

            case TargetRelation::SALES_MANAGER:
                return User::query()
                    ->whereHas('roles', fn($query) => $query->where('name', TargetRelation::SALES_MANAGER))
                    ->get();

        }
    }
}
