<?php

namespace App\Repositories;

use App\Models\Legacy\Location;
use App\Models\Odin\CompanyZipCodeException;
use Illuminate\Contracts\Container\BindingResolutionException;

class CompanyZipCodeExceptionRepository
{
    /**
     * @param int $companyId
     * @param string $stateAbbr
     * @param array $countyKeys
     * @return void
     * @throws BindingResolutionException
     */
    public function addCounties(int $companyId, string $stateAbbr, array $countyKeys): void
    {
        /** @var LocationRepository $locationRepository */
        $locationRepository = app()->make(LocationRepository::class);
        $counties = $locationRepository->getCountiesInStatesList([$stateAbbr]);
        $inserts = [];
        foreach ($countyKeys as $countyKey){
            $inserts[] = [
                CompanyZipCodeException::FIELD_COUNTY_LOCATION_ID => $counties->where(Location::COUNTY_KEY, $countyKey)->first()->id,
                CompanyZipCodeException::FIELD_COMPANY_ID => $companyId
            ];
        }
        CompanyZipCodeException::query()
            ->insert($inserts);
    }

    /**
     * @param int $companyId
     * @param string $stateAbbr
     * @param array $countyKeys
     * @return void
     */
    public function removeCounties(int $companyId, string $stateAbbr, array $countyKeys): void
    {
        CompanyZipCodeException::query()
            ->where(CompanyZipCodeException::FIELD_COMPANY_ID, $companyId)
            ->leftJoin(Location::TABLE, CompanyZipCodeException::FIELD_COUNTY_LOCATION_ID, '=', Location::TABLE.'.'.Location::ID)
            ->where(Location::TABLE.'.'.Location::STATE_ABBREVIATION, $stateAbbr)
            ->whereIn(Location::TABLE.'.'.Location::COUNTY_KEY, $countyKeys)
            ->delete();
    }
}