<?php

namespace App\Repositories;

use App\Contracts\Repositories\CompanyAccountManagerContract;
use App\Enums\ActivityLog\ActivityLogDescription;
use App\Enums\ActivityLog\ActivityLogName;
use App\Models\AccountManager;
use App\Models\AccountManagerClient;
use App\Models\Odin\Company;
use App\Models\User;
use App\Repositories\ActivityLog\ActivityLogRepository;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class CompanyAccountManagerRepository implements CompanyAccountManagerContract
{
    /**
     * @param string $companyReference
     * @return Collection|null
     */
    public function getAccountManager(string $companyReference): ?Collection
    {
        /** @var AccountManagerClient $accountManagerClient */
        $accountManagerClient = AccountManagerClient::query()
                                                    ->where(AccountManagerClient::FIELD_COMPANY_REFERENCE, $companyReference)
                                                    ->where(AccountManagerClient::FIELD_STATUS, AccountManagerClient::STATUS_ACTIVE)
                                                    ->first();

        if (!$accountManagerClient)
            return collect();

        /** @var AccountManager $accountManager */
        $accountManager = AccountManager::query()
                                        ->find($accountManagerClient->{AccountManagerClient::FIELD_ACCOUNT_MANAGER_ID});

        if (!$accountManager)
            return collect();

        return User::query()->where(User::FIELD_ID, $accountManager->{AccountManager::FIELD_USER_ID})->get();
    }

    /**
     * Handles assigning an account manager to a company.
     *
     * @param int $companyId
     * @param int|null $accountManagerId
     * @param ?bool $unassignManager
     * @return AccountManagerClient|null
     */
    public function assignAccountManager(int $companyId, ?int $accountManagerId, ?bool $unassignManager = false): ?AccountManagerClient
    {
        $activityLogRepository = app(ActivityLogRepository::class);

        /** @var Company|null $company */
        $company = Company::query()->find($companyId);
        if (!$company || (!$accountManagerId && !$unassignManager))
            return null;

        $existingAccountManagerClient = AccountManagerClient::query()
                            ->where(AccountManagerClient::FIELD_COMPANY_REFERENCE, $company->reference)
                            ->where(AccountManagerClient::FIELD_STATUS, AccountManagerClient::STATUS_ACTIVE)
                            ->first();
        if ($existingAccountManagerClient) {
            $existingAccountManagerClient->status = AccountManagerClient::STATUS_INACTIVE;
            $existingAccountManagerClient->save();
        }

        $activityLogRepository->createActivityLog(
            logName    : ActivityLogName::COMPANY_ACCOUNT_MANAGER_CHANGE->value,
            description: ActivityLogDescription::USER_UPDATED->value,
            subjectType: $company::class,
            subjectId  : $company->id,
            properties : [
                'from_id' => $existingAccountManagerClient?->account_manager_id ?? null,
                'to_id'   => $accountManagerId ?? null,
            ],
        );

        if ($unassignManager) return null;

        /** @var AccountManagerClient */
        return AccountManagerClient::query()
            ->create(
                [
                    AccountManagerClient::FIELD_COMPANY_REFERENCE => $company->reference,
                    AccountManagerClient::FIELD_ACCOUNT_MANAGER_ID => $accountManagerId,
                    AccountManagerClient::FIELD_STATUS => AccountManagerClient::STATUS_ACTIVE,
                ],
            );
    }

    /**
     * @param array $relationships
     * @return Collection<int, AccountManager>
     */
    public function getAllAccountManagers(array $relationships): Collection
    {
        return AccountManager::query()->with($relationships)->get();
    }

    /**
     * Get the most recently deactivated AM for a company. If updated_at values have been homogenised by old logic,
     *   go by created_at
     * @param int $companyId
     * @return AccountManager|null
     */
    public function getPreviousAccountManagerClient(int $companyId): ?AccountManagerClient
    {
        $company = Company::query()->findOrFail($companyId);

        /** @var Collection<AccountManagerClient> $accountManagerClients */
        $accountManagerClients = AccountManagerClient::query()
            ->where(AccountManagerClient::FIELD_COMPANY_REFERENCE, $company->{Company::FIELD_REFERENCE})
            ->where(AccountManagerClient::FIELD_STATUS, AccountManagerClient::STATUS_INACTIVE)
            ->latest(Model::UPDATED_AT)
            ->get();
        return $accountManagerClients->count() > 1 && $accountManagerClients->get(0)->{Model::UPDATED_AT} === $accountManagerClients->get(1)->{Model::UPDATED_AT}
            ? $accountManagerClients->sortByDesc(Model::CREATED_AT)->first()
            : $accountManagerClients->first();
    }
}
