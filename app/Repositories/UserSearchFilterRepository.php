<?php

namespace App\Repositories;

use App\Models\UserSearchFilters;

class UserSearchFilterRepository
{

    /**
     * @param  int  $userId
     * @param  string  $category
     * @return UserSearchFilters|null
     */
    public function find(int $userId, string $category): ?UserSearchFilters
    {
        /** @var UserSearchFilters $result */
        $result = UserSearchFilters::query()
            ->where(UserSearchFilters::FIELD_USER_ID, '=', $userId)
            ->where(UserSearchFilters::FIELD_CATEGORY, '=', $category)
            ->first();

        return $result;
    }

    /**
     * @param  int  $userId
     * @param  string  $category
     * @param  array  $data
     * @return UserSearchFilters
     */
    public function createOrUpdate(int $userId, string $category, array $data = []): UserSearchFilters
    {
        /** @var UserSearchFilters $userFilterSet */
        $userFilterSet = UserSearchFilters::firstOrCreate([
            UserSearchFilters::FIELD_USER_ID => $userId,
            UserSearchFilters::FIELD_CATEGORY => $category,
        ]);

        $userFilterSet->update([
            UserSearchFilters::FIELD_DATA => json_encode($data)
        ]);

        return $userFilterSet;
    }


}
