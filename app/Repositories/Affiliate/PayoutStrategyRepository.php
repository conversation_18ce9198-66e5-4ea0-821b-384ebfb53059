<?php

namespace App\Repositories\Affiliate;

use App\Enums\Affiliate\PayoutStrategyTypeEnum;
use App\Models\Affiliates\PayoutStrategy;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class PayoutStrategyRepository
{
    /**
     * @param int $affiliateId
     * @param PayoutStrategyTypeEnum $type
     * @param int $value
     * @param Carbon $activeFrom
     * @param int|null $authorId
     * @param Carbon|null $activeTo
     * @return PayoutStrategy
     */
    public function create(
        int                    $affiliateId,
        PayoutStrategyTypeEnum $type,
        int                    $value,
        Carbon                 $activeFrom,
        ?int                   $authorId = null,
        ?Carbon                $activeTo = null
    ): PayoutStrategy
    {
        return PayoutStrategy::query()->create([
            PayoutStrategy::FIELD_AFFILIATE_ID => $affiliateId,
            PayoutStrategy::FIELD_TYPE => $type,
            PayoutStrategy::FIELD_VALUE => $value,
            PayoutStrategy::FIELD_ACTIVE_FROM => $activeFrom,
            PayoutStrategy::FIELD_ACTIVE_TO => $activeTo,
            PayoutStrategy::FIELD_AUTHOR_ID => $authorId,
        ]);
    }

    /**
     * @param PayoutStrategy $strategy
     * @param Carbon|null $activeTo
     * @return bool
     */
    public function update(
        PayoutStrategy $strategy,
        ?Carbon        $activeTo = null,
    ): bool
    {
        return $strategy->update([
            PayoutStrategy::FIELD_ACTIVE_TO => $activeTo ?? $strategy->active_to,
        ]);
    }

    /**
     * @param int $affiliateId
     * @return ?PayoutStrategy
     */
    public function getActiveByAffiliateId(int $affiliateId): ?PayoutStrategy
    {
        return PayoutStrategy::query()
            ->where(PayoutStrategy::FIELD_AFFILIATE_ID, $affiliateId)
            ->where(function ($query) {
                $query->whereNull(PayoutStrategy::FIELD_ACTIVE_TO)
                    ->orWhere(PayoutStrategy::FIELD_ACTIVE_TO, '>', now());
            })
            ->first();
    }

}
