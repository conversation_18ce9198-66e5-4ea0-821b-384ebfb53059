<?php

namespace App\Repositories\Affiliate;

use App\Models\Affiliates\Payout;

class PayoutRepository
{
    public function updateOrCreate(
        int $atomicValue,
        int $payoutStrategyId,
        int $affiliateId,
        int $consumerProductId,
    ): Payout
    {
        return Payout::query()->updateOrCreate(
            [
                Payout::FIELD_AFFILIATE_ID => $affiliateId,
                Payout::FIELD_PAYOUT_STRATEGY_ID => $payoutStrategyId,
                Payout::FIELD_CONSUMER_PRODUCT_ID => $consumerProductId,
            ],
            [
                Payout::FIELD_CENT_VALUE => $atomicValue
            ]
        );
    }

    public function upsert(array $data): int
    {
        return Payout::query()->upsert(
            $data,
            [Payout::FIELD_CONSUMER_PRODUCT_ID, Payout::FIELD_AFFILIATE_ID, Payout::FIELD_PAYOUT_STRATEGY_ID],
            [Payout::FIELD_CENT_VALUE]
        );
    }

}
