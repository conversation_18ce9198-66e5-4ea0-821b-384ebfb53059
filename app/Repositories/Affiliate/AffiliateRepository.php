<?php

namespace App\Repositories\Affiliate;

use App\Models\Affiliates\Affiliate;
use Ramsey\Uuid\Uuid;

class AffiliateRepository
{
    /**
     * @param string $name
     * @return Affiliate
     */
    public function create(
        string $name,
    ): Affiliate
    {
        return Affiliate::query()->create([
            Affiliate::FIELD_NAME => $name,
            Affiliate::FIELD_UUID => Uuid::uuid4()->toString()
        ]);
    }

    /**
     * @param Affiliate $affiliate
     * @param string|null $name
     * @return bool
     */
    public function update(
        Affiliate $affiliate,
        ?string $name = null,
    ): bool
    {
        return $affiliate->update([
            Affiliate::FIELD_NAME => $name ?? $affiliate->{Affiliate::FIELD_NAME},
        ]);
    }

}
