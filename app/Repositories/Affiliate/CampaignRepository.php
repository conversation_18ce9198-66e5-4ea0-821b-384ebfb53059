<?php

namespace App\Repositories\Affiliate;

use App\Models\Affiliates\Campaign;

class CampaignRepository
{
    /**
     * @param Campaign $campaign
     * @param string|null $name
     * @param int|null $accountId
     * @param int|null $campaignId
     * @return bool
     */
    public function update(
        Campaign $campaign,
        ?string  $name = null,
        ?int     $accountId = null,
        ?int     $campaignId = null,
    ): bool
    {
        return $campaign->update([
            Campaign::FIELD_NAME        => $name ?? $campaign->name,
            Campaign::FIELD_ACCOUNT_ID  => $accountId,
            Campaign::FIELD_CAMPAIGN_ID => $campaignId,
        ]);
    }

}
