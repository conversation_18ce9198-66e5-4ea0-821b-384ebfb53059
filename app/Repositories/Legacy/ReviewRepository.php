<?php

namespace App\Repositories\Legacy;

use App\Contracts\Repositories\ReviewRepositoryContract;
use App\Models\Legacy\EloquentReview;
use App\Models\Legacy\EloquentCompany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;

class ReviewRepository implements ReviewRepositoryContract
{
    /**
     * @param int $userId
     * @param string $userEmail
     * @param int $limit
     * @param array $relations
     * @return LengthAwarePaginator
     */
    public function getRelatedReviewsByUserPaginated(
        int    $userId,
        string $userEmail,
        int    $limit = 5,
        array  $relations = []
    ): LengthAwarePaginator
    {
        return $this->getRelatedReviewsByUserQuery(
            $userId,
            $userEmail,
            $relations
        )->paginate($limit);
    }

    /**
     * Related reviews by user id and email
     *
     * @param int $userId
     * @param string $userEmail
     * @param array $relations
     * @return Builder
     */
    public function getRelatedReviewsByUserQuery(int $userId, string $userEmail, array $relations = []): Builder
    {
        $query = EloquentReview::query();

        if ($userId > 0) {
            $query->where(function ($query) use ($userId, $userEmail) {
                $query->where(EloquentReview::FIELD_USER_ID, $userId)
                    ->orWhere(function ($query) use ($userEmail) {
                        $query->where(EloquentReview::FIELD_USER_ID, 0)
                            ->where(EloquentReview::FIELD_USER_EMAIL, $userEmail)
                            ->where(EloquentReview::FIELD_USER_EMAIL, '<>', '');
                    });
            });
        } else {
            $query->where(EloquentReview::FIELD_USER_ID, 0)
                ->where(EloquentReview::FIELD_USER_EMAIL, $userEmail)
                ->where(EloquentReview::FIELD_USER_EMAIL, '<>', '');
        }

        if (!empty($relations)) {
            $query->with($relations);
        }

        $query->orderBy(EloquentReview::FIELD_TIMESTAMP_ADDED, 'DESC');

        return $query;
    }

    /**
     * Gets the latest review by company id
     * @param int $companyId
     * @return EloquentReview
     */
    public function getLatestApprovedReviewForCompany(int $companyId): ?EloquentReview
    {
        $company = EloquentCompany::query()->findOrFail($companyId);
        return $company->reviews()->where(EloquentReview::FIELD_APPROVED, 1)->orderBy(EloquentReview::FIELD_TIMESTAMP_ADDED, 'desc')->first();
    }

    /**
     * @param array $companyIdType
     * @return array
     */
    public function getCompaniesReviewCounts(array $companyIdType = []): array
    {
        if(empty($companyIdType)) {
            return [];
        }

        return EloquentReview::where(EloquentReview::FIELD_APPROVED, '=', EloquentReview::REVIEW_APPROVED)
            ->where(function($query) use ($companyIdType) {
                foreach($companyIdType as $companyId => $companyType) {
                    $query->orWhere(function($query) use ($companyId, $companyType) {
                        $query
                            ->where(EloquentReview::FIELD_REVIEW_TYPE, '=', $companyType)
                            ->where(EloquentReview::FIELD_REVIEW_LINK, '=', $companyId);
                    });
                }
            })
            ->groupBy(EloquentReview::FIELD_REVIEW_LINK)
            ->selectRaw(implode(',', [
                EloquentReview::FIELD_REVIEW_LINK,
                "COUNT(*) AS review_count"
            ]))
            ->get()
            ->keyBy(EloquentReview::FIELD_REVIEW_LINK)
            ->toArray();
    }

    /**
     * @param int $companyId
     *
     * @return EloquentReview|null
     */
    public function getLastReview(int $companyId): ?EloquentReview
    {
        /** @var EloquentReview|null $review */
        $review = EloquentReview::query()
            ->where(EloquentReview::FIELD_REVIEW_LINK, $companyId)
            ->orderBy(EloquentReview::FIELD_TIMESTAMP_ADDED, 'DESC')
            ->first();

        return $review;
    }
}
