<?php

namespace App\Repositories\Legacy;

use App\Contracts\Repositories\LeadPriceRepositoryContract;
use App\Enums\Odin\Industry;
use App\Models\Odin\Industry as IndustryModel;
use App\Enums\Odin\PropertyType;
use App\Enums\Odin\QualityTier;
use App\Enums\Odin\StateAbbreviation;
use App\Models\Legacy\LeadCategory;
use App\Models\Legacy\LeadPrice;
use App\Models\Legacy\Location;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductStateFloorPrice;
use App\Models\Odin\ServiceProduct;
use App\Services\DatabaseHelperService;
use Illuminate\Support\Collection;

class LeadPriceRepository implements LeadPriceRepositoryContract
{
    const DEFAULT_COMPANY_ID        = 1;
    const RESIDENTIAL_LEAD_CATEGORY = 1;
    const FLOOR_PRICE_TYPE          = 'floor';
    const SOLAR_INDUSTRY            = 'solar';

    /**
     * @param string $industry
     * @return Collection
     */
    public function getCountySpecificFloorPricesForIndustry(string $industry = 'solar'): Collection
    {
        return Location::query()->select([
            Location::TABLE . '.' . Location::STATE_ABBREVIATION,
            Location::TABLE . '.' . Location::COUNTY_KEY,
            LeadPrice::TABLE . '.' . LeadPrice::LEAD_TYPE_ID, # 1-Standard, 2-Premium
            LeadPrice::TABLE . '.' . LeadPrice::LEAD_SALES_TYPE_ID, // Exclusive, Duo, Trio, etc...
            LeadPrice::TABLE . '.' . LeadPrice::PRICE
        ])->leftJoin(LeadPrice::TABLE, function ($join) use ($industry) {
            $join->on(LeadPrice::TABLE . '.' . LeadPrice::LOCATION_ID, '=', Location::TABLE .'.'. Location::ID)
                ->where(LeadPrice::TABLE . '.' . LeadPrice::COMPANY_ID, self::DEFAULT_COMPANY_ID)
                ->where(LeadPrice::TABLE . '.' . LeadPrice::LEAD_CATEGORY_ID, self::RESIDENTIAL_LEAD_CATEGORY)
                ->where(LeadPrice::TABLE . '.' . LeadPrice::PRICE_TYPE, LeadPrice::PRICE_TYPE_FLOOR)
                ->where(LeadPrice::TABLE . '.' . LeadPrice::LEAD_INDUSTRY, $industry);
        })->where(Location::TABLE . '.' . Location::TYPE, Location::TYPE_COUNTY)
            ->where(Location::TABLE . '.' . Location::STATE_ABBREVIATION, 'NJ') // todo: remove
            ->orderBy(Location::TABLE . '.' . Location::STATE_ABBREVIATION)
            ->orderBy(Location::TABLE . '.' . Location::COUNTY_KEY)
            ->get();
    }

    /**
     * @param string $industry
     * @return Collection
     */
    public function getStateFloorPricesForIndustry(string $industry = 'solar'): Collection
    {
        if(in_array($industry, [Industry::SOLAR->getSlug(), Industry::ROOFING->getSlug()], true)) {
            return Location::query()->select([
                Location::TABLE . '.' . Location::STATE_ABBREVIATION,
                LeadPrice::TABLE . '.' . LeadPrice::LEAD_TYPE_ID, # 1-Standard, 2-Premium
                LeadPrice::TABLE . '.' . LeadPrice::LEAD_SALES_TYPE_ID, // Exclusive, Duo, Trio, etc...
                LeadPrice::TABLE . '.' . LeadPrice::PRICE
            ])->leftJoin(LeadPrice::TABLE, function ($join) use ($industry) {
                $join->on(LeadPrice::TABLE . '.' . LeadPrice::LOCATION_ID, '=', Location::TABLE . '.' . Location::ID)
                    ->where(LeadPrice::TABLE . '.' . LeadPrice::COMPANY_ID, self::DEFAULT_COMPANY_ID)
                    ->where(LeadPrice::TABLE . '.' . LeadPrice::LEAD_CATEGORY_ID, self::RESIDENTIAL_LEAD_CATEGORY)
                    ->where(LeadPrice::TABLE . '.' . LeadPrice::PRICE_TYPE, LeadPrice::PRICE_TYPE_FLOOR)
                    ->where(LeadPrice::TABLE . '.' . LeadPrice::LEAD_INDUSTRY, $industry);
            })->where(Location::TABLE . '.' . Location::TYPE, Location::TYPE_STATE)
                ->orderBy(Location::TABLE . '.' . Location::STATE_ABBREVIATION)
                ->get();
        }
        else {
            $serviceProductIds = ServiceProduct::query()
                ->whereHas(ServiceProduct::RELATION_SERVICE.'.'.IndustryService::RELATION_INDUSTRY, function($has) use ($industry) {
                    $has->where(IndustryModel::TABLE.'.'.IndustryModel::FIELD_SLUG, $industry);
                })
                ->pluck(ServiceProduct::FIELD_ID);

            return Location::query()
                ->select([
                    Location::TABLE . '.' . Location::STATE_ABBREVIATION,
                    ProductStateFloorPrice::TABLE . '.' . ProductStateFloorPrice::FIELD_QUALITY_TIER_ID,
                    ProductStateFloorPrice::TABLE . '.' . ProductStateFloorPrice::FIELD_SALE_TYPE_ID, // Exclusive, Duo, Trio, etc...
                    ProductStateFloorPrice::TABLE . '.' . ProductStateFloorPrice::FIELD_PRICE
                ])
                ->leftJoin( DatabaseHelperService::database().'.'.ProductStateFloorPrice::TABLE, function($join) use ($serviceProductIds) {
                    $join
                        ->on(
                            DatabaseHelperService::database().'.'.ProductStateFloorPrice::TABLE.'.'.ProductStateFloorPrice::FIELD_STATE_LOCATION_ID,
                            '=',
                            Location::TABLE.'.'.Location::ID,
                        )
                        ->whereIn(ProductStateFloorPrice::TABLE.'.'.ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID, $serviceProductIds);
                })
                ->where(Location::TABLE . '.' . Location::TYPE, Location::TYPE_STATE)
                ->orderBy(Location::TABLE . '.' . Location::STATE_ABBREVIATION)
                ->get();
        }
    }

    /**
     * // Note: this also includes the state level pricing for the defined state
     *
     * @param string $industry
     * @param PropertyType $propertyType
     * @param StateAbbreviation $stateAbbreviation
     * @param array|null $countyKeys
     * @return Collection
     */
    public function getCountyFloors(string $industry, PropertyType $propertyType, StateAbbreviation $stateAbbreviation, ?array $countyKeys = []): Collection
    {
        $query = LeadPrice::query()
            ->leftJoin(Location::TABLE, LeadPrice::TABLE.'.'.LeadPrice::LOCATION_ID, '=', Location::TABLE.'.'.Location::ID)
            ->where(Location::STATE_ABBREVIATION, $stateAbbreviation->value)
            ->where(LeadPrice::PRICE_TYPE, LeadPrice::PRICE_TYPE_FLOOR)
            ->where(LeadPrice::COMPANY_ID, LeadPrice::DEFAULT_COMPANY)
            ->where(LeadPrice::LEAD_INDUSTRY, $this->enumIndustryToLegacyIndustry($industry))
            ->where(LeadPrice::LEAD_CATEGORY_ID, $this->enumPropertyTypeToLegacyCategoryId($propertyType));

        if(!empty($countyKeys)) {
            $query->where(function($where) use ($countyKeys) {
                $where
                    ->where(Location::TYPE, Location::TYPE_STATE)
                    ->orWhere(function($where) use ($countyKeys) {
                        $where
                            ->where(Location::TYPE, Location::TYPE_COUNTY)
                            ->whereIn(Location::COUNTY_KEY, $countyKeys);
                    });
            });
        }
        else {
            $query->whereIn(Location::TYPE, [Location::TYPE_COUNTY, Location::TYPE_STATE]);
        }

        return $query->get();
    }

    /**
     * @param string $industry
     * @param PropertyType $propertyType
     * @return Collection<int, LeadPrice>
     */
    public function getStateFloors(string $industry, PropertyType $propertyType): Collection
    {
        return LeadPrice::query()
            ->leftJoin(Location::TABLE, LeadPrice::TABLE.'.'.LeadPrice::LOCATION_ID, '=', Location::TABLE.'.'.Location::ID)
            ->where(Location::TYPE, Location::TYPE_STATE)
            ->where(LeadPrice::PRICE_TYPE, LeadPrice::PRICE_TYPE_FLOOR)
            ->where(LeadPrice::COMPANY_ID, LeadPrice::DEFAULT_COMPANY)
            ->where(LeadPrice::LEAD_INDUSTRY, $this->enumIndustryToLegacyIndustry($industry))
            ->where(LeadPrice::LEAD_CATEGORY_ID, $this->enumPropertyTypeToLegacyCategoryId($propertyType))
            ->get();
    }

    /**
     * @param Collection $campaignIds
     * @param string $industry
     * @param PropertyType $propertyType
     * @param StateAbbreviation $stateAbbreviation
     * @param string|null $countyKey
     * @return Collection
     */
    public function getCountyBids(Collection $campaignIds, string $industry, PropertyType $propertyType, StateAbbreviation $stateAbbreviation, ?string $countyKey = ''): Collection
    {
        $query = LeadPrice::query()
                    ->leftJoin(Location::TABLE, LeadPrice::TABLE.'.'.LeadPrice::LOCATION_ID, '=', Location::TABLE.'.'.Location::ID)
                    ->where(Location::STATE_ABBREVIATION, $stateAbbreviation->value)
                    ->where(LeadPrice::PRICE_TYPE, LeadPrice::PRICE_TYPE_BID)
                    ->whereIn(LeadPrice::LEAD_CAMPAIGN_ID, $campaignIds)
                    ->where(LeadPrice::LEAD_INDUSTRY, $this->enumIndustryToLegacyIndustry($industry))
                    ->where(LeadPrice::LEAD_CATEGORY_ID, $this->enumPropertyTypeToLegacyCategoryId($propertyType));

        if(!empty($countyKey)) {
            $query->where(function($where) use ($countyKey) {
                $where
                    ->where(Location::TYPE, Location::TYPE_STATE)
                    ->orWhere(function($where) use ($countyKey) {
                        $where
                            ->where(Location::TYPE, Location::TYPE_COUNTY)
                            ->where(Location::COUNTY_KEY, $countyKey);
                    });
            });
        }
        else {
            $query->whereIn(Location::TYPE, [Location::TYPE_COUNTY, Location::TYPE_STATE]);
        }

        return $query->get();
    }

    /**
     * @param Collection $campaignIds
     * @param string $industry
     * @param PropertyType $propertyType
     * @param StateAbbreviation|null $stateAbbreviation
     * @return Collection
     */
    public function getStateBids(Collection $campaignIds, string $industry, PropertyType $propertyType, ?StateAbbreviation $stateAbbreviation = null): Collection
    {
        $query = LeadPrice::query()
                    ->leftJoin(Location::TABLE, LeadPrice::TABLE.'.'.LeadPrice::LOCATION_ID, '=', Location::TABLE.'.'.Location::ID)
                    ->where(Location::TYPE, Location::TYPE_STATE)
                    ->where(LeadPrice::PRICE_TYPE, LeadPrice::PRICE_TYPE_BID)
                    ->whereIn(LeadPrice::LEAD_CAMPAIGN_ID, $campaignIds)
                    ->where(LeadPrice::LEAD_INDUSTRY, $this->enumIndustryToLegacyIndustry($industry))
                    ->where(LeadPrice::LEAD_CATEGORY_ID, $this->enumPropertyTypeToLegacyCategoryId($propertyType));

        if($stateAbbreviation) {
            $query->where(Location::STATE_ABBREVIATION, $stateAbbreviation->value);
        }

        return $query->get();
    }

    /**
     * @param string $industry
     * @return string
     */
    private function enumIndustryToLegacyIndustry(string $industry): string
    {
        return match ($industry){Industry::SOLAR->value => LeadPrice::LEAD_INDUSTRY_SOLAR, Industry::ROOFING->value => LeadPrice::LEAD_INDUSTRY_ROOFING};
    }

    /**
     * @param PropertyType $propertyType
     * @return int
     */
    private function enumPropertyTypeToLegacyCategoryId(PropertyType $propertyType): int
    {
        return match ($propertyType){PropertyType::RESIDENTIAL => LeadCategory::RESIDENTIAL, PropertyType::COMMERCIAL => LeadCategory::COMMERCIAL};
    }

    /**
     * @param QualityTier $qualityTier
     * @return int
     */
    private function enumQualityTierToLegacyTypeId(QualityTier $qualityTier): int
    {
        return match ($qualityTier){QualityTier::STANDARD => LeadPrice::LEAD_TYPE_STANDARD, QualityTier::PREMIUM => LeadPrice::LEAD_TYPE_PREMIUM};
    }
}
