<?php

namespace App\Repositories\Legacy;

use App\DTO\Billing\Credit\ConcreteCompanyCredit;
use App\Models\Legacy\EloquentCreditLog;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class CompanyCreditRepository
{
    /**
     * @param int $legacyCompanyId
     * @param float $amountInDollars
     * @param string $creditTypeSlug
     * @param string|null $comment
     * @return void
     */
    public function deductCreditFromV1(
        int $legacyCompanyId,
        float $amountInDollars,
        string $creditTypeSlug,
        ?string $comment = null
    ): void
    {
        DB::connection('readonly')->table(EloquentCreditLog::TABLE)
            ->insert([
                EloquentCreditLog::LOG_DATE   => now()->unix(),
                EloquentCreditLog::COMPANY_ID => $legacyCompanyId,
                EloquentCreditLog::INVOICE_ID => null,
                EloquentCreditLog::TYPE       => $creditTypeSlug,
                EloquentCreditLog::AMOUNT     => $amountInDollars * -1,
                EloquentCreditLog::USERID     => null,
                EloquentCreditLog::STATUS     => EloquentCreditLog::STATUS_APPLIED,
                EloquentCreditLog::MANUAL     => false,
                EloquentCreditLog::COMMENT    => $comment,
                EloquentCreditLog::CREATED_AT => now(),
                EloquentCreditLog::UPDATED_AT => now(),
            ]);
    }

    /**
     * @param int $legacyCompanyId
     * @return Collection
     */
    public function getCompanyCreditLogs(int $legacyCompanyId): Collection
    {
        return EloquentCreditLog::query()
            ->select([
                EloquentCreditLog::ID,
                EloquentCreditLog::COMPANY_ID,
                EloquentCreditLog::AMOUNT,
                EloquentCreditLog::STATUS,
                EloquentCreditLog::TYPE,
                EloquentCreditLog::COMMENT,
                DB::raw('FROM_UNIXTIME(' . EloquentCreditLog::LOG_DATE . ') as log_date'),
            ])
            ->where(EloquentCreditLog::COMPANY_ID, $legacyCompanyId)
            ->get();
    }

    /**
     * @param Collection $issuedLogs
     * @param int $atomicAmount
     * @return mixed
     * @throws Exception
     */
    protected function deductIssuedCredits(Collection $issuedLogs, int $atomicAmount): Collection
    {
        /**
         * @var ConcreteCompanyCredit $item
         */
        foreach ($issuedLogs as $companyCredit) {
            if ($atomicAmount == 0) {
                break;
            }

            $amountToUse = min($companyCredit->getAtomicRemainingAmount(), $atomicAmount);

            $companyCredit->decrementAtomicRemainingAmount($amountToUse);

            $atomicAmount -= $amountToUse;
        }

        if ($atomicAmount > 0) {
            throw new Exception('Amount applied has no issued');
        }

        return $issuedLogs->filter(function (ConcreteCompanyCredit $item) {
            return $item->getAtomicRemainingAmount() > 0;
        })->values();
    }

    /**
     * @param int $legacyCompanyId
     * @return Collection<ConcreteCompanyCredit>
     * @throws Exception
     */
    public function getIssuedCreditsWithRemainingBalance(int $legacyCompanyId): Collection
    {
        $allLogs = $this->getCompanyCreditLogs(
            legacyCompanyId: $legacyCompanyId
        )->groupBy(EloquentCreditLog::TYPE);

        $result = collect();

        foreach ($allLogs as $creditType => $creditLogs) {
            $issuedLogs = collect();

            foreach ($creditLogs as $log) {
                try {
                    if ($log->status === EloquentCreditLog::STATUS_ISSUED) {
                        $issuedLogs->add(new ConcreteCompanyCredit(
                            id                   : $log->{EloquentCreditLog::ID},
                            companyId            : $log->{EloquentCreditLog::COMPANY_ID},
                            atomicInitialAmount  : $log->{EloquentCreditLog::AMOUNT} * 100,
                            atomicRemainingAmount: $log->{EloquentCreditLog::AMOUNT} * 100,
                            creditType           : $log->{EloquentCreditLog::TYPE},
                            dateApplied          : Carbon::parse($log->log_date),
                            source               : 'legacy',
                            notes                : $log->{EloquentCreditLog::COMMENT},
                        ));

                        continue;
                    }

                    if ($issuedLogs->isEmpty()) {
                        throw new Exception('No issued');
                    }

                    $atomicAmount = abs($log->amount * 100);

                    $issuedLogs = $this->deductIssuedCredits(
                        issuedLogs  : $issuedLogs,
                        atomicAmount: $atomicAmount
                    );
                } catch (Exception $exception) {
                    logger()->error($exception);
                }
            }

            $result->put($creditType, $issuedLogs);
        }

        return collect($result)
            ->filter()
            ->flatten(1)
            ->filter(fn(ConcreteCompanyCredit $item) => $item->getAtomicRemainingAmount() > 0)
            ->values();
    }
}
