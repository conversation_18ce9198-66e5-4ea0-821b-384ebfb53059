<?php

namespace App\Repositories\Legacy;

use App\Models\Legacy\EloquentUtility;
use App\Models\Legacy\ZipCodeToUtility;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class UtilityRepository
{
    /**
     * @param string $zipCode
     * @return Collection<int, EloquentUtility>
     */
    public function getUtilitiesInZipcode(string $zipCode): Collection
    {
        return $this->getUtilitiesInZipcodeQuery($zipCode)->get();
    }

    public function getUtilitiesInZipcodeQuery(string $zipCode): Builder
    {
        return EloquentUtility::query()
            ->select(EloquentUtility::TABLE . '.*')
            ->leftJoin(
                ZipCodeToUtility::TABLE,
                EloquentUtility::TABLE . '.' . EloquentUtility::FIELD_UTILITY_ID,
                '=',
                ZipCodeToUtility::TABLE . '.' . ZipCodeToUtility::FIELD_UTILITY_ID
            )
            ->where(ZipCodeToUtility::FIELD_ZIP_CODE, $zipCode);
    }

    /**
     * @param string $uuid
     * @return Builder|Model|object|null
     */
    public function findByUuid(string $uuid) {
        return EloquentUtility::query()
            ->where(EloquentUtility::FIELD_UUID, $uuid)
            ->first();
    }
}
