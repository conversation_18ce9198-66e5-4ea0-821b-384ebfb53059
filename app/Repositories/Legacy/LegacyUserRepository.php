<?php

namespace App\Repositories\Legacy;

use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentUser;
use App\Models\Odin\CompanyUser;
use App\Models\User;
use App\Services\CompanyRegistration\CompanyRegistrationSyncService;
use App\Services\DatabaseHelperService;
use App\Services\PubSub\PubSubService;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\UnauthorizedException;

class LegacyUserRepository
{
    const REQUEST_COMPANY_REFERENCE = 'company_reference';
    const REQUEST_USER_ID           = 'user_id';
    const REQUEST_DATA              = 'data';

    /**
     * @param PubSubService $pubSubService
     */
    public function __construct(
        protected PubSubService $pubSubService,
        protected CompanyRegistrationSyncService $syncService,
    ){}

    /**
     * @param int $companyId
     * @param bool $phoneMustExist
     * @param bool $emailMustExist
     * @param bool|null $paginate
     * @param int|null $perPage
     * @return Collection|Paginator
     */
    public function getActiveUsersByCompanyId(
        int $companyId,
        bool $phoneMustExist,
        bool $emailMustExist,
        ?bool $paginate = false,
        ?int $perPage = 10
    ): Collection|Paginator
    {
        $query = EloquentUser::query()
                             ->where(EloquentUser::COMPANY_ID, $companyId)
                             ->where(EloquentUser::STATUS, EloquentUser::FIELD_STATUS_VALUE_ACTIVE);

        if ($phoneMustExist) {
            $query->whereNotNull(EloquentUser::PHONE);
        }
        if ($emailMustExist) {
            $query->whereNotNull(EloquentUser::EMAIL);
        }

        if($paginate) {
            return $query->paginate($perPage);
        }
        else {
            return $query->get();
        }
    }

    /**
     * Returns a legacy user by their id.
     *
     * @param int $id
     * @return array|null
     */
    public function getLegacyUser(int $id): ?array
    {
        /** @var EloquentUser $user */
        $user = EloquentUser::query()->find($id);

        if(!$user)
            return null;

        return [
            "id" => $id,
            "name" => $user->firstname . " " . $user->lastname,
            "company" => $user->company?->companyname,
            "company_id" => $user->companyid
        ];
    }

    /**
     * @param string $companyReference
     * @param int $userId
     * @param array $user
     * @return bool
     */
    public function updateCompanyUser(string $companyReference, int $userId, array $user): bool
    {
        return $this->pubSubService->handle(EventCategory::ADMIN2->value, EventName::COMPANY_USER_UPDATED->value,
            [
                self::REQUEST_COMPANY_REFERENCE => $companyReference,
                self::REQUEST_USER_ID           => $userId,
                self::REQUEST_DATA              => $user
            ]
        ) !== null;
    }

    /**
     * @param string $companyReference
     * @param int $userId
     * @return bool
     */
    public function deleteCompanyUser(string $companyReference, int $userId): bool
    {
        return $this->pubSubService->handle(EventCategory::ADMIN2->value, EventName::COMPANY_USER_DELETED->value,
            [
                self::REQUEST_COMPANY_REFERENCE => $companyReference,
                self::REQUEST_USER_ID           => $userId
            ]
        ) !== null;
    }

    /**
     * @param string $companyReference
     * @param array $data
     * @return bool
     */
    public function createCompanyUser(string $companyReference, array $data): bool
    {
        $transformData = $this->syncService->transformForLegacy(CompanyUser::class, $data);
        $eventData = [
            'reference'    => $data[CompanyUser::FIELD_REFERENCE],
            'data'         => $transformData,
        ];
        return $this->syncService->syncChangesToLegacy(EventName::COMPANY_USER_CREATED, $companyReference, $eventData);
    }
}
