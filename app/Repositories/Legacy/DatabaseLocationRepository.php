<?php

namespace App\Repositories\Legacy;

use App\Contracts\Repositories\DatabaseLocationRepositoryContract;
use App\Models\Legacy\Location;
use Illuminate\Support\Collection;

class DatabaseLocationRepository implements DatabaseLocationRepositoryContract
{
    const CHUNK_SIZE = 100;

    /**
     * @return Collection
     */
    public function getStates(): Collection
    {
        return Location::where(Location::TYPE, Location::TYPE_STATE)
                        ->get()
                        ->keyBy(Location::STATE_ABBREVIATION);
    }

    /** @inheritDoc */
    public function getZipCode(string $zipCode)
    {
        /** @var Location|null $location */
        $location = Location::query()
            ->where(Location::TYPE, Location::TYPE_ZIP_CODE)
            ->where(Location::ZIP_CODE, $zipCode)
            ->first();

        return $location;
    }

    /**
     * @param Collection $zipCodeIds
     * @return array
     */
    public function getCountiesByZipCodesExt(Collection $zipCodeIds): array
    {
        $countyIds = [];

        $countyResults = collect();
        $chunkedCollection = $zipCodeIds->chunk(self::CHUNK_SIZE);

        foreach($chunkedCollection as $chunkZipCodeIds) {
            /** @var \Illuminate\Database\Eloquent\Collection $results */
            $results = Location::query()->select([
                Location::STATE_ABBREVIATION,
                Location::COUNTY_KEY
            ])
                ->where(Location::TYPE, Location::TYPE_ZIP_CODE)
                ->whereIn(Location::ID, $chunkZipCodeIds)
                ->groupBy(Location::STATE_ABBREVIATION)
                ->groupBy(Location::COUNTY_KEY)
                ->orderBy(Location::STATE_ABBREVIATION)
                ->orderBy(Location::COUNTY_KEY)
                ->get();

            foreach ($results as $key => $location) {
                $countyResults->push($location);
            }
        }
        if (!empty($countyResults) && count($countyResults) > 0) {
            $counties = $this->groupAndPluck($countyResults, Location::STATE_ABBREVIATION, Location::COUNTY_KEY);

            $countyIds = collect();
            foreach ($counties as $stateAbbr => $countyKeys) {
                /** @var \Illuminate\Database\Eloquent\Collection $results */
                $results = Location::query()->select([
                    Location::ID, Location::STATE_ABBREVIATION, Location::COUNTY_KEY
                ])
                    ->where(Location::TYPE, Location::TYPE_COUNTY)
                    ->where(Location::STATE_ABBREVIATION, $stateAbbr)
                    ->whereIn(Location::COUNTY_KEY, $countyKeys)
                    ->orderBy(Location::STATE_ABBREVIATION)
                    ->orderBy(Location::COUNTY_KEY)
                    ->get()
                    ->keyBy(Location::ID);

                foreach ($results as $key => $location) {
                    $countyIds->push($key);
                }
            }

            $countyIds = $countyIds->toArray();
        }

        return $countyIds;
    }

    /**
     * @param Collection $zipCodeIds
     * @param bool $returnIds
     * @return array
     */
    public function getStateByZipCodes(Collection $zipCodeIds, bool $returnIds = false): array
    {
        $statesResults = [];
        $chunkedCollection = $zipCodeIds->chunk(self::CHUNK_SIZE);

        foreach($chunkedCollection as $chunkZipCodeIds) {
            $results = Location::query()->toBase()->select([
                Location::STATE_ABBREVIATION
            ])
                ->where(Location::TYPE, Location::TYPE_ZIP_CODE)
                ->whereIn(Location::ID, $chunkZipCodeIds)
                ->groupBy(Location::STATE_ABBREVIATION)
                ->orderBy(Location::STATE_ABBREVIATION)
                ->get();

            foreach ($results as $key => $location) {
                $statesResults[] = $location;
            }
        }

        $states = array_unique(array_column($statesResults, Location::STATE_ABBREVIATION));

        if ($returnIds) {
            /** @var Collection $stateIds */
            $stateIds = Location::whereIn(Location::STATE_ABBREVIATION, $states)
                ->where(Location::TYPE, Location::TYPE_STATE)
                ->get()
                ->pluck(Location::ID);
            return $stateIds->toArray();
        }

        return $states;
    }

    /**
     * @param Collection $collection
     * @param string $groupKey
     * @param string $pluckKey
     * @return Collection
     */
    protected function groupAndPluck($collection, $groupKey, $pluckKey): Collection
    {
        $grouped = $collection->groupBy($groupKey);
        $plucked = collect();
        /**
         * @var string $key
         * @var Collection $item
         */
        foreach ($grouped as $key => $item) {
            $plucked->put($key, collect($item->toArray())->pluck($pluckKey)->toArray());
        }
        return $plucked;
    }
}
