<?php

namespace App\Repositories\Legacy;

use App\Contracts\Repositories\CompanyCRMRepositoryContract;
use App\Models\Legacy\EloquentCompanyCRMAction;
use App\Services\Legacy\APIConsumer;

class CompanyCRMRepository implements CompanyCRMRepositoryContract
{
    const REQUEST_COMPANY_REFERENCE = 'company_reference';
    const REQUEST_USER_ID           = 'user_id';
    const REQUEST_CONTACT_ID        = 'contact_id';
    const REQUEST_ACTION_TEXT       = 'action_text';

    const API_BASE_ENDPOINT = '/repositories/company-crm';
    const API_CREATE_ACTION = self::API_BASE_ENDPOINT . '/create-action';

    /** @var APIConsumer $apiConsumer */
    protected APIConsumer $apiConsumer;

    /**
     * @param APIConsumer $apiConsumer
     */
    public function __construct(APIConsumer $apiConsumer)
    {
        $this->apiConsumer = $apiConsumer;
    }

    /**
     * @inheritDoc
     */
    public function createAction(string $companyReference, int $userId, int $contactId, string $actionText): ?EloquentCompanyCRMAction
    {
        $action = $this->apiConsumer->post(self::API_CREATE_ACTION,
            [
                self::REQUEST_COMPANY_REFERENCE  => $companyReference,
                self::REQUEST_USER_ID            => $userId,
                self::REQUEST_CONTACT_ID         => $contactId ?? 0,
                self::REQUEST_ACTION_TEXT        => $actionText
            ]
        )->json(APIConsumer::RESPONSE_RESULT);

        return EloquentCompanyCRMAction::query()->find($action->{EloquentCompanyCRMAction::ID});
    }

    /**
     * @param array $companyIds
     * @return array
     */
    public function getCompaniesLastUpdated(array $companyIds = []): array
    {
        if(empty($companyIds)) {
            return [];
        }

        return EloquentCompanyCRMAction::selectRaw(implode(',', [
            EloquentCompanyCRMAction::COMPANY_ID,
            "MAX(".EloquentCompanyCRMAction::TIMESTAMP_ADDED.") AS ".EloquentCompanyCRMAction::TIMESTAMP_ADDED
        ]))
            ->whereIn(EloquentCompanyCRMAction::COMPANY_ID, $companyIds)
            ->groupBy(EloquentCompanyCRMAction::COMPANY_ID)
            ->get()
            ->keyBy(EloquentCompanyCRMAction::COMPANY_ID)
            ->toArray();
    }
}
