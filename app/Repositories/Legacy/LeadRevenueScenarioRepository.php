<?php

namespace App\Repositories\Legacy;

use App\Contracts\Repositories\LeadRevenueScenarioRepositoryContract;
use App\Models\LeadRevenueScenario;
use App\Services\Legacy\APIConsumer;

class LeadRevenueScenarioRepository implements LeadRevenueScenarioRepositoryContract
{
    const API_BASE_ENDPOINT = '/repositories/lead-revenue-scenario';
    const API_UPDATE_SOLD_FOR_ENDPOINT = self::API_BASE_ENDPOINT . '/update-sold-for';
    const API_CREATE_OR_UPDATE_LEAD_REVENUE_SCENARIO_ENDPOINT = self::API_BASE_ENDPOINT . '/create-or-update-lead-revenue-scenario';

    /** @var APIConsumer $apiConsumer */
    protected APIConsumer $apiConsumer;

    /**
     * @param APIConsumer $apiConsumer
     */
    public function __construct(APIConsumer $apiConsumer)
    {
        $this->apiConsumer = $apiConsumer;
    }

    /**
     * Creates or updates a lead revenue scenario for a given lead.
     *
     * @param string $leadReference
     * @param float $current
     * @param float $maximum
     * @param float $floorPrice
     * @param string $type
     * @return bool
     */
    public function createOrUpdateLeadRevenueScenario(
        string $leadReference,
        float $current,
        float $maximum,
        float $floorPrice,
        string $type = LeadRevenueScenario::TYPE_UNVERIFIED
    ): bool
    {
        return $this->apiConsumer->patch(
            self::API_CREATE_OR_UPDATE_LEAD_REVENUE_SCENARIO_ENDPOINT,
            compact("leadReference", "current", "maximum", "floorPrice", "type")
        )->json(APIConsumer::RESPONSE_RESULT);
    }

    /**
     * Handles setting the value that a given lead is sold for.
     *
     * @param string $leadReference
     * @param float $soldFor
     * @return bool
     */
    public function updateSoldFor(string $leadReference, float $soldFor): bool
    {
        return $this->apiConsumer->patch(
            self::API_UPDATE_SOLD_FOR_ENDPOINT,
            compact("leadReference", "soldFor")
        )->json(APIConsumer::RESPONSE_RESULT);
    }
}
