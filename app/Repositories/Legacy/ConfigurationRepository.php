<?php

namespace App\Repositories\Legacy;

use App\Models\Legacy\EloquentConfiguration;

class ConfigurationRepository
{
    /**
     * @param string $name
     * @param int|null $relid
     * @param string|null $reltype
     *
     * @return EloquentConfiguration|null
     */
    public function getConfigurationValueByName(string $name, ?int $relid = null, ?string $reltype = null): ?EloquentConfiguration
    {
        $query = EloquentConfiguration::query()->where(EloquentConfiguration::NAME, $name);

        if ($relid !== null) $query->where(EloquentConfiguration::REL_ID, $relid);
        if ($reltype !== null) $query->where(EloquentConfiguration::REL_TYPE, $reltype);

        return $query->first();
    }
}
