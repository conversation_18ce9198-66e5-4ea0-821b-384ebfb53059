<?php

namespace App\Repositories\Legacy;

use App\Contracts\Repositories\MediaRepositoryContract;
use App\Models\Legacy\EloquentCompany;

class MediaRepository implements MediaRepositoryContract
{
    /**
     * @inheritDoc
     */
    public function getLegacyMediaURLForCompany(int $legacyCompany, string $fileName): ?string
    {
        /** @var EloquentCompany|null $company */
        $company = EloquentCompany::query()->find($legacyCompany);

        if(empty($company) || !strlen(trim($fileName))) {
            return null;
        }

        $legacyRoot = config('app.solarreviews_domain.www');
        $md5Key     = EloquentCompany::CONTENT_PATH_KEY;

        $secureKey  = $legacyCompany . md5($legacyCompany . $md5Key);
        $legacyPath = rtrim($legacyRoot, '/') . "/content/company/{$secureKey}";

        return "{$legacyPath}/media/{$fileName}";
    }
}
