<?php

namespace App\Repositories\Legacy;

use App\Contracts\Repositories\CompanySuccessManagerContract;
use App\Models\SuccessManager;
use App\Models\SuccessManagerClient;
use App\Services\Legacy\APIConsumer;
use Illuminate\Support\Collection;

class CompanySuccessManagerRepository implements CompanySuccessManagerContract
{

    /**
     * @param APIConsumer $apiConsumer
     */
    public function __construct(protected APIConsumer $apiConsumer) {}

    /**
     * @param string $companyReference
     * @return Collection|null
     */
    public function getSuccessManager(string $companyReference): ?Collection
    {
        $user = SuccessManagerClient::query()
                    ->where(SuccessManagerClient::FIELD_COMPANY_REFERENCE, $companyReference)
                    ->firstOrFail()
                    ->{SuccessManagerClient::RELATION_SUCCESS_MANAGER}
                    ->{SuccessManager::RELATION_USER};

        return collect([$user]);
    }

    public function assignSuccessManager(string $companyReference, int $successManagerLegacyUserId): array
    {
        return $this->apiConsumer
                ->patch("/repositories/companies/{$companyReference}/success-manager/{$successManagerLegacyUserId}")
                ->json() ?? [];
    }
}
