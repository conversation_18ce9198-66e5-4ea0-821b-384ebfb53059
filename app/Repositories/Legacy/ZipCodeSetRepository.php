<?php

namespace App\Repositories\Legacy;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use App\Contracts\Repositories\ZipCodeSetRepositoryContract;
use App\Models\Legacy\EloquentZipCode;

/**
 * @deprecated
 */
class ZipCodeSetRepository implements ZipCodeSetRepositoryContract
{
    const FALLBACK_UTC = -8;

    /**
     * @inheritDoc
     */
    public function getStandardZipCode(string $zipCode): ?EloquentZipCode
    {
        /** @var EloquentZipCode $realZip */
        $realZip = EloquentZipCode::query()
            ->where(EloquentZipCode::FIELD_ZIP_CODE, $zipCode)
            ->where(EloquentZipCode::FIELD_ZIP_TYPE, EloquentZipCode::ZIP_TYPE_STANDARD)
            ->first();
        return $realZip;
    }

    /**
     * @param string $zipCode
     * @return EloquentZipCode|Model|null
     */
    public function getZipCode(string $zipCode): null|EloquentZipCode|Model
    {
        return EloquentZipCode::query()
            ->where(EloquentZipCode::FIELD_ZIP_CODE, $zipCode)
            ->first();
    }

    /**
     * @param array $zipcodes
     * @return Collection
     */
    public function getStandardZipCodes(array $zipcodes): Collection
    {
        return EloquentZipCode::query()
            ->whereIn(EloquentZipCode::FIELD_ZIP_CODE, $zipcodes)
            ->where(EloquentZipCode::FIELD_ZIP_TYPE, EloquentZipCode::ZIP_TYPE_STANDARD)
            ->get()
            ->keyBy(EloquentZipCode::FIELD_ZIP_CODE);
    }

    /**
     * @param string $zipCode
     *
     * @return int
     */
    public function getUtc(string $zipCode): int
    {
        $zipCode = $this->getStandardZipCode($zipCode);

        if($zipCode) {
            if($this->isActiveDST() && $zipCode->dst === "Y") {
                return $zipCode->utc + 1;
            }

            return $zipCode->utc;
        }

        return self::FALLBACK_UTC;
    }

    /**
     * @return string
     */
    public function isActiveDST()
    {
        $currentTimezone = date_default_timezone_get();
        $timezoneToCheckDST = 'America/Los_Angeles';

        date_default_timezone_set($timezoneToCheckDST);
        $isActiveDST = date("I");
        date_default_timezone_set($currentTimezone);

        return $isActiveDST;
    }
}
