<?php

namespace App\Repositories\Legacy;

use App\Enums\AlertMethod;
use App\Enums\AlertType;
use App\Models\Legacy\EloquentAlert;
use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Odin\Company;
use Illuminate\Support\Collection;

class CompanyNotificationRepository
{
    /**
     * @param Company $company
     * @param AlertType $type
     * @return Collection<string>
     */
    public function getNotifiableCompanyEmails(
        Company $company,
        AlertType $type,
    ): Collection
    {
        return EloquentCompanyContact::query()
            ->select(
                EloquentCompanyContact::TABLE . '.' . EloquentCompanyContact::FIELD_EMAIL
            )
            ->join(
                EloquentAlert::TABLE,
                EloquentAlert::TABLE . '.' . EloquentAlert::FIELD_CONTACT_ID,
                EloquentCompanyContact::TABLE . '.' . EloquentCompanyContact::FIELD_CONTACT_ID
            )
            ->where(EloquentAlert::TABLE . '.' . EloquentAlert::FIELD_TYPE, $type->value)
            ->where(EloquentAlert::TABLE . '.' . EloquentAlert::FIELD_METHOD, AlertMethod::EMAIL->value)
            ->where(EloquentAlert::TABLE . '.' . EloquentAlert::FIELD_COMPANY_ID, $company->{Company::FIELD_LEGACY_ID})
            ->where(EloquentCompanyContact::TABLE . '.' . EloquentCompanyContact::FIELD_COMPANY_ID, $company->{Company::FIELD_LEGACY_ID})
            ->where(EloquentCompanyContact::TABLE . '.' . EloquentCompanyContact::FIELD_STATUS, EloquentCompanyContact::STATUS_ACTIVE)
            ->get()
            ->pluck(EloquentCompanyContact::FIELD_EMAIL);
    }
}
