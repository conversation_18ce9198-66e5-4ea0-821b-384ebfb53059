<?php

namespace App\Repositories\Legacy;

use App\Contracts\Repositories\CompanyAddressesRepositoryContract;
use App\Services\Legacy\APIConsumer;

class CompanyAddressRepository implements CompanyAddressesRepositoryContract
{
    /**
     * @param APIConsumer $apiConsumer
     */
    public function __construct(protected APIConsumer $apiConsumer) {}

    /**
     * @inheritDoc
     */
    public function updateCompanyAddressDetails(string $companyReference, int $addressId, array $data): array
    {
        return $this->apiConsumer
            ->patch("/repositories/companies/{$companyReference}/address/{$addressId}", $data)
            ->json()
            ['data'] ?? [];
    }

    /**
     * @inheritDoc
     */
    public function createCompanyAddress(string $companyReference, array $data): array
    {
        return $this->apiConsumer
            ->post("/repositories/companies/{$companyReference}/address", $data)
            ->json()
            ['data'] ?? [];
    }

    /**
     * @inheritDoc
     */
    public function deleteCompanyAddress(string $companyReference, int $legacyAddressId): array
    {
        return $this->apiConsumer
            ->delete("/repositories/companies/{$companyReference}/address/{$legacyAddressId}")
            ->json()
            ['data'] ?? [];
    }
}
