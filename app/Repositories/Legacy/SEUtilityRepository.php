<?php

namespace App\Repositories\Legacy;

use App\Models\Legacy\SEUtility;
use App\Models\Legacy\SEZipCodeToUtility;
use App\Repositories\LocationRepository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class SEUtilityRepository
{
    const UTILITIES_TO_DISPLAY = 7;

    public function __construct(protected LocationRepository $locationRepository)
    {}

    /**
     * @param string $zipCode
     * @param bool $skipFirstResults
     * @return Collection
     */
    public function getUtilitiesByZipCode(string $zipCode, bool $skipFirstResults = false): Collection
    {
        $location = $this->locationRepository->getZipCode($zipCode);

        /** @var Collection|SEUtility[] $zipCodeUtilityModels */
        $zipCodeUtilityModels = $this->findByZipCode($location->zip_code, self::UTILITIES_TO_DISPLAY);

        /** @var Collection|SEUtility[] $countyUtilityModels */
        $countyUtilityModels = $this->findByCounty($location->state_abbr, $location->county, self::UTILITIES_TO_DISPLAY);

        /** @var Collection|SEUtility[] $stateUtilityModels */
        $stateUtilityModels = $this->getByStateOrderByName($location->state_abbr);

        if($skipFirstResults) {
            if($countyUtilityModels->count() > 0) {
                return $countyUtilityModels;
            }

            return $stateUtilityModels;
        }

        if($zipCodeUtilityModels->count() > 0) {
            return $zipCodeUtilityModels;
        } else if($countyUtilityModels->count() > 0) {
            return $countyUtilityModels;
        }

        return $stateUtilityModels;
    }

    /**
     * @param string $zipCode
     * @param $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function findByZipCode(string $zipCode, $limit = 7): \Illuminate\Database\Eloquent\Collection
    {
        $builder = SEZipCodeToUtility::query()
            ->where(SEZipCodeToUtility::FIELD_ZIP_CODE, $zipCode);

        if (!is_null($limit)) {
            $builder->limit($limit);
        }

        $utilityIds = $builder->get([SEZipCodeToUtility::FIELD_UTILITY_ID])->pluck(SEZipCodeToUtility::FIELD_UTILITY_ID);
        return $this->findByIds($utilityIds->all());
    }

    /**
     * @param string $stateAbbreviation
     * @param string $countyName
     * @param int|null $limit
     * @return Builder[]|\Illuminate\Database\Eloquent\Collection
     */
    private function findByCounty(string $stateAbbreviation, string $countyName, int $limit = null): \Illuminate\Database\Eloquent\Collection|array
    {
        $query = SEUtility::query()
            ->where(SEUtility::FIELD_STATE, $stateAbbreviation)
            ->where(SEUtility::FIELD_COUNTIES_SERVED, 'like', "%{$countyName}%")
            ->orderBy(SEUtility::FIELD_NAME);

        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    /**
     * @param string $stateAbbreviation
     * @param int|null $limit
     * @return Builder[]|\Illuminate\Database\Eloquent\Collection
     */
    private function getByStateOrderByName(string $stateAbbreviation, int $limit = null): \Illuminate\Database\Eloquent\Collection|array
    {
        $query = SEUtility::query()->where('state', $stateAbbreviation)->orderBy('name');

        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    /**
     * @param array $ids
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function findByIds(array $ids): \Illuminate\Database\Eloquent\Collection
    {
        return SEUtility::query()->findMany($ids);
    }
}
