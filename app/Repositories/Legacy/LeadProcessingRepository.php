<?php

namespace App\Repositories\Legacy;

use App\Contracts\Repositories\LeadProcessingRepositoryContract;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Enums\HttpMethod;
use App\Jobs\CalculatePotentialRevenue;
use App\Jobs\SendLegacyAdminRequest;
use App\Models\AppointmentProcessingAllocation;
use App\Models\LeadProcessingAllocation;
use App\Models\LeadProcessingCallingTimeZoneConfiguration;
use App\Models\LeadProcessingConfiguration;
use App\Models\LeadProcessingHeartbeat;
use App\Models\LeadProcessingHistory;
use App\Models\LeadProcessingInitial;
use App\Models\LeadProcessingPendingReview;
use App\Models\LeadProcessingReservedLead;
use App\Models\LeadProcessingTeam;
use App\Models\LeadProcessingTimeZoneConfiguration;
use App\Models\LeadProcessingUnderReview;
use App\Models\LeadProcessor;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Odin\Company;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Product as ProductModel;
use App\Enums\Odin\Product as ProductEnum;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use App\Models\User;
use App\Repositories\LeadProcessing\LeadProcessingQueueConstraintsRepository;
use App\Repositories\Odin\ProductAssignmentRepository;
use App\Services\Legacy\APIConsumer;
use App\Services\PubSub\PubSubService;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class LeadProcessingRepository implements LeadProcessingRepositoryContract
{
    const API_BASE_ENDPOINT                        = '/repositories/lead-processing';
    const API_LEAD_LOCKED_BY_ENDPOINT              = self::API_BASE_ENDPOINT . '/lead-locked-by';
    const API_RESERVE_LEAD_TO_SYSTEM_ENDPOINT      = self::API_BASE_ENDPOINT . '/reserve-lead-to-system';
    const API_RESERVE_LEAD_ENDPOINT                = self::API_BASE_ENDPOINT . '/reserve-lead';
    const API_RELEASE_LEAD_ENDPOINT                = self::API_BASE_ENDPOINT . '/release-lead';
    const API_UPDATE_LEAD_STATUS_ENDPOINT          = self::API_BASE_ENDPOINT . '/update-lead-status';
    const API_UPDATE_LEAD_STATUS_REASON_ENDPOINT   = self::API_BASE_ENDPOINT . '/update-lead-status-reason';
    const API_UPDATE_LEAD_TYPE_ENDPOINT            = self::API_BASE_ENDPOINT . '/update-lead-type';
    const API_BULK_UPDATE_LEAD_TYPE_ENDPOINT       = self::API_BASE_ENDPOINT . '/bulk-update-lead-type';
    const API_UPDATE_LEAD_CLASSIFICATION_ENDPOINT  = self::API_BASE_ENDPOINT . '/update-lead-classification';
    const API_BULK_VERIFY_LEADS_ENDPOINT           = self::API_BASE_ENDPOINT . '/bulk-verify-leads';
    const API_ADD_PUBLIC_COMMENT_ENDPOINT          = self::API_BASE_ENDPOINT . '/add-public-comment';
    const API_UPDATE_BASIC_INFO_ENDPOINT           = self::API_BASE_ENDPOINT . '/update-basic-info';
    const API_UPDATE_BEST_TIME_TO_CONTACT_ENDPOINT = self::API_BASE_ENDPOINT . '/update-best-time-to-contact';
    const API_DELETE_FAILED_QUOTE_COMPANIES        = self::API_BASE_ENDPOINT . '/delete-failed-quote-companies';

    const REQUEST_LEAD_REFERENCE = 'lead_reference';
    const REQUEST_DATA           = 'data';

    /** @var APIConsumer $apiConsumer */
    protected APIConsumer $apiConsumer;

    /** @var LeadProcessingQueueConstraintsRepository $leadProcessingQueueConstraintsRepository */
    protected LeadProcessingQueueConstraintsRepository $leadProcessingQueueConstraintsRepository;

    /** @var PubSubService $pubSubService */
    protected PubSubService $pubSubService;

    /**
     * @param APIConsumer $apiConsumer
     * @param LeadProcessingQueueConstraintsRepository $leadProcessingQueueConstraintsRepository
     * @param PubSubService $pubSubService
     */
    public function __construct(
        APIConsumer                              $apiConsumer,
        LeadProcessingQueueConstraintsRepository $leadProcessingQueueConstraintsRepository,
        PubSubService                            $pubSubService
    )
    {
        $this->apiConsumer                              = $apiConsumer;
        $this->leadProcessingQueueConstraintsRepository = $leadProcessingQueueConstraintsRepository;
        $this->pubSubService                            = $pubSubService;
    }

    /**
     * Retrieves the user id of who locked the lead, if anyone
     *
     * @param string $leadReference
     * @return int
     * @throws Exception
     */
    public function getLeadLockedBy(string $leadReference): int
    {
        return $this->apiConsumer->get(
            self::API_LEAD_LOCKED_BY_ENDPOINT,
            compact("leadReference")
        )->json(APIConsumer::RESPONSE_RESULT);
    }

    /**
     * Reserves a lead for the system
     *
     * @param int $leadId
     * @param int|null $consumerProductId
     * @return bool
     */
    public function reserveLeadToSystem(int $leadId, ?int $consumerProductId = null): bool
    {
        LeadProcessingReservedLead::query()->updateOrCreate(
            [
                LeadProcessingReservedLead::FIELD_LEAD_ID => $leadId
            ],
            [
                LeadProcessingReservedLead::FIELD_LEAD_ID             => $leadId,
                LeadProcessingReservedLead::FIELD_PROCESSOR_ID        => self::SYSTEM_USER_ID,
                LeadProcessingReservedLead::FIELD_CONSUMER_PRODUCT_ID => $consumerProductId ?? $this->getConsumerProductIdForLead($leadId)
            ]
        );

        return true;
    }

    /**
     * Reserves a lead for a given processor.
     *
     * @param int $leadId
     * @param int $processorId
     * @param int|null $consumerProductId
     *
     * @return bool
     */
    public function reserveLead(int $leadId, int $processorId, ?int $consumerProductId = null): bool
    {
        LeadProcessingReservedLead::query()->updateOrCreate(
            [
                LeadProcessingReservedLead::FIELD_LEAD_ID => $leadId
            ],
            [
                LeadProcessingReservedLead::FIELD_LEAD_ID => $leadId,
                LeadProcessingReservedLead::FIELD_PROCESSOR_ID => $processorId,
                LeadProcessingReservedLead::FIELD_CONSUMER_PRODUCT_ID => $consumerProductId ?? $this->getConsumerProductIdForLead($leadId)
            ],
        );

        return true;
    }

    /**
     * Unlocks a lead.
     *
     * @param int      $leadId
     * @param int|null $leadProcessorId
     * @return bool
     */
    public function releaseLead(int $leadId, ?int $leadProcessorId = null): bool
    {
        LeadProcessingReservedLead::query()->where(LeadProcessingReservedLead::FIELD_LEAD_ID, $leadId)->delete();

        return true;
    }

    /**
     * Handles updating a status for a lead.
     *
     * @param string $leadReference
     * @param string $status
     * @param bool $async
     * @return bool
     * @throws Exception
     */
    public function updateLeadStatus(string $leadReference, string $status, bool $async = false): bool
    {
        if($async) {
            SendLegacyAdminRequest::dispatch(
                HttpMethod::METHOD_PATCH,
                self::API_UPDATE_LEAD_STATUS_ENDPOINT,
                compact("leadReference", "status"),
                '',
                '',
                [],
                true
            );

            return true;
        }
        else {
            return $this->apiConsumer->patch(
                self::API_UPDATE_LEAD_STATUS_ENDPOINT,
                compact("leadReference", "status"),
                60
            )->json(APIConsumer::RESPONSE_RESULT);
        }
    }

    /**
     * @deprecated - comments are now handled in Admin2
     *
     * @param string $leadReference
     * @param string $statusReason
     * @return bool
     */
    public function updateLeadStatusReason(string $leadReference, string $statusReason): bool
    {
        return true;
    }

    /**
     * @param string $leadReference
     * @param string $type
     * @return bool
     */
    public function updateLeadType(string $leadReference, string $type): bool
    {
        return $this->apiConsumer->patch(self::API_UPDATE_LEAD_TYPE_ENDPOINT,
            compact("leadReference", "type")
        )->json(APIConsumer::RESPONSE_RESULT);
    }

    /**
     * @param string $leadReference
     * @param string $classification
     * @return bool
     */
    public function updateLeadClassification(string $leadReference, string $classification): bool
    {
        $result = $this->apiConsumer->patch(self::API_UPDATE_LEAD_CLASSIFICATION_ENDPOINT,
            compact("leadReference", "classification")
        )->json(APIConsumer::RESPONSE_RESULT);

        return gettype($result) === 'boolean' ? $result : false;
    }

    /**
     * @param array $leadTypes
     * @return bool
     * @throws Exception
     */
    public function bulkUpdateLeadType(array $leadTypes): bool
    {
        return $this->apiConsumer->post(self::API_BULK_UPDATE_LEAD_TYPE_ENDPOINT,
            compact('leadTypes'),
            60
        )->json(APIConsumer::RESPONSE_RESULT);
    }

    /**
     * @param array $leadReferences
     * @return bool
     * @throws Exception
     */
    public function bulkVerifyLeads(array $leadReferences): bool
    {
        return $this->apiConsumer->post(self::API_BULK_VERIFY_LEADS_ENDPOINT,
            compact('leadReferences'),
            60
        )->json(APIConsumer::RESPONSE_RESULT);
    }

    /**
     *
     * @param string $leadReference
     * @param string $comment
     * @return bool
     *
     *@deprecated - comments are now handled in Admin2
     */
    public function addPublicComment(string $leadReference, string $comment): bool
    {
        return true;
    }

    /**
     * @param string $leadReference
     * @param Collection $data
     * @return bool
     * @throws Exception
     */
    public function updateBasicInfo(string $leadReference, Collection $data): bool
    {
        return !! $this->pubSubService->handle(
            EventCategory::ADMIN2->value,
            EventName::LEAD_BASIC_INFO_UPDATED->value,
            [
                self::REQUEST_LEAD_REFERENCE => $leadReference,
                self::REQUEST_DATA           => $data->toArray()
            ]
        );
    }

    /**
     * @param string $leadReference
     * @param string $bestTimeToContact
     * @return bool
     */
    public function updateBestTimeToContact(string $leadReference, string $bestTimeToContact): bool
    {
        return $this->apiConsumer->patch(self::API_UPDATE_BEST_TIME_TO_CONTACT_ENDPOINT,
            compact("leadReference", "bestTimeToContact")
        )->json(APIConsumer::RESPONSE_RESULT);
    }

    /**
     * Returns a lead processor by their user id.
     *
     * @param int $userId
     * @return LeadProcessor|null
     */
    public function getLeadProcessorByUserId(int $userId): ?LeadProcessor
    {
        /** @var LeadProcessor|null $processor */
        $processor = LeadProcessor::query()->where(LeadProcessor::FIELD_USER_ID, $userId)->first();

        return $processor;
    }

    /**
     * @inheritDoc
     */
    public function findLeadProcessor(int $processorId, bool $withTrashed = true): ?LeadProcessor
    {
        /** @var Builder $baseQuery */
        $baseQuery = $withTrashed
            ? LeadProcessor::withTrashed()
            : LeadProcessor::query();

        /** @var LeadProcessor|null $processor */
        $processor = $baseQuery->where(LeadProcessor::FIELD_ID, $processorId)->first();

        return $processor;
    }

    /**
     * @param EloquentQuote $lead
     * @return bool
     */
    public function removeInitial(EloquentQuote $lead): bool
    {
        LeadProcessingInitial::query()
            ->where(LeadProcessingInitial::FIELD_LEAD_ID, $lead->quoteid)
            ->delete();

        return true;
    }

    /**
     * @param EloquentQuote $lead
     * @param LeadProcessor $processor
     * @param string        $reason
     * @return bool
     */
    public function markLeadAsPendingReview(EloquentQuote $lead, LeadProcessor $processor, string $reason): bool
    {
        LeadProcessingPendingReview::query()->updateOrCreate([
            LeadProcessingPendingReview::FIELD_LEAD_ID => $lead->quoteid,
            LeadProcessingPendingReview::FIELD_USER_ID => $processor->id,
            LeadProcessingPendingReview::FIELD_REASON => $reason
        ], [
            LeadProcessingPendingReview::FIELD_CONSUMER_PRODUCT_ID => $this->getConsumerProductIdForLead($lead->quoteid, $processor)
        ]);

        return true;
    }

    /**
     * Removes a lead from being pending review.
     *
     * @param EloquentQuote $lead
     * @return bool
     */
    public function removePendingReview(EloquentQuote $lead): bool
    {
        LeadProcessingPendingReview::query()
            ->where(LeadProcessingPendingReview::FIELD_LEAD_ID, $lead->quoteid)
            ->delete();

        return true;
    }

    /**
     * Update a pending review lead's reason
     *
     * @param EloquentQuote $lead
     * @param string $reason
     * @return bool
     */
    public function updatePendingReviewReason(EloquentQuote $lead, string $reason): bool
    {
        LeadProcessingPendingReview::where(LeadProcessingPendingReview::FIELD_LEAD_ID, '=', $lead->quoteid)
            ->update([
                LeadProcessingPendingReview::FIELD_REASON => $reason
            ]);

        return true;
    }

    /**
     * @param EloquentQuote $lead
     * @param LeadProcessor $processor
     * @param string $reason
     * @param string|null $existingReason
     * @param int|null $consumerProductId
     *
     * @return bool
     */
    public function markLeadAsUnderReview(EloquentQuote $lead, LeadProcessor $processor, string $reason, ?string $existingReason = null, ?int $consumerProductId = null): bool
    {
        $model = LeadProcessingUnderReview::query()->updateOrCreate(
            [
                LeadProcessingUnderReview::FIELD_LEAD_ID => $lead->quoteid,
            ],
            [
                LeadProcessingUnderReview::FIELD_LEAD_ID => $lead->quoteid,
                LeadProcessingUnderReview::FIELD_USER_ID => $processor->id,
                LeadProcessingUnderReview::FIELD_REASON => $reason,
                LeadProcessingUnderReview::FIELD_EXISTING_REASON => $existingReason,
                LeadProcessingUnderReview::FIELD_LOCATION_ID => $lead->address?->location()?->id,
                LeadProcessingUnderReview::FIELD_CONSUMER_PRODUCT_ID => $consumerProductId ?? $this->getConsumerProductIdForLead($lead->quoteid, $processor)
            ]
        );

        CalculatePotentialRevenue::dispatch($model->getKey());

        return true;
    }

    /**
     * @param EloquentQuote $lead
     * @return bool
     */
    public function removeUnderReview(EloquentQuote $lead): bool
    {
        LeadProcessingUnderReview::query()->where(LeadProcessingUnderReview::FIELD_LEAD_ID, '=', $lead->{EloquentQuote::ID})->delete();

        return true;
    }

    public function removeHeartbeat(EloquentQuote $lead, LeadProcessor $processor): void
    {
        LeadProcessingHeartbeat::query()
            ->where(LeadProcessingHeartbeat::FIELD_LEAD_ID, $lead->quoteid)
            ->where(LeadProcessingHeartbeat::FIELD_LEAD_PROCESSOR_ID, $processor->id)
            ->delete();
    }

    /**
     * Returns whether a heartbeat exists for a lead and a lead processor.
     *
     * @param EloquentQuote $lead
     * @param LeadProcessor $processor
     * @return bool
     */
    public function hasHeartbeat(EloquentQuote $lead, LeadProcessor $processor): bool
    {
        return LeadProcessingHeartbeat::query()
                ->where(LeadProcessingHeartbeat::FIELD_LEAD_ID, $lead->quoteid)
                ->where(LeadProcessingHeartbeat::FIELD_LEAD_PROCESSOR_ID, $processor->id)
                ->first() !== null;
    }

    /**
     * Handles retrieving the heartbeat for a given lead and processor.
     *
     * @param EloquentQuote $lead
     * @param LeadProcessor $processor
     * @return LeadProcessingHeartbeat|null
     */
    public function getHeartbeat(EloquentQuote $lead, LeadProcessor $processor): ?LeadProcessingHeartbeat
    {
        /** @var LeadProcessingHeartbeat|null $model */
        $model = LeadProcessingHeartbeat::query()
            ->where(LeadProcessingHeartbeat::FIELD_LEAD_ID, $lead->quoteid)
            ->where(LeadProcessingHeartbeat::FIELD_LEAD_PROCESSOR_ID, $processor->id)
            ->first();

        return $model;
    }

    /**
     * Processes a given heartbeat.
     *
     * @param EloquentQuote $lead
     * @param LeadProcessor $processor
     * @return void
     */
    public function processHeartbeat(EloquentQuote $lead, LeadProcessor $processor): void
    {
        LeadProcessingHeartbeat::query()
            ->updateOrCreate(
                [
                    LeadProcessingHeartbeat::FIELD_LEAD_ID => $lead->quoteid,
                    LeadProcessingHeartbeat::FIELD_LEAD_PROCESSOR_ID => $processor->id
                ], [
                    LeadProcessingHeartbeat::FIELD_LAST_HEARTBEAT => Carbon::now()->timestamp,
                    LeadProcessingHeartbeat::FIELD_CONSUMER_PRODUCT_ID => $this->getConsumerProductIdForLead($lead->quoteid, $processor)
                ]
            );
    }

    /**
     * @param int $offset
     * @return LeadProcessingTimeZoneConfiguration|Model|null
     */
    public function getTimezoneConfigurationByStandardUTCOffset(int $offset): LeadProcessingTimeZoneConfiguration|Model|null
    {
        return LeadProcessingTimeZoneConfiguration::query()
            ->where(LeadProcessingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET, $offset)
            ->first();
    }

    /**
     * @return Collection
     */
    public function getAllTimezoneConfigurations(): Collection
    {
        return LeadProcessingTimeZoneConfiguration::all();
    }

    /**
     * @param Collection $timezones
     * @return bool
     */
    public function saveTimezoneConfigurations(Collection $timezones): bool
    {
        foreach($timezones as $timezone) {
            LeadProcessingTimeZoneConfiguration::where(LeadProcessingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET, '=', $timezone[LeadProcessingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET])
                ->update([
                    LeadProcessingTimeZoneConfiguration::FIELD_NAME => $timezone[LeadProcessingTimeZoneConfiguration::FIELD_NAME],
                    LeadProcessingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR => $timezone[LeadProcessingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR],
                    LeadProcessingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR => $timezone[LeadProcessingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR]
                ]);
        }

        return true;
    }

    /**
     * @param int $offset
     * @return LeadProcessingCallingTimeZoneConfiguration|Model|null
     */
    public function getCallingTimezoneConfigurationByStandardUTCOffset(int $offset): LeadProcessingCallingTimeZoneConfiguration|Model|null
    {
        return LeadProcessingCallingTimeZoneConfiguration::query()
            ->where(LeadProcessingCallingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET, $offset)
            ->first();
    }

    /**
     * @return Collection
     */
    public function getAllCallingTimezoneConfigurations(): Collection
    {
        return LeadProcessingCallingTimeZoneConfiguration::all();
    }

    /**
     * @param Collection $timezones
     * @return bool
     */
    public function saveCallingTimezoneConfigurations(Collection $timezones): bool
    {
        foreach($timezones as $timezone) {
            LeadProcessingCallingTimeZoneConfiguration::where(LeadProcessingCallingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET, '=', $timezone[LeadProcessingCallingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET])
                ->update([
                    LeadProcessingCallingTimeZoneConfiguration::FIELD_NAME => $timezone[LeadProcessingCallingTimeZoneConfiguration::FIELD_NAME],
                    LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR => $timezone[LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR],
                    LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR => $timezone[LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR]
                ]);
        }

        return true;
    }

    /**
     * Returns the global configuration for lead processing.
     *
     * @return LeadProcessingConfiguration|null
     */
    public function getLeadProcessingConfiguration(): ?LeadProcessingConfiguration
    {
        /** @var LeadProcessingConfiguration|null $model */
        $model = LeadProcessingConfiguration::query()->first();

        return $model;
    }

    /**
     * @param int $timeZoneOpeningDelayMin
     * @param int $leadRecencyThresholdSec
     * @param int $minimumReviewTime
     * @param int $leadProcessableDelaySec
     * @param int $checkNextLeadInterval
     * @param int $lastLeadCreatedInterval
     * @return bool
     */
    public function saveLeadProcessingConfiguration(
        int $timeZoneOpeningDelayMin,
        int $leadRecencyThresholdSec,
        int $minimumReviewTime,
        int $leadProcessableDelaySec,
        int $checkNextLeadInterval,
        int $lastLeadCreatedInterval
    ): bool
    {
        return (bool) LeadProcessingConfiguration::query()
            ->first()
            ->update([
                LeadProcessingConfiguration::FIELD_TIME_ZONE_OPENING_DELAY_IN_MINUTES => $timeZoneOpeningDelayMin,
                LeadProcessingConfiguration::FIELD_LEAD_RECENCY_THRESHOLD_IN_SECONDS => $leadRecencyThresholdSec,
                LeadProcessingConfiguration::FIELD_MINIMUM_REVIEW_TIME => $minimumReviewTime,
                LeadProcessingConfiguration::FIELD_LEAD_PROCESSABLE_DELAY_SECONDS => $leadProcessableDelaySec,
                LeadProcessingConfiguration::FIELD_CHECK_NEXT_LEAD_INTERVAL_SECONDS => $checkNextLeadInterval,
                LeadProcessingConfiguration::FIELD_LAST_LEAD_CREATED_INTERVAL_MIN => $lastLeadCreatedInterval
            ]);
    }

    /**
     * @param int $leadId
     * @return LeadProcessingAllocation|null|Model
     */
    public function getLeadProcessingAllocationByLeadId(int $leadId): LeadProcessingAllocation|null|Model
    {
        return LeadProcessingAllocation::query()->where(LeadProcessingAllocation::FIELD_LEAD_ID, $leadId)->first();
    }

    /**
     * @param int $leadId
     * @return Builder
     */
    public function getUndeliveredQuoteCompaniesQuery(int $leadId): Builder
    {
        return EloquentQuoteCompany::query()
            ->where(EloquentQuoteCompany::QUOTE_ID, $leadId)
            ->where(EloquentQuoteCompany::DELIVERED, EloquentQuoteCompany::NOT_DELIVERED);
    }

    /**
     * @param int $leadId
     * @return EloquentQuoteCompany[]|Collection
     */
    public function getUndeliveredQuoteCompanies(int $leadId): Collection
    {
        return $this->getUndeliveredQuoteCompaniesQuery($leadId)->get();
    }

    /**
     * @param int $leadId
     * @return EloquentQuoteCompany[]|Collection
     */
    public function getDeliveredQuoteCompanies(int $leadId): Collection
    {
        return EloquentQuoteCompany::query()
            ->where(EloquentQuoteCompany::QUOTE_ID, $leadId)
            ->where(EloquentQuoteCompany::DELIVERED, EloquentQuoteCompany::IS_DELIVERED)
            ->whereIn(EloquentQuoteCompany::LEAD_COST_TYPE, [EloquentQuoteCompany::VALUE_LEAD_COST_TYPE_PAYPERLEAD, EloquentQuoteCompany::VALUE_LEAD_COST_TYPE_REJECTED_APPOINTMENT_LEAD])
            ->get();
    }

    /**
     * @param int $leadId
     * @return LeadCampaign[]|Collection
     */
    public function getDeliveredQuoteCompanyCampaigns(int $leadId): Collection
    {
        $quoteCompanies = $this->getDeliveredQuoteCompanies($leadId)->load(EloquentQuoteCompany::RELATION_LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION);
        $salesTypeConfigurations = $quoteCompanies->map(function($qc){return $qc->{EloquentQuoteCompany::RELATION_LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION};});
        return LeadCampaign::query()->whereIn(LeadCampaign::ID, $salesTypeConfigurations->pluck(LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID))->get();
    }

    /**
     * @param string $leadReference
     * @throws Exception
     */
    public function deleteFailedQuoteCompanies(string $leadReference): void
    {
        $this->apiConsumer->post(self::API_DELETE_FAILED_QUOTE_COMPANIES,
            compact("leadReference")
        );
    }

    /**
     * Create a lead processor history entry
     *
     * @param int $processorId
     * @param int $leadId
     * @param int $queueId
     * @param string $action
     * @return int
     */
    public function recordProcessorHistory(int $processorId, int $leadId, int $queueId, string $action): int
    {
        if(!in_array($action, LeadProcessingHistory::ACTIONS)) {
            return false;
        }

        /** @var LeadProcessor|null $processor */
        $processor = $this->findLeadProcessor($processorId);

        $history = new LeadProcessingHistory();

        $history->{LeadProcessingHistory::FIELD_LEAD_ID}                = $leadId;
        $history->{LeadProcessingHistory::FIELD_LEAD_PROCESSOR_ID}      = $processorId;
        $history->{LeadProcessingHistory::FIELD_QUEUE_CONFIGURATION_ID} = $queueId;
        $history->{LeadProcessingHistory::FIELD_ACTION}                 = $action;
        $history->{LeadProcessingHistory::FIELD_CONSUMER_PRODUCT_ID}    = $this->getConsumerProductIdForLead($leadId, $processor);

        $history->save();

        return $history->{LeadProcessingHistory::FIELD_ID};
    }

    /**
     * @param LeadProcessor $processor
     * @param int $startTimestamp
     * @return LeadProcessingHistory[]|array|\Illuminate\Database\Eloquent\Collection|\Illuminate\Database\Query\Builder[]|Collection
     */
    public function getProcessorHistory(LeadProcessor $processor, int $startTimestamp = 0): Collection
    {
        $historyQuery = LeadProcessingHistory::where(LeadProcessingHistory::FIELD_LEAD_PROCESSOR_ID, '=', $processor->{LeadProcessor::FIELD_ID})
            ->orderBy(LeadProcessingHistory::CREATED_AT, 'desc');

        if($startTimestamp > 0
        && $startTimestamp < Carbon::now()->getTimestamp()) {
            $historyQuery->where(DB::raw("UNIX_TIMESTAMP(".LeadProcessingHistory::CREATED_AT.")"), '>=', $startTimestamp);
        }
        else {
            $historyQuery->whereRaw("UNIX_TIMESTAMP(".LeadProcessingHistory::CREATED_AT.") >= ".Carbon::now()->subDays(7)->timestamp);
        }

        return $historyQuery->get();
    }

    /**
     * Shortcut helper for setting status & reason at the same time.
     *
     * @param EloquentQuote $lead
     * @param string        $status
     * @param string        $reason
     * @return bool
     */
    public function updateStatusAndReason(EloquentQuote $lead, string $status, string $reason): bool
    {
        return $this->updateLeadStatus($lead->{EloquentQuote::REFERENCE}, $status);
    }

    /**
     * Returns 'system' processor, to be used for automated functionality
     * Note: User and Processor are 'deleted' so as not to appear in UI
     *
     * @return LeadProcessor
     */
    public function getSystemProcessor(): LeadProcessor
    {
        $systemUser = User::where(User::FIELD_NAME, User::SYSTEM_USER_RESERVED_NAME)
            ->where(User::FIELD_EMAIL, User::SYSTEM_USER_RESERVED_EMAIL)
            ->withTrashed()
            ->first();
        if (!$systemUser){$systemUser = $this->createSystemUser();}

        $systemProcessor = LeadProcessor::where(LeadProcessor::FIELD_USER_ID, $systemUser->id)
            ->withTrashed()
            ->first();
        if (!$systemProcessor){$systemProcessor = $this->createSystemProcessor($systemUser);}

        return $systemProcessor;
    }

    /**
     * @return User
     */
    public function createSystemUser(): User
    {
        $user =  User::create([
            User::FIELD_NAME  => User::SYSTEM_USER_RESERVED_NAME,
            User::FIELD_EMAIL => User:: SYSTEM_USER_RESERVED_EMAIL
        ]);
        $user->delete();
        return $user;
    }

    /**
     * @param User $systemUser
     * @return LeadProcessor
     */
    private function createSystemProcessor(User $systemUser): LeadProcessor
    {
        $processor = LeadProcessor::create([
            LeadProcessor::FIELD_USER_ID => $systemUser->id,
            LeadProcessor::FIELD_LEAD_PROCESSING_TEAM_ID => LeadProcessingTeam::NO_TEAM_ASSIGNED_ID
        ]);
        $processor->delete();
        return $processor;
    }

    /**
     * @param LeadProcessor $processor
     * @param int $leadId
     *
     * @return LeadProcessingReservedLead|null
     */
    public function getReservedLeadByProcessorAndLeadId(LeadProcessor $processor, int $leadId): ?LeadProcessingReservedLead
    {
        /** @var LeadProcessingReservedLead|null $reservedLead */
        $reservedLead = LeadProcessingReservedLead::query()
            ->where(LeadProcessingReservedLead::FIELD_PROCESSOR_ID, $processor->id)
            ->where(LeadProcessingReservedLead::FIELD_LEAD_ID, $leadId)
            ->first();

        return $reservedLead;
    }

    /**
     * Finds consumer product ID for a lead.
     * - If the lead processor provided, performs search against the combination of lead and processor in reserved leads.
     * - Otherwise, tries looking for the consumer models connected to the given lead ID.
     *
     * @inheritDoc
     */
    public function getConsumerProductIdForLead(int $leadId, ?LeadProcessor $processor = null): ?int
    {
        if($processor) {
            /** @var LeadProcessingReservedLead|null $reservedLead */
            $reservedLead = $this->getReservedLeadByProcessorAndLeadId($processor, $leadId);
            if($reservedLead && $reservedLead->{LeadProcessingReservedLead::FIELD_CONSUMER_PRODUCT_ID})
                return $reservedLead->{LeadProcessingReservedLead::FIELD_CONSUMER_PRODUCT_ID};
        }

        /** @var ConsumerProduct|null $consumerProduct */
        $consumerProduct = Consumer::query()->where(Consumer::FIELD_LEGACY_ID, $leadId)->first()
            ?->{Consumer::RELATION_CONSUMER_PRODUCT}->first();

        return $consumerProduct
            ?->{ConsumerProduct::FIELD_ID}
            ?? null;
    }

    /**
     * @param int $leadId
     * @return int
     */
    public function getRemainingLegsForAppointmentLead(int $leadId): int
    {
        /** @var ProductAssignmentRepository $par */
        $par = app(ProductAssignmentRepository::class);

        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = ConsumerProduct::query()
            ->whereHas(ConsumerProduct::RELATION_CONSUMER, function($has) use ($leadId) {
                $has->where(Consumer::FIELD_LEGACY_ID, $leadId);
            })
            ->whereHas(ConsumerProduct::RELATION_SERVICE_PRODUCT.'.'.ServiceProduct::RELATION_PRODUCT, function($has) {
                $has->where(ProductModel::FIELD_NAME, ProductEnum::LEAD->value);
            })
            ->firstOrFail();

        $cancelledRejectedLegs = $consumerProduct
            ->{ConsumerProduct::RELATION_APPOINTMENT_PROCESSING_ALLOCATIONS}
            ?->where(AppointmentProcessingAllocation::FIELD_CANCELLED_REJECTED, true)
            ?->count() ?? 0;

        return $consumerProduct->{ConsumerProduct::FIELD_CONTACT_REQUESTS} - max($cancelledRejectedLegs, $par->findSoldProductAssignmentsByConsumerProduct($consumerProduct)->count());
    }

    /**
     * @param int $leadId
     * @return array
     */
    public function getSoldAppointmentCompanies(int $leadId): array
    {
        return ProductAssignment::query()
            ->join(Company::TABLE, function($join) {
                $join->on(
                    Company::TABLE.'.'.Company::FIELD_ID,
                    '=',
                    ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_COMPANY_ID
                );
            })
            ->whereHas(ProductAssignment::RELATION_CONSUMER, function($has) use ($leadId) {
                $has->where(Consumer::TABLE.'.'.Consumer::FIELD_LEGACY_ID, $leadId);
            })
            ->whereHas(ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_SERVICE_PRODUCT.'.'.ServiceProduct::RELATION_PRODUCT, function($has) {
                $has->where(ProductModel::FIELD_NAME, ProductEnum::APPOINTMENT->value);
            })
            ->whereDoesntHave(ProductAssignment::RELATION_PRODUCT_REJECTIONS)
            ->whereDoesntHave(ProductAssignment::RELATION_PRODUCT_CANCELLATIONS)
            ->select([
                Company::TABLE.'.'.Company::FIELD_LEGACY_ID,
                Company::TABLE.'.'.Company::FIELD_ID
            ])
            ->get()
            ->mapWithKeys(function($companyIds) {
                return [$companyIds[Company::FIELD_ID] => $companyIds[Company::FIELD_LEGACY_ID]];
            })
            ->toArray();
    }
}
