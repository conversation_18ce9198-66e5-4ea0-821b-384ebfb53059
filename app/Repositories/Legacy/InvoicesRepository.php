<?php

namespace App\Repositories\Legacy;

use App\Contracts\Repositories\InvoicesRepositoryContract;
use App\Models\Legacy\EloquentCreditLog;
use App\Models\Legacy\EloquentInvoice;
use App\Models\Legacy\EloquentInvoiceItem;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Odin\Company;
use App\Services\Legacy\BaseLegacySyncService;
use Exception;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use JetBrains\PhpStorm\ArrayShape;
use Spatie\FlareClient\Http\Exceptions\BadResponse;


class InvoicesRepository implements InvoicesRepositoryContract
{

    public function __construct(
        protected BaseLegacySyncService $legacySyncService,
    ){}

    /**
     * @param int $companyId
     * @return array
     */
    #[ArrayShape(['total' => "int", 'paid' => "int", 'unpaid' => "int", 'tobeinvoiced' => "\Illuminate\Support\HigherOrderCollectionProxy|mixed", 'nonrejectable' => "\Illuminate\Support\HigherOrderCollectionProxy|mixed", 'available_credit' => "mixed", 'credit_amount' => "mixed", 'voucher_amount' => "mixed"])]
    public function getCompanyInvoicesSummary(int $companyId): array
    {
        /** @var Company $company */
        $company = Company::query()->findOrFail($companyId);
        $legacyCompanyId = $company->legacy_id;

        $companyInvoices = $this->getInitialInvoiceChargesForCompany($legacyCompanyId);
        $nonRejectableCharges = $this->getNonRejectableLeadCostForCompany($legacyCompanyId);
        $upcomingCharges = $this->getUpcomingChargesForCompany($legacyCompanyId);
        $companyCreditAmounts = $this->getAccountCreditForCompany($legacyCompanyId);
        [$unpaidInvoices, $paidInvoices] = $this->splitPaidUnpaidInvoices($companyInvoices);
        $totalInvoicesAmount = $paidInvoices + $unpaidInvoices;

        return [
            'total'               => $totalInvoicesAmount,
            'paid'                => $paidInvoices,
            'unpaid'              => $unpaidInvoices,
            'tobeinvoiced'        => $upcomingCharges->TotalAmount,
            'nonrejectable'       => $nonRejectableCharges->TotalAmount,
            'available_credit'    => $companyCreditAmounts['available_credit'],
            'credit_amount'       => $companyCreditAmounts['credit_amount'],
            'voucher_amount'      => $companyCreditAmounts['voucher_amount'],
            'signup_bonus_amount' => $companyCreditAmounts['signup_bonus_amount'],
        ];
    }

    /**
     * @param Collection $invoices
     * @return array|int[]
     */
    protected function splitPaidUnpaidInvoices(Collection $invoices): array
    {
        $paidInvoices = 0;
        $unpaidInvoices = 0;
        $invoices->each(function ($item) use (&$unpaidInvoices, &$paidInvoices) {
            if ($item->status == EloquentInvoice::VALUE_STATUS_INITIAL) {
                $unpaidInvoices += $item->TotalAmount;
            }
            if ($item->status == EloquentInvoice::VALUE_STATUS_PAID) {
                $paidInvoices += $item->TotalAmount;
            }
        });
        return [$unpaidInvoices, $paidInvoices];
    }

    /**
     * @param int $companyId
     * @return Collection
     */
    protected function getNonRejectableLeadCostForCompany(int $companyId): EloquentQuote
    {
        return EloquentQuote::query()->selectRaw('ROUND(Sum('.EloquentQuoteCompany::COST.'),2) as TotalAmount')
            ->join(EloquentQuoteCompany::TABLE, EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::QUOTE_ID, '=', EloquentQuote::TABLE.'.'.EloquentQuote::QUOTE_ID)
            ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::COMPANY_ID, '=',$companyId)
            ->whereIn(EloquentQuote::TABLE.'.'.EloquentQuote::STATUS, array(EloquentQuote::VALUE_STATUS_INITIAL, EloquentQuote::VALUE_STATUS_ALLOCATED))
            ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::DELIVERED,'=', EloquentQuoteCompany::IS_DELIVERED)
            ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::CHARGEABLE,'=', EloquentQuoteCompany::IS_CHARGEABLE)
            ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::CHARGE_STATUS,'=', EloquentQuoteCompany::VALUE_CHARGE_STATUS_INITIAL)
            ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::INVOICE_ITEM_ID,'=', EloquentQuoteCompany::VALUE_UNINVOICE_ID)
            ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::COST,'>', 0)
            ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::BILLING_VERSION,'v1')
            ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::TIMESTAMP_REJECTION_EXPIRY,'<', time())
            ->get()->first();
    }

    /**
     * @param int $companyId
     * @return Collection
     */
    protected function getUpcomingChargesForCompany(int $companyId): EloquentQuote
    {
        return EloquentQuote::query()->selectRaw('ROUND(Sum('.EloquentQuoteCompany::COST.'),2) as TotalAmount')
            ->join(EloquentQuoteCompany::TABLE, EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::QUOTE_ID, '=', EloquentQuote::TABLE.'.'.EloquentQuote::QUOTE_ID)
            ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::COMPANY_ID, '=',$companyId)
            ->whereIn(EloquentQuote::TABLE.'.'.EloquentQuote::STATUS, array(EloquentQuote::VALUE_STATUS_INITIAL, EloquentQuote::VALUE_STATUS_ALLOCATED))
            ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::DELIVERED,'=', EloquentQuoteCompany::IS_DELIVERED)
            ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::CHARGEABLE,'=', EloquentQuoteCompany::IS_CHARGEABLE)
            ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::CHARGE_STATUS,'=', EloquentQuoteCompany::VALUE_CHARGE_STATUS_INITIAL)
            ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::INVOICE_ITEM_ID,'=', EloquentQuoteCompany::VALUE_UNINVOICE_ID)
            ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::BILLING_VERSION,'v1')
            ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::COST,'>', 0)
            ->get()->first();
    }

    /**
     * @param int $companyId
     * @return Collection
     */
    protected function getInitialInvoiceChargesForCompany(int $companyId): Collection
    {
        return EloquentInvoice::query()->selectRaw(EloquentInvoice::TABLE.'.'.EloquentInvoice::STATUS.','.
                'ROUND(Sum(('.EloquentInvoiceItem::QUANTITY.' * '.EloquentInvoiceItem::ITEM_EX_TAX_PRICE.') + '.EloquentInvoiceItem::TOTAL_TAX_AMOUNT.'),2) as TotalAmount'
            )->join(EloquentInvoiceItem::TABLE, EloquentInvoiceItem::TABLE.'.'.EloquentInvoiceItem::INVOICE_ID, '=', EloquentInvoice::TABLE.'.'.EloquentInvoice::INVOICE_ID)
            ->where(EloquentInvoice::TABLE.'.'.EloquentInvoice::COMPANY_ID, '=', $companyId)
            ->whereIn(EloquentInvoice::TABLE.'.'.EloquentInvoice::STATUS, array(EloquentInvoice::VALUE_STATUS_PAID, EloquentInvoice::VALUE_STATUS_INITIAL))
            ->groupBy(EloquentInvoice::TABLE.'.'.EloquentInvoice::INVOICE_ID)
            ->get();
    }

    /**
     * @param int|null $legacyCompanyId
     * @param array $legacyCompanyIds
     * @return Builder
     */
    public function getCompanyCreditsQuery(
        ?int $legacyCompanyId = null,
        array $legacyCompanyIds = [],
    ): Builder
    {
        return EloquentCreditLog::query()->selectRaw(
            EloquentCreditLog::TABLE . '.' . EloquentCreditLog::TYPE . ',
            ROUND(sum(' . EloquentCreditLog::TABLE . '.' . EloquentCreditLog::AMOUNT . '),2) as outstanding ')
            ->addSelect(EloquentCreditLog::TABLE . '.' . EloquentCreditLog::COMPANY_ID)
            ->when($legacyCompanyId, fn($query) => $query->where(EloquentCreditLog::TABLE . '.' . EloquentCreditLog::COMPANY_ID, '=', $legacyCompanyId))
            ->when(filled($legacyCompanyIds), fn($query) => $query->whereIn(EloquentCreditLog::TABLE . '.' . EloquentCreditLog::COMPANY_ID, $legacyCompanyIds))
            ->groupBy(EloquentCreditLog::TABLE . '.' . EloquentCreditLog::TYPE)
            ->having('outstanding', '>' , '0');
    }

    /**
     * @param int $legacyCompanyId
     * @return array
     */
    protected function getAccountCreditForCompany(int $legacyCompanyId): array
    {
        $creditAmount = 0;
        $voucherAmount = 0;
        $signupBonusAmount = 0;

        $companyCredit = $this->getCompanyCreditsQuery(
            legacyCompanyId: $legacyCompanyId
        )->get();

        foreach ($companyCredit as $credit) {
            switch ($credit->type) {
                case EloquentCreditLog::TYPE_CREDIT:
                    $creditAmount = $credit->outstanding;
                    break;
                case EloquentCreditLog::TYPE_VOUCHER:
                    $voucherAmount = $credit->outstanding;
                    break;
                case EloquentCreditLog::TYPE_SIGNUP_BONUS:
                    $signupBonusAmount = $credit->outstanding;
                    break;
            }
        }

        return array(
            'available_credit'    => $creditAmount + $voucherAmount + $signupBonusAmount,
            'credit_amount'       => $creditAmount,
            'voucher_amount'      => $voucherAmount,
            'signup_bonus_amount' => $signupBonusAmount
        );
    }


    /**
     * @param int $companyLegacyId
     * @param string $description
     * @param array $lineItems
     * @param float|int $totalPrice
     * @param float|int $creditAmount
     * @return array
     * @throws BadResponse
     * @throws Exception
     */
    public function createInvoice(int $companyLegacyId, string $description, array $lineItems, float|int $totalPrice, float|int $creditAmount): array
    {
        $data = [
            'invoice_amount' => $totalPrice,
            'credit_amount' => $creditAmount,
            'companyid' => $companyLegacyId,
            'invoice_description' => $description,
            'line_items' => $lineItems
        ];

        $createInvoiceResponse = $this->legacySyncService->post("invoice/create", $data);

        [
            'invoice_id' => $invoiceId,
            'invoice_url' => $invoiceUrl
        ] = $createInvoiceResponse->json();

        if (!$invoiceId) {
            throw new BadResponse('Legacy response failed to return a required invoice_id parameter.');
        }
        if (!$invoiceUrl) {
            throw new BadResponse('Legacy response failed to return a required invoice_url parameter.');
        }

        return [$invoiceId, $invoiceUrl];
    }


    /**
     * @param int $invoiceId
     * @param int $companyLegacyId
     * @return false
     * @throws BadResponse
     * @throws Exception
     */
    public function chargeInvoice(int $invoiceId, int $companyLegacyId): bool
    {
        $chargeInvoiceResponse = $this->legacySyncService->post(
            "/invoice/$invoiceId/pay-now", ['companyid' => $companyLegacyId]
        )->json();

        $data = $chargeInvoiceResponse['data'] ?? null;

        if (!$data) {
            throw new BadResponse("Legacy invoice $invoiceId created but not charged.");
        }

        [
            'status'  => $status,
            'message' => $message,
        ] = $data;

        if (is_null($status) || !$message) {
            throw new BadResponse("Legacy invoice $invoiceId created but not charged.");
        }

        return $data['paid'] ?? false;
    }


    /**
     * @param int $invoiceId
     * @param int $companyLegacyId
     * @return bool
     * @throws BadResponse
     * @throws Exception
     */
    public function cancelInvoice(int $invoiceId, int $companyLegacyId): bool
    {
        $cancelInvoiceResponse = $this->legacySyncService->post(
            "/invoice/$invoiceId/cancel", ['companyid' => $companyLegacyId]
        )->json();

        $data = $cancelInvoiceResponse['data'] ?? null;

        if (!$data) {
            throw new BadResponse("Legacy invoice could not be cancelled.");
        }

        return $data['status'] ?? false;
    }
}
