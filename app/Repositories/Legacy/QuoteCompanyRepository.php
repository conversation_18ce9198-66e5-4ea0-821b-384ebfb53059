<?php

namespace App\Repositories\Legacy;

use App\Contracts\Repositories\QuoteCompanyRepositoryContract;
use App\DataModels\TotaledChargeableAndDeliveredLeadsDataModel;
use App\DataModels\TotaledRejectedLeadsDataModel;
use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Legacy\LeadCategory;
use App\Models\Odin\Consumer;
use App\Models\Odin\ProductAssignment;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class QuoteCompanyRepository implements QuoteCompanyRepositoryContract
{
    /**
     * @inheritDoc
     */
    public function getMostRecentQuoteCompanyByCompanyId(int $companyId): EloquentQuoteCompany|Builder|null
    {
        return EloquentQuoteCompany::query()
            ->where(EloquentQuoteCompany::COMPANY_ID, $companyId)
            ->where(EloquentQuoteCompany::SOLD_STATUS, EloquentQuoteCompany::VALUE_SOLD_STATUS_SOLD)
            ->orderBy(EloquentQuoteCompany::TIMESTAMP_DELIVERED, 'DESC')
            ->first();
    }

    /**
     * This is most recent in terms of ID, not necessarily timestamp. These are effectively the same things and make
     * an enormous difference in the speed of this query
     *
     * @inheritDoc
     */
    public function getMostRecentQuoteCompaniesByCompanyIds(array $companyIds): array
    {
        return EloquentQuoteCompany::query()
            ->select([
                EloquentQuoteCompany::COMPANY_ID,
                EloquentQuoteCompany::TIMESTAMP_ADDED
            ])
            ->whereIn(EloquentQuoteCompany::QUOTE_COMPANY_ID, function (\Illuminate\Database\Query\Builder $query) use (
                $companyIds
            ) {
                $query->selectRaw('MAX(quotecompanyid)')
                    ->from(EloquentQuoteCompany::TABLE)
                    ->whereIn(EloquentQuoteCompany::COMPANY_ID, $companyIds)
                    ->where(EloquentQuoteCompany::CHARGEABLE, true)
                    ->where(EloquentQuoteCompany::DELIVERED, true)
                    ->groupBy(EloquentQuoteCompany::COMPANY_ID);
            })
            ->pluck(EloquentQuoteCompany::TIMESTAMP_ADDED, EloquentQuoteCompany::COMPANY_ID)
            ->toArray();
    }

    /**
     * @inheritDoc
     */
    public function getCompanyIdsThatPurchasedLeadsInLast30Days(): array
    {
        return EloquentQuoteCompany::query()
            ->chargeableAndDelivered()
            ->where(EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY, '>=', Carbon::now()->subDays(30)->timestamp)
            ->groupBy(EloquentQuoteCompany::COMPANY_ID)
            ->pluck(EloquentQuoteCompany::COMPANY_ID)->toArray();
    }

    /**
     * @inheritDoc
     */
    public function getTotaledChargeableAndDeliveredLeadsByCompany(
        int $companyId = 0,
        int $timestampBudgetStart = 0
    ): ?TotaledChargeableAndDeliveredLeadsDataModel
    {
        /** @var Collection $result */
        $result = $this->getTotaledChargeableAndDeliveredLeadsQuery($companyId, $timestampBudgetStart)->get();
        if (!empty($result)) {
            $totalSpent = $result->first();
            return new TotaledChargeableAndDeliveredLeadsDataModel($totalSpent->total_cost_spent, $totalSpent->total_count_spent);
        }
        return null;
    }

    /**
     * @inheritDoc
     */
    public function getTotaledChargeableAndDeliveredLeads(
        array $companyIds,
        int $timestampBudgetStart = 0
    ): array
    {
        /** @var Collection $result */
        $result = $this->getTotaledChargeableAndDeliveredLeadsQueryForCompanies($companyIds, $timestampBudgetStart)->get();
        $results = [];
        foreach($companyIds as $id) {
            $item = $result->where(EloquentQuoteCompany::COMPANY_ID, $id)->first();
            $results[$id] = $item ? new TotaledChargeableAndDeliveredLeadsDataModel($item->total_cost_spent, $item->total_count_spent) : null;
        }

        return $results;
    }

    /**
     * @param int $companyId
     * @param int $timestampBudgetStart
     * @return Builder
     */
    private function getTotaledChargeableAndDeliveredLeadsQuery(
        int $companyId = 0,
        int $timestampBudgetStart = 0
    ): Builder
    {
        $connection = (new EloquentQuoteCompany())->getConnection();
        /** @var Builder $query */
        $query = EloquentQuoteCompany::query()
            ->select([
                $connection->raw("COUNT(cost) as `total_count_spent`"),
                $connection->raw("SUM(cost) as `total_cost_spent`"),
            ])
            ->chargeableAndDelivered()
            ->where(EloquentQuoteCompany::COMPANY_ID, $companyId)
            ->where(EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY, '>=', $timestampBudgetStart);

        return $query;
    }

    /**
     * @param array $companyIds
     * @param int   $timestampBudgetStart
     * @return Builder
     */
    private function getTotaledChargeableAndDeliveredLeadsQueryForCompanies(
        array $companyIds,
        int $timestampBudgetStart = 0
    ): Builder
    {
        $connection = (new EloquentQuoteCompany())->getConnection();
        /** @var Builder $query */
        $query = EloquentQuoteCompany::query()
            ->select([
                EloquentQuoteCompany::COMPANY_ID,
                $connection->raw("COUNT(cost) as `total_count_spent`"),
                $connection->raw("SUM(cost) as `total_cost_spent`"),
            ])
            ->chargeableAndDelivered()
            ->whereIn(EloquentQuoteCompany::COMPANY_ID, $companyIds)
            ->where(EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY, '>=', $timestampBudgetStart)
            ->groupBy(EloquentQuoteCompany::COMPANY_ID);

        return $query;
    }

    /**
     * @param Builder $query
     * @param int $leadsPurchasedCountTimestamp
     * @return void
     */
    public static function applyLeadCostAndLeadCount(Builder $query, int $leadsPurchasedCountTimestamp): void
    {
        $query->withSum([EloquentCompany::RELATION_QUOTE_COMPANIES . " as lead_cost" => function (Builder $query) use ($leadsPurchasedCountTimestamp) {
            /** @var Builder|EloquentQuoteCompany $query */
            $query
                ->chargeableAndDelivered()->where(EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY, '>=', $leadsPurchasedCountTimestamp);
        }], EloquentQuoteCompany::COST)
            ->withCount([EloquentCompany::RELATION_QUOTE_COMPANIES . " as lead_count" => function (Builder $query) use ($leadsPurchasedCountTimestamp) {
                /** @var Builder|EloquentQuoteCompany $query */
                $query
                    ->chargeableAndDelivered()->where(EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY, '>=', $leadsPurchasedCountTimestamp);
            }]);
    }

    /**
     * @inheritDoc
     */
    public function getTotaledRejectedLeads(
        array $companyIds,
        int $timestampBudgetStart = 0
    ): array
    {
        /** @var Collection $result */
        $result = $this->getTotaledRejectedLeadsQueryForCompanies($companyIds, $timestampBudgetStart)->get();
        $results = [];
        foreach($companyIds as $id) {
            $item = $result->where(EloquentQuoteCompany::COMPANY_ID, $id)->first();
            $results[$id] = $item ? new TotaledRejectedLeadsDataModel($item->total_cost_rejection, $item->total_count_rejection) : null;
        }

        return $results;
    }

    /**
     * @inheritDoc
     */
    public function getTotaledRejectedLeadsByCompany(
        int $companyId = 0,
        int $timestampBudgetStart = 0
    ): ?TotaledRejectedLeadsDataModel
    {
        /** @var Collection $result */
        $result = $this->getTotaledRejectedLeadsQuery($companyId, $timestampBudgetStart)->get();
        if (!empty($result)) {
            $totalRejected = $result->first();
            return new TotaledRejectedLeadsDataModel($totalRejected->total_cost_rejection, $totalRejected->total_count_rejection);
        }
        return null;
    }

    /**
     * @param int $companyId
     * @param int $timestampBudgetStart
     * @return Builder
     */
    private function getTotaledRejectedLeadsQuery(
        int $companyId = 0,
        int $timestampBudgetStart = 0
    ): Builder
    {
        $connection = (new EloquentQuoteCompany())->getConnection();
        /** @var Builder $query */
        $query = EloquentQuoteCompany::query()
            ->select([
                $connection->raw("COUNT(cost) as `total_count_rejection`"),
                $connection->raw("SUM(cost) as `total_cost_rejection`"),
            ])
            ->chargeableAndDelivered()
            ->chargeStatusRejected()
            ->where(EloquentQuoteCompany::COMPANY_ID, $companyId)
            ->where(EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY, '>=', $timestampBudgetStart);

        return $query;
    }

    /**
     * @param int $companyId
     * @param int $timestampBudgetStart
     * @return Builder
     */
    private function getTotaledRejectedLeadsQueryForCompanies(
        array $companyIds,
        int $timestampBudgetStart = 0
    ): Builder
    {
        $connection = (new EloquentQuoteCompany())->getConnection();
        /** @var Builder $query */
        $query = EloquentQuoteCompany::query()
            ->select([
                EloquentQuoteCompany::COMPANY_ID,
                $connection->raw("COUNT(cost) as `total_count_rejection`"),
                $connection->raw("SUM(cost) as `total_cost_rejection`"),
            ])
            ->chargeableAndDelivered()
            ->chargeStatusRejected()
            ->groupBy(EloquentQuoteCompany::COMPANY_ID)
            ->whereIn(EloquentQuoteCompany::COMPANY_ID, $companyIds)
            ->where(EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY, '>=', $timestampBudgetStart);

        return $query;
    }

    /**
     * @param array $campaignIds
     * @param int $timestampBudgetStart
     * @return Collection
     */
    public function getTotaledChargeableAndDeliveredLeadsByCampaignIds(array $campaignIds, int $timestampBudgetStart): Collection
    {
        return EloquentQuoteCompany::query()
            ->leftJoin(
                LeadCampaignSalesTypeConfiguration::TABLE,
                EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION_ID,
                LeadCampaignSalesTypeConfiguration::TABLE.'.'.LeadCampaignSalesTypeConfiguration::ID
            )
            ->chargeableAndDelivered()
            ->chargeStatusNotRejected()
            ->whereIn(LeadCampaignSalesTypeConfiguration::TABLE.'.'.LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID, $campaignIds)
            ->where(EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY, '>=', $timestampBudgetStart)
            ->with(EloquentQuoteCompany::RELATION_LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION)
        ->get();
    }

    /**
     * @param int $companyId
     * @param int $limit
     * @return Builder
     */
    public function getLeadsByCompanyId(int $companyId): Builder
    {
        /** @var Builder $query */
        $query = EloquentQuoteCompany::query()
            ->Join(EloquentQuote::TABLE,EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::QUOTE_ID,'=',EloquentQuote::TABLE.'.'.EloquentQuote::ID)
            ->Join(LeadCategory::TABLE,EloquentQuote::TABLE.'.'.EloquentQuote::LEAD_CATEGORY_ID,LeadCategory::TABLE.'.'.LeadCategory::ID)
            ->Join(EloquentAddress::TABLE,EloquentQuote::TABLE.'.'.EloquentQuote::ADDRESS_ID,EloquentAddress::TABLE.'.'.EloquentAddress::ID)
            ->where(EloquentQuoteCompany::COMPANY_ID, $companyId)
            ->orderBy(EloquentQuoteCompany::QUOTE_COMPANY_ID, 'desc');

        return $query;
    }

    public function getLeadsByCompanyIdWithSearchFilters(
        int $companyId,
        Int|null $campaignId = null,
        String|null $status = null,
        String|null $category = null,
        Int|null $leadId = null,
        String|null $name = null,
        String|null $state = null,
        $fromDate = null,
        $toDate = null
    ): Builder
    {
        $query = $this->getLeadsByCompanyId($companyId);

        if($campaignId) {
            $query->leftJoin(
                LeadCampaignSalesTypeConfiguration::TABLE,
                EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION_ID,
                LeadCampaignSalesTypeConfiguration::TABLE.'.'.LeadCampaignSalesTypeConfiguration::ID
            )->where(LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID, $campaignId);
        }
        if($status) {
            $query->where(EloquentQuote::TABLE.'.'. EloquentQuote::STATUS, $status);
        }
        if($category) {
            $query->where(LeadCategory::NAME, $category);
        }
        if($leadId) {
            $query->where(EloquentQuote::TABLE.'.'.EloquentQuote::ID, $leadId);
        }
        if($name) {
            $query->where(function($query) use ($name) {
                $query->where(EloquentQuote::FIRST_NAME, 'like', '%'.$name.'%')
                      ->orWhere(EloquentQuote::LAST_NAME, 'like', '%'.$name.'%')
                      ->orwhere(EloquentQuote::USER_EMAIL,'like',  '%'.$name.'%');
            });
        }
        if($state) {
            $query->where(function($query) use ($state) {
                $query->where(EloquentAddress::STATE, $state)
                    ->orWhere(EloquentAddress::ZIP_CODE, $state);
            });
        }
        if($toDate && $fromDate) {
            $query->where(EloquentQuote::TABLE .'.'.EloquentQuote::TIMESTAMP_ADDED, '>', $fromDate)
                  ->where(EloquentQuote::TABLE .'.'.EloquentQuote::TIMESTAMP_ADDED, '<', $toDate);
        }

        return $query;
    }

    /**
     * @param array $companyIdsInitDeliveryTimes
     * @return array
     */
    public function getBudgetSpentByCompanies(array $companyIdsInitDeliveryTimes = []): array
    {
        if(empty($companyIdsInitDeliveryTimes)) {
            return [];
        }

        return EloquentQuoteCompany::query()
            ->groupBy(EloquentQuoteCompany::COMPANY_ID)
            ->selectRaw(implode(',', [
                EloquentQuoteCompany::COMPANY_ID,
                "SUM(cost) as `total_cost_spent`"
            ]))
            ->where(EloquentQuoteCompany::DELIVERED, true)
            ->where(EloquentQuoteCompany::CHARGEABLE, true)
            ->where(EloquentQuoteCompany::INCLUDE_IN_BUDGET, true)
            ->where(function($query) use ($companyIdsInitDeliveryTimes) {
                foreach($companyIdsInitDeliveryTimes as $companyId => $timestampBudgetStart) {
                    $query->orWhere(function($query) use ($companyId, $timestampBudgetStart) {
                        $query
                            ->where(EloquentQuoteCompany::COMPANY_ID, '=', $companyId)
                            ->where(EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY, '>=', $timestampBudgetStart);
                    });
                }
            })
            ->get()
            ->keyBy(EloquentQuoteCompany::COMPANY_ID)
            ->toArray();
    }

    /**
     * @param array $companyIds
     * @param int   $timestampBudgetStart
     * @return array
     */
    public function getRevenueForCompanies(
        array $companyIds,
        int $timestampBudgetStart = 0
    ): array
    {
        return EloquentQuoteCompany::query()
            ->selectRaw(implode(',', [
                EloquentQuoteCompany::COMPANY_ID,
                "SUM(cost) as `cost`"
            ]))
            ->where(EloquentQuoteCompany::CHARGEABLE, true)
            ->where(EloquentQuoteCompany::DELIVERED, true)
            ->whereNot(EloquentQuoteCompany::CHARGE_STATUS, EloquentQuoteCompany::VALUE_CHARGE_STATUS_REJECTED)
            ->whereIn(EloquentQuoteCompany::COMPANY_ID, $companyIds)
            ->where(EloquentQuoteCompany::TIMESTAMP_DELIVERED, '>=', $timestampBudgetStart)
            ->groupBy(EloquentQuoteCompany::COMPANY_ID)
            ->pluck(EloquentQuoteCompany::COST, EloquentQuoteCompany::COMPANY_ID)
            ->toArray();
    }

    /**
     * @param LeadCampaign $campaign
     * @param int $timestamp
     * @return Collection
     */
    public function getChargeableAndDeliveredLeadsForCampaign(LeadCampaign $campaign, int $timestamp): Collection
    {
        return EloquentQuoteCompany::query()
           ->leftJoin(
               LeadCampaignSalesTypeConfiguration::TABLE,
               EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION_ID,
               LeadCampaignSalesTypeConfiguration::TABLE . '.' . LeadCampaignSalesTypeConfiguration::ID
           )
           ->chargeableAndDelivered()
           ->chargeStatusNotRejected()
           ->where(LeadCampaignSalesTypeConfiguration::TABLE . '.' . LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID, $campaign->id)
           ->where(EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY, '>=', $timestamp)
           ->with(EloquentQuoteCompany::RELATION_LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION)
           ->get();
    }

    /**
     * @param EloquentQuote $lead
     * @return Collection
     */
    public function getOffHourQuoteCompaniesByLead(EloquentQuote $lead): Collection
    {
        return $lead->quoteCompanies->where(EloquentQuoteCompany::OFF_HOUR_SALE, true);
    }
}
