<?php

namespace App\Repositories\Legacy;

use App\Contracts\Repositories\NonPurchasingCompanyLocationRepositoryInterface;
use App\Models\CompanyUserRelationship;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\Location;
use App\Models\Legacy\NonPurchasingCompanyLocation;
use App\Models\Odin\Company;
use App\Services\DatabaseHelperService;
use Illuminate\Support\Collection;

class NonPurchasingCompanyLocationRepository implements NonPurchasingCompanyLocationRepositoryInterface
{
    /**
     * @param int $locationId
     * @return array|null
     */
    public function getCompanyIdsByLocation(int $locationId): ?array
    {
        return NonPurchasingCompanyLocation::query()
            ->select(DatabaseHelperService::database().'.'.Company::TABLE.'.'.Company::FIELD_ID)
            ->where(NonPurchasingCompanyLocation::FIELD_LOCATION_ID, $locationId)
            ->join(
                DatabaseHelperService::database().'.'.Company::TABLE,
                DatabaseHelperService::database().'.'.Company::TABLE.'.'.Company::FIELD_LEGACY_ID,
                '=',
                NonPurchasingCompanyLocation::TABLE.'.'.NonPurchasingCompanyLocation::FIELD_COMPANY_ID
            )
            ->pluck(Company::FIELD_ID)
            ->toArray();
    }

    /**
     * @param int $locationId
     * @return Collection<NonPurchasingCompanyLocation>|null
     */
    public function getLegacyCompanyIdsByLocation(int $locationId): ?Collection
    {
        return NonPurchasingCompanyLocation::query()
            ->where(NonPurchasingCompanyLocation::FIELD_LOCATION_ID, $locationId)
            ->get()
            ->pluck(NonPurchasingCompanyLocation::FIELD_COMPANY_ID);
    }

    public function getLegacyCompanyIdsByLocationForAccountManager(int $locationId, int $accountManagerUserId): ?Collection
    {
        $query = NonPurchasingCompanyLocation::query()
            ->where(NonPurchasingCompanyLocation::TABLE .'.'. NonPurchasingCompanyLocation::FIELD_LOCATION_ID,
                $locationId)
            ->join(
                EloquentCompany::TABLE,
                EloquentCompany::TABLE.'.'. EloquentCompany::ID,
                '=',
                NonPurchasingCompanyLocation::TABLE .'.'. NonPurchasingCompanyLocation::FIELD_COMPANY_ID
            )
            ->join(
                DatabaseHelperService::database().'.'.Company::TABLE,
                DatabaseHelperService::database().'.'.Company::TABLE.'.'. Company::FIELD_LEGACY_ID,
                '=',
                EloquentCompany::TABLE.'.'. EloquentCompany::ID
            )
            ->join(
                DatabaseHelperService::database().'.'.CompanyUserRelationship::TABLE,
                DatabaseHelperService::database().'.'.CompanyUserRelationship::TABLE.'.'.CompanyUserRelationship::FIELD_COMPANY_ID,
                "=",
                DatabaseHelperService::database().'.'.Company::TABLE.'.'.Company::FIELD_ID
            )
            ->where(DatabaseHelperService::database().'.'.CompanyUserRelationship::TABLE.'.'.CompanyUserRelationship::FIELD_USER_ID,
                $accountManagerUserId);
;
        return $query->pluck(NonPurchasingCompanyLocation::TABLE .'.'. NonPurchasingCompanyLocation::FIELD_COMPANY_ID);
    }

    /**
     * @param Company $company
     * @return Collection<int, Location>
     */
    public function getStateLocationsByCompany(Company $company): Collection
    {
        return NonPurchasingCompanyLocation::query()
                                           ->where(NonPurchasingCompanyLocation::FIELD_COMPANY_ID, $company->legacy_id)
                                           ->with([NonPurchasingCompanyLocation::RELATION_LOCATION])
                                           ->get()
                                           ->map(fn(NonPurchasingCompanyLocation $npl) => $npl->location)
                                           ->where(Location::TYPE, Location::TYPE_STATE)
                                           ->unique(Location::STATE_KEY);
    }
}
