<?php

namespace App\Repositories\Legacy;

use App\Contracts\Repositories\QuoteRepositoryContract;
use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\EloquentVarStore;
use App\Models\Legacy\Location;
use App\Repositories\LocationRepository;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class QuoteRepository implements QuoteRepositoryContract
{
    public function __construct(protected LocationRepository $locationRepository) {}

    /**
     * @param int $id
     * @return EloquentQuote|null
     */
    function find(int $id): EloquentQuote|null
    {
        return EloquentQuote::find($id);
    }

    function findByReference(string $leadReference): EloquentQuote|null
    {
        return EloquentQuote::where(EloquentQuote::REFERENCE, $leadReference)->first();
    }

    /**
     * @param int $id
     * @return EloquentQuote
     */
    function findOrFail(int $id): EloquentQuote
    {
        return EloquentQuote::findOrFail($id);
    }

    public function findLeadByNumber(string $phoneNumber): ?EloquentQuote
    {
        return EloquentQuote::query()
            ->whereHas(EloquentQuote::RELATION_ADDRESS, fn($query) => $query->where(EloquentAddress::PHONE, $phoneNumber))
            ->where(EloquentQuote::STATUS, '<>', EloquentQuote::VALUE_STATUS_CANCELLED)
            ->where(EloquentQuote::TIMESTAMP_ADDED, '>=', Carbon::now()->subMonths(3)->timestamp)
            ->orderByDesc(EloquentQuote::QUOTE_ID)
            ->first();
    }

    /**
     * Handles getting all related quotes where they've been allocated in the last 90 days.
     *
     * @param int $quoteId
     * @param string $userEmail
     * @param string $userIp
     * @param string $zipCode
     * @param string $phoneNumber
     * @return EloquentQuote[]|Collection
     */
    public function getAllocatedRelatedQuotesLast90Days(
        int     $quoteId,
        ?string $userEmail,
        ?string $userIp,
        ?string $zipCode,
        ?string $phoneNumber
    )
    {
        $query = $this->buildQueryForRelatedQuotes($quoteId, $userEmail, $userIp, $zipCode, $phoneNumber)
            ->where(EloquentQuote::TIMESTAMP_ADDED, '>=', Carbon::now()->subDays(90)->timestamp);

        return $query->get();
    }

    /**
     * @param int $quoteId
     * @param string $userEmail
     * @param string $userIp
     * @param string $zipCode
     * @param string|null $phoneNumber
     * @param int|null $perPage
     * @return EloquentQuote[]|LengthAwarePaginator
     */
    public function getPaginatedRelatedQuotes($quoteId, $userEmail, $userIp, $zipCode, $phoneNumber, $perPage = null)
    {
        /** @var Builder $queryBuilder */
        $queryBuilder = $this->buildQueryForRelatedQuotes(
            (int)$quoteId,
            (string)$userEmail,
            (string)$userIp,
            (string)$zipCode,
            $phoneNumber ? (string)$phoneNumber : null,
            [
                EloquentQuote::RELATION_TRACKING_URL,
                EloquentQuote::RELATION_ADDRESS
            ]
        );

        return $queryBuilder->paginate($perPage);
    }

    /**
     * @param int $quoteId
     * @param string $userEmail
     * @param string $userIp
     * @param string $zipCode
     * @param string|null $phoneNumber
     * @param array $relations
     *
     * @return Builder|\Illuminate\Database\Query\Builder
     */
    public function buildQueryForRelatedQuotes(
        int     $quoteId,
        string  $userEmail,
        string  $userIp,
        string  $zipCode,
        ?string $phoneNumber,
        array   $relations = []
    ): Builder|\Illuminate\Database\Query\Builder
    {
        $quotesQuery = EloquentQuote::join(
            EloquentAddress::TABLE,
            EloquentAddress::TABLE . '.' . EloquentAddress::ADDRESS_ID,
            '=',
            EloquentQuote::TABLE . '.' . EloquentQuote::ADDRESS_ID
        )
            ->where(EloquentQuote::QUOTE_ID, '<>', $quoteId)
            ->where(function ($query) use ($userEmail, $userIp, $zipCode, $phoneNumber) {
                $query->where(EloquentQuote::USER_EMAIL, '=', $userEmail);

                if (!empty($userIp) && !empty($zipCode)) {
                    $query->orWhere([
                        [EloquentQuote::IP_ADDRESS, '=', $userIp],
                        [EloquentAddress::ZIP_CODE, '=', $zipCode],
                    ]);
                }
                if (!empty($phoneNumber)) {
                    $query->orWhere(EloquentAddress::PHONE, '=', $phoneNumber);
                }
            });

        if (!empty($relations)) {
            $quotesQuery->with($relations);
        }

        return $quotesQuery;
    }

    /**
     * @param EloquentQuote $lead
     * @param Carbon $startTime
     *
     * @return int
     */
    public function getUnsoldLeadCountInCounty(EloquentQuote $lead, Carbon $startTime): int
    {
        $zipCodes = $this->locationRepository->getZipCodesInCountyByZipCode($lead->address->zipcode)->pluck(Location::ZIP_CODE);
        return $this->getUnsoldLeadBaseQuery()
            ->join(EloquentAddress::TABLE, EloquentQuote::TABLE . '.' . EloquentQuote::ADDRESS_ID, '=', EloquentAddress::TABLE . '.' . EloquentAddress::ADDRESS_ID)
            ->whereIn(EloquentAddress::TABLE . '.' . EloquentAddress::ZIP_CODE, $zipCodes)
            ->where(EloquentQuote::TABLE . '.' . EloquentQuote::TIMESTAMP_ADDED, '>=', $startTime->timestamp)
            ->where(EloquentQuote::TABLE . '.' . EloquentQuote::SOLAR_LEAD, $lead->solar_lead)
            ->where(EloquentQuote::TABLE . '.' . EloquentQuote::ROOFING_LEAD, $lead->roofing_lead)
            ->count();
    }

    /**
     * @param array $zipCodes
     * @param Carbon $startTime
     * @return array
     */
    public function getUnsoldLeadCountByCounties(array $zipCodes, Carbon $startTime): array
    {
        $zipCodesLocations = $this->locationRepository->getSiblingZipcodes($zipCodes);

        $unsoldLeads = $this->getUnsoldLeadBaseQuery()
            ->join(EloquentAddress::TABLE, EloquentQuote::TABLE . '.' . EloquentQuote::ADDRESS_ID, '=', EloquentAddress::TABLE . '.' . EloquentAddress::ADDRESS_ID)
            ->whereIn(EloquentAddress::ZIP_CODE, $zipCodesLocations->pluck(Location::ZIP_CODE))
            ->where(EloquentQuote::TABLE . '.' . EloquentQuote::TIMESTAMP_ADDED, '>=', $startTime->timestamp)
            ->get();

        $unsoldCountsByCounty = [];
        foreach ($zipCodesLocations as $location){
            /** @var Location $location */
            $zipCode = $location->zip_code;
            $leads = $unsoldLeads->where(EloquentAddress::ZIP_CODE, $zipCode);
            $solarCount = $leads->where(EloquentQuote::SOLAR_LEAD, true)->count();
            $roofingCount = $leads->where(EloquentQuote::ROOFING_LEAD, true)->count();

            $stateCountyKey = $location->state_abbr.':'.$location->county_key;
            if(!key_exists($stateCountyKey,$unsoldCountsByCounty)){
                $unsoldCountsByCounty[$stateCountyKey] = ['solar' => 0, 'roofing' => 0];
            }

            $unsoldCountsByCounty[$stateCountyKey]['solar'] += $solarCount;
            $unsoldCountsByCounty[$stateCountyKey]['roofing'] += $roofingCount;
        }
        return $unsoldCountsByCounty;
    }

    /**
     * @param Collection $zipCodes
     * @param Carbon $startTime
     * @param string $industry
     * @param array|null $select
     *
     * @return Collection
     */
    public function getUnsoldLeadsInCountyByZipCodesAndIndustry(Collection $zipCodes, Carbon $startTime, string $industry = EloquentQuote::LEAD_INDUSTRY_SOLAR, array $select = null): Collection
    {
        $query = $this->getUnsoldLeadBaseQuery()
            ->join(EloquentAddress::TABLE, EloquentQuote::TABLE . '.' . EloquentQuote::ADDRESS_ID, '=', EloquentAddress::TABLE . '.' . EloquentAddress::ADDRESS_ID)
            ->whereIn(EloquentAddress::TABLE . '.' . EloquentAddress::ZIP_CODE, $zipCodes)
            ->where(EloquentQuote::TABLE . '.' . EloquentQuote::TIMESTAMP_ADDED, '>=', $startTime->timestamp);

        switch ($industry) {
            case EloquentQuote::LEAD_INDUSTRY_ROOFING:
                $query->where(EloquentQuote::TABLE . '.' . EloquentQuote::ROOFING_LEAD, true);
                break;
            case EloquentQuote::LEAD_INDUSTRY_SOLAR:
            default:
                $query->where(EloquentQuote::TABLE . '.' . EloquentQuote::SOLAR_LEAD, true);
                break;
        }

        if ($select) $query->select($select);
        else $query->select([EloquentQuote::TABLE . '.*']);

        return $query->get();
    }

    /**
     * @param Collection $zipCodes
     * @param Carbon $startTime
     * @param string $industry
     *
     * @return Collection
     */
    public function getUndersoldLeadsInCountyByZipCodesAndIndustry(Collection $zipCodes, Carbon $startTime, string $industry = EloquentQuote::LEAD_INDUSTRY_SOLAR): Collection
    {
        $query = EloquentQuote::query()
            ->select([EloquentQuote::TABLE . '.*', DB::raw('COUNT(*) AS total')])
            ->whereIn(EloquentQuote::TABLE . '.' . EloquentQuote::STATUS, [EloquentQuote::VALUE_STATUS_ALLOCATED])
            ->join(EloquentAddress::TABLE, EloquentQuote::TABLE . '.' . EloquentQuote::ADDRESS_ID, '=', EloquentAddress::TABLE . '.' . EloquentAddress::ADDRESS_ID)
            ->join(EloquentQuoteCompany::TABLE, EloquentQuote::TABLE . '.' . EloquentQuote::QUOTE_ID, '=', EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::QUOTE_ID)
            ->whereIn(EloquentAddress::TABLE . '.' . EloquentAddress::ZIP_CODE, $zipCodes)
            ->where(EloquentQuote::TABLE . '.' . EloquentQuote::TIMESTAMP_ADDED, '>=', $startTime->timestamp)
            ->where(EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::CHARGEABLE, true)
            ->where(EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::DELIVERED, true)
            ->whereNot(EloquentQuoteCompany::CHARGE_STATUS, EloquentQuoteCompany::VALUE_CHARGE_STATUS_REJECTED)
            ->groupBy(EloquentQuote::TABLE . '.' . EloquentQuote::QUOTE_ID)
            ->havingRaw(EloquentQuote::TABLE . '.' . EloquentQuote::NUMBER_OF_QUOTES . ' > total');


        switch ($industry) {
            case EloquentQuote::LEAD_INDUSTRY_ROOFING:
                $query->where(EloquentQuote::TABLE . '.' . EloquentQuote::ROOFING_LEAD, true);
                break;
            case EloquentQuote::LEAD_INDUSTRY_SOLAR:
            default:
                $query->where(EloquentQuote::TABLE . '.' . EloquentQuote::SOLAR_LEAD, true);
                break;
        }

        return $query->get();
    }

    /**
     * @param Collection<EloquentQuote> $leads
     *
     * @return Collection<EloquentQuote>
     */
    public function filterSMSVerifiedLeads(Collection $leads): Collection
    {
        return $leads->filter(function (EloquentQuote $lead) {
            return !!$lead->varStores()->getQuery()
                ->where(EloquentVarStore::CATEGORY, 'estimator_data')
                ->where(EloquentVarStore::NAME, 'phone_validated')
                ->first();
        });
    }

    /**
     * @return Builder
     */
    protected function getUnsoldLeadBaseQuery(): Builder
    {
        return EloquentQuote::query()
            ->whereNotIn(EloquentQuote::TABLE . '.' . EloquentQuote::STATUS, [EloquentQuote::VALUE_STATUS_INITIAL, EloquentQuote::VALUE_STATUS_CANCELLED])
            ->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from(EloquentQuoteCompany::TABLE)
                    ->whereColumn(EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::QUOTE_ID, EloquentQuote::TABLE . '.' . EloquentQuote::QUOTE_ID);
            });
    }

    /**
     * @param array $leadIds
     * @return Collection <int, EloquentQuote>
     */
    public function findByIds(array $leadIds): Collection
    {
        return EloquentQuote::query()->whereIn(EloquentQuote::ID, $leadIds)->get();
    }
}
