<?php

namespace App\Repositories\Legacy;

use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\IndustryType;
use App\Models\Legacy\Ranking\CompanyRankingValue;
use App\Models\Legacy\Ranking\RankingCategory;
use App\Models\Legacy\Ranking\RankingOption;
use App\Models\Legacy\Ranking\RankingQuestion;
use App\Models\Legacy\Ranking\RankingQuestionChoice;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class CompanyRankingsRepository
{

    /**
     * @param CompanyRepository $companyRepository
     */
    public function __construct(
        protected CompanyRepository $companyRepository
    )
    {
    }

    /**
     * Handles building the base query for fetching categories for a given company type.
     *
     * @param string $companyType
     * @return Builder
     */
    protected function buildCategoriesForCompanyTypeQuery(string $companyType): Builder
    {
        $industries = $this->companyRepository->getIndustriesForCompanyType($companyType);

        return RankingCategory::query()->whereHas(RankingCategory::RELATION_INDUSTRY_TYPES, function ($query) use ($industries) {
            return $query->whereIn(IndustryType::TABLE.'.'.IndustryType::FIELD_ID, $industries);
        })->with(
            [
                RankingCategory::RELATION_QUESTIONS => function ($query) {
                    $query
                        ->whereNull(RankingQuestion::TABLE.".".RankingQuestion::FIELD_PARENT_QUESTION_ID)
                        ->with(
                            RankingQuestion::RELATION_CHOICES,
                            RankingQuestion::RELATION_CHOICES.".".RankingQuestionChoice::RELATION_SUB_QUESTION,
                            RankingQuestion::RELATION_CHOICES.".".RankingQuestionChoice::RELATION_SUB_QUESTION.".".RankingQuestion::RELATION_CHOICES,
                            RankingQuestion::RELATION_CHOICES.".".RankingQuestionChoice::RELATION_SUB_QUESTION.".".RankingQuestion::RELATION_LIST_FIELDS,
                            RankingQuestion::RELATION_LIST_FIELDS
                        );
                },
                RankingCategory::RELATION_RANKING_OPTIONS
            ]
        )->orderBy(RankingCategory::FIELD_ORDINAL_POSITION);
    }

    /**
     * Handles fetching the categories w/ answers for a given company type.
     *
     * @param string $companyType
     * @return Collection
     */
    public function getCategoriesForCompanyType(string $companyType): Collection
    {
        $query = $this->buildCategoriesForCompanyTypeQuery($companyType);

        return $query->get();
    }

    /**
     * @param EloquentCompany|null $company
     *
     * @return float|int
     */
    public function getRankingScore(EloquentCompany $company = null): float|int
    {
        $totalScore = $this->getCategoriesForCompanyType($company->type ?? EloquentCompany::TYPE_INSTALLER)->map(function (RankingCategory $category) {
            return $category->rankingOptions->sortByDesc(RankingOption::FIELD_POINT_VALUE)->first()->point_value;
        })->sum();

        $score = 1;

        if ($company !== null) {
            $assignments = $company->companyRankingValue;

            if ($assignments->count() > 0) {
                $total = $assignments->map(function (CompanyRankingValue $value) {
                    return $value->ranking_value;
                })->sum();
                // Normalize score from 0-self::$totalScore to 1-5
                $score = ((($total - 0) * (5 - 1)) / ($totalScore - 0)) + 1;
            }
        }

        return $score;
    }

    /**
     * @param EloquentCompany|null $company
     * @return array
     */
    public function getRankingFactors(EloquentCompany $company = null): array
    {
        $assignments = $company?->companyRankingValue;

        $factors = [];

        if ($assignments?->count() > 0) {
            $factors = $assignments->map(function (CompanyRankingValue $factor) {
                return [
                    'name' => $factor->rankingCategory->display_name,
                    'score' => $factor->ranking_value,
                    'possible_score' => $factor->rankingCategory->rankingOptions->sortByDesc(RankingOption::FIELD_POINT_VALUE)->first()->point_value
                ];
            })->toArray();
        }

        return $factors;
    }
}
