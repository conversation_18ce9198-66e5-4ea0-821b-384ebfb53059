<?php

namespace App\Repositories\Legacy;

use App\Contracts\Repositories\LeadRejectionRepositoryContract;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Services\Legacy\Statistics;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;

class LeadRejectionRepository implements LeadRejectionRepositoryContract
{
    /**
     * @param int $startTimestamp
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Query\Builder
     */
    public function getBaseLeadRejectionQuery(int $startTimestamp): \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Query\Builder
    {
        return $this->getBaseLeadCountQuery($startTimestamp)
                    ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::CHARGE_STATUS, EloquentQuoteCompany::VALUE_CHARGE_STATUS_REJECTED);
    }

    /**
     * @param int $startTimestamp
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Query\Builder
     */
    public function getBaseLeadCountQuery(int $startTimestamp): \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Query\Builder
    {
        return EloquentQuoteCompany::query()
                    ->selectRaw("count(tblquotecompany.cost) as 'quotecount', sum(tblquotecompany.cost) as 'quotecost', tblquotecompany.companyid")
                    ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::INCLUDE_IN_BUDGET, true)
                    ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::DELIVERED, 1)
                    ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::CHARGEABLE, 1)
                    ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY, '>=', $startTimestamp);
    }

    /**
     * Performs the rejection calculation and returns the statistics result
     *
     * @param array $leadQuery
     * @param array $rejectionQuery
     * @param int $startTimestamp
     * @return Statistics
     */
    public function performRejectionCalculation(array $leadQuery, array $rejectionQuery, int $startTimestamp): Statistics
    {
        $leadCost                  = $leadQuery["quotecost"];
        $leadTotal                 = $leadQuery["quotecount"];
        $rejectionCost             = $rejectionQuery["quotecost"];
        $rejectionTotal            = $rejectionQuery["quotecount"];
        $rejectionCostPercentage   = 0;
        $rejectionNumberPercentage = 0;

        if ($leadTotal > 0) {
            $rejectionNumberPercentage = ($rejectionTotal / $leadTotal) * 100;
        }

        if ($leadCost > 0) {
            $rejectionCostPercentage = ($rejectionCost / $leadCost) * 100;
        }

        return new Statistics($rejectionNumberPercentage, $rejectionCostPercentage, $startTimestamp, $leadTotal);
    }

    /**
     * Gets an array of Lead Rejection Statistics for the given company ids
     *
     * @param array $companyIds
     * @return \Illuminate\Support\Collection
     */
    public function getLeadRejectionStatisticsInList(array $companyIds): \Illuminate\Support\Collection
    {
        // 30 days ago including today
        $startTimestamp = Carbon::today()->subDays(29)->timestamp;

        /** @var Collection $leadCounts */
        $leadCounts = collect(
            $this->getBaseLeadCountQuery($startTimestamp)
               ->whereIn(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::COMPANY_ID, $companyIds)
               ->groupBy(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::COMPANY_ID)
               ->get()->toArray()
        )->keyBy(EloquentQuoteCompany::COMPANY_ID);

        /** @var Collection $rejectionCounts */
        $rejectionCounts = collect(
            $this->getBaseLeadRejectionQuery($startTimestamp)
                ->whereIn(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::COMPANY_ID, $companyIds)
                ->groupBy(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::COMPANY_ID)
                ->get()->toArray()
        )->keyBy(EloquentQuoteCompany::COMPANY_ID);

        $statistics = collect();

        foreach ($companyIds as $companyId) {
            if ($leadCounts->has($companyId) && $rejectionCounts->has($companyId)) {
                $statistics->put($companyId, $this->performRejectionCalculation($leadCounts->get($companyId), $rejectionCounts->get($companyId), $startTimestamp));
            }
        }

        return $statistics;
    }

    /**
     * @param EloquentCompany $company
     * @param int|null $startTimestamp
     * @param int|null $endTimestamp
     *
     * @return int
     */
    public function getTotalLeadRejectionForCompany(EloquentCompany $company, int $startTimestamp = null, int $endTimestamp = null): int
    {
        $query = EloquentQuoteCompany::query()
            ->where(EloquentQuoteCompany::COMPANY_ID, $company->companyid)
            ->where(EloquentQuoteCompany::CHARGE_STATUS, EloquentQuoteCompany::VALUE_CHARGE_STATUS_REJECTED);

        if ($startTimestamp) $query->where(EloquentQuoteCompany::TIMESTAMP_DELIVERED, '>=', $startTimestamp);

        if ($endTimestamp) $query->where(EloquentQuoteCompany::TIMESTAMP_DELIVERED, '<=', $endTimestamp);

        return $query->count();
    }
}
