<?php

namespace App\Repositories;

use App\Models\Billing\InvoiceItem;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryLog;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class CompanyCampaignDeliveryLogRepository
{
    /**
     * @param int $perPage
     * @param int $page
     * @param int|null $companyId
     * @param bool|null $succeeded
     * @param string|null $campaign
     * @param int|null $consumerProductId
     * @param array|null $dateRange
     * @param int|null $invoiceId
     *
     * @return LengthAwarePaginator
     */
    public function listDeliveryLogs(
        int     $perPage,
        int     $page,
        ?int    $companyId = null,
        ?bool   $succeeded = null,
        ?string $campaign = null,
        ?int    $consumerProductId = null,
        ?array  $dateRange = null,
        ?int    $invoiceId = null
    ): LengthAwarePaginator
    {
        return $this->baseQuery(
            companyId: $companyId,
            succeeded: $succeeded,
            campaign: $campaign,
            consumerProductId: $consumerProductId,
            dateRange: $dateRange,
            invoiceId: $invoiceId
        )->paginate($perPage, ['*'], 'page', $page);
    }

    /**
     * @param int $limit
     * @param int|null $companyId
     * @param bool|null $succeeded
     * @param string|null $campaign
     * @param int|null $consumerProductId
     * @param array|null $dateRange
     * @param int|null $invoiceId
     *
     * @return Collection
     */
    public function listAllDeliveryLogs(
        int $limit= 500,
        ?int $companyId = null,
        ?bool $succeeded = null,
        ?string $campaign = null,
        ?int $consumerProductId = null,
        ?array $dateRange = null,
        ?int $invoiceId = null
    ): Collection
    {
        return $this->baseQuery(
            companyId: $companyId,
            succeeded: $succeeded,
            campaign: $campaign,
            consumerProductId: $consumerProductId,
            dateRange: $dateRange,
            invoiceId: $invoiceId
        )->limit($limit)->get();
    }

    /**
     * @param int|null $companyId
     * @param bool|null $succeeded
     * @param string|null $campaign
     * @param int|null $consumerProductId
     * @param array|null $dateRange
     * @param int|null $invoiceId
     *
     * @return Builder
     */
    protected function baseQuery(?int $companyId, ?bool $succeeded, ?string $campaign, ?int $consumerProductId, ?array $dateRange, ?int $invoiceId = null): Builder
    {
        $query = CompanyCampaignDeliveryLog::query();

        if (isset($companyId)) {
            $query->whereHas(CompanyCampaignDeliveryLog::RELATION_CAMPAIGN, function (Builder $builder) use ($companyId) {
                $builder->withTrashed()->where(CompanyCampaign::FIELD_COMPANY_ID, $companyId);
            });
        }

        if (isset($succeeded)) {
            $query->where(CompanyCampaignDeliveryLog::FIELD_SUCCESS, $succeeded);
        }

        if (isset($campaign)) {
            $query->whereHas(CompanyCampaignDeliveryLog::RELATION_CAMPAIGN, function (Builder $builder) use ($campaign) {
                $builder->withTrashed()->whereAny([CompanyCampaign::FIELD_ID, CompanyCampaign::FIELD_NAME],'LIKE', '%' . $campaign . '%');
            });
        }

        if (isset($consumerProductId)) {
            $query->whereHas(CompanyCampaignDeliveryLog::RELATION_PRODUCT, function (Builder $builder) use ($consumerProductId) {
               $builder->where(ConsumerProduct::FIELD_ID, $consumerProductId);
            });
        }

        if (isset($dateRange)) {
            $query->whereBetween(CompanyCampaignDeliveryLog::FIELD_CREATED_AT, [$dateRange['from'],  $dateRange['to']]);
        }

        if ($invoiceId) {
            [$consumerProductIds, $campaignIds] = self::getCampaignAndConsumerProductIdsForInvoice($invoiceId);

            $query->whereIn(CompanyCampaignDeliveryLog::FIELD_CONSUMER_PRODUCT_ID, $consumerProductIds)
                ->whereIn(CompanyCampaignDeliveryLog::FIELD_CAMPAIGN_ID, $campaignIds);
        }

        return $query->orderByDesc(CompanyCampaignDeliveryLog::FIELD_CREATED_AT);
    }

    /**
     * @param int $invoiceId
     *
     * @return array
     */
    public static function getCampaignAndConsumerProductIdsForInvoice(int $invoiceId): array
    {
        $productAssignments = ProductAssignment::query()
            ->with([ProductAssignment::RELATION_BUDGET . '.' . Budget::RELATION_BUDGET_CONTAINER])
            ->whereHas(
                ProductAssignment::RELATION_ODIN_INVOICE_ITEM,
                fn(Builder $query) => $query->where(InvoiceItem::FIELD_INVOICE_ID, $invoiceId)
            )
            ->get();

        $consumerProductIds = $productAssignments->pluck(ProductAssignment::FIELD_CONSUMER_PRODUCT_ID)->unique()->toArray();
        $campaignIds = $productAssignments
            ->map(fn(ProductAssignment $productAssignment) => $productAssignment->budget->budgetContainer->company_campaign_id)
            ->unique()
            ->toArray();

        return [$consumerProductIds, $campaignIds];
    }
}
