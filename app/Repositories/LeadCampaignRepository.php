<?php


namespace App\Repositories;


use App\Builders\LeadCampaignBuilder;
use App\Models\ComputedRejectionStatistic;
use App\Models\Legacy\EloquentCompany;
use App\DataModels\Odin\Prices\BudgetUsageData;
use App\Enums\Odin\BudgetCategory;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Legacy\LeadSalesType;
use App\Models\Odin\Company;
use App\Models\User;
use App\Services\DatabaseHelperService;
use App\Services\Legacy\APIConsumer;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use \Illuminate\Http\Client\Response;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Collection;
use App\Repositories\Legacy\CompanyRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class LeadCampaignRepository
{
    const EXCLUSIVE_SALES_TYPE_ID = 1;
    const DUO_SALES_TYPE_ID = 2;
    const TRIO_SALES_TYPE_ID = 3;
    const QUAD_SALES_TYPE_ID = 4;
    const UNVERIFIED_SALES_TYPE_ID = 5;
    const EMAIL_ONLY_SALES_TYPE_ID = 6;

    const VERIFIED_SALE_TYPE_IDS = [
        self::EXCLUSIVE_SALES_TYPE_ID,
        self::DUO_SALES_TYPE_ID,
        self::TRIO_SALES_TYPE_ID,
        self::QUAD_SALES_TYPE_ID
    ];
    const int SOLD_LEAD_AVERAGE_COUNT = 50;

    /**
     * LeadCampaignRepository constructor.
     * @param LeadCampaignBuilder $builder
     * @param CompanyRepository $companyRepository
     * @param APIConsumer $consumer
     */
    public function __construct(
        protected LeadCampaignBuilder $builder,
        private CompanyRepository $companyRepository,
        protected APIConsumer $consumer
    ){}

    /**
     * @param array $relations
     * @return Collection
     * @throws BindingResolutionException
     */
    public function getAllAvailableCampaigns(array $relations = [LeadCampaign::RELATION_COMPANY, LeadCampaign::RELATION_LEAD_CAMPAIGN_ZIP_LOCATIONS, LeadCampaign::RELATION_LEAD_CAMPAIGN_UTILITIES]): Collection
    {
        $rejectionThreshold = ComputedRejectionStatistic::ACTIVE_REJECTION_FIELD_IMPACTING_ELIGIBILITY === ComputedRejectionStatistic::FIELD_CRM_REJECTION_PERCENTAGE ?
            config('sales.leads.crm_rejection_percentage_threshold') :
            config('sales.leads.overall_rejection_percentage_threshold');

        return $this->builder
            ->onlyActiveCompanies()
            ->onlySolarCompanies()
            ->onlyPurchasingCompanies()
            ->onlyActiveCampaigns()
            ->onlyCompaniesWithRejectionBelowThreshold($rejectionThreshold)
            ->onlyResidentialCampaigns()
            ->excludeLowBidCampaigns()
            ->with($relations)
            ->get();
    }

    /**
     * @param int $legacyCompanyId
     * @param bool|null $withDeleted
     * @param bool|null $status
     * @return Builder
     */
    public function getCompanyLeadCampaigns(
        int $legacyCompanyId,
        ?bool $withDeleted = null,
        ?bool $status = null,
    ): Builder
    {
        $query = $withDeleted ? LeadCampaign::withTrashed() : LeadCampaign::query();

        return $query
            ->where(LeadCampaign::COMPANY_ID, $legacyCompanyId)
            ->when($status === true, fn ($query) => $query->where(LeadCampaign::STATUS, LeadCampaign::STATUS_ACTIVE));
    }

    /**
     * @param array $relations
     * @return Collection
     * @throws BindingResolutionException
     */
    public function getAllFutureAvailableCampaigns(array $relations = [LeadCampaign::RELATION_COMPANY, LeadCampaign::RELATION_LEAD_CAMPAIGN_ZIP_LOCATIONS, LeadCampaign::RELATION_LEAD_CAMPAIGN_UTILITIES]): Collection
    {
        $rejectionThreshold = ComputedRejectionStatistic::ACTIVE_REJECTION_FIELD_IMPACTING_ELIGIBILITY === ComputedRejectionStatistic::FIELD_CRM_REJECTION_PERCENTAGE ?
            config('sales.leads.crm_rejection_percentage_threshold') :
            config('sales.leads.overall_rejection_percentage_threshold');

        return $this->builder
            ->onlyActiveCompanies()
            ->onlyPurchasingCompanies()
            ->onlyCampaignsSetToReactivateSoon()
            ->onlyNoLimitBudgetCampaigns()
            ->onlyCompaniesWithRejectionBelowThreshold($rejectionThreshold)
            ->onlyResidentialCampaigns()
            ->excludeLowBidCampaigns()
            ->with($relations)
            ->get();
    }

    /**
     * @param array $companyIds
     * @return \Illuminate\Support\Collection
     */
    public function getCampaignsStatusByCompany(array $companyIds): \Illuminate\Support\Collection
    {
        $overBudgetQuery = $this->companyRepository->getOverBudgetSubQuery();

        $overBudgetQuery
            ->whereIn(LeadCampaign::TABLE.'.'.LeadCampaign::COMPANY_ID, $companyIds);

        $overBudgetTable = 'over_budget_table';
        $leadCampaignId = $overBudgetTable.'.'.LeadCampaign::ID;
        $leadCampaignStatus = $overBudgetTable.'.'.LeadCampaign::STATUS;
        $leadCampaignActive = LeadCampaign::STATUS_ACTIVE;
        $overBudgetCount = 'over_budget_count';
        $campaignsCount = 'campaigns_count';
        $activeCount = 'active_count';

        $campaignsBudgetData =  DB::query()
                                    ->fromSub($overBudgetQuery, $overBudgetTable)
                                    ->groupBy($overBudgetTable.'.'.LeadCampaign::COMPANY_ID)
                                    ->select([
                                        $overBudgetTable.'.'.LeadCampaign::COMPANY_ID,
                                        DB::raw("SUM(IF({$overBudgetTable}.over_budget = true, 1, 0)) AS $overBudgetCount"),
                                        DB::raw("SUM(IF($leadCampaignStatus = $leadCampaignActive, 1, 0)) AS $activeCount"),
                                        DB::raw("COUNT(DISTINCT $leadCampaignId) AS $campaignsCount")
                                    ])
                                    ->get()
                                    ->keyBy(LeadCampaign::COMPANY_ID);

        $companyCampaignStatus = collect();
        foreach($campaignsBudgetData as $companyId => $campaignsBudgetDatum) {
            if($campaignsBudgetDatum->{$campaignsCount} == $campaignsBudgetDatum->{$activeCount})
            {
                $campaignStatus = CompanyRepository::CAMPAIGN_STATUS_ACTIVE;
            }
            else if($campaignsBudgetDatum->{$campaignsCount} > $campaignsBudgetDatum->{$activeCount}
                && $campaignsBudgetDatum->{$activeCount} > 0)
            {
                $campaignStatus = CompanyRepository::CAMPAIGN_STATUS_ONE_PAUSED;
            }
            else if($campaignsBudgetDatum->{$campaignsCount} > $campaignsBudgetDatum->{$activeCount}
                && $campaignsBudgetDatum->{$activeCount} == 0)
            {
                $campaignStatus = CompanyRepository::CAMPAIGN_STATUS_PAUSED;
            }
            else if($campaignsBudgetDatum->{$campaignsCount} == $campaignsBudgetDatum->{$overBudgetCount})
            {
                $campaignStatus = CompanyRepository::CAMPAIGN_STATUS_OVER_BUDGET;
            }
            else if($campaignsBudgetDatum->{$campaignsCount} > $campaignsBudgetDatum->{$overBudgetCount}
                && $campaignsBudgetDatum->{$overBudgetCount} > 0)
            {
                $campaignStatus = CompanyRepository::CAMPAIGN_STATUS_ONE_OVER_BUDGET;
            }

            $companyCampaignStatus->put($companyId, $campaignStatus ?? '');
        }

        return $companyCampaignStatus;
    }

    public function delete(int $legacyCompanyId, string $campaignUuid): Response
    {
        return $this->consumer->delete("/repositories/companies/{$legacyCompanyId}/campaigns/{$campaignUuid}", ["legacyUserId" => Auth::user()?->{User::FIELD_LEGACY_USER_ID}]);
    }

    /**
     * Returns whether a company is buying unverified leads.
     *
     * @param int $companyId
     * @return bool
     */
    public function isUnverifiedOn(int $companyId): bool
    {
        // TODO: Once campaigns are implemented first-party in A2 upgrade this.
        /** @var Company $company */
        $company = Company::query()->findOrFail($companyId);
        /** @var EloquentCompany $legacyCompany */
        $legacyCompany = EloquentCompany::query()->findOrFail($company->legacy_id);

        return $legacyCompany->leadCampaignsTypes()
                   ->where(LeadSalesType::TABLE.'.'.LeadSalesType::ID, LeadSalesType::LEAD_SALE_TYPE_UNVERIFIED_ID)
                   ->where(LeadCampaignSalesTypeConfiguration::TABLE.'.'.LeadCampaignSalesTypeConfiguration::STATUS,1)
                   ->count() === $legacyCompany->campaigns()->count();
    }

    /**
     * Returns whether a company is buying email only leads.
     *
     * @param int $companyId
     * @return bool
     */
    public function isEmailOnlyOn(int $companyId): bool
    {
        // TODO: Once campaigns are implemented first-party in A2 upgrade this.
        /** @var Company $company */
        $company = Company::query()->findOrFail($companyId);
        /** @var EloquentCompany $legacyCompany */
        $legacyCompany = EloquentCompany::query()->findOrFail($company->legacy_id);

        return $legacyCompany->leadCampaignsTypes()
            ->where(LeadSalesType::TABLE.'.'.LeadSalesType::ID, LeadSalesType::LEAD_SALE_TYPE_EMAIL_ONLY_ID)
            ->where(LeadCampaignSalesTypeConfiguration::TABLE.'.'.LeadCampaignSalesTypeConfiguration::STATUS,1)
            ->count() === $legacyCompany->campaigns()->count();
    }

    /**
     * @param int $leadCampaignId
     * @return LeadCampaign|null
     */
    public function find(int $leadCampaignId): ?LeadCampaign
    {
        return LeadCampaign::query()->where(LeadCampaign::ID, $leadCampaignId)->first();
    }

    /**
     * @param array $campaignIds
     * @param int $legacySalesTypeId
     * @return \Illuminate\Support\Collection <int, LeadCampaignSalesTypeConfiguration>
     */
    public function findCampaignConfigsBySalesType(array $campaignIds, int $legacySalesTypeId): \Illuminate\Support\Collection
    {
        return LeadCampaignSalesTypeConfiguration::query()
            ->whereIn(LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID, $campaignIds)
            ->where(LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID, $legacySalesTypeId)
            ->get();
    }

    /**
     * @param array $leadCampaignIds
     * @param bool $ignoreA2Managed
     * @return BudgetUsageData
     * @throws Exception
     */
    public function getLeadCampaignBudgetUsage(array $leadCampaignIds, ?bool $ignoreA2Managed = false): BudgetUsageData
    {
        $costCol = EloquentQuoteCompany::COST;
        $lastModifiedLeadLimit = LeadCampaign::TABLE . '.' . LeadCampaign::LAST_MODIFIED_LEAD_LIMIT;
        $saleTypeLastModifiedLeadLimit = LeadCampaignSalesTypeConfiguration::TABLE . '.' . LeadCampaignSalesTypeConfiguration::LAST_MODIFIED_LEAD_LIMIT;
        $maxDailySpend = LeadCampaign::TABLE . '.' . LeadCampaign::MAX_DAILY_SPEND;
        $maxDailyLead = LeadCampaign::TABLE . '.' . LeadCampaign::MAX_DAILY_LEAD;
        $maxBudgetUsageCol = LeadCampaign::TABLE . '.' . LeadCampaign::MAXIMUM_BUDGET_USAGE;

        $budgetSpentCol = 'budget_spent';
        $budgetTimeframeCol = 'budget_timeframe';
        $budgetUnitCol = 'budget_unit';
        $dailyBudgetCol = 'daily_budget';
        $leadCampaignIdCol = 'lead_campaign_id';
        $saleTypeIdCol = 'sale_type_id';
        $campaignBudgetIdCol = 'campaign_budget_id';

        $budgetUnitSpend = "'".LeadCampaign::DISPLAY_BUDGET_UNIT_CURRENCY."'";
        $budgetUnitLead = "'".LeadCampaign::DISPLAY_BUDGET_UNIT_LEAD."'";
        $budgetUnitUnlimited = "'".LeadCampaign::DISPLAY_BUDGET_UNIT_UNLIMITED."'";

        $baseLeadCampaignQuery = LeadCampaign::query()
            ->when($ignoreA2Managed, fn(Builder $query) =>
                $query->where(LeadCampaign::IS_MANAGED_BY_A2, false)
            );


        $defaultCampaignUsageQuery = $baseLeadCampaignQuery
            ->whereIn(LeadCampaign::TABLE . '.' . LeadCampaign::ID, $leadCampaignIds)
            ->where(LeadCampaign::TABLE . '.' . LeadCampaign::NAME, 'DEFAULT')
            ->join(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentQuoteCompany::TABLE, function ($join) {
                $join
                    ->on(
                        DatabaseHelperService::readOnlyDatabase() . '.' . EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::COMPANY_ID,
                        '=',
                        DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaign::TABLE . '.' . LeadCampaign::COMPANY_ID
                    );
            })
            ->join(DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE, function($join) {
                $join->on(
                    DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE.'.'.EloquentCompany::COMPANY_ID,
                    '=',
                    DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::COMPANY_ID
                );
            })
            ->where(function ($where) {
                $where
                    ->whereNull(EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION_ID)
                    ->orWhere(EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION_ID, 0);
            })
            ->where(EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::CHARGEABLE, true)
            ->where(EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::DELIVERED, true)
            ->where(EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::INCLUDE_IN_BUDGET, true)
            ->whereRaw(EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY . " >= IF({$lastModifiedLeadLimit} > DATE_SUB(NOW(), INTERVAL 1 MONTH), UNIX_TIMESTAMP({$lastModifiedLeadLimit}), UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 MONTH)))")
            ->selectRaw(implode(
                ',',
                [
                    LeadCampaign::TABLE . '.' . LeadCampaign::ID." AS {$leadCampaignIdCol}",
                    LeadCampaign::TABLE . '.' . LeadCampaign::COMPANY_ID,
                    "0 AS {$saleTypeIdCol}",
                    "0 AS {$campaignBudgetIdCol}",
                    "IF({$maxDailySpend} > 0, SUM({$costCol}), COUNT({$costCol})) AS {$budgetSpentCol}",
                    "FLOOR((UNIX_TIMESTAMP() - IF({$lastModifiedLeadLimit} > DATE_SUB(NOW(), INTERVAL 1 MONTH), UNIX_TIMESTAMP({$lastModifiedLeadLimit}), UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 MONTH)))) / 86400) + 1 AS {$budgetTimeframeCol}",
                    "IF({$maxDailySpend} > 0, {$maxDailySpend}, COALESCE({$maxDailyLead}, 0)) AS {$dailyBudgetCol}",
                    "{$maxBudgetUsageCol}",
                    "IF({$maxDailyLead} > 0, {$budgetUnitLead}, IF({$maxDailySpend} > 0, {$budgetUnitSpend}, {$budgetUnitUnlimited})) AS {$budgetUnitCol}",
                    EloquentCompany::TABLE.'.'.EloquentCompany::NEVER_EXCEED_BUDGET
                ]
            ))
            ->groupBy([
                $leadCampaignIdCol,
                LeadCampaign::TABLE . '.' . LeadCampaign::COMPANY_ID,
            ])
            ->getQuery();

        $allCampaignBudgetUsage = $baseLeadCampaignQuery
            ->whereIn(LeadCampaign::TABLE . '.' . LeadCampaign::ID, $leadCampaignIds)
            ->join(DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaignSalesTypeConfiguration::TABLE, function ($join) {
                $join
                    ->on(
                        DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaignSalesTypeConfiguration::TABLE . '.' . LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID,
                        '=',
                        DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaign::TABLE . '.' . LeadCampaign::ID
                    );
            })
            ->join(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentQuoteCompany::TABLE, function ($join) {
                $join
                    ->on(
                        DatabaseHelperService::readOnlyDatabase() . '.' . EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION_ID,
                        '=',
                        DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaignSalesTypeConfiguration::TABLE . '.' . LeadCampaignSalesTypeConfiguration::ID
                    );
            })
            ->join(DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE, function($join) {
                $join->on(
                    DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE.'.'.EloquentCompany::COMPANY_ID,
                    '=',
                    DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::COMPANY_ID
                );
            })
            ->where(EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::CHARGEABLE, true)
            ->where(EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::DELIVERED, true)
            ->where(EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::INCLUDE_IN_BUDGET, true)
            ->whereRaw(EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY . " >= IF(COALESCE({$saleTypeLastModifiedLeadLimit}, {$lastModifiedLeadLimit}) > DATE_SUB(NOW(), INTERVAL 1 MONTH), UNIX_TIMESTAMP(COALESCE({$saleTypeLastModifiedLeadLimit}, {$lastModifiedLeadLimit})), UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 MONTH)))")
            ->selectRaw(implode(
                ',',
                [
                    LeadCampaign::TABLE . '.' . LeadCampaign::ID." AS {$leadCampaignIdCol}",
                    LeadCampaign::TABLE . '.' . LeadCampaign::COMPANY_ID,
                    LeadCampaignSalesTypeConfiguration::TABLE . '.' . LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID . " AS {$saleTypeIdCol}",
                    LeadCampaignSalesTypeConfiguration::TABLE.'.'.LeadCampaignSalesTypeConfiguration::ID." AS {$campaignBudgetIdCol}",
                    "IF({$maxDailySpend} > 0, SUM({$costCol}), COUNT({$costCol})) AS {$budgetSpentCol}",
                    "FLOOR(
                        (
                            UNIX_TIMESTAMP() - IF(
                                COALESCE({$saleTypeLastModifiedLeadLimit}, {$lastModifiedLeadLimit}) > DATE_SUB(NOW(), INTERVAL 1 MONTH),
                                UNIX_TIMESTAMP(COALESCE({$saleTypeLastModifiedLeadLimit}, {$lastModifiedLeadLimit})),
                                UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 MONTH))
                            )
                        ) / 86400
                    ) + 1 AS {$budgetTimeframeCol}",
                    "IF({$maxDailySpend} > 0, {$maxDailySpend}, COALESCE({$maxDailyLead}, 0)) AS {$dailyBudgetCol}",
                    "{$maxBudgetUsageCol}",
                    "IF({$maxDailyLead} > 0, {$budgetUnitLead}, IF({$maxDailySpend} > 0, {$budgetUnitSpend}, {$budgetUnitUnlimited})) AS {$budgetUnitCol}",
                    EloquentCompany::TABLE.'.'.EloquentCompany::NEVER_EXCEED_BUDGET
                ]
            ))
            ->groupBy([
                $leadCampaignIdCol,
                LeadCampaign::TABLE . '.' . LeadCampaign::COMPANY_ID,
                $saleTypeIdCol
            ])
            ->union($defaultCampaignUsageQuery)
            ->get()
            ->groupBy([
                $leadCampaignIdCol,
                $saleTypeIdCol
            ])
            ->toArray();

        $saleTypeBudgetTimeframeCol = 'sale_type_budget_timeframe';

        $campaignBudgetBaseInfo = $baseLeadCampaignQuery
            ->whereIn(LeadCampaign::TABLE . '.' . LeadCampaign::ID, $leadCampaignIds)
            ->join(DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE, function($join) {
                $join->on(
                    DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE.'.'.EloquentCompany::COMPANY_ID,
                    '=',
                    DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::COMPANY_ID
                );
            })
            ->leftJoin(DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaignSalesTypeConfiguration::TABLE, function ($join) {
                $join
                    ->on(
                        DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaignSalesTypeConfiguration::TABLE . '.' . LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID,
                        '=',
                        DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaign::TABLE . '.' . LeadCampaign::ID
                    );
            })
            ->selectRaw(implode(',', [
                LeadCampaign::TABLE . '.' . LeadCampaign::ID." AS {$leadCampaignIdCol}",
                LeadCampaign::TABLE . '.' . LeadCampaign::COMPANY_ID,
                LeadCampaignSalesTypeConfiguration::TABLE.'.'.LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID." AS {$saleTypeIdCol}",
                LeadCampaignSalesTypeConfiguration::TABLE.'.'.LeadCampaignSalesTypeConfiguration::ID." AS {$campaignBudgetIdCol}",
                "0 AS {$budgetSpentCol}",
                "FLOOR((UNIX_TIMESTAMP() - IF({$lastModifiedLeadLimit} > DATE_SUB(NOW(), INTERVAL 1 MONTH), UNIX_TIMESTAMP({$lastModifiedLeadLimit}), UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 MONTH)))) / 86400) + 1 AS {$budgetTimeframeCol}",
                "FLOOR((UNIX_TIMESTAMP() - IF({$saleTypeLastModifiedLeadLimit} > DATE_SUB(NOW(), INTERVAL 1 MONTH), UNIX_TIMESTAMP({$saleTypeLastModifiedLeadLimit}), UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 MONTH)))) / 86400) + 1 AS {$saleTypeBudgetTimeframeCol}",
                "IF({$maxDailySpend} > 0, {$maxDailySpend}, COALESCE({$maxDailyLead}, 0)) AS {$dailyBudgetCol}",
                "{$maxBudgetUsageCol}",
                "IF({$maxDailyLead} > 0, {$budgetUnitLead}, IF({$maxDailySpend} > 0, {$budgetUnitSpend}, {$budgetUnitUnlimited})) AS {$budgetUnitCol}",
                EloquentCompany::TABLE.'.'.EloquentCompany::NEVER_EXCEED_BUDGET
            ]))
            ->groupBy([
                $leadCampaignIdCol,
                $saleTypeIdCol
            ])
            ->get()
            ->groupBy([
                $leadCampaignIdCol,
                $saleTypeIdCol
            ])
            ->toArray();

        $budgetUsageData = new BudgetUsageData();

        foreach($leadCampaignIds as $leadCampaignId) {
            $budgetBaseInfo = $campaignBudgetBaseInfo[$leadCampaignId];

            $verifiedBudgetBaseInfo = reset($budgetBaseInfo)[0];

            if(!empty($budgetBaseInfo[self::UNVERIFIED_SALES_TYPE_ID][0])) {
                $unverifiedBudgetBaseInfo = $budgetBaseInfo[self::UNVERIFIED_SALES_TYPE_ID][0];
            }
            else {
                $unverifiedBudgetBaseInfo = $verifiedBudgetBaseInfo;

                $unverifiedBudgetBaseInfo[$campaignBudgetIdCol] = 0;
            }

            if(!empty($budgetBaseInfo[self::EMAIL_ONLY_SALES_TYPE_ID][0])) {
                $emailOnlyBudgetBaseInfo = $budgetBaseInfo[self::EMAIL_ONLY_SALES_TYPE_ID][0];
            }
            else {
                $emailOnlyBudgetBaseInfo = $verifiedBudgetBaseInfo;

                $emailOnlyBudgetBaseInfo[$campaignBudgetIdCol] = 0;
            }

            $companyId = $verifiedBudgetBaseInfo[LeadCampaign::COMPANY_ID];
            $neverExceedBudget = $verifiedBudgetBaseInfo[EloquentCompany::NEVER_EXCEED_BUDGET];
            $maxBudgetUsage = $verifiedBudgetBaseInfo[LeadCampaign::MAXIMUM_BUDGET_USAGE];
            $budgetUnit = $verifiedBudgetBaseInfo[$budgetUnitCol];

            $budgetUsageData->initCampaignBudgetDataBaseStructure($leadCampaignId, $companyId, $neverExceedBudget);

            $currentCampaignBudgetUsage = $allCampaignBudgetUsage[$leadCampaignId] ?? null;

            if(empty($currentCampaignBudgetUsage)) {
                $budgetUsageData->setBudgetUsage(
                    $leadCampaignId,
                    $companyId,
                    0,
                    BudgetCategory::VERIFIED->value,
                    $verifiedBudgetBaseInfo[$budgetSpentCol],
                    $verifiedBudgetBaseInfo[$dailyBudgetCol],
                    $verifiedBudgetBaseInfo[$budgetTimeframeCol],
                    $maxBudgetUsage,
                    $budgetUnit,
                    $neverExceedBudget
                );

                continue;
            }

            foreach(self::VERIFIED_SALE_TYPE_IDS as $saleTypeId) {
                $verifiedBudgetBaseInfo[$budgetSpentCol] += !empty($currentCampaignBudgetUsage[$saleTypeId][0][$budgetSpentCol]) ? $currentCampaignBudgetUsage[$saleTypeId][0][$budgetSpentCol] : 0;
            }

            /**
             * Unverified and Email Only sale types have their own budget
             */
            $unverifiedBudgetBaseInfo[$budgetSpentCol] = !empty($currentCampaignBudgetUsage[self::UNVERIFIED_SALES_TYPE_ID][0][$budgetSpentCol]) ? $currentCampaignBudgetUsage[self::UNVERIFIED_SALES_TYPE_ID][0][$budgetSpentCol] : 0;
            $emailOnlyBudgetBaseInfo[$budgetSpentCol] = !empty($currentCampaignBudgetUsage[self::EMAIL_ONLY_SALES_TYPE_ID][0][$budgetSpentCol]) ? $currentCampaignBudgetUsage[self::EMAIL_ONLY_SALES_TYPE_ID][0][$budgetSpentCol] : 0;

            $verifiedBudgetBaseInfo[$budgetSpentCol] = round($verifiedBudgetBaseInfo[$budgetSpentCol], 2);
            $unverifiedBudgetBaseInfo[$budgetSpentCol] = round($unverifiedBudgetBaseInfo[$budgetSpentCol], 2);
            $emailOnlyBudgetBaseInfo[$budgetSpentCol] = round($emailOnlyBudgetBaseInfo[$budgetSpentCol], 2);

            $budgetUsageData->setBudgetUsage(
                $leadCampaignId,
                $companyId,
                0,
                BudgetCategory::VERIFIED->value,
                $verifiedBudgetBaseInfo[$budgetSpentCol],
                $verifiedBudgetBaseInfo[$dailyBudgetCol],
                $verifiedBudgetBaseInfo[$budgetTimeframeCol],
                $maxBudgetUsage,
                $budgetUnit,
                $neverExceedBudget
            );

            $budgetUsageData->setBudgetUsage(
                $leadCampaignId,
                $companyId,
                $unverifiedBudgetBaseInfo[$campaignBudgetIdCol],
                BudgetCategory::UNVERIFIED->value,
                $unverifiedBudgetBaseInfo[$budgetSpentCol],
                $unverifiedBudgetBaseInfo[$dailyBudgetCol],
                $unverifiedBudgetBaseInfo[$saleTypeLastModifiedLeadLimit] ?? $unverifiedBudgetBaseInfo[$budgetTimeframeCol],
                $maxBudgetUsage,
                $budgetUnit,
                $neverExceedBudget
            );

            $budgetUsageData->setBudgetUsage(
                $leadCampaignId,
                $companyId,
                $emailOnlyBudgetBaseInfo[$campaignBudgetIdCol],
                BudgetCategory::EMAIL_ONLY->value,
                $emailOnlyBudgetBaseInfo[$budgetSpentCol],
                $emailOnlyBudgetBaseInfo[$dailyBudgetCol],
                $emailOnlyBudgetBaseInfo[$saleTypeLastModifiedLeadLimit] ?? $emailOnlyBudgetBaseInfo[$budgetTimeframeCol],
                $maxBudgetUsage,
                $budgetUnit,
                $neverExceedBudget
            );
        }

        return $budgetUsageData;
    }
}
