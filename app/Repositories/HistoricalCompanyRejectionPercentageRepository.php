<?php

namespace App\Repositories;

use App\DataModels\HistoricalCompanyRejectionPercentagesDataModel;
use App\Models\ComputedRejectionStatistic;
use App\Models\HistoricalCompanyRejectionPercentage;
use App\Models\Odin\Product;
use App\Repositories\Odin\ProductRepository;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use App\Enums\Odin\Product as ProductEnum;
use App\Enums\Odin\HistoricalCompanyRejectionPercentageType as RPT;

class HistoricalCompanyRejectionPercentageRepository
{
    /**
     * @param ProductRepository $productRepository
     */
    public function __construct(
        private readonly ProductRepository $productRepository
    ) {

    }

    /**
     * @param int $companyId
     * @param ProductEnum $product
     * @return int|null
     */
    public function getLatestEligibilityTimestamp(int $companyId, ProductEnum $product): ?int
    {
        $now = CarbonImmutable::now('UTC');

        /** @var HistoricalCompanyRejectionPercentage|null $latestOverRejectionThreshold */
        $rejectionPercentages = HistoricalCompanyRejectionPercentage::query()
            ->where(HistoricalCompanyRejectionPercentage::FIELD_COMPANY_ID, $companyId)
            ->where(HistoricalCompanyRejectionPercentage::FIELD_YEAR, $now->year)
            ->whereHas(HistoricalCompanyRejectionPercentage::RELATION_PRODUCT, function($has) use ($product) {
                $has->where(Product::TABLE.'.'.Product::FIELD_NAME, $product->value);
            })
            ->first()
            ?->{HistoricalCompanyRejectionPercentage::FIELD_REJECTION_PERCENTAGES};

        if(empty($rejectionPercentages)) {
            return null;
        }

        if($product === ProductEnum::LEAD) {
            $rejectionThreshold = config('sales.leads.overall_rejection_percentage_threshold');
        }
        else if($product === ProductEnum::APPOINTMENT) {
            $rejectionThreshold = config('sales.appointments.overall_rejection_percentage_threshold');
        }
        else {
            $rejectionThreshold = config('sales.defaults.overall_rejection_percentage_threshold');
        }

        $thirtyDaysAgo = $now->subDays(29)->dayOfYear;

        $latestOverRejectionDay = 0;
        for($ld = $now->dayOfYear; $ld >= $thirtyDaysAgo; $ld--) {
            $date = $now->startOfYear()->setDay($ld);

            if($rejectionPercentages->getRejectionPercentage($date, RPT::OVERALL) >= $rejectionThreshold) {
                $latestOverRejectionDay = $ld;

                break;
            }
        }

        if($latestOverRejectionDay > 0) {
            $firstUnderRejectionDay = 0;
            for($fd = $latestOverRejectionDay; $fd <= $now->dayOfYear; $fd++) {
                $date = $now->startOfYear()->setDay($fd);

                if($rejectionPercentages->getRejectionPercentage($date, RPT::OVERALL) !== null
                && $rejectionPercentages->getRejectionPercentage($date, RPT::OVERALL) < $rejectionThreshold) {
                    $firstUnderRejectionDay = $fd;

                    break;
                }
            }

            if($firstUnderRejectionDay > 0) {
                return $now->dayOfYear($firstUnderRejectionDay)->startOfDay()->timestamp;
            }
        }

        return null;
    }

    /**
     * @param int $chunkSize
     * @return bool
     */
    public function recordCurrentRejectionPercentages(int $chunkSize = 500): bool
    {
        $now = Carbon::now('UTC');

        $productIdsKeyedArray = array_fill_keys(
            $this->productRepository->getAllProducts()->pluck(Product::FIELD_ID)->toArray(),
            []
        );

        $missingCompanyIds = $productIdsKeyedArray;

        ComputedRejectionStatistic::query()
            ->leftJoin(HistoricalCompanyRejectionPercentage::TABLE, function($join) use ($now) {
                $join
                    ->on(
                        ComputedRejectionStatistic::TABLE.'.'.ComputedRejectionStatistic::FIELD_COMPANY_ID,
                        '=',
                        HistoricalCompanyRejectionPercentage::TABLE.'.'.HistoricalCompanyRejectionPercentage::FIELD_COMPANY_ID
                    )
                    ->on(
                        ComputedRejectionStatistic::TABLE.'.'.ComputedRejectionStatistic::FIELD_PRODUCT_ID,
                        '=',
                        HistoricalCompanyRejectionPercentage::TABLE.'.'.HistoricalCompanyRejectionPercentage::FIELD_PRODUCT_ID
                    )
                    ->where(HistoricalCompanyRejectionPercentage::TABLE.'.'.HistoricalCompanyRejectionPercentage::FIELD_YEAR, $now->year);
            })
            ->whereNull(HistoricalCompanyRejectionPercentage::TABLE.'.'.HistoricalCompanyRejectionPercentage::FIELD_ID)
            ->select([
                ComputedRejectionStatistic::TABLE.'.'.ComputedRejectionStatistic::FIELD_COMPANY_ID,
                ComputedRejectionStatistic::TABLE.'.'.ComputedRejectionStatistic::FIELD_PRODUCT_ID
            ])
            ->distinct()
            ->cursor()
            ->each(function($row) use (&$missingCompanyIds) {
                $missingCompanyIds[$row[HistoricalCompanyRejectionPercentage::FIELD_PRODUCT_ID]][] = $row[HistoricalCompanyRejectionPercentage::FIELD_COMPANY_ID];
            });

        $updateRowsEmpty = $productIdsKeyedArray;

        ComputedRejectionStatistic::query()
            ->select([
                ComputedRejectionStatistic::FIELD_OVERALL_REJECTION_PERCENTAGE,
                ComputedRejectionStatistic::FIELD_CRM_REJECTION_PERCENTAGE,
                ComputedRejectionStatistic::FIELD_MANUAL_REJECTION_PERCENTAGE,
                ComputedRejectionStatistic::FIELD_COMPANY_ID,
                ComputedRejectionStatistic::FIELD_PRODUCT_ID
            ])
            ->chunk($chunkSize, function(Collection $rejectionPercentages) use ($missingCompanyIds, $now, $updateRowsEmpty) {
                $this->processComputedRejectionStatisticsChunkForHistoricalRecord(
                    $rejectionPercentages,
                    $missingCompanyIds,
                    $now,
                    $updateRowsEmpty
                );
            });

        return true;
    }

    /**
     * @param Collection $rejectionPercentages
     * @param array $missingCompanyIds
     * @param Carbon $now
     * @param array $updateRowsEmpty
     * @return bool
     */
    private function processComputedRejectionStatisticsChunkForHistoricalRecord(
        Collection $rejectionPercentages,
        array $missingCompanyIds,
        Carbon $now,
        array $updateRowsEmpty
    ): bool
    {
        DB::transaction(function() use ($missingCompanyIds, $rejectionPercentages, $now, $updateRowsEmpty) {
            $insertRows = [];
            $updateRows = $updateRowsEmpty;

            $rejectionPercentagesCol = HistoricalCompanyRejectionPercentage::FIELD_REJECTION_PERCENTAGES;

            $year = $now->year;

            foreach($rejectionPercentages as $rp) {
                if(in_array(
                    $rp[ComputedRejectionStatistic::FIELD_COMPANY_ID],
                    $missingCompanyIds[$rp[ComputedRejectionStatistic::FIELD_PRODUCT_ID]],
                    true
                )) {
                    $hcrpdm = new HistoricalCompanyRejectionPercentagesDataModel();

                    $hcrpdm->setRejectionPercentage($rp[ComputedRejectionStatistic::FIELD_OVERALL_REJECTION_PERCENTAGE], $now, RPT::OVERALL);
                    $hcrpdm->setRejectionPercentage($rp[ComputedRejectionStatistic::FIELD_CRM_REJECTION_PERCENTAGE], $now, RPT::CRM);
                    $hcrpdm->setRejectionPercentage($rp[ComputedRejectionStatistic::FIELD_MANUAL_REJECTION_PERCENTAGE], $now, RPT::MANUAL);

                    $insertRows[] = [
                        HistoricalCompanyRejectionPercentage::FIELD_COMPANY_ID => $rp[ComputedRejectionStatistic::FIELD_COMPANY_ID],
                        HistoricalCompanyRejectionPercentage::FIELD_YEAR => $year,
                        HistoricalCompanyRejectionPercentage::FIELD_PRODUCT_ID => $rp[ComputedRejectionStatistic::FIELD_PRODUCT_ID],
                        HistoricalCompanyRejectionPercentage::FIELD_REJECTION_PERCENTAGES => $hcrpdm->toJson(),
                        HistoricalCompanyRejectionPercentage::CREATED_AT => $now->format("Y-m-d H:i:s")
                    ];
                }
                else {
                    $updateRows[$rp[ComputedRejectionStatistic::FIELD_PRODUCT_ID]][$rp[ComputedRejectionStatistic::FIELD_COMPANY_ID]] = sprintf(
                        "WHEN %s THEN JSON_SET(%s, '%s', %s)",
                        $rp[ComputedRejectionStatistic::FIELD_COMPANY_ID],
                        $rejectionPercentagesCol,
                        sprintf(
                            '$."%s"',
                            $now->format(HistoricalCompanyRejectionPercentagesDataModel::DATE_FORMAT)
                        ),
                        sprintf(
                            'JSON_OBJECT("%s", %s, "%s", %s, "%s", %s)',
                            RPT::OVERALL->value,
                            $rp[ComputedRejectionStatistic::FIELD_OVERALL_REJECTION_PERCENTAGE],
                            RPT::CRM->value,
                            $rp[ComputedRejectionStatistic::FIELD_CRM_REJECTION_PERCENTAGE],
                            RPT::MANUAL->value,
                            $rp[ComputedRejectionStatistic::FIELD_MANUAL_REJECTION_PERCENTAGE]
                        )
                    );
                }
            }

            if(!empty($insertRows)) {
                HistoricalCompanyRejectionPercentage::query()->insert($insertRows);
            }

            foreach($updateRows as $productId => $updates) {
                if(empty($updates)) {
                    continue;
                }

                $caseQuery = sprintf(
                    "(CASE %s %s END)",
                    HistoricalCompanyRejectionPercentage::FIELD_COMPANY_ID,
                    implode(' ', $updates)
                );

                HistoricalCompanyRejectionPercentage::query()
                    ->whereIn(HistoricalCompanyRejectionPercentage::FIELD_COMPANY_ID, array_keys($updates))
                    ->where(HistoricalCompanyRejectionPercentage::FIELD_PRODUCT_ID, $productId)
                    ->update([
                        HistoricalCompanyRejectionPercentage::FIELD_REJECTION_PERCENTAGES => DB::raw($caseQuery),
                        HistoricalCompanyRejectionPercentage::UPDATED_AT => $now->format("Y-m-d H:i:s")
                    ]);
            }
        });

        return true;
    }
}
