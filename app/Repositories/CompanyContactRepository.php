<?php

namespace App\Repositories;

use App\Contracts\Repositories\CompanyContactRepositoryContract;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Legacy\EloquentUser;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignDeliveryMethod;
use App\Models\Legacy\LeadDeliveryMethod;
use App\Models\Odin\CompanyUser;
use App\Services\Legacy\APIConsumer;
use App\Services\PubSub\PubSubService;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class CompanyContactRepository implements CompanyContactRepositoryContract
{
    const REQUEST_COMPANY_REFERENCE = 'company_reference';
    const REQUEST_CONTACT_ID        = 'contact_id';
    const REQUEST_DATA              = 'data';

    /**
     * @param APIConsumer $apiConsumer
     * @param PubSubService $pubSubService
     */
    public function __construct(protected APIConsumer $apiConsumer, protected PubSubService $pubSubService) {}

    /**
     * @param int $id
     * @return array|null
     */
    public function getLegacyContact(int $id): ?array
    {
        $contact = EloquentCompanyContact::query()->find($id);

        if(empty($contact)) {
            return null;
        }

        return [
            "id" => $contact->{EloquentCompanyContact::FIELD_CONTACT_ID},
            "name" => "{$contact->{EloquentCompanyContact::FIELD_FIRST_NAME}} {$contact->{EloquentCompanyContact::FIELD_LAST_NAME}}",
            "company" => $contact->{EloquentCompanyContact::RELATION_COMPANY}?->{EloquentCompany::COMPANY_NAME},
            "company_id" => $contact->{EloquentCompanyContact::FIELD_COMPANY_ID}
        ];
    }

    /**
     * @param int $campaignId
     * @param bool $phoneDeliveryMustBeEnabled
     * @param bool $emailDeliveryMustBeEnabled
     * @return Collection<int, EloquentCompanyContact>
     */
    public function getActiveContactsByCampaignId(int $campaignId, bool $phoneDeliveryMustBeEnabled, bool $emailDeliveryMustBeEnabled): Collection
    {
        return EloquentCompanyContact::query()->whereIn(EloquentCompanyContact::FIELD_CONTACT_ID, function ($query) use ($campaignId, $phoneDeliveryMustBeEnabled, $emailDeliveryMustBeEnabled) {
            $query->select(LeadDeliveryMethod::TABLE . '.' . LeadDeliveryMethod::FIELD_CONTACT_ID)
                  ->from(LeadCampaignDeliveryMethod::TABLE)
                  ->leftJoin(
                      LeadDeliveryMethod::TABLE,
                      LeadCampaignDeliveryMethod::TABLE . '.' . LeadCampaignDeliveryMethod::FIELD_LEAD_DELIVERY_METHOD_ID,
                      LeadDeliveryMethod::TABLE . '.' . LeadDeliveryMethod::FIELD_ID
                  )
                  ->where(LeadCampaignDeliveryMethod::TABLE . '.' . LeadCampaignDeliveryMethod::FIELD_LEAD_CAMPAIGN_ID, $campaignId);
            if ($phoneDeliveryMustBeEnabled) {
                $query->where(LeadCampaignDeliveryMethod::TABLE . '.' . LeadCampaignDeliveryMethod::FIELD_TYPE, 'like', '%' . LeadCampaignDeliveryMethod::TYPE_VALUE_SMS . '%');
            }
            if ($emailDeliveryMustBeEnabled) {
                $query->where(LeadCampaignDeliveryMethod::TABLE . '.' . LeadCampaignDeliveryMethod::FIELD_TYPE, 'like', '%' . LeadCampaignDeliveryMethod::TYPE_VALUE_EMAIL . '%');
            }
        })->get();
    }

    /**
     * @param int $companyId
     * @param bool $phoneDeliveryMustBeEnabled
     * @param bool $emailDeliveryMustBeEnabled
     * @return Collection
     */
    public function getActiveContactsForCompany(int $companyId, bool $phoneDeliveryMustBeEnabled, bool $emailDeliveryMustBeEnabled): Collection
    {
        $query = EloquentCompanyContact::query()
            ->where(EloquentCompanyContact::FIELD_COMPANY_ID, $companyId)
            ->where(EloquentCompanyContact::FIELD_STATUS, EloquentCompanyContact::STATUS_ACTIVE);

        if($phoneDeliveryMustBeEnabled)
            $query->where(EloquentCompanyContact::FIELD_PHONE, '<>', "");

        if($emailDeliveryMustBeEnabled)
            $query->where(EloquentCompanyContact::FIELD_EMAIL, '<>', "");

        return $query->get();
    }

    /**
     * Finds a contact by a phone number.
     *
     * @param string $phoneNumber
     * @return EloquentCompanyContact|null
     */
    public function findContactByPhoneNumber(string $phoneNumber): ?EloquentCompanyContact
    {
        return EloquentCompanyContact::query()
            ->where(function($query) use ($phoneNumber) {
              return $query->where(EloquentCompanyContact::FIELD_PHONE, $phoneNumber)
                  ->orWhere(EloquentCompanyContact::FIELD_MOBILE, $phoneNumber);
            })
            ->orderByDesc(EloquentCompanyContact::FIELD_CONTACT_ID)->first();
    }

    /**
     * Finds a company user by their phone number
     *
     * @param string $phoneNumber
     * @return EloquentUser|null
     */
    public function findUserByPhoneNumber(string $phoneNumber): ?EloquentUser
    {
        return EloquentUser::query()
            ->where(function($query) use ($phoneNumber) {
                return $query->where(EloquentUser::PHONE, $phoneNumber);
            })
            ->where(EloquentUser::COMPANY_ID, '>', 0)
            ->orderByDesc(EloquentUser::USER_ID)->first();
    }

    /**
     * @inheritDoc
     */
    public function addCompanyContact(string $companyReference, array $contact): bool
    {
        return $this->pubSubService->handle(EventCategory::ADMIN2->value, EventName::COMPANY_CONTACT_CREATED->value,
            [
                self::REQUEST_COMPANY_REFERENCE => $companyReference,
                self::REQUEST_DATA              => $contact
            ]
        ) !== null;
    }

    /**
     * @inheritDoc
     */
    public function updateCompanyContact(string $companyReference, int $contactId, array $data): bool
    {
        return $this->pubSubService->handle(EventCategory::ADMIN2->value, EventName::COMPANY_CONTACT_UPDATED->value,
            [
                self::REQUEST_COMPANY_REFERENCE => $companyReference,
                self::REQUEST_CONTACT_ID        => $contactId,
                self::REQUEST_DATA              => $data
            ]
        ) !== null;
    }

    /**
     * @inheritDoc
     */
    public function deleteCompanyContact(string $companyReference, int $contactId): bool
    {
        return $this->pubSubService->handle(EventCategory::ADMIN2->value, EventName::COMPANY_CONTACT_DELETED->value,
                [
                    self::REQUEST_COMPANY_REFERENCE => $companyReference,
                    self::REQUEST_CONTACT_ID        => $contactId,
                ]
            ) !== null;
    }

    /**
     * @param int $contactId
     * @return bool
     */
    public function toggleContactPin(int $contactId): bool
    {
        $targetAction = CompanyUser::query()->findOrFail($contactId);

        return $targetAction?->update([
            CompanyUser::FIELD_PINNED => !$targetAction->{CompanyUser::FIELD_PINNED}
        ]) ?? false;
    }

    /**
     * @param array $cellPhones
     * @param array $officePhones
     * @param array $emailAddresses
     * @return Collection
     */
    public function getCompaniesWithSimilarContactDetails(array $cellPhones, array $officePhones, array $emailAddresses): Collection
    {
        /** @var Collection $companyUsers */
        $companyUsers = CompanyUser::query()
            ->whereIn(CompanyUser::FIELD_CELL_PHONE, $cellPhones)
            ->orWhere(fn(Builder $query) =>
                $query->whereIn(CompanyUser::FIELD_OFFICE_PHONE, $officePhones))
            ->orWhere(fn(Builder $query) =>
                $query->whereIn(CompanyUser::FIELD_EMAIL, $emailAddresses))
            ->get();

        return $companyUsers
            ->pluck(CompanyUser::FIELD_COMPANY_ID)
            ->unique();
    }
}
