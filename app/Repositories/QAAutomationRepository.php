<?php

namespace App\Repositories;

use App\Models\QAAutomation\QAAutomationIndustryService;
use App\Models\QAAutomation\QAAutomationRule;
use Illuminate\Support\Collection;

class QAAutomationRepository
{
    /**
     * @param int $id
     * @return QAAutomationIndustryService
     */
    public function findByIdOrFail(int $id): QAAutomationIndustryService
    {
        return QAAutomationIndustryService::query()->findOrFail($id);
    }

    /**
     * @return Collection
     */
    public function getQAIndustryServiceConfigurations(): Collection
    {
        return QAAutomationIndustryService::all();
    }

    /**
     * @param int $industryServiceId
     * @param bool|null $enabled
     * @param int|null $type
     * @param int|null $qaIndustryServiceId
     * @return QAAutomationIndustryService
     */
    public function updateQAIndustryServiceConfiguration(
        int $industryServiceId,
        ?bool $enabled = true,
        ?int $type = 1,
        ?int $qaIndustryServiceId = null
    ): QAAutomationIndustryService
    {
        if($qaIndustryServiceId) {
            $qaIndustryService = QAAutomationIndustryService::query()->findOrFail($qaIndustryServiceId);
            $qaIndustryService->update([
               QAAutomationIndustryService::FIELD_INDUSTRY_SERVICE_ID => $industryServiceId,
               QAAutomationIndustryService::FIELD_TYPE => $type,
               QAAutomationIndustryService::FIELD_ENABLED => $enabled,
            ]);
        } else {
            $qaIndustryService = QAAutomationIndustryService::query()->create([
                QAAutomationIndustryService::FIELD_INDUSTRY_SERVICE_ID => $industryServiceId,
                QAAutomationIndustryService::FIELD_TYPE => $type,
                QAAutomationIndustryService::FIELD_ENABLED => $enabled,
            ]);
        }

        return $qaIndustryService;
    }

    /**
     * @return Collection
     */
    public function getQARules(): Collection
    {
        return QAAutomationRule::all();
    }

    /**
     * @param array $fields
     * @param bool|null $enabled
     * @param bool|null $matchSuccess
     * @param string|null $expression
     * @param int|null $type
     * @param array|null $data
     * @param int|null $qaRuleId
     * @return QAAutomationRule
     */
    public function updateQARule(
        array $fields,
        ?bool $enabled = true,
        ?bool $matchSuccess = false,
        ?string $expression = '',
        ?int $type = 1,
        ?array $data = null,
        ?int $qaRuleId = null,
    ): QAAutomationRule
    {
        if($qaRuleId) {
            $qaRule = QAAutomationRule::query()->findOrFail($qaRuleId);
            $qaRule->update([
                QAAutomationRule::FIELD_TYPE => $type,
                QAAutomationRule::FIELD_ENABLED => $enabled,
                QAAutomationRule::FIELD_MATCH_SUCCESS => $matchSuccess,
                QAAutomationRule::FIELD_FIELDS => $fields,
                QAAutomationRule::FIELD_EXPRESSION => $expression,
                QAAutomationRule::FIELD_DATA => $data,
            ]);
        } else {
            $qaRule = QAAutomationRule::query()->create([
                QAAutomationRule::FIELD_TYPE => $type,
                QAAutomationRule::FIELD_ENABLED => $enabled,
                QAAutomationRule::FIELD_MATCH_SUCCESS => $matchSuccess,
                QAAutomationRule::FIELD_FIELDS => $fields,
                QAAutomationRule::FIELD_EXPRESSION => $expression,
                QAAutomationRule::FIELD_DATA => $data,
            ]);
        }
        return $qaRule;
    }

    /**
     * @param int $qaRuleId
     * @return void
     */
    public function deleteQARule(int $qaRuleId): void
    {
        $rule = QAAutomationRule::find($qaRuleId);
        if ($rule)
            $rule->delete();
    }
}
