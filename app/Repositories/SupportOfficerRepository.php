<?php

namespace App\Repositories;

use App\Models\SupportOfficer;

class SupportOfficerRepository
{
    /**
     * @param int $userId
     * @param bool $includedInRoundRobin
     *
     * @return SupportOfficer
     */
    public function createSupportOfficer(int $userId, bool $includedInRoundRobin): SupportOfficer
    {
        /** @var SupportOfficer $supportOfficer */
        $supportOfficer = SupportOfficer::query()->create([
            SupportOfficer::FIELD_USER_ID                 => $userId,
            SupportOfficer::FIELD_INCLUDED_IN_ROUND_ROBIN => $includedInRoundRobin
        ]);

        return $supportOfficer;
    }

    /**
     * @param SupportOfficer $supportOfficer
     * @param bool $includedInRoundRobin
     *
     * @return bool
     */
    public function updateSupportOfficer(SupportOfficer $supportOfficer, bool $includedInRoundRobin): bool
    {
        $supportOfficer->include_in_round_robin = $includedInRoundRobin;

        return $supportOfficer->save();
    }
}
