<?php

namespace App\Repositories\Odin;

use App\DataModels\Odin\ConsumerProductTrackingPayloadDataModel;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductTracking;


class ConsumerTrackingRepository
{
    /**
     * Handles creating a new Consumer Tracking model
     *
     * @param array $data
     *
     * @return ConsumerProductTracking
     */
    public function createConsumerTrackingFromAttributes(array $data = []): ConsumerProductTracking
    {
        $consumerTracking = new ConsumerProductTracking();

        $consumerTracking->fill(collect($data)->filter(fn($value) => $value !== null)->toArray());

        $consumerTracking->{ConsumerProductTracking::PAYLOAD} = ConsumerProductTrackingPayloadDataModel::fromJson(json_encode($data));

        $consumerTracking->save();

        return $consumerTracking;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param array $data
     * @return ConsumerProductTracking
     */
    public function createOrUpdateConsumerTrackingFromAttributes(ConsumerProduct $consumerProduct, array $data = []): ConsumerProductTracking
    {
        $existing = $consumerProduct->consumerProductTracking;
        if ($existing)
        {
            foreach($data as $key => $value) {
                $existing->{$key} = $value;
            }

            $existing->{ConsumerProductTracking::PAYLOAD} = ConsumerProductTrackingPayloadDataModel::fromJson(json_encode($data));

            $existing->save();

            return $existing->refresh();
        }
        else {
            return $this->createConsumerTrackingFromAttributes($data);
        }
    }

    /**
     * Returns a company by the legacy id or fails.
     *
     * @param int $legacyId
     * @return ConsumerProductTracking
     */
    public function getConsumerTrackingByLegacyIdOrFail(int $legacyId): ConsumerProductTracking
    {
        /** @var ConsumerProductTracking $consumerTracking */
        $consumerTracking = ConsumerProductTracking::query()->where(ConsumerProductTracking::LEGACY_ID, $legacyId)->firstOrFail();
        return $consumerTracking;
    }

}
