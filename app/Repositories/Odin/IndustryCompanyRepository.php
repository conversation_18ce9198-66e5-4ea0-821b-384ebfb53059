<?php

namespace App\Repositories\Odin;

use App\Contracts\Repositories\Odin\IndustryCompanyRepositoryContract;
use App\Models\Legacy\EloquentCompany;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyIndustry;
use App\Repositories\Legacy\CompanyRepository as LegacyCompanyRepository;
use Illuminate\Support\Collection;

class IndustryCompanyRepository implements IndustryCompanyRepositoryContract
{
    const RESPONSE_STATUS_KEY = 'status';

    /**
     * @param LegacyCompanyRepository $legacyCompanyRepository
     */
    public function __construct(
        protected LegacyCompanyRepository $legacyCompanyRepository,
    ){}

    /**
     * @inheritDoc
     */
    public function getIndustryCompanies(int $industry): Collection
    {
        return CompanyIndustry::query()->where(CompanyIndustry::FIELD_INDUSTRY_ID, $industry)->latest()->get();
    }

    /**
     * @inheritDoc
     */
    public function getIndustryCompanyById(int $industryCompanyId): ?CompanyIndustry
    {
        /** @var CompanyIndustry|null $companyIndustry */
        $companyIndustry = CompanyIndustry::query()->find($industryCompanyId);

        return $companyIndustry;
    }

    /**
     * @inheritDoc
     */
    public function addIndustryCompany(int $industry, int $company): bool
    {
        return CompanyIndustry::query()->firstOrCreate(
            [
                CompanyIndustry::FIELD_INDUSTRY_ID => $industry,
                CompanyIndustry::FIELD_COMPANY_ID  => $company
            ]
        ) !== null;
    }

    /**
     * @inheritDoc
     */
    public function deleteIndustryCompany(int $id): bool
    {
        return CompanyIndustry::query()->where(CompanyIndustry::FIELD_ID, $id)->delete();
    }

    /**
     * @inheritDoc
     */
    public function getNonAddedCompaniesAgainstIndustryId(int $industry): Collection
    {
        /** @var CompanyIndustry|null $industryCompanies */
        $industryCompanies = CompanyIndustry::query()
                                    ->where(CompanyIndustry::FIELD_INDUSTRY_ID, $industry)
                                    ->distinct()
                                    ->pluck(CompanyIndustry::FIELD_COMPANY_ID);

        return Company::query()->whereNotIn(Company::FIELD_ID, $industryCompanies)->latest()->get();
    }

    /**
     * @inheritDoc
     */
    public function setCompanyTypeInLegacy(Company $company, array $industryIds): bool
    {
        if(!$industryIds) return false;

        $legacyData = [EloquentCompany::TYPE => $this->legacyCompanyRepository->getLegacyCompanyTypeFromIndustryIds($industryIds)];

        $legacyResponse = $this->legacyCompanyRepository->setCompanyType(
            companyReference : $company->{Company::FIELD_REFERENCE},
            data             : $legacyData
        );

        if(!$legacyResponse
        || !key_exists(self::RESPONSE_STATUS_KEY, $legacyResponse)
        || !$legacyResponse[self::RESPONSE_STATUS_KEY]) {
            logger()->warning(
                "The company type couldn't be set up for the company (Reference: `$company->reference`) due to the failure in communication with the Legacy server."
                .PHP_EOL."The server responded as: " . json_encode($legacyResponse)
            );
        }

        return true;
    }
}
