<?php

namespace App\Repositories\Odin;

use App\Enums\Odin\Industry;
use App\Enums\Odin\Product as ProductEnum;
use App\Models\MissedProducts\MissedProduct;
use App\Models\Odin\CompanyService;
use App\Models\Odin\Industry as IndustryModel;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ServiceProduct;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class ServiceProductRepository
{
    protected ProductRepository $productRepository;

    public function __construct(ProductRepository $productRepository)
    {
        $this->productRepository = $productRepository;
    }

    /**
     * @param string $industry
     * @param ProductEnum $product
     * @return ServiceProduct|Model|null
     */
    public function getDefaultServiceProductByIndustry(string $industry, ProductEnum $product = ProductEnum::LEAD): ServiceProduct|Model|null
    {
        /** @var IndustryModel $solarIndustry*/
        $solarIndustry = IndustryModel::query()->where(IndustryModel::FIELD_NAME, Industry::SOLAR)->firstOrFail();

        /** @var IndustryModel $roofingIndustry*/
        $roofingIndustry = IndustryModel::query()->where(IndustryModel::FIELD_NAME, Industry::ROOFING)->firstOrFail();

        /** @var IndustryService $solarInstallationService */
        $solarInstallationService = $solarIndustry->services->firstOrFail(fn(IndustryService $industryService) => $industryService->slug === 'solar-installation');

        /** @var IndustryService $roofReplacementService */
        $roofReplacementService = $roofingIndustry->services->firstOrFail(fn(IndustryService $industryService) => $industryService->slug === 'roof-replacement');

        /** @var Product $product */
        $product = Product::query()->where(Product::FIELD_NAME, $product)->firstOrFail();

        return match (strtolower($industry)) {
            strtolower(Industry::ROOFING->value) => ServiceProduct::query()
                ->where(ServiceProduct::FIELD_PRODUCT_ID, $product->id)
                ->where(ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $roofReplacementService->id)
                ->first(),
            default => ServiceProduct::query()
                ->where(ServiceProduct::FIELD_PRODUCT_ID, $product->id)
                ->where(ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $solarInstallationService->id)
                ->first(),
        };
    }

    public function getServiceProductByIndustry(int $industryServiceId, string $productName): ?ServiceProduct
    {
        $product = $this->productRepository->getProductByName($productName);
        return ServiceProduct::query()
            ->where(ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, '=', $industryServiceId)
            ->where(ServiceProduct::FIELD_PRODUCT_ID, '=', $product->id)
            ->get()
            ?->first();
    }

    /**
     * @param int $industryServiceId
     * @param int $productId
     *
     * @return ServiceProduct
     */
    public function findServiceProductByIndustryServiceAndProductOrFail(int $industryServiceId, int $productId): ServiceProduct
    {
        /** @var ServiceProduct */
        return ServiceProduct::query()
            ->where(ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $industryServiceId)
            ->where(ServiceProduct::FIELD_PRODUCT_ID, $productId)
            ->firstOrFail();
    }

    /**
     * Builds a query to fetch a given company's service products, ordering the result
     * based on the service product with the most amount of products.
     *
     * @param int|null $companyId
     * @return Builder
     */
    public function getServiceProductsByCompany(?int $companyId = null): Builder
    {
        return ServiceProduct::query()
            ->select(ServiceProduct::TABLE . '.*')
            ->selectRaw(ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_ID . ', COUNT(' . MissedProduct::TABLE . '.' . MissedProduct::FIELD_ID . ') as product_count')
            ->join(MissedProduct::TABLE, MissedProduct::TABLE . '.' . MissedProduct::FIELD_INDUSTRY_SERVICE_ID, '=', ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_INDUSTRY_SERVICE_ID)
            ->where(ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_PRODUCT_ID, '=', 1) // Only retrieve Lead Service Products
            ->when($companyId, function($query) use ($companyId) {
                $industryServiceIds = CompanyService::query()
                    ->distinct()
                    ->where(CompanyService::FIELD_COMPANY_ID, "=", $companyId)
                    ->pluck(CompanyService::FIELD_INDUSTRY_SERVICE_ID)
                    ->toArray();

                return $query->whereIn(ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $industryServiceIds);
            })
            ->groupBy(ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_ID)
            ->orderBy('product_count', 'desc');
    }

    /**
     * @param int $industryServiceId
     * @param ProductEnum $product
     * @return ServiceProduct|null
     */
    public function getServiceProductByProductAndIndustryService(int $industryServiceId, ProductEnum $product = ProductEnum::LEAD): ?ServiceProduct
    {
        /** @type ServiceProduct */
        return ServiceProduct::query()
            ->whereHas(ServiceProduct::RELATION_PRODUCT, function($has) use ($product) {
                $has->where(Product::FIELD_NAME, $product->value);
            })
            ->where(ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $industryServiceId)
            ->first();
    }

    /**
     * @return Collection
     */
    public function all(): Collection
    {
        return ServiceProduct::all();
    }
}

