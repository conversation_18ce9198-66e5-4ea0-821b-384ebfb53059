<?php

namespace App\Repositories\Odin;

use App\Enums\Odin\Product as ProductEnum;
use App\Enums\Odin\ProductAssignmentAffectRejectionPercentage;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryLog;
use App\Models\Legacy\CrmDeliveryLog;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductRejection;
use App\Models\Odin\ServiceProduct;
use App\Models\TestProduct;
use App\Services\DatabaseHelperService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class ProductRejectionRepository extends ProductRepository
{
    /**
     * @param ProductAssignmentRepository $productAssignmentRepository
     * @param ProductRepository $productRepository
     */
    public function __construct(
        protected ProductAssignmentRepository $productAssignmentRepository,
        protected ProductRepository           $productRepository
    )
    {
    }

    /**
     * @param Company $company
     * @param bool $chargeable
     * @param bool $delivered
     * @param array|null $productIds
     * @param array|null $industryIds
     * @param array|null $industryServiceIds
     * @param int|null $startTimestamp
     * @param int|null $endTimestamp
     * @return Builder
     */
    private function getProductRejectionsForCompanyQuery(
        Company $company,
        bool    $chargeable,
        bool    $delivered,
        array   $productIds = null,
        array   $industryIds = null,
        array   $industryServiceIds = null,
        int     $startTimestamp = null,
        int     $endTimestamp = null
    ): Builder
    {
        $query = ProductRejection::query()
            ->join(ProductAssignment::TABLE,
                ProductRejection::TABLE . '.' . ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID, '=',
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID)
            ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID, $company->id)
            ->groupBy(ProductRejection::TABLE .'.'. ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID);

        if($chargeable) {
            $query->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CHARGEABLE, true);
        }

        if($delivered) {
            $query->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED, true);
        }

        if ($productIds || $industryIds || $industryServiceIds) {
            $query = $this->addProductIndustryOrServiceCriteria($query, $productIds, $industryIds, $industryServiceIds, true);
        } else {
            // This will show all product assignments that affect rejection percentage without specifying an affected product
            $query->where(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_AFFECT_REJECTION_PERCENTAGE,'!=', ProductAssignmentAffectRejectionPercentage::NO_AFFECT);
        }

        // We add the timespan criteria to the Product Assignment table because the rejection calculation should be based on the timing of the
        // assignment, not the date of the rejection
        return $this->productAssignmentRepository->addTimespanCriteria($query, $startTimestamp, $endTimestamp);
    }

    /**
     * @param Builder $query
     * @param int|null $startTimestamp
     * @param int|null $endTimestamp
     * @return Builder
     */
    private function addTimespanCriteria(Builder $query, int $startTimestamp = null, int $endTimestamp = null): Builder
    {
        if ($startTimestamp) {
            $query->where(ProductRejection::TABLE . '.' . ProductRejection::CREATED_AT, '>=', Carbon::createFromTimestamp($startTimestamp));
        }

        if ($endTimestamp) {
            $query->where(ProductRejection::TABLE . '.' . ProductRejection::CREATED_AT, '<=', Carbon::createFromTimestamp($endTimestamp));
        }

        return $query;
    }

    /**
     * @param Company $company
     * @param int|null $startTimestamp
     * @param int|null $endTimestamp
     * @return int
     */
    public function getLeadRejectionCountForCompany
    (
        Company $company,
        int     $startTimestamp = null,
        int     $endTimestamp = null
    ): int
    {
        $leadProductId = Product::query()->where(Product::FIELD_NAME, ProductEnum::LEAD)->first()->{Product::FIELD_ID};

        return $this->getProductRejectionCountForCompany($company, [$leadProductId], null, null, $startTimestamp, $endTimestamp);
    }

    /**
     * @param Company $company
     * @param array|null $productIds
     * @param array|null $industryIds
     * @param array|null $industryServiceIds
     * @param int|null $startTimestamp
     * @param int|null $endTimestamp
     * @return int
     */
    public function getProductRejectionCountForCompany(
        Company $company,
        array   $productIds = null,
        array   $industryIds = null,
        array   $industryServiceIds = null,
        int     $startTimestamp = null,
        int     $endTimestamp = null
    ): int
    {
        return $this->getProductRejectionsForCompanyQuery($company, true, true, $productIds, $industryIds, $industryServiceIds, $startTimestamp, $endTimestamp)
            ->pluck(ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID)
            ->count();
    }

    /**
     * @param int $id
     *
     * @return ProductRejection
     */
    public function findProductRejectionByProductAssigmentIdOrFail(int $id): ProductRejection
    {
        /** @var ProductRejection $productRejection */
        $productRejection = ProductRejection::query()->where(ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID, $id)->firstOrFail();

        return $productRejection;
    }

    /**
     * @param int $productAssignmentId
     * @param int $companyUserId
     * @param string $reason
     *
     * @return ProductRejection
     */
    public function createProductRejection(int $productAssignmentId, int $companyUserId, string $reason): ProductRejection
    {
        /** @var ProductRejection $productRejection */
        $productRejection = ProductRejection::query()->create([
            ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID => $productAssignmentId,
            ProductRejection::FIELD_COMPANY_USER_ID => $companyUserId,
            ProductRejection::FIELD_REASON => $reason
        ]);

        return $productRejection;
    }

    /**
     * @param Company $company
     * @param array|null $productIds
     * @param array|null $industryIds
     * @param array|null $industryServiceIds
     * @param int|null $startTimestamp
     * @param int|null $endTimestamp
     * @return float
     */
    public function getTotalProductRejectionsCostForCompany(
        Company $company,
        ?array   $productIds = null,
        ?array   $industryIds = null,
        ?array   $industryServiceIds = null,
        ?int     $startTimestamp = null,
        ?int     $endTimestamp = null
    ): float
    {
        $sum = $this->getProductRejectionsForCompanyQuery(
                        $company,
                        true,
                        true,
                        $productIds,
                        $industryIds,
                        $industryServiceIds,
                        $startTimestamp,
                        $endTimestamp
                    )
                    ->pluck(ProductAssignment::FIELD_COST)
                    ->sum();

        return round($sum, 2);
    }

    /**
     * @param ProductEnum $product
     * @param int|null $startTimestamp
     * @return int
     */
    public function getRejectionExpiry(ProductEnum $product = ProductEnum::LEAD, ?int $startTimestamp = null): int
    {
        $startTime = Carbon::createFromTimestamp($startTimestamp ?? time());

        if(($startTime->isWednesday() && $startTime->hour > 9)
        || $startTime->isThursday()
        || $startTime->isFriday()) {
            $startTime->addDays(2);
        }
        elseif($startTime->isSaturday()) {
            $startTime->addDay()->setTime(20, 0);
        }
        elseif($startTime->isSunday()) {
            $startTime->setTime(20, 0);
        }

        $rejectionWindowHours = match($product) {
            ProductEnum::LEAD, ProductEnum::DIRECT_LEADS => config('sales.leads.rejection_window_duration_hours'),
            ProductEnum::APPOINTMENT => config('sales.appointments.rejection_window_duration_hours')
        };

        return $startTime->addHours($rejectionWindowHours)->timestamp;
    }

    /**
     * @param int $companyId
     * @param int $productId
     * @param int|null $startTimestamp
     * @return float
     */
    public function getOdinCrmRejections(int $companyId, int $productId, int $startTimestamp = null): float
    {
        $startTime =  $startTimestamp ? Carbon::createFromTimestamp($startTimestamp) : Carbon::today()->subDays(29)->timestamp;

        $failedLogsQuery = CompanyCampaignDeliveryLog::query()
            ->join(CompanyCampaign::TABLE, CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID, '=', CompanyCampaignDeliveryLog::TABLE .'.'. CompanyCampaignDeliveryLog::FIELD_CAMPAIGN_ID)
            ->join(Company::TABLE, Company::TABLE .'.'. Company::FIELD_ID, '=', CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_COMPANY_ID)
            ->join(ProductAssignment::TABLE, ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, '=', CompanyCampaignDeliveryLog::TABLE .'.'. CompanyCampaignDeliveryLog::FIELD_CONSUMER_PRODUCT_ID)
            ->leftJoin(TestProduct::TABLE, TestProduct::TABLE .'.'. TestProduct::FIELD_PRODUCT_ID, '=', CompanyCampaignDeliveryLog::TABLE .'.'. CompanyCampaignDeliveryLog::FIELD_CONSUMER_PRODUCT_ID)
            ->where(Company::TABLE .'.'. Company::FIELD_ID, $companyId)
            ->where(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_PRODUCT_ID, $productId)
            ->where(CompanyCampaignDeliveryLog::TABLE .'.'. CompanyCampaignDeliveryLog::FIELD_SUCCESS, false)
            ->where(CompanyCampaignDeliveryLog::TABLE .'.'. CompanyCampaignDeliveryLog::CREATED_AT, '>', $startTime)
            ->where(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_DELIVERED, false)
            ->whereNull(TestProduct::TABLE .'.'. TestProduct::FIELD_ID)
            ->distinct(CompanyCampaignDeliveryLog::TABLE .'.'. CompanyCampaignDeliveryLog::FIELD_CONSUMER_PRODUCT_ID)
            ->selectRaw("SUM(". ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_COST. ") as total_cost")
            ->get()
            ->first();

        return floatval($failedLogsQuery['total_cost'] ?? 0);
    }

    /**
     * @return array
     */
    public function getCompanyIdsWithFailedLogsInLast30Days(): array
    {
        $startTime = Carbon::today()->subDays(29);

        return CrmDeliveryLog::query()
            ->select(DatabaseHelperService::database().'.'.Company::TABLE.'.'.Company::FIELD_ID)
            ->join(
                DatabaseHelperService::database().'.'.Company::TABLE,
                DatabaseHelperService::database().'.'.Company::TABLE.'.'.Company::FIELD_LEGACY_ID,
                '=',
                CrmDeliveryLog::TABLE.'.'.CrmDeliveryLog::COMPANY_ID
            )
            ->where(CrmDeliveryLog::TABLE.'.'.CrmDeliveryLog::DELIVERY_STATUS, false)
            ->where(CrmDeliveryLog::TABLE.'.'.CrmDeliveryLog::SEND_TEST_LEAD, false)
            ->where(CrmDeliveryLog::TABLE.'.'.CrmDeliveryLog::TIMESTAMP_ADDED, '>', $startTime)
            ->groupBy(DatabaseHelperService::database().'.'.Company::TABLE.'.'.Company::FIELD_ID)
            ->pluck(DatabaseHelperService::database().'.'.Company::TABLE.'.'.Company::FIELD_ID)
            ->toArray();
    }
}
