<?php

namespace App\Repositories\Odin\ProductProcessing;

use App\Builders\Pricing\BasePricingBuilder;
use App\DataModels\Odin\ConfigurableFieldDataModel;
use App\Enums\GlobalConfigurationKey;
use App\Models\GlobalConfiguration;
use App\Repositories\LocationRepository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class FloorPricingRepository
{
    /**
     * @param int $serviceProductId
     * @param int $qualityTierId
     * @param int $propertyTypeId
     * @return Builder
     */
    public function getAllStates(int $serviceProductId, int $qualityTierId, int $propertyTypeId): Builder
    {
        return BasePricingBuilder::query()
            ->forServiceProduct($serviceProductId)
            ->forQualityTier($qualityTierId)
            ->forPropertyType($propertyTypeId)
            ->getQuery();
    }

    /**
     * @param int $serviceProductId
     * @param int $qualityTierId
     * @param int $propertyTypeId
     * @param int $stateLocationId
     * @param bool|null $countiesOnly
     * @return Builder
     */
    public function getAllCountiesByState(int $serviceProductId, int $qualityTierId, int $propertyTypeId, int $stateLocationId, ?bool $countiesOnly = true): Builder
    {
        return BasePricingBuilder::query()
            ->forServiceProduct($serviceProductId)
            ->forQualityTier($qualityTierId)
            ->forPropertyType($propertyTypeId)
            ->forState($stateLocationId)
            ->withCounties($countiesOnly)
            ->aliasPriceColumns()
            ->getQuery();
    }

    /**
     * @param bool $asArray
     * @return ConfigurableFieldDataModel|array|null
     */
    public function getDefaultFloorPricing(bool $asArray = false): ConfigurableFieldDataModel|array|null
    {
        $config = GlobalConfiguration::query()
            ->where(GlobalConfiguration::FIELD_CONFIGURATION_KEY, GlobalConfigurationKey::DEFAULT_PRICING->value)
            ->first()
            ?->{GlobalConfiguration::FIELD_CONFIGURATION_PAYLOAD}
            ?? null;

        return $asArray
            ? $config?->toArray()['data'] ?? []
            : $config;
    }

    /**
     * @param ConfigurableFieldDataModel $payload
     * @param int $userId
     * @return bool
     */
    public function setDefaultFloorPricing(ConfigurableFieldDataModel $payload, int $userId): bool
    {
        $existingConfig = GlobalConfiguration::query()
            ->where(GlobalConfiguration::FIELD_CONFIGURATION_KEY, GlobalConfigurationKey::DEFAULT_PRICING->value)
            ->first();

        return $existingConfig
            ? $existingConfig->update([
                GlobalConfiguration::FIELD_CONFIGURATION_PAYLOAD => $payload,
                GlobalConfiguration::FIELD_UPDATED_BY_ID         => $userId,
            ])
            : !!GlobalConfiguration::query()
                ->create([
                    GlobalConfiguration::FIELD_CONFIGURATION_KEY     => GlobalConfigurationKey::DEFAULT_PRICING->value,
                    GlobalConfiguration::FIELD_CONFIGURATION_PAYLOAD => $payload,
                    GlobalConfiguration::FIELD_CREATED_BY_ID         => $userId,
                ]);
    }

    /**
     * @param string $zipCode
     * @param int $serviceProductId
     * @param int $propertyTypeId
     * @param int $qualityTierId
     * @param int $saleTypeId
     * @return float
     */
    public function getScopedCountyFloorPriceByZipCode(
        string $zipCode,
        int $serviceProductId,
        int $propertyTypeId,
        int $qualityTierId,
        int $saleTypeId,
    ): float
    {
        /** @var LocationRepository $locationRepository */
        $locationRepository = app(LocationRepository::class);
        $countyLocation = $locationRepository->getCountyFromZipcode($zipCode);
        $stateLocationId = $locationRepository->getStateByStateAbbr($countyLocation->state_abbr)->id;
        $countyLocationId = $countyLocation->id;

        $leadFloorPrice = BasePricingBuilder::query()
            ->forState($stateLocationId)
            ->forCounty($countyLocationId)
            ->forPropertyType($propertyTypeId)
            ->forQualityTier($qualityTierId)
            ->forSaleType($saleTypeId)
            ->forServiceProduct($serviceProductId)
            ->select([])
            ->aliasPriceColumns()
            ->getQuery()
            ->first();

        return max(
            $leadFloorPrice?->{BasePricingBuilder::ALIAS_STATE_FLOOR_PRICE},
            $leadFloorPrice?->{BasePricingBuilder::ALIAS_COUNTY_FLOOR_PRICE},
            0,
        );
    }

    /**
     * Multiply floor prices for zip code targeted campaigns
     * Provide array of column names to modify multiple price columns/fields
     *
     * @param Collection $prices
     * @param array|null $priceColumns
     * @return void
     */
    public static function updatePriceCollectionForZipCodeTargeting(Collection &$prices, ?array $priceColumns = null): void
    {
        $multiplier = config('sales.defaults.zip_code_targeting_price_multiplier') ?? 1.2;
        foreach ($prices as &$price) {
            self::multiplyPrice($price, $multiplier, $priceColumns);
        }
    }

    /**
     * @param Model $price
     * @param array|null $priceColumns
     * @return void
     */
    public static function updatePriceForZipCodeTargeting(Model &$price, ?array $priceColumns = null): void
    {
        $multiplier = config('sales.defaults.zip_code_targeting_price_multiplier') ?? 1.2;
        self::multiplyPrice($price, $multiplier, $priceColumns);
    }

    /**
     * @param Model $price
     * @param float $multiplier
     * @param array|null $priceColumns
     * @return void
     */
    private static function multiplyPrice(Model &$price, float $multiplier, ?array &$priceColumns = null): void
    {
        if ($priceColumns)
            foreach($priceColumns as $column) {
                $price->{$column} = round(($price->{$column} ?? 0) * $multiplier);
            }
        else
            $price->price = round(($price->price ?? 0) * $multiplier);
    }

}
