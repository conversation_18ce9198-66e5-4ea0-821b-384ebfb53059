<?php

namespace App\Repositories\Odin\ProductProcessing;

use App\Models\AvailableBudget;
use App\Models\LeadProcessingCallingTimeZoneConfiguration;
use App\Models\LeadProcessingReservedLead;
use App\Models\LeadProcessor;
use App\Models\Legacy\EloquentZipCode;
use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ServiceProduct;
use App\Models\PingPostAffiliates\LeadProcessingAffiliate;
use App\Services\DatabaseHelperService;
use App\Services\Odin\ProductProcessing\ProductProcessingService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;

class ProductProcessingAffiliateQueueRepository
{
    const int MAX_DAYS = 30;
    const int MAX_LEADS = 50;
    const string RESERVED_LEAD_ID = 'reserved_lead_id';
    const array ALL_UTC_OFFSETS = [-10, -9, -8, -7, -6, -5];

    /**
     * @param LeadProcessor $processor
     * @param array|null $utcOffsets
     * @return ConsumerProduct|null
     */
    public function getNextProduct(LeadProcessor $processor, ?array $utcOffsets = null): ?ConsumerProduct
    {
        $query = $this->getQueryForAffiliateQueue(processor: $processor, utcOffsets: $utcOffsets)->limit(self::MAX_LEADS);

        $result = collect(
            DB::select($query->toRawSql())
        )->sortByDesc(fn($queue) => $queue->{LeadProcessingAffiliate::FIELD_PRIORITY} + ($queue->{LeadProcessingAffiliate::FIELD_POSSIBLE_REVENUE} / 100));

        if ($result->isEmpty()) {
            return null;
        }

        $reserved = $result->filter(fn($affiliateLead) => !! $affiliateLead->{self::RESERVED_LEAD_ID});

        if ($reserved->isNotEmpty()) {
            return ConsumerProduct::query()->find($reserved->first()->{LeadProcessingAffiliate::FIELD_CONSUMER_PRODUCT_ID});
        }

        return ConsumerProduct::query()->find($result->first()->{LeadProcessingAffiliate::FIELD_CONSUMER_PRODUCT_ID});
    }

    /**
     * @param LeadProcessor $processor
     * @param int[]|null $industriesOverwrite
     * @param int[]|null $utcOffsets
     *
     * @return Builder
     */
    public function getQueryForAffiliateQueue(LeadProcessor $processor, ?array $industriesOverwrite = null, ?array $utcOffsets = null): Builder
    {
        /** @var ProductProcessingQueueRepository $queueRepository */
        $queueRepository = app(ProductProcessingQueueRepository::class);

        $query = $this->getBaseQuery(
            $processor,
            $industriesOverwrite ?? $processor->team->industries->pluck(Industry::FIELD_ID)->toArray()
        );

        $this->applyQueueTimezoneFilter($query, $utcOffsets ?? [$queueRepository->getTimezoneForProcessor($processor)]);
        $this->applyCallingTimezoneFilter($query);
        $this->orderBy($query);

        return $query;
    }

    /**
     * @param LeadProcessor $processor
     * @param array $industries
     *
     * @return Builder
     */
    protected function getBaseQuery(LeadProcessor $processor, array $industries): Builder
    {
        return $this->addInitialJoins(LeadProcessingAffiliate::query(), $processor)
            ->select([
                LeadProcessingAffiliate::TABLE . '.*',
                DB::raw(LeadProcessingReservedLead::TABLE . '.' . LeadProcessingReservedLead::FIELD_ID . ' AS ' . self::RESERVED_LEAD_ID)]
            )
            ->where(LeadProcessingAffiliate::TABLE . '.' . LeadProcessingAffiliate::CREATED_AT,'>=', now()->subDays(self::MAX_DAYS))
            ->whereNotExists(fn(QueryBuilder $query) => $query->select(DB::raw(1))
                ->from(LeadProcessingReservedLead::TABLE)
                ->whereColumn(
                    LeadProcessingReservedLead::TABLE . '.' . LeadProcessingReservedLead::FIELD_CONSUMER_PRODUCT_ID,
                    LeadProcessingAffiliate::TABLE . '.' . LeadProcessingAffiliate::FIELD_CONSUMER_PRODUCT_ID)
                ->where(LeadProcessingReservedLead::FIELD_PROCESSOR_ID, '!=', $processor->id)
            )
            ->whereIn(IndustryService::TABLE . '.' . IndustryService::FIELD_INDUSTRY_ID, $industries);
    }

    /**
     * @param Builder $query
     * @param LeadProcessor $processor
     *
     * @return Builder
     */
    protected function addInitialJoins(Builder $query, LeadProcessor $processor): Builder
    {
        return $query->join(
            ConsumerProduct::TABLE,
            ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID,
            LeadProcessingAffiliate::TABLE . '.' . LeadProcessingAffiliate::FIELD_CONSUMER_PRODUCT_ID
        )->join(
            Address::TABLE,
            Address::TABLE . '.' . Address::FIELD_ID,
            ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ADDRESS_ID
        )->join(
            Location::TABLE,
            Location::TABLE . '.' . Location::ID,
            Address::TABLE . '.' . Address::FIELD_ZIP_CODE_LOCATION_ID
        )->join(
            DatabaseHelperService::readOnlyDatabase() . '.' . EloquentZipCode::TABLE,
            fn(JoinClause $joinClause) => $joinClause->on(
                DatabaseHelperService::readOnlyDatabase() . '.' . EloquentZipCode::TABLE . '.' . EloquentZipCode::FIELD_ZIP_CODE,
                Address::TABLE . '.' . Address::FIELD_ZIP_CODE
            )->where(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentZipCode::TABLE . '.' . EloquentZipCode::FIELD_ZIP_TYPE, EloquentZipCode::ZIP_TYPE_STANDARD)
            ->where(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentZipCode::TABLE . '.' . EloquentZipCode::FIELD_CITY_TYPE, EloquentZipCode::CITY_TYPE_PRIMARY)
        )->join(
            ServiceProduct::TABLE,
            ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_ID,
            ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_SERVICE_PRODUCT_ID
        )->join(
            IndustryService::TABLE,
            IndustryService::TABLE . '.' . IndustryService::FIELD_ID,
            ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_INDUSTRY_SERVICE_ID
        )->leftJoin(LeadProcessingReservedLead::TABLE, fn(JoinClause $joinClause) => $joinClause->on(
            LeadProcessingReservedLead::TABLE . '.' . LeadProcessingReservedLead::FIELD_CONSUMER_PRODUCT_ID,
            LeadProcessingAffiliate::TABLE . '.' . LeadProcessingAffiliate::FIELD_CONSUMER_PRODUCT_ID)
            ->where(LeadProcessingReservedLead::TABLE . '.' . LeadProcessingReservedLead::FIELD_PROCESSOR_ID, $processor->id)
        );
    }

    /**
     * @param Builder $query
     *
     * @return void
     */
    protected function applyBudgetFilter(Builder $query): void
    {
        $query->whereExists(fn(QueryBuilder $query) => $query->select(DB::raw(1))
            ->from(AvailableBudget::TABLE)
            ->where(fn(QueryBuilder $query) => $query->where(AvailableBudget::TABLE . '.' . AvailableBudget::FIELD_BUDGET_AVAILABLE_DOLLARS, '>', 0)
                ->orWhere(AvailableBudget::TABLE . '.' . AvailableBudget::FIELD_BUDGET_AVAILABLE_VOLUME, '>', 0)
                ->orWhere(AvailableBudget::TABLE . '.' . AvailableBudget::FIELD_UNLIMITED_BUDGET_COUNT, '>', 0))
            ->whereColumn(
                AvailableBudget::TABLE . '.' . AvailableBudget::FIELD_LOCATION_ID,
                Address::TABLE  . '.' . Address::FIELD_ZIP_CODE_LOCATION_ID
            )
            ->whereColumn(AvailableBudget::TABLE . '.' . AvailableBudget::FIELD_INDUSTRY_ID, IndustryService::TABLE . '.' . IndustryService::FIELD_INDUSTRY_ID)
        );
    }

    /**
     * @param Builder $query
     * @param int[] $utc
     *
     * @return void
     */
    protected function applyQueueTimezoneFilter(Builder $query, array $utc): void
    {
        $query->whereIn(Address::TABLE . '.' . Address::FIELD_UTC, $utc);
    }

    /**
     * @param Builder $query
     *
     * @return void
     */
    protected function applyCallingTimezoneFilter(Builder $query): void
    {
        $defaultUtc           = ProductProcessingService::FALLBACK_UTC_OFFSET;
        $defaultTimezone      = LeadProcessingCallingTimeZoneConfiguration::query()->where(LeadProcessingCallingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET, $defaultUtc)->first();
        $defaultTimezoneOpen  = $defaultTimezone[LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR];
        $defaultTimezoneClose = $defaultTimezone[LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR];
        $timezoneOpenCol      = LeadProcessingCallingTimeZoneConfiguration::TABLE . '.' . LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR;
        $timezoneCloseCol     = LeadProcessingCallingTimeZoneConfiguration::TABLE . '.' . LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR;
        $zipcodeUtcCol        = DatabaseHelperService::readOnlyDatabase() . '.' . EloquentZipCode::TABLE . '.' . EloquentZipCode::FIELD_UTC;
        $zipcodeDstCol        = DatabaseHelperService::readOnlyDatabase() . '.' . EloquentZipCode::TABLE . '.' . EloquentZipCode::FIELD_DST;

        $query->leftJoin(LeadProcessingCallingTimeZoneConfiguration::TABLE, fn(JoinClause $joinClause) => $joinClause->on(
            Address::TABLE . '.' . Address::FIELD_UTC,
            LeadProcessingCallingTimeZoneConfiguration::TABLE . '.' . LeadProcessingCallingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET)
        )->whereRaw(
            "TIME_FORMAT(CONVERT_TZ(NOW(), '+00:00', CONCAT(IFNULL($zipcodeUtcCol, $defaultUtc) + IF($zipcodeDstCol = 'Y', 1, 0), ':00')), '%k') >= IFNULL($timezoneOpenCol, $defaultTimezoneOpen)"
        )->whereRaw(
            "TIME_FORMAT(CONVERT_TZ(NOW(), '+00:00', CONCAT(IFNULL($zipcodeUtcCol, $defaultUtc) + IF($zipcodeDstCol = 'Y', 1, 0), ':00')), '%k') < IFNULL($timezoneCloseCol, $defaultTimezoneClose)"
        );
    }

    /**
     * @param Builder $query
     *
     * @return void
     */
    protected function orderBy(Builder $query): void
    {
        $query->orderByDesc(LeadProcessingAffiliate::TABLE . '.' . LeadProcessingAffiliate::FIELD_PRIORITY)
            ->orderByDesc(LeadProcessingAffiliate::TABLE . '.' . LeadProcessingAffiliate::FIELD_POSSIBLE_REVENUE)
            ->orderByDesc(LeadProcessingAffiliate::TABLE . '.' . LeadProcessingAffiliate::CREATED_AT);
    }
}
