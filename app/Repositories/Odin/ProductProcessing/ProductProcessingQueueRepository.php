<?php

namespace App\Repositories\Odin\ProductProcessing;

use App\Enums\Odin\Industry as IndustryEnum;
use App\Events\ConsumerProcessing\ConsumerProductCancelledEvent;
use App\Events\ConsumerProcessing\ConsumerProductMarkedAsPendingReviewEvent;
use App\Events\ConsumerProcessing\ConsumerProductMarkedAsUnderReviewEvent;
use App\Jobs\RecordMonitoringLog;
use App\Models\LeadProcessingAllocation;
use App\Models\LeadProcessingBudgetStatus;
use App\Models\LeadProcessingConfiguration;
use App\Models\LeadProcessingHistory;
use App\Models\LeadProcessingInitial;
use App\Models\LeadProcessingPendingReview;
use App\Models\LeadProcessingQueueConfiguration;
use App\Models\LeadProcessingQueueConstraintsBucketFlags;
use App\Models\LeadProcessingReservedLead;
use App\Models\LeadProcessingTeam;
use App\Models\LeadProcessingUnderReview;
use App\Models\LeadProcessor;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Repositories\Odin\ServiceProductRepository;
use App\Services\ConsumerProductLifecycleTrackingService;
use App\Services\DatabaseHelperService;
use App\Services\LeadProcessing\LeadCommunicationService;
use App\Services\Odin\ProductProcessing\ProductProcessingService;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ProductProcessingQueueRepository
{
    const string STATUS_INITIAL        = 'initial';
    const string STATUS_PENDING_REVIEW = 'pending_review';
    const string STATUS_UNDER_REVIEW   = 'under_review';
    const string STATUS_ALLOCATED      = 'allocated';
    const string STATUS_NO_COMPANIES   = 'no_companies';
    const string STATUS_CANCELLED      = 'cancelled';

    const array STATUS_MAPPINGS = [
        self::STATUS_INITIAL        => ConsumerProduct::STATUS_INITIAL,
        self::STATUS_PENDING_REVIEW => ConsumerProduct::STATUS_PENDING_REVIEW,
        self::STATUS_UNDER_REVIEW   => ConsumerProduct::STATUS_UNDER_REVIEW,
        self::STATUS_ALLOCATED      => ConsumerProduct::STATUS_ALLOCATED,
        self::STATUS_NO_COMPANIES   => ConsumerProduct::STATUS_UNSOLD,
        self::STATUS_CANCELLED      => ConsumerProduct::STATUS_CANCELLED
    ];

    /**
     * When searching for a lead of a particular status, how many days back to look
     *
     * @var array
     */
    const array MAX_DAYS_DISPLAY = [
        self::STATUS_INITIAL        => 1,
        self::STATUS_PENDING_REVIEW => 2,
        self::STATUS_UNDER_REVIEW   => 30,
        self::STATUS_ALLOCATED      => 1,
        self::STATUS_NO_COMPANIES   => 1,
        self::STATUS_CANCELLED      => 1
    ];

    /**
     * If the amount of leads for a given queue is below this buffer,
     * then the queue will take into account the next largest
     * queue to assist with clearing old leads.
     */
    const int HELP_OTHER_TIMEZONE_LEAD_COUNT_BUFFER = 100;


    /**
     * @param ProductProcessingQueueConstraintsRepository $productProcessingQueueConstraintsRepository
     * @param ProductProcessingRepository $productProcessingRepository
     * @param Dispatcher $dispatcher
     * @param ServiceProductRepository $serviceProductRepository
     * @param ProductProcessingAgedQueueRepository $agedQueueRepository
     * @param ProductProcessingAffiliateQueueRepository $affiliateQueueRepository
     */
    public function __construct(
        protected ProductProcessingQueueConstraintsRepository $productProcessingQueueConstraintsRepository,
        protected ProductProcessingRepository                 $productProcessingRepository,
        protected Dispatcher                                  $dispatcher,
        protected ServiceProductRepository                    $serviceProductRepository,
        protected ProductProcessingAgedQueueRepository        $agedQueueRepository,
        protected ProductProcessingAffiliateQueueRepository   $affiliateQueueRepository,
        protected ConsumerProductRepository                   $consumerProductRepository,
    ){}

    /**
     * Handles retrieving the next lead in the queue.
     *
     * @param LeadProcessor $processor
     * @param int|null $previousProductId
     *
     * @return ConsumerProduct|Model
     * @throws Exception
     */
    public function getNextProduct(LeadProcessor $processor, ?int $previousProductId = null): ?ConsumerProduct
    {
        if ($processor->lead_processing_team_id === LeadProcessingTeam::NO_TEAM_ASSIGNED_ID) {
            return null;
        }

        $queue            = $this->getQueueForProcessor($processor);
        /** @var LeadProcessingTeam $team */
        $team             = $processor->{LeadProcessor::RELATION_LEAD_PROCESSING_TEAM};

        $leadProcessorTimezone = $this->getTimezoneForProcessor($processor);

        if ($queue->primary_status === LeadProcessingQueueConfiguration::STATUS_AGED) {
            return $this->agedQueueRepository->getNextProduct($processor);
        }
        else if ($queue->primary_status === LeadProcessingQueueConfiguration::STATUS_AGED_NO_LIMIT_ONLY) {
            return $this->agedQueueRepository->getNextProduct($processor, true);
        }
        else if ($queue->primary_status === LeadProcessingQueueConfiguration::STATUS_AFFILIATE) {
            $product = $this->affiliateQueueRepository->getNextProduct($processor);

            if (!$product) {
                $product = $this->affiliateQueueRepository->getNextProduct($processor, ProductProcessingAffiliateQueueRepository::ALL_UTC_OFFSETS);
            }

            return $product;
        }
        else if($queue->primary_status === LeadProcessingQueueConfiguration::STATUS_UNDER_REVIEW) {
            $queryConfig = 1;

            $nextLeadQuery = $this->getQueryForQueue(
                $queue,
                false,
                [$leadProcessorTimezone],
                0,
                $team->industries,
                false,
                true,
                $previousProductId
            );

            $nextLeadQuery->selectRaw(ConsumerProduct::TABLE . '.*');

            // For troubleshooting queues
            $this->logQuery($queryConfig, $nextLeadQuery, $team, $processor);

            return $this->getResultOfNextLeadQuery($nextLeadQuery, $queue);
        }
        else if(in_array($queue->primary_status, [LeadProcessingQueueConfiguration::STATUS_INITIAL, LeadProcessingQueueConfiguration::STATUS_PENDING_REVIEW])) {
            $priorityTimezone = $this->getPriorityTimezone($queue, $leadProcessorTimezone, $processor->team->industries, $previousProductId);
            $timezones = $this->getTimezonesForQueue($queue, $processor, $leadProcessorTimezone, $priorityTimezone, $previousProductId);

            $nextLeadQuery = $this->getQueryForQueue(
                $queue,
                true,
                $timezones,
                0,
                $team->industries,
                false,
                true,
                $previousProductId
            );

            $queryConfig = 2;

            if($this->getCountForQueueQuery($nextLeadQuery) === 0) {
                $nextLeadQuery = $this->getQueryForQueue(
                    $queue,
                    $priorityTimezone !== null,
                    [$priorityTimezone ?? -5],
                    0,
                    $team->industries,
                    false,
                    true,
                    $previousProductId
                );

                $queryConfig = 3;
            }

            $nextLeadQuery->selectRaw(ConsumerProduct::TABLE . '.*');

            // For troubleshooting queues
            $this->logQuery($queryConfig, $nextLeadQuery, $team, $processor);

            return $this->getResultOfNextLeadQuery($nextLeadQuery, $queue);
        }

        throw new Exception("Missing queue status");
    }

    /**
     * @param $queryConfig
     * @param $nextLeadQuery
     * @param $team
     * @param $processor
     * @return void
     */
    private function logQuery($queryConfig, $nextLeadQuery, $team, $processor): void
    {
        if(App::environment('production')
            && LeadProcessingConfiguration::query()->first()->{LeadProcessingConfiguration::FIELD_NEXT_LEAD_LOGGING_TEAM_ID} === $team->{LeadProcessingTeam::FIELD_ID}) {
            $teamName = $team->{LeadProcessingTeam::FIELD_NAME};
            $this->logStatus(
                "{$teamName} next product query",
                [
                    'query'        => base64_encode(gzcompress(Str::replaceArray('?', $nextLeadQuery->getBindings(), $nextLeadQuery->toSql()), 9)),
                    'config'       => $queryConfig,
                    'team'         => $teamName,
                    'processor_id' => $processor->{LeadProcessor::FIELD_ID}
                ]
            );
        }
    }

    /**
     * @param Builder $query
     *
     * @return void
     */
    protected function excludeAssignedProducts(Builder $query): void
    {
       $query->whereDoesntHave(ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT, function(Builder $query){
           $query->where(ProductAssignment::FIELD_OFF_HOUR_SALE, false);
       });
    }

    /**
     * @param Builder $query
     * @param LeadProcessingQueueConfiguration $queue
     *
     * @return Builder|Model|mixed|object|null
     */
    protected function getResultOfNextLeadQuery(Builder $query, LeadProcessingQueueConfiguration $queue): mixed
    {
        return ($queue->primary_status === LeadProcessingQueueConfiguration::STATUS_UNDER_REVIEW && $query->count() <= 50)
            ? $query->get()->last()
            : $query->first();
    }

    /**
     * @param LeadProcessingQueueConfiguration $queue
     * @param LeadProcessor $processor
     * @param int $leadProcessorTimezone
     * @param int|null $priorityTimezone
     * @param int|null $excludeProductId
     *
     * @return array
     * @throws Exception
     */
    protected function getTimezonesForQueue(LeadProcessingQueueConfiguration $queue, LeadProcessor $processor, int $leadProcessorTimezone, ?int $priorityTimezone, ?int $excludeProductId = null): array
    {
        if ($priorityTimezone !== null
            && $this->getCountForQueueQuery(
                $this->getQueryForQueue(
                    $queue,
                    true,
                    [$leadProcessorTimezone],
                    0,
                    $processor->team->industries,
                    false,
                    true,
                    $excludeProductId
                )
            ) < self::HELP_OTHER_TIMEZONE_LEAD_COUNT_BUFFER
        ) {
            return [$leadProcessorTimezone, $priorityTimezone];
        }

        return [$leadProcessorTimezone];
    }

    /**
     * Gets the timezone for a given lead processor's team.
     *
     * @param LeadProcessor $processor
     *
     * @return int
     */
    public function getTimezoneForProcessor(LeadProcessor $processor): int
    {
        return $this->isActiveDST() ? $processor->team->primary_utc_offset + 1 : $processor->team->primary_utc_offset;
    }

    /**
     * @return bool
     */
    public function isActiveDST(): bool
    {
        $currentTimezone    = date_default_timezone_get();
        $timezoneToCheckDST = 'America/Los_Angeles';

        date_default_timezone_set($timezoneToCheckDST);
        $isActiveDST = date("I");
        date_default_timezone_set($currentTimezone);

        return $isActiveDST;
    }

    /**
     * Handles flipping the last value of the queue.
     *
     * @param LeadProcessingQueueConfiguration $queue
     *
     * @return bool
     */
    public function flipQueueState(LeadProcessingQueueConfiguration $queue): bool
    {
        $queue->last_round = !$queue->last_round;
        $queue->save();

        return true;
    }

    /**
     * Handles returning the queue for a given lead processor.
     *
     * @param LeadProcessor $processor
     *
     * @return LeadProcessingQueueConfiguration
     */
    protected function getQueueForProcessor(LeadProcessor $processor): LeadProcessingQueueConfiguration
    {
        return $processor->team->primaryQueue;
    }

    /**
     * Handles retrieving the base query for getting leads, based on a given queue configuration.
     *
     * @param LeadProcessingQueueConfiguration|Model $queue
     * @param bool $restrictTimezone
     * @param array $utcOffset
     * @param int $lockedBy
     * @param Collection|null $industries
     * @param bool $ignoreLocked
     * @param bool $addConstraints
     * @param int|null $excludeProductId
     * @param bool $ignoreIndustry
     *
     * @return Builder
     * @throws Exception
     */
    public function getQueryForQueue(
        LeadProcessingQueueConfiguration|Model $queue,
        bool                                   $restrictTimezone = true,
        array                                  $utcOffset = [-5],
        int                                    $lockedBy = 0,
        ?Collection                            $industries = null,
        bool                                   $ignoreLocked = false,
        bool                                   $addConstraints = true,
        ?int                                   $excludeProductId = null,
        ?bool                                  $ignoreIndustry = false,
    ): Builder
    {
        $query = ConsumerProduct::query();
        $query = $this->addQueueRestrictionsToQuery($query, $queue->primary_status);

        if (!$ignoreIndustry && $industries) {
            $applicableServiceIds = $industries->flatMap(fn(Industry $industry) => $industry->services()->pluck(IndustryService::FIELD_ID));
            $query->whereHas(ConsumerProduct::RELATION_INDUSTRY_SERVICE, fn(Builder $subQuery) =>
                $subQuery->whereIn(IndustryService::TABLE.'.'.IndustryService::FIELD_ID, $applicableServiceIds)
            );
        }

        if ($addConstraints) {
            $query = $this->productProcessingQueueConstraintsRepository->addQueueConstraints(
                $query,
                $queue->{LeadProcessingQueueConfiguration::FIELD_ID},
                $industries ?? collect(),
                (bool)$queue->last_round
            );
        }

        if (!$ignoreLocked) {
            if ($lockedBy === 0) {
                $query->whereNotExists(function ($query) {
                    $query->select(DB::raw(1))->from(LeadProcessingReservedLead::TABLE)
                        ->whereColumn(
                            LeadProcessingReservedLead::TABLE . '.' . LeadProcessingReservedLead::FIELD_CONSUMER_PRODUCT_ID,
                            ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID
                        );
                });
            } else {
                $query->whereExists(function ($query) use ($lockedBy) {
                    $query->select(DB::raw(1))->from(LeadProcessingReservedLead::TABLE)
                        ->whereColumn(
                            LeadProcessingReservedLead::TABLE . '.' . LeadProcessingReservedLead::FIELD_CONSUMER_PRODUCT_ID,
                            ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID
                        )->where(
                            LeadProcessingReservedLead::TABLE . '.' . LeadProcessingReservedLead::FIELD_PROCESSOR_ID,
                            $lockedBy
                        );
                });
            }
        }

        if ($restrictTimezone && count($utcOffset) > 0) {
            if (!DatabaseHelperService::joinExists($query->getQuery(), Address::TABLE)) {
                $query->join(Address::TABLE, Address::TABLE . '.' . Address::FIELD_ID, '=', ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ADDRESS_ID);
            }

            $query->whereIn(Address::TABLE . '.' . Address::FIELD_UTC, $utcOffset);
        }

        if ($excludeProductId > 0) {
            $query->where(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID, '!=', $excludeProductId);
        }

        $this->excludeAssignedProducts($query);

        return $query
            ->groupBy(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID);
    }

    /**
     * @param Builder $query
     * @param string $primaryStatus
     *
     * @return Builder
     */
    protected function addQueueRestrictionsToQuery(Builder $query, string $primaryStatus): Builder
    {
        return match ($primaryStatus) {
            self::STATUS_INITIAL => $this->addInitialQueueRestrictions($query),
            self::STATUS_PENDING_REVIEW => $this->addPendingReviewQueueRestrictions($query),
            self::STATUS_UNDER_REVIEW => $this->addUnderReviewQueueRestrictions($query),
            default => $query,
        };
    }

    /**
     * Handles adding restrictions for the initial queue.
     *
     * @param Builder $query
     *
     * @return Builder
     */
    protected function addInitialQueueRestrictions(Builder $query): Builder
    {
        return $query
            ->join(LeadProcessingInitial::TABLE, function ($query) {
                $query->on(LeadProcessingInitial::TABLE . '.' . LeadProcessingInitial::FIELD_CONSUMER_PRODUCT_ID, '=', ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID);
            })
            ->where(function($query){
                $query
                    ->where(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_STATUS, self::STATUS_MAPPINGS[self::STATUS_INITIAL])
                    ->orWhere(function(Builder $query){
                        $query
                            ->where(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_STATUS, self::STATUS_MAPPINGS[self::STATUS_ALLOCATED])
                            ->whereHas(ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT, function(Builder $query){
                                $query->where(ProductAssignment::FIELD_OFF_HOUR_SALE, true);
                            })
                            ->whereDoesntHave(ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT, function(Builder $query){
                                $query->where(ProductAssignment::FIELD_OFF_HOUR_SALE, false);
                            });
                    });
            })

            ->join(Consumer::TABLE, function (JoinClause $join) {
                $join->on(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_CONSUMER_ID, '=', Consumer::TABLE . '.' . Consumer::FIELD_ID)
                    ->where(Consumer::TABLE . '.' . Consumer::FIELD_CLASSIFICATION, '<>', Consumer::CLASSIFICATION_EMAIL_ONLY);
            })
            ->whereBetween(ConsumerProduct::TABLE . '.' . ConsumerProduct::CREATED_AT, [
                Carbon::now()->subDays(self::MAX_DAYS_DISPLAY[self::STATUS_INITIAL]),
                Carbon::now()->subSeconds($this->productProcessingRepository->getLeadProcessingConfiguration()[LeadProcessingConfiguration::FIELD_LEAD_PROCESSABLE_DELAY_SECONDS] ?? LeadProcessingConfiguration::LEAD_PROCESSABLE_DELAY_DEFAULT)
            ]);
    }

    /**
     * Handles adding restrictions for the under review queue.
     *
     * @param Builder $query
     *
     * @return Builder
     */
    protected function addUnderReviewQueueRestrictions(Builder $query): Builder
    {
        return $query
            ->join(
                LeadProcessingUnderReview::TABLE,
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID,
                '=',
                LeadProcessingUnderReview::TABLE . '.' . LeadProcessingUnderReview::FIELD_CONSUMER_PRODUCT_ID
            )
            ->where(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_STATUS, self::STATUS_MAPPINGS[self::STATUS_UNDER_REVIEW])
            ->where(ConsumerProduct::TABLE . '.' . ConsumerProduct::CREATED_AT, '>=', Carbon::now()->subDays(self::MAX_DAYS_DISPLAY[self::STATUS_UNDER_REVIEW]));
    }

    /**
     * Adds the pending review queue restrictions needed.
     *
     * @param Builder $query
     *
     * @return Builder
     */
    protected function addPendingReviewQueueRestrictions(Builder $query): Builder
    {
        return $query
            ->join(LeadProcessingPendingReview::TABLE, function ($query) {
                $query->on(LeadProcessingPendingReview::TABLE . '.' . LeadProcessingPendingReview::FIELD_CONSUMER_PRODUCT_ID, '=', ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID);
            })
            ->join(Consumer::TABLE, function (JoinClause $join) {
                $join->on(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_CONSUMER_ID, '=', Consumer::TABLE . '.' . Consumer::FIELD_ID)
                    ->where(Consumer::TABLE . '.' . Consumer::FIELD_CLASSIFICATION, '<>', Consumer::CLASSIFICATION_EMAIL_ONLY);
            })
            ->where(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_STATUS, self::STATUS_MAPPINGS[self::STATUS_PENDING_REVIEW])
            ->where(ConsumerProduct::TABLE . '.' . ConsumerProduct::CREATED_AT, '>=', Carbon::now()->subDays(self::MAX_DAYS_DISPLAY[self::STATUS_PENDING_REVIEW]));
    }

    /**
     * Returns the count for a given query.
     *
     * @param Builder|\Illuminate\Database\Query\Builder $query
     *
     * @return int
     */
    protected function getCountForQueueQuery(Builder|\Illuminate\Database\Query\Builder $query): int
    {
        return $query->selectRaw('COUNT(*) as count')->get()->count();
    }

    /**
     * Verifies if a processor has a locked lead or not.
     *
     * @param LeadProcessingQueueConfiguration $queue
     * @param LeadProcessingTeam $team
     * @param LeadProcessor $processor
     * @param int|null $excludeProductId
     *
     * @return bool
     * @throws Exception
     */
    protected function hasLockedLead(
        LeadProcessingQueueConfiguration $queue,
        LeadProcessingTeam               $team,
        LeadProcessor                    $processor,
        ?int                             $excludeProductId
    ): bool
    {
        return $this->getCountForQueueQuery(
                $this->getQueryForQueue(
                    $queue,
                    false,
                    [$this->getTimezoneForProcessor($processor)],
                    $processor->id,
                    $team->industries,
                    false,
                    true,
                    $excludeProductId
                )
            ) > 0;
    }

    /**
     * Handles retrieving the timezone with the highest priority.
     *
     * @param LeadProcessingQueueConfiguration $queue
     * @param int|null $ignoreTimezone
     * @param Collection|null $industries
     * @param int|null $excludeProductId
     *
     * @return int|null
     * @throws Exception
     */
    protected function getPriorityTimezone(
        LeadProcessingQueueConfiguration $queue,
        ?int                             $ignoreTimezone = null,
        ?Collection                      $industries = null,
        ?int                             $excludeProductId = null
    ): ?int
    {
        $query = $this->getQueryForQueue($queue, false, [], 0, $industries, false, true, $excludeProductId);

        if (!DatabaseHelperService::joinExists($query->getQuery(), Address::TABLE)) {
            $query->join(Address::TABLE, Address::TABLE . '.' . Address::FIELD_ID, '=', ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ADDRESS_ID);
        }

        $query->selectRaw('COUNT(*) as `count`, ' . Address::TABLE . '.' . Address::FIELD_UTC)
            ->groupBy(Address::TABLE . '.' . Address::FIELD_UTC);

        if ($ignoreTimezone !== null) {
            $query->where(Address::TABLE . '.' . Address::FIELD_UTC, '<>', $ignoreTimezone);
        }

        $result = $query->orderBy('count', 'DESC')->first();

        return $result?->utc;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param LeadProcessor $processor
     * @param string $newProcessingStatus
     * @param string $reason
     * @param string|null $comment
     * @param string|null $publicComment
     * @param bool|null $automatic
     * @return bool
     * @throws Exception
     */
    public function changeQueues(
        ConsumerProduct $consumerProduct,
        LeadProcessor   $processor,
        string          $newProcessingStatus,
        string          $reason,
        ?string         $comment = null,
        ?string         $publicComment = null,
        ?bool           $automatic = null
    ): bool
    {
        ConsumerProductLifecycleTrackingService::queueUpdated($consumerProduct, $processor, $newProcessingStatus, $reason, $comment, $automatic);

        switch ($newProcessingStatus) {
            case self::STATUS_PENDING_REVIEW:
                $this->handleChangeToPendingReviewStatus($consumerProduct, $processor, $reason, $comment, $publicComment);
                break;
            case self::STATUS_UNDER_REVIEW:
                $this->handleChangeToUnderReviewStatus($consumerProduct, $processor, $reason, $comment, $publicComment, $automatic);
                break;
            case self::STATUS_CANCELLED:
                $this->handleChangeToCancelledStatus($consumerProduct, $processor, $reason, $comment);
                break;
            case self::STATUS_ALLOCATED:
                $this->handleChangeToAllocatedStatus($consumerProduct, $processor, $reason);
                break;
            default:
                throw new Exception("Invalid product processing status");
        }

        return true;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param LeadProcessor $processor
     * @param string $reason
     * @param string|null $comment
     * @param bool|null $publicComment
     * @return void
     *
     * @throws Exception
     */
    protected function handleChangeToAllocatedStatus(
        ConsumerProduct $consumerProduct,
        LeadProcessor   $processor,
        string          $reason,
    ): void
    {
        $this->productProcessingRepository->removeInitial($consumerProduct);
        $this->productProcessingRepository->removePendingReview($consumerProduct);
        $this->productProcessingRepository->removeUnderReview($consumerProduct);
        $this->productProcessingRepository->removeAged($consumerProduct);
        $this->productProcessingRepository->removeAffiliate($consumerProduct);

        $this->productProcessingQueueConstraintsRepository->deleteBucketFlags($consumerProduct->{ConsumerProduct::FIELD_ID});

        $this->productProcessingRepository->updateProductStatusAndReason($consumerProduct, ConsumerProduct::STATUS_PENDING_ALLOCATION, $reason);

        $this->productProcessingRepository->recordProcessorHistory(
            $consumerProduct,
            $processor,
            LeadProcessingHistory::ACTION_ALLOCATED,
        );
    }

    /**
     * @param int $consumerProductId
     * @param LeadProcessor $processor
     * @param string $reason
     * @param Carbon $deliverAt
     * @return LeadProcessingAllocation
     */
    public function addProductToAllocationQueue(
        int           $consumerProductId,
        LeadProcessor $processor,
        string        $reason,
        Carbon        $deliverAt,
    ): LeadProcessingAllocation
    {
        /** @var LeadProcessingAllocation $processingAllocation */
        $processingAllocation = LeadProcessingAllocation::query()->updateOrCreate(
            [
                LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID    => $consumerProductId,
            ],
            [
                LeadProcessingAllocation::FIELD_LEAD_PROCESSOR_ID      => $processor->{LeadProcessor::FIELD_ID},
                LeadProcessingAllocation::FIELD_PROCESSING_SCENARIO    => $reason,
                LeadProcessingAllocation::FIELD_DELIVER_AT             => $deliverAt,
                LeadProcessingAllocation::FIELD_DELIVERED              => LeadProcessingAllocation::NOT_DELIVERED,
                LeadProcessingAllocation::FIELD_QUEUE_CONFIGURATION_ID => $processor->{LeadProcessor::RELATION_LEAD_PROCESSING_TEAM}->{LeadProcessingTeam::FIELD_PRIMARY_QUEUE_CONFIGURATION_ID},
                LeadProcessingAllocation::FIELD_LEAD_ID                => ProductProcessingService::getLegacyId($consumerProductId),
            ]
        );

        return $processingAllocation;
    }

    /**
     * @param int $ageInHoursLowerLimit
     * @param int|null $ageInHoursUpperLimit
     *
     * @return EloquentCollection
     */
    public function getUnderReviewConsumerProductsByAge(int $ageInHoursLowerLimit, ?int $ageInHoursUpperLimit = null): EloquentCollection
    {
        return LeadProcessingUnderReview::query()
            ->select(LeadProcessingUnderReview::TABLE . '.*')
            ->join(
                ConsumerProduct::TABLE,
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID,
                LeadProcessingUnderReview::TABLE . '.' . LeadProcessingUnderReview::FIELD_CONSUMER_PRODUCT_ID
            )
            ->where(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_STATUS, ConsumerProduct::STATUS_UNDER_REVIEW)
            ->where(LeadProcessingUnderReview::TABLE . '.' . LeadProcessingUnderReview::CREATED_AT, '<=', now()->subHours($ageInHoursLowerLimit))
            ->when(
                $ageInHoursUpperLimit !== null,
                fn(Builder $query) => $query->where(LeadProcessingUnderReview::TABLE . '.' . LeadProcessingUnderReview::CREATED_AT, '>', now()->subHours($ageInHoursUpperLimit))
            )
            ->get();
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param LeadProcessor $processor
     * @param string $reason
     * @param string|null $comment
     * @param bool|null $publicComment
     * @return void
     * @throws BindingResolutionException
     * @throws Exception
     */
    protected function handleChangeToPendingReviewStatus(
        ConsumerProduct $consumerProduct,
        LeadProcessor   $processor,
        string          $reason,
        ?string         $comment = null,
        ?bool           $publicComment = null,
    ): void
    {
        $this->productProcessingRepository->removeInitial($consumerProduct);
        $this->productProcessingRepository->removePendingReview($consumerProduct);
        $this->productProcessingRepository->removeUnderReview($consumerProduct);

        $legacyId = $consumerProduct->consumer->legacy_id;
        $this->productProcessingQueueConstraintsRepository->saveProductProcessingQueueConstraintsBucketFlags($consumerProduct->id, $legacyId);
        $flags = $this->productProcessingQueueConstraintsRepository->getFlagsByProductId($consumerProduct->id);

        // Unsure if we still need/want the logic in ->shouldBeMarkedPending, but copied it from old processing
        if ($this->shouldBeMarkedPending($consumerProduct, $flags)) {
            $this->productProcessingRepository->markLeadAsPendingReview($consumerProduct, $processor, $reason);
            $this->consumerProductRepository->updateConsumerProductStatus($consumerProduct, ConsumerProduct::STATUS_PENDING_REVIEW);
            $this->productProcessingRepository->recordProcessorHistory($consumerProduct, $processor, LeadProcessingHistory::ACTION_PENDING_REVIEW);

            event(new ConsumerProductMarkedAsPendingReviewEvent(
                consumerProduct: $consumerProduct,
                processor: $processor,
                reason: $reason,
                comment: $comment,
                publicComment: $publicComment,
            ));
        }
        else {
            $this->changeQueues($consumerProduct, $processor, self::STATUS_UNDER_REVIEW, $reason, $comment, $publicComment);
        }
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param LeadProcessor $processor
     * @param string $reason
     * @param string|null $comment
     * @param bool|null $publicComment
     * @param bool|null $automatic
     * @return void
     * @throws BindingResolutionException
     */
    protected function handleChangeToUnderReviewStatus(
        ConsumerProduct $consumerProduct,
        LeadProcessor   $processor,
        string          $reason,
        ?string         $comment = null,
        ?bool           $publicComment = false,
        ?bool           $automatic = null
    ): void
    {
        $priorQueue = $this->getProductQueue($consumerProduct);

        $previousReason = $consumerProduct->pendingReview?->reason ?? $consumerProduct->underReview?->existing_reason ?? null;
        $this->productProcessingRepository->removeInitial($consumerProduct);
        $this->productProcessingRepository->removePendingReview($consumerProduct);
        $elapsed = (int) now()->diffInHours($consumerProduct->created_at, true);
        if ($elapsed >= 48 && stripos($reason, 'requalify') === false) {
            $reason .= ", Requalify";
        }

        $this->productProcessingQueueConstraintsRepository->saveProductProcessingQueueConstraintsBucketFlags($consumerProduct->id);
        $this->consumerProductRepository->updateConsumerProductStatus($consumerProduct, ConsumerProduct::STATUS_UNDER_REVIEW);
        $this->productProcessingRepository->markLeadAsUnderReview($consumerProduct, $processor, $reason, $previousReason);

        event(new ConsumerProductMarkedAsUnderReviewEvent(
            consumerProduct: $consumerProduct,
            processor: $processor,
            reason: $reason,
            comment: $comment,
            publicComment: $publicComment
        ));

        $historyId = $this->productProcessingRepository->recordProcessorHistory(
            $consumerProduct,
            $processor,
            $automatic ? LeadProcessingHistory::ACTION_SYSTEM_MOVED_PR_UR : LeadProcessingHistory::ACTION_UNDER_REVIEW
        );

        if ($priorQueue !== self::STATUS_INITIAL && !$automatic) {
            app()->make(LeadCommunicationService::class)->recordContactAttempt($consumerProduct->id, $historyId, $processor->id);
        }
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param LeadProcessor $processor
     * @param string $reason
     * @param string|null $comment
     * @param bool|null $publicComment
     * @return void
     */
    protected function handleChangeToCancelledStatus(
        ConsumerProduct $consumerProduct,
        LeadProcessor   $processor,
        string          $reason,
        ?string         $comment = null,
        ?bool           $publicComment = null,
    ): void
    {
        $this->productProcessingRepository->removeInitial($consumerProduct);
        $this->productProcessingRepository->removePendingReview($consumerProduct);
        $this->productProcessingRepository->removeUnderReview($consumerProduct);
        $this->productProcessingRepository->removeAged($consumerProduct);

        $this->productProcessingQueueConstraintsRepository->deleteBucketFlags($consumerProduct->id);
        $this->consumerProductRepository->updateConsumerProductStatus($consumerProduct, ConsumerProduct::STATUS_CANCELLED);
        $this->productProcessingRepository->recordProcessorHistory($consumerProduct, $processor, LeadProcessingHistory::ACTION_CANCELLED);

        event(new ConsumerProductCancelledEvent(
            consumerProduct: $consumerProduct,
            processor: $processor,
            reason: $reason,
            comment: $comment,
            publicComment: $publicComment,
        ));
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @return string|null
     */
    public function getProductQueue(ConsumerProduct $consumerProduct): ?string
    {
        if (LeadProcessingInitial::query()->where(LeadProcessingInitial::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->id)->exists()) {
            return self::STATUS_INITIAL;
        } else if (LeadProcessingPendingReview::query()->where(LeadProcessingPendingReview::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->id)->exists()) {
            return self::STATUS_PENDING_REVIEW;
        } else if (LeadProcessingUnderReview::query()->where(LeadProcessingUnderReview::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->id)->exists()) {
            return self::STATUS_UNDER_REVIEW;
        } else {
            return null;
        }
    }

    /**
     * @param string $message
     * @param array $payload
     * @return void
     */
    public function logStatus(string $message, array $payload = []): void
    {
        try {
            RecordMonitoringLog::dispatch($message, $payload);
        }
        catch (Exception $e) {
            logger()->debug($e);
        }
    }

    /**
     * TODO: do we need this logic, and is it still correct?
     * @see LeadProcessingQueueRepository::handleChangeToPendingReviewStatus()
     * Copied the logic for now, removed aggregators since the query is legacy and returns 0
     *
     * @param ConsumerProduct $consumerProduct
     * @param LeadProcessingQueueConstraintsBucketFlags $flags
     * @return bool
     */
    private function shouldBeMarkedPending(ConsumerProduct $consumerProduct, LeadProcessingQueueConstraintsBucketFlags $flags): bool
    {
        $industryId = $consumerProduct->industryService->industry_id;
        if (
            !in_array($industryId, [IndustryEnum::SOLAR->model()->id, IndustryEnum::ROOFING->model()->id])
            || $flags->budgetStatus?->key === LeadProcessingBudgetStatus::KEY_WITHIN_BUDGET
        ) {
            return true;
        }

        return false;
    }
}
