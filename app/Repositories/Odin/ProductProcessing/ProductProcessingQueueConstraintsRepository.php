<?php

namespace App\Repositories\Odin\ProductProcessing;

use App\Enums\Odin\Industry;
use App\Models\AvailableBudget;
use App\Models\LeadProcessingBudgetStatus;
use App\Models\LeadProcessingCallingTimeZoneConfiguration;
use App\Models\LeadProcessingCommunication;
use App\Models\LeadProcessingConstraint;
use App\Models\LeadProcessingQueueConstraint;
use App\Models\LeadProcessingQueueConstraintsBucketFlags;
use App\Models\LeadProcessingReservedLead;
use App\Models\LeadProcessingTimeframe;
use App\Models\LeadProcessingUnderReview;
use App\Models\Odin\Address;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ServiceProduct;
use App\Models\TimeframeContactBuffers;
use App\Models\USZipCode;
use App\Repositories\LeadProcessing\LeadProcessingBudgetStatusRepository;
use App\Repositories\LeadProcessing\LeadProcessingTimeframeRepository;
use App\Services\DatabaseHelperService;
use App\Services\Odin\ProductProcessing\ProductProcessingService;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\DatabaseManager;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class ProductProcessingQueueConstraintsRepository
{
    const string STATUS_INITIAL        = 'initial';

    /**
     * @param DatabaseManager $db
     */
    public function __construct(
        protected DatabaseManager $db
    ) {}

    /**
     * @param int $queueId
     * @param string $constraintType
     * @param string $constraintKey
     *
     * @return Collection
     */
    public function getConstraints(
        int    $queueId = 0,
        string $constraintType = '',
        string $constraintKey = ''
    ): Collection
    {
        $searchQuery = LeadProcessingQueueConstraint::query()
            ->join(LeadProcessingConstraint::TABLE, function ($query) {
                $query->on(LeadProcessingConstraint::TABLE . '.' . LeadProcessingConstraint::FIELD_ID, '=', LeadProcessingQueueConstraint::TABLE . '.' . LeadProcessingQueueConstraint::FIELD_CONSTRAINT_ID);
            })
            ->orderBy(LeadProcessingQueueConstraint::FIELD_ORDER, 'ASC');

        if ($queueId > 0) {
            $searchQuery->where(LeadProcessingQueueConstraint::FIELD_QUEUE_ID, '=', $queueId);
        }

        if (!empty($constraintType)) {
            $searchQuery->where(LeadProcessingConstraint::FIELD_TYPE, '=', $constraintType);
        }

        if (!empty($constraintKey)) {
            $searchQuery->where(LeadProcessingConstraint::FIELD_KEY, '=', $constraintKey);
        }

        return $searchQuery->get();
    }

    /**
     * @param Builder $query
     * @param int $queueId
     * @param Collection $industries
     * @param bool $lastRound
     *
     * @return Builder
     * @throws Exception
     */
    public function addQueueConstraints(
        Builder $query,
        int     $queueId,
        Collection $industries,
        bool    $lastRound
    ): Builder
    {
        $query->join(LeadProcessingQueueConstraintsBucketFlags::TABLE, function ($query) {
            $query->on(LeadProcessingQueueConstraintsBucketFlags::TABLE . '.' . LeadProcessingQueueConstraintsBucketFlags::FIELD_CONSUMER_PRODUCT_ID, '=', ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID);
        });

        $constraints = $this->getConstraints($queueId);

        foreach ($constraints as $constraint) {
            if ($constraint->{LeadProcessingConstraint::FIELD_TYPE} === LeadProcessingConstraint::CONSTRAINT_TYPE_FILTER) {
                $query = $this->addFilterConstraint($query, $constraint->{LeadProcessingConstraint::FIELD_KEY}, $industries);
            } else if ($constraint->{LeadProcessingConstraint::FIELD_TYPE} === LeadProcessingConstraint::CONSTRAINT_TYPE_SORT) {
                $query = $this->addSortConstraint($query, $constraint->{LeadProcessingConstraint::FIELD_KEY}, $lastRound);
            } else if ($constraint->{LeadProcessingConstraint::FIELD_TYPE} === LeadProcessingConstraint::CONSTRAINT_TYPE_BUCKET) {
                $query = $this->addBucketConstraint($query, $constraint->{LeadProcessingConstraint::FIELD_KEY});
            }
        }

        return $query;
    }

    /**
     * @param Builder $query
     * @param string $constraintKey
     * @param bool $lastRound
     * @return Builder
     * @throws Exception
     */
    protected function addSortConstraint(
        Builder $query,
        string  $constraintKey,
        bool    $lastRound
    ): Builder
    {
        switch ($constraintKey) {
            case LeadProcessingConstraint::CONSTRAINT_REVENUE:
                $query = $this->addPossibleRevenueSorting($query, $lastRound);
                break;
            case LeadProcessingConstraint::CONSTRAINT_OLDEST_FIRST:
                $query->orderBy(ConsumerProduct::TABLE . '.' . ConsumerProduct::CREATED_AT, 'ASC');
                break;
            case LeadProcessingConstraint::CONSTRAINT_LAST_ROUND:
                $query->orderBy(ConsumerProduct::TABLE . '.' . ConsumerProduct::CREATED_AT, $lastRound ? 'ASC' : 'DESC');
                break;
            default:
                throw new Exception("Invalid sort constraint");
        }

        return $query;
    }

    /**
     * @param Builder $query
     * @param string $constraintKey
     * @param Collection $industries
     * @return Builder
     * @throws Exception
     */
    protected function addFilterConstraint(
        Builder $query,
        string  $constraintKey,
        Collection $industries,
    ): Builder
    {
        switch ($constraintKey) {
            case LeadProcessingConstraint::CONSTRAINT_SUPER_PREMIUM:
                $query->where(LeadProcessingQueueConstraintsBucketFlags::TABLE . '.' . LeadProcessingQueueConstraintsBucketFlags::FIELD_SUPER_PREMIUM, '=', true);
                break;
            case LeadProcessingConstraint::CONSTRAINT_CONTACT_BUFFERS:
                if (!DatabaseHelperService::joinExists($query->getQuery(), LeadProcessingTimeframe::TABLE)) {
                    $query->join(LeadProcessingTimeframe::TABLE, function ($query) {
                        $query->on(LeadProcessingTimeframe::TABLE . '.' . LeadProcessingTimeframe::FIELD_ID, '=', LeadProcessingQueueConstraintsBucketFlags::TABLE . '.' . LeadProcessingQueueConstraintsBucketFlags::FIELD_TIMEFRAME_ID);
                    });
                }

                $query = $this->addContactBuffersFiltering($query);
                break;
            case LeadProcessingConstraint::CONSTRAINT_LOCAL_HOURS:
                $query = $this->addLocalHoursFiltering($query);
                break;
            case LeadProcessingConstraint::CONSTRAINT_AVAILABLE_BUDGET:
                $query = $this->addAvailableBudgetFiltering($query, $industries);
                break;
            case LeadProcessingConstraint::CONSTRAINT_AVAILABLE_NO_LIMIT_BUDGET:
                $query = $this->addAvailableBudgetFiltering($query, $industries);
                $query = $this->addAvailableNoLimitBudgetFiltering($query);
                break;
            default:
                throw new Exception("Invalid filter constraint");
        }

        return $query;
    }

    /**
     * @param Builder $query
     * @param string $constraintKey
     * @return Builder
     * @throws Exception
     */
    protected function addBucketConstraint(
        Builder $query,
        string  $constraintKey
    ): Builder
    {
        switch ($constraintKey) {
            case LeadProcessingConstraint::CONSTRAINT_BUDGET:
                $query
                    ->join(LeadProcessingBudgetStatus::TABLE, function ($query) {
                        $query->on(LeadProcessingQueueConstraintsBucketFlags::TABLE . '.' . LeadProcessingQueueConstraintsBucketFlags::FIELD_BUDGET_STATUS_ID, '=', LeadProcessingBudgetStatus::TABLE . '.' . LeadProcessingBudgetStatus::FIELD_ID);
                    })
                    ->orderBy(LeadProcessingBudgetStatus::TABLE . '.' . LeadProcessingBudgetStatus::FIELD_PRIORITY, 'ASC');
                break;
            case LeadProcessingConstraint::CONSTRAINT_TIMEFRAME:
                if (!DatabaseHelperService::joinExists($query->getQuery(), LeadProcessingTimeframe::TABLE)) {
                    $query->join(LeadProcessingTimeframe::TABLE, function ($query) {
                        $query->on(LeadProcessingTimeframe::TABLE . '.' . LeadProcessingTimeframe::FIELD_ID, '=', LeadProcessingQueueConstraintsBucketFlags::TABLE . '.' . LeadProcessingQueueConstraintsBucketFlags::FIELD_TIMEFRAME_ID);
                    });
                }

                $query->orderBy(LeadProcessingTimeframe::TABLE . '.' . LeadProcessingTimeframe::FIELD_PRIORITY, 'ASC');
                break;
            default:
                throw new Exception("Invalid bucket constraint");
        }

        return $query;
    }

    /**
     * Begin query constraint functions
     */

    /**
     * @param Builder $query
     * @param Collection $industries
     * @return Builder
     */
    private function addAvailableBudgetFiltering(Builder $query, Collection $industries): Builder
    {
        if(!DatabaseHelperService::joinExists($query->getQuery(), ServiceProduct::TABLE)) {
            $query->join(ServiceProduct::TABLE, function($join) {
                $join->on(
                    ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_ID,
                    '=',
                    ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_SERVICE_PRODUCT_ID
                );
            });
        }

        if(!DatabaseHelperService::joinExists($query->getQuery(), IndustryService::TABLE)) {
            $query->join(IndustryService::TABLE, function($join) {
                $join->on(
                    IndustryService::TABLE.'.'.IndustryService::FIELD_ID,
                    '=',
                    ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_INDUSTRY_SERVICE_ID
                );
            });
        }

        return $query
            ->join(AvailableBudget::TABLE, function ($query) {
                $query
                    ->on(
                        AvailableBudget::TABLE.'.'.AvailableBudget::FIELD_LOCATION_ID,
                        '=',
                        LeadProcessingUnderReview::TABLE.'.'.LeadProcessingUnderReview::FIELD_LOCATION_ID
                    )
                    ->on(
                        AvailableBudget::TABLE.'.'.AvailableBudget::FIELD_INDUSTRY_ID,
                        '=',
                        IndustryService::TABLE.'.'.IndustryService::FIELD_INDUSTRY_ID
                    );
            });
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    private function addAvailableNoLimitBudgetFiltering(Builder $query): Builder
    {
        return $query->where(AvailableBudget::TABLE.'.'.AvailableBudget::FIELD_UNLIMITED_BUDGET_COUNT, '>', 0);
    }

    /**
     * Handles restricting the query based on an opening and closing hour for the timezones local time.
     *
     * @param Builder $query
     *
     * @return Builder
     */
    private function addLocalHoursFiltering(Builder $query): Builder
    {
        if (!collect($query->getQuery()->joins)->pluck('table')->contains(Address::TABLE)) {
            $query->join(Address::TABLE, function ($query) {
                $query->on(Address::TABLE . '.' . Address::FIELD_ID, '=', ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ADDRESS_ID);
            });
        }

        $timezoneOpenCol  = LeadProcessingCallingTimeZoneConfiguration::TABLE . '.' . LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR;
        $timezoneCloseCol = LeadProcessingCallingTimeZoneConfiguration::TABLE . '.' . LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR;
        $zipcodeUtcCol    = USZipCode::TABLE . '.' . USZipCode::FIELD_UTC;
        $zipcodeDstCol    = USZipCode::TABLE . '.' . USZipCode::FIELD_DST;

        $defaultUtc           = ProductProcessingService::FALLBACK_UTC_OFFSET;
        $defaultTimezone      = LeadProcessingCallingTimeZoneConfiguration::query()->where(LeadProcessingCallingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET, $defaultUtc)->first();
        $defaultTimezoneOpen  = (int)$defaultTimezone[LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR];
        $defaultTimezoneClose = (int)$defaultTimezone[LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR];

        return $query
            ->leftJoin(USZipCode::TABLE, function ($query) {
                $query
                    ->on(USZipCode::TABLE . '.' . USZipCode::FIELD_ZIP_CODE, '=', Address::TABLE . '.' . Address::FIELD_ZIP_CODE)
                    ->where(USZipCode::TABLE . '.' . USZipCode::FIELD_ZIP_TYPE, '=', USZipCode::DEFAULT_ZIP_TYPE);
            })
            ->leftJoin(LeadProcessingCallingTimeZoneConfiguration::TABLE, function ($query) {
                $query->on(USZipCode::TABLE . '.' . USZipCode::FIELD_UTC, '=', LeadProcessingCallingTimeZoneConfiguration::TABLE . '.' . LeadProcessingCallingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET);
            })
            ->whereRaw(
                "TIME_FORMAT(CONVERT_TZ(NOW(), '+00:00', CONCAT(IFNULL(CAST($zipcodeUtcCol AS SIGNED), $defaultUtc) + IF($zipcodeDstCol = 'Y', 1, 0), ':00')), '%k') >= IFNULL($timezoneOpenCol, $defaultTimezoneOpen)"
            )
            ->whereRaw(
                "TIME_FORMAT(CONVERT_TZ(NOW(), '+00:00', CONCAT(IFNULL(CAST($zipcodeUtcCol AS SIGNED), $defaultUtc) + IF($zipcodeDstCol = 'Y', 1, 0), ':00')), '%k') < IFNULL($timezoneCloseCol, $defaultTimezoneClose)"
            );
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    private function addContactBuffersFiltering(Builder $query): Builder
    {
        return $query
            ->join(TimeframeContactBuffers::TABLE, TimeframeContactBuffers::TABLE . '.' . TimeframeContactBuffers::FIELD_TIMEFRAME_ID,LeadProcessingTimeframe::TABLE . '.' . LeadProcessingTimeframe::FIELD_ID)
            ->leftJoin(LeadProcessingCommunication::TABLE, function ($join) {
                $join->on(LeadProcessingQueueConstraintsBucketFlags::TABLE.'.'.LeadProcessingQueueConstraintsBucketFlags::FIELD_CONSUMER_PRODUCT_ID, LeadProcessingCommunication::TABLE.'.'.LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID)
                    ->where(LeadProcessingCommunication::TABLE.'.'.LeadProcessingCommunication::FIELD_MOST_RECENT, true);
            })->where(function ($query) {
                $communicationCreatedAtCol = LeadProcessingCommunication::TABLE . '.' . LeadProcessingCommunication::CREATED_AT;
                $contactAttemptBufferHrsCol = TimeframeContactBuffers::TABLE . '.' . TimeframeContactBuffers::FIELD_CONTACT_ATTEMPT_BUFFER_HRS;
                $query
                    ->whereRaw("UNIX_TIMESTAMP() - ({$contactAttemptBufferHrsCol} * 60 * 60) >= UNIX_TIMESTAMP({$communicationCreatedAtCol})")
                    ->orWhereNull(LeadProcessingCommunication::TABLE.'.'.LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID);
            });
    }

    /**
     * We want to swap between the first and last lead in the top 50, so rotate between taking 1 and 50
     *
     * @param Builder $query
     * @param bool $lastRound
     * @return Builder
     */
    private function addPossibleRevenueSorting(Builder $query, bool $lastRound): Builder
    {
        return $query->orderByDesc(LeadProcessingUnderReview::TABLE.'.'.LeadProcessingUnderReview::FIELD_POSSIBLE_REVENUE)->take($lastRound ? 50 : 1);
    }

    /**
     * @param int $consumerProductId
     * @return bool
     * @throws BindingResolutionException
     */
    public function saveProductProcessingQueueConstraintsBucketFlags(int $consumerProductId): bool
    {
        /** @var LeadProcessingBudgetStatusRepository $leadProcessingBudgetStatusRepository */
        $leadProcessingBudgetStatusRepository = app()->make(LeadProcessingBudgetStatusRepository::class);
        /** @var LeadProcessingTimeframeRepository $leadProcessingTimeframeRepository */
        $leadProcessingTimeframeRepository = app()->make(LeadProcessingTimeframeRepository::class);

        $flags = LeadProcessingQueueConstraintsBucketFlags::query()->firstOrCreate([LeadProcessingQueueConstraintsBucketFlags::FIELD_CONSUMER_PRODUCT_ID => $consumerProductId]);

        $flags->budget_status_id = $leadProcessingBudgetStatusRepository->determineLeadProcessingBudgetStatus();
        $flags->timeframe_id = $leadProcessingTimeframeRepository->getTimeframeByKey(LeadProcessingTimeframe::KEY_LESS_THAN_TWENTY_FOUR_HRS)?->id;
        $flags->super_premium = false;
        $flags->lead_id = ProductProcessingService::getLegacyId($consumerProductId);

        return $flags->save();
    }

    /**
     * @param int $consumerProductId
     * @return bool
     */
    public function deleteBucketFlags(int $consumerProductId): bool
    {
        LeadProcessingQueueConstraintsBucketFlags::query()
            ->where(LeadProcessingQueueConstraintsBucketFlags::FIELD_CONSUMER_PRODUCT_ID, $consumerProductId)
            ->delete();

        return true;
    }

    /**
     * @param int $consumerProductId
     * @return LeadProcessingQueueConstraintsBucketFlags|null
     */
    public function getFlagsByProductId(int $consumerProductId): ?LeadProcessingQueueConstraintsBucketFlags
    {
        return LeadProcessingQueueConstraintsBucketFlags::query()
            ->where(LeadProcessingQueueConstraintsBucketFlags::FIELD_CONSUMER_PRODUCT_ID, $consumerProductId)
            ->first();
    }

    /**
     * Update the timeframe bucket ID for each lead according to the current time
     *
     * @return bool
     */
    public function updateTimeframeFlags(): bool
    {
        $twentyFourHrs = 60 * 60 * 24;
        $fortyEightHrs = 60 * 60 * 24 * 2;
        $sevenDays = 60 * 60 * 24 * 7;
        $thirtyDays = 60 * 60 * 24 * 30;

        $timeframes = LeadProcessingTimeframe::all()->pluck(LeadProcessingTimeframe::FIELD_ID, LeadProcessingTimeframe::FIELD_KEY);

        $lessThanTwentyFourHrsTimeframe = $timeframes[LeadProcessingTimeframe::KEY_LESS_THAN_TWENTY_FOUR_HRS];
        $twentyFourToFortyEightHrsTimeframe = $timeframes[LeadProcessingTimeframe::KEY_TWENTY_FOUR_TO_FORTY_EIGHT_HRS];
        $twoToSevenDaysTimeframe = $timeframes[LeadProcessingTimeframe::KEY_TWO_TO_SEVEN_DAYS];
        $sevenToThirtyDaysTimeframe = $timeframes[LeadProcessingTimeframe::KEY_SEVEN_TO_THIRTY_DAYS];
        $thirtyToNinetyDaysTimeframe = $timeframes[LeadProcessingTimeframe::KEY_THIRTY_TO_NINETY_DAYS];

        $consumerCreatedAt = ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_CREATED_AT;

        $this->db
            ->table(LeadProcessingQueueConstraintsBucketFlags::TABLE)
            ->join(ConsumerProduct::TABLE, ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ID, LeadProcessingQueueConstraintsBucketFlags::TABLE .'.'. LeadProcessingQueueConstraintsBucketFlags::FIELD_CONSUMER_PRODUCT_ID)
            ->leftJoin(LeadProcessingReservedLead::TABLE, LeadProcessingReservedLead::TABLE .'.'. LeadProcessingReservedLead::FIELD_CONSUMER_PRODUCT_ID, ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ID)
            ->whereNull(LeadProcessingReservedLead::TABLE .'.'. LeadProcessingReservedLead::FIELD_ID)
            ->update([
                LeadProcessingQueueConstraintsBucketFlags::FIELD_TIMEFRAME_ID => $this->db->raw("
                    CASE
                        WHEN
                            (UNIX_TIMESTAMP() - $consumerCreatedAt) <= $twentyFourHrs
                        THEN
                            $lessThanTwentyFourHrsTimeframe
                        WHEN
                            (UNIX_TIMESTAMP() - $consumerCreatedAt) BETWEEN ($twentyFourHrs + 1) AND $fortyEightHrs
                        THEN
                            $twentyFourToFortyEightHrsTimeframe
                        WHEN
                            (UNIX_TIMESTAMP() - $consumerCreatedAt) BETWEEN ($fortyEightHrs + 1) AND $sevenDays
                        THEN
                            $twoToSevenDaysTimeframe
                        WHEN
                            (UNIX_TIMESTAMP() - $consumerCreatedAt) BETWEEN ($sevenDays + 1) AND $thirtyDays
                        THEN
                            $sevenToThirtyDaysTimeframe
                        ELSE
                            $thirtyToNinetyDaysTimeframe
                    END
                ")
            ]);

        return true;
    }
}
