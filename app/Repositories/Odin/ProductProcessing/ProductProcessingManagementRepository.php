<?php

namespace App\Repositories\Odin\ProductProcessing;

use App\Models\LeadProcessingCallingTimeZoneConfiguration;
use App\Models\LeadProcessingConfiguration;
use App\Models\LeadProcessingFailedLead;
use App\Models\LeadProcessingQueueConfiguration;
use App\Models\LeadProcessingTeam;
use App\Models\LeadProcessingTeamIndustry;
use App\Models\LeadProcessingTimeZoneConfiguration;
use App\Models\LeadProcessor;
use App\Models\User;
use App\Services\Odin\ProductProcessing\ProductQueueStatisticsService;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Collection;

class ProductProcessingManagementRepository
{
    public function __construct(
        protected ProductProcessingQueueRepository $queueRepository,
        protected ProductQueueStatisticsService $statisticsService,
    ) {}

    /**
     * Handles retrieving all the lead processing queues.
     *
     * @return Collection
     */
    public function getAllQueues(): Collection
    {
        return LeadProcessingQueueConfiguration::query()
            ->orderBy(LeadProcessingQueueConfiguration::FIELD_NAME)
            ->get();
    }

    /**
     * Handles retrieving a lead processing queue by its id.
     *
     * @param int $id
     * @return LeadProcessingQueueConfiguration|null
     */
    public function getQueue(int $id): ?LeadProcessingQueueConfiguration
    {
        /** @var LeadProcessingQueueConfiguration|null $queue */
        $queue = LeadProcessingQueueConfiguration::query()->find($id);

        return $queue;
    }

    /**
     * Handles creating a queue.
     *
     * @param string $name
     * @param string $status
     * @return bool
     */
    public function createQueue(string $name, string $status): bool
    {
        if (!$name || !$status)
            return false;

        LeadProcessingQueueConfiguration::create([
            LeadProcessingQueueConfiguration::FIELD_NAME           => $name,
            LeadProcessingQueueConfiguration::FIELD_PRIMARY_STATUS => $status,
            LeadProcessingQueueConfiguration::FIELD_LAST_ROUND     => false
        ]);

        return true;
    }

    /**
     * Handles creating a queue.
     *
     * @param int $id
     * @param string $name
     * @param string $status
     * @return bool
     */
    public function updateQueue(int $id, string $name, string $status): bool
    {
        if (!$name || !$status)
            return false;

        LeadProcessingQueueConfiguration::query()->updateOrCreate([
            LeadProcessingQueueConfiguration::FIELD_ID => $id
        ], [
            LeadProcessingQueueConfiguration::FIELD_NAME           => $name,
            LeadProcessingQueueConfiguration::FIELD_PRIMARY_STATUS => $status,
            LeadProcessingQueueConfiguration::FIELD_LAST_ROUND     => false
        ]);

        return true;
    }

    /**
     * Handles deleting a queue.
     *
     * @param int $id
     * @return bool
     */
    public function deleteQueue(int $id): bool
    {
        LeadProcessingQueueConfiguration::query()->where(LeadProcessingQueueConfiguration::FIELD_ID, $id)->delete();

        return true;
    }

    /**
     * Handles retrieving all the lead processing teams.
     *
     * @return Collection
     */
    public function getTeams(): Collection
    {
        return LeadProcessingTeam::query()
            ->orderBy(LeadProcessingTeam::FIELD_NAME)
            ->get();
    }

    /**
     * Handles retrieving a lead processing team by its id.
     *
     * @param int $id
     * @return LeadProcessingTeam|null
     */
    public function getTeam(int $id): ?LeadProcessingTeam
    {
        /** @var LeadProcessingTeam|null $team */
        $team = LeadProcessingTeam::query()->find($id);

        return $team;
    }

    /**
     * Handles creating a team.
     *
     * @param string $name
     * @param int $queueId
     * @param int $offset
     * @param array $industries
     * @return bool
     */
    public function createTeam(string $name, int $queueId, int $offset, array $industries): bool
    {
        if (!$name || !$queueId || !$offset) {
            return false;
        }

        /** @var LeadProcessingTeam $team */
        $team = LeadProcessingTeam::query()->create([
            LeadProcessingTeam::FIELD_NAME                           => $name,
            LeadProcessingTeam::FIELD_PRIMARY_QUEUE_CONFIGURATION_ID => $queueId,
            LeadProcessingTeam::FIELD_PRIMARY_UTC_OFFSET             => $offset
        ]);

        return $this->updateTeamIndustries($team, $industries);
    }

    /**
     * Handles updating a team.
     *
     * @param int $id
     * @param string $name
     * @param int $queueId
     * @param int $offset
     * @param array $industries
     * @return bool
     */
    public function updateTeam(int $id, string $name, int $queueId, int $offset, array $industries): bool
    {
        if (!$name || !$queueId || !$offset || count($industries) === 0) {
            return false;
        }

        LeadProcessingTeam::query()->updateOrCreate([
            LeadProcessingTeam::FIELD_ID => $id
        ], [
            LeadProcessingTeam::FIELD_NAME                           => $name,
            LeadProcessingTeam::FIELD_PRIMARY_QUEUE_CONFIGURATION_ID => $queueId,
            LeadProcessingTeam::FIELD_PRIMARY_UTC_OFFSET             => $offset,
        ]);

        /** @var LeadProcessingTeam $team */
        $team = LeadProcessingTeam::query()->findOrFail($id);

        return $this->updateTeamIndustries($team, $industries);
    }

    /**
     * Handles setting the industries a given team works in.
     *
     * @param LeadProcessingTeam $team
     * @param array $industries
     * @return bool
     */
    protected function updateTeamIndustries(LeadProcessingTeam $team, array $industries): bool
    {
        $team->leadProcessingTeamIndustries()->delete();
        $team->leadProcessingTeamIndustries()->createMany(
            collect($industries)->map(fn($industryId) => [
                LeadProcessingTeamIndustry::FIELD_LEAD_PROCESSING_TEAM_ID => $team->id,
                LeadProcessingTeamIndustry::FIELD_INDUSTRY_ID             => $industryId
            ])->toArray()
        );

        return true;
    }

    /**
     * Handles deleting a given team.
     *
     * @param int $id
     * @return bool
     */
    public function deleteTeam(int $id): bool
    {
        LeadProcessingTeam::query()->where(LeadProcessingTeam::FIELD_ID, $id)->delete();

        return true;
    }

    /**
     * Handles retrieving all the lead processors.
     *
     * @return Collection
     */
    public function getProcessors(): Collection
    {
        return LeadProcessor::query()
            ->with([
                LeadProcessor::RELATION_USER,
                LeadProcessor::RELATION_LEAD_PROCESSING_TEAM,
            ])
            ->get()
            ->sortBy(LeadProcessor::RELATION_USER . '.' . User::FIELD_NAME);
    }

    /**
     * Handles retrieving a processor by their id.
     *
     * @param int $id
     * @return LeadProcessor|null
     */
    public function getProcessor(int $id): ?LeadProcessor
    {
        /** @var LeadProcessor|null $processor */
        $processor = LeadProcessor::query()->find($id);

        return $processor;
    }

    /**
     * Retrieve a processor by their user ID
     *
     * @param int $userId
     * @return LeadProcessor|null
     */
    public function getProcessorByUserId(int $userId): ?LeadProcessor
    {
        return LeadProcessor::where(LeadProcessor::FIELD_USER_ID, '=', $userId)
            ->first();
    }

    /**
     * Handles creating a processor.
     *
     * @param int $userId
     * @param int $teamId
     * @return bool
     */
    public function createProcessor(int $userId, int $teamId): bool
    {
        if (!$userId || !$teamId)
            return false;

        /** @var LeadProcessor $processor */
        LeadProcessor::create([
            LeadProcessor::FIELD_USER_ID                 => $userId,
            LeadProcessor::FIELD_LEAD_PROCESSING_TEAM_ID => $teamId
        ]);

        return true;
    }

    /**
     * Handles creating a processor.
     *
     * @param int $id
     * @param int $userId
     * @param int $teamId
     * @return bool
     */
    public function updateProcessor(int $id, int $userId, int $teamId): bool
    {
        if (!$userId || !$teamId)
            return false;

        LeadProcessor::query()->updateOrCreate([
            LeadProcessor::FIELD_ID => $id
        ], [
            LeadProcessor::FIELD_USER_ID                 => $userId,
            LeadProcessor::FIELD_LEAD_PROCESSING_TEAM_ID => $teamId
        ]);

        return true;
    }

    /**
     * Handles deleting a lead processor.
     *
     * @param int $id
     * @return bool
     */
    public function deleteProcessor(int $id): bool
    {
        LeadProcessor::query()->where(LeadProcessor::FIELD_ID, $id)->delete();

        return true;
    }

    /**
     * @param int $industryId
     * @param int[]|null $utcOffsets
     *
     * @return array
     * @throws Exception
     */
    public function getIndustryStatistics(int $industryId, ?array $utcOffsets = null): array
    {
        $this->statisticsService->setIndustry($industryId);

        return [
            'initial'        => $this->statisticsService->getInitialCount(),
            'pending_review' => $this->statisticsService->getPendingReviewCount(),
            'under_review'   => $this->statisticsService->getUnderReviewCount($utcOffsets, true),
            'allocated'      => $this->statisticsService->getAllocatedCount(),
            'cancelled'      => $this->statisticsService->getCancelledCount(),
            'unsold'         => $this->statisticsService->getUnsoldCount(),
            // Aged queue statistics caused replica database to be pinned. Investigate, resolve, and re-enable
            'aged'           => 0 , // $this->statisticsService->getAgedCount($utcOffsets),
            'affiliate'      => $this->statisticsService->getAffiliateCount($utcOffsets),
        ];
    }

    /**
     * Handles retrieving the last 48 hours of bounced leads.
     *
     * @return Collection
     */
    public function getBouncedLeads(): Collection
    {
        return LeadProcessingFailedLead::query()
            ->select([LeadProcessingFailedLead::FIELD_CONSUMER_PRODUCT_ID, LeadProcessingFailedLead::FIELD_REASON])
            ->where(LeadProcessingFailedLead::UPDATED_AT, '>=', Carbon::now()->subHours(24))
            ->orderBy(LeadProcessingFailedLead::UPDATED_AT, 'DESC')
            ->get();
    }

    /**
     * @return Collection
     */
    public function getAllTimezoneConfigurations(): Collection
    {
        return LeadProcessingTimeZoneConfiguration::all();
    }

    /**
     * @param Collection $timezones
     * @return bool
     */
    public function saveTimezoneConfigurations(Collection $timezones): bool
    {
        foreach($timezones as $timezone) {
            LeadProcessingTimeZoneConfiguration::where(LeadProcessingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET, '=', $timezone[LeadProcessingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET])
                ->update([
                    LeadProcessingTimeZoneConfiguration::FIELD_NAME => $timezone[LeadProcessingTimeZoneConfiguration::FIELD_NAME],
                    LeadProcessingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR => $timezone[LeadProcessingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR],
                    LeadProcessingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR => $timezone[LeadProcessingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR]
                ]);
        }

        return true;
    }

    /**
     * @return Collection
     */
    public function getAllCallingTimezoneConfigurations(): Collection
    {
        return LeadProcessingCallingTimeZoneConfiguration::all();
    }

    /**
     * @param Collection $timezones
     * @return bool
     */
    public function saveCallingTimezoneConfigurations(Collection $timezones): bool
    {
        foreach($timezones as $timezone) {
            LeadProcessingCallingTimeZoneConfiguration::where(LeadProcessingCallingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET, '=', $timezone[LeadProcessingCallingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET])
                ->update([
                    LeadProcessingCallingTimeZoneConfiguration::FIELD_NAME => $timezone[LeadProcessingCallingTimeZoneConfiguration::FIELD_NAME],
                    LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR => $timezone[LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR],
                    LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR => $timezone[LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR]
                ]);
        }

        return true;
    }

    /**
     * @param int $timeZoneOpeningDelayMin
     * @param int $leadRecencyThresholdSec
     * @param int $minimumReviewTime
     * @param int $leadProcessableDelaySec
     * @param int $checkNextLeadInterval
     * @param int $lastLeadCreatedInterval
     * @return bool
     */
    public function saveLeadProcessingConfiguration(
        int $timeZoneOpeningDelayMin,
        int $leadRecencyThresholdSec,
        int $minimumReviewTime,
        int $leadProcessableDelaySec,
        int $checkNextLeadInterval,
        int $lastLeadCreatedInterval
    ): bool
    {
        return LeadProcessingConfiguration::query()
            ->first()
            ->update([
                LeadProcessingConfiguration::FIELD_TIME_ZONE_OPENING_DELAY_IN_MINUTES => $timeZoneOpeningDelayMin,
                LeadProcessingConfiguration::FIELD_LEAD_RECENCY_THRESHOLD_IN_SECONDS  => $leadRecencyThresholdSec,
                LeadProcessingConfiguration::FIELD_MINIMUM_REVIEW_TIME                => $minimumReviewTime,
                LeadProcessingConfiguration::FIELD_LEAD_PROCESSABLE_DELAY_SECONDS     => $leadProcessableDelaySec,
                LeadProcessingConfiguration::FIELD_CHECK_NEXT_LEAD_INTERVAL_SECONDS   => $checkNextLeadInterval,
                LeadProcessingConfiguration::FIELD_LAST_LEAD_CREATED_INTERVAL_MIN     => $lastLeadCreatedInterval
            ]);
    }
}
