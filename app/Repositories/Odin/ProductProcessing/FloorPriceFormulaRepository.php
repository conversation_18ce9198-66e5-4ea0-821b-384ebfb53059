<?php

namespace App\Repositories\Odin\ProductProcessing;

use App\Models\Legacy\Location;
use App\Models\Odin\FloorPriceFormula;
use App\Services\DatabaseHelperService;
use Illuminate\Support\Collection;

class FloorPriceFormulaRepository
{

    /**
     * @param array $stateAbbrs
     * @return Collection <int, FloorPriceFormula>
     */
    public function getFormulasByStates(array $stateAbbrs): Collection
    {
        return FloorPriceFormula::query()
            ->leftJoin(Location::TABLE, FloorPriceFormula::TABLE.'.'.FloorPriceFormula::FIELD_LOCATION_ID, '=', Location::TABLE.'.'.Location::ID)
            ->whereIn(Location::TYPE, [Location::TYPE_STATE, Location::TYPE_COUNTY])
            ->whereIn(Location::STATE_ABBREVIATION, $stateAbbrs)
            ->get();
    }

    /**
     * @param array $matchParams
     * @param array $updateParams
     * @return FloorPriceFormula
     */
    public function updateOrCreateFormula(array $matchParams, array $updateParams): FloorPriceFormula
    {
        return FloorPriceFormula::updateOrCreate($matchParams, $updateParams);
    }

    /**
     * @param string $industry
     * @param string $product
     * @param int $locationId
     * @param string $propertyType
     * @param string $qualityTier
     * @param string $saleType
     *
     * @return FloorPriceFormula|null
     */
    public function getFormula(string $industry, string $product, int $locationId, string $propertyType, string $qualityTier, string $saleType): ?FloorPriceFormula
    {
        /** @var FloorPriceFormula|null */
        return FloorPriceFormula::query()
            ->where(FloorPriceFormula::FIELD_INDUSTRY, $industry)
            ->where(FloorPriceFormula::FIELD_PRODUCT, $product)
            ->where(FloorPriceFormula::FIELD_LOCATION_ID, $locationId)
            ->where(FloorPriceFormula::FIELD_PROPERTY_TYPE, $propertyType)
            ->where(FloorPriceFormula::FIELD_QUALITY_TIER, $qualityTier)
            ->where(FloorPriceFormula::FIELD_SALES_TYPE, $saleType)
            ->first();
    }
}
