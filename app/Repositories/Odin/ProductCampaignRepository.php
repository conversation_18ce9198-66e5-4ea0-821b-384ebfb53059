<?php

namespace App\Repositories\Odin;

use App\Enums\Odin\ProductCampaignStatusType;
use App\Events\ProductCampaignStatusUpdatedEvent;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignPauseReasonsList;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Legacy\LeadSalesType;
use App\Models\Odin\Company;
use App\Models\Odin\Product;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ProductCampaignBudget;
use App\Models\Odin\ServiceProduct;
use App\Services\DatabaseHelperService;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use App\Enums\Odin\Product as ProductEnum;

class ProductCampaignRepository
{
    /**
     * @param int $id
     * @param Company $company
     * @return ProductCampaign|null
     */
    public function findByIdAndCompany(int $id, Company $company): ?ProductCampaign
    {
        /** @type ProductCampaign|null */
        return ProductCampaign::query()
            ->where(ProductCampaign::FIELD_COMPANY_ID, $company->{Company::FIELD_ID})
            ->where(ProductCampaign::FIELD_ID, $id)
            ->first();
    }

    /**
     * @param string $legacyCampaignUuid
     * @param ProductEnum $product
     * @return ProductCampaign
     */
    public function createFromLegacyCampaign(
        string $legacyCampaignUuid,
        ProductEnum $product = ProductEnum::LEAD
    ): ProductCampaign
    {
        $legacyCampaign = LeadCampaign::query()
            ->where(LeadCampaign::UUID, $legacyCampaignUuid)
            ->with([
                LeadCampaign::RELATION_LEAD_SALES_TYPE_CONFIGURATIONS
            ])
            ->firstOrFail();

        $productId = Product::query()->where(Product::FIELD_NAME, $product->value)->firstOrFail()->{Product::FIELD_ID};
        $companyId = Company::query()->where(Company::FIELD_LEGACY_ID, $legacyCampaign->{LeadCampaign::COMPANY_ID})->firstOrFail()->{Company::FIELD_ID};

        /** @var ProductCampaign $productCampaign */
        $productCampaign = ProductCampaign::query()->create([
            ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID => $legacyCampaign->{LeadCampaign::ID},
            ProductCampaign::FIELD_NAME => $legacyCampaign->{LeadCampaign::NAME},
            ProductCampaign::FIELD_COMPANY_ID => $companyId,
            ProductCampaign::FIELD_PRODUCT_ID => $productId
        ]);

        return $productCampaign;
    }

    /**
     * @param int $companyId
     * @param int|null $status
     * @param int|null $productId
     *
     * @return Collection
     */
    public function getCompanyCampaigns(int $companyId, ?int $status = null, ?int $productId = null): Collection
    {
        return $this->getCompanyCampaignsQuery($companyId, $status, $productId)->get();
    }

    /**
     * @param int $companyId
     * @param bool|null $status
     * @param int|null $productId
     * @param bool|null $withDeleted
     * @return Builder
     */
    public function getCompanyCampaignsQuery(
        int $companyId,
        ?bool $status = null,
        ?int $productId = null,
        ?bool $withDeleted = null
    ): Builder
    {
        $query = $withDeleted ? ProductCampaign::withTrashed() : ProductCampaign::query();

        return $query
            ->where(ProductCampaign::FIELD_COMPANY_ID, $companyId)
            ->when($status === true, fn(Builder $query) => $query->where(ProductCampaign::FIELD_STATUS, 1))
            ->when($productId !== null, fn(Builder $query) => $query->where(ProductCampaign::FIELD_PRODUCT_ID, $productId));
    }

    /**
     * @param int $legacyCompanyId
     * @param string $relationship
     * @return Collection<int, ProductCampaign>
     */
    public function getAllByLegacyCompanyId(int $legacyCompanyId, string $relationship = ProductCampaign::RELATION_BUDGETS): Collection
    {
        return $this->joinLegacyCampaignQuery()
            ->where(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::COMPANY_ID, $legacyCompanyId)
            ->with($relationship)
            ->get();
    }

    /**
     * @param string $legacyCampaignUuid
     * @param ProductEnum $product
     * @param array $relationships
     * @return ProductCampaign|null
     */
    public function getByLegacyCampaignUuid(string $legacyCampaignUuid, ProductEnum $product = ProductEnum::LEAD, array $relationships = [ProductCampaign::RELATION_BUDGETS]): ?Model
    {
        return $this->joinLegacyCampaignQuery()
            ->whereHas(ProductCampaign::RELATION_PRODUCT, function($has) use ($product) {
                $has->where(Product::TABLE.'.'.Product::FIELD_NAME, $product->value);
            })
            ->where(DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaign::TABLE . '.' . LeadCampaign::UUID, $legacyCampaignUuid)
            ->with($relationships)
            ->first();
    }

    /**
     * @param string $legacyUuid
     *
     * @return ProductCampaign
     */
    public function findCampaignByLegacyUuidOrFail(string $legacyUuid): ProductCampaign
    {
        /** @var ProductCampaign */
        return ProductCampaign::query()
            ->selectRaw(ProductCampaign::TABLE . '.*')
            ->join(
                DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaign::TABLE,
                ProductCampaign::TABLE . '.' . ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID,
                DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaign::TABLE . '.' . LeadCampaign::ID
            )
            ->where(DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaign::TABLE . '.' . LeadCampaign::UUID, $legacyUuid)
            ->firstOrFail();
    }

    /**
     * @param string $legacyUuid
     *
     * @return ProductCampaign|null
     */
    public function findCampaignByLegacyUuid(string $legacyUuid): ?ProductCampaign
    {
        /** @var ProductCampaign */
        return ProductCampaign::query()
            ->selectRaw(ProductCampaign::TABLE . '.*')
            ->join(
                DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaign::TABLE,
                ProductCampaign::TABLE . '.' . ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID,
                DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaign::TABLE . '.' . LeadCampaign::ID
            )
            ->where(DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaign::TABLE . '.' . LeadCampaign::UUID, $legacyUuid)
            ->first();
    }

    /**
     * @param int $legacyParentId
     *
     * @return ProductCampaign
     */
    public function findProductCampaignByLegacyParentIdOrFail(int $legacyParentId): ProductCampaign
    {
        /** @var ProductCampaign */
        return ProductCampaign::query()->where(ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID, $legacyParentId)->firstOrFail();
    }

    /**
     * @param ProductCampaign $productCampaign
     *
     * @return ServiceProduct
     */
    public function getServiceProductFromCampaign(ProductCampaign $productCampaign): ServiceProduct
    {
        /** @var ServiceProduct */
        return ServiceProduct::query()
            ->where(ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $productCampaign->industry_service_id)
            ->whereHas(ServiceProduct::RELATION_PRODUCT, fn(Builder $query) => $query->where(Product::FIELD_ID, $productCampaign->product_id))
            ->firstOrFail();
    }

    /**
     * @param int $companyId
     * @param array $industryServiceIds
     * @param int $productId
     * @return bool
     */
    public function isUnverifiedOnInServiceScope(int $companyId, array $industryServiceIds, int $productId = 1): bool
    {
        return $this->checkSalesTypeIsOn($companyId, $industryServiceIds, $productId, LeadSalesType::LEAD_SALE_TYPE_UNVERIFIED_ID);
    }

    /**
     * @param int $companyId
     * @param array $industryServiceIds
     * @param int $productId
     * @return bool
     */
    public function isEmailOnlyOnInServiceScope(int $companyId, array $industryServiceIds, int $productId = 1): bool
    {
        return $this->checkSalesTypeIsOn($companyId, $industryServiceIds, $productId, LeadSalesType::LEAD_SALE_TYPE_EMAIL_ONLY_ID);
    }

    private function checkSalesTypeIsOn(int $companyId, array $industryServiceIds, int $productId, int $salesTypeId): bool
    {
        $query = ProductCampaign::query()
            ->where(ProductCampaign::FIELD_COMPANY_ID, $companyId)
            ->whereIn(ProductCampaign::FIELD_INDUSTRY_SERVICE_ID, $industryServiceIds)
            ->where(ProductCampaign::FIELD_PRODUCT_ID, $productId);

        $totalScopedCampaigns = $query->count();

        $salesTypeIsOn = $query->join(DatabaseHelperService::readOnlyDatabase() .'.'. LeadCampaignSalesTypeConfiguration::TABLE,
            ProductCampaign::TABLE .'.'. ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID,
            '=',
            DatabaseHelperService::readOnlyDatabase() .'.'. LeadCampaignSalesTypeConfiguration::TABLE .'.'. LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID
            )->where(DatabaseHelperService::readOnlyDatabase() .'.'. LeadCampaignSalesTypeConfiguration::TABLE .'.'. LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID, $salesTypeId)
            ->where(DatabaseHelperService::readOnlyDatabase() .'.'. LeadCampaignSalesTypeConfiguration::TABLE .'.'. LeadCampaignSalesTypeConfiguration::STATUS, 1)
            ->count();

        return $totalScopedCampaigns > 0 && $salesTypeIsOn === $totalScopedCampaigns;
    }

    /**
     * @return Builder
     */
    private function joinLegacyCampaignQuery(): Builder
    {
        return ProductCampaign::query()
            ->select(ProductCampaign::TABLE.'.*')
            ->leftJoin(
                DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE,
                ProductCampaign::TABLE.'.'.ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID,
                '=',
                DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::ID
            );
    }

    /**
     * @param int $productCampaignId
     * @param bool $newStatus
     * @param Company $company
     * @param int $initiatorId
     * @param string|null $statusReason
     * @param int|null $reactivateAtTimestamp
     * @return bool
     * @throws Exception
     */
    public function updateStatus(
        int $productCampaignId,
        bool $newStatus,
        Company $company,
        int $initiatorId,
        ?string $statusReasonKey = null,
        ?int $reactivateAtTimestamp = 0
    ): bool
    {
        if($reactivateAtTimestamp < 0) {
            throw new Exception("Invalid reactivation timestamp");
        }

        $productCampaign = ProductCampaign::query()->findOrFail($productCampaignId);

        $oldStatus = $productCampaign->{ProductCampaign::FIELD_STATUS};

        $productCampaign->{ProductCampaign::FIELD_STATUS} = $newStatus;
        $productCampaign->{ProductCampaign::FIELD_REACTIVATE_AT_TIMESTAMP} = $reactivateAtTimestamp;

        $productCampaign->save();

        $pauseReason = LeadCampaignPauseReasonsList::query()->where(LeadCampaignPauseReasonsList::FIELD_KEY, $statusReasonKey)->first();

        ProductCampaignStatusUpdatedEvent::dispatch(
            $productCampaignId,
            $company->{Company::FIELD_REFERENCE},
            $initiatorId,
            $oldStatus,
            $newStatus,
            $reactivateAtTimestamp ? ProductCampaignStatusType::TEMPORARY : ProductCampaignStatusType::PERMANENT,
            $pauseReason ? $pauseReason->{LeadCampaignPauseReasonsList::FIELD_NAME} : $statusReasonKey,
            $reactivateAtTimestamp
        );

        return true;
    }

    /**
     * @param Company $company
     * @return bool
     */
    public function deactivateCompanyCampaigns(Company $company): bool
    {
        $productCampaignIds = ProductCampaign::query()
            ->where(ProductCampaign::FIELD_COMPANY_ID, $company->{Company::FIELD_ID})
            ->pluck(ProductCampaign::FIELD_ID);

        ProductCampaign::query()
            ->whereIn(ProductCampaign::FIELD_ID, $productCampaignIds)
            ->update([
                ProductCampaign::FIELD_STATUS => false,
                ProductCampaign::FIELD_REACTIVATE_AT_TIMESTAMP => 0
            ]);

        ProductCampaignBudget::query()
            ->whereIn(ProductCampaignBudget::FIELD_PRODUCT_CAMPAIGN_ID, $productCampaignIds)
            ->update([
                ProductCampaignBudget::FIELD_STATUS => false
            ]);

        return true;
    }

    /**
     * @param int $productCampaignId
     * @return bool
     */
    public function deleteCampaign(int $productCampaignId): bool
    {
        ProductCampaign::query()->where(ProductCampaign::FIELD_ID, $productCampaignId)->delete();

        return true;
    }

    /**
     * @param int $companyId
     * @param string $query
     * @return Collection
     */
    public function searchCompanyProductCampaigns(int $companyId, string $query): Collection
    {
        return ProductCampaign::query()
            ->where(ProductCampaign::FIELD_COMPANY_ID, $companyId)
            ->where(ProductCampaign::FIELD_NAME, 'LIKE', "%{$query}%")
            ->get();
    }
}
