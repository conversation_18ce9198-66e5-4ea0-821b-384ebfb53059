<?php

namespace App\Repositories\Odin;

use App\Contracts\Repositories\Odin\IndustryTypeRepositoryContract;
use App\Models\Odin\CompanyIndustryType;
use App\Models\Odin\CompanyReview;
use App\Models\Odin\IndustryType;
use Illuminate\Support\Collection;

class IndustryTypeRepository implements IndustryTypeRepositoryContract
{
    /**
     * @param CompanyIndustryTypeRepository $companyIndustryTypeRepository
     */
    public function __construct(protected CompanyIndustryTypeRepository $companyIndustryTypeRepository){}

    /**
     * @inheritDoc
     */
    public function getIndustryTypes(int $industry): Collection
    {
        return IndustryType::query()->where(IndustryType::FIELD_INDUSTRY_ID, $industry)->latest()->get();
    }

    /**
     * @inheritDoc
     */
    public function updateOrCreateIndustryType(
        int    $globalType,
        int    $industry,
        string $name,
        string $key,
        ?int   $id = null
    ): bool
    {
        return IndustryType::query()->updateOrCreate(
            [
                IndustryType::FIELD_ID => $id
            ],
            [
                IndustryType::FIELD_GLOBAL_TYPE_ID  => $globalType,
                IndustryType::FIELD_INDUSTRY_ID     => $industry,
                IndustryType::FIELD_NAME            => $name,
                IndustryType::FIELD_KEY             => $key,
            ]
        ) !== null;
    }

    /**
     * @inheritDoc
     */
    public function deleteIndustryType(int $id): bool
    {
        return IndustryType::query()->where(IndustryType::FIELD_ID, $id)->delete();
    }

    /**
     * @inheritDoc
     */
    public function getIndustryTypeReviews(int $industryType): Collection
    {
        /** @var CompanyIndustryType $companies */
        $companies = $this->companyIndustryTypeRepository->getCompanies($industryType)->pluck(CompanyIndustryType::FIELD_COMPANY_ID);

        return CompanyReview::query()->whereIn(CompanyReview::FIELD_COMPANY_ID, $companies)->latest()->get();
    }

    /**
     * @inheritDoc
     */
    public function createIndustryTypeReview(
        int     $industryType,
        int     $company,
        int     $relId,
        int     $relType,
        string  $firstName,
        string  $lastName,
        ?string $email,
        ?string $phone,
        string  $title,
        string  $body,
        int     $overallScore,
        int     $status,
        string  $ipAddress
    ): bool
    {
        /** @var CompanyIndustryType|null $isCompanySetupForIndustryType */
        $isCompanySetupForIndustryType = $this->companyIndustryTypeRepository->get($industryType, $company);

        if(empty($isCompanySetupForIndustryType))
            $this->companyIndustryTypeRepository->add($industryType, $company);

        return CompanyReview::query()->create(
            [
                CompanyReview::FIELD_COMPANY_ID    => $company,
                CompanyReview::FIELD_REL_ID        => $relId,
                CompanyReview::FIELD_REL_TYPE      => $relType,
                CompanyReview::FIELD_FIRST_NAME    => $firstName,
                CompanyReview::FIELD_LAST_NAME     => $lastName,
                CompanyReview::FIELD_EMAIL         => $email,
                CompanyReview::FIELD_PHONE         => $phone,
                CompanyReview::FIELD_TITLE         => $title,
                CompanyReview::FIELD_BODY          => $body,
                CompanyReview::FIELD_OVERALL_SCORE => $overallScore,
                CompanyReview::FIELD_STATUS        => $status,
                CompanyReview::FIELD_IP_ADDRESS    => $ipAddress
            ]
        ) !== null;
    }

    /**
     * @inheritDoc
     */
    public function deleteIndustryTypeReview(int $review): bool
    {
        return CompanyReview::query()->where(CompanyReview::FIELD_ID, $review)->delete();
    }
}
