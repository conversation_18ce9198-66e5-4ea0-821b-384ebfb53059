<?php

namespace App\Repositories\Odin;

use App\Models\Odin\LocationSiloPage;
use App\Models\Odin\Silo;
use Illuminate\Support\Collection;

class SiloRepository
{
    /**
     * @param array $attributes
     * @return Silo
     */
    public function createSilo(array $attributes): Silo
    {
        /** @var Silo */
        return Silo::query()
            ->create($attributes);
    }

    /**
     * @param array $locationSiloPagesArray
     * @return int
     */
    public function createLocationsForNewSilo(array $locationSiloPagesArray): int
    {
        return LocationSiloPage::query()
            ->upsert($locationSiloPagesArray, []);
    }

    /**
     * @return Collection<Silo>
     */
    public function getAllSilos(): Collection
    {
        return Silo::query()
            ->with([Silo::RELATION_WEBSITE, Silo::RELATION_INDUSTRY, Silo::RELATION_LOCATION_SILO_PAGES, Silo::RELATION_LOCATION_SILO_PAGES .'.'. LocationSiloPage::RELATION_LOCATION])
            ->get();
    }

    /**
     * @param int $siloId
     * @param array $attributes
     * @return Silo
     */
    public function updateSiloById(int $siloId, array $attributes): Silo
    {
        $silo = Silo::query()->findOrFail($siloId);
        $silo->update($attributes);

        /** @var Silo */
        return $silo->refresh();
    }
}
