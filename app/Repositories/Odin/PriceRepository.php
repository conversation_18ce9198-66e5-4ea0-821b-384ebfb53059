<?php

namespace App\Repositories\Odin;

use App\Enums\Odin\PropertyType;
use App\Enums\Odin\QualityTier;
use App\Enums\Odin\SaleTypes;
use App\Models\Campaigns\CustomCampaignCountyFloorPrice;
use App\Models\Campaigns\CustomCampaignStateFloorPrice;
use App\Models\Campaigns\Modules\BaseCompanyCampaignModule;
use App\Models\Campaigns\Modules\Bidding\CompanyCampaignBidPriceModule;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ProductCountyBidPrice;
use App\Models\Odin\ProductCountyFloorPrice;
use App\Models\Odin\ProductStateBidPrice;
use App\Models\Odin\ProductStateFloorPrice;
use App\Models\Odin\ServiceProduct;
use App\Models\SaleType;
use App\Repositories\LocationRepository;
use Illuminate\Support\Collection;

class PriceRepository
{
    const DEFAULT_PRICE = 120;

    public function __construct(protected LocationRepository $locationRepository) {}

    /**
     * @param ConsumerProduct $consumerProduct
     * @param ProductCampaign $productCampaign
     * @param SaleTypes $saleType
     * @param QualityTier $qualityTier
     * @param PropertyType $propertyType
     *
     * @return float
     */
    public function getPriceForProductAndCampaign(
        ConsumerProduct $consumerProduct,
        ProductCampaign $productCampaign,
        SaleTypes $saleType = SaleTypes::EXCLUSIVE,
        QualityTier $qualityTier = QualityTier::STANDARD,
        PropertyType $propertyType = PropertyType::RESIDENTIAL,
    ): float
    {
        $zipCode = $this->locationRepository->getZipCode($consumerProduct->address->zip_code);
        $statLocation = $this->locationRepository->getState($zipCode->state_key);
        $countyLocation = $this->locationRepository->getCounty($zipCode->state_key, $zipCode->county_key);

        /** @var SaleType $saleTypeModel */
        $saleTypeModel = SaleType::query()->where(SaleType::FIELD_NAME, $saleType)->firstOrFail();

        /** @var \App\Models\Odin\QualityTier $qualityTierModel */
        $qualityTierModel = \App\Models\Odin\QualityTier::query()->where(\App\Models\Odin\QualityTier::FIELD_NAME, $qualityTier)->firstOrFail();

        /** @var \App\Models\Odin\PropertyType $propertyTypeModel */
        $propertyTypeModel = \App\Models\Odin\PropertyType::query()->where(\App\Models\Odin\PropertyType::FIELD_NAME, $propertyType)->firstOrFail();

        return $this->getCountyBidPrice(
            $consumerProduct,
            $productCampaign,
            $countyLocation->id,
            $statLocation->id,
            $saleTypeModel->id,
            $qualityTierModel->id,
            $propertyTypeModel->id
        ) ?? $this->getStateBidPrice(
            $consumerProduct,
            $productCampaign,
            $statLocation->id,
            $saleTypeModel->id,
            $qualityTierModel->id,
            $propertyTypeModel->id
        ) ?? $this->getCountyFloorPrice(
            $consumerProduct,
            $countyLocation->id,
            $statLocation->id,
            $saleTypeModel->id,
            $qualityTierModel->id,
            $propertyTypeModel->id
        ) ?? $this->getStateFlorPrice(
            $consumerProduct,
            $statLocation->id,
            $saleTypeModel->id,
            $qualityTierModel->id,
            $propertyTypeModel->id
        ) ?? self::DEFAULT_PRICE;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param ProductCampaign $productCampaign
     * @param int $countyLocationId
     * @param int $stateLocationId
     * @param int $saleTypeId
     * @param int $qualityTierId
     * @param int $propertyTypeId
     *
     * @return float|null
     */
    protected function getCountyBidPrice(
        ConsumerProduct $consumerProduct,
        ProductCampaign $productCampaign,
        int $countyLocationId,
        int $stateLocationId,
        int $saleTypeId,
        int $qualityTierId,
        int $propertyTypeId
    ): ?float
    {
        return ProductCountyBidPrice::query()
            ->where(ProductCountyBidPrice::FIELD_PRODUCT_CAMPAIGN_ID, $productCampaign->id)
            ->where(ProductCountyBidPrice::FIELD_SERVICE_PRODUCT_ID, $consumerProduct->service_product_id)
            ->where(ProductCountyBidPrice::FIELD_COUNTY_LOCATION_ID, $countyLocationId)
            ->where(ProductCountyBidPrice::FIELD_STATE_LOCATION_ID, $stateLocationId)
            ->where(ProductCountyBidPrice::FIELD_SALE_TYPE_ID, $saleTypeId)
            ->where(ProductCountyBidPrice::FIELD_QUALITY_TIER_ID, $qualityTierId)
            ->where(ProductCountyBidPrice::FIELD_PROPERTY_TYPE_ID, $propertyTypeId)
            ->first()?->price;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param ProductCampaign $productCampaign
     * @param int $stateLocationId
     * @param int $saleTypeId
     * @param int $qualityTierId
     * @param int $propertyTypeId
     *
     * @return float|null
     */
    protected function getStateBidPrice(
        ConsumerProduct $consumerProduct,
        ProductCampaign $productCampaign,
        int $stateLocationId,
        int $saleTypeId,
        int $qualityTierId,
        int $propertyTypeId
    ): ?float
    {
        return ProductStateBidPrice::query()
            ->where(ProductStateBidPrice::FIELD_PRODUCT_CAMPAIGN_ID, $productCampaign->id)
            ->where(ProductStateBidPrice::FIELD_SERVICE_PRODUCT_ID, $consumerProduct->service_product_id)
            ->where(ProductStateBidPrice::FIELD_STATE_LOCATION_ID, $stateLocationId)
            ->where(ProductStateBidPrice::FIELD_SALE_TYPE_ID, $saleTypeId)
            ->where(ProductStateBidPrice::FIELD_QUALITY_TIER_ID, $qualityTierId)
            ->where(ProductStateBidPrice::FIELD_PROPERTY_TYPE_ID, $propertyTypeId)
            ->first()?->price;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param int $countyLocationId
     * @param int $stateLocationId
     * @param int $saleTypeId
     * @param int $qualityTierId
     * @param int $propertyTypeId
     *
     * @return float|null
     */
    protected function getCountyFloorPrice(
        ConsumerProduct $consumerProduct,
        int $countyLocationId,
        int $stateLocationId,
        int $saleTypeId,
        int $qualityTierId,
        int $propertyTypeId
    ): ?float
    {
        return ProductCountyFloorPrice::query()
            ->where(ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID, $consumerProduct->service_product_id)
            ->where(ProductCountyFloorPrice::FIELD_COUNTY_LOCATION_ID, $countyLocationId)
            ->where(ProductCountyFloorPrice::FIELD_STATE_LOCATION_ID, $stateLocationId)
            ->where(ProductCountyFloorPrice::FIELD_SALE_TYPE_ID, $saleTypeId)
            ->where(ProductCountyFloorPrice::FIELD_QUALITY_TIER_ID, $qualityTierId)
            ->where(ProductCountyFloorPrice::FIELD_PROPERTY_TYPE_ID, $propertyTypeId)
            ->first()?->price;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param int $stateLocationId
     * @param int $saleTypeId
     * @param int $qualityTierId
     * @param int $propertyTypeId
     *
     * @return float|null
     */
    protected function getStateFlorPrice(
        ConsumerProduct $consumerProduct,
        int $stateLocationId,
        int $saleTypeId,
        int $qualityTierId,
        int $propertyTypeId
    ): ?float
    {
        return ProductStateFloorPrice::query()
            ->where(ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID, $consumerProduct->service_product_id)
            ->where(ProductStateFloorPrice::FIELD_STATE_LOCATION_ID, $stateLocationId)
            ->where(ProductStateFloorPrice::FIELD_SALE_TYPE_ID, $saleTypeId)
            ->where(ProductStateFloorPrice::FIELD_QUALITY_TIER_ID, $qualityTierId)
            ->where(ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID, $propertyTypeId)
            ->first()?->price;
    }

    /**
     * @param array $locationIds
     * @param array $industryServiceIds
     * @return array
     */
    public function getFloorPricesForLocationsByService(array $locationIds, array $industryServiceIds): array
    {
        $countyFloors = ProductCountyFloorPrice::query()
            ->leftJoin(ServiceProduct::TABLE, ProductCountyFloorPrice::TABLE . '.' . ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID, ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_ID)
            ->whereIntegerInRaw(ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $industryServiceIds)
            ->whereIntegerInRaw(ProductCountyFloorPrice::FIELD_COUNTY_LOCATION_ID, $locationIds)
            ->get([
                ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_INDUSTRY_SERVICE_ID,
                ProductCountyFloorPrice::FIELD_COUNTY_LOCATION_ID . ' as location_id',
                ProductCountyFloorPrice::FIELD_PRICE,
                ProductCountyFloorPrice::FIELD_QUALITY_TIER_ID,
                ProductCountyFloorPrice::FIELD_SALE_TYPE_ID
            ])->toArray();

        $stateFloors = ProductStateFloorPrice::query()
            ->leftJoin(ServiceProduct::TABLE, ProductStateFloorPrice::TABLE . '.' . ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID, ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_ID)
            ->whereIntegerInRaw(ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $industryServiceIds)
            ->whereIntegerInRaw(ProductStateFloorPrice::FIELD_STATE_LOCATION_ID, $locationIds)
            ->get([
                ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_INDUSTRY_SERVICE_ID,
                ProductStateFloorPrice::FIELD_STATE_LOCATION_ID . ' as location_id',
                ProductStateFloorPrice::FIELD_PRICE,
                ProductStateFloorPrice::FIELD_QUALITY_TIER_ID,
                ProductStateFloorPrice::FIELD_SALE_TYPE_ID
            ])->toArray();

        return collect(array_merge($stateFloors, $countyFloors))->mapToGroups(fn($price, $key) => [$price[ServiceProduct::FIELD_INDUSTRY_SERVICE_ID] => $price])->toArray();

    }

    /**
     * @param array $campaignIds
     * @param array $locationIdToStateAndCountyId
     * @return array
     */
    public function getCampaignFloorsForLocations(array $campaignIds, array $locationIdToStateAndCountyId): array
    {
        $countyIds = $stateIds = [];
        foreach ($locationIdToStateAndCountyId as $mappedLocationId) {
            $stateIds[]  = $mappedLocationId['state_location_id'];
            $countyIds[] = $mappedLocationId['county_location_id'];
        }

        $countyFloors = CustomCampaignCountyFloorPrice::query()
            ->whereIntegerInRaw(CustomCampaignCountyFloorPrice::FIELD_COMPANY_CAMPAIGN_ID, $campaignIds)
            ->whereIntegerInRaw(CustomCampaignCountyFloorPrice::FIELD_COUNTY_LOCATION_ID, $countyIds)
            ->get();

        $stateFloors = CustomCampaignStateFloorPrice::query()
            ->whereIntegerInRaw(CustomCampaignStateFloorPrice::FIELD_COMPANY_CAMPAIGN_ID, $campaignIds)
            ->whereIntegerInRaw(CustomCampaignStateFloorPrice::FIELD_STATE_LOCATION_ID, $stateIds)
            ->get();

        $campaignFloors = [];
        foreach ($campaignIds as $campaignId) {
            $campaignCountyFloors        = $countyFloors->where(CustomCampaignCountyFloorPrice::FIELD_COMPANY_CAMPAIGN_ID, $campaignId);
            $campaignStateFloors         = $stateFloors->where(CustomCampaignStateFloorPrice::FIELD_COMPANY_CAMPAIGN_ID, $campaignId);
            $campaignFloors[$campaignId] = [
                'states'   => $campaignStateFloors->map(fn(CustomCampaignStateFloorPrice $floor) => [
                    CustomCampaignStateFloorPrice::FIELD_QUALITY_TIER_ID => $floor->quality_tier_id,
                    CustomCampaignStateFloorPrice::FIELD_SALE_TYPE_ID    => $floor->sale_type_id,
                    'location_id'                                        => $floor->state_location_id,
                    CustomCampaignStateFloorPrice::FIELD_PRICE           => $floor->price,
                ])->toArray(),
                'counties' => $campaignCountyFloors->map(fn(CustomCampaignCountyFloorPrice $floor) => [
                    CustomCampaignCountyFloorPrice::FIELD_QUALITY_TIER_ID => $floor->quality_tier_id,
                    CustomCampaignCountyFloorPrice::FIELD_SALE_TYPE_ID    => $floor->sale_type_id,
                    'location_id'                                         => $floor->county_location_id,
                    CustomCampaignCountyFloorPrice::FIELD_PRICE           => $floor->price,
                ])->toArray(),
            ];
        }

        return $campaignFloors;
    }

    /**
     * @param array $campaignIds
     * @param array $locationIdToStateAndCountyId
     * @return array
     */
    public function getCampaignBidsForLocations(array $campaignIds, array $locationIdToStateAndCountyId): array
    {
        $countyIds = $stateIds = [];
        foreach ($locationIdToStateAndCountyId as $mappedLocationId) {
            $stateIds[]  = $mappedLocationId['state_location_id'];
            $countyIds[] = $mappedLocationId['county_location_id'];
        }

        $campaignIdToModuleId = CompanyCampaignBidPriceModule::query()
            ->whereIntegerInRaw(BaseCompanyCampaignModule::FIELD_CAMPAIGN_ID, $campaignIds)
            ->get([BaseCompanyCampaignModule::FIELD_ID, BaseCompanyCampaignModule::FIELD_CAMPAIGN_ID])
            ->pluck(BaseCompanyCampaignModule::FIELD_ID, BaseCompanyCampaignModule::FIELD_CAMPAIGN_ID)->toArray();

        $moduleIds = array_values($campaignIdToModuleId);

        $countyBids = ProductCountyBidPrice::query()
            ->whereIntegerInRaw(ProductCountyBidPrice::FIELD_MODULE_ID, $moduleIds)
            ->whereIntegerInRaw(ProductCountyBidPrice::FIELD_COUNTY_LOCATION_ID, $countyIds)
            ->get();
        $stateBids  = ProductStateBidPrice::query()
            ->whereIntegerInRaw(ProductStateBidPrice::FIELD_MODULE_ID, $moduleIds)
            ->whereIntegerInRaw(ProductStateBidPrice::FIELD_STATE_LOCATION_ID, $stateIds)
            ->get();

        $campaignBids = [];
        foreach ($campaignIds as $campaignId) {
            $moduleId                  = key_exists($campaignId, $campaignIdToModuleId) ? $campaignIdToModuleId[$campaignId] : null;
            $campaignCountyBids        = $moduleId ? $countyBids->where(ProductCountyBidPrice::FIELD_MODULE_ID, $moduleId) : collect();
            $campaignStateBids         = $moduleId ? $stateBids->where(ProductStateBidPrice::FIELD_MODULE_ID, $moduleId):  collect();
            $campaignBids[$campaignId] = [
                'states'   => $campaignStateBids->map(fn(ProductStateBidPrice $bid) => [
                    CustomCampaignStateFloorPrice::FIELD_QUALITY_TIER_ID => $bid->quality_tier_id,
                    CustomCampaignStateFloorPrice::FIELD_SALE_TYPE_ID    => $bid->sale_type_id,
                    'location_id'                                        => $bid->state_location_id,
                    CustomCampaignStateFloorPrice::FIELD_PRICE           => $bid->price,
                ])->toArray(),
                'counties' => $campaignCountyBids->map(fn(ProductCountyBidPrice $bid) => [
                    CustomCampaignCountyFloorPrice::FIELD_QUALITY_TIER_ID => $bid->quality_tier_id,
                    CustomCampaignCountyFloorPrice::FIELD_SALE_TYPE_ID    => $bid->sale_type_id,
                    'location_id'                                         => $bid->county_location_id,
                    CustomCampaignCountyFloorPrice::FIELD_PRICE           => $bid->price,
                ])->toArray(),
            ];
        }

        return $campaignBids;

    }




}
