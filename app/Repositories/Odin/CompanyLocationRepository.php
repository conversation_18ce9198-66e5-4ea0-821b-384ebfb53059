<?php

namespace App\Repositories\Odin;

use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Ramsey\Uuid\Uuid;

class CompanyLocationRepository
{
    const DEFAULT_LOCATION_NAME = 'Office';

    /**
     * @param int $id
     * @return CompanyLocation
     */
    public function findOrFail(int $id): CompanyLocation
    {
        /** @var CompanyLocation $companyLocation */
        $companyLocation = CompanyLocation::query()->findOrFail($id);

        return $companyLocation;
    }

    /**
     * @param int $legacyCompanyId
     * @param int $legacyAddressId
     * @return CompanyLocation
     */
    public function findByLegacyAddressAndLegacyCompany(int $legacyCompanyId, int $legacyAddressId): CompanyLocation
    {
        $company = Company::query()->where(Company::FIELD_LEGACY_ID, $legacyCompanyId)->firstOrFail();
        $address = Address::query()->where(Address::FIELD_LEGACY_ID, $legacyAddressId)->firstOrFail();

        /** @var CompanyLocation $companyLocation */
        $companyLocation = CompanyLocation::query()
            ->where(CompanyLocation::FIELD_COMPANY_ID,  $company->{Company::FIELD_ID})
            ->where(CompanyLocation::FIELD_ADDRESS_ID, $address->{Address::FIELD_ID})
            ->firstOrFail();

        return $companyLocation;
    }

    /**
     * Handles creating company location
     *
     * @param array $data
     * @return CompanyLocation|Model
     */
    public function updateOrCreateCompanyLocation(array $data): CompanyLocation|Model
    {
        $newLocation = CompanyLocation::query()->updateOrCreate(
            [
                CompanyLocation::FIELD_COMPANY_ID => $data[CompanyLocation::FIELD_COMPANY_ID],
                CompanyLocation::FIELD_ADDRESS_ID => $data[CompanyLocation::FIELD_ADDRESS_ID]
            ],
            [
                CompanyLocation::FIELD_COMPANY_ID => $data[CompanyLocation::FIELD_COMPANY_ID],
                CompanyLocation::FIELD_ADDRESS_ID => $data[CompanyLocation::FIELD_ADDRESS_ID],
                CompanyLocation::FIELD_NAME       => $data[CompanyLocation::FIELD_NAME]     ?? self::DEFAULT_LOCATION_NAME,
                CompanyLocation::FIELD_PHONE      => $data[CompanyLocation::FIELD_PHONE]    ?? null,
                CompanyLocation::FIELD_IMPORTED   => $data[CompanyLocation::FIELD_IMPORTED] ?? false
            ]
        );
        if (!$newLocation->{CompanyLocation::FIELD_REFERENCE}) {
            $newLocation->update([ CompanyLocation::FIELD_REFERENCE  => Uuid::uuid4() ]);
        }
        return $newLocation;
    }

    /**
     * Remove a CompanyLocation & related Address
     * @param int $locationId
     * @param bool $removeAddress
     * @return bool
     */
    public function deleteCompanyLocation(int $locationId, bool $removeAddress = true): bool
    {
        $location = $this->findOrFail($locationId);
        if ($removeAddress) {
            if ($location->{CompanyLocation::RELATION_ADDRESS} && !$location->{CompanyLocation::RELATION_ADDRESS}->delete()) {
                return false;
            }
        }
        return $location->delete();
    }


    /**
     * Get company locations by phone
     * @param string $phone
     * @return Collection<CompanyLocation>
     */
    public function getCompanyLocationsByPhone(string $phone): Collection
    {
        return CompanyLocation::query()
            ->select([
                Company::TABLE . '.' . Company::FIELD_ID . ' as company_id',
                Company::TABLE . '.' . Company::FIELD_NAME . ' as company_name',
                CompanyLocation::TABLE . '.' . '*'
            ])
            ->where(CompanyLocation::FIELD_PHONE, $phone)
            ->join(Company::TABLE, Company::TABLE . '.' . Company::FIELD_ID, '=', CompanyLocation::TABLE . '.' . CompanyLocation::FIELD_COMPANY_ID)
            ->get();
    }


    /**
     * Get company locations by phone
     * @param string $id
     * @return ?CompanyLocation
     */
    public function getCompanyLocationById(string $id): ?CompanyLocation
    {
        /** @var CompanyLocation $companyLocation */
        $companyLocation = CompanyLocation::query()->find($id);

        return $companyLocation;
    }

    /**
     * @param int|null $companyId
     * @return Builder
     */
    public function list(
        ?int $companyId = null,
    ): Builder
    {
        return CompanyLocation::query()
            ->when($companyId, fn($query) => $query->where(CompanyLocation::FIELD_COMPANY_ID, $companyId));
    }
}
