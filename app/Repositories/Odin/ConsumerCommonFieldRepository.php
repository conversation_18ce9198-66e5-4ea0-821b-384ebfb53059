<?php

namespace App\Repositories\Odin;

use App\Models\Odin\ConsumerCommonField;

class ConsumerCommonFieldRepository
{
    public function updateMany(array $fields): bool
    {
        foreach ($fields as $field) {
            ConsumerCommonField::query()
                ->where(ConsumerCommonField::FIELD_ID, $field['id'])
                ->update([
                    ConsumerCommonField::FIELD_NAME => $field['name']
                ]);
        }

        return true;
    }
}
