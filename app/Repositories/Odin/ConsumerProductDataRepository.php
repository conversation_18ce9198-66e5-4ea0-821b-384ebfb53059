<?php

namespace App\Repositories\Odin;

use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryConsumerField;
use App\Transformers\Odin\ConsumerProductDataTransformer;
use Illuminate\Database\Eloquent\Model;
use App\Models\Odin\ConsumerProductData;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

/**
 * Repository class to interact with the DB for the ConsumerProductData model.
 */
class ConsumerProductDataRepository
{
    public function __construct(protected ConsumerProductDataTransformer $productDataTransformer) {}

    /**
     * Maps fields from a consumer_product_data 'Payload' JSON object to
     * the keys of ServiceConsumerField and IndustryConsumerField tables.
     * Matching data will be output to a 'result' object, example:
     * [
     *  "result" => {
     *      "Storeys": {"value": 3, "cat_id": 3},
     *      "House Sides": {"value": 10, "cat_id": 3},
     *      "System Size": {"value": 4546.84, "cat_id": 2},
     *  }
     * ]
     *
     * Expects an array containing 'id', 'key', 'name', and 'category_id'
     * from ServiceConsumerField model or IndustryConsumerField.
     *
     *
     * @param array $display_fields
     * @param int $consumerProductId
     * @return Model|null
     */
    public function mapPayloadDataToDisplayFields(array $display_fields, int $consumerProductId): ?Model
    {
        return ConsumerProductData::query()
            ->join('consumer_products', 'consumer_products.consumer_product_data_id', '=', 'consumer_product_data.id')
            ->select(DB::raw(
                !$display_fields ? 'NULL AS result' :
                    "JSON_OBJECT(" . implode(", ", array_map(function ($field) {
                        $name = $field['name'];
                        $cat_id = $field['category_id'];
                        $key = $field['key'];
                        return "'$name', JSON_OBJECT('cat_id',$cat_id, 'value',JSON_EXTRACT(consumer_product_data.payload, '$.\"$key\"'))";
                    }, $display_fields)) . ") AS result, JSON_UNQUOTE(JSON_EXTRACT(payload, '$.roof_type')) AS roof_type",
            ))
            ->where('consumer_products.id', '=', $consumerProductId)
            ->first();
    }

    /**
     * @param ConsumerProduct $consumerProduct
     *
     * @return Collection
     */
    public function getConsumerProductDataForCompany(ConsumerProduct $consumerProduct): Collection
    {
        $data = collect();
        $allowedIndustryFields = IndustryConsumerField::query()
            ->where(IndustryConsumerField::FIELD_INDUSTRY_ID, $consumerProduct->serviceProduct->service->industry->id)
            ->where(IndustryConsumerField::FIELD_SEND_TO_COMPANY, true)
            ->get();

        if ($allowedIndustryFields->isEmpty()) return $data;

        $allowedIndustryFields->each(fn(IndustryConsumerField $industryConsumerField) => $data->push([
            'key' => $industryConsumerField->key,
            'name' => $industryConsumerField->name,
            'value' => $this->productDataTransformer->getConsumerProductDataByKey($consumerProduct, $industryConsumerField->key)
        ]));

        return $data->filter();
    }
}
