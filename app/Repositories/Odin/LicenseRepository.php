<?php

namespace App\Repositories\Odin;

use App\Models\License;
use App\Models\Odin\Company;

class LicenseRepository
{

    /**
     * @param Company $company
     * @param array $data
     * @return License
     */
    public function createLicense(Company $company, array $data): License
    {
        $data[License::FIELD_COMPANY_ID] = $company->id;
        $data[License::FIELD_COUNTRY] =  $data[License::FIELD_COUNTRY] ?? 'US';

        /** @var License $newLicense */
        $newLicense = License::query()
            ->create($data);

        return $newLicense;
    }

    /**
     * @param Company $company
     * @param int $licenseId
     * @param array $data
     * @return License
     */
    public function updateLicense(Company $company, int $licenseId, array $data): License
    {
        /** @var License $license */
        $license = $company->licenses()->findOrFail($licenseId);

        $license->update($data);
        return $license->refresh();
    }

    /**
     * @param Company $company
     * @param int $licenseId
     * @return bool
     */
    public function deleteLicense(Company $company, int $licenseId): bool
    {
        $target = $company->licenses()->findOrFail($licenseId);
        return !!$target->delete();
    }
}
