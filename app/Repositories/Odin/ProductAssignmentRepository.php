<?php

namespace App\Repositories\Odin;

use App\Campaigns\Modules\Legacy\LegacyModule;
use App\DataModels\Campaigns\ConsumerProject;
use App\Enums\Billing\BillingVersion;
use App\Enums\Billing\InvoiceStates;
use App\Events\ProductAssignment\ProductAssignmentUpdatedEvent;
use App\Models\AppointmentProcessingAllocation;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceItem;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Odin\Consumer;
use App\Models\SaleType;
use App\Services\DatabaseHelperService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;
use App\Contracts\Repositories\Odin\ProductAssignmentRepositoryContract;
use App\Enums\Odin\ProductAssignmentAffectRejectionPercentage;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class ProductAssignmentRepository extends ProductRepository implements ProductAssignmentRepositoryContract
{
    /**
     * @inheritDoc
     */
    public function findByLegacyIdOrFail(int $legacyId): ProductAssignment
    {
        /** @var ProductAssignment $productAssignment */
        $productAssignment = ProductAssignment::query()->where(ProductAssignment::FIELD_LEGACY_ID, $legacyId)->firstOrFail();

        return $productAssignment;
    }

    /**
     * @inheritDoc
     */
    public function findByLegacyId(int $legacyId): ?ProductAssignment
    {
        /** @var ProductAssignment $productAssignment */
        $productAssignment = ProductAssignment::query()->where(ProductAssignment::FIELD_LEGACY_ID, $legacyId)->first();

        return $productAssignment;
    }

    /**
     * @inheritDoc
     */
    public function findByIdOrFail(int $id): ProductAssignment
    {
        /** @var ProductAssignment $productAssignment */
        $productAssignment = ProductAssignment::query()->findOrFail($id);

        return $productAssignment;
    }

    /**
     * @inheritDoc
     */
    public function createProductAssignment(array $data): ProductAssignment
    {
        $productAssignment = new ProductAssignment();
        $productAssignment->fill($data);

        $productAssignment->save();

        return $productAssignment;
    }

    /**
     * @param int|null $startTimestamp
     * @param int|null $endTimestamp
     * @return array
     */
    public function getCompanyIdsWithProductAssignments(
        ?int $startTimestamp = null,
        ?int $endTimestamp = null
    ): array
    {
        $query = ProductAssignment::query()->select(ProductAssignment::FIELD_COMPANY_ID);

        $this->addTimespanCriteria($query, $startTimestamp, $endTimestamp);

        return $query->groupBy(ProductAssignment::FIELD_COMPANY_ID)
            ->pluck(ProductAssignment::FIELD_COMPANY_ID)
            ->toArray();
    }

    /**
     * @param int $timeframeInDays
     * @return array
     */
    public function getCompanyIdsWithProductAssignmentsInTimeframeInDays(int $timeframeInDays = 30): array
    {
        $startTimestamp = Carbon::today()->subDays($timeframeInDays - 1)->timestamp;

        return $this->getCompanyIdsWithProductAssignments($startTimestamp);
    }

    /**
     * @inheritDoc
     */
    public function updateProductAssignment(ProductAssignment $productAssignment, array $data): bool
    {
        return $productAssignment->update($data);
    }

    /** @inheritDoc */
    public function updateOrCreateProductAssignment(array $identifyingData, array $data): ProductAssignment
    {
        /** @var ProductAssignment */
        return ProductAssignment::query()->updateOrCreate($identifyingData, $data);
    }

    /**
     * @param Company $company
     * @param bool $chargeable
     * @param bool $delivered
     * @param bool|null $affectRejectionPercentage
     * @param array|null $productIds
     * @param array|null $industryIds
     * @param array|null $industryServiceIds
     * @param int|null $startTimestamp
     * @param int|null $endTimestamp
     * @param bool|null $excludeRejections
     * @param bool|null $excludeCancellations
     * @return float
     */
    public function getTotalProductAssignmentsCostForCompany(
        Company $company,
        bool $chargeable = true,
        bool $delivered = true,
        ?bool $affectRejectionPercentage = null,
        ?array $productIds = null,
        ?array $industryIds = null,
        ?array $industryServiceIds = null,
        ?int $startTimestamp = null,
        ?int $endTimestamp = null,
        ?bool $excludeRejections = null,
        ?bool $excludeCancellations = null
    ): float
    {
        $sum = $this->getProductAssignmentsForCompanyQuery(
            company                  : $company,
            chargeable               : $chargeable,
            delivered                : $delivered,
            affectRejectionPercentage: $affectRejectionPercentage,
            productIds               : $productIds,
            industryIds              : $industryIds,
            industryServiceIds       : $industryServiceIds,
            startTimestamp           : $startTimestamp,
            endTimestamp             : $endTimestamp,
            excludeRejections        : $excludeRejections,
            excludeCancellations     : $excludeCancellations
        )
            ->pluck(ProductAssignment::FIELD_COST)
            ->sum();

        return round($sum, 2);
    }

    /**
     * @param Company $company
     * @param Carbon $deliveredBefore
     * @param array $campaignIds
     * @param array $consumerProductIds
     * @param array $relations
     * @return Collection
     */
    public function getChargeableUninvoicedProductAssignments(
        Company $company,
        Carbon $deliveredBefore,
        array $campaignIds = [],
        array $consumerProductIds = [],
        array $relations = []
    ): Collection
    {
        return $this->getChargeableUninvoicedProductAssignmentsQuery(
            company           : $company,
            deliveredBefore   : $deliveredBefore,
            campaignIds       : $campaignIds,
            consumerProductIds: $consumerProductIds,
            relations         : $relations
        )->get();
    }

    /**
     * @param Company|null $company
     * @param Carbon|null $deliveredBefore
     * @param bool $excludeRejectable
     * @param BillingVersion|null $billingVersion
     * @param Carbon|null $deliveredAfter
     * @param array $campaignIds
     * @param array $consumerProductIds
     * @param array $relations
     * @return Builder
     */
    public function getChargeableUninvoicedProductAssignmentsQuery(
        ?Company $company = null,
        ?Carbon $deliveredBefore = null,
        bool $excludeRejectable = true,
        ?BillingVersion $billingVersion = BillingVersion::V2,
        ?Carbon $deliveredAfter = null,
        array $campaignIds = [],
        array $consumerProductIds = [],
        array $relations = []
    ): Builder
    {
        return $this->getProductAssignmentsForCompanyQuery(
            company                  : $company,
            affectRejectionPercentage: false,
            excludeRejections        : true,
            excludeCancellations     : true,
        )->select(ProductAssignment::TABLE . '.*')
            ->with($relations)
            ->whereHas(ProductAssignment::RELATION_CONSUMER_PRODUCT, function (Builder $query) use ($company) {
                $query->where(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_STATUS, ConsumerProduct::STATUS_ALLOCATED);
            })
            ->whereNotNull(ProductAssignment::FIELD_BUDGET_ID)
            ->when($excludeRejectable, function (Builder $builder) {
                $builder->where(ProductAssignment::FIELD_REJECTION_EXPIRY, '<', now());
            })
            ->whereDoesntHave(ProductAssignment::RELATION_ODIN_INVOICE_ITEM, function (Builder $builder) {
                $builder->whereHas(InvoiceItem::RELATION_INVOICE, function (Builder $builder) {
                    $builder->whereIn(Invoice::FIELD_STATUS, InvoiceStates::getInvoiceItemsLockedStates());
                });
            })
            ->when($billingVersion === BillingVersion::V2, function ($query) {
                $query->where(function ($query) {
                    $query->whereNull(DatabaseHelperService::database() . '.' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_LEGACY_ID)
                        ->orWhereExists(function ($query) {
                            $query->from(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentQuoteCompany::TABLE)
                                ->whereColumn(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::QUOTE_COMPANY_ID, ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_LEGACY_ID)
                                ->where(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::BILLING_VERSION, BillingVersion::V2);
                        });
                });
            })
            ->when($billingVersion === BillingVersion::V1, function ($query) use ($deliveredBefore) {
                $query->where(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::INVOICE_ITEM_ID, 0);
            })
            ->when($deliveredBefore, function ($query) use ($deliveredBefore) {
                $query->where(ProductAssignment::FIELD_DELIVERED_AT, '<', $deliveredBefore);
            })
            ->when($deliveredAfter, function ($query) use ($deliveredAfter) {
                $query->where(ProductAssignment::FIELD_DELIVERED_AT, '>', $deliveredAfter);
            })
            ->when(filled($consumerProductIds), function ($query) use ($consumerProductIds) {
                $query->whereIn(ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, $consumerProductIds);
            })
            ->when(filled($campaignIds), function ($query) use ($campaignIds) {
                $query->whereHas(ProductAssignment::RELATION_BUDGET, function ($query) use ($campaignIds) {
                    $query->whereHas(Budget::RELATION_BUDGET_CONTAINER, function ($query) use ($campaignIds) {
                        $query->whereIn(BudgetContainer::FIELD_CAMPAIGN_ID, $campaignIds);
                    });
                });
            });
    }

    /**
     * @param bool $chargeable
     * @param bool $delivered
     * @param Company|null $company
     * @param bool|null $affectRejectionPercentage
     * @param array|null $productIds
     * @param array|null $industryIds
     * @param array|null $industryServiceIds
     * @param int|null $startTimestamp
     * @param int|null $endTimestamp
     * @param bool|null $excludeRejections
     * @param bool|null $excludeCancellations
     * @return Builder
     */
    private function getProductAssignmentsForCompanyQuery(
        ?Company $company = null,
        ?bool $chargeable = true,
        ?bool $delivered = true,
        ?bool $affectRejectionPercentage = null,
        ?array $productIds = null,
        ?array $industryIds = null,
        ?array $industryServiceIds = null,
        ?int $startTimestamp = null,
        ?int $endTimestamp = null,
        ?bool $excludeRejections = null,
        ?bool $excludeCancellations = null
    ): Builder
    {
        $query = ProductAssignment::query();

        if ($company) {
            $query->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID, $company->id);
        }

        if ($chargeable) {
            $query->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CHARGEABLE, true);
        }

        if ($delivered) {
            $query->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED, true);
        }

        if ($excludeRejections) {
            $query->whereDoesntHave(ProductAssignment::RELATION_PRODUCT_REJECTIONS);
        }

        if ($excludeCancellations) {
            $query->whereDoesntHave(ProductAssignment::RELATION_PRODUCT_CANCELLATIONS);
        }

        if ($productIds || $industryIds || $industryServiceIds) {
            $query = $this->addProductIndustryOrServiceCriteria($query, $productIds, $industryIds, $industryServiceIds, $affectRejectionPercentage);
        } else if ($affectRejectionPercentage) {
            // This is if we want to see all product assignments that affect rejection percentage without specifying an affected product
            $query->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_AFFECT_REJECTION_PERCENTAGE, '!=', ProductAssignmentAffectRejectionPercentage::NO_AFFECT);
        }

        return $this->addTimespanCriteria($query, $startTimestamp, $endTimestamp);
    }

    /**
     * @param Builder $query
     * @param int|null $startTimestamp
     * @param int|null $endTimestamp
     * @return Builder
     */
    public function addTimespanCriteria(Builder $query, ?int $startTimestamp = null, ?int $endTimestamp = null): Builder
    {
        if ($startTimestamp) {
            $query->where(ProductAssignment::TABLE . '.' . ProductAssignment::CREATED_AT, '>=', Carbon::createFromTimestamp($startTimestamp));
        }

        if ($endTimestamp) {
            $query->where(ProductAssignment::TABLE . '.' . ProductAssignment::CREATED_AT, '<=', Carbon::createFromTimestamp($endTimestamp));
        }

        return $query;
    }

    /**
     * @inheritDoc
     */
    public function findByConsumerProductAndCompany(ConsumerProduct $consumerProduct, Company $company): ?ProductAssignment
    {
        /** @var ProductAssignment|null */
        return ProductAssignment::query()
            ->where(ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->{ConsumerProduct::FIELD_ID})
            ->where(ProductAssignment::FIELD_COMPANY_ID, $company->{Company::FIELD_ID})
            ->first();
    }

    /** @inheritDoc */
    public function findSoldProductAssignmentsByConsumerProduct(ConsumerProduct $consumerProduct, array $relations = []): Collection
    {
        $apptConsumerProductIds = AppointmentProcessingAllocation::query()
            ->where(AppointmentProcessingAllocation::FIELD_LEAD_CONSUMER_PRODUCT_ID, $consumerProduct->{ConsumerProduct::FIELD_ID})
            ->pluck(AppointmentProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID)
            ->toArray();

        $apptConsumerProductIds[] = $consumerProduct->{ConsumerProduct::FIELD_ID};

        return ProductAssignment::query()
            ->whereIn(ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, $apptConsumerProductIds)
            ->where(function ($where) {
                $where
                    ->where(function ($subWhere) {
                        $subWhere
                            ->where(ProductAssignment::FIELD_CHARGEABLE, true)
                            ->where(ProductAssignment::FIELD_DELIVERED, true);
                    })
                    ->orWhere(ProductAssignment::FIELD_PARENT_PRODUCT_ID, $this->getAppointmentProductId());
            })
            ->whereDoesntHave(ProductAssignment::RELATION_PRODUCT_CANCELLATIONS)
            ->whereDoesntHave(ProductAssignment::RELATION_PRODUCT_REJECTIONS)
            ->with($relations)
            ->get();
    }

    /**
     * @param int $companyId
     * @param int $consumerId
     *
     * @return bool
     * @throws BindingResolutionException
     */
    public function noChargeLead(int $companyId, int $consumerId): bool
    {
        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = ConsumerProduct::query()->where(ConsumerProduct::FIELD_CONSUMER_ID, $consumerId)->firstOrFail();

        /** @var ProductAssignment $productAssignment */
        $productAssignment = ProductAssignment::query()->where(ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->id)
            ->where(ProductAssignment::FIELD_COMPANY_ID, $companyId)
            ->first();

        if (!$productAssignment) return false;

        if (!$this->canNoCharge($productAssignment)) return false;

        $productAssignment->chargeable = false;
        $productAssignment->save();

        /** @var LegacyModule $legacyModule */
        $legacyModule = app(LegacyModule::class);

        $legacyModule->updateLegacyQuoteCompany($productAssignment);

        ProductAssignmentUpdatedEvent::dispatch($productAssignment->id);

        return true;
    }

    /**
     * @inheritDoc
     */
    public function getByConsumerProduct(ConsumerProduct $consumerProduct): Collection
    {
        /** @var Collection<ProductAssignment> $productAssignments */
        $productAssignments = ProductAssignment::query()
            ->where(ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->{ConsumerProduct::FIELD_ID})
            ->get();

        return $productAssignments;
    }

    /**
     * @param Budget $budget
     * @param Carbon $startDate
     * @param Carbon|null $endDate
     * @return Collection<int, ProductAssignment>
     */
    public function getChargeableAssignments(Budget $budget, Carbon $startDate, ?Carbon $endDate = null): Collection
    {
        if (!$endDate)
            $endDate = Carbon::now();

        return ProductAssignment::query()
            ->where(ProductAssignment::FIELD_BUDGET_ID, $budget->id)
            ->whereBetween(ProductAssignment::FIELD_DELIVERED_AT, [$startDate, $endDate])
            ->where(ProductAssignment::FIELD_CHARGEABLE, true)
            ->where(ProductAssignment::FIELD_DELIVERED, true)
            ->doesntHave(ProductAssignment::RELATION_PRODUCT_REJECTIONS)
            ->get();
    }

    /**
     * @param array $companyIds
     * @param int $lookBackHowManyHours
     * @return Collection<int, ProductAssignment>
     */
    public function getProductAssignmentsByCompanyIds(array $companyIds, int $lookBackHowManyHours): Collection
    {
        return ProductAssignment::query()
            ->whereIn(ProductAssignment::FIELD_COMPANY_ID, $companyIds)
            ->where(ProductAssignment::FIELD_DELIVERED_AT, '>=', Carbon::now()->subHours($lookBackHowManyHours))
            ->get();
    }

    /**
     * @param int $productAssignmentId
     * @return ProductAssignment
     */
    public function find(int $productAssignmentId): ProductAssignment
    {
        /** @var ProductAssignment */
        return ProductAssignment::query()->find($productAssignmentId);
    }

    /**
     * @param ConsumerProject $project
     * @return Collection<int, ProductAssignment>
     */
    public function getUndeliveredAssignmentsByConsumerProject(ConsumerProject $project): Collection
    {
        return ProductAssignment::query()
            ->where(ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, $project->leadConsumerProduct()->id)
            ->where(ProductAssignment::FIELD_DELIVERED, false)
            ->get();
    }


    /**
     * @param ProductAssignment $productAssignment
     *
     * @return bool
     */
    public function canNoCharge(ProductAssignment $productAssignment): bool
    {
        if (now() > $productAssignment->rejection_expiry) return false;
        if ($productAssignment->invoiceItem()->exists()) return false;

        return true;
    }

    /**
     * @param Consumer $consumer
     * @param bool|null $delivered
     *
     * @return Collection<ProductAssignment>
     */
    public function getAllAssignmentsForConsumer(Consumer $consumer, ?bool $delivered = null): Collection
    {
        $consumerProductIds = $consumer->consumerProducts()->select(ConsumerProduct::FIELD_ID)->pluck(ConsumerProduct::FIELD_ID)->toArray();

        return ProductAssignment::query()
            ->whereIn(ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, $consumerProductIds)
            ->when($delivered !== null, fn(Builder $q) => $q->where(ProductAssignment::FIELD_DELIVERED, $delivered))
            ->get();
    }

    public function getAllAssignmentsForConsumerIncludingClones(Consumer $consumer, ?bool $delivered = null): Collection
    {
        $parentConsumer = filled($consumer->cloned_from_id) ? $consumer->clonedFrom : $consumer;

        $assignments = collect();

        $clones = $parentConsumer->clones()->with(Consumer::RELATION_PRODUCT_ASSIGNMENTS)->get();

        $allConsumers = $clones->push($parentConsumer);

        foreach ($allConsumers as $consumer) {
            $consumerAssignments = $consumer
                ->productAssignments()
                ->when($delivered !== null, fn(Builder $q) => $q->where(ProductAssignment::FIELD_DELIVERED, $delivered))
                ->get();

            $assignments = $assignments->concat($consumerAssignments);
        }

        return $assignments;
    }

    /**
     * @param ProductAssignment $productAssignment
     * @param SaleType $saleType
     * @param float $cost
     *
     * @return bool
     */
    public function updateSaleTypeAndPrice(ProductAssignment $productAssignment, SaleType $saleType, float $cost): bool
    {
        $productAssignment->sale_type_id = $saleType->id;
        $productAssignment->cost = $cost;

        return $productAssignment->save();
    }

    /**
     * @param array $legacyQuoteIds
     * @param array $columns
     * @param bool $futureCampaignsOnly
     * @return Collection<ProductAssignment>
     */
    public function getProductAssignmentsForLegacyQuotes(array $legacyQuoteIds, array $columns = ['*'], bool $futureCampaignsOnly = true): Collection
    {
        $query = ProductAssignment::query()
            ->leftJoin(
                DatabaseHelperService::readOnlyDatabase() . '.' . EloquentQuoteCompany::TABLE,
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_LEGACY_ID,
                EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::COMPANY_ID
            )
            ->whereIntegerInRaw(EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::QUOTE_ID, $legacyQuoteIds);

        if ($futureCampaignsOnly) {
            $query->whereNot(ProductAssignment::FIELD_BUDGET_ID, 0);
        }

        return $query->get($columns);
    }

    /**
     * @param array $consumerProductIds
     * @param bool $futureCampaignsOnly
     * @return Collection<ProductAssignment>
     */
    public function getProductAssignmentsForConsumerProducts(array $consumerProductIds, bool $futureCampaignsOnly = true): Collection
    {
        $query = ProductAssignment::query()
            ->leftJoin(ConsumerProduct::TABLE, ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID)
            ->whereIntegerInRaw(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID, $consumerProductIds);

        if ($futureCampaignsOnly) {
            $query->whereNot(ProductAssignment::FIELD_BUDGET_ID, 0);
        }

        return $query->get([ProductAssignment::TABLE . '.*']);
    }

    public function getCompanyUninvoicedProductAssignmentsValue(
        ?Company $company,
        bool $excludeRejectable = true
    )
    {
        return $this->getChargeableUninvoicedProductAssignmentsQuery(
            company          : $company,
            excludeRejectable: $excludeRejectable,
        )->select([DB::raw('SUM(' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COST . ') as to_be_invoiced')])
            ->groupBy(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID)
            ->value('to_be_invoiced');
    }
}
