<?php

namespace App\Repositories\Odin;

use App\Models\Odin\Company;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductCampaign;
use App\Models\TestProduct;
use App\Models\TestProductCommunication;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use phpDocumentor\Reflection\Types\Boolean;

class TestLeadApiRepository
{
    const PER_PAGE = 'per_page';
    const PAGE = 'page';
    const COMPANY_NAME = 'company_name';
    const CAMPAIGN_NAME = 'campaign_name';
    const INDUSTRY_TYPE = 'industry_types';
    const SERVICE_TYPES = 'service_types';
    const FILTER_BY_USER = 'product_author';

    const TEST_LEAD_EVENT_FIELD_EVENT = 'event';
    const TEST_LEAD_EVENT_FIELD_DATE = 'date';
    const TEST_LEAD_EVENT_CREATED_AT = 'created_at';
    const TEST_LEAD_EVENT_DELIVERED_AT = 'delivered_at';
    const TEST_LEAD_EVENT_REVEAL_AT = 'reveal_at';
    const TEST_LEAD_EVENT_EXPIRE_AT = 'expire_at';

    public function __construct()
    {
    }

    /**
     * @param $filters
     * @return LengthAwarePaginator|Collection
     */
    public function getTestProducts($filters): LengthAwarePaginator|Collection
    {
        $query = TestProduct::query();

        if (isset($filters[self::COMPANY_NAME])) {
            $query->whereHas(TestProduct::RELATION_COMPANY, function ($query) use ($filters) {
                if (ctype_digit($filters[self::COMPANY_NAME])) {
                    $companyId = (int)$filters[self::COMPANY_NAME];
                    $query->where(Company::FIELD_ID, $companyId);
                } else {
                    $query->where(Company::FIELD_NAME, 'like', '%' . $filters[self::COMPANY_NAME] . '%');
                }
            });
        }

        if (isset($filters[self::CAMPAIGN_NAME])) {
            $query->whereHas(TestProduct::RELATION_PRODUCT_CAMPAIGN, function ($query) use ($filters) {
                $query->where(ProductCampaign::FIELD_NAME, 'like', '%' . $filters[self::CAMPAIGN_NAME] . '%');
            });
        }

        if (isset($filters[self::INDUSTRY_TYPE])) {
            $query->whereHas(TestProduct::RELATION_PRODUCT_CAMPAIGN, function ($query) use ($filters) {
                $query->whereHas(ProductCampaign::RELATION_SERVICE, function ($query) use ($filters) {
                    $query->whereHas(IndustryService::RELATION_INDUSTRY, function ($query) use ($filters) {
                        $query->whereIn(IndustryService::FIELD_ID, $filters[self::INDUSTRY_TYPE]);
                    });
                });
            });
        }

        if (isset($filters[self::SERVICE_TYPES])) {
            $query->whereHas(TestProduct::RELATION_PRODUCT_CAMPAIGN, function ($query) use ($filters) {
                $query->whereHas(ProductCampaign::RELATION_SERVICE, function ($query) use ($filters) {
                    $query->whereIn(IndustryService::FIELD_ID, $filters[self::SERVICE_TYPES]);
                });
            });
        }

        if (isset($filters[self::FILTER_BY_USER])) {
            $query->whereIn(TestProduct::FIELD_CREATED_BY_ID, $filters[self::FILTER_BY_USER]);
        }

        return isset($filters['no_pagination']) && +$filters['no_pagination'] === 1
            ? $query->latest()->get()
            : $query->latest()->paginate($filters[self::PER_PAGE]);
    }

    /**
     * @param int $testLeadId
     * @return ?TestProduct
     */
    public function getTestLead(int $testLeadId): ?Model
    {
        return TestProduct::query()->find($testLeadId);
    }

    /**
     * @param TestProduct $product
     * @param string|null $sortBy
     * @param string $sortDesc
     * @return Collection<TestProductCommunication>
     */
    public function getTestLeadCommunications(
        TestProduct $product,
        ?string     $sortBy = TestProduct::CREATED_AT,
        string      $sortDesc = "DESC"
    ): Collection
    {
        return $product->communications()
            ->orderBy($sortBy ?? TestProduct::CREATED_AT, $sortDesc)
            ->get();
    }

    /**
     * @param int $id
     * @param int $fraud_score
     * @return void
     */
    public function updateFraudScore(int $id, int $fraud_score): void
    {
        TestProductCommunication::query()
            ->where(TestProductCommunication::FIELD_ID, $id)
            ->update(['fraud_score' => $fraud_score]);
    }

    /**
     * Helper function to format an event for a test lead.
     *
     * @param string $eventName
     * @param Carbon $date
     * @return string[]
     */
    protected function formatTestLeadEvent(string $eventName, Carbon $date): array
    {
        return [self::TEST_LEAD_EVENT_FIELD_EVENT => $eventName, self::TEST_LEAD_EVENT_FIELD_DATE => $date];
    }

    /**
     * Finds, and formats the generic events of a test product.
     *
     * @param TestProduct $product
     * @return Collection
     */
    public function getTestLeadEvents(TestProduct $product): Collection
    {
        return collect(
            [
                $this->formatTestLeadEvent(self::TEST_LEAD_EVENT_CREATED_AT, $product->created_at),
                $product->delivered_at ? $this->formatTestLeadEvent(self::TEST_LEAD_EVENT_DELIVERED_AT, $product->delivered_at) : null,
                $product->reveal_at->isPast() ? $this->formatTestLeadEvent(self::TEST_LEAD_EVENT_REVEAL_AT, $product->reveal_at) : null,
                $product->expire_at->isPast() ? $this->formatTestLeadEvent(self::TEST_LEAD_EVENT_EXPIRE_AT, $product->expire_at) : null,
            ]
        )->filter();
    }

    /**
     * @return array
     */
    public function getTestLeadTableFilters(): array
    {
        $industry           = Industry::query()->select(Industry::FIELD_NAME, Industry::FIELD_ID)->get();
        $service            = IndustryService::query()->select(IndustryService::FIELD_ID, IndustryService::FIELD_NAME)->get();
        $generated_by_users = TestProduct::query()
            ->whereNotNull(TestProduct::FIELD_CREATED_BY_ID)
            ->with([TestProduct::RELATION_CREATED_BY])
            ->select(TestProduct::FIELD_CREATED_BY_ID)
            ->get()
            ->unique(TestProduct::FIELD_CREATED_BY_ID)
            ->map(function ($item) {
                return [
                    User::FIELD_ID   => $item->created_by_id,
                    User::FIELD_NAME => $item->createdBy->name
                ];
            })->values()
            ->toArray();

        return [
            "industries"         => $industry,
            "services"           => $service,
            "generated_by_users" => $generated_by_users
        ];
    }

}
