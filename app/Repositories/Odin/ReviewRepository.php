<?php

namespace App\Repositories\Odin;

use App\Contracts\Repositories\ReviewRepositoryContract;
use App\Models\Odin\CompanyReview;
use App\Models\Odin\Consumer;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class ReviewRepository implements ReviewRepositoryContract
{

    public function getRelatedReviewsByUserPaginated(int $userId, string $userEmail, int $limit = 5, array $relations = []): LengthAwarePaginator
    {
        // TODO: Implement getRelatedReviewsByUserPaginated() method.
    }

    public function getRelatedReviewsByUserQuery(int $userId, string $userEmail, array $relations = []): Builder
    {
        // TODO: Implement getRelatedReviewsByUserQuery() method.
    }

    public function getCompaniesReviewCounts(array $companyIdType = []): array
    {
        // TODO: Implement getCompaniesReviewCounts() method.
    }

    /**
     * @param int $companyId
     *
     * @return CompanyReview|null
     */
    public function getLastReview(int $companyId): ?CompanyReview
    {
        /** @var CompanyReview|null $review */
        $review = CompanyReview::query()
            ->where(CompanyReview::FIELD_COMPANY_ID, $companyId)
            ->where(CompanyReview::FIELD_STATUS, CompanyReview::STATUS_APPROVED)
            ->latest()
            ->first();

        return $review;
    }

    /**
     * Search for reviews by Consumer id OR matching email OR matching ip_address
     *
     * @param int $consumerId
     * @param string $consumerProductIpAddress
     * @param int|null $limit
     * @return Builder
     */
    public function getRelatedReviewsByConsumerOrIpAddress(int $consumerId, string $consumerProductIpAddress, int|null $limit = null): Builder
    {
        $consumer = Consumer::query()->findOrFail($consumerId);

        return CompanyReview::query()
            ->where(CompanyReview::FIELD_CONSUMER_ID, $consumerId)
            ->orWhere(CompanyReview::FIELD_EMAIL, $consumer->{Consumer::FIELD_EMAIL})
            ->orWhere(fn(Builder $query) => $query
                ->when($consumerProductIpAddress)
                ->where(CompanyReview::FIELD_IP_ADDRESS, $consumerProductIpAddress))
            ->latest()
            ->limit($limit);
    }
}
