<?php

namespace App\Repositories\Odin;


use App\Exceptions\CustomValidationException;
use App\Models\Odin\CompanyQualityScoreRule;
use Illuminate\Database\Eloquent\Collection;

class CompanyQualityScoreRuleRepository
{

    /**
     * Get all company quality score rules
     * @return Collection
     */
    public function getAll(): Collection
    {
        return CompanyQualityScoreRule::all();
    }

    /**
     * Create a company quality score ruleset
     * @param array $payload
     * @return CompanyQualityScoreRule
     */
    public function store(array $payload): CompanyQualityScoreRule
    {
        $companyQualityScoreRule = new CompanyQualityScoreRule();

        $companyQualityScoreRule->fill($payload);
        $companyQualityScoreRule->save();

        return $companyQualityScoreRule;
    }

    /**
     * Update a company quality score ruleset
     * @param CompanyQualityScoreRule $companyQualityScoreRule
     * @param array $payload
     * @return bool
     */
    public function update(CompanyQualityScoreRule $companyQualityScoreRule, array $payload): bool
    {
        return $companyQualityScoreRule->update($payload);
    }


    /**
     * Delete a company quality score ruleset
     * @param CompanyQualityScoreRule $companyQualityScoreRule
     * @return bool
     * @throws \Exception
     */
    public function delete(CompanyQualityScoreRule $companyQualityScoreRule): bool
    {
        return $companyQualityScoreRule->delete();
    }
}
