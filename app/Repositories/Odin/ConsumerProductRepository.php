<?php

namespace App\Repositories\Odin;

use App\Contracts\Repositories\Odin\ConsumerProductRepositoryContract;
use App\Enums\Odin\VerificationServiceTypes;
use App\Jobs\Odin\ConsumerProductVerificationJob;
use App\Models\LeadProcessingReservedLead;
use App\Models\Odin\Address;
use App\Models\Odin\ConsumerProductTcpaRecord;
use App\Models\Odin\Industry;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use App\Models\User;
use App\Services\HelperService;
use App\Services\Odin\ConsumerProductVerification\ConsumerProductVerificationResult;
use App\Services\Odin\ConsumerProductVerification\ConsumerProductVerificationServiceFactory;
use App\Services\Odin\ConsumerProductVerification\IPQualityScoreResult;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductData;
use App\Enums\Odin\Product as ProductEnum;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Exception;
use InvalidArgumentException;

class ConsumerProductRepository implements ConsumerProductRepositoryContract
{
    const PRODUCT_DATA_KEY_VERIFICATION         = 'verification';
    const PRODUCT_DATA_KEY_IP_QUALITY_SCORE     = 'ip_quality';
    const PRODUCT_DATA_KEY_STATUS_REASON        = 'status_reason';
    const PRODUCT_DATA_KEY_COMMENTS             = 'comments';
    const PRODUCT_DATA_KEY_BEST_TIME_TO_CONTACT = 'best_time_to_call';

    /**
     * @param ConsumerProductVerificationServiceFactory $serviceFactory
     */
    public function __construct(
        protected ConsumerProductVerificationServiceFactory $serviceFactory,
    ) {}

    /**
     * @param Consumer $consumer
     * @param array $data
     *
     * @return ConsumerProduct
     */
    public function createConsumerProduct(Consumer $consumer, array $data): ConsumerProduct
    {
        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = $consumer->consumerProducts()->create($data);

        return $consumerProduct;
    }

    /**
     * @param Consumer $consumer
     * @param int $service_product_id
     * @param array $data
     *
     * @return ConsumerProduct
     */
    public function updateOrCreateConsumerProduct(Consumer $consumer, int $service_product_id, array $data): ConsumerProduct
    {
        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = $consumer->consumerProducts()->updateOrCreate([
            ConsumerProduct::FIELD_SERVICE_PRODUCT_ID => $service_product_id
        ], $data);

        $includesTcpa = $data[ConsumerProductTcpaRecord::FIELD_TCPA_ID] ?? false;
        if ($includesTcpa) {
            $newTcpaRecord = ConsumerProductTcpaRecord::query()
                ->create([
                    ConsumerProductTcpaRecord::FIELD_TCPA_ID => $data[ConsumerProductTcpaRecord::FIELD_TCPA_ID],
                    ConsumerProductTcpaRecord::FIELD_TCPA_SERVICE_TYPE => "WatchDog"
                ]);
            $consumerProduct->update([ConsumerProduct::FIELD_CONSUMER_PRODUCT_TCPA_RECORD_ID => $newTcpaRecord->id]);
        }

        return $consumerProduct;
    }

    /**
     * @param int $consumerProductId
     * @param array $relations
     * @return ConsumerProduct|Model
     */
    public function findOrFail(int $consumerProductId, array $relations = []): ConsumerProduct|Model
    {
        return ConsumerProduct::query()->with($relations)->findOrFail($consumerProductId);
    }

    public function findByProductAssignmentIdOrFail(int $productAssignmentId)
    {
        return ConsumerProduct::query()
            ->whereHas(ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT, function (Builder $builder) use ($productAssignmentId) {
                $builder->where(ProductAssignment::FIELD_ID, $productAssignmentId);
            })
            ->firstOrFail();

    }

    /**
     * Create a ConsumerProduct's ConsumerProductData
     *
     * @param ConsumerProduct $consumerProduct
     * @param array $data
     *
     * @return ConsumerProductData
     */
    public function createConsumerProductData(ConsumerProduct $consumerProduct, array $data): ConsumerProductData
    {
        /** @var ConsumerProductData $consumerProductData */
        $consumerProductData = ConsumerProductData::query()->create([
            ConsumerProductData::FIELD_PAYLOAD => $data
        ]);

        $consumerProduct->consumer_product_data_id = $consumerProductData->id;
        $consumerProduct->save();

        return $consumerProductData;
    }

    /**
     * Handles updating consumer product against the requested data set.
     *
     * @param ConsumerProduct $consumerProduct
     * @param array $data
     * @param bool $refreshVerificationDetails
     * @return bool
     * @throws Exception
     */
    public function updateConsumerProductModel(ConsumerProduct $consumerProduct, array $data, bool $refreshVerificationDetails = false): bool
    {
        if (count($data) === 0) return true;

        $productStatus = $consumerProduct->update($data);

        if($refreshVerificationDetails) ConsumerProductVerificationJob::dispatch($consumerProduct->id);

        return $productStatus;
    }

    /**
     * Update the payload on a ConsumerProduct's associated ConsumerProductData
     *
     * @param ConsumerProduct $consumerProduct
     * @param array $updateData
     * @return bool
     */
    public function updateConsumerProductData(ConsumerProduct $consumerProduct, array $updateData): bool
    {
        if (count($updateData) === 0) return true;

        /** @var ConsumerProductData $consumerProductData */
        $consumerProductData = $consumerProduct->consumerProductData()->firstOrCreate([]);
        $consumerProductData->{ConsumerProductData::FIELD_PAYLOAD} = $consumerProductData->payload ? array_merge($consumerProductData->payload, $updateData) : $updateData;
        $consumerProductData->save();

        $consumerProduct->consumer_product_data_id = $consumerProductData->id;
        return $consumerProduct->save();
    }

    /**
     * @inheritDoc
     */
    public function getConsumerProductAsLead(int $consumerProduct): ConsumerProduct|Model
    {
        /** @var Builder $consumerProduct */
        $consumerProduct = ConsumerProduct::query()
            ->where(ConsumerProduct::FIELD_ID, $consumerProduct)
            ->whereHas(ConsumerProduct::RELATION_SERVICE_PRODUCT, function(Builder $query) {
                $query->whereHas(ServiceProduct::RELATION_PRODUCT, function(Builder $query) {
                    $query->where(Product::FIELD_NAME, ProductEnum::LEAD->value);
                });
            });

        return $consumerProduct->firstOrFail();
    }

    /**
     * @param int $consumerProductId
     * @return ConsumerProduct|Model|null
     */
    public function getConsumerProductById(int $consumerProductId): ConsumerProduct|Model|null
    {
        return ConsumerProduct::query()->where(ConsumerProduct::FIELD_ID, $consumerProductId)->first();
    }

    /**
     * @deprecated
     * @param int $leadId
     * @param int $status
     * @return void
     */
    public function updateConsumerProductStatusByLegacyLeadIdIfExists(int $leadId, int $status): void
    {
            $consumerProduct = $this->getConsumerProductByLegacyLeadId($leadId);

            if($consumerProduct) {
                $this->updateConsumerProductStatus($consumerProduct, $status);
            }
    }

    /**
     * @inheritDoc
     */
    public function getConsumerProductVerificationDetails(ConsumerProduct $consumerProduct): ConsumerProductVerificationResult
    {
        /** @var ConsumerProductData $consumerProductData */
        $consumerProductData = ConsumerProductData::query()->find($consumerProduct->{ConsumerProduct::FIELD_CONSUMER_PRODUCT_DATA_ID});
        if(empty($consumerProductData)) return $this->createConsumerProductVerificationDetails($consumerProduct);

        $payload = $consumerProductData->toArray()[ConsumerProductData::FIELD_PAYLOAD];

        if(empty($payload) || !isset($payload[self::PRODUCT_DATA_KEY_VERIFICATION]) || !$payload[self::PRODUCT_DATA_KEY_VERIFICATION])
            return $this->createConsumerProductVerificationDetails($consumerProduct);

        return new ConsumerProductVerificationResult($payload[self::PRODUCT_DATA_KEY_VERIFICATION]);
    }

    /**
     * @inheritDoc
     */
    public function createConsumerProductVerificationDetails(ConsumerProduct $consumerProduct): ConsumerProductVerificationResult
    {
        /** @var ConsumerProductVerificationResult $verifiedData */
        $verifiedData = $this->serviceFactory
                             ->makeService(VerificationServiceTypes::IDENTITY_CHECK->value)
                             ->verifyConsumerProduct($consumerProduct);

        $this->updateConsumerProductData($consumerProduct, [ConsumerProductRepository::PRODUCT_DATA_KEY_VERIFICATION => $verifiedData]);

        return $verifiedData;
    }

    /**
     * @inheritDoc
     *
     */
    public function getIPQualityScore(ConsumerProduct $consumerProduct): ?IPQualityScoreResult
    {
        /** @var ConsumerProductData $consumerProductData */
        $consumerProductData = ConsumerProductData::query()->find($consumerProduct->{ConsumerProduct::FIELD_CONSUMER_PRODUCT_DATA_ID});
        if(empty($consumerProductData)) return $this->createIPQualityScore($consumerProduct);

        $payload = $consumerProductData->toArray()[ConsumerProductData::FIELD_PAYLOAD];

        if(empty($payload) || !isset($payload[self::PRODUCT_DATA_KEY_IP_QUALITY_SCORE]) || !$payload[self::PRODUCT_DATA_KEY_IP_QUALITY_SCORE])
            return $this->createIPQualityScore($consumerProduct);

        return new IPQualityScoreResult($payload[self::PRODUCT_DATA_KEY_IP_QUALITY_SCORE]);
    }

    /**
     * @inheritDoc
     */
    public function createIPQualityScore(ConsumerProduct $consumerProduct): ?IPQualityScoreResult
    {
        try {
            /** @var IPQualityScoreResult $ipScore */
            $ipScore = $this->serviceFactory
                ->makeService(VerificationServiceTypes::IP_QUALITY_SCORE->value)
                ->verifyConsumerProduct($consumerProduct);

            if(empty($ipScore) || !property_exists($ipScore, 'transaction')) {
                logger()->error("Couldn't fetch verification details for the consumer product: {$consumerProduct->id}. The service responded as: " . json_encode($ipScore->toArray()));
                return null;
            }

            $this->updateConsumerProductData($consumerProduct, [ConsumerProductRepository::PRODUCT_DATA_KEY_IP_QUALITY_SCORE => $ipScore]);

            return $ipScore;

        } catch(Exception $e) {
            logger()->error("Couldn't fetch verification details for the consumer product: {$consumerProduct->id} due to the exception: `{$e->getMessage()}`");
            return null;
        }
    }

    /**
     * Update ConsumerProduct model and ConsumerProductData by id
     *
     * @param int $consumerProductId
     * @param Collection $data
     * @param array $modelKeys - keys to update directly on the model
     * @param array $payloadKeys - keys to insert into the Data model's payload
     * @return bool
     * @throws Exception
     */
    public function updateConsumerProductModelAndPayloadById(int $consumerProductId, Collection $data, array $modelKeys, array $payloadKeys): bool
    {
        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = $this->findOrFail($consumerProductId);

        $splitData = $data->reduce(function($output, $value, $key) use (&$modelKeys, &$payloadKeys) {
            if (in_array($key, $modelKeys)) $output['model'][$key] = $value;
            else if (in_array($key, $payloadKeys)) $output['payload'][$key] = $value;
            return $output;
        }  , [ 'model' => [], 'payload' => [] ]);

        return $this->updateConsumerProductModel($consumerProduct, $splitData['model'])
            && $this->updateConsumerProductData($consumerProduct, $splitData['payload']);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param int $status
     *
     * @return bool
     */
    public function updateConsumerProductStatus(ConsumerProduct $consumerProduct, int $status): bool
    {
        if (!in_array($status, ConsumerProduct::STATUSES)) throw new InvalidArgumentException('Invalid consumer product status');

        $consumerProduct->status = $status;

        return $consumerProduct->save();
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @return bool
     */
    public function markConsumerProductGoodToSell(ConsumerProduct $consumerProduct): bool
    {
        $consumerProduct->good_to_sell = true;

        return $consumerProduct->save();
    }

    /**
     * @param String $phone
     * @return ConsumerProduct|Model|null
     */
    public function getConsumerProductFromPhoneNumber(string $phone) : ConsumerProduct|Model|null
    {
        $phoneNumber = HelperService::formatUSPhoneNumber($phone);

        if(is_null($phoneNumber)) {
            return null;
        }

        return ConsumerProduct::query()
            ->whereHas(ConsumerProduct::RELATION_CONSUMER, fn ($query) => $query->where(Consumer::FIELD_FORMATTED_PHONE, $phoneNumber))
            ->with(ConsumerProduct::RELATION_CONSUMER)
            ->orderByDesc(ConsumerProduct::FIELD_ID)
            ->first();
    }

    /**
     * @deprecated
     * @param int $legacyLeadId
     * @return ConsumerProduct|Model|null
     */
    public function getConsumerProductByLegacyLeadId(int $legacyLeadId): ConsumerProduct|Model|null
    {
        return ConsumerProduct::query()
            ->select(ConsumerProduct::TABLE.'.*')
            ->leftJoin(Consumer::TABLE,ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CONSUMER_ID, Consumer::TABLE.'.'.Consumer::FIELD_ID)
            ->where(Consumer::TABLE.'.'.Consumer::FIELD_LEGACY_ID, $legacyLeadId)
            ->first();
    }

    /**
     * Get all consumers products by consumer phone
     * @param string $phone
     * @return Collection
     */
    public function getConsumerInfoByPhone(string $phone) : Collection
    {
        $phoneNumber = HelperService::formatUSPhoneNumber($phone);

        if(is_null($phoneNumber)) {
            return collect();
        }

        return ConsumerProduct::query()
            ->whereHas(ConsumerProduct::RELATION_CONSUMER, function ($query) use ($phoneNumber) {
                $query->where(Consumer::FIELD_FORMATTED_PHONE, $phoneNumber);
            })
            ->with([
                ConsumerProduct::RELATION_CONSUMER,
                ConsumerProduct::RELATION_SERVICE_PRODUCT,
                ConsumerProduct::RELATION_SERVICE_PRODUCT . '.' . ServiceProduct::RELATION_PRODUCT
            ])
            ->whereHas(ConsumerProduct::RELATION_SERVICE_PRODUCT, function(Builder $query) {
                $query->whereHas(ServiceProduct::RELATION_PRODUCT, function(Builder $query) {
                    $query->where(Product::FIELD_NAME, ProductEnum::LEAD->value);
                });
            })
            ->get();
    }

    /**
     * @param Consumer $consumer
     * @return void
     */
    public function setDefaultContactRequestsForMultiIndustryLead(Consumer $consumer): void
    {
        $consumerProduct = $consumer->consumerProducts->first();

        if ($consumer->max_contact_requests > 0 && $consumerProduct->contact_requests > 0) return;

        if ($consumerProduct->industryService->industry->{Industry::FIELD_SLUG} === IndustryEnum::SOLAR->getSlug()) {
            $consumer->max_contact_requests    = ConsumerProduct::DEFAULT_CONTACT_REQUEST_FOR_SOLAR_LEAD;
            $consumerProduct->contact_requests = ConsumerProduct::DEFAULT_CONTACT_REQUEST_FOR_SOLAR_LEAD;
        } else {
            $consumer->max_contact_requests    = ConsumerProduct::DEFAULT_CONTACT_REQUEST_FOR_MULTI_INDUSTRY_LEAD;
            $consumerProduct->contact_requests = ConsumerProduct::DEFAULT_CONTACT_REQUEST_FOR_MULTI_INDUSTRY_LEAD;
        }

        $consumer->save();
        $consumerProduct->save();
    }

    /** @inheritDoc */
    public function searchConsumerProducts(string $query): Collection
    {
        $likeQuery = "%".preg_replace("/\s/", "%", $query)."%";

        return ConsumerProduct::query()
            ->where(function($where) use ($query, $likeQuery) {
                $where
                    ->where(ConsumerProduct::FIELD_ID, $query)
                    ->orWhereHas(ConsumerProduct::RELATION_CONSUMER, function($has) use ($query, $likeQuery) {
                        $has
                            ->where(Consumer::FIELD_EMAIL, 'LIKE', $likeQuery)
                            ->orWhere(Consumer::FIELD_LEGACY_ID, $query)
                            ->orWhere(Consumer::FIELD_FIRST_NAME, 'LIKE', $likeQuery)
                            ->orWhere(Consumer::FIELD_LAST_NAME, 'LIKE', $likeQuery)
                            ->orWhereRaw("CONCAT(`".Consumer::FIELD_FIRST_NAME."`, ' ', `".Consumer::FIELD_LAST_NAME."`) LIKE '{$likeQuery}'");
                    });
            })
            ->where(ConsumerProduct::CREATED_AT, '>=', Carbon::now('UTC')->subDays(30))
            ->get();
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param array $relations
     * @return Builder
     */
    public function getDuplicateConsumerProductsQuery(ConsumerProduct $consumerProduct, array $relations = []): Builder
    {
        $consumerEmail = $consumerProduct->{ConsumerProduct::RELATION_CONSUMER}?->{Consumer::FIELD_EMAIL};
        $consumerPhone = $consumerProduct->{ConsumerProduct::RELATION_CONSUMER}?->{Consumer::FIELD_FORMATTED_PHONE};
        $ipAddress     = $consumerProduct->{ConsumerProduct::FIELD_IP_ADDRESS};

        $minimumCreatedAt = Carbon::now('UTC')->subDays(90);

        // find all Consumers with a matching email address or phone number, as these columns are not unique
        $matchingConsumers = Consumer::query()
            ->select(Consumer::FIELD_ID)
            ->where(function (Builder $query) use ($consumerEmail, $consumerPhone) {
                $query->where(Consumer::FIELD_EMAIL, $consumerEmail);

                if ($consumerPhone) {
                    $query->orWhere(Consumer::FIELD_FORMATTED_PHONE, $consumerPhone);
                }
            })
            ->where(Consumer::CREATED_AT, '>=', $minimumCreatedAt)
            ->pluck(Consumer::FIELD_ID)->all();

        $filtered = array_diff($matchingConsumers, [$consumerProduct->{ConsumerProduct::FIELD_CONSUMER_ID}]);

        // find all ConsumerProducts with a matching ip_address, or with a related consumer with a matching email address or phone number
        $query = ConsumerProduct::query()
            ->select(ConsumerProduct::FIELD_ID, ConsumerProduct::FIELD_CONSUMER_ID, ConsumerProduct::FIELD_STATUS, Model::CREATED_AT)
            ->with($relations)
            ->whereIn(ConsumerProduct::FIELD_CONSUMER_ID, $filtered);

        if($ipAddress) {
            $query->orWhere(function (Builder $query) use ($ipAddress, $consumerProduct, $minimumCreatedAt) {
                $query->where(ConsumerProduct::FIELD_CONSUMER_ID, '!=', $consumerProduct->{ConsumerProduct::FIELD_CONSUMER_ID})
                    ->where(ConsumerProduct::CREATED_AT, '>=', $minimumCreatedAt)
                    ->where(ConsumerProduct::FIELD_IP_ADDRESS, $ipAddress);
            });
        }

        return $query;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param int $timeframeInDays
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function getDuplicateConsumerProductQueryWithJoin(ConsumerProduct $consumerProduct, int $timeframeInDays): \Illuminate\Database\Eloquent\Builder
    {
        $base = ConsumerProduct::query()
            ->select([
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID . ' as consumer_product_id'
            ])
            ->join(
                Consumer::TABLE,
                Consumer::TABLE  . '.' . Consumer::FIELD_ID,
                '=',
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_CONSUMER_ID
            )
            ->join(
                Address::TABLE,
                Address::TABLE . '.' . Address::FIELD_ID,
                '=',
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ADDRESS_ID
            )
            ->join(
                ServiceProduct::TABLE,
                ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_ID,
                '=',
                ConsumerProduct::FIELD_SERVICE_PRODUCT_ID
            )
            ->where(ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_STATUS, '!=', ConsumerProduct::STATUS_CANCELLED)
            ->where(Consumer::TABLE  . '.' . Consumer::FIELD_ID, '!=', $consumerProduct->consumer->id)
            ->whereNotIn(Consumer::TABLE  . '.' . Consumer::FIELD_CLASSIFICATION, [Consumer::CLASSIFICATION_EMAIL_ONLY])
            ->where(ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $consumerProduct->serviceProduct->industry_service_id)
            ->where(ConsumerProduct::TABLE . '.' . ConsumerProduct::CREATED_AT, '>=', now()->subDays($timeframeInDays));

        $emailQuery = $base->clone()
            ->where(Consumer::TABLE  . '.' . Consumer::FIELD_EMAIL, $consumerProduct->consumer->email);

        if ($consumerProduct->consumer->formatted_phone) {
            $phoneQuery = $base->clone()->where(Consumer::TABLE . '.' . Consumer::FIELD_FORMATTED_PHONE, $consumerProduct->consumer->formatted_phone);
            $emailQuery->union($phoneQuery);
        }

        if ($consumerProduct->ip_address) {
            $addressQuery = $base->clone()->where(Consumer::TABLE . '.' . Consumer::FIELD_FORMATTED_PHONE, $consumerProduct->consumer->formatted_phone);
            $emailQuery->union($addressQuery);
        }

        return ConsumerProduct::query()
            ->joinSub($emailQuery, 'sub', 'sub.consumer_product_id', ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID);
    }

    /**
     * @param int $consumerProductId
     *
     * @return User|null
     */
    public function getReservedByUser(int $consumerProductId): ?User
    {
        /** @var LeadProcessingReservedLead|null $reserved */
        $reserved = LeadProcessingReservedLead::query()
            ->where(LeadProcessingReservedLead::FIELD_CONSUMER_PRODUCT_ID, $consumerProductId)
            ->where(LeadProcessingReservedLead::FIELD_PROCESSOR_ID, '!=', LeadProcessingReservedLead::SYSTEM_ID)
            ->first();

        return $reserved?->processor?->user;
    }
}
