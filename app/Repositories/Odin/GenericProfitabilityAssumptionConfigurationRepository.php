<?php

namespace App\Repositories\Odin;

use App\Models\Odin\Company;
use App\Models\Odin\GenericProfitabilityAssumptionConfiguration;
use App\Models\Odin\IndustryService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class GenericProfitabilityAssumptionConfigurationRepository
{
    /**
     * @param IndustryServiceRepository $industryServiceRepository
     */
    public function __construct(
        protected IndustryServiceRepository $industryServiceRepository,
    ) {}

    /**
     * @param int $profitabilityAssumptionConfigurationId
     * @return GenericProfitabilityAssumptionConfiguration
     */
    public function findOrFail(int $profitabilityAssumptionConfigurationId): GenericProfitabilityAssumptionConfiguration
    {
        /** @var GenericProfitabilityAssumptionConfiguration $profitabilityAssumptionConfiguration */
        $profitabilityAssumptionConfiguration = GenericProfitabilityAssumptionConfiguration::query()
            ->findOrFail($profitabilityAssumptionConfigurationId);
        return $profitabilityAssumptionConfiguration;
    }

    /**
     * @param int $companyId
     * @param IndustryService $service
     * @return GenericProfitabilityAssumptionConfiguration|null
     */
    public function findByCompanyAndIndustryService(int $companyId, IndustryService $service): ?GenericProfitabilityAssumptionConfiguration
    {
        /** @var GenericProfitabilityAssumptionConfiguration $profitabilityConfiguration */
        $profitabilityConfiguration = GenericProfitabilityAssumptionConfiguration::query()
            ->where(GenericProfitabilityAssumptionConfiguration::FIELD_COMPANY_ID, $companyId)
            ->where(GenericProfitabilityAssumptionConfiguration::FIELD_INDUSTRY_SERVICE_ID, $service->{IndustryService::FIELD_ID})
            ->first();
        return $profitabilityConfiguration ?? $this->getDefaultProfitabilityConfiguration($service->id);
    }

    /**
     * @param int $companyId
     * @return Collection<GenericProfitabilityAssumptionConfiguration>
     */
    public function getGlobalProfitabilityConfigurations(int $companyId): Collection
    {
        /** @var Company $company */
        $company = Company::query()->findOrFail($companyId);
        $serviceIds = $company->services->pluck(IndustryService::FIELD_ID);

        $profitabilityConfigurations = GenericProfitabilityAssumptionConfiguration::query()
            ->where(fn(Builder $query) =>
                $query->where(GenericProfitabilityAssumptionConfiguration::FIELD_COMPANY_ID, $companyId)
                    ->whereIn(GenericProfitabilityAssumptionConfiguration::FIELD_INDUSTRY_SERVICE_ID, $serviceIds)
            )->orWhere(fn(Builder $query) =>
                $query->where(GenericProfitabilityAssumptionConfiguration::FIELD_IS_DEFAULT, true)
                    ->whereIn(GenericProfitabilityAssumptionConfiguration::FIELD_INDUSTRY_SERVICE_ID, $serviceIds)
            )->get();

        return $profitabilityConfigurations->filter(function(GenericProfitabilityAssumptionConfiguration $config) use ($profitabilityConfigurations, $serviceIds) {
            // Remove any defaults where the company has their own set profitability
            return !($config->is_default
                    && ($profitabilityConfigurations->search(fn(GenericProfitabilityAssumptionConfiguration $matchConfig) => !$matchConfig->is_default && $matchConfig->industry_service_id === $config->industry_service_id)) > -1);
        });
    }

    /**
     * @param int $companyId
     * @param IndustryService $service
     * @param array $data
     * @return GenericProfitabilityAssumptionConfiguration|null
     */
    public function updateOrCreateProfitabilityConfiguration(int $companyId, IndustryService $service, array $data): ?GenericProfitabilityAssumptionConfiguration
    {
        $company = Company::query()->find($companyId);

        /** @var GenericProfitabilityAssumptionConfiguration $newConfiguration */
        $newConfiguration = $company
            ? GenericProfitabilityAssumptionConfiguration::query()
                ->updateOrCreate([
                    GenericProfitabilityAssumptionConfiguration::FIELD_COMPANY_ID           => $company->{Company::FIELD_ID},
                    GenericProfitabilityAssumptionConfiguration::FIELD_INDUSTRY_SERVICE_ID  => $service->{IndustryService::FIELD_ID},
                ], $data)
            : null;
        return $newConfiguration;
    }

    /**
     * @param int $industryServiceId
     * @return ?GenericProfitabilityAssumptionConfiguration|null
     */
    private function getDefaultProfitabilityConfiguration(int $industryServiceId): ?GenericProfitabilityAssumptionConfiguration
    {
        /** @var ?GenericProfitabilityAssumptionConfiguration $defaultProfitability */
        $defaultProfitability = GenericProfitabilityAssumptionConfiguration::query()
            ->where(GenericProfitabilityAssumptionConfiguration::FIELD_IS_DEFAULT, true)
            ->where(GenericProfitabilityAssumptionConfiguration::FIELD_INDUSTRY_SERVICE_ID, $industryServiceId)
            ->first();
        return $defaultProfitability;
    }
}
