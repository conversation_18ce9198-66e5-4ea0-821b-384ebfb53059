<?php

namespace App\Repositories\Odin;

use App\Contracts\Repositories\Odin\CompanyIndustryTypeRepositoryContract;
use App\Models\Odin\CompanyIndustryType;
use Illuminate\Support\Collection;

class CompanyIndustryTypeRepository implements CompanyIndustryTypeRepositoryContract
{
    /**
     * @inheritDoc
     */
    public function getCompanies(int $industryType): Collection
    {
        return CompanyIndustryType::query()->where(CompanyIndustryType::FIELD_INDUSTRY_TYPE_ID, $industryType)->latest()->get();
    }

    /**
     * @inheritDoc
     */
    public function get(int $industryType, int $company): Collection
    {
        return CompanyIndustryType::query()
                    ->where(CompanyIndustryType::FIELD_INDUSTRY_TYPE_ID, $industryType)
                    ->where(CompanyIndustryType::FIELD_COMPANY_ID, $company)
                    ->latest()
                    ->get();
    }

    /**
     * @inheritDoc
     */
    public function add(int $industryType, int $company): bool
    {
        return CompanyIndustryType::query()->create(
            [
                CompanyIndustryType::FIELD_INDUSTRY_TYPE_ID => $industryType,
                CompanyIndustryType::FIELD_COMPANY_ID       => $company
            ]
        ) !== null;
    }
}
