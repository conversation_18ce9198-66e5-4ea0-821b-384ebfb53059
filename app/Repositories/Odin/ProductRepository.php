<?php

namespace App\Repositories\Odin;

use App\Enums\ConsumerProductChannel;
use App\Enums\Odin\Product as ProductEnum;
use App\Enums\Odin\ProductAssignmentAffectRejectionPercentage;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use App\Models\UpsellAutomationLogEntry;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;

class ProductRepository
{
    /**
     * @return Collection
     */
    public function getAllProducts(): Collection
    {
        return Product::all();
    }

    /**
     * @param string $name
     * @param int|null $id
     *
     * @return Product
     */
    public function createOrUpdateProduct(string $name, ?int $id = null): Product
    {
        /** @var Product $product */
        $product = Product::query()->updateOrCreate(
            [Product::FIELD_ID => $id],
            [Product::FIELD_NAME => $name]
        );

        return $product;
    }

    /**
     * @param Product $product
     *
     * @return bool|null
     */
    public function deleteProduct(Product $product): ?bool
    {
        $deleted = $product->delete();
        if ($deleted) {
            ServiceProduct::query()
                ->where(ServiceProduct::FIELD_PRODUCT_ID, $product->id)
                ->delete();
        }

        return $deleted;
    }

    /**
     * Get total leads assigned to company. Includes rejected leads
     *
     * @param Company $company
     * @param int|null $startTimestamp
     * @param int|null $endTimestamp
     * @return int
     */
    public function getLeadCountForCompany(Company $company, int $startTimestamp = null, int $endTimestamp = null): int
    {
        $leadProductId = Product::query()->where(Product::FIELD_NAME, ProductEnum::LEAD)->first()->{Product::FIELD_ID};

        return $this->getProductCountForCompany($company, [$leadProductId], null, null, $startTimestamp, $endTimestamp, false);
    }

    /**
     * @param Company $company
     * @param int|null $startTimestamp
     * @param int|null $endTimestamp
     *
     * @return array
     */
    public function getLeadCountForCompanyByChannel(Company $company, int $startTimestamp = null, int $endTimestamp = null): array
    {
        $leadProductId = Product::query()->where(Product::FIELD_NAME, ProductEnum::LEAD)->first()->{Product::FIELD_ID};

        return $this->getProductsForCompanyQuery(
            company: $company,
            productIds: [$leadProductId],
            startTimeStamp: $startTimestamp,
            endTimestamp: $endTimestamp,
            includeRejectedLeads: false
        )
            ->leftJoin(UpsellAutomationLogEntry::TABLE, fn(JoinClause $joinClause) => $joinClause->on(
                UpsellAutomationLogEntry::TABLE . '.' . UpsellAutomationLogEntry::FIELD_CONSUMER_PRODUCT_ID,
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID
            )->where(UpsellAutomationLogEntry::TABLE . '.' . UpsellAutomationLogEntry::FIELD_STATUS, UpsellAutomationLogEntry::STATUS_UPSOLD))
            ->selectRaw('IF(' . UpsellAutomationLogEntry::TABLE . '.' . UpsellAutomationLogEntry::FIELD_CONSUMER_PRODUCT_ID . ' IS NOT NULL, '
                . ConsumerProductChannel::UNSOLD_LEGS->value . ', ' . ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_CHANNEL
                . ') AS lead_channel, COUNT(*) AS total')
            ->groupBy('lead_channel')
            ->get()
            ->keyBy('lead_channel')
            ->toArray();
    }

    /**
     * @param Company $company
     * @param array|null $productIds
     * @param array|null $industryIds
     * @param array|null $industryServiceIds
     * @param int|null $startTimestamp
     * @param int|null $endTimestamp
     * @param bool $omitRejectedLeads
     * @return Collection|Product
     */
    public function getProductsForCompany(
        Company $company,
        array   $productIds = null,
        array   $industryIds = null,
        array   $industryServiceIds = null,
        int     $startTimestamp = null,
        int     $endTimestamp = null,
        bool    $omitRejectedLeads = true
    ): Collection|array
    {
        return $this->getProductsForCompanyQuery($company, $productIds, $industryIds, $industryServiceIds, $startTimestamp, $endTimestamp, $omitRejectedLeads)->get();
    }

    /**
     * @param Company $company
     * @param array|null $productIds
     * @param array|null $industryIds
     * @param array|null $industryServiceIds
     * @param int|null $startTimestamp
     * @param int|null $endTimestamp
     * @param bool $includeRejectedLeads
     * @return int
     */
    public function getProductCountForCompany(
        Company $company,
        array   $productIds = null,
        array   $industryIds = null,
        array   $industryServiceIds = null,
        int     $startTimestamp = null,
        int     $endTimestamp = null,
        bool    $includeRejectedLeads = true,
    ): int
    {
        return $this->getProductsForCompanyQuery($company, $productIds, $industryIds, $industryServiceIds, $startTimestamp, $endTimestamp, $includeRejectedLeads)->count();
    }

    /**
     * @param Company $company
     * @param array|null $productIds
     * @param array|null $industryIds
     * @param array|null $industryServiceIds
     * @param int|null $startTimeStamp
     * @param int|null $endTimestamp
     * @param bool $includeRejectedLeads
     * @return Builder
     */
    private function getProductsForCompanyQuery(
        Company $company,
        array   $productIds = null,
        array   $industryIds = null,
        array   $industryServiceIds = null,
        int     $startTimeStamp = null,
        int     $endTimestamp = null,
        bool    $includeRejectedLeads = true
    ): Builder
    {
        $query = ProductAssignment::query()
            ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID, $company->id);

        if (!$includeRejectedLeads) {
            //todo
        }

        if ($productIds || $industryIds || $industryServiceIds) {
            $query = $this->addProductIndustryOrServiceCriteria($query, $productIds, $industryIds, $industryServiceIds);
        }

        return $this->addDeliveryTimestampCriteria($query, $startTimeStamp, $endTimestamp);
    }

    /**
     * @param Builder $query
     * @param int|null $startTimestamp
     * @param int|null $endTimestamp
     * @return Builder
     */
    private function addDeliveryTimestampCriteria(
        Builder $query,
        int     $startTimestamp = null,
        int     $endTimestamp = null
    ): Builder
    {
        if ($startTimestamp) {
            $query->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED_AT, '>=', Carbon::createFromTimestamp($startTimestamp));
        }

        if ($endTimestamp) {
            $query->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED_AT, '<=', Carbon::createFromTimestamp($endTimestamp));
        }

        return $query;
    }

    /**
     * @param Builder $query
     * @param array|null $productIds
     * @param array|null $industryIds
     * @param array|null $industryServiceIds
     * @param bool|null $affectRejectionPercentage
     * @return Builder
     */
    protected function addProductIndustryOrServiceCriteria(
        Builder $query,
        array   $productIds = null,
        array   $industryIds = null,
        array   $industryServiceIds = null,
        ?bool   $affectRejectionPercentage = null
    ): Builder
    {
        $query->join(ConsumerProduct::TABLE,
            ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, '=',
            ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID)
            ->join(ServiceProduct::TABLE,
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_SERVICE_PRODUCT_ID, '=',
                ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_ID);

        if ($industryIds) {
            $query->join(IndustryService::TABLE,
                ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, '=',
                IndustryService::TABLE . '.' . IndustryService::FIELD_ID)
                ->whereIn(IndustryService::TABLE . '.' . IndustryService::FIELD_INDUSTRY_ID, $industryIds);
        }

        if ($industryServiceIds) {
            $query->whereIn(ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $industryServiceIds);
        }

        // We can calculate rejection percentage against 1 product at a time
        if($affectRejectionPercentage && count($productIds) === 1) {
            $query->where(function($where) use ($productIds) {
                $where
                    ->where(function($subWhere) use ($productIds)  {
                        $subWhere
                            ->whereIn(ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_PRODUCT_ID, $productIds)
                            ->where(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_AFFECT_REJECTION_PERCENTAGE, ProductAssignmentAffectRejectionPercentage::AFFECT_OWN_PRODUCT->value);
                    })
                    ->orWhere(function($subWhere) use ($productIds)  {
                        $subWhere
                            ->whereIn(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_PARENT_PRODUCT_ID, $productIds)
                            ->where(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_AFFECT_REJECTION_PERCENTAGE, ProductAssignmentAffectRejectionPercentage::AFFECT_PARENT_PRODUCT->value);
                    });
            });
        } else if ($productIds) {
            $query->whereIn(ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_PRODUCT_ID, $productIds);
        }

        return $query;
    }

    /**
     * @return int
     */
    public function getLeadProductId(): int
    {
        return $this->getProductByName(ProductEnum::LEAD->value)->{Product::FIELD_ID};
    }

    /**
     * @return int
     */
    public function getAppointmentProductId(): int
    {
        return $this->getProductByName(ProductEnum::APPOINTMENT->value)->{Product::FIELD_ID};
    }

    /**
     * @return int
     */
    public function getDirectLeadProductId(): int
    {
        return $this->getProductByName(ProductEnum::DIRECT_LEADS->value)->id;
    }


    /**
     * @param String $name
     * @return Product
     */
    public function getProductByName(String $name): Product
    {
        return Product::query()
            ->where(Product::FIELD_NAME, $name)
            ->get()
            ->first();
    }

    /**
     * @param int $productId
     * @return Product
     */
    public function getProductById(int $productId): Product
    {
        return Product::findOrFail($productId);
    }
}
