<?php

namespace App\Repositories\Odin;

use App\Models\Legacy\Location;
use App\Repositories\Legacy\SystemCostRepository;

class SolarShortCodesRepository
{
    const AVERAGE_COST_PER_WATT                  = 'average_cost_per_watt';
    const AVERAGE_COST                           = 'average_cost';
    const AVERAGE_COST_AFTER_TAX_CREDIT          = 'average_cost_after_tax_credit';
    const AVERAGE_COST_PER_WATT_AFTER_TAX_CREDIT = 'average_cost_per_watt_after_tax_credit';
    const LOW_COST_PER_WATT                      = 'low_cost_per_watt';
    const HIGH_COST_PER_WATT                     = 'high_cost_per_watt';

    const DEFAULT_COST_PER_WATT = 3.2;
    const DEFAULT_SYSTEM_SIZE_IN_KW = 6;

    const FEDERAL_TAX_CREDIT = 0.3;

    const LOW_COST_MODIFIER = 0.8;
    const HIGH_COST_MODIFIER = 1.2;

    public function __construct(protected SystemCostRepository $systemCostRepository)
    {
    }

    /**
     * @param Location|null $location
     * @return array
     */
    public function getSolarShortCodesByLocation(?Location $location): array
    {
        $costPerWatt = self::DEFAULT_COST_PER_WATT;

        if($location?->state_abbr) {
            $costPerWatt = $this->systemCostRepository->getCostInState($location->state_abbr, self::DEFAULT_SYSTEM_SIZE_IN_KW);
        }

        $costPerWatt = round($costPerWatt, 2);

        $averageCost                      = round($costPerWatt * self::DEFAULT_SYSTEM_SIZE_IN_KW * 1000);
        $averageCostAfterTaxCredit        = round($averageCost * (1 - self::FEDERAL_TAX_CREDIT));
        $averageCostPerWattAfterTaxCredit = round($costPerWatt * (1 - self::FEDERAL_TAX_CREDIT), 2);

        return [
            self::AVERAGE_COST_PER_WATT                  => $costPerWatt,
            self::AVERAGE_COST                           => number_format($averageCost),
            self::AVERAGE_COST_AFTER_TAX_CREDIT          => number_format($averageCostAfterTaxCredit),
            self::AVERAGE_COST_PER_WATT_AFTER_TAX_CREDIT => $averageCostPerWattAfterTaxCredit,
            self::LOW_COST_PER_WATT                      => round($costPerWatt * self::LOW_COST_MODIFIER, 2),
            self::HIGH_COST_PER_WATT                     => round($costPerWatt * self::HIGH_COST_MODIFIER, 2),
        ];
    }
}
