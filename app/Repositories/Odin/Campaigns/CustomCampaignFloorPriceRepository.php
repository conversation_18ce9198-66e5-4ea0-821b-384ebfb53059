<?php

namespace App\Repositories\Odin\Campaigns;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\CustomCampaignCountyFloorPrice;
use App\Models\Campaigns\CustomCampaignStateFloorPrice;
use Illuminate\Support\Collection;

class CustomCampaignFloorPriceRepository
{

    /**
     * @param string $companyCampaignReference
     * @return Collection
     */
    public function getCustomFloorPricesByCampaignReference(string $companyCampaignReference): Collection
    {
        /** @var CompanyCampaign $companyCampaign */
        $companyCampaign = CompanyCampaign::query()
            ->where('reference', $companyCampaignReference)
            ->firstOrFail();

        return $companyCampaign->customFloorPrices();
    }

    /**
     * @param int $companyCampaignId
     * @param int $stateLocationId
     * @param int $propertyTypeId
     * @param int $qualityTierId
     * @param int $saleTypeId
     * @param float $price
     * @return bool
     */
    public function storeCustomCampaignStateFloorPrice(
        int $companyCampaignId,
        int $stateLocationId,
        int $propertyTypeId,
        int $qualityTierId,
        int $saleTypeId,
        float $price,
    ): bool
    {
        return !!CustomCampaignStateFloorPrice::query()->updateOrCreate([
            CustomCampaignStateFloorPrice::FIELD_COMPANY_CAMPAIGN_ID => $companyCampaignId,
            CustomCampaignStateFloorPrice::FIELD_STATE_LOCATION_ID   => $stateLocationId,
            CustomCampaignStateFloorPrice::FIELD_PROPERTY_TYPE_ID    => $propertyTypeId,
            CustomCampaignStateFloorPrice::FIELD_QUALITY_TIER_ID     => $qualityTierId,
            CustomCampaignStateFloorPrice::FIELD_SALE_TYPE_ID        => $saleTypeId,
        ], [
            CustomCampaignStateFloorPrice::FIELD_PRICE => $price,
        ]);
    }

    /**
     * @param int $companyCampaignId
     * @param int $stateLocationId
     * @param int $countyLocationId
     * @param int $propertyTypeId
     * @param int $qualityTierId
     * @param int $saleTypeId
     * @param float $price
     * @return bool
     */
    public function storeCustomCampaignCountyFloorPrice(
        int $companyCampaignId,
        int $stateLocationId,
        int $countyLocationId,
        int $propertyTypeId,
        int $qualityTierId,
        int $saleTypeId,
        float $price,
    ): bool
    {
        return !!CustomCampaignCountyFloorPrice::query()->updateOrCreate([
            CustomCampaignCountyFloorPrice::FIELD_COMPANY_CAMPAIGN_ID => $companyCampaignId,
            CustomCampaignCountyFloorPrice::FIELD_STATE_LOCATION_ID   => $stateLocationId,
            CustomCampaignCountyFloorPrice::FIELD_COUNTY_LOCATION_ID  => $countyLocationId,
            CustomCampaignCountyFloorPrice::FIELD_PROPERTY_TYPE_ID    => $propertyTypeId,
            CustomCampaignCountyFloorPrice::FIELD_QUALITY_TIER_ID     => $qualityTierId,
            CustomCampaignCountyFloorPrice::FIELD_SALE_TYPE_ID        => $saleTypeId,
        ], [
            CustomCampaignCountyFloorPrice::FIELD_PRICE => $price,
        ]);
    }
}
