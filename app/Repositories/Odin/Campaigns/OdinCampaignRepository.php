<?php

namespace App\Repositories\Odin\Campaigns;

use App\Enums\Odin\Product;
use App\Exceptions\DebugException;
use App\Models\ComputedRejectionStatistic;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignLocation;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Legacy\LeadSalesType;
use App\Models\Legacy\Location;
use App\Models\Odin\ProductAppointment;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\Company;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductCampaignBudget;
use App\Repositories\ComputedRejectionStatisticRepository;
use App\Services\DatabaseHelperService;
use App\Services\Legacy\QuoteCompanyCalculationsService;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;

class OdinCampaignRepository
{
    const ALLOWED_COMPANY_STATUSES = [EloquentCompany::STATUS_ACTIVE];
    const UNVERIFIED_CLASSIFICATIONS = Consumer::UNVERIFIED_CLASSIFICATIONS;
    const LIMIT_REJECTION_VALUE    = 20.0;
    const USAGE_THRESHOLD          = 115.0;

    private bool $debug = false;
    private ?ProductCampaign $debugProductCampaign = null;
    private ?Company $debugCompany = null;

    /**
     * @param ComputedRejectionStatisticRepository $rejectionRepository
     * @param QuoteCompanyCalculationsService $quoteCompanyCalculationsService
     */
    public function __construct(
        protected ComputedRejectionStatisticRepository $rejectionRepository,
        protected QuoteCompanyCalculationsService      $quoteCompanyCalculationsService
    )
    {
    }

    /**
     * @param bool $debug
     * @param Company $company
     * @param ProductCampaign|null $productCampaign
     */
    public function setDebug(bool $debug, Company $company, ?ProductCampaign $productCampaign = null): void
    {
        $this->debug = $debug;
        $this->debugCompany = $company;
        $this->debugProductCampaign = $productCampaign;
    }

    /**
     * @param string $errorMsg
     * @param Builder|null $query
     * @param Collection|null $campaigns
     * @return void
     * @throws DebugException
     * @throws Exception
     */
    public function checkDebugResult(string $errorMsg, ?Builder $query = null, ?Collection $campaigns = null): void
    {
        if($this->debug
        && $this->debugCompany) {
            if($query) {
                $companyIdCol = 'company_id';
                $campaignIdCol = 'campaign_id';

                $companyCampaigns = $query
                    ->selectRaw(implode(',', [
                        ProductCampaign::TABLE . '.' . ProductCampaign::FIELD_COMPANY_ID . " AS {$companyIdCol}",
                        ProductCampaign::TABLE . '.' . ProductCampaign::FIELD_ID . " AS {$campaignIdCol}"
                    ]))
                    ->pluck($companyIdCol, $campaignIdCol)
                    ->toArray();
            }
            else if($campaigns) {
                foreach($campaigns as $legacyLeadCampaign) {
                    if($legacyLeadCampaign instanceof LeadCampaign) {
                        $productCampaign = $legacyLeadCampaign->{LeadCampaign::RELATION_PRODUCT_CAMPAIGN};
                    }
                    else if(is_array($legacyLeadCampaign)) {
                        $productCampaign = ProductCampaign::query()->where(ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID, $legacyLeadCampaign["campaign_id"])->first();
                    }

                    if($productCampaign) {
                        $companyCampaigns[$productCampaign->{ProductCampaign::FIELD_ID}] = $productCampaign->{ProductCampaign::FIELD_COMPANY_ID};
                    }
                }
            }
            else {
                throw new Exception("Missing query or campaigns");
            }

            if($this->debugProductCampaign) {
                $pass = ($companyCampaigns[$this->debugProductCampaign->{ProductCampaign::FIELD_ID}] ?? null) === $this->debugCompany->{Company::FIELD_ID};
            }
            else {
                $pass = in_array($this->debugCompany->{Company::FIELD_ID}, array_filter(array_unique(array_values($companyCampaigns))), true);
            }

            if(!$pass) {
                throw new DebugException($errorMsg);
            }
        }
    }

    /**
     * Builds the base query for searching through campaigns.
     *
     * @param IndustryService|null $service
     * @param string|null $zipCode
     * @return Builder
     * @throws DebugException
     */
    protected function getBaseQuery(?IndustryService $service = null, ?string $zipCode = null): Builder
    {
        $query = LeadCampaign::query();

        if ($service != null) {
            $query = $query->join(
                DatabaseHelperService::database() . '.' . ProductCampaign::TABLE,
                fn(JoinClause $join) => $join->on(LeadCampaign::TABLE . '.' . LeadCampaign::ID, '=', ProductCampaign::TABLE . '.' . ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID)
                    ->where(ProductCampaign::TABLE . '.' . ProductCampaign::FIELD_INDUSTRY_SERVICE_ID, $service->id)
            );

            $this->checkDebugResult("Missing campaign for service '{$service->{IndustryService::FIELD_NAME}}'", $query);
        }

        if ($zipCode != null) {
            $query = $query->join(LeadCampaignLocation::TABLE, LeadCampaign::TABLE . '.' . LeadCampaign::ID, '=', LeadCampaignLocation::TABLE . '.' . LeadCampaignLocation::LEAD_CAMPAIGN_ID)
                ->join(Location::TABLE, fn(JoinClause $join) => $join->on(Location::TABLE . '.' . Location::ID, '=', LeadCampaignLocation::LOCATION_ID)
                    ->where(Location::TYPE, Location::TYPE_ZIP_CODE)
                    ->where(Location::ZIP_CODE, $zipCode)
                );

            $this->checkDebugResult("No campaigns targeting zipcode '$zipCode'", $query);
        }

        return $query;
    }

    /**
     * Returns the available campaigns for a given product.
     *
     * @param ConsumerProduct $product
     * @return Collection
     * @throws DebugException
     */
    public function getAvailableCampaignsForProduct(ConsumerProduct $product): Collection
    {
        $query = $this->getBaseQuery($product->industryService, $product->address->zip_code)
            ->where(ProductCampaign::TABLE . '.' . ProductCampaign::FIELD_PRODUCT_ID, $product->serviceProduct->product_id);

        $this->checkDebugResult("No campaigns for product '{$product->serviceProduct->product->name}'", $query);

        if ($product->serviceProduct->product->name === Product::LEAD->value)
            $query->where(LeadCampaign::TABLE . '.' . LeadCampaign::STATUS, LeadCampaign::STATUS_ACTIVE);
        else
            $query->where(ProductCampaign::TABLE . '.' . ProductCampaign::FIELD_STATUS, true);

        $this->checkDebugResult("Campaigns are inactive", $query);

        $campaigns = $query->select(LeadCampaign::TABLE.'.*')->get();

        if ($product->serviceProduct->product->name === Product::LEAD->value)
            return $this->filterLeadCampaigns($campaigns, $product);
        else
            return $this->filterCampaigns($campaigns, $product);
    }

    /**
     * @param ConsumerProduct $product
     * @return Collection
     * @throws DebugException
     */
    public function getAvailableCompaniesForProductByCampaigns(ConsumerProduct $product): Collection
    {
        $campaigns = $this->getAvailableCampaignsForProduct($product);
        $companyIds = $campaigns->pluck(LeadCampaign::COMPANY_ID)->unique();
        $companies = Company::query()->whereIn(Company::FIELD_LEGACY_ID, $companyIds->toArray())->with([Company::RELATION_LEGACY_COMPANY])->get();

        return $this->filterCompanies($companies, $product);
    }

    /**
     * @param ConsumerProduct $product
     * @param int|null $overrideSaleTypeId
     * @return Collection
     * @throws DebugException
     */
    public function getAvailableCampaignsForDelivery(ConsumerProduct $product, ?int $overrideSaleTypeId = null): Collection
    {
        $campaigns = $this->getAvailableCampaignsForProduct($product);
        $campaigns = $this->filterCampaignsByCompany($campaigns, $product);
        $return = collect();

        $companyIds = $campaigns->pluck(LeadCampaign::COMPANY_ID)->unique();
        $companies = Company::query()->whereIn(Company::FIELD_LEGACY_ID, $companyIds->toArray())->with([Company::RELATION_LEGACY_COMPANY])->get();
        $companyIds = $companies->pluck(Company::FIELD_ID);

        /** @var ComputedRejectionStatistic[]|Collection $rejections */
        $rejections = $this->rejectionRepository->getProductRejectionPercentageForCompanies($companyIds->toArray(), $product->serviceProduct->product_id);

        /** @var LeadCampaign $campaign */
        foreach($campaigns as $campaign) {
            $saleTypesFilter = $campaign
                ->leadSalesTypeConfigurations()
                ->where(LeadCampaignSalesTypeConfiguration::STATUS, LeadCampaignSalesTypeConfiguration::STATUS_ENABLE);

            if($overrideSaleTypeId > 0) {
                $saleTypesFilter->where(LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID, $overrideSaleTypeId);
            }

            $saleTypes = $saleTypesFilter->get();

            /** @var LeadCampaignSalesTypeConfiguration $type */
            foreach($saleTypes as $type) {
                $computedRejectionStatistics = $this->rejectionRepository->getComputedRejectionStatisticsByLegacyIdFromRejectionsCollection($rejections, $campaign->company_id);

                $return->add([
                    "campaign_id"                                                          => $campaign->id,
                    "sale_type_id"                                                         => $type->{LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID},
                    "company_id"                                                           => $campaign->company_id,
                    ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_ELIGIBILITY => $computedRejectionStatistics?->{ComputedRejectionStatistic::ACTIVE_REJECTION_FIELD_IMPACTING_ELIGIBILITY} ?? 0,
                    ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID         => $computedRejectionStatistics?->{ComputedRejectionStatistic::ACTIVE_REJECTION_FIELD_IMPACTING_BID} ?? 0
                ]);
            }
        }

        return $return;
    }

    /**
     * Filters campaigns that aren't valid for a given consumer product.
     *
     * @param Collection $campaigns
     * @param ConsumerProduct $product
     * @return Collection
     */
    protected function filterLeadCampaigns(Collection $campaigns, ConsumerProduct $product): Collection
    {
        return $campaigns->filter(function (LeadCampaign $campaign) use ($product) {
            if ($product->consumer->classification === Consumer::CLASSIFICATION_EMAIL_ONLY || $product->consumer->classification === Consumer::CLASSIFICATION_VERIFIED_EMAIL_ONLY) {
                if ($campaign->leadSalesTypeConfigurations()
                        ->where(LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID, LeadSalesType::LEAD_SALE_TYPE_UNVERIFIED_ID)
                        ->where(LeadCampaignSalesTypeConfiguration::STATUS, LeadCampaignSalesTypeConfiguration::STATUS_ENABLE)
                        ->count() == 0)
                    return false;
            } elseif (in_array($product->consumer->classification, self::UNVERIFIED_CLASSIFICATIONS)) {
                if ($campaign->leadSalesTypeConfigurations()
                        ->whereIn(LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID, LeadSalesType::UNVERIFIED_SALES_TYPES)
                        ->where(LeadCampaignSalesTypeConfiguration::STATUS, LeadCampaignSalesTypeConfiguration::STATUS_ENABLE)
                        ->count() == 0)
                    return false;
            }

            $budgetSpent = $this->quoteCompanyCalculationsService->calcBudgetSpentByLeadCampaign($campaign);
            $averageDaily = $this->quoteCompanyCalculationsService->calcAverageDailyByLeadCampaign($campaign, $budgetSpent);
            $usage = $this->quoteCompanyCalculationsService->calcUsageByLeadCampaign($campaign, $budgetSpent, $averageDaily);

            if ($usage >= $campaign->maximum_budget_usage)
                return false;

            return true;
        });
    }

    /**
     * @param Collection $campaigns
     * @param ConsumerProduct $consumerProduct
     * @return Collection
     * @throws DebugException
     */
    protected function filterCampaigns(Collection $campaigns, ConsumerProduct $consumerProduct): Collection
    {
        /** @var ProductAppointment $appointment */
        $appointment = ProductAppointment::query()->where(ProductAppointment::CONSUMER_PRODUCT_ID, $consumerProduct->id)->firstOrFail();

        $filteredCampaigns = $campaigns->filter(function (LeadCampaign $campaign) use ($consumerProduct, $appointment) {
            //check if the budget is enabled
            if (!$campaign->productCampaign->budgets()->where(ProductCampaignBudget::FIELD_QUALITY_TIER, $appointment->appointment_type)->first()?->status)
                return false;

            return true;
        });

        $this->checkDebugResult("Campaigns don't have active {$appointment->{ProductAppointment::APPOINTMENT_TYPE}->value} budget", null, $filteredCampaigns);

        return $filteredCampaigns;
    }

    /**
     * Filters companies that aren't valid for a given consumer product.
     *
     * @param Collection $companies
     * @param ConsumerProduct $product
     * @return Collection
     *
     * @TODO: Once the new statuses have been confirmed to be working, remove any uses of legacy company here.
     */
    protected function filterCompanies(Collection $companies, ConsumerProduct $product): Collection
    {
        $legacyIds = $companies->pluck(Company::FIELD_LEGACY_ID);
        $rejections = $this->rejectionRepository->getProductRejectionPercentageForCompaniesByLegacyIds($legacyIds->toArray(), $product->serviceProduct->product_id);

        return $companies->filter(function (Company $company) use ($product, $rejections) {
            return $this->isCompanyValid($company, $rejections);
        });
    }

    /**
     * @param Company $company
     * @param ComputedRejectionStatistic|Collection $rejections
     * @return bool
     */
    protected function isCompanyValid(Company $company, ComputedRejectionStatistic|Collection $rejections): bool
    {
        if (!in_array($company->legacyCompany->status, self::ALLOWED_COMPANY_STATUSES))
            return false;

        if (!$company->legacyCompany->isBuyingLeads())
            return false;

        $rejectionPercentage = $this->rejectionRepository->getComputedRejectionStatisticsByLegacyIdFromRejectionsCollection($rejections, $company->legacy_id)
            ?->{ComputedRejectionStatistic::ACTIVE_REJECTION_FIELD_IMPACTING_ELIGIBILITY} ?? 0;

        $rejectionThreshold = ComputedRejectionStatistic::ACTIVE_REJECTION_FIELD_IMPACTING_ELIGIBILITY === ComputedRejectionStatistic::FIELD_CRM_REJECTION_PERCENTAGE ?
            config('sales.leads.crm_rejection_percentage_threshold') :
            config('sales.leads.overall_rejection_percentage_threshold');

        if ($rejectionPercentage >= $rejectionThreshold)
            return false;

        return true;
    }

    /**
     * Filters companies that aren't valid for a given consumer product.
     *
     * @param Collection $campaigns
     * @param ConsumerProduct $product
     * @return Collection
     *
     * @TODO: Once the new statuses have been confirmed to be working, remove any uses of legacy company here.
     * @throws DebugException
     */
    protected function filterCampaignsByCompany(Collection $campaigns, ConsumerProduct $product): Collection
    {
        $companyIds = $campaigns->pluck(LeadCampaign::COMPANY_ID)->unique();
        $companies = Company::query()->whereIn(Company::FIELD_LEGACY_ID, $companyIds->toArray())->with([Company::RELATION_LEGACY_COMPANY])->get();
        $legacyIds = $companies->pluck(Company::FIELD_LEGACY_ID);

        $rejections = $this->rejectionRepository->getProductRejectionPercentageForCompaniesByLegacyIds($legacyIds->toArray(), $product->serviceProduct->product_id);

        $filteredCampaigns = $campaigns->filter(function(LeadCampaign $campaign) use ($product, $companies, $rejections) {
            return $this->isCompanyValid($companies->where(Company::FIELD_LEGACY_ID, $campaign->company_id)->first(), $rejections);
        });

        $this->checkDebugResult("Company is inactive or exceeded rejection threshold", null, $filteredCampaigns);

        return $filteredCampaigns;
    }
}
