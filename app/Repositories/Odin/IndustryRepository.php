<?php

namespace App\Repositories\Odin;

use App\Builders\Odin\IndustryBuilder;
use App\Contracts\Repositories\Odin\IndustryRepositoryContract;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Jobs\CalculateCompanyCampaignLowBidFlagJob;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryConfiguration;
use App\Models\Odin\IndustryService;
use App\Services\Campaigns\CompanyCampaignService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;

class IndustryRepository implements IndustryRepositoryContract
{
    /**
     * Returns list of industries along with their details based on the requested parameters.
     *
     * @inheritDoc
     */
    public function getIndustries(bool $appendServices = false, bool $appendCompanies = false, bool $appendCompanyServices = false, array $industries = []): Collection
    {
        /** @var IndustryBuilder $query */
        $query = IndustryBuilder::query()
                            ->forIndustryIds($industries)
                            ->getIndustryData()
                            ->appendServicesData($appendServices)
                            ->appendCompaniesData($appendCompanies)
                            ->appendCompanyServicesData($appendCompanyServices)
                            ->getQuery();

        return $query->get();
    }

    /**
     * Takes care of adding and updating an industry into the system based on the requested parameters.
     *
     * @inheritDoc
     */
    public function updateOrCreateIndustry(
        string $name,
        ?string $lightModeColor = null,
        ?string $darkModeColor = null,
        ?int $id = null
    ): bool {
        return Industry::query()->updateOrCreate(
            [
                Industry::FIELD_ID => $id
            ],
            [
                Industry::FIELD_NAME => $name,
                Industry::FIELD_COLOR_DARK => $darkModeColor,
                Industry::FIELD_COLOR_LIGHT => $lightModeColor,
            ]
        ) !== null;
    }

    /**
     * Takes care of removing a specific industry from the system.
     *
     * @inheritDoc
     */
    public function deleteIndustry(int $id): bool
    {
        /** @var Industry $industry */
        $industry = Industry::query()->find($id);
        if(!$industry) return false;

        $industry->websites()->delete();
        $industry->services()->delete();
        $industry->companyFields()->delete();
        $industry->consumerFields()->delete();
        $industry->types()->delete();

        return $industry->delete();
    }

    /**
     * Returns details of a specific industry based on the requested parameters.
     *
     * @inheritDoc
     */
    public function getIndustryDetail(int $industry, bool $appendServices = false, bool $appendCompanies = false, bool $appendCompanyServices = false): Collection
    {
        /** @var IndustryBuilder $query */
        $query = IndustryBuilder::query()
                            ->forIndustryId($industry)
                            ->getIndustryData()
                            ->appendServicesData($appendServices)
                            ->appendCompaniesData($appendCompanies)
                            ->appendCompanyServicesData($appendCompanyServices)
                            ->getQuery();

        return $query->get();
    }

    /**
     * Returns list of services being offered against the requested industry.
     *
     * @inheritDoc
     */
    public function getIndustryServices(int $industry, bool $appendCompanyServices = false): Collection
    {
        /** @var IndustryBuilder $query */
        $query = IndustryBuilder::query()
                            ->forIndustryId($industry)
                            ->getServicesData()
                            ->appendCompanyServicesData($appendCompanyServices)
                            ->getQuery();

        return $query->get();
    }

    /**
     * Returns list of companies against the requested service.
     *
     * @inheritDoc
     */
    public function getIndustryServicedCompanies(int $industryService): Collection
    {
        /** @var IndustryBuilder $query */
        $query = IndustryBuilder::query()
                            ->forServiceId($industryService)
                            ->getServicedCompaniesData()
                            ->getQuery();

        return $query->get();
    }

    /**
     * Return a Collection of Industries by name search
     * @param string[] $industryNames
     * @return Collection<Industry>
     */
    public function getIndustriesByNames(array $industryNames): Collection
    {
        return collect($industryNames)->reduce(function(Collection $output, string $name) {
            $industry = Industry::query()
                ->where(Industry::FIELD_NAME, $name)
                ?->first() ?? null;
            return $industry
                ? $output->push($industry)
                : $output;
        }, collect());
    }

    public function getIndustryByName(String $name): ?Industry
    {
        return Industry::query()
                ->where(Industry::FIELD_NAME, $name)
                ->get()->first() ?? null;
    }

    /**
     * @param string $industrySlug
     * @return int
     */
    public function getIndustryIdBySlug(string $industrySlug): int
    {
        return Industry::query()
            ->where(Industry::FIELD_SLUG, $industrySlug)
            ->first()
            ->{Industry::FIELD_ID};
    }

    /**
     * @param array $industrySlugs
     * @return array
     */
    public function getIndustryIdsBySlugs(array $industrySlugs): array
    {
        return Industry::query()
            ->whereIn(Industry::FIELD_SLUG, $industrySlugs)
            ->pluck(Industry::FIELD_ID)
            ->toArray();
    }

    /**
     * @param int $industryId
     * @param array $configurationPayload
     * @return bool
     */
    public function updateIndustryConfiguration(int $industryId, array $configurationPayload): bool
    {
        /** @var IndustryConfiguration $industryConfiguration */
        $industryConfiguration = Industry::query()
            ->findOrFail($industryId)
            ->industryConfiguration()
            ->firstOrCreate();

        return $industryConfiguration->update($configurationPayload);
    }

    /**
     * @return int[]
     */
    public function getCustomFloorPriceIndustryIds(): array
    {
        return IndustryConfiguration::query()
            ->where(IndustryConfiguration::FIELD_ALLOW_CUSTOM_FLOOR_PRICES, true)
            ->pluck(IndustryConfiguration::FIELD_INDUSTRY_ID)
            ->toArray();
    }

    /**
     * @param int $industryId
     * @param bool $allowCustomFloorPrices
     * @return bool
     */
    public function setCustomFloorPriceFlag(int $industryId, bool $allowCustomFloorPrices): bool
    {
        /** @var IndustryConfiguration $industryConfiguration */
        $industryConfiguration = Industry::query()->findOrFail($industryId)->industryConfiguration()->firstOrCreate([]);
        $industryConfiguration->allow_custom_floor_prices = $allowCustomFloorPrices;
        $success = $industryConfiguration->save();

        if ($success) {
            $industryModel = Industry::query()->find($industryId);
            CalculateCompanyCampaignLowBidFlagJob::dispatch(null, $industryModel);

            if ($industryConfiguration->refresh()->allow_custom_floor_prices) {
                /** @var CompanyCampaignService $companyCampaignService */
                $companyCampaignService = app(CompanyCampaignService::class);
                $companyCampaignService->disableCustomFloorPricingForIndustry($industryId);
            }
        }

        return $success;
    }

    /**
     * Returns industries that have active future campaigns
     *
     * @return Collection
     */
    public function getIndustriesWithFutureCampaignsActive(): Collection
    {
        return Industry::query()
            ->join(IndustryConfiguration::TABLE, IndustryConfiguration::TABLE.'.'.IndustryConfiguration::FIELD_INDUSTRY_ID, '=', Industry::TABLE.'.'.Industry::FIELD_ID)
            ->where(IndustryConfiguration::TABLE.'.'.IndustryConfiguration::FIELD_FUTURE_CAMPAIGNS_ACTIVE, true)
            ->get([
                Industry::TABLE.'.'.Industry::FIELD_ID,
                Industry::TABLE.'.'.Industry::FIELD_NAME,
                Industry::TABLE.'.'.Industry::FIELD_SLUG,
            ]);
    }

    /**
     * @return EloquentCollection<Industry>
     */
    public function getIndustriesWithServicesForRegistration(): EloquentCollection
    {
        return Industry::query()
            ->with([Industry::RELATION_SERVICES => fn($query) => $query->where(IndustryService::FIELD_SHOW_ON_REGISTRATION, true)])
            ->orderBy(Industry::FIELD_NAME)
            ->get()
            ->filter(fn(Industry $industry) => $industry->services->count() > 0)
            ->sortBy(fn(Industry $industry) => match ($industry->name) {
                IndustryEnum::SOLAR->value => -100,
                IndustryEnum::ROOFING->value => -95,
                IndustryEnum::HVAC->value => -90,
                IndustryEnum::KITCHENS->value => -85,
                IndustryEnum::BATHROOMS->value => -80,
                IndustryEnum::SIDING->value => -75,
                IndustryEnum::WINDOWS->value => -70,
                default => 0,
            })
            ->values();
    }
}
