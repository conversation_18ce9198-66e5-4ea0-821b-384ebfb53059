<?php

namespace App\Repositories\Odin;

use App\Builders\Odin\ConsumerBuilder;
use App\Jobs\Odin\ConsumerProductVerificationJob;
use App\Models\LeadProcessingAllocation;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\Website;
use App\Services\HelperService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Ramsey\Uuid\Uuid;

class ConsumerRepository
{
    /**
     * @param ConsumerProductRepository $productRepository
     */
    public function __construct(
        protected ConsumerProductRepository $productRepository,
    ) {}

    /**
     * @param array $data
     *
     * @return Consumer
     */
    public function createConsumerFromAttributes(array $data = []): Consumer
    {
        $consumer = new Consumer();
        $consumer->reference = Uuid::uuid4();
        $consumer->fill(collect($data)->filter(fn($value) => $value !== null)->toArray())->save();

        return $consumer;
    }

    /**
     * @param int $consumerId
     * @return Consumer
     * @throws ModelNotFoundException
     */
    public function findOrFail(int $consumerId): Consumer|Model
    {
        return Consumer::query()->findOrFail($consumerId);
    }

    /**
     * @param int $legacyId
     *
     * @return Consumer
     */
    public function findByLegacyIdOrFail(int $legacyId): Consumer
    {
        /** @var Consumer $consumer */
        $consumer = Consumer::query()->where(Consumer::FIELD_LEGACY_ID, $legacyId)->firstOrFail();

        return $consumer;
    }

    /**
     * @param string $uuid
     * @param bool $withProductData
     * @return Consumer
     */
    public function findByReferenceOrFail(string $uuid, bool $withProductData = false): Consumer
    {
        /** @var Consumer $consumer */
        $consumer = Consumer::query()
            ->where(Consumer::FIELD_REFERENCE, $uuid)
            ->when($withProductData, fn(Builder $query) =>
                $query->with([
                    Consumer::RELATION_CONSUMER_PRODUCT,
                    Consumer::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA,
                    Consumer::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_ADDRESS,
                ])
            )->firstOrFail();

        return $consumer;
    }

    /**
     * @param int $legacyId
     *
     * @return Consumer|null
     */
    public function findByLegacyId(int $legacyId): ?Consumer
    {
        /** @var Consumer|null $consumer */
        $consumer = Consumer::query()->where(Consumer::FIELD_LEGACY_ID, $legacyId)->first();

        return $consumer;
    }

    /**
     * @param int $companyId
     * @param Int|null $campaignId
     * @param Int|null $status
     * @param Int|null $service
     * @param Int|null $leadId
     * @param String|null $name
     * @param String|null $state
     * @param $fromDate
     * @param $toDate
     * @param Int|null $productId
     * @param string|null $leadRefundStatus
     * @param bool|null $orderByDelivery
     * @return Builder
     */
    public function getConsumersByCompanyIdWithSearchFilters(
        int $companyId,
        Int|null $campaignId = null,
        Int|null $status = null,
        Int|null $service = null,
        Int|null $leadId = null,
        String|null $name = null,
        String|null $state = null,
        $fromDate = null,
        $toDate = null,
        Int|null $productId = null,
        ?string $leadRefundStatus = null,
        ?bool $orderByDelivery = false,
    ): Builder
    {
        return ConsumerBuilder::query()
            ->forCompanyId($companyId)
            ->forCompanyCampaign($campaignId)
            ->forStatus($status)
            ->forService($service)
            ->forLeadId($leadId)
            ->forName($name)
            ->forState($state)
            ->forFromDate($fromDate)
            ->forToDate($toDate)
            ->forProductId($productId)
            ->forLeadRefundStatus($leadRefundStatus)
            ->orderByDelivery($orderByDelivery)
            ->getQuery();
    }

    /**
     * @param Consumer $consumer
     * @param array $data
     * @param bool $refreshVerificationDetails
     * @return bool
     * @throws Exception
     */
    public function updateConsumerModel(Consumer $consumer, array $data, bool $refreshVerificationDetails = false): bool
    {
        $consumerUpdateStatus = $consumer->update($data);

        if($refreshVerificationDetails) ConsumerProductVerificationJob::dispatch($consumer->consumerProducts->first()->id);

        return $consumerUpdateStatus;
    }


    /**
     * Finds consumer relation by phone or email, returns null if none found
     *
     * @param string|null $email
     * @param string|null $phone
     * @return EloquentCollection|null
     */
    public function getAssociatedConsumers(string $email = null, string $phone = null): EloquentCollection | null
    {
        $query = Consumer::query();

        if (!$email && !$phone) {
            return null;
        }
        if ($email) {
            $query->where(Consumer::FIELD_EMAIL, $email);
        }
        if ($phone) {
            $query->orWhere(Consumer::FIELD_PHONE, 'LIKE', normalizePhoneNumberSql($phone));
        }
        return $query->get();
    }

    /**
     * @param Consumer $consumer
     * @return string
     */
    public function getNextAllocationTime(Consumer $consumer): string
    {
        $consumerProductIds = $consumer->{Consumer::RELATION_CONSUMER_PRODUCT}->pluck(ConsumerProduct::FIELD_ID)->toArray();

        $leadAllocation = LeadProcessingAllocation::query()
            ->whereIn(LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID, $consumerProductIds)
            ->where(LeadProcessingAllocation::FIELD_DELIVERED, LeadProcessingAllocation::NOT_DELIVERED)
            ->where(LeadProcessingAllocation::FIELD_DELIVER_AT, '!=', LeadProcessingAllocation::BLANK_DELIVERY)
            ->orderBy(LeadProcessingAllocation::FIELD_DELIVER_AT, 'asc')
            ->select([
                LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID,
                LeadProcessingAllocation::FIELD_DELIVER_AT
            ])->first();

        if ($leadAllocation) {
            $time = Carbon::parse($leadAllocation->{LeadProcessingAllocation::FIELD_DELIVER_AT}, 'UTC')->format("l, F j, Y H:i");

            return "as lead on {$time} UTC";
        }

        return '';
    }

        /**
     * @param Carbon $startTime
     * @param Carbon $endTime
     * @param bool $excludeRevalidationRequested
     *
     * @return Collection<Consumer>
     */
    public function getUnverifiedConsumers(Carbon $startTime, Carbon $endTime, bool $excludeRevalidationRequested = true): Collection
    {
        return Consumer::query()
            ->whereNotNull(Consumer::FIELD_PHONE)
            ->where(Consumer::CREATED_AT, '>=', $startTime)
            ->where(Consumer::CREATED_AT, '<=', $endTime)
            ->whereNotIn(Consumer::FIELD_CLASSIFICATION, Consumer::VERIFIED_CLASSIFICATIONS)
            ->when($excludeRevalidationRequested, fn(Builder $query) => $query->whereNull(Consumer::FIELD_REVALIDATION_REQUESTED_AT))
            ->get();
    }

    /**
     * @param string $phone
     *
     * @return Consumer|null
     */
    public function findConsumerByPhone(string $phone): ?Consumer
    {
        /** @var Consumer|null */
        return Consumer::query()->where(Consumer::FIELD_PHONE, $phone)
            ->orWhere(Consumer::FIELD_FORMATTED_PHONE, HelperService::formatUSPhoneNumber($phone))
            ->latest()
            ->first();
    }

    /**
     * @return Builder
     */
    public function getConsumersProducts(): Builder
    {
        return Consumer::query()
            ->leftJoin(ConsumerProduct::TABLE, Consumer::TABLE . '.' . Consumer::FIELD_ID, ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_CONSUMER_ID)
            ->leftJoin(ProductAssignment::TABLE, ProductAssignment::TABLE . '.' .  ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, ConsumerProduct::TABLE . '.' .  ConsumerProduct::FIELD_ID)
            ->leftJoin(Website::TABLE, Website::TABLE . '.' .  Website::FIELD_ID, Consumer::TABLE . '.' . Consumer::FIELD_WEBSITE_ID)
            ->leftJoin(Address::TABLE, Address::TABLE . '.' .  Address::FIELD_ID, ConsumerProduct::TABLE . '.' .  ConsumerProduct::FIELD_ADDRESS_ID)
            ->select(
                Consumer::TABLE . '.' .  Consumer::FIELD_ID,
                Consumer::TABLE . '.' . Consumer::FIELD_FIRST_NAME,
                Consumer::TABLE . '.' . Consumer::FIELD_CLASSIFICATION,
                Consumer::TABLE . '.' .  Consumer::FIELD_CREATED_AT,
                ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_STATUS . ' AS consumer_status',
                Website::TABLE . '.' .  WEBSITE::FIELD_NAME . ' AS website_name',
                CONSUMER::TABLE . '.' .  Consumer::FIELD_PHONE,
                Address::TABLE . '.' .  Address::FIELD_UTC . ' AS timezone_val'
            )
            ->whereIn(Consumer::TABLE . '.' . Consumer::FIELD_CLASSIFICATION, [Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_SMS, Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_LEAD_PROCESSING])
            ->whereBetween(Consumer::TABLE . '.' . Model::CREATED_AT, [
                Carbon::now()->subHours(48)->toDateTimeString(),
                Carbon::now()->subHours(24)->toDateTimeString()
            ])
            ->where(ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_STATUS,  ConsumerProduct::STATUS_ALLOCATED)
            ->where(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_CHARGEABLE, 1)
            ->where(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_DELIVERED, 1)
            ->groupBy(
                Consumer::TABLE . '.' . Consumer::FIELD_ID,
                Consumer::TABLE . '.' . Consumer::FIELD_FIRST_NAME,
                Consumer::TABLE . '.' . Consumer::FIELD_CLASSIFICATION,
                Consumer::TABLE . '.' .  Consumer::FIELD_CREATED_AT,
                ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_STATUS,
                Website::TABLE . '.' .  WEBSITE::FIELD_NAME,
                CONSUMER::TABLE . '.' .  Consumer::FIELD_PHONE,
                Address::TABLE . '.' .  Address::FIELD_UTC
            )
            ->orderBy(Consumer::TABLE . '.' . Model::CREATED_AT, 'DESC');
    }

    /**
     * @param array $legacyQuoteIds
     * @param array $relationships
     * @param array $columns
     * @return Collection<Consumer>
     */
    public function getConsumersByLegacyIds(array $legacyQuoteIds, array $relationships = [], array $columns = ['*']): Collection
    {
        return Consumer::query()->whereIntegerInRaw(Consumer::FIELD_LEGACY_ID, $legacyQuoteIds)->with($relationships)->get($columns);
    }

    public function getConsumers(
        ?Carbon $dateFrom = null,
        ?Carbon $dateTo = null,
        ?array  $consumerProductStatuses = [],
        ?array  $consumerClassifications = [],
        ?bool   $hasValidPhone = null,
    )
    {
        return Consumer::query()
            ->select([Consumer::TABLE . '.*'])
            ->join(
                ConsumerProduct::TABLE,
                ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_CONSUMER_ID,
                '=',
                Consumer::TABLE .'.'. Consumer::FIELD_ID
            )
            ->when(filled($consumerClassifications), function($query) use ($consumerClassifications) {
                $query->whereIntegerInRaw(Consumer::TABLE .'.'. Consumer::FIELD_CLASSIFICATION, $consumerClassifications);
            })
            ->when(filled($consumerProductStatuses), function ($query) use ($consumerProductStatuses) {
                $query->whereIntegerInRaw(ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_STATUS, $consumerProductStatuses);
            })
            ->when($dateFrom, function ($query) use ($dateFrom) {
                $query->where(ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_CREATED_AT, '>=', $dateFrom->toDateTimeString());
            })
            ->when($dateTo, function ($query) use ($dateTo) {
                $query->where(ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_CREATED_AT, '<=', $dateTo->toDateTimeString());
            })
            ->when($hasValidPhone, function ($query) use ($hasValidPhone) {
                $query->whereNotNull(Consumer::TABLE .'.'. Consumer::FIELD_FORMATTED_PHONE);
            });
    }
}
