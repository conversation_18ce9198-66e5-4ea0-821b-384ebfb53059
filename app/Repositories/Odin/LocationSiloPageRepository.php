<?php

namespace App\Repositories\Odin;


use App\Enums\Odin\LocationSiloPageLocationType;
use App\Models\Odin\LocationSiloPage;
use App\Models\Odin\Silo;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class LocationSiloPageRepository
{
    const FIELD_AS_FULL_PATH = 'full_path';

    /**
     * <PERSON>les returning the base query used by this repository which joins the correct tables.
     *
     * @return Builder
     */
    protected function getBaseJoinedQuery(): Builder
    {
        return LocationSiloPage::query()
            ->join(
                Silo::TABLE,
                LocationSiloPage::TABLE . '.' . LocationSiloPage::FIELD_SILO_ID,
                '=',
                Silo::TABLE . '.' . Silo::FIELD_ID
            );
    }

    /**
     * @param string $fullPath
     * @param int $websiteId
     * @return LocationSiloPage|null
     */
    public function getLocationSiloPageByUrlAndWebsiteId(string $fullPath, int $websiteId): ?LocationSiloPage
    {
        /** @var LocationSiloPage|null $page */
        $page = $this->getBaseJoinedQuery()
            ->select(LocationSiloPage::TABLE . '.*')
            ->with([LocationSiloPage::RELATION_LOCATION, LocationSiloPage::RELATION_PARENT_LOCATION])
            ->where(DB::raw("CONCAT(" . Silo::TABLE . '.' . Silo::FIELD_ROOT_PATH . ", " . LocationSiloPage::FIELD_RELATIVE_PATH . ')'), $fullPath)
            ->where(Silo::FIELD_WEBSITE_ID, $websiteId)
            ->where(Silo::TABLE .'.'. Silo::FIELD_IS_ACTIVE,  '=', true)
            ->where(LocationSiloPage::TABLE .'.'. LocationSiloPage::FIELD_IS_ACTIVE, true)
            ->first();

        return $page;
    }

    /**
     * @param LocationSiloPage $locationSiloPage
     * @return Collection
     */
    public function getStatePagesByNationalSiloPage(LocationSiloPage $locationSiloPage): Collection
    {
        return $this->getBaseJoinedQuery()
            ->select(LocationSiloPage::TABLE . '.*')
            ->with(LocationSiloPage::RELATION_LOCATION)
            ->where(Silo::FIELD_ROOT_PATH, $locationSiloPage->silo->{Silo::FIELD_ROOT_PATH})
            ->where(Silo::FIELD_WEBSITE_ID, $locationSiloPage->silo->{Silo::FIELD_WEBSITE_ID})
            ->where(Silo::TABLE .'.'. Silo::FIELD_IS_ACTIVE,  '=', true)
            ->where(LocationSiloPage::FIELD_LOCATION_TYPE, LocationSiloPageLocationType::STATE)
            ->where(LocationSiloPage::TABLE .'.'. LocationSiloPage::FIELD_IS_ACTIVE, true)
            ->get();
    }

    /**
     * @param LocationSiloPage $locationSiloPage
     * @return Collection
     */
    public function getCityPagesByStateSiloPage(LocationSiloPage $locationSiloPage): Collection
    {
        return $this->getBaseJoinedQuery()
            ->select(LocationSiloPage::TABLE . '.*')
            ->with(LocationSiloPage::RELATION_LOCATION)
            ->where(Silo::FIELD_ROOT_PATH, $locationSiloPage->silo->{Silo::FIELD_ROOT_PATH})
            ->where(Silo::FIELD_WEBSITE_ID, $locationSiloPage->silo->{Silo::FIELD_WEBSITE_ID})
            ->where(Silo::TABLE .'.'. Silo::FIELD_IS_ACTIVE,  '=', true)
            ->where(LocationSiloPage::FIELD_PARENT_LOCATION_ID, $locationSiloPage->{LocationSiloPage::FIELD_LOCATION_ID})
            ->where(LocationSiloPage::FIELD_LOCATION_TYPE, LocationSiloPageLocationType::CITY)
            ->where(LocationSiloPage::TABLE .'.'. LocationSiloPage::FIELD_IS_ACTIVE, true)
            ->get();
    }

    /**
     * @param string $siloRootPath
     * @return array
     */
    public function getFullPathsBySiloRootPath(string $siloRootPath): array
    {
        return $this->getBaseJoinedQuery()
            ->select(DB::raw(DB::raw("CONCAT(" . Silo::TABLE . '.' . Silo::FIELD_ROOT_PATH . ", " . LocationSiloPage::FIELD_RELATIVE_PATH . ') as ' . self::FIELD_AS_FULL_PATH)))
            ->where(Silo::FIELD_ROOT_PATH, $siloRootPath)
            ->where(Silo::TABLE .'.'. Silo::FIELD_IS_ACTIVE,  '=', true)
            ->where(LocationSiloPage::TABLE .'.'. LocationSiloPage::FIELD_IS_ACTIVE, true)
            ->pluck(self::FIELD_AS_FULL_PATH)
            ->toArray();
    }

    /**
     * @param int $locationPageId
     * @param array $attributes
     * @return Silo
     */
    public function updateLocationPageSiloById(int $locationPageId, array $attributes): Silo
    {
        /** @var LocationSiloPage $locationPage */
        $locationPage = LocationSiloPage::query()
            ->findOrFail($locationPageId);
        $locationPage->update($attributes);

        return $locationPage->silo;
    }
}
