<?php

namespace App\Repositories\Odin;

use App\Enums\Odin\QualityTier as QualityTierEnum;
use App\Enums\Odin\SaleTypes as SaleTypesEnum;
use App\Models\Legacy\Location;
use App\Models\Odin\ProductCountyBidPrice;
use App\Models\Odin\ProductCountyFloorPrice;
use App\Models\Odin\ProductStateBidPrice;
use App\Models\Odin\ProductStateFloorPrice;
use App\Models\Odin\QualityTier;
use App\Repositories\LocationRepository;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class ProductPriceRepository
{
    const FLOOR_PRICE = 'floor_price';
    const BID_PRICE = 'bid_price';

    public function __construct(protected LocationRepository $locationRepository) {}

    /**
     * @param int $stateLocationId
     * @param int $serviceProductId
     * @param int|null $qualityTierId
     * @param int|null $propertyTypeId
     * @param int|null $saleTypeId
     * @param array $relations
     * @return Collection
     */
    public function getStateFloorPricesForServiceProduct(
        int  $stateLocationId,
        int  $serviceProductId,
        ?int $qualityTierId = null,
        ?int $propertyTypeId = null,
        ?int $saleTypeId = null,
        array $relations = [],
    ): Collection
    {
        return $this->getStateFloorPriceQueryForServiceProduct(
            stateLocationId: $stateLocationId,
            serviceProductId: $serviceProductId,
            qualityTierId: $qualityTierId,
            propertyTypeId: $propertyTypeId,
            saleTypeId: $saleTypeId,
            relations: $relations
        )->get();
    }

    /**
     * @param int $stateLocationId
     * @param int $productCampaignId
     * @param int $serviceProductId
     * @param int|null $qualityTierId
     * @param int|null $propertyTypeId
     * @param int|null $saleTypeId
     *
     * @return Collection<ProductStateBidPrice>
     */
    public function getStateBidPricesForCampaignAndServiceProduct(
        int  $stateLocationId,
        int  $productCampaignId,
        int  $serviceProductId,
        ?int $qualityTierId = null,
        ?int $propertyTypeId = null,
        ?int $saleTypeId = null
    ): Collection
    {
        $query = ProductStateBidPrice::query()
            ->where(ProductStateBidPrice::FIELD_STATE_LOCATION_ID, $stateLocationId)
            ->where(ProductStateBidPrice::FIELD_PRODUCT_CAMPAIGN_ID, $productCampaignId)
            ->where(ProductStateBidPrice::FIELD_SERVICE_PRODUCT_ID, $serviceProductId);

        if ($qualityTierId) {
            $query->where(ProductStateBidPrice::FIELD_QUALITY_TIER_ID, $qualityTierId);
        }
        if ($propertyTypeId) {
            $query->where(ProductStateBidPrice::FIELD_PROPERTY_TYPE_ID, $propertyTypeId);
        }
        if ($saleTypeId) {
            $query->where(ProductStateBidPrice::FIELD_SALE_TYPE_ID, $saleTypeId);
        }

        return $query->get();
    }

    /**
     * @param int $stateLocationId
     * @param int $serviceProductId
     * @param int|null $countyLocationId
     * @param int|null $qualityTierId
     * @param int|null $propertyTypeId
     * @param int|null $saleTypeId
     * @param array $relations
     * @return Collection
     */
    public function getCountyFloorPricesForServiceProduct(
        int  $stateLocationId,
        int  $serviceProductId,
        ?int $countyLocationId = null,
        ?int $qualityTierId = null,
        ?int $propertyTypeId = null,
        ?int $saleTypeId = null,
        array $relations = []
    ): Collection
    {
        $query = $this->getCountyFloorPriceQueryForServiceProduct(
            stateLocationId: $stateLocationId,
            serviceProductId: $serviceProductId,
            countyLocationId: $countyLocationId,
            qualityTierId: $qualityTierId,
            propertyTypeId: $propertyTypeId,
            saleTypeId: $saleTypeId,
            relations: $relations,
        );

        return $query->get();
    }

    /**
     * Handles fetching prices for a county with state prices as fallbacks
     * Will have a ->county_price property if there ia county floor price override exists, otherwise ->price is the state floor price
     *
     * @param int $stateLocationId
     * @param int $countyLocationId
     * @param int $serviceProductId
     * @param int|null $qualityTierId
     * @param int|null $propertyTypeId
     * @param array $relations
     * @return Collection
     */
    public function getCountyFloorAndInheritedPricesForServiceProduct(
        int  $stateLocationId,
        int  $countyLocationId,
        int  $serviceProductId,
        ?int $qualityTierId = null,
        ?int $propertyTypeId = null,
        array $relations = []
    ): Collection
    {
        $query = $this->getStateFloorPriceQueryForServiceProduct(
            stateLocationId: $stateLocationId,
            serviceProductId: $serviceProductId,
            qualityTierId: $qualityTierId,
            propertyTypeId: $propertyTypeId,
            relations: $relations,
        );

        $query->leftJoin(ProductCountyFloorPrice::TABLE, function (JoinClause $join) use ($countyLocationId) {
            $join->on(ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_STATE_LOCATION_ID, '=', ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_STATE_LOCATION_ID)
                ->on(ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_SALE_TYPE_ID, '=', ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_SALE_TYPE_ID)
                ->on(ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID, '=', ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_PROPERTY_TYPE_ID)
                ->on(ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_QUALITY_TIER_ID, '=', ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_QUALITY_TIER_ID)
                ->on(ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID, '=', ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID)
                ->where(ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_COUNTY_LOCATION_ID, $countyLocationId);
            })->select([
                ProductStateFloorPrice::TABLE . '.*',
                ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_PRICE . ' as county_price',
            ]);

        return $query->get();
    }

    /**
     * @param int $stateLocationId
     * @param int $serviceProductId
     * @param int|null $qualityTierId
     * @param int|null $propertyTypeId
     * @param int|null $saleTypeId
     * @param array $relations
     * @return Builder
     */
    protected function getStateFloorPriceQueryForServiceProduct(
        int  $stateLocationId,
        int  $serviceProductId,
        ?int $qualityTierId = null,
        ?int $propertyTypeId = null,
        ?int $saleTypeId = null,
        array $relations = []
    ): Builder
    {
        // constrain to valid quality & sale types for this service product
        $qualityTierIds = $qualityTierId
            ? [$qualityTierId]
            : QualityTierEnum::byServiceProductId($serviceProductId, QualityTierEnum::RETURN_TYPE_ID);
        $saleTypeIds = $saleTypeId
            ? [$saleTypeId]
            : SaleTypesEnum::byServiceProductId($serviceProductId, SaleTypesEnum::RETURN_TYPE_ID);

        $query = ProductStateFloorPrice::query()
            ->with($relations)
            ->where(ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_STATE_LOCATION_ID, $stateLocationId)
            ->where(ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID, $serviceProductId)
            ->whereIn(ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_QUALITY_TIER_ID, $qualityTierIds)
            ->whereIn(ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_SALE_TYPE_ID, $saleTypeIds);

        if ($propertyTypeId) {
            $query->where(ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID, $propertyTypeId);
        }

        return $query;
    }

    /**
     * @param int $stateLocationId
     * @param int $serviceProductId
     * @param int|null $countyLocationId
     * @param int|null $qualityTierId
     * @param int|null $propertyTypeId
     * @param int|null $saleTypeId
     * @param array $relations
     * @return Builder
     */
    protected function getCountyFloorPriceQueryForServiceProduct(
        int  $stateLocationId,
        int  $serviceProductId,
        ?int $countyLocationId = null,
        ?int $qualityTierId = null,
        ?int $propertyTypeId = null,
        ?int $saleTypeId = null,
        array $relations = []
    ): Builder
    {
        $query = ProductCountyFloorPrice::query()
            ->with($relations)
            ->where(ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_STATE_LOCATION_ID, $stateLocationId)
            ->where(ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID, $serviceProductId);

        if ($countyLocationId) {
            $query->where(ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_COUNTY_LOCATION_ID, $countyLocationId);
        }

        if ($qualityTierId) {
            $query->where(ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_QUALITY_TIER_ID, $qualityTierId);
        }

        if ($propertyTypeId) {
            $query->where(ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_PROPERTY_TYPE_ID, $propertyTypeId);
        }

        if ($saleTypeId) {
            $query->where(ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_SALE_TYPE_ID, $saleTypeId);
        }

        return $query;
    }

    /**
     * @param int $stateLocationId
     * @param int $productCampaignId
     * @param int $serviceProductId
     * @param int|null $countyLocationId
     * @param int|null $qualityTierId
     * @param int|null $propertyTypeId
     * @param int|null $saleTypeId
     *
     * @return Collection<ProductCountyBidPrice>
     */
    public function getCountyBidPricesForCampaignAndServiceProduct(
        int  $stateLocationId,
        int  $productCampaignId,
        int  $serviceProductId,
        ?int $countyLocationId = null,
        ?int $qualityTierId = null,
        ?int $propertyTypeId = null,
        ?int $saleTypeId = null
    ): Collection
    {
        $query = ProductCountyBidPrice::query()
            ->where(ProductCountyBidPrice::FIELD_STATE_LOCATION_ID, $stateLocationId)
            ->where(ProductCountyBidPrice::FIELD_PRODUCT_CAMPAIGN_ID, $productCampaignId)
            ->where(ProductCountyBidPrice::FIELD_SERVICE_PRODUCT_ID, $serviceProductId);

        if ($countyLocationId) {
            $query->where(ProductCountyBidPrice::FIELD_COUNTY_LOCATION_ID, $countyLocationId);
        }
        if ($qualityTierId) {
            $query->where(ProductCountyBidPrice::FIELD_QUALITY_TIER_ID, $qualityTierId);
        }
        if ($propertyTypeId) {
            $query->where(ProductCountyBidPrice::FIELD_PROPERTY_TYPE_ID, $propertyTypeId);
        }
        if ($saleTypeId) {
            $query->where(ProductCountyBidPrice::FIELD_SALE_TYPE_ID, $saleTypeId);
        }

        return $query->get();
    }

    /**
     * @param int $stateLocationId
     * @param int $campaignId
     * @param int $serviceProductId
     * @param int $qualityTierId
     * @param int $propertyTypeId
     * @param int $saleTypeId
     * @param float $price
     * @param bool $updateCountyBidPrices
     * @param bool $returnInitialPrice
     *
     * @return ?float
     * @throws Exception
     */
    public function updateStateBidPrice(
        int   $stateLocationId,
        int   $campaignId,
        int   $serviceProductId,
        int   $qualityTierId,
        int   $propertyTypeId,
        int   $saleTypeId,
        float $price,
        bool  $updateCountyBidPrices = false,
        ?bool  $returnInitialPrice = false,
    ): ?float
    {
        $floorPrice = ProductStateFloorPrice::query()
                ->where(ProductStateFloorPrice::FIELD_STATE_LOCATION_ID, $stateLocationId)
                ->where(ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID, $serviceProductId)
                ->where(ProductStateFloorPrice::FIELD_QUALITY_TIER_ID, $qualityTierId)
                ->where(ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID, $propertyTypeId)
                ->where(ProductStateFloorPrice::FIELD_SALE_TYPE_ID, $saleTypeId)
                ->first()?->price;

        if ($floorPrice > $price) {
            throw new Exception('Bid price cannot be less than floor price');
        }

        $priceQueryParameters = [
            ProductStateBidPrice::FIELD_STATE_LOCATION_ID   => $stateLocationId,
            ProductStateBidPrice::FIELD_PRODUCT_CAMPAIGN_ID => $campaignId,
            ProductStateBidPrice::FIELD_SERVICE_PRODUCT_ID  => $serviceProductId,
            ProductStateBidPrice::FIELD_QUALITY_TIER_ID     => $qualityTierId,
            ProductStateBidPrice::FIELD_PROPERTY_TYPE_ID    => $propertyTypeId,
            ProductStateBidPrice::FIELD_SALE_TYPE_ID        => $saleTypeId,
        ];

        /** @var ProductStateBidPrice|null $existingStatePrice */
        $existingStatePrice = ProductStateBidPrice::query()
            ->where($priceQueryParameters)
            ->first();

        $initialBidPrice = $existingStatePrice?->price ?? $floorPrice ?? null;

        $updated = $existingStatePrice
            ? $existingStatePrice->update([ProductStateBidPrice::FIELD_PRICE => $price])
            : ProductStateBidPrice::query()
                ->create([...$priceQueryParameters, ProductStateBidPrice::FIELD_PRICE => $price]);



        if ($updateCountyBidPrices)
            $this->updateCountyBidPriceForState(
                $stateLocationId,
                $campaignId,
                $serviceProductId,
                $qualityTierId,
                $propertyTypeId,
                $saleTypeId,
                $price
            );

        return $updated && $returnInitialPrice
            ? $initialBidPrice
            : null;
    }

    /**
     * @param int $stateLocationId
     * @param int $productCampaignId
     * @param int $serviceProductId
     * @param int $qualityTierId
     * @param int $propertyTypeId
     * @param int $saleTypeId
     * @param float $price
     *
     * @return void
     */
    public function updateCountyBidPriceForState(
        int   $stateLocationId,
        int   $productCampaignId,
        int   $serviceProductId,
        int   $qualityTierId,
        int   $propertyTypeId,
        int   $saleTypeId,
        float $price
    ): void
    {
        $stateLocation = $this->locationRepository->getStateByIdOrFail($stateLocationId);
        $countyLocations = $this->locationRepository->getCountiesInState($stateLocation->state_key)->pluck(Location::ID);

        ProductCountyBidPrice::query()
            ->where(ProductCountyBidPrice::FIELD_STATE_LOCATION_ID, $stateLocationId)
            ->where(ProductCountyBidPrice::FIELD_PRODUCT_CAMPAIGN_ID, $productCampaignId)
            ->where(ProductCountyBidPrice::FIELD_SERVICE_PRODUCT_ID, $serviceProductId)
            ->where(ProductCountyBidPrice::FIELD_QUALITY_TIER_ID, $qualityTierId)
            ->where(ProductCountyBidPrice::FIELD_PROPERTY_TYPE_ID, $propertyTypeId)
            ->where(ProductCountyBidPrice::FIELD_SALE_TYPE_ID, $saleTypeId)
            ->whereIn(ProductCountyBidPrice::FIELD_COUNTY_LOCATION_ID, $countyLocations)
            ->delete();
        $updateOrCreateData = collect();

        if ($countyLocations->isEmpty()) return;

        foreach ($countyLocations as $countyLocationId) {
            $updateOrCreateData->push([
                ProductCountyBidPrice::FIELD_STATE_LOCATION_ID   => $stateLocationId,
                ProductCountyBidPrice::FIELD_PRODUCT_CAMPAIGN_ID => $productCampaignId,
                ProductCountyBidPrice::FIELD_SERVICE_PRODUCT_ID  => $serviceProductId,
                ProductCountyBidPrice::FIELD_COUNTY_LOCATION_ID  => $countyLocationId,
                ProductCountyBidPrice::FIELD_QUALITY_TIER_ID     => $qualityTierId,
                ProductCountyBidPrice::FIELD_PROPERTY_TYPE_ID    => $propertyTypeId,
                ProductCountyBidPrice::FIELD_SALE_TYPE_ID        => $saleTypeId,
                ProductCountyBidPrice::FIELD_PRICE               => $price
            ]);
        }

        ProductCountyBidPrice::query()->insert(
            $updateOrCreateData->toArray(),
        );
    }

    /**
     * @param int $stateLocationId
     * @param int $campaignId
     * @param int $serviceProductId
     * @param int $countyLocationId
     * @param int $qualityTierId
     * @param int $propertyTypeId
     * @param int $saleTypeId
     * @param float $price
     * @param ?bool $returnInitialPrice
     *
     * @return ?float
     * @throws Exception
     */
    public function updateCountyBidPrice(
        int   $stateLocationId,
        int   $campaignId,
        int   $serviceProductId,
        int   $countyLocationId,
        int   $qualityTierId,
        int   $propertyTypeId,
        int   $saleTypeId,
        float $price,
        ?bool  $returnInitialPrice = false,
    ): ?float
    {
        $floorPrice = ProductCountyFloorPrice::query()
            ->where(ProductCountyFloorPrice::FIELD_STATE_LOCATION_ID, $stateLocationId)
            ->where(ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID, $serviceProductId)
            ->where(ProductCountyFloorPrice::FIELD_COUNTY_LOCATION_ID, $countyLocationId)
            ->where(ProductCountyFloorPrice::FIELD_QUALITY_TIER_ID, $qualityTierId)
            ->where(ProductCountyFloorPrice::FIELD_PROPERTY_TYPE_ID, $propertyTypeId)
            ->where(ProductCountyFloorPrice::FIELD_SALE_TYPE_ID, $saleTypeId)
            ->first()?->price ?? null;

        if ($floorPrice > $price) {
            throw new Exception('Bid price cannot be less than floor price');
        }

        $priceQueryParameters = [
            ProductCountyBidPrice::FIELD_STATE_LOCATION_ID   => $stateLocationId,
            ProductCountyBidPrice::FIELD_PRODUCT_CAMPAIGN_ID => $campaignId,
            ProductCountyBidPrice::FIELD_SERVICE_PRODUCT_ID  => $serviceProductId,
            ProductCountyBidPrice::FIELD_COUNTY_LOCATION_ID  => $countyLocationId,
            ProductCountyBidPrice::FIELD_QUALITY_TIER_ID     => $qualityTierId,
            ProductCountyBidPrice::FIELD_PROPERTY_TYPE_ID    => $propertyTypeId,
            ProductCountyBidPrice::FIELD_SALE_TYPE_ID        => $saleTypeId
        ];

        /** @var ProductCountyBidPrice | null $existingCountyPrice */
        $existingCountyPrice = ProductCountyBidPrice::query()->where($priceQueryParameters)->first();

        $initialPrice = $existingCountyPrice?->price ?? $floorPrice;

        $updated = $existingCountyPrice
            ? $existingCountyPrice->update([ProductCountyBidPrice::FIELD_PRICE => $price])
            : ProductCountyBidPrice::query()
                ->create([...$priceQueryParameters, ProductCountyBidPrice::FIELD_PRICE => $price]);

        return $updated && $returnInitialPrice
            ? $initialPrice
            : null;
    }

    /**
     * @param Collection<ProductStateFloorPrice|ProductCountyFloorPrice> $floorPrices
     * @param Collection<ProductStateBidPrice|ProductCountyBidPrice> $bidPrices
     * @param bool $countyPrice
     *
     * @return Collection
     */
    public function transformAndGroupPricesByQualityTier(Collection $floorPrices, Collection $bidPrices, bool $countyPrice = false): Collection
    {
        $groupedFloorPrices = $floorPrices->groupBy(fn(ProductCountyFloorPrice|ProductStateFloorPrice $price) => $price->quality_tier_id);
        $groupedBidPrices = $bidPrices->groupBy(fn(ProductCountyBidPrice|ProductStateBidPrice $price) => $price->quality_tier_id);
        $groupedPrices = collect();

        foreach ($groupedFloorPrices as $qualityTierId => $prices) {
            /** @var QualityTier $qualityTier */
            $qualityTier = QualityTier::query()->findOrFail($qualityTierId);

            $groupedPrices->push([
                $qualityTier->name => $prices->map(
                    fn(ProductCountyFloorPrice|ProductStateFloorPrice $price) => $this->addBidPrice($price, $groupedBidPrices->get($qualityTierId), $countyPrice)
                )
            ]);
        }

        return $groupedPrices->collapse();
    }

    /**
     * @param ProductCountyFloorPrice|ProductStateFloorPrice $floorPrice
     * @param Collection|null $bidPrices
     * @param bool $countyPrice
     *
     * @return array
     */
    protected function addBidPrice(ProductCountyFloorPrice|ProductStateFloorPrice $floorPrice, ?Collection $bidPrices = null, bool $countyPrice = false): array
    {
        $price = Arr::except($floorPrice->toArray(), ['price', 'created_at', 'updated_at']);

        $price[self::FLOOR_PRICE] = $floorPrice->price;
        $price[self::BID_PRICE] = $bidPrices?->first(function (ProductCountyBidPrice|ProductStateBidPrice $bidPrice) use ($floorPrice, $countyPrice) {
            $status = $bidPrice->state_location_id === $floorPrice->state_location_id
                && $bidPrice->service_product_id === $floorPrice->service_product_id
                && $bidPrice->sale_type_id === $floorPrice->sale_type_id
                && $bidPrice->quality_tier_id === $floorPrice->quality_tier_id
                && $bidPrice->property_type_id === $floorPrice->property_type_id;

            if ($countyPrice) $status = $status && $bidPrice->county_location_id === $floorPrice->county_location_id;

            return $status;
        })?->price;

        return $price;
    }

    /**
     * @param Collection $floorPrices
     * @param Collection $bidPrices
     * @return Collection
     */
    public function transformAndGroupPrices(Collection $floorPrices, Collection $bidPrices): Collection
    {
        $transformedPrices = $floorPrices->groupBy([
            fn($price) => $price->serviceProduct->product->name,
            fn($price) => $price->propertyType->name,
            fn($price) => $price->qualityTier->name,
            fn($price) => $price->stateLocation->state_abbr,
            fn($price) => $price->countyLocation?->county_key ?? 'state',
            fn($price) => $price->saleType->key
        ])->collect();

        $groupedBidPrices = $bidPrices->groupBy([
            fn($price) => $price->serviceProduct->product->name,
            fn($price) => $price->propertyType->name,
            fn($price) => $price->qualityTier->name,
            fn($price) => $price->stateLocation->state_abbr,
            fn($price) => $price->countyLocation?->county_key ?? 'state',
            fn($price) => $price->saleType->key
        ]);

        foreach($floorPrices as $floorPrice) {
            $productName = $floorPrice->serviceProduct->product->name;
            $propertyTypeName = $floorPrice->propertyType->name;
            $qualityTierName = $floorPrice->qualityTier->name;
            $stateKey = $floorPrice->stateLocation->state_abbr;
            $stateCountyKey = $floorPrice->countyLocation?->county_key ?? 'state';
            $saleTypeKey = $floorPrice->saleType->key;

            $transformedPrices
                ->get($productName)
                ->get($propertyTypeName)
                ->get($qualityTierName)
                ->get($stateKey)
                ->get($stateCountyKey)
                ->put(
                    $saleTypeKey,
                    [
                        self::FLOOR_PRICE => $floorPrice->price,
                        self::BID_PRICE => $groupedBidPrices
                            ?->get($productName)
                            ?->get($propertyTypeName)
                            ?->get($qualityTierName)
                            ?->get($stateKey)
                            ?->get($stateCountyKey)
                            ?->get($saleTypeKey)
                            ?->first()
                            ?->price ?? $floorPrice->county_price ?? $floorPrice->price
                    ]
                );
        }

        return $transformedPrices;
    }


}
