<?php

namespace App\Repositories\Odin;

use App\Builders\Odin\CompanyBuilder;
use App\Builders\Odin\CompanyContractBuilder;
use App\Campaigns\Modules\Legacy\LegacyModule;
use App\DataModels\Odin\Prices\BudgetUsageData;
use App\Enums\Campaigns\CampaignStatus;
use App\Enums\Company\CompanyAdminStatus;
use App\Enums\CompanyConsolidatedStatus;
use App\Enums\LegacyCompanyAdminStatus;
use App\Enums\LegacyCompanyBasicStatus;
use App\Enums\Odin\BudgetCategory;
use App\Enums\Odin\CompanyChangeLogType;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\Odin\IndustryServiceSlug;
use App\Enums\PermissionType;
use App\Enums\RoleType;
use App\Enums\SupportedTimezones;
use App\Http\Controllers\API\CompaniesController;
use App\Http\Resources\ActionResource;
use App\Jobs\VerifyCompaniesWebsiteJob;
use App\Models\Action;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\CompanyCampaignRelation;
use App\Models\CompanyContract;
use App\Models\AccountManager;
use App\Models\AccountManagerClient;
use App\Models\CompanyOptInName;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentHistoryLog;
use App\Models\Legacy\EloquentInvoice;
use App\Models\Legacy\EloquentInvoiceItem;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\EloquentReview;
use App\Models\Legacy\EloquentUser;
use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyConfiguration;
use App\Models\Odin\CompanyData;
use App\Models\Odin\CompanyIndustry;
use App\Models\Odin\CompanyLink;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\CompanyReview;
use App\Models\Odin\CompanyService;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ProductCampaignBudget;
use App\Models\Territory\RelationshipManager;
use App\Models\User;
use App\Services\Billing\CompanyBillingServiceV4;
use App\Services\DatabaseHelperService;
use App\Services\ImpersonateService;
use App\Services\Odin\CompanyChangeLogService;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Throwable;
use App\Enums\Odin\QualityTier as QualityTierEnum;

class CompanyRepository
{

    const CONTRACT_APPROVAL = "contract approval";

    /**
     * @param \App\Repositories\Legacy\CompanyRepository $legacyCompanyRepository
     * @param AddressRepository $addressRepository
     * @param CompanyLocationRepository $companyLocationRepository
     * @param IndustryRepository $industryRepository
     * @param ProductRepository $productRepository
     */
    public function __construct(
        protected \App\Repositories\Legacy\CompanyRepository $legacyCompanyRepository,
        protected AddressRepository                          $addressRepository,
        protected CompanyLocationRepository                  $companyLocationRepository,
        protected IndustryRepository                         $industryRepository,
        protected ProductRepository                          $productRepository
    )
    {
    }

    /**
     * Sync Company creation from Registration with matching ID
     *
     * @param array $data
     * @param int $primaryKey
     * @return Company|null
     */
    public function createCompanyWithPrimaryKey(array $data, int $primaryKey): ?Company
    {
        $keyAlreadyTaken = Company::query()->find($primaryKey);

        if ($primaryKey > 0 && !$keyAlreadyTaken) {
            try {
                $newCompany = new Company();
                $newCompany[Company::FIELD_ID] = $primaryKey;
                $newCompany->fill($data);
                if ($newCompany->save()) {
                    return $newCompany;
                }
            }
            catch(Throwable $error) {
                logger()->error("An error occurred while attempting to sync Company {$primaryKey} from Registration:\n\t{$error->getMessage()}");
            }
        }
        else {
            logger()->warning("Received a Company registration event but primary key '{$primaryKey}' was in use. Using next available primary key.");
            $data[Company::FIELD_LEGACY_ID] = $primaryKey;
            return $this->createCompanyFromAttributes($data);
        }
        return null;
    }

    /**
     * Handles creating a new company user
     *
     * @param array $data
     * @return Company
     */
    public function createCompanyFromAttributes(array $data = []): Company
    {
        $company = new Company();
        $company->fill($data);
        $company->save();

        return $company;
    }

    /**
     * Returns a company by the legacy id or fails.
     *
     * @param int $legacyId
     * @return Company
     */
    public function getCompanyByLegacyIdOrFail(int $legacyId): Company
    {
        /** @var Company $company */
        $company = Company::query()->where(Company::FIELD_LEGACY_ID, $legacyId)->firstOrFail();

        return $company;
    }

    /**
     * @param int $companyId
     * @return Company|null
     */
    public function find(int $companyId)
    {
        return Company::query()->find($companyId);
    }

    /**
     * @param int $companyId
     * @return EloquentCompany
     */
    public function findLegacyCompanyByOdinIdOrFail(int $companyId): EloquentCompany
    {
        $legacyId = $this->findOrFail($companyId)->{Company::FIELD_LEGACY_ID};
        /** @var EloquentCompany $legacyCompany */
        $legacyCompany = EloquentCompany::query()->findOrFail($legacyId);
        return $legacyCompany;
    }

    /**
     * Handles creating company data.
     *
     * @param Company $company
     * @param array $data
     * @return CompanyData
     */
    public function createCompanyFieldData(Company $company, array $data): CompanyData
    {
        /** @var CompanyData $companyData */
        $companyData = $company->data()->updateOrCreate(
            [CompanyData::FIELD_COMPANY_ID => $company->id],
            [
                CompanyData::FIELD_COMPANY_ID => $company->id,
                CompanyData::FIELD_PAYLOAD => $data
            ],
        );

        return $companyData;
    }

    /**
     * Update model-type data on the Company model
     *
     * @param Company $company
     * @param array $data
     * @return int
     */
    public function updateCompanyModel(Company $company, array $data): bool
    {
        if (count($data) === 0) return true;

        return $company->update($data);
    }

    /**
     * Update a Company's field-type relational data in CompanyData
     *
     * @param Company $company
     * @param array $updateData
     * @return bool
     */
    public function updateCompanyData(Company $company, array $updateData): bool
    {
        if (count($updateData) === 0) return true;

        /** @var CompanyData $companyData */
        $companyData = $company->data()->firstOrCreate([CompanyData::FIELD_COMPANY_ID => $company->id]);
        $companyData->{CompanyData::FIELD_PAYLOAD} = $companyData->payload ? array_merge($companyData->payload, $updateData) : $updateData;
        return $companyData->save();
    }

    /**
     * @param int $id
     *
     * @return Company
     */
    public function findOrFail(int $id): Company
    {
        /** @var Company $company */
        $company = Company::query()->findOrFail($id);

        return $company;
    }

    /**
     * @param int $legacyId
     * @return Company
     */
    public function findByLegacyIdOrFail(int $legacyId): Company
    {
        /** @var Company $company */
        $company = Company::query()->where(Company::FIELD_LEGACY_ID, $legacyId)->firstOrFail();

        return $company;
    }

    /**]
     * @param Company $company
     * @param string $key
     * @param mixed $default
     *
     * @return mixed
     */
    public function getCompanyDataByKey(Company $company, string $key, mixed $default = null): mixed
    {
        if (!$company->data) return $default;

        return $company->data->payload[$key] ?? $default;
    }

    /**
     * @param mixed $companyName
     * @return Builder
     */
    public function getCompaniesWithSearchFilters(
        Mixed $companyName,
        Mixed $entityName,
        Mixed $campaignStatus = null,
        Mixed $state = null,
        Int|null $status = null,
        Int|null $adminId = null,
        Array|null $adminStatus = null,
        Array|null $serviceType = null,
        mixed $paymentMethod,
        mixed $superPremiumOptIn,
        Mixed $sortCol = null,
        Mixed $sortDir = null,
    ): Builder
    {
        return CompanyBuilder::query()
            ->forCampaignStatus($campaignStatus)
            ->forCompanyName($companyName)
            ->forEntityName($entityName)
            ->forState($state)
            ->forStatus($status)
            ->forAccountManager($adminId)
            ->forAdminStatus($adminStatus)
            ->forServiceType($serviceType)
            ->forPaymentMethod($paymentMethod)
            ->forSuperPremiumOptIn($superPremiumOptIn)
            ->sortColumn($sortCol)
            ->sortDirection($sortDir)
            ->getQuery();
    }

    /**
     * @param Company $company
     * @param Collection $data
     *
     * @return bool
     */
    public function updateBasicDetails(Company $company, Collection $data): bool
    {
        $companyCopy = $company->replicate();
        /** @var User $user */
        $user = Auth::user();

        if ($data->has(Company::FIELD_NAME)) $company->name = $data->pull(Company::FIELD_NAME);
        if ($data->has(Company::FIELD_WEBSITE)) $company->website = $data->pull(Company::FIELD_WEBSITE);
        if ($data->has(Company::FIELD_SALES_STATUS)) $company->sales_status = $data->pull(Company::FIELD_SALES_STATUS);
        if ($data->has(Company::FIELD_ADMIN_APPROVED)) $company->admin_approved = (int)$data->pull(Company::FIELD_ADMIN_APPROVED);
        if ($data->has(Company::FIELD_ADMIN_LOCKED)) $company->admin_locked = (int)$data->pull(Company::FIELD_ADMIN_LOCKED);
        if ($data->has(Company::FIELD_SOURCED_FROM)) $company->sourced_from = $data->pull(Company::FIELD_SOURCED_FROM);

        $shouldUpdateAdminStatus = $this->shouldUpdateAminStatus($company, $data, $user);

        if ($shouldUpdateAdminStatus) {
            $company->admin_status = $data->pull(Company::FIELD_ADMIN_STATUS);
        }
        if ($data->has(Company::FIELD_STATUS) && $user->hasPermissionTo(PermissionType::COMPANY_BASIC_STATUS_EDIT)) {
            $company->status = $data->pull(Company::FIELD_STATUS);
        }

        if ($company->save() && $company->wasChanged(Company::FIELD_WEBSITE)) {
            VerifyCompaniesWebsiteJob::dispatch(VerifyCompaniesWebsiteJob::PROCESS_RECORD, $company);
        }
        $this->updateCompanyData($company, $data->toArray());

        if ($shouldUpdateAdminStatus) {
            $this->logAdminStatusUpdate(
                company: $company,
                oldStatus: $companyCopy->admin_status,
                newStatus: $company->admin_status,
                reason: $data->pull(CompaniesController::REQUEST_ADMIN_STATUS_CHANGE_REASON, '')
            );
        }

        return true;
    }

    /**
     * @param array $data
     *
     * @return bool
     */
    public function updateCompanyPhoneOrAddress(array $data): bool
    {
        if ($data[Address::FIELD_ADDRESS_1]) {
            if (!$data[CompanyLocation::FIELD_ADDRESS_ID]) {
                $data[CompanyLocation::FIELD_ADDRESS_ID] = $this->addressRepository->createAddressFromAttributes([
                    Address::FIELD_ADDRESS_1 => $data[Address::FIELD_ADDRESS_1],
                    Address::FIELD_ADDRESS_2 => $data[Address::FIELD_ADDRESS_2],
                    Address::FIELD_CITY => $data[Address::FIELD_CITY],
                    Address::FIELD_STATE => $data[Address::FIELD_STATE],
                    Address::FIELD_ZIP_CODE => $data[Address::FIELD_ZIP_CODE]
                ])?->id;
            } else {
                $this->addressRepository->updateAddress($this->addressRepository->findByIdOrFail($data[CompanyLocation::FIELD_ADDRESS_ID]), $data);
            }

            if(empty($data[CompanyLocation::FIELD_ADDRESS_ID])) {
                return false;
            }

            // Restrict creating a new location if the location ID is given & deemed valid
            if(!empty($data[CompanyLocation::FIELD_ID])) {
                $companyLocation = $this->companyLocationRepository->getCompanyLocationById($data[CompanyLocation::FIELD_ID]);
                if(!empty($companyLocation)) {
                    $companyLocation->address_id = $data[CompanyLocation::FIELD_ADDRESS_ID];
                    $companyLocation->phone      = $data[CompanyLocation::FIELD_PHONE];
                    $companyLocation->name       = $data[CompanyLocation::FIELD_NAME];
                    $companyLocation->save();

                    return true;
                }
            }

            $this->companyLocationRepository->updateOrCreateCompanyLocation($data);
        }

        return true;
    }

    /**
     * @return Collection|Company[]|null
     */
    public function getCompaniesWithZoomInfoID(): Collection|Company|null
    {
        return Company::query()
            ->whereHas(Company::RELATION_DATA, function ($has) {
                $has->whereNotNull(CompanyData::FIELD_PAYLOAD . '->zoom_info_id');
            })
            ->get();
    }

    /**
     * @param string $zoomInfoID
     * @return Company|null
     */
    public function getCompanyByZoomInfoID(string $zoomInfoID): Company|null
    {
        return Company::query()
            ->whereHas(Company::RELATION_DATA, function($has) use ($zoomInfoID) {
                $has->where(CompanyData::FIELD_PAYLOAD.'->zoom_info_id', $zoomInfoID);
            })
            ->get()
            ->first();
    }

    /**
     * @return Collection|Company[]|null
     */
    public function getCompaniesWithZoomInfoIDWithoutZoomInfoData(): Collection|Company|null
    {
        return Company::query()
            ->whereHas(Company::RELATION_DATA, function($has) {
                $has->whereNotNull(CompanyData::FIELD_PAYLOAD.'->zoom_info_id')
                    ->whereNull(CompanyData::FIELD_PAYLOAD.'->employee_count')
                    ->orWhere(function($query) {
                        $query->whereNotNull(CompanyData::FIELD_PAYLOAD.'->zoom_info_id')
                            ->whereNull(CompanyData::FIELD_PAYLOAD.'->revenue_in_thousands');
                    });
            })
            ->get();
    }

    /**
     * Update Company's prescreened value - generate a new timestamp if true, otherwise null
     *
     * @param Company $company
     * @param mixed $newValue
     * @return bool
     */
    public function updatePrescreened(Company $company, mixed $newValue): bool
    {
        if ($company->prescreened() === $newValue) return false;
        $newTimeStamp = $newValue === true
            ? Carbon::now()->toDateTime()
            : null;
        return $this->updateCompanyModel($company, [ Company::FIELD_PRESCREENED_AT => $newTimeStamp ]);
    }

    /**
     * Get company campaigns base query
     *
     * @param int $companyId
     * @param int|null $status
     * @return Builder
     */
    public function getCompanyCampaignsQuery(int $companyId, int $status = null): Builder
    {
        $legacyCompanyId = Company::query()->findOrFail($companyId)->{Company::FIELD_LEGACY_ID};

        $campaignsQuery = LeadCampaign::query()
            ->select(LeadCampaign::TABLE . '.*')
            ->leftJoin(DatabaseHelperService::database() .'.'. CompanyCampaignRelation::TABLE,
                CompanyCampaignRelation::TABLE .'.'. CompanyCampaignRelation::FIELD_RELATION_ID,
                '=',
                LeadCampaign::TABLE .'.'. LeadCampaign::ID)
            ->with(LeadCampaign::RELATION_PRODUCT_CAMPAIGN.'.'.ProductCampaign::RELATION_BUDGETS)
            ->where(LeadCampaign::TABLE . '.' . LeadCampaign::COMPANY_ID, $legacyCompanyId)
            ->whereNull(CompanyCampaignRelation::TABLE .'.'. CompanyCampaignRelation::FIELD_ID);

        if($status === LeadCampaign::STATUS_ACTIVE || $status === LeadCampaign::STATUS_INACTIVE) {
            $campaignsQuery->where(LeadCampaign::TABLE . '.' . LeadCampaign::STATUS,
                '=',
                $status === LeadCampaign::STATUS_ACTIVE
            );
        }

        return $campaignsQuery;
    }

    /**
     * @param int $companyId
     * @param int|null $status
     * @param int|null $page
     * @param int|null $perPage
     * @return Paginator
     * @throws Exception
     */
    public function getCompanyCampaignsPaginated(
        int $companyId,
        int $status = null,
        ?int $page = 1,
        ?int $perPage = null
    ): Paginator
    {
        $campaignsPaginated = $this
            ->getCompanyCampaignsQuery($companyId, $status)
            ->paginate(
                $perPage,
                ['*'],
                'page',
                $page
            );

        $appointmentProductId = $this->productRepository->getAppointmentProductId();
        $campaigns = $campaignsPaginated->getCollection()->keyBy(LeadCampaign::UUID);

        $leadCampaignProductCampaignMap = [];
        foreach($campaigns as $leadCampaignUuid => $campaign) {
            $productCampaign = $campaign->{LeadCampaign::RELATION_PRODUCT_CAMPAIGN};

            if($productCampaign?->{ProductCampaign::FIELD_PRODUCT_ID} === $appointmentProductId) {
                $productCampaignId = $productCampaign->{ProductCampaign::FIELD_ID};

                if(!empty($productCampaignId)) {
                    $leadCampaignProductCampaignMap[$productCampaignId] = $leadCampaignUuid;
                }
            }
        }

        $productCampaignIds = array_unique(array_keys($leadCampaignProductCampaignMap));

        /** @var ProductCampaignBudgetRepository $pcbr */
        $pcbr = app(ProductCampaignBudgetRepository::class);

        foreach ([QualityTierEnum::IN_HOME, QualityTierEnum::ONLINE] as $qualityTier) {
            $budgetUsageData = $pcbr->getProductCampaignBudgetUsage(
                $productCampaignIds,
                $qualityTier
            );

            $budgetUsagePercentageKey = strtolower("{$qualityTier->name}_budget_usage_percentage");
            $budgetUsageKey = strtolower("{$qualityTier->name}_budget_usage");

            foreach($leadCampaignProductCampaignMap as $productCampaignId => $leadCampaignUuid) {
                $budgetUsageCampaignData = $budgetUsageData->getBudgetUsageData($productCampaignId);

                if ($budgetUsageCampaignData->isEmpty()) {
                    continue;
                }

                $budgetData = $budgetUsageCampaignData->get(BudgetUsageData::BUDGET_INFO)->get(BudgetCategory::VERIFIED->value);

                if (empty($budgetData->get(BudgetUsageData::BUDGET_UNIT))) {
                    continue;
                }
                else if (in_array($budgetData->get(BudgetUsageData::BUDGET_UNIT), [LeadCampaign::DISPLAY_BUDGET_UNIT_UNLIMITED, ProductCampaignBudget::VALUE_TYPE_NO_LIMIT], true)) {
                    $campaigns->get($leadCampaignUuid)->{$budgetUsagePercentageKey} = 0;
                    $campaigns->get($leadCampaignUuid)->{$budgetUsageKey} = $budgetData->get(BudgetUsageData::BUDGET_SPENT)." Appointment(s)";

                    continue;
                }

                $dailyBudget = $budgetData->get(BudgetUsageData::DAILY_BUDGET);
                $budgetUnit = $budgetData->get(BudgetUsageData::BUDGET_UNIT);
                $budgetSpent = $budgetData->get(BudgetUsageData::BUDGET_SPENT);
                $budgetTimeframeDays = $budgetData->get(BudgetUsageData::BUDGET_TIMEFRAME_DAYS);

                $usagePercentage = round($budgetSpent / $budgetTimeframeDays * 100 / $dailyBudget, 2);

                $displayUnit = 'Dollar(s)';

                if(in_array($budgetUnit,  [LeadCampaign::DISPLAY_BUDGET_UNIT_LEAD, ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_LEADS], true)) {
                    $displayUnit = 'Lead(s)';
                }
                else if($budgetUnit === ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_APPOINTMENTS) {
                    $displayUnit = 'Appointment(s)';
                }

                $campaigns->get($leadCampaignUuid)->{$budgetUsagePercentageKey} = $usagePercentage;
                $campaigns->get($leadCampaignUuid)->{$budgetUsageKey} = "{$budgetSpent} {$displayUnit}";
            }
        }

        $campaignsPaginated->setCollection($campaigns->values());

        return $campaignsPaginated;
    }

    /**
     * Get Company invoices by Odin id
     *
     * @param int $companyId
     * @return HasMany
     * @throws ModelNotFoundException
     */
    public function getCompanyInvoices(int $companyId): HasMany
    {
        $company = $this->findOrFail($companyId);
        return $company->invoices()->orderByDesc(EloquentInvoice::TIMESTAMP_ADDED);
    }

    /**
     * Update Company Model and Data payload by id
     *
     * @param int $companyId
     * @param Collection $data
     * @param array $modelKeys - keys to update directly on Company model
     * @param array $payloadKeys - keys to update on CompanyData payload
     * @return bool
     */
    public function updateCompanyModelAndPayloadById(int $companyId, Collection $data, array $modelKeys, array $payloadKeys): bool
    {
        $company = $this->findOrFail($companyId);

        $splitData = $data->reduce(function($output, $value, $key) use (&$modelKeys, &$payloadKeys) {
            if (in_array($key, $modelKeys)) $output['model'][$key] = $value;
            else if (in_array($key, $payloadKeys)) $output['payload'][$key] = $value;
            return $output;
        }  , [ 'model' => [], 'payload' => [] ]);

        return $this->updateCompanyModel($company, $splitData['model'])
            && $this->updateCompanyData($company, $splitData['payload']);

    }

    /**
     * @param Company $company
     * @param array $slugs
     * @return bool
     */
    public function updateCompanyServicesBySlugs(Company $company, array $slugs): bool
    {
        CompanyService::query()->where(CompanyService::FIELD_COMPANY_ID, $company->{Company::FIELD_ID})->delete();
        forEach($slugs as $slug) {
            $industryService = IndustryService::query()->where(IndustryService::FIELD_SLUG, $slug)->firstOrFail();
            CompanyService::create([
                CompanyService::FIELD_COMPANY_ID => $company->{Company::FIELD_ID},
                CompanyService::FIELD_INDUSTRY_SERVICE_ID => $industryService->{IndustryService::FIELD_ID}
            ]);
        }

        return true;
    }

    /**
     * Create Industries and IndustryServices for a Company based on legacy company_type
     * Will not create any duplicates
     *
     * @param Company $company
     * @param string $legacyType
     * @return void
     * @throws BindingResolutionException
     */
    public function updateIndustryAndServiceRelationsFromLegacyCompanyType(Company $company, string $legacyType): void
    {
        $requiredServicesBySlug = match($legacyType) {
            'installer'              => [ IndustryServiceSlug::SOLAR_INSTALLATION->value ],
            'aggregator', 'pp_agg'   => [ IndustryServiceSlug::SOLAR_AGGREGATOR->value ],
            'roofer'                 => [
                IndustryServiceSlug::ROOF_INSTALLATION->value,
                IndustryServiceSlug::ROOF_REPAIR->value,
                IndustryServiceSlug::GUTTER_REPLACEMENT->value,
            ],
            default                  => [],
        };

        $industryServiceRepository = app()->make(CompanyIndustryServiceRepository::class);

        $requiredServices = $industryServiceRepository->getIndustryServicesBySlug($requiredServicesBySlug);
        $requiredServices->each(fn(IndustryService $service) => $industryServiceRepository
            ->addCompanyServiceAndRequiredIndustry($company, $service)
        );
    }

    /**
     * Find by Reference column
     * @param string $reference
     * @return Company|null
     * @throws ModelNotFoundException
     */
    public function findByLegacyReferenceOrFail(string $reference): ?Company
    {
        /** @var ?Company */
        return Company::query()
            ->where(Company::FIELD_REFERENCE, $reference)
            ->firstOrFail();
    }

    /**
     * @param string $reference
     *
     * @return Company
     */
    public function findByReferenceOrFail(string $reference): Company
    {
        /** @var Company */
        return Company::query()
            ->where(Company::FIELD_REFERENCE, '=', $reference)
            ->firstOrFail();
    }

    /**
     * @param array $ipAddresses
     * @return Collection
     */
    public function getCompaniesByIPAddress(array $ipAddresses): Collection
    {
        /** @var Collection $companies */
        $companies = Company::query()
            ->whereHas(Company::RELATION_DATA, fn(Builder $query) =>
                $query->whereIn(CompanyData::FIELD_PAYLOAD.'->'.GlobalConfigurableFields::IP_ADDRESS->value, $ipAddresses))
            ->orWhereHas(Company::RELATION_CONTRACTS, fn(Builder $query) =>
                $query->whereIn(CompanyContract::FIELD_IP_ADDRESS, $ipAddresses))
            ->get();

        return $companies;
    }

    /**
     * @param string $companyName
     * @return Company
     */
    public function getCompanyByNameOrFail(string $companyName): Company
    {
        return Company::query()
            ->where(Company::FIELD_NAME, $companyName)
            ->firstOrFail();
    }

    /**
     * @param string $companyName
     * @return Collection
     */
    public function getSimilarCompaniesByName(string $companyName): Collection
    {
        $cleanedCompanyName = preg_replace('/llc|pty|ltd|co|inc|,|\./', '',  strtolower($companyName));
//        some companies have extra into in brackets - like the office location
        $cleanedCompanyName =  trim(preg_replace('/\(([^()]*+|(?R))*\)/','', $cleanedCompanyName));
        return Company::query()
            ->where(DB::raw('LOWER('.Company::FIELD_NAME.')'), 'LIKE', '%'.$cleanedCompanyName.'%')
            ->get();
    }

    /**
     * @param array $placeIds
     * @return \Illuminate\Database\Eloquent\Collection|array
     */
    public function getCompaniesAndAccountManagersByPlaceIdIfExists(array $placeIds): \Illuminate\Database\Eloquent\Collection|array
    {
        return Company::query()
            ->select(
                User::TABLE.'.'.User::FIELD_NAME . ' as account_manager_name',
                Company::TABLE.'.'.Company::FIELD_WEBSITE,
                Company::TABLE.'.'.Company::FIELD_ID . ' as company_id',
                Address::TABLE.'.'.Address::FIELD_PLACE_ID,
                Company::TABLE.'.'.Company::FIELD_STATUS . ' as company_status',
                Company::TABLE.'.'.Company::FIELD_CONSOLIDATED_STATUS . ' as company_consolidated_status',
            )
            ->join(CompanyLocation::TABLE, Company::TABLE .'.'. Company::FIELD_ID, '=', CompanyLocation::TABLE .'.'. CompanyLocation::FIELD_COMPANY_ID)
            ->join(Address::TABLE, CompanyLocation::TABLE .'.'. CompanyLocation::FIELD_ADDRESS_ID, '=', Address::TABLE .'.'. Address::FIELD_ID)
            ->join(AccountManagerClient::TABLE, Company::TABLE .'.'. Company::FIELD_REFERENCE, '=', AccountManagerClient::TABLE .'.'. AccountManagerClient::FIELD_COMPANY_REFERENCE)
            ->join(AccountManager::TABLE, AccountManagerClient::TABLE .'.'. AccountManagerClient::FIELD_ACCOUNT_MANAGER_ID, '=', AccountManager::TABLE .'.'. AccountManager::FIELD_ID)
            ->join(User::TABLE, AccountManager::TABLE .'.'. AccountManager::FIELD_USER_ID, '=', User::TABLE .'.'. User::FIELD_ID)
            ->whereIn(Address::FIELD_PLACE_ID, $placeIds)
            ->where(AccountManagerClient::TABLE .'.'. AccountManagerClient::FIELD_STATUS, true)
            ->get();
    }

    /**
     * @param Company $company
     * @param bool $muteEvents
     * @return bool
     * @deprecated
     * Pending delete
     */
    public function updateConsolidatedStatus(Company $company, bool $muteEvents = false): bool
    {
        return true;

        $company->load([
            Company::RELATION_LEGACY_COMPANY
        ]);

        // TODO: Remove after legacy logic brought over to A2
        if (!$company->legacyCompany) {
            return false;
        }

        $companyCopy = $company->replicate();

        if ($this->updateCompanyStatuses($company, $muteEvents)) {
            $company->refresh();
            $company->{Company::FIELD_CONSOLIDATED_STATUS} = CompanyConsolidatedStatus::get($company->admin_status->value, $company->status);
            $this->logConsolidatedStatusChange($companyCopy, $company);
            return $muteEvents ? $company->saveQuietly() : $company->save();
        }

        return false;
    }

    protected function logAdminStatusUpdate(Company $company, CompanyAdminStatus $oldStatus, CompanyAdminStatus $newStatus, string $reason): void
    {
        if ($oldStatus === $newStatus) {
            return;
        }

        /** @var CompanyChangeLogService $companyChangeLogService */
        $companyChangeLogService = app(CompanyChangeLogService::class);

        /** @var User $user */
        $user = session()->has(ImpersonateService::SESSION_KEY) ? User::query()->findOrFail(session()->get(ImpersonateService::SESSION_KEY)) : Auth::user();

        $companyChangeLogService->log(
            company: $company,
            log: CompanyChangeLogType::ADMIN_STATUS_CHANGE,
            payload: [
                'old' => $oldStatus->value,
                'new' => $newStatus->value,
                'reason' => $reason,
                'user_id' => $user->id
            ]
        );
    }

    /**
     * @param Company $company
     * @param Collection $data
     * @param User $user
     *
     * @return bool
     */
    protected function shouldUpdateAminStatus(Company $company, Collection $data, User $user): bool
    {
        if (!$data->has(Company::FIELD_ADMIN_STATUS)) {
            return false;
        }

        if ($company->admin_status->value === $data->get(Company::FIELD_ADMIN_STATUS)) {
            return false;
        }

        if ($company->getAdminStatusOptionsForUser()->filter(fn(CompanyAdminStatus $status) => $status->value === $data->get(Company::FIELD_ADMIN_STATUS))->isEmpty()) {
            return false;
        }

        return true;
    }

    /**
     * @param Company $companyCopy
     * @param Company $company
     *
     * @return void
     */
    protected function logConsolidatedStatusChange(Company $companyCopy, Company $company, bool $manualChange = false): void
    {
        if ($companyCopy->consolidated_status === $company->consolidated_status) {
            return;
        }

        /** @var CompanyChangeLogService $companyChangeLogService */
        $companyChangeLogService = app(CompanyChangeLogService::class);

        $companyChangeLogService->log(
            company: $company,
            log: CompanyChangeLogType::CONSOLIDATED_STATUS_CHANGE,
            payload: [
                Company::FIELD_CONSOLIDATED_STATUS => [
                    'old' => $companyCopy->consolidated_status,
                    'new' => $company->consolidated_status
                ],
                Company::FIELD_ADMIN_STATUS => [
                    'old' => $companyCopy->admin_status,
                    'new' => $company->admin_status
                ],
                Company::FIELD_STATUS => [
                    'old' => $companyCopy->status,
                    'new' => $company->status
                ],
                'manual_change' => $manualChange
            ]);
    }

    /**
     * @param Company $company
     * @param bool $muteEvents
     * @return bool
     */
    private function updateCompanyStatuses(Company $company, bool $muteEvents = false): bool
    {
        $company->{Company::FIELD_STATUS}         = $this->getBasicStatus($company);
        $company->{Company::FIELD_ADMIN_STATUS}   = $this->getAdminStatus($company);

        return $muteEvents ? $company->saveQuietly() : $company->save();
    }

    private function getBasicStatus(Company $company): int
    {
        /** @var CompanyBillingServiceV4 $billingService */
        $billingService = app(CompanyBillingServiceV4::class);

        return LegacyCompanyBasicStatus::get(
            adminApproved:                    $company->admin_approved,                                                       // TODO: Add ability to approve in A2 Company page
            validPaymentMethod:               $billingService->companyHasValidPaymentMethod($company->id),
            activeCampaignsCount:             $this->getActiveCampaignsCount($company),
            reactivateCampaignsInFutureCount: $this->getCampaignsWithReactivationCount($company),
            leadsPurchasedCount:              $this->getLeadsPurchasedCount($company),
            imported:                         $company->imported
        );
    }

    /**
     * @param Company $company
     * @return int|null
     */
    private function getAdminStatus(Company $company): ?int
    {
        return LegacyCompanyAdminStatus::get(
            archived:    $company->legacyCompany->is_archived,    // TODO: Add ability to archive in A2 Company page
            status:      $company->admin_status->value,         // TODO: Suspend / add to collections in A2
            adminLocked: $company->admin_locked
        );
    }

    /**
     * @param Company $company
     * @return int
     */
    private function getLeadsPurchasedCount(Company $company): int
    {
        return $company
            ->{Company::RELATION_PRODUCT_ASSIGNMENTS}()
            ->where(ProductAssignment::FIELD_CHARGEABLE, true)
            ->where(ProductAssignment::FIELD_DELIVERED, true)
            ->count();
    }

    /**
     * @param Company $company
     * @return int
     */
    private function getActiveCampaignsCount(Company $company): int
    {
        return $company
            ->{Company::RELATION_FUTURE_CAMPAIGNS}()
            ->where(CompanyCampaign::FIELD_STATUS, CampaignStatus::ACTIVE)
            ->count();
    }

    /**
     * @param Company $company
     * @return int
     */
    private function getCampaignsWithReactivationCount(Company $company): int
    {
        return $company
            ->{Company::RELATION_FUTURE_CAMPAIGNS}()
            ->where(CompanyCampaign::FIELD_STATUS, CampaignStatus::PAUSED_TEMPORARILY)
            ->count();
    }

    /**
     * @return  Collection
     */
    public function getAllUnverifiedCompaniesWebsite(): Collection
    {
        return Company::query()
            ->whereNotNull(Company::FIELD_WEBSITE)
            ->whereNull(Company::FIELD_WEBSITE_VERIFIED_AT)
            ->get();
    }

    /**
     * Update Companies record as batches
     * @param $data
     * @return void
     */
    public function updateBatchCompanyRecords($data): void
    {
        try {
            Company::query()->upsert($data, [Company::FIELD_ID], [Company::FIELD_WEBSITE_VERIFIED_URL, Company::FIELD_WEBSITE_VERIFIED_AT]);
        } catch (Throwable $e) {
            logger()->error("Couldn't update companies website verification details: " . $e->getMessage());
        }
    }

    public function getTimezone(Company $company)
    {
        $mostCommon = CompanyLocation::query()
            ->select(Address::TABLE . '.' . Address::FIELD_ID, DB::raw("COUNT(" . Address::TABLE . '.' . Address::FIELD_ID . ") as count"))
            ->join(Address::TABLE, Address::TABLE . '.' . Address::FIELD_ID, CompanyLocation::TABLE . '.' . CompanyLocation::FIELD_ADDRESS_ID)
            ->where(CompanyLocation::TABLE . '.' . CompanyLocation::FIELD_COMPANY_ID, $company->{Company::FIELD_ID})
            ->groupBy(CompanyLocation::TABLE . '.' . CompanyLocation::FIELD_ADDRESS_ID)
            ->orderBy('count', 'DESC')
            ->first();

        if (!$mostCommon) return SupportedTimezones::MOUNTAIN->value;

        $address = Address::query()->where(Address::FIELD_ID,$mostCommon->id)->first();

        return $address && $address->{Address::FIELD_UTC} ? $address->{Address::FIELD_UTC} : SupportedTimezones::MOUNTAIN->value;
    }

    /**
     * @param int $industryId
     * @return array
     */
    public function getCompanyIdsByIndustryId(int $industryId): array
    {
        return CompanyIndustry::query()
            ->select(CompanyIndustry::FIELD_COMPANY_ID)
            ->where(CompanyIndustry::FIELD_INDUSTRY_ID, $industryId)
            ->pluck(CompanyIndustry::FIELD_COMPANY_ID)
            ->toArray();
    }

    /**
     * @param Company $company
     * @param bool $appointmentsActive
     * @return bool
     */
    public function updateAppointmentsActive(Company $company, bool $appointmentsActive): bool
    {
        $configuration = $company->{Company::RELATION_CONFIGURATION};

        $configuration->{CompanyConfiguration::FIELD_APPOINTMENTS_ACTIVE} = $appointmentsActive;
        $configuration->{CompanyConfiguration::FIELD_REQUIRE_APPOINTMENTS_CALENDAR} = $appointmentsActive;

        $configuration->save();

        return true;
    }

    public function getAssociatedProducts(int $companyId, int $invoiceId): Collection
    {
        $products = DB::connection('readonly')->table(EloquentInvoice::TABLE)
            ->join(EloquentInvoiceItem::TABLE, EloquentInvoice::TABLE . '.' . EloquentInvoice::ID, '=', EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::INVOICE_ID)
            ->join(EloquentQuoteCompany::TABLE, EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::ID, '=',  EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::INVOICE_ITEM_ID)
            ->join( DatabaseHelperService::database() . '.' . ProductAssignment::TABLE, EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::ID, '=',  ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_LEGACY_ID)
            ->join(DatabaseHelperService::database() . '.' . ConsumerProduct::TABLE, ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, '=', ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID)
            ->join( DatabaseHelperService::database() . '.' . Consumer::TABLE,  ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_CONSUMER_ID, '=',  Consumer::TABLE . '.' . Consumer::FIELD_ID)
            ->where(EloquentInvoice::TABLE . '.' . EloquentInvoice::INVOICE_ID, $invoiceId)
            ->select(
                Consumer::FIELD_FIRST_NAME,
                Consumer::FIELD_LAST_NAME,
                Consumer::TABLE . '.' . Consumer::FIELD_STATUS,
                Consumer::TABLE . '.' . Consumer::FIELD_LEGACY_ID,
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CONSUMER_PRODUCT_ID,
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED_AT)
            ->get();
        return $products;
    }

    /**
     * Returns the company links to a given company id. Join on the Company table required to retrieve the other company's name in the link.
     * Only the other companies id is returned and results are ordered by the other company id.
     *
     * @param int $companyId
     * @return LengthAwarePaginator
     */
    public function getLinkedCompaniesPaginated(int $companyId): LengthAwarePaginator
    {
        return CompanyLink::query()
            ->where(CompanyLink::FIELD_COMPANY_ID_ONE, $companyId)
            ->orWhere(CompanyLink::FIELD_COMPANY_ID_TWO, $companyId)
            ->orderByRaw('CASE WHEN company_id_one = ? THEN company_id_two ELSE company_id_one END', [$companyId])
            ->paginate(5);
    }

    /**
     * Returns the company link instance given a link id.
     *
     * @param int $linkId
     * @return Builder|Builder[]|\Illuminate\Database\Eloquent\Collection|\Illuminate\Database\Eloquent\Model|null
     */
    public function getCompanyLink(int $linkId)
    {
        return CompanyLink::query()->findOrFail($linkId);
    }

    /**
     * Returns true if a link between the two companies already exists, regardless of the order of company ids.
     *
     * @param int $companyId
     * @param int $otherCompanyId
     * @return bool
     */
    public function checkCompanyLinkExists(int $companyId, int $otherCompanyId):bool
    {
        return (CompanyLink::query()->where(fn ($query) =>  $query->where(CompanyLink::FIELD_COMPANY_ID_ONE, $companyId)
            ->where(CompanyLink::FIELD_COMPANY_ID_TWO, $otherCompanyId))
            ->orWhere(fn ($query) =>  $query->where(CompanyLink::FIELD_COMPANY_ID_TWO, $companyId)
                ->where(CompanyLink::FIELD_COMPANY_ID_ONE, $otherCompanyId))
            ->exists());
    }

    /**
     * Creates a new link between two companies given an array with both company id's and a comment.
     *
     * @param array $newLink
     * @return bool
     */
    public function createCompanyLink(array $newLink): bool
    {
        $link = new CompanyLink();
        $link->{CompanyLink::FIELD_COMPANY_ID_ONE} = $newLink['company_id'];
        $link->{CompanyLink::FIELD_COMPANY_ID_TWO} = $newLink['other_company_id'];
        $link->{CompanyLink::FIELD_CREATED_BY_USER} = auth()->user()->id;
        $link->{CompanyLink::FIELD_COMMENT} = $newLink['comment'];

        return $link->save();
    }


    public function getCompanyUserActions(Company $company, int $companyUserId, int $perPage): LengthAwarePaginator
    {
        return $company->companyUserActions()
            ->where(Action::FIELD_FOR_ID, $companyUserId)
            ->paginate($perPage)
            ->through(fn(Action $action) => new ActionResource($action));
    }

    /**
     * @param int $companyId
     * @return string|null
     */
    public function getReferenceById(int $companyId): ?string
    {
        return Company::query()->find($companyId)?->{Company::FIELD_REFERENCE};
    }

    /**
     * @param int $companyId
     * @param String|null $contractName
     * @param String|null $contractType
     * @return Builder
     */
    public function getContractsByCompanyIdWithSearchFilters(
        int $companyId,
        String|null $contractName = null,
        String|null $contractType = null,
    ): Builder
    {
        return CompanyContractBuilder::query()
                ->forCompanyId($companyId)
                ->forContractName($contractName)
                ->forContractType($contractType)
                ->getQuery();
    }

    /**
     * @param int $companyId
     * @return Builder
     */
    public function getLegacyContracts(int $companyId): Builder
    {
        return EloquentHistoryLog::query()
            ->with(EloquentHistoryLog::RELATION_USER)
            ->where(EloquentHistoryLog::ACTIVITY, self::CONTRACT_APPROVAL)
            ->whereHas(EloquentHistoryLog::RELATION_USER, function ($query)  use ($companyId) {
                $query->where(EloquentUser::COMPANY_ID, $companyId);
            })
            ->select(DB::raw("DATE_FORMAT(FROM_UNIXTIME(" . EloquentHistoryLog::TIMESTAMP_ADDED ." - 21600),'%Y-%m-%d %H:%i:%s') as date"),
                EloquentHistoryLog::USER_ID, EloquentHistoryLog::IP_ADDRESS, EloquentHistoryLog::ACTIVITY)
            ->orderBy(EloquentHistoryLog::HISTORY_ID, 'DESC');
    }

    /**
     * @param Company $company
     * @param string $optInName
     * @param int|null $optInNameId
     *
     * @return bool
     */
    public function saveOptInName(Company $company, string $optInName, ?int $optInNameId = null): bool
    {
        if (!$optInNameId) {
            return !!$company->optInNames()->firstOrCreate([CompanyOptInName::FIELD_NAME => $optInName]);
        }

        /** @var CompanyOptInName $optInNameModel */
        $optInNameModel = $company->optInNames()->where(CompanyOptInName::FIELD_ID, $optInNameId)->firstOrFail();
        $optInNameModel->name = $optInName;

        return $optInNameModel->save();
    }
}
