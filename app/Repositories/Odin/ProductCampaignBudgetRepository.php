<?php

namespace App\Repositories\Odin;

use App\DataModels\Odin\Prices\BudgetUsageData;
use App\Enums\Odin\BudgetCategory;
use App\Enums\Odin\QualityTier;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyConfiguration;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Product as ProductModel;
use App\Enums\Odin\Product as ProductEnum;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ProductCampaignBudget;
use App\Models\Odin\ProductCancellation;
use App\Models\Odin\ProductRejection;
use App\Models\Odin\ServiceProduct;
use Exception;

class ProductCampaignBudgetRepository
{
    /**
     * @param ProductRepository $productRepository
     */
    public function __construct(
        private readonly ProductRepository $productRepository
    ) {

    }

    /**
     * @param array $productCampaignIds
     * @param QualityTier $qualityTier
     * @return BudgetUsageData
     * @throws Exception
     */
    public function getProductCampaignBudgetUsage(array $productCampaignIds, QualityTier $qualityTier): BudgetUsageData
    {
        $valueTypeCol = ProductCampaignBudget::TABLE.'.'.ProductCampaignBudget::FIELD_VALUE_TYPE;
        $typeAvgDailySpend = ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_SPEND;
        $budgetStartTimestampCol = ProductCampaignBudget::TABLE.'.'.ProductCampaignBudget::FIELD_BUDGET_START_TIMESTAMP;
        $valueCol = ProductCampaignBudget::TABLE.'.'.ProductCampaignBudget::FIELD_VALUE;
        $categoryCol = ProductCampaignBudget::FIELD_CATEGORY;
        $productCampaignIdCol = ProductCampaignBudget::FIELD_PRODUCT_CAMPAIGN_ID;
        $costCol = ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_COST;
        $deliveredAtCol = ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_DELIVERED_AT;

        $costWithinTimeframeQuery = "IF(UNIX_TIMESTAMP({$deliveredAtCol}) >= UNIX_TIMESTAMP({$budgetStartTimestampCol}), {$costCol}, NULL)";

        $budgetSpentCol = 'budget_spent';
        $budgetTimeframeCol = 'budget_timeframe';
        $budgetUnitCol = 'budget_unit';
        $dailyBudgetCol = 'daily_budget';

        $budgetInfo = ProductCampaignBudget::query()
            ->join(ProductCampaign::TABLE, function($join) {
                $join->on(
                    ProductCampaign::TABLE.'.'.ProductCampaign::FIELD_ID,
                    '=',
                    ProductCampaignBudget::TABLE.'.'.ProductCampaignBudget::FIELD_PRODUCT_CAMPAIGN_ID
                );
            })
            ->join(Company::TABLE, function($join) {
                $join->on(
                    ProductCampaign::TABLE.'.'.ProductCampaign::FIELD_COMPANY_ID,
                    '=',
                    Company::TABLE.'.'.Company::FIELD_ID
                );
            })
            ->join(CompanyConfiguration::TABLE, function($join) {
                $join->on(
                    CompanyConfiguration::TABLE.'.'.CompanyConfiguration::FIELD_COMPANY_ID,
                    '=',
                    Company::TABLE.'.'.Company::FIELD_ID
                );
            })
            ->whereIn(ProductCampaignBudget::TABLE.'.'.ProductCampaignBudget::FIELD_PRODUCT_CAMPAIGN_ID, $productCampaignIds)
            ->where(ProductCampaignBudget::TABLE.'.'.ProductCampaignBudget::FIELD_STATUS, true)
            ->where(ProductCampaignBudget::TABLE.'.'.ProductCampaignBudget::FIELD_QUALITY_TIER, $qualityTier->value)
            ->selectRaw(implode(',', [
                ProductCampaignBudget::TABLE.'.'.ProductCampaignBudget::FIELD_ID,
                ProductCampaignBudget::TABLE . '.' . ProductCampaignBudget::FIELD_PRODUCT_CAMPAIGN_ID." AS {$productCampaignIdCol}",
                ProductCampaign::TABLE . '.' . ProductCampaign::FIELD_COMPANY_ID,
                ProductCampaignBudget::TABLE . '.' . ProductCampaignBudget::FIELD_CATEGORY . " AS {$categoryCol}",
                "FLOOR((UNIX_TIMESTAMP() - UNIX_TIMESTAMP({$budgetStartTimestampCol})) / 86400) + 1 AS {$budgetTimeframeCol}",
                "{$valueCol} AS {$dailyBudgetCol}",
                ProductCampaignBudget::TABLE.'.'.ProductCampaignBudget::FIELD_MAX_BUDGET_USAGE,
                "{$valueTypeCol} AS {$budgetUnitCol}",
                CompanyConfiguration::TABLE.'.'.CompanyConfiguration::FIELD_NEVER_EXCEED_BUDGET
            ]))
            ->groupBy([
                ProductCampaignBudget::TABLE.'.'.ProductCampaignBudget::FIELD_ID
            ])
            ->get()
            ->map(function(ProductCampaignBudget $budget) {
                return $budget->getRawOriginal();
            })
            ->groupBy([
                ProductCampaignBudget::FIELD_PRODUCT_CAMPAIGN_ID,
                ProductCampaignBudget::FIELD_CATEGORY
            ]);

        $spentInfo = ProductAssignment::query()
            ->join(ProductCampaignBudget::TABLE, function($join) {
                $join->on(
                    ProductCampaignBudget::TABLE.'.'.ProductCampaignBudget::FIELD_ID,
                    '=',
                    ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_PRODUCT_CAMPAIGN_BUDGET_ID
                );
            })
            ->join(ConsumerProduct::TABLE, function($join) {
                $join->on(
                    ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ID,
                    '=',
                    ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CONSUMER_PRODUCT_ID
                );
            })
            ->join(ServiceProduct::TABLE, function($join) {
                $join->on(
                    ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_ID,
                    '=',
                    ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_SERVICE_PRODUCT_ID
                );
            })
            ->join(ProductModel::TABLE, function($join) {
                $join->on(
                    ProductModel::TABLE.'.'.ProductModel::FIELD_ID,
                    '=',
                    ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_PRODUCT_ID
                );
            })
            ->leftJoin(ProductRejection::TABLE, function($join) {
                $join->on(
                    ProductRejection::TABLE.'.'.ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID,
                    '=',
                    ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_ID
                );
            })
            ->leftJoin(ProductCancellation::TABLE, function($join) {
                $join->on(
                    ProductCancellation::TABLE.'.'.ProductCancellation::FIELD_PRODUCT_ASSIGNMENT_ID,
                    '=',
                    ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_ID
                );
            })
            ->whereIn(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_PRODUCT_CAMPAIGN_BUDGET_ID, $budgetInfo->values()->collapse()->values()->collapse()->pluck(ProductCampaignBudget::FIELD_ID)->filter()->unique())
            ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_DELIVERED, true)
            ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CHARGEABLE, true)
            ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_EXCLUDE_BUDGET, 0)
            ->where(function($where) {
                $where
                    ->where(ProductModel::TABLE.'.'.ProductModel::FIELD_NAME, ProductEnum::APPOINTMENT->value)
                    ->orWhere(function($orWhere) {
                        $orWhere
                            ->where(ProductModel::TABLE.'.'.ProductModel::FIELD_NAME, ProductEnum::LEAD->value)
                            ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_PARENT_PRODUCT_ID, $this->productRepository->getAppointmentProductId());
                    });
            })
            ->whereNull(ProductCancellation::TABLE.'.'.ProductCancellation::FIELD_ID)
            ->whereNull(ProductRejection::TABLE.'.'.ProductRejection::FIELD_ID)
            ->groupBy([
                ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CAMPAIGN_ID,
                ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_PRODUCT_CAMPAIGN_BUDGET_ID
            ])
            ->selectRaw(implode(',', [
                ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CAMPAIGN_ID,
                ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_PRODUCT_CAMPAIGN_BUDGET_ID,
                "COALESCE(IF({$valueTypeCol} = '{$typeAvgDailySpend}', SUM({$costWithinTimeframeQuery}), COUNT({$costWithinTimeframeQuery})), 0) AS {$budgetSpentCol}",
            ]))
            ->get()
            ->groupBy([
                ProductAssignment::FIELD_CAMPAIGN_ID,
                ProductAssignment::FIELD_PRODUCT_CAMPAIGN_BUDGET_ID
            ])
            ->toArray();

        $budgetInfo = $budgetInfo->toArray();

        $budgetUsageData = new BudgetUsageData();

        foreach($productCampaignIds as $campaignId) {
            $verifiedBudgetInfo = !empty($budgetInfo[$campaignId][BudgetCategory::VERIFIED->value][0]) ? $budgetInfo[$campaignId][BudgetCategory::VERIFIED->value][0] : null;
            if($verifiedBudgetInfo) {
                $productCampaignBudgetId = $verifiedBudgetInfo[ProductCampaignBudget::FIELD_ID];

                $spent = !empty($spentInfo[$campaignId][$productCampaignBudgetId][0])
                        ? $spentInfo[$campaignId][$productCampaignBudgetId][0][$budgetSpentCol]
                        : 0;

                $budgetUsageData->setBudgetUsage(
                    $campaignId,
                    $verifiedBudgetInfo[ProductCampaign::FIELD_COMPANY_ID],
                    $productCampaignBudgetId,
                    BudgetCategory::VERIFIED->value,
                    $spent,
                    $verifiedBudgetInfo[$dailyBudgetCol],
                    $verifiedBudgetInfo[$budgetTimeframeCol],
                    $verifiedBudgetInfo[ProductCampaignBudget::FIELD_MAX_BUDGET_USAGE],
                    $verifiedBudgetInfo[$budgetUnitCol],
                    $verifiedBudgetInfo[CompanyConfiguration::FIELD_NEVER_EXCEED_BUDGET]
                );
            }

            $unverifiedBudgetInfo = !empty($budgetInfo[$campaignId][BudgetCategory::UNVERIFIED->value][0]) ? $budgetInfo[$campaignId][BudgetCategory::UNVERIFIED->value][0] : null;
            if($unverifiedBudgetInfo) {
                $productCampaignBudgetId = $unverifiedBudgetInfo[ProductCampaignBudget::FIELD_ID];

                $spent = !empty($spentInfo[$campaignId][$productCampaignBudgetId][0])
                        ? $spentInfo[$campaignId][$productCampaignBudgetId][0][$budgetSpentCol]
                        : 0;

                $budgetUsageData->setBudgetUsage(
                    $campaignId,
                    $unverifiedBudgetInfo[ProductCampaign::FIELD_COMPANY_ID],
                    $productCampaignBudgetId,
                    BudgetCategory::UNVERIFIED->value,
                    $spent,
                    $unverifiedBudgetInfo[$dailyBudgetCol],
                    $unverifiedBudgetInfo[$budgetTimeframeCol],
                    $unverifiedBudgetInfo[ProductCampaignBudget::FIELD_MAX_BUDGET_USAGE],
                    $unverifiedBudgetInfo[$budgetUnitCol],
                    $unverifiedBudgetInfo[CompanyConfiguration::FIELD_NEVER_EXCEED_BUDGET]
                );
            }

            $emailOnlyBudgetInfo = !empty($budgetInfo[$campaignId][BudgetCategory::EMAIL_ONLY->value][0]) ? $budgetInfo[$campaignId][BudgetCategory::EMAIL_ONLY->value][0] : null;
            if($emailOnlyBudgetInfo) {
                $productCampaignBudgetId = $emailOnlyBudgetInfo[ProductCampaignBudget::FIELD_ID];

                $spent = !empty($spentInfo[$campaignId][$productCampaignBudgetId][0])
                        ? $spentInfo[$campaignId][$productCampaignBudgetId][0][$budgetSpentCol]
                        : 0;

                $budgetUsageData->setBudgetUsage(
                    $campaignId,
                    $emailOnlyBudgetInfo[ProductCampaign::FIELD_COMPANY_ID],
                    $productCampaignBudgetId,
                    BudgetCategory::EMAIL_ONLY->value,
                    $spent,
                    $emailOnlyBudgetInfo[$dailyBudgetCol],
                    $emailOnlyBudgetInfo[$budgetTimeframeCol],
                    $emailOnlyBudgetInfo[ProductCampaignBudget::FIELD_MAX_BUDGET_USAGE],
                    $emailOnlyBudgetInfo[$budgetUnitCol],
                    $emailOnlyBudgetInfo[CompanyConfiguration::FIELD_NEVER_EXCEED_BUDGET]
                );
            }
        }

        return $budgetUsageData;
    }
}
