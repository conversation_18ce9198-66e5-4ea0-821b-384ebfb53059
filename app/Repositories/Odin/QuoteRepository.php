<?php

namespace App\Repositories\Odin;

use App\Contracts\Repositories\QuoteRepositoryContract;
use App\Models\Odin\Company;
use App\Models\Odin\Consumer;
use App\Models\Odin\ProductAssignment;

class QuoteRepository implements QuoteRepositoryContract
{

    function find(int $id)
    {
        // TODO: Implement find() method.
    }

    public function getAllocatedRelatedQuotesLast90Days(int $quoteId, ?string $userEmail, ?string $userIp, ?string $zipCode, ?string $phoneNumber)
    {
        // TODO: Implement getAllocatedRelatedQuotesLast90Days() method.
    }

    public function getPaginatedRelatedQuotes($quoteId, $userEmail, $userIp, $zipCode, $phoneNumber, $perPage = null)
    {
        // TODO: Implement getPaginatedRelatedQuotes() method.
    }

    function buildQueryForRelatedQuotes(int $quoteId, string $userEmail, string $userIp, string $zipCode, ?string $phoneNumber, array $relations = []): \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Query\Builder
    {
        // TODO: Implement buildQueryForRelatedQuotes() method.
    }

    /**
     * @param Company $company
     *
     * @return ProductAssignment|null
     */
    public function getLastLead(Company $company): ?ProductAssignment
    {
        /** @var ProductAssignment $productAssignment */
        $productAssignment = ProductAssignment::query()
            ->where(ProductAssignment::FIELD_COMPANY_ID, $company->id)
            ->where(ProductAssignment::FIELD_DELIVERED, true)
            ->latest()
            ->first();

        return $productAssignment;
    }
}
