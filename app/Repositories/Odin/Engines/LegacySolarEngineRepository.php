<?php

namespace App\Repositories\Odin\Engines;

use App\Models\Legacy\EloquentUtility;
use App\Models\Legacy\SolarCalculatorEngine\IncentivesLookup;
use App\Models\Legacy\SolarCalculatorEngine\OpenCalculatorInput;
use App\Models\Legacy\SolarCalculatorEngine\OpenCalculatorResult;
use App\Repositories\Legacy\SystemCostRepository;
use App\Repositories\Legacy\UtilityRepository;
use App\Repositories\LocationRepository;
use App\Services\Legacy\IncentiveLookupService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class LegacySolarEngineRepository
{
    const BILL_TOLERANCE = 30;
    const DEFAULT_SYSTEM_SIZE_IN_KW = 7;
    const FALLBACK_UTILITY_UUID = 'HXELMXOR';

    public function __construct(
        protected UtilityRepository $utilityRepository,
        protected IncentiveLookupService $incentiveLookupService,
        protected SystemCostRepository $systemCostRepository,
        protected LocationRepository $locationRepository
    )
    {
    }

    /**
     * @param string $zipCode
     * @param string $utilityUuid
     * @param int $monthlyBill
     * @param int $targetUsageOffset
     * @param int $azimuth
     * @param int $tilt
     * @return mixed
     */
    public function getResult(
        string $zipCode,
        string $utilityUuid,
        int $monthlyBill,
        int $targetUsageOffset = 100,
        int $azimuth = 180,
        int $tilt = 26
    ): OpenCalculatorResult
    {
        $input = $this->getMatchingInput($zipCode, $utilityUuid, $monthlyBill, $targetUsageOffset, $azimuth, $tilt);

        if(!$input) {
            // todo: run new calc through Estimator
            $input = $this->getClosestInputWithFallback($utilityUuid, $monthlyBill, $targetUsageOffset, $azimuth, $tilt);
        }

        if($input) {
            $result = OpenCalculatorResult::find($input->open_calculator_result_id);
            $this->updateInputAndResultCounts($input, $result);

            return $result;
        }

        // This should never happen but is a fail-safe
        return OpenCalculatorResult::query()->firstOrFail();
    }

    /**
     * @param OpenCalculatorInput $input
     * @param OpenCalculatorResult $result
     * @return void
     */
    private function updateInputAndResultCounts(OpenCalculatorInput $input, OpenCalculatorResult $result): void
    {
        $input->update([
            OpenCalculatorInput::FIELD_COUNT => $input->count + 1
        ]);

        $result->update([
            OpenCalculatorResult::FIELD_COUNT => $result->count + 1
        ]);
    }

    /**
     * @param string $zipCode
     * @param string $utilityUuid
     * @param int $monthlyBill
     * @param int $targetUsageOffset
     * @param int $azimuth
     * @param int $tilt
     * @return OpenCalculatorInput|null
     */
    private function getMatchingInput(
        string $zipCode,
        string $utilityUuid,
        int $monthlyBill,
        int $targetUsageOffset = 100,
        int $azimuth = 180,
        int $tilt = 26
    ): ?OpenCalculatorInput
    {
        $inputs = $this->getMatchingInputs($zipCode, $utilityUuid, $monthlyBill, $targetUsageOffset, $azimuth, $tilt);
        return $this->getInputWithClosestBill($inputs, $monthlyBill);
    }

    /**
     * @param string $utilityUuid
     * @param int $monthlyBill
     * @param int $targetUsageOffset
     * @param int $azimuth
     * @param int $tilt
     * @return OpenCalculatorInput|null
     */
    private function getClosestInputWithFallback(
        string $utilityUuid,
        int $monthlyBill,
        int $targetUsageOffset = 100,
        int $azimuth = 180,
        int $tilt = 26
    ): ?OpenCalculatorInput
    {
        $inputs = $this->getInputsWithoutZipCode($utilityUuid, $monthlyBill, $targetUsageOffset, $azimuth, $tilt);

        if($inputs->isEmpty()) {
            $inputs = $this->getInputsWithoutUtilityOrZipCode($monthlyBill, $targetUsageOffset, $azimuth, $tilt);
            if($inputs->isEmpty()) {
                return $this->getGeneralFallbackInput();
            }
        }

        return $this->getInputWithClosestBill($inputs, $monthlyBill);
    }

    /**
     * @param Collection $inputs
     * @param int $monthlyBill
     * @return OpenCalculatorInput|null
     */
    private function getInputWithClosestBill(Collection $inputs, int $monthlyBill): ?OpenCalculatorInput
    {
        $closestBill = $inputs->pluck(OpenCalculatorInput::FIELD_MONTHLY_BILL)->pipe(function ($bills) use ($monthlyBill){
            $closest = null;
            foreach ($bills as $bill){
                if($closest === null || abs($monthlyBill - $closest) > abs($bill - $monthlyBill) ){
                    $closest = $bill;
                }
            }
            return $closest;
        });

        return $inputs->where(OpenCalculatorInput::FIELD_MONTHLY_BILL, $closestBill)->first();
    }

    /**
     * @param string $zipCode
     * @param string $utilityUuid
     * @param int $monthlyBill
     * @param int $targetUsageOffset
     * @param int $azimuth
     * @param int $tilt
     * @return Collection|array
     */
    private function getMatchingInputs(
        string $zipCode,
        string $utilityUuid,
        int    $monthlyBill,
        int    $targetUsageOffset = 100,
        int    $azimuth = 180,
        int    $tilt = 26
    ): Collection|array
    {
        return OpenCalculatorInput::query()
            ->where(OpenCalculatorInput::FIELD_ZIP_CODE, $zipCode)
            ->where(OpenCalculatorInput::FIELD_UTILITY_UUID, $utilityUuid)
            ->where(OpenCalculatorInput::FIELD_MONTHLY_BILL, '>=', ($monthlyBill - self::BILL_TOLERANCE))
            ->where(OpenCalculatorInput::FIELD_MONTHLY_BILL, '<=', ($monthlyBill + self::BILL_TOLERANCE))
            ->where(OpenCalculatorInput::FIELD_ELECTRIC_OFFSET, $targetUsageOffset)
            ->where(OpenCalculatorInput::FIELD_AZIMUTH, $azimuth)
            ->where(OpenCalculatorInput::FIELD_TILT, $tilt)
            ->where(OpenCalculatorInput::FIELD_MONTH, Carbon::now()->subMonth()->month)
            ->get();
    }

    /**
     * @param string $utilityUuid
     * @param int $monthlyBill
     * @param int $targetUsageOffset
     * @param int $azimuth
     * @param int $tilt
     * @return Collection|array
     */
    private function getInputsWithoutZipCode(
        string $utilityUuid,
        int    $monthlyBill,
        int    $targetUsageOffset = 100,
        int    $azimuth = 180,
        int    $tilt = 26
    ): Collection|array
    {
        return OpenCalculatorInput::query()
            ->where(OpenCalculatorInput::FIELD_UTILITY_UUID, $utilityUuid)
            ->where(OpenCalculatorInput::FIELD_MONTHLY_BILL, '>=', ($monthlyBill - self::BILL_TOLERANCE))
            ->where(OpenCalculatorInput::FIELD_MONTHLY_BILL, '<=', ($monthlyBill + self::BILL_TOLERANCE))
            ->where(OpenCalculatorInput::FIELD_ELECTRIC_OFFSET, $targetUsageOffset)
            ->where(OpenCalculatorInput::FIELD_AZIMUTH, $azimuth)
            ->where(OpenCalculatorInput::FIELD_TILT, $tilt)
            ->where(OpenCalculatorInput::FIELD_MONTH, Carbon::now()->subMonth()->month)
            ->get();
    }

    /**
     * @param int $monthlyBill
     * @param int $targetUsageOffset
     * @param int $azimuth
     * @param int $tilt
     * @return Collection|array
     */
    private function getInputsWithoutUtilityOrZipCode(
        int $monthlyBill,
        int $targetUsageOffset = 100,
        int $azimuth = 180,
        int $tilt = 26
    ): Collection|array
    {
        return OpenCalculatorInput::query()
            ->where(OpenCalculatorInput::FIELD_MONTHLY_BILL, '>=', ($monthlyBill - self::BILL_TOLERANCE))
            ->where(OpenCalculatorInput::FIELD_MONTHLY_BILL, '<=', ($monthlyBill + self::BILL_TOLERANCE))
            ->where(OpenCalculatorInput::FIELD_UTILITY_UUID, self::FALLBACK_UTILITY_UUID)
            ->where(OpenCalculatorInput::FIELD_ELECTRIC_OFFSET, $targetUsageOffset)
            ->where(OpenCalculatorInput::FIELD_AZIMUTH, $azimuth)
            ->where(OpenCalculatorInput::FIELD_TILT, $tilt)
            ->where(OpenCalculatorInput::FIELD_MONTH, Carbon::now()->subMonth()->month)
            ->get();
    }

    /**
     * @return OpenCalculatorInput|Model|null
     */
    private function getGeneralFallbackInput(): OpenCalculatorInput|Model|null
    {
        return OpenCalculatorInput::query()
            ->where(OpenCalculatorInput::FIELD_ELECTRIC_OFFSET, 100)
            ->where(OpenCalculatorInput::FIELD_AZIMUTH, 180)
            ->where(OpenCalculatorInput::FIELD_TILT, 26)
            ->where(OpenCalculatorInput::FIELD_MONTHLY_BILL, 200)
            ->where(OpenCalculatorInput::FIELD_UTILITY_UUID, self::FALLBACK_UTILITY_UUID)
            ->first();
    }

    /**
     * @param string $utilityUuid
     * @return array
     */
    public function getIncentivesByUtilityUuid(string $utilityUuid): array
    {
        /** @var EloquentUtility $utility */
        $utility = $this->utilityRepository->findByUuid($utilityUuid);

        return $utility ? $this->incentiveLookupService->getUtilityIncentives($utility)->pluck(IncentivesLookup::RELATION_INCENTIVE)->toArray() : [];
    }

    /**
     * @param string $zipCode
     * @return float|int
     */
    public function getSystemCost(string $zipCode): float|int
    {
        $state = $this->locationRepository->getZipCode($zipCode)?->state_abbr;

        if($state) {
            return $this->systemCostRepository->getCostInState($state, self::DEFAULT_SYSTEM_SIZE_IN_KW);
        }

        return SystemCostRepository::DEFAULT_GENERATED_COST;
    }
}
