<?php

namespace App\Repositories\Odin;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyService;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Services\CompanyRegistration\CompanyRegistrationSyncService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use App\Repositories\Legacy\CompanyRepository as LegacyCompanyRepository;
use Exception;

class CompanyIndustryServiceRepository
{
    const REQUEST_COMPANY_SERVICES  = 'services';
    const RESPONSE_STATUS_KEY       = 'status';

    /**
     * @param IndustryCompanyRepository      $industryCompanyRepository
     * @param IndustryServiceRepository      $industryServiceRepository
     * @param CompanyRegistrationSyncService $companySyncService
     * @param LegacyCompanyRepository        $legacyCompanyRepository
     */
    public function __construct(
        protected IndustryCompanyRepository       $industryCompanyRepository,
        protected IndustryServiceRepository       $industryServiceRepository,
        protected CompanyRegistrationSyncService  $companySyncService,
        protected LegacyCompanyRepository         $legacyCompanyRepository,
    ) {}

    /**
     * Add a new IndustryService to a Company if not already present
     * If the Industry that the IndustryService BelongsTo is not currently on the Company, add that too
     *
     * @param Company $company
     * @param IndustryService $industryService
     * @return bool - returns true if the Service/Industry is either found or successfully created
     */
    public function addCompanyServiceAndRequiredIndustry(Company $company, IndustryService $industryService): bool
    {

        $relatedIndustry = $industryService->{IndustryService::RELATION_INDUSTRY};

        $createdCompanyService = $this->addCompanyService($company, $industryService);
        if ($createdCompanyService) {
            $this->industryCompanyRepository->addIndustryCompany($relatedIndustry->{Industry::FIELD_ID}, $company->{Company::FIELD_ID});
        }

        return true;
    }


    /**
     * Add an IndustryService to a Company on the CompanyService relation, if it doesn't already exist
     *
     * @param Company|Model $company
     * @param IndustryService|Model $industryService
     * @return bool
     */
    public function addCompanyService(Company|Model $company, IndustryService|Model $industryService): bool
    {
        if (!$company->services()
            ->where(CompanyService::FIELD_COMPANY_ID, $company->{Company::FIELD_ID})
            ->where(CompanyService::FIELD_INDUSTRY_SERVICE_ID, $industryService->{IndustryService::FIELD_ID})
            ->first()) {
                $company->services()->attach($industryService);
                return true;
        }
        return false;
    }

    /**
     * Remove an IndustryService from a Company. Clean up any unused Industries if required.
     *
     * @param Company $company
     * @param IndustryService $industryService
     * @param bool $cleanUpEmptyIndustries
     * @return bool
     * @throws Exception
     */
    public function removeCompanyService(Company $company, IndustryService $industryService, ?bool $cleanUpEmptyIndustries = false): bool
    {
        if ($company->services()->detach($industryService->{IndustryService::FIELD_ID})) {
            if ($cleanUpEmptyIndustries) $this->cleanUpCompanyIndustries($company);

            $industryService->{IndustryService::FIELD_INDUSTRY_ID} = $this->legacyCompanyRepository->getLegacyCompanyTypeFromIndustryIds([$industryService->{IndustryService::FIELD_INDUSTRY_ID}]);
            $legacyData = $this->companySyncService->transformForLegacy(CompanyService::class, $industryService->toArray());

            $legacyResponse = $this->legacyCompanyRepository->removeCompanyService(
                companyReference : $company->{Company::FIELD_REFERENCE},
                service          : $legacyData
            );

            if(!$legacyResponse
            || !key_exists(self::RESPONSE_STATUS_KEY, $legacyResponse)
            || !$legacyResponse[self::RESPONSE_STATUS_KEY]) {
                logger()->warning(
                    "The removal of a service `$industryService->name` from the company `$company->name` couldn't go through due to an error. The Legacy server responded as: " . json_encode($legacyResponse)
                );
            }

            return true;
        }
        return false;
    }

    /**
     * Remove any Industries from a Company where they no longer provide any IndustryServices from that Industry
     *
     * @param Company $company
     * @return void
     */
    public function cleanUpCompanyIndustries(Company $company): void
    {
        $industriesInUse = $company->services()->get()
            ->reduce(fn($output, $service) => $output->contains($service->{IndustryService::RELATION_INDUSTRY}->{Industry::FIELD_ID})
                ? $output
                : $output->push($service->{IndustryService::RELATION_INDUSTRY}->{Industry::FIELD_ID})
            , collect());
        $company->industries()->each(function($industry) use ($company, $industriesInUse) {
            if (!$industriesInUse->contains($industry->{Industry::FIELD_ID})) {
                $company->industries()->detach($industry->{Industry::FIELD_ID});
            }
        });
    }

    /**
     * Return a Collection of IndustryServices by slug or array of slugs
     *
     * @param array<string>|string $slugOrSlugs
     * @return Collection
     */
    public function getIndustryServicesBySlug(array|string $slugOrSlugs): Collection
    {
        $slugs = collect($slugOrSlugs);
        return $slugs->reduce(function(Collection $output, string $slug) {
            $industryService = IndustryService::query()->where(IndustryService::FIELD_SLUG, $slug)->first();
            return $industryService ? $output->push($industryService) : $output;
        }, collect());
    }

    /**
     * @param Company $company
     * @param array   $industryServiceIds
     * @param bool    $includeSettingUpIndustries
     * @param ?bool   $clearServices
     * @return Collection|null
     * @throws Exception
     */
    public function setupServicesForCompany(Company $company, array $industryServiceIds, bool $includeSettingUpIndustries = true, ?bool $clearServices = false): ?Collection
    {
        if(!$industryServiceIds) return null;

        if ($clearServices) {
            $company->services()->detach();
            $company->industries()->detach();
        }

        $services   = $this->industryServiceRepository->getIndustryServicesById($industryServiceIds);
        $legacyData = [];

        $services = $services->map(function (IndustryService $service) use ($company, $includeSettingUpIndustries, &$legacyData){
            $includeSettingUpIndustries
                ? $this->addCompanyServiceAndRequiredIndustry($company, $service)
                : $this->addCompanyService($company, $service);

            $service->{IndustryService::FIELD_INDUSTRY_ID} = $this->legacyCompanyRepository->getLegacyCompanyTypeFromIndustryIds([$service->{IndustryService::FIELD_INDUSTRY_ID}]);
            $legacyData[] = $this->companySyncService->transformForLegacy(CompanyService::class, $service->toArray());

            return true;
        });

        $legacyResponse = $this->legacyCompanyRepository->setCompanyServices(
            companyReference : $company->{Company::FIELD_REFERENCE},
            services         : [self::REQUEST_COMPANY_SERVICES => $legacyData]
        );

        if(!$legacyResponse
        || !key_exists(self::RESPONSE_STATUS_KEY, $legacyResponse)
        || !$legacyResponse[self::RESPONSE_STATUS_KEY]) {
            logger()->warning(
                "The industry services couldn't sync with the Legacy server for the company `$company->name`, ID: `$company->id`, Reference: `$company->reference`."
                .PHP_EOL."The requested industry services IDs were: " . json_encode($industryServiceIds)
                .PHP_EOL."The Legacy server responded as: " . json_encode($legacyResponse)
            );
        }

        return $services;
    }

}
