<?php

namespace App\Repositories\Odin;

use App\Models\Legacy\Location;
use App\Models\Legacy\SolarCalculatorEngine\Incentive;
use App\Services\Legacy\IncentiveLookupService;

class SolarIncentivesRepository
{
    public function __construct(protected IncentiveLookupService $incentiveLookupService)
    {
    }

    /**
     * @param Location|null $location
     * @return array
     */
    public function getDataByLocation(?Location $location): array
    {
        $filteredIncentiveLookups = $this->incentiveLookupService->getLocationIncentives($location)->filter(function ($incentiveLookup) {
            return in_array($incentiveLookup->incentive->sector, ['State', 'Federal']);
        });

        return $filteredIncentiveLookups->map(function($incentive) {
            return [
                'name'   => $incentive->incentive->{Incentive::FIELD_NAME},
                'type'   => $incentive->incentive->{Incentive::FIELD_TYPE},
                'sector' => $incentive->incentive->{Incentive::FIELD_SECTOR},
                'url'    => $incentive->incentive->{Incentive::FIELD_URL},
                'notes'  => $incentive->incentive->{Incentive::FIELD_DISPLAY_NOTE}
            ];
        })->toArray();
    }
}
