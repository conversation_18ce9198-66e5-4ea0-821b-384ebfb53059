<?php

namespace App\Repositories\Odin\ProductStatistics;

use App\Enums\Campaigns\CampaignStatus;
use App\Enums\Campaigns\Modules\Budget\BudgetType;
use App\Enums\Odin\BudgetCategory;
use App\Enums\Odin\SolarConfigurableFields;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModule;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductData;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductRejection;
use App\Models\Odin\ServiceProduct;
use App\Models\SaleType;
use Carbon\Carbon;
use Carbon\CarbonTimeZone;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;

class ProductStatisticsRepository
{
    const string ALIAS_PREMIUM_ELECTRIC_COST      = 'premium_electric_cost';
    const string ALIAS_CONSUMER_PRODUCT_COUNT     = 'consumer_product_count';
    const string ALIAS_DELIVERED_COUNT            = 'product_assignment_delivered_count';
    const string ALIAS_CHARGEABLE_COUNT           = 'product_assignment_chargeable_count';
    const string ALIAS_CHARGEABLE_COST            = 'product_assignment_chargeable_cost';
    const string ALIAS_BUDGET_TYPE                = 'budget_type';
    const string ALIAS_CONSUMER_SALE_KEY          = 'consumer_products_sale_type_key';
    const string ALIAS_ASSIGNMENT_SALE_KEY        = 'product_assignments_sale_type_key';
    const string ALIAS_PRODUCT_CREATED_AT         = 'consumer_products_created_at';
    const string ALIAS_PRODUCT_CREATED_AT_MONTHLY = 'consumer_products_created_at_monthly';
    const string ALIAS_BUDGET_ENABLED             = 'budget_enabled';

    const int BUDGET_TYPE_VERIFIED   = 0;
    const int BUDGET_TYPE_UNVERIFIED = 1;
    const int BUDGET_TYPE_EMAIL_ONLY = 2;

    const string ALIAS_SALE_TYPE_TABLE = 'st2';

    protected Builder $query;

    protected bool $useStraightJoin = true;

    protected array $rawSelects = [
        self::ALIAS_CONSUMER_PRODUCT_COUNT     => "COUNT(DISTINCT " . ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID . ") AS " . self::ALIAS_CONSUMER_PRODUCT_COUNT,
        self::ALIAS_DELIVERED_COUNT            => "COUNT(DISTINCT " . ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_ID . ") AS " . self::ALIAS_DELIVERED_COUNT,
        self::ALIAS_CHARGEABLE_COUNT           => "COUNT(DISTINCT CASE WHEN " . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CHARGEABLE . " IS TRUE THEN " . ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_ID . " END) AS " . self::ALIAS_CHARGEABLE_COUNT,
        self::ALIAS_CHARGEABLE_COST            => "SUM(CASE WHEN " . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CHARGEABLE . " IS TRUE THEN " . ProductAssignment::FIELD_COST . " END) AS " . self::ALIAS_CHARGEABLE_COST,
        self::ALIAS_BUDGET_TYPE                => "CASE WHEN " . Consumer::TABLE . '.' . Consumer::FIELD_CLASSIFICATION . " = 0 THEN " . self::BUDGET_TYPE_EMAIL_ONLY
            . " WHEN " . Consumer::TABLE . '.' . Consumer::FIELD_CLASSIFICATION . " = 2 THEN " . self::BUDGET_TYPE_UNVERIFIED
            . " ELSE " . self::BUDGET_TYPE_VERIFIED . " END AS " . self::ALIAS_BUDGET_TYPE,
        self::ALIAS_PREMIUM_ELECTRIC_COST      => "IF(CAST(JSON_EXTRACT(" . ConsumerProductData::TABLE . '.' . ConsumerProductData::FIELD_PAYLOAD . ", '$." . SolarConfigurableFields::ELECTRIC_COST->value . "') AS UNSIGNED) > 300, 1, 0) AS " . self::ALIAS_PREMIUM_ELECTRIC_COST,
        self::ALIAS_PRODUCT_CREATED_AT         => "DATE_FORMAT(CONVERT_TZ(" . ConsumerProduct::TABLE . '.' . ConsumerProduct::CREATED_AT . ", 'UTC', 'TIMEZONE_PLACEHOLDER'), '%Y-%m-%d') AS " . self::ALIAS_PRODUCT_CREATED_AT,
        self::ALIAS_PRODUCT_CREATED_AT_MONTHLY => "DATE_FORMAT(CONVERT_TZ(" . ConsumerProduct::TABLE . '.' . ConsumerProduct::CREATED_AT . ", 'UTC', 'TIMEZONE_PLACEHOLDER'), '%Y-%m') AS " . self::ALIAS_PRODUCT_CREATED_AT,
    ];

    protected CarbonTimeZone $timezone;

    public function __construct() {
        $this->timezone = CarbonTimeZone::create();
    }

    /**
     * @param int $companyId
     * @param int|array $serviceProductIds
     * @param Carbon $startDate
     * @param bool|null $monthlyGrouping
     * @return Builder
     */
    public function getAnalyticsVolumeStatistics(int $companyId, int|array $serviceProductIds, Carbon $startDate, ?bool $monthlyGrouping = false): Builder
    {
        return $this->getBaseCampaignLocationQuery($startDate)
            ->addJoinsForAnalytics()
            ->forServiceProducts($serviceProductIds)
            ->addSelectsForAnalytics($monthlyGrouping)
            ->addConstraintsForAnalytics($companyId)
            ->excludeRejectedProducts()
            ->withAvailableConsumerProductStatuses()
            ->groupForAnalytics()
            ->getQuery();
    }

    /**
     * @param int $companyId
     * @param int $serviceProductId
     * @return Builder
     */
    public function getCampaignListStatistics(int $companyId, int $serviceProductId): Builder
    {
        return $this->getBaseCampaignLocationQuery()
            ->addJoinsForCampaignList()
            ->addSelectsForCampaignList()
            ->forServiceProducts($serviceProductId)
            ->addConstraintsForCampaignList($companyId)
            ->excludeRejectedProducts()
            ->withAvailableConsumerProductStatuses()
            ->groupForCampaignList()
            ->getQuery();
    }

    /**
     * @param int $companyId
     * @param int $serviceProductId
     * @param int|null $stateLocationId
     * @param int|null $countyLocationId
     * @return Builder
     */
    public function getLocationStatistics(
        int $companyId,
        int $serviceProductId,
        ?int $stateLocationId,
        ?int $countyLocationId,
    ): Builder
    {
        return $this->getBaseConsumerProductQuery()
            ->addJoinsForLocation($companyId)
            ->addSelectsForLocation()
            ->forServiceProducts($serviceProductId)
            ->addTimeConstraints()
            ->addConstraintsForLocation($stateLocationId, $countyLocationId)
            ->excludeRejectedProducts()
            ->groupForLocation()
            ->getQuery();
    }

    /**
     * @param int $companyId
     * @param int $serviceProductId
     * @return Builder
     */
    public function getPurchasedProductTypeStatistics(int $companyId, int $serviceProductId): Builder
    {
        return $this->getBaseProductAssignmentQuery()
            ->addSelectsForProductType()
            ->addConstraintsForProductType($companyId)
            ->forServiceProducts($serviceProductId)
            ->addTimeConstraints()
            ->excludeRejectedProducts()
            ->groupForProductType()
            ->getQuery();
    }

    /**
     * @param Carbon|null $startDate
     * @return self
     */
    protected function getBaseCampaignLocationQuery(?Carbon $startDate = null): self
    {
        $startDate = $startDate ?? now()->subDays(30)->toDateString();

        $this->query = CompanyCampaignLocationModuleLocation::query()
            ->join(CompanyCampaignLocationModule::TABLE, CompanyCampaignLocationModule::TABLE .'.'. CompanyCampaignLocationModule::FIELD_ID, CompanyCampaignLocationModuleLocation::TABLE .'.'. CompanyCampaignLocationModuleLocation::FIELD_MODULE_ID)
            ->join(CompanyCampaign::TABLE, fn(JoinClause $join) =>
                $join->on(CompanyCampaignLocationModule::TABLE .'.'. CompanyCampaignLocationModule::FIELD_CAMPAIGN_ID, CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID)
                    ->on(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_COMPANY_ID, CompanyCampaignLocationModuleLocation::TABLE .'.'. CompanyCampaignLocationModuleLocation::FIELD_COMPANY_ID)
                    ->whereNull(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_DELETED_AT)
            )->join(ServiceProduct::TABLE, fn(JoinClause $join) =>
                $join->on(ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_SERVICE_ID)
                    ->on(ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_PRODUCT_ID, CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_PRODUCT_ID)
            )->join(Address::TABLE, fn(JoinClause $join) =>
                $join->on(Address::TABLE .'.'. Address::FIELD_ZIP_CODE_LOCATION_ID, CompanyCampaignLocationModuleLocation::TABLE .'.'. CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID)
                    ->where(Address::TABLE .'.'. Address::CREATED_AT, '>', $startDate)
            )->join(ConsumerProduct::TABLE, fn(JoinClause $join) =>
                $join->on(ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ADDRESS_ID, Address::TABLE .'.'. Address::FIELD_ID)
                    ->on(ConsumerProduct::FIELD_SERVICE_PRODUCT_ID, ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_ID)
                    ->where(ConsumerProduct::TABLE .'.'. ConsumerProduct::CREATED_AT, '>', $startDate)
            )->join(BudgetContainer::TABLE, BudgetContainer::TABLE .'.'. BudgetContainer::FIELD_COMPANY_CAMPAIGN_ID, CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID)
            ->join(Budget::TABLE, Budget::TABLE .'.'. Budget::FIELD_BUDGET_CONTAINER_ID, BudgetContainer::TABLE .'.'. BudgetContainer::FIELD_ID)
            ->leftJoin(ProductAssignment::TABLE, fn(JoinClause $join) =>
                $join->on(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ID)
                    ->on(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_BUDGET_ID, Budget::TABLE .'.'. Budget::FIELD_ID)
                    ->where(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_DELIVERED, true)
            );

        return $this;
    }

    /**
     * @return $this
     */
    protected function getBaseConsumerProductQuery(): self
    {
        $this->query = ConsumerProduct::query()
            ->join(ServiceProduct::TABLE, ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_ID, ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_SERVICE_PRODUCT_ID)
            ->join(Consumer::TABLE, Consumer::TABLE .'.'. Consumer::FIELD_ID, ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_CONSUMER_ID)
            ->join(Address::TABLE, Address::TABLE .'.'. Address::FIELD_ID, ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ADDRESS_ID)
            ->where(ConsumerProduct::FIELD_CONTACT_REQUESTS, '>', 0);

        return $this;
    }

    /**
     * @return self
     */
    protected function getBaseProductAssignmentQuery(): self
    {
        $optionalBudgetKeys = [BudgetCategory::UNVERIFIED->value, BudgetCategory::EMAIL_ONLY->value];

        $this->query = ProductAssignment::query()
            ->join(ConsumerProduct::TABLE, ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ID, ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_CONSUMER_PRODUCT_ID)
            ->join(ServiceProduct::TABLE, ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_ID, ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_SERVICE_PRODUCT_ID)
            ->join(SaleType::TABLE, SaleType::TABLE .'.'. SaleType::FIELD_ID, ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_SALE_TYPE_ID)
            ->leftJoin(CompanyCampaign::TABLE, fn(JoinClause $join) =>
                $join->on(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_PRODUCT_ID, ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_PRODUCT_ID)
                    ->on(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_SERVICE_ID, ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_INDUSTRY_SERVICE_ID)
                    ->on(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_COMPANY_ID, ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_COMPANY_ID)
                    ->whereNull(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_DELETED_AT)
                    ->where(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_STATUS, CampaignStatus::ACTIVE)
            )->leftJoin(BudgetContainer::TABLE, BudgetContainer::TABLE .'.'. BudgetContainer::FIELD_COMPANY_CAMPAIGN_ID, CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID)
            ->leftJoin(Budget::TABLE, fn(JoinClause $join) =>
                $join->on(Budget::TABLE .'.'. Budget::FIELD_BUDGET_CONTAINER_ID, BudgetContainer::TABLE .'.'. BudgetContainer::FIELD_ID)
                    ->on(Budget::TABLE .'.'. Budget::FIELD_KEY, SaleType::TABLE .'.'. SaleType::FIELD_KEY)
                    ->where(Budget::TABLE .'.'. Budget::FIELD_STATUS, Budget::STATUS_ENABLED)
                    ->whereIn(Budget::TABLE .'.'. Budget::FIELD_KEY, $optionalBudgetKeys)
            );

        return $this;
    }

    /**
     * TODO: remove if not needed
     * @return $this
     */
    protected function addJoinsForAnalytics(): self
    {
        return $this;
    }

    /**
     *  TODO: remove if not needed
     * @return $this
     */
    protected function addJoinsForCampaignList(): self
    {
        return $this;
    }

    /**
     * @param int $companyId
     * @return $this
     */
    protected function addJoinsForLocation(int $companyId): self
    {
        $this->query->leftJoin(ProductAssignment::TABLE, fn(JoinClause $join) =>
            $join->on(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ID)
                ->where(ProductAssignment::FIELD_DELIVERED, true)
                ->where(ProductAssignment::FIELD_COMPANY_ID, $companyId)
            )->join(ConsumerProductData::TABLE, ConsumerProductData::TABLE .'.'. ConsumerProductData::FIELD_ID, ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_CONSUMER_PRODUCT_DATA_ID)
            ->leftJoin(SaleType::TABLE, SaleType::TABLE .'.'. SaleType::FIELD_ID, ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_SALE_TYPE_ID)
            ->join(SaleType::TABLE . " AS " . self::ALIAS_SALE_TYPE_TABLE, fn(JoinClause $join) =>
                $join->on(self::ALIAS_SALE_TYPE_TABLE .'.'. SaleType::FIELD_SALE_LIMIT, ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_CONTACT_REQUESTS)
                    ->where(self::ALIAS_SALE_TYPE_TABLE .'.'. SaleType::FIELD_DEFAULT_TYPE, true)
            );

        return $this;
    }

    /**
     * @param bool $monthlyGrouping
     * @return $this
     */
    protected function addSelectsForAnalytics(bool $monthlyGrouping = false): self
    {
        $dateSelect = $monthlyGrouping
            ? $this->rawSelects[self::ALIAS_PRODUCT_CREATED_AT_MONTHLY]
            : $this->rawSelects[self::ALIAS_PRODUCT_CREATED_AT];

        $dateSelect = str($dateSelect)->replace('TIMEZONE_PLACEHOLDER', $this->getTimezone());

        $this->query->select(DB::raw(
            ($this->useStraightJoin ? 'STRAIGHT_JOIN ' : '')
            . $dateSelect
            .','. $this->rawSelects[self::ALIAS_CONSUMER_PRODUCT_COUNT]
            .','. $this->rawSelects[self::ALIAS_CHARGEABLE_COUNT]
            .','. $this->rawSelects[self::ALIAS_CHARGEABLE_COST]
            .','. $this->rawSelects[self::ALIAS_DELIVERED_COUNT]
        ));

        return $this;
    }
    /**
     * @return self
     */
    protected function addSelectsForCampaignList(): self
    {
        $this->query->select(DB::raw(
            ($this->useStraightJoin ? 'STRAIGHT_JOIN ' : '')
            . CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID
            .','. CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_REFERENCE
            .', MAX('. Budget::TABLE .'.'. Budget::FIELD_LAST_MODIFIED_AT . ") AS " . Budget::FIELD_LAST_MODIFIED_AT
            .','. $this->rawSelects[self::ALIAS_CONSUMER_PRODUCT_COUNT]
            .','. $this->rawSelects[self::ALIAS_CHARGEABLE_COUNT]
            .','. $this->rawSelects[self::ALIAS_CHARGEABLE_COST],
        ));

        return $this;
    }

    /**
     * @return $this
     */
    protected function addSelectsForLocation(): self
    {
        $this->query->select(DB::raw(
            SaleType::TABLE .'.'. SaleType::FIELD_KEY . " AS " . self::ALIAS_ASSIGNMENT_SALE_KEY
            .','. ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_CONTACT_REQUESTS
            .','. $this->rawSelects[self::ALIAS_BUDGET_TYPE]
            .','. $this->rawSelects[self::ALIAS_PREMIUM_ELECTRIC_COST]
            .','. $this->rawSelects[self::ALIAS_CONSUMER_PRODUCT_COUNT]
            .','. $this->rawSelects[self::ALIAS_CHARGEABLE_COUNT]
            .','. self::ALIAS_SALE_TYPE_TABLE .'.'. SaleType::FIELD_KEY . " AS " . self::ALIAS_CONSUMER_SALE_KEY
        ));

        return $this;
    }

    /**
     * @return $this
     */
    protected function addSelectsForProductType(): self
    {
        $this->query->select(DB::raw(
            "COUNT(DISTINCT " . ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_ID . ")" . " AS " . self::ALIAS_CHARGEABLE_COUNT
            .','. "MAX(" . Budget::TABLE .'.'. Budget::FIELD_STATUS . ")" . " AS " . self::ALIAS_BUDGET_ENABLED
            .','. SaleType::TABLE .'.'. SaleType::FIELD_KEY
        ));

        return $this;
    }

    /**
     * @param int $companyId
     * @return self
     */
    protected function addConstraintsForAnalytics(int $companyId): self
    {
        $this->query->where(CompanyCampaignLocationModuleLocation::TABLE .'.'. CompanyCampaignLocationModuleLocation::FIELD_COMPANY_ID, $companyId);

        return $this;
    }

    /**
     * @param int $companyId
     * @return self
     */
    protected function addConstraintsForCampaignList(int $companyId): self
    {
        $this->query->where(CompanyCampaignLocationModuleLocation::TABLE .'.'. CompanyCampaignLocationModuleLocation::FIELD_COMPANY_ID, $companyId)
            ->whereColumn(ConsumerProduct::TABLE .'.'. ConsumerProduct::CREATED_AT, '>', DB::raw("GREATEST(" . Budget::TABLE .'.'. Budget::FIELD_LAST_MODIFIED_AT . ", '" . now()->subDays(30) . "')"));

        return $this;
    }

    /**
     * @param int|null $stateLocationId
     * @param int|null $countyLocationId
     * @return $this
     */
    protected function addConstraintsForLocation(?int $stateLocationId, ?int $countyLocationId): self
    {
        if ($countyLocationId)
            $this->query->where(Address::TABLE .'.'. Address::FIELD_COUNTY_LOCATION_ID, $countyLocationId);
        else if ($stateLocationId)
            $this->query->where(Address::TABLE .'.'. Address::FIELD_STATE_LOCATION_ID, $stateLocationId);

        return $this;
    }

    protected function addConstraintsForProductType(int $companyId): self
    {
        $this->query->where(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_COMPANY_ID, $companyId);

        return $this;
    }

    /**
     * @param array|int $serviceProductIds
     * @return $this
     */
    protected function forServiceProducts(array|int $serviceProductIds): self
    {
        if ($serviceProductIds) {
            if (gettype($serviceProductIds) === 'array')
                $this->query->whereIn(ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_ID, $serviceProductIds);
            else
                $this->query->where(ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_ID, $serviceProductIds);
        }

        return $this;
    }

    /**
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return $this
     */
    protected function addTimeConstraints(?Carbon $startDate = null, ?Carbon $endDate = null): self
    {
        $startDate = $startDate ?? now()->subDays(30)->startOfDay();

        $this->query->where(ConsumerProduct::TABLE .'.'. ConsumerProduct::CREATED_AT, '>=', $startDate);
        if ($endDate)
            $this->query->where(ConsumerProduct::TABLE .'.'. ConsumerProduct::CREATED_AT, '<', $endDate);

        return $this;
    }

    /**
     * @return $this
     */
    protected function excludeRejectedProducts(): self
    {
        $this->query->leftJoin(ProductRejection::TABLE, fn(JoinClause $join) =>
            $join->on(ProductRejection::TABLE .'.'. ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID, ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_ID)
                ->whereNull(ProductRejection::TABLE .'.'. ProductRejection::FIELD_DELETED_AT)
        )->whereNull(ProductRejection::TABLE .'.'. ProductRejection::FIELD_ID);

        return $this;
    }

    /**
     * @return $this
     */
    protected function withAvailableConsumerProductStatuses(): self
    {
        $this->query->whereIn(ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_STATUS, [
            ConsumerProduct::STATUS_UNDER_REVIEW,
            ConsumerProduct::STATUS_ALLOCATED,
            ConsumerProduct::STATUS_UNSOLD,
            ConsumerProduct::STATUS_PENDING_ALLOCATION,
        ]);

        return $this;
    }

    /**
     * @return self
     */
    protected function groupForAnalytics(): self
    {
        $this->query->groupBy(self::ALIAS_PRODUCT_CREATED_AT);

        return $this;
    }

    /**
     * @return self
     */
    protected function groupForCampaignList(): self
    {
        $this->query->groupBy(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID);

        return $this;
    }

    /**
     * @return $this
     */
    protected function groupForLocation(): self
    {
        $this->query->groupBy([
            self::ALIAS_PREMIUM_ELECTRIC_COST,
            self::ALIAS_BUDGET_TYPE,
            self::ALIAS_ASSIGNMENT_SALE_KEY,
            self::ALIAS_CONSUMER_SALE_KEY
        ]);

        return $this;
    }

    /**
     * @return self
     */
    protected function groupForProductType(): self
    {
        $this->query->groupBy(SaleType::TABLE .'.'. SaleType::FIELD_KEY);

        return $this;
    }

    /**
     * @return Builder
     */
    protected function getQuery(): Builder
    {
        return $this->query;
    }

    function setTimezone(CarbonTimeZone $timezone) {
        $this->timezone = $timezone;
    }

    function getTimezone() {
        return $this->timezone;
    }
}
