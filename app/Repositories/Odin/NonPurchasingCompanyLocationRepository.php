<?php

namespace App\Repositories\Odin;

use App\Models\CompanyUserRelationship;
use App\Models\Legacy\Location;
use App\Models\Locations\USLocation;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\NonPurchasingCompanyLocation;
use App\Models\ServiceRadius;
use App\Repositories\LocationRepository;
use App\Repositories\ZipCodeRepository;
use Carbon\Carbon;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class NonPurchasingCompanyLocationRepository
{
    /**
     * @param int $locationId
     * @return Collection
     */
    public function getCompanyIdsByLocation(int $locationId): Collection
    {
        return NonPurchasingCompanyLocation::query()
            ->where(NonPurchasingCompanyLocation::FIELD_LOCATION_ID, $locationId)
            ->pluck(NonPurchasingCompanyLocation::FIELD_COMPANY_ID);
    }

    /**
     * @param int $locationId
     * @param int $accountManagerUserId
     * @return Collection
     */
    public function getCompanyIdsByLocationForAccountManager(int $locationId, int $accountManagerUserId): Collection
    {
        return NonPurchasingCompanyLocation::query()
            ->join(Company::TABLE, Company::TABLE .'.'. Company::FIELD_ID, NonPurchasingCompanyLocation::TABLE .'.'. NonPurchasingCompanyLocation::FIELD_COMPANY_ID)
            ->join(CompanyUserRelationship::TABLE, CompanyUserRelationship::TABLE .'.'. CompanyUserRelationship::FIELD_COMPANY_ID, Company::TABLE .'.'. Company::FIELD_ID)
            ->where([
                NonPurchasingCompanyLocation::TABLE .'.'. NonPurchasingCompanyLocation::FIELD_LOCATION_ID => $locationId,
                CompanyUserRelationship::TABLE.'.'.CompanyUserRelationship::FIELD_USER_ID => $accountManagerUserId,
            ])->pluck(NonPurchasingCompanyLocation::TABLE .'.'. NonPurchasingCompanyLocation::FIELD_COMPANY_ID);
    }

    /**
     * @param Company $company
     * @return Collection<int, Location>
     */
    public function getStateLocationsByCompany(Company $company): Collection
    {
        return NonPurchasingCompanyLocation::query()
            ->join(USLocation::TABLE, fn(JoinClause $join) =>
                $join->on(USLocation::TABLE .'.'. USLocation::FIELD_ID, NonPurchasingCompanyLocation::TABLE .'.'. NonPurchasingCompanyLocation::FIELD_LOCATION_ID)
                    ->where(USLocation::FIELD_TYPE, USLocation::TYPE_STATE)
            )->where(NonPurchasingCompanyLocation::FIELD_COMPANY_ID, $company->id)
            ->distinct(USLocation::FIELD_STATE_KEY)
            ->pluck(USLocation::FIELD_STATE_KEY);
    }

    /**
     * @param CompanyLocation $companyLocation
     * @return bool
     */
    public function updateLocationsForCompanyLocation(CompanyLocation $companyLocation): bool
    {
        $locationRepository = app(LocationRepository::class);
        $zipCodeRepository = app(ZipCodeRepository::class);
        $this->removeRadiusBasedLocationsForCompanyLocation($companyLocation);
        $centralZipLocation = $locationRepository->getZipCode($companyLocation->address->zip_code);
        $centralZipCode = $zipCodeRepository->getByZipCodeString($companyLocation->address->zip_code);
        $radius = $this->getRadiusForCompanyLocation($companyLocation);
        if (!$centralZipCode)
            return false;

        $zipCodeStrings = $zipCodeRepository->getZipCodesByRadius($centralZipCode, $radius);

        return $this->insertNonPurchasingLocationsByZipCodeLocations($companyLocation, $zipCodeStrings, $centralZipLocation->state_abbr);
    }

    /**
     * @param CompanyLocation $location
     * @return bool
     */
    public function removeRadiusBasedLocationsForCompanyLocation(CompanyLocation $location): bool
    {
        return $location->nonPurchasingLocations()
            ->where(NonPurchasingCompanyLocation::FIELD_IS_SET_BY_RADIUS, true)
            ->delete();
    }

    /**
     * @param Company $company
     * @return bool
     */
    public function removeRadiusBasedLocationsForCompany(Company $company): bool
    {
        return NonPurchasingCompanyLocation::query()
            ->where(NonPurchasingCompanyLocation::FIELD_COMPANY_ID, $company->id)
            ->where(NonPurchasingCompanyLocation::FIELD_IS_SET_BY_RADIUS, true)
            ->delete();
    }

    /**
     * @param Collection $zipCodes
     * @param string $stateAbbr
     * @return Collection
     */
    private function getZipCodeLocationIdsByZipCodeAndStateQuery(Collection $zipCodes, string $stateAbbr): Collection
    {
        return USLocation::query()
            ->select(USLocation::FIELD_ID)
            ->whereIn(USLocation::FIELD_ZIP_CODE, $zipCodes->toArray())
            ->where(USLocation::FIELD_STATE_ABBREVIATION, $stateAbbr)
            ->pluck(USLocation::FIELD_ID);
    }
    /**
     * @param CompanyLocation $location
     * @return int
     */
    private function getRadiusForCompanyLocation(CompanyLocation $location): int
    {
        $radius = $location->company->serviceRadius?->radius;
        if (!$radius) {
            $radius = ServiceRadius::query()
                ->where(ServiceRadius::FIELD_LOCATION_ID, $location->address?->state_location_id)
                ->first()
                ?->radius;
        }

        return $radius ?? ServiceRadius::DEFAULT_RADIUS;
    }

    /**
     * @param CompanyLocation $companyLocation
     * @param Collection $zipCodes
     * @param string $stateAbbreviation
     * @return bool
     */
    private function insertNonPurchasingLocationsByZipCodeLocations(CompanyLocation $companyLocation, Collection $zipCodes, string $stateAbbreviation): bool
    {
        $stateAlias = 'state_location';
        $countyAlias = 'county_locations';
        $cityAlias = 'city_locations';

        $groupedData = USLocation::query()
            ->select([
                DB::raw("$cityAlias." . USLocation::FIELD_ID . ' as city_location_id'),
                DB::raw("$countyAlias." . USLocation::FIELD_ID . ' as county_location_id'),
                DB::raw("$stateAlias." . USLocation::FIELD_ID . ' as state_location_id'),
            ])
            ->join(USLocation::TABLE . " as $cityAlias", fn(JoinClause $join) =>
                $join->on("$cityAlias." . USLocation::FIELD_ID, USLocation::TABLE .'.'. USLocation::FIELD_PARENT_LOCATION_ID)
                    ->where("$cityAlias." . USLocation::FIELD_TYPE, USLocation::TYPE_CITY)
            )->join(USLocation::TABLE . " as $countyAlias", fn(JoinClause $join) =>
                $join->on("$countyAlias." . USLocation::FIELD_ID, "$cityAlias." . USLocation::FIELD_PARENT_LOCATION_ID)
                    ->where("$countyAlias." . USLocation::FIELD_TYPE, USLocation::TYPE_COUNTY)
            )->join(USLocation::TABLE . " as $stateAlias", fn(JoinClause $join) =>
                $join->on("$stateAlias." . USLocation::FIELD_ID, "$countyAlias." . USLocation::FIELD_PARENT_LOCATION_ID)
                    ->where("$stateAlias." . USLocation::FIELD_TYPE, USLocation::TYPE_STATE)
            )->whereIn(USLocation::FIELD_ZIP_CODE, $zipCodes->toArray())
            ->where(USLocation::FIELD_STATE_ABBREVIATION, $stateAbbreviation)
            ->get()
            ->groupBy(['state_location_id', 'county_location_id']);

        $insertData = [];
        $now = now();
        foreach ($groupedData as $stateLocationId => $stateLocation) {
            $insertData[] = $this->createNonPurchasingLocationRow($companyLocation->company_id, $companyLocation->id, $stateLocationId, $now);
            foreach($stateLocation as $countyLocationId => $countyLocation) {
                $insertData[] = $this->createNonPurchasingLocationRow($companyLocation->company_id, $companyLocation->id, $countyLocationId, $now);
                foreach($countyLocation as $cityLocation) {
                    $insertData[] = $this->createNonPurchasingLocationRow($companyLocation->company_id, $companyLocation->id, $cityLocation->city_location_id, $now);
                }
            }
        }

        return NonPurchasingCompanyLocation::query()
            ->insert($insertData);
    }

    /**
     * @param int $companyId
     * @param int $companyLocationId
     * @param int $locationId
     * @param Carbon $now
     * @return array
     */
    private function createNonPurchasingLocationRow(int $companyId, int $companyLocationId, int $locationId, Carbon $now): array
    {
        return [
            NonPurchasingCompanyLocation::FIELD_COMPANY_LOCATION_ID => $companyLocationId,
            NonPurchasingCompanyLocation::FIELD_COMPANY_ID          => $companyId,
            NonPurchasingCompanyLocation::FIELD_LOCATION_ID         => $locationId,
            NonPurchasingCompanyLocation::FIELD_IS_SET_BY_RADIUS    => true,
            NonPurchasingCompanyLocation::CREATED_AT                => $now,
        ];
    }
}