<?php

namespace App\Repositories\Odin;

use App\Models\Odin\Website;
use App\Models\Odin\WebsiteApiKey;
use App\Models\Odin\WebsiteApiKeyOrigin;
use App\Services\Odin\ApiKeyGeneratorService;

class WebsiteRepository
{
    public function __construct(protected ApiKeyGeneratorService $apiKeyGeneratorService) {}

    /**
     * @param string $name
     * @param string $url
     * @param string $abbreviation
     * @param string $cpDomain
     * @param int|null $id
     *
     * @return Website
     */
    public function updateOrCreateWebsite(string $name, string $url, string $abbreviation, string $cpDomain, ?int $id = null): Website
    {
        /** @var Website $website */
        $website = Website::query()->updateOrCreate(
            [Website::FIELD_ID => $id],
            [Website::FIELD_NAME => $name, Website::FIELD_URL => $url, Website::FIELD_ABBREVIATION => $abbreviation, Website::FIELD_CP_DOMAIN => $cpDomain]
        );

        return $website;
    }

    /**
     * @param Website $website
     *
     * @return bool|null
     */
    public function deleteWebsite(Website $website): ?bool
    {
        $website->apiKeys()->delete();

        return $website->delete();
    }

    /**
     * @param Website $website
     * @param string $keyName
     * @param bool $status
     *
     * @return WebsiteApiKey
     */
    public function addWebsiteApiKey(Website $website, mixed $websiteKeyPayload, bool $status = false): WebsiteApiKey
    {
        /** @var WebsiteApiKey $apiKey */
        $apiKey = $website->apiKeys()->create([
            WebsiteApiKey::FIELD_NAME => $websiteKeyPayload[WebsiteApiKey::FIELD_NAME],
            WebsiteApiKey::FIELD_KEY => $this->apiKeyGeneratorService->generateKey(),
            WebsiteApiKey::FIELD_STATUS => $status
        ]);


        // Remove empty values
        if (is_array($websiteKeyPayload[WebsiteApiKey::RELATION_ORIGINS])) {
            $filteredOrigins = array_filter($websiteKeyPayload[WebsiteApiKey::RELATION_ORIGINS], function($origin) {
                return !empty($origin[WebsiteApiKeyOrigin::FIELD_ORIGIN]);
            });

            $apiKey->origins()->createMany($filteredOrigins);
        }

        return $apiKey;
    }


    /**
     * @param WebsiteApiKey $websiteApiKey
     * @param mixed $websiteKeyPayload
     * @return WebsiteApiKey
     */
    public function updateWebsiteApiKeyOrigins(WebsiteApiKey $websiteApiKey, mixed $websiteKeyPayload): WebsiteApiKey
    {
        // TODO - Create a abstract sync method
        // Delete origin it is not inside incoming origins list.
        // Check it by website api key origin id
        foreach ($websiteApiKey->origins as $databaseOrigin) {
            $exists = array_filter(
                $websiteKeyPayload[WebsiteApiKey::RELATION_ORIGINS],
                fn($origin) =>
                    isset($origin[WebsiteApiKeyOrigin::FIELD_ID]) &&
                    $origin[WebsiteApiKeyOrigin::FIELD_ID] == $databaseOrigin->{WebsiteApiKeyOrigin::FIELD_ID}
            );

            if (!count($exists)) $databaseOrigin->delete();
        }

        // Create new origins
        foreach ($websiteKeyPayload[WebsiteApiKey::RELATION_ORIGINS] as $incomingOrigin) {
            if (!isset($incomingOrigin[WebsiteApiKeyOrigin::FIELD_ID])){
                $websiteApiKey->origins()->create($incomingOrigin);
            }
        }

        return $websiteApiKey;
    }

    /**
     * @param WebsiteApiKey $websiteApiKey
     * @param bool $status
     *
     * @return bool
     */
    public function updateWebsiteApiKeyStatus(WebsiteApiKey $websiteApiKey, bool $status): bool
    {
        $websiteApiKey->status = $status;

        return $websiteApiKey->save();
    }

    /**
     * @param string $abbreviation
     * @return Website|null
     */
    public function getWebsiteByAbbreviation(string $abbreviation): ?Website
    {
        /** @var Website|null $website */
        $website = Website::query()->where(Website::FIELD_ABBREVIATION, $abbreviation)->first();

        return $website;
    }
}
