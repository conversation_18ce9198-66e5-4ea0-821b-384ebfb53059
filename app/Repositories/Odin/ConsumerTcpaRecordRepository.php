<?php

namespace App\Repositories\Odin;

use App\Models\Odin\ConsumerProductTcpaRecord;

class ConsumerTcpaRecordRepository
{
    /**
     * Handles creating a new Consumer Tracking model
     *
     * @param array $data
     *
     * @return ConsumerProductTcpaRecord
     */
    public function createConsumerTcpaRecordFromAttributes(array $data = []): ConsumerProductTcpaRecord
    {
        $consumerTcpaRecord = new ConsumerProductTcpaRecord();
        $consumerTcpaRecord->fill(collect($data)->filter(fn($value) => $value !== null)->toArray());
        $consumerTcpaRecord->save();
        return $consumerTcpaRecord;
    }
}
