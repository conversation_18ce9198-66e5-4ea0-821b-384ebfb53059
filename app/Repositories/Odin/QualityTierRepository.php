<?php

namespace App\Repositories\Odin;

use App\Models\Odin\QualityTier as QualityTierModel;

class QualityTierRepository
{
    /**
     * @return array
     */
    public function all(): array
    {
        return QualityTierModel::query()->pluck(QualityTierModel::FIELD_ID, QualityTierModel::FIELD_NAME)->toArray();
    }

    /**
     * @param string $name
     *
     * @return QualityTierModel|null
     */
    public function findByName(string $name): ?QualityTierModel
    {
        /** @var QualityTierModel|null */
        return QualityTierModel::query()->where(QualityTierModel::FIELD_NAME, $name)->first();
    }

    /**
     * @param int $id
     *
     * @return QualityTierModel|null
     */
    public function findById(int $id): ?QualityTierModel
    {
        /** @var QualityTierModel|null */
        return QualityTierModel::query()->find($id);
    }
}
