<?php

namespace App\Repositories\Odin;

use App\Builders\DashboardShadowBuilder;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Industry;
use App\Models\User;
use App\Services\Dashboard\DashboardLoginTokenService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class DashboardShadowRepository
{
    /**
     * Returns a token for shadowing for a given company.
     *
     * @param Company $company
     * @param bool $isFixr
     *
     * @return string|null
     * @throws BindingResolutionException
     */
    public function getShadowToken(Company $company, bool $isFixr = false): ?string
    {
        $user = $this->getUserToShadow($company);
        /** @var User|null $shadower */
        $shadower = Auth::user();

        if($isFixr) {
            /** @var DashboardLoginTokenService $tokenService */
            $tokenService = app()->make(DashboardLoginTokenService::class);

            return $user && $shadower ? $tokenService->generateToken($user, $shadower) : null;
        }

        return DashboardShadowBuilder::query()
            ->forUser($user?->legacy_id)
            ->setShadower($shadower->legacy_user_id ?? Auth::id())
            ->setExpiration(config('app.client_api.expiration'))
            ->setSigningKey(config('app.client_api.signing_key'))
            ->getToken();
    }

    /**
     * Returns the url for shadowing.
     *
     * @param Company $company
     *
     * @return string
     */
    public function getShadowUrl(Company $company, bool $isFixr = false): string
    {
        if($isFixr)
        {
            $url = config('app.dashboard.fixr_url');
            if($url === null || (app()->isProduction() && !Str::startsWith($url, "https")) || !Str::startsWith($url, "http"))
                // fallback in case env pass through fails. allow non-secure http if NOT in production
                $url = app()->isProduction() ? "https://dashboard.fixr.com" : "https://devdashboard.fixr.com";

            return $url . "/login-with-token";
        }

        if ($company->industries->first(fn(Industry $industry) => $industry->name === \App\Enums\Odin\Industry::ROOFING->value))
            return config('app.dashboard.roofing_url') . "/shadow";

        //todo: aggregator

        return config('app.dashboard.url') . "/shadow";
    }

    /**
     * Returns the first active user for a company to shadow.
     *
     * @param Company $company
     *
     * @return CompanyUser|null
     */
    public function getUserToShadow(Company $company): ?CompanyUser
    {
        /** @var CompanyUser|null $user */
        $user = $company->users()
            ->where(CompanyUser::FIELD_CAN_LOG_IN, true)
            ->where(CompanyUser::FIELD_IS_CONTACT, false)
            ->where(CompanyUser::FIELD_STATUS, CompanyUser::STATUS_ACTIVE)
            ->first();

        return $user;
    }

    public function getAllShadowUrls(Company $company): array
    {
        //todo use enum to get these dashboard types
        //todo get the default for the company
        return [
            'solar'     => ['url' => config('app.dashboard.url') . "/shadow"],
            'roofing'   => ['url' => config('app.dashboard.roofing_url') . "/shadow"],
            'fixr'      => ['url' => $this->getShadowUrl($company, true)],
        ];
    }
}
