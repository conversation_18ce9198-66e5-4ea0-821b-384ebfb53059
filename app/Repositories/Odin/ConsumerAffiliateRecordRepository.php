<?php

namespace App\Repositories\Odin;

use App\Contracts\Repositories\Odin\ConsumerAffiliateRecordRepositoryContract;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductAffiliateRecord;

class ConsumerAffiliateRecordRepository implements ConsumerAffiliateRecordRepositoryContract
{
    /**
     * @param ConsumerRepository $consumerRepository
     */
    public function __construct(protected ConsumerRepository $consumerRepository){}

    /**
     * @inheritDoc
     */
    public function createOrUpdateConsumerAffiliateRecord(
        int $affiliate,
        int $campaign,
        ?string $trackName,
        ?string $trackCode,
        ?int $id = null
    ): ConsumerProductAffiliateRecord
    {
        /** @var ConsumerProductAffiliateRecord $affiliateRecord */
        $affiliateRecord = ConsumerProductAffiliateRecord::query()->updateOrCreate(
            [
                ConsumerProductAffiliateRecord::FIELD_ID => $id
            ],
            [
                ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID => $affiliate,
                ConsumerProductAffiliateRecord::FIELD_CAMPAIGN_ID  => $campaign,
                ConsumerProductAffiliateRecord::FIELD_TRACK_NAME   => $trackName,
                ConsumerProductAffiliateRecord::FIELD_TRACK_CODE   => $trackCode
            ]
        );

        return $affiliateRecord;
    }

    /**
     * @inheritDoc
     */
    public function prepareConsumerAffiliateRecord(?int $legacyId, array $data): bool
    {
        if (!$legacyId) return false;

        $consumer = $this->consumerRepository->findByLegacyIdOrFail($legacyId);

        /** @var ConsumerProduct|null $consumerProduct */
        $consumerProduct = $consumer->consumerProducts->firstOrFail();

        $consumerProduct->consumer_product_affiliate_record_id = $this->createOrUpdateConsumerAffiliateRecord(
            $data[ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID],
            $data[ConsumerProductAffiliateRecord::FIELD_CAMPAIGN_ID],
            $data[ConsumerProductAffiliateRecord::FIELD_TRACK_NAME],
            $data[ConsumerProductAffiliateRecord::FIELD_TRACK_CODE]
        )->id;

        return $consumerProduct->save();
    }
}
