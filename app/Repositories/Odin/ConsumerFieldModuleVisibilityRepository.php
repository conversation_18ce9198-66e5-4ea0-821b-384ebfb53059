<?php

namespace App\Repositories\Odin;

use App\Models\Odin\ConsumerFieldModuleVisibility;

class ConsumerFieldModuleVisibilityRepository
{
    public function saveMany(string $category, string $categoryId, array $fieldsVisibility): true
    {
        foreach ($fieldsVisibility as $item) {
            $this->updateOrCreate($category, $categoryId, $item);
        }

        return true;
    }

    public function getFieldsVisibilityByFieldCategory(string $category, int|string $categoryId): \Illuminate\Database\Eloquent\Collection
    {
        return ConsumerFieldModuleVisibility::query()
            ->where(ConsumerFieldModuleVisibility::FIELD_CONSUMER_FIELD_CATEGORY_ID, $categoryId)
            ->where(ConsumerFieldModuleVisibility::FIELD_CONSUMER_FIELD_CATEGORY, $category)
            ->get();
    }

    public function updateOrCreate(string $category, string $categoryId, array $fieldVisibility): void
    {
        ConsumerFieldModuleVisibility::query()
            ->updateOrCreate([
                ConsumerFieldModuleVisibility::FIELD_CONSUMER_FIELD_CATEGORY_ID => $categoryId,
                ConsumerFieldModuleVisibility::FIELD_CONSUMER_FIELD_CATEGORY => $category,
                ConsumerFieldModuleVisibility::FIELD_CONSUMER_FIELD_ID => $fieldVisibility[ConsumerFieldModuleVisibility::FIELD_CONSUMER_FIELD_ID],
                ConsumerFieldModuleVisibility::FIELD_MODULE_TYPE => $fieldVisibility[ConsumerFieldModuleVisibility::FIELD_MODULE_TYPE],
                ConsumerFieldModuleVisibility::FIELD_FEATURE_TYPE => $fieldVisibility[ConsumerFieldModuleVisibility::FIELD_FEATURE_TYPE],
                ConsumerFieldModuleVisibility::FIELD_CONSUMER_FIELD_TYPE => $fieldVisibility[ConsumerFieldModuleVisibility::FIELD_CONSUMER_FIELD_TYPE]
            ], [
                ConsumerFieldModuleVisibility::FIELD_IS_VISIBLE => $fieldVisibility[ConsumerFieldModuleVisibility::FIELD_IS_VISIBLE]
            ]);
    }
}
