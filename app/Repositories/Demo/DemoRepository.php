<?php

namespace App\Repositories\Demo;

use App\Enums\Calendar\DemoStatus;
use App\Models\Calendar\Demo;

class DemoRepository
{
    /**
     * @param int $calendarEventId
     * @param string $status
     * @param int $userId
     * @param string|null $note
     * @return Demo
     */
    public function updateOrCreate(
        int $calendarEventId,
        string $status,
        int $userId,
        ?string $note = null,
    ): Demo
    {
        $existing = Demo::query()->where([
            Demo::FIELD_CALENDAR_EVENT_ID => $calendarEventId,
        ])->first();

        // Keep the status as completed
        if ($existing?->status === DemoStatus::COMPLETED->value) {
            $status = DemoStatus::COMPLETED->value;
        }

        return Demo::query()->updateOrCreate([
            Demo::FIELD_CALENDAR_EVENT_ID => $calendarEventId,
        ], [
            Demo::FIELD_STATUS  => $status,
            Demo::FIELD_NOTE    => $note,
            Demo::FIELD_USER_ID => $userId,
        ]);
    }
}
