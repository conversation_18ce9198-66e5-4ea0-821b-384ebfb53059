<?php

namespace App\Repositories;

use App\Enums\Competitor;

class CompetitorRepository
{
    /**
     * @return array
     */
    public function getAllCompetitorsAsKeyValueSelectArray(): array
    {
        return Competitor::getAsKeyValueSelectArray();
    }

    /**
     * @return array
     */
    public function getAllCompetitorsAsValueArray(): array
    {
        return Competitor::getValueArray();
    }
}
