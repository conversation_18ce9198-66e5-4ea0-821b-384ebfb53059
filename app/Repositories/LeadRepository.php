<?php

namespace App\Repositories;

use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentQuoteCompany;

class LeadRepository
{
    /**
     * @param EloquentCompany $company
     * @param int|null $startTimestamp
     * @param int|null $endTimestamp
     *
     * @return int
     */
    public function getTotalLeadsForCompany(EloquentCompany $company, int $startTimestamp = null, int $endTimestamp = null): int
    {
        $query = EloquentQuoteCompany::query()
            ->where(EloquentQuoteCompany::COMPANY_ID, $company->companyid)
            ->where(EloquentQuoteCompany::DELIVERED, EloquentQuoteCompany::IS_DELIVERED);

        if ($startTimestamp) $query->where(EloquentQuoteCompany::TIMESTAMP_DELIVERED, '>=', $startTimestamp);

        if ($endTimestamp) $query->where(EloquentQuoteCompany::TIMESTAMP_DELIVERED, '<=', $endTimestamp);

        return $query->count();
    }
}
