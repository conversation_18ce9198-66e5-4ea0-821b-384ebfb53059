<?php

namespace App\Repositories;

use App\Models\UserFilterPreset;

class UserFilterPresetRepository
{
    /**
     * Takes care of adding and updating user settings.
     *
     * @param mixed $data
     * @param int $userId
     * @return bool
     */
    public function createOrUpdateFilterPreset(mixed $data, int $userId): bool
    {
        return UserFilterPreset::query()->updateOrCreate(
                [
                    UserFilterPreset::FIELD_USER_ID => $userId
                ],
                [
                    UserFilterPreset::FIELD_DATA => json_encode($data)
                ]
            ) !== null;
    }
}