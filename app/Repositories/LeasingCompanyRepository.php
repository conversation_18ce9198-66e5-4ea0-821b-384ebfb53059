<?php

namespace App\Repositories;

use App\Enums\GlobalConfigurationKey;
use App\Models\GlobalConfiguration;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\Company;

class LeasingCompanyRepository
{
    /**
     * @return Company|null
     */
    public function getLeasingCompany(): ?Company
    {
        /** @var GlobalConfiguration $globalConfiguration */
        $globalConfiguration = GlobalConfiguration::query()
            ->where(GlobalConfiguration::FIELD_CONFIGURATION_KEY, GlobalConfigurationKey::LEASING_COMPANY)
            ->first();

        if (!$globalConfiguration) {
            return null;
        }

        $companyReference = $globalConfiguration->configuration_payload->data->get('reference');

        if (!$companyReference) {
            return null;
        }

        /** @var Company|null */
        return Company::query()->where(Company::FIELD_REFERENCE, $companyReference)->first();
    }

    /**
     * @param string $zipCode
     *
     * @return LeadCampaign|null
     */
    public function getLeasingCompanyLegacyCampaignForZipCode(string $zipCode): ?LeadCampaign
    {
        $leasingCompany = $this->getLeasingCompany();

        if (!$leasingCompany) {
            logger()->error('No leasing company was found');

            return null;
        }

        /** @var LeadCampaign|null */
        return LeadCampaign::query()
            ->select(LeadCampaign::TABLE . '.*')
            ->join(
                LeadCampaignLocation::TABLE,
                LeadCampaign::TABLE . '.' . LeadCampaign::ID,
                LeadCampaignLocation::TABLE . '.' . LeadCampaignLocation::LEAD_CAMPAIGN_ID
            )
            ->join(
                Location::TABLE,
                LeadCampaignLocation::TABLE . '.' . LeadCampaignLocation::LOCATION_ID,
                Location::TABLE . '.' . Location::ID
            )
            ->whereNull(LeadCampaign::TABLE . '.' . LeadCampaign::DELETED_AT)
            ->where(LeadCampaign::TABLE . '.' . LeadCampaign::STATUS, LeadCampaign::STATUS_ACTIVE)
            ->where(LeadCampaign::TABLE . '.' . LeadCampaign::IS_MANAGED_BY_A2, false)
            ->where(LeadCampaign::TABLE . '.' . LeadCampaign::COMPANY_ID, $leasingCompany->legacy_id)
            ->where(Location::TABLE . '.' . Location::TYPE, Location::TYPE_ZIP_CODE)
            ->where(Location::TABLE . '.' . Location::ZIP_CODE, $zipCode)
            ->first();
    }
}
