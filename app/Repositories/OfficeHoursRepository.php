<?php

namespace App\Repositories;

use Carbon\Carbon;

class OfficeHoursRepository
{
    /**
     * The opening and closing hours for when a processor can call a lead.
     * <PERSON> has stated that this value does not need to be configurable, and for the foreseeable future
     * there's no cases in which we should call a lead outside these hours.
     */
    const OFFICE_OPENING_HOUR = 9;
    const OFFICE_CLOSING_HOUR = 20;

    /**
     * @param Carbon $time
     * @return bool
     */
    public function getOfficeOpenForTimeUTC(Carbon $time): bool
    {
        $openingTime = Carbon::createFromFormat('H', self::OFFICE_OPENING_HOUR, 'PST')->setTimezone('UTC');
        $closingTime = Carbon::createFromFormat('H', self::OFFICE_CLOSING_HOUR, 'PST')->setTimezone('UTC');
        return $time->between($openingTime, $closingTime);
    }
}
