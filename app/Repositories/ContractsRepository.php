<?php

namespace App\Repositories;

use App\Models\Contract;
use Illuminate\Support\Collection;

class ContractsRepository
{
    /**
     * @param string $id
     * @return Contract
     */
    public function findByContractProviderIdOrFail(string $id): Contract
    {
        /** @var Contract $contract */
        $contract = Contract::query()->where(Contract::FIELD_CONTRACT_PROVIDER_ID, $id)->firstOrFail();

        return $contract;
    }

    /**
     * @return Collection
     */
    public function getAllContracts(): Collection
    {
        return Contract::all();
    }

    /**
     * @param bool $status
     * @return Collection
     */
    public function getContractsForStatus(bool $status): Collection
    {
        return Contract::query()->where(Contract::FIELD_ACTIVE, $status)->get();
    }

    /**
     * @param int $websiteId
     * @param int $contract_key_id
     * @return Contract|null
     */
    public function getActiveContractForWebsiteAndKey(int $websiteId, int $contract_key_id): ?Contract
    {
        /** @var Contract $contract */
       $contract = Contract::query()
            ->where(Contract::FIELD_WEBSITE_ID, $websiteId)
            ->where(Contract::FIELD_CONTRACT_KEY_ID, $contract_key_id)
            ->where(Contract::FIELD_ACTIVE, true)
            ->first();

       return $contract;
    }
}