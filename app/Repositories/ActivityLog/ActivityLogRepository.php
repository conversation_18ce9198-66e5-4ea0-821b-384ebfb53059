<?php

namespace App\Repositories\ActivityLog;

use App\Enums\ActivityLog\ActivityLogDescription;
use App\Enums\ActivityLog\ActivityLogName;
use App\Enums\ActivityLog\ActivityLogSubjectType;
use App\Services\ImpersonateService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Spatie\Activitylog\Models\Activity;

class ActivityLogRepository
{
    const string LOG_NAME     = 'log_name';
    const string DESCRIPTION  = 'description';
    const string SUBJECT_TYPE = 'subject_type';
    const string SUBJECT_ID   = 'subject_id';
    const string CAUSER_TYPE  = 'causer_type';
    const string CAUSER_ID    = 'causer_id';
    const string PROPERTIES   = 'properties';
    const string BATCH_UUID   = 'batch_uuid';
    const string CREATED_AT   = 'created_at';
    const string UPDATED_AT   = 'updated_at';
    const string ID           = 'id';
    const string PROPERTY_FIELD_IMPERSONATING_USER_ID = 'impersonating_user_id';


    /**
     * @param string $logName
     * @param string $description
     * @param string|null $subjectType
     * @param int|null $subjectId
     * @param array|null $properties
     * @return Activity
     */
    public function createActivityLog(
        string  $logName,
        string  $description,
        ?string $subjectType = null,
        ?int    $subjectId = null,
        ?array  $properties = null,
    ): Activity
    {

        $user = Auth::user();

        $impersonatorId = Session::get(ImpersonateService::SESSION_KEY);

        //if user is being impersonated, store the impersonated id.
        if (!empty($impersonatorId)) {
            $properties[self::PROPERTY_FIELD_IMPERSONATING_USER_ID] = $user->id;
        }

        return Activity::query()
            ->create([
                self::LOG_NAME     => $logName,
                self::DESCRIPTION  => $description,
                self::SUBJECT_TYPE => $subjectType ?: null,
                self::SUBJECT_ID   => $subjectId ?: null,
                self::CAUSER_TYPE  => empty($user) ? null : $user::class,
                self::CAUSER_ID    => $impersonatorId ?: $user?->id,
                self::PROPERTIES   => $properties,
                self::BATCH_UUID   => Str::uuid(),
            ]);
    }

    /**
     * @param string $logName
     * @param string $description
     * @param string|null $subjectType
     * @param int|null $subjectId
     * @param array|null $properties
     * @param Carbon|null $createdAt
     * @param Carbon|null $updatedAt
     * @return array
     */
    public function formatActivityLog(
        string  $logName,
        string  $description,
        ?string $subjectType = null,
        ?int    $subjectId = null,
        ?array  $properties = null,
        ?Carbon $createdAt = null,
        ?Carbon $updatedAt = null,
    ): array
    {
        $user = Auth::user();

        $impersonatorId = Session::get(ImpersonateService::SESSION_KEY);

        //if user is being impersonated, store the impersonated id.
        if (!empty($impersonatorId)) {
            $properties[self::PROPERTY_FIELD_IMPERSONATING_USER_ID] = $user->id;
        }

        return [
            self::LOG_NAME     => $logName,
            self::DESCRIPTION  => $description,
            self::SUBJECT_TYPE => $subjectType ?: null,
            self::SUBJECT_ID   => $subjectId ?: null,
            self::CAUSER_TYPE  => empty($user) ? null : $user::class,
            self::CAUSER_ID    => $impersonatorId ?: $user?->id,
            self::PROPERTIES   => json_encode($properties),
            self::BATCH_UUID   => Str::uuid()->toString(),
            self::CREATED_AT   => $createdAt ?? now(),
            self::UPDATED_AT   => $updatedAt ?? now(),
        ];
    }

    /**
     * @param Collection $collection
     * @return bool
     */
    public function insertActivityLogs(Collection $collection): bool
    {
        return Activity::query()->insert($collection->toArray());
    }

    /**
     * @param array|null $names
     * @param ActivityLogSubjectType|null $subjectType
     * @param int|null $subjectId
     * @return Builder
     */
    public function getActivityLogs(
        ?array                  $names = null,
        ?ActivityLogSubjectType $subjectType = null,
        ?int                    $subjectId = null,
    ): Builder
    {
        return Activity::query()
            ->when(filled($names), fn (Builder $builder) => $builder->whereIn(self::LOG_NAME, $names))
            ->when(filled($subjectType), fn (Builder $builder) => $builder->where(self::SUBJECT_TYPE, $subjectType->getClass()))
            ->when(filled($subjectId), fn (Builder $builder) => $builder->where(self::SUBJECT_ID, $subjectId));
    }

    /**
     * @return ActivityLogDescription
     */
    public static function systemOrUser(): ActivityLogDescription
    {
        return filled(Auth::user()) ? ActivityLogDescription::USER_UPDATED : ActivityLogDescription::SYSTEM_UPDATED;
    }

    /**
     * @param string $uuid
     * @return Collection
     */
    public function getBatchedActivityLogs(string $uuid): Collection
    {
        return Activity::query()
            ->where('batch_uuid', $uuid)
            ->get();
    }

    /**
     * @param Collection $activityLogs
     * @return Collection
     */
    public function transformBatchedLogs(Collection $activityLogs): Collection
    {
        $logName = ActivityLogName::tryFrom($activityLogs->first()?->log_name ?? '');
        $transformer = $logName?->getBatchTransform();
        if ($transformer) {
            $transformer($activityLogs);
        }

        return $activityLogs;
    }
}
