<?php

namespace App\Repositories;

use App\Models\Action;
use App\Models\ActionTag;
use App\Services\Odin\ActionService;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class ActionRepository
{

    public function __construct(protected ActionService $actionService)
    {
    }

    /**
     * @param int $userId
     * @param string $subject
     * @param string $message
     * @param int|null $forId
     * @param string|null $relationType
     * @param int|null $taskId
     * @param int|null $categoryId
     * @param string|null $displayDate
     * @param bool|null $tagByEmail
     * @return Action
     */
    public function createAction(
        int $userId,
        string $subject,
        string $message,
        ?int $forId,
        ?string $relationType,
        ?int $taskId = null,
        ?int $categoryId = null,
        ?string $displayDate = null,
        ?bool $tagByEmail = null
    ): Action {
        /** @var Action $action */
        $action = Action::query()->create([
            Action::FIELD_FROM_USER_ID      => $userId,
            Action::FIELD_SUBJECT           => $subject,
            Action::FIELD_MESSAGE           => $message,
            Action::FIELD_FOR_ID            => $forId,
            Action::FIELD_FOR_RELATION_TYPE => $relationType,
            Action::FIELD_RELATION_TASK_ID  => $taskId,
            Action::FIELD_CATEGORY_ID       => $categoryId,
            Action::FIELD_DISPLAY_DATE      => strlen($displayDate) > 0 ? Carbon::createFromDate($displayDate) : null,
            Action::FIELD_TAG_BY_EMAIL      => $tagByEmail
        ]);

        return $action;
    }

    /**
     * @param int $userId
     * @param string $subject
     * @param string $message
     * @param int|null $actionId
     * @param int|null $forId
     * @param string|null $relationType
     * @param int|null $taskId
     * @param int|null $categoryId
     * @param string|null $displayDate
     * @param bool|null $tagByEmail
     * @return Action
     */
    public function updateOrCreate(
        int $userId,
        string $subject,
        string $message,
        ?int $actionId = null,
        ?int $forId = null,
        ?string $relationType = null,
        ?int $taskId = null,
        ?int $categoryId = null,
        ?string $displayDate = null,
        ?bool $tagByEmail = null
    ): Action {
        /** @var Action $action */
        $action = Action::query()->updateOrCreate(
            [
                Action::FIELD_ID => $actionId
            ],
            [
                Action::FIELD_FROM_USER_ID => $userId,
                Action::FIELD_SUBJECT => $subject,
                Action::FIELD_MESSAGE => $message,
                Action::FIELD_FOR_ID => $forId,
                Action::FIELD_FOR_RELATION_TYPE => $relationType,
                Action::FIELD_RELATION_TASK_ID => $taskId,
                Action::FIELD_CATEGORY_ID => $categoryId,
                Action::FIELD_DISPLAY_DATE => strlen($displayDate) > 0 ? Carbon::createFromDate($displayDate) : null,
                Action::FIELD_TAG_BY_EMAIL => $tagByEmail
            ]
        );

        return $action;
    }

    /**
     * @param int $actionId
     * @return bool
     */
    public function toggleActionPin(int $actionId): bool
    {
        $targetAction = Action::query()->findOrFail($actionId);
        return $targetAction?->update([Action::FIELD_PINNED => !$targetAction->{Action::FIELD_PINNED}]) ?? false;
    }

    /**
     * @param int $companyId
     *
     * @return Action|null
     */
    public function getLastActionForCompany(int $companyId): ?Action
    {
        /** @var Action|null $action */
        $action = Action::query()
            ->where(Action::FIELD_FOR_ID, $companyId)
            ->where(Action::FIELD_FOR_RELATION_TYPE, Action::RELATION_TYPE_COMPANY)
            ->latest()
            ->first();

        return $action;
    }

    /**
     * @param Action $action
     * @param array $userIds
     */
    public function editActionTags(Action $action, array $userIds): void
    {
        /** @var Collection $actionTags */
        $actionTags = $action->{Action::RELATION_TAGS}()->get();
        $newTags = [];

        foreach ($userIds as $userId) {
            /** @var ActionTag|null $existingTag */
            $existingTag = $actionTags->first(function($tag) use ($userId) {
                return $tag->{ActionTag::FIELD_USER_ID} === $userId;
            });

            if (!$existingTag) {
                $tag = $this->createTag($action->{Action::FIELD_ID}, $userId);
                $newTags[] = $tag->toArray();
            }
        }
        if (!empty($newTags)) {
            $this->actionService->tagUsers($action, array_column($newTags, ActionTag::FIELD_USER_ID));
        }

        foreach ($actionTags as $tag) {
            if (!in_array($tag->{ActionTag::FIELD_USER_ID}, $userIds)) {
                $this->removeTag($tag);
            }
        }
    }

    /**
     * @param int $actionId
     * @param int $userId
     * @return ActionTag
     */
    public function createTag(int $actionId, int $userId): ActionTag
    {
        /** @var ActionTag $tag */
        $tag = ActionTag::query()->create([
            ActionTag::FIELD_USER_ID      => $userId,
            ActionTag::FIELD_ACTION_ID    => $actionId,
        ]);

        return $tag;
    }

    /**
     * @param ActionTag $tag
     * @return bool
     */
    public function removeTag(ActionTag $tag): bool
    {
        return $tag->delete();
    }
}
