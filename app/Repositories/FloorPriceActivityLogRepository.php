<?php

namespace App\Repositories;

use App\Models\FloorPriceActivityLog;
use App\Models\Odin\ProductCountyFloorPrice;
use App\Models\Odin\ProductStateFloorPrice;
use Illuminate\Database\Eloquent\Builder;

class FloorPriceActivityLogRepository
{
    /**
     * Return the logs of price changes within states or counties
     * @param int|null $stateLocationId
     * @param int|null $serviceProductId
     * @param int|null $saleTypeId
     * @param int|null $qualityTierId
     * @param int|null $propertyTypeId
     * @param int|null $productId
     * @return Builder
     */
    public function getFloorPriceChangeLogsQuery(
        ?int $stateLocationId = null,
        ?int $serviceProductId = null,
        ?int $saleTypeId = null,
        ?int $qualityTierId = null,
        ?int $propertyTypeId = null,
        ?int $productId = null,
    ): Builder
    {
        return FloorPriceActivityLog::query()
            ->where(FloorPriceActivityLog::FIELD_LOG_NAME, isset($stateLocationId) ? 'county_floor_price' : 'state_floor_price')
            ->where(FloorPriceActivityLog::FIELD_EVENT, 'updated')
            ->whereHasMorph(FloorPriceActivityLog::RELATION_SUBJECT, [ProductCountyFloorPrice::class, ProductStateFloorPrice::class], function ($query, $type) use (
                $stateLocationId,
                $serviceProductId,
                $saleTypeId,
                $qualityTierId,
                $propertyTypeId,
                $productId,
            ) {
                if (in_array($type, [ProductCountyFloorPrice::class, ProductStateFloorPrice::class], true)) {
                    $query
                        ->when($stateLocationId, function ($query) use ($stateLocationId) {
                            $query->where(ProductStateFloorPrice::FIELD_STATE_LOCATION_ID, $stateLocationId);
                        })
                        ->when($serviceProductId, function ($query) use ($serviceProductId) {
                            $query->where(ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID, $serviceProductId);
                        })
                        ->when($qualityTierId, function ($query) use ($qualityTierId) {
                            $query->where(ProductStateFloorPrice::FIELD_QUALITY_TIER_ID, $qualityTierId);
                        })
                        ->when($propertyTypeId, function ($query) use ($propertyTypeId) {
                            $query->where(ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID, $propertyTypeId);;
                        });
                }
            });
    }
}
