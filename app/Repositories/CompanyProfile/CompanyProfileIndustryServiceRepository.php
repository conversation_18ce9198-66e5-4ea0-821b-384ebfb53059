<?php

namespace App\Repositories\CompanyProfile;

use App\Models\CompanyProfile\CompanyProfile;
use App\Models\CompanyProfile\CompanyProfileIndustryService;
use App\Models\Odin\IndustryService;

class CompanyProfileIndustryServiceRepository
{
    /**
     * @param CompanyProfile $profile
     * @param IndustryService $industryService
     * @return CompanyProfileIndustryService
     */
    public function firstOrCreate(
        CompanyProfile  $profile,
        IndustryService $industryService,
    ): CompanyProfileIndustryService
    {
        return CompanyProfileIndustryService::query()->firstOrCreate([
            CompanyProfileIndustryService::FIELD_COMPANY_PROFILE_ID  => $profile->id,
            CompanyProfileIndustryService::FIELD_INDUSTRY_SERVICE_ID => $industryService->id,
        ]);
    }

}
