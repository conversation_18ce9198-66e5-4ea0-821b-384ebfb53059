<?php

namespace App\Repositories\CompanyProfile;

use App\Models\CompanyProfile\CompanyProfile;
use App\Models\CompanyProfile\CompanyProfileLocation;
use App\Models\Legacy\Location;

class CompanyProfileLocationRepository
{
    /**
     * @param CompanyProfile $profile
     * @param Location $location
     * @param string $raw
     * @return CompanyProfileLocation
     */
    public function firstOrCreate(
        CompanyProfile $profile,
        Location       $location,
        string         $raw,
    ): CompanyProfileLocation
    {
        return CompanyProfileLocation::query()->firstOrCreate([
            CompanyProfileLocation::FIELD_COMPANY_PROFILE_ID => $profile->id,
            CompanyProfileLocation::FIELD_LOCATION_ID        => $location->id,
            CompanyProfileLocation::FIELD_RAW                => $raw,
        ]);
    }

}
