<?php

namespace App\Repositories\CompanyProfile;

use App\DTO\CompanyProfile\CompanyProfileReviewDTO;
use App\Models\CompanyProfile\CompanyProfile;
use App\Models\CompanyProfile\CompanyProfileReview;

class CompanyProfileReviewRepository
{
    /**
     * @param CompanyProfile $profile
     * @param CompanyProfileReviewDTO $companyProfileReviewDTO
     * @return CompanyProfileReview
     */
    public function create(
        CompanyProfile          $profile,
        CompanyProfileReviewDTO $companyProfileReviewDTO
    ): CompanyProfileReview
    {
        return CompanyProfileReview::query()->create([
            CompanyProfileReview::FIELD_TEXT               => $companyProfileReviewDTO->getText(),
            CompanyProfileReview::FIELD_RATING             => $companyProfileReviewDTO->getRating(),
            CompanyProfileReview::FIELD_DATE               => $companyProfileReviewDTO->getDate(),
            CompanyProfileReview::FIELD_AUTHOR             => $companyProfileReviewDTO->getAuthor(),
            CompanyProfileReview::FIELD_HELPFUL            => $companyProfileReviewDTO->getHelpful(),
            CompanyProfileReview::FIELD_ADDITIONAL_RATINGS => $companyProfileReviewDTO->getAdditionalRatings(),
            CompanyProfileReview::FIELD_SOURCE             => $companyProfileReviewDTO->getSource(),
            CompanyProfileReview::FIELD_COMPANY_PROFILE_ID => $profile->id,
        ]);
    }

}
