<?php

namespace App\Repositories\CompanyProfile;

use App\DTO\CompanyProfile\CompanyProfileDTO;
use App\Models\CompanyProfile\CompanyProfile;

class CompanyProfileRepository
{
    /**
     * @param string|null $profileSlug
     * @param array $domains
     * @param bool|null $published
     * @return CompanyProfile|null
     */
    public function find(
        ?string $profileSlug = null,
        array   $domains = [],
        ?bool   $published = null,
    ): ?CompanyProfile
    {
        $query = CompanyProfile::query();

        $query->when($profileSlug, fn ($query) => $query->where(CompanyProfile::FIELD_PROFILE_SLUG, $profileSlug));

        $query->when($published !== null, fn($query) => $query->where(CompanyProfile::FIELD_PUBLISHED, $published));

        $query->when(filled($domains),function ($query) use ($domains) {
            $query->where(function ($query) use ($domains) {
                foreach ($domains as $domain) {
                    $query = $query->orWhere(CompanyProfile::FIELD_WEBSITES, 'like', '%' . $domain . '%');
                }
            });
        });

        return $query->first();
    }

    /**
     * @param CompanyProfileDTO $profileDTO
     * @param CompanyProfile|null $companyProfile
     * @param string $slug
     * @return CompanyProfile
     */
    public function update(
        CompanyProfileDTO $profileDTO,
        string $slug,
        ?CompanyProfile $companyProfile = null,
    ): CompanyProfile
    {
        return CompanyProfile::query()->updateOrCreate([
            CompanyProfile::FIELD_ID => $companyProfile?->id,
            CompanyProfile::FIELD_PROFILE_SLUG => $slug,
        ], $profileDTO->toCompanyProfileFields());
    }
}
