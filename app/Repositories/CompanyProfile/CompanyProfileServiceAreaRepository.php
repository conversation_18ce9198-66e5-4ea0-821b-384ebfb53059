<?php

namespace App\Repositories\CompanyProfile;

use App\Models\CompanyProfile\CompanyProfile;
use App\Models\CompanyProfile\CompanyProfileIndustryService;
use App\Models\CompanyProfile\CompanyProfileLocation;
use App\Models\CompanyProfile\CompanyProfileServiceArea;

class CompanyProfileServiceAreaRepository
{
    /**
     * @param CompanyProfile $profile
     * @param CompanyProfileLocation $profileLocation
     * @param CompanyProfileIndustryService $profileIndustryService
     * @return CompanyProfileServiceArea
     */
    public function firstOrCreate(
        CompanyProfile                $profile,
        CompanyProfileLocation        $profileLocation,
        CompanyProfileIndustryService $profileIndustryService,
    ): CompanyProfileServiceArea
    {
        return CompanyProfileServiceArea::query()->firstOrCreate([
            CompanyProfileServiceArea::FIELD_COMPANY_PROFILE_ID                  => $profile->id,
            CompanyProfileServiceArea::FIELD_COMPANY_PROFILE_LOCATION_ID         => $profileLocation->id,
            CompanyProfileServiceArea::FIELD_COMPANY_PROFILE_INDUSTRY_SERVICE_ID => $profileIndustryService->id,
        ]);
    }
}
