<?php

namespace App\Repositories\BundleManagement;

use App\Models\BundleInvoiceHistory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class BundleInvoiceHistoryRepository
{
    /**
     * @return Collection
     */
    public function all(): Collection
    {
        return BundleInvoiceHistory::all();
    }


    /**
     * @param $bundleInvoiceId
     * @param string $description
     * @param string|null $note
     * @return BundleInvoiceHistory
     */
    public function createInvoiceHistory($bundleInvoiceId, string $description, ?string $note = null): BundleInvoiceHistory
    {
        /** @var BundleInvoiceHistory $historyRecord */
        $historyRecord = BundleInvoiceHistory::query()->create([
            BundleInvoiceHistory::FIELD_BUNDLE_INVOICE_ID => $bundleInvoiceId,
            BundleInvoiceHistory::FIELD_DESCRIPTION => $description,
            BundleInvoiceHistory::FIELD_NOTE => $note
        ]);

        return $historyRecord;
    }
}
