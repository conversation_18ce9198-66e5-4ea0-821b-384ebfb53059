<?php

namespace App\Repositories\BundleManagement;

use App\Builders\BundleInvoiceBuilder;
use App\Enums\BundleInvoiceStatus;
use App\Models\Bundle;
use App\Models\BundleInvoice;
use App\Models\User;
use App\Services\BundleManagement\BundleInvoiceService;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Spatie\FlareClient\Http\Exceptions\BadResponse;

class BundleInvoiceRepository
{
    public function __construct(protected BundleInvoiceService $bundleService)
    {
    }

    /**
     * @return Collection
     */
    public function all(): Collection
    {
        return BundleInvoice::all();
    }

    /**
     * @param int $bundleId
     * @param int $companyId
     * @param string $billingVersion
     * @param array $payload
     * @return BundleInvoice
     */
    public function createNewInvoice(
        int $bundleId,
        int $companyId,
        string $billingVersion = 'v1',
        array $payload = []
    ): BundleInvoice
    {
        /** @var BundleInvoice $invoice */
        $invoice = BundleInvoice::query()->create([
            BundleInvoice::FIELD_BUNDLE_ID       => $bundleId,
            BundleInvoice::FIELD_COMPANY_ID      => $companyId,
            BundleInvoice::FIELD_STATUS          => BundleInvoiceStatus::NEW,
            BundleInvoice::FIELD_BILLING_VERSION => $billingVersion,
            BundleInvoice::FIELD_PAYLOAD         => $payload
        ]);

        return $invoice;
    }

    /**
     * @param BundleInvoice $invoice
     * @return bool|null
     */
    public function deleteInvoice(BundleInvoice $invoice): ?bool
    {
        return $invoice->delete();
    }

    /**
     * @param string|null $bundleName
     * @param float|null $costFrom
     * @param float|null $costTo
     * @param float|null $creditFrom
     * @param float|null $creditTo
     * @param mixed|null $status
     * @param mixed|null $issued_at
     * @param mixed|null $companyName
     * @param string|null $sortCol
     * @param string|null $sortDir
     * @param bool|null $showAutoApprovedOnly
     * @return Builder
     */
    public function getInvoicesWithSearchFilters(
        ?string $bundleName = null,
        ?float $costFrom = null,
        ?float $costTo = null,
        ?float $creditFrom = null,
        ?float $creditTo = null,
        mixed $status = null,
        ?array $issued_at = null,
        mixed $companyName = null,
        ?string $sortCol = null,
        ?string $sortDir = null,
        ?bool $showAutoApprovedOnly = null,
    ): Builder
    {
        return BundleInvoiceBuilder::query()
            ->forBundleName($bundleName)
            ->forCostFrom($costFrom)
            ->forCostTo($costTo)
            ->forCreditFrom($creditFrom)
            ->forCreditTo($creditTo)
            ->forStatus($status)
            ->forIssuedAt($issued_at)
            ->forCompanyName($companyName)
            ->forAutoApprovedOnly($showAutoApprovedOnly)
            ->sortDirection($sortDir)
            ->sortColumn($sortCol)
            ->getQuery();
    }


    /**
     * @param BundleInvoice $bundleInvoice
     * @param string $transition
     * @param User|null $user
     * @param array $options
     * @return bool
     * @throws Exception
     */
    public function transitionInvoiceStatus(
        BundleInvoice $bundleInvoice,
        string $transition,
        ?User $user = null,
        array $options = []
    ): bool
    {
        switch ($transition) {
            case BundleInvoice::TRANSITION_ISSUE:
                return $this->handleIssueTransition($bundleInvoice, $user);
            case BundleInvoice::TRANSITION_PAID:
                $this->handlePaidTransition($bundleInvoice);
                break;
            case BundleInvoice::TRANSITION_APPROVE:
                $this->handleApproveTransition($bundleInvoice, $user);
                break;
            case BundleInvoice::TRANSITION_DENY:
                $this->handleDenyTransition($bundleInvoice, $user);
                break;
            case BundleInvoice::TRANSITION_CANCEL:
                $this->handleCancelTransition($bundleInvoice, $user);
                break;
            case BundleInvoice::TRANSITION_FAIL:
                $this->handleFailedTransition($bundleInvoice, $user, $options);
                break;
            default:
                return false;
        }
        return true;
    }

    /**
     * @param BundleInvoice $bundleInvoice
     * @param string $status
     *
     * @return bool
     */
    public function canTransitionInvoiceStatus(BundleInvoice $bundleInvoice, string $status): bool
    {
        return match ($status) {
            BundleInvoice::TRANSITION_PAID => !in_array($bundleInvoice->status, [
                BundleInvoiceStatus::PAID,
                BundleInvoiceStatus::COMPLETE,
                BundleInvoiceStatus::CANCELLED
            ]),
            default                        => true,
        };
    }

    /**
     * @param BundleInvoice $bundleInvoice
     * @param User|null $user
     * @return bool
     * @throws Exception
     */
    protected function handleIssueTransition(BundleInvoice $bundleInvoice, ?User $user = null): bool
    {
        if ($this->bundleService->createPayableLegacyInvoice($bundleInvoice, $user)) {
            $bundleInvoice->update([
                BundleInvoice::FIELD_ISSUED_BY => $user->{User::FIELD_ID},
                BundleInvoice::FIELD_ISSUED_AT => Carbon::now(),
                BundleInvoice::FIELD_STATUS    => BundleInvoiceStatus::ISSUED,
            ]);

            return true;
        }

        return false;
    }

    /**
     * @param BundleInvoice $bundleInvoice
     * @param User|null $user
     * @return void
     */
    protected function handleCancelTransition(BundleInvoice $bundleInvoice, ?User $user = null): void
    {
        // TODO: Check if there's any invoices in legacy that require cancellation belonging to '$event->bundleInvoice->id'
        $bundleInvoice->update([
            BundleInvoice::FIELD_CANCELLED_BY => $user?->id,
            BundleInvoice::FIELD_CANCELLED_AT => Carbon::now(),
            BundleInvoice::FIELD_STATUS       => BundleInvoiceStatus::CANCELLED
        ]);
    }


    /**
     * @param BundleInvoice $bundleInvoice
     * @param User|null $user
     * @param array $options
     * @return void
     */
    protected function handleFailedTransition(BundleInvoice $bundleInvoice, ?User $user = null, array $options = []): void
    {
        // TODO: Check if there's any invoices in legacy that require cancellation belonging to '$event->bundleInvoice->id'
        $now = Carbon::now();
        $bundleInvoice->update([
            BundleInvoice::FIELD_FAILED_AT    => $now,
            BundleInvoice::FIELD_FAIL_REASON  => $options[BundleInvoice::FIELD_FAIL_REASON] ?? '',
            BundleInvoice::FIELD_CANCELLED_BY => $user?->id,
            BundleInvoice::FIELD_CANCELLED_AT => $now,
            BundleInvoice::FIELD_STATUS       => BundleInvoiceStatus::CANCELLED
        ]);
    }

    /**
     * @param BundleInvoice $bundleInvoice
     * @return void
     */
    protected function handlePaidTransition(BundleInvoice $bundleInvoice): void
    {
        $bundleInvoice->update([
            BundleInvoice::FIELD_PAID_AT => Carbon::now(),
            BundleInvoice::FIELD_STATUS  => BundleInvoiceStatus::PAID
        ]);
    }

    /**
     * @param BundleInvoice $bundleInvoice
     * @param User|null $user
     * @return bool
     * @throws BadResponse
     */
    protected function handleApproveTransition(BundleInvoice $bundleInvoice, ?User $user = null): bool
    {
        if ($this->bundleService->applyLeadCredit($bundleInvoice)) {
            $bundleInvoice->update([
                BundleInvoice::FIELD_APPROVED_BY => $user?->id,
                BundleInvoice::FIELD_APPROVED_AT => Carbon::now(),
                BundleInvoice::FIELD_STATUS      => BundleInvoiceStatus::COMPLETE
            ]);

            return true;
        }

        return false;
    }

    /**
     * @param BundleInvoice $bundleInvoice
     * @param User|null $user
     * @return void
     */
    protected function handleDenyTransition(BundleInvoice $bundleInvoice, ?User $user = null): void
    {
        // TODO: Cancel the invoice in legacy belonging to '$event->bundleInvoice->id'
        $bundleInvoice->update([
            BundleInvoice::FIELD_DENIED_BY => $user?->id,
            BundleInvoice::FIELD_DENIED_AT => Carbon::now(),
            BundleInvoice::FIELD_STATUS    => BundleInvoiceStatus::COMPLETE
        ]);
    }

    public function getSoldActiveBundlesCount(array $invoiceIds): Collection
    {
        return BundleInvoice::query()->select(DB::raw('count(*) as bundle_count, bundle_id'))
            ->whereIn(BundleInvoice::FIELD_PAYABLE_INVOICE_ID, $invoiceIds)
            ->whereHas(BundleInvoice::RELATION_BUNDLE, function (Builder $builder) {
                $builder->whereNotNull(Bundle::TABLE . '.' . Bundle::FIELD_ACTIVATED_AT);
            })
            ->groupBy(BundleInvoice::FIELD_BUNDLE_ID)
            ->get();
    }

}
