<?php

namespace App\Repositories;

use App\DataModels\Campaigns\AvailableBudgetDataModel as FutureCampaignAvailableBudgetDataModel;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Jobs\RecordMonitoringLog;
use App\Models\AvailableBudget;
use App\Models\AvailableCampaignByLocation;
use App\Models\AvailableCompanyByLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\Industry;
use App\Repositories\Odin\IndustryRepository;
use App\Services\DatabaseHelperService;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Collection as BaseCollection;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class AvailableBudgetRepository
{
    /**
     * @param IndustryRepository $industryRepository
     */
    public function __construct(protected IndustryRepository $industryRepository)
    {
    }

    /**
     * @param string $industry
     * @return void
     * @throws Exception
     */
    private function delete(string $industry): void
    {
        AvailableBudget::query()
            ->where(AvailableBudget::FIELD_INDUSTRY_TYPE, $industry)
            ->delete();

        // todo: Need to update AvailableBudget to use 'roofing' instead of 'roofer'
        if($industry === AvailableBudget::INDUSTRY_TYPE_ROOFING) {
            $industry = IndustryEnum::ROOFING->getSlug();
        }

        AvailableCompanyByLocation::query()
            ->where(AvailableCompanyByLocation::FIELD_INDUSTRY_SLUG, $industry)
            ->delete();

        $industryId = IndustryEnum::fromSlug($industry)->model()->{Industry::FIELD_ID};

        // TODO: Re-enable when using new ERPL calculation
//        AvailableCampaignByLocation::query()
//            ->where(AvailableCampaignByLocation::FIELD_INDUSTRY_ID, $industryId)
//            ->delete();
    }

    /**
     * Process and insert budget data for future campaigns
     *
     * @param BaseCollection<FutureCampaignAvailableBudgetDataModel> $budgetData
     * @param string $industry
     *
     * @return void
     * @throws Exception
     */
    public function processAndInsertBudgetData(BaseCollection $budgetData, string $industry): void
    {
        $this->delete($this->getIndustryKey($industry));
        $time = Carbon::now();

        foreach ($budgetData->chunk(500) as $data) {
            $this->writeDataToAvailableBudgetsForFutureCampaigns($data, $time);
            $this->writeDataToAvailableCompanyByLocationsForFutureCampaign($data, $time);
//            $this->writeDataToAvailableCampaignByLocationsForFutureCampaign($data, $time); // TODO: Re-enable when using new ERPL calculation
        }
    }

    /**
     * @param BaseCollection<FutureCampaignAvailableBudgetDataModel> $data
     * @param Carbon $time
     *
     * @return void
     */
    protected function writeDataToAvailableBudgetsForFutureCampaigns(BaseCollection $data, Carbon $time): void
    {
        $columns = collect([
            AvailableBudget::FIELD_LOCATION_ID,
            AvailableBudget::FIELD_COUNTY_LOCATION_ID,
            AvailableBudget::FIELD_INDUSTRY_TYPE,
            AvailableBudget::FIELD_INDUSTRY_ID,
            AvailableBudget::FIELD_BUDGET_TYPE,
            AvailableBudget::FIELD_AVAILABLE_CAMPAIGN_COUNT,
            AvailableBudget::FIELD_UNLIMITED_BUDGET_COUNT,
            AvailableBudget::FIELD_BUDGET_AVAILABLE_DOLLARS,
            AvailableBudget::FIELD_BUDGET_AVAILABLE_VOLUME,
            AvailableBudget::FIELD_POTENTIAL_QUEUED_REVENUE,
            AvailableBudget::FIELD_CREATED_AT,
            AvailableBudget::FIELD_UPDATED_AT
        ])->join(', ');

        $values = $data->filter(function(FutureCampaignAvailableBudgetDataModel $model){
            return $model->availableCampaignCount > 0;
        });

        $values = $values->map(
            fn(FutureCampaignAvailableBudgetDataModel $model) =>
                "($model->locationId, $model->countyLocationId, '{$this->getIndustryKey($model->industryType)}', $model->industryId, $model->budgetType, $model->availableCampaignCount, $model->unlimitedBudgetCount, $model->availableBudgetDollars, $model->availableBudgetVolume, $model->potentialQueuedRevenue, '$time', '$time')"
        )->join(', ');

        if(!empty($values)) {
            $query = "INSERT INTO " . DatabaseHelperService::database() . '.' . AvailableBudget::TABLE . " ($columns) VALUES " . $values;

            DB::insert($query);
        }
    }

    /**
     * @param BaseCollection<FutureCampaignAvailableBudgetDataModel> $data
     * @param Carbon $time
     *
     * @return void
     */
    protected function writeDataToAvailableCompanyByLocationsForFutureCampaign(BaseCollection $data, Carbon $time): void
    {
        $columns = collect([
            AvailableCompanyByLocation::FIELD_COMPANY_ID,
            AvailableCompanyByLocation::FIELD_LOCATION_ID,
            AvailableCompanyByLocation::FIELD_COUNTY_LOCATION_ID,
            AvailableCompanyByLocation::FIELD_INDUSTRY_SLUG,
            AvailableCompanyByLocation::FIELD_LEGACY_COMPANY_ID,
            AvailableCompanyByLocation::FIELD_UNLIMITED_BUDGETS,
            AvailableCompanyByLocation::CREATED_AT,
            AvailableCompanyByLocation::UPDATED_AT
        ])->join(',');

        $values = $data->map(function (FutureCampaignAvailableBudgetDataModel $model) use ($time) {
            $collection = collect();

            foreach ($model->companyIds as $companyId) {
                $ids      = explode(':', $companyId);
                $id       = $ids[0] ?? null;
                $legacyId = $ids[1] ?? null;

                if (!$id || !$legacyId) {
                    continue;
                }

                $collection->push("($id, $model->locationId, $model->countyLocationId, '$model->industryType', $legacyId, $model->unlimitedBudgetCount, '$time', '$time')");
            }

            return $collection->unique()->join(', ');
        })->join(', ');

        if(!empty($values)) {
            $query = "INSERT INTO " . DatabaseHelperService::database() . '.' . AvailableCompanyByLocation::TABLE . " ($columns) VALUES " . $values;

            DB::insert($query);
        }
    }

    /**
     * @param BaseCollection<FutureCampaignAvailableBudgetDataModel> $data
     * @param Carbon $time
     *
     * @return void
     */
    protected function writeDataToAvailableCampaignByLocationsForFutureCampaign(BaseCollection $data, Carbon $time): void
    {
        $columns = collect([
            AvailableCampaignByLocation::FIELD_CAMPAIGN_ID,
            AvailableCampaignByLocation::FIELD_COMPANY_ID,
            AvailableCampaignByLocation::FIELD_LOCATION_ID,
            AvailableCampaignByLocation::FIELD_COUNTY_LOCATION_ID,
            AvailableCampaignByLocation::FIELD_INDUSTRY_ID,
            AvailableCampaignByLocation::CREATED_AT,
            AvailableCampaignByLocation::UPDATED_AT
        ])->join(',');

        $values = $data->map(function (FutureCampaignAvailableBudgetDataModel $model) use ($time) {
            $collection = collect();

            foreach ($model->campaignCompanyIds as $campaignId) {
                $ids        = explode(':', $campaignId);
                $campaignId = $ids[0] ?? null;
                $companyId  = $ids[1] ?? null;

                if (!$campaignId || !$companyId) {
                    continue;
                }

                $collection->push("($campaignId, $companyId, $model->locationId, $model->countyLocationId, '$model->industryId', '$time', '$time')");
            }

            return $collection->unique()->join(', ');
        })->join(', ');

        if(!empty($values)) {
            $query = "INSERT INTO " . DatabaseHelperService::database() . '.' . AvailableCampaignByLocation::TABLE . " ($columns) VALUES " . $values;

            DB::insert($query);
        }
    }

    /**
     * @param string $industry
     * @return Collection
     */
    public function getAvailableCampaignsWithVerifiedBudgetsForIndustry(string $industry): Collection
    {
        if($industry === "roofing")
            $industry = "roofer";

        $query = Location::query()
            ->select([
               DatabaseHelperService::database().'.'.AvailableBudget::TABLE.'.'.AvailableBudget::FIELD_AVAILABLE_CAMPAIGN_COUNT,
               Location::TABLE.'.'.Location::COUNTY_KEY.' as county_key',
               Location::TABLE.'.'.Location::STATE_ABBREVIATION.' as state_abbr',
               Location::TABLE.'.'.Location::ID.' as location_id',
           ])->leftJoin(DatabaseHelperService::database().'.'.AvailableBudget::TABLE, function ($leftJoin) use ($industry) {
                $leftJoin->on(
                    Location::TABLE.'.'.Location::ID,
                    '=',
                    DatabaseHelperService::database().'.'.AvailableBudget::TABLE.'.'.AvailableBudget::FIELD_LOCATION_ID
                )
                //->where(AvailableBudget::FIELD_BUDGET_TYPE, AvailableBudget::BUDGET_TYPE_VERIFIED); // todo: We're only have verified. Omit from query for now to speed up
                ->where(AvailableBudget::FIELD_INDUSTRY_TYPE, $industry);
            });

        return $query->where(Location::TABLE.'.'.Location::TYPE, Location::TYPE_ZIP_CODE)
            ->get();
    }

    /**
     * @param string $industry
     *
     * @return string
     */
    protected function getIndustryKey(string $industry): string
    {
        return match ($industry) {
            'roofing' => 'roofer',
            default => $industry
        };
    }
}
