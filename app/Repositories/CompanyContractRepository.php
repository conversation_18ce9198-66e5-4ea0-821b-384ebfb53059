<?php

namespace App\Repositories;

use App\Enums\ContractKeys;
use App\Enums\ContractProvider;
use App\Enums\ContractType;
use App\Enums\Odin\OriginDomain;
use App\Models\CompanyContract;
use App\Models\Contract;
use App\Models\ContractKey;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Website;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;

class CompanyContractRepository
{
    /**
     * @param array $data
     * @return CompanyContract
     */
    public function createCompanyContractFromAttributes(array $data = []): CompanyContract
    {
        $newContract = new CompanyContract();
        $newContract->fill($data);
        $newContract->save();

        return $newContract;
    }

    /**
     * @param string $uuid
     * @return CompanyContract
     */
    public function findCompanyContractByUuidOrFail(string $uuid): CompanyContract
    {
        /** @var CompanyContract $contract */
        $contract = CompanyContract::query()
            ->where(CompanyContract::FIELD_UUID, $uuid)
            ->firstOrFail();

        return $contract;
    }

    /**
     * @param CompanyUser $user
     * @param ContractKey $contractKey
     * @param Website $website
     * @return CompanyContract|null
     */
    public function getValidCompanyUserContractByType(CompanyUser $user, ContractKey $contractKey, Website $website): ?CompanyContract
    {
        /** @var ?CompanyContract */
        return CompanyContract::query()
            ->where(CompanyContract::FIELD_COMPANY_USER_ID, $user->id)
            ->whereHas(CompanyContract::RELATION_CONTRACT, function (Builder $builder) use ($contractKey, $website) {
//                todo add contract active to avoid outdated contracts - for now omit as to not incite resign for all users;
                $builder->where(function (Builder $query) use ($contractKey, $website) {
                    $query->where(Contract::FIELD_WEBSITE_ID, $website->id)
                        ->where(Contract::FIELD_CONTRACT_KEY_ID, $contractKey->id);
                })->orWhere(function (Builder $query) {
                    //this contract is reserved for custom contract sending / uploads
                    $query->where(Contract::FIELD_WEBSITE_ID, Website::query()->where(Website::FIELD_ABBREVIATION, OriginDomain::SOLAR_REVIEWS->getAbbreviation())->firstOrFail()?->id)
                        ->where(Contract::FIELD_CONTRACT_KEY_ID, ContractKey::query()->where(ContractKey::FIELD_KEY, ContractKeys::LEAD_BUYING_AGREEMENT)->firstOrFail()?->id)
                        ->where(Contract::FIELD_ACTIVE, true);
                });
            })
            ->where(CompanyContract::FIELD_AGREED_AT, '<=', now())
            ->first();
    }

    /**
     * @param string $signatureId
     * @return CompanyContract|null
     */
    public function getCompanyContractBySignatureIdOrFail(string $signatureId): ?CompanyContract
    {
        /** @var CompanyContract $companyContract */
        $companyContract = CompanyContract::query()->where(CompanyContract::FIELD_SIGNATURE_ID, $signatureId)->firstOrFail();

        return $companyContract;
    }

    /**
     * @param int $companyId
     * @return Builder
     */
    public function getCompanyContractsQuery(
        int $companyId
    ): Builder
    {
        return CompanyContract::query()->where(CompanyContract::FIELD_COMPANY_ID, $companyId);
    }

    public function getMostRecentContract(CompanyUser $user, ContractProvider $contractProvider = ContractProvider::DOCUSIGN): CompanyContract|null
    {
        /** @var Website $website */
        $website = Website::query()->where(Website::FIELD_ABBREVIATION, ContractType::FIXR)->firstOrFail();

        /** @var ContractKey $contractKey */
        $contractKey = ContractKey::query()->where(ContractKey::FIELD_KEY, ContractKeys::TERMS_AND_CONDITIONS)->firstOrFail();

        return $user->contracts()
            ->whereHas(CompanyContract::RELATION_CONTRACT, function (Builder $builder) use ($contractKey, $website, $contractProvider) {
                $builder->where(Contract::FIELD_CONTRACT_KEY_ID, $contractKey->id)
                    ->where(Contract::FIELD_WEBSITE_ID, $website->id);
            })
            ->orderBy(CompanyContract::CREATED_AT, 'desc')
            ->first();
    }
}
