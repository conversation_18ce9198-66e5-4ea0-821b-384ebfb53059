<?php

namespace App\Repositories\OutreachCadence;

use App\Events\OutreachCadence\CompanyRoutineDeletedEvent;
use App\Models\AccountManager;
use App\Models\Cadence\BaseModel;
use App\Models\Cadence\CadenceRoutine;
use App\Models\Cadence\CadenceScheduledGroup;
use App\Models\Cadence\CadenceScheduledGroupAction;
use App\Models\Cadence\CompanyCadenceRoutine;
use App\Models\Cadence\CompanyCadenceScheduledGroup;
use App\Models\Cadence\CompanyCadenceScheduledGroupAction;
use App\Models\Odin\Company;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class CompanyCadenceRoutineRepository
{

    public function __construct(protected CadenceRoutineConfigurationRepository $configurationRepository) {}

    /**
     * @param array $companyIds
     * @return int
     */
    public function terminateOngoingCompanyRoutinesByCompanyIds(array $companyIds): int
    {
        $query         = CompanyCadenceRoutine::query()
                                              ->whereIn(CompanyCadenceRoutine::FIELD_COMPANY_ID, $companyIds)
                                              ->where(CompanyCadenceRoutine::FIELD_STATUS, CompanyCadenceRoutine::STATUS_PENDING);
        $affectedCount = $query->count();
        foreach ($query->pluck(BaseModel::FIELD_ID) as $routineId) {
            $this->deleteRoutineById($routineId);
        }
        return $affectedCount;
    }

    /**
     * @param array $companyIds
     * @return Collection
     */
    public function companiesWithOngoingRoutines(array $companyIds): Collection
    {
        $ongoingCompanyIds = CompanyCadenceRoutine::query()
                                                  ->whereIn(CompanyCadenceRoutine::FIELD_COMPANY_ID, $companyIds)
                                                  ->where(CompanyCadenceRoutine::FIELD_STATUS, CompanyCadenceRoutine::STATUS_PENDING)
                                                  ->get()
                                                  ->pluck(CompanyCadenceRoutine::FIELD_COMPANY_ID)
                                                  ->toArray();
        return Company::query()->whereIn(Company::FIELD_ID, $ongoingCompanyIds)->get();
    }

    /**
     * @param int $companyId
     * @param int $routineConfigId
     * @param bool $useAccountManager
     * @param int $userId
     * @return CompanyCadenceRoutine
     */
    public function createCompanyRoutineFromConfig(int $companyId, int $routineConfigId, bool $useAccountManager, int $userId): CompanyCadenceRoutine
    {
        /** @var CadenceRoutine $routineConfig */
        $routineConfig = $this->configurationRepository->find($routineConfigId);

        /** @var CompanyCadenceRoutine $routine */
        $routine = $routineConfig->companyRoutines()->create([
            CompanyCadenceRoutine::FIELD_COMPANY_ID                   => $companyId,
            CompanyCadenceRoutine::FIELD_CONTACT_DECISION_MAKERS_ONLY => $routineConfig->contact_decision_makers_only,
            CompanyCadenceRoutine::FIELD_CONTACT_ON_WEEKDAYS_ONLY     => $routineConfig->contact_on_weekdays_only,
            CompanyCadenceRoutine::FIELD_DOMAIN                       => $routineConfig->domain,
            CompanyCadenceRoutine::FIELD_USE_ACCOUNT_MANAGER          => $useAccountManager,
            CompanyCadenceRoutine::FIELD_USER_ID                      => $userId,
        ]);

        /** @var CadenceScheduledGroup $groupConfig */
        foreach ($routineConfig->scheduledGroups as $groupConfig) {

            /** @var CompanyCadenceScheduledGroup $group */
            $group = $routine->scheduledGroups()->create([
                CompanyCadenceScheduledGroup::FIELD_EXECUTION_DELAY_MINUTES     => $groupConfig->execution_delay_minutes,
                CompanyCadenceScheduledGroup::FIELD_EXECUTION_TIME_EXACT        => $groupConfig->execution_time_exact,
                CompanyCadenceScheduledGroup::FIELD_EXECUTION_TIME_WINDOW_START => $groupConfig->execution_time_window_start,
                CompanyCadenceScheduledGroup::FIELD_EXECUTION_TIME_WINDOW_END   => $groupConfig->execution_time_window_end,
            ]);

            /** @var CadenceScheduledGroupAction $actionConfig */
            foreach ($groupConfig->actions as $actionConfig) {

                $group->actions()->create([
                    CompanyCadenceScheduledGroupAction::FIELD_ACTION_TYPE       => $actionConfig->action_type,
                    CompanyCadenceScheduledGroupAction::FIELD_TASK_TEMPLATE_ID  => $actionConfig->task_template_id,
                    CompanyCadenceScheduledGroupAction::FIELD_SMS_TEMPLATE_ID   => $actionConfig->sms_template_id,
                    CompanyCadenceScheduledGroupAction::FIELD_EMAIL_TEMPLATE_ID => $actionConfig->email_template_id,
                ]);

            }
        }

        return $routine;
    }

    /**
     * @param int $taskId
     * @return Model|CompanyCadenceScheduledGroupAction|null
     */
    public function findPendingActionByTaskId(int $taskId): null|Model|CompanyCadenceScheduledGroupAction
    {
        return CompanyCadenceScheduledGroupAction::query()
                                                 ->where(CompanyCadenceScheduledGroupAction::FIELD_STATUS, CompanyCadenceScheduledGroupAction::STATUS_PENDING)
                                                 ->where(CompanyCadenceScheduledGroupAction::FIELD_TASK_ID, $taskId)
                                                 ->first();
    }

    /**
     * @param bool $includeConcluded
     * @param array $relationships
     * @return Collection<int, CompanyCadenceRoutine>
     */
    public function getCompanyRoutines(
        bool  $includeConcluded = false,
        array $relationships = [
            CompanyCadenceRoutine::RELATION_SCHEDULED_GROUPS . '.' . CompanyCadenceScheduledGroup::RELATION_ACTIONS . '.' . CompanyCadenceScheduledGroupAction::RELATION_EMAIL_TEMPLATE,
            CompanyCadenceRoutine::RELATION_SCHEDULED_GROUPS . '.' . CompanyCadenceScheduledGroup::RELATION_ACTIONS . '.' . CompanyCadenceScheduledGroupAction::RELATION_SMS_TEMPLATE,
            CompanyCadenceRoutine::RELATION_SCHEDULED_GROUPS . '.' . CompanyCadenceScheduledGroup::RELATION_ACTIONS . '.' . CompanyCadenceScheduledGroupAction::RELATION_TASK_TEMPLATE,
            CompanyCadenceRoutine::RELATION_COMPANY
        ]
    ): Collection
    {
        // todo accept filter params
        $query = CompanyCadenceRoutine::query()->with($relationships)->orderByRaw('FIELD(status, "pending", "concluded")');

        if (!$includeConcluded)
            $query->where(CompanyCadenceRoutine::FIELD_STATUS, CompanyCadenceRoutine::STATUS_PENDING);

        return $query->get()->map(function ($routine) {
            /** @var CompanyCadenceRoutine $routine */
            $routine->accountManager = $routine->company->accountManagerClient()?->accountManager()->with(AccountManager::RELATION_USER)->first();
            foreach ($routine->scheduledGroups as $group) {
                foreach ($group->actions as $action) {
                    /** @var CompanyCadenceScheduledGroupAction $action */
                    $action->template = match ($action->action_type) {
                        CompanyCadenceScheduledGroupAction::ACTION_TYPE_EMAIL => $action->emailTemplate,
                        CompanyCadenceScheduledGroupAction::ACTION_TYPE_SMS => $action->smsTemplate,
                        CompanyCadenceScheduledGroupAction::ACTION_TYPE_TASK => $action->taskTemplate
                    };
                }
            }
            return $routine;
        });
    }

    /**
     * @param int $groupId
     * @return void
     */
    public function skipGroupById(int $groupId): void
    {
        CompanyCadenceScheduledGroup::query()->find($groupId)->update([CompanyCadenceScheduledGroup::FIELD_SKIP => true]);
    }

    /**
     * @param int $groupId
     * @return void
     */
    public function unSkipGroupById(int $groupId): void
    {
        CompanyCadenceScheduledGroup::query()->find($groupId)->update([CompanyCadenceScheduledGroup::FIELD_SKIP => false]);
    }

    /**
     * @param $routineId
     * @return void
     */
    public function deleteRoutineById($routineId): void
    {
        CompanyCadenceRoutine::query()->find($routineId)?->delete();
        CompanyRoutineDeletedEvent::dispatch($routineId);
    }

    /**
     * @param int $companyId
     * @param array $relationships
     * @return Collection<int, CompanyCadenceRoutine>
     */
    public function findRoutinesByCompanyId(
        int   $companyId,
        array $relationships = [
            CompanyCadenceRoutine::RELATION_SCHEDULED_GROUPS . '.' . CompanyCadenceScheduledGroup::RELATION_ACTIONS . '.' . CompanyCadenceScheduledGroupAction::RELATION_EMAIL_TEMPLATE,
            CompanyCadenceRoutine::RELATION_SCHEDULED_GROUPS . '.' . CompanyCadenceScheduledGroup::RELATION_ACTIONS . '.' . CompanyCadenceScheduledGroupAction::RELATION_SMS_TEMPLATE,
            CompanyCadenceRoutine::RELATION_SCHEDULED_GROUPS . '.' . CompanyCadenceScheduledGroup::RELATION_ACTIONS . '.' . CompanyCadenceScheduledGroupAction::RELATION_TASK_TEMPLATE,
            CompanyCadenceRoutine::RELATION_COMPANY
        ]
    ): Collection
    {
        $routines = CompanyCadenceRoutine::query()
                                         ->orderByRaw('FIELD(status, "pending", "concluded"), company_cadence_routines.id desc')
                                         ->where(CompanyCadenceRoutine::FIELD_COMPANY_ID, $companyId)
                                         ->with($relationships)
                                         ->withTrashed()
                                         ->get();
        foreach ($routines as $routine) {
            /** @var CompanyCadenceRoutine $routine */
            $routine->accountManager = $routine->company->accountManagerClient()?->accountManager()->with(AccountManager::RELATION_USER)->first();
            foreach ($routine->scheduledGroups as $group) {
                foreach ($group->actions as $action) {
                    /** @var CompanyCadenceScheduledGroupAction $action */
                    $action->template = match ($action->action_type) {
                        CompanyCadenceScheduledGroupAction::ACTION_TYPE_EMAIL => $action->emailTemplate()->withTrashed()->first(),
                        CompanyCadenceScheduledGroupAction::ACTION_TYPE_SMS => $action->smsTemplate()->withTrashed()->first(),
                        CompanyCadenceScheduledGroupAction::ACTION_TYPE_TASK => $action->taskTemplate()->withTrashed()->first(),
                    };
                }
            }
        }
        return $routines;
    }

    /**
     * @param int $groupId
     * @return void
     */
    public function deleteGroupAndAction(int $groupId): void
    {
        /** @var CompanyCadenceScheduledGroup $group */
        $group = CompanyCadenceScheduledGroup::find($groupId);
        $group->actions()->delete();
        $group->delete();
    }

    /**
     * @param int $companyCadenceRoutineId
     * @return CompanyCadenceRoutine|null
     */
    public function find(int $companyCadenceRoutineId): ?CompanyCadenceRoutine
    {
        return CompanyCadenceRoutine::find($companyCadenceRoutineId);
    }

    /**
     * @param CompanyCadenceRoutine $routine
     * @return CompanyCadenceScheduledGroup|null
     */
    public function getLastFailedGroup(CompanyCadenceRoutine $routine): ?CompanyCadenceScheduledGroup
    {
        $groups = $routine->scheduledGroups
            ->where(CompanyCadenceScheduledGroup::FIELD_STATUS, CompanyCadenceScheduledGroup::STATUS_CONCLUDED)
            ->sortByDesc(Model::UPDATED_AT)
            ->sortByDesc(BaseModel::FIELD_ID);

        /** @var CompanyCadenceScheduledGroup $group */
        foreach ($groups as $group) {
            if (!$group->isSuccessful())
                return $group;
        }
        return null;
    }

    /**
     * @param CompanyCadenceScheduledGroup $group
     * @return void
     */
    public function resetGroup(CompanyCadenceScheduledGroup $group): void
    {
        /** @var CompanyCadenceScheduledGroupAction $action */
        foreach ($group->actions as $action) {
            $action->update([
                CompanyCadenceScheduledGroupAction::FIELD_STATUS           => CompanyCadenceScheduledGroupAction::STATUS_PENDING,
                CompanyCadenceScheduledGroupAction::FIELD_RESOLUTION_NOTES => null,
            ]);
        }
        $group->update([
            CompanyCadenceScheduledGroup::FIELD_STATUS                     => CompanyCadenceScheduledGroup::STATUS_NOT_STARTED,
            CompanyCadenceScheduledGroup::FIELD_RESOLUTION_NOTES           => null,
            CompanyCadenceScheduledGroup::FIELD_TARGET_EXECUTION_TIMESTAMP => null,
            CompanyCadenceScheduledGroup::FIELD_SUCCESS_COUNT              => 0,
        ]);
    }

    /**
     * @param int $companyId
     * @return bool
     */
    public function companyHasOngoingRoutine(int $companyId): bool
    {
        $ongoingRoutines = CompanyCadenceRoutine::query()
                                                ->where(CompanyCadenceRoutine::FIELD_COMPANY_ID, $companyId)
                                                ->where(CompanyCadenceRoutine::FIELD_STATUS, CompanyCadenceRoutine::STATUS_PENDING)
                                                ->get();

        return $ongoingRoutines->count() > 0;
    }
}
