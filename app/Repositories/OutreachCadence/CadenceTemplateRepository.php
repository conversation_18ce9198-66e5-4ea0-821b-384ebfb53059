<?php

namespace App\Repositories\OutreachCadence;

use App\Http\Controllers\API\OutreachCadence\CadenceTemplateController;
use App\Models\Cadence\BaseModel;
use App\Models\Cadence\CadenceEmailHeaderFooter;
use App\Models\Cadence\CadenceEmailTemplate;
use App\Models\Cadence\CadenceRoutine;
use App\Models\Cadence\CadenceScheduledGroup;
use App\Models\Cadence\CadenceScheduledGroupAction;
use App\Models\Cadence\CadenceSmsTemplate;
use App\Models\Cadence\CadenceTaskTemplate;
use App\Models\User;
use App\Services\EmailTemplates\EmailTemplateImageService;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class CadenceTemplateRepository
{
    const EMAIL          = 'email';
    const SMS            = 'sms';
    const TASK           = 'task';
    const HEADER         = 'header';
    const FOOTER         = 'footer';
    const CONTENT_IMAGES = 'content_images';

    /**
     * @param User $user
     * @param bool $includeGlobal
     * @param bool $withTrashed
     * @return array
     */
    public function getAllTemplatesArray(User $user, bool $includeGlobal = true, bool $withTrashed = false): array
    {
        return [
            self::EMAIL => $this->getEmailTemplates($user, $includeGlobal, $withTrashed)->toArray(),
            self::SMS   => $this->getSmsTemplates($user, $includeGlobal, $withTrashed)->toArray(),
            self::TASK  => $this->getTaskTemplates($user, $includeGlobal, $withTrashed)->toArray(),
        ];
    }

    /**
     * @param User $user
     * @param bool $includeGlobal
     * @param bool $withTrashed
     * @param array $relationships
     * @return Collection<int, CadenceEmailTemplate>
     * @throws Exception
     */
    public function getEmailTemplates(User $user, bool $includeGlobal = true, bool $withTrashed = false, array $relationships = [CadenceEmailTemplate::RELATIONSHIP_USER]): Collection
    {
        $templates = $this->getTemplates(CadenceEmailTemplate::class, $user, $includeGlobal, $withTrashed, $relationships);
        return $this->addImagesToEmailTemplates($templates);
    }

    /**
     * @param User $user
     * @param bool $includeGlobal
     * @param bool $withTrashed
     * @param array $relationships
     * @return Collection<int, CadenceSmsTemplate>
     */
    public function getSmsTemplates(User $user, bool $includeGlobal = true, bool $withTrashed = false, array $relationships = [CadenceSmsTemplate::RELATIONSHIP_USER]): Collection
    {
        return $this->getTemplates(CadenceSmsTemplate::class, $user, $includeGlobal, $withTrashed, $relationships);
    }

    /**
     * @param User $user
     * @param bool $includeGlobal
     * @param bool $withTrashed
     * @param array $relationships
     * @return Collection<int, CadenceTaskTemplate>
     */
    public function getTaskTemplates(User $user, bool $includeGlobal = true, bool $withTrashed = false, array $relationships = [CadenceTaskTemplate::RELATIONSHIP_USER]): Collection
    {
        return $this->getTemplates(CadenceTaskTemplate::class, $user, $includeGlobal, $withTrashed, $relationships);
    }

    /**
     * @param string $class
     * @param User $user
     * @param bool $includeGlobal
     * @param bool $withTrashed
     * @param array $relationships
     * @return Collection
     */
    private function getTemplates(string $class, User $user, bool $includeGlobal, bool $withTrashed, array $relationships): Collection
    {
        $query = $class::query()
                       ->where($class::FIELD_USER_ID, $user->id)
                       ->with($relationships)
                       ->orderBy(Model::CREATED_AT, 'desc');

        if($withTrashed)
            $query->withTrashed();

        if ($includeGlobal) {
            // Templates must be treated as global if they belong to a global routine
            $query->orWhereIn(BaseModel::FIELD_ID, $this->getTemplateIdsWithGlobalRoutine($class))
                  ->orWhere($class::FIELD_GLOBAL, true);
        }

        return $query->get();
    }

    /**
     * @param array $params
     * @param User $user
     * @return CadenceEmailTemplate
     */
    public function createEmailTemplate(array $params, User $user): CadenceEmailTemplate
    {
        $params[CadenceEmailTemplate::FIELD_USER_ID] = $user->id;
        return CadenceEmailTemplate::create($params);
    }

    /**
     * @param array $params
     * @param User $user
     * @return CadenceSmsTemplate
     */
    public function createSmsTemplate(array $params, User $user): CadenceSmsTemplate
    {
        $params[CadenceSmsTemplate::FIELD_USER_ID] = $user->id;
        return CadenceSmsTemplate::create($params);
    }

    /**
     * @param array $params
     * @param User $user
     * @return CadenceTaskTemplate
     */
    public function createTaskTemplate(array $params, User $user): CadenceTaskTemplate
    {
        $params[CadenceTaskTemplate::FIELD_USER_ID] = $user->id;
        return CadenceTaskTemplate::create($params);
    }

    /**
     * @param int $templateId
     * @param array $params
     * @return bool
     */
    public function updateEmailTemplate(int $templateId, array $params): bool
    {
        return CadenceEmailTemplate::query()->where(BaseModel::FIELD_ID, $templateId)->update($params);
    }

    /**
     * @param int $templateId
     * @param array $params
     * @return bool
     */
    public function updateSmsTemplate(int $templateId, array $params): bool
    {
        return CadenceSmsTemplate::query()->where(BaseModel::FIELD_ID, $templateId)->update($params);
    }

    /**
     * @param int $templateId
     * @param array $params
     * @return bool
     */
    public function updateTaskTemplate(int $templateId, array $params): bool
    {
        return CadenceTaskTemplate::query()->where(BaseModel::FIELD_ID, $templateId)->update($params);
    }

    /**
     * @param int $templateId
     * @return void
     */
    public function deleteEmailTemplate(int $templateId): void
    {
        CadenceEmailTemplate::query()->where(BaseModel::FIELD_ID, $templateId)->delete();
    }

    /**
     * @param int $templateId
     * @return void
     */
    public function deleteSmsTemplate(int $templateId): void
    {
        CadenceSmsTemplate::query()->where(BaseModel::FIELD_ID, $templateId)->delete();
    }

    /**
     * @param int $templateId
     * @return void
     */
    public function deleteTaskTemplate(int $templateId): void
    {
        CadenceTaskTemplate::query()->where(BaseModel::FIELD_ID, $templateId)->delete();
    }

    /**
     * @param int $userId
     * @return array
     * @throws Exception
     */
    public function getUserHeaderAndFooter(int $userId): array
    {
        $templates = CadenceEmailHeaderFooter::query()->where(CadenceEmailHeaderFooter::FIELD_USER_ID, $userId)->get();

        $header = $templates->where(CadenceEmailHeaderFooter::FIELD_TYPE, CadenceEmailHeaderFooter::TYPE_HEADER)->first();
        $footer = $templates->where(CadenceEmailHeaderFooter::FIELD_TYPE, CadenceEmailHeaderFooter::TYPE_FOOTER)->first();
        $header = $this->addImagesToHeaderFooter($header, $userId);
        $footer = $this->addImagesToHeaderFooter($footer, $userId);

        return [
            self::HEADER => $header,
            self::FOOTER => $footer,
        ];
    }

    /**
     * @param int $userId
     * @param string $content
     * @return void
     */
    public function updateUserHeader(int $userId, string $content): void
    {
        CadenceEmailHeaderFooter::query()->updateOrCreate(
            [
                CadenceEmailHeaderFooter::FIELD_TYPE    => CadenceEmailHeaderFooter::TYPE_HEADER,
                CadenceEmailHeaderFooter::FIELD_USER_ID => $userId
            ],
            [CadenceEmailHeaderFooter::FIELD_CONTENT => $content,]
        );
    }

    /**
     * @param int $userId
     * @param string $content
     * @return void
     */
    public function updateUserFooter(int $userId, string $content): void
    {
        CadenceEmailHeaderFooter::query()->updateOrCreate(
            [
                CadenceEmailHeaderFooter::FIELD_TYPE    => CadenceEmailHeaderFooter::TYPE_FOOTER,
                CadenceEmailHeaderFooter::FIELD_USER_ID => $userId
            ],
            [CadenceEmailHeaderFooter::FIELD_CONTENT => $content,]
        );
    }

    /**
     * @param CadenceEmailHeaderFooter|null $template
     * @param int $userId
     * @return CadenceEmailHeaderFooter|null
     * @throws Exception
     */
    private function addImagesToHeaderFooter(?CadenceEmailHeaderFooter $template, int $userId): null|CadenceEmailHeaderFooter
    {
        if (!$template)
            return null;

        $template->{self::CONTENT_IMAGES} = $this->getContentImages(
            $template->{CadenceEmailHeaderFooter::FIELD_CONTENT},
            $userId,
            $template->{CadenceEmailHeaderFooter::FIELD_TYPE}
        );
        return $template;
    }

    /**
     * @param Collection<int, CadenceEmailTemplate> $templates
     * @return Collection<int, CadenceEmailTemplate>
     * @throws Exception
     */
    private function addImagesToEmailTemplates(Collection $templates): Collection
    {
        /** @var CadenceEmailTemplate $template */
        return $templates->map(function ($template) {
            $template->{self::CONTENT_IMAGES} = $this->getContentImages(
                $template->{CadenceEmailTemplate::FIELD_BODY},
                $template->id,
                'email'
            );
            return $template;
        });
    }

    /**
     * @param string $content
     * @param int $modelId
     * @param string $templateType
     * @return array
     * @throws Exception
     */
    private function getContentImages(string $content, int $modelId, string $templateType): array
    {
        /** @var EmailTemplateImageService $service */
        $service    = app(EmailTemplateImageService::class);
        $shortcodes = $service->extractShortcodes($content);
        $imageNames = [];
        foreach ($shortcodes as $shortcode) {
            $imageNames[CadenceTemplateController::IMAGE_UPLOAD_BASE_PATH . "/{$templateType}/{$modelId}/$shortcode"] = $shortcode;
        }
        $objects       = $service->downloadImageDataUrlsFromCloudStorage($modelId, array_keys($imageNames));
        $contentImages = [];
        foreach ($objects as $path => $imageDataUrl) {
            $contentImages[$imageNames[$path]] = $imageDataUrl;
        }
        return $contentImages;
    }

    /**
     * @param int $templateId
     * @param User $user
     * @return void
     * @throws Exception
     */
    public function cloneEmailTemplate(int $templateId, User $user): void
    {
        /** @var CadenceEmailTemplate $template */
        $template       = CadenceEmailTemplate::find($templateId);
        $template->name = "[CLONE] {$template->name}";
        $clone          = $this->createEmailTemplate($template->toArray(), $user);

        // save original template images for new template
        $images = $this->getContentImages($template->body, $template->id, 'email');

        /** @var EmailTemplateImageService $emailTemplateService */
        $emailTemplateService = app(EmailTemplateImageService::class);

        foreach ($images as $name => $b64) {
            $emailTemplateService->uploadImageDataUrlToCloudStorage(
                $clone->id,
                $name,
                $b64,
                CadenceTemplateController::IMAGE_UPLOAD_BASE_PATH . '/' . 'email'
            );
        }


    }

    /**
     * @param int $templateId
     * @param User $user
     * @return void
     */
    public function cloneSmsTemplate(int $templateId, User $user): void
    {
        /** @var CadenceSmsTemplate $template */
        $template       = CadenceSmsTemplate::find($templateId);
        $template->name = "[CLONE] {$template->name}";
        $this->createSmsTemplate($template->toArray(), $user);
    }

    /**
     * @param int $templateId
     * @param User $user
     * @return void
     */
    public function cloneTaskTemplate(int $templateId, User $user): void
    {
        /** @var CadenceTaskTemplate $template */
        $template       = CadenceTaskTemplate::find($templateId);
        $template->name = "[CLONE] {$template->name}";
        $this->createTaskTemplate($template->toArray(), $user);
    }

    /**
     * @param string $class
     * @return array
     */
    private function getTemplateIdsWithGlobalRoutine(string $class): array
    {
        $query = $this->getRoutinesWithJoinedActionsQuery();
        $query->where(CadenceRoutine::TABLE . '.' . CadenceRoutine::FIELD_GLOBAL, true);
        $column = match ($class) {
            CadenceEmailTemplate::class => CadenceScheduledGroupAction::FIELD_EMAIL_TEMPLATE_ID,
            CadenceSmsTemplate::class => CadenceScheduledGroupAction::FIELD_SMS_TEMPLATE_ID,
            CadenceTaskTemplate::class => CadenceScheduledGroupAction::FIELD_TASK_TEMPLATE_ID,
        };
        $ids    = $query->get([$column]);
        return $ids->pluck($column)->unique()->toArray();
    }

    /**
     * @return Builder
     */
    private function getRoutinesWithJoinedActionsQuery(): Builder
    {
        return CadenceRoutine::query()
                             ->leftJoin(
                                 CadenceScheduledGroup::TABLE,
                                 CadenceRoutine::TABLE . '.' . BaseModel::FIELD_ID,
                                 '=',
                                 CadenceScheduledGroup::TABLE . '.' . CadenceScheduledGroup::FIELD_CADENCE_ROUTINE_ID
                             )
                             ->leftJoin(
                                 CadenceScheduledGroupAction::TABLE,
                                 CadenceScheduledGroup::TABLE . '.' . BaseModel::FIELD_ID,
                                 '=',
                                 CadenceScheduledGroupAction::TABLE . '.' . CadenceScheduledGroupAction::FIELD_CADENCE_SCHEDULED_GROUP_ID
                             );
    }
}
