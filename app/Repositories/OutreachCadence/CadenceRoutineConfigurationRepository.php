<?php

namespace App\Repositories\OutreachCadence;

use App\Http\Requests\OutreachCadence\UpdateCadenceRoutineRequest;
use App\Models\Cadence\BaseModel;
use App\Models\Cadence\CadenceRoutine;
use App\Models\Cadence\CadenceScheduledGroup;
use App\Models\Cadence\CadenceScheduledGroupAction;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class CadenceRoutineConfigurationRepository
{
    const ROUTINE_DEFAULT_VALUES = [CadenceRoutine::FIELD_NAME => '--Rename Me--'];

    /**
     * @param User $user
     * @param bool $includeGlobal
     * @param array $relationships
     * @return Collection<int, CadenceRoutine>
     */
    public function getRoutineConfigs(
        User  $user,
        bool  $includeGlobal = false,
        array $relationships = [CadenceRoutine::RELATION_SCHEDULED_GROUPS . '.' . CadenceScheduledGroup::RELATION_ACTIONS, CadenceRoutine::RELATION_USER],

    ): Collection
    {
        $query = CadenceRoutine::query()->with($relationships)->where(CadenceRoutine::FIELD_USER_ID, $user->id)->orderBy(Model::CREATED_AT, 'desc');
        if ($includeGlobal)
            $query->orWhere(CadenceRoutine::FIELD_GLOBAL, true);
        return $query->get();
    }

    /**
     * @param int $routineId
     * @return void
     */
    public function deleteRoutineConfig(int $routineId): void
    {
        CadenceRoutine::query()->where(BaseModel::FIELD_ID, $routineId)->delete();
    }

    /**
     * @param array $params
     * @return array
     */
    public function extractActionsFromParams(array $params): array
    {
        $actions = [];
        if (key_exists(UpdateCadenceRoutineRequest::SCHEDULED_GROUPS, $params)) {
            $actions = $params[UpdateCadenceRoutineRequest::SCHEDULED_GROUPS];
            unset($params[UpdateCadenceRoutineRequest::SCHEDULED_GROUPS]);
        }
        return [$params, $actions];
    }

    /**
     * @param array $params
     * @return array
     */
    private function addDefaultsToParams(array $params): array
    {
        foreach (self::ROUTINE_DEFAULT_VALUES as $key => $value) {
            if (!key_exists($key, $params))
                $params[$key] = $value;
        }
        return $params;
    }

    /**
     * @param array $params
     * @param int|null $userId
     * @param bool $forceCreateNewActions
     * @return void
     */
    public function createRoutineConfig(array $params, ?int $userId, bool $forceCreateNewActions = false): void
    {
        [$params, $actions] = $this->extractActionsFromParams($params);
        $params                                = $this->addDefaultsToParams($params);
        $params[CadenceRoutine::FIELD_USER_ID] = $userId;
        /** @var CadenceRoutine $routine */
        $routine = CadenceRoutine::create($params);
        $this->createOrUpdateActions($routine, $actions, $forceCreateNewActions);
    }

    /**
     * @param array $params
     * @param int $routineId
     * @return void
     */
    public function updateRoutineConfig(array $params, int $routineId): void
    {
        [$params, $actions] = $this->extractActionsFromParams($params);

        $query = CadenceRoutine::query()->where(BaseModel::FIELD_ID, $routineId);
        $query->update($params);

        /** @var CadenceRoutine $routine */
        $routine = $query->first();
        $this->createOrUpdateActions($routine, $actions);
    }

    /**
     * @param CadenceRoutine $routine
     * @param array $actions
     * @param bool $forceCreateNew
     * @return void
     */
    private function createOrUpdateActions(CadenceRoutine $routine, array $actions, bool $forceCreateNew = false): void
    {
        foreach ($actions as $group) {

            // Delete
            if (key_exists('id', $group) && key_exists('delete', $group) && $group['delete'])
                $this->deleteGroupAndActions($group['id']);

            // Create
            elseif (!key_exists('id', $group) || $forceCreateNew)
                $this->createGroupAndAction($routine, $group);

            // Update
            else
                $this->updateGroupAndAction($routine, $group);

        }
    }

    /**
     * @param int $scheduledGroupId
     * @return void
     */
    private function deleteGroupAndActions(int $scheduledGroupId): void
    {
        CadenceScheduledGroupAction::query()->where(CadenceScheduledGroupAction::FIELD_CADENCE_SCHEDULED_GROUP_ID, $scheduledGroupId)->delete();
        CadenceScheduledGroup::query()->where(BaseModel::FIELD_ID, $scheduledGroupId)->delete();
    }

    /**
     * @param CadenceRoutine $routine
     * @param array $group
     * @return void
     */
    private function createGroupAndAction(CadenceRoutine $routine, array $group): void
    {
        [$groupParams, $actionParams] = $this->separateGroupAndActionParams($group);

        // Set to email by default
        if (!key_exists(CadenceScheduledGroupAction::FIELD_ACTION_TYPE, $actionParams))
            $actionParams[CadenceScheduledGroupAction::FIELD_ACTION_TYPE] = CadenceScheduledGroupAction::ACTION_TYPE_EMAIL;

        /** @var CadenceScheduledGroup $group */
        $group = $routine->scheduledGroups()->create($groupParams);
        $group->actions()->create($actionParams);
    }

    /**
     * @param CadenceRoutine $routine
     * @param array $group
     * @return void
     */
    private function updateGroupAndAction(CadenceRoutine $routine, array $group): void
    {
        [$groupParams, $actionParams] = $this->separateGroupAndActionParams($group);

        /** @var CadenceScheduledGroup $group */
        $group = $routine->scheduledGroups->where(BaseModel::FIELD_ID, $group[BaseModel::FIELD_ID])->first();
        $group->fill($groupParams);
        $group->save();

        // The data structure allows for many actions, but for now we are treating it as one-to-one
        /** @var CadenceScheduledGroupAction $action */
        $action = $group->actions->first();
        $action->fill($actionParams);
        $action->save();
    }

    /**
     * @param array $group
     * @return array
     */
    private function separateGroupAndActionParams(array $group): array
    {
        $actionParams = [];
        if (key_exists(UpdateCadenceRoutineRequest::ACTIONS, $group) && count($group[UpdateCadenceRoutineRequest::ACTIONS]) > 0) {
            $actionParams = $group[UpdateCadenceRoutineRequest::ACTIONS][0];
        }
        $groupParams = $group;
        unset($groupParams[UpdateCadenceRoutineRequest::ACTIONS]);

        return [$groupParams, $actionParams];
    }

    /**
     * @param int $routineConfigId
     * @return CadenceRoutine|null
     */
    public function find(int $routineConfigId): ?CadenceRoutine
    {
        return CadenceRoutine::query()->find($routineConfigId);
    }

    /**
     * @param int $routineId
     * @param User $user
     * @return void
     */
    public function cloneRoutine(int $routineId, User $user): void
    {
        /** @var CadenceRoutine $routine */
        $routine = CadenceRoutine::find($routineId);
        $routine->load(CadenceRoutine::RELATION_SCHEDULED_GROUPS . '.' . CadenceScheduledGroup::RELATION_ACTIONS);
        $routine->name = "[CLONE] {$routine->name}";
        $this->createRoutineConfig($routine->toArray(), $user->id, true);
    }

    /**
     * @param array $newGroupOrder
     * @return void
     */
    public function updateActionOrders(array $newGroupOrder): void
    {
        $orderString = implode(',', $newGroupOrder);
        $groups      = CadenceScheduledGroup::query()->whereIn(CadenceScheduledGroup::FIELD_ID, $newGroupOrder)
                                            ->orderByRaw("FIELD(id, {$orderString})")
                                            ->get();
        /** @var CadenceScheduledGroup $group */
        foreach ($groups as $index => $group) {
            $group->ordinal_value = $index + 1;
            $group->save();
        }

    }


}
