<?php

namespace App\Repositories\CompanyMetricsRepository;

use App\Enums\CompanyMetrics\CompanyMetricRequestTypes;
use App\Enums\CompanyMetrics\CompanyMetricSources;
use App\Models\CompanyMetric;
use App\Models\Odin\Company;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class CompanyMetricsRepository
{
    /**
     * @param int $companyId
     * @param CompanyMetricRequestTypes $metricRequestType
     * @return CompanyMetric|null
     */
    public function getLatestCompanyMetric(int $companyId, CompanyMetricRequestTypes $metricRequestType): ?CompanyMetric
    {
        /** @var CompanyMetric|null */
        return CompanyMetric::query()
            ->where(CompanyMetric::FIELD_COMPANY_ID, '=', $companyId)
            ->where(CompanyMetric::FIELD_REQUEST_TYPE, '=', $metricRequestType->value)
            ->latest('updated_at')
            ->first();
    }

    /**
     * @param int $companyId
     * @param CompanyMetricRequestTypes $metricRequestType
     * @return Collection
     */
    public function getCompanyMetrics(int $companyId, CompanyMetricRequestTypes $metricRequestType): Collection
    {
        return CompanyMetric::query()
            ->where(CompanyMetric::FIELD_COMPANY_ID, '=', $companyId)
            ->where(CompanyMetric::FIELD_REQUEST_TYPE, '=', $metricRequestType->value)
            ->get();
    }

    /**
     * @param array $response
     * @param CompanyMetricRequestTypes $requestType
     * @param Company $company
     * @param string $requestString
     * @param CompanyMetricSources $companyMetricSource
     * @return CompanyMetric
     */
    public function createCompanyMetric(array $response, CompanyMetricRequestTypes $requestType, Company $company, string $requestString, CompanyMetricSources $companyMetricSource): CompanyMetric
    {
        $companyMetric = new CompanyMetric([
            CompanyMetric::FIELD_COMPANY_ID         => $company->id,
            CompanyMetric::FIELD_SOURCE             => $companyMetricSource->value,
            CompanyMetric::FIELD_REQUEST_TYPE       => $requestType->value,
            CompanyMetric::FIELD_REQUEST_URL        => $requestString,
            CompanyMetric::FIELD_REQUEST_RESPONSE   => $response,
        ]);

        $companyMetric->save();

        return $companyMetric;
    }
}
