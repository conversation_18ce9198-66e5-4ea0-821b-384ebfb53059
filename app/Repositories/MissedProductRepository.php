<?php

namespace App\Repositories;

use App\Builders\Odin\MissedProductBuilder;
use App\Enums\MissedProducts\MissedProductReasonEventType;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModule;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\MissedProducts\MissedProduct;
use App\Models\MissedProducts\MissedProductReasonEvent;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Repositories\Odin\ServiceProductRepository;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Carbon\Carbon;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;

class MissedProductRepository
{
    const int DEFAULT_EXPIRATION_IN_DAYS = 90;

    public function __construct(
        protected ServiceProductRepository        $serviceProductRepository,
    ) {}

    /**
     * @param int $consumerProductId
     * @param int $serviceId
     * @param int $legsRemaining
     * @param bool $isReserved
     * @return MissedProduct
     */
    public function create(
        int $consumerProductId,
        int $serviceId,
        int $legsRemaining,
        ?bool $isReserved = false
    ): MissedProduct {
        /** @var MissedProduct $missedProduct */
        $missedProduct = MissedProduct::query()->firstOrCreate([
                MissedProduct::FIELD_CONSUMER_PRODUCT_ID => $consumerProductId
            ],[
                MissedProduct::FIELD_CONSUMER_PRODUCT_ID => $consumerProductId,
                MissedProduct::FIELD_INDUSTRY_SERVICE_ID => $serviceId,
                MissedProduct::FIELD_SELLABLE_LEGS => $legsRemaining,
                MissedProduct::FIELD_IS_RESERVED => $isReserved,
            ]);

        return $missedProduct;
    }

    /**
     * @param MissedProduct $product
     * @return bool
     */
    public function delete(MissedProduct $product): bool
    {
        return $product->delete();
    }

    /**
     * @return void
     */
    public function cleanupTable(): void
    {
        $expirationDate = now()->subDays(config('sales.missed_products_expiry_days', self::DEFAULT_EXPIRATION_IN_DAYS));

        MissedProduct::query()
            ->where(MissedProduct::CREATED_AT, '<', $expirationDate)
            ->delete();
    }

    /**
     * @param Company $company
     * @param Carbon|null $fromDate
     * @return Builder
     */
    public function getMissedProductsForNewCompany(Company $company, ?Carbon $fromDate = null): Builder
    {
        $industries = $company->industries()->pluck(Industry::TABLE.'.'.Industry::FIELD_ID)->toArray();
        $counties = $company->locations->reduce(function(array $output, CompanyLocation $location) {
            $countyLocationId = $location->address->county_location_id;
            if (!in_array($countyLocationId, $output))
                $output[] = $countyLocationId;
            return $output;
        }, []);

        $fromDate = $fromDate ?? now()->subWeeks(1);

        return MissedProductBuilder::query()
            ->forCompany($company->id)
            ->forIndustries($industries)
            ->forCountyLocations($counties)
            ->fromDate($fromDate)
            ->getQuery();
    }

    /**
     * @param int $companyId
     * @param Carbon|null $fromDate
     * @return int
     */
    public function getOutbidEventCountForCompany(int $companyId, ?Carbon $fromDate = null): int
    {
        $company = Company::query()->findOrFail($companyId);
        $fromDate = $fromDate ?? $this->getDefaultStartDate();

        return MissedProductReasonEvent::query()
            ->join(CompanyCampaign::TABLE, fn(JoinClause $join) =>
                $join->on(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID, '=', MissedProductReasonEvent::TABLE .'.'. MissedProductReasonEvent::FIELD_COMPANY_CAMPAIGN_ID)
                    ->where(CompanyCampaign::TABLE .'.'. CompanyCampaign::CREATED_AT, '<', $fromDate)
            )->where(MissedProductReasonEvent::FIELD_EVENT_TYPE, MissedProductReasonEventType::OUTBID)
            ->where(MissedProductReasonEvent::FIELD_STARTED_AT, '>=', $fromDate->startOfDay())
            ->where(MissedProductReasonEvent::FIELD_COMPANY_ID, $company->id)
            ->sum(MissedProductReasonEvent::FIELD_TOTAL)
            ?? 0;
    }

    /**
     * @param int $campaignId
     * @param Carbon|null $fromDate
     * @return int
     */
    public function getOutbidEventCountForCampaign(int $campaignId, ?Carbon $fromDate = null): int
    {
        $campaign = CompanyCampaign::query()->findOrFail($campaignId);
        $fromDate = max($fromDate ?? $this->getDefaultStartDate(), $campaign->created_at);

        return MissedProductReasonEvent::query()
            ->where(MissedProductReasonEvent::FIELD_EVENT_TYPE, MissedProductReasonEventType::OUTBID)
            ->where(MissedProductReasonEvent::FIELD_STARTED_AT, '>=', $fromDate->startOfDay())
            ->where(MissedProductReasonEvent::FIELD_COMPANY_CAMPAIGN_ID, $campaign->id)
            ->sum(MissedProductReasonEvent::FIELD_TOTAL)
            ?? 0;
    }

    /**
     * @param int $companyId
     * @param Carbon|null $fromDate
     * @return Builder
     */
    public function getMissedProductsForExistingCompanyQuery(int $companyId, ?Carbon $fromDate = null): Builder
    {
        $company = Company::query()->findOrFail($companyId);
        $fromDate = $fromDate ?? $this->getDefaultStartDate();

        $zipLocationIds = CompanyCampaignLocationModuleLocation::query()
            ->join(CompanyCampaignLocationModule::TABLE, CompanyCampaignLocationModule::TABLE . '.' . CompanyCampaignLocationModule::FIELD_ID, '=', CompanyCampaignLocationModuleLocation::TABLE . '.' . CompanyCampaignLocationModuleLocation::FIELD_MODULE_ID)
            ->join(CompanyCampaign::TABLE, fn(JoinClause $join) =>
                $join->on(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID, '=', CompanyCampaignLocationModule::TABLE . '.' . CompanyCampaignLocationModule::FIELD_CAMPAIGN_ID)
                    ->where(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_COMPANY_ID, '=', $companyId)
                    ->where(CompanyCampaign::TABLE .'.'. CompanyCampaign::CREATED_AT, '<', $fromDate) // Ignore fresh campaigns
            )->distinct(CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID)
            ->pluck(CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID)
            ->toArray();
        $industries = $company->industries()->pluck(Industry::FIELD_ID)->toArray();
        /** @var LocationRepository $locationRepository */
        $locationRepository = app(LocationRepository::class);
        $countyLocationIds = $locationRepository->getCountyLocationsFromZipCodeLocations($zipLocationIds, true)->toArray();
        $excludeDateRange = $this->getBadPurchasingStatusExclusionTime($company, $fromDate);

        return MissedProductBuilder::query()
            ->forCompany($companyId)
            ->forCountyLocations($countyLocationIds)
            ->forIndustries($industries)
            ->fromDate($fromDate)
            ->excludeDateRange($excludeDateRange)
            ->getQuery();
    }

    /**
     * @param int $campaignId
     * @param Carbon|null $fromDate
     * @return Builder
     */
    public function getMissedProductsForCompanyCampaignQuery(int $campaignId, ?Carbon $fromDate = null): Builder
    {
        $campaign = CompanyCampaign::query()->findOrFail($campaignId);
        $fromDate = $fromDate ?? $this->getDefaultStartDate();

        // Ignore fresh campaigns
        $fromDate = max($campaign->created_at, $fromDate);
        $countyLocations = $campaign->locationModule->getCountyLocationIds();
        $excludeDateRange = $this->getBadPurchasingStatusExclusionTime($campaign->company, $fromDate);

        return MissedProductBuilder::query()
            ->forCompany($campaign->company_id)
            ->forCountyLocations($countyLocations)
            ->forIndustryServices($campaign->service_id)
            ->forProducts($campaign->product_id)
            ->fromDate($fromDate)
            ->excludeDateRange($excludeDateRange)
            ->addSelectColumns([
                DB::raw(ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ID . ' as cp_id'),
                DB::raw(ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_CREATED_AT . ' as cp_created'),
            ])
            ->getQuery();
    }

    /**
     * @return Carbon
     */
    private function getDefaultStartDate(): Carbon
    {
        return now()->subDays(MissedProductBuilder::DEFAULT_DAYS_TO_QUERY);
    }

    /**
     * Check if company had a bad consolidated status during the period so we can exclude the date range
     *
     * @param Company $company
     * @param Carbon $fromDate
     * @return Carbon[]|null
     */
    public function getBadPurchasingStatusExclusionTime(Company $company, Carbon $fromDate): ?array
    {
        $companyBadStatusEvents = MissedProductReasonEvent::query()
            ->select(DB::raw('MIN(' . MissedProductReasonEvent::FIELD_STARTED_AT . ') as started_at, MAX(' . MissedProductReasonEvent::FIELD_ENDED_AT . ') as ended_at'))
            ->where(MissedProductReasonEvent::FIELD_COMPANY_ID, $company->id)
            ->where(MissedProductReasonEvent::FIELD_EVENT_TYPE, MissedProductReasonEventType::EXITED_PURCHASING_STATUS)
            ->where(MissedProductReasonEvent::FIELD_ENDED_AT, '>=', $fromDate)
            ->first();

        return $companyBadStatusEvents->ended_at
            ? [max($companyBadStatusEvents->started_at, $fromDate), $companyBadStatusEvents->ended_at]
            : null;
    }
}
