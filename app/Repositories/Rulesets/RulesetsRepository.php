<?php

namespace App\Repositories\Rulesets;

use App\Contracts\Repositories\RulesetsRepositoryContract;
use App\Models\Ruleset;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class RulesetsRepository implements RulesetsRepositoryContract
{
    /**
     * @param array $queryFilter
     * @inheritDoc
     */
    public function getAll(?array $queryFilter): Collection
    {
        $query = Ruleset::query();

        if (isset($queryFilter['type'])) {
            $query->where(Ruleset::FIELD_TYPE, $queryFilter['type']);
        }

        return $query->get();
    }

    /**
     * @inheritDoc
     */
    public function getRulesetByName(string $name): ?Ruleset
    {
        /** @var Ruleset|null $ruleset */
        $ruleset = Ruleset::query()->where(Ruleset::FIELD_NAME, $name)->first();

        return $ruleset;
    }

    /**
     * @inheritDoc
     */
    public function getRulesetById(int $id): ?Ruleset
    {
        /** @var Ruleset|null $ruleset */
        $ruleset = Ruleset::query()->where(Ruleset::FIELD_ID, $id)->first();

        return $ruleset;
    }

    /**
     * @inheritDoc
     */
    public function updateOrCreateRuleset(
        string $name,
        string $type,
        string $source,
        array $filter,
        array $payload,
        ?int $id = null
    ): Ruleset|Model
    {
        return Ruleset::query()->updateOrCreate(
            [
                Ruleset::FIELD_ID      => $id
            ],
            [
                Ruleset::FIELD_NAME    => $name,
                Ruleset::FIELD_TYPE    => $type,
                Ruleset::FIELD_SOURCE  => $source,
                Ruleset::FIELD_FILTER  => $filter,
                Ruleset::FIELD_RULES   => $payload
            ]
        );
    }

    /**
     * @inheritDoc
     */
    public function deleteRuleset(Ruleset $ruleset): bool
    {
        return $ruleset->delete();
    }

}
