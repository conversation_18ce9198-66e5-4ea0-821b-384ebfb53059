<?php

namespace App\Repositories\MissedProducts;

use App\Enums\MissedProducts\MissedProductReasonEventType;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\MissedProducts\MissedProductReasonEvent;
use App\Models\Odin\Company;
use Illuminate\Support\Facades\DB;

/**
 * These events are intended for tracking reasons for missed products for company feedback
 */
class MissedProductReasonEventRepository
{
    const int MAXIMUM_EVENT_AGE_IN_DAYS = 90;

    /**
     * @param CompanyCampaign $campaign
     * @param MissedProductReasonEventType $event
     * @return bool
     */
    public function handleNewCampaignEvent(CompanyCampaign $campaign, MissedProductReasonEventType $event): bool
    {
        return match($event) {
            MissedProductReasonEventType::OVER_BUDGET                                                       => $this->handleOverBudget($campaign),
            MissedProductReasonEventType::CAMPAIGN_ACTIVATED, MissedProductReasonEventType::CAMPAIGN_PAUSED => $this->handleCampaignStatusChange($campaign, $event),
            MissedProductReasonEventType::OUTBID                                                            => $this->handleCampaignOutbid($campaign),
            default                                                                                         => false,
        };
    }

    /**
     * @param Company $company
     * @param MissedProductReasonEventType $event
     * @return bool
     */
    public function handleNewCompanyEvent(Company $company, MissedProductReasonEventType $event): bool
    {
        return match($event) {
            MissedProductReasonEventType::ENTERED_PURCHASING_STATUS,
            MissedProductReasonEventType::EXITED_PURCHASING_STATUS
                    => $this->handleBadCompanyStatusEvent($company, $event),
            default => false,
        };
    }

    /**
     * @param Company $company
     * @param MissedProductReasonEventType $event
     * @return bool
     */
    protected function handleBadCompanyStatusEvent(Company $company, MissedProductReasonEventType $event): bool
    {
        if ($event === MissedProductReasonEventType::ENTERED_PURCHASING_STATUS) {
            $exitEvent = MissedProductReasonEvent::query()
                ->where(MissedProductReasonEvent::FIELD_COMPANY_ID, $company->id)
                ->where(MissedProductReasonEvent::FIELD_EVENT_TYPE, MissedProductReasonEventType::EXITED_PURCHASING_STATUS)
                ->whereNull(MissedProductReasonEvent::FIELD_ENDED_AT)
                ->orderByDesc(MissedProductReasonEvent::FIELD_ID)
                ->first();
            if ($exitEvent) {
                return $exitEvent->update([MissedProductReasonEvent::FIELD_ENDED_AT => now()]);
            }
            else {
                return !!MissedProductReasonEvent::query()->create([
                    MissedProductReasonEvent::FIELD_EVENT_TYPE          => MissedProductReasonEventType::EXITED_PURCHASING_STATUS,
                    MissedProductReasonEvent::FIELD_STARTED_AT          => null,
                    MissedProductReasonEvent::FIELD_ENDED_AT            => now(),
                    MissedProductReasonEvent::FIELD_COMPANY_ID          => $company->id,
                ]);
            }
        }
        else {
            return !!MissedProductReasonEvent::query()->create([
                MissedProductReasonEvent::FIELD_EVENT_TYPE          => MissedProductReasonEventType::EXITED_PURCHASING_STATUS,
                MissedProductReasonEvent::FIELD_STARTED_AT          => now(),
                MissedProductReasonEvent::FIELD_ENDED_AT            => null,
                MissedProductReasonEvent::FIELD_COMPANY_ID          => $company->id,
            ]);
        }
    }

    /**
     * @param CompanyCampaign $campaign
     * @return bool
     */
    protected function handleCampaignOutbid(CompanyCampaign $campaign): bool
    {
        $alreadyOutbidToday = DB::table(MissedProductReasonEvent::TABLE)
            ->where(MissedProductReasonEvent::FIELD_EVENT_TYPE, MissedProductReasonEventType::OUTBID)
            ->where(MissedProductReasonEvent::FIELD_COMPANY_CAMPAIGN_ID, $campaign->id)
            ->where(MissedProductReasonEvent::CREATED_AT, '>=', now()->startOfDay());
        if ($alreadyOutbidToday->exists()) {
            $alreadyOutbidToday->increment(MissedProductReasonEvent::FIELD_TOTAL);
        }
        else {
            MissedProductReasonEvent::query()->create([
                MissedProductReasonEvent::FIELD_EVENT_TYPE          => MissedProductReasonEventType::OUTBID,
                MissedProductReasonEvent::FIELD_STARTED_AT          => now()->startOfDay(),
                MissedProductReasonEvent::FIELD_ENDED_AT            => now()->endOfDay(),
                MissedProductReasonEvent::FIELD_COMPANY_CAMPAIGN_ID => $campaign->id,
                MissedProductReasonEvent::FIELD_COMPANY_ID          => $campaign->company_id,
                MissedProductReasonEvent::FIELD_TOTAL               => 1,
            ]);
        }

        return true;
    }

    /**
     * @param CompanyCampaign $campaign
     * @param MissedProductReasonEventType $event
     * @return bool
     */
    protected function handleCampaignStatusChange(CompanyCampaign $campaign, MissedProductReasonEventType $event): bool
    {
        if ($event === MissedProductReasonEventType::CAMPAIGN_ACTIVATED) {
            $pauseEvent = MissedProductReasonEvent::query()
                ->where(MissedProductReasonEvent::FIELD_COMPANY_CAMPAIGN_ID, $campaign->id)
                ->where(MissedProductReasonEvent::FIELD_EVENT_TYPE, MissedProductReasonEventType::CAMPAIGN_PAUSED)
                ->whereNull(MissedProductReasonEvent::FIELD_ENDED_AT)
                ->orderBy(MissedProductReasonEvent::CREATED_AT, 'desc')
                ->first();

            if ($pauseEvent) {
                return $pauseEvent->update([
                    MissedProductReasonEvent::FIELD_ENDED_AT => now(),
                ]);
            }
            else {
                return !!MissedProductReasonEvent::query()->create([
                    MissedProductReasonEvent::FIELD_EVENT_TYPE          => MissedProductReasonEventType::CAMPAIGN_PAUSED,
                    MissedProductReasonEvent::FIELD_ENDED_AT            => now(),
                    MissedProductReasonEvent::FIELD_COMPANY_CAMPAIGN_ID => $campaign->id,
                    MissedProductReasonEvent::FIELD_COMPANY_ID          => $campaign->company_id,
                ]);
            }
        }
        else {
            return !!MissedProductReasonEvent::query()
                ->create([
                    MissedProductReasonEvent::FIELD_EVENT_TYPE          => MissedProductReasonEventType::CAMPAIGN_PAUSED,
                    MissedProductReasonEvent::FIELD_COMPANY_CAMPAIGN_ID => $campaign->id,
                    MissedProductReasonEvent::FIELD_COMPANY_ID          => $campaign->company_id,
                    MissedProductReasonEvent::FIELD_STARTED_AT          => now(),
                ]);
        }
    }
    /**
     * @param CompanyCampaign $campaign
     * @return bool
     */
    protected function handleOverBudget(CompanyCampaign $campaign): bool
    {
        $alreadyOverBudget = MissedProductReasonEvent::query()
            ->where(MissedProductReasonEvent::FIELD_COMPANY_CAMPAIGN_ID, $campaign->id)
            ->where(MissedProductReasonEvent::FIELD_EVENT_TYPE, MissedProductReasonEventType::OVER_BUDGET)
            ->where(MissedProductReasonEvent::FIELD_STARTED_AT, '>=', now()->startOfDay())
            ->first();
        if ($alreadyOverBudget) {
            return true;
        }
        else {
            $overBudgetYesterday = MissedProductReasonEvent::query()
                ->where(MissedProductReasonEvent::FIELD_COMPANY_CAMPAIGN_ID, $campaign->id)
                ->where(MissedProductReasonEvent::FIELD_ENDED_AT, '>=', now()->subDay()->endOfDay())
                ->where(MissedProductReasonEvent::FIELD_EVENT_TYPE, MissedProductReasonEventType::OVER_BUDGET)
                ->first();
            if ($overBudgetYesterday) {
                return $overBudgetYesterday->update([
                    MissedProductReasonEvent::FIELD_ENDED_AT => now()->endOfDay()
                ]);
            }
            else {
                return !!MissedProductReasonEvent::query()->create([
                    MissedProductReasonEvent::FIELD_EVENT_TYPE          => MissedProductReasonEventType::OVER_BUDGET,
                    MissedProductReasonEvent::FIELD_COMPANY_CAMPAIGN_ID => $campaign->id,
                    MissedProductReasonEvent::FIELD_COMPANY_ID          => $campaign->company_id,
                    MissedProductReasonEvent::FIELD_STARTED_AT          => now(),
                    MissedProductReasonEvent::FIELD_ENDED_AT            => now()->endOfDay(),
                ]);
            }
        }
    }

    /**
     * @return void
     */
    public function cleanupTable(): void
    {
        MissedProductReasonEvent::query()
            ->where(MissedProductReasonEvent::CREATED_AT, '<', now()->subDays(config('sales.missed_product_reason_events_expiry_days', self::MAXIMUM_EVENT_AGE_IN_DAYS)))
            ->delete();
    }
}