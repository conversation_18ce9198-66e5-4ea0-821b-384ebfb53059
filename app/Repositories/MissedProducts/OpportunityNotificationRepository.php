<?php

namespace App\Repositories\MissedProducts;

use App\Builders\OpportunityNotificationBuilder;
use App\Models\MissedProducts\OpportunityNotification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class OpportunityNotificationRepository
{
    /**
     * @return Collection
     */
    public function all(): Collection
    {
        return OpportunityNotification::all();
    }

    /**
     * @param string $recipients
     * @param string $content
     * @param string $deliveryMethod
     * @param float $companyId
     * @param mixed|null $sentAt
     * @param int $configId
     * @param int|null $viewCount
     * @return OpportunityNotification
     */
    public function createNotification(
        string $recipients,
        string $content,
        string $deliveryMethod,
        float $companyId,
        mixed $sentAt,
        int $configId,
        ?int $viewCount = 0,
    ): OpportunityNotification
    {
        /** @var OpportunityNotification $notification */
        $notification = OpportunityNotification::query()->create([
            OpportunityNotification::FIELD_RECIPIENTS => $recipients,
            OpportunityNotification::FIELD_CONTENT => $content,
            OpportunityNotification::FIELD_DELIVERY_METHOD => $deliveryMethod,
            OpportunityNotification::FIELD_VIEW_COUNT => $viewCount,
            OpportunityNotification::FIELD_COMPANY_ID => $companyId,
            OpportunityNotification::FIELD_SENT_AT => $sentAt,
            OpportunityNotification::FIELD_CONFIG_ID => $configId
        ]);

        return $notification;
    }

    /**
     * @param OpportunityNotification $notification
     * @param string $recipients
     * @param string $content
     * @param string $deliveryMethod
     * @param float $companyId
     * @param string $consumerProductId
     * @param mixed|null $sentAt
     * @param int|null $viewCount
     * @return OpportunityNotification
     */
    public function updateNotification(
        OpportunityNotification $notification,
        string $recipients,
        string $content,
        string $deliveryMethod,
        float $companyId,
        string $consumerProductId,
        mixed $sentAt = null,
        ?int $viewCount = null,
    ): OpportunityNotification
    {
        $notification->update([
            OpportunityNotification::FIELD_RECIPIENTS => $recipients,
            OpportunityNotification::FIELD_CONTENT => $content,
            OpportunityNotification::FIELD_DELIVERY_METHOD => $deliveryMethod,
            OpportunityNotification::FIELD_VIEW_COUNT => $viewCount,
            OpportunityNotification::FIELD_COMPANY_ID => $companyId,
            OpportunityNotification::FIELD_SENT_AT => $sentAt,
        ]);

        return $notification;
    }

    /**
     * @param OpportunityNotification $notification
     * @return bool|null
     */
    public function deleteNotification(OpportunityNotification $notification): ?bool
    {
        return $notification->delete();
    }


    /**
     * @param string|null $recipients
     * @param string|null $content
     * @param string|null $deliveryMethod
     * @param array|null $sentAt
     * @param mixed|null $companyName
     * @param string|null $sortCol
     * @param string|null $sortDir
     * @param int|null $configurationId
     * @return Builder
     */
    public function getNotificationsWithSearchFilters(
        ?string $recipients = null,
        ?string $content = null,
        ?string $deliveryMethod = null,
        ?array $sentAt = null,
        mixed $companyName = null,
        ?string $sortCol = null,
        ?string $sortDir = null,
        ?int $configurationId = null,
    ): Builder
    {
        return OpportunityNotificationBuilder::query()
            ->forRecipients($recipients)
            ->forContent($content)
            ->forDeliveryMethod($deliveryMethod)
            ->forSentAt($sentAt)
            ->forCompanyName($companyName)
            ->forConfigId($configurationId)
            ->sortDirection($sortDir)
            ->sortColumn($sortCol)
            ->getQuery();
    }


    /**
     * @param int|null $configId
     * @param int|null $companyId
     * @param string|null $recipients
     * @return OpportunityNotification|null
     */
    public function getLastNotification(
        ?int $configId = null,
        ?int $companyId = null,
        ?string $recipients = null,
    ): OpportunityNotification|null
    {
        /** @var OpportunityNotification $notification */
        $notification = OpportunityNotificationBuilder::query()
            ->forConfigId($configId)
            ->forCompanyId($companyId)
            ->forRecipients($recipients)
            ->sortColumn(OpportunityNotificationBuilder::COLUMN_ID)
            ->sortDirection(OpportunityNotificationBuilder::DESC_DIRECTION)
            ->getQuery()
            ->limit(1)
            ->first();

        return $notification;
    }
}
