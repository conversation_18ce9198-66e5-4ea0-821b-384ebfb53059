<?php

namespace App\Repositories\MissedProducts;

use App\Builders\OpportunityNotificationConfigBuilder;
use App\Enums\NotificationConfigProductType;
use App\Enums\OpportunityNotifications\OpportunityNotificationConfigType;
use App\Models\EmailTemplate;
use App\Models\MissedProducts\OpportunityNotification;
use App\Models\MissedProducts\OpportunityNotificationConfig;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class OpportunityNotificationConfigRepository
{
    /**
     * @return Collection
     */
    public function all(): Collection
    {
        return OpportunityNotification::all();
    }

    /**
     * @param array $payload
     * @return OpportunityNotificationConfig
     * @throws Exception
     */
    public function createNotificationConfig(array $payload): OpportunityNotificationConfig
    {
        $type = OpportunityNotificationConfigType::tryFrom($payload[OpportunityNotificationConfig::FIELD_TYPE]);
        if ($type === OpportunityNotificationConfigType::BDM_COMPANIES) {
            $payload[OpportunityNotificationConfig::FIELD_ACTIVE] = true;
            $payload[OpportunityNotificationConfig::FIELD_ATTEMPT_ON_DAYS] = [];
        }
        else if ($type === OpportunityNotificationConfigType::CAMPAIGN_SUMMARY) {
            $payload[OpportunityNotificationConfig::FIELD_MAXIMUM_DAYS_LAST_LEAD] = $payload[OpportunityNotificationConfig::FIELD_MAXIMUM_DAYS_LAST_LEAD] ?? 90;
        }

        /** @var OpportunityNotificationConfig $config */
        $config = OpportunityNotificationConfig::query()->create($payload);

        return $config;
    }

    /**
     * @param OpportunityNotificationConfig $config
     * @param array $payload
     * @return OpportunityNotificationConfig
     */
    public function updateNotificationConfig(OpportunityNotificationConfig $config, array $payload): OpportunityNotificationConfig
    {
        $disableUpdate = [OpportunityNotificationConfig::FIELD_TYPE];
        $config->update(array_filter($payload, fn($key) => !in_array($key, $disableUpdate), ARRAY_FILTER_USE_KEY));

        return $config;
    }

    /**
     * @param OpportunityNotificationConfig $config
     * @return bool|null
     */
    public function deleteNotificationConfig(OpportunityNotificationConfig $config): ?bool
    {
        return $config->delete();
    }

    /**
     * @return Collection|null
     */
    public function getEmailTemplateOptions(): Collection|null
    {
        return EmailTemplate::query()
            ->get([EmailTemplate::FIELD_ID, EmailTemplate::FIELD_NAME]);
    }

    /**
     * @param string|null $name
     * @param int|null $maxFrequency
     * @param string|null $sendTime
     * @param int|null $leadThreshold
     * @param bool|null $active
     * @param string|null $sortCol
     * @param string|null $sortDir
     * @param array|null $countingRelations
     * @return Builder
     */
    public function getConfigsWithSearchFilters(
        ?string $name = null,
        ?int    $maxFrequency = null,
        ?string $sendTime = null,
        ?int    $leadThreshold = null,
        ?bool   $active = null,
        ?string $sortCol = null,
        ?string $sortDir = null,
        ?array  $countingRelations = [],
    ): Builder
    {
        return OpportunityNotificationConfigBuilder::query()
            ->forName($name)
            ->forMaxFrequency($maxFrequency)
            ->forSendTime($sendTime)
            ->forLeadThreshold($leadThreshold)
            ->forActive($active)
            ->sortDirection($sortDir)
            ->sortColumn($sortCol)
            ->withCountingRelations($countingRelations)
            ->getQuery();
    }
}
