<?php

namespace App\Repositories\Calendar;

use App\Models\Calendar\CalendarEventAttendee;

class CalendarEventAttendeeRepository
{
    /**
     * @param int $calendarEventId
     * @param string $email
     * @param string $status
     * @param int|null $identifiedContactId
     * @param string|null $relationType
     * @param int|null $relationId
     * @param string|null $name
     * @param bool|null $isOrganizer
     * @return CalendarEventAttendee
     */
    public function updateOrCreate(
        int $calendarEventId,
        string $email,
        string $status,
        ?int $identifiedContactId = null,
        ?string $relationType = null,
        ?int $relationId = null,
        ?string $name = null,
        ?bool $isOrganizer = null
    ): CalendarEventAttendee
    {
        return CalendarEventAttendee::query()
            ->updateOrCreate([
                CalendarEventAttendee::FIELD_CALENDAR_EVENT_ID => $calendarEventId,
                CalendarEventAttendee::FIELD_EMAIL             => $email,
            ], [
                CalendarEventAttendee::FIELD_NAME                  => $name,
                CalendarEventAttendee::FIELD_STATUS                => $status,
                CalendarEventAttendee::FIELD_IDENTIFIED_CONTACT_ID => $identifiedContactId,
                CalendarEventAttendee::FIELD_RELATION_TYPE         => $relationType,
                CalendarEventAttendee::FIELD_RELATION_ID           => $relationId,
                CalendarEventAttendee::FIELD_IS_ORGANIZER          => $isOrganizer,
            ]);
    }

}
