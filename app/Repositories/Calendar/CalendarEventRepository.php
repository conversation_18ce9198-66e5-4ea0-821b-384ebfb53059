<?php

namespace App\Repositories\Calendar;

use App\Models\Calendar\CalendarEvent;

class CalendarEventRepository
{
    /**
     * @param int $calendarId
     * @param int $userId
     * @param string $externalId
     * @param string $title
     * @param string $status
     * @param string|null $recurrenceRule
     * @param array|null $recurrenceData
     * @param string|null $startTime
     * @param string|null $conferenceUrl
     * @param string|null $description
     * @param string|null $endTime
     * @param string|null $timezone
     * @param string|null $location
     * @param array|null $payload
     * @return CalendarEvent
     */
    public function updateOrCreate(
        int $calendarId,
        int $userId,
        string $externalId,
        string $title,
        string $status,
        ?string $recurrenceRule = null,
        ?array $recurrenceData = [],
        ?string $startTime = null,
        ?string $conferenceUrl = null,
        ?string $description = null,
        ?string $endTime = null,
        ?string $timezone = null,
        ?string $location = null,
        ?array $payload = [],
    ): CalendarEvent
    {
        return CalendarEvent::query()
            ->updateOrCreate([
                CalendarEvent::FIELD_CALENDAR_ID => $calendarId,
                CalendarEvent::FIELD_USER_ID     => $userId,
                CalendarEvent::FIELD_EXTERNAL_ID => $externalId,
            ], [
                CalendarEvent::FIELD_TITLE           => $title,
                CalendarEvent::FIELD_PAYLOAD         => $payload,
                CalendarEvent::FIELD_DESCRIPTION     => $description,
                CalendarEvent::FIELD_LOCATION        => $location,
                CalendarEvent::FIELD_STATUS          => $status,
                CalendarEvent::FIELD_START_TIME      => $startTime,
                CalendarEvent::FIELD_END_TIME        => $endTime,
                CalendarEvent::FIELD_TIMEZONE        => $timezone,
                CalendarEvent::FIELD_CONFERENCE_URL  => $conferenceUrl,
                CalendarEvent::FIELD_RECURRENCE_RULE => $recurrenceRule,
                CalendarEvent::FIELD_RECURRENCE_DATA => json_encode($recurrenceData ?? []),
            ]);
    }

}
