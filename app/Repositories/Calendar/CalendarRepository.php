<?php

namespace App\Repositories\Calendar;

use App\Models\Calendar\Calendar;

class CalendarRepository
{
    const string DEFAULT_PROVIDER = 'google';

    /**
     * @param int $userId
     * @param string $externalId
     * @param string $name
     * @param string $timezone
     * @param string|null $syncToken
     * @return Calendar
     */
    public function updateOrCreate(
        int $userId,
        string $externalId,
        string $name,
        string $timezone,
        ?string $syncToken = null,
    ): Calendar
    {
        return Calendar::query()
            ->updateOrCreate([
                Calendar::FIELD_USER_ID     => $userId,
                Calendar::FIELD_EXTERNAL_ID => $externalId,
                Calendar::FIELD_PROVIDER    => self::DEFAULT_PROVIDER
            ], [
                Calendar::FIELD_NAME       => $name,
                Calendar::FIELD_TIMEZONE   => $timezone,
                Calendar::FIELD_SYNC_TOKEN => $syncToken,
            ]);
    }

}
