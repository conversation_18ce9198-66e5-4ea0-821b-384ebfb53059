<?php

namespace App\Repositories;

use App\Enums\LeadsReport\LeadsReportGroupEnum;
use App\Enums\LeadsReport\LeadsReportQueryEnum;
use App\Services\DatabaseHelperService;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;

class LeadsReportRepository
{

    /**
     * @param LeadsReportQueryEnum $queryType
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param LeadsReportGroupEnum $group
     * @param array $columns
     * @param mixed $filters
     * @param Collection|null $limitedLocations
     * @param int|null $companyId
     * @param array|null $campaigns
     * @return Builder
     * @throws Exception
     */
    public function getLeadReportQuery(
        LeadsReportQueryEnum $queryType,
        Carbon $startDate,
        Carbon $endDate,
        LeadsReportGroupEnum $group,
        array $columns,
        mixed $filters,
        ?Collection $limitedLocations,
        ?int $companyId,
        ?array $campaigns,
    ): Builder
    {
        return LeadsReportQueryEnum::getBuilder($queryType)
            ->forStartDate($startDate)
            ->forEndDate($endDate)
            ->forGroup($group)
            ->forColumns($columns)
            ->forLimitedLocations($limitedLocations)
            ->withFilters($filters)
            ->forCompany($companyId)
            ->forCampaigns($campaigns)
            ->fromDatabase(DatabaseHelperService::database())
            ->fromReadonlyDatabase(DatabaseHelperService::readOnlyDatabase())
            ->getQuery();
    }
}
