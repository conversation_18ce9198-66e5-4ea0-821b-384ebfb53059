<?php

namespace App\Repositories;

use App\DTO\FloorPricing\FloorPricingHistoryLog;
use App\Models\FloorPriceActivityLog;
use App\Models\Legacy\Location;
use App\Models\Odin\ProductCountyFloorPrice;
use App\Models\Odin\ProductStateFloorPrice;
use App\Models\SaleType;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class FloorPricingHistoryRepository
{
    public function __construct(
        protected FloorPriceActivityLogRepository $activityLogRepository,
    )
    {

    }

    /**
     * @param FloorPriceActivityLog $activityLog
     * @return FloorPricingHistoryLog
     */
    protected function transformLog(FloorPriceActivityLog $activityLog): FloorPricingHistoryLog
    {
        $relatedSubject = $activityLog->{FloorPriceActivityLog::RELATION_SUBJECT};

        $typeName = $relatedSubject instanceof ProductStateFloorPrice
            ? $relatedSubject->{ProductStateFloorPrice::RELATION_SALE_TYPE}->{SaleType::FIELD_NAME}
            : $relatedSubject->{ProductCountyFloorPrice::RELATION_SALE_TYPE}->{SaleType::FIELD_NAME};

        $referenceLocation = $relatedSubject instanceof ProductStateFloorPrice
            ? $relatedSubject->{ProductStateFloorPrice::RELATION_STATE_LOCATION}
            : $relatedSubject->{ProductCountyFloorPrice::RELATION_COUNTY_LOCATION};

        return new FloorPricingHistoryLog(
            causerId  : $activityLog->{FloorPriceActivityLog::FIELD_CAUSER_ID},
            causerName: $activityLog->{FloorPriceActivityLog::RELATION_CAUSER}->{User::FIELD_NAME},
            county    : $referenceLocation->{Location::COUNTY},
            countyKey : $referenceLocation->{Location::COUNTY_KEY},
            date      : $activityLog->{FloorPriceActivityLog::FIELD_CREATED_AT},
            priceFrom : Arr::get($activityLog->properties->get('old'), 'price'),
            priceTo   : Arr::get($activityLog->properties->get('attributes'), 'price'),
            saleType  : $typeName,
            state     : $referenceLocation->{Location::STATE},
            stateAbbr : $referenceLocation->{Location::STATE_ABBREVIATION},
            stateKey  : $referenceLocation->{Location::STATE_KEY},
        );
    }

    /**
     * @param int $serviceProductId
     * @param int $qualityTierId
     * @param int $propertyTypeId
     * @param int|null $stateLocationId
     * @return Collection<FloorPricingHistoryLog>
     */
    public function getFloorPriceHistoryLogs(
        int $serviceProductId,
        int $qualityTierId,
        int $propertyTypeId,
        ?int $stateLocationId = null,
    ): Collection
    {
        return $this->activityLogRepository->getFloorPriceChangeLogsQuery(
            stateLocationId : $stateLocationId,
            serviceProductId: $serviceProductId,
            qualityTierId   : $qualityTierId,
            propertyTypeId  : $propertyTypeId,
        )
            ->get()
            ->map(fn (FloorPriceActivityLog $activityLog) => $this->transformLog($activityLog))
            ->sortByDesc(fn(FloorPricingHistoryLog $item) => Carbon::parse($item->getDate()))
            ->values();
    }
}
