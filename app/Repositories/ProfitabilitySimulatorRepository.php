<?php

namespace App\Repositories;

use App\Enums\Odin\Industry;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\LeadProcessingHistory;
use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Legacy\LeadCategory;
use App\Models\Legacy\LeadPrice;
use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use App\Services\DatabaseHelperService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use JetBrains\PhpStorm\Deprecated;

class ProfitabilitySimulatorRepository
{
    /**
     * @param array $locationIds
     * @param string $industry
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return Collection<int, EloquentQuote>
     */
    public function getGoodToSellLeadsInZipCodes(array $locationIds, string $industry, Carbon $startDate, Carbon $endDate): Collection
    {
        $query = EloquentQuote::query()
            ->select([
                EloquentQuote::TABLE.'.'.EloquentQuote::QUOTE_ID,
                EloquentQuote::TABLE.'.'.EloquentQuote::TIMESTAMP_ADDED,
                EloquentQuote::TABLE.'.'.EloquentQuote::LEAD_TYPE_ID,
                Location::TABLE.'.'.Location::ID.' as location_id'
            ])
            ->join(
                EloquentAddress::TABLE,
                EloquentAddress::TABLE.'.'.EloquentAddress::ADDRESS_ID,
                EloquentQuote::TABLE.'.'.EloquentQuote::ADDRESS_ID)
            ->join(
                Location::TABLE,
                Location::TABLE.'.'.Location::ZIP_CODE,
                EloquentAddress::TABLE.'.'.EloquentAddress::ZIP_CODE
            )
            ->where(EloquentQuote::TABLE.'.'.EloquentQuote::TIMESTAMP_ADDED, '>=', $startDate->timestamp)
            ->where(EloquentQuote::TABLE.'.'.EloquentQuote::TIMESTAMP_ADDED, '<=', $endDate->timestamp)
            ->where(EloquentQuote::TABLE.'.'.EloquentQuote::CLASSIFICATION, EloquentQuote::CLASSIFICATION_VERIFIED)
            ->where(EloquentQuote::TABLE.'.'.EloquentQuote::STATUS, '!=', EloquentQuote::VALUE_STATUS_CANCELLED)
            ->where(EloquentQuote::TABLE.'.'.EloquentQuote::LEAD_CATEGORY_ID, LeadCategory::RESIDENTIAL)
            ->whereIntegerInRaw(Location::TABLE.'.'.Location::ID, $locationIds);

        if($industry === Industry::SOLAR->getSlug()) {
            $query->where(EloquentQuote::TABLE.'.'.EloquentQuote::SOLAR_LEAD, true);
        } else {
            $query->where(EloquentQuote::TABLE.'.'.EloquentQuote::ROOFING_LEAD, true);
        }

        return $query->orderBy(EloquentQuote::TABLE.'.'.EloquentQuote::TIMESTAMP_ADDED)->get();
    }

    /**
     * @param array $zipCodeLocationIds
     * @return array
     */
    public function getMappedStateAndCountyLocationIds(array $zipCodeLocationIds): array
    {
        $stateIds = Location::query()
            ->select(Location::TABLE .'.'. Location::ID . ' as zip_code_location_id', 'l2.id as state_location_id')
            ->join(Location::TABLE.' as l2', function ($join) {
                /** @var JoinClause $join */
                $join->on('l2.state_abbr',
                          '=',
                          Location::TABLE.'.'.Location::STATE_ABBREVIATION
                )->where('l2.type', Location::TYPE_STATE);
            })
            ->whereIntegerInRaw(Location::TABLE.'.'.Location::ID, $zipCodeLocationIds)
            ->get();

        $countyIds = Location::query()
            ->select(Location::TABLE .'.'. Location::ID . ' as zip_code_location_id', 'l2.id as county_location_id')
            ->join(Location::TABLE.' as l2', function ($join) {
                /** @var JoinClause $join */
                $join->on(
                    'l2.state_abbr',
                    Location::TABLE.'.'.Location::STATE_ABBREVIATION)
                    ->on(
                        'l2.county_key',
                        Location::TABLE.'.'.Location::COUNTY_KEY)
                    ->where('l2.type', Location::TYPE_COUNTY);
            })
            ->whereIntegerInRaw(Location::TABLE.'.'.Location::ID, $zipCodeLocationIds)
            ->get();

        $mappedIds = [];

        foreach($zipCodeLocationIds as $locationId){
            $mappedIds[$locationId] = [
                'state_location_id'  => $stateIds->where('zip_code_location_id', $locationId)->first()->state_location_id,
                'county_location_id' => $countyIds->where('zip_code_location_id', $locationId)->first()->county_location_id
            ];
        }

        return $mappedIds;
    }

    /**
     * @param array $locationIds
     * @return array
     */
    public function getStateAndCountyLocationIds(array $locationIds): array
    {
        $stateIds = Location::query()
            ->select('l2.id')
            ->join(Location::TABLE.' as l2', function ($join) {
                /** @var JoinClause $join */
                $join->on('l2.state_abbr',
                          '=',
                          Location::TABLE.'.'.Location::STATE_ABBREVIATION
                )->where('l2.type', Location::TYPE_STATE);
            })
            ->whereIntegerInRaw(Location::TABLE.'.'.Location::ID, $locationIds)
            ->distinct()
            ->pluck('l2.id')
            ->toArray();

        $countyIds = Location::query()
            ->select('l2.id')
            ->join(Location::TABLE.' as l2', function ($join) {
                /** @var JoinClause $join */
                $join->on(
                    'l2.state_abbr',
                    Location::TABLE.'.'.Location::STATE_ABBREVIATION)
                    ->on(
                        'l2.county_key',
                         Location::TABLE.'.'.Location::COUNTY_KEY)
                    ->where('l2.type', Location::TYPE_COUNTY);
            })
            ->whereIntegerInRaw(Location::TABLE.'.'.Location::ID, $locationIds)
            ->distinct()
            ->pluck('l2.id')
            ->toArray();

        return array_merge($stateIds, $countyIds);
    }

    /**
     * @param array $quoteIds
     */
    public function getSoldLegs(array $quoteIds)
    {
        $undersoldLeads = EloquentQuoteCompany::query()
            ->select([
                EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::QUOTE_COMPANY_ID,
                EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::QUOTE_ID,
                EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::COST,
                LeadCampaignSalesTypeConfiguration::TABLE.'.'.LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID,
                EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::COMPANY_ID
            ])
            ->join(
                LeadCampaignSalesTypeConfiguration::TABLE,
                LeadCampaignSalesTypeConfiguration::TABLE.'.'.LeadCampaignSalesTypeConfiguration::ID,
                EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION_ID
            )
            ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::CHARGEABLE, true)
            ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::DELIVERED, true)
            ->whereIntegerInRaw(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::QUOTE_ID, $quoteIds)
            ->get();

        return $undersoldLeads->mapToGroups(function ($item, $key) {
            return [$item[EloquentQuoteCompany::QUOTE_ID] => $item];
        });
    }

    /**
     * @param Collection<int, EloquentQuote> $soldLeads
     * @param array $locationIds
     * @return \Illuminate\Database\Eloquent\Collection|array
     */
    public function getCampaignAndCompanyPrices(Collection $soldLeads, array $locationIds): \Illuminate\Database\Eloquent\Collection|array
    {
        $campaignIds = $soldLeads->values()->flatten()->pluck(LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID)->unique()->values()->toArray();
        $companyIds = $soldLeads->values()->flatten()->pluck(EloquentQuoteCompany::COMPANY_ID)->unique()->values()->toArray();

        return LeadPrice::query()
            ->select([
                LeadPrice::TABLE.'.'.LeadPrice::LEAD_SALES_TYPE_ID,
                LeadPrice::TABLE.'.'.LeadPrice::LOCATION_ID,
                LeadPrice::TABLE.'.'.LeadPrice::PRICE,
                LeadPrice::TABLE.'.'.LeadPrice::PRICE_TYPE,
                LeadPrice::TABLE.'.'.LeadPrice::LEAD_CAMPAIGN_ID,
                Location::TABLE.'.'.Location::TYPE
            ])->join(
                Location::TABLE,
                Location::TABLE.'.'.Location::ID,
                LeadPrice::TABLE.'.'.LeadPrice::LOCATION_ID
            )
            ->whereIntegerInRaw(LeadPrice::LOCATION_ID, $locationIds)
            ->whereIntegerInRaw(LeadPrice::LEAD_CAMPAIGN_ID, $campaignIds)
            ->orWhere(function(Builder $query) use ($companyIds, $locationIds) {
                $query->whereIntegerInRaw(LeadPrice::COMPANY_ID, $companyIds)
                    ->whereIntegerInRaw(LeadPrice::LOCATION_ID, $locationIds);
            })->get();
    }

    /**
     * @param array $zipCodeLocationIds
     * @param array $industryServiceIds
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param bool $goodToSell
     * @return Collection<ConsumerProduct>
     */
    public function getConsumerProductsInZipCodes(array $zipCodeLocationIds, array $industryServiceIds, Carbon $startDate, Carbon $endDate, bool $goodToSell = false): Collection
    {
        $query = ConsumerProduct::query()
            ->select([
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID,
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_CONSUMER_ID,
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_GOOD_TO_SELL,
                Consumer::TABLE . '.' . Consumer::FIELD_ID . ' as consumer_id',
                Consumer::TABLE . '.' . Consumer::FIELD_LEGACY_ID,
                Consumer::TABLE . '.' . Consumer::FIELD_MAX_CONTACT_REQUESTS,
                Consumer::TABLE . '.' . Consumer::CREATED_AT . ' as consumer_created_at',
                EloquentQuote::TABLE . '.' . EloquentQuote::SOLAR_LEAD,
                EloquentQuote::TABLE . '.' . EloquentQuote::ELECTRIC_COST,
                EloquentQuote::TABLE . '.' . EloquentQuote::BEST_TIME_TO_CALL,
                EloquentQuote::TABLE . '.' . EloquentQuote::BEST_TIME_TO_CALL_OTHER,
                ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_INDUSTRY_SERVICE_ID,
                Location::TABLE . '.' . Location::ID . ' as location_id',
                Address::TABLE . '.' . Address::FIELD_UTC . ' as utc',
                LeadProcessingHistory::TABLE . '.' . LeadProcessingHistory::CREATED_AT . ' as marked_good_to_sell_at',
                DB::raw(
                    "group_concat(concat(product_assignments.company_id, ',', product_assignments.cost, ',', budgets.key, ',', budget_containers.company_campaign_id) separator ';')  as product_assignments"
                ),
            ])
            ->join(ServiceProduct::TABLE, ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_SERVICE_PRODUCT_ID, ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_ID)
            ->join(Address::TABLE, ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ADDRESS_ID, Address::TABLE . '.' . Address::FIELD_ID)
            ->join(Location::TABLE, Address::TABLE . '.' . Address::FIELD_ZIP_CODE, Location::TABLE . '.' . Location::ZIP_CODE)
            ->join(LeadProcessingHistory::TABLE, function ($query) {
                $query->on(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID, '=', LeadProcessingHistory::TABLE . '.' . LeadProcessingHistory::FIELD_CONSUMER_PRODUCT_ID)
                    ->where(LeadProcessingHistory::TABLE . '.' . LeadProcessingHistory::FIELD_ACTION, LeadProcessingHistory::ACTION_ALLOCATED);
            })
            ->join(Consumer::TABLE, ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_CONSUMER_ID, Consumer::TABLE . '.' . ConsumerProduct::FIELD_ID)
            ->join(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentQuote::TABLE, Consumer::TABLE . '.' . Consumer::FIELD_LEGACY_ID, EloquentQuote::TABLE . '.' . EloquentQuote::ID)
            ->leftJoin(ProductAssignment::TABLE, ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID)
            ->join(Budget::TABLE, Budget::TABLE . '.' . Budget::FIELD_ID, ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_BUDGET_ID)
            ->join(BudgetContainer::TABLE, BudgetContainer::TABLE . '.' . BudgetContainer::FIELD_ID, Budget::TABLE . '.' . Budget::FIELD_BUDGET_CONTAINER_ID)
            ->whereIntegerInRaw(ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $industryServiceIds)
            ->whereIntegerInRaw(Location::TABLE . '.' . Location::ID, $zipCodeLocationIds)
            ->whereBetween(ConsumerProduct::TABLE . '.' . Model::CREATED_AT, [$startDate, $endDate])
            ->whereNot(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_STATUS, ConsumerProduct::STATUS_CANCELLED)
            ->groupBy(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID);

        if ($goodToSell) {
            $query->where(ConsumerProduct::FIELD_GOOD_TO_SELL, true);
        }

        return $query->get()->each(fn(ConsumerProduct $product) => $product->product_assignments = $this->getProductAssignmentsArray($product->product_assignments));
    }

    /**
     * @param string $productAssignments
     * @return array
     */
    private function getProductAssignmentsArray(string $productAssignments): array
    {
        return array_map(function ($assignment) {
            $arr = explode(',', $assignment);
            return [
                'company_id' => $arr[0],
                'cost' => $arr[1],
                'budget_key' => $arr[2],
                'campaign_id' => $arr[3],
            ];
        }, explode(';', $productAssignments));
    }
}
