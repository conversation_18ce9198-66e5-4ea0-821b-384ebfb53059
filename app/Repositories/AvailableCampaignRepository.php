<?php

namespace App\Repositories;

use App\Builders\SalesBaits\AvailableCampaignBuilder;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\LeadCampaign;
use App\Repositories\Legacy\LegacyUserRepository;
use App\Services\PubSub\ContactSubscriptionService;
use Illuminate\Support\Collection;

class AvailableCampaignRepository
{
    const DEFAULT_CAMPAIGN_LIMIT = 5;

    public function __construct(
        protected ContactSubscriptionService $subscriptionService,
        protected LegacyUserRepository $userRepository,
        protected CompanyContactRepository $contactRepository
    ) {}

    /**
     * @param EloquentQuote $lead
     * @param int|null $limit
     * @return Collection<int, LeadCampaign>
     */
    public function getCampaignsByLead(EloquentQuote $lead, ?int $limit = self::DEFAULT_CAMPAIGN_LIMIT): Collection
    {
        $companyCampaigns =  AvailableCampaignBuilder::query($lead)->get();

        $campaigns = collect();
        foreach ($companyCampaigns as $companyCampaign){
            if($limit && $campaigns->count() >= $limit){break;}

            $company = $companyCampaign->company;
            $users = $this->userRepository->getActiveUsersByCompanyId($company->companyid, false, true);
            $userSubscriptions = $this->subscriptionService->getStatusesForUsers($users);
            $validCampaign = null;
            foreach ($users as $user){
                if($userSubscriptions[$user->userid]->emailSubscribed){
                    $validCampaign = $companyCampaign->campaigns->first();
                    break;
                }
            }
            if($validCampaign){
                $campaigns->push($validCampaign);
                continue;
            }
            foreach ($companyCampaign->campaigns as $campaign){
                $contacts = $this->contactRepository->getActiveContactsByCampaignId($campaign->id, false, true);
                $contactSubscriptions = $this->subscriptionService->getStatusesForContacts($contacts);
                foreach ($contacts as $contact){
                    if($contactSubscriptions[$contact->contactid]->emailSubscribed){
                        $validCampaign = $campaign;
                        break;
                    }
                }
                if($validCampaign){break;}
            }
            if($validCampaign){$campaigns->push($validCampaign);}
        }

        return $campaigns;
    }
}
