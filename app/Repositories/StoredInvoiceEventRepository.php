<?php

namespace App\Repositories;

use Illuminate\Support\Collection;
use Spatie\EventSourcing\StoredEvents\Models\EloquentStoredEvent;

class StoredInvoiceEventRepository
{
    const string FIELD_AGGREGATE_UUID    = 'aggregate_uuid';
    const string FIELD_ID                = 'id';
    const string FIELD_AGGREGATE_VERSION = 'aggregate_version';
    const string FIELD_EVENT_VERSION     = 'event_version';
    const string FIELD_EVENT_CLASS       = 'event_class';
    const string FIELD_EVENT_PROPERTIES  = 'event_properties';
    const string FIELD_META_DATA         = 'meta_data';
    const string FIELD_CREATED_AT        = 'created_at';

    /**
     * @param string $invoiceUuid
     * @return Collection
     */
    public function getInvoiceEvents(string $invoiceUuid): Collection
    {
        /** @var Collection  */
        return EloquentStoredEvent::query()
            ->where(self::FIELD_AGGREGATE_UUID, $invoiceUuid)
            ->orderBy(self::FIELD_AGGREGATE_VERSION)
            ->get();
    }

}
