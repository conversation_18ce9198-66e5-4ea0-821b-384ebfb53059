<?php

namespace App\Repositories\LeadProcessing;

use App\Models\LeadProcessorNotification;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Builder;

class LeadProcessorNotificationRepository
{

    /**
     * @param array $leadProcessorIds
     * @param string $subject
     * @param string $message
     * @param int|null $leadId
     * @return LeadProcessorNotification[]|Collection
     */
    public function createNotifications(array $leadProcessorIds, string $subject, string $message, ?int $leadId): Collection
    {
        $notifications = collect();
        foreach ($leadProcessorIds as $processorId){
            $notifications->push(LeadProcessorNotification::create([
                LeadProcessorNotification::FIELD_LEAD_PROCESSOR_ID => $processorId,
                LeadProcessorNotification::FIELD_LEAD_ID => $leadId,
                LeadProcessorNotification::FIELD_SUBJECT => $subject,
                LeadProcessorNotification::FIELD_BODY => $message,
                LeadProcessorNotification::FIELD_READ => LeadProcessorNotification::UNREAD,
                LeadProcessorNotification::CREATED_AT => Carbon::now('UTC'),
                LeadProcessorNotification::UPDATED_AT => Carbon::now('UTC')
            ]));
        }
        return $notifications;
    }

    /**
     * @param int $leadProcessorId
     * @param int $inLastHours
     * @return LeadProcessorNotification[]|Collection
     */
    public function getProcessorNotifications(int $leadProcessorId, int $inLastHours): Collection
    {
        $query = LeadProcessorNotification::query()
            ->where(LeadProcessorNotification::FIELD_LEAD_PROCESSOR_ID, $leadProcessorId)
            ->where(LeadProcessorNotification::CREATED_AT, '>=', Carbon::now()->subHours($inLastHours));

        return $query->orderBy(LeadProcessorNotification::CREATED_AT, 'DESC')->get();
    }

    /**
     * @param int $leadProcessorId
     * @return Builder
     */
    public function getUnreadProcessorNotificationsQuery(int $leadProcessorId): Builder
    {
        return LeadProcessorNotification::query()
            ->where(LeadProcessorNotification::FIELD_LEAD_PROCESSOR_ID, $leadProcessorId)
            ->where(LeadProcessorNotification::FIELD_READ, LeadProcessorNotification::READ);
    }

    /**
     * @param int $id
     * @return LeadProcessorNotification|null
     */
    public function getNotification(int $id): ?LeadProcessorNotification
    {
        /** @var LeadProcessorNotification|null $notification */
        $notification = LeadProcessorNotification::query()->find($id);

        return $notification;
    }

    /**
     * Marks notification as read.
     *
     * @param LeadProcessorNotification $notification
     * @return bool
     */
    public function markAsRead(LeadProcessorNotification $notification): bool
    {
        $notification->read = true;
        $notification->save();

        return true;
    }
}
