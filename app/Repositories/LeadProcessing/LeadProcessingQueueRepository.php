<?php

namespace App\Repositories\LeadProcessing;

use App\Events\ConsumerProcessing\ConsumerProductApprovedEvent;
use App\Events\ConsumerProcessing\ConsumerProductCancelledEvent;
use App\Events\ConsumerProcessing\ConsumerProductMarkedAsPendingReviewEvent;
use App\Events\ConsumerProcessing\ConsumerProductMarkedAsUnderReviewEvent;
use App\Events\LeadProcessing\LeadCancelledEvent;
use App\Events\LeadProcessing\LeadReleasedEvent;
use App\Jobs\RecordMonitoringLog;
use App\Models\LeadProcessingAllocation;
use App\Models\LeadProcessingBudgetStatus;
use App\Models\LeadProcessingConfiguration;
use App\Models\LeadProcessingHistory;
use App\Models\LeadProcessingInitial;
use App\Models\LeadProcessingPendingReview;
use App\Models\LeadProcessingQueueConfiguration;
use App\Models\LeadProcessingQueueConstraintsBucketFlags;
use App\Models\LeadProcessingReservedLead;
use App\Models\LeadProcessingTeam;
use App\Models\LeadProcessingUnderReview;
use App\Models\LeadProcessor;
use App\Models\Legacy\EloquentQuote;
use App\Models\Odin\ConsumerProduct;
use App\Repositories\Legacy\LeadProcessingRepository;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Repositories\Odin\ProductProcessing\ProductProcessingRepository;
use App\Services\ConsumerProductLifecycleTrackingService;
use App\Services\DatabaseHelperService;
use App\Services\LeadProcessing\LeadCommunicationService;
use App\Services\Legacy\LeadAggregatorsService;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use function Sentry\captureException;

class LeadProcessingQueueRepository
{
    const STATUS_INITIAL        = 'initial';
    const STATUS_PENDING_REVIEW = 'pending_review';
    const STATUS_UNDER_REVIEW   = 'under_review';
    const STATUS_ALLOCATED      = 'allocated';
    const STATUS_NO_COMPANIES   = 'no_companies';
    const STATUS_CANCELLED      = 'cancelled';

    /**
     * The opening and closing hours for when a processor can call a lead.
     * Matt has stated that this value does not need to be configurable, and for the foreseeable future
     * there's no cases in which we should call a lead outside these hours.
     */
    const CALL_OPENING_HOUR = 9;
    const CALL_CLOSING_HOUR = 20;

    const STATUS_MAPPINGS = [
        self::STATUS_INITIAL        => EloquentQuote::VALUE_STATUS_INITIAL,
        self::STATUS_PENDING_REVIEW => EloquentQuote::VALUE_STATUS_INITIAL,
        self::STATUS_UNDER_REVIEW   => EloquentQuote::VALUE_STATUS_UNDER_REVIEW,
        self::STATUS_ALLOCATED      => EloquentQuote::VALUE_STATUS_ALLOCATED,
        self::STATUS_NO_COMPANIES   => EloquentQuote::VALUE_STATUS_NO_COMPANIES,
        self::STATUS_CANCELLED      => EloquentQuote::VALUE_STATUS_CANCELLED
    ];

    const PUBLIC_COMMENT_QUEUES = [
        LeadProcessingQueueConfiguration::STATUS_PENDING_REVIEW,
        LeadProcessingQueueConfiguration::STATUS_UNDER_REVIEW
    ];

    const INDUSTRY_SOLAR   = 'solar';
    const INDUSTRY_ROOFING = 'roofing';

    /**
     * When searching for a lead of a particular status, how many days back to look
     *
     * @var array
     */
    const MAX_DAYS_DISPLAY = [
        self::STATUS_INITIAL        => 1,
        self::STATUS_PENDING_REVIEW => 2,
        self::STATUS_UNDER_REVIEW   => 30,
        self::STATUS_ALLOCATED      => 1,
        self::STATUS_NO_COMPANIES   => 1,
        self::STATUS_CANCELLED      => 1
    ];

    /**
     * If the amount of leads for a given queue is below this buffer,
     * then the queue will take into account the next largest
     * queue to assist with clearing old leads.
     */
    const HELP_OTHER_TIMEZONE_LEAD_COUNT_BUFFER = 100;
    /**
     * Statuses that aren't an actual lead status and require alternate logic.
     */
    const QUEUE_ALTERNATE_STATUSES = [
        self::STATUS_PENDING_REVIEW
    ];

    /** @var LeadProcessingRepository $leadProcessingRepository */
    protected LeadProcessingRepository $leadProcessingRepository;

    /** @var LeadProcessingQueueConstraintsRepository $leadProcessingQueueConstraintsRepository */
    protected LeadProcessingQueueConstraintsRepository $leadProcessingQueueConstraintsRepository;

    /** @var Dispatcher $dispatcher */
    protected Dispatcher $dispatcher;

    /** @var ConsumerProductRepository $consumerProductRepository */
    protected ConsumerProductRepository $consumerProductRepository;

    /** @var bool $shouldLog */
    private bool $shouldLog;

    /**
     * @param LeadProcessingRepository $leadProcessingRepository
     * @param LeadProcessingQueueConstraintsRepository $leadProcessingQueueConstraintsRepository
     * @param Dispatcher $dispatcher
     * @param ConsumerProductRepository $consumerProductRepository
     */
    public function __construct(
        LeadProcessingRepository                 $leadProcessingRepository,
        LeadProcessingQueueConstraintsRepository $leadProcessingQueueConstraintsRepository,
        Dispatcher                               $dispatcher,
        ConsumerProductRepository                $consumerProductRepository
    )
    {
        $this->dispatcher                               = $dispatcher;
        $this->leadProcessingRepository                 = $leadProcessingRepository;
        $this->leadProcessingQueueConstraintsRepository = $leadProcessingQueueConstraintsRepository;
        $this->consumerProductRepository                = $consumerProductRepository;
        $this->shouldLog                                = true;
    }

    /**
     * Handles retrieving the next lead in the queue.
     *
     * @return EloquentQuote|Model
     * @throws Exception
     */
    public function getNextLead(LeadProcessor $processor, ?int $previousLeadId = null): ?EloquentQuote
    {
        if ($processor->lead_processing_team_id === LeadProcessingTeam::NO_TEAM_ASSIGNED_ID) {
            return null;
        }

        $queue            = $this->getQueueForProcessor($processor);
        /** @var LeadProcessingTeam $team */
        $team             = $processor->{LeadProcessor::RELATION_LEAD_PROCESSING_TEAM};
        $priorityTimezone = $this->getPriorityTimezone($queue, $this->getTimezoneForProcessor($processor), strtolower($processor->team->industry->name), $previousLeadId);
        $timezones        = $this->getTimezonesForQueue($queue, $processor, $previousLeadId);

        $primaryTimezoneQueueQuery = $this->getQueryForQueue(
            $queue,
            true,
            $timezones,
            0,
            strtolower($team->industry->name),
            false,
            true,
            $previousLeadId
        );

        if ($this->hasLockedLead($queue, $team, $processor, $previousLeadId)) {
            $nextLeadQuery = $this->getQueryForQueue(
                $queue,
                false,
                $timezones,
                $processor->id,
                strtolower($team->industry->name),
                false,
                true,
                $previousLeadId
            );
        } else if ($this->getCountForQueueQuery($primaryTimezoneQueueQuery) > 0) {
            $nextLeadQuery = $primaryTimezoneQueueQuery;
        } else {
            $nextLeadQuery = $this->getQueryForQueue(
                $queue,
                $priorityTimezone !== null,
                [$priorityTimezone ?? -5],
                0,
                strtolower($team->industry->name),
                false,
                true,
                $previousLeadId
            );
        }

        return $this->getResultOfNextLeadQuery($nextLeadQuery, $queue);
    }

    /**
     * @param Builder $query
     * @param LeadProcessingQueueConfiguration $queue
     * @return Builder|Model|mixed|object|null
     */
    protected function getResultOfNextLeadQuery(Builder $query, LeadProcessingQueueConfiguration $queue): mixed
    {
        if($queue->primary_status === LeadProcessingQueueConfiguration::STATUS_UNDER_REVIEW && $query->count() <= 50) {
            return $query->get()->last();
        }

        return $query->first();
    }

    /**
     * @param LeadProcessingQueueConfiguration $queue
     * @param LeadProcessor $processor
     * @param int|null $excludeLeadId
     *
     * @return array
     * @throws Exception
     */
    protected function getTimezonesForQueue(LeadProcessingQueueConfiguration $queue, LeadProcessor $processor, ?int $excludeLeadId = null): array
    {
        $leadProcessorTimezone = $this->getTimezoneForProcessor($processor);
        $priorityTimezone      = $this->getPriorityTimezone($queue, $leadProcessorTimezone, strtolower($processor->team->industry->name));

        if ($priorityTimezone !== null
            && $this->getCountForQueueQuery(
                $this->getQueryForQueue(
                    $queue,
                    true,
                    [$leadProcessorTimezone],
                    0,
                    strtolower($processor->team->industry->name),
                    false,
                    true,
                    $excludeLeadId
                )
            ) < self::HELP_OTHER_TIMEZONE_LEAD_COUNT_BUFFER
        ) {
            return [$leadProcessorTimezone, $priorityTimezone];
        }

        return [$leadProcessorTimezone];
    }

    /**
     * Gets the timezone for a given lead processor's team.
     *
     * @param LeadProcessor $processor
     * @return int
     */
    protected function getTimezoneForProcessor(LeadProcessor $processor): int
    {
        return $this->isActiveDST() ? $processor->team->primary_utc_offset + 1 : $processor->team->primary_utc_offset;
    }

    /**
     * @return bool
     */
    public function isActiveDST(): bool
    {
        $currentTimezone    = date_default_timezone_get();
        $timezoneToCheckDST = 'America/Los_Angeles';

        date_default_timezone_set($timezoneToCheckDST);
        $isActiveDST = date("I");
        date_default_timezone_set($currentTimezone);

        return $isActiveDST;
    }

    /**
     * Handles flipping the last value of the queue.
     *
     * @param LeadProcessingQueueConfiguration $queue
     * @return bool
     */
    public function flipQueueState(LeadProcessingQueueConfiguration $queue): bool
    {
        $queue->last_round = !$queue->last_round;
        $queue->save();

        return true;
    }

    /**
     * Handles returning the queue for a given lead processor.
     *
     * @param LeadProcessor $processor
     * @return LeadProcessingQueueConfiguration
     */
    protected function getQueueForProcessor(LeadProcessor $processor): LeadProcessingQueueConfiguration
    {
        return $processor->team->primaryQueue;
    }

    /**
     * Handles retrieving the base query for getting leads, based on a given queue configuration.
     *
     * @param LeadProcessingQueueConfiguration|Model $queue
     * @param bool $restrictTimezone
     * @param array $utcOffset
     * @param int $lockedBy
     * @param string $industry
     * @param bool $ignoreLocked
     * @param bool $addConstraints
     * @param int|null $excludeLeadId
     *
     * @return Builder
     * @throws Exception
     */
    protected function getQueryForQueue(
        LeadProcessingQueueConfiguration|Model $queue,
        bool                             $restrictTimezone = true,
        array                            $utcOffset = [-5],
        int                              $lockedBy = 0,
        string                           $industry = self::INDUSTRY_SOLAR,
        bool                             $ignoreLocked = false,
        bool                             $addConstraints = true,
        ?int                             $excludeLeadId = null
    ): Builder
    {
        $query = EloquentQuote::query();
        $query = $this->addQueueRestrictionsToQuery($query, $queue->primary_status);

        if ($addConstraints) {
            $query = $this->leadProcessingQueueConstraintsRepository->addQueueConstraints(
                $query,
                $queue->{LeadProcessingQueueConfiguration::FIELD_ID},
                $industry,
                (bool)$queue->last_round
            );
        }

        if (!$ignoreLocked) {
            if($lockedBy === 0) {
                $query->whereNotExists(function($query) {
                    $query->select(DB::raw(1))->from(DatabaseHelperService::database().'.'.LeadProcessingReservedLead::TABLE)
                        ->whereColumn(
                            DatabaseHelperService::database().'.'.LeadProcessingReservedLead::TABLE.'.'.LeadProcessingReservedLead::FIELD_LEAD_ID,
                            DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuote::TABLE.'.'.EloquentQuote::ID
                        );
                })->where(EloquentQuote::LOCKED_BY_USER_ID, 0); // Legacy check in case it has manually been locked.
            } else {
                $query->whereExists(function($query) use ($lockedBy) {
                    $query->select(DB::raw(1))->from(DatabaseHelperService::database().'.'.LeadProcessingReservedLead::TABLE)
                        ->whereColumn(
                            DatabaseHelperService::database().'.'.LeadProcessingReservedLead::TABLE.'.'.LeadProcessingReservedLead::FIELD_LEAD_ID,
                            DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuote::TABLE.'.'.EloquentQuote::ID
                        )->where(
                            DatabaseHelperService::database().'.'.LeadProcessingReservedLead::TABLE.'.'.LeadProcessingReservedLead::FIELD_PROCESSOR_ID,
                            $lockedBy
                        );
                });
            }
        }

        if ($restrictTimezone) {
            $query->whereIn(EloquentQuote::TABLE . '.' . EloquentQuote::UTC, $utcOffset);
        }

        if($excludeLeadId > 0) {
            $query->where(EloquentQuote::TABLE.'.'.EloquentQuote::QUOTE_ID, '!=', $excludeLeadId);
        }

        switch ($industry) {
            case self::INDUSTRY_ROOFING:
                $query->where(EloquentQuote::TABLE . '.' . EloquentQuote::ROOFING_LEAD, true);
                break;

            case self::INDUSTRY_SOLAR:
            default:
                $query->where(EloquentQuote::TABLE . '.' . EloquentQuote::SOLAR_LEAD, true);
                break;
        }

        $query->with([
            EloquentQuote::RELATION_ADDRESS,
            EloquentQuote::RELATION_LEAD_CATEGORY,
            EloquentQuote::RELATION_QUOTE_EVENTS
        ]);

        $query->groupBy(EloquentQuote::TABLE . '.' . EloquentQuote::ID);

        return $query;
    }

    /**
     * @param Builder $query
     * @param string $primaryStatus
     * @return Builder
     */
    protected function addQueueRestrictionsToQuery(Builder $query, string $primaryStatus): Builder
    {
        return match ($primaryStatus) {
            self::STATUS_INITIAL => $this->addInitialQueueRestrictions($query),
            self::STATUS_PENDING_REVIEW => $this->addPendingReviewQueueRestrictions($query),
            self::STATUS_UNDER_REVIEW => $this->addUnderReviewQueueRestrictions($query),
            default => $query,
        };
    }

    /**
     * Handles adding restrictions for the initial queue.
     *
     * @param Builder $query
     * @return Builder
     */
    protected function addInitialQueueRestrictions(Builder $query): Builder
    {
        return $query
            ->join(DatabaseHelperService::database().'.'.LeadProcessingInitial::TABLE, function ($query) {
                $query->on(DatabaseHelperService::database().'.'.LeadProcessingInitial::TABLE . '.' . LeadProcessingInitial::FIELD_LEAD_ID, '=', DatabaseHelperService::readOnlyDatabase() . '.' . EloquentQuote::TABLE . '.' . EloquentQuote::ID);
            })
            ->where(EloquentQuote::STATUS, self::STATUS_MAPPINGS[self::STATUS_INITIAL])
            ->where(EloquentQuote::CLASSIFICATION, '<>', EloquentQuote::CLASSIFICATION_EMAIL_ONLY)
            ->whereBetween(EloquentQuote::TABLE . '.' . EloquentQuote::TIMESTAMP_ADDED, [
                Carbon::now()->subDays(self::MAX_DAYS_DISPLAY[self::STATUS_INITIAL])->timestamp,
                Carbon::now()->timestamp - ($this->leadProcessingRepository->getLeadProcessingConfiguration()[LeadProcessingConfiguration::FIELD_LEAD_PROCESSABLE_DELAY_SECONDS] ?? LeadProcessingConfiguration::LEAD_PROCESSABLE_DELAY_DEFAULT)
            ]);
    }

    /**
     * Handles adding restrictions for the under review queue.
     *
     * @param Builder $query
     * @return Builder
     */
    protected function addUnderReviewQueueRestrictions(Builder $query): Builder
    {
        return $query
            ->join(
                DatabaseHelperService::database().'.'.LeadProcessingUnderReview::TABLE,
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuote::TABLE . '.' . EloquentQuote::ID,
                '=',
                DatabaseHelperService::database().'.'.LeadProcessingUnderReview::TABLE . '.' . LeadProcessingUnderReview::FIELD_LEAD_ID
            )
            ->where(EloquentQuote::TABLE . '.' . EloquentQuote::STATUS, self::STATUS_MAPPINGS[self::STATUS_UNDER_REVIEW])
            ->where(EloquentQuote::TABLE . '.' . EloquentQuote::TIMESTAMP_ADDED, '>=', Carbon::now()->subDays(self::MAX_DAYS_DISPLAY[self::STATUS_UNDER_REVIEW])->timestamp);
    }

    /**
     * Adds the pending review queue restrictions needed.
     *
     * @param Builder $query
     *
     * @return Builder
     */
    protected function addPendingReviewQueueRestrictions(Builder $query): Builder
    {
        return $query
            ->join(DatabaseHelperService::database().'.'.LeadProcessingPendingReview::TABLE, function ($query) {
                $query->on(DatabaseHelperService::database().'.'.LeadProcessingPendingReview::TABLE . '.' . LeadProcessingPendingReview::FIELD_LEAD_ID, '=', DatabaseHelperService::readOnlyDatabase() . '.' . EloquentQuote::TABLE . '.' . EloquentQuote::ID);
            })
            ->where(EloquentQuote::TABLE . '.' . EloquentQuote::CLASSIFICATION, '<>', EloquentQuote::CLASSIFICATION_EMAIL_ONLY)
            ->where(EloquentQuote::TABLE . '.' . EloquentQuote::STATUS, self::STATUS_MAPPINGS[self::STATUS_PENDING_REVIEW])
            ->where(EloquentQuote::TABLE . '.' . EloquentQuote::TIMESTAMP_ADDED, '>=', Carbon::now()->subDays(self::MAX_DAYS_DISPLAY[self::STATUS_PENDING_REVIEW])->timestamp);
    }

    /**
     * Returns the count for a given query.
     *
     * @param Builder|\Illuminate\Database\Query\Builder $query
     * @return int
     */
    protected function getCountForQueueQuery(Builder|\Illuminate\Database\Query\Builder $query): int
    {
        return $query->selectRaw('COUNT(*) as count')->get()->count();
    }

    /**
     * @param string $industry
     * @return int
     * @throws Exception
     */
    public function getInitialLeadCount(string $industry = self::INDUSTRY_SOLAR): int
    {
        return $this->getCountForQueueQuery(
            $this->getQueryForQueue(
                LeadProcessingQueueConfiguration::query()->where(LeadProcessingQueueConfiguration::FIELD_PRIMARY_STATUS, self::STATUS_INITIAL)->first(),
                false,
                [-5],
                0,
                $industry,
                true
            )
        );
    }

    public function getPendingReviewCount(string $industry = self::INDUSTRY_SOLAR): int
    {
        return $this->getCountForQueueQuery(
            $this->getQueryForQueue(
                LeadProcessingQueueConfiguration::query()->where(LeadProcessingQueueConfiguration::FIELD_PRIMARY_STATUS, self::STATUS_PENDING_REVIEW)->first(),
                false,
                [-5],
                0,
                $industry,
                true
            )
        );
    }

    /**
     * @param string $industry
     * @return int
     * @throws Exception
     */
    public function getUnderReviewCount(string $industry = self::INDUSTRY_SOLAR): int
    {
        $query = $this->getQueryForQueue(
            LeadProcessingQueueConfiguration::query()->where(LeadProcessingQueueConfiguration::FIELD_PRIMARY_STATUS, self::STATUS_UNDER_REVIEW)->first(),
            false,
            [-5],
            0,
            $industry,
            true
        )->getQuery();

        $query->limit = null;

        return $this->getCountForQueueQuery($query);
    }

    /**
     * @return int
     */
    public function getAllocatedCount(): int
    {
        $count = EloquentQuote::query()
            ->selectRaw('COUNT(*) as count')
            ->where(EloquentQuote::TIMESTAMP_ADDED, '>=', Carbon::now()->subDays(self::MAX_DAYS_DISPLAY[self::STATUS_ALLOCATED])->timestamp)
            ->where(EloquentQuote::STATUS, self::STATUS_MAPPINGS[self::STATUS_ALLOCATED])
            ->first();

        return $count ? $count->count : 0;
    }

    /**
     * @return int
     */
    public function getCancelledCount(): int
    {
        $count = EloquentQuote::query()
            ->selectRaw('COUNT(*) as count')
            ->where(EloquentQuote::TIMESTAMP_ADDED, '>=', Carbon::now()->subDays(self::MAX_DAYS_DISPLAY[self::STATUS_CANCELLED])->timestamp)
            ->where(EloquentQuote::STATUS, self::STATUS_MAPPINGS[self::STATUS_CANCELLED])
            ->first();

        return $count ? $count->count : 0;
    }

    /**
     * @return int
     */
    public function getNoCompaniesCount(): int
    {
        $count = EloquentQuote::query()
            ->selectRaw('COUNT(*) as count')
            ->where(EloquentQuote::TIMESTAMP_ADDED, '>=', Carbon::now()->subDays(self::MAX_DAYS_DISPLAY[self::STATUS_NO_COMPANIES])->timestamp)
            ->where(EloquentQuote::STATUS, self::STATUS_MAPPINGS[self::STATUS_NO_COMPANIES])
            ->first();

        return $count ? $count->count : 0;
    }

    /**
     * Verifies if a processor has a locked lead or not.
     *
     * @param LeadProcessingQueueConfiguration $queue
     * @param LeadProcessingTeam $team
     * @param LeadProcessor $processor
     * @param int|null $excludeLeadId
     * @return bool
     * @throws Exception
     */
    protected function hasLockedLead(
        LeadProcessingQueueConfiguration $queue,
        LeadProcessingTeam               $team,
        LeadProcessor                    $processor,
        ?int                             $excludeLeadId
    ): bool
    {
        return $this->getCountForQueueQuery(
            $this->getQueryForQueue(
                $queue,
                false,
                [$this->getTimezoneForProcessor($processor)],
                $processor->id,
                strtolower($team->industry->name),
                false,
                true,
                $excludeLeadId
            )
        ) > 0;
    }

    /**
     * Handles retrieving the timezone with the highest priority.
     * @param LeadProcessingQueueConfiguration $queue
     * @param int|null $ignoreTimezone
     * @param string $industry
     * @param int|null $excludeLeadId
     * @return int|null
     * @throws Exception
     */
    protected function getPriorityTimezone(
        LeadProcessingQueueConfiguration $queue,
        ?int                             $ignoreTimezone = null,
        string                           $industry = self::INDUSTRY_SOLAR,
        ?int                             $excludeLeadId = null
    ): ?int
    {
        $query = $this->getQueryForQueue($queue, false, [], 0, $industry, false, true, $excludeLeadId);
        $query = $query->selectRaw('COUNT(*) as `count`, ' . EloquentQuote::TABLE . '.' . EloquentQuote::UTC)
            ->groupBy(EloquentQuote::TABLE . '.' . EloquentQuote::UTC);

        if ($ignoreTimezone !== null) {
            $query->where(EloquentQuote::TABLE . '.' . EloquentQuote::UTC, '<>', $ignoreTimezone);
        }

        $result = $query->orderBy('count', 'DESC')->first();

        return $result ? $result->utc : null;
    }

    /**
     * @param EloquentQuote $lead
     * @param LeadProcessor $processor
     * @param $reason
     * @param $deliverAt
     * @param int|null $consumerProductId
     * @return LeadProcessingAllocation
     */
    public function addLeadToAllocationQueue(
        EloquentQuote $lead,
        LeadProcessor $processor,
                      $reason,
                      $deliverAt,
        ?int          $consumerProductId = null
    ): LeadProcessingAllocation
    {
        return LeadProcessingAllocation::updateOrCreate(
            [LeadProcessingAllocation::FIELD_LEAD_ID => $lead->{EloquentQuote::ID}],
            [
                LeadProcessingAllocation::FIELD_LEAD_PROCESSOR_ID      => $processor->{LeadProcessor::FIELD_ID},
                LeadProcessingAllocation::FIELD_PROCESSING_SCENARIO    => $reason,
                LeadProcessingAllocation::FIELD_DELIVER_AT             => $deliverAt,
                LeadProcessingAllocation::FIELD_DELIVERED              => LeadProcessingAllocation::NOT_DELIVERED,
                LeadProcessingAllocation::FIELD_QUEUE_CONFIGURATION_ID => $processor->{LeadProcessor::RELATION_LEAD_PROCESSING_TEAM}->{LeadProcessingTeam::FIELD_PRIMARY_QUEUE_CONFIGURATION_ID},
                LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID    => $consumerProductId ?? $this->leadProcessingRepository->getConsumerProductIdForLead($lead->{EloquentQuote::ID}, $processor)
            ]
        );
    }

    /**
     * @param int $leadId
     * @return bool
     */
    public function isLeadInInitialQueue(int $leadId): bool
    {
        return LeadProcessingInitial::query()->where(LeadProcessingInitial::FIELD_LEAD_ID, $leadId)->get()->count() > 0;
    }

    /**
     * @param int $ageInHoursLowerLimit
     * @param int|null $ageInHoursUpperLimit
     * @return Collection
     */
    public function getUnderReviewLeadsByAge(int $ageInHoursLowerLimit, ?int $ageInHoursUpperLimit = null): Collection
    {
        $query = LeadProcessingUnderReview::query()
            ->leftJoin(
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuote::TABLE,
                DatabaseHelperService::database().'.'.LeadProcessingUnderReview::TABLE.'.'.LeadProcessingUnderReview::FIELD_LEAD_ID,
                '=',
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuote::TABLE.'.'.EloquentQuote::ID
            )
            ->where(DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuote::TABLE.'.'.EloquentQuote::STATUS, EloquentQuote::VALUE_STATUS_UNDER_REVIEW)
            ->where(LeadProcessingUnderReview::CREATED_AT, '<=', Carbon::now()->subHours($ageInHoursLowerLimit));

        if($ageInHoursUpperLimit){
            $query->where(LeadProcessingUnderReview::CREATED_AT, '>', Carbon::now()->subHours($ageInHoursUpperLimit));
        }

        return $query->get();
    }

    /**
     * @param EloquentQuote $lead
     * @return string|null
     */
    public function getLeadQueue(EloquentQuote $lead): ?string
    {
        if (LeadProcessingInitial::query()->where(LeadProcessingInitial::FIELD_LEAD_ID, $lead->{EloquentQuote::ID})->count() > 0) {
            return self::STATUS_INITIAL;
        } else if (LeadProcessingPendingReview::query()->where(LeadProcessingPendingReview::FIELD_LEAD_ID, $lead->{EloquentQuote::ID})->count() > 0) {
            return self::STATUS_PENDING_REVIEW;
        } else if (LeadProcessingUnderReview::query()->where(LeadProcessingUnderReview::FIELD_LEAD_ID, $lead->{EloquentQuote::ID})->count() > 0) {
            return self::STATUS_UNDER_REVIEW;
        } else {
            return null;
        }
    }

    /**
     * @param EloquentQuote $lead
     * @param LeadProcessor $processor
     * @param string $reason
     * @param string|null $comment
     * @param bool|null $publicComment
     * @param ConsumerProduct|null $consumerProduct
     * @return void
     * @throws BindingResolutionException
     */
    protected function handleChangeToPendingReviewStatus(
        EloquentQuote $lead,
        LeadProcessor $processor,
        string $reason,
        ?string $comment = null,
        ?bool $publicComment = null,
        ?ConsumerProduct $consumerProduct = null
    ): void
    {
        $this->leadProcessingRepository->removeInitial($lead);
        $this->leadProcessingRepository->removePendingReview($lead);
        $this->leadProcessingRepository->removeUnderReview($lead);

        $this->leadProcessingQueueConstraintsRepository->saveLeadProcessingQueueConstraintsBucketFlags($lead, $consumerProduct?->id);
        $flags = $this->leadProcessingQueueConstraintsRepository->getFlagsByLeadId($lead->{EloquentQuote::ID});

        if ((!$lead->solar_lead && !$lead->roofing_lead) || ($flags->{LeadProcessingQueueConstraintsBucketFlags::RELATION_BUDGET_STATUS}->{LeadProcessingBudgetStatus::FIELD_KEY} === LeadProcessingBudgetStatus::KEY_WITHIN_BUDGET
        || count(app(LeadAggregatorsService::class)->getAvailableAggregators($lead)) > 0)) {
            $this->leadProcessingRepository->updateStatusAndReason($lead, EloquentQuote::VALUE_STATUS_INITIAL, $reason);

            $this->leadProcessingRepository->markLeadAsPendingReview($lead, $processor, $reason);

            if ($consumerProduct)
                event(new ConsumerProductMarkedAsPendingReviewEvent(
                    consumerProduct: $consumerProduct,
                    processor: $processor,
                    reason: $reason,
                    comment: $comment,
                    publicComment: $publicComment,
                ));

            $this->consumerProductRepository->updateConsumerProductStatusByLegacyLeadIdIfExists($lead->quoteid, ConsumerProduct::STATUS_PENDING_REVIEW);

            $this->leadProcessingRepository->recordProcessorHistory($processor->{LeadProcessor::FIELD_ID}, $lead->{EloquentQuote::QUOTE_ID}, $processor->team->primary_queue_configuration_id, LeadProcessingHistory::ACTION_PENDING_REVIEW);
        } else {
            $this->changeQueues($lead, $processor, self::STATUS_UNDER_REVIEW, $reason);
        }
    }

    /**
     * @param EloquentQuote $lead
     * @param LeadProcessor $processor
     * @param string $reason
     * @param string|null $comment
     * @param bool|null $publicComment
     * @param bool|null $automatic
     * @param ConsumerProduct|null $consumerProduct
     * @return void
     * @throws BindingResolutionException
     */
    protected function handleChangeToUnderReviewStatus(
        EloquentQuote $lead,
        LeadProcessor $processor,
        string $reason,
        ?string $comment = null,
        ?bool $publicComment = null,
        ?bool $automatic = null,
        ?ConsumerProduct $consumerProduct = null,
    ): void
    {
        $priorQueue = $this->getLeadQueue($lead);

        $this->leadProcessingRepository->removeInitial($lead);
        $this->leadProcessingRepository->removePendingReview($lead);

        if ((time() - $lead->{EloquentQuote::TIMESTAMP_ADDED}) >= (60 * 60 * 24 * 2)
            && stripos($reason, 'requalify') === false) {
            $reason .= ", Requalify";
        }

        $this->leadProcessingRepository->updateStatusAndReason($lead, EloquentQuote::VALUE_STATUS_UNDER_REVIEW, $reason);

        $this->leadProcessingQueueConstraintsRepository->saveLeadProcessingQueueConstraintsBucketFlags($lead, $consumerProduct?->id);

        $this->leadProcessingRepository->markLeadAsUnderReview($lead, $processor, $reason, $lead->pendingReview ? $lead->pendingReview->reason : ($lead->underReview?->existing_reason));

        $this->consumerProductRepository->updateConsumerProductStatusByLegacyLeadIdIfExists($lead->quoteid, ConsumerProduct::STATUS_UNDER_REVIEW);

        if ($consumerProduct)
            event(new ConsumerProductMarkedAsUnderReviewEvent(
                consumerProduct: $consumerProduct,
                processor: $processor,
                reason: $reason,
                comment: $comment,
                publicComment: $publicComment
            ));

        $historyId = $this->leadProcessingRepository->recordProcessorHistory(
            $processor->{LeadProcessor::FIELD_ID},
            $lead->{EloquentQuote::QUOTE_ID},
            $processor->team->primary_queue_configuration_id,
            $automatic ? LeadProcessingHistory::ACTION_SYSTEM_MOVED_PR_UR : LeadProcessingHistory::ACTION_UNDER_REVIEW
        );

        if ($priorQueue !== self::STATUS_INITIAL
        && !$automatic && $consumerProduct) {
            app()->make(LeadCommunicationService::class)->recordContactAttempt($consumerProduct->id, $historyId, $processor->{LeadProcessor::FIELD_ID});
        }
    }

    /**
     * @param EloquentQuote $lead
     * @param LeadProcessor $processor
     * @param string $reason
     * @param string|null $comment
     * @param bool|null $publicComment
     * @param ConsumerProduct|null $consumerProduct
     * @return void
     * @throws Exception
     */
    protected function handleChangeToCancelledStatus(
        EloquentQuote $lead,
        LeadProcessor $processor,
        string $reason,
        ?string $comment = null,
        ?bool $publicComment = null,
        ?ConsumerProduct $consumerProduct = null
    ): void
    {
        $this->leadProcessingRepository->removeInitial($lead);
        $this->leadProcessingRepository->removePendingReview($lead);
        $this->leadProcessingRepository->removeUnderReview($lead);
        $this->removeAged($lead);

        $this->leadProcessingQueueConstraintsRepository->deleteBucketFlags($lead->{EloquentQuote::ID});

        $this->leadProcessingRepository->updateStatusAndReason($lead, EloquentQuote::VALUE_STATUS_CANCELLED, $reason);

        $this->dispatcher->dispatch(new LeadCancelledEvent($lead->{EloquentQuote::REFERENCE}, $processor->id, $reason, $comment));

        if ($consumerProduct)
            event(new ConsumerProductCancelledEvent(
                consumerProduct: $consumerProduct,
                processor: $processor,
                reason: $reason,
                comment: $comment
            ));

        $this->consumerProductRepository->updateConsumerProductStatusByLegacyLeadIdIfExists($lead->quoteid, ConsumerProduct::STATUS_CANCELLED);

        $this->leadProcessingRepository->recordProcessorHistory($processor->{LeadProcessor::FIELD_ID}, $lead->{EloquentQuote::QUOTE_ID}, $processor->team->primary_queue_configuration_id, LeadProcessingHistory::ACTION_CANCELLED);
    }

    /**
     * @param EloquentQuote $lead
     *
     * @return void
     */
    protected function removeAged(EloquentQuote $lead): void
    {
        /** @var ProductProcessingRepository $repository */
        $repository = app(ProductProcessingRepository::class);

        $lead->consumer->consumerProducts->each(fn(ConsumerProduct $consumerProduct) => $repository->removeAged($consumerProduct));
    }

    /**
     * @param EloquentQuote $lead
     * @param LeadProcessor $processor
     * @param string $reason
     * @param string|null $comment
     * @param string|null $publicComment
     * @param ConsumerProduct|null $consumerProduct
     * @return void
     * @throws Exception
     */
    protected function handleChangeToAllocatedStatus(EloquentQuote $lead, LeadProcessor $processor, string $reason, ?string $comment = null, ?string $publicComment = null, ?ConsumerProduct $consumerProduct = null): void
    {
        $this->leadProcessingRepository->removeInitial($lead);
        $this->leadProcessingRepository->removePendingReview($lead);
        $this->leadProcessingRepository->removeUnderReview($lead);

        $this->leadProcessingQueueConstraintsRepository->deleteBucketFlags($lead->{EloquentQuote::ID});

        $this->leadProcessingRepository->updateStatusAndReason($lead, EloquentQuote::VALUE_STATUS_ALLOCATED, $reason);

        $this->dispatcher->dispatch(new ConsumerProductApprovedEvent(
            consumerProduct: $consumerProduct,
            processor: $processor,
            reason: $reason,
            comment: $comment
        ));

        $this->consumerProductRepository->updateConsumerProductStatusByLegacyLeadIdIfExists($lead->quoteid, ConsumerProduct::STATUS_PENDING_ALLOCATION);

        $this->leadProcessingRepository->recordProcessorHistory($processor->{LeadProcessor::FIELD_ID}, $lead->{EloquentQuote::QUOTE_ID}, $processor->team->primary_queue_configuration_id, LeadProcessingHistory::ACTION_ALLOCATED);

    }

    /**
     * @param EloquentQuote $lead
     * @param LeadProcessor $processor
     * @param string $newProcessingStatus
     * @param string $reason
     * @param string|null $comment
     * @param bool|null $publicComment
     * @param bool|null $automatic
     * @param ConsumerProduct|null $consumerProduct
     * @return bool
     * @throws BindingResolutionException
     */
    public function changeQueues(
        EloquentQuote $lead,
        LeadProcessor $processor,
        string        $newProcessingStatus,
        string        $reason,
        ?string       $comment = null,
        ?bool         $publicComment = false,
        ?bool         $automatic = null,
        ?ConsumerProduct $consumerProduct = null,
    ): bool
    {
        if(empty($consumerProduct)) {
            $consumerProduct = $this->consumerProductRepository->getConsumerProductByLegacyLeadId($lead->quoteid);
        }

        if($consumerProduct){
            ConsumerProductLifecycleTrackingService::queueUpdated($consumerProduct, $processor, $newProcessingStatus, $reason, $comment, $automatic);
        }
        else {
            // This really shouldn't be happening, but just to be sure...
            captureException(new Exception("Lead Processing Error: a legacy Lead has entered the queue with no matching ConsumerProduct. To queue: $newProcessingStatus, lead ID: $lead->quoteid"));
        }

        switch ($newProcessingStatus) {
            case self::STATUS_PENDING_REVIEW:
                $this->handleChangeToPendingReviewStatus($lead, $processor, $reason, $comment, $publicComment, $consumerProduct);
                break;
            case self::STATUS_UNDER_REVIEW:
                $this->handleChangeToUnderReviewStatus($lead, $processor, $reason, $comment, $publicComment, $automatic, $consumerProduct);
                break;
            case self::STATUS_CANCELLED:
                $this->handleChangeToCancelledStatus($lead, $processor, $reason, $comment, $publicComment, $consumerProduct);
                break;
            case self::STATUS_ALLOCATED:
                $this->handleChangeToAllocatedStatus($lead, $processor, $reason, $comment, $publicComment, $consumerProduct);
                break;
            default:
                throw new Exception("Invalid lead processing status");
        }

        return true;
    }

    /**
     * @return bool
     * @throws BindingResolutionException
     */
    public function moveOldPendingReviewLeadsToUnderReview(): bool
    {
        $pendingReviewTimeframeLimit = time() - (60 * 60 * 24 * self::MAX_DAYS_DISPLAY[self::STATUS_PENDING_REVIEW]);

        $oldPRLeads = LeadProcessingPendingReview::query()
                        ->join(DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuote::TABLE, function($query) {
                            $query->on(
                                DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuote::TABLE.'.'.EloquentQuote::ID,
                                '=',
                                DatabaseHelperService::database().'.'.LeadProcessingPendingReview::TABLE.'.'.LeadProcessingPendingReview::FIELD_LEAD_ID
                            );
                        })
                        ->where(EloquentQuote::TABLE.'.'.EloquentQuote::TIMESTAMP_ADDED, '<=', $pendingReviewTimeframeLimit)
                        ->get();

        foreach($oldPRLeads as $oldPRLead) {
            $lead = $oldPRLead->{LeadProcessingPendingReview::RELATION_LEAD};
            $processor = $oldPRLead->{LeadProcessingPendingReview::RELATION_LEAD_PROCESSOR};

            $this->changeQueues(
                $lead,
                $oldPRLead->{LeadProcessingPendingReview::RELATION_LEAD_PROCESSOR},
                self::STATUS_UNDER_REVIEW,
                "Aged Out of Pending Review",
                null,
                true
            );

            $this->logStatus(
                "Automatically moved old lead from Pending to Under Review: {$lead->{EloquentQuote::ID}}",
                [
                    "lead_id" => $lead->{EloquentQuote::ID}
                ]
            );

            $this->leadProcessingRepository->releaseLead($lead->{EloquentQuote::ID});
            $this->leadProcessingRepository->removeHeartbeat($lead, $processor);
            $this->dispatcher->dispatch(new LeadReleasedEvent($lead->{EloquentQuote::REFERENCE}, $processor->id));
        }

        return true;
    }

    /**
     * Logs a status message of the job.
     *
     * This only runs if the shouldLog value is set to true.
     * @see LeadProcessingQueueRepository::$shouldLog
     *
     * @param string $message
     * @param array $payload
     * @return void
     */
    protected function logStatus(string $message, array $payload = []): void
    {
        try {
            if($this->shouldLog) {
                RecordMonitoringLog::dispatch($message, $payload);
            }
        }
        catch (Exception $e) {
            logger()->error($e->getMessage());
        }
    }
}
