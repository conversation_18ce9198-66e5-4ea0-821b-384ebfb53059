<?php

namespace App\Repositories\LeadProcessing;

use App\Models\LeadProcessingBudgetStatus;
use Illuminate\Database\Eloquent\Collection;

class LeadProcessingBudgetStatusRepository
{
    /**
     * @param string|null $key
     * @return Collection
     */
    public function getBudgetStatuses(
        ?string $key = ''
    ): Collection
    {
        $searchQuery = LeadProcessingBudgetStatus::query();

        if(!empty(trim($key))) {
            $searchQuery->where(LeadProcessingBudgetStatus::FIELD_KEY, '=', trim($key));
        }

        return $searchQuery->get();
    }

    /**
     * Todo: Re-write function to use new campaign logic to determine budget status
     * @return int|null
     */
    public function determineLeadProcessingBudgetStatus(): ?int
    {
        $budgetStatuses = $this->getBudgetStatuses()->keyBy(LeadProcessingBudgetStatus::FIELD_KEY);

        if(empty($budgetStatuses)) {
            return null;
        }

        return $budgetStatuses[LeadProcessingBudgetStatus::KEY_WITHIN_BUDGET][LeadProcessingBudgetStatus::FIELD_ID];
    }
}
