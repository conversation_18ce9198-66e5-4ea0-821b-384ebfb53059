<?php

namespace App\Repositories\LeadProcessing;

use App\Models\AvailableBudget;
use App\Models\LeadProcessingBudgetStatus;
use App\Models\LeadProcessingCallingTimeZoneConfiguration;
use App\Models\LeadProcessingCommunication;
use App\Models\LeadProcessingConstraint;
use App\Models\LeadProcessingQueueConstraint;
use App\Models\LeadProcessingQueueConstraintsBucketFlags;
use App\Models\LeadProcessingTimeframe;
use App\Models\LeadProcessingUnderReview;
use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentConfiguration;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentZipCode;
use App\Models\Legacy\Industry;
use App\Models\Odin\Consumer;
use App\Models\TimeframeContactBuffers;
use App\Repositories\Legacy\SuperPremiumLimitsRepository;
use App\Services\DatabaseHelperService;
use App\Services\LeadProcessing\LeadProcessingService;
use App\Services\Legacy\Campaigns\LeadCampaignAvailabilityService;
use Carbon\CarbonImmutable;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\DatabaseManager;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class LeadProcessingQueueConstraintsRepository
{
    const INDUSTRY_SOLAR   = 'solar';
    const INDUSTRY_ROOFING = 'roofing';

    const STATUS_INITIAL        = 'initial';
    const STATUS_PENDING_REVIEW = 'pending_review';
    const STATUS_UNDER_REVIEW   = 'under_review';

    /**
     * The opening and closing hours for when a processor can call a lead.
     * Matt has stated that this value does not need to be configurable, and for the foreseeable future
     * there's no cases in which we should call a lead outside these hours.
     */
    const CALL_OPENING_HOUR = 9;
    const CALL_CLOSING_HOUR = 20;

    /**
     * @var DatabaseManager
     */
    protected $db;

    /**
     * @param DatabaseManager $db
     */
    public function __construct(
        DatabaseManager $db
    )
    {
        $this->db = $db;
    }

    /**
     * @param int $queueId
     * @param string $constraintType
     * @param string $constraintKey
     *
     * @return Collection
     */
    public function getConstraints(
        int    $queueId = 0,
        string $constraintType = '',
        string $constraintKey = ''
    ): Collection
    {
        $searchQuery = LeadProcessingQueueConstraint::query()
            ->join(LeadProcessingConstraint::TABLE, function ($query) {
                $query->on(LeadProcessingConstraint::TABLE . '.' . LeadProcessingConstraint::FIELD_ID, '=', LeadProcessingQueueConstraint::TABLE . '.' . LeadProcessingQueueConstraint::FIELD_CONSTRAINT_ID);
            })
            ->orderBy(LeadProcessingQueueConstraint::FIELD_ORDER, 'ASC');

        if ($queueId > 0) {
            $searchQuery->where(LeadProcessingQueueConstraint::FIELD_QUEUE_ID, '=', $queueId);
        }

        if (!empty($constraintType)) {
            $searchQuery->where(LeadProcessingConstraint::FIELD_TYPE, '=', $constraintType);
        }

        if (!empty($constraintKey)) {
            $searchQuery->where(LeadProcessingConstraint::FIELD_KEY, '=', $constraintKey);
        }

        return $searchQuery->get();
    }

    /**
     * @param Builder $query
     * @param int $queueId
     * @param string $industry
     * @param bool $lastRound
     *
     * @return Builder
     * @throws Exception
     */
    public function addQueueConstraints(
        Builder $query,
        int     $queueId,
        string  $industry,
        bool    $lastRound
    ): Builder
    {
        $query->join(DatabaseHelperService::database().'.'.LeadProcessingQueueConstraintsBucketFlags::TABLE, function ($query) {
            $query->on(DatabaseHelperService::database().'.'.LeadProcessingQueueConstraintsBucketFlags::TABLE . '.' . LeadProcessingQueueConstraintsBucketFlags::FIELD_LEAD_ID, '=', EloquentQuote::TABLE . '.' . EloquentQuote::ID);
        });

        $constraints = $this->getConstraints($queueId);

        foreach ($constraints as $constraint) {
            if ($constraint->{LeadProcessingConstraint::FIELD_TYPE} === LeadProcessingConstraint::CONSTRAINT_TYPE_FILTER) {
                $query = $this->addFilterConstraint($query, $constraint->{LeadProcessingConstraint::FIELD_KEY}, $industry);
            } else if ($constraint->{LeadProcessingConstraint::FIELD_TYPE} === LeadProcessingConstraint::CONSTRAINT_TYPE_SORT) {
                $query = $this->addSortConstraint($query, $constraint->{LeadProcessingConstraint::FIELD_KEY}, $lastRound);
            } else if ($constraint->{LeadProcessingConstraint::FIELD_TYPE} === LeadProcessingConstraint::CONSTRAINT_TYPE_BUCKET) {
                $query = $this->addBucketConstraint($query, $constraint->{LeadProcessingConstraint::FIELD_KEY});
            }
        }

        return $query;
    }

    /**
     * @param Builder $query
     * @param string $constraintKey
     * @param bool $lastRound
     * @return Builder
     * @throws Exception
     */
    protected function addSortConstraint(
        Builder $query,
        string  $constraintKey,
        bool    $lastRound
    ): Builder
    {
        switch ($constraintKey) {
            case LeadProcessingConstraint::CONSTRAINT_REVENUE:
                $query = $this->addPossibleRevenueSorting($query, $lastRound);
                break;
            case LeadProcessingConstraint::CONSTRAINT_OLDEST_FIRST:
                $query->orderBy(DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuote::TABLE . '.' . EloquentQuote::TIMESTAMP_ADDED, 'ASC');
                break;
            case LeadProcessingConstraint::CONSTRAINT_LAST_ROUND:
                $query->orderBy(DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuote::TABLE . '.' . EloquentQuote::TIMESTAMP_ADDED, $lastRound ? 'ASC' : 'DESC');
                break;
            default:
                throw new Exception("Invalid sort constraint");
        }

        return $query;
    }

    /**
     * @param Builder $query
     * @param string $constraintKey
     * @param string $industry
     * @return Builder
     * @throws Exception
     */
    protected function addFilterConstraint(
        Builder $query,
        string  $constraintKey,
        string  $industry,
    ): Builder
    {
        switch ($constraintKey) {
            case LeadProcessingConstraint::CONSTRAINT_SUPER_PREMIUM:
                $query->where(DatabaseHelperService::database().'.'.LeadProcessingQueueConstraintsBucketFlags::TABLE . '.' . LeadProcessingQueueConstraintsBucketFlags::FIELD_SUPER_PREMIUM, '=', true);
                break;
            case LeadProcessingConstraint::CONSTRAINT_CONTACT_BUFFERS:
                if (!collect($query->getQuery()->joins)->pluck('table')->contains(DatabaseHelperService::database().'.'.LeadProcessingTimeframe::TABLE)) {
                    $query->join(DatabaseHelperService::database().'.'.LeadProcessingTimeframe::TABLE, function ($query) {
                        $query->on(DatabaseHelperService::database().'.'.LeadProcessingTimeframe::TABLE . '.' . LeadProcessingTimeframe::FIELD_ID, '=', DatabaseHelperService::database().'.'.LeadProcessingQueueConstraintsBucketFlags::TABLE . '.' . LeadProcessingQueueConstraintsBucketFlags::FIELD_TIMEFRAME_ID);
                    });
                }

                $query = $this->addContactBuffersFiltering($query);
                break;
            case LeadProcessingConstraint::CONSTRAINT_LOCAL_HOURS:
                $query = $this->addLocalHoursFiltering($query);
                break;
            case LeadProcessingConstraint::CONSTRAINT_AVAILABLE_BUDGET:
                $query = $this->addAvailableBudgetFiltering($query, $industry);
                break;
            default:
                throw new Exception("Invalid filter constraint");
        }

        return $query;
    }

    /**
     * @param Builder $query
     * @param string $constraintKey
     * @return Builder
     * @throws Exception
     */
    protected function addBucketConstraint(
        Builder $query,
        string  $constraintKey
    ): Builder
    {
        switch ($constraintKey) {
            case LeadProcessingConstraint::CONSTRAINT_BUDGET:
                $query
                    ->join(DatabaseHelperService::database().'.'.LeadProcessingBudgetStatus::TABLE, function ($query) {
                        $query->on(DatabaseHelperService::database().'.'.LeadProcessingQueueConstraintsBucketFlags::TABLE . '.' . LeadProcessingQueueConstraintsBucketFlags::FIELD_BUDGET_STATUS_ID, '=', DatabaseHelperService::database().'.'.LeadProcessingBudgetStatus::TABLE . '.' . LeadProcessingBudgetStatus::FIELD_ID);
                    })
                    ->orderBy(DatabaseHelperService::database().'.'.LeadProcessingBudgetStatus::TABLE . '.' . LeadProcessingBudgetStatus::FIELD_PRIORITY, 'ASC');
                break;
            case LeadProcessingConstraint::CONSTRAINT_TIMEFRAME:
                if (!collect($query->getQuery()->joins)->pluck('table')->contains(DatabaseHelperService::database().'.'.LeadProcessingTimeframe::TABLE)) {
                    $query->join(DatabaseHelperService::database().'.'.LeadProcessingTimeframe::TABLE, function ($query) {
                        $query->on(DatabaseHelperService::database().'.'.LeadProcessingTimeframe::TABLE . '.' . LeadProcessingTimeframe::FIELD_ID, '=', DatabaseHelperService::database().'.'.LeadProcessingQueueConstraintsBucketFlags::TABLE . '.' . LeadProcessingQueueConstraintsBucketFlags::FIELD_TIMEFRAME_ID);
                    });
                }

                $query->orderBy(DatabaseHelperService::database().'.'.LeadProcessingTimeframe::TABLE . '.' . LeadProcessingTimeframe::FIELD_PRIORITY, 'ASC');
                break;
            default:
                throw new Exception("Invalid bucket constraint");
        }

        return $query;
    }

    /**
     * Begin query constraint functions
     */

    /**
     * @param Builder $query
     * @param string $industry
     * @return Builder
     */
    private function addAvailableBudgetFiltering(Builder $query, string $industry): Builder
    {
        $query->join(DatabaseHelperService::database() . '.' . AvailableBudget::TABLE, function ($query) {
            $query->on(DatabaseHelperService::database() . '.' . AvailableBudget::TABLE . '.' . AvailableBudget::FIELD_LOCATION_ID, '=', DatabaseHelperService::database() . '.' . LeadProcessingUnderReview::TABLE . '.' . LeadProcessingUnderReview::FIELD_LOCATION_ID);
        });

        $industryType = match($industry) {
            'roofing' => AvailableBudget::INDUSTRY_TYPE_ROOFING,
            'solar' => AvailableBudget::INDUSTRY_TYPE_SOLAR,
            default => AvailableBudget::INDUSTRY_TYPE_SOLAR
        };

        return $query->where(DatabaseHelperService::database() . '.' . AvailableBudget::TABLE . '.' . AvailableBudget::FIELD_INDUSTRY_TYPE, $industryType);
    }

    /**
     * Handles restricting the query based on an opening and closing hour for the timezones local time.
     *
     * @param Builder $query
     * @return Builder
     */
    private function addLocalHoursFiltering(Builder $query): Builder
    {
        if(!collect($query->getQuery()->joins)->pluck('table')->contains(DatabaseHelperService::readOnlyDatabase().'.'.EloquentAddress::TABLE)) {
            $query->join(DatabaseHelperService::readOnlyDatabase().'.'.EloquentAddress::TABLE, function($query) {
                $query->on(DatabaseHelperService::readOnlyDatabase().'.'.EloquentAddress::TABLE.'.'.EloquentAddress::ID, '=', DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuote::TABLE.'.'.EloquentQuote::ADDRESS_ID);
            });
        }

        $timezoneOpenCol = LeadProcessingCallingTimeZoneConfiguration::TABLE.'.'.LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR;
        $timezoneCloseCol = LeadProcessingCallingTimeZoneConfiguration::TABLE.'.'.LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR;
        $zipcodeUtcCol = EloquentZipCode::TABLE.'.'.EloquentZipCode::FIELD_UTC;
        $zipcodeDstCol = EloquentZipCode::TABLE.'.'.EloquentZipCode::FIELD_DST;

        $defaultUtc = LeadProcessingService::FALLBACK_UTC_OFFSET;
        $defaultTimezone = LeadProcessingCallingTimeZoneConfiguration::query()->where(LeadProcessingCallingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET, $defaultUtc)->first();
        $defaultTimezoneOpen = (int) $defaultTimezone[LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR];
        $defaultTimezoneClose = (int) $defaultTimezone[LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR];

        return $query
            ->leftJoin(DatabaseHelperService::readOnlyDatabase().'.'.EloquentZipCode::TABLE, function($query) {
                $query
                    ->on(DatabaseHelperService::readOnlyDatabase().'.'.EloquentZipCode::TABLE.'.'.EloquentZipCode::FIELD_ZIP_CODE, '=', DatabaseHelperService::readOnlyDatabase().'.'.EloquentAddress::TABLE.'.'.EloquentAddress::ZIP_CODE)
                    ->where(DatabaseHelperService::readOnlyDatabase().'.'.EloquentZipCode::TABLE.'.'.EloquentZipCode::FIELD_ZIP_TYPE, '=', EloquentZipCode::ZIP_TYPE_STANDARD);
            })
            ->leftJoin(DatabaseHelperService::database().'.'.LeadProcessingCallingTimeZoneConfiguration::TABLE, function($query) {
                $query->on(DatabaseHelperService::readOnlyDatabase().'.'.EloquentZipCode::TABLE.'.'.EloquentZipCode::FIELD_UTC, '=', DatabaseHelperService::database().'.'.LeadProcessingCallingTimeZoneConfiguration::TABLE.'.'.LeadProcessingCallingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET);
            })
            ->whereRaw(
                "TIME_FORMAT(CONVERT_TZ(NOW(), '+00:00', CONCAT(IFNULL($zipcodeUtcCol, $defaultUtc) + IF($zipcodeDstCol = 'Y', 1, 0), ':00')), '%k') >= IFNULL($timezoneOpenCol, $defaultTimezoneOpen)"
            )
            ->whereRaw(
                "TIME_FORMAT(CONVERT_TZ(NOW(), '+00:00', CONCAT(IFNULL($zipcodeUtcCol, $defaultUtc) + IF($zipcodeDstCol = 'Y', 1, 0), ':00')), '%k') < IFNULL($timezoneCloseCol, $defaultTimezoneClose)"
            );
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    private function addContactBuffersFiltering(Builder $query): Builder
    {
        return $query
            ->join(DatabaseHelperService::database().'.'.TimeframeContactBuffers::TABLE, function ($query) {
                $query->on(
                    DatabaseHelperService::database().'.'.TimeframeContactBuffers::TABLE . '.' . TimeframeContactBuffers::FIELD_TIMEFRAME_ID,
                    '=',
                    DatabaseHelperService::database().'.'.LeadProcessingTimeframe::TABLE . '.' . LeadProcessingTimeframe::FIELD_ID
                );
            })
            ->leftJoin(DatabaseHelperService::database().'.'.LeadProcessingCommunication::TABLE, function ($join) {
                $join
                    ->on(
                        DatabaseHelperService::database().'.'.LeadProcessingQueueConstraintsBucketFlags::TABLE.'.'.LeadProcessingQueueConstraintsBucketFlags::FIELD_LEAD_ID,
                        '=',
                        DatabaseHelperService::database().'.'.LeadProcessingCommunication::TABLE.'.'.LeadProcessingCommunication::FIELD_LEAD_ID
                    )
                    ->where(LeadProcessingCommunication::TABLE.'.'.LeadProcessingCommunication::FIELD_MOST_RECENT, true);
            })
            ->where(function ($query) {
                $communicationCreatedAtCol = LeadProcessingCommunication::TABLE . '.' . LeadProcessingCommunication::CREATED_AT;
                $contactAttemptBufferHrsCol = DatabaseHelperService::database() . '.' . TimeframeContactBuffers::TABLE . '.' . TimeframeContactBuffers::FIELD_CONTACT_ATTEMPT_BUFFER_HRS;

                $query
                    ->whereRaw("UNIX_TIMESTAMP() - ({$contactAttemptBufferHrsCol} * 60 * 60) >= UNIX_TIMESTAMP({$communicationCreatedAtCol})")
                    ->orWhereNull(LeadProcessingCommunication::TABLE.'.'.LeadProcessingCommunication::FIELD_LEAD_ID);
            });
    }

    /**
     * We want to swap between the first and last lead in the top 50, so rotate between taking 1 and 50
     *
     * @param Builder $query
     * @param bool $lastRound
     * @return Builder
     */
    private function addPossibleRevenueSorting(Builder $query, bool $lastRound): Builder
    {
        return $query->orderByDesc(DatabaseHelperService::database().'.'.LeadProcessingUnderReview::TABLE.'.'.LeadProcessingUnderReview::FIELD_POSSIBLE_REVENUE)->take($lastRound ? 50 : 1);
    }

    /**
     * End query constraint functions
     */

    /**
     * @param EloquentQuote $lead
     * @param int|null $consumerProductId
     * @return bool
     * @throws BindingResolutionException
     */
    public function saveLeadProcessingQueueConstraintsBucketFlags(EloquentQuote $lead, int $consumerProductId = null): bool
    {
        $consumerProductId = $consumerProductId
            ?? $lead->consumer->consumerProducts()->first()?->id ?? null;

        /** @var LeadProcessingBudgetStatusRepository $leadProcessingBudgetStatusRepository */
        $leadProcessingBudgetStatusRepository = app()->make(LeadProcessingBudgetStatusRepository::class);

        /** @var LeadProcessingTimeframeRepository $leadProcessingTimeframeRepository */
        $leadProcessingTimeframeRepository = app()->make(LeadProcessingTimeframeRepository::class);

        $flags = LeadProcessingQueueConstraintsBucketFlags::firstOrNew([LeadProcessingQueueConstraintsBucketFlags::FIELD_LEAD_ID => $lead->{EloquentQuote::ID}]);

        $flags->{LeadProcessingQueueConstraintsBucketFlags::FIELD_BUDGET_STATUS_ID}    = $leadProcessingBudgetStatusRepository->determineLeadProcessingBudgetStatus();
        $flags->{LeadProcessingQueueConstraintsBucketFlags::FIELD_TIMEFRAME_ID}        = $leadProcessingTimeframeRepository->getTimeframeByKey(LeadProcessingTimeframe::KEY_LESS_THAN_TWENTY_FOUR_HRS)->{LeadProcessingTimeframe::FIELD_ID};
        $flags->{LeadProcessingQueueConstraintsBucketFlags::FIELD_SUPER_PREMIUM}       = false;
        $flags->{LeadProcessingQueueConstraintsBucketFlags::FIELD_CONSUMER_PRODUCT_ID} = $consumerProductId;

        return $flags->save();
    }

    /**
     * @param int $leadId
     * @return LeadProcessingQueueConstraintsBucketFlags|null
     */
    public function getFlagsByLeadId(int $leadId): ?LeadProcessingQueueConstraintsBucketFlags
    {
        return LeadProcessingQueueConstraintsBucketFlags::where(LeadProcessingQueueConstraintsBucketFlags::FIELD_LEAD_ID, $leadId)->first();
    }

    /**
     * @param int $leadId
     * @return bool
     *
     * @throws Exception
     */
    public function deleteBucketFlags(int $leadId): bool
    {
        LeadProcessingQueueConstraintsBucketFlags::query()->where(LeadProcessingQueueConstraintsBucketFlags::FIELD_LEAD_ID, '=', $leadId)->delete();

        return true;
    }

    /**
     * Update the timeframe bucket ID for each lead according to the current time
     *
     * @return bool
     */
    public function updateTimeframeFlags(): bool
    {
        $twentyFourHrs = 60 * 60 * 24;
        $fortyEightHrs = 60 * 60 * 24 * 2;
        $sevenDays = 60 * 60 * 24 * 7;
        $thirtyDays = 60 * 60 * 24 * 30;

        $timeframes = LeadProcessingTimeframe::all()->pluck(LeadProcessingTimeframe::FIELD_ID, LeadProcessingTimeframe::FIELD_KEY);

        $lessThanTwentyFourHrsTimeframe = $timeframes[LeadProcessingTimeframe::KEY_LESS_THAN_TWENTY_FOUR_HRS];
        $twentyFourToFortyEightHrsTimeframe = $timeframes[LeadProcessingTimeframe::KEY_TWENTY_FOUR_TO_FORTY_EIGHT_HRS];
        $twoToSevenDaysTimeframe = $timeframes[LeadProcessingTimeframe::KEY_TWO_TO_SEVEN_DAYS];
        $sevenToThirtyDaysTimeframe = $timeframes[LeadProcessingTimeframe::KEY_SEVEN_TO_THIRTY_DAYS];
        $thirtyToNinetyDaysTimeframe = $timeframes[LeadProcessingTimeframe::KEY_THIRTY_TO_NINETY_DAYS];

        $quoteTimestampAddedCol = EloquentQuote::TABLE . '.' . EloquentQuote::TIMESTAMP_ADDED;

        $this->db
            ->table(DatabaseHelperService::database() . '.' . LeadProcessingQueueConstraintsBucketFlags::TABLE)
            ->join(DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuote::TABLE, function ($query) {
                $query->on(
                    DatabaseHelperService::readOnlyDatabase() . '.' . EloquentQuote::TABLE . '.' . EloquentQuote::ID,
                    '=',
                    DatabaseHelperService::database() . '.' . LeadProcessingQueueConstraintsBucketFlags::TABLE . '.' . LeadProcessingQueueConstraintsBucketFlags::FIELD_LEAD_ID);
            })
            ->where(EloquentQuote::TABLE . '.' . EloquentQuote::LOCKED_BY_USER_ID, '=', 0)
            ->update([
                LeadProcessingQueueConstraintsBucketFlags::FIELD_TIMEFRAME_ID => $this->db->raw("
                    CASE
                        WHEN
                            (UNIX_TIMESTAMP() - $quoteTimestampAddedCol) <= $twentyFourHrs
                        THEN
                            $lessThanTwentyFourHrsTimeframe
                        WHEN
                            (UNIX_TIMESTAMP() - $quoteTimestampAddedCol) BETWEEN ($twentyFourHrs + 1) AND $fortyEightHrs
                        THEN
                            $twentyFourToFortyEightHrsTimeframe
                        WHEN
                            (UNIX_TIMESTAMP() - $quoteTimestampAddedCol) BETWEEN ($fortyEightHrs + 1) AND $sevenDays
                        THEN
                            $twoToSevenDaysTimeframe
                        WHEN
                            (UNIX_TIMESTAMP() - $quoteTimestampAddedCol) BETWEEN ($sevenDays + 1) AND $thirtyDays
                        THEN
                            $sevenToThirtyDaysTimeframe
                        ELSE
                            $thirtyToNinetyDaysTimeframe
                    END
                ")
            ]);

        return true;
    }
}
