<?php

namespace App\Repositories\LeadProcessing;

use App\Models\Legacy\CrmDeliveryLog;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\EloquentUser;
use App\Services\DatabaseHelperService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class LeadProcessingReportRepository
{
    /**
     * @param string $start_date
     * @param string $end_date
     * @return Builder
     */
    public function getAllOverSoldLead(string $start_date, string $end_date): Builder
    {
        return EloquentQuote::query()
            ->select([
                DB::raw('DATE(from_unixtime(' . EloquentQuote::TABLE . '.' . EloquentQuote::TIMESTAMP_ADDED . ')) as created_date '),
                EloquentQuote::TABLE . '.' . EloquentQuote::QUOTE_ID . ' as lead_id',
                EloquentQuote::TABLE . '.' . EloquentQuote::NUMBER_OF_QUOTES . ' as requested_count',
                DB::raw('(SELECT COUNT(*) FROM ' . EloquentQuoteCompany::TABLE . ' WHERE ' . EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::QUOTE_ID . ' = ' . EloquentQuote::TABLE . '.' . EloquentQuote::QUOTE_ID . ') as delivered_count')])
            ->where(EloquentQuote::TABLE . '.' . EloquentQuote::NUMBER_OF_QUOTES, '<>', 0)
            ->whereBetween(EloquentQuote::TABLE . '.' . EloquentQuote::TIMESTAMP_ADDED, [Carbon::parse($start_date)->timestamp, Carbon::parse($end_date)->timestamp])
            ->groupBy(EloquentQuote::TABLE . '.' . EloquentQuote::QUOTE_ID)
            ->havingRaw(EloquentQuote::TABLE . '.' . EloquentQuote::NUMBER_OF_QUOTES . ' < delivered_count')
            ->orderByDesc('created_date');
    }

    /**
     * @param string $start_date
     * @param string $end_date
     * @return Builder
     */
    public function getAllCrmBouncedLeads(string $start_date, string $end_date): Builder
    {
        return CrmDeliveryLog::query()
            ->select([
                CrmDeliveryLog::QUOTE_ID . ' as quote_id',
                CrmDeliveryLog::COMPANY_ID,
                CrmDeliveryLog::DELIVERY_DATA,
                CrmDeliveryLog::ERROR_LOG,
                CrmDeliveryLog::SEND_TEST_LEAD,
                CrmDeliveryLog::TIMESTAMP_ADDED . ' as created_date'
            ])
            ->where(CrmDeliveryLog::DELIVERY_STATUS, false)
            ->whereBetween(CrmDeliveryLog::TIMESTAMP_ADDED, [Carbon::parse($start_date), Carbon::parse($end_date)])
            ->orderByDesc('created_date');
    }

    /**
     * @param string $start_date
     * @param string $end_date
     * @param string $leadStatus
     * @return Builder
     */

    public function getAllAllocatedLead(string $start_date, string $end_date, string $leadStatus = EloquentQuote::VALUE_STATUS_ALLOCATED): Builder
    {
        return EloquentQuote::query()
            ->leftJoin(DatabaseHelperService::readOnlyDatabase() . '.jobs as j', 'j.payload', 'like', DB::raw("concat('%', " . EloquentQuote::QUOTE_ID . ", '%')"))
            ->where(EloquentQuote::STATUS, $leadStatus)
            ->where(function ($query) {
                $query->whereRaw('(SELECT COUNT(*) FROM ' . EloquentQuoteCompany::TABLE . ' WHERE ' . EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::QUOTE_ID . ' = ' . EloquentQuote::TABLE . '.' . EloquentQuote::QUOTE_ID . ') = 0');
            })
            ->whereBetween(EloquentQuote::TABLE . '.' . EloquentQuote::TIMESTAMP_ADDED, [Carbon::parse($start_date)->timestamp, Carbon::parse($end_date)->timestamp])
            ->whereNull('j.id')
            ->selectRaw('DATE(from_unixtime(' . EloquentQuote::TABLE . '.' . EloquentQuote::TIMESTAMP_ADDED . ')) as created_date, ' . EloquentQuote::TABLE . '.' . EloquentQuote::QUOTE_ID . ' as lead_id')
            ->orderByDesc('created_date');
    }

    /**
     * @param string $start_date
     * @param string $end_date
     * @return Builder
     */

    public function getAllDeliveredButNotAllocatedOrCancelledLead(string $start_date, string $end_date): Builder
    {
        return EloquentQuoteCompany::query()
            ->leftJoin(EloquentQuote::TABLE, EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::QUOTE_ID, EloquentQuote::TABLE . '.' . EloquentQuote::QUOTE_ID)
            ->where(EloquentQuote::STATUS, '<>', EloquentQuote::VALUE_STATUS_ALLOCATED)
            ->where(EloquentQuote::STATUS, '<>', EloquentQuote::VALUE_STATUS_CANCELLED)
            ->whereBetween(EloquentQuote::TABLE . '.' . EloquentQuote::TIMESTAMP_ADDED, [Carbon::parse($start_date)->timestamp, Carbon::parse($end_date)->timestamp])
            ->selectRaw('DATE(from_unixtime(' . EloquentQuote::TABLE . '.' . EloquentQuote::TIMESTAMP_ADDED . ')) as created_date, ' . EloquentQuote::TABLE . '.' . EloquentQuote::QUOTE_ID . ' as quote_id, ' . EloquentQuote::TABLE . '.' . EloquentQuote::STATUS . ' as status')
            ->groupBy('created_date', EloquentQuote::TABLE . '.' . EloquentQuote::QUOTE_ID)
            ->orderByDesc('created_date');
    }

    /**
     * @return Builder
     */

    public function getNoChargedLead(): Builder
    {
        return EloquentQuoteCompany::query()
            ->leftJoin(EloquentCompany::TABLE, EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::COMPANY_ID, EloquentCompany::TABLE . '.' . EloquentCompany::COMPANY_ID)
            ->leftJoin(EloquentUser::TABLE, EloquentCompany::TABLE . '.' . EloquentCompany::ACCOUNT_MANAGER_ID, EloquentUser::TABLE . '.' . EloquentUser::USER_ID)
            ->where(EloquentQuoteCompany::DELIVERED, EloquentQuoteCompany::IS_DELIVERED)
            ->where(EloquentQuoteCompany::CHARGEABLE, EloquentQuoteCompany::NOT_CHARGEABLE)
            ->select([
                DB::raw('DATE(from_unixtime(' . EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::TIMESTAMP_DELIVERED . ')) as delivery_date '),
                EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::QUOTE_ID . ' as lead_id',
                EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::CHARGE_STATUS,
                EloquentCompany::TABLE . '.' . EloquentCompany::COMPANY_NAME,
                EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::COMPANY_ID,
                EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::COST,
                EloquentUser::TABLE . '.' . EloquentUser::DISPLAY_NAME . ' as account_manager',
            ])
            ->orderByDesc('delivery_date');
    }
}
