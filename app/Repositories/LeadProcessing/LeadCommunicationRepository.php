<?php

namespace App\Repositories\LeadProcessing;

use App\Enums\PhoneType;
use App\Models\Call;
use App\Models\LeadProcessingCommunication;
use App\Models\LeadProcessingTimeframe;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Phone;
use App\Models\Text;
use App\Models\LeadProcessor;
use App\Models\TimeframeContactBuffers;
use App\Models\UserPhone;
use App\Repositories\Legacy\LeadProcessingRepository;
use App\Services\Odin\ProductProcessing\ProductProcessingService;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Throwable;

class LeadCommunicationRepository
{
    const FALLBACK_PROCESSOR_ID = 0;

    const TWENTY_FOUR_HOURS = 60 * 60 * 24;
    const FORTY_EIGHT_HOURS = 60 * 60 * 24 * 2;
    const SEVEN_DAYS = 60 * 60 * 24 * 7;
    const THIRTY_DAYS = 60 * 60 * 24 * 30;

    /**
     * @param int $consumerProductId
     * @param int $leadProcessingHistoryId
     * @param int $leadProcessorId
     * @return bool
     */
    public function createActionedRecord(
        int $consumerProductId,
        int $leadProcessingHistoryId,
        int $leadProcessorId,
    ): bool
    {
        $this->createLeadProcessingCommunicationAction(
            $consumerProductId,
            $leadProcessingHistoryId,
            $leadProcessorId,
        );

        return true;
    }

    /**
     * @param int $consumerProductId
     * @param int $processingHistoryId
     * @param int $leadProcessorId
     * @param int|null $legacyId
     * @return LeadProcessingCommunication
     */
    protected function createLeadProcessingCommunicationAction(
        int $consumerProductId,
        int $processingHistoryId,
        int $leadProcessorId,
    ): LeadProcessingCommunication
    {
        return LeadProcessingCommunication::create([
            LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID => $consumerProductId,
            LeadProcessingCommunication::FIELD_TYPE                => LeadProcessingCommunication::TYPE_ACTION,
            LeadProcessingCommunication::FIELD_RELATION_ID         => $processingHistoryId,
            LeadProcessingCommunication::FIELD_LEAD_PROCESSOR_ID   => $leadProcessorId,
            LeadProcessingCommunication::FIELD_LEAD_ID             => ProductProcessingService::getLegacyId($consumerProductId),
        ]);
    }

    /**
     * Handles returning a phone number for a given processor.
     *
     * @TODO: In the future, we'd like to trial scoping phone numbers to nearest a given lead.
     *          We can simply update this method to allow that.
     *
     * @param LeadProcessor $processor
     * @return Phone|null
     */
    public function getPhoneNumberForProcessor(LeadProcessor $processor): ?Phone
    {
        /** @var Phone|null $phone */
        $phone = $processor->phones()->where(Phone::FIELD_STATUS, Phone::STATUS_ACTIVE)->first();

        return $phone;
    }

    public function getPhoneByPhoneNumber(LeadProcessor $processor, string $phoneNumber): ?Phone
    {
        return $processor->phones()
            ->where(Phone::FIELD_STATUS, Phone::STATUS_ACTIVE)
            ->where(Phone::FIELD_PHONE, $phoneNumber)
            ->first();
    }

    protected function createOutboundTextRecord(
        string $messageReference,
        int    $phoneId,
        string $otherNumber,
        string $messageBody
    ): Text
    {
        return Text::create([
            Text::FIELD_EXTERNAL_REFERENCE => $messageReference,
            Text::FIELD_PHONE_ID           => $phoneId,
            Text::FIELD_OTHER_NUMBER       => $otherNumber,
            Text::FIELD_DIRECTION          => Text::DIRECTION_OUTBOUND,
            Text::FIELD_MESSAGE_BODY       => $messageBody,
            Model::CREATED_AT              => Carbon::now('UTC'),
            Model::UPDATED_AT              => Carbon::now('UTC')
        ]);
    }

    /**
     * @param string $messageReference
     * @param int    $phoneId
     * @param string $otherNumber
     * @param string $messageBody
     * @return Text|Model
     */
    protected function updateOrCreateInboundText(
        string $messageReference,
        int    $phoneId,
        string $otherNumber,
        string $messageBody
    ): Text
    {
        return Text::query()->updateOrCreate([
            Text::FIELD_EXTERNAL_REFERENCE => $messageReference
        ], [
            Text::FIELD_EXTERNAL_REFERENCE => $messageReference,
            Text::FIELD_PHONE_ID           => $phoneId,
            Text::FIELD_OTHER_NUMBER       => $otherNumber,
            Text::FIELD_MESSAGE_BODY       => $messageBody,
            Text::FIELD_DIRECTION          => Text::DIRECTION_INBOUND,
            Model::CREATED_AT              => Carbon::now('UTC'),
            Model::UPDATED_AT              => Carbon::now('UTC')
        ]);
    }

    public function createOutboundSMS(
        int           $consumerProductId,
        LeadProcessor $processor,
        int           $phoneId,
        string        $toNumber,
        string        $body,
        string        $messageReference
    ): LeadProcessingCommunication
    {
        $text = $this->createOutboundTextRecord($messageReference, $phoneId, $toNumber, $body);

        return LeadProcessingCommunication::create([
            LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID => $consumerProductId,
            LeadProcessingCommunication::FIELD_TYPE                => LeadProcessingCommunication::TYPE_SMS,
            LeadProcessingCommunication::FIELD_RELATION_ID         => $text->id,
            LeadProcessingCommunication::FIELD_LEAD_PROCESSOR_ID   => $processor->id,
            LeadProcessingCommunication::FIELD_LEAD_ID             => ProductProcessingService::getLegacyId($consumerProductId),
        ]);
    }

    public function createOutboundCallRecord(string $reference, int $phoneId, string $otherNumber): Call
    {
        return Call::create([
            Call::FIELD_EXTERNAL_REFERENCE => $reference,
            Call::FIELD_PHONE_ID           => $phoneId,
            Call::FIELD_OTHER_NUMBER       => $otherNumber,
            Call::FIELD_DIRECTION          => Call::DIRECTION_OUTBOUND,
            Call::FIELD_CALL_START         => Carbon::now('UTC')->timestamp,
            Call::CREATED_AT               => Carbon::now('UTC')->timestamp,
            Call::UPDATED_AT               => Carbon::now('UTC')->timestamp,
        ]);
    }

    /**
     * @param int $consumerProductId
     * @param int $callId
     * @param int $leadProcessorId
     * @return LeadProcessingCommunication
     */
    protected function createLeadProcessingCallCommunication(
        int $consumerProductId,
        int $callId,
        int $leadProcessorId
    ): LeadProcessingCommunication
    {
        return LeadProcessingCommunication::create([
            LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID => $consumerProductId,
            LeadProcessingCommunication::FIELD_TYPE                => LeadProcessingCommunication::TYPE_CALL,
            LeadProcessingCommunication::FIELD_RELATION_ID         => $callId,
            LeadProcessingCommunication::FIELD_LEAD_PROCESSOR_ID   => $leadProcessorId,
            LeadProcessingCommunication::FIELD_LEAD_ID             => ProductProcessingService::getLegacyId($consumerProductId),
        ]);
    }

    public function createOutboundCall(int $consumerProductId, LeadProcessor $processor, int $phoneId, string $otherNumber, string $reference): LeadProcessingCommunication
    {
        $call = $this->createOutboundCallRecord($reference, $phoneId, $otherNumber);

        return $this->createLeadProcessingCallCommunication($consumerProductId, $call->id, $processor->id);
    }

    /**
     * Handles updating or creating an inbound SMS communication.
     *
     * @param LeadProcessor|null $leadProcessor
     * @param string $toNumber
     * @param string $fromNumber
     * @param string $reference
     * @param string $body
     * @param int|null $consumerProductId
     * @return LeadProcessingCommunication
     */
    public function updateOrCreateInboundSMS(
        ?LeadProcessor $leadProcessor,
        string         $toNumber,
        string         $fromNumber,
        string         $reference,
        string         $body,
        ?int           $consumerProductId
    ): LeadProcessingCommunication
    {
        $model = $this->updateOrCreateInboundText($reference, $this->getPhoneIdFromPhoneNumber($toNumber), $fromNumber, $body);

        return LeadProcessingCommunication::query()->updateOrCreate([
            LeadProcessingCommunication::FIELD_TYPE        => LeadProcessingCommunication::TYPE_SMS,
            LeadProcessingCommunication::FIELD_RELATION_ID => $model->id,
        ], [
            LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID => $consumerProductId,
            LeadProcessingCommunication::FIELD_TYPE                => LeadProcessingCommunication::TYPE_SMS,
            LeadProcessingCommunication::FIELD_RELATION_ID         => $model->id,
            LeadProcessingCommunication::FIELD_LEAD_PROCESSOR_ID   => $leadProcessor ? $leadProcessor->id : self::FALLBACK_PROCESSOR_ID,
            Model::CREATED_AT                                      => Carbon::now('UTC'),
            Model::UPDATED_AT                                      => Carbon::now('UTC')
        ]);
    }

    /**
     * Handles retrieving the id of a phone number by it's number.
     *
     * @param string $number
     * @return int
     */
    protected function getPhoneIdFromPhoneNumber(string $number): int
    {
        /** @var Phone $model */
        $model = Phone::query()->where(Phone::FIELD_PHONE, $number)->firstOrFail();

        return $model->id;
    }

    /**
     * @param string $otherNumber
     * @return int|null
     */
    public function getConsumerProductIdByOtherNumber(string $otherNumber): ?int
    {
        $call = $this->getMostRecentCallByOtherNumber($otherNumber);
        $text = $this->getMostRecentTextByOtherNumber($otherNumber);

        return $this->getMostRecentConsumerProductIdFromCallAndText($call, $text);
    }

    /**
     * Handles retrieving the lead processor for a given other number.
     *
     * @param string $otherNumber
     * @return LeadProcessor|null
     */
    public function getLeadProcessorByOtherNumber(string $otherNumber): ?LeadProcessor
    {
        $call = $this->getMostRecentCallByOtherNumber($otherNumber);
        $text = $this->getMostRecentTextByOtherNumber($otherNumber);

        return $this->getMostRecentLeadProcessorFromCallAndText($call, $text);
    }



    /**
     * Handles receiving the most recent lead processing communication from a given call or text.
     *
     * @param Call|null $call
     * @param Text|null $text
     * @return LeadProcessingCommunication|null
     */
    protected function getMostRecentLeadProcessingCommunicationFromCallAndText(
        ?Call $call = null,
        ?Text $text = null
    ): ?LeadProcessingCommunication
    {
        /** @var LeadProcessingCommunication|null $leadProcessingCommunication */
        $leadProcessingCommunication = null;

        // If we have a match for both a call and a text, find the lead with the latest communication between the two
        if ($call && $text) {
            if ($call->created_at > $text->created_at) {
                $leadProcessingCommunication = $this->getLeadProcessingCommunicationByCallId($call->id);
            } else {
                $leadProcessingCommunication = $this->getLeadProcessingCommunicationByTextId($text->id);
            }
        } else if ($call) {
            $leadProcessingCommunication = $this->getLeadProcessingCommunicationByCallId($call->id);
        } else if ($text) {
            $leadProcessingCommunication = $this->getLeadProcessingCommunicationByTextId($text->id);
        }

        return $leadProcessingCommunication;
    }

    /**
     * @param int $callId
     * @return LeadProcessingCommunication|null
     */
    protected function getLeadProcessingCommunicationByCallId(int $callId): ?LeadProcessingCommunication
    {
        /** @var LeadProcessingCommunication|null $model */
        $model = LeadProcessingCommunication::query()
            ->where(LeadProcessingCommunication::FIELD_TYPE, LeadProcessingCommunication::TYPE_CALL)
            ->where(LeadProcessingCommunication::FIELD_RELATION_ID, $callId)
            ->first();

        return $model;
    }

    /**
     * @param int $textId
     * @return LeadProcessingCommunication|null
     */
    protected function getLeadProcessingCommunicationByTextId(int $textId): ?LeadProcessingCommunication
    {
        /** @var LeadProcessingCommunication|null $model */
        $model = LeadProcessingCommunication::query()
            ->where(LeadProcessingCommunication::FIELD_TYPE, LeadProcessingCommunication::TYPE_SMS)
            ->where(LeadProcessingCommunication::FIELD_RELATION_ID, $textId)
            ->first();

        return $model;
    }

    /**
     * Handles returning the lead id from a given call or text.
     *
     * @param Call|null $call
     * @param Text|null $text
     * @return int|null
     */
    protected function getMostRecentConsumerProductIdFromCallAndText(?Call $call, ?Text $text): ?int
    {
        $communication = $this->getMostRecentLeadProcessingCommunicationFromCallAndText($call, $text);

        return $communication?->consumer_product_id;
    }

    /**
     * Handles retrieving the most recent lead processor from a call and/or text.
     *
     * @param Call|null $call
     * @param Text|null $text
     * @return LeadProcessor|null
     */
    protected function getMostRecentLeadProcessorFromCallAndText(?Call $call, ?Text $text): ?LeadProcessor
    {
        $communication = $this->getMostRecentLeadProcessingCommunicationFromCallAndText($call, $text);

        return $communication?->leadProcessor;
    }

    /**
     * Handles retrieving the most recent call by another number.
     *
     * @param string $otherNumber
     * @return Call|null
     */
    protected function getMostRecentCallByOtherNumber(string $otherNumber): ?Call
    {
        /** @var Call|null $model */
        $model = Call::query()
                     ->where(Call::FIELD_OTHER_NUMBER, $otherNumber)
                     ->orderBy(Model::CREATED_AT, 'DESC')
                     ->first();

        return $model;
    }

    /**
     * Handles retrieving the most recent text from another number.
     *
     * @param string $otherNumber
     * @return Text|null
     */
    protected function getMostRecentTextByOtherNumber(string $otherNumber): ?Text
    {
        /** @var Text|null $model */
        $model = Text::query()
                     ->where(Text::FIELD_OTHER_NUMBER, $otherNumber)
                     ->orderBy(Model::CREATED_AT, 'DESC')
                     ->first();

        return $model;
    }

    /**
     * Returns a collection of numbers that are active, and unassigned.
     *
     * @return Collection
     */
    public function getAvailableNumbers(): Collection
    {
        // Phones that have already been taken by users
        $phonesAlreadyTaken = UserPhone::withoutTrashed()->get()->pluck(UserPhone::FIELD_PHONE_ID);

        // whereDoesntHave relation does not work because that method does not take into account records soft deleted
        return Phone::query()
                    ->where(Phone::FIELD_STATUS, Phone::STATUS_ACTIVE)
                    ->where(Phone::FIELD_TYPE, PhoneType::OTHER)
                    ->whereNotIn(Phone::FIELD_ID, $phonesAlreadyTaken)
                    ->get();
    }

    /**
     * @param int $leadProcessingCommunicationRelationId
     * @param bool $callComplete
     * @param string|null $callResult
     * @return bool
     */
    public function updateCall(
        int    $leadProcessingCommunicationRelationId,
        bool   $callComplete = false,
        string $callResult = null
    ): bool
    {
        Call::query()
            ->where(Call::FIELD_ID, $leadProcessingCommunicationRelationId)
            ->update(
                collect([
                    Call::FIELD_RESULT   => $callResult,
                    Call::FIELD_CALL_END => $callComplete ? Carbon::now('UTC')->timestamp : null,
                    Call::UPDATED_AT     => Carbon::now('UTC')->timestamp
                ])->filter()->toArray()
            );

        return true;
    }

    public function createInboundCallRecord(string $reference, int $phoneId, string $otherNumber, string $result): Call
    {
        $callEnd = $result === Call::RESULT_ANSWERED ? null : Carbon::now('UTC')->timestamp;

        return Call::create([
            Call::FIELD_EXTERNAL_REFERENCE => $reference,
            Call::FIELD_PHONE_ID           => $phoneId,
            Call::FIELD_OTHER_NUMBER       => $otherNumber,
            Call::FIELD_DIRECTION          => Call::DIRECTION_INBOUND,
            Call::FIELD_RESULT             => $result,
            Call::FIELD_CALL_START         => Carbon::now('UTC')->timestamp,
            Call::FIELD_CALL_END           => $callEnd,
            Call::CREATED_AT               => Carbon::now('UTC')->timestamp,
            Call::UPDATED_AT               => Carbon::now('UTC')->timestamp
        ]);
    }

    /**
     * @param LeadProcessor|null $leadProcessor
     * @param string $phoneNumber
     * @param string $otherNumber
     * @param string $reference
     * @param string $callResult
     * @param int|null $consumerProductId
     * @return LeadProcessingCommunication
     */
    public function createInboundCall(
        ?LeadProcessor $leadProcessor,
        string         $phoneNumber,
        string         $otherNumber,
        string         $reference,
        string         $callResult,
        ?int           $consumerProductId,
    ): LeadProcessingCommunication
    {
        $call = $this->createInboundCallRecord($reference, $this->getPhoneIdFromPhoneNumber($phoneNumber), $otherNumber, $callResult);

        return $this->createLeadProcessingCallCommunication($consumerProductId, $call->id, $leadProcessor ? $leadProcessor->id : LeadProcessingCommunication::FALLBACK_PROCESSOR_ID);
    }


    /**
     * @param int|null $consumerProductId
     * @return void
     *
     * This method purposefully uses the query builder instead of calling save() on a model in order to avoid emitting
     * model events. This is to prevent the LeadProcessingCommunicationObserver::saved() method from entering an infinite loop,
     * since saved() calls this method.
     * @throws Throwable
     */
    public function updateMostRecentCommunication(?int $consumerProductId = null): void
    {
        DB::transaction(function() use ($consumerProductId) {
            LeadProcessingCommunication::query()
                ->when(!empty($consumerProductId), fn($query) => $query->where(LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID, $consumerProductId))
                ->where(LeadProcessingCommunication::FIELD_MOST_RECENT, true)
                ->update([
                    LeadProcessingCommunication::FIELD_MOST_RECENT => false
                ]);

            $communicationIdCol = LeadProcessingCommunication::TABLE . '.' . LeadProcessingCommunication::FIELD_ID;

            $subQuery = DB::table(LeadProcessingCommunication::TABLE)
                ->selectRaw("MAX({$communicationIdCol}) AS `most_recent_communication_id`")
                ->when(!empty($consumerProductId), fn($query) => $query->where(LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID, $consumerProductId))
                ->groupBy(LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID);

            LeadProcessingCommunication::query()
                ->joinSub(
                    $subQuery,
                    'most_recent_communications',
                    fn($join) => $join->on(
                        LeadProcessingCommunication::TABLE.'.'.LeadProcessingCommunication::FIELD_ID,
                        '=',
                        'most_recent_communications.most_recent_communication_id'
                    )
                )
                ->update([
                    LeadProcessingCommunication::FIELD_MOST_RECENT => true
                ]);
        });
    }

    /**
     * @param Consumer $consumer
     * @return bool
     */
    public function canCallConsumer(Consumer $consumer): bool
    {
        /** @var LeadProcessingRepository $leadProcessingRepository */
        $leadProcessingRepository = app(LeadProcessingRepository::class);

        $leadProcessorId = $leadProcessingRepository->getLeadProcessorByUserId(Auth::user()->id)?->{LeadProcessor::FIELD_ID} ?? 0;

        $mostRecentCommunication = $consumer
            ->{Consumer::RELATION_LEAD_PROCESSING_COMMUNICATIONS}
            ?->where(LeadProcessingCommunication::FIELD_MOST_RECENT, true)
            ?->first();

        if(!empty($leadProcessorId)
        && !empty($mostRecentCommunication)) {
            if($mostRecentCommunication->{LeadProcessingCommunication::FIELD_LEAD_PROCESSOR_ID} === $leadProcessorId) {
                return true;
            }

            return $mostRecentCommunication->{LeadProcessingCommunication::CREATED_AT}->diffInHours(Carbon::now('UTC')) >= 8;
        }
        else {
            $lastContacted = $mostRecentCommunication
                ?->{LeadProcessingCommunication::CREATED_AT}
                ?->timestamp;

            if(empty($lastContacted)) {
                return true;
            }

            $timeframeBuffers = LeadProcessingTimeframe::query()
                ->join(TimeframeContactBuffers::TABLE, function($join) {
                    $join->on(
                        TimeframeContactBuffers::TABLE.'.'.TimeframeContactBuffers::FIELD_TIMEFRAME_ID,
                        '=',
                        LeadProcessingTimeframe::TABLE.'.'.LeadProcessingTimeframe::FIELD_ID
                    );
                })
                ->selectRaw(implode(',', [
                    LeadProcessingTimeframe::TABLE.'.'.LeadProcessingTimeframe::FIELD_KEY." AS `key`",
                    TimeframeContactBuffers::TABLE.'.'.TimeframeContactBuffers::FIELD_CONTACT_ATTEMPT_BUFFER_HRS." * 60 * 60 AS `buffer_secs`"
                ]))
                ->pluck('buffer_secs', 'key')
                ->toArray();

            $now = CarbonImmutable::now('UTC')->timestamp;

            $timeSinceLastContact = $now - $lastContacted;

            $consumerAge = $now - $consumer->{Consumer::CREATED_AT}->timestamp;

            if($consumerAge <= self::TWENTY_FOUR_HOURS) {
                $timeframeKey = LeadProcessingTimeframe::KEY_LESS_THAN_TWENTY_FOUR_HRS;
            }
            else if($consumerAge >= (self::TWENTY_FOUR_HOURS + 1)
                && $consumerAge <= self::FORTY_EIGHT_HOURS) {
                $timeframeKey = LeadProcessingTimeframe::KEY_TWENTY_FOUR_TO_FORTY_EIGHT_HRS;
            }
            else if($consumerAge >= (self::FORTY_EIGHT_HOURS + 1)
                && $consumerAge <= self::SEVEN_DAYS) {
                $timeframeKey = LeadProcessingTimeframe::KEY_TWO_TO_SEVEN_DAYS;
            }
            else if($consumerAge >= (self::SEVEN_DAYS + 1)
                && $consumerAge <= self::THIRTY_DAYS) {
                $timeframeKey = LeadProcessingTimeframe::KEY_SEVEN_TO_THIRTY_DAYS;
            }
            else {
                $timeframeKey = LeadProcessingTimeframe::KEY_THIRTY_TO_NINETY_DAYS;
            }

            return $timeSinceLastContact >= (int) $timeframeBuffers[$timeframeKey];
        }
    }
}
