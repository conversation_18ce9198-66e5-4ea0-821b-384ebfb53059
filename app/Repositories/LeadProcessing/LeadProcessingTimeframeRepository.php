<?php

namespace App\Repositories\LeadProcessing;


use App\Models\LeadProcessingTimeframe;

class LeadProcessingTimeframeRepository
{
    /**
     * @param string $key
     * @return LeadProcessingTimeframe|null
     */
    public function getTimeframeByKey(string $key): ?LeadProcessingTimeframe
    {
        return LeadProcessingTimeframe::where(LeadProcessingTimeframe::FIELD_KEY, '=', $key)->first();
    }
}
