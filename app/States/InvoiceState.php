<?php

namespace App\States;

use App\States\Billing\Chargeback;
use App\States\Billing\Collection;
use App\States\Billing\Deleted;
use App\States\Billing\Draft;
use App\States\Billing\Failed;
use App\States\Billing\Issued;
use App\States\Billing\Paid;
use App\States\Billing\Refunded;
use App\States\Billing\Voided;
use App\States\Billing\WrittenOff;
use Spatie\ModelStates\Exceptions\InvalidConfig;
use Spatie\ModelStates\State;
use Spatie\ModelStates\StateConfig;

abstract class InvoiceState extends State
{
    protected string $title;

    /**
     * @return string
     */
    abstract public function status(): string;

    /**
     * @return string
     */
    public function getTitle(): string
    {
        return $this->title ?? static::$name ?? static::class;
    }

    /**
     * @throws InvalidConfig
     */
    public static function config(): StateConfig
    {
        return parent::config()
            ->default(Draft::class)
            ->allowTransition(Draft::class, Issued::class)
            ->allowTransition(Draft::class, Deleted::class)
            ->allowTransition(Draft::class, Failed::class)
            ->allowTransition(Issued::class, Paid::class)
            ->allowTransition(Issued::class, Collection::class)
            ->allowTransition(Issued::class, Chargeback::class)
            ->allowTransition(Issued::class, Failed::class)
            ->allowTransition(Issued::class, Voided::class)
            ->allowTransition(Issued::class, WrittenOff::class)
            ->allowTransition(Paid::class, Refunded::class)
            ->allowTransition(Paid::class, Chargeback::class)
            ->allowTransition(Paid::class, Failed::class)
            ->allowTransition(Voided::class, Deleted::class)
            ->allowTransition(Failed::class, Collection::class)
            ->allowTransition(Failed::class, Paid::class)
            ->allowTransition(Failed::class, Voided::class)
            ->allowTransition(Failed::class, WrittenOff::class)
            ->allowTransition(Failed::class, Chargeback::class)
            ->allowTransition(Chargeback::class, Refunded::class)
            ->registerState([
                Draft::class,
                Issued::class,
                Paid::class,
                Refunded::class,
                Deleted::class,
                Voided::class,
                Failed::class,
                Deleted::class,
                Collection::class,
                Chargeback::class,
                WrittenOff::class,
            ]);
    }
}
