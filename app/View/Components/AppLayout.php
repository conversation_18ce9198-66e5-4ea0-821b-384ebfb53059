<?php

namespace App\View\Components;

use Illuminate\View\Component;

class AppLayout extends Component
{
    /**
     * @var false
     */
    public bool $loadCbScript;
    public bool $loadFlowClientScript;

    public function __construct(bool $loadCbScript = false, bool $loadFlowClientScript = false)
    {
        $this->loadFlowClientScript = $loadFlowClientScript;
        $this->loadCbScript = $loadCbScript;
    }

    /**
     * Get the view / contents that represents the component.
     *
     * @return \Illuminate\View\View
     */
    public function render()
    {
        return view('layouts.app');
    }
}
