<?php

namespace App\Console\Commands;

use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductData;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class MigrateIpAddressData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate-ip-addresses';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate IP Addresses from EloquentQuote to ConsumerProduct';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $bar = $this->output->createProgressBar(ConsumerProduct::query()->whereNull(ConsumerProduct::FIELD_IP_ADDRESS)->count());
        $bar->start();

        DB::table(ConsumerProduct::TABLE)
            ->select([
                         ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ID .' as id',
                         ConsumerProductData::TABLE.'.'.ConsumerProductData::VIRTUAL_FIELD_IP_ADDRESS .' as ip_address'
                     ])
            ->join(
                ConsumerProductData::TABLE,
                ConsumerProductData::TABLE.'.'.ConsumerProductData::FIELD_ID,
                ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CONSUMER_PRODUCT_DATA_ID
            )
            ->whereNull(ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_IP_ADDRESS)
            ->orderByDesc(ConsumerProduct::TABLE.'.'.ConsumerProduct::CREATED_AT)
            ->chunk(5000, function($consumerProducts) use($bar) {
                $data = [];
                foreach ($consumerProducts as $consumerProduct) {

                    $data[] = [
                        ConsumerProduct::FIELD_ID         => $consumerProduct->id,
                        ConsumerProduct::FIELD_IP_ADDRESS => $consumerProduct->ip_address,
                    ];

                    $bar->advance();
                }

                DB::table(ConsumerProduct::TABLE)->upsert($data, ConsumerProduct::FIELD_ID);
            });

        $bar->finish();
    }
}
