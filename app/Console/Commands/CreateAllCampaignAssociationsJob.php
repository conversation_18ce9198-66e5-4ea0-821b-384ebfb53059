<?php

namespace App\Console\Commands;

use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Product;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\Company;
use App\Models\Odin\IndustryService;
use App\Services\QueueHelperService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Illuminate\Support\Facades\DB;

class CreateAllCampaignAssociationsJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /**
     * @param array $companyIds
     */
    public function __construct(
        private array $companyIds,
    )
    {
        $this->onConnection(QueueHelperService::QUEUE_CONNECTION);
        $this->onQueue(QueueHelperService::QUEUE_NAME_LEGACY_MIGRATION);
    }

    /**
     * @return void
     */
    public function handle(): void
    {
        DB::beginTransaction();

        Company::query()
            ->with(Company::RELATION_CAMPAIGNS)
            ->whereIn(Company::FIELD_ID, $this->companyIds)
            ->get()
            ->each(fn(Company $company) => $this->createAssociationsForCompany($company));

        DB::commit();
    }

    /**
     * @param Company $company
     * @return void
     */
    private function createAssociationsForCompany(Company $company): void
    {
        /** @var IndustryService $service */
        $service = $company->services()->first();
        if (!$service) {
            logger()->warning("Company $company->id does not have an IndustryService attached, no product_campaigns could be created.");
            return;
        }

        $campaigns = $company->campaigns()->where(LeadCampaign::DELETED_AT, null)->get();

        /** @var Product $product */
        $product = Product::query()->where(Product::FIELD_NAME, \App\Enums\Odin\Product::LEAD)->firstOrFail();

        /** @var LeadCampaign $campaign */
        foreach($campaigns as $campaign) {
            ProductCampaign::query()->firstOrCreate(
                [ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID => $campaign->id],
                [
                    ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID => $campaign->id,
                    ProductCampaign::FIELD_INDUSTRY_SERVICE_ID => $service->id,
                    ProductCampaign::FIELD_PRODUCT_ID => $product->id,
                    ProductCampaign::FIELD_COMPANY_ID => $company->id
                ]
            );
        }

    }
}
