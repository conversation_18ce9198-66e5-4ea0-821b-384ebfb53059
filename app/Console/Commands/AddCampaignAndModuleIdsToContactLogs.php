<?php

namespace App\Console\Commands;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignContactDeliveryLog;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModule;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleContact;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\ProductAssignment;
use Illuminate\Console\Command;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Laravel\Prompts\Progress;
use Throwable;
use function Laravel\Prompts\clear;
use function Laravel\Prompts\progress;

class AddCampaignAndModuleIdsToContactLogs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update-ids:contact-delivery-logs {--chunk-size=5000 : The number of batches/jobs to create}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add missing ids to campaign contact delivery logs.';

    protected int $chunkSize;
    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $this->chunkSize = $this->option('chunk-size') ?: 5000;

        clear();
        $filled = $this->backfillIds();
        if ($filled) {
            $repairedEmail = $this->repairEmailLogs();
            if ($repairedEmail) {
                $repairedPhone = $this->repairPhoneLogs();
                if ($repairedPhone) {
                    $this->info("\nAll processes completed.\n");

                    return Command::SUCCESS;
                }
            }
        }

        return Command::FAILURE;
    }

    /**
     * @return bool
     */
    private function backfillIds(): bool
    {
        $this->info("Backfilling campaign_id in contact logs...");

        $query = DB::table(CompanyCampaignContactDeliveryLog::TABLE)
            ->select([
                DB::raw(CompanyCampaignContactDeliveryLog::TABLE .'.'. CompanyCampaignContactDeliveryLog::FIELD_ID),
                DB::raw(CompanyCampaignDeliveryModule::TABLE .'.'. CompanyCampaignDeliveryModule::FIELD_ID . ' AS module_id'),
                DB::raw(CompanyCampaignDeliveryModule::TABLE .'.'. CompanyCampaignDeliveryModule::FIELD_CAMPAIGN_ID . ' AS campaign_id'),
            ])->join(CompanyCampaignDeliveryModuleContact::TABLE, CompanyCampaignDeliveryModuleContact::TABLE .'.'. CompanyCampaignDeliveryModuleContact::FIELD_ID, '=', CompanyCampaignContactDeliveryLog::TABLE .'.'. CompanyCampaignContactDeliveryLog::FIELD_CONTACT_MODULE_ID)
            ->join(CompanyCampaignDeliveryModule::TABLE, CompanyCampaignDeliveryModule::TABLE .'.'. CompanyCampaignDeliveryModule::FIELD_ID, '=', CompanyCampaignDeliveryModuleContact::TABLE .'.'. CompanyCampaignDeliveryModuleContact::FIELD_MODULE_ID)
            ->where(CompanyCampaignContactDeliveryLog::TABLE .'.'. CompanyCampaignContactDeliveryLog::FIELD_CAMPAIGN_ID, '=', 0);

        $count = $query->count();

        if (!$count) {
            $this->line("No rows needed updating.");
            return true;
        }

        $chunks = ceil($count/$this->chunkSize);

        $this->info("\nUpdating $count rows in $chunks chunks.\n");
        $progressBar = progress('Processing chunks', $chunks);

        return $query->orderBy(CompanyCampaignContactDeliveryLog::TABLE .'.'. CompanyCampaignContactDeliveryLog::FIELD_ID)
            ->chunkById($this->chunkSize, function(Collection $logs) use ($progressBar) {
                return $this->handleChunk($logs, $progressBar);
            }, CompanyCampaignContactDeliveryLog::TABLE .'.'. CompanyCampaignContactDeliveryLog::FIELD_ID, CompanyCampaignContactDeliveryLog::FIELD_ID);
    }

    /**
     * @return bool
     */
    private function repairEmailLogs(): bool
    {
        $this->info("\nRepairing email logs...");

        $query = $this->getRepairQuery(0);

        $count = $query->count();
        if (!$count) {
            $this->line("No email logs found needing repair.");

            return true;
        }

        $chunks = ceil($count/$this->chunkSize);

        $this->info("\nUpdating $count rows in $chunks chunks.\n");
        $progressBar = progress('Processing chunks', $chunks);

        return $query->orderBy(CompanyCampaignContactDeliveryLog::FIELD_ID)
            ->chunkById($this->chunkSize, function(Collection $logs) use ($progressBar) {
                return $this->handleChunk($logs, $progressBar);
            }, CompanyCampaignContactDeliveryLog::TABLE .'.'. CompanyCampaignContactDeliveryLog::FIELD_ID, CompanyCampaignContactDeliveryLog::FIELD_ID);
    }

    /**
     * @return bool
     */
    private function repairPhoneLogs(): bool
    {
        $this->info("\nRepairing phone logs...");

        $query = $this->getRepairQuery(1);

        $count = $query->count();
        if (!$count) {
            $this->line("No phone logs found needing repair.");

            return true;
        }

        $chunks = ceil($count/$this->chunkSize);

        $this->info("\nUpdating $count rows in $chunks chunks.\n");
        $progressBar = progress('Processing chunks', $chunks);

        return $query->orderBy(CompanyCampaignContactDeliveryLog::FIELD_ID)
            ->chunkById($this->chunkSize, function(Collection $logs) use ($progressBar) {
                return $this->handleChunk($logs, $progressBar);
            }, CompanyCampaignContactDeliveryLog::TABLE .'.'. CompanyCampaignContactDeliveryLog::FIELD_ID, CompanyCampaignContactDeliveryLog::FIELD_ID);
    }

    private function getRepairQuery(int $contactType): Builder
    {
        $userColumn = $contactType === 0
            ? CompanyUser::TABLE .'.'. CompanyUser::FIELD_EMAIL
            : CompanyUser::TABLE .'.'. CompanyUser::FIELD_CELL_PHONE;

        return DB::table(CompanyCampaignContactDeliveryLog::TABLE)
            ->select([
                DB::raw(CompanyCampaignContactDeliveryLog::TABLE .'.'. CompanyCampaignContactDeliveryLog::FIELD_ID),
                DB::raw(CompanyCampaignDeliveryModule::TABLE .'.'. CompanyCampaignDeliveryModule::FIELD_ID . ' AS module_id'),
                DB::raw(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID . ' AS campaign_id'),
            ])->distinct()
            ->leftJoin(CompanyCampaignDeliveryModuleContact::TABLE, CompanyCampaignDeliveryModuleContact::TABLE .'.'. CompanyCampaignDeliveryModuleContact::FIELD_ID, '=', CompanyCampaignContactDeliveryLog::TABLE .'.'. CompanyCampaignContactDeliveryLog::FIELD_CONTACT_MODULE_ID)
            ->join(CompanyUser::TABLE, fn(JoinClause $join) =>
                $join->on(DB::raw(CompanyCampaignContactDeliveryLog::FIELD_PAYLOAD . "->>'$.to'"), '=', $userColumn)
                    ->where(CompanyCampaignContactDeliveryLog::TABLE .'.'. CompanyCampaignContactDeliveryLog::FIELD_TYPE, $contactType)
            )->join(ProductAssignment::TABLE, fn(JoinClause $join) =>
                $join->on(CompanyUser::TABLE .'.'. CompanyUser::FIELD_COMPANY_ID, '=', ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_COMPANY_ID)
                    ->on(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, '=', CompanyCampaignContactDeliveryLog::TABLE .'.'. CompanyCampaignContactDeliveryLog::FIELD_CONSUMER_PRODUCT_ID)
            )->join(Budget::TABLE, Budget::TABLE .'.'. Budget::FIELD_ID, '=', ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_BUDGET_ID)
            ->join(BudgetContainer::TABLE, BudgetContainer::TABLE .'.'. BudgetContainer::FIELD_ID, '=', Budget::TABLE .'.'. Budget::FIELD_BUDGET_CONTAINER_ID)
            ->join(CompanyCampaign::TABLE, CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID, '=', BudgetContainer::TABLE .'.'. BudgetContainer::FIELD_COMPANY_CAMPAIGN_ID)
            ->join(CompanyCampaignDeliveryModule::TABLE, CompanyCampaignDeliveryModule::TABLE .'.'. CompanyCampaignDeliveryModule::FIELD_CAMPAIGN_ID, '=', CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID)
            ->whereNull(CompanyCampaignDeliveryModuleContact::TABLE .'.'. CompanyCampaignDeliveryModuleContact::FIELD_ID)
            ->where(CompanyCampaignContactDeliveryLog::TABLE .'.'. CompanyCampaignContactDeliveryLog::FIELD_TYPE, $contactType);
    }

    /**
     * @param Collection $logs
     * @param Progress $progressBar
     * @return bool
     */
    private function handleChunk(Collection $logs, Progress &$progressBar): bool
    {
        DB::beginTransaction();
        $upsertData = $logs->reduce(function(array $output, $log) {
            $output[] = [
                CompanyCampaignContactDeliveryLog::FIELD_ID => $log->id,
                CompanyCampaignContactDeliveryLog::FIELD_CAMPAIGN_ID => $log->campaign_id,
                CompanyCampaignContactDeliveryLog::FIELD_MODULE_ID => $log->module_id,
            ];
            return $output;
        }, []);

        try {
            DB::table(CompanyCampaignContactDeliveryLog::TABLE)
                ->upsert($upsertData, [], [CompanyCampaignContactDeliveryLog::FIELD_CAMPAIGN_ID, CompanyCampaignContactDeliveryLog::FIELD_MODULE_ID]);
        }
        catch (Throwable $e) {
            $this->error($e->getMessage());
            DB::rollBack();

            return false;
        }
        DB::commit();

        $progressBar->advance();
        return true;
    }
}