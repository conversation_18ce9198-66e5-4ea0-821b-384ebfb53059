<?php

namespace App\Console\Commands;

use App\DTO\Billing\PaymentMethods\PaymentMethodDTO;
use App\Enums\Billing\BillingLogLevel;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Enums\Billing\PaymentMethodServices;
use App\Models\Legacy\EloquentCompanyPaymentProfile;
use App\Models\Legacy\EloquentConfiguration;
use App\Models\Odin\Company;
use App\Repositories\Billing\CompanyPaymentMethodRepository;
use App\Repositories\Odin\ProductAssignmentRepository;
use App\Services\Billing\BillingLogService;
use App\Services\Billing\BillingProfile\BillingProfileService;
use App\Services\PaymentGateway\PaymentGatewayServiceFactory;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class ImportCreditCardsFromStripe extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import-credit-cars-from-stripe {--company-id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command imports credit cards from stripe';

    const int DEFAULT_THRESHOLD = 400;

    /**
     * Create a new command instance.
     */
    public function __construct(
        protected BillingProfileService $billingProfileService,
        protected CompanyPaymentMethodRepository $companyPaymentMethodRepository,
        protected ProductAssignmentRepository $productAssignmentRepository
    )
    {
        parent::__construct();
    }

    /**
     * @return void
     */
    public function handle(): void
    {
        $companyId = $this->option('company-id');

        if (!$companyId) {
            $this->error('Company id is required.');
            return;
        }

        $companyIds = collect(explode(',', $companyId))
            ->map(fn(string $item) => trim($item))
            ->filter()
            ->unique();

        $this->log('Start migrating company ids: ' . json_encode($companyIds));

        foreach ($companyIds as $companyId) {
            try {
                $this->process($companyId);
            } catch (Exception $exception) {
                $this->log(
                    message: $exception->getMessage(),
                    level  : BillingLogLevel::ERROR,
                    context: [
                        'error' => $exception
                    ]
                );
            }
        }

        $this->log("Migration completed.");
    }

    /**
     * @param int $companyId
     * @return void
     */
    public function process(int $companyId): void
    {
        $company = Company::query()->findOrFail($companyId);

        $this->newLine();
        $this->log('Migrating ' . $company->name);

        $this->log("Migrating billing profiles...");

        $paymentProfiles = $this->getPaymentProfiles($company->{Company::FIELD_LEGACY_ID});

        $this->log("Legacy Payment profiles: " . $paymentProfiles->count());

        $thresholdInDollars = $this->getChargeThreshold($company->{Company::FIELD_LEGACY_ID});
        $this->log("Charge threshold: $" . $thresholdInDollars);

        if ($paymentProfiles->count() > 0) {
            foreach ($paymentProfiles as $profile) {
                $this->processPaymentProfile(
                    company           : $company,
                    profile           : $profile,
                    thresholdInDollars: $thresholdInDollars
                );
            }
        }
    }


    /**
     * @param int $legacyCompanyId
     * @return Collection<EloquentCompanyPaymentProfile>
     */
    protected function getPaymentProfiles(int $legacyCompanyId): Collection
    {
        return EloquentCompanyPaymentProfile::query()
            ->where(EloquentCompanyPaymentProfile::COMPANY_ID, $legacyCompanyId)
            ->get();
    }


    /**
     * @param int $legacyCompanyId
     * @return int
     */
    protected function getChargeThreshold(int $legacyCompanyId): int
    {
        return EloquentConfiguration::query()
            ->where(EloquentConfiguration::NAME, 'quote_chargeable_threshold')
            ->where(EloquentConfiguration::REL_ID, $legacyCompanyId)
            ->where(EloquentConfiguration::REL_TYPE, EloquentConfiguration::REL_TYPE_COMPANY_ID)
            ->first()?->value ?? self::DEFAULT_THRESHOLD;
    }

    /**
     * @param Company $company
     * @param EloquentCompanyPaymentProfile $profile
     * @param int $thresholdInDollars
     * @return void
     */
    protected function processPaymentProfile(
        Company $company,
        EloquentCompanyPaymentProfile $profile,
        int $thresholdInDollars
    ): void
    {
        $customerCode = $profile->{EloquentCompanyPaymentProfile::PAYMENT_PROVIDER_IDENTIFIER};
        $paymentMethods = (PaymentGatewayServiceFactory::make(PaymentMethodServices::STRIPE))
            ->getPaymentMethods($customerCode);

        $this->log("Found " . $paymentMethods->count() . " payment methods for customer: $customerCode");

        $hasBillingProfile = (bool)$this->billingProfileService->getBillingProfile(
            companyId    : $company->id,
            paymentMethod: PaymentMethodServices::STRIPE->value
        );

        if (!$hasBillingProfile) {
            $this->log('Creating billing profile for stripe');
            $this->billingProfileService->createProfileWithDefaultConfiguration(
                paymentMethod     : PaymentMethodServices::STRIPE,
                companyId         : $company->id,
                default           : true,
                thresholdInDollars: $thresholdInDollars,
            );
        } else {
            $this->log('Company already has a billing profile for stripe');
        }

        $this->log('Importing ' . $paymentMethods->count() . ' credit cards');

        foreach ($paymentMethods as $paymentMethod) {
            $this->migratePaymentMethod(
                paymentMethod: $paymentMethod,
                companyId    : $company->id,
                customerCode : $customerCode
            );
        }
    }

    /**
     * @param PaymentMethodDTO $paymentMethod
     * @param string $companyId
     * @param string $customerCode
     * @return void
     */
    protected function migratePaymentMethod(
        PaymentMethodDTO $paymentMethod,
        string $companyId,
        string $customerCode
    ): void
    {
        $exists = (bool)$this->companyPaymentMethodRepository->getByExternalId($paymentMethod->getId());

        if (!$exists) {
            $this->log('Importing credit card ' . $paymentMethod->getId());
            $this->companyPaymentMethodRepository->create(
                companyId                      : $companyId,
                type                           : PaymentMethodServices::STRIPE,
                addedByType                    : InvoiceEventAuthorTypes::SYSTEM->value,
                isDefault                      : $paymentMethod->getIsDefault(),
                paymentGatewayPaymentMethodCode: $paymentMethod->getId(),
                paymentGatewayClientCode       : $customerCode,
            );
        } else {
            $this->log('Credit card already exists ' . $paymentMethod->getId());
        }
    }

    /**
     * @param string $message
     * @param BillingLogLevel|null $level
     * @param array $context
     * @return void
     */
    protected function log(
        string $message,
        ?BillingLogLevel $level = BillingLogLevel::INFO,
        array $context = []
    ): void
    {
        $this->info($message);

        BillingLogService::log(
            message  : $message,
            level    : $level,
            namespace: 'migrate_company_to_billing_v2',
            context  : [
                ...$context,
                'company_id' => $this->option('company-id')
            ]
        );
    }
}
