<?php

namespace App\Console\Commands;

use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\EloquentUser;
use App\Models\SuccessManager;
use App\Models\SuccessManagerClient;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;

class CreateInitialSuccessManagersAndClientsFromLegacy extends Command
{
    /**
     * The name and signature of the console command.
     * @var string
     */
    protected $signature = 'create-success-managers-and-clients-from-legacy';

    /**
     * The console command description.
     * @var string
     */
    protected $description = 'Create Users, SuccessManagers, and SuccessManagerClients based on legacy relationships';

    /**
     * Execute the console command.
     * @return void
     */
    public function handle(): void
    {
        $this->importAccountManagers()
            ->importClients();
    }

    /**
     * Handles the import of account managers.
     *
     * @return CreateInitialSuccessManagersAndClientsFromLegacy
     */
    protected function importAccountManagers(): self
    {
        $ids = $this->getBaseQuery()
            ->select(EloquentCompany::SALES_CONSULTANT_ID)
            ->groupBy(EloquentCompany::SALES_CONSULTANT_ID)
            ->get()
            ->pluck(EloquentCompany::SALES_CONSULTANT_ID);

        $legacyUsers = EloquentUser::query()->whereIn(EloquentUser::ID, $ids)->get();
        $users = $legacyUsers->map(fn(EloquentUser $user) => User::query()->updateOrCreate(
            [User::FIELD_LEGACY_USER_ID => $user->userid],
            [
                User::FIELD_NAME  => $user->name,
                User::FIELD_EMAIL => $user->email,
                User::FIELD_LEGACY_USER_ID => $user->userid
            ]
        ));

        /** @var User $user */
        foreach ($users as $user) {
            //giving the success managers account managers permissions by default
            $user->assignRole('account-manager');
            SuccessManager::query()->updateOrCreate(
                [SuccessManager::FIELD_USER_ID => $user->id],
                [
                    SuccessManager::FIELD_TYPE => SuccessManager::TYPE_JUNIOR
                ]
            );
        }
        return $this;
    }

    /**
     * Returns all account managers, keyed by their legacy id.
     *
     * @return array
     */
    protected function getSuccessManagersKeyedByLegacyId(): array
    {
        $managers = SuccessManager::query()->with(SuccessManager::RELATION_USER)->get();

        return $managers->filter(fn($manager) => !!$manager->user->legacy_user_id)
            ->pluck(SuccessManager::FIELD_ID, SuccessManager::RELATION_USER . '.' . User::FIELD_LEGACY_USER_ID)
            ->toArray();
    }

    /**
     * Handles the import of clients.
     *
     * @return CreateInitialSuccessManagersAndClientsFromLegacy
     */
    protected function importClients(): self
    {
        $successManagers = $this->getSuccessManagersKeyedByLegacyId();

        $this->getBaseQuery()->chunk(100, function ($companies) use ($successManagers) {
            /** @var EloquentCompany $company */
            foreach ($companies as $company) {
                // create client
                if(isset($successManagers[$company->{EloquentCompany::SALES_CONSULTANT_ID}])) {
                    SuccessManagerClient::query()->updateOrCreate(
                        [
                            SuccessManagerClient::FIELD_SUCCESS_MANAGER_ID => $successManagers[$company->{EloquentCompany::SALES_CONSULTANT_ID}],
                            SuccessManagerClient::FIELD_COMPANY_REFERENCE  => $company->reference,
                        ],
                        [
                            SuccessManagerClient::FIELD_SUCCESS_MANAGER_ID => $successManagers[$company->{EloquentCompany::SALES_CONSULTANT_ID}],
                            SuccessManagerClient::FIELD_COMPANY_REFERENCE  => $company->reference,
                            SuccessManagerClient::FIELD_TOTAL_SPEND        => $this->getTotalInvoicedSpendForCompany($company),
                            SuccessManagerClient::FIELD_STATUS             => SuccessManagerClient::STATUS_ACTIVE
                        ]
                    );

                    echo "Create SuccessManagerClient:\n   - UserID: {$successManagers[$company->{EloquentCompany::SALES_CONSULTANT_ID}]}\n   - Company: $company->companyname\n";
                }
            }
        });

        return $this;
    }

    /**
     * Returns the base query for this script.
     *
     * @return Builder
     */
    protected function getBaseQuery(): Builder
    {
        return EloquentCompany::query()
            ->whereNotNull(EloquentCompany::SALES_CONSULTANT_ID)
            ->where(EloquentCompany::ACCOUNT_MANAGER_ID, '<>', 0)
            ->whereIn(EloquentCompany::STATUS, [EloquentCompany::STATUS_ACTIVE, EloquentCompany::STATUS_PRESALES,
                    EloquentCompany::STATUS_REGISTERING, EloquentCompany::STATUS_PENDING, EloquentCompany::STATUS_SUSPENDED, EloquentCompany::STATUS_COLLECTION]
            );
    }

    /**
     * @param EloquentCompany $company
     * @return float
     */
    private function getTotalInvoicedSpendForCompany(EloquentCompany $company): float
    {
        return EloquentQuoteCompany::query()
            ->where(EloquentQuoteCompany::COMPANY_ID, $company->companyid)
            ->where(EloquentQuoteCompany::CHARGEABLE, 1)
            ->where(EloquentQuoteCompany::DELIVERED, 1)
            ->where(EloquentQuoteCompany::CHARGEABLE, 1)
            ->whereNotNull(EloquentQuoteCompany::INVOICE_ITEM_ID)
            ->where(EloquentQuoteCompany::INVOICE_ITEM_ID, '<>', 0)
            ->sum(EloquentQuoteCompany::COST);
    }
}
