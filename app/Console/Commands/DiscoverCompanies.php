<?php

namespace App\Console\Commands;

use App\Models\CompanyDiscovery\AddressDiscoveryStatus;
use App\Models\CompanyDiscovery\CompanyDiscoveryStatus;
use App\Models\CompanyDiscovery\USZipCodeDiscoveryStatus;
use App\Models\Odin\Industry;
use App\Models\USZipCode;
use App\Repositories\Odin\CompanyRepository;
use App\Services\CompanyDiscoveryService;
use App\Services\ExternalSearch\GooglePlacesService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class DiscoverCompanies extends Command
{
    const CHOICE_YES = 'Yes';
    const CHOICE_NO  = 'No';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'discover:companies';

    /** @var string|null $placeType */
    protected ?string $placeType = USZipCodeDiscoveryStatus::DEFAULT_PLACE_TYPE;

    /** @var string|null $placeKeyword */
    protected ?string $placeKeyword = USZipCodeDiscoveryStatus::DEFAULT_PLACE_KEYWORD;

    /** @var int $radius */
    protected int $radius = USZipCodeDiscoveryStatus::DEFAULT_RADIUS;

    /** @var int|null $zipLimit */
    protected int|null $zipLimit = null;

    /** @var Industry $industry */
    protected Industry $industry;

    /** @var string $discoveryReference */
    protected string $discoveryReference;

    /** @var array $nearbySearchRequestArray */
    protected array $nearbySearchRequestArray = [];

    /** @var CompanyRepository $companyRepository */
    protected CompanyRepository $companyRepository;

    /** @var CompanyDiscoveryService $companyDiscoveryService */
    protected CompanyDiscoveryService $companyDiscoveryService;

    /** @var GooglePlacesService $placesService */
    protected GooglePlacesService $placesService;

    /** @var string $currentPlaceId */
    protected string $currentPlaceId;

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handles fetching of companies.';

    /**
     * @param CompanyRepository $companyRepository
     * @param CompanyDiscoveryService $companyDiscoveryService
     * @param GooglePlacesService $placesService
     */
    public function __construct(CompanyRepository $companyRepository, CompanyDiscoveryService $companyDiscoveryService, GooglePlacesService $placesService)
    {
        parent::__construct();
        $this->companyRepository       = $companyRepository;
        $this->companyDiscoveryService = $companyDiscoveryService;
        $this->placesService           = $placesService;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        $unfinishedIndustryIds = $this->getUnfinishedIndustryIds();

        if ($unfinishedIndustryIds) {
            $existingDiscovery = $this->choice('Are you continuing a paused discovery or adding an additional thread?', [self::CHOICE_NO, self::CHOICE_YES]);

            if ($existingDiscovery === self::CHOICE_YES) {
                $this->setExistingDiscoveryFields($unfinishedIndustryIds);
                $this->processRemainingUSZipCodes();
                $this->getDetailsForRemainingPlaces();
                return;
            }
        }

        $this->startNewDiscovery();
    }

    /**
     * @return void
     */
    private function startNewDiscovery(): void
    {
        $this->setNewDiscoveryChoices();
        $this->createZipCodeStatusRecords();
        $this->processRemainingUSZipCodes();
        $this->getDetailsForRemainingPlaces();
    }

    /**
     * @return void
     */
    private function getDetailsForRemainingPlaces(): void
    {
        $this->info("\nGetting details for places");

        $numberOfLocationsToProcess = $this->getNumberOfLocationsToProcess();

        try {
            $places = $this->getPlacesToProcess($numberOfLocationsToProcess);

            $bar = $this->output->createProgressBar($places->count());
            $bar->start();

            $places->map(function ($place) use ($bar) {
                $place->refresh();

                $this->currentPlaceId = $place->address->place_id;

                if (!$place->checked) {
                    $this->companyDiscoveryService->processPlaceByAddress($place->address);
                    $place->update([
                        AddressDiscoveryStatus::FIELD_CHECKED => true
                    ]);
                }

                $bar->advance();
            });

            $countOfPlacesLeft = $this->countOfPlacesLeft();

            if($countOfPlacesLeft) {
                $choice = $this->choice("There are $countOfPlacesLeft places left to process. Would you like to continue processing?", [self::CHOICE_NO, self::CHOICE_YES]);

                if ($choice === self::CHOICE_YES) {
                    $this->getDetailsForRemainingPlaces();
                }
            }

        } catch (\Exception $e) {
            logger()->error("Failed to execute company discovery contract. Error: {$e->getMessage()}, {$this->currentPlaceId}");
        }
    }

    /**
     * @return int
     */
    private function countOfPlacesLeft(): int
    {
        return AddressDiscoveryStatus::with(AddressDiscoveryStatus::RELATION_ADDRESS)
            ->where(AddressDiscoveryStatus::TABLE . '.' . AddressDiscoveryStatus::FIELD_CHECKED, '<>', true)
            ->where(AddressDiscoveryStatus::TABLE . '.' . AddressDiscoveryStatus::FIELD_DISCOVERY_REFERENCE, $this->discoveryReference)
            ->get()
            ->count();
    }

    /**
     * @param int|null $limit
     * @return Collection|AddressDiscoveryStatus[]|null
     */
    private function getPlacesToProcess(int $limit = null): Collection|AddressDiscoveryStatus|null
    {
        $query = AddressDiscoveryStatus::with(AddressDiscoveryStatus::RELATION_ADDRESS)
            ->where(AddressDiscoveryStatus::TABLE . '.' . AddressDiscoveryStatus::FIELD_CHECKED, '<>', true)
            ->where(AddressDiscoveryStatus::TABLE . '.' . AddressDiscoveryStatus::FIELD_DISCOVERY_REFERENCE, $this->discoveryReference)
            ->inRandomOrder();

        if($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    /**
     * @return \Illuminate\Database\Eloquent\Builder[]|Collection|USZipCode[]
     */
    private function getUsZipCodesToDiscover(): Collection|USZipCode
    {
        $query = USZipCode::query()
            ->where(USZipCode::FIELD_ZIP_TYPE, USZipCode::DEFAULT_ZIP_TYPE)
            ->where(USZipCode::FIELD_CITY_TYPE, USZipCode::DEFAULT_CITY_TYPE)
            ->select([USZipCode::FIELD_ZIP_CODE, USZipCode::FIELD_LATITUDE, USZipCode::FIELD_LONGITUDE]);

        if($this->zipLimit) {
            return $query->inRandomOrder()->limit($this->zipLimit)->get();
        }

        return $query->get();
    }

    /**
     * @return void
     */
    private function createZipCodeStatusRecords(): void
    {
        $this->info('Creating status records for each zip code');

        $usZipCodes = $this->getUsZipCodesToDiscover();

        $insertArray = [];

        foreach ($usZipCodes as $usZipCode) {
            $this->makeInsertArray($insertArray, $usZipCode);
        }

        DB::transaction(function () use ($insertArray) {
            foreach (array_chunk($insertArray, 2000) as $chunkInsert) {
                USZipCodeDiscoveryStatus::insert($chunkInsert);
            }
        });
    }

    /**
     * @param array $insertArray
     * @param USZipCode $usZipCode
     * @return void
     */
    private function makeInsertArray(array &$insertArray, USZipCode $usZipCode): void
    {
        $insertArray[] = [
            USZipCodeDiscoveryStatus::FIELD_ZIP_CODE            => $usZipCode->{USZipCodeDiscoveryStatus::FIELD_ZIP_CODE},
            USZipCodeDiscoveryStatus::FIELD_LATITUDE            => $usZipCode->{USZipCodeDiscoveryStatus::FIELD_LATITUDE},
            USZipCodeDiscoveryStatus::FIELD_LONGITUDE           => $usZipCode->{USZipCodeDiscoveryStatus::FIELD_LONGITUDE},
            USZipCodeDiscoveryStatus::FIELD_RADIUS              => $this->radius,
            USZipCodeDiscoveryStatus::FIELD_PLACE_TYPE          => $this->placeType,
            USZipCodeDiscoveryStatus::FIELD_PLACE_KEYWORD       => $this->placeKeyword,
            USZipCodeDiscoveryStatus::FIELD_INDUSTRY_ID         => $this->industry->id,
            USZipCodeDiscoveryStatus::FIELD_DISCOVERY_REFERENCE => $this->discoveryReference
        ];
    }

    /**
     * @return array
     */
    private function getUnfinishedIndustryIds(): array
    {
        return USZipCodeDiscoveryStatus::query()->where(USZipCodeDiscoveryStatus::FIELD_CHECKED, '<>', true)
            ->groupBy(USZipCodeDiscoveryStatus::FIELD_INDUSTRY_ID)
            ->get()
            ->pluck(USZipCodeDiscoveryStatus::FIELD_INDUSTRY_ID)
            ->toArray();
    }

    /**
     * @return Collection|USZipCodeDiscoveryStatus[]|null
     */
    private function getLocationsToDiscover(): Collection|USZipCodeDiscoveryStatus|null
    {
        return USZipCodeDiscoveryStatus::query()
            ->where(USZipCodeDiscoveryStatus::FIELD_CHECKED, '<>', true)
            ->where(USZipCodeDiscoveryStatus::FIELD_INDUSTRY_ID, $this->industry->id)
            ->get();
    }

    /**
     * @return void
     */
    private function processRemainingUSZipCodes(): void
    {
        $this->info("Processing locations");
        $this->setNearbySearchRequestArray();

        try {
            $locations = $this->getLocationsToDiscover();

            $bar = $this->output->createProgressBar($locations->count());
            $bar->start();

            $locations->shuffle()->map(function ($location) use ($bar) {
                $location->refresh();

                if (!$location->checked) {
                    $this->processLocation($location);
                }

                $bar->advance();
            });
        } catch (\Exception $e) {
            logger()->error("Failed to execute company discovery contract. Error: {$e->getMessage()}");
        }
    }

    /**
     * @return void
     */
    private function setNearbySearchRequestArray(): void
    {
        $this->nearbySearchRequestArray = [
            GooglePlacesService::API_FIELD_KEY    => config('services.google.company_discovery.places_api_key'),
            GooglePlacesService::API_FIELD_RADIUS => $this->radius,
        ];

        if ($this->placeType) {
            $this->nearbySearchRequestArray = array_merge($this->nearbySearchRequestArray, [
                GooglePlacesService::API_FIELD_TYPE => $this->placeType
            ]);
        }

        if ($this->placeKeyword) {
            $this->nearbySearchRequestArray = array_merge($this->nearbySearchRequestArray, [
                GooglePlacesService::API_FIELD_KEYWORD => $this->placeKeyword
            ]);
        }
    }

    /**
     * @param USZipCodeDiscoveryStatus $location
     * @return array
     */
    private function getCompaniesInLocation(USZipCodeDiscoveryStatus $location): array
    {
        $url = GooglePlacesService::BASE_API_URI . GooglePlacesService::NEARBY_SEARCH_API_URI;

        $requestArray = array_merge($this->nearbySearchRequestArray, [
            GooglePlacesService::API_FIELD_LOCATION => "{$location->latitude},{$location->longitude}"
        ]);

        $response = Http::get($url, $requestArray);

        return $response->json()[GooglePlacesService::API_KEY_RESULTS];
    }

    /**
     * @param USZipCodeDiscoveryStatus $location
     * @return void
     */
    private function processLocation(USZipCodeDiscoveryStatus $location): void
    {
        $companies = $this->placesService->getCompaniesInLocation($location->latitude, $location->longitude, $this->nearbySearchRequestArray);

        foreach ($companies as $company) {
            $this->companyDiscoveryService->processCompany($company, $this->discoveryReference);
        }

        $location->checked = true;
        $location->save();
    }

    /**
     * @param array $unfinishedIndustryIds
     * @return void
     */
    private function setExistingDiscoveryFields(array $unfinishedIndustryIds): void
    {
        $this->setIndustryOfExistingDiscovery($unfinishedIndustryIds);

        /** @var USZipCodeDiscoveryStatus $existingDiscovery */
        $existingDiscovery = USZipCodeDiscoveryStatus::query()
            ->where(USZipCodeDiscoveryStatus::FIELD_INDUSTRY_ID, $this->industry->id)
            ->where(USZipCodeDiscoveryStatus::FIELD_CHECKED, '<>', true)
            ->first();

        $this->placeKeyword       = $existingDiscovery->place_keyword;
        $this->placeType          = $existingDiscovery->place_type;
        $this->radius             = $existingDiscovery->radius;
        $this->discoveryReference = $existingDiscovery->discovery_reference;
    }

    /**
     * @param array $unfinishedIndustryIds
     * @return void
     */
    private function setIndustryOfExistingDiscovery(array $unfinishedIndustryIds): void
    {
        $unfinishedIndustries = Industry::query()->whereIn(Industry::FIELD_ID, $unfinishedIndustryIds);
        $industryName         = $this->choice('What is the industry name for the existing discovery?', $unfinishedIndustries->pluck('name')->toArray());

        $this->industry = Industry::query()->where(Industry::FIELD_NAME, $industryName)->first();
    }

    /**
     * @return void
     */
    private function setNewDiscoveryChoices(): void
    {
        $this->setIndustry();
        $this->setPlaceType();
        $this->setPlaceKeyword();
        $this->setRadius();
        $this->setLimits();
        $this->confirmChoices();
        $this->discoveryReference = Str::uuid();
    }

    /**
     * @return void
     */
    private function setIndustry(): void
    {
        $unfinishedIndustryIds = $this->getUnfinishedIndustryIds();

        $industries   = Industry::query()->select('name')->whereNotIn(Industry::FIELD_ID, $unfinishedIndustryIds)->get();
        $industryName = $this->choice('Which industry would you like to discover companies in? If your desired industry is not listed it either does not exist in the database or there is an unfinished discovery in process for that industry.', $industries->pluck('name')->toArray());

        $this->industry = Industry::query()->where(Industry::FIELD_NAME, $industryName)->first();
    }

    /**
     * @return void
     */
    private function setPlaceType(): void
    {
        $choice = $this->choice('Do you want to include a "Place Type" in your search? Place type options can be found in Table 1 here: https://developers.google.com/maps/documentation/places/web-service/supported_types#table1', [self::CHOICE_NO, self::CHOICE_YES]);

        if ($choice === self::CHOICE_YES) {
            $this->placeType = $this->ask('Enter the desired Place Type');
        }
    }

    /**
     * @return void
     */
    private function setPlaceKeyword(): void
    {
        $choice = $this->choice('Do you want to include a "Place Keyword" in your search? A Place Keyword should represent a category of establishments. Any other types of input can generate errors and are not guaranteed to return valid results. If a Place Type is used then a Place Keyword is likely unnecessary.', [self::CHOICE_NO, self::CHOICE_YES]);

        if ($choice === self::CHOICE_YES) {
            $this->placeKeyword = $this->ask('Enter the desired Place Keyword');
        }
    }

    /**
     * @return void
     */
    private function setRadius(): void
    {
        $choice = $this->choice("Would you like to change the default discovery radius of $this->radius meters (5 miles)?", [self::CHOICE_NO, self::CHOICE_YES]);

        if ($choice === self::CHOICE_YES) {
            $this->radius = $this->ask('Enter the radius in meters');
        }
    }

    /**
     * @return void
     */
    private function setLimits(): void
    {
        $choice = $this->choice("Would you like to limit the number of zip codes crawled?", [self::CHOICE_NO, self::CHOICE_YES]);

        if ($choice === self::CHOICE_YES) {
            $this->zipLimit = $this->ask('Enter the number of zip codes to crawl');
        }
    }

    /**
     * @return void
     */
    private function confirmChoices(): void
    {
        $zipLimit = $this->zipLimit ? : $this->getUsZipCodesToDiscover()->count();

        $choice = $this->choice("You have chosen the following:\n
            Industry: {$this->industry->name}\n
            Place Type: {$this->placeType}\n
            Place Keyword: $this->placeKeyword\n
            Radius: $this->radius\n
            Zip codes to crawl: $zipLimit \n\n
        Does this look correct?", [self::CHOICE_NO, self::CHOICE_YES]);

        if ($choice === self::CHOICE_NO) {
            $this->error('Exiting');
            exit();
        }
    }

    /**
     * @return int|null
     */
    private function getNumberOfLocationsToProcess(): ?int
    {
        $choice = $this->choice('Would you like to throttle the number of locations to process?', [self::CHOICE_NO, self::CHOICE_YES]);

        if ($choice === self::CHOICE_YES) {
            return intval($this->ask('Enter the number of locations you would like to process'));
        }

        return null;
    }
}
