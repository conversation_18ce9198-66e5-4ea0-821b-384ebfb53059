<?php

namespace App\Console\Commands;

use App\Models\USZipCode;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class RepopulateUSZipCodes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'repopulate:us-zip-codes';

    /** @var int */
    private int $lineCount;

    /** @var int */
    private int $populationLineCount;

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run this command to repopulate the list of US Zip Codes. File should be in database/seeders/data/us-zip-codes.csv';

    const FILE_PATH = 'database/seeders/data/us-zip-codes.csv';

    const POPULATION_FILE_PATH = 'database/seeders/data/us-zip-codes-population.csv';

    // Headers for the Population zip code CSV. Only a few of these are added to the us_zip_codes table
    const string POP_ZIP              = 'zip';
    const string POP_LAT              = 'lat';
    const string POP_LNG              = 'lng';
    const string POP_CITY             = 'city';
    const string POP_STATE_ID         = 'state_id';
    const string POP_STATE_NAME       = 'state_name';
    const string POP_ZCTA             = 'zcta';
    const string POP_PARENT_ZCTA      = 'parent_zcta';
    const string POP_POPULATION       = 'population';
    const string POP_DENSITY          = 'density';
    const string POP_COUNTY_FIPS      = 'county_fips';
    const string POP_COUNTY_NAME      = 'county_name';
    const string POP_COUNTY_WEIGHTS   = 'county_weights';
    const string POP_COUNTY_NAMES_ALL = 'county_names_all';
    const string POP_COUNTY_FIPS_ALL  = 'county_fips_all';
    const string POP_IMPRECISE        = 'imprecise';
    const string POP_MILITARY         = 'military';
    const string POP_TIMEZONE         = 'timezone';

    /**
     * Execute the console command
     *
     * @return int
     */
    public function handle(): int
    {
        $filePath = base_path(self::FILE_PATH);
        $populationFilePath = base_path(self::POPULATION_FILE_PATH);

        $this->processFile($filePath, $populationFilePath);

        return 0;
    }

    /**
     * @param $handle
     * @return bool
     */
    private function validateFile($handle): bool
    {
        $this->info("Validating file");
        fgetcsv($handle); // skip header

        $bar = $this->output->createProgressBar($this->lineCount);
        $bar->start();

        while($csvLine = fgetcsv($handle)) {
            $assocCSVLine = $this->mapCSVToTableFieldNames($csvLine);

            if(!$this->validateFileData($assocCSVLine)) {
                return false;
            }

            $bar->advance();
        }

        rewind($handle);
        $bar->finish();
        $this->newLine();

        return true;
    }

    /**
     * @param $handle
     * @return bool
     */
    private function validatePopulationFile($handle): bool
    {
        $this->info("Validating population file");
        fgetcsv($handle); // skip header

        $bar = $this->output->createProgressBar($this->populationLineCount);
        $bar->start();

        while($csvLine = fgetcsv($handle)) {
            $assocCSVLine = $this->mapPopulationCSVToTableFieldNames($csvLine);

            if(!$this->validatePopulationFileData($assocCSVLine)) {
                return false;
            }

            $bar->advance();
        }

        rewind($handle);
        $bar->finish();
        $this->newLine();

        return true;
    }

    /**
     * @param $handle
     * @param $populationHandle
     * @return void
     */
    private function storeData($handle, $populationHandle)
    {
        $this->info("Building population data");
        fgetcsv($populationHandle); // skip header

        $bar = $this->output->createProgressBar($this->populationLineCount);
        $bar->start();

        $populationData = [];

        while ($csvLine = fgetcsv($populationHandle)) {
            $assocCSVLine = $this->mapPopulationCSVToTableFieldNames($csvLine);
            $populationData[$assocCSVLine[self::POP_ZIP]] = $this->makePopulationUpdateArray($assocCSVLine);
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();

        $this->info("Building data");
        fgetcsv($handle); // skip header

        $insertArray = [];

        $bar = $this->output->createProgressBar($this->lineCount);
        $bar->start();

        while ($csvLine = fgetcsv($handle)) {
            $assocCSVLine = $this->mapCSVToTableFieldNames($csvLine);
            $this->makeInsertArray($insertArray, $assocCSVLine, $populationData);

            $bar->advance();
        }

        $bar->finish();
        $this->newLine();

        $this->info("Storing data");

        $bar = $this->output->createProgressBar($this->lineCount / 2000);
        $bar->setFormat("%current%/%max% %bar% %percent%% \nElapsed: %elapsed% \nEst. Remaining: %remaining% \nMemory Usage: %memory%\n");
        $bar->start();

        DB::transaction(function() use ($insertArray, $bar) {
            foreach(array_chunk($insertArray, 2000) as $chunkInsert) {
                USZipCode::insert($chunkInsert);

                $bar->advance();
            }
        });

        $bar->finish();
        $this->newLine();
    }

    /**
     * @param $filePath
     * @return int
     */
    private function getCsvLineCount($filePath): int
    {
        $file = new \SplFileObject($filePath, 'r');
        $file->seek(PHP_INT_MAX);
        return $file->key() + 1;
    }

    /**
     * @param string $filePath
     * @param string $populationFilePath
     * @return void
     */
    public function processFile(string $filePath, string $populationFilePath): void
    {
        $this->lineCount = $this->getCsvLineCount($filePath);
        $this->populationLineCount = $this->getCsvLineCount($populationFilePath);

        $handle = fopen($filePath, "r");
        $populationHandle = fopen($populationFilePath, "r");

        if($this->validateFile($handle) && $this->validatePopulationFile($populationHandle)) {
            USZipCode::truncate();

            $this->storeData($handle, $populationHandle);
        }

    }

    /**
     * @param array $insertArray
     * @param array $assocCSVLine
     * @param array $populationData
     * @return void
     */
    private function makeInsertArray(array &$insertArray, array $assocCSVLine, array $populationData): void
    {
        $insertArray[] = array_merge([
            USZipCode::FIELD_ZIP_CODE    => $assocCSVLine[USZipCode::FIELD_ZIP_CODE],
            USZipCode::FIELD_ZIP_TYPE    => $assocCSVLine[USZipCode::FIELD_ZIP_TYPE],
            USZipCode::FIELD_CITY_NAME   => $assocCSVLine[USZipCode::FIELD_CITY_NAME],
            USZipCode::FIELD_CITY_TYPE   => $assocCSVLine[USZipCode::FIELD_CITY_TYPE],
            USZipCode::FIELD_COUNTY_NAME => $assocCSVLine[USZipCode::FIELD_COUNTY_NAME],
            USZipCode::FIELD_COUNTY_FIPS => $assocCSVLine[USZipCode::FIELD_COUNTY_FIPS],
            USZipCode::FIELD_STATE_NAME  => $assocCSVLine[USZipCode::FIELD_STATE_NAME],
            USZipCode::FIELD_STATE_ABBR  => $assocCSVLine[USZipCode::FIELD_STATE_ABBR],
            USZipCode::FIELD_STATE_FIPS  => $assocCSVLine[USZipCode::FIELD_STATE_FIPS],
            USZipCode::FIELD_MSA_CODE    => $assocCSVLine[USZipCode::FIELD_MSA_CODE],
            USZipCode::FIELD_AREA_CODE   => $assocCSVLine[USZipCode::FIELD_AREA_CODE],
            USZipCode::FIELD_TIME_ZONE   => $assocCSVLine[USZipCode::FIELD_TIME_ZONE],
            USZipCode::FIELD_UTC         => $assocCSVLine[USZipCode::FIELD_UTC],
            USZipCode::FIELD_DST         => $assocCSVLine[USZipCode::FIELD_DST],
            USZipCode::FIELD_LATITUDE    => $assocCSVLine[USZipCode::FIELD_LATITUDE],
            USZipCode::FIELD_LONGITUDE   => $assocCSVLine[USZipCode::FIELD_LONGITUDE],
        ], $populationData[$assocCSVLine[USZipCode::FIELD_ZIP_CODE]] ?? $this->noPopulationMatch());
    }

    /**
     * @return array
     */
    private function noPopulationMatch(): array
    {
        return [
            USZipCode::FIELD_POPULATION     => 0,
            USZipCode::FIELD_DENSITY        => 0,
            USZipCode::FIELD_COUNTY_WEIGHTS => null,
        ];
    }

    /**
     * @param array $assocCSVLine
     * @return array
     */
    private function makePopulationUpdateArray(array $assocCSVLine): array
    {
        return [
            USZipCode::FIELD_POPULATION     => $assocCSVLine[self::POP_POPULATION],
            USZipCode::FIELD_DENSITY        => $assocCSVLine[self::POP_DENSITY],
            USZipCode::FIELD_COUNTY_WEIGHTS => $assocCSVLine[self::POP_COUNTY_WEIGHTS],
        ];
    }

    /**
     * @param array $assocCVSLine
     * @return bool|null
     */
    private function validateFileData(array $assocCVSLine): ?bool
    {
        $validator = Validator::make($assocCVSLine, [
            USZipCode::FIELD_ZIP_CODE    => 'required|string|size:5',
            USZipCode::FIELD_ZIP_TYPE    => 'required|string|size:1',
            USZipCode::FIELD_CITY_NAME   => 'required|string',
            USZipCode::FIELD_CITY_TYPE   => 'required|string|size:1',
            USZipCode::FIELD_COUNTY_NAME => 'required|string',
            USZipCode::FIELD_COUNTY_FIPS => 'required|string|max:5',
            USZipCode::FIELD_STATE_NAME  => 'required|string',
            USZipCode::FIELD_STATE_ABBR  => 'required|string|size:2',
            USZipCode::FIELD_STATE_FIPS  => 'required|string|max:2',
            USZipCode::FIELD_MSA_CODE    => 'required|string',
            USZipCode::FIELD_AREA_CODE   => 'required|string',
            USZipCode::FIELD_TIME_ZONE   => 'required|string',
            USZipCode::FIELD_UTC         => 'required|string',
            USZipCode::FIELD_DST         => 'required|string|size:1',
            USZipCode::FIELD_LATITUDE    => 'required|string',
            USZipCode::FIELD_LONGITUDE   => 'required|string'
        ]);

        if($validator->fails()) {
            logger()->error("US Zip Code Repopulation Error: File failed validation - " . $validator->errors());
            return false;
        }

        return true;
    }

    /**
     * @param array $assocCVSLine
     * @return bool|null
     */
    private function validatePopulationFileData(array $assocCVSLine): ?bool
    {
        $validator = Validator::make($assocCVSLine, [
            self::POP_ZIP              => 'required|string|size:5',
            self::POP_LAT              => 'required|string',
            self::POP_LNG              => 'required|string',
            self::POP_CITY             => 'required|string',
            self::POP_STATE_ID         => 'required|string',
            self::POP_STATE_NAME       => 'required|string',
            self::POP_ZCTA             => 'required|string',
            self::POP_PARENT_ZCTA      => 'nullable|string',
            self::POP_POPULATION       => 'nullable|string',
            self::POP_DENSITY          => 'nullable|string',
            self::POP_COUNTY_FIPS      => 'required|string',
            self::POP_COUNTY_NAME      => 'required|string',
            self::POP_COUNTY_WEIGHTS   => 'required|string',
            self::POP_COUNTY_NAMES_ALL => 'required|string',
            self::POP_COUNTY_FIPS_ALL  => 'required|string',
            self::POP_IMPRECISE        => 'required|string',
            self::POP_MILITARY         => 'required|string',
            self::POP_TIMEZONE         => 'required|string',
        ]);

        if($validator->fails()) {
            logger()->error("US Zip Code Population File Error: File failed validation - " . $validator->errors());
            return false;
        }

        return true;
    }

    /**
     * @param array $csvLine
     * @return array
     */
    private function mapCSVToTableFieldNames(array $csvLine): array
    {
        $map = [
            USZipCode::FIELD_ZIP_CODE,
            USZipCode::FIELD_ZIP_TYPE,
            USZipCode::FIELD_CITY_NAME,
            USZipCode::FIELD_CITY_TYPE,
            USZipCode::FIELD_COUNTY_NAME,
            USZipCode::FIELD_COUNTY_FIPS,
            USZipCode::FIELD_STATE_NAME,
            USZipCode::FIELD_STATE_ABBR,
            USZipCode::FIELD_STATE_FIPS,
            USZipCode::FIELD_MSA_CODE,
            USZipCode::FIELD_AREA_CODE,
            USZipCode::FIELD_TIME_ZONE,
            USZipCode::FIELD_UTC,
            USZipCode::FIELD_DST,
            USZipCode::FIELD_LATITUDE,
            USZipCode::FIELD_LONGITUDE
        ];

        return array_combine($map, $csvLine);
    }

    /**
     * @param array $csvLine
     * @return array
     */
    private function mapPopulationCSVToTableFieldNames(array $csvLine): array
    {
        $map = [
            self::POP_ZIP,
            self::POP_LAT,
            self::POP_LNG,
            self::POP_CITY,
            self::POP_STATE_ID,
            self::POP_STATE_NAME,
            self::POP_ZCTA,
            self::POP_PARENT_ZCTA,
            self::POP_POPULATION,
            self::POP_DENSITY,
            self::POP_COUNTY_FIPS,
            self::POP_COUNTY_NAME,
            self::POP_COUNTY_WEIGHTS,
            self::POP_COUNTY_NAMES_ALL,
            self::POP_COUNTY_FIPS_ALL,
            self::POP_IMPRECISE,
            self::POP_MILITARY,
            self::POP_TIMEZONE,
        ];

        return array_combine($map, $csvLine);
    }
}
