<?php

namespace App\Console\Commands;

use App\Enums\ConsumerProcessing\ConsumerProcessingActivityType;
use App\Models\ConsumerProcessingActivity;
use App\Models\Legacy\EloquentComment;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ServiceProduct;
use App\Models\User;
use App\Services\DatabaseHelperService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use function Laravel\Prompts\clear;
use function Laravel\Prompts\progress;

class MigrateLegacyLeadComments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'legacy-migrate:comments {--chunk-size=5000 : The number of batches/jobs to create}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handles the migration of legacy lead comments.';

    private array $userIdMap = [];
    private array $systemUser = [
        'id' => 1,
        'name' => 'System User',
    ];
    private Carbon $now;

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $chunkSize = $this->option('chunk-size') ?: 5000;

        $legacyCommentBaseQuery = DB::table(DatabaseHelperService::readOnlyDatabase() .'.'. EloquentComment::TABLE)
            ->join(Consumer::TABLE, Consumer::TABLE .'.'. Consumer::FIELD_LEGACY_ID, '=', EloquentComment::TABLE .'.'. EloquentComment::REL_ID)
            ->where(EloquentComment::TABLE .'.'. EloquentComment::REL_TYPE, EloquentComment::REL_TYPE_QUOTE)
            ->orderBy(EloquentComment::TABLE .'.'. EloquentComment::COMMENT_ID);

        $count = $legacyCommentBaseQuery
            ->count();
        $chunks = ceil($count/$chunkSize);
        $legacyCommentQuery = $legacyCommentBaseQuery
            ->select([EloquentComment::TABLE .'.*', ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_CONSUMER_ID, DB::raw('min(consumer_products.id) as consumer_product_id')])
            ->join(ConsumerProduct::TABLE, ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_CONSUMER_ID, '=',Consumer::TABLE .'.'. Consumer::FIELD_ID)
            ->join(ServiceProduct::TABLE, ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_ID, '=', ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_SERVICE_PRODUCT_ID)
            ->groupBy(EloquentComment::TABLE .'.'. EloquentComment::COMMENT_ID);

        clear();
        $this->info("Processing $count lead comments in $chunks chunks...");
        $progressBar = progress('Processing lead comment chunks', $chunks);

        $this->userIdMap = User::query()
            ->select([User::FIELD_ID, User::FIELD_LEGACY_USER_ID, User::FIELD_NAME])
            ->get()
            ->reduce(function(array $output, User $user) {
                $output[$user->legacy_user_id] = [
                        'id'   => $user->id,
                        'name' => $user->name,
                ];
                return $output;
        }, []);
        $this->systemUser['id'] = User::systemUser()->id;
        $this->now = now();

        $success = $legacyCommentQuery->chunkById($chunkSize, function(Collection $legacyComments) use ($progressBar, &$userIdMap) {
            DB::beginTransaction();
            if ($this->handleChunk($legacyComments)) {
                DB::commit();
                $progressBar->advance();

                return true;
            }
            else {
                DB::rollBack();
                $this->error("\nError processing chunk. " . $progressBar->progress . " chunks were successfully migrated.");

                return false;
            }
        }, EloquentComment::TABLE .'.'. EloquentComment::COMMENT_ID, EloquentComment::COMMENT_ID);

        if ($success) {
            $this->info("\nSuccessfully migrated $count lead comments.\nChunky!\n\n");

            return Command::SUCCESS;
        }

        return Command::FAILURE;
    }

    /**
     * @param Collection $legacyComments
     * @return bool
     */
    private function handleChunk(Collection $legacyComments): bool
    {
        $insertData = $legacyComments->reduce(function(array $output, $comment) {
            $output[] = $this->transformComment($comment);
            return $output;
        }, []);

        return ConsumerProcessingActivity::query()
            ->insert($insertData);
    }

    private function transformComment($comment): array
    {
        $type = $this->getCommentType($comment->comment);

        return [
            ConsumerProcessingActivity::FIELD_CONSUMER_ID         => $comment->consumer_id,
            ConsumerProcessingActivity::FIELD_CONSUMER_PRODUCT_ID => $comment->consumer_product_id,
            ConsumerProcessingActivity::FIELD_SUMMARY             => $this->getSummary($type, $comment),
            ConsumerProcessingActivity::FIELD_COMMENT             => $this->getComment($type, $comment),
            ConsumerProcessingActivity::FIELD_USER_ID             => $this->userIdMap[$comment->addedbyuserid]['id'] ?? $this->systemUser['id'],
            ConsumerProcessingActivity::FIELD_ACTIVITY_TYPE       => $type->value,
            ConsumerProcessingActivity::CREATED_AT                => $comment->timestampadded
                ? Carbon::createFromTimestamp($comment->timestampadded)
                : $this->now,
        ];
    }

    private function getComment(ConsumerProcessingActivityType $type, $comment): ?string
    {
        return match ($type) {
            ConsumerProcessingActivityType::USER_COMMENT, ConsumerProcessingActivityType::DNC_REQUEST
                => $comment->comment,
            default => null,
        };
    }

    private function getSummary(ConsumerProcessingActivityType $type, $comment): string
    {
        return match ($type) {
            ConsumerProcessingActivityType::USER_COMMENT => 'User Comment',
            ConsumerProcessingActivityType::DNC_REQUEST => 'DNC Privacy Request',
            default => $comment->comment,
        };
    }

    private function getCommentType(string $commentString): ConsumerProcessingActivityType
    {
        return match (true) {
            !!preg_match("/^cancel/i", $commentString)                       => ConsumerProcessingActivityType::CANCELLED,
            !!preg_match("/^approv/i", $commentString)                       => ConsumerProcessingActivityType::APPROVED,
            !!preg_match("/^under\sr/i", $commentString)                     => ConsumerProcessingActivityType::UNDER_REVIEW,
            !!preg_match("/^pending\sr/i", $commentString)                   => ConsumerProcessingActivityType::PENDING_REVIEW,
            !!preg_match("/^installers\snotified\sdnc\sr/i", $commentString) => ConsumerProcessingActivityType::PENDING_REVIEW,
            !!preg_match("/^called\sby/i", $commentString)                  => ConsumerProcessingActivityType::CALL,
            default                                                          => ConsumerProcessingActivityType::USER_COMMENT,
        };
    }
}