<?php

namespace App\Console\Commands;

use App\Models\Action;
use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Legacy\EloquentUser;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Services\DatabaseHelperService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixDuplicateCompanyUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:duplicate-company-users';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fixes duplicate company users';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $this->updateLegacyCompanyUsers();
        $this->updateLegacyCompanyContacts();
        $this->consolidateCompanyUsers();
        $this->consolidateCompanyContacts();
    }

    private function updateLegacyCompanyUsers(): void
    {
        $this->info("\nUpdating company user data");

        $query = $this->getLegacyCompanyUserQuery();

        $bar = $this->output->createProgressBar($query->count());

        $query->chunk(100, function ($legacyUsers) use ($bar) {
            DB::beginTransaction();

            foreach($legacyUsers as $legacyUser) {
                CompanyUser::withTrashed()
                    ->where(CompanyUser::FIELD_COMPANY_ID, $legacyUser->{'odin_company_id'})
                    ->where(CompanyUser::FIELD_LEGACY_ID, $legacyUser->{'legacy_user_id'})
                    ->update([
                        CompanyUser::FIELD_CAN_LOG_IN => true,
                        CompanyUser::FIELD_IS_CONTACT => false
                    ]);

                $bar->advance();
            }

            DB::commit();
        });

        $bar->finish();
    }

    private function updateLegacyCompanyContacts(): void
    {
        $this->info("\nUpdating company contact data");

        $query = $this->getLegacyCompanyContactsQuery();

        $bar = $this->output->createProgressBar($query->count());

        $query->chunk(100, function($legacyContacts) use ($bar) {
            DB::beginTransaction();

            foreach($legacyContacts as $legacyContact) {
                CompanyUser::withTrashed()
                    ->where(CompanyUser::FIELD_COMPANY_ID, $legacyContact->{'odin_company_id'})
                    ->where(CompanyUser::FIELD_LEGACY_ID, $legacyContact->{'legacy_user_id'})
                    ->update([
                        CompanyUser::FIELD_CAN_LOG_IN => false,
                        CompanyUser::FIELD_IS_CONTACT => true
                    ]);

                $bar->advance();
            }

            DB::commit();
        });

        $bar->finish();
    }

    private function consolidateCompanyUsers(): void
    {
        $this->info("\nConsolidating company users");

        $query = $this->getCompanyUsersQuery();

        $bar = $this->output->createProgressBar($query->count());

        $query->chunk(100, function($users) use ($bar) {
            DB::beginTransaction();

            foreach($users as $user) {
                CompanyUser::withTrashed()
                    ->where(CompanyUser::FIELD_LEGACY_ID, $user->{CompanyUser::FIELD_LEGACY_ID})
                    ->where(CompanyUser::FIELD_COMPANY_ID, $user->{CompanyUser::FIELD_COMPANY_ID})
                    ->where(CompanyUser::FIELD_ID, '!=', $user->{CompanyUser::FIELD_ID})
                    ->forceDelete();

                $bar->advance();
            }

            DB::commit();
        });

        $bar->finish();
    }

    private function consolidateCompanyContacts(): void
    {
        $this->info("\nConsolidating company contacts");
        Action::unsetEventDispatcher();

        $query = $this->getCompanyContactsQuery();
        $bar = $this->output->createProgressBar($query->count());

        $query->chunk(100, function($contacts) use ($bar) {
            DB::beginTransaction();

            $userIdsToDelete = [];

            foreach($contacts as $contact) {
                $userId = $contact->{CompanyUser::FIELD_ID};

                $duplicateUserIds = CompanyUser::withTrashed()
                    ->where(CompanyUser::FIELD_LEGACY_ID, $contact->{CompanyUser::FIELD_LEGACY_ID})
                    ->where(CompanyUser::FIELD_COMPANY_ID, $contact->{CompanyUser::FIELD_COMPANY_ID})
                    ->where(CompanyUser::FIELD_ID, '!=', $userId)
                    ->pluck(CompanyUser::FIELD_ID)
                    ->toArray();

                $this->reassignActions($userId, $duplicateUserIds);

                $userIdsToDelete = array_merge($userIdsToDelete, $duplicateUserIds);

                $bar->advance();
            }

            CompanyUser::withTrashed()
                ->whereIn(CompanyUser::FIELD_ID, $userIdsToDelete)
                ->forceDelete();

            DB::commit();
        });

        $bar->finish();
    }

    private function reassignActions(int $userId, array $duplicateUserIds): void
    {
        Action::query()
            ->where(Action::FIELD_FOR_RELATION_TYPE, Action::RELATION_TYPE_COMPANY_CONTACT)
            ->whereIn(Action::FIELD_FOR_ID, $duplicateUserIds)
            ->update([
                Action::FIELD_FOR_ID => $userId
            ]);
    }

    private function getLegacyCompanyUserQuery(): \Illuminate\Database\Eloquent\Builder
    {
        return EloquentUser::withTrashed()
            ->select([
                DatabaseHelperService::database().'.'.Company::TABLE.'.'.Company::FIELD_ID . ' as odin_company_id',
                EloquentUser::TABLE . '.'. EloquentUser::USER_ID . ' as legacy_user_id'
            ])
            ->join(
                DatabaseHelperService::database().'.'.Company::TABLE,
                EloquentUser::TABLE.'.'.EloquentUser::COMPANY_ID,
                '=',
                DatabaseHelperService::database().'.'.Company::TABLE.'.'.Company::FIELD_LEGACY_ID
            )
            ->where(EloquentUser::TABLE.'.'.EloquentUser::COMPANY_ID, '!=', 0);
    }

    private function getLegacyCompanyContactsQuery(): \Illuminate\Database\Eloquent\Builder
    {
        return EloquentCompanyContact::withTrashed()
            ->select([
                DatabaseHelperService::database().'.'.Company::TABLE.'.'.Company::FIELD_ID . ' as odin_company_id',
                EloquentCompanyContact::TABLE . '.'. EloquentCompanyContact::FIELD_CONTACT_ID . ' as legacy_user_id'
            ])
            ->join(
                DatabaseHelperService::database().'.'.Company::TABLE,
                EloquentCompanyContact::TABLE.'.'.EloquentCompanyContact::FIELD_COMPANY_ID,
                '=',
                DatabaseHelperService::database().'.'.Company::TABLE.'.'.Company::FIELD_LEGACY_ID
            );
    }

    private function getCompanyUsersQuery(): \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Query\Builder
    {
        return CompanyUser::withTrashed()
            ->select([CompanyUser::FIELD_ID, CompanyUser::FIELD_LEGACY_ID, CompanyUser::FIELD_COMPANY_ID])
            ->where(CompanyUser::FIELD_CAN_LOG_IN, true)
            ->where(CompanyUser::FIELD_IS_CONTACT, false)
            ->whereNotNull(CompanyUser::FIELD_LEGACY_ID)
            ->groupBy(CompanyUser::FIELD_LEGACY_ID)
            ->havingRaw('COUNT(*) > 1');
    }

    private function getCompanyContactsQuery(): \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Query\Builder
    {
        return CompanyUser::withTrashed()
            ->select(CompanyUser::FIELD_ID, CompanyUser::FIELD_LEGACY_ID, CompanyUser::FIELD_COMPANY_ID)
            ->where(CompanyUser::FIELD_CAN_LOG_IN, false)
            ->where(CompanyUser::FIELD_IS_CONTACT, true)
            ->whereNotNull(CompanyUser::FIELD_LEGACY_ID)
            ->groupBy(CompanyUser::FIELD_LEGACY_ID)
            ->havingRaw('COUNT(*) > 1');
    }

}
