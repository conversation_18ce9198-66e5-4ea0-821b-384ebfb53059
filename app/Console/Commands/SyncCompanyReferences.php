<?php

namespace App\Console\Commands;

use App\Models\AccountManagerClient;
use App\Models\Legacy\EloquentCompany;
use App\Models\Odin\Company;
use App\Services\DatabaseHelperService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Schema;

class SyncCompanyReferences extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:company-references';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync company references between A2 and legacy';

    /**
     * @return int
     * @throws \Doctrine\DBAL\Exception
     *
     * @source Code from: https://stackoverflow.com/a/********
     */
    public function handle()
    {
        $this->info("Syncing company references");
        $this->newLine();

        $start = microtime(true);

        DB::beginTransaction();

        Schema::disableForeignKeyConstraints();

        $legacyReferenceCol = 'legacy_reference';
        $adminReferenceCol = 'admin_reference';

        $currentReferences = Company::query()
            ->join(DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE, function($join) {
                $join->on(
                    DatabaseHelperService::database().'.'.Company::TABLE.'.'.Company::FIELD_LEGACY_ID,
                    '=',
                    DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE.'.'.EloquentCompany::COMPANY_ID
                );
            })
            ->whereColumn(Company::TABLE.'.'.Company::FIELD_REFERENCE, '!=', EloquentCompany::TABLE.'.'.EloquentCompany::REFERENCE)
            ->selectRaw(sprintf(
                "%s, %s",
                EloquentCompany::TABLE.'.'.EloquentCompany::REFERENCE." AS {$legacyReferenceCol}",
                Company::TABLE.'.'.Company::FIELD_REFERENCE." AS {$adminReferenceCol}"
            ))
            ->pluck($legacyReferenceCol, $adminReferenceCol)
            ->toArray();

        $this->warn("Syncing company table");
        $this->newLine();

        Company::query()
            ->join(DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE, function($join) {
                $join->on(
                    DatabaseHelperService::database().'.'.Company::TABLE.'.'.Company::FIELD_LEGACY_ID,
                    '=',
                    DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE.'.'.EloquentCompany::COMPANY_ID
                );
            })
            ->whereColumn(Company::TABLE.'.'.Company::FIELD_REFERENCE, '!=', EloquentCompany::TABLE.'.'.EloquentCompany::REFERENCE)
            ->update([
                Company::TABLE.'.'.Company::FIELD_REFERENCE => DB::raw(EloquentCompany::TABLE.'.'.EloquentCompany::REFERENCE)
            ]);

        $this->warn("Syncing account manager clients table");
        $this->newLine();

        $amc = AccountManagerClient::query()
            ->whereIn(AccountManagerClient::FIELD_COMPANY_REFERENCE, array_keys($currentReferences))
            ->get();

        foreach($amc as $accountManagerClient) {
            $accountManagerClient->{AccountManagerClient::FIELD_COMPANY_REFERENCE} = $currentReferences[$accountManagerClient->{AccountManagerClient::FIELD_COMPANY_REFERENCE}];

            $accountManagerClient->save();
        }

        Schema::enableForeignKeyConstraints();

        DB::commit();

        $elapsed = round(microtime(true) - $start, 2);

        $this->info("Elapsed $elapsed secs");

        return 0;
    }
}
