<?php

namespace App\Console\Commands;

use App\Models\Legacy\EloquentCompany;
use App\Models\Odin\Company;
use App\Services\DatabaseHelperService;
use App\Services\Odin\CompanyLogoService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Symfony\Component\Console\Command\Command as CommandAlias;
use Throwable;

class UpdateCompanyLogos extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:company-logos';

    /**
     * @var string
     */
    protected $description = '<PERSON>les updating the legacy-structured logos to their new path/url and moves them to the Cloud for the companies already migrated to A2.';

    /** @var CompanyLogoService $companyLogoService */
    protected CompanyLogoService $companyLogoService;

    /**
     * @param CompanyLogoService $companyLogoService
     */
    public function __construct(CompanyLogoService $companyLogoService)
    {
        parent::__construct();
        $this->companyLogoService = $companyLogoService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $this->line("Initiating the process...");

        $totalCompanies = $this->getBaseQuery()->count();

        if($totalCompanies > 0) {
            $this->info("Total no. of company logos to update: $totalCompanies");

            $this->withProgressBar($this->getBaseQuery()->get(), function ($company) {
                try {
                    /** @var Company $company */
                    $response = $this->companyLogoService->handle($company);

                    if(is_string($response) && strlen($response)) {
                        $this->handleError($response);
                    }
                }
                catch(Throwable $e) {
                    $this->handleError("{$company->{Company::FIELD_ID}}: " . $e->getMessage());
                }
            });

            $this->newLine();
            $this->info('Finished processing.');
        }
        else {
            $this->line("No company/logo to update. Make sure you've already migrated the legacy companies.");
        }

        return CommandAlias::SUCCESS;
    }

    /**
     * @return Builder
     */
    protected function getBaseQuery(): Builder
    {
        return Company::query()
            ->join(DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE, function($join) {
                /** @var JoinClause $join */
                $join->on(
                    DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE.'.'.EloquentCompany::ID,
                    DatabaseHelperService::database().'.'.Company::TABLE.'.'.Company::FIELD_LEGACY_ID
                );
            })
            ->where(Company::FIELD_LINK_TO_LOGO, '!=', '')
            ->WhereNotNull(Company::FIELD_LINK_TO_LOGO)
            ->where(Company::FIELD_LINK_TO_LOGO, 'NOT LIKE', '%'.$this->companyLogoService->getCompanyLogoBaseUrlWithBucket().'%')
            ->select(Company::TABLE.'.*');
    }

    /**
     * @param string $message
     * @return void
     */
    protected function handleError(string $message): void
    {
        $this->newLine();
        $this->warn($message);

        logger()->error($message);
    }
}
