<?php

namespace App\Console\Commands;

use App\Enums\CompanyConsumerReviewStatus;
use App\Enums\Odin\Industry;
use App\Enums\Odin\IndustryServiceSlug;
use App\Enums\Reviews\ReviewQuestion;
use App\Enums\Reviews\ReviewQuestionDataType;
use App\Models\ConsumerReviews\Review;
use App\Models\ConsumerReviews\ReviewData;
use App\Models\ConsumerReviews\Reviewer;
use App\Models\ConsumerReviews\ReviewReply;
use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentComment;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentReview;
use App\Models\Legacy\EloquentReviewAnswer;
use App\Models\Legacy\EloquentReviewAttachment;
use App\Models\Legacy\EloquentReviewExtraData;
use App\Models\Legacy\EloquentReviewQuestion;
use App\Models\Legacy\EloquentUser;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Website;
use App\Services\DatabaseHelperService;
use Illuminate\Console\Command;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Ramsey\Uuid\Uuid;
use Throwable;
use function Laravel\Prompts\clear;
use function Laravel\Prompts\progress;

class MigrateLegacyCompanyReviews extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'legacy-migrate:company-reviews {--start-from-id=} {--batch-job-size=1000: The number of reviews each batch job should handle}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handles the migration of legacy company reviews to their new schema.';

    protected string $storageBaseUrl;
    protected array $companyIdMap;
    protected array $addressToLocationMap;
    protected int $solarIndustry;
    protected int $installationService;
    protected array $convertedReviewers = [];

    /**
     * Execute the console command.
     *
     * @return int
     * @throws Throwable
     */
    public function handle()
    {
        $startFromId = $this->option('start-from-id', null);
        $batchJobSize = (int) $this->option('batch-job-size');
        $baseUrl = config('services.google.storage.base_url');
        $bucket = config('services.google.storage.urls.review_attachments');
        $this->storageBaseUrl = preg_replace("/\/+/", '/', "$baseUrl/$bucket");

        clear();
        $this->line($startFromId > 0
            ? "Migrating company reviews from ID $startFromId..."
            : "Migrating all company reviews...");

        // Performance
        DB::disableQueryLog();

        $this->companyIdMap = DB::table(Company::TABLE)
            ->select([Company::FIELD_LEGACY_ID, Company::FIELD_ID])
            ->get()
            ->mapWithKeys(fn($company) => [
                $company->legacy_id => $company->id,
            ])->toArray();

        $this->addressToLocationMap = DB::table(DatabaseHelperService::readOnlyDatabase() .'.'. EloquentReview::TABLE)
            ->distinct([Address::TABLE .'.'. Address::FIELD_LEGACY_ID, CompanyLocation::TABLE .'.'. CompanyLocation::FIELD_ID])
            ->join(Address::TABLE, Address::TABLE .'.'. Address::FIELD_LEGACY_ID, EloquentReview::FIELD_ADDRESS_ID)
            ->join(CompanyLocation::TABLE, CompanyLocation::TABLE .'.'. CompanyLocation::FIELD_ADDRESS_ID, Address::TABLE .'.'. Address::FIELD_ID)
            ->where(EloquentReview::FIELD_ADDRESS_ID, '!=', 0)
            ->get()
            ->mapWithKeys(fn($address) => [
                $address->legacy_id => $address->id,
            ])->toArray();

        $this->solarIndustry = Industry::SOLAR->model()->id;
        $this->installationService = IndustryService::query()
            ->where(IndustryService::FIELD_INDUSTRY_ID, $this->solarIndustry)
            ->where(IndustryService::FIELD_SLUG, IndustryServiceSlug::SOLAR_INSTALLATION)
            ->first()
            ->id;

        $total = EloquentReview::query()
            ->whereIn(EloquentReview::TABLE .'.'. EloquentReview::FIELD_REVIEW_TYPE, EloquentCompany::TYPE_DISPLAY_NAMES)
            ->when($startFromId > 0, fn(Builder $query) =>
                $query->where(EloquentReview::FIELD_REVIEW_ID, '>=', $startFromId)
            )->count();

        if($total > 0) {
            $chunks = ceil($total / $batchJobSize);

            $this->line("$total company reviews total in $chunks chunks.");
            $progressBar = progress('Processing reviews chunks', $chunks);

            $lastId = null;

            EloquentReview::query()
                ->whereIn(EloquentReview::TABLE .'.'. EloquentReview::FIELD_REVIEW_TYPE, EloquentCompany::TYPE_DISPLAY_NAMES)
                ->orderBy(EloquentReview::TABLE .'.'. EloquentReview::FIELD_REVIEW_ID)
                ->with([
                    EloquentReview::RELATION_ADDRESS,
                    EloquentReview::RELATION_USER,
                    EloquentReview::RELATION_REVIEW_EXTRA_DATA,
                    EloquentReview::RELATION_REVIEW_ATTACHMENTS,
                    EloquentReview::RELATION_COMMENTS,
                    EloquentReview::RELATION_COMMENTS.'.user',
                    EloquentReview::RELATION_ANSWERS,
                    EloquentReview::RELATION_ANSWERS.'.'.EloquentReviewAnswer::RELATION_QUESTION
                ])->chunkById($batchJobSize, function (Collection $chunk) use ($progressBar, &$lastId) {
                    DB::beginTransaction();
                    if ($this->handleChunk($chunk)) {
                        DB::commit();
                        $progressBar->advance();
                        $lastId = $chunk->last()->reviewid;

                        return true;
                    }
                    else {
                        DB::rollBack();
                        $this->error("\nError processing chunk. " . $progressBar->progress . " chunks were successfully migrated.
                            \nThe last legacy Review ID successfully migrated was $lastId.
                            \nMigration can be continued by passing the argument --start-from-id=".$lastId+1);

                        return false;
                    }
                }, EloquentReview::TABLE .'.'. EloquentReview::FIELD_REVIEW_ID, EloquentReview::FIELD_REVIEW_ID);
        }
        else {
            $this->line("No company review to migrate");
        }

        $this->info("\nFinished migrating company reviews.");

        return Command::SUCCESS;
    }

    /**
     * @param Collection<EloquentReview> $chunk
     * @return bool
     */
    private function handleChunk(Collection $chunk): bool
    {
        try {
            foreach ($chunk as $review) {
                $this->migrateCompanyReview($review);
            }

            return true;
        }
        catch (Throwable $e) {
            $this->error($e->getMessage());

            return false;
        }
    }

    /**
     * @param EloquentReview $review
     * @return bool
     */
    protected function migrateCompanyReview(EloquentReview $review): bool
    {
        // Get reviewer name
        if ($review->{EloquentReview::RELATION_USER}?->{EloquentUser::DISPLAY_NAME}) {
            $reviewerName = $review->{EloquentReview::RELATION_USER}->{EloquentUser::DISPLAY_NAME};
        } else {
            $reviewerName = $review->{EloquentReview::FIELD_USER_NAME};
        }
        $reviewerId = $review->{EloquentReview::RELATION_USER}?->{EloquentUser::USER_ID} ?? null;

        // Get Company
        $company = Company::query()->where(Company::FIELD_LEGACY_ID, $review->{EloquentReview::FIELD_REVIEW_LINK})->get()->first();

        // Get Website
        $website = Website::query()->where(Website::FIELD_ABBREVIATION, 'sr')->get()->first();

        // Review creation date
        $creationDate = Carbon::createFromTimestamp($review->{EloquentReview::FIELD_TIMESTAMP_ADDED})->format("Y-m-d H:i:s");

        $reviewReference = Uuid::uuid4()->toString();
        // Build Custom Data
        $reviewDataPayload = $this->buildReviewDataPayload($review, $reviewReference);

        $statusMap = [
            -1  => CompanyConsumerReviewStatus::DECLINED,
            0   => CompanyConsumerReviewStatus::PENDING_APPROVAL,
            1   => CompanyConsumerReviewStatus::APPROVED,
        ];

        if (array_key_exists($review->{EloquentReview::FIELD_APPROVED}, $statusMap)) {
            $reviewStatus = $statusMap[$review->{EloquentReview::FIELD_APPROVED}];
        } else {
            $reviewStatus = CompanyConsumerReviewStatus::DECLINED;
        }

        $emailValidated = $review->user?->emailvalidated ?? false;
        $phoneValidated = $review->user?->is_phone_sms_verified ?? false;
        $reviewVerified = $reviewStatus === CompanyConsumerReviewStatus::APPROVED
            || $emailValidated
            || $phoneValidated;

        $comments = preg_replace('/<br\s*\/?>/', "\n", $review->{EloquentReview::FIELD_COMMENTS});

        $foundExistingReviewer = false;
        if ($reviewerId > 0 && array_key_exists($reviewerId, $this->convertedReviewers)) {
            $newReviewerId = $this->convertedReviewers[$reviewerId];
            $foundExistingReviewer = true;
        }
        else {
            $newReviewerId = Reviewer::query()->insertGetId([
                Reviewer::FIELD_REFERENCE           => Uuid::uuid4()->toString(),
                Reviewer::FIELD_NAME                => $reviewerName,
                Reviewer::FIELD_EMAIL               => $review->{EloquentReview::FIELD_USER_EMAIL},
                Reviewer::FIELD_PHONE               => preg_replace("/\D/", '', $review->{EloquentReview::RELATION_ADDRESS}?->{EloquentAddress::PHONE}),
                Reviewer::FIELD_IS_EMAIL_VERIFIED   => $emailValidated,
                Reviewer::FIELD_EMAIL_VERIFIED_AT   => $emailValidated ? $creationDate : null,
                Reviewer::FIELD_IS_PHONE_VERIFIED   => $phoneValidated,
                Reviewer::CREATED_AT                => $creationDate,
            ]);
        }

        $newReviewDataId = ReviewData::query()->insertGetId([
            ReviewData::FIELD_OVERALL_SCORE => $this->clampRating((int) $review->{EloquentReview::FIELD_OVERALL_SCORE}),
            ReviewData::FIELD_TITLE         => $review->{EloquentReview::FIELD_TITLE},
            ReviewData::FIELD_COMMENTS      => $comments,
            ReviewData::FIELD_DATA          => json_encode($reviewDataPayload),
            ReviewData::CREATED_AT          => $creationDate,
        ]);

        $newReviewId = Review::query()->insertGetId([
            Review::FIELD_UUID                  => $reviewReference,
            Review::FIELD_REVIEWER_ID           => $newReviewerId,
            Review::FIELD_COMPANY_ID            => $company?->{Company::FIELD_ID} ?? 0,
            Review::FIELD_REVIEW_DATA_ID        => $newReviewDataId,
            Review::FIELD_WEBSITE_ID            => $website->{Website::FIELD_ID},
            Review::FIELD_COMPANY_LOCATION_ID   => $this->addressToLocationMap[$review->{EloquentReview::FIELD_ADDRESS_ID}] ?? null,
            Review::FIELD_STATUS                => $reviewStatus->value,
            Review::FIELD_INDUSTRY_ID           => $this->solarIndustry,
            Review::FIELD_INDUSTRY_SERVICE_ID   => $this->installationService,
            Review::FIELD_IS_VERIFIED           => $reviewVerified,
            Review::CREATED_AT                  => $creationDate,
        ]);

        $reviewComments = $review->{EloquentReview::RELATION_COMMENTS};
        if (!$reviewComments->isEmpty()) {
            foreach ($reviewComments as $comment) {
                ReviewReply::query()->insertgetId([
                    ReviewReply::FIELD_REVIEW_ID            => $newReviewId,
                    ReviewReply::FIELD_COMMENTS             => $comment->{EloquentComment::COMMENT},
                    ReviewReply::FIELD_IS_PUBLIC            => $comment->{EloquentComment::IS_PUBLIC},
                    ReviewReply::FIELD_CUSTOM_ADMIN_NAME    => $comment->user?->{EloquentUser::DISPLAY_NAME},
                ]);
            }
        }

        if ($reviewerId > 0 && !$foundExistingReviewer)
            $this->convertedReviewers[$reviewerId] = $newReviewerId;

        return true;
    }

    public function makeStringPayload(?string $value): array {
        $value = ($value === '')
            ? null
            : $value;

        return [
            ReviewData::DATA_KEY_TYPE  => ReviewQuestionDataType::STRING->value,
            ReviewData::DATA_KEY_VALUE => $value,
        ];
    }

    public function makeIntPayload(string|int|null $value): array {
        if (gettype($value) === 'string')
            $value = ($value === '')
                ? null
                : preg_replace("/\D/", '', $value);
        if ($value !== null)
            $value = (int) $value;

        return [
            ReviewData::DATA_KEY_TYPE  => ReviewQuestionDataType::INT->value,
            ReviewData::DATA_KEY_VALUE => $value,
        ];
    }

    public function makeFloatPayload(string|int|null $value): array {
        if (gettype($value) === 'string')
            $value = ($value === '')
                ? null
                : preg_replace("/[^\d.]/", '', $value);
        if ($value !== null)
            $value = (float) $value;

        return [
            ReviewData::DATA_KEY_TYPE  => ReviewQuestionDataType::FLOAT->value,
            ReviewData::DATA_KEY_VALUE => $value,
        ];
    }

    public function makeRatingPayload(string|int|null $value): array {
        if (gettype($value) === 'string')
            $value = preg_replace("/\D/", '', $value);
        if ($value !== null)
            $value = $this->clampRating((int) $value);

        return [
            ReviewData::DATA_KEY_TYPE  => ReviewQuestionDataType::RATING->value,
            ReviewData::DATA_KEY_VALUE => $value,
        ];
    }

    public function clampRating(int $value): int
    {
        return max(0, min((int) $value, 5));
    }

    public function makeBoolPayload(bool|int|string|null $value): array {
        if (gettype($value) === 'string')
            $value = preg_match("/(yes|true|1)/i", $value);

        return [
            ReviewData::DATA_KEY_TYPE  => ReviewQuestionDataType::BOOL->value,
            ReviewData::DATA_KEY_VALUE => (bool) $value,
        ];
    }

    public function wouldRecommendMap(string $value): array {
        $v = match($value) {
            '1-Yes', '1' => true,
            '2-No', '-1' => false,
            default => null,
        };

        return [
            ReviewData::DATA_KEY_TYPE  => ReviewQuestionDataType::BOOL->value,
            ReviewData::DATA_KEY_VALUE => $v,
        ];
    }

    public function getCompanyId(?int $companyId): ?string {
        return $this->companyIdMap[$companyId] ?? null;
    }

    /**
     * @param EloquentReview $review
     * @param string $uuid
     * @return array
     */
    public function buildReviewDataPayload(EloquentReview $review, string $uuid): array
    {
        $baseUrl = config('services.google.storage.base_url');
        $bucket = config('services.google.storage.buckets.review_attachments');
        $this->storageBaseUrl = preg_replace("/\/+/", '/', "$baseUrl/$bucket/");

        $textAnswers = [];
        $ratingAnswers = [];
        $customData = [];
        $attachmentData = [];
        $otherAnswers = [];

        $ratingMap = [
            '1-Very Poor' => 1,
            '2-Poor' => 2,
            '3-Fair' => 3,
            '4-Good' => 4,
            '5-Excellent' => 5,
        ];

        $zip_code = $review->{EloquentReview::RELATION_ADDRESS}?->{EloquentAddress::ZIP_CODE};
        $installedYear = null;

        foreach ($review->{EloquentReview::RELATION_ANSWERS} as $answer) {
            $question = $answer->{EloquentReviewAnswer::RELATION_QUESTION};
            if ($question->{EloquentReviewQuestion::FIELD_TYPE} === 'slider') {
                $key = $question->{EloquentReviewQuestion::FIELD_QUESTION_NAME} === 'rating_price_changed'
                    ? 'rating_price_charged' //fix DB typo
                    : $question->{EloquentReviewQuestion::FIELD_QUESTION_NAME};
                $rating = $answer->{EloquentReviewAnswer::FIELD_ANSWER};
                if (array_key_exists($rating, $ratingMap)) {
                    $ratingAnswers[$key] = $this->makeRatingPayload($ratingMap[$rating]);
                } else {
                    $ratingAnswers[$key] = $this->makeRatingPayload($rating);
                }
            } else if ($question->{EloquentReviewQuestion::FIELD_TYPE} === 'select') {
                $textAnswers[$question->{EloquentReviewQuestion::FIELD_QUESTION_NAME}] = $this->makeStringPayload($answer->{EloquentReviewAnswer::FIELD_ANSWER});
            } else if ($question->{EloquentReviewQuestion::FIELD_TYPE} === 'choice') {
                if ($question->{EloquentReviewQuestion::FIELD_QUESTION_NAME} === 'recommend_installer')
                    $otherAnswers[$question->{EloquentReviewQuestion::FIELD_QUESTION_NAME}] = $this->wouldRecommendMap($answer->{EloquentReviewAnswer::FIELD_ANSWER});
                else
                    $otherAnswers[$question->{EloquentReviewQuestion::FIELD_QUESTION_NAME}] = $this->makeBoolPayload($answer->{EloquentReviewAnswer::FIELD_ANSWER});
            } else if ($question->{EloquentReviewQuestion::FIELD_QUESTION_NAME} === 'installed_year') {
                $installedYear = $answer->{EloquentReviewAnswer::FIELD_ANSWER};
            } else if ($question->{EloquentReviewQuestion::FIELD_QUESTION_NAME} === 'system_size') {
                $otherAnswers[$question->{EloquentReviewQuestion::FIELD_QUESTION_NAME}] = $this->makeFloatPayload($answer->{EloquentReviewAnswer::FIELD_ANSWER});
            } else {
                $textAnswers[Str::snake($question->{EloquentReviewQuestion::FIELD_QUESTION_NAME})] = $this->makeStringPayload($answer->{EloquentReviewAnswer::FIELD_ANSWER});
            }
            if ($question->{EloquentReviewQuestion::FIELD_QUESTION_NAME} === 'zip_code') {
                $zip_code = $answer->{EloquentReviewAnswer::FIELD_ANSWER};
            }
        }

        $reviewAttachments = $review->{EloquentReview::RELATION_REVIEW_ATTACHMENTS};
        if (!$reviewAttachments->isEmpty()) {
            foreach ($reviewAttachments as $attachment) {
                $path = preg_replace("/[\/]{2,}/", '/', $this->storageBaseUrl . "/$uuid/" . $attachment->{EloquentReviewAttachment::ATTRIBUTE_PATH});
                $attachmentData[] = [
                    ReviewData::ATTACHMENT_KEY_PUBLIC_URL => $path,
                    ReviewData::ATTACHMENT_KEY_ID         => '',
                ];
            }
        }

        $eloquentReviewExtraData = $review->{EloquentReview::RELATION_REVIEW_EXTRA_DATA};
        if ($eloquentReviewExtraData) {
            $inverterCompany  = $this->getCompanyId($eloquentReviewExtraData->{EloquentReviewExtraData::FIELD_INVERTER_BRAND});
            $panelCompany     = $this->getCompanyId($eloquentReviewExtraData->{EloquentReviewExtraData::FIELD_PANEL_BRAND});
            $installerCompany = $this->getCompanyId($eloquentReviewExtraData->{EloquentReviewExtraData::FIELD_COMPANY_INSTALLED});
            $installedYear = $installedYear ?: $eloquentReviewExtraData->{EloquentReviewExtraData::FIELD_YEAR_INSTALLED};
            $systemPrice = $eloquentReviewExtraData->{EloquentReviewExtraData::FIELD_SYSTEM_PRICE};

            $customData = [
                EloquentReviewExtraData::FIELD_PRODUCT_TYPE           => $this->makeStringPayload($eloquentReviewExtraData->{EloquentReviewExtraData::FIELD_PRODUCT_TYPE}),
                EloquentReviewExtraData::FIELD_SYSTEM_SIZE            => $this->makeFloatPayload($eloquentReviewExtraData->{EloquentReviewExtraData::FIELD_SYSTEM_SIZE}),
                EloquentReviewExtraData::FIELD_SYSTEM_PRICE           => $this->makeIntPayload($systemPrice),
                EloquentReviewExtraData::FIELD_PRICE_AFTER_INCENTIVES => $systemPrice > 0
                    ? $this->makeBoolPayload($eloquentReviewExtraData->{EloquentReviewExtraData::FIELD_PRICE_AFTER_INCENTIVES})
                    : null,
                ReviewQuestion::YEAR_INSTALLED->value                 => $this->makeIntPayload($installedYear),
                EloquentReviewExtraData::FIELD_MODEL_REFERENCE        => $this->makeStringPayload($eloquentReviewExtraData->{EloquentReviewExtraData::FIELD_MODEL_REFERENCE}),
                EloquentReviewExtraData::FIELD_INVERTER_BRAND         => $this->makeStringPayload($inverterCompany),
                EloquentReviewExtraData::FIELD_PANEL_BRAND            => $this->makeStringPayload($panelCompany),
                EloquentReviewExtraData::FIELD_COMPANY_INSTALLED      => $this->makeStringPayload($installerCompany),
                ReviewQuestion::REVIEW_TYPE->value                    => $this->makeStringPayload($review->{EloquentReview::FIELD_REVIEW_TYPE}),
                ReviewQuestion::SYSTEM_TYPE->value                    => $this->makeStringPayload($review->{EloquentReview::FIELD_SYSTEM_TYPE}),
            ];
        }

        $user = $review->user;
        $displayLocationParts = array_filter([$user?->location, $user?->state], fn($v) => $v);

        return [
            ReviewData::DATA_KEY_USER_IP          => $review->{EloquentReview::FIELD_IP_ADDRESS},
            ReviewData::DATA_KEY_ZIP_CODE         => $zip_code,
            ReviewData::DATA_KEY_DISPLAY_LOCATION => $displayLocationParts ? implode(', ', $displayLocationParts) : '',
            ReviewData::DATA_KEY_CUSTOM           => [
                ...$customData,
                ...$textAnswers,
                ...$ratingAnswers,
                ...$otherAnswers,
            ],
            ReviewData::DATA_KEY_ATTACHMENTS      => $attachmentData,
        ];
    }
}
