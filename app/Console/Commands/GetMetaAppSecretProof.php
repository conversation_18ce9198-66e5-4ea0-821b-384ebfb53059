<?php

namespace App\Console\Commands;

use App\Enums\Advertising\Advertiser;
use App\Models\ClientToken;
use App\Models\ClientTokenService;
use App\Services\Advertising\Authentication\MetaAdsAuthService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;

class GetMetaAppSecretProof extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:meta-app-secret-proof';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate the Meta App Secret Proof';

    /**
     * Execute the console command.
     */
    public function handle(MetaAdsAuthService $metaAdsAuthService)
    {
        $advertisers = array_flip(Advertiser::advertisersByKey());

        $advertiserDisplayName = $this->choice(
            'Which advertiser?',
            array_keys($advertisers),
            0
        );

        $advertiser = Advertiser::fromKey($advertisers[$advertiserDisplayName]);

        if($advertiser === Advertiser::GABE) {
            $appSecrets = $metaAdsAuthService->generateAppSecretProof(
                $this->getAccessToken($advertiser),
                config('services.meta.ads.app_secret')
            );
        }
        else if($advertiser === Advertiser::WADE) {
            $appSecrets = $metaAdsAuthService->generateAppSecretProof(
                $this->getAccessToken($advertiser),
                config('services.meta.wade_ads.app_secret')
            );
        }
        else {
            throw new Exception("Invalid advertiser");
        }

        $this->info("Chose {$advertiserDisplayName}");

        $this->newLine();
        foreach($appSecrets as $key => $value) {
            $this->info("{$key}: {$value}");
        }
        $this->newLine();

        return 0;
    }

    /**
     * @param Advertiser $advertiser
     * @return string
     */
    private function getAccessToken(Advertiser $advertiser): string
    {
        return ClientToken::query()
            ->whereHas(ClientToken::RELATION_CLIENT_TOKEN_SERVICE, function($has) {
                $has->where(ClientTokenService::TABLE.'.'.ClientTokenService::FIELD_SERVICE_KEY, ClientTokenService::META_ADS_API_SERVICE_KEY);
            })
            ->where(ClientToken::FIELD_ADDITIONAL_DATA.'->'.ClientToken::ADDITIONAL_DATA_ADVERTISER, Advertiser::getKey($advertiser))
            ->firstOrFail()
            ->{ClientToken::FIELD_CLIENT_TOKEN};
    }
}
