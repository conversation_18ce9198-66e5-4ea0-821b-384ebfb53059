<?php

namespace App\Console\Commands;

use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Symfony\Component\Console\Helper\ProgressBar;

class PopulateCountyAndLocationIdsInAddress extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'populate:address-county-and-location-ids';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add the missing county and location ids in addresses table';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $zipCodes = Address::query()->selectRaw('DISTINCT ' . Address::FIELD_ZIP_CODE)
            ->get();

        $progressBar = $this->output->createProgressBar($zipCodes->count());

        foreach ($zipCodes->chunk(500) as $chunk) {
            $this->processZipCodes($chunk, $progressBar);
        }

        $progressBar->finish();
    }

    /**
     * @param Collection $zipCodes
     *
     * @return void
     */
    protected function processZipCodes(Collection $zipCodes, ProgressBar $progressBar): void
    {
        Location::query()
            ->select([
                Location::TABLE . '.' . Location::ZIP_CODE,
                Location::TABLE . '.' . Location::COUNTY,
                Location::TABLE . '.' . Location::ID,
                DB::raw('county_locations.id AS county_location_id'),
                DB::raw('state_locations.id AS state_location_id')
            ])
            ->join(Location::TABLE . ' AS county_locations', fn(JoinClause $joinClause) => $joinClause->on(Location::TABLE . '.' . Location::COUNTY_KEY, 'county_locations.county_key')
                ->on(Location::TABLE . '.' . Location::STATE_ABBREVIATION, 'county_locations.state_abbr')
                ->where('county_locations.type', Location::TYPE_COUNTY))
            ->join(Location::TABLE . ' AS state_locations', fn(JoinClause $joinClause) => $joinClause->on(Location::TABLE . '.' . Location::STATE_ABBREVIATION, 'state_locations.state_abbr')
                ->where('state_locations.type', Location::TYPE_STATE))
            ->where(Location::TABLE . '.' . Location::TYPE, Location::TYPE_ZIP_CODE)
            ->whereIn(Location::TABLE . '.' . Location::ZIP_CODE, $zipCodes->pluck(Address::FIELD_ZIP_CODE)->toArray())
            ->get()
            ->each(fn($model) => Address::withoutEvents(fn() => Address::query()
                ->where(Address::FIELD_ZIP_CODE, $model->zip_code)
                ->update([
                    Address::FIELD_COUNTY               => $model->county,
                    Address::FIELD_ZIP_CODE_LOCATION_ID => $model->id,
                    Address::FIELD_COUNTY_LOCATION_ID   => $model->county_location_id,
                    Address::FIELD_STATE_LOCATION_ID    => $model->state_location_id,
                ]))
            );

        $progressBar->advance($zipCodes->count());
    }
}
