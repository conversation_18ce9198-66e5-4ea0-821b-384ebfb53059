<?php

namespace App\Console\Commands;

use App\Models\Legacy\EloquentCompany;
use App\Models\Odin\Company;
use App\Models\Odin\Industry;
use App\Services\DatabaseHelperService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class UpdateIndustryForLegacyRoofingCompanies extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:industry-for-legacy-roofing-companies';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add/update roofing industry for the roofing companies that do not have industry set or set it to wrong';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        /** @var Industry|null $roofingIndustry */
        $roofingIndustry = Industry::query()->where(Industry::FIELD_NAME, Industry::INDUSTRY_ROOFING)->first();

        if (!$roofingIndustry) {
            $this->error('Roofing industry does not exist.');
            return;
        }

        $query = Company::query()
            ->selectRaw(Company::TABLE . '.' . '*')
            ->join(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentCompany::TABLE, Company::TABLE . '.' . Company::FIELD_LEGACY_ID, '=', DatabaseHelperService::readOnlyDatabase() . '.' . EloquentCompany::TABLE . '.' . EloquentCompany::COMPANY_ID)
            ->where(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentCompany::TABLE . '.' . EloquentCompany::TYPE, EloquentCompany::TYPE_ROOFER)
            ->whereDoesntHave(Company::RELATION_INDUSTRIES, function (Builder $query) {
                $query->where(Industry::TABLE . '.' . Industry::FIELD_NAME, Industry::INDUSTRY_ROOFING);
            });

        $this->info("Adding/Updating industry for {$query->count()} companies");

        $query->chunkById(100, function (Collection $companies) use ($roofingIndustry) {
            $this->info("Processing {$companies->count()} companies. Ids: {$companies->pluck(Company::FIELD_ID)->join(', ')}");
            /** @var Company $company */
            foreach ($companies as $company) {
                $company->industries()->detach();
                $company->industries()->attach($roofingIndustry->id);
            }
        });

        $this->info('Finished.');
    }
}
