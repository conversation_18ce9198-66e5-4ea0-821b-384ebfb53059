<?php

namespace App\Console\Commands;

use App\Jobs\CalculateAndStoreGoogleRatingsJob;
use Illuminate\Console\Command;

class CalculateAndStoreGoogleRatings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'calculate:google-ratings';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Calculates and stores expert ratings against company data';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        CalculateAndStoreGoogleRatingsJob::dispatchSync();
    }
}
