<?php

namespace App\Console\Commands;

use App\DTO\Prospects\NewBuyerProspectDTO;
use App\Enums\Prospects\ProspectSource;
use App\Enums\Prospects\ProspectStatus;
use App\Enums\Prospects\ProspectResolution;
use App\Models\Prospects\NewBuyerProspect;
use Illuminate\Support\Arr;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Validator;
use League\Csv\Reader;
use League\Csv\Statement;

class ImportBuyerProspectsCommand extends Command
{
    protected $signature = 'prospects:import {file : Path to the CSV file}';

    protected $description = 'Import prospects from a CSV file';

    /**
     * @param NewBuyerProspectDTO $prospectDTO
     * @return bool
     */
    protected function validate(NewBuyerProspectDTO $prospectDTO): bool
    {
        $validator = Validator::make($prospectDTO->toArray(), [
            'company_name' => 'required|string|max:255',
            'status' => 'required|in:' . implode(',', array_column(ProspectStatus::cases(), 'value')),
            'source' => 'required|in:' . implode(',', array_column(ProspectSource::cases(), 'value')),
            'decision_maker_email' => 'nullable|email|max:255',
            'company_website' => 'nullable|url|max:255',
            'industry_service_ids' => 'sometimes|array',
            'industry_service_ids.*' => 'integer'
        ]);

        if ($validator->fails()) {
            $this->error("Validation failed for record: " . $validator->errors()->toArray());
            return false;
        }
        return true;
    }

    /**
     * @param string|null $industry
     * @return array
     *
     * Solar Installation = 1
     * Roof Repair = 5
     * Windows - Install & Replace = 21
     * Siding - Replacement = 22
     * HVAC Central Air - Install & Replace - 15
     * Bathroom Remodeling - 30
     * Kitchen Remodeling - 31
     * Siding - Replacement - 22
     */
    protected function parseIndustryToIndustryService(?string $industry): array
    {
        return match ($industry) {
            'solar' => [1],
            'roofing' => [5],
            'windows' => [21],
            'siding' => [22],
            'hvac' => [15],
            'bathrooms' => [30],
            'remodeling' => [30, 31],
            default => [$industry],
        };
    }

    /**
     * @param array $row
     * @return string
     */
    protected function parseAddress(array $row): string {
       $addr = $row['street1'];
       if (isset($row['street2']) && $row['street2'] !== 'None') {
           $addr .= " {$row['street2']}";
       }
       $addr.= ", {$row['zip_code']}";
       return $addr;
    }

    protected function parseSourceData(array $row): array {
        return [
            'zip_codes' => [$row['zip_code']],
            'years_in_business' => $row['years_in_business'],
            'is_advertiser' => $row['is_advertiser'],
            'recent_lead_count' => $row['recent_lead_count'],
            'business_type' => $row['business_type'],
            'marketPlace' => $row['marketPlace'],
            'has_current_agreement' => $row['has_current_agreement'],
            'angi_profile_url' => $row['angi_profile_url'],
        ];
    }
    protected function parseRow(array $row): NewBuyerProspectDTO
    {
        return new NewBuyerProspectDTO(
            external_reference: $row['angi_company_id'],
            status: ProspectStatus::INITIAL,
            resolution: null,
            industry_service_ids: $this->parseIndustryToIndustryService($row['industry']),
            source: ProspectSource::ALIST,
            source_data: $this->parseSourceData($row),
            company_name: $row['company_name'],
            company_description: $row['company_description'] ?? null,
            company_website: null,
            address_street: $this->parseAddress($row),
            address_city_key: null,
            address_state_abbr: null,
            decision_maker_first_name: null,
            decision_maker_last_name: null,
            decision_maker_email: null,
            notes: null
        );
    }

    public function handle()
    {
        $filePath = $this->argument('file');
        if (!file_exists($filePath)) {
            $this->error("File does not exist: $filePath");
            return self::FAILURE;
        }

        $this->info("Importing from $filePath");

        try {
            $csv = Reader::createFromPath($filePath, 'r');
            $csv->setHeaderOffset(0);
            $successCount = 0;
            $failureCount = 0;

            $records = (new Statement())->process($csv);
            foreach ($records as $record) {
                try {

                    $prospectDTO = $this->parseRow($record);
                    if (!$this->validate($prospectDTO)) {
                        continue;
                    }

                    $this->info("Importing prospect for {$prospectDTO->company_name}");
                    $existingBuyerProspect = NewBuyerProspect::query()->where('external_reference', $prospectDTO->external_reference)->first();
                    if ($existingBuyerProspect === null) {
                        NewBuyerProspect::create($prospectDTO->toArray());
                        $this->info("Created new prospect for {$prospectDTO->company_name}");
                        $successCount++;
                    } else {
                        $this->warn("Existing Prospects found for {$prospectDTO->company_name}. Updating...");
                        $zip_codes = array_merge($existingBuyerProspect->source_data['zip_codes'], $prospectDTO->source_data['zip_codes']);
                        $industries = array_merge($existingBuyerProspect->industry_service_ids, $prospectDTO->industry_service_ids);
                        $existingBuyerProspect->update([
                            'source_data' => array_merge($existingBuyerProspect->source_data, ['zip_codes' => $zip_codes]),
                            'industry_service_ids' =>  collect($industries)->unique()->toArray(),
                        ]);
                        $existingBuyerProspect->save();
                    }

                } catch (\Exception $e) {
                    $this->error("Error processing record: " . $e->getMessage() . "\n" . $e->getTraceAsString());
                    $failureCount++;
                }
            }

            $this->info("Import complete. Successful: $successCount, Failed: $failureCount");
            return self::SUCCESS;

        } catch (\Exception $e) {
            $this->error("CSV Import failed: " . $e->getMessage());
            return self::FAILURE;
        }
    }
}
