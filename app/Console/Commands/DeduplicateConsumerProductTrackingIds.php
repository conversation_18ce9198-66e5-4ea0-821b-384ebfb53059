<?php

namespace App\Console\Commands;

use App\Models\Odin\ConsumerProduct;
use Carbon\Carbon;
use Illuminate\Console\Command;

class DeduplicateConsumerProductTrackingIds extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:deduplicate-consumer-product-tracking-ids';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix duplicate consumer product tracking IDs in the consumer products table';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info("\nDeduplicating consumer product tracking IDs \n");

        $progress = 0;

        ConsumerProduct::query()
            ->selectRaw(sprintf(
                "COUNT(DISTINCT %s) AS `count`, %s, %s",
                ConsumerProduct::FIELD_CONSUMER_ID,
                "GROUP_CONCAT(".ConsumerProduct::FIELD_CONSUMER_ID." SEPARATOR ',') AS `consumer_ids`",
                ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID
            ))
            ->groupBy(ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID)
            ->havingRaw("`count` > 1")
            ->chunk(1000, function($rows) use (&$progress) {
                $now = Carbon::now('UTC')->format('Y-m-d H:i:s');

                $smallerConsumerIds = [];
                foreach($rows as $row) {
                    $consumerIds = array_unique(array_map(fn($id) => (int) $id, explode(',', $row['consumer_ids'])));

                    if(count($consumerIds) === 1) {
                        continue;
                    }

                    sort($consumerIds);

                    array_pop($consumerIds);

                    $smallerConsumerIds = array_merge($smallerConsumerIds, $consumerIds);
                }

                if(!empty($smallerConsumerIds)) {
                    ConsumerProduct::query()
                        ->whereIntegerInRaw(ConsumerProduct::FIELD_CONSUMER_ID, $smallerConsumerIds)
                        ->update([
                            ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID => null,
                            ConsumerProduct::FIELD_UPDATED_AT => $now
                        ]);
                }

                $progress += count($rows);

                $this->info("Processed: {$progress} \r");
            });

        $this->info("\nFinished \n");
    }
}
