<?php

namespace App\Console\Commands;

use App\Jobs\ConsumerProcessing\CreateInitialProductJob;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use Illuminate\Console\Command;

class AddMissingLeadsToInitial extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'create:missing-initial-leads';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add leads to initial queue';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $consumerProductIds = $this->ask('Enter comma separated consumer product ids');

        $consumerProductIds = explode(',', $consumerProductIds);

        $consumerProducts = ConsumerProduct::query()
            ->select(ConsumerProduct::FIELD_ID, ConsumerProduct::FIELD_CONSUMER_ID)
            ->with([
                       ConsumerProduct::RELATION_CONSUMER => function ($with) {
                           $with->select([
                                             Consumer::FIELD_ID,
                                             Consumer::FIELD_REFERENCE,
                                         ]);
                       }
                   ])->whereIn(ConsumerProduct::FIELD_ID, $consumerProductIds)
            ->get();


        foreach($consumerProducts as $consumerProduct) {
            CreateInitialProductJob::dispatch($consumerProduct->id);
        }

        return 0;
    }
}
