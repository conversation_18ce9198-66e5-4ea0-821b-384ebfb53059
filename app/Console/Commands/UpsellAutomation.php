<?php

namespace App\Console\Commands;

use App\Enums\UpsellAutomationVariableEnum;
use App\Jobs\UpsellAutomationJob;
use Illuminate\Console\Command;
use Symfony\Component\Console\Input\InputOption;

class UpsellAutomation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:upsell-automation
        { --queue : Pass this flag to run the upsell automation job on the long-running queue. Otherwise the job will be dispatch synced to this command. }'; // The rest of the options are defined dynamically from UpsellAutomationVariableEnum meta-data

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to upsell available legs. Automation parameters defined in Global Configurations Management with key \'upsell_automation_variables\'';

    const string DEFAULT_CMD_AUTOMATION_LOG_NAME = 'Manual Command Log';

    public function __construct()
    {
        parent::__construct();

        // Dynamically modify the signature to add options from upsell automation variable enum
        $this->setDynamicOptions();
    }

    /**
     * @return void
     */
    protected function setDynamicOptions(): void
    {
        // Add automation parameters as command line options
        foreach (UpsellAutomationVariableEnum::cases() as $variable) {
            $metaData = $variable->getMetaData();
            if ($metaData[UpsellAutomationVariableEnum::KEY_ARG] ?? null) {
                $this->addOption(
                    $metaData[UpsellAutomationVariableEnum::KEY_ARG],
                    null,
                    InputOption::VALUE_REQUIRED,
                    $metaData[UpsellAutomationVariableEnum::KEY_DESC]
                );
            }
        }
    }

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        // Get config parameters from command line
        $config = [
            UpsellAutomationVariableEnum::AUTOMATION_ENABLED->value => true, // Hard coded is enabled - command will always work whether automation is enabled or not in the config
            UpsellAutomationVariableEnum::ACTIVE_START_UTC->value => '00:00:00', // Hard coded to ignore automation times when run manually
            UpsellAutomationVariableEnum::ACTIVE_END_UTC->value => '23:59:59', // Hard coded to ignore automation times when run manually
            UpsellAutomationVariableEnum::AUTOMATION_DELAY_MINS->value => 0, // Automation Delay set to 0 when ran from command
            UpsellAutomationVariableEnum::BATCH->value => (bool)$this->option('queue'),
        ];

        // Get input variables
        foreach (UpsellAutomationVariableEnum::cases() as $variable) {
            // Get meta data for each variable
            $metaData = $variable->getMetaData();

            // If the KEY_ARG is defined then this variable can be passed as a command line argument
            if ($metaData[UpsellAutomationVariableEnum::KEY_ARG] ?? null) {
                // Check command line for argument
                $option = $this->option($metaData[UpsellAutomationVariableEnum::KEY_ARG]);

                // Set argument in config if given
                if ($option)
                    $config[$variable->value] = $variable->castToType($option);
            }
        }

        // If name is not given as parameter, use default command name for upsell automation log
        if (!array_key_exists(UpsellAutomationVariableEnum::LOG_NAME->value, $config))
            $config[UpsellAutomationVariableEnum::LOG_NAME->value] = self::DEFAULT_CMD_AUTOMATION_LOG_NAME;

        // Dispatch sync by default, dispatch on queue if --queue was passed
        if ($this->option('queue')) {
            UpsellAutomationJob::dispatch($config);
        } else {
            UpsellAutomationJob::dispatchSync($config);
        }

    }
}
