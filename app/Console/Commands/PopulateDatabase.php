<?php

namespace App\Console\Commands;

use App\Models\Odin\Company;
use Database\Seeders\Populators\DatabasePopulator;
use Illuminate\Console\Command;

class PopulateDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:populate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Populate the Admin2 database with dummy data';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        if(config('app.env') === 'production') {
            $this->line("Database populator cannot be run in Production.");
            return 0;
        }
        $existingCompanies = Company::all()->count();
        if ($existingCompanies) {
            if (!$this->confirm("This seeder is intended for a fresh database, but there are {$existingCompanies} Companies already present in the table. Are you sure you wish to proceed?")) {
                return 0;
            }
        }

        $this->call(DatabasePopulator::class);

        $companiesCreated = Company::all()->count();
        $this->info("{$companiesCreated} Companies now in database.");

        return 1;
    }
}
