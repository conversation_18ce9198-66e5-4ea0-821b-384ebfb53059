<?php

namespace App\Console\Commands;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyData;
use App\Repositories\Odin\CompanyRepository;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;

class UpdateCompanyZoomInfoData extends Command
{
    const API_KEY_ZOOM_INFO_TOKEN = 'jwt';

    const API_ZOOM_INFO_BASE_URI           = 'https://api.zoominfo.com';
    const API_ZOOM_INFO_AUTHENTICATE_URI   = self::API_ZOOM_INFO_BASE_URI . '/authenticate';
    const API_ZOOM_INFO_ENRICH_COMPANY_URI = self::API_ZOOM_INFO_BASE_URI . '/enrich/company';

    const CHOICE_ALL_COMPANIES = 'All Companies';
    const CHOICE_ONLY_COMPANIES_WITH_MISSING_DATA = 'Only Companies with missing data';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'zoom_info:update_company_data';

    /** @var string|null $zoomInfoToken */
    protected ?string $zoomInfoToken = null;

    /** @var string $zoomInfoEmail */
    protected string $zoomInfoEmail;

    /** @var string $zoomInfoPassword */
    protected string $zoomInfoPassword;

    /** @var string $choice */
    protected string $choice;

    /** @var CompanyRepository $companyRepository */
    protected CompanyRepository $companyRepository;

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handles updating data for Companies that have a Zoom Info ID.';

    /**
     * @param CompanyRepository $companyRepository
     */
    public function __construct(CompanyRepository $companyRepository)
    {
        parent::__construct();
        $this->companyRepository = $companyRepository;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        $this->setupZoomInfo();
        $this->getZoomInfoData();
    }

    /**
     * @return void
     */
    private function setupZoomInfo(): void
    {
        $this->zoomInfoEmail = $this->ask('Enter your ZoomInfo email log in');
        $this->zoomInfoPassword = $this->secret('Enter your ZoomInfo password');

        $this->authenticateZoomInfo();

        if (is_null($this->zoomInfoToken)) {
            $this->warn('Unable to generate an auth token for ZoomInfo');
            exit();
        }

        $this->choice = $this->choice('Would you like to get ZoomInfo data for all companies with a ZoomInfoID or only for companies that are missing Employee Count or Revenue data?', [self::CHOICE_ALL_COMPANIES, self::CHOICE_ONLY_COMPANIES_WITH_MISSING_DATA]);
    }

    /**
     * @return void
     */
    private function authenticateZoomInfo(): void
    {
        $response = Http::withHeaders([
            'Content-Type' => 'application/json'
        ])->post(self::API_ZOOM_INFO_AUTHENTICATE_URI, [
            'username' => $this->zoomInfoEmail,
            'password' => $this->zoomInfoPassword
        ]);

        $this->zoomInfoToken = Arr::get($response->json(), self::API_KEY_ZOOM_INFO_TOKEN);
    }

    /**
     * @return void
     */
    private function getZoomInfoData(): void
    {
        $this->info("Getting ZoomInfo Data for companies");

        try {
            $companies = $this->getCompaniesToProcess();

            $bar = $this->output->createProgressBar($companies->count());
            $bar->start();

            $companies->shuffle()->map(function ($company) use ($bar) {
                $company->refresh();

                if (!$company->checked) {
                    $this->saveZoomInfoData($company);
                }

                $bar->advance();
            });
        } catch (\Exception $e) {
            logger()->error("Failed to execute company discovery contract. Error: {$e->getMessage()}");
        }
    }

    /**
     * @return Collection|Company[]|null
     */
    private function getCompaniesToProcess(): Collection|Company|null
    {
        if($this->choice === self::CHOICE_ALL_COMPANIES) {
            return $this->companyRepository->getCompaniesWithZoomInfoID();
        }

        return $this->companyRepository->getCompaniesWithZoomInfoIDWithoutZoomInfoData();
    }

    /**
     * @param Company $company
     * @return void
     */
    private function saveZoomInfoData(Company $company): void
    {
        $data = $this->getCompanyZoomInfoData($company);

        if ($data) {
            $this->companyRepository->updateCompanyData($company, $data);
        }
    }

    /**
     * @param Company $company
     * @return array|bool
     */
    private function getCompanyZoomInfoData(Company $company): array|bool
    {
        $zoomInfoId = $this->companyRepository->getCompanyDataByKey($company, 'zoom_info_id');

        $response = Http::withHeaders([
            'Content-Type'  => 'application/json',
            'Authorization' => 'Bearer ' . $this->zoomInfoToken
        ])->post(self::API_ZOOM_INFO_ENRICH_COMPANY_URI, [
            'matchCompanyInput' => [
                [
                    'companyId' => $zoomInfoId
                ]
            ],
            'outputFields' => [
                'employeeCount',
                'revenue'
            ]
        ])->json();


        if(Arr::get($response, 'data.result.0.matchStatus') === 'FULL_MATCH') {
            return [
                'employee_count' => Arr::get($response, 'data.result.0.data.0.employeeCount'),
                'revenue_in_thousands' => Arr::get($response, 'data.result.0.data.0.revenue'),
            ];
        }

        return false;
    }
}
