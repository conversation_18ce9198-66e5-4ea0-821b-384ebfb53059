<?php

namespace App\Console\Commands;

use App\Models\Action;
use App\Models\Legacy\EloquentCompanyCRMAction;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class MigrateCRMActions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate-legacy:crm-actions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate the legacy CRM actions to actions table';

    /**
     * Execute the console command.
     *
     * @param EloquentCompanyCRMAction $companyCRMAction
     *
     * @return void
     */
    public function handle(EloquentCompanyCRMAction $companyCRMAction): void
    {
        $this->info('Starting migration....');
        $this->info("Total: {$companyCRMAction->newQuery()->count()}");

        $companyCRMAction->newQuery()->orderBy(EloquentCompanyCRMAction::ID)->chunk(100, function ($CRMActions) {
            /** @var Collection<EloquentCompanyCRMAction> $CRMActions */

            $this->info("Migrating {$CRMActions->count()} ....");

            Action::query()->insert(
                $CRMActions->map(fn(EloquentCompanyCRMAction $CRMAction) => [
                    Action::FIELD_FOR_ID            => $CRMAction->contactid ?: $CRMAction->companyid,
                    Action::FIELD_FOR_RELATION_TYPE => $CRMAction->contactid ? Action::RELATION_TYPE_COMPANY_CONTACT : Action::RELATION_TYPE_COMPANY,
                    Action::FIELD_SUBJECT           => 'Legacy Admin Action',
                    Action::FIELD_MESSAGE           => $CRMAction->actiontext,
                    Action::CREATED_AT              => $CRMAction->timestampadded ? Carbon::createFromTimestamp($CRMAction->timestampadded) : null,
                    Action::UPDATED_AT              => $CRMAction->timestampadded ? Carbon::createFromTimestamp($CRMAction->timestampadded) : null,
                ])->toArray()
            );

            $this->info("Done {$CRMActions->count()}");
        });

        $this->info('Finished.');
    }
}
