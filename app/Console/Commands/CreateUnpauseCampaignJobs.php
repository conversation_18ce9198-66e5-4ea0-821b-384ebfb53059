<?php

namespace App\Console\Commands;

use App\Jobs\UnpauseFutureCampaignJob;
use App\Models\Campaigns\CampaignReactivation;
use Carbon\Carbon;
use Illuminate\Console\Command;

class CreateUnpauseCampaignJobs extends Command
{
    /**
     * The name and signature of the console command.
     * @var string
     */
    protected $signature = 'create-unpause-campaign-jobs';

    /**
     * The console command description.
     * @var string
     */
    protected $description = 'Creates unpause campaign jobs to migrate from old unpause logic';

    /**
     * Execute the console command.
     * @return void
     */
    public function handle(): void
    {
        $campaignReactivations = CampaignReactivation::query()
            ->whereNotNull(CampaignReactivation::FIELD_REACTIVATE_AT)
            ->get();

        /** @var CampaignReactivation $campaignReactivation */
        foreach($campaignReactivations as $campaignReactivation) {
            $campaignReactivation->reactivate_at = $campaignReactivation->reactivate_at->addHours(12);

            $campaignReactivation->save();

            UnpauseFutureCampaignJob::dispatch($campaignReactivation->campaign_id)->delay($campaignReactivation->reactivate_at);
        }
    }
}
