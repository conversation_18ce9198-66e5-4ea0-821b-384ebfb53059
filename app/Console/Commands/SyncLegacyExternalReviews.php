<?php

namespace App\Console\Commands;

use App\Models\Legacy\EloquentCompanyAddress;
use App\Models\Odin\Address;
use App\Models\Odin\CompanyExternalReview;
use App\Models\Odin\CompanyLocation;
use App\Services\Odin\CompanySyncServices;
use Illuminate\Console\Command;
use App\Services\DatabaseHelperService;

class SyncLegacyExternalReviews extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:legacy-external-reviews';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync missing legacy external review data to admin2';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $legacyCompanyAddresses = EloquentCompanyAddress::query()
            ->whereNotNull(EloquentCompanyAddress::FIELD_GOOGLE_PLACE_ID)
            ->leftJoin(DatabaseHelperService::database() .'.'. Address::TABLE, EloquentCompanyAddress::TABLE .'.'. EloquentCompanyAddress::FIELD_ADDRESS_ID, DatabaseHelperService::database() .'.'. Address::TABLE .'.'. Address::FIELD_LEGACY_ID)
            ->leftJoin(DatabaseHelperService::database() .'.'. CompanyLocation::TABLE, DatabaseHelperService::database() .'.'. Address::TABLE .'.'. Address::FIELD_ID, DatabaseHelperService::database() .'.'. CompanyLocation::TABLE .'.'. CompanyLocation::FIELD_ADDRESS_ID)
            ->select(
                EloquentCompanyAddress::TABLE .'.'. EloquentCompanyAddress::FIELD_GOOGLE_PLACE_ID,
                EloquentCompanyAddress::TABLE .'.'. EloquentCompanyAddress::FIELD_GOOGLE_RATING,
                EloquentCompanyAddress::TABLE .'.'. EloquentCompanyAddress::FIELD_GOOGLE_REVIEW_COUNT,
                DatabaseHelperService::database() .'.'. CompanyLocation::TABLE .'.'. CompanyLocation::FIELD_ID .' as company_location_id'
            )
            ->get();

        $bar = $this->output->createProgressBar($legacyCompanyAddresses->count());
        $bar->start();

        foreach($legacyCompanyAddresses as $legacyCompanyAddress) {
            if ($legacyCompanyAddress->company_location_id) {
                CompanyExternalReview::updateOrCreate([
                    CompanyExternalReview::FIELD_COMPANY_LOCATION_ID => $legacyCompanyAddress->company_location_id,
                    CompanyExternalReview::FIELD_REFERENCE           => $legacyCompanyAddress->google_place_id,
                ], [
                    CompanyExternalReview::FIELD_COMPANY_LOCATION_ID => $legacyCompanyAddress->company_location_id,
                    CompanyExternalReview::FIELD_REFERENCE           => $legacyCompanyAddress->google_place_id,
                    CompanyExternalReview::FIELD_AGG_COUNT           => $legacyCompanyAddress->google_review_count,
                    CompanyExternalReview::FIELD_AGG_VALUE           => $legacyCompanyAddress->google_rating,
                    CompanyExternalReview::FIELD_NAME                => CompanyExternalReview::NAME_PLACES
                ]);
            }

            $bar->advance();
        }
    }
}
