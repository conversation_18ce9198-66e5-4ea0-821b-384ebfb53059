<?php

namespace App\Console\Commands;

use App\Jobs\VerifyCompaniesWebsiteJob;
use App\Models\Odin\Company;
use App\Repositories\Odin\CompanyRepository;
use App\Services\QueueHelperService;
use App\Services\WebsiteVerificationService;
use Illuminate\Bus\Batch;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Bus;
use Throwable;

class VerifyCompaniesWebsite extends Command
{
    const BATCH_SIZE = 500;
    const DISPATCH_PROCESS = "process_console";
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'verify:companies_website';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check if companies website is valid and update verified field';

    /**
     * Execute the console command.
     * @param CompanyRepository $companyRepository
     * @return void
     * @throws Throwable
     */
    public function handle(CompanyRepository $companyRepository): void
    {
        $companies = $companyRepository->getAllUnverifiedCompaniesWebsite();
        $totalRecords = $companies->count();

        if ($totalRecords > 0) {
            $this->info('Start companies verification process');
            $data = [];

            $bar = $this->output->createProgressBar($totalRecords);

            foreach ($companies->chunk(self::BATCH_SIZE) as $companies) {
                $companies->map(function ($company) use ($bar, $companyRepository) {
                    $company->refresh();

                    $websiteVerificationService = new WebsiteVerificationService($company->website);
                    $data[] = [
                        Company::FIELD_ID => $company->{Company::FIELD_ID},
                        Company::FIELD_WEBSITE_VERIFIED_URL => $websiteVerificationService->validateWebsiteUrl(),
                        Company::FIELD_WEBSITE_VERIFIED_AT => now()
                    ];

                    $bar->advance();

                    $companyRepository->updateBatchCompanyRecords($data);
                });

            }

            $bar->finish();

        } else {
            $this->newLine();
            $this->line("No companies website to verify");
        }
    }
}
