<?php

namespace App\Console\Commands;

use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductRejection;
use App\Services\DatabaseHelperService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class PopulateMissingProductRejections extends Command
{
    const ASSIGNMENT_ID = 'assignment_id';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'populate:missing-product-rejections';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create missing ProductRejection A2 models for the rejected legacy QuoteCompanies';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $this->info("Total: {$this->getQuery()->count()} missing ProductRejections need to be created");

        $this->getQuery()->chunk(100, function (Collection $quoteCompanies) {
            $this->info("Processing {$quoteCompanies->count()}");

            ProductRejection::query()->insert($this->prepareRejectionDataToInsert($quoteCompanies));

            $this->info('Processed');
        });

        return Command::SUCCESS;
    }

    /**
     * @param Collection<EloquentQuoteCompany> $quoteCompanies
     *
     * @return array
     */
    protected function prepareRejectionDataToInsert(Collection $quoteCompanies): array
    {
        $rejectionData = [];

        foreach ($quoteCompanies as $quoteCompany) {
            $rejectionData[] = [
                ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID => $quoteCompany->{self::ASSIGNMENT_ID},
                ProductRejection::FIELD_COMPANY_USER_ID => 0, //The legacy model does not have any record for the user rejected. So, setting to 0
                ProductRejection::FIELD_REASON => $quoteCompany->{EloquentQuoteCompany::REJECT_NOTES},
                ProductRejection::CREATED_AT => Carbon::createFromTimestamp($quoteCompany->{EloquentQuoteCompany::TIMESTAMP_REJECTED_AT}),
                ProductRejection::UPDATED_AT => Carbon::createFromTimestamp($quoteCompany->{EloquentQuoteCompany::TIMESTAMP_REJECTED_AT}),
                ProductRejection::FIELD_DELETED_AT => $quoteCompany->{EloquentQuoteCompany::CHARGE_STATUS} === EloquentQuoteCompany::VALUE_CHARGE_STATUS_REJECTED
                    ? null
                    : Carbon::createFromTimestamp($quoteCompany->{EloquentQuoteCompany::TIMESTAMP_REJECTED_AT})->addDay() //The legacy model does not keep the timestamp when the lead was unrejected.
            ];
        }

        return $rejectionData;
    }

    /**
     * @return Builder
     */
    protected function getQuery(): Builder
    {
        return EloquentQuoteCompany::query()
            ->selectRaw(EloquentQuoteCompany::TABLE . '.*,' . DatabaseHelperService::database() . '.' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID . ' as '. self::ASSIGNMENT_ID)
            ->where(EloquentQuoteCompany::TIMESTAMP_REJECTED_AT, '>', 0)
            ->whereNotIn(EloquentQuoteCompany::QUOTE_COMPANY_ID, function (\Illuminate\Database\Query\Builder $query) {
                $query->select(DatabaseHelperService::database() . '.' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_LEGACY_ID)
                    ->from(DatabaseHelperService::database() . '.' . ProductAssignment::TABLE)
                    ->join(
                        DatabaseHelperService::database() . '.' . ProductRejection::TABLE,
                        DatabaseHelperService::database() . '.' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID,
                        '=',
                        DatabaseHelperService::database() . '.' . ProductRejection::TABLE . '.' . ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID
                    );
            })
            ->join(
                DatabaseHelperService::database() . '.' . ProductAssignment::TABLE,
               EloquentQuoteCompany::QUOTE_COMPANY_ID,
               '=',
                DatabaseHelperService::database() . '.' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_LEGACY_ID
            )
            ->orderBy(EloquentQuoteCompany::QUOTE_COMPANY_ID, 'DESC');
    }
}
