<?php

namespace App\Console\Commands;

use App\Builders\Odin\CompanyCommunicationsBuilder;
use Illuminate\Console\Command;

class CompanyCommunications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'get:company-communications {--company-id=} {--user-id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Retrieves company communications for given company and user.';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $companyId = $this->option('company-id');
        $userId = $this->option('user-id');

        print "Company ID: $companyId\n";
        print "User ID: $userId\n\n";

        if (!$companyId && !$userId) {
            print "Must pass either a company id or a user id.\n";
            return;
        }

        $query = CompanyCommunicationsBuilder::query()
            ->forCompanyId($companyId)
            ->forUserId($userId)
            ->getQuery();
        $results = $query->get();

        foreach ($results as $result) {
            print json_encode($result)."\n\n";
        }
    }
}
