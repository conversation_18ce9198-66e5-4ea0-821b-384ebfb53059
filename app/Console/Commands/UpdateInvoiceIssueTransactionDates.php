<?php

namespace App\Console\Commands;

use App\Enums\Billing\InvoiceTransactionType;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceTransaction;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;

class UpdateInvoiceIssueTransactionDates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'billing:update-invoice-issue-transaction-dates';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command fixes invoice issue transactions ';

    /**
     * @return void
     * @throws Exception
     */
    public function handle(): void
    {
        $issuedTransactions = InvoiceTransaction::query()
            ->where(InvoiceTransaction::FIELD_TYPE, InvoiceTransactionType::ISSUED)
            ->get();

        $this->info('Updating ' . $issuedTransactions->count() . ' issue transactions...');

        $progressBar = $this->output->createProgressBar($issuedTransactions->count());

        $progressBar->start();

        foreach ($issuedTransactions as $transaction) {
            try {
                $invoice = $transaction->{InvoiceTransaction::RELATION_INVOICE};

                if (!$invoice) {
                    $this->warn('Invoice not found for transaction id ' . $transaction->{InvoiceTransaction::FIELD_ID});
                    continue;
                }

                $date = Carbon::parse($invoice->{Invoice::FIELD_ISSUE_AT});
                $time = Carbon::parse($invoice->{Invoice::FIELD_CREATED_AT});

                $newDate = $date->setTime(
                    $time->hour,
                    $time->minute,
                    $time->second
                );

                $transaction->update([
                    InvoiceTransaction::FIELD_DATE => $newDate
                ]);
            } catch (Exception $exception) {
                $this->error($exception);
            }

            $progressBar->advance();
        }

        $progressBar->finish();

        $this->newLine();
        $this->info('Update Complete!');
    }
}
