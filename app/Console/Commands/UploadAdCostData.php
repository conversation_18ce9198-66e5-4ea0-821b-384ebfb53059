<?php

namespace App\Console\Commands;

use App\Enums\Advertising\Advertiser;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Enums\Odin\Industry;
use App\Models\DailyAdCost;
use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\Location;
use App\Repositories\Advertising\GoogleAdsGeoTargetRepository;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\IndustryRepository;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class UploadAdCostData extends Command
{
    const int GOOGLE_ADS_COUNTY_DATE_START_INDEX   = 4;
    const int GOOGLE_ADS_STATE_DATE_START_INDEX    = 3;
    const int MICROSOFT_ADS_STATE_DATE_START_INDEX = 1;

    const array GABE_META_CAMPAIGN_IDS      = [54, 75, 78, 79, 82, 84, 99, 101, 104, 105, 106, 107, 111, 118, 371];
    const array GABE_MICROSOFT_CAMPAIGN_IDS = [176, 51, 98, 88, 71, 69];
    const array WADE_META_CAMPAIGN_IDS      = [742, 746, 733, 747, 745, 755, 743, 744];

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'upload:ad-cost-data';

    private int    $lineCount;
    private string $filePath;
    private int    $advertiser;
    private int    $industryId;
    private int    $platform;
    private        $handle;
    private        $bar;

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run this command to add ad cost data. File should be in database/seeders/data/';

    const string FILE_PATH = 'database/seeders/data/';

    public function __construct(
        protected GoogleAdsGeoTargetRepository $googleAdsGeoTargetRepository,
        protected IndustryRepository           $industryRepository,
        protected LocationRepository           $locationRepository
    )
    {
        parent::__construct();
    }

    /**
     * Execute the console command
     *
     * @return int
     * @throws \Exception
     */
    public function handle(): int
    {
        $fileName       = $this->ask('What is the file name?');
        $this->filePath = base_path(self::FILE_PATH).$fileName;

        $platformChoice = $this->choice("What ad platform is this for?", [AdvertisingPlatform::GOOGLE->value, AdvertisingPlatform::META->value, AdvertisingPlatform::MICROSOFT->value]);
        $this->platform = AdvertisingPlatform::getInteger($platformChoice);

        $advertiserName   = $this->choice("Which advertiser is this for?", Advertiser::displayNames());
        $this->advertiser = $advertiserName === Advertiser::WADE->getDisplayName() ? Advertiser::WADE->value : Advertiser::GABE->value;

        $industry         = $this->choice("Which industry is this for?", [Industry::SOLAR->getSlug(), Industry::ROOFING->getSlug()]);
        $this->industryId = $this->industryRepository->getIndustryIdBySlug($industry);

        $this->lineCount = $this->getCsvLineCount($this->filePath);

        $this->handle = fopen($this->filePath, "r");

        switch ($this->platform) {
            case AdvertisingPlatform::getInteger(AdvertisingPlatform::META->value):
                $this->handleMeta();
                return 0;
            case AdvertisingPlatform::getInteger(AdvertisingPlatform::MICROSOFT->value):
                $this->handleMicrosoft();
                return 0;
            case AdvertisingPlatform::getInteger(AdvertisingPlatform::GOOGLE->value):
            default:
                $this->handleGoogle();
                return 0;
        }
    }

    /**
     * @throws \Exception
     */
    private function handleMicrosoft(): void
    {
        $dates = $this->processMicrosoftStates();

        $this->estimatePlatformCounties($dates);
    }

    /**
     * @param Carbon $date
     * @return Collection|array
     */
    private function getPlatformStateCostsForDate(Carbon $date): Collection|array
    {
        return DailyAdCost::query()
            ->select([
                 DailyAdCost::FIELD_COST,
                 DailyAdCost::FIELD_LOCATION_ID
            ])
            ->where(DailyAdCost::FIELD_DATE, $date->format('Y-m-d'))
            ->where(DailyAdCost::FIELD_ADVERTISER, $this->advertiser)
            ->where(DailyAdCost::FIELD_PLATFORM, $this->platform)
            ->where(DailyAdCost::FIELD_INDUSTRY_ID, $this->industryId)
            ->get();
    }

    /**
     * @param array $dates
     * @return void
     * @throws \Exception
     */
    private function estimatePlatformCounties(array $dates): void
    {
        $this->info('Estimating county data');
        $this->bar = $this->output->createProgressBar(count($dates));
        $this->bar->start();

        if ($this->advertiser === Advertiser::GABE->value) {
            $campaignIds = $this->platform === AdvertisingPlatform::getInteger(AdvertisingPlatform::META->value) ? self::GABE_META_CAMPAIGN_IDS : self::GABE_MICROSOFT_CAMPAIGN_IDS;
        } else {
            $campaignIds = $this->platform === AdvertisingPlatform::getInteger(AdvertisingPlatform::META->value) ? self::WADE_META_CAMPAIGN_IDS : [];
        }

        $data = [];

        foreach ($dates as $date) {
            $countyCounts = $this->getLeadsDateRange($campaignIds, $date->timestamp, $date->endOfDay()->timestamp);

            $stateCosts   = $this->getPlatformStateCostsForDate($date);
            $stateIds     = $countyCounts->unique('state_id')->pluck('state_id')->toArray();

            foreach ($stateIds as $stateId) {
                $stateCountSum = $countyCounts->where('state_id', $stateId)->sum('count');

                $states = $stateCosts->where('location_id', $stateId);

                if(!$states) {
                    continue;
                }

                $stateCost = $states->sum('cost');

                $countyCounts->where('state_id', $stateId)->each(function ($county) use ($stateCountSum, $stateCost, &$data, $date, $stateId) {

                    $cost = ($county->count / $stateCountSum) * $stateCost;
                    $data[] = [
                        DailyAdCost::FIELD_LOCATION_ID => $county->county_id,
                        DailyAdCost::FIELD_COST        => $cost,
                        DailyAdCost::FIELD_DATE        => $date,
                        DailyAdCost::FIELD_INDUSTRY_ID => $this->industryId,
                        DailyAdCost::FIELD_ADVERTISER  => $this->advertiser,
                        DailyAdCost::FIELD_PLATFORM    => $this->platform
                    ];
                });
            }

            $this->bar->advance();
        }
        $this->bar->finish();
        $this->info("");

        $this->info('Storing county data');
        $this->bar = $this->output->createProgressBar($this->lineCount);
        $this->bar->start();
        $this->addDataAsTransaction($data);
    }

    /**
     * @return array
     */
    public function processMicrosoftStates(): array
    {
        $this->info('Parsing state data');
        $this->bar = $this->output->createProgressBar($this->lineCount);
        $this->bar->start();

        $header = fgetcsv($this->handle);

        $dates = Arr::map(array_splice($header, self::MICROSOFT_ADS_STATE_DATE_START_INDEX), function(string $value) {
            return Carbon::createFromFormat("Y-m-d", explode("_", $value)[0])->startOfDay();
        });

        $data = [];

        while ($csvLine = fgetcsv($this->handle)) {
            $state = Str::slug($csvLine[0]);
            $locationId = $this->locationRepository->getState($state)?->{Location::ID};

            if($locationId) {
                $costValues = array_splice($csvLine, self::MICROSOFT_ADS_STATE_DATE_START_INDEX);

                foreach($costValues as $index => $costValue) {
                    if($costValue) {
                        $data[] = [
                            DailyAdCost::FIELD_LOCATION_ID => $locationId,
                            DailyAdCost::FIELD_COST        => $costValue,
                            DailyAdCost::FIELD_DATE        => $dates[$index],
                            DailyAdCost::FIELD_INDUSTRY_ID => $this->industryId,
                            DailyAdCost::FIELD_ADVERTISER  => $this->advertiser,
                            DailyAdCost::FIELD_PLATFORM    => $this->platform
                        ];
                    }
                }
            }

            $this->bar->advance();
        }

        $this->bar->finish();
        $this->info("");

        $this->info("Storing state data");
        $this->bar = $this->output->createProgressBar(count($data));

        $this->addDataAsTransaction($data);

        return array_unique($dates);
    }

    /**
     * @return array
     */
    public function processMetaStates(): array
    {
        $this->info('Parsing state data');
        $this->bar = $this->output->createProgressBar($this->lineCount);
        $this->bar->start();

        fgetcsv($this->handle); // skip header

        $dates = [];
        $data = [];

        while ($csvLine = fgetcsv($this->handle)) {
            $state = Str::slug($csvLine[0]);

            if($state === 'unknown') {
                continue;
            } else if ($state === "washington-district-of-columbia") {
                $state = "district-of-columbia";
            }

            $date = Carbon::createFromFormat("m/d/y", $csvLine[1])->startOfDay();
            $cost = $csvLine[2];
            $dates[] = $date;

            $locationId = $this->locationRepository->getState($state)?->{Location::ID};

            if($locationId) {
                $data[] = [
                    DailyAdCost::FIELD_LOCATION_ID => $locationId,
                    DailyAdCost::FIELD_COST        => $cost,
                    DailyAdCost::FIELD_DATE        => $date,
                    DailyAdCost::FIELD_INDUSTRY_ID => $this->industryId,
                    DailyAdCost::FIELD_ADVERTISER  => $this->advertiser,
                    DailyAdCost::FIELD_PLATFORM    => $this->platform
                ];
            }

            $this->bar->advance();
        }
        $this->bar->finish();
        $this->info("");


        $this->info("Storing state data");
        $this->bar = $this->output->createProgressBar(count($data));

        $this->addDataAsTransaction($data);

        return array_unique($dates);
    }

    /**
     * @throws \Exception
     */
    public function handleMeta(): void
    {
        $dates = $this->processMetaStates();

        $this->estimatePlatformCounties($dates);
    }


    /**
     * @param array $campaignIds
     * @param string $startTimestamp
     * @param string $endTimestamp
     * @return Collection|array
     */
    public function getLeadsDateRange(array $campaignIds, string $startTimestamp, string $endTimestamp): Collection|array
    {
        $query = EloquentQuote::query()
            ->selectRaw('s.id as state_id, c.id as county_id, COUNT(*) as count')
            ->join(
                EloquentAddress::TABLE,
                EloquentAddress::TABLE.'.'.EloquentAddress::ID,
                EloquentQuote::TABLE.'.'.EloquentQuote::ADDRESS_ID
            )->join(
                Location::TABLE,
                Location::TABLE.'.'.Location::ZIP_CODE,
                EloquentAddress::TABLE.'.'.EloquentAddress::ZIP_CODE
            )->join(Location::TABLE. ' as c', function($join) {
                $join->on(
                    'c.county_key',
                    '=',
                    Location::TABLE.'.'.Location::COUNTY_KEY
                )->where('c.type', Location::TYPE_COUNTY)
                ->whereRaw('c.state_abbr = '.Location::TABLE.'.'.Location::STATE_ABBREVIATION);
            })->join(Location::TABLE. ' as s', function($join) {
                $join->on(
                    's.state_abbr',
                    '=',
                    Location::TABLE.'.'.Location::STATE_ABBREVIATION
                )->where('s.type', Location::TYPE_STATE);
            })
            ->where(EloquentQuote::TIMESTAMP_ADDED, '>=', $startTimestamp)
            ->where(EloquentQuote::TIMESTAMP_ADDED, '<=', $endTimestamp)
            ->whereIn(EloquentQuote::CAMPAIGN_ID, $campaignIds)
            ->groupBy('c.id');

        if($this->industryId === 1) {
            $query->where(EloquentQuote::TABLE.'.'.EloquentQuote::SOLAR_LEAD, true);
        } else {
            $query->where(EloquentQuote::TABLE.'.'.EloquentQuote::ROOFING_LEAD, true);
        }

        return $query->get();
    }

    /**
     * @return void
     * @throws \Exception
     */
    public function handleGoogle(): void
    {
        $location = $this->choice("Is this state or county data?", ['State', 'County']);
        $isCounty = $location === "County";

        $this->info('Parsing data');
        $this->bar = $this->output->createProgressBar($this->lineCount);
        $this->bar->start();

        fgetcsv($this->handle); // skip title
        fgetcsv($this->handle); // skip date range

        $header = fgetcsv($this->handle);

        $dateOffset = $isCounty ? self::GOOGLE_ADS_COUNTY_DATE_START_INDEX : self::GOOGLE_ADS_STATE_DATE_START_INDEX;

        $dates = Arr::map(array_splice($header, $dateOffset), function(string $value) {
            return Carbon::createFromFormat("Y-m-d", explode("_", $value)[0])->startOfDay();
        });

        $data = [];

        while ($csvLine = fgetcsv($this->handle)) {
            $state = $csvLine[1];

            if($isCounty) {
                $county = $csvLine[2];
                $locationId = $this->googleAdsGeoTargetRepository->getCountyLocationId($state, $county);
            } else {
                $locationId = $this->googleAdsGeoTargetRepository->getStateLocationId($state);
            }

            if($locationId) {
                $costValues = array_splice($csvLine, self::GOOGLE_ADS_COUNTY_DATE_START_INDEX);

                foreach($costValues as $index => $cost) {
                    if($cost !== ' --') {
                        $data[] = [
                            DailyAdCost::FIELD_LOCATION_ID => $locationId,
                            DailyAdCost::FIELD_COST        => $cost,
                            DailyAdCost::FIELD_DATE        => $dates[$index],
                            DailyAdCost::FIELD_INDUSTRY_ID => $this->industryId,
                            DailyAdCost::FIELD_ADVERTISER  => $this->advertiser,
                            DailyAdCost::FIELD_PLATFORM    => $this->platform
                        ];
                    }
                }
            }

            $this->bar->advance();
        }

        $this->bar->finish();
        $this->info("");

        $this->info("Storing data");
        $this->bar = $this->output->createProgressBar(count($data));

        $this->addDataAsTransaction($data);
    }

    /**
     * @param array $data
     * @return void
     */
    private function addDataAsTransaction(array $data): void
    {
        DB::transaction(function() use ($data) {
            foreach(array_chunk($data, 2000) as $chunkInsert) {
                DailyAdCost::query()->insert($chunkInsert);
                $this->bar->advance(2000);
            }
        });

        $this->bar->finish();
        $this->info("");
    }

    /**
     * @param $filePath
     * @return int
     */
    private function getCsvLineCount($filePath): int
    {
        $file = new \SplFileObject($filePath, 'r');
        $file->seek(PHP_INT_MAX);
        return $file->key() + 1;
    }
}
