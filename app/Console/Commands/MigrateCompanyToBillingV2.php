<?php

namespace App\Console\Commands;

use App\DTO\Billing\PaymentMethods\PaymentMethodDTO;
use App\Enums\Billing\BillingLogLevel;
use App\Enums\Billing\BillingVersion;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Enums\Billing\PaymentMethodServices;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentCompanyPaymentProfile;
use App\Models\Legacy\EloquentConfiguration;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Odin\Company;
use App\Models\Odin\ProductAssignment;
use App\Repositories\Billing\CompanyPaymentMethodRepository;
use App\Repositories\Odin\ProductAssignmentRepository;
use App\Services\Billing\BillingLogService;
use App\Services\Billing\BillingProfile\BillingProfileService;
use App\Services\DatabaseHelperService;
use App\Services\PaymentGateway\PaymentGatewayServiceFactory;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;

class MigrateCompanyToBillingV2 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'legacy-migrate:billing {--company-id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command migrates legacy stripe payment methods, threshold, uninvoiced quotes and the company to v2';

    protected Company $company;

    protected string $billingReleaseDate = '2024-10-30 03:51:52';

    const int DEFAULT_THRESHOLD = 400;

    /**
     * Create a new command instance.
     */
    public function __construct(
        protected BillingProfileService $billingProfileService,
        protected CompanyPaymentMethodRepository $companyPaymentMethodRepository,
        protected ProductAssignmentRepository $productAssignmentRepository
    )
    {
        parent::__construct();
    }

    /**
     * @return void
     */
    public function handle(): void
    {
        $companyId = $this->option('company-id');

        if (!$companyId) {
            $this->log('No specified Company Id, migrating all v1 companies');
            $companyIds = Company::query()->select(Company::TABLE . '.' . Company::FIELD_ID)
                ->join(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentCompany::TABLE, EloquentCompany::TABLE . '.' . EloquentCompany::COMPANY_ID, '=', Company::TABLE . '.' . Company::FIELD_LEGACY_ID)
                ->where(DatabaseHelperService::readOnlyDatabase() .'.'.EloquentCompany::TABLE.'.'. EloquentCompany::BILLING_VERSION, '=', BillingVersion::V1->value)
                ->get()
                ->pluck(Company::FIELD_ID)
                ->toArray();
        } else {
            $companyIds = collect(explode(',', $companyId))
                ->map(fn(string $item) => trim($item))
                ->filter()
                ->unique();
        }

        $this->log('Start migrating company ids: ' . json_encode($companyIds));

        foreach ($companyIds as $companyId) {
            try {
                $this->process($companyId);
            } catch (Exception $exception) {
                $this->log(
                    message: $exception->getMessage(),
                    level  : BillingLogLevel::ERROR,
                    context: [
                        'error' => $exception
                    ]
                );
            }
        }

        $this->log("Migration completed.");
    }

    /**
     * @param int $companyId
     * @return void
     */
    public function process(int $companyId): void
    {
        $company = Company::query()->findOrFail($companyId);

        $this->newLine();
        $this->log('Migrating ' . $company->name);

        if ($this->hasBeenMigrated($company)) {
            $this->log('Company has already been migrated.');
            return;
        }

        $this->log("Migrating billing profiles...");

        $paymentProfiles = $this->getPaymentProfiles($company->{Company::FIELD_LEGACY_ID});

        $this->log("Legacy Payment profiles: " . $paymentProfiles->count());

        $thresholdInDollars = $this->getChargeThreshold($company->{Company::FIELD_LEGACY_ID});
        $this->log("Charge threshold: $" . $thresholdInDollars);

        if ($paymentProfiles->count() > 0) {
            foreach ($paymentProfiles as $profile) {
                $this->processPaymentProfile(
                    company           : $company,
                    profile           : $profile,
                    thresholdInDollars: $thresholdInDollars
                );
            }
        } else {
            $this->billingProfileService->createProfileWithDefaultConfiguration(
                paymentMethod: PaymentMethodServices::STRIPE,
                companyId    : $companyId,
                default      : true,
            );
        }

        $this->migrateUninvoicedLeads($company);
        $this->migrateCredits($company);
        $this->migrateCompany($company);
    }

    /**
     * @param Company $company
     * @return void
     */
    protected function migrateCredits(Company $company): void
    {
        $arguments = [
            '--company-id' => $company->id,
        ];

        Artisan::call('legacy-migrate:credits', $arguments);

        $output = Artisan::output();

        $this->info($output);
    }

    /**
     * @param Company $company
     * @return void
     */
    protected function migrateCompany(Company $company): void
    {
        $this->log("Updating company billing version to v2");

        // Bypass model readonly condition
        DB::connection('readonly')->table(EloquentCompany::TABLE)
            ->where(EloquentCompany::ID, $company->legacyCompany->{EloquentCompany::ID})
            ->update([EloquentCompany::BILLING_VERSION => BillingVersion::V2->value]);
    }

    /**
     * @param Company $company
     * @return bool
     */
    protected function hasBeenMigrated(Company $company): bool
    {
        return $company->legacyCompany->{EloquentCompany::BILLING_VERSION} === BillingVersion::V2->value;
    }


    /**
     * @param int $legacyCompanyId
     * @return Collection<EloquentCompanyPaymentProfile>
     */
    protected function getPaymentProfiles(int $legacyCompanyId): Collection
    {
        return EloquentCompanyPaymentProfile::query()
            ->where(EloquentCompanyPaymentProfile::COMPANY_ID, $legacyCompanyId)
            ->get();
    }


    /**
     * @param int $legacyCompanyId
     * @return int
     */
    protected function getChargeThreshold(int $legacyCompanyId): int
    {
        return EloquentConfiguration::query()
            ->where(EloquentConfiguration::NAME, 'quote_chargeable_threshold')
            ->where(EloquentConfiguration::REL_ID, $legacyCompanyId)
            ->where(EloquentConfiguration::REL_TYPE, EloquentConfiguration::REL_TYPE_COMPANY_ID)
            ->first()?->value ?? self::DEFAULT_THRESHOLD;
    }

    /**
     * @param Company $company
     * @param EloquentCompanyPaymentProfile $profile
     * @param int $thresholdInDollars
     * @return void
     */
    protected function processPaymentProfile(
        Company $company,
        EloquentCompanyPaymentProfile $profile,
        int $thresholdInDollars
    ): void
    {
        $customerCode = $profile->{EloquentCompanyPaymentProfile::PAYMENT_PROVIDER_IDENTIFIER};
        $paymentMethods = (PaymentGatewayServiceFactory::make(PaymentMethodServices::STRIPE))
            ->getPaymentMethods($customerCode);

        $this->log("Found " . $paymentMethods->count() . " payment methods for customer: $customerCode");

        $this->billingProfileService->createProfileWithDefaultConfiguration(
            paymentMethod     : PaymentMethodServices::STRIPE,
            companyId         : $company->id,
            default           : true,
            thresholdInDollars: $thresholdInDollars,
        );

        foreach ($paymentMethods as $paymentMethod) {
            $this->migratePaymentMethod(
                paymentMethod: $paymentMethod,
                companyId    : $company->id,
                customerCode : $customerCode
            );
        }
    }

    /**
     * @param Company $company
     * @return void
     */
    protected function migrateUninvoicedLeads(Company $company): void
    {
        $productAssignments = $this->productAssignmentRepository->getChargeableUninvoicedProductAssignmentsQuery(
            company          : $company,
            excludeRejectable: false,
            billingVersion   : BillingVersion::V1,
            deliveredAfter   : Carbon::parse($this->billingReleaseDate)
        )->get();

        $totalLeadsInDollars = $productAssignments->sum(ProductAssignment::FIELD_COST);

        $this->log(
            message: 'Updating ' . $productAssignments->count() . ' uninvoiced product assignments to V2. Total in dollars: $' . $totalLeadsInDollars,
            context: [
                'product_assignment_ids' => $productAssignments->pluck(ProductAssignment::FIELD_ID),
            ]
        );

        $eloquentQuoteCompanyIds = $productAssignments->pluck(ProductAssignment::FIELD_LEGACY_ID);

        $updatedCount = EloquentQuoteCompany::query()
            ->whereIn(EloquentQuoteCompany::ID, $eloquentQuoteCompanyIds)
            ->update([
                EloquentQuoteCompany::BILLING_VERSION => BillingVersion::V2->value
            ]);

        $this->log(
            message: $updatedCount . ' uninvoiced product assignments updated to V2'
        );
    }

    /**
     * @param PaymentMethodDTO $paymentMethod
     * @param string $companyId
     * @param string $customerCode
     * @return void
     */
    protected function migratePaymentMethod(
        PaymentMethodDTO $paymentMethod,
        string $companyId,
        string $customerCode
    ): void
    {
        $this->companyPaymentMethodRepository->create(
            companyId                      : $companyId,
            type                           : PaymentMethodServices::STRIPE,
            addedByType                    : InvoiceEventAuthorTypes::SYSTEM->value,
            isDefault                      : $paymentMethod->getIsDefault(),
            paymentGatewayPaymentMethodCode: $paymentMethod->getId(),
            paymentGatewayClientCode       : $customerCode,
        );
    }

    /**
     * @param string $message
     * @param BillingLogLevel|null $level
     * @param array $context
     * @return void
     */
    protected function log(
        string $message,
        ?BillingLogLevel $level = BillingLogLevel::INFO,
        array $context = []
    ): void
    {
        $this->info($message);

        BillingLogService::log(
            message  : $message,
            level    : $level,
            namespace: 'migrate_company_to_billing_v2',
            context  : [
                ...$context,
                'company_id' => $this->option('company-id')
            ]
        );
    }
}
