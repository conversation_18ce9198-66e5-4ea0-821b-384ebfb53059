<?php

namespace App\Console\Commands;

use App\Models\Calendar\CalendarEvent;
use App\Models\Calendar\CalendarEventAttendee;
use App\Models\Calendar\Demo;
use App\Models\User;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class FixDemoHost extends Command
{
    protected $signature = 'demo:fix-host
                            {--dry-run : Preview changes without executing them}
                            {--batch-size=100 : Number of records to process at once}';

    protected $description = 'Fix demo host assignments by updating user IDs to match calendar event organizers and remove duplicates';

    private array $userEmailCache = [];
    private int $updatedDemos = 0;
    private int $deletedDemos = 0;

    /**
     * @return int
     */
    public function handle(): int
    {
        $this->info('Starting demo host fix process...');

        $isDryRun = $this->option('dry-run');
        $batchSize = (int) $this->option('batch-size');

        if ($isDryRun) {
            $this->warn('Running in DRY RUN mode - no changes will be made');
        }

        try {
            DB::beginTransaction();

            $this->fixIncorrectHosts($isDryRun, $batchSize);
            $this->removeDuplicateDemos($isDryRun);

            if (!$isDryRun) {
                DB::commit();
                $this->info("Process completed successfully!");
                $this->info("Updated demos: {$this->updatedDemos}");
                $this->info("Deleted duplicate demos: {$this->deletedDemos}");
            } else {
                DB::rollBack();
                $this->info("Dry run completed - no changes were made");
                $this->info("Would update demos: {$this->updatedDemos}");
                $this->info("Would delete duplicate demos: {$this->deletedDemos}");
            }

            return self::SUCCESS;

        } catch (Exception $e) {
            DB::rollBack();
            $this->error("Error occurred: " . $e->getMessage());
            return self::FAILURE;
        }
    }

    /**
     * @param bool $isDryRun
     * @param int $batchSize
     * @return void
     */
    private function fixIncorrectHosts(bool $isDryRun, int $batchSize): void
    {
        $this->info('Checking for demos with incorrect host assignments...');

        $incorrectHosts = $this->getIncorrectHostData();

        if ($incorrectHosts->isEmpty()) {
            $this->info('No incorrect host assignments found.');
            return;
        }

        $this->info("Found {$incorrectHosts->count()} demos with incorrect hosts");

        $progressBar = $this->output->createProgressBar($incorrectHosts->count());
        $progressBar->start();

        $incorrectHosts->chunk($batchSize)->each(function (Collection $batch) use ($isDryRun, $progressBar) {
            $this->processBatch($batch, $isDryRun, $progressBar);
        });

        $progressBar->finish();
        $this->newLine();
    }

    /**
     * @return Collection
     */
    private function getIncorrectHostData(): Collection
    {
        return CalendarEventAttendee::query()
            ->select([
                Demo::TABLE . '.' . Demo::FIELD_ID . ' as demo_id',
                Demo::TABLE . '.' . Demo::FIELD_USER_ID . ' as current_user_id',
                CalendarEventAttendee::TABLE . '.' . CalendarEventAttendee::FIELD_EMAIL . ' as organizer_email',
                User::TABLE . '.' . User::FIELD_EMAIL . ' as current_user_email',
            ])
            ->join(Demo::TABLE, function ($join) {
                $join->on(Demo::TABLE . '.' . Demo::FIELD_CALENDAR_EVENT_ID, '=',
                    CalendarEventAttendee::TABLE . '.' . CalendarEventAttendee::FIELD_CALENDAR_EVENT_ID);
            })
            ->join(User::TABLE, function ($join) {
                $join->on(User::TABLE . '.' . User::FIELD_ID, '=',
                    Demo::TABLE . '.' . Demo::FIELD_USER_ID);
            })
            ->where(CalendarEventAttendee::TABLE . '.' . CalendarEventAttendee::FIELD_IS_ORGANIZER, true)
            ->whereColumn(
                CalendarEventAttendee::TABLE . '.' . CalendarEventAttendee::FIELD_EMAIL,
                '<>',
                User::TABLE . '.' . User::FIELD_EMAIL
            )
            ->get();
    }

    /**
     * @param Collection $batch
     * @param bool $isDryRun
     * @param $progressBar
     * @return void
     */
    private function processBatch(Collection $batch, bool $isDryRun, $progressBar): void
    {
        foreach ($batch as $record) {
            $correctUserId = $this->findUserByEmail($record->organizer_email);

            if (!$correctUserId) {
                $this->newLine();
                $this->warn("No user found for organizer email: {$record->organizer_email} (Demo ID: {$record->demo_id})");
                $progressBar->advance();
                continue;
            }

            if ($correctUserId !== $record->current_user_id) {
                $this->updateDemoHost($record, $correctUserId, $isDryRun);
                $this->updatedDemos++;
            }

            $progressBar->advance();
        }
    }

    /**
     * @param object $record
     * @param int $correctUserId
     * @param bool $isDryRun
     * @return void
     */
    private function updateDemoHost(object $record, int $correctUserId, bool $isDryRun): void
    {
        $message = sprintf(
            'Demo %d: Updating user ID from %d to %d (organizer: %s)',
            $record->demo_id,
            $record->current_user_id,
            $correctUserId,
            $record->organizer_email
        );

        if ($isDryRun) {
            $this->line("Would update: $message");
            return;
        }

        Demo::query()
            ->where(Demo::FIELD_ID, $record->demo_id)
            ->update([Demo::FIELD_USER_ID => $correctUserId]);

        $this->line("Updated: $message");
    }

    /**
     * @param bool $isDryRun
     * @return void
     */
    private function removeDuplicateDemos(bool $isDryRun): void
    {
        $this->info('Checking for duplicate demos...');

        $duplicates = $this->findDuplicateDemos();

        if ($duplicates->isEmpty()) {
            $this->info('No duplicate demos found.');
            return;
        }

        $this->info("Found {$duplicates->count()} sets of duplicate demos");

        foreach ($duplicates as $duplicate) {
            $this->processDuplicateSet($duplicate, $isDryRun);
        }
    }

    /**
     * @return Collection
     */
    private function findDuplicateDemos(): Collection
    {
        return Demo::query()
            ->select([
                CalendarEvent::TABLE . '.' . CalendarEvent::FIELD_EXTERNAL_ID . ' as external_id',
                Demo::TABLE . '.' . Demo::FIELD_USER_ID . ' as user_id',
                DB::raw('COUNT(*) as count'),
                DB::raw('GROUP_CONCAT(' . Demo::TABLE . '.' . Demo::FIELD_ID . ' ORDER BY ' . Demo::TABLE . '.' . Demo::FIELD_ID . ' ASC) as demo_ids')
            ])
            ->join(CalendarEvent::TABLE, function ($join) {
                $join->on(CalendarEvent::TABLE . '.' . CalendarEvent::FIELD_ID, '=',
                    Demo::TABLE . '.' . Demo::FIELD_CALENDAR_EVENT_ID);
            })
            ->groupBy([
                CalendarEvent::TABLE . '.' . CalendarEvent::FIELD_EXTERNAL_ID,
                Demo::TABLE . '.' . Demo::FIELD_USER_ID
            ])
            ->havingRaw('COUNT(*) > 1')
            ->orderBy(Demo::TABLE . '.' . Demo::FIELD_USER_ID)
            ->orderBy(CalendarEvent::TABLE . '.' . CalendarEvent::FIELD_EXTERNAL_ID)
            ->get();
    }

    /**
     * @param object $duplicate
     * @param bool $isDryRun
     * @return void
     */
    private function processDuplicateSet(object $duplicate, bool $isDryRun): void
    {
        $demoIds = array_map('intval', explode(',', $duplicate->demo_ids));
        $keepId = array_shift($demoIds);

        if (empty($demoIds)) {
            return;
        }

        $message = sprintf(
            'Calendar %s, User %d: Keeping demo %d, deleting %s',
            $duplicate->external_id,
            $duplicate->user_id,
            $keepId,
            implode(', ', $demoIds)
        );

        if ($isDryRun) {
            $this->line("Would delete duplicates: $message");
            $this->deletedDemos += count($demoIds);
            return;
        }

        $deleted = Demo::query()
            ->whereIn(Demo::FIELD_ID, $demoIds)
            ->delete();

        $this->deletedDemos += $deleted;
        $this->line("Deleted duplicates: $message");
    }

    /**
     * @param string $email
     * @return int|null
     */
    private function findUserByEmail(string $email): ?int
    {
        if (isset($this->userEmailCache[$email])) {
            return $this->userEmailCache[$email];
        }

        $user = User::query()
            ->where(function ($query) use ($email) {
                $query->where(User::FIELD_EMAIL, $email)
                    ->orWhereJsonContains(User::FIELD_EMAIL_ALIASES, $email);
            })
            ->first();

        if (!$user) {
            $this->userEmailCache[$email] = null;
            return null;
        }

        $this->cacheUserEmails($user);

        return $user->id;
    }

    /**
     * @param User $user
     * @return void
     */
    private function cacheUserEmails(User $user): void
    {
        $allEmails = $user->getAllEmails();

        foreach ($allEmails as $email) {
            $this->userEmailCache[$email] = $user->id;
        }
    }
}
