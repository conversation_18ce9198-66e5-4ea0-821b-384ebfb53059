<?php

namespace App\Console\Commands;

use App\Models\BaseModel;
use App\Models\CompanyProfile\CompanyProfile;
use App\Services\CompanyProfile\CompanyProfilePromotionService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Throwable;

class PromoteCompanyProfiles extends Command
{
    protected $signature = 'company-profile:promote
                            {--profile_id= : Comma separated ids}';

    protected $description = 'Promote company profiles to companies or new buyer prospects';

    /**
     * @return int
     * @throws Throwable
     */
    public function handle(): int
    {
        $this->info('Starting promoting company profiles...');

        try {
            DB::beginTransaction();

            $profileIds = $this->getProfileIds();

            if (empty($profileIds)) {
                throw new Exception('Profile id is required');
            }

            $this->promoteProfiles($profileIds);

            DB::commit();

            return self::SUCCESS;

        } catch (Exception $e) {
            DB::rollBack();
            $this->error("Error occurred: " . $e->getMessage());
            return self::FAILURE;
        }
    }

    /**
     * @return array
     */
    public function getProfileIds(): array
    {
        return Str::of($this->option('profile_id'))
            ->explode(',')
            ->map(fn(string $item) => trim($item))
            ->filter()
            ->unique()
            ->values()
            ->toArray();
    }

    /**
     * @param array $profileIds
     * @return void
     * @throws Throwable
     */
    public function promoteProfiles(array $profileIds): void
    {
        /** @var CompanyProfilePromotionService $companyProfilePromotionService */
        $companyProfilePromotionService = app(CompanyProfilePromotionService::class);

        foreach ($profileIds as $profileId) {
            try {
                $profile = CompanyProfile::query()->findOrFail($profileId);
                $promotions = $companyProfilePromotionService->promote($profile);

                $this->info("Profile id $profileId promoted to:");
                $promotions->map(fn(BaseModel $model) => $this->info($model::class . ":" . $model->id));
            } catch (Exception $exception) {
                $this->error($exception);
            }
        }
    }
}
