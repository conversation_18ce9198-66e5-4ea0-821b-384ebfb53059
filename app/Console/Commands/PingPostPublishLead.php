<?php

namespace App\Console\Commands;

use App\Enums\PingPostVariableEnum;
use App\Jobs\PingPostPublishJob;
use App\Services\Odin\PingPostPublishing\PingPostPublishService;
use Illuminate\Console\Command;
use <PERSON>ymfony\Component\Console\Input\InputOption;

class PingPostPublishLead extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:ping-post-publish
        { consumer_product_ids : Comma separated list of consumer product ids to ping post }
        { --check-validity : Check validity of consumer product id }
        { --queue : Pass this flag to run the ping post job on the allocation queue. Otherwise the job will be dispatch synced to this command. }'; // The rest of the options are defined dynamically from PingPostVariableEnum meta-data

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to run ping post automation for given consumer product ids. Automation parameters defined in Global Configurations Management with key \'ping_post\'';

    public function __construct()
    {
        parent::__construct();

        // Dynamically modify the signature to add options from upsell automation variable enum
        $this->setDynamicOptions();
    }

    /**
     * @return void
     */
    protected function setDynamicOptions(): void
    {
        // Add automation parameters as command line options
        foreach (PingPostVariableEnum::cases() as $variable) {
            $metaData = $variable->getMetaData();
            if ($metaData[PingPostVariableEnum::KEY_ARG] ?? null) {
                $this->addOption(
                    $metaData[PingPostVariableEnum::KEY_ARG],
                    null,
                    InputOption::VALUE_REQUIRED,
                    $metaData[PingPostVariableEnum::KEY_DESC]
                );
            }
        }
    }

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $consumerProductIds = array_map('intval', explode(',', $this->argument('consumer_product_ids')));

        // Get config parameters from command line
        $config = [
            PingPostVariableEnum::ENABLED->value => true, // Hard coded is enabled - command will always work whether automation is enabled or not in the config
        ];

        // Get input variables
        foreach (PingPostVariableEnum::cases() as $variable) {
            // Get meta data for each variable
            $metaData = $variable->getMetaData();

            // If the KEY_ARG is defined then this variable can be passed as a command line argument
            if ($metaData[PingPostVariableEnum::KEY_ARG] ?? null) {
                // Check command line for argument
                $option = $this->option($metaData[PingPostVariableEnum::KEY_ARG]);

                // Set argument in config if given
                if ($option)
                    $config[$variable->value] = $variable->castToType($option);
            }
        }

        $this->info("Consumer Product Ids: ".json_encode($consumerProductIds));
        $this->info("Config: ".json_encode($config));

        foreach ($consumerProductIds as $consumerProductId) {
            if ($this->option("check-validity")) {
                $service = app(PingPostPublishService::class);
                $test = $service->checkPingPostValidity($consumerProductId);
                print "Validity: ";
                if ($test) {
                    print "True\n";
                } else {
                    print "False\n";
                }
            }

            if ($this->option('queue')) {
                PingPostPublishJob::dispatch($consumerProductId, $config);
            } else {
                PingPostPublishJob::dispatchSync($consumerProductId, $config);
            }
        }
    }
}
