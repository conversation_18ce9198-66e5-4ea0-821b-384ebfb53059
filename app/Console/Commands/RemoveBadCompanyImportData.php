<?php

namespace App\Console\Commands;

use App\Models\CompanyDiscovery\AddressDiscoveryStatus;
use App\Models\Odin\CompanyLocation;
use Illuminate\Console\Command;

class RemoveBadCompanyImportData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clean-up:bad-company-import-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cleans up bad company import data. Returns legacy ids of deleted companies';

    /** @var string discoveryReference */
    protected string $discoveryReference;

    /** @var array $companyLegacyIds */
    protected array $companyLegacyIds = [];

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $this->discoveryReference = $this->choice('Which import are you looking to clean up?', $this->getUnfinishedImportIds());

        $addresses = AddressDiscoveryStatus::query()
            ->where(AddressDiscoveryStatus::FIELD_DISCOVERY_REFERENCE, $this->discoveryReference)
            ->where(AddressDiscoveryStatus::FIELD_CHECKED, 1)
            ->get();

        $bar = $this->output->createProgressBar($addresses->count());
        $bar->start();

        /** @var AddressDiscoveryStatus $addressDiscoveryStatus */
        foreach ($addresses as $addressDiscoveryStatus) {

            /** @var CompanyLocation $companyLocation */
            $companyLocation = CompanyLocation::query()
                ->with([
                    CompanyLocation::RELATION_EXTERNAL_REVIEWS,
                    CompanyLocation::RELATION_COMPANY
                ])
                ->where(CompanyLocation::FIELD_ADDRESS_ID, $addressDiscoveryStatus->{AddressDiscoveryStatus::FIELD_ADDRESS_ID})
                ->first();

            if($companyLocation) {
                $company = $companyLocation->company;

                $companyLocation->externalReviews()->delete();
                $companyLocation->delete();

                if($company->locations->count() === 0) {

                    if($company->legacy_id) {
                        $this->companyLegacyIds[] = $company->legacy_id;
                    }

                    $company->data()->delete();
                    $company->delete();
                }
            }

            $addressDiscoveryStatus->checked = false;
            $addressDiscoveryStatus->save();

            $bar->advance();
        }

        $ids = join(',',$this->companyLegacyIds);
        $this->info("Legacy IDs of deleted Companies: $ids");
    }

    /**
     * @return array
     */
    private function getUnfinishedImportIds(): array
    {
        return AddressDiscoveryStatus::query()
            ->where(AddressDiscoveryStatus::FIELD_CHECKED, '<>', true)
            ->groupBy(AddressDiscoveryStatus::FIELD_DISCOVERY_REFERENCE)
            ->get()
            ->pluck(AddressDiscoveryStatus::FIELD_DISCOVERY_REFERENCE)
            ->toArray();
    }
}
