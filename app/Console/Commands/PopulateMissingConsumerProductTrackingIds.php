<?php

namespace App\Console\Commands;

use App\Enums\Odin\Product as ProductEnum;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\LeadTrackingUrl;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductTracking;
use App\Models\Odin\Product;
use App\Models\Odin\ServiceProduct;
use App\Models\Odin\Website;
use App\Services\DatabaseHelperService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class PopulateMissingConsumerProductTrackingIds extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:populate-missing-consumer-product-tracking-ids';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Attempt to populate missing consumer product tracking IDs from legacy';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info("\nPopulating missing consumer product tracking IDs\n");

        $now = Carbon::now('UTC')->format('Y-m-d H:i:s');

        $leadServiceProductIds = ServiceProduct::query()
            ->whereHas(ServiceProduct::RELATION_PRODUCT, function($has) {
                $has->where(Product::TABLE.'.'.Product::FIELD_NAME, ProductEnum::LEAD->value);
            })
            ->pluck(ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_ID)
            ->toArray();

        $websites = Website::query()
            ->pluck(Website::FIELD_ID, Website::FIELD_ABBREVIATION)
            ->toArray();

        $progress = 0;

        ConsumerProduct::query()
            ->join(DatabaseHelperService::database().'.'.Consumer::TABLE, function($join) {
                $join->on(
                    DatabaseHelperService::database().'.'.Consumer::TABLE.'.'.Consumer::FIELD_ID,
                    '=',
                    DatabaseHelperService::database().'.'.ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CONSUMER_ID
                );
            })
            ->join(DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuote::TABLE, function($join) {
                $join->on(
                    DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuote::TABLE.'.'.EloquentQuote::QUOTE_ID,
                    '=',
                    DatabaseHelperService::database().'.'.Consumer::TABLE.'.'.Consumer::FIELD_LEGACY_ID
                );
            })
            ->join(DatabaseHelperService::readOnlyDatabase().'.'.LeadTrackingUrl::TABLE, function($join) {
                $join->on(
                    DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuote::TABLE.'.'.EloquentQuote::QUOTE_ID,
                    '=',
                    DatabaseHelperService::readOnlyDatabase().'.'.LeadTrackingUrl::TABLE.'.'.LeadTrackingUrl::LEAD_ID
                );
            })
            ->whereNull(ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID)
            ->selectRaw(implode(',', [
                Consumer::TABLE.'.'.Consumer::FIELD_ID,
                Consumer::TABLE.'.'.Consumer::FIELD_LEGACY_ID,
                EloquentQuote::TABLE.'.'.EloquentQuote::QUOTE_ID,
                EloquentQuote::TABLE.'.'.EloquentQuote::TRACK_CODE,
                EloquentQuote::TABLE.'.'.EloquentQuote::TRACK_NAME,
                "LOWER(".EloquentQuote::TABLE.'.'.EloquentQuote::ORIGIN.") AS ".EloquentQuote::ORIGIN,
                LeadTrackingUrl::TABLE.'.'.LeadTrackingUrl::URL_START,
                LeadTrackingUrl::TABLE.'.'.LeadTrackingUrl::URL_CONVERT,
                LeadTrackingUrl::TABLE.'.'.LeadTrackingUrl::SOURCE
            ]))
            ->distinct()
            ->chunk(1000, function($rows) use ($now, $leadServiceProductIds, $websites, &$progress) {
                DB::transaction(function() use ($rows, $now, $leadServiceProductIds, $websites, &$progress) {
                    $keyedConsumers = $rows->keyBy(Consumer::FIELD_ID);

                    $cptQuery = ConsumerProductTracking::query()->select([
                        ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::ID,
                        ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::AD_TRACK_CODE,
                        ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::AD_TRACK_TYPE
                    ]);

                    $cptInsertRows = [];
                    foreach($rows as $row) {
                        $cptInsertRows[] = [
                            ConsumerProductTracking::AD_TRACK_TYPE => $row->{EloquentQuote::TRACK_NAME},
                            ConsumerProductTracking::AD_TRACK_CODE => $row->{EloquentQuote::TRACK_CODE},
                            ConsumerProductTracking::WEBSITE_ID => $websites[$row->{EloquentQuote::ORIGIN}] ?? 0,
                            ConsumerProductTracking::URL_START => $row->{LeadTrackingUrl::URL_START},
                            ConsumerProductTracking::URL_CONVERT => $row->{LeadTrackingUrl::URL_CONVERT},
                            ConsumerProductTracking::CALCULATOR_SOURCE => $row->{LeadTrackingUrl::SOURCE}
                        ];

                        $cptQuery->orWhere(function($where) use ($row) {
                            $where
                                ->where(ConsumerProductTracking::AD_TRACK_CODE, $row->{EloquentQuote::TRACK_CODE})
                                ->where(ConsumerProductTracking::AD_TRACK_TYPE, $row->{EloquentQuote::TRACK_NAME});
                        });
                    }

                    ConsumerProductTracking::query()->insert($cptInsertRows);

                    $consumerProductTrackingRows = [];

                    $cptQuery
                        ->get()
                        ->each(function($cpt) use (&$consumerProductTrackingRows) {
                            if(empty($consumerProductTrackingRows[$cpt->{ConsumerProductTracking::AD_TRACK_TYPE}])) {
                                $consumerProductTrackingRows[$cpt->{ConsumerProductTracking::AD_TRACK_TYPE}] = [];
                            }

                            $consumerProductTrackingRows[$cpt->{ConsumerProductTracking::AD_TRACK_TYPE}][$cpt->{ConsumerProductTracking::AD_TRACK_CODE}] = $cpt->id;
                        });

                    $consumerIds = $keyedConsumers->keys()->toArray();

                    $updates = [];
                    foreach($consumerIds as $consumerId) {
                        $trackName = $keyedConsumers->get($consumerId)?->{EloquentQuote::TRACK_NAME};
                        $trackCode = $keyedConsumers->get($consumerId)?->{EloquentQuote::TRACK_CODE};

                        if(empty($trackName)
                        || empty($trackCode)) {
                            continue;
                        }

                        $cptId = $consumerProductTrackingRows[$trackName][$trackCode] ?? 0;

                        if($cptId) {
                            $updates[$consumerId] = sprintf(
                                "WHEN %s THEN %s",
                                $consumerId,
                                $cptId
                            );
                        }
                    }

                    if(!empty($updates)) {
                        $caseQuery = sprintf(
                            "(CASE %s %s END)",
                            ConsumerProduct::FIELD_CONSUMER_ID,
                            implode(' ', array_values($updates))
                        );

                        ConsumerProduct::query()
                            ->whereIntegerInRaw(ConsumerProduct::FIELD_CONSUMER_ID, array_keys($updates))
                            ->whereIntegerInRaw(ConsumerProduct::FIELD_SERVICE_PRODUCT_ID, $leadServiceProductIds)
                            ->update([
                                ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID => DB::raw($caseQuery),
                                ConsumerProduct::FIELD_UPDATED_AT => $now
                            ]);
                    }

                    $progress += count($rows);

                    $this->info("Processed: {$progress}\r");
                });
            });

        $this->info("\nFinished\n");
    }
}
