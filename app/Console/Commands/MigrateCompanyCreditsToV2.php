<?php

namespace App\Console\Commands;

use App\DTO\Billing\Credit\ConcreteCompanyCredit;
use App\Enums\Billing\BillingLogLevel;
use App\Enums\Billing\CreditType;
use App\Helpers\CarbonHelper;
use App\Models\Billing\Credit;
use App\Models\Odin\Company;
use App\Repositories\Legacy\CompanyCreditRepository;
use App\Repositories\Legacy\InvoicesRepository;
use App\Services\Billing\BillingLogService;
use App\Services\Billing\CreditService;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Number;
use Illuminate\Support\Str;

class MigrateCompanyCreditsToV2 extends Command
{

    public function __construct(
        protected InvoicesRepository $legacyInvoiceRepository,
        protected CreditService $creditService,
        protected CompanyCreditRepository $legacyCompanyCreditRepository
    )
    {
        parent::__construct();
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'legacy-migrate:credits {--company-id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command migrates legacy credits to v2';

    /**
     * @return void
     * @throws Exception
     */
    public function handle(): void
    {
        try {
            $companies = $this->getCompanies();

            if ($companies->isEmpty()) {
                throw new Exception('No companies found for the given ids.');
            }

            foreach ($companies as $company) {
                $this->log(
                    message: 'Migrating credits applied to ' . $company->name,
                    company: $company
                );
                $this->migrateCompanyCredits($company);
            }
        } catch (Exception $exception) {
            $this->log(
                message: $exception->getMessage(),
                level  : BillingLogLevel::ERROR,
                context: [
                    'options' => $this->getOptions()
                ]
            );
        }
    }

    /**
     * @throws Exception
     */
    public function migrateCompanyCredits(Company $company): void
    {
        /** @var Collection<ConcreteCompanyCredit> $availableCredits */
        $availableCredits = $this->legacyCompanyCreditRepository->getIssuedCreditsWithRemainingBalance(
            legacyCompanyId: $company->{Company::FIELD_LEGACY_ID}
        );

        if ($availableCredits->isEmpty()) {
            $this->log(
                message: 'No credits available found for ' . $company->name,
                company: $company
            );

            return;
        }

        $creditsTotals = $availableCredits
            ->groupBy(fn(ConcreteCompanyCredit $item) => $item->getCreditType())
            ->map(function (Collection $items, string $type) {
                $creditTotal = $items->sum(fn(ConcreteCompanyCredit $item) => $item->getRemainingAmountInDollars());
                return "$type " . Number::currency($creditTotal);
            });

        $this->log(
            message: "Available credits found for $company->name: " . $creditsTotals->join(', '),
            context: [
                'available_credits' => $availableCredits
            ],
            company: $company
        );

        foreach ($availableCredits as $credit) {
            $this->processCredit(
                company: $company,
                credit : $credit
            );
        }

        $this->log(
            message: "Credits migration finished for $company->name",
            context: [
                'available_credits' => $availableCredits
            ],
            company: $company
        );
    }


    /**
     * @param Company $company
     * @param ConcreteCompanyCredit $credit
     * @return void
     */
    public function processCredit(Company $company, ConcreteCompanyCredit $credit): void
    {
        try {
            $expireDate = $credit->getCreditType() !== 'credit'
                ? $credit->getDateApplied()->copy()->addYear()
                : null;

            $this->addCreditToCompanyInV2(
                company             : $company,
                atomicInitialValue  : $credit->getAtomicInitialAmount(),
                atomicRemainingValue: $credit->getAtomicRemainingAmount(),
                creditTypeSlug      : $credit->getCreditType(),
                appliedAt           : $credit->getDateApplied(),
                expiresAt           : $expireDate,
                notes               : $credit->getNotes(),
            );

            $this->log(
                message: "Added " . Number::currency($credit->getRemainingAmountInDollars())
                . ' of ' . $credit->getCreditType() . " to $company->name in v2 with expire date of " . (empty($expireDate) ? '(no expire date)' : $expireDate),
                context: [
                    'credit_data' => $credit->toArray(),
                ],
                company: $company
            );

            $this->deductCreditFromV1(
                company        : $company,
                amountInDollars: $credit->getRemainingAmountInDollars(),
                creditTypeSlug : $credit->getCreditType(),
            );

            $this->log(
                message: "Deducted " . Number::currency($credit->getRemainingAmountInDollars())
                . ' of ' . $credit->getCreditType() . " from $company->name in v1",
                context: [
                    'credit_data' => $credit->toArray(),
                ],
                company: $company
            );
        } catch (Exception $exception) {
            $this->log(
                message: $exception->getMessage(),
                level  : BillingLogLevel::ERROR,
                context: [
                    'error'  => $exception,
                    'credit' => $credit
                ],
                company: $company
            );
        }
    }

    /**
     * @return Collection<Company>
     * @throws Exception
     */
    public function getCompanies(): Collection
    {
        $companyIds = collect(explode(',', $this->option('company-id')))
            ->map(fn(string $item) => trim($item))
            ->filter()
            ->unique();

        if ($companyIds->isEmpty()) {
            throw new Exception('The company-id option is required. Eg: 1,2,3');
        }

        return Company::query()
            ->whereIn(Company::FIELD_ID, $companyIds)
            ->get();
    }

    /**
     * @param Company $company
     * @param float $amountInDollars
     * @param string $creditTypeSlug
     * @return void
     */
    public function deductCreditFromV1(
        Company $company,
        float $amountInDollars,
        string $creditTypeSlug
    ): void
    {
        $now = CarbonHelper::now()->toFormat();
        $creditNotes = "(Migrated to v2 on $now)";

        $this->legacyCompanyCreditRepository->deductCreditFromV1(
            legacyCompanyId: $company->{Company::FIELD_LEGACY_ID},
            amountInDollars: $amountInDollars,
            creditTypeSlug : $creditTypeSlug,
            comment        : $creditNotes,
        );
    }

    /**
     * @param Company $company
     * @param int $atomicInitialValue
     * @param int $atomicRemainingValue
     * @param string $creditTypeSlug
     * @param Carbon $appliedAt
     * @param Carbon|null $expiresAt
     * @param string|null $notes
     * @return void
     */
    public function addCreditToCompanyInV2(
        Company $company,
        int $atomicInitialValue,
        int $atomicRemainingValue,
        string $creditTypeSlug,
        Carbon $appliedAt,
        ?Carbon $expiresAt = null,
        ?string $notes = null
    ): void
    {
        $creditTypeSlug = match ($creditTypeSlug) {
            'signup_bonus' => CreditType::SIGNUP_BONUS->value, // in admin is with dash not underscore
            default        => $creditTypeSlug
        };

        $now = CarbonHelper::now()->toFormat();
        $creditNotes = "(Migrated from v1 on $now) " . $notes;

        $credit = new Credit();

        $credit->fill([
            Credit::FIELD_UUID            => Str::uuid()->toString(),
            Credit::FIELD_COMPANY_ID      => $company->{Company::FIELD_ID},
            Credit::FIELD_CREDIT_TYPE     => $creditTypeSlug,
            Credit::FIELD_INITIAL_VALUE   => $atomicInitialValue,
            Credit::FIELD_REMAINING_VALUE => $atomicRemainingValue,
            Credit::FIELD_EXPIRES_AT      => $expiresAt,
            Credit::FIELD_NOTES           => $creditNotes,
            Credit::CREATED_AT            => $appliedAt,
            Credit::UPDATED_AT            => $appliedAt,
        ]);

        $credit->save();
    }

    /**
     * @param string $message
     * @param BillingLogLevel|null $level
     * @param array $context
     * @param Company|null $company
     * @return void
     */
    protected function log(
        string $message,
        ?BillingLogLevel $level = BillingLogLevel::INFO,
        array $context = [],
        ?Company $company = null
    ): void
    {
        $this->info($message);

        BillingLogService::log(
            message    : $message,
            level      : $level,
            namespace  : 'migrate_credits_to_billing_v2',
            relatedType: $company ? Company::class : null,
            relatedId  : $company?->id,
            context    : [
                ...$context,
                'company_id' => $this->option('company-id')
            ]
        );
    }
}
