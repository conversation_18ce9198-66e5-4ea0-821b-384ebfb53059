<?php

namespace App\Console\Commands;

use App\Enums\GlobalConfigurationKey;
use App\Enums\GlobalConfigurationMailchimpField;
use App\Repositories\GlobalConfiguration\GlobalConfigurationRepository;
use GuzzleHttp\Exception\ConnectException;
use Illuminate\Console\Command;
use MailchimpMarketing\ApiClient;

class InitialiseEmailMarketing extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'marketing-campaigns:initialise-email-marketing';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run to initialise the required variables for Email Marketing to work';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $emailImplementation = $this->choice('What email marketing would you like to implement?', ['mailchimp'], 0);

        match ($emailImplementation) {
            'mailchimp' => $this->mailChimpImplementation(),
        };

        $this->info("Email Marketing Initialised.");
    }

    public function mailChimpImplementation(): void
    {
        $apiKey = config('services.marketing.email.mailchimp.api_key');
        $server = config('services.marketing.email.mailchimp.server');

        $mailchimpApi = new ApiClient();
        $mailchimpApi->setConfig([
            'apiKey' => $apiKey,
            'server' => $server,
        ]);

        try {
            $mailchimpApi->ping->get();
        } catch (ConnectException $exception) {
            $this->info("Failed to ping mailchimp. check your mailchimp config.");
            return;
        }

        /** @var GlobalConfigurationRepository $globalConfigRepository */
        $globalConfigRepository = app()->make(GlobalConfigurationRepository::class);

        $globalConfigPayload = $globalConfigRepository->getConfigurationPayload(GlobalConfigurationKey::MAILCHIMP)?->toArray()['data'];

        if (!empty($globalConfigPayload)) {
            $this->info("A Global config payload for mailchimp already exists.");
            return;
        }

        $this->info(
            "You will now need to enter some fields required for generating emails. "
            . "these fields are stored in the global config table and can be managed from there."
        );

        $listId = json_decode(json_encode($mailchimpApi->lists->getAllLists()), true)['lists'][0]['id'];

        $this->info("Creating Revalidate Link Merge Field");

        $mergeField = json_decode(json_encode($mailchimpApi->lists->addListMergeField(
            $listId,
            [
                "name" => 'REVALIDATELINK',
                "type" => 'url',
            ]
        )), true);

        $revalidatedTag = $mergeField['tag'];

        $globalConfigRepository->createConfiguration(
            GlobalConfigurationKey::MAILCHIMP->value,
            ['data' => [
                GlobalConfigurationMailchimpField::AUDIENCE_ID->value              => $listId,
                GlobalConfigurationMailchimpField::MERGE_FIELD_CALLBACK_URL->value => $revalidatedTag,
            ]]
        );
    }
}
