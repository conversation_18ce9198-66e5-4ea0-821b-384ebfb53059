<?php

namespace App\Console\Commands\Affiliate;

use App\Enums\Affiliate\PayoutStrategyTypeEnum;
use App\Jobs\Affiliate\GenerateInitialAffiliatePayouts;
use App\Models\Affiliates\Affiliate;
use App\Models\Affiliates\PayoutStrategy;
use Illuminate\Console\Command;

class RecalculateAffiliatePayouts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'affiliates:recalculate-payouts {affiliate-ids?* : Space separated list of Affiliate ids, if none given will be prompted to execute update for all affiliates}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $affiliates = $this->argument('affiliate-ids');

        if (empty($affiliates)) {
            $choice = $this->choice('do you want to recalculate payouts for all affiliates?', ['yes', 'no']);
            if($choice === 'yes') {
                $affiliates = Affiliate::query()->pluck(Affiliate::FIELD_ID);
            } else {
                return;
            }
        }

        foreach ($affiliates as $affiliateId) {
            $this->info('dispatching job to update all payouts for affiliate id: ' . $affiliateId);
            GenerateInitialAffiliatePayouts::dispatch($affiliateId);
        }
    }
}
