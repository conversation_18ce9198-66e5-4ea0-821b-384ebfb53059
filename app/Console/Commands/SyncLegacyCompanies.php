<?php

namespace App\Console\Commands;

use App\Jobs\LegacyMigrations\MigrateLegacyCompaniesJob;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Services\Odin\CompanySyncServices;
use Illuminate\Console\Command;

class SyncLegacyCompanies extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:legacy-companies';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync missing legacy companies to admin2';

    /**
     * Execute the console command.
     *
     * @param CompanySyncServices $companySyncServices
     *
     * @return void
     */
    public function handle(CompanySyncServices $companySyncServices): void
    {
        $companies = $this->ask('Company Ids to sync (comma seperated or press enter to sync all)');

        if (!$companies) {
            $this->info('Syncing all companies is under development');
            return;
        }

        $companies = explode(',', $companies);

        $industries = Industry::all()->pluck(Industry::FIELD_ID, Industry::FIELD_NAME)->toArray();
        $industryServices = IndustryService::all()->pluck(IndustryService::FIELD_ID, IndustryService::FIELD_SLUG)->toArray();

        foreach ($companies as $company) {
            $this->info("Syncing company: {$company}");
            $status = $companySyncServices->syncLegacyCompany(trim($company), $industries, $industryServices);

            if ($status !== true) $this->alert($status);
        }

        $this->info('Finished.');
    }
}
