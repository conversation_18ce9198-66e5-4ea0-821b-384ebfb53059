<?php

namespace App\Console\Commands;

use App\Models\LeadProcessingUnderReview;
use App\Models\LeadProcessor;
use App\Models\User;
use App\Services\DatabaseHelperService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class InsertRecordsFromOldUnderReviewTable extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'carry-over-old-under-review-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Brings over missing data from old under review table';

    const READ_ONLY_DB_CONNECTION = 'readonly';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        ini_set('memory_limit','1024M');
        $underReviewLeads = DB::connection(self::READ_ONLY_DB_CONNECTION)
            ->table('lead_processing_under_reviews')
            ->select([
                'lead_processing_under_reviews.lead_id',
                'lead_processing_under_reviews.reason',
                'lead_processing_under_reviews.created_at',
                'lead_processing_under_reviews.updated_at',
                'lead_processing_under_reviews.existing_reason',
                'lead_processors.user_id',
                'locations.id AS location_id',
            ])
            ->join('lead_processors',
                'lead_processors.id', '=', 'lead_processing_under_reviews.lead_processor_id' )
            ->join('tblquote',
                'tblquote.quoteid', '=', 'lead_processing_under_reviews.lead_id' )
            ->join('tbladdress',
                'tbladdress.addressid', '=', 'tblquote.addressid' )
            ->join('locations', function($join) {
                $join->on('locations.zip_code', '=', 'tbladdress.zipcode')
                    ->where('locations.type', '=', 'zi');
            })
            ->get();

        $legacyUserIds = $underReviewLeads->pluck('user_id')->unique();

        $data = [];

        foreach($legacyUserIds as $legacyUserId) {
            /** @var User $user */
            $user = User::query()->where(User::FIELD_LEGACY_USER_ID, $legacyUserId)->first();

            if($user && $user->leadProcessor !== null) {
                foreach($underReviewLeads as $underReviewLead) {
                    if($underReviewLead->user_id === $legacyUserId) {
                        $data[] = [
                            'lead_id'           => $underReviewLead->lead_id,
                            'reason'            => $underReviewLead->reason,
                            'created_at'        => $underReviewLead->created_at,
                            'updated_at'        => $underReviewLead->updated_at,
                            'existing_reason'   => $underReviewLead->existing_reason,
                            'lead_processor_id' => $user->leadProcessor?->id,
                            'location_id'       => $underReviewLead->location_id
                        ];
                    }
                }
            }
        }

        foreach(collect($data)->chunk(500) as $result) {
            LeadProcessingUnderReview::query()->insertOrIgnore(
                $result
            );
        }
    }
}
