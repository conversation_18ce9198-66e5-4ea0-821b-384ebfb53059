<?php

namespace App\Console\Commands;

use App\Jobs\Mailbox\ImportCompanyEmailsJob;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class ImportCompanyEmails extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import-company-emails {--company-id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import company emails.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): void
    {
        $companyIds = $this->getCompanyIds();

        if ($companyIds->isEmpty()) {
            $this->info("No company ids provided");
            return;
        }

        foreach ($companyIds as $companyId) {
            ImportCompanyEmailsJob::dispatch($companyId);
        }
    }

    public function getCompanyIds(): Collection
    {
        $companyId = $this->option('company-id');

        if (filled($companyId)) {
            return collect(explode(',', $companyId))
                ->map(fn(string $item) => trim($item))
                ->filter()
                ->unique();
        }

        return collect();
    }
}
