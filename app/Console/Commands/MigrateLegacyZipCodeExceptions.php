<?php

namespace App\Console\Commands;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyZipCodeException;
use App\Services\DatabaseHelperService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use <PERSON><PERSON>\Prompts\Progress;
use Throwable;
use function Laravel\Prompts\clear;
use function Laravel\Prompts\progress;

class MigrateLegacyZipCodeExceptions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'legacy-migrate:zip-code-exceptions {--chunk-size=5000 : The number of records per batch}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate zip code exceptions for companies';

    private int $chunkSize = 5000;
    private Progress $progressBar;

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->chunkSize = (int) $this->option('chunk-size') ?: 5000;
        $count = DB::table(DatabaseHelperService::readOnlyDatabase() .'.campaign_zip_code_level_county_exceptions')
            ->count();

        if (!$count) {
            $this->line("Target table is empty.");

            return Command::FAILURE;
        }

        clear();
        $chunks = ceil($count / $this->chunkSize);
        $this->info("Processing $count records in $chunks chunks...\n");
        $this->progressBar = progress("Processing chunky chunkity chunks", $chunks);

        DB::beginTransaction();
        try {
            if ($this->migrateExceptions()) {
                DB::commit();

                return Command::SUCCESS;
            }
            throw new Exception("Migration failed");
        }
        catch(Throwable $e) {
            DB::rollBack();
            $this->error($e->getMessage());
        }

        return Command::FAILURE;
    }

    private function migrateExceptions(): bool
    {
        DB::table(DatabaseHelperService::readOnlyDatabase() .'.campaign_zip_code_level_county_exceptions')
            ->select([
                DB::raw('campaign_zip_code_level_county_exceptions.*'),
                DB::raw(Company::TABLE .'.'. Company::FIELD_ID . ' as odin_company_id'),
            ])->join(Company::TABLE, Company::TABLE .'.'. Company::FIELD_LEGACY_ID, 'campaign_zip_code_level_county_exceptions.'. CompanyZipCodeException::FIELD_COMPANY_ID)
            ->orderBy('campaign_zip_code_level_county_exceptions.'. CompanyZipCodeException::FIELD_ID)
            ->chunkById($this->chunkSize, function(Collection $collection) {
                $insertData = $collection->reduce(function (array $output, $exception) {
                    $output[] = $this->transformException($exception);
                    return $output;
                }, []);

                if (DB::table(CompanyZipCodeException::TABLE)->insert($insertData))
                    $this->progressBar->advance();
                else
                    throw new Exception("Failed to insert records");
            }, 'campaign_zip_code_level_county_exceptions.'. CompanyZipCodeException::FIELD_ID, CompanyZipCodeException::FIELD_ID);

        $this->progressBar->finish();

        return true;
    }

    private function transformException($exception): array
    {
        return [
            CompanyZipCodeException::FIELD_COMPANY_ID         => $exception->odin_company_id,
            CompanyZipCodeException::FIELD_COUNTY_LOCATION_ID => $exception->county_location_id,
        ];
    }
}
