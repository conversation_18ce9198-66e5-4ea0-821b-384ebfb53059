<?php

namespace App\Console\Commands;

use App\Enums\CompanyMetrics\CompanyMetricRequestTypes;
use App\Enums\CompanyMetrics\CompanyMetricSources;
use App\Models\CompanyMetric;
use App\Models\Odin\Company;
use App\Services\ShovelsApiService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class ImportCompanyPermitMetrics extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:import-company-permit-metrics
                            {api_key : Shovels API key for authentication}
                            {company_ids?* : Comma-separated list of company IDs (optional if using --random)}
                            {--random : Select 10 random companies instead of specific IDs}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import permit metrics for companies from Shovels API for the last month. Use --random to select 10 random companies or provide specific company IDs.';


    protected ShovelsApiService $shovelsApiService;

    public function __construct(ShovelsApiService $shovelsApiService)
    {
        parent::__construct();
        $this->shovelsApiService = $shovelsApiService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $apiKey = $this->argument('api_key');
        $useRandom = $this->option('random');

        // Determine which companies to process
        if ($useRandom) {
            $this->info('Selecting 10 random companies...');
            $companyIds = $this->getRandomCompanyIds();
        } else {
            $companyIdsArray = $this->argument('company_ids');

            if (empty($companyIdsArray)) {
                $this->error('Either provide company_ids or use the --random option.');
                return self::FAILURE;
            }

            // Handle both single string with commas and array of IDs
            if (count($companyIdsArray) === 1 && str_contains($companyIdsArray[0], ',')) {
                // Single argument with comma-separated values
                $companyIds = collect(explode(',', $companyIdsArray[0]))
                    ->map(fn($id) => trim($id))
                    ->filter()
                    ->map(fn($id) => (int) $id)
                    ->unique()
                    ->values();
            } else {
                // Multiple arguments or single ID
                $companyIds = collect($companyIdsArray)
                    ->map(fn($id) => (int) trim($id))
                    ->filter()
                    ->unique()
                    ->values();
            }
        }

        if ($companyIds->isEmpty()) {
            $this->error('No valid companies found to process.');
            return self::FAILURE;
        }

        $this->info("Processing {$companyIds->count()} companies...");

        // Set API key for the service
        $this->shovelsApiService->setApiKey($apiKey);

        // Test API connection first
        $this->info('Testing API connection...');
        try {
            $testResponse = $this->shovelsApiService->testApiConnection();
            $this->info('✓ API connection successful');
            $this->info('Test response: ' . json_encode($testResponse, JSON_PRETTY_PRINT));
        } catch (\Exception $e) {
            $this->error('✗ API connection failed: ' . $e->getMessage());
            return self::FAILURE;
        }

        // Calculate date range for last month
        $endDate = Carbon::now();
        $startDate = Carbon::now()->subMonth();

        $successCount = 0;
        $errorCount = 0;

        foreach ($companyIds as $companyId) {
            $this->info("=== Processing Company ID: {$companyId} ===");

            try {
                $company = Company::find($companyId);

                if (!$company) {
                    $this->warn("Company with ID {$companyId} not found. Skipping...");
                    $errorCount++;
                    continue;
                }

                $this->info("Found company: ID={$company->id}, Name='{$company->name}', Website='{$company->website}'");

                if (empty($company->name)) {
                    $this->warn("Company {$companyId} missing name. Name: '{$company->name}'. Skipping...");
                    $errorCount++;
                    continue;
                }

                $this->info("Processing company: {$company->name}");
                $this->info("Date range: {$startDate->format('Y-m-d')} to {$endDate->format('Y-m-d')}");
                $this->info("API Key: " . substr($apiKey, 0, 8) . "...");

                // Search for contractor using company name only
                $this->info("Calling Shovels API...");
                $contractorData = $this->shovelsApiService->searchContractors(
                    $company->name,
                    null, // No website parameter
                    $startDate,
                    $endDate
                );

                $this->info("API call completed.");
                $this->info("Contractor data: " . json_encode($contractorData));

                if ($contractorData) {
                    $this->info("Contractor data found, storing metrics...");
                    // Store the permit metrics data
                    $this->storeCompanyMetrics($company, $contractorData);
                    $this->info("✓ Successfully imported permit metrics for {$company->name}");
                    $successCount++;
                } else {
                    $this->warn("No contractor data found for {$company->name}");
                    $errorCount++;
                }

            } catch (\Exception $e) {
                $this->error("Error processing company {$companyId}: " . $e->getMessage());
                $this->error("Exception class: " . get_class($e));
                $this->error("Stack trace: " . $e->getTraceAsString());
                $errorCount++;
            }

            $this->info("=== Finished Company ID: {$companyId} ===\n");
        }

        $this->info("\nImport completed:");
        $this->info("✓ Successful: {$successCount}");
        $this->info("✗ Errors: {$errorCount}");

        return $successCount > 0 ? self::SUCCESS : self::FAILURE;
    }

    /**
     * Get 10 random company IDs that have a name
     */
    private function getRandomCompanyIds(): Collection
    {
        return Company::whereNotNull(Company::FIELD_NAME)
            ->where(Company::FIELD_NAME, '!=', '')
            ->inRandomOrder()
            ->limit(10)
            ->pluck(Company::FIELD_ID);
    }

    /**
     * Store company metrics data
     */
    private function storeCompanyMetrics(Company $company, array $contractorData): void
    {
        CompanyMetric::create([
            CompanyMetric::FIELD_COMPANY_ID => $company->id,
            CompanyMetric::FIELD_SOURCE => CompanyMetricSources::SHOVELS->value,
            CompanyMetric::FIELD_REQUEST_TYPE => CompanyMetricRequestTypes::PERMIT_METRICS->value,
            CompanyMetric::FIELD_REQUEST_URL => $this->shovelsApiService->getLastRequestUrl(),
            CompanyMetric::FIELD_REQUEST_RESPONSE => $contractorData,
        ]);
    }
}
