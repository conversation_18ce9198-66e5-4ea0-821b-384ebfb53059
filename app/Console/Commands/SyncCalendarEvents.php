<?php

namespace App\Console\Commands;

use App\Models\Calendar\Calendar;
use App\Services\Calendar\CalendarService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class SyncCalendarEvents extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'calendar:sync-events {--calendar_id=} {--from_date= : Used to filter calendar events}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $calendarId = $this->option('calendar_id');
        $fromDate = $this->option('from_date');

        if (empty($calendarId) || empty($fromDate)) {
            $this->error('Calendar id and from date are required.');
            return;
        }

        $fromDate = Carbon::parse($fromDate);

        $calendar = Calendar::query()->findOrFail($calendarId);

        /** @var CalendarService $calendarService */
        $calendarService = app(CalendarService::class);

        $calendarService->importLatestEvents(
            calendar: $calendar,
            timeMin : $fromDate,
        );

        $this->info('Successfully synced events');
    }
}
