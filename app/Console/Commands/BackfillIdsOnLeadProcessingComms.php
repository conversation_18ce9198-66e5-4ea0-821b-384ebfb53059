<?php

namespace App\Console\Commands;

use App\Models\LeadProcessingCommunication;
use App\Models\LeadProcessingHistory;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Throwable;
use function Laravel\Prompts\clear;
use function Laravel\Prompts\progress;

class BackfillIdsOnLeadProcessingComms extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backfill:lead-processing-communications {--chunk-size=5000 : The number of batches/jobs to create}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fill the consumer_product_id column on lead_processing_communications table';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $chunkSize = $this->option('chunk-size') ?: 5000;
        $count = DB::table(LeadProcessingCommunication::TABLE)
            ->where(LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID, 0)
            ->count();
        if (!$count) {
            $this->line("No LeadProcessingCommunications missing ids were found.");

            return Command::SUCCESS;
        }

        clear();
        $chunks = ceil($count / $chunkSize);
        $this->info("Processing $count LeadProcessingCommunications in $chunks chunks...\n");
        $progressBar = progress('Processing LeadProcessingCommunications chunks', $chunks);

        DB::table(LeadProcessingCommunication::TABLE)
            ->select([LeadProcessingCommunication::TABLE .'.'. LeadProcessingCommunication::FIELD_ID, LeadProcessingHistory::TABLE .'.'. LeadProcessingHistory::FIELD_CONSUMER_PRODUCT_ID])
            ->join(LeadProcessingHistory::TABLE, LeadProcessingHistory::TABLE .'.'. LeadProcessingHistory::FIELD_ID, LeadProcessingCommunication::TABLE .'.'. LeadProcessingCommunication::FIELD_RELATION_ID)
            ->where(LeadProcessingCommunication::TABLE .'.'. LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID, 0)
            ->orderBy(LeadProcessingCommunication::TABLE .'.'. LeadProcessingCommunication::FIELD_ID)
            ->chunkById($chunkSize, function ($chunk) use ($progressBar) {
                $upsertData = $chunk->reduce(function ($out, $row) {
                    $out[] = [
                        LeadProcessingCommunication::FIELD_ID                  => $row->id,
                        LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID => $row->consumer_product_id,
                    ];
                    return $out;
                }, []);

                DB::beginTransaction();

                try {
                    DB::table(LeadProcessingCommunication::TABLE)
                        ->upsert($upsertData, [], [LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID]);

                    $progressBar->advance();

                    DB::commit();
                }
                catch (Throwable $e) {
                    DB::rollback();
                    throw $e;
                }
            }, LeadProcessingCommunication::TABLE .'.'. LeadProcessingCommunication::FIELD_ID, LeadProcessingCommunication::FIELD_ID);

        $this->info("\nSuccessfully backfilled ids.\n");

        return Command::SUCCESS;
    }
}
