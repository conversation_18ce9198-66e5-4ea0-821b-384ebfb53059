<?php

namespace App\Console\Commands;

use App\Models\Legacy\EloquentCompany;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use Illuminate\Console\Command;
use Illuminate\Contracts\Container\BindingResolutionException;

class SyncDncContactsFromLegacyCompanyNotes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:dnc-users';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync company users dnc contact flag from legacy company notes';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        //lets try and find all the companies with dnc
        $companies =  EloquentCompany::query()
            ->whereLike(EloquentCompany::NOTES, '%dnc%')
            ->get();

        /** @var EloquentCompany $legacyCompany */
        foreach($companies as $legacyCompany) {
            $this->info($legacyCompany->{EloquentCompany::NOTES});
            $company = Company::query()->where(Company::FIELD_LEGACY_ID, $legacyCompany->companyid)->firstOrFail();
            $pattern = "/[0-9a-zA-Z]([-.\w]*[0-9a-zA-Z_+])*@(([0-9a-zA-Z][-\w]*\.)+[a-zA-Z]{2,9})/";
            preg_match_all($pattern, $legacyCompany->{EloquentCompany::NOTES}, $emailMatches);
            $companyUserEmailMatches = $company->users()->whereIn(CompanyUser::FIELD_EMAIL, $emailMatches[0])->get();
            /** @var CompanyUser $user */
            foreach($companyUserEmailMatches as $user) {
                $this->info('match - id:' . $user->id .' company-id: ' . $user->company_id . ' email: ' . $user->email);
                $user->{CompanyUser::FIELD_DNC_CONTACT} = true;
                $user->save();
            }
        }
    }
}