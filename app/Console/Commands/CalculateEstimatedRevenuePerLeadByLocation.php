<?php

namespace App\Console\Commands;

use App\Jobs\CalculateEstimatedRevenuePerLeadByLocationJob;
use Illuminate\Console\Command;

class CalculateEstimatedRevenuePerLeadByLocation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'calculate:estimated-revenue-per-lead-by-location';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Calculates and stores estimated revenue per lead data';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        CalculateEstimatedRevenuePerLeadByLocationJob::dispatchSync();
    }
}
