<?php

namespace App\Console\Commands;

use App\Models\Legacy\EloquentCompanyYoutubeLink;
use App\Models\Odin\Company;
use App\Services\DatabaseHelperService;
use App\Services\Odin\MigrateLegacyCompanyYoutubeLinksService;
use Illuminate\Console\Command;
use Illuminate\Database\Query\JoinClause;
use Symfony\Component\Console\Command\Command as CommandAlias;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Exception;

class MigrateLegacyCompanyYoutubeLinks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'legacy-migrate:company-youtube-links';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '<PERSON><PERSON> migrating the legacy company Youtube links to A2.';

    /**
     * Execute the console command.
     *
     * @param MigrateLegacyCompanyYoutubeLinksService $service
     * @return int
     */
    public function handle(MigrateLegacyCompanyYoutubeLinksService $service): int
    {
        $this->line("Initiating the migration process...");

        $totalLinks = $this->getBaseQuery()->count();

        if($totalLinks > 0) {
            $this->info("Total no. of links to migrate: $totalLinks");

            try {
                $this->getBaseQuery()
                     ->select(DB::raw(EloquentCompanyYoutubeLink::TABLE.".*"))
                     ->orderBy(EloquentCompanyYoutubeLink::ID)
                     ->chunk(500, function($legacyLinks) use ($service) {
                         /** @var Collection $legacyLinks */
                         $service->handle($legacyLinks);
                     });
            }
            catch (Exception $exc) {
                $this->alert("Error: {$exc->getMessage()}");
            }

            $this->info('Finished processing.');
        }
        else {
            $this->line("No link to migrate.");
        }

        return CommandAlias::SUCCESS;
    }

    /**
     * Prepares the base query to retrieve a list of YouTube links for companies migrated to A2.
     *
     * @return Builder
     */
    protected function getBaseQuery(): Builder
    {
        return EloquentCompanyYoutubeLink::query()
            ->join(DatabaseHelperService::database().'.'.Company::TABLE, function($join) {
                /** @var JoinClause $join */
                $join->on(
                    DatabaseHelperService::database().'.'.Company::TABLE.'.'.Company::FIELD_LEGACY_ID,
                    DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompanyYoutubeLink::TABLE.'.'.EloquentCompanyYoutubeLink::COMPANY_ID
                );
            })
            ->where(EloquentCompanyYoutubeLink::LINK, '!=', '')
            ->whereNotNull(EloquentCompanyYoutubeLink::LINK);
    }
}
