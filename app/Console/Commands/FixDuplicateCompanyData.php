<?php

namespace App\Console\Commands;

use App\Models\Odin\CompanyData;
use Illuminate\Console\Command;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;
use Symfony\Component\Console\Helper\ProgressBar;

class FixDuplicateCompanyData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:duplicate-company-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Identifies duplicate company data instances and merges into one instance.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        //steps: identify all the duplicate company data
        $this->info("\nRetrieving duplicate company data instances");
        $duplicateCompanyData = $this->getDuplicateCompanyData();

        if ($duplicateCompanyData->count() > 0) {
            $this->info("\n{$duplicateCompanyData->count()} companies with duplicate data found");
            $this->info("\nMerging Duplicates...");
            $bar = $this->output->createProgressBar($duplicateCompanyData->count());
            $this->mergeDuplicateCompanyData($duplicateCompanyData, $bar);

            $this->info("\nDuplicated Company Data Merged Successfully");
        } else {
            $this->info("\nNo duplicate company data instances found");
        }

        return Command::SUCCESS;
    }

    /**
     * Identifies duplicate company data instances and returns grouped by company_id
     *
     * @return Collection
     */
    private function getDuplicateCompanyData(): Collection
    {
        return CompanyData::query()->whereIn(CompanyData::FIELD_COMPANY_ID, function (Builder $query) {
            $query->select(CompanyData::FIELD_COMPANY_ID)
                ->from(CompanyData::TABLE)
                ->groupBy(CompanyData::FIELD_COMPANY_ID)
                ->havingRaw('COUNT(*) > 1');
        })->get()->groupBy(CompanyData::FIELD_COMPANY_ID);

    }

    /**
     * Merges company data payload values into most recently updated company_data instance and deletes all others
     *
     * @param Collection $duplicateCompanyDataByCompanyId
     * @param ProgressBar $bar
     * @return void
     */
    private function mergeDuplicateCompanyData(Collection $duplicateCompanyDataByCompanyId, ProgressBar $bar): void
    {
        foreach ($duplicateCompanyDataByCompanyId as $duplicateCompanyData) {
            $sortedCompanyData = $duplicateCompanyData->sortByDesc(function (CompanyData $companyData) {
                return $companyData->updated_at ?? $companyData->created_at;
            });

            $newestCompanyData = $sortedCompanyData->first();

            $mergedPayload = collect($newestCompanyData->{CompanyData::FIELD_PAYLOAD});

            foreach ($sortedCompanyData as $companyDataInstance) {
                if ($companyDataInstance !== $newestCompanyData) {
                    $mergedPayload = collect($companyDataInstance->{CompanyData::FIELD_PAYLOAD})->merge($mergedPayload);
                }
            }

            $newestCompanyData->{CompanyData::FIELD_PAYLOAD} = $mergedPayload->toArray();

            $newestCompanyData->save();

            $duplicateCompanyData->except($newestCompanyData->{CompanyData::FIELD_ID})->each->delete();

            $bar->advance();
        }

        $bar->finish();
    }
}
