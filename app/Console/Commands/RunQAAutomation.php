<?php

namespace App\Console\Commands;

use App\Jobs\CalculateAndStoreExpertRatingsJob;
use App\Models\Odin\ConsumerProduct;
use App\Services\QAAutomation\QAAutomationService;
use Illuminate\Console\Command;

class RunQAAutomation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'run:qa-automation
        {cp-ids : IDs of consumer products to check with QA Automation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Runs the QA Automation for a consumer product record and prints the result.';

    /**
     * Execute the console command.
     */
    public function handle(QAAutomationService $qaService): void
    {
        $cpIds = array_map('intval', explode(',', $this->argument('cp-ids')));
        foreach ($cpIds as $consumerProductId) {
            $consumerProduct = ConsumerProduct::findOrFail($consumerProductId);
            $consumer = $consumerProduct->consumer;

            print "Consumer {$consumer->id}, Name: $consumer->first_name $consumer->last_name, Phone: $consumer->formatted_phone, Email: $consumer->email, own property: ".(bool)$consumerProduct->own_property.", classification: $consumer->classification,  industry service: ".$consumerProduct->industryService?->slug."\n";

            $result = $qaService->qualifyConsumerProduct($consumerProductId);

            if ($result) {
                $this->info("Consumer Product $consumerProductId: PASSED");
            } else {
                $this->info("Consumer Product $consumerProductId: FAILED");
            }
        }
    }
}
