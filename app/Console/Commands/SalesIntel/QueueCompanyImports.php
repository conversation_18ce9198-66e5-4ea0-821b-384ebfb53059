<?php

namespace App\Console\Commands\SalesIntel;

use App\Jobs\SalesIntel\ImportCompanies;
use App\Models\SalesIntel\FailedCompanyImportRecord;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class QueueCompanyImports extends Command
{
    protected $signature = 'salesintel:queue-company-imports
                            {--states= : A comma-separated list of 2 letter state codes}
                            {--zipcodes= : A comma-separated list of 5 digit zipcodes}
                            {--domains= : A comma-separated list of valid url domains}
                            {--specialties= : A comma-separated list of string keywords}
                            {--naics= : A comma-separated list of NAICS codes}';

    protected $description = 'Dispatches a job to import companies from SalesIntel';

    public function handle()
    {
        $filters = collect([
            'states',
            'zipcodes',
            'domains',
            'specialties',
        ]);

        collect($this->options())
            ->reject(fn ($values, $option) => $filters->doesntContain($option) || blank($values))
            ->each(fn ($values, $filter) => $this->importViaFilter($filter, $values));
    }

    private function importViaFilter(string $filter, string $values)
    {
        $filter = str($filter)->singular();

        $naics = $this->getNAICS();

        $failedRecords = $this->getFailedRecords($filter, $naics);

        $this->sanitizeValues($values, $filter)
            ->reject(function ($value) use ($failedRecords, $filter, $naics) {
                if ($failedRecords->contains($value)) {
                    $this->info(str("No more Company records exist for the {$filter} '{$value}'")
                        ->when($naics, fn ($str) => $str->append(' with NAICS '.implode(', ', $naics))));

                    return true;
                }
            })->each(function ($value) use ($filter, $naics) {
                $this->info(str("Queuing Company Imports for the {$filter} '{$value}'")
                    ->when($naics, fn ($str) => $str->append(' with NAICS '.implode(', ', $naics))));

                ImportCompanies::dispatch($filter, $value, $naics);
            });
    }

    private function sanitizeValues(string $values, string $filter): Collection
    {
        return str($values)
            ->explode(',')
            ->transform(function ($value) use ($filter) {
                $value = str($value)->trim();

                return match ($filter) {
                    'state' => $value->upper(),
                    'domain' => $value->replaceMatches('((http(s*)(:\/\/))|(www.)|(/$))', '')->before('/'),
                    default => $value
                };
            })->filter(fn ($value) => match ($filter) {
                'state' => str($value)->isMatch('/^[A-z]{2}$/'),
                'zipcode' => str($value)->isMatch('/^[0-9]{4}[1-9]{1}$/'),
                // From https://stackoverflow.com/a/6041965 via https://laracasts.com/discuss/channels/laravel/regex-to-test-if-text-contains-a-url
                'domain' => str($value)->isMatch('/^([\w_-]+(?:(?:\.[\w_-]+)+))([\w.,@?^=%&:\/~+#-]*[\w@?^=%&\/~+#-])/'),
                'specialty' => str($value)->isNotEmpty(),
                default => false
            });
    }

    private function getFailedRecords(string $filter, ?array $naics)
    {
        return FailedCompanyImportRecord::whereFilter($filter)
            ->when(
                $naics,
                fn ($query) => $query->whereNaics(implode(',', $naics)),
                fn ($query) => $query->whereNull('naics')
            )->distinct()
            ->pluck('value');
    }

    private function getNAICS()
    {
        if ($this->hasOption('naics')) {
            return str($this->option('naics'))
                ->remove(' ')
                ->explode(',')
                ->filter(fn ($code) => is_numeric($code))
                ->all();
        }

        return collect();
    }
}
