<?php

namespace App\Console\Commands\SalesIntel;

use App\Jobs\SalesIntel\ImportUsers;
use App\Models\Odin\Company;
use Illuminate\Console\Command;

class QueueUserImports extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'salesintel:queue-user-imports {ids? : An optional comma-separated list of Company IDs}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Dispatches jobs to import users from SalesIntel for companies';

    public function handle(): void
    {
        $this->getCompanies()
            ->each(function (Company $company) {
                $this->info("Queuing User Imports for {$company->name} for domain {$company->website}");

                ImportUsers::dispatch($company);
            });
    }

    private function getCompanies()
    {
        if ($this->hasArgument('ids') && filled($this->argument('ids'))) {
            return Company::whereIn('id', $this->sanitizeIds());
        } else {
            return Company::limit(100)
                ->doesntHave('userImportRecords')
                ->doesntHave('failedImportRecords')
                ->doesntHave('users')
                ->whereNot('website', '')
                ->get();
        }
    }

    private function sanitizeIds()
    {
        return str($this->argument('ids'))
            ->explode(',')
            ->filter(fn ($id) => is_numeric($id) && (int) $id > 0)
            ->values();
    }
}
