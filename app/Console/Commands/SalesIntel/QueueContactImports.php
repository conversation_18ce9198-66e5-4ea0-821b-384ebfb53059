<?php

namespace App\Console\Commands\SalesIntel;

use App\Enums\Prospects\ProspectResolution;
use App\Enums\Prospects\ProspectSource;
use App\Enums\Prospects\ProspectStatus;
use App\Jobs\SalesIntel\ImportContacts;
use App\Models\Prospects\NewBuyerProspect;
use Illuminate\Console\Command;
use Illuminate\Contracts\Database\Query\Builder;

class QueueContactImports extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'salesintel:queue-contact-imports';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Dispatches jobs to import contacts from SalesIntel for prospects';

    public function handle(): void
    {
        NewBuyerProspect::limit(100)
            ->whereSource(ProspectSource::SALESINTEL)
            ->whereStatus(ProspectStatus::INITIAL)
            ->whereNull('resolution')
            ->doesntHave('contacts')
            ->get()
            ->each(function (NewBuyerProspect $prospect) {
                $this->info("Queuing Contact Imports for {$prospect->company_name}");

                ImportContacts::dispatch($prospect);
            });
    }
}
