<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Schema;

class ReseedDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:reseed';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reseed database (does not run in production)';

    /**
     * @return int
     * @throws \Doctrine\DBAL\Exception
     *
     * @source Code from: https://stackoverflow.com/a/18910102
     */
    public function handle()
    {
        if(App::environment(['production', 'prod'])) {
            $this->error("This command does not run in production");

            return 0;
        }

        $this->info("Truncating and reseeding all tables except migrations");
        $this->newLine();

        $tableNames = array_column(Schema::getTables(), 'name');

        Schema::disableForeignKeyConstraints();

        foreach($tableNames as $tableName) {
            if($tableName === 'migrations') {
                continue;
            }

            DB::table($tableName)->truncate();

            $this->comment("Truncated $tableName");
        }

        Schema::enableForeignKeyConstraints();

        $this->newLine();

        $this->info("All tables truncated");

        $this->newLine();

        $this->info("Seeding tables");

        $start = microtime(true);

        $this->newLine();

        $this->call('db:seed');

        $elapsed = floor(microtime(true) - $start);

        $this->info("Elapsed $elapsed secs");

        return 0;
    }
}
