<?php

namespace App\Console\Commands;

use App\Enums\Calendar\DemoStatus;
use App\Models\Calendar\CalendarEvent;
use App\Models\Calendar\Demo;
use App\Services\DemoService;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class CreateDemoForCalendarEvents extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'create-demo-for-calendar-events {--calendar_event_id=} {--calendar_event_ids=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create demo for calendar events';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $calendarEvents = $this->getCalendarEvents();

        if ($calendarEvents->isEmpty()) {
            $this->error('No calendar event id specified');
            return;
        }

        $this->createDemoForEvents($calendarEvents);

        $this->info('Demo for events have been created');
    }

    /**
     * @param Collection $calendarEvents
     * @return void
     */
    protected function createDemoForEvents(Collection $calendarEvents): void
    {
        foreach ($calendarEvents as $calendarEvent) {
            Demo::query()
                ->create([
                    Demo::FIELD_CALENDAR_EVENT_ID => $calendarEvent->{CalendarEvent::FIELD_ID},
                    Demo::FIELD_STATUS            => DemoStatus::CONFIRMED->value,
                    Demo::FIELD_USER_ID           => $calendarEvent->{CalendarEvent::FIELD_USER_ID},
                    Demo::FIELD_CREATED_AT        => $calendarEvent->{CalendarEvent::FIELD_CREATED_AT},
                    Demo::FIELD_UPDATED_AT        => $calendarEvent->{CalendarEvent::FIELD_CREATED_AT},
                ]);
        }
    }

    /**
     * @return Collection
     */
    protected function getCalendarEvents(): Collection
    {
        $calendarEventId = $this->option('calendar_event_id');
        $calendarEventIds = explode(',', $this->option('calendar_event_ids') ?? '');

        $uniqueCalendarEvents = collect($calendarEventIds)->push($calendarEventId)->filter()->unique();

        return CalendarEvent::query()
            ->whereDoesntHave(CalendarEvent::RELATION_DEMO)
            ->whereIn(CalendarEvent::FIELD_ID, $uniqueCalendarEvents)
            ->get();
    }
}
