<?php

namespace App\Console\Commands;

use App\Jobs\GetCompanyMetricsJob;
use App\Models\CompanyMetric;
use App\Models\Odin\Company;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Symfony\Component\Console\Output\OutputInterface;

class UpdateCompanyMetrics extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-company-metrics
        {--all : Retrieve metrics for all companies for the current month}
        {--current-month : Retrieve metrics for all companies without a company metrics entry for the current month}
        {--company-ids=none : Retrieve metrics for comma seperated list of company ids (ex --company-ids=1,3,5,7,9)}
        {--batch-size=500 : Max number of companies passed to each update job created}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Retrieve and store company metrics for all existing companies (--all), companies with '.
                             'the given ids (--companyIds=1,2,3...), or only companies without metrics data for the '.
                             'current month (--current-month).';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $companyIds = [];
        $companyIdsString = $this->option('company-ids');
        $allCompanies = $this->option('all');
        $currentMonth = $this->option('current-month');
        $batchSize = $this->option('batch-size');
        $count = 0;
        $query = Company::query();

        // Check for both or no arguments given
        if ($allCompanies && $companyIdsString != 'none') {
            $this->error("Cannot pass --all and --companyIds");
            return;
        } else if ($allCompanies && $currentMonth) {
            $this->error("Cannot pass --all and --current-month");
            return;
        } else if ($currentMonth && $companyIdsString != 'none') {
            $this->error("Cannot pass --companyIds and --current-month");
            return;
        } else if (!$allCompanies && $companyIdsString == 'none' && !$currentMonth) {
            $this->error("Must pass either --all, --current-month, or --companyIds=1,2,3...");
            return;
        }

        // Check for companyId values
        if ($companyIdsString != 'none') {
            $companyIds = array_map('intval', explode(',', $companyIdsString));
        }

        // Check for bad values in companyIds list
        if (in_array(0, $companyIds)) {
            $this->error("companyIds can only contain integers.");
            return;
        }

        // Get query depending on option
        if ($allCompanies) {
            $this->info("Updating Metrics for All Companies.\n", OutputInterface::VERBOSITY_NORMAL);
            $query = $this->getQueryAllCompanies($query);
        }
        if ($companyIds) {
            $this->info("Updating Metrics for Company Ids: ".json_encode($companyIds)."\n", OutputInterface::VERBOSITY_NORMAL);
            $query = $this->getQueryCompanyIds($query, $companyIds);
        }
        if ($currentMonth) {
            $this->info("Updating Metrics for Companies without metrics data for the current month.\n", OutputInterface::VERBOSITY_NORMAL);
            $query = $this->getQueryCurrentMonth($query);
        }

        // Setup for info printing
        $totalCompanies = $query->count();
        $bar = $this->output->createProgressBar($totalCompanies);
        $bar->start();
        $totalCompaniesCollection = new Collection();

        // Make API requests and create company_metrics entries
        $query->chunk($batchSize, function (Collection $companies) use (&$count, $bar, &$totalCompaniesCollection) {
            GetCompanyMetricsJob::dispatch($companies);
            $count += $companies->count();
            $bar->advance($companies->count());
            $totalCompaniesCollection = $totalCompaniesCollection->merge($companies);
        });

        // Print details
        $bar->finish();
        $this->info("\n\nUpdated Metrics for $count Companies.", OutputInterface::VERBOSITY_NORMAL);

        // Company list only printed if -v verbose flag is passed
        $this->info("\nCompanies:\n", OutputInterface::VERBOSITY_VERBOSE);
        foreach ($totalCompaniesCollection as $company) {
            $this->info($company[Company::FIELD_NAME].' (id '.$company[Company::FIELD_ID].')', OutputInterface::VERBOSITY_VERBOSE);
        }
    }

    /**
     * Grabs companies with a website
     * @param Builder $query
     * @return Builder
     */
    protected function getQueryAllCompanies(Builder $query): Builder
    {
        return $query
            ->where(function($subQuery) {
                /** @var Builder $subQuery */
                $subQuery
                    ->whereNotNull(Company::FIELD_WEBSITE)
                    ->where(Company::FIELD_WEBSITE, '!=', '');
            });
    }

    /**
     * Companies with the given IDs
     * @param Builder $query
     * @param array $companyIds
     * @return Builder
     */
    protected function getQueryCompanyIds(Builder $query, array $companyIds): Builder
    {
        return $query
            ->where(function($subQuery) use ($companyIds) {
                /** @var Builder $subQuery */
                $subQuery
                    ->whereNotNull(Company::FIELD_WEBSITE)
                    ->where(Company::FIELD_WEBSITE, '!=', '')
                    ->whereIn(Company::FIELD_ID, $companyIds);
            });
    }

    /**
     * Companies that don't have a metrics entry for the current month
     * @param Builder $query
     * @return Builder
     */
    protected function getQueryCurrentMonth(Builder $query): Builder
    {
        return $query
            ->select('*')
            ->whereNotExists(function($subQuery) {
                $subQuery
                    ->from(CompanyMetric::TABLE)
                    ->select('*')
                    ->where(CompanyMetric::FIELD_REQUEST_RESPONSE.'->month', now()->subMonth()->month)
                    ->where(CompanyMetric::FIELD_REQUEST_RESPONSE.'->year', now()->year)
                    ->whereColumn(Company::TABLE.'.'.Company::FIELD_ID, '=', CompanyMetric::TABLE.'.'.CompanyMetric::FIELD_COMPANY_ID);
            })
            ->whereNotNull(Company::FIELD_WEBSITE)
            ->where(Company::FIELD_WEBSITE, '!=', '');
    }
}
