<?php

namespace App\Console\Commands\Company;

use App\Enums\ActivityLog\ActivityLogName;
use App\Enums\ActivityLog\ActivityLogSubjectType;
use App\Jobs\DeleteCompany;
use App\Models\Odin\Company;
use App\Repositories\ActivityLog\ActivityLogRepository;
use App\Services\Companies\Delete\CompanyDeleteService;
use Illuminate\Console\Command;

class RetryScheduledCompanyDeletions extends Command
{
    protected $signature = 'company:retry-scheduled {--override-cache : Force deletion regardless of cache state}';
    protected $description = 'Dispatches DeleteCompany jobs for all companies previously scheduled.';
    protected bool $overrideCache = false;

    public function handle()
    {
        $this->overrideCache = $this->option('override-cache');

        $companies = app(ActivityLogRepository::class)->getActivityLogs(
            names: [ActivityLogName::COMPANY_DELETION_QUEUED->value],
            subjectType: ActivityLogSubjectType::COMPANY
        )->distinct()->pluck(ActivityLogRepository::SUBJECT_ID);

        $count = $companies->count();

        $this->info("$count unique companies scheduled for deletion.");

        $companies = Company::withTrashed()->whereIntegerInRaw(Company::FIELD_ID, $companies->toArray())->get();

        $count = $companies->count();

        $this->info("$count companies have not already been deleted.");

        $bar = $this->output->createProgressBar($count);
        $bar->start();

        foreach ($companies as $company) {
            $service = new CompanyDeleteService($company);
            if ($this->overrideCache && !$service->markedForDeletion()) {
                $service->markForDeletion();
            }

            DeleteCompany::dispatch($company->id);
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info('Company delete jobs dispatched.');
    }

}
