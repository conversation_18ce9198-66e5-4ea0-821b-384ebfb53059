<?php

namespace App\Console\Commands;

use App\Jobs\Billing\SyncCompanyCreditCardsInfoJob;
use App\Models\Billing\CompanyPaymentMethod;
use App\Services\Billing\BillingLogService;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class SyncCompanyCreditCardNumbers extends Command
{
    protected string $logNamespace = 'sync_company_credit_card_numbers';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync-company-credit-card-numbers {--company-id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "Pull and save the companies' credit cards";


    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $companyIds = $this->getCompanyIds();

        if ($companyIds->isEmpty()) {
            $this->logInfo(
                message: "No company ids found. Exiting command."
            );
            return;
        }

        $companyIdCount = $companyIds->count();

        $this->logInfo(
            message: "Dispatching total of $companyIdCount jobs to sync companies credit cards details...",
            context: [
                'company_ids' => $companyIds
            ]
        );

        $this->syncCompaniesCreditCardNumbers(
            companyIds: $companyIds
        );

        $this->logInfo(
            message: "Jobs dispatched. Exiting command.",
        );
    }

    public function logInfo(string $message, array $context = []): void
    {
        $this->info($message);

        BillingLogService::log(
            message  : $message,
            namespace: $this->logNamespace,
            context  : $context
        );
    }

    /**
     * @param Collection $companyIds
     * @return void
     */
    public function syncCompaniesCreditCardNumbers(Collection $companyIds): void
    {
        foreach ($companyIds as $companyId) {
            SyncCompanyCreditCardsInfoJob::dispatch($companyId);
        }
    }

    /**
     * @return Collection
     */
    public function getCompanyIds(): Collection
    {
        $companyId = $this->option('company-id');

        if (filled($companyId)) {
            return collect(explode(',', $companyId))
                ->map(fn(string $item) => trim($item))
                ->filter()
                ->unique();
        }

        $this->logInfo(
            message: "No company ids provided. Falling back to all companies without credit card number.",
            context: [
                'option_company-id' => $companyId
            ]
        );

        return CompanyPaymentMethod::onlyStripe()
            ->select(CompanyPaymentMethod::TABLE . '.' . CompanyPaymentMethod::FIELD_COMPANY_ID)
            ->whereNull(CompanyPaymentMethod::TABLE . '.' . CompanyPaymentMethod::FIELD_NUMBER)
            ->whereNull(CompanyPaymentMethod::TABLE . '.' . CompanyPaymentMethod::FIELD_DELETED_AT)
            ->groupBy(CompanyPaymentMethod::TABLE . '.' . CompanyPaymentMethod::FIELD_COMPANY_ID)
            ->pluck(CompanyPaymentMethod::FIELD_COMPANY_ID);
    }
}
