<?php

namespace App\Console\Commands;

use App\Enums\Campaigns\CampaignStatus;
use App\Helpers\CarbonHelper;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ProductAssignment;
use App\Repositories\Campaigns\CompanyCampaignDataRepository;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;

class GenerateCompanyCampaignData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'company-campaigns:generate-company-campaign-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Identifies Company Campaigns have bought a lead, and stores their last lead sold at and average lead cost in the company campaign data model';

    /**
     * Execute the console command.
     */
    public function handle(CompanyCampaignDataRepository $campaignDataRepository)
    {
        $this->info("Retrieving eligible campaigns.");
        $companyCampaigns = $this->getEligibleCampaigns();
        $campaignCount    = $companyCampaigns->count();
        $this->info("$campaignCount eligible campaigns retrieved");
        $bar = $this->output->createProgressBar($campaignCount);
        foreach ($companyCampaigns as $campaign) {
            $soldProductAssignments = $campaign->soldProductAssignmentsQuery()->limit(50)->get();

            $bar->advance();

            if ($soldProductAssignments->isEmpty()) {
                continue;
            }

            $lastLeadSoldAt = $soldProductAssignments->first()->{ProductAssignment::FIELD_DELIVERED_AT};

            $lastLeadSoldAt = CarbonHelper::parse($lastLeadSoldAt);

            $average   = $soldProductAssignments->pluck(ProductAssignment::FIELD_COST)->average();
            $leadCount = $soldProductAssignments->count();

            $companyCampaignData = $campaignDataRepository->firstOrNew($campaign->id);

            $campaignDataRepository->saveData(
                campaignData            : $companyCampaignData,
                leadLastSoldAt          : $lastLeadSoldAt,
                averageCost             : $average,
                leadCostLastCalculatedAt: now(),
                leadCount               : $leadCount,
            );
        }
        $bar->finish();
        $this->info("\nCompany Campaign Data Calculated");
    }

    /**
     * @return Collection<CompanyCampaign>|array
     */
    private function getEligibleCampaigns(): Collection|array
    {
        $companyIds = ProductAssignment::query()
            ->where(ProductAssignment::FIELD_CHARGEABLE, true)
            ->where(ProductAssignment::FIELD_DELIVERED, true)
            ->where(ProductAssignment::FIELD_DELIVERED_AT, '>', now()->subMonths(3))
            ->distinct()
            ->pluck(ProductAssignment::FIELD_COMPANY_ID);

        return CompanyCampaign::query()
            ->whereDoesntHave(CompanyCampaign::RELATION_CAMPAIGN_DATA)
            ->whereIntegerInRaw(CompanyCampaign::FIELD_COMPANY_ID, $companyIds)
            ->get();
    }
}
