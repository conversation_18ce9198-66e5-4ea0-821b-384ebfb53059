<?php

namespace App\Console\Commands;

use App\Jobs\LegacyMigrations\MigrateLegacyCompanyContactsJob;
use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Odin\CompanyUser;
use App\Services\DatabaseHelperService;
use App\Services\BatchHelperService;
use App\Services\QueueHelperService;
use Illuminate\Bus\Batch;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Illuminate\Console\Command;
use Throwable;

class MigrateLegacyCompanyContacts extends Command
{


    const BATCH_COUNT = 'batch-count';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'legacy-migrate:company-contacts {--batch-count=20 : The number of batches/jobs to create} {--ids=* : Pass in specific ids to migrate }';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handles the migration of legacy company contacts to their new schema.';

    /**
     * Execute the console command.
     *
     * @return int
     * @throws Throwable
     */
    public function handle()
    {
        $this->line("Migrating company contacts");

        // Performance
        DB::disableQueryLog();

        $ids = $this->option('ids');
        if (count($ids)) {
            $contactsJob = new MigrateLegacyCompanyContactsJob(collect($ids));
            Bus::dispatchSync($contactsJob);
            return count($ids);
        }

        $legacyIdCol = CompanyUser::FIELD_LEGACY_ID;

        $contactIdCol = EloquentCompanyContact::FIELD_CONTACT_ID;
        $companyUserIdCol = CompanyUser::FIELD_ID;
        $countCol = 'count';

        $alreadyMigratedCount = CompanyUser::withTrashed()
                                    ->where(CompanyUser::FIELD_IS_CONTACT, 1)
                                    ->selectRaw("COUNT($companyUserIdCol) AS $countCol")
                                    ->first()
                                    ->{$countCol};

        $legacyCount = EloquentCompanyContact::withTrashed()
                            ->selectRaw("COUNT($contactIdCol) AS $countCol")
                            ->first()
                            ->{$countCol};

        $total = $legacyCount - $alreadyMigratedCount;

        if($total > 0) {
            $this->line("$total contacts total");

            $this->line("Creating migration jobs");

            $batchCount = (int) $this->option(self::BATCH_COUNT);
            $batchJobs = [];

            $alreadyMigratedSubQuery = CompanyUser::withTrashed()
                ->where(CompanyUser::FIELD_IS_CONTACT, 1)
                ->selectRaw("$legacyIdCol");

            $contactIds = EloquentCompanyContact::withTrashed()
                ->leftJoinSub($alreadyMigratedSubQuery, 'already_migrated', function($join) use ($legacyIdCol) {
                    $join->on(
                        EloquentCompanyContact::TABLE.'.'.EloquentCompanyContact::FIELD_CONTACT_ID,
                        '=',
                        "already_migrated.{$legacyIdCol}"
                    );
                })
                ->select(EloquentCompanyContact::TABLE.'.'.EloquentCompanyContact::FIELD_CONTACT_ID)
                ->whereNull("already_migrated.{$legacyIdCol}")
                ->pluck(EloquentCompanyContact::FIELD_CONTACT_ID);

            $chunkedContactIds = app(BatchHelperService::class)->chunkByBatchCount($batchCount, $contactIds->toArray());

            foreach ($chunkedContactIds as $idsChunk) {
                $batchJobs[] = new MigrateLegacyCompanyContactsJob(collect($idsChunk));
            }

            $batch = Bus::batch($batchJobs)
                        ->catch(function (Batch $batch, Throwable $throwable) {
                            logger()->error("Legacy company contacts migration error: ".$throwable->getMessage());
                        })
                        ->allowFailures()
                        ->name("Legacy Company Contacts Migration")
                        ->onConnection(QueueHelperService::QUEUE_CONNECTION)
                        ->onQueue(QueueHelperService::QUEUE_NAME_LEGACY_MIGRATION)
                        ->dispatch();

            $bar = $this->output->createProgressBar($batch->totalJobs);

            $bar->setFormat("%current%/%max% migration jobs done. Elapsed: %elapsed%");
            $bar->setRedrawFrequency(1);

            $bar->start();

            while (!$batch->finished() && !$batch->cancelled()) {
                $batch = $batch->fresh();
                $bar->setProgress($batch->processedJobs());
            }

            $bar->finish();

            $this->newLine();
            $this->line("Company contacts migrated");
        }
        else {
            $this->line("No contacts to migrate");
        }

        return 0;
    }
}
