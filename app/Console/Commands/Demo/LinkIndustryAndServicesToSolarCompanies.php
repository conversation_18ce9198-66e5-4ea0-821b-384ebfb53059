<?php

namespace App\Console\Commands\Demo;

use App\Models\Legacy\CompanyRanking;
use App\Models\Legacy\EloquentCompany;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyData;
use App\Models\Odin\CompanyIndustry;
use App\Models\Odin\CompanyService;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Services\DatabaseHelperService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class LinkIndustryAndServicesToSolarCompanies extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'demo:solar-company-link-industry {industry} {word_to_replace=solar} {--services=all}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Link industry and services to solar companies. Arguments: Name of the industry. Word to replace with new industry name, default is "solar". Options: --services = all or comma separated name of the services';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $industry      = $this->argument('industry');
        $wordToReplace = $this->argument('word_to_replace');
        $services      = $this->option('services') ?? 'all';

        /** @var Industry $industryModel */
        $industryModel = Industry::query()
            ->where(Industry::FIELD_NAME, $industry)
            ->orWhere(Industry::FIELD_SLUG, $industry)
            ->firstOrFail();

        if ($services !== 'all')
            $industryServices = $industryModel->services()
                ->whereIn(IndustryService::FIELD_NAME, explode(',', $services))
                ->orWhereIn(IndustryService::FIELD_SLUG, explode(',', $services))
                ->pluck(IndustryService::FIELD_ID);
        else
            $industryServices = $industryModel->services()->pluck(IndustryService::FIELD_ID);

        $this->info('Linking services to companies....');

        $this->attachIndustryAndServices($this->getCompanyQuery($industryServices->toArray())->pluck(Company::FIELD_ID), $industryModel, $industryServices);

        $this->info('Updating names of companies....');

        $this->updateCompanyNames($this->getCompanyQuery([])->pluck(Company::FIELD_NAME, Company::FIELD_ID), $wordToReplace, $industryModel->name);

        $this->info('Finished');

        return Command::SUCCESS;
    }

    /**
     * Handles attaching the industry and services to the company.
     *
     * @param Collection $companyIds
     * @param Industry $industry
     * @param Collection $services
     * @return void
     */
    protected function attachIndustryAndServices(Collection $companyIds, Industry $industry, Collection $services): void
    {
        foreach($companyIds->chunk(500) as $datum) {
            $items = collect($datum);

            $industryData = $items->map(fn($item) => [CompanyIndustry::FIELD_INDUSTRY_ID => $industry->id, CompanyIndustry::FIELD_COMPANY_ID => $item]);
            $serviceData = $items->flatMap(fn($item) => $services->map(fn($service) => [CompanyService::FIELD_COMPANY_ID => $item, CompanyService::FIELD_INDUSTRY_SERVICE_ID => $service]));

            CompanyIndustry::query()->insert($industryData->toArray());
            CompanyService::query()->insert($serviceData->toArray());
        }
    }

    /**
     * Handles updating the company names.
     *
     * @param Collection $companies
     * @param string $wordToReplace
     * @param string $replaceTo
     * @return void
     */
    protected function updateCompanyNames(Collection $companies, string $wordToReplace, string $replaceTo): void
    {
        foreach($companies->chunk(500) as $datum) {
            $items = collect($datum)->map(fn($item, $id) => [Company::FIELD_ID => $id, Company::FIELD_NAME => str_ireplace($wordToReplace, $replaceTo, $item)]);

            Company::query()->upsert($items->toArray(), [Company::FIELD_ID], [Company::FIELD_NAME]);
        }
    }

    /**
     * Returns the query for the company.
     *
     * @return Builder
     */
    protected function getCompanyQuery(array $services = []): Builder
    {
        return Company::query()
            ->with([
                Company::RELATION_INDUSTRIES,
                Company::RELATION_SERVICES
            ])
            ->join(
                DatabaseHelperService::readOnlyDatabase() . '.' . EloquentCompany::TABLE,
                Company::TABLE . '.' . Company::FIELD_LEGACY_ID,
                DatabaseHelperService::readOnlyDatabase() . '.' . EloquentCompany::TABLE . '.' . EloquentCompany::COMPANY_ID
            )
            ->where(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentCompany::TABLE . '.' . EloquentCompany::TYPE, EloquentCompany::TYPE_INSTALLER)
            ->whereDoesntHave(Company::RELATION_SERVICES, fn($query) => $query->whereIn(IndustryService::TABLE.'.'.IndustryService::FIELD_ID, $services));
    }
}
