<?php

namespace App\Console\Commands\Demo;

use App\Enums\Company\CompanyCampaignStatus;
use App\Enums\CompanyConsolidatedStatus;
use App\Models\Legacy\CompanyRanking;
use App\Models\Legacy\EloquentCompany;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyData;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Services\DatabaseHelperService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class DemoCompaniesLinkIndustryAndServices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'demo:link-industry {company_count} {industry} {--services=all}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Link industry and services to demo companies. Arguments: number of companies and name of the industry. Options: --services = all or comma separated name of the services';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $companyCount = $this->argument('company_count');
        $industry     = $this->argument('industry');
        $services     = $this->option('services') ?? 'all';

        if ($companyCount < 50) $this->error('Minimum 50 companies required');

        /** @var Industry $industryModel */
        $industryModel = Industry::query()
            ->where(Industry::FIELD_NAME, $industry)
            ->orWhere(Industry::FIELD_SLUG, $industry)
            ->firstOrFail();

        $this->findCCompanies($companyCount, $industryModel, $services);

        return Command::SUCCESS;
    }

    protected function findCCompanies(int $count, Industry $industry, string $services)
    {
        $this->info('Getting companies....');

        $spend5000Companies = $this->getQuery(5000)->limit(round($count * .25))->get();
        $otherCompanies = $this->getQuery(5000, '<')->limit(round($count * .25))->get();
        $companies = $spend5000Companies->merge($otherCompanies);

        $remainingCount = $count - $companies->count();

        //add some active companies
        $companies = $companies->merge(
            $this->getQueryForRanking()
                ->whereNotIn(Company::TABLE . '.' . Company::FIELD_ID, $companies->pluck(Company::FIELD_ID))
                ->limit(round($remainingCount * .50))
                ->where(Company::TABLE . '.' . Company::FIELD_CONSOLIDATED_STATUS, CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS)
                ->get()
        );

        //add some inactive companies
        $companies = $companies->merge(
            $this->getQueryForRanking()
                ->whereNotIn(Company::TABLE . '.' . Company::FIELD_ID, $companies->pluck(Company::FIELD_ID))
                ->whereIn(Company::TABLE . '.' . Company::FIELD_CAMPAIGN_STATUS, [CompanyCampaignStatus::CAMPAIGNS_PAUSED, CompanyCampaignStatus::CAMPAIGNS_OFF])
                ->limit($remainingCount - $companies->count())
                ->get()
        );

        if ($companies->count() < $count) {
            $companies = $companies->merge(
                Company::query()
                    ->whereNotIn(Company::TABLE . '.' . Company::FIELD_ID, $companies->pluck(Company::FIELD_ID))
                    ->limit($count - $companies->count())
                    ->get()
            );
        }

        $this->linkIndustryAndServices($companies, $industry, $services);

        $this->info('Companies: ' . $companies->pluck(Company::FIELD_ID)->implode(', '));

        $this->info('Finished');
    }

    protected function linkIndustryAndServices(Collection $companies, Industry $industry, string $services)
    {
        $this->info('Attaching industry and services');

        if ($services !== 'all')
            $industryServices = $industry->services()
                ->whereIn(IndustryService::FIELD_NAME, explode(',', $services))
                ->orWhereIn(IndustryService::FIELD_SLUG, explode(',', $services))
                ->get();
        else
            $industryServices = $industry->services;

        /** @var Company $company */
        foreach ($companies as $company) {
            if (!$company->industries->first(fn(Industry $industryModel) => $industryModel->id === $industry->id)) {
                $company->industries()->attach($industry->id);
            }

            if ($industryServices->isNotEmpty()) {
                $industryServices = $industryServices->filter(fn(IndustryService $industryService) => !in_array($industryService->id, $company->services->pluck(IndustryService::FIELD_ID)->toArray()));

                if ($industryServices->isNotEmpty()) {
                    $company->services()->attach($industryServices->pluck(IndustryService::FIELD_ID)->toArray());
                }
            }

            if (Str::of(Str::lower($company->name))->contains('solar') || Str::of(Str::lower($company->name))->contains('roofing')) {
                $company->name = str_ireplace('solar', $industry->name, $company->name);
                $company->name = str_ireplace('roofing', $industry->name, $company->name);
                $company->update();
            }
        }
    }

    /**
     * @param float $spend
     * @param string $operator
     *
     * @return Builder
     */
    protected function getQuery(float $spend, string $operator = '>='): Builder
    {
        return $this->getQueryForRanking()
            ->whereExists(function (\Illuminate\Database\Query\Builder $query) use ($spend, $operator) {
                $query->selectRaw('company_id, SUM(cost) AS total')
                    ->from(ProductAssignment::TABLE)
                    ->where(ProductAssignment::FIELD_DELIVERED_AT, '>=', now()->subDays(30))
                    ->where(ProductAssignment::FIELD_CHARGEABLE, true)
                    ->where(ProductAssignment::FIELD_DELIVERED, true)
                    ->whereRaw(Company::TABLE . '.' . Company::FIELD_ID . '=' . ProductAssignment::FIELD_COMPANY_ID)
                    ->groupBy(ProductAssignment::FIELD_COMPANY_ID)
                    ->havingRaw("total $operator $spend");
            });
    }

    /**
     * @return Builder
     */
    protected function getQueryForRanking(): Builder
    {
        return Company::query()
            ->join(
                DatabaseHelperService::readOnlyDatabase() . '.' . EloquentCompany::TABLE,
                Company::TABLE . '.' . Company::FIELD_LEGACY_ID, '=',
                DatabaseHelperService::readOnlyDatabase() . '.' . EloquentCompany::TABLE . '.' . EloquentCompany::COMPANY_ID
            )
            ->join(
                DatabaseHelperService::readOnlyDatabase() . '.' . CompanyRanking::TABLE,
                DatabaseHelperService::readOnlyDatabase() . '.' . EloquentCompany::TABLE . '.' . EloquentCompany::COMPANY_ID,
                '=',
                DatabaseHelperService::readOnlyDatabase() . '.' . CompanyRanking::TABLE . '.' . CompanyRanking::COMPANY_ID
            )
            ->join(
                CompanyData::TABLE,
                Company::TABLE . '.' . Company::FIELD_ID,
                '=',
                CompanyData::TABLE . '.' . CompanyData::FIELD_COMPANY_ID
            )
            ->whereRaw('payload->"$.expert_rating_overall_score" > 0')
            ->where(DatabaseHelperService::readOnlyDatabase() . '.' . CompanyRanking::TABLE . '.' . CompanyRanking::BAYESIAN_ALL_TIME, '>', 0);
    }
}
