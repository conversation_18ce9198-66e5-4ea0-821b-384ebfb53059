<?php

namespace App\Console\Commands;

use App\Models\Odin\Company;
use Illuminate\Console\Command;

class CleanCompanyWebsites extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clean-up:company-websites';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cleans company websites';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $companies = Company::query()
            ->whereNotNull(Company::FIELD_WEBSITE)
            ->get();

        $bar = $this->output->createProgressBar($companies->count());
        $bar->start();

        /** @var Company $company */
        foreach($companies as $company) {
            $company->website = getWebsiteDomain($company->website);
            $company->save();

            $bar->advance();
        }
    }
}
