<?php

namespace App\Console\Commands;

use App\Jobs\LegacyMigrations\MigrateLegacyCompanyUsersJob;
use App\Models\Legacy\EloquentUser;
use App\Models\Odin\CompanyUser;
use App\Services\BatchHelperService;
use App\Services\QueueHelperService;
use Illuminate\Bus\Batch;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Illuminate\Console\Command;
use Throwable;

class MigrateLegacyCompanyUsers extends Command
{
    const BATCH_COUNT = 'batch-count';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'legacy-migrate:company-users {--batch-count=20 : The number of batches/jobs to create} {--ids=* : Pass in specific ids to migrate }';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handles the migration of legacy company users to their new schema.';

    /**
     * Execute the console command.
     *
     * @return int
     * @throws Throwable
     */
    public function handle()
    {
        $this->line("Migrating company users");

        // Performance
        DB::disableQueryLog();

        $ids = $this->option('ids');
        if (count($ids)) {
            MigrateLegacyCompanyUsersJob::dispatchSync(collect($ids));
            return count($ids);
        }

        $legacyIdCol = CompanyUser::FIELD_LEGACY_ID;

        $userIdCol = EloquentUser::ID;
        $companyUserIdCol = CompanyUser::FIELD_ID;
        $countCol = 'count';

        $alreadyMigratedCount = CompanyUser::withTrashed()
            ->where(CompanyUser::FIELD_IS_CONTACT, 0)
            ->selectRaw("COUNT($companyUserIdCol) AS $countCol")
            ->first()
            ->{$countCol};

        $legacyCount = EloquentUser::withTrashed()
            ->whereNotNull(EloquentUser::COMPANY_ID)
            ->where(EloquentUser::COMPANY_ID, '>', 0)
            ->selectRaw("COUNT($userIdCol) AS $countCol")
            ->first()
            ->{$countCol};

        $total = $legacyCount - $alreadyMigratedCount;

        if($total > 0) {
            $this->line("$total users total");

            $this->line("Creating migration jobs");

            $batchCount = (int) $this->option(self::BATCH_COUNT);

            $batchJobs = [];

            $alreadyMigratedSubQuery = CompanyUser::withTrashed()
                ->where(CompanyUser::FIELD_IS_CONTACT, 0)
                ->selectRaw("$legacyIdCol");

            $userIds = EloquentUser::withTrashed()
                ->leftJoinSub($alreadyMigratedSubQuery, 'already_migrated', function($join) use ($legacyIdCol) {
                    $join->on(
                        EloquentUser::TABLE.'.'.EloquentUser::ID,
                        '=',
                        "already_migrated.{$legacyIdCol}"
                    );
                })
                ->select(EloquentUser::TABLE.'.'.EloquentUser::ID)
                ->whereNull("already_migrated.{$legacyIdCol}")
                ->whereNotNull(EloquentUser::COMPANY_ID)
                ->where(EloquentUser::COMPANY_ID, '>', 0)
                ->pluck(EloquentUser::ID);

            $chunkedUserIds = app(BatchHelperService::class)->chunkByBatchCount($batchCount, $userIds->toArray());

            foreach ($chunkedUserIds as $idsChunk) {
                $batchJobs[] = new MigrateLegacyCompanyUsersJob(collect($idsChunk));
            }

            $batch = Bus::batch($batchJobs)
                        ->catch(function (Batch $batch, Throwable $throwable) {
                            logger()->error("Legacy company users migration error: ".$throwable->getMessage());
                        })
                        ->allowFailures()
                        ->name("Legacy Company Users Migration")
                        ->onConnection(QueueHelperService::QUEUE_CONNECTION)
                        ->onQueue(QueueHelperService::QUEUE_NAME_LEGACY_MIGRATION)
                        ->dispatch();

            $bar = $this->output->createProgressBar($batch->totalJobs);

            $bar->setFormat("%current%/%max% migration jobs done. Elapsed: %elapsed%");
            $bar->setRedrawFrequency(1);

            $bar->start();

            while (!$batch->finished() && !$batch->cancelled()) {
                $batch = $batch->fresh();
                $bar->setProgress($batch->processedJobs());
            }

            $bar->finish();

            $this->newLine();
            $this->line("Company users migrated");
        }
        else {
            $this->line("No users to migrate");
        }

        return 0;
    }
}
