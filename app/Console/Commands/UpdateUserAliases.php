<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Validator;
use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class UpdateUserAliases extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:update-aliases {--user_id= : User id} {--aliases= : Comma-separated list of email aliases}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "Update a user's email aliases";


    /**
     * Execute the console command.
     */
    public function handle()
    {
        /** @var Collection $aliases */
        [
            'user_id' => $userId,
            'aliases' => $aliases,
        ] = $this->validate();

        if (empty($userId)) {
            $this->error('No user id provided');
            exit(Command::FAILURE);
        }

        $user = User::query()->find($userId);

        if (!$user) {
            $this->error('User not found.');
            exit(Command::FAILURE);
        }

        $this->printCurrentData(
            user: $user,
            aliases: $aliases
        );

        $this->askPermissionToProceed(
            user: $user,
            aliases: $aliases
        );

        return Command::SUCCESS;
    }


    /**
     * @return array|void
     * @throws ValidationException
     */
    protected function validateOptions()
    {
        $userId = $this->option('user_id');
        $aliases = $this->option('aliases');

        $validator = Validator::make([
            'user_id' => $userId,
            'aliases' => $aliases,
        ], [
            'user_id' => 'required|integer',
            'aliases' => 'string',
        ]);

        if ($validator->fails()) {
            foreach ($validator->errors()->all() as $error) {
                $this->error($error);
            }
            exit(Command::FAILURE);
        }

        return $validator->validated();
    }

    /**
     * @param int $userId
     * @param string $rawAliases
     * @return Collection|void
     */
    protected function validateAliases(int $userId, string $rawAliases)
    {
        $aliases = Str::of($rawAliases)
            ->explode(',')
            ->map(fn($alias) => Str::of($alias)->lower()->trim())
            ->filter()
            ->unique()
            ->values();

        foreach ($aliases as $alias) {
            $overlapUser = User::query()
                ->whereNot(User::FIELD_ID, $userId)
                ->where(function ($query) use ($alias) {
                    $query->whereJsonContains(User::FIELD_EMAIL_ALIASES, $alias)
                        ->orWhere(User::FIELD_EMAIL, $alias);
                })->first();

            if (filled($overlapUser)) {
                $this->error("The provided email alias $alias belongs to the user $overlapUser->name. Remove it before trying again.");
                exit(Command::FAILURE);
            }
        }

        return $aliases;
    }

    /**
     * @return array
     * @throws ValidationException
     */
    public function validate(): array
    {
        [
            'user_id' => $userId,
            'aliases' => $rawAliases,
        ] = $this->validateOptions();

        $aliases = $this->validateAliases(
            userId    : $userId,
            rawAliases: $rawAliases,
        );

        return [
            'user_id' => $userId,
            'aliases' => $aliases,
        ];
    }

    /**
     * @param User $user
     * @param Collection $aliases
     * @return void
     */
    protected function askPermissionToProceed(
        User $user,
        Collection $aliases
    ): void
    {
        $choice = $this->choice("Do you want to replace all $user->name's email aliases?", ['yes' => 'yes', 'no' => 'no']);

        if ($choice === 'yes') {
            $user->update([
                User::FIELD_EMAIL_ALIASES => $aliases
            ]);

            $this->info('User email aliases updated successfully');
        }
    }

    /**
     * @param User $user
     * @param Collection $aliases
     * @return void
     */
    protected function printCurrentData(
        User $user,
        Collection $aliases
    ): void
    {
        $this->info('Current User emails: ');
        $this->info("Primary: $user->email");
        $this->info("Aliases");

        if (filled($user->email_aliases)) {
            foreach ($user->email_aliases as $alias) {
                $this->info("- $alias");
            }
        } else {
            $this->info('No alias set');
        }

        $this->newLine();
        $this->info('Emails provided: ');
        foreach ($aliases as $alias) {
            $this->info("- $alias");
        }
    }
}
