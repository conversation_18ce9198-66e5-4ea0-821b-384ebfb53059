<?php

namespace App\Console\Commands;

use App\Models\AvailableCampaignByLocation;
use Illuminate\Console\Command;

class ClearAvailableCampaignByLocationTable extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:clear-available-campaign-by-location-table';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clears the available_campaign_by_locations table.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        AvailableCampaignByLocation::truncate();
    }
}
