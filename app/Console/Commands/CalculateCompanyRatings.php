<?php

namespace App\Console\Commands;

use App\Jobs\CalculateCompanyRatingsJob;
use App\Models\ConsumerReviews\Review;
use App\Models\Odin\Company;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Symfony\Component\Console\Output\OutputInterface;

class CalculateCompanyRatings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:calculate-company-ratings
        {--auto : Calculate for all companies in the reviews table}
        {--all : Calculate company review scores for all companies}
        {--company-ids=none : Calculate review scores for a comma seperated list of company ids (ex --company-ids=1,3,5,7,9)}
        {--batch-size=500 : Max number of companies passed to each calculate job created}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Calculate overall company ratings from a Bayesian weighted average of current '
        .' reviews.';

    /**
     * Execute the console command.
     */
    public function handle() : void
    {
        $companyIds = [];
        $companyIdsString = $this->option('company-ids');
        $allCompanies = $this->option('all');
        $batchSize = $this->option('batch-size');
        $count = 0;
        $auto = $this->option('auto');

        if ($auto) {
            $query = DB::table(Company::TABLE)
                ->join(Review::TABLE, Review::TABLE .'.'. Review::FIELD_COMPANY_ID, '=', Company::TABLE .'.'. Company::FIELD_ID)
                ->select(Company::TABLE .'.'. Company::FIELD_ID)
                ->distinct(Company::TABLE .'.'. Company::FIELD_ID);
        }
        else {
            $query = Company::query()
                ->select([Company::FIELD_ID, Company::FIELD_NAME]);
            // Check for both or no arguments given
            if ($allCompanies && $companyIdsString != 'none') {
                $this->error("Cannot pass --all and --companyIds");
                return;
            } else if (!$allCompanies && $companyIdsString == 'none') {
                $this->error("Must pass either --all or --companyIds=1,2,3...");
                return;
            }

            // Check for companyId values
            if ($companyIdsString != 'none') {
                $companyIds = array_map('intval', explode(',', $companyIdsString));
            }
            // Check for bad values in companyIds list
            if (in_array(0, $companyIds)) {
                $this->error("companyIds can only contain integers.");
                return;
            }

            // Get query depending on option
            if ($allCompanies) {
                $this->info("Updating Company Consumer Ratings for All Companies.\n", OutputInterface::VERBOSITY_NORMAL);
                $query = $this->getQueryAllCompanies($query);
            }
            if ($companyIds) {
                $this->info("Updating Metrics for Company Ids: " . json_encode($companyIds) . "\n", OutputInterface::VERBOSITY_NORMAL);
                $query = $this->getQueryCompanyIds($query, $companyIds);
            }
        }

        // Setup for info printing
        $totalCompanies = $query->count();

        $chunks = $totalCompanies/$batchSize;
        $this->line("$totalCompanies companies with reviews found");
        $bar = $this->output->createProgressBar($chunks);
        $bar->start();
        $totalCompaniesCollection = new Collection();

        // Calculate average for selected companies
        $query->orderBy(Company::TABLE .'.'. Company::FIELD_ID)
            ->chunkById($batchSize, function (Collection $companies) use (&$count, $bar, &$totalCompaniesCollection) {
                CalculateCompanyRatingsJob::dispatchSync($companies->pluck(Company::FIELD_ID)->toArray());
                $count += $companies->count();
                $bar->advance();
                $totalCompaniesCollection = $totalCompaniesCollection->merge($companies);
            }, Company::TABLE .'.'. Company::FIELD_ID, Company::FIELD_ID);

        // Print details
        $bar->finish();
        $this->info("\n\nCalculated Consumer Rating average for $count Companies.", OutputInterface::VERBOSITY_NORMAL);
        // Company list only printed if -v verbose flag is passed
        $this->info("\nCompanies:\n", OutputInterface::VERBOSITY_VERBOSE);
        foreach ($totalCompaniesCollection as $company) {
            $this->info('(id '.$company->id.')', OutputInterface::VERBOSITY_VERBOSE);
        }
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    protected function getQueryAllCompanies(Builder $query): Builder
    {
        return $query;
    }

    /**
     * @param Builder $query
     * @param array $companyIds
     * @return Builder
     */
    protected function getQueryCompanyIds(Builder $query, array $companyIds): Builder
    {
        return $query->whereIn(Company::FIELD_ID, $companyIds);
    }
}
