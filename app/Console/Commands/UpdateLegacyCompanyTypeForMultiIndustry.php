<?php

namespace App\Console\Commands;

use App\Models\Odin\Company;
use App\Models\Odin\Industry;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Models\Odin\IndustryService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class UpdateLegacyCompanyTypeForMultiIndustry extends Command
{
    const VALID_COMPANY_STATUSES = [
        Company::STATUS_REGISTERING,
        Company::STATUS_PENDING_APPROVAL,
        Company::STATUS_LEADS_OFF_NEVER_PURCHASED
    ];

    const INVALID_INDUSTRIES = [
        IndustryEnum::SOLAR,
        IndustryEnum::ROOFING,
    ];

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:legacy-company-type-multi';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "Update EloquentCompany models to type => 'multi' for any existing Fixr-created Companies";

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $this->line("\n\nUpdating legacy EloquentCompany->type for multi-industry Companies...");

        $statusStrings = implode(', ', array_map(fn(int $v) => Company::COMPANY_STATUS_MAPPING[$v], self::VALID_COMPANY_STATUSES));
        $industryMap = array_map(fn($enum) => $enum->value, self::INVALID_INDUSTRIES);
        $industryStrings = implode(', ', $industryMap);

        $this->line("\tExcluding Company Statuses: $statusStrings");
        $this->line("\tExcluding Industries: $industryStrings");

        $ignoreIndustryServiceIds = IndustryService::query()
            ->with(IndustryService::RELATION_INDUSTRY)
            ->whereHas(IndustryService::RELATION_INDUSTRY, fn(Builder $query) =>
                $query->whereIn(Industry::FIELD_NAME, $industryMap)
            )->pluck(IndustryService::FIELD_ID);

        $companyIds = Company::query()
            ->has(Company::RELATION_CONTRACTS)
            ->with(Company::RELATION_SERVICES)
            ->whereIn(Company::FIELD_STATUS, self::VALID_COMPANY_STATUSES)
            ->whereHas(Company::RELATION_SERVICES, fn(Builder $query) =>
                $query->whereNotIn(IndustryService::TABLE .'.'. IndustryService::FIELD_ID, $ignoreIndustryServiceIds)
            )->pluck(Company::FIELD_LEGACY_ID);
        $companyCount = $companyIds->count();

        $this->line("\n\tFound $companyCount multi-industry companies to update");

        $filteredIds = $companyIds->filter();
        $badIds = $companyCount - $filteredIds->count();
        if ($badIds > 0) {
            $this->warn("\n\tWARNING: $badIds multi-industry companies had invalid legacy ID's and will not be synced.");
        }

        if ($filteredIds->count() > 0) {
            $legacyIds = $filteredIds->join(',');
            $rowsUpdated = DB::connection('readonly')->update("UPDATE tblcompany SET type = 'multi' WHERE companyid IN ($legacyIds)");

            $this->info("\n\t$rowsUpdated rows were updated.");
        }

        $this->line("\nCommand completed.\n");

        return 0;
    }
}
