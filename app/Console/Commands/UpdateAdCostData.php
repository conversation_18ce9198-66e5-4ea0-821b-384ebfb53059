<?php

namespace App\Console\Commands;

use App\Enums\Advertising\Advertiser;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Enums\Advertising\AdvertisingResolution;
use App\Enums\Odin\Industry;
use App\Models\Odin\Industry as IndustryModel;
use App\Jobs\Advertising\UpdateAdCostDataJob;
use App\Jobs\Advertising\UpdateGoogleAdsGeoTargetsJob;
use App\Jobs\Advertising\UpdateMetaLocationsJob;
use App\Models\DailyAdCost;
use App\Models\Legacy\Location;
use App\Services\Advertising\AdvertisingCostService;
use App\Services\DatabaseHelperService;
use App\Services\QueueHelperService;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Exception;
use Illuminate\Bus\Batch;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Symfony\Component\Console\Output\OutputInterface;
use Throwable;

class UpdateAdCostData extends Command
{
    // Maximum days of data the command will retrieve if no start date is given
    const int MAX_DEFAULT_DAYS = 7;

    private Carbon $startDate;
    private Carbon $endDate;
    private array $resolutions;
    private array $industries;
    private array $adSources;
    private array $advertisers;
    private ?string $filePath;
    private bool $noDatabase;
    private bool $hardClear;
    private bool $noClear;
    private bool $googleLocations;
    private bool $metaLocations;
    private bool $progressReport;
    private int $batchSize;
    private array $batchPeriods;
    private AdvertisingCostService $advertisingCostService;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:ad-cost-data
        {--start-date= : (YYYY-MM-DD) Optional, if not given will default at latest missing date from daily_ad_costs table (max of 7 days back if nothing found).}
        {--end-date= : (YYYY-MM-DD) Optional, if not given will default to today.}
        {--resolutions=state,county : Comma separated list [options: state,county], If not given will default to state and county data.}
        {--industries=all : Comma separated list of industry slugs [options: solar,roofing,hvac,...], If not given will default to all industries with ad data.}
        {--ad-sources=google_ads,meta_ads,microsoft_ads : Comma separated list [options: google_ads,meta_ads,microsoft_ads], If not given will default to google, meta, and microsoft data.}
        {--advertisers=all : Comma separated list [options: gabe,wade,will,thomas,gabe_new,andy,harvey], If not given will default to all advertisers.}
        {--file-path= : Optional, Data will be saved as a CSV at the filepath if one is given, DB will also be updated if --no-db is not given.}
        {--d|no-db : Optional, Database will not be updated if this flag is given, only useful if --file-path is given.}
        {--f|force : If this flag is given the command will not ask for confirmation before erasing existing contents of given filepath if it is not empty.}
        {--p|progress : Optional, if given the command will report progress as jobs are executed. If not given command will dispatch jobs and terminate.}
        {--g|google-locations : Flag to dispatch job to update google geo targets.}
        {--m|meta-locations : Flag to dispatch job to update meta locations.}
        {--inline : Flag to retrieve the data within the command}
        {--hard-clear : Clear all daily_ad_cost data over the given date range before entering new data}
        {--clear-section : Clear all daily_ad_cost data over the given date range that matches selected parameters before entering new data}
        {--no-clear : Insert new data without clearing any existing ad data entries with the same values}
        {--batch-size=day : Cmd will split date period into given number of jobs for each ad data source (\'day\' can be given to create a job for each day worth of data).}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Retrieve and store daily ad cost information from advertiser APIs into the daily_ad_costs '.
                             'table. Data is stored as ad spend by day per platform/advertiser/industry/county/state. '.
                             'County level data is provided by google but estimated for Meta and Microsoft ad spend data, '.
                             'as they only report at the state level. Dates and type of data retrieved can be configured with the options listed below. '.
                             'The retrieved data can be downloaded as a CSV by setting the file path with --filepath=. '.
                             'Data can be downloaded to a CSV without updating the daily_ad_costs table if the --no-db flag is given.'.
                             'If no options are given with the command, it will retrieve all ad cost data from the latest date '.
                             'in the daily_ad_costs table.';

    /**
     * @param AdvertisingCostService $advertisingCostService
     */
    public function __construct(AdvertisingCostService $advertisingCostService)
    {
        parent::__construct();
        $this->advertisingCostService = $advertisingCostService;
    }

    /**
     * Execute the console command.
     * @throws Exception
     * @throws Throwable
     */
    public function handle(): void
    {
        if (!$this->parseArguments())
            exit(1);

        if ($this->updateLocationTargets())
            return;

        $this->headerMessage();

        if (!$this->handleFile())
            return;

        // Performance (only effective when --progress is passed)
        DB::disableQueryLog();

        if (in_array(AdvertisingPlatform::GOOGLE, $this->adSources)) {
            $this->getAdvertiserData(AdvertisingPlatform::GOOGLE);
        }
        if (in_array(AdvertisingPlatform::META, $this->adSources)) {
            $this->getAdvertiserData(AdvertisingPlatform::META);
        }
        if (in_array(AdvertisingPlatform::MICROSOFT, $this->adSources)) {
            $this->getAdvertiserData(AdvertisingPlatform::MICROSOFT);
        }

        DB::enableQueryLog();
    }

    /**
     * @param AdvertisingPlatform $adSource
     * @return void
     * @throws Throwable
     */
    protected function getAdvertiserData(AdvertisingPlatform $adSource): void
    {
        $this->line('');
        $this->info("Retrieving ".AdvertisingPlatform::displayName($adSource->value)." Ad Data");

        if ($this->option('inline')) {
            $this->getDataInline($adSource);
            return;
        }

        $batchJobs = [];
        foreach ($this->batchPeriods as $period) {
            $batchJobs[] = new UpdateAdCostDataJob(
                period: $period,
                adSource: $adSource,
                resolutions: $this->resolutions,
                industries: $this->industries,
                advertisers: $this->advertisers,
                filePath: $this->filePath,
                noDb: $this->noDatabase,
                noDelete: $this->noClear);
        }

        $batch = Bus::batch($batchJobs)
            ->catch(function(Batch $batch, Throwable $throwable) {
                logger()->error("Ad cost data retrieval error: ".$throwable->getMessage());
            })
            ->allowFailures()
            ->name("Ad Cost Update")
            ->onQueue(QueueHelperService::QUEUE_NAME_LONG_RUNNING)
            ->dispatch();

        if ($this->progressReport || $this->filePath) {
            $batchPeriodIndex = 0;
            $currentPeriod = $this->batchPeriods[$batchPeriodIndex];

            $bar = $this->output->createProgressBar($batch->totalJobs);

            $bar->setFormat("%bar% %percent%% \n%current%/%max% " . AdvertisingPlatform::displayName($adSource->value) .
                " jobs done. (\033[32m%successJobs% Successful\033[0m, \033[31m %failedJobs% Failed\033[0m) ".
                "\nElapsed: %elapsed% \nEst. Remaining: %remaining% \nCurrent Job Date Range: %startDate% to %endDate%\nMemory Usage: %memory%\n");

            $bar->setMessage($currentPeriod->getStartDate()->format('Y-m-d'), 'startDate');
            $bar->setMessage($currentPeriod->getEndDate()->format('Y-m-d'), 'endDate');
            $bar->setMessage(0, 'successJobs');
            $bar->setMessage(0, 'failedJobs');

            $bar->setRedrawFrequency(1);

            $bar->start();

            $finished = false;
            $failedJobs = 0;
            $failedDateRanges = [];

            while (!$finished) {
                $batchModel = DB::table('job_batches')
                    ->where('id', $batch->id)->first();
                if ($batchModel->failed_jobs != $failedJobs)
                    $failedDateRanges[] = $currentPeriod;
                $totalJobs = $batchModel->total_jobs + $batchModel->failed_jobs;
                $pendingJobs = $batchModel->pending_jobs;
                $processedJobsCount = $totalJobs - $pendingJobs;
                $bar->setProgress($processedJobsCount);

                if ($processedJobsCount < count($this->batchPeriods)) {
                    $currentPeriod = $this->batchPeriods[$processedJobsCount];
                    $bar->setMessage($currentPeriod->getStartDate()->format('Y-m-d'), 'startDate');
                    $bar->setMessage($currentPeriod->getEndDate()->format('Y-m-d'), 'endDate');
                }

                $failedJobs = $batchModel->failed_jobs;
                $successfulJobs = $processedJobsCount - $failedJobs;

                $finished = !(count($this->batchPeriods) - $failedJobs - $successfulJobs);

                $bar->setMessage($successfulJobs, 'successJobs');
                $bar->setMessage($failedJobs, 'failedJobs');

                sleep(1);
            }

            $totalJobs = count($this->batchPeriods);
            $completedJobs = $totalJobs - $failedJobs;

            $bar->finish();
            $this->newLine();
            $this->info("{$completedJobs}/{$totalJobs} Jobs Completed Successfully.");
            if ($failedJobs) {
                $this->line("\033[31m{$failedJobs}/{$totalJobs} Jobs Failed. Failed Date Ranges:\033[0m");
                foreach ($failedDateRanges as $failedDateRange) {
                    $this->line("    \033[31m[{$failedDateRange->getStartDate()->format('Y-m-d')} - {$failedDateRange->getEndDate()->format('Y-m-d')}]\033[0m");
                }
            }
            $this->info(AdvertisingPlatform::displayName($adSource->value) . " Ad data retrieval batch complete.");
            $this->newLine();
        }
    }

    /**
     * @return bool
     * @throws Exception
     */
    protected function parseArguments(): bool
    {
        $this->filePath = $this->option('file-path');
        $this->noDatabase = $this->option('no-db');
        $this->googleLocations = $this->option('google-locations');
        $this->metaLocations = $this->option('meta-locations');
        $this->progressReport = $this->option('progress');
        $this->noClear = $this->option('no-clear');

        // Add all industries if all default given
        if ($this->option('industries') === 'all') {
            $industryStrings = array_keys(Industry::slugsList());
        } else {
            $industryStrings = explode(',', $this->option('industries'));
        }

        if ($this->option('advertisers') === 'all') {
            $this->advertisers = Advertiser::cases();
            $advertiserStrings = null;
        } else {
            $advertiserStrings = explode(',', $this->option('advertisers'));
        }

        $resolutionStrings = explode(',', $this->option('resolutions'));
        $adSourceStrings = explode(',', $this->option('ad-sources'));

        // Convert industry, resolution, ad source, advertiser to enumerators and confirm exist
        try {
            $this->adSources = array_map(function ($key) {return AdvertisingPlatform::fromKey($key);}, $adSourceStrings);
            $this->industries = array_map(function ($key) {return Industry::fromSlug($key);}, $industryStrings);
            $this->resolutions = array_map(function ($key) {return AdvertisingResolution::fromKey($key);}, $resolutionStrings);
            if ($advertiserStrings)
                $this->advertisers = array_map(function ($key) {return Advertiser::fromKey($key);}, $advertiserStrings);
        } catch (Exception $e) {
            $this->error("Failed to parse arguments. error: ".$e->getMessage());
            return false;
        }

        // Get Start Date if none given
        if (!$this->option('start-date')) {
            $latestDailyCost = DailyAdCost::latest(DailyAdCost::FIELD_DATE)->first();
            if ($latestDailyCost) {
                $this->startDate = Carbon::parse($latestDailyCost->{DailyAdCost::FIELD_DATE})->addDays(1);
                if ($this->startDate->isToday() || $this->startDate->isFuture()) {
                    $this->info("Daily Ad Costs table is up to date, choose a manual start date to re-fetch Daily Ad Cost data.");
                    return false;
                }
                $this->info("No start date given, using ".$this->startDate." from last entry in daily_ad_costs.");
            } else {
                $this->startDate = Carbon::now()->subDays(self::MAX_DEFAULT_DAYS);
                $this->info("No start date given, using ".$this->startDate);
            }
        } else {
            $this->startDate = Carbon::parse($this->option('start-date'));
        }

        // Get end date if none given
        if (!$this->option('end-date')) {
            $this->endDate = Carbon::now()->subDays(1);
        } else {
            $this->endDate = Carbon::parse($this->option('end-date'));
        }

        // Check End date is not before start date
        if ($this->startDate->gt($this->endDate)) {
            $this->error('Start date more recent than end date');
            return false;
        }

        // Check for hard clear argument
        if ($this->option('hard-clear')) {
            $this->hardClearRange($this->startDate, $this->endDate);
        }

        if ($this->option('clear-section')) {
            $this->clearSectionRange();
        }

        // Check No DB was not given without filepath
        if ($this->noDatabase && !$this->filePath) {
            $this->error("Passing no database without a filepath doesn't do anything, no file and no data changed");
            return false;
        }

        // Job for each day if day is given, otherwise number of jobs defined
        if ($this->option('batch-size') === 'day') {
            $this->batchSize = $this->startDate->diffInDays($this->endDate) + 1;
        } else {
            $this->batchSize = $this->option('batch-size');
        }

        // Split date period into number of requested jobs
        $this->batchPeriods = $this->splitDatePeriod($this->startDate, $this->endDate, $this->batchSize);

        return true;
    }

    /**
     * @param Carbon $start
     * @param Carbon $end
     * @param int $parts
     * @return array
     */
    function splitDatePeriod(Carbon $start, Carbon $end, int $parts): array
    {
        // Split given date period into given number of parts
        $totalDays = $start->diffInDays($end) + 1;
        $partSize = ceil($totalDays / $parts);

        $periods = [];
        for ($i = 0; $i < $parts; $i++) {
            $currentStart = $start->copy()->addDays($i * $partSize);
            $currentEnd = $currentStart->copy()->addDays($partSize - 1);
            if ($currentEnd > $end)
                $currentEnd = $end;
            $periods[] = CarbonPeriod::create($currentStart, $currentEnd);
            if ($currentEnd == $end)
                break;
        }

        return $periods;
    }

    /**
     * @return bool
     */
    protected function updateLocationTargets(): bool
    {
        $updatedTargets = false;
        if ($this->googleLocations) {
            UpdateGoogleAdsGeoTargetsJob::dispatchSync(force: true);
            $updatedTargets = true;
        }
        if ($this->metaLocations) {
            UpdateMetaLocationsJob::dispatchSync(force: true);
            $updatedTargets = true;
        }
        return $updatedTargets;
    }

    /**
     * @return void
     */
    function headerMessage(): void
    {
        $this->info("Retrieving Daily Ad Cost Data from {$this->startDate->format('Y-m-d')} to {$this->endDate->format('Y-m-d')}.");
        if ($this->output->getVerbosity() === OutputInterface::VERBOSITY_VERBOSE) {
            $this->line("    Industries: " . implode(',', array_map(function ($industry) {
                    return $industry->value;
                }, $this->industries)));
            $this->line("    Resolutions: " . implode(',', array_map(function ($resolution) {
                    return $resolution->value;
                }, $this->resolutions)));
            $this->line("    Advertisers: " . implode(',', array_map(function ($advertiser) {
                    return $advertiser->getDisplayName();
                }, $this->advertisers)));
            $this->line("    Ad Sources: " . implode(',', array_map(function ($adSource) {
                    return $adSource->value;
                }, $this->adSources)));
            $this->line("    Jobs for each Ad Source: " . $this->batchSize);
            $this->line("    Total Jobs: " . ($this->batchSize * count($this->advertisers)));
            $this->newLine();
        }
    }

    /**
     * @return bool
     * @throws Exception
     */
    function handleFile(): bool
    {
        if (!$this->filePath)
            return true;

        if (!$this->createFile())
            return false;

        $this->advertisingCostService->addCostDataCsvHeader(
            $this->filePath,
            $this->startDate,
            $this->endDate,
            $this->industries,
            $this->resolutions,
            $this->advertisers,
            $this->adSources,
        );

        return true;
    }

    /**
     * @return bool
     */
    function createFile(): bool
    {
        if (!file_exists($this->filePath)) {
            // Create the file if it doesn't exist
            file_put_contents($this->filePath, '');
            $this->info("Created file at: {$this->filePath}");
        } else {
            // If the file exists, get its size
            $fileSize = filesize($this->filePath);
            if ($fileSize !== 0) {
                $this->warn("Warning: The file at {$this->filePath} is not empty, continuing this operation will erase existing contents of the file.");

                // Check if the --force option is used
                if (!$this->option('force')) {
                    // Ask the user for confirmation
                    if (!$this->confirm('Do you wish to continue?')) {
                        $this->info('Operation aborted, file not changed.');
                        return false;
                    }
                }

                $this->info("Overwriting file at: {$this->filePath}");
                file_put_contents($this->filePath, '');
                return true;
            }
            $this->info("Writing to file at: {$this->filePath}");
        }
        return true;
    }

    /**
     * @param AdvertisingPlatform $adSource
     * @return void
     * @throws Throwable
     */
    protected function getDataInline(AdvertisingPlatform $adSource): void
    {
        $this->info("Inline Ad Cost Retrieval");
        foreach ($this->batchPeriods as $period) {
            $this->info("Getting Ad Cost {$period->getStartDate()->format('Y-m-d')} to {$period->getEndDate()->format('Y-m-d')}");
            $this->advertisingCostService->getDailyAdCostData(
                period: $period,
                adSource: $adSource,
                resolutions: $this->resolutions,
                industries: $this->industries,
                advertisers: $this->advertisers,
                filePath: $this->filePath,
                noDb: $this->noDatabase,
                noDelete: $this->noClear);
        }
    }

    /**
     * @param Carbon $start
     * @param Carbon $end
     * @return void
     */
    protected function hardClearRange(Carbon $start, Carbon $end): void
    {
        if ($this->confirm("Are you sure you want to clear all Daily Ad Cost data from {$start->format('Y-m-d')} to {$end->format('Y-m-d')} before re-fetching?")) {
            DB::table(DailyAdCost::TABLE)
                ->where(DailyAdCost::FIELD_DATE, '>=', $start->format('Y-m-d'))
                ->where(DailyAdCost::FIELD_DATE, '<=', $end->format('Y-m-d'))
                ->delete();
            $this->info("Ad Cost data cleared from {$start->format('Y-m-d')} to {$end->format('Y-m-d')}");
        } else {
            $this->info("No Ad Cost data cleared before fetch.");
        }
    }

    /**
     * Clearing all Ad Cost Data that matches the given parameters, use when locations are messed up for a specific platform
     * @return void
     * @throws Exception
     */
    protected function clearSectionRange(): void
    {
        $industryIds = IndustryModel::query()
            ->select(IndustryModel::FIELD_ID)
            ->whereIn(IndustryModel::FIELD_SLUG, array_map(function ($industry) {return $industry->getSlug();}, $this->industries))
            ->get()->toArray();

        DB::table(DailyAdCost::TABLE)
            ->where(DailyAdCost::FIELD_DATE, '>=', $this->startDate->format('Y-m-d'))
            ->where(DailyAdCost::FIELD_DATE, '<=', $this->endDate->format('Y-m-d'))
            ->whereIn(DailyAdCost::FIELD_INDUSTRY_ID, $industryIds)
            ->whereIn(DailyAdCost::FIELD_PLATFORM, array_map(function ($adSource) {return AdvertisingPlatform::getInteger($adSource->value);}, $this->adSources))
            ->whereIn(DailyAdCost::FIELD_ADVERTISER, array_map(function ($advertiser) {return $advertiser->value;}, $this->advertisers))
            ->whereExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from(Location::TABLE)
                    ->whereColumn(Location::TABLE.'.'.Location::ID, DailyAdCost::TABLE.'.'.DailyAdCost::FIELD_LOCATION_ID)
                    ->whereIn(Location::TYPE, array_map(function ($resolution) {return $resolution->locationType();}, $this->resolutions));
            })
            ->delete();
        $this->info("Ad Cost data cleared from {$this->startDate->format('Y-m-d')} to {$this->endDate->format('Y-m-d')}");
    }
}
