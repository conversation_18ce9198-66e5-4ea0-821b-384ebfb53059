<?php

namespace App\Console\Commands;

use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Odin\Company;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use App\Models\SaleType;
use App\Services\DatabaseHelperService;
use Illuminate\Console\Command;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\SaleTypes as SaleTypeEnum;
use Illuminate\Support\Facades\DB;

class RestoreDeletedMultiIndustryQuoteCompanies extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'restore:missing-quote-companies';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    protected array $saleTypeMap;

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $industryIds = Industry::query()
            ->where(Industry::FIELD_SLUG, [IndustryEnum::SOLAR->getSlug(), IndustryEnum::ROOFING->getSlug()])
            ->pluck(Industry::FIELD_ID);

        $productAssignments = ProductAssignment::query()
            ->leftJoin(DatabaseHelperService::readOnlyDatabase() .'.'. EloquentQuoteCompany::TABLE, EloquentQuoteCompany::TABLE .'.'. EloquentQuoteCompany::ID, '=', ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_LEGACY_ID)
            ->join(ConsumerProduct::TABLE, ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ID, '=', ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_CONSUMER_PRODUCT_ID)
            ->join(Consumer::TABLE, Consumer::TABLE .'.'. Consumer::FIELD_ID, '=', ConsumerProduct::FIELD_CONSUMER_ID)
            ->join(ServiceProduct::TABLE, ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_ID, '=', ConsumerProduct::FIELD_SERVICE_PRODUCT_ID)
            ->join(IndustryService::TABLE, IndustryService::TABLE .'.'. IndustryService::FIELD_ID, '=', ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_INDUSTRY_SERVICE_ID)
            ->join(Company::TABLE, Company::TABLE .'.'. Company::FIELD_ID, '=', ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_COMPANY_ID)
            ->whereNotIn(IndustryService::TABLE .'.'. IndustryService::FIELD_INDUSTRY_ID, $industryIds)
            ->whereNull(EloquentQuoteCompany::TABLE .'.'. EloquentQuoteCompany::ID)
            ->where(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_CHARGEABLE, true)
            ->where(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_COMPANY_ID, 39)
            ->where(ProductAssignment::TABLE .'.'. ProductAssignment::CREATED_AT, '>', now()->subDays(1))
            ->select(ProductAssignment::TABLE .'.*')
            ->selectRaw(Consumer::TABLE .'.'. Consumer::FIELD_LEGACY_ID . " as legacy_quote_id")
            ->selectRaw(Company::TABLE .'.'. Company::FIELD_LEGACY_ID . " as legacy_company_id")
            ->get();

        $saleTypes = SaleType::all();
        $this->saleTypeMap = $saleTypes->mapWithKeys(fn($saleType) => [
            $saleType->id => SaleTypeEnum::mapSaleTypeToLegacyId(SaleTypeEnum::tryFrom($saleType->name))
        ])->toArray();

        $quoteCompanies = $productAssignments->map(fn($productAssignment) => $this->transformProductAssignment($productAssignment));
        $quoteToProductAssignmentMap = $productAssignments->mapWithKeys(fn($productAssignment) => [
            $productAssignment->legacy_quote_id   => $productAssignment->id
        ]);

        DB::table(DatabaseHelperService::readOnlyDatabase() .'.'. EloquentQuoteCompany::TABLE)
            ->insert($quoteCompanies->toArray());

        $quoteCompanies->each(function($quoteCompany) use (&$quoteToProductAssignmentMap) {
            $newQuoteCompany = EloquentQuoteCompany::query()
                ->where(EloquentQuoteCompany::COMPANY_ID, $quoteCompany[EloquentQuoteCompany::COMPANY_ID])
                ->where(EloquentQuoteCompany::QUOTE_ID, $quoteCompany[EloquentQuoteCompany::QUOTE_ID])
                ->first();

            $productAssignmentId = $quoteToProductAssignmentMap[$newQuoteCompany->{EloquentQuoteCompany::QUOTE_ID}];

            ProductAssignment::query()
                ->find($productAssignmentId)
                ->update([ProductAssignment::FIELD_LEGACY_ID => $newQuoteCompany->{EloquentQuoteCompany::ID}]);

        });

        return 0;
    }

    protected function transformProductAssignment(ProductAssignment $productAssignment): array
    {
        $assignmentPayload = $productAssignment->payload;
        $exclusiveSaleTypeId = $this->saleTypeMap[SaleType::query()->where(SaleType::FIELD_KEY, 'exclusive')->first()->id];

        return [
            EloquentQuoteCompany::QUOTE_ID                  => $productAssignment->legacy_quote_id,
            EloquentQuoteCompany::COMPANY_ID                => $productAssignment->legacy_company_id,
            EloquentQuoteCompany::CHARGEABLE                => $productAssignment->chargeable,
            EloquentQuoteCompany::DELIVERED                 => $productAssignment->delivered ?? true,
            EloquentQuoteCompany::COST                      => $productAssignment->cost,
            EloquentQuoteCompany::CHARGE_STATUS             => $assignmentPayload['charge_status'] ?? "initial",
            EloquentQuoteCompany::SOLD_STATUS               => $assignmentPayload['sold_status'] ?? "sold",
            EloquentQuoteCompany::INVOICE_ITEM_ID           => 0,
            'saletype'                                      => $productAssignment->sale_type_id === $exclusiveSaleTypeId ? 'exclusive' : 'multple',
            EloquentQuoteCompany::INCLUDE_IN_BUDGET         => $assignmentPayload['incl_in_budget'] ?? true,
            EloquentQuoteCompany::NON_BUDGET_PREMIUM_LEAD   => $assignmentPayload['non_budget_premium_lead'] ?? 0,
            EloquentQuoteCompany::TIMESTAMP_ADDED           => $productAssignment->created_at->timestamp,
            EloquentQuoteCompany::LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION_ID     => $assignmentPayload['lead_campaign_sales_type_configuration_id'],
        ];
    }
}
