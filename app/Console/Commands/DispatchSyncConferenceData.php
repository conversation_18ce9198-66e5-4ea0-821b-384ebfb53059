<?php

namespace App\Console\Commands;

use App\Jobs\SyncConferenceData;
use App\Models\Calendar\CalendarEvent;
use Illuminate\Console\Command;

class DispatchSyncConferenceData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dispatch-sync-conference-data {--calendar_event_id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Dispatch job to sync event conference data';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $calendarEventId = $this->option('calendar_event_id');

        $calendarEvent = CalendarEvent::query()->find($calendarEventId);

        if (!$calendarEvent) {
            $this->error('Calendar Event not found');
            return;
        }

        SyncConferenceData::dispatch($calendarEvent);

        $this->info('Job dispatched...');
    }
}
