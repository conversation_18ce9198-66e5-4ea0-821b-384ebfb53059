<?php

namespace App\Console\Commands;

use App\Models\Odin\Address;
use App\Models\Odin\CompanyLocation;
use App\Repositories\Legacy\ZipCodeSetRepository;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Exception;

class AddUtcToCompanyLocations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'add-utc-to-company-locations';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Calculate and store utc offsets for the missing companies locations.';

    /**
     * Execute the console command.
     *
     * @param ZipCodeSetRepository $zipCodeSetRepository
     * @return void
     */
    public function handle(ZipCodeSetRepository $zipCodeSetRepository): void
    {
        $this->info("Initiating the processing.");

        $this->getQuery()
             ->chunk(100, function (Collection $addresses) use($zipCodeSetRepository) {
                 /** @var Address $address */
                 foreach ($addresses as $address) {
                     try {
                         $zipcode = $address->{Address::FIELD_ZIP_CODE};

                         if (strlen(trim($zipcode)) > 0) {
                             $address->{Address::FIELD_UTC} = $zipCodeSetRepository->getUtc($zipcode);
                             $address->save();
                         } else {
                             $this->error("Zipcode doesn't exist for Address: {$address->{Address::FIELD_ID}}");
                         }
                     } catch (Exception $exc) {
                         $this->alert("Error: {$exc->getMessage()}");
                     }
                 }
             });

        $this->info("Finished");
    }

    /**
     * Handles preparing query to fetch addresses against the company locations missing their utc offsets.
     *
     * @return Builder
     */
    protected function getQuery(): Builder
    {
        $query = Address::query();

        $query
            ->select(Address::TABLE . '.*')
            ->join(CompanyLocation::TABLE, function($join) {
                /** @var JoinClause $join */
                $join->on(CompanyLocation::TABLE . '.' . CompanyLocation::FIELD_ADDRESS_ID,
                    '=',
                    Address::TABLE . '.' . Address::FIELD_ID
                );
            })
            ->where(function($subQuery) {
                /** @var Builder $subQuery */
                $subQuery
                    ->where(Address::FIELD_UTC, '=', '')
                    ->orWhereNull(Address::FIELD_UTC);
            });

        return $query;
    }
}
