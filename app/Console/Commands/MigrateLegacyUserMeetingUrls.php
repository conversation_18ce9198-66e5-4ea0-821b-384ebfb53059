<?php

namespace App\Console\Commands;

use App\Models\Legacy\EloquentUser;
use App\Models\User;
use Illuminate\Console\Command;
use function array_filter;

class MigrateLegacyUserMeetingUrls extends Command
{
    const OPTION_FORCE = 'force';
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'legacy-migrate:meeting-urls {--force : update User model even if it contains a value }';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate legacy meeting URLs from EloquentUser to User';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $force = !!$this->option(self::OPTION_FORCE);
        $this->info("\nMigrating Meeting URLs from EloquentUsers to Users...");
        if ($force) {
            $this->info("\n\t*** Force option supplied - User models will be updated regardless of current value. ***");
        }

        $usersUpdated = 0;

        $idMapping = User::all()->reduce(function(array $output, User $user) {
            if ($user->{User::FIELD_LEGACY_USER_ID} > 0) {
                $output[$user->{User::FIELD_ID}] = $user->{User::FIELD_LEGACY_USER_ID};
            }
            return $output;
        }, []);

        $eloquentUsers = EloquentUser::query()
            ->whereIn(EloquentUser::ID, array_values($idMapping))
            ->where(EloquentUser::MEETING_URL, '!=', '')
            ->whereNotNull(EloquentUser::MEETING_URL)
            ->get();

        $this->line("\n{$eloquentUsers->count()} EloquentUsers found with valid meeting URL fields and a linked User");

        $eloquentUsers->each(function(EloquentUser $eloquentUser) use ($idMapping, &$usersUpdated, $force) {
            $targetUserIds = array_filter($idMapping, fn($legacyId) => $legacyId === $eloquentUser->{EloquentUser::ID} );
            foreach ($targetUserIds as $id => $legacyId) {
                $targetUser = User::query()->find($id);
                if ($targetUser && ($force || !$targetUser->{User::FIELD_MEETING_URL})) {
                    $targetUser->update([User::FIELD_MEETING_URL => $eloquentUser->{EloquentUser::MEETING_URL}]);
                    $usersUpdated++;
                }
            }
        });

        $this->info("\n$usersUpdated Users were updated from Legacy.\n");

        return $usersUpdated ? 1 : 0;
    }
}
