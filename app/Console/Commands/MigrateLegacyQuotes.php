<?php

namespace App\Console\Commands;

use App\Jobs\LegacyMigrations\MigrateLegacyQuotesJob;
use App\Models\Legacy\EloquentQuote;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ServiceProduct;
use App\Models\Odin\Website;
use App\Services\DatabaseHelperService;
use App\Services\QueueHelperService;
use Carbon\CarbonTimeZone;
use Illuminate\Bus\Batch;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Illuminate\Console\Command;
use Throwable;

class MigrateLegacyQuotes extends Command
{
    const OPT_BATCH_JOB_SIZE = 'batch-job-size';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'legacy-migrate:quotes {--batch-job-size=1000: The number of quotes each batch job should handle}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handles the migration of legacy quotes to their new schema.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $quotesInput           = $this->ask('Quote Ids to migrate/sync (comma seperated or press enter to sync all missing ones).');
        $customQuotesToMigrate = [];

        if(trim($quotesInput)) {
            $customQuotesToMigrate = array_unique(explode(',', trim($quotesInput)));
            $customQuotesCount     = count($customQuotesToMigrate);

            if($customQuotesCount) {
                $this->info("Migrating/syncing the given quote ID" . ($customQuotesCount > 1 ? 's' : '') . " to Admin2.");
            }
        }
        else {
            $this->info("Migrating/syncing all missing quotes to Admin2.");
        }

        // Performance
        DB::disableQueryLog();

        $quoteIdCol = EloquentQuote::ID;
        $countCol = 'count';

        $totalQuotes = EloquentQuote::query()
            ->count();

        $totalMigrated = EloquentQuote::query()
            ->join(DatabaseHelperService::database() . '.' . Consumer::TABLE, DatabaseHelperService::database() . '.' . Consumer::TABLE . '.' . Consumer::FIELD_LEGACY_ID, '=' , EloquentQuote::TABLE . '.' . EloquentQuote::ID)
            ->count();

        $total = $customQuotesCount ?? $totalQuotes - $totalMigrated;

        if($total > 0) {
            $this->info("Quotes total: $total");

            $mayFirstTwoThousandTwentyTwo = Carbon::createFromDate(2022, 5, 1, new CarbonTimeZone('UTC'))->timestamp;
            $ninetyDaysAgo = Carbon::now(new CarbonTimeZone('UTC'))->subDays(90)->timestamp;

            $serviceProducts = ServiceProduct::query()
                ->with([ServiceProduct::RELATION_SERVICE])
                ->where(ServiceProduct::FIELD_PRODUCT_ID, Product::query()->where(Product::FIELD_NAME, \App\Enums\Odin\Product::LEAD->value)->first()->{Product::FIELD_ID})
                ->get();

            $serviceProductsKeyed = [];
            foreach($serviceProducts as $serviceProduct) {
                $serviceProductsKeyed[$serviceProduct->{ServiceProduct::RELATION_SERVICE}->{IndustryService::FIELD_SLUG}] = $serviceProduct->{ServiceProduct::FIELD_ID};
            }

            $industries = Industry::all()->pluck(Industry::FIELD_ID, Industry::FIELD_NAME)->toArray();

            $websites = Website::all()->pluck(Website::FIELD_ID, Website::FIELD_URL)->toArray();

            $badAddressId = Address::query()->where(Address::FIELD_ADDRESS_1, Address::getBadDataUuid())->first()->{Address::FIELD_ID};

            $batchJobSize = (int) $this->option(self::OPT_BATCH_JOB_SIZE);

            $chunk = ceil($totalQuotes/$batchJobSize);
            $totalJobs = empty($customQuotesToMigrate)
                ? ceil($totalQuotes/$chunk)
                : 1;

            $jobDispatched = 0;

            $this->info("Total {$totalJobs} job" . ($totalJobs > 1 ? 's' : '') . " to be dispatched ...");

            $query = EloquentQuote::query()->select(EloquentQuote::ID);

            if(!empty($customQuotesToMigrate)) {
                $query->whereIn(EloquentQuote::ID, $customQuotesToMigrate);
            }

            $query->chunk($chunk, function($quoteIds) use (&$batchJobs, $websites, $serviceProductsKeyed, $industries, $mayFirstTwoThousandTwentyTwo, $ninetyDaysAgo, $badAddressId, &$jobDispatched, $totalJobs) {
                MigrateLegacyQuotesJob::dispatch($quoteIds->pluck(EloquentQuote::ID), $websites, $serviceProductsKeyed, $industries, $mayFirstTwoThousandTwentyTwo, $ninetyDaysAgo, $badAddressId)
                    ->onConnection(QueueHelperService::QUEUE_CONNECTION)
                    ->onQueue(QueueHelperService::QUEUE_NAME_LEGACY_MIGRATION);

                $jobDispatched++;

                $this->info("Dispatched {$jobDispatched} of {$totalJobs} jobs");
            });

            $this->info('Finished.');
        }
        else {
            $this->line("No legacy quotes to migrate");
        }

        return 0;
    }
}
