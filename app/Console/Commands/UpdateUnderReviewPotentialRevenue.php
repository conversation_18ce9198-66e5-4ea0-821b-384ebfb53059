<?php

namespace App\Console\Commands;

use App\Jobs\CalculatePotentialRevenue;
use App\Models\LeadProcessingUnderReview;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Model;

class UpdateUnderReviewPotentialRevenue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:under-review-potential-revenue {days=30}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Updates the potential revenue for under review leads over the last {days} days.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $days = intval($this->argument('days'));
        $date = Carbon::now()->subDays($days);
        $leads = LeadProcessingUnderReview::query()
            ->where(Model::CREATED_AT, '>=', $date)
            ->get()
            ->map(fn(LeadProcessingUnderReview $lead) => CalculatePotentialRevenue::dispatch($lead->id));

        return 0;
    }
}
