<?php

namespace App\Console\Commands;

use App\Enums\Odin\LocationSiloPageLocationType;
use App\Models\Legacy\Location;
use App\Models\Odin\LocationSiloPage;
use App\Models\Odin\Silo;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\IndustryRepository;
use App\Repositories\Odin\WebsiteRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Validator;

/**
 * @TODO: Upgrade to use new models.
 * @see Silo
 * @see LocationSiloPage
 */
class ImportLocationSiloPages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:location-silo-pages';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Imports location silo pages from a CSV for SolarReviews.com';

    const COLUMN_LOCATION_TYPE = 'location_type'; // Doubles as entry slug for now
    const COLUMN_STATE_SLUG    = 'state_slug';
    const COLUMN_CITY_SLUG     = 'city_slug';
    const COLUMN_RELATIVE_PATH = 'relative_path';

    /**
     * @param LocationRepository $locationRepository
     * @param WebsiteRepository $websiteRepository
     * @param IndustryRepository $industryRepository
     */
    public function __construct(
        protected LocationRepository $locationRepository,
        protected WebsiteRepository  $websiteRepository,
        protected IndustryRepository $industryRepository
    )
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     * @throws \Exception
     */
    public function handle()
    {
        $file   = base_path('database/seeders/data/location-silo-pages.csv');
        $handle = fopen($file, "r");

        $siloName            = $this->ask('Enter directory name (ie "Solar Companies")');
        $collectionHandle    = $this->ask('Enter collection handle (ie "companies_silo")');
        $siloRootPath        = $this->ask('Enter directory root path (ie "/solar-companies")');
        $websiteAbbreviation = $this->ask('Enter website abbreviation (ie "sr")');
        $industrySlug        = $this->ask('Enter industry slug');

        fgetcsv($handle);// skip header

        while ($csvLine = fgetcsv($handle)) {
            if (sizeof($csvLine) !== 4) {
                throw new \Exception('Invalid data');
            }

            $validator = Validator::make($this->mapCSVtoTableFieldNames($csvLine), [
                self::COLUMN_LOCATION_TYPE => 'string|required',
                self::COLUMN_STATE_SLUG    => 'string',
                self::COLUMN_CITY_SLUG     => 'string',
                self::COLUMN_RELATIVE_PATH => 'string'
            ]);

            if ($validator->fails()) {
                throw new \Exception('Invalid data');
            }
        }

        // go back to the start of the file
        rewind($handle);
        fgetcsv($handle);// skip header

        $websiteId = $this->websiteRepository->getWebsiteByAbbreviation($websiteAbbreviation)->id;
        $industryId = $this->industryRepository->getIndustryIdBySlug($industrySlug);
        $silo = null;

        if($websiteId && $industryId) {
            $silo = Silo::create([
                Silo::FIELD_NAME                => $siloName,
                Silo::FIELD_ROOT_PATH           => $siloRootPath,
                Silo::FIELD_COLLECTION_HANDLE   => $collectionHandle,
                Silo::FIELD_WEBSITE_ID          => $websiteId,
                Silo::FIELD_INDUSTRY_ID         => $industryId,
                Silo::FIELD_INDUSTRY_SERVICE_ID => null,
                Silo::FIELD_IS_ACTIVE           => true,
                Silo::FIELD_FLOW_ID             => null,
                Silo::FIELD_REVISION_ID         => null
            ]);
        }


        // process the data
        while ($silo && $csvLine = fgetcsv($handle)) {
            $assocCSVLine = $this->mapCSVtoTableFieldNames($csvLine);

            $type = match ($assocCSVLine[self::COLUMN_LOCATION_TYPE]) {
                'national' => LocationSiloPageLocationType::NATIONAL->value,
                'state' => LocationSiloPageLocationType::STATE->value,
                'city' => LocationSiloPageLocationType::CITY->value
            };

            $location       = null;
            $parentLocation = null;

            if($type !== LocationSiloPageLocationType::NATIONAL->value) {
                $stateKey = $assocCSVLine[self::COLUMN_STATE_SLUG];
                $cityKey  = $assocCSVLine[self::COLUMN_CITY_SLUG];

                $location = $this->getLocation($stateKey, $cityKey);

                if(!$location) {
                    continue;
                }

                if($cityKey) {
                    $parentLocation = $this->getLocation($stateKey, null);
                }
            }

            LocationSiloPage::create([
                LocationSiloPage::FIELD_RELATIVE_PATH      => $assocCSVLine[self::COLUMN_RELATIVE_PATH],
                LocationSiloPage::FIELD_LOCATION_TYPE      => $type,
                LocationSiloPage::FIELD_ENTRY_SLUG         => $assocCSVLine[self::COLUMN_LOCATION_TYPE],
                LocationSiloPage::FIELD_LOCATION_ID        => $location?->{Location::ID},
                LocationSiloPage::FIELD_PARENT_LOCATION_ID => $parentLocation?->{Location::ID},
                LocationSiloPage::FIELD_SILO_ID            => $silo->{Silo::FIELD_ID},
            ]);

        }
        return 0;
    }

    /**
     * @param string $stateKey
     * @param string|null $cityKey
     * @return ?Location
     */
    private function getLocation(string $stateKey, ?string $cityKey): ?Location
    {
        if($stateKey && $cityKey) {
            return $this->locationRepository->getCityByStateAndCityKey($stateKey, $cityKey);
        }

        return $this->locationRepository->getState($stateKey);
    }

    /**
     * Map the csv columns to the database table field names
     *
     * @param array $csvLine
     * @return array
     */
    private function mapCSVtoTableFieldNames(array $csvLine): array
    {
        $map = [
            self::COLUMN_LOCATION_TYPE,
            self::COLUMN_STATE_SLUG,
            self::COLUMN_CITY_SLUG,
            self::COLUMN_RELATIVE_PATH
        ];

        return array_combine($map, $csvLine);
    }
}
