<?php

namespace App\Console\Commands;

use App\Models\Action;
use App\Models\ActivityFeed;
use App\Models\Legacy\EloquentCompanyCRMAction;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Repositories\Legacy\CompanyCRMRepository;
use App\Repositories\Odin\CompanyRepository;
use App\Repositories\Odin\CompanyUserRepository;
use Illuminate\Console\Command;
use Exception;

class FixActionForIds extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:action-for-ids';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fixes action for_id based on legacy_id and relation';

    /** @var CompanyRepository $companyRepository */
    protected CompanyRepository $companyRepository;

    /** @var CompanyUserRepository $companyUserRepository */
    protected CompanyUserRepository $companyUserRepository;

    /** @var CompanyCRMRepository $companyCRMRepository */
    protected CompanyCRMRepository $companyCRMRepository;

    public function __construct(CompanyRepository $companyRepository, CompanyUserRepository $companyUserRepository, CompanyCRMRepository $companyCRMRepository)
    {
        parent::__construct();
        $this->companyRepository = $companyRepository;
        $this->companyUserRepository = $companyUserRepository;
        $this->companyCRMRepository = $companyCRMRepository;
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        Action::unsetEventDispatcher();

        $query = Action::query()->whereNotNull(Action::FIELD_LEGACY_FOR_ID);
        $bar = $this->output->createProgressBar($query->count());
        $bar->start();

        $query->chunk(1000, function ($actions) use ($bar) {
            /** @var Action $action */
            foreach ($actions as $action) {
                $relationId   = null;
                $companyId    = null;
                $relationType = null;

                switch($action->{Action::FIELD_FOR_RELATION_TYPE}) {
                    case Action::RELATION_TYPE_COMPANY:
                        try {
                            // try to assign relation to company
                            $relationId = $this->companyRepository->getCompanyByLegacyIdOrFail($action->{Action::FIELD_LEGACY_FOR_ID})->{Company::FIELD_ID};
                            $companyId  = $relationId;
                        } catch (Exception $e) {
                            // if company doesn't exist delete action
                            $this->deleteActionAndActivityFeed($action);
                        }

                        break;
                    case Action::RELATION_TYPE_COMPANY_CONTACT:
                        try {
                            // try to assign relation to company contact
                            $user       = $this->companyUserRepository->getCompanyContactByLegacyIdWithTrashedOrFail($action->{Action::FIELD_LEGACY_FOR_ID});
                            $relationId = $user->{CompanyUser::FIELD_ID};
                            $companyId  = $user->company->{Company::FIELD_ID};
                        } catch (Exception $e) {
                            try{
                                // if company contact doesn't exist try to assign relation to company
                                $relationId   = $this->getFallbackRelationIdByContactId($action->{Action::FIELD_LEGACY_FOR_ID});
                                $companyId    = $relationId;
                                $relationType = Action::RELATION_TYPE_COMPANY;
                            } catch (Exception $e) {
                                // if company doesn't exist delete action
                                $this->deleteActionAndActivityFeed($action);
                            }
                        }
                        break;
                    default:
                        break;
                }

                if($relationId && $companyId) {
                    $this->updateAction($action, $relationId, $companyId, $relationType);
                }

                $bar->advance();
            }
        });

    }

    /**
     * @param Action $action
     * @return void
     */
    private function deleteActionAndActivityFeed(Action $action): void
    {
        $action->activity?->delete();
        $action->delete();
    }

    /**
     * @param Action $action
     * @param int $relationId
     * @param int $companyId
     * @param string|null $relationType
     * @return void
     */
    private function updateAction(Action $action, int $relationId, int $companyId, ?string $relationType): void
    {
        if($relationType) {
            $action->{Action::FIELD_FOR_RELATION_TYPE} = Action::RELATION_TYPE_COMPANY;
        }

        $action->{Action::FIELD_FOR_ID} = $relationId;
        $action->save();

        $action->activity?->update([
                ActivityFeed::FIELD_COMPANY_ID => $companyId
        ]);
    }

    /**
     * @param $contactId
     * @return int|null
     */
    private function getLegacyCompanyIdByContactId($contactId): ?int
    {
        return EloquentCompanyCRMAction::query()
            ->where(EloquentCompanyCRMAction::CONTACT_ID, $contactId)
            ->first()?->{EloquentCompanyCRMAction::COMPANY_ID};
    }

    /**
     * @param int $contactId
     * @return int|null
     */
    private function getFallbackRelationIdByContactId(int $contactId): ?int
    {
        $legacyCompanyId = $this->getLegacyCompanyIdByContactId($contactId);

        if($legacyCompanyId) {
            return $this->companyRepository->getCompanyByLegacyIdOrFail($legacyCompanyId)->{Company::FIELD_ID};
        }

        return null;
    }
}
