<?php

namespace App\Console\Commands\ConsumerProjects;

use App\ConsumerProcessing\Services\ConsumerProjectProcessingService;
use App\DataModels\Campaigns\ConsumerProject;
use App\Mediators\CampaignMediator;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class ConsumerProjectTesting extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'consumer-projects:test {--consumer-id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This is not going to stay. Just here now for help w/ testing';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(CampaignMediator $mediator): int
    {
        $this->output->block("Starting Consumer Projects Testing");

        if($this->option('consumer-id')) {
            $this->info("Retrieving consumer: " . $this->option('consumer-id'));
            $consumer = Consumer::query()->find($this->option('consumer-id'));
        } else {
            $this->info('Finding a valid consumer to test with');
            /** @var Consumer $consumer */
            $consumer = Consumer::query()
                ->where(Consumer::FIELD_STATUS, Consumer::STATUS_INITIAL)
                ->where(Consumer::FIELD_CLASSIFICATION, Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_SMS)
                ->whereDoesntHave(Consumer::RELATION_CONSUMER_PRODUCT, function (Builder $query) {
                    $query->whereHas(ConsumerProduct::RELATION_INDUSTRY_SERVICE, function (Builder $query) {
                        $query->whereIn(IndustryService::FIELD_INDUSTRY_ID, [1, 2]);
                    });
                })
                ->whereHas(Consumer::RELATION_CONSUMER_PRODUCT, function (Builder $query) {
                    $query->whereHas(ConsumerProduct::RELATION_ADDRESS, function(Builder $query){
                        $query->whereIn(Address::FIELD_ZIP_CODE, ["92337"]);
                    });
                })
                ->inRandomOrder()
                ->first();
        }

        $this->line("Found consumer: " . $consumer->id);
        $this->info('Testing consumer projects');
        $project = new ConsumerProject($consumer, $consumer->consumerProducts()->first()->address, now());

        $this->info("Potential Products");
        dump($project->potentialProducts());

        $campaigns = $this->getAvailableCampaigns($consumer, $mediator, $project);
        $this->info("After filter, " . $campaigns->count() . " campaigns available");

        $this->testAllocationService($consumer, $project->address);

        return Command::SUCCESS;
    }

    protected function testAllocationService(Consumer $consumer, Address $address): void
    {
        /** @var ConsumerProjectProcessingService $service */
        $service = app(ConsumerProjectProcessingService::class);

        $this->info("Testing Allocation Service");
        $service->allocate($consumer, $address);

    }

    protected function getAvailableCampaigns(Consumer $consumer, CampaignMediator $mediator, ConsumerProject $project): Collection
    {
        /** @var ConsumerProduct $product */
        $product = $consumer->consumerProducts()->first();
        $this->info("Fetching campaigns");
        $campaigns = CompanyCampaign::query()
            ->where(CompanyCampaign::FIELD_SERVICE_ID, $product->serviceProduct->industry_service_id)
            ->get();
        $this->info("Found " . $campaigns->count() . " Campaigns");

        $campaigns = $mediator->preFilter($campaigns, $project);

        return $mediator->filter($campaigns, $project);
    }
}
