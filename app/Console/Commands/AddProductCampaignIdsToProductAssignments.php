<?php

namespace App\Console\Commands;

use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCampaign;
use Illuminate\Console\Command;

class AddProductCampaignIdsToProductAssignments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:product-assignment-campaign-ids';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Only for dev environment to update test data Multi-Industry ProductAssignments where they are missing ProductCampaign id';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        if (app()->environment() === 'production') {
            $this->error("This command should not be run in production.");
            return 1;
        }
        $count = 0;

        ProductAssignment::all()->each(function(ProductAssignment $pa) use (&$count) {
            if ($pa->campaign_id) return;
            $salesConfigId = $pa->payload[EloquentQuoteCompany::LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION_ID] ?? null;
            if ($salesConfigId) {
                $leadCampaignId = LeadCampaignSalesTypeConfiguration::query()->find($salesConfigId)
                    ?->{LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID};
                if ($leadCampaignId) {
                    $productCampaignId = ProductCampaign::query()
                        ->where(ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID, $leadCampaignId)
                        ->first()
                        ?->id;
                    if ($productCampaignId) {
                        $pa->update([ProductAssignment::FIELD_CAMPAIGN_ID => $productCampaignId]);
                        $count++;
                    }
                }
            }
        });
        $this->info("\n\tUpdated $count ProductAssignments with ProductCampaign ids.\n");

        return 0;
    }
}
