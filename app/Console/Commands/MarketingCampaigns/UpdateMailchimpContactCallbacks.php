<?php

namespace App\Console\Commands\MarketingCampaigns;

use App\Jobs\BatchUpdateMailchimpContacts;
use App\Models\MarketingCampaignConsumer;
use App\Models\Odin\Consumer;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class UpdateMailchimpContactCallbacks extends Command
{
    const int MAILCHIMP_MAXIMUM_UPLOAD = 500;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email-marketing:update-mailchimp-contact-callbacks';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update all mailchimp contacts with the new standard callback url';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info("retrieving consumers that are in marketing campaigns");
        $jobCount = 0;

        Consumer::query()
            ->select(
                Consumer::FIELD_FIRST_NAME,
                Consumer::FIELD_LAST_NAME,
                Consumer::FIELD_EMAIL,
                Consumer::FIELD_REFERENCE
            )
            ->whereHas(Consumer::RELATION_MARKETING_CONSUMER, function ($query) {
                //only mcc that were uploaded to mailchimp
                $query->whereNotNull(MarketingCampaignConsumer::FIELD_EXTERNAL_REFERENCE);
            })
            ->chunk(self::MAILCHIMP_MAXIMUM_UPLOAD, function (Collection $consumers) use (&$jobCount) {
                BatchUpdateMailchimpContacts::dispatch($consumers);
                $jobCount++;
            });

        $this->info("$jobCount jobs dispatched to update consumers");
    }
}
