<?php

namespace App\Console\Commands\MarketingCampaigns;

use App\Models\MarketingCampaignConsumer;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\RecycledLeads\LeadProcessingAged;
use Illuminate\Console\Command;

class SyncMarketingCampaignConsumerClones extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email-marketing:sync-clones';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $mccs = MarketingCampaignConsumer::query()
            ->with([
                MarketingCampaignConsumer::RELATION_CONSUMER .'.'. Consumer::RELATION_CLONES .'.'. Consumer::RELATION_CONSUMER_PRODUCT,
                MarketingCampaignConsumer::RELATION_CONSUMER .'.'. Consumer::RELATION_CONSUMER_PRODUCT
            ])
            ->whereNotNull(MarketingCampaignConsumer::FIELD_REVALIDATED_AT)
            ->whereNull(MarketingCampaignConsumer::FIELD_CLONED_CONSUMER_PRODUCT_ID)
            ->get();


        $totalCount = $mccs->count();

        $aged = 0;
        $marketing = 0;

        $this->line("identified $totalCount mcc that were revalidated but didn't have a cloned_consumer_product_id");

        /** @var MarketingCampaignConsumer $mcc */
        foreach ($mccs as $mcc) {
            $originalConsumer = $mcc->consumer;

            $originalConsumerProducts = $originalConsumer->consumerProducts;

            $consumerProductIds = $originalConsumerProducts->pluck(ConsumerProduct::FIELD_ID)->toArray();

            $this->info("Checking if consumer product ids exist in processing aged queue");

            $this->info(json_encode($consumerProductIds));

            $existsInAged = LeadProcessingAged::query()
                ->whereIntegerInRaw(
                    LeadProcessingAged::FIELD_CONSUMER_PRODUCT_ID,
                    $consumerProductIds
                )
                ->exists();

            if ($existsInAged) {
                $this->info("Consumer reference: {$mcc->consumer_reference} exists in aged queue, please do manually");
                $aged++;

            } else {
                $clone = $originalConsumer->clones->first();
                $marketing++;

                $clonedConsumerProduct = $clone->consumerProducts->first();

                $mcc->cloned_consumer_product_id = $clonedConsumerProduct->id;

                $mcc->save();
            }
        }

        $this->info("Identified $aged consumers that were also revaildated my mcc");
        $this->info("Identified $marketing consumers that were not in aged queue");
    }
}
