<?php

namespace App\Console\Commands;

use App\Repositories\Flows\FlowRepository;
use App\Repositories\Flows\v2\RevisionRepository;
use App\Services\GoogleFirestoreService;
use Google\Cloud\Firestore\QuerySnapshot;
use Illuminate\Console\Command;

class MigrateFlowsToV2 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update-flows-to-v2';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update Flows to V2';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $firestoreService = app()->make(GoogleFirestoreService::class);

        /** @var QuerySnapshot $reference */
        $snapshot = $firestoreService->getCollectionReference(FlowRepository::LEGACY_COLLECTION_FLOWS)
            ->documents();
        $v2FlowPath = RevisionRepository::COLLECTION_FLOWS;

        $flowsFound = count($snapshot->rows());

        $this->info("\n$flowsFound Flows were found.\n");

        $count = 0;

        foreach($snapshot as $document) {
            $id = $document->id();
            $this->line("\tProcessing Flow $id...");
            if ($firestoreService->addDocumentToCollection($v2FlowPath, $id, $document->data(), true)) {
                $count ++;
            }
        }

        $this->info("\n$count Flows were migrated.\n");
    }
}
