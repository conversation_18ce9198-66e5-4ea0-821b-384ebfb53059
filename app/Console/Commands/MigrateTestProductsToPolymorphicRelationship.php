<?php

namespace App\Console\Commands;

use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\ProductCampaign;
use App\Models\TestProduct;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Symfony\Component\Console\Command\Command as CommandAlias;
use Symfony\Component\Console\Helper\ProgressBar;

class MigrateTestProductsToPolymorphicRelationship extends Command
{
    const UPDATE_CHUNK = 1000;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate-test-products-to-polymorphic-relationship {--all : Migrate all items}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate test products to polymorphic relationship';

    protected array $errors = [];

    protected ProgressBar $progressBar;

    /**
     * @return Builder
     */
    protected function getTestProductsMigrationQuery(): Builder
    {
        $query = TestProduct::query();

        if (!$this->option('all')) {
            $this->line("Migrating only test products with no relation type and id");

            $query->where(function (Builder $query){
                return $query
                    ->whereNull(TestProduct::FIELD_RELATION_ID)
                    ->orWhere(TestProduct::FIELD_RELATION_ID, '=', '');
            })->where(function (Builder $query){
                return $query
                    ->whereNull(TestProduct::FIELD_RELATION_TYPE)
                    ->orWhere(TestProduct::FIELD_RELATION_TYPE, '=', '');
            });
        } else {
            $this->line("Migrating all test products");
        }

        return $query;
    }

    protected function migrateTestProduct(TestProduct $testProduct): void
    {
        try {
            $testProduct->{TestProduct::FIELD_RELATION_ID} = $testProduct->campaign_id ?: $testProduct->legacy_lead_campaign_id;
            $testProduct->{TestProduct::FIELD_RELATION_TYPE} = $testProduct->campaign_id ? ProductCampaign::class : LeadCampaign::class;
            $testProduct->save();
        } catch (\Exception $exception) {
            $this->addError('Test product id ' . $testProduct->{TestProduct::FIELD_ID} . ' failed to update. ' . $exception->getMessage());
        }
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        try {

            $query = $this->getTestProductsMigrationQuery();

            $totalTestProducts = $query->count();

            $this->progressBar = $this->output->createProgressBar($query->count());

            $this->newLine();
            $this->line("Migrating $totalTestProducts test products");

            $query->chunk(self::UPDATE_CHUNK, function ($testProducts)  {
                foreach ($testProducts as $testProduct) {
                    $this->migrateTestProduct($testProduct);
                    $this->progressBar->advance();
                }
            });
        } catch (\Exception $exception) {
            $this->addError($exception->getMessage());
        }

        if (count($this->errors) > 0) {
            $this->outputErrors();
            return CommandAlias::FAILURE;
        }

        $this->newLine();
        $this->line('Migration finished successfully');

        return CommandAlias::SUCCESS;
    }

    /**
     * @param $error
     * @return void
     */
    protected function addError($error): void
    {
        $this->errors[] = $error;
    }

    /**
     * @return void
     */
    protected function outputErrors(): void
    {
        if (!empty($this->errors)) {
            $this->error("\nErrors occurred:");

            foreach ($this->errors as $error) {
                $this->error($error);
            }
        }
    }
}
