<?php

namespace App\Console\Commands;

use App\Models\Odin\Company;
use App\Models\Legacy\EloquentMedia;
use App\Services\DatabaseHelperService;
use App\Services\Odin\MigrateLegacyCompanyMediaService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Exception;

class MigrateCompanyMediaFromLegacyAdmin extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:company-media-from-legacy-admin';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handles migrating media files from the legacy admin to the new schema (A2) for the companies already been migrated to A2.';

    /**
     * Execute the console command.
     *
     * @param MigrateLegacyCompanyMediaService $companyMediaService
     * @return int
     */
    public function handle(MigrateLegacyCompanyMediaService $companyMediaService): int
    {
        $this->line("Initiating the migration process...");

        $totalMediaItems = $this->getBaseQuery()->count();

        if($totalMediaItems > 0) {
            $this->info("Total no. of media items to migrate: $totalMediaItems");

            try {
                $this->getBaseQuery()
                     ->select(DB::raw(EloquentMedia::TABLE.".*"))
                     ->orderBy(EloquentMedia::ID)
                     ->chunk(1000, function($legacyMediaItems) use ($companyMediaService) {
                          /** @var Collection $legacyMediaItems */
                          $companyMediaService->handle($legacyMediaItems);
                     });
            }
            catch (Exception $exc) {
                $this->alert("Error: {$exc->getMessage()}");
            }

            $this->info('Finished processing.');
        }
        else {
            $this->line("No media item to migrate.");
        }

        return 0;
    }

    /**
     * @return Builder
     */
    protected function getBaseQuery(): Builder
    {
        return EloquentMedia::query()
            ->join(DatabaseHelperService::database().'.'.Company::TABLE, function($join) {
                $join->on(
                    DatabaseHelperService::database().'.'.Company::TABLE.'.'.Company::FIELD_LEGACY_ID,
                    DatabaseHelperService::readOnlyDatabase().'.'.EloquentMedia::TABLE.'.'.EloquentMedia::REL_ID
                );
            })
            ->where(EloquentMedia::REL_TYPE, EloquentMedia::REL_TYPE_COMPANY);
    }
}
