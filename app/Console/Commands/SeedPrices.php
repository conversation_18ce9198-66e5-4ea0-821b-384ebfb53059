<?php

namespace App\Console\Commands;

use App\Models\Odin\ProductCountyBidPrice;
use App\Models\Odin\ProductCountyFloorPrice;
use App\Models\Odin\ProductStateBidPrice;
use App\Models\Odin\ProductStateFloorPrice;
use Database\Seeders\ProductCountyBidPricesSeeder;
use Database\Seeders\ProductCountyFloorPricesSeeder;
use Database\Seeders\ProductStateBidPricesSeeder;
use Database\Seeders\ProductStateFloorPricesSeeder;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;

class SeedPrices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:seed-prices';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Truncate and then seed all pricing tables';

    private array $seeders = [
        ProductStateFloorPricesSeeder::class,
        ProductCountyFloorPricesSeeder::class,
        ProductCountyBidPricesSeeder::class,
        ProductStateBidPricesSeeder::class
    ];

    private array $tables = [
        ProductStateFloorPrice::TABLE,
        ProductCountyFloorPrice::TABLE,
        ProductCountyBidPrice::TABLE,
        ProductStateBidPrice::TABLE
    ];

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        if(App::environment(['production', 'prod'])) {
            $this->error("This command does not run in production");

            return 0;
        }

        $this->info("Truncating and reseeding all pricing tables");
        $this->newLine();

        foreach($this->tables as $table) {
            DB::table($table)->truncate();

            $this->comment("Truncated $table");
        }

        $this->newLine();

        $this->info("Pricing tables truncated");

        $this->newLine();

        $this->info("Seeding pricing tables");

        $start = microtime(true);

        $this->newLine();

        foreach($this->seeders as $seeder) {
            $this->info("Running $seeder");

            app($seeder)->run();
        }

        $elapsed = floor(microtime(true) - $start);

        $this->info("Elapsed $elapsed secs");

        return 0;
    }
}
