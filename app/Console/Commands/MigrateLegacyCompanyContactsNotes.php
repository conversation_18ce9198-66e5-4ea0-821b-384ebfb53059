<?php

namespace App\Console\Commands;

use App\Jobs\LegacyMigrations\MigrateLegacyCompanyContactsNotesJob;
use App\Models\Legacy\EloquentCompanyContact;
use App\Services\BatchHelperService;
use App\Services\QueueHelperService;
use Illuminate\Bus\Batch;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Throwable;

class MigrateLegacyCompanyContactsNotes extends Command
{
    const BATCH_COUNT = 'batch-count';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'legacy-migrate:company-contacts-notes {--batch-count=20 : The number of batches/jobs to create}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handles the migration of legacy company contacts notes';

    /**
     * Execute the console command.
     *
     * @return int
     * @throws Throwable
     */
    public function handle()
    {
        $this->line("Migrating company contacts notes");

        $batchCount = (int) $this->option(self::BATCH_COUNT);
        // Performance
        DB::disableQueryLog();

        // there is no way to tell which notes have already been migrated
        // so we can just match all the company contacts with have not empty notes
        $contactIds = EloquentCompanyContact::withTrashed()
            ->select(EloquentCompanyContact::TABLE.'.'.EloquentCompanyContact::FIELD_CONTACT_ID)
            ->where(EloquentCompanyContact::FIELD_NOTES, '!=', '')
            ->pluck(EloquentCompanyContact::FIELD_CONTACT_ID);

        if ($contactIds->count() > 0) {
            $chunkedContactIds = app(BatchHelperService::class)->chunkByBatchCount($batchCount, $contactIds->toArray());

            foreach ($chunkedContactIds as $idsChunk) {
                $batchJobs[] = new MigrateLegacyCompanyContactsNotesJob(collect($idsChunk));
            }

            $batch = Bus::batch($batchJobs)
                ->catch(function (Batch $batch, Throwable $throwable) {
                    logger()->error("Legacy company contacts migration error: ".$throwable->getMessage());
                })
                ->allowFailures()
                ->name("Legacy Company Contacts Migration")
                ->onConnection(QueueHelperService::QUEUE_CONNECTION)
                ->onQueue(QueueHelperService::QUEUE_NAME_LEGACY_MIGRATION)
                ->dispatch();

            $bar = $this->output->createProgressBar($batch->totalJobs);

            $bar->setFormat("%current%/%max% migration jobs done. Elapsed: %elapsed%");
            $bar->setRedrawFrequency(1);

            $bar->start();

            while (!$batch->finished() && !$batch->cancelled()) {
                $batch = $batch->fresh();
                $bar->setProgress($batch->processedJobs());
            }

            $bar->finish();

            $this->newLine();
            $this->line("Company contacts migrated");
        }

        return 0;

    }
}