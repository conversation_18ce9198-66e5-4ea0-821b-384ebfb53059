<?php

namespace App\Console\Commands;

use App\Models\Odin\CompanyUser;
use App\Repositories\Odin\CompanyRepository;
use App\Services\CompanyDiscoveryService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Validator;

class ImportContacts extends Command
{
    const CHOICE_YES = 'Yes';
    const CHOICE_NO  = 'No';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'discovery:import-contacts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Imports ZoomInfo contacts from a CSV';

    /** @var CompanyRepository $companyRepository */
    protected CompanyRepository $companyRepository;

    /** @var CompanyDiscoveryService $companyDiscoveryService */
    protected CompanyDiscoveryService $companyDiscoveryService;

    const COMPANY_ZOOM_INFO_ID = 'company_zoom_info_id';
    const CONTACT_ZOOM_INFO_ID = 'contact_zoom_info_id';
    const JOB_HIERARCHY        = 'job_hierarchy';
    const QUERY_NAME           = 'query_name';

    /**
     * @param CompanyRepository $companyRepository
     * @param CompanyDiscoveryService $companyDiscoveryService
     */
    public function __construct(CompanyRepository $companyRepository, CompanyDiscoveryService $companyDiscoveryService)
    {
        parent::__construct();
        $this->companyRepository = $companyRepository;
        $this->companyDiscoveryService = $companyDiscoveryService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     * @throws \Exception
     */
    public function handle()
    {
        $file   = base_path('database/seeders/data/contacts.csv');
        $handle = fopen($file, "r");

        fgetcsv($handle);// skip header

        while ($csvLine = fgetcsv($handle)) {
            if (sizeof($csvLine) !== 11) {
                throw new \Exception('Invalid data');
            }

            $validator = Validator::make($this->mapCSVtoTableFieldNames($csvLine), [
                self::CONTACT_ZOOM_INFO_ID      => 'string|required',
                CompanyUser::FIELD_LAST_NAME    => 'string',
                CompanyUser::FIELD_FIRST_NAME   => 'string',
                CompanyUser::FIELD_TITLE        => 'string',
                CompanyUser::FIELD_OFFICE_PHONE => 'string',
                CompanyUser::FIELD_EMAIL        => 'string',
                CompanyUser::FIELD_CELL_PHONE   => 'string',
                CompanyUser::FIELD_DEPARTMENT   => 'string',
                self::COMPANY_ZOOM_INFO_ID      => 'string'
            ]);

            if ($validator->fails()) {
                throw new \Exception('Invalid data');
            }
        }

        $askThrottle = true;
        $throttling = false;
        $throttleCount = 0;

        // go back to the start of the file
        rewind($handle);
        fgetcsv($handle);// skip header

        // process the data
        while ($csvLine = fgetcsv($handle)) {

            if($askThrottle) {
                $choice = $this->choice('Would you like to throttle the number of contacts to process?', [self::CHOICE_NO, self::CHOICE_YES]);

                if ($choice === self::CHOICE_YES) {
                    $throttling = true;
                    $throttleCount = intval($this->ask('Enter the number of contacts you would like to process'));
                }

                $askThrottle = false;
            }

            $assocCSVLine = $this->mapCSVtoTableFieldNames($csvLine);

            $company = $this->companyRepository->getCompanyByZoomInfoID($assocCSVLine[self::COMPANY_ZOOM_INFO_ID]);

            $email = $assocCSVLine[CompanyUser::FIELD_EMAIL];

            if (!$company || !$email) {
                continue;
            }

            $companyUser = CompanyUser::query()
                ->where(CompanyUser::FIELD_COMPANY_ID, $company->id)
                ->where(CompanyUser::FIELD_EMAIL, $email)
                ->first();

            if ($companyUser) {
                continue;
            }

            $companyUser = CompanyUser::create([
                CompanyUser::FIELD_COMPANY_ID   => $company->id,
                CompanyUser::FIELD_ZOOM_INFO_ID => $assocCSVLine[self::CONTACT_ZOOM_INFO_ID],
                CompanyUser::FIELD_FIRST_NAME   => $assocCSVLine[CompanyUser::FIELD_FIRST_NAME],
                CompanyUser::FIELD_LAST_NAME    => $assocCSVLine[CompanyUser::FIELD_LAST_NAME],
                CompanyUser::FIELD_CELL_PHONE   => $assocCSVLine[CompanyUser::FIELD_CELL_PHONE],
                CompanyUser::FIELD_OFFICE_PHONE => $assocCSVLine[CompanyUser::FIELD_OFFICE_PHONE],
                CompanyUser::FIELD_EMAIL        => $assocCSVLine[CompanyUser::FIELD_EMAIL],
                CompanyUser::FIELD_TITLE        => $assocCSVLine[CompanyUser::FIELD_TITLE],
                CompanyUser::FIELD_DEPARTMENT   => $assocCSVLine[CompanyUser::FIELD_DEPARTMENT],
                CompanyUser::FIELD_IMPORTED     => true,
                CompanyUser::FIELD_IS_CONTACT   => true
            ]);

            $this->companyDiscoveryService->syncCompanyContactToLegacy($company, $companyUser);

            if($throttling) {
                $throttleCount--;
                if($throttleCount === 0) {
                    $askThrottle = true;
                    $throttling = false;
                }
            }
        }
        return 0;
    }

    /**
     * Map the csv columns to the database table field names
     *
     * @param array $csvLine
     * @return array
     */
    private function mapCSVtoTableFieldNames(array $csvLine): array
    {
        $map = [
            self::CONTACT_ZOOM_INFO_ID,
            CompanyUser::FIELD_LAST_NAME,
            CompanyUser::FIELD_FIRST_NAME,
            CompanyUser::FIELD_TITLE,
            self::JOB_HIERARCHY,
            CompanyUser::FIELD_OFFICE_PHONE,
            CompanyUser::FIELD_EMAIL,
            CompanyUser::FIELD_DEPARTMENT,
            CompanyUser::FIELD_CELL_PHONE,
            self::COMPANY_ZOOM_INFO_ID,
            self::QUERY_NAME
        ];

        return array_combine($map, $csvLine);
    }
}
