<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class SyncAdmin2Companies extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:admin2-companies';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync missing admin2 companies to legacy';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $companies = $this->ask('Company Ids to sync (comma seperated or press enter to sync all)');

        $this->info('Under development');
    }
}
