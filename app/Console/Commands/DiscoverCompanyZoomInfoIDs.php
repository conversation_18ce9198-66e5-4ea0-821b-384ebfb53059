<?php

namespace App\Console\Commands;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyData;
use App\Repositories\Odin\CompanyRepository;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;

class DiscoverCompanyZoomInfoIDs extends Command
{
    const API_KEY_ZOOM_INFO_TOKEN = 'jwt';

    const API_ZOOM_INFO_BASE_URI           = 'https://api.zoominfo.com';
    const API_ZOOM_INFO_AUTHENTICATE_URI   = self::API_ZOOM_INFO_BASE_URI . '/authenticate';
    const API_ZOOM_INFO_COMPANY_SEARCH_URI = self::API_ZOOM_INFO_BASE_URI . '/search/company';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'discover:zoom_info_ids';

    /** @var string|null $zoomInfoToken */
    protected ?string $zoomInfoToken = null;

    /** @var string $zoomInfoEmail */
    protected string $zoomInfoEmail;

    /** @var string $zoomInfoPassword */
    protected string $zoomInfoPassword;

    /** @var CompanyRepository $companyRepository */
    protected CompanyRepository $companyRepository;

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handles fetching of Zoom Info IDs for Companies.';

    /**
     * @param CompanyRepository $companyRepository
     */
    public function __construct(CompanyRepository $companyRepository)
    {
        parent::__construct();
        $this->companyRepository = $companyRepository;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        $this->setupZoomInfo();

        $this->getZoomInfoIDs();
    }

    /**
     * @return void
     */
    private function setupZoomInfo(): void
    {
        $this->zoomInfoEmail = $this->ask('Enter your ZoomInfo email log in');
        $this->zoomInfoPassword = $this->secret('Enter your ZoomInfo password');

        $this->authenticateZoomInfo();

        if (is_null($this->zoomInfoToken)) {
            $this->warn('Unable to generate an auth token for ZoomInfo. Company discovery can continue, but contacts will not be imported. Do you want to continue?');
        }
    }

    /**
     * @return void
     */
    private function authenticateZoomInfo(): void
    {
        $response = Http::withHeaders([
            'Content-Type' => 'application/json'
        ])->post(self::API_ZOOM_INFO_AUTHENTICATE_URI, [
            'username' => $this->zoomInfoEmail,
            'password' => $this->zoomInfoPassword
        ]);

        $this->zoomInfoToken = Arr::get($response->json(), self::API_KEY_ZOOM_INFO_TOKEN);
    }

    /**
     * @return void
     */
    private function getZoomInfoIDs(): void
    {
        $this->info("Getting ZoomInfo IDs for companies without data");
        $this->handleCompanies($this->getCompaniesWithoutData());

        $this->info("Getting ZoomInfo IDs for companies with data but without Zoom Info Id");
        $this->handleCompanies($this->getCompaniesWithoutZoomInfoId());
    }

    /**
     * @param Collection $companies
     * @return void
     */
    private function handleCompanies(Collection $companies): void
    {
        try {
            $bar = $this->output->createProgressBar($companies->count());
            $bar->start();

            $companies->shuffle()->map(function ($company) use ($bar) {
                $company->refresh();

                if (!$company->checked) {
                    $this->saveZoomInfoId($company);
                }

                $bar->advance();
            });
        } catch (\Exception $e) {
            logger()->error("Failed to execute company discovery contract. Error: {$e->getMessage()}");
        }
    }

    /**
     * @return Collection|array
     */
    private function getCompaniesWithoutData(): Collection|array
    {
        return Company::query()->whereNotNull(Company::FIELD_WEBSITE)
            ->where(Company::FIELD_WEBSITE,'<>','')
            ->whereDoesntHave(Company::RELATION_DATA)
            ->get();
    }

    /**
     * @return Collection|array
     */
    private function getCompaniesWithoutZoomInfoId(): Collection|array
    {
        return Company::query()
            ->whereNotNull(Company::FIELD_WEBSITE)
            ->where(Company::FIELD_WEBSITE,'<>','')
            ->whereHas(Company::RELATION_DATA, function($has) {
                $has->whereNull(CompanyData::FIELD_PAYLOAD.'->zoom_info_id');
            })
            ->get();
    }

    /**
     * @param Company $company
     * @return void
     */
    private function saveZoomInfoId(Company $company): void
    {
        $companyZoomInfoId = $this->getCompanyZoomInfoIdByWebsite($company->website);

        if ($companyZoomInfoId) {

            if(!$this->companyRepository->getCompanyDataByKey($company, 'zoom_info_id')) {
                $this->companyRepository->updateCompanyData($company, ['zoom_info_id' => $companyZoomInfoId]);
            }
        }
    }

    /**
     * @param string $website
     * @return string|bool
     */
    private function getCompanyZoomInfoIdByWebsite(string $website): string|bool
    {
        $response = Http::withHeaders([
            'Content-Type'  => 'application/json',
            'Authorization' => 'Bearer ' . $this->zoomInfoToken
        ])->post(self::API_ZOOM_INFO_COMPANY_SEARCH_URI, [
            'companyWebsite' => $website
        ])->json();

        $data = Arr::get($response, 'data');

        if ($data) {
            return Arr::get($data[0], 'id');
        }

        return false;
    }
}
