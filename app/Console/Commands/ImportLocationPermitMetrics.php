<?php

namespace App\Console\Commands;

use App\Enums\CompanyMetrics\CompanyMetricRequestTypes;
use App\Enums\CompanyMetrics\CompanyMetricSources;
use App\Enums\Prospects\ProspectSource;
use App\Enums\Prospects\ProspectStatus;
use App\Models\CompanyMetric;
use App\Models\Odin\Company;
use App\Models\Prospects\NewBuyerProspect;
use App\Services\ShovelsApiService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class ImportLocationPermitMetrics extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:import-location-permit-metrics
                            {api_key : Shovels API key for authentication}
                            {geo_id : Geographic ID (zip code, city, county, etc.)}
                            {--date-from= : Start date (YYYY-MM-DD), defaults to last month}
                            {--date-to= : End date (YYYY-MM-DD), defaults to today}
                            {--permit-tags=* : Industry tags to filter permits (e.g., solar, hvac, reroof)}';


    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Search for permits by location and industry tags, then match contractors to companies in database via website';

    protected ShovelsApiService $shovelsApiService;

    public function __construct(ShovelsApiService $shovelsApiService)
    {
        parent::__construct();
        $this->shovelsApiService = $shovelsApiService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $apiKey = $this->argument('api_key');
        $geoId = $this->argument('geo_id');
        $permitTags = $this->option('permit-tags');

        // Set API key for the service
        $this->shovelsApiService->setApiKey($apiKey);

        // Calculate date range
        $endDate = $this->option('date-to')
            ? Carbon::parse($this->option('date-to'))
            : Carbon::now();

        $startDate = $this->option('date-from')
            ? Carbon::parse($this->option('date-from'))
            : Carbon::now()->subMonths(3); // Look back 3 months for more data

        $this->info("Searching for permits in location: {$geoId}");
        $this->info("Date range: {$startDate->format('Y-m-d')} to {$endDate->format('Y-m-d')}");

        if (!empty($permitTags)) {
            $this->info("Permit tags filter: " . implode(', ', $permitTags));
        } else {
            $this->info("Permit tags filter: None (all industries)");
        }

        try {
            // Search for contractors by location with pagination
            $this->info('Starting paginated search through Shovels API...');

            $allContractors = [];
            $cursor = null;
            $pageNumber = 1;
            $totalPages = 0;

            do {
                $this->info("Fetching page {$pageNumber}...");

                $response = $this->shovelsApiService->searchContractorsByLocation(
                    $geoId,
                    $startDate,
                    $endDate,
                    $permitTags,
                    $cursor
                );

                $contractors = $response['items'] ?? [];
                $nextCursor = $response['next_cursor'] ?? null;

                $this->info("Page {$pageNumber}: Found " . count($contractors) . " contractors");

                if (!empty($contractors)) {
                    $allContractors = array_merge($allContractors, $contractors);
                }

                $cursor = $nextCursor;
                $pageNumber++;

                // Safety check to prevent infinite loops
                if ($pageNumber > 100) {
                    $this->warn('Reached maximum page limit (100). Stopping pagination.');
                    break;
                }

            } while ($cursor !== null);

            $totalPages = $pageNumber - 1;
            $this->info("Pagination completed. Fetched {$totalPages} pages with " . count($allContractors) . " total contractors.");
            $this->info('Last request URL: ' . $this->shovelsApiService->getLastRequestUrl());

            if (empty($allContractors)) {
                $this->warn('No contractors found for the specified location and date range.');
                $this->info('This could mean:');
                $this->info('  - No permits were filed in this location during the date range');
                $this->info('  - The geo_id format is not recognized by the API');
                $this->info('  - The date range is too narrow');
                $this->info('');
                $this->info('Try:');
                $this->info('  - A broader date range (e.g., --date-from="2024-01-01")');
                $this->info('  - A different geo_id format (zip code, city name, county)');
                $this->info('  - Check the logs for the actual API response');
                return self::SUCCESS;
            }

            $this->info("Processing {" . count($allContractors) . "} contractors. Attempting to match with companies...");

            $matchedCount = 0;
            $prospectCount = 0;
            $processedCount = 0;

            foreach ($allContractors as $contractor) {
                $processedCount++;
                $this->info("Processing contractor {$processedCount}/" . count($allContractors) . ": " . ($contractor['name'] ?? 'Unknown'));

                // Try to match contractor to company
                $company = $this->matchContractorToCompany($contractor);

                if ($company) {
                    $this->info("✓ Matched to company: {$company->name} (ID: {$company->id})");

                    // Store the permit metrics data
                    $this->storeCompanyMetrics($company, $contractor, $geoId);
                    $matchedCount++;
                } else {
                    // Try to create a prospect using email domain or other data
                    $prospect = $this->createProspectFromContractor($contractor, $geoId, $permitTags);

                    if ($prospect) {
                        $this->info("✓ Created prospect: {$prospect->company_name} (ID: {$prospect->id})");
                        $prospectCount++;
                    } else {
                        $this->warn("✗ No matching company found and insufficient data for prospect: " . ($contractor['name'] ?? 'Unknown'));
                    }
                }
            }

            $this->info("\nImport completed:");
            $this->info("✓ Pages fetched: {$totalPages}");
            $this->info("✓ Contractors processed: {$processedCount}");
            $this->info("✓ Companies matched: {$matchedCount}");
            $this->info("✓ Prospects created: {$prospectCount}");
            $this->info("✗ No action taken: " . ($processedCount - $matchedCount - $prospectCount));

            return self::SUCCESS;

        } catch (\Exception $e) {
            $this->error("Error during import: " . $e->getMessage());
            $this->error("Exception class: " . get_class($e));
            return self::FAILURE;
        }
    }

    /**
     * Map permit tag tally to industry service IDs
     *
     * @param array $tagTally Array with tag names as keys and counts as values
     * @return array Array of industry service IDs
     */
    private function mapPermitTageToIndustryService(array $tagTally): array
    {
        $industryIds = [];

        // Define mapping from permit tags to industry service IDs
        $tagToIndustryMap = [
            'solar' => 1,
            'roofing' => 5,
            'reroof' => 5,
            'windows' => 21,
            'siding' => 22,
            'hvac' => 15,
            'heat_pump' => 15,
            'bathrooms' => 30,
            'remodeling' => 30,
        ];

        // Process each tag in the tally
        foreach ($tagTally as $tag => $count) {
            if (isset($tagToIndustryMap[$tag]) && $count > 0) {
                $industryId = $tagToIndustryMap[$tag];

                // Add industry ID if not already present
                if (!in_array($industryId, $industryIds)) {
                    $industryIds[] = $industryId;
                }

                $this->info("    Mapped tag '{$tag}' (count: {$count}) to industry ID: {$industryId}");
            }
        }

        // If no tags mapped, default to solar (ID: 1)
        if (empty($industryIds)) {
            $industryIds[] = 1;
            $this->info("    No tags mapped to supported industries, defaulting to solar (ID: 1)");
            $this->info("    Available tags: " . implode(', ', array_keys($tagTally)));
        }

        return $industryIds;
    }
    /**
     * Attempt to match a contractor to a company in the database
     */
    private function matchContractorToCompany(array $contractor): ?Company
    {
        // Extract contractor details
        $contractorName = $contractor['name'] ?? null;
        $contractorWebsite = $contractor['website'] ?? null;
        $contractorEmail = $contractor['primary_email'] ?? $contractor['email'] ?? null;

        $this->info("  Contractor details:");
        $this->info("    Name: " . ($contractorName ?? 'N/A'));
        $this->info("    Website: " . ($contractorWebsite ?? 'N/A'));
        $this->info("    Email: " . ($contractorEmail ?? 'N/A'));
        $this->info("    Primary Industry: " . ($contractor['primary_industry'] ?? 'N/A'));
        $this->info("    Permit Count: " . ($contractor['permit_count'] ?? 'N/A'));
        $this->info("    Total Job Value: " . ($contractor['total_job_value'] ?? 'N/A'));
        $this->info("    Avg Job Value: " . ($contractor['avg_job_value'] ?? 'N/A'));
        $this->info("    Tag Tally: " . json_encode($contractor['tag_tally'] ?? []));

        // Strategy 1: Match by cleaned website/LinkedIn URL
        if ($contractorWebsite) {
            $cleanedContractorWebsite = $this->cleanWebsiteUrl($contractorWebsite);
            $this->info("    Cleaned Website: {$cleanedContractorWebsite}");

            $company = Company::where(function($query) use ($cleanedContractorWebsite) {
                $query->whereRaw('LOWER(TRIM(TRAILING "/" FROM REPLACE(REPLACE(website, "https://", ""), "http://", ""))) = ?', [strtolower($cleanedContractorWebsite)])
                      ->orWhereRaw('LOWER(TRIM(TRAILING "/" FROM REPLACE(REPLACE(website_verified_url, "https://", ""), "http://", ""))) = ?', [strtolower($cleanedContractorWebsite)]);
            })->first();

            if ($company) {
                $this->info("    ✓ Matched by website");
                return $company;
            }
        }

        // Strategy 2: Match by email domain
        if ($contractorEmail && str_contains($contractorEmail, '@')) {
            $emailDomain = strtolower(substr($contractorEmail, strpos($contractorEmail, '@') + 1));
            $this->info("    Email domain: {$emailDomain}");

            $company = Company::where(function($query) use ($emailDomain) {
                $query->whereRaw('LOWER(TRIM(TRAILING "/" FROM REPLACE(REPLACE(website, "https://", ""), "http://", ""))) = ?', [$emailDomain])
                      ->orWhereRaw('LOWER(TRIM(TRAILING "/" FROM REPLACE(REPLACE(website_verified_url, "https://", ""), "http://", ""))) = ?', [$emailDomain]);
            })->first();

            if ($company) {
                $this->info("    ✓ Matched by email domain");
                return $company;
            }
        }

        // Strategy 3: Fuzzy match by name (exact match first, then partial)
        if ($contractorName) {
            // Exact name match
            $company = Company::whereRaw('LOWER(name) = ?', [strtolower($contractorName)])->first();
            if ($company) {
                $this->info("    ✓ Matched by exact name");
                return $company;
            }
        }

        return null;
    }

    /**
     * Clean website URL for comparison
     */
    private function cleanWebsiteUrl(string $url): string
    {
        // Remove protocol
        $cleaned = preg_replace('/^https?:\/\//', '', $url);

        // Remove www
        $cleaned = preg_replace('/^www\./', '', $cleaned);

        // Remove trailing slash
        $cleaned = rtrim($cleaned, '/');

        // For LinkedIn URLs, extract the company identifier
        if (str_contains($cleaned, 'linkedin.com/company/')) {
            $cleaned = str_replace('linkedin.com/company/', '', $cleaned);
            $cleaned = explode('/', $cleaned)[0]; // Take only the company identifier
        }

        return strtolower($cleaned);
    }

    /**
     * Create a NewBuyerProspect from contractor data when no company match is found
     */
    private function createProspectFromContractor(array $contractor, string $geoId, array $permitTags = []): ?NewBuyerProspect
    {
        $contractorName = $contractor['name'] ?? null;
        $contractorEmail = $contractor['primary_email'] ?? $contractor['email'] ?? null;
        $contractorWebsite = $contractor['website'] ?? null;
        $contractorPhone = $contractor['primary_phone'] ?? $contractor['phone'] ?? null;
        $permitCount = $contractor['permit_count'] ?? 0;
        $tagTally = $contractor['tag_tally'] ?? [];

        // We need at least a name and either email or website
        if (!$contractorName || (!$contractorEmail && !$contractorWebsite)) {
            $this->info("    ✗ Insufficient data for prospect (need name + email/website)");
            return null;
        }

        // Filter 1: Check permit count threshold
        if ($permitCount < 5) {
            $this->info("    ✗ Contractor has insufficient permits ({$permitCount} < 5 required)");
            return null;
        }

        // Filter 2: Check for personal email domains
        if ($contractorEmail && $this->isPersonalEmailDomain($contractorEmail)) {
            $emailDomain = substr($contractorEmail, strpos($contractorEmail, '@') + 1);
            $this->info("    ✗ Personal email domain detected: {$emailDomain}");
            return null;
        }

        // Filter 3: Check if contractor fits supported industries
        $industryIds = $this->mapPermitTageToIndustryService($tagTally);
        if (empty($industryIds) || $this->isDefaultIndustryOnly($industryIds, $tagTally)) {
            $this->info("    ✗ Contractor does not fit supported industries");
            return null;
        }

        // Extract website from email domain if no website provided
        $websiteFromEmail = null;
        if ($contractorEmail && !$contractorWebsite) {
            $emailDomain = substr($contractorEmail, strpos($contractorEmail, '@') + 1);
            $websiteFromEmail = $emailDomain;
            $this->info("    Using email domain as website: {$websiteFromEmail}");
        }

        // Check if prospect already exists
        $existingProspect = NewBuyerProspect::where('company_name', $contractorName)
            ->orWhere(function($query) use ($contractorEmail, $websiteFromEmail) {
                if ($contractorEmail) {
                    $query->where('decision_maker_email', $contractorEmail);
                }
                if ($websiteFromEmail) {
                    $query->orWhere('company_website', $websiteFromEmail);
                }
            })
            ->first();

        if ($existingProspect) {
            $this->info("    ✗ Prospect already exists: {$existingProspect->company_name} (ID: {$existingProspect->id})");
            return $existingProspect;
        }

        // Log tag tally for debugging
        $tagTally = $contractor['tag_tally'] ?? [];
        if (!empty($tagTally)) {
            $this->info("    Tag tally: " . json_encode($tagTally));
        } else {
            $this->info("    No tag tally data available");
        }

        // Create new prospect
        $prospectData = [
            'company_name' => $contractorName,
            'website' => $contractorWebsite ?: $websiteFromEmail,
            'email' => $contractorEmail,
            'phone' => $contractorPhone,
            'source' => ProspectSource::SHOVELS_API,
            'status' => ProspectStatus::INITIAL,
            'notes' => $this->buildProspectNotes($contractor, $geoId, $permitTags),
            'industry_service_ids' => $this->mapPermitTageToIndustryService($tagTally),
            'data' => [
                'shovels_contractor_data' => $contractor,
                'search_geo_id' => $geoId,
                'permit_tags' => $permitTags,
                'search_date' => Carbon::now()->toISOString(),
                'permit_count' => $contractor['permit_count'] ?? 0,
                'total_job_value' => $contractor['total_job_value'] ?? 0,
                'avg_job_value' => $contractor['avg_job_value'] ?? 0,
            ]
        ];

        $prospect = NewBuyerProspect::create($prospectData);

        $this->info("    ✓ Created new prospect with " . ($contractorWebsite ? 'LinkedIn URL' : 'email domain'));

        return $prospect;
    }

    /**
     * Build notes for the prospect based on contractor data
     */
    private function buildProspectNotes(array $contractor, string $geoId, array $permitTags): string
    {
        $notes = [];

        $notes[] = "Found via Shovels API search in location: {$geoId}";

        if (!empty($permitTags)) {
            $notes[] = "Industry tags: " . implode(', ', $permitTags);
        }

        if (isset($contractor['permit_count'])) {
            $notes[] = "Permit count: " . $contractor['permit_count'];
        }

        if (isset($contractor['total_job_value'])) {
            $notes[] = "Total job value: $" . number_format($contractor['total_job_value']);
        }

        if (isset($contractor['primary_industry'])) {
            $notes[] = "Primary industry: " . $contractor['primary_industry'];
        }

        if (isset($contractor['employee_count'])) {
            $notes[] = "Employee count: " . $contractor['employee_count'];
        }

        if (isset($contractor['revenue'])) {
            $notes[] = "Revenue: " . $contractor['revenue'];
        }

        return implode("\n", $notes);
    }

    /**
     * Check if email domain is a personal/consumer email provider
     */
    private function isPersonalEmailDomain(string $email): bool
    {
        $domain = strtolower(substr($email, strpos($email, '@') + 1));

        $personalDomains = [
            'gmail.com',
            'yahoo.com',
            'hotmail.com',
            'outlook.com',
            'aol.com',
            'comcast.net',
            'verizon.net',
            'att.net',
            'cox.net',
            'charter.net',
            'earthlink.net',
            'sbcglobal.net',
            'live.com',
            'msn.com',
            'icloud.com',
            'me.com',
            'mac.com',
            'protonmail.com',
            'yandex.com',
            'mail.com',
            'zoho.com',
        ];

        return in_array($domain, $personalDomains);
    }

    /**
     * Check if the contractor only maps to default industry (indicating no real industry match)
     */
    private function isDefaultIndustryOnly(array $industryIds, array $tagTally): bool
    {
        if (count($industryIds) === 1 && $industryIds[0] === 1) {
            $tagToIndustryMap = [
                'solar' => 1,
                'roofing' => 5,
                'reroof' => 5,
                'windows' => 21,
                'siding' => 22,
                'hvac' => 15,
                'heat_pump' => 15,
                'bathrooms' => 30,
                'remodeling' => 30,
            ];

            // Check if any of the contractor's tags actually map to our supported industries
            foreach ($tagTally as $tag => $count) {
                if (isset($tagToIndustryMap[$tag]) && $count > 0) {
                    return false; // Found a real industry match
                }
            }

            // Only default industry and no real tag matches
            return true;
        }

        // Multiple industries or non-default single industry = valid
        return false;
    }

    /**
     * Store company metrics data
     */
    private function storeCompanyMetrics(Company $company, array $contractorData, string $geoId): void
    {
        // Add metadata to the contractor data
        $metricsData = array_merge($contractorData, [
            'search_geo_id' => $geoId,
            'search_date' => Carbon::now()->toISOString(),
            'match_strategy' => 'location_based_search'
        ]);

        CompanyMetric::create([
            CompanyMetric::FIELD_COMPANY_ID => $company->id,
            CompanyMetric::FIELD_SOURCE => CompanyMetricSources::SHOVELS->value,
            CompanyMetric::FIELD_REQUEST_TYPE => CompanyMetricRequestTypes::PERMIT_METRICS->value,
            CompanyMetric::FIELD_REQUEST_URL => $this->shovelsApiService->getLastRequestUrl(),
            CompanyMetric::FIELD_REQUEST_RESPONSE => $metricsData,
        ]);
    }
}
