<?php

namespace App\Console\Commands\CompanyMergeTool;

use App\Models\Legacy\EloquentCompanyAddress;
use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Legacy\EloquentInvoice;
use App\Models\Legacy\EloquentInvoiceItem;
use App\Models\Legacy\EloquentUser;
use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\CompanyUser;
use Closure;
use Illuminate\Support\Facades\DB;

/**
 * All model handlers MUST include the primary key in the $changes and $undo entries
 *  or mysql upsert will fail
 * The Admin2 proposed changes are passed in for models which will just follow <PERSON>'s lead and
 *  reassign the legacy company id for any model A2 proposed to reassign
 */
class CompanyMergeLegacyModelHandlers extends BaseMergeHandler
{
    public function __construct(
        protected int $sourceCompanyId,
        protected int $targetCompanyId,
        protected array $proposedAdminChanges
    )
    {
        parent::__construct($this->sourceCompanyId, $this->targetCompanyId);
    }

    /**
     * @param Closure $changeProcessor
     * @return void
     */
    public function processHandlers(Closure $changeProcessor): void
    {
        $this->handleLeadCampaigns($changeProcessor);
        $this->handleCompanyUsersAndContacts($changeProcessor);
        $this->handleEloquentCompanyAddresses($changeProcessor);
        $this->handleInvoicesAndItems($changeProcessor);
    }

    protected function handleInvoicesAndItems(Closure $processor): void
    {
        $invoices = $this->getLegacyTable(EloquentInvoice::TABLE)
            ->where(EloquentInvoice::COMPANY_ID, $this->sourceCompanyId)
            ->select([EloquentInvoice::INVOICE_ID, EloquentInvoice::COMPANY_ID])
            ->get();
        $invoiceIds = $invoices->pluck(EloquentInvoice::INVOICE_ID)
            ->toArray();
        $invoiceItems = $this->getLegacyTable(EloquentInvoiceItem::TABLE)
            ->where(EloquentInvoiceItem::REL_TYPE, EloquentInvoiceItem::REL_TYPE_COMPANY)
            ->where(EloquentInvoiceItem::REL_ID, $this->sourceCompanyId)
            ->whereIn(EloquentInvoiceItem::INVOICE_ID, $invoiceIds)
            ->select([EloquentInvoiceItem::INVOICE_ITEM_ID, EloquentInvoiceItem::REL_ID, EloquentInvoiceItem::INVOICE_ID])
            ->get();

        $invoiceChangeFunction = $this->getGenericChangeHandler(EloquentInvoice::INVOICE_ID, EloquentInvoice::COMPANY_ID);
        $invoiceUndoFunction = $this->getGenericUndoHandler(EloquentInvoice::INVOICE_ID, EloquentInvoice::COMPANY_ID);
        $invoiceMessageFunction = fn($v) => "Reassigning EloquentInvoice " . $v->{EloquentInvoice::INVOICE_ID} . " and related EloquentInvoiceItems";

        $invoiceItemChangeFunction = $this->getGenericChangeHandler(EloquentInvoiceItem::INVOICE_ITEM_ID, EloquentInvoiceItem::REL_ID);
        $invoiceItemUndoFunction = $this->getGenericUndoHandler(EloquentInvoiceItem::INVOICE_ITEM_ID, EloquentInvoiceItem::REL_ID);

        $processor(EloquentInvoice::TABLE, $invoices, $invoiceChangeFunction, $invoiceUndoFunction, $invoiceMessageFunction);
        $processor(EloquentInvoiceItem::TABLE, $invoiceItems, $invoiceItemChangeFunction, $invoiceItemUndoFunction);
    }

    /**
     * There is no legacy id on CompanyLocation to follow, so just reassign all as non-default
     */
    protected function handleEloquentCompanyAddresses(Closure $processor): void
    {
        $targetAddressIds = $this->getLegacyTable(EloquentCompanyAddress::TABLE)
            ->where(EloquentCompanyAddress::FIELD_COMPANY_ID, $this->targetCompanyId)
            ->pluck(EloquentCompanyAddress::FIELD_ADDRESS_ID)
            ->toArray();
        $companyAddresses = $this->getLegacyTable(EloquentCompanyAddress::TABLE)
            ->where(EloquentCompanyAddress::FIELD_COMPANY_ID, $this->sourceCompanyId)
            ->whereNotIn(EloquentCompanyAddress::FIELD_ADDRESS_ID, $targetAddressIds)
            ->select([EloquentCompanyAddress::FIELD_COMPANY_ID, EloquentCompanyAddress::FIELD_ADDRESS_ID, EloquentCompanyAddress::FIELD_NAME, EloquentCompanyAddress::FIELD_IS_DEFAULT])
            ->get();

        $changeFunction = fn($v) => [
            EloquentCompanyAddress::FIELD_COMPANY_ID => $this->targetCompanyId,
            EloquentCompanyAddress::FIELD_ADDRESS_ID => $v->{EloquentCompanyAddress::FIELD_ADDRESS_ID},
            EloquentCompanyAddress::FIELD_IS_DEFAULT => false,
        ];
        $undoFunction = fn($v) => [
            EloquentCompanyAddress::FIELD_COMPANY_ID => $this->sourceCompanyId,
            EloquentCompanyAddress::FIELD_ADDRESS_ID => $v->{EloquentCompanyAddress::FIELD_ADDRESS_ID},
            EloquentCompanyAddress::FIELD_IS_DEFAULT => $v->{EloquentCompanyAddress::FIELD_IS_DEFAULT},
        ];
        $messageFunction = fn($v) => "Reassigning EloquentCompanyAddress " . $v->{EloquentCompanyAddress::FIELD_NAME};

        $processor(EloquentCompanyAddress::TABLE, $companyAddresses, $changeFunction, $undoFunction, $messageFunction);
    }

    protected function handleCompanyUsersAndContacts(Closure $processor): void
    {
        $legacyIds = $this->getLegacyIdsFromProposedAdminChanges(CompanyUser::TABLE, CompanyUser::FIELD_ID, CompanyUser::FIELD_LEGACY_ID);

        $legacyUsers = $this->getLegacyTable(EloquentUser::TABLE)
            ->whereIn(EloquentUser::ID, $legacyIds)
            ->where(EloquentUser::COMPANY_ID, $this->sourceCompanyId)
            ->select([EloquentUser::ID, EloquentUser::COMPANY_ID, EloquentUser::FIRST_NAME, EloquentUser::LAST_NAME])
            ->get();
        $legacyContacts = $this->getLegacyTable(EloquentCompanyContact::TABLE)
            ->whereIn(EloquentCompanyContact::FIELD_CONTACT_ID, $legacyIds)
            ->where(EloquentCompanyContact::FIELD_COMPANY_ID, $this->sourceCompanyId)
            ->select([EloquentCompanyContact::FIELD_CONTACT_ID, EloquentCompanyContact::FIELD_COMPANY_ID, EloquentCompanyContact::FIELD_FIRST_NAME, EloquentCompanyContact::FIELD_LAST_NAME])
            ->get();

        $contactChangeFunction = $this->getGenericChangeHandler(EloquentCompanyContact::FIELD_CONTACT_ID, EloquentCompanyContact::FIELD_COMPANY_ID);
        $contactUndoFunction = $this->getGenericUndoHandler(EloquentCompanyContact::FIELD_CONTACT_ID, EloquentCompanyContact::FIELD_COMPANY_ID);
        $contactMessageFunction = fn($v) => "Reassigning CompanyContact " . $v->{EloquentCompanyContact::FIELD_FIRST_NAME} . " " . $v->{EloquentCompanyContact::FIELD_LAST_NAME};

        $userChangeFunction = $this->getGenericChangeHandler(EloquentUser::ID, EloquentUser::COMPANY_ID);
        $userUndoFunction = $this->getGenericUndoHandler(EloquentUser::ID, EloquentUser::COMPANY_ID);
        $userMessageFunction = fn($v) => "Reassigning EloquentUser " . $v->{EloquentUser::FIRST_NAME} .  " " . $v->{EloquentUser::LAST_NAME};

        $processor(EloquentCompanyContact::TABLE, $legacyContacts, $contactChangeFunction, $contactUndoFunction, $contactMessageFunction);
        $processor(EloquentUser::TABLE, $legacyUsers, $userChangeFunction, $userUndoFunction, $userMessageFunction);
    }

    /**
     * @param Closure $processor
     * @return void
     */
    protected function handleLeadCampaigns(Closure $processor): void
    {
        $remapCampaigns = $this->getLegacyTable(LeadCampaign::TABLE)
            ->where(LeadCampaign::COMPANY_ID, $this->sourceCompanyId)
            ->select([LeadCampaign::ID, LeadCampaign::COMPANY_ID, LeadCampaign::NAME])
            ->get();

        $changeFunction = $this->getGenericChangeHandler(LeadCampaign::ID, LeadCampaign::COMPANY_ID);
        $undoFunction = $this->getGenericUndoHandler(LeadCampaign::ID, LeadCampaign::COMPANY_ID);
        $messageFunction = fn($v) => "Reassigning LeadCampaign " . $v->name ?? "Unnamed Campaign";

        $processor(LeadCampaign::TABLE, $remapCampaigns, $changeFunction, $undoFunction, $messageFunction);
    }

    /**
     * Get legacy IDs from an Admin2 table based on the proposed Admin2 changes already processed
     *
     * @param string $admin2Table
     * @param string $modelIdColumn
     * @param string $modelLegacyIdColumn
     * @return array
     */
    protected function getLegacyIdsFromProposedAdminChanges(string $admin2Table, string $modelIdColumn, string $modelLegacyIdColumn): array
    {
        $adminChanges = $this->proposedAdminChanges[$admin2Table] ?? [];
        $targetIds = array_map(fn($v) => $v[$modelIdColumn] ?? null, $adminChanges);

        return DB::table($admin2Table)
            ->whereIn($modelIdColumn, $targetIds)
            ->select([$modelLegacyIdColumn])
            ->pluck($modelLegacyIdColumn)
            ->filter()
            ->toArray();
    }
}
