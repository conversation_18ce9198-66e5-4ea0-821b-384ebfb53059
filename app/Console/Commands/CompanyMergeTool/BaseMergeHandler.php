<?php

namespace App\Console\Commands\CompanyMergeTool;

use App\Services\DatabaseHelperService;
use Closure;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

abstract class BaseMergeHandler
{
    protected function __construct(
        protected int $sourceCompanyId,
        protected int $targetCompanyId,
    ) {}
    /**
     * @param Closure $changeProcessor
     * @return void
     */
    abstract public function processHandlers(Closure $changeProcessor): void;

    /**
     * Return a generic Change handler for a model which is only updating the company_id column
     * Must provide Primary Key as the first $idColumn argument
     *
     * @param string $idColumn
     * @param string $companyIdColumn
     * @return Closure
     */
    protected function getGenericChangeHandler(string $idColumn = 'id', string $companyIdColumn = 'company_id'): Closure
    {
        return fn($v) => [
            $idColumn => $v->{$idColumn},
            $companyIdColumn => $this->targetCompanyId,
        ];
    }

    /**
     * Return a generic Undo handler for a model which is only updating the company_id column
     * Must provide Primary Key as the first $idColumn argument
     *
     * @param string $idColumn
     * @param string $companyIdColumn
     * @return Closure
     */
    protected function getGenericUndoHandler(string $idColumn = 'id', string $companyIdColumn = 'company_id'): Closure
    {
        return fn($v) => [
            $idColumn => $v->{$idColumn},
            $companyIdColumn => $this->sourceCompanyId,
        ];
    }

    /**
     * @param string $table
     * @return Builder
     */
    protected function getLegacyTable(string $table): Builder
    {
        return DB::table(DatabaseHelperService::readOnlyDatabase() .'.'. $table);
    }
}
