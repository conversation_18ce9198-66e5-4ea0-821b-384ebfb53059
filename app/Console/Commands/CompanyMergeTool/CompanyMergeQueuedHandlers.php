<?php

namespace App\Console\Commands\CompanyMergeTool;

use App\Models\CompanyMergeRecord;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Odin\Company;
use App\Models\Odin\ProductAssignment;
use Closure;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class CompanyMergeQueuedHandlers extends BaseMergeHandler
{
    const int CHUNK_SIZE = 5000;

    protected array $undoProductAssignments = [];
    protected array $undoQuoteCompanies = [];

    protected Company $sourceCompany;
    protected Company $targetCompany;

    public function __construct(
        protected CompanyMergeRecord $companyMergeRecord,
    )
    {
        $this->sourceCompany = $this->companyMergeRecord->sourceCompany;
        $this->targetCompany = $this->companyMergeRecord->targetCompany;

        parent::__construct($this->sourceCompany->id, $this->targetCompany->id);
    }

    /**
     * Custom, queued processing for ProductAssignments to avoid lock timeout on large companies
     *  and split the undo payload
     * @return bool
     */
    public function customProcessing(): bool
    {
        DB::beginTransaction();

        try {
            $updated = DB::table(ProductAssignment::TABLE)
                ->where(ProductAssignment::FIELD_COMPANY_ID, $this->sourceCompanyId)
                ->select([ProductAssignment::FIELD_ID, ProductAssignment::FIELD_COMPANY_ID, ProductAssignment::FIELD_LEGACY_ID])
                ->chunkById(self::CHUNK_SIZE, function (Collection $chunk) {
                    $this->handleProductAssignments($chunk->reduce(function($output, $productAssignment) {
                        $output[] = [
                            ProductAssignment::FIELD_ID         => $productAssignment->id,
                            ProductAssignment::FIELD_COMPANY_ID => $this->targetCompany->id,
                        ];
                        return $output;
                    }, []));

                    $this->handleEloquentQuoteCompanies($chunk->reduce(function($output, $productAssignment) {
                        $output[] = [
                            EloquentQuoteCompany::QUOTE_COMPANY_ID => $productAssignment->legacy_id,
                            EloquentQuoteCompany::COMPANY_ID       => $this->targetCompany->legacy_id,
                        ];
                        return $output;
                    }, []));
                });

            if ($updated && $this->commitQueuedUndoChanges()) {
                DB::commit();

                return true;
            }
        }
        catch (Exception $e) {
            logger()->error($e->getMessage());
        }

        DB::rollBack();

        return false;
    }

    /**
     * @return bool
     */
    public function customUndo(): bool
    {
        DB::beginTransaction();

        try {
            $payload = $this->companyMergeRecord->queued_undo_payload;

            $chunks = array_chunk(($payload[CompanyMergeService::DATABASE_ADMIN2] ?? []), self::CHUNK_SIZE);
            foreach($chunks as $chunk) {
                $restoreDatabasePayload = array_map(fn($id) => [
                    ProductAssignment::FIELD_ID => $id,
                    ProductAssignment::FIELD_COMPANY_ID => $this->sourceCompany->id,
                ], $chunk);

                $this->handleProductAssignments($restoreDatabasePayload, true);
            }

            $chunks = array_chunk(($payload[CompanyMergeService::DATABASE_LEGACY] ?? []), self::CHUNK_SIZE);
            foreach($chunks as $chunk) {
                $restoreDatabasePayload = array_map(fn($id) => [
                    EloquentQuoteCompany::QUOTE_COMPANY_ID => $id,
                    EloquentQuoteCompany::COMPANY_ID       => $this->sourceCompany->legacy_id,
                ], $chunk);

                $this->handleEloquentQuoteCompanies($restoreDatabasePayload, true);
            }

            $chunks = null;

            if ($this->finaliseUndo()) {
                DB::commit();

                return true;
            }
        }
        catch (Exception $e) {
            logger()->error($e->getMessage());
        }

        DB::rollBack();

        return false;
    }

    public function processHandlers(Closure $changeProcessor): void
    {}

    /**
     * @param array $productAssignmentData
     * @param bool|null $undo
     * @return void
     */
    protected function handleProductAssignments(array $productAssignmentData, ?bool $undo = false): void
    {
        DB::table(ProductAssignment::TABLE)
            ->upsert($productAssignmentData, [], [ProductAssignment::FIELD_COMPANY_ID]);

        if (!$undo) {
            foreach ($productAssignmentData as $productAssignment) {
                $this->undoProductAssignments[] = $productAssignment['id'];
            }
        }
    }

    /**
     * @param array $quoteCompanyData
     * @param bool|null $undo
     * @return void
     */
    protected function handleEloquentQuoteCompanies(array $quoteCompanyData, ?bool $undo = false): void
    {
        $this->getLegacyTable(EloquentQuoteCompany::TABLE)
            ->upsert($quoteCompanyData, [], [EloquentQuoteCompany::COMPANY_ID]);

        if (!$undo) {
            foreach ($quoteCompanyData as $quoteCompany) {
                $this->undoQuoteCompanies[] = $quoteCompany[EloquentQuoteCompany::QUOTE_COMPANY_ID];
            }
        }
    }

    /**
     * @return bool
     */
    protected function commitQueuedUndoChanges(): bool
    {
        $payload = [
            CompanyMergeService::DATABASE_ADMIN2 => $this->undoProductAssignments,
            CompanyMergeService::DATABASE_LEGACY => $this->undoQuoteCompanies,
        ];

        return !!$this->companyMergeRecord->update([
            CompanyMergeRecord::FIELD_QUEUED_PAYLOAD => $payload,
            CompanyMergeRecord::FIELD_COMPLETED_AT   => now(),
        ]);
    }

    /**
     * @return bool
     */
    protected function finaliseUndo(): bool
    {
        return $this->companyMergeRecord
            ->delete();
    }
}
