<?php

namespace App\Console\Commands\CompanyMergeTool;

use App\Models\Action;
use App\Models\ActivityFeed;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\CompanyContract;
use App\Models\CompanyMetric;
use App\Models\License;
use App\Models\Odin\Address;
use App\Models\Odin\CompanyExpertReview;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\CompanyMediaAsset;
use App\Models\Odin\CompanyReview;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\GenericProfitabilityAssumptionConfiguration;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCampaign;
use App\Models\Sales\Task;
use Closure;
use Illuminate\Support\Facades\DB;

/**
 * All model handlers MUST include the primary key in the $changes and $undo entries
 *  or mysql upsert will fail
 */
class CompanyMergeModelHandlers extends BaseMergeHandler
{
    public function __construct(
        protected int $sourceCompanyId,
        protected int $targetCompanyId,
        protected Closure $proposedChangesFunction,
    )
    {
        parent::__construct($this->sourceCompanyId, $this->targetCompanyId);
    }

    /**
     * @param Closure $changeProcessor
     * @return void
     */
    public function processHandlers(Closure $changeProcessor): void
    {
        $this->handleCompanyCampaigns($changeProcessor);
        $this->handleCompanyUsers($changeProcessor);
        $this->handleProductCampaigns($changeProcessor);
        $this->handleCompanyReviews($changeProcessor);
        $this->handleCompanyExpertReviews($changeProcessor);
        $this->handleCompanyContract($changeProcessor);
        $this->handleGenericProfitabilityAssumptionConfiguration($changeProcessor);
        $this->handleCompanyMediaAssets($changeProcessor);
        $this->handleCompanyLicenses($changeProcessor);
        $this->handleActivityFeedItems($changeProcessor);
        $this->handleCompanyMetric($changeProcessor);
        $this->handleActions($changeProcessor);
        $this->handleTasks($changeProcessor);
        $this->handleCompanyLocations($changeProcessor);
    }

    protected function handleCompanyLocations(Closure $processor): void
    {
        $targetZipCodes = DB::table(CompanyLocation::TABLE)
            ->join(Address::TABLE, Address::TABLE .'.'. Address::FIELD_ID, '=', CompanyLocation::TABLE .'.'. CompanyLocation::FIELD_ADDRESS_ID)
            ->where(CompanyLocation::FIELD_COMPANY_ID, $this->sourceCompanyId)
            ->pluck(Address::TABLE .'.'. Address::FIELD_ZIP_CODE)
            ->toArray();

        $locations = DB::table(CompanyLocation::TABLE)
            ->join(Address::TABLE, Address::TABLE .'.'. Address::FIELD_ID, '=', CompanyLocation::TABLE .'.'. CompanyLocation::FIELD_ADDRESS_ID)
            ->where(CompanyLocation::FIELD_COMPANY_ID, $this->sourceCompanyId)
            ->whereNotIn(Address::TABLE .'.'. Address::FIELD_ZIP_CODE, $targetZipCodes)
            ->select([CompanyLocation::TABLE .'.'. CompanyLocation::FIELD_ID,
                      CompanyLocation::TABLE .'.'. CompanyLocation::FIELD_COMPANY_ID,
                      Address::TABLE .'.'. Address::FIELD_ZIP_CODE,
                      CompanyLocation::TABLE .'.'. CompanyLocation::FIELD_IS_PRIMARY,
                      CompanyLocation::TABLE .'.'. CompanyLocation::FIELD_NAME,
            ])->get();

        $changeFunction = fn($v) => [
            CompanyLocation::FIELD_ID => $v->id,
            CompanyLocation::FIELD_COMPANY_ID => $this->targetCompanyId,
            CompanyLocation::FIELD_IS_PRIMARY => false,
        ];
        $undoFunction = fn($v) => [
            CompanyLocation::FIELD_ID => $v->id,
            CompanyLocation::FIELD_COMPANY_ID => $this->sourceCompanyId,
            CompanyLocation::FIELD_IS_PRIMARY => $v->is_primary,
        ];
        $messageFunction = fn($v) => "Reassigning CompanyLocation " . $v->{CompanyLocation::FIELD_NAME};

        $processor(CompanyLocation::TABLE, $locations, $changeFunction, $undoFunction, $messageFunction);
    }

    protected function handleTasks(Closure $processor): void
    {
        $tasks = DB::table(Task::TABLE)
            ->where(Task::VIRTUAL_FIELD_MANUAL_COMPANY_ID, $this->sourceCompanyId)
            ->select([Task::FIELD_ID, Task::VIRTUAL_FIELD_MANUAL_COMPANY_ID, Task::FIELD_SUBJECT])
            ->get();

        $changeHandler = fn($v) => [
            Task::FIELD_ID => $v->id,
            "$.company_id|payload" => $this->targetCompanyId,
        ];
        $undoHandler = fn($v) => [
            Task::FIELD_ID => $v->id,
            "$.company_id|payload" => $this->sourceCompanyId,
        ];
        $messageHandler = fn($v) => "Reassigning Task " . $v->{Task::FIELD_SUBJECT};

        $processor(Task::TABLE, $tasks, $changeHandler, $undoHandler, $messageHandler);
    }

    protected function handleActions(Closure $processor): void
    {
        $actions = DB::table(Action::TABLE)
            ->where(Action::FIELD_FOR_RELATION_TYPE, Action::RELATION_TYPE_COMPANY)
            ->where(Action::FIELD_FOR_ID, $this->sourceCompanyId)
            ->select([Action::FIELD_ID, Action::FIELD_FOR_ID, Action::FIELD_SUBJECT])
            ->get();

        $changeHandler = $this->getGenericChangeHandler(Action::FIELD_ID, Action::FIELD_FOR_ID);
        $undoHandler = $this->getGenericUndoHandler(Action::FIELD_ID, Action::FIELD_FOR_ID);
        $messageHandler = fn($v) => "Reassigning Action " . $v->{Action::FIELD_SUBJECT};

        $processor(Action::TABLE, $actions, $changeHandler, $undoHandler, $messageHandler);
    }

    protected function handleCompanyMetric(Closure $processor): void
    {
        $metrics = DB::table(CompanyMetric::TABLE)
            ->where(CompanyMetric::FIELD_COMPANY_ID, $this->sourceCompanyId)
            ->select([CompanyMetric::FIELD_ID, CompanyMetric::FIELD_COMPANY_ID, CompanyMetric::FIELD_REQUEST_TYPE])
            ->get();

        $changeHandler = $this->getGenericChangeHandler();
        $undoHandler = $this->getGenericUndoHandler();
        $messageHandler = fn($v) => "Reassigning CompanyMetric type " . $v->{CompanyMetric::FIELD_REQUEST_TYPE};

        $processor(CompanyMetric::TABLE, $metrics, $changeHandler, $undoHandler, $messageHandler);
    }

    protected function handleActivityFeedItems(Closure $processor): void
    {
        $activityFeeds = DB::table(ActivityFeed::TABLE)
            ->where(ActivityFeed::FIELD_COMPANY_ID, $this->sourceCompanyId)
            ->select([ActivityFeed::FIELD_ID, ActivityFeed::FIELD_COMPANY_ID, ActivityFeed::FIELD_ITEM_TYPE, ActivityFeed::FIELD_ITEM_ID])
            ->get();

        $changeFunction = $this->getGenericChangeHandler();
        $undoFunction = $this->getGenericUndoHandler();
        $messageFunction = fn($v) => "Reassigning ActivityFeed type " . $v->{ActivityFeed::FIELD_ITEM_TYPE} . ", ID " . $v->{ActivityFeed::FIELD_ITEM_ID};

        $processor(ActivityFeed::TABLE, $activityFeeds, $changeFunction, $undoFunction, $messageFunction);
    }

    /**
     * Avoid duplicating by license number
     */
    protected function handleCompanyLicenses(Closure $processor): void
    {
        $targetLicenseNumbers = DB::table(License::TABLE)
            ->where(License::FIELD_COMPANY_ID, $this->targetCompanyId)
            ->pluck(License::FIELD_LICENSE_NUMBER)
            ->toArray();
        $licenses = DB::table(License::TABLE)
            ->where(License::FIELD_COMPANY_ID, $this->sourceCompanyId)
            ->whereNotIn(License::FIELD_LICENSE_NUMBER, $targetLicenseNumbers)
            ->select([License::FIELD_ID, License::FIELD_COMPANY_ID, License::FIELD_NAME])
            ->get();

        $changeFunction = $this->getGenericChangeHandler();
        $undoFunction = $this->getGenericUndoHandler();
        $messageFunction = fn($v) => "Reassigning License " . $v->{License::FIELD_NAME} ?? "unknown license name";

        $processor(License::TABLE, $licenses, $changeFunction, $undoFunction, $messageFunction);

    }

    protected function handleCompanyMediaAssets(Closure $processor): void
    {
        $assets = DB::table(CompanyMediaAsset::TABLE)
            ->where(CompanyMediaAsset::FIELD_COMPANY_ID, $this->sourceCompanyId)
            ->select([CompanyMediaAsset::FIELD_ID, CompanyMediaAsset::FIELD_COMPANY_ID, CompanyMediaAsset::FIELD_NAME])
            ->get();

        $changeFunction = $this->getGenericChangeHandler();
        $undoFunction = $this->getGenericUndoHandler();
        $messageFunction = fn($v) => "Reassigning CompanyMediaAsset " . $v->{CompanyMediaAsset::FIELD_NAME} ?? "unknown name";

        $processor(CompanyMediaAsset::TABLE, $assets, $changeFunction, $undoFunction, $messageFunction);

    }

    /**
     * This will avoid overwriting configurations for existing industry_service_ids (unlikely to happen)
     */
    protected function handleGenericProfitabilityAssumptionConfiguration(Closure $processor): void
    {
        $existingServiceIds = DB::table(GenericProfitabilityAssumptionConfiguration::TABLE)
            ->where(GenericProfitabilityAssumptionConfiguration::FIELD_COMPANY_ID, $this->targetCompanyId)
            ->pluck(GenericProfitabilityAssumptionConfiguration::FIELD_INDUSTRY_SERVICE_ID)
            ->toArray();
        $configurations = DB::table(GenericProfitabilityAssumptionConfiguration::TABLE)
            ->where(GenericProfitabilityAssumptionConfiguration::FIELD_COMPANY_ID, $this->sourceCompanyId)
            ->whereNotIn(GenericProfitabilityAssumptionConfiguration::FIELD_INDUSTRY_SERVICE_ID, $existingServiceIds)
            ->select([GenericProfitabilityAssumptionConfiguration::FIELD_ID, GenericProfitabilityAssumptionConfiguration::FIELD_COMPANY_ID, GenericProfitabilityAssumptionConfiguration::FIELD_INDUSTRY_SERVICE_ID])
            ->get();

        $changeFunction = $this->getGenericChangeHandler();
        $undoFunction = $this->getGenericUndoHandler();
        $messageFunction = fn($v) => "Reassigning GenericProfitabilityConfig for service ID " . $v->{GenericProfitabilityAssumptionConfiguration::FIELD_INDUSTRY_SERVICE_ID} ?? "unknown id";

        $processor(GenericProfitabilityAssumptionConfiguration::TABLE, $configurations, $changeFunction, $undoFunction, $messageFunction);
    }

    /**
     * This will check that the CompanyUser is due to be merged, as well as matching company_id
     */
    protected function handleCompanyContract(Closure $processor): void
    {
        $proposedMergedUserIds = array_map(
            fn($v) => $v[CompanyUser::FIELD_ID],
            $this->getProposedChanges(CompanyUser::TABLE)
        );

        $contracts = DB::table(CompanyContract::TABLE)
            ->where(CompanyContract::FIELD_COMPANY_ID, $this->sourceCompanyId)
            ->whereIn(CompanyContract::FIELD_COMPANY_USER_ID, $proposedMergedUserIds)
            ->select([CompanyContract::FIELD_ID, CompanyContract::FIELD_COMPANY_ID, CompanyContract::FIELD_COMPANY_USER_ID])
            ->get();
        $changeFunction = $this->getGenericChangeHandler();
        $undoFunction = $this->getGenericUndoHandler();
        $messageFunction = fn($v) => "Reassigning CompanyContract for CompanyUser ID " . $v->{CompanyContract::FIELD_COMPANY_USER_ID} ?? "unknown id";

        $processor(CompanyContract::TABLE, $contracts, $changeFunction, $undoFunction, $messageFunction);
    }

    protected function handleProductCampaigns(Closure $processor): void
    {
        $productCampaigns = DB::table(ProductCampaign::TABLE)
            ->where(ProductCampaign::FIELD_COMPANY_ID, $this->sourceCompanyId)
            ->select([ProductCampaign::FIELD_ID, ProductCampaign::FIELD_COMPANY_ID, ProductCampaign::FIELD_NAME])
            ->get();

        $changeFunction = $this->getGenericChangeHandler();
        $undoFunction = $this->getGenericUndoHandler();
        $messageFunction = fn($v) => "Reassigning ProductCampaign " . $v->name ?? "unknown name";

        $processor(ProductCampaign::TABLE, $productCampaigns, $changeFunction, $undoFunction, $messageFunction);
    }

    protected function handleCompanyExpertReviews(Closure $processor): void
    {
        $expertReviews = DB::table(CompanyExpertReview::TABLE)
            ->where(CompanyExpertReview::FIELD_COMPANY_ID, $this->sourceCompanyId)
            ->select([CompanyExpertReview::FIELD_ID, CompanyExpertReview::FIELD_COMPANY_ID, CompanyExpertReview::FIELD_USER_ID])
            ->get();

        $changeFunction = $this->getGenericChangeHandler();
        $undoFunction = $this->getGenericUndoHandler();
        $messageFunction = fn($v) => "Reassigning CompanyExpertReview by user ID " . $v->{CompanyExpertReview::FIELD_USER_ID} ?? "unknown user";

        $processor(CompanyExpertReview::TABLE, $expertReviews, $changeFunction, $undoFunction, $messageFunction);
    }

    protected function handleCompanyReviews(Closure $processor): void
    {
        $reviews = DB::table(CompanyReview::TABLE)
            ->where(CompanyReview::FIELD_COMPANY_ID, $this->sourceCompanyId)
            ->select([CompanyReview::FIELD_ID, CompanyReview::FIELD_COMPANY_ID, CompanyReview::FIELD_EMAIL])
            ->get();

        $changeFunction = $this->getGenericChangeHandler();
        $undoFunction = $this->getGenericUndoHandler();
        $messageFunction = fn($v) => "Reassigning CompanyReview by email " . $v->email ?? "unknown@email";

        $processor(CompanyReview::TABLE, $reviews, $changeFunction, $undoFunction, $messageFunction);
    }

    protected function handleCompanyUsers(Closure $processor): void
    {
        $targetUsers = DB::table(CompanyUser::TABLE)
            ->where(CompanyUser::FIELD_COMPANY_ID, $this->targetCompanyId)
            ->get();
        $sourceUsers = DB::table(CompanyUser::TABLE)
            ->where(CompanyUser::FIELD_COMPANY_ID, $this->sourceCompanyId)
            ->get();
        $users = $sourceUsers->reduce(function($output, &$sourceUser) use ($targetUsers) {
            if ($targetUsers->contains(fn($targetUser) => $targetUser->{CompanyUser::FIELD_EMAIL} === $sourceUser->{CompanyUser::FIELD_EMAIL})) {
                $sourceUser->duplicate_email = true;
            }
            $output->push($sourceUser);
            return $output;
        }, collect());

        $changeFunction = function($v) {
            return property_exists($v, 'duplicate_email')
                ? null
                : [CompanyUser::FIELD_ID => $v->id, CompanyUser::FIELD_COMPANY_ID => $this->targetCompanyId];
        };

        $undoFunction = function($v) {
            return property_exists($v, 'duplicate_email')
                ? null
                : [CompanyUser::FIELD_ID => $v->id, CompanyUser::FIELD_COMPANY_ID => $this->sourceCompanyId];
        };

        $messageFunction = fn($v) => property_exists($v, 'duplicate_email')
            ? "Skipping CompanyUser due to duplicate email address: " . $v->first_name . " " . $v->last_name . ", " . $v->email
            : "Reassigning CompanyUser " . $v->first_name . " " . $v->last_name;

        $processor(CompanyUser::TABLE, $users, $changeFunction, $undoFunction, $messageFunction);
    }

    /**
     * Skip CompanyUsers in the source company with duplicate email addresses to avoid
     * breaking logins in the target Company.
     */
    protected function handleCompanyCampaigns(Closure $processor): void
    {
        $remapCampaigns = DB::table(CompanyCampaign::TABLE)
            ->where(CompanyCampaign::FIELD_COMPANY_ID, $this->sourceCompanyId)
            ->select([CompanyCampaign::FIELD_ID, CompanyCampaign::FIELD_COMPANY_ID, CompanyCampaign::FIELD_NAME])
            ->get();

        $changeFunction = $this->getGenericChangeHandler();
        $undoFunction = $this->getGenericUndoHandler();
        $messageFunction = fn($v) => "Reassigning CompanyCampaign " . $v->name ?? "Unnamed Campaign";

        $processor(CompanyCampaign::TABLE, $remapCampaigns, $changeFunction, $undoFunction, $messageFunction);
    }



    /**
     * Getter for up-to-date proposed changes from parent service
     * A handler using this must be called after the handler whose data it needs
     *
     * @param string $tableName
     * @return array
     */
    protected function getProposedChanges(string $tableName): array
    {
        $getter = $this->proposedChangesFunction;

        return $getter($tableName) ?? [];
    }
}
