<?php

namespace App\Console\Commands\CompanyMergeTool;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessQueuedHandlersForCompanyMergeJob  implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        protected int $companyMergeRecordId,
        protected bool $undoOperation = false,
    ) {}

    public function uniqueId(): string
    {
        return $this->companyMergeRecordId;
    }

    public function handle(): void
    {
        /** @var CompanyMergeService $mergeService */
        $mergeService = app(CompanyMergeService::class);

        if ($this->undoOperation)
            $mergeService->handleQueuedUndo($this->companyMergeRecordId);
        else
            $mergeService->handleQueuedChanges($this->companyMergeRecordId);
    }
}
