<?php

namespace App\Console\Commands\CompanyMergeTool;

use App\Enums\Company\CompanyAdminStatus;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Odin\Company;
use App\Models\CompanyMergeRecord;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Repositories\Odin\CompanyIndustryServiceRepository;
use App\Services\DatabaseHelperService;
use Closure;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class CompanyMergeService
{
    const string DATABASE_ADMIN2 = 'admin2';
    const string DATABASE_LEGACY = 'legacy';

    const string KEY_CHANGES     = 'changes';
    const string KEY_UNDO        = 'undo';
    const string KEY_MESSAGES    = 'messages';

    const string RESPONSE_STATUS   = 'status';

    protected CompanyMergeModelHandlers $modelHandlers;
    protected CompanyMergeLegacyModelHandlers $legacyModelHandlers;

    protected int $sourceCompanyId;
    protected int $targetCompanyId;
    protected int $sourceCompanyLegacyId;
    protected int $targetCompanyLegacyId;
    protected int $mergeRecordId;
    protected bool $dryRun;
    protected bool $fromConsole = false;

    protected array $proposedChanges = [];
    protected array $undoChanges = [];
    protected array $messages = [];

    protected array $proposedLegacyChanges = [];
    protected array $undoLegacyChanges = [];
    protected array $legacyMessages = [];

    // do not attempt DB updates on reserved control names
    protected array $ignoreColumns = [
        'detach'
    ];

    public function __construct() {}

    /**
     * @param int $sourceCompanyId
     * @param int $targetCompanyId
     * @param bool $dryRun
     * @param bool $fromConsole
     * @return array
     */
    public function handleMerge(int $sourceCompanyId, int $targetCompanyId, bool $dryRun, bool $fromConsole = false): array
    {
        $sourceCompany = Company::query()->findOrFail($sourceCompanyId);
        $targetCompany = Company::query()->findOrFail($targetCompanyId);
        $this->sourceCompanyId = $sourceCompanyId;
        $this->targetCompanyId = $targetCompanyId;
        $this->sourceCompanyLegacyId = $sourceCompany->legacy_id;
        $this->targetCompanyLegacyId = $targetCompany->legacy_id;
        $this->dryRun = $dryRun;
        $this->fromConsole = $fromConsole;

        // Add count reports for models processed by queues
        $this->addCountMessages();

        // Process Admin2 model handlers
        $this->modelHandlers = new CompanyMergeModelHandlers(
            $this->sourceCompanyId,
            $this->targetCompanyId,
            $this->getProposedAdminChanges(...),
        );
        $this->modelHandlers->processHandlers($this->proposeChanges(...));

        // Process Legacy model handlers
        $this->legacyModelHandlers = new CompanyMergeLegacyModelHandlers(
            $this->sourceCompanyLegacyId,
            $this->targetCompanyLegacyId,
            $this->proposedChanges,
        );

        $this->legacyModelHandlers->processHandlers($this->proposeLegacyChanges(...));

        if ($dryRun && $this->updateCompanies()) {
            return [
                self::RESPONSE_STATUS => true,
                self::KEY_CHANGES     => $this->reportProposedChanges(),
                self::KEY_UNDO        => $this->reportUndoChanges(),
                self::KEY_MESSAGES    => $this->reportMessages(),
            ];
        }
        else {
            if ($this->executeProposedChanges()) {
                return [
                    self::RESPONSE_STATUS => true,
                    self::KEY_MESSAGES    => $this->reportMessages(),
                ];
            }
            else {
                return [
                    self::RESPONSE_STATUS => false,
                    self::KEY_MESSAGES    => $this->reportMessages(),
                ];
            }
        }
    }

    /**
     * @param int $sourceCompanyId
     * @param int $targetCompanyId
     * @param bool $dryRun
     * @param bool $fromConsole
     * @return array
     */
    public function handleUndo(int $sourceCompanyId, int $targetCompanyId, bool $dryRun, bool $fromConsole = false): array
    {
        $this->sourceCompanyId = $sourceCompanyId;
        $this->targetCompanyId = $targetCompanyId;
        $this->dryRun = $dryRun;
        $this->fromConsole = $fromConsole;

        /** @var CompanyMergeRecord $mergeRecord */
        $mergeRecord = CompanyMergeRecord::query()
            ->where(CompanyMergeRecord::FIELD_SOURCE_COMPANY_ID, $sourceCompanyId)
            ->where(CompanyMergeRecord::FIELD_TARGET_COMPANY_ID, $targetCompanyId)
            ->latest()
            ->first();

        if (!$mergeRecord) {
            $this->logMessage("\nNo undo record exists for this Company combination.\n");

            return [
                self::RESPONSE_STATUS => false,
            ];
        }

        $this->mergeRecordId = $mergeRecord->id;

        $undoPayload = $mergeRecord->undo_payload;
        $this->undoChanges = $undoPayload[self::DATABASE_ADMIN2];
        $this->undoLegacyChanges = $undoPayload[self::DATABASE_LEGACY];

        if ($dryRun) {
            return [
                self::RESPONSE_STATUS => true,
                self::KEY_UNDO        => $this->reportUndoChanges()
            ];
        }
        else {
            return [
                self::RESPONSE_STATUS => $this->executeUndo(),
                self::KEY_UNDO        => $this->reportUndoChanges(),
            ];
        }
    }

    /**
     * @param int $mergeRecordId
     * @return void
     */
    public function handleQueuedChanges(int $mergeRecordId): void
    {
        /** @var CompanyMergeRecord $mergeRecord */
        $mergeRecord = CompanyMergeRecord::query()
            ->findOrFail($mergeRecordId);

        $queuedHandlers = new CompanyMergeQueuedHandlers($mergeRecord);
        if ($queuedHandlers->customProcessing())
            logger()->info("Queued company merge job for $mergeRecord->source_company_id => $mergeRecord->target_company_id completed successfully.");
    }

    public function handleQueuedUndo(int $mergeRecordId): void
    {
        /** @var CompanyMergeRecord $mergeRecord */
        $mergeRecord = CompanyMergeRecord::query()
            ->findOrFail($mergeRecordId);

        $queuedHandlers = new CompanyMergeQueuedHandlers($mergeRecord);
        if ($queuedHandlers->customUndo())
            logger()->info("Queued company merge UNDO job for $mergeRecord->source_company_id => $mergeRecord->target_company_id completed successfully.");
    }

    /**
     * @return bool
     */
    protected function executeProposedChanges(): bool
    {
        DB::beginTransaction();

        $mainUpdateSuccess = $this->runMainDatabaseUpdates(
            $this->proposedChanges,
            $this->proposedLegacyChanges,
        );

        if ($mainUpdateSuccess) {
            try {
                $updateCompanies = $this->updateCompanies() && $this->updateLegacyCompanies();
                $mergeRecord = $this->commitUndoChanges();

                if (!$updateCompanies || !$mergeRecord)
                    throw new Exception("Error updating company models");

                DB::commit();
                ProcessQueuedHandlersForCompanyMergeJob::dispatch($mergeRecord->id);

                return true;
            }
            catch(Exception $e) {
                $this->logMessage($e);
            }
        }

        DB::rollBack();

        return false;
    }

    /**
     * @return bool
     */
    protected function executeUndo(): bool
    {
        DB::beginTransaction();

        $mainUpdateSuccess = $this->runMainDatabaseUpdates(
            $this->undoChanges,
            $this->undoLegacyChanges,
        );

        if ($mainUpdateSuccess) {
            try {
                $updateCompanies = $this->restoreCompanies()
                    && $this->restoreLegacyCompanies()
                    && $this->deleteMergeRecordSync();
                if (!$updateCompanies)
                    throw new Exception("Error restoring company models");

                ProcessQueuedHandlersForCompanyMergeJob::dispatch($this->mergeRecordId, true);
                DB::commit();

                return true;
            }
            catch(Exception $e) {
                $this->logMessage($e);
            }
        }

        DB::rollBack();

        return false;
    }

    /**
     * @param array $admin2Data
     * @param array $legacyData
     * @return bool
     */
    protected function runMainDatabaseUpdates(array $admin2Data, array $legacyData): bool
    {
        $jsonUpdateRegex = "/^\\$/";
        try {
            foreach ($admin2Data as $admin2Table => $updateData) {
                $columns = array_keys($updateData[0] ?? []);
                $columns = array_filter($columns, fn(string $col) => !in_array($col, $this->ignoreColumns));
                if (!$columns) continue;

                $jsonColumns = array_filter($columns, fn(string $column) => preg_match($jsonUpdateRegex, $column));
                if ($jsonColumns) {
                    $primaryKey = $columns[0] === 'id'
                        ? $columns[0]
                        : (array_filter($columns, fn(string $column) => !preg_match($jsonUpdateRegex, $column)) ?? [])[0] ?? null;
                    if (!$primaryKey)
                        throw new Exception("Bad JSON update configuration in $admin2Table array");

                    foreach ($updateData as $update) {
                        $targetId = $update[$primaryKey];
                        foreach ($jsonColumns as $jsonColumn) {
                            [$jsonTarget, $column] = explode("|", $jsonColumn);
                            $value = $update[$jsonColumn];
                            DB::statement("update $admin2Table set $column = JSON_SET($column, '$jsonTarget', $value) where $primaryKey = $targetId");
                        }
                    }
                }
                else {
                    DB::table($admin2Table)
                        ->upsert($updateData, [], $columns);
                }
            }
            foreach ($legacyData as $legacyTable => $updateData) {
                $columns = array_keys($updateData[0] ?? []);
                $columns = array_filter($columns, fn(string $col) => !in_array($col, $this->ignoreColumns));
                if (!$columns) continue;

                DB::table(DatabaseHelperService::readOnlyDatabase() .'.'. $legacyTable)
                    ->upsert($updateData, [], $columns);
            }
        }
        catch(Exception $e) {
            $this->logMessage($e);

            return false;
        }

        return true;
    }

    /**
     * TODO: also softDelete to remove from general searches/queries, create explicit methods to find merged records?
     * @return bool
     */
    protected function updateCompanies(): bool
    {
        /** @var Company $sourceCompany */
        $sourceCompany = Company::query()
            ->find($this->sourceCompanyId);
        /** @var Company $targetCompany */
        $targetCompany = Company::query()
            ->find($this->targetCompanyId);

        $targetServices = $targetCompany->services()
            ->pluck(IndustryService::TABLE .'.'. IndustryService::FIELD_ID)
            ->toArray();
        $addServices = $sourceCompany->services()
            ->whereNotIn(IndustryService::TABLE .'.'. IndustryService::FIELD_ID, $targetServices)
            ->with([IndustryService::RELATION_INDUSTRY])
            ->get();

        $this->messages[IndustryService::TABLE] = $addServices->map(fn($service) =>
            "Adding IndustryService " . $service->industry->name . " -> $service->name"
        )->toArray();

        $this->undoChanges[IndustryService::TABLE] = $addServices->pluck(IndustryService::FIELD_ID)
            ->map(fn($v) => ['detach' => $v])
            ->toArray();

        $this->undoChanges[Company::TABLE] = [
            [
                Company::FIELD_ID           => $this->sourceCompanyId,
                Company::FIELD_ADMIN_STATUS => $sourceCompany->admin_status,
                Company::FIELD_NAME         => $sourceCompany->name,
            ],
        ];

        if ($this->dryRun)
            return true;

        /** @var CompanyIndustryServiceRepository $repository */
        $repository = app(CompanyIndustryServiceRepository::class);
        $addServices->each(fn(IndustryService $service) =>
            $repository->addCompanyServiceAndRequiredIndustry($targetCompany, $service)
        );

        $newName = "$sourceCompany->name (merged to $targetCompany->id)";

        return Company::query()
            ->where(Company::FIELD_ID, $this->sourceCompanyId)
            ->update([
                Company::FIELD_ADMIN_STATUS => CompanyAdminStatus::ARCHIVED,
                Company::FIELD_NAME => $newName
            ]);
    }

    /**
     * TODO: will need to update this to restore the model if the above softDelete() option is used
     * @return bool
     */
    protected function restoreCompanies(): bool
    {
        /** @var Company $targetCompany */
        $targetCompany = Company::query()
            ->find($this->targetCompanyId);

        $removeIndustryServices = $this->undoChanges[IndustryService::TABLE] ?? [];
        $targetCompany->services()
            ->detach($removeIndustryServices);

        /** @var CompanyIndustryServiceRepository $repository */
        $repository = app(CompanyIndustryServiceRepository::class);
        $repository->cleanUpCompanyIndustries($targetCompany);

        return true;
    }

    protected function updateLegacyCompanies(): bool
    {
        if ($this->dryRun)
            return true;

        /** @var EloquentCompany $sourceCompany */
        $sourceCompany = EloquentCompany::query()
            ->find($this->sourceCompanyLegacyId);
        /** @var EloquentCompany $targetCompany */
        $targetCompany = EloquentCompany::query()
            ->find($this->targetCompanyLegacyId);

        $this->undoLegacyChanges[EloquentCompany::TABLE] = [
            [
                EloquentCompany::ID     => $this->sourceCompanyLegacyId,
                EloquentCompany::STATUS => $sourceCompany->status,
            ],
            [
                EloquentCompany::ID     => $this->targetCompanyLegacyId,
                EloquentCompany::TYPE   => $targetCompany->type,
            ]
        ];

        DB::table(DatabaseHelperService::readOnlyDatabase() .'.'. EloquentCompany::TABLE)
            ->where(EloquentCompany::COMPANY_ID, $targetCompany->companyid)
            ->update([
                EloquentCompany::TYPE   => EloquentCompany::TYPE_MULTI,
            ]);

        DB::table(DatabaseHelperService::readOnlyDatabase() .'.'. EloquentCompany::TABLE)
            ->where(EloquentCompany::COMPANY_ID, $sourceCompany->companyid)
            ->update([
                EloquentCompany::STATUS => EloquentCompany::STATUS_HIDDEN,
            ]);

        return true;
    }

    /**
     * The undo array should handle everything needed so far
     */
    protected function restoreLegacyCompanies(): bool
    {
        return true;
    }

    /**
     * @param string $table
     * @param Collection $data
     * @param Closure $changeFunction
     * @param Closure $undoFunction
     * @param Closure|null $messageFunction
     * @return void
     */
    protected function proposeChanges(string $table, Collection $data, Closure $changeFunction, Closure $undoFunction, ?Closure $messageFunction = null): void
    {
        [self::KEY_CHANGES => $changes, self::KEY_UNDO => $undo, self::KEY_MESSAGES => $messages] = $this->processProposedChanges($data, $changeFunction, $undoFunction, $messageFunction);
        $this->recordProposedChanges(self::DATABASE_ADMIN2, $table, $changes, $undo, $messages);
    }

    /**
     * @param string $table
     * @param Collection $data
     * @param Closure $changeFunction
     * @param Closure $undoFunction
     * @param Closure|null $messageFunction
     * @return void
     */
    protected function proposeLegacyChanges(string $table, Collection $data, Closure $changeFunction, Closure $undoFunction, ?Closure $messageFunction = null): void
    {
        [self::KEY_CHANGES => $changes, self::KEY_UNDO => $undo, self::KEY_MESSAGES => $messages] = $this->processProposedChanges($data, $changeFunction, $undoFunction, $messageFunction);
        $this->recordProposedChanges(self::DATABASE_LEGACY, $table, $changes, $undo, $messages);
    }

    /**
     * @param Collection $data
     * @param Closure $changeFunction
     * @param Closure $undoFunction
     * @param Closure|null $messageFunction
     * @return array
     */
    protected function processProposedChanges(Collection $data, Closure $changeFunction, Closure $undoFunction, ?Closure $messageFunction): array
    {
        return $data->reduce(function($output, $v) use ($changeFunction, $undoFunction, $messageFunction) {
            $changes = $changeFunction($v);
            $undo = $undoFunction($v);
            if ($changes)
                $output[self::KEY_CHANGES][] = $changes;
            if ($undo)
                $output[self::KEY_UNDO][] = $undo;
            if ($messageFunction) {
                $message = $messageFunction($v);
                if ($message)
                    $output[self::KEY_MESSAGES][] = $message;
            }

            return $output;
        }, [
            self::KEY_CHANGES  => [],
            self::KEY_UNDO     => [],
            self::KEY_MESSAGES => [],
        ]);
    }

    /**
     * @param string $database
     * @param string $table
     * @param array $changes
     * @param array $undoChange
     * @param array $messages
     * @return void
     */
    protected function recordProposedChanges(string $database, string $table, array $changes, array $undoChange, array $messages): void
    {
        if ($database === self::DATABASE_LEGACY) {
            $this->proposedLegacyChanges[$table] = $changes;
            $this->undoLegacyChanges[$table]     = $undoChange;
            $this->legacyMessages[$table]        = $messages;
        }
        else {
            $this->proposedChanges[$table] = $changes;
            $this->undoChanges[$table]     = $undoChange;
            $this->messages[$table]        = $messages;
        }
    }

    /**
     * @param string $tableName
     * @return array
     */
    protected function getProposedAdminChanges(string $tableName): array
    {
        return $this->proposedChanges[$tableName] ?? [];
    }

    /**
     * @return void
     */
    protected function addCountMessages(): void
    {
        $productAssignmentTitle = ProductAssignment::TABLE . " (processed by queue)";
        $productAssignmentCount = ProductAssignment::query()
            ->where(ProductAssignment::FIELD_COMPANY_ID, $this->sourceCompanyId)
            ->count();
        $quoteCompanyTitle = EloquentQuoteCompany::TABLE . " (processed by queue)";
        $quoteCompanyCount = EloquentQuoteCompany::query()
            ->where(EloquentQuoteCompany::COMPANY_ID, $this->sourceCompanyLegacyId)
            ->count();

        $this->messages[$productAssignmentTitle] = [
            "$productAssignmentCount records to be processed by queue"
        ];
        $this->legacyMessages[$quoteCompanyTitle] = [
            "$quoteCompanyCount records to be processed by queue"
        ];
    }

    /**
     * @return CompanyMergeRecord|null
     */
    protected function commitUndoChanges(): ?CompanyMergeRecord
    {
        $payload = [
            self::DATABASE_ADMIN2 => $this->undoChanges,
            self::DATABASE_LEGACY => $this->undoLegacyChanges,
        ];

        /** @var CompanyMergeRecord */
        return CompanyMergeRecord::query()
            ->create([
                CompanyMergeRecord::FIELD_SOURCE_COMPANY_ID => $this->sourceCompanyId,
                CompanyMergeRecord::FIELD_TARGET_COMPANY_ID => $this->targetCompanyId,
                CompanyMergeRecord::FIELD_PAYLOAD           => $payload,
                CompanyMergeRecord::CREATED_AT              => now(),
            ]);
    }

    /**
     * This only removes the main payload but leaves the record, as the queued handlers still need to run
     * @return bool
     */
    protected function deleteMergeRecordSync(): bool
    {
        return CompanyMergeRecord::query()
            ->find($this->mergeRecordId)
            ->update([
                CompanyMergeRecord::FIELD_PAYLOAD => null,
            ]);
    }

    /**
     * @return array
     */
    protected function reportMessages(): array
    {
        return [
            self::DATABASE_ADMIN2 => $this->messages,
            self::DATABASE_LEGACY => $this->legacyMessages,
        ];
    }

    /**
     * @return array
     */
    protected function reportProposedChanges(): array
    {
        return [
            self::DATABASE_ADMIN2 => $this->proposedChanges,
            self::DATABASE_LEGACY => $this->proposedLegacyChanges,
        ];
    }

    /**
     * @return array
     */
    protected function reportUndoChanges(): array
    {
        return [
            self::DATABASE_LEGACY => $this->undoLegacyChanges,
            self::DATABASE_ADMIN2 => $this->undoChanges,
        ];
    }

    /**
     * @param string $message
     * @return void
     */
    protected function logMessage(string $message): void
    {
        if ($this->fromConsole)
            echo "$message\n";
        else
            logger()->info($message);
    }
}
