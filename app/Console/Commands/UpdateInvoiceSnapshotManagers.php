<?php

namespace App\Console\Commands;

use App\Jobs\Billing\CalculateUpdateInvoiceSnapshotManager;
use App\Jobs\Billing\UpdateInvoiceSnapshotManager;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class UpdateInvoiceSnapshotManagers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'billing:update-invoice-snapshot-managers {--company_id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $companyId = Str::of($this->option('company_id'))
            ->trim()
            ->toInteger();

        if ($companyId > 0) {
            UpdateInvoiceSnapshotManager::dispatchSync($companyId);
            $this->info("Invoice snapshots of company id $companyId got updated");
            return;
        }

        $choice = $this->choice(
            "Do you want to update all snapshots in the database?",
            ['yes', 'no'],
            'yes'
        );

        if ($choice === 'yes') {
            CalculateUpdateInvoiceSnapshotManager::dispatch();
            $this->info("Jobs dispatched");
            return;
        }
    }
}
