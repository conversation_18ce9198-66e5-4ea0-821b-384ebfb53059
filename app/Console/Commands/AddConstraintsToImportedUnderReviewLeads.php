<?php

namespace App\Console\Commands;

use App\Jobs\AddConstraintsToLeadJob;
use App\Models\LeadProcessingUnderReview;
use Illuminate\Console\Command;

class AddConstraintsToImportedUnderReviewLeads extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:imported-constraints';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Updates all UR leads and sets their constraints.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        LeadProcessingUnderReview::query()->chunk(500, function($leads) {
            /** @var LeadProcessingUnderReview $lead */
            foreach($leads as $lead)
               try {AddConstraintsToLeadJob::dispatch($lead->lead_id);} catch(\Exception $e) {}
        });
        return 0;
    }
}
