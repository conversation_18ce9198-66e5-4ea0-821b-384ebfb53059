<?php

namespace App\Console\Commands;

use App\Jobs\Reports\SendMonthlyCostSummaryEmailJob;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\ProductAssignment;
use Illuminate\Console\Command;

class SendMonthlyCostSummaryCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reports:send-monthly-cost-summary
                            {--company-id= : Override the company ID (optional)}
                            {--company-user-id= : Override the company user ID (optional)}
                            {--dry-run : Show what would be sent without actually sending}';


    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manually execute the monthly cost summary email job';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== Monthly Cost Summary Email Job ===');

        // Get company ID
        $companyId = $this->option('company-id') ?? 15158;
        $companyUserId = $this->option('company-user-id') ?? null;
        $this->info("Company ID: {$companyId}");

        // Validate company
        $company = Company::find($companyId);
        if (!$company) {
            $this->error("Company with ID {$companyId} not found!");
            return 1;
        }

        $this->info("Company: {$company->name}");

        // Check contacts
        $contacts = CompanyUser::query()
            ->where(CompanyUser::FIELD_COMPANY_ID, $company->id)
            ->where(CompanyUser::FIELD_STATUS, CompanyUser::STATUS_ACTIVE)
            ->where(CompanyUser::FIELD_IS_CONTACT, CompanyUser::USER_IS_CONTACT)
            ->whereNotNull(CompanyUser::FIELD_EMAIL)
            ->where(CompanyUser::FIELD_EMAIL, '!=', '')
            ->get();

        $this->info("Active contacts: " . $contacts->count());

        if ($contacts->count() === 0) {
            $this->warn("No active contacts found for this company!");
            return 1;
        }

        // Show contacts
        $this->table(
            ['Name', 'Email'],
            $contacts->map(fn($c) => [$c->first_name . ' ' . $c->last_name, $c->email])
        );

        // Check assignments
        $assignmentCount = ProductAssignment::query()
            ->where(ProductAssignment::FIELD_COMPANY_ID, $companyId)
            ->where(ProductAssignment::FIELD_DELIVERED, true)
            ->where(ProductAssignment::FIELD_CHARGEABLE, true)
            ->whereBetween(ProductAssignment::FIELD_DELIVERED_AT, [now()->startOfMonth(), now()])
            ->count();

        $this->info("Product assignments this month to date: {$assignmentCount}");
        $this->info("Date range: " . now()->startOfMonth()->format('Y-m-d') . " to " . now()->format('Y-m-d'));

        if ($assignmentCount === 0) {
            $this->warn("No product assignments found for this month to date. Email will be sent but will be empty.");
        }

        // Dry run check
        if ($this->option('dry-run')) {
            $this->info("DRY RUN: Would send emails to {$contacts->count()} contacts with {$assignmentCount} assignments.");
            return 0;
        }

        // Confirm execution
        if (!$this->confirm('Do you want to execute the job and send emails?')) {
            $this->info('Operation cancelled.');
            return 0;
        }

        // Execute the job handle method directly
        $this->info('Executing job handle method...');

        try {
            $job = new SendMonthlyCostSummaryEmailJob(
                companyId: $companyId,
                companyUserId: $companyUserId);
            $job->handle();

            $this->info('✅ Job executed successfully!');
            $this->info("📧 Emails sent to {$contacts->count()} contacts.");

        } catch (\Exception $e) {
            $this->error('❌ Job execution failed: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
            return 1;
        }

        return 0;
    }
}
