<?php

namespace App\Console\Commands;

use App\Jobs\LegacyMigrations\MigrateLegacyQuoteCompaniesJob;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Odin\ProductAssignment;
use App\Models\SaleType;
use App\Services\DatabaseHelperService;
use App\Services\QueueHelperService;
use Illuminate\Bus\Batch;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Illuminate\Console\Command;
use Throwable;

class MigrateLegacyQuoteCompanies extends Command
{
    const OPT_BATCH_JOB_SIZE = 'batch-job-size';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'legacy-migrate:quote-companies {--batch-job-size=1000: The number of quote companies each batch job should handle}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handles the migration of legacy quote companies to their new schema.';

    /**
     * Execute the console command.
     *
     * @return int
     * @throws Throwable
     */
    public function handle()
    {
        $this->info("Migrating quote companies");

        $quoteCompanyIdCol = EloquentQuoteCompany::ID;
        $countCol = 'count';

        $totalQuoteCompanies = EloquentQuoteCompany::query()->count();
        $totalMigrated = EloquentQuoteCompany::query()
            ->join(DatabaseHelperService::database() . '.' . ProductAssignment::TABLE, DatabaseHelperService::database() . '.' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_LEGACY_ID, '=', EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::ID)
            ->count();

        $total = $totalQuoteCompanies - $totalMigrated;

        // Performance
        DB::disableQueryLog();

        if($total > 0) {
            $this->info("$total quote companies total");

            $saleTypes = SaleType::all()->pluck(SaleType::FIELD_ID, SaleType::FIELD_NAME)->toArray();

            $batchJobSize = (int) $this->option(self::OPT_BATCH_JOB_SIZE);

            $chunk = ceil($totalQuoteCompanies/$batchJobSize);
            $totalJobs = ceil($totalQuoteCompanies/$chunk);
            $jobDispatched = 0;

            $this->info("total {$totalJobs} jobs to be dispatched ....");

            EloquentQuoteCompany::query()
                ->select(EloquentQuoteCompany::ID)
                ->chunk(ceil($totalQuoteCompanies/$batchJobSize), function($quoteCompanyIds) use ($saleTypes, $totalJobs, &$jobDispatched) {
                    MigrateLegacyQuoteCompaniesJob::dispatch($quoteCompanyIds->pluck(EloquentQuoteCompany::ID), $saleTypes)
                        ->onConnection(QueueHelperService::QUEUE_CONNECTION)
                        ->onQueue(QueueHelperService::QUEUE_NAME_LEGACY_MIGRATION);

                    $jobDispatched++;

                    $this->info("Dispatched {$jobDispatched} of {$totalJobs} jobs");
                });

            $this->info('Finished.');
        }
        else {
            $this->line("No legacy quote companies to migrate");
        }

        return 0;
    }
}
