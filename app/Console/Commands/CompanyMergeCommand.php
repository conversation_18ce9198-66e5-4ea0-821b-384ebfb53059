<?php

namespace App\Console\Commands;

use App\Console\Commands\CompanyMergeTool\CompanyMergeService;
use App\Models\Odin\Company;
use Illuminate\Console\Command;

/**
 * This is now available in the front end in company->configurations
 */
class CompanyMergeCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'merge:companies {--source=} {--target=} {--dry-run}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Merge two Companies into one - the {target} id will become the only active Company.
                               The source Company will be viewable only as a record.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $sourceCompanyId = $this->option('source');
        $targetCompanyId = $this->option('target');
        $dryRun = $this->option('dry-run');

        $sourceCompany = Company::query()->find($sourceCompanyId);
        $targetCompany = Company::query()->find($targetCompanyId);

        if (!$sourceCompany || !$targetCompany) {
            $this->error("A valid source Company and target Company must be supplied.");

            return Command::FAILURE;
        }
        else if ($sourceCompanyId === $targetCompany) {
            $this->error("Merging a Company into itself would break the wibbliness factor of the space-time continuum. You monster.");

            return Command::FAILURE;
        }

        $this->info("Starting merge or $sourceCompany->name ($sourceCompanyId) to $targetCompany->name ($targetCompanyId).");

        /** @var CompanyMergeService $service */
        $service = app(CompanyMergeService::class);
        $result = $service->handleMerge($sourceCompany->id, $targetCompany->id, $dryRun, true);

        if ($dryRun) {
            if (!$result[CompanyMergeService::RESPONSE_STATUS] ?? false) {
                $this->error("Merge command failed.");
            }
            else {
                $this->printMessages($result[CompanyMergeService::KEY_CHANGES] ?? []);
                $this->printMessages($result[CompanyMergeService::KEY_UNDO] ?? []);
                $this->printMessages($result[CompanyMergeService::KEY_MESSAGES] ?? []);

                return Command::SUCCESS;
            }
        }
        else {
            if (!$result[CompanyMergeService::RESPONSE_STATUS] ?? false) {
                $this->warn("Merge operation did not complete.");
            }
            else {
                $this->printMessages($result[CompanyMergeService::KEY_MESSAGES] ?? []);
                $this->info("\nMerge completed successfully.");

                return Command::SUCCESS;
            }
        }

        $this->info("Finished merging.");

        return Command::SUCCESS;
    }

    private function printMessages(array $messageArray): void
    {
        if (!$messageArray) {
            print("No messages found.");
            return;
        }

        print("\n\n\nUndo:\n");
        print("ADMIN2 =>\n");
        print_r($messageArray[CompanyMergeService::DATABASE_ADMIN2]);
        print("LEGACY =>\n");
        print_r($messageArray[CompanyMergeService::DATABASE_LEGACY]);
    }
}
