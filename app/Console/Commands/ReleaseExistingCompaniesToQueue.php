<?php

namespace App\Console\Commands;

use App\Enums\CompanySalesStatus;
use App\Enums\RoleType;
use App\Models\CompanyUserRelationship;
use App\Models\Odin\Company;
use App\Models\Role;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class ReleaseExistingCompaniesToQueue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'companies:release-to-queue
                            {company_ids : Comma-separated list of company IDs}
                            {--roles= : Optional comma-separated list of role names to unassign}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Unassign all company relationships from a comma separated list of company_ids';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $companyIds = $this->parseCompanyIds();
        $roles = $this->parseRoles();

        if ($companyIds->isEmpty()) {
            $this->error('No valid company IDs provided.');
            return 1;
        }

        $this->info('Starting company relationship release process...');
        $this->info('Company IDs: ' . $companyIds->implode(', '));

        if ($roles->isNotEmpty()) {
            $this->info('Roles to unassign: ' . $roles->implode(', '));
        } else {
            $this->info('Will unassign ALL roles for each company');
        }

        $companies = Company::query()
            ->whereIn(Company::FIELD_ID, $companyIds->toArray())
            ->get();

        if ($companies->isEmpty()) {
            $this->error('No companies found with the provided IDs.');
            return 1;
        }

        $this->info('Found ' . $companies->count() . ' companies to process.');

        if (!$this->confirm('Do you want to continue with the unassignment process?')) {
            $this->info('Operation cancelled.');
            return 0;
        }

        $this->processCompanies($companies, $roles);

        $this->info('Company relationship release process completed successfully.');
        return 0;
    }

    /**
     * Parse and validate company IDs from the input
     */
    private function parseCompanyIds(): Collection
    {
        $input = $this->argument('company_ids');

        if (empty($input)) {
            return collect();
        }

        return collect(explode(',', $input))
            ->map(fn($id) => trim($id))
            ->filter(fn($id) => !empty($id) && is_numeric($id))
            ->map(fn($id) => (int) $id)
            ->unique();
    }

    /**
     * Parse and validate role names from the option
     */
    private function parseRoles(): Collection
    {
        $input = $this->option('roles');

        if (empty($input)) {
            return collect();
        }

        $validRoles = collect(RoleType::cases())->map(fn($role) => $role->value);

        return collect(explode(',', $input))
            ->map(fn($role) => trim($role))
            ->filter(fn($role) => !empty($role))
            ->filter(function ($role) use ($validRoles) {
                if (!$validRoles->contains($role)) {
                    $this->warn("Invalid role name: {$role}. Skipping.");
                    return false;
                }
                return true;
            })
            ->unique();
    }

    /**
     * Process companies and unassign relationships
     */
    private function processCompanies(Collection $companies, Collection $roles): void
    {
        $progressBar = $this->output->createProgressBar($companies->count());
        $progressBar->start();

        $totalUnassignments = 0;
        $reason = 'Company manually released back to queue';

        foreach ($companies as $company) {
            $this->newLine();
            $this->info("Processing Company ID: {$company->id}");

            $rolesToUnassign = $this->getRolesToUnassign($company, $roles);

            if ($rolesToUnassign->isEmpty()) {
                $this->info("  No roles to unassign for Company ID: {$company->id}");
            } else {
                foreach ($rolesToUnassign as $role) {
                    try {
                        // if $role is sales-development-manager and CompanySalesStatus is either demo scheduled or demo completed or closed, skip
                        if ($role === RoleType::SALES_DEVELOPMENT_REPRESENTATIVE->value &&
                            $company->sales_status === CompanySalesStatus::DEMO_SCHEDULED ||
                            $company->sales_status === CompanySalesStatus::DEMO_COMPLETED ||
                            $company->sales_status === CompanySalesStatus::CLOSED) {
                            $this->info("  - Skipping unassignment of {$role} for Company ID: {$company->id} as company is in demo scheduled, demo completed or closed status");
                            continue;
                        }

                        $unassignedUser = $company->unassign(
                            role: $role,
                            notify: false,
                            reason: $reason);

                        if ($unassignedUser) {
                            $this->info("  ✓ Unassigned {$role}: {$unassignedUser->name} ({$unassignedUser->email})");
                            $totalUnassignments++;
                        } else {
                            $this->info("  - No assignment found for role: {$role}");
                        }
                    } catch (\Exception $e) {
                        $this->error("  ✗ Failed to unassign {$role}: " . $e->getMessage());
                    }
                }
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);
        $this->info("Summary:");
        $this->info("- Companies processed: {$companies->count()}");
        $this->info("- Total unassignments: {$totalUnassignments}");
    }

    /**
     * Get roles to unassign for a company
     */
    private function getRolesToUnassign(Company $company, Collection $requestedRoles): Collection
    {
        $currentAssignments = $company->userRelationships()
            ->with(CompanyUserRelationship::RELATION_ROLE)
            ->get()
            ->pluck('role.name')
            ->unique();

        if ($requestedRoles->isNotEmpty()) {
            return $requestedRoles->intersect($currentAssignments);
        }

        return $currentAssignments;
    }
}
