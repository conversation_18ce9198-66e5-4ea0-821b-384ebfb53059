<?php

namespace App\Console\Commands;

use App\Models\Odin\Company;
use App\Services\CompanySlugService;
use Illuminate\Console\Command;
use Random\RandomException;
use Symfony\Component\Console\Command\Command as CommandAlias;

class FormatCompanySlugs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'company:format:slugs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Formats the existing company slugs to be URL friendly and adds slugs for any company that\'s missing one.';

    /**
     * Execute the console command.
     *
     * @param CompanySlugService $slugService
     * @return int
     * @throws RandomException
     */
    public function handle(CompanySlugService $slugService): int
    {
        $companiesQuery = Company::query()->with(Company::RELATION_ACTIVE_COMPANY_SLUG);

        $bar = $this->output->createProgressBar($companiesQuery->count());
        $bar->start();

        $companiesQuery->chunk(200, function($companies) use ($bar, $slugService) {
            foreach($companies as $company) {
                $slugService->formatSlugForCompany($company);
                $bar->advance();
            }
        });

        $bar->finish();

        $this->newLine();

        $this->info('Company slugs have been formatted.');

        return CommandAlias::SUCCESS;
    }
}
