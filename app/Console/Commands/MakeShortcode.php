<?php

namespace App\Console\Commands;

use Illuminate\Console\GeneratorCommand;
use Symfony\Component\Console\Input\InputArgument;

class MakeShortcode extends GeneratorCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'make:shortcode {name}';

    /**
     * @var string
     */
    protected $type = 'class';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a new workflow shortcode';

    /**
     * @param $stub
     * @param $name
     * @return array|string
     */
    protected function replaceClass($stub, $name): array|string
    {
        $stub = parent::replaceClass($stub, $name);

        return str_replace('DummyShortcode', $this->argument('name'), $stub);
    }

    /**
     * @param $rootNamespace
     * @return string
     */
    protected function getDefaultNamespace($rootNamespace): string
    {
        return $rootNamespace . '\Workflows\Shortcodes';
    }

    /**
     * @return array[]
     */
    protected function getArguments(): array
    {
        return [
            ['name', InputArgument::REQUIRED, 'The name of the shortcode.'],
        ];
    }

    /**
     * @return string
     */
    protected function getStub(): string
    {
        return  app_path() . '/Console/Commands/Stubs/make-shortcode.stub';
    }
}
