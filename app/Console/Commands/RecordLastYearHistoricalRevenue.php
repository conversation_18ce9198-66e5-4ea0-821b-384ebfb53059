<?php

namespace App\Console\Commands;

use App\Repositories\HistoricalCompanyRevenueRepository;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Carbon\CarbonPeriod;
use Carbon\CarbonPeriodImmutable;
use Illuminate\Console\Command;

class RecordLastYearHistoricalRevenue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:record-last-year-historical-revenue';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Record historical company revenue for last year';

    public function __construct(private readonly HistoricalCompanyRevenueRepository $historicalCompanyRevenueRepository)
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $daysPeriod = CarbonPeriodImmutable::since(CarbonImmutable::today('UTC')->subYear()->startOfYear())
            ->until(CarbonImmutable::today('UTC')->subYear()->endOfYear())
            ->days(1);

        foreach($daysPeriod as $day) {
            echo "Recording ".$day->format('Y-m-d')."\r";

            $this->historicalCompanyRevenueRepository->recordDayRevenue($day);
        }

        $monthsPeriod = CarbonPeriodImmutable::since(CarbonImmutable::today('UTC')->subYear()->startOfYear())
            ->until(CarbonImmutable::today('UTC')->subYear()->endOfYear())
            ->month(1);

        foreach($monthsPeriod as $month) {
            echo "Recording ".$month->format('Y-m');

            $this->historicalCompanyRevenueRepository->recordMonthRevenue($month);
        }

        $year = CarbonImmutable::today('UTC')->subYear()->startOfYear();

        echo "Recording ".$year->format('Y');

        $this->historicalCompanyRevenueRepository->recordYearRevenue($year);
    }
}
