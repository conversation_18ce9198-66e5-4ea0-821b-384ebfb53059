<?php

namespace App\Console\Commands;

use App\Enums\Advertising\AdvertisingPlatform;
use App\Jobs\Advertising\UpdateTieredAdvertisingCampaignLocationsJob;
use Illuminate\Console\Command;

class UpdateTieredAdvertisingCampaignLocations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:tiered-advertising-campaign-locations';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Updates tiered advertising campaign locations.';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        foreach (AdvertisingPlatform::cases() as $adsPlatform) {
            $this->info("Updating ".$adsPlatform->value);
            UpdateTieredAdvertisingCampaignLocationsJob::dispatchSync($adsPlatform->value);
        }
    }
}
