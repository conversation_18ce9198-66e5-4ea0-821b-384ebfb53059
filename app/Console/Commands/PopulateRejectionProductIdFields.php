<?php

namespace App\Console\Commands;

use App\Enums\Odin\ProductAssignmentAffectRejectionPercentage;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAppointment;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use App\Repositories\Odin\ProductRepository;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;

class PopulateRejectionProductIdFields extends Command
{
    const LEAD_PRODUCT_ASSIGNMENT_ID        = 'lead_product_assignment_id';
    const APPOINTMENT_PRODUCT_ASSIGNMENT_ID = 'appointment_product_assignment_id';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'populate:rejection-product-id-fields';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Adds the appropriate rejection product id fields to the product assignments table';

    public function __construct(protected ProductRepository $productRepository)
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $this->info('Getting appointments and resulting leads');
        $appointmentsAndLeads = $this->getAppointmentsAndResultingLeads();

        $this->info('Handling rejected appointments');
        $this->setValuesForRejectedAppointments($appointmentsAndLeads);

        $this->info('Handling cancelled appointments');
        $this->setValuesForCancelledAppointments($appointmentsAndLeads);
    }

    /**
     * @return Collection|array
     */
    private function getAppointmentsAndResultingLeads(): Collection|array
    {
        $appointmentProductId = $this->productRepository->getAppointmentProductId();

        return ProductAssignment::query()
            ->select('pa1.id AS ' . self::LEAD_PRODUCT_ASSIGNMENT_ID, ProductAssignment::TABLE .'.'. 'id AS ' . self::APPOINTMENT_PRODUCT_ASSIGNMENT_ID)
            ->join(
                ConsumerProduct::TABLE,
                ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ID,
                '=',
                ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_CONSUMER_PRODUCT_ID)
            ->join(
                ServiceProduct::TABLE,
                ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_ID,
                '=',
                ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_SERVICE_PRODUCT_ID)
            ->join(
                Product::TABLE,
                Product::TABLE .'.'. Product::FIELD_ID,
                '=',
                ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_PRODUCT_ID)
            ->join(ProductAppointment::TABLE,
                ProductAppointment::TABLE .'.'. ProductAppointment::CONSUMER_PRODUCT_ID,
                '=',
                ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_CONSUMER_PRODUCT_ID)
            ->join(ProductAssignment::TABLE.' AS pa1', function($join) {
                $join
                    ->on('pa1.'.ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, '=', ProductAppointment::TABLE.'.'.ProductAppointment::LEAD_CONSUMER_PRODUCT_ID)
                    ->on('pa1.'.ProductAssignment::FIELD_COMPANY_ID, '=', ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_COMPANY_ID);
            })
            ->where(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_CHARGEABLE, false)
            ->where(Product::TABLE .'.'. Product::FIELD_ID, $appointmentProductId)
            ->get();
    }

    /**
     * @param Collection $appointmentsAndLeads
     * @return void
     */
    private function setValuesForCancelledAppointments(Collection $appointmentsAndLeads): void
    {
        $appointmentProductId = $this->productRepository->getAppointmentProductId();

        $appointmentProductAssignmentIds = $appointmentsAndLeads->pluck(self::APPOINTMENT_PRODUCT_ASSIGNMENT_ID)->toArray();

        $cancelledAppointmentIds = ProductAssignment::query()
            ->whereDoesntHave(ProductAssignment::RELATION_PRODUCT_REJECTIONS)
            ->whereIn(ProductAssignment::FIELD_ID, $appointmentProductAssignmentIds)
            ->get()
            ->pluck(ProductAssignment::FIELD_ID)
            ->toArray();

        $leadIds = [];

        $appointmentsAndLeads->map(function($appointmentAndLead) use ($cancelledAppointmentIds, &$leadIds) {
            if(in_array($appointmentAndLead->{self::APPOINTMENT_PRODUCT_ASSIGNMENT_ID}, $cancelledAppointmentIds)) {
                $leadIds[] = $appointmentAndLead->{self::LEAD_PRODUCT_ASSIGNMENT_ID};
            }
        });

        // Set cancelled appointments to no affect for rejection
        ProductAssignment::query()
            ->whereIn(ProductAssignment::FIELD_ID, $cancelledAppointmentIds)
            ->update([
                ProductAssignment::FIELD_AFFECT_REJECTION_PERCENTAGE => ProductAssignmentAffectRejectionPercentage::NO_AFFECT->value
            ]);

        // Set leads resulting from cancelled appointment to affect parent product for rejection
        ProductAssignment::query()
            ->whereIn(ProductAssignment::FIELD_ID, $leadIds)
            ->update([
                ProductAssignment::FIELD_AFFECT_REJECTION_PERCENTAGE => ProductAssignmentAffectRejectionPercentage::AFFECT_PARENT_PRODUCT->value,
                ProductAssignment::FIELD_PARENT_PRODUCT_ID => $appointmentProductId
            ]);
    }

    /**
     * @param Collection $appointmentsAndLeads
     * @return void
     */
    private function setValuesForRejectedAppointments(Collection $appointmentsAndLeads): void
    {
        $appointmentProductId = $this->productRepository->getAppointmentProductId();

        $appointmentProductAssignmentIds = $appointmentsAndLeads->pluck(self::APPOINTMENT_PRODUCT_ASSIGNMENT_ID)->toArray();

        $rejectedAppointmentIds = ProductAssignment::query()
            ->whereHas(ProductAssignment::RELATION_PRODUCT_REJECTIONS)
            ->whereIn(ProductAssignment::FIELD_ID, $appointmentProductAssignmentIds)
            ->get()
            ->pluck(ProductAssignment::FIELD_ID)
            ->toArray();

        $leadIds = [];

        $appointmentsAndLeads->map(function($appointmentAndLead) use ($rejectedAppointmentIds, &$leadIds) {
            if(in_array($appointmentAndLead->{self::APPOINTMENT_PRODUCT_ASSIGNMENT_ID}, $rejectedAppointmentIds)) {
                $leadIds[] = $appointmentAndLead->{self::LEAD_PRODUCT_ASSIGNMENT_ID};
            }
        });

        // Set rejected appointments to chargeable
        ProductAssignment::query()
            ->whereIn(ProductAssignment::FIELD_ID, $rejectedAppointmentIds)
            ->update([
                ProductAssignment::FIELD_CHARGEABLE => true
            ]);

        // Set leads resulting from rejected appointment to no affect
        ProductAssignment::query()
            ->whereIn(ProductAssignment::FIELD_ID, $leadIds)
            ->update([
                ProductAssignment::FIELD_AFFECT_REJECTION_PERCENTAGE => ProductAssignmentAffectRejectionPercentage::NO_AFFECT->value,
                ProductAssignment::FIELD_PARENT_PRODUCT_ID => $appointmentProductId
            ]);
    }
}
