<?php

namespace App\Console\Commands;

use App\Models\Odin\Consumer;
use App\Services\HelperService;
use Illuminate\Console\Command;

class UpdateConsumerFormattedPhones extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:consumer-formatted-phones';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Updates the formatted_phone columns for each consumer';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        /** @var HelperService $helperService */
        $helperService = app()->make(HelperService::class);

        Consumer::query()
            ->select([
                 Consumer::FIELD_ID,
                 Consumer::FIELD_PHONE
            ])
            ->chunk(10000, function($consumers) use ($helperService) {
                $data = [];

                foreach ($consumers as $consumer) {
                    $phone = $consumer->{Consumer::FIELD_PHONE};

                    if(!is_null($phone)) {
                        $data[] = [
                            Consumer::FIELD_ID              => $consumer->{Consumer::FIELD_ID},
                            Consumer::FIELD_FORMATTED_PHONE => $helperService->formatUSPhoneNumber($phone)
                        ];
                    }
                }

                Consumer::query()->upsert($data, Consumer::FIELD_ID);
            });
    }
}
