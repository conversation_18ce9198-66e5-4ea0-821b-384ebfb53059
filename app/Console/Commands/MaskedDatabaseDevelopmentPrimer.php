<?php

namespace App\Console\Commands;

use App\Enums\EmailTemplateType;
use App\Models\EmailTemplate;
use App\Models\EmailTemplateBackground;
use App\Models\LeadProcessingConfiguration;
use App\Models\LeadProcessingTeam;
use App\Models\LeadProcessingTimeZoneConfiguration;
use App\Models\LeadProcessor;
use App\Models\User;
use Database\Seeders\ClientTokenServiceSeeder;
use Database\Seeders\GlobalConfigurationSeeder;
use Database\Seeders\PermissionsSeeder;
use Database\Seeders\TestUsersSeeder;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;

class MaskedDatabaseDevelopmentPrimer extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dev:prime-masked-db';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Collection of seeders and presets to run on a fresh masked DB for dev/testing environment';

    /**
     * @var array
     */
    protected array $seeders = [
        PermissionsSeeder::class,
        TestUsersSeeder::class,
        ClientTokenServiceSeeder::class,
        GlobalConfigurationSeeder::class,
    ];

    protected int $exampleAdminUserId;

    protected array $leadProcessingChanges = [
        LeadProcessingConfiguration::FIELD_TIME_ZONE_OPENING_DELAY_IN_MINUTES => 1,
        LeadProcessingConfiguration::FIELD_LEAD_RECENCY_THRESHOLD_IN_SECONDS => 1,
        LeadProcessingConfiguration::FIELD_MINIMUM_REVIEW_TIME => 1,
        LeadProcessingConfiguration::FIELD_LEAD_PROCESSABLE_DELAY_SECONDS => 1,
    ];

    protected array $timezoneChanges = [
        LeadProcessingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR => 0,
        LeadProcessingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR => 23,
    ];

    protected array $emailHeaderFooter = [
        EmailTemplateBackground::FIELD_NAME     => 'Default Lead Delivery Header',
        EmailTemplateBackground::FIELD_HEADER   => "[brand] - New Lead [lead_id]",
        EmailTemplateBackground::FIELD_FOOTER   => "[fixr_address_1]\ [fixr_address_2]\ [fixr_support_email]\  **[fixr_website]**",
        EmailTemplateBackground::FIELD_PERSONAL => false,
    ];

    protected array $emailTemplate = [
        EmailTemplate::FIELD_NAME     => 'Default Lead Delivery',
        EmailTemplate::FIELD_SUBJECT  => "[lead_industry] lead [company_name] / [campaign_name]",
        EmailTemplate::FIELD_CONTENT  => "[lead_industry] - [lead_service] Request\ [full_name]\ [full_address]\ [email]\ [phone]",
        EmailTemplate::FIELD_PERSONAL => false,
    ];

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (App::environment('production')) {
            $this->error("This command cannot be run in Production. Aborting.");

            return Command::FAILURE;
        }

        $this->runSeeders();
        $this->updateLeadProcessingConfig();
        $this->createDefaultLeadDeliveryTemplate();

        $this->info("\nAll commands completed.\n");

        return Command::SUCCESS;
    }

    private function runSeeders(): void
    {
        $this->info("Running seeders...");
        $this->line(array_reduce($this->seeders, fn($out, $v) => $out . "$v\n", ""));

        foreach ($this->seeders as $seeder) {
            app($seeder)->run();
        }
    }

    private function updateLeadProcessingConfig(): void
    {
        $this->info("Updating lead processing & timezone configurations...");
        LeadProcessingConfiguration::query()
            ->first()
            ->update($this->leadProcessingChanges);
        LeadProcessingTimeZoneConfiguration::query()
            ->update($this->timezoneChanges);

        $this->exampleAdminUserId = User::query()
            ->where(User::FIELD_EMAIL, '<EMAIL>')
            ->first()
            ?->id;
        $allIndustriesTeam = LeadProcessingTeam::query()
            ->where(LeadProcessingTeam::FIELD_PRIMARY_QUEUE_CONFIGURATION_ID, 1)
            ->where(LeadProcessingTeam::FIELD_NAME, 'like', '%all industries')
            ->first()
            ?->id;

        if ($this->exampleAdminUserId && $allIndustriesTeam) {
            $this->info("Adding Example Admin User to All Industries lead allocation team...");
            LeadProcessor::query()
                ->create([
                    LeadProcessor::FIELD_USER_ID   => $this->exampleAdminUserId,
                    LeadProcessor::FIELD_LEAD_PROCESSING_TEAM_ID   => $allIndustriesTeam
                ]);
        }
    }

    private function createDefaultLeadDeliveryTemplate(): void
    {
        $this->info("Adding default lead delivery Email Template...");

        $this->emailHeaderFooter[EmailTemplateBackground::FIELD_OWNER_USER_ID] = $this->exampleAdminUserId;
        $headerFooter = EmailTemplateBackground::query()
            ->create($this->emailHeaderFooter);
        $this->emailTemplate[EmailTemplate::FIELD_OWNER_USER_ID] = $this->exampleAdminUserId;
        $this->emailTemplate[EmailTemplate::FIELD_BACKGROUND_ID] = $headerFooter->id;
        $template = EmailTemplate::query()
            ->create($this->emailTemplate);
        EmailTemplate::query()->update([EmailTemplate::FIELD_DEFAULT_LEAD_DELIVERY_TEMPLATE => false]);
        $template->{EmailTemplate::FIELD_DEFAULT_LEAD_DELIVERY_TEMPLATE} = true;

        $template->save();
    }
}
