<?php

namespace App\Console\Commands;

use App\Jobs\AddConsumerProductIdJob;
use App\Models\LeadProcessingAllocation;
use App\Models\LeadProcessingFailedLead;
use App\Models\LeadProcessingHeartbeat;
use App\Models\LeadProcessingHistory;
use App\Models\LeadProcessingInitial;
use App\Models\LeadProcessingPendingReview;
use App\Models\LeadProcessingReservedLead;
use App\Models\LeadProcessingUnderReview;
use Illuminate\Console\Command;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;

class AddConsumerProductIdToQueueTables extends Command
{
    // models that are required for lead processing
    protected array $primaryModels = [
        LeadProcessingInitial::class,
        LeadProcessingReservedLead::class,
        LeadProcessingPendingReview::class,
        LeadProcessingUnderReview::class
    ];

    // models that are not required for lead processing and may takes long time to complete
    protected array $secondaryModels = [
        LeadProcessingHeartbeat::class,
        LeadProcessingFailedLead::class,
        LeadProcessingHistory::class,
        LeadProcessingAllocation::class
    ];

    const CONSUMER_PRODUCT_ID = 'consumer_product_id';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'multi-industry:update-queue-tables';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add consumer_product_id (if available) to the queue tables';

    /**
     * Execute the console command.
     *
     * @return void
     * @throws BindingResolutionException
     */
    public function handle(): void
    {
        $this->info('Processing primary models.');
        $this->processPrimaryModels();

        $this->info('Processing secondary models.');
        $this->processSecondaryModels();
    }

    /**
     * @return void
     * @throws BindingResolutionException
     */
    protected function processPrimaryModels(): void
    {
        foreach ($this->primaryModels as $class) {
            $this->info("Processing model: {$class}");

            app()->make($class)
                ->newQuery()
                ->whereNull(self::CONSUMER_PRODUCT_ID)
                ->chunk(100, function (Collection $models) {
                    /** @var LeadProcessingInitial|LeadProcessingReservedLead|LeadProcessingPendingReview|LeadProcessingUnderReview $model */
                    foreach ($models as $model) {
                        $consumerProductId = $model->lead->consumer?->consumerProducts->first()?->id;

                        if ($consumerProductId) {
                            $model->consumer_product_id = $consumerProductId;
                            $model->save();
                        } else {
                            $this->error("Consumer/Consumer Product does not exist for Lead: {$model->lead->quoteid}");
                        }
                    }
                });
        }
    }

    /**
     * @return void
     * @throws BindingResolutionException
     */
    protected function processSecondaryModels(): void
    {
        foreach ($this->secondaryModels as $class) {
            $this->info("Dispatching jobs for model: {$class}");

            app()->make($class)
                ->newQuery()
                ->whereNull(self::CONSUMER_PRODUCT_ID)
                ->chunk(500, function (Collection $models) {
                    dispatch(new AddConsumerProductIdJob($models));
                });
        }
    }
}
