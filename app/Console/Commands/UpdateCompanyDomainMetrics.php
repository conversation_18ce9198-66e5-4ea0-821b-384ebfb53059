<?php

namespace App\Console\Commands;

use App\Enums\CompanyMetrics\CompanyMetricSources;
use App\Jobs\CompanyMetrics\GetCompanyDomainMetricsJob;
use App\Models\CompanyMetric;
use App\Models\Odin\Company;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Symfony\Component\Console\Output\OutputInterface;

class UpdateCompanyDomainMetrics extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-company-domain-metrics
        {--all : Retrieve domain metrics for all companies for the current month}
        {--current-month : Retrieve domain metrics for all companies without a company metrics entry for the current month}
        {--company-ids=none : Retrieve domain metrics for comma seperated list of company ids (ex --company-ids=1,3,5,7,9)}
        {--batch-size=500 : Max number of companies passed to each update job created}
        {--mode=fresh : Update mode - fresh updates all companies, unprocessed only updates companies without Semrush metrics}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Retrieve and store company domain metrics for all existing companies (--all), companies with '.
                             'the given ids (--companyIds=1,2,3...), or only companies without domain metrics data for the '.
                             'current month (--current-month). Use --mode=fresh (default) to update all selected companies or '.
                             '--mode=unprocessed to only update companies without any Semrush metrics.';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $companyIds = [];
        $companyIdsString = $this->option('company-ids');
        $allCompanies = $this->option('all');
        $currentMonth = $this->option('current-month');
        $batchSize = $this->option('batch-size');
        $mode = $this->option('mode');
        $count = 0;
        $successCount = 0;
        $failureCount = 0;
        $failedCompanies = [];
        $query = Company::query();

        // Validate mode option
        if (!in_array($mode, ['fresh', 'unprocessed'])) {
            $this->error("Invalid mode '$mode'. Must be either 'fresh' or 'unprocessed'.");
            return;
        }

        // Check for both or no arguments given
        if ($allCompanies && $companyIdsString != 'none') {
            $this->error("Cannot pass --all and --companyIds");
            return;
        } else if ($allCompanies && $currentMonth) {
            $this->error("Cannot pass --all and --current-month");
            return;
        } else if ($currentMonth && $companyIdsString != 'none') {
            $this->error("Cannot pass --companyIds and --current-month");
            return;
        } else if (!$allCompanies && $companyIdsString == 'none' && !$currentMonth) {
            $this->error("Must pass either --all, --current-month, or --companyIds=1,2,3...");
            return;
        }

        // Check for companyId values
        if ($companyIdsString != 'none') {
            $companyIds = array_map('intval', explode(',', $companyIdsString));
        }

        // Check for bad values in companyIds list
        if (in_array(0, $companyIds)) {
            $this->error("companyIds can only contain integers.");
            return;
        }

        // Get query depending on option
        if ($allCompanies) {
            $this->info("Updating Domain Metrics for All Companies (mode: $mode).\n", OutputInterface::VERBOSITY_NORMAL);
            $query = $this->getQueryAllCompanies($query);
        }
        if ($companyIds) {
            $this->info("Updating Domain Metrics for Company Ids: ".json_encode($companyIds)." (mode: $mode).\n", OutputInterface::VERBOSITY_NORMAL);
            $query = $this->getQueryCompanyIds($query, $companyIds);
        }
        if ($currentMonth) {
            $this->info("Updating Domain Metrics for Companies without domain metrics data for the current month (mode: $mode).\n", OutputInterface::VERBOSITY_NORMAL);
            $query = $this->getQueryCurrentMonth($query);
        }

        // Apply mode filter
        $query = $this->applyModeFilter($query, $mode);

        // Setup for info printing
        $totalCompanies = $query->count();
        $bar = $this->output->createProgressBar($totalCompanies);
        $bar->start();
        $totalCompaniesCollection = new Collection();

        // Make API requests and create company_metrics entries
        $query->chunk($batchSize, function (Collection $companies) use (&$count, &$successCount, &$failureCount, &$failedCompanies, $bar, &$totalCompaniesCollection) {
            // Dispatch individual jobs for each company since GetCompanyDomainMetricsJob takes single Company
            foreach ($companies as $company) {
                try {
                    GetCompanyDomainMetricsJob::dispatchSync($company);
                    $successCount++;
                } catch (\Exception $e) {
                    $failureCount++;
                    $failedCompanies[] = [
                        'id' => $company[Company::FIELD_ID],
                        'name' => $company[Company::FIELD_NAME],
                        'reason' => $e->getMessage()
                    ];
                }
                $count++;
                $bar->advance();
            }
            $totalCompaniesCollection = $totalCompaniesCollection->merge($companies);
        });

        // Print details
        $bar->finish();
        $this->info("\n\nProcessed Domain Metrics for $count Companies.", OutputInterface::VERBOSITY_NORMAL);
        $this->info("Successful: $successCount", OutputInterface::VERBOSITY_NORMAL);
        $this->info("Failed: $failureCount", OutputInterface::VERBOSITY_NORMAL);

        // Show failed companies with details
        if (!empty($failedCompanies)) {
            $this->info("\nFailed Companies:", OutputInterface::VERBOSITY_NORMAL);
            foreach ($failedCompanies as $failedCompany) {
                $this->error("- {$failedCompany['name']} (id {$failedCompany['id']}): {$failedCompany['reason']}");
            }
        }

        // Company list only printed if -v verbose flag is passed
        $this->info("\nSuccessful Companies:\n", OutputInterface::VERBOSITY_VERBOSE);
        foreach ($totalCompaniesCollection as $company) {
            // Only show companies that were successful (not in failed list)
            $companyId = $company[Company::FIELD_ID];
            $isInFailedList = collect($failedCompanies)->contains('id', $companyId);
            if (!$isInFailedList) {
                $this->info($company[Company::FIELD_NAME].' (id '.$company[Company::FIELD_ID].')', OutputInterface::VERBOSITY_VERBOSE);
            }
        }
    }

    /**
     * Grabs companies with a website
     * @param Builder $query
     * @return Builder
     */
    protected function getQueryAllCompanies(Builder $query): Builder
    {
        return $query
            ->where(function($subQuery) {
                /** @var Builder $subQuery */
                $subQuery
                    ->whereNotNull(Company::FIELD_WEBSITE)
                    ->where(Company::FIELD_WEBSITE, '!=', '');
            });
    }

    /**
     * Companies with the given IDs
     * @param Builder $query
     * @param array $companyIds
     * @return Builder
     */
    protected function getQueryCompanyIds(Builder $query, array $companyIds): Builder
    {
        return $query
            ->where(function($subQuery) use ($companyIds) {
                /** @var Builder $subQuery */
                $subQuery
                    ->whereNotNull(Company::FIELD_WEBSITE)
                    ->where(Company::FIELD_WEBSITE, '!=', '')
                    ->whereIn(Company::FIELD_ID, $companyIds);
            });
    }

    /**
     * Companies that don't have a domain metrics entry for the current month
     * @param Builder $query
     * @return Builder
     */
    protected function getQueryCurrentMonth(Builder $query): Builder
    {
        return $query
            ->select('*')
            ->whereNotExists(function($subQuery) {
                $subQuery
                    ->from(CompanyMetric::TABLE)
                    ->select('*')
                    ->where(CompanyMetric::FIELD_REQUEST_RESPONSE.'->month', now()->subMonth()->month)
                    ->where(CompanyMetric::FIELD_REQUEST_RESPONSE.'->year', now()->year)
                    ->whereColumn(Company::TABLE.'.'.Company::FIELD_ID, '=', CompanyMetric::TABLE.'.'.CompanyMetric::FIELD_COMPANY_ID);
            })
            ->whereNotNull(Company::FIELD_WEBSITE)
            ->where(Company::FIELD_WEBSITE, '!=', '');
    }

    /**
     * Apply mode filter to query
     * @param Builder $query
     * @param string $mode
     * @return Builder
     */
    protected function applyModeFilter(Builder $query, string $mode): Builder
    {
        if ($mode === 'unprocessed') {
            return $query->whereNotExists(function($subQuery) {
                $subQuery
                    ->from(CompanyMetric::TABLE)
                    ->select('*')
                    ->where(CompanyMetric::FIELD_SOURCE, CompanyMetricSources::SEMRUSH->value)
                    ->whereColumn(Company::TABLE.'.'.Company::FIELD_ID, '=', CompanyMetric::TABLE.'.'.CompanyMetric::FIELD_COMPANY_ID);
            });
        }
        
        return $query;
    }
}