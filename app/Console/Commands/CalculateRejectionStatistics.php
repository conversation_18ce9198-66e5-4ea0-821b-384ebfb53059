<?php

namespace App\Console\Commands;

use App\Jobs\CalculateRejectionStatisticsJob;
use Illuminate\Console\Command;

class CalculateRejectionStatistics extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = "calculate:rejection-statistics {timeframe_in_days=30}";

    /**
     * The console command description
     *
     * @var string
     */
    protected $description = '<PERSON>les calculating rejection statistics for each company';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $timeframeInDays = $this->argument('timeframe_in_days');

        CalculateRejectionStatisticsJob::dispatchSync($timeframeInDays);
    }
}
