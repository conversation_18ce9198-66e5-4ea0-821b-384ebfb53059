<?php

namespace App\Console\Commands;

use App\Enums\ComparisonOperator;
use App\Enums\NotificationLinkType;
use App\Models\Legacy\EloquentQuote;
use App\Models\Notification;
use App\Models\Odin\ConsumerProduct;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class AddConsumerProductIdToNotificationTableForLeads extends Command
{
    const DATE_FORMAT = 'Y-m-d';
    const DATE_KEY    = 'date';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update-lead-notifications';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handles adding consumer_product_id (if available) to notification table for leads.';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $startDate = $this->getProcessingPeriodStartDate();
        $endDate   = $this->getProcessingPeriodEndDate();

        $this->info("Processing Notifications for the period ({$startDate->toDateString()} to {$endDate->toDateString()})");
        $this->processNotifications($startDate, $endDate);

        $this->info('Finished');
    }

    /**
     * @return Carbon
     */
    protected function getProcessingPeriodStartDate(): Carbon
    {
        return $this->askForDate();
    }

    /**
     * @return Carbon
     */
    protected function getProcessingPeriodEndDate(): Carbon
    {
        return $this->askForDate('end');
    }

    /**
     * @param string $dateType
     * @return Carbon
     */
    protected function askForDate(string $dateType = 'start'): Carbon
    {
        $date = $this->ask("Enter the {$dateType} date (". self::DATE_FORMAT ."): ");

        $validator = Validator::make(
            [
                self::DATE_KEY => $date,
            ],
            [
                self::DATE_KEY => 'required|date_format:' . self::DATE_FORMAT,
            ]
        );

        if($validator->fails()) {
            $this->error("The {$dateType} date doesn't match the format ". self::DATE_FORMAT);
            return $this->askForDate($dateType);
        }

        return Carbon::createFromFormat(self::DATE_FORMAT, $date);
    }

    /**
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return void
     */
    protected function processNotifications(Carbon $startDate, Carbon $endDate): void
    {
        ini_set('memory_limit','1024M');

        Notification::query()
            ->whereDate(Model::CREATED_AT, ComparisonOperator::GREATER_THAN_EQUAL->value, $startDate)
            ->whereDate(Model::CREATED_AT, ComparisonOperator::LESS_THAN_EQUAL->value, $endDate)
            ->where(function($query) {
                /** @var Builder $query */
                $query
                    ->where(Notification::FIELD_TYPE, Notification::TYPE_LEAD)
                    ->orWhere(Notification::FIELD_LINK_TYPE, NotificationLinkType::LEAD);
            })
            ->whereNotExists(function ($query) {
                /** @var Builder $query */
                $query
                    ->select(DB::raw(1))
                    ->from(ConsumerProduct::TABLE)
                    ->whereColumn(Notification::TABLE . '.' . Notification::FIELD_FROM_ID, ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID);
            })
            ->chunk(100, function (Collection $notifications) {
                /** @var Notification $notification */
                foreach ($notifications as $notification) {
                    /** @var EloquentQuote|null $quote */
                    $quote = EloquentQuote::query()->find($notification->{Notification::FIELD_FROM_ID});
                    $consumerProductId = $quote?->consumer?->consumerProducts->first()?->id;

                    if ($consumerProductId) {
                        $notification->{Notification::FIELD_FROM_ID} = $consumerProductId;
                        $notification->save();
                    } else {
                        $this->error("Quote/Consumer/Consumer-Product does not exist for Lead: {$notification->{Notification::FIELD_FROM_ID}}");
                    }
                }
            });
    }
}
