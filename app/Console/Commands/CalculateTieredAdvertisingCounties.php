<?php

namespace App\Console\Commands;

use App\Jobs\CalculateTieredAdvertisingCountiesJob;
use Illuminate\Console\Command;

class CalculateTieredAdvertisingCounties extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'calculate:tiered-advertising-counties';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Calculates and stores tiered advertising counties.';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        CalculateTieredAdvertisingCountiesJob::dispatchSync();
    }
}
