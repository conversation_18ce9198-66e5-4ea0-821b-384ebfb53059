<?php

namespace App\Console\Commands;

use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Odin\ProductAssignment;
use App\Services\DatabaseHelperService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Throwable;

class FixProductAssignmentDataDiscrepancies extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:product-assignments-data-discrepancies';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fixes discrepancies with cost, delivered, and delivery timestamps';

    /**
     * Execute the console command.
     *
     * @return int
     * @throws Throwable
     */
    public function handle()
    {
        ini_set('memory_limit', '-1');

        $this->updateRecordsWithDifferentCosts();
        $this->updateRecordsWithDifferentDeliveries();
        $this->updateRecordsWithMissingDeliveredAtTimestamps();

        return 0;
    }

    /**
     * @return void
     */
    private function updateRecordsWithMissingDeliveredAtTimestamps(): void
    {
        $this->info("\nUpdating records with different delivered at timestamp values");

        $query = $this->getRecordsWithMissingDeliveredAtTimestamps();

        $bar = $this->output->createProgressBar($query->count());
        $bar->start();

        DB::beginTransaction();

        $query->chunk(1000, function ($productAssignments) use ($bar) {
            foreach ($productAssignments as $productAssignment) {
                ProductAssignment::query()
                    ->where(ProductAssignment::FIELD_ID, $productAssignment->{'odin_id'})
                    ->update([
                        ProductAssignment::FIELD_DELIVERED_AT => Carbon::createFromTimestamp($productAssignment->{EloquentQuoteCompany::TIMESTAMP_DELIVERED})
                    ]);

                $bar->advance();
            }
        });

        DB::commit();

        $bar->finish();
    }

    /**
     * @return void
     */
    private function updateRecordsWithDifferentDeliveries(): void
    {
        $this->info("\nUpdating records with different delivered values");

        $query = $this->getRecordsWithDifferentDeliveryValuesQuery();

        $bar = $this->output->createProgressBar($query->count());
        $bar->start();

        DB::beginTransaction();

        $query->chunk(1000, function ($productAssignments) use ($bar) {
            foreach ($productAssignments as $productAssignment) {
                ProductAssignment::query()
                    ->where(ProductAssignment::FIELD_ID, $productAssignment->{'odin_id'})
                    ->update([
                        ProductAssignment::FIELD_DELIVERED    => $productAssignment->{'legacy_delivered'},
                        ProductAssignment::FIELD_DELIVERED_AT => Carbon::createFromTimestamp($productAssignment->{EloquentQuoteCompany::TIMESTAMP_DELIVERED})
                    ]);

                $bar->advance();
            }
        });

        DB::commit();

        $bar->finish();
    }

    /**
     * @return void
     */
    private function updateRecordsWithDifferentCosts(): void
    {
        $this->info('Updating records with different costs');
        $query = $this->getRecordsWithDifferentCostQuery();

        $bar = $this->output->createProgressBar($query->count());
        $bar->start();

        DB::beginTransaction();

        $query->chunk(1000, function ($productAssignments) use ($bar) {
            foreach ($productAssignments as $productAssignment) {
                if (round($productAssignment->{'legacy_cost'}, 2) !== $productAssignment->{'odin_cost'}) {
                    ProductAssignment::query()
                        ->where(ProductAssignment::FIELD_ID, $productAssignment->{'odin_id'})
                        ->update([
                            ProductAssignment::FIELD_COST => $productAssignment->{'legacy_cost'}
                        ]);
                }

                $bar->advance();
            }

        });

        DB::commit();

        $bar->finish();
    }

    /**
     * @return Builder
     */
    private function getRecordsWithDifferentCostQuery(): Builder
    {
        return EloquentQuoteCompany::query()
            ->select(
                DatabaseHelperService::database() . '.' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID . ' as odin_id',
                DatabaseHelperService::database() . '.' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COST . ' as odin_cost',
                EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::COST . ' as legacy_cost',
            )
            ->leftJoin(
                DatabaseHelperService::database() . '.' . ProductAssignment::TABLE,
                DatabaseHelperService::database() . '.' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_LEGACY_ID,
                '=',
                EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::ID
            )
            ->whereNotNull(DatabaseHelperService::database() . '.' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_LEGACY_ID)
            ->whereRaw(
                'CAST(' . EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::COST . ' as decimal(8,2)) != ' .
                DatabaseHelperService::database() . '.' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COST
            );
    }

    /**
     * @return Builder
     */
    private function getRecordsWithDifferentDeliveryValuesQuery(): Builder
    {
        return EloquentQuoteCompany::query()
            ->select(
                DatabaseHelperService::database() . '.' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID . ' as odin_id',
                DatabaseHelperService::database() . '.' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COST . ' as odin_cost',
                EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::TIMESTAMP_DELIVERED,
                EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::DELIVERED . ' as legacy_delivered',
            )
            ->leftJoin(
                DatabaseHelperService::database() . '.' . ProductAssignment::TABLE,
                DatabaseHelperService::database() . '.' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_LEGACY_ID,
                '=',
                EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::ID
            )
            ->whereNotNull(DatabaseHelperService::database() . '.' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_LEGACY_ID)
            ->where(DatabaseHelperService::database() . '.' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED, false)
            ->where(EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::DELIVERED, true)
            ->orWhere(function($query) {
                $query->where(DatabaseHelperService::database() . '.' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED, true)
                    ->where(EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::DELIVERED, false);
            });
    }

    /**
     * @return Builder
     */
    private function getRecordsWithMissingDeliveredAtTimestamps(): Builder
    {
        return EloquentQuoteCompany::query()
            ->select(
                DatabaseHelperService::database() . '.' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID . ' as odin_id',
                EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::TIMESTAMP_DELIVERED
            )
            ->leftJoin(
                DatabaseHelperService::database() . '.' . ProductAssignment::TABLE,
                DatabaseHelperService::database() . '.' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_LEGACY_ID,
                '=',
                EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::ID
            )
            ->whereNotNull(DatabaseHelperService::database() . '.' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_LEGACY_ID)
            ->where(DatabaseHelperService::database() . '.' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED_AT, 0)
            ->where(EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::TIMESTAMP_DELIVERED, '>', 0);
    }
}
