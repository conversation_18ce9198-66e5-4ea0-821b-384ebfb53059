<?php

namespace App\Console\Commands\dev;

use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use Faker\Generator;
use Illuminate\Console\Command;

class GenerateAppointments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dev:generate-appointments {count=10}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create dummy appointments';

    /**
     * Execute the console command.
     *
     * @param int|null $count
     * @return array
     */
    public function handle(?int $count = null): array
    {
        $output = !$count; // suppress output when called programmatically
        $count  = $count ?: $this->argument('count');
        if ($output) {
            $bar = $this->output->createProgressBar($count);
        }
        $consumerProductIds = [];
        for ($i = 0; $i < $count; $i++) {
            /** @var Consumer $consumer */
            $consumer = Consumer::factory()->create();
            /** @var Address $address */
            $address = Address::factory()->create();
            /** @var ConsumerProduct $consumerProduct */
            $consumerProduct      = ConsumerProduct::factory()->create([
                ConsumerProduct::FIELD_CONSUMER_ID => $consumer->id,
                ConsumerProduct::FIELD_ADDRESS_ID  => $address->id
            ]);
            $consumerProductIds[] = $consumerProduct->id;
            if ($output) {
                $bar->advance();
            }
        }
        if ($output) {
            $bar->finish();
            dump('Successfully Created ConsumerProduct(s): [' . implode(', ', $consumerProductIds) . ']');
        }
        return $consumerProductIds;
    }
}
