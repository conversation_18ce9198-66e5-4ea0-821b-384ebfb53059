<?php

namespace App\Console\Commands\dev;

use App\Enums\Odin\QualityTier;
use App\Http\Controllers\Odin\ResourceAPI\v2\ConsumerController;
use App\Http\Requests\Odin\v2\StoreConsumerRequest;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Repositories\Odin\ConsumerProductRepository;
use Faker\Factory as FakerFactory;
use Illuminate\Console\Command;
use Illuminate\Http\JsonResponse;
use \Illuminate\Contracts\Container\BindingResolutionException;

/**
 * Create multi-industry lead, will not prompt for, or automatically apply any service from Roofing or Solar Industries
 * Use --ui for interactive prompts
 *
 * Use other options for immediate creation e.g.
 *     ./artisan create:multi-industry-lead --zip=90210 --state=CA --service=9
 *
 * Any options not supplied will be auto generated
 *
 * When using the --leads option to generate multiple leads, any of the IndustryService/ZipCode/State values supplied
 * via either the menu or the CLI options will be used for every lead in the batch. Other values will be random fakered.
 */
class CreateMultiIndustryLead extends Command
{
    const AUTOFILL_VALUE    = 'autofill';
    const MAX_LEADS         = 10;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'create:multi-industry-lead {--ui} {--service=} {--zip=} {--state=} {--leads=} {--appointments=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a multi industry Consumer via Odin v2 controller. Provide --ui for interactive creation, or override faker values with --service= --state= and --zip= options. --appointments flag will add appointments of the supplied type to the lead, default to online.';

    /**
     * @return void
     * @throws BindingResolutionException
     */
    public function handle(): void
    {
        $faker = FakerFactory::create();

        $numberOfLeads = intval($this->option('leads'));
        $numberOfLeads = $numberOfLeads ?: 1;
        if ($numberOfLeads > self::MAX_LEADS) {
            $this->warn("Maximum number of leads per command is ".self::MAX_LEADS.", settings leads quantity to ".self::MAX_LEADS);
            $numberOfLeads = self::MAX_LEADS;
        }

        if (config('app.env') === 'production') {
            $this->warn("\tCommand will not run in production, aborting.");
            return;
        }

        $this->line("\n\nGenerating $numberOfLeads multi-industry lead(s)...");

        $industry = null;
        $service = null;
        $zipCode = null;
        $state = null;

        // Prompt for input
        if ($this->option('ui')) {
            $validIndustries = Industry::query()
                ->whereNotIn(Industry::FIELD_NAME, ['solar', 'roofing'])
                ->has(Industry::RELATION_SERVICES)
                ->get();
            $validServices = IndustryService::query()
                ->whereIn(IndustryService::FIELD_INDUSTRY_ID, $validIndustries->pluck(Industry::FIELD_ID))
                ->get();

            if ($validServices) {
                $industryMenuOptions = $validIndustries->map(fn(Industry $v, $i) => $i + 1 . ") $v->name");
                $industryIndex = $this->ask("Please select an Industry:\n\t" . $industryMenuOptions->join("\n\t"));

                while (!intval($industryIndex) || !$validIndustries->get($industryIndex - 1)) {
                    $industryIndex = $this->ask("Please choose a valid number from the list:");
                }

                /** @var Industry $selectedIndustry */
                $industry = $validIndustries->get($industryIndex - 1);

                $filteredServices = $validServices->filter(fn(IndustryService $v) => $v->industry_id === $industry->id);
                $serviceMenuOptions = $filteredServices->map(fn(IndustryService $v, $i) => $i + 1 . ") $v->name");

                $serviceIndex = $this->ask("Please select an IndustryService:\n\t" . $serviceMenuOptions->join("\n\t"));

                while (!intval($serviceIndex) || !$filteredServices->get($serviceIndex - 1)) {
                    $serviceIndex = $this->ask("\tPlease choose a valid number from the list:");
                }

                $service = $filteredServices->get($serviceIndex - 1);

                $zipCodeInput = trim($this->ask("Enter a zipcode (leave blank to use faker-generated zip): "));
                while ($zipCodeInput && !preg_match("/^\d{5}$/", $zipCodeInput)) {
                    $zipCodeInput = $this->ask("\tPlease supply a 5 digit string, or leave blank: ");
                }
                $zipCode = $zipCodeInput ?: self::AUTOFILL_VALUE;

                $stateAbbrInput = trim($this->ask("Enter a state (alpha-2 abbreviation e.g. 'CA') or leave blank for faker-generated: "));
                while ($stateAbbrInput && strlen($stateAbbrInput) !== 2) {
                    $stateAbbrInput = $this->ask("\tPlease supply a 2-character string, or leave blank: ");
                }
                $state = $stateAbbrInput ?: self::AUTOFILL_VALUE;
            }
            else {
                $this->warn("The database does not contain any valid multi-industry Industry/IndustryService models");
                return;
            }
        }
        // Use CLI options
        else if ($this->option('service')) {
            $service = IndustryService::query()
                ->find($this->option('service'));
            $industry = $service?->industry;
        }
        // Find an MI service automatically
        else {
            $industry = Industry::query()
                ->whereNotIn(Industry::FIELD_NAME, ['solar', 'roofing'])
                ->get()
                ->random();

            $service = $industry
                ? IndustryService::query()
                    ->where(IndustryService::FIELD_INDUSTRY_ID, $industry?->id)
                    ->get()
                    ->random()
                : null;
        }

        if (!$service) {
            $message = $this->option('service')
                ? "\tThe supplied Service ID was not valid for a multi industry lead."
                : "\tNo valid Industry Service could be found automatically";
            $this->warn($message);
            return;
        }

        $zipCodeOption = $this->option('zip');
        if ($zipCodeOption && !$zipCode && $zipCode !== self::AUTOFILL_VALUE) {
            $zipCodeOption = trim($zipCodeOption);
            if (!preg_match("/^\d{5}$/", $zipCodeOption)) {
                $this->warn("\tAn invalid zip code was supplied - please supply a 5 digit string");
                return;
            }
            $zipCode = $zipCodeOption;
        }

        $stateOption = $this->option('state');
        if ($stateOption && !$state && $state !== self::AUTOFILL_VALUE) {
            if (strlen(trim($stateOption)) !== 2) {
                $this->warn("\tAn invalid State abbreviation was supplied - please supply a 2-character string");
                return;
            }
            $state = $stateOption;
        }

        for ($i = 1; $i <= $numberOfLeads; $i++) {

            $this->line("\tCreating Consumer $i/$numberOfLeads in $industry->name/$service->name...");

            $maxRequests = $faker->numberBetween(1, 4);

            $payload = [
                "consumer" => [
                    "email"                     => $faker->email(),
                    "phone"                     => $faker->phoneNumber(),
                    "first_name"                => $faker->firstName(),
                    "last_name"                 => $faker->lastName(),
                    "contact_requests"          => $maxRequests,
                    "classification"            => 3,
                    "address_1"                 => $faker->streetAddress(),
                    "city"                      => $faker->city(),
                    "state"                     => (!$state || $state === self::AUTOFILL_VALUE) ? $faker->stateAbbr() : $state,
                    "zip_code"                  => (!$zipCode || $zipCode === self::AUTOFILL_VALUE) ? $faker->postcode() : $zipCode,
                    "own_property"              => "yes",
                    "best_time_to_call"         => "2023-02-05 9am",
                    "best_time_to_call_other"   => "2023-02-05 9am",
                    "ip_address"                => $faker->ipv4(),
                    "property_type_id"          => 1,
                    "origin"                    => "fixr",
                    "originkey"                 => "fixr",
                    "max_contact_requests"      => $maxRequests,
                    "phone_verified"            => true,
                ],
                "industry"  => $industry->slug,
                "service"   => $service->slug,
            ];

            $appointments = gettype($this->option('appointments')) === 'string';
            if ($appointments)
                $this->addAppointments($payload);

            $newRequest = new StoreConsumerRequest();
            $newRequest->setMethod('post');
            $newRequest->request->add($payload);
            $newRequest->header('Content-Type', 'application/json');

            $odinController = app()->make(ConsumerController::class);

            /** @var JsonResponse $response */
            $responseData = $odinController->createConsumer($newRequest, app()->make(ConsumerProductRepository::class))?->getData()?->data;

            if ($responseData->status) {
                $this->addLatLong($responseData->uuid);
                $this->info("\tSuccessfully created a new Consumer with the UUID: $responseData->uuid\n");
            } else {
                $this->warn("\tAn error occurred creating the Consumer, please check logs. Aborting.\n");
                return;
            }
        }
    }

    /**
     * Add a default lat/long so we get a roof image
     * @param string $consumerUuid
     * @return void
     */
    private function addLatLong(string $consumerUuid): void
    {
        Consumer::query()
            ->where(Consumer::FIELD_REFERENCE, $consumerUuid)
            ->first()
            ?->consumerProducts()
            ->first()
            ?->address()
            ->first()
            ?->update([
                Address::FIELD_LATITUDE  => 40.8507233,
                Address::FIELD_LONGITUDE => -74.8794142
            ]);
    }

    private function addAppointments(array &$payload): void
    {
        $type = preg_match("/^in.*home/i", trim($this->option('appointments') ?? '')) ? QualityTier::IN_HOME : QualityTier::ONLINE;
        $payload['consumer']['appointment_type'] = $type->value;
        for ($i = rand(1,3); $i > 0; $i--) {
            $payload['consumer']['appointments'][] = [
                'appointment_date' => now()->addDays(rand(1, 5))->format("Y-m-d"),
                'appointment_time' => rand(10, 16) . ":00:00",
            ];
        }
    }
}
