<?php

namespace App\Console\Commands\dev;

use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use Faker\Generator;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class GenerateAllocatedAppointments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dev:generate-allocated-appointments {company_id} {count=10}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create dummy appointment allocations';

    /**
     * Execute the console command.
     *
     * @return array
     */
    public function handle(): array
    {
        $companyId          = $this->argument('company_id');
        $count              = $this->argument('count');
        $bar = $this->output->createProgressBar($count);
        $consumerProductIds = (new GenerateAppointments)->handle($count);
        $assignmentIds = [];
        foreach ($consumerProductIds as $consumerProductId) {
            $productAssignment = ProductAssignment::factory()->create([
                ProductAssignment::FIELD_COMPANY_ID          => $companyId,
                ProductAssignment::FIELD_CONSUMER_PRODUCT_ID => $consumerProductId
            ]);
            $assignmentIds[] = $productAssignment->id;
            $bar->advance();
        }
        $bar->finish();
        dump('Successfully Created ProductAssignment(s): [' . implode(', ', $assignmentIds) . ']');
        return $assignmentIds;
    }
}
