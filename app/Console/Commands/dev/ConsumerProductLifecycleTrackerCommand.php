<?php

namespace App\Console\Commands\dev;

use App\Models\ConsumerProductLifecycleTracker;
use App\Models\Legacy\EloquentQuote;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use function Laravel\Prompts\select;
use function Laravel\Prompts\text;

class ConsumerProductLifecycleTrackerCommand extends Command //implements PromptsForMissingInput
{

    protected $signature = 'dev:consumer-product-lifecycle-utility {consumerProductId=0}';

    protected $description = 'Get lifecycle updates for a consumer product';

    public function handle()
    {
        $consumerProductId = $this->argument('consumerProductId');

        if($consumerProductId == 0){
            $idType = select(label: 'ID Type', options: ['Consumer Product', 'Consumer', 'Legacy Quote']);
            $id = intval(text($idType . ' ID'));
            $consumerProductId = match($idType){
                'Consumer' => $this->getConsumerProductIdByConsumer($id),
                'Legacy Quote' => $this->getConsumerProductIdByLegacyQuote($id),
                default => $id
            };
        }
        if(!$consumerProductId || $consumerProductId <= 0){
            $this->error('Consumer product not found');
            return;
        }

        /** @var ConsumerProductLifecycleTracker $tracker */
        $tracker = ConsumerProductLifecycleTracker::query()->where(ConsumerProductLifecycleTracker::FIELD_CONSUMER_PRODUCT_ID, $consumerProductId)->first();
        if(!$tracker){
            $this->error('Consumer product lifecycle data not found');
            return;
        }

        $utcOffset = 0;

        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = $tracker->consumerProduct()->first();
        if($consumerProduct){
            $this->line('');
            $this->comment($consumerProduct->consumer->getFullName());
            $this->comment($consumerProduct->address->getFullAddress());
            $utcOffset = $consumerProduct->address->utc;
            $this->comment("UTC: $utcOffset [all timestamps reflect this offset]");
        }

        $updates = $this->parseUpdatesFromTracker($tracker, $utcOffset);
        $lastUpdate = end($updates);
        $this->line('');
        foreach ($updates as $update) {
            $this->info('[' . $update['timestamp'] . '] ' . $update['action']);
            $supportingData = count($update) < 3 ? null : $update['data'];
            if($supportingData){$this->comment($supportingData);}
            if($update != $lastUpdate){
                $this->line('|');
                $this->line('v');
            }
        }
        $this->line('');
    }

    private function parseUpdatesFromTracker(ConsumerProductLifecycleTracker $tracker, int $utcOffset): array
    {
        $updates = collect();
        $updates->push($this->created($tracker, $utcOffset));
        $updates = $updates->merge($this->statusUpdates($tracker, $utcOffset));
        $updates = $updates->merge($this->queueUpdates($tracker, $utcOffset));
        $updates->push($this->goodToSell($tracker, $utcOffset));
        $updates = $updates->merge($this->scheduled($tracker, $utcOffset));
        $updates = $updates->merge($this->attempts($tracker, $utcOffset));
        $updates = $updates->filter();
        return $updates->sortBy('timestamp')->toArray();
    }

    private function created(ConsumerProductLifecycleTracker $tracker, int $utcOffset): Collection
    {
        return collect(['timestamp' => $tracker->consumer_product_created_at->addHours($utcOffset)->toDateTimeString(), 'action' => 'Created']);
    }

    private function statusUpdates(ConsumerProductLifecycleTracker $tracker, int $utcOffset): Collection
    {
        if(!$tracker->status_updates){
            return collect();
        }

        return collect(array_map(function ($update) use ($utcOffset) {
            return collect([
                'timestamp' => $this->convertDateTime($update['updated_at'], $utcOffset),
                'action' => 'Status Change: ' . $update['status']
            ]);
        }, $tracker->status_updates));
    }

    private function queueUpdates(ConsumerProductLifecycleTracker $tracker, int $utcOffset): Collection
    {
        if(!$tracker->queue_updates){
            return collect();
        }
        return collect(array_map(function ($update) use ($utcOffset) {
            return collect([
                'timestamp' => $this->convertDateTime($update['updated_at'], $utcOffset),
                'action' => 'Queue Change: ' . ucfirst($update['queue']),
                'data' => '(reason: ' . $update['reason'] . ', comment: ' . $update['comment'] . ', automatic: ' . $update['automatic'] . ', processor: ' . $update['processor'] . ')'
                ]);
        }, $tracker->queue_updates));
    }

    private function goodToSell(ConsumerProductLifecycleTracker $tracker, int $utcOffset): ?Collection
    {
        if(!$tracker->queue_updates){
            return null;
        }
        return collect(['timestamp' => $tracker->flagged_good_to_sell_at->addHours($utcOffset)->toDateTimeString(), 'action' => 'Good To Sell']);
    }

    private function scheduled(ConsumerProductLifecycleTracker $tracker, int $utcOffset): Collection
    {
        if(!$tracker->allocation_attempts_scheduled){
            return collect();
        }
        return collect(array_map(function ($update) use ($utcOffset) {
            return collect([
                'timestamp' => $this->convertDateTime($update['schedule_calculated_at'], $utcOffset),
                'action' => 'Attempt Scheduled',
                'data' => '(delay: ' . $update['delay_in_seconds']/60 . ' minutes)'
            ]);
        }, $tracker->allocation_attempts_scheduled));
    }

    private function attempts(ConsumerProductLifecycleTracker $tracker, int $utcOffset): Collection
    {
        if(!$tracker->allocation_attempts_scheduled){
            return collect();
        }
        $updates = collect();
        foreach ($tracker->allocation_attempts as $job) {
            foreach ($job['job_attempts'] as $attempt) {
                $proposedAssignments = $attempt['proposed_assignments'] ? $attempt['proposed_assignments']['count'] : 0;
                $campaigns = $attempt['campaigns'] ? $attempt['campaigns']['count'] : 0;
                $updates->push(collect([
                    'timestamp' => $this->convertDateTime($attempt['started_at'], $utcOffset),
                    'action' => 'Allocation Attempted',
                    'data' => '(completed_at: ' . $this->convertDateTime($attempt['completed_at'], $utcOffset) . ', conclusion: ' . $attempt['conclusion'] . ', campaigns: ' . $campaigns . ', proposed_assignments: ' . $proposedAssignments . ')'
                ]));
            }
        }
        return $updates;
    }

    private function convertDateTime(?string $datetime, int $utcOffset): ?string
    {
        try {
            $carbon = Carbon::createFromFormat('Y-m-d H:i:s', $datetime);
        }catch(\Exception $e){
            return $datetime;
        }
        $carbon->addHours($utcOffset);
        return $carbon->toDateTimeString();
    }

    private function getConsumerProductIdByConsumer(int $consumerId): ?int
    {;
        return ConsumerProduct::query()->where('consumer_id', $consumerId)->first()?->id;
    }

    private function getConsumerProductIdByLegacyQuote(int $quoteId): ?int
    {
        /** @var EloquentQuote $quote */
        $quote = EloquentQuote::find($quoteId);
        $consumerId = $quote?->consumer?->id;
        if($consumerId){
            return $this->getConsumerProductIdByConsumer($consumerId);
        }
        return null;
    }
}
