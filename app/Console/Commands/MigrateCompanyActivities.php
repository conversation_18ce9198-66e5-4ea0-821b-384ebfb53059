<?php

namespace App\Console\Commands;

use App\Jobs\MigrateCompanyActivitiesJob;
use App\Models\Action;
use App\Models\ActivityFeed;
use App\Models\Call;
use App\Models\Legacy\EloquentCompany;
use App\Models\RunningWorkflow;
use App\Models\Sales\Task;
use App\Services\BatchHelperService;
use App\Services\DatabaseHelperService;
use App\Services\QueueHelperService;
use Illuminate\Bus\Batch;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Bus;
use Throwable;

class MigrateCompanyActivities extends Command
{
    const BATCH_COUNT = 'batch-count';
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'legacy-migrate:company-activities {--batch-count=20 : The number of batches/jobs to create}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will update the text, calls and task in the activity feed for all companies';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $batchJobs = [];

        $companyIds = $this->getAllCompanyIdsThatNeedActivityFeedUpdated();

        $total = $companyIds->count();

        if($total > 0)
        {
            $this->line("$total companies total");

            $batchCount = (int) $this->option(self::BATCH_COUNT);

            $chunkedCompanyIds = app(BatchHelperService::class)->chunkByBatchCount($batchCount, $companyIds->toArray());

            foreach ($chunkedCompanyIds as $idsChunk) {
                $batchJobs[] = new MigrateCompanyActivitiesJob(collect($idsChunk));
            }

            $batch = Bus::batch($batchJobs)
                ->catch(function (Batch $batch, Throwable $throwable) {
                    logger()->error("Legacy companies migration error: " . $throwable->getMessage());
                })
                ->allowFailures()
                ->name("Legacy Companies Migration")
                ->onConnection('redis')
                ->onQueue(QueueHelperService::QUEUE_NAME_LEGACY_MIGRATION)
                ->dispatch();

            $bar = $this->output->createProgressBar($batch->totalJobs);

            $bar->setFormat("%current%/%max% migration jobs done. Elapsed: %elapsed%");
            $bar->setRedrawFrequency(1);

            $bar->start();

            while (!$batch->finished() && !$batch->cancelled()) {
                $batch = $batch->fresh();
                $bar->setProgress($batch->processedJobs());
            }

            $bar->finish();
        }
        else {
            $this->line("No companies to migrate");
        }

        return 0;

    }

    protected function getAllCompanyIdsThatNeedActivityFeedUpdated() : \Illuminate\Support\Collection
    {
        //todo update this to use odin/company model

        $companyIdsWithoutCalls = EloquentCompany::query()
            ->join(DatabaseHelperService::database().'.'.Call::TABLE, function($join) {
                $join->on(
                    DatabaseHelperService::database().'.'.Call::TABLE.'.'.Call::FIELD_RELATION_ID,
                    '=',
                    EloquentCompany::TABLE.'.'.EloquentCompany::ID
                )
                    ->where(DatabaseHelperService::database().'.'.Call::TABLE.'.'.Call::FIELD_RELATION_TYPE, Call::RELATION_COMPANY);
            })
            //get all the calls that do not have associated activity feeds
            ->whereNotExists(function ($query) {
                $query->select('*')
                    ->from(DatabaseHelperService::database().'.'.ActivityFeed::TABLE)
                    ->join(DatabaseHelperService::database().'.'.Call::TABLE, function($join) {
                        $join->on(
                            DatabaseHelperService::database().'.'.Call::TABLE.'.'.Call::FIELD_ID,
                            '=',
                            DatabaseHelperService::database().'.'.ActivityFeed::TABLE.'.'.ActivityFeed::FIELD_ITEM_ID
                        )
                            ->where(DatabaseHelperService::database().'.'.Call::TABLE.'.'.Call::FIELD_RELATION_TYPE, Call::RELATION_COMPANY);
                    });
            })
            ->groupBy(EloquentCompany::ID)
            ->pluck(EloquentCompany::ID);

        $companyIdsWithoutTasksWorkflows = EloquentCompany::query()
            ->join(DatabaseHelperService::database().'.'.RunningWorkflow::TABLE, function ($join) {
                $join->on(
                    EloquentCompany::TABLE.'.'.EloquentCompany::ID,
                    '=',
                    DatabaseHelperService::database().'.'.RunningWorkflow::TABLE.'.'.RunningWorkflow::VIRTUAL_FIELD_EVENT_COMPANY_ID
                )
                    ->whereNotNull(DatabaseHelperService::database().'.'.RunningWorkflow::TABLE.'.'.RunningWorkflow::VIRTUAL_FIELD_EVENT_COMPANY_ID);
            })
            ->whereNotExists(function ($query) {
                $query->select('*')
                    ->from(DatabaseHelperService::database().'.'.ActivityFeed::TABLE)
                    ->leftjoin(DatabaseHelperService::database().'.'.Task::TABLE, function($join) {
                        $join->on(
                            DatabaseHelperService::database().'.'.Task::TABLE.'.'.Task::FIELD_ID,
                            '=',
                            DatabaseHelperService::database().'.'.ActivityFeed::TABLE.'.'.ActivityFeed::FIELD_ITEM_ID
                        );
                    })
                    ->leftJoin(DatabaseHelperService::database().'.'.RunningWorkflow::TABLE, function ($join) {
                        $join->on(
                            DatabaseHelperService::database().'.'.Task::TABLE.'.'.Task::FIELD_RUNNING_WORKFLOW_ID,
                            '=',
                            DatabaseHelperService::database().'.'.RunningWorkflow::TABLE.'.'.RunningWorkflow::FIELD_ID
                        );
                    })
                    ->whereNotNull(DatabaseHelperService::database().'.'.RunningWorkflow::TABLE.'.'.RunningWorkflow::VIRTUAL_FIELD_EVENT_COMPANY_ID);
            })
            ->groupBy(EloquentCompany::ID)
            ->pluck(EloquentCompany::ID);

        $companyIdsWithoutTasks = EloquentCompany::query()
            ->Join(DatabaseHelperService::database().'.'.Task::TABLE, function($join) {
                $join->on(
                    DatabaseHelperService::database().'.'.Task::TABLE.'.'.Task::VIRTUAL_FIELD_MANUAL_COMPANY_ID,
                    '=',
                    EloquentCompany::TABLE.'.'.EloquentCompany::ID
                )
                    ->whereNotNull(DatabaseHelperService::database().'.'.Task::TABLE.'.'.Task::VIRTUAL_FIELD_MANUAL_COMPANY_ID);
            })
            ->whereNotExists(function ($query) {
                $query->select('*')
                    ->from(DatabaseHelperService::database().'.'.ActivityFeed::TABLE)
                    ->leftjoin(DatabaseHelperService::database().'.'.Task::TABLE, function($join) {
                        $join->on(
                            DatabaseHelperService::database().'.'.Task::TABLE.'.'.Task::FIELD_ID,
                            '=',
                            DatabaseHelperService::database().'.'.ActivityFeed::TABLE.'.'.ActivityFeed::FIELD_ITEM_ID
                        );
                    })
                    ->whereNotNull(DatabaseHelperService::database().'.'.Task::TABLE.'.'.Task::VIRTUAL_FIELD_MANUAL_COMPANY_ID);
            })
            ->groupBy(EloquentCompany::ID)
            ->pluck(EloquentCompany::ID);

        //check for companies that have actions not in the activity_feed
        $companyIdsWithoutActions = EloquentCompany::query()
            ->join(DatabaseHelperService::database().'.'.Action::TABLE, function($join) {
                $join->on(
                    DatabaseHelperService::database().'.'.Action::TABLE.'.'.Action::FIELD_FOR_ID,
                    '=',
                    EloquentCompany::TABLE.'.'.EloquentCompany::ID
                )
                ->where(DatabaseHelperService::database().'.'.Action::TABLE.'.'.Action::FIELD_FOR_RELATION_TYPE, Action::RELATION_TYPE_COMPANY);
            })
            ->whereNotExists(function ($query) {
                $query->select('*')
                    ->from(DatabaseHelperService::database().'.'.ActivityFeed::TABLE)
                    ->join(DatabaseHelperService::database().'.'.Action::TABLE, function($join) {
                        $join->on(
                            DatabaseHelperService::database().'.'.Action::TABLE.'.'.Action::FIELD_ID,
                            '=',
                            DatabaseHelperService::database().'.'.ActivityFeed::TABLE.'.'.ActivityFeed::FIELD_ITEM_ID
                        )
                        ->where(DatabaseHelperService::database().'.'.Action::TABLE.'.'.Action::FIELD_FOR_RELATION_TYPE, Action::RELATION_TYPE_COMPANY);
                    });
            })
            ->groupBy(EloquentCompany::ID)
            ->pluck(EloquentCompany::ID);

        return $companyIdsWithoutCalls->merge($companyIdsWithoutTasks->merge($companyIdsWithoutTasksWorkflows->merge($companyIdsWithoutActions)))->unique();
    }
}
