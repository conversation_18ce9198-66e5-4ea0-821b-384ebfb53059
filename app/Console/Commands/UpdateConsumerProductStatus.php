<?php

namespace App\Console\Commands;

use App\Enums\Odin\Industry as IndustryEnum;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryConfiguration;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class UpdateConsumerProductStatus extends Command
{
    const int CHUNK = 250;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:consumer-product-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update status of the consumer products with incorrect status (pending_allocation)';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $industrySelection = $this->choice(
            'Choose an industry to update',
            // Only update for industries that are migrated to future campaign
            IndustryConfiguration::query()
                ->with([IndustryConfiguration::RELATION_INDUSTRY])
                ->where(IndustryConfiguration::FIELD_FUTURE_CAMPAIGNS_ACTIVE, true)
                ->get()
                ->map(fn(IndustryConfiguration $industryConfiguration) => $industryConfiguration->industry->slug)
                ->flatten()
                ->toArray()
        );

        /** @var Industry $industry */
        $industry =Industry::query()->where(Industry::FIELD_SLUG, $industrySelection)->firstOrFail();
        $serviceProductIds = ServiceProduct::query()
            ->select(ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_ID)
            ->join(IndustryService::TABLE, IndustryService::TABLE . '.' . IndustryService::FIELD_ID, '=', ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_INDUSTRY_SERVICE_ID)
            ->join(Industry::TABLE, Industry::TABLE . '.' . Industry::FIELD_ID, '=', IndustryService::TABLE . '.' . IndustryService::FIELD_INDUSTRY_ID)
            ->where(Industry::TABLE . '.' . Industry::FIELD_SLUG, $industry->slug)
            ->get()
            ->map(fn(ServiceProduct $serviceProduct) => $serviceProduct->id)
            ->toArray();

        $this->info('Starting status update.....');

        ConsumerProduct::query()
            ->whereIn(ConsumerProduct::FIELD_SERVICE_PRODUCT_ID, $serviceProductIds)
            ->where(ConsumerProduct::FIELD_STATUS, ConsumerProduct::STATUS_PENDING_ALLOCATION)
            ->where(ConsumerProduct::CREATED_AT, '<=', now()->subDays(3)) //update statuses of consumer products that are at least 3 days old
            ->withCount([ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT => function(Builder $query) {
                $query->where(ProductAssignment::FIELD_DELIVERED, true);
            }])
            ->chunk(self::CHUNK, function (Collection $products) {
                $this->info("Processing {$products->count()} consumer products");
                $this->processStatus($products);
            });

        $this->info('Finished status update');
    }

    /**
     * @param Collection<ConsumerProduct> $products
     *
     * @return void
     */
    protected function processStatus(Collection $products): void
    {
        $allocatedProductIds = collect();
        $unsoldProductIds = collect();

        foreach ($products as $product) {
            if ($product->product_assignment_count > 0) {
                $allocatedProductIds->push($product->id);
            } else {
                $unsoldProductIds->push($product->id);
            }
        }

        if ($allocatedProductIds->isNotEmpty()) {
            DB::table(ConsumerProduct::TABLE)
                ->whereIn(ConsumerProduct::FIELD_ID, $allocatedProductIds->toArray())
                ->update([ConsumerProduct::FIELD_STATUS => ConsumerProduct::STATUS_ALLOCATED]);
        }

        if ($unsoldProductIds->isNotEmpty()) {
            DB::table(ConsumerProduct::TABLE)
                ->whereIn(ConsumerProduct::FIELD_ID, $unsoldProductIds->toArray())
                ->update([ConsumerProduct::FIELD_STATUS => ConsumerProduct::STATUS_UNSOLD]);
        }
    }
}
