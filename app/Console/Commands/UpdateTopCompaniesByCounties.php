<?php

namespace App\Console\Commands;

use App\Jobs\UpdateTopCompaniesByCountiesJob;
use Illuminate\Console\Command;

class UpdateTopCompaniesByCounties extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:top-companies-by-counties';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update Top Companies by Counties';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        UpdateTopCompaniesByCountiesJob::dispatchSync();
        return Command::SUCCESS;
    }

}
