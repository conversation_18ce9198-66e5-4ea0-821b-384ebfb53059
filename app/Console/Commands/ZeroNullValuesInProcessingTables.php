<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Throwable;
use function Laravel\Prompts\clear;
use function Laravel\Prompts\progress;

class ZeroNullValuesInProcessingTables extends Command
{
    const array MODIFY_TABLES = [
        'lead_processing_reserved_leads',
        'lead_processing_heartbeats',
        'lead_processing_allocations',
        'lead_processing_failed_leads',
        'lead_processing_history',
        'lead_processing_initials',
        'lead_processing_pending_reviews',
        'lead_processing_queue_constraints_bucket_flags',
        'lead_processing_under_reviews',
    ];

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migration:fix-null-processing-ids {--chunk-size=5000 : The number of records per chunk}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update any null vales to 0 before switching nullable on consumer_product_id columns';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $chunkSize = $this->option('chunk-size') ?: 5000;
        clear();
        $results = [];

        foreach (self::MODIFY_TABLES as $table) {
            $this->line('');
            $query = DB::table($table)
                ->select(['id', 'consumer_product_id'])
                ->whereNull('consumer_product_id');

            $count = $query->count();

            if ($count === 0) {
                $this->line("0 null ids found on $table, skipping...");

                continue;
            }
            $chunks = ceil($count / $chunkSize);
            $this->line("$count null ids found on $table. updating...");

            $progressBar = progress("Processing $table in $chunks chunks", $chunks);
            $query->orderBy('id')
                ->chunkById($chunkSize, function(Collection $chunk) use ($progressBar, $table) {
                    $upsertData = $chunk->reduce(function (array $out, $row) {
                         $out[] = [
                             'id'                  => $row->id,
                             'consumer_product_id' => 0,
                         ];
                         return $out;
                    }, []);

                    DB::beginTransaction();
                    try {
                        DB::table($table)->upsert($upsertData, [], ["consumer_product_id"]);
                    }
                    catch (Throwable $e) {
                        DB::rollback();
                        throw $e;
                    }

                    DB::commit();
                    $progressBar->advance();
                }, "$table.id", 'id');

            $progressBar->finish();

            $results[] = "Updated $count rows on $table.";
        }

        $this->info("\n" . implode("\n", $results) . "\n");
    }
}
