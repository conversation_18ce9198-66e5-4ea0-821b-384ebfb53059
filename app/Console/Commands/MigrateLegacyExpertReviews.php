<?php

namespace App\Console\Commands;

use App\Jobs\LegacyMigrations\MigrateLegacyCompanyExpertReviewsJob;
use App\Jobs\LegacyMigrations\MigrateLegacyCompanyUsersJob;
use App\Models\Legacy\EloquentUser;
use App\Models\Odin\CompanyExpertReview;
use App\Models\Odin\CompanyUser;
use App\Services\BatchHelperService;
use App\Services\QueueHelperService;
use Illuminate\Bus\Batch;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Throwable;

class MigrateLegacyExpertReviews extends Command
{
    const BATCH_COUNT = 'batch-count';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'legacy-migrate:company-expert-reviews {--batch-count=20 : The number of batches/jobs to create}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handles the migration of legacy company expert reviews to their new schema.';

    /**
     * Execute the console command.
     *
     * @return int
     * @throws Throwable
     */
    public function handle()
    {
        $this->line("Migrating company expert reviews");

        // Performance
        DB::disableQueryLog();

        $this->line("Creating migration jobs");

        $batchCount = (int) $this->option(self::BATCH_COUNT);

        $batchJobs = [];
        $legacyIdCol = CompanyExpertReview::FIELD_LEGACY_ID;
        $alreadyMigratedlegacyIds = CompanyExpertReview::withTrashed()
            ->selectRaw($legacyIdCol)
            ->pluck(CompanyExpertReview::FIELD_LEGACY_ID)->toArray();

        $reviewIds = DB::connection('readonly')
            ->table('company_expert_reviews')
            ->select('id')
            ->where('review_type', 'company')
            ->whereNotIn('id', $alreadyMigratedlegacyIds)
            ->pluck('id');

        if($reviewIds->count() < 1) {
            $this->line("No users to migrate");
            return 0;
        }

        $chunkedReviewIds = app(BatchHelperService::class)->chunkByBatchCount($batchCount, $reviewIds->toArray());

        foreach ($chunkedReviewIds as $idsChunk) {
            $batchJobs[] = new MigrateLegacyCompanyExpertReviewsJob(collect($idsChunk));
        }

        $batch = Bus::batch($batchJobs)
            ->catch(function (Batch $batch, Throwable $throwable) {
                logger()->error("Legacy company expert reviews migration error: ".$throwable->getMessage());
            })
            ->allowFailures()
            ->name("Legacy Company Expert Reviews Migration")
            ->onConnection(QueueHelperService::QUEUE_CONNECTION)
            ->onQueue(QueueHelperService::QUEUE_NAME_LEGACY_MIGRATION)
            ->dispatch();


        $bar = $this->output->createProgressBar($batch->totalJobs);

        $bar->setFormat("%current%/%max% migration jobs done. Elapsed: %elapsed%");
        $bar->setRedrawFrequency(1);

        $bar->start();

        while (!$batch->finished() && !$batch->cancelled()) {
            $batch = $batch->fresh();
            $bar->setProgress($batch->processedJobs());
        }

        $bar->finish();

        $this->newLine();
        $this->line("Company users migrated");

        return 0;
    }
}
