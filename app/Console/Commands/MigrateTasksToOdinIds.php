<?php

namespace App\Console\Commands;

use App\Models\Odin\Company;
use App\Models\Sales\Task;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class MigrateTasksToOdinIds extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate-to-odin-ids:tasks';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate all existing Task models to use Odin Company ids instead of legacy. Sets the uses_odin_id column to TRUE.';

    protected int $totalTasks;

    protected Builder $companies;

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $this->line("Migrating Tasks to use Odin Company references...");

        $migrationRun = Schema::hasColumn(Task::TABLE, Task::FIELD_USES_ODIN_ID);

        if (!$migrationRun) {
            $this->error("Column \"".Task::FIELD_USES_ODIN_ID."\" not found on tasks table. Please run Task table migration first.");
            return 0;
        }

        $this->totalTasks = Task::query()->count();
        $this->companies = Company::query();

        $this->line("$this->totalTasks tasks total");
        $bar = $this->output->createProgressBar($this->totalTasks);

        $bar->setFormat("%current%/%max% migrated. Elapsed: %elapsed%. Remaining: %remaining%");
        $bar->setRedrawFrequency(1);

        $bar->start();

        Task::query()->chunk(100, function(Collection $tasks) use ($bar) {
            DB::beginTransaction();

            foreach ($tasks as $task) {
                $this->migrateTaskToUseOdinCompanyId($task);
                $bar->advance();
            }

            DB::commit();
        });

        $bar->finish();

        $this->newLine();
        $this->info("Task->company_id migration finished.");

        return 1;

    }

    public function migrateTaskToUseOdinCompanyId(Task $task)
    {
        if ($task->{Task::FIELD_USES_ODIN_ID}) {
            $this->newLine()->warn("Task id(".$task->{Task::FIELD_ID}.") error - already using Odin id, skipped.");
            return;
        }

        $oldPayload = $task->{Task::FIELD_PAYLOAD};
        $legacyCompanyId = $oldPayload['company_id'] ?? null;
        $company = ($legacyCompanyId >= 0)
            ? $this->companies->where(Company::FIELD_LEGACY_ID, $legacyCompanyId)->first()
            : null;

        if ($company) {
            $newPayload = [
                'company_id' => $company->{Company::FIELD_ID},
            ];

            $task->update([
                Task::FIELD_PAYLOAD => $newPayload,
                Task::FIELD_USES_ODIN_ID => true,
            ]);
        }
        else {
            $this->newLine()->warn("Task ".$task->{Task::FIELD_ID}." error - could not locate company from legacy id.");
        }
    }
}
