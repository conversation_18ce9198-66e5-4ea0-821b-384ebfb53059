<?php

namespace App\Console\Commands;

use App\Enums\ContactIdentification\SearchableFieldType;
use App\Models\ContactIdentification\IdentifiedContact;
use App\Models\ContactIdentification\PossibleContact;
use App\Services\ContactIdentification\ContactIdentificationService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class IdentifyContact extends Command
{
    protected $signature = 'identify-contact
                            {--dry-run : Preview changes without executing them}
                            {--value= : The value to identify}';

    protected $description = 'Identify the contact related to the value provided';

    /**
     * @param ContactIdentificationService $contactIdentificationService
     * @return int
     */
    public function handle(ContactIdentificationService $contactIdentificationService): int
    {
        $this->info('Starting contact identification process...');

        $isDryRun = $this->option('dry-run');
        $value = $this->option('value');

        if ($isDryRun) {
            $this->warn('Running in DRY RUN mode - no changes will be made');
        }

        try {
            DB::beginTransaction();

            $type = str_contains($value, '@') ? SearchableFieldType::EMAIL : null;

            if (!$type) {
                throw new Exception('Type not supported');
            }

            $this->ensureIdentifiedContactState(
                value: $value,
                type : $type,
            );

            $identifiedContact = $contactIdentificationService->createIdentifiedContactAndDispatchJob(
                identifierValue: $value,
                type           : SearchableFieldType::EMAIL,
                syncronous     : true
            );

            $this->info("\n");

            if ($identifiedContact->possibleContacts->count() > 0) {
                $this->info("Contacts found: " . $identifiedContact->possibleContacts
                        ->map(fn(PossibleContact $contact) => $contact->identifiable::class . ':' . $contact->identifiable->id)
                        ->join("\n")
                );
            } else {
                $this->info('No contacts found');
            }

            if ($identifiedContact->nominatedContact) {
                $this->info("\n");

                $this->info("Nominated: " . ($identifiedContact->nominatedContact ?
                        $identifiedContact->nominatedContact->identifiable::class . ':' . $identifiedContact->nominatedContact->identifiable->id
                        : 'None')
                );
            }

            if (!$isDryRun) {
                $this->warn('Data has been persisted');
                DB::commit();
            } else {
                $this->warn('Nothing has been persisted');
                DB::rollBack();
            }

            return self::SUCCESS;

        } catch (Exception $e) {
            DB::rollBack();
            $this->error("Error occurred: " . $e->getMessage());
            return self::FAILURE;
        }
    }


    /**
     * @param string $value
     * @param SearchableFieldType $type
     * @return void
     */
    public function ensureIdentifiedContactState(string $value, SearchableFieldType $type): void
    {
        /** @var ?IdentifiedContact $identifiedContact */
        $identifiedContact = IdentifiedContact::query()
            ->where(IdentifiedContact::FIELD_IDENTIFIER_VALUE, $value)
            ->where(IdentifiedContact::FIELD_IDENTIFIER_FIELD_TYPE, $type->value)
            ->latest()
            ->first();

        if ($identifiedContact?->nominatedContact) {
            $identifiedContact->update([
                IdentifiedContact::FIELD_NOMINATED_CONTACT_ID => null
            ]);
        }
    }
}
