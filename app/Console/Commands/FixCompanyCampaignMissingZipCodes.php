<?php

namespace App\Console\Commands;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModule;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyConfiguration;
use App\Services\DatabaseHelperService;
use Illuminate\Console\Command;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Throwable;

class FixCompanyCampaignMissingZipCodes extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:company-campaign-zip-codes';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fixes company campaign zip code coverage where zip code targeting is not enabled at the company or campaign level.';

    /**
     * Execute the console command.
     *
     * @return int
     * @throws Throwable
     */
    public function handle(): int
    {
        ini_set('memory_limit', '-1');
        DB::statement('SET SESSION group_concat_max_len = 10000');

        $this->info("Getting Campaign Locations");
        $campaignLocations = $this->getCountyCampaignZipCodeCounts();
        $campaignCount = $campaignLocations->unique('campaign_id')->count();

        $this->info("Total county level campaigns: $campaignCount");
        $campaignCountyCount = $campaignLocations->count();
        $this->info("Total campaign counties: $campaignCountyCount");

        $this->info("Getting Zip Code Counts");
        $zipCodeCounts = $this->getCountyZipCodeCounts();

        $missingZips = [];
        $newLocationModuleLocations = [];
        $createdAtNow = now()->format("Y-m-d H:i:s");

        $this->info("Building missing zip code entries");
        $bar = $this->output->createProgressBar($campaignCountyCount);
        $bar->setFormat("%bar% %percent%% %current%/%max% \nElapsed: %elapsed% \nEst. Remaining: %remaining% \nMemory Usage: %memory%\n");

        $bar->start();

        foreach ($campaignLocations as $campaignLocation) {
            $realCountyZipCount = $zipCodeCounts[$campaignLocation->{Location::STATE_ABBREVIATION}][$campaignLocation->{Location::COUNTY_KEY}]->first()->{'zip_count'};

            if ($campaignLocation->{'zip_count'} !== $realCountyZipCount) {
                $coveredZips = explode(',', $campaignLocation->{'covered_zip_codes'});
                $allZips = explode(',', $zipCodeCounts[$campaignLocation->{Location::STATE_ABBREVIATION}][$campaignLocation->{Location::COUNTY_KEY}]->first()->{'zip_codes'});

                $zipsToAdd = array_diff($allZips, $coveredZips);

                $zipLocationIdMapStrings = explode(",", $zipCodeCounts[$campaignLocation->{Location::STATE_ABBREVIATION}][$campaignLocation->{Location::COUNTY_KEY}]->first()->{'zip_code_locations'});

                // Zip => Location ID of zip entry
                $zipLocationIdMap = [];
                foreach ($zipLocationIdMapStrings as $zipId) {
                    list($key, $value) = explode(":", $zipId);
                    $zipLocationIdMap[trim($key)] = trim($value);
                }

                foreach ($zipsToAdd as $key => $zip) {
                    $newLocationModuleLocations[] = [
                        CompanyCampaignLocationModuleLocation::FIELD_MODULE_ID      => $campaignLocation->{'cclm_id'},
                        CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID    => $zipLocationIdMap[$zip],
                        CompanyCampaignLocationModuleLocation::FIELD_ZIP_CODE       => $zip,
                        CompanyCampaignLocationModuleLocation::CREATED_AT           => $createdAtNow,
                    ];
                }

                if (array_key_exists($campaignLocation->{'campaign_id'}, $missingZips)) {
                    $missingZips[$campaignLocation->{'campaign_id'}] += 1;
                } else {
                    $missingZips[$campaignLocation->{'campaign_id'}] = 1;
                }
            }

            $bar->advance();
        }

        $bar->finish();
        $this->info("New module locations: ".count($newLocationModuleLocations));

        $this->info("Creating new campaign location module locations for missing zip codes");
        $bar = $this->output->createProgressBar(count($newLocationModuleLocations));
        $bar->setFormat("%bar% %percent%% %current%/%max% \nElapsed: %elapsed% \nEst. Remaining: %remaining% \nMemory Usage: %memory%\n");
        $bar->start();

        DB::transaction(function () use ($newLocationModuleLocations, $bar) {
            foreach (array_chunk($newLocationModuleLocations, 5000) as $chunkInsert) {
                CompanyCampaignLocationModuleLocation::insert($chunkInsert);
                $bar->advance(5000);
            }
        });

        $bar->finish();
        $this->info("New module creation complete.");

        $this->info("Getting updated campaign info for verify");
        $campaignLocations = $this->getCountyCampaignZipCodeCounts();

        $this->info("Verifying full county coverage for non-zip code targeted campaigns");
        $bar = $this->output->createProgressBar($campaignCountyCount);
        $bar->setFormat("%bar% %percent%% %current%/%max% \nElapsed: %elapsed% \nEst. Remaining: %remaining% \nMemory Usage: %memory%\n");
        $bar->start();

        foreach ($campaignLocations as $campaignLocation) {
            $realCountyZipCount = $zipCodeCounts[$campaignLocation->{Location::STATE_ABBREVIATION}][$campaignLocation->{Location::COUNTY_KEY}]->first()->{'zip_count'};

            if ($campaignLocation->{'zip_count'} !== $realCountyZipCount) {
                $this->error("Zip Code count does not match for campaign ".$campaignLocation->{'campaign_id'});
            }

            $bar->advance();
        }

        return 0;
    }

    /**
     * @return Collection
     */
    public function getCountyCampaignZipCodeCounts(): Collection
    {
        return CompanyCampaign::query()
            ->join(Company::TABLE, Company::TABLE.'.'.Company::FIELD_ID, '=', CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_COMPANY_ID)
            ->join(CompanyConfiguration::TABLE, CompanyConfiguration::TABLE.'.'.CompanyConfiguration::FIELD_COMPANY_ID, Company::TABLE.'.'.Company::FIELD_ID)
            ->join(CompanyCampaignLocationModule::TABLE, CompanyCampaignLocationModule::TABLE.'.'.CompanyCampaignLocationModule::FIELD_CAMPAIGN_ID, '=', CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_ID)
            ->join(CompanyCampaignLocationModuleLocation::TABLE, CompanyCampaignLocationModuleLocation::TABLE.'.'.CompanyCampaignLocationModuleLocation::FIELD_MODULE_ID, CompanyCampaignLocationModule::TABLE.'.'.CompanyCampaignLocationModule::FIELD_ID)
            ->join(Location::TABLE, fn(JoinClause $join) =>
            $join->on(Location::TABLE.'.'.Location::ID, '=', CompanyCampaignLocationModuleLocation::TABLE.'.'.CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID)
                ->whereNotNull(Location::TABLE.'.'.Location::ZIP_CODE)
                ->where(Location::TABLE.'.'.Location::TYPE, Location::TYPE_ZIP_CODE)
            )
            ->where(CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_ZIP_CODE_TARGETED, false)
            ->where(CompanyConfiguration::TABLE.'.'.CompanyConfiguration::FIELD_UNRESTRICTED_ZIP_CODE_TARGETING, false)
            ->groupBy(CompanyCampaignLocationModule::TABLE.'.'.CompanyCampaignLocationModule::FIELD_ID, Location::TABLE.'.'.Location::STATE_ABBREVIATION, Location::TABLE.'.'.Location::COUNTY_KEY)
            ->orderBy(Location::TABLE.'.'.Location::ZIP_CODE)
            ->get([
                CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_ID.' as campaign_id',
                CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_NAME.' as campaign_name',
                CompanyCampaignLocationModule::TABLE.'.'.CompanyCampaignLocationModule::FIELD_ID.' as cclm_id',
                Location::TABLE.'.'.Location::STATE_ABBREVIATION,
                Location::TABLE.'.'.Location::COUNTY_KEY,
                DB::raw('COUNT(DISTINCT '.Location::TABLE.'.'.Location::ZIP_CODE.') as zip_count'),
                DB::raw('GROUP_CONCAT(DISTINCT '.Location::TABLE.'.'.Location::ZIP_CODE.') as covered_zip_codes'),
            ]);
    }

    /**
     * Returns collection grouped by state and county for speed, for access its ['CO']['denver']->first()->{'zip_codes'}
     * @return Collection
     */
    public function getCountyZipCodeCounts(): Collection
    {
        return DB::table(Location::TABLE.' as zips')
            ->join(Location::TABLE.' as counties', fn (JoinClause $join) =>
            $join->on('counties.'.Location::COUNTY_KEY, 'zips.'.Location::COUNTY_KEY)
                ->on('counties.'.Location::STATE_ABBREVIATION, 'zips.'.Location::STATE_ABBREVIATION)
                ->where('counties.'.Location::TYPE, Location::TYPE_COUNTY)
            )
            ->where('zips.'.Location::TYPE, Location::TYPE_ZIP_CODE)
            ->groupBy('counties.'.Location::STATE_ABBREVIATION, 'counties.'.Location::COUNTY_KEY)
            ->orderBy('zips.'.Location::ZIP_CODE)
            ->get([
                'counties.'.Location::STATE_ABBREVIATION,
                'counties.'.Location::COUNTY_KEY,
                DB::raw('COUNT(DISTINCT zips.'.Location::ZIP_CODE.') as zip_count'),
                DB::raw('GROUP_CONCAT(DISTINCT zips.'.Location::ZIP_CODE.') as zip_codes'),
                DB::raw('GROUP_CONCAT(DISTINCT CONCAT(zips.'.Location::ZIP_CODE.', \':\', zips.'.Location::ID.')) as zip_code_locations')
            ])->groupBy([Location::STATE_ABBREVIATION, Location::COUNTY_KEY]);
    }
}
