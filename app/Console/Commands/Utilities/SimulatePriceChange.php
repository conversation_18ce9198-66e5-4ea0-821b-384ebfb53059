<?php

namespace App\Console\Commands\Utilities;

use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Legacy\LeadType;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Services\DatabaseHelperService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class SimulatePriceChange extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'simulate:price-change';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Simulates price change';

    protected bool $ignoreBudget = true;
    protected Carbon $start;
    protected Carbon $end;

    protected array $newPrices = ['AL' => ['S' => [110,98,80,63,23,13],'P' => [132,118,96,76,28,16]],'AK' => ['S' => [110,98,80,63,23,13],'P' => [132,118,96,76,28,16]],'AZ' => ['S' => [133,115,98,75,30,15],'P' => [160,138,118,90,36,18]],'AR' => ['S' => [110,95,80,63,23,13],'P' => [132,114,96,76,28,16]],'CA' => ['S' => [165,140,120,100,45,20],'P' => [198,168,144,120,54,31]],'CO' => ['S' => [138,118,95,75,30,15],'P' => [166,142,114,90,36,18]],'CT' => ['S' => [145,115,100,70,40,20],'P' => [172,140,117,86,47,23]],'DE' => ['S' => [110,98,85,63,23,13],'P' => [132,118,102,76,28,16]],'DC' => ['S' => [115,98,80,60,23,13],'P' => [138,118,96,72,28,16]],'FL' => ['S' => [150,130,110,90,25,15],'P' => [180,156,132,108,30,15]],'GA' => ['S' => [110,98,85,63,23,13],'P' => [132,118,102,76,28,16]],'HI' => ['S' => [170,145,105,90,45,25],'P' => [203,172,125,109,55,30]],'ID' => ['S' => [110,98,85,63,23,13],'P' => [132,118,102,76,28,16]],'IL' => ['S' => [110,98,85,65,23,15],'P' => [132,118,102,78,28,18]],'IN' => ['S' => [110,95,78,63,23,13],'P' => [132,114,94,76,28,16]],'IA' => ['S' => [110,95,78,63,23,13],'P' => [132,114,94,76,28,16]],'KS' => ['S' => [110,95,78,63,23,13],'P' => [132,114,94,76,28,16]],'KY' => ['S' => [110,95,78,63,23,13],'P' => [132,114,94,76,28,16]],'LA' => ['S' => [110,95,78,63,23,13],'P' => [132,114,94,76,28,16]],'ME' => ['S' => [110,95,78,63,23,13],'P' => [132,114,94,76,28,16]],'MD' => ['S' => [170,145,115,85,45,20],'P' => [203,172,140,101,55,25]],'MA' => ['S' => [170,145,115,100,50,25],'P' => [203,172,140,101,55,25]],'MI' => ['S' => [110,95,78,63,23,13],'P' => [132,114,94,76,28,16]],'MN' => ['S' => [118,100,85,65,23,13],'P' => [142,120,102,78,28,16]],'MS' => ['S' => [110,95,78,63,23,13],'P' => [132,114,94,76,28,16]],'MO' => ['S' => [110,95,78,63,23,13],'P' => [132,114,94,76,28,16]],'MT' => ['S' => [110,95,78,63,23,13],'P' => [132,114,94,76,28,16]],'NE' => ['S' => [110,95,78,63,23,13],'P' => [132,114,94,76,28,16]],'NV' => ['S' => [150,125,100,70,40,20],'P' => [179,148,117,86,47,23]],'NH' => ['S' => [125,108,88,68,25,15],'P' => [150,130,106,82,30,18]],'NJ' => ['S' => [195,155,115,100,50,25],'P' => [234,187,140,117,60,30]],'NM' => ['S' => [130,115,98,80,33,18],'P' => [156,138,118,96,40,22]],'NY' => ['S' => [125,105,80,60,35,15],'P' => [148,125,94,70,40,15]],'NC' => ['S' => [130,115,98,70,25,15],'P' => [156,138,118,84,30,18]],'ND' => ['S' => [110,98,78,63,23,13],'P' => [132,118,94,76,28,16]],'OH' => ['S' => [110,98,85,63,23,13],'P' => [132,118,102,76,28,16]],'OK' => ['S' => [110,98,85,63,23,13],'P' => [132,118,102,76,28,16]],'OR' => ['S' => [110,98,85,63,23,13],'P' => [132,118,102,76,28,16]],'PA' => ['S' => [110,98,85,63,23,13],'P' => [132,118,102,76,28,16]],'PR' => ['S' => [110,98,85,63,23,13],'P' => [132,118,102,76,28,16]],'RI' => ['S' => [138,120,100,80,33,18],'P' => [166,144,120,96,40,22]],'SC' => ['S' => [130,118,100,70,25,15],'P' => [156,142,120,84,30,18]],'SD' => ['S' => [110,95,78,63,23,13],'P' => [132,114,94,76,28,16]],'TN' => ['S' => [110,95,78,63,23,13],'P' => [132,114,94,76,28,16]],'TX' => ['S' => [150,130,110,90,25,15],'P' => [180,156,132,108,30,15]],'UT' => ['S' => [125,108,88,68,25,15],'P' => [150,130,106,82,30,18]],'VT' => ['S' => [110,95,78,63,23,13],'P' => [132,114,94,76,28,16]],'VA' => ['S' => [110,95,80,65,23,18],'P' => [132,114,96,78,28,22]],'WA' => ['S' => [110,95,78,63,23,13],'P' => [132,114,94,76,28,16]],'WV' => ['S' => [110,95,78,63,23,13],'P' => [132,114,94,76,28,16]],'WI' => ['S' => [110,98,85,63,23,13],'P' => [132,118,102,76,28,16]],'WY' => ['S' => [110,95,78,63,23,13],'P' => [132,114,94,76,28,16]]];
    protected array $currentPrices = ['AL' => ['S' => [70,65,50,35,20,10],'P' => [86,78,62,40,25,10]],'AK' => ['S' => [70,65,50,35,20,10],'P' => [86,78,62,40,25,10]],'AZ' => ['S' => [115,100,85,60,35,15],'P' => [140,117,100,70,40,16]],'AR' => ['S' => [70,60,50,35,20,10],'P' => [86,70,62,40,25,8]],'CA' => ['S' => [135,110,85,70,45,20],'P' => [170,130,104,85,52,31]],'CO' => ['S' => [125,105,80,60,35,15],'P' => [148,125,94,70,40,16]],'CT' => ['S' => [145,115,100,70,40,20],'P' => [172,140,117,86,47,23]],'DE' => ['S' => [70,65,60,35,20,10],'P' => [86,78,62,40,25,10]],'DC' => ['S' => [80,65,50,30,20,10],'P' => [94,78,62,47,26,15]],'FL' => ['S' => [100,70,60,45,25,15],'P' => [117,86,70,55,30,15]],'GA' => ['S' => [70,65,60,35,20,10],'P' => [86,78,62,40,25,10]],'HI' => ['S' => [170,145,105,90,45,25],'P' => [203,172,125,109,55,30]],'ID' => ['S' => [70,65,60,35,20,10],'P' => [86,78,62,40,25,10]],'IL' => ['S' => [70,65,60,40,20,15],'P' => [86,78,62,47,25,15]],'IN' => ['S' => [70,60,45,35,20,10],'P' => [86,70,55,40,25,10]],'IA' => ['S' => [70,60,45,35,20,10],'P' => [86,70,55,40,25,10]],'KS' => ['S' => [70,60,45,35,20,10],'P' => [86,70,55,40,25,10]],'KY' => ['S' => [70,60,45,35,20,10],'P' => [86,70,55,40,25,10]],'LA' => ['S' => [70,60,45,35,20,10],'P' => [86,70,55,40,25,10]],'ME' => ['S' => [70,60,45,35,20,10],'P' => [86,70,55,40,25,10]],'MD' => ['S' => [170,145,115,85,45,20],'P' => [203,172,140,101,55,25]],'MA' => ['S' => [170,145,115,100,50,25],'P' => [203,172,140,101,55,25]],'MI' => ['S' => [70,60,45,35,20,10],'P' => [86,70,55,40,25,10]],'MN' => ['S' => [85,70,60,40,20,10],'P' => [101,86,70,47,25,15]],'MS' => ['S' => [70,60,45,35,20,10],'P' => [86,70,55,40,25,15]],'MO' => ['S' => [70,60,45,35,20,10],'P' => [86,70,55,40,25,10]],'MT' => ['S' => [70,60,45,35,20,10],'P' => [86,70,55,40,25,10]],'NE' => ['S' => [70,60,45,35,20,10],'P' => [86,70,55,40,25,10]],'NV' => ['S' => [150,125,100,70,40,20],'P' => [179,148,117,86,47,23]],'NH' => ['S' => [100,85,65,45,25,15],'P' => [117,101,78,55,30,15]],'NJ' => ['S' => [195,155,115,100,50,25],'P' => [234,187,140,117,60,30]],'NM' => ['S' => [110,100,85,70,40,20],'P' => [133,117,101,86,47,23]],'NY' => ['S' => [125,105,80,60,35,15],'P' => [148,125,94,70,40,15]],'NC' => ['S' => [110,100,85,50,25,15],'P' => [133,117,101,62,30,15]],'ND' => ['S' => [70,65,45,35,20,10],'P' => [86,70,55,40,25,10]],'OH' => ['S' => [70,65,60,35,20,10],'P' => [86,78,70,40,25,10]],'OK' => ['S' => [70,65,60,35,20,10],'P' => [86,78,70,40,25,10]],'OR' => ['S' => [70,65,60,35,20,10],'P' => [86,78,70,40,25,10]],'PA' => ['S' => [70,65,60,35,20,10],'P' => [86,78,70,40,25,10]],'PR' => ['S' => [70,65,60,35,20,10],'P' => [86,78,70,40,25,10]],'RI' => ['S' => [125,110,90,70,40,20],'P' => [148,138,109,86,47,25]],'SC' => ['S' => [110,105,90,50,25,15],'P' => [133,125,109,62,30,15]],'SD' => ['S' => [70,60,45,35,20,10],'P' => [86,70,55,40,25,10]],'TN' => ['S' => [70,60,45,35,20,10],'P' => [86,70,55,40,25,10]],'TX' => ['S' => [100,85,70,45,25,15],'P' => [117,101,86,55,30,15]],'UT' => ['S' => [100,85,65,45,25,15],'P' => [117,101,86,55,30,15]],'VT' => ['S' => [70,60,45,35,20,10],'P' => [86,70,55,40,25,10]],'VA' => ['S' => [70,60,50,40,20,20],'P' => [86,70,55,40,25,10]],'WA' => ['S' => [70,60,45,35,20,10],'P' => [86,78,70,40,25,10]],'WV' => ['S' => [70,60,45,35,20,10],'P' => [86,70,55,40,25,10]],'WI' => ['S' => [70,65,60,35,20,10],'P' => [86,78,70,40,25,10]],'WY' => ['S' => [70,60,45,35,20,10],'P' => [86,70,55,40,25,10]]];

    /**
     * Execute the console command.
     *
     */
    public function handle()
    {
        $this->start = Carbon::now()->subMonth()->startOfMonth();
        $this->end   = Carbon::now()->subMonth()->endOfMonth();

        $this->outputRevenueWithProposedPrices();
    }

    /**
     * @return Collection
     */
    private function simulateCosts(): Collection
    {
        $quoteCompanies = $this->getQuoteCompanies();

        return $quoteCompanies->map(function($quoteCompany) use ($quoteCompanies) {
            $cost = $this->getCost(
                $quoteCompany->{'lead_type'},
                $quoteCompany->{LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID},
                $quoteCompany->{EloquentQuoteCompany::COST},
                $quoteCompany->{EloquentAddress::STATE_ABBR},
                $quoteCompany->{LeadCampaign::MAX_DAILY_LEAD},
                $quoteCompany->{LeadCampaign::MAX_DAILY_SPEND}
            );

            $bidOverCurrentFloor = $this->getBidOverCurrentFloorPrice(
                $quoteCompany->{'lead_type'},
                $quoteCompany->{LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID},
                $quoteCompany->{EloquentQuoteCompany::COST},
                $quoteCompany->{EloquentAddress::STATE_ABBR}
            );

            return collect([
                'current_cost'           => $quoteCompany->{EloquentQuoteCompany::COST},
                'new_cost'               => $cost,
                'bid_over_current_floor' => $bidOverCurrentFloor,
                'bid_over_new_floor'     => max($quoteCompany->{EloquentQuoteCompany::COST} - $cost, 0),
                'quoteid'                => $quoteCompany->{EloquentQuoteCompany::QUOTE_ID},
                'state'                  => $quoteCompany->state,
                'lead_sales_type_id'     => $quoteCompany->{LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID}
            ]);
        });
    }

    /**
     * @param int $leadType
     * @param int $salesType
     * @param float $originalCost
     * @param string $state
     * @return float|int
     */
    private function getBidOverCurrentFloorPrice(int $leadType, int $salesType, float $originalCost, string $state,): float|int
    {
        $floorPrice = $this->getCurrentPricingFromArray($leadType, $salesType, $state);

        if($originalCost > $floorPrice) {
            return $originalCost - $floorPrice;
        }

        return 0;
    }

    private function getCost(int $leadType, int $salesType, float $originalCost, string $state, ?int $maxLeads, ?int $maxSpend) {
        $newCost = max($originalCost, $this->getNewPricingFromArray($leadType, $salesType, $state));

        if($this->ignoreBudget) {
            return $newCost;
        }

        if(empty($maxLeads) && empty($maxSpend)) {
            /**
            if($state == 'VT' && $originalCost != $this->getPricingFromArray($leadType, $salesType, $state)) {
                dd([$quoteid,
                    $originalCost,
                    $this->getPricingFromArray($leadType, $salesType, $state)
                ]);
            }
             **/
            return $newCost;
        }

        return $originalCost;
    }

    /**
     * @param int $leadType
     * @param int $salesType
     * @param string $state
     * @return int
     */
    private function getNewPricingFromArray(int $leadType, int $salesType, string $state): int
    {
        $leadType = $leadType === 1 ? 'S' : 'P';

        return $this->newPrices[$state][$leadType][$salesType-1];
    }

    /**
     * @param int $leadType
     * @param int $salesType
     * @param string $state
     * @return int
     */
    private function getCurrentPricingFromArray(int $leadType, int $salesType, string $state): int
    {
        $leadType = $leadType === 1 ? 'S' : 'P';

        return $this->currentPrices[$state][$leadType][$salesType-1];
    }

    /**
     * @return Collection|array
     */
    private function getQuoteCompanies(): Collection|array
    {
        return EloquentQuoteCompany::query()
            ->select(
                EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::QUOTE_ID,
                EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::COST,
                EloquentAddress::TABLE.'.'.EloquentAddress::STATE_ABBR,
                LeadCampaign::TABLE.'.'.LeadCampaign::MAX_DAILY_LEAD,
                LeadCampaign::TABLE.'.'.LeadCampaign::MAX_DAILY_SPEND,
                LeadType::TABLE.'.'.LeadType::ID . ' as lead_type',
                LeadCampaignSalesTypeConfiguration::TABLE.'.'.LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID
            )
            ->join(
                EloquentQuote::TABLE,
                EloquentQuote::TABLE.'.'.EloquentQuote::QUOTE_ID,
                '=',
                EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::QUOTE_ID
            )
            ->join(
                LeadType::TABLE,
                LeadType::TABLE.'.'.LeadType::ID,
                '=',
                EloquentQuote::TABLE.'.'.EloquentQuote::LEAD_TYPE_ID
            )
            ->join(
                EloquentAddress::TABLE,
                EloquentAddress::TABLE.'.'.EloquentAddress::ADDRESS_ID,
                '=',
                EloquentQuote::TABLE.'.'.EloquentQuote::ADDRESS_ID
            )
            ->join(
                LeadCampaignSalesTypeConfiguration::TABLE,
                LeadCampaignSalesTypeConfiguration::TABLE.'.'.LeadCampaignSalesTypeConfiguration::ID,
                '=',
                EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION_ID
            )
            ->join(
                LeadCampaign::TABLE,
                LeadCampaign::TABLE.'.'.LeadCampaign::ID,
                '=',
                LeadCampaignSalesTypeConfiguration::TABLE.'.'.LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID
            )
            ->join(
                DatabaseHelperService::database().'.'.ProductAssignment::TABLE,
                DatabaseHelperService::database().'.'.ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_LEGACY_ID,
                '=',
                EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::QUOTE_COMPANY_ID
            )
            ->join(
                DatabaseHelperService::database().'.'.ConsumerProduct::TABLE,
                DatabaseHelperService::database().'.'.ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ID,
                '=',
                DatabaseHelperService::database().'.'.ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CONSUMER_PRODUCT_ID
            )
            ->where(EloquentQuote::TABLE.'.'.EloquentQuote::TIMESTAMP_ADDED, '>=', $this->start->timestamp)
            ->where(EloquentQuote::TABLE.'.'.EloquentQuote::TIMESTAMP_ADDED, '<=', $this->end->timestamp)
            ->where(EloquentQuote::TABLE.'.'.EloquentQuote::SOLAR_LEAD, true)
            ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::CHARGEABLE, true)
            ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::DELIVERED, true)
            ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::CHARGE_STATUS, '!=', EloquentQuoteCompany::VALUE_CHARGE_STATUS_REJECTED)
            ->where(DatabaseHelperService::database().'.'.ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_SERVICE_PRODUCT_ID, 1)
            ->get();
    }

    private function outputRevenueWithProposedPrices()
    {
        $this->info('Revenue with proposed prices from '. $this->start->toDateString() . ' to ' . $this->end->toDateString());
        $this->info("Ignore budgets?: $this->ignoreBudget \n");

        $quoteCompanies = $this->simulateCosts();

        $output =
            "State|".
            "Total Current Revenue|".
            "Total Leads Sold|".
            "Total Additional Revenue|".
            "Total Percent Increase|".
            "Total Bid Over Current Floor|".
            "Total Bid Over New Floor|".
            "Exclusive Current Revenue|".
            "Exclusive Leads Sold|".
            "Exclusive Additional Revenue|".
            "Exclusive Percent Increase|".
            "Exclusive Bid Over Current Floor|".
            "Exclusive Bid Over New Floor|".
            "Duo Current Revenue|".
            "Duo Leads Sold|".
            "Duo Additional Revenue|".
            "Duo Percent Increase|".
            "Duo Bid Over Current Floor|".
            "Duo Bid Over New Floor|".
            "Trio Current Revenue|".
            "Trio Leads Sold|".
            "Trio Additional Revenue|".
            "Trio Percent Increase|".
            "Trio Bid Over Current Floor|".
            "Trio Bid Over New Floor|".
            "Quad Current Revenue|".
            "Quad Leads Sold|".
            "Quad Additional Revenue|".
            "Quad Percent Increase|".
            "Quad Bid Over Current Floor|".
            "Quad Bid Over New Floor|".
            "Unverified Current Revenue|".
            "Unverified Leads Sold|".
            "Unverified Additional Revenue|".
            "Unverified Percent Increase|".
            "Unverified Bid Over Current Floor|".
            "Unverified Bid Over New Floor"
        ;

        $this->info($output);

        foreach($quoteCompanies->groupBy('state') as $quoteCompaniesByState) {
            $state = $quoteCompaniesByState->first()['state'];
            $output = $state ."|".
                $this->getDataForState($quoteCompaniesByState)."|".
                $this->getDataForState($quoteCompaniesByState, 1)."|".
                $this->getDataForState($quoteCompaniesByState, 2)."|".
                $this->getDataForState($quoteCompaniesByState, 3)."|".
                $this->getDataForState($quoteCompaniesByState, 4)."|".
                $this->getDataForState($quoteCompaniesByState, 5);

            $this->info($output);
        }
    }

    /**
     * @param Collection $quoteCompanies
     * @param int|null $salesType
     * @return string
     */
    private function getDataForState(Collection $quoteCompanies, int $salesType = null): string
    {
        if($salesType) {
            $quoteCompanies = $quoteCompanies->where(LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID, $salesType);
        }

        $currentRevenue    = $quoteCompanies->sum('current_cost');
        $newRevenue        = $quoteCompanies->sum('new_cost');
        $additionalRevenue = $newRevenue - $currentRevenue;

        $percentIncrease = empty($currentRevenue) ? 0 : (($additionalRevenue / $currentRevenue) * 100);

        return "$".number_format($currentRevenue)."|".
            $quoteCompanies->unique('quoteid')->count()."|".
            "$".number_format($additionalRevenue)."|".
            round($percentIncrease)."%|".
            "$".number_format($quoteCompanies->sum('bid_over_current_floor'))."|".
            "$".number_format($quoteCompanies->sum('bid_over_new_floor'));
    }
}
