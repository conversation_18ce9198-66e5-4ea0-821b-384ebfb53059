<?php

namespace App\Console\Commands\Utilities;

use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\EloquentUtility;
use App\Repositories\Odin\Engines\LegacySolarEngineRepository;
use Illuminate\Console\Command;

class SimulateSolarEstimates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'simulate:solar-estimates';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Simulate solar estimates for recent leads to an installer';

    /**
     * Execute the console command.
     *
     */
    public function handle(LegacySolarEngineRepository $solarEngineRepository)
    {
        $legacyCompanyId = intval($this->ask('What is the Legacy Company ID?'));
        $limit = intval($this->ask('How many leads should be analyzed?'));

        $quotes = $this->getRecentLeadsByLegacyCompanyId($legacyCompanyId, $limit);

        $this->info(
            "name|entered zip|assumed zip|entered bill|system size|cost per watt|low estimated cost|high estimated cost|assumed bill|estimated bill|estimated annual cost|estimated usage|utility rate|entered utility|assumed utility|quoteid|charge status|timestamp delivered"
        );

        $quotes->map(function($quote) use ($solarEngineRepository) {
            $result = $solarEngineRepository->getResult(
                $quote->{EloquentAddress::ZIP_CODE},
                $quote->{EloquentUtility::FIELD_UUID},
                intval($quote->{EloquentQuote::ELECTRIC_COST})
            );

            $costPerWatt = $solarEngineRepository->getSystemCost($quote->{EloquentAddress::ZIP_CODE});

            $estimate = json_decode($result['result'], true)['analysisData'];

            $assumptions = $estimate['assumptions'];

            $systemSize  = $assumptions['systemSizeInKw'];
            $utilityRate = $assumptions['beforeBaseUtilityRate'];
            $usage       = $estimate['result']['before']['firstYear']['kWh'];
            $cost        = $estimate['result']['before']['firstYear']['cost'];
            $monthlyBill = $result['monthly_bill'];
            $utilityUuid = $result['utility_uuid'];
            $zipCode     = $result['zip_code'];

            $this->info(
                $quote->{EloquentQuote::FIRST_NAME} . ' ' . $quote->{EloquentQuote::LAST_NAME}."|".
                $quote->{EloquentAddress::ZIP_CODE}."|".
                $zipCode."|".
                $quote->{EloquentQuote::ELECTRIC_COST}."|".
                $systemSize."|".
                $costPerWatt."|".
                round($systemSize * 1000 * $costPerWatt * .85)."|".
                round($systemSize * 1000 * $costPerWatt * 1.4)."|".
                $monthlyBill."|".
                round($cost/12)."|".
                $cost."|".
                $usage."|".
                $utilityRate."|".
                $quote->{EloquentUtility::FIELD_UUID}."|".
                $utilityUuid."|".
                $quote->{EloquentQuote::QUOTE_ID}."|".
                $quote->{EloquentQuoteCompany::CHARGE_STATUS}."|".
                $quote->{EloquentQuoteCompany::TIMESTAMP_DELIVERED}
            );
        });
    }


    public function getRecentLeadsByLegacyCompanyId(int $legacyCompanyId, int $limit = 100)
    {
        return EloquentQuoteCompany::query()
            ->select([
                EloquentQuote::TABLE.'.'.EloquentQuote::QUOTE_ID,
                EloquentQuote::TABLE.'.'.EloquentQuote::FIRST_NAME,
                EloquentQuote::TABLE.'.'.EloquentQuote::LAST_NAME,
                EloquentQuote::TABLE.'.'.EloquentQuote::ELECTRIC_COST,
                EloquentAddress::TABLE.'.'.EloquentAddress::ZIP_CODE,
                EloquentUtility::TABLE.'.'.EloquentUtility::FIELD_UUID,
                EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::TIMESTAMP_DELIVERED,
                EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::CHARGE_STATUS
            ])
            ->join(
                EloquentQuote::TABLE,
                EloquentQuote::TABLE.'.'.EloquentQuote::QUOTE_ID,
                '=',
                EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::QUOTE_ID
            )
            ->join(
                EloquentAddress::TABLE,
                EloquentAddress::TABLE.'.'.EloquentAddress::ADDRESS_ID,
                '=',
                EloquentQuote::TABLE.'.'.EloquentQuote::ADDRESS_ID
            )
            ->join(
                EloquentUtility::TABLE,
                EloquentUtility::TABLE.'.'.EloquentUtility::FIELD_UTILITY_ID,
                '=',
                EloquentQuote::TABLE.'.'.EloquentQuote::UTILITY_ID
            )
            ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::COMPANY_ID, $legacyCompanyId)
            ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::CHARGEABLE, true)
            ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::DELIVERED, true)
            ->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::DELIVERED, true)
            ->where(EloquentQuote::TABLE.'.'.EloquentQuote::SOLAR_LEAD, true)
            ->orderByDesc(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::QUOTE_ID)
            ->take($limit)
            ->get();
    }
}
