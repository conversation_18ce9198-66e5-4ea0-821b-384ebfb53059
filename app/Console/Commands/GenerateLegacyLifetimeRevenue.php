<?php

namespace App\Console\Commands;

use App\Jobs\CalculateLifetimeRevenue;
use App\Models\Legacy\EloquentInvoice;
use App\Models\Odin\Company;
use App\Services\DatabaseHelperService;
use App\Services\QueueHelperService;
use Illuminate\Console\Command;

class GenerateLegacyLifetimeRevenue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:generate_company_legacy_lifetime_revenue {companyIds? : Comma-separated list of IDs}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $ids = $this->argument('companyIds') ?? [];
        if (!empty($ids)) {
            // Parse comma-separated string to an array of integers
            $ids = collect(explode(',', $ids))
                ->map(fn($id) => (int) trim($id))
                ->filter()
                ->unique()
                ->values()
                ->all();

        } else {
            $ids = EloquentInvoice::query()
                ->join(
                    DatabaseHelperService::database() .'.'. Company::TABLE,
                    EloquentInvoice::TABLE . '.' . EloquentInvoice::COMPANY_ID,
                    '=',
                    Company::TABLE .'.' . Company::FIELD_LEGACY_ID,
                )
                ->where(EloquentInvoice::TABLE .'.'. EloquentInvoice::STATUS, EloquentInvoice::VALUE_STATUS_PAID)
                ->distinct()
                ->pluck(Company::TABLE . '.' . Company::FIELD_ID)
                ->toArray();
        }

        $this->info('Using IDs: ' . implode(', ', $ids));
        $this->info(count($ids) . ' jobs to be dispatched');

        foreach ($ids as $id) {
            CalculateLifetimeRevenue::dispatch($id)->onQueue(QueueHelperService::QUEUE_NAME_LONG_RUNNING);
        }
    }
}
