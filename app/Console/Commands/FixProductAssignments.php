<?php

namespace App\Console\Commands;

use App\Contracts\Services\AddressIdentificationServiceInterface;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\Odin\SolarConfigurableFields;
use App\Jobs\LegacyMigrations\MigrateLegacyQuoteCompaniesJob;
use App\Models\Call;
use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\EloquentVarStore;
use App\Models\Legacy\LeadTrackingUrl;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyData;
use App\Models\Odin\CompanyExternalReview;
use App\Models\Odin\CompanyIndustry;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\CompanyService;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductAffiliateRecord;
use App\Models\Odin\ConsumerProductData;
use App\Models\Odin\ConsumerProductTcpaRecord;
use App\Models\Odin\ConsumerProductTracking;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCancellation;
use App\Models\Odin\ProductRejection;
use App\Models\Odin\ServiceProduct;
use App\Models\Odin\Website;
use App\Models\SaleType;
use App\Models\Text;
use App\Services\AddressIdentification\AddressIdentificationServiceFactory;
use App\Services\DatabaseHelperService;
use Carbon\Carbon;
use Carbon\CarbonTimeZone;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Throwable;
use Ramsey\Uuid\Uuid;

class FixProductAssignments extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:migrate-missing-product-assignments';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handles the migration of legacy quote companies to their new schema.';

    /**
     * @var AddressIdentificationServiceInterface
     */
    private AddressIdentificationServiceInterface $addressIdentificationService;

    /**
     *
     */
    public function __construct()
    {
        $this->addressIdentificationService = AddressIdentificationServiceFactory::make();
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     * @throws Throwable
     */
    public function handle()
    {
        ini_set('memory_limit','-1');

        $this->deleteDuplicateRecords();

        $this->migrateMissingRecords();

        $this->call('populate:rejection-product-id-fields');

        $this->info('Finished.');
        return 0;
    }

    /**
     * @return void
     */
    private function migrateMissingRecords(): void
    {
        $this->migrateMissingAddresses();
        $this->migrateMissingConsumers();
        $this->migrateMissingConsumerProducts();
        $this->migrateMissingQuoteCompanies();
    }

    /**
     * @return void
     */
    private function migrateMissingAddresses(): void
    {
        $this->info("\nMigrating missing Addresses");

        $missingIds = $this->getMissingLegacyAddressIds();

        $this->migrateAddresses($missingIds);
    }

    /**
     * @return void
     */
    private function migrateMissingConsumers(): void
    {
        $this->info("\nMigrating missing Consumers");

        $quoteIds = $this->getMissingQuotesIds();

        $this->migrateConsumers($quoteIds);
    }

    /**
     * @param array $quoteIds
     * @return void
     */
    private function migrateConsumers(array $quoteIds): void
    {
        $websites = $this->getWebsites();
        $query    = $this->getMissingQuotesQuery($quoteIds);
        $bar      = $this->output->createProgressBar($query->count());

        $bar->start();

        $query->chunk(500, function ($quotes) use ($bar, $websites) {
            $data = [];

            foreach($quotes as $quote) {
                $data[] = $this->getConsumerMigrationArray($quote, $websites);
            }

            Consumer::query()->insert($data);

            $bar->advance($quotes->count());
        });

        $bar->finish();
    }

    /**
     * @param array $legacyAddressIds
     * @return void
     */
    private function migrateAddresses(array $legacyAddressIds): void
    {
        $query = $this->getMissingAddressesQuery($legacyAddressIds);
        $bar   = $this->output->createProgressBar($query->count());

        $bar->start();

        $query->chunk(500, function ($addresses) use ($bar) {
            $data = [];

            foreach($addresses as $address) {
                $data[] = $this->getAddressMigrationArray($address);
            }

            Address::query()->insert($data);

            $bar->advance($addresses->count());
        });

        $bar->finish();
    }

    /**
     * @param EloquentAddress $address
     * @return array
     */
    private function getAddressMigrationArray(EloquentAddress $address): array
    {
        $placeId = $this->addressIdentificationService->getIdFromAddressComponents(
            $address->{EloquentAddress::ADDRESS1},
            $address->{EloquentAddress::CITY},
            $address->{EloquentAddress::STATE_ABBR},
            $address->{EloquentAddress::ZIP_CODE},
            $address->{EloquentAddress::ADDRESS2},
        );

        return [
            Address::FIELD_LEGACY_ID => $address->{EloquentAddress::ID},
            Address::FIELD_ADDRESS_1 => $address->{EloquentAddress::ADDRESS1},
            Address::FIELD_ADDRESS_2 => $address->{EloquentAddress::ADDRESS2},
            Address::FIELD_CITY      => $address->{EloquentAddress::CITY},
            Address::FIELD_STATE     => $address->{EloquentAddress::STATE_ABBR},
            Address::FIELD_ZIP_CODE  => $address->{EloquentAddress::ZIP_CODE},
            Address::FIELD_COUNTRY   => $address->{EloquentAddress::COUNTRY},
            Address::FIELD_LATITUDE  => $address->{EloquentAddress::LATITUDE},
            Address::FIELD_LONGITUDE => $address->{EloquentAddress::LONGITUDE},
            Address::FIELD_PLACE_ID  => $placeId
        ];
    }

    /**
     * @param array $quoteIds
     * @return Builder
     */
    private function getMissingQuotesQuery(array $quoteIds): Builder
    {
        return EloquentQuote::query()
                ->select([
                    EloquentQuote::TABLE . '.*',
                    EloquentAddress::TABLE.'.'.EloquentAddress::PHONE . ' as phone',
                ])
                ->whereIntegerInRaw(EloquentQuote::QUOTE_ID, $quoteIds)
                ->join(
                    DatabaseHelperService::database().'.'.Address::TABLE,
                    DatabaseHelperService::database().'.'.Address::TABLE.'.'.Address::FIELD_LEGACY_ID,
                    '=',
                    EloquentQuote::ADDRESS_ID
                )
                ->leftJoin(
                    EloquentAddress::TABLE,
                    EloquentAddress::TABLE.'.'.EloquentAddress::ADDRESS_ID,
                    '=',
                    EloquentQuote::TABLE.'.'.EloquentQuote::ADDRESS_ID
                );
    }

    /**
     * @param array $legacyAddressIds
     * @return Builder
     */
    private function getMissingAddressesQuery(array $legacyAddressIds): Builder
    {
        return EloquentAddress::query()
            ->whereIntegerInRaw(EloquentAddress::ID, $legacyAddressIds);
    }

    /**
     * @param EloquentQuote $quote
     * @param array $websites
     * @return array
     */
    private function getConsumerMigrationArray(EloquentQuote $quote, array $websites): array
    {
        return [
            Consumer::FIELD_REFERENCE            => Uuid::uuid4(),
            Consumer::FIELD_WEBSITE_ID           => $this->determineWebsite($quote, $websites),
            Consumer::FIELD_EMAIL                => $quote->{EloquentQuote::USER_EMAIL},
            Consumer::FIELD_PHONE                => $quote->{'phone'},
            Consumer::FIELD_FIRST_NAME           => $quote->{EloquentQuote::FIRST_NAME},
            Consumer::FIELD_LAST_NAME            => $quote->{EloquentQuote::LAST_NAME},
            Consumer::FIELD_STATUS               => $this->determineConsumerStatus($quote),
            Consumer::FIELD_CLASSIFICATION       => $this->determineClassification($quote),
            Consumer::FIELD_STATUS_REASON        => '', //TODO
            Consumer::FIELD_MAX_CONTACT_REQUESTS => $quote->{EloquentQuote::NUMBER_OF_QUOTES},
            Consumer::CREATED_AT                 => Carbon::createFromTimestamp($quote->{EloquentQuote::TIMESTAMP_ADDED})->format("Y-m-d H:i:s"),
            Consumer::FIELD_LEGACY_ID            => $quote->{EloquentQuote::ID}
        ];
    }

    /**
     * @return array
     */
    private function getWebsites(): array
    {
        return Website::all()->pluck(Website::FIELD_ID, Website::FIELD_URL)->toArray();
    }

    /**
     * @return void
     */
    private function migrateMissingConsumerProducts(): void
    {
        $this->info("\nMigrating missing Consumer Products");

        $consumerIds = $this->getConsumerIdsWithMissingConsumerProducts();

        $this->migrateConsumerProducts($consumerIds);
    }

    /**
     * @return array
     */
    private function getConsumerIdsWithMissingConsumerProducts(): array
    {
        return Consumer::query()
            ->select(Consumer::TABLE.'.'.Consumer::FIELD_ID)
            ->leftJoin(
                ConsumerProduct::TABLE,
                ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CONSUMER_ID,
                '=',
                Consumer::TABLE.'.'.Consumer::FIELD_ID
            )
            ->whereNull(ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ID)
            ->pluck(Consumer::TABLE.'.'.Consumer::FIELD_ID)
            ->toArray();
    }

    /**
     * @return float|int|string
     */
    private function getMayFirstTwoThousandTwentyTwo(): float|int|string
    {
        return Carbon::createFromDate(2022, 5, 1, new CarbonTimeZone('UTC'))->timestamp;
    }

    /**
 * @return float|int|string
 */
    private function getNinetyDaysAgo(): float|int|string
    {
        return Carbon::now(new CarbonTimeZone('UTC'))->subDays(90)->timestamp;
    }

    /**
     * @return array
     */
    private function getServiceProductsKeyed(): array
    {
        $serviceProducts = ServiceProduct::query()
            ->with([ServiceProduct::RELATION_SERVICE])
            ->where(ServiceProduct::FIELD_PRODUCT_ID, Product::query()->where(Product::FIELD_NAME, \App\Enums\Odin\Product::LEAD->value)->first()->{Product::FIELD_ID})
            ->get();

        $serviceProductsKeyed = [];
        foreach($serviceProducts as $serviceProduct) {
            if($serviceProduct->{ServiceProduct::RELATION_SERVICE}) {
                $serviceProductsKeyed[$serviceProduct->{ServiceProduct::RELATION_SERVICE}->{IndustryService::FIELD_SLUG}] = $serviceProduct->{ServiceProduct::FIELD_ID};
            }
        }
        return $serviceProductsKeyed;
    }

    /**
     * @return void
     */
    private function migrateMissingQuoteCompanies(): void
    {
        $this->info("\nMigrating missing Product Assignments");

        $saleTypes = SaleType::all()->pluck(SaleType::FIELD_ID, SaleType::FIELD_NAME)->toArray();

        $missingIds = $this->getMissingQuoteCompanyIds();

        $missingQuoteCompaniesQuery = EloquentQuoteCompany::query()
            ->select(EloquentQuoteCompany::ID)
            ->whereIntegerInRaw(EloquentQuoteCompany::ID, $missingIds);

        $bar = $this->output->createProgressBar($missingQuoteCompaniesQuery->count());
        $bar->start();

        $missingQuoteCompaniesQuery
            ->chunk(1000, function($quoteCompanyIds) use ($saleTypes, $bar) {
                MigrateLegacyQuoteCompaniesJob::dispatchSync($quoteCompanyIds->pluck(EloquentQuoteCompany::ID), $saleTypes, false);

                $bar->advance($quoteCompanyIds->count());
            });
    }

    /**
     * @return array
     */
    private function getMissingQuotesIds(): array
    {
        return EloquentQuote::query()
            ->select(EloquentQuote::TABLE.'.'.EloquentQuote::ID)
            ->leftJoin(
                DatabaseHelperService::database().'.'.Consumer::TABLE,
                DatabaseHelperService::database().'.'.Consumer::TABLE.'.'.Consumer::FIELD_LEGACY_ID,
                '=',
                EloquentQuote::TABLE.'.'.EloquentQuote::ID
            )
            ->whereNull(DatabaseHelperService::database().'.'.Consumer::TABLE.'.'.Consumer::FIELD_ID)
            ->pluck(EloquentQuote::TABLE.'.'.EloquentQuote::ID)
            ->toArray();
    }

    /**
     * @return array
     */
    private function getMissingLegacyAddressIds(): array
    {
        return EloquentAddress::query()
            ->select(EloquentAddress::TABLE.'.'.EloquentAddress::ID)
            ->leftJoin(
                DatabaseHelperService::database().'.'.Address::TABLE,
                DatabaseHelperService::database().'.'.Address::TABLE.'.'.Address::FIELD_LEGACY_ID,
                '=',
                EloquentAddress::TABLE.'.'.EloquentAddress::ID
            )
            ->whereNull(DatabaseHelperService::database().'.'.Address::TABLE.'.'.Address::FIELD_ID)
            ->pluck(EloquentAddress::TABLE.'.'.EloquentAddress::ID)
            ->toArray();
    }

    /**
     * @return array
     */
    private function getMissingQuoteCompanyIds(): array
    {
        return EloquentQuoteCompany::query()
            ->select(EloquentQuoteCompany::ID)
            ->leftJoin(
                DatabaseHelperService::database().'.'.ProductAssignment::TABLE,
                DatabaseHelperService::database().'.'.ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_LEGACY_ID,
                '=',
                EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::ID
            )
            ->whereNull(DatabaseHelperService::database().'.'.ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_ID)
            ->pluck(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::ID)
            ->toArray();
    }

    /**
     * @return void
     */
    private function deleteDuplicateRecords(): void
    {
        $this->deleteDuplicateCompanyRecords();
        $this->deleteDuplicateConsumerRecords();

        $legacyIdsQuery = $this->getDuplicateProductAssignmentLegacyIdsQuery();
        $count = $this->getDuplicateProductAssignmentCount($legacyIdsQuery);

        while($count > 0) {
            $this->deleteDuplicateProductAssignmentRecords($count, $legacyIdsQuery);

            $legacyIdsQuery = $this->getDuplicateProductAssignmentLegacyIdsQuery();
            $count = $this->getDuplicateProductAssignmentCount($legacyIdsQuery);
        }
    }

    /**
     * @param Collection $duplicateAddresses
     * @return array
     */
    private function getAddressIdsToDelete(Collection $duplicateAddresses): array
    {
        $duplicateLegacyIds = $duplicateAddresses->pluck(Address::FIELD_LEGACY_ID)->toArray();
        $addressIdsToKeep   = $duplicateAddresses->pluck(Address::FIELD_ID)->toArray();

        return Address::query()
            ->select(Address::FIELD_ID)
            ->whereIntegerInRaw(Address::TABLE.'.'.Address::FIELD_LEGACY_ID, $duplicateLegacyIds)
            ->whereIntegerNotInRaw(Address::TABLE.'.'.Address::FIELD_ID, $addressIdsToKeep)
            ->pluck(Address::FIELD_ID)
            ->toArray();
    }

    /**
     * @param Collection $duplicateCompanies
     * @return \Illuminate\Database\Eloquent\Collection|array
     */
    private function getCompaniesToDelete(Collection $duplicateCompanies): \Illuminate\Database\Eloquent\Collection|array
    {
        $duplicateLegacyIds = $duplicateCompanies->pluck(Company::FIELD_LEGACY_ID)->toArray();
        $companyIdsToKeep   = $duplicateCompanies->pluck(Company::FIELD_ID)->toArray();

        return Company::withTrashed()
            ->select([
                Company::TABLE.'.'.Company::FIELD_ID . ' as company_id',
                CompanyLocation::TABLE.'.'.CompanyLocation::FIELD_ID . ' as company_location_id',
                CompanyLocation::TABLE.'.'.CompanyLocation::FIELD_ADDRESS_ID . ' as address_id',
            ])
            ->whereIntegerInRaw(Company::TABLE.'.'.Company::FIELD_LEGACY_ID, $duplicateLegacyIds)
            ->whereIntegerNotInRaw(Company::TABLE.'.'.Company::FIELD_ID, $companyIdsToKeep)
            ->leftJoin(
                CompanyLocation::TABLE,
                CompanyLocation::TABLE.'.'.CompanyLocation::FIELD_COMPANY_ID,
                '=',
                Company::TABLE.'.'.Company::FIELD_ID
            )->leftJoin(
                CompanyUser::TABLE,
                CompanyUser::TABLE.'.'.CompanyUser::FIELD_COMPANY_ID,
                '=',
                Company::TABLE.'.'.Company::FIELD_ID
            )->get();
    }

    /**
     * @param Collection $duplicateConsumers
     * @return \Illuminate\Database\Eloquent\Collection|array
     */
    private function getConsumersToDelete(Collection $duplicateConsumers): \Illuminate\Database\Eloquent\Collection|array
    {
        $duplicateLegacyIds = $duplicateConsumers->pluck(Consumer::FIELD_LEGACY_ID)->toArray();
        $consumerIdsToKeep  = $duplicateConsumers->pluck(Consumer::FIELD_ID)->toArray();

        return Consumer::query()
            ->select([
                Consumer::TABLE.'.'.Consumer::FIELD_ID . ' as consumer_id',
                ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CONSUMER_ID .' as consumer_product_id',
                ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CONSUMER_PRODUCT_DATA_ID,
                ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID,
                ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CONSUMER_PRODUCT_TCPA_RECORD_ID,
                ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CONSUMER_PRODUCT_AFFILIATE_RECORD_ID,
                ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ADDRESS_ID,
            ])
            ->whereIntegerInRaw(Consumer::TABLE.'.'.Consumer::FIELD_LEGACY_ID, $duplicateLegacyIds)
            ->whereIntegerNotInRaw(Consumer::TABLE.'.'.Consumer::FIELD_ID, $consumerIdsToKeep)
            ->leftJoin(
                ConsumerProduct::TABLE,
                ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CONSUMER_ID,
                '=',
                Consumer::TABLE.'.'.Consumer::FIELD_ID
            )
            ->get();
    }

    /**
     * @return void
     */
    private function deleteDuplicateConsumerRecords(): void
    {
        $duplicateConsumers = Consumer::query()
            ->select(Consumer::FIELD_LEGACY_ID, Consumer::FIELD_ID)
            ->orderBy(Consumer::FIELD_ID)
            ->groupBy(Consumer::FIELD_LEGACY_ID)
            ->havingRaw('COUNT('.Consumer::FIELD_LEGACY_ID.') > 1')
            ->get();

        $consumersToDelete = $this->getConsumersToDelete($duplicateConsumers);

        $this->updateCalls($consumersToDelete, $duplicateConsumers);
        $this->updateTexts($consumersToDelete, $duplicateConsumers);

        $this->deleteConsumers($consumersToDelete);
    }

    /**
     * @return void
     */
    private function deleteDuplicateCompanyRecords(): void
    {
        $duplicateCompanies = Company::withTrashed()
            ->select(Company::FIELD_LEGACY_ID, Company::FIELD_ID)
            ->orderBy(Company::FIELD_ID)
            ->groupBy(Company::FIELD_LEGACY_ID)
            ->havingRaw('COUNT('.Company::FIELD_LEGACY_ID.') > 1')
            ->get();

        $companiesToDelete = $this->getCompaniesToDelete($duplicateCompanies);

        $this->deleteCompanies($companiesToDelete);
    }

    /**
     * @param Collection $companiesToDelete
     * @return void
     */
    private function deleteCompanies(Collection $companiesToDelete): void
    {
        $this->info('Deleting Companies and related data');
            DB::beginTransaction();

            $companyIds = $companiesToDelete
                ->whereNotNull('company_id')
                ->unique('company_id')
                ->pluck('company_id')
                ->toArray();

            $companyLocationIds = $companiesToDelete
                ->whereNotNull('company_location_id')
                ->unique('company_location_id')
                ->pluck('company_location_id')
                ->toArray();

            $addressIds = $companiesToDelete
                ->whereNotNull('address_id')
                ->unique('address_id')
                ->pluck('address_id')
                ->toArray();

            Address::query()
                ->whereIntegerInRaw(Address::FIELD_ID, $addressIds)
                ->forceDelete();

            CompanyExternalReview::query()
                ->whereIntegerInRaw(CompanyExternalReview::FIELD_COMPANY_LOCATION_ID, $companyLocationIds)
                ->forceDelete();

            CompanyLocation::query()
                ->whereIntegerInRaw(CompanyLocation::FIELD_ID, $companyLocationIds)
                ->forceDelete();

            CompanyService::query()
                ->whereIntegerInRaw(CompanyService::FIELD_COMPANY_ID, $companyIds)
                ->forceDelete();

            CompanyUser::withTrashed()
                ->whereIntegerInRaw(CompanyUser::FIELD_COMPANY_ID, $companyIds)
                ->forceDelete();

            CompanyIndustry::query()
                ->whereIntegerInRaw(CompanyIndustry::FIELD_COMPANY_ID, $companyIds)
                ->forceDelete();

            CompanyData::query()
                ->whereIntegerInRaw(CompanyData::FIELD_COMPANY_ID, $companyIds)
                ->forceDelete();

            Company::withTrashed()
                ->whereIntegerInRaw(Company::FIELD_ID, $companyIds)
                ->forceDelete();

            DB::commit();
    }

    /**
     * @param Collection $consumersToDelete
     * @return void
     */
    private function deleteConsumers(Collection $consumersToDelete): void
    {
        $this->info('Deleting Consumers and related data');

        $bar = $this->output->createProgressBar($consumersToDelete->count());
        $bar->start();

        $chunks = $consumersToDelete->chunk(1000);

        foreach($chunks as $consumers) {
            DB::beginTransaction();

            $consumerIds = $consumers
                ->unique('consumer_id')
                ->pluck('consumer_id')
                ->toArray();

            $consumerProductIds = $consumers
                ->whereNotNull('consumer_product_id')
                ->unique('consumer_product_id')
                ->pluck('consumer_product_id')
                ->toArray();

            $addressIds = $consumers
                ->whereNotNull(ConsumerProduct::FIELD_ADDRESS_ID)
                ->unique(ConsumerProduct::FIELD_ADDRESS_ID)
                ->pluck(ConsumerProduct::FIELD_ADDRESS_ID)
                ->toArray();

            $consumerProductDataIds = $consumers
                ->whereNotNull(ConsumerProduct::FIELD_CONSUMER_PRODUCT_DATA_ID)
                ->unique(ConsumerProduct::FIELD_CONSUMER_PRODUCT_DATA_ID)
                ->pluck(ConsumerProduct::FIELD_CONSUMER_PRODUCT_DATA_ID)
                ->toArray();

            $consumerProductTrackingIds = $consumers
                ->whereNotNull(ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID)
                ->unique(ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID)
                ->pluck(ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID)
                ->toArray();

            $consumerProductTcpaRecordIds = $consumers
                ->whereNotNull(ConsumerProduct::FIELD_CONSUMER_PRODUCT_TCPA_RECORD_ID)
                ->unique(ConsumerProduct::FIELD_CONSUMER_PRODUCT_TCPA_RECORD_ID)
                ->pluck(ConsumerProduct::FIELD_CONSUMER_PRODUCT_TCPA_RECORD_ID)
                ->toArray();

            $consumerProductAffiliateRecordIds = $consumers
                ->whereNotNull(ConsumerProduct::FIELD_CONSUMER_PRODUCT_AFFILIATE_RECORD_ID)
                ->unique(ConsumerProduct::FIELD_CONSUMER_PRODUCT_AFFILIATE_RECORD_ID)
                ->pluck(ConsumerProduct::FIELD_CONSUMER_PRODUCT_AFFILIATE_RECORD_ID)
                ->toArray();

            Address::query()
                ->whereIntegerInRaw(Address::FIELD_ID, $addressIds)
                ->forceDelete();

            ConsumerProductData::query()
                ->whereIntegerInRaw(ConsumerProductData::FIELD_ID, $consumerProductDataIds)
                ->forceDelete();

            ConsumerProductTracking::query()
                ->whereIntegerInRaw(ConsumerProductTracking::ID, $consumerProductTrackingIds)
                ->forceDelete();

            ConsumerProductTcpaRecord::query()
                ->whereIntegerInRaw(ConsumerProductTcpaRecord::FIELD_ID, $consumerProductTcpaRecordIds)
                ->forceDelete();

            ConsumerProductAffiliateRecord::query()
                ->whereIntegerInRaw(ConsumerProductAffiliateRecord::FIELD_ID, $consumerProductAffiliateRecordIds)
                ->forceDelete();

            ConsumerProduct::query()
                ->whereIntegerInRaw(ConsumerProduct::FIELD_ID, $consumerProductIds)
                ->forceDelete();

            Consumer::query()
                ->whereIntegerInRaw(Consumer::FIELD_ID, $consumerIds)
                ->forceDelete();

            DB::commit();

            $bar->advance($consumers->count());
        }

        $bar->finish();
    }

    /**
     * @param Collection $consumersToDelete
     * @param Collection $duplicateConsumers
     * @return void
     */
    private function updateCalls(Collection $consumersToDelete, Collection $duplicateConsumers): void
    {
        $this->info("\nUpdating Call records");

        $callsToUpdate = Call::query()
            ->select([
                Call::TABLE.'.'.Call::FIELD_ID,
                Call::TABLE.'.'.Call::FIELD_RELATION_ID,
                Consumer::TABLE.'.'.Consumer::FIELD_LEGACY_ID
            ])
            ->whereIntegerInRaw(Call::FIELD_RELATION_ID,$consumersToDelete->pluck('consumer_id')->toArray())
            ->where(Call::FIELD_RELATION_TYPE, Call::RELATION_LEAD)
            ->join(
                Consumer::TABLE,
                Consumer::TABLE.'.'.Consumer::FIELD_ID,
                '=',
                Call::TABLE.'.'.Call::FIELD_RELATION_ID
            )
            ->get();

        $callData = [];

        foreach($callsToUpdate as $call) {
            $callData[] = [
                Call::FIELD_ID => $call->{Call::FIELD_ID},
                Call::FIELD_RELATION_ID => $duplicateConsumers->where(Consumer::FIELD_LEGACY_ID, $call->{Consumer::FIELD_LEGACY_ID})->first()->{Consumer::FIELD_ID},
            ];
        }

        Call::query()->upsert($callData, [Call::FIELD_ID]);
    }

    /**
     * @param Collection $consumersToDelete
     * @param Collection $duplicateConsumers
     * @return void
     */
    private function updateTexts(Collection $consumersToDelete, Collection $duplicateConsumers): void
    {
        $this->info("Updating Text records");

        $textsToUpdate = Text::query()
            ->select([
                Text::TABLE.'.'.Text::FIELD_ID,
                Text::TABLE.'.'.Text::FIELD_RELATION_ID,
                Consumer::TABLE.'.'.Consumer::FIELD_LEGACY_ID
            ])
            ->whereIntegerInRaw(Text::FIELD_RELATION_ID, $consumersToDelete->pluck('consumer_id')->toArray())
            ->where(Text::FIELD_RELATION_TYPE, Text::RELATION_LEAD)
            ->join(
                Consumer::TABLE,
                Consumer::TABLE.'.'.Consumer::FIELD_ID,
                '=',
                Text::TABLE.'.'.Text::FIELD_RELATION_ID
            )
            ->get();

        $textData = [];

        foreach($textsToUpdate as $text) {
            $textData[] = [
                Text::FIELD_ID => $text->{Text::FIELD_ID},
                Text::FIELD_RELATION_ID => $duplicateConsumers->where(Consumer::FIELD_LEGACY_ID, $text->{Consumer::FIELD_LEGACY_ID})->first()->{Consumer::FIELD_ID},
            ];
        }

        Text::query()->upsert($textData, [Call::FIELD_ID]);
    }

    /**
     * @param int $count
     * @param Builder $legacyIdsQuery
     * @return void
     */
    private function deleteDuplicateProductAssignmentRecords(int $count, Builder $legacyIdsQuery): void
    {
        $this->info("\nDeleting Product Assignments and related data");

        $bar = $this->output->createProgressBar($count);
        $bar->start();

        $legacyIdsQuery->chunk(1000, function($ids) use ($bar) {
            $ids = $ids->pluck(ProductAssignment::FIELD_LEGACY_ID)->toArray();

            ProductAssignment::query()
                ->select(ProductAssignment::FIELD_ID)
                ->whereIn(ProductAssignment::FIELD_LEGACY_ID, $ids)->chunk(1000, function ($productAssignments) use ($bar) {
                    DB::beginTransaction();

                    $productAssignmentIds = $productAssignments->pluck(ProductAssignment::FIELD_ID)->toArray();

                    ProductRejection::query()
                        ->whereIntegerInRaw(ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID, $productAssignmentIds)
                        ->delete();

                    ProductCancellation::query()
                        ->whereIntegerInRaw(ProductCancellation::FIELD_PRODUCT_ASSIGNMENT_ID, $productAssignmentIds)
                        ->delete();

                    ProductAssignment::query()
                        ->whereIntegerInRaw(ProductAssignment::FIELD_ID, $productAssignmentIds)
                        ->delete();

                    DB::commit();

                    $bar->advance($productAssignments->count());
                });
        });

        $bar->finish();
    }

    /**
     * @param Builder $legacyIdsQuery
     * @return int
     */
    private function getDuplicateProductAssignmentCount(Builder $legacyIdsQuery): int
    {
        return ProductAssignment::query()
            ->select(ProductAssignment::FIELD_ID)
            ->whereIn(ProductAssignment::FIELD_LEGACY_ID, $legacyIdsQuery)
            ->count();
    }

    /**
     * @return Builder
     */
    private function getDuplicateProductAssignmentLegacyIdsQuery(): Builder
    {
        return ProductAssignment::query()
            ->select(ProductAssignment::FIELD_LEGACY_ID)
            ->groupBy(ProductAssignment::FIELD_LEGACY_ID)
            ->havingRaw('COUNT('.ProductAssignment::FIELD_LEGACY_ID.') > 1');
    }

    /**
     * @param array $consumerIds
     * @return void
     */
    private function migrateConsumerProducts(array $consumerIds): void
    {
        $quotes = EloquentQuote::query()
            ->select([
                EloquentQuote::TABLE.'.*',
                DatabaseHelperService::database().'.'.Consumer::TABLE.'.'.Consumer::FIELD_ID . ' as consumer_id',
                DatabaseHelperService::database().'.'.Address::TABLE.'.'.Address::FIELD_ID . ' as odin_address_id',
            ])
            ->with(EloquentQuote::RELATION_TRACKING_URL)
            ->whereIntegerInRaw(DatabaseHelperService::database().'.'.Consumer::TABLE.'.'.Consumer::FIELD_ID, $consumerIds)
            ->join(
                DatabaseHelperService::database().'.'.Consumer::TABLE,
                DatabaseHelperService::database().'.'.Consumer::TABLE.'.'.Consumer::FIELD_LEGACY_ID,
                '=',
                EloquentQuote::TABLE.'.'.EloquentQuote::ID
            )->join(
                DatabaseHelperService::database().'.'.Address::TABLE,
                DatabaseHelperService::database().'.'.Address::TABLE.'.'.Address::FIELD_LEGACY_ID,
                '=',
                EloquentQuote::TABLE.'.'.EloquentQuote::ADDRESS_ID
            )
            ->get();

        $bar = $this->output->createProgressBar($quotes->count());
        $bar->start();

        $data = [];

        $serviceProducts              = $this->getServiceProductsKeyed();
        $mayFirstTwoThousandTwentyTwo = $this->getMayFirstTwoThousandTwentyTwo();
        $ninetyDaysAgo                = $this->getNinetyDaysAgo();

        foreach ($quotes as $quote) {
            if ($quote->{'odin_address_id'}) {
                $consumerProductDataId     = $this->migrateConsumerProductData($quote, $serviceProducts);
                $consumerProductTrackingId = $this->migrateConsumerProductTracking($quote);

                $data[] = $this->getConsumerProductMigrationArray(
                    $quote,
                    $consumerProductDataId,
                    $consumerProductTrackingId,
                    $serviceProducts,
                    $mayFirstTwoThousandTwentyTwo,
                    $ninetyDaysAgo
                );
            }
            $bar->advance();
        }

        foreach(array_chunk($data, 500) as $dataChunk) {
            ConsumerProduct::insert($dataChunk);
        }


        $bar->finish();
    }

    /**
     * @param EloquentQuote $quote
     * @param int $consumerProductDataId
     * @param int|null $consumerProductTrackingId
     * @param array $serviceProducts
     * @param $mayFirstTwoThousandTwentyTwo
     * @param $ninetyDaysAgo
     * @return array
     */
    private function getConsumerProductMigrationArray(EloquentQuote $quote, int $consumerProductDataId, ?int $consumerProductTrackingId, array $serviceProducts, $mayFirstTwoThousandTwentyTwo, $ninetyDaysAgo): array
    {
        return [
            ConsumerProduct::FIELD_CONSUMER_ID                  => $quote->{'consumer_id'},
            ConsumerProduct::FIELD_SERVICE_PRODUCT_ID           => $this->getServiceProductId($quote, $serviceProducts),
            ConsumerProduct::FIELD_ADDRESS_ID                   => $quote->{'odin_address_id'},
            ConsumerProduct::FIELD_GOOD_TO_SELL                 => $this->determineConsumerGoodToSell($quote, $mayFirstTwoThousandTwentyTwo),
            ConsumerProduct::FIELD_STATUS                       => $this->determineConsumerProductStatus($quote, false, $ninetyDaysAgo),
            ConsumerProduct::FIELD_CONTACT_REQUESTS             => $quote->{EloquentQuote::NUMBER_OF_QUOTES},
            ConsumerProduct::FIELD_CONSUMER_PRODUCT_DATA_ID     => $consumerProductDataId,
            ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID => $consumerProductTrackingId,
            ConsumerProduct::CREATED_AT                         => Carbon::createFromTimestamp($quote->{EloquentQuote::TIMESTAMP_ADDED})->format("Y-m-d H:i:s"),
        ];
    }

    /**
     * @param EloquentQuote $quote
     * @param $serviceProducts
     * @return int
     */
    private function migrateConsumerProductData(EloquentQuote $quote, $serviceProducts): int
    {
        $serviceProductId = $this->getServiceProductId($quote, $serviceProducts);

        //Make Consumer Product Data

        if ($serviceProductId === $serviceProducts['roof-repair']) {
            $consumerProductDataPayload = [
                GlobalConfigurableFields::STOREYS->value                 => $quote->{EloquentQuote::STOREYS},
                GlobalConfigurableFields::ROOF_DIRECTION->value          => $quote->{EloquentQuote::ROOF_DIRECTION},
                GlobalConfigurableFields::ROOF_SHADING->value            => $quote->{EloquentQuote::ROOF_SHADING},
                GlobalConfigurableFields::ROOF_PITCH->value              => $quote->{EloquentQuote::ROOF_PITCH},
                GlobalConfigurableFields::ROOF_TYPE->value               => $quote->{EloquentQuote::ROOF_TYPE},
                GlobalConfigurableFields::ROOF_TYPE_OTHER->value         => $quote->{EloquentQuote::ROOF_TYPE_OTHER},
                GlobalConfigurableFields::ROOF_CONDITION->value          => $quote->{EloquentQuote::ROOF_CONDITION},
                GlobalConfigurableFields::BEST_TIME_TO_CALL->value       => $quote->{EloquentQuote::BEST_TIME_TO_CALL},
                GlobalConfigurableFields::BEST_TIME_TO_CALL_OTHER->value => $quote->{EloquentQuote::BEST_TIME_TO_CALL_OTHER},
                GlobalConfigurableFields::OWN_PROPERTY->value            => $quote->{EloquentQuote::OWN_PROPERTY},
                GlobalConfigurableFields::IP_ADDRESS->value              => $quote->{EloquentQuote::IP_ADDRESS}
            ];
        } else {
            $consumerProductDataPayload = [
                SolarConfigurableFields::SYSTEM_TYPE->value              => $quote->{EloquentQuote::SYSTEM_TYPE},
                SolarConfigurableFields::SYSTEM_SIZE->value              => $quote->{EloquentQuote::SYSTEM_SIZE},
                SolarConfigurableFields::SYSTEM_SIZE_OTHER->value        => $quote->{EloquentQuote::SYSTEM_SIZE_OTHER},
                GlobalConfigurableFields::STOREYS->value                 => $quote->{EloquentQuote::STOREYS},
                SolarConfigurableFields::ELECTRIC_COST->value            => $quote->{EloquentQuote::ELECTRIC_COST},
                GlobalConfigurableFields::ROOF_DIRECTION->value          => $quote->{EloquentQuote::ROOF_DIRECTION},
                GlobalConfigurableFields::ROOF_SHADING->value            => $quote->{EloquentQuote::ROOF_SHADING},
                GlobalConfigurableFields::ROOF_PITCH->value              => $quote->{EloquentQuote::ROOF_PITCH},
                GlobalConfigurableFields::ROOF_TYPE->value               => $quote->{EloquentQuote::ROOF_TYPE},
                GlobalConfigurableFields::ROOF_TYPE_OTHER->value         => $quote->{EloquentQuote::ROOF_TYPE_OTHER},
                GlobalConfigurableFields::ROOF_CONDITION->value          => $quote->{EloquentQuote::ROOF_CONDITION},
                SolarConfigurableFields::PANEL_TIER->value               => $quote->{EloquentQuote::PANEL_TIER},
                GlobalConfigurableFields::BEST_TIME_TO_CALL->value       => $quote->{EloquentQuote::BEST_TIME_TO_CALL},
                GlobalConfigurableFields::BEST_TIME_TO_CALL_OTHER->value => $quote->{EloquentQuote::BEST_TIME_TO_CALL_OTHER},
                SolarConfigurableFields::UTILITY_NAME->value             => $quote->{EloquentQuote::UTILITY},
                SolarConfigurableFields::UTILITY_ID->value               => $quote->{EloquentQuote::UTILITY_ID},
                GlobalConfigurableFields::OWN_PROPERTY->value            => $quote->{EloquentQuote::OWN_PROPERTY},
                GlobalConfigurableFields::IP_ADDRESS->value              => $quote->{EloquentQuote::IP_ADDRESS}
            ];
        }

        $newConsumerProductData = ConsumerProductData::query()->create([
            ConsumerProductData::FIELD_PAYLOAD => $consumerProductDataPayload
        ]);

        return $newConsumerProductData->{ConsumerProductData::FIELD_ID};
    }

    /**
     * @param EloquentQuote $quote
     * @return int|null
     */
    private function migrateConsumerProductTracking(EloquentQuote $quote): ?int
    {
        $consumerProductTrackingId = null;
        $consumerProductTrackingData = [];

        if($quote->{EloquentQuote::RELATION_TRACKING_URL}) {
            $trackingUrl = $quote->{EloquentQuote::RELATION_TRACKING_URL};

            $consumerProductTrackingData = [
                ConsumerProductTracking::LEGACY_ID         => $trackingUrl->{LeadTrackingUrl::ID},
                ConsumerProductTracking::URL_START         => $trackingUrl->{LeadTrackingUrl::URL_START} ?? '',
                ConsumerProductTracking::URL_CONVERT       => $trackingUrl->{LeadTrackingUrl::URL_CONVERT} ?? '',
                ConsumerProductTracking::CALCULATOR_SOURCE => $trackingUrl->{LeadTrackingUrl::SOURCE} ?? ''
            ];
        }

        if(!empty($quote->{EloquentQuote::TRACK_CODE})
            && !empty($quote->{EloquentQuote::TRACK_NAME})) {
            $consumerProductTrackingData = [
                ConsumerProductTracking::LEGACY_ID         => $consumerProductTrackingData[ConsumerProductTracking::LEGACY_ID] ?? null,
                ConsumerProductTracking::URL_START         => $consumerProductTrackingData[ConsumerProductTracking::URL_START] ?? '',
                ConsumerProductTracking::URL_CONVERT       => $consumerProductTrackingData[ConsumerProductTracking::URL_CONVERT] ?? '',
                ConsumerProductTracking::CALCULATOR_SOURCE => $consumerProductTrackingData[ConsumerProductTracking::CALCULATOR_SOURCE] ?? '',
                ConsumerProductTracking::AD_TRACK_TYPE     => $quote->{EloquentQuote::TRACK_NAME},
                ConsumerProductTracking::AD_TRACK_CODE     => $quote->{EloquentQuote::TRACK_CODE}
            ];
        }

        if(!empty($consumerProductTrackingData)) {
            $consumerProductTrackingId = ConsumerProductTracking::query()->insertGetId($consumerProductTrackingData);
        }

        return $consumerProductTrackingId;
    }

    /**
     * @param EloquentQuote $quote
     * @param array $serviceProducts
     * @return mixed
     */
    private function getServiceProductId(EloquentQuote $quote, array $serviceProducts): mixed
    {
        if ($quote->{EloquentQuote::ROOFING_LEAD}) {
            return $serviceProducts['roof-repair'];
        }

        return $serviceProducts['solar-installation'];
    }


    /**
     * A consumer is considered good to sell if:
     * 1) The lead came in on or before 5/1/2022 and is allocated
     * 2) The lead came in after 5/1/2022 and has classification verified or is allocated
     *
     * @param EloquentQuote $quote
     * @param $mayFirstTwoThousandTwentyTwo
     * @return bool
     */
    protected function determineConsumerGoodToSell(EloquentQuote $quote, $mayFirstTwoThousandTwentyTwo): bool
    {
        if($quote->{EloquentQuote::TIMESTAMP_ADDED} <= $mayFirstTwoThousandTwentyTwo) {
            return $quote->{EloquentQuote::STATUS} === EloquentQuote::VALUE_STATUS_ALLOCATED;
        }
        else {
            return $quote->{EloquentQuote::STATUS} === EloquentQuote::VALUE_STATUS_ALLOCATED || $quote->{EloquentQuote::CLASSIFICATION} === EloquentQuote::CLASSIFICATION_VERIFIED;
        }
    }

    /**
     * @param EloquentQuote $quote
     * @param bool $inInitialQueue
     * @param $ninetyDaysAgo
     * @return int
     */
    protected function determineConsumerProductStatus(EloquentQuote $quote, bool $inInitialQueue, $ninetyDaysAgo): int
    {
        if($quote->{EloquentQuote::STATUS} === EloquentQuote::VALUE_STATUS_ALLOCATED) {
            return ConsumerProduct::STATUS_ALLOCATED;
        }
        else if($quote->{EloquentQuote::STATUS} === EloquentQuote::VALUE_STATUS_CANCELLED) {
            return ConsumerProduct::STATUS_CANCELLED;
        }
        else if($quote->{EloquentQuote::TIMESTAMP_ADDED} <= $ninetyDaysAgo
            && !in_array($quote->{EloquentQuote::STATUS}, [EloquentQuote::VALUE_STATUS_ALLOCATED, EloquentQuote::VALUE_STATUS_SOLD, EloquentQuote::VALUE_STATUS_NO_COMPANIES, EloquentQuote::VALUE_STATUS_CANCELLED])) {
            return ConsumerProduct::STATUS_UNSOLD;
        }
        else if($quote->{EloquentQuote::STATUS} === EloquentQuote::VALUE_STATUS_INITIAL) {
            return $inInitialQueue ? ConsumerProduct::STATUS_INITIAL : ConsumerProduct::STATUS_PENDING_REVIEW;
        }
        else {
            return ConsumerProduct::STATUS_UNDER_REVIEW;
        }
    }

    /**
     * @param EloquentQuote $quote
     * @param array $websites
     * @return int
     */
    protected function determineWebsite(EloquentQuote $quote, array $websites): int
    {
        return match($quote->{EloquentQuote::ORIGIN}) {
            'sr' => $websites['https://www.solarreviews.com'],
            'se' => $websites['www.solar-estimate.org'],
            'rc' => $websites['www.roofingcalculator.com'],
            'spr' => $websites['www.solarpowerrocks.com'],
            'sn' => $websites['www.sunnumber.com'],
            'cmb' => $websites['www.cutmybill.com'],
            default => 0
        };
    }

    /**
     * @param EloquentQuote $quote
     * @return int
     */
    protected function determineClassification(EloquentQuote $quote): int
    {
        $hasPhone = !empty($quote->{'phone'});
        $phoneValidated = (bool) $quote
            ->varStores()
            ->getQuery()
            ->where(EloquentVarStore::CATEGORY, 'estimator_data')
            ->where(EloquentVarStore::NAME, 'phone_validated')
            ->first();

        if(!$hasPhone) {
            return Consumer::CLASSIFICATION_EMAIL_ONLY;
        }
        else if(!$phoneValidated) {
            return Consumer::CLASSIFICATION_UNVERIFIED_PHONE;
        }
        else {
            return Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_SMS;
        }
    }

    /**
     * @param EloquentQuote $quote
     * @return int
     */
    protected function determineConsumerStatus(EloquentQuote $quote): int
    {
        if($quote->{EloquentQuote::STATUS} === EloquentQuote::VALUE_STATUS_INITIAL) {
            return Consumer::STATUS_INITIAL;
        }
        else if($quote->{EloquentQuote::STATUS} === EloquentQuote::VALUE_STATUS_ALLOCATED
            || $quote->{EloquentQuote::STATUS} === EloquentQuote::VALUE_STATUS_SOLD) {
            return Consumer::STATUS_COMPLETED;
        }
        else if($quote->{EloquentQuote::STATUS} === EloquentQuote::VALUE_STATUS_CANCELLED) {
            return Consumer::STATUS_CANCELLED;
        }
        else {
            return Consumer::STATUS_IN_PROGRESS;
        }
    }
}
