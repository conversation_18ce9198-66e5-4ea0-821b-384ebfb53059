<?php

namespace App\Console\Commands;

use App\DTO\Billing\MakeChangeRequestInvoice\PaymentMethodAttemptDTO;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Enums\Billing\InvoicePaymentStatus;
use App\Jobs\Billing\ScheduleChargeRequestInvoiceJob;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\CompanyPaymentMethod;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoicePayment;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class FixScheduleChargeAttempt extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'billing:fix-schedule-charge-attempt {--invoice-payment-uuid=} {--date=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command schedules a charge attempt';

    /**
     * @var InvoicePayment|null
     */
    protected ?InvoicePayment $invoicePayment;

    /**
     * @throws Exception
     */
    protected function validate(
        ?string $invoicePaymentUuid,
        ?string $date,
    ): void
    {
        if (empty($invoicePaymentUuid) || empty($date)) {
            throw new Exception('Payment uuid or date cannot be empty');
        }

        $this->invoicePayment = InvoicePayment::findByUuid($invoicePaymentUuid);

        if (!$this->invoicePayment) {
            throw new Exception('Invoice payment not found');
        }
    }

    /**
     * @return void
     * @throws Exception
     */
    public function handle(): void
    {
        $invoicePaymentUuid = $this->option('invoice-payment-uuid');
        $nextAttemptAt = $this->option('date');

        $this->validate(
            invoicePaymentUuid: $invoicePaymentUuid,
            date              : $nextAttemptAt
        );

        $nextAttemptAt = Carbon::parse($nextAttemptAt);

        $maxAttempts = $this->invoicePayment->{InvoicePayment::FIELD_MAX_ATTEMPTS_PER_PAYMENT_METHOD};
        $numberOfAttempts = $this->invoicePayment->{InvoicePayment::FIELD_ATTEMPT_NUMBER};
        $attemptsLeft = $maxAttempts - $numberOfAttempts;

        if (empty($attemptsLeft)) {
            throw new Exception('No attempts left');
        }

        $this->updateInvoicePayment(
            nextAttemptAt: $nextAttemptAt
        );

        $this->scheduleChargeAttempt(
            date            : $nextAttemptAt,
            paymentUuid     : $invoicePaymentUuid,
            invoiceUuid     : $this->invoicePayment->invoice->{Invoice::FIELD_UUID},
            total           : $this->invoicePayment->{InvoicePayment::FIELD_TOTAL},
            maxAttempts     : $maxAttempts,
            billingProfileId: $this->invoicePayment->invoice->{Invoice::FIELD_BILLING_PROFILE_ID},
            attemptsLeft    : $attemptsLeft,
        );
    }

    /**
     * @param Carbon $nextAttemptAt
     * @return void
     */
    protected function updateInvoicePayment(Carbon $nextAttemptAt): void
    {
        $this->invoicePayment->update([
            InvoicePayment::FIELD_STATUS          => InvoicePaymentStatus::RESCHEDULED->value,
            InvoicePayment::FIELD_NEXT_ATTEMPT_AT => $nextAttemptAt
        ]);
    }

    /**
     * @param int $billingProfileId
     * @param int $attemptsLeft
     * @return Collection
     */
    protected function mountPaymentMethodAttempts(int $billingProfileId, int $attemptsLeft): Collection
    {
        $billingProfile = BillingProfile::query()
            ->where('id', $billingProfileId)
            ->firstOrFail();

        $paymentMethodsAttempts = CompanyPaymentMethod::query()
            ->where(CompanyPaymentMethod::FIELD_COMPANY_ID, $billingProfile->{BillingProfile::FIELD_COMPANY_ID})
            ->where(CompanyPaymentMethod::FIELD_TYPE, $billingProfile->{BillingProfile::FIELD_PAYMENT_METHOD})
            ->orderByDesc(CompanyPaymentMethod::FIELD_IS_DEFAULT)
            ->get();

        return $paymentMethodsAttempts->map(fn(CompanyPaymentMethod $paymentMethod) => new PaymentMethodAttemptDTO(
            attempts              : $attemptsLeft,
            companyPaymentMethodId: $paymentMethod->{CompanyPaymentMethod::FIELD_ID},
            companyPaymentMethod  : $paymentMethod
        ));
    }

    /**
     * @param Carbon $date
     * @param string $paymentUuid
     * @param string $invoiceUuid
     * @param int $total
     * @param string $authorType
     * @param int $billingProfileId
     * @param int $attemptsLeft
     * @param int|null $authorId
     * @return void
     */
    public function scheduleChargeAttempt(
        Carbon $date,
        string $paymentUuid,
        string $invoiceUuid,
        int $total,
        int $maxAttempts,
        int $billingProfileId,
        int $attemptsLeft,
    ): void
    {
        $rescheduledDateInSeconds = now()->diffInSeconds($date);

        $paymentMethodsAttempts = $this->mountPaymentMethodAttempts(
            billingProfileId: $billingProfileId,
            attemptsLeft    : $attemptsLeft
        );

        ScheduleChargeRequestInvoiceJob::dispatch(
            $paymentUuid,
            $invoiceUuid,
            $total,
            InvoiceEventAuthorTypes::SYSTEM->value,
            $billingProfileId,
            $maxAttempts,
            null,
            $paymentMethodsAttempts,
        )->delay($rescheduledDateInSeconds);
    }
}
