<?php

namespace App\Console\Commands\Mailbox;

use App\Jobs\Mailbox\RemoveUserEmailsListenerJob;
use App\Jobs\Mailbox\SetupUserEmailsListenerJob;
use App\Models\User;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class ManageEmailListener extends Command
{
    const string OPTION_USER_ID = 'user_id';
    const string OPTION_ACTION  = 'action';

    const string ACTION_CREATE  = 'create';
    const string ACTION_REMOVE  = 'remove';
    const string ACTION_REFRESH = 'refresh';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mailbox:manage-email-listener {--user_id=} {--action=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage email listener';

    /**
     * Execute the console command.
     *
     * @return void
     * @throws ValidationException
     * @throws Exception
     */
    public function handle(): void
    {
        [
            self::OPTION_USER_ID => $userId,
            self::OPTION_ACTION  => $action,
        ] = $this->validateInput();

        $this->handleAction(
            userId: $userId,
            action: $action,
        );

        $this->info('Command successfully executed.');
    }

    /**
     * @param int $userId
     * @param string $action
     * @return void
     * @throws Exception
     */
    public function handleAction(
        int $userId,
        string $action,
    ): void
    {
        $user = User::query()->findOrFail($userId);

        if (!$user) {
            throw new Exception('Token for user not found');
        }

        $this->info("Executing the action '$action' for user $user->name...");

        match ($action) {
            self::ACTION_CREATE  => SetupUserEmailsListenerJob::dispatchSync($user),
            self::ACTION_REMOVE  => RemoveUserEmailsListenerJob::dispatchSync($user),
            self::ACTION_REFRESH => $this->handleRefresh($userId),
            default              => throw new Exception("Action $action not supported")
        };

        $this->info("Success executing '$action' for user $user->name...");
    }

    /**
     * @throws Exception
     */
    protected function handleRefresh(int $userId): void
    {
        $this->handleAction(
            userId: $userId,
            action: self::ACTION_REMOVE
        );

        $this->handleAction(
            userId: $userId,
            action: self::ACTION_CREATE
        );
    }

    /**
     * @return array
     * @throws ValidationException
     */
    public function validateInput(): array
    {
        $validator = Validator::make($this->options(), [
            self::OPTION_ACTION  => ['required', 'in:' . implode(',', [self::ACTION_CREATE, self::ACTION_REMOVE, self::ACTION_REFRESH])],
            self::OPTION_USER_ID => ['required', 'exists:users,id'],
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $validator->validated();
    }
}

