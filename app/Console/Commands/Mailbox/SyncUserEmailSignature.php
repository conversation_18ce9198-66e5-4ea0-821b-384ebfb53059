<?php

namespace App\Console\Commands\Mailbox;

use App\Models\Mailbox\MailboxUserToken;
use App\Models\User;
use App\Services\Mailbox\SyncMailboxService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class SyncUserEmailSignature extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'mailbox:sync-signature {--user_id= : Comma separated user ids}';

    /**
     * The console command description.
     */
    protected $description = 'Sync email signatures for specified users or all users with mailbox tokens';

    private SyncMailboxService $syncMailboxService;
    private int                $processedCount = 0;
    private int                $errorCount     = 0;

    /**
     * Execute the console command.
     */
    public function handle(SyncMailboxService $syncMailboxService): int
    {
        $this->syncMailboxService = $syncMailboxService;

        $this->info('Starting email signature sync process...');

        try {
            $users = $this->getTargetUsers();

            if ($users->isEmpty()) {
                $this->warn('No users found to process.');
                return self::SUCCESS;
            }

            $this->info("Processing {$users->count()} users...");

            $this->processUsers($users);
            $this->displayResults();

            return self::SUCCESS;

        } catch (Exception $e) {
            $this->error("Fatal error occurred: " . $e->getMessage());
            return self::FAILURE;
        }
    }

    /**
     * @return Collection<User>
     */
    private function getTargetUsers(): Collection
    {
        $userIdInput = $this->option('user_id');

        if ($userIdInput) {
            return $this->getUsersByIds($userIdInput);
        }

        return $this->promptForAllUsers();
    }

    /**
     * @param string $userIdInput
     * @return Collection
     */
    private function getUsersByIds(string $userIdInput): Collection
    {
        $userIds = $this->parseUserIds($userIdInput);

        if ($userIds->isEmpty()) {
            $this->warn('No valid user IDs provided.');
            return collect();
        }

        return User::query()
            ->whereIn(User::FIELD_ID, $userIds)
            ->get();
    }

    /**
     * @param string $input
     * @return Collection
     */
    private function parseUserIds(string $input): Collection
    {
        return Str::of($input)
            ->split('/,/')
            ->map(fn($id) => trim($id))
            ->filter()
            ->map(fn($id) => is_numeric($id) ? (int)$id : null)
            ->filter()
            ->unique()
            ->values();
    }

    /**
     * @return Collection
     */
    private function promptForAllUsers(): Collection
    {
        $runForAll = $this->confirm('No specific users provided. Do you want to sync signatures for all users with mailbox tokens?');

        if (!$runForAll) {
            $this->info('Operation cancelled by user.');
            return collect();
        }

        $userIds = MailboxUserToken::query()
            ->pluck(MailboxUserToken::FIELD_USER_ID)
            ->unique();

        return User::query()
            ->whereIn(User::FIELD_ID, $userIds)
            ->get();
    }

    /**
     * @param Collection $users
     * @return void
     * @throws Exception
     */
    private function processUsers(Collection $users): void
    {
        $progressBar = $this->output->createProgressBar($users->count());
        $progressBar->setFormat('Processing: %current%/%max% [%bar%] %percent:3s%% - %message%');
        $progressBar->start();

        DB::beginTransaction();

        try {
            foreach ($users as $user) {
                $this->processUser($user, $progressBar);
                $progressBar->advance();
            }

            DB::commit();
            $progressBar->finish();
            $this->newLine();

        } catch (Exception $e) {
            DB::rollBack();
            $progressBar->finish();
            $this->newLine();
            throw $e;
        }
    }

    /**
     * @param User $user
     * @param $progressBar
     * @return void
     */
    private function processUser(User $user, $progressBar): void
    {
        try {
            $progressBar->setMessage("Processing user: {$user->email}");
            $this->syncMailboxService->syncEmailSignature($user);
            $this->processedCount++;
        } catch (Exception $e) {
            $this->handleException(
                user: $user,
                e   : $e
            );
        }
    }

    /**
     * @param User $user
     * @param Exception $e
     * @return void
     */
    private function handleException(User $user, Exception $e): void
    {
        $this->errorCount++;
        $errorMessage = "Failed to sync signature for user {$user->id} ({$user->email}): {$e->getMessage()}";
        $this->newLine();
        $this->error($errorMessage);
    }

    /**
     * @return void
     */
    private function displayResults(): void
    {
        $this->newLine();
        $this->info('=== Email Signature Sync Results ===');
        $this->info("Successfully processed: {$this->processedCount} users");

        if ($this->errorCount > 0) {
            $this->warn("Failed to process: {$this->errorCount} users");
        }

        $this->info('Sync process completed!');
    }
}
