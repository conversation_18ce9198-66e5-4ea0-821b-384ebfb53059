<?php

namespace App\Console\Commands\Mailbox;

use App\DTO\Mail\ListEmailQueryDTO;
use App\Jobs\Mailbox\SyncUserMailboxJob;
use App\Models\User;
use Illuminate\Console\Command;

class SyncEmails extends Command
{
    const string FIELD_USER_ID     = 'user_id';
    const string FIELD_EXTERNAL_ID = 'external_id';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mailbox:sync-emails  {--user_id=} {--external_id= : Omit to sync all emails} {--query.raw_query= : Used to filter emails} {--query.after_date= : Used to filter emails} {--query.before_date= : Used to filter emails}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import user emails';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $userId = $this->option(self::FIELD_USER_ID);
        $externalId = $this->option(self::FIELD_EXTERNAL_ID);

        if (empty($userId)) {
            $this->error('User id is required.');
            return;
        }

        $query = $this->parseListEmailsQuery();

        /** @var User $user */
        $user = User::query()->findOrFail($userId);

        SyncUserMailboxJob::dispatch($user, $externalId, null, $query->toArray());
        $this->info('Job dispatched...');
    }

    /**
     * @return ListEmailQueryDTO
     */
    private function parseListEmailsQuery(): ListEmailQueryDTO
    {
        $options = collect($this->options())
            ->filter(fn($value, string $key) => str_starts_with($key, 'query.'))
            ->map(fn($value, string $key) => [
                str_replace('query.', '', $key) => $value
            ])
            ->reduce(fn($carry, $item) => array_merge($carry, $item), []);

        return ListEmailQueryDTO::fromArray($options);
    }
}

