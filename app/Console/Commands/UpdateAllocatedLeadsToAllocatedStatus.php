<?php

namespace App\Console\Commands;

use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentQuoteCompany;
use Illuminate\Console\Command;
use Illuminate\Database\Query\Builder;

class UpdateAllocatedLeadsToAllocatedStatus extends Command
{
    const DEFAULT_WEEKS = 1;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clean-up:update-allocated-leads-to-allocated-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Checks if leads with a status of initial, underreview, or nocompanies have been allocated, and updates status accordingly';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $weeks = intval($this->ask('How many weeks should we look back? (Default: '.self::DEFAULT_WEEKS.')') ?? self::DEFAULT_WEEKS);
        $companyExclusions = [];
        if($this->confirm('Should any companies be excluded?', true)){
            $companyExclusions = $this->ask('Please enter a comma-seperated list of legacy company IDs to exclude');
            $companyExclusions = explode(',', preg_replace("/[^0-9,]/", "", $companyExclusions));
            $this->info('Please confirm the list of company exclusions');
            $this->question('['.implode(', ',$companyExclusions).']');
            if(!$this->confirm('Are these correct?')){
                $this->info('Exiting...');
                return Command::FAILURE;
            }
        }

        $leadsQuery = EloquentQuote::query()
            ->whereRaw("FROM_UNIXTIME(tblquote.timestampadded) >= SUBDATE(NOW(), interval " . $weeks . " week)")
            ->whereIn(EloquentQuote::STATUS, [EloquentQuote::VALUE_STATUS_INITIAL, EloquentQuote::VALUE_STATUS_UNDER_REVIEW, EloquentQuote::VALUE_STATUS_NO_COMPANIES])
            ->whereExists(function (Builder $query) {
               $query->select('*')->from(EloquentQuoteCompany::TABLE)
                     ->whereRaw(EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::QUOTE_ID . ' = ' . EloquentQuote::TABLE . '.' . EloquentQuote::QUOTE_ID)
                     ->where(EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::DELIVERED, 1)
                     ->where(EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::CHARGEABLE, 1)
                     ->where(EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::SOLD_STATUS, EloquentQuoteCompany::VALUE_SOLD_STATUS_SOLD);
            });

        if(count($companyExclusions) > 0) {
            $leadsQuery->whereNotExists(function (Builder $query) use ($companyExclusions) {
                $query->select('*')->from(EloquentQuoteCompany::TABLE)
                      ->whereRaw(EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::QUOTE_ID . ' = ' . EloquentQuote::TABLE . '.' . EloquentQuote::QUOTE_ID)
                      ->whereIn(EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::COMPANY_ID, $companyExclusions);
            });
        }

        $leads = $leadsQuery->get()->count();
        if($this->confirm($leads." allocated leads found with an incorrect status, update statuses to 'allocated'?", true)){
            $leadsQuery->update([EloquentQuote::STATUS => EloquentQuote::VALUE_STATUS_ALLOCATED]);
            $this->info($leads.' updated');
        }

        $this->info("\n__COMPLETE__");
        return Command::SUCCESS;
    }
}
