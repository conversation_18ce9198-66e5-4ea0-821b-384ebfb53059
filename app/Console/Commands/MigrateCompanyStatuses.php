<?php

namespace App\Console\Commands;

use App\Enums\Company\CompanyAdminStatus;
use App\Enums\Company\CompanyCampaignStatus;
use App\Enums\Company\CompanySystemStatus;
use App\Enums\CompanyConsolidatedStatus;
use App\Models\Odin\Company;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class MigrateCompanyStatuses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate-company-statuses';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate existing company statuses to admin, system and campaign statuses';


    protected array $failedCompanies = [];

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $this->info('Starting....');
        $progressBar = $this->output->createProgressBar(Company::query()->count());
        $companiesProcessed = 0;
        $time = now();

        $progressBar->setFormat('%current%/%max% [%bar%] %percent:3s%% | %message% | %remaining%');

        Company::query()
            ->with([Company::RELATION_FUTURE_CAMPAIGNS])
            ->chunk(100, function (Collection $companies) use ($progressBar, &$companiesProcessed, $time) {
                $companies->each(fn(Company $company) => $this->calculateStatuses($company));

                $this->updateStatuses($companies);

                $companiesProcessed += $companies->count();
                $companiesPerSecond = round($companiesProcessed / max(1, $time->diffInSeconds(now())));

                $progressBar->setMessage("$companiesPerSecond Companies/sec");
                $progressBar->advance($companies->count());
            });

        $progressBar->finish();
        $this->newLine();
        $this->info('Finished');

        if (count($this->failedCompanies)) {
            $this->info('System status was set to suspended due to stripe error for companies: ' . collect($this->failedCompanies)->join(', '));
        }
    }

    protected function calculateStatuses(Company $company): void
    {
        switch ($company->consolidated_status) {
            case CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS:
                $company->campaign_status = CompanyCampaignStatus::CAMPAIGNS_ACTIVE;
                $company->system_status = CompanySystemStatus::ELIGIBLE;
                $company->admin_status = CompanyAdminStatus::ADMIN_APPROVED;
                $company->consolidated_status = CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS;
                break;
            case CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_MERGED:
            case CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_ARCHIVED:
                $company->campaign_status = $company->determineCampaignStatus();
                $company->system_status = $this->determineSystemStatus($company);
                $company->admin_status = CompanyAdminStatus::ARCHIVED;
                $company->consolidated_status = CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CANNOT_RECEIVE_LEADS;
                break;
            case CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAMPAIGNS_PAUSED:
                $company->campaign_status = CompanyCampaignStatus::CAMPAIGNS_PAUSED;
                $company->system_status = $this->determineSystemStatus($company);
                $company->admin_status = $this->determineAdminStatus($company);
                $company->consolidated_status = CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CANNOT_RECEIVE_LEADS;
                break;
            case CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAMPAIGNS_OFF:
                $company->campaign_status = CompanyCampaignStatus::CAMPAIGNS_OFF;
                $company->system_status = $this->determineSystemStatus($company);
                $company->admin_status = $this->determineAdminStatus($company);
                $company->consolidated_status = CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CANNOT_RECEIVE_LEADS;
                break;
            case CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAMPAIGNS_OFF_NEVER_PURCHASED:
                $company->campaign_status = CompanyCampaignStatus::CAMPAIGNS_OFF_NEVER_PURCHASED;
                $company->system_status = $this->determineSystemStatus($company);
                $company->admin_status = $this->determineAdminStatus($company);
                $company->consolidated_status = CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CANNOT_RECEIVE_LEADS;
                break;
            case CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_REGISTERING:
            case CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_PENDING_APPROVAL:
            case CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_ADMIN_LOCKED:
                $company->campaign_status = $company->determineCampaignStatus();
                $company->system_status = $this->determineSystemStatus($company);
                $company->admin_status = CompanyAdminStatus::ADMIN_LOCKED;
                $company->consolidated_status = CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CANNOT_RECEIVE_LEADS;
                break;
            case CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_PROFILE_ONLY:
                $company->campaign_status = $company->determineCampaignStatus();
                $company->system_status = $this->determineSystemStatus($company);
                $company->admin_status = CompanyAdminStatus::PROFILE_ONLY;
                $company->consolidated_status = CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CANNOT_RECEIVE_LEADS;
                break;
            case CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_SUSPENDED_ALL_CAMPAIGNS:
                $company->campaign_status = $company->determineCampaignStatus();
                $company->system_status = CompanySystemStatus::SUSPENDED_PAYMENT;
                $company->admin_status = $this->determineAdminStatus($company);
                $company->consolidated_status = CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CANNOT_RECEIVE_LEADS;
                break;
            case CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_COLLECTIONS:
                $company->campaign_status = $company->determineCampaignStatus();
                $company->system_status = $this->determineSystemStatus($company);
                $company->admin_status = CompanyAdminStatus::COLLECTIONS;
                $company->consolidated_status = CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CANNOT_RECEIVE_LEADS;
                break;
            case CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CANNOT_RECEIVE_LEADS:
                break;
            default:
                $company->campaign_status = $company->determineCampaignStatus();
                $company->system_status = $this->determineSystemStatus($company);
                $company->admin_status = $this->determineAdminStatus($company);
                $company->consolidated_status = CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CANNOT_RECEIVE_LEADS;
        }
    }

    /**
     * @param Collection $companies
     *
     * @return void
     */
    protected function updateStatuses(Collection $companies): void
    {
        DB::disableQueryLog();

        Company::query()->upsert(
            values: $companies->map(fn(Company $company) => [
                Company::FIELD_ID => $company->id,
                Company::FIELD_CAMPAIGN_STATUS => $company->campaign_status,
                Company::FIELD_SYSTEM_STATUS => $company->system_status,
                Company::FIELD_ADMIN_STATUS => $company->admin_status,
                Company::FIELD_CONSOLIDATED_STATUS => $company->consolidated_status
            ])->toArray(),
            uniqueBy: [Company::FIELD_ID],
            update: [Company::FIELD_CAMPAIGN_STATUS, Company::FIELD_SYSTEM_STATUS, Company::FIELD_ADMIN_STATUS, Company::FIELD_CONSOLIDATED_STATUS]
        );
    }

    /**
     * @param Company $company
     *
     * @return CompanyAdminStatus
     */
    protected function determineAdminStatus(Company $company): CompanyAdminStatus
    {
        if ($company->admin_locked) {
            return CompanyAdminStatus::ADMIN_LOCKED;
        }

        if ($company->admin_approved) {
            return CompanyAdminStatus::ADMIN_APPROVED;
        }

        return CompanyAdminStatus::ADMIN_LOCKED;
    }

    /**
     * @param Company $company
     *
     * @return CompanySystemStatus
     */
    protected function determineSystemStatus(Company $company): CompanySystemStatus
    {
        //handle stripe error
        try {
            return $company->determineSystemStatus();
        } catch (Exception $exception) {
            $this->failedCompanies[] = $company->id;
            return CompanySystemStatus::SUSPENDED_PAYMENT_METHOD;
        }
    }
}
