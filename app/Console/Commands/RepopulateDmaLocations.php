<?php

namespace App\Console\Commands;

use App\Models\Dma;
use App\Models\DmaLocation;
use App\Models\Legacy\EloquentZipCode;
use App\Models\Legacy\Location;
use App\Services\Advertising\Locations\MetaAdsLocationsService;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use SplFileObject;
use Throwable;

class RepopulateDmaLocations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'repopulate:dma-locations '.
        '{--check : Check for any county locations in our database not mapped to a DMA.} '.
        '{--meta : Get Meta DMA Keys from the meta ads API.}';

    /** @var int */
    private int $lineCount;
    private Collection $countyLocations;
    private array $metaDmaKeys;
    private Collection $dmas;

    const string FILE_PATH = 'database/seeders/data/dma-locations.csv';
    const string READ_ONLY_DB_CONNECTION = 'readonly';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run this command to repopulate the dma and dma_locations tables with up to date '.
        'Meta Ads DMA information. File should be at ' . self::FILE_PATH;

    /**
     * @param MetaAdsLocationsService $metaAdsLocationsService
     */
    public function __construct(protected MetaAdsLocationsService $metaAdsLocationsService)
    {
        parent::__construct();
    }

    /**
     * Process the command
     *
     * @return int
     * @throws Throwable
     */
    public function handle(): int
    {
        if ($this->option('check')) {
            $this->checkUnmappedCounties();
            return 0;
        }

        $filePath = base_path(self::FILE_PATH);
        $this->processFile($filePath);

        return 0;
    }

    /**
     * @param $handle
     * @return bool
     */
    private function validateFile($handle): bool
    {
        $this->info("Validating DMA file");
        fgetcsv($handle); // skip header

        $bar = $this->output->createProgressBar($this->lineCount);
        $bar->start();

        while($csvLine = fgetcsv($handle)) {
            $assocCSVLine = $this->mapCSVToTableFieldNames($csvLine);
            if(!$this->validateFileData($assocCSVLine)) {
                return false;
            }
            $bar->advance();
        }

        rewind($handle);
        $bar->finish();
        $this->newLine();
        $this->info('Validated file successfully');

        return true;
    }

    /**
     * This function uses the zip codes table, which contains county fips information, to create a collection of county
     * location objects containing their fips code.
     * @return Collection
     */
    private function getCountiesWithCountyFips(): Collection
    {
        $locationTable1 = 'l1';
        $locationTable2 = 'l2';
        $zipcodeTable   = 'z';
        return DB::connection(self::READ_ONLY_DB_CONNECTION)
            ->table(Location::TABLE.' AS '.$locationTable1)
            ->select(
                $locationTable2.'.'.Location::ID,
                $zipcodeTable.'.'.EloquentZipCode::FIELD_COUNTY_FIPS,
                $locationTable2.'.'.Location::COUNTY,
                $locationTable2.'.'.Location::STATE_ABBREVIATION,
                $locationTable2.'.'.Location::STATE,
            )
            ->leftJoin(
                DB::raw(
                    "(SELECT ".
                        EloquentZipCode::FIELD_ZIP_CODE.
                        ", MAX(".EloquentZipCode::FIELD_COUNTY_FIPS.") AS ".EloquentZipCode::FIELD_COUNTY_FIPS.
                        ", MAX(".EloquentZipCode::FIELD_STATE_FIPS.") AS ".EloquentZipCode::FIELD_STATE_FIPS.
                    " FROM ".EloquentZipCode::TABLE." GROUP BY ".EloquentZipCode::FIELD_ZIP_CODE.") AS ".$zipcodeTable),
                $zipcodeTable.'.zipcode',
                '=',
                $locationTable1.'.'.Location::ZIP_CODE,
            )
            ->join(Location::TABLE.' AS '.$locationTable2, function($join) use ($locationTable1, $locationTable2) {
                $join->on($locationTable1.'.'.Location::STATE, '=', $locationTable2.'.'.Location::STATE)
                     ->on($locationTable1.'.'.Location::COUNTY, '=', $locationTable2.'.'.Location::COUNTY);
            })
            ->where($locationTable2.'.'.Location::TYPE, '=', Location::TYPE_COUNTY)
            ->groupBy(EloquentZipCode::FIELD_COUNTY_FIPS)
            ->get();
    }

    /**
     * @return void
     */
    private function getCountyLocations(): void
    {
        $this->info("Retrieving legacy county locations");
        // This query creates an array of county fips => location id, mapping county fips codes to the id of the county entry in the legacy locations table
        $this->countyLocations = $this->getCountiesWithCountyFips()->pluck(Location::ID, EloquentZipCode::FIELD_COUNTY_FIPS);
    }

    /**
     * @return void
     * @throws Throwable
     */
    private function getMetaDmaKeys(): void
    {
        $this->info("Retrieving Meta DMA keys");
        $this->metaDmaKeys = $this->metaAdsLocationsService->getMetaDmaKeys();
    }

    /**
     * @param $handle
     * @return array
     */
    private function buildDmaData($handle): array
    {
        $this->info("Building DMA data");
        fgetcsv($handle); // skip header

        $insertArray = [];

        $bar = $this->output->createProgressBar($this->lineCount);
        $bar->start();

        while ($csvLine = fgetcsv($handle)) {
            $assocCSVLine = $this->mapCSVToTableFieldNames($csvLine);
            $this->makeDmaFromCsvRow($insertArray, $assocCSVLine);
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        rewind($handle);
        return $insertArray;
    }

    /**
     * @param array $insertArray
     * @return void
     */
    private function storeDmaData(array $insertArray): void
    {
        // Clear DMA tables
        Dma::truncate();

        $this->info("Storing data");

        $bar = $this->output->createProgressBar($this->lineCount / 100);
        $bar->start();

        DB::transaction(function() use ($insertArray, $bar) {
            foreach(array_chunk($insertArray, 100) as $chunkInsert) {
                Dma::insert($chunkInsert);

                $bar->advance();
            }
        });

        $this->dmas = Dma::all();

        $bar->finish();
        $this->newLine();
    }

    /**
     * @param $handle
     * @return array
     */
    private function buildDmaLocationData($handle): array
    {
        $this->info("Building DMA Location data");
        fgetcsv($handle); // skip header

        $insertArray = [];

        $bar = $this->output->createProgressBar($this->lineCount);
        $bar->start();

        while ($csvLine = fgetcsv($handle)) {
            $assocCSVLine = $this->mapCSVToTableFieldNames($csvLine);
            $this->makeDmaLocationFromCsvRow($insertArray, $assocCSVLine);
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        return $insertArray;
    }

    /**
     * @param array $insertArray
     * @return void
     */
    private function storeDmaLocationData(array $insertArray): void
    {
        // Clear DMA tables
        DmaLocation::truncate();

        $this->info("Storing DMA Location data");

        $bar = $this->output->createProgressBar($this->lineCount / 100);
        $bar->start();

        DB::transaction(function() use ($insertArray, $bar) {
            foreach(array_chunk($insertArray, 100) as $chunkInsert) {
                DmaLocation::insert($chunkInsert);

                $bar->advance();
            }
        });

        $bar->finish();
        $this->newLine();
    }

    /**
     * @param $filePath
     * @return int
     */
    private function getCsvLineCount($filePath): int
    {
        $file = new SplFileObject($filePath, 'r');
        $file->seek(PHP_INT_MAX);
        return $file->key() + 1;
    }

    /**
     * @param string $filePath
     * @return void
     * @throws Throwable
     */
    public function processFile(string $filePath): void
    {
        $this->lineCount = $this->getCsvLineCount($filePath);

        $handle = fopen($filePath, "r");

        if($this->validateFile($handle)) {
            $this->getCountyLocations();

            if ($this->option('meta'))
                $this->getMetaDmaKeys();

            $dmaData = $this->buildDmaData($handle);
            $this->storeDmaData($dmaData);

            $dmaLocationData = $this->buildDmaLocationData($handle);
            $this->storeDmaLocationData($dmaLocationData);
        }

    }

    /**
     * @param array $insertArray
     * @param array $assocCSVLine
     * @return void
     */
    private function makeDmaFromCsvRow(array &$insertArray, array $assocCSVLine): void
    {
        // Only add unique DMAs
        $dmaCodes = array_column($insertArray, Dma::FIELD_DMA_CODE);
        if (in_array($assocCSVLine[Dma::FIELD_DMA_CODE], $dmaCodes))
            return;

        $insertArray[] = [
            Dma::FIELD_DMA_CODE     => $assocCSVLine[Dma::FIELD_DMA_CODE],
            Dma::FIELD_DMA_NAME     => $assocCSVLine[Dma::FIELD_DMA_NAME],
            Dma::FIELD_META_DMA_KEY => $this->metaDmaKeys[$assocCSVLine[Dma::FIELD_DMA_CODE]] ?? null,
        ];
    }

    /**
     * @param array $insertArray
     * @param array $assocCSVLine
     * @return void
     */
    private function makeDmaLocationFromCsvRow(array &$insertArray, array $assocCSVLine): void
    {
        $dmaId = $this->dmas->where(Dma::FIELD_DMA_CODE, $assocCSVLine[Dma::FIELD_DMA_CODE])->first()->{Dma::FIELD_ID};
        $locationId = $this->countyLocations[$assocCSVLine[DmaLocation::FIELD_COUNTY_FIPS]] ?? null;

        if ($locationId === null) {
            $this->newLine();
            $this->error("No county fips found for " . $assocCSVLine[DmaLocation::FIELD_COUNTY_NAME] .
                " (" . $assocCSVLine[DmaLocation::FIELD_STATE_KEY] . ") Expecting: " . $assocCSVLine[DmaLocation::FIELD_COUNTY_FIPS]);
        }
        $insertArray[] = [
            DmaLocation::FIELD_DMA_ID       => $dmaId,
            DmaLocation::FIELD_LOCATION_ID  => $locationId,
            DmaLocation::FIELD_STATE_KEY    => $assocCSVLine[DmaLocation::FIELD_STATE_KEY],
            DmaLocation::FIELD_STATE_FIPS   => $assocCSVLine[DmaLocation::FIELD_STATE_FIPS],
            DmaLocation::FIELD_COUNTY_NAME  => $assocCSVLine[DmaLocation::FIELD_COUNTY_NAME],
            DmaLocation::FIELD_COUNTY_FIPS  => $assocCSVLine[DmaLocation::FIELD_COUNTY_FIPS],
        ];
    }

    /**
     * @param array $assocCVSLine
     * @return bool|null
     */
    private function validateFileData(array $assocCVSLine): ?bool
    {
        $validator = Validator::make($assocCVSLine, [
            Dma::FIELD_DMA_CODE             => 'required|string|max:3',
            Dma::FIELD_DMA_NAME             => 'required|string',
            DmaLocation::FIELD_STATE_KEY    => 'required|string|size:2',
            DmaLocation::FIELD_STATE_FIPS   => 'required|string|max:2',
            DmaLocation::FIELD_COUNTY_NAME  => 'required|string',
            DmaLocation::FIELD_COUNTY_FIPS  => 'required|string|max:5',
        ]);

        if($validator->fails()) {
            $this->newLine();
            $this->error("DMA Locations Repopulation Error: File failed validation - " . $validator->errors());
            return false;
        }

        return true;
    }

    /**
     * @param array $csvLine
     * @return array
     */
    private function mapCSVToTableFieldNames(array $csvLine): array
    {
        $map = [
            Dma::FIELD_DMA_CODE,
            Dma::FIELD_DMA_NAME,
            DmaLocation::FIELD_STATE_KEY,
            DmaLocation::FIELD_STATE_FIPS,
            DmaLocation::FIELD_COUNTY_NAME,
            DmaLocation::FIELD_COUNTY_FIPS,
        ];

        return array_combine($map, $csvLine);
    }

    /**
     * @return void
     */
    private function checkUnmappedCounties(): void
    {
        $this->info("Getting county locations");
        $counties = $this->getCountiesWithCountyFips();

        $this->info("Getting DMAs");
        $dmaLocations = DmaLocation::query()
            ->select(DmaLocation::FIELD_ID, DmaLocation::FIELD_DMA_ID, DmaLocation::FIELD_LOCATION_ID)
            ->get();

        $this->info("Checking DMA County Coverage");
        $bar = $this->output->createProgressBar($counties->count());
        $bar->start();

        $countiesMapped = 0;
        $countiesUnmapped = 0;
        $alaskaUnmapped = 0;
        $puertoRicoUnmapped = 0;
        foreach ($counties as $county) {
            if ($dmaLocations->contains(function ($dmaLocation) use ($county) {
                return $dmaLocation->location_id == $county->id;
            })) {
                $countiesMapped++;
            } else {
                // Parts of Alaska and Puerto Rico are not included in DMAs
                if ($county->state_abbr === 'AK') {
                    $alaskaUnmapped++;
                } else if ($county->state_abbr === 'PR') {
                    $puertoRicoUnmapped++;
                } else {
                    $stateFips = substr($county->county_fips, 0, 2);
                    $countyFips = substr($county->county_fips, 2, 3);
                    $this->newLine();
                    $this->error("County: $county->county ($county->state) not mapped to DMA. Location id: $county->id. " .
                        "($county->state_abbr,$county->county,$stateFips,$countyFips)");
                    $countiesUnmapped++;
                }
            }
            $bar->advance();
        }
        $bar->finish();
        $this->newLine();

        $this->info("Counties Mapped to DMAs: $countiesMapped");
        $this->info("Counties Unmapped: $countiesUnmapped");
        $this->info("Alaska Unmapped Counties: $alaskaUnmapped");
        $this->info("Puerto Rico Unmapped Counties: $puertoRicoUnmapped");
    }
}
