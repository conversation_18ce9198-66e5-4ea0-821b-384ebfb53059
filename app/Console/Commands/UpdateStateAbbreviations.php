<?php

namespace App\Console\Commands;

use App\Enums\Odin\StateAbbreviation;
use App\Models\Odin\Address;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateStateAbbreviations extends Command
{
    /**
     * The name and signature of the console command.
     * The console command description.
     *
     * @var string
     */
    protected $signature = 'update:state-abbreviations';
    protected $description = 'Replace full state names with their abbreviations in the addresses table';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Fetching state abbreviations...');
        $stateMap = StateAbbreviation::getAsKeyValueSelectArray();

        $this->info('Preparing updates...');

        $bar = $this->output->createProgressBar(Address::count());

        Address::query()->chunk(100, function ($addresses) use ($stateMap, $bar) {
            $updates = [];
            foreach ($addresses as $address) {
                $state = null;
                if (strlen($address->state) > 2) {
                    $state = $stateMap[$address->state] ?? null;
                } else {
                    if (!ctype_upper($address->state)) {
                        $state = strtoupper($address->state);
                    }
                }
                if($state) {
                    $updates[] = [
                        'id' => $address->id,
                        'state' => $state
                    ];
                }
            }

            $this->info('Updating addresses...');
            Address::query()->upsert($updates, ['id'], ['state']);

            $bar->advance(count($addresses));
        });

        $bar->finish();
        $this->info("\nAddresses updated successfully.");
    }
}
