<?php

namespace App\Console\Commands;

use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ServiceProduct;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Throwable;

/**
 * service_products table is full of duplicates, and rows for non-existent products
 */
class CleanUpServiceProductsTable extends Command
{

    protected $signature = 'clean:service_products {--dry-run}';

    protected $description = 'Clean up service products table - remove duplicate and redundant rows. Supply --dry-run to report rows without delete';

    private bool $dryRun = false;

    public function handle(): int
    {
        $this->dryRun = $this->option('dry-run');

        if (!$this->dryRun)
            DB::beginTransaction();

        $this->info("\nChecking redundant rows...");
        $this->removeRedundantRows();

        $this->info("\nChecking duplicate rows...");
        $serviceProductData = $this->getServiceProductData();
        $this->line(count($serviceProductData) . " product/service combinations with duplicates found.");

        $updatedConsumerProducts = 0;
        $deletedServiceProducts = 0;

        foreach($serviceProductData as $duplicateCollection) {
            if ($this->dryRun) {
                $this->line(count($duplicateCollection['duplicate_rows']) . " duplicate row(s) found for product_id: " . $duplicateCollection[ServiceProduct::FIELD_PRODUCT_ID] . ", industry_service_id: " . $duplicateCollection[ServiceProduct::FIELD_INDUSTRY_SERVICE_ID] . " - service_products id(s) to be deleted = " . join(",", $duplicateCollection['duplicate_rows']));
            }
            else {
                $updated = $this->repairConsumerProducts($duplicateCollection['duplicate_rows'], $duplicateCollection['first_row']);
                if ($updated !== -1) {
                    $updatedConsumerProducts += $updated;
                    $deleted = $this->deleteDuplicateRows($duplicateCollection['duplicate_rows']);
                    if ($deleted !== -1) {
                        $deletedServiceProducts += $deleted;
                    }
                    else {
                        DB::rollBack();
                        return Command::FAILURE;
                    }
                }
                else {
                    DB::rollBack();
                    return Command::FAILURE;
                }
            }
        }

        if (!$this->dryRun) {
            $this->info("\nUpdated $updatedConsumerProducts ConsumerProducts");
            $this->info("Deleted $deletedServiceProducts duplicate rows");

            DB::commit();
        }

        return Command::SUCCESS;
    }

    private function repairConsumerProducts(array $duplicateServiceProductIds, int $firstServiceProductId): int
    {
        try {
            return DB::table(ConsumerProduct::TABLE)
                ->whereIn(ConsumerProduct::FIELD_SERVICE_PRODUCT_ID, $duplicateServiceProductIds)
                ->update([ConsumerProduct::FIELD_SERVICE_PRODUCT_ID => $firstServiceProductId]);
        }
        catch(Throwable $e) {
            $this->error("Error updating existing ConsumerProducts: " . $e->getMessage());
            return -1;
        }
    }

    private function deleteDuplicateRows(array $duplicateServiceProductIds): int
    {
        try {
            return DB::table(ServiceProduct::TABLE)
                ->whereIn(ServiceProduct::FIELD_ID, $duplicateServiceProductIds)
                ->delete();
        }
        catch(Throwable $e) {
            $this->error("Error deleting existing ServiceProducts: " . $e->getMessage());
            return -1;
        }
    }

    private function removeRedundantRows(): void
    {
        $validProducts = DB::table(Product::TABLE)
            ->pluck(Product::FIELD_ID)
            ->toArray();
        $validIndustryServices = DB::table(IndustryService::TABLE)
            ->pluck(IndustryService::FIELD_ID)
            ->toArray();

        $redundantRows = DB::table(ServiceProduct::TABLE)
            ->whereNotIn(ServiceProduct::FIELD_PRODUCT_ID, $validProducts)
            ->orWhereNotIn(ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $validIndustryServices);

        if ($this->dryRun) {
            $this->line($redundantRows->count() . " rows were found with invalid product_id or industry_service_id");
            $redundantRows->get()->each(fn($row) => $this->line("\tid: " . $row->id . ", product_id: " . $row->product_id . ", industry_service_id: " . $row->industry_service_id));
        }
        else {
            $count = $redundantRows->count();
            $redundantRows->delete();
            $this->line($count . " rows were deleted");
        }

    }

    private function getServiceProductData(): array
    {
        $baseData = DB::table(ServiceProduct::TABLE)
            ->get()
            ->reduce(function(array $output, $serviceProduct) {
                $output[$serviceProduct->industry_service_id] = $output[$serviceProduct->industry_service_id] ?? [];
                $output[$serviceProduct->industry_service_id][$serviceProduct->product_id] = $output[$serviceProduct->industry_service_id][$serviceProduct->product_id] ?? [];
                $output[$serviceProduct->industry_service_id][$serviceProduct->product_id][] = $serviceProduct->id;
                return $output;
            }, []);

        $outputData = [];
        foreach ($baseData as $industryServiceId => $products) {
            foreach ($products as $productId => $product) {
                if (count($product) > 1) {
                    $outputData[] = [
                        ServiceProduct::FIELD_PRODUCT_ID => $productId,
                        ServiceProduct::FIELD_INDUSTRY_SERVICE_ID => $industryServiceId,
                        'first_row' => $product[0],
                        'duplicate_rows' => array_slice($product, 1),
                    ];
                }
            }
        }

        return $outputData;
    }
}
