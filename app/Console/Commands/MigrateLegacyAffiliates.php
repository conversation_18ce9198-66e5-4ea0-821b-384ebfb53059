<?php

namespace App\Console\Commands;

use App\Models\Affiliates\Affiliate;
use App\Models\Affiliates\Campaign;
use App\Models\Affiliates\Category;
use App\Models\Legacy\EloquentCampaign;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentUser;
use App\Utilities\AffiliatesPortal\ApiClient;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\DB;
use Ramsey\Uuid\Uuid;

class MigrateLegacyAffiliates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'legacy-migrate:affiliates {--chunk-size=500}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate legacy affiliates to the new schema';

    /**
     * @return void
     * @throws ConnectionException
     */
    public function handle()
    {
        $this->info("Starting migration");

        $chunkSize = (int) $this->option('chunk-size');

        $this->migrateAffiliates($chunkSize);
        $this->migrateAffiliateUsers($chunkSize);
        $this->migrateCategories($chunkSize);
        $this->migrateCampaigns($chunkSize);

        $this->info("Finished migration");
    }

    /**
     * @param int $chunkSize
     * @return bool
     */
    private function migrateAffiliates(int $chunkSize): bool
    {
        $this->info("Migrating affiliates");

        $now = Carbon::now('UTC')->timestamp;

        $existingAffiliates = Affiliate::query()->pluck(Affiliate::FIELD_ID)->toArray();

        EloquentCompany::query()
            ->where(EloquentCompany::TABLE.'.'.EloquentCompany::TYPE, EloquentCompany::TYPE_AFFILIATE)
            ->whereIntegerNotInRaw(EloquentCompany::TABLE.'.'.EloquentCompany::ID, $existingAffiliates)
            ->distinct()
            ->select([
                EloquentCompany::TABLE.'.'.EloquentCompany::ID,
                EloquentCompany::TABLE.'.'.EloquentCompany::COMPANY_NAME,
                EloquentCompany::TABLE.'.'.EloquentCompany::TIMESTAMP_ADDED
            ])
            ->chunk($chunkSize, function($affiliates) use ($now) {
                DB::transaction(function() use ($affiliates, $now) {
                    $affiliateRows = [];

                    foreach($affiliates as $affiliate) {
                        $uuid = Uuid::uuid4()->toString();

                        $affiliateRows[] = [
                            Affiliate::FIELD_ID => $affiliate->{EloquentCompany::ID},
                            Affiliate::FIELD_NAME => $affiliate->{EloquentCompany::COMPANY_NAME},
                            Affiliate::FIELD_UUID => $uuid,
                            Affiliate::FIELD_CREATED_AT => Carbon::createFromTimestamp($affiliate->{EloquentCompany::TIMESTAMP_ADDED} ?: $now)->format('Y-m-d H:i:s'),
                        ];

                        if(count($affiliateRows) >= 500) {
                            Affiliate::query()->insert($affiliateRows);

                            $affiliateRows = [];
                        }
                    }

                    if(!empty($affiliateRows)) {
                        Affiliate::query()->insert($affiliateRows);
                    }
                });
            });

        $this->info("Finished migrating affiliates");

        return true;
    }

    /**
     * @param int $chunkSize
     * @return bool
     */
    private function migrateAffiliateUsers(int $chunkSize): bool
    {
        $this->info("Migrating affiliate users");

        /** @var $apiClient ApiClient */
        $apiClient = app(ApiClient::class);

        $affiliates = Affiliate::query()->pluck( Affiliate::FIELD_UUID, Affiliate::FIELD_ID)->toArray();

        EloquentUser::query()
            ->whereIntegerInRaw(EloquentUser::TABLE.'.'.EloquentUser::COMPANY_ID, array_keys($affiliates))
            ->where(EloquentUser::TABLE.'.'.EloquentUser::STATUS, EloquentUser::FIELD_STATUS_VALUE_ACTIVE)
            ->distinct()
            ->select([
                EloquentUser::TABLE.'.'.EloquentUser::FIRST_NAME,
                EloquentUser::TABLE.'.'.EloquentUser::LAST_NAME,
                EloquentUser::TABLE.'.'.EloquentUser::EMAIL,
                EloquentUser::TABLE.'.'.EloquentUser::COMPANY_ID
            ])
            ->chunk($chunkSize, function($users) use ($apiClient, $affiliates) {
                foreach($users as $user) {
                    $apiClient->createUser([
                        'name' => $user->{EloquentUser::FIRST_NAME}.' '.$user->{EloquentUser::LAST_NAME},
                        'email' => $user->{EloquentUser::EMAIL},
                        'view_lead_details' => true,
                        'affiliate_id' => $affiliates[$user->{EloquentUser::COMPANY_ID}],
                    ]);
                }
            });

        $this->info("Finished migrating affiliate users");

        return true;
    }

    /**
     * @param int $chunkSize
     * @return bool
     */
    private function migrateCategories(int $chunkSize): bool
    {
        $this->info("Migrating categories");

        $now = Carbon::now('UTC')->format('Y-m-d H:i:s');

        $affiliateIds = Affiliate::query()->pluck(Affiliate::FIELD_ID)->toArray();
        $existingCampaignIds = Campaign::query()->pluck(Campaign::FIELD_ID)->toArray();

        EloquentCampaign::query()
            ->whereIntegerInRaw(EloquentCampaign::TABLE.'.'.EloquentCampaign::COMPANY_ID, $affiliateIds)
            ->whereIntegerNotInRaw(EloquentCampaign::TABLE.'.'.EloquentCampaign::CAMPAIGN_ID, $existingCampaignIds)
            ->select([
                EloquentCampaign::TABLE.'.'.EloquentCampaign::COMPANY_ID,
                EloquentCampaign::TABLE.'.'.EloquentCampaign::CATEGORY
            ])
            ->distinct()
            ->chunk($chunkSize, function($categories) use ($now) {
                DB::transaction(function() use ($now, $categories) {
                    $categoryRows = [];

                    foreach($categories as $category) {
                        $categoryRows[] = [
                            Category::FIELD_AFFILIATE_ID => $category->{EloquentCampaign::COMPANY_ID},
                            Category::FIELD_NAME => $category->{EloquentCampaign::CATEGORY},
                            Category::CREATED_AT => $now
                        ];

                        if(count($categoryRows) >= 500) {
                            Category::query()->insert($categoryRows);

                            $categoryRows = [];
                        }
                    }

                    if(!empty($categoryRows)) {
                        Category::query()->insert($categoryRows);
                    }
                });
            });

        $this->info("Finished migrating categories");

        return true;
    }

    /**
     * @param int $chunkSize
     * @return bool
     */
    private function migrateCampaigns(int $chunkSize): bool
    {
        $this->info("Migrating campaigns");

        $now = Carbon::now('UTC')->timestamp;

        $affiliateIds = Affiliate::query()->pluck(Affiliate::FIELD_ID)->toArray();
        $existingCampaignIds = Campaign::query()->pluck(Campaign::FIELD_ID)->toArray();

        $categoryIds = Category::query()->pluck(Category::FIELD_ID, Category::FIELD_NAME)->toArray();

        EloquentCampaign::query()
            ->whereIntegerInRaw(EloquentCampaign::TABLE.'.'.EloquentCampaign::COMPANY_ID, $affiliateIds)
            ->whereIntegerNotInRaw(EloquentCampaign::TABLE.'.'.EloquentCampaign::CAMPAIGN_ID, $existingCampaignIds)
            ->select([
                EloquentCampaign::TABLE.'.'.EloquentCampaign::CAMPAIGN_ID,
                EloquentCampaign::TABLE.'.'.EloquentCampaign::COMPANY_ID,
                EloquentCampaign::TABLE.'.'.EloquentCampaign::NAME,
                EloquentCampaign::TABLE.'.'.EloquentCampaign::CATEGORY,
                EloquentCampaign::TABLE.'.'.EloquentCampaign::STATUS,
                EloquentCampaign::TABLE.'.'.EloquentCampaign::TIMESTAMP_ADDED,
                EloquentCampaign::TABLE.'.'.EloquentCampaign::TIMESTAMP_UPDATED
            ])
            ->distinct()
            ->chunk($chunkSize, function($campaigns) use ($categoryIds, $now) {
                DB::transaction(function() use ($campaigns, $categoryIds, $now) {
                    $campaignRows = [];

                    foreach($campaigns as $campaign) {
                        $campaignRows[] = [
                            Campaign::FIELD_ID => $campaign->{EloquentCampaign::CAMPAIGN_ID},
                            Campaign::FIELD_AFFILIATE_ID => $campaign->{EloquentCampaign::COMPANY_ID},
                            Campaign::FIELD_NAME => $campaign->{EloquentCampaign::NAME},
                            Campaign::FIELD_CATEGORY_ID => $categoryIds[$campaign->{EloquentCampaign::CATEGORY}] ?? 0,
                            Campaign::FIELD_STATUS => $campaign->{EloquentCampaign::STATUS},
                            Campaign::FIELD_CREATED_AT => Carbon::createFromTimestamp($campaign->{EloquentCampaign::TIMESTAMP_ADDED} ?: $now)->format('Y-m-d H:i:s'),
                            Campaign::FIELD_UPDATED_AT => Carbon::createFromTimestamp($campaign->{EloquentCampaign::TIMESTAMP_UPDATED} ?: $now)->format('Y-m-d H:i:s')
                        ];
                    }

                    Campaign::query()->insert($campaignRows);
                });
            });

        $this->info("Finished migrating campaigns");

        return true;
    }
}
