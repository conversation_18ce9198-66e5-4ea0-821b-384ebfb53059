<?php

namespace App\Console\Commands;

use App\Models\Odin\Company;
use App\Repositories\Odin\CompanyRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\Console\Command\Command as CommandAlias;
use Throwable;

class UpdateCompanyConsolidatedStatus extends Command
{
    const ARG_COMPANY_IDS = 'company-ids';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = "company:consolidated-status:update {--null-only : Only update companies with null consolidated status} {company-ids?* : Space separated list of company ID's to update. Updates all by default. }";

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Updates the consolidated status of companies.';

    /** @var CompanyRepository $companyRepository */
    protected CompanyRepository $companyRepository;

    /**
     * @param CompanyRepository $companyRepository
     */
    public function __construct(CompanyRepository $companyRepository)
    {
        parent::__construct();
        $this->companyRepository = $companyRepository;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $companyQuery = Company::query();

        $companyIds = $this->argument(self::ARG_COMPANY_IDS);
        if(!empty($companyIds)) {
            $validator = Validator::make(
                [
                    self::ARG_COMPANY_IDS => $companyIds
                ],
                [
                    self::ARG_COMPANY_IDS => ['array', 'required'],
                    self::ARG_COMPANY_IDS.'.*' => ["integer", "min:0", "exists:".Company::TABLE.",".Company::FIELD_ID]
                ]
            );

            if($validator->fails()) {
                $this->error("Company ID's must be positive integers and exist in companies table");

                return CommandAlias::INVALID;
            }

            $companyQuery->whereIn(Company::FIELD_ID, $companyIds);
        }
        else if ($this->option('null-only')) {
            $companyQuery->whereNull(Company::FIELD_CONSOLIDATED_STATUS);
        }

        $this->withProgressBar($companyQuery->get(), function (Company $company) {
            try {
                $company->recalculateConsolidatedStatus();
            }
            catch(Throwable $e) {
                $this->newLine();

                $this->error("{$company->{Company::FIELD_ID}}:".$e->getMessage());
            }
        });

        $this->newLine();

        $this->info('Consolidated status has been updated for all companies.');

        return CommandAlias::SUCCESS;
    }
}
