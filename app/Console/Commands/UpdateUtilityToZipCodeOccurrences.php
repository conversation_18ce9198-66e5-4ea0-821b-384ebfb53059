<?php

namespace App\Console\Commands;

use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\Location;
use App\Models\UtilityToZipCodeOccurrence;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateUtilityToZipCodeOccurrences extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update-utility-to-zip-occurrences';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update the utility to zip code occurrences table';

    const TIMESPAN_IN_YEARS  = 4;
    const MINIMUM_LEAD_COUNT = 3;

    /**
     * Execute the console command.
     *
     */
    public function handle()
    {
        ini_set('memory_limit', '1024M');

        $data = EloquentQuote::query()->select([
            Location::TABLE . '.' . Location::ID . ' AS location_id',
            EloquentQuote::TABLE . '.' . EloquentQuote::UTILITY_ID . ' AS utility_id',
            DB::raw('count(*) AS lead_count')
        ])
            ->join(EloquentAddress::TABLE, EloquentQuote::TABLE . '.' . EloquentQuote::ADDRESS_ID, '=', EloquentAddress::TABLE . '.' . EloquentAddress::ID)
            ->join(Location::TABLE, EloquentAddress::TABLE . '.' . EloquentAddress::ZIP_CODE, '=', Location::TABLE . '.' . Location::ZIP_CODE)
            ->where(Location::TABLE . '.' . Location::TYPE, Location::TYPE_ZIP_CODE)
            ->where(EloquentQuote::TABLE . '.' . EloquentQuote::UTILITY_ID, '>', 0)
            ->where(EloquentQuote::TABLE . '.' . EloquentQuote::TIMESTAMP_ADDED, '>', Carbon::now()->subYears(self::TIMESPAN_IN_YEARS)->timestamp)
            ->where(EloquentQuote::TABLE . '.' . EloquentQuote::SOLAR_LEAD, 1)
            ->groupBy([
                EloquentAddress::TABLE . '.' . EloquentAddress::ZIP_CODE,
                EloquentQuote::TABLE . '.' . EloquentQuote::UTILITY_ID
            ])
            ->get()
            ->toArray();

        UtilityToZipCodeOccurrence::query()->truncate();


        $dataChunks = array_chunk($data, 10000);

        foreach($dataChunks as $dataChunk) {
            UtilityToZipCodeOccurrence::query()->insert($dataChunk);
        }

        UtilityToZipCodeOccurrence::query()
            ->where(UtilityToZipCodeOccurrence::FIELD_LEAD_COUNT, '<', self::MINIMUM_LEAD_COUNT)
            ->delete();

        $this->removeNonMaxLeadCounts();
    }

    /**
     * @return void
     */
    protected function removeNonMaxLeadCounts(): void
    {
        $uniqueLocations = UtilityToZipCodeOccurrence::query()
            ->groupBy(UtilityToZipCodeOccurrence::FIELD_LOCATION_ID)
            ->pluck(UtilityToZipCodeOccurrence::FIELD_LOCATION_ID)
            ->toArray();

        $highestIds = [];

        foreach ($uniqueLocations as $uniqueLocation) {
            $highestIds[] = UtilityToZipCodeOccurrence::query()
                ->where(UtilityToZipCodeOccurrence::FIELD_LOCATION_ID, $uniqueLocation)
                ->orderByDesc(UtilityToZipCodeOccurrence::FIELD_LEAD_COUNT)
                ->first()->id;
        }

        UtilityToZipCodeOccurrence::query()
            ->whereNotIn(UtilityToZipCodeOccurrence::FIELD_ID, $highestIds)
            ->delete();
    }
}
