<?php

namespace App\Console\Commands;

use App\Models\CompanyDiscovery\AddressDiscoveryStatus;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use Illuminate\Console\Command;

class CleanImportedAddresses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clean-up:imported-addresses';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cleans imported address';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $addresses = AddressDiscoveryStatus::query()
            ->select(Address::TABLE.'.*')
            ->join(Address::TABLE, AddressDiscoveryStatus::TABLE.'.'.AddressDiscoveryStatus::FIELD_ADDRESS_ID, '=', Address::TABLE.'.'.Address::FIELD_ID)
            ->get();

        $bar = $this->output->createProgressBar($addresses->count());
        $bar->start();

        /** @var Address $address */
        foreach($addresses as $address) {

            Address::query()->updateOrCreate([
                Address::FIELD_ID => $address->id
            ],
            [
                Address::FIELD_ADDRESS_1 => trim($address->address_1 .' '. $address->address_2),
                Address::FIELD_ADDRESS_2 => null,
                Address::FIELD_LEGACY_ID => null
            ]);

            $bar->advance();
        }
    }
}
