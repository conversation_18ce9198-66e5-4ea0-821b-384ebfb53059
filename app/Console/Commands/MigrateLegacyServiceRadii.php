<?php

namespace App\Console\Commands;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Models\ServiceRadius;
use App\Services\DatabaseHelperService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use function Laravel\Prompts\clear;

class MigrateLegacyServiceRadii extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'legacy-migrate:service-radii';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate legacy locations to A2 table';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $count = DB::table(DatabaseHelperService::readOnlyDatabase() . '.' . ServiceRadius::TABLE)
            ->count();

        if (!$count) {
            $this->line("Legacy service_radii table is empty.");

            return Command::FAILURE;
        }

        ServiceRadius::query()->truncate();
        clear();
        $now = now();

        $insertData = DB::table(DatabaseHelperService::readOnlyDatabase() . '.' . ServiceRadius::TABLE)
            ->select([
                ServiceRadius::TABLE . '.*',
                DB::raw(Company::TABLE .'.'. CompanyLocation::FIELD_ID . ' as odin_company_id'),
            ])->leftJoin(Company::TABLE, Company::TABLE .'.'. Company::FIELD_LEGACY_ID, ServiceRadius::TABLE .'.'. ServiceRadius::FIELD_COMPANY_ID)
            ->get()
            ->reduce(function (array $output, $serviceRadius) use ($now) {
                $output[] = [
                    ServiceRadius::FIELD_COMPANY_ID  => $serviceRadius->odin_company_id,
                    ServiceRadius::FIELD_RADIUS      => $serviceRadius->radius,
                    ServiceRadius::FIELD_LOCATION_ID => $serviceRadius->location_id,
                    ServiceRadius::CREATED_AT        => $now,
                ];
                return $output;
            }, []);

        if (DB::table(ServiceRadius::TABLE)->insert($insertData)) {
            $this->info("Successfully migrated legacy records.");

            return Command::SUCCESS;
        }

        $this->warn("Something terrible has happened.");

        return Command::FAILURE;
    }
}