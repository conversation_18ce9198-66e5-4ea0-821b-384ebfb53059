<?php

namespace App\Console\Commands;

use App\Models\Legacy\EloquentCompany;
use App\Models\Odin\Company;
use App\Services\DatabaseHelperService;
use App\Services\Odin\MigrateAWSCompanyMediaService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Exception;
use Illuminate\Support\Collection;

class MigrateCompanyMediaFromAWS extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:company-media-from-aws';

    /**
     * NOTE,
     * - Ensure that you've already transferred the AWS (bucket) media (company_media) to GCP (bucket).
     * - Once done, the GCP (bucket) will be having a folder named as the 'company_media' on bucket's root carrying legacy/AWS media files with the legacy directory structure.
     *
     * @var string
     */
    protected $description = 'Handles migrating AWS media files to A2 (schema) and moving them to the cloud with new structure for the companies already migrated to A2.';

    /**
     * Execute the console command.
     *
     * @param MigrateAWSCompanyMediaService $companyMediaService
     * @return int
     */
    public function handle(MigrateAWSCompanyMediaService $companyMediaService): int
    {
        $this->line("Initiating the migration process...");

        $totalCompanies = $this->getBaseQuery()->count();

        if($totalCompanies > 0) {
            $this->info("Total no. of companies to migrate their media: $totalCompanies");

            try {
                $this->getBaseQuery()
                     ->chunk(500, function($companies) use($companyMediaService) {
                         /** @var Collection $companies */
                         $companyMediaService->handle($companies);
                     });
            }
            catch (Exception $exc) {
                $this->alert("Error: {$exc->getMessage()}");
            }

            $this->info('Finished processing.');
        }
        else {
            $this->line("No company/media to migrate. Make sure you've already migrated the legacy companies.");
        }

        return 0;
    }

    /**
     * @return Builder
     */
    protected function getBaseQuery(): Builder
    {
        return Company::query()
            ->join(DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE, function($join) {
                $join->on(
                    DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE.'.'.EloquentCompany::ID,
                    DatabaseHelperService::database().'.'.Company::TABLE.'.'.Company::FIELD_LEGACY_ID
                );
            });
    }
}
