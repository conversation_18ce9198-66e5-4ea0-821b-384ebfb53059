<?php

namespace App\Console\Commands;

use App\Enums\Locations\CountryLocation;
use App\Enums\Locations\LocationType;
use App\Models\Legacy\LegacyLocation;
use App\Models\Legacy\Location;
use App\Models\Locations\BaseLocationModel;
use App\Models\Locations\USLocation;
use App\Services\DatabaseHelperService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;
use Laravel\Prompts\Progress;
use stdClass;
use Throwable;
use function Laravel\Prompts\clear;
use function Laravel\Prompts\progress;

class MigrateLegacyLocations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'legacy-migrate:locations {--chunk-size=5000 : The number of records per batch}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate legacy locations to A2 table';

    private string $modelName = 'Locations';
    private int $chunkSize = 5000;
    private Progress $progressBar;
    private array $locationTypeMap = [
        LegacyLocation::TYPE_STATE    => LocationType::ADMINISTRATIVE_AREA_1->value,
        LegacyLocation::TYPE_COUNTY   => LocationType::ADMINISTRATIVE_AREA_2->value,
        LegacyLocation::TYPE_CITY     => LocationType::LOCALITY->value,
        LegacyLocation::TYPE_ZIP_CODE => LocationType::POSTAL_CODE->value,
    ];

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->chunkSize = (int) $this->option('chunk-size') ?: 5000;
        $count = DB::table(DatabaseHelperService::readOnlyDatabase() .'.'. LegacyLocation::TABLE)
            ->count();

        if (!$count) {
            $this->line("Legacy locations table is empty.");

            return Command::FAILURE;
        }

        clear();
        $chunks = ceil($count / $this->chunkSize);
        $this->info("Processing $count $this->modelName in $chunks chunks...\n");
        $this->progressBar = progress("Processing $this->modelName chunks", $chunks);

        DB::beginTransaction();
        USLocation::unguard(); // match ids with legacy
        try {
            if (!$this->migrateLegacyLocations())
                throw new Exception("Migration failed");
        }
        catch (Throwable $e) {
            DB::rollBack();
            $this->error($e->getMessage());

            return Command::FAILURE;
        }
        $this->progressBar->finish();
        DB::commit();
        USLocation::reguard();

        $this->info("\nSuccessfully migrated $chunks of chunky $this->modelName data.");
        $this->line("\nUpdating parent pointers...");

        DB::beginTransaction();
        if ($this->addParentPointers()) {
            $this->progressBar->finish();
            $this->info("\nUpdated all parent pointers successfully");
        }
        else {
            $this->warn("\nThere was an error updating parent pointers.");
            DB::rollBack();
            return Command::FAILURE;
        }
        DB::commit();
        $this->info("\nAll pointers validated successfully.");

        if (!$this->validatePointers()) {
            $this->warn("Pointer validation failed. All data migrated, but pointer ids must be fixed before using the table.");

            return Command::FAILURE;
        }

        if (!$this->validatePrimaryKeys()) {
            $this->error("Primary key validation failed. New table does not match legacy table.\n");

            return Command::FAILURE;
        }
        $this->info("\nAll legacy primary keys have an exact match on the new table.");

        $this->info("\nAll operations successful. You deserve a beer.\n");

        return Command::SUCCESS;
    }

    /**
     * @return bool
     */
    private function migrateLegacyLocations(): bool
    {
        return DB::table(DatabaseHelperService::readOnlyDatabase() .'.'. LegacyLocation::TABLE)
            ->orderBy(LegacyLocation::TABLE . '.' . LegacyLocation::ID)
            ->chunkById($this->chunkSize, function ($chunk) {
                $insertData = $chunk->reduce(function (array $output, $location) {
                    $output[] = $this->transformLocation($location);

                    return $output;
                }, []);
                if (DB::table(USLocation::TABLE)->insert($insertData)) {
                    $this->progressBar->advance();
                    return true;
                }
                else
                    return false;
            }, LegacyLocation::TABLE . '.' . LegacyLocation::ID, LegacyLocation::ID);
    }

    /**
     * @param stdClass $location
     * @return array
     */
    private function transformLocation(stdClass $location): array
    {
        return [
            USLocation::FIELD_ID                 => $location->id,
            USLocation::FIELD_TYPE               => $this->locationTypeMap[$location->type],
            USLocation::FIELD_STATE              => $location->state,
            USLocation::FIELD_STATE_KEY          => $location->state_key,
            USLocation::FIELD_STATE_ABBREVIATION => $location->state_abbr,
            USLocation::FIELD_COUNTY             => $location->county,
            USLocation::FIELD_COUNTY_KEY         => $location->county_key,
            USLocation::FIELD_CITY               => $location->city,
            USLocation::FIELD_CITY_KEY           => $location->city_key,
            USLocation::FIELD_ZIP_CODE           => $location->zip_code,
        ];
    }

    /**
     * Add parent pointers. Any region which is not a top level region type should point to a parent
     *  This may not be the immediate region type above i.e. some Cities will have a State as their parent as they have no County
     *
     * @return bool
     */
    private function addParentPointers(): bool
    {
        $stateLocations = 'state_locations';
        $countyLocations = 'county_locations';
        $cityLocations = 'city_locations';

        $selectColumns = [
            DB::raw(LegacyLocation::TABLE .'.'. LegacyLocation::ID . ' as child_id'),
            DB::raw("$stateLocations.". LegacyLocation::ID . ' as state_id'),
            DB::raw("$countyLocations.". LegacyLocation::ID . ' as county_id'),
            DB::raw("$cityLocations.". LegacyLocation::ID . ' as city_id'),
        ];

        $countyQuery = DB::table(DatabaseHelperService::readOnlyDatabase() .'.'. LegacyLocation::TABLE)
            ->select(array_slice($selectColumns, 0, 2))
            ->join(DatabaseHelperService::readOnlyDatabase() .'.'. LegacyLocation::TABLE . " as $stateLocations", fn(JoinClause $join) =>
                $join->on(LegacyLocation::TABLE . '.' . LegacyLocation::STATE_ABBREVIATION, "$stateLocations." .LegacyLocation::STATE_ABBREVIATION)
                    ->where("$stateLocations." . LegacyLocation::TYPE, LegacyLocation::TYPE_STATE)
            )->where(LegacyLocation::TABLE .'.'. LegacyLocation::TYPE, LegacyLocation::TYPE_COUNTY);
        if ($this->processPointerChunks($countyQuery, "County")) {
            $cityQuery = DB::table(DatabaseHelperService::readOnlyDatabase() .'.'. LegacyLocation::TABLE)
                ->select(array_slice($selectColumns, 0, 3))
                ->join(DatabaseHelperService::readOnlyDatabase() .'.'. LegacyLocation::TABLE . " as $stateLocations", fn(JoinClause $join) =>
                    $join->on(LegacyLocation::TABLE . '.' . LegacyLocation::STATE_ABBREVIATION, "$stateLocations." .LegacyLocation::STATE_ABBREVIATION)
                        ->where("$stateLocations." . LegacyLocation::TYPE, LegacyLocation::TYPE_STATE)
                )->leftJoin(DatabaseHelperService::readOnlyDatabase() .'.'. LegacyLocation::TABLE . " as $countyLocations", fn(JoinClause $join) =>
                    $join->on(LegacyLocation::TABLE . '.' . LegacyLocation::STATE_ABBREVIATION, "$countyLocations." .LegacyLocation::STATE_ABBREVIATION)
                        ->on(LegacyLocation::TABLE . '.' . LegacyLocation::COUNTY_KEY, "$countyLocations." .LegacyLocation::COUNTY_KEY)
                        ->where("$countyLocations." . LegacyLocation::TYPE, LegacyLocation::TYPE_COUNTY)
                )->where(LegacyLocation::TABLE .'.'. LegacyLocation::TYPE, LegacyLocation::TYPE_CITY);
            if ($this->processPointerChunks($cityQuery, "City")) {
                $zipQuery = DB::table(DatabaseHelperService::readOnlyDatabase() .'.'. LegacyLocation::TABLE)
                    ->select($selectColumns)
                    ->join(DatabaseHelperService::readOnlyDatabase() .'.'. LegacyLocation::TABLE . " as $stateLocations", fn(JoinClause $join) =>
                        $join->on(LegacyLocation::TABLE . '.' . LegacyLocation::STATE_ABBREVIATION, "$stateLocations." .LegacyLocation::STATE_ABBREVIATION)
                            ->where("$stateLocations." . LegacyLocation::TYPE, LegacyLocation::TYPE_STATE)
                    )->leftJoin(DatabaseHelperService::readOnlyDatabase() .'.'. LegacyLocation::TABLE . " as $countyLocations", fn(JoinClause $join) =>
                        $join->on(LegacyLocation::TABLE . '.' . LegacyLocation::STATE_ABBREVIATION, "$countyLocations." .LegacyLocation::STATE_ABBREVIATION)
                            ->on(LegacyLocation::TABLE . '.' . LegacyLocation::COUNTY_KEY, "$countyLocations." .LegacyLocation::COUNTY_KEY)
                            ->where("$countyLocations." . LegacyLocation::TYPE, LegacyLocation::TYPE_COUNTY)
                    )->leftJoin(DatabaseHelperService::readOnlyDatabase() .'.'. LegacyLocation::TABLE . " as $cityLocations", fn(JoinClause $join) =>
                        $join->on(LegacyLocation::TABLE . '.' . LegacyLocation::STATE_ABBREVIATION, "$cityLocations." .LegacyLocation::STATE_ABBREVIATION)
                            ->on(LegacyLocation::TABLE . '.' . LegacyLocation::COUNTY_KEY, "$cityLocations." .LegacyLocation::COUNTY_KEY)
                            ->on(LegacyLocation::TABLE . '.' . LegacyLocation::CITY_KEY, "$cityLocations." .LegacyLocation::CITY_KEY)
                            ->where("$cityLocations." . LegacyLocation::TYPE, LegacyLocation::TYPE_CITY)
                    )->where(LegacyLocation::TABLE .'.'. LegacyLocation::TYPE, LegacyLocation::TYPE_ZIP_CODE);

                return $this->processPointerChunks($zipQuery, "Zip Code");
            }
        }

        return false;
    }

    /**
     * @param Builder $query
     * @param string $name
     * @return bool
     */
    private function processPointerChunks(Builder $query, string $name): bool
    {
        $count = $query->count();
        $chunks = ceil($count / $this->chunkSize);
        $this->progressBar = progress("Adding $name parent pointers in $chunks chunks...", $chunks);

        $success = $query->orderBy('child_id')
            ->chunkById($this->chunkSize, function ($chunk) {
                $upsertData = $chunk->reduce(function (array $output, $location) {
                    $output[] = [
                        USLocation::FIELD_ID                 => $location->child_id,
                        USLocation::FIELD_PARENT_LOCATION_ID => $location->city_id ?? $location->county_id ?? $location->state_id ?? null,
                    ];
                    return $output;
                }, []);

                try {
                    DB::table(USLocation::TABLE)->upsert($upsertData, [], [USLocation::FIELD_PARENT_LOCATION_ID]);
                    $this->progressBar->advance();
                }
                catch (Throwable $e) {
                    throw new Exception($e->getMessage());
                }
            }, LegacyLocation::TABLE . '.' . LegacyLocation::ID, 'child_id');

        if ($success)
            $this->progressBar->advance();

        return $success;
    }

    /**
     * Ensure pointers to the same table are valid
     *  top geographic region cannot have a parent
     *  other regions can only point to a higher region type
     *
     * @param CountryLocation $country
     * @return bool
     */
    private function validatePointers(CountryLocation $country = CountryLocation::US): bool
    {
        $locationTypeValues = array_map(fn(LocationType $type) => $type->value, LocationType::cases());
        $invalidRows = [];
        $missingRows = [];
        $table = $country->model()::TABLE;
        $locationTypeNames = $country->locationTypeNames();
        $hadErrors = false;

        try {
            foreach ($locationTypeValues as $locationType) {
                //The highest administrative region should not have a parent
                if ($locationType === min(...$locationTypeValues)) {
                    $invalid = DB::table($table)
                        ->select(BaseLocationModel::FIELD_ID)
                        ->where(BaseLocationModel::FIELD_TYPE, $locationType)
                        ->whereNotNull(BaseLocationModel::FIELD_PARENT_LOCATION_ID)
                        ->get()
                        ->pluck(BaseLocationModel::FIELD_ID)
                        ->toArray();
                    if ($invalid)
                        $invalidRows[$locationType] = $invalid;
                } else {
                    $invalidRows[$locationType] = [];
                    DB::table($table)
                        ->select([
                            $table . '.' . BaseLocationModel::FIELD_ID,
                            $table . '.' . BaseLocationModel::FIELD_PARENT_LOCATION_ID,
                            $table . '.' . BaseLocationModel::FIELD_TYPE,
                            DB::raw('l2.' . BaseLocationModel::FIELD_TYPE . ' as parent_type'),
                        ])->leftjoin($table . ' as l2', $table . '.' . BaseLocationModel::FIELD_PARENT_LOCATION_ID, 'l2.' . BaseLocationModel::FIELD_ID)
                        ->where($table .'.'. BaseLocationModel::FIELD_TYPE, $locationType)
                        ->orderBy($table . '.' . BaseLocationModel::FIELD_ID)
                        ->chunkById($this->chunkSize, function ($chunk) use (&$missingRows, &$invalidRows, $locationType) {
                            foreach ($chunk as $location) {
                                if ($location->parent_type >= $location->type)
                                    $invalidRows[$locationType][] = $location->id;
                                else if ($location->parent_type === null)
                                    $missingRows[$locationType][] = $location->id;
                            }
                        }, $table . '.' . BaseLocationModel::FIELD_ID, BaseLocationModel::FIELD_ID);
                }
            }
        }
        catch (Throwable $e) {
            $this->error($e->getMessage());

            return false;
        }

        foreach ($invalidRows as $locationType => $invalidIds) {
            $locationName = $locationTypeNames[$locationType] ?? "Unknown geographic region type";

            $count = count($invalidIds);
            if ($count) {
                $hadErrors = true;
                $warning = $locationType === min(...$locationTypeValues)
                    ? "The above $locationName location IDs have parent pointers. The top geographic region type must not have a parent location."
                    : "The above $locationName location IDs have parent pointers which point to the same or lower region type. Geographic regions must only have parents of a higher region type.";

                $this->line(implode(",", $invalidIds));
                $this->warn($warning);
            }
        }

        foreach ($missingRows as $locationType => $missingIds) {
            $locationName = $locationTypeNames[$locationType] ?? "Unknown geographic region type";

            if (count($missingIds)) {
                $hadErrors = true;
                $warning = "The above $locationName location IDs are missing parent pointers";

                $this->line(implode(",", $missingIds));
                $this->warn($warning);
            }
        }

        return !$hadErrors;
    }

    /**
     * Verify that all new locations match legacy locations by primary key
     *
     * @return bool
     */
    private function validatePrimaryKeys(): bool
    {
        $this->info("\nVerifying Legacy <> Admin2 primary keys match");
        $locationNames = CountryLocation::US->locationTypeNames();
        $hadErrors = false;

        foreach ($this->locationTypeMap as $legacyKey => $enumType) {
            $mismatched = DB::table(DatabaseHelperService::readOnlyDatabase() .'.'. LegacyLocation::TABLE)
                ->select(LegacyLocation::TABLE .'.'. LegacyLocation::ID)
                ->leftJoin(USLocation::TABLE, fn(JoinClause $join) =>
                    $join->on(LegacyLocation::TABLE .'.'. LegacyLocation::ID, USLocation::TABLE .'.'. USLocation::FIELD_ID)
                        ->where(USLocation::TABLE .'.'. USLocation::FIELD_TYPE, $enumType)
                )->where(LegacyLocation::TABLE .'.'. LegacyLocation::TYPE, $legacyKey)
                ->when($enumType === USLocation::TYPE_STATE->value, fn(Builder $query) =>
                    $query->whereColumn(USLocation::TABLE .'.'. USLocation::FIELD_STATE_ABBREVIATION, '!=', LegacyLocation::TABLE .'.'. LegacyLocation::STATE_ABBREVIATION)
                )->when($enumType === USLocation::TYPE_COUNTY->value, fn(Builder $query) =>
                    $query->whereColumn(USLocation::TABLE .'.'. USLocation::FIELD_COUNTY_KEY, '!=', LegacyLocation::TABLE .'.'. LegacyLocation::COUNTY_KEY)
                )->when($enumType === USLocation::TYPE_CITY->value, fn(Builder $query) =>
                    $query->whereColumn(USLocation::TABLE .'.'. USLocation::FIELD_CITY_KEY, '!=', LegacyLocation::TABLE .'.'. LegacyLocation::CITY_KEY)
                )->when($enumType === USLocation::TYPE_ZIP_CODE->value, fn(Builder $query) =>
                    $query->whereColumn(USLocation::TABLE .'.'. USLocation::FIELD_ZIP_CODE, '!=', LegacyLocation::TABLE .'.'. LegacyLocation::ZIP_CODE)
                );

            $count = $mismatched->count();
            if ($count) {
                $hadErrors = true;
                $typeName = $locationNames[$enumType] ?? "Unknown geographic region type";
                $this->warn("$count Legacy $typeName location(s) mismatched with Admin2");
            }
        }

        return !$hadErrors;
    }
}