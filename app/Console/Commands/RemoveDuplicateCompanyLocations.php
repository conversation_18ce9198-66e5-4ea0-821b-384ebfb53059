<?php

namespace App\Console\Commands;

use App\Models\Odin\CompanyLocation;
use Illuminate\Console\Command;

class RemoveDuplicateCompanyLocations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clean-up:duplicate-company-locations';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Removes duplicate company locations';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $companyLocations = CompanyLocation::query()
            ->groupBy(CompanyLocation::FIELD_ADDRESS_ID)
            ->groupBy(CompanyLocation::FIELD_COMPANY_ID)
            ->havingRaw('COUNT(*) > 1')
            ->get();

        $bar = $this->output->createProgressBar($companyLocations->count());
        $bar->start();

        /** @var CompanyLocation $companyLocation */
        foreach($companyLocations as $companyLocation) {
            $record = CompanyLocation::query()
                ->orderByDesc(CompanyLocation::FIELD_ID)
                ->where(CompanyLocation::FIELD_ADDRESS_ID, $companyLocation->address_id)
                ->where(CompanyLocation::FIELD_COMPANY_ID, $companyLocation->company_id)
                ->first();

            $record->externalReviews()->delete();
            $record->delete();

            $bar->advance();

        }
    }
}
