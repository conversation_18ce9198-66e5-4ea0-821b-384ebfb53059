<?php

namespace App\Console\Commands;

use App\Models\CompanyDiscovery\AddressDiscoveryStatus;
use App\Models\Odin\Address;
use App\Services\CompanyDiscoveryService;
use Illuminate\Console\Command;

class RetryFailedAddressDiscovery extends Command
{
    const CHOICE_YES = 'Yes';
    const CHOICE_NO  = 'No';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clean-up:failed-address-discoveries';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Retries address import where discovery of place ID failed';

    /** @var CompanyDiscoveryService $companyDiscoveryService */
    protected CompanyDiscoveryService $companyDiscoveryService;

    public function __construct(CompanyDiscoveryService $companyDiscoveryService)
    {
        parent::__construct();
        $this->companyDiscoveryService = $companyDiscoveryService;
    }


    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $addressIds = $this->getAddressIdsToProcess();

        $bar = $this->output->createProgressBar($addressIds->count());
        $bar->start();

        /** @var Address $address */
        foreach($addressIds as $address) {
            $address = Address::find($address->id);

            $this->companyDiscoveryService->processPlaceByAddress($address);

            $bar->advance();
        }
    }

    /**
     * @return \Illuminate\Database\Eloquent\Builder[]|\Illuminate\Database\Eloquent\Collection
     */
    private function getAddressIdsToProcess(): \Illuminate\Database\Eloquent\Collection|array
    {
        $numberOfLocationsToProcess = $this->getNumberOfLocationsToProcess();

        $query = AddressDiscoveryStatus::query()
            ->select(Address::TABLE.'.'.Address::FIELD_ID)
            ->join(Address::TABLE, AddressDiscoveryStatus::TABLE.'.'.AddressDiscoveryStatus::FIELD_ADDRESS_ID, '=', Address::TABLE.'.'.Address::FIELD_ID)
            ->where(AddressDiscoveryStatus::TABLE.'.'.AddressDiscoveryStatus::FIELD_CHECKED, true)
            ->where(Address::FIELD_ZIP_CODE,'=',"");

        if($numberOfLocationsToProcess) {
            $query->limit($numberOfLocationsToProcess);
        }

        return $query->get();
    }

    /**
     * @return int|null
     */
    private function getNumberOfLocationsToProcess(): ?int
    {
        $choice = $this->choice('Would you like to throttle the number of locations to process?', [self::CHOICE_NO, self::CHOICE_YES]);

        if ($choice === self::CHOICE_YES) {
            return intval($this->ask('Enter the number of locations you would like to process'));
        }

        return null;
    }
}
