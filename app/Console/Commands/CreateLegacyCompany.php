<?php

namespace App\Console\Commands;

use App\Jobs\CreateLegacyCompanyJob;
use App\Models\Odin\Company;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class CreateLegacyCompany extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'create-legacy-company {--company_id=} {--company_ids=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create legacy company';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $companyId = $this->option('company_id');

        if ($companyId) {
            $this->handleId((int)$companyId);
            return;
        }

        $companyIds = $this->option('company_ids');

        if (filled($companyIds)) {
            $this->handleCompanyIds(
                collect(explode(',', $companyIds))
            );
            return;
        }
    }

    /**
     * @param Collection $companyIds
     * @return void
     */
    protected function handleCompanyIds(Collection $companyIds): void
    {
        $companies = Company::query()
            ->whereIn(Company::FIELD_ID, $companyIds->unique()->filter())
            ->whereNull(Company::FIELD_LEGACY_ID)
            ->get();

        foreach ($companies as $company) {
            CreateLegacyCompanyJob::dispatch($company);
        }

        $this->info('Jobs dispatched..');
    }

    /**
     * @param int $companyId
     * @return void
     */
    protected function handleId(int $companyId): void
    {
        if (empty($companyId)) {
            $this->error('Company is required');
            return;
        }

        $company = Company::query()->findOrFail($companyId);

        if ($company->{Company::FIELD_LEGACY_ID}) {
            $this->error('Company already has legacy company');
            return;
        }

        CreateLegacyCompanyJob::dispatchSync($company);

        $company->refresh();

        $this->info("Legacy company id $company->legacy_id");
    }
}
