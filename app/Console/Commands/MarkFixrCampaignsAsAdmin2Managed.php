<?php

namespace App\Console\Commands;

use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Product;
use App\Models\Odin\ProductCampaign;
use App\Services\DatabaseHelperService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;

class MarkFixrCampaignsAsAdmin2Managed extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:campaigns-a2-managed';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update is_managed_by_a2 flag on Fixr Dashboard created campaigns';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $this->line("Updating Fixr-created campaigns...");

        if (!Schema::connection('readonly')->hasColumn(LeadCampaign::TABLE, LeadCampaign::IS_MANAGED_BY_A2)) {
            $this->warn("Aborting command - Lead Campaign column 'is_managed_by_a2' not found on readonly database table.\nHas the migration on legacy SR been run?");
            return 1;
        }

        /** @var Product $product */
        $product = Product::query()->where(Product::FIELD_NAME, \App\Enums\Odin\Product::LEAD)->firstOrFail();

        $campaignIds = ProductCampaign::query()
            ->where(ProductCampaign::FIELD_PRODUCT_ID, $product->id)
            ->pluck(ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID)
            ->unique();

        $campaignCount = $campaignIds->count();

        $this->info("Processing $campaignCount Lead Campaigns...");

        $updated = LeadCampaign::query()
            ->whereIn(LeadCampaign::ID, $campaignIds)
            ->update([LeadCampaign::IS_MANAGED_BY_A2 => true]);

        $this->line("Command finished - $updated rows were updated.");

        return 0;
    }
}
