<?php

namespace App\Console\Commands;

use App\Console\Commands\CompanyMergeTool\CompanyMergeService;
use App\Models\Odin\Company;
use Illuminate\Console\Command;

/**
 * This is now available in the front end in company->configurations
 */
class UndoCompanyMergeCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'undo:merge:companies {--source=} {--target=} {--dry-run}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Undo a Company merge.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $sourceCompanyId = $this->option('source');
        $targetCompanyId = $this->option('target');
        $dryRun          = $this->option('dry-run');

        $sourceCompany = Company::query()->find($sourceCompanyId);
        $targetCompany = Company::query()->find($targetCompanyId);

        if (!$sourceCompany || !$targetCompany) {
            $this->error("A valid source Company and target Company must be supplied.");

            return Command::FAILURE;
        } else if ($sourceCompanyId === $targetCompany) {
            $this->error("Merging a Company into itself would break the wibbliness factor of the space-time continuum. Trying to undo the process is noble, but insane.");

            return Command::FAILURE;
        }

        $this->info("Attempting to undo merge of $sourceCompany->name ($sourceCompanyId) to $targetCompany->name ($targetCompanyId).");

        /** @var CompanyMergeService $service */
        $service = app(CompanyMergeService::class);
        $result = $service->handleUndo($sourceCompany->id, $targetCompany->id, $dryRun, true);
        if ($dryRun) {
            if (!$result[CompanyMergeService::RESPONSE_STATUS] ?? false) {
                $this->error("Undo command failed.");
            }
            else {
                $this->printMessages($result[CompanyMergeService::KEY_UNDO] ?? []);

                return Command::SUCCESS;
            }
        }
        else {
            if (!$result[CompanyMergeService::RESPONSE_STATUS] ?? false) {
                $this->warn("Undo merge operation did not complete.");
            }
            else {
                $this->printMessages($result[CompanyMergeService::KEY_UNDO] ?? []);
                $this->info("\nUndo completed successfully.");

                return Command::SUCCESS;
            }
        }

        return Command::FAILURE;
    }

    /**
     * @param array $messageArray
     * @return void
     */
    private function printMessages(array $messageArray): void
    {
        if (!$messageArray) {
            print("No messages found.");
            return;
        }

        print("\n\n\nUndo:\n");
        print("ADMIN2 =>\n");
        print_r($messageArray[CompanyMergeService::DATABASE_ADMIN2]);
        print("LEGACY =>\n");
        print_r($messageArray[CompanyMergeService::DATABASE_LEGACY]);
    }
}
