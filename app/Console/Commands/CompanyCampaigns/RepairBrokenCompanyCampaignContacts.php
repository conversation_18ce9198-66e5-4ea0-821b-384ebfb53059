<?php

namespace App\Console\Commands\CompanyCampaigns;

use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\Product as ProductEnum;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\CompanyCampaignRelation;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModule;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleContact;
use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Legacy\EloquentUser;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignDeliveryMethod;
use App\Models\Legacy\LeadDeliveryMethod;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Services\DatabaseHelperService;
use Illuminate\Console\Command;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class RepairBrokenCompanyCampaignContacts
{
    protected IndustryEnum $industry;
    protected int $appointmentProductId;
    protected Builder $brokenContactQuery;
    protected Builder $missingContactQuery;

    protected array $brokenContacts = [];
    protected array $missingContacts = [];

    protected array $insertData = [];
    protected array $upsertData = [];
    protected array $removeIds = [];

    protected int $total = 0;
    protected int $count = 0;

    public function findAndRepairContacts(IndustryEnum $industry): int
    {
        $this->industry = $industry;
        $this->appointmentProductId = Product::query()
            ->where(Product::FIELD_NAME, ProductEnum::APPOINTMENT->value)
            ->first()
            ->id;

        print("\n");

        $this->findMissingContacts();
        $this->updateProgress("missing");
        $this->prepareMissingContacts($this->missingContacts);

        if ($this->insertData) {
            DB::table(CompanyCampaignDeliveryModuleContact::TABLE)
                ->insert($this->insertData);
            $this->count += count($this->insertData);
        }
        $this->updateProgress("missing");

        print("\n");
        $this->findBrokenContacts();
        $this->updateProgress("broken");
        $this->prepareBrokenContacts($this->brokenContacts);

        if ($this->upsertData) {
            $upsert = DB::table(CompanyCampaignDeliveryModuleContact::TABLE)
                ->upsert($this->upsertData,
                    CompanyCampaignDeliveryModuleContact::FIELD_ID,
                    [CompanyCampaignDeliveryModuleContact::FIELD_CONTACT_ID],
                );
            $this->count += $upsert / 2;
        }
        $this->updateProgress("broken");

        $this->cleanUpInvalidContacts();
        print("\n");

        return Command::SUCCESS;
    }

    protected function findBrokenContacts(): void
    {
        $this->brokenContactQuery = DB::table(DatabaseHelperService::readOnlyDatabase() .'.'. EloquentCompanyContact::TABLE)
            ->join(DatabaseHelperService::readOnlyDatabase() .'.'. LeadDeliveryMethod::TABLE, LeadDeliveryMethod::TABLE .'.'. LeadDeliveryMethod::FIELD_CONTACT_ID, '=', EloquentCompanyContact::TABLE .'.'. EloquentCompanyContact::FIELD_CONTACT_ID)
            ->join(DatabaseHelperService::readOnlyDatabase() .'.'. LeadCampaignDeliveryMethod::TABLE, LeadCampaignDeliveryMethod::TABLE .'.'. LeadCampaignDeliveryMethod::FIELD_LEAD_DELIVERY_METHOD_ID, '=', LeadDeliveryMethod::TABLE .'.'. LeadDeliveryMethod::FIELD_ID)
            ->join(DatabaseHelperService::readOnlyDatabase() .'.'. LeadCampaign::TABLE, LeadCampaign::TABLE .'.'. LeadCampaign::ID, '=', LeadCampaignDeliveryMethod::TABLE .'.'. LeadCampaignDeliveryMethod::FIELD_LEAD_CAMPAIGN_ID)
            ->join(CompanyCampaignRelation::TABLE, CompanyCampaignRelation::TABLE .'.'. CompanyCampaignRelation::FIELD_RELATION_ID, '=', LeadCampaign::TABLE .'.'. LeadCampaign::ID)
            ->join(CompanyCampaign::TABLE, CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID, '=', CompanyCampaignRelation::TABLE .'.'. CompanyCampaignRelation::FIELD_COMPANY_CAMPAIGN_ID)
            ->join(IndustryService::TABLE, IndustryService::TABLE .'.'. IndustryService::FIELD_ID, '=', CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_SERVICE_ID)
            ->join(Industry::TABLE, Industry::TABLE .'.'. Industry::FIELD_ID, '=', IndustryService::TABLE .'.'. IndustryService::FIELD_INDUSTRY_ID)
            ->join(CompanyCampaignDeliveryModule::TABLE, CompanyCampaignDeliveryModule::TABLE .'.'. CompanyCampaignDeliveryModule::FIELD_CAMPAIGN_ID, '=', CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID)
            ->join(CompanyCampaignDeliveryModuleContact::TABLE, CompanyCampaignDeliveryModuleContact::TABLE .'.'. CompanyCampaignDeliveryModuleContact::FIELD_MODULE_ID, '=', CompanyCampaignDeliveryModule::TABLE .'.'. CompanyCampaignDeliveryModule::FIELD_ID)
            ->join(CompanyUser::TABLE, CompanyUser::TABLE .'.'. CompanyUser::FIELD_ID, '=', CompanyCampaignDeliveryModuleContact::TABLE .'.'. CompanyCampaignDeliveryModuleContact::FIELD_CONTACT_ID)
            ->join(Company::TABLE, Company::TABLE .'.'. Company::FIELD_ID, '=', CompanyUser::TABLE .'.'. CompanyUser::FIELD_COMPANY_ID)
            ->where(Industry::TABLE .'.'. Industry::FIELD_NAME, $this->industry->value)
            ->whereColumn(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_COMPANY_ID, '!=', Company::TABLE .'.'. Company::FIELD_LEGACY_ID)
            ->whereColumn(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_COMPANY_ID, '!=', CompanyUser::TABLE .'.'. CompanyUser::FIELD_COMPANY_ID)
            ->distinct(CompanyCampaignDeliveryModuleContact::TABLE .'.'. CompanyCampaignDeliveryModuleContact::FIELD_ID)
            ->select([
                CompanyCampaignDeliveryModuleContact::TABLE .'.'. CompanyCampaignDeliveryModuleContact::FIELD_ID . " as delivery_module_contact_id",
                CompanyUser::TABLE .'.'. CompanyUser::FIELD_ID . " as company_user_id",
                CompanyUser::TABLE .'.'. CompanyUser::FIELD_LEGACY_ID . " as user_legacy_id",
                CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID . " as company_campaign_id",
                CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_COMPANY_ID . " as campaign_company_id",
                CompanyUser::TABLE .'.'. CompanyUser::FIELD_COMPANY_ID . " as user_company_id",
            ]);

        $this->total = $this->brokenContactQuery->count();
        $this->brokenContacts = $this->brokenContactQuery->get()
            ->groupBy('user_legacy_id')
            ->toArray();
    }

    /**
     * @return void
     */
    protected function findMissingContacts(): void
    {
        $this->missingContactQuery = DB::table(DatabaseHelperService::readOnlyDatabase() .'.'. EloquentCompanyContact::TABLE)
            ->join(DatabaseHelperService::readOnlyDatabase() .'.'. LeadDeliveryMethod::TABLE, LeadDeliveryMethod::TABLE .'.'. LeadDeliveryMethod::FIELD_CONTACT_ID, '=', EloquentCompanyContact::TABLE .'.'. EloquentCompanyContact::FIELD_CONTACT_ID)
            ->join(DatabaseHelperService::readOnlyDatabase() .'.'. LeadCampaignDeliveryMethod::TABLE, LeadCampaignDeliveryMethod::TABLE .'.'. LeadCampaignDeliveryMethod::FIELD_LEAD_DELIVERY_METHOD_ID, '=', LeadDeliveryMethod::TABLE .'.'. LeadDeliveryMethod::FIELD_ID)
            ->join(DatabaseHelperService::readOnlyDatabase() .'.'. LeadCampaign::TABLE, LeadCampaign::TABLE .'.'. LeadCampaign::ID, '=', LeadCampaignDeliveryMethod::TABLE .'.'. LeadCampaignDeliveryMethod::FIELD_LEAD_CAMPAIGN_ID)
            ->join(CompanyCampaignRelation::TABLE, CompanyCampaignRelation::TABLE .'.'. CompanyCampaignRelation::FIELD_RELATION_ID, '=', LeadCampaign::TABLE .'.'. LeadCampaign::ID)
            ->join(CompanyCampaign::TABLE, CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID, '=', CompanyCampaignRelation::TABLE .'.'. CompanyCampaignRelation::FIELD_COMPANY_CAMPAIGN_ID)
            ->join(IndustryService::TABLE, IndustryService::TABLE .'.'. IndustryService::FIELD_ID, '=', CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_SERVICE_ID)
            ->join(Industry::TABLE, Industry::TABLE .'.'. Industry::FIELD_ID, '=', IndustryService::TABLE .'.'. IndustryService::FIELD_INDUSTRY_ID)
            ->join(CompanyCampaignDeliveryModule::TABLE, CompanyCampaignDeliveryModule::TABLE .'.'. CompanyCampaignDeliveryModule::FIELD_CAMPAIGN_ID, '=', CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID)
            ->join(CompanyUser::TABLE, CompanyUser::TABLE .'.'. CompanyUser::FIELD_LEGACY_ID, '=', EloquentCompanyContact::TABLE .'.'. EloquentCompanyContact::FIELD_CONTACT_ID)
            ->leftJoin(CompanyCampaignDeliveryModuleContact::TABLE, CompanyCampaignDeliveryModuleContact::TABLE .'.'. CompanyCampaignDeliveryModuleContact::FIELD_CONTACT_ID, '=', CompanyUser::TABLE .'.'. CompanyUser::FIELD_ID)
            ->where(Industry::TABLE .'.'. Industry::FIELD_NAME, '=', 'Siding')
            ->whereNull(CompanyCampaignDeliveryModuleContact::TABLE .'.'. CompanyCampaignDeliveryModuleContact::FIELD_ID)
            ->whereColumn(CompanyUser::TABLE .'.'. CompanyUser::FIELD_COMPANY_ID, '=', CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_COMPANY_ID)
            ->distinct(EloquentCompanyContact::TABLE .'.'. EloquentCompanyContact::FIELD_CONTACT_ID)
            ->select([
                EloquentCompanyContact::TABLE .'.'. EloquentCompanyContact::FIELD_CONTACT_ID . " as legacy_contact_id",
                CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_COMPANY_ID,
                CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_NAME . " as company_campaign_name",
                CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID . " as company_campaign_id",
                CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_PRODUCT_ID,
                CompanyUser::TABLE .'.'. CompanyUser::FIELD_ID . " as company_user_id",
                CompanyCampaignDeliveryModule::TABLE .'.'. CompanyCampaignDeliveryModule::FIELD_ID . " as company_campaign_delivery_module_id",
                LeadCampaignDeliveryMethod::TABLE .'.'. LeadCampaignDeliveryMethod::FIELD_TYPE,
            ]);

        $this->total = $this->missingContactQuery->count();
        $this->missingContacts = $this->missingContactQuery->get()->toArray();
    }

    /**
     * @param array $contacts
     * @return void
     */
    protected function prepareMissingContacts(array $contacts): void
    {
        $this->insertData = [];
        $this->count = 0;

        foreach ($contacts as $contact) {
            $isAppointment = $contact->product_id === $this->appointmentProductId;
            $types = explode(',', $contact->type);
            $sms = $isAppointment
                ? in_array(LeadCampaignDeliveryMethod::TYPE_VALUE_APPT_SMS, $types)
                : in_array(LeadCampaignDeliveryMethod::TYPE_VALUE_SMS, $types);
            $email = $isAppointment
                ? in_array(LeadCampaignDeliveryMethod::TYPE_VALUE_APPT_EMAIL, $types)
                : in_array(LeadCampaignDeliveryMethod::TYPE_VALUE_EMAIL, $types);
            $active = $sms || $email;

            $this->insertData[] = [
                CompanyCampaignDeliveryModuleContact::FIELD_MODULE_ID    => $contact->company_campaign_delivery_module_id,
                CompanyCampaignDeliveryModuleContact::FIELD_CONTACT_ID   => $contact->company_user_id,
                CompanyCampaignDeliveryModuleContact::FIELD_SMS_ACTIVE   => $sms,
                CompanyCampaignDeliveryModuleContact::FIELD_EMAIL_ACTIVE => $email,
                CompanyCampaignDeliveryModuleContact::FIELD_ACTIVE       => $active,
            ];
        }
    }

    /**
     * @param array $keyedContacts
     * @return void
     */
    protected function prepareBrokenContacts(array $keyedContacts): void
    {
        $this->upsertData = [];
        $this->count = 0;

        foreach($keyedContacts as $legacyUserId => $contacts) {
            // Correct user might be on A2 with the right legacy_id
            $correctCompanyUser = DB::table(CompanyUser::TABLE)
                ->where(CompanyUser::FIELD_COMPANY_ID, $contacts[0]->campaign_company_id)
                ->where(CompanyUser::FIELD_LEGACY_ID, $legacyUserId)
                ->first();

            if (!$correctCompanyUser) {
                // Correct user might have a legacy_id mismatch but be locatable via email or name
                $legacyUser = EloquentUser::query()
                    ->find($legacyUserId);

                if ($legacyUser) {
                    $correctCompanyUser = DB::table(CompanyUser::TABLE)
                        ->where(CompanyUser::FIELD_COMPANY_ID, $contacts[0]->campaign_company_id)
                        ->where(CompanyUser::FIELD_EMAIL, $legacyUser->{EloquentUser::EMAIL})
                        ->orWhere(fn(Builder $query) => $query->where(CompanyUser::FIELD_FIRST_NAME, $legacyUser->{EloquentUser::FIRST_NAME})
                            ->where(CompanyUser::FIELD_LAST_NAME, $legacyUser->{EloquentUser::LAST_NAME})
                        )->first();
                }
            }

            if ($correctCompanyUser) {
                foreach($contacts as $contact) {
                    $this->upsertData[] = [
                        CompanyCampaignDeliveryModuleContact::FIELD_ID         => $contact->delivery_module_contact_id,
                        CompanyCampaignDeliveryModuleContact::FIELD_CONTACT_ID => $correctCompanyUser->id,
                    ];
                }
            }
            else {
                array_push($this->removeIds, ...array_map(fn($contact) => $contact->delivery_module_contact_id, $contacts));
            }
        }
    }

    /**
     * Whether or not the contact link could be repaired, these contact deliveries have either
     *  - no company user
     *  - a company user with the wrong company
     * and should be removed
     *
     * @return void
     */
    protected function cleanUpInvalidContacts(): void
    {
        $emptyContactLinks = DB::table(CompanyCampaignDeliveryModuleContact::TABLE)
            ->join(CompanyCampaignDeliveryModule::TABLE, CompanyCampaignDeliveryModule::TABLE . '.' . CompanyCampaignDeliveryModule::FIELD_ID, '=', CompanyCampaignDeliveryModuleContact::TABLE . '.' . CompanyCampaignDeliveryModuleContact::FIELD_MODULE_ID)
            ->join(CompanyCampaign::TABLE, CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID, '=', CompanyCampaignDeliveryModule::TABLE .'.'. CompanyCampaignDeliveryModule::FIELD_CAMPAIGN_ID)
            ->join(IndustryService::TABLE, IndustryService::TABLE . '.' . IndustryService::FIELD_ID, '=', CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_SERVICE_ID)
            ->join(Industry::TABLE, Industry::TABLE . '.' . Industry::FIELD_ID, '=', IndustryService::TABLE . '.' . IndustryService::FIELD_INDUSTRY_ID)
            ->leftJoin(CompanyUser::TABLE, CompanyUser::TABLE .'.'. CompanyUser::FIELD_ID, '=', CompanyCampaignDeliveryModuleContact::TABLE .'.'. CompanyCampaignDeliveryModuleContact::FIELD_CONTACT_ID)
            ->whereNull(CompanyUser::TABLE .'.'. CompanyUser::FIELD_ID)
            ->where(Industry::TABLE .'.'. Industry::FIELD_NAME, $this->industry->value)
            ->pluck(CompanyCampaignDeliveryModuleContact::TABLE .'.'. CompanyCampaignDeliveryModuleContact::FIELD_ID)
            ->toArray();

        $removeIds = [...$this->removeIds, ...$emptyContactLinks];

        $removed = DB::table(CompanyCampaignDeliveryModuleContact::TABLE)
            ->whereIn(CompanyCampaignDeliveryModuleContact::FIELD_ID, $removeIds)
            ->delete();

        print("\n" . "Removed $removed invalid campaign contacts.");
    }


    /**
     * @param string $repairType
     * @return void
     */
    protected function updateProgress(string $repairType): void
    {
        echo "{$this->count}/{$this->total} $repairType contacts updated.\r";
    }
}
