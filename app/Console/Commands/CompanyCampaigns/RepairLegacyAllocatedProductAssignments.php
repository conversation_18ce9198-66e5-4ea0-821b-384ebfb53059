<?php

namespace App\Console\Commands\CompanyCampaigns;

use App\Enums\Odin\Industry as IndustryEnum;
use Illuminate\Console\Command;

/**
 * Add missing fields (budget_id, potentially sale_type_id, quality_tier_id) to ProductAssignments
 *  generated when future-enabled industries have leads sold via Legacy Admin
 */
class RepairLegacyAllocatedProductAssignments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'repair:product-assignments {--industry=} {--timeframe=week} {--dry-run} {--batch-size=10000}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Repair ProductAssignment models generated by allocating in Legacy, by adding missing A2 fields.';

    /**
     * @return int
     */
    public function handle(): int
    {
        $dryRun = $this->option('dry-run');
        $batchSize = $this->option('batch-size');
        $timeframe = $this->option('timeframe');
        $industrySelection = $this->option('industry');

        if (!$industrySelection) {
            $industrySelection = $this->choice(
                "Choose an industry to update:",
                array_map(fn($i) => $i->value, IndustryEnum::cases()),
            );
        }

        $industry = IndustryEnum::tryFromSlug($industrySelection);
        $industry = $industry ?: IndustryEnum::tryFrom($industrySelection);

        if (!$industry) {
            $this->error("The supplied industry was invalid: $industrySelection");
            return Command::FAILURE;
        }

        if (!in_array($timeframe, ['day', 'week', 'all'])) {
            $timeframe = $this->choice("Select a timeframe:", [
                'day'   => 'Previous day',
                'week'  => 'Previous week',
                'all'   => 'No time constraint',
            ]);
        }

        $startTimestamp = match($timeframe) {
            'day'   => now()->subDay()->toDateTimeString(),
            'week'  => now()->subWeek()->toDateTimeString(),
            'all'   => null,
        };

        /** @var UpdateProductAssignmentsForLegacyIndustry $service */
        $service = app(UpdateProductAssignmentsForLegacyIndustry::class);

        $repaired = $service->repairLeadsByIndustryAndStartTime(
            $industry,
            $startTimestamp,
            $dryRun,
            $batchSize
        );

        if (!$dryRun) {
            $this->info("$repaired ProductAssignments updated.");
        }

        return Command::SUCCESS;
    }
}
