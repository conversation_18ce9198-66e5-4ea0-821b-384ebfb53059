<?php

namespace App\Console\Commands\CompanyCampaigns;

use App\Enums\Odin\Product as ProductEnum;
use App\Models\Campaigns\CompanyCampaignRelation;
use App\Models\Campaigns\CompanyCampaignSchedule;
use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyConfiguration;
use App\Models\Odin\CompanyService;
use App\Models\Odin\Industry;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ProductCampaignSchedule;
use App\Services\DatabaseHelperService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;

/**
 * This command should contain everything required to modify existing database tables for CompanyCampaign requirements
 *      it will likely need significant additions to handle Solar and Roofing migrations
 */
class CompanyCampaignsPostMigration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'post-migration:campaigns {--industry=} {--batch-size=10000} {--leads-only}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'A collection of scripts to be run after the primary migration has been run, including modifying existing data';

    /**
     * Line descriptions of each task performed here
     *
     * @var string[]
     */
    protected array $subCommandDescriptions = [
        'Update Product Assignments budget_id for CompanyCampaign link, add any missing quality_tier_id and sale_type_id',
        'Attempt to repair broken campaign contact deliveries, then remove invalid ones (from company/legacy-company mismatches or empty contact links)',
        'Add the default IndustryService to any Company missing it, if one is set for the Industry (Roofing => Roof Replacement)',
        "Link a company's schedules to their new CompanyCampaigns if using scheduling/appointments"
    ];

    /**
     * @return int
     */
    public function handle(): int
    {
        $this->info("\n\nThis script will perform the following tasks:");
        $this->info(collect($this->subCommandDescriptions)
            ->map(fn($v) => "\t - " . $v)
            ->join("\n"));
        $this->line('');

        $industrySelection = $this->option('industry');
        $leadsOnly = $this->option('leads-only');

        if (!$industrySelection) {
            $industrySelection = $this->choice(
                "Choose an industry to update:",
                array_map(fn($i) => $i->value, IndustryEnum::cases()),
            );
        }

        $industry = IndustryEnum::tryFromSlug($industrySelection);
        $industry = $industry ?: IndustryEnum::tryFrom($industrySelection);

        if (!$industry) {
            $this->error("The supplied industry was invalid: $industrySelection");
            return Command::FAILURE;
        }

        $industryId = Industry::query()
            ->where(Industry::FIELD_NAME, $industry->value)
            ->firstOrFail()
            ->id;

        $campaignExists = CompanyCampaign::query()
            ->join(IndustryService::TABLE, IndustryService::TABLE .'.'. IndustryService::FIELD_ID, '=', CompanyCampaign::FIELD_SERVICE_ID)
            ->where(IndustryService::FIELD_INDUSTRY_ID, $industryId)
            ->exists();

        if (!$campaignExists) {
            $this->warn("\nThere don't appear to be any CompanyCampaigns for '$industry->value'.");
            $continue = $this->confirm("\nDo you still wish to run the post-migration script? This is not recommended.");

            if (!$continue)
                return Command::FAILURE;
        }

        $batchSize = max(intval($this->option('batch-size')), 1000);

        if ($this->isMultiIndustry($industry)) {
            /** @var UpdateProductAssignmentsForMultiIndustry $productAssigmentUpdater */
            $productAssigmentUpdater = app(UpdateProductAssignmentsForMultiIndustry::class);
            $productAssigmentUpdater->updateBudgetIdOnProductAssignments($industry);
        }
        else {
            /** @var UpdateProductAssignmentsForLegacyIndustry $productAssigmentUpdater */
            $productAssigmentUpdater = app(UpdateProductAssignmentsForLegacyIndustry::class);
            $productAssigmentUpdater->updateBudgetIdOnProductAssignments($industry, $batchSize);
        }

        if ($leadsOnly)
            return Command::SUCCESS;

        /** @var RepairBrokenCompanyCampaignContacts $contactRepairer */
        $contactRepairer = app(RepairBrokenCompanyCampaignContacts::class);
        $contactRepairer->findAndRepairContacts($industry);

        $this->addDefaultServiceToCompanies($industry, $batchSize);

        if (!$this->populateCampaignScheduleTable($industryId)) {
            $this->error("Failed to migrate schedule links to company campaigns");
        }

        $this->info("\nPost-migration tasks complete.\n");
        return Command::SUCCESS;
    }

    /**
     * @param IndustryEnum $industry
     * @param int $batchSize
     * @return void
     */
    protected function addDefaultServiceToCompanies(IndustryEnum $industry, int $batchSize): void
    {
        $defaultServices = [
            IndustryEnum::ROOFING->value => IndustryService::query()
                ->where(IndustryService::FIELD_SLUG, 'roof-replacement')
                ->first()
                ?->id ?? null,
            IndustryEnum::SOLAR->value => IndustryService::query()
                    ->where(IndustryService::FIELD_SLUG, 'solar-installation')
                    ->first()
                    ?->id ?? null,
        ];

        $defaultServiceId = $defaultServices[$industry->value] ?? null;
        if (!$defaultServiceId) {
            $this->line('No default service for ' . $industry->value . ', skipping company services update.');
            return;
        }
        $industryId = IndustryService::query()->find($defaultServiceId)
            ->industry
            ->id;

        $companyIds = DB::table(CompanyCampaign::TABLE)
            ->distinct(Company::TABLE .'.'. Company::FIELD_ID)
            ->select(Company::TABLE .'.'. Company::FIELD_ID)
            ->join(IndustryService::TABLE, IndustryService::TABLE .'.'. IndustryService::FIELD_ID, '=', CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_SERVICE_ID)
            ->join(Company::TABLE, Company::TABLE .'.'. Company::FIELD_ID, '=', CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_COMPANY_ID)
            ->leftJoin(CompanyService::TABLE, fn(JoinClause $join) =>
                $join->on(CompanyService::TABLE .'.'. CompanyService::FIELD_COMPANY_ID, '=', Company::TABLE .'.'. Company::FIELD_ID)
                    ->where(CompanyService::TABLE .'.'. CompanyService::FIELD_INDUSTRY_SERVICE_ID, $defaultServiceId)
            )->where(IndustryService::TABLE .'.'. IndustryService::FIELD_INDUSTRY_ID, $industryId)
            ->whereNull(CompanyService::TABLE .'.'. CompanyService::FIELD_ID)
            ->pluck(Company::FIELD_ID)
            ->toArray();

        $count = count($companyIds);

        $this->line("Found $count companies missing default service for $industry->value.");
        if (!$count) return;

        DB::beginTransaction();

        $insertData = [];

        try {
            foreach($companyIds as $companyId) {
                $insertData[] = [
                    CompanyService::FIELD_COMPANY_ID          => $companyId,
                    CompanyService::FIELD_INDUSTRY_SERVICE_ID => $defaultServiceId,
                ];

                if (count($insertData) > $batchSize) {
                    DB::table(CompanyService::TABLE)
                        ->insert($insertData);
                    $insertData = [];
                }
            }

            if (count($insertData)) {
                DB::table(CompanyService::TABLE)
                    ->insert($insertData);
            }
        }
        catch (Exception $e) {
            $this->error($e->getMessage());
            DB::rollBack();

            return;
        }

        DB::commit();
    }

    /**
     * @param int $industryId
     * @return bool
     */
    private function populateCampaignScheduleTable(int $industryId): bool
    {
        $appointmentProductId = Product::query()
            ->where(Product::FIELD_NAME, ProductEnum::APPOINTMENT->value)
            ->first()->id;

        $campaignScheduleData = DB::table(CompanyCampaign::TABLE)
            ->select([
                CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID . ' as company_campaign_id',
                ProductCampaignSchedule::TABLE .'.'. ProductCampaignSchedule::FIELD_SCHEDULE_ID,
            ])->join(CompanyCampaignRelation::TABLE, CompanyCampaignRelation::TABLE .'.'. CompanyCampaignRelation::FIELD_COMPANY_CAMPAIGN_ID, '=', CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID)
            ->join(DatabaseHelperService::readOnlyDatabase() .'.'. LeadCampaign::TABLE, LeadCampaign::TABLE .'.'. LeadCampaign::ID, '=', CompanyCampaignRelation::TABLE .'.'. CompanyCampaignRelation::FIELD_RELATION_ID)
            ->join(ProductCampaign::TABLE, ProductCampaign::TABLE .'.'. ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID, '=', LeadCampaign::TABLE .'.'. LeadCampaign::ID)
            ->join(ProductCampaignSchedule::TABLE, ProductCampaignSchedule::TABLE .'.'. ProductCampaignSchedule::FIELD_PRODUCT_CAMPAIGN_ID, '=', ProductCampaign::TABLE .'.'. ProductCampaign::FIELD_ID)
            ->join(IndustryService::TABLE, IndustryService::TABLE .'.'. IndustryService::FIELD_ID, '=', CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_SERVICE_ID)
            ->where(IndustryService::TABLE .'.'. IndustryService::FIELD_INDUSTRY_ID, $industryId)
            ->where(ProductCampaignSchedule::TABLE .'.'. ProductCampaignSchedule::FIELD_SCHEDULE_ID, '>', 0)
            ->where(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_PRODUCT_ID, $appointmentProductId)
            ->get()
            ->reduce(fn($output, $campaign) =>
                [
                    ...$output,
                    [
                        CompanyCampaignSchedule::FIELD_COMPANY_CAMPAIGN_ID => $campaign->company_campaign_id,
                        CompanyCampaignSchedule::FIELD_SCHEDULE_ID         => $campaign->schedule_id,
                    ]
                ]
            , []);

        if (!count($campaignScheduleData)) {
            $this->line("No campaign schedules found for this industry.");
            return true;
        }

        try {
            DB::table(CompanyCampaignSchedule::TABLE)
                ->upsert(
                    $campaignScheduleData,
                    [CompanyCampaignSchedule::FIELD_COMPANY_CAMPAIGN_ID, CompanyCampaignSchedule::FIELD_SCHEDULE_ID]
                );

            $this->line(count($campaignScheduleData) . " campaign schedules were processed.");

            return true;
        }
        catch(Exception $e) {
            $this->error($e->getMessage());
        }

        return false;
    }

    /**
     * @param IndustryEnum $industry
     * @return bool
     */
    private function isMultiIndustry(IndustryEnum $industry): bool
    {
        return !in_array($industry, [IndustryEnum::ROOFING, IndustryEnum::SOLAR]);
    }
}
