<?php

namespace App\Console\Commands\CompanyCampaigns;

use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\Product as ProductEnum;
use App\Enums\Odin\QualityTier as QualityTierEnum;
use App\Enums\Odin\SaleTypes;
use App\Models\Legacy\LeadCategory;
use App\Models\Legacy\LeadPrice;
use App\Models\Legacy\LeadSalesType;
use App\Models\Legacy\LeadType;
use App\Models\Legacy\Location;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ProductCountyFloorPrice;
use App\Models\Odin\ProductStateFloorPrice;
use App\Models\Odin\PropertyType;
use App\Models\Odin\QualityTier;
use App\Models\Odin\ServiceProduct;
use App\Models\SaleType;
use App\Services\DatabaseHelperService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use stdClass;

class MigrateLegacyFloorPricing extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'legacy-migrate:floor-pricing {--batch-size=2500}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate legacy floor pricing for a legacy industry.';

    protected int $batchSize = 1000;

    protected array $validLeadSaleTypes = [];
    protected array $validLeadQualityTiers = [];
    protected array $validAppointmentSaleTypes = [];
    protected array $validAppointmentQualityTiers = [];

    protected array $qualityTierMapping = [];
    protected array $saleTypeMapping = [];
    protected array $propertyTypeMapping = [];

    protected array $leadServiceProductIds = [];
    protected array $appointmentServiceProductIds = [];

    protected int $standardQualityTierId;

    protected int $stateCount = 0;
    protected int $stateTotal = 0;
    protected int $countyCount = 0;
    protected int $countyTotal = 0;

    protected array $appointmentFormulaMapping = [];

    /**
     * @return int
     * @throws Exception
     */
    public function handle(): int
    {
        $validIndustries = [IndustryEnum::SOLAR, IndustryEnum::ROOFING];
        $this->batchSize = (int) $this->option('batch-size') ?: $this->batchSize;

        $industryOptions = array_map(fn($i) => $i->value, $validIndustries);

        $industrySelection = $this->choice(
            "Choose an industry to migrate",
            $industryOptions
        );

        $industryEnum = IndustryEnum::from($industrySelection);
        $industrySlug = $industryEnum->getSlug();
        $leadProductId = Product::query()
            ->where(Product::FIELD_NAME, ProductEnum::LEAD->value)
            ->firstOrFail()->id;
        $appointmentProductId = Product::query()
            ->where(Product::FIELD_NAME, ProductEnum::APPOINTMENT->value)
            ->firstOrFail()->id;

        DB::table(Industry::TABLE)
            ->select([ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_ID, ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_PRODUCT_ID])
            ->join(IndustryService::TABLE, IndustryService::TABLE .'.'. IndustryService::FIELD_INDUSTRY_ID, '=', Industry::TABLE .'.'. Industry::FIELD_ID)
            ->join(ServiceProduct::TABLE, ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, '=', IndustryService::TABLE .'.'. IndustryService::FIELD_ID)
            ->where(Industry::TABLE .'.'. Industry::FIELD_SLUG, $industrySlug)
            ->get()
            ->each(function(stdClass $serviceProduct) use ($leadProductId, $appointmentProductId) {
                if ($serviceProduct->product_id === $leadProductId)
                    $this->leadServiceProductIds[] = $serviceProduct->id;
                else if ($serviceProduct->product_id === $appointmentProductId)
                    $this->appointmentServiceProductIds[] = $serviceProduct->id;
            });

        $this->validLeadSaleTypes    = SaleTypes::byProductAndIndustry(ProductEnum::LEAD, $industryEnum, SaleTypes::RETURN_TYPE_NAME);
        $this->validLeadQualityTiers = QualityTierEnum::byProductAndIndustry(ProductEnum::LEAD, $industryEnum);
        $this->validAppointmentSaleTypes    = SaleTypes::byProductAndIndustry(ProductEnum::APPOINTMENT, $industryEnum, SaleTypes::RETURN_TYPE_NAME);
        $this->validAppointmentQualityTiers = QualityTierEnum::byProductAndIndustry(ProductEnum::APPOINTMENT, $industryEnum);

        $this->standardQualityTierId = QualityTier::query()
            ->where(QualityTier::FIELD_NAME, QualityTierEnum::STANDARD->value)
            ->firstOrFail()
            ->id;

        $this->stateTotal = Location::query()->where(Location::TYPE, Location::TYPE_STATE)->count();

        $this->createLegacyMappings();

        $this->createFormulaMapping();

        // Legacy industries are not using this table yet, so clear it out first
        $this->line("Clearing prices for $industrySelection...");
        $this->deletePricesForIndustry();

        $this->info("\nMigrating pricing for " . count($this->leadServiceProductIds) . " services in $industrySelection...");

        if (!$this->migrateStateLeadPrices($industrySlug)) {
            $this->error("Failed to migrate State Lead prices");

            return Command::FAILURE;
        }

        if (!$this->migrateCountyLeadPrices($industrySlug)) {
            $this->error("Failed to migrate County Lead prices");

            return Command::FAILURE;
        }

        if (!$this->migrateStateAppointmentPrices($industrySlug)) {
            $this->error("Failed to migrate State Appointment prices");

            return Command::FAILURE;
        }

        if (!$this->migrateCountyAppointmentPrices($industrySlug)) {
            $this->error("Failed to migrate County Appointment prices");

            return Command::FAILURE;
        }

        $this->info("\nFinished migrating pricing for $industrySelection.\n");

        return Command::SUCCESS;
    }

    /**
     *
     * @param string $industrySlug
     * @return bool
     */
    private function migrateCountyLeadPrices(string $industrySlug): bool
    {
        $this->info("\nMigrating County floor prices...");
        $stateIdMap = DB::table(Location::TABLE)
            ->where(Location::TYPE, Location::TYPE_STATE)
            ->get()
            ->reduce(function($output, $location) {
                $output[$location->state_abbr] = $location->id;
                return $output;
            }, []);

        $countyQuery = DB::table(DatabaseHelperService::readOnlyDatabase() .'.'. LeadPrice::TABLE)
            ->join(Location::TABLE, Location::TABLE .'.'. Location::ID, '=', LeadPrice::TABLE .'.'. LeadPrice::LOCATION_ID)
            ->where(Location::TABLE .'.'. Location::TYPE, Location::TYPE_COUNTY)
            ->where(LeadPrice::LEAD_INDUSTRY, $industrySlug)
            ->where(LeadPrice::COMPANY_ID, 1)
            ->where(LeadPrice::PRICE_TYPE, '!=', LeadPrice::PRICE_TYPE_BID)
            ->whereIn(LeadPrice::LEAD_CATEGORY_ID, array_keys($this->propertyTypeMapping))
            ->whereIn(LeadPrice::LEAD_SALES_TYPE_ID, array_keys($this->saleTypeMapping))
            ->select([
                Location::TABLE .'.'. Location::STATE_ABBREVIATION,
                LeadPrice::TABLE .'.*',
            ]);

        $this->countyTotal = $countyQuery->count();
        $this->updateProgress($this->countyCount, $this->countyTotal, 'counties');

        DB::beginTransaction();

        $countyQuery->orderBy(LeadPrice::TABLE .'.'. LeadPrice::ID);

        $insertData = [];

        try {
            $countyQuery->chunkById($this->batchSize, function (Collection $chunk) use (&$statePrices, &$stateIdMap, &$insertData, &$priceCount) {
                foreach ($chunk as $floorPrice) {
                    foreach ($this->leadServiceProductIds as $serviceProductId) {
                        $propertyType = $this->propertyTypeMapping[$floorPrice->{LeadPrice::LEAD_CATEGORY_ID}] ?? null;
                        $saleType     = $this->saleTypeMapping[$floorPrice->{LeadPrice::LEAD_SALES_TYPE_ID}] ?? null;
                        $qualityTier  = $this->qualityTierMapping[$floorPrice->{LeadPrice::LEAD_TYPE_ID}] ?? null;

                        if (!$propertyType || !$saleType || !$qualityTier) continue;

                        $insertData[] = [
                            ProductCountyFloorPrice::FIELD_COUNTY_LOCATION_ID => $floorPrice->{LeadPrice::LOCATION_ID},
                            ProductCountyFloorPrice::FIELD_STATE_LOCATION_ID  => $stateIdMap[$floorPrice->{Location::STATE_ABBREVIATION}],
                            ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID => $serviceProductId,
                            ProductCountyFloorPrice::FIELD_SALE_TYPE_ID       => $saleType,
                            ProductCountyFloorPrice::FIELD_QUALITY_TIER_ID    => $qualityTier,
                            ProductCountyFloorPrice::FIELD_PROPERTY_TYPE_ID   => $propertyType,
                            ProductCountyFloorPrice::FIELD_PRICE              => $floorPrice->{LeadPrice::PRICE}
                        ];
                    }

                }

                $this->insertData(ProductCountyFloorPrice::TABLE, $insertData);

                $priceCount        = $chunk->count();
                $this->countyCount += $priceCount;
                $this->updateProgress($this->countyCount, $this->countyTotal, 'county prices');

                return true;
            });
        }
        catch (Exception $e) {
            $this->error($e->getMessage());
        }

        $this->updateProgress($this->countyCount, $this->countyTotal, 'county prices');
        $this->line("\n");

        DB::commit();

        return true;
    }

    /**
     * @param string $industrySlug
     * @return bool
     */
    private function migrateStateLeadPrices(string $industrySlug): bool
    {
        $this->info("\nMigrating State floor prices");

        $allStatePrices = DB::table(DatabaseHelperService::readOnlyDatabase() .'.'. LeadPrice::TABLE)
            ->join(Location::TABLE, Location::TABLE .'.'. Location::ID, '=', LeadPrice::LOCATION_ID)
            ->select(LeadPrice::TABLE .'.*')
            ->where(LeadPrice::PRICE_TYPE, LeadPrice::PRICE_TYPE_FLOOR)
            ->where(LeadPrice::COMPANY_ID, 1)
            ->where(Location::TYPE, Location::TYPE_STATE)
            ->where(LeadPrice::LEAD_INDUSTRY, $industrySlug)
            ->whereIn(LeadPrice::LEAD_CATEGORY_ID, array_keys($this->propertyTypeMapping))
            ->whereIn(LeadPrice::LEAD_SALES_TYPE_ID, array_keys($this->saleTypeMapping))
            ->groupBy([
                LeadPrice::LOCATION_ID,
                LeadPrice::LEAD_SALES_TYPE_ID,
                LeadPrice::LEAD_CATEGORY_ID,
                LeadPrice::LEAD_TYPE_ID,
            ])
            ->orderBy(LeadPrice::TABLE .'.'. LeadPrice::ID)
            ->get()
            ->groupBy(LeadPrice::LOCATION_ID);

        $insertData = [];

        DB::beginTransaction();

        foreach ($allStatePrices as $stateLocationId => $statePrices) {
            foreach ($this->leadServiceProductIds as $serviceProductId) {
                foreach ($statePrices as $price) {
                    $insertData[] = [
                        ProductStateFloorPrice::FIELD_STATE_LOCATION_ID  => $price->{LeadPrice::LOCATION_ID},
                        ProductStateFloorPrice::FIELD_SALE_TYPE_ID       => $this->saleTypeMapping[$price->{LeadPrice::LEAD_SALES_TYPE_ID}],
                        ProductStateFloorPrice::FIELD_QUALITY_TIER_ID    => $this->qualityTierMapping[$price->{LeadPrice::LEAD_TYPE_ID}],
                        ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID   => $this->propertyTypeMapping[$price->{LeadPrice::LEAD_CATEGORY_ID}],
                        ProductStateFloorPrice::FIELD_PRICE              => $price->{LeadPrice::PRICE},
                        ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID => $serviceProductId,
                    ];
                }

                if (count($insertData) > $this->batchSize) {
                    if ($this->insertData(ProductStateFloorPrice::TABLE, $insertData)) {
                        $this->updateProgress($this->stateCount, $this->stateTotal, 'states');
                    }
                    else {
                        DB::rollBack();
                        $this->error("Failed to insert records'");

                        return false;
                    }
                }
            }

            $this->stateCount ++;
        }

        if ($insertData) {
            if (!$this->insertData(ProductStateFloorPrice::TABLE, $insertData)) {
                DB::rollBack();
                $this->error("Failed to insert records'");

                return false;
            }
        }
        $this->updateProgress($this->stateCount, $this->stateTotal, 'states');

        DB::commit();

        return true;
    }

    /**
     * @return bool
     */
    public function migrateStateAppointmentPrices(): bool
    {
        $this->info("\nMigrating State appointment prices");

        $stateAppointmentPrices = DB::table(ProductStateFloorPrice::TABLE)
            ->select([
                ProductStateFloorPrice::FIELD_STATE_LOCATION_ID,
                ProductStateFloorPrice::FIELD_SALE_TYPE_ID,
                ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID,
                ProductStateFloorPrice::FIELD_PRICE,
            ])->where(ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID, $this->leadServiceProductIds[0])
            ->where(ProductStateFloorPrice::FIELD_QUALITY_TIER_ID, $this->standardQualityTierId)
            ->get()
            ->toArray();

        DB::beginTransaction();

        try {
            foreach ($this->appointmentServiceProductIds as $index => $serviceProductId) {
                foreach ($this->validAppointmentQualityTiers as $qualityTier) {
                    $qualityTierId = QualityTier::query()
                        ->where(QualityTier::FIELD_NAME, $qualityTier)
                        ->firstOrFail()
                        ->id;

                    $insertData = array_map(fn($price) => [
                        ProductStateFloorPrice::FIELD_STATE_LOCATION_ID  => $price->state_location_id,
                        ProductStateFloorPrice::FIELD_SALE_TYPE_ID       => $price->sale_type_id,
                        ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID   => $price->property_type_id,
                        ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID => $serviceProductId,
                        ProductStateFloorPrice::FIELD_QUALITY_TIER_ID    => $qualityTierId,
                        ProductStateFloorPrice::FIELD_PRICE              => $this->calculateAppointmentPrice($qualityTierId, $price->price),
                    ], $stateAppointmentPrices);

                    DB::table(ProductStateFloorPrice::TABLE)
                        ->insert($insertData);

                }

                $this->updateProgress($index + 1, count($this->appointmentServiceProductIds), 'services');
            }
        }
        catch(Exception $e) {
            $this->error("Failed to insert appointment prices. " . $e->getMessage());
            DB::rollBack();

            return false;
        }

        DB::commit();
        $this->line("State appointment prices updated.");

        return true;
    }

    /**
     * @return bool
     */
    public function migrateCountyAppointmentPrices(): bool
    {
        $this->info("\nMigrating County appointment prices");

        $countyAppointmentPrices = DB::table(ProductCountyFloorPrice::TABLE)
            ->select([
                ProductCountyFloorPrice::FIELD_STATE_LOCATION_ID,
                ProductCountyFloorPrice::FIELD_COUNTY_LOCATION_ID,
                ProductCountyFloorPrice::FIELD_SALE_TYPE_ID,
                ProductCountyFloorPrice::FIELD_PROPERTY_TYPE_ID,
                ProductCountyFloorPrice::FIELD_PRICE,
            ])->where(ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID, $this->leadServiceProductIds[0])
            ->where(ProductCountyFloorPrice::FIELD_QUALITY_TIER_ID, $this->standardQualityTierId)
            ->get()
            ->toArray();

        try {
            foreach ($this->appointmentServiceProductIds as $index => $serviceProductId) {
                foreach ($this->validAppointmentQualityTiers as $qualityTier) {
                    $qualityTierId = QualityTier::query()
                        ->where(QualityTier::FIELD_NAME, $qualityTier)
                        ->firstOrFail()
                        ->id;

                    $insertData = array_map(fn($price) => [
                        ProductCountyFloorPrice::FIELD_STATE_LOCATION_ID  => $price->state_location_id,
                        ProductCountyFloorPrice::FIELD_COUNTY_LOCATION_ID => $price->county_location_id,
                        ProductCountyFloorPrice::FIELD_SALE_TYPE_ID       => $price->sale_type_id,
                        ProductCountyFloorPrice::FIELD_PROPERTY_TYPE_ID   => $price->property_type_id,
                        ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID => $serviceProductId,
                        ProductCountyFloorPrice::FIELD_QUALITY_TIER_ID    => $qualityTierId,
                        ProductCountyFloorPrice::FIELD_PRICE              => $this->calculateAppointmentPrice($qualityTierId, $price->price),
                    ], $countyAppointmentPrices);

                    DB::table(ProductCountyFloorPrice::TABLE)
                        ->insert($insertData);
                }

                $this->updateProgress($index + 1, count($this->appointmentServiceProductIds), 'services');
            }
        }
        catch(Exception $e) {
            $this->error("Failed to insert appointment prices. " . $e->getMessage());
            DB::rollBack();

            return false;
        }

        DB::commit();
        $this->line("County appointment prices updated.");

        return true;
    }

    /**
     * @param int $qualityTierId
     * @param int $price
     * @return float
     */
    private function calculateAppointmentPrice(int $qualityTierId, int $price): float
    {
        $multiplier = $this->appointmentFormulaMapping[$qualityTierId] ?? 2;

        return $multiplier * $price;
    }

    /**
     * @param string $table
     * @param array $insertData
     * @return bool
     */
    private function insertData(string $table, array &$insertData): bool
    {
        if (DB::table($table)->insert($insertData)) {
            $insertData = [];
            return true;
        }
        else
            return false;
    }

    /**
     * @param int $count
     * @param int $total
     * @param string $targetType
     * @return void
     */
    private function updateProgress(int $count, int $total, string $targetType): void
    {
        echo "$count/$total $targetType updated.\r";
    }

    /**
     * @return void
     */
    private function createLegacyMappings(): void
    {
        LeadType::all()->each(function(LeadType $qualityTier) {
            if (in_array($qualityTier->name, $this->validLeadQualityTiers)) {
                $this->qualityTierMapping[$qualityTier->{LeadType::ID}] = QualityTier::query()
                    ->where(QualityTier::FIELD_NAME, $qualityTier->name)
                    ->firstOrFail()
                    ->id;
            }
        });

        LeadCategory::all()->each(function(LeadCategory $propertyType) {
            $odinPropertyTypeId = PropertyType::query()
                ->where(PropertyType::FIELD_NAME, $propertyType->name)
                ->first()
                ?->id ?? null;
            if ($odinPropertyTypeId) $this->propertyTypeMapping[$propertyType->id] = $odinPropertyTypeId;
        });

        LeadSalesType::all()->each(function(LeadSalesType $salesType) {
            if (in_array($salesType->name, $this->validLeadSaleTypes)) {
                $this->saleTypeMapping[$salesType->id] = SaleType::query()
                    ->where(SaleType::FIELD_NAME, $salesType->name)
                    ->firstOrFail()
                    ->id;
            }
        });
    }

    /**
     * @param array $targetArray
     * @param array $intermediateKeys
     * @param string $targetProperty
     * @return mixed
     */
    private function getNestedValue(array $targetArray, array $intermediateKeys, string $targetProperty): mixed
    {
        foreach ($intermediateKeys as $key) {
            if (array_key_exists($key, $targetArray)) {
                $targetArray = $targetArray[$key];
            } else
                return null;
        }

        return ($targetArray instanceof stdClass)
            ? $targetArray->{$targetProperty}
            : $targetArray[$targetProperty] ?? null;
    }

    /**
     * @return void
     */
    private function createFormulaMapping(): void
    {
        $inHomeId = QualityTier::query()->where(QualityTier::FIELD_NAME, QualityTierEnum::IN_HOME)->first()->id;
        $onlineId = QualityTier::query()->where(QualityTier::FIELD_NAME, QualityTierEnum::ONLINE)->first()->id;
        $this->appointmentFormulaMapping = [
            $inHomeId => 3,
            $onlineId => 2,
        ];
    }

    /**
     * @return void
     */
    private function deletePricesForIndustry(): void
    {
        DB::table(ProductStateFloorPrice::TABLE)
            ->whereIn(ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID, [...$this->leadServiceProductIds, ...$this->appointmentServiceProductIds])
            ->delete();
        DB::table(ProductCountyFloorPrice::TABLE)
            ->whereIn(ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID, [...$this->leadServiceProductIds, ...$this->appointmentServiceProductIds])
            ->delete();
    }
}
