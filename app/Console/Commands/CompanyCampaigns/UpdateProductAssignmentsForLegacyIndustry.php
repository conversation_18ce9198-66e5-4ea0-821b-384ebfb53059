<?php

namespace App\Console\Commands\CompanyCampaigns;

use App\Enums\Odin\BudgetCategory;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\QualityTier as QualityTierEnum;
use App\Enums\Odin\SaleTypes;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\CompanyCampaignRelation;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ProductCampaignBudget;
use App\Models\Odin\QualityTier;
use App\Models\Odin\ServiceProduct;
use App\Models\SaleType;
use App\Services\DatabaseHelperService;
use Exception;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use App\Enums\Odin\Product as ProductEnum;

class UpdateProductAssignmentsForLegacyIndustry
{
    protected int $industryId;

    protected array $campaignBudgetMap = [];
    protected array $leadBudgetKeyMap = [];
    protected array $legacyBudgetKeyMap = [
        1 => BudgetCategory::VERIFIED->value,
        2 => BudgetCategory::VERIFIED->value,
        3 => BudgetCategory::VERIFIED->value,
        4 => BudgetCategory::VERIFIED->value,
        5 => BudgetCategory::UNVERIFIED->value,
        6 => BudgetCategory::EMAIL_ONLY->value,
    ];

    protected array $legacySaleTypeIdMap = [
        0 => 0,
    ];

    protected array $qualityTierIdMap = [];
    protected int $standardQualityId;

    protected int $leadProductId;
    protected int $appointmentProductId;

    protected Builder $appointmentProductAssignments;
    protected Builder $leadProductAssignments;
    protected array $upsertData = [];

    protected bool $ignoreAppointments = false;
    protected ?string $startDateTime = null;
    protected bool $dryRun = false;

    protected int $chunkSize;

    protected int $processed = 0;
    protected int $updated = 0;
    protected int $total = 0;

    /**
     * @param IndustryEnum $industry
     * @param string|null $startTime
     * @param bool $dryRun
     * @param int $chunkSize
     * @return int
     */
    public function repairLeadsByIndustryAndStartTime(IndustryEnum $industry, string $startTime = null, bool $dryRun = false, int $chunkSize = 10000): int
    {
        $this->ignoreAppointments = true;
        $this->startDateTime = $startTime;
        $this->dryRun = $dryRun;

        return $this->updateBudgetIdOnProductAssignments($industry, $chunkSize);
    }

    /**
     * Attach ProductAssignments in a given Industry to a CompanyCampaign Budget
     *
     * @param IndustryEnum $industry
     * @param int $chunkSize
     * @return int - number of rows upserted
     */
    public function updateBudgetIdOnProductAssignments(IndustryEnum $industry, int $chunkSize = 10000): int
    {
        $this->chunkSize = $chunkSize;
        $this->industryId = Industry::query()
            ->where(Industry::FIELD_SLUG, $industry->getSlug())
            ->first()
            ->id;

        SaleType::all()
            ->each(function(SaleType $saleType) {
                $key = $this->getBudgetKeyForSaleType($saleType->name);
                if ($key) {
                    $this->leadBudgetKeyMap[$saleType->id] = $key;
                }
                $saleTypeEnum = SaleTypes::tryFrom($saleType->name);
                if ($saleTypeEnum) {
                    $legacyId = SaleTypes::mapSaleTypeToLegacyId($saleTypeEnum);
                    $this->legacySaleTypeIdMap[$legacyId] = $saleType->id;
                }
            });

        QualityTier::all()
            ->each(function(QualityTier $qualityTier) {
                $mappedKey = $this->getMappedQualityTierKey($qualityTier->name);
                if ($mappedKey)
                    $this->qualityTierIdMap[$mappedKey] = $qualityTier->id;
                if ($qualityTier->name === QualityTierEnum::STANDARD->value)
                    $this->standardQualityId = $qualityTier->id;
            });


        $this->leadProductId = Product::query()
            ->where(Product::FIELD_NAME, ProductEnum::LEAD)
            ->firstOrFail()
            ->id;

        $this->appointmentProductId = Product::query()
            ->where(Product::FIELD_NAME, ProductEnum::APPOINTMENT)
            ->firstOrFail()
            ->id;

        $this->getProductAssignments();

        $this->total = $this->leadProductAssignments->count() + $this->appointmentProductAssignments->count();

        if ($this->total === 0 && $this->dryRun)
            return $this->displayDryRunData();

        if (!$this->dryRun)
            $this->updateProgress();

        // Appointments
        if (!$this->ignoreAppointments) {
            $this->mapCampaignsToBudgetsForAppointments();

            $this->appointmentProductAssignments->chunkById($this->chunkSize, function (Collection $chunk) {
                $this->prepareUpsertDataForAppointments($chunk);

                if ($this->dryRun) {
                    $this->displayDryRunData();
                }
                else {
                    if ($this->upsertData) {
                        $updated = ProductAssignment::query()
                            ->upsert($this->upsertData, ProductAssignment::FIELD_ID, [
                                ProductAssignment::FIELD_BUDGET_ID,
                                ProductAssignment::FIELD_QUALITY_TIER_ID,
                            ]);

                        $this->updated += ($updated ?? 0) / 2;

                    }
                }

                $this->processed += $chunk->count();
                $this->updateProgress();
            });
        }

        // Leads
        $this->mapCampaignsToBudgetForLeads();

        $this->leadProductAssignments->chunkById($this->chunkSize, function (Collection $chunk) {
            $this->prepareUpsertDataForLeads($chunk);

            if ($this->dryRun) {
                $this->displayDryRunData();
            }
            else {
                if ($this->upsertData) {
                     $updated = ProductAssignment::query()
                        ->upsert($this->upsertData, ProductAssignment::FIELD_ID, [
                            ProductAssignment::FIELD_BUDGET_ID,
                            ProductAssignment::FIELD_QUALITY_TIER_ID,
                            ProductAssignment::FIELD_SALE_TYPE_ID,
                        ]);

                    $this->updated += ($updated ?? 0) / 2;
                }
            }

            $this->processed += $chunk->count();
            $this->updateProgress();
        }, 'product_assignments.id', 'id');

        print("\n\n$this->total records processed.");
        print("\n$this->updated records updated.");
        print("\n" . $this->total - $this->updated . " records could not be updated.\n");

        return $this->updated;
    }

    /**
     * @param Collection $chunk
     * @return void
     */
    protected function prepareUpsertDataForAppointments(Collection $chunk): void
    {
        $this->upsertData = [];
        $productAssignmentUpdateData = [];

        foreach ($chunk as $productAssignment) {
            if (array_key_exists($productAssignment->{ProductAssignment::FIELD_PRODUCT_CAMPAIGN_BUDGET_ID}, $this->campaignBudgetMap)) {
                if (array_key_exists($productAssignment->{ProductCampaignBudget::FIELD_QUALITY_TIER}, $this->campaignBudgetMap[$productAssignment->{ProductAssignment::FIELD_PRODUCT_CAMPAIGN_BUDGET_ID}])) {
                    $productAssignmentUpdateData[] = [
                        ProductAssignment::FIELD_ID              => $productAssignment->{ProductAssignment::FIELD_ID},
                        ProductAssignment::FIELD_BUDGET_ID       => $this->campaignBudgetMap[$productAssignment->{ProductAssignment::FIELD_PRODUCT_CAMPAIGN_BUDGET_ID}][$productAssignment->{ProductCampaignBudget::FIELD_QUALITY_TIER}],
                        ProductAssignment::FIELD_QUALITY_TIER_ID => $this->qualityTierIdMap[$productAssignment->{ProductCampaignBudget::FIELD_QUALITY_TIER}] ?? 0,
                    ];
                }
            }
        }

        $this->upsertData = $productAssignmentUpdateData;
    }

    /**
     * @param Collection $chunk
     * @return void
     */
    protected function prepareUpsertDataForLeads(Collection $chunk): void
    {
        $this->upsertData = [];
        $productAssignmentUpdateData = [];

        foreach ($chunk as $productAssignment) {
            if (array_key_exists($productAssignment->{LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID}, $this->campaignBudgetMap)) {
                $targetBudgetKey = $this->leadBudgetKeyMap[$productAssignment->{ProductAssignment::FIELD_SALE_TYPE_ID}] ?? $this->legacyBudgetKeyMap[$productAssignment->{LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID}] ?? null;
                if (array_key_exists($targetBudgetKey, $this->campaignBudgetMap[$productAssignment->{LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID}])) {
                    $productAssignmentUpdateData[] = [
                        ProductAssignment::FIELD_ID              => $productAssignment->{ProductAssignment::FIELD_ID},
                        ProductAssignment::FIELD_BUDGET_ID       => $this->campaignBudgetMap[$productAssignment->{LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID}][$targetBudgetKey],
                        ProductAssignment::FIELD_QUALITY_TIER_ID => $this->qualityTierIdMap[$productAssignment->{EloquentQuoteCompany::LEAD_COST_TYPE}] ?? $this->standardQualityId,
                        ProductAssignment::FIELD_SALE_TYPE_ID    => $productAssignment->{ProductAssignment::FIELD_SALE_TYPE_ID} ?: $this->getMappedSaleTypeId($productAssignment->{LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID} ?? null),
                    ];
                }
            }
        }

        $this->upsertData = $productAssignmentUpdateData;
    }

    /**
     * @return void
     */
    protected function mapCampaignsToBudgetsForAppointments(): void
    {
        $this->campaignBudgetMap = DB::table(ProductCampaignBudget::TABLE)
            ->join(ProductCampaign::TABLE, ProductCampaign::TABLE .'.'. ProductCampaign::FIELD_ID, '=', ProductCampaignBudget::TABLE .'.'. ProductCampaignBudget::FIELD_PRODUCT_CAMPAIGN_ID)
            ->join(DatabaseHelperService::readOnlyDatabase() .'.'. LeadCampaign::TABLE, LeadCampaign::TABLE .'.'. LeadCampaign::ID, '=', ProductCampaign::TABLE .'.'. ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID)
            ->join(CompanyCampaignRelation::TABLE, CompanyCampaignRelation::TABLE .'.'. CompanyCampaignRelation::FIELD_RELATION_ID, '=', LeadCampaign::TABLE .'.'. LeadCampaign::ID)
            ->join(CompanyCampaign::TABLE, CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID, '=', CompanyCampaignRelation::TABLE .'.'. CompanyCampaignRelation::FIELD_COMPANY_CAMPAIGN_ID)
            ->join(BudgetContainer::TABLE, BudgetContainer::TABLE .'.'. BudgetContainer::FIELD_COMPANY_CAMPAIGN_ID, '=', CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID)
            ->join(Budget::TABLE, Budget::TABLE .'.'. Budget::FIELD_BUDGET_CONTAINER_ID, '=', BudgetContainer::TABLE .'.'. BudgetContainer::FIELD_ID)
            ->select([
                ProductCampaignBudget::TABLE .'.'. ProductCampaignBudget::FIELD_ID . ' as product_campaign_budget_id',
                Budget::TABLE .'.'. Budget::FIELD_ID . ' as budget_id',
                ProductCampaignBudget::TABLE .'.'. ProductCampaignBudget::FIELD_QUALITY_TIER . ' as budget_key'
            ])
            ->get()
            ->reduce(function($output, $budgetGroup) {
                $output[$budgetGroup->product_campaign_budget_id] = $output[$budgetGroup->product_campaign_budget_id] ?? [];
                $output[$budgetGroup->product_campaign_budget_id][$budgetGroup->budget_key] = $budgetGroup->budget_id;

                return $output;
            }, []);
    }

    /**
     * @return void
     */
    protected function mapCampaignsToBudgetForLeads(): void
    {
        $this->campaignBudgetMap = DB::table(DatabaseHelperService::readOnlyDatabase() .'.'. LeadCampaign::TABLE)
            ->join(CompanyCampaignRelation::TABLE,  CompanyCampaignRelation::TABLE .'.'. CompanyCampaignRelation::FIELD_RELATION_ID, '=', LeadCampaign::TABLE .'.'. LeadCampaign::ID)
            ->join(CompanyCampaign::TABLE, CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID, '=', CompanyCampaignRelation::TABLE .'.'. CompanyCampaignRelation::FIELD_COMPANY_CAMPAIGN_ID)
            ->join(IndustryService::TABLE, IndustryService::TABLE .'.'. IndustryService::FIELD_ID, '=', CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_SERVICE_ID)
            ->join(BudgetContainer::TABLE, BudgetContainer::TABLE .'.'. BudgetContainer::FIELD_COMPANY_CAMPAIGN_ID, '=', CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID)
            ->join(Budget::TABLE, Budget::TABLE .'.'. Budget::FIELD_BUDGET_CONTAINER_ID, '=', BudgetContainer::TABLE .'.'. BudgetContainer::FIELD_ID)
            ->select([
                LeadCampaign::TABLE .'.'. LeadCampaign::ID . ' as lead_campaign_id',
                Budget::TABLE .'.'. Budget::FIELD_ID . ' as budget_id',
                Budget::TABLE .'.'. Budget::FIELD_KEY . ' as budget_key',
            ])
            ->get()
            ->reduce(function($output, $budgetGroup) {
                $output[$budgetGroup->lead_campaign_id] = $output[$budgetGroup->lead_campaign_id] ?? [];
                $output[$budgetGroup->lead_campaign_id][$budgetGroup->budget_key] = $budgetGroup->budget_id;

                return $output;
            }, []);
    }

    /**
     * @return void
     */
    protected function getProductAssignments(): void
    {
        $this->appointmentProductAssignments = DB::table(ProductAssignment::TABLE)
            ->join(ConsumerProduct::TABLE, ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ID, '=', ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_CONSUMER_PRODUCT_ID)
            ->join(ServiceProduct::TABLE, ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_ID, '=', ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_SERVICE_PRODUCT_ID)
            ->join(IndustryService::TABLE, IndustryService::TABLE .'.'. IndustryService::FIELD_ID, '=', ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_INDUSTRY_SERVICE_ID)
            ->join(ProductCampaignBudget::TABLE, ProductCampaignBudget::TABLE .'.'. ProductCampaignBudget::FIELD_ID, '=', ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_PRODUCT_CAMPAIGN_BUDGET_ID)
            ->where(IndustryService::TABLE .'.'. IndustryService::FIELD_INDUSTRY_ID, $this->industryId)
            ->where(ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_PRODUCT_ID, $this->appointmentProductId)
            ->where(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_BUDGET_ID, 0)
            ->when($this->startDateTime, fn(Builder $query) =>
                $query->where(ProductAssignment::TABLE .'.'. ProductAssignment::CREATED_AT, '>=', $this->startDateTime)
            )
            ->select([
                ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_ID,
                ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_SALE_TYPE_ID,
                ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_PRODUCT_CAMPAIGN_BUDGET_ID,
                ProductCampaignBudget::TABLE .'.'. ProductCampaignBudget::FIELD_QUALITY_TIER,
            ])
            ->orderBy(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_ID);

        $this->leadProductAssignments = DB::table(ProductAssignment::TABLE)
            ->join(ConsumerProduct::TABLE, ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ID, '=', ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_CONSUMER_PRODUCT_ID)
            ->join(ServiceProduct::TABLE, ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_ID, '=', ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_SERVICE_PRODUCT_ID)
            ->join(IndustryService::TABLE, IndustryService::TABLE .'.'. IndustryService::FIELD_ID, '=', ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_INDUSTRY_SERVICE_ID)
            ->join(DatabaseHelperService::readOnlyDatabase() .'.'. EloquentQuoteCompany::TABLE, EloquentQuoteCompany::TABLE .'.'. EloquentQuoteCompany::ID, '=', ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_LEGACY_ID)
            ->join(DatabaseHelperService::readOnlyDatabase() .'.'. LeadCampaignSalesTypeConfiguration::TABLE, LeadCampaignSalesTypeConfiguration::TABLE .'.'. LeadCampaignSalesTypeConfiguration::ID, '=', EloquentQuoteCompany::TABLE .'.'. EloquentQuoteCompany::LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION_ID)
            ->where(IndustryService::TABLE .'.'. IndustryService::FIELD_INDUSTRY_ID, $this->industryId)
            ->where(ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_PRODUCT_ID, $this->leadProductId)
            ->where(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_BUDGET_ID, 0)
            ->when($this->startDateTime, fn(Builder $query) =>
                $query->where(ProductAssignment::TABLE .'.'. ProductAssignment::CREATED_AT, '>=', $this->startDateTime)
            )
            ->select([
                ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_ID,
                ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_SALE_TYPE_ID,
                LeadCampaignSalesTypeConfiguration::TABLE .'.'. LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID,
                LeadCampaignSalesTypeConfiguration::TABLE .'.'. LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID,
                EloquentQuoteCompany::TABLE .'.'. EloquentQuoteCompany::LEAD_COST_TYPE,
            ])
            ->orderBy(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_ID);
    }

    /**
     * @param string $saleTypeKey
     * @return string
     */
    protected function getBudgetKeyForSaleType(string $saleTypeKey): string
    {
        return match($saleTypeKey) {
            SaleTypes::DUO->value, SaleTypes::QUAD->value, SaleTypes::TRIO->value, SaleTypes::EXCLUSIVE->value => BudgetCategory::VERIFIED->value,
            SaleTypes::EMAIL_ONLY->value => BudgetCategory::EMAIL_ONLY->value,
            SaleTypes::UNVERIFIED->value => BudgetCategory::UNVERIFIED->value,
            default => null,
        };
    }

    /**
     * @param string $budgetKey
     * @return string|null
     */
    protected function getMappedQualityTierKey(string $budgetKey): ?string
    {
        return match($budgetKey) {
            QualityTierEnum::PREMIUM->value                                 => 'premium',
            QualityTierEnum::IN_HOME->value, QualityTierEnum::ONLINE->value => $budgetKey,
            default                                                         => null,
        };
    }

    /**
     * @param int|null $legacyId
     * @return int
     */
    protected function getMappedSaleTypeId(?int $legacyId): int
    {
        return $this->legacySaleTypeIdMap[$legacyId ?? 0];
    }

    protected function updateProgress(): void
    {
        echo "{$this->processed}/{$this->total} Product Assignments processed.\r";
    }

    protected function displayDryRunData(): int
    {
        $reportData = !count($this->upsertData)
            ? ["\tNo repairable items found"]
            : array_map(fn($data) =>
                "\nProductAssignment ID " . $data['id'] . ", assigning:"
                . "\n\tbudget_id => " . $data['budget_id']
                . "\n\tquality_tier_id => " . $data['quality_tier_id']
                . "\n\tsale_type_id => " . $data['sale_type_id'],
            $this->upsertData);

        $printData = [
            "ProductAssignments dry run report for " . count($this->upsertData) . " items -",
            ...$reportData,
        ];

        echo "\n\n" . implode("\n", $printData) . "\n\n";

        return count($this->upsertData);
    }
}
