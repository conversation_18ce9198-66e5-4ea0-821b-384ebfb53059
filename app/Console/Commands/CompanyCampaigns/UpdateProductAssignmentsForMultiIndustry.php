<?php

namespace App\Console\Commands\CompanyCampaigns;

use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\QualityTier as QualityTierEnum;
use App\Enums\Odin\SaleTypes;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\CompanyCampaignRelation;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\QualityTier;
use App\Models\Odin\ServiceProduct;
use App\Models\SaleType;
use App\Services\DatabaseHelperService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateProductAssignmentsForMultiIndustry
{
    protected int $industryId;

    protected array $productCampaignToBudgetMap = [];
    protected array $productAssignments = [];
    protected array $upsertData = [];

    protected array $budgetToSaleTypeIdMap = [];
    protected int $standardQualityId;

    protected int $total = 0;
    protected int $count = 0;

    /**
     * Attach ProductAssignments in a given Industry to a CompanyCampaign Budget
     *
     * @param IndustryEnum $industry
     * @return int - number of rows upserted
     */
    public function updateBudgetIdOnProductAssignments(IndustryEnum $industry): int
    {
        $this->industryId = Industry::query()
            ->where(Industry::FIELD_SLUG, $industry->getSlug())
            ->first()
            ->id;

        SaleType::all()
            ->each(function(SaleType $saleType) {
                $key = $this->getBudgetKeyForSaleType($saleType->name);
                if ($key)
                    $this->budgetToSaleTypeIdMap[$saleType->id] = $key;
            });

        $this->standardQualityId = QualityTier::query()
            ->where(QualityTier::FIELD_NAME, QualityTierEnum::STANDARD->value)
            ->first()
            ?->id ?? 0;

        $this->mapProductCampaignsToBudget();

        $this->getProductAssignments();

        print("\n");
        $this->updateProgress();

        $this->prepareUpsertData();

        if ($this->upsertData) {
            $upsertCount = ProductAssignment::query()
                ->upsert($this->upsertData, ProductAssignment::FIELD_ID, [
                    ProductAssignment::FIELD_BUDGET_ID,
                    ProductAssignment::FIELD_QUALITY_TIER_ID,
                ]);

            $this->count += $upsertCount / 2;
            $this->updateProgress();
            print("\n");

            return Command::SUCCESS;
        }

        return Command::FAILURE;
    }

    /**
     * @return void
     */
    protected function prepareUpsertData(): void
    {
        $productAssignmentUpdateData = [];
        foreach ($this->productAssignments as $productAssignment) {
            if (array_key_exists($productAssignment->{ProductAssignment::FIELD_CAMPAIGN_ID}, $this->productCampaignToBudgetMap)) {
                $targetBudgetKey = $this->budgetToSaleTypeIdMap[$productAssignment->{ProductAssignment::FIELD_SALE_TYPE_ID}] ?? null;
                if (array_key_exists($targetBudgetKey, $this->productCampaignToBudgetMap[$productAssignment->{ProductAssignment::FIELD_CAMPAIGN_ID}])) {
                    $productAssignmentUpdateData[] = [
                        ProductAssignment::FIELD_ID              => $productAssignment->{ProductAssignment::FIELD_ID},
                        ProductAssignment::FIELD_BUDGET_ID       => $this->productCampaignToBudgetMap[$productAssignment->{ProductAssignment::FIELD_CAMPAIGN_ID}][$targetBudgetKey],
                        ProductAssignment::FIELD_QUALITY_TIER_ID => $this->standardQualityId
                    ];
                }
            }
        }

        $this->upsertData = $productAssignmentUpdateData;
    }

    /**
     * @return void
     */
    protected function mapProductCampaignsToBudget(): void
    {
        $this->productCampaignToBudgetMap = DB::table(ProductCampaign::TABLE)
            ->join(IndustryService::TABLE, IndustryService::TABLE .'.'. IndustryService::FIELD_ID, '=', ProductCampaign::TABLE .'.'. ProductCampaign::FIELD_INDUSTRY_SERVICE_ID)
            ->join(DatabaseHelperService::readOnlyDatabase() .'.'. LeadCampaign::TABLE, DatabaseHelperService::readOnlyDatabase() .'.'. LeadCampaign::TABLE .'.'. LeadCampaign::ID, '=', ProductCampaign::TABLE .'.'. ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID)
            ->join(CompanyCampaignRelation::TABLE,  CompanyCampaignRelation::TABLE .'.'. CompanyCampaignRelation::FIELD_RELATION_ID, '=', LeadCampaign::TABLE .'.'. LeadCampaign::ID)
            ->join(CompanyCampaign::TABLE, CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID, '=', CompanyCampaignRelation::TABLE .'.'. CompanyCampaignRelation::FIELD_COMPANY_CAMPAIGN_ID)
            ->join(BudgetContainer::TABLE, BudgetContainer::TABLE .'.'. BudgetContainer::FIELD_COMPANY_CAMPAIGN_ID, '=', CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID)
            ->join(Budget::TABLE, Budget::TABLE .'.'. Budget::FIELD_BUDGET_CONTAINER_ID, '=', BudgetContainer::TABLE .'.'. BudgetContainer::FIELD_ID)
            ->select([
                ProductCampaign::TABLE .'.'. ProductCampaign::FIELD_ID . ' as product_campaign_id',
                Budget::TABLE .'.'. Budget::FIELD_ID . ' as budget_id',
                Budget::TABLE .'.'. Budget::FIELD_KEY . ' as budget_key',
            ])
            ->get()
            ->reduce(function($output, $budgetGroup) {
                $output[$budgetGroup->product_campaign_id] = $output[$budgetGroup->product_campaign_id] ?? [];
                $output[$budgetGroup->product_campaign_id][$budgetGroup->budget_key] = $budgetGroup->budget_id;
                return $output;
            }, []);
    }

    /**
     * @return void
     */
    protected function getProductAssignments(): void
    {
        $query = DB::table(ProductAssignment::TABLE)
            ->join(ConsumerProduct::TABLE, ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ID, '=', ProductAssignment::FIELD_CONSUMER_PRODUCT_ID)
            ->join(ServiceProduct::TABLE, ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_ID, '=', ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_SERVICE_PRODUCT_ID)
            ->join(IndustryService::TABLE, IndustryService::TABLE .'.'. IndustryService::FIELD_ID, '=', ServiceProduct::FIELD_INDUSTRY_SERVICE_ID)
            ->where(IndustryService::TABLE .'.'. IndustryService::FIELD_INDUSTRY_ID, $this->industryId)
            ->where(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_CAMPAIGN_ID, '!=', 0)
            ->where(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_BUDGET_ID, 0)
            ->select([
                ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_ID,
                ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_SALE_TYPE_ID,
                ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_CAMPAIGN_ID
            ]);

        $this->total = $query->count();

        $this->productAssignments = $query->get()->toArray();
    }

    /**
     * @param string $saleTypeKey
     * @return string|null
     */
    protected function getBudgetKeyForSaleType(string $saleTypeKey): ?string
    {
        return match($saleTypeKey) {
            SaleTypes::DUO->value, SaleTypes::QUAD->value, SaleTypes::TRIO->value, SaleTypes::EXCLUSIVE->value => 'verified',
            SaleTypes::EMAIL_ONLY->value => 'email_only',
            SaleTypes::UNVERIFIED->value => 'unverified',
            default => null,
        };
    }

    /**
     * @return void
     */
    protected function updateProgress(): void
    {
        echo "{$this->count}/{$this->total} Product Assignments updated.\r";
    }
}
