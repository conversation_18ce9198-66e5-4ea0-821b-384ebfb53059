<?php

namespace App\Console\Commands\CompanyCampaigns;

use App\Enums\Odin\Industry as IndustryEnum;
use App\Transformers\Campaign\LegacyCampaignToCompanyCampaignMigrator;
use Exception;
use Illuminate\Console\Command;
use Throwable;

class MigrateLegacyCampaigns extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'legacy-migrate:campaigns {--batch-size=250}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate legacy campaigns to company campaigns';

    /**
     * @param LegacyCampaignToCompanyCampaignMigrator $legacyCampaignToCompanyCampaignMigrator
     * @return int
     * @throws Exception
     * @throws Throwable
     */
    public function handle(
        LegacyCampaignToCompanyCampaignMigrator  $legacyCampaignToCompanyCampaignMigrator,
    ): int
    {
        // Filter out solar/roofing until ready, flip boolean for testing
        $allowAllIndustries = true;
        $batchSize = (int) $this->option('batch-size');

        if($batchSize < 1) {
            throw new Exception("Batch size must be at least one");
        }

        $industryOptions = $allowAllIndustries
            ? array_map(fn($i) => $i->value, IndustryEnum::cases())
            : array_filter(array_map(fn($i) => $i->value, IndustryEnum::cases()), fn(string $v) => $v != IndustryEnum::SOLAR->value);

        $industrySelection = $this->choice(
            "Choose an industry to migrate",
            $industryOptions
        );

        $industry = IndustryEnum::from($industrySelection);

        system('clear');

        $this->info("Migrating {$industrySelection} campaigns");
        $this->newLine();

        $result = $legacyCampaignToCompanyCampaignMigrator->transformIndustryLeadCampaigns($industry, $batchSize);

        $this->newLine();

        if($result) {
            $this->info("{$industrySelection} campaigns migrated");
            $runPostMigrationCommand = $this->confirm("\n\nMigration was successful\nWould you like to run the post-migration script for $industrySelection?", true);

            if ($runPostMigrationCommand) {
                $result = $this->call('post-migration:campaigns', ['--industry' => $industry->value]);
                if ($result !== Command::SUCCESS)
                    $this->error("Error running post-migration for $industrySelection");
            }

        }
        else {
            $this->error("Error migrating {$industrySelection} campaigns");
        }

        return $result ? Command::SUCCESS : Command::FAILURE;
    }
}
