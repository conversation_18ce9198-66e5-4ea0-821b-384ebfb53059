<?php

namespace App\Console\Commands\CompanyCampaigns;

use App\Models\Campaigns\CampaignReactivation;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\CompanyCampaignRelation;
use App\Models\Campaigns\Modules\Bidding\CompanyCampaignBidPriceModule;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModule;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleContact;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleCRM;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModule;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\CompanyCampaignPropertyType;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;
use function app;

/**
 * This command is solely for removing migration data for testing CompanyCampaign migrations
 * ...or breaking your dev DB if you feel like it?
 */
class TruncateCompanyCampaignTables extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'legacy-migrate:truncate-campaigns';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Truncate all data related to Company Campaigns - testing only';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        if (App::environment() !== 'development') {
            $this->error("This command can only be run in development");
            return 0;
        }

        $modelsToTruncate = collect([
            CompanyCampaignDeliveryModuleCRM::class,
            CompanyCampaignDeliveryModuleContact::class,
            CompanyCampaignDeliveryModule::class,
            Budget::class,
            BudgetContainer::class,
            CompanyCampaignLocationModuleLocation::class,
            CompanyCampaignLocationModule::class,
            CompanyCampaignBidPriceModule::class,
            CompanyCampaignRelation::class,
            CompanyCampaign::class,
            CompanyCampaignPropertyType::class,
            CampaignReactivation::class,
        ]);

        $this->warn('This command will destroy all previously migrated legacy data in Models:');
        $modelsToTruncate->each(fn($model) => $this->info("  - {$model}"));

        if ($this->confirm('Are you sure you wish to continue?')) {
            $modelsToTruncate->each(fn($model) => app($model)::truncate());

            $this->warn('Data destroyed. Pew pew pew.');
            return 1;
        }

        $this->line('Operation aborted, no data destroyed. You coward.');
        return 0;
    }

}
