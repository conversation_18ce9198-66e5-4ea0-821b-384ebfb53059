<?php

namespace App\Console\Commands\CompanyCampaigns;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModule;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use TheSeer\Tokenizer\Exception;

class AddCompanyIdToCampaignLocations extends Command
{
    const int DEFAULT_BATCH_SIZE = 20000;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = "update:campaign-location-company-ids {--batch-size=" . self::DEFAULT_BATCH_SIZE . "}";

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Populate the company_id column on company_campaign_location_module_locations';

    /**
     * @return int
     */
    public function handle(): int
    {
        $this->info("\n\nAdding missing company_ids to company_campaign_location_module_locations table...");
        $batchSize = max(intval($this->option('batch-size')), self::DEFAULT_BATCH_SIZE);

        $locations = DB::table(CompanyCampaignLocationModuleLocation::TABLE)
            ->select([CompanyCampaignLocationModuleLocation::TABLE .'.'. CompanyCampaignLocationModuleLocation::FIELD_ID, CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_COMPANY_ID])
            ->join(CompanyCampaignLocationModule::TABLE, CompanyCampaignLocationModule::TABLE .'.'. CompanyCampaignLocationModule::FIELD_ID, CompanyCampaignLocationModuleLocation::TABLE .'.'. CompanyCampaignLocationModuleLocation::FIELD_MODULE_ID)
            ->join(CompanyCampaign::TABLE, CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID, CompanyCampaignLocationModule::TABLE .'.'. CompanyCampaignLocationModule::FIELD_CAMPAIGN_ID)
            ->where(CompanyCampaignLocationModuleLocation::TABLE .'.'. CompanyCampaignLocationModuleLocation::FIELD_COMPANY_ID, '=', 0)
            ->where(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_COMPANY_ID, '!=', 0)
            ->orderBy(CompanyCampaignLocationModuleLocation::TABLE .'.'. CompanyCampaignLocationModuleLocation::FIELD_ID);

        $locationCount = $locations->count();

        if (!$locationCount) {
            $this->line("\nNo campaign locations need updating.\n");
            return Command::SUCCESS;
        }

        $this->line("$locationCount campaign_locations found...");

        $chunkCount = ceil($locationCount / $batchSize);
        $this->line("Processing $chunkCount chunks...");

        $bar = $this->output->createProgressBar($chunkCount);
        $bar->start();

        $completed = $locations->chunkById($batchSize, function(Collection $chunk) use($bar) {
            $upsertData = $chunk->reduce(function($output, $location) {
                $output[] = [
                    CompanyCampaignLocationModuleLocation::FIELD_ID => $location->id,
                    CompanyCampaignLocationModuleLocation::FIELD_COMPANY_ID => $location->company_id,
                ];
                return $output;
            }, []);

            try {
                DB::table(CompanyCampaignLocationModuleLocation::TABLE)
                    ->upsert(
                        $upsertData,
                        [],
                        [CompanyCampaignLocationModuleLocation::FIELD_COMPANY_ID]
                    );
            }
            catch(Exception $e) {
                $this->error($e->getMessage());

                return false;
            }

            $bar->advance();
        }, CompanyCampaignLocationModuleLocation::TABLE .'.'. CompanyCampaignLocationModuleLocation::FIELD_ID,
            CompanyCampaignLocationModuleLocation::FIELD_ID
        );

        if (!$completed)
            return Command::FAILURE;

        $bar->finish();
        $this->info("\n\nCompleted updating company_ids.\n");

        return Command::SUCCESS;
    }
}