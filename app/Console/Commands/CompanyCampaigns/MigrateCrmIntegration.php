<?php

namespace App\Console\Commands\CompanyCampaigns;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Delivery\CompanyCRMTemplate;
use App\Models\Legacy\CrmIntegration;
use App\Models\Odin\Company;
use App\Transformers\Campaign\LegacyCRMIntegrationTransformer;
use Illuminate\Console\Command;

/**
 * This command is for testing CRM integration migrations.
 * If there's a need to use it in Production it will need to be reviewed, and have the Prod check removed.
 */
class MigrateCrmIntegration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * companyId - provide the Admin2.0 id to convert all of a company's integrations as Templates
     * integrationId - provide the legacy id to convert one of a company's integrations as Template
     * campaignId - provide an Admin2.0 CompanyCampaign ID to create all company Templates, and the campaign deliverers
     * toCompanyId - force a new company ID to write the resulting Templates to
     *
     * @var string
     */
    protected $signature = 'legacy-migrate:crm-integration {--companyId=} {--integrationId=} {--campaignId=} {--toCompanyId=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Development script to migrate a CRM integration, or all Company integrations (as templates), or all Campaign integrations (as standalone deliveries)';

    /**
     * @return int
     */
    public function handle(): int
    {
        if (app()->isProduction())
            return $this->abort("This command is not designed for Production use.");

        $forCompany     = (int) $this->option('companyId');
        $forIntegration = (int) $this->option('integrationId');
        $forCampaign    = (int) $this->option('campaignId');
        $toCompanyId    = (int) $this->option('toCompanyId');

        $companyId = null;
        $moduleId = null;

        if ($forCompany > 0) {
            $company = Company::query()->find($forCompany);
            if (!$company)
                return $this->abort("Company not found, aborting");

            $integrations = CrmIntegration::query()
                ->where(CrmIntegration::COMPANY_ID, $company->legacy_id)
                ->get();

            $companyId = $company->id;
        }
        else if ($forIntegration > 0) {
            $integrations = CrmIntegration::query()
                ->where(CrmIntegration::ID, $forIntegration)
                ->get();

            if ($integrations?->count()) {
                $company = Company::query()
                    ->where(Company::FIELD_LEGACY_ID, $integrations->first()->{CrmIntegration::COMPANY_ID})
                    ->first();
                $companyId = $company?->id;
            }
        }
        else if ($forCampaign > 0) {
            $campaign = CompanyCampaign::query()
                ->find($forCampaign);
            if (!$campaign)
                return $this->abort("Campaign not found, aborting");

            $integrations = CrmIntegration::query()
                ->where(CrmIntegration::COMPANY_ID, $campaign->company->legacy_id)
                ->get();

            $companyId = $campaign->company->id;
            $moduleId = $campaign->deliveryModule->id;
        }
        else {
            $integrations = null;
        }

        if ($toCompanyId > 0) {
            $toCompany = Company::query()->find($toCompanyId);
            if (!$toCompany) {
                $this->error("Target company not found: " . $toCompany);
                return Command::FAILURE;
            }
        }

        if (!$integrations?->count())
            return $this->abort("No integrations were found, aborting");

        $integrationTransformer = new LegacyCRMIntegrationTransformer(true, $companyId, $moduleId);

        $transformData = $integrationTransformer->transformCollection($integrations, $toCompanyId ?: null);
        $count = $transformData->count();

        if ($count) {
            $inserted = CompanyCRMTemplate::query()
                ->insert($transformData->toArray());

            if ($inserted) {
                $this->info("\n$count record(s) inserted.\n");
                return Command::SUCCESS;
            }
        }

        return Command::FAILURE;
    }

    private function abort(string $message): int
    {
        $this->error($message);
        return Command::FAILURE;
    }
}
