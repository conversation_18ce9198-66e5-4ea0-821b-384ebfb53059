<?php

namespace App\Console\Commands;

use App\Enums\Odin\GlobalConfigurableFields;
use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\EloquentZipCode;
use App\Models\Legacy\LeadTrackingUrl;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductAffiliateRecord;
use App\Models\Odin\ConsumerProductData;
use App\Models\Odin\ConsumerProductTcpaRecord;
use App\Models\Odin\ConsumerProductTracking;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\PropertyType;
use App\Models\Odin\ServiceProduct;
use App\Models\Odin\Website;
use App\Repositories\Odin\ProductRepository;
use App\Repositories\Odin\ServiceProductRepository;
use App\Services\DatabaseHelperService;
use Exception;
use Illuminate\Console\Command;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\PropertyType as PropertyTypeEnum;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class RecreateMissingProductAssignmentConsumerProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:recreate-missing-product-assignment-consumer-products {--chunksize=1000}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'For product assignments that have missing consumer products, recreate the consumer products with legacy data';

    public function __construct(
        private readonly ServiceProductRepository $serviceProductRepository,
        private readonly ProductRepository $productRepository
    )
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $chunkSize = (int) $this->option('chunksize');

        if($chunkSize <= 0) {
            throw new Exception("Chunk size must be greater than zero");
        }

        $this->line("\nStarting");

        $missingConsumerProducts = $this->getMissingConsumerProductsData();

        $this->line("\nObtained missing consumer products");

        $missingConsumers = $this->getMissingConsumersData($missingConsumerProducts->keys()->toArray());

        $this->line("\nObtained missing consumers");

        $consumerProductConsumers = [];
        foreach($missingConsumerProducts as $legacyProductAssignmentId => $missingConsumerProductProductAssignment) {
            if(!empty($missingConsumers[$legacyProductAssignmentId])) {
                $consumerProductConsumers[$missingConsumerProductProductAssignment->{ProductAssignment::FIELD_CONSUMER_PRODUCT_ID}] = $missingConsumers[$legacyProductAssignmentId];
            }
        }

        unset($missingConsumers);

        $this->line("\nMapped missing consumer products to consumers");

        $solarServiceProductId = $this->serviceProductRepository->getDefaultServiceProductByIndustry(IndustryEnum::SOLAR->value)->{ServiceProduct::FIELD_ID};
        $roofingServiceProductId = $this->serviceProductRepository->getDefaultServiceProductByIndustry(IndustryEnum::ROOFING->value)->{ServiceProduct::FIELD_ID};

        $consumers = Consumer::query()
            ->whereIntegerInRaw(Consumer::FIELD_ID, array_column($consumerProductConsumers, Consumer::FIELD_ID))
            ->get()
            ->keyBy(Consumer::FIELD_ID);

        $propertyTypes = PropertyType::query()->pluck(PropertyType::FIELD_ID, PropertyType::FIELD_NAME)->toArray();

        $addresses = Address::query()
            ->whereIntegerInRaw(Address::FIELD_ID, array_column($consumerProductConsumers, EloquentQuote::ADDRESS_ID))
            ->pluck(Address::FIELD_ID, Address::FIELD_LEGACY_ID)
            ->toArray();

        $websites = Website::query()
            ->pluck(Website::FIELD_ID, Website::FIELD_ABBREVIATION)
            ->toArray();

        $this->recreateMissingData(
            $consumerProductConsumers,
            $missingConsumerProducts,
            $solarServiceProductId,
            $roofingServiceProductId,
            $consumers,
            $propertyTypes,
            $addresses,
            $websites,
            $chunkSize
        );

        $this->line("\nDone");
    }

    private function recreateMissingData(
        array $consumerProductConsumers,
        Collection $missingConsumerProducts,
        int $solarServiceProductId,
        int $roofingServiceProductId,
        Collection $consumers,
        array $propertyTypes,
        array $addresses,
        array $websites,
        int $chunkSize = 1000
    ): bool
    {
        $this->line("\nInserting missing data\n");

        $inserted = 0;

        $consumerProductRows = [];
        $trackingRows = [];
        $tcpaRows = [];
        $dataRows = [];
        $affiliateRows = [];
        $addressRows = [];
        foreach($consumerProductConsumers as $consumerProductId => $consumerData) {
            $currentConsumer = $consumers->get($consumerData[Consumer::FIELD_ID]);

            $serviceProductId = null;
            if($consumerData[EloquentQuote::MULTI_INDUSTRY_LEAD]) {
                $serviceProductId = $missingConsumerProducts
                    ->get($consumerData[EloquentQuoteCompany::QUOTE_COMPANY_ID])
                    ?->{ProductAssignment::RELATION_CAMPAIGN}
                    ?->{ProductCampaign::RELATION_SERVICE}
                    ?->{IndustryService::RELATION_SERVICE_PRODUCTS}
                    ?->first()
                    ?->{ServiceProduct::FIELD_ID};
            }
            else if($consumerData[EloquentQuote::SOLAR_LEAD]) {
                $serviceProductId = $solarServiceProductId;
            }
            else if($consumerData[EloquentQuote::ROOFING_LEAD]) {
                $serviceProductId = $roofingServiceProductId;
            }

            if(empty($serviceProductId)) {
                continue;
            }

            $trackingRows[$consumerProductId] = [
                ConsumerProductTracking::URL_CONVERT => $consumerData[LeadTrackingUrl::URL_CONVERT] ?? '',
                ConsumerProductTracking::URL_START => $consumerData[LeadTrackingUrl::URL_START] ?? '',
                ConsumerProductTracking::WEBSITE_ID => $websites[$consumerData[EloquentQuote::ORIGIN]],
                ConsumerProductTracking::AD_TRACK_CODE => $consumerData[EloquentQuote::TRACK_CODE] ?? '',
                ConsumerProductTracking::AD_TRACK_TYPE => $consumerData[EloquentQuote::TRACK_NAME] ?? ''
            ];

            $tcpaRows[$consumerProductId] = [
                ConsumerProductTcpaRecord::FIELD_TCPA_ID => $consumerData[EloquentQuote::TCPA_LEAD_ID],
                ConsumerProductTcpaRecord::FIELD_TCPA_SERVICE_TYPE => "WatchDog"
            ];

            $dataRows[$consumerProductId] = [
                ConsumerProductData::FIELD_PAYLOAD => json_encode([
                    GlobalConfigurableFields::IP_ADDRESS->value => $consumerData[EloquentQuote::IP_ADDRESS]
                ])
            ];

            $affiliateRows[$consumerProductId] = [
                ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID => $consumerData[EloquentQuote::AFFILIATE_ID],
                ConsumerProductAffiliateRecord::FIELD_CAMPAIGN_ID => $consumerData[EloquentQuote::CAMPAIGN_ID],
                ConsumerProductAffiliateRecord::FIELD_TRACK_CODE => $consumerData[EloquentQuote::TRACK_CODE],
                ConsumerProductAffiliateRecord::FIELD_TRACK_NAME => $consumerData[EloquentQuote::TRACK_NAME]
            ];

            if(empty($addresses[$consumerData[EloquentQuote::ADDRESS_ID]])) {
                $addressId = null;

                $addressRows[$consumerProductId] = [
                    Address::FIELD_ADDRESS_1 => $consumerData[EloquentAddress::ADDRESS1],
                    Address::FIELD_ADDRESS_2 => $consumerData[EloquentAddress::ADDRESS2],
                    Address::FIELD_CITY => $consumerData[EloquentAddress::CITY],
                    Address::FIELD_STATE => $consumerData[EloquentAddress::STATE_ABBR],
                    Address::FIELD_ZIP_CODE => $consumerData[EloquentAddress::ZIP_CODE],
                    Address::FIELD_COUNTRY => $consumerData[EloquentAddress::COUNTRY],
                    Address::FIELD_LATITUDE => $consumerData[EloquentAddress::LATITUDE],
                    Address::FIELD_LONGITUDE => $consumerData[EloquentAddress::LONGITUDE],
                    Address::FIELD_LEGACY_ID => $consumerData[EloquentQuote::ADDRESS_ID],
                    Address::FIELD_UTC => $consumerData[EloquentZipCode::FIELD_UTC],
                    Address::FIELD_IMPORTED => false
                ];
            }
            else {
                $addressId = $addresses[$consumerData[EloquentQuote::ADDRESS_ID]];
            }

            $consumerProductRows[$consumerProductId] = [
                ConsumerProduct::FIELD_ID => $consumerProductId,
                ConsumerProduct::FIELD_CONSUMER_ID => $currentConsumer->{Consumer::FIELD_ID},
                ConsumerProduct::FIELD_SERVICE_PRODUCT_ID => $serviceProductId,
                ConsumerProduct::FIELD_ADDRESS_ID => $addressId,
                ConsumerProduct::FIELD_GOOD_TO_SELL => true,
                ConsumerProduct::FIELD_STATUS => ConsumerProduct::STATUS_ALLOCATED,
                ConsumerProduct::FIELD_CONTACT_REQUESTS => min($currentConsumer->{Consumer::FIELD_MAX_CONTACT_REQUESTS}, 4),
                ConsumerProduct::FIELD_PROPERTY_TYPE_ID => $propertyTypes[PropertyTypeEnum::fromLegacyLeadCategory($consumerData[EloquentQuote::LEAD_CATEGORY_ID])->value],
                ConsumerProduct::FIELD_CONSUMER_PRODUCT_DATA_ID => null,
                ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID => null,
                ConsumerProduct::FIELD_CONSUMER_PRODUCT_TCPA_RECORD_ID => null,
                ConsumerProduct::FIELD_CONSUMER_PRODUCT_AFFILIATE_RECORD_ID => null,
                ConsumerProduct::CREATED_AT => $currentConsumer->{Consumer::CREATED_AT}->format('Y-m-d H:i:s')
            ];

            if(count($consumerProductRows) >= $chunkSize) {
                $this->insertData(
                    $consumerProductRows,
                    $trackingRows,
                    $tcpaRows,
                    $dataRows,
                    $affiliateRows,
                    $addressRows
                );

                $inserted += count($consumerProductRows);

                $this->line("Inserted {$inserted}\r");

                $consumerProductRows = [];
                $trackingRows = [];
                $tcpaRows = [];
                $dataRows = [];
                $affiliateRows = [];
            }
        }

        if(!empty($consumerProductRows)) {
            $this->insertData(
                $consumerProductRows,
                $trackingRows,
                $tcpaRows,
                $dataRows,
                $affiliateRows,
                $addressRows
            );

            $inserted += count($consumerProductRows);

            $this->line("Inserted {$inserted}\r");
        }

        return true;
    }

    private function getMissingConsumerProductsData(): Collection
    {
        $leadProductId = $this->productRepository->getLeadProductId();

        return ProductAssignment::query()
            ->whereDoesntHave(ProductAssignment::RELATION_CONSUMER_PRODUCT)
            ->distinct()
            ->with([
                ProductAssignment::RELATION_CAMPAIGN.'.'.ProductCampaign::RELATION_SERVICE.'.'.IndustryService::RELATION_SERVICE_PRODUCTS => function($has) use ($leadProductId) {
                    $has->where(ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_PRODUCT_ID, $leadProductId);
                }
            ])
            ->select([
                ProductAssignment::FIELD_CONSUMER_PRODUCT_ID,
                ProductAssignment::FIELD_LEGACY_ID,
                ProductAssignment::FIELD_CAMPAIGN_ID
            ])
            ->get()
            ->keyBy(ProductAssignment::FIELD_LEGACY_ID);
    }

    private function getMissingConsumersData(array $quoteCompanyIds): array
    {
        return EloquentQuoteCompany::query()
            ->join(DatabaseHelperService::database().'.'.Consumer::TABLE, function($join) {
                $join->on(
                    DatabaseHelperService::database().'.'.Consumer::TABLE.'.'.Consumer::FIELD_LEGACY_ID,
                    '=',
                    DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::QUOTE_ID
                );
            })
            ->join(DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuote::TABLE, function($join) {
                $join->on(
                    DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuote::TABLE.'.'.EloquentQuote::QUOTE_ID,
                    '=',
                    DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::QUOTE_ID
                );
            })
            ->join(DatabaseHelperService::readOnlyDatabase().'.'.EloquentAddress::TABLE, function($join) {
                $join->on(
                    DatabaseHelperService::readOnlyDatabase().'.'.EloquentAddress::TABLE.'.'.EloquentAddress::ID,
                    '=',
                    DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuote::TABLE.'.'.EloquentQuote::ADDRESS_ID
                );
            })
            ->leftJoin(DatabaseHelperService::readOnlyDatabase().'.'.EloquentZipCode::TABLE, function($join) {
                $join->on(
                    DatabaseHelperService::readOnlyDatabase().'.'.EloquentAddress::TABLE.'.'.EloquentAddress::ZIP_CODE,
                    '=',
                    DatabaseHelperService::readOnlyDatabase().'.'.EloquentZipCode::TABLE.'.'.EloquentZipCode::FIELD_ZIP_CODE
                );
            })
            ->leftJoin(DatabaseHelperService::readOnlyDatabase().'.'.LeadTrackingUrl::TABLE, function($join) {
                $join->on(
                    DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuote::TABLE.'.'.EloquentQuote::QUOTE_ID,
                    '=',
                    DatabaseHelperService::readOnlyDatabase().'.'.LeadTrackingUrl::TABLE.'.'.LeadTrackingUrl::LEAD_ID
                );
            })
            ->whereIntegerInRaw(EloquentQuoteCompany::ID, $quoteCompanyIds)
            ->distinct()
            ->select([
                Consumer::TABLE.'.'.Consumer::FIELD_ID,
                EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::QUOTE_COMPANY_ID,
                EloquentQuote::TABLE.'.'.EloquentQuote::SOLAR_LEAD,
                EloquentQuote::TABLE.'.'.EloquentQuote::ROOFING_LEAD,
                EloquentQuote::TABLE.'.'.EloquentQuote::MULTI_INDUSTRY_LEAD,
                EloquentQuote::TABLE.'.'.EloquentQuote::LEAD_CATEGORY_ID,
                EloquentQuote::TABLE.'.'.EloquentQuote::ADDRESS_ID,
                EloquentQuote::TABLE.'.'.EloquentQuote::TCPA_LEAD_ID,
                EloquentQuote::TABLE.'.'.EloquentQuote::TRACK_CODE,
                EloquentQuote::TABLE.'.'.EloquentQuote::TRACK_NAME,
                EloquentQuote::TABLE.'.'.EloquentQuote::ORIGIN,
                EloquentQuote::TABLE.'.'.EloquentQuote::IP_ADDRESS,
                EloquentQuote::TABLE.'.'.EloquentQuote::AFFILIATE_ID,
                EloquentQuote::TABLE.'.'.EloquentQuote::CAMPAIGN_ID,
                LeadTrackingUrl::TABLE.'.'.LeadTrackingUrl::URL_START,
                LeadTrackingUrl::TABLE.'.'.LeadTrackingUrl::URL_CONVERT,
                EloquentAddress::TABLE.'.'.EloquentAddress::ADDRESS1,
                EloquentAddress::TABLE.'.'.EloquentAddress::ADDRESS2,
                EloquentAddress::TABLE.'.'.EloquentAddress::CITY,
                EloquentAddress::TABLE.'.'.EloquentAddress::STATE_ABBR,
                EloquentAddress::TABLE.'.'.EloquentAddress::ZIP_CODE,
                EloquentAddress::TABLE.'.'.EloquentAddress::LONGITUDE,
                EloquentAddress::TABLE.'.'.EloquentAddress::LATITUDE,
                EloquentAddress::TABLE.'.'.EloquentAddress::COUNTRY,
                EloquentZipCode::TABLE.'.'.EloquentZipCode::FIELD_UTC
            ])
            ->get()
            ->keyBy(EloquentQuoteCompany::QUOTE_COMPANY_ID)
            ->toArray();
    }

    private function insertData(
        array $consumerProductRows,
        array $trackingRows,
        array $tcpaRows,
        array $dataRows,
        array $affiliateRows,
        array $addressRows
    ): bool
    {
        DB::transaction(function() use ($consumerProductRows, $trackingRows, $tcpaRows, $dataRows, $affiliateRows, $addressRows) {
            foreach($consumerProductRows as $consumerProductId => &$consumerProductRow) {
                if(!empty($addressRows[$consumerProductId])) {
                    $consumerProductRow[ConsumerProduct::FIELD_ADDRESS_ID] = Address::query()->insertGetId($addressRows[$consumerProductId]);
                }

                $consumerProductRow[ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID] = ConsumerProductTracking::query()->insertGetId($trackingRows[$consumerProductId]);
                $consumerProductRow[ConsumerProduct::FIELD_CONSUMER_PRODUCT_TCPA_RECORD_ID] = ConsumerProductTcpaRecord::query()->insertGetId($tcpaRows[$consumerProductId]);
                $consumerProductRow[ConsumerProduct::FIELD_CONSUMER_PRODUCT_DATA_ID] = ConsumerProductData::query()->insertGetId($dataRows[$consumerProductId]);
                $consumerProductRow[ConsumerProduct::FIELD_CONSUMER_PRODUCT_AFFILIATE_RECORD_ID] = ConsumerProductAffiliateRecord::query()->insertGetId($affiliateRows[$consumerProductId]);
            }

            ConsumerProduct::query()->insert($consumerProductRows);
        });

        return true;
    }
}
