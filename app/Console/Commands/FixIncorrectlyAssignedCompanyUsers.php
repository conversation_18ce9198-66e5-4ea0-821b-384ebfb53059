<?php

namespace App\Console\Commands;

use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Legacy\EloquentUser;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Services\DatabaseHelperService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;

class FixIncorrectlyAssignedCompanyUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:incorrectly-assigned-company-users';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fixes company users assigned to the wrong company';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $this->info("\nUpdating company users");
        $query = $this->getCompanyUsersQuery();
        $this->updateCompanyUsers($query);

        $this->info("\nUpdating company contacts");
        $query = $this->getCompanyContactsQuery();
        $this->updateCompanyUsers($query);
    }

    /**
     * @param Builder $query
     * @return void
     */
    private function updateCompanyUsers(Builder $query): void
    {
        $bar = $this->output->createProgressBar($query->count());

        $query->chunk(100, function($users) use ($bar) {

            $companies = Company::query()
                ->select([
                    Company::FIELD_ID,
                    Company::FIELD_LEGACY_ID
                ])
                ->whereIn(
                    Company::FIELD_LEGACY_ID,
                    $users->pluck('correct_legacy_company_id')->toArray()
                )->get();

            foreach($users as $user) {

                $correctCompanyId = $companies->where(Company::FIELD_LEGACY_ID, $user->{'correct_legacy_company_id'})->first()?->{Company::FIELD_ID};

                if($correctCompanyId) {
                    $user->update([
                        CompanyUser::FIELD_COMPANY_ID => $correctCompanyId
                    ]);
                }

                $bar->advance();
            }
        });

        $bar->finish();
    }

    /**
     * @return Builder|\Illuminate\Database\Query\Builder
     */
    private function getCompanyUsersQuery(): Builder|\Illuminate\Database\Query\Builder
    {
        return CompanyUser::withTrashed()
            ->select([
                CompanyUser::TABLE.'.'.CompanyUser::FIELD_ID,
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentUser::TABLE.'.'.EloquentUser::COMPANY_ID . ' as correct_legacy_company_id'
            ])
            ->join(
                Company::TABLE,
                Company::TABLE.'.'.Company::FIELD_ID,
                '=',
                CompanyUser::TABLE.'.'.CompanyUser::FIELD_COMPANY_ID
            )
            ->join(
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentUser::TABLE,
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentUser::TABLE.'.'.EloquentUser::USER_ID,
                '=',
                CompanyUser::TABLE.'.'.CompanyUser::FIELD_LEGACY_ID
            )
            ->whereRaw(
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentUser::TABLE.'.'.EloquentUser::COMPANY_ID . " != " . Company::TABLE.'.'.Company::FIELD_LEGACY_ID
            )
            ->where(CompanyUser::TABLE.'.'.CompanyUser::FIELD_CAN_LOG_IN, 1)
            ->where(CompanyUser::TABLE.'.'.CompanyUser::FIELD_IS_CONTACT, 0)
            ->where(DatabaseHelperService::readOnlyDatabase().'.'.EloquentUser::TABLE.'.'.EloquentUser::COMPANY_ID, '!=', 0);
    }

    /**
     * @return Builder|\Illuminate\Database\Query\Builder
     */
    private function getCompanyContactsQuery(): Builder|\Illuminate\Database\Query\Builder
    {
        return CompanyUser::withTrashed()
            ->select([
                CompanyUser::TABLE.'.'.CompanyUser::FIELD_ID,
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompanyContact::TABLE.'.'.EloquentCompanyContact::FIELD_COMPANY_ID . ' as correct_legacy_company_id'
            ])
            ->join(
                Company::TABLE,
                Company::TABLE.'.'.Company::FIELD_ID,
                '=',
                CompanyUser::TABLE.'.'.CompanyUser::FIELD_COMPANY_ID
            )
            ->join(
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompanyContact::TABLE,
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompanyContact::TABLE.'.'.EloquentCompanyContact::FIELD_CONTACT_ID,
                '=',
                CompanyUser::TABLE.'.'.CompanyUser::FIELD_LEGACY_ID
            )
            ->whereRaw(
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompanyContact::TABLE.'.'.EloquentCompanyContact::FIELD_COMPANY_ID . " != " . Company::TABLE.'.'.Company::FIELD_LEGACY_ID
            )
            ->where(CompanyUser::TABLE.'.'.CompanyUser::FIELD_CAN_LOG_IN, 0)
            ->where(CompanyUser::TABLE.'.'.CompanyUser::FIELD_IS_CONTACT, 1)
            ->where(DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompanyContact::TABLE.'.'.EloquentCompanyContact::FIELD_COMPANY_ID, '!=', 0);
    }
}
