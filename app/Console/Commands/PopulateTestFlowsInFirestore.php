<?php

namespace App\Console\Commands;

use App\Services\GoogleFirestoreService;
use Illuminate\Console\Command;
use Ramsey\Uuid\Uuid;
use Illuminate\Contracts\Container\BindingResolutionException;

class PopulateTestFlowsInFirestore extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'populate:test-flows';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Populate test data in Firestore for Calculator Flow work';

    /**
     * @return int
     * @throws BindingResolutionException
     */
    public function handle(): int
    {
        $this->info("\n\nPopulating Firestore database with test flows.");

        /** @var GoogleFirestoreService $firestore */
        $firestore = app()->make(GoogleFirestoreService::class);

        $firestore->addDocumentToCollection('Flows', 'SolarCalculator', [
            'name'              => 'Solar Open Calculator',
            'description'       => 'SolarReviews Open Solar Calculator',
            'versions'          => [
                'production'    => null
            ],
        ]);

        $firestore->addDocumentToCollection('Flows', 'BathroomCalculator', [
            'name'              => 'Bathroom Calculator',
            'description'       => 'Fixr Bathroom Calculator',
            'versions'          => [
                'production'    => null
            ],
        ]);

        $solarHistory = Uuid::uuid4()->toString();
        $bathroomHistory = Uuid::uuid4()->toString();

        $testFlowString = '{"name":"","slides":[{"id":"slide-wqtr6x8k","name":"slide-wqtr6x8k","hierarchy":[{"name":"Continue Button","id":"continue-button-qt047ext","type":"button","paddingTop":20,"paddingBottom":20,"paddingLeft":20,"paddingRight":20,"marginTop":0,"marginBottom":0,"marginLeft":0,"marginRight":0,"overwriteMargin":false,"width":6,"blockAlignment":"text-center","action":0,"actionData":{},"icon":"<path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3\"></path>","content":"Continsdgue","iconSide":1,"color":"primary","textColor":"white"},{"name":"Continue Button","id":"continue-button-d0ex1lx6","type":"button","paddingTop":20,"paddingBottom":20,"paddingLeft":20,"paddingRight":20,"marginTop":0,"marginBottom":0,"marginLeft":0,"marginRight":0,"overwriteMargin":false,"width":6,"blockAlignment":"text-center","action":2,"actionData":{"slide":"slide-1kujfc8x"},"icon":"<path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3\"></path>","content":"Contidsgnue","iconSide":1,"color":"primary","textColor":"white"},{"name":"Multiple Choice","id":"multiple-choice-3la4cc1w","type":"multiple-choice","paddingTop":20,"paddingBottom":20,"paddingLeft":20,"paddingRight":20,"marginTop":0,"marginBottom":0,"marginLeft":0,"marginRight":0,"overwriteMargin":false,"width":6,"blockAlignment":"text-center","backendIdentifier":"multiple-choice-yszjk2np","value":"option-1","options":[{"id":"option-1","displayName":"Test11123","action":{"type":0,"data":{}}},{"id":"option-2","displayName":"Tets12342","action":{"type":0,"data":{}}},{"id":"option-3","displayName":"33333","action":{"type":0,"data":{}}}],"multiSelect":false,"layoutType":0,"autoProgress":true,"gridCols":2,"gridGap":2}],"width":12},{"id":"slide-1kujfc8x","name":"slide-1kujfc8x","hierarchy":[{"name":"Back Button","id":"back-button-1k67wwrk","type":"button","paddingTop":20,"paddingBottom":20,"paddingLeft":20,"paddingRight":20,"marginTop":0,"marginBottom":0,"marginLeft":0,"marginRight":0,"overwriteMargin":false,"width":6,"blockAlignment":"text-center","action":1,"actionData":{},"icon":"<path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18\"></path>","content":"Back","iconSide":0,"color":"gray","textColor":"black"},{"name":"Range Input","id":"range-input-jcmmbr6rl","type":"range-input","paddingTop":20,"paddingBottom":20,"paddingLeft":20,"paddingRight":20,"marginTop":0,"marginBottom":0,"marginLeft":0,"marginRight":0,"overwriteMargin":false,"width":6,"blockAlignment":"text-center","backendIdentifier":"range-input-jcmmbr6rl","value":null,"label":"","min":0,"max":100,"step":1,"prefix":"","suffix":"","default":50,"primaryColor":"primary","trackColor":"light-gray"}],"width":12}],"style":0,"branches":[{"id":"branch-zebyoz0y","name":"branch-tk626o2r","slides":[{"id":"slide-j2feyi4j","name":"slide-j2feyi4j","hierarchy":[{"name":"Continue Button","id":"continue-button-r5cbd953","type":"button","paddingTop":20,"paddingBottom":20,"paddingLeft":20,"paddingRight":20,"marginTop":0,"marginBottom":0,"marginLeft":0,"marginRight":0,"overwriteMargin":false,"width":6,"blockAlignment":"text-center","action":0,"actionData":{},"icon":"<path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3\"></path>","content":"Continue","iconSide":1,"color":"primary","textColor":"white"},{"name":"Back Button","id":"back-button-tbxqpfps","type":"button","paddingTop":20,"paddingBottom":20,"paddingLeft":20,"paddingRight":20,"marginTop":0,"marginBottom":0,"marginLeft":0,"marginRight":0,"overwriteMargin":false,"width":6,"blockAlignment":"text-center","action":1,"actionData":{},"icon":"<path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18\"></path>","content":"Back","iconSide":0,"color":"gray","textColor":"black"},{"name":"Continue Button","id":"continue-button-04zewmel","type":"button","paddingTop":20,"paddingBottom":20,"paddingLeft":20,"paddingRight":20,"marginTop":0,"marginBottom":0,"marginLeft":0,"marginRight":0,"overwriteMargin":false,"width":6,"blockAlignment":"text-center","action":0,"actionData":{},"icon":"<path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3\"></path>","content":"Continue","iconSide":1,"color":"primary","textColor":"white"},{"name":"Universal Button","id":"universal-button-2umnis4d","type":"button","paddingTop":20,"paddingBottom":20,"paddingLeft":20,"paddingRight":20,"marginTop":0,"marginBottom":0,"marginLeft":0,"marginRight":0,"overwriteMargin":false,"width":6,"blockAlignment":"text-center","action":0,"actionData":{},"icon":null,"content":"Button","iconSide":0,"color":"secondary","textColor":"white"},{"name":"Universal Button","id":"universal-button-66ss14ku","type":"button","paddingTop":20,"paddingBottom":20,"paddingLeft":20,"paddingRight":20,"marginTop":0,"marginBottom":0,"marginLeft":0,"marginRight":0,"overwriteMargin":false,"width":6,"blockAlignment":"text-center","action":0,"actionData":{},"icon":null,"content":"Button","iconSide":0,"color":"secondary","textColor":"white"}],"width":12}]}]}';

        $revisionData = [
            'SolarCalculator' => [
                [
                    'uuid'          => Uuid::uuid4()->toString(),
                    'name'          => 'main',
                    'parent_revision'=> $solarHistory,
                    'version'       => '5.0',
                    'description'   => 'Production version of Solar Calculator',
                    'actioned_by'   => 1,
                    'type'          => 'variant',
                    'created_at'    => now()->subDays(31),
                    'updated_at'    => now()->subDays(14),
                ],
                [
                    'uuid'          => $solarHistory,
                    'name'          => 'main',
                    'parent_revision' => null,
                    'version'       => '4.0',
                    'description'   => 'Production version of Solar Calculator',
                    'actioned_by'   => 2,
                    'type'          => 'history',
                    'created_at'    => now()->subDays(31),
                    'updated_at'    => now()->subDays(14),
                ],
                [
                    'uuid'          => Uuid::uuid4()->toString(),
                    'name'          => 'experimental',
                    'parent_revision' => null,
                    'version'       => '0.5',
                    'description'   => 'Development version of Solar Calculator',
                    'actioned_by'   => 1,
                    'type'          => 'variant',
                    'created_at'    => now()->subDays(2),
                    'updated_at'    => now(),
                ],
            ],
            'BathroomCalculator' => [
                [
                    'uuid'          => Uuid::uuid4()->toString(),
                    'name'          => 'main',
                    'parent_revision' => $bathroomHistory,
                    'version'       => '2.0',
                    'description'   => 'Production version of Bathroom Calculator',
                    'actioned_by'   => 1,
                    'type'          => 'variant',
                    'created_at'    => now()->subDays(7),
                    'updated_at'    => now()->subDays(2),
                    'main'          => true,
                ],
                [
                    'uuid'          => $bathroomHistory,
                    'name'          => 'main',
                    'parent_revision'        => null,
                    'version'       => '1.0',
                    'description'   => 'Production version of Bathroom Calculator',
                    'actioned_by'   => 2,
                    'type'          => 'history',
                    'created_at'    => now()->subDays(7),
                    'updated_at'    => now()->subDays(2),
                    'main'          => true,
                ],
                [
                    'uuid'          => Uuid::uuid4()->toString(),
                    'name'          => 'test2',
                    'parent_revision'        => null,
                    'version'       => '0.4',
                    'description'   => 'Development version of Bathroom Calculator',
                    'actioned_by'   => 1,
                    'type'          => 'variant',
                    'created_at'    => now()->subDays(2),
                    'updated_at'    => now(),
                ],
            ],
        ];

        foreach ($revisionData as $calculator => $revisions) {
            $this->line("Creating $calculator...");
            foreach($revisions as $revision) {
                $currentUuid = $revision['uuid'];
                $this->line("\tCreating Revision: $currentUuid");
                $isMainVersion = $revision['main'] ?? false;
                $revisionData = array_filter($revision, fn($v) => !in_array($v, ['uuid', 'main']), ARRAY_FILTER_USE_KEY);

                $firestore->addDocumentToCollection("Flows/$calculator/RevisionMeta", $currentUuid, $revisionData);
                $firestore->addDocumentToCollection("Flows/$calculator/Revisions", $currentUuid, [
                    "current" => preg_match("/\d\.0/", $revision['version']) ? $testFlowString : null,
                    "working" => preg_match("/\d\.0/", $revision['version']) ? null : $testFlowString,
                ]);

                if ($isMainVersion) {
                    $firestore->updateDocument("Flows", $calculator, [ 'versions.production' => $currentUuid]);
                }
            }
        }

        $this->info("\nFirestore database has been populated with test Flows.\n");

        return 0;
    }

}
