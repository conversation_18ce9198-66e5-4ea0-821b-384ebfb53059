<?php

namespace App\Console\Commands;

use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\CompanyLocation;
use App\Services\DatabaseHelperService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class UpdateStateInAddressesTable extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:states-in-addresses';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Resolve issues with state being the name rather than ABBR in addresses table.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        foreach($this->getAffectedStates() as $state) {
            $this->updateState($state);
        }

        return Command::SUCCESS;
    }

    /**
     * Handles updating a specific state.
     *
     * @param string $state
     * @return void
     */
    protected function updateState(string $state): void
    {
        /** @var Location $location */
        $location = Location::query()->where(Location::STATE, $state)->where(Location::TYPE, Location::TYPE_STATE)->first();

        if($location) {
            $result = $this->confirm("Updating addresses with '{$state}' to '{$location->state_abbr}'. Is this okay?");

            if($result) {
                $this->info("Updating {$state}...");

                $ids = $this->getAffectedIdsForState($state);

                foreach($ids->chunk(100) as $chunk) {
                    Address::query()->whereIn(Address::FIELD_ID, $chunk)->update([Address::TABLE.'.'.Address::FIELD_STATE => $location->state_abbr]);
                }
            }
        } else {
            $this->error("Failed to find location for '{$state}'.");
        }
    }

    /**
     * Returns the affected ids for a given state.
     *
     * @param string $state
     * @return Collection
     */
    protected function getAffectedIdsForState(string $state): Collection
    {
        return $this->getBaseQuery()->select([Address::TABLE.'.'.Address::FIELD_ID])
            ->where(Address::TABLE.'.'.Address::FIELD_STATE, $state)
            ->get()
            ->pluck(Address::FIELD_ID);
    }

    /**
     * Returns the states that are affected.
     *
     * @return Collection
     */
    protected function getAffectedStates(): Collection
    {
        return $this->getBaseQuery()->select([Address::TABLE . '.' . Address::FIELD_STATE])
            ->groupBy(Address::FIELD_STATE)
            ->get()
            ->pluck(Address::FIELD_STATE);
    }

    /**
     * Builds the base query for finding affected locations.
     *
     * @return Builder
     */
    protected function getBaseQuery(): Builder
    {
        $query = Address::query();

        $query->join(CompanyLocation::TABLE, CompanyLocation::TABLE . '.' . CompanyLocation::FIELD_ADDRESS_ID, '=', Address::TABLE . '.' . Address::FIELD_ID)
            ->leftJoin(Location::TABLE, Location::TABLE . '.' . Location::STATE_ABBREVIATION, '=', Address::TABLE . '.' . Address::FIELD_STATE)
            ->where(Location::TABLE . '.' . Location::ID, null)
            ->where(Address::FIELD_COUNTRY, 'US')
            ->where(DB::raw('CHAR_LENGTH(addresses.state)'), '>', 2);

        return $query;
    }
}
