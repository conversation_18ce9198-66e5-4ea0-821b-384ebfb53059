<?php

namespace App\Console\Commands;

use App\Jobs\CalculateAvailableBudgetByZipCodeJob;
use Illuminate\Console\Command;

class CalculateAvailableBudgets extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'calculate:available-budgets';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Calculates and stores available budget data';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        CalculateAvailableBudgetByZipCodeJob::dispatchSync();
    }
}
