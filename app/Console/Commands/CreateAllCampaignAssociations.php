<?php

namespace App\Console\Commands;

use App\Models\Odin\Company;
use App\Services\QueueHelperService;
use Illuminate\Bus\Batch;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Bus;
use Throwable;

class CreateAllCampaignAssociations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'create:campaign-associations {--batch-size=100 : The size of batches to process} {--admin-status=*0 : Company admin_status values to process }';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create Campaign Associations for all existing campaigns.';

    /**
     * Execute the console command.
     * @throws Throwable
     * @return int
     */
    public function handle()
    {
        $this->line("Creating Campaign Associations for existing campaigns...");

        $batchSize = $this->option('batch-size');
        $allowedAdminStatuses = $this->option('admin-status') ?? [ 0 ];
        $stringStatus = join(", ", $allowedAdminStatuses);

        $queueName = QueueHelperService::QUEUE_NAME_LEGACY_MIGRATION;

        $this->line("Processing Companies with admin statuses $stringStatus and batch size $batchSize");

        $companyIds = Company::query()
            ->whereIn(Company::FIELD_ADMIN_STATUS, $allowedAdminStatuses)
            ->orWhereNull(Company::FIELD_ADMIN_STATUS)
            ->select(Company::FIELD_ID)
            ->pluck(Company::FIELD_ID);
        $companiesCount = $companyIds->count();
        $this->line("Processing $companiesCount Companies on queue:$queueName....");

        $chunkedIds = $companyIds->chunk($batchSize);
        $batchJobs = $chunkedIds->map(fn($idChunk) => new CreateAllCampaignAssociationsJob($idChunk->toArray()));

        $batch = Bus::batch($batchJobs)
            ->catch(fn(Batch $batch, Throwable $throwable) => logger()->error("Campaign Association error: ".$throwable->getMessage()))
            ->name("Campaign Association Migration")
            ->allowFailures()
            ->onConnection(QueueHelperService::QUEUE_CONNECTION)
            ->onQueue($queueName)
            ->dispatch();

        $bar = $this->output->createProgressBar($batch->totalJobs);
        $bar->setFormat("%current%/%max% campaign association jobs dispatched. Elapsed: %elapsed%");
        $bar->setRedrawFrequency(1);

        $bar->start();
        while(!$batch->finished() && !$batch->cancelled()) {
            $batch = $batch->fresh();
            $bar->setProgress($batch->processedJobs());
        }
        $bar->finish();

        $this->info("\nAll jobs dispatched to queue:$queueName.");

        return 0;
    }
}
