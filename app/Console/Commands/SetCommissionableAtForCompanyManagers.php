<?php

namespace App\Console\Commands;

use App\Enums\RoleType;
use App\Models\CompanyUserRelationship;
use App\Models\Odin\Company;
use App\Models\Odin\ProductAssignment;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

class SetCommissionableAtForCompanyManagers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'relationships:populate-commissionable-at-company-managers';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Populate commissionable_at for BDM relationships where it is currently unset';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $this->info('Starting....');
        $this->info('Processing BDMs');
        $this->processBdms();
        $this->newLine();
        $this->info('Processing AMs');
        $this->processAms();
        $this->newLine();
        $this->info('Processing Oms');
        $this->processOms();
        $this->newLine();
        $this->info('Finished');
    }

    protected function processBdms(): void
    {
        $progressBar = $this->output->createProgressBar($this->getQuery(RoleType::BUSINESS_DEVELOPMENT_MANAGER->value, false)->count());

        $progressBar->setFormat('%current%/%max% [%bar%] %percent:3s%% | %remaining%');

        $this->getQuery(RoleType::BUSINESS_DEVELOPMENT_MANAGER->value, false)
            ->chunk(100, function (Collection $relationships) use ($progressBar) {
                $relationships->each(function (CompanyUserRelationship $relationship) {
                    if ($relationship->commissionable_at) {
                        return;
                    }

                    $relationship->commissionable_at = $relationship->created_at->addDays(45);
                });

                $this->update($relationships);
                $progressBar->advance($relationships->count());
            });
    }

    protected function processAms(): void
    {
        $progressBar = $this->output->createProgressBar($this->getQuery(RoleType::ACCOUNT_MANAGER->value)->count());

        $progressBar->setFormat('%current%/%max% [%bar%] %percent:3s%% | %remaining%');

        $this->getQuery(RoleType::ACCOUNT_MANAGER->value)
            ->chunk(100, function (Collection $relationships) use ($progressBar) {
                $relationships->each(function (CompanyUserRelationship $relationship) {
                    if ($relationship->commissionable_at) {
                        return;
                    }

                    /** @var ProductAssignment|null $productAssignment */
                    $productAssignment = $relationship->company->productAssignments->first();

                    if ($productAssignment) {
                        $relationship->commissionable_at = $productAssignment->delivered_at->addDays(90);
                    }
                });

                $this->update($relationships);
                $progressBar->advance($relationships->count());
            });
    }

    protected function processOms(): void
    {
        $progressBar = $this->output->createProgressBar($this->getQuery(RoleType::ONBOARDING_MANAGER->value)->count());

        $progressBar->setFormat('%current%/%max% [%bar%] %percent:3s%% | %remaining%');

        $this->getQuery(RoleType::ONBOARDING_MANAGER->value)
            ->chunk(100, function (Collection $relationships) use ($progressBar) {
                $relationships->each(function (CompanyUserRelationship $relationship) {
                    if ($relationship->commissionable_at) {
                        return;
                    }

                    /** @var ProductAssignment|null $productAssignment */
                    $productAssignment = $relationship->company->productAssignments->first();

                    if ($productAssignment) {
                        $relationship->commissionable_at = $productAssignment->delivered_at->addDays(90);
                    }
                });

                $this->update($relationships);
                $progressBar->advance($relationships->count());
            });
    }

    /**
     * @param Collection $relationships
     *
     * @return void
     */
    protected function update(Collection $relationships): void
    {
        CompanyUserRelationship::query()->upsert(
            values: $relationships
                ->map(fn(CompanyUserRelationship $relationship) => [
                    CompanyUserRelationship::FIELD_ID               => $relationship->id,
                    CompanyUserRelationship::FIELD_COMMISIONABLE_AT => $relationship->commissionable_at
                ])
                ->toArray(),
            uniqueBy: [CompanyUserRelationship::FIELD_ID],
            update: [CompanyUserRelationship::FIELD_COMMISIONABLE_AT]
        );
    }

    /**
     * @param string $role
     * @param bool $loadRelation
     * @param int $days
     *
     * @return Builder
     */
    protected function getQuery(string $role, bool $loadRelation = true, int $days = 120): Builder
    {
        $query = CompanyUserRelationship::query()
            ->whereHas(CompanyUserRelationship::RELATION_ROLE, fn(Builder $builder) => $builder->where('name', $role));

        if ($loadRelation) {
            $query->with(CompanyUserRelationship::RELATION_COMPANY . '.' . Company::RELATION_PRODUCT_ASSIGNMENTS, function (HasMany $query) use ($days) {
                $query->select([
                    ProductAssignment::FIELD_ID,
                    ProductAssignment::FIELD_COMPANY_ID,
                    ProductAssignment::FIELD_DELIVERED_AT,
                    ProductAssignment::CREATED_AT,
                    ProductAssignment::UPDATED_AT
                ])
                    ->where(ProductAssignment::FIELD_CHARGEABLE, true)
                    ->where(ProductAssignment::FIELD_DELIVERED, true)
                    ->where(ProductAssignment::FIELD_DELIVERED_AT, '>=', now()->subDays($days))
                    ->oldest(ProductAssignment::FIELD_DELIVERED_AT)
                    ->limit(1);
            });
        }

        return $query;
    }
}
