<?php

namespace App\Console\Commands;

use App\Jobs\CalculateAndStoreExpertRatingsJob;
use Illuminate\Console\Command;

class CalculateAndStoreExpertRatings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'calculate:expert-ratings';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Calculates and stores expert ratings against company data';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        CalculateAndStoreExpertRatingsJob::dispatchSync();
    }
}
