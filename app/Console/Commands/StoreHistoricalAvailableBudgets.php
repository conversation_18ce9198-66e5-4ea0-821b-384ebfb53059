<?php

namespace App\Console\Commands;

use App\Jobs\CalculateHistoricalAvailableBudgetsJob;
use Illuminate\Console\Command;

class StoreHistoricalAvailableBudgets extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'store:historical-available-budgets';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Store a snapshot of historical available budgets';

    /**
     * Execute the console command.
     *
     */
    public function handle()
    {
        CalculateHistoricalAvailableBudgetsJob::dispatch();
    }
}
