<?php

namespace App\Console\Commands\Deepgram;

use App\Models\Conference\ConferenceTranscript;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class AnalyzeConferenceTranscriptWithDeepgramCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:analyze-conference-transcript-with-deepgram {conferenceTranscriptId}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Take a conference transcript id and pushes a job to analyze its text with Deepgram';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        if (blank(config('services.deepgram.api_key'))) {
            $this->error('You must set the DEEPGRAM_API_KEY environment variable to run this command.');

            return self::FAILURE;
        }

        try {
            ConferenceTranscript::findOrFail($this->argument('conferenceTranscriptId'))->analyzeWithDeepgram();
        } catch (ModelNotFoundException $e) {
            $this->error($e->getMessage());

            return self::FAILURE;
        }

        $this->info('Job dispatched...');

        return self::SUCCESS;
    }
}
