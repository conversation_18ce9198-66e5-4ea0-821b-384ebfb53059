<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class MigrateAllLegacyTables extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'legacy-migrate:all';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Runs all the legacy model migration commands.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->runCommand('legacy-migrate:addresses', [], $this->output);
        $this->runCommand('legacy-migrate:companies', [], $this->output);
        $this->runCommand('legacy-migrate:company-users', [], $this->output);
        $this->runCommand('legacy-migrate:company-contacts', [], $this->output);
        $this->runCommand('legacy-migrate:company-reviews', [], $this->output);
        $this->runCommand('legacy-migrate:quotes', [], $this->output);
        $this->runCommand('legacy-migrate:quote-companies', [], $this->output);

        return 0;
    }
}
