<?php

namespace App\Actions\AffiliatesPortal;

use App\Models\Affiliates\Affiliate;
use App\Models\Affiliates\Campaign;
use App\Models\Affiliates\Payout;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductAffiliateRecord;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCancellation;
use App\Models\Odin\ProductRejection;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class GetStatisticsAction
{
    /**
     * @param int $start
     * @param int $end
     * @param array $ids
     * @param string $type
     * @return array
     * @throws Exception
     */
    public function handle(int $start, int $end, array $ids, string $type): array
    {
        if(!in_array($type, ['affiliates', 'campaigns'], true)) {
            throw new Exception('Invalid type');
        }

        $statistics = [];
        foreach($ids as $id) {
            $statistics[$id] = [
                'total_revenue' => 0,
                'legs_sold' => 0,
                'leads_created' => 0,
                'leads_good_to_sell' => 0,
                'total_payout' => 0,
            ];
        }

        $goodToSellField = ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_GOOD_TO_SELL;
        $costField = ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_COST;
        $payoutField = Payout::TABLE.'.'.Payout::FIELD_CENT_VALUE;
        $productAssignmentIdField = ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_ID;
        $consumerProductIdField = ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ID;

        $revenueSubquery = ProductAssignment::query()
            ->select([
                DB::raw("SUM($costField) as total_revenue"),
                DB::raw("COALESCE(COUNT(DISTINCT {$productAssignmentIdField}), 0) AS legs_sold"),
                ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CONSUMER_PRODUCT_ID.' as consumer_product_id',
            ])
            ->join(
                ConsumerProduct::TABLE,
                ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CONSUMER_PRODUCT_ID,
                '=',
                ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ID
            )
            ->whereBetween(ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CREATED_AT, [
                Carbon::createFromTimestamp($start, 'UTC')->format('Y-m-d H:i:s'),
                Carbon::createFromTimestamp($end, 'UTC')->format('Y-m-d H:i:s')
            ])
            ->whereNotExists(function ($subQuery) use ($productAssignmentIdField) {
                $subQuery
                    ->from(ProductRejection::TABLE)
                    ->select('*')
                    ->whereColumn($productAssignmentIdField, '=', ProductRejection::TABLE.'.'.ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID);
            })
            ->whereNotExists(function ($subQuery) use ($productAssignmentIdField) {
                $subQuery
                    ->from(ProductCancellation::TABLE)
                    ->select('*')
                    ->whereColumn($productAssignmentIdField, '=', ProductCancellation::TABLE.'.'.ProductCancellation::FIELD_PRODUCT_ASSIGNMENT_ID);
            })
            ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CHARGEABLE, true)
            ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_DELIVERED, true)
            ->groupBy('consumer_product_id');

        ConsumerProductAffiliateRecord::query()
            ->join(ConsumerProduct::TABLE, function($join) use ($start, $end) {
                $join
                    ->on(
                        ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CONSUMER_PRODUCT_AFFILIATE_RECORD_ID,
                        '=',
                        ConsumerProductAffiliateRecord::TABLE.'.'.ConsumerProductAffiliateRecord::FIELD_ID
                    )
                    ->whereBetween(ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CREATED_AT, [
                        Carbon::createFromTimestamp($start, 'UTC')->format('Y-m-d H:i:s'),
                        Carbon::createFromTimestamp($end, 'UTC')->format('Y-m-d H:i:s')
                    ]);
            })
            ->join(
                Payout::TABLE,
                ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ID,
                '=',
                Payout::TABLE.'.'.Payout::FIELD_CONSUMER_PRODUCT_ID
            )
            ->leftJoinSub(
                $revenueSubquery,
                'sub',
                'sub.consumer_product_id',
                '=',
                ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ID,
            )
            ->when($type === 'affiliates', function (Builder $query) use ($ids) {
                $query
                    ->join(Campaign::TABLE, function($join) {
                        $join->on(
                            Campaign::TABLE.'.'.Campaign::FIELD_ID,
                            '=',
                            ConsumerProductAffiliateRecord::TABLE.'.'.ConsumerProductAffiliateRecord::FIELD_CAMPAIGN_ID
                        );
                    })
                    ->join(Affiliate::TABLE, function($join) {
                        $join->on(
                            Affiliate::TABLE.'.'.Affiliate::FIELD_ID,
                            '=',
                            Campaign::TABLE.'.'.Campaign::FIELD_AFFILIATE_ID
                        );
                    })
                    ->whereIn(Affiliate::TABLE.'.'.Affiliate::FIELD_UUID, $ids)
                    ->groupBy([Affiliate::TABLE.'.'.Affiliate::FIELD_UUID])
                    ->selectRaw(Affiliate::TABLE.'.'.Affiliate::FIELD_UUID." AS id_col");
            })
            ->when($type === 'campaigns', function (Builder $query) use ($ids) {
                $query
                    ->whereIn(ConsumerProductAffiliateRecord::TABLE.'.'.ConsumerProductAffiliateRecord::FIELD_CAMPAIGN_ID, $ids)
                    ->groupBy([ConsumerProductAffiliateRecord::TABLE.'.'.ConsumerProductAffiliateRecord::FIELD_CAMPAIGN_ID])
                    ->selectRaw(ConsumerProductAffiliateRecord::TABLE.'.'.ConsumerProductAffiliateRecord::FIELD_CAMPAIGN_ID." AS id_col");
            })
            ->selectRaw(implode(',', [
                'COALESCE(SUM(sub.total_revenue), 0) AS total_revenue',
                "COALESCE(SUM($payoutField)) AS atomic_payout",
                'COALESCE(SUM(sub.legs_sold), 0) AS legs_sold',
                "COALESCE(COUNT(DISTINCT {$consumerProductIdField}), 0) AS leads_created",
                "COALESCE(COUNT(DISTINCT IF({$goodToSellField}, {$consumerProductIdField}, NULL)), 0) AS leads_good_to_sell",
            ]))
            ->get()
            ->each(function($row) use (&$statistics) {
                $statistics[$row->id_col] = [
                    'total_revenue' => $row->total_revenue,
                    'total_payout' => $row->atomic_payout,
                    'legs_sold' => $row->legs_sold,
                    'leads_created' => $row->leads_created,
                    'leads_good_to_sell' => $row->leads_good_to_sell,
                ];
            });

        return $statistics;
    }
}
