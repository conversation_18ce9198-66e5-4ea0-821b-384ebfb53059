<?php

namespace App\Actions;

use App\Models\WatchdogVideo;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Http;
use Sentry\Severity;

use function Sentry\captureMessage;

class GetWatchdogPlaybackUrl
{
    public function handle(string $source): JsonResponse
    {
        abort_if(blank(config('watchdog.url')), 500, 'No url given');
        abort_if(blank(config('watchdog.token')), 500, 'No access token given');

        $response = Http::withToken(config('watchdog.token'))
            ->acceptJson()
            ->get(rtrim(config('watchdog.url'), '/')."/api/videos/{$source}/playback");

        if ($response->notFound()) {
            WatchdogVideo::whereHas('consumer', function ($query) use ($source) {
                $query->where('reference', $source);
            })->delete();
        }

        if ($response->failed()) {
            captureMessage('Failed to get video playback url for '.$source, Severity::warning());

            return response()->json([
                'watchdog_server_json' => $response->json(),
                'watchdog_server_body' => $response->body(),
                'watchdog_server_status' => $response->status(),
            ], 424);
        }

        return response()->json(['data' => $response->json()]);
    }
}
