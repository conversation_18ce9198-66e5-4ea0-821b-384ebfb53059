<?php

namespace App\Actions\Company;

use App\Enums\Company\CompanySystemStatus;
use App\Enums\CompanySalesStatus;
use App\Enums\Odin\Industry;
use App\Models\Odin\Company;
use App\Models\Odin\ProductAssignment;
use App\Services\Prospects\BusinessDevelopmentManagerAssignmentService;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;

class FindUndersoldCompanyByIndustry
{
    public function run(Industry $industry, array $timezones = []): ?Company
    {
        return Company::query()
            ->distinct()
            ->select('companies.*')
            ->doesntHave('accountManager')
            ->doesntHave('businessDevelopmentManager')
            ->doesntHave('salesDevelopmentRepresentative')
            ->joinSub($this->getLatestAssignments(), 'latest_assignments', function(JoinClause $join) {
                $join->on('companies.id', 'latest_assignments.company_id');
            })->join('company_industries', 'companies.id', 'company_industries.company_id')
            ->join('industries', 'company_industries.industry_id', 'industries.id')
            ->whereDate('latest_assignments.last_assignment', '<=', now()->subDay(30))
            // ->whereDate('latest_assignments.last_assignment', '>=', now()->subYears(2))
            ->where('industries.slug', $industry->getSlug())
            ->when($timezones, fn ($query) => $query->whereHas('primaryLocation.address.usZipCode', fn ($query) => $query->whereIn('time_zone', $timezones)))
            ->orderByDesc('latest_assignments.last_assignment')
            ->first();
    }

    private function getLatestAssignments()
    {
        return ProductAssignment::query()
            ->selectRaw('company_id, max(delivered_at) as last_assignment')
            ->join('companies', 'product_assignments.company_id', 'companies.id')
            ->where('companies.system_status', CompanySystemStatus::ELIGIBLE)
            ->whereNotIn('companies.sales_status', CompanySalesStatus::disqualifyingStatuses())
            ->whereChargeable(true)
            ->whereDelivered(true)
            ->groupBy('company_id');
    }
}
