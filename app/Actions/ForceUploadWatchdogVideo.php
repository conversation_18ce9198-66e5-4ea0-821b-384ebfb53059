<?php

declare(strict_types=1);

namespace App\Actions;

use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Http;

class ForceUploadWatchdogVideo
{
    public function handle(string $video): JsonResponse
    {
        abort_if(blank(config('watchdog.url')), 500, 'No url given');
        abort_if(blank(config('watchdog.token')), 500, 'No access token given');

        $response = Http::withToken(config('watchdog.token'))
            ->acceptJson()
            ->get(rtrim(config('watchdog.url'), '/')."/api/videos/{$video}/upload");

        if ($response->failed()) {
            return response()->json([
                'watchdog_server_json' => $response->json(),
                'watchdog_server_body' => $response->body(),
                'watchdog_server_status' => $response->status(),
            ], 424);
        }

        return response()->json(['data' => $response->json()]);
    }
}
