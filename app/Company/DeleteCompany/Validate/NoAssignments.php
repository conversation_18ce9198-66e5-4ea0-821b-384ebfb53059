<?php

namespace App\Company\DeleteCompany\Validate;

use App\Models\Odin\ProductAssignment;

class NoAssignments implements DeleteValidatorContract
{
    function validate(int $companyId): bool
    {
        return ProductAssignment::query()->where(ProductAssignment::FIELD_COMPANY_ID, $companyId)->doesntExist();
    }

    function title(): string
    {
        return 'Company has no Product Assignments';
    }

    function failReason(): string
    {
        return 'Company has Product assignments';
    }
}
