<?php

namespace App\Company\DeleteCompany\Validate;

use App\Models\Billing\Invoice;

class NoInvoices implements DeleteValidatorContract
{
    function validate(int $companyId): bool
    {
        return Invoice::query()->where(Invoice::FIELD_COMPANY_ID, $companyId)->doesntExist();
    }

    function title(): string
    {
        return 'Company has no Invoices';
    }

    function failReason(): string
    {
        return 'Company has Invoices';
    }
}
