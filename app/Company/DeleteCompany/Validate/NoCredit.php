<?php

namespace App\Company\DeleteCompany\Validate;

use App\Models\Billing\Credit;

class NoCredit implements DeleteValidatorContract
{
    function validate(int $companyId): bool
    {
        return Credit::query()->where(Credit::FIELD_COMPANY_ID, $companyId)->doesntExist();
    }

    function title(): string
    {
        return 'Company has no Credit';
    }

    function failReason(): string
    {
        return 'Company has Credit';
    }
}
