<?php

namespace App\Company\DeleteCompany\Validate;

use App\Models\Billing\BillingProfile;

class NoBillingProfiles implements DeleteValidatorContract
{
    function validate(int $companyId): bool
    {
        return BillingProfile::query()->where(BillingProfile::FIELD_COMPANY_ID, $companyId)->doesntExist();
    }

    function title(): string
    {
        return 'Company has no Billing Profiles';
    }

    function failReason(): string
    {
        return 'Company has Billing Profiles';
    }
}
