<?php

namespace App\Company\DeleteCompany\Validate;

use App\Models\ConsumerReviews\Review;

class NoReviews implements DeleteValidatorContract
{
    function validate(int $companyId): bool
    {
        return Review::query()->where(Review::FIELD_COMPANY_ID, $companyId)->doesntExist();
    }

    function title(): string
    {
        return 'Company has no reviews';
    }

    function failReason(): string
    {
        return 'Company has reviews';
    }
}
