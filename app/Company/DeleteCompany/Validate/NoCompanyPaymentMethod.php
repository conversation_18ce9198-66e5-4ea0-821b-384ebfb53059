<?php

namespace App\Company\DeleteCompany\Validate;

use App\Models\Billing\CompanyPaymentMethod;

class NoCompanyPaymentMethod implements DeleteValidatorContract
{
    function validate(int $companyId): bool
    {
        return CompanyPaymentMethod::query()->where(CompanyPaymentMethod::FIELD_COMPANY_ID, $companyId)->doesntExist();
    }

    function title(): string
    {
        return 'Company has no Payment Methods';
    }

    function failReason(): string
    {
        return 'Company has Payment Methods';
    }
}
