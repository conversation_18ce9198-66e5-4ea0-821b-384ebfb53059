<?php

namespace App\Company\DeleteCompany\Deletes;

use Illuminate\Database\Eloquent\Builder;

abstract class DeleteContract
{
    /**
     * Class string of the model to delete
     * @return string
     */
    abstract function modelClass(): string;

    /**
     * Base delete query
     * @param int $companyId
     * @return Builder
     */
    abstract function query(int $companyId): Builder;

    /**
     * @return string
     */
    function title(): string
    {
        return $this->modelClass()::TABLE;
    }

    /**
     * @param int $companyId
     * @return int
     */
    function preview(int $companyId): int
    {
        return $this->query($companyId)->count();
    }

    /**
     * @param int $companyId
     * @return mixed
     */
    function delete(int $companyId): mixed
    {
        $recordsDeleted = $this->query($companyId)->forceDelete();

        $title = $this->title();

        logger()->info("Deleted {$recordsDeleted} records from $title");

        return $recordsDeleted;
    }
}
