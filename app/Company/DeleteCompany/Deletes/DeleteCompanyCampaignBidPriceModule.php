<?php

namespace App\Company\DeleteCompany\Deletes;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\BaseCompanyCampaignModule;
use App\Models\Campaigns\Modules\Bidding\CompanyCampaignBidPriceModule;
use Illuminate\Database\Eloquent\Builder;

class DeleteCompanyCampaignBidPriceModule extends DeleteContract
{
    function query(int $companyId): Builder
    {
        return CompanyCampaignBidPriceModule::query()
            ->whereHas(BaseCompanyCampaignModule::RELATION_CAMPAIGN, function(Builder $query) use ($companyId) {
                $query->where(CompanyCampaign::FIELD_COMPANY_ID, $companyId);
            });
    }

    function modelClass(): string
    {
        return CompanyCampaignBidPriceModule::class;
    }
}
