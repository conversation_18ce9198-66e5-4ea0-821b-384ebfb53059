<?php

namespace App\Company\DeleteCompany\Deletes;

use Illuminate\Database\Eloquent\Builder;

class BaseDelete extends DeleteContract
{
    const string FIELD_COMPANY_ID = 'company_id';
    public function __construct(protected string $modelClass)
    {}

    function query(int $companyId): Builder
    {
        return $this->modelClass::query()->where(self::FIELD_COMPANY_ID, $companyId);
    }

    function modelClass(): string
    {
        return $this->modelClass;
    }
}
