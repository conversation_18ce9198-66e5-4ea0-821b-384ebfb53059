<?php

namespace App\Company\DeleteCompany\Deletes;

use App\Models\Action;
use Illuminate\Database\Eloquent\Builder;

class DeleteAction extends DeleteContract
{
    function query(int $companyId): Builder
    {
        return Action::query()
            ->where(Action::FIELD_FOR_RELATION_TYPE, Action::RELATION_TYPE_COMPANY)
            ->where(Action::FIELD_FOR_ID, $companyId);
    }

    function modelClass(): string
    {
        return Action::class;
    }
}
