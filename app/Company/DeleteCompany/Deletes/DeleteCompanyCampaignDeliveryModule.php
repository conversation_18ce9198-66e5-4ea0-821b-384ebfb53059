<?php

namespace App\Company\DeleteCompany\Deletes;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModule;
use Illuminate\Database\Eloquent\Builder;

class DeleteCompanyCampaignDeliveryModule extends DeleteContract
{
    function query(int $companyId): Builder
    {
        return CompanyCampaignDeliveryModule::query()
            ->whereHas(CompanyCampaignDeliveryModule::RELATION_CAMPAIGN, function(Builder $query) use ($companyId) {
                $query->where(CompanyCampaign::FIELD_COMPANY_ID, $companyId);
            });
    }

    function modelClass(): string
    {
        return CompanyCampaignDeliveryModule::class;
    }
}
