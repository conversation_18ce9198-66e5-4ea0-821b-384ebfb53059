<?php

namespace App\Company\DeleteCompany\Deletes;

use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleCRM;
use App\Models\Campaigns\Modules\Delivery\CompanyCRMTemplate;
use Illuminate\Database\Eloquent\Builder;

class DeleteCompanyCampaignDeliveryModuleCRM extends DeleteContract
{
    function query(int $companyId): Builder
    {
        return CompanyCampaignDeliveryModuleCRM::query()
            ->whereHas(CompanyCampaignDeliveryModuleCRM::RELATION_TEMPLATE, function(Builder $query) use ($companyId) {
                $query->where(CompanyCRMTemplate::FIELD_COMPANY_ID, $companyId);
            });
    }

    function modelClass(): string
    {
        return CompanyCampaignDeliveryModuleCRM::class;
    }
}
