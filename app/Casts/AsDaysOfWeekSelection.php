<?php

namespace App\Casts;

use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;
use \Exception;

/**
 * Stores a comma-separated string in the database
 * Casts to an array of integers that map to day of the week
 * Sunday = 0 ... Saturday = 6
 */
class AsDaysOfWeekSelection implements CastsAttributes
{
    /**
     * Cast the given value.
     *
     * @param  array<string, mixed>  $attributes
     */
    public function get(Model $model, string $key, mixed $value, array $attributes): mixed
    {
        return $value
            ? array_map(fn($v) => (int) $v, explode(',', $value))
            : [];
    }

    /**
     * Prepare the given value for storage.
     *
     * @param array<string, mixed> $attributes
     * @throws Exception
     */
    public function set(Model $model, string $key, mixed $value, array $attributes): mixed
    {
        if (gettype($value) === 'array') {
            $validDays = array_filter($value, fn($value) => $value >= 0 && $value <= 6);
            return implode(',', $validDays);
        }
        else
            throw new Exception("Weekdays must be an array of integers 0 to 6");
    }
}
