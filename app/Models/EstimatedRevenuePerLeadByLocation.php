<?php

namespace App\Models;

use App\Models\Legacy\Location;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * Class EstimatedRevenuePerLeadByLocation
 * @package App\Models
 * @property integer $id
 * @property integer $location_id
 * @property float $estimated_revenue
 * @property int $available_companies
 * @property string $industry_type
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class EstimatedRevenuePerLeadByLocation extends Model
{
    const TABLE = 'estimated_revenue_per_lead_by_locations';

    const FIELD_ID                                  = 'id';
    const FIELD_LOCATION_ID                         = 'location_id';
    const FIELD_ESTIMATED_REVENUE                   = 'estimated_revenue';
    const FIELD_AVAILABLE_COMPANIES                 = 'available_companies';
    const FIELD_INDUSTRY_TYPE                       = 'industry_type';
    const FIELD_INDUSTRY_ID                         = 'industry_id';
    const FIELD_COUNTY_FIPS                         = 'county_fips';
    const FIELD_NEGATIVE_ZIP_CODES                  = 'negative_zip_codes';
    const FIELD_CREATED_AT                          = 'created_at';
    const FIELD_UPDATED_AT                          = 'updated_at';

    const INDUSTRY_TYPE_SOLAR   = 'solar';
    const INDUSTRY_TYPE_ROOFING = 'roofing';

    const RELATION_LOCATION = 'location';

    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    /**
     * @return HasOne
     */
    public function location(): HasOne
    {
        return $this->hasOne(Location::class, Location::ID, self::FIELD_LOCATION_ID);
    }
}
