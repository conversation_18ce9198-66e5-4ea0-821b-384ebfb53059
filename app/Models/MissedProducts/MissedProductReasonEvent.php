<?php

namespace App\Models\MissedProducts;

use App\Enums\MissedProducts\MissedProductReasonEventType;
use App\Models\BaseModel;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\Company;
use Carbon\Carbon;
use Database\Factories\Odin\MissedProductReasonEventFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $company_id
 * @property int|null $company_campaign_id
 * @property MissedProductReasonEventType $event_type
 * @property int|null $total
 * @property Carbon $started_at
 * @property Carbon $ended_at
 *
 * @property-read Company $company
 * @property-read CompanyCampaign $companyCampaign
 */
class MissedProductReasonEvent extends BaseModel
{
    use HasFactory;
    const string TABLE = 'missed_product_reason_events';

    const string FIELD_ID                  = 'id';
    const string FIELD_COMPANY_ID          = 'company_id';
    const string FIELD_COMPANY_CAMPAIGN_ID = 'company_campaign_id';
    const string FIELD_EVENT_TYPE          = 'event_type';
    const string FIELD_STARTED_AT          = 'started_at';
    const string FIELD_ENDED_AT            = 'ended_at';
    const string FIELD_TOTAL               = 'total';

    const string RELATION_COMPANY          = 'company';
    const string RELATION_COMPANY_CAMPAIGN = 'companyCampaign';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_ENDED_AT   => 'datetime',
        self::FIELD_STARTED_AT => 'datetime',
        self::FIELD_EVENT_TYPE => MissedProductReasonEventType::class,
    ];

    protected static function newFactory(): MissedProductReasonEventFactory
    {
        return MissedProductReasonEventFactory::new();
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);

    }

    /**
     * @return BelongsTo
     */
    public function companyCampaign(): BelongsTo
    {
        return $this->belongsTo(CompanyCampaign::class, self::FIELD_COMPANY_CAMPAIGN_ID, CompanyCampaign::FIELD_ID);
    }
}
