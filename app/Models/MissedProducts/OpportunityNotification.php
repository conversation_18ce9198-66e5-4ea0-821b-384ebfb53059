<?php

namespace App\Models\MissedProducts;

use App\Enums\OpportunityNotifications\OpportunityNotificationDeliveryMethods;
use App\Models\BaseModel;
use App\Models\Odin\Company;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property string $recipients
 * @property Carbon|null $sent_at
 * @property OpportunityNotificationDeliveryMethods|int $deliveryMethod
 * @property string $content
 * @property int $view_count
 * @property int $company_id
 * @property int $config_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property-read Company $company
 * @property-read OpportunityNotificationConfig $config
 */
class OpportunityNotification extends BaseModel
{
    use HasFactory;

    const string TABLE = 'opportunity_notifications';

    const string FIELD_ID              = 'id';
    const string FIELD_RECIPIENTS      = 'recipients';
    const string FIELD_SENT_AT         = 'sent_at';
    const string FIELD_DELIVERY_METHOD = 'delivery_method';
    const string FIELD_CONTENT         = 'content';
    const string FIELD_VIEW_COUNT      = 'view_count';

    const string FIELD_COMPANY_ID = 'company_id';
    const string FIELD_CONFIG_ID  = 'config_id';
    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';

    const string RELATION_COMPANY = 'company';
    const string RELATION_CONFIG  = 'config';

    protected $guarded = [self::FIELD_ID];

    protected $table = self::TABLE;

    protected $casts = [
        self::FIELD_DELIVERY_METHOD => OpportunityNotificationDeliveryMethods::class,
        self::FIELD_SENT_AT         => 'datetime'
    ];

    /**
     * Defines relationship to the company.
     *
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }
    
    /**
     * Defines relationship to the notification config.
     *
     * @return BelongsTo
     */
    public function config(): BelongsTo
    {
        return $this->belongsTo(OpportunityNotificationConfig::class, self::FIELD_CONFIG_ID, OpportunityNotificationConfig::FIELD_ID);
    }
}
