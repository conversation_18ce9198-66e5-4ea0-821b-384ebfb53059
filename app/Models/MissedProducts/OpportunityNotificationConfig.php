<?php

namespace App\Models\MissedProducts;

use App\Casts\AsDaysOfWeekSelection;
use App\Enums\OpportunityNotifications\OpportunityNotificationConfigType;
use App\Models\BaseModel;
use App\Models\Ruleset;
use App\Models\User;
use App\Models\UserPreset;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

/**
 * @property int $id
 * @property OpportunityNotificationConfigType $type
 * @property string $name
 * @property string $uuid
 * @property int $rule_id
 * @property int[] $attempt_on_days
 * @property int $maximum_send_frequency
 * @property int $maximum_promo_products
 * @property int $filter_preset_id
 * @property string $send_time
 * @property int $lead_threshold
 * @property int $campaign_threshold
 * @property int $maximum_days_since_last_lead
 * @property int $days_to_query
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $created_by
 * @property Carbon $updated_by
 * @property Carbon $expires_at
 * @property boolean $active
 * @property-read Ruleset $ruleset
 * @property-read User $createdBy
 * @property-read User $updatedBy
 */
class OpportunityNotificationConfig extends BaseModel
{
    use HasFactory, SoftDeletes;

    const string TABLE = 'opportunity_notification_configs';

    const string FIELD_ID                     = 'id';
    const string FIELD_UUID                   = 'uuid';
    const string FIELD_TYPE                   = 'type';
    const string FIELD_NAME                   = 'name';
    const string FIELD_RULE_ID                = 'rule_id';
    const string FIELD_FILTER_PRESET_ID       = 'filter_preset_id';
    const string FIELD_SEND_TIME              = 'send_time';
    const string FIELD_MAXIMUM_SEND_FREQUENCY = 'maximum_send_frequency';
    const string FIELD_MAXIMUM_PROMO_PRODUCTS = 'maximum_promo_products';
    const string FIELD_ATTEMPT_ON_DAYS        = 'attempt_on_days';
    const string FIELD_LEAD_THRESHOLD         = 'lead_threshold';
    const string FIELD_CAMPAIGN_THRESHOLD     = 'campaign_threshold';
    const string FIELD_ACTIVE                 = 'active';
    const string FIELD_MAXIMUM_DAYS_LAST_LEAD = 'maximum_days_since_last_lead';
    const string FIELD_DAYS_TO_QUERY          = 'days_to_query';
    const string FIELD_EXPIRES_AT             = 'expires_at';
    const string FIELD_CREATED_BY             = 'created_by';
    const string FIELD_UPDATED_BY             = 'updated_by';

    const string RELATION_RULESET = 'ruleset';

    const string RELATION_FILTER_PRESET  = 'filterPreset';
    const string RELATION_NOTIFICATIONS  = 'notifications';

    protected $guarded = [
        self::FIELD_ID,
        self::FIELD_UUID
    ];

    protected $table = self::TABLE;

    protected $casts = [
        self::FIELD_ATTEMPT_ON_DAYS => AsDaysOfWeekSelection::class,
        self::FIELD_TYPE            => OpportunityNotificationConfigType::class,
        self::FIELD_EXPIRES_AT      => 'date',
    ];

    public static function boot(): void
    {
        parent::boot();
        self::creating(function ($model) {
            $model->uuid = (string) Str::uuid();
        });
    }

    /**
     * @return BelongsTo
     */
    public function ruleset(): BelongsTo
    {
        return $this->belongsTo(Ruleset::class, self::FIELD_RULE_ID, Ruleset::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function filterPreset(): HasOne
    {
        return $this->hasOne(UserPreset::class, UserPreset::FIELD_ID,  self::FIELD_FILTER_PRESET_ID);
    }


    /**
     * @return HasMany
     */
    public function notifications(): HasMany
    {
        return $this->hasMany(OpportunityNotification::class, OpportunityNotification::FIELD_CONFIG_ID, self::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_CREATED_BY, User::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_UPDATED_BY, User::FIELD_ID);
    }

    /**
     * @return Carbon
     */
    public static function getMaximumExpiresAt(): Carbon
    {
        return now()->addMonths(2);
    }

    /**
     * @return Carbon
     */
    public function getStartDate(): Carbon
    {
        return now()->subDays($this->days_to_query);
    }

    /**
     * @return OpportunityNotificationConfig|null
     */
    public static function getBDMQueueConfig(): ?OpportunityNotificationConfig
    {
        return OpportunityNotificationConfig::query()
            ->where(OpportunityNotificationConfig::FIELD_TYPE, OpportunityNotificationConfigType::BDM_COMPANIES)
            ->first()
            ?? null;
    }
}
