<?php

namespace App\Models\MissedProducts;

use App\Models\BaseModel;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @property int $id
 * @property float $price
 * @property bool $is_reserved
 * @property int $sellable_legs_count
 * @property int $consumer_product_id
 * @property int $industry_service_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property-read IndustryService $industryService
 * @property-read ConsumerProduct $consumerProduct
 */
class MissedProduct extends BaseModel
{
    const TABLE = 'missed_products';

    const FIELD_ID                  = 'id';
    const FIELD_PRICE               = 'price';
    const FIELD_IS_RESERVED         = 'is_reserved';
    const FIELD_SELLABLE_LEGS       = 'sellable_legs_count';
    const FIELD_CONSUMER_PRODUCT_ID = 'consumer_product_id';
    const FIELD_INDUSTRY_SERVICE_ID = 'industry_service_id';
    const FIELD_CREATED_AT          = 'created_at';
    const FIELD_UPDATED_AT          = 'updated_at';

    const RELATION_CONSUMER_PRODUCT = 'consumerProduct';
    const RELATION_INDUSTRY_SERVICE = 'industryService';

    protected $guarded = [self::FIELD_ID];

    protected $table = self::TABLE;

    /**
     * Defines relationship to the Consumer Product.
     *
     * @return HasOne
     */
    public function consumerProduct(): HasOne
    {
        return $this->hasOne(ConsumerProduct::class, ConsumerProduct::FIELD_ID, self::FIELD_CONSUMER_PRODUCT_ID);
    }

    /**
     * Defines relationship to the Industry Service.
     *
     * @return hasOne
     */
    public function industryService(): hasOne
    {
        return $this->hasOne(IndustryService::class, IndustryService::FIELD_ID, self::FIELD_INDUSTRY_SERVICE_ID);
    }
}
