<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $audit_event
 * @property string $actor
 * @property string $actor_type
 * @property int $actor_id
 * @property string $model_type
 * @property int $model_id
 * @property string $description
 * @property string|null $audit_user_agent
 * @property string|null $audit_url
 * @property string|null $audit_ip
 * @property string|null $tags
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $deleted_at
 */
class AuditLog extends BaseModel
{
    use SoftDeletes;

    const TABLE                                 = "audit_logs";

    const FIELD_ID                              = 'id';
    const FIELD_AUDIT_EVENT                     = 'audit_event';
    const FIELD_ACTOR                           = 'actor';
    const FIELD_ACTOR_ID                        = 'actor_id';
    const FIELD_ACTOR_TYPE                      = 'actor_type';
    const FIELD_MODEL_ID                        = 'model_id';
    const FIELD_MODEL_TYPE                      = 'model_type';
    const FIELD_DESCRIPTION                     = 'description';
    const FIELD_AUDIT_USER_AGENT                = 'audit_user_agent';
    const FIELD_AUDIT_URL                       = 'audit_url';
    const FIELD_AUDIT_IP                        = 'audit_ip';
    const FIELD_AUDIT_TAGS                      = 'tags';

    const FIELD_CREATED_AT                      = 'created_at';
    const FIELD_UPDATED_AT                      = 'updated_at';
    const FIELD_DELETED_AT                      = 'deleted_at';

    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    public function actor(): MorphTo
    {
        return $this->morphTo();
    }

    public function model(): MorphTo
    {
        return $this->morphTo();
    }


}
