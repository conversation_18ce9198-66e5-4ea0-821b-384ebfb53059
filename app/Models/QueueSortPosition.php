<?php

namespace App\Models;

use App\Enums\QueueSortable\QueueName;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * @property string $class_name
 * @property QueueName $queue_name
 * @property int model_id
 * @property int $ordinal_value
 * @property ?Carbon released_at
 * @property ?int released_by_user_id
 */
class QueueSortPosition extends Model
{
    const string TABLE = 'queue_sort_positions';

    protected $table      = self::TABLE;

    public    $timestamps = false;

    const string FIELD_CLASS_NAME          = 'class_name';
    const string FIELD_QUEUE_NAME          = 'queue_name';
    const string FIELD_MODEL_ID            = 'model_id';
    const string FIELD_ORDINAL_VALUE       = 'ordinal_value';
    const string FIELD_RELEASED_AT         = 'released_at';
    const string FIELD_RELEASED_BY_USER_ID = 'released_by_user_id';

    protected $guarded = ['id'];

    protected $casts = [
        'released_at' => 'datetime',
    ];
}
