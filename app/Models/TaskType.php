<?php

namespace App\Models;

use App\Models\Sales\Task;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property string $name
 * @property string[] $modules
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Collection<Task> $tasks
 */
class TaskType extends Model
{
    use HasFactory;

    const TABLE = 'task_types';

    const FIELD_ID      = 'id';
    const FIELD_NAME    = 'name';
    const FIELD_MODULES = 'modules';

    const RELATION_TASKS = 'tasks';

    protected $table   = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts   = [self::FIELD_MODULES => 'array'];

    /**
     * Defines relationship to the tasks that use this task type.
     *
     * @return HasMany
     */
    public function tasks(): HasMany
    {
        return $this->hasMany(Task::class, Task::FIELD_TASK_TYPE_ID, self::FIELD_ID);
    }
}
