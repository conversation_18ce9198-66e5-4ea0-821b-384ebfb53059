<?php

namespace App\Models;

use App\Models\Legacy\EloquentQuote;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

/**
 * @property int                $id
 * @property int                $processor_id
 * @property int                $lead_id
 * @property int                $consumer_product_id
 * @property Carbon             $created_at
 * @property Carbon             $updated_at
 *
 * @property-read ConsumerProduct $consumerProduct
 * @property-read LeadProcessor $processor
 * @property-read ProductAssignment[] $productAssignments
 * @property-read ?User $user
 */
class LeadProcessingReservedLead extends BaseModel
{
    const string TABLE = 'lead_processing_reserved_leads';

    const string FIELD_ID                  = 'id';
    const string FIELD_PROCESSOR_ID        = 'processor_id';
    const string FIELD_CONSUMER_PRODUCT_ID = 'consumer_product_id';
    /** @deprecated  */
    const string FIELD_LEAD_ID      = 'lead_id';


    const int SYSTEM_ID = -1;

    const string RELATION_LEAD = 'lead';
    const string RELATION_PROCESSOR = 'processor';
    const string RELATION_PRODUCT_ASSIGNMENT = 'productAssignment';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * Defines relationship to the lead.
     * @deprecated
     * @return HasOne
     */
    public function lead(): HasOne
    {
        return $this->hasOne(EloquentQuote::class, EloquentQuote::ID, self::FIELD_LEAD_ID);
    }

    /**
     * @return HasOne
     */
    public function consumerProduct(): HasOne
    {
        return $this->hasOne(ConsumerProduct::class, self::FIELD_CONSUMER_PRODUCT_ID, ConsumerProduct::FIELD_ID);
    }

    /**
     * Defines relationship to the lead processor.
     *
     * @return HasOne
     */
    public function processor(): HasOne
    {
        return $this->hasOne(LeadProcessor::class, LeadProcessor::FIELD_ID, self::FIELD_PROCESSOR_ID);
    }

    /**
     * @return HasOneThrough
     */
    public function user(): HasOneThrough
    {
        return $this->hasOneThrough(
            User::class,
            LeadProcessor::class,
            LeadProcessor::FIELD_ID,
            User::FIELD_ID,
            self::FIELD_PROCESSOR_ID,
            LeadProcessor::FIELD_USER_ID
        );
    }

    /**
     * @return HasMany
     */
    public function productAssignment(): HasMany
    {
        return $this->hasMany(ProductAssignment::class, ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, self::FIELD_CONSUMER_PRODUCT_ID);
    }
}
