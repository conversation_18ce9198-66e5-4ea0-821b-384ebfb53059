<?php

namespace App\Models;

use App\Models\Odin\Consumer;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property boolean $email_verified
 * @property Carbon|null $email_verified_at
 * @property string|null $password
 *
 * @property-read  Consumer $consumer
 */
class ConsumerAuth extends Model
{
    const string TABLE = 'consumer_auth';

    const string FIELD_ID = 'id';
    const string FIELD_EMAIL_VERIFIED = 'email_verified';
    const string FIELD_EMAIL_VERIFIED_AT = 'email_verified_at';
    const string FIELD_PASSWORD = 'password';

    const string RELATION_CONSUMER = 'consumer';

    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];
    protected $casts = [
        self::FIELD_EMAIL_VERIFIED => 'boolean',
        self::FIELD_EMAIL_VERIFIED_AT => 'datetime'
    ];

    /**
     * @return BelongsTo
     */
    public function consumer(): BelongsTo
    {
        return $this->belongsTo(Consumer::class);
    }
}
