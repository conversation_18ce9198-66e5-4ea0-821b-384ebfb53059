<?php

namespace App\Models\Firestore\Flows;

/**
 * Flows are stored on Firestore
 * Model exists for standardizing key names only
 */
class Revision
{
    const ENVIRONMENT     = 'environment';
    const REVISION_ID     = 'revision_id';
    const NAME            = 'name';
    const DESCRIPTION     = 'description';
    const TYPE            = 'type';
    const VERSION         = 'version';
    const REVISION_DATA   = 'revision_data';
    const DATA_CURRENT    = 'current';
    const DATA_WORKING    = 'working';
    const PARENT_REVISION = 'parent_revision';
    const ACTIONED_BY     = 'actioned_by';
    const CREATED_AT      = 'created_at';
    const UPDATED_AT      = 'updated_at';
    const DELETED_AT      = 'deleted_at';
    const DELETED_TTL     = 'deleted_ttl';
    const FETCHED_WHILE_TRASHED = 'fetched_while_trashed';

    // v2 keys
    const IS_PUBLISHED    = 'is_published';
    const FLOW_DATA       = 'flow';
    const SLIDE_DATA      = 'slides';
    const SCHEMA          = 'schema';
    const CONVERT_V1      = 'convert_v1';
    const SLIDE_HIERARCHY = 'hierarchy';
}
