<?php

namespace App\Models;

use App\Enums\ConsumerProcessing\ConsumerProcessingActivityType;
use App\Enums\ConsumerProcessing\ConsumerProcessingActivityVisibilityScope;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property int $id
 * @property int $consumer_id
 * @property int $consumer_product_id
 * @property string $summary
 * @property string|null $comment
 * @property string $title
 * @property int $user_id
 * @property ConsumerProcessingActivityType $activity_type
 * @property int|null $activity_id
 * @property ConsumerProcessingActivityVisibilityScope $scope
 *
 * @property-read Consumer $consumer
 * @property-read ConsumerProduct $consumerProduct
 * @property-read User $user
 * @property-read Call|Text|null $activity
 */
class ConsumerProcessingActivity extends BaseModel
{
    const string TABLE = 'consumer_processing_activities';

    const string FIELD_ID                  = 'id';
    const string FIELD_CONSUMER_ID         = 'consumer_id';
    const string FIELD_CONSUMER_PRODUCT_ID = 'consumer_product_id';
    const string FIELD_SUMMARY             = 'summary';
    const string FIELD_COMMENT             = 'comment';
    const string FIELD_USER_ID             = 'user_id';
    const string FIELD_ACTIVITY_TYPE       = 'activity_type';
    const string FIELD_ACTIVITY_ID         = 'activity_id';
    const string FIELD_SCOPE               = 'scope';

    const string RELATION_CONSUMER         = 'consumer';
    const string RELATION_CONSUMER_PRODUCT = 'consumerProduct';
    const string RELATION_USER             = 'user';
    const string RELATION_ACTIVITY         = 'activity';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_ACTIVITY_TYPE => ConsumerProcessingActivityType::class,
        self::FIELD_SCOPE         => ConsumerProcessingActivityVisibilityScope::class,
    ];

    /**
     * @return BelongsTo
     */
    public function consumer(): BelongsTo
    {
        return $this->belongsTo(Consumer::class, self::FIELD_CONSUMER_ID, Consumer::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function consumerProduct(): BelongsTo
    {
        return $this->belongsTo(ConsumerProduct::class, self::FIELD_CONSUMER_PRODUCT_ID, Consumer::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID)
            ->withTrashed();
    }

    /**
     * @return MorphTo
     */
    public function activity(): MorphTo
    {
        return $this->morphTo();
    }
}