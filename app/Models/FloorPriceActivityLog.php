<?php

namespace App\Models;

use Spatie\Activitylog\Models\Activity;

class FloorPriceActivityLog extends Activity
{
    const string TABLE = 'floor_price_activity_logs';

    protected $table = self::TABLE;

    const string FIELD_ID           = 'id';
    const string FIELD_LOG_NAME     = 'log_name';
    const string FIELD_DESCRIPTION  = 'description';
    const string FIELD_SUBJECT_TYPE = 'subject_type';
    const string FIELD_SUBJECT_ID   = 'subject_id';
    const string FIELD_EVENT        = 'event';
    const string FIELD_CAUSER_TYPE  = 'causer_type';
    const string FIELD_CAUSER_ID    = 'causer_id';
    const string FIELD_PROPERTIES   = 'properties';
    const string FIELD_BATCH_UUID   = 'batch_uuid';
    const string FIELD_CREATED_AT   = 'created_at';
    const string FIELD_UPDATED_AT   = 'updated_at';

    const string RELATION_SUBJECT = 'subject';
    const string RELATION_CAUSER  = 'causer';
}
