<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class TieredAdvertisingCampaign extends Model
{
    use SoftDeletes;

    const string TABLE = 'tiered_advertising_campaigns';

    const string FIELD_ID = 'id';
    const string FIELD_INDUSTRY_ID = 'industry_id';
    const string FIELD_PLATFORM = 'platform';
    const string FIELD_NAME = 'name';
    const string FIELD_TIER = 'tier';
    const string FIELD_TIERED_ADVERTISING_ACCOUNT_ID = 'tiered_advertising_account_id';
    const string FIELD_PLATFORM_CAMPAIGN_ID = 'platform_campaign_id';
    const string FIELD_TCPA_BID = 'tcpa_bid';
    const string FIELD_UPPER_BOUND = 'upper_bound';
    const string FIELD_LOWER_BOUND = 'lower_bound';
    const string FIELD_COVERED_POPULATION = 'covered_population';
    const string FIELD_DATA = 'data';
    const string FIELD_INSTANCE_ID = 'instance_id';
    const string FIELD_DELETED_AT = 'deleted_at';

    const string DATA_LAST_LOCATION_UPDATE = 'last_location_update';

    const string TEMP_NAME = 'Name TBD When Enabled';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $casts = [
        self::FIELD_DATA => 'array',
    ];
}
