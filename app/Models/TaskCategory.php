<?php

namespace App\Models;

use App\Models\Sales\Task;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property string $name
 * @property Collection<Task> $tasks
 */
class TaskCategory extends Model
{
    use HasFactory;

    const TABLE = 'task_categories';

    const ID = 'id';
    const NAME = 'name';

    const DEFAULT_CATEGORY = 1;

    protected $table = self::TABLE;

    protected $guarded = [self::ID];

    /**
     * @return HasMany
     */
    public function tasks(): HasMany
    {
        return $this->hasMany(Task::class);
    }
}
