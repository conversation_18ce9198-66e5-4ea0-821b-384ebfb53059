<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * @property int $id
 * @property int $hunter_id
 * @property string $industry
 */
class HunterIndustry extends BaseModel
{
    use HasFactory;

    const TABLE = 'hunter_industries';

    const ID = 'id';
    const HUNTER_ID = 'hunter_id';
    const INDUSTRY = 'industry';

    protected $table = self::TABLE;
    protected $guarded = [self::ID];
}
