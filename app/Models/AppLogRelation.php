<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property int $id
 * @property int $app_log_id
 * @property string $relation_type
 * @property int $relation_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read BaseModel $relation
 */
class AppLogRelation extends BaseModel
{
    use HasFactory;

    const string TABLE = 'app_log_relations';

    const string FIELD_ID            = 'id';
    const string FIELD_APP_LOG_ID    = 'app_log_id';
    const string FIELD_RELATION_TYPE = 'relation_type';
    const string FIELD_RELATION_ID   = 'relation_id';
    const string FIELD_CREATED_AT    = 'created_at';
    const string FIELD_UPDATED_AT    = 'updated_at';

    const string RELATION_RELATION = 'relation';

    protected $guarded = [
        self::FIELD_ID
    ];


    /**
     * @return BelongsTo
     */
    public function log(): BelongsTo
    {
        return $this->belongsTo(AppLog::class, self::FIELD_APP_LOG_ID, AppLog::FIELD_ID);
    }

    /**
     * @return MorphTo
     */
    public function relation(): MorphTo
    {
        return $this->morphTo();
    }
}
