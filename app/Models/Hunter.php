<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property int $user_id
 * @property boolean $include_in_round_robin
 *
 * @property-read User $user
 * @property-read Collection<HunterState> $states
 * @property-read Collection<HunterIndustry> $industries
 */
class Hunter extends BaseModel
{
    use SoftDeletes, HasFactory;

    const TABLE = 'hunters';

    const FIELD_ID                      = 'id';
    const FIELD_USER_ID                 = 'user_id';
    const FIELD_INCLUDED_IN_ROUND_ROBIN = 'include_in_round_robin';
    const FIELD_DELETED_AT              = 'deleted_at';

    const RELATION_STATES = 'states';
    const RELATION_INDUSTRIES = 'industries';

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * @return HasMany
     */
    public function states(): HasMany
    {
        return $this->hasMany(HunterState::class);
    }

    /**
     * @return HasMany
     */
    public function industries(): HasMany
    {
        return $this->hasMany(HunterIndustry::class);
    }
}
