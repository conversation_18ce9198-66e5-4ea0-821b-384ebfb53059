<?php

namespace App\Models;

use App\Enums\PhoneType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property string $phone
 * @property string $status
 * @property string $friendly_name
 * @property string $region
 * @property PhoneType $type
 * @property string $external_reference
 *
 * @property-read Collection<int, User> $users
 */
class Phone extends BaseModel
{
    use hasFactory;

    const TABLE = "phones";

    const FIELD_ID                 = 'id';
    const FIELD_PHONE              = 'phone';
    const FIELD_STATUS             = 'status';
    const FIELD_FRIENDLY_NAME      = 'friendly_name';
    const FIELD_REGION             = 'region';
    const FIELD_TYPE               = 'type';
    const FIELD_EXTERNAL_REFERENCE = 'external_reference';
    const FIELD_EXTERNAL_TYPE      = 'external_type';

    const STATUS_ACTIVE   = 'active';
    const STATUS_INACTIVE = 'inactive';

    const EXTERNAL_TYPE_TWILIO = 'twilio';

    const RELATION_USERS = 'users';
    const RELATION_TEXTS = 'texts';
    const RELATION_CALLS = 'calls';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [self::FIELD_TYPE => PhoneType::class];

    /**
     * @return HasMany
     */
    public function calls(): HasMany
    {
        return $this->hasMany(Call::class, Call::FIELD_PHONE_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function texts(): HasMany
    {
        return $this->hasMany(Text::class, Text::FIELD_PHONE_ID, self::FIELD_ID);
    }

    /**
     * Retrieve BelongsToMany query excluding soft deleted records
     * @param bool $withTrash
     * @return BelongsToMany
     */
    public function users(bool $withTrash = false): BelongsToMany
    {
        $query = $this->belongsToMany(User::class, UserPhone::TABLE, UserPhone::FIELD_PHONE_ID, UserPhone::FIELD_USER_ID);

        if (!$withTrash) $query->whereNull(UserPhone::TABLE . '.' . UserPhone::FIELD_DELETED_AT);

        return $query;
    }

    /**
     * @return User|null
     */
    public function primaryUser(): ?User
    {
        return $this->users?->first();
    }
}
