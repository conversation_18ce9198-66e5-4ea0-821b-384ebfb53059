<?php

namespace App\Models\Odin;

use App\Enums\Odin\ConsumerFieldType;
use App\Enums\Odin\SystemModule;
use App\Models\BaseModel;
use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\Enums\ConsumerFieldSource;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @property int $id
 * @property int $consumer_field_id
 * @property string $consumer_field_category
 * @property string $consumer_field_category_id
 * @property string $module_type
 * @property boolean $is_visible
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read IndustryService $service
 */
class ConsumerFieldModuleVisibility extends BaseModel
{
    const TABLE = 'consumer_field_module_visibility';

    const FIELD_ID                  = 'id';
    const FIELD_CONSUMER_FIELD_CATEGORY = 'consumer_field_category';
    const FIELD_CONSUMER_FIELD_CATEGORY_ID = 'consumer_field_category_id';
    const FIELD_CONSUMER_FIELD_ID   = 'consumer_field_id';
    const FIELD_CONSUMER_FIELD_TYPE = 'consumer_field_type';
    const FIELD_MODULE_TYPE         = 'module_type';
    const FIELD_FEATURE_TYPE        = 'feature_type';
    const FIELD_IS_VISIBLE          = 'is_visible';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_MODULE_TYPE => SystemModule::class,
        self::FIELD_CONSUMER_FIELD_CATEGORY => ConsumerFieldType::class
    ];

    public function field(): ?HasOne
    {
        if ($this->{self::FIELD_CONSUMER_FIELD_TYPE} === ConsumerFieldSource::COMMON->value) {
            return $this->HasOne(ConsumerCommonField::class, ConsumerCommonField::FIELD_ID, self::FIELD_CONSUMER_FIELD_ID);
        } else if ($this->{self::FIELD_CONSUMER_FIELD_TYPE} === ConsumerFieldSource::CONFIGURABLE->value && $this->{self::FIELD_CONSUMER_FIELD_CATEGORY} === ConsumerFieldType::INDUSTRY) {
            return $this->HasOne(IndustryConsumerField::class, IndustryConsumerField::FIELD_ID, self::FIELD_CONSUMER_FIELD_ID);
        }  else if ($this->{self::FIELD_CONSUMER_FIELD_TYPE} === ConsumerFieldSource::CONFIGURABLE->value && $this->{self::FIELD_CONSUMER_FIELD_CATEGORY} === ConsumerFieldType::SERVICE) {
            return $this->HasOne(IndustryConsumerField::class, IndustryConsumerField::FIELD_ID, self::FIELD_CONSUMER_FIELD_ID);
        }
    }
}

