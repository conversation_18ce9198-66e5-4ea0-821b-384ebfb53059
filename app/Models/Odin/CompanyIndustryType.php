<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $industry_type_id
 * @property int $company_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read IndustryType $industryType
 * @property-read Company $company
 */
class CompanyIndustryType extends BaseModel
{
    use HasFactory;
    const TABLE = 'company_industry_types';

    const FIELD_ID               = 'id';
    const FIELD_INDUSTRY_TYPE_ID = 'industry_type_id';
    const FIELD_COMPANY_ID       = 'company_id';

    const RELATION_INDUSTRY_TYPE = 'industryType';
    const RELATION_COMPANY       = 'company';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function industryType(): BelongsTo
    {
        return $this->belongsTo(IndustryType::class, self::FIELD_INDUSTRY_TYPE_ID, IndustryType::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }
}
