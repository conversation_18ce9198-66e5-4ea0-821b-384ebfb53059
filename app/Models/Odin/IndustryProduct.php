<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $industry_id
 * @property int $product_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Industry $industry
 * @property-read Product $product
 */
class IndustryProduct extends BaseModel
{
    const TABLE = 'industry_products';

    const FIELD_ID          = 'id';
    const FIELD_INDUSTRY_ID = 'industry_id';
    const FIELD_PRODUCT_ID  = 'product_id';

    const RELATION_INDUSTRY = 'industry';
    const RELATION_PRODUCT  = 'product';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function industry(): BelongsTo
    {
        return $this->belongsTo(Industry::class, self::FIELD_INDUSTRY_ID, Industry::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, self::FIELD_PRODUCT_ID, Product::FIELD_ID);
    }
}
