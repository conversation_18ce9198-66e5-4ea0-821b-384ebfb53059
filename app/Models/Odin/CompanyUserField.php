<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property string $name
 * @property string $key
 * @property int $type
 * @property boolean $hidden
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property-read ConfigurableFieldType $fieldType
 *
 * @property-read CompanyUser $user
 */
class CompanyUserField extends BaseModel
{
    const TABLE = 'company_user_fields';

    const FIELD_ID              = 'id';
    const FIELD_NAME            = 'name';
    const FIELD_KEY             = 'key';
    const FIELD_TYPE            = 'type';
    const FIELD_HIDDEN          = 'hidden';

    const RELATION_USER = 'user';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    // todo: define type values


    /**
     * @return BelongsTo
     */
    public function fieldType(): BelongsTo
    {
        return $this->belongsTo(ConfigurableFieldType::class, self::FIELD_TYPE);
    }
}
