<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyExpertReview extends BaseModel
{
    use SoftDeletes, HasFactory;

    const TABLE = 'company_expert_reviews';

    const FIELD_ID                      = 'id';
    const FIELD_COMPANY_ID              = 'company_id';
    const FIELD_LEGACY_ID               = 'legacy_id';
    const FIELD_USER_ID                 = 'user_id';
    const FIELD_LEGACY_USER_ID          = 'legacy_user_id';
    const FIELD_BODY                    = 'body';
    const FIELD_DELETED_AT              = 'deleted_at';

    const RELATION_COMPANY  = 'company';
    const RELATION_USER     = 'user';

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }
}
