<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use Carbon\Carbon;
use Database\Factories\CompanyExternalReviewFactory;
use Database\Factories\EloquentAddressFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $company_location_id
 * @property string $reference
 * @property string $name
 * @property int $agg_count
 * @property float $agg_value
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read CompanyLocation $companyLocation
 */
class CompanyExternalReview extends BaseModel
{
    use HasFactory;

    /**
     * @return CompanyExternalReviewFactory
     */
    protected static function newFactory(): CompanyExternalReviewFactory
    {
        return CompanyExternalReviewFactory::new();
    }

    const TABLE = 'company_external_reviews';

    const FIELD_ID                  = 'id';
    const FIELD_COMPANY_LOCATION_ID = 'company_location_id';
    const FIELD_REFERENCE           = 'reference';
    const FIELD_NAME                = 'name';
    const FIELD_AGG_COUNT           = 'agg_count';
    const FIELD_AGG_VALUE           = 'agg_value';

    const RELATION_COMPANY_LOCATION = 'companyLocation';

    const NAME_PLACES = 'places';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function companyLocation(): BelongsTo
    {
        return $this->belongsTo(CompanyLocation::class, self::FIELD_COMPANY_LOCATION_ID, CompanyLocation::FIELD_ID);
    }
}
