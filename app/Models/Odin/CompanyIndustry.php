<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $industry_id
 * @property int $company_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Industry $industry
 * @property-read Company $company
 */
class CompanyIndustry extends BaseModel
{
    use HasFactory;

    const TABLE = 'company_industries';

    const FIELD_ID          = 'id';
    const FIELD_INDUSTRY_ID = 'industry_id';
    const FIELD_COMPANY_ID  = 'company_id';

    const RELATION_INDUSTRY = 'industry';
    const RELATION_COMPANY  = 'company';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function industry(): BelongsTo
    {
        return $this->belongsTo(Industry::class, self::FIELD_INDUSTRY_ID, Industry::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }
}
