<?php

namespace App\Models\Odin;

use App\Enums\Odin\LocationSiloPageLocationType;
use App\Models\BaseModel;
use App\Models\Legacy\Location;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @property int $id
 * @property int $silo_id
 * @property LocationSiloPageLocationType $location_type
 * @property string $entry_slug
 * @property string $relative_path
 * @property int $parent_location_id
 * @property int $location_id
 * @property bool $is_active
 *
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Location $location
 * @property-read Location $parentLocation
 * @property-read Silo $silo
 */
class LocationSiloPage extends BaseModel
{
    const TABLE = 'location_silo_pages';

    const FIELD_ID                  = 'id';
    const FIELD_SILO_ID             = 'silo_id';
    const FIELD_RELATIVE_PATH       = 'relative_path';
    const FIELD_ENTRY_SLUG          = 'entry_slug';
    const FIELD_LOCATION_TYPE       = 'location_type';
    const FIELD_PARENT_LOCATION_ID  = 'parent_location_id';
    const FIELD_LOCATION_ID         = 'location_id';
    const FIELD_IS_ACTIVE           = 'is_active';

    const RELATION_LOCATION        = 'location';
    const RELATION_PARENT_LOCATION = 'parentLocation';
    const RELATION_SILO            = 'silo';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_LOCATION_TYPE   => LocationSiloPageLocationType::class,
    ];

    /**
     * @return HasOne
     */
    public function location(): HasOne
    {
        return $this->hasOne(Location::class, Location::ID, self::FIELD_LOCATION_ID);
    }

    /**
     * @return HasOne
     */
    public function parentLocation(): HasOne
    {
        return $this->hasOne(Location::class,  Location::ID, self::FIELD_PARENT_LOCATION_ID);
    }

    /**
     * @return BelongsTo
     */
    public function silo(): BelongsTo
    {
        return $this->belongsTo(Silo::class, self::FIELD_SILO_ID, Silo::FIELD_ID);
    }
}
