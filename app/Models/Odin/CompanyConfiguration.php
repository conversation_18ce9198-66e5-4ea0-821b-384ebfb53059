<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $company_id
 * @property boolean $allow_leads_no_cc
 * @property boolean $enable_tcpa_playback
 * @property boolean $never_exceed_budget
 * @property boolean $disallow_ranking
 * @property boolean $receive_off_hour_leads
 * @property boolean $appointments_active
 * @property boolean $mi_appointments_active
 * @property boolean $require_appointments_calendar
 * @property boolean $unrestricted_zip_code_targeting
 * @property boolean $campaign_alert_enabled
 * @property boolean $accept_under_review_leads
 * @property boolean $pause_campaign_on_threshold_exceeded
 * @property boolean $consumer_proxy_phone_enabled
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Company $company
 */
class CompanyConfiguration extends BaseModel
{
    use HasFactory;

    const string TABLE = 'company_configurations';

    const string FIELD_ID                                   = 'id';
    const string FIELD_COMPANY_ID                           = 'company_id';
    const string FIELD_ALLOW_LEADS_NO_CC                    = 'allow_leads_no_cc';
    const string FIELD_ENABLE_TCPA_PLAYBACK                 = 'enable_tcpa_playback';
    const string FIELD_NEVER_EXCEED_BUDGET                  = 'never_exceed_budget';
    const string FIELD_DISALLOW_RANKING                     = 'disallow_ranking';
    const string FIELD_RECEIVE_OFF_HOUR_LEADS               = 'receive_off_hour_leads';
    const string FIELD_APPOINTMENTS_ACTIVE                  = 'appointments_active';
    const string FIELD_MI_APPOINTMENTS_ACTIVE               = 'mi_appointments_active';
    const string FIELD_REQUIRE_APPOINTMENTS_CALENDAR        = 'require_appointments_calendar';
    const string FIELD_MISSED_PRODUCTS_ACTIVE               = 'missed_products_active';
    const string FIELD_UNRESTRICTED_ZIP_CODE_TARGETING      = 'unrestricted_zip_code_targeting';
    const string FIELD_CAMPAIGN_ALERT_ENABLED               = 'campaign_alert_enabled';
    const string FIELD_ACCEPT_UNDER_REVIEW_LEADS            = 'accept_under_review_leads';
    const string FIELD_PAUSE_CAMPAIGN_ON_THRESHOLD_EXCEEDED = 'pause_campaign_on_threshold_exceeded';
    const string FIELD_CONSUMER_PROXY_PHONE_ENABLED         = 'consumer_proxy_phone_enabled';

    const string FIELD_REVIEWS_ENABLED = 'reviews_enabled';

    const string RELATION_COMPANY = 'company';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }
}
