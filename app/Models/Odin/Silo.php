<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property string $name
 * @property string $root_path
 * @property string $collection_handle
 * @property int $website_id
 * @property int $industry_id
 * @property int $industry_service_id
 * @property bool $is_active
 * @property string $flow_id
 * @property string $revision_id
 *
 * @property-read Industry $industry
 * @property-read IndustryService $service
 * @property-read Website $website
 * @property-read Collection<LocationSiloPage> $locationSiloPages
 */
class Silo extends BaseModel
{
    const TABLE = 'silos';

    const FIELD_ID                  = 'id';
    const FIELD_NAME                = 'name';
    const FIELD_ROOT_PATH           = 'root_path';
    const FIELD_COLLECTION_HANDLE   = 'collection_handle';
    const FIELD_WEBSITE_ID          = 'website_id';
    const FIELD_INDUSTRY_ID         = 'industry_id';
    const FIELD_INDUSTRY_SERVICE_ID = 'industry_service_id';
    const FIELD_IS_ACTIVE           = 'is_active';
    const FIELD_FLOW_ID             = 'flow_id';
    const FIELD_REVISION_ID         = 'revision_id';

    const RELATION_INDUSTRY             = 'industry';
    const RELATION_SERVICE              = 'service';
    const RELATION_WEBSITE              = 'website';
    const RELATION_LOCATION_SILO_PAGES  = 'locationSiloPages';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return HasOne
     */
    public function industry(): HasOne
    {
        return $this->hasOne(Industry::class,  Industry::FIELD_ID, self::FIELD_INDUSTRY_ID);
    }

    /**
     * @return HasOne
     */
    public function service(): HasOne
    {
        return $this->hasOne(IndustryService::class, IndustryService::FIELD_ID, self::FIELD_INDUSTRY_SERVICE_ID);
    }

    /**
     * @return BelongsTo
     */
    public function website(): BelongsTo
    {
        return $this->belongsTo(Website::class, self::FIELD_WEBSITE_ID, Website::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function locationSiloPages(): HasMany
    {
        return $this->hasMany(LocationSiloPage::class, LocationSiloPage::FIELD_SILO_ID, self::FIELD_ID);
    }
}
