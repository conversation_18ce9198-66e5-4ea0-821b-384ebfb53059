<?php

namespace App\Models\Odin;

use App\Database\Casts\AsHistoricalCompanyDailyStatuses;
use App\Database\Casts\AsHistoricalCompanyMonthlyStatuses;
use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class HistoricalCompanyStatuses extends BaseModel
{
    use HasFactory;

    const TABLE = 'historical_company_statuses';

    const FIELD_ID = 'id';
    const FIELD_COMPANY_ID = 'company_id';
    const FIELD_YEAR = 'year';
    const FIELD_DAILY_STATUSES = 'daily_statuses';
    const FIELD_MONTHLY_STATUSES = 'monthly_statuses';

    const RELATION_COMPANY = 'company';
    const RELATION_PRODUCT_ASSIGNMENTS = 'productAssignments';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $casts = [
        self::FIELD_DAILY_STATUSES => AsHistoricalCompanyDailyStatuses::class,
        self::FIELD_MONTHLY_STATUSES => AsHistoricalCompanyMonthlyStatuses::class
    ];

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function productAssignments(): HasMany
    {
        return $this->hasMany(ProductAssignment::class, ProductAssignment::FIELD_COMPANY_ID, self::FIELD_COMPANY_ID);
    }
}
