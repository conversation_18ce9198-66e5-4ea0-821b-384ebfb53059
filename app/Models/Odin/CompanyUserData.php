<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $company_user_id
 * @property array $payload
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read CompanyUser $user
 */
class CompanyUserData extends BaseModel
{
    const TABLE = 'company_user_data';

    const FIELD_ID              = 'id';
    const FIELD_COMPANY_USER_ID = 'company_user_id';
    const FIELD_PAYLOAD         = 'payload';

    const RELATION_USER = 'user';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_PAYLOAD => 'array'
    ];

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(CompanyUser::class, self::FIELD_COMPANY_USER_ID, CompanyUser::FIELD_ID);
    }
}
