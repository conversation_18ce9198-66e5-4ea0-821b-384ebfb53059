<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use App\Models\Legacy\LeadCampaign;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property int $product_id
 * @property int|null $industry_service_id
 * @property int $company_id
 * @property string $name
 * @property boolean $status
 * @property int $reactivate_at_timestamp
 * @property int $parent_legacy_lead_campaign_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $deleted_at
 *
 * @property-read Collection|ProductCampaignBudget[] $budgets
 * @property-read LeadCampaign $legacyCampaign
 * @property-read Company $company
 * @property-read Product $product
 * @property-read LeadCampaign $campaign
 * @property-read IndustryService $service
 * @property-read ProductCampaignSchedule[] $schedules
 */
class ProductCampaign extends BaseModel
{
    use HasFactory, SoftDeletes;

    const TABLE = 'product_campaigns';

    const FIELD_ID = 'id';
    const FIELD_PRODUCT_ID          = 'product_id';
    const FIELD_INDUSTRY_SERVICE_ID = 'industry_service_id';
    const FIELD_COMPANY_ID          = 'company_id';
    const FIELD_NAME = 'name';
    const FIELD_STATUS = 'status';
    const FIELD_REACTIVATE_AT_TIMESTAMP = 'reactivate_at_timestamp';
    const FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID = 'parent_legacy_lead_campaign_id';
    const FIELD_CREATED_AT = 'created_at';
    const FIELD_UPDATED_AT = 'updated_at';
    const FIELD_DELETED_AT = 'deleted_at';

    const RELATION_BUDGETS = 'budgets';
    const RELATION_PRODUCT = 'product';
    const RELATION_COMPANY = 'company';
    const RELATION_LEGACY_CAMPAIGN = 'legacyCampaign';
    const RELATION_CAMPAIGN = 'campaign';
    const RELATION_SERVICE  = 'service';
    const RELATION_SCHEDULES = 'schedules';

    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    /**
     * @return HasMany
     */
    public function schedules(): HasMany
    {
        return $this->hasMany(ProductCampaignSchedule::class, ProductCampaignSchedule::FIELD_PRODUCT_CAMPAIGN_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function budgets(): HasMany
    {
        return $this->hasMany(ProductCampaignBudget::class, ProductCampaignBudget::FIELD_PRODUCT_CAMPAIGN_ID, self::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function product(): HasOne
    {
        return $this->hasOne(Product::class, Product::FIELD_ID, self::FIELD_PRODUCT_ID);
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function legacyCampaign(): BelongsTo
    {
        return $this->belongsTo(LeadCampaign::class, self::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID, LeadCampaign::ID);
    }

    /**
     * @return BelongsTo
     */
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(LeadCampaign::class,self::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID, LeadCampaign::ID);
    }

    /**
     * @return BelongsTo
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(IndustryService::class, self::FIELD_INDUSTRY_SERVICE_ID, IndustryService::FIELD_ID);
    }
}
