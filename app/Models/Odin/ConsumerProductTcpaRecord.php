<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property string $tcpa_id
 * @property string $tcpa_service_type
 * @property Collection<ConsumerProduct> $consumerProducts
 */
class ConsumerProductTcpaRecord extends BaseModel
{
    use HasFactory;

    const TABLE = 'consumer_product_tcpa_records';

    const FIELD_ID                = 'id';
    const FIELD_TCPA_ID           = 'tcpa_id';
    const FIELD_TCPA_SERVICE_TYPE = 'tcpa_service_type';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return HasMany
     */
    public function consumerProducts(): HasMany
    {
        return $this->hasMany(ConsumerProduct::class);
    }
}
