<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int $id
 * @property int $industry_service_id
 * @property int $product_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Product $product
 * @property-read IndustryService $service
 * @property-read ProductStateFloorPrice[] $serviceProductStateFloorPrices
 */
class ServiceProduct extends BaseModel
{
    use HasFactory;

    const TABLE = 'service_products';

    const FIELD_ID                  = 'id';
    const FIELD_INDUSTRY_SERVICE_ID = 'industry_service_id';
    const FIELD_PRODUCT_ID          = 'product_id';

    const RELATION_SERVICE = 'service';
    const RELATION_PRODUCT = 'product';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(IndustryService::class, self::FIELD_INDUSTRY_SERVICE_ID, IndustryService::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, self::FIELD_PRODUCT_ID, Product::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function serviceProductStateFloorPrices(): HasMany
    {
        return $this->hasMany(ProductStateFloorPrice::class, ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID, self::FIELD_ID);
    }
}
