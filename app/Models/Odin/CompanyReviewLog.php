<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $user_id
 * @property int $rel_id
 * @property int $rel_type
 * @property int $type
 * @property string $value
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read User $user
 */
class CompanyReviewLog extends BaseModel
{
    const TABLE = 'company_review_logs';

    const FIELD_ID       = 'id';
    const FIELD_USER_ID  = 'user_id';
    const FIELD_REL_ID   = 'rel_id';
    const FIELD_REL_TYPE = 'rel_type';
    const FIELD_TYPE     = 'type';
    const FIELD_VALUE    = 'value';

    const RELATION_USER = 'user';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    // todo: define type values
    // todo: define rel type values

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }
}
