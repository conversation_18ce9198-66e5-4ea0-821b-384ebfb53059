<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $industry_id
 * @property int $website_id
 * @property string $slug
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Industry $industry
 * @property-read Website $website
 */
class IndustryWebsite extends BaseModel
{
    const TABLE = 'industry_websites';

    const FIELD_ID          = 'id';
    const FIELD_INDUSTRY_ID = 'industry_id';
    const FIELD_WEBSITE_ID  = 'website_id';
    const FIELD_SLUG        = 'slug';

    const RELATION_INDUSTRY = 'industry';
    const RELATION_WEBSITE  = 'website';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function industry(): BelongsTo
    {
        return $this->belongsTo(Industry::class, self::FIELD_INDUSTRY_ID, Industry::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function website(): BelongsTo
    {
        return $this->belongsTo(Website::class, self::FIELD_WEBSITE_ID, Website::FIELD_ID);
    }
}
