<?php

namespace App\Models\Odin;

use App\Database\Casts\AsConsumerProductTrackingPayload;
use App\DataModels\Odin\ConsumerProductTrackingPayloadDataModel;
use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property int $legacy_id
 * @property string $url_start
 * @property string $url_convert
 * @property string $calculator_source
 * @property int $website_id
 * @property string $ad_track_type
 * @property string $ad_track_code
 * @property bool $conversion_uploaded
 * @property float $estimated_revenue
 * @property ConsumerProductTrackingPayloadDataModel $payload
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Website $website
 * @property-read Collection<ConsumerProduct> $consumerProducts
 */
class ConsumerProductTracking extends BaseModel
{
    use HasFactory;

    const TABLE = 'consumer_product_tracking';

    const ID                = 'id';
    const LEGACY_ID         = 'legacy_id';
    const CONSUMER_PRODUCT_ID = 'consumer_product_id';
    const URL_START         = 'url_start';
    const URL_CONVERT       = 'url_convert';
    const CALCULATOR_SOURCE = 'calculator_source';
    const WEBSITE_ID        = 'website_id';
    const AD_TRACK_TYPE     = 'ad_track_type';
    const AD_TRACK_CODE     = 'ad_track_code';
    const CONVERSION_UPLOADED = 'conversion_uploaded';
    const ESTIMATED_REVENUE = 'estimated_revenue';
    const PAYLOAD           = 'payload';

    const RELATION_WEBSITE = 'website';

    protected $table = self::TABLE;

    protected $guarded = [self::ID];

    protected $casts = [
        self::PAYLOAD => AsConsumerProductTrackingPayload::class
    ];

    /**
     * @return HasMany
     */
    public function consumerProducts(): HasMany
    {
        return $this->hasMany(ConsumerProduct::class);
    }

    /**
     * @return HasOne
     */
    public function website(): HasOne
    {
        return $this->hasOne(Website::class, Website::FIELD_ID, self::WEBSITE_ID);
    }
}
