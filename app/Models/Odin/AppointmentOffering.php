<?php

namespace App\Models\Odin;

use App\Models\BestRevenueScenarioLog;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AppointmentOffering extends Model
{
    use HasFactory;

    const TABLE = 'appointment_offerings';

    const FIELD_ID = 'id';
    const FIELD_CONSUMER_PRODUCT_ID = 'consumer_product_id';
    const FIELD_PRODUCT_CAMPAIGN_BUDGET_ID = 'product_campaign_budget_id';
    const FIELD_SALE_TYPE_ID = 'sale_type_id';
    const FIELD_BEST_REVENUE_SCENARIO_LOGS_RUN_ID = 'best_revenue_scenario_logs_run_id';
    const FIELD_STATUS = 'status';
    const FIELD_ATTEMPT = 'attempt';
    const FIELD_PRICE = 'price';
    const FIELD_START_TIME = 'start_time';
    const FIELD_EXPIRY_TIME = 'expiry_time';
    const FIELD_LINK_KEY = 'link_key';
    const FIELD_LINK_CODE = 'link_code';
    const FIELD_CREATED_AT = 'created_at';
    const FIELD_UPDATED_AT = 'updated_at';

    const RELATION_CONSUMER_PRODUCT = 'consumerProduct';
    const RELATION_PRODUCT_CAMPAIGN_BUDGET = 'productCampaignBudget';
    const RELATION_BEST_REVENUE_SCENARIO_LOGS = 'bestRevenueScenarioLogs';
    const RELATION_PRODUCT_APPOINTMENT = 'productAppointment';

    const STATUS_PENDING = 1;
    const STATUS_DENIED = 2;
    const STATUS_ACCEPTED = 3;
    const STATUS_EXPIRED = 4;
    const STATUS_DOUBLE_BOOKED = 5;
    const STATUS_NOT_RECEIVED = 6;

    const STATUSES = [
        self::STATUS_PENDING,
        self::STATUS_DENIED,
        self::STATUS_ACCEPTED,
        self::STATUS_EXPIRED,
        self::STATUS_DOUBLE_BOOKED,
        self::STATUS_NOT_RECEIVED
    ];

    protected $table = self::TABLE;
    protected $guarded = [
        self::FIELD_ID
    ];

    protected $casts = [
        self::FIELD_EXPIRY_TIME => 'datetime',
        self::FIELD_START_TIME => 'datetime'
    ];

    /**
     * @return BelongsTo
     */
    public function consumerProduct(): BelongsTo
    {
        return $this->belongsTo(ConsumerProduct::class, self::FIELD_CONSUMER_PRODUCT_ID, ConsumerProduct::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function productCampaignBudget(): BelongsTo
    {
        return $this->belongsTo(ProductCampaignBudget::class, self::FIELD_PRODUCT_CAMPAIGN_BUDGET_ID, ProductCampaignBudget::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function bestRevenueScenarioLogs(): HasMany
    {
        return $this->hasMany(BestRevenueScenarioLog::class, BestRevenueScenarioLog::FIELD_RUN_ID, self::FIELD_BEST_REVENUE_SCENARIO_LOGS_RUN_ID);
    }

    /**
     * @return BelongsTo
     */
    public function productAppointment(): BelongsTo
    {
        return $this->belongsTo(ProductAppointment::class, self::FIELD_CONSUMER_PRODUCT_ID, ProductAppointment::CONSUMER_PRODUCT_ID);
    }
}
