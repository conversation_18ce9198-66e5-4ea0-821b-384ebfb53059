<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @property int $id
 * @property int $company_id
 * @property int $rel_id
 * @property int $consumer_id
 * @property int $rel_type
 * @property string $first_name
 * @property string $last_name
 * @property string $email
 * @property string $phone
 * @property boolean $email_validated
 * @property boolean $phone_validated
 * @property string $title
 * @property string $body
 * @property float $overall_score
 * @property int $status
 * @property string $ip_address
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Company $company
 * @property-read Consumer $consumer
 * @property-read CompanyReviewResponse[] $responses
 * @property-read CompanyReviewData[] $data
 */
class CompanyReview extends BaseModel
{
    use HasFactory;

    const TABLE = 'company_reviews';

    const FIELD_ID              = 'id';
    const FIELD_COMPANY_ID      = 'company_id';
    const FIELD_REL_ID          = 'rel_id';
    const FIELD_CONSUMER_ID     = 'consumer_id';
    const FIELD_REL_TYPE        = 'rel_type';
    const FIELD_FIRST_NAME      = 'first_name';
    const FIELD_LAST_NAME       = 'last_name';
    const FIELD_EMAIL           = 'email';
    const FIELD_PHONE           = 'phone';
    const FIELD_EMAIL_VALIDATED = 'email_validated';
    const FIELD_PHONE_VALIDATED = 'phone_validated';
    const FIELD_TITLE           = 'title';
    const FIELD_BODY            = 'body';
    const FIELD_OVERALL_SCORE   = 'overall_score';
    const FIELD_STATUS          = 'status';
    const FIELD_IP_ADDRESS      = 'ip_address';
    const FIELD_LEGACY_ID       = 'legacy_id';

    const RELATION_COMPANY   = 'company';
    const RELATION_CONSUMER  = 'consumer';
    const RELATION_RESPONSES = 'responses';
    const RELATION_DATA      = 'data';

    const REL_TYPE_PRODUCT = '1';
    const REL_TYPE_COMPANY = '2';

    const RELATION_TYPES = [
        self::REL_TYPE_PRODUCT,
        self::REL_TYPE_COMPANY
    ];

    const RELATION_TYPE_NAMES = [
        self::REL_TYPE_PRODUCT => 'Product',
        self::REL_TYPE_COMPANY => 'Company'
    ];

    const STATUS_INITIAL = 0;
    const STATUS_APPROVED = 1;
    const STATUS_ARCHIVED = -1;
    const STATUS_DECLINED = -2;

    const STATUSES = [
        self::STATUS_INITIAL,
        self::STATUS_APPROVED,
        self::STATUS_ARCHIVED,
        self::STATUS_DECLINED
    ];

    const STATUS_NAMES = [
        self::STATUS_INITIAL => 'Initial',
        self::STATUS_APPROVED => 'Approved',
        self::STATUS_ARCHIVED => 'Archived',
        self::STATUS_DECLINED => 'Declined'
    ];

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function data(): HasOne
    {
        return $this->hasOne(CompanyReviewData::class, CompanyReviewData::FIELD_COMPANY_REVIEW_ID, self::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function consumer(): BelongsTo
    {
        return $this->belongsTo(Consumer::class, self::FIELD_CONSUMER_ID, Consumer::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function responses(): HasMany
    {
        return $this->hasMany(CompanyReviewResponse::class, CompanyReviewResponse::FIELD_REVIEW_ID, self::FIELD_ID);
    }
}
