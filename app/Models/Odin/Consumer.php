<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use App\Models\Call;
use App\Models\ConsumerAuth;
use App\Models\ConsumerProcessingActivity;
use App\Models\LeadProcessingCommunication;
use App\Models\Legacy\EloquentQuote;
use App\Models\MarketingCampaignConsumer;
use App\Models\Text;
use App\Models\WatchdogVideo;
use App\Services\HelperService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property string $reference
 * @property int $website_id
 * @property string $email
 * @property string $phone
 * @property string $formatted_phone
 * @property string $first_name
 * @property string $last_name
 * @property int $status
 * @property int $classification
 * @property int $legacy_id
 * @property string $status_reason
 * @property boolean $is_valid_phone
 * @property int $max_contact_requests
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $revalidation_requested_at
 * @property int|null $cloned_from_id
 *
 * @property-read Product[] $products
 * @property-read CompanyReview[] $reviews
 * @property-read Collection<ConsumerProduct> $consumerProducts
 * @property-read EloquentQuote $legacyLead
 * @property-read Website $website
 * @property-read self|null $clonedFrom
 * @property-read Collection<self> $clones
 * @property-read Collection<ProductAssignment> $productAssignments
 * @property-read WatchdogVideo $watchdogVideos
 * @property-read LeadProcessingCommunication[] $leadProcessingCommunications
 * @property-read ConsumerAuth|null $auth
 * @property-read Collection<ConsumerProcessingActivity> $processingActivities
 */
class Consumer extends BaseModel
{
    use HasFactory;

    const string TABLE = 'consumers';

    const string FIELD_ID                        = 'id';
    const string FIELD_REFERENCE                 = 'reference';
    const string FIELD_WEBSITE_ID                = 'website_id';
    const string FIELD_EMAIL                     = 'email';
    const string FIELD_PHONE                     = 'phone';
    const string FIELD_FORMATTED_PHONE           = 'formatted_phone';
    const string FIELD_FIRST_NAME                = 'first_name';
    const string FIELD_LAST_NAME                 = 'last_name';
    const string FIELD_STATUS                    = 'status';
    const string FIELD_CLASSIFICATION            = 'classification';
    const string FIELD_STATUS_REASON             = 'status_reason';
    const string FIELD_MAX_CONTACT_REQUESTS      = 'max_contact_requests';
    const string FIELD_LEGACY_ID                 = 'legacy_id';
    const string FIELD_CREATED_AT                = 'created_at';
    const string FIELD_CLONED_FROM_ID            = 'cloned_from_id';
    const string FIELD_REVALIDATION_REQUESTED_AT = 'revalidation_requested_at';

    const string ATTRIBUTE_IS_VALID_PHONE   = 'is_valid_phone';

    const int STATUS_CANCELLED   = -1;
    const int STATUS_INITIAL     = 0;
    const int STATUS_IN_PROGRESS = 1;
    const int STATUS_COMPLETED   = 2;

    const int CLASSIFICATION_EMAIL_ONLY                          = 0;
    const int CLASSIFICATION_VERIFIED_EMAIL_ONLY                 = 1;
    const int CLASSIFICATION_UNVERIFIED_PHONE                    = 2;
    const int CLASSIFICATION_VERIFIED_PHONE_VIA_SMS              = 3;
    const int CLASSIFICATION_VERIFIED_PHONE_VIA_CALL             = 4;
    const int CLASSIFICATION_VERIFIED_PHONE_VIA_LEAD_PROCESSING  = 5;
    const int CLASSIFICATION_VERIFIED_PHONE_VIA_REVALIDATION_SMS = 6;
    const int CLASSIFICATION_VERIFIED_VIA_REVALIDATION_EMAIL     = 7;

    const string RELATION_PRODUCTS                       = 'products';
    const string RELATION_REVIEWS                        = 'reviews';
    const string RELATION_CONSUMER_PRODUCT               = 'consumerProducts';
    const string RELATION_LEGACY_LEAD                    = 'legacyLead';
    const string RELATION_CLONED_FROM                    = 'clonedFrom';
    const string RELATION_PRODUCT_ASSIGNMENTS            = 'productAssignments';
    const string RELATION_LEAD_PROCESSING_COMMUNICATIONS = 'leadProcessingCommunications';
    const string RELATION_MARKETING_CONSUMER             = 'marketingConsumer';
    const string RELATION_CLONES                         = 'clones';
    const string RELATION_AUTH                           = 'auth';
    const string RELATION_PROCESSING_ACTIVITIES          = 'processingActivities';
    const string RELATION_WEBSITE                        = 'website';

    const array STATUSES = [
        self::STATUS_CANCELLED,
        self::STATUS_INITIAL,
        self::STATUS_IN_PROGRESS,
        self::STATUS_COMPLETED
    ];

    const array CLASSIFICATIONS = [
        self::CLASSIFICATION_EMAIL_ONLY,
        self::CLASSIFICATION_VERIFIED_EMAIL_ONLY,
        self::CLASSIFICATION_UNVERIFIED_PHONE,
        self::CLASSIFICATION_VERIFIED_PHONE_VIA_SMS,
        self::CLASSIFICATION_VERIFIED_PHONE_VIA_CALL,
        self::CLASSIFICATION_VERIFIED_PHONE_VIA_LEAD_PROCESSING
    ];

    const array VERIFIED_CLASSIFICATIONS = [
        self::CLASSIFICATION_VERIFIED_EMAIL_ONLY,
        self::CLASSIFICATION_VERIFIED_PHONE_VIA_SMS,
        self::CLASSIFICATION_VERIFIED_PHONE_VIA_CALL,
        self::CLASSIFICATION_VERIFIED_PHONE_VIA_LEAD_PROCESSING,
        self::CLASSIFICATION_VERIFIED_PHONE_VIA_REVALIDATION_SMS
    ];

    const array UNVERIFIED_CLASSIFICATIONS = [
        self::CLASSIFICATION_EMAIL_ONLY,
        self::CLASSIFICATION_UNVERIFIED_PHONE
    ];

    const array CLASSIFICATION_TEXT = [
        self::CLASSIFICATION_EMAIL_ONLY => 'Email Only',
        self::CLASSIFICATION_VERIFIED_EMAIL_ONLY => 'Verified Email Only',
        self::CLASSIFICATION_UNVERIFIED_PHONE => 'Phone Unverified',
        self::CLASSIFICATION_VERIFIED_PHONE_VIA_SMS => 'Phone Verified Via SMS',
        self::CLASSIFICATION_VERIFIED_PHONE_VIA_CALL => 'Phone Verified Via Call',
        self::CLASSIFICATION_VERIFIED_PHONE_VIA_LEAD_PROCESSING => 'Phone Verified Via Lead Processing',
        self::CLASSIFICATION_VERIFIED_PHONE_VIA_REVALIDATION_SMS => 'Phone Verified Via Revalidation SMS'
    ];

    //TODO: needs to be configurable per company
    const int  PROXY_PHONE_PERCENT = 5;

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected static function booted()
    {
        parent::booted();

        static::saving(function (Consumer $companyUser) {
            self::updateFormattedPhoneNumber($companyUser);
        });
    }

    /**
     * @param Consumer $consumer
     * @return void
     */
    private static function updateFormattedPhoneNumber(Consumer $consumer): void
    {
        $consumer->{self::FIELD_FORMATTED_PHONE} = $consumer->{self::FIELD_PHONE} ? HelperService::formatUSPhoneNumber($consumer->{self::FIELD_PHONE}) : null;
    }

    /**
     * @return HasMany
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(CompanyReview::class, CompanyReview::FIELD_CONSUMER_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function consumerProducts(): HasMany
    {
        return $this->HasMany(ConsumerProduct::class, ConsumerProduct::FIELD_CONSUMER_ID, self::FIELD_ID);
    }

    /**
     * @return String
     */
    public function getFullName(): String
    {
        return $this->{self::FIELD_FIRST_NAME} . ' ' . $this->{self::FIELD_LAST_NAME};
    }

    /**
     * Grab consumer initials with options to grab full first or last name.
     *
     * @param bool|null $first
     * @param bool|null $last
     * @return string
     */
    public function getInitial(?bool $first = true, ?bool $last = true): string
    {
        $firstName = $first ? mb_substr($this->{Consumer::FIELD_FIRST_NAME},0,1) : $this->{Consumer::FIELD_FIRST_NAME};
        $lastName = $last ? mb_substr($this->{Consumer::FIELD_LAST_NAME},0,1) : $this->{Consumer::FIELD_LAST_NAME};
        return $firstName .' '. $lastName;
    }

    /**
     * @return BelongsTo
     */
    public function legacyLead(): BelongsTo
    {
        return $this->belongsTo(EloquentQuote::class, self::FIELD_LEGACY_ID, EloquentQuote::ID);
    }

    /**
     * @return BelongsTo
     */
    public function website(): BelongsTo
    {
        return $this->belongsTo(Website::class, self::FIELD_WEBSITE_ID, Website::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function calls(): HasMany
    {
        return $this->hasMany(Call::class, Call::FIELD_RELATION_ID, self::FIELD_ID)
            ->where(Call::FIELD_RELATION_TYPE, Call::RELATION_LEAD);
    }

    /**
     * @return HasMany
     */
    public function texts(): HasMany
    {
        return $this->hasMany(Text::class, Text::FIELD_RELATION_ID, self::FIELD_ID)
            ->where(Text::FIELD_RELATION_TYPE, Text::RELATION_LEAD);
    }

    public function watchdogVideos(): HasMany
    {
        return $this->hasMany(WatchdogVideo::class);
    }

    /**
     * @return bool|null
     * @todo this logic is copied over from transformers, null return needs to be re-evaluated
     */
    public function getIsValidPhoneAttribute(): ?bool
    {
        $verifiedClassifications = [
            Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_SMS,
            Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_CALL,
            Consumer::CLASSIFICATION_VERIFIED_VIA_REVALIDATION_EMAIL,
            Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_LEAD_PROCESSING,
            Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_REVALIDATION_SMS
        ];

        if (in_array($this->{Consumer::FIELD_CLASSIFICATION}, $verifiedClassifications, true)) {
            return true;
        }

        if ($this->{Consumer::FIELD_CLASSIFICATION} === Consumer::CLASSIFICATION_UNVERIFIED_PHONE) {
            return false;
        }

        return null;

    }

    /**
     * @return BelongsTo
     */
    public function clonedFrom(): BelongsTo
    {
        return $this->belongsTo(self::class);
    }

    /**
     * @return HasMany
     */
    public function clones(): HasMany
    {
        return $this->hasMany(
            self::class,
            self::FIELD_CLONED_FROM_ID,
            self::FIELD_ID
        );
    }

    /**
     * @return HasManyThrough
     */
    public function productAssignments(): HasManyThrough
    {
        return $this->hasManyThrough(
            ProductAssignment::class,
            ConsumerProduct::class,
            ConsumerProduct::FIELD_CONSUMER_ID,
            ProductAssignment::FIELD_CONSUMER_PRODUCT_ID,
            self::FIELD_ID,
            ConsumerProduct::FIELD_ID,
        );

    }

    public function leadProcessingCommunications(): HasManyThrough
    {
        return $this->hasManyThrough(
            LeadProcessingCommunication::class,
            ConsumerProduct::class,
            ConsumerProduct::FIELD_CONSUMER_ID,
            LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID,
            self::FIELD_ID,
            ConsumerProduct::FIELD_ID,
        );
    }

    /**
     * @return HasMany
     */
    public function marketingConsumer(): HasMany
    {
        return $this->hasMany(
            MarketingCampaignConsumer::class,
            MarketingCampaignConsumer::FIELD_CONSUMER_REFERENCE,
            self::FIELD_REFERENCE
        );
    }

    public function mostRecentMarketingConsumer()
    {
        return $this->marketingConsumer()->orderByDesc(MarketingCampaignConsumer::FIELD_ID)->first();
    }

    /**
     * @return HasOne
     */
    public function auth(): HasOne
    {
        return $this->hasOne(ConsumerAuth::class);
    }

    /**
     * @return bool
     */
    public function hasSecondaryServices(): bool
    {
        return $this->consumerProducts()
            ->where(ConsumerProduct::FIELD_IS_SECONDARY_SERVICE, true)
            ->exists();
    }

    /**
     * @return HasMany
     */
    public function processingActivities(): HasMany
    {
        return $this->hasMany(ConsumerProcessingActivity::class, ConsumerProcessingActivity::FIELD_CONSUMER_ID, self::FIELD_ID);
    }
}
