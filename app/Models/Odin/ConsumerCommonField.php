<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\Enums\ConsumerFieldCategory;
use Carbon\Carbon;

/**
 * @property int $id
 * @property string $name
 * @property string $key
 * @property boolean $is_visible
 *
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class ConsumerCommonField extends BaseModel
{
    const TABLE = 'consumer_common_fields';

    const FIELD_ID          = 'id';
    const FIELD_NAME        = 'name';
    const FIELD_KEY         = 'key';
    const FIELD_CATEGORY    = 'category';

    protected $casts = [
        self::FIELD_CATEGORY => ConsumerFieldCategory::class
    ];

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];
}

