<?php

namespace App\Models\Odin;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\CompanyOptInName;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @Property int $id
 * @Property int $consumer_product_id
 * @Property int $company_id
 * @property ?int $company_campaign_id
 * @property ?int $company_opt_in_name_id
 *
 * @property-read ConsumerProduct $consumerProduct
 * @property-read Company $company
 * @property-read CompanyCampaign $campaign
 * @property-read CompanyOptInName $companyOptInName
 */
class OptInCompany extends Model
{
    use HasFactory;

    const string TABLE = 'opt_in_companies';

    const string FIELD_ID                     = 'id';
    const string FIELD_CONSUMER_PRODUCT_ID    = 'consumer_product_id';
    const string FIELD_COMPANY_ID             = 'company_id';
    const string FIELD_COMPANY_CAMPAIGN_ID    = 'company_campaign_id';
    const string FIELD_COMPANY_OPT_IN_NAME_ID = 'company_opt_in_name_id';
    const string FIELD_CREATED_AT             = 'created_at';
    const string FIELD_UPDATED_AT             = 'updated_at';

    const string RELATION_CONSUMER_PRODUCT    = 'consumerProduct';
    const string RELATION_COMPANY             = 'company';
    const string RELATION_COMPANY_OPT_IN_NAME = 'companyOptInName';

    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function consumerProduct(): BelongsTo
    {
        return $this->belongsTo(ConsumerProduct::class);
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * @return BelongsTo
     */
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(CompanyCampaign::class);
    }

    /**
     * @return BelongsTo
     */
    public function companyOptInName(): BelongsTo
    {
        return $this->belongsTo(CompanyOptInName::class);
    }
}
