<?php

namespace App\Models\Odin;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $product_campaign_id
 * @property int $schedule_id
 *
 * @property-read ProductCampaign $productCampaign
 */
class ProductCampaignSchedule extends Model
{
    use HasFactory;

    const TABLE = 'product_campaign_schedules';

    const FIELD_ID = 'id';
    const FIELD_PRODUCT_CAMPAIGN_ID = 'product_campaign_id';
    const FIELD_SCHEDULE_ID = 'schedule_id';
    const FIELD_CREATED_AT = 'created_at';
    const FIELD_UPDATED_AT = 'updated_at';

    protected $table = self::TABLE;
    protected $guarded = [
        self::FIELD_ID
    ];

    /**
     * @return BelongsTo
     */
    public function productCampaign(): BelongsTo
    {
        return $this->belongsTo(ProductCampaign::class, self::FIELD_PRODUCT_CAMPAIGN_ID, ProductCampaign::FIELD_ID);
    }
}
