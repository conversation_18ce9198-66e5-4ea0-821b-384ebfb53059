<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use App\Models\Contract;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int $id
 * @property string $name
 * @property string $url
 * @property string $abbreviation
 * @property string $cp_domain
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read WebsiteApiKey[] $apiKeys
 * @property-read Industry[] $industries
 */
class Website extends BaseModel
{
    use HasFactory;
    const TABLE = 'websites';

    const FIELD_ID           = 'id';
    const FIELD_NAME         = 'name';
    const FIELD_URL          = 'url';
    const FIELD_ABBREVIATION = 'abbreviation';
    const FIELD_CP_DOMAIN    = 'cp_domain';

    const RELATION_API_KEYS   = 'apiKeys';
    const RELATION_INDUSTRIES = 'industries';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return HasMany
     */
    public function apiKeys(): HasMany
    {
        return $this->hasMany(WebsiteApiKey::class, WebsiteApiKey::FIELD_WEBSITE_ID, self::FIELD_ID);
    }

    /**
     * @return BelongsToMany
     */
    public function industries(): BelongsToMany
    {
        return $this->belongsToMany(Industry::class, IndustryWebsite::TABLE, IndustryWebsite::FIELD_WEBSITE_ID, IndustryWebsite::FIELD_INDUSTRY_ID);
    }

    public function contracts(): HasMany
    {
        return $this->hasMany(Contract::class, Contract::FIELD_WEBSITE_ID, self::FIELD_ID);
    }
}
