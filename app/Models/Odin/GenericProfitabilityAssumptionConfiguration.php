<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property int $percentage_leads_successful
 * @property int $average_lead_revenue
 * @property int $average_job_cost
 * @property int $labour_materials_cost
 * @property int $company_id
 * @property int $industry_service_id
 * @property bool $is_default
 *
 * @property-read ?Company $company
 * @property-read IndustryService $service
 */
class GenericProfitabilityAssumptionConfiguration extends BaseModel
{
    use HasFactory;

    const TABLE = 'generic_profitability_assumption_configurations';

    const FIELD_ID                          = 'id';
    const FIELD_PERCENTAGE_LEADS_SUCCESSFUL = 'percentage_leads_successful';
    const FIELD_AVERAGE_LEAD_REVENUE        = 'average_lead_revenue';
    const FIELD_AVERAGE_JOB_COST            = 'average_job_cost';
    const FIELD_LABOUR_MATERIALS_COST       = 'labour_materials_cost';
    const FIELD_COMPANY_ID                  = 'company_id';
    const FIELD_INDUSTRY_SERVICE_ID         = 'industry_service_id';
    const FIELD_IS_DEFAULT                  = 'is_default'; // Is a default IndustryService configuration and not attached to a company

    protected $table = self::TABLE;

    protected $guarded = [ self::FIELD_ID ];

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(IndustryService::class, self::FIELD_INDUSTRY_SERVICE_ID, IndustryService::FIELD_ID);
    }

}
