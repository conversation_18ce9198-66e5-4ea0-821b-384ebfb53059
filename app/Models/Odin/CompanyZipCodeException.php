<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use App\Models\Legacy\Location;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * We are now only allowing campaign locations to be defined by counties,
 * this model simply represents a list of exclusions to this rule
 * which will allow specified company/county pairs to still be narrowed down to zip codes
 *
 * @property int $county_location_id
 * @property int $company_id
 * @property int $industry_id
 *
 * @property-read Location $location
 */
class CompanyZipCodeException extends BaseModel
{
    const string TABLE = 'company_zip_code_exceptions';

    const string FIELD_ID                 = 'id';
    const string FIELD_COUNTY_LOCATION_ID = 'county_location_id';
    const string FIELD_COMPANY_ID         = 'company_id';

    const string RELATION_LOCATION = 'location';

    protected $table = self::TABLE;
    protected $guarded = [ self::FIELD_ID ];

    /**
     * @return BelongsTo
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class, self::FIELD_COUNTY_LOCATION_ID);
    }
}
