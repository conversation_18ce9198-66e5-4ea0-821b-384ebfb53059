<?php

namespace App\Models\Odin;

use App\Models\BaseModel;

/**
 * @property int $id
 * @property string $type
 */
class ConfigurableFieldType extends BaseModel
{
    const TABLE = 'configurable_field_types';

    const FIELD_ID = 'id';
    const FIELD_TYPE = 'type';
    const FIELD_LABEL = 'label';

    const TYPE_STRING = 'String';
    const TYPE_FLOAT = 'Float';
    const TYPE_INTEGER = 'Integer';
    const TYPE_BOOLEAN = 'Boolean';
    const TYPE_ARRAY = 'Array';
    const TYPE_URL   = 'URL';

    protected $table = self::TABLE;
    protected $guarded = [
        self::FIELD_ID
    ];
}
