<?php

namespace App\Models\Odin;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

/**
 * @property-read int $id
 * @property-read string $name
 */
class QualityTier extends Model
{
    use HasFactory;

    const TABLE = 'quality_tiers';

    const FIELD_ID = 'id';
    const FIELD_NAME = 'name';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];

    /**
     * @param string|null $mapToColumn
     * @return Collection
     */
    public static function getIdMap(?string $mapToColumn = self::FIELD_NAME): Collection
    {
        return QualityTier::all()
            ->mapWithKeys(fn(QualityTier $qualityTier) => [
                $qualityTier->{$mapToColumn} => $qualityTier->id,
            ]);
    }
}
