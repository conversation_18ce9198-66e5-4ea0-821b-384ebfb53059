<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use App\Models\LeadProcessingHistory;
use App\Models\Legacy\LeadAppointment;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use \App\Enums\Odin\QualityTier as QualityTierEnum;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @property int $id
 * @property int|null $legacy_id
 * @property int $lead_consumer_product_id
 * @property int $consumer_product_id
 * @property QualityTierEnum $appointment_type
 * @property $appointment_date
 * @property $appointment_time
 * @property boolean $allocated_as_lead
 * @property int|null $original_appointment_id
 * @property bool $related_appt_sold_and_unsellable
 *
 * @property-read string $appointment
 * @property-read ConsumerProduct $leadConsumerProduct
 * @property-read ConsumerProduct $consumerProduct
 * @property-read ProductAssignment[] $leadProductAssignments
 * @property-read ProductAssignment[] $productAssignments
 * @property-read ProductAppointment $originalAppointment
 * @property-read LeadProcessingHistory $leadProcessingHistory
 * @property-read LeadAppointment $legacyLeadAppointment
 */
class ProductAppointment extends BaseModel
{
    use HasFactory;

    const TABLE = 'product_appointments';

    const ID = 'id';
    const LEGACY_ID = 'legacy_id';
    const LEAD_CONSUMER_PRODUCT_ID = 'lead_consumer_product_id';
    const CONSUMER_PRODUCT_ID = 'consumer_product_id';
    const APPOINTMENT_TYPE = 'appointment_type';
    const APPOINTMENT_DATE = 'appointment_date';
    const APPOINTMENT_TIME = 'appointment_time';
    const ALLOCATED_AS_LEAD = 'allocated_as_lead';
    const ORIGINAL_APPOINTMENT_ID = 'original_appointment_id';
    const RELATED_APPT_SOLD_AND_UNSELLABLE = 'related_appt_sold_and_unsellable';

    const RELATION_LEAD_CONSUMER_PRODUCT = 'leadConsumerProduct';
    const RELATION_CONSUMER_PRODUCT = 'consumerProduct';
    const RELATION_LEAD_PRODUCT_ASSIGNMENTS = 'leadProductAssignments';
    const RELATION_PRODUCT_ASSIGNMENTS = 'productAssignments';
    const RELATION_ORIGINAL_APPOINTMENT = 'originalAppointment';
    const RELATION_LEAD_PROCESSING_HISTORY = 'leadProcessingHistory';
    const RELATION_LEGACY_LEAD_APPOINTMENT = 'legacyLeadAppointment';
    const RELATION_APPOINTMENT_OFFERINGS = 'appointmentOfferings';

    const APPOINTMENT = 'appointment';

    const TIME_FORMAT = 'H:i:s';
    const DATE_FORMAT = 'Y-m-d';

    protected $table = self::TABLE;
    protected $guarded = [self::ID];

    protected $casts = [
        self::APPOINTMENT_TYPE => QualityTierEnum::class
    ];

    /**
     * @return BelongsTo
     */
    public function leadConsumerProduct(): BelongsTo
    {
        return $this->belongsTo(ConsumerProduct::class, self::LEAD_CONSUMER_PRODUCT_ID, ConsumerProduct::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function consumerProduct(): BelongsTo
    {
        return $this->belongsTo(ConsumerProduct::class, self::CONSUMER_PRODUCT_ID, ConsumerProduct::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function leadProductAssignments(): HasMany
    {
        return $this->hasMany(
            ProductAssignment::class,
            ProductAssignment::FIELD_CONSUMER_PRODUCT_ID,
            self::LEAD_CONSUMER_PRODUCT_ID
        );
    }

    /**
     * @return HasMany
     */
    public function productAssignments(): HasMany
    {
        return $this->hasMany(
            ProductAssignment::class,
            ProductAssignment::FIELD_CONSUMER_PRODUCT_ID,
            self::CONSUMER_PRODUCT_ID
        );
    }

    /**
     * @return BelongsTo
     */
    public function originalAppointment(): BelongsTo
    {
        return $this->belongsTo(ProductAppointment::class, self::ORIGINAL_APPOINTMENT_ID, ProductAppointment::ID);
    }

    /**
     * @return HasMany
     */
    public function leadProcessingHistory(): HasMany
    {
        return $this->hasMany(LeadProcessingHistory::class, LeadProcessingHistory::FIELD_CONSUMER_PRODUCT_ID, self::LEAD_CONSUMER_PRODUCT_ID);
    }

    /**
     * @return HasOne
     */
    public function legacyLeadAppointment(): HasOne
    {
        return $this->hasOne(LeadAppointment::class, LeadAppointment::ID, self::LEGACY_ID);
    }

    /**
     * @return HasMany
     */
    public function appointmentOfferings(): HasMany
    {
        return $this->hasMany(AppointmentOffering::class, AppointmentOffering::FIELD_CONSUMER_PRODUCT_ID, self::CONSUMER_PRODUCT_ID);
    }

    /**
     * @return Attribute
     */
    protected function appointment(): Attribute
    {
        return Attribute::make(
            get: fn($value, $attributes) => "{$this->appointment_date} {$this->appointment_time}",
        );
    }
}
