<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Query\Builder;

/**
 * @property int $id
 * @property int $company_id
 * @property int $address_id
 * @property string $reference
 * @property string $name
 * @property string $phone
 * @property boolean $imported
 * @property boolean $is_primary
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Company $company
 * @property-read Address $address
 * @property-read Collection<CompanyExternalReview> $externalReviews
 * @property-read float|null $average_of_external_review_rating
 * @property-read Collection $nonPurchasingLocations
 * @mixin Builder
 */
class CompanyLocation extends BaseModel
{

    use HasFactory;

    const string TABLE = 'company_locations';

    const string FIELD_ID                = 'id';
    const string FIELD_COMPANY_ID        = 'company_id';
    const string FIELD_ADDRESS_ID        = 'address_id';
    const string FIELD_REFERENCE         = 'reference';
    const string FIELD_NAME              = 'name';
    const string FIELD_PHONE             = 'phone';
    const string FIELD_IMPORTED          = 'imported';
    const string FIELD_IS_PRIMARY        = 'is_primary';
    const string FIELD_CREATED_AT        = 'created_at';
    const string FIELD_UPDATED_AT        = 'updated_at';

    const string RELATION_COMPANY          = 'company';
    const string RELATION_ADDRESS          = 'address';
    const string RELATION_EXTERNAL_REVIEWS = 'externalReviews';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function address(): HasOne
    {
        return $this->hasOne(Address::class, Address::FIELD_ID, self::FIELD_ADDRESS_ID);
    }

    /**
     * @return HasMany
     */
    public function externalReviews(): HasMany
    {
        return $this->hasMany(CompanyExternalReview::class, CompanyExternalReview::FIELD_COMPANY_LOCATION_ID, self::FIELD_ID);
    }

    public function averageOfExternalReviewRating(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->externalReviews->average('agg_value')
        );
    }

    /**
     * @return HasMany
     */
    public function nonPurchasingLocations(): HasMany
    {
        return $this->hasMany(NonPurchasingCompanyLocation::class, NonPurchasingCompanyLocation::FIELD_COMPANY_LOCATION_ID, self::FIELD_ID);
    }
}
