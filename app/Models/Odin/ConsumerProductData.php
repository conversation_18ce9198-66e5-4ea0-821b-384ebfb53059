<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int $id
 * @property array $payload
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * Virtual Fields:
 * @property-read string $ip_address
 *
 * @property-read ConsumerProduct[] $consumerProducts
 */
class ConsumerProductData extends BaseModel
{
    use HasFactory;

    const TABLE = 'consumer_product_data';

    const FIELD_ID      = 'id';
    const FIELD_PAYLOAD = 'payload';

    const VIRTUAL_FIELD_IP_ADDRESS = 'ip_address';
    const VIRTUAL_FIELD_OTHER_INTERESTS = 'other_interests';


    const RELATION_CONSUMER_PRODUCTS = 'consumerProducts';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_PAYLOAD => 'array'
    ];

    /**
     * @return HasMany
     */
    public function consumerProducts(): HasMany
    {
        return $this->hasMany(ConsumerProduct::class, ConsumerProduct::FIELD_CONSUMER_PRODUCT_DATA_ID, self::FIELD_ID);
    }
}
