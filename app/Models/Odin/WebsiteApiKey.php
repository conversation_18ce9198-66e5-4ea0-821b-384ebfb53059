<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int $id
 * @property int $website_id
 * @property string $name
 * @property boolean $status
 * @property string $key
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Website $website
 */
class WebsiteApiKey extends BaseModel
{
    use HasFactory;

    const TABLE = 'website_api_keys';

    const FIELD_ID         = 'id';
    const FIELD_WEBSITE_ID = 'website_id';
    const FIELD_NAME       = 'name';
    const FIELD_STATUS     = 'status';
    const FIELD_KEY        = 'key';

    const RELATION_WEBSITE = 'website';
    const RELATION_ORIGINS = 'origins';

    const STATUS_ACTIVE   = 1;
    const STATUS_INACTIVE = 0;

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_STATUS => 'boolean'
    ];

    /**
     * @return BelongsTo
     */
    public function website(): BelongsTo
    {
        return $this->belongsTo(Website::class, self::FIELD_WEBSITE_ID, Website::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function origins(): HasMany
    {
        return $this->hasMany(WebsiteApiKeyOrigin::class, WebsiteApiKeyOrigin::FIELD_WEBSITE_API_KEY_ID, self::FIELD_ID);
    }
}
