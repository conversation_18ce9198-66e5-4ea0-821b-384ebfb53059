<?php

namespace App\Models\Odin;

use App\Enums\Odin\AppointmentCancellationReason;
use App\Events\ProductCancellationDeleted;
use App\Events\ProductCancellationSaved;
use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property int $product_assignment_id
 * @property AppointmentCancellationReason $reason
 * @property string $note
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $deleted_at
 *
 * @property-read ProductAssignment $productAssignment
 */
class ProductCancellation extends BaseModel
{
    use HasFactory, SoftDeletes;

    const TABLE = 'product_cancellations';

    const FIELD_ID = 'id';
    const FIELD_PRODUCT_ASSIGNMENT_ID = 'product_assignment_id';
    const FIELD_REASON = 'reason';
    const FIELD_NOTE = 'note';
    const FIELD_CREATED_AT = 'created_at';
    const FIELD_UPDATED_AT = 'updated_at';
    const FIELD_DELETED_AT = 'deleted_at';

    const RELATION_PRODUCT_ASSIGNMENT = 'productAssignment';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_REASON => AppointmentCancellationReason::class
    ];

    protected $dispatchesEvents = [
        'saved' => ProductCancellationSaved::class,
        'deleted' => ProductCancellationDeleted::class,
    ];

    /**
     * @return BelongsTo
     */
    public function productAssignment(): BelongsTo
    {
        return $this->belongsTo(ProductAssignment::class, self::FIELD_PRODUCT_ASSIGNMENT_ID, ProductAssignment::FIELD_ID);
    }
}
