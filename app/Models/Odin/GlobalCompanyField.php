<?php

namespace App\Models\Odin;

use App\Database\Casts\AsConfigurableFieldPayload;
use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property string $name
 * @property string $key
 * @property int $type
 * @property boolean $show_on_profile
 * @property boolean $show_on_dashboard
 * @property string $payload
 * @property string $category
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property-read ConfigurableFieldType $fieldType
 */
class GlobalCompanyField extends BaseModel
{
    const TABLE = 'global_company_fields';

    const FIELD_ID                = 'id';
    const FIELD_NAME              = 'name';
    const FIELD_KEY               = 'key';
    const FIELD_TYPE              = 'type';
    const FIELD_SHOW_ON_PROFILE   = 'show_on_profile';
    const FIELD_SHOW_ON_DASHBOARD = 'show_on_dashboard';
    const FIELD_PAYLOAD           = 'payload';
    const FIELD_CATEGORY          = 'category';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_PAYLOAD => AsConfigurableFieldPayload::class
    ];

    /**
     * @return BelongsTo
     */
    public function fieldType(): BelongsTo
    {
        return $this->belongsTo(ConfigurableFieldType::class, self::FIELD_TYPE);
    }
}
