<?php

namespace App\Models\Odin;

use App\Enums\ContactDirectionEnum;
use App\Models\BaseModel;
use App\Models\Call;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModule;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleContact;
use App\Models\CompanyContract;
use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Legacy\EloquentUser;
use App\Models\SalesIntel\UserImportRecord;
use App\Models\Text;
use App\Services\HelperService;
use App\Notifications\PasswordCreate;
use App\Notifications\PasswordReset;
use App\Traits\CanVerifyEmail;
use App\Traits\HasReference;
use Carbon\Carbon;
use Illuminate\Auth\Authenticatable;
use Illuminate\Contracts\Auth\CanResetPassword as CanResetPasswordContract;
use Illuminate\Auth\Passwords\CanResetPassword;
use Illuminate\Contracts\Auth\Access\Authorizable as AuthorizableContract;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Contracts\Auth\MustVerifyEmail as MustVerifyEmailContract;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Query\Builder;
use Illuminate\Foundation\Auth\Access\Authorizable;
use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Notifications\DatabaseNotificationCollection;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Auth;
/**
 * @property int $id
 * @property int $company_id
 * @property string $first_name
 * @property string $last_name
 * @property string $title
 * @property string $department
 * @property string $email
 * @property string $cell_phone
 * @property string $office_phone
 * @property string $notes
 * @property int|null $formatted_cell_phone
 * @property int|null $formatted_office_phone
 * @property string $zoom_info_id
 * @property boolean $can_log_in
 * @property int $status
 * @property int $failed_login_count
 * @property string $reference
 * @property string $password
 * @property int $legacy_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $deleted_at
 * @property boolean $is_contact
 * @property boolean $is_decision_maker
 * @property Carbon|null $phone_verified_at
 * @property int $authentication_type
 * @property boolean $imported
 * @property string $import_source
 * @property string $created_by_id
 * @property string $updated_by_id
 * @property boolean $must_reset_password
 * @property boolean $dnc_contact
 *
 * @property-read Company $company
 * @property-read CompanyUserData $data
 * @property-read CompanyUserLog[] $logs
 * @property-read ProductRejection[] $productRejections
 * @property mixed|null $email_verified_at
 * @property-read int|null $logs_count
 * @property-read DatabaseNotificationCollection<int, DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read int|null $product_rejections_count
 * @property-read Collection<Call> $office_phone_calls
 * @property-read Collection<Text> $texts
 * @property-read Collection<Call> $cell_phone_calls
 * @property-read Collection<Call> $office_phone_calls_over_one_minute
 * @property-read Collection<Call> $cell_phone_calls_over_one_minute
 * @property-read Call|null $latest_call_for_cell_phone
 * @property-read Call|null $latest_call_for_office_phone
 * @property-read Call|null $latest_call
 * @property-read Text|null $latest_text
 * @property-read Text|Call|null $latest_contact
 * @property-read Collection<CompanyContract> $contracts
 * @mixin Builder
 */
class CompanyUser extends BaseModel implements MustVerifyEmailContract, AuthenticatableContract, AuthorizableContract, CanResetPasswordContract
{
    use SoftDeletes, CanVerifyEmail, Notifiable, Authenticatable, Authorizable, HasReference, HasFactory, CanResetPassword;

    const TABLE = 'company_users';

    const FIELD_ID                     = 'id';
    const FIELD_COMPANY_ID             = 'company_id';
    const FIELD_FIRST_NAME             = 'first_name';
    const FIELD_LAST_NAME              = 'last_name';
    const FIELD_TITLE                  = 'title';
    const FIELD_DEPARTMENT             = 'department';
    const FIELD_EMAIL                  = 'email';
    const FIELD_CELL_PHONE             = 'cell_phone';
    const FIELD_OFFICE_PHONE           = 'office_phone';
    const FIELD_FORMATTED_CELL_PHONE   = 'formatted_cell_phone';
    const FIELD_FORMATTED_OFFICE_PHONE = 'formatted_office_phone';
    const FIELD_AUTHENTICATION_TYPE    = 'authentication_type';

    const FIELD_ZOOM_INFO_ID       = 'zoom_info_id';
    const FIELD_CAN_LOG_IN         = 'can_log_in';
    const FIELD_STATUS = 'status';
    const FIELD_NOTES = 'notes';
    const FIELD_PINNED = 'pinned';
    const FIELD_FAILED_LOGIN_COUNT = 'failed_login_count';
    const FIELD_PASSWORD           = 'password';
    const FIELD_LEGACY_ID          = 'legacy_id';
    const FIELD_REFERENCE          = 'reference';
    const FIELD_DELETED_AT         = 'deleted_at';
    const FIELD_IS_CONTACT         = 'is_contact';
    const FIELD_IS_DECISION_MAKER  = 'is_decision_maker';
    const FIELD_EMAIL_VERIFIED_AT  = 'email_verified_at';
    const FIELD_PHONE_VERIFIED_AT  = 'phone_verified_at';
    const FIELD_CREATED_AT         = 'created_at';

    const FIELD_IMPORTED           = 'imported';
    const FIELD_IMPORT_SOURCE      = 'import_source';
    const FIELD_SCHEDULING_USER_REFERENCE = 'scheduling_user_reference';
    const FIELD_CAN_RECEIVE_PROMOTIONS = 'can_receive_promotions';
    const FIELD_UNSUBSCRIBED_FROM_PROMOTIONS = 'unsubscribed_from_promotions';

    const FIELD_MUST_RESET_PASSWORD          = 'must_reset_password';

    const FIELD_CREATED_BY_ID = 'created_by_id';
    const FIELD_UPDATED_BY_ID = 'updated_by_id';
    const string FIELD_DNC_CONTACT = 'dnc_contact';

    const RELATION_COMPANY            = 'company';
    const RELATION_DATA               = 'data';
    const RELATION_LOGS               = 'logs';
    const RELATION_PRODUCT_REJECTIONS = 'productRejections';
    const RELATION_OFFICE_PHONE_CALLS = 'office_phone_calls';
    const RELATION_CELL_PHONE_CALLS   = 'cell_phone_calls';
    const RELATION_OFFICE_PHONE_TEXTS = 'officePhoneTexts';
    const RELATION_CELL_PHONE_TEXTS   = 'cellPhoneTexts';
    const RELATION_TEXTS   = 'texts';

    const RELATION_CALLS   = 'calls';
    const string RELATION_CONTRACTS = 'contracts';

    const USER_ALLOWED_TO_LOGIN     = 1;
    const USER_NOT_ALLOWED_TO_LOGIN = 0;

    const USER_IS_CONTACT     = 1;
    const USER_IS_NOT_CONTACT = 0;

    const USER_IS_ELIGIBLE_FOR_PROMOTIONS     = 1;
    const USER_IS_NOT_ELIGIBLE_FOR_PROMOTIONS = 0;

    const AUTHENTICATION_TYPE_UNKNOWN   = 0;
    const AUTHENTICATION_TYPE_LEGACY    = 1;
    const AUTHENTICATION_TYPE_ADMIN2    = 2;
    const AUTHENTICATION_TYPE_MAPPING = [
        0   => 'Unknown',
        1   => 'Legacy',
        2   => 'Admin2',
    ];
    const AUTHENTICATION_MINIMUM_PASSWORD_LENGTH = 8;

    // todo: define statuses
    const STATUS_INACTIVE   = 0;
    const STATUS_ACTIVE     = 1;
    const STATUS_MAPPING = [
        0 => 'Inactive',
        1 => 'Active',
    ];

    const IMPORT_SOURCE_MANUAL = 'manual';
    const IMPORT_SOURCE_SALES_INTEL = 'salesintel';
    const IMPORT_SOURCE_ZOOM = 'zoom';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID, self::FIELD_PASSWORD];

    protected $casts = [
        self::FIELD_DELETED_AT => 'datetime',
    ];

    protected static function booted()
    {
        parent::booted();

        static::created(function (CompanyUser $companyUser) {
            if (!$companyUser->reference) {
                $companyUser->saveReference('uuid');
            }

            if ($companyUser->import_source === CompanyUser::IMPORT_SOURCE_SALES_INTEL) {
                UserImportRecord::create([
                    'company_id' => $companyUser->company->id,
                    'company_user_id' => $companyUser->id
                ]);
            }
        });

        static::saving(function (CompanyUser $companyUser) {
            self::updatePhoneNumbers($companyUser);
        });
    }

    private static function updatePhoneNumbers(CompanyUser $companyUser)
    {
        $companyUser->{self::FIELD_FORMATTED_CELL_PHONE}   = $companyUser->{self::FIELD_CELL_PHONE} ? intval(HelperService::removeNonNumbers($companyUser->{self::FIELD_CELL_PHONE})) : null;
        $companyUser->{self::FIELD_FORMATTED_OFFICE_PHONE} = $companyUser->{self::FIELD_OFFICE_PHONE} ? intval(HelperService::removeNonNumbers($companyUser->{self::FIELD_OFFICE_PHONE})) : null;
    }

    /**
     * @return string
     */
    public function completeName(): string
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function data(): HasOne
    {
        return $this->hasOne(CompanyUserData::class, CompanyUserData::FIELD_COMPANY_USER_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function logs(): HasMany
    {
        return $this->hasMany(CompanyUserLog::class, CompanyUserLog::FIELD_COMPANY_USER_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function productRejections(): HasMany
    {
        return $this->hasMany(ProductRejection::class, ProductRejection::FIELD_COMPANY_USER_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function office_phone_calls(): HasMany
    {
        return $this->hasMany(Call::class, Call::FIELD_FORMATTED_OTHER_NUMBER, self::FIELD_FORMATTED_OFFICE_PHONE);
    }

    /**
     * @return HasMany
     */
    public function cell_phone_calls(): HasMany
    {
        return $this->hasMany(Call::class, Call::FIELD_FORMATTED_OTHER_NUMBER, self::FIELD_FORMATTED_CELL_PHONE);
    }

    /**
     * @return HasMany
     */
    public function officePhoneTexts(): HasMany
    {
        return $this->hasMany(Text::class, Text::FIELD_OTHER_NUMBER, self::FIELD_FORMATTED_OFFICE_PHONE);
    }

    /**
     * @return HasMany
     */
    public function cellPhoneTexts(): HasMany
    {
        return $this->hasMany(Text::class, Text::FIELD_OTHER_NUMBER, self::FIELD_FORMATTED_CELL_PHONE);
    }

    /**
     * @return Attribute
     */
    public function officePhoneCallsOverOneMinute(): Attribute
    {
        $calls = $this->office_phone_calls->filter(function (Call $call) {
            return $call->duration_in_minutes >= 1;
        });

        return Attribute::make(
            get: fn() => $calls
        );
    }

    /**
     * @return Attribute
     */
    public function cellPhoneCallsOverOneMinute(): Attribute
    {
        $calls = $this->cell_phone_calls->filter(function (Call $call) {
            return $call->duration_in_minutes >= 1;
        });

        return Attribute::make(
            get: fn() => $calls
        );
    }

    /**
     * @return Attribute<Call>
     */
    public function latestCallForOfficePhone(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->getLatestCallForOfficePhone()
        );
    }

    /**
     * @return Attribute<Call>
     */
    public function latestCallForCellPhone(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->getLatestCallForCellPhone()
        );
    }

    /**
     * @param ContactDirectionEnum|null $direction
     * @return Call|null
     */
    private function getLatestCallForCellPhone(?ContactDirectionEnum $direction = null): ?Call
    {
        $cellPhoneCalls = $this->cell_phone_calls;
        if ($direction !== null) {
            $cellPhoneCalls = $cellPhoneCalls->where(Call::FIELD_DIRECTION, '=', $direction->value);
        }

        return $cellPhoneCalls->sortByDesc('call_end')->first();
    }

    /**
     * @param ContactDirectionEnum|null $direction
     * @return Call|null
     */
    private function getLatestCallForOfficePhone(?ContactDirectionEnum $direction = null): ?Call
    {
        $officePhoneCalls = $this->office_phone_calls;

        if ($direction !== null) {
            $officePhoneCalls = $officePhoneCalls->where(Call::FIELD_DIRECTION, '=', $direction->value);
        }

        return $officePhoneCalls->sortByDesc('call_end')->first();
    }

    /**
     * @param ContactDirectionEnum|null $direction
     * @return Text|null
     */
    private function getLatestTextForCellPhone(?ContactDirectionEnum $direction = null): ?Text
    {
        $cellPhoneTexts = $this->cellPhoneTexts()->get();
        if ($direction !== null) {
            $cellPhoneTexts = $cellPhoneTexts->where(Text::FIELD_DIRECTION, '=', $direction->value);
        }

        return $cellPhoneTexts->sortByDesc(Text::FIELD_CREATED_AT)->first();
    }

    /**
     * @param ContactDirectionEnum|null $direction
     * @return Text|null
     */
    private function getLatestTextForOfficePhone(?ContactDirectionEnum $direction = null): ?Text
    {
        $officePhoneTexts = $this->officePhoneTexts()->get();

        if ($direction !== null) {
            $officePhoneTexts = $officePhoneTexts->where(Text::FIELD_DIRECTION, '=', $direction->value);
        }

        return $officePhoneTexts->sortByDesc(Text::FIELD_CREATED_AT)->first();
    }

    /**
     * @param ContactDirectionEnum|null $direction
     * @return Call|null
     */
    public function latestCallByDirection(?ContactDirectionEnum $direction = null): ?Call
    {
        $latestCallForOfficePhone = $this->getLatestCallForOfficePhone($direction);
        $latestCallForCellPhone   = $this->getLatestCallForCellPhone($direction);

        if ($latestCallForCellPhone instanceof Call && $latestCallForOfficePhone instanceof Call) {
            $latestOfficePhoneCallEnd = $latestCallForOfficePhone->call_end;
            $latestCellPhoneCallEnd = $latestCallForCellPhone->call_end;

            if($latestOfficePhoneCallEnd instanceof Carbon && $latestCellPhoneCallEnd instanceof Carbon) {
                $result = $latestOfficePhoneCallEnd->gt($latestCellPhoneCallEnd) ? $latestCallForOfficePhone : $latestCallForCellPhone;
            } else if ($latestOfficePhoneCallEnd instanceof Carbon) {
                $result = $latestCallForOfficePhone;
            } else if ($latestCellPhoneCallEnd instanceof Carbon) {
                $result = $latestCallForCellPhone;
            } else {
                $result = null;
            }
        } else if ($latestCallForCellPhone instanceof Call) {
            $result = $latestCallForCellPhone;
        } else if ($latestCallForOfficePhone instanceof Call) {
            $result = $latestCallForOfficePhone;
        } else {
            $result = null;
        }

        return $result;
    }

    /**
     * @return Attribute
     */
    public function latestCall(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->latestCallByDirection()
        );
    }

    /**
     * @return Attribute
     */
    public function latestText(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->latestTextByDirection()
        );
    }

    /**
     * @param ContactDirectionEnum|null $direction
     * @return Text|null
     */
    public function latestTextByDirection(?ContactDirectionEnum $direction = null): ?Text
    {
        $latestOfficeText = $this->getLatestTextForOfficePhone($direction);
        $latestCellText = $this->getLatestTextForCellPhone($direction);

        if ($latestOfficeText === null && $latestCellText === null) {
            $result = null;
        } elseif ($latestOfficeText === null) {
            $result = $latestCellText;
        } elseif ($latestCellText === null) {
            $result = $latestOfficeText;
        } else {
            if ($latestOfficeText->{Text::FIELD_CREATED_AT}->gt($latestCellText->{Text::FIELD_CREATED_AT})) {
                $result =  $latestOfficeText;
            } else {
                $result = $latestCellText;
            }
        }

        return $result;
    }

    public function latestContact(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->latestContactByDirection()
        );
    }


    /**
     * @param ContactDirectionEnum|null $direction
     * @return Call|Text|null
     */
    public function latestContactByDirection(?ContactDirectionEnum $direction = null): Call|Text|null
    {
        $latestCall = $this->latestCallByDirection($direction) ?? null;
        $latestText = $this->latestTextByDirection($direction) ?? null;


        if ($latestCall === null && $latestText === null) {
            $result = null;
        } elseif ($latestCall === null) {
            $result = $latestText;
        } elseif ($latestText === null) {
            $result = $latestCall;
        } else {
            if ($latestCall->{Call::FIELD_CALL_END}->gt($latestText->{Text::FIELD_CREATED_AT})) {
                $result =  $latestCall;
            } else {
                $result = $latestText;
            }
        }

        return $result;
    }

    /**
     * Scope a query to only include decision makers.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return void
     */
    public function scopeDecisionMaker($query)
    {
        $query->where(CompanyUser::FIELD_IS_DECISION_MAKER, 1);
    }

    public function calls(): hasMany
    {
        return $this->hasMany(Call::class, Call::FIELD_RELATION_ID, self::FIELD_ID)
            ->where(Call::FIELD_RELATION_TYPE, 'company_user');
    }

    public function texts(): hasMany
    {
        return $this->hasMany(Text::class, Text::FIELD_RELATION_ID, self::FIELD_ID)
            ->where(Text::FIELD_RELATION_TYPE, 'company_user');
    }

    public function getAuthPasswordName(): string
    {
        return 'password';
    }

    public function contracts(): HasMany
    {
        return $this->hasMany(CompanyContract::class, CompanyContract::FIELD_COMPANY_USER_ID, self::FIELD_ID);
    }

    /**
     * @return bool
     */
    public function isCampaignDeliveryContact(): bool
    {
        return CompanyCampaignDeliveryModuleContact::query()
            ->where(CompanyCampaignDeliveryModuleContact::FIELD_CONTACT_ID, $this->id)
            ->where(CompanyCampaignDeliveryModuleContact::FIELD_ACTIVE, true)
            ->has(CompanyCampaignDeliveryModuleContact::RELATION_MODULE . '.' . CompanyCampaignDeliveryModule::RELATION_CAMPAIGN)
            ->exists();
    }

}
