<?php

namespace App\Models\Odin;

use App\Enums\ActivityLog\ActivityLogName;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Bidding\CompanyCampaignBidPriceModule;
use App\Models\Legacy\Location;
use App\Models\SaleType;
use App\Services\ActivityLogService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Models\Activity;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * @property-read int $id
 * @property-read int $state_location_id
 * @property-read int $product_campaign_id
 * @property-read int $service_product_id
 * @property-read int $sale_type_id
 * @property-read int $quality_tier_id
 * @property-read int $property_type_id
 * @property int $module_id
 * @property-read float $price
 *
 * @property-read ServiceProduct $serviceProduct
 * @property-read Location $location
 * @property-read QualityTier $qualityTier
 * @property-read SaleType $saleType
 * @property-read PropertyType $propertyType
 * @property-read Location $stateLocation
 * @property-read CompanyCampaignBidPriceModule $biddingModule
 */
class ProductStateBidPrice extends Model
{
    use HasFactory, LogsActivity;

    const TABLE = 'product_state_bid_prices';

    const FIELD_ID = 'id';
    const FIELD_STATE_LOCATION_ID = 'state_location_id';
    const FIELD_PRODUCT_CAMPAIGN_ID = 'product_campaign_id';
    const FIELD_SERVICE_PRODUCT_ID = 'service_product_id';
    const FIELD_SALE_TYPE_ID = 'sale_type_id';
    const FIELD_QUALITY_TIER_ID = 'quality_tier_id';
    const FIELD_PROPERTY_TYPE_ID = 'property_type_id';
    const FIELD_PRICE = 'price';
    const FIELD_MODULE_ID = 'module_id';

    const RELATION_SERVICE_PRODUCT = 'serviceProduct';
    const RELATION_LOCATION = 'location';
    const RELATION_QUALITY_TIER = 'qualityTier';
    const RELATION_SALE_TYPE = 'saleType';
    const RELATION_PROPERTY_TYPE = 'propertyType';
    const RELATION_STATE_LOCATION = 'stateLocation';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];

    /**
     * @return BelongsTo
     */
    public function serviceProduct(): BelongsTo
    {
        return $this->belongsTo(ServiceProduct::class, self::FIELD_SERVICE_PRODUCT_ID, ServiceProduct::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class, self::FIELD_STATE_LOCATION_ID, Location::ID);
    }

    /**
     * @return BelongsTo
     */
    public function stateLocation(): BelongsTo
    {
        return $this->belongsTo(Location::class, self::FIELD_STATE_LOCATION_ID, Location::ID);
    }

    /**
     * @return BelongsTo
     */
    public function qualityTier(): BelongsTo
    {
        return $this->belongsTo(QualityTier::class, self::FIELD_QUALITY_TIER_ID, QualityTier::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function saleType(): BelongsTo
    {
        return $this->belongsTo(SaleType::class, self::FIELD_SALE_TYPE_ID, SaleType::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function propertyType(): BelongsTo
    {
        return $this->belongsTo(PropertyType::class, self::FIELD_PROPERTY_TYPE_ID, PropertyType::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function biddingModule(): BelongsTo
    {
        return $this->belongsTo(CompanyCampaignBidPriceModule::class, self::FIELD_MODULE_ID, CompanyCampaignBidPriceModule::FIELD_ID);
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([self::FIELD_PRICE])
            ->useLogName(ActivityLogName::CAMPAIGN_STATE_BIDS_UPDATED->value)
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /** Add context to activity logs */
    public function tapActivity(Activity $activity): void
    {
        $activity->subject_id = $this->biddingModule->company_campaign_id;
        $activity->subject_type = CompanyCampaign::class;
        $props = $activity->properties;
        $props->put(ActivityLogService::PAYLOAD_DETAILS, [
            self::FIELD_ID                => $this->id,
            self::FIELD_QUALITY_TIER_ID   => $this->quality_tier_id,
            self::FIELD_SALE_TYPE_ID      => $this->sale_type_id,
            self::FIELD_STATE_LOCATION_ID => $this->state_location_id,
        ]);
        $activity->properties = $props;
    }
}
