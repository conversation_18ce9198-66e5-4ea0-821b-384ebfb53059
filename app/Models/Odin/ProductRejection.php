<?php

namespace App\Models\Odin;

use App\Events\ProductRejectionDeleted;
use App\Events\ProductRejectionSaved;
use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property int $product_assignment_id
 * @property int $company_user_id
 * @property string $reason
 * @property boolean $conversion_uploaded
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $deleted_at
 *
 * @property-read ProductAssignment $productAssignment
 * @property-read CompanyUser $user
 */
class ProductRejection extends BaseModel
{
    use SoftDeletes, HasFactory;

    const TABLE = 'product_rejections';

    const FIELD_ID                    = 'id';
    const FIELD_PRODUCT_ASSIGNMENT_ID = 'product_assignment_id';
    const FIELD_COMPANY_USER_ID       = 'company_user_id';
    const FIELD_REASON                = 'reason';
    const FIELD_CONVERSION_UPLOADED   = 'conversion_uploaded';
    const FIELD_DELETED_AT            = 'deleted_at';

    const RELATION_PRODUCT_ASSIGNMENT = 'productAssignment';
    const RELATION_USER               = 'user';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_DELETED_AT => 'datetime',
    ];

    protected $dispatchesEvents = [
        'saved' => ProductRejectionSaved::class,
        'deleted' => ProductRejectionDeleted::class,
    ];

    /**
     * @return BelongsTo
     */
    public function productAssignment(): BelongsTo
    {
        return $this->belongsTo(ProductAssignment::class, self::FIELD_PRODUCT_ASSIGNMENT_ID, ProductAssignment::FIELD_ID);
    }
}
