<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use App\Models\EmailTemplate;
use App\Models\LeadProcessingTeam;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property string $slug
 * @property string $name
 * @property int|null delivery_email_template_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read IndustryType[] $types
 * @property-read Collection<IndustryService> $services
 * @property-read IndustryWebsite[] $websites
 * @property-read Collection<IndustryCompanyField> $companyFields
 * @property-read IndustryConsumerField[] $consumerFields
 * @property-read IndustryCompanyReviewField[] $companyReviewFields
 * @property-read IndustryConfiguration $industryConfiguration
 * @property-read EmailTemplate|null $deliveryEmailTemplate
 */
class Industry extends BaseModel
{
    use HasFactory;
    const TABLE = 'industries';

    const FIELD_ID                         = 'id';
    const FIELD_NAME                       = 'name';
    const FIELD_SLUG                       = 'slug';
    const FIELD_COLOR_LIGHT                = 'light_mode_color';
    const FIELD_COLOR_DARK                 = 'dark_mode_color';
    const FIELD_DELIVERY_EMAIL_TEMPLATE_ID = 'delivery_email_template_id';

    const RELATION_TYPES                  = 'types';
    const RELATION_SERVICES               = 'services';
    const RELATION_WEBSITES               = 'websites';
    const RELATION_COMPANY_FIELDS         = 'companyFields';
    const RELATION_CONSUMER_FIELDS        = 'consumerFields';
    const RELATION_COMPANY_REVIEW_FIELDS  = 'companyReviewFields';
    const RELATION_INDUSTRY_CONFIGURATION = 'industryConfiguration';

    const INDUSTRY_SOLAR   = 'Solar';
    const INDUSTRY_ROOFING = 'Roofing';


    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    private static function slugify(string $str): string
    {
        return strtolower(str_replace(' ', '-', $str));
    }

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted(): void
    {
        static::creating(function (Industry $industry) {
            $industryNameAlreadyExists = Industry::query()->where(self::FIELD_NAME, $industry->name)->exists();

            if (!$industryNameAlreadyExists) {
                $industry->{self::FIELD_SLUG} = self::slugify($industry->name);
            } else {
                $name = $industry->name;

                throw new Exception("Industry name, \"$name\", already exists.");
            }
        });

        static::updating(function (Industry $industry) {
            $industryNameAlreadyExists = Industry::query()->where(self::FIELD_NAME, $industry->name)->where(self::FIELD_ID, '!=', $industry->id)->exists();

            if (!$industryNameAlreadyExists) {
                $industry->{self::FIELD_SLUG} = self::slugify($industry->name);
            } else {
                $name = $industry->name;

                throw new Exception("Industry name, \"$name\", already exists.");
            }
        });
    }

    /**
     * @return HasMany
     */
    public function types(): HasMany
    {
        return $this->hasMany(IndustryType::class, IndustryType::FIELD_INDUSTRY_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function services(): HasMany
    {
        return $this->hasMany(IndustryService::class, IndustryService::FIELD_INDUSTRY_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function websites(): HasMany
    {
        return $this->hasMany(IndustryWebsite::class, IndustryWebsite::FIELD_INDUSTRY_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function companyFields(): HasMany
    {
        return $this->hasMany(IndustryCompanyField::class, IndustryCompanyField::FIELD_INDUSTRY_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function consumerFields(): HasMany
    {
        return $this->hasMany(IndustryConsumerField::class, IndustryConsumerField::FIELD_INDUSTRY_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function companyReviewFields(): HasMany
    {
        return $this->hasMany(IndustryCompanyReviewField::class, IndustryCompanyReviewField::FIELD_INDUSTRY_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function leadProcessingTeams(): HasMany
    {
        return $this->hasMany(LeadProcessingTeam::class, LeadProcessingTeam::FIELD_INDUSTRY_ID, self::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function industryConfiguration(): HasOne
    {
        return $this->hasOne(IndustryConfiguration::class, IndustryConfiguration::FIELD_INDUSTRY_ID, self::FIELD_ID);
    }

    public static function getAsKeyValueSelectArray(): array
    {
        $result = [];

        /**
         * @var self $industry
         */
        foreach (self::all() as $industry) {
            $result[$industry->name] = $industry->id;
        }

        return $result;
    }

    /**
     * Defines the relationship between this industry and the email template for product delivery.
     *
     * @return HasOne
     */
    public function deliveryEmailTemplate(): HasOne
    {
        return $this->hasOne(EmailTemplate::class, EmailTemplate::FIELD_ID, self::FIELD_DELIVERY_EMAIL_TEMPLATE_ID);
    }

    // todo: companies
    // todo: products
}
