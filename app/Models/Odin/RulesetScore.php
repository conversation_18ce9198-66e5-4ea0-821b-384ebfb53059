<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use App\Models\Ruleset;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property array $rules
 * @property int $industry_id
 * @property int $total_points
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class RulesetScore extends BaseModel
{
    const TABLE = 'ruleset_scores';
    const FIELD_ID = 'id';
    const FIELD_MODEL_TYPE = 'model_type';
    const FIELD_MODEL_ID = 'model_id';
    const FIELD_RULESET_ID = 'ruleset_id';
    const FIELD_DATA = 'data';

    const FIELD_CALCULATED_AT = 'calculated_at';
    const FIELD_SCORE = 'score';
    const FIELD_CREATED_AT = 'created_at';
    const FIELD_UPDATED_AT = 'updated_at';

    const RELATION_RULESET = 'ruleset';

    protected $table = self::TABLE;

    protected $fillable = [
        self::FIELD_MODEL_TYPE,
        self::FIELD_MODEL_ID,
        self::FIELD_RULESET_ID,
        self::FIELD_CALCULATED_AT,
        self::FIELD_SCORE,
    ];

    protected $casts = [
        self::FIELD_DATA => 'array',
    ];

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function ruleset(): BelongsTo
    {
        return $this->belongsTo(Ruleset::class, self::FIELD_RULESET_ID, Ruleset::FIELD_ID);
    }
}
