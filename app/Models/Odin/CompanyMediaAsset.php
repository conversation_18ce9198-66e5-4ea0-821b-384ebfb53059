<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int    $id
 * @property int    $company_id
 * @property string $name
 * @property string $url
 * @property int    $type
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Company $company
 */
class CompanyMediaAsset extends BaseModel
{
    use HasFactory;
    const TABLE = 'company_media_assets';

    const FIELD_ID          = 'id';
    const FIELD_COMPANY_ID  = 'company_id';
    const FIELD_NAME        = 'name';
    const FIELD_URL         = 'url';
    const FIELD_TYPE        = 'type';
    const FIELD_CREATED_AT  = 'created_at';
    const FIELD_UPDATED_AT  = 'updated_at';

    const RELATION_COMPANY  = 'company';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, Company::FIELD_ID, self::FIELD_COMPANY_ID);
    }
}
