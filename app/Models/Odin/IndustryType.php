<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $global_type_id
 * @property int $industry_id
 * @property string $name
 * @property string $key
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Industry $industry
 * @property-read GlobalType $globalType
 */
class IndustryType extends BaseModel
{
    const TABLE = 'industry_types';

    const FIELD_ID             = 'id';
    const FIELD_GLOBAL_TYPE_ID = 'global_type_id';
    const FIELD_INDUSTRY_ID    = 'industry_id';
    const FIELD_NAME           = 'name';
    const FIELD_KEY            = 'key';

    const RELATION_INDUSTRY    = 'industry';
    const RELATION_GLOBAL_TYPE = 'globalType';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function industry(): BelongsTo
    {
        return $this->belongsTo(Industry::class, self::FIELD_INDUSTRY_ID, Industry::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function globalType(): BelongsTo
    {
        return $this->belongsTo(GlobalType::class, self::FIELD_GLOBAL_TYPE_ID, GlobalType::FIELD_ID);
    }

    // todo: companies
}
