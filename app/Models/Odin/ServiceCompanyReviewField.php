<?php

namespace App\Models\Odin;

use App\Database\Casts\AsConfigurableFieldPayload;
use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $industry_service_id
 * @property string $name
 * @property string $key
 * @property int $type
 * @property string $payload
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read IndustryService $service
 */
class ServiceCompanyReviewField extends BaseModel
{
    const TABLE = 'service_company_review_fields';

    const FIELD_ID                  = 'id';
    const FIELD_INDUSTRY_SERVICE_ID = 'industry_service_id';
    const FIELD_NAME                = 'name';
    const FIELD_KEY                 = 'key';
    const FIELD_TYPE                = 'type';
    const FIELD_PAYLOAD             = 'payload';

    const RELATION_SERVICE = 'service';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    // todo: define type values

    protected $casts = [
        self::FIELD_PAYLOAD => AsConfigurableFieldPayload::class
    ];

    /**
     * @return BelongsTo
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(IndustryService::class, self::FIELD_INDUSTRY_SERVICE_ID, IndustryService::FIELD_ID);
    }
}
