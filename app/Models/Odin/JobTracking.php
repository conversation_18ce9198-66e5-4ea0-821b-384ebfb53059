<?php

namespace App\Models\Odin;

use App\Enums\Odin\JobStatus;
use App\Enums\Odin\JobTrackingRelation;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property JobTrackingRelation $relation
 * @property int $relation_id
 * @property string $job_uuid
 * @property JobStatus $status
 * @property Carbon $create_at
 * @property Carbon $updated_at
 * @property int|null $available_at
 * @property int $attempts
 */
class JobTracking extends Model
{
    use HasFactory;

    const string TABLE = 'job_tracking';

    const string ID = 'id';
    const string RELATION = 'relation';
    const string RELATION_ID = 'relation_id';
    const string JOB_UUID = 'job_uuid';
    const string STATUS = 'status';
    const string AVAILABLE_AT = 'available_at';
    const string ATTEMPTS = 'attempts';

    protected $table = self::TABLE;

    protected $casts = [
        self::RELATION => JobTrackingRelation::class,
        self::STATUS => JobStatus::class
    ];

     protected $guarded = [
         self::ID
     ];
}
