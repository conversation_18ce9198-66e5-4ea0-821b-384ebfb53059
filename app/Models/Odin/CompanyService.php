<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $industry_service_id
 * @property int $company_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read IndustryService $service
 * @property-read Company $company
 */
class CompanyService extends BaseModel
{
    use HasFactory;

    const TABLE = 'company_services';

    const FIELD_ID                  = 'id';
    const FIELD_INDUSTRY_SERVICE_ID = 'industry_service_id';
    const FIELD_COMPANY_ID          = 'company_id';

    const RELATION_SERVICE = 'service';
    const RELATION_COMPANY = 'company';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $fillable = [
        self::FIELD_ID,
        self::FIELD_INDUSTRY_SERVICE_ID,
        self::FIELD_COMPANY_ID,
        self::CREATED_AT,
        self::UPDATED_AT
    ];

    /**
     * @return BelongsTo
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(IndustryService::class, self::FIELD_INDUSTRY_SERVICE_ID, IndustryService::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }
}
