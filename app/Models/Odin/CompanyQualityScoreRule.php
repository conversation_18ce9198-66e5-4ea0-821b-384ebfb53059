<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use App\Models\Ruleset;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int    $id
 * @property string $name
 * @property int    $is_production
 * @property int    $industry_id
 * @property int    $rule_id
 * @property int    $total_points
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Industry $industry
 * @property-read Ruleset  $ruleset
 */
class CompanyQualityScoreRule extends BaseModel
{
    use HasFactory, SoftDeletes;

    const TABLE = 'company_quality_score_rules';
    const FIELD_ID = 'id';
    const FIELD_NAME = 'name';
    const FIELD_IS_PRODUCTION = 'is_production';
    const FIELD_INDUSTRY_ID = 'industry_id';
    const FIELD_RULE_ID = 'rule_id';
    const FIELD_TOTAL_POINTS = 'total_points';
    const FIELD_CREATED_AT = 'created_at';
    const FIELD_UPDATED_AT = 'updated_at';

    const RELATION_INDUSTRY = 'industry';
    const RELATION_RULESET  = 'ruleset';

    protected $table = self::TABLE;

    protected $fillable = [
        self::FIELD_NAME,
        self::FIELD_IS_PRODUCTION,
        self::FIELD_INDUSTRY_ID,
        self::FIELD_RULE_ID,
        self::FIELD_TOTAL_POINTS
    ];

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function industry(): BelongsTo
    {
        return $this->belongsTo(Industry::class, self::FIELD_INDUSTRY_ID, Industry::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function ruleset(): BelongsTo
    {
        return $this->belongsTo(Ruleset::class, self::FIELD_RULE_ID, Ruleset::FIELD_ID);
    }

}
