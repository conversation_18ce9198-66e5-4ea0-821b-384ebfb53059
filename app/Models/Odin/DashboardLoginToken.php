<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $company_id
 * @property int $company_user_id
 * @property int $shadower_id
 * @property string $token
 * @property bool $expired
 * @property Carbon $expires_at
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Company $company
 * @property-read CompanyUser $companyUser
 * @property-read User $shadower
 */
class DashboardLoginToken extends BaseModel
{
    use HasFactory;
    const TABLE = 'dashboard_login_tokens';

    const FIELD_ID              = 'id';
    const FIELD_COMPANY_ID      = 'company_id';
    const FIELD_COMPANY_USER_ID = 'company_user_id';
    const FIELD_SHADOWER_ID     = 'shadower_id';
    const FIELD_TOKEN           = 'token';
    const FIELD_EXPIRED         = 'expired';
    const FIELD_EXPIRES_AT      = 'expires_at';

    const RELATION_COMPANY      = 'company';
    const RELATION_COMPANY_USER = 'companyUser';

    protected $table      = self::TABLE;
    protected $guarded    = [self::FIELD_ID];
    protected $primaryKey = self::FIELD_ID;

    protected $casts = [
        self::FIELD_EXPIRES_AT => 'datetime'
    ];

    /**
     * Define the scope for only searching active classes.
     *
     * @param Builder $query
     * @return void
     */
    public function scopeActive(Builder $query): void
    {
        $query->where(self::FIELD_EXPIRED, false)
            ->where(self::FIELD_EXPIRES_AT, '>=', Carbon::now());
    }

    /**
     * Defines the relationship of a token to a company.
     *
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }

    /**
     * Defines the relationship of a token to a company user.
     *
     * @return BelongsTo
     */
    public function companyUser(): BelongsTo
    {
        return $this->belongsTo(CompanyUser::class, self::FIELD_COMPANY_USER_ID, CompanyUser::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function shadower(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_SHADOWER_ID, User::FIELD_ID);
    }
}
