<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $origin
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $deleted_at
 * @property int $website_api_key_id
 *
 */
class WebsiteApiKeyOrigin extends BaseModel
{
    use HasFactory, SoftDeletes;

    const TABLE = 'website_api_key_origins';

    const FIELD_ID         = 'id';
    const FIELD_ORIGIN       = 'origin';
    const FIELD_WEBSITE_API_KEY_ID = 'website_api_key_id';
    const RELATION_WEBSITE_API_KEY = 'websiteApiKey';

    protected $table = self::TABLE;

    protected $fillable = [
        self::FIELD_ORIGIN
    ];

    /**
     * @return BelongsTo
     */
    public function websiteApiKey(): BelongsTo
    {
        return $this->belongsTo(WebsiteApiKey::class, self::FIELD_WEBSITE_API_KEY_ID, Website::FIELD_ID);
    }
}
