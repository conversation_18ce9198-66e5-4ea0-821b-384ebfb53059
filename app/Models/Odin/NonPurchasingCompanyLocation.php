<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use App\Models\Locations\USLocation;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/***
 * @property int $id
 * @property int $company_id
 * @property int $location_id
 * @property int $company_location_id
 * @property boolean $isSetByRadius
 *
 * @property-read Company $company
 * @property-read USLocation $location
 * @property-read CompanyLocation $companyLocation
 */
class NonPurchasingCompanyLocation extends BaseModel
{
    const string TABLE = 'non_purchasing_company_locations';

    const string FIELD_ID                  = 'id';
    const string FIELD_COMPANY_ID          = 'company_id';
    const string FIELD_COMPANY_LOCATION_ID = 'company_location_id';
    const string FIELD_LOCATION_ID         = 'location_id';
    const string FIELD_IS_SET_BY_RADIUS    = 'is_set_by_radius';

    const string RELATION_COMPANY          = 'company';
    const string RELATION_LOCATION         = 'location';
    const string RELATION_COMPANY_LOCATION = 'companyLocation';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(USLocation::class, self::FIELD_LOCATION_ID, USLocation::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function companyLocation(): BelongsTo
    {
        return $this->belongsTo(CompanyLocation::class, self::FIELD_COMPANY_LOCATION_ID, CompanyLocation::FIELD_ID);
    }
}
