<?php

namespace App\Models\Odin;

use App\Database\Casts\AsConfigurableFieldPayload;
use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $industry_service_id
 * @property string $name
 * @property string $key
 * @property int $type
 * @property string $payload
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read GlobalType $globalType
 */
class CompanyReviewTypeField extends BaseModel
{
    const TABLE = 'company_review_type_fields';

    const FIELD_ID             = 'id';
    const FIELD_GLOBAL_TYPE_ID = 'global_type_id';
    const FIELD_NAME           = 'name';
    const FIELD_KEY            = 'key';
    const FIELD_TYPE           = 'type';
    const FIELD_PAYLOAD        = 'payload';

    const RELATION_GLOBAL_TYPE = 'globalType';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_PAYLOAD => AsConfigurableFieldPayload::class
    ];

    // todo: define type values

    /**
     * @return BelongsTo
     */
    public function globalType(): BelongsTo
    {
        return $this->belongsTo(GlobalType::class, self::FIELD_GLOBAL_TYPE_ID, GlobalType::FIELD_ID);
    }
}
