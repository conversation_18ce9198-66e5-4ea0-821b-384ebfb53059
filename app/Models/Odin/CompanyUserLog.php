<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $company_user_id
 * @property string $ip_address
 * @property int $type
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read CompanyUser $user
 */
class CompanyUserLog extends BaseModel
{
    const TABLE = 'company_user_logs';

    const FIELD_ID              = 'id';
    const FIELD_COMPANY_USER_ID = 'company_user_id';
    const FIELD_IP_ADDRESS      = 'ip_address';
    const FIELD_TYPE            = 'type';

    const RELATION_USER = 'user';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    // todo: define type values
    const TYPE_LOGIN = 1;

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(CompanyUser::class, self::FIELD_COMPANY_USER_ID, CompanyUser::FIELD_ID);
    }
}
