<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use Carbon\Carbon;

/**
 * @property int $id
 * @property int $location_id
 * @property int $status
 * @property Carbon $utc_date
 * @property int $count
 * @property boolean $verified
 * @property int $service_id
 */
class AvailableLead extends BaseModel
{
    const TABLE = 'available_leads';

    const FIELD_ID          = 'id';
    const FIELD_LOCATION_ID = 'location_id';
    const FIELD_STATUS      = 'status';
    const FIELD_UTC_DATE    = 'utc_date';
    const FIELD_COUNT       = 'count';
    const FIELD_VERIFIED    = 'verified';
    const FIELD_SERVICE_ID  = 'service_id';
    const FIELD_CREATED_AT  = 'created_at';
    const FIELD_UPDATED_AT  = 'updated_at';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];
}
