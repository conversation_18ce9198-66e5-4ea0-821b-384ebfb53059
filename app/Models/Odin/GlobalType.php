<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int $id
 * @property string $name
 * @property string $key
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read IndustryType[] $industryTypes
 * @property-read CompanyReviewTypeField[] $companyReviewFields
 */
class GlobalType extends BaseModel
{
    use HasFactory;

    const TABLE = 'global_types';

    const FIELD_ID   = 'id';
    const FIELD_NAME = 'name';
    const FIELD_KEY  = 'key';

    const RELATION_INDUSTRY_TYPES        = 'industryTypes';
    const RELATION_COMPANY_REVIEW_FIELDS = 'companyReviewFields';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return HasMany
     */
    public function industryTypes(): HasMany
    {
        return $this->hasMany(IndustryType::class, IndustryType::FIELD_GLOBAL_TYPE_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function companyReviewFields(): HasMany
    {
        return $this->hasMany(CompanyReviewTypeField::class, CompanyReviewTypeField::FIELD_GLOBAL_TYPE_ID, self::FIELD_ID);
    }
}
