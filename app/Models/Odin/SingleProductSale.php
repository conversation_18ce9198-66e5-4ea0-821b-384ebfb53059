<?php

namespace App\Models\Odin;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @property int $id
 * @property float $price
 * @property int $status
 * @property int $consumer_product_id
 * @property int $company_id
 * @property int|null $product_assignment_id
 * @property int|null $invoice_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property-read ProductAssignment|null $productAssignment
 * @property-read ConsumerProduct $consumerProduct
 * @property-read Company $company
 */
class SingleProductSale extends Model
{
    use HasFactory;

    const TABLE = 'single_product_sales';

    const FIELD_ID = 'id';
    const FIELD_PRICE = 'price';
    const FIELD_STATUS = 'status';
    const FIELD_COMPANY_ID = 'company_id';
    const FIELD_INVOICE_ID = 'invoice_id'; // EloquentQuote ID
    const FIELD_CONSUMER_PRODUCT_ID = 'consumer_product_id';
    const FIELD_PRODUCT_ASSIGNMENT_ID = 'product_assignment_id';

    const FIELD_STATUS_INITIAL = 1;
    const FIELD_STATUS_PAID = 2;
    const FIELD_STATUS_FAILED = 0;

    const FIELD_CREATED_AT = 'created_at';
    const FIELD_UPDATED_AT = 'updated_at';

    const RELATION_CONSUMER_PRODUCT = 'consumerProduct';
    const RELATION_PRODUCT_ASSIGNMENT = 'productAssignment';
    const RELATION_COMPANY = 'company';

    protected $guarded = [self::FIELD_ID];

    protected $table = self::TABLE;


    /**
     * Defines relationship to the Company that purchased the product.
     *
     * @return HasOne
     */
    public function company(): HasOne
    {
        return $this->hasOne(Company::class, Company::FIELD_ID, self::FIELD_COMPANY_ID);
    }

    /**
     * Defines relationship to the Consumer Product.
     *
     * @return HasOne
     */
    public function consumerProduct(): HasOne
    {
        return $this->hasOne(ConsumerProduct::class, ConsumerProduct::FIELD_ID, self::FIELD_CONSUMER_PRODUCT_ID);
    }

    /**
     * Defines relationship to the Product Assignment.
     *
     * @return hasOne
     */
    public function productAssignment(): hasOne
    {
        return $this->hasOne(ProductAssignment::class, ProductAssignment::FIELD_ID, self::FIELD_PRODUCT_ASSIGNMENT_ID);
    }
}
