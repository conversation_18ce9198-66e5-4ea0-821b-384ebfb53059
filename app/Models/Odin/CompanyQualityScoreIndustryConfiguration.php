<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @property int $id
 * @property bool $global
 * @property int $industry_id
 * @property int $company_quality_score_rule_id
 * @property array $test_company_ids
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Industry $industry
 * @property-read CompanyQualityScoreRule $companyQualityScoreRule
 */
class CompanyQualityScoreIndustryConfiguration extends BaseModel
{
    const FIELD_ID                              = 'id';
    const FIELD_GLOBAL                          = 'global';
    const FIELD_INDUSTRY_ID                     = 'industry_id';
    const FIELD_COMPANY_QUALITY_SCORE_RULE_ID   = 'company_quality_score_rule_id';
    const FIELD_TEST_COMPANY_IDS                = 'test_company_ids';
    const FIELD_CREATED_AT                      = 'created_at';
    const FIELD_UPDATED_AT                      = 'updated_at';

    const RELATION_INDUSTRY                     = 'industry';
    const RELATION_COMPANY_QUALITY_SCORE_RULE   = 'companyQualityScoreRule';

    const TABLE = 'company_quality_score_industry_configurations';

    protected $table = self::TABLE;

    protected $casts = [
        self::FIELD_TEST_COMPANY_IDS => 'array'
    ];

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function industry(): BelongsTo
    {
        return $this->belongsTo(Industry::class, self::FIELD_INDUSTRY_ID, Industry::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function companyQualityScoreRule(): HasOne
    {
        return $this->hasOne(CompanyQualityScoreRule::class, CompanyQualityScoreRule::FIELD_ID, self::FIELD_COMPANY_QUALITY_SCORE_RULE_ID);
    }

}
