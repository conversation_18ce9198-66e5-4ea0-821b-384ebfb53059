<?php

namespace App\Models\Odin;

use App\Database\Casts\AsConfigurableFieldPayload;
use App\Models\BaseModel;
use Carbon\Carbon;

/**
 * @property int $id
 * @property string $name
 * @property string $key
 * @property int $type
 * @property string $payload
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class GlobalCompanyReviewField extends BaseModel
{
    const TABLE = 'global_company_review_fields';

    const FIELD_ID      = 'id';
    const FIELD_NAME    = 'name';
    const FIELD_KEY     = 'key';
    const FIELD_TYPE    = 'type';
    const FIELD_PAYLOAD = 'payload';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_PAYLOAD => AsConfigurableFieldPayload::class
    ];

    // todo: define type values
}
