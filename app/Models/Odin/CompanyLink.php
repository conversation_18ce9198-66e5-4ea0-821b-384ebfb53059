<?php

namespace App\Models\Odin;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

class CompanyLink extends Model
{
    use HasFactory;
    const TABLE = 'company_links';
    const FIELD_ID = 'id';
    const FIELD_COMPANY_ID_ONE = 'company_id_one';
    const FIELD_COMPANY_ID_TWO = 'company_id_two';
    const FIELD_CREATED_BY_USER = 'created_by_user';
    const FIELD_COMMENT = 'comment';
    const FIELD_CREATED_AT = 'created_at';
    const FIELD_UPDATED_AT = 'updated_at';


    public function companyOne(): HasOne
    {
        return $this->hasOne(Company::class, Company::FIELD_ID, self::FIELD_COMPANY_ID_ONE);
    }

    public function companyTwo(): HasOne
    {
        return $this->hasOne(Company::class, Company::FIELD_ID, self::FIELD_COMPANY_ID_TWO);
    }

    public function createdBy(): HasOne
    {
        return $this->hasOne(User::class, User::FIELD_ID, self::FIELD_CREATED_BY_USER);
    }
}
