<div id="table-inner" class="" style="min-width: 0px; table-layout: auto; border: 0px solid rgb(170, 170, 170);"><div class="tr" id="header-row" style="border-bottom: 1px solid rgb(0, 0, 0);"><div class="td sorting" tabindex="0" role="button" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 0px solid rgb(0, 0, 0); font-weight: bold; background-color: rgb(230, 244, 247);"><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box; color: rgb(0, 0, 0);"><p>Rank</p>
</div></div><div class="td sorting" tabindex="0" role="button" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0); font-weight: bold; background-color: rgb(230, 244, 247);"><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box; color: rgb(0, 0, 0);"><p>Company</p>
</div></div><div class="td sorting" tabindex="0" role="button" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0); font-weight: bold; background-color: rgb(230, 244, 247);"><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box; color: rgb(0, 0, 0);"><p>City</p>
</div></div><div class="td sorting" tabindex="0" role="button" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0); font-weight: bold; background-color: rgb(230, 244, 247);"><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box; color: rgb(0, 0, 0);"><p>State</p>
</div></div><div class="td sorting" tabindex="0" role="button" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0); font-weight: bold; background-color: rgb(230, 244, 247);"><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box; color: rgb(0, 0, 0);"><p>Peak Number of Employees</p>
</div></div><div class="td sorting" tabindex="0" role="button" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0); font-weight: bold; background-color: rgb(230, 244, 247);"><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box; color: rgb(0, 0, 0);"><p>Total 2024 Revenue in Dollars</p>
</div></div><div class="td sorting" tabindex="0" role="button" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0); font-weight: bold; background-color: rgb(230, 244, 247);"><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box; color: rgb(0, 0, 0);"><p>% of Commercial Work</p>
</div></div><div class="td sorting" tabindex="0" role="button" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0); font-weight: bold; background-color: rgb(230, 244, 247);"><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box; color: rgb(0, 0, 0);"><p>% of Residential Work</p>
</div></div><div class="td sorting" tabindex="0" role="button" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0); font-weight: bold; background-color: rgb(230, 244, 247);"><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box; color: rgb(0, 0, 0);"><p>Involved with Private Equity?</p>
</div></div><div class="td sorting" tabindex="0" role="button" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0); font-weight: bold; background-color: rgb(230, 244, 247);"><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box; color: rgb(0, 0, 0);"><p>Platform Name</p>
</div></div></div><div class="tr body-row" style="border-bottom: 1px solid rgb(0, 0, 0);"><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 0px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Rank</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">101</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Company</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Elite Roofing &amp; Solar</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>City</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Denver</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>State</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Colo.</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Peak Number of Employees</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">30</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Total 2024 Revenue in Dollars</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">21,069,700</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Commercial Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">15</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Residential Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">85</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Involved with Private Equity?</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>No</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Platform Name</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"></div></div></div><div class="tr body-row" style="border-bottom: 1px solid rgb(0, 0, 0);"><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 0px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Rank</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">102</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Company</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>The Third Estimate</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>City</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Solon</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>State</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Ohio</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Peak Number of Employees</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">59</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Total 2024 Revenue in Dollars</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">20,784,010</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Commercial Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">1</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Residential Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">99</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Involved with Private Equity?</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>No</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Platform Name</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"></div></div></div><div class="tr body-row" style="border-bottom: 1px solid rgb(0, 0, 0);"><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 0px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Rank</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">103</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Company</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Results Contracting</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>City</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Coon Rapids</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>State</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Minn.</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Peak Number of Employees</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">70</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Total 2024 Revenue in Dollars</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">20,424,458</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Commercial Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">1</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Residential Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">99</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Involved with Private Equity?</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>No</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Platform Name</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"></div></div></div><div class="tr body-row" style="border-bottom: 1px solid rgb(0, 0, 0);"><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 0px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Rank</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">104</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Company</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Southern Premier Roofing</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>City</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Raleigh</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>State</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>N.C.</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Peak Number of Employees</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">40</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Total 2024 Revenue in Dollars</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">20,017,040</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Commercial Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">3</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Residential Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">97</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Involved with Private Equity?</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>No</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Platform Name</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"></div></div></div><div class="tr body-row" style="border-bottom: 1px solid rgb(0, 0, 0);"><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 0px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Rank</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">105</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Company</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Arnold Roofing and Restoration</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>City</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Simpsonville</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>State</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>S.C.</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Peak Number of Employees</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">35</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Total 2024 Revenue in Dollars</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">18,018,879</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Commercial Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">85</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Residential Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">15</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Involved with Private Equity?</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>No</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Platform Name</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"></div></div></div><div class="tr body-row" style="border-bottom: 1px solid rgb(0, 0, 0);"><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 0px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Rank</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">106</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Company</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Pfeifer Roofing, Inc.</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>City</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Salem</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>State</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Ore.</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Peak Number of Employees</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">70</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Total 2024 Revenue in Dollars</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">17,758,499</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Commercial Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">27</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Residential Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">73</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Involved with Private Equity?</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>No</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Platform Name</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"></div></div></div><div class="tr body-row" style="border-bottom: 1px solid rgb(0, 0, 0);"><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 0px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Rank</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">107</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Company</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Pyramid Roofing Company</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>City</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Grandview</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>State</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Mo.</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Peak Number of Employees</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">37</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Total 2024 Revenue in Dollars</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">17,625,129</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Commercial Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">20</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Residential Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">80</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Involved with Private Equity?</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>No</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Platform Name</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"></div></div></div><div class="tr body-row" style="border-bottom: 1px solid rgb(0, 0, 0);"><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 0px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Rank</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">108</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Company</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Aegis Construction Group, Inc.</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>City</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Villa Park</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>State</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Ill.</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Peak Number of Employees</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">35</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Total 2024 Revenue in Dollars</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">17,200,000</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Commercial Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">2</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Residential Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">98</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Involved with Private Equity?</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>No</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Platform Name</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"></div></div></div><div class="tr body-row" style="border-bottom: 1px solid rgb(0, 0, 0);"><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 0px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Rank</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">109</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Company</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Ready Roofer</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>City</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Garden City</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>State</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Kan.</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Peak Number of Employees</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">36</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Total 2024 Revenue in Dollars</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">17,117,981</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Commercial Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">5</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Residential Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">95</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Involved with Private Equity?</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>No</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Platform Name</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"></div></div></div><div class="tr body-row" style="border-bottom: 1px solid rgb(0, 0, 0);"><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 0px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Rank</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">110</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Company</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Accent Roofing Service</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>City</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Lawrenceville</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>State</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Ga.</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Peak Number of Employees</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">80</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Total 2024 Revenue in Dollars</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">16,841,363</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Commercial Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">2</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Residential Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">98</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Involved with Private Equity?</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>No</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Platform Name</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"></div></div></div><div class="tr body-row" style="border-bottom: 1px solid rgb(0, 0, 0);"><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 0px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Rank</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">111</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Company</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Integrity Roofing &amp; Construction</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>City</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Poulsbo</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>State</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Wash.</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Peak Number of Employees</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">70</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Total 2024 Revenue in Dollars</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">16,393,105</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Commercial Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">15</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Residential Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">85</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Involved with Private Equity?</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Yes</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Platform Name</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Stone Grove</p>
</div></div></div><div class="tr body-row" style="border-bottom: 1px solid rgb(0, 0, 0);"><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 0px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Rank</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">112</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Company</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Mayday Restoration, LLC</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>City</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Lakeland</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>State</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Minn.</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Peak Number of Employees</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">21</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Total 2024 Revenue in Dollars</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">16,213,209</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Commercial Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">40</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Residential Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">60</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Involved with Private Equity?</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>No</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Platform Name</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"></div></div></div><div class="tr body-row" style="border-bottom: 1px solid rgb(0, 0, 0);"><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 0px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Rank</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">113</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Company</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Firehouse Roofing</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>City</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Rowlett</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>State</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Texas</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Peak Number of Employees</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">53</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Total 2024 Revenue in Dollars</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">15,829,054</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Commercial Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">1</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Residential Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">99</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Involved with Private Equity?</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>No</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Platform Name</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"></div></div></div><div class="tr body-row" style="border-bottom: 1px solid rgb(0, 0, 0);"><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 0px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Rank</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">114</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Company</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Four Seasons Kanga Roof</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>City</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Roseville</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>State</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Mich.</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Peak Number of Employees</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">72</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Total 2024 Revenue in Dollars</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">15,274,829</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Commercial Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">53</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Residential Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">47</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Involved with Private Equity?</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>No</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Platform Name</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"></div></div></div><div class="tr body-row" style="border-bottom: 1px solid rgb(0, 0, 0);"><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 0px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Rank</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">115</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Company</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Ark Roofer</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>City</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Georgetown</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>State</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Texas</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Peak Number of Employees</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">24</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Total 2024 Revenue in Dollars</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">14,499,504</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Commercial Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">10</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Residential Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">90</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Involved with Private Equity?</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>No</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Platform Name</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"></div></div></div><div class="tr body-row" style="border-bottom: 1px solid rgb(0, 0, 0);"><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 0px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Rank</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">116</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Company</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Wedge Roofing</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>City</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Petaluma</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>State</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Calif.</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Peak Number of Employees</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">58</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Total 2024 Revenue in Dollars</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">14,400,000</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Commercial Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">48</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Residential Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">52</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Involved with Private Equity?</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>No</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Platform Name</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"></div></div></div><div class="tr body-row" style="border-bottom: 1px solid rgb(0, 0, 0);"><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 0px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Rank</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">117</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Company</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Emmons Roofing &amp; Siding</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>City</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Williamstown</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>State</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>N.J.</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Peak Number of Employees</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">41</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Total 2024 Revenue in Dollars</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">14,371,427</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Commercial Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">10</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Residential Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">90</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Involved with Private Equity?</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>No</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Platform Name</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"></div></div></div><div class="tr body-row" style="border-bottom: 1px solid rgb(0, 0, 0);"><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 0px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Rank</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">118</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Company</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Smith Roofing &amp; Exteriors</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>City</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Knoxville</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>State</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Tenn.</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Peak Number of Employees</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">30</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Total 2024 Revenue in Dollars</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">13,600,000</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Commercial Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">5</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Residential Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">95</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Involved with Private Equity?</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>No</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Platform Name</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"></div></div></div><div class="tr body-row" style="border-bottom: 1px solid rgb(0, 0, 0);"><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 0px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Rank</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">119</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Company</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Journey Home Restoration</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>City</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Lorain</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>State</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Ohio</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Peak Number of Employees</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">22</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Total 2024 Revenue in Dollars</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">13,269,000</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Commercial Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">30</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Residential Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">70</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Involved with Private Equity?</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>No</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Platform Name</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"></div></div></div><div class="tr body-row" style="border-bottom: 1px solid rgb(0, 0, 0);"><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 0px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Rank</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">120</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Company</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Construction Unlimited</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>City</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Apopka</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>State</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Fla.</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Peak Number of Employees</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">25</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Total 2024 Revenue in Dollars</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">12,603,179</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Commercial Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">10</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Residential Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">90</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Involved with Private Equity?</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>No</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Platform Name</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"></div></div></div><div class="tr body-row" style="border-bottom: 1px solid rgb(0, 0, 0);"><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 0px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Rank</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">121</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Company</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Hayes Roofing, LLC</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>City</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Inman</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>State</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>S.C.</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Peak Number of Employees</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">25</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Total 2024 Revenue in Dollars</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">12,586,423</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Commercial Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">5</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Residential Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">95</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Involved with Private Equity?</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>No</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Platform Name</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"></div></div></div><div class="tr body-row" style="border-bottom: 1px solid rgb(0, 0, 0);"><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 0px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Rank</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">122</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Company</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Native Roofing &amp; Solar, LLC</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>City</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Colorado Springs</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>State</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Colo.</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Peak Number of Employees</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">21</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Total 2024 Revenue in Dollars</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">12,000,000</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Commercial Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">5</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Residential Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">95</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Involved with Private Equity?</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>No</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Platform Name</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"></div></div></div><div class="tr body-row" style="border-bottom: 1px solid rgb(0, 0, 0);"><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 0px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Rank</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">123</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Company</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Cypress Solar &amp; Roofing</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>City</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Bossier City</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>State</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>La.</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Peak Number of Employees</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">20</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Total 2024 Revenue in Dollars</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">11,328,492</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Commercial Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">42</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Residential Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">58</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Involved with Private Equity?</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>No</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Platform Name</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"></div></div></div><div class="tr body-row" style="border-bottom: 1px solid rgb(0, 0, 0);"><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 0px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Rank</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">124</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Company</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Lewis Walker Roofing, Inc.</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>City</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Lake City</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>State</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Fla.</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Peak Number of Employees</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">100</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Total 2024 Revenue in Dollars</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">11,992,585</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Commercial Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">65</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Residential Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">35</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Involved with Private Equity?</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>No</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Platform Name</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"></div></div></div><div class="tr body-row" style="border-bottom: 1px solid rgb(0, 0, 0);"><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 0px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Rank</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">125</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Company</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Phoenix Roofing &amp; Repair</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>City</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Phoenix</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>State</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>Ariz.</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Peak Number of Employees</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">30</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Total 2024 Revenue in Dollars</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">11,000,000</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Commercial Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">30</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>% of Residential Work</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-end; justify-content: center; text-align: end; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;">70</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Involved with Private Equity?</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"><p>No</p>
</div></div><div class="td" style="padding: 0rem; height: 100%; font-size: 1rem; border-left: 1px solid rgb(0, 0, 0);"><span class="cell-header" style="justify-content: flex-start; text-align: start;"><p>Platform Name</p>
</span><div class="cell-body" style="flex-direction: column; align-items: flex-start; justify-content: center; text-align: start; padding: 0.8rem 0.4rem; height: 100%; box-sizing: border-box;"></div></div></div></div>