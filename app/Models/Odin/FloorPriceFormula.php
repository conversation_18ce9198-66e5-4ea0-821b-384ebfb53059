<?php

namespace App\Models\Odin;

use App\Enums\Odin\QualityTier;
use App\Enums\Odin\SaleTypes;
use App\Models\Legacy\Location;
use App\Services\DatabaseHelperService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 *
 * @property $id
 * @property $quality_tier
 * @property $sales_type
 * @property $product
 * @property $industry
 * @property $property_type
 * @property $location_id
 * @property $multiplier_value
 * @property $multiplier_type
 * @property $multiplier_quality_tier
 * @property $multiplier_sales_type
 *
 * @property-read $location
 */
class FloorPriceFormula extends Model
{
    const TABLE                         = 'floor_price_formulas';
    const FIELD_ID                      = 'id';
    const FIELD_QUALITY_TIER            = 'quality_tier';
    const FIELD_SALES_TYPE              = 'sales_type';
    const FIELD_PRODUCT                 = 'product';
    const FIELD_INDUSTRY                = 'industry';
    const FIELD_PROPERTY_TYPE           = 'property_type';
    const FIELD_LOCATION_ID             = 'location_id';
    const FIELD_MULTIPLIER_VALUE        = 'multiplier_value';
    const FIELD_MULTIPLIER_TYPE         = 'multiplier_type';
    const FIELD_MULTIPLIER_QUALITY_TIER = 'multiplier_quality_tier';
    const FIELD_MULTIPLIER_SALES_TYPE   = 'multiplier_sales_type';

    const MULTIPLIER_TYPE_MAX_REVENUE = 'max_revenue';
    const MULTIPLIER_TYPE_RATE        = 'rate';

    const DEFAULT_MULTIPLIER_VALUE = 1.75;
    const DEFAULT_MULTIPLIER_TYPE = self::MULTIPLIER_TYPE_MAX_REVENUE;
    const DEFAULT_MULTIPLIER_QUALITY_TIER = QualityTier::STANDARD;
    const DEFAULT_MULTIPLIER_SALES_TYPE = SaleTypes::QUAD;

    const RELATION_LOCATION = 'location';

    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    /**
     * Default formula to be used for state level appointments when undefined
     *
     * @return static
     */
    public static function defaultAppointmentFormula(): self
    {
        $formula = new self();
        $formula->multiplier_value = self::DEFAULT_MULTIPLIER_VALUE;
        $formula->multiplier_type = self::DEFAULT_MULTIPLIER_TYPE;
        $formula->multiplier_quality_tier = self::DEFAULT_MULTIPLIER_QUALITY_TIER->value;
        $formula->multiplier_sales_type = self::DEFAULT_MULTIPLIER_SALES_TYPE->value;
        return $formula;
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::TABLE, self::FIELD_LOCATION_ID, Location::ID);
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_MULTIPLIER_VALUE => $this->multiplier_value,
            self::FIELD_MULTIPLIER_TYPE => $this->multiplier_type,
            self::FIELD_MULTIPLIER_SALES_TYPE => is_string($this->multiplier_sales_type) ? $this->multiplier_sales_type : $this->multiplier_sales_type?->value,
            self::FIELD_MULTIPLIER_QUALITY_TIER => is_string($this->multiplier_quality_tier) ? $this->multiplier_quality_tier : $this->multiplier_quality_tier?->value,
        ];
    }
}
