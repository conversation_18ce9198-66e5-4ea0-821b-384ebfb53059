<?php

namespace App\Models;

use App\Database\Casts\AsWorkflowPayload;
use App\Enums\RunningWorkflowStatus;
use App\Models\Legacy\EloquentQuote;
use App\Models\Odin\Company;
use App\Workflows\WorkflowPayload;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @property int $id
 * @property int $workflow_id
 * @property RunningWorkflowStatus $status
 * @property int $current_action_id
 * @property WorkflowPayload $payload
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property int|null $event_lead_id
 * @property int|null $event_company_id
 *
 * @property-read WorkflowAction|null $currentAction
 * @property-read Company|null $company
 * @property-read EloquentQuote|null $legacyLead
 */
class RunningWorkflow extends BaseModel
{
    use HasFactory;

    const TABLE = 'running_workflows';

    const FIELD_ID                = 'id';
    const FIELD_WORKFLOW_ID       = 'workflow_id';
    const FIELD_STATUS            = 'status';
    const FIELD_CURRENT_ACTION_ID = 'current_action_id';
    const FIELD_PAYLOAD           = 'payload';

    const VIRTUAL_FIELD_EVENT_LEAD_ID    = 'event_lead_id';
    const VIRTUAL_FIELD_EVENT_COMPANY_ID = 'event_company_id';

    protected $table   = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_STATUS  => RunningWorkflowStatus::class,
        self::FIELD_PAYLOAD => AsWorkflowPayload::class,
    ];

    const RELATION_LEGACY_LEAD = 'legacyLead';
    const RELATION_COMPANY     = 'company';

    /**
     * Defines the relationship to the current action in the running workflow.
     *
     * @return HasOne
     */
    public function currentAction(): HasOne
    {
        return $this->hasOne(WorkflowAction::class, WorkflowAction::FIELD_ID, self::FIELD_CURRENT_ACTION_ID);
    }

    /**
     * @return HasOne
     */
    public function legacyLead(): HasOne
    {
        return $this->hasOne(EloquentQuote::class, EloquentQuote::QUOTE_ID, self::VIRTUAL_FIELD_EVENT_LEAD_ID);
    }

    /**
     * @return HasOne
     */
    public function company(): HasOne
    {
        return $this->hasOne(Company::class, Company::FIELD_ID, self::VIRTUAL_FIELD_EVENT_COMPANY_ID);
    }
}
