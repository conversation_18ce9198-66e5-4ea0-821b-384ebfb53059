<?php

namespace App\Models\CompanyDiscovery;

use App\Models\BaseModel;

/**
 * Class USZipCodeDiscoveryStatus
 *
 * @property int $id
 * @property string $zip_code
 * @property float $latitude
 * @property float $longitude
 * @property int $radius
 * @property string $place_type
 * @property string $place_keyword
 * @property int $industry_id
 * @property bool $checked
 * @property string $discovery_reference
 */
class USZipCodeDiscoveryStatus extends BaseModel
{
    const TABLE = 'us_zip_code_discovery_statuses';

    protected $table = self::TABLE;

    const FIELD_ID                  = 'id';
    const FIELD_ZIP_CODE            = 'zip_code';
    const FIELD_LATITUDE            = 'latitude';
    const FIELD_LONGITUDE           = 'longitude';
    const FIELD_RADIUS              = 'radius';        // measured in meters https://developers.google.com/maps/documentation/places/web-service/search-nearby#radius
    const FIELD_PLACE_TYPE          = 'place_type';    // https://developers.google.com/maps/documentation/places/web-service/search-nearby#type
    const FIELD_PLACE_KEYWORD       = 'place_keyword'; // https://developers.google.com/maps/documentation/places/web-service/search-nearby#keyword
    const FIELD_INDUSTRY_ID         = 'industry_id';
    const FIELD_CHECKED             = 'checked';
    const FIELD_DISCOVERY_REFERENCE = 'discovery_reference';

    const DEFAULT_RADIUS        = 8000;
    const DEFAULT_PLACE_TYPE    = null;
    const DEFAULT_PLACE_KEYWORD = null;

    protected $guarded = [self::FIELD_ID];
}
