<?php

namespace App\Models\CompanyDiscovery;

use App\Models\BaseModel;
use App\Models\Odin\Address;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class AddressDiscoveryStatus
 *
 * @property int $id
 * @property int $address_id
 * @property bool $checked
 * @property string $discovery_reference
 *
 * @property-read Address $address
 */
class AddressDiscoveryStatus extends BaseModel
{
    const TABLE = 'address_discovery_statuses';

    protected $table = self::TABLE;

    const FIELD_ID                  = 'id';
    const FIELD_ADDRESS_ID          = 'address_id';
    const FIELD_CHECKED             = 'checked';
    const FIELD_DISCOVERY_REFERENCE = 'discovery_reference';

    const RELATION_ADDRESS = 'address';

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function address(): BelongsTo
    {
        return $this->belongsTo(Address::class);
    }
}
