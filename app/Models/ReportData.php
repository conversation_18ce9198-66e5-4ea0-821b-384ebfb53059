<?php

namespace App\Models;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Query\Expression;
use Illuminate\Support\Facades\DB;

/**
 * @property int $id
 * @property string $report
 * @property string $relation_type
 * @property string $relation_id
 * @property array $data
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class ReportData extends BaseModel
{
    use HasFactory;

    const string TABLE                  = 'report_data';

    const string FIELD_ID               = 'id';
    const string FIELD_REPORT           = 'report';
    const string FIELD_RELATION_TYPE    = 'relation_type';
    const string FIELD_RELATION_ID      = 'relation_id';
    const string FIELD_DATA             = 'data';

    const string REPORT_COUNTY_COVERAGE = 'county_coverage';

    const string RELATION_TYPE_COUNTY_LOCATION = 'county_location';

    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_DATA => 'array'
    ];

    /**
     * @param string $column
     * @return Expression
     */
    public static function jsonDataSelect(string $column): Expression
    {
        return DB::raw("JSON_UNQUOTE(JSON_EXTRACT(".self::FIELD_DATA.", '$.".$column."')) AS ".$column);
    }
}
