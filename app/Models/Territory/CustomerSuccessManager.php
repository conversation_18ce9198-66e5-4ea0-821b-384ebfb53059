<?php

namespace App\Models\Territory;

use App\Models\BaseModel;
use App\Models\Odin\Company;
use App\Models\User;
use App\Traits\HasActiveStateByDate;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Support\Collection;

/**
 * @property integer id
 * @property string name
 * @property integer user_id
 * @property integer created_by_id
 * @property Carbon active_from
 * @property Carbon active_to
 * @property Carbon created_at
 * @property Carbon updated_at
 *
 * @property-read User $user
 * @property-read User $createdBy
 * @property-read Collection<Company> $companies
 */
class CustomerSuccessManager extends BaseModel
{
    use HasFactory, HasActiveStateByDate;

    const string TABLE               = 'customer_success_managers';
    const string FIELD_ID            = 'id';
    const string FIELD_NAME          = 'name';
    const string FIELD_USER_ID       = 'user_id';
    const string FIELD_CREATED_BY_ID = 'created_by_id';
    const string FIELD_ACTIVE_FROM   = 'active_from';
    const string FIELD_ACTIVE_TO     = 'active_to';
    const string FIELD_CREATED_AT    = 'created_at';
    const string FIELD_UPDATED_AT    = 'updated_at';
    const string RELATION_USER       = 'user';
    const string RELATION_CREATED_BY = 'createdBy';
    const string RELATION_COMPANIES  = 'companies';


    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];
    protected $casts   = [
        self::FIELD_ACTIVE_TO   => 'date',
        self::FIELD_ACTIVE_FROM => 'date',
    ];

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_CREATED_BY_ID, User::FIELD_ID);
    }

    /**
     * @return HasManyThrough
     */
    public function companies(): HasManyThrough
    {
        return $this->hasManyThrough(
            Company::class,
            CustomerManagerCompany::class,
            CustomerManagerCompany::FIELD_CUSTOMER_SUCCESS_MANAGER_ID,
            Company::FIELD_ID,
            self::FIELD_ID,
            CustomerManagerCompany::FIELD_COMPANY_ID
        );
    }
}
