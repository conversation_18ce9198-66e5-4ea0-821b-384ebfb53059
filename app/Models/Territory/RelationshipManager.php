<?php

namespace App\Models\Territory;

use App\Models\BaseModel;
use App\Models\Odin\Company;
use App\Models\User;
use App\Traits\HasActiveStateByDate;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property integer id
 * @property integer user_id
 * @property Carbon active_from
 * @property Carbon active_to
 * @property Carbon created_at
 * @property Carbon updated_at
 *
 * @property-read Collection<Company> $companies
 * @property-read User $user
 */
class RelationshipManager extends BaseModel
{
    use HasFactory, HasActiveStateByDate, SoftDeletes;

    const string TABLE             = 'relationship_managers';
    const string FIELD_ID          = 'id';
    const string FIELD_USER_ID     = 'user_id';
    const string FIELD_ACTIVE_FROM = 'active_from';
    const string FIELD_ACTIVE_TO   = 'active_to';
    const string FIELD_CREATED_AT  = 'created_at';
    const string FIELD_UPDATED_AT  = 'updated_at';
    const string FIELD_DELETED_AT  = 'deleted_at';

    const string RELATION_COMPANIES = 'companies';
    const string RELATION_USER      = 'user';

    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];
    protected $casts = [
        self::FIELD_ACTIVE_TO   => 'date',
        self::FIELD_ACTIVE_FROM => 'date',
    ];

    /**
     * Returns active relationship manager or creates new one
     *
     * @param int $userId
     * @return self
     */
    public static function findOrCreate(int $userId): self
    {
        /** @var self */
        return self::query()->where(
            function ($query) {
                $query->where(self::FIELD_ACTIVE_TO, '>=', now())
                    ->orWhere(self::FIELD_ACTIVE_TO, '=', null);
            }
        )->firstOrCreate([self::FIELD_USER_ID => $userId]);
    }

    /**
     * @return HasMany
     */
    public function companies(): HasMany
    {
        return $this->hasMany(Company::class, Company::FIELD_RELATIONSHIP_MANAGER_ID, self::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }
}
