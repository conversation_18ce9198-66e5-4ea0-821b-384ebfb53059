<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class TieredAdvertisingInstance extends Model
{
    use SoftDeletes;

    const string TABLE = 'tiered_advertising_instances';

    const string FIELD_ID = 'id';
    const string FIELD_NAME = 'name';
    const string FIELD_INDUSTRY_ID = 'industry_id';
    const string FIELD_PLATFORM = 'platform';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];
}
