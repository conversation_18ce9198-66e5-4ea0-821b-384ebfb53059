<?php

namespace App\Models;

use App\Enums\TestProductStatus;
use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCampaign;
use Carbon\Carbon;
use Database\Factories\Odin\TestProductFactory;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property int $id
 * @property int $product_id
 * @property int $company_id
 * @property int|null $campaign_id
 * @property int|null $legacy_lead_campaign_id
 * @property int|null $relation_id
 * @property string|null $relation_type
 * @property string $email
 * @property string $phone
 * @property TestProductStatus $status
 * @property bool $contacted
 * @property Carbon $reveal_at
 * @property Carbon $expire_at
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property int|null $create_by_id
 * @property Carbon|null $delivered_at
 *
 * @property-read ConsumerProduct $product
 * @property-read Company $company
 * @property-read ProductCampaign $campaign
 * @property-read ProductAssignment $productAssignments
 * @property-read Collection|TestProductCommunication[] $communications
 * @property-read LeadCampaign $legacyLeadCampaign
 */
class TestProduct extends BaseModel
{
    use HasFactory;

    const TABLE = 'test_products';

    const FIELD_ID                      = 'id';
    const FIELD_PRODUCT_ID              = 'product_id';
    const FIELD_COMPANY_ID              = 'company_id';
    const FIELD_CAMPAIGN_ID             = 'campaign_id';
    const FIELD_RELATION_ID             = 'relation_id';
    const FIELD_RELATION_TYPE           = 'relation_type';
    const FIELD_LEGACY_LEAD_CAMPAIGN_ID = 'legacy_lead_campaign_id';
    const FIELD_EMAIL                   = 'email';
    const FIELD_PHONE                   = 'phone';
    const FIELD_STATUS                  = 'status';
    const FIELD_CONTACTED               = 'contacted';
    const FIELD_EXPIRE_AT               = 'expire_at';
    const FIELD_REVEAL_AT               = 'reveal_at';
    const FIELD_CREATED_AT              = 'created_at';
    const FIELD_CREATED_BY_ID           = 'created_by_id';


    const RELATION_PRODUCT              = 'product';
    const RELATION_PRODUCT_ASSIGNMENTS  = 'productAssignments';
    const RELATION_COMPANY              = 'company';
    const RELATION_PRODUCT_CAMPAIGN     = 'campaign';
    const RELATION_COMMUNICATIONS       = 'communications';
    const RELATION_LEGACY_LEAD_CAMPAIGN = 'legacyLeadCampaign';
    const RELATION_CREATED_BY           = 'createdBy';

    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_STATUS          => TestProductStatus::class,
        self::FIELD_REVEAL_AT       => 'datetime',
        self::FIELD_EXPIRE_AT       => 'datetime',
    ];

    protected static function newFactory(): TestProductFactory
    {
        return TestProductFactory::new();
    }

    /**
     * Defines the relationship to a consumer product.
     *
     * @return BelongsTo
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(ConsumerProduct::class, self::FIELD_PRODUCT_ID, ConsumerProduct::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function productAssignments(): hasMany
    {
        return $this->hasMany(ProductAssignment::class,ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, self::FIELD_PRODUCT_ID);
    }

    /**
     * Defines the relationship to a company.
     *
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }

    /**
     * Defines the relationship to a given company's campaign.
     *
     * @return BelongsTo
     */
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(ProductCampaign::class, self::FIELD_CAMPAIGN_ID, ProductCampaign::FIELD_ID);
    }

    /**
     * Defines the relationship to a given company's legacy Lead campaign
     * Note that if the TestProduct has a Product Campaign associated then can get the legacy Campaign though there
     * this relationship is for when a Lead Campaign doesn't have an associated Product Campaign
     *
     * @return BelongsTo
     */
    public function legacyLeadCampaign(): BelongsTo
    {
        return $this->belongsTo(LeadCampaign::class, self::FIELD_LEGACY_LEAD_CAMPAIGN_ID, LeadCampaign::ID);
    }

    /**
     * Defines relationship to all communications related to this test product.
     *
     * @return HasMany
     */
    public function communications(): HasMany
    {
        return $this->hasMany(TestProductCommunication::class, TestProductCommunication::FIELD_TEST_PRODUCT_ID, self::FIELD_ID);
    }

    /**
     * Defines an accessor to shortcut the ->delivered_at to the delivered_at timestamp of related product_assignment.
     *
     * @return Attribute
     */
    public function deliveredAt(): Attribute
    {
        return Attribute::make(get: fn() => $this->productAssignments()->where(ProductAssignment::FIELD_DELIVERED, '=', true)->first()?->{ProductAssignment::FIELD_DELIVERED_AT});
    }

    /**
     * @return BelongsTo
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_CREATED_BY_ID, User::FIELD_ID);
    }

    /**
     * TODO - Rename to campaign ?
     * @return MorphTo
     */
    public function relation(): MorphTo
    {
        return $this->morphTo(__FUNCTION__, self::FIELD_RELATION_TYPE, self::FIELD_RELATION_ID);
    }
}
