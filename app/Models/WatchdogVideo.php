<?php

namespace App\Models;

use App\Actions\GetWatchdogPlaybackUrl;
use App\Models\Odin\Consumer;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $watchdog_video_id
 * @property int $consumer_id
 * @property string|null $link
 * @property-read Consumer $consumer
 */
class WatchdogVideo extends BaseModel
{
    use HasFactory;

    protected $fillable = [
        'watchdog_video_id',
        'consumer_id',
        'link',
    ];

    public function consumer(): BelongsTo
    {
        return $this->belongsTo(Consumer::class);
    }

    public function getLink(): ?string
    {
        return $this->link ?? data_get(
            app(abstract: GetWatchdogPlaybackUrl::class)->handle($this->consumer->reference)->getData(true),
            'data.url'
        );
    }
}
