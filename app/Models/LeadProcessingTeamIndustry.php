<?php

namespace App\Models;

use App\Models\Odin\Industry;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @property int $id
 * @property int $lead_processing_team_id
 * @property int $industry_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read LeadProcessingTeam $leadProcessingTeam
 * @property-read Industry $industry
 */
class LeadProcessingTeamIndustry extends BaseModel
{
    const TABLE = 'lead_processing_team_industries';

    const FIELD_ID                      = 'id';
    const FIELD_LEAD_PROCESSING_TEAM_ID = 'lead_processing_team_id';
    const FIELD_INDUSTRY_ID             = 'industry_id';

    const RELATION_LEAD_PROCESSING_TEAM = 'leadProcessingTeam';
    const RELATION_INDUSTRY             = 'industry';

    protected $table   = self::TABLE;
    protected $fillable = [self::FIELD_LEAD_PROCESSING_TEAM_ID, self::FIELD_INDUSTRY_ID];

    /**
     * Defines the relationship to the lead processing team.
     *
     * @return BelongsTo
     */
    public function leadProcessingTeam(): BelongsTo
    {
        return $this->belongsTo(LeadProcessingTeam::class, self::FIELD_LEAD_PROCESSING_TEAM_ID, LeadProcessingTeam::FIELD_ID);
    }

    /**
     * Defines the relation to the industry.
     *
     * @return BelongsTo
     */
    public function industry(): BelongsTo
    {
        return $this->belongsTo(Industry::class, self::FIELD_INDUSTRY_ID, Industry::FIELD_ID);
    }
}
