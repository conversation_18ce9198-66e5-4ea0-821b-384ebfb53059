<?php

namespace App\Models;

use App\Enums\Emails\DomainStatus;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property string $domain
 * @property DomainStatus $status
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Collection<MarketingCampaignConsumer> $marketingConsumers
 */
class MarketingDomain extends BaseModel
{
    const string TABLE = 'marketing_domains';

    const string FIELD_ID     = 'id';
    const string FIELD_DOMAIN = 'domain';
    const string FIELD_STATUS = 'status';
    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';

    const string RELATION_MARKETING_CONSUMERS = 'marketingConsumers';

    protected $guarded = [
        self::FIELD_ID,
    ];

    protected $casts = [
        self::FIELD_STATUS => DomainStatus::class,
    ];

    public function marketingConsumers(): HasMany
    {
        return $this->hasMany(MarketingCampaignConsumer::class, MarketingCampaignConsumer::FIELD_SENT_FROM_DOMAIN_ID, self::FIELD_ID);
    }
}
