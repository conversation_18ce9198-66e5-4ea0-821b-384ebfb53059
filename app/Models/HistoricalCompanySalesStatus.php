<?php

namespace App\Models;

use App\Database\Casts\AsHistoricalCompanySalesStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class HistoricalCompanySalesStatus extends BaseModel
{
    use HasFactory;

    const TABLE = 'historical_company_sales_status';

    const FIELD_ID = 'id';
    const FIELD_COMPANY_ID = 'company_id';
    const FIELD_YEAR = 'year';
    const FIELD_SALES_STATUS = 'sales_status';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $casts = [
        self::FIELD_SALES_STATUS => AsHistoricalCompanySalesStatus::class
    ];
}
