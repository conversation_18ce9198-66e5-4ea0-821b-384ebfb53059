<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property string $name
 * @property int $company_id
 */
class CompanyOptInName extends Model
{
    use HasFactory;
    const string TABLE = 'company_opt_in_names';

    const string FIELD_ID         = 'id';
    const string FIELD_NAME       = 'name';
    const string FIELD_COMPANY_ID = 'company_id';

    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];
}
