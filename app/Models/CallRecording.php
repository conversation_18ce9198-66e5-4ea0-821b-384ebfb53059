<?php

namespace App\Models;


use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $callId
 * @property int $callDurationSeconds
 * @property string $recordingLink
 * @property string $sId
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @property-read Call $call
 */
class CallRecording extends Model
{
    use HasFactory;

    const TABLE = 'call_recordings';

    const FIELD_ID               = 'id';
    const FIELD_CALL_ID          = 'call_id';
    const FIELD_DURATION_SECONDS = 'duration_seconds';
    const FIELD_RECORDING_LINK   = 'recording_link';
    const FIELD_SID              = 'sid';
    const FIELD_CREATED_AT       = 'created_at';
    const FIELD_UPDATED_AT       = 'updated_at';

    const RELATION_CALL          = 'call';

    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function call(): BelongsTo
    {
        return $this->belongsTo(Call::class, self::FIELD_CALL_ID, Call::FIELD_ID);
    }

}
