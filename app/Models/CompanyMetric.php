<?php

namespace App\Models;

use App\Models\BaseModel;
use App\Models\Odin\Company;
use Carbon\Carbon;
use Database\Factories\CompanyMetric\CompanyMetricFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @property int $id
 * @property int $company_id
 * @property string $source
 * @property string $request_type
 * @property string $request_url
 * @property array $request_response
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class CompanyMetric extends BaseModel
{
    use HasFactory;

    const TABLE = 'company_metrics';

    const FIELD_ID                  = 'id';
    const FIELD_COMPANY_ID          = 'company_id';
    const FIELD_SOURCE              = 'source';
    const FIELD_REQUEST_TYPE        = 'request_type';
    const FIELD_REQUEST_URL         = 'request_url';
    const FIELD_REQUEST_RESPONSE    = 'request_response';
    const FIELD_CREATED_AT          = 'created_at';
    const FIELD_UPDATED_AT          = 'updated_at';
    const RELATION_COMPANY          = 'company';

    const string REQUEST_TYPE_PERMIT_METRICS = 'permit-metrics';

    protected $casts = [
        self::FIELD_REQUEST_RESPONSE => 'array'
    ];
    protected $fillable = [
        self::FIELD_COMPANY_ID,
        self::FIELD_SOURCE,
        self::FIELD_REQUEST_TYPE,
        self::FIELD_REQUEST_URL,
        self::FIELD_REQUEST_RESPONSE
    ];

    /**
     * @return Factory
     */
    protected static function newFactory(): Factory
    {
        return CompanyMetricFactory::new();
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID);
    }

}
