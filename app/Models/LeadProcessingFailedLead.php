<?php

namespace App\Models;

use App\Models\Legacy\EloquentQuote;
use App\Models\Odin\ConsumerProduct;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property string $reason
 * @property array $errors
 * @property int $lead_processor_id
 * @property int $consumer_product_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read ConsumerProduct $consumerProduct
 * @property-read LeadProcessor $processor
 */
class LeadProcessingFailedLead extends BaseModel
{
    const string TABLE = 'lead_processing_failed_leads';

    const string FIELD_ID                = 'id';
    const string FIELD_CONSUMER_PRODUCT_ID = 'consumer_product_id';
    const string FIELD_REASON            = 'reason';
    const string FIELD_ERRORS            = 'errors';
    const string FIELD_LEAD_PROCESSOR_ID = 'lead_processor_id';
    /** @deprecated  */
    const string FIELD_LEAD_ID           = 'lead_id';

    const string RELATION_LEAD           = 'lead';
    const string RELATION_LEAD_PROCESSOR = 'processor';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts
        = [
            self::FIELD_ERRORS => 'array'
        ];

    /**
     * @deprecated
     */
    public function lead(): BelongsTo
    {
        return $this->belongsTo(EloquentQuote::class, self::FIELD_LEAD_ID, EloquentQuote::ID);
    }

    /**
     * @return BelongsTo
     */
    public function consumerProduct(): BelongsTo
    {
        return $this->belongsTo(ConsumerProduct::class, self::FIELD_CONSUMER_PRODUCT_ID, ConsumerProduct::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function processor(): BelongsTo
    {
        return $this->belongsTo(LeadProcessor::class, self::FIELD_LEAD_PROCESSOR_ID, LeadProcessor::FIELD_ID);
    }
}
