<?php

namespace App\Models;

use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $industry_service_id
 * @property int $exclusive_margin
 * @property int $duo_margin
 * @property int $trio_margin
 * @property int $quad_margin
 *
 * @property-read IndustryService $industryService
 */
class PricingMargin extends Model
{
    use HasFactory;

    const string TABLE = 'pricing_margins';

    const string FIELD_ID = 'id';
    const string FIELD_INDUSTRY_SERVICE_ID = 'industry_service_id';
    const string FIELD_EXCLUSIVE_MARGIN = 'exclusive_margin';
    const string FIELD_DUO_MARGIN = 'duo_margin';
    const string FIELD_TRIO_MARGIN = 'trio_margin';
    const string FIELD_QUAD_MARGIN = 'quad_margin';

    const string RELATION_INDUSTRY = 'industry';
    const string RELATION_INDUSTRY_SERVICE = 'industryService';

    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function industryService(): BelongsTo
    {
        return $this->belongsTo(IndustryService::class);
    }
}
