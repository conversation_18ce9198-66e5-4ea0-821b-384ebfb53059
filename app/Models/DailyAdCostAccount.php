<?php

namespace App\Models;

use App\Enums\Advertising\Advertiser;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Models\Legacy\Location;

/**
 * Class DailyAdCostAccount
 *
 * @property int $id
 * @property string $name
 * @property AdvertisingPlatform $platform
 * @property Advertiser $advertiser
 * @property string $platform_account_id
 * @property int $industry_id
 * @property int $industry_service_id
 * @property array $data
 *
 * @property-read Location $location
 * @property-read AdvertisingAccount $advertisingAccount
 */
class DailyAdCostAccount extends BaseModel
{
    const string TABLE = 'daily_ad_cost_accounts';

    const string FIELD_ID = 'id';
    const string FIELD_NAME = 'name';
    const string FIELD_PLATFORM = 'platform_slug';
    const string FIELD_ADVERTISER = 'advertiser';
    const string FIELD_PLATFORM_ACCOUNT_ID = 'platform_account_id';
    const string FIELD_INDUSTRY_ID = 'industry_id';
    const string FIELD_INDUSTRY_SERVICE_ID = 'industry_service_id';
    const string FIELD_EXCLUDE = 'exclude';
    const string FIELD_DATA = 'data';

    protected $casts = [
        self::FIELD_PLATFORM => AdvertisingPlatform::class,
        self::FIELD_ADVERTISER => Advertiser::class,
        self::FIELD_DATA => 'array',
    ];

    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];

}
