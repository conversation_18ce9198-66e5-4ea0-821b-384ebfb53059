<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;


/**
 * @property int $id
 * @property string $name
 * @property boolean $generic
 * @property int $workflow_event_id
 * @property int $entry_action_id
 *
 * @property-read WorkflowAction|null $entry_action
 * @property-read Collection<WorkflowAction> $actions
 */
class Workflow extends Model
{
    use HasFactory;

    const TABLE = 'workflows';

    const FIELD_ID                = 'id';
    const FIELD_NAME              = 'name';
    const FIELD_GENERIC           = 'generic';
    const FIELD_ENTRY_ACTION_ID   = 'entry_action_id';
    const FIELD_WORKFLOW_EVENT_ID = 'workflow_event_id';

    const RELATION_ACTIONS          = 'actions';
    const RELATION_WORKFLOW_EVENT   = 'workflowEvent';

    protected $table    = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [self::FIELD_GENERIC => 'boolean'];

    /**
     * Get the associated actions for this workflow.
     *
     * @return HasMany
     */
    public function actions(): HasMany
    {
        return $this->hasMany(WorkflowAction::class, WorkflowAction::FIELD_WORKFLOW_ID, self:: FIELD_ID);
    }

    /**
     * Get the entry action for this workflow.
     *
     * @return Attribute
     */
    public function entryAction(): Attribute
    {
        return Attribute::make(
            get: fn($value, $attributes) => $this->actions()->where(WorkflowAction::FIELD_ID, $this->{self::FIELD_ENTRY_ACTION_ID})->first()
        );
    }

    /**
     * @return BelongsTo
     */
    public function workflowEvent(): BelongsTo
    {
        return $this->belongsTo(WorkflowEvent::class, self::FIELD_WORKFLOW_EVENT_ID, WorkflowEvent::FIELD_ID);
    }
}
