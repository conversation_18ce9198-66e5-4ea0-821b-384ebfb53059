<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class SuccessManager
 *
 * @package App\Models
 *
 * @property integer $id
 * @property integer $user_id
 * @property integer $type
 * @property bool    $include_in_sales_round_robin
 * @property Carbon  $created_at
 * @property Carbon  $updated_at
 * @property Carbon  $deleted_at
 *
 * @property-read User $user
 * @property-read SuccessManagerClient[]|Collection $clients
 */
class SuccessManager extends Model
{
    use SoftDeletes, HasFactory;

    const TABLE = 'success_managers';

    const FIELD_ID                           = 'id';
    const FIELD_USER_ID                      = 'user_id';
    const FIELD_TYPE                         = 'type';
    const FIELD_INCLUDE_IN_SALES_ROUND_ROBIN = 'include_in_sales_round_robin';
    const FIELD_CREATED_AT                   = 'created_at';
    const FIELD_UPDATED_AT                   = 'updated_at';
    const FIELD_DELETED_AT                   = 'deleted_at';

    const RELATION_USER    = 'user';
    const RELATION_CLIENTS = 'clients';

    const TYPE_JUNIOR = 0;
    const TYPE_SENIOR = 1;

    const TYPES = [
        self::TYPE_JUNIOR,
        self::TYPE_SENIOR
    ];

    const TYPE_NAMES = [
        self::TYPE_JUNIOR => "Junior",
        self::TYPE_SENIOR => "Senior"
    ];

    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function clients(): HasMany
    {
        return $this->hasMany(SuccessManagerClient::class, SuccessManagerClient::FIELD_SUCCESS_MANAGER_ID, self::FIELD_ID);
    }
}
