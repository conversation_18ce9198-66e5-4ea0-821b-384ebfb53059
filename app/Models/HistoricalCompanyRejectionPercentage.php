<?php

namespace App\Models;

use App\Database\Casts\AsHistoricalCompanyRejectionPercentagesPayload;
use App\Models\Odin\Product;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $company_id
 * @property int $year
 * @property int $product_id
 * @property array $rejection_percentage
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Product $product
 */
class HistoricalCompanyRejectionPercentage extends BaseModel
{
    use HasFactory;

    const TABLE = 'historical_company_rejection_percentages';

    const FIELD_ID                   = 'id';
    const FIELD_COMPANY_ID           = 'company_id';
    const FIELD_YEAR                 = 'year';
    const FIELD_PRODUCT_ID           = 'product_id';
    const FIELD_REJECTION_PERCENTAGES = 'rejection_percentages';
    const FIELD_CREATED_AT           = 'created_at';
    const FIELD_UPDATED_AT           = 'updated_at';

    const RELATION_PRODUCT = 'product';

    protected $guarded = [self::FIELD_ID];
    protected $table   = self::TABLE;

    protected $casts = [
        self::FIELD_REJECTION_PERCENTAGES => AsHistoricalCompanyRejectionPercentagesPayload::class
    ];

    /**
     * @return BelongsTo
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, self::FIELD_PRODUCT_ID, Product::FIELD_ID);
    }
}
