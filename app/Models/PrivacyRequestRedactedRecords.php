<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property int $id
 * @property string $model_type
 * @property int $model_id
 * @property string $website
 * @property int $initiator_id
 * @property bool $success
 * @property int $privacy_request_id
 *
 * @property-read PrivacyRequest $privacyRequest
 * @property-read User $initiator
 */
class PrivacyRequestRedactedRecords extends BaseModel
{
    const string TABLE = 'privacy_request_redacted_records';

    const string FIELD_ID = 'id';

    const string FIELD_MODEL_TYPE         = 'model_type';
    const string FIELD_MODEL_ID           = 'model_id';
    const string FIELD_WEBSITE            = 'website';
    const string FIELD_INITIATOR_ID       = 'initiator_id';
    const string FIELD_STATUS             = 'status';
    const string FIELD_PRIVACY_REQUEST_ID = 'privacy_request_id';
    const string RELATION_PRIVACY_REQUEST = 'privacyRequest';

    protected $guarded = [self::FIELD_ID];

    public function initiator(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_INITIATOR_ID, User::FIELD_ID);
    }

    public function model(): MorphTo
    {
        return $this->morphTo();
    }

    public function privacyRequest(): BelongsTo
    {
        return $this->belongsTo(PrivacyRequest::class, self::FIELD_PRIVACY_REQUEST_ID, PrivacyRequest::FIELD_ID);
    }
}