<?php

namespace App\Models\ContractorProfile;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $company_id
 * @property string|null $introduction
 * @property string|null $customers_like
 * @property string|null $customers_dislike
 * @property float|null $rating
 * @property bool $status // whether the profile should be displayed
 * @property bool $force_display // this will display the profile even if qualifying criteria is not met (e.g. status === Suspended)
 * @property string $business_hours_timezone
 * @property array $business_hours
 * @property array $brands_sold
 * @property array $licenses
 * @property array $certifications
 * @property array $services
 */
class ContractorProfile extends Model
{
    use HasFactory;

    const string TABLE = 'contractor_profiles';

    const string FIELD_ID                      = 'id';
    const string FIELD_COMPANY_ID              = 'company_id';
    const string FIELD_INTRODUCTION            = 'introduction';
    const string FIELD_CUSTOMERS_LIKE          = 'customers_like';
    const string FIELD_CUSTOMERS_DISLIKE       = 'customers_dislike';
    const string FIELD_RATING                  = 'rating';
    const string FIELD_STATUS                  = 'status';
    const string FIELD_FORCE_DISPLAY           = 'force_display';
    const string FIELD_BUSINESS_HOURS_TIMEZONE = 'business_hours_timezone';
    const string FIELD_BUSINESS_HOURS          = 'business_hours';
    const string FIELD_BRANDS_SOLD             = 'brands_sold';
    const string FIELD_LICENSES                = 'licenses';
    const string FIELD_CERTIFICATIONS          = 'certifications';
    const string FIELD_SERVICES                = 'services';

    protected $table   = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts   = [
        self::FIELD_BUSINESS_HOURS => 'array',
        self::FIELD_BRANDS_SOLD    => 'array',
        self::FIELD_LICENSES       => 'array',
        self::FIELD_CERTIFICATIONS => 'array',
        self::FIELD_SERVICES       => 'array',
    ];

}
