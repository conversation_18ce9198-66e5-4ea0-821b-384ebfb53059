<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

class UserCallForwarding extends Model
{

    /**
     * @property int $id
     * @property Carbon $created_at
     * @property Carbon $updated_at
     * @property int $user_id
     * @property boolean $status
     * @property string $forward_to_number
     *
     * @property-read User $user
     */

    const TABLE = 'user_call_forwarding';

    const FIELD_ID                = 'id';
    const FIELD_CREATED_AT        = 'created_at';
    const FIELD_UPDATED_AT        = 'updated_at';
    const FIELD_USER_ID           = 'user_id';
    const FIELD_STATUS            = 'status';
    const FIELD_FORWARD_TO_NUMBER = 'forward_to_number';

    const RELATION_USER  = 'user';

    const STATUS_ENABLED  = 1;
    const STATUS_DISABLED = 0;

    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID);
    }

}
