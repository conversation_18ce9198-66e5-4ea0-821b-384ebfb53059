<?php

namespace App\Models\Affiliates;

use App\Models\Odin\ConsumerProduct;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property int $cent_value
 * @property int $payout_strategy_id
 * @property int $affiliate_id
 * @property int $consumer_product_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Affiliate $affiliate
 * @property-read PayoutStrategy $strategy
 * @property-read ConsumerProduct $consumerProduct
 */
class Payout extends Model
{
    use HasFactory;

    const string TABLE = 'affiliate_payouts';

    const string FIELD_ID = 'id';
    const string FIELD_PAYOUT_STRATEGY_ID = 'payout_strategy_id';
    const string FIELD_AFFILIATE_ID = 'affiliate_id';
    const string FIELD_CONSUMER_PRODUCT_ID = 'consumer_product_id';
    const string FIELD_CENT_VALUE = 'cent_value';
    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';

    const string RELATION_AFFILIATE = 'affiliate';
    const string RELATION_CONSUMER_PRODUCT = 'consumerProduct';
    const string RELATION_STRATEGY = 'strategy';

    protected $guarded = [self::FIELD_ID];
    protected $table = self::TABLE;

    /**
     * @return BelongsTo
     */
    public function affiliate(): BelongsTo
    {
        return $this->belongsTo(Affiliate::class);
    }

    /**
     * @return BelongsTo
     */
    public function consumerProduct(): BelongsTo
    {
        return $this->belongsTo(ConsumerProduct::class);
    }

    /**
     * @return BelongsTo
     */
    public function strategy(): BelongsTo
    {
        return $this->belongsTo(PayoutStrategy::class, self::FIELD_PAYOUT_STRATEGY_ID, PayoutStrategy::FIELD_ID);
    }
}
