<?php

namespace App\Models\Affiliates;

use App\Models\Odin\ConsumerProductAffiliateRecord;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property int $affiliate_id
 * @property string $name
 * @property int $category_id
 * @property bool $status
 * @property null|int $account_id
 * @property null|int $campaign_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $deleted_at
 *
 * @property-read Affiliate $affiliate
 * @property-read Category $category
 * @property-read ConsumerProductAffiliateRecord $consumerProductAffiliateRecord
 */
class Campaign extends Model
{
    use HasFactory, SoftDeletes;

    const string TABLE = 'affiliate_campaigns';

    const string FIELD_ID = 'id';
    const string FIELD_AFFILIATE_ID = 'affiliate_id';
    const string FIELD_NAME = 'name';
    const string FIELD_CATEGORY_ID = 'category_id';
    const string FIELD_STATUS = 'status';
    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';
    const string FIELD_DELETED_AT = 'deleted_at';
    const string FIELD_ACCOUNT_ID = 'account_id';
    const string FIELD_CAMPAIGN_ID = 'campaign_id';

    const string RELATION_AFFILIATE = 'affiliate';
    const string RELATION_CATEGORY = 'category';
    const string RELATION_CONSUMER_PRODUCT_AFFILIATE_RECORD = 'consumerProductAffiliateRecord';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];

    /**
     * @return BelongsTo
     */
    public function affiliate(): BelongsTo
    {
        return $this->belongsTo(Affiliate::class, self::FIELD_AFFILIATE_ID, Affiliate::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class,  self::FIELD_CATEGORY_ID, Category::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function consumerProductAffiliateRecord(): HasMany
    {
        return $this->hasMany(ConsumerProductAffiliateRecord::class, ConsumerProductAffiliateRecord::FIELD_CAMPAIGN_ID, self::FIELD_ID);
    }
}
