<?php

namespace App\Models\Affiliates;

use App\Models\Advertiser as BaseAdvertiser;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Advertiser extends Model
{
    use HasFactory;

    const TABLE = 'affiliate_advertisers';

    const FIELD_ID = 'id';
    const FIELD_AFFILIATE_ID = 'affiliate_id';
    const FIELD_ADVERTISER_ID = 'advertiser_id';

    protected $table = self::TABLE;
    protected $guarded = [
        self::FIELD_ID
    ];

    /**
     * @return BelongsTo
     */
    public function affiliate(): BelongsTo
    {
        return $this->belongsTo(Affiliate::class, Affiliate::FIELD_ID, self::FIELD_AFFILIATE_ID);
    }

    /**
     * @return BelongsTo
     */
    public function advertiser(): BelongsTo
    {
        return $this->belongsTo(BaseAdvertiser::class, BaseAdvertiser::FIELD_ID, self::FIELD_ADVERTISER_ID);
    }
}
