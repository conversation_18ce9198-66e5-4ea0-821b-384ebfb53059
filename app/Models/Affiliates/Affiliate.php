<?php

namespace App\Models\Affiliates;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property string $uuid
 * @property string $name
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $deleted_at
 *
 * @property-read Campaign $campaign
 * @property-read Category $category
 * @property-read PayoutStrategy $strategy
 */
class Affiliate extends Model
{
    use HasFactory, SoftDeletes;

    const string TABLE = 'affiliates';

    const string FIELD_ID = 'id';
    const string FIELD_UUID = 'uuid';
    const string FIELD_NAME = 'name';
    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';
    const string FIELD_DELETED_AT = 'deleted_at';

    const string RELATION_CAMPAIGNS = 'campaigns';
    const string RELATION_CATEGORIES = 'categories';
    const string RELATION_STRATEGIES = 'strategies';
    const string RELATION_STRATEGY = 'strategy';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];

    /**
     * @return HasMany
     */
    public function campaigns(): HasMany
    {
        return $this->hasMany(Campaign::class, Campaign::FIELD_AFFILIATE_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function categories(): HasMany
    {
        return $this->hasMany(Category::class, Category::FIELD_AFFILIATE_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function strategies(): HasMany
    {
        return $this->hasMany(PayoutStrategy::class, PayoutStrategy::FIELD_AFFILIATE_ID, self::FIELD_ID);
    }

    public function strategy(): HasOne
    {
        return $this->strategies()
            ->one()
            ->ofMany([],function (Builder $query) {
                $query->where(PayoutStrategy::FIELD_ACTIVE_FROM, '<=', Carbon::now())
                    ->where(function ($query) {
                        $query->whereNull(PayoutStrategy::FIELD_ACTIVE_TO)
                            ->orWhere(PayoutStrategy::FIELD_ACTIVE_TO, '>', now());
                });
        });
    }
}
