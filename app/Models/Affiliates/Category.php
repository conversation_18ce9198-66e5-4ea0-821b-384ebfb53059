<?php

namespace App\Models\Affiliates;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Category extends Model
{
    use HasFactory, SoftDeletes;

    const TABLE = 'affiliate_categories';

    const FIELD_ID = 'id';
    const FIELD_AFFILIATE_ID = 'affiliate_id';
    const FIELD_NAME = 'name';
    const FIELD_DELETED_AT = 'deleted_at';

    protected $table = self::TABLE;
    protected $guarded = [
        self::FIELD_ID
    ];

    /**
     * @return HasMany
     */
    public function campaigns(): HasMany
    {
        return $this->hasMany(Campaign::class, Campaign::FIELD_CATEGORY_ID, self::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function affiliate(): BelongsTo
    {
        return $this->belongsTo(Affiliate::class, Affiliate::FIELD_ID, self::FIELD_AFFILIATE_ID);
    }
}
