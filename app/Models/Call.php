<?php

namespace App\Models;

use App\Enums\CommunicationRelationTypes;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentQuote;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Schema\Builder;

/**
 * App\Models\Call
 * @property int                      $id
 * @property string                   $external_reference
 * @property string                   $external_type
 * @property int                      $phone_id
 * @property string                   $other_number
 * @property string                   $direction
 * @property string                   $result
 * @property string                   $relation_type
 * @property int                      $relation_id
 * @property Carbon                   $call_start
 * @property Carbon                   $call_end
 * @property Carbon                   $created_at
 * @property Carbon                   $updated_at
 * @property-read Phone               $phone
 * @property-read EloquentQuote       $lead
 * @property string|null $formatted_other_number
 * @property-read \App\Models\ActivityFeed|null $activity
 * @property-read \App\Models\CallRecording|null $callRecording
 * @property-read EloquentCompany|null $company
 * @property-read int|null $duration_in_minutes
 * @property-read Consumer $consumer
 * @property-read ConsumerProduct $consumerProduct
 * @property-read CompanyUser $companyUser
 * @mixin Builder
 */
class Call extends BaseModel
{
    use hasFactory;

    const TABLE = 'calls';

    const FIELD_ID                     = 'id';
    const FIELD_EXTERNAL_REFERENCE     = 'external_reference';
    const FIELD_EXTERNAL_TYPE          = 'external_type';
    const FIELD_PHONE_ID               = 'phone_id';
    const FIELD_OTHER_NUMBER           = 'other_number';
    const FIELD_FORMATTED_OTHER_NUMBER = 'formatted_other_number';
    const FIELD_DIRECTION              = 'direction';
    const FIELD_RESULT                 = 'result';
    const FIELD_CALL_START             = 'call_start';
    const FIELD_CALL_END               = 'call_end';
    const FIELD_RELATION_TYPE          = 'relation_type';
    const FIELD_RELATION_ID            = 'relation_id';
    const FIELD_NOTE                   = 'note';
    const FIELD_CREATED_AT             = 'created_at';


    const DIRECTION_OUTBOUND = 'outbound';
    const DIRECTION_INBOUND  = 'inbound';

    const RESULT_ANSWERED  = 'answered';
    const RESULT_BUSY      = 'busy';
    const RESULT_VOICEMAIL = 'voicemail';
    const RESULT_MISSED    = 'missed';
    const RESULT_INITIAL   = 'initial';

    const EXTERNAL_TYPE_TWILIO = 'twilio';

    const EXTERNAL_TYPES = [
        self::EXTERNAL_TYPE_TWILIO
    ];

    const RESULTS = [
        self::RESULT_INITIAL,
        self::RESULT_BUSY,
        self::RESULT_ANSWERED,
        self::RESULT_VOICEMAIL,
        self::RESULT_MISSED
    ];

    const RELATION_PHONE            = 'phone';
    const RELATION_LEAD             = 'lead';
    const RELATION_COMPANY          = 'company';
    const RELATION_CONSUMER         = 'consumer';
    const RELATION_COMPANY_USER     = 'companyUser';
    const RELATION_COMPANY_LOCATION = 'companyLocation';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_CALL_START => 'datetime',
        self::FIELD_CALL_END   => 'datetime'
    ];

    /**
     * @return BelongsTo
     */
    public function phone(): BelongsTo
    {
        return $this->belongsTo(Phone::class, self::FIELD_PHONE_ID, Phone::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function lead(): BelongsTo
    {
        return $this->belongsTo(EloquentQuote::class, self::FIELD_RELATION_ID, EloquentQuote::ID)
            ->where(self::FIELD_RELATION_TYPE, self::RELATION_LEAD);
    }

    /**
     * @return BelongsTo
     */
    public function consumer(): BelongsTo
    {
        return $this->belongsTo(Consumer::class, self::FIELD_RELATION_ID, Consumer::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function consumerProduct(): BelongsTo
    {
        return $this->belongsTo(ConsumerProduct::class, self::FIELD_RELATION_ID, ConsumerProduct::FIELD_ID);

    }

    /**
     * @return Consumer|null
     */
    public function getConsumer(): ?Consumer
    {
        return $this->relation_type === CommunicationRelationTypes::CONSUMER_PRODUCT->value
            ? $this->consumerProduct?->consumer
            : $this->consumer;
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_RELATION_ID, Company::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function companyUser(): BelongsTo
    {
        return $this->belongsTo(CompanyUser::class, self::FIELD_RELATION_ID, CompanyUser::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function callRecording(): HasOne
    {
        return $this->hasOne(CallRecording::class, CallRecording::FIELD_CALL_ID, self::FIELD_ID);
    }

    /**
     * @return MorphOne
     */
    public function activity(): MorphOne
    {
        return $this->morphOne(ActivityFeed::class, 'item');
    }

    public function durationInMinutes(): Attribute
    {
        $callEnd   = $this->call_end;
        $callStart = $this->call_start;

        $result = null;

        if ($callStart instanceof Carbon && $callEnd instanceof Carbon) {
            $result = $callEnd->diffInMinutes($callStart, true);
        }

        return Attribute::make(
            get: fn() => $result
        );
    }

    /**
     * @return MorphOne
     */
    public function consumerProcessingActivity(): MorphOne
    {
        return $this->morphOne(ConsumerProcessingActivity::class, ConsumerProcessingActivity::RELATION_ACTIVITY);
    }
}
