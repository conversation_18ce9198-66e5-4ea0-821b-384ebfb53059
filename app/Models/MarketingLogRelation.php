<?php

namespace App\Models;

use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\MarketingLogType;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property int $id
 * @property string $message
 * @property MarketingLogType $namespace
 * @property LogLevel $level
 * @property string $stack_trace
 * @property array $context
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read MarketingCampaignConsumer|MarketingCampaign $relation
 */
class MarketingLogRelation extends BaseModel
{
    use HasFactory;

    const string TABLE = 'marketing_log_relations';

    const string FIELD_ID               = 'id';
    const string FIELD_MARKETING_LOG_ID = 'marketing_log_id';
    const string FIELD_RELATION_TYPE    = 'relation_type';
    const string FIELD_RELATION_ID      = 'relation_id';
    const string FIELD_CREATED_AT       = 'created_at';
    const string FIELD_UPDATED_AT       = 'updated_at';

    const string RELATION_RELATION = 'relation';

    protected $guarded = [self::FIELD_ID];


    public function log(): BelongsTo
    {
        return $this->belongsTo(MarketingLog::class, self::FIELD_MARKETING_LOG_ID, MarketingLog::FIELD_ID);
    }

    public function relation(): MorphTo
    {
        return $this->morphTo();
    }
}
