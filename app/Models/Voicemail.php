<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $user_id
 * @property string $from_number
 * @property string|null $from_name
 * @property string|null $from_type
 * @property int|null $from_id
 * @property string $voicemail_link
 * @property string|null $call_sid
 * @property bool $read
 * @property float $duration
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read User $user
 */
class Voicemail extends Model
{
    use HasFactory;

    const FIELD_ID             = 'id';
    const FIELD_USER_ID        = 'user_id';
    const FIELD_FROM_NUMBER    = 'from_number';
    const FIELD_FROM_NAME      = 'from_name';
    const FIELD_FROM_TYPE      = 'from_type';
    const FIELD_FROM_ID        = 'from_id';
    const FIELD_VOICEMAIL_LINK = 'voicemail_link';
    const FIELD_CALL_SID       = 'call_sid';
    const FIELD_READ           = 'read';
    const FIELD_DURATION       = 'duration';

    const TABLE = 'voicemails';

    const RELATION_USER = 'user';

    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];
    protected $casts   = [
        self::FIELD_READ => 'boolean'
    ];

    /**
     * Defines relationship to user.
     *
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }
}
