<?php

namespace App\Models;

use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\LeadCampaign;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $lead_id
 * @property int $company_id
 * @property int $campaign_id
 * @property int $clicks
 * @property int actions_taken
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read EloquentQuote $lead
 * @property-read EloquentCompany $company
 * @property-read LeadCampaign $campaign
 */
class SalesBaitLead extends BaseModel
{
    use HasFactory;

    const TABLE = 'sales_bait_leads';

    const FIELD_ID            = 'id';
    const FIELD_LEAD_ID       = 'lead_id';
    const FIELD_COMPANY_ID    = 'company_id';
    const FIELD_CAMPAIGN_ID   = 'campaign_id';
    const FIELD_CLICKS        = 'clicks';
    const FIELD_ACTIONS_TAKEN = 'actions_taken';
    const FIELD_CREATED_AT    = 'created_at';

    const RELATION_LEAD     = 'lead';
    const RELATION_COMPANY  = 'company';
    const RELATION_CAMPAIGN = 'campaign';

    protected $guarded = [self::FIELD_ID];

    protected $table = self::TABLE;

    /**
     * Defines relationship to lead.
     *
     * @return BelongsTo
     */
    public function lead(): BelongsTo
    {
        return $this->belongsTo(EloquentQuote::class, self::FIELD_LEAD_ID, EloquentQuote::ID);
    }

    /**
     * Defines relationship to company.
     *
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(EloquentCompany::class, self::FIELD_COMPANY_ID, EloquentCompany::ID);
    }

    /**
     * @return BelongsTo
     */
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(LeadCampaign::class, self::FIELD_CAMPAIGN_ID, LeadCampaign::ID);
    }

}
