<?php

namespace App\Models\Mailbox;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int id
 * @property string external_id
 * @property int user_id
 * @property string slug
 * @property string name
 * @property Carbon created_at
 * @property Carbon updated_at
 */
class MailboxUserLabel extends Model
{
    use SoftDeletes;

    const TABLE                 = 'mailbox_user_labels';

    const FIELD_ID              = 'id';
    const FIELD_EXTERNAL_ID     = 'external_id';
    const FIELD_USER_ID         = 'user_id';
    const FIELD_SLUG            = 'slug';
    const FIELD_NAME            = 'name';

    protected $fillable = [
        self::FIELD_EXTERNAL_ID,
        self::FIELD_USER_ID,
        self::FIELD_SLUG,
        self::FIELD_NAME,
    ];
}
