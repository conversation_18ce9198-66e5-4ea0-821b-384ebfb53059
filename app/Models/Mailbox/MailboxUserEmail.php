<?php

namespace App\Models\Mailbox;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int id
 * @property int user_id
 * @property int email_id
 * @property boolean is_inbox
 * @property boolean is_starred
 * @property boolean is_important
 * @property boolean is_archived
 * @property boolean is_read
 *
 * @property Carbon created_at
 * @property Carbon updated_at
 */
class MailboxUserEmail extends Model
{
    use SoftDeletes;

    const TABLE                         = 'mailbox_user_emails';
    const FIELD_ID                      = 'id';
    const FIELD_UUID                    = 'uuid';
    const FIELD_USER_ID                 = 'user_id';
    const FIELD_EMAIL_ID                = 'email_id';
    const FIELD_IS_INBOX                = 'is_inbox';
    const FIELD_IS_SENT                 = 'is_sent';
    const FIELD_IS_STARRED              = 'is_starred';
    const FIELD_IS_IMPORTANT            = 'is_important';
    const FIELD_IS_ARCHIVED             = 'is_archived';
    const FIELD_IS_READ                 = 'is_read';
    const FIELD_EXTERNAL_ID             = 'external_id';
    const FIELD_EXTERNAL_THREAD_ID      = 'external_thread_id';
    const FIELD_EXTERNAL_HISTORY_ID     = 'external_history_id';
    const FIELD_EXTERNAL_REFERENCES     = 'external_references';
    const FIELD_SENT_AT                 = 'sent_at';
    const FIELD_DELETED_AT              = 'deleted_at';
    const RELATION_EMAIL                = 'email';
    const RELATION_USER                 = 'user';
    const RELATION_LABEL                = 'labels';

    protected $fillable = [
        self::FIELD_UUID,
        self::FIELD_USER_ID,
        self::FIELD_EMAIL_ID,
        self::FIELD_IS_INBOX,
        self::FIELD_IS_SENT,
        self::FIELD_IS_STARRED,
        self::FIELD_IS_IMPORTANT,
        self::FIELD_IS_ARCHIVED,
        self::FIELD_IS_READ,
        self::FIELD_EXTERNAL_ID,
        self::FIELD_EXTERNAL_THREAD_ID,
        self::FIELD_EXTERNAL_HISTORY_ID,
        self::FIELD_EXTERNAL_REFERENCES,
        self::FIELD_SENT_AT,
    ];

    /**
     * @return BelongsTo
     */
    public function email(): BelongsTo
    {
        return $this->belongsTo(MailboxEmail::class, self::FIELD_EMAIL_ID, MailboxEmail::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function labels(): HasMany
    {
        return $this->hasMany(MailboxEmailLabel::class, self::FIELD_EMAIL_ID, MailboxEmailLabel::FIELD_EMAIL_ID);
    }
}
