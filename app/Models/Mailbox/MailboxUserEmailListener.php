<?php

namespace App\Models\Mailbox;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @property int id
 * @property int user_id
 * @property string expires_at
 * @property Carbon created_at
 * @property Carbon updated_at
 */
class MailboxUserEmailListener extends Model
{
    const TABLE             = 'mailbox_user_email_listener';

    const FIELD_ID          = 'id';
    const FIELD_USER_ID     = 'user_id';
    const FIELD_EXPIRES_AT  = 'expires_at';

    const RELATION_USER     = 'user';

    protected $fillable = [
        self::FIELD_USER_ID,
        self::FIELD_EXPIRES_AT,
    ];

    /**
     * @return HasOne
     */
    public function user(): HasOne
    {
        return $this->hasOne(User::class, User::FIELD_ID, self::FIELD_USER_ID);
    }
}
