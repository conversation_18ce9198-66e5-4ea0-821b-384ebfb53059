<?php

namespace App\Models\Mailbox;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int id
 * @property int user_id
 * @property string token
 * @property string refresh_token
 * @property Carbon created_at
 * @property Carbon updated_at
 *
 * @property-read User $user
 */
class MailboxUserToken extends Model
{
    const TABLE                 = 'mailbox_user_tokens';

    const FIELD_ID              = 'id';
    const FIELD_USER_ID         = 'user_id';
    const FIELD_TOKEN           = 'token';
    const FIELD_REFRESH_TOKEN   = 'refresh_token';
    const FIELD_CREATED_AT      = 'created_at';
    const FIELD_UPDATED_AT      = 'updated_at';

    const RELATION_USER         = 'user';

    protected $fillable = [
        self::FIELD_USER_ID,
        self::FIELD_TOKEN,
        self::FIELD_REFRESH_TOKEN,
        self::FIELD_CREATED_AT,
        self::FIELD_UPDATED_AT,
    ];

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }
}
