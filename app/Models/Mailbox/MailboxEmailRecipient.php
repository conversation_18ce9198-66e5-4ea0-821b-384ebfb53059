<?php

namespace App\Models\Mailbox;

use App\Traits\ContactIdentifiable;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int id
 * @property int email_id
 * @property string email_address
 * @property string type
 * @property string relation_type
 * @property string relation_id
 * @property Carbon created_at
 * @property Carbon updated_at
 *
 * @property-read MailboxEmail $email
 */
class MailboxEmailRecipient extends Model
{
    use ContactIdentifiable;

    const TABLE                       = 'mailbox_email_recipients';
    const FIELD_ID                    = 'id';
    const FIELD_EMAIL_ID              = 'email_id';
    const FIELD_EMAIL_ADDRESS         = 'email_address';
    const FIELD_TYPE                  = 'type';
    const FIELD_RELATION_TYPE         = 'relation_type';
    const FIELD_RELATION_ID           = 'relation_id';
    const FIELD_CREATED_AT            = 'created_at';
    const FIELD_UPDATED_AT            = 'updated_at';


    protected $fillable = [
        self::FIELD_EMAIL_ID,
        self::FIELD_EMAIL_ADDRESS,
        self::FIELD_TYPE,
        self::FIELD_RELATION_TYPE,
        self::FIELD_RELATION_ID,
        self::FIELD_CREATED_AT,
        self::FIELD_UPDATED_AT,
        self::FIELD_IDENTIFIED_CONTACT_ID,
    ];

    /**
     * @return BelongsTo
     */
    public function email(): BelongsTo
    {
        return $this->belongsTo(MailboxEmail::class, self::FIELD_EMAIL_ID, MailboxEmail::FIELD_ID);
    }
}
