<?php

namespace App\Models\Mailbox;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int id
 * @property int email_id
 * @property string external_id
 * @property string external_attachment_id
 * @property string external_filename
 * @property string external_type
 * @property string url
 * @property int size
 * @property Carbon created_at
 * @property Carbon updated_at
 */
class MailboxEmailAttachment extends Model
{
    const TABLE = 'mailbox_email_attachments';

    const FIELD_ID                          = 'id';
    const FIELD_EMAIL_ID                    = 'email_id';
    const FIELD_EXTERNAL_ID                 = 'external_id';
    const FIELD_EXTERNAL_ATTACHMENT_ID      = 'external_attachment_id';
    const FIELD_EXTERNAL_FILENAME           = 'external_filename';
    const FIELD_EXTERNAL_TYPE               = 'external_type';
    const FIELD_URL                         = 'url';
    const FIELD_SIZE                        = 'size';

    const FIELD_CREATED_AT                  = 'created_at';
    const FIELD_UPDATED_AT                  = 'updated_at';
    const RELATION_EMAIL                    = 'email';

    protected $fillable = [
        self::FIELD_ID,
        self::FIELD_EMAIL_ID,
        self::FIELD_EXTERNAL_ID,
        self::FIELD_EXTERNAL_ATTACHMENT_ID,
        self::FIELD_EXTERNAL_FILENAME,
        self::FIELD_EXTERNAL_TYPE,
        self::FIELD_URL,
        self::FIELD_SIZE,
    ];


    /**
     * @return BelongsTo
     */
    public function email(): BelongsTo
    {
        return $this->belongsTo(MailboxEmail::class, self::FIELD_EMAIL_ID, MailboxEmail::FIELD_ID);
    }
}
