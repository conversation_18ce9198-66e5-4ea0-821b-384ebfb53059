<?php

namespace App\Models\Mailbox;

use App\Enums\Mailbox\EmailType;
use App\Models\ActivityFeed;
use App\Models\ContactIdentification\IdentifiedContact;
use App\Models\Note;
use App\Models\User;
use App\Traits\ContactIdentifiable;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $content
 * @property int $from_user_id
 * @property string $from_user_email
 * @property string $direction
 * @property string $subject
 * @property string $snippet
 * @property string $type
 * @property string $external_message_id
 * @property string $from_identified_contact_id
 * @property string $from_a20
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read array<MailboxEmailRecipient> $recipients
 * @property-read array<MailboxEmailAttachment> $attachments
 * @property-read array<MailboxUserLabel> $labels
 * @property-read User $fromUser
 */
class MailboxEmail extends Model
{
    use SoftDeletes, ContactIdentifiable;

    protected string $fieldIdentifiedContactId = self::FIELD_FROM_IDENTIFIED_CONTACT_ID;

    const TABLE = 'mailbox_emails';

    const string FIELD_ID                         = 'id';
    const string FIELD_CONTENT                    = 'content';
    const string FIELD_FROM_USER_ID               = 'from_user_id';
    const string FIELD_FROM_USER_EMAIL            = 'from_user_email';
    const string FIELD_DIRECTION                  = 'direction';
    const string FIELD_SUBJECT                    = 'subject';
    const string FIELD_SNIPPET                    = 'snippet';
    const string FIELD_TYPE                       = 'type';
    const string FIELD_EXTERNAL_MESSAGE_ID        = 'external_message_id';
    const string FIELD_SENT_AT                    = 'sent_at';
    const string FIELD_CREATED_AT                 = 'created_at';
    const string FIELD_UPDATED_AT                 = 'updated_at';
    const string FIELD_FROM_IDENTIFIED_CONTACT_ID = 'from_identified_contact_id';
    const string FIELD_FROM_A20                   = 'from_a20';

    const string RELATION_RECIPIENTS              = 'recipients';
    const string RELATION_FROM_USER               = 'fromUser';
    const string RELATION_FROM_IDENTIFIED_CONTACT = 'fromIdentifiedContact';
    const string RELATION_LABELS                  = 'labels';
    const string RELATION_ATTACHMENTS             = 'attachments';

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_TYPE => EmailType::class
    ];

    /**
     * @return HasMany
     */
    public function recipients(): HasMany
    {
        return $this->hasMany(MailboxEmailRecipient::class, MailboxEmailRecipient::FIELD_EMAIL_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function attachments(): HasMany
    {
        return $this->hasMany(MailboxEmailAttachment::class, MailboxEmailAttachment::FIELD_EMAIL_ID, self::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function fromUser(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_FROM_USER_ID, User::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function fromIdentifiedContact(): BelongsTo
    {
        return $this->belongsTo(IdentifiedContact::class, self::FIELD_FROM_IDENTIFIED_CONTACT_ID, IdentifiedContact::FIELD_ID);
    }

    /**
     * @return HasManyThrough
     */
    public function labels(): HasManyThrough
    {
        return $this->hasManyThrough(
            MailboxUserLabel::class,
            MailboxEmailLabel::class,
            MailboxEmailLabel::FIELD_EMAIL_ID,
            MailboxUserLabel::FIELD_ID,
            self::FIELD_ID,
            MailboxEmailLabel::FIELD_LABEL_ID
        );
    }

    /**
     * @return Collection
     */
    public function getAllUniqueAttachmentsInThread(): Collection
    {
        return $this->{self::RELATION_ATTACHMENTS}()
            ->groupBy(MailboxEmailAttachment::FIELD_EXTERNAL_FILENAME)
            ->get();
    }

    /**
     * @return MorphMany
     */
    public function notes(): \Illuminate\Database\Eloquent\Relations\MorphMany
    {
        return $this->morphMany(Note::class, Note::RELATION_NOTABLE, Note::FIELD_RELATION_TYPE, Note::FIELD_RELATION_ID);
    }

    /**
     * @return MorphOne
     */
    public function activity(): MorphOne
    {
        return $this->morphOne(ActivityFeed::class, 'item');
    }
}
