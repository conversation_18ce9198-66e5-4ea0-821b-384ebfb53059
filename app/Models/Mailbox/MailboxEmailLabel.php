<?php

namespace App\Models\Mailbox;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int id
 * @property int email_id
 * @property int label_id
 * @property Carbon created_at
 * @property Carbon updated_at
 *
 * @property-read MailboxEmail $email
 * @property-read MailboxUserLabel $label
 */
class MailboxEmailLabel extends Model
{
    const TABLE             = 'mailbox_email_labels';

    const FIELD_ID          = 'id';
    const FIELD_EMAIL_ID    = 'email_id';
    const FIELD_LABEL_ID    = 'label_id';

    protected $guarded = [
        self::FIELD_ID,
    ];

    /**
     * @return BelongsTo
     */
    public function email(): BelongsTo
    {
        return $this->belongsTo(MailboxEmail::class, self::FIELD_EMAIL_ID, MailboxEmail::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function label(): BelongsTo
    {
        return $this->belongsTo(MailboxUserLabel::class, self::FIELD_EMAIL_ID, MailboxUserLabel::FIELD_ID);
    }
}
