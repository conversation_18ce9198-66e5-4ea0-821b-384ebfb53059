<?php

namespace App\Models;

use App\Models\Odin\Website;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @property int $id
 * @property string $platform
 * @property string $platform_account_id
 * @property int $website_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Website $website
 */
class AdvertisingAccountWebsite extends Model
{
    use HasFactory;

    const TABLE = 'advertising_account_websites';

    const FIELD_ID = 'id';
    const FIELD_PLATFORM = 'platform';
    const FIELD_PLATFORM_ACCOUNT_ID = 'platform_account_id';
    const FIELD_WEBSITE_ID = 'website_id';

    const RELATION_WEBSITE = 'website';

    protected $table = self::TABLE;
    protected $guarded = [
        self::FIELD_ID
    ];

    /**
     * @return HasOne
     */
    public function website(): HasOne
    {
        return $this->hasOne(Website::class, Website::FIELD_ID, self::FIELD_WEBSITE_ID);
    }
}
