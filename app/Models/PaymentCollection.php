<?php

namespace App\Models;

use Database\Factories\Odin\PaymentCollectionFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentCollection extends Model
{
    use HasFactory;

    protected static function newFactory(): PaymentCollectionFactory
    {
        return PaymentCollectionFactory::new();
    }

    const TABLE = 'payment_collections';

    const FIELD_ID = 'id';
    const FIELD_COMPANY_ID = 'company_id';
    const FIELD_PAID = 'paid';
    const FIELD_PAID_DATE = 'paid_date';
    const FIELD_AMOUNT = 'amount';
    const FIELD_NOTES = 'notes';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];
}
