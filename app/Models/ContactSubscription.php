<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * @param int $id
 * @param int $contact_id
 * @param string $contact_type
 * @param string $contact_method
 * @param string $notification_type
 * @param int $unsubscribed
 * @param Carbon $created_at
 * @param Carbon $updated_at
 * @param Carbon $deleted_at
 */
class ContactSubscription extends BaseModel
{
    use SoftDeletes, HasFactory;

    const TABLE = 'contact_subscriptions';

    const FIELD_ID = 'id';
    const FIELD_CONTACT_ID = 'contact_id';
    const FIELD_CONTACT_TYPE = 'contact_type';
    const FIELD_CONTACT_METHOD = 'contact_method';
    const FIELD_NOTIFICATION_TYPE = 'notification_type';
    const FIELD_UNSUBSCRIBED = 'unsubscribed';
    const FIELD_CREATED_AT = 'created_at';
    const FIELD_UPDATED_AT = 'updated_at';
    const FIELD_DELETED_AT = 'deleted_at';

    const SUBSCRIBED = 0;

    protected $table = self::TABLE;

    protected $fillable = [
        self::FIELD_ID,
        self::FIELD_CONTACT_ID,
        self::FIELD_CONTACT_TYPE,
        self::FIELD_CONTACT_METHOD,
        self::FIELD_NOTIFICATION_TYPE,
        self::FIELD_UNSUBSCRIBED
    ];

    const CONTACT_TYPE_CONTACT = 'contact';
    const CONTACT_TYPE_USER = 'user';

    const CONTACT_METHOD_EMAIL = 'email';
    const CONTACT_METHOD_SMS = 'sms';

    const NOTIFICATION_TYPE_SALESBAIT = 'salesbait';
}
