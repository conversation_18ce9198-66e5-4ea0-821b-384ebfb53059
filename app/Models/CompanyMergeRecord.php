<?php

namespace App\Models;

use App\Models\Odin\Company;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property int $source_company_id
 * @property int $target_company_id
 * @property array $undo_payload
 * @property array $queued_undo_payload
 * @property Carbon $completed_at
 *
 * @property-read Company $sourceCompany
 * @property-read Company $targetCompany
 */
class CompanyMergeRecord extends BaseModel
{
    const string TABLE = 'company_merge_records';

    const string FIELD_ID                = 'id';
    const string FIELD_SOURCE_COMPANY_ID = 'source_company_id';
    const string FIELD_TARGET_COMPANY_ID = 'target_company_id';
    const string FIELD_PAYLOAD           = 'undo_payload';
    const string FIELD_QUEUED_PAYLOAD    = 'queued_undo_payload';
    const string FIELD_COMPLETED_AT      = 'completed_at';

    const string RELATION_SOURCE_COMPANY = 'sourceCompany';
    const string RELATION_TARGET_COMPANY = 'targetCompany';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_PAYLOAD        => 'array',
        self::FIELD_QUEUED_PAYLOAD => 'array',
    ];

    /**
     * @return BelongsTo
     */
    public function sourceCompany(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_SOURCE_COMPANY_ID, Company::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function targetCompany(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_TARGET_COMPANY_ID, Company::FIELD_ID);
    }
}
