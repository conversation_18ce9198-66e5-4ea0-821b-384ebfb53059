<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property string $event_category
 * @property string $event_name
 * @property-read Collection<Workflow> $workflows
 */
class WorkflowEvent extends Model
{
    use HasFactory;

    const TABLE = 'workflow_events';

    const FIELD_ID = 'id';
    const FIELD_EVENT_CATEGORY = 'event_category';
    const FIELD_EVENT_NAME = 'event_name';

    protected $fillable = [
        self::FIELD_EVENT_CATEGORY,
        self::FIELD_EVENT_NAME
    ];

    protected $table = self::TABLE;

    const RELATION_WORKFLOWS = 'workflows';

    /**
     * Get the workflows attached to this event
     *
     * @return HasMany
     */
    public function workflows(): HasMany
    {
        return $this->hasMany(Workflow::class, Workflow::FIELD_WORKFLOW_EVENT_ID, self::FIELD_ID);
    }
}
