<?php

namespace App\Models;

use Illuminate\Http\Response;
use Twilio\TwiML\VoiceResponse;

/**
 * @property int id
 * @property int $user_id
 * @property string $path
 */
class UserVoicemailMessage extends BaseModel
{
    const TABLE = 'user_voicemail_messages';

    const FIELD_ID      = 'id';
    const FIELD_USER_ID = 'user_id';
    const FIELD_PATH    = 'path';

    protected $table   = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];

    /**
     * @return Response|null
     */
    public function getTwilioResponse(): ?Response
    {
        if(!file_exists($this->path))
            return null;

        $response = new VoiceResponse();
        $response->play(url($this->path));
        $response->record(["timeout" => 30, "transcribe" => true]);
        $response->hangup();
        return response($response->asXML());
    }
}
