<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * This model can be used to decouple a round robin from other business logic where needed
 *
 * @property int $id
 * @property int $user_id
 * @property int $round_robin_id
 *
 * @property-read User $user
 */
class RoundRobinParticipant extends BaseModel
{
    const string TABLE = 'round_robin_participants';

    const string FIELD_ID             = 'id';
    const string FIELD_ROUND_ROBIN_ID = 'round_robin_id';
    const string FIELD_USER_ID        = 'user_id';

    protected $guarded = [self::FIELD_ID];

    /** @return BelongsTo */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }

    /** @return BelongsTo */
    public function roundRobin(): BelongsTo
    {
        return $this->belongsTo(RoundRobin::class, self::FIELD_ROUND_ROBIN_ID, RoundRobin::FIELD_ID);
    }
}