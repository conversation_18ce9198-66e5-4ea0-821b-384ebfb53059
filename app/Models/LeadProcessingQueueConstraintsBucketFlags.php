<?php

namespace App\Models;

use App\Models\Legacy\EloquentQuote;
use App\Models\Odin\ConsumerProduct;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @class LeadProcessingQueueConstraintsBucketFlags
 *
 * @property int $budget_status_id
 * @property int $timeframe_id
 * @property int $consumer_product_id
 * @property boolean $super_premium
 * @property boolean $qualified
 * @property boolean $previously_qualified
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read LeadProcessingBudgetStatus $budgetStatus
 * @property-read LeadProcessingTimeframe $agePhase
 */
class LeadProcessingQueueConstraintsBucketFlags extends BaseModel
{
    const string TABLE = 'lead_processing_queue_constraints_bucket_flags';

    const string FIELD_ID                  = 'id';
    const string FIELD_CONSUMER_PRODUCT_ID = 'consumer_product_id';
    const string FIELD_BUDGET_STATUS_ID    = 'budget_status_id';
    const string FIELD_TIMEFRAME_ID        = 'timeframe_id';
    const string FIELD_SUPER_PREMIUM       = 'super_premium';
    const string FIELD_CREATED_AT          = 'created_at';
    const string FIELD_UPDATED_AT          = 'updated_at';

    /* @deprecated */
    const string FIELD_LEAD_ID             = 'lead_id';

    const string RELATION_LEAD          = 'lead';
    const string RELATION_BUDGET_STATUS = 'budgetStatus';
    const string RELATION_TIMEFRAME     = 'timeframe';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];

    /**
     * @deprecated
     */
    public function lead(): BelongsTo
    {
        return $this->belongsTo(EloquentQuote::class, self::FIELD_LEAD_ID, EloquentQuote::ID);
    }

    /**
     * @return BelongsTo
     */
    public function consumerProduct(): BelongsTo
    {
        return $this->belongsTo(ConsumerProduct::class, self::FIELD_CONSUMER_PRODUCT_ID, ConsumerProduct::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function budgetStatus(): HasOne
    {
        return $this->hasOne(LeadProcessingBudgetStatus::class, LeadProcessingBudgetStatus::FIELD_ID, self::FIELD_BUDGET_STATUS_ID);
    }

    /**
     * @return HasOne
     */
    public function timeframe(): HasOne
    {
        return $this->hasOne(LeadProcessingTimeframe::class, LeadProcessingTimeframe::FIELD_ID, self::FIELD_TIMEFRAME_ID);
    }
}
