<?php
namespace App\Models\ConsumerReviews;

use App\Models\BaseModel;
use App\Models\Odin\CompanyUser;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id;
 * @property string $comments;
 * @property int $admin_user_id;
 * @property int $company_user_id;
 * @property string|null $custom_admin_name
 * @property Carbon $created_at;
 * @property Carbon $updated_at;
 * @property Carbon $deleted_at;
 *
 * @property-read CompanyUser|null $companyUser
 * @property-read User|null $user
 * @property-read Review $review
 */

class ReviewReply extends BaseModel
{
    use HasFactory, SoftDeletes;

    const string TABLE                      = 'review_replies';
    const string FIELD_ID                   = 'id';
    const string FIELD_REVIEW_ID            = 'review_id';
    const string FIELD_COMMENTS             = 'comments';
    const string FIELD_COMPANY_USER_ID      = 'company_user_id';
    const string FIELD_ADMIN_USER_ID        = 'admin_user_id';
    const string FIELD_CUSTOM_ADMIN_NAME    = 'custom_admin_name';
    const string FIELD_IS_PUBLIC            = 'is_public';

    const string RELATION_COMPANY_USER = 'company_user';
    const string RELATION_USER         = 'user';
    const string RELATION_REVIEW       = 'review';

    const string DEFAULT_REPLY_ADMIN_NAME   = 'Fixr';

    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function companyUser(): BelongsTo
    {
        return $this->belongsTo(CompanyUser::class, self::FIELD_COMPANY_USER_ID, CompanyUser::FIELD_ID);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_ADMIN_USER_ID, User::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function review(): BelongsTo
    {
        return $this->belongsTo(Review::class, self::FIELD_REVIEW_ID, Review::FIELD_ID);
    }
}
