<?php
namespace App\Models\ConsumerReviews;

use App\Models\Odin\Consumer;
use Carbon\Carbon;
use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\ReviewerCanVerifyEmail;
use Illuminate\Notifications\Notifiable;

/**
 * @property int $id
 * @property string $reference
 * @property string $name
 * @property string $email
 * @property string $phone
 * @property int address_id
 * @property boolean $is_email_verified
 * @property boolean $is_phone_verified
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $deleted_at
 *
 */

class Reviewer extends BaseModel
{
    use HasFactory, SoftDeletes, ReviewerCanVerifyEmail, Notifiable;

    const string TABLE                              = 'reviewers';
    const string FIELD_ID                           = 'id';
    const string FIELD_REFERENCE                    = 'reference';
    const string FIELD_NAME                         = 'name';
    const string FIELD_EMAIL                        = 'email';
    const string FIELD_PHONE                        = 'phone';
    const string FIELD_ADDRESS_ID                   = 'address_id';
    const string FIELD_EMAIL_VERIFICATION_TOKEN     = 'email_verification_token';
    const string FIELD_IS_EMAIL_VERIFIED            = 'is_email_verified';
    const string FIELD_EMAIL_VERIFIED_AT            = 'email_verified_at';
    const string FIELD_IS_PHONE_VERIFIED            = 'is_phone_verified';
    const string FIELD_DATA                         = 'data';
    const string FIELD_DATA_CONSUMERS               = 'related_consumer_ids';

    const int EMAIL_VERIFICATION_TOKEN_LENGTH       = 40;


    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_DATA => 'array'
    ];

    /**
     * @return HasMany
     */
    public function review(): HasMany
    {
        return $this->hasMany(Review::class, Review::FIELD_REVIEWER_ID, Reviewer::FIELD_ID);
    }

}
