<?php

namespace App\Models\ConsumerReviews;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property float $overall_score
 * @property string $title
 * @property string $comments
 * @property array $data
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 */
class ReviewData extends BaseModel
{
    use HasFactory;

    const string TABLE = 'review_data';

    const string FIELD_ID            = 'id';
    const string FIELD_OVERALL_SCORE = 'overall_score';
    const string FIELD_TITLE         = 'title';
    const string FIELD_COMMENTS      = 'comments';
    const string FIELD_DATA          = 'data';

    const string DATA_KEY_USER_IP          = 'user_ip_address';
    const string DATA_KEY_ZIP_CODE         = 'zip_code';
    const string DATA_KEY_DISPLAY_LOCATION = 'display_location';
    const string DATA_KEY_CUSTOM           = 'custom';
    const string DATA_KEY_ATTACHMENTS      = 'attachments';

    const string ATTACHMENT_KEY_ID         = 'id';
    const string ATTACHMENT_KEY_PUBLIC_URL = 'url';

    const string DATA_KEY_TYPE  = 'type';
    const string DATA_KEY_VALUE = 'value';

    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_DATA => 'array'
    ];

    /**
     * @return BelongsTo
     */
    public function review(): BelongsTo
    {
        return $this->belongsTo(Review::class, Review::FIELD_REVIEW_DATA_ID, self::FIELD_ID);
    }
}
