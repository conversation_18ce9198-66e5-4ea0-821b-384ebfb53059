<?php

namespace App\Models\ConsumerReviews;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property float $average_score
 * @property int $company_id
 * @property int $company_location_id
 * @property int $num_reviews
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */

class CompanyRating extends Model
{
    use HasFactory;

    const string TABLE                         = 'company_ratings';

    const string FIELD_ID                      = 'id';
    const string FIELD_OVERALL_SCORE           = 'average_score';
    const string FIELD_NUM_REVIEWS             = 'num_reviews';
    const string FIELD_COMPANY_ID              = 'company_id';
    const string FIELD_COMPANY_LOCATION_ID     = 'company_location_id';
    const string FIELD_CREATED_AT              = 'created_at';
    const string FIELD_DATA                    = 'data';
    const string FIELD_DATA_COUNT_BREAKDOWN    = 'count_breakdown';
    const string RELATION_COMPANY              = 'company';
    const string RELATION_COMPANY_LOCATION     = 'companyLocation';

    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_DATA => 'array'
    ];

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function companyLocation(): BelongsTo
    {
        return $this->belongsTo(CompanyLocation::class, self::FIELD_COMPANY_LOCATION_ID, CompanyLocation::FIELD_ID);
    }
}
