<?php
namespace App\Models\ConsumerReviews;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Website;
use App\Traits\HasUuid;
use Carbon\Carbon;
use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property string $uuid
 * @property int $reviewer_id
 * @property int $company_id
 * @property int $company_location_id
 * @property int $review_data_id
 * @property int $review_reply_id
 * @property int $industry_id
 * @property int $industry_service_id
 * @property string $status
 * @property int $approver_user_id
 * @property Carbon $approved_at
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $deleted_at
 *
 * @property-read Company $company
 * @property-read Website $website
 * @property-read CompanyLocation $companyLocation
 * @property-read ReviewData $reviewData
 * @property-read Collection<ReviewReply> $reviewReplies
 * @property-read Collection<ReviewReply> $publicReplies
 * @property-read Reviewer $reviewer
 * @property-read Industry $industry
 * @property-read IndustryService $industryService
 */

class Review extends BaseModel
{
    use HasFactory, SoftDeletes, HasUuid;

    const string TABLE                     = 'reviews';

    const string FIELD_ID                  = 'id';
    const string FIELD_UUID                = 'uuid';
    const string FIELD_LEGACY_ID           = 'legacy_id';
    const string FIELD_REVIEWER_ID         = 'reviewer_id';
    const string FIELD_COMPANY_ID          = 'company_id';
    const string FIELD_COMPANY_LOCATION_ID = 'company_location_id';
    const string FIELD_REVIEW_DATA_ID      = 'review_data_id';
    const string FIELD_INDUSTRY_ID         = 'industry_id';
    const string FIELD_INDUSTRY_SERVICE_ID = 'industry_service_id';
    const string FIELD_WEBSITE_ID          = 'website_id';
    const string FIELD_STATUS              = 'status';
    const string FIELD_APPROVER_USER_ID    = 'approver_user_id';
    const string FIELD_APPROVED_AT         = 'approved_at';
    const string FIELD_IS_VERIFIED         = 'is_verified';
    const string FIELD_CREATED_AT          = 'created_at';
    const string RELATION_REVIEWER         = 'reviewer';
    const string RELATION_REVIEW_DATA      = 'reviewData';
    const string RELATION_REVIEW_REPLIES   = 'reviewReplies';
    const string RELATION_PUBLIC_REPLIES   = 'publicReplies';
    const string FIELD_DELETED_AT          = 'deleted_at';

    const int STATUS_REJECTED           = -1;

    const int STATUS_PENDING_APPROVAL   = 0;
    const int STATUS_APPROVED           = 1;

    const array STATUS_MAPPING = [
        self::STATUS_REJECTED           => 'Rejected',
        self::STATUS_PENDING_APPROVAL   => 'Pending',
        self::STATUS_APPROVED           => 'Approved',
    ];

    const array STATUSES = [
        self::STATUS_REJECTED,
        self::STATUS_PENDING_APPROVAL,
        self::STATUS_APPROVED
    ];

    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function companyLocation(): BelongsTo
    {
        return $this->belongsTo(CompanyLocation::class, self::FIELD_COMPANY_LOCATION_ID, CompanyLocation::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function industry(): BelongsTo
    {
        return $this->belongsTo(Industry::class, self::FIELD_INDUSTRY_ID, Industry::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function industryService(): BelongsTo
    {
        return $this->belongsTo(IndustryService::class, self::FIELD_INDUSTRY_SERVICE_ID, IndustryService::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(Reviewer::class, self::FIELD_REVIEWER_ID, Reviewer::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function reviewData(): HasOne
    {
        return $this->hasOne(ReviewData::class, ReviewData::FIELD_ID, self::FIELD_REVIEW_DATA_ID);
    }

    /**
     * @return BelongsTo
     */
    public function website(): BelongsTo
    {
        return $this->belongsTo(Website::class, self::FIELD_WEBSITE_ID, Website::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function reviewReplies(): HasMany
    {
        return $this->hasMany(ReviewReply::class, ReviewReply::FIELD_REVIEW_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function publicReplies(): HasMany
    {
        return $this->hasMany(ReviewReply::class, ReviewReply::FIELD_REVIEW_ID, self::FIELD_ID)
            ->where(ReviewReply::FIELD_IS_PUBLIC, true);
    }
}

