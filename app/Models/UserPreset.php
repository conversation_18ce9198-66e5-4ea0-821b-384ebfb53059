<?php

namespace App\Models;

use App\Enums\UserPresetType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property int $user_id
 * @property string $name
 * @property string $key
 * @property UserPresetType $type
 * @property string $category
 * @property array $value
 *
 * @property-read User $user
 */
class UserPreset extends BaseModel
{
    use HasFactory, SoftDeletes;

    const FIELD_ID              = 'id';
    const FIELD_USER_ID         = 'user_id';
    const FIELD_NAME            = 'name';
    const FIELD_KEY             = 'key';
    const FIELD_TYPE            = 'type';
    const FIELD_CATEGORY        = 'category';
    const FIELD_VALUE           = 'value';

    const RELATION_USER         = 'user';

    const TABLE                 = 'user_presets';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_VALUE   => 'array',
        self::FIELD_TYPE    => UserPresetType::class,
    ];

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }
}
