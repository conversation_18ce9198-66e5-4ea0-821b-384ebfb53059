<?php

namespace App\Models;

use App\Enums\RoundRobinType;
use App\States\Billing\Collection;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int $id
 * @property RoundRobinType $type
 * @property integer $last_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property string|null $filter
 *
 * @property-read Collection<RoundRobinParticipant> $participants
 */
class RoundRobin extends Model
{
    use HasFactory;

    const string TABLE = 'round_robins';

    const string FIELD_ID      = 'id';
    const string FIELD_TYPE    = 'type';
    const string FIELD_LAST_ID = 'last_id';
    const string FIELD_FILTER  = 'filter';

    protected $table   = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts   = [self::FIELD_TYPE => RoundRobinType::class];

    /** @return HasMany */
    public function participants(): HasMany
    {
        return $this->hasMany(RoundRobinParticipant::class, RoundRobinParticipant::FIELD_ROUND_ROBIN_ID, self::FIELD_ID);
    }
}
