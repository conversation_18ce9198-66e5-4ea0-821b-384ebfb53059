<?php

namespace App\Models;

use App\Models\Legacy\EloquentQuote;
use App\Models\Odin\ConsumerProduct;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class LeadProcessingPendingReview
 *
 * @property int                $id
 * @property int                $lead_processor_id
 * @property string             $reason
 * @property int                $consumer_product_id
 *
 * @property-read LeadProcessor $leadProcessor
 * @property-read ConsumerProduct $consumerProduct
 */
class LeadProcessingPendingReview extends BaseModel
{
    const string TABLE = 'lead_processing_pending_reviews';

    const string FIELD_ID                  = 'id';
    const string FIELD_CONSUMER_PRODUCT_ID = 'consumer_product_id';
    const string FIELD_USER_ID             = 'lead_processor_id';
    const string FIELD_REASON              = 'reason';
    /** @deprecated  */
    const string FIELD_LEAD_ID = 'lead_id';

    const string RELATION_LEAD_PROCESSOR = 'leadProcessor';
    /** @deprecated  */
    const string RELATION_LEAD           = 'lead';
    const string RELATION_USER           = 'user';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function leadProcessor(): BelongsTo
    {
        return $this->belongsTo(LeadProcessor::class, self::FIELD_USER_ID, LeadProcessor::FIELD_ID)->withTrashed();
    }

    /**
     * @deprecated
     */
    public function consumerProduct(): BelongsTo
    {
        return $this->belongsTo(ConsumerProduct::class, self::FIELD_CONSUMER_PRODUCT_ID, ConsumerProduct::FIELD_ID);
    }

    /**
     * @deprecated
     */
    public function lead(): BelongsTo
    {
        return $this->belongsTo(EloquentQuote::class, self::FIELD_LEAD_ID, EloquentQuote::ID);
    }
}
