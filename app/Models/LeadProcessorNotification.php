<?php

namespace App\Models;

use App\Models\Legacy\EloquentQuote;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @property int $id
 * @property int $lead_processor_id
 * @property string $body
 * @property int $lead_id
 * @property bool $read
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read EloquentQuote|null $lead
 * @property-read LeadProcessor $leadProcessor
 */
class LeadProcessorNotification extends BaseModel
{
    const TABLE = 'lead_processor_notifications';

    const FIELD_ID = 'id';
    const FIELD_LEAD_PROCESSOR_ID = 'lead_processor_id';
    const FIELD_SUBJECT = 'subject';
    const FIELD_BODY = 'body';
    const FIELD_LEAD_ID = 'lead_id';
    const FIELD_READ = 'read';

    const RELATION_LEAD = 'lead';
    const RELATION_LEAD_PROCESSOR = 'leadProcessor';

    const READ = 1;
    const UNREAD = 0;

    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    /**
     * @return HasOne
     */
    public function lead(): HasOne
    {
        return $this->hasOne(EloquentQuote::class, EloquentQuote::ID, self::FIELD_LEAD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function leadProcessor(): BelongsTo
    {
        return $this->belongsTo(LeadProcessor::class, self::FIELD_LEAD_PROCESSOR_ID, LeadProcessor::FIELD_ID);
    }
}
