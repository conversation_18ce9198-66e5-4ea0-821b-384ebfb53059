<?php

namespace App\Models;

use App\Models\Legacy\EloquentQuote;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $lead_id
 * @property string $type
 * @property float $current_revenue_scenario
 * @property float $maximum_revenue_scenario
 * @property float $sold_for
 * @property float $floor_price
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read EloquentQuote $lead
 */
class LeadRevenueScenario extends BaseModel
{
    const TABLE = 'lead_revenue_scenarios';

    const FIELD_ID                       = 'id';
    const FIELD_LEAD_ID                  = 'lead_id';
    const FIELD_TYPE                     = 'type';
    const FIELD_CURRENT_REVENUE_SCENARIO = 'current_revenue_scenario';
    const FIELD_MAXIMUM_REVENUE_SCENARIO = 'maximum_revenue_scenario';
    const FIELD_SOLD_FOR                 = 'sold_for';
    const FIELD_FLOOR_PRICE              = 'floor_price';

    const TYPE_EMAIL_ONLY = 'email_only';
    const TYPE_UNVERIFIED = 'unverified';
    const TYPE_VERIFIED   = 'verified';
    const TYPE_CANCELLED  = 'cancelled';

    const CLASSIFICATION_MAPPINGS = [
        self::TYPE_EMAIL_ONLY => EloquentQuote::CLASSIFICATION_EMAIL_ONLY,
        self::TYPE_UNVERIFIED => EloquentQuote::CLASSIFICATION_UNVERIFIED,
        self::TYPE_VERIFIED => EloquentQuote::CLASSIFICATION_VERIFIED
    ];

    const RELATION_LEAD = 'lead';

    protected $table   = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * Defines the belongs to relationship between this model and the Lead model.
     *
     * @return BelongsTo
     */
    public function lead(): BelongsTo
    {
        return $this->belongsTo(EloquentQuote::class, self::FIELD_LEAD_ID, EloquentQuote::ID);
    }
}
