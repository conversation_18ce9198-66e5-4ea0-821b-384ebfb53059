<?php

namespace App\Models\ContactIdentification;

use App\Enums\ContactIdentification\IdentificationStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property int $relation_id
 * @property string $relation_type
 * @property string $identifier_field
 * @property string $identifier_value
 * @property string $identifier_field_type
 * @property string $identification_status
 * @property string $created_at
 * @property string $updated_at
 */
class IdentifiedContact extends Model
{
    use SoftDeletes;

    const TABLE = 'identified_contacts';

    const FIELD_ID                    = 'id';
    const FIELD_NOMINATED_CONTACT_ID  = 'nominated_contact_id';
    const FIELD_IDENTIFIER_VALUE      = 'identifier_value';
    const FIELD_IDENTIFIER_FIELD_TYPE = 'identifier_field_type';
    const FIELD_IDENTIFICATION_STATUS = 'identification_status';
    const FIELD_CREATED_AT            = 'created_at';
    const FIELD_UPDATED_AT            = 'updated_at';

    const RELATION_POSSIBLE_CONTACTS = 'possibleContacts';
    const RELATION_NOMINATED_CONTACT = 'nominatedContact';

    protected $guarded = [
        self::FIELD_ID
    ];

    public function isKnown(): bool
    {
        return $this->{IdentifiedContact::FIELD_IDENTIFICATION_STATUS} === IdentificationStatus::MULTIPLE_RESULTS->value
            || $this->{IdentifiedContact::FIELD_IDENTIFICATION_STATUS} === IdentificationStatus::SINGLE_RESULT->value;
    }

    /**
     * @return HasMany
     */
    public function possibleContacts(): HasMany
    {
        return $this->hasMany(PossibleContact::class, PossibleContact::FIELD_IDENTIFIED_CONTACT_ID, self::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function nominatedContact(): HasOne
    {
        return $this->hasOne(PossibleContact::class, PossibleContact::FIELD_ID, self::FIELD_NOMINATED_CONTACT_ID);
    }
}
