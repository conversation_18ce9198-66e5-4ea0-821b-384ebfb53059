<?php

namespace App\Models\RecycledLeads;

use App\Models\BaseModel;
use App\Models\Odin\ConsumerProduct;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $consumer_product_id
 * @property array|null $data
 * @property int $status
 *
 * @property-read ConsumerProduct $consumerProduct
 */
class RecycledLead extends BaseModel
{
    use HasFactory;

    const string TABLE = 'recycled_leads';

    const string FIELD_ID = 'id';
    const string FIELD_CONSUMER_PRODUCT_ID = 'consumer_product_id';
    const string FIELD_DATA = 'data';
    const string FIELD_STATUS = 'status';

    const string RELATION_CONSUMER_PRODUCT = 'consumerProduct';

    const int STATUS_SOLD = 1;
    const int STATUS_CANCELED = -1;

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];
    protected $casts = [
        self::FIELD_DATA => 'array'
    ];

    /**
     * @return BelongsTo
     */
    public function consumerProduct(): BelongsTo
    {
        return $this->belongsTo(ConsumerProduct::class);
    }
}
