<?php

namespace App\Models\RecycledLeads;

use App\Models\BaseModel;
use App\Models\Odin\ConsumerProduct;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $consumer_product_id
 * @property float|null $possible_revenue
 * @property Carbon|null $skipped_at
 * @property int $skip_count
 * @property array|null $skip_reasons
 * @property int $recency_points
 *
 * @property-read ConsumerProduct $consumerProduct
 */
class LeadProcessingAged extends BaseModel
{
    use HasFactory;

    const string TABLE = 'lead_processing_aged';

    const string FIELD_ID = 'id';
    const string FIELD_CONSUMER_PRODUCT_ID = 'consumer_product_id';
    const string FIELD_POSSIBLE_REVENUE = 'possible_revenue';
    const string FIELD_SKIPPED_AT = 'skipped_at';
    const string FIELD_SKIP_COUNT = 'skip_count';
    const string FIELD_SKIP_REASONS = 'skip_reasons';
    const string FIELD_RECENCY_POINTS = 'recency_points';

    const string RELATION_CONSUMER_PRODUCT = 'consumerProduct';

    const string REASON_PROCESSOR = 'processor';
    const string REASON_TIMESTAMP = 'timestamp';
    const string REASON_REASON = 'reason';

    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_SKIPPED_AT => 'datetime',
        self::FIELD_SKIP_REASONS => 'array'
    ];

    /**
     * @return BelongsTo
     */
    public function consumerProduct(): BelongsTo
    {
        return $this->belongsTo(ConsumerProduct::class);
    }
}
