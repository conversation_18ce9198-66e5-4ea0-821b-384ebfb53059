<?php

namespace App\Models;

use App\DTO\EmailTemplate\EmailTemplatePayloadDTO;
use App\Enums\EmailTemplateScope;
use App\Enums\EmailTemplateType;
use App\Models\Odin\Industry;
use Carbon\Carbon;
use Google\Protobuf\Internal\FieldDescriptorProto_Type;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;

/**
 * @property int $id
 * @property int $owner_user_id
 * @property int $background_id
 * @property int $industry_id
 * @property string $name
 * @property string $subject
 * @property string $content
 * @property boolean personal
 * @property boolean $default_lead_delivery_template
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $deleted_at
 * @property string $engine
 * @property EmailTemplatePayloadDTO $payload
 *
 * @property-read User $owner
 * @property-read Industry $industry
 * @property-read EmailTemplateBackground $background
 */
class EmailTemplate extends Model
{
    use HasFactory;
    use SoftDeletes;

    const string TABLE = 'email_templates';

    const string FIELD_ID                             = 'id';
    const string FIELD_OWNER_USER_ID                  = 'owner_user_id';
    const string FIELD_BACKGROUND_ID                  = 'background_id';
    const string FIELD_INDUSTRY_ID                    = 'industry_id';
    const string FIELD_NAME                           = 'name';
    const string FIELD_SUBJECT                        = 'subject';
    const string FIELD_CONTENT                        = 'content';
    const string FIELD_TYPE                           = 'type';
    const string FIELD_ACTIVE                         = 'active';
    const string FIELD_PERSONAL                       = 'personal';
    const string FIELD_ENGINE                         = 'engine';
    const string FIELD_PAYLOAD                        = 'payload';
    const string FIELD_DEFAULT_LEAD_DELIVERY_TEMPLATE = 'default_lead_delivery_template';
    const string FIELD_DELETED_AT                     = 'deleted_at';
    const string FIELD_CREATED_AT                     = 'created_at';
    const string FIELD_UPDATED_AT                     = 'updated_at';

    const string RELATION_OWNER      = 'owner';
    const string RELATION_INDUSTRY   = 'industry';
    const string RELATION_BACKGROUND = 'background';

    protected $table = self::TABLE;

    protected $fillable = [
        self::FIELD_OWNER_USER_ID,
        self::FIELD_NAME,
        self::FIELD_SUBJECT,
        self::FIELD_CONTENT,
        self::FIELD_PERSONAL,
        self::FIELD_TYPE,
        self::FIELD_INDUSTRY_ID,
        self::FIELD_BACKGROUND_ID,
        self::FIELD_ENGINE,
        self::FIELD_ACTIVE,
    ];

    protected $casts = [
        self::FIELD_TYPE    => EmailTemplateType::class,
        self::FIELD_PAYLOAD => EmailTemplatePayloadDTO::class,
    ];

    /**
     * @return BelongsTo
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_OWNER_USER_ID, User::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function industry(): BelongsTo
    {
        return $this->belongsTo(Industry::class, self::FIELD_INDUSTRY_ID, Industry::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function background(): HasOne
    {
        return $this->hasOne(EmailTemplateBackground::class, EmailTemplateBackground::FIELD_ID, self::FIELD_BACKGROUND_ID);
    }

    public function scopeByScope($query, ?EmailTemplateScope $scope)
    {
        if ($scope === EmailTemplateScope::PERSONAL) {
            return $query->where(EmailTemplate::FIELD_OWNER_USER_ID, Auth::id())
                ->where(EmailTemplate::FIELD_PERSONAL, true);
        } else {
            return $query->where(function ($query) {
                $query->where(EmailTemplate::FIELD_OWNER_USER_ID, Auth::id())
                    ->orWhere(EmailTemplate::FIELD_PERSONAL, false);
            });
        }
    }
}
