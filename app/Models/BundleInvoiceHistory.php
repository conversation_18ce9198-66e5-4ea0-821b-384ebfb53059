<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class BundleInvoiceHistory
 *
 * @package App\Models
 *
 * @property integer $id
 * @property integer $bundle_invoice_id
 * @property string $description
 * @property string $note
 * @property-read BundleInvoice $invoice
 */
class BundleInvoiceHistory extends Model
{
    use HasFactory;

    const TABLE = 'bundle_invoice_history';
    const FIELD_ID = 'id';
    const FIELD_BUNDLE_INVOICE_ID = 'bundle_invoice_id';
    const FIELD_DESCRIPTION = 'description';
    const FIELD_NOTE = 'note';

    const RELATION_INVOICE = 'invoice';

    protected $guarded = [ self::FIELD_ID ];

    protected $table = self::TABLE;

    /**
     * @return BelongsTo
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(BundleInvoice::class, self::FIELD_BUNDLE_INVOICE_ID, BundleInvoice::FIELD_ID);
    }
}
