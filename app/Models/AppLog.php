<?php

namespace App\Models;

use App\Enums\Log\LogLevel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int $id
 * @property string $message
 * @property string $namespace
 * @property string $feature
 * @property LogLevel $level
 * @property string $stack_trace
 * @property number $line
 * @property string $file
 * @property array $context
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class AppLog extends BaseModel
{
    use HasFactory;

    const string TABLE = 'app_logs';

    const string FIELD_ID           = 'id';
    const string FIELD_MESSAGE      = 'message';
    const string FIELD_FUNCTION     = 'function';
    const string FIELD_FEATURE      = 'feature';
    const string FIELD_LEVEL        = 'level';
    const string FIELD_STACK_TRACE  = 'stack_trace';
    const string FIELD_CONTEXT      = 'context';
    const string FIELD_LINE         = 'line';
    const string FIELD_FILE         = 'file';
    const string FIELD_CREATED_AT   = 'created_at';
    const string FIELD_UPDATED_AT   = 'updated_at';
    const string RELATION_RELATIONS = 'relations';

    protected $guarded = [
        self::FIELD_ID,
    ];

    protected $casts = [
        self::FIELD_CONTEXT => 'array',
        self::FIELD_LEVEL   => LogLevel::class,
    ];

    /**
     * @return HasMany
     */
    public function relations(): HasMany
    {
        return $this->hasMany(AppLogRelation::class);
    }
}
