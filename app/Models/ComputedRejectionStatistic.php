<?php

namespace App\Models;

use App\Models\Odin\Company;
use App\Models\Odin\Product;
use App\Observers\ComputedRejectionStatisticObserver;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $company_id
 * @property int $legacy_company_id
 * @property int $product_id
 * @property double $manual_rejection_percentage
 * @property double $crm_rejection_percentage
 * @property double $overall_rejection_percentage
 * @property int $assigned_product_cost
 * @property int $manual_rejected_product_cost
 * @property int $crm_rejected_product_cost
 * @property Carbon $crm_reset_time
 *
 * @property-read Company $company
 */

#[ObservedBy(ComputedRejectionStatisticObserver::class)]
class ComputedRejectionStatistic extends Model
{
    use HasFactory;
    const TABLE = 'computed_rejection_statistics';

    const FIELD_ID                           = 'id';
    const FIELD_COMPANY_ID                   = 'company_id';
    const FIELD_LEGACY_COMPANY_ID            = 'legacy_company_id';
    const FIELD_PRODUCT_ID                   = 'product_id';
    const FIELD_MANUAL_REJECTION_PERCENTAGE  = 'manual_rejection_percentage';
    const FIELD_CRM_REJECTION_PERCENTAGE     = 'crm_rejection_percentage';
    const FIELD_OVERALL_REJECTION_PERCENTAGE = 'overall_rejection_percentage';
    const FIELD_ASSIGNED_PRODUCT_COST        = 'assigned_product_cost';
    const FIELD_MANUAL_REJECTED_PRODUCT_COST = 'manual_rejected_product_cost';
    const FIELD_CRM_REJECTED_PRODUCT_COST    = 'crm_rejected_product_cost';
    const FIELD_CRM_RESET_TIME               = 'crm_reset_time';

    const ACTIVE_REJECTION_FIELD_IMPACTING_ELIGIBILITY = self::FIELD_CRM_REJECTION_PERCENTAGE;
    const ACTIVE_REJECTION_FIELD_IMPACTING_BID         = self::FIELD_OVERALL_REJECTION_PERCENTAGE;

    const REJECTION_PERCENTAGE_IMPACTING_ELIGIBILITY = 'rejection_percentage_impacting_eligibility';
    const REJECTION_PERCENTAGE_IMPACTING_BID         = 'rejection_percentage_impacting_bid';

    const string RELATION_PRODUCT = 'product';
    const string RELATION_COMPANY = 'company';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $casts = [
        self::FIELD_CRM_RESET_TIME => 'datetime',
    ];

    /**
     * @return BelongsTo
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, self::FIELD_PRODUCT_ID, Product::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }
}
