<?php

namespace App\Models;

use App\Models\Odin\Company;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;

/**
 * @property int      $id
 * @property int      $company_id
 * @property int      $user_id
 * @property string   $item_type
 * @property int      $item_id
 * @property Carbon   $created_at
 * @property Carbon   $updated_at
 * @property Carbon   $deleted_at
 * @property null|int $company_cadence_group_action_id
 *
 * @property-read Company    $company
 * @property-read User       $user
 * @property-read Collection $item
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\ActivityConversation> $conversations
 * @property-read int|null $conversations_count
 * @mixin Builder
 */
class ActivityFeed extends BaseModel
{
    use HasFactory;
    use SoftDeletes;

    const TABLE = "activity_feeds";

    const FIELD_ID                              = 'id';
    const FIELD_COMPANY_ID                      = 'company_id';
    const FIELD_USER_ID                         = 'user_id';
    const FIELD_ITEM_TYPE                       = 'item_type';
    const FIELD_ITEM_ID                         = 'item_id';
    const FIELD_CREATED_AT                      = 'created_at';
    const FIELD_UPDATED_AT                      = 'updated_at';
    const FIELD_DELETED_AT                      = 'deleted_at';
    const FIELD_COMPANY_CADENCE_GROUP_ACTION_ID = 'company_cadence_group_action_id';

    const RELATION_COMPANY        = 'company';
    const RELATION_USER           = 'user';
    const RELATION_ACTIVITY_ITEM  = 'item';
    const RELATION_CONVERSATIONS  = 'conversations';

    protected $table   = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }

    /**
     * Get the parent ActivityFeedItem model eg. email, text, call
     * @return MorphTo
     */
    public function item(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * All ActivityConversations connected to this activity (not direct children)
     *
     * @return HasMany
     */
    public function conversations(): HasMany
    {
        return $this->hasMany(ActivityConversation::class, ActivityConversation::FIELD_ACTIVITY_ID, self::FIELD_ID);
    }

}
