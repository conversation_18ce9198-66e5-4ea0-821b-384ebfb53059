<?php

namespace App\Models;

use App\Models\Legacy\EloquentQuote;
use App\Models\Odin\ConsumerProduct;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class LeadProcessingUnderReview
 *
 * @package App
 *
 * @property int                $id
 * @property int                $lead_processor_id
 * @property int                $lead_id
 * @property int                $location_id
 * @property int                $consumer_product_id
 * @property string             $reason
 * @property string             $existing_reason
 * @property float              $possible_revenue
 * @property Carbon             $checked_unverified_budget_at
 *
 * @property-read LeadProcessor $leadProcessor
 * @property-read EloquentQuote $lead
 * @property-read ConsumerProduct $consumerProduct
 */
class LeadProcessingUnderReview extends BaseModel
{
    const string TABLE = 'lead_processing_under_reviews';

    const string FIELD_ID                           = 'id';
    const string FIELD_LEAD_ID                      = 'lead_id';
    const string FIELD_USER_ID                      = 'lead_processor_id';
    const string FIELD_REASON                       = 'reason';
    const string FIELD_EXISTING_REASON              = 'existing_reason';
    const string FIELD_LOCATION_ID                  = 'location_id';
    const string FIELD_POSSIBLE_REVENUE             = 'possible_revenue';
    const string FIELD_CHECKED_UNVERIFIED_BUDGET_AT = 'checked_unverified_budget_at';
    const string FIELD_CONSUMER_PRODUCT_ID          = 'consumer_product_id';

    const RELATION_LEAD_PROCESSOR   = 'leadProcessor';
    const RELATION_LEAD             = 'lead';
    const RELATION_CONSUMER_PRODUCT = 'consumerProduct';
    const RELATION_USER             = 'user';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function leadProcessor(): BelongsTo
    {
        return $this->belongsTo(LeadProcessor::class, self::FIELD_USER_ID, LeadProcessor::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function lead(): BelongsTo
    {
        return $this->belongsTo(EloquentQuote::class, self::FIELD_LEAD_ID, EloquentQuote::ID);
    }

    /**
     * @return BelongsTo
     */
    public function consumerProduct(): BelongsTo
    {
        return $this->belongsTo(ConsumerProduct::class, self::FIELD_CONSUMER_PRODUCT_ID, ConsumerProduct::FIELD_ID);
    }
}
