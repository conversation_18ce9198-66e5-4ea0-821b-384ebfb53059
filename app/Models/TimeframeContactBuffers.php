<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $timeframe_id
 * @property int $contact_attempt_buffer_hrs
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read LeadProcessingTimeframe $timeframe
 */
class TimeframeContactBuffers extends BaseModel
{
    const TABLE = 'timeframe_contact_buffers';

    const FIELD_TIMEFRAME_ID               = 'timeframe_id';
    const FIELD_CONTACT_ATTEMPT_BUFFER_HRS = 'contact_attempt_buffer_hrs';
    const FIELD_CREATED_AT                 = 'created_at';
    const FIELD_UPDATED_AT                 = 'updated_at';

    const RELATION_TIMEFRAME = 'timeframe';

    protected $table      = self::TABLE;
    protected $primaryKey = self::FIELD_TIMEFRAME_ID;

    /**
     * @return BelongsTo
     */
    public function timeframe(): BelongsTo
    {
        return $this->belongsTo(LeadProcessingTimeframe::class, self::FIELD_TIMEFRAME_ID, LeadProcessingTimeframe::FIELD_ID);
    }
}
