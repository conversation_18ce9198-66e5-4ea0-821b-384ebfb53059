<?php

namespace App\Models\Calendar;

use App\Enums\Calendar\CalendarProviderType;
use App\Models\BaseModel;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property int $user_id
 * @property string $expires_at
 * @property string $external_resource_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read User $user
 */
class UserCalendarListener extends BaseModel
{
    use SoftDeletes;

    const string TABLE = 'user_calendar_listeners';

    const string FIELD_ID                   = 'id';
    const string FIELD_USER_ID              = 'user_id';
    const string FIELD_EXPIRES_AT           = 'expires_at';
    const string FIELD_EXTERNAL_RESOURCE_ID = 'external_resource_id';
    const string FIELD_CREATED_AT           = 'created_at';
    const string FIELD_UPDATED_AT           = 'updated_at';

    const string RELATION_USER = 'user';

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $table = self::TABLE;

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }
}
