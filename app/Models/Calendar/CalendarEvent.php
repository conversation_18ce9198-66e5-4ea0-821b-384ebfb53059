<?php

namespace App\Models\Calendar;

use App\Enums\Calendar\CalendarEventStatus;
use App\Models\BaseModel;
use App\Models\Conference\Conference;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * @property int $id
 *
 * @property int $calendar_id
 * @property int $external_id
 * @property string $title
 * @property string $description
 * @property array $payload
 * @property string $location
 * @property CalendarEventStatus $status
 * @property Carbon $start_time
 * @property Carbon $end_time
 *
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $deleted_at
 *
 * @property-read Calendar $calendar
 * @property-read Demo $demo
 */
class CalendarEvent extends BaseModel
{
    use SoftDeletes, LogsActivity, HasFactory;

    const string TABLE = 'calendar_events';

    const string FIELD_ID = 'id';

    const string FIELD_CALENDAR_ID                  = 'calendar_id';
    const string FIELD_USER_ID                      = 'user_id';
    const string FIELD_EXTERNAL_ID                  = 'external_id';
    const string FIELD_TITLE                        = 'title';
    const string FIELD_PAYLOAD                      = 'payload';
    const string FIELD_DESCRIPTION                  = 'description';
    const string FIELD_LOCATION                     = 'location';
    const string FIELD_STATUS                       = 'status';
    const string FIELD_CONFERENCE_URL               = 'conference_url';
    const string FIELD_LAST_CONFERENCE_DATA_SYNC_AT = 'last_conference_data_sync_at';
    const string FIELD_TIMEZONE                     = 'timezone';
    const string FIELD_START_TIME                   = 'start_time';
    const string FIELD_END_TIME                     = 'end_time';
    const string FIELD_RECURRENCE_RULE              = 'recurrence_rule';
    const string FIELD_RECURRENCE_DATA              = 'recurrence_data';

    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';
    const string FIELD_DELETED_AT = 'deleted_at';

    const string RELATION_CONFERENCES = 'conferences';
    const string RELATION_ATTENDEES   = 'attendees';
    const string RELATION_DEMO        = 'demo';
    const string RELATION_USER        = 'user';

    protected $casts = [
        self::FIELD_STATUS                       => CalendarEventStatus::class,
        self::FIELD_PAYLOAD                      => 'array',
        self::FIELD_START_TIME                   => 'datetime',
        self::FIELD_END_TIME                     => 'datetime',
        self::FIELD_LAST_CONFERENCE_DATA_SYNC_AT => 'datetime',
    ];

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $table = self::TABLE;

    /**
     * @return BelongsTo
     */
    public function calendar(): BelongsTo
    {
        return $this->belongsTo(Calendar::class, self::FIELD_CALENDAR_ID, Calendar::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function demo(): HasOne
    {
        return $this->hasOne(Demo::class, Demo::FIELD_CALENDAR_EVENT_ID, self::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function attendees(): HasMany
    {
        return $this->hasMany(CalendarEventAttendee::class, CalendarEventAttendee::FIELD_CALENDAR_EVENT_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function conferences(): HasMany
    {
        return $this->hasMany(Conference::class, Conference::FIELD_CALENDAR_EVENT_ID, self::FIELD_ID);
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->useLogName(self::TABLE)
            ->logAll()
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * @return string|null
     */
    public function getConferenceCode(): ?string
    {
        return Str::of($this->{self::FIELD_CONFERENCE_URL})
            ->explode('/')
            ->last();
    }
}
