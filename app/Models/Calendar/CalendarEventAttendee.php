<?php

namespace App\Models\Calendar;

use App\Models\BaseModel;
use App\Models\ContactIdentification\IdentifiedContact;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spa<PERSON>\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * @property int $id
 *
 * @property int $calendar_event_id
 * @property string $name
 * @property string $email
 * @property string $status
 * @property int $identified_contact_id
 * @property string $relation_type
 * @property string $relation_id
 *
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $deleted_at
 *
 * @property-read Calendar $calendar
 */
class CalendarEventAttendee extends BaseModel
{
    use SoftDeletes, LogsActivity;

    const string TABLE = 'calendar_event_attendees';

    const string FIELD_ID = 'id';

    const string FIELD_CALENDAR_EVENT_ID     = 'calendar_event_id';
    const string FIELD_NAME                  = 'name';
    const string FIELD_EMAIL                 = 'email';
    const string FIELD_STATUS                = 'status';
    const string FIELD_IDENTIFIED_CONTACT_ID = 'identified_contact_id';
    const string FIELD_RELATION_TYPE         = 'relation_type';
    const string FIELD_RELATION_ID           = 'relation_id';
    const string FIELD_IS_ORGANIZER          = 'is_organizer';

    const string RELATION_IDENTIFIED_CONTACT = 'identifiedContact';
    const string RELATION_CALENDAR_EVENT     = 'calendarEvent';

    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';
    const string FIELD_DELETED_AT = 'deleted_at';

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $table = self::TABLE;

    /**
     * @return BelongsTo
     */
    public function calendarEvent(): BelongsTo
    {
        return $this->belongsTo(CalendarEvent::class, self::FIELD_CALENDAR_EVENT_ID, CalendarEvent::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function identifiedContact(): BelongsTo
    {
        return $this->belongsTo(IdentifiedContact::class, self::FIELD_IDENTIFIED_CONTACT_ID, IdentifiedContact::FIELD_ID);
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->useLogName(self::TABLE)
            ->logAll()
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
}
