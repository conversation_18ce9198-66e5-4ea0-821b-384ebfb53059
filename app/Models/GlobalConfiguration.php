<?php

namespace App\Models;

use App\Database\Casts\AsConfigurableFieldPayload;
use App\DataModels\Odin\ConfigurableFieldDataModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $configuration_key
 * @property ConfigurableFieldDataModel $configuration_payload
 *
 */
class GlobalConfiguration extends Model
{
    use SoftDeletes, HasFactory;

    const FIELD_ID          = 'id';
    const FIELD_CONFIGURATION_KEY        = 'configuration_key';

    const FIELD_CONFIGURATION_PAYLOAD  = 'configuration_payload';

    const FIELD_UPDATED_BY_ID = 'updated_by_id';

    const FIELD_CREATED_BY_ID = 'created_by_id';

    const FIELD_CREATED_AT = 'created_at';
    const FIELD_UPDATE_AT = 'updated_at';

    const FIELD_DELETED_AT = 'deleted_at';

    const TABLE = 'global_configurations';

    protected $casts = [
        self::FIELD_CONFIGURATION_PAYLOAD => AsConfigurableFieldPayload::class
    ];

    protected $table = self::TABLE;
    protected $guarded = [ self::FIELD_ID ];

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_UPDATED_BY_ID, User::FIELD_ID);
    }
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_CREATED_BY_ID, User::FIELD_ID);
    }
}
