<?php

namespace App\Models;

use App\Models\Legacy\EloquentQuote;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class LeadProcessingHistory
 *
 * @package App\LeadProcessing\Models
 * @property int                $id
 * @property int                $lead_id
 * @property int                $lead_processor_id
 * @property int                $queue_configuration_id
 * @property int                $consumer_product_id
 * @property string             $action
 * @property Carbon             $created_at
 * @property Carbon             $updated_at
 *
 * @property-read EloquentQuote $lead
 * @property-read LeadProcessor $leadProcessor
 * @property-read LeadProcessingQueueConfiguration $queueConfiguration
 */
class LeadProcessingHistory extends BaseModel
{
    const TABLE = 'lead_processing_history';

    const FIELD_ID                     = 'id';
    const FIELD_LEAD_PROCESSOR_ID      = 'lead_processor_id';
    const FIELD_QUEUE_CONFIGURATION_ID = 'queue_configuration_id';
    const FIELD_ACTION                 = 'action';
    const FIELD_CONSUMER_PRODUCT_ID = 'consumer_product_id';
    /** @deprecated  */
    const FIELD_LEAD_ID                = 'lead_id';


    const ACTION_CANCELLED = 'Cancelled';
    const ACTION_ALLOCATED = 'Allocated';
    const ACTION_UNDER_REVIEW = 'Marked Under Review';
    const ACTION_PENDING_REVIEW = 'Marked Pending Review';
    const ACTION_SYSTEM_MOVED_PR_UR = 'System Moved from PR to UR';

    const ACTIONS
        = [
            self::ACTION_ALLOCATED,
            self::ACTION_CANCELLED,
            self::ACTION_PENDING_REVIEW,
            self::ACTION_UNDER_REVIEW,
            self::ACTION_SYSTEM_MOVED_PR_UR
        ];

    const RELATION_LEAD                = 'lead';
    const RELATION_LEAD_PROCESSOR      = 'leadProcessor';
    const RELATION_QUEUE_CONFIGURATION = 'queueConfiguration';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function lead(): BelongsTo
    {
        return $this->belongsTo(EloquentQuote::class, self::FIELD_LEAD_ID, EloquentQuote::ID);
    }

    /**
     * @return BelongsTo
     */
    public function leadProcessor(): BelongsTo
    {
        return $this->belongsTo(LeadProcessor::class, self::FIELD_LEAD_PROCESSOR_ID, LeadProcessor::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function queueConfiguration(): BelongsTo
    {
        return $this->belongsTo(LeadProcessingQueueConfiguration::class, self::FIELD_QUEUE_CONFIGURATION_ID, LeadProcessingQueueConfiguration::FIELD_ID);
    }
}
