<?php

namespace App\Models;

use App\Models\Legacy\EloquentQuote;
use App\Models\Odin\ConsumerProduct;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class LeadProcessingInitial
 *
 * @package App
 *
 * @property int $id
 * @property int $lead_processor_id
 * @property int $consumer_product_id
 * @property Carbon $created_at
 *
 * @property-read ConsumerProduct $consumerProduct
 */
class LeadProcessingInitial extends BaseModel
{
    const string TABLE = 'lead_processing_initials';

    const string FIELD_ID                  = 'id';
    const string FIELD_CONSUMER_PRODUCT_ID = 'consumer_product_id';
    const string RELATION_CONSUMER_PRODUCT = 'consumerProduct';

    /* @deprecated */
    const FIELD_LEAD_ID             = 'lead_id';
    /* @deprecated */
    const RELATION_LEAD = 'lead';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @deprecated
     * @return BelongsTo
     */
    public function lead(): BelongsTo
    {
        return $this->belongsTo(EloquentQuote::class, self::FIELD_LEAD_ID, EloquentQuote::ID);
    }

    /**
     * @return BelongsTo
     */
    public function consumerProduct(): BelongsTo
    {
        return $this->belongsTo(ConsumerProduct::class, self::FIELD_CONSUMER_PRODUCT_ID, ConsumerProduct::FIELD_ID);
    }
}
