<?php

namespace App\Models;

use App\Enums\LeadRefundItemChargeRefundStatus;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LeadRefundItemRefund extends BaseModel
{
    const string TABLE    = 'lead_refund_item_refunds';
    const string FIELD_ID = 'id';

    const string FIELD_LEAD_REFUND_ITEM_ID  = 'lead_refund_item_id';
    const string FIELD_LEGACY_INVOICE_ID    = 'legacy_invoice_id';
    const string FIELD_LEGACY_CHARGE_ID     = 'legacy_charge_id';
    const string FIELD_LEAD_REFUND_ID       = 'lead_refund_id';
    const string FIELD_EXTERNAL_CHARGE_ID   = 'external_charge_id';
    const string FIELD_EXTERNAL_REFUND_ID   = 'external_refund_id';
    const string FIELD_INTERNAL_REFUND_UUID = 'internal_refund_uuid';
    const string FIELD_STATUS               = 'status';
    const string FIELD_ERROR_MESSAGE        = 'error_message';


    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';

    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_STATUS => LeadRefundItemChargeRefundStatus::class
    ];

    /**
     * @return BelongsTo
     */
    public function item(): BelongsTo
    {
        return $this->belongsTo(LeadRefundItem::class, self::FIELD_LEAD_REFUND_ITEM_ID, LeadRefundItem::FIELD_ID);
    }
}
