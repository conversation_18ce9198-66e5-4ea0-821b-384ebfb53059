<?php

namespace App\Models;

use App\Models\Locations\USLocation;
use App\Models\Odin\Company;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $company_location_id
 * @property int $location_id
 * @property int $radius
 *
 * @property-read ?Company $company
 * @property-read ?USLocation $location
 */
class ServiceRadius extends BaseModel
{
    const string TABLE = 'service_radii';

    const string FIELD_ID          = 'id';
    const string FIELD_COMPANY_ID  = 'company_id';
    const string FIELD_LOCATION_ID = 'location_id';
    const string FIELD_RADIUS      = 'radius';

    const string RELATION_COMPANY  = 'company';
    const string RELATION_LOCATION = 'location';

    const int DEFAULT_RADIUS = 50;

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo|null
     */
    public function company(): ?BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }

    /**
     * @return BelongsTo|null
     */
    public function location(): ?BelongsTo
    {
        return $this->belongsTo(USLocation::class, self::FIELD_LOCATION_ID, USLocation::FIELD_ID);
    }
}