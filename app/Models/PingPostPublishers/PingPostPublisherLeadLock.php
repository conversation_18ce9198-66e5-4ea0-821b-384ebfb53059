<?php

namespace App\Models\PingPostPublishers;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property int $consumer_product_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class PingPostPublisherLeadLock extends Model
{
    const string TABLE = 'ping_post_publisher_lead_locks';

    const string FIELD_ID = 'id';
    const string FIELD_CONSUMER_PRODUCT_ID = 'consumer_product_id';
    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID,
    ];
}
