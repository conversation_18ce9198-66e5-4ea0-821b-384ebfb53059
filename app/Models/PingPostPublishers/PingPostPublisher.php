<?php

namespace App\Models\PingPostPublishers;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property string $key
 * @property string $name
 * @property int $company_id
 * @property bool $active
 * @property string $ping_post_url
 * @property array $data
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class PingPostPublisher extends Model
{
    use SoftDeletes;

    const string TABLE = 'ping_post_publishers';

    const string FIELD_ID = 'id';
    const string FIELD_KEY = 'key';
    const string FIELD_NAME = 'name';
    const string FIELD_COMPANY_ID = 'company_id';
    const string FIELD_ACTIVE = 'active';
    const string FIELD_PING_URL = 'ping_url';
    const string FIELD_POST_URL = 'post_url';
    const string FIELD_DATA = 'data';
    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';
    const string FIELD_DELETED_AT = 'deleted_at';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID,
    ];

    protected $casts = [
        self::FIELD_DATA => 'array',
    ];
}
