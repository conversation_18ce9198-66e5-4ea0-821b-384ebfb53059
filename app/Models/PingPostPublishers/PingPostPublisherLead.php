<?php

namespace App\Models\PingPostPublishers;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property int $consumer_product_id
 * @property int $ping_post_publisher_id
 * @property int $product_assignment_id
 * @property int $status
 * @property float $cost
 * @property array $data
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class PingPostPublisherLead extends Model
{
    const string TABLE = 'ping_post_publisher_leads';

    const string FIELD_ID = 'id';
    const string FIELD_CONSUMER_PRODUCT_ID = 'consumer_product_id';
    const string FIELD_PRODUCT_ASSIGNMENT_ID = 'product_assignment_id';
    const string FIELD_PING_POST_PUBLISHER_ID = 'ping_post_publisher_id';
    const string FIELD_STATUS = 'status';
    const string FIELD_COST = 'cost';
    const string FIELD_DATA = 'data';
    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';

    const int STATUS_CHARGEABLE = 0;

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID,
    ];

    protected $casts = [
        self::FIELD_DATA => 'array',
    ];
}
