<?php

namespace App\Models\PingPostPublishers;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property int $ping_post_publisher_id
 * @property int $ping_post_publisher_api_key_id
 * @property string $request
 * @property string $response
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class PingPostPublisherLog extends Model
{
    const string TABLE = 'ping_post_publisher_logs';

    const string FIELD_ID = 'id';
    const string FIELD_CONSUMER_PRODUCT_ID = 'consumer_product_id';
    const string FIELD_PING_POST_PUBLISHER_ID = 'ping_post_publisher_id';
    const string FIELD_TYPE = 'type';
    const string FIELD_LOG = 'log';
    const string FIELD_BID = 'bid';
    const string FIELD_DATA = 'data';
    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';

    const int TYPE_LOG = 0;
    const int TYPE_ERROR = 1;
    const int TYPE_INITIAL = 3;
    const int TYPE_WOULD_HAVE_SOLD = 4;
    const int TYPE_LOCK = 5;

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID,
    ];

    protected $casts = [
        self::FIELD_DATA => 'array',
    ];
}
