<?php

namespace App\Models\PingPostPublishers;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property int $ping_post_publisher_id
 * @property string $key
 * @property string $type
 * @property string $status
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class PingPostPublisherApiKey extends Model
{
    use SoftDeletes;

    const string TABLE = 'ping_post_publisher_api_keys';

    const string FIELD_ID = 'id';
    const string FIELD_PING_POST_PUBLISHER_ID = 'ping_post_publisher_id';
    const string FIELD_KEY = 'key';
    const string FIELD_TYPE = 'type';
    const string FIELD_ACTIVE = 'active';
    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';
    const string FIELD_DELETED_AT = 'deleted_at';

    const string TYPE_MAIN = 'main';
    const string TYPE_TEST = 'test';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID,
    ];
}
