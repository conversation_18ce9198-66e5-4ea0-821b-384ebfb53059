<?php

namespace App\Models\PingPostPublishers;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property int $ping_post_publisher_id
 * @property int $ping_post_publisher_api_key_id
 * @property int $consumer_product_id
 * @property string $destination
 * @property array $headers
 * @property array $request
 * @property bool $processed
 * @property array $response
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class PingPostPublisherRequest extends Model
{
    const string TABLE = 'ping_post_publisher_requests';

    const string FIELD_ID = 'id';
    const string FIELD_PING_POST_PUBLISHER_ID = 'ping_post_publisher_id';
    const string FIELD_PING_POST_PUBLISHER_API_KEY_ID = 'ping_post_publisher_api_key_id';
    const string FIELD_CONSUMER_PRODUCT_ID = 'consumer_product_id';
    const string FIELD_DESTINATION = 'destination';
    const string FIELD_HEADERS = 'headers';
    const string FIELD_REQUEST = 'request';
    const string FIELD_PROCESSED = 'processed';
    const string FIELD_RESPONSE = 'response';
    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID,
    ];

    protected $casts = [
        self::FIELD_REQUEST => 'array',
        self::FIELD_RESPONSE => 'array',
        self::FIELD_HEADERS => 'array',
    ];
}
