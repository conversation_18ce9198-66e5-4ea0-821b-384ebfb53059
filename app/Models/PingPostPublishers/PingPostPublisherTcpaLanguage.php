<?php

namespace App\Models\PingPostPublishers;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property int $industry_service_id
 * @property int $ping_post_publisher_id
 * @property string $tcpa_text
 * @property array $data
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class PingPostPublisherTcpaLanguage extends Model
{
    const string TABLE = 'ping_post_publisher_tcpa_languages';

    const string FIELD_ID = 'id';
    const string FIELD_INDUSTRY_SERVICE_ID = 'industry_service_id';
    const string FIELD_PING_POST_PUBLISHER_ID = 'ping_post_publisher_id';
    const string FIELD_TCPA_TEXT = 'tcpa_text';
    const string FIELD_DATA = 'data';
    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID,
    ];

    protected $casts = [
        self::FIELD_DATA => 'array',
    ];
}
