<?php

namespace App\Models;

use App\Models\Odin\Company;
use App\Models\Odin\Industry;
use Carbon\Carbon;
use Database\Factories\Odin\LicenseFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @property int $id
 * @property string $name
 * @property int $company_id
 * @property int $industry_id
 * @property Carbon $issued_at
 * @property Carbon $expires_at
 * @property string $url
 * @property string $country
 * @property string $state
 * @property string $license_number
 * @property string $type
 *
 * @property-read Company $company
 * @property-read Industry $industry
 */
class License extends BaseModel
{
    use HasFactory;

    const TABLE                 = 'licenses';

    const FIELD_ID              = 'id';
    const FIELD_COMPANY_ID      = 'company_id';
    const FIELD_INDUSTRY_ID     = 'industry_id';
    const FIELD_ISSUED_AT       = 'issued_at';
    const FIELD_EXPIRES_AT      = 'expires_at';
    const FIELD_NAME            = 'name';
    const FIELD_URL             = 'url';
    const FIELD_COUNTRY         = 'country';
    const FIELD_STATE           = 'state';
    const FIELD_LICENSE_NUMBER  = 'license_number';
    const FIELD_TYPE            = 'type';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_ISSUED_AT  => 'datetime',
        self::FIELD_EXPIRES_AT => 'datetime'
    ];

    protected static function newFactory(): LicenseFactory
    {
        return LicenseFactory::new();
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function industry(): HasOne
    {
        return $this->hasOne(Industry::class, Industry::FIELD_ID, self::FIELD_INDUSTRY_ID);
    }
}
