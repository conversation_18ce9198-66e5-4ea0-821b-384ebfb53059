<?php

namespace App\Models;

use App\Models\Legacy\EloquentQuote;
use App\Models\Odin\ConsumerProduct;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @property int                        $id
 * @property int                        $consumer_product_id
 * @property string                     $type
 * @property int                        $relation_id
 * @property int                        $lead_processor_id
 * @property bool                       $most_recent
 * @property Carbon                     $created_at
 * @property Carbon                     $updated_at
 *
 * @property-read ConsumerProduct       $consumerProduct
 * @property-read LeadProcessor         $leadProcessor
 * @property-read LeadProcessingHistory $leadProcessingHistory
 * @property-read Text                  $text
 * @property-read Call                  $call
 */
class LeadProcessingCommunication extends BaseModel
{
    use HasFactory;

    const string TABLE = 'lead_processing_communications';

    const string FIELD_ID                  = 'id';
    const string FIELD_CONSUMER_PRODUCT_ID = 'consumer_product_id';
    const string FIELD_TYPE                = 'type';
    const string FIELD_RELATION_ID         = 'relation_id';
    const string FIELD_LEAD_PROCESSOR_ID   = 'lead_processor_id';
    const string FIELD_MOST_RECENT         = 'most_recent';
    /** @deprecated   */
    const string FIELD_LEAD_ID           = 'lead_id';

    const string TYPE_SMS    = 'sms';
    const string TYPE_CALL   = 'call';
    const string TYPE_ACTION = 'action';

    /**
     * When no processor is assigned, the following ID will be used.
     */
    const int FALLBACK_PROCESSOR_ID = 0;

    const string RELATION_LEAD                    = 'lead';
    const string RELATION_LEAD_PROCESSOR          = 'leadProcessor';
    const string RELATION_LEAD_PROCESSING_HISTORY = 'leadProcessingHistory';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @deprecated
     */
    public function lead(): BelongsTo
    {
        return $this->belongsTo(EloquentQuote::class, self::FIELD_LEAD_ID, EloquentQuote::ID);
    }

    /**
     * @return BelongsTo
     */
    public function leadProcessor(): BelongsTo
    {
        return $this->belongsTo(LeadProcessor::class, self::FIELD_LEAD_PROCESSOR_ID, LeadProcessor::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function leadProcessingHistory(): HasOne
    {
        return $this->hasOne(LeadProcessingHistory::class, LeadProcessingHistory::FIELD_ID, self::FIELD_RELATION_ID);
    }

    /**
     * @return HasOne
     */
    public function call(): HasOne
    {
        return $this->hasOne(Call::class, Call::FIELD_ID, self::FIELD_RELATION_ID);
    }

    /**
     * @return HasOne
     */
    public function text(): HasOne
    {
        return $this->hasOne(Text::class, Text::FIELD_ID, self::FIELD_RELATION_ID);
    }
}
