<?php

namespace App\Models\SalesIntel;

use App\Models\Odin\Company;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CompanyImportRecord extends Model
{
    /** @use HasFactory<\Database\Factories\SalesIntel\CompanyImportRecordFactory> */
    use HasFactory;

    protected $table = 'sales_intel_company_import_records';

    protected $guarded = [
        'id',
    ];

    public function importable()
    {
        return $this->morphTo();
    }
}
