<?php

namespace App\Models\SalesIntel;

use App\Models\Odin\Company;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class FailedUserImportRecord extends Model
{
    /** @use HasFactory<\Database\Factories\SalesIntel\FailedUserImportRecordFactory> */
    use HasFactory;
    const string TABLE = 'sales_intel_failed_user_import_records';

    public $table = self::TABLE;

    public function company()
    {
        return $this->belongsTo(Company::class);
    }
}
