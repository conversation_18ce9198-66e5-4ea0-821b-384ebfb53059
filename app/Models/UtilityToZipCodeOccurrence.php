<?php

namespace App\Models;

use Carbon\Carbon;

/**
 * @property int $id
 * @property int $utility_id
 * @property int $location_id
 * @property int $lead_count
 */
class UtilityToZipCodeOccurrence extends BaseModel
{
    const TABLE = 'utility_to_zip_code_occurrences';

    const FIELD_ID          = 'id';
    const FIELD_UTILITY_ID  = 'utility_id';
    const FIELD_LOCATION_ID = 'location_id';
    const FIELD_LEAD_COUNT  = 'lead_count';

    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];
}
