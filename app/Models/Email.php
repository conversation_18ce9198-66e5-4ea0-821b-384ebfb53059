<?php

namespace App\Models;

use App\Models\Odin\CompanyUser;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphOne;

/**
 * @property int $id
 * @property string $to_address
 * @property string $from_address
 * @property string $subject
 * @property string $body
 * @property int $to_company_user_id
 * @property int $from_user_id
 * @property int $company_cadence_group_action_id
 *
 * @property-read CompanyUser $toCompanyUser
 * @property-read User $fromUser
 */
class Email extends BaseModel
{
    use HasFactory;

    const TABLE = 'emails';

    const FIELD_ID                              = 'id';
    const FIELD_TO_ADDRESS                      = 'to_address';
    const FIELD_FROM_ADDRESS                    = 'from_address';
    const FIELD_SUBJECT                         = 'subject';
    const FIELD_BODY                            = 'body';
    const FIELD_TO_COMPANY_USER_ID              = 'to_company_user_id';
    const FIELD_FROM_USER_ID                    = 'from_user_id';
    const FILED_COMPANY_CADENCE_GROUP_ACTION_ID = 'company_cadence_group_action_id';

    const RELATION_TO_COMPANY_USER = 'toCompanyUser';
    const RELATION_FROM_USER       = 'fromUser';

    protected $table   = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function toCompanyUser(): BelongsTo
    {
        return $this->belongsTo(CompanyUser::class, self::FIELD_TO_COMPANY_USER_ID);
    }

    /**
     * @return BelongsTo
     */
    public function fromUser(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_FROM_USER_ID);
    }

    /**
     * @return MorphOne
     */
    public function activity(): MorphOne
    {
        return $this->morphOne(ActivityFeed::class, 'item');
    }
}
