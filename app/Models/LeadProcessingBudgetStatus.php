<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property string $key
 * @property string $name
 * @property int $priority
 */
class LeadProcessingBudgetStatus extends BaseModel
{
    const TABLE = 'lead_processing_budget_statuses';

    const FIELD_ID       = 'id';
    const FIELD_KEY      = 'key';
    const FIELD_NAME     = 'name';
    const FIELD_PRIORITY = 'priority';

    const KEY_WITHIN_BUDGET = 'within_budget';
    const KEY_OVER_BUDGET   = 'over_budget';
    const KEY_NO_COMPANIES  = 'no_companies';

    protected $table = self::TABLE;
}
