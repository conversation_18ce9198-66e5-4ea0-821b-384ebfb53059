<?php

namespace App\Models;

use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Legacy\EloquentUser;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $sales_bait_id
 * @property int $relation_type
 * @property int $relation_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read SalesBaitLead $salesBait
 * @property-read EloquentCompanyContact $legacyContact
 * @property-read EloquentUser $legacyUser
 */
class SalesBaitRegisteredInterest extends BaseModel
{
    const TABLE = 'sales_bait_registered_interests';

    const FIELD_ID            = 'id';
    const FIELD_SALES_BAIT_ID = 'sales_bait_id';
    const FIELD_RELATION_TYPE = 'relation_type';
    const FIELD_RELATION_ID   = 'relation_id';

    const TYPE_USER    = 0;
    const TYPE_CONTACT = 1;

    const RELATION_SALES_BAIT = 'salesBait';
    const RELATION_LEGACY_CONTACT = 'legacyContact';

    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    /**
     * Defines the relationship to the sales bait lead model.
     *
     * @return BelongsTo
     */
    public function salesBait(): BelongsTo
    {
        return $this->belongsTo(SalesBaitLead::class, self::FIELD_SALES_BAIT_ID, SalesBaitLead::FIELD_ID);
    }

    /**
     * Defines relationship to legacy contact.
     *
     * @return BelongsTo
     */
    public function legacyContact(): BelongsTo
    {
        return $this->belongsTo(EloquentCompanyContact::class, self::FIELD_RELATION_ID, EloquentCompanyContact::FIELD_CONTACT_ID);
    }

    /**
     * Defines relationship to legacy user.
     *
     * @return BelongsTo
     */
    public function legacyUser(): BelongsTo
    {
        return $this->belongsTo(EloquentUser::class, self::FIELD_RELATION_ID, EloquentUser::ID);
    }
}
