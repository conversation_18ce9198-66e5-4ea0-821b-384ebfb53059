<?php

namespace App\Models\Locations;

use App\Enums\Locations\LocationType;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * @property int $id
 * @property LocationType $type
 * @property string $state_abbr
 * @property string $state
 * @property string $state_key
 * @property string $county
 * @property string $county_key
 * @property string $city
 * @property string $city_key
 * @property string $zip_code
 **/
class USLocation extends BaseLocationModel
{
    use HasFactory;

    const string TABLE = 'us_locations';

    const string FIELD_ID                 = 'id';
    const string FIELD_TYPE               = 'type';
    const string FIELD_PARENT_LOCATION_ID = 'parent_location_id';
    const string FIELD_STATE              = 'state';
    const string FIELD_STATE_KEY          = 'state_key';
    const string FIELD_STATE_ABBREVIATION = 'state_abbr';
    const string FIELD_COUNTY             = 'county';
    const string FIELD_COUNTY_KEY         = 'county_key';
    const string FIELD_CITY               = 'city';
    const string FIELD_CITY_KEY           = 'city_key';
    const string FIELD_ZIP_CODE           = 'zip_code';

    const LocationType TYPE_STATE    = LocationType::ADMINISTRATIVE_AREA_1;
    const LocationType TYPE_COUNTY   = LocationType::ADMINISTRATIVE_AREA_2;
    const LocationType TYPE_CITY     = LocationType::LOCALITY;
    const LocationType TYPE_ZIP_CODE = LocationType::POSTAL_CODE;

    // International-friendly column name references
    const string ADMINISTRATIVE_AREA_1     = self::FIELD_STATE;
    const string ADMINISTRATIVE_AREA_1_KEY = self::FIELD_STATE_ABBREVIATION; // state-key is redundant
    const string ADMINISTRATIVE_AREA_2     = self::FIELD_COUNTY;
    const string ADMINISTRATIVE_AREA_2_KEY = self::FIELD_COUNTY_KEY;
    const string LOCALITY                  = self::FIELD_CITY;
    const string LOCALITY_KEY              = self::FIELD_CITY_KEY;
    const string POSTAL_CODE               = self::FIELD_ZIP_CODE;

    const array TYPES = [
        self::TYPE_STATE,
        self::TYPE_COUNTY,
        self::TYPE_CITY,
        self::TYPE_ZIP_CODE,
    ];

    protected $table = self::TABLE;

    // International-friendly accessors
    protected function administrative_area_1(): Attribute
    {
        return Attribute::make(get: fn() => $this->state);
    }

    protected function administrative_area_1_key(): Attribute
    {
        return Attribute::make(get: fn() => $this->state_abbr);
    }

    protected function administrative_area_2(): Attribute
    {
        return Attribute::make(get: fn() => $this->county);
    }

    protected function administrative_area_key_2(): Attribute
    {
        return Attribute::make(get: fn() => $this->county_key);
    }

    protected function locality(): Attribute
    {
        return Attribute::make(get: fn() => $this->city);
    }

    protected function locality_key(): Attribute
    {
        return Attribute::make(get: fn() => $this->city_key);
    }

    protected function postal_code(): Attribute
    {
        return Attribute::make(get: fn() => $this->zip_code);
    }
}