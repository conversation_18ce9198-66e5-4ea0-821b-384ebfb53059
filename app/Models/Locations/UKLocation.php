<?php

namespace App\Models\Locations;

use App\Enums\Locations\LocationType;
use Illuminate\Database\Eloquent\Casts\Attribute;

/**
 * @deprecated example only, DO NOT USE
 *
 * @property int $id
 * @property LocationType $type
 * @property string $country
 * @property string $country_abbreviation
 * @property string $county
 * @property string $county_key
 * @property string $city
 * @property string $city_key
 * @property string $postcode
 **/
class UKLocation extends BaseLocationModel
{
    const string TABLE = 'example_model_only';

    const string FIELD_ID                 = 'id';
    const string FIELD_TYPE               = 'type';
    const string FIELD_PARENT_LOCATION_ID = 'parent_location_id';
    const string FIELD_COUNTRY            = 'country';
    const string FIELD_COUNTRY_KEY        = 'country_abbreviation';
    const string FIELD_COUNTY             = 'county';
    const string FIELD_COUNTY_KEY         = 'county_key';
    const string FIELD_CITY               = 'city';
    const string FIELD_CITY_KEY           = 'city_key';
    const string FIELD_POSTCODE           = 'postcode';

    const LocationType TYPE_COUNTRY      = LocationType::ADMINISTRATIVE_AREA_1; // e.g. 'england'
    const LocationType TYPE_COUNTY       = LocationType::ADMINISTRATIVE_AREA_2; // e.g. 'greater london'
    const LocationType TYPE_CITY         = LocationType::LOCALITY;
    const LocationType TYPE_POSTCODE     = LocationType::POSTAL_CODE;

    // International-friendly column name references
    const string ADMINISTRATIVE_AREA_1     = self::FIELD_COUNTRY;
    const string ADMINISTRATIVE_AREA_1_KEY = self::FIELD_COUNTRY_KEY;
    const string ADMINISTRATIVE_AREA_2     = self::FIELD_COUNTY;
    const string ADMINISTRATIVE_AREA_2_KEY = self::FIELD_COUNTY_KEY;
    const string LOCALITY                  = self::FIELD_CITY;
    const string LOCALITY_KEY              = self::FIELD_CITY_KEY;
    const string POSTAL_CODE               = self::FIELD_POSTCODE;

    const array TYPES = [
        self::TYPE_COUNTRY,
        self::TYPE_COUNTY,
        self::TYPE_CITY,
        self::TYPE_POSTCODE,
    ];

    // International-friendly accessors
    protected function administrative_area_1(): Attribute
    {
        return Attribute::make(get: fn() => $this->country);
    }

    protected function administrative_area_1_key(): Attribute
    {
        return Attribute::make(get: fn() => $this->country_abbreviation);
    }

    protected function administrative_area_2(): Attribute
    {
        return Attribute::make(get: fn() => $this->county);
    }

    protected function administrative_area_key_2(): Attribute
    {
        return Attribute::make(get: fn() => $this->county_key);
    }

    protected function locality(): Attribute
    {
        return Attribute::make(get: fn() => $this->city);
    }

    protected function locality_key(): Attribute
    {
        return Attribute::make(get: fn() => $this->city_key);
    }

    protected function postal_code(): Attribute
    {
        return Attribute::make(get: fn() => $this->postcode);
    }
}