<?php

namespace App\Models\Locations;

use App\Enums\Locations\LocationType;
use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property LocationType $type
 * @property int $parent_location_id
 *
 * @property string administrative_area_1
 * @property string administrative_area_1_key
 * @property string administrative_area_2
 * @property string administrative_area_2_key
 * @property string locality
 * @property string locality_key
 * @property string postal_code
 */
abstract class BaseLocationModel extends BaseModel
{
    const string FIELD_ID                 = 'id';
    const string FIELD_TYPE               = 'type';
    const string FIELD_PARENT_LOCATION_ID = 'parent_location_id';

    // These must be overridden in each model
    const string ADMINISTRATIVE_AREA_1     = 'administrative_area_1';
    const string ADMINISTRATIVE_AREA_1_KEY = 'administrative_area_1_key';
    const string ADMINISTRATIVE_AREA_2     = 'administrative_area_2';
    const string ADMINISTRATIVE_AREA_2_KEY = 'administrative_area_2_key';
    const string LOCALITY                  = 'locality';
    const string LOCALITY_KEY              = 'locality_key';
    const string POSTAL_CODE               = 'postal_code';

    protected $fillable = [];

    protected $casts = [
        self::FIELD_TYPE => LocationType::class,
    ];

    public $timestamps = false;

    public function parentLocation(): ?BelongsTo
    {
        return $this->belongsTo(BaseLocationModel::class, self::FIELD_PARENT_LOCATION_ID, self::FIELD_ID);
    }

    protected abstract function administrative_area_1(): Attribute;
    protected abstract function administrative_area_1_key(): Attribute;
    protected abstract function administrative_area_2(): Attribute;
    protected abstract function administrative_area_key_2(): Attribute;
    protected abstract function locality(): Attribute;
    protected abstract function locality_key(): Attribute;
    protected abstract function postal_code(): Attribute;
}