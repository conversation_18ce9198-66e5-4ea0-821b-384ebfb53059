<?php

namespace App\Models\Locations;

use App\Enums\Locations\LocationType;
use Illuminate\Database\Eloquent\Casts\Attribute;

/**
 * @deprecated example only, DO NOT USE
 *
 * @property int $id
 * @property LocationType $type
 * @property string $state
 * @property string $state_abbreviation
 * @property string $council_area
 * @property string $council_area_key
 * @property string $city
 * @property string $city_key
 * @property string $postcode
 **/
class AusLocation extends BaseLocationModel
{
    const string TABLE = 'example_model_only';

    const string FIELD_ID                 = 'id';
    const string FIELD_TYPE               = 'type';
    const string FIELD_PARENT_LOCATION_ID = 'parent_location_id';
    const string FIELD_STATE              = 'state';
    const string FIELD_STATE_ABBREVIATION = 'state_abbreviation';
    const string FIELD_COUNCIL_AREA       = 'council_area'; // government sites use 'local government area (LGA)'
    const string FIELD_COUNCIL_AREA_KEY   = 'council_area_key';
    const string FIELD_CITY               = 'city';
    const string FIELD_CITY_KEY           = 'city_key';
    const string FIELD_POSTCODE           = 'postcode';

    const LocationType TYPE_STATE        = LocationType::ADMINISTRATIVE_AREA_1; // e.g. 'south australia'
    const LocationType TYPE_COUNCIL_AREA = LocationType::ADMINISTRATIVE_AREA_2; // e.g. 'city of adelaide'
    const LocationType TYPE_CITY         = LocationType::LOCALITY;
    const LocationType TYPE_POSTCODE     = LocationType::POSTAL_CODE;

    // International-friendly column name references
    const string ADMINISTRATIVE_AREA_1     = self::FIELD_STATE;
    const string ADMINISTRATIVE_AREA_1_KEY = self::FIELD_STATE_ABBREVIATION; // state-key is redundant
    const string ADMINISTRATIVE_AREA_2     = self::FIELD_COUNCIL_AREA;
    const string ADMINISTRATIVE_AREA_2_KEY = self::FIELD_COUNCIL_AREA_KEY;
    const string LOCALITY                  = self::FIELD_CITY;
    const string LOCALITY_KEY              = self::FIELD_CITY_KEY;
    const string POSTAL_CODE               = self::FIELD_POSTCODE;

    const array TYPES = [
        self::TYPE_STATE,
        self::TYPE_COUNCIL_AREA,
        self::TYPE_CITY,
        self::TYPE_POSTCODE,
    ];

    // International-friendly accessors
    protected function administrative_area_1(): Attribute
    {
        return Attribute::make(get: fn() => $this->state);
    }

    protected function administrative_area_1_key(): Attribute
    {
        return Attribute::make(get: fn() => $this->state_abbreviation);
    }

    protected function administrative_area_2(): Attribute
    {
        return Attribute::make(get: fn() => $this->council_area);
    }

    protected function administrative_area_key_2(): Attribute
    {
        return Attribute::make(get: fn() => $this->council_area_key);
    }

    protected function locality(): Attribute
    {
        return Attribute::make(get: fn() => $this->city);
    }

    protected function locality_key(): Attribute
    {
        return Attribute::make(get: fn() => $this->city_key);
    }

    protected function postal_code(): Attribute
    {
        return Attribute::make(get: fn() => $this->postcode);
    }
}