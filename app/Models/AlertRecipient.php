<?php

namespace App\Models;

use App\Models\Odin\CompanyUser;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property int $id
 * @property int $alert_id
 * @property int $notifiable_type
 * @property int $notifiable_id
 * @property int $channel
 *
 * @property-read  Alert $alert
 * @property-read  User|CompanyUser|mixed $notifiable
 */
class AlertRecipient extends Model
{
    const string TABLE = 'alert_recipients';

    const string FIELD_ID = 'id';
    const string FIELD_ALERT_ID = 'alert_id';
    const string FIELD_NOTIFIABLE_TYPE = 'notifiable_type';
    const string FIELD_NOTIFIABLE_ID = 'notifiable_id';
    const string FIELD_CHANNEL = 'channel';

    const string CHANNEL_EMAIL = 'email';
    const string CHANNEL_SMS = 'sms';

    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function alert(): BelongsTo
    {
        return $this->belongsTo(Alert::class);
    }

    /**
     * @return MorphTo
     */
    public function notifiable(): MorphTo
    {
        return $this->morphTo();
    }
}
