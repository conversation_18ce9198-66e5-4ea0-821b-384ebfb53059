<?php

namespace App\Models;

use App\Casts\ObjectForcedJson;
use App\Models\Odin\ConsumerProduct;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * This model is intended to be used exclusively as a debugging tool - this shall not be referenced by any code other
 * than for the purpose of logging updates in the lifecycle of a consumer product,
 * as the structure is subject to change and the data will be routinely pruned.
 *
 * @property int $id
 * @property int $consumer_product_id
 * @property Carbon $consumer_product_created_at
 * @property ?array $status_updates
 * @property ?array $queue_updates
 * @property ?Carbon $flagged_good_to_sell_at
 * @property ?array $allocation_attempts_scheduled
 * @property ?array $allocation_attempts
 * @property ?Carbon $invoiced_at
 *
 * @property-read ConsumerProduct $consumerProduct
 */
class ConsumerProductLifecycleTracker extends BaseModel
{
    const string TABLE = 'consumer_product_lifecycle_trackers';

    const string FIELD_ID                            = 'id';
    const string FIELD_CONSUMER_PRODUCT_ID           = 'consumer_product_id';
    const string FIELD_CONSUMER_PRODUCT_CREATED_AT   = 'consumer_product_created_at';
    const string FIELD_STATUS_UPDATES                = 'status_updates';
    const string FIELD_QUEUE_UPDATES                 = 'queue_updates';
    const string FIELD_FLAGGED_GOOD_TO_SELL_AT       = 'flagged_good_to_sell_at';
    const string FIELD_ALLOCATION_ATTEMPTS_SCHEDULED = 'allocation_attempts_scheduled';
    const string FIELD_ALLOCATION_ATTEMPTS           = 'allocation_attempts';
    const string FIELD_INVOICED_AT                   = 'invoiced_at';

    const string RELATION_CONSUMER_PRODUCT = 'consumerProduct';

    protected $table   = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts   = [
        self::FIELD_CONSUMER_PRODUCT_CREATED_AT   => 'datetime',
        self::FIELD_STATUS_UPDATES                => ObjectForcedJson::class,
        self::FIELD_QUEUE_UPDATES                 => ObjectForcedJson::class,
        self::FIELD_FLAGGED_GOOD_TO_SELL_AT       => 'datetime',
        self::FIELD_ALLOCATION_ATTEMPTS_SCHEDULED => ObjectForcedJson::class,
        self::FIELD_ALLOCATION_ATTEMPTS           => ObjectForcedJson::class,
        self::FIELD_INVOICED_AT                   => 'datetime',
    ];

    /**
     * @return BelongsTo
     */
    public function consumerProduct(): BelongsTo
    {
        return $this->belongsTo(ConsumerProduct::class);
    }

}
