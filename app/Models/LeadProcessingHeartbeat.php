<?php

namespace App\Models;

use App\Models\Legacy\EloquentQuote;
use App\Models\Odin\ConsumerProduct;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int                $id
 * @property int                $lead_id
 * @property int                $lead_processor_id
 * @property int                $last_heartbeat
 * @property int                $consumer_product_id
 * @property Carbon             $created_at
 * @property Carbon             $updated_at
 *
 * @property-read ConsumerProduct $consumerProduct
 * @property-read LeadProcessor $processor
 */
class LeadProcessingHeartbeat extends BaseModel
{
    const string TABLE = 'lead_processing_heartbeats';

    const string FIELD_ID                  = 'id';
    const string FIELD_LEAD_PROCESSOR_ID   = 'lead_processor_id';
    const string FIELD_LAST_HEARTBEAT      = 'last_heartbeat';
    const string FIELD_CONSUMER_PRODUCT_ID = 'consumer_product_id';
    /** @deprecated  */
    const string FIELD_LEAD_ID           = 'lead_id';


    const string RELATION_LEAD      = 'lead';
    const string RELATION_PROCESSOR = 'processor';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @deprecated
     */
    public function lead(): BelongsTo
    {
        return $this->belongsTo(EloquentQuote::class, self::FIELD_LEAD_ID, EloquentQuote::ID);
    }

    /**
     * @return BelongsTo
     */
    public function consumerProduct(): BelongsTo
    {
        return $this->belongsTo(COnsumerProduct::class, self::FIELD_CONSUMER_PRODUCT_ID, ConsumerProduct::FIELD_ID);
    }

    /**
     * Defines the relationship between the heartbeat and a processor.
     *
     * @return BelongsTo
     */
    public function processor(): BelongsTo
    {
        return $this->belongsTo(LeadProcessor::class, self::FIELD_LEAD_PROCESSOR_ID, LeadProcessor::FIELD_ID);
    }
}
