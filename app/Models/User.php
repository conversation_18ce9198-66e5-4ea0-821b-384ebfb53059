<?php

namespace App\Models;

use App\Enums\SupportedTimezones;
use App\Models\Mailbox\MailboxUserToken;
use App\Models\Prospects\CloserDemo;
use App\Models\Teams\Team;
use App\Models\Teams\TeamLeader;
use App\Models\Teams\TeamMember;
use App\Models\Territory\RelationshipManager;
use App\Repositories\UserRepository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Collection;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Models\Activity;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Permission\Traits\HasRoles;
use App\Notifications\PasswordReset;
use App\Notifications\PasswordCreate;
use App\Traits\CompanyAssignable;

/**
 * @property int $id
 * @property int $legacy_user_id
 * @property string $name
 * @property string $email
 * @property string|null $slack_username
 * @property SupportedTimezones $timezone
 * @property string $meeting_url
 * @property ?string $calendly_api_token
 * @property bool $force_two_factor_auth
 * @property bool $verified_2fa
 * @property array $email_aliases
 * @property string|null $two_factor_auth_secret_key
 * @property string|null $email_signature
 *
 * @property-read LeadProcessor $leadProcessor
 * @property-read SuccessManager $successManager
 * @property-read AccountManager|null $accountManager
 * @property-read SupportOfficer|null $supportOfficer
 * @property-read Hunter|null $hunter
 * @property-read Collection<int, Phone> $phones
 * @property-read UserCallForwarding $callForwarding
 * @property-read Collection<Team> $leadsTeams
 * @property-read Collection<Team> $teams
 * @property-read bool $isSalesTeamLeader
 * @property-read Collection<UserPreset> $presets
 * @property-read RelationshipManager $relationshipManager
 * @property-read ?UserVoicemailMessage $userVoicemailMessage
 */
class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles, SoftDeletes, CompanyAssignable, LogsActivity;

    const string TABLE = 'users';

    const string FIELD_ID                    = 'id';
    const string FIELD_NAME                  = 'name';
    const string FIELD_EMAIL                 = 'email';
    const string FIELD_PASSWORD              = 'password';
    const string FIELD_LEGACY_USER_ID        = 'legacy_user_id';
    const string FIELD_SLACK_USERNAME        = 'slack_username';
    const string FIELD_TIMEZONE              = 'timezone';
    const string FIELD_MEETING_URL           = 'meeting_url';
    const string FIELD_CALENDLY_API_TOKEN   = 'calendly_api_token';

    const string FIELD_FORCE_TWO_FACTOR_AUTH = 'force_two_factor_auth';
    const string FIELD_VERIFIED_2FA          = 'verified_2fa';
    const string FIELD_CREATED_BY_ID         = 'created_by_id';
    const string FIELD_UPDATED_BY_ID         = 'updated_by_id';
    const string FIELD_EMAIL_ALIASES         = 'email_aliases';
    const string FIELD_USES_MAILBOX          = 'uses_mailbox';
    const string FIELD_DELETED_AT            = 'deleted_at';
    const string FIELD_EMAIL_SIGNATURE       = 'email_signature';

    const string TWO_FACTOR_AUTH_SECRET_KEY  = 'two_factor_auth_secret_key';

    const string SYSTEM_USER_RESERVED_NAME  = 'AUTOMATED_SYSTEM_USER_DO_NOT_MODIFY';
    const string SYSTEM_USER_RESERVED_EMAIL = '<EMAIL>';

    const string RELATION_PHONES               = 'phones';
    const string RELATION_ROLES                = 'roles';
    const string RELATION_PRIMARY_PHONE        = 'primaryPhone';
    const string RELATION_CALL_FORWARDING      = 'callForwarding';
    const string RELATION_LEADS_TEAMS          = 'leadsTeams';
    const string RELATION_TEAMS                = 'teams';
    const string RELATION_USER_PRESETS         = 'presets';
    const string RELATION_ACCOUNT_MANAGER      = 'accountManager';
    const string RELATION_RELATIONSHIP_MANAGER = 'relationshipManager';
    const string RELATION_PRIVACY_REQUEST_REDACTION_RECORDS = 'privacyRequestRedactionRecords';

    const string IS_SALES_TEAM_LEADER = 'isSalesTeamLeader';

    protected $table = self::TABLE;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        self::FIELD_NAME,
        self::FIELD_EMAIL,
        self::FIELD_PASSWORD,
        self::FIELD_LEGACY_USER_ID,
        self::FIELD_SLACK_USERNAME,
        self::FIELD_TIMEZONE,
        self::FIELD_MEETING_URL,
        self::FIELD_FORCE_TWO_FACTOR_AUTH,
        self::FIELD_UPDATED_BY_ID,
        self::FIELD_CREATED_BY_ID,
        self::FIELD_USES_MAILBOX,
        self::FIELD_EMAIL_ALIASES,
        self::FIELD_EMAIL_SIGNATURE,
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at'  => 'datetime',
        self::FIELD_TIMEZONE => SupportedTimezones::class,
        self::FIELD_EMAIL_ALIASES => 'array'
    ];

    /**
     * Returns 'system' user, to be used for automated functionality
     *
     * @return User
     */
    public static function systemUser(): self
    {
        /** @var UserRepository $userRepository */
        $userRepository = app(UserRepository::class);
        return $userRepository->getSystemUser();
    }

    /**
     * Send the password reset notification.
     *
     * @param string $token
     * @return void
     */
    public function sendPasswordResetNotification($token)
    {
        $this->notify(new PasswordReset($token));
    }

    public function sendPasswordCreateNotification(string $token): void
    {
        $this->notify(new PasswordCreate($token));
    }

    /**
     * Defines the belongs to relationship to the Lead Processor table.
     *
     * @return HasOne
     */
    public function leadProcessor(): HasOne
    {
        return $this->hasOne(LeadProcessor::class, LeadProcessor::FIELD_USER_ID, self::FIELD_ID);
    }

    /**
     * Defines the belongs to relationship to the Account Manager table.
     *
     * @return HasOne
     */
    public function accountManager(): HasOne
    {
        return $this->hasOne(AccountManager::class, AccountManager::FIELD_USER_ID, self::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function relationshipManager(): HasOne
    {
        return $this->hasOne(RelationshipManager::class, RelationshipManager::FIELD_USER_ID, self::FIELD_ID);
    }

    public function successManager(): HasOne
    {
        return $this->hasOne(SuccessManager::class, SuccessManager::FIELD_USER_ID, self::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function supportOfficer(): HasOne
    {
        return $this->hasOne(SupportOfficer::class);
    }

    /**
     * @return HasOne
     */
    public function hunter(): HasOne
    {
        return $this->hasOne(Hunter::class);
    }

    /**
     * @param bool $withTrash
     * @return BelongsToMany
     */
    public function phones(bool $withTrash = false): BelongsToMany
    {
        // TODO - Remove duplicates when $withTrash = true
        $query = $this->belongsToMany(Phone::class, UserPhone::TABLE, UserPhone::FIELD_USER_ID, UserPhone::FIELD_PHONE_ID)->withTimestamps();

        if (!$withTrash) $query->whereNull(UserPhone::FIELD_DELETED_AT);

        return $query;
    }

    /**
     * @return Phone|null
     */
    public function primaryPhone(): ?Phone
    {
        return $this->phones()->orderByDesc('user_phones.created_at')->first();
    }

    /**
     * @param int $phoneId
     * @return void
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function assignPhone(int $phoneId): void
    {
        $userRepository = app()->make(UserRepository::class);
        $userRepository->assignPhoneToUser($phoneId, $this->id);
    }

    /**
     * @return HasOne
     */
    public function callForwarding(): HasOne
    {
        return $this->hasOne(UserCallForwarding::class, UserCallForwarding::FIELD_USER_ID)
            ->where(UserCallForwarding::FIELD_STATUS, UserCallForwarding::STATUS_ENABLED)
            ->where(UserCallForwarding::FIELD_FORWARD_TO_NUMBER, '<>', '');
    }

    public function ActivityFeeds(): HasMany
    {
        return $this->hasMany(ActivityFeed::class, ActivityFeed::FIELD_USER_ID, self::FIELD_ID);
    }


    /**
     * Get the filter preset that belongs to user,
     *
     * @return HasOne
     */
    public function filterPreset(): HasOne
    {
        return $this->hasOne(UserFilterPreset::class, UserFilterPreset::FIELD_USER_ID, self::FIELD_ID);
    }

    /**
     * @return HasManyThrough
     */
    public function leadsTeams(): HasManyThrough
    {
        return $this->hasManyThrough(
            Team::class,
            TeamLeader::class,
            TeamLeader::FIELD_USER_ID,
            Team::FIELD_ID,
            self::FIELD_ID,
            TeamLeader::FIELD_TEAM_ID,
        );
    }

    /**
     * @return HasManyThrough
     */
    public function teams(): HasManyThrough
    {
        return $this->hasManyThrough(
            Team::class,
            TeamMember::class,
            TeamMember::FIELD_USER_ID,
            Team::FIELD_ID,
            self::FIELD_ID,
            TeamMember::FIELD_TEAM_ID,
        );
    }

    /**
     * @return bool
     */
    public function isSalesTeamLeader(): bool
    {
        return !!$this->{self::RELATION_LEADS_TEAMS}
            ->filter(fn(Team $team) => preg_match("/sales/i", $team->{Team::RELATION_TEAM_TYPE}->{Team::FIELD_NAME}))
            ->first();
    }

    /**
     * @return bool
     */
    public function isHRManager(): bool
    {
        return $this->hasRole('hr-manager');
    }

    /**
     * @return HasMany
     */
    public function presets(): HasMany
    {
        return $this->hasMany(UserPreset::class, UserPreset::FIELD_USER_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function teamLeaderRoles(): HasMany
    {
        return $this->hasMany(TeamLeader::class, TeamLeader::FIELD_USER_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function teamMemberRoles(): HasMany
    {
        return $this->hasMany(TeamMember::class, TeamMember::FIELD_USER_ID, self::FIELD_ID);
    }


    public function actions(): Builder
    {
        return UserAction::query()
            ->where(function ($query) {
                $query->where(UserAction::FIELD_USER_ID, $this->{self::FIELD_ID});
            })
            ->orderBy(UserAction::FIELD_PINNED, 'desc')
            ->latest();

    }

    /**
     * Retrieves the user that created the user,
     *
     * @return HasOne
     */
    public function createdBy(): HasOne
    {
        return $this->hasOne(User::class, User::FIELD_ID, self::FIELD_CREATED_BY_ID);
    }

    /**
     * Retrieves the user that updated the user,
     *
     * @return HasOne
     */
    public function updatedBy(): HasOne
    {
        return $this->hasOne(User::class, User::FIELD_ID, self::FIELD_UPDATED_BY_ID);
    }

    /**
     * @return HasMany
     */
    public function mailboxTokens(): HasMany
    {
        return $this->hasMany(MailboxUserToken::class, MailboxUserToken::FIELD_USER_ID, self::FIELD_ID);
    }

    public function privacyRequestRedactionRecords(): HasMany
    {
        return $this->hasMany(PrivacyRequestRedactedRecords::class, PrivacyRequestRedactedRecords::FIELD_INITIATOR_ID, self::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function userVoicemailMessage(): HasOne
    {
        return $this->hasOne(UserVoicemailMessage::class);
    }

    /**
     * @return MorphMany
     */
    public function activity(): morphMany
    {
        return $this->morphMany(Activity::class, 'causer');
    }

    /**
     * @return HasMany
     */
    public function closerDemos(): HasMany
    {
        return $this->hasMany(CloserDemo::class);
    }

    public function roleConfigurations(): HasMany
    {
        return $this->hasMany(RoleConfiguration::class);
    }

    public function roleConfiguration(string $role)
    {
        return $this->roleConfigurations()->whereHas('role', fn($query) => $query->whereName($role))->first();
    }

    public function configurationForRole(string $config, string $role, $fallback = null) {
        return $this->roleConfiguration($role)?->config($config) ?? $fallback;
    }

    /**
     * @return bool
     */
    public function isBdm(): bool
    {
        return $this->hasRole('business-development-manager');
    }

    public function getAllEmails()
    {
        return collect([
            $this->email,
            $this->email_aliases,
        ])->flatten()
            ->filter()
            ->unique();
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable()
            ->useLogName('users')
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
}
