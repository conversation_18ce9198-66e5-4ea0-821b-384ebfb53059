<?php

namespace App\Models\Billing;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int $id
 * @property string $name
 * @property string $slug
 * @property string $description
 * @property string $line_item_text
 * @property int $expires_in_days
 * @property int $consumption_order
 * @property bool $cash
 * @property bool $active
 * @readonly bool $invoiceable // TODO - Remove
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class CreditType extends BaseModel
{
    use HasFactory;

    const string TABLE                   = 'credit_types';
    const string FIELD_ID                = 'id';
    const string FIELD_NAME              = 'name';
    const string FIELD_SLUG              = 'slug';
    const string FIELD_DESCRIPTION       = 'description';
    const string FIELD_LINE_ITEM_TEXT    = 'line_item_text';
    const string FIELD_EXPIRES_IN_DAYS   = 'expires_in_days';
    const string FIELD_CONSUMPTION_ORDER = 'consumption_order';
    const string FIELD_CASH              = 'cash';
    const string FIELD_ACTIVE            = 'active';
    const string FIELD_INVOICEABLE       = 'invoiceable';
    const string FIELD_CREATED_AT        = 'created_at';
    const string FIELD_UPDATED_AT        = 'updated_at';
    const string RELATION_CREDITS        = 'credits';

    protected $guarded = [
        self::FIELD_ID,
    ];

    /**
     * @return HasMany
     */
    public function credits(): HasMany
    {
        return $this->hasMany(Credit::class, Credit::FIELD_CREDIT_TYPE, CreditType::FIELD_SLUG);
    }

}
