<?php

namespace App\Models\Billing;

use App\Enums\InvoiceRefundStatus;
use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int id
 * @property float amount
 * @property string request_status
 * @property int invoice_refund_id
 * @property int refunded_payment_id
 * @property int refund_transaction_id
 */
class InvoiceRefundCharge extends BaseModel
{
    use HasFactory;

    const string TABLE                         = 'invoice_refund_charges';
    const string FIELD_ID                      = 'id';
    const string FIELD_UUID                    = 'uuid';
    const string FIELD_AMOUNT                  = 'amount';
    const string FIELD_REQUEST_STATUS          = 'request_status';
    const string FIELD_INVOICE_REFUND_ID       = 'invoice_refund_id';
    const string FIELD_REFUNDED_PAYMENT_ID     = 'refunded_payment_id';
    const string FIELD_REFUND_TRANSACTION_UUID = 'refund_transaction_uuid';

    const string FIELD_CREATED_AT            = 'created_at';
    const string FIELD_UPDATED_AT            = 'updated_at';
    const string RELATION_INVOICE_REFUND     = 'invoiceRefund';
    const string RELATION_REFUNDED_PAYMENT   = 'refundedPayment';
    const string RELATION_REFUND_TRANSACTION = 'refundTransaction';

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $casts = [
        self::FIELD_REQUEST_STATUS => InvoiceRefundStatus::class
    ];

    /**
     * @return BelongsTo
     */
    public function invoiceRefund(): BelongsTo
    {
        return $this->belongsTo(InvoiceRefund::class, self::FIELD_INVOICE_REFUND_ID, InvoiceRefund::FIELD_ID);
    }

    /**
     * The payment transaction that is being refunded.
     *
     * @return BelongsTo
     */
    public function refundedPayment(): BelongsTo
    {
        return $this->belongsTo(InvoiceTransaction::class, self::FIELD_REFUNDED_PAYMENT_ID, InvoiceTransaction::FIELD_ID);
    }

    /**
     * The refund transaction.
     *
     * @return BelongsTo
     */
    public function refundTransaction(): BelongsTo
    {
        return $this->belongsTo(InvoiceTransaction::class, self::FIELD_REFUND_TRANSACTION_UUID, InvoiceTransaction::FIELD_UUID);
    }

}
