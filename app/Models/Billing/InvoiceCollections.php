<?php

namespace App\Models\Billing;

use App\Models\BaseModel;
use App\Models\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class InvoiceCollections extends BaseModel
{
    use SoftDeletes;

    const string TABLE = 'invoice_collections';

    const string FIELD_ID               = 'id';
    const string FIELD_UUID             = 'uuid';
    const string FIELD_INVOICE_ID       = 'invoice_id';
    const string FIELD_SENT_DATE        = 'sent_date';
    const string FIELD_RECOVERY_STATUS  = 'recovery_status';
    const string FIELD_AMOUNT_RECOVERED = 'amount_recovered';
    const string FIELD_AMOUNT_COLLECTED = 'amount_collected';
    const string FIELD_USER_ID          = 'user_id';
    const string FIELD_RECOVERY_DATE    = 'recovery_date';
    const string FIELD_CREATED_AT       = 'created_at';
    const string FIELD_UPDATED_AT       = 'updated_at';

    const string RELATION_INVOICE = 'invoice';
    const string RELATION_USER    = 'user';

    protected $guarded = [
        self::FIELD_ID
    ];

    /**
     * @return BelongsTo
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class, self::FIELD_INVOICE_ID, Invoice::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }
}
