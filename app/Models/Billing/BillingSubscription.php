<?php

namespace App\Models\Billing;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\Company;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * BillingSubscriptions determine when to initiate charge events for campaigns
 *
 * @property int $id
 * @property string $uuid
 * @property int $company_id
 * @property int $company_campaign_id
 * @property string $frequency
 * @property float $threshold
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $deleted_at
 *
 * @property Company $company
 * @property CompanyCampaign $companyCampaign
 */
class BillingSubscription extends Model
{
    use HasFactory;

}
