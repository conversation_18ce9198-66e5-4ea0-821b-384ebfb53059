<?php

namespace App\Models\Billing;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @property int $id
 * @property string $event_class
 * @property string $action_class
 * @property int $sort_order
 * @property int $billing_profile_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read BillingProfile $billingProfile
 */
class BillingProfilePolicy extends Model
{
    use HasFactory;

    const string TABLE = 'billing_profile_policies';

    const string FIELD_ID                 = 'id';
    const string FIELD_EVENT_CLASS        = 'event_class';
    const string FIELD_ACTION_CLASS       = 'action_class';
    const string FIELD_SORT_ORDER         = 'sort_order';
    const string FIELD_ACTION_DATA        = 'action_data';
    const string FIELD_BILLING_PROFILE_ID = 'billing_profile_id';
    const string FIELD_CREATED_AT         = 'created_at';
    const string FIELD_UPDATED_AT         = 'updated_at';

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $casts = [
        self::FIELD_ACTION_DATA => 'array'
    ];

    /**
     * @return HasOne
     */
    public function billingProfile(): HasOne
    {
        return $this->HasOne(BillingProfile::class, BillingProfile::FIELD_ID, self::FIELD_BILLING_PROFILE_ID);
    }
}
