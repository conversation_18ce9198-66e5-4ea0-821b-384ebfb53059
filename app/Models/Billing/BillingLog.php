<?php

namespace App\Models\Billing;

use App\Enums\Billing\BillingLogLevel;
use Illuminate\Database\Eloquent\Model;

class BillingLog extends Model
{
    const string TABLE = 'billing_logs';

    const string FIELD_ID           = 'id';
    const string FIELD_RELATED_TYPE = 'related_type';
    const string FIELD_RELATED_ID   = 'related_id';
    const string FIELD_CONTEXT      = 'context';
    const string FIELD_NAMESPACE    = 'namespace';
    const string FIELD_MESSAGE      = 'message';
    const string FIELD_TRACE        = 'trace';
    const string FIELD_LEVEL        = 'level';

    protected $table = self::TABLE;

    protected $casts = [
        self::FIELD_CONTEXT => 'array',
        self::FIELD_LEVEL   => BillingLogLevel::class,
    ];

    protected $guarded = [
        self::FIELD_ID
    ];
}
