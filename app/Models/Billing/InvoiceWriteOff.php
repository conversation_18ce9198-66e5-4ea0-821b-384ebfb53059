<?php

namespace App\Models\Billing;

use App\Models\BaseModel;
use App\Models\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class InvoiceWriteOff extends BaseModel
{
    use SoftDeletes;

    const string TABLE = 'invoice_write_offs';

    const string FIELD_ID         = 'id';
    const string FIELD_UUID       = 'uuid';
    const string FIELD_INVOICE_ID = 'invoice_id';
    const string FIELD_AMOUNT     = 'amount';
    const string FIELD_USER_ID    = 'user_id';

    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';

    const string RELATION_INVOICE = 'invoice';
    const string RELATION_USER    = 'user';

    protected $guarded = [
        self::FIELD_ID
    ];

    /**
     * @return BelongsTo
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class, self::FIELD_INVOICE_ID, Invoice::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }
}
