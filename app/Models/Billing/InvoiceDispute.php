<?php

namespace App\Models\Billing;

use App\Enums\Billing\Disputes\InvoiceDisputeReason;
use App\Enums\Billing\Disputes\InvoiceDisputeStatus;
use App\Models\BaseModel;
use App\Traits\Uuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;


class InvoiceDispute extends BaseModel
{
    use HasFactory, Uuid;

    const string TABLE = 'invoice_disputes';

    const string FIELD_ID                    = 'id';
    const string FIELD_INVOICE_ID            = 'invoice_id';
    const string FIELD_EXTERNAL_ID           = 'external_id';
    const string FIELD_REASON                = 'reason';
    const string FIELD_STATUS                = 'status';
    const string FIELD_AMOUNT                = 'amount';
    const string FIELD_TRANSACTION_CHARGE_ID = 'transaction_charge_id';
    const string FIELD_EXTERNAL_CHARGE_ID    = 'external_charge_id';
    const string FIELD_CURRENCY              = 'currency';
    const string FIELD_SOURCE                = 'source';
    const string FIELD_CREATED_AT            = 'created_at';

    const string RELATION_INVOICE = 'invoice';

    protected $guarded = [
        self::FIELD_ID,
    ];

    protected $casts = [
        self::FIELD_STATUS => InvoiceDisputeStatus::class,
        self::FIELD_REASON => InvoiceDisputeReason::class,
    ];

    /**
     * @return BelongsTo
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class, self::FIELD_INVOICE_ID, Invoice::FIELD_ID);
    }
}
