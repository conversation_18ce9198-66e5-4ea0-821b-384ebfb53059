<?php

namespace App\Models\Billing;

use App\DTO\Billing\InvoiceItemDTO;
use App\Enums\Billing\InvoiceItemTypes;
use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property int $invoice_id
 * @property string $description
 * @property int $billable_id
 * @property string $billable_type
 * @property float $unit_price
 * @property int $quantity
 * @property float $tax
 * @property int $added_by
 * @property-read Collection $billable_item
 */
class InvoiceItem extends BaseModel
{
    use HasFactory;

    const string TABLE = 'invoice_items';

    const string FIELD_ID             = 'id';
    const string FIELD_INVOICE_ID     = 'invoice_id';
    const string FIELD_DESCRIPTION    = 'description';
    const string FIELD_BILLABLE_ID    = 'billable_id';
    const string FIELD_BILLABLE_TYPE  = 'billable_type';
    const string FIELD_UNIT_PRICE     = 'unit_price';
    const string FIELD_QUANTITY       = 'quantity';
    const string FIELD_BILLABLE_ITEM  = 'billable_item';
    const string FIELD_TAX            = 'tax';
    const string FIELD_ADDED_BY       = 'added_by';
    const string FIELD_CREATED_AT     = 'created_at';
    const string RELATION_INVOICE     = 'invoice';
    const string RELATION_BILLABLE    = 'billable';
    const string RELATION_REFUND_ITEM = 'refundedItem';

    protected $guarded = [
        self::FIELD_ID,
    ];

    protected $casts = [
        self::FIELD_TAX        => 'float',
        self::FIELD_UNIT_PRICE => 'float',
        self::FIELD_QUANTITY   => 'int',
    ];

    /**
     * @return MorphTo
     */
    public function billable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * @return float
     */
    public function totalPrice(): float
    {
        return $this->quantity * $this->unit_price;
    }

    /**
     * @return BelongsTo
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class, self::FIELD_INVOICE_ID, Invoice::FIELD_ID);
    }

    /**
     * @param bool|null $includeTax
     * @return float
     */
    public function totalItemPrice(?bool $includeTax = false): float
    {
        $price = ($this->quantity * $this->unit_price);

        return $includeTax ? $price + $this->tax ?? 0 : $price;
    }

    /**
     * @return HasOne
     */
    public function refundedItem(): HasOne
    {
        return $this->hasOne(InvoiceRefundItem::class, InvoiceRefundItem::FIELD_INVOICE_ITEM_ID, self::FIELD_ID);
    }

    /**
     * @return InvoiceItemDTO
     */
    public function toDTO(): InvoiceItemDTO
    {
        return new InvoiceItemDTO(
            invoice_item_id: $this->{self::FIELD_ID},
            invoice_id     : $this->{self::FIELD_INVOICE_ID},
            billable_id    : $this->{self::FIELD_BILLABLE_ID},
            billable_type  : InvoiceItemTypes::fromClass($this->{self::FIELD_BILLABLE_TYPE})->value,
            unit_price     : $this->{self::FIELD_UNIT_PRICE},
            quantity       : $this->{self::FIELD_QUANTITY},
            description    : $this->{self::FIELD_DESCRIPTION},
            added_by       : $this->{self::FIELD_ADDED_BY},
        );
    }

    //toDto
}
