<?php

namespace App\Models\Billing;

use App\Contracts\Services\PaymentGatewayServiceContract;
use App\Enums\Billing\PaymentMethodServices;
use App\Models\BaseModel;
use App\Models\Odin\Company;
use App\Observers\CompanyPaymentMethodObserver;
use App\Services\PaymentGateway\PaymentGatewayServiceFactory;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @method static Builder onlyStripe()
 *
 * @property int $company_id
 * @property string $type
 *
 * @property-read Company $company
 */

#[ObservedBy([CompanyPaymentMethodObserver::class])]
class CompanyPaymentMethod extends BaseModel
{
    use HasFactory, SoftDeletes;

    const string TABLE = 'company_payment_methods';

    const string FIELD_ID                                  = 'id';
    const string FIELD_PAYMENT_GATEWAY_CLIENT_CODE         = 'payment_gateway_client_code';
    const string FIELD_PAYMENT_GATEWAY_PAYMENT_METHOD_CODE = 'payment_gateway_payment_method_code';
    const string FIELD_TYPE                                = 'type';
    const string FIELD_COMPANY_ID                          = 'company_id';
    const string FIELD_IS_DEFAULT                          = 'is_default';
    const string FIELD_ADDED_BY_ID                         = 'added_by_id';
    const string FIELD_ADDED_BY_TYPE                       = 'added_by_type';
    const string FIELD_EXPIRY_MONTH                        = 'expiry_month';
    const string FIELD_EXPIRY_YEAR                         = 'expiry_year';
    const string FIELD_NUMBER                              = 'number';


    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';
    const string FIELD_DELETED_AT = 'deleted_at';

    protected $guarded = [
        self::FIELD_ID
    ];

    public function getFormattedExpiry(): ?string
    {
        if (filled($this->expiry_month) && filled($this->expiry_year)
        ) {
            return "$this->expiry_month/$this->expiry_year";
        }

        return null;
    }


    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeOnlyStripe(Builder $query): Builder
    {
        return $query
            ->where(self::FIELD_TYPE, PaymentMethodServices::STRIPE->value);
    }

    /**
     * @return PaymentGatewayServiceContract
     */
    public function getPaymentGatewayService(): PaymentGatewayServiceContract
    {
        return PaymentGatewayServiceFactory::make(PaymentMethodServices::tryFrom($this->type));
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }
}
