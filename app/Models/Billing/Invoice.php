<?php

namespace App\Models\Billing;

use App\Enums\Billing\ApprovalStatus;
use App\Enums\Billing\BillingLogLevel;
use App\Enums\Billing\InvoiceItemTypes;
use App\Enums\Billing\InvoicePaymentStatus;
use App\Enums\Billing\InvoiceStates;
use App\Models\BaseModel;
use App\Models\Bundle;
use App\Models\Odin\Company;
use App\Observers\InvoiceObserver;
use App\Services\DatabaseHelperService;
use App\States\InvoiceState;
use App\Traits\Uuid;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Spatie\EventSourcing\Projections\Projection;
use Spatie\ModelStates\HasStates;
use Spatie\Tags\HasTags;
use Spatie\Tags\Tag;

/**
 * @property int $id
 * @property string $uuid
 * @property int $company_id
 * @property string $invoice_url
 * @property string $notes
 * @property string $created_by_id
 * @property Carbon $due_at
 * @property Carbon $issue_at
 * @property Carbon $created_at
 *
 * @property-read Collection<Tag> $tags
 * @property-read Company $company
 * @property-read InvoiceState $status
 * @property-read Collection<InvoiceItem> $items
 * @property-read Collection<InvoiceRefund> $refunds
 *
 * @method static Invoice unpaid()
 * @method static Invoice|Builder overdue(?Carbon $reference = null)
 * @method static Invoice|Builder due(?Carbon $reference = null)
 */

#[ObservedBy([InvoiceObserver::class])]
class Invoice extends BaseModel
{
    use HasFactory, HasStates, Uuid, HasTags, SoftDeletes;

    const string TABLE = 'invoices';

    public function getTable()
    {
        return DatabaseHelperService::database() . '.' . self::TABLE;
    }

    const string FIELD_ID                 = 'id';
    const string FIELD_UUID               = 'uuid';
    const string FIELD_COMPANY_ID         = 'company_id';
    const string FIELD_INVOICE_URL        = 'invoice_url';
    const string FIELD_NOTES              = 'notes';
    const string FIELD_CREATED_BY_ID      = 'created_by_id';
    const string FIELD_STATUS             = 'status';
    const string FIELD_CREATED_AT         = 'created_at';
    const string FIELD_DUE_AT             = 'due_at';
    const string FIELD_ISSUE_AT           = 'issue_at';
    const string FIELD_BILLING_PROFILE_ID = 'billing_profile_id';
    const string FIELD_PDF_FAILED         = 'pdf_failed';


    const string RELATION_COMPANY         = 'company';
    const string RELATION_INVOICE_ITEMS   = 'items';
    const string RELATION_TAGS            = 'tags';
    const string RELATION_REFUNDS         = 'refunds';
    const string RELATION_COLLECTIONS     = 'collections';
    const string RELATION_BILLING_PROFILE = 'billingProfile';
    const string RELATION_PAYMENTS        = 'payments';
    const string RELATION_CREDITS_APPLIED = 'creditsApplied';


    protected $casts = [
        'status' => InvoiceState::class,
    ];

    protected $guarded = [
        self::FIELD_ID,
    ];

    /**
     * @return float|int
     */
    public function getInvoiceItemsTotal(): float|int
    {
        /** @var Collection<InvoiceItem> $invoiceItems */
        $invoiceItems = $this->{self::RELATION_INVOICE_ITEMS};
        $total = 0.0;

        foreach ($invoiceItems as $invoiceItem) {
            //$total += ($invoiceItem->quantity * $invoiceItem->unit_price) + ($invoiceItem?->tax ?? 0);
            $total += ($invoiceItem->quantity * $invoiceItem->unit_price); //todo: add tax back
        }

        return $total;
    }

    /**
     * @return float
     */
    public function getTotalIssuable(): float
    {
        return $this->getInvoiceItemsTotal() - $this->getTotalCreditsApplied();
    }

    /**
     * @return float
     */
    public function getTotalOutstanding(): float
    {
        return $this->lastSnapshot()?->{InvoiceSnapshot::FIELD_TOTAL_OUTSTANDING} ?? 0;
    }

    /**
     * @return float
     */
    public function getTotalPaid(): float
    {
        return $this->lastSnapshot()?->{InvoiceSnapshot::FIELD_TOTAL_PAID} ?? 0;
    }

    /**
     * @return float
     */
    public function getTotalCollections(): float
    {
        return $this->lastSnapshot()?->{InvoiceSnapshot::FIELD_TOTAL_COLLECTIONS} ?? 0;
    }

    /**
     * @return HasMany
     */
    public function items(): HasMany
    {
        return $this->hasMany(InvoiceItem::class, InvoiceItem::FIELD_INVOICE_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function creditsApplied(): HasMany
    {
        return $this->hasMany(InvoiceCredit::class, InvoiceCredit::FIELD_INVOICE_ID, self::FIELD_ID);
    }

    /**
     * @return float
     */
    public function getTotalCreditsApplied(): float
    {
        return $this->creditsApplied()->sum(InvoiceCredit::FIELD_AMOUNT_APPLIED);
    }

    /**
     * @return HasMany
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(InvoiceTransaction::class, InvoiceTransaction::FIELD_INVOICE_UUID, self::FIELD_UUID);
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * @return Collection
     */
    public function getTags(): Collection
    {
        return $this->tags()->get()->map(fn($t) => $t->slug)->values();
    }

    /**
     * @param string $tag
     * @return bool
     */
    public function hasTag(string $tag): bool
    {
        return $this->getTags()->contains($tag);
    }

    /**
     * @return MorphMany
     */
    public function approvals(): MorphMany
    {
        return $this->morphMany(ActionApproval::class, ActionApproval::RELATION_APPROVABLE);
    }

    /**
     * @return HasMany
     */
    public function refunds(): HasMany
    {
        return $this->hasMany(InvoiceRefund::class, InvoiceRefund::FIELD_INVOICE_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function collections(): HasMany
    {
        return $this->hasMany(InvoiceCollections::class, InvoiceCollections::FIELD_INVOICE_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function writeOffs(): HasMany
    {
        return $this->hasMany(InvoiceWriteOff::class, InvoiceWriteOff::FIELD_INVOICE_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function snapshots(): HasMany
    {
        return $this->hasMany(InvoiceSnapshot::class, InvoiceSnapshot::FIELD_INVOICE_ID, self::FIELD_ID);
    }

    /**
     * @return InvoiceSnapshot|null
     */
    public function lastSnapshot(): ?InvoiceSnapshot
    {
        /** @var ?InvoiceSnapshot */
        return $this->snapshots()->latest(InvoiceSnapshot::FIELD_ID)->first();
    }

    /**
     * @return InvoiceCollections|null
     */
    public function lastCollections(): ?InvoiceCollections
    {
        /** @var ?InvoiceCollections */
        return $this->collections()->latest(InvoiceCollections::FIELD_ID)->first();
    }

    /**
     * @return InvoiceWriteOff|null
     */
    public function lastWriteOffs(): ?InvoiceWriteOff
    {
        /** @var ?InvoiceWriteOff */
        return $this->writeOffs()->latest(InvoiceWriteOff::FIELD_ID)->first();
    }

    /**
     * @return bool
     */
    public function hasPendingAction(): bool
    {
        return ActionApproval::query()
            ->where(ActionApproval::FIELD_APPROVABLE_ID, $this->id)
            ->where(ActionApproval::FIELD_APPROVABLE_TYPE, Invoice::class)
            ->where(function ($query) {
                $query->where(ActionApproval::FIELD_STATUS, ApprovalStatus::PENDING->value)
                    ->orWhere(ActionApproval::FIELD_IS_PROCESSING, true);
            })
            ->exists();
    }

    /**
     * @return HasMany
     */
    public function chargebacks(): HasMany
    {
        return $this->hasMany(InvoiceDispute::class, InvoiceDispute::FIELD_INVOICE_ID, self::FIELD_ID);
    }

    /**
     * @return InvoiceSnapshot|null
     */
    public function latestSnapshot(): ?InvoiceSnapshot
    {
        /** @var ?InvoiceSnapshot */
        return $this->snapshots()->orderByDesc(InvoiceSnapshot::FIELD_ID)->first();
    }

    /**
     * @param Collection<\App\DTO\Billing\InvoiceItemDTO>|null $items
     * @return Collection<\App\DTO\Billing\InvoiceItemDTO>
     */
    public function itemsToDTO(?Collection $items = null): Collection
    {
        return ($items ?? $this->items)->map(fn(InvoiceItem $invoiceItem) => $invoiceItem->toDTO());
    }

    /**
     * @param array<InvoiceItemTypes> $types
     * @return bool
     */
    public function hasBillableTypes(array $types): bool
    {
        $typeClasses = collect($types)->map(fn(InvoiceItemTypes $types) => $types->getClass());

        return $this->items()->whereIn(InvoiceItem::FIELD_BILLABLE_TYPE, $typeClasses)->exists();
    }

    /**
     * @return bool
     */
    public function canApplyCredits(): bool
    {
        return !$this->hasBillableTypes([
            InvoiceItemTypes::BUNDLE,
            InvoiceItemTypes::CREDIT,
        ]);
    }

    /**
     * @return HasOne
     */
    public function billingProfile(): HasOne
    {
        return $this->HasOne(BillingProfile::class, BillingProfile::FIELD_ID, self::FIELD_BILLING_PROFILE_ID);
    }

    /**
     * @return MorphMany
     */
    public function logs(): MorphMany
    {
        return $this->morphMany(BillingLog::class, 'related');
    }

    /**
     * @return bool
     */
    public function hasError(): bool
    {
        return $this->logs()->where(BillingLog::FIELD_LEVEL, BillingLogLevel::ERROR)->exists();
    }

    /**
     * @return HasMany
     */
    public function payments(): HasMany
    {
        return $this->hasMany(InvoicePayment::class, InvoicePayment::FIELD_INVOICE_ID, self::FIELD_ID);
    }

    /**
     * @return bool
     */
    public function isProcessingPayment(): bool
    {
        // todo - scope
        return $this->payments()
            ->whereIn(InvoicePayment::FIELD_STATUS, [
                InvoicePaymentStatus::RESCHEDULED,
                InvoicePaymentStatus::PENDING,
            ])->exists();
    }

    /**
     * @return bool
     */
    public function isBundleInvoice(): bool
    {
        return (bool)$this->items?->first(function (InvoiceItem $item) {
            return $item->{InvoiceItem::FIELD_BILLABLE_TYPE} === Bundle::class;
        });
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeUnpaid(Builder $query): Builder
    {
        return $query->whereIn(Invoice::FIELD_STATUS, [
            InvoiceStates::ISSUED,
            InvoiceStates::FAILED,
        ]);
    }

    /**
     * @param Builder $query
     * @param Carbon|null $reference
     * @return Builder
     */
    public function scopeDue(Builder $query, ?Carbon $reference = null): Builder
    {
        if (empty($reference)) {
            $reference = now();
        }

        return $query->where(Invoice::FIELD_DUE_AT, '<=', $reference);
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeOverdue(Builder $query, ?Carbon $reference = null): Builder
    {
        if (empty($reference)) {
            $reference = now();
        }

        return $query->where(Invoice::FIELD_DUE_AT, '<', $reference);
    }
}
