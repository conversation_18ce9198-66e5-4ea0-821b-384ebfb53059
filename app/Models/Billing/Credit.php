<?php

namespace App\Models\Billing;

use App\Models\Odin\Company;
use App\Models\User;
use App\Traits\Uuid;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\MorphMany;

/**
 * @property int $id
 * @property string $uuid
 * @property int $company_id
 * @property int $invoice_id
 * @property int $user_id
 * @property string $status
 * @property string $credit_type
 * @property Carbon $expires_at
 * // * @property boolean $is_manual
 * @property float $amount //todo: delete
 * @property float $initial_value
 * @property float $remaining_value
 *
 * @property-read Company $company
 * @property-read Invoice $invoice
 * @property-read User $user
 */
// This is not a projection because we don't have uuid and it throws an error when we try to save it
class Credit extends Model
{
    use HasFactory, Uuid;

    const string TABLE = 'credits';

    const string FIELD_ID              = 'id';
    const string FIELD_COMPANY_ID      = 'company_id';
    const string FIELD_CREDIT_TYPE     = 'credit_type';
    const string FIELD_INITIAL_VALUE   = 'initial_value';
    const string FIELD_REMAINING_VALUE = 'remaining_value';
    const string FIELD_CREATED_AT      = 'created_at';
    const string FIELD_UPDATED_AT      = 'updated_at';
    const string FIELD_DELETED_AT      = 'deleted_at';
    const string FIELD_EXPIRES_AT      = 'expires_at';
    const string FIELD_UUID            = 'uuid';
    const string FIELD_NOTES           = 'notes';

    const string RELATION_COMPANY     = 'company';
    const string RELATION_CREDIT_TYPE = 'creditType';
    const string RELATION_BILLING_PROFILES = 'billingProfiles';

    protected $guarded = [
        self::FIELD_ID
    ];

    public function scopeActive($query)
    {
        return $query->where(function ($query) {
            $query->where(self::FIELD_EXPIRES_AT, '>', Carbon::now())
                ->orWhereNull(self::FIELD_EXPIRES_AT);
        });
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * @return BelongsTo
     */
    public function creditType(): BelongsTo
    {
        return $this->belongsTo(CreditType::class, Credit::FIELD_CREDIT_TYPE, CreditType::FIELD_SLUG);
    }

    public function approvals(): MorphMany
    {
        return $this->morphMany(ActionApproval::class, ActionApproval::RELATION_APPROVABLE);
    }

    public function billingProfiles(): HasManyThrough
    {
        return $this->hasManyThrough(
            BillingProfile::class,
            CreditBillingProfile::class,
            CreditBillingProfile::FIELD_CREDIT_ID,
            BillingProfile::FIELD_ID,
            self::FIELD_ID,
            CreditBillingProfile::FIELD_BILLING_PROFILE_ID,
        );
    }
}
