<?php

namespace App\Models\Billing;

use App\Models\BaseModel;
use App\Traits\Uuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InvoicePaymentCharge extends BaseModel
{
    use HasFactory, Uuid;

    const string TABLE = 'invoice_payment_charges';

    const string FIELD_ID                 = 'id';
    const string FIELD_UUID               = 'uuid';
    const string FIELD_INVOICE_PAYMENT_ID = 'invoice_payment_id';
    const string FIELD_PAYMENT_METHOD_ID  = 'payment_method_id';
    const string FIELD_TRANSACTION_UUID   = 'transaction_uuid';
    const string FIELD_TOTAL              = 'total';
    const string FIELD_STATUS             = 'status';
    const string FIELD_ERROR_MESSAGE      = 'error_message';

    const string RELATION_PAYMENT_METHOD = 'paymentMethod';

    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';

    protected $guarded = [
        self::FIELD_ID
    ];

    /**
     * @return BelongsTo
     */
    public function paymentMethod(): BelongsTo
    {
        return $this
            ->belongsTo(CompanyPaymentMethod::class, self::FIELD_PAYMENT_METHOD_ID, CompanyPaymentMethod::FIELD_ID)
            ->withTrashed();
    }

    /**
     * @return BelongsTo
     */
    public function payment(): BelongsTo
    {
        return $this->belongsTo(InvoicePayment::class, self::FIELD_INVOICE_PAYMENT_ID, InvoicePayment::FIELD_ID);
    }
}
