<?php

namespace App\Models\Billing;

use App\Contracts\Services\PaymentGatewayServiceContract;
use App\Enums\Billing\BillingVersion;
use App\Enums\Billing\PaymentMethodServices;
use App\Enums\Campaigns\CampaignStatus;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Legacy\EloquentCompany;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\User;
use App\Services\DatabaseHelperService;
use App\Services\PaymentGateway\PaymentGatewayServiceFactory;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * @property int $id
 * @property string payment_gateway_client_code
 * @property string payment_gateway_payment_method_code
 * @property PaymentMethodServices payment_method
 * @property string billing_contact
 * @property string $billing_frequency_cron
 * @property array $cron_data
 * @property int $campaign_id
 * @property int $company_id
 * @property int $threshold_in_dollars
 * @property int $max_allowed_charge_attempts
 * @property int $created_by_id
 * @property Carbon $last_billed_at
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property bool $default
 * @property bool $verified
 * @property bool $process_auto
 * @property int $due_in_days
 * @property int $invoice_template_id
 *
 * @property-read CompanyCampaign $campaign
 * @property-read Company $company
 * @property-read CompanyUser $createdBy
 * @property-read array<BillingProfilePolicy> $policies
 * @property-read InvoiceTemplate $invoiceTemplate
 *
 * @method static Builder onlyV2Companies()
 * @method static Builder active()
 */
class BillingProfile extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    const string TABLE = 'billing_profiles';

    const string FIELD_ID                                  = 'id';
    const string FIELD_NAME                                = 'name';
    const string FIELD_PAYMENT_GATEWAY_CLIENT_CODE         = 'payment_gateway_client_code';
    const string FIELD_PAYMENT_GATEWAY_PAYMENT_METHOD_CODE = 'payment_gateway_payment_method_code';
    const string FIELD_PAYMENT_METHOD                      = 'payment_method';
    const string FIELD_BILLING_FREQUENCY_CRON              = 'billing_frequency_cron';
    const string FIELD_CRON_DATA                           = 'cron_data';
    const string FIELD_COMPANY_ID                          = 'company_id';
    const string FIELD_THRESHOLD_IN_DOLLARS                = 'threshold_in_dollars';
    const string FIELD_MAX_ALLOWED_CHARGE_ATTEMPTS         = 'max_allowed_charge_attempts';
    const string FIELD_CREATED_BY_ID                       = 'created_by_id';
    const string FIELD_UPDATED_BY_ID                       = 'updated_by_id';
    const string FIELD_LAST_BILLED_AT                      = 'last_billed_at';
    const string FIELD_PROCESS_AUTO                        = 'process_auto';
    const string FIELD_DEFAULT                             = 'default';
    const string FIELD_PAYMENT_METHOD_ID                   = 'payment_method_id';
    const string FIELD_VERIFIED                            = 'verified';
    const string FIELD_CREATED_AT                          = 'created_at';
    const string FIELD_UPDATED_AT                          = 'updated_at';
    const string FIELD_DELETED_AT                          = 'deleted_at';
    const string FIELD_DUE_IN_DAYS                         = 'due_in_days';
    const string FIELD_INVOICE_TEMPLATE_ID                 = 'invoice_template_id';
    const string FIELD_ARCHIVED_BY                         = 'archived_by';
    const string FIELD_ARCHIVED_AT                         = 'archived_at';

    const string RELATION_POLICIES         = 'policies';
    const string RELATION_CAMPAIGNS        = 'campaigns';
    const string RELATION_CONTACT          = 'contact';
    const string RELATION_COMPANY          = 'company';
    const string RELATION_INVOICE_TEMPLATE = 'invoiceTemplate';


    protected $guarded = [
        self::FIELD_ID
    ];

    protected $casts = [
        self::FIELD_PAYMENT_METHOD => PaymentMethodServices::class,
        self::FIELD_CRON_DATA      => 'array'
    ];

    /**
     * @return PaymentGatewayServiceContract
     */
    public function getPaymentGatewayService(): PaymentGatewayServiceContract
    {
        return PaymentGatewayServiceFactory::make($this->{BillingProfile::FIELD_PAYMENT_METHOD});
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeOnlyV2Companies(Builder $query): Builder
    {
        return $query
            ->select(BillingProfile::TABLE . '.*')
            ->join(Company::TABLE, Company::TABLE . '.' . Company::FIELD_ID, BillingProfile::TABLE . '.' . BillingProfile::FIELD_COMPANY_ID)
            ->leftJoin(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentCompany::TABLE, DatabaseHelperService::readOnlyDatabase() . '.' . EloquentCompany::TABLE . '.' . EloquentCompany::COMPANY_ID, Company::TABLE . '.' . Company::FIELD_LEGACY_ID)
            ->where(function (Builder $query) {
                $query
                    ->where(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentCompany::TABLE . '.' . EloquentCompany::BILLING_VERSION, BillingVersion::V2)
                    ->orWhereNull(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentCompany::TABLE . '.' . EloquentCompany::BILLING_VERSION);
            });
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->whereNull(self::FIELD_ARCHIVED_AT);
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(CompanyUser::class, self::FIELD_CREATED_BY_ID, CompanyUser::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function policies(): HasMany
    {
        return $this->hasMany(BillingProfilePolicy::class, BillingProfilePolicy::FIELD_BILLING_PROFILE_ID, self::FIELD_ID);
    }

    /**
     * @return BelongsToMany
     */
    public function campaigns(): BelongsToMany
    {
        return $this->belongsToMany(
            CompanyCampaign::class,
            CampaignBillingProfile::TABLE,
            CampaignBillingProfile::FIELD_BILLING_PROFILE_ID,
            CampaignBillingProfile::FIELD_CAMPAIGN_ID
        )->wherePivotNull('deleted_at');
    }

    /**
     * @return Collection
     */
    public function getActiveCampaigns(): Collection
    {
        return $this->campaigns()
            ->where(CompanyCampaign::FIELD_STATUS, CampaignStatus::ACTIVE)
            ->get();
    }

    /**
     * @return HasOne
     */
    public function invoiceTemplate(): HasOne
    {
        return $this->hasOne(
            InvoiceTemplate::class,
            InvoiceTemplate::FIELD_ID,
            self::FIELD_INVOICE_TEMPLATE_ID
        );
    }

    /**
     * @return HasOne
     */
    public function paymentMethod(): HasOne
    {
        return $this->hasOne(
            CompanyPaymentMethod::class,
            CompanyPaymentMethod::FIELD_ID,
            self::FIELD_PAYMENT_METHOD_ID
        );
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll()
            ->logExcept([
                self::FIELD_LAST_BILLED_AT,
                self::FIELD_UPDATED_AT
            ])
            ->logOnlyDirty()
            ->useLogName('billing_profiles')
            ->dontSubmitEmptyLogs();
    }

    public function archivedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_ARCHIVED_BY, User::FIELD_ID)->withTrashed();
    }

    public function credits(): HasManyThrough
    {
        return $this->hasManyThrough(
            Credit::class,
            CreditBillingProfile::class,
            CreditBillingProfile::FIELD_BILLING_PROFILE_ID,
            Credit::FIELD_ID,
            self::FIELD_ID,
            CreditBillingProfile::FIELD_CREDIT_ID,
        );
    }

    public function getTotalOutstanding()
    {
        return $this->credits()->active()->sum(Credit::FIELD_REMAINING_VALUE);
    }
}
