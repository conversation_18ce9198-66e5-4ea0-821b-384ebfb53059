<?php

namespace App\Models\Billing;

use App\Models\Odin\Company;
use App\States\InvoiceState;
use App\Traits\Uuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Spatie\EventSourcing\Projections\Projection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $uuid
 * @property float $total
 * @property string $company_id
 * @property string $invoice_id
 * @property string $generated_at
 * @property string $due_at
 * @property string $payment_method
 * @property string $invoice_url
 *
 * @property-read InvoiceState $status
 */
class CompanyInvoice extends Projection
{
    use Uuid, HasFactory;

    const string TABLE = 'company_invoices';

    const string FIELD_ID             = 'id';
    const string FIELD_UUID           = 'uuid';
    const string FIELD_TOTAL          = 'total';
    const string FIELD_COMPANY_ID     = 'company_id';
    const string FIELD_INVOICE_ID     = 'invoice_id';
    const string FIELD_STATUS         = 'status';
    const string FIELD_GENERATED_AT   = 'generated_at';
    const string FIELD_DUE_AT         = 'due_at';
    const string FIELD_PAYMENT_METHOD = 'payment_method';
    const string FIELD_INVOICE_URL    = 'invoice_url';
    const string FIELD_CREATED_AT     = 'created_at';
    const string FIELD_UPDATED_AT     = 'updated_at';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID,
    ];

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * @return HasOne
     */
    public function invoice(): HasOne
    {
        return $this->hasOne(Invoice::class);
    }
}
