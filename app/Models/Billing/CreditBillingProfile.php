<?php

namespace App\Models\Billing;

use Illuminate\Database\Eloquent\Model;


class CreditBillingProfile extends Model
{
    const string TABLE = 'credit_billing_profiles';

    const string FIELD_ID                 = 'id';
    const string FIELD_CREDIT_ID          = 'credit_id';
    const string FIELD_BILLING_PROFILE_ID = 'billing_profile_id';
    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';


    protected $guarded = [
        self::FIELD_ID
    ];
}
