<?php

namespace App\Models\Billing;

use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InvoiceCredit extends BaseModel
{
    const string TABLE = 'invoice_credits';

    const string FIELD_ID             = 'id';
    const string FIELD_INVOICE_ID     = 'invoice_id';
    const string FIELD_CREDIT_ID      = 'credit_id';
    const string FIELD_AMOUNT_APPLIED = 'amount_applied';
    const string FIELD_AUTHOR_ID      = 'author_id';
    const string FIELD_AUTHOR_TYPE    = 'author_type';
    const string FIELD_APPLIED_AT     = 'applied_at';
    const string FIELD_CREATED_AT     = 'created_at';
    const string FIELD_UPDATED_AT     = 'updated_at';

    const string RELATION_INVOICE = 'invoice';
    const string RELATION_CREDIT  = 'credit';

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $casts = [
        'author_type' => InvoiceEventAuthorTypes::class
    ];

    /**
     * @return BelongsTo
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class, self::FIELD_INVOICE_ID, Invoice::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function credit(): BelongsTo
    {
        return $this->belongsTo(Credit::class, self::FIELD_CREDIT_ID, Credit::FIELD_ID);
    }
}
