<?php

namespace App\Models\Billing;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;


class InvoiceTemplate extends Model
{
    use HasFactory;

    const string TABLE = 'invoice_templates';

    const string FIELD_ID            = 'id';
    const string FIELD_NAME          = 'name';
    const string FIELD_IS_GLOBAL     = 'is_global';
    const string FIELD_MODEL_TYPE    = 'model_type';
    const string FIELD_MODEL_ID      = 'model_id';
    const string FIELD_PROPS         = 'props';
    const string FIELD_CREATED_BY_ID = 'created_by_id';

    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';

    protected $casts = [
        self::FIELD_PROPS => 'array'
    ];

    protected $guarded = [
        self::FIELD_ID
    ];

    /**
     * @return MorphTo
     */
    public function model(): MorphTo
    {
        return $this->morphTo(__FUNCTION__, self::FIELD_MODEL_TYPE, self::FIELD_MODEL_ID);
    }
}
