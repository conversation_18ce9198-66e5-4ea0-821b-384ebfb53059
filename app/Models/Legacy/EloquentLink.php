<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Casts\Attribute;

/**
 * @property int $linkid
 * @property string $reltype
 * @property int $relid
 * @property string $linktype
 * @property string $linkvalue
 *
 * @property-read string $url
 * @property-read string $type
 * @property-read string $svg
 */
class EloquentLink extends LegacyModel
{
    const TABLE = 'tbllink';

    const LINK_ID = 'linkid';
    const REL_TYPE = 'reltype';
    const REL_ID = 'relid';
    const LINK_TYPE = 'linktype';
    const LINK_VALUE = 'linkvalue';

    const CREATED_AT = 'timestampadded';

    const UPDATED_AT = 'timestampupdated';

    const REL_TYPE_COMPANY_ID = 'companyid';

    protected $table = self::TABLE;

    protected $fillable = [
        self::LINK_TYPE,
        self::LINK_VALUE,
        self::REL_TYPE,
    ];

    protected $dateFormat = 'U';

    public function url(): Attribute
    {
        return Attribute::make(fn() => $this->linkvalue);
    }

    public function type(): Attribute
    {
        $result = null;

        if (str_contains($this->linkvalue, 'youtube.com')) {
            $result = 'youtube';
        } else if (str_contains($this->linkvalue, 'twitter.com') || str_contains($this->linkvalue, 'x.com')) {
            $result = 'twitter';
        } else if (str_contains($this->linkvalue, 'linkedin.com')) {
            $result = 'linkedin';
        } else if (str_contains($this->linkvalue, 'facebook.com')) {
            $result = 'facebook';
        } else if (str_contains($this->linkvalue, 'blogger.com')) {
            $result = 'blogger';
        } else if (str_contains($this->linkvalue, 'pinterest.com')) {
            $result = 'pinterest';
        } else if (str_contains($this->linkvalue, 'plus.google.com')) {
            $result = 'googleplus';
        } else {
            $result = 'default';
        }

        return Attribute::make(fn() => $result);
    }

    public function svg(): Attribute
    {
        return Attribute::make(fn() => data_get($this->svgs(), $this->type) ?? $this->svgs()['default']);
    }

    private function svgs()
    {
        return [
            'youtube' => '<svg class="cursor-pointer w-4 h-4 flex-shrink-0" width="21" height="15" viewBox="0 0 21 15" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M19.607 2.20301C19.4931 1.78041 19.2705 1.39501 18.9614 1.08518C18.6522 0.775338 18.2673 0.551868 17.845 0.437007C16.279 0.00700739 10.014 7.59651e-06 10.014 7.59651e-06C10.014 7.59651e-06 3.75 -0.00699261 2.183 0.404007C1.76093 0.524154 1.37682 0.750785 1.06757 1.06214C0.758311 1.3735 0.534287 1.75913 0.417002 2.18201C0.00400165 3.74801 1.46514e-06 6.99601 1.46514e-06 6.99601C1.46514e-06 6.99601 -0.00399852 10.26 0.406001 11.81C0.636001 12.667 1.311 13.344 2.169 13.575C3.751 14.005 9.999 14.012 9.999 14.012C9.999 14.012 16.264 14.019 17.83 13.609C18.2525 13.4943 18.6377 13.2714 18.9477 12.9622C19.2576 12.653 19.4814 12.2682 19.597 11.846C20.011 10.281 20.014 7.03401 20.014 7.03401C20.014 7.03401 20.034 3.76901 19.607 2.20301ZM8.01 10.005L8.015 4.00501L13.222 7.01001L8.01 10.005Z" fill="currentColor" /> </svg>',
            'twitter' => '<svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M2.54173 0.0874634C1.68399 0.0874634 0.989258 0.782195 0.989258 1.63994V15.6122C0.989258 16.4699 1.68399 17.1647 2.54173 17.1647H16.514C17.3717 17.1647 18.0665 16.4699 18.0665 15.6122V1.63994C18.0665 0.782195 17.3717 0.0874634 16.514 0.0874634H2.54173ZM4.59755 3.96865H8.17703L10.2662 6.95534L12.8511 3.96865H13.9776L10.7726 7.68003L14.6932 13.2835H11.1137L8.79559 9.96932L5.93322 13.2835H4.78857L8.28619 9.24312L4.59755 3.96865ZM6.32892 4.8874L11.5715 12.3602H12.9603L7.71614 4.8874H6.32892Z" fill="currentColor" /> </svg>',
            'linkedin' => '<svg class="cursor-pointer w-4 h-4 flex-shrink-0" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M1.32205 2.64411C2.05221 2.64411 2.64411 2.05221 2.64411 1.32205C2.64411 0.591904 2.05221 0 1.32205 0C0.591904 0 0 0.591904 0 1.32205C0 2.05221 0.591904 2.64411 1.32205 2.64411Z" fill="currentColor" /> <path d="M3.89233 3.64593V10.9807H6.16967V7.35348C6.16967 6.39638 6.34973 5.4695 7.53644 5.4695C8.70684 5.4695 8.72134 6.56376 8.72134 7.41391V10.9813H10.9999V6.95892C10.9999 4.98309 10.5745 3.46466 8.26514 3.46466C7.15638 3.46466 6.41318 4.07312 6.10925 4.64895H6.07844V3.64593H3.89233V3.64593ZM0.181152 3.64593H2.46212V10.9807H0.181152V3.64593Z" fill="currentColor" /> </svg>',
            'facebook' => '<svg class="cursor-pointer w-4 h-4 flex-shrink-0" viewBox="0 0 5 10" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M3.24818 10V5.44595H4.72901L4.94912 3.66289H3.24818V2.52716C3.24818 2.01263 3.38635 1.66036 4.09812 1.66036H5V0.0706607C4.56118 0.0218702 4.1201 -0.00168805 3.67877 9.3996e-05C2.36986 9.3996e-05 1.47119 0.829113 1.47119 2.35102V3.65956H0V5.44262H1.4744V10H3.24818Z" fill="currentColor" /> </svg>',
            'blogger' => '<svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M18.6427 8.26319C18.5707 7.94119 18.3707 7.64219 18.1407 7.51819C18.0707 7.47919 17.6187 7.43019 17.1367 7.40919C16.3277 7.37319 16.2387 7.35719 15.9847 7.20819C15.5797 6.97119 15.4687 6.71519 15.4667 6.02119C15.4647 4.69419 14.9127 3.46219 13.8207 2.35119C13.0447 1.55819 12.1757 1.02219 11.1867 0.722191C10.9507 0.650191 10.4187 0.625191 8.64171 0.604191C5.85471 0.571191 5.23671 0.628191 4.28571 1.00619C2.53771 1.70319 1.27771 3.17219 0.820707 5.05619C0.733707 5.40919 0.717707 5.97619 0.696707 9.23319C0.671707 13.3132 0.700707 13.9132 0.954707 14.7212C1.16671 15.3892 1.37971 15.7982 1.81571 16.3782C2.65071 17.4862 3.89871 18.2852 5.14971 18.5112C5.74471 18.6182 13.0807 18.6462 13.8327 18.5432C15.1387 18.3652 16.1637 17.8412 17.1257 16.8592C17.8197 16.1492 18.2547 15.3802 18.5397 14.3602C18.6567 13.9362 18.6667 13.7302 18.6887 11.2432C18.7057 9.36519 18.6907 8.48519 18.6427 8.26319ZM5.69571 5.70419C6.00871 5.38819 6.09471 5.37519 8.05971 5.37519C9.82371 5.37519 9.88171 5.37919 10.1407 5.50919C10.5157 5.69819 10.6787 5.96519 10.6787 6.38919C10.6787 6.77319 10.5257 7.04219 10.1857 7.25819C10.0017 7.37319 9.89271 7.38119 8.16471 7.39119C7.09771 7.39819 6.24871 7.37819 6.12171 7.34319C5.45271 7.15919 5.20371 6.20019 5.69571 5.70419ZM13.4017 13.7412L12.8047 13.8392L9.69071 13.8742C6.95471 13.9072 6.17971 13.8562 6.03871 13.7942C5.75071 13.6702 5.48471 13.3222 5.43671 13.0142C5.39471 12.7222 5.54071 12.3182 5.76671 12.1142C6.05171 11.8572 6.17571 11.8482 9.67771 11.8442C13.2797 11.8422 13.2607 11.8412 13.6027 12.1592C14.0847 12.6092 13.9837 13.4102 13.4017 13.7412Z" fill="currentColor" /> </svg>',
            'pinterest' => '<svg width="15" height="19" viewBox="0 0 15 19" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M0.490786 7.06755C0.490786 6.28955 0.626786 5.55455 0.894786 4.86855C1.15084 4.20225 1.53126 3.59072 2.01579 3.06655C2.49932 2.54926 3.05265 2.10195 3.65979 1.73755C4.2827 1.3607 4.95708 1.0764 5.66179 0.893553C6.375 0.706274 7.1094 0.611827 7.84679 0.612553C8.98579 0.612553 10.0458 0.853553 11.0288 1.33355C11.999 1.8048 12.8247 2.528 13.4198 3.42755C14.0338 4.34255 14.3388 5.37755 14.3388 6.53155C14.3388 7.22355 14.2708 7.90055 14.1318 8.56255C13.9953 9.22418 13.7784 9.86663 13.4858 10.4756C13.2069 11.065 12.8423 11.6099 12.4038 12.0926C11.9631 12.5675 11.4293 12.9467 10.8358 13.2066C10.1914 13.489 9.49431 13.6311 8.79079 13.6236C8.30179 13.6236 7.81379 13.5086 7.33179 13.2776C6.84979 13.0476 6.50379 12.7316 6.29579 12.3266C6.22279 12.6076 6.12279 13.0136 5.98979 13.5446C5.86179 14.0746 5.77579 14.4166 5.73779 14.5716C5.69779 14.7256 5.62379 14.9826 5.51579 15.3386C5.44254 15.6019 5.34858 15.859 5.23479 16.1076L4.89079 16.7816C4.74202 17.0708 4.57571 17.3506 4.39279 17.6196C4.21179 17.8816 3.98779 18.1946 3.72079 18.5546L3.57179 18.6076L3.47279 18.4996C3.36579 17.3666 3.31079 16.6886 3.31079 16.4646C3.31079 15.8016 3.38979 15.0576 3.54579 14.2316C3.69879 13.4066 3.94079 12.3696 4.26579 11.1226C4.59079 9.87655 4.77679 9.14355 4.82679 8.92655C4.59779 8.45955 4.48179 7.84955 4.48179 7.09955C4.48179 6.50055 4.66879 5.93955 5.04379 5.41155C5.41979 4.88555 5.89479 4.62255 6.47079 4.62255C6.91179 4.62255 7.25379 4.76855 7.49879 5.06155C7.74479 5.35355 7.86479 5.72155 7.86479 6.17055C7.86479 6.64655 7.70679 7.33555 7.38879 8.23655C7.07079 9.13855 6.91279 9.81155 6.91279 10.2586C6.91279 10.7116 7.07479 11.0906 7.39879 11.3876C7.71853 11.6847 8.14138 11.8457 8.57779 11.8366C8.97379 11.8366 9.34079 11.7466 9.68179 11.5656C10.0175 11.3899 10.308 11.139 10.5308 10.8326C11.016 10.1668 11.3619 9.41008 11.5478 8.60755C11.6438 8.18555 11.7178 7.78455 11.7638 7.40755C11.8128 7.02855 11.8338 6.67055 11.8338 6.33055C11.8338 5.08355 11.4378 4.11155 10.6508 3.41555C9.85979 2.71955 8.82979 2.37355 7.56279 2.37355C6.12179 2.37355 4.91679 2.83955 3.95179 3.77455C2.98579 4.70655 2.49979 5.89155 2.49979 7.32855C2.49979 7.64555 2.54779 7.95155 2.63879 8.24755C2.72779 8.54255 2.82479 8.77755 2.92979 8.95155C3.03379 9.12255 3.13179 9.28955 3.22079 9.44355C3.31079 9.59755 3.35779 9.70755 3.35779 9.77355C3.35779 9.97555 3.30479 10.2386 3.19779 10.5636C3.08679 10.8886 2.95579 11.0506 2.79779 11.0506C2.78279 11.0506 2.72079 11.0396 2.61279 11.0166C2.23875 10.9049 1.90096 10.6961 1.63379 10.4116C1.35022 10.1166 1.12644 9.76956 0.974786 9.38955C0.823564 9.01091 0.705781 8.61974 0.622786 8.22055C0.533118 7.84283 0.488805 7.45577 0.490786 7.06755Z" fill="currentColor" /> </svg>',
            'googleplus' => '<svg width="26" height="16" viewBox="0 0 26 16" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M22.5412 11.3811V8.35595H25.5676V6.08805H22.5412V3.06293H20.2733V6.08805H17.2482V8.35595H20.2733V11.3811H22.5412ZM8.18034 2.98046C9.59606 2.98046 10.5495 3.58524 11.0917 4.10004L13.2434 2.01332C11.9339 0.788782 10.2258 0.0390628 8.18034 0.0390628C4.0094 0.0378133 0.618164 3.42655 0.618164 7.60124C0.618164 11.7759 4.0094 15.1647 8.18034 15.1647C12.545 15.1647 15.4426 12.0946 15.4426 7.77617C15.4426 7.14766 15.3627 6.70157 15.2514 6.22675H8.17659V9.0457H12.34C12.1426 10.234 11.0768 12.2395 8.18034 12.2395C5.67752 12.2395 3.63579 10.159 3.63579 7.61123C3.63579 5.05969 5.67752 2.98046 8.18034 2.98046Z" fill="currentColor" /> </svg>',
            'default' => '<svg class="cursor-pointer w-4 h-4 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"> <path fill="currentColor" fill-rule="evenodd" d="M19.902 4.098a3.75 3.75 0 00-5.304 0l-4.5 4.5a3.75 3.75 0 001.035 *********** 0 01-.646 1.353 5.25 5.25 0 01-1.449-8.45l4.5-4.5a5.25 5.25 0 117.424 7.424l-1.757 1.757a.75.75 0 11-1.06-1.06l1.757-1.757a3.75 3.75 0 000-5.304zm-7.389 4.267a.75.75 0 011-.353 5.25 5.25 0 011.449 8.45l-4.5 4.5a5.25 5.25 0 11-7.424-7.424l1.757-1.757a.75.75 0 111.06 1.06l-1.757 1.757a3.75 3.75 0 105.304 5.304l4.5-4.5a3.75 3.75 0 00-1.035-*********** 0 01-.354-1z" clip-rule="evenodd" /> </svg>',
        ];
    }
}
