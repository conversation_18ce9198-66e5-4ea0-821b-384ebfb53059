<?php

namespace App\Models\Legacy;

/**
 * Class CrmDeliveryLog
 *
 * @package App
 *
 * @property int $id
 * @property int $crm_integration_id
 * @property string $crm_name
 * @property int $quoteid
 * @property int $quotecompanyid
 * @property int $companyid
 * @property string $delivery_data
 * @property int $delivery_status
 * @property string $error_log
 * @property int $send_test_lead
 * @property int $added_by_user_id
 * @property int $timestamp_added
 * @property float $cost
 */
class CrmDeliveryLog extends LegacyModel
{
    const TABLE = 'tbl_crm_delivery_log';
    protected $table = self::TABLE;

    const ID                 = 'id';
    const CRM_INTEGRATION_ID = 'crm_integration_id';
    const CRM_NAME           = 'crm_name';
    const QUOTE_ID           = 'quoteid';
    const QUOTE_COMPANY_ID   = 'quotecompanyid';
    const COMPANY_ID         = 'companyid';
    const DELIVERY_STATUS    = 'delivery_status';
    const DELIVERY_DATA      = 'delivery_data';
    const ERROR_LOG          = 'error_log';
    const SEND_TEST_LEAD     = 'send_test_lead';
    const ADDED_BY_USER_ID   = 'added_by_user_id';
    const TIMESTAMP_ADDED    = 'timestamp_added';
    const COST               = 'cost';

    const VALUE_DELIVERY_STATUS_SUCCESS = 1;
    const VALUE_DELIVERY_STATUS_FAILED = 0;

    protected $guarded = [self::ID];

    public $timestamps = false;
}
