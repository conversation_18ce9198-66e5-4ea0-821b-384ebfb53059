<?php
namespace App\Models\Legacy;
use DateTime;
use Illuminate\Contracts\Filesystem\Filesystem;
use Illuminate\Contracts\Filesystem\Factory as FilesystemFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Http\UploadedFile;
/**
 * Class ReviewAttachment
 *
 * @package App
 *
 * @property int            $id
 * @property int            $review_id
 * @property string         $name
 * @property string         $path
 * @property string         $extension
 * @property string         $reference
 * @property string         $file
 * @property boolean        $public
 * @property DateTime      $created_at
 * @property DateTime      $updated_at
 *
 * @property EloquentReview $review
 */
class EloquentReviewAttachment extends LegacyModel
{
    const REFERENCE_TYPE                    = 'uuid4';
    const STORAGE_DISK_REVIEW_ATTACHMENT    = 'review_attachments';
    const STORAGE_DISK_REVIEW_PHOTOS        = 'review_photos';
    const ATTRIBUTE_ID                      = 'id';
    const ATTRIBUTE_REFERENCE               = 'reference';
    const ATTRIBUTE_REVIEW_ID               = 'review_id';
    const ATTRIBUTE_NAME                    = 'name';
    const ATTRIBUTE_PATH                    = 'path';
    const ATTRIBUTE_EXTENSION               = 'extension';
    const ATTRIBUTE_CREATED_AT              = 'created_at';
    const ATTRIBUTE_UPDATED_AT              = 'updated_at';
    const ATTRIBUTE_FILE                    = 'file';
    const ATTRIBUTE_PUBLIC                  = 'public';

    public $table      = "review_attachments";
    public $timestamps = true;

    /** @var FilesystemFactory */
    protected $filesystemManager;

    protected $fillable = [
        self::ATTRIBUTE_PATH,
        self::ATTRIBUTE_REVIEW_ID,
        self::ATTRIBUTE_NAME
    ];

    protected $casts = [
        self::ATTRIBUTE_PUBLIC => 'boolean'
    ];

    /**
     * @return BelongsTo
     */
    public function review(): BelongsTo
    {
        return $this->belongsTo(EloquentReview::class, EloquentReview::FIELD_REVIEW_ID, self::ATTRIBUTE_REVIEW_ID);
    }

    /**
     * Get cloudfront url for the public attachment
     *
     * @return string|null
     */
    public function cloudfrontUrl(): ?string
    {
        if (!$this->public) {
            return null;
        }
        return rtrim(config('filesystems.cloudfront_url'), '/') . '/' . self::STORAGE_DISK_REVIEW_PHOTOS . '/' . $this->path;
    }
}
