<?php

namespace App\Models\Legacy;

use App\Enums\EloquentTransactionType;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentInvoiceItem;
use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Class EloquentInvoice
 *
 * @package App
 *
 * @property int $invoiceid
 * @property int $companyid
 * @property int $timestampadded
 * @property string $period
 * @property string $status
 * @property bool $processing
 * @property int $paymentmethod
 * @property int $timestampstatusupdated
 * @property int $timestamppaymentdue
 * @property bool $autobilling
 * @property bool $iscredit
 *
 * @property-read EloquentCompany|null $company
 * @property-read Collection|EloquentInvoiceItem[] $invoiceItems
 */

class EloquentInvoice extends LegacyModel
{
    const ID = self::INVOICE_ID;
    const INVOICE_ID = 'invoiceid';
    const COMPANY_ID = 'companyid';
    const TIMESTAMP_ADDED = 'timestampadded';
    const PERIOD = 'period';
    const STATUS = 'status';
    const TIMESTAMP_STATUS_UPDATED = 'timestampstatusupdated';
    const TIMESTAMP_PAYMENT_DUE = 'timestamppaymentdue';
    const AUTO_BILLING = 'autobilling';
    const NOTES = 'notes';
    const IS_CREDIT = 'iscredit';
    const PROCESSING = 'processing';
    const PAYMENT_METHOD = 'paymentmethod';

    const VALUE_STATUS_INITIAL = 'initial';
    const VALUE_STATUS_PROCESSING = 'processing';
    const VALUE_STATUS_PAID = 'paid';
    const VALUE_STATUS_PAID_FAILED = 'failed';
    const VALUE_STATUS_COLLECTION = 'collection';
    const VALUE_STATUS_CANCELLED = 'cancelled';
    const VALUE_STATUS_PAUSE = 'pause';
    const VALUE_STATUS_REFUND = 'refund';
    const VALUE_STATUS_BANK_REFUND = 'bank-refund';
    const VALUE_STATUS_CHARGEBACK = 'chargeback';
    const VALUE_STATUS_CHARGEBACK_LOST = 'chargeback-lost';
    const VALUE_STATUS_CHARGEBACK_WON = 'chargeback-won';
    const VALUE_IS_CREDIT = '1';
    const VALUE_STATUS_SETUP = 'setup';
    const VALUE_PAYMENT_TYPE_CREDIT_CARD = '1';
    const VALUE_PAYMENT_TYPE_BANK_ACCOUNT = '2';
    const VALUE_PAYMENT_TYPE_MIXED= '3';
    const VALUE_PAYMENT_TYPE_OTHER= '4';

    const TABLE = 'tblinvoice';

    const RELATION_COMPANY = 'company';
    const RELATION_INVOICE_ITEMS = 'invoiceItems';

    protected $table = self::TABLE;

    protected $primaryKey = self::ID;

    public $timestamps = false;

    protected $casts = [
        self::AUTO_BILLING => 'boolean',
    ];

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(EloquentCompany::class, EloquentCompany::ID, self::COMPANY_ID);
    }

    /**
     * @return HasMany
     */
    public function invoiceItems(): HasMany
    {
        return $this->hasMany(EloquentInvoiceItem::class, EloquentInvoiceItem::INVOICE_ID, self::ID);
    }

    /**
     * @return string
     */
    public function getTotalPriceDisplay(): string
    {
        $total = $this->getTotalPrice();
        return '$'. number_format(round($total, 2), 2);
    }

    /**
     * return int
     */
    public function getTotalPrice(): int
    {
        $total = 0.00;
        foreach($this->invoiceItems as $invoiceItem) {
            $total += $invoiceItem->getTotalPrice();
        }
        return $total;
    }

    /**
     * @return string
     */
    public function getInvoicePaymentTypeDisplay() : string
    {
        return match((string) $this->paymentmethod) {
            self::VALUE_PAYMENT_TYPE_CREDIT_CARD => 'Credit Card',
            self::VALUE_PAYMENT_TYPE_BANK_ACCOUNT => 'Bank Account',
            self::VALUE_PAYMENT_TYPE_MIXED => 'Mixed',
            self::VALUE_PAYMENT_TYPE_OTHER => 'Unknown',
            default => $this->status === self::VALUE_STATUS_PAID ? 'Unknown' : 'NA'
        };
    }
}
