<?php
namespace App\Models\Legacy;

/**
 * Class ReviewExtraData
 * @package App
 *
 * @property int $id
 * @property int $review_id
 * @property string|null $product_type
 * @property string|null $model_reference
 * @property float|null $system_size
 * @property float|null $system_price
 * @property bool|null $price_after_incentives
 * @property int|null $company_installed
 * @property int|null $year_installed
 * @property int|null $panel_brand
 * @property int|null $inverter_brand
 *
 */
class EloquentReviewExtraData extends LegacyModel
{
    const TABLE                         = 'review_extra_data';

    const FIELD_ID                      = 'id';
    const FIELD_REVIEW_ID               = 'review_id';
    const FIELD_PRODUCT_TYPE            = 'product_type';
    const FIELD_MODEL_REFERENCE         = 'model_reference';
    const FIELD_SYSTEM_SIZE             = 'system_size';
    const FIELD_SYSTEM_PRICE            = 'system_price';
    const FIELD_PRICE_AFTER_INCENTIVES  = 'price_after_incentives';
    const FIELD_COMPANY_INSTALLED       = 'company_installed';
    const FIELD_YEAR_INSTALLED          = 'year_installed';
    const FIELD_PANEL_BRAND             = 'panel_brand';
    const FIELD_INVERTER_BRAND          = 'inverter_brand';

    const PRODUCT_TYPE_PANEL            = 'panel';
    const PRODUCT_TYPE_INVERTER         = 'inverter';
    const PRODUCT_TYPE_BATTERY          = 'battery';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID,
        self::CREATED_AT,
        self::UPDATED_AT
    ];
}
