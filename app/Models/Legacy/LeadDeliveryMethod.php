<?php

namespace App\Models\Legacy;

use App\Models\Odin\CompanyUser;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasOne;

class LeadDeliveryMethod extends LegacyModel
{

    const TABLE = 'tbl_lead_delivery_methods';

    const FIELD_ID         = 'id';
    const FIELD_CONTACT_ID = 'contact_id';
    const FIELD_COMPANY_ID = 'company_id';
    const FIELD_NAME = 'name';
    const FIELD_PHONE_NUMBER = 'phone_number';
    const FIELD_EMAIL = 'email';

    const RELATION_CONTACT = 'contact';
    const RELATION_COMPANY_CONTACT = 'companyContact';

    protected $table = self::TABLE;

    /**
     * Finds the legacy contact
     * @return HasOne
     */
    public function contact()
    {
        return $this->hasOne(
            EloquentCompanyContact::class,
            EloquentCompanyContact::FIELD_CONTACT_ID,
            self::FIELD_CONTACT_ID
        );
    }

    /**
     * Finds the Admin2.0 contact
     * @return HasOne
     */
    public function companyContact(): HasOne
    {
        return $this->hasOne(CompanyUser::class, CompanyUser::FIELD_LEGACY_ID, self::FIELD_CONTACT_ID)
            ->ofMany([], fn(Builder $query) =>
                $query->where(CompanyUser::FIELD_IS_CONTACT, 1)
            );
    }
}
