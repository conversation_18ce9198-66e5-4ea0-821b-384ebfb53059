<?php

namespace App\Models\Legacy;

use App\Models\Odin\ProductAssignment;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class EloquentInvoiceItem
 * @package App
 * @property int invoiceitemid
 * @property int invoiceid
 * @property string reltype
 * @property int relid
 * @property float quantity
 * @property string description
 * @property float itemextaxprice
 * @property float totaltaxamount
 * @property int timestampadded
 * @property int addedbyuserid
 *
 * @property-read EloquentInvoice $eloquentInvoice
 */

class EloquentInvoiceItem extends LegacyModel
{
    const ID = self::INVOICE_ITEM_ID;
    const INVOICE_ITEM_ID = 'invoiceitemid';
    const INVOICE_ID = 'invoiceid';
    const REL_TYPE = 'reltype';
    const REL_ID = 'relid';
    const QUANTITY = 'quantity';
    const DESCRIPTION = 'description';
    const ITEM_EX_TAX_PRICE = 'itemextaxprice';
    const TOTAL_TAX_AMOUNT = 'totaltaxamount';
    const TIMESTAMP_ADDED = 'timestampadded';
    const ADDED_BY_USER_ID = 'addedbyuserid';

    const RELATION_QUOTE_COMPANY = 'quoteCompanies';
    const RELATION_PRODUCT_ASSIGNMENT = 'product_Assignment';
    const RELATION_ELOQUENT_INVOICE = 'eloquentInvoice';
    const RELATION_ELOQUENT_QUOTE_COMPANY = 'eloquentQuoteCompanies';

    const REL_TYPE_COMPANY = 'company';

    const VALUE_UNINVOICE_ID = 0;

    const TABLE = 'tblinvoiceitem';

    protected $table = self::TABLE;

    protected $primaryKey = self::ID;

    public $timestamps = false;

    /**
     * @return float
     */
    public function getTotalPrice(): float
    {
        return $this->quantity * ($this->itemextaxprice + $this->totaltaxamount);
    }

    public function eloquentQuoteCompanies(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(EloquentQuoteCompany::class, EloquentQuoteCompany::INVOICE_ITEM_ID, self::ID);
    }
    public function Product_Assignment(): \Illuminate\Database\Eloquent\Relations\HasOneThrough
    {
        return $this->hasOneThrough(
            ProductAssignment::class,
            EloquentQuoteCompany::class,
            EloquentQuoteCompany::INVOICE_ITEM_ID,
            ProductAssignment::FIELD_LEGACY_ID,
            EloquentInvoiceItem::ID,
            EloquentQuoteCompany::ID,
        );
    }

    public function eloquentInvoice(): BelongsTo
    {
        return $this->belongsTo(EloquentInvoice::class, EloquentInvoice::INVOICE_ID, self::INVOICE_ID);
    }
}
