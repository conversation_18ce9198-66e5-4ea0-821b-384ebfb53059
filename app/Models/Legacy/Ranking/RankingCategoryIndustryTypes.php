<?php

namespace App\Models\Legacy\Ranking;

use App\Models\Legacy\IndustryType;
use App\Models\Legacy\LegacyModel;

/**
 * Class RankingCategoryIndustryTypes
 *
 * @property int $id
 * @property int $industry_type_id
 * @property int $category_id
 * @property \DateTime $created_at
 * @property \DateTime $updated_at
 *
 * @property-read IndustryType $industryType
 */
class RankingCategoryIndustryTypes extends LegacyModel
{
    const TABLE = 'ranking_category_industry_types';

    const FIELD_ID               = 'id';
    const FIELD_INDUSTRY_TYPE_ID = 'industry_type_id';
    const FIELD_CATEGORY_ID      = 'category_id';
}
