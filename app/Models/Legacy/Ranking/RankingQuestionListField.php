<?php

namespace App\Models\Legacy\Ranking;

use App\Models\Legacy\LegacyModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class RankingQuestionListField
 *
 * @property int $id
 * @property int $questionId
 * @property string $fieldText
 *
 * @property-read RankingQuestion $question
 */
class RankingQuestionListField extends LegacyModel
{
    use SoftDeletes;

    const TABLE = 'ranking_question_list_fields';

    const FIELD_ID          = 'id';
    const FIELD_QUESTION_ID = 'question_id';
    const FIELD_FIELD_TEXT  = 'field_text';
    const FIELD_UPDATED_AT  = 'updated_at';
    const FIELD_CREATED_AT  = 'created_at';
    const FIELD_DELETED_AT  = 'deleted_at';

    protected $fillable = [
        self::FIELD_ID
    ];

    protected $date = [
        self::FIELD_DELETED_AT
    ];

    /**
     * @return BelongsTo
     */
    public function question(): BelongsTo
    {
        return $this->belongsTo(RankingQuestion::class, self::FIELD_QUESTION_ID);
    }

}
