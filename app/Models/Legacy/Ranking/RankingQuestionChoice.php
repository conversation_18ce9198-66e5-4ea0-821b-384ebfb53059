<?php

namespace App\Models\Legacy\Ranking;

use App\Models\Legacy\LegacyModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class RankingQuestionChoice
 *
 * @property int $id
 * @property string $choice_text
 * @property int $sub_question
 * @property \DateTime $created_at
 * @property \DateTime $updated_at
 *
 * @property-read RankingQuestion $question
 * @property-read RankingQuestion $subQuestion
 */

class RankingQuestionChoice extends LegacyModel
{
    use SoftDeletes;

    const TABLE = 'ranking_question_choices';

    const FIELD_ID                   = 'id';
    const FIELD_QUESTION_ID          = 'question_id';
    const FIELD_CHOICE_TEXT          = 'choice_text';
    const FIELD_SUB_QUESTION         = 'sub_question';
    const FIELD_TIMESTAMP_CREATED_AT = 'created_at';
    const FIELD_TIMESTAMP_UPDATED_AT = 'updated_at';
    const FIELD_TIMESTAMP_DELETED_AT = 'deleted_at';

    const RELATION_QUESTION     = 'question';
    const RELATION_SUB_QUESTION = 'subQuestion';

    protected $fillable = [
        self::FIELD_ID
    ];

    protected $date = [
        self::FIELD_TIMESTAMP_DELETED_AT
    ];

    /**
     * @return BelongsTo
     */
    public function question(): BelongsTo
    {
        return $this->belongsTo(RankingQuestion::class, self::FIELD_QUESTION_ID);
    }

    /**
     * @return HasOne
     */
    public function subQuestion(): HasOne
    {
        return $this->hasOne(RankingQuestion::class, RankingQuestion::FIELD_ID, self::FIELD_SUB_QUESTION);
    }
}
