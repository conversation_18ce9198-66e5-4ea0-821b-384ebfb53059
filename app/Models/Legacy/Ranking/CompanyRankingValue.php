<?php

namespace App\Models\Legacy\Ranking;

use App\Models\Legacy\LegacyModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 *
 * @property int $id
 * @property int $company_id
 * @property int $ranking_category_id
 * @property int $ranking_value
 * @property int $needs_review
 *
 * @property-read RankingCategory $rankingCategory
 *
 */
class CompanyRankingValue extends LegacyModel
{
    use SoftDeletes;

    const TABLE = 'company_ranking_values';

    const FIELD_ID                  = 'id';
    const FIELD_COMPANY_ID          = 'company_id';
    const FIELD_RANKING_CATEGORY_ID = 'ranking_category_id';
    const FIELD_RANKING_VALUE       = 'ranking_value';

    const RELATION_RANKING_CATEGORY = 'rankingCategory';

    protected $table = self::TABLE;

    /**
     * @return BelongsTo
     */
    public function rankingCategory(): BelongsTo
    {
        return $this->belongsTo(RankingCategory::class, self::FIELD_RANKING_CATEGORY_ID, RankingCategory::FIELD_ID);
    }
}
