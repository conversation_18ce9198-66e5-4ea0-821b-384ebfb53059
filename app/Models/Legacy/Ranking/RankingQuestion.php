<?php

namespace App\Models\Legacy\Ranking;

use App\Models\Legacy\LegacyModel;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class RankingQuestion
 *
 * @property int $id
 * @property string $question_text
 * @property int $type_id
 * @property int $parent_question_id
 * @property \DateTime $created_at
 * @property \DateTime $updated_at
 *
 * @property-read RankingCategory $category
 * @property-read RankingQuestionChoice[] $choices
 * @property-read RankingQuestionListField[] $listFields
 * @property-read $companyAnswers
 *
 */
class RankingQuestion extends LegacyModel
{
    use SoftDeletes;

    const TABLE = 'ranking_questions';

    const FIELD_ID                    = 'id';
    const FIELD_CATEGORY_ID           = 'category_id';
    const FIELD_QUESTION_TEXT         = 'question_text';
    const FIELD_TYPE_ID               = 'type_id';
    const FIELD_TARGET_CUSTOMER_GROUP = 'target_customer_group';
    const FIELD_PARENT_QUESTION_ID    = 'parent_question_id';
    const FIELD_TIMESTAMP_DELETED_AT  = 'deleted_at';

    const RELATION_CHOICES     = 'choices';
    const RELATION_LIST_FIELDS = 'listFields';

    protected $fillable = [
        self::FIELD_ID
    ];

    protected $date = [
        self::FIELD_TIMESTAMP_DELETED_AT
    ];

    /**
     * @return HasMany
     */
    public function choices(): HasMany
    {
        return $this->hasMany(RankingQuestionChoice::class, RankingQuestionChoice::FIELD_QUESTION_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function listFields(): HasMany
    {
        return $this->hasMany(RankingQuestionListField::class, RankingQuestionListField::FIELD_QUESTION_ID, self::FIELD_ID);
    }
}
