<?php

namespace App\Models\Legacy\Ranking;

use App\Models\Legacy\LegacyModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class RankingOption
 * @package App\Ranking
 *
 * @property int $id
 * @property int $category_id
 * @property string $description
 * @property int $point_value
 * @property \DateTime $created_at
 * @property \DateTime $updated_at
 *
 * @property-read RankingCategory $category
 */

class RankingOption extends LegacyModel
{
    use SoftDeletes;

    const TABLE = 'ranking_options';

    const FIELD_ID = 'id';
    const FIELD_CATEGORY_ID = 'category_id';
    const FIELD_DESCRIPTION = 'description';
    const FIELD_POINT_VALUE = 'point_value';
    const FIELD_TIMESTAMP_CREATED_AT = 'created_at';
    const FIELD_TIMESTAMP_UPDATED_AT = 'updated_at';
    const FIELD_TIMESTAMP_DELETED_AT = 'deleted_at';

    const RELATION_CATEGORY = 'category';

    protected $fillable = [
        self::FIELD_ID
    ];

    protected $date = [
        self::FIELD_TIMESTAMP_DELETED_AT
    ];

    /**
     * @return BelongsTo
     */
    function category()
    {
        return $this->belongsTo(RankingCategory::class);
    }

}
