<?php

namespace App\Models\Legacy\Ranking;

use App\Models\Legacy\IndustryType;
use App\Models\Legacy\LegacyModel;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class RankingCategory
 * @package App\Ranking
 *
 * @property int $id
 * @property string $display_name
 * @property int $ordinal_position
 * @property bool $hidden
 *
 * @property-read Collection|IndustryType[] $industryTypes
 * @property-read Collection|RankingQuestion[] $rankingQuestions
 * @property-read Collection|RankingOption[] $rankingOptions
 * @property-read Collection|CompanyRankingValue $rankingValues
 */

class RankingCategory extends LegacyModel
{
    use SoftDeletes;

    const TABLE = 'ranking_categories';

    const FIELD_ID                   = 'id';
    const FIELD_DISPLAY_NAME         = 'display_name';
    const FIELD_ORDINAL_POSITION     = 'ordinal_position';
    const FIELD_TIMESTAMP_DELETED_AT = 'deleted_at';
    const FIELD_HIDDEN               = 'hidden';

    const RELATION_INDUSTRY_TYPES  = 'industryTypes';
    const RELATION_QUESTIONS       = 'rankingQuestions';
    const RELATION_RANKING_OPTIONS = 'rankingOptions';
    const RELATION_RANKING_VALUES  = 'rankingValues';

    protected $fillable = [
        self::FIELD_ID
    ];

    protected $date = [
        self::FIELD_TIMESTAMP_DELETED_AT
    ];

    /**
     * @return BelongsToMany
     */
    function industryTypes(): BelongsToMany
    {
        return $this->belongsToMany(IndustryType::class, RankingCategoryIndustryTypes::TABLE, RankingCategoryIndustryTypes::FIELD_CATEGORY_ID, RankingCategoryIndustryTypes::FIELD_INDUSTRY_TYPE_ID);
    }

    /**
     * @return HasMany
     */
    function rankingQuestions(): HasMany
    {
        return $this->hasMany(RankingQuestion::class, RankingQuestion::FIELD_CATEGORY_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function rankingOptions(): HasMany
    {
        return $this->hasMany(RankingOption::class, RankingOption::FIELD_CATEGORY_ID, self::FIELD_ID);
    }

    /**
     * @return mixed
     */
    public function getMaxPoints(): mixed
    {
        $this->max_points = $this->rankingOptions->max(RankingOption::FIELD_POINT_VALUE);

        return $this->max_points;
    }

    /**
     * Defines the relationship between a category & the ranking values for that category.
     *
     * @return HasMany
     */
    public function rankingValues(): HasMany
    {
        return $this->hasMany(CompanyRankingValue::class, CompanyRankingValue::FIELD_RANKING_CATEGORY_ID, self::FIELD_ID);
    }
}
