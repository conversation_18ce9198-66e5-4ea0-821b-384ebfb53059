<?php

namespace App\Models\Legacy\SolarCalculatorEngine;

use App\Models\Legacy\LegacyModel;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class OpenCalculatorInput
 * @package App
 *
 * @property int $id
 * @property int $open_calculator_result_id
 * @property string $zip_code
 * @property int $monthly_bill
 * @property int $electric_offset
 * @property string $utility_uuid
 * @property int $azimuth
 * @property int $tilt
 * @property int $count
 * @property int $month
 * @property string $created_at
 * @property string $updated_at
 *
 * @property-read OpenCalculatorResult|Collection $result
 */
class OpenCalculatorInput extends LegacyModel
{
    const FIELD_ID                        = 'id';
    const FIELD_OPEN_CALCULATOR_RESULT_ID = 'open_calculator_result_id';
    const FIELD_ZIP_CODE                  = 'zip_code';
    const FIELD_MONTHLY_BILL              = 'monthly_bill';
    const FIELD_ELECTRIC_OFFSET           = 'electric_offset';
    const FIELD_UTILITY_UUID              = 'utility_uuid';
    const FIELD_AZIMUTH                   = 'azimuth';
    const FIELD_TILT                      = 'tilt';
    const FIELD_COUNT                     = 'count';
    const FIELD_MONTH                     = 'month';
    const FIELD_CREATED_AT                = 'created_at';
    const FIELD_UPDATE_AT                 = 'updated_at';

    const RELATION_RESULT = 'result';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'open_calculator_inputs';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        self::FIELD_OPEN_CALCULATOR_RESULT_ID,
        self::FIELD_ZIP_CODE,
        self::FIELD_MONTHLY_BILL,
        self::FIELD_ELECTRIC_OFFSET,
        self::FIELD_UTILITY_UUID,
        self::FIELD_AZIMUTH,
        self::FIELD_TILT,
        self::FIELD_COUNT,
        self::FIELD_MONTH,
    ];

    /**
     * @return BelongsTo
     */
    public function result(): BelongsTo
    {
        return $this->belongsTo(OpenCalculatorResult::class, self::FIELD_OPEN_CALCULATOR_RESULT_ID, OpenCalculatorResult::FIELD_ID);
    }
}
