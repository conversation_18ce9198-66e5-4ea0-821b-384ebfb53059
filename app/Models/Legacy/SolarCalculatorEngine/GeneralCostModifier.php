<?php

namespace App\Models\Legacy\SolarCalculatorEngine;

use App\Models\Legacy\LegacyModel;

/**
 * Class GeneralCostModifier
 * @package App
 * @property string $reference
 * @property string $field_name
 * @property string $field_value
 * @property float $multiplier
 */
class GeneralCostModifier extends LegacyModel
{
    const TABLE = 'general_cost_modifiers';

    const FIELD_REFERENCE   = 'reference';
    const FIELD_FIELD_NAME  = 'field_name';
    const FIELD_FIELD_VALUE = 'field_value';
    const FIELD_MULTIPLIER  = 'multiplier';

    const FIELD_NAME_CUSTOMER_SECTOR_TYPE = 'customer_sector_type';
    const FIELD_NAME_IS_DIY               = 'is_diy';
    const FIELD_NAME_SYSTEM_TYPE          = 'system_type';

    public $primaryKey = self::FIELD_REFERENCE;
}
