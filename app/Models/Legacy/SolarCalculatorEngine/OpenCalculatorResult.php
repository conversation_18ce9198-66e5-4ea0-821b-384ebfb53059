<?php

namespace App\Models\Legacy\SolarCalculatorEngine;

use App\Models\Legacy\LegacyModel;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Class OpenCalculatorResult
 * @package App
 *
 * @property int $id
 * @property string $zip_code
 * @property int $monthly_bill
 * @property int $electric_offset
 * @property string $utility_uuid
 * @property int $azimuth
 * @property int $tilt
 * @property int $count
 * @property string $result
 * @property boolean $parsed_inputs
 * @property string $created_at
 * @property string $updated_at
 *
 * @property-read OpenCalculatorInput[]|Collection $inputs
 */
class OpenCalculatorResult extends LegacyModel
{
    const FIELD_ID              = 'id';
    const FIELD_ZIP_CODE        = 'zip_code';
    const FIELD_MONTHLY_BILL    = 'monthly_bill';
    const FIELD_ELECTRIC_OFFSET = 'electric_offset';
    const FIELD_UTILITY_UUID    = 'utility_uuid';
    const FIELD_RESULT          = 'result';
    const FIELD_AZIMUTH         = 'azimuth';
    const FIELD_TILT            = 'tilt';
    const FIELD_COUNT           = 'count';
    const FIELD_PARSED_INPUTS   = 'parsed_inputs';
    const FIELD_CREATED_AT      = 'created_at';
    const FIELD_UPDATE_AT       = 'updated_at';

    const RELATION_INPUTS = 'inputs';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'open_calculator_results';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        self::FIELD_ZIP_CODE,
        self::FIELD_MONTHLY_BILL,
        self::FIELD_ELECTRIC_OFFSET,
        self::FIELD_UTILITY_UUID,
        self::FIELD_RESULT,
        self::FIELD_AZIMUTH,
        self::FIELD_TILT,
        self::FIELD_COUNT,
        self::FIELD_PARSED_INPUTS
    ];

    /**
     * Returns a has many of the installer offers related to this model.
     *
     * @return HasMany
     */
    public function inputs(): HasMany
    {
        return $this->hasMany(OpenCalculatorInput::class, OpenCalculatorInput::FIELD_OPEN_CALCULATOR_RESULT_ID, self::FIELD_ID);
    }
}
