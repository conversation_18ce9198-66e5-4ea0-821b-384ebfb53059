<?php

namespace App\Models\Legacy\SolarCalculatorEngine;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Incentive
 * @package App
 *
 * @property string $code
 * @property string $sector
 * @property string $name
 * @property string $type
 * @property string $url
 * @property string $calc_basis
 * @property float $upfront_dollars_per_watt
 * @property float $upfront_percentage_cost
 * @property float $upfront_dollars_fixed
 * @property float $upfront_capped_kw
 * @property float $upfront_capped_dollars
 * @property float $upfront_capped_percentage
 * @property float $perf_dollars_per_kwh
 * @property int $perf_term_years
 * @property string|null $display_note
 * @property string $reference
 */
class Incentive extends Model
{
    const TABLE = 'incentives';

    const FIELD_CODE                      = 'code';
    const FIELD_SECTOR                    = 'sector';
    const FIELD_TYPE                      = 'type';
    const FIELD_NAME                      = 'name';
    const FIELD_URL                       = 'url';
    const FIELD_CALC_BASIS                = 'calc_basis';
    const FIELD_UPFRONT_DOLLARS_PER_WATT  = 'upfront_dollars_per_watt';
    const FIELD_UPFRONT_PERCENTAGE_COST   = 'upfront_percentage_cost';
    const FIELD_UPFRONT_DOLLARS_FIXED     = 'upfront_dollars_fixed';
    const FIELD_UPFRONT_CAPPED_KW         = 'upfront_capped_kw';
    const FIELD_UPFRONT_CAPPED_DOLLARS    = 'upfront_capped_dollars';
    const FIELD_UPFRONT_CAPPED_PERCENTAGE = 'upfront_capped_percentage';
    const FIELD_PERF_DOLLARS_PER_KWH      = 'perf_dollars_per_kwh';
    const FIELD_PERF_TERM_YEARS           = 'perf_term_years';
    const FIELD_DISPLAY_NOTE              = 'display_note';
    const FIELD_REFERENCE                 = 'reference';
    const FIELD_DELETED_AT                = 'deleted_at';

    const CALC_TYPE_UPFRONT_PER_WATT    = 'dollars-per-watt';
    const CALC_TYPE_UPFRONT_PERCENTAGE  = 'percentage-costs';
    const CALC_TYPE_UPFRONT_FIXED       = 'fixed-amount';
    const CALC_TYPE_PERFORMANCE_PER_KWH = 'performance-per-kwh';
    const CALC_TYPE_DISPLAY_ONLY        = 'display-only';

    const FEDERAL_TAX_CREDIT_INCENTIVE_CODE = 'US37F';

    protected $table = self::TABLE;

    //Guard nothing as all fields will be updated by an API
    protected $guarded = [];

    protected $primaryKey = self::FIELD_REFERENCE;
    public $incrementing = false;
    public $timestamps = false;
}
