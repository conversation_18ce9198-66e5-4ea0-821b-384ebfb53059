<?php

namespace App\Models\Legacy\SolarCalculatorEngine;

use App\Models\Legacy\EloquentUtility;
use App\Models\Legacy\LegacyModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class IncentivesLookup
 * @package App
 *
 * @property string $lookup_type
 * @property string $lookup_value
 * @property string $incentive_reference
 * @property string $reference
 * @property  EloquentUtility|null $utility
 * @property-read  Incentive $incentive
 * @property-read  string $incentive_type
 */
class IncentivesLookup extends LegacyModel
{
    const TABLE = 'incentives_lookups';

    const FIELD_LOOKUP_TYPE         = 'lookup_type';
    const FIELD_LOOKUP_VALUE        = 'lookup_value';
    const FIELD_INCENTIVE_REFERENCE = 'incentive_reference';
    const FIELD_REFERENCE           = 'reference';

    const RELATION_INCENTIVE = 'incentive';

    const LOOKUP_TYPE_COUNTRY = 'Country';
    const LOOKUP_TYPE_STATE   = 'State';
    const LOOKUP_TYPE_UTILITY = 'Utility_EIA_ID';
    const LOOKUP_COUNTRY_US   = 'US';

    protected $table = self::TABLE;

    //Guard nothing as all fields will be updated by an API
    protected $guarded = [];

    protected $primaryKey   = self::FIELD_REFERENCE;
    public    $incrementing = false;
    public    $timestamps   = false;

    /**
     * Get Incentive for this lookup
     *
     * @return BelongsTo
     */
    public function incentive(): BelongsTo
    {
        return $this->belongsTo(Incentive::class, self::FIELD_INCENTIVE_REFERENCE, Incentive::FIELD_REFERENCE);
    }
}
