<?php

namespace App\Models\Legacy\SolarCalculatorEngine;

use App\Models\Legacy\LegacyModel;

/**
 * Class InstallerSystemOffer
 * @package App
 * @property string $reference
 * @property string $installer_reference
 * @property string $panel_reference
 * @property string $inverter_reference
 * @property string $battery_reference
 * @property string $system_type
 * @property string $customer_sector_type
 * @property float $min_size // in kW
 * @property float $max_size // in kW
 * @property string $size_category
 * @property float $cost_per_watt
 * @property boolean $is_active
 * @property boolean $is_diy
 * @property string $deleted_at
 * @property boolean $is_anonymous
 * @property string $state_abbr
 * @property boolean $is_generated
 * @property string $created_at
 * @property string $updated_at
 */
class InstallerSystemOffer extends LegacyModel
{
    const TABLE = 'installer_system_offers';

    const FIELD_REFERENCE                  = 'reference';
    const FIELD_INSTALLER_REFERENCE        = 'installer_reference';
    const FIELD_PANEL_REFERENCE            = 'panel_reference';
    const FIELD_INVERTER_REFERENCE         = 'inverter_reference';
    const FIELD_BATTERY_REFERENCE          = 'battery_reference';
    const FIELD_SYSTEM_TYPE                = 'system_type';
    const FIELD_CUSTOMER_SECTOR_TYPE       = 'customer_sector_type';
    const FIELD_MIN_SIZE                   = 'min_size';
    const FIELD_MAX_SIZE                   = 'max_size';
    const FIELD_SIZE_CATEGORY              = 'size_category';
    const FIELD_COST_PER_WATT              = 'cost_per_watt';
    const FIELD_IS_ACTIVE                  = 'is_active';
    const FIELD_IS_DIY                     = 'is_diy';
    const FIELD_DELETED_AT                 = 'deleted_at';
    const FIELD_IS_ANONYMOUS               = 'is_anonymous';
    const FIELD_STATE_ABBR                 = 'state_abbr';
    const FIELD_IS_GENERATED               = 'is_generated';
    const FIELD_CREATED_AT                 = 'created_at';
    const FIELD_UPDATED_AT                 = 'updated_at';

    const SYSTEM_TYPE_GRID_TIED = 'grid_tied';
    const SYSTEM_TYPE_HYBRID    = 'hybrid';
    const SYSTEM_TYPE_OFF_GRID  = 'off_grid';

    const CUSTOMER_SECTOR_TYPE_RESIDENTIAL = 'residential';
    const CUSTOMER_SECTOR_TYPE_COMMERCIAL = 'commercial';
    const CUSTOMER_SECTOR_TYPE_INDUSTRIAL = 'industrial';

    const ACTIVE_OFFER = 1;
    const NOT_DIY      = 0;
    const DIY          = 1;

    const ANONYMOUS     = 1;
    const NOT_ANONYMOUS = 0;

    public $primaryKey = self::FIELD_REFERENCE;
}
