<?php

namespace App\Models\Legacy;

use App\Models\Legacy\DayOfWeek;
use Illuminate\Database\Eloquent\SoftDeletes;

class LeadAggregatorSchedule extends LegacyModel
{
    use SoftDeletes;

    const TABLE = 'lead_aggregator_schedules';

    const FIELD_ID = 'id';
    const FIELD_LEAD_AGGREGATOR_ID = 'lead_aggregator_id';
    const FIELD_DAY_OF_WEEK = 'day_of_week';
    const FIELD_START_TIME = 'start_time';
    const FIELD_END_TIME = 'end_time';
    const FIELD_ANYTIME = 'anytime';
    const FIELD_ACTIVE = 'active';
    const FIELD_CREATED_AT = 'created_at';
    const FIELD_UPDATED_AT = 'updated_at';
    const FIELD_DELETED_AT = 'deleted_at';

    const RELATION_LEAD_AGGREGATOR = 'leadAggregator';
    const RELATION_DAY_OF_WEEK = 'dayOfWeek';

    const ACTIVE_STATUS = 1;
    const INACTIVE_STATUS = 0;

    protected $table = self::TABLE;

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function leadAggregator() {
        return $this->belongsTo(LeadAggregator::class, self::FIELD_LEAD_AGGREGATOR_ID);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function dayOfWeek() {
        return $this->hasOne(DayOfWeek::class, DayOfWeek::FIELD_ID, self::FIELD_DAY_OF_WEEK);
    }
}
