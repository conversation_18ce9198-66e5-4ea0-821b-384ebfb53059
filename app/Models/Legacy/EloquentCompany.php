<?php

namespace App\Models\Legacy;

use App\Contracts\Legacy\Payments\PaymentMethodAggregatorContract;
use App\Models\AccountManagerClient;
use App\Models\Action;
use App\Models\Call;
use App\Models\Legacy\Ranking\CompanyRankingValue;
use App\Models\Odin\Company;
use App\Models\SalesBaitLead;
use App\Models\SalesBaitRestrictedCompany;
use App\Models\SuccessManagerClient;
use App\Services\Legacy\Payments\Credentials\StripeCredentialService;
use App\Services\Legacy\Payments\Gateways\PaymentGatewayAccountRepositoryFactory;
use App\Services\Legacy\Payments\Gateways\PaymentGatewayFactory;
use App\Services\Legacy\Payments\PaymentMethodAggregator;
use Carbon\Carbon;
use Database\Factories\Legacy\EloquentCompanyFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

/**
 * Class EloquentCompany
 *
 * @property int $companyid
 * @property string $companyname
 * @property string $description
 * @property string $status
 * @property string $website
 * @property int $buyingsolarestimateleads
 * @property string $type
 * @property int $accountmanagerid
 * @property string $reference
 * @property string $companylegalentityname
 * @property int $timestampadded
 * @property int $timestampstatusupdated
 * @property int $timestampnextinvoice
 * @property int $arbsubscriptionid
 * @property bool $familybusiness
 * @property bool $disallow_ranking
 * @property bool $enable_multi_campaign
 * @property bool $enable_lead_compliance_jornaya
 * @property bool $enable_watchdog_compliance_links
 * @property bool $is_phone_required_for_review
 * @property bool $autobilling
 * @property bool $never_exceed_budget
 * @property bool $allow_lead_sales_without_cc
 * @property string $yearstartedsolar
 * @property string $link
 * @property string $payment_source
 * @property string|null $rectanglelinktologo
 * @property string|null $watchdog_id
 * @property $linktologo
 * @property int $revenue_in_thousands
 * @property int $employee_count
 * @property-read float|null $estimated_revenue
 * @property-read Collection<EloquentCompanyContact> $contacts
 * @property-read Collection<EloquentUser> $users
 * @property-read Collection<NonPurchasingCompanyLocation> $nonPurchasingCompanyLocations
 * @property-read CompanyRanking|null $companyRanking
 * @property Collection|CompanyRankingValue[] $companyRankingValue
 * @property-read EloquentUser|null $accountManager
 * @property-read AccountManagerClient|null $accountManagerClient
 * @property-read EloquentCompanyAddress $defaultAddress
 * @property-read Collection<EloquentCompanyAddress> $companyAddresses
 * @property-read Collection<EloquentLicense> $companyLicenses
 * @property-read Collection<EloquentLink> $socialMediaLink
 * @property-read EloquentPricingPlan|null $pricingPlan
 * @property-read EloquentUser|null $customerSuccessManager
 * @property-read SalesBaitRestrictedCompany|null $restrictedSalesBait
 * @property-read Collection<LeadCampaign> $campaigns
 * @property-read Company|null $miCompany
 * @property-read Collection<EloquentCompanyPaymentProfile> $companyPaymentProfiles
 * @property-read Collection|EloquentCompanyNabcepCertification[] $companyNabcepCertifications
 * @property-read bool $is_archived
 * @property-read bool $is_admin_locked
 * @property-read bool $is_admin_approved
 * @property bool $imported
 * @mixin Builder
 */
class EloquentCompany extends LegacyModel
{
    use HasFactory;

    const TABLE = 'tblcompany';

    const ID                               = self::COMPANY_ID;
    const COMPANY_ID                       = 'companyid';
    const DESCRIPTION                      = 'description';
    const STATUS                           = 'status';
    const WEBSITE                          = 'website';
    const LINK                             = 'link';
    const FACEBOOK                         = 'facebook';
    const LINKEDIN                         = 'linkedin';
    const TWITTER                          = 'twitter';
    const YEAR_STARTED_BUSINESS            = 'yearstartedbusiness';
    const YEAR_STARTED_SOLAR               = 'yearstartedsolar';
    const FAMILY_BUSINESS                  = 'familybusiness';
    const QUALIFIED                        = 'qualified';
    const TIMESTAMP_UPDATED                = 'timestamplastupdate';
    const LICENSES                         = 'licenses';
    const IS_PHONE_REQUIRED_FOR_REVIEW     = 'is_phone_required_for_review';
    const BUYING_LEADS                     = 'buyingsolarestimateleads';
    const BUYING_SOLAR_ESTIMATE_LEADS      = 'buyingsolarestimateleads';
    const TYPE                             = 'type';
    const ACCOUNT_MANAGER_ID               = 'accountmanagerid';
    const SALES_CONSULTANT_ID              = 'salesconsultantid';
    const COMPANY_NAME                     = 'companyname';
    const COMPANY_LEGAL_ENTITY_NAME        = 'companylegalentityname';
    const TIMESTAMP_BUDGET_START           = 'timestampbudgetstart';
    const PAYMENT_SOURCE                   = 'payment_source';
    const LEAD_BUDGET_LIMIT                = 'leadbudgetlimit';
    const TIMESTAMP_ADDED                  = 'timestampadded';
    const REFERENCE                        = 'reference';
    const TIMESTAMP_LAST_PRESCREENED       = 'timestamplastprescreened';
    const ENABLE_MULTI_CAMPAIGN            = 'enable_multi_campaign';
    const LINK_TO_LOGO                     = 'linktologo';
    const ENABLE_WATCHDOG_COMPLIANCE_LINKS = 'enable_watchdog_compliance_links';
    const NEVER_EXCEED_BUDGET              = 'never_exceed_budget';
    const DISALLOW_RANKING                 = 'disallow_ranking'; // Temporarily blocks "bad" companies from being displayed favorably
    const PHONE_REQUIRED_FOR_REVIEW        = 'is_phone_required_for_review';
    const AUTO_BILLING                     = 'autobilling';
    const REVENUE_IN_THOUSANDS             = 'revenue_in_thousands';
    const EMPLOYEE_COUNT                   = 'employee_count';
    const PRICING_PLAN_ID                  = 'pricingplanid';
    const IMPORTED                         = 'imported';
    const CHARGE_ATTEMPTS                  = 'charge_attempts';
    const ENABLE_LEAD_COMPLIANCE_JORNAYA   = 'enable_lead_compliance_jornaya';
    const ALLOW_LEAD_SALES_WITHOUT_CC      = 'allow_lead_sales_without_cc';
    const OFFICE_IN_USA                    = 'officeinusa';
    const SALES_EMAIL                      = 'sales_email';
    const TECH_SUPPORT_EMAIL               = 'tech_support_email';
    const WATCHDOG_ID                      = 'watchdog_id';
    const BILLING_VERSION                  = 'billing_version';
    const NOTES                            = 'notes';


    const DEFAULT_COMPANY_LOGO = '/images/SolarReviews-icon_125x125_transparent.png';

    const STATUS_ACTIVE      = 'active';
    const STATUS_INACTIVE    = 'inactive';
    const STATUS_REGISTERING = 'registering';
    const STATUS_PRESALES    = 'presales';
    const STATUS_TRIAL       = 'trial';
    const STATUS_COLLECTION  = 'collection';
    const STATUS_ARCHIVE     = 'archive';
    const STATUS_HIDDEN      = 'hidden';
    const STATUS_SUSPENDED   = 'suspended';
    const STATUS_PENDING     = 'pending';
    const STATUS_ARCHIVED    = 'archived';

    const STATUSES = [
        self::STATUS_ACTIVE,
        self::STATUS_INACTIVE,
        self::STATUS_REGISTERING,
        self::STATUS_PRESALES,
        self::STATUS_TRIAL,
        self::STATUS_COLLECTION,
        self::STATUS_ARCHIVE,
        self::STATUS_HIDDEN,
        self::STATUS_SUSPENDED,
        self::STATUS_PENDING,
        self::STATUS_ARCHIVED
    ];

    const BUYING_LEADS_STATUS_ACTIVE       = 1;
    const BUYING_LEADS_STATUS_PAUSED       = -1;
    const BUYING_LEADS_STATUS_INACTIVE     = 0;
    const BUYING_LEADS_STATUS_ADMIN_LOCKED = -2;

    const LEAD_BUYING_STATUSES = [
        self::BUYING_LEADS_STATUS_ACTIVE,
        self::BUYING_LEADS_STATUS_PAUSED,
        self::BUYING_LEADS_STATUS_INACTIVE,
        self::BUYING_LEADS_STATUS_ADMIN_LOCKED
    ];

    const TYPE_ADMIN                = 'admin';
    const TYPE_INSTALLER            = 'installer';
    const TYPE_MANUFACTURER         = 'manufacturer';
    const TYPE_AFFILIATE            = 'affiliate';
    const TYPE_AGGREGATOR           = 'aggregator';
    const TYPE_PING_POST_AGGREGATOR = 'pp_agg';
    const TYPE_DISTRIBUTOR          = 'distributor';
    const TYPE_ROOFER               = 'roofer';
    const TYPE_NON_PROFIT           = "nonprofit";
    const TYPE_SERVICE              = "service";
    const TYPE_MULTI                = "multi";

    const COMPANY_TYPES = [
        self::TYPE_ADMIN,
        self::TYPE_INSTALLER,
        self::TYPE_MANUFACTURER,
        self::TYPE_AFFILIATE,
        self::TYPE_AGGREGATOR,
        self::TYPE_PING_POST_AGGREGATOR,
        self::TYPE_DISTRIBUTOR,
        self::TYPE_ROOFER,
        self::TYPE_NON_PROFIT,
        self::TYPE_SERVICE,
        self::TYPE_MULTI,
    ];

    const TYPE_DISPLAY_NAMES = [
        self::TYPE_ADMIN                => "Admin",
        self::TYPE_INSTALLER            => 'Installer',
        self::TYPE_MANUFACTURER         => 'Manufacturer',
        self::TYPE_AFFILIATE            => 'Affiliate',
        self::TYPE_AGGREGATOR           => 'Aggregator',
        self::TYPE_PING_POST_AGGREGATOR => 'Ping/Post Aggregator',
        self::TYPE_DISTRIBUTOR          => 'Distributor',
        self::TYPE_ROOFER               => 'Roofer',
        self::TYPE_NON_PROFIT           => 'Non-Profit',
        self::TYPE_SERVICE              => 'Service',
        self::TYPE_MULTI                => 'Multi-Industry',
    ];

    const PAYMENT_SOURCE_VIA_PAYMENT_GATEWAY    = 'pg';
    const PAYMENT_SOURCE_WIRE_TRANSFER          = 'wt';
    const PAYMENT_SOURCE_AUTO_RECURRING_BILLING = 'arb';

    const PAYMENT_SOURCES = [
        self::PAYMENT_SOURCE_VIA_PAYMENT_GATEWAY    => "Payment Gateway",
        self::PAYMENT_SOURCE_WIRE_TRANSFER          => "Wire Transfer",
        self::PAYMENT_SOURCE_AUTO_RECURRING_BILLING => "Auto Recurring Billing"
    ];

    const ALLOWED_EXTENSIONS = [
        '.jpg',
        '.jpeg',
        '.png',
        '.gif',
        '.webp',
        '.bmp'
    ];

    const CONTENT_PATH_KEY = 'toomuchisnotenoughsweetie';

    const RELATION_CAMPAIGNS                        = 'campaigns';
    const RELATION_COMPANY_ADDRESSES                = 'companyAddresses';
    const RELATION_ADDRESSES                        = 'addresses';
    const RELATION_USERS_HISTORY_LOG                = 'historyLog';
    const RELATION_USERS                            = 'users';
    const RELATION_CONFIGURATIONS                   = 'configurations';
    const RELATION_CONTACTS                         = 'contacts';
    const RELATION_NON_PURCHASING_COMPANY_LOCATIONS = 'nonPurchasingCompanyLocations';
    const RELATION_ACCOUNT_MANAGER_CLIENT           = 'accountManagerClient';
    const RELATION_DEFAULT_ADDRESS                  = 'defaultAddress';
    const RELATION_RESTRICTED_SALES_BAIT            = 'restrictedSalesBait';
    const RELATION_QUOTE_COMPANIES                  = 'quoteCompanies';
    const RELATION_INVOICES                         = 'invoices';
    const RELATION_CALLS                            = 'calls';
    const RELATION_MI_COMPANY                       = 'miCompany';
    const RELATION_LEAD_CAMPAIGNS                   = 'leadCampaigns';
    const RELATION_COMPANY_RANKING                  = 'companyRanking';
    const RELATION_COMPANY_RANKING_VALUE            = 'companyRankingValue';
    const RELATION_COMPANY_NABCEP_CERTIFICATIONS    = 'companyNabcepCertifications';
    const RELATION_REVIEWS                          = 'reviews';
    const RELATION_AFFILIATE_CAMPAIGNS              = 'affiliateCampaigns';


    const NON_PURCHASING_STATUSES = [
        EloquentCompany::STATUS_PRESALES,
        EloquentCompany::STATUS_REGISTERING,
        EloquentCompany::STATUS_PENDING
    ];

    protected $primaryKey = self::ID;
    protected $table      = self::TABLE;
    public    $timestamps = false;

    protected $fillable = [
        self::DESCRIPTION
    ];

    public $incrementing = false;

    /**
     * @return EloquentCompanyFactory
     */
    protected static function newFactory(): EloquentCompanyFactory
    {
        return EloquentCompanyFactory::new();
    }

    /**
     * @return ?string
     */
    public function getIndustry(): ?string
    {
        return match ($this->type) {
            self::TYPE_INSTALLER => 'solar',
            self::TYPE_ROOFER => 'roofer',
            default => null
        };
    }

    /**
     * Defines relationship to campaigns.
     *
     * @return HasMany
     */
    public function campaigns(): HasMany
    {
        return $this->hasMany(LeadCampaign::class, LeadCampaign::COMPANY_ID, self::ID);
    }

    public function affiliateCampaigns(): HasMany
    {
        return $this->hasMany(EloquentCampaign::class, EloquentCampaign::COMPANY_ID, self::ID);
    }

    /**
     * Defines relationship to salesBaitLeads
     * @return HasMany
     */
    public function salesBaitLeads(): HasMany
    {
        return $this->hasMany(
            SalesBaitLead::class,
            SalesBaitLead::FIELD_COMPANY_ID,
            self::ID
        );
    }

    /**
     * Defines relationship to CompanyContact
     * @param bool|null $withInactive
     * @return HasMany
     */

    public function contacts(?bool $withInactive = false): HasMany
    {
        $contacts = $this->hasMany(EloquentCompanyContact::class, EloquentCompanyContact::FIELD_COMPANY_ID, self::ID);
        return $withInactive
            ? $contacts
            : $contacts->where(EloquentCompanyContact::FIELD_STATUS, EloquentCompanyContact::STATUS_ACTIVE);
    }

    /**
     * Defines relationship to Users
     * @param bool|null $withInactive
     * @return HasMany
     */
    public function users(?bool $withInactive = false): HasMany
    {
        $users = $this->hasMany(EloquentUser::class, EloquentUser::COMPANY_ID, self::ID);
        return $withInactive
            ? $users
            : $users->where(EloquentUser::STATUS, EloquentUser::FIELD_STATUS_VALUE_ACTIVE);
    }

    /**
     * @return Builder
     */
    public function actions(): Builder
    {
        $companyContactIds = EloquentCompanyContact::query()
            ->where(EloquentCompanyContact::FIELD_COMPANY_ID, $this->companyid)
            ->pluck(EloquentCompanyContact::FIELD_CONTACT_ID)
            ->toArray();

        $companyUserIds = EloquentUser::query()
            ->where(EloquentUser::COMPANY_ID, $this->companyid)
            ->pluck(EloquentUser::USER_ID)
            ->toArray();

        return Action::query()
            ->where(function ($query) {
                $query
                    ->where(Action::FIELD_FOR_RELATION_TYPE, Action::RELATION_TYPE_COMPANY)
                    ->where(Action::FIELD_FOR_ID, $this->companyid);
            })
            ->orWhere(function ($query) use ($companyContactIds) {
                $query
                    ->where(Action::FIELD_FOR_RELATION_TYPE, Action::RELATION_TYPE_COMPANY_CONTACT)
                    ->whereIn(Action::FIELD_FOR_ID, $companyContactIds);
            })
            ->orWhere(function ($query) use ($companyUserIds) {
                $query
                    ->where(Action::FIELD_FOR_RELATION_TYPE, Action::RELATION_TYPE_COMPANY_USER)
                    ->whereIn(Action::FIELD_FOR_ID, $companyUserIds);
            })
            ->orderBy(Action::FIELD_PINNED, 'desc')
            ->latest();
    }

    public function invoices(): hasMany
    {
        return $this->hasMany(EloquentInvoice::class, EloquentInvoice::COMPANY_ID, self::ID);
    }

    public function reviews(): hasMany
    {
        return $this->hasMany(EloquentReview::class, EloquentReview::FIELD_REVIEW_LINK, self::ID);
    }

    public function isPrescreeed(): bool
    {
        return $this->{self::TIMESTAMP_LAST_PRESCREENED} + (365 * 24 * 60 * 60) > Carbon::now()->timestamp;
    }


    /**
     * @return HasMany
     */
    public function companyAddresses()
    {
        return $this->hasMany(EloquentCompanyAddress::class, EloquentCompanyAddress::FIELD_COMPANY_ID, self::ID);
    }

    /**
     * The addresses that belongs to the company
     *
     * @return BelongsToMany
     */
    public function addresses()
    {
        return $this->belongsToMany(EloquentAddress::class, EloquentCompanyAddress::TABLE, self::ID,
            EloquentCompanyAddress::FIELD_ADDRESS_ID);
    }

    /**
     * @return HasOne
     */
    public function pricingPlan(): HasOne
    {
        return $this->hasOne(EloquentPricingPlan::class, EloquentPricingPlan::PRICING_PLAN_ID, self::PRICING_PLAN_ID);
    }

    /**
     * @return HasOne
     */
    public function defaultAddress(): hasOne
    {
        return $this->hasOne(EloquentCompanyAddress::class, EloquentCompanyAddress::FIELD_COMPANY_ID, self::ID)
            ->where(EloquentCompanyAddress::FIELD_IS_DEFAULT, 1);
    }

    /**
     * @return HasManyThrough
     */
    public function historyLog()
    {
        return $this->hasManyThrough(
            EloquentHistoryLog::class,
            EloquentUser::class,
            EloquentUser::COMPANY_ID,
            EloquentHistoryLog::USER_ID,
            self::COMPANY_ID
        );
    }

    /**
     * @return HasMany
     */
    public function companyMedia(): HasMany
    {
        return $this->hasMany(EloquentMedia::class, EloquentMedia::REL_ID, self::ID)
            ->where(EloquentMedia::REL_TYPE, EloquentMedia::REL_TYPE_COMPANY);
    }

    /**
     * @return HasMany
     */
    public function attachments(): HasMany
    {
        return $this->hasMany(CompanyAttachment::class, CompanyAttachment::ATTRIBUTE_COMPANY_ID, self::COMPANY_ID);
    }

    /**
     * @return MorphMany
     */
    function configurations()
    {
        return $this->morphMany(
            EloquentConfiguration::class,
            null,
            EloquentConfiguration::REL_TYPE,
            EloquentConfiguration::REL_ID
        );
    }

    /**
     * @return HasMany
     */
    public function nonPurchasingCompanyLocations()
    {
        return $this->hasMany(NonPurchasingCompanyLocation::class, NonPurchasingCompanyLocation::FIELD_COMPANY_ID, self::ID);
    }

    /**
     * @return HasMany
     */
    public function quoteCompanies(): HasMany
    {
        return $this->hasMany(EloquentQuoteCompany::class, EloquentQuoteCompany::COMPANY_ID, self::ID);
    }

    /**
     * @return HasMany
     */
    public function companyLicenses(): HasMany
    {
        return $this->hasMany(EloquentLicense::class, EloquentLicense::COMPANY_ID, self::ID);
    }

    /**
     * @return HasMany
     */
    public function socialMediaLink(): HasMany
    {
        return $this->hasMany(EloquentLink::class, EloquentLink::REL_ID, self::ID)
            ->where(EloquentLink::REL_TYPE, EloquentLink::REL_TYPE_COMPANY_ID);
    }

    /**
     * Defines relationship to account manager.
     *
     * @return HasOne
     */
    public function accountManager(): HasOne
    {
        return $this->hasOne(EloquentUser::class, EloquentUser::ID, self::ACCOUNT_MANAGER_ID);
    }

    /**
     * @return Model|null
     */
    public function accountManagerClient()
    {
        return AccountManagerClient::query()
            ->where(AccountManagerClient::FIELD_COMPANY_REFERENCE, $this->reference)
            ->where(AccountManagerClient::FIELD_STATUS, AccountManagerClient::STATUS_ACTIVE)
            ->first();
    }

    /**
     * @return SuccessManagerClient|null
     */
    public function successManagerClient(): ?SuccessManagerClient
    {
        /** @var SuccessManagerClient|null $SuccessManagerClient */
        $SuccessManagerClient = SuccessManagerClient::query()
            ->where(SuccessManagerClient::FIELD_COMPANY_REFERENCE, $this->reference)
            ->where(SuccessManagerClient::FIELD_STATUS, SuccessManagerClient::STATUS_ACTIVE)
            ->first();

        return $SuccessManagerClient;
    }

    /**
     * @return string|null
     */
    private function getLinkToCompanyLogo(): ?string
    {
        return Str::endsWith($this->linktologo, self::ALLOWED_EXTENSIONS) ? $this->linktologo : null;
    }

    /**
     * @return string
     */
    public function logoUrl(): string
    {
        if (!trim($this->getLinkToCompanyLogo())) {
            return self::DEFAULT_COMPANY_LOGO;
        }

        return config('app.solarreviews_domain.www') . '/content' . $this->relativeContentPath() . '/logo/' . $this->getLinkToCompanyLogo();
    }

    /**
     * Get url for company page of installer or manufacturer
     *
     * @return string
     */
    public function companyUrl(): string
    {
        if ($this->type === self::TYPE_INSTALLER) {
            return config('app.solarreviews_domain.www') . "/installers/{$this->link}-reviews";
        }

        if ($this->type === self::TYPE_MANUFACTURER) {
            return config('app.solarreviews_domain.www') . "/manufacturers/{$this->link}";
        }

        return "";
    }

    /**
     * @return string
     */
    private function relativeContentPath(): string
    {
        $secureKey = $this->companyid . md5($this->companyid . self::CONTENT_PATH_KEY);
        return '/company/' . $secureKey;
    }

    /**
     * @return HasOne|EloquentUser
     */
    public function customerSuccessManager()
    {
        return $this->hasOne(EloquentUser::class, EloquentUser::ID, self::SALES_CONSULTANT_ID);
    }

    /**
     * Defines the relationship between a company and a restricted sales bait.
     *
     * @return HasOne
     */
    public function restrictedSalesBait(): HasOne
    {
        return $this->hasOne(SalesBaitRestrictedCompany::class, SalesBaitRestrictedCompany::FIELD_ID, self::COMPANY_ID);
    }

    /**
     * Defines relationship to Calls
     * @return HasMany
     */
    public function calls(): HasMany
    {
        return $this->hasMany(Call::class, Call::FIELD_RELATION_ID, self::ID)
            ->where(Call::FIELD_RELATION_TYPE, Call::RELATION_COMPANY);
    }

    /**
     * @return HasOne
     */
    public function miCompany(): HasOne
    {
        return $this->hasOne(Company::class, Company::FIELD_LEGACY_ID, self::ID);
    }

    /**
     * @return HasMany
     */
    public function leadCampaigns(): HasMany
    {
        return $this->hasMany(LeadCampaign::class, LeadCampaign::COMPANY_ID, self::ID);
    }

    public function isAdminLocked(): Attribute
    {
        return Attribute::make(get: fn(
        ) => $this->{self::BUYING_LEADS} === EloquentCompany::BUYING_LEADS_STATUS_ADMIN_LOCKED);
    }

    public function isAdminApproved(): Attribute
    {
        return Attribute::make(get: fn(
        ) => !in_array($this->{self::STATUS}, [EloquentCompany::STATUS_PENDING, EloquentCompany::STATUS_REGISTERING]));
    }

    public function isArchived(): Attribute
    {
        return Attribute::make(get: fn(
        ) => in_array($this->{self::STATUS}, [EloquentCompany::STATUS_ARCHIVED, EloquentCompany::STATUS_ARCHIVE, EloquentCompany::STATUS_HIDDEN, EloquentCompany::STATUS_INACTIVE]));
    }

    /**
     * Lead campaign types.
     *
     * @return HasMany
     */
    public function leadCampaignsTypes(): HasMany
    {
        return $this->hasMany(LeadCampaign::class, LeadCampaign::COMPANY_ID, self::ID)
            ->join(LeadCampaignSalesTypeConfiguration::TABLE, LeadCampaign::TABLE.'.'.LeadCampaign::ID, '=',
                LeadCampaignSalesTypeConfiguration::TABLE.'.'.LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID)
            ->join(LeadSalesType::TABLE, LeadSalesType::TABLE.'.'.LeadSalesType::ID, '=',
                LeadCampaignSalesTypeConfiguration::TABLE.'.'.LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID)
            ->select(LeadCampaign::TABLE.'.*', LeadSalesType::TABLE.'.'.LeadSalesType::NAME.' as LeadType',
                LeadSalesType::TABLE.'.'.LeadSalesType::ID.' as LeadTypeId',
                LeadCampaignSalesTypeConfiguration::TABLE.'.'.LeadCampaignSalesTypeConfiguration::STATUS.' as LeadTypeStatus');
    }

    /**
     * @return HasMany
     */
    public function companyPaymentProfiles(): HasMany
    {
        return $this->hasMany(EloquentCompanyPaymentProfile::class, EloquentCompanyPaymentProfile::COMPANY_ID,
            self::ID);
    }

    public function paymentGateways(): PaymentGatewayFactory
    {
        /** @var StripeCredentialService $credentialService */
        $credentialService = app()->make(StripeCredentialService::class);

        return new PaymentGatewayFactory(
            new PaymentGatewayAccountRepositoryFactory($this),
            $credentialService
        );
    }

    public function paymentMethods(): PaymentMethodAggregatorContract
    {
        return new PaymentMethodAggregator($this->paymentGateways());
    }

    /**
     * @return bool
     */
    public function isTypeRoofer()
    {
        return $this->type === self::TYPE_ROOFER;
    }

    public function isBuyingLeads(): bool
    {
        return $this->buyingsolarestimateleads == self::BUYING_LEADS_STATUS_ACTIVE;
    }

    /**
     * Get CompanyRanking for company
     *
     * @return HasOne
     */
    public function companyRanking(): HasOne
    {
        return $this->hasOne(CompanyRanking::class, CompanyRanking::COMPANY_ID, self::ID);
    }

    /**
     * Defines the relationship between the company and the ranking factor assignments.
     *
     * @return HasMany
     */
    public function companyRankingValue(): HasMany
    {
        return $this->hasMany(CompanyRankingValue::class, CompanyRankingValue::FIELD_COMPANY_ID, self::COMPANY_ID);
    }

    /**
     * @return HasMany
     */
    public function companyNabcepCertifications(): HasMany
    {
        return $this->hasMany(EloquentCompanyNabcepCertification::class,
            EloquentCompanyNabcepCertification::COMPANY_ID, self::ID);
    }
}
