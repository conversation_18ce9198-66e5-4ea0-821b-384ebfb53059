<?php

namespace App\Models\Legacy;

use App\Models\LeadProcessingReservedLead;
use App\Models\Legacy\EloquentQuoteEvent;
use App\Models\LeadProcessingCommunication;
use App\Models\LeadProcessingPendingReview;
use App\Models\LeadProcessingUnderReview;
use App\Models\Odin\Consumer;
use App\Services\LeadVerification\LeadVerificationResult;
use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Collection;
use Database\Factories\EloquentQuoteFactory;

/**
 * @property int                                   $quoteid
 * @property int                                   $addressid
 * @property string                                $firstname
 * @property string                                $lastname
 * @property string                                $useremail
 * @property int                                   $utilityid
 * @property string                                $electriccost
 * @property string                                $ownproperty
 * @property int                                   $timestampadded
 * @property int                                   $numberofquotes
 * @property string                                $besttimetocall
 * @property string                                $besttimetocallother
 * @property string|null                           $classification
 * @property boolean                               $solar_lead
 * @property boolean                               $roofing_lead
 * @property string                                $origin
 * @property string                                $originkey
 * @property string                                $statusreason
 * @property string                                $status
 * @property string                                $ipaddress
 * @property int                                   $userid
 * @property string                                $utility
 * @property int                                   $lockedbyuserid
 * @property int|null                              $lead_type_id
 * @property string                                $comments
 * @property int|null                              $lead_category_id
 * @property string                                $uuid
 * @property string                                $track_name
 * @property string                                $track_code
 * @property string                                $contact_method
 * @property boolean                               $multi_industry_lead
 *
 * @property Collection|EloquentQuoteCompany[]     $quoteCompanies
 * @property Collection                            $quoteEvents
 * @property EloquentAddress                       $address
 * @property Collection|EloquentVarStore[]         $varStores
 * @property LeadVerificationResult|null           $lead_verification_details
 * @property-read LeadCategory                     leadCategory
 * @property-read LeadProcessingPendingReview|null $pendingReview
 * @property-read LeadProcessingUnderReview|null   $underReview
 * @property-read LeadProcessingCommunication[]    $leadCommunications
 * @property-read LeadProcessingReservedLead       $reserved
 * @property-read LeadRoofDetail                   $roofDetails
 * @property-read Consumer|null                    $consumer
 * @property-read Collection<EloquentComment>      $leadComments
 * @property-read Collection<CrmDeliveryLog>       $crmDeliveryLogs
 */
class EloquentQuote extends LegacyModel
{
    use HasFactory;

    const TABLE = 'tblquote';

    const ID                      = self::QUOTE_ID;
    const QUOTE_ID                = 'quoteid';
    const FIRST_NAME              = 'firstname';
    const LAST_NAME               = 'lastname';
    const USER_EMAIL              = 'useremail';
    const OWN_PROPERTY            = 'ownproperty';
    const NUMBER_OF_QUOTES        = 'numberofquotes';
    const BEST_TIME_TO_CALL       = 'besttimetocall';
    const BEST_TIME_TO_CALL_OTHER = 'besttimetocallother';
    const ELECTRIC_COST           = 'electriccost';
    const LEAD_CATEGORY_ID        = 'lead_category_id';
    const ADDRESS_ID              = 'addressid';
    const SOLAR_LEAD              = 'solar_lead';
    const ROOFING_LEAD            = 'roofing_lead';
    const UTILITY_ID              = 'utilityid';
    const UTILITY                 = 'utility';
    const TIMESTAMP_ADDED         = 'timestampadded';
    const LOCKED_BY_USER_ID       = 'lockedbyuserid';
    const UTC                     = 'utc';
    const STATUS                  = 'status';
    const CLASSIFICATION          = 'classification';
    const LEAD_TYPE_ID            = 'lead_type_id';
    const IP_ADDRESS              = 'ipaddress';
    const ROOF_TYPE               = 'rooftype';
    const ROOF_TYPE_OTHER         = 'rooftypeother';
    const ROOF_CONDITION = 'roof_condition';
    const ROOF_DIRECTION = 'roofdirection';
    const ROOF_SHADING = 'roofshading';
    const ROOF_PITCH = 'roofpitch';
    const PANEL_TIER = 'panel_tier';
    const SYSTEM_SIZE             = 'systemsize';
    const SYSTEM_SIZE_OTHER = 'systemsizeother';
    const STOREYS = 'storeys';
    const COMMENTS                = 'comments';
    const TRACK_NAME              = 'trackname';
    const TRACK_CODE              = 'trackcode';
    const TCPA_LEAD_ID = 'leadid';
    const REFERENCE               = 'uuid';
    const SYSTEM_TYPE = 'system_type';
    const ORIGIN = 'origin';
    const ORIGIN_KEY = 'originkey';
    const STATUS_REASON = 'statusreason';
    const EMAIL_IS_VALIDATED_FLAG = 'emailvalidated';
    const PHONE_IS_VALIDATED_FLAG = 'phonevalidated';
    const PHONE_VALIDATION_METHOD = 'phonevalidationmethod';
    const PROPERTY_TYPE = 'propertytype';
    const PROJECT_TYPE = 'projecttype';
    const UTILITY_NAME = 'utility';
    const PAYMENT_TYPE = 'paymenttype';
    const TRACK_LEAD_ID = 'trackleadid';
    const CONTACT_METHOD = 'contact_method';
    const ROOFING_LEAD_TYPE_ID = 'roofing_lead_type_id';
    const TCPA_DISCLOSURE_CAPTURED ='tcpa_disclosure_captured';
    const AUTO_ASSIGNED_STATUS = 'auto_assigned_status';
    const AFFILIATE_ID = 'affiliateid';
    const CAMPAIGN_ID = 'campaignid';
    const MULTI_INDUSTRY_LEAD = 'multi_industry_lead';

    const CLASSIFICATION_VERIFIED   = 'verified';
    const CLASSIFICATION_UNVERIFIED = 'unverified';
    const CLASSIFICATION_EMAIL_ONLY = 'email_only';

    const LEAD_INDUSTRY_ROOFING = 'roofing';
    const LEAD_INDUSTRY_SOLAR   = 'solar';

    const VALUE_STATUS_INITIAL = 'initial';
    const VALUE_STATUS_ALLOCATED = 'allocated';
    const VALUE_STATUS_SALES_BAIT = 'salesbait';
    const VALUE_STATUS_BROKERAGE = 'brokerage';
    const VALUE_STATUS_SOLD = 'sold';
    const VALUE_STATUS_UNDER_REVIEW = 'underreview';
    const VALUE_STATUS_NO_COMPANIES = 'nocompanies';
    const VALUE_STATUS_CANCELLED = 'cancelled';
    const VALUE_STATUS_EMAIL_ONLY = 'emailonly';

    const RELATION_ADDRESS           = 'address';
    const RELATION_LEAD_CATEGORY     = 'leadCategory';
    const RELATION_QUOTE_EVENTS      = 'quoteEvents';
    const RELATION_TRACKING_URL      = 'trackingUrl';
    const RELATION_RESERVED          = 'reserved';
    const RELATION_ROOF_DETAILS      = 'roofDetails';
    const RELATION_CRM_DELIVERY_LOGS = 'crmDeliveryLogs';

    const RELATION_CONSUMER      = 'consumer';
    const MAX_HOURS = '70 HOUR';
    const LEAD_LIMIT_PER_PAGE = 10;
    const QUOTE_ORIGIN_ADMIN
        = [
            'sr' => 'www.solarreviews.com',
            'se' => 'www.solar-estimate.org',
            'rc' => 'roofingcalculator.com',
            'spr' => 'www.solarpowerrocks.com',
            'sb' => 'Solar Brokers',
            'sn' => 'www.sunnumber.com',
            'cmb' => 'cutmybill.com',
            'ws' => 'Wave Solar',
            'fixr' => 'www.fixr.com'
        ];

    protected $table = self::TABLE;

    protected $primaryKey = self::ID;

    public $timestamps = false;

    protected $casts
        = [
            self::SOLAR_LEAD => 'boolean',
            self::ROOFING_LEAD => 'boolean',
            self::MULTI_INDUSTRY_LEAD => 'boolean'
        ];

    protected $guarded = [self::ID];

    /**
     * @return EloquentQuoteFactory
     */
    protected static function newFactory(): EloquentQuoteFactory
    {
        return EloquentQuoteFactory::new();
    }

    /**
     * @return null|string
     */
    public function getUtilityName(): null|string
    {
        $utility = null;

        if ($this->utility) {
            $utility = $this->utility;
        } else {
            $eloquentUtility = $this->getEloquentUtility();
            if ($eloquentUtility) {
                $utility = $eloquentUtility->name;
            }
        }

        return $utility;
    }

    /**
     * @return int
     */
    public function getUtilityId(): int
    {
        $utilityId = $this->utilityid;
        if (intval($utilityId) == 0) {
            /** @var EloquentUtility|null $utility */
            $utility = EloquentUtility::query()->where(EloquentUtility::FIELD_NAME, trim($this->utility))->first();
            if ($utility) {
                $utilityId = $utility->utilityid;
            }
        }
        return $utilityId;
    }

    /**
     * @return EloquentUtility|Model|null
     */
    public function getEloquentUtility(): EloquentUtility|Model|null
    {
        $utilityId = $this->utilityid;

        if (intval($utilityId) == 0) {
            $utility = EloquentUtility::query()->where(EloquentUtility::FIELD_NAME, trim($this->utility))->first();
        } else {
            $utility = EloquentUtility::query()->where(EloquentUtility::FIELD_UTILITY_ID, $utilityId)->first();
        }

        return $utility;
    }

    /**
     * @return BelongsTo
     */
    public function address(): BelongsTo
    {
        return $this->belongsTo(EloquentAddress::class, EloquentAddress::ID, self::ADDRESS_ID);
    }

    /**
     * @return HasMany
     */
    public function quoteEvents(): HasMany
    {
        return $this->hasMany(EloquentQuoteEvent::class, EloquentQuoteEvent::QUOTE_ID, self::ID);
    }

    /**
     * @return BelongsTo
     */
    public function leadCategory(): BelongsTo
    {
        return $this->belongsTo(LeadCategory::class, self::LEAD_CATEGORY_ID, LeadCategory::ID);
    }

    /**
     * @return HasMany
     */
    public function quoteCompanies(): HasMany
    {
        return $this->hasMany(EloquentQuoteCompany::class, EloquentQuoteCompany::QUOTE_ID, self::ID);
    }

    /**
     * Saves the lead verification details against a quote
     *
     * @param mixed $verificationDetails
     */
    function saveLeadVerificationDetails(mixed $verificationDetails)
    {
        if (!$this->exists) {
            throw new \LogicException("Cannot create quote event on an uninstantiated quote.");
        }

        $this->quoteEvents()->updateOrCreate(
            [EloquentQuoteEvent::QUOTE_ID => $this->quoteid],
            [
                EloquentQuoteEvent::TYPE => EloquentQuoteEvent::TYPE_LEAD_VERIFIED,
                EloquentQuoteEvent::VALUE => json_encode($verificationDetails),
            ]
        );
    }

    /**
     * @return bool
     * @throws Exception
     */
    function isDelivered(): bool
    {
        if (!$this->exists) {
            throw new Exception('Can not create quote company on a uninstantiated quote.');
        }

        $quoteCompany = $this->quoteCompanies->where(EloquentQuoteCompany::DELIVERED, true)->first();
        return (bool)$quoteCompany;
    }

    /**
     * Defines the relationship with pending review.
     *
     * @return HasOne
     */
    public function pendingReview(): HasOne
    {
        return $this->hasOne(LeadProcessingPendingReview::class, LeadProcessingPendingReview::FIELD_LEAD_ID, self::ID);
    }

    /**
     * Defines the relationship with under review.
     *
     * @return HasOne
     */
    public function underReview(): HasOne
    {
        return $this->hasOne(LeadProcessingUnderReview::class, LeadProcessingUnderReview::FIELD_LEAD_ID, self::ID);
    }

    /**
     * Defines relationship with lead processing communications.
     *
     * @return HasMany
     */
    public function leadCommunications(): HasMany
    {
        return $this->hasMany(LeadProcessingCommunication::class, LeadProcessingCommunication::FIELD_LEAD_ID, self::ID);
    }

    /**
     * @param int $userId
     * @return bool
     */
    public function isLockedBy($userId = 0): bool
    {
        return $this->lockedbyuserid == $userId;
    }

    /**
     * @return MorphMany
     */
    public function varStores(): MorphMany
    {
        return $this->morphMany(EloquentVarStore::class, null, EloquentVarStore::REL_TYPE, EloquentVarStore::REL_ID);
    }

    /**
     * Returns the morph class key for this lead.
     *
     * @return string
     */
    public function getMorphClass(): string
    {
        return "quote";
    }

    /**
     * @return LeadVerificationResult|null
     */
    function getLeadVerificationDetailsAttribute()
    {
        /** @var EloquentQuoteEvent|null $event */
        $event = $this->quoteEvents->where(EloquentQuoteEvent::TYPE, EloquentQuoteEvent::TYPE_LEAD_VERIFIED)->first();

        if ($event === null) {
            return null;
        }

        $result = new LeadVerificationResult(json_decode($event->value));
        return $result;
    }

    /**
     * Get the lead industry model
     *
     * @return Industry|null
     */
    public function getLeadIndustry(): ?Industry
    {
        $industry = null;

        if (!empty($this->{self::SOLAR_LEAD})) {
            $industry = Industry::where(Industry::FIELD_KEY, '=', self::LEAD_INDUSTRY_SOLAR)->first();
        } else if (!empty($this->{self::ROOFING_LEAD})) {
            $industry = Industry::where(Industry::FIELD_KEY, '=', self::LEAD_INDUSTRY_ROOFING)->first();
        }

        return $industry;
    }

    public function trackingUrl(): HasOne
    {
        return $this->hasOne(LeadTrackingUrl::class, LeadTrackingUrl::LEAD_ID, self::ID);
    }

    /**
     * Defines relationship to the reserved model.
     *
     * @return HasOne
     */
    public function reserved(): HasOne
    {
        return $this->hasOne(LeadProcessingReservedLead::class, LeadProcessingReservedLead::FIELD_LEAD_ID, self::ID);
    }

    /**
     * Defines the has one relationship with the roof details.
     *
     * @return HasOne
     */
    public function roofDetails(): HasOne
    {
        return $this->hasOne(LeadRoofDetail::class, LeadRoofDetail::FIELD_QUOTE_ID, self::ID);
    }

    /**
     * @return HasOne
     */
    public function consumer(): HasOne {
        return $this->hasOne(Consumer::class, Consumer::FIELD_LEGACY_ID, self::ID);
    }

    /**
     * @return HasMany
     */
    public function leadComments(): HasMany
    {
        return $this->hasMany(EloquentComment::class, EloquentComment::REL_ID, self::ID)
            ->where(EloquentComment::REL_TYPE, EloquentComment::REL_TYPE_QUOTE);
    }

    /**
     * Returns static map image of this lead.
     *
     * @return string
     */
    public function getAddressImage(): string
    {
        $params = [
            "center" => $this->address->latitude.','.$this->address->longitude ,
            "zoom" => 19,
            "size" => "650x400",
            "maptype" => "satellite",
            "key" => config('services.google.maps.static_maps_key')
        ];

        return "https://maps.googleapis.com/maps/api/staticmap?" . http_build_query($params);
    }

    /**
     * @return HasMany
     */
    public function appointments(): HasMany
    {
        return $this->hasMany(LeadAppointment::class, LeadAppointment::LEAD_ID, self::ID);
    }

    /**
     * @return HasMany
     */
    public function crmDeliveryLogs(): HasMany
    {
        return $this->hasMany(CrmDeliveryLog::class, CrmDeliveryLog::QUOTE_ID, self::QUOTE_ID);
    }
}
