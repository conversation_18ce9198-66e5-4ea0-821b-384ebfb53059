<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class CompanyRanking
 * @package App
 *
 * @property int $id
 * @property int $company_id
 * @property float $bayesian_all_time
 * @property int $review_count
 *
 * @property-read EloquentCompany $company
 */
class CompanyRanking extends LegacyModel
{
    const ID                = 'id';
    const COMPANY_ID        = 'company_id';
    const BAYESIAN_ALL_TIME = 'bayesian_all_time';
    const REVIEW_COUNT      = 'review_count';

    const RELATION_COMPANY = 'company';

    const TABLE = 'company_rankings';

    /** @var string $table */
    protected $table = self::TABLE;

    protected $primaryKey = self::ID;

    public $timestamps = false;

    protected $fillable = [
        self::COMPANY_ID,
        self::BAYESIAN_ALL_TIME,
        self::REVIEW_COUNT
    ];

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(EloquentCompany::class, self::COMPANY_ID, EloquentCompany::ID);
    }

}
