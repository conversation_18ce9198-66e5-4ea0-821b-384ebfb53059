<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Model;

/**
 * Class EloquentReviewQuestion
 * @package App
 *
 * @property int $questionid
 * @property string $question_name
 * @property string $reviewtype
 * @property int $sortorder
 * @property string $type
 * @property string $options
 * @property string $name
 * @property string $description
 * @property string $values
 * @property int $status
 */
class EloquentReviewQuestion extends LegacyModel
{
    const TABLE  = 'tblreviewquestion';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'tblreviewquestion';

    //table field constants
    const FIELD_QUESTION_ID = 'questionid';
    const FIELD_QUESTION_NAME = 'question_name';
    const FIELD_REVIEW_TYPE = 'reviewtype';
    const FIELD_SORT_ORDER = 'sortorder';
    const FIELD_TYPE = 'type';
    const FIELD_OPTIONS = 'options';
    const FIELD_NAME = 'name';
    const FIELD_DESCRIPTION = 'description';
    const FIELD_VALUES = 'values';
    const FILED_STATUS = 'status';

    //question_name field values
    const QUESTION_NAME_VALUE_INSTALLED_YEAR = 'installed_year';
    const QUESTION_NAME_VALUE_ZIP_CODE = 'zip_code';
    const QUESTION_NAME_VALUE_SYSTEM_SIZE = 'system_size';
    const QUESTION_NAME_VALUE_RATING_SALES_PROCESS = 'rating_sales_process';
    const QUESTION_NAME_VALUE_RATING_PRICE_CHARGED = 'rating_price_changed';
    const QUESTION_NAME_VALUE_RATING_ON_SCHEDULE = 'rating_on_schedule';
    const QUESTION_NAME_VALUE_RATING_QUALITY_INSTALLATION = 'rating_quality_of_installation';
    const QUESTION_NAME_VALUE_RATING_AFTER_SALES_SUPPORT = 'rating_after_sales_support';

    const VALUE_STATUS_ACTIVE = 1;
}
