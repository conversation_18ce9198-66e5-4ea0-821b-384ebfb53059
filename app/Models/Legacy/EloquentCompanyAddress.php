<?php

namespace App\Models\Legacy;

use App\Jobs\NonPurchasingCompanyLocations\UpdateLocationsBasedOnRadiusJob;
use Illuminate\Database\Eloquent\Model;

/**
 * Class EloquentCompanyAddress
 * @package App\Models\Legacy
 * @property int $companyid
 * @property int $addressid
 * @property int $isdefault
 * @property string $name
 * @property string $skypenumber
 * @property string $google_place_id
 * @property int $google_review_count
 * @property string $google_rating
 *
 * @property-read EloquentAddress address
 * @property-read EloquentCompany $company
 */
class EloquentCompanyAddress extends LegacyModel
{
    const FIELD_COMPANY_ID = 'companyid';
    const FIELD_ADDRESS_ID = 'addressid';
    const FIELD_IS_DEFAULT = 'isdefault';
    const FIELD_NAME = 'name';
    const FIELD_SKYPE_NUMBER = 'skypenumber';
    const FIELD_GOOGLE_PLACE_ID = 'google_place_id';
    const FIELD_GOOGLE_REVIEW_COUNT = 'google_review_count';
    const FIELD_GOOGLE_RATING = 'google_rating';

    const RELATION_ADDRESS = 'address';
    const RELATION_COMPANY = 'company';

    const IS_DEFAULT = 1;
    const IS_NOT_DEFAULT = 0;

    const TABLE = 'tblcompanyaddress';

    protected $table = self::TABLE;

    public $timestamps = false;

    protected $fillable = [
        self::FIELD_COMPANY_ID,
        self::FIELD_ADDRESS_ID,
        self::FIELD_IS_DEFAULT,
        self::FIELD_NAME,
        self::FIELD_SKYPE_NUMBER,
        self::FIELD_GOOGLE_PLACE_ID,
        self::FIELD_GOOGLE_REVIEW_COUNT,
        self::FIELD_GOOGLE_RATING
    ];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    function address() {
        return $this->hasOne(EloquentAddress::class, EloquentAddress::ID, self::FIELD_ADDRESS_ID);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    function company()
    {
        return $this->belongsTo(EloquentCompany::class,self::FIELD_COMPANY_ID, EloquentCompany::ID);
    }
}
