<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * This model reflects the ZipCodeToUtility model in Solar-Estimate
 *
 *
 * Class SEZipCodeToUtility
 * @package App
 *
 * @property int $id
 * @property string $zip_code
 * @property int $utility_id
 * @property SEUtility|null $utility
 */
class SEZipCodeToUtility extends Model
{
    const TABLE = 'se_zip_code_to_utilities';

    const FIELD_ID = 'id';
    const FIELD_ZIP_CODE = 'zip_code';
    const FIELD_UTILITY_ID = 'utility_id';

    const RELATION_UTILITY = 'utility';

    protected $fillable = [
        self::FIELD_ZIP_CODE,
        self::FIELD_UTILITY_ID
    ];

    protected $table = self::TABLE;

    public $timestamps = false;


    /**
     * @return BelongsTo
     */
    public function utility(): BelongsTo
    {
        return $this->belongsTo(SEUtility::class);
    }
}
