<?php

namespace App\Models\Legacy;

use App\Services\LeadCampaignCRMIntegrations\Helpers\LeadCampaignCRMIntegrationsHelper;
use App\Collections\Collection;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

/**
 * Class CrmIntegration
 *
 * @package App
 *
 * @property int $id
 * @property int $company_id
 * @property string $name
 * @property string $crm_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * Relations
 * @property-read EloquentCompany $company
 * @property-read EloquentIntegrationField[]|Collection $integrationFields
 * @property-read Crm $crm
 * @property-read Collection|LeadCampaignCrmIntegration[] $leadCampaignCrmIntegrations
 */
class CrmIntegration extends LegacyModel
{
    const TABLE = 'tbl_crm_integrations';
    protected $table = self::TABLE;

    const ID = 'id';
    const COMPANY_ID = 'company_id';
    const NAME = 'name';
    const CRM_ID = 'crm_id';
    const PRODUCT = 'product';

    const RELATION_CRM = 'crm';
    const RELATION_INTEGRATION_FIELDS = 'integrationFields';

    const PRODUCT_LEAD = 'lead';
    const PRODUCT_APPOINTMENT = 'appointment';

    protected $guarded = [self::ID];

    /**
     * @return BelongsTo
     */
    public function company()
    {
        return $this->belongsTo(
            EloquentCompany::class,
            self::COMPANY_ID,
            EloquentCompany::ID
        );
    }

    /**
     *
     * @return \App\Contracts\LeadCampaignCRMIntegration
     * @throws \ErrorException
     */
    public function integration()
    {
        /** @var LeadCampaignCRMIntegrationsHelper $crmIntegrationsHelperService */
        $crmIntegrationsHelperService = app()->make(LeadCampaignCRMIntegrationsHelper::class);

        if (!$this->hasLeadCampaignIntegration()) {
            throw new \ErrorException('Does not have a lead campaign crm integration');
        }

        return $crmIntegrationsHelperService->make($this->crm->name);
    }

    /**
     * @return bool
     */
    public function hasLeadCampaignIntegration()
    {
        /** @var LeadCampaignCRMIntegrationsHelper $crmIntegrationsHelperService */
        $crmIntegrationsHelperService = app()->make(LeadCampaignCRMIntegrationsHelper::class);
        return $crmIntegrationsHelperService->exists($this->crm->name)
            || $crmIntegrationsHelperService->existsLegacy($this->crm->name);
    }

    /**
     * @param EloquentQuoteCompany $quoteCompany
     * @param bool $isTestLead
     * @param array|null $overrideData
     * @return int
     * @throws \ErrorException
     */
    public function sendLeadViaCRM(EloquentQuoteCompany $quoteCompany, $isTestLead = false, $overrideData = null)
    {
        return $this->integration()->send($quoteCompany, $this, $isTestLead, $overrideData);
    }

    /**
     * @param EloquentQuoteCompany $quoteCompany
     * @return array
     */
    public function getBuildLeadData(EloquentQuoteCompany $quoteCompany)
    {
        return $this->integration()->buildData($quoteCompany, $this);
    }

    /**
     * @return HasMany
     */
    public function integrationFieldsMany()
    {
        return $this->hasMany(EloquentIntegrationField::class, EloquentIntegrationField::REL_ID, self::ID);
    }

    /**
     * @return string
     * @throws \ErrorException
     */
    public function integrationName()
    {
        return $this->integration()->name();
    }

    /**
     * @return bool
     * @throws \ErrorException
     */
    public function isIntegrationFunctional()
    {
        return $this->integration()->isFunctional($this);
    }

    /**
     * @return Collection|EloquentIntegrationField[]
     */
    public function getArrayIntegrationFields()
    {
        return $this->integrationFieldsMany()->getBaseQuery()
            ->where(EloquentIntegrationField::REL_ID, $this->id)
            ->where(EloquentIntegrationField::REL_TYPE, EloquentIntegrationField::REL_TYPE_CRM_INTEGRATION_ID)
            ->get();
    }

    /**
     * @return BelongsTo
     */
    public function crm()
    {
        return $this->belongsTo(
            Crm::class,
            self::CRM_ID,
            Crm::ID
        );
    }

    /**
     * @return MorphMany
     */
    public function integrationFields()
    {
        return $this->morphMany(EloquentIntegrationField::class, EloquentIntegrationField::REL_FUNC_NAME, EloquentIntegrationField::REL_TYPE, EloquentIntegrationField::REL_ID);
    }

    /**
     * @return HasMany
     */
    public function leadCampaignCrmIntegrations()
    {
        return $this->hasMany(LeadCampaignCrmIntegration::class, LeadCampaignCrmIntegration::CRM_INTEGRATION_ID, self::ID);
    }
}
