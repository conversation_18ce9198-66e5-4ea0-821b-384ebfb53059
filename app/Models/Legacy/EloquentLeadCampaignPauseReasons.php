<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Model;

/**
 * @class EloquentLeadCampaignPauseReasons
 *
 * @property string $id
 * @property string $lead_campaign_id
 * @property string $reason
 */
class EloquentLeadCampaignPauseReasons extends LegacyModel
{
    const ID = "id";
    const LEAD_CAMPAIGN_ID = "lead_campaign_id";
    const PAUSE_REASON = "reason";
    const CREATED_AT = "created_at";
    const UPDATED_AT = "updated_at";

    const TABLE = "tbl_lead_campaign_pause_reasons";

    protected $table = self::TABLE;

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function leadCampaign() {
        return $this->belongsTo(LeadCampaign::class, self::LEAD_CAMPAIGN_ID, LeadCampaign::ID);
    }
}
