<?php

namespace App\Models\Legacy;

use App\Models\ActivityFeed;
use App\Models\Legacy\LegacyModel;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Support\Facades\DB;

/**
 * Class EloquentEmail
 * @package App
 *
 * @property int $emailid
 * @property string $reltype
 * @property int $relid
 * @property string $fromaddress
 * @property string $toaddress
 * @property string $replyto
 * @property string $subject
 * @property string $content
 * @property string $attachments
 * @property string $status
 * @property string $statusmessage
 * @property int $timestampadded
 * @property int $timestampupdated
 * @property string $sendattempts
 * @property string $trackingreltype
 * @property string $trackingrelid
 * @property int $trackingid
 * @property int $timestampopened
 * @property int $priority
 * @property string $format
 */
class EloquentEmail extends LegacyModel
{
    /**
     * @param array $attributes
     */
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->table = DB::connection($this->connection)->getDatabaseName() . '.' . $this->getTable();
    }

    const ID = self::EMAIL_ID;
    const EMAIL_ID = 'emailid';
    const RELTYPE = 'reltype';
    const RELID = 'relid';
    const FROM_ADDRESS = 'from_address';
    const TO_ADDRESS = 'to_address';
    const REPLYTO = 'replyto';
    const SUBJECT = 'subject';
    const CONTENT = 'content';
    const ATTACHMENTS = 'attachments';
    const STATUS = 'status';
    const STATUSMESSAGE = 'statusmessage';
    const TIMESTAMPADDED = 'timestampadded';
    const TIMESTAMPUPDATED = 'timestampupdated';
    const TIMESTAMPOPENED = 'timestampopened';
    const SENDATTEMPTS = 'sendattempts';
    const TRACKINGRELTYPE = 'trackingreltype';
    const TRACKINGRELID = 'trackingrelid';
    const PRIORITY = 'priority';
    const FORMAT = 'format';

    const TABLE = 'tblemail';

    protected $table = self::TABLE;

    protected $primaryKey = self::ID;

    public $timestamps = false;

    /**
     * @return MorphOne
     */
    public function activity(): MorphOne
    {
        return $this->morphOne(ActivityFeed::class, 'item');
    }
}
