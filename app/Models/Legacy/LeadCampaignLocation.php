<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class LeadCampaignLocation
 *
 * @package App
 *
 * @property int $location_id
 *
 * @property-read Location $location
 */
class LeadCampaignLocation extends LegacyModel
{
    const TABLE = 'tbl_lead_campaign_locations';

    const ID               = 'id';
    const LEAD_CAMPAIGN_ID = 'lead_campaign_id';
    const LOCATION_ID      = 'location_id';

    const RELATION_LOCATION = 'location';

    protected $table      = self::TABLE;
    protected $guarded    = [self::ID];
    public    $timestamps = false;

    /**
     * @return BelongsTo
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(LegacyLocation::class, self::LOCATION_ID, LegacyLocation::ID);
    }
}
