<?php

namespace App\Models\Legacy;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Class CrmIntegrationField
 *
 * @package App
 *
 * @property int $id
 * @property int $crm_id
 * @property string $name
 * @property string $display_name
 * @property int $ordinal_position
 *
 * @property-read Crm $crm
 */
class CrmIntegrationField extends LegacyModel
{
    const TABLE = 'tbl_crm_integration_fields';
    protected $table = self::TABLE;

    const ID = 'id';
    const CRM_ID = 'crm_id';
    const NAME = 'name';
    const DISPLAY_NAME = 'display_name';
    const ORDINAL_POSITION = 'ordinal_position';

    protected $guarded = [self::ID];

    public $timestamps = false;

    /**
     * @return BelongsTo
     */
    public function crm()
    {
        return $this->belongsTo(
            Crm::class,
            self::CRM_ID,
            Crm::ID
        );
    }

    /**
     * @return HasMany
     */
    public function crmFields()
    {
        return $this->hasMany(CrmField::class, CrmField::CRM_INTEGRATION_FIELD_ID, self::ID);
    }
}
