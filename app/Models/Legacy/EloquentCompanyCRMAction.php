<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class EloquentCompanyCRMAction
 *
 * @property int $crmactionid
 * @property int $companyid
 * @property int $userid
 * @property int $contactid
 * @property string $actiontext
 * @property int $timestampadded
 * @property int $status
 *
 * @property-read EloquentCompany|null $company
 * @property-read EloquentUser|null $user
 * @property-read EloquentCompanyContact|null $contact
 */
class EloquentCompanyCRMAction extends LegacyModel
{
    const TABLE = 'tblcompanycrmaction';

    const ID              = self::CRM_ACTION_ID;
    const CRM_ACTION_ID   = 'crmactionid';
    const COMPANY_ID      = 'companyid';
    const USER_ID         = 'userid';
    const CONTACT_ID      = 'contactid';
    const ACTION_TEXT     = 'actiontext';
    const TIMESTAMP_ADDED = 'timestampadded';
    const STATUS          = 'status';

    const RELATION_COMPANY = 'company';
    const RELATION_USER    = 'user';
    const RELATION_CONTACT = 'contact';

    const STATUS_ACTIVE   = 1;
    const STATUS_INACTIVE = 0;

    protected $table      = self::TABLE;
    public    $primaryKey = self::ID;
    public    $timestamps = false;

    /**
     * @return BelongsTo
     */
    public function company()
    {
        return $this->belongsTo(EloquentCompany::class, self::COMPANY_ID, EloquentCompany::ID);
    }

    /**
     * @return BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(EloquentUser::class, self::USER_ID, EloquentUser::ID);
    }

    /**
     * @return BelongsTo
     */
    public function contact()
    {
        return $this->belongsTo(EloquentCompanyContact::class, self::CONTACT_ID, EloquentCompanyContact::FIELD_CONTACT_ID);
    }
}
