<?php

namespace App\Models\Legacy;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Class EloquentUser
 *
 * @property int $userid
 * @property string $firstname
 * @property string $lastname
 * @property string $title
 * @property int $status
 * @property int $securitylevel
 * @property int $dateregistered
 * @property string $phone
 * @property string $email
 * @property string $name
 * @property string $password
 * @property int $failedlogincount
 * @property int $scheduling_user_id
 * @property Carbon $deleted_at
 *
 * @property-read EloquentCompany|null $company
 */
class EloquentUser extends LegacyModel
{
    use SoftDeletes, HasFactory;

    const TABLE = 'tbluser';

    const ID         = self::USER_ID;
    const USER_ID    = 'userid';
    const DISPLAY_NAME = 'displayname';

    const FIRST_NAME = 'firstname';
    const LAST_NAME  = 'lastname';
    const TITLE      = 'title';
    const STATUS     = 'status';
    const PHONE      = 'phone';
    const EMAIL      = 'email';
    const SECURITY_LEVEL = 'securitylevel';
    const COMPANY_ID = 'companyid';
    const FAILED_LOGIN_COUNT    = 'failedlogincount';
    const PASSWORD              = 'password';
    const DELETED_AT = 'deleted_at';
    const DATE_REGISTERED   = 'dateregistered';
    const MEETING_URL       = 'meeting_url';
    const SCHEDULING_USER_ID = 'scheduling_user_id';

    const FIELD_STATUS_VALUE_ACTIVE   = 1;
    const FIELD_STATUS_VALUE_INACTIVE = 0;
    const ACTIVE_STATUS               = 'active';
    const INACTIVE_STATUS             = 'inactive';

    const RELATION_HISTORY_LOG = 'historyLog';
    const RELATION_COMPANY = 'company';

    /** @var string $table */
    protected $table      = self::TABLE;

    protected $primaryKey = self::ID;

    public    $timestamps = false;

    /**
     * @return bool
     */
    public function active(): bool
    {
        return $this->status == 1;
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(EloquentCompany::class, self::COMPANY_ID, EloquentCompany::ID);
    }

    public function name(): Attribute
    {
        return Attribute::make(get: fn() => $this->firstname . ' ' . $this->lastname);
    }

    /**
     * Gets a history log of the user
     * @return HasMany
     */
    public function historyLog()
    {
        return $this->hasMany(EloquentHistoryLog::class, EloquentHistoryLog::USER_ID, self::ID);
    }
}
