<?php

namespace App\Models\Legacy;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class EloquentQuoteEvent
 *
 * @property int $id
 * @property int $quote_id
 * @property string $type
 * @property string $value
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property EloquentQuote|null $quote
 */
class EloquentQuoteEvent extends Model
{
    const ID       = 'id';
    const QUOTE_ID = 'quote_id';
    const TYPE     = 'type';
    const VALUE    = 'value';

    const TYPE_LEAD_VERIFIED = 'lead_verified';

    protected $table = 'tbl_quote_events';

    /**
     * @return BelongsTo
     */
    function quote(): BelongsTo
    {
        return $this->belongsTo(EloquentQuote::class, self::QUOTE_ID);
    }
}
