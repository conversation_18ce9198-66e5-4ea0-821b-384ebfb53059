<?php

namespace App\Models\Legacy;

use App\Models\Legacy\LegacyModel;

/**
 * Class CompanyAttachment
 * @package App
 *
 * @property int $id
 * @property int $company_id
 * @property string $name
 * @property string $path
 * @property string $extension
 * @property string $reference
 * @property string $file
 */
class CompanyAttachment extends LegacyModel
{
    const ATTRIBUTE_ID         = 'id';
    const ATTRIBUTE_REFERENCE  = 'reference';
    const ATTRIBUTE_COMPANY_ID = 'company_id';
    const ATTRIBUTE_NAME       = 'name';
    const ATTRIBUTE_PATH       = 'path';
    const ATTRIBUTE_EXTENSION  = 'extension';
    const ATTRIBUTE_CREATED_AT = 'created_at';
    const ATTRIBUTE_UPDATED_AT = 'updated_at';
    const ATTRIBUTE_FILE       = 'file';

    public $table = "company_attachments";
    public $timestamps = true;
}
