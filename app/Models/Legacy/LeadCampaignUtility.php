<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * Class LeadCampaignUtility
 *
 * @package App
 *
 * @property int $id
 * @property int $lead_campaign_id
 * @property int $utility_id
 *
 * @property-read LeadCampaign $leadCampaign
 * @property-read EloquentUtility $utility
 */
class LeadCampaignUtility extends LegacyModel
{
    const TABLE = 'tbl_lead_campaign_utilities';

    const ID               = 'id';
    const LEAD_CAMPAIGN_ID = 'lead_campaign_id';
    const UTILITY_ID       = 'utility_id';

    protected $table      = self::TABLE;
    protected $guarded    = [self::ID];
    public    $timestamps = false;

    /**
     * @return HasOne
     */
    public function utility(): HasOne
    {
        return $this->hasOne(EloquentUtility::class, EloquentUtility::ID, self::UTILITY_ID);
    }
}
