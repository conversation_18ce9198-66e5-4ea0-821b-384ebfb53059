<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class LeadCampaignLeadCategory
 *
 * @property int $id
 * @property int $lead_campaign_id
 * @property int $lead_category_id
 *
 * @property-read LeadCategory $leadCategory
 * @property-read LeadCampaign $leadCampaign
 */
class LeadCampaignLeadCategory extends LegacyModel
{
    const ID = 'id';
    const LEAD_CAMPAIGN_ID = 'lead_campaign_id';
    const LEAD_CATEGORY_ID = 'lead_category_id';

    const TABLE = 'tbl_lead_campaign_lead_categories';

    const RELATION_LEAD_CATEGORY = 'leadCategory';
    const RELATION_LEAD_CAMPAIGN = 'leadCampaign';

    protected $table = self::TABLE;
    protected $guarded = [self::ID];
    public $timestamps = false;

    /**
     * @return BelongsTo
     */
    public function leadCategory(): BelongsTo
    {
        return $this->belongsTo(
            LeadCategory::class,
            self::LEAD_CATEGORY_ID,
            LeadCategory::ID
        );
    }

    /**
     * @return BelongsTo
     */
    public function leadCampaign(): BelongsTo
    {
        return $this->belongsTo(
            LeadCampaign::class,
            self::LEAD_CAMPAIGN_ID,
            LeadCampaign::ID
        );
    }
}
