<?php

namespace App\Models\Legacy;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class LeadRoofDetail
 *
 * @package App
 *
 * @property int $id
 * @property string $uuid
 * @property int $quote_id
 * @property float|null $predominant_slope
 * @property int $complexity
 * @property int $skylights
 * @property int $vents
 * @property int $dormers
 * @property int $chimneys
 * @property int $hvac_units
 * @property int $other_obstructions
 * @property bool $requested_new_gutters
 * @property bool $requested_roof_tear_off
 * @property bool $requested_roof_disposal
 * @property float|null $roof_estimate_low
 * @property float|null $roof_estimate_median
 * @property float|null $roof_estimate_high
 * @property float|null $roof_replacement_area
 * @property string|null $trusted_form_cert
 * @property string|null $trusted_form_token
 * @property string|null $trusted_form_ping
 * @property string|null $roof_sub_type
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read EloquentQuote|null $lead
 */
class LeadRoofDetail extends LegacyModel
{
    const TABLE = 'lead_roof_details';

    const FIELD_ID                      = 'id';
    const FIELD_UUID                    = 'uuid';
    const FIELD_QUOTE_ID                = 'quote_id';
    const FIELD_PREDOMINANT_SLOPE       = 'predominant_slope';
    const FIELD_COMPLEXITY              = 'complexity';
    const FIELD_ROOF_SUB_TYPE           = 'roof_sub_type';
    const FIELD_SKYLIGHTS               = 'skylights';
    const FIELD_VENTS                   = 'vents';
    const FIELD_DORMERS                 = 'dormers';
    const FIELD_CHIMNEYS                = 'chimneys';
    const FIELD_HVAC_UNITS              = 'hvac_units';
    const FIELD_OTHER_OBSTRUCTIONS      = 'other_obstructions';
    const FIELD_REQUESTED_NEW_GUTTERS   = 'requested_new_gutters';
    const FIELD_REQUESTED_ROOF_TEAR_OFF = 'requested_roof_tear_off';
    const FIELD_REQUESTED_ROOF_DISPOSAL = 'requested_roof_disposal';
    const FIELD_ROOF_ESTIMATE_LOW       = 'roof_estimate_low';
    const FIELD_ROOF_ESTIMATE_MEDIAN    = 'roof_estimate_median';
    const FIELD_ROOF_ESTIMATE_HIGH      = 'roof_estimate_high';
    const FIELD_ROOF_REPLACEMENT_AREA   = 'roof_replacement_area';
    const FIELD_TRUSTED_FORM_CERT       = 'trusted_form_cert';
    const FIELD_TRUSTED_FORM_TOKEN      = 'trusted_form_token';
    const FIELD_TRUSTED_FORM_PING       = 'trusted_form_ping';

    const REFERENCE = self::FIELD_UUID;

    const RELATION_LEAD = 'lead';

    protected $guarded = [self::FIELD_ID, self::FIELD_UUID, self::FIELD_QUOTE_ID];

    protected $casts = [
        self::FIELD_REQUESTED_NEW_GUTTERS   => 'boolean',
        self::FIELD_REQUESTED_ROOF_DISPOSAL => 'boolean',
        self::FIELD_REQUESTED_ROOF_TEAR_OFF => 'boolean',
        self::CREATED_AT                    => 'datetime',
        self::UPDATED_AT                    => 'datetime',
    ];

    protected $table = self::TABLE;

    /**
     * Defines the belongs to relationship to a given lead.
     *
     * @return BelongsTo
     */
    public function lead(): BelongsTo
    {
        return $this->belongsTo(EloquentQuote::class, self::FIELD_QUOTE_ID, EloquentQuote::QUOTE_ID);
    }
}
