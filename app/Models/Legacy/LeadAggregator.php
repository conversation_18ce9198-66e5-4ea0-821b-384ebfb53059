<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\SoftDeletes;

class LeadAggregator extends LegacyModel
{
    use SoftDeletes;

    const TABLE = "lead_aggregators";

    const FIELD_ID = 'id';
    const FIELD_COMPANY_ID = 'company_id';
    const FIELD_TIMEZONE = 'timezone';
    const FIELD_DAYLIGHT_SAVINGS = 'daylight_savings';
    const FIELD_DAILY_CAP = 'daily_cap';
    const FIELD_STATUS = 'status';
    const FIELD_CREATED_AT = 'created_at';
    const FIELD_UPDATED_AT = 'updated_at';
    const FIELD_DELETED_AT = 'deleted_at';

    const RELATION_SCHEDULES = 'schedules';
    const RELATION_INDUSTRIES = 'industries';
    const RELATION_LEAD_CATEGORIES = 'leadCategories';
    const RELATION_LEAD_TYPES = 'leadTypes';
    const RELATION_SALES_HISTORY = 'salesHistory';
    const RELATION_PING_POST_FIELDS = 'pingPostFields';
    const RELATION_PING_POST_ENDPOINT = 'pingPostEndpoint';
    const RELATION_COMPANY = 'company';

    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;

    const USA_EASTERN_TIMEZONE = 'America/New_York';
    const USA_CENTRAL_TIMEZONE = 'America/Chicago';
    const USA_MOUNTAIN_TIMEZONE = 'America/Denver';
    const USA_PACIFIC_TIMEZONE = 'America/Los_Angeles';
    const USA_ALASKA_TIMEZONE = 'America/Anchorage';
    const USA_HAWAII_TIMEZONE = 'Pacific/Honolulu';

    protected $fillable = [
        self::FIELD_ID
    ];

    protected $table = self::TABLE;

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function schedules() {
        return $this->hasMany(LeadAggregatorSchedule::class, LeadAggregatorSchedule::FIELD_LEAD_AGGREGATOR_ID, self::FIELD_ID);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function industries() {
        return $this->hasMany(LeadAggregatorIndustry::class, LeadAggregatorIndustry::FIELD_LEAD_AGGREGATOR_ID, self::FIELD_ID);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function leadCategories() {
        return $this->hasMany(LeadAggregatorLeadCategory::class, LeadAggregatorLeadCategory::FIELD_LEAD_AGGREGATOR_ID, self::FIELD_ID);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function leadTypes() {
        return $this->hasMany(LeadAggregatorLeadType::class, LeadAggregatorLeadType::FIELD_LEAD_AGGREGATOR_ID, self::FIELD_ID);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function salesHistory() {
        return $this->hasMany(LeadAggregatorSaleHistory::class, LeadAggregatorSaleHistory::FIELD_LEAD_AGGREGATOR_ID, self::FIELD_ID);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function pingPostFields() {
        return $this->hasMany(LeadAggregatorPingPostField::class, LeadAggregatorPingPostField::FIELD_LEAD_AGGREGATOR_ID, self::FIELD_ID);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function pingPostEndpoint() {
        return $this->hasOne(LeadAggregatorPingPostEndpoint::class, LeadAggregatorPingPostEndpoint::FIELD_LEAD_AGGREGATOR_ID, self::FIELD_ID);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function company() {
        return $this->belongsTo(EloquentCompany::class, self::FIELD_COMPANY_ID);
    }
}
