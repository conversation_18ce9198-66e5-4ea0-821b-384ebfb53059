<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class EloquentQuoteLog
 *
 * @package App
 *
 * @property int $quotelogid
 * @property int $quoteid
 * @property string $data
 * @property int $addedbyuserid
 * @property int $timestampadded
 *
 * @property-read EloquentQuote|null $quote
 * @property-read EloquentUser|null $addedbyuser
 */
class EloquentQuoteLog extends LegacyModel
{
    const TABLE = 'tblquotelog';
    const ID = self::QUOTE_LOG_ID;
    const QUOTE_LOG_ID = 'quotelogid';
    const QUOTE_ID = 'quoteid';
    const DATA = 'data';
    const ADDED_BY_USER_ID = 'addedbyuserid';
    const TIMESTAMP_ADDED = 'timestampadded';

    const RELATION_QUOTE = 'quote';
    const RELATION_ADDED_BY_USER = 'addedbyuser';

    protected $table = self::TABLE;
    public $timestamps = false;
    protected $primaryKey = self::ID;
    protected $fillable = [
        self::QUOTE_ID,
        self::DATA,
        self::ADDED_BY_USER_ID
    ];

    /**
     * @return BelongsTo
     */
    function quote() {
        return $this->belongsTo(EloquentQuote::class, self::QUOTE_ID, EloquentQuote::ID);
    }

    /**
     * @return BelongsTo
     */
    function addedbyuser() {
        return $this->belongsTo(EloquentUser::class, self::ADDED_BY_USER_ID, EloquentUser::ID);
    }
}
