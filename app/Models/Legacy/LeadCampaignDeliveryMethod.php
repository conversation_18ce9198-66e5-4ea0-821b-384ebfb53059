<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LeadCampaignDeliveryMethod extends LegacyModel
{
    const TABLE = 'tbl_lead_campaign_delivery_methods';

    const FIELD_ID                      = 'id';
    const FIELD_LEAD_CAMPAIGN_ID        = 'lead_campaign_id';
    const FIELD_LEAD_DELIVERY_METHOD_ID = 'lead_delivery_method_id';
    const FIELD_TYPE                    = 'type';

    const TYPE_VALUE_SMS   = 'sms';
    const TYPE_VALUE_EMAIL = 'email';
    const TYPE_VALUE_APPT_EMAIL = 'apptEmail';
    const TYPE_VALUE_APPT_SMS = 'apptSms';

    const RELATION_LEAD_DELIVERY_METHOD = 'leadDeliveryMethod';

    protected $table = self::TABLE;

    /**
     * @return BelongsTo
     */
    public function leadDeliveryMethod()
    {
        return $this->belongsTo(
            LeadDeliveryMethod::class,
            self::FIELD_LEAD_DELIVERY_METHOD_ID,
            LeadDeliveryMethod::FIELD_ID
        );
    }

    /**
     * @return array
     */
    public function getTypeArray()
    {
        return explode(',', $this->{self::FIELD_TYPE}) ?? [];
    }
}
