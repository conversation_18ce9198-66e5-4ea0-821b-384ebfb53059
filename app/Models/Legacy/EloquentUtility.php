<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * Class EloquentUtility
 *
 * @property string $utilityid
 * @property string $name
 * @property string $abbreviation
 * @property string $state
 * @property string $uuid
 * @property string $slug
 * @property int $eia_id
 */
class EloquentUtility extends LegacyModel
{
    const TABLE = 'tblutility';

    const ID               = self::FIELD_UTILITY_ID;
    const FIELD_UTILITY_ID = 'utilityid';
    const FIELD_NAME       = 'name';
    const FIELD_STATE      = 'state';
    const FIELD_UUID       = 'uuid';
    const FIELD_SLUG       = 'slug';
    const FIELD_EIA_ID     = 'eia_id';

    protected $primaryKey = self::ID;
    protected $table      = self::TABLE;
    public    $timestamps = true;
}
