<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Class Industry
 * @package App
 *
 * @property string $display_name
 * @property string $key
 * @property \DateTime $created_at
 * @property \DateTime $updated_at
 *
 * @property-read Collection|IndustryType[] $types
 */
class Industry extends LegacyModel
{
    const TABLE = 'industries';

    const FIELD_ID           = 'id';
    const FIELD_DISPLAY_NAME = 'display_name';
    const FIELD_KEY          = 'key';

    const KEY_SOLAR = 'solar';
    const KEY_ROOFING = 'roofing';

    /**
     * @return HasMany
     */
    function types(): HasMany
    {
        return $this->hasMany(IndustryType::class);
    }

}
