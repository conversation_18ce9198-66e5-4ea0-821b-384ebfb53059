<?php

namespace App\Models\Legacy;

/**
 * Class EloquentZipCode
 *
 * @property float $utc
 * @property string $dst
 */
class EloquentZipCode extends LegacyModel
{
    const string TABLE = 'tblzipcode';

    const string FIELD_ZIP_CODE_ID = 'zipcodeid';
    const string FIELD_ZIP_CODE    = 'zipcode';
    const string FIELD_COUNTY_FIPS = 'countyfips';
    const string FIELD_STATE_FIPS  = 'statefips';
    const string FIELD_ZIP_TYPE    = 'ziptype';
    const string FIELD_CITY_TYPE   = 'citytype';
    const string FIELD_UTC         = 'utc';
    const string FIELD_DST         = 'dst';

    const string ZIP_TYPE_STANDARD = 'S';
    const string CITY_TYPE_PRIMARY = 'D';

    protected $table      = self::TABLE;
    protected $primaryKey = self::FIELD_ZIP_CODE_ID;
    public    $timestamps = false;
}
