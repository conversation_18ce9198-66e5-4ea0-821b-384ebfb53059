<?php

namespace App\Models\Legacy;

use App\Enums\Locations\LocationType;
use App\Models\Locations\USLocation;

/**
 * Pass-through for new A2 model. Legacy model has been renamed LegacyLocation
 * @see LegacyLocation
 *
 * @property int $id
 * @property LocationType $type
 * @property string $state_abbr
 * @property string $state
 * @property string $state_key
 * @property string $county
 * @property string $county_key
 * @property string $city
 * @property string $city_key
 * @property string $zip_code
 */
class Location extends USLocation
{
    const string TABLE = USLocation::TABLE;

    const string ID                 = USLocation::FIELD_ID;
    const string TYPE               = USLocation::FIELD_TYPE;
    const string ZIP_CODE           = USLocation::FIELD_ZIP_CODE;
    const string STATE_ABBREVIATION = USLocation::FIELD_STATE_ABBREVIATION;
    const string STATE              = USLocation::FIELD_STATE;
    const string STATE_KEY          = USLocation::FIELD_STATE_KEY;
    const string COUNTY             = USLocation::FIELD_COUNTY;
    const string COUNTY_KEY         = USLocation::FIELD_COUNTY_KEY;
    const string CITY               = USLocation::FIELD_CITY;
    const string CITY_KEY           = USLocation::FIELD_CITY_KEY;

    protected $table = self::TABLE;
}
