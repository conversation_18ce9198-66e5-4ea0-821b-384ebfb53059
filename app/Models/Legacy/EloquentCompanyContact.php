<?php

namespace App\Models\Legacy;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class EloquentCompanyContact
 * @property int $contactid
 * @property int $companyid
 * @property string $title
 * @property string $firstname
 * @property string $lastname
 * @property string $email
 * @property string $phone
 * @property string $mobile
 * @property int $addressid
 * @property int $status
 * @property int $timestampadded
 * @property int $timestampupdated
 * @property Carbon $deleted_at
 * @property string $notes
 * @property int $ispublic
 * @property string $zoom_info_id
 * @property string $name
 *
 * @property EloquentCompany|null $company
 */
class EloquentCompanyContact extends LegacyModel
{
    use SoftDeletes;

    const FIELD_CONTACT_ID        = 'contactid';
    const FIELD_COMPANY_ID        = 'companyid';
    const FIELD_TITLE             = 'title';
    const FIELD_FIRST_NAME        = 'firstname';
    const FIELD_LAST_NAME         = 'lastname';
    const FIELD_EMAIL             = 'email';
    const FIELD_PHONE             = 'phone';
    const FIELD_MOBILE            = 'mobile';
    const FIELD_ADDRESS_ID        = 'addressid';
    const FIELD_STATUS            = 'status';
    const FIELD_TIMESTAMP_ADDED   = 'timestampadded';
    const FIELD_TIMESTAMP_UPDATED = 'timestampupdated';
    const FIELD_NOTES             = 'notes';
    const FIELD_IS_PUBLIC         = 'ispublic';
    const FIELD_DELETED_AT         = 'deleted_at';
    const FIELD_ZOOM_INFO_ID       = 'zoom_info_id';

    const RELATION_COMPANY = 'company';
    const RELATION_ALERTS  = 'alerts';

    const TABLE = 'tblcompanycontact';

    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;
    const IS_NOT_PUBLIC = 0;
    const IS_PUBLIC = 1;

    protected $table = self::TABLE;

    protected $primaryKey = self::FIELD_CONTACT_ID;

    public $timestamps = false;

    protected $fillable = [
        self::FIELD_COMPANY_ID,
        self::FIELD_FIRST_NAME,
        self::FIELD_LAST_NAME,
        self::FIELD_TITLE,
        self::FIELD_EMAIL,
        self::FIELD_PHONE,
        self::FIELD_MOBILE,
        self::FIELD_STATUS,
        self::FIELD_ZOOM_INFO_ID
    ];

    protected $casts = [self::FIELD_DELETED_AT => 'datetime'];

    protected $guarded = [self::FIELD_CONTACT_ID];

    /**
     * @return BelongsTo
     */
    public function company()
    {
        return $this->belongsTo(EloquentCompany::class, self::FIELD_COMPANY_ID, EloquentCompany::ID);
    }

    public function name(): Attribute
    {
        return Attribute::make(get: fn() => $this->firstname . ' ' . $this->lastname);
    }
}
