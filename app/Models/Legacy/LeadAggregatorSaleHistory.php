<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\SoftDeletes;

class LeadAggregatorSaleHistory extends LegacyModel
{
    use SoftDeletes;

    const TABLE = 'lead_aggregator_sales_history';

    const FIELD_ID = 'id';
    const FIELD_LEAD_AGGREGATOR_ID = 'lead_aggregator_id';
    const FIELD_QUOTE_ID = 'quote_id';
    const FIELD_RESPONSE_STATUS = 'response_status';
    const FIELD_TRANSACTION_TYPE = 'transaction_type';
    const FIELD_PRICE = 'price';
    const FIELD_ERRORS = 'errors';
    const FIELD_CREATED_AT = 'created_at';
    const FIELD_UPDATED_AT = 'updated_at';
    const FIELD_DELETED_AT = 'deleted_at';

    const RELATION_LEAD_AGGREGATOR = 'leadAggregator';
    const RELATION_QUOTE = 'quote';

    const TRANSACTION_TYPE_PING = 'ping'; //bid
    const TRANSACTION_TYPE_POST = 'post'; //sell
    const TRANSACTION_TYPE_EXCEPTION = 'exception'; // exception

    const RESPONSE_STATUS_SUCCESS = 1;
    const RESPONSE_STATUS_FAILURE = 0;

    protected $fillable = [
        self::FIELD_ID
    ];

    protected $table = self::TABLE;

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function leadAggregator() {
        return $this->belongsTo(LeadAggregator::class, self::FIELD_LEAD_AGGREGATOR_ID);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function quote() {
        return $this->hasOne(EloquentQuote::class, EloquentQuote::ID, self::FIELD_QUOTE_ID);
    }
}
