<?php

namespace App\Models\Legacy;

/**
 * @property int $company_id
 * @property string $provider_code
 */
class EloquentCompanyPaymentProfile extends LegacyModel
{
    const TABLE = 'company_payment_profiles';

    const COMPANY_ID    = 'company_id';
    const PROVIDER_CODE = 'provider_code';
    const PAYMENT_PROVIDER_IDENTIFIER = 'payment_provider_identifier';

    const SHORT_CODE_STRIPE = 'str';
    const SHORT_CODE_AUTHORIZE_NET = 'cim';

    const DISPLAY_NAME_STRIPE = 'Stripe';
    const DISPLAY_NAME_AUTHORIZE_NET = 'Authorize.net';

    protected $table = self::TABLE;

    protected $guarded = ["id"];
}
