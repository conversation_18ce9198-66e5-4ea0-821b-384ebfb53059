<?php

namespace App\Models\Legacy;

/**
 * @property int $id
 * @property int $lead_id
 * @property string $appointment_type
 * @property $appointment_date
 * @property $appointment_time
 */
class LeadAppointment extends LegacyModel
{
    const TABLE = 'lead_appointments';

    const ID = 'id';
    const LEAD_ID = 'lead_id';
    const APPOINTMENT_TYPE = 'appointment_type';
    const APPOINTMENT_DATE = 'appointment_date';
    const APPOINTMENT_TIME = 'appointment_time';

    const APPOINTMENT_TYPE_HOME = 'In-Home';
    const APPOINTMENT_TYPE_ONLINE = 'Online';

    const APPOINTMENT_TYPES = [
        self::APPOINTMENT_TYPE_HOME,
        self::APPOINTMENT_TYPE_ONLINE
    ];

    protected $table = self::TABLE;

    protected $guarded = [self::ID];
}
