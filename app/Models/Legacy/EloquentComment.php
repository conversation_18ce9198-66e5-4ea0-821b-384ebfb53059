<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class EloquentComment
 * @property int $commentid
 * @property int $ispublic
 * @property string $reltype
 * @property int $relid
 * @property string $comment
 * @property int $addedbyuserid
 * @property int $timestampadded
 * @property-read EloquentUser $user
 */
class EloquentComment extends LegacyModel
{
    const string TABLE = 'tblcomment';

    const ID         = self::COMMENT_ID;
    const COMMENT_ID = 'commentid';
    const IS_PUBLIC  = 'ispublic';
    const REL_TYPE = 'reltype';
    const REL_ID = 'relid';
    const COMMENT = 'comment';
    const ADDED_BY_USER_ID = 'addedbyuserid';
    const TIMESTAMP_ADDED = 'timestampadded';

    const REL_TYPE_QUOTE = 'quote';

    protected $primaryKey = self::ID;
    public    $timestamps = false;
    protected $casts      = [
        self::IS_PUBLIC => 'boolean',
    ];

    protected $table = self::TABLE;

    protected $fillable = [
        self::REL_ID,
        self::REL_TYPE,
        self::COMMENT,
        self::ADDED_BY_USER_ID,
        self::TIMESTAMP_ADDED,
    ];

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(EloquentUser::class,  self::ADDED_BY_USER_ID, EloquentUser::USER_ID);
    }
}
