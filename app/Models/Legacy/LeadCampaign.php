<?php

namespace App\Models\Legacy;

use App\Models\Campaigns\CompanyCampaignRelation;
use App\Models\ComputedRejectionStatistic;
use App\Models\Odin\ProductCampaign;
use App\Models\SalesBaitLead;
use App\Repositories\Odin\ProductRepository;
use App\Services\DatabaseHelperService;
use Carbon\Carbon;
use Database\Factories\LeadCampaignFactory;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use \Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon as SupportCarbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

/**
 * Class LeadCampaign
 *
 * @property int $id
 * @property Carbon $last_modified_lead_limit
 * @property int $company_id
 * @property string $name
 * @property string $uuid
 * @property boolean $status
 * @property bool $zip_code_targeted
 *
 * @property-read EloquentCompany $company
 * @property-read boolean         $has_utility_filters
 * @property-read integer         $max_daily_spend
 * @property-read integer         $max_daily_lead
 * @property-read integer         $maximum_budget_usage
 * @property-read Collection      $leadCampaignUtilities
 * @property-read Collection      $leadCampaignLocations
 * @property-read Collection      $leadCampaignZipLocations
 * @property-read Collection      $leadCampaignSalesTypeConfiguration
 * @property-read Collection      $leadSalesTypeConfigurations
 * @property-read Collection      $leadCampaignDeliveryMethods
 * @property-read Collection $salesBaitLeads
 * @property-read ProductCampaign $productCampaign
 * @property-read EloquentLeadCampaignPauseReasons[] $leadCampaignPauseReasons
 * @property-read CompanyCampaignRelation $companyCampaignRelation
 * @property-read LowBid[] $lowBids
 */
class LeadCampaign extends LegacyModel
{
    use SoftDeletes;
    use HasFactory;

    const TABLE = 'tbl_lead_campaigns';

    const ID                       = 'id';
    const NAME                     = 'name';
    const COMPANY_ID               = 'company_id';
    const LAST_MODIFIED_LEAD_LIMIT = 'last_modified_lead_limit';
    const REACTIVATE_DATE          = 'reactivate_date';
    const DELETED_AT               = 'deleted_at';
    const STATUS                   = 'status';
    const MAX_DAILY_SPEND          = 'max_daily_spend';
    const MAX_DAILY_LEAD           = 'max_daily_lead';
    const MAXIMUM_BUDGET_USAGE     = 'maximum_budget_usage';
    const HAS_UTILITY_FILTERS      = 'has_utility_filters';
    const UUID                     = 'uuid';
    const ALLOW_NON_BUDGET_PREMIUM_LEADS = 'allow_non_budget_premium_leads';
    const IS_MANAGED_BY_A2         = 'is_managed_by_a2';
    const ZIP_CODE_TARGETED        = 'zip_code_targeted';

    const STATUS_ACTIVE   = 1;
    const STATUS_INACTIVE = 0;
    const STATUS_ENABLE   = self::STATUS_ACTIVE;
    const STATUS_DISABLE  = self::STATUS_INACTIVE;

    const DEFAULT_CAMPAIGN_NAME = 'DEFAULT';

    const DISPLAY_BUDGET_UNIT_LEAD = 'lead';
    const DISPLAY_BUDGET_UNIT_CURRENCY = 'currency';
    const DISPLAY_BUDGET_UNIT_UNLIMITED = 'unlimited';

    const SUB_QUERY_QUOTE_COMPANY_SUB_TABLE = 'quote_company_sub_table';

    const RELATION_COMPANY                       = 'company';
    const RELATION_LEAD_CAMPAIGN_LOCATIONS       = 'leadCampaignLocations';
    const RELATION_LEAD_CAMPAIGN_LOCATIONS_LOCATIONS = 'leadCampaignLocationLocations';
    const RELATION_LEAD_CAMPAIGN_ZIP_LOCATIONS   = 'leadCampaignZipLocations';
    const RELATION_LEAD_CAMPAIGN_STATE_LOCATIONS = 'leadCampaignStateLocations';
    const RELATION_LEAD_CAMPAIGN_COUNTY_LOCATIONS = 'leadCampaignCountyLocations';
    const RELATION_LEAD_CAMPAIGN_UTILITIES       = 'leadCampaignUtilities';
    const RELATION_SALES_BAIT_LEADS              = 'salesBaitLeads';
    const RELATION_LEAD_CAMPAIGN_LEAD_CATEGORIES = 'leadCampaignLeadCategories';
    const RELATION_LEAD_SALES_TYPE_CONFIGURATIONS = 'leadSalesTypeConfigurations';
    const RELATION_LEAD_CAMPAIGN_DELIVERY_METHODS = 'leadCampaignDeliveryMethods';
    const RELATION_LEAD_CAMPAIGN_CRM_INTEGRATION = 'leadCampaignCrmIntegrations';
    const RELATION_PRODUCT_CAMPAIGN = 'productCampaign';
    const RELATION_LEAD_PRICES = 'leadPrices';
    const RELATION_LEAD_CAMPAIGN_PAUSE_REASONS = 'leadCampaignPauseReasons';
    const RELATION_COMPANY_CAMPAIGN_RELATION = 'companyCampaignRelation';
    const RELATION_LOW_BIDS = 'lowBids';

    protected $table = self::TABLE;

    protected $guarded = [self::ID];

    protected $casts = [
        self::LAST_MODIFIED_LEAD_LIMIT => 'datetime',
        self::REACTIVATE_DATE          => 'datetime',
    ];

    protected $hidden = [self::DELETED_AT];

    /**
     * @return LeadCampaignFactory
     */
    protected static function newFactory(): LeadCampaignFactory
    {
        return LeadCampaignFactory::new();
    }

    public function isUnlimitedBudget(): bool
    {
        return !$this->isSpendBudget() && !$this->isVolumeBudget();
    }

    public function isSpendBudget(): bool
    {
        return $this->max_daily_spend && $this->max_daily_spend > 0;
    }

    public function isVolumeBudget(): bool
    {
        return !$this->isSpendBudget() && $this->max_daily_lead && $this->max_daily_lead > 0;
    }

    public function formatBudget(): string
    {
        if ($this->max_daily_spend) {
            return "$" . number_format($this->max_daily_spend, 2);
        } elseif($this->max_daily_lead) {
            return $this->max_daily_lead . ' ' . Str::plural('Lead', $this->max_daily_lead);
        } else {
            return self::DISPLAY_BUDGET_UNIT_UNLIMITED;
        }
    }

    /**
     * @return string
     */
    public function getDisplayBudgetUnit(): string
    {
        if ($this->max_daily_spend) {
            return self::DISPLAY_BUDGET_UNIT_CURRENCY;
        } elseif($this->max_daily_lead) {
            return self::DISPLAY_BUDGET_UNIT_LEAD;
        } else {
            return self::DISPLAY_BUDGET_UNIT_UNLIMITED;
        }
    }

    /**
     * @return float|int
     */
    public function getBudget(): float|int
    {
        if ($this->max_daily_spend) {
            return $this->max_daily_spend;
        } elseif($this->max_daily_lead) {
            return $this->max_daily_lead;
        } else {
            return 0;
        }
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(EloquentCompany::class, self::COMPANY_ID, EloquentCompany::ID);
    }

    /**
     * @return HasMany
     */
    public function leadCampaignUtilities(): HasMany
    {
        return $this->hasMany(
            LeadCampaignUtility::class,
            LeadCampaignUtility::LEAD_CAMPAIGN_ID,
            self::ID
        );
    }

    /**
     * @return HasMany
     */
    public function leadCampaignLocations(): HasMany
    {
        return $this->hasMany(
            LeadCampaignLocation::class,
            LeadCampaignLocation::LEAD_CAMPAIGN_ID,
            self::ID
        );
    }

    /**
     * @return HasMany
     */
    public function leadCampaignZipLocations(): HasMany
    {
        return $this->hasMany(
            LeadCampaignLocation::class,
            LeadCampaignLocation::LEAD_CAMPAIGN_ID,
            self::ID
        )->whereHas(LeadCampaignLocation::RELATION_LOCATION, fn($query) => $query->where(Location::TYPE, Location::TYPE_ZIP_CODE));
    }

    /**
     * @return BelongsToMany
     */
    public function leadCampaignLocationLocations(): BelongsToMany
    {
        return $this->belongsToMany(
            Location::class,
            DatabaseHelperService::readOnlyDatabase() .'.'. LeadCampaignLocation::TABLE,
            LeadCampaignLocation::LEAD_CAMPAIGN_ID,
            LeadCampaignLocation::LOCATION_ID
        );
    }

    /**
     * @return HasMany
     */
    public function leadCampaignStateLocations(): HasMany
    {
        return $this->hasMany(
            LeadCampaignLocation::class,
            LeadCampaignLocation::LEAD_CAMPAIGN_ID,
            self::ID
        )->whereHas(LeadCampaignLocation::RELATION_LOCATION, fn($query) => $query->where(Location::TYPE, Location::TYPE_STATE));
    }

    /**
     * @return HasMany
     */
    public function leadCampaignCountyLocations(): HasMany
    {
        return $this->hasMany(
            LeadCampaignLocation::class,
            LeadCampaignLocation::LEAD_CAMPAIGN_ID,
            self::ID
        )->whereHas(LeadCampaignLocation::RELATION_LOCATION, fn($query) => $query->where(Location::TYPE, Location::TYPE_COUNTY));
    }

    /**
     * @return HasMany
     */
    public function leadCampaignLeadCategories(): HasMany
    {
        return $this->hasMany(
            LeadCampaignLeadCategory::class,
            LeadCampaignLeadCategory::LEAD_CAMPAIGN_ID,
            self::ID
        );
    }

    /**
     * @return HasMany
     */
    public function salesBaitLeads(): HasMany
    {
        return $this->hasMany(
            SalesBaitLead::class,
            SalesBaitLead::FIELD_CAMPAIGN_ID,
            self::ID
        );
    }

    /**
     * @return HasMany
     */
    public function leadSalesTypeConfigurations(): HasMany
    {
        return $this->hasMany(
            LeadCampaignSalesTypeConfiguration::class,
            LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID,
            self::ID
        );
    }

    /**
     * @return HasMany
     */
    public function leadCampaignDeliveryMethods()
    {
        return $this->hasMany(
            LeadCampaignDeliveryMethod::class,
            LeadCampaignDeliveryMethod::FIELD_LEAD_CAMPAIGN_ID,
            self::ID
        );
    }

    /**
     * @return HasOne
     */
    public function productCampaign(): HasOne
    {
        return $this->hasOne(
            ProductCampaign::class,
            ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID,
            self::ID
        );
    }

    /**
     * @return HasMany
     */
    public function leadPrices(): HasMany
    {
        return $this->hasMany(LeadPrice::class, LeadPrice::LEAD_CAMPAIGN_ID, self::ID);
    }

    public function leadCampaignCrmIntegrations()
    {
        return $this->hasMany(
            LeadCampaignCrmIntegration::class,
            LeadCampaignCrmIntegration::LEAD_CAMPAIGN_ID,
            self::ID
        );
    }

    /**
     * @return HasMany
     */
    public function leadCampaignPauseReasons(): HasMany
    {
        return $this->hasMany(EloquentLeadCampaignPauseReasons::class, EloquentLeadCampaignPauseReasons::LEAD_CAMPAIGN_ID, self::ID);
    }

    /**
     * @return MorphMany
     */
    public function companyCampaignRelation(): MorphMany
    {
        return $this->morphMany(CompanyCampaignRelation::class, 'relation', CompanyCampaignRelation::FIELD_RELATION_TYPE, CompanyCampaignRelation::FIELD_RELATION_ID);
    }

    public function lowBids(): HasMany
    {
        return $this->hasMany(LowBid::class, LowBid::FIELD_CAMPAIGN_ID, self::ID);
    }

    /**
     * @param Builder $query
     * @param float $rejectionPercentageThreshold
     * @return Builder
     * @throws BindingResolutionException
     */
    public function scopeRemoveLegacyLeadCampaignCompaniesOverRejectionPercentage(Builder $query, float $rejectionPercentageThreshold): Builder
    {
        /** @var ProductRepository $productRepository */
        $productRepository = app()->make(ProductRepository::class);
        $productId         = $productRepository->getLeadProductId();

        $query
            ->leftJoin(
                DatabaseHelperService::database().'.'.ComputedRejectionStatistic::TABLE,
                DatabaseHelperService::database().'.'.ComputedRejectionStatistic::TABLE.'.'.ComputedRejectionStatistic::FIELD_LEGACY_COMPANY_ID,
                '=',
                DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::COMPANY_ID
            )
            ->where(function($where) use ($productId) {
                $where
                    ->where(DatabaseHelperService::database().'.'.ComputedRejectionStatistic::TABLE.'.'.ComputedRejectionStatistic::FIELD_PRODUCT_ID, $productId)
                    ->orWhereNull(DatabaseHelperService::database().'.'.ComputedRejectionStatistic::TABLE.'.'.ComputedRejectionStatistic::FIELD_PRODUCT_ID);
            })
            ->where(function($where) use ($rejectionPercentageThreshold) {
                $where
                    ->where(DatabaseHelperService::database().'.'.ComputedRejectionStatistic::TABLE.'.'.ComputedRejectionStatistic::ACTIVE_REJECTION_FIELD_IMPACTING_ELIGIBILITY, '<=', $rejectionPercentageThreshold)
                    ->orWhereNull(DatabaseHelperService::database().'.'.ComputedRejectionStatistic::TABLE.'.'.ComputedRejectionStatistic::ACTIVE_REJECTION_FIELD_IMPACTING_ELIGIBILITY);
            });

        return $query;
    }

    /**
     * @return Builder
     */
    public function getSoldQuoteCompaniesQuery(): Builder
    {
        return EloquentQuoteCompany::query()
            ->join(
                DatabaseHelperService::readOnlyDatabase() .'.'. EloquentQuote::TABLE,
                EloquentQuote::TABLE .'.'. EloquentQuote::QUOTE_ID,
                '=',
                EloquentQuoteCompany::TABLE .'.'. EloquentQuoteCompany::QUOTE_ID
            )->join(
                DatabaseHelperService::readOnlyDatabase() .'.'. LeadCampaignSalesTypeConfiguration::TABLE,
                LeadCampaignSalesTypeConfiguration::TABLE .'.'. LeadCampaignSalesTypeConfiguration::ID,
                '=',
                EloquentQuoteCompany::TABLE .'.'. EloquentQuoteCompany::LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION_ID
            )->where(LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID, $this->id)
            ->where(EloquentQuoteCompany::DELIVERED, true)
            ->where(EloquentQuoteCompany::CHARGEABLE, true)
            ->where(
                EloquentQuoteCompany::LEAD_COST_TYPE,
                '!=',
                EloquentQuoteCompany::VALUE_LEAD_COST_TYPE_REJECTED_APPOINTMENT_LEAD
            )->where(EloquentQuote::TABLE .'.'. EloquentQuote::STATUS, EloquentQuote::VALUE_STATUS_ALLOCATED)
            ->orderByDesc(EloquentQuoteCompany::TIMESTAMP_DELIVERED)
            ->select([
                EloquentQuoteCompany::QUOTE_COMPANY_ID,
                EloquentQuoteCompany::TIMESTAMP_DELIVERED,
                EloquentQuoteCompany::COST,
            ]);
    }
}
