<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * Class LeadPrice
 *
 * @property int $id
 * @property int $company_id
 * @property int $lead_category_id
 * @property int $lead_type_id
 * @property int $lead_sales_type_id
 * @property int $location_id
 * @property float $price
 * @property string $price_type
 * @property int $lead_campaign_id
 * @property string $lead_industry
 *
 * @property-read LeadSalesType $leadSalesType
 * @property-read LeadType $leadType
 * @property-read LeadCategory $leadCategory
 * @property-read Location $location
 */
class LeadPrice extends LegacyModel
{
    const TABLE = 'tbl_lead_prices';

    const ID                 = 'id';
    const COMPANY_ID         = 'company_id';
    const LEAD_CATEGORY_ID   = 'lead_category_id';
    const LEAD_TYPE_ID       = 'lead_type_id';
    const LEAD_SALES_TYPE_ID = 'lead_sales_type_id';
    const LOCATION_ID        = 'location_id';
    const PRICE              = 'price';
    const PRICE_TYPE         = 'price_type';
    const LEAD_CAMPAIGN_ID   = 'lead_campaign_id';
    const LEAD_INDUSTRY      = 'lead_industry';

    const LEAD_INDUSTRY_SOLAR   = 'solar';
    const LEAD_INDUSTRY_ROOFING = 'roofing';

    const LEAD_TYPE_STANDARD = 1;
    const LEAD_TYPE_PREMIUM  = 2;

    const SALES_TYPE_EXCLUSIVE  = 1;
    const SALES_TYPE_DUO        = 2;
    const SALES_TYPE_TRIO       = 3;
    const SALES_TYPE_QUAD       = 4;
    const SALES_TYPE_UNVERIFIED = 5;
    const SALES_TYPE_EMAIL_ONLY = 6;

    const DEFAULT_COMPANY = 1;

    const PRICE_TYPE_FLOOR      = 'floor';
    const PRICE_TYPE_BID        = 'bid';
    const PRICE_TYPE_DEFAULT    = self::PRICE_TYPE_FLOOR;

    const RELATION_LEAD_SALES_TYPE = 'leadSalesType';
    const RELATION_LEAD_CATEGORY = 'leadCategory';
    const RELATION_LEAD_TYPE = 'leadType';
    const RELATION_LOCATION = 'location';

    protected $table      = self::TABLE;
    protected $guarded    = [self::ID];
    public    $timestamps = false;

    /**
     * @return HasOne
     */
    public function leadSalesType(): HasOne
    {
        return $this->hasOne(LeadSalesType::class, LeadSalesType::ID, self::LEAD_SALES_TYPE_ID);
    }

    /**
     * @return HasOne
     */
    public function location(): HasOne
    {
        return $this->hasOne(Location::class, Location::ID, self::LOCATION_ID);
    }

    /**
     * @return HasOne
     */
    public function leadCategory(): HasOne
    {
        return $this->hasOne(LeadCategory::class, LeadCategory::ID, self::LEAD_CATEGORY_ID);
    }

    /**
     * @return HasOne
     */
    public function leadType(): HasOne
    {
        return $this->hasOne(LeadType::class, LeadType::ID, self::LEAD_TYPE_ID);
    }
}
