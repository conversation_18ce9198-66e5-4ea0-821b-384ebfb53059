<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * Class EloquentIntegrationField
 *
 * @package App
 *
 * @property int        $integrationid
 * @property string     $reltype
 * @property int        $relid
 * @property string     $name
 * @property string     $value
 */
class EloquentIntegrationField extends LegacyModel
{
    const ID = self::INTEGRATION_ID;
    const INTEGRATION_ID = 'integrationid';
    const REL_FUNC_NAME = 'integrationRel';
    const REL_TYPE = 'reltype';
    const REL_ID = 'relid';
    const NAME = 'name';
    const VALUE = 'value';

    const REL_TYPE_COMPANY_ID = 'companyid';
    const REL_TYPE_CRM_INTEGRATION_ID = 'crm_integration_id';

    protected $table = 'tblintegration';
    protected $primaryKey = self::ID;
    public $timestamps = false;

    protected $fillable = [
        self::NAME,
        self::VALUE,
    ];

    /**
     * @return MorphTo
     */
    public function integrationRel()
    {
        return $this->morphTo();
    }
}
