<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * This model reflects the Utility model in Solar-Estimate
 *
 * Class SEUtility
 *
 * @package App
 *
 * @property int $id
 * @property $created_at
 * @property $updated_at
 * @property int $eia_id
 * @property string $name
 * @property string $type
 * @property string $state
 * @property string $counties_served
 * @property float $elec_rate
 * @property string|null $uuid
 *
 * @property-read EloquentUtility $eloquentUtility
 */
class SEUtility extends Model
{
    const TABLE = 'se_utilities';

    const FIELD_CREATED_AT      = 'created_at';
    const FIELD_UPDATED_AT      = 'updated_at';
    const FIELD_ID              = 'id';
    const FIELD_EIA_ID          = 'eia_id';
    const FIELD_NAME            = 'name';
    const FIELD_TYPE            = 'type';
    const FIELD_STATE           = 'state';
    const FIELD_COUNTIES_SERVED = 'counties_served';
    const FIELD_ELEC_RATE       = 'elec_rate';
    const FIELD_UUID            = 'uuid';

    const RELATION_LEGACY_UTILITY = 'eloquentUtility';

    const FIELD_NAME_VALUE_OTHER = 'OTHER';

    protected $table      = self::TABLE;
    public    $timestamps = true;
    protected $fillable   = [];

    /**
     * @return bool
     */
    function isOther(): bool {
        return $this->name = self::FIELD_NAME_VALUE_OTHER;
    }

    /**
     * @return BelongsTo
     */
    public function eloquentUtility(): BelongsTo
    {
        return $this->belongsTo(EloquentUtility::class, self::FIELD_UUID, EloquentUtility::FIELD_UUID);
    }
}
