<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Class EloquentCampaign
 *
 * @package App
 *
 * @property int $campaignid
 * @property int $companyid
 * @property string $name
 * @property int $category
 * @property int $status
 * @property string $secretkey
 * @property int $timestampupdated
 */
class EloquentCampaign extends LegacyModel
{
    use HasFactory;

    const CAMPAIGN_ID = 'campaignid';
    const COMPANY_ID = 'companyid';
    const NAME = 'name';
    const CATEGORY = 'category';
    const STATUS = 'status';
    const SECRET_KEY = 'secretkey';
    const TIMESTAMP_UPDATED = 'timestampupdated';
    const TIMESTAMP_ADDED = 'timestampadded';

    const TABLE = 'tblcampaign';

    protected $table = self::TABLE;

    public $timestamps = false;

    protected $primaryKey = self::CAMPAIGN_ID;
}
