<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Model;

/**
 * Class EloquentHistoryLog
 * @package App
 *
 * @property int $historyid
 * @property string $sessionkey
 * @property int $userid
 * @property string $timestampadded
 * @property string $ipaddress
 * @property string $activity
 * @property string $logdata
 */
class EloquentHistoryLog extends LegacyModel
{
    const ID = self::HISTORY_ID;
    const HISTORY_ID = 'historyid';
    const SESSION_KEY = 'sessionkey';
    const USER_ID = 'userid';
    const TIMESTAMP_ADDED = 'timestampadded';
    const IP_ADDRESS = 'ipaddress';
    const ACTIVITY = 'activity';
    const LOG_DATA = 'logdata';

    const ACTIVITY_LOGIN = "login";
    const ACTIVITY_CONTRACT_APPROVAL = 'contract approval';
    const ACTIVITY_LEAD_CONTRACT_APPROVAL = 'leads contract approval';

    const RELATION_USER = 'user';

    const TABLE = 'tblhistorylog';

    protected $table = self::TABLE;

    protected $primaryKey = self::ID;

    public $timestamps = false;

    /**
     * Gets the user that this history log belongs too
     * @return BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(EloquentUser::class, self::USER_ID, EloquentUser::ID);
    }
}
