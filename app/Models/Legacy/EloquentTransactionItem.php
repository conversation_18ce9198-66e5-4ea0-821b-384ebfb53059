<?php

namespace App\Models\Legacy;

use App\Enums\EloquentTransactionType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EloquentTransactionItem extends LegacyModel
{
    use HasFactory;

    protected $table = self::TABLE;

    const string TABLE = 'tbltransactionitem';

    const string ID                  = self::TRANSACTION_ITEM_ID;
    const string TRANSACTION_ITEM_ID = 'transactionitemid';
    const string TRANSACTION_ID      = 'transactionid';
    const string KEY                 = 'key';
    const string VALUE               = 'value';
    const string TIMESTAMP_ADDED     = 'timestampadded';

    public function scopeInvoiceCharge(Builder $query, string $invoiceId): Builder
    {
        return $query
            ->where(EloquentTransactionItem::KEY, 'Request ID')
            ->where(EloquentTransactionItem::VALUE, 'LIKE', 'ch_%')
            ->whereHas('transaction', function ($query) use ($invoiceId) {
                $query->where(EloquentTransaction::REL_TYPE, EloquentTransactionType::INVOICE->value)
                    ->where(EloquentTransaction::REL_ID, $invoiceId);
            });
    }

    /**
     * @return BelongsTo
     */
    public function transaction(): BelongsTo
    {
        return $this->belongsTo(EloquentTransaction::class, self::TRANSACTION_ID, EloquentTransaction::ID);
    }
}
