<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class EloquentTransaction extends LegacyModel
{
    use HasFactory;

    protected $table = self::TABLE;

    const string TABLE = 'tbltransaction';

    const string ID                = self::TRANSACTION_ID;
    const string TRANSACTION_ID    = 'transactionid';
    const string REL_ID            = 'relid';
    const string REL_TYPE          = 'reltype';
    const string TYPE              = 'type';
    const string STATUS            = 'status';
    const string AMOUNT            = 'amount';
    const string TIMESTAMP_UPDATED = 'timestampupdated';

    /**
     * @return HasMany
     */
    public function items(): HasMany
    {
        return $this->hasMany(EloquentTransactionItem::class, EloquentTransactionItem::TRANSACTION_ID, self::ID);
    }
}
