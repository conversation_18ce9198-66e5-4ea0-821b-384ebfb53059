<?php

namespace App\Models\Legacy;

/**
 * Class EloquentSubscribe
 *
 * @property int $subscribeid
 * @property string $email
 * @property string $type
 * @property string $note
 * @property string $period
 * @property bool $unsubscribed
 * @property int $timestampadded
 * @property int $timestampunsubscribed
 * @property int $timestamplastsent
 */
class EloquentSubscribe extends LegacyModel
{
    const ID = self::SUBSCRIBE_ID;
    const SUBSCRIBE_ID = 'subscribeid';
    const EMAIL = 'email';
    const TYPE = 'type';
    const NOTE = 'note';
    const PERIOD = 'period';
    const UNSUBSCRIBED = 'unsubscribed';
    const TIMESTAMP_ADDED = 'timestampadded';
    const TIMESTAMP_UNSUBSCRIBED = 'timestampunsubscribed';
    const TIMESTAMP_LAST_SENT = 'timestamplastsent';

    const STATUS_UNSUBSCRIBED = 1;

    const TABLE = 'tblsubscribe';

    const TYPE_COMPANY_MARKETING = 'companymarketing';

    protected $table = self::TABLE;

    protected $primaryKey = self::ID;

    public $timestamps = false;

    protected $fillable = [
        self::EMAIL,
        self::TYPE,
        self::NOTE,
        self::PERIOD,
        self::UNSUBSCRIBED
    ];

    protected $casts = [
        self::UNSUBSCRIBED => 'boolean',
    ];
}
