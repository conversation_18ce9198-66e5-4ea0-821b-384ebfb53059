<?php

namespace App\Models\Legacy;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

/**
 * Class CompanyNabcepCertification
 *
 *
 * @property int $id
 * @property int $company_id
 * @property int $option_id
 * @property string $first_name
 * @property string $last_name
 * @property string $certification_type
 * @property string $certification_number
 * @property Carbon $expired_at
 * @property string $city
 * @property string $state
 *
 * @property-read bool $expired
 */
class EloquentCompanyNabcepCertification extends LegacyModel
{
    const ID                   = 'id';
    const COMPANY_ID           = 'company_id';
    const OPTION_ID            = 'option_id';
    const FIRST_NAME           = 'first_name';
    const LAST_NAME            = 'last_name';
    const CERTIFICATION_TYPE   = 'certification_type';
    const CERTIFICATION_NUMBER = 'certification_number';
    const EXPIRED_AT           = 'expired_at';
    const CITY                 = 'city';
    const STATE                = 'state';

    protected $table = 'company_nabcep_certifications';

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = [
        self::ID,
        self::CREATED_AT,
        self::UPDATED_AT,
    ];

    /**
     * Casts
     *
     * @var array
     */
    protected $casts = [
        self::EXPIRED_AT => 'datetime'
    ];

    /**
     * Scope a query to only active(unexpired) certificates
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeActive($query)
    {
        return $query->where(self::EXPIRED_AT, '>', Carbon::now());
    }
}
