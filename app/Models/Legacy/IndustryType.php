<?php

namespace App\Models\Legacy;

use DateTime;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class IndustryType
 * @package App
 *
 * @property string $display_name
 * @property string $key
 * @property DateTime $created_at
 * @property DateTime $updated_at
 *
 * @property-read Industry $industry
 */
class IndustryType extends LegacyModel
{
    const TABLE = 'industry_types';

    const FIELD_ID                   = 'id';
    const FIELD_KEY                  = 'key';

    const RELATION_INDUSTRY                        = 'industry';
    const RELATION_RANKING_CATEGORY_INDUSTRY_TYPES = 'rankingCategoryIndustryTypes';

    /**
     * @return BelongsTo
     */
    function industry(): BelongsTo
    {
        return $this->belongsTo(Industry::class);
    }
}
