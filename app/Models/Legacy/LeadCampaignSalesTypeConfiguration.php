<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Casts\Attribute;

/**
 * Class LeadCampaignSalesTypeConfiguration
 *
 * @property int $id
 * @property-read integer         $max_daily_spend
 * @property-read integer         $max_daily_lead
 * @property-read LeadCampaign $leadCampaign
 * @property-read LeadSalesType $leadSalesType
 */
class LeadCampaignSalesTypeConfiguration extends LegacyModel
{
    const TABLE = 'tbl_lead_campaign_sales_type_configuration';

    const ID                       = 'id';
    const LEAD_CAMPAIGN_ID         = 'lead_campaign_id';
    const LEAD_SALES_TYPE_ID       = 'lead_sales_type_id';
    const LAST_MODIFIED_LEAD_LIMIT = 'last_modified_lead_limit';
    const MAX_DAILY_SPEND          = 'max_daily_spend';
    const MAX_DAILY_LEAD           = 'max_daily_lead';
    const STATUS                   = 'status';

    const STATUS_ENABLE = 1;
    const STATUS_DISABLE = 0;

    const RELATION_LEAD_CAMPAIGN = 'leadCampaign';
    const RELATION_LEAD_SALES_TYPE = 'leadSalesType';
    const RELATION_ELOQUENT_QUOTE_COMPANIES = 'eloquentQuoteCompanies';

    const DISPLAY_LEAD_SALES_TYPE = 'display_lead_sales_type';
    const DISPLAY_BUDGET          = 'display_budget';

    protected $table   = self::TABLE;
    protected $guarded = [self::ID];
    protected $casts   = [self::LAST_MODIFIED_LEAD_LIMIT => 'datetime'];
    protected $appends = [self::DISPLAY_LEAD_SALES_TYPE, self::DISPLAY_BUDGET];

    /**
     * @return BelongsTo
     */
    public function leadCampaign(): BelongsTo
    {
        return $this->belongsTo(LeadCampaign::class, self::LEAD_CAMPAIGN_ID, LeadCampaign::ID)->withTrashed();
    }

    /**
     * @return BelongsTo
     */
    public function leadSalesType()
    {
        return $this->belongsTo(LeadSalesType::class, self::LEAD_SALES_TYPE_ID, LeadSalesType::ID);
    }

    /**
     * @return HasMany
     */
    public function eloquentQuoteCompanies(): HasMany
    {
        return $this->hasMany(EloquentQuoteCompany::class, EloquentQuoteCompany::LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION_ID, self::ID);
    }

    /**
     * @return Attribute
     */
    public function leadSalesTypeDisplay(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->leadSalesType?->name ?? ''
        );
    }

    /**
     * @return string
     */
    public function formatBudget(): string
    {
        if ($this->max_daily_spend) {
            return "$" . number_format($this->max_daily_spend, 2);
        } elseif($this->max_daily_lead) {
            return $this->max_daily_lead . ' ' . Str::plural('Lead', $this->max_daily_lead);
        } else {
            return LeadCampaign::DISPLAY_BUDGET_UNIT_UNLIMITED;
        }
    }

    /**
     * @return string
     */
    public function getDisplayBudgetUnit(): string
    {
        if ($this->max_daily_spend) {
            return LeadCampaign::DISPLAY_BUDGET_UNIT_CURRENCY;
        } elseif($this->max_daily_lead) {
            return LeadCampaign::DISPLAY_BUDGET_UNIT_LEAD;
        } else {
            return LeadCampaign::DISPLAY_BUDGET_UNIT_UNLIMITED;
        }
    }

    /**
     * @return float|int
     */
    public function getBudget(): float|int
    {
        if ($this->max_daily_spend) {
            return $this->max_daily_spend;
        } elseif($this->max_daily_lead) {
            return $this->max_daily_lead;
        } else {
            return 0;
        }
    }
}
