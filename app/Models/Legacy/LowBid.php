<?php

namespace App\Models\Legacy;

/**
 * @property int $campaign_id
 * @property int $bid_id
 * @property float $standard_floor
 * @property float $effective_floor
 */
class LowBid extends LegacyModel
{
    const TABLE = 'low_bids';

    const FIELD_ID              = 'id';
    const FIELD_CAMPAIGN_ID     = 'campaign_id';
    const FIELD_BID_ID          = 'bid_id';
    const FIELD_STANDARD_FLOOR  = 'standard_floor';
    const FIELD_EFFECTIVE_FLOOR = 'effective_floor';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

}
