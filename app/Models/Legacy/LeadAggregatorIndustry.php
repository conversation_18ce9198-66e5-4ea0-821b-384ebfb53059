<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\SoftDeletes;

class LeadAggregatorIndustry extends LegacyModel
{
    use SoftDeletes;

    const TABLE = 'lead_aggregator_industries';

    const FIELD_ID = 'id';
    const FIELD_LEAD_AGGREGATOR_ID = 'lead_aggregator_id';
    const FIELD_INDUSTRY_ID = 'industry_id';
    const FIELD_CREATED_AT = 'created_at';
    const FIELD_UPDATED_AT = 'updated_at';
    const FIELD_DELETED_AT = 'deleted_at';

    const RELATION_LEAD_AGGREGATOR = 'leadAggregator';
    const RELATION_INDUSTRY = 'industry';

    protected $fillable = [
        self::FIELD_ID
    ];

    protected $table = self::TABLE;

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function leadAggregator() {
        return $this->belongsTo(LeadAggregator::class, self::FIELD_LEAD_AGGREGATOR_ID);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function industry() {
        return $this->hasOne(Industry::class, Industry::FIELD_ID, self::FIELD_INDUSTRY_ID);
    }
}
