<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * Class EloquentReview
 *
 * @property int $reviewid
 * @property int $timestampadded
 * @property EloquentCompany $company
 *
 * @property-read LeadCategory leadCategory
 * @property-read LeadProcessingPendingReview|null $pendingReview
 */
class EloquentReview extends LegacyModel
{
    const FIELD_REVIEW_ID = 'reviewid';
    const FIELD_REVIEW_TYPE = 'reviewtype';
    const FIELD_REVIEW_LINK = 'reviewlink';
    const FIELD_ADDRESS_ID = 'addressid';
    const FIELD_USER_ID = 'userid';
    const FIELD_USER_EMAIL = 'useremail';
    const FIELD_USER_NAME = 'username';
    const FIELD_TIMESTAMP_ADDED = 'timestampadded';
    const FIELD_APPROVED = 'approved';
    const FIELD_IP_ADDRESS = 'ipaddress';
    const FIELD_TITLE = 'title';
    const FIELD_COMMENTS = 'comments';
    const FIELD_OVERALL_SCORE = 'overallscore';
    const FIELD_REVIEW_VALIDATED = 'reviewvalidated';
    const FIELD_SYSTEM_TYPE = 'systemtype';

    const REVIEW_APPROVED = 1;

    const RELATION_USER = 'user';
    const RELATION_ADDRESS = 'address';
    const RELATION_ANSWERS =  'answers';
    const RELATION_REVIEW_EXTRA_DATA = 'extraData';
    const RELATION_REVIEW_ATTACHMENTS = 'attachments';
    const RELATION_COMMENTS = 'reviewComments';

    const TABLE = 'tblreview';

    protected $table      = self::TABLE;
    protected $primaryKey = self::FIELD_REVIEW_ID;
    public    $timestamps = false;

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(EloquentCompany::class, self::FIELD_REVIEW_LINK, EloquentCompany::COMPANY_ID);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(EloquentUser::class, self::FIELD_USER_ID, EloquentUser::ID);
    }

    /**
     * @return BelongsTo
     */
    public function address(): BelongsTo
    {
        return $this->belongsTo(EloquentAddress::class, self::FIELD_ADDRESS_ID, EloquentAddress::ID);
    }

    /**
     * @return HasMany
     */
    public function answers(): HasMany
    {
        return $this->hasMany(EloquentReviewAnswer::class, EloquentReviewAnswer::FIELD_REVIEW_ID, self::FIELD_REVIEW_ID);
    }

    /**
     * @return HasOne
     */
    public function extraData(): HasOne
    {
        return $this->hasOne(EloquentReviewExtraData::class, EloquentReviewExtraData::FIELD_REVIEW_ID, self::FIELD_REVIEW_ID);
    }

    /**
     * @return HasMany
     */
    public function attachments(): HasMany
    {
        return $this->hasMany(EloquentReviewAttachment::class, EloquentReviewAttachment::ATTRIBUTE_REVIEW_ID, self::FIELD_REVIEW_ID);
    }

    /**
     * @return HasMany
     */
    public function reviewComments(): HasMany
    {
        return $this->hasMany(EloquentComment::class, EloquentComment::REL_ID, self::FIELD_REVIEW_ID)
            ->where(EloquentComment::REL_TYPE, 'review');
    }
}
