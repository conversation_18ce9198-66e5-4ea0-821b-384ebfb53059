<?php

namespace App\Models\Legacy;

/**
 * Class EloquentConfiguration
 *
 */
class EloquentConfiguration extends LegacyModel
{
    const TABLE = 'tblconfiguration';

    const ID = self::CONFIGURATION_ID;
    const CONFIGURATION_ID = 'configurationid';
    const NAME = 'name';
    const VALUE = 'value';
    const REL_ID = 'relid';
    const REL_TYPE = 'reltype';

    const REL_TYPE_COMPANY_ID = 'companyid';

    const VALUE_ACTIVE = 1;

    const NAME_SUPER_PREMIUM_BUDGET_DIVISION_AMOUNT = 'budget_division_amount';
    const NAME_SUPER_PREMIUM_LEADS_PER_DIVISION = 'leads_per_division';
    const NAME_SUPER_PREMIUM_ELECTRIC_COST_MIN = 'electric_cost_min';
    const NAME_PHONE_NUMBER_ACTIVE = 'phone_number_active';
    const NAME_SR_PHONE_NUMBER_ACTIVE = 'sr_phone_number_active';
    const NAME_CONTRACT_APPROVED = 'contract_approved';
    const NAME_WEBSITE_ACTIVE = 'website_active';
    const NAME_IS_PREPAID = 'is_prepaid';

    protected $table      = self::TABLE;
    protected $primaryKey = self::ID;
    public    $timestamps = false;
}
