<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Model;

/**
 * Class EloquentCompanyReviewEmail
 * @package App
 *
 * @property int $reviewemailid
 * @property int $companyid
 * @property int $timestampadded
 * @property string $firstname
 * @property string $lastname
 * @property string $email
 * @property int $status
 * @property int $unsubscribed
 * @property int $timestampunsubscribed
 * @property int $reviewid
 * @property int $archived
 */
class EloquentCompanyReviewEmail extends LegacyModel
{
    const REVIEW_EMAIL_ID        = 'reviewemailid';
    const ID                     = self::REVIEW_EMAIL_ID;
    const COMPANY_ID             = 'companyid';
    const TIMESTAMP_ADDED        = 'timestampadded';
    const FIRST_NAME             = 'firstname';
    const LAST_NAME              = 'lastname';
    const EMAIL                  = 'email';
    const STATUS                 = 'status';
    const UNSUBSCRIBED           = 'unsubscribed';
    const TIMESTAMP_UNSUBSCRIBED = 'timestampunsubscribed';
    const REVIEW_ID              = 'reviewid';
    const ARCHIVED               = 'archived';

    const TABLE                             = 'tblcompanyreviewemail';
    const RELATION_REVIEW                   = 'tblreview';
    const RELATION_COMPANY_REVIEW_EMAIL_LOG = 'tblcompanyreviewemaillog';

    const VALUE_UNSUBSCRIBED    = 0;
    const VALUE_SUBSCRIBED      = 1;
    const VALUE_STATUS_ACTIVE   = 1;
    const VALUE_STATUS_INACTIVE = 0;
    const VALUE_ARCHIVED        = 1;
    const VALUE_UNARCHIVED      = 0;

    protected $primaryKey = self::ID;
    protected $table      = self::TABLE;
    protected $guarded    = [self::ID];
    public    $timestamps = false;

    protected $fillable = [
        self::REVIEW_EMAIL_ID,
        self::STATUS,
    ];
}