<?php

namespace App\Models\Legacy;

/**
 * Class EloquentReviewAnswer
 * @package App
 *
 * @property int $answerid
 * @property int $reviewid
 * @property int $questionid
 * @property string $answer
 * @property string $timestampupdated
 *
 * @property-read EloquentReview $review
 */
class EloquentReviewAnswer extends LegacyModel
{
    const FIELD_ID = self::FIELD_ANSWER_ID;
    const FIELD_ANSWER_ID = 'answerid';
    const FIELD_REVIEW_ID = 'reviewid';
    const FIELD_QUESTION_ID = 'questionid';
    const FIELD_ANSWER = 'answer';
    const FIELD_TIMESTAMP_UPDATED = 'timestampupdated';

    const RELATION_REVIEW = 'review';
    const RELATION_QUESTION = 'question';

    const REVIEW_QUESTION_ZIP_CODE = 14;

    const TABLE = 'tblreviewanswer';

    public $timestamps = false;
    protected $table = self::TABLE;
    protected $primaryKey = self::FIELD_ID;

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function review()
    {
        return $this->belongsTo(EloquentReview::class, self::FIELD_REVIEW_ID, EloquentReview::FIELD_REVIEW_ID);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function question()
    {
        return $this->belongsTo(EloquentReviewQuestion::class, self::FIELD_QUESTION_ID, EloquentReviewQuestion::FIELD_QUESTION_ID);
    }
}
