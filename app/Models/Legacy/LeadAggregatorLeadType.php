<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\SoftDeletes;

class LeadAggregatorLeadType extends LegacyModel
{
    use SoftDeletes;

    const TABLE = 'lead_aggregator_lead_types';

    const FIELD_ID = 'id';
    const FIELD_LEAD_AGGREGATOR_ID = 'lead_aggregator_id';
    const FIELD_LEAD_TYPE_ID = 'lead_type_id';
    const FIELD_CREATED_AT = 'created_at';
    const FIELD_UPDATED_AT = 'updated_at';
    const FIELD_DELETED_AT = 'deleted_at';

    const RELATION_LEAD_AGGREGATOR = 'leadAggregator';
    const RELATION_LEAD_TYPE = 'leadType';

    protected $fillable = [
        self::FIELD_ID
    ];

    protected $table = self::TABLE;

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function leadAggregator() {
        return $this->belongsTo(LeadAggregator::class, self::FIELD_LEAD_AGGREGATOR_ID);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function leadType() {
        return $this->hasOne(LeadType::class, LeadType::ID, self::FIELD_LEAD_TYPE_ID);
    }
}
