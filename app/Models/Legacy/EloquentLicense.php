<?php

namespace App\Models\Legacy;


/**
 * Class EloquentLicense
 *
 * @property int $id
 * @property int $companyid
 * @property string $license
 *
 */

class EloquentLicense extends LegacyModel
{
    const ID                     = 'id';
    const COMPANY_ID             = 'companyid';
    const LICENSE                = 'license';
    const STATE                  = 'state';
    const ISSUE_DATE             = 'issue_date';
    const EXPIRATION_DATE        = 'expiration_date';
    const ENTITY_NAME            = 'entity_name';
    const TYPE                   = 'type';
    const EPC                    = 'epc';

    const TABLE = 'company_licenses';

    public $timestamps = false;

    protected $table = self::TABLE;

    protected $fillable = [
        self::COMPANY_ID,
        self::LICENSE,
        self::STATE,
        self::ISSUE_DATE,
        self::EXPIRATION_DATE,
        self::ENTITY_NAME,
        self::TYPE,
        self::EPC
    ];
}
