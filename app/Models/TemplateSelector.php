<?php

namespace App\Models;

use App\Enums\TemplateManagement\TemplatePurposeKey;
use App\Enums\TemplateManagement\TemplateRelation;
use App\Models\Odin\Industry;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property int $id
 * @property TemplateRelation $relation
 * @property TemplatePurposeKey $purpose_key
 * @property string $template_type
 * @property int $template_id
 * @property int|null $industry_id
 *
 * @property-read mixed $template
 * @property-read Industry|null $industry
 */
class TemplateSelector extends Model
{
    const string TABLE = 'template_selectors';

    const string FIELD_ID = 'id';
    const string FIELD_RELATION = 'relation';
    const string FIELD_PURPOSE_KEY = 'purpose_key';
    const string FIELD_TEMPLATE_TYPE = 'template_type';
    const string FIELD_TEMPLATE_ID = 'template_id';
    const string FIELD_INDUSTRY_ID = 'industry_id';

    const string RELATION_TEMPLATE = 'template';
    const string RELATION_INDUSTRY = 'industry';

    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];
    protected $casts = [
        self::FIELD_RELATION => TemplateRelation::class,
        self::FIELD_PURPOSE_KEY => TemplatePurposeKey::class
    ];

    /**
     * @return MorphTo
     */
    public function template(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * @return BelongsTo
     */
    public function industry(): BelongsTo
    {
        return $this->belongsTo(Industry::class);
    }
}
