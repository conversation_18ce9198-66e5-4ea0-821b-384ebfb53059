<?php

namespace App\Models;

use App\Enums\LeadRefundItemStatus;
use App\Enums\LeadRefundStatus;
use App\Models\Odin\Company;
use Database\Factories\Odin\LeadRefundFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Collection;

class LeadRefund extends BaseModel
{
    use HasFactory;
    const string TABLE            = 'lead_refunds';
    const string FIELD_ID         = 'id';
    const string FIELD_TOTAL      = 'total';
    const string FIELD_STATUS     = 'status';
    const string FIELD_COMPANY_ID = 'company_id';
    const string FIELD_CREATED_AT   = 'created_at';
    const string FIELD_UPDATED_AT   = 'updated_at';
    const string FIELD_REQUESTED_BY = 'requested_by';
    const string FIELD_REVIEWED_BY  = 'reviewed_by';
    const string FIELD_REVIEWED_AT  = 'reviewed_at';

    const string RELATION_ITEMS     = 'items';
    const string RELATION_APPROVALS = 'approvals';

    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_STATUS => LeadRefundStatus::class
    ];

    protected static function newFactory(): LeadRefundFactory
    {
        return LeadRefundFactory::new();
    }

    /**
     * @return HasMany
     */
    public function items(): HasMany
    {
        return $this->hasMany(LeadRefundItem::class, LeadRefundItem::FIELD_LEAD_REFUND_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function approvals(): HasMany
    {
        return $this->hasMany(LeadRefundApproval::class, LeadRefundApproval::FIELD_LEAD_REFUND_ID, self::FIELD_ID);
    }


    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }

    /**
     * @return Collection<LeadRefundItem>
     */
    public function getRefundableItems(): Collection
    {
        return $this
            ->items()
            ->where(LeadRefundItem::FIELD_STATUS, LeadRefundItemStatus::APPROVED->value)
            ->get();
    }

    /**
     * @return BelongsTo
     */
    public function requestedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_REQUESTED_BY, User::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function reviewedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_REVIEWED_BY, User::FIELD_ID);
    }

    /**
     * @return MorphMany
     */
    public function comments(): MorphMany
    {
        return $this->morphMany(Note::class, Note::RELATION_NOTABLE, Note::FIELD_RELATION_TYPE, Note::FIELD_RELATION_ID);
    }

    public function canRefund(): bool
    {
        return in_array($this->status, [
            LeadRefundStatus::APPROVED,
            LeadRefundStatus::APPROVED_WITH_REJECTIONS,
        ]);
    }
}
