<?php

namespace App\Models;

use Database\Factories\Odin\TopCompanyByCountyFactory;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $industry_service_id
 * @property int $county_location_id
 * @property int $company_id
 * @property string $company_name
 * @property string $company_reference
 * @property int $company_review_count
 * @property float $company_bayesian_all_time
 */
class TopCompanyByCounty extends Model
{
    use HasFactory;

    const string FIELD_ID                        = 'id';
    const string FIELD_INDUSTRY_SERVICE_ID       = 'industry_service_id';
    const string FIELD_COUNTY_LOCATION_ID        = 'county_location_id';
    const string FIELD_COMPANY_ID                = 'company_id';
    const string FIELD_COMPANY_NAME              = 'company_name';
    const string FIELD_COMPANY_REFERENCE         = 'company_reference';
    const string FIELD_COMPANY_REVIEW_COUNT      = 'company_review_count';
    const string FIELD_COMPANY_BAYESIAN_ALL_TIME = 'company_bayesian_all_time';

    const string TABLE = 'top_companies_by_counties';

    protected $guarded = [self::FIELD_ID];
    protected $table   = self::TABLE;

    protected static function newFactory(): TopCompanyByCountyFactory
    {
        return TopCompanyByCountyFactory::new();
    }
}
