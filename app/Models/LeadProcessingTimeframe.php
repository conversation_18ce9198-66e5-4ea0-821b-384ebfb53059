<?php

namespace App\Models;

/**
 * @property int $id
 * @property string $key
 * @property string $name
 * @property int $priority
 */
class LeadProcessingTimeframe extends BaseModel
{
    const TABLE = 'lead_processing_timeframes';

    const FIELD_ID = 'id';
    const FIELD_KEY = 'key';
    const FIELD_NAME = 'name';
    const FIELD_PRIORITY = 'priority';

    const KEY_LESS_THAN_TWENTY_FOUR_HRS = 'less_than_twenty_four_hrs';
    const KEY_TWENTY_FOUR_TO_FORTY_EIGHT_HRS = 'twenty_four_to_forty_eight_hrs';
    const KEY_TWO_TO_SEVEN_DAYS = 'two_to_seven_days';
    const KEY_SEVEN_TO_THIRTY_DAYS = 'seven_to_thirty_days';
    const KEY_THIRTY_TO_NINETY_DAYS = 'thirty_to_ninety_days';

    protected $table = self::TABLE;
}
