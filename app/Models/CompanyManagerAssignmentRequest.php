<?php

namespace App\Models;

use App\Models\Odin\Company;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Permission\Models\Role;

/**
 * @property int $id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property int $user_id
 * @property int $company_id
 * @property int $role_id
 * @property int $deciding_user_id
 * @property Carbon $decided_at
 *
 * @property string $status
 * @property boolean $is_unread
 *
 * @property-read User $user
 * @property-read Company $company
 * @property-read Role $role
 * @property-read User $deciding_user
 */
class CompanyManagerAssignmentRequest extends Model
{
    use HasFactory;

    const string TABLE = 'company_manager_assignment_requests';

    protected $table = self::TABLE;

    const string FIELD_ID         = 'id';
    const string FIELD_COMPANY_ID = 'company_id';
    const string FIELD_USER_ID    = 'user_id';
    const string FIELD_ROLE_ID    = 'role_id';
    const string FIELD_STATUS     = 'status';
    const string FIELD_IS_UNREAD  = 'is_unread';

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        'decided_at' => 'datetime'
    ];

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }

    public function deciding_user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'deciding_user_id');
    }
}
