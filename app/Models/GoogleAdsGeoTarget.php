<?php

namespace App\Models;

use App\Models\Legacy\Location;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class GoogleAdsGeoTarget extends Model
{
    const TABLE = 'google_ads_geo_targets';

    const FIELD_ID = 'id';
    const FIELD_LOCATION_ID = 'location_id';
    const FIELD_CRITERIA_ID = 'criteria_id';
    const FIELD_PARENT_ID = 'parent_id';
    const FIELD_NAME = 'name';
    const FIELD_CANONICAL_NAME = 'canonical_name';
    const FIELD_TARGET_TYPE = 'target_type';
    const FIELD_VERSION_DATE = 'version_date';

    const TARGET_TYPE_COUNTY = 'county';
    const TARGET_TYPE_STATE = 'state';
    const TARGET_TYPE_POSTAL_CODE = 'postal code';
    const TARGET_TYPE_COUNTRY = 'country';
    const TARGET_TYPE_MUNICIPALITY = 'municipality';
    const TARGET_TYPE_CITY = 'city';
    const TARGET_EXCEPTION = 'exception';

    const RELATION_LOCATION = 'location';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];

    /**
     * @return BelongsTo
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class, self::FIELD_LOCATION_ID, Location::ID);
    }
}
