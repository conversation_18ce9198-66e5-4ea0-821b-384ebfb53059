<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class UpsellAutomationLog
 *
 * @package App\Models
 *
 * @property integer $id
 * @property string $name
 * @property string $type
 * @property Carbon $started_at
 * @property Carbon $ended_at
 * @property int $leads_upsold
 * @property float $additional_revenue
 * @property array $data
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class UpsellAutomationLog extends Model
{
    const string TABLE = 'upsell_automation_logs';

    const string FIELD_ID                  = 'id';
    const string FIELD_NAME                = 'name';
    const string FIELD_TYPE                = 'type';
    const string FIELD_STARTED_AT          = 'started_at';
    const string FIELD_ENDED_AT            = 'ended_at';
    const string FIELD_LEADS_UPSOLD        = 'leads_upsold';
    const string FIELD_ADDITIONAL_REVENUE  = 'additional_revenue';
    const string FIELD_DATA                = 'data';

    const string FIELD_CREATED_AT          = 'created_at';
    const string FIELD_UPDATED_AT          = 'updated_at';

    const string TYPE_LIVE  = 'live';
    const string TYPE_TEST  = 'test';

    const string DATA_CONFIG                = 'config';
    const string DATA_TIME_FRAME_START      = 'time_frame_start';
    const string DATA_TIME_FRAME_END        = 'time_frame_end';
    const string DATA_LEADS_IN_WINDOW       = 'leads_in_window';
    const string DATA_COMPANY_DELIVERIES    = 'company_deliveries';
    const string DATA_AUTOMATION_SEC        = 'automation_sec';
    const string DATA_TOTAL_WORKER_JOBS     = 'total_worker_jobs';
    const string DATA_FAILED_WORKER_JOBS    = 'failed_worker_jobs';

    const string DEFAULT_AUTOMATION_NAME    = 'Automation Log';

    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_STARTED_AT  => 'datetime',
        self::FIELD_ENDED_AT    => 'datetime',
        self::FIELD_DATA        => 'array',
    ];
}
