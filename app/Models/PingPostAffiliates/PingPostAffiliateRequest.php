<?php

namespace App\Models\PingPostAffiliates;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property int $ping_post_affiliate_id
 * @property int $ping_post_affiliate_api_key_id
 * @property string $request
 * @property string $response
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class PingPostAffiliateRequest extends Model
{
    const string TABLE = 'ping_post_affiliate_requests';

    const string FIELD_ID = 'id';
    const string FIELD_PING_POST_AFFILIATE_ID = 'ping_post_affiliate_id';
    const string FIELD_PING_POST_AFFILIATE_API_KEY_ID = 'ping_post_affiliate_api_key_id';
    const string FIELD_SOURCE_IP = 'source_ip';
    const string FIELD_HEADERS = 'headers';
    const string FIELD_REQUEST = 'request';
    const string FIELD_PROCESSED = 'processed';
    const string FIELD_RESPONSE = 'response';
    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID,
    ];

    protected $casts = [
        self::FIELD_REQUEST => 'array',
        self::FIELD_RESPONSE => 'array',
        self::FIELD_HEADERS => 'array',
    ];
}
