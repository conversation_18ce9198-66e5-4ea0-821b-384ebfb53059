<?php

namespace App\Models\PingPostAffiliates;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property int $ping_post_affiliate_id
 * @property string $key
 * @property string $type
 * @property string $status
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class PingPostAffiliateApiKey extends Model
{
    use SoftDeletes;

    const string TABLE = 'ping_post_affiliate_api_keys';

    const string FIELD_ID = 'id';
    const string FIELD_PING_POST_AFFILIATE_ID = 'ping_post_affiliate_id';
    const string FIELD_KEY = 'key';
    const string FIELD_TYPE = 'type';
    const string FIELD_STATUS = 'status';
    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';
    const string FIELD_DELETED_AT = 'deleted_at';

    const string STATUS_ACTIVE = 'active';
    const string STATUS_INACTIVE = 'inactive';

    const string TYPE_POST = 'post';
    const string TYPE_PING_POST = 'ping_post';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID,
    ];
}
