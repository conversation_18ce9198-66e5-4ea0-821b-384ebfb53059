<?php

namespace App\Models\PingPostAffiliates;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property int $consumer_product_id
 * @property int $ping_post_affiliate_id
 * @property int $ping_post_affiliate_request_id
 * @property float $cost
 * @property array $data
 * @property array $manual_fields
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class PingPostAffiliateLead extends Model
{
    const string TABLE = 'ping_post_affiliate_leads';

    const string FIELD_ID = 'id';
    const string FIELD_CONSUMER_PRODUCT_ID = 'consumer_product_id';
    const string FIELD_PING_POST_AFFILIATE_ID = 'ping_post_affiliate_id';
    const string FIELD_PING_POST_AFFILIATE_REQUEST_ID = 'ping_post_affiliate_request_id';
    const string FIELD_COST = 'cost';
    const string FIELD_URL_ORIGIN = 'url_origin';
    const string FIELD_TRUSTED_FORM_URL = 'trusted_form_url';
    const string FIELD_UNIVERSAL_LEAD_ID = 'universal_lead_id';
    const string FIELD_LEAD_CREATION_DATE = 'lead_creation_date';
    const string FIELD_CAMPAIGN_NAME = 'campaign_name';
    const string FIELD_DATA = 'data';
    const string FIELD_MANUAL_FIELDS = 'manual_fields';
    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';

    const string MANUAL_PUBLISHER   = 'publisher';
    const string MANUAL_QUALITY     = 'quality';
    const string MANUAL_NOTES       = 'notes';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID,
    ];

    protected $casts = [
        self::FIELD_DATA => 'array',
        self::FIELD_MANUAL_FIELDS => 'array',
        self::FIELD_LEAD_CREATION_DATE => 'datetime',
    ];
}
