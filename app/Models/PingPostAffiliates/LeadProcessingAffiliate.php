<?php

namespace App\Models\PingPostAffiliates;

use App\Models\BaseModel;
use App\Models\Odin\ConsumerProduct;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $consumer_product_id
 * @property float|null $possible_revenue
 * @property int $priority
 *
 * @property-read $consumerProduct
 */
class LeadProcessingAffiliate extends BaseModel
{
    use HasFactory;

    const string TABLE = 'lead_processing_affiliates';

    const string FIELD_ID = 'id';
    const string FIELD_CONSUMER_PRODUCT_ID = 'consumer_product_id';
    const string FIELD_POSSIBLE_REVENUE = 'possible_revenue';
    const string FIELD_PRIORITY = 'priority';

    const string RELATION_CONSUMER_PRODUCT = 'consumerProduct';


    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function consumerProduct(): BelongsTo
    {
        return $this->belongsTo(ConsumerProduct::class);
    }
}
