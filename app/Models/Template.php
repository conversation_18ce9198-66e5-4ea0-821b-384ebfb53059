<?php

namespace App\Models;

use App\Enums\TemplateManagement\TemplateType;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $uuid
 * @property string $owner_type
 * @property int $owner_id
 * @property TemplateType $type
 * @property string $name
 * @property string|null $description
 * @property string|null $subject
 * @property string $content
 * @property Carbon $updated_at
 *
 * @property-read mixed $owner
 */
class Template extends Model
{
    use SoftDeletes;

    const string TABLE = 'templates';

    const string FIELD_ID = 'id';
    const string FIELD_UUID = 'uuid';
    const string FIELD_OWNER_TYPE = 'owner_type';
    const string FIELD_OWNER_ID = 'owner_id';
    const string FIELD_TYPE = 'type';
    const string FIELD_NAME = 'name';
    const string FIELD_DESCRIPTION = 'description';
    const string FIELD_SUBJECT = 'subject';
    const string FIELD_CONTENT = 'content';

    const string RELATION_OWNER = 'owner';

    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];
    protected $casts = [self::FIELD_TYPE => TemplateType::class];

    /**
     * @return MorphTo
     */
    public function owner(): MorphTo
    {
        return $this->morphTo();
    }
}
