<?php

namespace App\Models;

use App\Enums\EmailAddress\SpamTrapScore;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * @property string $id
 * @property string $email
 * @property string $sanitized_email
 * @property bool $valid
 * @property ?bool $disposable
 * @property ?bool $frequent_complainer
 * @property ?SpamTrapScore $spam_trap_score
 * @property ?float $fraud_score
 * @property array $payload
 */
class EmailAddress extends BaseModel
{
    use HasFactory;

    const string TABLE = 'email_addresses';

    const string FIELD_ID                  = 'id';
    const string FIELD_EMAIL               = 'email';
    const string FIELD_SANITIZED_EMAIL     = 'sanitized_email';
    const string FIELD_VALID               = 'valid';
    const string FIELD_DISPOSABLE          = 'disposable';
    const string FIELD_FREQUENT_COMPLAINER = 'frequent_complainer';
    const string FIELD_SPAM_TRAP_SCORE     = 'spam_trap_score';
    const string FIELD_FRAUD_SCORE         = 'fraud_score';
    const string FIELD_PAYLOAD             = 'payload';
    const string FIELD_CREATED_AT          = 'created_at';
    const string FIELD_UPDATED_AT          = 'updated_at';

    protected $table = self::TABLE;
    protected $guarded = [
        self::FIELD_ID,
    ];

    protected $casts = [
        self::FIELD_SPAM_TRAP_SCORE => SpamTrapScore::class,
        self::FIELD_PAYLOAD         => 'array',
    ];
}
