<?php

namespace App\Models;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\AppointmentOffering;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCampaign;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $appointment_offering_id
 * @property int $product_campaign_id
 * @property int $product_assignment_id
 * @property boolean $consumer_delivered
 * @property boolean $consumer_delivered_sms
 * @property boolean $consumer_delivered_email
 * @property boolean $company_delivered
 * @property string $consumer_token
 * @property int $consumer_code
 * @property int $attempts
 * @property int $next_attempt_timestamp
 * @property int $company_campaign_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read AppointmentOffering $appointmentOffering
 * @property-read ProductAssignment $productAssignment
 * @property-read CompanyCampaign $companyCampaign
 */
class AppointmentDelivery extends Model
{
    use HasFactory;

    const string TABLE = 'appointment_deliveries';

    const string FIELD_ID                       = 'id';
    const string FIELD_APPOINTMENT_OFFERING_ID  = 'appointment_offering_id';
    const string FIELD_PRODUCT_CAMPAIGN_ID      = 'product_campaign_id';
    const string FIELD_PRODUCT_ASSIGNMENT_ID    = 'product_assignment_id';
    const string FIELD_CONSUMER_DELIVERED       = 'consumer_delivered';
    const string FIELD_CONSUMER_DELIVERED_SMS   = 'consumer_delivered_sms';
    const string FIELD_CONSUMER_DELIVERED_EMAIL = 'consumer_delivered_email';
    const string FIELD_COMPANY_DELIVERED        = 'company_delivered';
    const string FIELD_CONSUMER_TOKEN           = 'consumer_token';
    const string FIELD_CONSUMER_CODE            = 'consumer_code';
    const string FIELD_ATTEMPTS                 = 'attempts';
    const string FIELD_NEXT_ATTEMPT_TIMESTAMP   = 'next_attempt_timestamp';
    const string FIELD_COMPANY_CAMPAIGN_ID      = 'company_campaign_id';

    const string RELATION_APPOINTMENT_OFFERING = 'appointmentOffering';
    const string RELATION_PRODUCT_ASSIGNMENT   = 'productAssignment';
    const string RELATION_PRODUCT_CAMPAIGN     = 'productCampaign';
    const string RELATION_COMPANY_CAMPAIGN     = 'companyCampaign';

    protected $table = self::TABLE;
    protected $guarded = [
        self::FIELD_ID
    ];

    /**
     * @return BelongsTo
     */
    public function productCampaign(): BelongsTo
    {
        return $this->belongsTo(ProductCampaign::class, self::FIELD_PRODUCT_CAMPAIGN_ID, ProductCampaign::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function appointmentOffering(): BelongsTo
    {
        return $this->belongsTo(AppointmentOffering::class, self::FIELD_APPOINTMENT_OFFERING_ID, AppointmentOffering::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function productAssignment(): BelongsTo
    {
        return $this->belongsTo(ProductAssignment::class, self::FIELD_PRODUCT_ASSIGNMENT_ID, ProductAssignment::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function companyCampaign(): BelongsTo
    {
        return $this->belongsTo(CompanyCampaign::class, self::FIELD_COMPANY_CAMPAIGN_ID, CompanyCampaign::FIELD_ID);
    }
}
