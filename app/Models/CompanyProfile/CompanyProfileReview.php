<?php

namespace App\Models\CompanyProfile;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property string $text
 * @property float $rating
 * @property Carbon $date
 * @property string $author
 * @property integer $helpful
 * @property array $additional_ratings
 * @property string $source
 * @property integer $company_profile_id
 * @property Carbon $created_at
 */
class CompanyProfileReview extends Model
{
    use HasFactory;

    const string TABLE = 'company_profile_reviews';

    const string FIELD_ID                 = 'id';
    const string FIELD_TEXT               = 'text';
    const string FIELD_RATING             = 'rating';
    const string FIELD_DATE               = 'date';
    const string FIELD_AUTHOR             = 'author';
    const string FIELD_HELPFUL            = 'helpful';
    const string FIELD_ADDITIONAL_RATINGS = 'additional_ratings';
    const string FIELD_SOURCE             = 'source';
    const string FIELD_COMPANY_PROFILE_ID = 'company_profile_id';
    const string FIELD_CREATED_AT         = 'created_at';
    const string FIELD_UPDATED_AT         = 'updated_at';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $casts = [
        self::FIELD_ADDITIONAL_RATINGS => 'array',
        self::FIELD_DATE               => 'date'
    ];
}
