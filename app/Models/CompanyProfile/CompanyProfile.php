<?php

namespace App\Models\CompanyProfile;

use App\Models\Legacy\Location;
use App\Models\Odin\Company;
use App\Models\Odin\IndustryService;
use App\Models\Prospects\NewBuyerProspect;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property string $name
 * @property array $categories
 * @property string $description
 * @property string $general_info
 * @property array $hours
 * @property array $websites
 * @property array $phones
 * @property array $addresses
 * @property integer $years_in_business
 * @property float $rating
 * @property boolean $paid
 * @property string $logo
 * @property string $email
 * @property array $other_links
 * @property array $social_links
 * @property integer $discovery_score
 * @property array $payload
 * @property ?integer $company_id
 * @property ?string $summary_positives
 * @property ?string $summary_negatives
 * @property ?string $summary_overview
 * @property ?string $summary_source
 * @property ?string $profile_slug
 * @property boolean $published
 * @property array $images
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Collection<CompanyProfileReview> $reviews
 * @property-read Collection<CompanyProfileServiceArea> $serviceAreas
 * @property-read Collection<CompanyProfileIndustryService> $companyProfileIndustryServices
 * @property-read Collection<CompanyProfileLocation> $companyProfileLocations
 * @property-read Collection<IndustryService> $industryServices
 * @property-read Collection<Location> $locations
 */
class CompanyProfile extends Model
{
    use HasFactory;

    const string TABLE = 'company_profiles';

    const string FIELD_ID                    = 'id';
    const string FIELD_NAME                  = 'name';
    const string FIELD_CATEGORIES            = 'categories';
    const string FIELD_DESCRIPTION           = 'description';
    const string FIELD_GENERAL_INFO          = 'general_info';
    const string FIELD_HOURS                 = 'hours';
    const string FIELD_WEBSITES              = 'websites';
    const string FIELD_PHONES                = 'phones';
    const string FIELD_ADDRESSES             = 'addresses';
    const string FIELD_YEARS_IN_BUSINESS     = 'years_in_business';
    const string FIELD_RATING                = 'rating';
    const string FIELD_PAID                  = 'paid';
    const string FIELD_LOGO                  = 'logo';
    const string FIELD_EMAIL                 = 'email';
    const string FIELD_OTHER_LINKS           = 'other_links';
    const string FIELD_SOCIAL_LINKS          = 'social_links';
    const string FIELD_DISCOVERY_SCORE       = 'discovery_score';
    const string FIELD_SUMMARY_POSITIVES     = 'summary_positives';
    const string FIELD_SUMMARY_NEGATIVES     = 'summary_negatives';
    const string FIELD_SUMMARY_OVERVIEW      = 'summary_overview';
    const string FIELD_SUMMARY_SOURCE        = 'summary_source';
    const string FIELD_PROFILE_SLUG          = 'profile_slug';
    const string FIELD_IMAGES                = 'images';
    const string FIELD_PUBLISHED             = 'published';
    const string FIELD_PAYLOAD               = 'payload';
    const string FIELD_CREATED_AT            = 'created_at';
    const string FIELD_UPDATED_AT            = 'updated_at';
    const string FIELD_COMPANY_ID            = 'company_id';
    const string FIELD_NEW_BUYER_PROSPECT_ID = 'new_buyer_prospect_id';

    const string RELATION_REVIEWS                           = 'reviews';
    const string RELATION_SERVICE_AREAS                     = 'serviceAreas';
    const string RELATION_COMPANY_PROFILE_INDUSTRY_SERVICES = 'companyProfileIndustryServices';
    const string RELATION_COMPANY_PROFILE_LOCATIONS         = 'companyProfileLocations';
    const string RELATION_INDUSTRY_SERVICES                 = 'industryServices';
    const string RELATION_COMPANY                           = 'company';
    const string RELATION_NEW_BUYER_PROSPECT                = 'newBuyerProspect';


    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $casts = [
        self::FIELD_CATEGORIES   => 'array',
        self::FIELD_HOURS        => 'array',
        self::FIELD_WEBSITES     => 'array',
        self::FIELD_PHONES       => 'array',
        self::FIELD_ADDRESSES    => 'array',
        self::FIELD_OTHER_LINKS  => 'array',
        self::FIELD_SOCIAL_LINKS => 'array',
        self::FIELD_PAYLOAD      => 'array',
        self::FIELD_IMAGES       => 'array',
    ];

    public function reviews(): HasMany
    {
        return $this->hasMany(CompanyProfileReview::class);
    }

    public function serviceAreas(): HasMany
    {
        return $this->hasMany(CompanyProfileServiceArea::class);
    }

    public function companyProfileIndustryServices(): HasMany
    {
        return $this->hasMany(CompanyProfileIndustryService::class);
    }

    public function industryServices(): HasManyThrough
    {
        return $this->hasManyThrough(
            IndustryService::class,
            CompanyProfileIndustryService::class,
            CompanyProfileIndustryService::FIELD_COMPANY_PROFILE_ID,
            IndustryService::FIELD_ID,
            self::FIELD_ID,
            CompanyProfileIndustryService::FIELD_INDUSTRY_SERVICE_ID,
        );
    }

    public function companyProfileLocations(): HasMany
    {
        return $this->hasMany(CompanyProfileLocation::class);
    }

    public function getLocationsAttribute()
    {
        $locationIds = $this->companyProfileLocations()->pluck(CompanyProfileLocation::FIELD_LOCATION_ID);

        return Location::query()->whereIntegerInRaw(Location::ID, $locationIds)->get();
    }

    /**
     * @return HasOne
     */
    public function company(): HasOne
    {
        return $this->hasOne(Company::class, Company::FIELD_ID, self::FIELD_COMPANY_ID);
    }

    /**
     * @return HasOne
     */
    public function newBuyerProspect(): HasOne
    {
        return $this->hasOne(NewBuyerProspect::class, NewBuyerProspect::FIELD_ID, self::FIELD_NEW_BUYER_PROSPECT_ID);
    }

    /**
     * @return string|null
     */
    public function getPrimaryWebsite(): ?string
    {
        return filled($this->websites) ? $this->websites[0] : null;
    }

    /**
     * @return string|null
     */
    public function getPrimaryPhone(): ?string
    {
        return filled($this->phones) ? $this->phones[0] : null;
    }
}
