<?php

namespace App\Models\CompanyProfile;

use App\Models\Odin\IndustryService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property int $company_profile_id
 * @property int $industry_service_id
 *
 * @property-read Collection $industryService
 * @property-read CompanyProfile $profile
 */
class CompanyProfileIndustryService extends Model
{
    use HasFactory;

    const string TABLE = 'company_profile_industry_services';

    const string FIELD_ID                  = 'id';
    const string FIELD_COMPANY_PROFILE_ID  = 'company_profile_id';
    const string FIELD_INDUSTRY_SERVICE_ID = 'industry_service_id';

    const string RELATION_INDUSTRY_SERVICE = 'industryService';
    const string RELATION_PROFILE = 'profile';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];

    /**
     * @return BelongsTo
     */
    public function industryService(): BelongsTo
    {
        return $this->belongsTo(IndustryService::class);
    }

    public function profile(): BelongsTo
    {
        return $this->belongsTo(CompanyProfile::class);
    }
}
