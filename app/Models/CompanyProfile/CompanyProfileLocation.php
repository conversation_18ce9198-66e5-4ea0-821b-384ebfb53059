<?php

namespace App\Models\CompanyProfile;

use App\Models\Legacy\Location;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $company_profile_id
 * @property int $location_id
 * @property string $raw
 *
 * @property-read Location $location
 * @property-read CompanyProfile $profile
 */
class CompanyProfileLocation extends Model
{
    use HasFactory;

    const string TABLE = 'company_profile_locations';

    const string FIELD_ID                 = 'id';
    const string FIELD_COMPANY_PROFILE_ID = 'company_profile_id';
    const string FIELD_LOCATION_ID        = 'location_id';
    const string FIELD_RAW                = 'raw';

    const string RELATION_LOCATION = 'location';
    const string RELATION_PROFILE  = 'profile';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];

    public function profile(): BelongsTo
    {
        return $this->belongsTo(CompanyProfile::class, self::FIELD_COMPANY_PROFILE_ID, CompanyProfile::FIELD_ID);
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }
}
