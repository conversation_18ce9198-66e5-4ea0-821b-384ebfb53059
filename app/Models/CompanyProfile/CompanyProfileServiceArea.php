<?php

namespace App\Models\CompanyProfile;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $company_profile_id
 * @property int $company_profile_location_id
 * @property int $company_profile_industry_service_id
 *
 * @property-read CompanyProfile $profile
 * @property-read CompanyProfileLocation $companyProfileLocation
 * @property-read CompanyProfileIndustryService $companyProfileIndustryService
 */
class CompanyProfileServiceArea extends Model
{
    use HasFactory;

    const string TABLE = 'company_profile_service_areas';

    const string FIELD_ID                                  = 'id';
    const string FIELD_COMPANY_PROFILE_ID                  = 'company_profile_id';
    const string FIELD_COMPANY_PROFILE_LOCATION_ID         = 'company_profile_location_id';
    const string FIELD_COMPANY_PROFILE_INDUSTRY_SERVICE_ID = 'company_profile_industry_service_id';

    const string RELATION_PROFILE                          = 'profile';
    const string RELATION_COMPANY_PROFILE_LOCATION         = 'companyProfileLocation';
    const string RELATION_COMPANY_PROFILE_INDUSTRY_SERVICE = 'companyProfileIndustryService';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];

    public function profile(): BelongsTo
    {
        return $this->belongsTo(CompanyProfile::class);
    }

    public function companyProfileLocation(): BelongsTo
    {
        return $this->belongsTo(CompanyProfileLocation::class);
    }

    public function companyProfileIndustryService(): BelongsTo
    {
        return $this->belongsTo(CompanyProfileIndustryService::class);
    }

}
