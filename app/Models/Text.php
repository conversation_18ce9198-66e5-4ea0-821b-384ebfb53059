<?php

namespace App\Models;

use App\Enums\CommunicationRelationTypes;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property string $external_reference
 * @property string $external_type
 * @property int $phone_id
 * @property string $other_number
 * @property string $direction
 * @property string $message_body
 * @property string $relation_type
 * @property int $relation_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property int $company_cadence_group_action_id
 *
 * @property-read Phone $phone
 * @property-read Collection<TextMediaAsset> $textMediaAssets
 * @property-read Consumer $consumer
 * @property-read ConsumerProduct $consumerProduct
 * @property-read CompanyUser $companyUser
 */
class Text extends BaseModel
{
    use hasFactory;

    const string TABLE = 'texts';

    const string FIELD_ID                              = 'id';
    const string FIELD_EXTERNAL_REFERENCE              = 'external_reference';
    const string FIELD_EXTERNAL_TYPE                   = 'external_type';
    const string FIELD_PHONE_ID                        = 'phone_id';
    const string FIELD_OTHER_NUMBER                    = 'other_number';
    const string FIELD_DIRECTION                       = 'direction';
    const string FIELD_MESSAGE_BODY                    = 'message_body';
    const string FIELD_RELATION_TYPE                   = 'relation_type';
    const string FIELD_RELATION_ID                     = 'relation_id';
    const string FIELD_CREATED_AT                      = 'created_at';
    const string FIELD_NOTE                            = 'note';
    const string FILED_COMPANY_CADENCE_GROUP_ACTION_ID = 'company_cadence_group_action_id';

    const string DIRECTION_OUTBOUND = 'outbound';
    const string DIRECTION_INBOUND  = 'inbound';

    const string EXTERNAL_TYPE_TWILIO = 'twilio';

    const string RELATION_PHONE        = 'phone';
    const string RELATION_COMPANY      = 'company';
    const string RELATION_LEAD         = 'lead';
    const string RELATION_CONSUMER     = 'consumer';
    const string RELATION_COMPANY_USER = 'companyUser';
    const string RELATION_TEXT_MEDIA_ASSET = 'textMediaAssets';

    protected $table   = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function phone(): BelongsTo
    {
        return $this->belongsTo(Phone::class, self::FIELD_PHONE_ID, Phone::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_RELATION_ID, Company::FIELD_ID)
                    ->where(self::FIELD_RELATION_TYPE, self::RELATION_COMPANY);
    }

    /**
     * @return BelongsTo
     */
    public function consumer(): BelongsTo
    {
        return $this->belongsTo(Consumer::class, self::FIELD_RELATION_ID, Consumer::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function consumerProduct(): BelongsTo
    {
        return $this->belongsTo(ConsumerProduct::class, self::FIELD_RELATION_ID, ConsumerProduct::FIELD_ID);

    }

    /**
     * @return Consumer|null
     */
    public function getConsumer(): ?Consumer
    {
        return $this->relation_type === CommunicationRelationTypes::CONSUMER_PRODUCT->value
            ? $this->consumerProduct?->consumer
            : $this->consumer;
    }

    /**
     * @return BelongsTo
     */
    public function companyUser(): BelongsTo
    {
        return $this->belongsTo(CompanyUser::class, self::FIELD_RELATION_ID, CompanyUser::FIELD_ID);
    }

    /**
     * @return MorphOne
     */
    public function activity(): MorphOne
    {
        return $this->morphOne(ActivityFeed::class, 'item');
    }

    /**
     * @return HasMany
     */
    public function textMediaAssets(): HasMany
    {
        return $this->hasMany(TextMediaAsset::class, TextMediaAsset::FIELD_TEXT_ID, self::FIELD_ID);
    }

    /**
     * @return MorphOne
     */
    public function consumerProcessingActivity(): MorphOne
    {
        return $this->morphOne(ConsumerProcessingActivity::class, ConsumerProcessingActivity::RELATION_ACTIVITY);
    }
}
