<?php

namespace App\Models\Conference;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 *
 * @property int $conference_id
 * @property string $external_id
 * @property string $name
 * @property string $earliest_start_time
 * @property string $latest_end_time
 * @property int $duration_in_seconds
 *
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Conference $conference
 */
class ConferenceParticipant extends BaseModel
{
    use HasFactory;

    const string TABLE = 'conference_participants';

    const string FIELD_ID = 'id';

    const string FIELD_CONFERENCE_ID       = 'conference_id';
    const string FIELD_NAME                = 'name';
    const string FIELD_EXTERNAL_ID         = 'external_id';
    const string FIELD_EARLIEST_START_TIME = 'earliest_start_time';
    const string FIELD_LATEST_END_TIME     = 'latest_end_time';
    const string FIELD_DURATION_IN_SECONDS = 'duration_in_seconds';

    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $casts = [
        self::FIELD_EARLIEST_START_TIME => 'datetime',
        self::FIELD_LATEST_END_TIME     => 'datetime',
    ];

    protected $table = self::TABLE;

    /**
     * @return BelongsTo
     */
    public function conference(): BelongsTo
    {
        return $this->belongsTo(Conference::class, self::FIELD_CONFERENCE_ID, self::FIELD_ID);
    }
}
