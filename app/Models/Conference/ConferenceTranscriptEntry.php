<?php

namespace App\Models\Conference;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 *
 * @property int $conference_transcript_id
 * @property int $conference_participant_id
 * @property string $external_id
 * @property string $external_participant_id
 * @property string $start_time
 * @property string $end_time
 * @property string $text
 *
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Conference $conference
 * @property-read ConferenceTranscript $transcript
 * @property-read ConferenceParticipant $participant
 */
class ConferenceTranscriptEntry extends BaseModel
{
    use HasFactory;

    const string TABLE = 'conference_transcript_entries';

    const string FIELD_ID = 'id';

    const string FIELD_CONFERENCE_TRANSCRIPT_ID  = 'conference_transcript_id';
    const string FIELD_CONFERENCE_PARTICIPANT_ID = 'conference_participant_id';
    const string FIELD_EXTERNAL_ID               = 'external_id';
    const string FIELD_EXTERNAL_PARTICIPANT_ID   = 'external_participant_id';
    const string FIELD_START_TIME                = 'start_time';
    const string FIELD_END_TIME                  = 'end_time';
    const string FIELD_TEXT                      = 'text';

    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';

    const string RELATION_PARTICIPANT = 'participant';

    protected $casts = [
        self::FIELD_START_TIME => 'datetime',
        self::FIELD_END_TIME   => 'datetime',
    ];

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $table = self::TABLE;

    /**
     * @return BelongsTo
     */
    public function transcript(): BelongsTo
    {
        return $this->belongsTo(ConferenceTranscript::class, self::FIELD_CONFERENCE_TRANSCRIPT_ID, self::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function participant(): BelongsTo
    {
        return $this->belongsTo(ConferenceParticipant::class, self::FIELD_CONFERENCE_PARTICIPANT_ID, self::FIELD_ID);
    }
}
