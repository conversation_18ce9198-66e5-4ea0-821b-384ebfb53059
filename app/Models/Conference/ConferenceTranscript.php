<?php

namespace App\Models\Conference;

use App\Jobs\Deepgram\AnalyzeConferenceTranscriptWithDeepgram;
use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

/**
 * @property int $id
 *
 * @property int $conference_id
 * @property string $external_id
 * @property string $end_time
 * @property string $start_time
 * @property int $duration_in_seconds
 * @property string $docs_destination_document_id
 * @property string $docs_destination_document_url
 *
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Conference $conference
 * @property-read Collection<ConferenceTranscriptEntry> $entries
 * @property-read string $text
 */
class ConferenceTranscript extends BaseModel
{
    use HasFactory;

    const string FIELD_CONFERENCE_ID = 'conference_id';
    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_DOCS_DESTINATION_DOCUMENT_ID = 'docs_destination_document_id';
    const string FIELD_DOCS_DESTINATION_DOCUMENT_URL = 'docs_destination_document_url';
    const string FIELD_DURATION_IN_SECONDS = 'duration_in_seconds';
    const string FIELD_END_TIME = 'end_time';
    const string FIELD_EXTERNAL_ID = 'external_id';
    const string FIELD_ID = 'id';
    const string FIELD_START_TIME = 'start_time';
    const string FIELD_UPDATED_AT = 'updated_at';
    const string RELATION_ENTRIES = 'entries';
    const string TABLE = 'conference_transcripts';
    protected $casts = [
        self::FIELD_START_TIME => 'datetime',
        self::FIELD_END_TIME => 'datetime',
    ];

    protected $guarded = [
        self::FIELD_ID,
    ];

    protected $table = self::TABLE;

    /**
     * @return BelongsTo
     */
    public function conference(): BelongsTo
    {
        return $this->belongsTo(Conference::class, self::FIELD_CONFERENCE_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function entries(): HasMany
    {
        return $this->hasMany(ConferenceTranscriptEntry::class,
            ConferenceTranscriptEntry::FIELD_CONFERENCE_TRANSCRIPT_ID, self::FIELD_ID);
    }

    public function analyzeWithDeepgram(): void
    {
        AnalyzeConferenceTranscriptWithDeepgram::dispatch($this);
    }

    /**
     * @see $text
     */
    public function text(): Attribute
    {
        $text = $this->entries
            ->sortBy('start_time')
            ->sortBy('end_time')
            ->filter(fn(ConferenceTranscriptEntry $entry) => $entry->text)
            ->implode(function (ConferenceTranscriptEntry $transcriptEntry) {
                return "{$transcriptEntry->participant->name}\n{$transcriptEntry->start_time->format('H:i:s')} - {$transcriptEntry->end_time->format('H:i:s')}\n\n$transcriptEntry->text";
            }, "\n\n");

        return Attribute::make(
            get: static fn() => $text,
        );
    }
}
