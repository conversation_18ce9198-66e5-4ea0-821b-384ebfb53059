<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * @property int $id
 * @property int $hunter_id
 * @property string $state_abbr
 */
class HunterState extends BaseModel
{
    use HasFactory;

    const TABLE = 'hunter_states';

    const ID = 'id';
    const HUNTER_ID = 'hunter_id';
    const STATE_ABBR = 'state_abbr';

    protected $table = self::TABLE;
    protected $guarded = [self::ID];
}
