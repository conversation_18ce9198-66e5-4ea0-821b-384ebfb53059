<?php

namespace App\Models;

use App\Enums\RoleType;
use App\Models\Odin\Company;
use App\Observers\CompanyUserRelationshipObserver;
use App\Services\Companies\CompanyManagerAssignmentService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property int $user_id
 * @property int $company_id
 * @property int $role_id
 * @property string $conclusion
 * @property array $payload
 * @property ?Carbon $commissionable_at
 * @property ?Carbon $commissionable_to
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property ?Carbon $deleted_at
 *
 * @property-read User $user
 * @property-read Company $company
 * @property-read \Spatie\Permission\Models\Role $role
 * @property-read \Spatie\Permission\Models\Role $type
 */

#[ObservedBy([CompanyUserRelationshipObserver::class])]
class CompanyUserRelationship extends Model
{
    use SoftDeletes, HasFactory;

    const string TABLE = 'company_user_relationships';

    const string FIELD_ID               = 'id';
    const string FIELD_USER_ID          = 'user_id';
    const string FIELD_COMPANY_ID       = 'company_id';
    const string FIELD_ROLE_ID          = 'role_id';
    const string FIELD_CONCLUSION       = 'conclusion';
    const string FIELD_PAYLOAD          = 'payload';
    const string FIELD_COMMISIONABLE_AT = 'commissionable_at';
    const string FIELD_COMMISIONABLE_TO = 'commissionable_to';
    const string FIELD_CREATED_AT       = 'created_at';
    const string FIELD_UPDATED_AT       = 'updated_at';
    const string FIELD_DELETED_AT       = 'deleted_at';

    const string RELATION_USER = 'user';
    const string RELATION_COMPANY = 'company';
    const string RELATION_ROLE = 'role';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_PAYLOAD          => 'array',
        self::FIELD_COMMISIONABLE_AT => 'datetime',
        self::FIELD_CREATED_AT       => 'datetime',
        self::FIELD_UPDATED_AT       => 'datetime',
        self::FIELD_DELETED_AT       => 'datetime',
        self::FIELD_COMMISIONABLE_TO => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }

    /**
     * @return void
     */
    public function setCommissionableAt(): void
    {
        if ($this->commissionable_at) {
            return;
        }

        /** @var CompanyManagerAssignmentService $service */
        $service = app(CompanyManagerAssignmentService::class);
        $fistLead = $service->getFirstLeadForCompany($this->company);

        $commissionDate = match ($this->role->name) {
            RoleType::BUSINESS_DEVELOPMENT_MANAGER->value, RoleType::ONBOARDING_MANAGER->value => $fistLead?->delivered_at,
            RoleType::ACCOUNT_MANAGER->value => $fistLead?->delivered_at->addDays(90),
            default => null,
        };

        if ($commissionDate) {
            $this->update([
                self::FIELD_COMMISIONABLE_AT => $commissionDate
            ]);
        }
    }
}
