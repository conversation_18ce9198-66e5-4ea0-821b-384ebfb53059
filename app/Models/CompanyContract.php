<?php

namespace App\Models;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Traits\HasUuid;
use Carbon\Carbon;
use Database\Factories\Odin\CompanyContractFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

/**
 * @property int $id
 * @property string $uuid
 * @property string $contract_type
 * @property int $company_id
 * @property string $contract
 * @property int $contract_id
 * @property string $signature_id
 * @property string $file_url
 * @property string $ip_address
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @Property Carbon $agreed_at
 *
 * @property-read Company $company
 * @property-read Contract $contractModel
 */
class CompanyContract extends Model
{
    use HasFactory, HasUuid;

    const TABLE = 'company_contracts';

    const FIELD_ID              = 'id';
    const FIELD_UUID            = 'uuid';
    const FIELD_COMPANY_ID      = 'company_id';
    const FIELD_COMPANY_USER_ID = 'company_user_id';
    const FIELD_CONTRACT_TYPE   = 'contract_type';
    const FIELD_CONTRACT        = 'contract';
    const FIELD_AGREED_AT       = 'agreed_at';
    const FIELD_IP_ADDRESS      = 'ip_address';
    const FIELD_CONTRACT_ID     = 'contract_id';
    const FIELD_SIGNATURE_ID    = 'signature_id';
    const FIELD_FILE_URL        = 'file_url';
    const FIELD_CREATED_AT      = 'created_at';

    const RELATION_COMPANY      = 'company';
    const RELATION_COMPANY_USER = 'companyUser';
    const RELATION_CONTRACT     = 'contractModel';

    protected $table = self::TABLE;

    protected $guarded = [ self::FIELD_ID ];

    protected $casts = [self::FIELD_AGREED_AT => 'datetime'];

    protected static function newFactory(): CompanyContractFactory
    {
        return CompanyContractFactory::new();
    }

    protected static function booted()
    {
        parent::booted();
        static::created(function (CompanyContract $contract) {
            if (!$contract->{CompanyContract::FIELD_UUID}) $contract->saveUuid();
        });
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }

    public function companyUser(): BelongsTo
    {
        return $this->belongsTo(CompanyUser::class, self::FIELD_COMPANY_USER_ID, CompanyUser::FIELD_ID);
    }

    public function contractModel(): BelongsTo
    {
        return $this->belongsTo(Contract::class, self::FIELD_CONTRACT_ID, Contract::FIELD_ID);
    }

    public function agree(): bool
    {
        return $this->update([
            self::FIELD_AGREED_AT => Carbon::now(),
        ]);
    }

    public function hasAgreed(): bool
    {
        return $this->{self::FIELD_AGREED_AT} !== null;
    }

    /**
     * @return MorphMany
     */
    public function auditLogs(): MorphMany
    {
        return $this->morphMany(AuditLog::class, null, AuditLog::FIELD_MODEL_TYPE, AuditLog::FIELD_MODEL_ID);
    }

}
