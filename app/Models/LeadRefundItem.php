<?php

namespace App\Models;

use App\Enums\LeadRefundItemStatus;
use App\Enums\LeadRefundItemType;
use App\Models\Odin\ProductAssignment;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class LeadRefundItem extends BaseModel
{
    const string TABLE = 'lead_refund_items';

    const string FIELD_ID                    = 'id';
    const string FIELD_LEAD_REFUND_ID        = 'lead_refund_id';
    const string FIELD_PRODUCT_ASSIGNMENT_ID = 'product_assignment_id';
    const string FIELD_VALUE                 = 'value';
    const string FIELD_REFUND_REASON         = 'refund_reason';
    const string FIELD_STATUS                = 'status';
    const string FIELD_REFUND_TYPE           = 'refund_type';
    const string FIELD_CREATED_AT            = 'created_at';
    const string FIELD_UPDATED_AT            = 'updated_at';

    const string RELATION_REFUND                    = 'refund';
    const string RELATION_REFUND_ITEM_REFUND        = 'refundItemRefund';
    const string RELATION_LATEST_REFUND_ITEM_REFUND = 'latestRefundItemRefund';
    const string RELATION_PRODUCT_ASSIGNMENT        = 'productAssignment';

    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_STATUS      => LeadRefundItemStatus::class,
        self::FIELD_REFUND_TYPE => LeadRefundItemType::class,
    ];

    /**
     * @return BelongsTo
     */
    public function refund(): BelongsTo
    {
        return $this->belongsTo(LeadRefund::class, self::FIELD_LEAD_REFUND_ID, LeadRefund::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function refundItemRefund(): HasOne
    {
        return $this->hasOne(LeadRefundItemRefund::class, LeadRefundItemRefund::FIELD_LEAD_REFUND_ITEM_ID, self::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function latestRefundItemRefund(): HasOne
    {
        return $this->hasOne(LeadRefundItemRefund::class, LeadRefundItemRefund::FIELD_LEAD_REFUND_ITEM_ID, self::FIELD_ID)
            ->latestOfMany();
    }

    /**
     * @return BelongsTo
     */
    public function productAssignment(): BelongsTo
    {
        return $this->belongsTo(ProductAssignment::class, self::FIELD_PRODUCT_ASSIGNMENT_ID, ProductAssignment::FIELD_ID);
    }
}
