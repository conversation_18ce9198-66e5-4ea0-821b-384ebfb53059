<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int    $id
 * @property int    $text_id
 * @property string $name
 * @property string $url
 * @property int    $type https://www.twilio.com/docs/messaging/guides/accepted-mime-types
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Text $company
 */
class TextMediaAsset extends BaseModel
{
    const string TABLE = 'text_media_assets';

    const string FIELD_ID           = 'id';
    const string FIELD_TEXT_ID      = 'text_id';
    const string FIELD_URL          = 'url';
    const string FIELD_TYPE         = 'type';
    const string FIELD_CREATED_AT   = 'created_at';
    const string FIELD_UPDATED_AT   = 'updated_at';

    const string RELATION_TEXT  = 'text';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function text(): BelongsTo
    {
        return $this->belongsTo(Text::class, Text::FIELD_ID, self::FIELD_TEXT_ID);
    }

}