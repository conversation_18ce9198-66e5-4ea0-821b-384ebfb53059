<?php

namespace App\Models;

use App\Enums\BundleInvoiceStatus;
use App\Models\Odin\Industry;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Ramsey\Collection\Collection;

/**
 * Class Bundle
 *
 * @package App\Models
 *
 * @property integer $id
 * @property string $name
 * @property string $note
 * @property float $cost
 * @property float $credit
 * @property Int $created_by
 * @property Carbon $deleted_at
 * @property boolean $is_auto_approved
 * @property boolean $is_active
 * @property-read Collection|null $invoices
 * @property-read User|null $createdBy
 * @property-read Industry $industry
 */
class Bundle extends Model
{
    use HasFactory, SoftDeletes;

    const TABLE = 'bundles';

    const FIELD_ID = 'id';
    const FIELD_NAME = 'name';
    const FIELD_NOTE = 'note';
    const FIELD_TITLE = 'title';
    const FIELD_DESCRIPTION = 'description';
    const FIELD_COST = 'cost';
    const FIELD_CREDIT = 'credit';
    const FIELD_CREATED_BY = 'created_by';
    const FIELD_DELETED_AT = 'deleted_at';
    const FIELD_ACTIVATED_AT = 'activated_at';
    const FIELD_ACTIVE = 'active';
    const FIELD_INDUSTRY_ID      = 'industry_id';
    const FIELD_IS_AUTO_APPROVED = 'is_auto_approved';
    const FIELD_AUTO_APPLY_CREDITS = 'auto_apply_credits';

    const RELATION_INVOICES = 'invoices';
    const RELATION_CREATED_BY = 'createdBy';
    const RELATION_INDUSTRY = 'industry';

    protected $guarded = [ self::FIELD_ID ];

    /**
     * @return HasMany
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(BundleInvoice::class, BundleInvoice::FIELD_BUNDLE_ID, self::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function createdBy(): HasOne
    {
        return $this->hasOne(User::class, User::FIELD_ID, self::FIELD_CREATED_BY);
    }


    /**
     * @return HasOne
     */
    public function industry(): HasOne
    {
        return $this->hasOne(Industry::class, Industry::FIELD_ID, self::FIELD_INDUSTRY_ID);
    }

    /**
     * Check whether the activated_at is set, if it is then this bundle is active.
     *
     * @return Attribute
     */
    public function isActive(): Attribute
    {
        return Attribute::make(get: function() {
            return (bool)$this->activated_at;
        });
    }

    /**
     * @param $query
     * @return mixed
     */
    public function scopeActive($query)
    {
        return $query->whereNotNull(self::FIELD_ACTIVATED_AT);
    }
}
