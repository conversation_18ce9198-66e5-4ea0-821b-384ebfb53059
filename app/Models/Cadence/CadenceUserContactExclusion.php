<?php

namespace App\Models\Cadence;

/**
 * Company users that should not be contacted by cadence routine
 *
 * @property int|null $company_cadence_routine_id // If null, user should be removed from all current and future routines
 * @property int $user_id
 */
class CadenceUserContactExclusion extends BaseModel
{
    const TABLE = 'cadence_user_contact_exclusions';

    const FIELD_COMPANY_CADENCE_ROUTINE_ID = 'company_cadence_routine_id';
    const FIELD_USER_ID = 'user_id'; // todo: change to company_user_id

    protected $table = self::TABLE;
}
