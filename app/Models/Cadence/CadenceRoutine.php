<?php

namespace App\Models\Cadence;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;

/**
 * Template used when creating a CompanyCadenceRoutine
 *
 * @property string $name
 * @property bool $contact_decision_makers_only
 * @property bool $contact_on_weekdays_only
 * @property int $user_id
 * @property bool $global
 * @property string $domain
 *
 * @property-read Collection $scheduledGroups
 * @property-read Collection $companyRoutines
 * @property-read User $user
 */
class CadenceRoutine extends BaseModel
{
    use SoftDeletes, HasFactory;

    const TABLE = 'cadence_routines';

    const FIELD_NAME                         = 'name';
    const FIELD_CONTACT_DECISION_MAKERS_ONLY = 'contact_decision_makers_only';
    const FIELD_CONTACT_ON_WEEKDAYS_ONLY     = 'contact_on_weekdays_only';
    const FIELD_USER_ID                      = 'user_id';
    const FIELD_GLOBAL                       = 'global';
    const FIELD_DOMAIN                       = 'domain';

    const RELATION_SCHEDULED_GROUPS = 'scheduledGroups';
    const RELATION_COMPANY_ROUTINES = 'companyRoutines';
    const RELATION_USER             = 'user';

    protected $table = self::TABLE;

    /**
     * @return HasMany
     */
    public function scheduledGroups(): HasMany
    {
        return $this->hasMany(CadenceScheduledGroup::class)
                    ->orderBy(CadenceScheduledGroup::FIELD_ORDINAL_VALUE)
                    ->orderBy(BaseModel::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function companyRoutines(): HasMany
    {
        return $this->hasMany(CompanyCadenceRoutine::class);
    }

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
