<?php

namespace App\Models\Cadence;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

/**
 *
 * Template used to create CompanyCadenceScheduledGroups
 *
 * @property int $cadence_routine_id
 * @property int|null $ordinal_value
 * @property int $execution_delay_minutes
 * @property string|null $execution_time_exact
 * @property string|null $execution_time_window_start
 * @property string|null $execution_time_window_end
 *
 * @property-read Collection $actions
 */
class CadenceScheduledGroup extends BaseModel
{
    const TABLE = 'cadence_scheduled_groups';

    const FIELD_CADENCE_ROUTINE_ID          = 'cadence_routine_id';
    const FIELD_ORDINAL_VALUE               = 'ordinal_value';
    const FIELD_EXECUTION_DELAY_MINUTES     = 'execution_delay_minutes';
    const FIELD_EXECUTION_TIME_EXACT        = 'execution_time_exact';
    const FIELD_EXECUTION_TIME_WINDOW_START = 'execution_time_window_start';
    const FIELD_EXECUTION_TIME_WINDOW_END   = 'execution_time_window_end';

    const RELATION_ACTIONS = 'actions';

    protected $table = self::TABLE;

    /**
     * @return HasMany
     */
    public function actions(): HasMany
    {
        return $this->hasMany(CadenceScheduledGroupAction::class);
    }
}
