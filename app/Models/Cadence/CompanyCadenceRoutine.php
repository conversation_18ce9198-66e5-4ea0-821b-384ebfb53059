<?php

namespace App\Models\Cadence;

use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;

/**
 * A company cadence routine schedules contact attempts to the specified company, triggered by
 * the conclusion of the previous contact attempt, or from the point that the routine was applied.
 * Templated by the CadenceRoutine class.
 *
 * @property int $cadence_routine_id
 * @property int $company_id
 * @property bool $contact_decision_makers_only
 * @property bool $contact_on_weekdays_only
 * @property string $status
 * @property string $domain
 * @property bool $use_account_manager
 * @property int $user_id
 *
 * @property-read Collection scheduledGroups
 * @property-read Company $company
 * @property-read CadenceRoutine $cadenceRoutine
 * @property-read User|null $user
 */
class CompanyCadenceRoutine extends BaseModel
{
    use SoftDeletes, HasFactory;

    const TABLE = 'company_cadence_routines';

    const FIELD_CADENCE_ROUTINE_ID           = 'cadence_routine_id';
    const FIELD_COMPANY_ID                   = 'company_id';
    const FIELD_CONTACT_DECISION_MAKERS_ONLY = 'contact_decision_makers_only';
    const FIELD_CONTACT_ON_WEEKDAYS_ONLY     = 'contact_on_weekdays_only';
    const FIELD_DELETED_AT                   = 'deleted_at';
    const FIELD_STATUS                       = 'status';
    const FIELD_DOMAIN                       = 'domain';
    const FIELD_USE_ACCOUNT_MANAGER          = 'use_account_manager';
    const FIELD_USER_ID                      = 'user_id';

    const STATUS_PENDING   = 'pending';
    const STATUS_CONCLUDED = 'concluded';

    const RELATION_SCHEDULED_GROUPS = 'scheduledGroups';
    const RELATION_COMPANY          = 'company';
    const RELATION_CADENCE_ROUTINE  = 'cadenceRoutine';

    protected $table = self::TABLE;

    protected static function boot()
    {
        parent::boot();
        static::deleting(function (self $routine) {
            // todo: terminate any pending associated jobs
        });
    }

    /**
     * @return HasMany
     */
    public function scheduledGroups(): HasMany
    {
        return $this->hasMany(CompanyCadenceScheduledGroup::class);
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * @return BelongsTo
     */
    public function cadenceRoutine(): BelongsTo
    {
        return $this->belongsTo(CadenceRoutine::class, self::FIELD_CADENCE_ROUTINE_ID, CadenceRoutine::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
