<?php

namespace App\Models\Cadence;

use App\Models\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property string $name
 * @property string $notes
 * @property int $user_id
 * @property bool $global
 * @property string $task_name
 *
 * @property-read User $user
 */
class CadenceTaskTemplate extends BaseModel
{
    use SoftDeletes;

    const TABLE = 'cadence_task_templates';

    const FIELD_NAME      = 'name';
    const FIELD_NOTES     = 'notes';
    const FIELD_USER_ID   = 'user_id';
    const FIELD_GLOBAL    = 'global';
    const FIELD_TASK_NAME = 'task_name';

    const RELATIONSHIP_USER = 'user';

    protected $table = self::TABLE;

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
