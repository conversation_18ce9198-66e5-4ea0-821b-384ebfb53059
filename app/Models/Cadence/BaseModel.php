<?php

namespace App\Models\Cadence;

use Carbon\Carbon;

/**
 * @property int $id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class BaseModel extends \App\Models\BaseModel
{
    const FIELD_ID = 'id';
    const FIELD_CREATED_AT = self::CREATED_AT;
    const FIELD_UPDATED_AT = self::UPDATED_AT;

    protected $guarded = [
        self::FIELD_ID,
        self::CREATED_AT,
        self::UPDATED_AT,
    ];
}
