<?php

namespace App\Models\Cadence;

/**
 * @property int $user_id
 * @property string $type
 * @property string $content
 * @property string $image_data
 */
class CadenceEmailHeaderFooter extends BaseModel
{
    const TABLE = 'cadence_email_headers_and_footers';

    protected $table = self::TABLE;

    const FIELD_USER_ID    = 'user_id';
    const FIELD_TYPE       = 'type';
    const FIELD_CONTENT    = 'content';
    const FIELD_IMAGE_DATA = 'image_data';

    const TYPE_HEADER = 'header';
    const TYPE_FOOTER = 'footer';
}
