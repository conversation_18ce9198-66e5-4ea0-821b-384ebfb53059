<?php

namespace App\Models\Cadence;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

/**
 * A schedulable group with child action to be executed.
 * Templated by CadenceScheduledGroup.
 *
 * @property int $company_cadence_routine_id
 * @property bool $skip
 * @property string $status
 * @property int|null $ordinal_value
 * @property int $execution_delay_minutes
 * @property string|null $execution_time_exact
 * @property string|null $execution_time_window_start
 * @property string|null $execution_time_window_end
 * @property array $resolution_notes
 * @property string|null $target_execution_timestamp
 * @property int $success_count
 *
 * @property-read Collection $actions
 * @property-read CompanyCadenceRoutine $routine
 */
class CompanyCadenceScheduledGroup extends BaseModel
{
    const TABLE = 'company_cadence_scheduled_groups';

    const FIELD_COMPANY_CADENCE_ROUTINE_ID  = 'company_cadence_routine_id';
    const FIELD_SKIP                        = 'skip';
    const FIELD_STATUS                      = 'status';
    const FIELD_ORDINAL_VALUE               = 'ordinal_value';
    const FIELD_EXECUTION_DELAY_MINUTES     = 'execution_delay_minutes';
    const FIELD_EXECUTION_TIME_EXACT        = 'execution_time_exact';
    const FIELD_EXECUTION_TIME_WINDOW_START = 'execution_time_window_start';
    const FIELD_EXECUTION_TIME_WINDOW_END   = 'execution_time_window_end';
    const FIELD_RESOLUTION_NOTES            = 'resolution_notes';
    const FIELD_TARGET_EXECUTION_TIMESTAMP  = 'target_execution_timestamp';
    const FIELD_SUCCESS_COUNT               = 'success_count';

    const STATUS_NOT_STARTED = 'not_started'; // Default status -- Hasn't yet been scheduled
    const STATUS_QUEUED      = 'queued'; // Group actions have been added to queue, waiting to execute once delay is fulfilled
    const STATUS_PENDING     = 'pending'; // One or more actions are pending manual intervention I.e. resolving a task
    const STATUS_CONCLUDED   = 'concluded'; // All actions have been concluded
    const STATUS_SKIPPED     = 'skipped'; // Manually skipped

    const SUCCESS_COUNT_KEY = 'success_count';

    const RELATION_ACTIONS = 'actions';
    const RELATION_ROUTINE = 'routine';

    protected $table = self::TABLE;

    protected $casts = [self::FIELD_RESOLUTION_NOTES => 'array'];

    /**
     * @return HasMany
     */
    public function actions(): HasMany
    {
        return $this->hasMany(CompanyCadenceScheduledGroupAction::class);
    }

    /**
     * @return BelongsTo
     */
    public function routine(): BelongsTo
    {
        return $this->belongsTo(CompanyCadenceRoutine::class, self::FIELD_COMPANY_CADENCE_ROUTINE_ID);
    }

    /**
     * @param string|null $executionTimeString
     * @return array
     */
    private function parseTime(?string $executionTimeString): array
    {
        return $executionTimeString ? array_map(function ($string) {
            return intval($string);
        }, explode(':', $executionTimeString)) : [null, null];
    }

    /**
     * @return array
     */
    public function parseStartTime(): array
    {
        return $this->parseTime($this->execution_time_window_start);
    }

    /**
     * @return array
     */
    public function parseEndTime(): array
    {
        return $this->parseTime($this->execution_time_window_end);
    }

    /**
     * @param string $note
     * @param array $data
     * @return void
     */
    public function addResolutionNote(string $note, array $data = []): void
    {
        if ($this->resolution_notes === null)
            $this->resolution_notes = [];
        $notes                  = $this->resolution_notes;
        $notes[]                = [
            'message' => $note,
            'data'    => $data
        ];
        $this->resolution_notes = $notes;
        $this->save();
    }

    /**
     * Group is considered successful if one action has at least one successful communication
     * If the group action is a task, it is always considered successful
     *
     * @return bool
     */
    public function isSuccessful(): bool
    {
        return $this->actions->first()->action_type === CompanyCadenceScheduledGroupAction::ACTION_TYPE_TASK || $this->success_count > 0;
    }
}
