<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property string $reference
 * @property string $code
 * @property string $sent_reference
 * @property Carbon $sent_at
 * @property Carbon $expires_at
 */
class SmsVerification extends Model
{
    const TABLE = 'sms_verifications';

    const ID = 'id';
    const REFERENCE = 'reference';
    const CODE = 'code';
    const SENT_REFERENCE = 'sent_reference';
    const SENT_AT = 'sent_at';
    const EXPIRES_AT = 'expires_at';

    protected $table = self::TABLE;
    protected $guarded = [self::ID];
}
