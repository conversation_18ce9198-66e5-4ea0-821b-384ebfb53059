<?php

namespace App\Models;

use App\Enums\EmailTemplateScope;
use App\Models\Odin\Industry;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

/**
 * @property int     $id
 * @property int     $owner_user_id
 * @property int     $industry_id
 * @property string  $name
 * @property string  $header
 * @property string  $footer
 * @property boolean personal
 * @property Carbon  $created_at
 * @property Carbon  $updated_at
 * @property Carbon  $deleted_at
 *
 * @property-read User                      $owner
 * @property-read Industry                  $industry
 * @property-read Collection<EmailTemplate> $emailTemplates
 */
class EmailTemplateBackground extends Model
{
    use HasFactory;
    use SoftDeletes;

    const TABLE = 'email_template_backgrounds';

    const FIELD_ID = 'id';
    const FIELD_OWNER_USER_ID = 'owner_user_id';
    const FIELD_INDUSTRY_ID = 'industry_id';
    const FIELD_NAME = 'name';
    const FIELD_HEADER = 'header';
    const FIELD_FOOTER = 'footer';
    const FIELD_PERSONAL = 'personal';
    const FIELD_DELETED_AT = 'deleted_at';
    const FIELD_CREATED_AT = 'created_at';
    const FIELD_UPDATED_AT = 'updated_at';

    const RELATION_OWNER           = 'owner';
    const RELATION_INDUSTRY        = 'industry';
    const RELATION_EMAIL_TEMPLATES = 'emailTemplates';

    protected $table = self::TABLE;

    protected $fillable = [
        self::FIELD_OWNER_USER_ID,
        self::FIELD_NAME,
        self::FIELD_HEADER,
        self::FIELD_FOOTER,
        self::FIELD_PERSONAL,
        self::FIELD_INDUSTRY_ID,
        self::FIELD_CREATED_AT,
        self::FIELD_UPDATED_AT
    ];

    /**
     * @return BelongsTo
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_OWNER_USER_ID, User::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function industry(): BelongsTo
    {
        return $this->belongsTo(Industry::class, self::FIELD_INDUSTRY_ID, Industry::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function emailTemplates(): HasMany
    {
        return $this->hasMany(EmailTemplate::class, self::FIELD_ID, EmailTemplate::FIELD_BACKGROUND_ID);
    }

    public function scopeByScope($query, ?EmailTemplateScope $scope)
    {
        if ($scope === EmailTemplateScope::PERSONAL) {
            return $query->where(EmailTemplateBackground::FIELD_OWNER_USER_ID, Auth::id())
                ->where(EmailTemplateBackground::FIELD_PERSONAL, true);
        } else {
            return $query->where(function($query) {
                $query->where(EmailTemplateBackground::FIELD_OWNER_USER_ID, Auth::id())
                    ->orWhere(EmailTemplateBackground::FIELD_PERSONAL, false);
            });
        }
    }

}
