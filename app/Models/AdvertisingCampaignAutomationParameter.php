<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AdvertisingCampaignAutomationParameter extends Model
{
    const TABLE = 'advertising_campaign_automation_parameters';

    const FIELD_ID = 'id';
    const FIELD_PLATFORM = 'platform';
    const FIELD_PLATFORM_ACCOUNT_ID = 'platform_account_id';
    const FIELD_PLATFORM_CAMPAIGN_ID = 'platform_campaign_id';
    const FIELD_TYPE = 'type';
    const FIELD_STRATEGY = 'strategy';
    const FIELD_PARAMETER = 'parameter';
    const FIELD_OPERATOR = 'operator';
    const FIELD_THRESHOLD = 'threshold';
    const FIELD_THRESHOLD_TYPE = 'threshold_type';

    const PARAMETER_ZIPCODES_WITH_REVENUE = 'zipcodes_with_revenue';
    const PARAMETER_ZIPCODE_HAS_REVENUE = 'zipcode_has_revenue';
    const PARAMETER_COMPANIES_IN_COUNTY = 'companies_in_county';
    const PARAMETER_UNLIMITED_BUDGETS_AMOUNT = 'unlimited_budgets_amount';

    const OPERATOR_LESS_THAN = '<';
    const OPERATOR_LESS_THAN_EQUAL = '<=';
    const OPERATOR_EQUAL = '=';
    const OPERATOR_GREATER_THAN_EQUAL = '>=';
    const OPERATOR_GREATER_THAN = '>';
    const OPERATOR_NOT_EQUAL = '!=';

    const OPERATORS = [
        self::OPERATOR_LESS_THAN,
        self::OPERATOR_LESS_THAN_EQUAL,
        self::OPERATOR_EQUAL,
        self::OPERATOR_GREATER_THAN_EQUAL,
        self::OPERATOR_GREATER_THAN,
        self::OPERATOR_NOT_EQUAL
    ];

    const THRESHOLD_TYPE_AMOUNT = 'amount';
    const THRESHOLD_TYPE_PERCENT = 'percent';
    const THRESHOLD_TYPE_AVAILABLE_COMPANIES = 'available_companies';
    const THRESHOLD_TYPE_DOLLARS = 'dollars';

    const AUTOMATION_TYPE_LOCATION = 'location';
    const AUTOMATION_TYPE_BUDGET = 'budget';

    const AUTOMATION_STRATEGY_COUNTY = 'county';
    const AUTOMATION_STRATEGY_ZIPCODE = 'zipcode';

    //Type -> Strategy -> Parameter -> Threshold
    const PARAMETER_OPTIONS = [
        self::AUTOMATION_TYPE_LOCATION => [
            self::AUTOMATION_STRATEGY_COUNTY => [
                self::PARAMETER_ZIPCODES_WITH_REVENUE => [
                    self::THRESHOLD_TYPE_AMOUNT,
                    self::THRESHOLD_TYPE_PERCENT
                ],
                self::PARAMETER_COMPANIES_IN_COUNTY => [
                    self::THRESHOLD_TYPE_AVAILABLE_COMPANIES,
                ]
            ],
            self::AUTOMATION_STRATEGY_ZIPCODE => [
                self::PARAMETER_ZIPCODE_HAS_REVENUE => [
                    self::THRESHOLD_TYPE_DOLLARS,
                    self::THRESHOLD_TYPE_AVAILABLE_COMPANIES
                ]
            ]
        ],
        self::AUTOMATION_TYPE_BUDGET => [
            self::AUTOMATION_STRATEGY_COUNTY => [
                self::PARAMETER_UNLIMITED_BUDGETS_AMOUNT => [
                    self::THRESHOLD_TYPE_AMOUNT
                ]
            ]
        ]
    ];

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $table = self::TABLE;
}
