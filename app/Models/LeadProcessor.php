<?php

namespace App\Models;

use App\Repositories\Odin\ProductProcessing\ProductProcessingRepository;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;

/**
 * @property int                                           $id
 * @property int                                           $user_id
 * @property int                                           $lead_processing_team_id
 * @property Carbon                                        $created_at
 * @property Carbon                                        $updated_at
 * @property Carbon                                        $deleted_at
 *
 * @property-read User                                     $user
 * @property-read LeadProcessingTeam                       $team
 * @property-read Collection|LeadProcessorNotification[]   $leadProcessorNotifications
 * @property-read Collection|Phone[]                       $phones
 * @property-read Collection|LeadProcessingCommunication[] $leadProcessingCommunications
 */
class LeadProcessor extends BaseModel
{
    use SoftDeletes, HasFactory;

    const TABLE = 'lead_processors';

    const FIELD_ID                      = 'id';
    const FIELD_USER_ID                 = 'user_id';
    const FIELD_LEAD_PROCESSING_TEAM_ID = 'lead_processing_team_id';
    const FIELD_CREATED_AT              = 'created_at';
    const FIELD_UPDATED_AT              = 'updated_at';
    const FIELD_DELETED_AT              = 'deleted_at';

    const RELATION_USER                           = 'user';
    const RELATION_LEAD_PROCESSING_TEAM           = 'team';
    const RELATION_PHONES                         = 'phones';
    const RELATION_LEAD_PROCESSING_COMMUNICATIONS = 'leadProcessingCommunications';
    const RELATION_LEAD_PROCESSING_NOTIFICATIONS  = 'leadProcessingNotifications';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * Returns 'system' processor, to be used for automated functionality
     *
     * @return LeadProcessor
     */
    public static function systemProcessor(): self
    {
        return app(ProductProcessingRepository::class)->getSystemProcessor();
    }

    /**
     * Defines the belongs to relationship to the user table.
     *
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }

    /**
     * Defines the belongs to relationship to the lead processing team table.
     *
     * @return BelongsTo
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(LeadProcessingTeam::class, self::FIELD_LEAD_PROCESSING_TEAM_ID, LeadProcessingTeam::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function leadProcessorNotifications(): HasMany
    {
        return $this->hasMany(LeadProcessorNotification::class, LeadProcessorNotification::FIELD_LEAD_PROCESSOR_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function leadProcessingCommunications(): HasMany
    {
        return $this->hasMany(LeadProcessingCommunication::class, LeadProcessingCommunication::FIELD_LEAD_PROCESSOR_ID, self::FIELD_ID);
    }

    /**
     * @return Attribute
     */
    public function phones(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->user->phones
        );
    }
}
