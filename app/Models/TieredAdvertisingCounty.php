<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TieredAdvertisingCounty extends Model
{
    const string TABLE = 'tiered_advertising_counties';

    const string FIELD_ID = 'id';
    const string FIELD_INDUSTRY_ID = 'industry_id';
    const string FIELD_LOCATION_ID = 'location_id';
    const string FIELD_PLATFORM = 'platform';
    const string FIELD_TIERED_ADVERTISING_CAMPAIGN_ID = 'tiered_advertising_campaign_id';
    const string FIELD_TIER = 'tier';
    const string FIELD_TCPA_BID = 'tcpa_bid';
    const string FIELD_NEGATIVE_ZIP_CODES = 'negative_zip_codes';
    const string FIELD_DATA = 'data';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $casts = [
        self::FIELD_DATA => 'array',
        self::FIELD_NEGATIVE_ZIP_CODES => 'array',
    ];
}
