<?php

namespace App\Models;

use App\Enums\NotificationLinkType;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $user_id
 * @property int $from_id
 * @property string $subject
 * @property string $body
 * @property int $type
 * @property string $link
 * @property NotificationLinkType $link_type
 * @property bool $read
 * @property array $payload
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class Notification extends BaseModel
{
    const TABLE = 'notifications';

    const FIELD_ID        = 'id';
    const FIELD_USER_ID   = 'user_id';
    const FIELD_FROM_ID   = 'from_id';
    const FIELD_SUBJECT   = 'subject';
    const FIELD_BODY      = 'body';
    const FIELD_TYPE      = 'type';
    const FIELD_READ      = 'read';
    const FIELD_LINK      = 'link';
    const FIELD_LINK_TYPE = 'link_type';
    const FIELD_PAYLOAD   = 'payload';

    const RELATION_USER = 'user';

    const READ   = 1;
    const UNREAD = 0;

    const IDENTITY_TYPE_MAPPING = ['lead' => self::TYPE_LEAD, 'company' => self::TYPE_COMPANY];

    const TYPE_DEFAULT = 0;
    const TYPE_LEGACY  = 1;
    const TYPE_ACTIONS = 2;
    const TYPE_LEAD    = 3;
    const TYPE_COMPANY = 4;

    const FROM_SYSTEM = 0;

    protected $guarded = [self::FIELD_ID];

    protected $table = self::TABLE;

    protected $casts = [
        self::FIELD_LINK_TYPE => NotificationLinkType::class,
        self::FIELD_PAYLOAD   => 'array'
    ];

    /**
     * Defines relationship to the user.
     *
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }
}
