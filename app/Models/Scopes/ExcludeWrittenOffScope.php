<?php

namespace App\Models\Scopes;

use App\Enums\Billing\InvoiceStates;
use App\Models\Billing\Invoice;
use App\Services\DatabaseHelperService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class ExcludeWrittenOffScope implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     */
    public function apply(Builder $builder, Model $model): void
    {
        $builder->whereNot(DatabaseHelperService::database() . '.' . Invoice::TABLE . '.' . Invoice::FIELD_STATUS, InvoiceStates::WRITTEN_OFF->value);
    }

    /**
     * @param Builder $builder
     * @return void
     */
    public function extend(Builder $builder): void
    {
        $builder->macro('withWrittenOff', function (Builder $builder) {
            return $builder->withoutGlobalScope($this);
        });
    }
}
