<?php

namespace App\Models\Prospects;

use App\Enums\Prospects\CloserDemoResolution;
use App\Enums\Prospects\CloserDemoStatus;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $user_id
 * @property int $new_buyer_prospect_id
 * @property ?int $company_id
 * @property ?Carbon $demo_at
 * @property string $calendly_event_url
 * @property ?array $calendly_payload
 * @property CloserDemoStatus $status
 * @property ?CloserDemoResolution $resolution
 * @property bool $upcoming_notification_sent
 * @property bool $follow_up_notification_sent
 *
 * @property-read NewBuyerProspect $prospect
 */
class CloserDemo extends Model
{
    use HasFactory;

    const string TABLE = 'closer_demos';

    const string FIELD_ID                          = 'id';
    const string FIELD_USER_ID                     = 'user_id';
    const string FIELD_NEW_BUYER_PROSPECT_ID       = 'new_buyer_prospect_id';
    const string FIELD_COMPANY_ID                  = 'company_id';
    const string FIELD_DEMO_AT                     = 'demo_at';
    const string FIELD_CALENDLY_EVENT_URL          = 'calendly_event_url';
    const string FIELD_CALENDLY_PAYLOAD            = 'calendly_payload';
    const string FIELD_STATUS                      = 'status';
    const string FIELD_RESOLUTION                  = 'resolution';
    const string FIELD_UPCOMING_NOTIFICATION_SENT  = 'upcoming_notification_sent';
    const string FIELD_FOLLOW_UP_NOTIFICATION_SENT = 'follow_up_notification_sent';

    protected $table   = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts   = [
        self::FIELD_DEMO_AT          => 'datetime',
        self::FIELD_CALENDLY_PAYLOAD => 'array',
    ];

    /**
     * @return BelongsTo
     */
    public function prospect(): BelongsTo
    {
        return $this->belongsTo(NewBuyerProspect::class, self::FIELD_NEW_BUYER_PROSPECT_ID);
    }

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
