<?php

namespace App\Models\Prospects;

use Database\Factories\Prospects\ContactFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Contact extends Model
{
    /** @use HasFactory<ContactFactory> */
    use HasFactory;

    const string TABLE = 'prospect_contacts';

    const string FIELD_ID           = 'id';
    const string FIELD_FIRST_NAME   = 'first_name';
    const string FIELD_LAST_NAME    = 'last_name';
    const string FIELD_EMAIL        = 'email';
    const string FIELD_TITLE        = 'title';
    const string FIELD_DEPARTMENT   = 'department';
    const string FIELD_CELL_PHONE   = 'cell_phone';
    const string FIELD_OFFICE_PHONE = 'office_phone';
    const string FIELD_PROSPECT_ID  = 'prospect_id';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    public function prospect()
    {
        return $this->belongsTo(NewBuyerProspect::class);
    }
}
