<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;


/**
 * @property int $id
 * @property string $name
 *
 * @property-read Action[] $actions
 */
class ActionCategory extends Model
{
    const FIELD_ID          = 'id';
    const FIELD_NAME        = 'name';

    const RELATION_ACTIONS  = 'actions';

    const TABLE = 'action_categories';

    protected $guarded = [ self::FIELD_ID ];

    /**
     * @return HasMany
     */
    public function actions(): HasMany
    {
        return $this->hasMany(Action::class, Action::FIELD_CATEGORY_ID, self::FIELD_ID);
    }

}
