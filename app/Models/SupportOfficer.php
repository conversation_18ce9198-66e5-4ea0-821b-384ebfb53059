<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property int $user_id
 * @property boolean $include_in_round_robin
 *
 * @property-read User $user
 */
class SupportOfficer extends Model
{
    use SoftDeletes, HasFactory;

    const TABLE = 'support_officers';

    const FIELD_ID                      = 'id';
    const FIELD_USER_ID                 = 'user_id';
    const FIELD_INCLUDED_IN_ROUND_ROBIN = 'include_in_round_robin';
    const FIELD_DELETED_AT              = 'deleted_at';

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
