<?php

namespace App\Models;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\PropertyType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class CompanyCampaignPropertyType extends Model
{
    use HasFactory;

    const TABLE = 'company_campaign_property_types';

    const FIELD_ID = 'id';
    const FIELD_COMPANY_CAMPAIGN_ID = 'company_campaign_id';
    const FIELD_PROPERTY_TYPE_ID = 'property_type_id';

    protected $table = self::TABLE;
    protected $guarded = [
        self::FIELD_ID
    ];

    const RELATION_PROPERTY_TYPE = 'propertyType';
    const RELATION_COMPANY_CAMPAIGN = 'companyCampaign';

    /**
     * @return HasOne
     */
    public function propertyType(): HasOne
    {
        return $this->hasOne(PropertyType::class, PropertyType::FIELD_ID, self::FIELD_PROPERTY_TYPE_ID);
    }

    /**
     * @return BelongsTo
     */
    public function companyCampaign(): BelongsTo
    {
        return $this->belongsTo(CompanyCampaign::class, self::FIELD_COMPANY_CAMPAIGN_ID, CompanyCampaign::FIELD_ID);
    }
}
