<?php

namespace App\Models;

use App\Models\Legacy\EloquentCompany;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $company_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read EloquentCompany|null $company
 */
class SalesBaitRestrictedCompany extends BaseModel
{
    use HasFactory;

    const FIELD_ID         = 'id';
    const FIELD_COMPANY_ID = 'company_id';

    const TABLE = 'sales_bait_restricted_companies';

    const RELATION_COMPANY = 'company';

    protected $table   = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * Defines the relationship to the companies table.
     *
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(EloquentCompany::class, self::FIELD_COMPANY_ID, EloquentCompany::COMPANY_ID);
    }
}
