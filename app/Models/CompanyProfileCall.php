<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $company_id
 * @property string $sid
 * @property string $to
 * @property string $from
 * @property string $proxy_phone
 * @property array|null $content
 */
class CompanyProfileCall extends Model
{
    const string TABLE = 'company_profile_calls';

    const string FIELD_ID = 'id';
    const string FIELD_COMPANY_ID = 'company_id';
    const string FIELD_SID = 'sid';
    const string FIELD_TO = 'to';
    const string FIELD_FROM = 'from';
    const string FIELD_PROXY_PHONE = 'proxy_phone';
    const string FIELD_CONTENT = 'content';

    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];
    protected $casts = [self::FIELD_CONTENT => 'array'];
}
