<?php

namespace App\Models;

use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductCampaign;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

/**
 * @property int $id
 * @property int $consumer_product_id
 * @property int $sale_type_id
 * @property int $product_campaign_id
 * @property string $message
 */
class BestRevenueScenarioLog extends Model
{
    use HasFactory;

    const TABLE = 'best_revenue_scenario_logs';

    const FIELD_ID = 'id';
    const FIELD_RUN_ID = 'run_id';
    const FIELD_CONSUMER_PRODUCT_ID = 'consumer_product_id';
    const FIELD_SALE_TYPE_ID = 'sale_type_id';
    const FIELD_PRODUCT_CAMPAIGN_ID = 'product_campaign_id';
    const FIELD_STEP = 'step';
    const FIELD_MESSAGE = 'message';

    const MESSAGE_NO_POTENTIAL_CAMPAIGNS_FOUND = 0;
    const MESSAGE_POTENTIAL_CAMPAIGNS_FOUND = 1;
    const MESSAGE_REMOVED_BY_PRICING_STEP = 2;
    const MESSAGE_REMOVED_BY_OVER_BUDGET_FILTER_STEP = 3;
    const MESSAGE_REMOVED_BY_BRS_SORTING_STEP = 4;
    const MESSAGE_NOT_CHOSEN_PRICE = 5;
    const MESSAGE_NOT_CHOSEN_BUDGET_USAGE = 6;
    const MESSAGE_NOT_CHOSEN_REJECTION_PERCENTAGE = 7;
    const MESSAGE_NOT_CHOSEN_P_BU = 8;
    const MESSAGE_NOT_CHOSEN_BU_RP = 9;
    const MESSAGE_NOT_CHOSEN_P_RP = 10;
    const MESSAGE_NOT_CHOSEN_P_BU_RP = 11;
    const MESSAGE_NOT_CHOSEN_RANDOM = 12;
    const MESSAGE_BEST_REVENUE_SCENARIO_RESULT = 13;
    const MESSAGE_REMOVED_BY_SCHEDULING_STEP = 14;

    const MESSAGES = [
        self::MESSAGE_NO_POTENTIAL_CAMPAIGNS_FOUND => "No potential campaigns found",
        self::MESSAGE_POTENTIAL_CAMPAIGNS_FOUND => "Potential campaigns found",
        self::MESSAGE_REMOVED_BY_PRICING_STEP => "Removed because of pricing",
        self::MESSAGE_REMOVED_BY_OVER_BUDGET_FILTER_STEP => "Removed because over budget or no active budget",
        self::MESSAGE_REMOVED_BY_BRS_SORTING_STEP => "Removed during revenue scenario sorting",
        self::MESSAGE_NOT_CHOSEN_PRICE => "Price was lower",
        self::MESSAGE_NOT_CHOSEN_BUDGET_USAGE => "Budget usage was higher",
        self::MESSAGE_NOT_CHOSEN_REJECTION_PERCENTAGE => "Rejection percentage was higher",
        self::MESSAGE_NOT_CHOSEN_P_BU => "Price was lower and budget usage was higher",
        self::MESSAGE_NOT_CHOSEN_BU_RP => "Both budget usage and rejection percentage were higher",
        self::MESSAGE_NOT_CHOSEN_P_RP => "Price was lower and rejection percentage was higher",
        self::MESSAGE_NOT_CHOSEN_P_BU_RP => "Price was lower, and both rejection percentage and budget usage were higher",
        self::MESSAGE_NOT_CHOSEN_RANDOM => "Price, budget usage, and rejection percentage were all equal - luck of the draw",
        self::MESSAGE_BEST_REVENUE_SCENARIO_RESULT => "Best revenue scenario result",
        self::MESSAGE_REMOVED_BY_SCHEDULING_STEP => "Removed because no available calendars"
    ];

    const RELATION_PRODUCT_CAMPAIGN = 'productCampaign';
    const RELATION_CONSUMER_PRODUCT = 'consumerProduct';
    const RELATION_COMPANY = 'company';

    protected $table = self::TABLE;
    protected $guarded = [
        self::FIELD_ID
    ];

    /**
     * @return BelongsTo
     */
    public function productCampaign(): BelongsTo
    {
        return $this->belongsTo(ProductCampaign::class, self::FIELD_PRODUCT_CAMPAIGN_ID, ProductCampaign::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function consumerProduct(): BelongsTo
    {
        return $this->belongsTo(ConsumerProduct::class, self::FIELD_CONSUMER_PRODUCT_ID, ConsumerProduct::FIELD_ID);
    }

    /**
     * @return HasOneThrough
     */
    public function company(): HasOneThrough
    {
        return $this->hasOneThrough(
            Company::class,
            ProductCampaign::class,
            ProductCampaign::FIELD_ID,
            Company::FIELD_ID,
            self::FIELD_PRODUCT_CAMPAIGN_ID,
            ProductCampaign::FIELD_COMPANY_ID
        );
    }
}
