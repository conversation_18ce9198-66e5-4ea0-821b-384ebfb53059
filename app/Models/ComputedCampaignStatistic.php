<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int|null $legacy_campaign_id
 * @property int $product_campaign_id
 * @property double $budget_usage
 */
class ComputedCampaignStatistic extends Model
{
    use HasFactory;

    const FIELD_BUDGET_USAGE        = 'budget_usage';
    const FIELD_ID                  = 'id';
    const FIELD_LEGACY_COMPANY_ID   = 'legacy_company_id';
    const FIELD_PRODUCT_CAMPAIGN_ID = 'product_campaign_id';
    const TABLE                     = 'computed_rejection_statistics';
    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];
}
