<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property string $name
 * @property string $key
 * @property bool $default_type
 * @property int $sale_limit
 */
class SaleType extends Model
{
    use HasFactory;

    const TABLE = 'sale_types';

    const FIELD_ID              = 'id';
    const FIELD_NAME            = 'name';
    const FIELD_KEY             = 'key';
    const FIELD_DEFAULT_TYPE    = 'default_type';
    const FIELD_SALE_LIMIT      = 'sale_limit';

    const EXCLUSIVE_KEY = 'exclusive';
    const DUO_KEY       = 'duo';
    const TRIO_KEY      = 'trio';
    const QUAD_KEY      = 'quad';
    const EMAIL_ONLY_KEY = 'email_only';
    const UNVERIFIED_KEY = 'unverified';

    const DISABLED_LEAD_SALES_TYPES = [
        self::EMAIL_ONLY_KEY,
    ];

    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    /**
     * @param string|null $mapToColumn
     * @return Collection
     */
    public static function getIdMap(?string $mapToColumn = self::FIELD_KEY): Collection
    {
        return SaleType::all()
            ->mapWithKeys(fn(SaleType $saleType) => [
                $saleType->{$mapToColumn} => $saleType->id,
            ]);
    }
}
