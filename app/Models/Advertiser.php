<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Advertiser extends Model
{
    use HasFactory;

    const TABLE = 'advertisers';

    const FIELD_ID = 'id';
    const FIELD_NAME = 'name';
    const FIELD_KEY = 'key';

    const RELATION_ADVERTISING_ACCOUNTS = 'advertisingAccounts';

    protected $table = self::TABLE;
    protected $guarded = [
        self::FIELD_ID
    ];

    public function advertisingAccounts(): HasMany
    {
        return $this->hasMany(AdvertisingAccount::class, AdvertisingAccount::FIELD_ADVERTISER_ID, self::FIELD_ID);
    }
}
