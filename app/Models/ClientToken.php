<?php

namespace App\Models;

use Carbon\Carbon;
use \Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $service_id
 * @property string $client_token
 * @property string $refresh_token
 * @property int $expires_in
 * @property array $additional_data
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read ClientTokenService $clientTokenService
 */
class ClientToken extends BaseModel
{
    const TABLE = 'client_tokens';

    const FIELD_ID = 'id';
    const FIELD_SERVICE_ID = 'service_id';
    const FIELD_CLIENT_TOKEN = 'client_token';
    const FIELD_REFRESH_TOKEN = 'refresh_token';
    const FIELD_EXPIRES_IN = 'expires_in';
    const FIELD_ADDITIONAL_DATA = 'additional_data';
    const FIELD_CREATED_AT = 'created_at';
    const FIELD_UPDATED_AT = 'updated_at';

    const RELATION_CLIENT_TOKEN_SERVICE = 'clientTokenService';

    const ADDITIONAL_DATA_ADVERTISER = 'advertiser';

    protected $table = self::TABLE;
    protected $guarded = [
        self::FIELD_ID
    ];

    protected $casts = [
        self::FIELD_ADDITIONAL_DATA => 'array'
    ];

    /**
     * @return BelongsTo
     */
    public function clientTokenService(): BelongsTo
    {
        return $this->belongsTo(ClientTokenService::class, self::FIELD_SERVICE_ID, ClientTokenService::FIELD_ID);
    }
}
