<?php

namespace App\Models;

use App\Models\Legacy\Location;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $location_id
 * @property string $key
 * @property string $name
 * @property string $type
 *
 * @property-read Location $location
 */
class MetaAdsLocation extends Model
{
    const TABLE = 'meta_ads_locations';

    const FIELD_ID = 'id';
    const FIELD_LOCATION_ID = 'location_id';
    const FIELD_KEY = 'key';
    const FIELD_NAME = 'name';
    const FIELD_TYPE = 'type';

    const TYPE_COUNTRY = 'country';
    const TYPE_REGION = 'region'; //State
    const TYPE_MEDIUM_GEO_AREA = 'medium_geo_area'; //County
    const TYPE_ZIP = 'zip';
    const TYPE_CITY = 'city';

    const RELATION_LOCATION = 'location';

    protected $table = self::TABLE;
    protected $guarded = [
        self::FIELD_ID
    ];

    /**
     * @return BelongsTo
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class, self::FIELD_LOCATION_ID, Location::ID);
    }
}
