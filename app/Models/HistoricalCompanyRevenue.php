<?php

namespace App\Models;

use App\Database\Casts\AsHistoricalCompanyDailyRevenue;
use App\Database\Casts\AsHistoricalCompanyMonthlyRevenue;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HistoricalCompanyRevenue extends BaseModel
{
    use HasFactory;

    const TABLE = 'historical_company_revenue';

    const FIELD_ID                           = 'id';
    const FIELD_COMPANY_ID                   = 'company_id';
    const FIELD_YEAR                         = 'year';
    const FIELD_DAILY_REVENUE                = 'daily_revenue';
    const FIELD_MONTHLY_REVENUE              = 'monthly_revenue';
    const FIELD_YEARLY_REVENUE               = 'yearly_revenue';
    const FIELD_CREATED_AT                   = 'created_at';
    const FIELD_UPDATED_AT                   = 'updated_at';

    protected $table   = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_DAILY_REVENUE => AsHistoricalCompanyDailyRevenue::class,
        self::FIELD_MONTHLY_REVENUE => AsHistoricalCompanyMonthlyRevenue::class
    ];
}
