<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\AvailableCompanyByLocation
 * @property int $id
 * @property int $company_id
 * @property ?int $legacy_company_id
 * @property int $location_id
 * @property int $county_location_id
 * @property string $industry_slug
 * @property int $unlimited_budget
 */
class AvailableCompanyByLocation extends Model
{
    use HasFactory;

    const string TABLE = 'available_company_by_locations';

    const string FIELD_ID                 = 'id';
    const string FIELD_COMPANY_ID         = 'company_id';
    const string FIELD_LEGACY_COMPANY_ID  = 'legacy_company_id';
    const string FIELD_LOCATION_ID        = 'location_id';
    const string FIELD_COUNTY_LOCATION_ID = 'county_location_id';
    const string FIELD_INDUSTRY_SLUG      = 'industry_slug';
    const string FIELD_UNLIMITED_BUDGETS  = 'unlimited_budgets';
    const string FIELD_CREATED_AT         = 'created_at';
    const string FIELD_UPDATED_AT         = 'updated_at';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];
}
