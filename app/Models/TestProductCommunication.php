<?php

namespace App\Models;

use App\Enums\TestProductCommunicationType;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $test_product_id
 * @property TestProductCommunicationType $communication_type
 * @property string $from
 * @property string $to
 * @property string|null $content
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property int $fraud_score;
 *
 * @property-read TestProduct $testProduct
 */
class TestProductCommunication extends BaseModel
{
    const TABLE = 'test_product_communications';

    const FIELD_ID                 = 'id';
    const FIELD_TEST_PRODUCT_ID    = 'test_product_id';
    const FIELD_COMMUNICATION_TYPE = 'communication_type';
    const FIELD_FROM               = 'from';
    const FIELD_TO                 = 'to';
    const FIELD_CONTENT            = 'content';
    const FIELD_CREATED_AT         = 'created_at';

    const RELATION_TEST_PRODUCT = 'testProduct';

    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];
    protected $casts   = [
        self::FIELD_COMMUNICATION_TYPE => TestProductCommunicationType::class,
        self::FIELD_CONTENT => 'array'
    ];

    /**
     * Defines relationship to test product.
     *
     * @return BelongsTo
     */
    public function testProduct(): BelongsTo
    {
        return $this->belongsTo(TestProduct::class, self::FIELD_TEST_PRODUCT_ID, TestProduct::FIELD_ID);
    }
}
