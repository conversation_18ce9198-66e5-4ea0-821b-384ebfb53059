<?php

namespace App\Models;

use App\Models\Campaigns\CompanyCampaign;
use Database\Factories\Odin\CompanyCampaignDataFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property int $campaign_id
 * @property array $payload
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read CompanyCampaign $campaign
 */
class CompanyCampaignData extends BaseModel
{
    use HasFactory;

    const string TABLE = 'company_campaign_data';

    const string FIELD_ID          = 'id';
    const string FIELD_CAMPAIGN_ID = 'campaign_id';
    const string FIELD_PAYLOAD     = 'payload';
    const string FIELD_CREATED_AT  = 'created_at';
    const string FIELD_UPDATED_AT  = 'updated_at';

    const string RELATION_CAMPAIGN = 'campaign';

    protected $casts = [
        self::FIELD_PAYLOAD    => 'array',
        self::FIELD_CREATED_AT => 'datetime',
        self::FIELD_UPDATED_AT => 'datetime',
    ];

    protected $fillable = [
        self::FIELD_CAMPAIGN_ID,
        self::FIELD_PAYLOAD
    ];

    protected static function newFactory(): CompanyCampaignDataFactory
    {
        return CompanyCampaignDataFactory::new();
    }

    /**
     * @return BelongsTo
     */
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(CompanyCampaign::class, self::FIELD_CAMPAIGN_ID, CompanyCampaign::FIELD_ID);
    }
}
