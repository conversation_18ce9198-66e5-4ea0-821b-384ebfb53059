<?php

namespace App\Models;

use App\Models\Legacy\Location;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class DmaLocation
 *
 * @property int $id
 * @property int $dma_id
 * @property int $location_id
 * @property string $state_key
 * @property string $state_fips
 * @property string $county_name
 * @property string $county_fips
 *
 * @property-read Dma $dma
 * @property-read Location $location
 */
class DmaLocation extends BaseModel
{
    const string TABLE = 'dma_locations';

    const string FIELD_ID           = 'id';
    const string FIELD_DMA_ID       = 'dma_id';
    const string FIELD_LOCATION_ID  = 'location_id';
    const string FIELD_STATE_KEY    = 'state_key';
    const string FIELD_STATE_FIPS   = 'state_fips';
    const string FIELD_COUNTY_NAME  = 'county_name';
    const string FIELD_COUNTY_FIPS  = 'county_fips';

    const string RELATION_DMA       = 'dma';
    const string RELATION_LOCATION  = 'location';

    protected $table    = self::TABLE;
    protected $guarded  = [self::FIELD_ID];
    public $timestamps  = false;

    /**
     * @return BelongsTo
     */
    public function dma(): BelongsTo
    {
        return $this->belongsTo(Dma::class, self::FIELD_DMA_ID, Dma::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class, self::FIELD_LOCATION_ID, Location::ID);
    }
}
