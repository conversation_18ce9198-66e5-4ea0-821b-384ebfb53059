<?php

namespace App\Models;

use App\Models\Legacy\EloquentQuote;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @property int                        $id
 * @property int                        $lead_id
 * @property string                     $type
 * @property int                        $relation_id
 * @property Carbon                     $created_at
 * @property Carbon                     $updated_at
 *
 * @property-read EloquentQuote         $lead
 * @property-read LeadProcessor         $leadProcessor
 * @property-read LeadProcessingHistory $leadProcessingHistory
 * @property-read Text                  $text
 * @property-read Call                  $call
 */
class Communication extends BaseModel
{
    const TABLE = 'communications';

    const FIELD_ID                = 'id';
    const FIELD_TYPE              = 'type';
    const FIELD_RELATION_ID       = 'relation_id';

    const TYPE_SMS    = 'sms';
    const TYPE_CALL   = 'call';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return HasOne
     */
    public function call(): HasOne
    {
        return $this->hasOne(Call::class, Call::FIELD_ID, self::FIELD_RELATION_ID);
    }

    /**
     * @return HasOne
     */
    public function text(): HasOne
    {
        return $this->hasOne(Text::class, Text::FIELD_ID, self::FIELD_RELATION_ID);
    }
}
