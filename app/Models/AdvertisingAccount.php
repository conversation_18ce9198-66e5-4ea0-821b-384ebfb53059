<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property string $platform
 * @property int $advertiser_id
 * @property string $platform_account_id
 * @property string $name
 * @property string $industry
 * @property boolean $tracks_conversions
 * @property integer $upload_conversions_interval_hours
 * @property integer $upload_conversions_last_run_timestamp
 */
class AdvertisingAccount extends Model
{
    use HasFactory;

    const TABLE = 'advertising_accounts';

    const FIELD_ID = 'id';
    const FIELD_PLATFORM = 'platform';
    const FIELD_ADVERTISER_ID = 'advertiser_id';
    const FIELD_PLATFORM_ACCOUNT_ID = 'platform_account_id';
    const FIELD_NAME = 'name';
    const FIELD_INDUSTRY = 'industry';
    const FIELD_TRACKS_CONVERSIONS = 'tracks_conversions';
    const FIELD_UPLOAD_CONVERSIONS_INTERVAL_HOURS = 'upload_conversions_interval_hours';
    const FIELD_UPLOAD_CONVERSIONS_LAST_RUN_TIMESTAMP = 'upload_conversions_last_run_timestamp';

    const INDUSTRY_SOLAR   = 'solar';
    const INDUSTRY_ROOFING = 'roofing';

    const RELATION_ADVERTISER = 'advertiser';

    protected $table = self::TABLE;
    protected $guarded = [
        self::FIELD_ID
    ];

    public function advertiser(): BelongsTo
    {
        return $this->belongsTo(Advertiser::class, self::FIELD_ADVERTISER_ID, Advertiser::FIELD_ID);
    }
}
