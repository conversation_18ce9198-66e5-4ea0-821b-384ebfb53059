<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property string $platform
 * @property string $platform_account_id
 * @property string $platform_campaign_id
 * @property string $message
 */
class AdvertisingCampaignHistoryLog extends Model
{
    const TABLE = 'advertising_campaign_history_logs';

    const FIELD_ID = 'id';
    const FIELD_PLATFORM = 'platform';
    const FIELD_PLATFORM_ACCOUNT_ID = 'platform_account_id';
    const FIELD_PLATFORM_CAMPAIGN_ID = 'platform_campaign_id';
    const FIELD_MESSAGE = 'message';
    const FIELD_TYPE = 'type';

    const TYPE_AUTOMATION_ACTIVATED = 1;
    const TYPE_AUTOMATION_UPDATED = 2;
    const TYPE_AUTOMATION_DEACTIVATED = 3;
    const TYPE_INFO = 4;
    const TYPE_LOCATIONS_CHANGE = 5;

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];
}
