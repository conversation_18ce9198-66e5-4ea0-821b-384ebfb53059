<?php

namespace App\Models\Campaigns;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * The Schedules themselves are not stored in the A2 database
 *  so this is not an actual pivot table
 *
 * @property int $id
 * @property int $company_campaign_id
 * @property int $schedule_id
 *
 * @property-read CompanyCampaign $campaign
 */
class CompanyCampaignSchedule extends BaseModel
{
    const string TABLE = 'company_campaign_schedules';

    const string FIELD_ID                  = 'id';
    const string FIELD_COMPANY_CAMPAIGN_ID = 'company_campaign_id';
    const string FIELD_SCHEDULE_ID         = 'schedule_id';

    const string RELATION_COMPANY_CAMPAIGN = 'campaign';

    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(CompanyCampaign::class, self::FIELD_COMPANY_CAMPAIGN_ID, CompanyCampaign::FIELD_ID);
    }
}
