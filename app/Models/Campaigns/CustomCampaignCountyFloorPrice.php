<?php

namespace App\Models\Campaigns;

use App\Models\BaseModel;
use App\Models\FloorPriceActivityLog;
use App\Models\Legacy\Location;
use App\Models\Odin\PropertyType;
use App\Models\Odin\QualityTier;
use App\Models\SaleType;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * @property int $id
 * @property int $company_campaign_id
 * @property int $state_location_id
 * @property int $county_location_id
 * @property int $sale_type_id
 * @property int $quality_tier_id
 * @property int $property_type_id
 * @property float $price
 *
 * @property-read CompanyCampaign $companyCampaign
 */
class CustomCampaignCountyFloorPrice extends BaseModel
{
    use LogsActivity;

    const string TABLE = 'custom_campaign_county_floor_prices';

    const string FIELD_ID                   = 'id';
    const string FIELD_COMPANY_CAMPAIGN_ID  = 'company_campaign_id';
    const string FIELD_STATE_LOCATION_ID    = 'state_location_id';
    const string FIELD_COUNTY_LOCATION_ID   = 'county_location_id';
    const string FIELD_SALE_TYPE_ID         = 'sale_type_id';
    const string FIELD_QUALITY_TIER_ID      = 'quality_tier_id';
    const string FIELD_PROPERTY_TYPE_ID     = 'property_type_id';
    const string FIELD_PRICE                = 'price';

    const string RELATION_COMPANY_CAMPAIGN  = 'companyCampaign';
    const string RELATION_LOCATION          = 'location';
    const string RELATION_STATE_LOCATION    = 'stateLocation';
    const string RELATION_COUNTY_LOCATION   = 'countyLocation';
    const string RELATION_QUALITY_TIER      = 'qualityTier';
    const string RELATION_SALE_TYPE         = 'saleType';
    const string RELATION_PROPERTY_TYPE     = 'propertyType';
    const string ACTIVITY_LOG_TABLE         = FloorPriceActivityLog::TABLE;

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function companyCampaign(): BelongsTo
    {
        return $this->belongsTo(CompanyCampaign::class, self::FIELD_COMPANY_CAMPAIGN_ID, CompanyCampaign::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class, self::FIELD_COUNTY_LOCATION_ID, Location::ID);
    }

    /**
     * @return BelongsTo
     */
    public function countyLocation(): BelongsTo
    {
        return $this->location();
    }

    /**
     * @return BelongsTo
     */
    public function stateLocation(): BelongsTo
    {
        return $this->belongsTo(Location::class, self::FIELD_STATE_LOCATION_ID, Location::ID);
    }

    /**
     * @return BelongsTo
     */
    public function qualityTier(): BelongsTo
    {
        return $this->belongsTo(QualityTier::class, self::FIELD_QUALITY_TIER_ID, QualityTier::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function saleType(): BelongsTo
    {
        return $this->belongsTo(SaleType::class, self::FIELD_SALE_TYPE_ID, SaleType::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function propertyType(): BelongsTo
    {
        return $this->belongsTo(PropertyType::class, self::FIELD_PROPERTY_TYPE_ID, PropertyType::FIELD_ID);
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([self::FIELD_PRICE])
            ->useLogName('custom_campaign_county_floor_price')
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
}
