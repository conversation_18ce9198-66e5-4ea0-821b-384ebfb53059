<?php

namespace App\Models\Campaigns\Modules;

use App\Models\Campaigns\CompanyCampaign;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphOne;

/**
 * @property int $id
 * @property int $company_campaign_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read CompanyCampaign $campaign
 */
abstract class BaseCompanyCampaignModule extends Model
{
    const FIELD_ID          = 'id';
    const FIELD_CAMPAIGN_ID = 'company_campaign_id';

    const RELATION_CAMPAIGN = 'campaign';

    protected $guarded = [self::FIELD_ID];

    /**
     * Defines the relationship to the company campaign.
     *
     * @return BelongsTo
     */
    public function campaign(): BelongsTo
    {
        //return $this->belongsTo(CompanyCampaign::class, 'modules');
        return $this->belongsTo(CompanyCampaign::class, self::FIELD_CAMPAIGN_ID, CompanyCampaign::FIELD_ID);
    }
}
