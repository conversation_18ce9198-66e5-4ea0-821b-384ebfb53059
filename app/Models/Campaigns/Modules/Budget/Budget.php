<?php

namespace App\Models\Campaigns\Modules\Budget;

use App\Enums\Campaigns\Modules\Budget\BudgetType;
use App\Enums\ProductAssignment\BudgetProductConfigurationEnum;
use App\Models\BaseModel;
use App\Models\Odin\ProductAssignment;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * @property int $id
 * @property int $budget_container_id
 * @property string $display_name
 * @property string $key
 * @property int $status
 * @property BudgetType $type
 * @property int $value
 * @property Carbon $last_modified_at
 * @property int $last_modified_by_company_user_id
 * @property BudgetProductConfigurationEnum $product_configuration
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read BudgetContainer $budgetContainer
 * @property-read Collection<ProductAssignment> $productAssignments
 */
class Budget extends BaseModel
{
    use LogsActivity, HasFactory;

    const TABLE = 'budgets';

    const FIELD_ID                               = 'id';
    const FIELD_BUDGET_CONTAINER_ID              = 'budget_container_id';
    const FIELD_DISPLAY_NAME                     = 'display_name';
    const FIELD_KEY                              = 'key';
    const FIELD_STATUS                           = 'status';
    const FIELD_TYPE                             = 'type';
    const FIELD_VALUE                            = 'value';
    const FIELD_LAST_MODIFIED_AT                 = 'last_modified_at';
    const FIELD_LAST_MODIFIED_BY_COMPANY_USER_ID = 'last_modified_by_company_user_id';
    const FIELD_PRODUCT_CONFIGURATION            = 'product_configuration';
    const FIELD_CREATED_AT                       = 'created_at';
    const FIELD_UPDATED_AT                       = 'updated_at';

    const STATUS_DISABLED = 0;
    const STATUS_ENABLED  = 1;

    const RELATION_BUDGET_CONTAINER    = 'budgetContainer';
    const RELATION_PRODUCT_ASSIGNMENTS = 'productAssignments';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_TYPE                  => BudgetType::class,
        self::FIELD_PRODUCT_CONFIGURATION => BudgetProductConfigurationEnum::class,
        self::FIELD_LAST_MODIFIED_AT      => 'datetime'
    ];

    protected $dates = [
        self::FIELD_LAST_MODIFIED_AT => 'date'
    ];

    /**
     * @return BelongsTo
     */
    public function budgetContainer(): BelongsTo
    {
        return $this->belongsTo(BudgetContainer::class, self::FIELD_BUDGET_CONTAINER_ID, BudgetContainer::FIELD_ID);
    }

    public function productAssignments(): HasMany
    {
        return $this->hasMany(ProductAssignment::class, ProductAssignment::FIELD_BUDGET_ID, self::FIELD_ID);
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                self::FIELD_DISPLAY_NAME,
                self::FIELD_KEY,
                self::FIELD_STATUS,
                self::FIELD_TYPE,
                self::FIELD_VALUE,
                self::FIELD_PRODUCT_CONFIGURATION
            ])
            ->useLogName('campaign_budget')
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
}
