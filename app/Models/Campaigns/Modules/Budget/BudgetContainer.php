<?php

namespace App\Models\Campaigns\Modules\Budget;

use App\Enums\Campaigns\Modules\Budget\ContainerType;
use App\Enums\Odin\Product;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\BaseCompanyCampaignModule;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

/**
 * @property ContainerType $type
 * @property int $company_campaign_id
 *
 * @property-read Collection<int, Budget> $budgets
 */
class BudgetContainer extends BaseCompanyCampaignModule
{
    use HasFactory;

    const TABLE = 'budget_containers';

    const FIELD_TYPE = 'type';
    const FIELD_COMPANY_CAMPAIGN_ID = 'company_campaign_id';

    const RELATION_BUDGETS = 'budgets';

    protected $table = self::TABLE;

    protected $casts = [self::FIELD_TYPE => ContainerType::class];

    /**
     * @return HasMany
     */
    public function budgets(): HasMany
    {
        return $this->hasMany(Budget::class);
    }

    /**
     * @return BelongsTo
     */
    public function companyCampaign(): BelongsTo
    {
        return $this->belongsTo(CompanyCampaign::class);
    }

    /**
     * @return string
     */
    public function getProductAsString(): string
    {
        return match ($this->type) {
            ContainerType::LEADS => strtolower(Product::LEAD->value),
            ContainerType::APPOINTMENTS => strtolower(Product::APPOINTMENT->value),
            ContainerType::DIRECT_LEADS => strtolower(Product::DIRECT_LEADS->value)
        };
    }
}
