<?php

namespace App\Models\Campaigns\Modules\Bidding;

use App\Builders\Pricing\BidPricingBuilder;
use App\Models\Campaigns\Modules\BaseCompanyCampaignModule;
use App\Models\Odin\ProductCountyBidPrice;
use App\Models\Odin\ProductStateBidPrice;
use Database\Factories\Odin\CompanyCampaignBidPriceModuleFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

/**
 * @property-read Collection<ProductStateBidPrice> $stateBids
 * @property-read Collection<ProductCountyBidPrice> $countyBids
 */
class CompanyCampaignBidPriceModule extends BaseCompanyCampaignModule
{
    use HasFactory;

    const TABLE = 'company_campaign_bid_price_modules';

    const RELATION_COUNTY_BIDS = 'countyBids';
    const RELATION_STATE_BIDS  = 'stateBids';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];

    public static function newFactory(): CompanyCampaignBidPriceModuleFactory
    {
        return CompanyCampaignBidPriceModuleFactory::new();
    }
    /**
     * @return HasMany
     */
    public function countyBids(): HasMany
    {
        return $this->hasMany(ProductCountyBidPrice::class, ProductCountyBidPrice::FIELD_MODULE_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function stateBids(): HasMany
    {
        return $this->hasMany(ProductStateBidPrice::class, ProductStateBidPrice::FIELD_MODULE_ID, self::FIELD_ID);
    }

    /**
     * @return Collection
     */
    public function bids(): Collection
    {
        return collect([
            'county' => $this->countyBids,
            'state'  => $this->stateBids,
        ]);
    }

    /**
     * @return BidPricingBuilder
     */
    public function countyBidsQuery(): BidPricingBuilder
    {
        return BidPricingBuilder::query($this->campaign)
            ->forCountyBids();
    }

    /**
     * @return BidPricingBuilder
     */
    public function stateBidsQuery(): BidPricingBuilder
    {
        return BidPricingBuilder::query($this->campaign)
            ->forStateBids();
    }
}
