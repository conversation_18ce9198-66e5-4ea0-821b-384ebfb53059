<?php

namespace App\Models\Campaigns\Modules\Delivery;

use App\Campaigns\Delivery\CRM\BaseCRMDeliverer;
use App\Campaigns\Delivery\CRM\Enums\CRMType;
use App\Models\BaseModel;
use Carbon\Carbon;
use Database\Factories\Odin\CompanyCampaignDeliveryModuleCRMFactory;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * @property int $id
 * @property int $module_id
 * @property CRMType $crm_type
 * @property bool $active
 * @property string $display_name
 * @property array $payload
 * @Property int $template_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read CompanyCampaignDeliveryModule $module
 * @property-read CompanyCRMTemplate|null $template
 */
class CompanyCampaignDeliveryModuleCRM extends BaseModel
{
    use LogsActivity, HasFactory;

    const string TABLE = 'company_campaign_delivery_module_crms';

    const string FIELD_ID           = 'id';
    const string FIELD_MODULE_ID    = 'module_id';
    const string FIELD_CRM_TYPE     = 'crm_type';
    const string FIELD_ACTIVE       = 'active';
    const string FIELD_DISPLAY_NAME = 'display_name';
    const string FIELD_PAYLOAD      = 'payload';
    const string FIELD_TEMPLATE_ID  = 'template_id';

    const string RELATION_MODULE   = 'module';
    const string RELATION_TEMPLATE = 'template';

    protected $guarded = [self::FIELD_ID];
    protected $table   = self::TABLE;
    protected $casts = [
        self::FIELD_CRM_TYPE => CRMType::class,
        self::FIELD_PAYLOAD  => 'array',
    ];

    public static function newFactory(): CompanyCampaignDeliveryModuleCRMFactory
    {
        return CompanyCampaignDeliveryModuleCRMFactory::new();
    }

    /**
     * Accessor to allow CompanyCRMTemplate to provide payload
     * @return Attribute
     */
    protected function payload(): Attribute
    {
        return Attribute::make(
            get: fn(?string $value) => $this->usesTemplate()
                ? $this->template->payload
                : json_decode($value ?? "{}", true),
        );
    }

    /**
     * Accessor to allow CompanyCRMTemplate to provide CRMType
     * @return Attribute
     */
    protected function crmType(): Attribute
    {
        return Attribute::make(
            get: fn(int $value) => $this->usesTemplate()
                ? $this->template->crm_type
                : CRMType::from($value),
        );
    }

    /**
     * Accessor to allow CompanyCRMTemplate to provide display name
     * @return Attribute
     */
    protected function displayName(): Attribute
    {
        return Attribute::make(
            get: fn(string $value) => $this->usesTemplate()
                ? $this->template->display_name
                : $value,
        );
    }

    /**
     * Defines the relationship to the delivery module
     *
     * @return BelongsTo
     */
    public function module(): BelongsTo
    {
        return $this->belongsTo(
            CompanyCampaignDeliveryModule::class,
            self::FIELD_MODULE_ID,
            CompanyCampaignDeliveryModule::FIELD_ID
        );
    }

    /**
     * Update a nested payload field value
     *
     * @param string $fieldGroup
     * @param string $key
     * @param mixed $value
     * @return bool
     */
    public function updatePayloadFieldValue(string $fieldGroup, string $key, mixed $value): bool
    {
        $currentPayload = $this->payload;
        if (array_key_exists($fieldGroup, $currentPayload)) {
            foreach($currentPayload[$fieldGroup] as &$field) {
                if ($field[BaseCRMDeliverer::CRM_FIELD_KEY] === $key) {
                    $field[BaseCRMDeliverer::CRM_FIELD_VALUE] = $value;

                    return $this->update([
                        self::FIELD_PAYLOAD => $currentPayload,
                    ]);
                }
            }
        }

        return false;
    }

    /**
     * @return BelongsTo
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(CompanyCRMTemplate::class, self::FIELD_TEMPLATE_ID, CompanyCRMTemplate::FIELD_ID);
    }

    /**
     * @return bool
     */
    public function usesTemplate(): bool
    {
        return !!$this->template;
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->useLogName('company_campaign_crm')
            ->logOnly([self::FIELD_MODULE_ID, self::FIELD_CRM_TYPE, self::FIELD_ACTIVE, self::FIELD_DISPLAY_NAME, self::FIELD_PAYLOAD])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
}
