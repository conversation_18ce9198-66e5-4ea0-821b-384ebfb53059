<?php

namespace App\Models\Campaigns\Modules\Delivery;

use App\Campaigns\Delivery\CRM\Enums\CRMType;
use App\Models\BaseModel;
use App\Models\Odin\Company;
use Carbon\Carbon;
use Database\Factories\Odin\CompanyCRMTemplateFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property int $company_id
 * @property CRMType $crm_type
 * @property string $display_name
 * @property array $payload
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Collection<CompanyCampaignDeliveryModuleCRM> $campaignDeliveries
 * @property-read Company $company
 */
class CompanyCRMTemplate extends BaseModel
{
    use HasFactory;
    const string TABLE = 'company_crm_templates';

    const string FIELD_ID           = 'id';
    const string FIELD_COMPANY_ID   = 'company_id';
    const string FIELD_CRM_TYPE     = 'crm_type';
    const string FIELD_DISPLAY_NAME = 'display_name';
    const string FIELD_PAYLOAD      = 'payload';

    const string RELATION_CAMPAIGN_DELIVERIES = 'campaignDeliveries';
    const string RELATION_COMPANY             = 'company';

    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];
    protected $casts = [
        self::FIELD_CRM_TYPE => CRMType::class,
        self::FIELD_PAYLOAD  => 'array',
    ];

    public static function newFactory(): CompanyCRMTemplateFactory
    {
        return CompanyCRMTemplateFactory::new();
    }

    /**
     * @return HasMany
     */
    public function campaignDeliveries(): HasMany
    {
        return $this->hasMany(CompanyCampaignDeliveryModuleCRM::class, CompanyCampaignDeliveryModuleCRM::FIELD_TEMPLATE_ID, self::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }
}
