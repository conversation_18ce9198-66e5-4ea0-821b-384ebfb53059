<?php

namespace App\Models\Campaigns\Modules\Delivery;

use App\Models\BaseModel;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ConsumerProduct;
use Carbon\Carbon;
use Database\Factories\Odin\CompanyCampaignDeliveryLogFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $campaign_id
 * @property int $module_id
 * @property int $crm_id
 * @property int $consumer_product_id
 * @property bool $success
 * @property array $payload
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read CompanyCampaign $campaign
 * @property-read CompanyCampaignDeliveryModule $module
 * @property-read CompanyCampaignDeliveryModuleCRM $crm
 * @property-read ConsumerProduct $product
 */
class CompanyCampaignDeliveryLog extends BaseModel
{
    use HasFactory;
    const TABLE = 'company_campaign_crm_delivery_logs';

    const FIELD_ID                  = 'id';
    const FIELD_CAMPAIGN_ID         = 'campaign_id';
    const FIELD_MODULE_ID           = 'module_id';
    const FIELD_CRM_ID              = 'crm_id';
    const FIELD_CONSUMER_PRODUCT_ID = 'consumer_product_id';
    const FIELD_SUCCESS             = 'success';
    const FIELD_PAYLOAD             = 'payload';
    const FIELD_CREATED_AT          = 'created_at';
    const FIELD_UPDATED_AT          = 'updated_at';

    const RELATION_CAMPAIGN = 'campaign';
    const RELATION_MODULE   = 'module';
    const RELATION_CRM      = 'crm';
    const RELATION_PRODUCT  = 'product';

    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];
    protected $casts   = [self::FIELD_PAYLOAD => 'array'];

    public static function newFactory(): CompanyCampaignDeliveryLogFactory
    {
        return CompanyCampaignDeliveryLogFactory::new();
    }
    /**
     * Defines the relationship to the campaign model
     *
     * @return BelongsTo
     */
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(CompanyCampaign::class, self::FIELD_CAMPAIGN_ID, CompanyCampaign::FIELD_ID);
    }

    /**
     * Defines the relationship to the module
     *
     * @return BelongsTo
     */
    public function module(): BelongsTo
    {
        return $this->belongsTo(
            CompanyCampaignDeliveryModule::class,
            self::FIELD_MODULE_ID,
            CompanyCampaignDeliveryModule::FIELD_ID
        );
    }

    /**
     * Defines the relationship to the crm
     *
     * @return BelongsTo
     */
    public function crm(): BelongsTo
    {
        return $this->belongsTo(
            CompanyCampaignDeliveryModuleCRM::class,
            self::FIELD_CRM_ID,
            CompanyCampaignDeliveryModuleCRM::FIELD_ID
        );
    }

    /**
     * Defines the relationship to the product
     *
     * @return BelongsTo
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(ConsumerProduct::class, self::FIELD_CONSUMER_PRODUCT_ID, ConsumerProduct::FIELD_ID);
    }
}
