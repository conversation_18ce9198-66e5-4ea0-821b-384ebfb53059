<?php

namespace App\Models\Campaigns\Modules\Delivery;

use App\Campaigns\Delivery\Contacts\Enums\ContactDeliveryLogType;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ConsumerProduct;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $campaign_id
 * @property int $module_id
 * @property int $consumer_product_id
 * @property int $contact_module_contact_id
 * @property ContactDeliveryLogType $type
 * @property bool $success
 * @property array $payload
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read ConsumerProduct $consumerProduct
 * @property-read CompanyCampaignDeliveryModuleContact $contactModuleContact
 * @property-read CompanyCampaign $campaign
 */
class CompanyCampaignContactDeliveryLog extends Model
{
    use HasFactory;

    const string TABLE                      = 'company_campaign_contact_delivery_logs';

    const string FIELD_ID                   = 'id';
    const string FIELD_CAMPAIGN_ID          = 'campaign_id';
    const string FIELD_MODULE_ID            = 'module_id';
    const string FIELD_CONSUMER_PRODUCT_ID  = 'consumer_product_id';
    const string FIELD_CONTACT_MODULE_ID    = 'contact_module_contact_id';
    const string FIELD_TYPE                 = 'type';
    const string FIELD_SUCCESS              = 'success';
    const string FIELD_PAYLOAD              = 'payload';
    const string FIELD_CREATED_AT           = 'created_at';
    const string FIELD_UPDATED_AT           = 'updated_at';

    const string RELATION_CONSUMER_PRODUCT       = 'consumerProduct';
    const string RELATION_CONTACT_MODULE_CONTACT = 'contactModuleContact';
    const string RELATION_CAMPAIGN               = 'campaign';

    const string PAYLOAD_TO                 = 'to';
    const string PAYLOAD_CONTENT            = 'content';
    const string PAYLOAD_ERROR              = 'error';
    const string PAYLOAD_EMAIL_SUBJECT      = 'email_subject';

    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];
    protected $casts = [
        self::FIELD_TYPE => ContactDeliveryLogType::class,
        self::FIELD_SUCCESS => 'boolean',
        self::FIELD_PAYLOAD => 'array'
    ];

    /**
     * @return BelongsTo
     */
    public function consumerProduct(): BelongsTo
    {
        return $this->belongsTo(ConsumerProduct::class);
    }

    /**
     * @return BelongsTo
     */
    public function contactModuleContact(): BelongsTo
    {
        return $this->belongsTo(CompanyCampaignDeliveryModuleContact::class);
    }

    /**
     * @return BelongsTo
     */
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(CompanyCampaign::class, self::FIELD_CAMPAIGN_ID, CompanyCampaign::FIELD_ID);
    }
}
