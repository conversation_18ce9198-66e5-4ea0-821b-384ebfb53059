<?php

namespace App\Models\Campaigns\Modules\Delivery;

use App\Models\Campaigns\Modules\BaseCompanyCampaignModule;
use Database\Factories\Odin\CompanyCampaignDeliveryModuleFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

/**
 * @property-read Collection<CompanyCampaignDeliveryModuleContact> $contacts
 * @property-read Collection<CompanyCampaignDeliveryModuleCRM> $crms
 */
class CompanyCampaignDeliveryModule extends BaseCompanyCampaignModule
{
    use HasFactory;
    const TABLE = 'company_campaign_delivery_modules';

    const RELATION_CONTACTS = 'contacts';
    const RELATION_CRMS     = 'crms';

    protected $guarded = [self::FIELD_ID];
    protected $table   = self::TABLE;
    protected $casts   = [];

    public static function newFactory(): CompanyCampaignDeliveryModuleFactory
    {
        return CompanyCampaignDeliveryModuleFactory::new();
    }

    /**
     * Defines the has many relation to the contacts for this campaign.
     *
     * @return HasMany
     */
    public function contacts(): HasMany
    {
        return $this->hasMany(
            CompanyCampaignDeliveryModuleContact::class,
            CompanyCampaignDeliveryModuleContact::FIELD_MODULE_ID,
            self::FIELD_ID
        );
    }

    /**
     * Defines the has many relation to the crms for this campaign
     *
     * @return HasMany
     */
    public function crms(): HasMany
    {
        return $this->hasMany(
            CompanyCampaignDeliveryModuleCRM::class,
            CompanyCampaignDeliveryModuleCRM::FIELD_MODULE_ID,
            self::FIELD_ID
        );
    }
}
