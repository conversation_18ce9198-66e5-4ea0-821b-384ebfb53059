<?php

namespace App\Models\Campaigns\Modules\LocationModule;

use App\Models\Campaigns\Modules\BaseCompanyCampaignModule;
use App\Models\Legacy\Location;
use App\Repositories\LocationRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

/**
 * @property-read Collection<Location> $legacyLocations
 * @property-read Collection<CompanyCampaignLocationModuleLocation> $locations
 */
class CompanyCampaignLocationModule extends BaseCompanyCampaignModule
{
    const string TABLE = 'company_campaign_location_modules';

    const string RELATION_LEGACY_LOCATIONS = 'legacyLocations';
    const string RELATION_LOCATIONS        = 'locations';

    const string LOCATIONS_COUNTY_ID_KEY = LocationRepository::COUNTY_LOCATION_IDS;
    const string LOCATIONS_STATE_ID_KEY  = LocationRepository::STATE_LOCATION_IDS;

    protected $guarded = [self::FIELD_ID];

    protected $table = self::TABLE;

    protected $casts = [];

    /**
     * Has all location data
     * Defines relationship to the legacy locations.
     *
     * @return HasManyThrough
     *
     * @TODO: Replace Location model with A2 model when locations upgraded.
     */
    public function legacyLocations(): HasManyThrough
    {
        return $this->hasManyThrough(
            Location::class,
            CompanyCampaignLocationModuleLocation::class,
            CompanyCampaignLocationModuleLocation::FIELD_MODULE_ID,
            Location::ID,
            self::FIELD_ID,
            CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID
        );
    }

    /**
     * Only has zip code
     * Defines relationship to the locations.
     *
     * @return HasMany
     */
    public function locations(): HasMany
    {
        return $this->hasMany(
            CompanyCampaignLocationModuleLocation::class,
            CompanyCampaignLocationModuleLocation::FIELD_MODULE_ID,
            self::FIELD_ID
        );
    }

    /**
     * Fetch a list of State and County location IDs covered by this module
     * @return array
     */
    public function getStateAndCountyLocationIds(): array
    {
        /** @var LocationRepository $locationRepository */
        $locationRepository = app(LocationRepository::class);
        $zipLocationIds = $this->locations()
            ->pluck(CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID)
            ->toArray();

        return $locationRepository->getStateAndCountyLocationIdsFromZipCodeLocations($zipLocationIds);
    }

    /**
     * @return array
     */
    public function getCountyLocationIds(): array
    {
        /** @var LocationRepository $locationRepository */
        $locationRepository = app(LocationRepository::class);
        $zipLocationIds = $this->locations()
            ->pluck(CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID)
            ->toArray();

        return $locationRepository->getCountyLocationsFromZipCodeLocations($zipLocationIds, true)
            ->toArray();
    }
}
