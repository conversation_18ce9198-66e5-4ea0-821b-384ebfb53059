<?php

namespace App\Models\Campaigns\Modules\LocationModule;

use App\Models\BaseModel;
use App\Models\Legacy\Location;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $module_id
 * @property int $location_id
 * @property string $zip_code
 * @property string $company_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read CompanyCampaignLocationModule $module
 * @property-read Location $location
 *
 * @TODO: Replace Location model with A2 model when locations upgraded.
 */
class CompanyCampaignLocationModuleLocation extends BaseModel
{
    use HasFactory;
    const string TABLE = 'company_campaign_location_module_locations';

    const string FIELD_ID          = 'id';
    const string FIELD_MODULE_ID   = 'module_id';
    const string FIELD_LOCATION_ID = 'location_id';
    const string FIELD_ZIP_CODE    = 'zip_code';
    const string FIELD_COMPANY_ID  = 'company_id';

    const string RELATION_MODULE   = 'module';
    const string RELATION_LOCATION = 'location';

    protected $guarded = [self::FIELD_ID];

    protected $table = self::TABLE;

    /**
     * Defines the relationship to the location module.
     *
     * @return BelongsTo
     */
    public function module(): BelongsTo
    {
        return $this->belongsTo(
            CompanyCampaignLocationModule::class,
            self::FIELD_MODULE_ID,
            CompanyCampaignLocationModule::FIELD_ID
        );
    }

    /**
     * Defines the relationship to the location.
     *
     * @return BelongsTo
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(
            Location::class,
            self::FIELD_LOCATION_ID,
            Location::ID
        );
    }
}
