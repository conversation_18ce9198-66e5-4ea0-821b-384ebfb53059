<?php

namespace App\Models;

use App\Models\Legacy\EloquentQuote;
use App\Models\Odin\ConsumerProduct;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class LeadProcessingAllocation
 *
 * @package App\Models
 * @property int                                   $id
 * @property int                                   $lead_id
 * @property boolean                               $delivered
 * @property int                                   $queue_configuration_id
 * @property int                                   $lead_processor_id
 * @property string                                $processing_scenario
 * @property int                                   $consumer_product_id
 * @property Carbon                                $deliver_at
 * @property boolean                               $final_attempt
 * @property Carbon                                $created_at
 * @property Carbon                                $updated_at
 *
 * @property-read LeadProcessingQueueConfiguration $queueConfiguration
 * @property-read LeadProcessor                    $leadProcessor
 * @property-read ConsumerProduct                  $consumerProduct
 */
class LeadProcessingAllocation extends BaseModel
{
    const TABLE = 'lead_processing_allocations';

    const FIELD_ID                     = 'id';
    const FIELD_CONSUMER_PRODUCT_ID    = 'consumer_product_id';
    const FIELD_DELIVERED              = 'delivered';
    const FIELD_QUEUE_CONFIGURATION_ID = 'queue_configurations_id';
    const FIELD_LEAD_PROCESSOR_ID      = 'lead_processor_id';
    const FIELD_DELIVER_AT             = 'deliver_at';
    const FIELD_PROCESSING_SCENARIO    = 'processing_scenario';
    const FIELD_FINAL_ATTEMPT          = 'final_attempt';
    /** @deprecated  */
    const FIELD_LEAD_ID                = 'lead_id';

    const RELATION_LEAD                = 'lead';
    const RELATION_LEAD_PROCESSOR      = 'leadProcessor';
    const RELATION_QUEUE_CONFIGURATION = 'queueConfiguration';
    const RELATION_CONSUMER_PRODUCT    = 'consumerProduct';

    const DELIVERED = 1;
    const NOT_DELIVERED = 0;

    const BLANK_DELIVERY = '0000-00-00 00:00:00';

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function lead(): BelongsTo
    {
        return $this->belongsTo(EloquentQuote::class, self::FIELD_LEAD_ID, EloquentQuote::ID);
    }

    /**
     * @return BelongsTo
     */
    public function queueConfiguration(): BelongsTo
    {
        return $this->belongsTo(LeadProcessingQueueConfiguration::class, self::FIELD_QUEUE_CONFIGURATION_ID, LeadProcessingQueueConfiguration::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function leadProcessor(): BelongsTo
    {
        return $this->belongsTo(LeadProcessor::class, self::FIELD_LEAD_PROCESSOR_ID, LeadProcessor::FIELD_ID)->withTrashed();
    }

    /**
     * @return BelongsTo
     */
    public function consumerProduct(): BelongsTo
    {
        return $this->belongsTo(ConsumerProduct::class, self::FIELD_CONSUMER_PRODUCT_ID, ConsumerProduct::FIELD_ID);
    }
}
