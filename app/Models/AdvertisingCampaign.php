<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AdvertisingCampaign extends Model
{
    const TABLE = 'advertising_campaigns';

    const FIELD_ID = 'id';
    const FIELD_PLATFORM = 'platform';
    const FIELD_PLATFORM_ACCOUNT_ID = 'platform_account_id';
    const FIELD_PLATFORM_CAMPAIGN_ID = 'platform_campaign_id';
    const FIELD_LAST_RUN_TIMESTAMP = 'last_run_timestamp';
    const FIELD_RUN_INTERVAL_SECS = 'run_interval_secs';
    const FIELD_RUN_INTERVAL_DISPLAY_UNIT = 'run_interval_display_unit';

    const RUN_INTERVAL_MINUTES = 'minutes';
    const RUN_INTERVAL_HOURS = 'hours';
    const RUN_INTERVAL_WEEKS = 'weeks';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];
}
