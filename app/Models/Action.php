<?php

namespace App\Models;

use App\Enums\CompanySalesStatus;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Support\Str;

/**
 * @property int $id
 * @property int|null $from_user_id
 * @property int|null $for_id
 * @property string|null $for_relation_type
 * @property string $subject
 * @property string $message
 * @property int|null $related_task_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon|null $display_date
 * @property boolean $pinned
 * @property CompanySalesStatus|null $previous_sales_status
 * @property CompanySalesStatus|null $updated_sales_status
 *
 * @property User $from
 * @property ActivityFeed $activity
 */
class Action extends Model
{
    use HasFactory;

    const TABLE = 'actions';

    const FIELD_ID                    = 'id';
    const FIELD_FROM_USER_ID          = 'from_user_id';
    const FIELD_FOR_ID                = 'for_id';
    const FIELD_LEGACY_FOR_ID         = 'legacy_for_id';
    const FIELD_FOR_RELATION_TYPE     = 'for_relation_type';
    const FIELD_SUBJECT               = 'subject';
    const FIELD_MESSAGE               = 'message';
    const FIELD_DISPLAY_DATE          = 'display_date';
    const FIELD_RELATION_TASK_ID      = 'related_task_id';
    const FIELD_PINNED                = 'pinned';
    const FIELD_CATEGORY_ID           = 'action_category_id';
    const FIELD_TAG_BY_EMAIL          = 'tag_by_email';
    const FIELD_PREVIOUS_SALES_STATUS = 'previous_sales_status';
    const FIELD_UPDATED_SALES_STATUS  = 'updated_sales_status';

    const RELATION_TYPE_COMPANY         = 'company';
    const RELATION_TYPE_COMPANY_USER    = 'company_user';
    const RELATION_TYPE_COMPANY_CONTACT = 'company_contact';
    const RELATION_TYPE_CATEGORY        = 'category';
    const RELATION_TAGS                 = 'tags';
    const RELATION_FROM_USER            = 'from';

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_DISPLAY_DATE => 'date',
        self::FIELD_TAG_BY_EMAIL => 'boolean',
        self::FIELD_PREVIOUS_SALES_STATUS => CompanySalesStatus::class,
        self::FIELD_UPDATED_SALES_STATUS => CompanySalesStatus::class
    ];

    /**
     * Convert markdown to HTML
     * @return Attribute
     */
    protected function message(): Attribute {
        return Attribute::make(
            get: fn($value) => Str::markdown($value)
        );
    }

    /**
     * Defines relationship to who user is from.
     *
     * @return HasOne
     */
    public function from(): HasOne
    {
        return $this->hasOne(User::class, User::FIELD_ID, self::FIELD_FROM_USER_ID);
    }

    /**
     * Define relationship to Company or CompanyUser to which the action belongs
     *
     * @return Company|CompanyUser|Model|null
     * @throws ModelNotFoundException
     */
    public function to(): CompanyUser|Company|Model|null
    {
        return match ($this->{self::FIELD_FOR_RELATION_TYPE}) {
            self::RELATION_TYPE_COMPANY_USER, self::RELATION_TYPE_COMPANY_CONTACT => CompanyUser::query()->withTrashed()->findOrFail($this->{self::FIELD_FOR_ID}),
            self::RELATION_TYPE_COMPANY => Company::query()->findOrFail($this->{self::FIELD_FOR_ID}),
            default => null,
        };
    }

    /**
     * @return BelongsTo
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(ActionCategory::class, self::FIELD_CATEGORY_ID, ActionCategory::FIELD_ID);
    }

    /**
     * @return MorphOne
     */
    public function activity(): MorphOne
    {
        return $this->morphOne(ActivityFeed::class, 'item');
    }

    /**
     * @return HasMany
     */
    public function tags(): HasMany
    {
        return $this->hasMany(ActionTag::class, ActionTag::FIELD_ACTION_ID, self::FIELD_ID);
    }

}
