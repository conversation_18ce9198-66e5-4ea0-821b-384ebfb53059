<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HistoricalCompanyActivation extends Model
{
    use HasFactory;

    const TABLE = 'historical_company_activations';

    const FIELD_ID = 'id';
    const FIELD_COMPANY_ID = 'company_id';
    const FIELD_STATUS_DATE = 'status_date';
    const FIELD_TYPE = 'type';

    const REACTIVATION = 1;
    const DEACTIVATION = 2;
    const ACTIVATION = 3;

    protected $guarded = [
        self::FIELD_ID,
    ];

    protected $table = self::TABLE;


}
