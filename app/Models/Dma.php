<?php

namespace App\Models;

use App\Models\Legacy\Location;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Collection;

/**
 * Class Dma extends
 *
 * @property int $id
 * @property string $dma_code
 * @property string $dma_name
 * @property string $meta_dma_key
 *
 * @property-read Collection<DmaLocation> $dmaLocations
 * @property-read Collection<Location> $locations
 */
class Dma extends BaseModel
{
    const string TABLE = 'dmas';

    const string FIELD_ID           = 'id';
    const string FIELD_DMA_CODE     = 'dma_code';
    const string FIELD_DMA_NAME     = 'dma_name';
    const string FIELD_META_DMA_KEY = 'meta_dma_key';

    const string RELATION_DMA_LOCATIONS = 'dmaLocations';
    const string RELATION_LOCATIONS     = 'locations';

    protected $table    = self::TABLE;
    protected $guarded  = [self::FIELD_ID];
    public $timestamps  = false;

    /**
     * @return HasMany
     */
    public function dmaLocations(): HasMany
    {
        return $this->hasMany(DmaLocation::class, DmaLocation::FIELD_DMA_ID, self::FIELD_ID);
    }

    /**
     * @return Collection
     */
    public function locations(): Collection
    {
        return Location::query()
            ->whereIn(Location::ID, $this->{self::RELATION_DMA_LOCATIONS}->pluck(DmaLocation::FIELD_LOCATION_ID))
            ->get();
    }
}
