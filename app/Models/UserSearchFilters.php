<?php

namespace App\Models;

use App\Enums\UserSearchFilterCategory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class UserSearchFilters
 *
 * @property int $id
 * @property int $user_id
 * @property UserSearchFilterCategory $category
 * @property array $data
 */
class UserSearchFilters extends BaseModel
{
    const TABLE = 'user_search_filters';

    const FIELD_ID = 'id';
    const FIELD_USER_ID = 'user_id';
    const FIELD_DATA = 'data';
    const FIELD_CATEGORY = 'category';

    const RELATION_USER = 'user';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_DATA => 'array',
        self::FIELD_CATEGORY => UserSearchFilterCategory::class,
    ];

    /**
     * Get the user that owns the search filters
     *
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_ID, User::FIELD_ID);
    }
}
