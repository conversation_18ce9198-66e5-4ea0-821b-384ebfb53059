<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $content
 * @property int $relation_id
 * @property string $relation_type
 * @property int $user_id
 * @property int $parent_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $deleted_at
 *
 * @property-read Model $notable
 */
class Note extends Model
{
    use SoftDeletes;

    const TABLE               = 'notes';
    const FIELD_ID            = 'id';
    const FIELD_CONTENT       = 'content';
    const FIELD_RELATION_ID   = 'relation_id';
    const FIELD_RELATION_TYPE = 'relation_type';
    const FIELD_AUTHOR_ID     = 'author_id';
    const FIELD_PARENT_ID     = 'parent_id';
    const FIELD_CREATED_AT    = 'created_at';
    const FIELD_UPDATED_AT    = 'updated_at';
    const RELATION_NOTABLE    = 'notable';
    const RELATION_AUTHOR     = 'author';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return MorphTo
     */
    public function notable(): MorphTo
    {
        return $this->morphTo(__FUNCTION__, self::FIELD_RELATION_TYPE, self::FIELD_RELATION_ID);
    }

    /**
     * @return BelongsTo
     */
    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_AUTHOR_ID, User::FIELD_ID);
    }
}
