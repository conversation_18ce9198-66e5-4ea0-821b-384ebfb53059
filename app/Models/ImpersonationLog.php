<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $user_id
 * @property int $impersonating_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class ImpersonationLog extends Model
{
    use HasFactory;

    const TABLE = 'impersonation_logs';

    const FIELD_ID               = 'id';
    const FIELD_USER_ID          = 'user_id';
    const FIELD_IMPERSONATING_ID = 'impersonating_id';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];
}
