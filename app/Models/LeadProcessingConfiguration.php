<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $time_zone_opening_delay_in_minutes
 * @property int $lead_recency_threshold_in_seconds
 * @property int $minimum_review_time
 * @property int $lead_processable_delay_seconds
 * @property int $check_next_lead_interval_seconds
 */
class LeadProcessingConfiguration extends BaseModel
{
    const TABLE = 'lead_processing_configurations';

    const FIELD_ID                                 = 'id';
    const FIELD_TIME_ZONE_OPENING_DELAY_IN_MINUTES = 'time_zone_opening_delay_in_minutes';
    const FIELD_LEAD_RECENCY_THRESHOLD_IN_SECONDS  = 'recency_threshold_in_seconds';
    const FIELD_MINIMUM_REVIEW_TIME                = 'minimum_review_time';
    const FIELD_LEAD_PROCESSABLE_DELAY_SECONDS     = 'lead_processable_delay_seconds';
    const FIELD_CHECK_NEXT_LEAD_INTERVAL_SECONDS   = 'check_next_lead_interval_seconds';
    const FIELD_LAST_LEAD_CREATED_INTERVAL_MIN     = 'last_lead_created_interval_min';
    const FIELD_NEXT_LEAD_LOGGING_TEAM_ID          = 'next_lead_logging_team_id';

    const TIME_ZONE_OPENING_DELAY_DEFAULT  = 120;
    const LEAD_RECENCY_THRESHOLD_DEFAULT   = 180;
    const MINIMUM_REVIEW_TIME_DEFAULT      = 5;
    const LEAD_PROCESSABLE_DELAY_DEFAULT   = 60;
    const CHECK_NEXT_LEAD_INTERVAL_DEFAULT = 60;
    const LAST_LEAD_CREATED_INTERVAL_DEFAULT = 5;
    const NEXT_LEAD_LOGGING_TEAM_ID_DEFAULT = 0;

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];
}
