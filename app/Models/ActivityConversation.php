<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int    $id
 * @property int    $activity_id
 * @property int    $parent_id
 * @property int    $user_id
 * @property string $comment
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $deleted_at
 *
 * @property-read ActivityFeed          $activity
 * @property-read User                  $user
 * @property-read ActivityConversation  $children
 */
class ActivityConversation extends BaseModel
{
    use HasFactory;

    const TABLE = "activity_conversations";

    const FIELD_ID           = 'id';
    const FIELD_ACTIVITY_ID  = 'activity_id';
    const FIELD_PARENT_ID    = 'parent_id';
    const FIELD_USER_ID      = 'user_id';
    const FIELD_COMMENT      = 'comment';
    const FIELD_CREATED_AT   = 'created_at';
    const FIELD_UPDATED_AT   = 'updated_at';
    const FIELD_DELETED_AT   = 'deleted_at';

    const RELATION_ACTIVITY  = 'activity';
    const RELATION_USER      = 'user';
    const RELATION_CHILDREN  = 'children';

    protected $table   = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function activity(): BelongsTo
    {
        return $this->belongsTo(ActivityFeed::class, self::FIELD_ACTIVITY_ID, ActivityFeed::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function children(): HasMany
    {
        return $this->hasMany(ActivityConversation::class, self::FIELD_PARENT_ID, self::FIELD_ID)
                    ->orderByDesc(self::FIELD_CREATED_AT);
    }
}
