<?php

namespace App\Models;

use App\Enums\CommunicationType;
use App\Models\Odin\ProductAssignment;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $product_assignment_id
 * @property string $sid
 * @property CommunicationType $communication_type
 * @property array|null $content
 *
 * @property-read ProductAssignment $productAssignment
 */
class ProductAssignmentCommunication extends Model
{
    const string TABLE = 'product_assignment_communications';

    const string FIELD_ID = 'id';
    const string FIELD_PRODUCT_ASSIGNMENT_ID = 'product_assignment_id';
    const string FIELD_SID = 'sid';
    const string FIELD_COMMUNICATION_TYPE = 'communication_type';
    const string FIELD_CONTENT = 'content';

    protected $casts = [
        self::FIELD_COMMUNICATION_TYPE => CommunicationType::class,
        self::FIELD_CONTENT => 'array'
    ];

    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function productAssignment(): BelongsTo
    {
        return $this->belongsTo(ProductAssignment::class);
    }
}
