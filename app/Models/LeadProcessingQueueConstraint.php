<?php

namespace App\Models;

use \Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @property int $id
 * @property int $queue_id
 * @property int $order
 * @property int $constraint_id
 *
 * @property-read LeadProcessingQueueConfiguration $queue
 * @property-read LeadProcessingConstraint $constraint
 */
class LeadProcessingQueueConstraint extends BaseModel
{
    const TABLE = 'lead_processing_queue_constraints';

    const FIELD_ID            = 'id';
    const FIELD_QUEUE_ID      = 'queue_id';
    const FIELD_ORDER         = 'order';
    const FIELD_CONSTRAINT_ID = 'constraint_id';

    const RELATION_QUEUE      = 'queue';
    const RELATION_CONSTRAINT = 'constraint';

    protected $table = self::TABLE;

    /**
     * @return BelongsTo
     */
    public function queue(): BelongsTo
    {
        return $this->belongsTo(LeadProcessingQueueConfiguration::class, self::FIELD_QUEUE_ID, LeadProcessingQueueConfiguration::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function constraint(): HasOne
    {
        return $this->hasOne(LeadProcessingConstraint::class, LeadProcessingConstraint::FIELD_ID, self::FIELD_CONSTRAINT_ID);
    }
}
