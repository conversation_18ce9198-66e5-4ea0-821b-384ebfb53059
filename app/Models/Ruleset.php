<?php

namespace App\Models;

use App\Models\MissedProducts\OpportunityNotificationConfig;
use App\Models\Odin\CompanyQualityScoreRule;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;

/**
 * @property int    $id
 * @property string $name
 * @property array  $rules
 * @property array  $filter
 * @property string $type
 * @property string $source
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read OpportunityNotificationConfig[]|Collection $notificationConfigs
 * @property-read CompanyQualityScoreRule[]|Collection $companyQualityScores
 *
 */
class Ruleset extends BaseModel
{
    use HasFactory, SoftDeletes;

    const TABLE             = 'rulesets';

    const FIELD_ID              = 'id';
    const FIELD_NAME            = 'name';
    const FIELD_RULES           = 'rules';
    const FIELD_FILTER          = 'filter';
    const FIELD_TYPE            = 'type'; // Enums/RulesetType.php
    const FIELD_SOURCE          = 'source';
    const FIELD_CREATED_AT      = 'created_at';
    const FIELD_UPDATED_AT      = 'updated_at';
    const FIELD_DELETED_AT      = 'deleted_at';

    const RELATION_NOTIFICATION_CONFIGS = 'notificationConfigs';
    const RELATION_CQS = 'companyQualityScores';

    protected $table = self::TABLE;

    protected $casts = [
        self::FIELD_RULES => 'array',
        self::FIELD_FILTER => 'array',
    ];

    protected $fillable = [
        self::FIELD_NAME,
        self::FIELD_RULES,
        self::FIELD_TYPE,
        self::FIELD_FILTER,
        self::FIELD_SOURCE,
    ];

    protected $guarded = [self::FIELD_ID];

    /**
     * @return HasMany
     */
    public function notificationConfigs(): HasMany
    {
        return $this->hasMany(OpportunityNotificationConfig::class, OpportunityNotificationConfig::FIELD_RULE_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function companyQualityScores(): HasMany
    {
        return $this->hasMany(CompanyQualityScoreRule::class, CompanyQualityScoreRule::FIELD_RULE_ID, self::FIELD_ID);
    }
}
