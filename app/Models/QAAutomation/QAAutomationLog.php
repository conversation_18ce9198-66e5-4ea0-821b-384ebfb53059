<?php

namespace App\Models\QAAutomation;

use App\Models\Odin\ConsumerProduct;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class QAAutomationLog
 *
 * @package App\Models
 *
 * @property int $id
 * @property int $consumer_product_id
 * @property int $qa_automation_rule_id
 * @property string $entry
 * @property string $error_message
 * @property int $log_level
 * @property array $data
 * @property bool $enabled
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read ConsumerProduct $consumerProduct
 * @property-read QAAutomationRule $qaAutomationRule
 */
class QAAutomationLog extends Model
{
    const string TABLE = 'qa_automation_logs';

    const string FIELD_ID = 'id';
    const string FIELD_CONSUMER_PRODUCT_ID = 'consumer_product_id';
    const string FIELD_QA_AUTOMATION_RULE_ID = 'qa_automation_rule_id';
    const string FIELD_ENTRY = 'entry';
    const string FIELD_ERROR_MESSAGE = 'error_message';
    const string FIELD_LOG_LEVEL = 'log_level';
    const string FIELD_DATA = 'data';
    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';

    const string RELATION_CONSUMER_PRODUCT = 'consumerProduct';
    const string RELATION_QA_AUTOMATION_RULE = 'qaAutomationRule';

    const int LEVEL_LOG = 1;
    const int LEVEL_ERROR = 2;
    const int LEVEL_APPROVE = 3;

    const array LEVEL_STRINGS = [
        self::LEVEL_LOG => "log",
        self::LEVEL_ERROR => "error",
    ];

    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];
    protected $casts = [
        self::FIELD_DATA => 'array',
    ];

    /**
     * @return BelongsTo
     */
    public function consumerProduct(): BelongsTo
    {
        return $this->belongsTo(ConsumerProduct::class, self::FIELD_CONSUMER_PRODUCT_ID, ConsumerProduct::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function qaAutomationRule(): BelongsTo
    {
        return $this->belongsTo(QAAutomationRule::class, self::FIELD_QA_AUTOMATION_RULE_ID, QAAutomationRule::FIELD_ID);
    }
}
