<?php

namespace App\Models\QAAutomation;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * Class QAAutomationRule
 *
 * @package App\Models
 *
 * @property int $id
 * @property int $type
 * @property string $expression
 * @property array $fields
 * @property bool $enabled
 * @property array $data
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $deleted_at
 */
class QAAutomationRule extends Model
{
    use SoftDeletes, LogsActivity;

    const string TABLE = 'qa_automation_rules';

    const string FIELD_ID = 'id';
    const string FIELD_TYPE = 'type';
    const string FIELD_EXPRESSION = 'expression';
    const string FIELD_FIELDS = 'fields';
    const string FIELD_MATCH_SUCCESS = 'match_success';
    const string FIELD_ENABLED = 'enabled';
    const string FIELD_DATA = 'data';
    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';
    const string FIELD_DELETED_AT = 'deleted_at';

    const int TYPE_REGEX = 1;
    const int TYPE_PHONE_UNVERIFIED = 2;

    const array TYPE_STRINGS = [
        self::TYPE_REGEX => "regex",
        self::TYPE_PHONE_UNVERIFIED => 'phone_unverified',
    ];

    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];
    protected $casts = [
        self::FIELD_FIELDS => 'array',
        self::FIELD_DATA => 'array',
    ];

    /**
     * Activity log tracking
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['type', 'expression', 'fields', 'enabled', 'data'])
            ->useLogName('qa_automation_rules')
            ->logOnlyDirty()
            ->setDescriptionForEvent(fn(string $eventName) => "Post has been {$eventName}");
    }
}
