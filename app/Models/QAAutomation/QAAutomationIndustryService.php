<?php

namespace App\Models\QAAutomation;

use App\Models\Odin\IndustryService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

/**
 * Class QAAutomationIndustryService
 *
 * @package App\Models
 *
 * @property int $id
 * @property int $industry_service_id
 * @property int $type
 * @property bool $enabled
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read IndustryService $industryService
 */
class QAAutomationIndustryService extends Model
{
    use LogsActivity;

    const string TABLE = 'qa_automation_industry_services';

    const string FIELD_ID = 'id';
    const string FIELD_INDUSTRY_SERVICE_ID = 'industry_service_id';
    const string FIELD_TYPE = 'type';
    const string FIELD_ENABLED = 'enabled';
    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';

    const string RELATION_INDUSTRY_SERVICE    = 'industryService';

    const int TYPE_REGEX = 1;
    const int TYPE_PHONE_UNVERIFIED = 2;

    const array TYPE_STRINGS = [
        self::TYPE_REGEX => "regex",
        self::TYPE_PHONE_UNVERIFIED => 'phone_unverified',
    ];

    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function industryService(): BelongsTo
    {
        return $this->belongsTo(IndustryService::class, self::FIELD_INDUSTRY_SERVICE_ID, IndustryService::FIELD_ID);
    }

    /**
     * Activity log tracking
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['industry_service_id', 'enabled', 'type'])
            ->useLogName('qa_automation_industry_services')
            ->logOnlyDirty()
            ->setDescriptionForEvent(fn(string $eventName) => "Post has been {$eventName}");
    }
}
