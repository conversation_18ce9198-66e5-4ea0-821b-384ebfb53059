<?php

namespace App\Models;

use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\MarketingLogType;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int $id
 * @property string $message
 * @property MarketingLogType $namespace
 * @property LogLevel $level
 * @property string $stack_trace
 * @property array $context
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class MarketingLog extends BaseModel
{
    use HasFactory;

    const string TABLE = 'marketing_logs';

    const string FIELD_ID          = 'id';
    const string FIELD_MESSAGE     = 'message';
    const string FIELD_NAMESPACE   = 'namespace';
    const string FIELD_LEVEL       = 'level';
    const string FIELD_STACK_TRACE = 'stack_trace';
    const string FIELD_CONTEXT     = 'context';
    const string FIELD_CREATED_AT  = 'created_at';
    const string FIELD_UPDATED_AT  = 'updated_at';
    const string RELATION_RELATIONS = 'relations';

    protected $fillable = [
        self::FIELD_MESSAGE,
        self::FIELD_NAMESPACE,
        self::FIELD_LEVEL,
        self::FIELD_STACK_TRACE,
        self::FIELD_CONTEXT,
    ];

    protected $casts = [
        self::FIELD_CONTEXT   => 'array',
        self::FIELD_LEVEL     => LogLevel::class,
        self::FIELD_NAMESPACE => MarketingLogType::class,
    ];

    public function relations(): HasMany
    {
        return $this->hasMany(MarketingLogRelation::class);
    }
}
