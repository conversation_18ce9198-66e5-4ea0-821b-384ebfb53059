<?php

namespace App\Models\Deepgram;

use App\Models\Conference\ConferenceTranscript;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property int $conference_transcript_id
 * @property string|null $deepgram_request_id
 * @property Carbon|null $deepgram_requested_at
 * @property string|null $deepgram_language
 * @property string|null $deepgram_average_sentiment
 * @property float|null $deepgram_average_sentiment_score
 * @property string|null $deepgram_summary
 *
 * @property-read ConferenceTranscript $conference_transcript
 * @property-read Collection|ConferenceTranscriptDeepgramRecordSentiment[] $sentiments
 * @property-read Collection|ConferenceTranscriptDeepgramRecordTopic[] $topics
 *
 * @mixin \Eloquent|\Illuminate\Database\Eloquent\Builder
 */
class ConferenceTranscriptDeepgramRecord extends Model
{
    use HasFactory;

    protected $fillable = [
        'conference_transcript_id',
        'deepgram_request_id',
        'deepgram_requested_at',
        'deepgram_language',
        'deepgram_average_sentiment',
        'deepgram_average_sentiment_score',
        'deepgram_summary',
    ];

    protected $casts = [
        'deepgram_requested_at' => 'datetime',
    ];

    /**
     * @see $conference_transcript
     */
    public function conference_transcript(): BelongsTo
    {
        return $this->belongsTo(ConferenceTranscript::class);
    }

    /**
     * @see $topics
     */
    public function topics(): HasMany
    {
        return $this->hasMany(ConferenceTranscriptDeepgramRecordTopic::class);
    }

    /**
     * @see $sentiments
     */
    public function sentiments(): HasMany
    {
        return $this->hasMany(ConferenceTranscriptDeepgramRecordSentiment::class);
    }
}
