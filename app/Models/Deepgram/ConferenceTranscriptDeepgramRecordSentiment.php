<?php

namespace App\Models\Deepgram;

use App\Models\Conference\ConferenceTranscript;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $conference_transcript_deepgram_record_id
 * @property int $conference_transcript_id
 * @property string $sentiment
 * @property float $sentiment_score
 * @property string $text
 *
 * @property-read ConferenceTranscriptDeepgramRecord $deepgram_record
 * @property-read ConferenceTranscript $conference_transcript
 *
 * @mixin \Eloquent|\Illuminate\Database\Eloquent\Builder
 * @see \Database\Factories\Deepgram\ConferenceTranscriptDeepgramRecordSentimentFactory
 */
class ConferenceTranscriptDeepgramRecordSentiment extends Model
{
    use HasFactory;

    protected $fillable = [
        'conference_transcript_deepgram_record_id',
        'conference_transcript_id',
        'sentiment',
        'sentiment_score',
        'text',
    ];
}
