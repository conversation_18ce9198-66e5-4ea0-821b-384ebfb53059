<?php

namespace App\Models\Deepgram;

use App\Models\Conference\ConferenceTranscript;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $conference_transcript_deepgram_record_id
 * @property int $conference_transcript_id
 * @property string $topic
 * @property float $confidence_score
 * @property string $text
 *
 * @property-read ConferenceTranscriptDeepgramRecord $deepgram_record
 * @property-read ConferenceTranscript $conference_transcript
 *
 * @mixin \Eloquent|\Illuminate\Database\Eloquent\Builder
 * @see \Database\Factories\Deepgram\ConferenceTranscriptDeepgramRecordTopicFactory
 */
class ConferenceTranscriptDeepgramRecordTopic extends Model
{
    use HasFactory;

    protected $fillable = [
        'conference_transcript_deepgram_record_id',
        'conference_transcript_id',
        'topic',
        'confidence_score',
        'text',
    ];
}
