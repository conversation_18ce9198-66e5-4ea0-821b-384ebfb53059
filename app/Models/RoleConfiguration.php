<?php

namespace App\Models;

use App\Models\Odin\Industry;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use InvalidArgumentException;
use Spatie\Permission\Models\Role;

/**
 * @property int $id
 * @property int $user_id
 * @property int $role_id
 * @property array $data
 *
 * @property-read Role $role
 * @property-read User $user
 */
class RoleConfiguration extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'role_id',
        'data',
    ];

    protected $casts = [
        'data' => 'array',
    ];

    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function config(string $name) {
        return $this->data[$name] ?? null;
    }

    public static function displayNameFor(string $key): string
    {
        return str($key)->headline();
    }

    public static function typeFor(string $key): string
    {
        return match ($key) {
            'industries' => 'multiselect',
            default => 'input',
        };
    }

    public static function optionsFor(string $key): array|null
    {
        return match ($key) {
            'industries' => Industry::query()->orderBy(Industry::FIELD_ID)->get([Industry::FIELD_ID, Industry::FIELD_NAME])->toArray(),
            default => null,
        };
    }

    public static function placeholderFor(string $key): string|null
    {
        return match ($key) {
            'industries' => 'All Industries',
            default => null,
        };
    }
}
