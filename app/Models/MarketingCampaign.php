<?php

namespace App\Models;

use App\Enums\MarketingCampaigns\MarketingCampaignStatus;
use App\Enums\MarketingCampaigns\MarketingCampaignType;
use Carbon\Carbon;
use Database\Factories\MarketingCampaigns\MarketingCampaignFactory;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property string name
 * @property string description
 * @property string external_reference
 * @property string external_code
 * @property MarketingCampaignType type
 * @property MarketingCampaignStatus status
 * @property Carbon sent_at
 * @property array callback_payload
 * @property int created_by
 * @property Carbon updated_by
 * @property Carbon created_at
 * @property Carbon updated_at
 * @property array metrics
 * @property array configuration
 * @property bool processing
 *
 * @property-read Collection<MarketingCampaignConsumer> marketingCampaignConsumers
 * @property-read User createdBy
 * @property-read Collection<MarketingLog> logs
 */
class MarketingCampaign extends BaseModel
{
    use HasFactory;

    const string TABLE = 'marketing_campaigns';

    const string FIELD_ID                 = 'id';
    const string FIELD_NAME               = 'name';
    const string FIELD_DESCRIPTION        = 'description';
    const string FIELD_EXTERNAL_REFERENCE = 'external_reference';
    const string FIELD_CODE               = 'external_code';
    const string FIELD_TYPE               = 'type';
    const string FIELD_STATUS             = 'status';

    /**
     * this field is ambiguous,
     * mailchimp updates sent at when it's sent
     * internal uses to determine when to send campaign
     */
    const string FIELD_SENT_AT            = 'sent_at';
    const string FIELD_CALLBACK_PAYLOAD   = 'callback_payload';
    const string FIELD_CREATED_BY         = 'created_by';
    const string FIELD_UPDATED_BY         = 'updated_by';
    const string FIELD_CREATED_AT         = 'created_at';
    const string FIELD_UPDATED_AT         = 'updated_at';
    const string FIELD_METRICS            = 'metrics';
    const string FIELD_CONFIGURATION      = 'configuration';
    const string FIELD_PROCESSING         = 'processing';

    const string RELATION_MARKETING_CAMPAIGN_CONSUMERS = 'marketingCampaignConsumers';
    const string RELATION_CREATED_BY                   = 'createdBy';
    const string RELATION_LOGS = 'logs';

    protected $casts = [
        self::FIELD_STATUS           => MarketingCampaignStatus::class,
        self::FIELD_TYPE             => MarketingCampaignType::class,
        self::FIELD_SENT_AT          => 'datetime',
        self::FIELD_METRICS          => 'array',
        self::FIELD_CALLBACK_PAYLOAD => 'array',
        self::FIELD_CONFIGURATION    => 'array'
    ];

    protected $guarded = [
        self::FIELD_ID,
    ];

    /**
     * @return MarketingCampaignFactory|Factory
     */
    protected static function newFactory(): MarketingCampaignFactory|Factory
    {
        return MarketingCampaignFactory::new();
    }

    /**
     * @return HasMany
     */
    public function marketingCampaignConsumers(): HasMany
    {
        return $this->hasMany(
            MarketingCampaignConsumer::class,
            MarketingCampaignConsumer::FIELD_MARKETING_CAMPAIGN_ID,
            self::FIELD_ID
        );
    }

    /**
     * @return HasOne
     */
    public function createdBy(): HasOne
    {
        return $this->hasOne(
            User::class,
            User::FIELD_ID,
            self::FIELD_CREATED_BY,
        );
    }

    /**
     * @return MorphToMany
     */
    public function logs(): MorphToMany
    {
        return $this->morphToMany(MarketingLog::class, 'relation', MarketingLogRelation::TABLE);
    }
}
