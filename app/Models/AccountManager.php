<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class AccountManager
 *
 * @package App\Models
 *
 * @property integer $id
 * @property integer $user_id
 * @property integer $type
 * @property bool $include_in_sales_round_robin
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read User $user
 * @property-read AccountManagerClient[]|Collection $clients
 */
class AccountManager extends Model
{
    use SoftDeletes, HasFactory;

    const TABLE = 'account_managers';

    const FIELD_ID                           = 'id';
    const FIELD_USER_ID                      = 'user_id';
    const FIELD_TYPE                         = 'type';
    const FIELD_STATUS                       = 'status';
    const FIELD_INCLUDE_IN_SALES_ROUND_ROBIN = 'include_in_sales_round_robin';
    const FIELD_CREATED_AT                   = 'created_at';
    const FIELD_UPDATED_AT                   = 'updated_at';
    const FIELD_DELETED_AT                   = 'deleted_at';

    const RELATION_USER    = 'user';
    const RELATION_CLIENTS = 'clients';

    const TYPE_JUNIOR = 0;
    const TYPE_SENIOR = 1;

    const TYPES = [
        self::TYPE_JUNIOR => "junior",
        self::TYPE_SENIOR => "senior"
    ];

    const STATUS_INACTIVE = 0;
    const STATUS_ACTIVE   = 1;
    const STATUSES = [
        self::STATUS_INACTIVE,
        self::STATUS_ACTIVE,
    ];

    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function clients(): HasMany
    {
        return $this->hasMany(AccountManagerClient::class, AccountManagerClient::FIELD_ACCOUNT_MANAGER_ID, self::FIELD_ID);
    }
}
