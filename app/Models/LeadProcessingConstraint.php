<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property string $type
 * @property string $key
 * @property string $name
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class LeadProcessingConstraint extends BaseModel
{
    const TABLE = 'lead_processing_constraints';

    const FIELD_ID         = 'id';
    const FIELD_TYPE       = 'type';
    const FIELD_KEY        = 'key';
    const FIELD_NAME       = 'name';
    const FIELD_CREATED_AT = 'created_at';
    const FIELD_UPDATED_AT = 'updated_at';

    const CONSTRAINT_TYPE_SORT   = 'sort';
    const CONSTRAINT_TYPE_FILTER = 'filter';
    const CONSTRAINT_TYPE_BUCKET = 'bucket';

    const CONSTRAINT_SUPER_PREMIUM             = 'super_premium';
    const CONSTRAINT_BUDGET                    = 'budget';
    const CONSTRAINT_LAST_ROUND                = 'last_round';
    const CONSTRAINT_REVENUE                   = 'revenue';
    const CONSTRAINT_OLDEST_FIRST              = 'oldest_first';
    const CONSTRAINT_TIMEFRAME                 = 'timeframe';
    const CONSTRAINT_CONTACT_BUFFERS           = 'contact_buffers';
    const CONSTRAINT_LOCAL_HOURS               = 'local_hours';
    const CONSTRAINT_AVAILABLE_BUDGET          = 'available_budget';
    const CONSTRAINT_AVAILABLE_NO_LIMIT_BUDGET = 'available_no_limit_budget';

    protected $table = self::TABLE;
}
