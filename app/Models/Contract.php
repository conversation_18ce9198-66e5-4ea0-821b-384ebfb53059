<?php

namespace App\Models;

use App\Enums\ContractProvider;
use App\Models\Odin\Website;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $contract_provider_id
 * @property boolean $active
 * @property int $contract_key_id
 * @property string $description
 * @property int $website_id
 * @property ContractProvider $contract_provider
 *
 * @property-read Website $website
 * @property-read ContractKey $contractKey
 */
class Contract extends Model
{
    use SoftDeletes;

    const string TABLE = 'contracts';

    const string FIELD_ID                      = 'id';
    const string FIELD_CONTRACT_PROVIDER_ID    = 'contract_provider_id';
    const string FIELD_ACTIVE                  = 'active';
    const string FIELD_CONTRACT_KEY_ID         = 'contract_key_id';
    const string FIELD_DESCRIPTION             = 'description';
    const string FIELD_WEBSITE_ID              = 'website_id';
    const string FIELD_CONTRACT_PROVIDER       = 'contract_provider';

    const string RELATION_WEBSITE              = 'website';
    const string RELATION_CONTRACT_KEY         = 'contractKey';
    const string RELATION_COMPANY_CONTRACTS    = 'companyContracts';

    protected $guarded = [ self::FIELD_ID ];

    protected $casts = [
        self::FIELD_CONTRACT_PROVIDER => ContractProvider::class,
    ];

    /**
     * @return BelongsTo
     */
    public function website(): BelongsTo
    {
        return $this->belongsTo(Website::class, self::FIELD_WEBSITE_ID, Website::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function contractKey(): BelongsTo
    {
        return $this->belongsTo(ContractKey::class, self::FIELD_CONTRACT_KEY_ID, ContractKey::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function CompanyContracts(): HasMany
    {
        return $this->hasMany(CompanyContract::class, CompanyContract::FIELD_CONTRACT_ID, self::FIELD_ID);
    }

}
