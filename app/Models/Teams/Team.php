<?php

namespace App\Models\Teams;

use App\Enums\Team\TeamName;
use App\Models\BaseModel;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * @property int $id
 * @property TeamName $name
 * @property string $description
 * @property int $team_type_id
 * @property int $parent_team_id
 *
 * @property-read TeamLeader $leader
 * @property-read Collection<TeamMember> $members
 * @property-read TeamType $teamType
 */
class Team extends BaseModel
{
    use HasFactory, SoftDeletes, LogsActivity;

    const TABLE = 'teams';

    const FIELD_ID              = 'id';
    const FIELD_NAME            = 'name';
    const FIELD_DESCRIPTION     = 'description';
    const FIELD_TEAM_TYPE_ID    = 'team_type_id';
    const FIELD_PARENT_TEAM_ID  = 'parent_team_id';

    const RELATION_TEAM_LEADER  = 'leader';
    const RELATION_TEAM_MEMBERS = 'members';
    const RELATION_TEAM_TYPE    = 'teamType';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];
    protected $casts = [self::FIELD_NAME => TeamName::class];

    /**
     * @return HasOne
     */
    public function leader(): HasOne
    {
        return $this->hasOne(TeamLeader::class, TeamLeader::FIELD_TEAM_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function members(): HasMany
    {
        return $this->hasMany(TeamMember::class, TeamMember::FIELD_TEAM_ID, self::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function teamType(): BelongsTo
    {
        return $this->belongsTo(TeamType::class, self::FIELD_TEAM_TYPE_ID, TeamType::FIELD_ID);
    }

    /**
     * @return User|null
     */
    public function reportsTo(): ?User
    {
        return $this->{self::RELATION_TEAM_LEADER}?->{TeamLeader::RELATION_REPORTS_TO};
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                self::FIELD_NAME,
                self::FIELD_DESCRIPTION,
                self::FIELD_TEAM_TYPE_ID,
                self::FIELD_PARENT_TEAM_ID,
            ])
            ->useLogName('team')
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
}
