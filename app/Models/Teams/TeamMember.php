<?php

namespace App\Models\Teams;

use App\Models\BaseModel;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * @property int $id
 * @property string $title
 * @property int $user_id
 * @property int $team_id
 * @property int $reports_to_user_id
 *
 * @property-read Team $team
 * @property-read User $user
 * @property-read Collection<TeamMember> $teamMembers
 * @property-read TeamLeader $teamLeader
 * @property-read User $reportsTo
 */
class TeamMember extends BaseModel
{
    use HasFactory, SoftDeletes, LogsActivity;

    const TABLE = 'team_members';

    const FIELD_ID          = 'id';
    const FIELD_TITLE       = 'title';
    const FIELD_USER_ID     = 'user_id';
    const FIELD_TEAM_ID     = 'team_id';

    const RELATION_TEAM          = 'team';
    const RELATION_USER          = 'user';
    const RELATION_OTHER_MEMBERS = 'otherMembers';
    const RELATION_TEAM_LEADER   = 'leader';
    const RELATION_REPORTS_TO    = 'reportsTo';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class, self::FIELD_TEAM_ID, Team::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }

    /**
     * @return HasManyThrough
     */
    public function otherMembers(): HasManyThrough
    {
        return $this->hasManyThrough(
            TeamMember::class,
            Team::class,
            Team::FIELD_ID,
            TeamMember::FIELD_TEAM_ID,
            self::FIELD_TEAM_ID,
            Team::FIELD_ID
        )->where(self::TABLE.".".self::FIELD_ID, '!=', $this->{TeamMember::FIELD_ID});
    }

    /**
     * @return HasOneThrough
     */
    public function leader(): HasOneThrough
    {
        return $this->hasOneThrough(
            TeamLeader::class,
            Team::class,
            Team::FIELD_ID,
            TeamLeader::FIELD_TEAM_ID,
            self::FIELD_TEAM_ID,
            Team::FIELD_ID
        );
    }

    /**
     * Fetch the User this team member reports to
     * @return User | null
     */
    public function reportsTo(): ?User
    {
        return $this->{self::RELATION_TEAM_LEADER}?->{TeamLeader::RELATION_USER};
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                self::FIELD_USER_ID,
                self::FIELD_TEAM_ID
            ])
            ->useLogName('team_member')
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
}
