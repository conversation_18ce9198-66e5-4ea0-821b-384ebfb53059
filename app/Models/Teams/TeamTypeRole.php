<?php

namespace App\Models\Teams;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Permission\Models\Role;

/**
 * @property int $id
 * @property int $team_type_id
 * @property int $role_id
 * @property bool $is_leader_role
 *
 * @property-read TeamType $teamType
 * @property-read Role $role
 */
class TeamTypeRole extends BaseModel
{
    const TABLE = 'team_type_role';

    const FIELD_ID              = 'id';
    const FIELD_TEAM_TYPE_ID    = 'team_type_id';
    const FIELD_ROLE_ID         = 'role_id';
    const FIELD_IS_LEADER_ROLE  = 'is_leader_role';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class, self::FIELD_ROLE_ID, 'id');
    }

    /**
     * @return BelongsTo
     */
    public function teamType(): BelongsTo
    {
        return $this->belongsTo(TeamType::class, self::FIELD_TEAM_TYPE_ID, TeamType::FIELD_ID);
    }

}
