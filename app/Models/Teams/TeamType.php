<?php

namespace App\Models\Teams;

use App\Enums\Team\TeamType as TeamTypeEnum;
use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Spatie\Permission\Models\Role;

/**
 * @property int $id
 * @property TeamTypeEnum $name
 * @property string $description
 *
 * @property-read Collection<Role> $leaderRoles
 * @property-read Collection<Role> $memberRoles
 */
class TeamType extends BaseModel
{
    use HasFactory;

    const TABLE = 'team_types';

    const FIELD_ID          = 'id';
    const FIELD_NAME        = 'name';
    const FIELD_DESCRIPTION = 'description';

    const RELATION_LEADER_ROLES = 'leaderRoles';
    const RELATION_MEMBER_ROLES = 'memberRoles';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];
    protected $casts = [self::FIELD_NAME => TeamTypeEnum::class];

    /**
     * @return BelongsToMany
     */
    public function leaderRoles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, TeamTypeRole::TABLE, TeamTypeRole::FIELD_TEAM_TYPE_ID, TeamTypeRole::FIELD_ROLE_ID)
            ->wherePivot(TeamTypeRole::FIELD_IS_LEADER_ROLE, true);
    }

    /**
     * @return BelongsToMany
     */
    public function memberRoles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, TeamTypeRole::TABLE, TeamTypeRole::FIELD_TEAM_TYPE_ID, TeamTypeRole::FIELD_ROLE_ID)
            ->wherePivot(TeamTypeRole::FIELD_IS_LEADER_ROLE, false);
    }

}
