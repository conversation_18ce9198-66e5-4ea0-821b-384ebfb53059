<?php

namespace App\Models\Teams;

use App\Models\BaseModel;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * @property int $id
 * @property string $title
 * @property int $user_id
 * @property int $team_id
 * @property int $reports_to_user_id
 *
 * @property-read Team $team
 * @property-read User $user
 * @property-read Collection<TeamMember> $teamMembers
 * @property-read User $reportsTo
 */
class TeamLeader extends BaseModel
{
    use HasFactory, SoftDeletes, LogsActivity;

    const TABLE = 'team_leaders';

    const FIELD_ID          = 'id';
    const FIELD_TITLE       = 'title';
    const FIELD_USER_ID     = 'user_id';
    const FIELD_TEAM_ID     = 'team_id';
    const FIELD_REPORTS_TO  = 'reports_to_user_id';

    const RELATION_TEAM         = 'team';
    const RELATION_USER         = 'user';
    const RELATION_TEAM_MEMBERS = 'members';
    const RELATION_REPORTS_TO   = 'reportsTo';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class, self::FIELD_TEAM_ID, Team::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }

    /**
     * @return HasManyThrough
     */
    public function members(): HasManyThrough
    {
        return $this->hasManyThrough(
            TeamMember::class,
            Team::class,
            Team::FIELD_ID,
            TeamMember::FIELD_TEAM_ID,
            self::FIELD_TEAM_ID,
            Team::FIELD_ID
        );
    }

    /**
     * @return BelongsTo
     */
    public function reportsTo(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_REPORTS_TO, User::FIELD_ID);
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                self::FIELD_USER_ID,
                self::FIELD_TEAM_ID
            ])
            ->useLogName('team_leader')
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
}
