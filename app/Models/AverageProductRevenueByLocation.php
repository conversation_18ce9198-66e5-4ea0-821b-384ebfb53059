<?php

namespace App\Models;

/**
 * @property int $id
 * @property int $county_location_id
 * @property int $zip_code_location_id
 * @property ?int $industry_id
 * @property int $product_id
 * @property float $average_revenue
 */
class AverageProductRevenueByLocation extends BaseModel
{
    const string TABLE = 'average_product_revenue_by_locations';

    const string FIELD_ID                   = 'id';
    const string FIELD_COUNTY_LOCATION_ID   = 'county_location_id';
    const string FIELD_ZIP_CODE_LOCATION_ID = 'zip_code_location_id';
    const string FIELD_INDUSTRY_ID          = 'industry_id';
    const string FIELD_PRODUCT_ID           = 'product_id';
    const string FIELD_AVERAGE_REVENUE      = 'average_revenue';

    protected $table =  self::TABLE;
    protected $guarded = [self::FIELD_ID];
}
