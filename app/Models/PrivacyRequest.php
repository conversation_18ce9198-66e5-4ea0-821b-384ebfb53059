<?php

namespace App\Models;

use App\Enums\PrivacyManagement\PrivacyRequestStatuses;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int $id
 * @property string $uuid
 * @property int $approved_by_id
 * @property array $payload
 * @property array $scan_response
 * @property PrivacyRequestStatuses $status
 * @property string $source
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $deleted_at
 */
class PrivacyRequest extends BaseModel
{
    use HasFactory;

    const string TABLE = 'privacy_requests';

    const string FIELD_ID                       = 'id';
    const string FIELD_UUID                     = 'uuid';
    const string FIELD_APPROVED_BY_ID           = 'approved_by_id';
    const string FIELD_PAYLOAD                  = 'payload';
    const string FIELD_SCAN_RESPONSE            = 'scan_response';
    const string FIELD_STATUS                   = 'status';
    const string FIELD_SOURCE                   = 'source';
    const string FIELD_CREATED_AT               = 'created_at';
    const string FIELD_UPDATED_AT               = 'updated_at';
    const string FIELD_DELETED_AT               = 'deleted_at';
    const string JSON_PAYLOAD_FIELD_FIRST_NAME  = 'first_name';
    const string JSON_PAYLOAD_FIELD_LAST_NAME   = 'last_name';
    const string JSON_PAYLOAD_FIELD_EMAIL       = 'email';
    const string JSON_PAYLOAD_FIELD_PHONE       = 'phone';
    const string JSON_PAYLOAD_FIELD_DESCRIPTION = 'description';
    const string JSON_PAYLOAD_FIELD_ADDRESS     = 'address';

    const string RELATION_REDACTION_RECORDS = 'records';

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_STATUS        => PrivacyRequestStatuses::class,
        self::FIELD_PAYLOAD       => 'array',
        self::FIELD_SCAN_RESPONSE => 'array',
    ];

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_APPROVED_BY_ID, User::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function records(): hasMany
    {
        return $this->hasMany(PrivacyRequestRedactedRecords::class, PrivacyRequestRedactedRecords::FIELD_PRIVACY_REQUEST_ID, self::FIELD_ID);
    }
}
