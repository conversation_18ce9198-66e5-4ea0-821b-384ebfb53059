<?php

namespace App\Models;

use App\Enums\MarketingCampaigns\MarketingCampaignConsumerStatus;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use Carbon\Carbon;
use Database\Factories\MarketingCampaigns\MarketingCampaignConsumerFactory;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property int $marketing_campaign_id
 * @property string $consumer_reference
 * @property string $external_reference
 * @property string $marketing_campaign_reference
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $revalidated_at
 * @property ?Carbon $sent_at
 * @property MarketingCampaignConsumerStatus $status
 * @property ?int $cloned_consumer_product_id
 * @property ?int $sent_from_domain_id
 *
 * @property-read Consumer $consumer
 * @property-read MarketingCampaign $marketingCampaign
 * @property-read ?ConsumerProduct $clonedConsumerProduct
 * @property-read Collection<MarketingLog> logs
 */
class MarketingCampaignConsumer extends BaseModel
{
    use HasFactory;

    const string TABLE = 'marketing_campaign_consumers';

    const string FIELD_ID                           = 'id';
    const string FIELD_MARKETING_CAMPAIGN_ID        = 'marketing_campaign_id';
    const string FIELD_CONSUMER_REFERENCE           = 'consumer_reference';
    const string FIELD_EXTERNAL_REFERENCE           = 'external_reference';
    const string FIELD_MARKETING_CAMPAIGN_REFERENCE = 'marketing_campaign_reference';
    const string FIELD_STATUS                       = 'status';

    const string FIELD_CREATED_AT                 = 'created_at';
    const string FIELD_UPDATED_AT                 = 'updated_at';
    const string FIELD_SENT_AT                    = 'sent_at';
    const string FIELD_DELIVERED_AT               = 'delivered_at';
    const string FIELD_OPENED_AT                  = 'opened_at';
    const string FIELD_CLICKED_AT                 = 'clicked_at';
    const string FIELD_REVALIDATED_AT             = 'revalidated_at';
    const string FIELD_CLONED_CONSUMER_PRODUCT_ID = 'cloned_consumer_product_id';
    const string FIELD_SENT_FROM_DOMAIN_ID        = 'sent_from_domain_id';

    const string RELATION_MARKETING_CAMPAIGN      = 'marketingCampaign';
    const string RELATION_CONSUMER                = 'consumer';
    const string RELATION_CLONED_CONSUMER_PRODUCT = 'clonedConsumerProduct';
    const string RELATION_LOGS                    = 'logs';

    protected $guarded = [
        self::FIELD_ID,
    ];

    protected $casts = [
        self::FIELD_STATUS => MarketingCampaignConsumerStatus::class,
    ];

    /**
     * @return MarketingCampaignConsumerFactory|Factory
     */
    protected static function newFactory(): MarketingCampaignConsumerFactory|Factory
    {
        return MarketingCampaignConsumerFactory::new();
    }

    /**
     * @return BelongsTo
     */
    public function marketingCampaign(): BelongsTo
    {
        return $this->belongsTo(
            MarketingCampaign::class,
            self::FIELD_MARKETING_CAMPAIGN_ID,
            MarketingCampaign::FIELD_ID,
        );
    }

    /**
     * @return BelongsTo
     */
    public function consumer(): BelongsTo
    {
        return $this->belongsTo(
            Consumer::class,
            self::FIELD_CONSUMER_REFERENCE,
            Consumer::FIELD_REFERENCE
        );
    }

    /**
     * @return HasOne
     */
    public function clonedConsumerProduct(): HasOne
    {
        return $this->hasOne(
            ConsumerProduct::class,
            ConsumerProduct::FIELD_ID,
            self::FIELD_CLONED_CONSUMER_PRODUCT_ID,
        );
    }

    public function logs(): MorphToMany
    {
        return $this->morphToMany(MarketingLog::class, 'relation', MarketingLogRelation::TABLE);
    }
}
