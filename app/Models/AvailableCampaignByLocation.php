<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\AvailableCompanyByLocation
 * @property int $id
 * @property int $campaign_id
 * @property int $location_id
 * @property int $county_location_id
 * @property int $industry_id
 */
class AvailableCampaignByLocation extends Model
{
    use HasFactory;

    const string TABLE = 'available_campaign_by_locations';

    const string FIELD_ID                 = 'id';
    const string FIELD_CAMPAIGN_ID        = 'campaign_id';
    const string FIELD_COMPANY_ID         = 'company_id';
    const string FIELD_LOCATION_ID        = 'location_id';
    const string FIELD_COUNTY_LOCATION_ID = 'county_location_id';
    const string FIELD_INDUSTRY_ID        = 'industry_id';
    const string FIELD_CREATED_AT         = 'created_at';
    const string FIELD_UPDATED_AT         = 'updated_at';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];
}
