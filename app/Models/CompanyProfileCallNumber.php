<?php

namespace App\Models;

use App\Models\Odin\Company;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @Property int $id
 * @Property int $company_id
 * @Property int $phone_id
 * @Property boolean $in_use
 * @Property Carbon|null $in_use_since
 *
 * @property-read Phone $phone
 * @property-read Company $company
 *
 * @method static Builder notInUse
 * @method static Builder inUse
 */
class CompanyProfileCallNumber extends Model
{
    const string TABLE = 'company_profile_call_numbers';

    const string FIELD_ID = 'id';
    const string FIELD_PHONE_COMPANY_ID = 'company_id';
    const string FIELD_PHONE_ID = 'phone_id';
    const string FIELD_IN_USE = 'in_use';
    const string FIELD_IN_USE_SINCE = 'in_use_since';

    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];
    protected $casts = [
        self::FIELD_IN_USE => 'boolean',
        self::FIELD_IN_USE_SINCE => 'datetime'
    ];

    public function phone(): BelongsTo
    {
        return $this->belongsTo(Phone::class);
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function scopeNotInUse(Builder $query): Builder
    {
        return $query->where(self::FIELD_IN_USE, false);
    }

    public function scopeInUse(Builder $query): Builder
    {
        return $query->where(self::FIELD_IN_USE, true);
    }
}
