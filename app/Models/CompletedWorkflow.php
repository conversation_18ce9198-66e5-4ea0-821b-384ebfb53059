<?php

namespace App\Models;

use App\Database\Casts\AsWorkflowPayload;
use App\Models\Legacy\EloquentQuote;
use App\Workflows\WorkflowPayload;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @property int $id
 * @property int $workflow_id
 * @property int $running_workflow_id
 * @property WorkflowPayload $payload
 * @property int|null $event_lead_id
 *
 * @property-read User $user
 * @property-read EloquentQuote|null $legacyLead
 */
class CompletedWorkflow extends Model
{
    const TABLE = 'completed_workflows';

    const FIELD_ID                  = 'id';
    const FIELD_WORKFLOW_ID         = 'workflow_id';
    const FIELD_RUNNING_WORKFLOW_ID = 'running_workflow_id';
    const FIELD_PAYLOAD             = 'payload';

    const VIRTUAL_FIELD_EVENT_LEAD_ID    = 'event_lead_id';

    const RELATION_LEGACY_LEAD = 'legacyLead';


    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_PAYLOAD => AsWorkflowPayload::class,
    ];

    /**
     *
     * @return HasOne
     */
    public function legacyLead(): HasOne
    {
        return $this->hasOne(EloquentQuote::class, EloquentQuote::QUOTE_ID, self::VIRTUAL_FIELD_EVENT_LEAD_ID);
    }
}
