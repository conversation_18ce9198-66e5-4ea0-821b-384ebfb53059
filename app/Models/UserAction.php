<?php
namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Support\Str;

/**
 * @property int $id
 * @property int|null $user_id
 * @property string $subject
 * @property string $message
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon|null $display_date
 * @property boolean $pinned
 *
 */

class UserAction extends Model
{
    use HasFactory;

    const TABLE = 'user_actions';

    const FIELD_ID                    = 'id';
    const FIELD_USER_ID               = 'user_id';
    const FIELD_SUBJECT               = 'subject';
    const FIELD_MESSAGE               = 'message';
    const FIELD_DISPLAY_DATE          = 'display_date';
    const FIELD_PINNED                = 'pinned';
    const FIELD_TAG_BY_EMAIL          = 'tag_by_email';
    const RELATION_TAGS                 = 'tags';

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_DISPLAY_DATE => 'date',
        self::FIELD_TAG_BY_EMAIL => 'boolean',
    ];

    /**
     * Convert markdown to HTML
     * @return Attribute
     */
    protected function message(): Attribute {
        return Attribute::make(
            get: fn($value) => Str::markdown($value)
        );
    }

    /**
     * Defines relationship to who user is from.
     *
     * @return HasOne
     */
    public function from(): HasOne
    {
        return $this->hasOne(User::class, User::FIELD_ID, self::FIELD_USER_ID);
    }


    /**
     * @return HasMany
     */
    public function tags(): HasMany
    {
        return $this->hasMany(UserActionTag::class, UserActionTag::FIELD_ACTION_ID, self::FIELD_ID);
    }

}
