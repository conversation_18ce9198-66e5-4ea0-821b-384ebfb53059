<?php

namespace App\Models;

use App\Enums\Advertising\Advertiser;
use App\Enums\Advertising\AdvertisingPlatform;

/**
 * Class DailyAdCost
 *
 * @property int $id
 * @property string $slug
 * @property string $name
 * @property int $industry_id
 * @property int $advertiser
 * @property string $platform
 */
class RevenueStream extends BaseModel
{
    const string TABLE = 'revenue_streams';

    const string FIELD_ID = 'id';
    const string FIELD_SLUG = 'slug';
    const string FIELD_NAME = 'name';
    const string FIELD_INDUSTRY_ID = 'industry_id';
    const string FIELD_ADVERTISER = 'advertiser';
    const string FIELD_PLATFORM = 'platform';
    const string FIELD_CP_CASE = 'cp_case';
    const string FIELD_DAC_CASE = 'dac_case';
    const string FIELD_PRIORITY = 'priority';

    protected $casts = [
        self::FIELD_ADVERTISER => Advertiser::class,
    ];

    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];
}
