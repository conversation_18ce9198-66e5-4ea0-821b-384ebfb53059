<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class UserFilterPreset
 *
 * @property int $id
 * @property int $user_id
 * @property mixed $data
 */
class UserFilterPreset extends BaseModel
{
    const TABLE = 'user_filter_presets';

    const FIELD_ID = 'id';
    const FIELD_USER_ID = 'user_id';
    const FIELD_DATA = 'data';

    const RELATION_USER = 'user';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * Get the user that owns the filter preset,
     *
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_ID, User::FIELD_ID);
    }
}
