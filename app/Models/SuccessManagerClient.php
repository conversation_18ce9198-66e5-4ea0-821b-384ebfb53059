<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class SuccessManagerClient
 *
 * @package App\Models
 *
 * @property int    $id
 * @property int    $success_manager_id
 * @property string $company_reference
 * @property float  $total_spend
 * @property int    $status
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $deleted_at
 *
 * @property-read SuccessManager $successManager
 */
class SuccessManagerClient extends BaseModel
{
    use SoftDeletes;

    const TABLE = 'success_manager_clients';

    const FIELD_ID                 = 'id';
    const FIELD_SUCCESS_MANAGER_ID = 'success_manager_id';
    const FIELD_COMPANY_REFERENCE  = 'company_reference';
    const FIELD_TOTAL_SPEND        = 'total_spend';
    const FIELD_STATUS             = 'status';
    const FIELD_CREATED_AT         = 'created_at';
    const FIELD_UPDATED_AT         = 'updated_at';
    const FIELD_DELETED_AT         = 'deleted_at';

    const RELATION_SUCCESS_MANAGER = 'successManager';

    // todo: confirm status options
    const STATUS_INACTIVE = 0;
    const STATUS_ACTIVE   = 1;

    const STATUSES = [
        self::STATUS_INACTIVE,
        self::STATUS_ACTIVE,
    ];

    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function successManager(): BelongsTo
    {
        return $this->belongsTo(SuccessManager::class, self::FIELD_SUCCESS_MANAGER_ID, SuccessManager::FIELD_ID);
    }
}
