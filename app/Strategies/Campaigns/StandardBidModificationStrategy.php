<?php

namespace App\Strategies\Campaigns;

use App\Contracts\Campaigns\BidModificationStrategyContract;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\ComputedRejectionStatistic;
use App\Models\Odin\Company;
use App\Models\Odin\Product;
use Illuminate\Support\Collection;

class StandardBidModificationStrategy implements BidModificationStrategyContract
{

    const ATTRIBUTE_BID          = 'bid';
    const ATTRIBUTE_MODIFIED_BID = 'modified_bid';

    /** @inheritDoc */
    public function calculateModifiedBidForBudget(Budget $budget): float
    {
        if(!isset($budget->{self::ATTRIBUTE_BID}))
            return 0; // todo: handle absence of appended bid -- fetch bid?

        $companyRejectionFactor = $this->getCompanyRejectionFactor($budget->budgetContainer->campaign->company, $budget->budgetContainer->getProductAsString());
        return $budget->{self::ATTRIBUTE_BID} * $companyRejectionFactor;
    }

    /** @inheritDoc */
    public function appendModifiedBidToBudget(Budget $budget): Budget
    {
        $budget->{self::ATTRIBUTE_MODIFIED_BID} = $this->calculateModifiedBidForBudget($budget);
        return $budget;
    }

    /** @inheritDoc */
    public function appendModifiedBidToBudgets(Collection $budgets): Collection
    {
        $appendedBudgets = collect();
        foreach ($budgets as $budget){
            $appendedBudgets->push($this->appendModifiedBidToBudget($budget));
        }
        return $appendedBudgets;
    }

    /**
     * @param Company $company
     * @param string $product
     * @return float
     */
    private function getCompanyRejectionFactor(Company $company, string $product): float
    {
        $companyRejectionPercent = $company->rejectionStatistics
            ->where(ComputedRejectionStatistic::RELATION_PRODUCT . '.' . Product::FIELD_NAME, ucfirst($product))
            ->first()
            ?->{ComputedRejectionStatistic::FIELD_OVERALL_REJECTION_PERCENTAGE}
            ?? 0;

        return 1000 - $companyRejectionPercent;
    }
}
