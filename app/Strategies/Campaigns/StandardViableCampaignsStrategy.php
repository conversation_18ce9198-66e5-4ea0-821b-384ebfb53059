<?php

namespace App\Strategies\Campaigns;

use App\Contracts\Campaigns\ViableBudgetStrategyContract;
use App\Enums\Odin\Product;
use App\Enums\Odin\QualityTier;
use App\Enums\Odin\SaleTypes;
use App\Exceptions\LeadProcessingException;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use Illuminate\Support\Collection;

class StandardViableCampaignsStrategy implements ViableBudgetStrategyContract
{

    /** @inheritDoc */
    public function getViableCampaignBudgetsForProductVariant(Collection $campaigns, string $product, string $qualityTier, string $salesType): Collection
    {
        $campaignsThatCanAcceptProduct   = $this->getCampaignsThatCanAcceptProduct($campaigns, $product);
        $budgetsThatCanAcceptQualityTier = $this->getBudgetsThatCanAcceptQualityTier($campaignsThatCanAcceptProduct, $qualityTier);
        return $this->getBudgetsThatCanAcceptSalesType($budgetsThatCanAcceptQualityTier, $salesType);
    }

    /**
     * @param Collection<int, CompanyCampaign> $campaigns
     * @param string $product
     * @return Collection<int, CompanyCampaign>
     */
    private function getCampaignsThatCanAcceptProduct(Collection $campaigns, string $product): Collection
    {
        return $campaigns->filter(function (CompanyCampaign $campaign) use ($product) {
            // TODO: handle no budget container
            return $campaign->budgetContainer?->getProductAsString() === strtolower($product) ?? true;
        });
    }

    /**
     * @param Collection<int, CompanyCampaign> $campaigns
     * @param string $qualityTier
     * @return Collection<int, Budget>
     */
    private function getBudgetsThatCanAcceptQualityTier(Collection $campaigns, string $qualityTier): Collection
    {
        $budgets = collect();
        foreach ($campaigns as $campaign) {
            switch ($campaign->budgetContainer->getProductAsString()) {
                case strtolower(Product::DIRECT_LEADS->value):
                case strtolower(Product::LEAD->value):
                    $budgets = $budgets->merge($campaign->budgetContainer->budgets);
                    break;
                case strtolower(Product::APPOINTMENT->value):
                    $budgetKey = QualityTier::tryFrom($qualityTier)->getAppointmentBudgetKey();
                    if (!$budgetKey) break;

                    foreach ($campaign->budgetContainer->budgets as $budget) {
                        if ($budget->key === $budgetKey)
                            $budgets->push($budget);
                    }
                    break;
            }
        }

        return $budgets;
    }

    /**
     * @param Collection<int, Budget> $budgets
     * @param string $salesType
     * @return Collection<int, Budget>
     */
    private function getBudgetsThatCanAcceptSalesType(Collection $budgets, string $salesType): Collection
    {
        return $budgets->filter(function (Budget $budget) use ($salesType) {
            $acceptableSalesTypes = match ($budget->key) {
                'verified'                      => [SaleTypes::EXCLUSIVE->value, SaleTypes::DUO->value, SaleTypes::TRIO->value, SaleTypes::QUAD->value],
                'unverified', 'unverified_only' => [SaleTypes::UNVERIFIED->value],
                'email_only'                    => [SaleTypes::EMAIL_ONLY->value],
                'in_home'                       => [SaleTypes::EXCLUSIVE->value, SaleTypes::DUO->value],
                'online'                        => [SaleTypes::EXCLUSIVE->value, SaleTypes::DUO->value, SaleTypes::TRIO->value],
                'exclusive_only'                => [SaleTypes::EXCLUSIVE->value],
                default                         => throw new LeadProcessingException('Unsupported budget type', "$budget->key is unsupported.")
            };

            return in_array($salesType, $acceptableSalesTypes);
        });
    }
}
