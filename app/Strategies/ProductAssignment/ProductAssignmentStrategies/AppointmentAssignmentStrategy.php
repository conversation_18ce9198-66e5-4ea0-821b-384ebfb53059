<?php

namespace App\Strategies\ProductAssignment\ProductAssignmentStrategies;

use App\ConsumerProcessing\Jobs\AttemptConsumerProjectAllocationJob;
use App\DataModels\Campaigns\ConsumerProject;
use App\Enums\Odin\Product as ProductEnum;
use App\Enums\Odin\QualityTier as QualityTierEnum;
use App\Enums\Odin\SaleTypes as SaleTypesEnum;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyConfiguration;
use App\Repositories\Odin\SaleTypeRepository;
use App\Services\ConsumerProductLifecycleTrackingService;
use App\Services\Odin\Campaigns\CompanyCampaignAppointmentService;
use Illuminate\Support\Collection;
use Throwable;

class AppointmentAssignmentStrategy extends BaseAssignmentStrategy
{
    const string ASSIGNMENT_KEY_CAMPAIGN_ID         = 'company_campaign_id';
    const string ASSIGNMENT_KEY_SCHEDULE_ID         = 'schedule_id';
    const string ASSIGNMENT_KEY_CONSUMER_PRODUCT_ID = 'consumer_product_id';
    const string ASSIGNMENT_KEY_TOTAL               = 'total';
    const string ASSIGNMENT_GROUP_CAMPAIGNS_IDS     = 'campaign_ids';
    const string ASSIGNMENT_GROUP_PRICES            = 'prices';
    const string ASSIGNMENT_GROUP_ASSIGNMENTS       = 'assignments';

    /**
     * @inheritDoc
     * @throws Throwable
     */
    public function calculate(
        ConsumerProject $project,
        Collection $campaigns,
        Collection $assertedProductAssignments,
        ?ConsumerProductLifecycleTrackingService $tracker = null,
    ): Collection
    {
        if ($project->appointmentConsumerProductsCount()) //TODO: do we need to handle appointments attempting to allocate a second time through here?
            return collect();
        else if ($project->createAppointmentConsumerProducts())
            return $this->calculateForProduct(ProductEnum::APPOINTMENT, $project, $campaigns, $assertedProductAssignments, $tracker);
        else
            return collect();
    }

    /**
     * @inheritDoc
     * @throws Throwable
     */
    public function calculateForProduct(
        ProductEnum $expectedProduct,
        ConsumerProject $project,
        Collection $campaigns,
        Collection $assertedProductAssignments,
        ?ConsumerProductLifecycleTrackingService $tracker = null,
    ): Collection
    {
        $campaigns = $this->removeCampaignsForAssertedAssignments($campaigns, $assertedProductAssignments);
        $campaigns = $this->removeCampaignsWhereCompanyHasUndeliveredAssignment($campaigns, $project);
        $campaigns = $this->removeCompaniesNotUsingSchedules($campaigns);

        if ($campaigns->count() < 1)
            return collect();

        $availableScheduleData = $this->getCampaignSchedulesForProject($campaigns, $project);
        $campaigns             = $this->removeCampaignsByAvailability($campaigns, $availableScheduleData);

        if ($campaigns->count() < 1)
            return collect();

        $qualityTier = $this->getAppointmentQualityTier($project);
        $saleType    = $this->getAppointmentSaleType($project, $qualityTier);

        /** @var SaleTypeRepository $saleTypeRepository */
        $saleTypeRepository = app()->make(SaleTypeRepository::class);

        $saleTypes     = $saleTypeRepository->all();

        $viableBudgets                      = $this->getViableBudgetsForProductVariant($campaigns, ProductEnum::APPOINTMENT->value, $qualityTier->value, $saleType->value);
        $viableBudgets                      = $this->cloneBudgetCollection($viableBudgets);
        $viableBudgets                      = $this->addBidToBudgets($viableBudgets, $project->zipCode(), $project->propertyTypeId(), $qualityTier->model()->id, $saleTypes[$saleType->value]);
        $budgetsWithinTolerance             = $this->filterOutBudgetsThatWouldBeExceeded($viableBudgets);
        $budgetsWithinTolerance             = $this->addModifiedBidToBudgets($budgetsWithinTolerance);
        $highestModifiedBidBudgetPerCompany = $this->getHighestModifiedBidBudgetPerCompany($budgetsWithinTolerance);

        $bestAssignment = $this->calculateAppointmentBestRevenueScenario($highestModifiedBidBudgetPerCompany, $availableScheduleData);
        if (!$bestAssignment)
            return collect();

        $budgets = $highestModifiedBidBudgetPerCompany->reduce(function (Collection $output, Budget $budget) use ($bestAssignment) {
            $campaignId = $budget->budgetContainer->company_campaign_id;
            if (in_array($campaignId, array_keys($bestAssignment))) {
                $budget[BaseAssignmentStrategy::CONSUMER_PRODUCT_ID] = $bestAssignment[$campaignId][self::ASSIGNMENT_KEY_CONSUMER_PRODUCT_ID];
                $budget[BaseAssignmentStrategy::SCHEDULE_ID]         = $bestAssignment[$campaignId][self::ASSIGNMENT_KEY_SCHEDULE_ID];
                $output->push($budget);
            }
            return $output;
        }, collect());

        $budgetSet = collect([
            BaseAssignmentStrategy::BUDGETS      => $budgets,
            BaseAssignmentStrategy::PRODUCT      => ProductEnum::APPOINTMENT->value,
            BaseAssignmentStrategy::SALES_TYPE   => $project->getSaleTypeFromConsumer(),
            BaseAssignmentStrategy::QUALITY_TIER => $qualityTier->value,
        ]);

        return $this->convertToProposedAssignments($budgetSet, $project, true);
    }

    /**
     * @param Collection $campaigns
     * @param ConsumerProject $project
     * @return array
     * @throws Throwable
     */
    private function getCampaignSchedulesForProject(Collection $campaigns, ConsumerProject $project): array
    {
        /** @var CompanyCampaignAppointmentService $appointmentService */
        $appointmentService    = app(CompanyCampaignAppointmentService::class);
        $appointmentProducts   = $project->appointmentConsumerProducts();
        $companyAvailabilities = $appointmentService->getCompanyCampaignScheduleAvailability($campaigns, $appointmentProducts);

        return $companyAvailabilities;
    }

    /**
     * @param Collection $campaigns
     * @return Collection
     */
    private function removeCompaniesNotUsingSchedules(Collection $campaigns): Collection
    {
        $companiesWithCalendars = Company::query()
            ->select(Company::TABLE .'.'. Company::FIELD_ID)
            ->join(CompanyConfiguration::TABLE, CompanyConfiguration::TABLE . '.' . CompanyConfiguration::FIELD_COMPANY_ID, '=', Company::TABLE . '.' . Company::FIELD_ID)
            ->whereIn(Company::TABLE .'.'. Company::FIELD_ID, $campaigns->pluck(CompanyCampaign::FIELD_COMPANY_ID)->toArray())
            ->where([
                CompanyConfiguration::FIELD_REQUIRE_APPOINTMENTS_CALENDAR => true,
                CompanyConfiguration::FIELD_APPOINTMENTS_ACTIVE           => true,
            ])->pluck(Company::TABLE .'.'. Company::FIELD_ID)
            ->toArray();

        return $campaigns->filter(fn(CompanyCampaign $campaign) => in_array($campaign->company_id, $companiesWithCalendars));
    }

    /**
     * @param ConsumerProject $project
     * @return QualityTierEnum
     */
    private function getAppointmentQualityTier(ConsumerProject $project): QualityTierEnum
    {
        return match ($project->appointments()->first()->appointment_type) {
            QualityTierEnum::IN_HOME        => QualityTierEnum::IN_HOME,
            default                         => QualityTierEnum::ONLINE,
        };
    }

    /**
     * @param ConsumerProject $project
     * @param QualityTierEnum $qualityTier
     * @return SaleTypesEnum
     */
    private function getAppointmentSaleType(ConsumerProject $project, QualityTierEnum $qualityTier): SaleTypesEnum
    {
        $maxLegs = min($qualityTier->maximumSellableLegs(), $project->max_contact_requests);

        return SaleTypesEnum::mapAllocationsToVerifiedType($maxLegs);
    }

    /**
     * @param Collection $campaigns
     * @param array $availabilities
     * @return Collection
     */
    private function removeCampaignsByAvailability(Collection $campaigns, array $availabilities): Collection
    {
        $campaignIds = [];
        foreach ($availabilities as $consumerProductId => $campaignSchedules) {
            $ids = array_keys($campaignSchedules);
            foreach ($ids as $id) {
                if (!in_array($id, $campaignIds))
                    $campaignIds[] = $id;
            }
        }

        return $campaigns->filter(fn(CompanyCampaign $campaign) => in_array($campaign->id, $campaignIds));
    }

    /**
     * @param Collection $campaignBudgets
     * @param array $scheduleData
     * @return ?array
     */
    private function calculateAppointmentBestRevenueScenario(Collection $campaignBudgets, array $scheduleData): ?array
    {
        $budgetCombinations = collect();
        uasort($scheduleData, fn($a) => !count($a) ? 1 : -1);
        $consumerProductIds    = array_keys($scheduleData);
        $firstConsumerProduct  = $scheduleData[$consumerProductIds[0]];
        $secondConsumerProduct = $scheduleData[$consumerProductIds[1] ?? ''] ?? [];
        $thirdConsumerProduct  = $scheduleData[$consumerProductIds[2] ?? ''] ?? [];

        foreach ($firstConsumerProduct as $firstCampaignId => $schedules) {
            $availableScheduleForFirst = array_keys($schedules)[0] ?? null;
            if (!$availableScheduleForFirst)
                continue;

            $combination = [
                self::ASSIGNMENT_GROUP_CAMPAIGNS_IDS => [$firstCampaignId],
                self::ASSIGNMENT_GROUP_ASSIGNMENTS   => [$this->buildAppointmentAssignment($firstCampaignId, $availableScheduleForFirst, $consumerProductIds[0])],
                self::ASSIGNMENT_GROUP_PRICES        => [$this->getCampaignBidPrice($campaignBudgets, $firstCampaignId)],
            ];

            foreach ($secondConsumerProduct as $secondCampaignId => $secondSchedules) {
                $availableScheduleForSecond = array_keys($secondSchedules)[0] ?? null;
                if (in_array($secondCampaignId, $combination[self::ASSIGNMENT_GROUP_CAMPAIGNS_IDS]) || !$availableScheduleForSecond)
                    continue;

                $withSecondCampaign                                         = [...$combination];
                $withSecondCampaign[self::ASSIGNMENT_GROUP_CAMPAIGNS_IDS][] = $secondCampaignId;
                $withSecondCampaign[self::ASSIGNMENT_GROUP_ASSIGNMENTS][]   = $this->buildAppointmentAssignment($secondCampaignId, $availableScheduleForSecond, $consumerProductIds[1]);
                $withSecondCampaign[self::ASSIGNMENT_GROUP_PRICES][]        = $this->getCampaignBidPrice($campaignBudgets, $secondCampaignId);

                foreach ($thirdConsumerProduct as $thirdCampaignId => $thirdSchedules) {
                    $availableScheduleForThird = array_keys($thirdSchedules)[0] ?? null;
                    if (in_array($thirdCampaignId, $withSecondCampaign[self::ASSIGNMENT_GROUP_CAMPAIGNS_IDS]) || !$availableScheduleForThird)
                        continue;

                    $withThirdCampaign                                         = [...$withSecondCampaign];
                    $withThirdCampaign[self::ASSIGNMENT_GROUP_CAMPAIGNS_IDS][] = $thirdCampaignId;
                    $withThirdCampaign[self::ASSIGNMENT_GROUP_ASSIGNMENTS][]   = $this->buildAppointmentAssignment($thirdCampaignId, $availableScheduleForThird, $consumerProductIds[2]);
                    $withThirdCampaign[self::ASSIGNMENT_GROUP_PRICES][]        = $this->getCampaignBidPrice($campaignBudgets, $thirdCampaignId);

                    $budgetCombinations->push($withThirdCampaign);
                }

                $budgetCombinations->push($withSecondCampaign);
            }

            $budgetCombinations->push($combination);
        }

        $budgetCombinations->each(fn(array &$combination) => $combination[self::ASSIGNMENT_KEY_TOTAL] = array_reduce($combination['prices'], fn($total, $v) => $total + $v, 0));
        $budgetCombinations->sort(fn($a, $b) => $a[self::ASSIGNMENT_KEY_TOTAL] < $b[self::ASSIGNMENT_KEY_TOTAL] ? 1 : -1);
        $topCombination = $budgetCombinations->values()[0] ?? null;

        if (!$topCombination)
            return null;

        $topAssignmentSummary = array_reduce($topCombination[self::ASSIGNMENT_GROUP_ASSIGNMENTS], function (array $output, array $assignment) {
            $output[$assignment[self::ASSIGNMENT_KEY_CAMPAIGN_ID]] = [
                ...$assignment
            ];
            return $output;
        }, []);

        return $topAssignmentSummary;
    }

    /**
     * @param Collection $campaignBudgets
     * @param int $campaignId
     * @return float
     */
    private function getCampaignBidPrice(Collection &$campaignBudgets, int $campaignId): float
    {
        $target = $campaignBudgets->where(fn(Budget $budget) => $budget->budgetContainer->company_campaign_id === $campaignId)
            ->first();

        return $target
            ? $target->{self::BUDGET_MODIFIED_BID}
            : 0;
    }

    /**
     * @param int $campaignId
     * @param int $scheduleId
     * @param int $consumerProductId
     * @return array
     */
    private function buildAppointmentAssignment(int $campaignId, int $scheduleId, int $consumerProductId): array
    {
        return [
            self::ASSIGNMENT_KEY_CAMPAIGN_ID         => $campaignId,
            self::ASSIGNMENT_KEY_SCHEDULE_ID         => $scheduleId,
            self::ASSIGNMENT_KEY_CONSUMER_PRODUCT_ID => $consumerProductId,
        ];
    }
}
