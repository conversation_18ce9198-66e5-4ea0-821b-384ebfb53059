<?php

namespace App\Strategies\ProductAssignment\ProductAssignmentStrategies;

use App\Contracts\Campaigns\BidModificationStrategyContract;
use App\Contracts\Campaigns\ViableBudgetStrategyContract;
use App\Contracts\ProductAssignment\ProductAssignmentStrategyContract;
use App\DataModels\Campaigns\ConsumerProject;
use App\DataModels\ProductAssignment\ProposedProductAssignment;
use App\Enums\Odin\Product as ProductEnum;
use App\Enums\Odin\SaleTypes;
use App\Jobs\OpportunityNotifications\CreateCampaignOutbidEventsJob;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\Odin\CompanyConfiguration;
use App\Models\Odin\ProductAssignment;
use App\Repositories\CompanyConfigurationRepository;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\ProductAssignmentRepository;
use App\Repositories\Odin\ProductRepository;
use App\Repositories\Odin\QualityTierRepository;
use App\Repositories\Odin\SaleTypeRepository;
use App\Services\Campaigns\Modules\Budget\BudgetUsageService;
use App\Services\ConsumerProductLifecycleTrackingService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;
use App\Services\Campaigns\ProductBiddingService;
use Throwable;

abstract class BaseAssignmentStrategy implements ProductAssignmentStrategyContract
{

    const string BUDGET_MODIFIED_BID = 'modified_bid';
    const string BUDGETS             = 'budgets';
    const string MODIFIED_REVENUE    = 'modified_revenue';
    const string BUDGET_BID          = 'bid';
    const string SALES_TYPE          = 'sales_type';
    const string PRODUCT             = 'product';
    const string CONSUMER_PRODUCT_ID = 'consumer_product_id';
    const string QUALITY_TIER        = 'quality_tier';
    const string SCHEDULE_ID         = 'schedule_id';

    /**
     * @param ProductEnum $expectedProduct
     * @param ConsumerProject $project
     * @param Collection<int, CompanyCampaign> $campaigns
     * @param Collection<int, ProposedProductAssignment> $assertedProductAssignments
     * @param ConsumerProductLifecycleTrackingService|null $tracker
     * @return Collection<int, ProposedProductAssignment>
     * @throws BindingResolutionException
     */
    public function calculateForProduct(
        ProductEnum $expectedProduct,
        ConsumerProject $project,
        Collection $campaigns,
        Collection $assertedProductAssignments,
        ?ConsumerProductLifecycleTrackingService $tracker = null,
    ): Collection
    {
        /** @var QualityTierRepository $qualityTierRepository */
        $qualityTierRepository = app()->make(QualityTierRepository::class);

        /** @var SaleTypeRepository $saleTypeRepository */
        $saleTypeRepository = app()->make(SaleTypeRepository::class);

        $qualityTiers  = $qualityTierRepository->all();
        $saleTypes     = $saleTypeRepository->all();

        $campaigns = $this->removeCampaignsForAssertedAssignments($campaigns, $assertedProductAssignments);
        $campaigns = $this->removeCampaignsWhereCompanyHasUndeliveredAssignment($campaigns, $project);

        if ($campaigns->count() < 1)
            return collect();

        $totalLegs     = $this->getTotalAssignableLegs($project, $assertedProductAssignments);
        $assignedLegs  = $this->getAssignedLegs($assertedProductAssignments);
        $availableLegs = $totalLegs - $assignedLegs;
        if ($availableLegs < 1)
            return collect();

        $potentialProducts = $this->getPotentialProductsConsideringPreviousAssignments($project, $assertedProductAssignments);

        $budgetCombinations = collect();

        $propertyTypeId = $project->propertyTypeId();

        $maxBudgetsAvailable = 0;
        $maxLegs = 0;
        $consideredCampaignIds = [];

        foreach ($potentialProducts as $product => $potentialQualityTiers) {

            if ($product !== $expectedProduct->value)
                continue;

            foreach ($potentialQualityTiers as $qualityTier => $potentialSalesTypes) {
                foreach ($potentialSalesTypes as $salesType) {

                    // determine how many available legs for this specific sales type
                    $scopedAvailableLegs = $this->getMaxLegsForSalesType($salesType) - $assignedLegs;
                    if ($scopedAvailableLegs < 1)
                        continue;

                    // get all budgets that can accept this product variant
                    $viableBudgets = $this->getViableBudgetsForProductVariant($campaigns, $product, $qualityTier, $salesType);

                    // make new instance of the budget objects so that the bid price does not get updated in the loop
                    $viableBudgets = $this->cloneBudgetCollection($viableBudgets);

                    // get campaign bids for each available budget
                    $viableBudgets = $this->addBidToBudgets($viableBudgets, $project->zipCode(), $propertyTypeId, $qualityTiers[$qualityTier], $saleTypes[$salesType]);

                    // filter out budgets that would exceed threshold
                    $budgetsWithinTolerance = $this->filterOutBudgetsThatWouldBeExceeded($viableBudgets);

                    // calculate 'modified' bid - this is where we will take things like rejection into account to nerf bid influence
                    $budgetsWithinTolerance = $this->addModifiedBidToBudgets($budgetsWithinTolerance);

                    // get one budget per company with highest modified bid
                    $highestModifiedBidBudgetPerCompany = $this->getHighestModifiedBidBudgetPerCompany($budgetsWithinTolerance);
                    if ($highestModifiedBidBudgetPerCompany->count() >= $totalLegs)
                        $this->collectCampaignIdsFromBudgets($highestModifiedBidBudgetPerCompany, $consideredCampaignIds);

                    // choose top X budgets by bid
                    $topBudgets = $highestModifiedBidBudgetPerCompany->sortByDesc(self::BUDGET_MODIFIED_BID)->take($scopedAvailableLegs);

                    $maxBudgetsAvailable = max($topBudgets->count(), $maxBudgetsAvailable);
                    $maxLegs = max($maxLegs, $scopedAvailableLegs);

                    $budgetCombinations->push(collect([
                        self::MODIFIED_REVENUE    => $topBudgets->sum(self::BUDGET_MODIFIED_BID),
                        self::SALES_TYPE          => $salesType,
                        self::PRODUCT             => $product,
                        self::BUDGETS             => $topBudgets,
                        self::CONSUMER_PRODUCT_ID => $this->getConsumerProductIdForProduct($product, $project),
                        self::QUALITY_TIER        => $qualityTier
                    ]));
                }
            }
        }

        if ($budgetCombinations->count() < 1)
            return collect();

        if ($maxBudgetsAvailable < $maxLegs)
            $project->had_budget_coverage = false;

        if ($tracker)
            $this->logBRSDataToLifecycleTracker($budgetCombinations->sortByDesc(self::MODIFIED_REVENUE), $tracker);

        // todo: add tie breaker logic?
        /** @var Collection $budgetSet */
        $budgetSet = $budgetCombinations->sortByDesc(self::MODIFIED_REVENUE)->first();

        if ($consideredCampaignIds) {
            $finalCampaigns = [];
            $this->collectCampaignIdsFromBudgets($budgetSet->get(self::BUDGETS), $finalCampaigns);
            $outbidCampaigns = $finalCampaigns
                ? array_diff($consideredCampaignIds, $finalCampaigns)
                : [];
            if ($outbidCampaigns)
                CreateCampaignOutbidEventsJob::dispatch($outbidCampaigns);
        }

        return $this->convertToProposedAssignments($budgetSet, $project);
    }

    /**
     * @param Collection $budgets
     * @param int[] $ids
     * @return void
     */
    protected function collectCampaignIdsFromBudgets(Collection $budgets, array &$ids): void
    {
        $budgets->each(function($budget) use (&$ids) {
            $id = $budget?->budgetContainer?->company_campaign_id ?? null;
            if ($id && !in_array($id, $ids))
                $ids[] = $id;
        });
    }

    /**
     * if any assertions have already been invoiced, then the remaining legs must be sold as like products
     * e.g. if lead sold and invoiced as a duo, we cannot sell the remaining 2 legs as trio's because the invoiced
     * assignment can't be updated
     *
     * @param ConsumerProject $project
     * @param Collection<int, ProposedProductAssignment> $assertions
     * @return array
     */
    public function getPotentialProductsConsideringPreviousAssignments(ConsumerProject $project, Collection $assertions): array
    {
        $assertedProduct = null;
        $assertedSalesType = null;

        $existingAssertions = $this->getAssertedAssertions($assertions);
        if($existingAssertions->count() > 0){
            $assertedProduct = $existingAssertions->first()?->consumerProduct()?->serviceProduct?->product->name;

            $invoicedAssertions = $this->getInvoicedAssertions($existingAssertions);
            if($invoicedAssertions->count() > 0){
                $invoicedProductAssignment = $invoicedAssertions->first()->existingProductAssignment();
                // TODO: This should go through all product assignments
                //          It's silly we named it `productAssignment` and it's now a one->many and name wasn't changed
                $assertedSalesType = $invoicedProductAssignment->saleType->name;
            }
            else if ($project->hasOptInCompanies()) {
                $assertedProduct = ProductEnum::LEAD->value;
            }
            else if ($project->hasAppointments()) {
                // Product & sale type determined by previous Appointment allocation
                /** @var ProposedProductAssignment $appointmentAssertion */
                $appointmentAssertion = $assertions->where('productId', ProductEnum::APPOINTMENT->model()->id)->first();
                if ($appointmentAssertion) {
                    $assertedSalesType = $appointmentAssertion
                        ->saleType()
                        ?->name ?? null;
                    $assertedProduct   = ProductEnum::LEAD->value;
                }
            }
        }

        $potentialProducts = $project->potentialProducts();
        if($assertedProduct && array_key_exists($assertedProduct, $potentialProducts))
            $potentialProducts = [$assertedProduct => $potentialProducts[$assertedProduct]];

        if($assertedSalesType){
            foreach ($potentialProducts[$assertedProduct] as $qualityTier => $salesTypes){
                $potentialProducts[$assertedProduct][$qualityTier] = [$assertedSalesType];
            }
        }

        return $potentialProducts;
    }

    /**
     * @param Collection<int, ProposedProductAssignment> $assertions
     * @return int
     */
    public function getAssignedLegs(Collection $assertions): int
    {
        return $assertions->filter(function (ProposedProductAssignment $assertion) {
            return $assertion->isAsserted;
        })->count();
    }

    /**
     * @param ConsumerProject $project
     * @param Collection<int, ProposedProductAssignment> $assertions
     * @return int
     */
    public function getTotalAssignableLegs(ConsumerProject $project, Collection $assertions): int
    {
        $invoicedAssertions = $this->getInvoicedAssertions($assertions);

        if ($invoicedAssertions->count() > 0) {
            // if any assertions are already invoiced, the sales type cannot change, therefore the sales cap that correlates
            // with the sales type must be honored
            return $invoicedAssertions->first()->existingProductAssignment()->saleType->sale_limit;
        }
        else if ($project->hasAppointments()) {
            /** @var ProposedProductAssignment $assertedAppointment */
            $assertedAppointment = $assertions->where('productId', ProductEnum::APPOINTMENT->model()->id)
                ->first();
            if ($assertedAppointment) {
                return $assertedAppointment->saleType()?->sale_limit ?? $project->max_contact_requests;
            }
        }

        return $project->max_contact_requests;

    }

    /**
     * @param Collection<int, CompanyCampaign> $campaigns
     * @param Collection<int, ProposedProductAssignment> $assertions
     * @return Collection<int, CompanyCampaign>
     */
    public function removeCampaignsForAssertedAssignments(Collection $campaigns, Collection $assertions): Collection
    {
        $assertedCompanyIds = $assertions->map(function ($assertion) {
            return $assertion->companyId;
        })->toArray();

        return $campaigns->whereNotIn(CompanyCampaign::FIELD_COMPANY_ID, $assertedCompanyIds);
    }

    /**
     * @param Collection<int, ProposedProductAssignment> $assertions
     * @return Collection<int, ProposedProductAssignment>
     */
    public function getInvoicedAssertions(Collection $assertions): Collection
    {
        // TODO: This should go through all product assignments
        //          It's silly we named it `productAssignment` and it's now a one->many and name wasn't changed
        return $assertions->filter(function(ProposedProductAssignment $assignment){
            return $assignment->existingProductAssignment()?->invoiceItem()->exists();
        });
    }

    /**
     * @param Collection<int, ProposedProductAssignment> $assertions
     * @return Collection<int, ProposedProductAssignment>
     */
    private function getExistingAssertions(Collection $assertions): Collection
    {
        return $assertions->filter(function(ProposedProductAssignment $assignment){
            return $assignment->isExistingAssignment;
        });
    }

    /**
     * @param Collection $assertions
     * @return Collection
     */
    private function getAssertedAssertions(Collection $assertions): Collection
    {
        return $assertions->filter(function(ProposedProductAssignment $assignment) {
            return $assignment->isAsserted;
        });
    }

    /**
     * @param string $salesType
     * @return int
     */
    protected function getMaxLegsForSalesType(string $salesType): int
    {
        return match ($salesType) {
            SaleTypes::EXCLUSIVE->value => 1,
            SaleTypes::DUO->value => 2,
            SaleTypes::TRIO->value => 3,
            SaleTypes::QUAD->value, SaleTypes::EMAIL_ONLY->value, SaleTypes::UNVERIFIED->value => 4,
        };
    }

    /**
     * Determines only if the budget is able to accept such a product type, does not take usage or status into account
     *
     * @param Collection<int, CompanyCampaign> $campaigns
     * @param string $product
     * @param string $qualityTier
     * @param string $salesType
     * @return Collection
     */
    protected function getViableBudgetsForProductVariant(Collection $campaigns, string $product, string $qualityTier, string $salesType): Collection
    {
        /** @var ViableBudgetStrategyContract $viableBudgetStrategy */
        $viableBudgetStrategy = app(ViableBudgetStrategyContract::class);
        return $viableBudgetStrategy->getViableCampaignBudgetsForProductVariant($campaigns, $product, $qualityTier, $salesType);
    }

    /**
     * Appends current bid price to budgets
     *
     * @param Collection<int, Budget> $budgets
     * @param string $zipCode
     * @param int $propertyTypeId
     * @param int $qualityTierId
     * @param int $salesTypeId
     * @return Collection
     */
    protected function addBidToBudgets(Collection $budgets, string $zipCode, int $propertyTypeId, int $qualityTierId, int $salesTypeId): Collection
    {
        /** @var ProductBiddingService $bidService */
        $bidService = app(ProductBiddingService::class);
        /** @var LocationRepository $locationRepository */
        $locationRepository = app(LocationRepository::class);

        $countyLocation = $locationRepository->getCountyFromZipcode($zipCode);
        $stateLocationId = $locationRepository->getStateByStateAbbr($countyLocation->state_abbr)->id;
        $countyLocationId = $countyLocation->id;

        return $budgets->map(function(Budget $budget) use ($bidService, $countyLocationId, $stateLocationId, $propertyTypeId, $qualityTierId, $salesTypeId){
            $budget->{self::BUDGET_BID} = $bidService->getProductBid(
                companyCampaign: $budget->budgetContainer->campaign,
                countyLocationId: $countyLocationId,
                stateLocationId: $stateLocationId,
                propertyTypeId: $propertyTypeId,
                qualityTierId: $qualityTierId,
                salesTypeId: $salesTypeId
            );

            return $budget;
        });
    }

    /**
     * Remove budgets that would exceed usage threshold
     *
     * @param Collection<int, Budget> $budgets
     * @return Collection
     */
    protected function filterOutBudgetsThatWouldBeExceeded(Collection $budgets): Collection
    {
        $companyIds = $budgets->pluck(Budget::RELATION_BUDGET_CONTAINER.'.'.BudgetContainer::RELATION_CAMPAIGN.'.'.CompanyCampaign::FIELD_COMPANY_ID)->unique()->toArray();
        $companyIdToNeverExceedBudget = $this->getNeverExceedBudgetByCompany($companyIds);

        /** @var BudgetUsageService $budgetUsageService */
        $budgetUsageService = app(BudgetUsageService::class);
        return $budgets->filter(function(Budget $budget)use($budgetUsageService, $companyIdToNeverExceedBudget){
            return !$budgetUsageService->willBudgetBeExceeded(
                $budget,
                $budget->{self::BUDGET_BID},
                $companyIdToNeverExceedBudget[$budget->budgetContainer->campaign->company_id] ?? false
            );
        });
    }

    /**
     * Calculate modified bid and append to budgets
     *
     * @param Collection<int, Budget> $budgets
     * @return Collection
     */
    protected function addModifiedBidToBudgets(Collection $budgets): Collection
    {
        /** @var BidModificationStrategyContract $bidModificationStrategy */
        $bidModificationStrategy = app(BidModificationStrategyContract::class);
        return $bidModificationStrategy->appendModifiedBidToBudgets($budgets);
    }

    /**
     * Return only the highest bid budget for each company, based on modified bid
     *
     * @param Collection<int, Budget> $budgets
     * @return Collection
     */
    protected function getHighestModifiedBidBudgetPerCompany(Collection $budgets): Collection
    {
        $highestBidBudgets = collect();
        $groupedByCompany = $budgets->groupBy(Budget::RELATION_BUDGET_CONTAINER.'.'.BudgetContainer::RELATION_CAMPAIGN.'.'.CompanyCampaign::FIELD_COMPANY_ID);
        foreach ($groupedByCompany as  $companyId => $budgets){
            $highestBidBudgets->push($budgets->sortByDesc(self::BUDGET_MODIFIED_BID)->first());
        }
        return $highestBidBudgets;
    }

    /**
     * @param Collection $budgetSet
     * @return Collection<int, ProposedProductAssignment>
     */
    protected function convertToProposedAssignments(Collection $budgetSet, ConsumerProject $project, ?bool $hasAppointmentProducts = false): Collection
    {
        /** @var QualityTierRepository $qualityTierRepository */
        $qualityTierRepository = app()->make(QualityTierRepository::class);

        $productId = $this->getProductIdFromString($budgetSet->offsetGet(self::PRODUCT));
        $salesTypeId = $this->getSalesTypeIdFromString($budgetSet->offsetGet(self::SALES_TYPE));
        $qualityTier = $qualityTierRepository->findByName($budgetSet->offsetGet(self::QUALITY_TIER));
        $isMultiService = $project->isMultiService();

        $proposedAssignments = collect();
        /** @var Budget $budget */
        foreach ($budgetSet->offsetGet(self::BUDGETS) as $budget) {
            $proposedAssignments->push(new ProposedProductAssignment(
                companyId: $budget->budgetContainer->campaign->company_id,
                campaignId: $budget->budgetContainer->company_campaign_id,
                budgetId: $budget->id,
                price: $budget->{self::BUDGET_BID},
                productId: $productId,
                salesTypeId: $salesTypeId,
                consumerProductId: $isMultiService || $hasAppointmentProducts
                    ? $this->findConsumerProductId($budget, $project, (!$isMultiService && $hasAppointmentProducts))
                    : $budgetSet->offsetGet(self::CONSUMER_PRODUCT_ID),
                qualityTierId: $qualityTier?->id,
                scheduleId: $budget->{self::SCHEDULE_ID} ?? null,
            ));
        }

        return $proposedAssignments;
    }

    /**
     * @param Budget $budget
     * @param ConsumerProject $project
     * @param bool $hasAppointmentProducts
     * @return int
     */
    protected function findConsumerProductId(Budget $budget, ConsumerProject $project, bool $hasAppointmentProducts): int
    {
        if ($hasAppointmentProducts)
            return $budget->{self::CONSUMER_PRODUCT_ID};
        else {
            $serviceProductId = $budget->budgetContainer->campaign->serviceProduct()?->id ?? 0;

            return $project->tryConsumerProductIdFromServiceProductId($serviceProductId)
                ?? $project->getFirstConsumerProductByProduct(ProductEnum::tryFrom($budget->budgetContainer->campaign->product->name))?->id
                ?? 0;
        }
    }

    /**
     * @param string $product
     * @return string
     */
    private function getProductIdFromString(string $product): string
    {
        /** @var ProductRepository $productRepository */
        $productRepository = app(ProductRepository::class);
        return $productRepository->getProductByName(ucfirst($product))->id;
    }

    /**
     * @param string $salesType
     * @return string
     */
    private function getSalesTypeIdFromString(string $salesType): string
    {
        /** @var SaleTypeRepository $salesTypeRepository */
        $salesTypeRepository = app(SaleTypeRepository::class);
        return $salesTypeRepository->getSalesTypeByName(ucfirst($salesType))->id;
    }

    /**
     * Primarily intended to prevent companies who've bounced from retrying allocation
     *
     * @param Collection<int, CompanyCampaign> $campaigns
     * @param ConsumerProject $project
     * @return Collection<int, CompanyCampaign>
     */
    protected function removeCampaignsWhereCompanyHasUndeliveredAssignment(Collection $campaigns, ConsumerProject $project): Collection
    {
        /** @var ProductAssignmentRepository $assignmentRepository */
        $assignmentRepository                = app(ProductAssignmentRepository::class);
        $undeliveredAssignments              = $assignmentRepository->getUndeliveredAssignmentsByConsumerProject($project);
        $companiesWithUndeliveredAssignments = $undeliveredAssignments->pluck(ProductAssignment::FIELD_COMPANY_ID)->unique()->toArray();
        return $campaigns->filter(fn(CompanyCampaign $campaign) => !in_array($campaign->company_id, $companiesWithUndeliveredAssignments));
    }

    /**
     * @param Collection $budgets
     * @return Collection
     */
    protected function cloneBudgetCollection(Collection $budgets): Collection
    {
        return $budgets->map(function (Budget $budget) {
            $clone = $budget->replicate();
            $clone->id = $budget->id;
            $clone->updated_at = $budget->updated_at;
            $clone->created_at = $budget->created_at;

            return $clone;
        });
    }

    /**
     * @param string $product
     * @param ConsumerProject $project
     *
     * @return int
     */
    protected function getConsumerProductIdForProduct(string $product, ConsumerProject $project): int
    {
        return match ($product) {
            ProductEnum::DIRECT_LEADS->value => $project->getDirectLeadConsumerProductOrFail()->id,
            default => $project->leadConsumerProduct()->id
        };
    }

    private function getNeverExceedBudgetByCompany(array $companyIds): array
    {
        /** @var CompanyConfigurationRepository $configurationRepository */
        $configurationRepository = app(CompanyConfigurationRepository::class);
        $companyConfigurations = $configurationRepository->getConfigurationsByCompanyIds($companyIds, [CompanyConfiguration::FIELD_COMPANY_ID, CompanyConfiguration::FIELD_NEVER_EXCEED_BUDGET]);
        return $companyConfigurations->pluck(CompanyConfiguration::FIELD_NEVER_EXCEED_BUDGET, CompanyConfiguration::FIELD_COMPANY_ID)->toArray();
    }

    /**
     * @param Collection $budgetCombinations
     * @param ConsumerProductLifecycleTrackingService $tracker
     * @return void
     */
    protected function logBRSDataToLifecycleTracker(Collection $budgetCombinations, ConsumerProductLifecycleTrackingService $tracker): void
    {
        try {
            $brsSummary = [];
            foreach ($budgetCombinations as $budgetCombination) {
                $budgetSummaries = [];
                foreach ($budgetCombination->get(self::BUDGETS) as $budget) {
                    if (!$budget) continue;

                    $budgetSummaries[] = [
                        Budget::FIELD_ID                           => $budget->id,
                        BudgetContainer::FIELD_COMPANY_CAMPAIGN_ID => $budget->budgetContainer?->company_campaign_id,
                        self::BUDGET_BID                           => $budget->{self::BUDGET_BID},
                        self::BUDGET_MODIFIED_BID                  => $budget->{self::BUDGET_MODIFIED_BID},
                    ];
                }
                $brsSummary[] = [
                    self::MODIFIED_REVENUE => $budgetCombination->get(self::MODIFIED_REVENUE),
                    self::SALES_TYPE       => $budgetCombination->get(self::SALES_TYPE),
                    self::PRODUCT          => $budgetCombination->get(self::PRODUCT),
                    self::BUDGETS          => $budgetSummaries,
                ];
            }

            $tracker->addBudgetSummaryToAllocationData($brsSummary);
        }
        catch (Throwable $e) {
            logger()->error($e->getMessage());
        }
    }
}
