<?php

namespace App\Strategies\ProductAssignment\ProductAssignmentStrategies;

use App\DataModels\Campaigns\ConsumerProject;
use App\Enums\Odin\Product;
use App\Services\ConsumerProductLifecycleTrackingService;
use Illuminate\Support\Collection;

class DirectLeadsAssignmentStrategy extends BaseAssignmentStrategy
{
    #[\Override] public function calculate(
        ConsumerProject $project,
        Collection $campaigns,
        Collection $assertedProductAssignments,
        ?ConsumerProductLifecycleTrackingService $tracker = null,
    ): Collection
    {
        if (!$project->getDirectLeadConsumerProduct()) {
            if (!$project->createDirectLeadsConsumerProduct()) {
                return collect();
            }
        }

        return $this->calculateForProduct(
            expectedProduct: Product::DIRECT_LEADS,
            project: $project,
            campaigns: $campaigns,
            assertedProductAssignments: $assertedProductAssignments,
            tracker: $tracker,
        );
    }
}
