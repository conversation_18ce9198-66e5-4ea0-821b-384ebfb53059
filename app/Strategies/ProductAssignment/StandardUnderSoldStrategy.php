<?php

namespace App\Strategies\ProductAssignment;

use App\ConsumerProcessing\Jobs\AttemptConsumerProjectAllocationJob;
use App\Contracts\ProductAssignment\UnderSoldStrategyContract;
use App\DataModels\Campaigns\ConsumerProject;
use App\DataModels\ProductAssignment\ProductPayloads\AppointmentPayload;
use App\DataModels\ProductAssignment\ProposedProductAssignment;
use App\Enums\Odin\Product as ProductEnum;
use App\Models\Odin\Product;
use App\Repositories\Odin\ProductRepository;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class StandardUnderSoldStrategy implements UnderSoldStrategyContract
{

    const VALID_QUALIFICATION_HOURS               = 24;
    const DEFAULT_REATTEMPT_DELAY_HOURS           = 2;
    const AFTER_APPOINTMENT_REATTEMPT_DELAY_HOURS = 2;

    public function __construct(protected ProductRepository $productRepository) {}

    /** @inheritDoc */
    public function shouldContinueAssigningNow(ConsumerProject $project, Collection $proposedAssignments, ?Product $product = null): bool
    {
        //Only appointments amd Direct Leads currently attempt to sell remaining legs
        if (!in_array($product?->name, [ProductEnum::APPOINTMENT->value, ProductEnum::DIRECT_LEADS->value]))
            return false;

        if (!$this->hasViableAndSellableProductLegs($project, $proposedAssignments))
            return false;

        return true;
    }

    /** @inheritDoc */
    public function shouldScheduleAssignmentReattempt(ConsumerProject $project, Collection $proposedAssignments): bool
    {
        return $this->hasViableAndSellableProductLegs($project, $proposedAssignments);
    }

    /** @inheritDoc */
    public function getReattemptDelay(ConsumerProject $project, Collection $proposedAssignments): ?int
    {
        if (!$this->shouldScheduleAssignmentReattempt($project, $proposedAssignments))
            return null;

        $delay = self::DEFAULT_REATTEMPT_DELAY_HOURS;

        if ($this->hasNewAppointments($proposedAssignments)) {
            $lastAppointmentTime = $this->getLastAppointmentTime($proposedAssignments);
            if (!$lastAppointmentTime)
                // The last appointment time being null is indicative of an issue upstream, as a fail-safe, we'll
                // schedule the next attempt three days after the creation of the consumer project (appointments are
                // currently only booked up to three days out)
                $lastAppointmentTime = $project->created_at->addDays(3);

            $delay = (int) $lastAppointmentTime->diffInHours(Carbon::now(), true) + self::AFTER_APPOINTMENT_REATTEMPT_DELAY_HOURS;
        }

        return $delay * 60;
    }

    /**
     * @param Collection<int, ProposedProductAssignment> $proposedAssignments
     * @return bool
     */
    private function hasNewAppointments(Collection $proposedAssignments): bool
    {
        $appointments    = $this->getProposedAppointments($proposedAssignments);
        $newAppointments = $appointments->filter(function (ProposedProductAssignment $proposedAssignment) {
            return !$proposedAssignment->isExistingAssignment;
        });

        return $newAppointments->count() > 0;
    }

    /**
     * @param ConsumerProject $project
     * @param Collection<int, ProposedProductAssignment> $proposedAssignments
     * @return bool
     */
    private function hasUnassignedLegs(ConsumerProject $project, Collection $proposedAssignments): bool
    {
        return $project->max_contact_requests > $proposedAssignments->count();
    }

    /**
     * This will return true if there are unsold legs that haven't been disqualified as 'sellable' for any reason e.g. leads being too old etc...
     *
     * @param ConsumerProject $project
     * @param Collection $proposedAssignments
     * @return bool
     */
    public function hasViableAndSellableProductLegs(ConsumerProject $project, Collection $proposedAssignments): bool
    {
        if (!$this->hasUnassignedLegs($project, $proposedAssignments))
            return false; // no remaining legs

        if (!$project->last_qualified_at || $project->last_qualified_at->diffInHours(Carbon::now(), true) > self::VALID_QUALIFICATION_HOURS)
            return false; // not yet qualified or age since qualification too old

        return true;
    }

    /**
     * @param Collection<int, ProposedProductAssignment> $proposedAssignments
     * @return Carbon|null
     */
    private function getLastAppointmentTime(Collection $proposedAssignments): ?Carbon
    {
        $appointments    = $this->getProposedAppointments($proposedAssignments);
        $appointmentTime = null;
        /** @var ProposedProductAssignment $proposedAppointment */
        foreach ($appointments as $proposedAppointment) {
            /** @var AppointmentPayload $payload */
            $payload = $proposedAppointment->payload;
            if ($payload->appointmentTime) {
                if (!$appointmentTime || $payload->appointmentTime->gt($appointmentTime))
                    $appointmentTime = $payload->appointmentTime;
            }
        }
        return $appointmentTime;
    }

    /**
     * @param Collection<int, ProposedProductAssignment> $proposedAssignments
     * @return Collection
     */
    private function getProposedAppointments(Collection $proposedAssignments): Collection
    {
        $appointmentProductId = $this->productRepository->getAppointmentProductId();
        return $proposedAssignments->filter(function (ProposedProductAssignment $proposedAssignment) use ($appointmentProductId) {
            return $proposedAssignment->productId === $appointmentProductId;
        });
    }
}
