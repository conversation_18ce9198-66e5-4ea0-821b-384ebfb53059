<?php

namespace App\Strategies\CampaignBudget;

use App\Contracts\Strategies\CampaignBudget\CalculateBudgetStrategy;
use App\Models\AvailableBudget;
use Illuminate\Contracts\Container\BindingResolutionException;

class CalculateBudgetFactory
{
    const int BUDGET_TYPE_FUTURE_CAMPAIGN_VERIFIED   = 3;
    const int BUDGET_TYPE_FUTURE_CAMPAIGN_UNVERIFIED = 4;

    /**
     * Handles creating the correct budget strategy.
     *
     * @param int $type
     * @return CalculateBudgetStrategy
     * @throws BindingResolutionException
     */
    public static function make(int $type): CalculateBudgetStrategy
    {
        return match ($type) {
            AvailableBudget::BUDGET_TYPE_VERIFIED => app()->make(CalculateVerifiedBudgetStrategy::class),
            self::BUDGET_TYPE_FUTURE_CAMPAIGN_VERIFIED => app()->make(FutureCampaignCalculateVerifiedBudgetStrategy::class),
            self::BUDGET_TYPE_FUTURE_CAMPAIGN_UNVERIFIED => app()->make(FutureCampaignCalculateUnverifiedBudgetStrategy::class),
            default => new CalculateUnverifiedBudgetStrategy(),
        };
    }
}
