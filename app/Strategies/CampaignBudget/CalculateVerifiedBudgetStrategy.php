<?php

namespace App\Strategies\CampaignBudget;

use App\Contracts\Strategies\CampaignBudget\CalculateBudgetStrategy;
use App\DataModels\AvailableBudgetDataModel;
use App\Models\AvailableBudget;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignLocation;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Legacy\Location;
use App\Repositories\HistoricalAvailableLocationBudgetRepository;
use App\Repositories\Legacy\QuoteCompanyRepository;
use App\Services\LeadCampaignHelperService;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class CalculateVerifiedBudgetStrategy implements CalculateBudgetStrategy
{

    const BUDGET_TYPE_ID = AvailableBudget::BUDGET_TYPE_VERIFIED;
    const STANDARD       = HistoricalAvailableLocationBudgetRepository::STANDARD;
    const OMIT_REJECTION = HistoricalAvailableLocationBudgetRepository::OMIT_REJECTION;

    // This only applies when company has never_exceed_budget set to true
    const AVAILABLE_BUDGET_SPEND_MINIMUM = 100;

    const string INDUSTRY_TO_DATA                   = 'industryToData';
    const string LOCATION_ID_TO_COUNTY_LOCATION_IDS = 'locationIdToCountyLocationIds';

    private array      $availableBudgetDatum;
    private Collection $allQuoteCompanies;
    private array      $campaignIdToLocationIds;
    private array      $locationIdToCountyLocationIds;
    private bool       $historicalBudget = false;

    public function __construct(
        protected LeadCampaignHelperService $leadCampaignHelperService,
        protected QuoteCompanyRepository    $quoteCompanyRepository
    )
    {}

    /**
     * @param Collection $campaigns
     * @return array
     */
    public function calculateBudgetAvailable(Collection $campaigns): array
    {
        $this->initializePropertyVariables($campaigns);

        // Calculate available budget for each campaign, update zip code budget data accordingly
        foreach ($campaigns as $campaign) {
            if ($campaign->isUnlimitedBudget()) {
                $this->increaseUnlimitedCountForAllLocationsInCampaign($campaign);
            } elseif ($campaign->isSpendBudget() || $campaign->isVolumeBudget()) {
                $this->handleBudgetAvailableForCampaign($campaign);
            }
        }

        return [
            self::INDUSTRY_TO_DATA                   => $this->availableBudgetDatum,
            self::LOCATION_ID_TO_COUNTY_LOCATION_IDS => $this->locationIdToCountyLocationIds
        ];
    }

    /**
     * @param Collection $campaigns
     * @return array
     */
    public function calculateHistoricalBudgetAvailable(Collection $campaigns): array
    {
        $this->historicalBudget = true;

        $this->initializePropertyVariables($campaigns);

        // Calculate available budget for each campaign, update zip code budget data accordingly
        foreach ($campaigns as $campaign) {
            if ($campaign->isUnlimitedBudget()) {
                $this->increaseUnlimitedCountForAllLocationsInCampaign($campaign);
            } elseif ($campaign->isSpendBudget() || $campaign->isVolumeBudget()) {
                $this->handleBudgetAvailableForCampaign($campaign);
                $this->handleBudgetAvailableOmittingRejectionForCampaign($campaign);
            }
        }

        return $this->availableBudgetDatum;
    }

    /**
     * @return array[]
     */
    private function getAvailableBudgetDatum(): array
    {
        return [
            AvailableBudget::INDUSTRY_TYPE_SOLAR   => [],
            AvailableBudget::INDUSTRY_TYPE_ROOFING => []
        ];
    }

    /**
     * @param Collection $campaigns
     * @return Collection
     */
    private function getAllQuoteCompaniesInLast31Days(Collection $campaigns): Collection
    {
        return $this->quoteCompanyRepository->getTotaledChargeableAndDeliveredLeadsByCampaignIds(
            $campaigns->pluck(LeadCampaign::ID)->toArray(),
            Carbon::now()->subDays(31)->timestamp
        )->groupBy(EloquentQuoteCompany::RELATION_LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION . '.' . LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID);
    }

    /**
     * @param Collection $campaigns
     * @return void
     */
    private function initializePropertyVariables(Collection $campaigns): void
    {
        $this->availableBudgetDatum = $this->getAvailableBudgetDatum();

        $this->allQuoteCompanies = $this->getAllQuoteCompaniesInLast31Days($campaigns);

        // get all filtered campaign locations
        $this->campaignIdToLocationIds = $this->getCampaignIdToLocationIds($campaigns);

        $this->locationIdToCountyLocationIds = $this->getLocationIdToCountyLocationIds();
    }

    /**
     * @param LeadCampaign $campaign
     * @return void
     */
    private function handleBudgetAvailableForCampaign(LeadCampaign $campaign): void
    {
        $budgetAvailable = $this->getAvailableBudgetForCampaign($campaign);

        if ($budgetAvailable > 0 || $this->historicalBudget) {
            if ($campaign->isSpendBudget()) {
                $this->increaseAvailableDollarsForAllLocationsInCampaign($campaign, $budgetAvailable);
            } elseif ($campaign->isVolumeBudget()) {
                $this->increaseAvailableVolumeForAllLocationsInCampaign($campaign, $budgetAvailable);
            }
        }
    }

    /**
     * @param LeadCampaign $campaign
     * @return void
     */
    private function handleBudgetAvailableOmittingRejectionForCampaign(LeadCampaign $campaign): void
    {
        $budgetAvailable = $this->getAvailableBudgetForCampaign($campaign, true);

        if ($budgetAvailable > 0 || $this->historicalBudget) {
            if ($campaign->isSpendBudget()) {
                $this->increaseAvailableDollarsForAllLocationsInCampaign($campaign, $budgetAvailable, true);
            } elseif ($campaign->isVolumeBudget()) {
                $this->increaseAvailableVolumeForAllLocationsInCampaign($campaign, $budgetAvailable, true);
            }
        }
    }

    /**
     * @param LeadCampaign $campaign
     * @param bool $omitRejectionPeriod
     * @return float
     */
    private function getAvailableBudgetForCampaign(LeadCampaign $campaign, bool $omitRejectionPeriod = false): float
    {
        $timestampBudgetStart = $this->leadCampaignHelperService->getStartingTimestampForCampaign($campaign, $omitRejectionPeriod);
        $daysSinceBudgetStart = floor((time() - $timestampBudgetStart) / 86400) + 1; // this is being used in the legacy CalculateQuoteCompanyService

        $quoteCompanies = isset($this->allQuoteCompanies[$campaign->id]) ? $this->allQuoteCompanies[$campaign->id]->filter(function (
            $quoteCompany
        ) use ($campaign, $timestampBudgetStart) {
            return $quoteCompany->{EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY} >= $timestampBudgetStart;
        }) : null;

        if (!$quoteCompanies)
            $quoteCompanies = collect();

        $spend = $quoteCompanies->sum(EloquentQuoteCompany::COST);
        $count = $quoteCompanies->count();

        $usedBudget  = $campaign->isVolumeBudget() ? $count : $spend;
        $dailyBudget = $campaign->isVolumeBudget() ? $campaign->max_daily_lead : $campaign->max_daily_spend;

        $availableBudget = ($dailyBudget * $daysSinceBudgetStart * ($campaign->maximum_budget_usage / 100)) - $usedBudget;

        if (
            config('sales.available_budgets.omit_low_never_exceed_budget') &&
            $campaign->company->never_exceed_budget && 
            $campaign->isSpendBudget() &&
            $availableBudget < self::AVAILABLE_BUDGET_SPEND_MINIMUM
        ) {
            return 0;
        }

        return $availableBudget;
    }

    /**
     * @param LeadCampaign $campaign
     * @return void
     */
    private function increaseUnlimitedCountForAllLocationsInCampaign(LeadCampaign $campaign): void
    {
        $campaignLocationIds = $this->campaignIdToLocationIds[$campaign->{LeadCampaign::ID}];
        $industry            = $this->getCampaignIndustry($campaign);
        if (!$industry) {
            return;
        }
        foreach ($campaignLocationIds as $locationId) {
            $availableBudgetData = $this->getAvailableBudgetData($industry, $locationId);
            $availableBudgetData->addUnlimitedCampaign($campaign, $campaign->id);

            $this->saveData($industry, $locationId, $availableBudgetData);
        }
    }

    /**
     * @param LeadCampaign $campaign
     * @param float $dollarIncrement
     * @param bool $omitRejection
     * @return void
     */
    private function increaseAvailableDollarsForAllLocationsInCampaign(
        LeadCampaign $campaign,
        float        $dollarIncrement,
        bool         $omitRejection = false
    ): void
    {
        $campaignLocationIds = $this->campaignIdToLocationIds[$campaign->{LeadCampaign::ID}];
        $industry            = $this->getCampaignIndustry($campaign);
        if (!$industry) {
            return;
        }
        foreach ($campaignLocationIds as $locationId) {
            $availableBudgetData = $this->getAvailableBudgetData($industry, $locationId, $omitRejection);
            $availableBudgetData->addDollarCampaign($campaign, $campaign->id, $dollarIncrement, $this->historicalBudget);

            $this->saveData($industry, $locationId, $availableBudgetData, $omitRejection);
        }
    }

    /**
     * @param LeadCampaign $campaign
     * @param float $volumeIncrement
     * @param bool $omitRejection
     * @return void
     */
    private function increaseAvailableVolumeForAllLocationsInCampaign(
        LeadCampaign $campaign,
        float        $volumeIncrement,
        bool         $omitRejection = false
    ): void
    {
        $campaignLocationIds = $this->campaignIdToLocationIds[$campaign->{LeadCampaign::ID}];
        $industry            = $this->getCampaignIndustry($campaign);
        if (!$industry) {
            return;
        }
        foreach ($campaignLocationIds as $locationId) {
            $availableBudgetData = $this->getAvailableBudgetData($industry, $locationId, $omitRejection);
            $availableBudgetData->addVolumeCampaign($campaign, $campaign->id, intval($volumeIncrement), $this->historicalBudget);

            $this->saveData($industry, $locationId, $availableBudgetData, $omitRejection);
        }
    }

    /**
     * @param string $industry
     * @param int $locationId
     * @param bool $omitRejection
     * @return AvailableBudgetDataModel
     */
    private function getAvailableBudgetData(
        string $industry,
        int    $locationId,
        bool   $omitRejection = false
    ): AvailableBudgetDataModel
    {
        if ($this->historicalBudget) {
            if ($omitRejection) {
                $data = array_key_exists($industry, $this->availableBudgetDatum) &&
                        array_key_exists($locationId, $this->availableBudgetDatum[$industry]) &&
                        array_key_exists(self::OMIT_REJECTION, $this->availableBudgetDatum[$industry][$locationId]) ?
                        $this->availableBudgetDatum[$industry][$locationId][self::OMIT_REJECTION] : null;
            } else {
                $data = array_key_exists($industry, $this->availableBudgetDatum) &&
                        array_key_exists($locationId, $this->availableBudgetDatum[$industry]) &&
                        array_key_exists(self::STANDARD, $this->availableBudgetDatum[$industry][$locationId]) ?
                        $this->availableBudgetDatum[$industry][$locationId][self::STANDARD] : null;
            }

            return $data ?: new AvailableBudgetDataModel(0, 0, 0, 0, self::BUDGET_TYPE_ID);
        }

        $data = array_key_exists($industry, $this->availableBudgetDatum) && array_key_exists($locationId, $this->availableBudgetDatum[$industry]) ? $this->availableBudgetDatum[$industry][$locationId] : null;
        return $data ?: new AvailableBudgetDataModel(0, 0, 0, 0, self::BUDGET_TYPE_ID);

    }

    /**
     * @param string $industry
     * @param int $locationId
     * @param $availableBudgetData
     * @param bool $omitRejection
     * @return void
     */
    private function saveData(
        string $industry,
        int    $locationId,
               $availableBudgetData,
        bool   $omitRejection = false
    ): void
    {
        if ($this->historicalBudget) {
            if ($omitRejection) {
                $this->availableBudgetDatum[$industry][$locationId][self::OMIT_REJECTION] = $availableBudgetData;
            } else {
                $this->availableBudgetDatum[$industry][$locationId][self::STANDARD] = $availableBudgetData;
            }
        } else {
            $this->availableBudgetDatum[$industry][$locationId] = $availableBudgetData;
        }
    }

    /**
     * @param LeadCampaign $campaign
     * @return string|null
     */
    private function getCampaignIndustry(LeadCampaign $campaign): ?string
    {
        return $campaign->company->getIndustry();
    }

    /**
     * @param Collection $campaigns
     * @return array
     */
    private function getCampaignIdToLocationIds(Collection $campaigns): array
    {
        $campaignToLocationIds = [];

        /** @var LeadCampaign $campaign */
        foreach($campaigns as $campaign) {
            $campaignToLocationIds[$campaign->{LeadCampaign::ID}] = $campaign->{LeadCampaign::RELATION_LEAD_CAMPAIGN_LOCATIONS}->pluck(LeadCampaignLocation::LOCATION_ID)->toArray();
        }

        return $campaignToLocationIds;
    }

    /**
     * @return array
     */
    private function getLocationIdToCountyLocationIds(): array
    {
        return Location::query()
            ->select([
                 Location::TABLE.'.'.Location::ID .' as original_location_id',
                 'l2.'.Location::ID .' as county_location_id',
             ])
            ->whereIntegerInRaw(Location::TABLE.'.'.Location::ID, array_unique(Arr::flatten($this->campaignIdToLocationIds)))
            ->join(Location::TABLE." AS l2", function($join) {
                $join
                    ->on(
                        'l2.'.Location::STATE_ABBREVIATION,
                        '=',
                        Location::TABLE.'.'.Location::STATE_ABBREVIATION
                    )
                    ->on(
                        'l2.'.Location::COUNTY_KEY,
                        '=',
                        Location::TABLE.'.'.Location::COUNTY_KEY
                    )
                    ->where('l2.'.Location::TYPE, Location::TYPE_COUNTY);
            })
            ->pluck('county_location_id', 'original_location_id')
            ->toArray();
    }
}
