<?php

namespace App\Strategies\CampaignBudget;

use App\Contracts\Strategies\CampaignBudget\CalculateBudgetStrategy;
use App\DataModels\Campaigns\AvailableBudgetDataModel;
use App\Enums\Campaigns\Modules\Budget\BudgetType;
use App\Enums\ProductAssignment\BudgetProductConfigurationEnum;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModule;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyConfiguration;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductRejection;
use App\Services\AverageProductRevenueByLocationService;
use App\Services\DatabaseHelperService;
use App\Services\Odin\ConsumerProductService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class FutureCampaignCalculateVerifiedBudgetStrategy implements CalculateBudgetStrategy
{
    protected Collection $availableBudgetData;

    const int AVAILABLE_BUDGET_SPEND_MINIMUM  = 100;
    const int AVAILABLE_BUDGET_VOLUME_MINIMUM = 1;

    public function __construct()
    {
        $this->availableBudgetData = collect();
    }

    /**
     * @inheritDoc
     */
    public function calculateBudgetAvailable(Collection $campaigns): Collection
    {
        if ($campaigns->isEmpty()) {
            return collect();
        }

        /** @var CompanyCampaign $campaign */
        $campaign = $campaigns->first();
        $campaignIds = $campaigns->pluck(CompanyCampaign::FIELD_ID)->unique()->toArray();
        $industry = $campaign->service->industry;
        $product = $campaign->product;

        $this->calculateAvailableCampaignCount($campaignIds, $industry);
        $this->calculateUnlimitedBudgetCount($campaignIds);
        $this->calculateAvailableVolume($campaignIds);
        $this->calculateAvailableDollars($campaignIds);
        $this->populateCountyLocations();
        $this->estimatePotentialQueuedRevenue($product->id, $industry->id);

        return $this->availableBudgetData;
    }

    /**
     * @inheritDoc
     */
    public function calculateHistoricalBudgetAvailable(Collection $campaigns): array
    {
        return [];
    }

    /**
     * @param array $campaignIds
     * @param Industry $industry
     *
     * @return void
     */
    protected function calculateAvailableCampaignCount(array $campaignIds, Industry $industry): void
    {
        $this->getBaseQuery($campaignIds)
            ->join(
                Company::TABLE,
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_COMPANY_ID,
                '=',
                Company::TABLE . '.' . Company::FIELD_ID
            )
            ->selectRaw(CompanyCampaignLocationModuleLocation::TABLE . '.' . CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID .
                ', GROUP_CONCAT( '. CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID .') AS campaign_id' .
                ', GROUP_CONCAT( CONCAT('. CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID . ', ":", ' . CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_COMPANY_ID .')) AS campaign_company_id' .
                ', GROUP_CONCAT( CONCAT('. CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_COMPANY_ID . ', ":", '. Company::TABLE .'.' . Company::FIELD_LEGACY_ID . ')) AS company_id' .
                ', GROUP_CONCAT( '. CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_COMPANY_ID .') AS odin_company_id'
            )
            ->groupBy(CompanyCampaignLocationModuleLocation::TABLE . '.' . CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID)
            ->get()
            ->each(fn($model) => $this->availableBudgetData->put($model->location_id, new AvailableBudgetDataModel(
                industryType: $industry->slug,
                locationId: $model->location_id,
                countyLocationId: 0,
                potentialQueuedRevenue: 0,
                campaignIds: explode(',', $model->campaign_id),
                campaignCompanyIds: explode(',', $model->campaign_company_id),
                companyIds: explode(',', $model->company_id),
                odinCompanyIds: explode(',', $model->odin_company_id),
                industryId: $industry->id,
            )));
    }

    /**
     * @param array $campaignIds
     *
     * @return void
     */
    protected function calculateUnlimitedBudgetCount(array $campaignIds): void
    {
        $this->getBaseQuery($campaignIds)
            ->selectRaw(CompanyCampaignLocationModuleLocation::TABLE . '.' . CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID .
                        ', COUNT(DISTINCT(' . CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_COMPANY_ID . ')) as total')
            ->join(
                BudgetContainer::TABLE,
                BudgetContainer::TABLE . '.' . BudgetContainer::FIELD_CAMPAIGN_ID,
                '=',
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID
            )->join(
                Budget::TABLE,
                Budget::TABLE . '.' . Budget::FIELD_BUDGET_CONTAINER_ID,
                '=',
                BudgetContainer::TABLE . '.' . BudgetContainer::FIELD_ID
            )->where(Budget::TABLE . '.' . Budget::FIELD_TYPE, BudgetType::NO_LIMIT)
            ->where(Budget::TABLE . '.' . Budget::FIELD_PRODUCT_CONFIGURATION, BudgetProductConfigurationEnum::LEAD_VERIFIED)
            ->groupBy(CompanyCampaignLocationModuleLocation::TABLE . '.' . CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID)
            ->get()
            ->each(function ($model) {
                /** @var AvailableBudgetDataModel $dataModel */
                $dataModel = $this->availableBudgetData[$model->location_id] ?? null;

                if ($dataModel) {
                    $dataModel->unlimitedBudgetCount   = $model->total;
                    $dataModel->availableCampaignCount = $model->total;
                }
            });
    }

    /**
     * @param array $campaignIds
     *
     * @return void
     */
    protected function calculateAvailableVolume(array $campaignIds): void
    {
        $budgets = $this->getBudgetSpendBaseQuery($campaignIds)
            ->where(Budget::TABLE . '.' . Budget::FIELD_TYPE, BudgetType::TYPE_DAILY_UNITS)
            ->get()
            ->groupBy(Budget::FIELD_ID);;

       if ($budgets->isNotEmpty()) {
           $this->calculateBudgetUsage($budgets, BudgetType::TYPE_DAILY_UNITS);
       }

    }

    /**
     * @param array $campaignIds
     *
     * @return void
     */
    protected function calculateAvailableDollars(array $campaignIds): void
    {
        $budgets = $this->getBudgetSpendBaseQuery($campaignIds)
            ->where(Budget::TABLE . '.' . Budget::FIELD_TYPE, BudgetType::TYPE_DAILY_SPEND)
            ->get()
            ->groupBy(Budget::FIELD_ID);

        if ($budgets->isNotEmpty()) {
            $this->calculateBudgetUsage($budgets, BudgetType::TYPE_DAILY_SPEND);
        }
    }

    /**
     * @param Collection<Budget> $budgets
     * @param BudgetType $budgetType
     *
     * @return void
     */
    protected function calculateBudgetUsage(Collection $budgets, BudgetType $budgetType): void
    {
        $availableBudgets = collect();

        $budgets->each(function (Collection $budgets) use ($availableBudgets, $budgetType){
            /** @var Budget $budget */
            $budget = $budgets->first();

            $available = ($budget->value * ($budget->maximum_budget_usage / 100)) - $this->getAverageDaily(
                    $budgets,
                    $this->calculateNumberOfDays($budget),
                    $budgetType,
                    $this->getBudgetStartDate($budget)
                );

            if (
                config('sales.available_budgets.omit_low_never_exceed_budget') &&
                $budget->never_exceed_budget &&
                $available < ($budgetType === BudgetType::TYPE_DAILY_SPEND ? self::AVAILABLE_BUDGET_SPEND_MINIMUM : self::AVAILABLE_BUDGET_VOLUME_MINIMUM)
            ) {
                $available = 0;
            }

            // We want to add the greatest available budget per company that is over 0
            if($available > 0) {
                $existingValue = $availableBudgets->get($budget->company_id);

                if(is_null($existingValue) || $available > $existingValue) {
                    $availableBudgets->put(
                        $budget->company_id,
                        intval(ceil($available))
                    );
                }
            }
        });

        $this->updateAvailableBudget($availableBudgets->toArray(), $budgetType);
    }

    /**
     * @param Collection $budgets
     * @param int $numberOfDays
     * @param BudgetType $budgetType
     * @param Carbon $budgetStart
     *
     * @return float
     */
    protected function getAverageDaily(Collection $budgets, int $numberOfDays, BudgetType $budgetType, Carbon $budgetStart): float
    {
        $budgets = $budgets->filter(fn(Budget $budget) => $budget->{ProductAssignment::FIELD_DELIVERED_AT} && Carbon::parse($budget->{ProductAssignment::FIELD_DELIVERED_AT}) >= $budgetStart);

        if ($budgets->isEmpty()) {
            return 0;
        }

        return match ($budgetType) {
            BudgetType::TYPE_DAILY_UNITS => $budgets->count() / $numberOfDays,
            BudgetType::TYPE_DAILY_SPEND => $budgets->sum(ProductAssignment::FIELD_COST) / $numberOfDays,
            default => 0
        };
    }

    /**
     * @param Budget $budget
     *
     * @return int
     */
    protected function calculateNumberOfDays(Budget $budget): int
    {
        $numberOfDays = (int) $this->getBudgetStartDate($budget)->diffInDays(Carbon::now(), true);

        return $numberOfDays > 0 ? $numberOfDays : 1;
    }

    /**
     * @param Budget $budget
     *
     * @return Carbon
     */
    protected function getBudgetStartDate(Budget $budget): Carbon
    {
        $startingTimestamp = 0;
        $thirtyDaysAgo = Carbon::today()->subDays(29)->timestamp;
        if ($budget->last_modified_at) {
            $startingTimestamp = $budget->last_modified_at->startOfday()->timestamp;
        }
        return Carbon::createFromTimestamp(max($startingTimestamp, $thirtyDaysAgo));
    }

    /**
     * @param array $availableBudgets
     * @param BudgetType $budgetType
     *
     * @return void
     */
    protected function updateAvailableBudget(array $availableBudgets, BudgetType $budgetType): void
    {
        $this->availableBudgetData->each(function (AvailableBudgetDataModel $model) use ($availableBudgets, $budgetType){
            foreach (array_keys(array_flip($model->odinCompanyIds)) as $companyId) {
                $value = $availableBudgets[$companyId] ?? 0;

                if($value > 0) {
                    switch ($budgetType) {
                        case BudgetType::TYPE_DAILY_SPEND:
                            $model->availableBudgetDollars += $value;
                            $model->availableCampaignCount++;
                            break;
                        case BudgetType::TYPE_DAILY_UNITS:
                            $model->availableBudgetVolume += $value;
                            $model->availableCampaignCount++;
                            break;
                    }
                }
            }
        });
    }

    /**
     * @return void
     */
    protected function populateCountyLocations(): void
    {
        Location::query()
            ->select([
                Location::TABLE . '.' . Location::ID . ' as location_id',
                'l2.' . Location::ID . ' as county_location_id',
            ])
            ->whereIn(Location::TABLE . '.' . Location::ID, $this->availableBudgetData->keys())
            ->join(Location::TABLE . " AS l2", function ($join) {
                $join
                    ->on(
                        'l2.' . Location::STATE_ABBREVIATION,
                        '=',
                        Location::TABLE . '.' . Location::STATE_ABBREVIATION
                    )
                    ->on(
                        'l2.' . Location::COUNTY_KEY,
                        '=',
                        Location::TABLE . '.' . Location::COUNTY_KEY
                    )
                    ->where('l2.' . Location::TYPE, Location::TYPE_COUNTY);
            })
            ->each(function ($model) {
                /** @var AvailableBudgetDataModel $dataModel */
                $dataModel = $this->availableBudgetData[$model->location_id] ?? null;

                if ($dataModel) {
                    $dataModel->countyLocationId = $model->county_location_id;
                }
            });
    }

    /**
     * @param array $campaignIds
     *
     * @return Builder
     */
    protected function getBudgetSpendBaseQuery(array $campaignIds): Builder
    {
        return Budget::query()
            ->select([
                Budget::TABLE . '.' . Budget::FIELD_ID,
                Budget::TABLE . '.' . Budget::FIELD_VALUE,
                Budget::TABLE . '.' . Budget::FIELD_LAST_MODIFIED_AT,
                BudgetContainer::TABLE . '.' . BudgetContainer::FIELD_CAMPAIGN_ID,
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COST,
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED_AT,
                CompanyConfiguration::TABLE . '.' . CompanyConfiguration::FIELD_NEVER_EXCEED_BUDGET,
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_COMPANY_ID,
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_MAXIMUM_BUDGET_USAGE
            ])
            ->join(
                BudgetContainer::TABLE,
                BudgetContainer::TABLE . '.' . BudgetContainer::FIELD_ID,
                '=',
                Budget::TABLE . '.' . Budget::FIELD_BUDGET_CONTAINER_ID
            )->join(
                CompanyCampaign::TABLE,
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID,
                '=',
                BudgetContainer::TABLE . '.' . BudgetContainer::FIELD_CAMPAIGN_ID
            )
            ->join(
                CompanyConfiguration::TABLE,
                CompanyConfiguration::TABLE . '.' . CompanyConfiguration::FIELD_COMPANY_ID,
                '=',
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_COMPANY_ID,
            )
            ->leftJoin(ProductAssignment::TABLE, function (JoinClause $joinClause) {
                $joinClause->on(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_BUDGET_ID, '=',Budget::TABLE . '.' . Budget::FIELD_ID)
                    ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED, true)
                    ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CHARGEABLE, true)
                    ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED_AT, '>=', now()->subDays(31))
                    ->whereNotExists(function ($query) {
                        $query->select(DB::raw(1))
                            ->from(ProductRejection::TABLE)
                            ->whereColumn(ProductRejection::TABLE.'.'.ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID, ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_ID)
                            ->whereNull(ProductRejection::TABLE.'.'.ProductRejection::FIELD_DELETED_AT);
                    });
            })
            ->where(Budget::TABLE . '.' . Budget::FIELD_PRODUCT_CONFIGURATION, BudgetProductConfigurationEnum::LEAD_VERIFIED)
            ->whereIn(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID, $campaignIds);
    }

    /**
     * @param array $campaignIds
     *
     * @return Builder
     */
    protected function getBaseQuery(array $campaignIds): Builder
    {
        return CompanyCampaign::query()
            ->join(
                CompanyCampaignLocationModule::TABLE,
                CompanyCampaignLocationModule::TABLE . '.' . CompanyCampaignLocationModule::FIELD_CAMPAIGN_ID,
                '=',
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID
            )->join(
                CompanyCampaignLocationModuleLocation::TABLE,
                CompanyCampaignLocationModuleLocation::TABLE . '.' . CompanyCampaignLocationModuleLocation::FIELD_MODULE_ID,
                '=',
                CompanyCampaignLocationModule::TABLE . '.' . CompanyCampaignLocationModule::FIELD_ID
            )->whereIn(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID, $campaignIds);
    }

    /**
     * Estimate the revenue currently queued in pending allocation
     *
     * @param int $productId
     * @param int $industryId
     * @return void
     */
    protected function estimatePotentialQueuedRevenue(int $productId, int $industryId): void
    {
        /** @var AverageProductRevenueByLocationService $averageRevenueService */
        $averageRevenueService = app(AverageProductRevenueByLocationService::class);
        $countyIds = $this->availableBudgetData->pluck('countyLocationId')
            ->toArray();
        $countyLocationsAverages = $averageRevenueService->getAverageProductRevenueByCountyId($productId, $industryId, $countyIds);
        $defaultAverage = $averageRevenueService->getDefaultProductRevenue($productId);

        $pendingProducts = $this->getQueuedProductsForIndustry($productId, $industryId);

        $this->availableBudgetData->each(function(AvailableBudgetDataModel $dataModel) use ($countyLocationsAverages, $defaultAverage, $pendingProducts) {
            $queuedProducts = $pendingProducts[$dataModel->locationId] ?? 0;
            $dataModel->potentialQueuedRevenue = $queuedProducts * ($countyLocationsAverages[$dataModel->countyLocationId] ?? $defaultAverage);
        });
    }

    /**
     * @param int $productId
     * @param int $industryId
     * @return array
     */
    protected function getQueuedProductsForIndustry(int $productId, int $industryId): array
    {
        /** @var ConsumerProductService $consumerProductService */
        $consumerProductService = app(ConsumerProductService::class);

        return $consumerProductService->getConsumerProductsPendingAllocationQuery($productId, $industryId, 5)
            ->join(Address::TABLE, Address::TABLE .'.'. Address::FIELD_ID, '=', ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ADDRESS_ID)
            ->join(Location::TABLE, Location::TABLE .'.'. Location::ZIP_CODE, '=', Address::TABLE .'.'. Address::FIELD_ZIP_CODE)
            ->groupBy(Location::TABLE .'.'. Location::ID)
            ->selectRaw(Location::TABLE .'.'. Location::ID . ', count(' . ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ID . ') as products')
            ->get()
            ->mapWithKeys(fn($location) => [
                $location->id => $location->products
            ])->toArray();
    }
}
