<?php

namespace App\Contracts\Strategies\CampaignBudget;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Collection as BaseCollection;

interface CalculateBudgetStrategy
{
    /**
     * <PERSON>les calculating budget available for a given set of
     * campaign ID's.
     *
     * @param Collection $campaigns
     *
     * @return array| BaseCollection
     */
    public function calculateBudgetAvailable(Collection $campaigns): array|BaseCollection;

    /**
     * <PERSON><PERSON> calculating historical budget available for a given set of
     * campaign ID's.
     *
     * @param Collection $campaigns
     * @return array
     */
    public function calculateHistoricalBudgetAvailable(Collection $campaigns): array;
}
