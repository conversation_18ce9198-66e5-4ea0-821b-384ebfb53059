<?php

namespace App\Contracts\Search;

use Illuminate\Support\Collection;

interface SearchableContract
{
    /**
     * Determine if the user is authorized to search for this stuff.
     *
     * @return bool
     */
    public function authorize(): bool;

    /**
     * Returns the category that this search contract belongs to.
     *
     * @return string
     */
    public function getCategory(): string;

    /**
     * <PERSON><PERSON> performing the search based on the query.
     *
     * @param string $query
     * @return Collection
     */
    public function search(string $query): Collection;
}
