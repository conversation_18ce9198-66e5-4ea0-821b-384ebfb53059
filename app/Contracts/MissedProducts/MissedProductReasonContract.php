<?php

namespace App\Contracts\MissedProducts;

use App\Enums\MissedProducts\MissedProductCategory;
use App\Enums\MissedProducts\MissedProductReasonEventType;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\Company;
use App\Models\Odin\IndustryService;
use Carbon\Carbon;
use Illuminate\Support\Collection;

interface MissedProductReasonContract
{
    /**
     * Sets the company to operate on.
     *
     * @param Company|null $company
     * @return self
     */
    public function setCompany(?Company $company): self;

    /**
     * Sets the company to operate on.
     *
     * @param IndustryService|null $service
     * @return self
     */
    public function setService(?IndustryService $service): self;

    /**
     * @param CompanyCampaign $campaign
     * @return self
     */
    public function setCampaign(CompanyCampaign $campaign): self;

    /**
     * @return MissedProductReasonEventType|null
     */
    public function getMissedProductEventType(): ?MissedProductReasonEventType;

    /**
     * @param Carbon $date
     * @return self
     */
    public function setFromDate(Carbon $date): self;

    /**
     * Gets the unique key for this reason.
     *
     * @return string
     */
    public function getKey(): string;

    /**
     * Whether a given company is impacted by this.
     *
     * @return bool
     */
    public function doesImpact(): bool;

    /**
     * The title of this missed reason.
     *
     * @return string
     */
    public function getTitle(): string;

    /**
     * The text for this missed reason for display in email summary.
     *
     * @return string
     */
    public function getSummaryText(): string;

    /**
     * Gets a detailed message of why this missed reason is valid.
     *
     * @return string
     */
    public function getMessage(): string;

    /**
     * Gets the category this relates too
     *
     * @return MissedProductCategory
     */
    public function getCategory(): MissedProductCategory;

    /**
     * @return Collection|null
     */
    public function getTimeline(): ?Collection;
}
