<?php

namespace App\Contracts;

use App\Models\Legacy\EloquentQuote;
use App\Models\Sales\Task;

interface DynamicPriorityCalculationServiceContract
{
    /**
     * Calculate dynamic priority
     *
     * @param Task $task
     * @param EloquentQuote|null $lead
     * @param int|null $unsoldLeadCount
     * @return int|null
     */
    public function calculate(Task $task, ?EloquentQuote $lead, ?int $unsoldLeadCount): ?int;
}
