<?php

namespace App\Contracts\Campaigns;

use App\Campaigns\Modules\DataModels\AllocateData;
use App\Campaigns\Modules\DataModels\PostAllocationData;
use App\DataModels\Campaigns\ConsumerProject;
use App\DataModels\ProductAssignment\ProposedProductAssignment;
use App\Enums\Campaigns\CampaignStatus;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ProductAssignment;
use Illuminate\Support\Collection;

interface CampaignModuleContract
{
    /**
     * Lifecycle event for when a campaign is saved.
     *
     * @param CompanyCampaign $campaign
     * @param Collection $payload
     * @return bool
     */
    public function save(CompanyCampaign $campaign, Collection $payload): bool;

    /**
     * Lifecycle event for when a campaign status is changed.
     *
     * @param CompanyCampaign $campaign
     * @param CampaignStatus $old
     * @param CampaignStatus $new
     * @return bool
     */
    public function statusChange(CompanyCampaign $campaign, CampaignStatus $old, CampaignStatus $new): bool;

    /**
     * Lifecycle event for when a campaign is deleted.
     *
     * @param CompanyCampaign $campaign
     * @return bool
     */
    public function delete(CompanyCampaign $campaign): bool;

    /**
     * Lifecycle event for when a campaign is about to be filtered.
     *
     * @param CompanyCampaign $campaign
     * @param ConsumerProject $project
     * @return bool
     */
    public function preFilter(CompanyCampaign $campaign, ConsumerProject $project): bool;

    /**
     * Lifecycle event for when a campaign is being filtered.
     *
     * @param CompanyCampaign $campaign
     * @param ConsumerProject $project
     * @return bool
     */
    public function filter(CompanyCampaign $campaign, ConsumerProject $project): bool;

    /**
     * Lifecycle event for when a product is being allocated to a company.
     *
     * @param CompanyCampaign $campaign
     * @param ProposedProductAssignment $proposedAssignment
     * @param AllocateData $data
     * @return AllocateData
     */
    public function allocate(CompanyCampaign $campaign, ProposedProductAssignment $proposedAssignment, AllocateData $data): AllocateData;

    /**
     * Lifecycle event for when a product has been allocated to a company.
     *
     * @param CompanyCampaign $campaign
     * @param ProductAssignment $assignment
     * @param PostAllocationData $data
     * @return PostAllocationData
     */
    public function postAllocation(CompanyCampaign $campaign, ProductAssignment $assignment, PostAllocationData $data): PostAllocationData;

    /**
     * Lifecycle event for retrieving data for the frontend
     *
     * @param CompanyCampaign $campaign
     * @return Collection
     */
    public function transform(CompanyCampaign $campaign): Collection;

    /**
     * Lifecycle event for validating a payload
     *
     * @param CompanyCampaign $campaign
     * @param Collection $payload
     * @return bool
     */
    public function validate(CompanyCampaign $campaign, Collection $payload): bool;
}
