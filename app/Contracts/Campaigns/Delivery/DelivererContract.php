<?php

namespace App\Contracts\Campaigns\Delivery;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ConsumerProduct;

interface DelivererContract
{
    /**
     * @param ConsumerProduct $product
     * @param CompanyCampaign $campaign
     * @return bool
     */
    public function deliver(ConsumerProduct $product, CompanyCampaign $campaign): bool;

    /**
     * @param CompanyCampaign $campaign
     * @return bool
     */
    public function save(CompanyCampaign $campaign): bool;
}
