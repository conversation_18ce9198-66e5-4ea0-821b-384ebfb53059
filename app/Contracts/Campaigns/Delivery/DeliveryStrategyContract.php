<?php

namespace App\Contracts\Campaigns\Delivery;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ConsumerProduct;

interface DeliveryStrategyContract
{
    /**
     * @param ConsumerProduct $product
     * @param CompanyCampaign $campaign
     * @param array|null $payload
     * @return bool
     */
    public function deliver(ConsumerProduct $product, CompanyCampaign $campaign, ?array $payload = null): bool;
}
