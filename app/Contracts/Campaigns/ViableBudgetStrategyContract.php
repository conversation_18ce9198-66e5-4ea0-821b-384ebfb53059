<?php

namespace App\Contracts\Campaigns;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use Illuminate\Support\Collection;

interface ViableBudgetStrategyContract
{
    /**
     * @param Collection<int, CompanyCampaign> $campaigns
     * @param string $product
     * @param string $qualityTier
     * @param string $salesType
     * @return Collection<int, Budget>
     */
    public function getViableCampaignBudgetsForProductVariant(Collection $campaigns, string $product, string $qualityTier, string $salesType): Collection;
}
