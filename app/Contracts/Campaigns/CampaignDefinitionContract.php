<?php

namespace App\Contracts\Campaigns;

use Illuminate\Support\Collection;

interface CampaignDefinitionContract
{
    /**
     * Returns the modules registered with this campaign.
     *
     * @return Collection<CampaignModuleContract>
     */
    public function modules(): Collection;

    /**
     * Returns only the modules with frontend components
     *
     * @return Collection
     */
    public function frontendModules(): Collection;
}
