<?php

namespace App\Contracts\Campaigns;

interface HasFrontendComponent
{
    const WIZARD_KEY_NAME        = 'name';
    const WIZARD_KEY_MODULE_KEY  = 'key';
    const WIZARD_KEY_INPUTS      = 'inputs';
    const WIZARD_KEY_HEADER      = 'header'; // Pass input settings for Wizard custom header to the front end
    const WIZARD_KEY_PAYLOAD     = 'payload'; // If set, will bundle all inputs in the payload key as an array
    const INPUT_KEY_VALIDATION   = 'validation';
    const INPUT_KEY_COMPONENT    = 'component';
    const INPUT_KEY_INITIAL_DATA = 'initialData';
    const INPUT_KEY_NAME         = 'name';
    const INPUT_CONTAINED_KEYS   = 'containedKeys';

    /**
     * Returns the identifier for this module on the frontend.
     *
     * @return string
     */
    public function getFrontendKey(): string;

    /**
     * Returns the expected inputs/component configuration for this module.
     * Return null if this module does not have a standalone frontend component.
     *
     * @return array|null
     */
    public function getFrontendModuleConfiguration(): ?array;

    /**
     * Return the expected inputs/components configuration for this module's Wizard slide
     * Return null if this module does not have a Wizard slide
     *
     * @return array|null
     */
    public function getFrontendWizardConfiguration(): ?array;
}
