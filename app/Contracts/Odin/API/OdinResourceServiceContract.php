<?php

namespace App\Contracts\Odin\API;

use App\Services\Odin\API\APIFieldModel;
use Illuminate\Support\Collection;

interface OdinResourceServiceContract
{
    /**
     * Handles creating a resource via the Odin API.
     *
     * @param Collection<APIFieldModel> $data
     * @return bool
     */
    public function create(Collection $data): bool;

    /**
     * Handles updating a resource via the Odin API.
     *
     * @param mixed $primaryKey
     * @param Collection<APIFieldModel> $data
     * @return bool
     */
    public function update(mixed $primaryKey, Collection $data): bool;

    /**
     * Handles deleting a resource via the Odin API.
     *
     * @param mixed $primaryKey
     * @return bool
     */
    public function delete(mixed $primaryKey): bool;
}
