<?php

namespace App\Contracts\Odin;


use App\Models\Odin\Company;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

interface ContactIdentificationServiceContract
{
    /**
     * Get all records that matches with given phone number
     * @param string $phone
     * @return Collection
     */
    public function recognize(string $phone): Collection;

    /**
     * Transform single entity to known format
     * @param mixed $entity
     * @return array
     */
    public function transform(mixed $entity): array;

    /**
     * Get friendly relation type
     * In order to make it easy to identify what relation this contact belongs, this method should return a string with a human-readable value.
     * Example => type: company_user, friendly type: Company User
     * @return string
     */
    public function getFriendlyRelationType(): string;

    /**
     * Get relation type
     * @return string
     */
    public function getRelationType(): string;

    /**
     * Get relation subtype
     * @param mixed $entity
     * @return string
     */
    public function getRelationSubtype(mixed $entity): string;


    /**
     * Get friendly relation subtype
     * In order to make it easy to identify what relation this contact belongs, this method should return a string with a human-readable value.
     * Example => type: company_user, friendly type: Company User
     * @param mixed $entity
     * @return string
     */
    public function getFriendlyRelationSubtype(mixed $entity): string;


    /**
     * Get company by id
     * @param string $contactId
     * @return Company
     */
    public function getCompanyByContactId(string $contactId): Company;


    /**
     * Get related contact by id
     * @param string[] $relationIds
     * @return Collection
     */
    public function getRelatedContactsByIds(array $relationIds): Collection;
}
