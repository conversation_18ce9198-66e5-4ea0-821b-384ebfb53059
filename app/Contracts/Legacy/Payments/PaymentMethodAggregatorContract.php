<?php

namespace App\Contracts\Legacy\Payments;

interface PaymentMethodAggregatorContract
{
    /**
     * Attempts to find a payment method.
     *
     * @param string $gateway
     * @param string $id
     * @return CreditCardPaymentMethodContract|null
     */
    public function get(string $gateway, string $id): ?CreditCardPaymentMethodContract;

    /**
     * Returns all payment methods.
     *
     * @return CreditCardPaymentMethodContract[]
     */
    public function all(): array;

    /**
     * Deletes a payment method.
     *
     * @param string $gateway
     * @param string $id
     * @return void
     */
    public function delete(string $gateway, string $id): void;

    /**
     * Returns all preferred methods.
     *
     * @return CreditCardPaymentMethodContract[]
     */
    public function preferred(): array;

    /**
     * Returns the default method.
     *
     * @return CreditCardPaymentMethodContract|null
     */
    public function default(): ?CreditCardPaymentMethodContract;

    /**
     * Returns whether any methods exist.
     *
     * @return bool
     */
    public function exists(): bool;
}
