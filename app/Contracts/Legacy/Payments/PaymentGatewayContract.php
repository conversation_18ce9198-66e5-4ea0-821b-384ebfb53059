<?php

namespace App\Contracts\Legacy\Payments;

interface PaymentGatewayContract
{
    public function id(): string;

    public function name(): string;

    /**
     * @return CreditCardPaymentMethodContract[]
     */
    public function paymentMethods(): array;

    public function hasAccount(): bool;

    public function deletePaymentMethod(string $id): void;

    public function setDefaultPaymentMethod(string $id): void;

    public function updatePaymentMethod(CreditCardPaymentMethodContract $method): void;
}
