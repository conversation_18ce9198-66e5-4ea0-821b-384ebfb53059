<?php

namespace App\Contracts\Legacy\Payments;

interface PaymentGatewayAccountRepositoryContract
{
    public function getGatewayId(): string;

    public function getAccountId(): ?string;

    public function getDefaultPaymentMethodId(): ?string;

    public function setDefaultPaymentMethodId(): ?string;

    public function setAccountId(string $id): void;

    public function getCompanyId(): int;

    public function getCompanyName(): string;
}
