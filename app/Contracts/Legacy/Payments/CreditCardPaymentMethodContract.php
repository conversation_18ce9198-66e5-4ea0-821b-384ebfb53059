<?php

namespace App\Contracts\Legacy\Payments;

interface CreditCardPaymentMethodContract
{
    /**
     * Returns the id of the card.
     *
     * @return string
     */
    public function id(): string;

    /**
     * Returns the masked number of the card.
     *
     * @return string
     */
    public function maskedNumber(): string;

    /**
     * Returns the brand of the card.
     *
     * @return string
     */
    public function brand(): string;

    /**
     * Returns the type of card (Debit/Credit/Etc).
     *
     * @return string
     */
    public function type(): string;

    public function expiry(): string;

    public function expiryMonth(): string;

    public function expiryYear(): string;

    public function setExpiryMonth(string $month): self;

    public function setExpiryYear(string $year): self;

    public function name(): string;

    public function firstName(): string;

    public function lastName(): string;

    public function setName(string $name): self;

    public function company(): ?string;

    public function setCompany(?string $company): self;

    public function addressLineOne(): ?string;

    public function setAddressLineOne(?string $lineOne): self;

    public function addressLineTwo(): ?string;

    public function setAddressLineTwo(?string $lineTwo): self;

    public function city(): ?string;

    public function setCity(?string $city): self;

    public function state(): ?string;

    public function setState(?string $state): self;

    public function zipCode(): ?string;

    public function setZipCode(?string $zipCode): self;

    public function country(): ?string;

    public function setCountry(?string $country): self;

    public function paymentGateway(): PaymentGatewayContract;

    public function save(): void;

    public function default(): bool;

    public function setDefault(bool $default = true): self;
}
