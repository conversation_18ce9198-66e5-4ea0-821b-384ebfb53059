<?php

namespace App\Contracts\Workflows;

use App\Enums\Channel;

/**
 * To be implemented by Actions that create notifications.
 *
 */
interface HasNotificationContract extends HasTargetsContract
{
    /**
     * @return int
     */
    public function getNotifier(): int;

    /**
     * @return Channel
     */
    public function getChannel(): Channel;

    /**
     * @return string
     */
    public function getSubject(): string;

    /**
     * @return string
     */
    public function getMessage(): string;
}
