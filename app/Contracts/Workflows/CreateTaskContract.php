<?php

namespace App\Contracts\Workflows;

use App\DataModels\Target;
use App\DataModels\Workflows\TaskNotifiersDataModel;
use App\DataModels\Workflows\TaskResultDataModel;
use App\Models\Action;
use App\Models\Legacy\EloquentQuote;
use Carbon\Carbon;
use Illuminate\Support\Collection;

interface CreateTaskContract
{
    /**
     * @return int
     */
    public function getAssignedUserId(): ?int;

    /**
     * @return string
     */
    public function getSubject(): string;

    /**
     * @return Collection<TaskResultDataModel>
     */
    public function getResults(): Collection;

    /**
     * @return Carbon
     */
    public function getAvailableAt(): Carbon;

    /**
     * @return int
     */
    public function getTaskType(): int;

    /**
     * @return int
     */
    public function getPriority(): int;

    /**
     * @return bool
     */
    public function getReschedulingStatus(): bool;

    /**
     * Returns the list of notifiers for when a task has not been actioned.
     *
     * @return Collection<TaskNotifiersDataModel>
     */
    public function getNotifiers(): Collection;

    /**
     * Whether the task being created should inherit task notes.
     *
     * @return bool
     */
    public function shouldInheritTaskNotes(): bool;

    /**
     * @return int
     */
    public function getTaskCategory(): int;

    /**
     * @return int
     */
    public function getDynamicPriorityStatus(): int;

    /**
     * @return int|null
     */
    public function getDynamicPriorityType(): ?int;

    /**
     * @return EloquentQuote|null
     */
    public function getLead(): ?EloquentQuote;

    /**
     * @return Target
     */
    public function getTaskTarget(): Target;

    /**
     * @return string
     */
    public function getTaskEventCategory(): string;

    /**
     * @return string
     */
    public function getTaskEventName(): string;
}
