<?php

namespace App\Contracts\Workflows;

use App\Enums\Target;
use App\Enums\TargetRelation;

/**
 * To be implemented by Actions that cater to targets.
 */
interface HasTargetContract
{
    /**
     * @return int|string
     */
    public function getTarget(): int|string;

    /**
     * @return TargetRelation|null
     */
    public function getRelationType(): ?TargetRelation;

    /**
     * @return Target
     */
    public function getTargetType(): Target;
}
