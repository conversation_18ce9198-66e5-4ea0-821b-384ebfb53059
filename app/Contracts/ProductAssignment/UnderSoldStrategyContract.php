<?php

namespace App\Contracts\ProductAssignment;

use App\DataModels\Campaigns\ConsumerProject;
use App\DataModels\ProductAssignment\ProposedProductAssignment;
use App\Models\Odin\Product;
use Illuminate\Support\Collection;

interface UnderSoldStrategyContract
{
    /**
     * <PERSON>les determining if we should continue to attempt assignment for the subsequent product type, or break 'BRS' loop
     *
     * @param ConsumerProject $project
     * @param Collection<int, ProposedProductAssignment> $proposedAssignments
     * @param ?Product $product
     * @return bool
     */
    public function shouldContinueAssigningNow(ConsumerProject $project, Collection $proposedAssignments, ?Product $product = null): bool;

    /**
     * <PERSON>les determining if a future allocation attempt is required, based on the currently proposed assignments
     *
     * @param ConsumerProject $project
     * @param Collection<int, ProposedProductAssignment> $proposedAssignments
     * @return bool
     */
    public function shouldScheduleAssignmentReattempt(ConsumerProject $project, Collection $proposedAssignments): bool;

    /**
     * Calculates the delay in seconds for the rescheduled allocation attempt, if required
     *
     * @param ConsumerProject $project
     * @param Collection<int, ProposedProductAssignment> $proposedAssignments
     * @return int|null
     */
    public function getReattemptDelay(ConsumerProject $project, Collection $proposedAssignments): ?int;
}
