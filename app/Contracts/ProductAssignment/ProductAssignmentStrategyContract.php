<?php

namespace App\Contracts\ProductAssignment;

use App\DataModels\Campaigns\ConsumerProject;
use App\DataModels\ProductAssignment\ProposedProductAssignment;
use App\Models\Campaigns\CompanyCampaign;
use App\Services\ConsumerProductLifecycleTrackingService;
use Illuminate\Support\Collection;

interface ProductAssignmentStrategyContract
{
    /**
     * Handles calculation of proposed product assignments for specific product type
     *
     * @param ConsumerProject $project
     * @param Collection<int, CompanyCampaign> $campaigns
     * @param Collection<int, ProposedProductAssignment> $assertedProductAssignments
     * @param ConsumerProductLifecycleTrackingService|null $tracker
     * @return Collection<int, ProposedProductAssignment>
     */
    public function calculate(
        ConsumerProject $project,
        Collection $campaigns,
        Collection $assertedProductAssignments,
        ?ConsumerProductLifecycleTrackingService $tracker = null,
    ): Collection;
}
