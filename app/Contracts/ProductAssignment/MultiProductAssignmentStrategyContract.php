<?php

namespace App\Contracts\ProductAssignment;

use App\DataModels\Campaigns\ConsumerProject;
use App\DataModels\ProductAssignment\ProposedProductAssignment;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\Product;
use App\Services\ConsumerProductLifecycleTrackingService;
use Illuminate\Support\Collection;

interface MultiProductAssignmentStrategyContract
{
    /**
     * Handles calculation of proposed product assignments across all available product types
     *
     * @param ConsumerProject $project
     * @param Collection<int, CompanyCampaign> $campaigns
     * @param Collection<int, Product> $productTypes
     * @param array $payload
     * @param ConsumerProductLifecycleTrackingService|null $tracker
     * @return Collection<int, ProposedProductAssignment>
     */
    public function calculate(
        ConsumerProject $project,
        Collection $campaigns,
        Collection $productTypes,
        array $payload,
        ?ConsumerProductLifecycleTrackingService $tracker = null,
    ): Collection;
}
