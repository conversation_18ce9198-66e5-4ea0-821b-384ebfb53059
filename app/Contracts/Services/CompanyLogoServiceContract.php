<?php

namespace App\Contracts\Services;

use App\Models\Odin\Company;
use Google\Cloud\Core\Exception\BadRequestException;
use Illuminate\Http\UploadedFile;
use Exception;

interface CompanyLogoServiceContract
{
    /**
     * Processes the existing logo of a company and creates its new version.
     * Also, gears up to upload the new link of a logo to the Cloud.
     *
     * @param Company $company
     * @return bool|string
     * @throws BadRequestException
     */
    public function handle(Company $company): bool|string;

    /**
     * Handles preparing the Cloud storage URL for the given file name against the requested company.
     *
     * @param Company $company
     * @param string  $fileName
     * @return string
     */
    public function prepareNewUrlForUpload(Company $company, string $fileName): string;

    /**
     * Handles processing the given link of a logo and returns its `UploadedFile` instance.
     *
     * @param string $fileUrl
     * @return UploadedFile|null
     * @throws BadRequestException
     */
    public function prepareFileForUpload(string $fileUrl): ?UploadedFile;

    /**
     * Handles uploading the given file to the Cloud.
     *
     * @param Company      $company
     * @param UploadedFile $file
     * @return string|null
     * @throws Exception
     */
    public function uploadFileToCloud(Company $company, UploadedFile $file): ?string;

    /**
     * Returns base Url and bucket which are used to generate the Cloud storage link of a logo.
     *
     * @return string
     */
    public function getCompanyLogoBaseUrlWithBucket(): string;
}
