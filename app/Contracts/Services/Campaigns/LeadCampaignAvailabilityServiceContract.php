<?php

namespace App\Contracts\Services\Campaigns;

use Illuminate\Support\Collection;

interface LeadCampaignAvailabilityServiceContract
{
    /**
     * Returns whether there are companies available for the allocation of a given lead.
     *
     * @param string $leadReference
     * @param bool $onlyAdded
     * @param array|null $leadSalesTypeConfigurationIds
     * @param array $ignoreCompanyIds
     * @param int|null $timeout
     *
     * @return bool
     */
    public function areCompaniesAvailable(
        string $leadReference,
        bool   $onlyAdded = false,
        ?array $leadSalesTypeConfigurationIds = null,
        array  $ignoreCompanyIds = [],
        ?int $timeout = null
    ): bool;

    /**
     * Handles verifying if all companies are over budget for a given lead.
     *
     * @param string $leadReference
     * @param int|null $timeout
     *
     * @return bool
     */
    public function areAllCompaniesOverBudget(string $leadReference, ?int $timeout = null): bool;

    /**
     * @param string $leadReference
     * @return int
     */
    public function companiesWithAvailableBudgetCount(string $leadReference): int;

    /**
     * @param string $leadReference
     * @return int
     */
    public function companiesWithAvailableUnverifiedBudgetCount(string $leadReference): int;

    /**
     * @param string $leadReference
     * @param int|null $timeout
     *
     * @return bool
     */
    public function checkLeadHasSuperPremiumBuyers(string $leadReference, ?int $timeout = null): bool;

    /**
     * @param string $leadReference
     * @param array $ignoreCompanyIds
     * @param array $forceIncludeCampaignIds
     * @param bool $excludeBudget
     * @param int|null $remainingLegs
     * @param int|null $legsRequested
     * @return Collection
     */
    public function getBestOptionSalesTypeConfigurationsExcludingUnverified(
        string $leadReference,
        array  $ignoreCompanyIds = [],
        array  $forceIncludeCampaignIds = [],
        bool   $excludeBudget = false,
        ?int   $remainingLegs = null,
        ?int   $legsRequested = null
    ): Collection;

    /**
     * @param string $leadReference
     * @param array $ignoreCompanyIds
     * @return Collection
     */
    public function getBestOptionSalesTypeConfigurationsOnlyUnverified(
        string $leadReference,
        array  $ignoreCompanyIds = []
    ): Collection;
}
