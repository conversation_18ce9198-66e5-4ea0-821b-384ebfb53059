<?php

namespace App\Contracts\Services\CompanyRegistration;

use App\Models\ContractKey;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Website;
use Exception;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Collection;

interface CompanyRegistrationServiceContract
{
    /**
     * @param string $name
     * @param string $entityName
     * @param string|null $watchDogId
     * @param string|null $website
     * @param string|null $registrationDomain
     *
     * @return Company
     */
    public function registerBaseCompany(string $name, string $entityName, ?string $watchDogId = null, ?string $website = null, ?string $registrationDomain = null): Company;

    /**
     * @param Company $company
     * @param string $firstName
     * @param string $lastName
     * @param string $email
     * @param string|null $password
     * @param string|null $department
     *
     * @return CompanyUser
     */
    public function addCompanyUser(Company $company, string $firstName, string $lastName, string $email, string|null $password, string|null $department): CompanyUser;

    /**
     * Get Company's IndustryService names and slugs, grouped by Industry
     * @param Company $company
     * @return Collection
     */
    public function getCompanyServiceNamesByIndustry(Company $company): Collection;

    /**
     * Add all required Services and add any Industries as required.
     * Additionally, set up the requested set of services in legacy for the given company.
     *
     * @param Company $company
     * @param string[] $slugs
     * @return int - number of Services updated successfully
     * @throws Exception
     */
    public function refreshCompanyServicesBySlugs(Company $company, array $slugs): int;

    /**
     * Absolute update of Company Industries, any Industries not included will be removed.
     * Additionally, this gears up to set the company type in legacy based on the requested set of industries.
     *
     * @param Company $company
     * @param string[] $industryNames
     * @return int - number of Industries updated successfully on company
     * @throws Exception
     */
    public function refreshCompanyIndustriesByNames(Company $company, array $industryNames): int;

    /**
     * Add a new location/address to a Company during registration
     * @param Company $company
     * @param array $addressData
     * @return CompanyLocation|null
     */
    public function addLocationToCompany(Company $company, array $addressData): ?CompanyLocation;

    /**
     * Fires PubSub event to legacy site to add
     * - location
     * - service area
     * - campaign
     * to a company based on the requested parameters.
     *
     * @param Company         $company
     * @param CompanyLocation $companyLocation
     * @param int|null        $serviceArea
     * @return bool
     */
    public function addServiceAreaToCompany(Company $company, CompanyLocation $companyLocation, ?int $serviceArea = null): bool;

    /**
     * @param Company $company
     * @param array $requestData
     * @return Response
     * @throws Exception
     */
    public function addPaymentMethod(Company $company, array $requestData): Response;

    /**
     * @param Company $company
     * @param array $requestData
     * @return bool
     */
    public function updateCompanyDetails(Company $company, array $requestData): bool;

    /**
     * Handles setting lead budget of the created company in legacy.
     *
     * @param string $companyReference
     * @param int    $budget
     * @return bool
     * @throws Exception
     */
    public function setLeadBudgetOfCreatedCompany(string $companyReference, int $budget): bool;

    /**
     * @param Company $company
     * @param CompanyUser $user
     * @param ContractKey $contractKey
     * @param Website $website
     * @param string|null $ip
     * @param string|null $appId
     * @return array
     */
    public function getCompanyContract(Company $company, CompanyUser $user, ContractKey $contractKey, Website $website, ?string $ip, ?string $appId): array;

    /**
     * Handles setting lead contact of the created company.
     *
     * @param Company $company
     * @param array   $requestData
     * @return bool
     * @throws Exception
     */
    public function setLeadContactOfCreatedCompany(Company $company, array $requestData): bool;

}
