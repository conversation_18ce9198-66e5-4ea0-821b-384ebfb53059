<?php

namespace App\Contracts\Services;

use App\DTO\Billing\AddressData;
use App\DTO\Billing\InvoiceItemDTO;
use App\DTO\Billing\InvoiceTaxData;

interface TaxServiceApiContract
{
    /**
     * @param AddressData $buyerAddressData
     * @param array<InvoiceItemDTO> $invoiceItemsDtoArray
     * @param string $invoiceUuid
     * @return InvoiceTaxData|null
     */
    public function getInvoiceTax(AddressData $buyerAddressData, array $invoiceItemsDtoArray, string $invoiceUuid): ?InvoiceTaxData;
}
