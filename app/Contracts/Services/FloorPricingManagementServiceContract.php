<?php

namespace App\Contracts\Services;

interface FloorPricingManagementServiceContract
{
    /**
     * @param int $serviceProductId
     * @param int $qualityTierId
     * @param int $propertyTypeId
     * @param bool $checkMissingPrices
     * @return array
     */
    public function getStatePrices(int $serviceProductId, int $qualityTierId, int $propertyTypeId, bool $checkMissingPrices = false): array;

    /**
     * @param int $serviceProductId
     * @param int $qualityTierId
     * @param int $propertyTypeId
     * @param int $stateLocationId
     * @return array
     */
    public function getCountyPrices(int $serviceProductId, int $qualityTierId, int $propertyTypeId, int $stateLocationId): array;

    /**
     * @param array $priceUpdates
     * @param int $serviceProductId
     * @param int $qualityTierId
     * @param int $propertyTypeId
     * @return array
     */
    public function updateScopedNationalPrices(array $priceUpdates, int $serviceProductId, int $qualityTierId, int $propertyTypeId): array;

    /**
     * Sync scoped state floor prices
     *
     * @param array $priceUpdates
     * @param int $stateLocationId
     * @param int $serviceProductId
     * @param int $qualityTierId
     * @param int $propertyTypeId
     * @param array|null $loweredPricePolicy
     * @return array
     */
    public function updateScopedStatePrices(array $priceUpdates, int $stateLocationId, int $serviceProductId, int $qualityTierId, int $propertyTypeId, ?array $loweredPricePolicy = null): array;

    /**
     * @param array $priceUpdates
     * @param int $stateLocationId
     * @param int $countyLocationId
     * @param int $serviceProductId
     * @param int $qualityTierId
     * @param int $propertyTypeId
     * @param array|null $loweredPricePolicy
     * @return array
     */
    public function updateScopedCountyPrices(array $priceUpdates, int $stateLocationId, int $countyLocationId, int $serviceProductId, int $qualityTierId, int $propertyTypeId, ?array $loweredPricePolicy = null): array;

    /**
     * @param int $serviceProductId
     * @return bool
     */
    public function initialisePricingForServiceProduct(int $serviceProductId): bool;

    /**
     * @return array
     */
    public function getDefaultPricingTable(): array;

    /**
     * @param array $data
     * @param int $userId
     * @return bool
     */
    public function saveDefaultPricingTable(array $data, int $userId): bool;

    /**
     * @param int $fromServiceProductId
     * @param int $toServiceProductId
     * @return bool
     */
    public function importAllPricesFromServiceProduct(int $fromServiceProductId, int $toServiceProductId): bool;

    /**
     * @param int $serviceProductId
     * @return array
     */
    public function repairPricesForServiceProduct(int $serviceProductId): array;
}
