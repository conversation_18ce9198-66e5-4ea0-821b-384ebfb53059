<?php

namespace App\Contracts\Services\MarketingCampaign;

use App\DTO\EmailService\OutgoingEmailDTO;
use App\DTO\MarketingCampaign\AddUserToEmailCampaignResponse;
use App\DTO\MarketingCampaign\CreateEmailCampaignResponse;
use App\DTO\MarketingCampaign\EmailCampaignMetrics;
use App\DTO\MarketingCampaign\EmailCampaignUser;
use App\Enums\GlobalConfigurationKey;
use App\Enums\GlobalConfigurationMailchimpField;
use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\GlobalConfigurationMarketingField;
use App\Enums\MarketingCampaigns\MarketingCampaignConsumerStatus;
use App\Enums\MarketingCampaigns\MarketingCampaignStatus;
use App\Enums\MarketingCampaigns\MarketingLogType;
use App\Models\EmailTemplate;
use App\Models\MarketingCampaign;
use App\Repositories\GlobalConfiguration\GlobalConfigurationRepository;
use App\Repositories\MarketingCampaign\MarketingLogRepository;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use MailchimpMarketing\ApiClient;

class MailchimpEmailMarketingService implements EmailMarketingServiceContract
{
    private ApiClient $apiClient;
    private string    $listId;
    private string    $callbackUrl;
    private string    $callbackUrlTagId;
    private string    $fromEmail;
    private string    $fromName;

    public function __construct(GlobalConfigurationRepository $globalConfigurationRepository)
    {
        $mailchimpConfig = $globalConfigurationRepository->getConfigurationPayload(GlobalConfigurationKey::MAILCHIMP)?->toArray()['data'];
        $marketingConfig = $globalConfigurationRepository->getConfigurationPayload(GlobalConfigurationKey::MARKETING)?->toArray()['data'];

        $this->listId           = $mailchimpConfig[GlobalConfigurationMailchimpField::AUDIENCE_ID->value] ?? '';
        $this->callbackUrlTagId = $mailchimpConfig[GlobalConfigurationMailchimpField::MERGE_FIELD_CALLBACK_URL->value] ?? '';

        $this->fromEmail        = $marketingConfig[GlobalConfigurationMarketingField::FROM_EMAIL->value] ?? '';
        $this->fromName         = $marketingConfig[GlobalConfigurationMarketingField::FROM_NAME->value] ?? '';
        $this->callbackUrl      = $marketingConfig[GlobalConfigurationMarketingField::CALLBACK_URL->value] ?? '';

        $this->apiClient = new ApiClient();

        $this->apiClient->setConfig([
            'apiKey' => config('services.marketing.email.mailchimp.api_key'),
            'server' => config('services.marketing.email.mailchimp.server'),
        ]);
    }

    /**
     * @param string $name
     * @return array|null
     */
    public function findOrCreateMarketingCampaignSegment(string $name): ?array
    {
        $segment = $this->createStaticSegment($name);

        if (empty($segment)) {
            //try find from existing segments
            $segment = $this->findSegment($name);
            if (empty($segment)) {
                logger()->error("Failed to create static segment");
                return null;
            }
        }

        return $segment;
    }

    /**
     * @param string $name
     * @param Collection $users
     * @return CreateEmailCampaignResponse|null
     */
    public function createMarketingCampaign(string $name, Collection $users): ?CreateEmailCampaignResponse
    {
        $segment = $this->findOrCreateMarketingCampaignSegment($name);

        if (empty($segment)) {
            return null;
        }

        $response = $this->addUsersToMarketingCampaign($segment['id'], $users);

        if (empty($response)) {
            return null;
        }

        $campaign = $this->executeRequest(function () use ($segment, $name) {
            return $this->apiClient->campaigns->create([
                "type"       => "regular",
                "recipients" => [
                    "segment_opts" => [
                        "match"      => "all",
                        "conditions" => [
                            [
                                "condition_type" => "StaticSegment",
                                "field"          => "static_segment",
                                "op"             => "static_is",
                                "value"          => $segment['id'],
                            ]
                        ]
                    ],
                    "list_id"      => $this->listId,
                ],
                "settings"   => [
                    'title'     => $name,
                    'from_name' => $this->fromName,
                    'reply_to'  => $this->fromEmail,
                ]
            ]);
        },
            [
                'static_segment_id' => $segment['id'],
                'name'              => $name,
            ],
            LogLevel::ERROR,
            MarketingLogType::MAILCHIMP_CREATE_CAMPAIGN_REQUEST
        );

        if (empty($campaign)) {
            logger()->error("Failed to create marketing campaign");
            return null;
        }

        return new CreateEmailCampaignResponse(
            externalReference: $campaign['id'],
            externalCode     : $segment['id'],
            addedUsers       : $response
        );
    }

    /**
     * @param string $name
     * @return array|null
     */
    public function createStaticSegment(string $name): ?array
    {
        return $this->executeRequest(function () use ($name) {
            return $this->apiClient->lists->createSegment(
                $this->listId,
                ["name" => $name, 'static_segment' => []]
            );
        },
            ['segment_name' => $name],
            LogLevel::ERROR,
            MarketingLogType::MAILCHIMP_CREATE_SEGMENT_REQUEST
        );
    }

    /**
     * @param string $externalCode
     * @param Collection $users
     * @return Collection|null
     */
    public function addUsersToMarketingCampaign(string $externalCode, Collection $users): ?Collection
    {
        $response = $this->batchAddUsersToMailchimp($users);

        if (empty($response)) {
            logger()->error("Failed to add users to mailchimp");
            return null;
        }

        $errors = Arr::get($response, 'errors');
        if ($errors) {
            $errors = collect($errors);
            MarketingLogRepository::log(
                message  : "Error uploading some users to mailchimp",
                namespace: MarketingLogType::MAILCHIMP_ADD_USERS_RESPONSE,
                level    : LogLevel::ALERT,
                context  : $errors->toArray(),
            );
        }

        $emails = $users->map(fn(EmailCampaignUser $campaignUser) => $campaignUser->getEmail())->values()->toArray();

        $response = $this->executeRequest(
            function () use ($emails, $externalCode) {
                return $this->apiClient->lists->batchSegmentMembers(
                    ["members_to_add" => $emails],
                    $this->listId,
                    $externalCode
                );
            },
            ['emails' => $emails, 'external_code' => $externalCode],
            LogLevel::ERROR,
            MarketingLogType::ADD_USERS_TO_CAMPAIGN_REQUEST
        );

        if (empty($response)) {
            logger()->error("Failed to add users to marketing campaign");
            return null;
        }

        $errors = Arr::get($response, 'errors');
        if ($errors) {
            MarketingLogRepository::log(
                message  : "Some users were unable to be added to marketing campaign",
                namespace: MarketingLogType::MAILCHIMP_ADD_USERS_TO_CAMPAIGN,
                level    : LogLevel::ALERT,
                context  : $errors,
            );
        }

        $addedMembers = collect($response['members_added']);

        if ($addedMembers->isEmpty()) {
            logger()->error("No members added in this batch");
            return null;
        }

        return $users->map(function (EmailCampaignUser $user) use ($addedMembers) {
            //todo: would be cool to have polymorphic marketing logs and could map specific errors to the failed email campaign user

            $uploadedUser = $addedMembers->first(fn($member) => $member['email_address'] === $user->getEmail());

            return new AddUserToEmailCampaignResponse(
                consumerReference                 : $user->getConsumerReference(),
                marketingCampaignConsumerReference: $user->getMarketingCampaignReference(),
                status                            : $uploadedUser ? MarketingCampaignConsumerStatus::UPLOADED : MarketingCampaignConsumerStatus::ERROR,
                externalReference                 : $uploadedUser ? Arr::get($uploadedUser, 'id') : $user->getExternalReference(),
            );
        });
    }

    /**
     * @param $users
     * @return ?array
     */
    public function batchAddUsersToMailchimp($users): ?array
    {
        $formatted = $this->transformUsersToMailchimpFormat($users);

        return $this->executeRequest(
            function () use ($formatted) {
                return $this->apiClient->lists->batchListMembers(
                    $this->listId,
                    [
                        "members"         => $formatted,
                        'update_existing' => true,
                    ],
                    false,
                    true,
                );
            },
            $formatted,
            LogLevel::ERROR,
            MarketingLogType::ADD_USERS
        );
    }

    /**
     * @param $users
     * @return array
     */
    public function transformUsersToMailchimpFormat($users): array
    {
        return $users->map(function (EmailCampaignUser $user) {
            $consumerReference = $user->getConsumerReference();

            return [
                "email_address" => $user->getEmail(),
                "email_type"    => 'text',
                "status"        => "subscribed",
                "merge_fields"  => [
                    "FNAME"                 => $user->getFirstName(),
                    "LNAME"                 => $user->getLastName(),
                    $this->callbackUrlTagId => "$this->callbackUrl/$consumerReference?campaign=",
                    "ADDRESS"               => "",
                ]
            ];
        })->values()->toArray();
    }

    /**
     * @param callable $apiRequest
     * @param array $context
     * @param LogLevel $level
     * @param MarketingLogType $namespace
     * @return ?array
     */
    public function executeRequest(
        callable         $apiRequest,
        array            $context,
        LogLevel         $level,
        MarketingLogType $namespace
    ): ?array
    {
        try {
            return json_decode(json_encode($apiRequest()), true);
        } catch (ClientException $exception) {
            MarketingLogRepository::log(
                message   : $exception->getMessage(),
                namespace : $namespace,
                level     : $level,
                stackTrace: $exception->getTraceAsString(),
                context   : [
                    'response_body' => json_decode($exception->getResponse()->getBody()->getContents()),
                    'data'          => $context,
                ]
            );
        }

        return null;
    }

    /**
     * @return array|null
     */
    public function listSegments(): array|null
    {
        $response = $this->executeRequest(
            function () {
                return $this->apiClient->lists->listSegments(
                    $this->listId,
                );
            },
            [],
            LogLevel::ERROR,
            MarketingLogType::MAILCHIMP_LIST_SEGMENTS_REQUEST
        );

        return $response['segments'] ?? null;
    }

    /**
     * @param string $segmentName
     * @return ?array
     */
    public function findSegment(string $segmentName): ?array
    {
        $segments = $this->listSegments();

        return collect($segments)->first(function ($segment) use ($segmentName) {
            return $segment['name'] === $segmentName;
        });
    }

    public function findCampaignByExternalReference(string $externalReference): ?array
    {
        $campaign = $this->executeRequest(
            function () use ($externalReference) {
                return $this->apiClient->campaigns->get($externalReference);
            },
            ['external_reference' => $externalReference],
            LogLevel::INFO,
            MarketingLogType::MAILCHIMP_REQUEST_CAMPAIGN_INFORMATION
        );
        return $campaign ? [
            'external_reference' => Arr::get($campaign, 'id'),
            'name' => $campaign['settings']['title'],
        ] : null;
    }

    public function getEmailMarketingCampaignMetrics(MarketingCampaign $campaign): ?EmailCampaignMetrics
    {
        $externalReference = $campaign->{MarketingCampaign::FIELD_EXTERNAL_REFERENCE};

        if ($campaign->status === MarketingCampaignStatus::DRAFT) {
            return EmailCampaignMetrics::fromMarketingCampaign($campaign);
        }

        $response = $this->executeRequest(
            function () use ($externalReference) {
                return $this->apiClient->campaigns->get(
                    $externalReference,
                );
            },
            [],
            LogLevel::ERROR,
            MarketingLogType::MAILCHIMP_LIST_SEGMENTS_REQUEST
        );

        return $response ? new EmailCampaignMetrics(
            opens       : $response['report_summary']['opens'],
            uniqueOpens : $response['report_summary']['unique_opens'],
            openRate    : $response['report_summary']['open_rate'],
            clicks      : $response['report_summary']['clicks'],
            uniqueClicks: $response['report_summary']['subscriber_clicks'],
            clickRate   : $response['report_summary']['click_rate'],
        ) : null;
    }

    public function dispatchEmail(
        Collection $targetUsers,
        EmailTemplate $template,
        string $fromEmail,
        string $fromName
    ): bool
    {
        return true;
    }

    public function listValidSendingDomains(array $excludedDomains = []): Collection
    {
        //currently don't use this for mailchimp
        return collect();
    }

    public function sendTestEmail(OutgoingEmailDTO $target, EmailTemplate $template, string $fromName, string $fromEmail,)
    {
        // TODO: Implement sendTestEmail() method.
    }

    public function send(OutgoingEmailDTO $target, EmailTemplate $template, string $fromEmail, string $fromName,): bool
    {
        // TODO: Implement send() method.
    }
}
