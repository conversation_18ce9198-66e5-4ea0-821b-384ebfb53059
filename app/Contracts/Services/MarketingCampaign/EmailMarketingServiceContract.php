<?php

namespace App\Contracts\Services\MarketingCampaign;

use App\DTO\EmailService\DomainDTO;
use App\DTO\EmailService\OutgoingEmailDTO;
use App\DTO\MarketingCampaign\CampaignMetrics;
use App\DTO\MarketingCampaign\CreateEmailCampaignResponse;
use App\Models\EmailTemplate;
use App\Models\MarketingCampaign;
use Illuminate\Support\Collection;

interface EmailMarketingServiceContract
{
    public function createMarketingCampaign(string $name, Collection $users): ?CreateEmailCampaignResponse;

    public function addUsersToMarketingCampaign(string $externalCode, Collection $users): ?Collection;
    public function findCampaignByExternalReference(string $externalReference): ?array;

    public function getEmailMarketingCampaignMetrics(MarketingCampaign $campaign): ?CampaignMetrics;

    public function dispatchEmail(
        Collection $targetUsers,
        EmailTemplate $template,
        string $fromEmail,
        string $fromName,
    ): bool;

    /**
     * @return Collection<DomainDTO>
     */
    public function listValidSendingDomains(array $excludedDomains = []): Collection;

    public function sendTestEmail(
        OutgoingEmailDTO $target,
        EmailTemplate $template,
        string $fromName,
        string $fromEmail,
    );

    public function send(
        OutgoingEmailDTO $target,
        EmailTemplate $template,
        string $fromEmail,
        string $fromName,
    ): bool;

}
