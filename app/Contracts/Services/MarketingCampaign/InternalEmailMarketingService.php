<?php

namespace App\Contracts\Services\MarketingCampaign;

use App\DTO\EmailService\DomainDTO;
use App\DTO\EmailService\OutgoingEmailDTO;
use App\DTO\MarketingCampaign\AddUserToEmailCampaignResponse;
use App\DTO\MarketingCampaign\CampaignMetrics;
use App\DTO\MarketingCampaign\CreateEmailCampaignResponse;
use App\DTO\MarketingCampaign\EmailCampaignMetrics;
use App\DTO\MarketingCampaign\EmailCampaignUser;
use App\DTO\MarketingCampaign\InternalEmailCampaignMetrics;
use App\Enums\Emails\DomainStatus;
use App\Enums\Emails\EmailProvider;
use App\Enums\MarketingCampaigns\MarketingCampaignConsumerStatus;
use App\Exceptions\MarketingCampaign\EmailBatchSendFailedException;
use App\Models\EmailTemplate;
use App\Models\MarketingCampaign;
use App\Models\MarketingCampaignConsumer;
use App\Services\Emails\EmailService;
use App\Services\Emails\EmailServiceProviderContract;
use App\Services\Emails\EmailServiceProviderFactory;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class InternalEmailMarketingService implements EmailMarketingServiceContract
{
    const EmailProvider INTERNAL_MARKETING_EMAIL_SERVICE_PROVIDER = EmailProvider::SOCKET_LABS;
    protected EmailServiceProviderContract $emailServiceProvider;

    public function __construct(
        protected EmailService $emailService,
    )
    {
        $this->emailServiceProvider = EmailServiceProviderFactory::make(EmailProvider::SOCKET_LABS);
    }

    /**
     * @param string $name
     * @param Collection $users
     * @return CreateEmailCampaignResponse|null
     */
    public function createMarketingCampaign(string $name, Collection $users): ?CreateEmailCampaignResponse
    {
        $externalCode = Str::uuid()->toString();

        $response = $this->addUsersToMarketingCampaign($externalCode, $users);

        return new CreateEmailCampaignResponse(
            externalReference: Str::uuid()->toString(),
            externalCode     : $externalCode,
            addedUsers       : $response
        );
    }

    /**
     * @param string $externalCode
     * @param Collection $users
     * @return Collection|null
     */
    public function addUsersToMarketingCampaign(string $externalCode, Collection $users): ?Collection
    {
        return $users->map(function (EmailCampaignUser $user) {
            $status = $user->getMarketingConsumerStatus() !== MarketingCampaignConsumerStatus::ERROR
                ? MarketingCampaignConsumerStatus::UPLOADED
                : MarketingCampaignConsumerStatus::ERROR;

            return new AddUserToEmailCampaignResponse(
                consumerReference                 : $user->getConsumerReference(),
                marketingCampaignConsumerReference: $user->getMarketingCampaignReference(),
                status                            : $status,
                externalReference                 : Str::uuid(),
            );
        });
    }

    /**
     * @param string $externalReference
     * @return array|null
     */
    public function findCampaignByExternalReference(string $externalReference): ?array
    {
        return null;
    }

    /**
     * @param MarketingCampaign $campaign
     * @return EmailCampaignMetrics|null
     */
    public function getEmailMarketingCampaignMetrics(MarketingCampaign $campaign): ?CampaignMetrics
    {
        $metrics = MarketingCampaignConsumer::query()
            ->select(
                DB::raw('COUNT(*) as targeted'),
                DB::raw('COUNT(CASE WHEN ' . MarketingCampaignConsumer::TABLE . '.'. MarketingCampaignConsumer::FIELD_SENT_AT . ' IS NOT NULL THEN 1 END) as sent'),
                DB::raw('COUNT(CASE WHEN ' . MarketingCampaignConsumer::TABLE . '.'. MarketingCampaignConsumer::FIELD_DELIVERED_AT . ' IS NOT NULL THEN 1 END) as delivered'),
                DB::raw('COUNT(CASE WHEN ' . MarketingCampaignConsumer::TABLE . '.'. MarketingCampaignConsumer::FIELD_STATUS . ' = "' . MarketingCampaignConsumerStatus::ERROR->value . '" THEN 1 END) as failed'),
                DB::raw('COUNT(CASE WHEN ' . MarketingCampaignConsumer::TABLE . '.'. MarketingCampaignConsumer::FIELD_OPENED_AT . ' IS NOT NULL THEN 1 END) as opened'),
                DB::raw('COUNT(CASE WHEN ' . MarketingCampaignConsumer::TABLE . '.'. MarketingCampaignConsumer::FIELD_CLICKED_AT . ' IS NOT NULL THEN 1 END) as clicked'),
                DB::raw('COUNT(CASE WHEN ' . MarketingCampaignConsumer::TABLE . '.'. MarketingCampaignConsumer::FIELD_CLONED_CONSUMER_PRODUCT_ID . ' IS NOT NULL THEN 1 END) as leads_generated')
            )
            ->where(MarketingCampaignConsumer::FIELD_MARKETING_CAMPAIGN_ID, '=', $campaign->id)
            ->first();

        return new InternalEmailCampaignMetrics(
            sentCount: $metrics->sent,
            deliveredCount: $metrics->delivered,
            failedCount: $metrics->failed,
            openedCount: $metrics->opened,
            clickedCount: $metrics->clicked,
            revalidates: $metrics->leads_generated,
            targets: $metrics->targeted,
        );
    }

    /**
     * @param Collection<OutgoingEmailDTO> $targetUsers
     * @param EmailTemplate $template
     * @param string $fromEmail
     * @param string $fromName
     * @return bool
     * @throws EmailBatchSendFailedException
     */
    public function dispatchEmail(
        Collection $targetUsers,
        EmailTemplate $template,
        string $fromEmail,
        string $fromName,

    ): bool
    {
        return $this->emailServiceProvider->rawBatchSend(
            outgoingEmails: $targetUsers,
            subject       : $template->subject,
            body          : $template->content,
            fromEmail     : $fromEmail,
            fromName      : $fromName,
            header        : $template->background?->header ?? '',
            footer        : $template->background?->footer ?? '',
            templateId    : $template->id,
            backgroundId  : $template->background_id,
        );
    }

    /**
     * @param array $excludedDomains
     * @return Collection<DomainDTO>
     */
    public function listValidSendingDomains(array $excludedDomains = []): Collection
    {
        return $this->emailServiceProvider->listSendingDomains(excludedDomains: $excludedDomains);
    }

    /**
     * @param OutgoingEmailDTO $target
     * @param EmailTemplate $template
     * @param string $fromName
     * @param string $fromEmail
     * @return bool
     */
    public function sendTestEmail(
        OutgoingEmailDTO $target,
        EmailTemplate $template,
        string $fromName,
        string $fromEmail,
    ): bool
    {
        return $this->send(
            $target,
            $template,
            $fromEmail,
            $fromName
        );
    }

    /**
     * @param OutgoingEmailDTO $target
     * @param EmailTemplate $template
     * @param string $fromEmail
     * @param string $fromName
     * @return bool
     */
    public function send(
        OutgoingEmailDTO $target,
        EmailTemplate $template,
        string $fromEmail,
        string $fromName,
    ): bool
    {
        return $this->emailServiceProvider->rawSend(
            outgoingEmail : $target,
            subject       : $template->subject,
            body          : $template->content,
            fromEmail     : $fromEmail,
            fromName      : $fromName,
            header        : $template->background->header,
            footer        : $template->background->footer,
            templateId    : $template->id,
            backgroundId  : $template->background_id,
        );
    }
}
