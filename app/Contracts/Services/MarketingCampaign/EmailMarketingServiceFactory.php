<?php

namespace App\Contracts\Services\MarketingCampaign;

use App\Enums\MarketingCampaigns\MarketingCampaignType;
use Illuminate\Contracts\Container\BindingResolutionException;
use InvalidArgumentException;

class EmailMarketingServiceFactory
{
    /**
     * @param MarketingCampaignType $type
     * @return EmailMarketingServiceContract
     * @throws BindingResolutionException
     */
    public static function make(MarketingCampaignType $type): EmailMarketingServiceContract
    {
        //todo: SMS and Drip SMS don't belong in here but serve as a band-aid solution
        return match ($type) {
            MarketingCampaignType::MAILCHIMP_EMAIL => app()->make(MailchimpEmailMarketingService::class),
            MarketingCampaignType::INTERNAL_EMAIL, MarketingCampaignType::DRIP_EMAIL, MarketingCampaignType::DRIP_SMS, MarketingCampaignType::SMS => app()->make(InternalEmailMarketingService::class),
            default => throw new InvalidArgumentException("Unknown Marketing Campaign Type Parsed"),
        };
    }

}
