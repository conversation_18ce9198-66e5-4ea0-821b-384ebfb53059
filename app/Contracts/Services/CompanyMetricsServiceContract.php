<?php

namespace App\Contracts\Services;

use App\DTO\PPCResponse;
use App\Enums\CompanyMetrics\CompanyMetricSources;
use App\Models\Odin\Company;
use DateTime;
use Exception;
use GuzzleHttp\Exception\GuzzleException;

interface CompanyMetricsServiceContract
{
    /**
     * @return CompanyMetricSources
     */
    public function getServiceType(): CompanyMetricSources;

    /**
     * Retrieves monthly ad spending company metrics from API as a json response
     *
     * @param Company $company
     * @param DateTime|null $startDate
     * @param DateTime|null $endDate
     * @return PPCResponse|array|null
     * @throws Exception
     * @throws GuzzleException
     */
    public function getCompanyMetrics(Company $company, ?DateTime $startDate = null, ?DateTime $endDate = null): PPCResponse|null|array;

}
