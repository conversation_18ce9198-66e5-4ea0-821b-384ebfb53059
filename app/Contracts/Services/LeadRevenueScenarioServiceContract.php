<?php

namespace App\Contracts\Services;

use App\Models\LeadRevenueScenario;

interface LeadRevenueScenarioServiceContract
{
    /**
     * Calculates and stores the revenue scenario for a given lead.
     *
     * @param string $leadReference
     * @param string $type
     * @return bool|null
     */
    public function calculateAndStoreRevenueScenarioForLead(string $leadReference, string $type = LeadRevenueScenario::TYPE_UNVERIFIED): ?bool;
}
