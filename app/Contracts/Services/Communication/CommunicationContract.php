<?php

namespace App\Contracts\Services\Communication;

use Throwable;

interface CommunicationContract
{
    const string FROM_TYPE_PHONE = 'phone';
    const string FROM_TYPE_SERVICE = 'service';

    /**
     * Handles sending an SMS to a user & returns the reference of the SMS being sent.
     *
     * @param string $from
     * @param string $to
     * @param string $body
     * @param string|null $fromType
     * @param array|null $meta
     * @return string
     * @throws Throwable
     */
    public function sendSMS(
        string $from,
        string $to,
        string $body,
        ?string $fromType = self::FROM_TYPE_PHONE,
        ?array $meta = [],
    ): string;

    /**
     * Return a list of available numbers.
     *
     * @return array
     */
    public function getAvailableNumbers(): array;

    public function retrieveWebPhoneToken(array $numbers = []): string;

    /**
     * @return string
     */
    public function getServiceName(): string;
}
