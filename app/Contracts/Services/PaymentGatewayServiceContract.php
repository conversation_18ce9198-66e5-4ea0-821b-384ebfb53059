<?php

namespace App\Contracts\Services;

use App\DTO\Billing\PaymentMethods\PaymentMethodDTO;
use App\Enums\Billing\PaymentMethodServices;
use App\Models\Odin\Company;
use Illuminate\Support\Collection;

interface PaymentGatewayServiceContract
{
    /**
     * @return PaymentMethodServices
     */
    public function getPaymentServiceProviderCode(): PaymentMethodServices;

    /**
     * @param string $invoicePaymentChargeUuid
     * @param string $invoiceUuid
     * @param int $total
     * @param array $metadata
     * @param string|null $description
     * @param string|null $customerCode
     * @param string|null $paymentMethod
     * @return bool
     */
    public function makeChargeRequest(
        string $invoicePaymentChargeUuid,
        string $invoiceUuid,
        int $total,
        array $metadata,
        ?string $description,
        ?string $customerCode,
        ?string $paymentMethod
    ): bool;

    /**
     * @param string $invoiceRefundChargeUuid
     * @param int $total
     * @param string $chargeId
     * @param string $invoiceUuid
     * @return bool
     */
    public function makeRefundRequest(
        string $invoiceRefundChargeUuid,
        int $total,
        string $chargeId,
        string $invoiceUuid
    ): bool;

    /**
     * @param Company $company
     * @param string $email
     * @return string
     */
    public function createCustomer(Company $company, string $email): string;

    /**
     * @param string $token
     * @return string
     */
    public function createNewPaymentMethod(string $token): string;

    /**
     * @param string $customerCode
     * @param string $paymentMethod
     * @return void
     */
    public function addPaymentMethod(string $customerCode, string $paymentMethod): void;

    /**
     * @param string $customerCode
     * @return Collection<PaymentMethodDTO>
     */
    public function getPaymentMethods(string $customerCode): Collection;

    /**
     * @param string $customerCode
     * @param string $paymentMethod
     * @return void
     */
    public function setPaymentMethodPrimary(string $customerCode, string $paymentMethod): void;

    /**
     * @param string $paymentMethod
     * @return void
     */
    public function deletePaymentMethod(string $paymentMethod): void;
}
