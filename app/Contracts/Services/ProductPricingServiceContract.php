<?php
namespace App\Contracts\Services;

use App\DataModels\Odin\Prices\BidPriceTable;
use App\DataModels\Odin\Prices\FloorPriceTable;
use App\Enums\Odin\Product;
use App\Enums\Odin\PropertyType;
use App\Enums\Odin\QualityTier;
use App\Enums\Odin\SaleTypes;
use App\Enums\Odin\StateAbbreviation;
use Illuminate\Support\Collection;

interface ProductPricingServiceContract
{
    /**
     * @param int $serviceProductId
     * @param Product $product
     * @param string $industry
     * @param PropertyType $propertyType
     * @param StateAbbreviation|null $stateAbbreviation
     * @param array|null $countyKeys
     * @param QualityTier|null $qualityTier
     * @param SaleTypes|SaleTypes[]|null $saleType
     *
     * @return FloorPriceTable
     */
    public function getFloorTable(
        int $serviceProductId,
        Product $product,
        string $industry,
        PropertyType $propertyType,
        ?StateAbbreviation $stateAbbreviation = null,
        ?array $countyKeys = null,
        ?QualityTier $qualityTier = null,
        SaleTypes|array|null $saleType = null
    ): FloorPriceTable;

    /**
     * @param int $serviceProductId
     * @param Product $product
     * @param string $industry
     * @param PropertyType $propertyType
     * @param Collection $productCampaignIds
     * @param StateAbbreviation|null $stateAbbreviation
     * @param string|null $countyKey
     * @param QualityTier|null $qualityTier
     *
     * @return BidPriceTable
     */
    public function getBidTable(
        int $serviceProductId,
        Product $product,
        string $industry,
        PropertyType $propertyType,
        Collection $productCampaignIds,
        ?StateAbbreviation $stateAbbreviation = null,
        ?string $countyKey = null,
        ?QualityTier $qualityTier = null
    ): BidPriceTable;
}
