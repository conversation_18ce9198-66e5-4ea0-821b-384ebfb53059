<?php

namespace App\Contracts\Services;

use App\Enums\Odin\Industry;
use Exception;

interface LeadPriceServiceContract
{
    /**
     * Handles fetching price range for default campaign against the requested company and industry.
     *
     * @param string   $companyReference
     * @param Industry $industry
     * @return array
     * @throws Exception
     */
    public function getPriceRangeSalesTypeForDefaultCampaign(string $companyReference, Industry $industry): array;
}
