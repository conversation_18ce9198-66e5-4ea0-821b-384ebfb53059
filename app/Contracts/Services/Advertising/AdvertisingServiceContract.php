<?php

namespace App\Contracts\Services\Advertising;

use App\DataModels\Advertising\AccountCampaigns;
use App\DataModels\Advertising\AdCampaignsPaginatorDataModel;
use App\DataModels\Advertising\CampaignLocations;
use App\Enums\Advertising\Advertiser;
use Illuminate\Support\Collection;

interface AdvertisingServiceContract
{
    /**
     * @return Collection
     */
    public function getCampaignStatusOptions(): Collection;

    /**
     * @param $accountId
     * @param int $page
     * @param int $perPage
     * @param int|null $searchStateLocationId
     * @param string|null $costMetric
     * @param string|null $status
     * @param string|null $name
     * @param array|string[]|null $pageTokens
     * @return AdCampaignsPaginatorDataModel
     */
    public function getCampaignsPaginated(
        $accountId,
        int $page,
        int $perPage = 10,
        ?int $searchStateLocationId = null,
        ?string $costMetric = null,
        ?string $status = null,
        ?string $name = null,
        ?array &$pageTokens = null
    ): AdCampaignsPaginatorDataModel;

    /**
     * Returns the platform campaigns along with their associated location target ID's
     * Should return the platform's location ID's associated to the corresponding locations table's ID's
     *
     * @param int $logId
     * @param array $retryCampaigns
     * @return Collection|null
     */
    public function getAutomatedCampaignsWithLocations(int $logId, array $retryCampaigns = []): ?AccountCampaigns;

    /**
     * Populates a CampaignLocations data model with the locations to include/exclude per campaign
     *
     * @param $accountId
     * @param CampaignLocations $campaignLocations
     * @param Collection $campaigns
     * @param array $excludeLocations
     * @param int $logId
     * @return CampaignLocations
     */
    public function populateCampaignLocationsToUpdate($accountId, CampaignLocations &$campaignLocations, Collection $campaigns, array $excludeLocations, int $logId): CampaignLocations;

    /**
     * Makes the actual API call to the ads platform to update the locations to exclude
     * Should receive a list of platform locations ID's to exclude
     * $campaignLocations is obtained from AdvertisingCampaignService::determineCampaignLocationsToUpdate()
     *
     * @param int $logId
     * @param CampaignLocations $campaignLocations
     * @param bool $isRetry
     * @return bool
     */
    public function updateCampaignLocations(int $logId, CampaignLocations $campaignLocations, bool $isRetry = false): bool;

    /**
     * Makes the API calls to update the locations included in the tiered advertising campaigns structure
     *
     * @param int $industryId
     * @param int|null $instanceId
     * @return bool
     */
    public function updateTieredAdsCampaignLocations(int $industryId, ?int $instanceId = null): bool;

    /**
     * Verifies communication with the given account id
     * @param Advertiser $advertiser
     * @param string $accountId
     * @return ?string
     */
    public function getAccountName(Advertiser $advertiser, string $accountId): ?string;

    /**
     * Verifies communication with the given campaign id using the account id
     * @param Advertiser $advertiser
     * @param string $accountId
     * @param string $campaignId
     * @return ?string
     */
    public function getCampaignName(Advertiser $advertiser, string $accountId, string $campaignId): ?string;

    /**
     * Used in tiered advertising, sets the tcpa bid for the given campaign
     * @param Advertiser $advertiser
     * @param string $accountId
     * @param string $campaignId
     * @param float $tcpaBid
     * @return bool
     */
    public function setTcpaBid(Advertiser $advertiser, string $accountId, string $campaignId, float $tcpaBid): bool;
}
