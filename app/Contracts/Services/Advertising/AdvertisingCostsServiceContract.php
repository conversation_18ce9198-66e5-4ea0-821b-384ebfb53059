<?php

namespace App\Contracts\Services\Advertising;

use Carbon\CarbonPeriod;
use Illuminate\Support\Collection;

interface AdvertisingCostsServiceContract
{
    /**
     * @param CarbonPeriod $period
     * @param array $resolutions
     * @param array $industries
     * @param array $advertisers
     * @return Collection
     */
    public function retrieveDailyAdCostData(CarbonPeriod $period, array $resolutions, array $industries, array $advertisers): Collection;
}
