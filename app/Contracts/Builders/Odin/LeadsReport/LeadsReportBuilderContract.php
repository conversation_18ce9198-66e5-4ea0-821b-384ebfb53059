<?php

namespace App\Contracts\Builders\Odin\LeadsReport;

use App\Enums\LeadsReport\LeadsReportGroupEnum;
use Carbon\Carbon;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;

interface LeadsReportBuilderContract
{
    /**
     * Creates an instance of industry builder.
     *
     * @return static
     */
    public static function query(): self;

    /**
     * @param Carbon|null $startDate
     * @return $this
     */
    public function forStartDate(?Carbon $startDate = null): self;

    /**
     * @param Carbon|null $endDate
     * @return $this
     */
    public function forEndDate(?Carbon $endDate = null): self;

    /**
     * @param LeadsReportGroupEnum|null $group
     * @return $this
     */
    public function forGroup(?LeadsReportGroupEnum $group = null): self;

    /**
     * @param array|null $columns
     * @return $this
     */
    public function forColumns(?array $columns = null): self;

    /**
     * @param Collection|null $limitedLocations
     * @return $this
     */
    public function forLimitedLocations(?Collection $limitedLocations = null): self;

    /**
     * @param mixed|null $filters
     * @return $this
     */
    public function withFilters(mixed $filters = null): self;

    /**
     * @param int|null $companyId
     * @return self
     */
    public function forCompany(?int $companyId): self;

    /**
     * @param array|null $campaigns
     * @return self
     */
    public function forCampaigns(?array $campaigns): self;

    /**
     * @param string $db
     * @return $this
     */
    public function fromDatabase(string $db = ''): self;

    /**
     * @param string $dbRead
     * @return $this
     */
    public function fromReadonlyDatabase(string $dbRead = ''): self;

    /**
     * @param Builder $query
     * @return Builder
     */
    public function locationLimit(Builder $query): Builder;

    /**
     * Returns a query for this instance.
     *
     * @return Builder
     */
    public function getQuery(): Builder;

    /**
     * @return Collection
     */
    public function get(): Collection;
}
