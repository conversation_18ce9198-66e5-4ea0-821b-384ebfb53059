<?php

namespace App\Contracts\Builders\Odin\Company;

use App\Enums\Cadence;
use App\Enums\Logical;
use App\Enums\Operator;
use Closure;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Validation\ValidationException;

interface CompanySearchBuilderContract
{
    /**
     * @param  array  $statusIds
     * @return self
     * @throws ValidationException
     */
    public function setStatusIds(array $statusIds): self;

    /**
     * @param  array  $salesStatusIds
     * @return self
     * @throws ValidationException
     */
    public function setSalesStatusIds(array $salesStatusIds): self;

    /**
     * @param  array  $stateAbbreviations
     * @return self
     * @throws ValidationException
     */
    public function setStateAbbreviations(array $stateAbbreviations): self;

    /**
     * @param  array  $accountManagerIds
     * @return self
     * @throws ValidationException
     */
    public function setAccountsManagerIds(array $accountManagerIds): self;

    /**
     * @param  array  $industryIds
     * @return self
     * @throws ValidationException
     */
    public function setIndustryIds(array $industryIds): self;

    /**
     * @param  Logical  $industryLogical
     * @return self
     */
    public function setIndustryLogical(Logical $industryLogical): self;

    /**
     * @param  array  $officeLocationIds
     * @return self
     * @throws ValidationException
     */
    public function setOfficeLocationIds(array $officeLocationIds): self;

    /**
     * @param  Logical  $officeLocationLogical
     * @return self
     */
    public function setOfficeLocationLogical(Logical $officeLocationLogical
    ): self;

    /**
     * @param  string|null  $companyName
     * @return self
     */
    public function setCompanyName(?string $companyName): self;

    /**
     * @param  array  $successManagerIds
     * @return self
     * @throws ValidationException
     */
    public function setSuccessManagerIds(array $successManagerIds): self;

    /**
     * @param  array|null  $amountOfLeadsPurchasedObject
     * @return self
     */
    public function setAmountOfLeadsPurchasedObject(?array $amountOfLeadsPurchasedObject
    ): self;

    /**
     * @return self
     */
    public function appendUsers(): self;

    public function with(array $relations): self;

    /**
     * @param  array|null  $orderBy
     * @return self
     * @throws ValidationException
     */
    public function setOrderBy(?array $orderBy): self;

    /**
     * @param  bool|null  $neverExceedsBudget
     * @return self
     */
    public function setNeverExceedsBudget(?bool $neverExceedsBudget): self;

    /**
     * @param  string|null  $cadence
     *
     * @return self
     */
    public function setCadence(?Cadence $cadence): self;

    /**
     * @param  array  $campaignStatusIds
     * @return self
     */
    public function setCampaignStatusIds(array $campaignStatusIds): self;

    /**
     * @param  bool  $noLimitSelected
     * @param  int|null  $volumeInput
     * @param  Operator|null  $volumeOperator
     * @param  int|null  $costInput
     * @param  Operator|null  $costOperator
     * @return self
     */
    public function setCampaignBudget(
        ?bool $noLimitSelected,
        ?int $volumeInput,
        ?Operator $volumeOperator,
        ?int $costInput,
        ?Operator $costOperator
    ): self;

    /**
     * @param  array  $serviceAreas
     * @param  Logical  $logical
     * @return self
     */
    public function setCampaignServiceAreas(
        array $serviceAreas,
        Logical $logical
    ): self;

    /**
     * @param  int|null  $firstInput
     * @param  int|null  $secondInput
     * @param  Operator|null  $firstOperator
     * @param  Operator|null  $secondOperator
     * @param  Logical|null  $logical
     * @return self
     */
    public function setLeadRejection(
        ?int $firstInput,
        ?int $secondInput,
        ?Operator $firstOperator,
        ?Operator $secondOperator,
        ?Logical $logical
    ): self;

    /**
     * @param  int|null  $firstInput
     * @param  int|null  $secondInput
     * @param  Operator|null  $firstOperator
     * @param  Operator|null  $secondOperator
     * @param  Logical|null  $logical
     * @return self
     */
    public function setAppointmentRejection(
        ?int $firstInput,
        ?int $secondInput,
        ?Operator $firstOperator,
        ?Operator $secondOperator,
        ?Logical $logical
    ): self;

    /**
     * @param  int|null  $firstInput
     * @param  Operator|null  $firstOperator
     * @param  Logical|null  $logical
     * @param  int|null  $secondInput
     * @param  Operator|null  $secondOperator
     * @return self
     */
    public function setEmployeeCount(
        ?int $firstInput,
        ?Operator $firstOperator,
        ?Logical $logical,
        ?int $secondInput,
        ?Operator $secondOperator
    ): self;

    /**
     * @param  int|null  $firstInput
     * @param  Operator|null  $firstOperator
     * @param  Logical|null  $logical
     * @param  int|null  $secondInput
     * @param  Operator|null  $secondOperator
     * @return self
     */
    public function setEstimatedRevenue(
        ?int $firstInput,
        ?Operator $firstOperator,
        ?Logical $logical,
        ?int $secondInput,
        ?Operator $secondOperator
    ): self;

    /**
     * @param  int|null  $firstInput
     * @param  Operator|null  $firstOperator
     * @param  Logical|null  $logical
     * @param  int|null  $secondInput
     * @param  Operator|null  $secondOperator
     * @return self
     */
    public function setGoogleRating(
        ?int $firstInput,
        ?Operator $firstOperator,
        ?Logical $logical,
        ?int $secondInput,
        ?Operator $secondOperator
    ): self;

    /**
     * @param  int|null  $firstInput
     * @param  Operator|null  $firstOperator
     * @param  Logical|null  $logical
     * @param  int|null  $secondInput
     * @param  Operator|null  $secondOperator
     * @return self
     */
    public function setGoogleReview(
        ?int $firstInput,
        ?Operator $firstOperator,
        ?Logical $logical,
        ?int $secondInput,
        ?Operator $secondOperator
    ): self;

    /**
     * @param  int|null  $firstInput
     * @param  Operator|null  $firstOperator
     * @param  Logical|null  $logical
     * @param  int|null  $secondInput
     * @param  Operator|null  $secondOperator
     * @return self
     */
    public function setConsumerRating(
        ?int $firstInput,
        ?Operator $firstOperator,
        ?Logical $logical,
        ?int $secondInput,
        ?Operator $secondOperator
    ): self;

    /**
     * @param  int|null  $firstInput
     * @param  Operator|null  $firstOperator
     * @param  Logical|null  $logical
     * @param  int|null  $secondInput
     * @param  Operator|null  $secondOperator
     * @return self
     */
    public function setConsumerReviewCount(
        ?int $firstInput,
        ?Operator $firstOperator,
        ?Logical $logical,
        ?int $secondInput,
        ?Operator $secondOperator
    ): self;

    /**
     * @return self
     */
    public function filter(): self;

    /**
     * @return Collection
     */
    public function get(): Collection;

    /**
     * @param  Closure|int|null  $perPage
     * @param  array|string  $columns
     * @param  string  $pageName
     * @param  int|null  $page
     * @return LengthAwarePaginator
     */
    public function paginate(
        Closure|int|null $perPage = null,
        array|string $columns = ['*'],
        string $pageName = 'page',
        int|null $page = null
    ): LengthAwarePaginator;
}
