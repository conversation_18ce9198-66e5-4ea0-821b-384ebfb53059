<?php

namespace App\Contracts\Builders;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface BuilderContract
{
    /**
     * Runs the query builder, and returns a list of tasks.
     *
     * @return Collection
     */
    public function get(): Collection;

    /**
     * Returns the query for this instance.
     *
     * @return Builder
     */
    public function getQuery(): Builder;

    /**
     * Creates an instance of activity builder.
     *
     * @return self
     */
    public static function query(): self;

    /**
     * Runs the query builder, and return the count of the query
     *
     * @return int
     */
    public function count(): int;

    /**
     * Returns a paginated list of this builder.
     *
     * @param int $page
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function paginate(int $page, int $perPage): LengthAwarePaginator;
}
