<?php

namespace App\Contracts;

interface OneTimePasswordServiceInterface
{
    /**
     * Sends the verification code to the recipient
     *
     * @param string $key The key (reference) to store this code against in the database
     * @return bool The success of the delivery
     */
    public function sendCode(string $key): bool;

    /**
     * Verifies the code provided by the user, against the one sent to the user
     *
     * @param string $key
     * @param string $code
     * @return bool
     */
    public function verifyCode(string $key, string $code): bool;
}
