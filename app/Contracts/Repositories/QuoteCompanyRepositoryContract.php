<?php

namespace App\Contracts\Repositories;

use App\DataModels\TotaledChargeableAndDeliveredLeadsDataModel;
use App\DataModels\TotaledRejectedLeadsDataModel;
use App\Models\Legacy\EloquentQuoteCompany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

interface QuoteCompanyRepositoryContract
{
    /**
     * @param int $companyId
     * @return EloquentQuoteCompany|null|Builder
     */
    public function getMostRecentQuoteCompanyByCompanyId(int $companyId): Builder|EloquentQuoteCompany|null;

    /**
     * @param array $companyIds
     * @return array
     */
    public function getMostRecentQuoteCompaniesByCompanyIds(array $companyIds): array;

    /**
     * @param int $companyId
     * @param int $timestampBudgetStart
     * @return TotaledChargeableAndDeliveredLeadsDataModel|null
     */
    public function getTotaledChargeableAndDeliveredLeadsByCompany(
        int $companyId = 0,
        int $timestampBudgetStart = 0
    ): ?TotaledChargeableAndDeliveredLeadsDataModel;

    /**
     * @param int $companyId
     * @param int $timestampBudgetStart
     * @return TotaledRejectedLeadsDataModel|null
     */
    public function getTotaledRejectedLeadsByCompany(
        int $companyId = 0,
        int $timestampBudgetStart = 0
    ): ?TotaledRejectedLeadsDataModel;

    /**
     * @return array
     */
    public function getCompanyIdsThatPurchasedLeadsInLast30Days(): array;

    /**
     * @param int $companyId
     * @return ?Builder|null
     */
    public function getLeadsByCompanyId(int $companyId): ?Builder;

    /**
     * @param array $companyIdsInitDeliveryTimes
     * @return array
     */
    public function getBudgetSpentByCompanies(array $companyIdsInitDeliveryTimes = []): array;
}
