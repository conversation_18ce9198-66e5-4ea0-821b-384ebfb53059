<?php

namespace App\Contracts\Repositories;

interface MediaRepositoryContract
{
    /**
     * <PERSON>les preparing a URL for the legacy media/attachment file against the given combination of company (ID) and file name.
     *
     * @param int    $legacyCompany
     * @param string $fileName
     * @return string|null
     */
    public function getLegacyMediaURLForCompany(int $legacyCompany, string $fileName): ?string;
}
