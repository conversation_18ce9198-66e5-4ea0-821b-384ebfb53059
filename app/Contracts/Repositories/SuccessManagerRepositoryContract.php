<?php

namespace App\Contracts\Repositories;

use App\Models\SuccessManager;
use App\Models\SuccessManagerClient;
use Illuminate\Support\Collection;

interface SuccessManagerRepositoryContract
{
    /**
     * Returns a list of all success managers.
     *
     * @return Collection<SuccessManager>
     */
    public function getSuccessManagers(): Collection;

    /**
     * Returns a success manager against the requested ID.
     *
     * @param int $id
     * @return Collection
     */
    public function getSuccessManagerByIdOrFail(int $id): Collection;

    /**
     * Returns a list of types for success managers.
     *
     * @return array
     */
    public function getSuccessManagerTypes(): array;

    /**
     * <PERSON>les creating a new or updating an existing success manager based on the requested params.
     *
     * @param int $user
     * @param int $type
     * @param bool $includeInSalesRoundRobin
     * @param int|null $id
     * @return bool
     */
    public function updateOrCreateSuccessManager(
        int  $user,
        int  $type,
        bool $includeInSalesRoundRobin,
        ?int $id = null
    ): bool;

    /**
     * <PERSON>les deleting a success manager for the requested ID.
     *
     * @param SuccessManager $successManager
     * @return bool
     */
    public function deleteSuccessManagers(SuccessManager $successManager): bool;

    /**
     * Handles assigning a success manager to a company.
     *
     * @param int $companyId
     * @param int|null $successManagerId
     * @return SuccessManagerClient|null
     */
    public function assignSuccessManager(int $companyId, ?int $successManagerId): ?SuccessManagerClient;
}
