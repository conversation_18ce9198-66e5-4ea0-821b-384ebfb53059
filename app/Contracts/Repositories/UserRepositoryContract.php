<?php

namespace App\Contracts\Repositories;

use App\Models\User;
use Illuminate\Support\Collection;

interface UserRepositoryContract
{
    /**
     * Handles searching and returning users by their name and ID.
     *
     * @param string $searchQuery
     * @param array  $fields
     * @return Collection<User>|null
     */
    public function searchUsersByNameOrId(string $searchQuery, array $fields = ["*"]): ?Collection;

    /**
     * handles creating a user against the requested set of attributes.
     *
     * @param array $data
     * @return User
     */
    public function createUserFromAttributes(array $data = []): User;
}
