<?php

namespace App\Contracts\Repositories;

use App\Models\Teams\Team;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Collection;

interface TeamRepositoryContract
{
    /**
     * @param int $teamId
     * @return Team
     * @throws ModelNotFoundException
     */
    public function findOrFail(int $teamId): Team;

    /**
     * @param int $userId
     * @param int|null $filterByTeamTypeId
     * @return Collection<Team>
     */
    public function findTeamsByLeader(int $userId, ?int $filterByTeamTypeId = null): Collection;

    /**
     * @param int $teamTypeId
     * @return Collection<Team>
     */
    public function findTeamsByType(int $teamTypeId): Collection;

    /**
     * @param int $userId
     * @return Collection<Team>
     */
    public function findTeamsByMember(int $userId): Collection;

    /**
     * @param int $teamTypeId
     * @param string $name
     * @param string $description
     * @return Team
     * @throws ModelNotFoundException
     */
    public function createEmptyTeam(int $teamTypeId, string $name, string $description): Team;

    /**
     * @param int $teamTypeId
     * @param string $name
     * @param string $description
     * @param int $userId
     * @param string $leaderTitle
     * @param null | int $reportsTo
     * @return Team|null
     */
    public function createTeamFromLeader(int $teamTypeId, string $name, string $description, int $userId, string $leaderTitle, ?int $reportsTo = null): ?Team;

    /**
     * @param Team $team
     * @param int $userId
     * @return bool
     */
    public function assignUserToTeamLeader(Team $team, int $userId): bool;

    /**
     * @param Team $team
     * @param int $userId
     * @param string $memberTitle
     * @return bool
     */
    public function createTeamMemberFromUser(Team $team, int $userId, string $memberTitle): bool;

    /**
     * @param Team $team
     * @param int $userId
     * @return bool
     * @throws ModelNotFoundException
     */
    public function removeUserFromTeam(Team $team, int $userId): bool;

    /**
     * @param int $teamId
     * @return bool
     */
    public function deleteTeam(int $teamId): bool;

    /**
     * @param array $teamData
     * @param array $leaderData
     * @param array $memberData
     * @return bool
     */
    public function updateTeam(array $teamData, array $leaderData, array $memberData): bool;

}
