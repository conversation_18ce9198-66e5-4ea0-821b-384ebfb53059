<?php

namespace App\Contracts\Repositories;

use App\Builders\ActivityBuilder;
use App\Enums\ActivityType;
use App\Models\ActivityFeed;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

interface ActivityFeedRepositoryContract
{
    /**
     * Returns list of activity feeds based on the requested parameters.
     *
     * @param int|null          $companyId
     * @param int|null          $userId
     * @param string|null       $searchQuery
     * @param ActivityType|null $type
     * @param Carbon|null       $startDate
     * @param Carbon|null       $endDate
     * @param string|null       $sortByColumn
     * @param string|null       $sortOrder
     * @return Builder|null
     */
    public function getActivityFeeds(
        ?int            $companyId    = null,
        ?int            $userId       = null,
        ?string         $searchQuery  = null,
        ?ActivityType   $type         = null,
        ?Carbon         $startDate    = null,
        ?Carbon         $endDate      = null,
        ?string         $sortByColumn = null,
        ?string         $sortOrder    = null
    ): ?Builder;

    /**
     * Handles searching an activity based on the combination of company and activity IDs.
     *
     * @param int $companyId
     * @param int $activityId
     * @return ActivityFeed|null
     */
    public function findActivityByCompanyIdAndActivityId(int $companyId, int $activityId): ?ActivityFeed;

    /**
     * Returns overview (count) of each activity type based on the requested params.
     *
     * @param int|null $companyId
     * @param int|null $userId
     * @return array
     */
    public function getActivityFeedOverview(?int $companyId = null, ?int $userId = null): array;

    /**
     * @param int $itemId
     * @param ActivityType $type
     * @param ?bool $withTrashed
     * @return ?ActivityFeed
     */
    public function findActivityByItemIdAndType(int $itemId, ActivityType $type, ?bool $withTrashed = false): ?ActivityFeed;

    /**
     * @param int $itemId
     * @param ActivityType $itemType
     * @param int $companyId
     * @param int $userId
     * @return ?ActivityFeed
     */
    public function createActivity(int $itemId, ActivityType $itemType, int $companyId, int $userId): ?ActivityFeed;
}
