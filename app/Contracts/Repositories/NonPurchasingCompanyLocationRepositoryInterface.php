<?php

namespace App\Contracts\Repositories;

use App\Models\Legacy\NonPurchasingCompanyLocation;
use Illuminate\Support\Collection;

interface NonPurchasingCompanyLocationRepositoryInterface
{
    /**
     * @param int $locationId
     * @return Collection<NonPurchasingCompanyLocation>|null
     */
    public function getLegacyCompanyIdsByLocation(int $locationId): ?Collection;

    public function getLegacyCompanyIdsByLocationForAccountManager(int $locationId, int $accountManagerUserId): ?Collection;
}
