<?php

namespace App\Contracts\Repositories;

use App\Models\Legacy\EloquentQuote;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

interface QuoteRepositoryContract
{
    /**
     * @param int $id
     * @return EloquentQuote|null
     */
    function find(int $id);

    /**
     * @param int $quoteId
     * @param string|null $userEmail
     * @param string|null $userIp
     * @param string|null $zipCode
     * @param string|null $phoneNumber
     * @return mixed
     */
    public function getAllocatedRelatedQuotesLast90Days(
        int     $quoteId,
        ?string $userEmail,
        ?string $userIp,
        ?string $zipCode,
        ?string $phoneNumber
    );

    /**
     * @param int $quoteId
     * @param string $userEmail
     * @param string $userIp
     * @param string $zipCode
     * @param string|null $phoneNumber
     * @param int|null $perPage
     * @return EloquentQuote[]|LengthAwarePaginator
     */
    public function getPaginatedRelatedQuotes($quoteId, $userEmail, $userIp, $zipCode, $phoneNumber, $perPage = null);

    /**
     * @param int $quoteId
     * @param string $userEmail
     * @param string $userIp
     * @param string $zipCode
     * @param string|null $phoneNumber
     * @param array $relations
     * @return EloquentQuote|Builder
     */
    function buildQueryForRelatedQuotes(
        int     $quoteId,
        string  $userEmail,
        string  $userIp,
        string  $zipCode,
        ?string $phoneNumber,
        array   $relations = []
    ):  \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Query\Builder;
}
