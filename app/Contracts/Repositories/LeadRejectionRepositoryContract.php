<?php

namespace App\Contracts\Repositories;

use App\Services\Legacy\Statistics;
use Illuminate\Support\Collection;

interface LeadRejectionRepositoryContract
{
    /**
     * @param int $startTimestamp
     */
    public function getBaseLeadRejectionQuery(int $startTimestamp): \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Query\Builder;

    /**
     * @param int $startTimestamp
     */
    public function getBaseLeadCountQuery(int $startTimestamp): \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Query\Builder;

    /**
     * @param array $leadQuery
     * @param array $rejectionQuery
     * @param int $startTimestamp
     * @return Statistics
     */
    public function performRejectionCalculation(array $leadQuery, array $rejectionQuery, int $startTimestamp): Statistics;

    /**
     * @param array $companyIds
     * @return Collection
     */
    public function getLeadRejectionStatisticsInList(array $companyIds): Collection;
}
