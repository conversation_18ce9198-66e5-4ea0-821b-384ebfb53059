<?php

namespace App\Contracts\Repositories;

use App\Models\Legacy\EloquentCompanyCRMAction;

interface CompanyCRMRepositoryContract
{
    /**
     * @param string $companyReference
     * @param int $userId
     * @param int $contactId
     * @param string $actionText
     * @return EloquentCompanyCRMAction|null
     */
    public function createAction(string $companyReference, int $userId, int $contactId, string $actionText): ?EloquentCompanyCRMAction;

    /**
     * @param array $companyIds
     * @return array
     */
    public function getCompaniesLastUpdated(array $companyIds = []): array;
}
