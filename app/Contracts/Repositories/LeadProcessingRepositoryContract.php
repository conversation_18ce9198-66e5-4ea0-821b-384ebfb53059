<?php

namespace App\Contracts\Repositories;

use App\Models\LeadProcessingAllocation;
use App\Models\LeadProcessingConfiguration;
use App\Models\LeadProcessingHeartbeat;
use App\Models\LeadProcessingTimeZoneConfiguration;
use App\Models\LeadProcessor;
use App\Models\Legacy\EloquentQuote;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

interface LeadProcessingRepositoryContract
{
    const SYSTEM_USER_ID = -1;

    /**
     * Reserves a lead for the system
     *
     * @param int $leadId
     * @return bool
     */
    public function reserveLeadToSystem(int $leadId): bool;

    /**
     * Reserves a lead for a given processor.
     *
     * @param int $leadId
     * @param int $processorId
     * @return bool
     */
    public function reserveLead(int $leadId, int $processorId): bool;

    /**
     * Unlocks a lead.
     *
     * @param string $leadReference
     * @param int|null $leadProcessorId
     * @return bool
     */
    public function releaseLead(int $leadId, ?int $leadProcessorId = null): bool;

    /**
     * Handles updating a status for a lead.
     *
     * @param string $leadReference
     * @param string $status
     * @return bool
     */
    public function updateLeadStatus(string $leadReference, string $status): bool;

    /**
     * Handles updating the status reason for a given lead.
     *
     * @param string $leadReference
     * @param string $statusReason
     * @return bool
     */
    public function updateLeadStatusReason(string $leadReference, string $statusReason): bool;

    /**
     * @param string $leadReference
     * @param string $type
     * @return bool
     */
    public function updateLeadType(string $leadReference, string $type): bool;

    /**
     * @param string $leadReference
     * @param string $classification
     * @return bool
     */
    public function updateLeadClassification(string $leadReference, string $classification): bool;

    /**
     * @param string $leadReference
     * @param string $comment
     * @return bool
     */
    public function addPublicComment(string $leadReference, string $comment): bool;

    /**
     * @param string $leadReference
     * @param Collection $data
     * @return bool
     */
    public function updateBasicInfo(string $leadReference, Collection $data): bool;

    /**
     * @param string $leadReference
     * @param string $bestTimeToContact
     * @return bool
     */
    public function updateBestTimeToContact(string $leadReference, string $bestTimeToContact): bool;

    /**
     * @param LeadProcessor $processor
     * @param int $startTimestamp
     * @return Collection
     */
    public function getProcessorHistory(LeadProcessor $processor, int $startTimestamp = 0): Collection;

    /**
     * @param int $processorId
     * @param int $leadId
     * @param int $queueId
     * @param string $action
     * @return int
     */
    public function recordProcessorHistory(int $processorId, int $leadId, int $queueId, string $action): int;

    /**
     * @param string $leadReference
     * @return void
     */
    public function deleteFailedQuoteCompanies(string $leadReference): void;

    /**
     * @param int $leadId
     * @return Collection
     */
    public function getDeliveredQuoteCompanyCampaigns(int $leadId): Collection;

    /**
     * @param int $leadId
     * @return Collection
     */
    public function getDeliveredQuoteCompanies(int $leadId): Collection;

    /**
     * @param int $leadId
     * @return Collection
     */
    public function getUndeliveredQuoteCompanies(int $leadId): Collection;

    /**
     * @param int $leadId
     * @return Builder
     */
    public function getUndeliveredQuoteCompaniesQuery(int $leadId): Builder;

    /**
     * @param int $leadId
     * @return LeadProcessingAllocation|null|Model
     */
    public function getLeadProcessingAllocationByLeadId(int $leadId): LeadProcessingAllocation|null|Model;

    /**
     * @param int $timeZoneOpeningDelayMin
     * @param int $leadRecencyThresholdSec
     * @param int $minimumReviewTime
     * @param int $leadProcessableDelaySec
     * @param int $checkNextLeadInterval
     * @param int $lastLeadCreatedInterval
     * @return bool
     */
    public function saveLeadProcessingConfiguration(
        int $timeZoneOpeningDelayMin,
        int $leadRecencyThresholdSec,
        int $minimumReviewTime,
        int $leadProcessableDelaySec,
        int $checkNextLeadInterval,
        int $lastLeadCreatedInterval
    ): bool;

    /**
     * @return LeadProcessingConfiguration|null
     */
    public function getLeadProcessingConfiguration(): ?LeadProcessingConfiguration;

    /**
     * @param Collection $timezones
     * @return bool
     */
    public function saveTimezoneConfigurations(Collection $timezones): bool;

    /**
     * @return Collection
     */
    public function getAllTimezoneConfigurations(): Collection;

    /**
     * @param int $offset
     * @return LeadProcessingTimeZoneConfiguration|Model|null
     */
    public function getTimezoneConfigurationByStandardUTCOffset(int $offset): LeadProcessingTimeZoneConfiguration|Model|null;

    /**
     * @param EloquentQuote $lead
     * @param LeadProcessor $processor
     * @return void
     */
    public function processHeartbeat(EloquentQuote $lead, LeadProcessor $processor): void;

    /**
     * @param EloquentQuote $lead
     * @param LeadProcessor $processor
     * @return LeadProcessingHeartbeat|null
     */
    public function getHeartbeat(EloquentQuote $lead, LeadProcessor $processor): ?LeadProcessingHeartbeat;

    /**
     * @param EloquentQuote $lead
     * @param LeadProcessor $processor
     * @return bool
     */
    public function hasHeartbeat(EloquentQuote $lead, LeadProcessor $processor): bool;

    /**
     * @param EloquentQuote $lead
     * @param LeadProcessor $processor
     * @return void
     */
    public function removeHeartbeat(EloquentQuote $lead, LeadProcessor $processor): void;

    /**
     * @param EloquentQuote $lead
     * @return bool
     */
    public function removeUnderReview(EloquentQuote $lead): bool;

    /**
     * @param EloquentQuote $lead
     * @param LeadProcessor $processor
     * @param string $reason
     * @param string|null $existingReason
     * @param int|null $consumerProductId
     *
     * @return bool
     */
    public function markLeadAsUnderReview(EloquentQuote $lead, LeadProcessor $processor, string $reason, ?string $existingReason = null, ?int $consumerProductId = null): bool;

    /**
     * @param EloquentQuote $lead
     * @param string $reason
     * @return bool
     */
    public function updatePendingReviewReason(EloquentQuote $lead, string $reason): bool;

    /**
     * @param EloquentQuote $lead
     * @return bool
     */
    public function removePendingReview(EloquentQuote $lead): bool;

    /**
     * @param EloquentQuote $lead
     * @param LeadProcessor $processor
     * @param string $reason
     * @return bool
     */
    public function markLeadAsPendingReview(EloquentQuote $lead, LeadProcessor $processor, string $reason): bool;

    /**
     * @param EloquentQuote $lead
     * @return bool
     */
    public function removeInitial(EloquentQuote $lead): bool;

    /**
     * Attempts to find a processor by the processor id.
     *
     * @param int  $processorId
     * @param bool $withTrashed
     * @return LeadProcessor|null
     */
    public function findLeadProcessor(int $processorId, bool $withTrashed = true): ?LeadProcessor;

    /**
     * @param int $userId
     * @return LeadProcessor|null
     */
    public function getLeadProcessorByUserId(int $userId): ?LeadProcessor;

    /**
     * @param EloquentQuote $lead
     * @param string $status
     * @param string $reason
     * @return bool
     */
    public function updateStatusAndReason(EloquentQuote $lead, string $status, string $reason): bool;

    /**
     * Finds consumer product ID for a lead.
     * - If the lead processor provided, performs search against the combination of lead and processor in reserved leads.
     * - Otherwise, tries looking for the consumer models connected to the given lead ID.
     *
     * @param int                $leadId
     * @param LeadProcessor|null $processor
     * @return int|null
     */
    public function getConsumerProductIdForLead(int $leadId, ?LeadProcessor $processor = null): ?int;
}
