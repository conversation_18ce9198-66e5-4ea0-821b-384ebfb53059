<?php

namespace App\Contracts\Repositories;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;

interface ReviewRepositoryContract
{
    /**
     * @param int $userId
     * @param string $userEmail
     * @param int $limit
     * @param array $relations
     * @return LengthAwarePaginator
     */
    public function getRelatedReviewsByUserPaginated(
        int    $userId,
        string $userEmail,
        int    $limit = 5,
        array  $relations = []
    ): LengthAwarePaginator;

    /**
     * Related reviews by user id and email
     *
     * @param int $userId
     * @param string $userEmail
     * @param array $relations
     * @return Builder
     */
    public function getRelatedReviewsByUserQuery(int $userId, string $userEmail, array $relations = []): Builder;

    /**
     * @param array $companyIdType
     * @return array
     */
    public function getCompaniesReviewCounts(array $companyIdType = []): array;
}
