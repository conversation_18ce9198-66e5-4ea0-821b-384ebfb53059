<?php

namespace App\Contracts\Repositories;

use App\Models\Ruleset;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

interface RulesetsRepositoryContract
{
    /**
     * Returns a list of all rulesets.
     *
     * @param array|null $queryFilter
     * @return Collection
     */
    public function getAll(?array $queryFilter): Collection;

    /**
     * Returns a ruleset against the requested name.
     *
     * @param string $name
     * @return Ruleset|null
     */
    public function getRulesetByName(string $name): ?Ruleset;

    /**
     * Handles finding a ruleset by its ID.
     *
     * @param int $id
     * @return Ruleset|null
     */
    public function getRulesetById(int $id): ?Ruleset;

    /**
     * Handles creating/updating a ruleset based on the requested dataset.
     *
     * @param string $name
     * @param string $type
     * @param string $source
     * @param array $filter
     * @param array $payload
     * @param int|null $id
     * @return Ruleset|Model
     */
    public function updateOrCreateRuleset(
        string $name,
        string $type,
        string $source,
        array $filter,
        array $payload,
        ?int $id = null
    ): Ruleset|Model;

    /**
     * Handles deleting a ruleset and its associations (Company quality score, Opportunity notification).
     *
     * @param Ruleset $ruleset
     * @return bool
     */
    public function deleteRuleset(Ruleset $ruleset): bool;

}
