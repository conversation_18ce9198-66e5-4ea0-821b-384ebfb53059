<?php

namespace App\Contracts\Repositories;

use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\Location;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;
use \Exception;

interface CompanyRepositoryContract
{
    /**
     * @param string $companyReference
     * @param string $companyStatus
     * @return bool
     * @throws Exception
     */
    public function updateCompanyStatus(string $companyReference, string $companyStatus): bool;

    /**
     * @param Location $countyLocation
     * @param string $industry
     * @param bool $filterByAccountManager
     * @return \Illuminate\Database\Eloquent\Builder|null
     */
    public function getCompaniesAgainstNonPurchasingLocationQuery(
        Location $countyLocation,
        string $industry,
        bool   $filterByAccountManager = true
    ): \Illuminate\Database\Eloquent\Builder|null;

    /**
     * @param string $companyReference
     * @param array $data
     * @return bool
     * @throws Exception
     */
    public function updateCompanyDetails(string $companyReference, array $data): bool;

    /**
     * Handles updating the basic details for a given company.
     *
     * @param string $companyReference
     * @param array $data
     * @return bool
     */
    public function updateBasicDetails(string $companyReference, array $data): bool;

    /**
     * Handles updating the configurable fields for a given company.
     *
     * @param string $companyReference
     * @param array $data
     * @return bool
     */
    public function updateConfigurableFields(string $companyReference, array $data): bool;

    /**
     * @param array $companyIds
     * @return array
     */
    public function getCompaniesPaymentMethodStatuses(array $companyIds): array;

    /**
     * Handles setting lead budget for the created company in legacy.
     *
     * @param string $companyReference
     * @param array  $data
     * @return array
     * @throws Exception
     */
    public function setLeadBudgetOfCreatedCompany(string $companyReference, array $data): array;

    /**
     * Handles setting lead contact for the created company in legacy.
     *
     * @param string $companyReference
     * @param array  $data
     * @return array
     * @throws Exception
     */
    public function setLeadContactOfCreatedCompany(string $companyReference, array $data): array;

    /**
     * Handles preparing company type for Legacy based on the given set of industries.
     * In case the industry is solar/roofing, the company type for Legacy would be prepared as installer/roofer respectively.
     * The company type would be "multi" if a company offers in multiple industries, or in an industry other than solar/roofing.
     *
     * @param int[] $industryIds
     * @return string
     */
    public function getLegacyCompanyTypeFromIndustryIds(array $industryIds): string;

    /**
     * Handles setting services for the given company in legacy.
     *
     * @param string $companyReference
     * @param array  $services
     * @return array
     * @throws Exception
     */
    public function setCompanyServices(string $companyReference, array $services): array;

    /**
     * Handles setting company type in legacy.
     *
     * @param string $companyReference
     * @param array  $data
     * @return array
     * @throws Exception
     */
    public function setCompanyType(string $companyReference, array $data): array;

    /**
     * Handles deleting a given service from a company in legacy.
     *
     * @param string $companyReference
     * @param array  $service
     * @return array
     * @throws Exception
     */
    public function removeCompanyService(string $companyReference, array $service): array;
}
