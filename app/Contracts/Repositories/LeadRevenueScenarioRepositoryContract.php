<?php

namespace App\Contracts\Repositories;

use App\Models\LeadRevenueScenario;

interface LeadRevenueScenarioRepositoryContract
{
    /**
     * Creates or updates a lead revenue scenario for a given lead.
     *
     * @param string $leadReference
     * @param float $current
     * @param float $maximum
     * @param float $floorPrice
     * @param string $type
     * @return bool
     */
    public function createOrUpdateLeadRevenueScenario(
        string $leadReference,
        float $current,
        float $maximum,
        float $floorPrice,
        string $type = LeadRevenueScenario::TYPE_UNVERIFIED
    ): bool;

    /**
     * Handles setting the value that a given lead is sold for.
     *
     * @param string $leadReference
     * @param float $soldFor
     * @return bool
     */
    public function updateSoldFor(string $leadReference, float $soldFor): bool;
}
