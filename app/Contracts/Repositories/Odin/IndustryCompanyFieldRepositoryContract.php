<?php

namespace App\Contracts\Repositories\Odin;

use App\DataModels\Odin\ConfigurableFieldDataModel;
use App\Enums\Odin\CompanyConfigurableFieldCategory;
use App\Models\Odin\IndustryCompanyField;
use Illuminate\Support\Collection;

interface IndustryCompanyFieldRepositoryContract
{
    /**
     * @param int $industry
     * @return Collection<IndustryCompanyField>
     */
    public function getIndustryCompanyFields(int $industry): Collection;

    /**
     * @param int $industry
     * @param string $name
     * @param string $key
     * @param int $type
     * @param int $showOnProfile
     * @param int $showOnDashboard
     * @param ConfigurableFieldDataModel $payload
     * @param CompanyConfigurableFieldCategory $category
     * @param int|null $id
     *
     * @return bool
     */
    public function updateOrCreateIndustryCompanyField(
        int    $industry,
        string $name,
        string $key,
        int    $type,
        int    $showOnProfile,
        int    $showOnDashboard,
        ConfigurableFieldDataModel $payload,
        CompanyConfigurableFieldCategory $category,
        ?int   $id = null
    ): bool;

    /**
     * @param int $id
     * @return bool
     */
    public function deleteIndustryCompanyField(int $id): bool;
}
