<?php

namespace App\Contracts\Repositories\Odin;

use App\Models\Odin\CompanyIndustryType;
use Illuminate\Support\Collection;

interface CompanyIndustryTypeRepositoryContract
{
    /**
     * @param int $industryType
     * @return Collection<CompanyIndustryType>
     */
    public function getCompanies(int $industryType): Collection;

    /**
     * @param int $industryType
     * @param int $company
     * @return Collection<CompanyIndustryType>
     */
    public function get(int $industryType, int $company): Collection;

    /**
     * @param int $industryType
     * @param int $company
     * @return bool
     */
    public function add(int $industryType, int $company): bool;
}
