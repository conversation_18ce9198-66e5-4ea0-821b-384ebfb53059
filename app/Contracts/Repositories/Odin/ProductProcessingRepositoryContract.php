<?php

namespace App\Contracts\Repositories\Odin;

use App\Models\LeadProcessingHeartbeat;
use App\Models\LeadProcessingTimeZoneConfiguration;
use App\Models\LeadProcessor;
use App\Models\Legacy\EloquentQuote;
use App\Models\Odin\ConsumerProduct;

interface ProductProcessingRepositoryContract
{
    const SYSTEM_USER_ID = -1;

    /**
     * Removes a product from being at initial level.
     *
     * @param ConsumerProduct $consumerProduct
     * @return bool
     */
    public function removeInitial(ConsumerProduct $consumerProduct): bool;

    /**
     * Removes a product from being pending review.
     *
     * @param ConsumerProduct $consumerProduct
     * @return bool
     */
    public function removePendingReview(ConsumerProduct $consumerProduct): bool;

    /**
     * Removes a product from being under review.
     *
     * @param ConsumerProduct $consumerProduct
     * @return bool
     */
    public function removeUnderReview(ConsumerProduct $consumerProduct): bool;

    /**
     * Updates the status of a product.
     *
     * @param ConsumerProduct $consumerProduct
     * @param int $status
     * @return bool
     */
    public function updateProductStatus(ConsumerProduct $consumerProduct, int $status): bool;

    /**
     * Handles updating the status reason of a product.
     *
     * @param ConsumerProduct $consumerProduct
     * @param string $reason
     * @return bool
     */
    public function updateProductStatusReason(ConsumerProduct $consumerProduct, string $reason): bool;

    /**
     * Handles updating status and reason of a product.
     *
     * @param ConsumerProduct $consumerProduct
     * @param int $status
     * @param string $reason
     * @return bool
     */
    public function updateProductStatusAndReason(ConsumerProduct $consumerProduct, int $status, string $reason): bool;

    /**
     * Handles adding comment for a product.
     *
     * @param ConsumerProduct $consumerProduct
     * @param string $comments
     * @return bool
     */
    public function addProductComment(ConsumerProduct $consumerProduct, string $comments): bool;

    /**
     * Handles adding best time to contact for a product.
     *
     * @param ConsumerProduct $consumerProduct
     * @param string $addBestTimeToContact
     * @return bool
     */
    public function updateBestTimeToCall(ConsumerProduct $consumerProduct, string $addBestTimeToContact): bool;

    /**
     * Creates a product processor history entry.
     *
     * @param ConsumerProduct $consumerProduct
     * @param LeadProcessor $processor
     * @param string $action
     * @return int
     */
    public function recordProcessorHistory(ConsumerProduct $consumerProduct, LeadProcessor $processor, string $action): int;

    /**
     * Reserves a product for the system.
     *
     * @param int $leadId
     * @param int $consumerProductId
     * @return bool
     */
    public function reserveProductToSystem(int $consumerProductId): bool;

    /**
     * Returns the timezone configurations against the requested utc offset.
     *
     * @param int $offset
     * @return LeadProcessingTimeZoneConfiguration|null
     */
    public function getTimezoneConfigurationByStandardUTCOffset(int $offset): ?LeadProcessingTimeZoneConfiguration;
}
