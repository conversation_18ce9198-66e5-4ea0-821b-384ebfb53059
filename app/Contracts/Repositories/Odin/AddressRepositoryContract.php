<?php

namespace App\Contracts\Repositories\Odin;

use App\Models\Odin\Address;

interface AddressRepositoryContract
{
    /**
     * Handles searching address against the legacy ID.
     *
     * @param int $legacyId
     * @return Address|null
     */
    public function findByLegacyId(int $legacyId): ?Address;

    /**
     * Returns an address details against the requested legacy ID or fails.
     *
     * @param int $legacyId
     * @return Address
     */
    public function findByLegacyIdOrFail(int $legacyId): Address;

    /**
     * Returns an address details against the requested ID or fails.
     *
     * @param int $id
     * @return Address
     */
    public function findByIdOrFail(int $id): Address;

    /**
     * Handles creating a new address.
     *
     * @param array $data
     * @return Address
     */
    public function createAddressFromAttributes(array $data = []): Address;

    /**
     * Handles updating an existing address for the requested attributes.
     *
     * @param Address $address
     * @param array $data
     * @return bool
     */
    public function updateAddress(Address $address, array $data): bool;
}
