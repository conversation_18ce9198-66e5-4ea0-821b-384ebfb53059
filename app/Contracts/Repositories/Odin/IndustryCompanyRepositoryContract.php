<?php

namespace App\Contracts\Repositories\Odin;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyIndustry;
use Illuminate\Support\Collection;
use Exception;

interface IndustryCompanyRepositoryContract
{
    /**
     * @param int $industry
     * @return Collection<CompanyIndustry>
     */
    public function getIndustryCompanies(int $industry): Collection;

    /**
     * @param int $industryCompanyId
     * @return CompanyIndustry|null
     */
    public function getIndustryCompanyById(int $industryCompanyId): ?CompanyIndustry;

    /**
     * @param int $industry
     * @param int $company
     * @return bool
     */
    public function addIndustryCompany(int $industry, int $company): bool;

    /**
     * @param int $id
     * @return bool
     */
    public function deleteIndustryCompany(int $id): bool;

    /**
     * @param int $industry
     * @return Collection<Company>
     */
    public function getNonAddedCompaniesAgainstIndustryId(int $industry): Collection;

    /**
     * Handles determining company type for legacy based on the requested set of industries and updates the given company in legacy.
     *
     * @param Company $company
     * @param array   $industryIds
     * @return bool
     * @throws Exception
     */
    public function setCompanyTypeInLegacy(Company $company, array $industryIds): bool;
}
