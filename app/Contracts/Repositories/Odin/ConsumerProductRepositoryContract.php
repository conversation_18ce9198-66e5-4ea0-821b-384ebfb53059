<?php

namespace App\Contracts\Repositories\Odin;

use App\Models\Odin\ConsumerProduct;
use App\Services\Odin\ConsumerProductVerification\ConsumerProductVerificationResult;
use App\Services\Odin\ConsumerProductVerification\IPQualityScoreResult;
use Illuminate\Database\Eloquent\Model;
use Exception;
use Illuminate\Support\Collection;

interface ConsumerProductRepositoryContract
{
    /**
     * Handles verifying the requested consumer product ID as lead and returns its collection.
     *
     * @param int $consumerProduct
     * @return ConsumerProduct|Model
     */
    public function getConsumerProductAsLead(int $consumerProduct): ConsumerProduct|Model;

    /**
     * Handles creating verification details against the requested consumer product.
     * For verification, it consumes a 3rd party service (White Pages API).
     *
     * @param ConsumerProduct $consumerProduct
     * @return ConsumerProductVerificationResult
     * @throws Exception
     */
    public function createConsumerProductVerificationDetails(ConsumerProduct $consumerProduct): ConsumerProductVerificationResult;

    /**
     * Returns verification details of the requested consumer product.
     *
     * @param ConsumerProduct $consumerProduct
     * @return ConsumerProductVerificationResult
     * @throws Exception
     */
    public function getConsumerProductVerificationDetails(ConsumerProduct $consumerProduct): ConsumerProductVerificationResult;

    /**
     * Handles creating IP quality score against the requested consumer product.
     * For score information, it consumes a 3rd party service (IP Quality API).
     *
     * @param ConsumerProduct $consumerProduct
     * @return IPQualityScoreResult|null
     * @throws Exception
     */
    public function createIPQualityScore(ConsumerProduct $consumerProduct): ?IPQualityScoreResult;

    /**
     * Returns IP quality score of the requested consumer product.
     *
     * @param ConsumerProduct $consumerProduct
     * @return IPQualityScoreResult|null
     * @throws Exception
     */
    public function getIPQualityScore(ConsumerProduct $consumerProduct): ?IPQualityScoreResult;

    /**
     * @param string $query
     * @return Collection
     */
    public function searchConsumerProducts(string $query): Collection;
}
