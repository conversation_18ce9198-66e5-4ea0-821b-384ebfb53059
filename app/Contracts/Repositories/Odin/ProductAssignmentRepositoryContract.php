<?php

namespace App\Contracts\Repositories\Odin;

use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use Illuminate\Support\Collection;

interface ProductAssignmentRepositoryContract
{

    /**
     * Returns details of a product assignment against the requested legacy ID.
     *
     * @param int $legacyId
     * @return ProductAssignment
     */
    public function findByLegacyIdOrFail(int $legacyId): ProductAssignment;

    /**
     * Returns details of a product assignment against the requested legacy ID.
     *
     * @param int $legacyId
     * @return ProductAssignment|null
     */
    public function findByLegacyId(int $legacyId): ?ProductAssignment;

    /**
     * Returns details of a product assignment against the requested ID.
     *
     * @param int $id
     * @return ProductAssignment
     */
    public function findByIdOrFail(int $id): ProductAssignment;

    /**
     * @param ConsumerProduct $consumerProduct
     * @param Company $company
     * @return ProductAssignment|null
     */
    public function findByConsumerProductAndCompany(ConsumerProduct $consumerProduct, Company $company): ?ProductAssignment;

    /**
     * Handles creating a new product assignment based on the requested data set.
     *
     * @param array $data
     * @return ProductAssignment
     */
    public function createProductAssignment(array $data): ProductAssignment;

    /**
     * Handles updating an existing product assignment for the requested attributes.
     *
     * @param ProductAssignment $productAssignment
     * @param array $data
     * @return bool
     */
    public function updateProductAssignment(ProductAssignment $productAssignment, array $data): bool;

    /**
     * @param array $identifyingData
     * @param array $data
     * @return ProductAssignment
     */
    public function updateOrCreateProductAssignment(array $identifyingData, array $data): ProductAssignment;

    /**
     * @param ConsumerProduct $consumerProduct
     * @param array $relations
     * @return Collection
     * @deprecated Appointments no longer in use
     */
    public function findSoldProductAssignmentsByConsumerProduct(ConsumerProduct $consumerProduct, array $relations = []): Collection;

    /**
     * Handles fetching a list of product assignments for the given consumer product.
     *
     * @param ConsumerProduct $consumerProduct
     * @return Collection
     */
    public function getByConsumerProduct(ConsumerProduct $consumerProduct): Collection;
}
