<?php

namespace App\Contracts\Repositories\Odin;

use App\Models\Odin\CompanyReview;
use App\Models\Odin\IndustryType;
use Illuminate\Support\Collection;

interface IndustryTypeRepositoryContract
{
    /**
     * @param int $industry
     * @return Collection<IndustryType>
     */
    public function getIndustryTypes(int $industry): Collection;

    /**
     * @param int $globalType
     * @param int $industry
     * @param string $name
     * @param string $key
     * @param int|null $id
     * @return bool
     */
    public function updateOrCreateIndustryType(
        int    $globalType,
        int    $industry,
        string $name,
        string $key,
        ?int   $id = null
    ): bool;

    /**
     * @param int $id
     * @return bool
     */
    public function deleteIndustryType(int $id): bool;

    /**
     * @param int $industryType
     * @return Collection<CompanyReview>
     */
    public function getIndustryTypeReviews(int $industryType): Collection;

    /**
     * @param int $industryType
     * @param int $company
     * @param int $relId
     * @param int $relType
     * @param string $firstName
     * @param string $lastName
     * @param string|null $email
     * @param string|null $phone
     * @param string $title
     * @param string $body
     * @param int $overallScore
     * @param string $ipAddress
     * @return bool
     */
    public function createIndustryTypeReview(
        int     $industryType,
        int     $company,
        int     $relId,
        int     $relType,
        string  $firstName,
        string  $lastName,
        ?string $email,
        ?string $phone,
        string  $title,
        string  $body,
        int     $overallScore,
        int     $status,
        string  $ipAddress
    ): bool;

    /**
     * @param int $review
     * @return bool
     */
    public function deleteIndustryTypeReview(int $review): bool;
}
