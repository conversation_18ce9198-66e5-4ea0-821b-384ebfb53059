<?php

namespace App\Contracts\Repositories\Odin;

use App\Models\Odin\IndustryWebsite;
use App\Models\Odin\Website;
use Illuminate\Support\Collection;

interface IndustryWebsiteRepositoryContract
{
    /**
     * @param int $industry
     * @return Collection<IndustryWebsite>
     */
    public function getIndustryWebsites(int $industry): Collection;

    /**
     * @param int $industry
     * @param int $website
     * @param string $slug
     * @param int|null $id
     * @return bool
     */
    public function updateOrCreateIndustryWebsite(int $industry, int $website, string $slug, ?int $id = null): bool;

    /**
     * @param int $id
     * @return bool
     */
    public function deleteIndustryWebsite(int $id): bool;

    /**
     * @param int $industry
     * @return Collection<Website>
     */
    public function getNonAddedWebsitesAgainstIndustryId(int $industry): Collection;
}
