<?php

namespace App\Contracts\Repositories\Odin;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

interface CompanyUserRepositoryContract
{
    /**
     * Returns company user against the requested id.
     *
     * @param int $id
     * @return CompanyUser|Model
     */
    public function findCompanyUserByIdOrFail(int $id): CompanyUser|Model;

    /**
     * Returns list of users against the requested company.
     *
     * @param int $company
     * @return Builder
     */
    public function getCompanyUsersAgainstCompanyId(int $company): Builder;

    /**
     * Returns a company-user against the requested combination of company and user IDs.
     *
     * @param int $company
     * @param int $user
     * @return CompanyUser|Model
     */
    public function getCompanyUser(int $company, int $user): CompanyUser|Model;

    public function getCompanyUserOrCompanyContact(int $company, int $user): CompanyUser|Model;

    /**
     * Returns a company-user against the requested reference key.
     *
     * @param string $reference
     * @return CompanyUser|Model
     */
    public function getCompanyUserByReference(string $reference): CompanyUser|Model;

    /**
     * Handles updating model against the requested attributes.
     *
     * @param CompanyUser $user
     * @param array $data
     * @return bool
     */
    public function updateModel(CompanyUser $user, array $data): bool;

    /**
     * @param int $companyId
     * @param array $data
     * @return CompanyUser|null
     */
    public function createCompanyUser(int $companyId, array $data): ?CompanyUser;

    /**
     * Handles updating company user against the requested params.
     * Also exposes a PubSub event for the legacy site to update user details.
     *
     * @param string $companyReference
     * @param CompanyUser $user
     * @param array $data
     * @param ?bool $returnModel
     * @return bool|CompanyUser
     */
    public function updateCompanyUser(string $companyReference, CompanyUser $user, array $data, ?bool $returnModel = false): bool|CompanyUser;

    /**
     * Handles deleting the requested company user.
     * Also exposes a PubSub event for the legacy site to delete the user from there.
     *
     * @param string $companyReference
     * @param CompanyUser $user
     * @return bool
     */
    public function deleteCompanyUser(string $companyReference, CompanyUser $user): bool;

    /**
     * Returns list of contacts against the requested company.
     *
     * @param int $company
     * @return Collection<CompanyUser>
     */
    public function getCompanyContactsAgainstCompanyId(int $company): Collection;

    /**
     * Returns a company-contact against the requested combination of company and contact IDs.
     *
     * @param int $company
     * @param int $contact
     * @return CompanyUser|Model
     */
    public function getCompanyContact(int $company, int $contact): CompanyUser|Model;

    /**
     * Handles creating a company contact against the requested params.
     * Also exposes a PubSub event for the legacy site to create a company contact there.
     *
     * @param Company $company
     * @param array $data
     * @return array
     */
    public function createCompanyContact(Company $company, array $data, ?bool $returnModel = false): array;

    /**
     * Handles updating company contact against the requested params.
     * Also exposes a PubSub event for the legacy site to update the company contact details.
     *
     * @param string $companyReference
     * @param CompanyUser $contact
     * @param array $data
     * @return bool|CompanyUser
     */
    public function updateCompanyContact(string $companyReference, CompanyUser $contact, array $data, ?bool $returnModel = false): bool|CompanyUser;
}
