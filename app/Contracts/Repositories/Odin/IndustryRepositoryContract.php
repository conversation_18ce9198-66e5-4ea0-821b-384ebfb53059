<?php

namespace App\Contracts\Repositories\Odin;

use App\Models\Odin\Company;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use Illuminate\Support\Collection;

interface IndustryRepositoryContract
{
    /**
     * Returns list of industries along with their details based on the requested parameters.
     *
     * @param bool $appendServices
     * @param bool $appendCompanies
     * @param bool $appendCompanyServices
     * @param array $industries
     * @return Collection<Industry>
     */
    public function getIndustries(bool $appendServices = false, bool $appendCompanies = false, bool $appendCompanyServices = false, array $industries = []): Collection;

    /**
     * Takes care of adding and updating an industry into the system based on the requested parameters.
     *
     * @param string $name
     * @param string|null $lightModeColor
     * @param string|null $darkModeColor
     * @param int|null $id
     * @return bool
     */
    public function updateOrCreateIndustry(
        string $name,
        ?string $lightModeColor = null,
        ?string $darkModeColor = null,
        ?int $id = null
    ): bool;

    /**
     * Takes care of removing a specific industry from the system.
     *
     * @param int $id
     * @return bool
     */
    public function deleteIndustry(int $id): bool;

    /**
     * Returns details of a specific industry based on the requested parameters.
     *
     * @param int $industry
     * @param bool $appendServices
     * @param bool $appendCompanies
     * @param bool $appendCompanyServices
     * @return Collection<Industry>
     */
    public function getIndustryDetail(int $industry, bool $appendServices = false, bool $appendCompanies = false, bool $appendCompanyServices = false): Collection;

    /**
     * Returns list of services being offered against the requested industry.
     *
     * @param int $industry
     * @param bool $appendCompanyServices
     * @return Collection<IndustryService>
     */
    public function getIndustryServices(int $industry, bool $appendCompanyServices = false): Collection;

    /**
     * Returns list of companies against the requested service.
     *
     * @param int $industryService
     * @return Collection<Company>
     */
    public function getIndustryServicedCompanies(int $industryService): Collection;
}
