<?php

namespace App\Contracts\Repositories\Odin;

use Illuminate\Support\Collection;

interface AvailableLeadsRepositoryContract
{
    /**
     * Removes data from the table.
     *
     * @return void
     */
    public function truncate(): void;

    /**
     * * Calculates available leads against the requested service.
     *
     * @param int $industryService
     * @param string $period
     * @return Collection
     */
    public function getAvailableLeadsForIndustryService(int $industryService, string $period): Collection;

    /**
     * Handles adding records in the table.
     *
     * @param array $data
     * @return void
     */
    public function save(array $data): void;

    /**
     * Prepares data list and gears up for saving it.
     *
     * @param array $chunk
     * @param string|null $period
     * @return void
     */
    public function saveChunk(array $chunk, ?string $period = null): void;
}
