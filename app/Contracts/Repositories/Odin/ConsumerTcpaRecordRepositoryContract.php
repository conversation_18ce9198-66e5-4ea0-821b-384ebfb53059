<?php
namespace App\Contracts\Repositories\Odin;
interface ConsumerTcpaRecordRepositoryContract
{
    /**
     * @param int $consumer
     * @param string $tcpa
     * @param string $tcpaService
     * @param int|null $id
     * @return bool
     */
    public function createOrUpdateConsumerAffiliateRecord(
        int $consumer,
        string $tcpa,
        string $tcpaService,
        ?int $id = null
    ): bool;
}
