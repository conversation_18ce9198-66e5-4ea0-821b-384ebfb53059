<?php

namespace App\Contracts\Repositories\Odin;

use App\Models\Odin\ConsumerProductAffiliateRecord;

interface ConsumerAffiliateRecordRepositoryContract
{
    /**
     * Handles adding a new or updating an existing affiliate record.
     *
     * @param int $affiliate
     * @param int $campaign
     * @param string|null $trackName
     * @param string|null $trackCode
     * @param int|null $id
     * @return ConsumerProductAffiliateRecord
     */
    public function createOrUpdateConsumerAffiliateRecord(
        int $affiliate,
        int $campaign,
        ?string $trackName,
        ?string $trackCode,
        ?int $id = null
    ): ConsumerProductAffiliateRecord;

    /**
     * Validates legacy ID and prepares collection to save.
     *
     * @param int|null $legacyId
     * @param array $data
     * @return bool
     */
    public function prepareConsumerAffiliateRecord(?int $legacyId, array $data): bool;
}
