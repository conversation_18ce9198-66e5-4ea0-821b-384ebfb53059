<?php

namespace App\Contracts\Repositories;

use \Exception;

interface CompanyAddressesRepositoryContract
{
    /**
     * @param string $companyReference
     * @param int $addressId
     * @param array $data
     * @return array
     * @throws Exception
     */
    public function updateCompanyAddressDetails(string $companyReference, int $addressId, array $data): array;

    /**
     * @param string $companyReference
     * @param array $data
     * @return array
     * @throws Exception
     */
    public function createCompanyAddress(string $companyReference, array $data): array;

    /**
     * @param string $companyReference
     * @param int $addressId
     * @return array
     * @throws Exception
     */
    public function deleteCompanyAddress(string $companyReference, int $addressId): array;
}
