<?php

namespace App\Contracts;

use App\Enums\NotificationLinkType;
use App\Enums\NotificationType;

/**
 * Interface for Exceptions that should also generate a notification for a given role.
 */
interface NotifiableExceptionInterface
{
    /**
     * Returns a given role id
     *
     * @return ?int
     */
    public function getRoleId(): ?int;

    /**
     *  Gets the id of who the notification is from
     *
     * @return int
     */
    public function getFromId(): int;

    /**
     * Gets the subject of the notification
     *
     * @return string
     */
    public function getSubject(): string;

    /**
     * Returns the message from the exception
     *
     * @return string
     */
    public function getBody(): string;
    /**
     * Gets the type of notification
     *
     * @return NotificationType
     */
    public function getType(): NotificationType;

    /**
     * Gets the link for the notification or null
     *
     * @return string|null
     */
    public function getLink(): ?string;

    /**
     *  Gets the type of link or null
     *
     * @return NotificationLinkType|null
     */
    public function getLinkType(): ?NotificationLinkType;

    /**
     *  Gets the payload for the notification or null
     *
     * @return array|null
     */
    public function getPayload(): ?array;

}
