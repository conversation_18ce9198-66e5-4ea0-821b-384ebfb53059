<?php

namespace App\Contracts;

interface ShortcodeContract extends InteractsWithPipeline
{
    /**
     * <PERSON><PERSON> returning the key for the shortcode.
     *
     * @return string
     */
    public function getKey(): string;

    /**
     * @return string
     */
    public function getLabel(): string;

    /**
     * <PERSON>les replacing any instances of the shortcode.
     *
     * @param string $data
     * @param string $value
     * @return string
     */
    public function replace(string $data, string $value): string;
}
