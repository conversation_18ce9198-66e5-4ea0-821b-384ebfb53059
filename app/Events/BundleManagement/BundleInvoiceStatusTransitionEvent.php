<?php

namespace App\Events\BundleManagement;


use App\Models\BundleInvoice;
use App\Models\User;
use Illuminate\Foundation\Events\Dispatchable;

/**
 * Event for when a BundleInvoice needs to transition to a different status.
 */
class BundleInvoiceStatusTransitionEvent
{
    use Dispatchable;

    public function __construct(public BundleInvoice $bundleInvoice, public string $transition, public User $user){}
}
