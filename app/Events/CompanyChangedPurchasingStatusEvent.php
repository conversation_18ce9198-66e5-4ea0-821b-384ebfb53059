<?php

namespace App\Events;

use App\Enums\CompanyConsolidatedStatus;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CompanyChangedPurchasingStatusEvent
{
    use Dispatchable, SerializesModels;

    /**
     * Create a new event instance.
     *
     * @param int $companyId
     * @param CompanyConsolidatedStatus $newStatus
     * @param CompanyConsolidatedStatus $oldStatus
     */
    public function __construct(
        public int $companyId,
        public CompanyConsolidatedStatus $newStatus,
        public CompanyConsolidatedStatus $oldStatus,
    ) {}
}
