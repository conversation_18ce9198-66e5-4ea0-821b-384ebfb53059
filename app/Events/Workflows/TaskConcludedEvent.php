<?php

namespace App\Events\Workflows;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TaskConcludedEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    const CONCLUSION_COMPLETED = 'completed';
    const CONCLUSION_DELETED   = 'deleted';

    /**
     * @return void
     */
    public function __construct(
        public int $taskId,
        public string $conclusion
    ) {}
}
