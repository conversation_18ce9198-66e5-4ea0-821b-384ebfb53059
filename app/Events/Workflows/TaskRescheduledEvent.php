<?php

namespace App\Events\Workflows;

use Carbon\Carbon;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TaskRescheduledEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * @return void
     */
    public function __construct(public int $taskId, public Carbon $newDateTime) {}

}
