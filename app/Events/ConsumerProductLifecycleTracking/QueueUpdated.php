<?php

namespace App\Events\ConsumerProductLifecycleTracking;

use Carbon\Carbon;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class QueueUpdated
{
    use Dispatchable, SerializesModels;

    public function __construct(
        public int $consumerProductId,
        public Carbon $updatedAt,
        public ?string $processorName,
        public string $newQueue,
        public ?string $reason,
        public ?string $comment,
        public ?bool $automatic
    ) {}
}
