<?php

namespace App\Events\ConsumerProductLifecycleTracking;

use Carbon\Carbon;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class AllocationAttemptScheduled
{
    use Dispatchable, SerializesModels;

    public function __construct(
        public int $consumerProductId,
        public Carbon $scheduleCalculatedAt,
        public ?int $delayInSeconds
    ) {}

}
