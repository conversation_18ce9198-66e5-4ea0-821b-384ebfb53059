<?php

namespace App\Events\ConsumerProcessing;

use App\Models\LeadProcessor;
use App\Models\Odin\ConsumerProduct;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\SerializesModels;

abstract class ConsumerProductQueueChangeBaseEvent
{
    use Dispatchable, SerializesModels;

    public function __construct(
        public ConsumerProduct $consumerProduct,
        public LeadProcessor $processor,
        public string $reason,
        public ?string $comment = null,
        public ?bool $publicComment = null,
    ) {}
}
