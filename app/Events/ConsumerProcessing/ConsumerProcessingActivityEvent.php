<?php

namespace App\Events\ConsumerProcessing;

use App\Enums\ConsumerProcessing\ConsumerProcessingActivityType;
use App\Enums\ConsumerProcessing\ConsumerProcessingActivityVisibilityScope;
use App\Models\Call;
use App\Models\Odin\ConsumerProduct;
use App\Models\Text;
use App\Models\User;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ConsumerProcessingActivityEvent
{
    use Dispatchable, SerializesModels;

    public function __construct(
        public ConsumerProcessingActivityType $type,
        public ConsumerProduct $consumerProduct,
        public ?string $reason = null,
        public ?string $comment = null,
        public ?User $user = null,
        public Call|Text|null $relatedActivity = null,
        public ?ConsumerProcessingActivityVisibilityScope $scope = null,
    ) {}
}