<?php

namespace App\Events\CompanyCampaign;

use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CRMDeliveryFailureEvent
{
    use Dispatchable, SerializesModels;

    /**
     * @param int $consumerProductId
     * @param int $processorId
     * @param int[] $failedCompanyIds
     * @param bool $hadSuccessfulDeliveries
     */
    public function __construct(
        public int   $consumerProductId,
        public int   $processorId,
        public array $failedCompanyIds,
        public bool  $hadSuccessfulDeliveries,
    ) {}
}