<?php

namespace App\Events\CompanyCampaign;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CompanyCampaignServiceAreaUpdatedEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     *
     * @param int $companyCampaignId
     * @param array $oldLocations //zip code locations ids
     */
    public function __construct(public int $companyCampaignId, public array $oldLocations) {}
}
