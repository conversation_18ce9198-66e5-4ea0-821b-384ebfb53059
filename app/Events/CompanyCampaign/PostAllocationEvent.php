<?php

namespace App\Events\CompanyCampaign;

use App\Models\Odin\ProductAssignment;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class PostAllocationEvent
{
    use Dispatchable, SerializesModels;

    /**
     * Create a new event instance.
     *
     * @param Collection<ProductAssignment> $productAssignments
     */
    public function __construct(public Collection $productAssignments) {}
}
