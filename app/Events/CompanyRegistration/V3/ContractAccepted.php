<?php

namespace App\Events\CompanyRegistration\V3;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ContractAccepted
{
    use Dispatchable, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(public Company $company, public CompanyUser $companyUser) {}
}
