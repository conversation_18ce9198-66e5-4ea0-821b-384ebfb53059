<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class FloorPricingUpdatedEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     * @param string $pricingScope
     * @param int $locationId
     * @param int $serviceProductId
     * @param array $data
     */
    public function __construct(
        public string $pricingScope,
        public int $locationId,
        public int $serviceProductId,
        public array $data,
    )
    {}
}
