<?php

namespace App\Events\LeadProcessing;


use App\Concerns\HasLeadProcessingData;

/**
 * Event for when a lead has been reserved.
 */
class LeadReservedEvent
{
    use HasLeadProcessingData;

    /**
     * @param string $leadReference
     * @param int $processorId
     */
    public function __construct(string $leadReference, int $processorId)
    {
        $this->leadReference = $leadReference;
        $this->processorId = $processorId;
    }
}
