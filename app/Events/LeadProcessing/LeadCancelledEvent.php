<?php

namespace App\Events\LeadProcessing;

use App\Concerns\HasLeadProcessingData;

class LeadCancelledEvent
{
    use HasLeadProcessingData;

    /** @var string $reason */
    public string $reason;

    public function __construct(string $leadReference, int $processorId, string $reason)
    {
        $this->leadReference = $leadReference;
        $this->processorId = $processorId;
        $this->reason = $reason;
    }
}
