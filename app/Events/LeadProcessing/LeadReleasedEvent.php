<?php

namespace App\Events\LeadProcessing;

use App\Concerns\HasLeadProcessingData;

/**
 * Event for when a lead is released.
 */
class LeadReleasedEvent
{
    use HasLeadProcessingData;

    /**
     * @param string $leadReference
     * @param int $processorId
     */
    public function __construct(string $leadReference, int $processorId)
    {
        $this->leadReference = $leadReference;
        $this->processorId = $processorId;
    }
}
