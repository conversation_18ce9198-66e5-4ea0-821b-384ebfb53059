<?php

namespace App\Events;

use App\Enums\Odin\ProductCampaignStatusType;
use Illuminate\Foundation\Events\Dispatchable;

class ProductCampaignStatusUpdatedEvent
{
    use Dispatchable;

    /**
     * Create a new event instance.
     *
     * @param int $productCampaignId
     * @param string $companyReference
     * @param int $initiatorId
     * @param bool $oldStatus
     * @param int $newStatus
     * @param ProductCampaignStatusType $statusType
     * @param ?string $statusReason
     * @param int|null $reactivationTimestamp
     */
    public function __construct(
        public int $productCampaignId,
        public string $companyReference,
        public int $initiatorId,
        public bool $oldStatus,
        public int $newStatus,
        public ProductCampaignStatusType $statusType,
        public ?string $statusReason,
        public ?int $reactivationTimestamp
    )
    {
    }
}
