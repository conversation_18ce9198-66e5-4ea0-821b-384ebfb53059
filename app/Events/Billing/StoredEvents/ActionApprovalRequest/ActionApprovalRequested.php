<?php

namespace App\Events\Billing\StoredEvents\ActionApprovalRequest;

use App\Events\Base\Authored;

class ActionApprovalRequested extends Authored
{
    public function __construct(
        public string $actionApprovalUuid,
        public int $modelId,
        public string $modelClass,
        public string $actionRequested,
        public string $authorType,
        public ?int $authorId = null,
        public array $arguments = [],
        public ?string $note = null
    )
    {
        parent::__construct($authorType, $authorId);
    }
}

