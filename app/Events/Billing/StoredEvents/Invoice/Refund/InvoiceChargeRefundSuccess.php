<?php

namespace App\Events\Billing\StoredEvents\Invoice\Refund;

use Spatie\EventSourcing\StoredEvents\ShouldBeStored;

class InvoiceChargeRefundSuccess extends ShouldBeStored
{
    public function __construct(
        public string $invoiceUuid,
        public string $externalTransactionId,
        public int $amount,
        public string $currency,
        public int $amountCaptured,
        public bool $fullyRefunded,
        public string $source,
        public string $type,
        public ?string $scenario = null,
        public ?string $scope = null,
        public ?string $reason = null,
    )
    {
    }
}
