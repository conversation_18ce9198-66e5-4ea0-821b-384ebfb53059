<?php

namespace App\Events\Billing\StoredEvents\Invoice\Refund;

use App\Events\Base\Authored;

class RequestRefund extends Authored
{
    public function __construct(
        public string $invoiceUuid,
        public array  $invoiceRefundObject,
        string        $authorType,
        ?int          $authorId,
    )
    {
        parent::__construct(
            $authorType,
            $authorId,
        );
    }
}
