<?php

namespace App\Events\Billing\StoredEvents\Invoice\Refund;

use App\Events\Billing\StoredEvents\Invoice\Charge\BillingWorkflowStoredEvent;

class InvoiceChargeRefundUpdated extends BillingWorkflowStoredEvent
{
    public function __construct(
        public string $externalRefundId,
        public string $externalChargeId,
        public string $currency,
        public int $amount,
        public ?string $invoiceRefundChargeUuid = null,
        public ?string $invoiceUuid = null,
    )
    {
    }

    static function getId(): string
    {
        return 'invoice_charge_refund_updated';
    }

    static function getName(): string
    {
        return 'Invoice Charge Refund Updated';
    }

    static function getPossibleActions(): array
    {
        return [];
    }
}
