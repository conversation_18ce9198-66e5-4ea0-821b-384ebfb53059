<?php

namespace App\Events\Billing\StoredEvents\Invoice\WriteOff;

use App\Events\Base\Authored;

class InvoiceWrittenOff extends Authored
{
    public function __construct(
        public string $invoiceUuid,
        public string $uuid,
        public string $date,
        public float $amount,
        string $authorType,
        ?int $authorId = null,
    )
    {
        parent::__construct(
            $authorType,
            $authorId,
        );
    }
}
