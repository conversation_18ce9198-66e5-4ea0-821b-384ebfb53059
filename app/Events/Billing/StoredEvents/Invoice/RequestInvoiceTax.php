<?php

namespace App\Events\Billing\StoredEvents\Invoice;

use App\Events\Base\Authored;
use Carbon\Carbon;
use Spatie\EventSourcing\StoredEvents\ShouldBeStored;

class RequestInvoiceTax extends Authored
{
    public function __construct(
        public string $invoiceUuid,
        public ?int $authorId,
        public string $authorType,
    )
    {
        parent::__construct($this->authorType,$this->authorId);
    }
}
