<?php

namespace App\Events\Billing\StoredEvents\Invoice;

use App\Events\Base\Authored;

class InvoiceStatusUpdated extends Authored
{
    public function __construct(
        public string $invoiceUuid,
        public string $newStatus,
        public string $authorType,
        public ?string $newScenario = null,
        public ?int $authorId = null,
        public ?string $oldStatus = null,
        public ?string $date = null
    )
    {
        parent::__construct($this->authorType, $this->authorId);
    }


    static function getId(): string
    {
        return 'invoice_status_updated';
    }

    static function getName(): string
    {
        return 'Invoice Status Updated';
    }

    static function getDescription(): string
    {
        return '';
    }
}
