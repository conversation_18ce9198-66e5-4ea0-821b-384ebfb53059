<?php

namespace App\Events\Billing\StoredEvents\Invoice;

use App\Events\Base\Authored;
use Carbon\Carbon;

class InvoiceInitialized extends Authored
{
    public function __construct(
        public string $invoiceUuid,
        public string $companyReference,
        public Carbon $dueDate,
        public Carbon $issueDate,
        public ?int $authorId,
        public string $authorType,
        public int $billingProfileId,
        public ?string $notes = null,
    )
    {
        parent::__construct($this->authorType, $this->authorId);
    }
}
