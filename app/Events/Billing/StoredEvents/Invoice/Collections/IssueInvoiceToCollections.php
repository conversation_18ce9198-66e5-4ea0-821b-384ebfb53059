<?php

namespace App\Events\Billing\StoredEvents\Invoice\Collections;

use App\Events\Base\Authored;

class IssueInvoiceToCollections extends Authored
{
    public function __construct(
        public string $invoiceUuid,
        public string $uuid,
        string $authorType,
        public string $sentDate,
        public string $recoveryStatus,
        public float $amountCollected,
        ?int $authorId = null,
    )
    {
        parent::__construct(
            $authorType,
            $authorId,
        );
    }
}
