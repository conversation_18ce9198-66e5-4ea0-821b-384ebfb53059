<?php

namespace App\Events\Billing\StoredEvents\Invoice\Collections;

use App\Events\Base\Authored;

class UpdateInvoiceCollections extends Authored
{
    public function __construct(
        public string $invoiceUuid,
        public string $uuid,
        string $authorType,
        public string $recoveryStatus,
        public float $amountRecovered,
        public float $amountLost,
        public ?string $recoveryDate = null,
        ?int $authorId = null,
    )
    {
        parent::__construct(
            $authorType,
            $authorId,
        );
    }
}
