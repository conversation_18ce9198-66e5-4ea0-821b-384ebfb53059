<?php

namespace App\Events\Billing\StoredEvents\Invoice;

use App\Enums\Billing\BillingPolicyActionType;
use Spatie\EventSourcing\StoredEvents\ShouldBeStored;

class InvoiceTransactionCreated extends ShouldBeStored
{
    public function __construct(
        public string $uuid,
        public string $invoiceUuid,
        public string $externalReference,
        public float $amount,
        public string $currency,
        public string $type,
        public string $origin,
        public ?array $payload = [],
        public ?string $scenario = null,
        public ?string $scope = null,
        public ?string $date = null
    )
    {

    }


    static function getId(): string
    {
        return 'invoice-transaction-created';
    }

    static function getName(): string
    {
        return 'Invoice transaction created';
    }

    /**
     * @return BillingPolicyActionType[]
     */
    static function getPossibleActions(): array
    {
        return [
        ];
    }
}
