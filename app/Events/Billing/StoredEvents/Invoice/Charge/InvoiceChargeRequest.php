<?php

namespace App\Events\Billing\StoredEvents\Invoice\Charge;

use App\Events\Base\Authored;
use Illuminate\Support\Collection;

class InvoiceChargeRequest extends Authored
{
    public function __construct(
        public string $uuid,
        public string $invoiceUuid,
        public int $total,
        public int $maxAttempts,
        public int $billingProfileId,
        public string $authorType,
        public string $invoicePaymentDate,
        public ?int $authorId = null,
        public ?string $attemptChargeDate = null,
        public Collection $paymentMethodsAttempts = new Collection(),
    )
    {
        parent::__construct($this->authorType, $this->authorId);
    }
}
