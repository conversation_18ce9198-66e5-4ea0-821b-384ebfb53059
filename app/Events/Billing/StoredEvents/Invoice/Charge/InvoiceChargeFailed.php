<?php

namespace App\Events\Billing\StoredEvents\Invoice\Charge;

use App\Enums\Billing\BillingPolicyActionType;
use App\Enums\Billing\BillingPolicyEventType;

class InvoiceChargeFailed extends BillingWorkflowStoredEvent
{
    public function __construct(
        public string $externalTransactionId,
        public string $invoiceUuid,
        public string $errorMessage,
        public int    $amount,
        public string $currency,
        public string $source,
    )
    {}

    static function getId(): string
    {
        return BillingPolicyEventType::INVOICE_CHARGE_FAILED->value;
    }

    static function getName(): string
    {
        return 'Invoice Charge Failed';
    }

    static function getDescription(): string
    {
        return 'This event is triggered when we get a charge failed response from the payment service provider';
    }

    static function getPossibleActions(): array
    {
        return [
            BillingPolicyActionType::SUSPEND_COMPANY,
            BillingPolicyActionType::SEND_EMAIL_NOTIFICATION,
        ];
    }
}
