<?php

namespace App\Events\Billing\StoredEvents\Invoice\Charge;

use Spatie\EventSourcing\StoredEvents\ShouldBeStored;

class InvoiceChargeDisputeCreated extends ShouldBeStored
{
    public function __construct(
        public string $externalTransactionId,
        public string $chargeId,
        public float $amount,
        public string $reason,
        public string $status,
        public string $currency,
        public string $source,
    )
    {
    }
}
