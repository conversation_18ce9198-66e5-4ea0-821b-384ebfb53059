<?php

namespace App\Events\Billing\StoredEvents\Invoice\Charge;

use Spatie\EventSourcing\StoredEvents\ShouldBeStored;

class InvoiceChargeDisputeClosed extends ShouldBeStored
{
    public function __construct(
        public string $externalTransactionId,
        public string $chargeId,
        public string $status,
        public float $amount,
        public string $currency,
        public string $source,
        public string $reason,
    )
    {
    }
}
