<?php

namespace App\Events\Billing\StoredEvents\Invoice\Charge;

use App\Events\Base\Authored;

class InvoiceChargeRequestFailed extends Authored
{
    public function __construct(
        public string $invoicePaymentChargeUuid,
        public string $invoicePaymentUuid,
        public string $invoiceUuid,
        public string $errorMessage,
        public string $errorCode,
        public string $authorType,
        public string $paymentMethodType,
        public int $paymentMethodId,
        public int $amount,
        public string $failedAt,
        public ?int $authorId = null,
    )
    {
        parent::__construct($this->authorType, $this->authorId);
    }
}
