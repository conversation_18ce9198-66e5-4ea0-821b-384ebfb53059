<?php

namespace App\Events\Billing\StoredEvents\Invoice\Charge;

use App\Events\Base\Authored;

class InvoiceChargeRequestSuccess extends Authored
{
    public function __construct(
        public string $invoicePaymentChargeUuid,
        public string $invoicePaymentUuid,
        public string $invoiceUuid,
        public string $authorType,
        public string $paymentMethodType,
        public int $paymentMethodId,
        public int $amount,
        public string $requestedAt,
        public ?int $authorId = null,
    )
    {
        parent::__construct($this->authorType, $this->authorId);
    }
}
