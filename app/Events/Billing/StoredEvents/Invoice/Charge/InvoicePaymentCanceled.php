<?php

namespace App\Events\Billing\StoredEvents\Invoice\Charge;

use App\Events\Base\Authored;

class InvoicePaymentCanceled extends Authored
{
    public function __construct(
        public string $invoicePaymentUuid,
        public string $authorType,
        public int $invoicePaymentAttemptNumber,
        public int $invoicePaymentMaxAttemptsPerPaymentMethod,
        public int $invoicePaymentTotal,
        public ?int $authorId = null,
    )
    {
        parent::__construct($this->authorType, $this->authorId);
    }
}
