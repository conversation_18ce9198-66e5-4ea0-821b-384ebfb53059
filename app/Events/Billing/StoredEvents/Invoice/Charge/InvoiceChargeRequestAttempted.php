<?php

namespace App\Events\Billing\StoredEvents\Invoice\Charge;

use App\Events\Base\Authored;

class InvoiceChargeRequestAttempted extends Authored
{
    public function __construct(
        public string $invoicePaymentUuid,
        public string $invoicePaymentChargeUuid,
        public string $invoiceUuid,
        public string $authorType,
        public string $paymentMethodType,
        public int $paymentMethodId,
        public int $amount,
        public string $attemptedAt,
        public ?int $authorId = null,
    )
    {
        parent::__construct($this->authorType, $this->authorId);
    }
}
