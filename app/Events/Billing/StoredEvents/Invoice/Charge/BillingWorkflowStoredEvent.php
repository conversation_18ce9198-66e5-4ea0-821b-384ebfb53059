<?php

namespace App\Events\Billing\StoredEvents\Invoice\Charge;

use App\Events\Billing\BillingWorkflowEventContract;
use Illuminate\Foundation\Events\Dispatchable;
use Spatie\EventSourcing\StoredEvents\ShouldBeStored;

abstract class BillingWorkflowStoredEvent extends ShouldBeStored implements BillingWorkflowEventContract
{
    use Dispatchable;

    /**
     * @return string
     */
    abstract static function getId(): string;

    /**
     * @return string
     */
    abstract static function getName(): string;

    /**
     * @return string|null
     */
    static function getDescription(): ?string
    {
        return '';
    }

    /**
     * @return array
     */
    abstract static function getPossibleActions(): array;
}
