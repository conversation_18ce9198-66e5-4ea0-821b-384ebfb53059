<?php

namespace App\Events\Billing\StoredEvents\Invoice\Charge;

use App\Enums\Billing\BillingPolicyActionType;
use App\Enums\Billing\BillingPolicyEventType;

class InvoiceChargeRequestMaxAttemptsExceeded extends BillingWorkflowStoredEvent
{
    public function __construct(
        public string $invoicePaymentChargeUuid,
        public string $invoicePaymentUuid,
        public string $invoiceUuid,
        public string $paymentMethodType,
        public int $paymentMethodId,
        public int $amount,
        public string $exceededAt
    )
    {

    }

    /**
     * @return string
     */
    static function getId(): string
    {
        return BillingPolicyEventType::INVOICE_CHARGE_MAX_ATTEMPTS_EXCEEDED->value;
    }

    /**
     * @return string
     */
    static function getName(): string
    {
        return 'Invoice Charge Max Attempts Exceeded';
    }

    /**
     * @return string
     */
    static function getDescription(): string
    {
        return 'This event is triggered immediately after the maximum number of attempts to request our payment gateway to charge an invoice';
    }

    /**
     * @return BillingPolicyActionType[]
     */
    static function getPossibleActions(): array
    {
        return [
            BillingPolicyActionType::SUSPEND_COMPANY,
            BillingPolicyActionType::SEND_EMAIL_NOTIFICATION
        ];
    }
}
