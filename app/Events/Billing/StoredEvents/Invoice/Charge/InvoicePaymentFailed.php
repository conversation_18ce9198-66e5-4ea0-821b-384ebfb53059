<?php

namespace App\Events\Billing\StoredEvents\Invoice\Charge;

use App\Enums\Billing\BillingPolicyActionType;
use App\Enums\Billing\BillingPolicyEventType;

class InvoicePaymentFailed extends BillingWorkflowStoredEvent
{
    public function __construct(
        public string $invoicePaymentUuid,
        public string $invoiceUuid,
        public string $errorMessage,
        public string $authorType,
        public int $amount,
        public ?int $authorId = null,
    )
    {

    }

    /**
     * @return string
     */
    static function getId(): string
    {
        return BillingPolicyEventType::INVOICE_PAYMENT_FAILED->value;
    }

    /**
     * @return string
     */
    static function getName(): string
    {
        return 'Invoice Payment Failed';
    }

    /**
     * @return BillingPolicyActionType[]
     */
    static function getPossibleActions(): array
    {
        return [
            BillingPolicyActionType::SUSPEND_COMPANY,
            BillingPolicyActionType::SEND_EMAIL_NOTIFICATION
        ];
    }
}
