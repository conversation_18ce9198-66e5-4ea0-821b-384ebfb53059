<?php

namespace App\Events\Billing\StoredEvents\Credit;

use App\Events\Base\Authored;

class CreditAdded extends Authored
{
    public function __construct(
        public string $uuid,
        public string $companyReference,
        public float $amount,
        public string $type,
        public string $authorType,
        public ?int $authorId = null,
        public ?string $notes = null,
        public array $billingProfileIds = []
    )
    {
        parent::__construct($this->authorType, $this->authorId);
    }
}
