<?php

namespace App\Events\Billing\StoredEvents\Credit;

use Carbon\Carbon;
use Spatie\EventSourcing\StoredEvents\ShouldBeStored;

class CreditTypeDeducted extends ShouldBeStored
{
    public function __construct(
        public int $companyId,
        public float $amount,
        public string $type,
        public string $authorType,
        public ?int $authorId,
        public ?string $notes = null,
    )
    {
    }
}
