<?php

namespace App\Events\Billing\StoredEvents\Credit;

use Spatie\EventSourcing\Attributes\EventVersion;
use Spatie\EventSourcing\StoredEvents\ShouldBeStored;

#[EventVersion(1)]
class CreditAppliedToInvoice extends ShouldBeStored
{
    public function __construct(
        public string $creditUuid,
        public string $invoiceUuid,
        public int $companyId,
        public float $amount,
        public string $creditType,
        public string $authorType,
        public ?int $authorId,
        public ?string $notes = null,
    )
    {
    }
}
