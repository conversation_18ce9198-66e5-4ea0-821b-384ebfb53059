<?php

namespace App\Events\Billing\StoredEvents\InvoiceItem;

use App\Events\Base\Authored;

class InvoiceItemAdded extends Authored
{
    public function __construct(
        public string $invoiceUuid,
        public int $quantity,
        public float $unitPrice,
        public string $billableType,
        public string $authorType,
        public ?int $billableId = null,
        public ?string $description = null,
        public ?int $authorId = null,
        public ?string $notes = null,
    )
    {
        parent::__construct($this->authorType,$this->authorId);
    }
}
