<?php

namespace App\Events\Billing\LaravelEvents;

use App\Enums\Billing\BillingPolicyActionType;
use Illuminate\Foundation\Events\Dispatchable;

class InvoicePaid extends BillingWorkflowEvent
{
    use Dispatchable;

    public function __construct(
        public string $invoiceUuid
    )
    {

    }

    static function getId(): string
    {
        return 'invoice_paid';
    }

    static function getName(): string
    {
        return 'Invoice Paid';
    }

    static function getDescription(): ?string
    {
        return '';
    }

    static function getPossibleActions(): array
    {
        return [
            BillingPolicyActionType::SEND_EMAIL_NOTIFICATION
        ];
    }
}
