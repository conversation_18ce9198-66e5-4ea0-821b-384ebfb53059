<?php

namespace App\Events\Billing\LaravelEvents;

use App\Enums\Billing\BillingPolicyActionType;
use App\Enums\Billing\BillingPolicyEventType;

class InvoiceFailed extends BillingWorkflowEvent
{
    public function __construct(
        public string $invoiceUuid
    )
    {

    }

    /**
     * @return string
     */
    static function getId(): string
    {
        return BillingPolicyEventType::INVOICE_FAILED->value;
    }

    /**
     * @return string
     */
    static function getName(): string
    {
        return 'Invoice Failed';
    }

    /**
     * @return string|null
     */
    static function getDescription(): ?string
    {
        return '';
    }

    /**
     * @return array
     */
    static function getPossibleActions(): array
    {
        return [
            BillingPolicyActionType::SEND_EMAIL_NOTIFICATION,
            BillingPolicyActionType::SUSPEND_COMPANY,
        ];
    }

}
