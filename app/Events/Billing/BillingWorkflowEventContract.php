<?php

namespace App\Events\Billing;

interface BillingWorkflowEventContract
{
    /**
     * @return string
     */
    static function getId(): string;

    /**
     * @return string
     */
    static function getName(): string;

    /**
     * @return string|null
     */
    static function getDescription(): ?string;

    /**
     * @return array
     */
    static function getPossibleActions(): array;
}
