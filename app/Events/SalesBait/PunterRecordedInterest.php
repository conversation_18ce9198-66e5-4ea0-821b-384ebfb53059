<?php

namespace App\Events\SalesBait;

use App\Models\SalesBaitLead;
use App\Models\SalesBaitRegisteredInterest;
use Illuminate\Foundation\Events\Dispatchable;

class PunterRecordedInterest
{
    use Dispatchable;

    public function __construct(public int $salesBaitId, public int $interestId) {}

    /**
     * returns the sales bait lead model.
     *
     * @return SalesBaitLead|null
     */
    public function getSalesBait(): ?SalesBaitLead
    {
        /** @var SalesBaitLead $lead */
        $lead = SalesBaitLead::query()->find($this->salesBaitId);

        return $lead;
    }

    /**
     * Returns the sales bait interest model.
     *
     * @return SalesBaitRegisteredInterest|null
     */
    public function getSalesBaitInterest(): ?SalesBaitRegisteredInterest
    {
        /** @var SalesBaitRegisteredInterest $interest */
        $interest = SalesBaitRegisteredInterest::query()->find($this->interestId);

        return $interest;
    }
}
