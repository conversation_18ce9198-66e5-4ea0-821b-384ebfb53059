<?php

namespace App\Reports;

use App\Enums\Billing\CreditType;
use App\Events\Billing\StoredEvents\Credit\CreditAdded;
use App\Events\Billing\StoredEvents\Credit\CreditDeducted;
use App\Models\Odin\Company;
use Spatie\EventSourcing\EventHandlers\Projectors\EventQuery;
use Spatie\EventSourcing\StoredEvents\Models\EloquentStoredEvent;

class CompanyCreditsQuery extends EventQuery
{
    private float $total = 0;

    private array $totalByCreditType = [];

    public function __construct(Company $company)
    {
        EloquentStoredEvent::query()
            ->where(function ($query) use ($company) {
                $query->where('event_class', CreditAdded::class)
                    ->whereJsonContains('event_properties->type', CreditType::CREDIT->value)
                    ->whereJsonContains('event_properties->companyReference', $company->{Company::FIELD_REFERENCE});
            })
            ->orWhere(function ($query) use ($company) {
                $query->where('event_class', CreditDeducted::class)
                    ->whereJsonContains('event_properties->companyReference', $company->{Company::FIELD_REFERENCE});
            })
            ->each(
                fn(EloquentStoredEvent $event) => $this->apply($event->toStoredEvent())
            );
    }

    /**
     * @param CreditAdded $creditAdded
     * @return void
     */
    public function applyCreditAdded(CreditAdded $creditAdded): void
    {
        $this->total += $creditAdded->amount;

        if (!isset($this->totalByCreditType[$creditAdded->type])) {
            $this->totalByCreditType[$creditAdded->type] = 0;
        }

        $this->totalByCreditType[$creditAdded->type] += $creditAdded->amount;
    }

    /**
     * @param CreditDeducted $creditRemoved
     * @return void
     */
    public function applyCreditRemoved(CreditDeducted $creditRemoved): void
    {
        $this->total -= $creditRemoved->amount;

        if (!isset($this->totalByCreditType[$creditAdded->type])) {
            $this->totalByCreditType[$creditRemoved->type] = 0;
        }

        $this->totalByCreditType[$creditRemoved->type] -= $creditRemoved->amount;
    }

    /**
     * @return float
     */
    public function getTotal(): float
    {
        return $this->total;
    }

    /**
     * @param CreditType|null $type
     * @return array|float
     */
    public function getTotalByType(?CreditType $type = null): array|float
    {
        if (!isset($type)) return $this->totalByCreditType;

        return $this->totalByCreditType[$type->value];
    }
}
