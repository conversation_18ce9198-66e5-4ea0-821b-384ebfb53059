<?php

namespace App\Http\Resources\TopCompanies;

use App\Models\Odin\Company;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Company
 */
class TopCompaniesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'reference' => $this->reference,
            'name' => $this->name,
            'bayesian_all_time' => $this->companyRanking?->bayesian_all_time ?? 0,
            'review_count' => $this->companyRanking?->review_count ?? 0,
            'phone' => null, //todo
        ];
    }
}
