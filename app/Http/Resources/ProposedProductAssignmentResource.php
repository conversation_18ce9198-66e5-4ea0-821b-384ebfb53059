<?php

namespace App\Http\Resources;

use App\DataModels\ProductAssignment\ProposedProductAssignment;
use App\Models\Odin\Product;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin ProposedProductAssignment
 */
class ProposedProductAssignmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'company_id' => $this->companyId,
            'company_name' => $this->company()->name,
            'campaign_id' => $this->campaign()->id,
            'campaign_name' => $this->campaign()->name,
            'cost' => $this->price,
            'sale_type' => $this->saleType()?->name,
            'product' => Product::query()->find($this->productId)->first()?->name
        ];
    }
}
