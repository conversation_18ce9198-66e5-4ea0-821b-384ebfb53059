<?php

namespace App\Http\Resources;

use App\DTO\FloorPricing\CustomFloorPricingHistoryLog;
use App\DTO\FloorPricing\FloorPricingHistoryLog;
use App\Http\Resources\Odin\BaseJsonResource;
use Carbon\Carbon;
use Illuminate\Http\Request;

/**
 * @mixin CustomFloorPricingHistoryLog
 */
class CompanyCampaignCustomPricingLogResource extends BaseJsonResource
{
    /**
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return [
            "causer_id"         => $this->getCauserId(),
            "causer_name"       => $this->getCauserName(),
            "county"            => $this->getCounty(),
            "county_key"        => $this->getCountyKey(),
            "date"              => Carbon::parse($this->getDate())->format('M d, Y h:i A'),
            "price_from"        => $this->getPriceFrom(),
            "price_to"          => $this->getPriceTo(),
            "sale_type"         => $this->getSaleType(),
            "state"             => $this->getState(),
            "state_abbr"        => $this->getStateAbbr(),
            "state_key"         => $this->getStateKey(),
            "company_id"        => $this->getCompanyId(),
            "company_name"      => $this->getCompanyName(),
            "campaign_id"       => $this->getCampaignId(),
            "campaign_name"     => $this->getCampaignName(),
            "quality_tier"      => $this->getQualityTier(),
        ];
    }
}
