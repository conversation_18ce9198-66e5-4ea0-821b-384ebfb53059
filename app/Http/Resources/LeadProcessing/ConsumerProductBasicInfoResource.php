<?php

namespace App\Http\Resources\LeadProcessing;

use App\Enums\Cookie;
use App\Enums\PermissionType;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\User;
use App\Repositories\CommunicationRepository;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Resources\Json\JsonResource;

class ConsumerProductBasicInfoResource extends JsonResource
{

    /**
     * Return obfuscated Lead data for all users without appropriate permissions
     *
     * @return array<string, mixed>
     * @throws BindingResolutionException
     */
    public function toArray($request): array
    {
        /** @var Consumer $consumer */
        $consumer = $this->{ConsumerProduct::RELATION_CONSUMER};

        $canCall = app(CommunicationRepository::class)->canCallConsumer($consumer);

        /** @var User $user */
        $user = Auth::user();
        if ($user->hasPermissionTo(PermissionType::VIEW_LEAD_PII->value) || $request->cookie(Cookie::PII_TOKEN->value)) {
            return [
                'name' => $consumer->getFullName(),
                'email' => $consumer->{Consumer::FIELD_EMAIL},
                'address' => $this->{ConsumerProduct::RELATION_ADDRESS}?->getFullAddress(),
                'phone' => $consumer->{Consumer::FIELD_PHONE},
                'phone_is_valid' => $consumer->{Consumer::ATTRIBUTE_IS_VALID_PHONE},
                'own_property' => strtolower($this->{ConsumerProduct::ATTRIBUTE_OWN_PROPERTY} ?? ""),
                'can_call' => $canCall
            ];
        }

        return [
            'name' => $consumer->getFullName(),
            'email' => obfuscateEmail($consumer->{Consumer::FIELD_EMAIL}),
            'address' => $this->{ConsumerProduct::RELATION_ADDRESS}?->getObfuscatedAddress(),
//            we need to send the real phone for dialer
            'phone' => $consumer->{Consumer::FIELD_PHONE},
            'phone_is_valid' => $consumer->{Consumer::ATTRIBUTE_IS_VALID_PHONE},
            'own_property' => strtolower($this->{ConsumerProduct::ATTRIBUTE_OWN_PROPERTY} ?? ""),
            'can_call' => $canCall
        ];
    }
}
