<?php

namespace App\Http\Resources\SalesOverview;

use App\Models\Call;
use App\Models\CallRecording;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Call
 */
class CallResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'to'        => $this->direction === Call::DIRECTION_INBOUND ? $this->phone->phone : $this->other_number,
            'from'      => $this->direction === Call::DIRECTION_INBOUND ? $this->other_number : $this->phone->phone,
            'recording' => $this->callRecording?->only([CallRecording::FIELD_DURATION_SECONDS, CallRecording::FIELD_RECORDING_LINK]),
            'timestamp' => $this->created_at->timestamp,
        ];
    }
}
