<?php

namespace App\Http\Resources\SalesOverview;

use App\Models\Text;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Text
 */
class TextResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'to'                => $this->direction === Text::DIRECTION_INBOUND ? $this->phone->phone : $this->other_number,
            'from'              => $this->direction === Text::DIRECTION_INBOUND ? $this->other_number : $this->phone->phone,
            'content'           => $this->message_body,
            'timestamp'         => $this->created_at?->timestamp,
            'direction'         => $this->direction,
            'text_media_assets' => $this->textMediaAssets ?? [],
        ];
    }
}
