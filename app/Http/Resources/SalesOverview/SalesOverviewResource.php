<?php

namespace App\Http\Resources\SalesOverview;

use App\Http\Resources\Odin\BaseJsonResource;
use Illuminate\Http\Request;

class SalesOverviewResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            "user_id"         => $this->user_id,
            "user_name"       => $this->user_name,
            "calls_in"        => $this->calls_in,
            "calls_out"       => $this->calls_out,
            "texts_in"        => $this->texts_in,
            "texts_out"       => $this->texts_out,
            "emails_in"       => $this->emails_in,
            "emails_out"      => $this->emails_out,
            "calls_total"     => $this->calls_total,
            "texts_total"     => $this->texts_total,
            "emails_total"    => $this->emails_total,
            "completed_demos" => $this->completed_demos,
            "total"           => $this->total,
        ];
    }
}
