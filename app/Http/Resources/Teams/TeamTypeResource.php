<?php

namespace App\Http\Resources\Teams;

use App\Models\Teams\TeamType;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Spatie\Permission\Models\Role;

class TeamTypeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id'            => $this->{TeamType::FIELD_ID},
            'name'          => $this->{TeamType::FIELD_NAME},
            'description'   => $this->{TeamType::FIELD_DESCRIPTION},
            'leader_roles'  => $this->{TeamType::RELATION_LEADER_ROLES}
                                    ->map(fn(Role $role) => [
                                        'id'    => $role->id,
                                        'name'  => $role->name,
                                    ]),
            'member_roles'  => $this->{TeamType::RELATION_MEMBER_ROLES}
                                    ->map(fn(Role $role) => [
                                        'id'    => $role->id,
                                        'name'  => $role->name,
                                    ]),
        ];
    }
}
