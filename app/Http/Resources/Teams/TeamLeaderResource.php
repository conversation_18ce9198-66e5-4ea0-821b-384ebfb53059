<?php

namespace App\Http\Resources\Teams;

use App\Models\Teams\TeamLeader;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TeamLeaderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        $user = $this->{TeamLeader::RELATION_USER};
        $reportsTo = $this->{TeamLeader::RELATION_REPORTS_TO};

        return [
            'id'            => $this->{TeamLeader::FIELD_ID},
            'title'         => $this->{TeamLeader::FIELD_TITLE},
            'user_id'       => $user->{User::FIELD_ID},
            'user_name'     => $user?->{User::FIELD_NAME},
            'reports_to'    => [
                                'user_id'    => $reportsTo?->{User::FIELD_ID},
                                'user_name'  => $reportsTo?->{User::FIELD_NAME},
                ],
        ];
    }
}
