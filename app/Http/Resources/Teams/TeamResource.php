<?php

namespace App\Http\Resources\Teams;

use App\Models\Teams\Team;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Team
 */
class TeamResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id'            => $this->{Team::FIELD_ID},
            'name'          => $this->name->label(),
            'description'   => $this->{Team::FIELD_DESCRIPTION},
            'teamType'      => new TeamTypeResource($this->{Team::RELATION_TEAM_TYPE}),
            'leader'        => new TeamLeaderResource($this->{Team::RELATION_TEAM_LEADER}),
            'members'       => TeamMemberResource::collection($this->{Team::RELATION_TEAM_MEMBERS}),
            'member_count'  => $this->{Team::RELATION_TEAM_MEMBERS}?->count() ?? 0,
            'parent_team_id' => $this->parent_team_id,
        ];
    }
}
