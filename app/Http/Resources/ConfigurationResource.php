<?php

namespace App\Http\Resources;

use App\Models\RoleConfiguration;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ConfigurationResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'key'          => $this['key'],
            'value'        => $this['value'],
            'display_name' => RoleConfiguration::displayNameFor($this['key']),
            'type'         => RoleConfiguration::typeFor($this['key']),
            'options'      => RoleConfiguration::optionsFor($this['key']),
            'placeholder'  => RoleConfiguration::placeholderFor($this['key']),
        ];
    }
}
