<?php

namespace App\Http\Resources;

use App\Enums\Billing\BillingVersion;
use App\Enums\LeadRefundItemChargeRefundStatus;
use App\Http\Resources\Notes\NoteResource;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\LeadRefund;
use App\Models\LeadRefundItem;
use App\Models\LeadRefundItemRefund;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Number;
use Illuminate\Support\Str;

/**
 * @mixin LeadRefund
 */
class LeadRefundApprovalResource extends BaseJsonResource
{

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     * @throws Exception
     */
    public function toArray($request): array
    {
        $numberOfSuccessfulRefunds = $this->items()->whereHas(LeadRefundItem::RELATION_LATEST_REFUND_ITEM_REFUND, function ($query) {
            $query->where(LeadRefundItemRefund::FIELD_STATUS, LeadRefundItemChargeRefundStatus::REFUNDED->value);
        })->count();

        return [
            'id'                           => $this?->{LeadRefund::FIELD_ID},
            'requested_by'                 => [
                'id'   => $this->requestedBy?->id,
                'name' => $this->requestedBy?->name,
            ],
            'reviewed_by'                  => [
                'id'   => $this->reviewedBy?->id,
                'name' => $this->reviewedBy?->name,
            ],
            'company'                      => [
                'id'   => $this->company->id,
                'name' => $this->company->name,
            ],
            'items'                        => $this->items->map(fn(LeadRefundItem $leadRefundItem) => $this->transformRefundItem($leadRefundItem)),
            'comments'                     => NoteResource::collection($this->comments),
            'total'                        => Number::currency($this->total),
            'status'                       => $this->status,
            'refund_type'                  => $this->type,
            'requested_at'                 => $this->created_at->format('M d, Y h:i A'),
            'reviewed_at'                  => $this->reviewed_at ? Carbon::parse($this->reviewed_at)->format('M d, Y h:i A') : null,
            'number_of_successful_refunds' => $numberOfSuccessfulRefunds
        ];
    }

    /**
     * @param LeadRefundItem $leadRefundItem
     * @return array
     */
    public function transformRefundItem(LeadRefundItem $leadRefundItem): array
    {
        $productAssignment = $leadRefundItem->productAssignment;
        $consumerProduct = $productAssignment->consumerProduct;
        $consumer = $consumerProduct->consumer;

        $billingVersion = $productAssignment->getBillingVersion();

        $invoiceId = $billingVersion === BillingVersion::V2
            ? $productAssignment->odinInvoiceItem->invoice_id
            : $productAssignment?->invoiceItem?->invoiceid;

        $campaignName = Str::replace(
            'admin_2_generated_',
            '',
            $productAssignment
                ?->leadCampaignSalesTypeConfiguration
                ?->{LeadCampaignSalesTypeConfiguration::RELATION_LEAD_CAMPAIGN}
                ?->{LeadCampaign::NAME}
            ?? "");

        return [
            'invoice_id'            => $invoiceId,
            'billing_version'       => $billingVersion,
            'id'                    => $consumer->{Consumer::FIELD_ID},
            'legacy_id'             => $consumer->{Consumer::FIELD_LEGACY_ID},
            'product_assignment_id' => $productAssignment?->{ProductAssignment::FIELD_ID},
            'industry'              => $consumerProduct->{ConsumerProduct::RELATION_INDUSTRY_SERVICE}->{IndustryService::RELATION_INDUSTRY}->{Industry::FIELD_NAME},
            'service'               => $consumerProduct->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::RELATION_SERVICE}->{IndustryService::FIELD_NAME},
            'date'                  => $consumer->{Consumer::FIELD_CREATED_AT}->format('Y-m-d') ?? null,
            'contact'               => $consumer->getFullName(),
            'address'               => $consumerProduct->{ConsumerProduct::RELATION_ADDRESS}?->getFullAddress(),
            'cost'                  => $productAssignment?->{ProductAssignment::FIELD_COST},
            'campaign'              => $campaignName,
            'refund_type'           => $leadRefundItem->refund_type->getName(),
            'refund_reason'         => $leadRefundItem->refund_reason,
            'refund_item_id'        => $leadRefundItem->id,
            'status'                => $leadRefundItem->status,
            'refund_charge_status'  => $leadRefundItem->{LeadRefundItem::RELATION_LATEST_REFUND_ITEM_REFUND}?->{LeadRefundItemRefund::FIELD_STATUS},
        ];
    }
}
