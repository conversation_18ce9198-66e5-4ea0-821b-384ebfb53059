<?php

namespace App\Http\Resources\Advertising;

use App\Models\TieredAdvertisingCampaign;
use App\Services\Advertising\TieredAdvertisingService;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * Class TieredAdvertisingCampaignResource
 * @mixin TieredAdvertisingCampaign
 */
class TieredAdvertisingCampaignResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray(Request $request): array|Arrayable|JsonSerializable
    {
        return [
            "id"                    => $this->{TieredAdvertisingCampaign::FIELD_ID},
            "industry"              => $this->{TieredAdvertisingService::INDUSTRY_NAME},
            "name"                  => $this->{TieredAdvertisingCampaign::FIELD_NAME},
            "tier"                  => $this->{TieredAdvertisingCampaign::FIELD_TIER},
            "lowerBound"            => $this->{TieredAdvertisingCampaign::FIELD_LOWER_BOUND},
            "upperBound"            => $this->{TieredAdvertisingCampaign::FIELD_UPPER_BOUND},
            "tcpaBid"               => $this->{TieredAdvertisingCampaign::FIELD_TCPA_BID},
            "platform"              => $this->{TieredAdvertisingCampaign::FIELD_PLATFORM},
            "coveredPopulation"     => $this->{TieredAdvertisingCampaign::FIELD_COVERED_POPULATION},
            "lastLocationUpdate"    => $this->{TieredAdvertisingCampaign::FIELD_DATA}[TieredAdvertisingCampaign::DATA_LAST_LOCATION_UPDATE] ?? null,
            "campaignPlatformId"    => $this->{TieredAdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID},
            "accountPlatformId"     => $this->{TieredAdvertisingService::ACCOUNT_PLATFORM_ID},
            "accountName"           => $this->{TieredAdvertisingService::ACCOUNT_NAME},
        ];
    }
}
