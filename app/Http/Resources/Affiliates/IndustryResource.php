<?php

namespace App\Http\Resources\Affiliates;

use App\Models\Odin\Industry;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class IndustryResource extends JsonResource
{
    /**
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return [
            "id" => $this->{Industry::FIELD_ID},
            "name" => $this->{Industry::FIELD_NAME}
        ];
    }
}
