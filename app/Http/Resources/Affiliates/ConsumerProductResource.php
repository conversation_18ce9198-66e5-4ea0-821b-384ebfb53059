<?php

namespace App\Http\Resources\Affiliates;

use App\Enums\Affiliate\PayoutStrategyTypeEnum;
use App\Helpers\CarbonHelper;
use App\Helpers\NumberHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use Illuminate\Http\Request;

/**
 * @mixin ConsumerProduct
 */
class ConsumerProductResource extends BaseJsonResource
{
    /**
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        $payout = $this->affiliatePayout;
        $strategy = $payout->strategy;
        $strategyClass = $strategy->type->getClass();

        $productAssignment = $this->productAssignment()
            ->where(ProductAssignment::FIELD_CHARGEABLE, true)
            ->where(ProductAssignment::FIELD_DELIVERED, true)
            ->whereDoesntHave(ProductAssignment::RELATION_PRODUCT_CANCELLATIONS)
            ->whereDoesntHave(ProductAssignment::RELATION_PRODUCT_REJECTIONS)
            ->get();

        $revenue = $productAssignment->sum(ProductAssignment::FIELD_COST);

        $payoutValue = $strategyClass->calculate($this->resource, $strategy);

        return [
            "id" => $this->{ConsumerProduct::FIELD_ID},
            "created_at" => $this->created_at->timezone('MST')->format(CarbonHelper::FORMAT_BASE_TIMEZONE),
            "good_to_sell" => $this->{ConsumerProduct::FIELD_GOOD_TO_SELL},
            "status" => $this->getAffiliateStatus(),
            "consumer" => new ConsumerResource($this->{ConsumerProduct::RELATION_CONSUMER}),
            "address" => new AddressResource($this->{ConsumerProduct::RELATION_ADDRESS}),
            "service_product" => new ServiceProductResource($this->{ConsumerProduct::RELATION_SERVICE_PRODUCT}),
            "payout" => NumberHelper::currencyFromCents($payoutValue),
            "legs_sold" => $productAssignment->count(),
            $this->mergeWhen($request->is('internal-api/*'), [
                "strategy" => new PayoutStrategyResource($strategy),
                "revenue" => NumberHelper::currency($revenue),
            ]),
        ];
    }
}
