<?php

namespace App\Http\Resources\Affiliates;

use App\Helpers\NumberHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Affiliates\Campaign;
use Illuminate\Http\Request;

/**
 * @mixin Campaign
 */
class CampaignReportResource extends BaseJsonResource
{
    /**
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'payout' => NumberHelper::currencyFromCents($this->payout),
            'revenue' => NumberHelper::currency($this->revenue),
            'lead_count' => $this->lead_count,
            'lead_gts_count' => $this->lead_gts_count,
            'leg_count' => $this->leg_count,
        ];
    }
}
