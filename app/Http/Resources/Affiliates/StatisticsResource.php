<?php

namespace App\Http\Resources\Affiliates;

use App\Helpers\NumberHelper;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StatisticsResource extends JsonResource
{
    public bool $preserveKeys = true;

    /**
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return [
            'leads_created'      => $this['leads_created'],
            'leads_good_to_sell' => $this['leads_good_to_sell'],
            'legs_sold'          => $this['legs_sold'],
            'total_payout'       => NumberHelper::currencyFromCents($this['total_payout']),
            'total_revenue'      => $this->when(
                $request->is('internal-api/*'),
                NumberHelper::currency($this['total_revenue'])
            ),
        ];
    }
}
