<?php

namespace App\Http\Resources\Affiliates;

use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AddressResource extends JsonResource
{
    /**
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return [
            "street" => $this->{Address::FIELD_ADDRESS_1} . ' ' . $this->{Address::FIELD_ADDRESS_2},
            "city" => $this->{Address::FIELD_CITY},
            "state" => $this->{Address::FIELD_STATE},
            "zip_code" => $this->{Address::FIELD_ZIP_CODE},
            "county" => $this->{Address::RELATION_ZIPCODE}->{Location::COUNTY}
        ];
    }
}
