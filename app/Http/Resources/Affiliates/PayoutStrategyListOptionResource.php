<?php

namespace App\Http\Resources\Affiliates;

use App\Enums\Affiliate\PayoutStrategyTypeEnum;
use App\Http\Resources\Odin\ListOptionResource;

/**
 * @mixin PayoutStrategyTypeEnum
 */
class PayoutStrategyListOptionResource extends ListOptionResource
{
    public function getId(): string
    {
        return $this->value;
    }

    public function getName(): string
    {
        return $this->getClass()->title();
    }

    public function getPayload(): array
    {
        $strategyType = $this->getClass();

        $default = $strategyType->toPresentationValue($strategyType->defaultValue());
        $boundary = $strategyType->valueBoundaries()->toArray();

        $boundary = array_map(fn ($item) => $strategyType->toPresentationValue($item), $boundary);

        return [
            'default'  => $default,
            'boundary' => $boundary,
        ];
    }
}
