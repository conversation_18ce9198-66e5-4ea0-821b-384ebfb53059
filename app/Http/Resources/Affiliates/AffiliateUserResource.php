<?php

namespace App\Http\Resources\Affiliates;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AffiliateUserResource extends JsonResource
{
    /**
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return [
            "id" => $this['id'],
            "name" => $this['name'],
            "email" => $this['email'],
            "email_verified_at" => $this['email_verified_at'],
            "view_lead_details" => $this['view_lead_details'],
            "affiliate_id" => $this['affiliate_id'],
            "created_at" => $this['created_at'],
            "updated_at" => $this['updated_at'],
        ];
    }
}
