<?php

namespace App\Http\Resources\Affiliates;

use App\Builders\Affiliates\AffiliateReportBuilder;
use App\Helpers\NumberHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Affiliates\Affiliate;
use Illuminate\Http\Request;

/**
 * @mixin Affiliate
 */
class AffiliateReportResource extends BaseJsonResource
{
    /**
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->{AffiliateReportBuilder::ID_COL},
            'uuid' => $this->uuid,
            'name' => $this->{AffiliateReportBuilder::NAME_COL},
            'payout' => NumberHelper::currencyFromCents($this->{AffiliateReportBuilder::PAYOUT_COL}),
            'lead_count' => $this->{AffiliateReportBuilder::LEAD_COUNT_COL},
            'lead_gts_count' => $this->{AffiliateReportBuilder::LEAD_GTS_COUNT_COL},
            'leg_count' => $this->{AffiliateReportBuilder::LEG_SOLD_COUNT_COL},
            $this->mergeWhen($request->is('internal-api/*'), [
                "strategy" => new PayoutStrategyResource($this->strategy),
                "strategy_count"  => $this->strategies()->count(),
                'revenue' => NumberHelper::currency($this->{AffiliateReportBuilder::REVENUE_COL}),
            ])
        ];
    }
}
