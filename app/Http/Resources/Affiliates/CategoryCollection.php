<?php

namespace App\Http\Resources\Affiliates;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class CategoryCollection extends ResourceCollection
{
    public bool $preserveKeys = true;

    /**
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return $this->collection->mapWithKeys(fn($category) => [$category['id'] => $category['name']])->toArray();
    }
}
