<?php

namespace App\Http\Resources\Affiliates;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Affiliates\PayoutStrategy;
use Illuminate\Http\Request;

/**
 * @mixin PayoutStrategy
 */
class PayoutStrategyResource extends BaseJsonResource
{
    /**
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        $strategy = $this->type->getClass();

        return [
            'title' => $strategy->title(),
            'type' => $this->type->value,
            'value' => $strategy->toPresentationValue($this->value),
        ];
    }
}
