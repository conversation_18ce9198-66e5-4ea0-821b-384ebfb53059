<?php

namespace App\Http\Resources\Affiliates;

use App\Models\Odin\ServiceProduct;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ServiceProductResource extends JsonResource
{
    /**
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return [
            "industry_service" => new IndustryServiceResource($this->{ServiceProduct::RELATION_SERVICE})
        ];
    }
}
