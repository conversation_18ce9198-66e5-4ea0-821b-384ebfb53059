<?php

namespace App\Http\Resources\Affiliates;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Affiliates\Affiliate;
use Illuminate\Http\Request;

/**
 * @mixin Affiliate
 */
class AffiliateResource extends BaseJsonResource
{
    /**
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            $this->mergeWhen($request->is('internal-api/*'), [
                "uuid" => $this->{Affiliate::FIELD_UUID},
                "strategy" => new PayoutStrategyResource($this->{Affiliate::RELATION_STRATEGY}),
                "strategy_count"  => $this->strategies()->count(),
            ])
        ];
    }
}
