<?php

namespace App\Http\Resources\Affiliates;

use App\Models\Affiliates\Category;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CategoryResource extends JsonResource
{
    /**
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return [
            "id" => $this->{Category::FIELD_ID},
            "name" => $this->{Category::FIELD_NAME}
        ];
    }
}
