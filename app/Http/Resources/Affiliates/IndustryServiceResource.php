<?php

namespace App\Http\Resources\Affiliates;

use App\Models\Odin\IndustryService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class IndustryServiceResource extends JsonResource
{
    /**
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return [
            "id" => $this->{IndustryService::FIELD_ID},
            "name" => $this->{IndustryService::FIELD_NAME},
            "industry" => new IndustryResource($this->{IndustryService::RELATION_INDUSTRY})
        ];
    }
}
