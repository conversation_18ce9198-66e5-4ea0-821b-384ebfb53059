<?php

namespace App\Http\Resources\Affiliates;

use App\Models\Odin\Consumer;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ConsumerResource extends JsonResource
{
    /**
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return [
            "id" => $this->{Consumer::FIELD_ID},
            "name" => $this->getFullName(),
            "email" => $this->{Consumer::FIELD_EMAIL},
            "phone" => $this->{Consumer::FIELD_PHONE},
            "legs_requested" => $this->{Consumer::FIELD_MAX_CONTACT_REQUESTS}
        ];
    }
}
