<?php

namespace App\Http\Resources\Affiliates;

use App\Helpers\CarbonHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Affiliates\PayoutStrategy;
use Illuminate\Http\Client\Request;

/**
 * @mixin PayoutStrategy
 */
class PayoutStrategyEventResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        $strategy = $this->type->getClass();

        return [
            'title'   => $strategy->title(),
            'date'    => CarbonHelper::parse($this->{PayoutStrategy::FIELD_ACTIVE_FROM})->toFormat(),
            'type'     => $this->type->value,
            'value'    => $strategy->toPresentationValue($this->value),
            'active' => $this->when(empty($this->{PayoutStrategy::FIELD_ACTIVE_TO}),true,false),
            'payload' => [
                "Author: " . ($this->author?->name ?? 'System'),
                $this->when($this->{PayoutStrategy::FIELD_ACTIVE_TO},"Ended: " . CarbonHelper::parse($this->{PayoutStrategy::FIELD_ACTIVE_TO})->toFormat()),
            ]
        ];
    }
}
