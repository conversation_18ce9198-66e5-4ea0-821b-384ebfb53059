<?php

namespace App\Http\Resources\Affiliates;

use App\Models\Affiliates\Campaign;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Campaign
 */
class CampaignResource extends JsonResource
{
    /**
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return [
            "id" => $this->id,
            "name" => $this->name,
            "category_id" => $this->category_id,
            "status" => $this->status,
            $this->mergeWhen($request->is('internal-api/*'), [
                'account_id' => $this->account_id,
                'campaign_id' => $this->campaign_id,
            ])
        ];
    }
}
