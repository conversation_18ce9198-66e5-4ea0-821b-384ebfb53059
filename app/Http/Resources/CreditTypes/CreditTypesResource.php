<?php

namespace App\Http\Resources\CreditTypes;

use App\Models\Billing\CreditType;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin CreditType
 */
class CreditTypesResource extends JsonResource
{

    const string ID                = 'id';
    const string NAME              = 'name';
    const string DESCRIPTION       = 'description';
    const string LINE_ITEM_TEXT    = 'line_item_text';
    const string EXPIRES_IN_DAYS   = 'expires_in_days';
    const string CONSUMPTION_ORDER = 'consumption_order';
    const string CASH              = 'cash';
    const string ACTIVE            = 'active';
    const string SLUG              = 'slug';

    /**
     * Transforms the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return [
            self::ID                => $this->id,
            self::CONSUMPTION_ORDER => $this->consumption_order,
            self::NAME              => $this->name,
            self::DESCRIPTION       => $this->description,
            self::LINE_ITEM_TEXT    => $this->line_item_text,
            self::EXPIRES_IN_DAYS   => $this->expires_in_days,
            self::CASH              => $this->cash,
            self::ACTIVE            => $this->active,
            self::SLUG              => $this->slug,
        ];
    }
}
