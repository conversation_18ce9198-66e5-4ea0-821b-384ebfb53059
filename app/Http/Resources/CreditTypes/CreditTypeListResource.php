<?php

namespace App\Http\Resources\CreditTypes;

use App\Http\Resources\Billing\CreditResource;
use App\Models\Billing\Credit;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CreditTypeListResource extends JsonResource
{
    const string LIST            = 'list';
    const string TITLE           = 'title';
    const string CURRENT_BALANCE = 'current_balance';
    const string INITIAL_BALANCE = 'initial_balance';

    /**
     * Transforms the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        $credits = $this->resource;

        return $credits->reduce(function (array $result, Credit $credit) {
            if (!isset($result[$credit->creditType->slug])) {
                $result[$credit->creditType->slug] = [
                    self::TITLE           => $credit->creditType->name,
                    self::LIST            => [],
                    self::CURRENT_BALANCE => 0,
                    self::INITIAL_BALANCE => 0,
                ];
            }

            $result[$credit->creditType->slug][self::LIST][]          = new CreditResource($credit);
            $result[$credit->creditType->slug][self::CURRENT_BALANCE] += $credit->remaining_value;
            $result[$credit->creditType->slug][self::INITIAL_BALANCE] += $credit->initial_value;

            return $result;
        }, []);
    }
}