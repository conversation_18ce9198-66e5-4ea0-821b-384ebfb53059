<?php

namespace App\Http\Resources\CompanyProfile;

use App\Models\CompanyProfile\CompanyProfile;
use App\Models\CompanyProfile\CompanyProfileLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\IndustryService;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin CompanyProfile
 */
class CompanyProfileResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray(Request $request): array|Arrayable|JsonSerializable
    {
        /** @var ?CompanyProfileLocation $profileLocation */
        $profileLocation = $this->companyProfileLocations()->first();
        $primaryLocation = $profileLocation?->location;

        $industryServices = $this->industryServices;

        $industries = $industryServices
            ->map(fn(IndustryService $industryService) => $industryService->industry->name)
            ->unique();

        $industryServices = $industryServices->pluck(IndustryService::FIELD_NAME);

        $statesServiced = $this->locations->pluck(Location::STATE);

        return [
            'name'                  => $this->name,
            'id'                    => $this->id,
            'logo'                  => $this->logo,
            'office_location'       => [
                'full_address'             => $profileLocation?->raw,
                'google_maps_query_string' => $profileLocation?->raw,
                'city'                     => $primaryLocation?->city,
                'state'                    => $primaryLocation?->state,
                'zip_code'                 => $primaryLocation?->zip_code,
                'street_address'           => $profileLocation?->raw,
            ],
            'updated_at'            => $this->updated_at,
            'timezone'              => 'N/A',
            'operating_hours'       => $this->hours,
            'type'                  => '',
            'in_business_since'     => $this->years_in_business,
            'company_video_url'     => null,
            'verified'              => false,
            'reviews'               => CompanyProfileReviewResource::collection($this->reviews),
            'ratings'               => [],
            'expert_rating'         => null,
            'expert_rating_factors' => [],
            'images'                => $this->images,
            'industries'            => $industries,
            'services'              => $industryServices,
            'brands_sold'           => [],
            'service_areas'         => $statesServiced,
            'certifications'        => [],
            'licenses'              => [],
            'introduction'          => $this->summary_overview,
            'customers_like'        => $this->summary_positives,
            'customers_dislike'     => $this->summary_negatives,
            'website'               => $this->websites[0] ?? "",
            'prescreened'           => false,
        ];
    }
}
