<?php

namespace App\Http\Resources\CompanyProfile;

use App\Models\CompanyProfile\CompanyProfile;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin CompanyProfile
 */
class CompanyListingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return [
            'name'            => $this->name,
            'profile_slug'    => $this->profile_slug,
            'expert_rating'   => 'N/A',
            'consumer_rating' => $this->rating,
            'total_reviews'   => $this->count_reviews,
        ];
    }
}
