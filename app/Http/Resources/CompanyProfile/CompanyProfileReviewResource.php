<?php

namespace App\Http\Resources\CompanyProfile;

use App\Models\CompanyProfile\CompanyProfileReview;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin CompanyProfileReview
 */
class CompanyProfileReviewResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'title'         => '',
            'content'       => htmlspecialchars_decode($this->text),
            'name'          => htmlspecialchars_decode($this->author),
            'location'      => "N/A",
            'date'          => $this->created_at,
            'overall_score' => $this->rating,
        ];
    }

}
