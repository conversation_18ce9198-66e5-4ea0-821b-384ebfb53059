<?php

namespace App\Http\Resources\Billing;

use App\Http\Resources\Odin\BaseJsonResource;
use Illuminate\Http\Client\Request;
use Spatie\EventSourcing\StoredEvents\StoredEvent;

/**
 * @mixin StoredEvent
 */
class CreateInvoicePdfResource extends BaseJsonResource
{
    const string EVENT_PROPERTIES_INVOICE_UUID = 'invoiceUuid';
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            self::EVENT_PROPERTIES_INVOICE_UUID => $this->event_properties[self::EVENT_PROPERTIES_INVOICE_UUID],
        ];
    }
}
