<?php

namespace App\Http\Resources\Billing;

use App\Enums\Billing\InvoicePaymentChargeStatus;
use App\Enums\Billing\InvoicePaymentStatus;
use App\Enums\Billing\InvoiceStates;
use App\Enums\Billing\InvoiceTransactionType;
use App\Helpers\CarbonHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Http\Resources\TagResource;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoicePayment;
use App\Models\Billing\InvoicePaymentCharge;
use App\Models\Billing\InvoiceTransaction;
use App\Services\CurrencyService;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Collection;

/**
 * @mixin Invoice
 */
class ListInvoiceResource extends BaseJsonResource
{

    const string ID                   = 'id';
    const string UUID                 = 'uuid';
    const string STATUS               = 'status';
    const string ISSUE_AT             = 'issue_at';
    const string DUE_AT               = 'due_at';
    const string AMOUNT               = 'amount';
    const string PDF                  = 'pdf';
    const string COMPANY_ID           = 'company_id';
    const string COMPANY_NAME         = 'company_name';
    const string CURRENT_STATUS_DATA  = 'status_data';
    const string STATUS_TITLE         = 'title';
    const string STATUS_ID            = 'id';
    const string STATUS_ACTIONS       = 'actions';
    const string TAGS                 = 'tags';
    const string HAS_PENDING_ACTION   = 'has_pending_action';
    const string BILLING_PROFILE      = 'billing_profile';
    const string BILLING_PROFILE_ID   = 'id';
    const string PAYMENT_METHOD       = 'payment_method';
    const string PAYMENT_FAIL_COUNT   = 'payment_fail_count';
    const string TRANSACTION_STATUSES = 'transaction_statuses';
    const string HAS_PDF              = 'has_pdf';
    const string TOTALS               = 'totals';
    const string OUTSTANDING          = 'outstanding';
    const string ITEMS_TOTAL          = 'items_total';

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        $isProcessingPayment = $this->isProcessingPayment();

        $possibleStateTransitionsFormatted = $this->formatPossibleStateTransitions($isProcessingPayment);

        return [
            self::HAS_PENDING_ACTION   => $this->has_pending_action,
            self::ID                   => $this->id,
            self::COMPANY_ID           => $this->{Invoice::FIELD_COMPANY_ID},
            self::COMPANY_NAME         => $this->company_name,
            'payment_charges'          => $this->getPaymentCharges(),
            'credits_applied'          => $this->getTotalCreditsApplied(),
            self::BILLING_PROFILE      => [
                self::BILLING_PROFILE_ID => $this->{Invoice::FIELD_BILLING_PROFILE_ID},
                self::PAYMENT_METHOD     => $this->payment_method
            ],
            self::CURRENT_STATUS_DATA  => [
                self::STATUS_TITLE   => $this->status->getTitle(),
                self::STATUS_ID      => $this->status,
                self::STATUS_ACTIONS => $possibleStateTransitionsFormatted,
            ],
            self::PAYMENT_FAIL_COUNT   => $this->getPaymentFailCount(),
            self::TRANSACTION_STATUSES => $this->status !== InvoiceStates::DRAFT
                ? $this->getInvoiceTransactionStatuses()
                : [],
            self::ISSUE_AT             => CarbonHelper::parseWithTimezone($this->issue_at)->format('Y-m-d'),
            self::DUE_AT               => CarbonHelper::parseWithTimezone($this->due_at)->format('Y-m-d'),
            self::HAS_PDF              => filled($this->invoice_url),
            self::TAGS                 => TagResource::collection($this->tags),
            self::TOTALS               => [
                self::OUTSTANDING => $this->total_outstanding
            ],
            self::ITEMS_TOTAL          => $this->total_items_price,
            'pdf_failed'               => $this->pdf_failed
        ];
    }

    /**
     * @return string|null
     */
    public function getPaymentFailCount(): ?string
    {
        $invoicePayment = $this->payments
            ->sortByDesc(InvoicePayment::FIELD_CREATED_AT)
            ->whereIn(InvoicePayment::FIELD_STATUS, [
                InvoicePaymentStatus::RESCHEDULED,
                InvoicePaymentStatus::PENDING,
            ])
            ->where(function ($payment) {
                return $payment->where(InvoicePaymentCharge::FIELD_STATUS, InvoicePaymentChargeStatus::FAILED);
            })
            ->first();

        if (!$invoicePayment) return null;

        return $invoicePayment->{InvoicePayment::FIELD_ATTEMPT_NUMBER} . '/' . $invoicePayment->{InvoicePayment::FIELD_MAX_ATTEMPTS_PER_PAYMENT_METHOD};
    }

    /**
     * @return mixed
     */
    public function getPaymentCharges(): Collection
    {
        return $this->payments
            ->filter(fn($charge) => $charge->{InvoicePayment::FIELD_STATUS} === InvoicePaymentStatus::CHARGED)
            ->pluck('charges')
            ->flatten()
            ->filter(fn($charge) => $charge->{InvoicePaymentCharge::FIELD_STATUS} === InvoicePaymentChargeStatus::REQUESTED->value)
            ->map(fn($charge) => [
                'type'  => $charge->paymentMethod->type,
                'total' => CurrencyService::fromAtomicToCurrency($charge->total),
                'date'  => CarbonHelper::parseWithTimezone($charge->created_at)->toIso8601String(),
            ])
            ->values()
            ->sortByDesc(fn($c) => $c['date']);
    }

    /**
     * @return array
     */
    public function getRelevantTransactionTypes(): array
    {
        return match ($this->status->status()) {
            InvoiceStates::COLLECTION->value => [
                InvoiceTransactionType::COLLECTIONS
            ],
            InvoiceStates::CHARGEBACK->value => [
                InvoiceTransactionType::DISPUTE
            ],
            InvoiceStates::ISSUED->value     => [
                InvoiceTransactionType::PAYMENT->value,
                InvoiceTransactionType::REFUND->value,
            ],
            InvoiceStates::PAID->value       => [
                InvoiceTransactionType::REFUND->value,
            ],
            default                          => []
        };
    }

    /**
     * @return Collection
     */
    protected function getInvoiceTransactionStatuses(): Collection
    {
        $relevantTransactionTypes = $this->getRelevantTransactionTypes();

        return $this->transactions()
            ->select([
                InvoiceTransaction::FIELD_SCOPE,
                InvoiceTransaction::FIELD_SCENARIO,
                InvoiceTransaction::FIELD_TYPE,
            ])
            ->whereIn(InvoiceTransaction::FIELD_TYPE, $relevantTransactionTypes)
            ->groupBy([
                InvoiceTransaction::FIELD_SCOPE,
                InvoiceTransaction::FIELD_SCENARIO,
                InvoiceTransaction::FIELD_TYPE,
            ])->get()->map(fn(InvoiceTransaction $invoiceTransaction) => [
                'type'               => $invoiceTransaction->{InvoiceTransaction::FIELD_TYPE},
                'scope'              => $invoiceTransaction->{InvoiceTransaction::FIELD_SCOPE},
                'scenario'           => $invoiceTransaction->{InvoiceTransaction::FIELD_SCENARIO},
                'consolidated_title' => collect([
                    $invoiceTransaction->scope?->getTitle(),
                    $invoiceTransaction->type->getTitle(),
                    $invoiceTransaction->scenario?->getTitle(),
                ])->filter()->join(' '),
            ]);
    }

    /**
     * @param bool $isProcessingPayment
     * @return Collection
     */
    protected function formatPossibleStateTransitions(
        bool $isProcessingPayment
    ): Collection
    {
        return collect($this->status->transitionableStates())
            ->filter(function ($transition) use ($isProcessingPayment) {
                return !in_array($transition, [
                    InvoiceStates::CHARGEBACK->value,
                    InvoiceStates::FAILED->value,
                    InvoiceStates::REFUNDED->value,
                    ...($isProcessingPayment ? [InvoiceStates::PAID->value] : [])
                ]);
            })
            ->map(fn($transition) => [
                self::STATUS_ID    => $transition,
                self::STATUS_TITLE => InvoiceStates::tryFrom($transition)->getActionTitle(),
            ]);
    }
}
