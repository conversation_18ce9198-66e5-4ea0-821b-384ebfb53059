<?php

namespace App\Http\Resources\Billing;

use App\Enums\Billing\PaymentMethodServices;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Billing\CompanyPaymentMethod;
use Exception;
use Illuminate\Http\Client\Request;

/**
 * @mixin CompanyPaymentMethod
 */
class CompanyPaymentMethodResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     * @throws Exception
     */
    public function toArray($request): array
    {
        $optionData = $this->getOptionData();

        return [
            ...$optionData,
            'payment_gateway_client_code'         => $this->{CompanyPaymentMethod::FIELD_PAYMENT_GATEWAY_CLIENT_CODE},
            'payment_gateway_payment_method_code' => $this->{CompanyPaymentMethod::FIELD_PAYMENT_GATEWAY_PAYMENT_METHOD_CODE},
            'number'                              => $this->{CompanyPaymentMethod::FIELD_NUMBER},
            'type'                                => $this->{CompanyPaymentMethod::FIELD_TYPE},
            'company_id'                          => $this->{CompanyPaymentMethod::FIELD_COMPANY_ID},
            'is_default'                          => $this->{CompanyPaymentMethod::FIELD_IS_DEFAULT},
            'added_by_id'                         => $this->{CompanyPaymentMethod::FIELD_ADDED_BY_ID},
            'added_by_type'                       => $this->{CompanyPaymentMethod::FIELD_ADDED_BY_TYPE},
            'expiry_month'                        => $this->{CompanyPaymentMethod::FIELD_EXPIRY_MONTH},
            'expiry_year'                         => $this->{CompanyPaymentMethod::FIELD_EXPIRY_YEAR},
            'id'                                  => $this->{CompanyPaymentMethod::FIELD_ID},
        ];
    }

    /**
     * @return string[]
     * @throws Exception
     */
    public function getOptionData(): array
    {
        return match ($this->{CompanyPaymentMethod::FIELD_TYPE}) {
            PaymentMethodServices::STRIPE->value => [
                'name' => $this->{CompanyPaymentMethod::FIELD_NUMBER} . ' ' . $this->{CompanyPaymentMethod::FIELD_EXPIRY_MONTH} . '/' . $this->{CompanyPaymentMethod::FIELD_EXPIRY_YEAR}
            ],
            PaymentMethodServices::MANUAL->value => [
                'name' => 'Bank'
            ],
            default                       => throw new Exception('Payment method not supported yet')
        };
    }
}
