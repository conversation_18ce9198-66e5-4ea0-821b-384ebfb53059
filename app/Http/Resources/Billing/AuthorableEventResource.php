<?php

namespace App\Http\Resources\Billing;

use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Odin\CompanyUser;
use App\Models\User;
use Illuminate\Http\Client\Request;
use Spatie\EventSourcing\StoredEvents\StoredEvent;

/**
 * @mixin StoredEvent
 */
abstract class AuthorableEventResource extends BaseJsonResource
{
    const string EVENT_PROPERTY_AUTHOR_ID   = "authorId";
    const string EVENT_PROPERTY_AUTHOR_TYPE = "authorType";
    const string AUTHOR_DETAILS             = 'author_details';
    const string NAME                       = 'name';
    const string AUTHOR_TYPE                = 'author_type';
    const string AUTHOR_ID                  = 'author_id';

    abstract public function getEventData(): array;

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        $authorType = $this->event_properties[self::EVENT_PROPERTY_AUTHOR_TYPE];

        $userDetails = [
            self::AUTHOR_ID   => $this->event_properties[self::EVENT_PROPERTY_AUTHOR_ID],
            self::AUTHOR_TYPE => InvoiceEventAuthorTypes::tryFrom($authorType)
        ];

        if ($authorType === InvoiceEventAuthorTypes::USER->value) {
            $userDetails[self::NAME] = User::withTrashed()->find($this->event_properties[self::EVENT_PROPERTY_AUTHOR_ID])->{User::FIELD_NAME};
        } elseif ($authorType === InvoiceEventAuthorTypes::COMPANY_USER->value) {
            $userDetails[self::NAME] = CompanyUser::withTrashed()->find($this->event_properties[self::EVENT_PROPERTY_AUTHOR_ID])->completeName();
        } else {
            $userDetails[self::AUTHOR_ID] = 'N/A';
            $userDetails[self::NAME]      = 'System';
        }

        return [
            ...$this->getEventData(),
            self::AUTHOR_DETAILS => $userDetails,
        ];
    }
}
