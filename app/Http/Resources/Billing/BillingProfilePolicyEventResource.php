<?php

namespace App\Http\Resources\Billing;

use App\BillingWorkflows\Activities\BaseActivity;
use App\Enums\Billing\BillingPolicyActionType;
use App\Enums\Billing\BillingPolicyEventType;
use App\Events\Billing\BillingWorkflowEventContract;
use App\Http\Resources\Odin\BaseJsonResource;
use Exception;
use Illuminate\Http\Client\Request;

/**
 * @mixin BillingPolicyEventType
 */
class BillingProfilePolicyEventResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     * @throws Exception
     */
    public function toArray($request): array
    {
        /** @var BillingWorkflowEventContract $eventClass */
        $eventClass = $this->getClass();

        return [
            'event_slug'        => $this->value,
            'event_name'        => $eventClass::getName(),
            'event_description' => $eventClass::getDescription(),
            'actions'           => collect($eventClass::getPossibleActions())
                ->unique()
                ->map(fn(BillingPolicyActionType $action) => $this->formatActions($action))
        ];
    }

    /**
     * @param BillingPolicyActionType $actionType
     * @return array
     */
    protected function formatActions(BillingPolicyActionType $actionType): array
    {
        try {
            /** @var BaseActivity $actionClass */
            $actionClass = $actionType->getClass();

            return [
                'action_slug'        => $actionType->value,
                'action_description' => $actionClass::getDescription(),
                'action_name'        => $actionClass::getName(),
            ];
        } catch (Exception $exception) {
            logger()->error($exception);
            return [];
        }
    }
}
