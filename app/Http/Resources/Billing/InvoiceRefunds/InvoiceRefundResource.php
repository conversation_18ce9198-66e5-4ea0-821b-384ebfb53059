<?php

namespace App\Http\Resources\Billing\InvoiceRefunds;

use App\Enums\Billing\InvoiceItemTypes;
use App\Helpers\CarbonHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Billing\InvoiceItem;
use App\Models\Billing\InvoiceRefund;
use App\Models\Billing\InvoiceRefundCharge;
use App\Models\Billing\InvoiceRefundItem;
use App\Models\Billing\InvoiceTransaction;
use Carbon\Carbon;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Number;

/**
 * @mixin InvoiceRefund
 */
class InvoiceRefundResource extends BaseJsonResource
{
    const string ID             = 'id';
    const string INVOICE_ID     = 'invoice_id';
    const string REFUND_ITEMS   = 'refund_items';
    const string TOTAL          = 'total';
    const string REFUND_CHARGES = 'refund_charges';
    const string STATUS         = 'status';
    const string REASON         = 'reason';
    const string CREATED_AT     = 'created_at';

    public function __construct($resource)
    {
        parent::__construct($resource);
    }

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        /** @var Collection<InvoiceRefundItem> $refundItems */
        $refundItems = $this->{InvoiceRefund::RELATION_REFUND_ITEMS};

        /** @var Collection<InvoiceRefundCharge> $refundCharges */
        $refundCharges = $this->{InvoiceRefund::RELATION_REFUND_CHARGES};

        return [
            self::ID             => $this->id,
            self::INVOICE_ID     => $this->invoice_id,
            self::REFUND_ITEMS   => $this->formatRefundItems($refundItems),
            self::TOTAL          => $this->total,
            self::REFUND_CHARGES => $this->formatRefundCharges($refundCharges),
            self::STATUS         => $this->status,
            self::REASON         => $this->reason,
            self::CREATED_AT     => CarbonHelper::parseWithTimezone($this->created_at)->toIso8601String()
        ];
    }

    /**
     * @param Collection<InvoiceRefundItem> $refundItems
     * @return Collection
     */
    private function formatRefundItems(Collection $refundItems): Collection
    {
        $formatted = collect();

        foreach ($refundItems as $refundItem) {
            $formatted->push([
                'id'               => $refundItem->id,
                'type'             => $refundItem->invoice_item_id === null
                    ? 'Custom Value'
                    : InvoiceItemTypes::fromClass($refundItem->{InvoiceRefundItem::RELATION_INVOICE_ITEM})->getTitle(),
                'value'            => $refundItem->value,
                'value_in_dollars' => $refundItem->value / 100,
                'data'             => $refundItem?->invoiceItem?->{InvoiceItem::FIELD_DESCRIPTION} ?? 'No Data'
            ]);
        }

        return $formatted;
    }

    /**
     * @param Collection<InvoiceRefundCharge> $refundCharges
     * @return Collection
     */
    private function formatRefundCharges(Collection $refundCharges): Collection
    {
        $formatted = collect();

        foreach ($refundCharges as $refundCharge) {
            $formatted->push([
                'id'            => $refundCharge->id,
                'payment_value' => $refundCharge->{InvoiceRefundCharge::RELATION_REFUNDED_PAYMENT}->{InvoiceTransaction::FIELD_AMOUNT},
                'refund_value'  => $refundCharge->{InvoiceRefundCharge::FIELD_AMOUNT},
                'status'        => $refundCharge->{InvoiceRefundCharge::FIELD_REQUEST_STATUS},
            ]);
        }

        return $formatted;
    }
}
