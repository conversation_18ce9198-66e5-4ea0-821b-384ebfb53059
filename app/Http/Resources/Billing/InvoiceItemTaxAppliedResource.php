<?php

namespace App\Http\Resources\Billing;

use Spatie\EventSourcing\StoredEvents\StoredEvent;

/**
 * @mixin StoredEvent
 */
class InvoiceItemTaxAppliedResource extends AuthorableEventResource
{
    const string EVENT_PROPERTY_INVOICE_ITEM_ID = "invoiceItemId";
    const string EVENT_PROPERTY_TAX             = "tax";

    /**
     * Transform the resource into an array.
     *
     * @return array
     */
    public function getEventData(): array
    {
        return [
            self::EVENT_PROPERTY_INVOICE_ITEM_ID => $this->event_properties[self::EVENT_PROPERTY_INVOICE_ITEM_ID],
            self::EVENT_PROPERTY_TAX             => $this->event_properties[self::EVENT_PROPERTY_TAX],
        ];
    }
}
