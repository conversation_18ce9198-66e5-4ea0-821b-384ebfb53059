<?php

namespace App\Http\Resources\Billing;

use App\DTO\Billing\Refund\InvoiceRefundDataDTO;
use App\Enums\Billing\Refunds\RefundReason;
use App\Helpers\CarbonHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\LeadRefundItemRefund;
use Illuminate\Http\Client\Request;

/**
 * @mixin InvoiceRefundDataDTO
 */
class InvoiceRefundDataDTOResource extends BaseJsonResource
{
    const string CUSTOM_AMOUNT = 'custom_amount';
    const string ID            = 'id';
    const string ITEMS         = 'items';
    const string REASON        = 'reason';
    const string REFUNDED_AT   = 'refunded_at';

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'related_lead_refund_id'   => $this->getRelatedLeadRefundId(),
            self::CUSTOM_AMOUNT        => $this->getCustomAmount(),
            'custom_amount_in_dollars' => $this->getCustomAmount() / 100,
            self::ID                   => $this->getId(),
            self::ITEMS                => $this->getItems(),
            self::REASON               => RefundReason::tryFrom($this->reason)?->getTitle() ?? $this->reason,
            self::REFUNDED_AT          => CarbonHelper::parse($this->getRefundedAt()->toISOString())->toFormat(),
        ];
    }

    /**
     * @return string|null
     */
    public function getRelatedLeadRefundId(): ?string
    {
        if (empty($this->getUuid())) {
            return null;
        }

        $leadRefundItemRefund = LeadRefundItemRefund::query()
            ->where(LeadRefundItemRefund::FIELD_INTERNAL_REFUND_UUID, $this->getUuid())
            ->first();

        return $leadRefundItemRefund?->item?->refund?->id;
    }
}
