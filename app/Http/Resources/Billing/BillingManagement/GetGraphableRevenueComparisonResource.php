<?php

namespace App\Http\Resources\Billing\BillingManagement;

use App\DTO\Graphs\BaseGraph;
use App\DTO\Graphs\BaseGraphDataset;
use App\Http\Resources\Odin\BaseJsonResource;
use Illuminate\Http\Client\Request;

/**
 * @mixin BaseGraph
 */
class GetGraphableRevenueComparisonResource extends BaseJsonResource
{
    const string LABELS   = 'labels';
    const string DATASETS = 'datasets';
    const string SUMMARY  = 'summary';

    public function __construct($resource)
    {
        parent::__construct($resource);
    }

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        /** @var BaseGraph $data */
        $data = $this->resource;

        $datasets = $data->getDatasets();

        $summary = [
            'title' => 'Revenue',
            'data'  => []
        ];

        foreach ($datasets as $dataset) {
            $array    = $dataset->getData();
            $lastItem = end($array);

            $summary['data'][] = [
                'value'      => $lastItem,
                'additional' => $dataset->getLabel()
            ];
        }

        return [
            self::LABELS   => $data->getLabels(),
            self::DATASETS => collect($data->getDatasets())->map(function (BaseGraphDataset $dataset) {
                return $dataset->toArray();
            }),
            self::SUMMARY  => $summary
        ];
    }
}
