<?php

namespace App\Http\Resources\Billing\BillingManagement;

use App\Enums\Billing\InvoiceStates;
use App\Http\Resources\Odin\BaseJsonResource;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Collection;

/**
 * @mixin Collection
 */
class GetInvoiceStatusCountResource extends BaseJsonResource
{
    const string DATASET = 'dataset';
    const string LABELS  = 'labels';

    public function __construct($resource)
    {
        parent::__construct($resource);
    }

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        /** @var Collection $dataset */
        $dataset = $this->resource;

        $dataset = collect(InvoiceStates::cases())->mapWithKeys(fn(InvoiceStates $case) => ([
            $case->value => [
                'count' => $dataset->first(fn($item) => ($item->status->status() === $case->value))?->invoice_count ?? 0,
                'title' => $case->getTitle()
            ]
        ]));

        return [
            self::DATASET => $dataset->pluck('count'),
            self::LABELS  => $dataset->pluck('title')
        ];
    }
}
