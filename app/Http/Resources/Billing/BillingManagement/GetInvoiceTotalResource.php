<?php

namespace App\Http\Resources\Billing\BillingManagement;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Billing\InvoiceSnapshot;
use App\Services\CurrencyService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Collection;

/**
 * @mixin Collection<InvoiceSnapshot>
 */
class GetInvoiceTotalResource extends BaseJsonResource
{
    const string TOTAL = 'total';
    public function __construct($resource)
    {
        parent::__construct($resource);
    }

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     * @throws BindingResolutionException
     */
    public function toArray($request): array
    {
        /** @var Collection $collection */
        $collection = $this->resource;

        $total = $collection->reduce(function (?float $carry, InvoiceSnapshot $invoiceSnapshot) {
            return $carry + $invoiceSnapshot?->total_paid ?? 0.00;
        }, 0);

        return [
            self::TOTAL => app()->make(CurrencyService::class)->formatToCurrency($total / 100),
        ];
    }
}
