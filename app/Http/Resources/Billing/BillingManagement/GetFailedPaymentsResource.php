<?php

namespace App\Http\Resources\Billing\BillingManagement;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Billing\InvoiceSnapshot;
use Illuminate\Http\Client\Request;

/**
 * @mixin InvoiceSnapshot
 */
class GetFailedPaymentsResource extends BaseJsonResource
{
    public function __construct($resource)
    {
        parent::__construct($resource);
    }

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id'         => $this->id,
            'cost'       => $this->total_value / 100,
            'invoice_id' => $this->invoice_id
        ];
    }
}
