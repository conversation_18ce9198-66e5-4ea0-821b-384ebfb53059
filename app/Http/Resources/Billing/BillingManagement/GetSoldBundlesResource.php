<?php

namespace App\Http\Resources\Billing\BillingManagement;

use App\Http\Resources\Odin\BaseJsonResource;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Number;

/**
 * @mixin Collection
 */
class GetSoldBundlesResource extends BaseJsonResource
{
    const string DATASET = 'dataset';
    const string LABELS  = 'labels';
    const string TOTAL   = 'total';

    public function __construct($resource)
    {
        parent::__construct($resource);
    }

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        /** @var Collection $dataset */
        $dataset = $this->resource;

        $initial = $dataset->map(function ($item) {

            return [
                'count' => $item->bundle_count,
                'title' => $item->bundle->name,
            ];
        });

        $datapoints = $initial->pluck('count');

        $labels = $initial->pluck('title');

        $total = $dataset->sum(function ($item) {
            return $item->bundle->cost * $item->bundle_count;
        });

        return [
            self::DATASET => $datapoints,
            self::LABELS  => $labels,
            self::TOTAL   => Number::currency($total)
        ];
    }
}
