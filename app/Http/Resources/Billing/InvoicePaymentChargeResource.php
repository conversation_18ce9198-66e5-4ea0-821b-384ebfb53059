<?php

namespace App\Http\Resources\Billing;

use App\Helpers\CarbonHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Billing\CompanyPaymentMethod;
use App\Models\Billing\InvoicePaymentCharge;
use Illuminate\Http\Client\Request;

/**
 * @mixin InvoicePaymentCharge
 */
class InvoicePaymentChargeResource extends BaseJsonResource
{
    const string FIELD_ID                  = 'id';
    const string FIELD_PAYMENT_METHOD_ID   = 'payment_method_id';
    const string FIELD_STATUS              = 'status';
    const string FIELD_ERROR_MESSAGE       = 'error_message';
    const string FIELD_PAYMENT_METHOD_TYPE = 'payment_method_type';
    const string FIELD_CREATED_AT          = 'created_at';

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        $paymentMethod = CompanyPaymentMethod::withTrashed()
            ->findOrFail($this->payment_method_id);

        return [
            self::FIELD_ID                  => $this->id,
            self::FIELD_PAYMENT_METHOD_ID   => $this->payment_method_id,
            'payment_method_expiry_month'   => $paymentMethod->expiry_month,
            'payment_method_expiry_year'    => $paymentMethod->expiry_year,
            'payment_method_number'         => $paymentMethod->number,
            self::FIELD_STATUS              => $this->status,
            self::FIELD_ERROR_MESSAGE       => $this->error_message,
            self::FIELD_PAYMENT_METHOD_TYPE => $paymentMethod->type,
            self::FIELD_CREATED_AT          => CarbonHelper::parseWithTimezone($this->created_at)->toFormat()
        ];
    }
}
