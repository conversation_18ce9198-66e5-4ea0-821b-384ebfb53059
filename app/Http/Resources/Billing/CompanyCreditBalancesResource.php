<?php

namespace App\Http\Resources\Billing;

use App\Models\Billing\Credit;
use Illuminate\Http\Client\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Credit
 */
class CompanyCreditBalancesResource extends JsonResource
{
    const string BALANCE = 'balance';
    const string NAME    = 'name';

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            self::NAME           => $this?->name ?? $this->credit_type,
            'type'               => $this->credit_type,
            'balance_in_dollars' => +$this->{self::BALANCE} / 100,
            'balance'            => +$this->{self::BALANCE}
        ];
    }
}
