<?php

namespace App\Http\Resources\Billing\InvoiceTemplate;

use App\Enums\Billing\InvoiceTemplateComponent;
use App\Http\Resources\Odin\BaseJsonResource;
use Exception;
use Illuminate\Http\Client\Request;

/**
 * @mixin InvoiceTemplateComponent
 */
class InvoiceTemplateComponentResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     * @throws Exception
     */
    public function toArray($request): array
    {
        return [
            'id'   => $this->value,
            'name' => $this->getName(),
        ];
    }
}
