<?php

namespace App\Http\Resources\Billing\InvoiceTemplate;

use App\Enums\Billing\InvoiceTemplateModelType;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Billing\InvoiceTemplate;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use Exception;
use Illuminate\Http\Client\Request;

/**
 * @mixin InvoiceTemplate
 */
class InvoiceTemplateResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     * @throws Exception
     */
    public function toArray($request): array
    {
        $modelType = $this->{InvoiceTemplate::FIELD_MODEL_TYPE}
            ? InvoiceTemplateModelType::fromClass($this->{InvoiceTemplate::FIELD_MODEL_TYPE})
            : null;

        return [
            'id'              => $this->{InvoiceTemplate::FIELD_ID},
            'name'            => $this->{InvoiceTemplate::FIELD_NAME},
            'is_global'       => $this->{InvoiceTemplate::FIELD_IS_GLOBAL},
            'model_type'      => $modelType,
            'model_type_name' => $modelType?->getName(),
            'model_id'        => $this->{InvoiceTemplate::FIELD_MODEL_ID},
            'model_data'      => $this->getModelData(),
            'props'           => $this->{InvoiceTemplate::FIELD_PROPS},
        ];
    }

    /**
     * @param Industry $industry
     * @return array
     */
    protected function getIndustryModelData(Industry $industry): array
    {
        return [
            'name' => $industry->{Industry::FIELD_NAME}
        ];
    }

    /**
     * @param IndustryService $industryService
     * @return string[]
     */
    protected function getIndustryServiceData(IndustryService $industryService): array
    {
        return [
            'name' => $industryService->{IndustryService::RELATION_INDUSTRY}->{Industry::FIELD_NAME} . " - " . $industryService->{IndustryService::FIELD_NAME}
        ];
    }

    /**
     * @return array
     */
    protected function getModelData(): array
    {
        return match ($this->{InvoiceTemplate::FIELD_MODEL_TYPE}) {
            Industry::class        => $this->getIndustryModelData($this->model),
            IndustryService::class => $this->getIndustryServiceData($this->model),
            default                => []
        };
    }
}
