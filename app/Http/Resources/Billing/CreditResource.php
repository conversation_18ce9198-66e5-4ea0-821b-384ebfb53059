<?php

namespace App\Http\Resources\Billing;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Billing\Credit;
use Carbon\Carbon;
use Carbon\CarbonInterface;
use Illuminate\Http\Client\Request;

/**
 * @mixin Credit
 */
class CreditResource extends BaseJsonResource
{
    const string FIELD_ID               = 'id';
    const string FIELD_COMPANY_ID       = 'company_id';
    const string FIELD_CREDIT_TYPE      = 'credit_type';
    const string FIELD_INITIAL_VALUE    = 'initial_value';
    const string FIELD_REMAINING_VALUE  = 'remaining_value';
    const string FIELD_EXPIRES_AT       = 'expires_at';
    const string FIELD_EXPIRES_AT_HUMAN = 'expires_at_human';
    const string FIELD_IS_EXPIRED       = 'is_expired';
    const string FIELD_BILLING_PROFILES = 'billing_profiles';
    const string FIELD_APPLIED_AT       = 'applied_at';

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            self::FIELD_ID               => $this->{Credit::FIELD_ID},
            self::FIELD_COMPANY_ID       => $this->{Credit::FIELD_COMPANY_ID},
            self::FIELD_CREDIT_TYPE      => $this->{Credit::FIELD_CREDIT_TYPE},
            self::FIELD_INITIAL_VALUE    => $this->{Credit::FIELD_INITIAL_VALUE} / 100,
            self::FIELD_REMAINING_VALUE  => $this->{Credit::FIELD_REMAINING_VALUE} / 100,
            self::FIELD_IS_EXPIRED       => $this->{Credit::FIELD_EXPIRES_AT} && Carbon::now()->gte($this->{Credit::FIELD_EXPIRES_AT}),
            self::FIELD_BILLING_PROFILES => BillingProfileResource::collection($this->billingProfiles),
            self::FIELD_APPLIED_AT       => $this->{Credit::FIELD_CREATED_AT},
            self::FIELD_EXPIRES_AT       => $this->{Credit::FIELD_EXPIRES_AT}
                ? Carbon::parse($this->{Credit::FIELD_EXPIRES_AT})->format('Y-m-d')
                : null,
            self::FIELD_EXPIRES_AT_HUMAN => $this->{Credit::FIELD_EXPIRES_AT}
                ? Carbon::parse($this->{Credit::FIELD_EXPIRES_AT})
                    ->diffForHumans(Carbon::now(), ['syntax' => CarbonInterface::DIFF_ABSOLUTE, 'short' => null, 'parts' => 2])
                : null,
        ];
    }
}
