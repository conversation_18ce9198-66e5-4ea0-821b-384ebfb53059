<?php

namespace App\Http\Resources\Billing;

use Spatie\EventSourcing\StoredEvents\StoredEvent;

/**
 * @mixin StoredEvent
 */
class RequestInvoiceTaxResource extends AuthorableEventResource
{
    const string EVENT_PROPERTY_INVOICE_UUID = "invoiceUuid";

    public function getEventData(): array
    {
        return [
            self::EVENT_PROPERTY_INVOICE_UUID => $this->event_properties[self::EVENT_PROPERTY_INVOICE_UUID],
        ];
    }
}
