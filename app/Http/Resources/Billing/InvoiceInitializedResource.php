<?php

namespace App\Http\Resources\Billing;

use Spatie\EventSourcing\StoredEvents\StoredEvent;

/**
 * @mixin StoredEvent
 */
class InvoiceInitializedResource extends AuthorableEventResource
{
    const string EVENT_PROPERTY_NOTES             = "notes";
    const string EVENT_PROPERTY_DUE_DATE          = "dueDate";
    const string EVENT_PROPERTY_ISSUE_DATE        = "issueDate";
    const string EVENT_PROPERTY_INVOICE_UUID      = "invoiceUuid";
    const string EVENT_PROPERTY_COMPANY_REFERENCE = "companyReference";

    /**
     * Transform the resource into an array.
     *
     * @return array
     */
    public function getEventData(): array
    {
        return [
            self::EVENT_PROPERTY_NOTES             => $this->event_properties[self::EVENT_PROPERTY_NOTES],
            self::EVENT_PROPERTY_DUE_DATE          => $this->event_properties[self::EVENT_PROPERTY_DUE_DATE],
            self::EVENT_PROPERTY_ISSUE_DATE        => $this->event_properties[self::EVENT_PROPERTY_ISSUE_DATE],
            self::EVENT_PROPERTY_INVOICE_UUID      => $this->event_properties[self::EVENT_PROPERTY_INVOICE_UUID],
            self::EVENT_PROPERTY_COMPANY_REFERENCE => $this->event_properties[self::EVENT_PROPERTY_COMPANY_REFERENCE],
        ];
    }
}
