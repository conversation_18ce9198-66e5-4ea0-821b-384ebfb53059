<?php

namespace App\Http\Resources\Billing;

use App\Enums\Billing\InvoiceStates;
use Spatie\EventSourcing\StoredEvents\StoredEvent;

/**
 * @mixin StoredEvent
 */
class InvoiceStatusUpdatedResource extends AuthorableEventResource
{
    const string EVENT_PROPERTY_INVOICE_UUID = "invoiceUuid";
    const string EVENT_PROPERTY_NEW_STATUS   = "newStatus";
    const string EVENT_PROPERTY_OLD_STATUS   = "oldStatus";
    const string STATUS_DETAILS              = 'status_details';
    const string NAME                        = 'name';
    const string OLD_STATUS                  = 'old_status';
    const string NEW_STATUS                  = 'new_status';
    const string ID                          = 'id';

    /**
     * Transform the resource into an array.
     *
     * @return array
     */
    public function getEventData(): array
    {
        $statusDetails = [
            self::OLD_STATUS => [
                self::ID   => $this->event_properties[self::EVENT_PROPERTY_OLD_STATUS],
                self::NAME => InvoiceStates::tryFrom($this->event_properties[self::EVENT_PROPERTY_OLD_STATUS])->getTitle(),
            ],
            self::NEW_STATUS => [
                self::ID   => $this->event_properties[self::EVENT_PROPERTY_NEW_STATUS],
                self::NAME => InvoiceStates::tryFrom($this->event_properties[self::EVENT_PROPERTY_NEW_STATUS])->getTitle(),
            ],
        ];

        return [
            self::EVENT_PROPERTY_INVOICE_UUID => $this->event_properties[self::EVENT_PROPERTY_INVOICE_UUID],
            self::STATUS_DETAILS              => $statusDetails,
        ];
    }
}
