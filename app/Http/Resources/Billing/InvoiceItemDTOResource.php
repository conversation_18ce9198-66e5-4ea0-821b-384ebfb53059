<?php

namespace App\Http\Resources\Billing;

use App\DTO\Billing\InvoiceItemDTO;
use App\Enums\Billing\InvoiceItemTypes;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\User;
use Illuminate\Http\Client\Request;

/**
 * @mixin InvoiceItemDTO
 */
class InvoiceItemDTOResource extends BaseJsonResource
{

    const string FIELD_INVOICE_ID      = 'invoice_id';
    const string FIELD_INVOICE_ITEM_ID = 'invoice_item_id';
    const string FIELD_BILLABLE_ID     = 'billable_id';
    const string FIELD_BILLABLE_TYPE   = 'billable_type';
    const string FIELD_UNIT_PRICE      = 'unit_price';
    const string FIELD_QUANTITY        = 'quantity';
    const string FIELD_DESCRIPTION     = 'description';
    const string FIELD_ADDED_BY        = 'added_by';

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        $addedBy = $this->formatAddedBy();

        return [
            self::FIELD_INVOICE_ID      => $this->getInvoiceId(),
            self::FIELD_INVOICE_ITEM_ID => $this->getInvoiceItemId(),
            self::FIELD_BILLABLE_ID     => $this->getBillableId(),
            self::FIELD_BILLABLE_TYPE   => InvoiceItemTypes::fromClass($this->getBillableType())->value,
            self::FIELD_UNIT_PRICE      => $this->getUnitPrice(),
            'unit_price_in_dollars'     => $this->getUnitPrice() / 100,
            self::FIELD_QUANTITY        => $this->getQuantity(),
            self::FIELD_DESCRIPTION     => $this->getDescription(),
            self::FIELD_ADDED_BY        => $addedBy
        ];
    }

    /**
     * @return array
     */
    protected function formatAddedBy(): array
    {
        if (empty($this->getAddedBy())) {
            return [
                'id'   => null,
                'name' => 'System',
            ];
        }
        $authorName = User::query()->findOrFail($this->getAddedBy())->{User::FIELD_NAME};

        return [
            'id'   => $this->getAddedBy(),
            'name' => $authorName,
        ];
    }
}
