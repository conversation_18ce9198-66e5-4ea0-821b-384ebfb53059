<?php

namespace App\Http\Resources\Billing;

use App\BillingWorkflows\Activities\BaseActivity;
use App\Enums\Billing\BillingPolicyActionType;
use App\Enums\Billing\BillingPolicyEventType;
use App\Events\Billing\BillingWorkflowEventContract;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Billing\BillingProfilePolicy;
use Illuminate\Http\Client\Request;

class BillingProfilePolicyResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     * @throws \Exception
     */
    public function toArray($request): array
    {
        $eventType = BillingPolicyEventType::tryFrom($this['event_slug']);

        /** @var BillingWorkflowEventContract $eventClass */
        $eventClass = $eventType->getClass();

        return [
            'event_slug'        => $eventType->value,
            'event_name'        => $eventClass::getName(),
            'event_description' => $eventClass::getDescription(),
            'count_actions'     => count($this['actions']),
            'actions'           => $this['actions']->map(function (BillingProfilePolicy $action) use ($eventType) {
                $actionType = BillingPolicyActionType::fromClass($action[BillingProfilePolicy::FIELD_ACTION_CLASS]);

                /** @var BaseActivity $eventClass */
                $eventClass = $actionType->getClass();

                return [
                    'id'                 => $action->{BillingProfilePolicy::FIELD_ID},
                    'event_slug'         => $eventType->value,
                    'action_name'        => $eventClass::getName(),
                    'action_data'        => empty($action->{BillingProfilePolicy::FIELD_ACTION_DATA})
                        ? null
                        : $action->{BillingProfilePolicy::FIELD_ACTION_DATA},
                    'action_slug'        => $actionType->value,
                    'action_description' => $eventClass::getDescription(),
                ];
            }),
        ];
    }
}
