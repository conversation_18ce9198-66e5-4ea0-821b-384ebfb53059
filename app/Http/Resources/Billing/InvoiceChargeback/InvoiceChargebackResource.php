<?php

namespace App\Http\Resources\Billing\InvoiceChargeback;

use App\Helpers\CarbonHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceDispute;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Number;

/**
 * @mixin InvoiceDispute
 */
class InvoiceChargebackResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id'         => $this->id,
            'company'    => [
                'name' => $this->{InvoiceDispute::RELATION_INVOICE}?->{Invoice::RELATION_COMPANY}?->name,
                'id'   => $this->{InvoiceDispute::RELATION_INVOICE}?->{Invoice::RELATION_COMPANY}?->id
            ],
            'invoice_id' => $this->invoice_id,
            'reason'     => $this->reason,
            'status'     => $this->status,
            'amount'     => Number::currency($this->amount / 100),
            'created_at' => CarbonHelper::parseWithTimezone($this->created_at)->toFormat(),
            'updated_at' => CarbonHelper::parseWithTimezone($this->updated_at)->toFormat(),
        ];
    }
}
