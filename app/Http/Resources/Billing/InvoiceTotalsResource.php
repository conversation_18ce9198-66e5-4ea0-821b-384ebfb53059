<?php

namespace App\Http\Resources\Billing;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Billing\InvoiceSnapshot;
use Illuminate\Http\Client\Request;

/**
 * @mixin InvoiceSnapshot
 */
class InvoiceTotalsResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'total_issued'          => (float)$this->{InvoiceSnapshot::FIELD_TOTAL_VALUE} - $this->{InvoiceSnapshot::FIELD_TOTAL_CREDIT_APPLIED},
            'total'                 => (float)$this->{InvoiceSnapshot::FIELD_TOTAL_VALUE},
            'outstanding'           => (float)$this->{InvoiceSnapshot::FIELD_TOTAL_OUTSTANDING},
            'refunded'              => (float)$this->{InvoiceSnapshot::FIELD_TOTAL_REFUNDED},
            'paid'                  => (float)$this->{InvoiceSnapshot::FIELD_TOTAL_PAID},
            'collections'           => (float)$this->{InvoiceSnapshot::FIELD_TOTAL_COLLECTIONS},
            'collections_recovered' => (float)$this->{InvoiceSnapshot::FIELD_TOTAL_COLLECTIONS_RECOVERED},
            'collections_lost'      => (float)$this->{InvoiceSnapshot::FIELD_TOTAL_COLLECTIONS_LOST},
            'credits_applied'       => (float)$this->{InvoiceSnapshot::FIELD_TOTAL_CREDIT_APPLIED},
            'chargeback'            => (float)$this->{InvoiceSnapshot::FIELD_TOTAL_CHARGEBACK},
            'chargeback_recovered'  => (float)$this->{InvoiceSnapshot::FIELD_TOTAL_CHARGEBACK_WON},
            'chargeback_lost'       => (float)$this->{InvoiceSnapshot::FIELD_TOTAL_CHARGEBACK_LOST},
            'written_off'           => (float)$this->{InvoiceSnapshot::FIELD_TOTAL_WRITTEN_OFF},
        ];
    }
}
