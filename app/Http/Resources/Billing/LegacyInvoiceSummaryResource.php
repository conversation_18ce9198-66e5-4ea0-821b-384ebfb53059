<?php

namespace App\Http\Resources\Billing;

use App\Http\Resources\Odin\BaseJsonResource;
use Illuminate\Http\Request;

/**
 * @mixin array
 */
class LegacyInvoiceSummaryResource extends BaseJsonResource
{

    const string LEGACY_TOTAL               = 'total';
    const string LEGACY_PAID                = 'paid';
    const string LEGACY_UNPAID              = 'unpaid';
    const string LEGACY_TO_BE_INVOICED      = 'tobeinvoiced';
    const string LEGACY_NON_REJECTABLE      = 'nonrejectable';
    const string LEGACY_AVAILABLE_CREDIT    = 'available_credit';
    const string LEGACY_CREDIT_AMOUNT       = 'credit_amount';
    const string LEGACY_VOUCHER_AMOUNT      = 'voucher_amount';
    const string LEGACY_SIGNUP_BONUS_AMOUNT = 'signup_bonus_amount';

    public function toArray(Request $request): array
    {
        return [
            self::LEGACY_TOTAL               => $this['total'],
            self::LEGACY_PAID                => $this['paid'],
            self::LEGACY_UNPAID              => $this['unpaid'],
            self::LEGACY_TO_BE_INVOICED      => $this['tobeinvoiced'],
            self::LEGACY_NON_REJECTABLE      => $this['nonrejectable'],
            self::LEGACY_AVAILABLE_CREDIT    => $this['available_credit'],
            self::LEGACY_CREDIT_AMOUNT       => $this['credit_amount'],
            self::LEGACY_VOUCHER_AMOUNT      => $this['voucher_amount'],
            self::LEGACY_SIGNUP_BONUS_AMOUNT => $this['signup_bonus_amount'],
        ];
    }
}