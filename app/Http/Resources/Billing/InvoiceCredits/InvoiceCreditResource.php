<?php

namespace App\Http\Resources\Billing\InvoiceCredits;

use App\Helpers\CarbonHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Billing\InvoiceCredit;
use Illuminate\Http\Client\Request;

/**
 * @mixin InvoiceCredit
 */
class InvoiceCreditResource extends BaseJsonResource
{
    const string FIELD_ID             = 'id';
    const string FIELD_INVOICE_ID     = 'invoice_id';
    const string FIELD_CREDIT_ID      = 'credit_id';
    const string FIELD_AMOUNT_APPLIED = 'amount_applied';
    const string FIELD_APPLIED_AT     = 'applied_at';
    const string FIELD_CREATED_AT     = 'created_at';
    const string FIELD_UPDATED_AT     = 'updated_at';

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            self::FIELD_ID             => $this->id,
            self::FIELD_INVOICE_ID     => $this->invoice_id,
            'credit_type'              => $this->credit->credit_type,
            self::FIELD_AMOUNT_APPLIED => $this->amount_applied,
            self::FIELD_APPLIED_AT     => CarbonHelper::parseWithTimezone($this->applied_at)->toFormat(),
            'author'                   => $this->getAuthor()
        ];
    }

    /**
     * @return array
     */
    public function getAuthor(): array
    {
        // TODO - Improve and reuse in all places where we have author.
        $name = $this->{InvoiceCredit::FIELD_AUTHOR_ID}
            ? $this->{InvoiceCredit::FIELD_AUTHOR_TYPE}
                ->getClass()::withTrashed()
                ->find($this->{InvoiceCredit::FIELD_AUTHOR_ID})
                ?->name
            : ucfirst($this->{InvoiceCredit::FIELD_AUTHOR_TYPE}->value);

        return [
            'id'   => $this->{InvoiceCredit::FIELD_AUTHOR_ID} ?? $this->{InvoiceCredit::FIELD_AUTHOR_TYPE},
            'type' => $this->{InvoiceCredit::FIELD_AUTHOR_TYPE},
            'name' => $name
        ];
    }
}
