<?php

namespace App\Http\Resources\Billing;

use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Helpers\CarbonHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Billing\InvoicePayment;
use App\Models\Odin\CompanyUser;
use App\Models\User;

/**
 * @mixin InvoicePayment
 */
class InvoicePaymentResource extends BaseJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        $charges = InvoicePaymentChargeResource::collection($this->{InvoicePayment::RELATION_CHARGES})
            ->toArray($request);

        return [
            'id'                   => $this->{InvoicePayment::FIELD_ID},
            'amount'               => $this->{InvoicePayment::FIELD_TOTAL},
            'status'               => $this->{InvoicePayment::FIELD_STATUS},
            'author'               => $this->getAuthorData(
                type: $this->{InvoicePayment::FIELD_AUTHOR_TYPE},
                id  : $this->{InvoicePayment::FIELD_AUTHOR_ID},
            ),
            'updated_at'           => $this->{InvoicePayment::FIELD_UPDATED_AT} ? CarbonHelper::parseWithTimezone($this->{InvoicePayment::FIELD_UPDATED_AT})->toFormat() : null,
            'requested_at'         => $this->{InvoicePayment::FIELD_REQUESTED_AT} ? CarbonHelper::parseWithTimezone($this->{InvoicePayment::FIELD_REQUESTED_AT})->toFormat() : null,
            'next_attempt_at'      => $this->{InvoicePayment::FIELD_NEXT_ATTEMPT_AT} ? CarbonHelper::parseWithTimezone($this->{InvoicePayment::FIELD_NEXT_ATTEMPT_AT})->toFormat() : null,
            'attempt_number'       => $this->{InvoicePayment::FIELD_ATTEMPT_NUMBER},
            'charged_at'           => $this->{InvoicePayment::FIELD_CHARGED_AT} ? CarbonHelper::parseWithTimezone($this->{InvoicePayment::FIELD_CHARGED_AT})->toFormat() : null,
            'max_attempts'         => $this->{InvoicePayment::FIELD_MAX_ATTEMPTS_PER_PAYMENT_METHOD},
            'payment_method_types' => collect($charges)->pluck(InvoicePaymentChargeResource::FIELD_PAYMENT_METHOD_TYPE)->unique(),
            'charges'              => $charges,
            'error_message'        => $this->error_message
        ];
    }

    /**
     * @param string $type
     * @param int|null $id
     * @return array
     */
    protected function getAuthorData(
        string $type,
        ?int $id = null,
    ): array
    {
        $authorDetails = [
            'id'   => $id ?? 'N/A',
            'type' => $type,
            'name' => 'System'
        ];

        if ($type === InvoiceEventAuthorTypes::USER->value && $id) {
            $authorDetails['name'] = User::withTrashed()->find($id)?->{User::FIELD_NAME};
        } elseif ($type === InvoiceEventAuthorTypes::COMPANY_USER->value && $id) {
            $authorDetails['name'] = CompanyUser::withTrashed()->find($id)?->completeName();
        }

        return $authorDetails;
    }
}
