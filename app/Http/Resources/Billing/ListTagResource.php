<?php

namespace App\Http\Resources\Billing;

use App\Http\Resources\Odin\ListOptionResource;
use Illuminate\Support\Str;
use Spatie\Tags\Tag;

/**
 * @mixin Tag
 */
class ListTagResource extends ListOptionResource
{
    const string FIELD_ID   = 'id';
    const string FIELD_NAME = 'name';

    public function getId(): int|string
    {
        return $this->slug;
    }

    public function getName(): string
    {
        return Str::headline($this->name);
    }
}
