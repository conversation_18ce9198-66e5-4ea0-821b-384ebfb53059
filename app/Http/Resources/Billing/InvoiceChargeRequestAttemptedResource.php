<?php

namespace App\Http\Resources\Billing;

use App\Enums\Billing\InvoiceStates;
use Illuminate\Support\Arr;
use Spatie\EventSourcing\StoredEvents\StoredEvent;

/**
 * @mixin StoredEvent
 */
class InvoiceChargeRequestAttemptedResource extends AuthorableEventResource
{
    const string EVENT_PROPERTY_INVOICE_UUID = "invoiceUuid";

    /**
     * Transform the resource into an array.
     *
     * @return array
     */
    public function getEventData(): array
    {

        return [
            self::EVENT_PROPERTY_INVOICE_UUID => Arr::get($this->event_properties, self::EVENT_PROPERTY_INVOICE_UUID),
        ];
    }
}
