<?php

namespace App\Http\Resources\Billing;

use App\Models\Billing\BillingProfile;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Billing\CompanyPaymentMethod;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\CompanyUser;
use App\Services\CurrencyService;
use Carbon\Carbon;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Collection;

/**
 * @mixin BillingProfile
 */
class BillingProfileResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id'                    => $this->id,
            'name'                  => $this->name,
            'company'               => [
                'name' => $this->company->name,
                'id'   => $this->company->id
            ],
            'contact'               => [
                'id'   => $this->{BillingProfile::RELATION_CONTACT}?->{CompanyUser::FIELD_ID},
                'name' => $this->{BillingProfile::RELATION_CONTACT}?->completeName(),
            ],
            'threshold'             => $this->threshold_in_dollars,
            'frequency_type'        => $this->billing_frequency_cron,
            'frequency_data'        => $this->cron_data,
            'charge_attempts'       => $this->max_allowed_charge_attempts,
            'process_auto'          => $this->process_auto,
            'payment_method'        => $this->payment_method,
            'default'               => $this->default,
            'payment_method_id'     => $this->payment_method_id,
            'payment_method_data'   => [
                'number' => $this->paymentMethod?->{CompanyPaymentMethod::FIELD_NUMBER},
                'expiry' => $this->paymentMethod?->getFormattedExpiry(),
            ],
            'campaigns'             => $this->getCampaigns(),
            'due_in_days'           => $this->due_in_days,
            'invoice_template_id'   => $this->invoice_template_id,
            'invoice_template_name' => $this->invoiceTemplate?->name,
            'archived_at'           => $this->archived_at ? Carbon::parse($this->archived_at)->toIso8601String() : null,
            'archived_by_name'      => $this->archivedBy?->name,
            'archived_by_id'        => $this->archivedBy?->id,
            'total_credits_outstanding' => CurrencyService::fromAtomicToCurrency($this->getTotalOutstanding())
        ];
    }

    /**
     * @return Collection
     */
    public function getCampaigns(): Collection
    {
        return $this->campaigns->map(fn(CompanyCampaign $campaign) => [
            'id'   => $campaign->{CompanyCampaign::FIELD_ID},
            'name' => $campaign->{CompanyCampaign::FIELD_NAME},
        ]);
    }
}
