<?php

namespace App\Http\Resources\Billing\Reports;

use App\Helpers\CarbonHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\InvoiceCredit;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Number;

/**
 * @mixin InvoiceCredit
 */
class CompaniesOverviewReportResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'total_unrejectable'   => Number::currency($this->total_unrejectable),
            'total_to_be_invoiced' => Number::currency($this->total_to_be_invoiced),
            'max_delivered_at'     => CarbonHelper::parse($this->max_delivered_at)->toFormat(),
            'min_delivered_at'     => CarbonHelper::parse($this->min_delivered_at)->toFormat(),
            'company_id'           => $this->company_id,
            'company_name'         => $this->company_name,
            'billing_profiles'     => $this->billing_profiles?->map(function (BillingProfile $profile) {
                return [
                    'payment_method'       => $profile->{BillingProfile::FIELD_PAYMENT_METHOD},
                    'frequency_data'       => $profile->{BillingProfile::FIELD_CRON_DATA},
                    'frequency_type'       => $profile->{BillingProfile::FIELD_BILLING_FREQUENCY_CRON},
                    'threshold_in_dollars' => Number::currency($profile->{BillingProfile::FIELD_THRESHOLD_IN_DOLLARS}),
                    'last_billed_at'       => $profile->{BillingProfile::FIELD_LAST_BILLED_AT},
                ];
            })
        ];
    }
}
