<?php

namespace App\Http\Resources\Billing\Reports;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Billing\InvoiceSnapshot;
use Illuminate\Support\Number;

/**
 * @mixin InvoiceSnapshot
 */
class InvoiceBalanceReportResource extends BaseJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            "type"                        => $request->get('grouped_by', 'invoice'),
            "total_value"                 => Number::currency(($this->total_value ?? 0) / 100),
            "total_outstanding"           => Number::currency(($this->total_outstanding ?? 0) / 100),
            "total_refunded"              => Number::currency(($this->total_refunded ?? 0) / 100),
            "total_lost"                  => Number::currency(($this->total_lost ?? 0) / 100),
            "total_paid"                  => Number::currency(($this->total_paid ?? 0) / 100),
            "total_collections"           => Number::currency(($this->total_collections ?? 0) / 100),
            "total_collections_recovered" => Number::currency(($this->total_collections_recovered ?? 0) / 100),
            "total_collections_lost"      => Number::currency(($this->total_collections_lost ?? 0) / 100),
            "total_credits_applied"       => Number::currency(($this->total_credit_applied ?? 0) / 100),
            "total_written_off"           => Number::currency(($this->total_written_off ?? 0) / 100),
            "invoice_id"                  => $this->invoice_id,
            "company_id"                  => $this->company_id,
            "company_name"                => $this->company_name,
        ];
    }
}
