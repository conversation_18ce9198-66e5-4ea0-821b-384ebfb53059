<?php

namespace App\Http\Resources\Billing\Reports;

use App\Helpers\CarbonHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\Credit;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Number;

/**
 * @mixin Credit
 */
class CreditOutstandingReportResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'billing_profile_ids'    => $this->{Credit::RELATION_BILLING_PROFILES}->pluck(BillingProfile::FIELD_ID),
            'remaining_value'        => Number::currency($this->remaining_value / 100),
            'applied_at'             => CarbonHelper::parse($this->created_at)->toFormat(),
            'company'                => [
                'id'   => $this->company_id,
                'name' => $this->company_name,
            ],
            'is_expired'             => $this->expires_at && now()->greaterThan($this->expires_at),
            'credit_type'            => $this->credit_type,
            'last_lead_delivered_at' => $this->last_lead_delivered_at ? CarbonHelper::parse($this->last_lead_delivered_at)->toFormat() : null,
            'expires_at'             => $this->expires_at ? CarbonHelper::parse($this->expires_at)->toFormat() : 'No Expiry'
        ];
    }
}
