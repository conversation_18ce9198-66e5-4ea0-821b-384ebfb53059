<?php

namespace App\Http\Resources\Billing\Reports;

use App\Enums\Billing\InvoiceTransactionScenario;
use App\Enums\Billing\InvoiceTransactionScope;
use App\Enums\Billing\InvoiceTransactionType;
use App\Helpers\CarbonHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Services\CurrencyService;
use Illuminate\Http\Request;
use Illuminate\Support\Str;


class RevenueReportResource extends BaseJsonResource
{
    const string FIELD_INVOICE_ID = 'invoice_id';
    const string FIELD_AMOUNT     = 'amount';
    const string FIELD_STATUS     = 'status';
    const string FIELD_TYPE       = 'type';
    const string FIELD_SCENARIO   = 'scenario';
    const string FIELD_SCOPE      = 'scope';
    const string FIELD_COMPANY    = 'company';
    const string FIELD_CREATED_AT = 'created_at';

    /**
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        $consolidatedTitle = collect([
            InvoiceTransactionScope::tryFrom($this->scope)?->getTitle() ?? $this->scope,
            InvoiceTransactionType::tryFrom($this->type)?->getTitle() ?? $this->type,
            InvoiceTransactionScenario::tryFrom($this->scenario)?->getTitle() ?? $this->scenario,
        ])->filter()->map(fn($item) => Str::headline($item))->join(' ');

        return [
            self::FIELD_INVOICE_ID => $this->invoice_id,
            self::FIELD_AMOUNT     => CurrencyService::fromAtomicToCurrency($this->amount),
            self::FIELD_STATUS     => $this->invoice_status,
            self::FIELD_TYPE       => $consolidatedTitle,
            self::FIELD_SCENARIO   => $this->scenario,
            self::FIELD_SCOPE      => $this->scope,
            self::FIELD_COMPANY    => [
                'name' => $this->company_name,
                'id'   => $this->company_id,
            ],
            self::FIELD_CREATED_AT => CarbonHelper::parseWithTimezone($this->date)->toFormat()
        ];
    }

}
