<?php

namespace App\Http\Resources\Billing\Reports;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceSnapshot;
use App\Models\Odin\Company;
use Illuminate\Support\Number;

/**
 * @mixin InvoiceSnapshot
 */
class AgedInvoicesReportResource extends BaseJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        $groupedBy = $request->get('grouped_by', 'invoice');

        return [
            "type"         => $groupedBy,
            "0_15"         => $this->{"0_15"} ? Number::currency($this->{"0_15"} / 100) : null,
            "16_30"        => $this->{"16_30"} ? Number::currency($this->{"16_30"} / 100) : null,
            "31_60"        => $this->{"31_60"} ? Number::currency($this->{"31_60"} / 100) : null,
            "61_90"        => $this->{"61_90"} ? Number::currency($this->{"61_90"} / 100) : null,
            "90_plus"      => $this->{"90_plus"} ? Number::currency($this->{"90_plus"} / 100) : null,
            "total_issued" => Number::currency($this->total_issued / 100),
            "invoice_id"   => $this->invoice_id,
            "company_id"   => $this->company_id,
            "company_name" => $this->company_name,
        ];
    }
}
