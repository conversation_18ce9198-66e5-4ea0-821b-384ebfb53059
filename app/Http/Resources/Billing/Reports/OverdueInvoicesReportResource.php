<?php

namespace App\Http\Resources\Billing\Reports;

use App\Helpers\CarbonHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Billing\Invoice;
use App\Services\CurrencyService;
use Illuminate\Http\Client\Request;

/**
 * @mixin Invoice $invoice
 */
class OverdueInvoicesReportResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'billing_profile_id'               => $this->billing_profile_id,
            'billing_profile_due_in_days'      => $this->billing_profile_due_in_days,
            'issued_count'                     => $this->issued_count,
            'company_id'                       => $this->company_id,
            'company_name'                     => $this->company_name,
            'invoice_id'                       => $this->invoice_id,
            'invoice_status'                   => $this->invoice_status,
            'items_total'                      => CurrencyService::fromAtomicToCurrency($this->total_value ?? 0),
            'outstanding_total'                => CurrencyService::fromAtomicToCurrency($this->total_outstanding ?? 0),
            'issue_date'                       => CarbonHelper::parseWithTimezone($this->issue_at)->toFormat(CarbonHelper::FORMAT_DATE, CarbonHelper::TIMEZONE_DENVER),
            'due_date'                         => CarbonHelper::parseWithTimezone($this->due_at)->toFormat(CarbonHelper::FORMAT_DATE, CarbonHelper::TIMEZONE_DENVER),
            'pay_date'                         => $this->pay_date ? CarbonHelper::parseWithTimezone($this->pay_date)->toFormat(CarbonHelper::FORMAT_DATE, CarbonHelper::TIMEZONE_DENVER) : null,
            'days_to_pay'                      => $this->days_to_pay,
            'payment_method'                   => $this->payment_method,
            'due_count'                        => $this->due_count,
            'total_due'                        => CurrencyService::fromAtomicToCurrency($this->total_due ?? 0),
            'avg_days_to_pay'                  => round($this->avg_days_to_pay, 3),
            'unpaid_count'                     => $this->unpaid_count,
        ];
    }
}
