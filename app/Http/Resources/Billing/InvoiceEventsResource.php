<?php

namespace App\Http\Resources\Billing;

use App\Enums\Billing\InvoiceEvents;
use App\Helpers\CarbonHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use Carbon\CarbonInterface;
use Illuminate\Http\Client\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use Spatie\EventSourcing\StoredEvents\StoredEvent;

/**
 * @mixin StoredEvent
 */
class InvoiceEventsResource extends BaseJsonResource
{
    const string ID         = 'id';
    const string TITLE      = 'title';
    const string EVENT_TYPE = 'event_type';
    const string CREATED_AT = 'created_at';
    const string PAYLOAD    = 'payload';
    const string DAYS_AGO   = 'days_ago';

    const array EVENTS_TO_GROUP = [];

    const array EVENTS_TO_HIDE = [
        InvoiceEvents::INVOICE_SNAPSHOT_CREATED->value,
        InvoiceEvents::INVOICE_INITIALIZED->value,
        InvoiceEvents::REQUEST_INVOICE_TAX->value,
        InvoiceEvents::INVOICE_CHARGE_FAILED->value,
        InvoiceEvents::INVOICE_CHARGE_REQUEST->value,
    ];

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        $event = InvoiceEvents::fromClass($this->event_class);
        $parsedCreatedAt = CarbonHelper::parseWithTimezone($this->created_at);
        // Use the event file name as title if we didn't register it
        $placeholder = Str::of($this->event_class)->explode('\\')->last();

        return [
            self::ID         => $this->id,
            self::TITLE      => $event?->getTitle() ?? $placeholder,
            self::EVENT_TYPE => $event?->value ?? $this->event_class,
            self::CREATED_AT => $parsedCreatedAt->format('Y-m-d'),
            self::DAYS_AGO   => $parsedCreatedAt->diffForHumans(Carbon::now(), ['syntax' => CarbonInterface::DIFF_RELATIVE_TO_NOW]),
            self::PAYLOAD    => InvoiceEventsResourceFactory::make($this->resource) ?? [],
        ];
    }

    /**
     * @return AnonymousResourceCollection
     */
    public function groupCollection(): AnonymousResourceCollection
    {
        $parsed = parent::collection($this->resource)->toArray(request());

        $filteredEvents = array_filter($parsed, function (array $event) {
            return !in_array($event[self::EVENT_TYPE], self::EVENTS_TO_HIDE);
        });

        return $this->groupEventsByType($filteredEvents);
    }

    /**
     * @param array $events
     * @return AnonymousResourceCollection
     */
    public function groupEventsByType(array $events): AnonymousResourceCollection
    {
        $groupedEvents = [];
        $currentGroup = null;

        foreach ($events as $event) {
            $isTheFirstEvent = $currentGroup === null;
            $isDifferentFromThePrevious = $currentGroup && $currentGroup['group'] !== $event[self::EVENT_TYPE];
            $shouldGroup = in_array($event[self::EVENT_TYPE], self::EVENTS_TO_GROUP);

            if ($isTheFirstEvent || $isDifferentFromThePrevious || !$shouldGroup) {
                if ($currentGroup !== null) {
                    $groupedEvents[] = $currentGroup;
                }
                $currentGroup = [
                    ...$event,
                    'group' => $event[self::EVENT_TYPE],
                    'total' => 0,
                    'all'   => []
                ];
            }
            $currentGroup['all'][] = $event;
            $currentGroup['total']++;
        }

        if ($currentGroup !== null) {
            $groupedEvents[] = $currentGroup;
        }

        // Create and return the AnonymousResourceCollection
        return new AnonymousResourceCollection($groupedEvents, JsonResource::class);
    }
}
