<?php

namespace App\Http\Resources\Billing;

use App\Helpers\CarbonHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Billing\ActionApproval;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\Invoice;
use App\Models\Odin\Company;
use Exception;
use Illuminate\Http\Client\Request;

/**
 * @mixin ActionApproval
 */
class ActionApprovalResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     * @throws Exception
     */
    public function toArray($request): array
    {
        $company = $this->getCompany();

        $companyData = [
            'name' => $company?->name,
            'id'   => $company?->id,
        ];

        $requestedBy = $this->requestedBy ? [
            'id'   => $this->requestedBy->id,
            'name' => $this->requestedBy->name,
        ] : null;

        $reviewedBy = $this->reviewedBy ? [
            'id'   => $this->reviewedBy->id,
            'name' => $this->reviewedBy->name,
        ] : null;

        return [
            'company'               => $companyData,
            'id'                    => $this->{ActionApproval::FIELD_ID},
            'related_id'            => $this->{ActionApproval::FIELD_APPROVABLE_ID},
            'related_type'          => $this->{ActionApproval::FIELD_APPROVABLE_TYPE},
            'requested_by'          => $requestedBy,
            'requested_action'      => $this->requested_action->getTitle(),
            'requested_action_slug' => $this->requested_action->value,
            'payload'               => $this->payload,
            'reviewed_by'           => $reviewedBy,
            'reviewed_at'           => $this->reviewed_at ? CarbonHelper::parseWithTimezone($this->reviewed_at)->format('M d, Y h:i A') : null,
            'status'                => $this->status->value,
            'reason'                => $this->{ActionApproval::FIELD_REASON},
            'note'                  => $this->{ActionApproval::FIELD_NOTE},
            'requested_at'          => CarbonHelper::parseWithTimezone($this->{ActionApproval::FIELD_CREATED_AT})->format('M d, Y h:i A'),
        ];
    }

    /**
     * @return Company|null
     */
    protected function getCompany(): ?Company
    {
        return match ($this->approvable_type) {
            Invoice::class        => Invoice::query()->find($this->approvable_id)?->{Invoice::RELATION_COMPANY},
            BillingProfile::class => BillingProfile::query()->find($this->approvable_id)?->{Invoice::RELATION_COMPANY},
            Company::class        => Company::query()->find($this->approvable_id),
            default               => null
        };
    }
}
