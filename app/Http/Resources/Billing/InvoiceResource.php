<?php

namespace App\Http\Resources\Billing;

use App\DTO\Billing\InvoiceItemDTO;
use App\Enums\Billing\InvoicePaymentChargeStatus;
use App\Enums\Billing\InvoicePaymentStatus;
use App\Enums\Billing\InvoiceStates;
use App\Enums\Billing\InvoiceTransactionType;
use App\Helpers\CarbonHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Http\Resources\TagResource;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceItem;
use App\Models\Billing\InvoicePayment;
use App\Models\Billing\InvoicePaymentCharge;
use App\Models\Billing\InvoiceSnapshot;
use App\Models\Billing\InvoiceTransaction;
use App\Models\Odin\Company;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

/**
 * @mixin Invoice
 */
class InvoiceResource extends BaseJsonResource
{

    const string ID                  = 'id';
    const string UUID                = 'uuid';
    const string STATUS              = 'status';
    const string ISSUE_AT            = 'issue_at';
    const string DUE_AT              = 'due_at';
    const string AMOUNT              = 'amount';
    const string PDF                 = 'pdf';
    const string NOTES               = 'notes';
    const string INVOICE_ITEMS       = 'invoice_items';
    const string COMPANY_ID          = 'company_id';
    const string COMPANY_NAME        = 'company_name';
    const string CURRENT_STATUS_DATA = 'status_data';
    const string STATUS_TITLE        = 'title';
    const string STATUS_ID           = 'id';
    const string STATUS_ACTIONS      = 'actions';
    const string TAGS                = 'tags';
    const string HAS_PENDING_ACTION  = 'has_pending_action';

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        $invoiceItemDTOs = $this->parseInvoiceItemsToDto();

        $isProcessingPayment = $this->isProcessingPayment();

        $possibleStateTransitionsFormatted = $this->formatPossibleStateTransitions($isProcessingPayment);

        $lastSnapshot = $this->lastSnapshot();

        $billingProfile = BillingProfile::withTrashed()
            ->where(BillingProfile::FIELD_ID, $this->{Invoice::FIELD_BILLING_PROFILE_ID})
            ->first();

        return [
            self::HAS_PENDING_ACTION  => $this->hasPendingAction(),
            self::ID                  => $this->id,
            self::COMPANY_ID          => $this->{Invoice::FIELD_COMPANY_ID},
            self::COMPANY_NAME        => $this->{Invoice::RELATION_COMPANY}->{Company::FIELD_NAME},
            self::UUID                => $this->uuid,
            "billing_profile"         => $billingProfile,
            self::CURRENT_STATUS_DATA => [
                self::STATUS_TITLE   => $this->status->getTitle(),
                self::STATUS_ID      => $this->status,
                self::STATUS_ACTIONS => $possibleStateTransitionsFormatted
            ],
            'payment_fail_count'      => $this->getPaymentFailCount(),
            'transaction_statuses'    => $this->getInvoiceTransactionStatuses($lastSnapshot),
            self::ISSUE_AT            => CarbonHelper::parseWithTimezone($this->issue_at),
            self::DUE_AT              => CarbonHelper::parseWithTimezone($this->due_at),
            'has_pdf'                 => filled($this->invoice_url),
            self::NOTES               => $this->notes,
            self::INVOICE_ITEMS       => InvoiceItemDTOResource::collection($invoiceItemDTOs),
            self::TAGS                => TagResource::collection($this->tags),
            'totals'                  => new InvoiceTotalsResource($lastSnapshot),
            'collections'             => new InvoiceCollectionsResource($this->lastCollections()),
            'write_offs'              => new InvoiceWriteOffsResource($this->lastWriteOffs()),
            'credits_applied'         => $this->getTotalCreditsApplied(),
            'items_total'             => $this->getInvoiceItemsTotal(),
            'is_processing_payment'   => $isProcessingPayment,
            'locked'                  => $isProcessingPayment
        ];
    }

    /**
     * @return string|null
     */
    public function getPaymentFailCount(): ?string
    {
        $invoicePayment = $this->payments()
            ->whereIn(InvoicePayment::FIELD_STATUS, [
                InvoicePaymentStatus::RESCHEDULED,
                InvoicePaymentStatus::PENDING,
            ])
            ->whereHas(InvoicePayment::RELATION_CHARGES, function ($query) {
                $query->where(InvoicePaymentCharge::FIELD_STATUS, InvoicePaymentChargeStatus::FAILED);
            })
            ->latest()
            ->first();

        if (!$invoicePayment) return null;

        return $invoicePayment->{InvoicePayment::FIELD_ATTEMPT_NUMBER} . '/' . $invoicePayment->{InvoicePayment::FIELD_MAX_ATTEMPTS_PER_PAYMENT_METHOD};
    }

    /**
     * @return array
     */
    public function getRelevantTransactionTypes(): array
    {
        return match ($this->status->status()) {
            InvoiceStates::COLLECTION->value => [
                InvoiceTransactionType::COLLECTIONS
            ],
            InvoiceStates::CHARGEBACK->value => [
                InvoiceTransactionType::DISPUTE
            ],
            InvoiceStates::ISSUED->value     => [
                InvoiceTransactionType::PAYMENT->value,
                InvoiceTransactionType::REFUND->value,
            ],
            InvoiceStates::PAID->value       => [
                InvoiceTransactionType::REFUND->value,
            ],
            default                          => []
        };
    }

    /**
     * @param InvoiceSnapshot|null $invoiceSnapshot
     * @return Collection
     */
    protected function getInvoiceTransactionStatuses(?InvoiceSnapshot $invoiceSnapshot = null): Collection
    {
        if (empty($invoiceSnapshot)) {
            return collect();
        }

        $relevantTransactionTypes = $this->getRelevantTransactionTypes();

        return $this->transactions()
            ->select([
                InvoiceTransaction::FIELD_SCOPE,
                InvoiceTransaction::FIELD_SCENARIO,
                InvoiceTransaction::FIELD_TYPE,
            ])
            ->whereIn(InvoiceTransaction::FIELD_TYPE, $relevantTransactionTypes)
            ->groupBy([
                InvoiceTransaction::FIELD_SCOPE,
                InvoiceTransaction::FIELD_SCENARIO,
                InvoiceTransaction::FIELD_TYPE,
            ])->get()->map(fn(InvoiceTransaction $invoiceTransaction) => [
                'type'               => $invoiceTransaction->{InvoiceTransaction::FIELD_TYPE},
                'scope'              => $invoiceTransaction->{InvoiceTransaction::FIELD_SCOPE},
                'scenario'           => $invoiceTransaction->{InvoiceTransaction::FIELD_SCENARIO},
                'consolidated_title' => collect([
                    $invoiceTransaction->scope?->getTitle(),
                    $invoiceTransaction->type->getTitle(),
                    $invoiceTransaction->scenario?->getTitle(),
                ])->filter()->join(' '),
            ]);
    }

    /**
     * @param bool $isProcessingPayment
     * @return Collection
     */
    protected function formatPossibleStateTransitions(
        bool $isProcessingPayment
    ): Collection
    {
        return collect($this->status->transitionableStates())
            ->filter(function ($transition) use ($isProcessingPayment) {
                return !in_array($transition, [
                    InvoiceStates::CHARGEBACK->value,
                    InvoiceStates::FAILED->value,
                    InvoiceStates::REFUNDED->value,
                    ...($isProcessingPayment ? [InvoiceStates::PAID->value] : [])
                ]);
            })
            ->map(fn($transition) => [
                self::STATUS_ID    => $transition,
                self::STATUS_TITLE => InvoiceStates::tryFrom($transition)->getActionTitle(),
            ]);
    }

    /**
     * @return Collection<InvoiceItemDTO>
     */
    protected function parseInvoiceItemsToDto(): Collection
    {
        return $this->{Invoice::RELATION_INVOICE_ITEMS}
            ->map(fn(InvoiceItem $item) => new InvoiceItemDTO(
                invoice_item_id: $item->id,
                invoice_id     : $item->invoice_id,
                billable_id    : $item->billable_id,
                billable_type  : $item->billable_type,
                unit_price     : $item->unit_price,
                quantity       : $item->quantity,
                description    : $item->description,
                added_by       : $item->added_by,
            ));
    }
}
