<?php

namespace App\Http\Resources\Billing;

use App\Enums\Billing\InvoiceItemTypes;
use App\Models\Odin\ProductAssignment;
use Spatie\EventSourcing\StoredEvents\StoredEvent;

/**
 * @mixin StoredEvent
 */
class InvoiceItemDeletedResource extends AuthorableEventResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array
     */
    public function getEventData(): array
    {
        return [];
    }

}
