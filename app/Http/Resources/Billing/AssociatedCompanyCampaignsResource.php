<?php

namespace App\Http\Resources\Billing;

use App\DTO\Billing\InvoiceItemDTO;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Campaigns\CompanyCampaign;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Collection;

/**
 * @mixin InvoiceItemDTO
 */
class AssociatedCompanyCampaignsResource extends BaseJsonResource
{
    const string FIELD_HAYSTACK_CAMPAIGNS = 'haystack_campaigns';
    const string FIELD_NEEDLE_CAMPAIGNS   = 'needle_campaigns';
    const string FIELD_ID                 = 'id';
    const string FIELD_TITLE              = 'title';
    const string FIELD_ASSOCIATED         = 'associated';

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        /** @var Collection<CompanyCampaign> $haystackCampaigns */
        $haystackCampaigns = $this->resource->get(self::FIELD_HAYSTACK_CAMPAIGNS, collect());

        /** @var Collection<CompanyCampaign> $needleCampaigns */
        $needleCampaigns = $this->resource->get(self::FIELD_NEEDLE_CAMPAIGNS, collect());

        $formattedCampaigns = collect();

        foreach ($haystackCampaigns as $campaign) {
            $formattedCampaigns->push([
                self::FIELD_ID         => $campaign->id,
                self::FIELD_TITLE      => $campaign->name,
                self::FIELD_ASSOCIATED => $needleCampaigns->contains(CompanyCampaign::FIELD_ID, $campaign->id),
            ]);
        }

        return $formattedCampaigns->toArray();
    }
}
