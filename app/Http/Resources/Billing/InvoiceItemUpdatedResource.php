<?php

namespace App\Http\Resources\Billing;

use Spatie\EventSourcing\StoredEvents\StoredEvent;

/**
 * @mixin StoredEvent
 */
class InvoiceItemUpdatedResource extends AuthorableEventResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array
     */
    public function getEventData(): array
    {
        $changes = $this->event_properties['changes'];

        return ['changes' => $changes];
    }

}
