<?php

namespace App\Http\Resources\Billing\EventResource;

use App\Enums\Billing\ApprovableActionType;
use App\Http\Resources\Billing\AuthorableEventResource;
use Spatie\EventSourcing\StoredEvents\StoredEvent;

/**
 * @mixin StoredEvent
 */
class InvoiceActionRequestEventResource extends AuthorableEventResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array
     */
    public function getEventData(): array
    {
        return [
            'type' => ApprovableActionType::tryFrom($this->resource->event_properties['actionRequested'])?->getTitle(),
        ];
    }

}
