<?php

namespace App\Http\Resources\Billing\EventResource;

use App\Http\Resources\Billing\AuthorableEventResource;
use Illuminate\Support\Arr;
use Spatie\EventSourcing\StoredEvents\StoredEvent;

/**
 * @mixin StoredEvent
 */
class UpdateInvoiceCollectionsResource extends AuthorableEventResource
{
    const string EVENT_FIELD_AUTHOR_ID        = 'author_id';
    const string EVENT_FIELD_AUTHOR_TYPE      = 'author_type';
    const string EVENT_FIELD_RECOVERY_DATE    = 'recovery_date';
    const string EVENT_FIELD_RECOVERY_STATUS  = 'recovery_status';
    const string EVENT_FIELD_AMOUNT_RECOVERED = 'amount_recovered';

    /**
     * Transform the resource into an array.
     *
     * @return array
     */
    public function getEventData(): array
    {
        $eventProperties = $this->event_properties;

        return [
            self::EVENT_FIELD_AUTHOR_ID        => Arr::get($eventProperties, 'authorId'),
            self::EVENT_FIELD_AUTHOR_TYPE      => Arr::get($eventProperties, 'authorType'),
            self::EVENT_FIELD_RECOVERY_DATE    => Arr::get($eventProperties, 'recoveryDate'),
            self::EVENT_FIELD_RECOVERY_STATUS  => Arr::get($eventProperties, 'recoveryStatus'),
            self::EVENT_FIELD_AMOUNT_RECOVERED => Arr::get($eventProperties, 'amountRecovered'),
        ];
    }
}
