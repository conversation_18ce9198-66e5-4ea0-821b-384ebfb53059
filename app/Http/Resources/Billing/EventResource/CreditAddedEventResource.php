<?php

namespace App\Http\Resources\Billing\EventResource;

use App\Http\Resources\Billing\AuthorableEventResource;
use App\Models\Billing\CreditType;
use Spatie\EventSourcing\StoredEvents\StoredEvent;

/**
 * @mixin StoredEvent
 */
class CreditAddedEventResource extends AuthorableEventResource
{
    const string EVENT_PROPERTY_TYPE   = 'type';
    const string EVENT_PROPERTY_AMOUNT = 'amount';
    const string EVENT_PROPERTY_NOTES  = 'notes';

    /**
     * Transform the resource into an array.
     *
     * @return array
     */
    public function getEventData(): array
    {
        $eventProperties = $this->event_properties;

        /** @var CreditType $creditType */
        $creditType = CreditType::query()->where(CreditType::FIELD_SLUG, $eventProperties[self::EVENT_PROPERTY_TYPE])->first();

        return [
            self::EVENT_PROPERTY_TYPE   => $creditType?->{CreditType::FIELD_NAME},
            self::EVENT_PROPERTY_AMOUNT => $eventProperties[self::EVENT_PROPERTY_AMOUNT] / 100,
            self::EVENT_PROPERTY_NOTES  => $eventProperties[self::EVENT_PROPERTY_NOTES]
        ];
    }
}
