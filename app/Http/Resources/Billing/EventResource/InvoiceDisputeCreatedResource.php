<?php

namespace App\Http\Resources\Billing\EventResource;

use App\Http\Resources\Odin\BaseJsonResource;
use Spatie\EventSourcing\StoredEvents\StoredEvent;

/**
 * @mixin StoredEvent
 */
class InvoiceDisputeCreatedResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array
     */
    public function getEventData(): array
    {
        $eventProperties = $this->event_properties;

        return $eventProperties;
    }
}
