<?php

namespace App\Http\Resources\Billing\EventResource;

use App\Http\Resources\Billing\AuthorableEventResource;
use App\Models\Billing\ActionApproval;
use Spatie\EventSourcing\StoredEvents\StoredEvent;

/**
 * @mixin StoredEvent
 */
class InvoiceActionRequestReviewedEventResource extends AuthorableEventResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array
     */
    public function getEventData(): array
    {
        $action = ActionApproval::findByUuid($this->resource->event_properties['actionApprovalUuid']);

        return [
            'reason' => $action?->{ActionApproval::FIELD_REASON},
            'type'   => $action?->{ActionApproval::FIELD_REQUESTED_ACTION}?->getTitle(),
            'status' => $this->resource->event_properties['status']
        ];
    }
}
