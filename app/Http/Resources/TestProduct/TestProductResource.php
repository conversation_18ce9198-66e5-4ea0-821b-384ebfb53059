<?php

namespace App\Http\Resources\TestProduct;

use App\Models\Odin\Consumer;
use App\Models\Odin\IndustryService;
use App\Models\TestProduct;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin TestProduct
 */
class TestProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     *
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id'                    => $this->id,
            'name'                  => $this->product?->consumer?->getFullName(),
            'phone'                 => $this->phone,
            'email'                 => $this->email,
            'status'                => $this->status->toName(),
            'company_id'            => $this->company->id,
            'company_name'          => $this->company->name,
            'campaign_id'           => $this->relation?->id ?? $this->campaign?->id,
            'campaign_name'         => $this->relation?->name ?? $this->campaign?->name,
            'legacy_campaign_id'    => $this->legacyLeadCampaign?->id,
            'legacy_campaign_name'  => $this->legacyLeadCampaign?->name,
            'product_id'            => $this->product_id,
            'lead_id'               => $this->product?->consumer?->legacy_id,
            'industry'              => $this->product->industryService?->industry->name ?? $this->company->services()->first()?->industry?->name,
            'service'               => $this->product->industryService?->name ?? $this->company->services()->first()?->{IndustryService::FIELD_NAME},
            'verified'              => in_array($this->product?->consumer?->classification, Consumer::VERIFIED_CLASSIFICATIONS),
            'created_at'            => $this->created_at->toDateTimeString(),
            'reveal_at'             => $this->reveal_at?->toDateTimeString(),
            'communications'        => $this->communications()->exists(),
            'created_by_id'         => $this->{TestProduct::RELATION_CREATED_BY}?->id,
            'created_by_name'       => $this->{TestProduct::RELATION_CREATED_BY}?->name
        ];
    }
}
