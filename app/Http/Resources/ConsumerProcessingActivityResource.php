<?php

namespace App\Http\Resources;

use App\Enums\ConsumerProcessing\ConsumerProcessingActivityType;
use App\Enums\ConsumerProcessing\ConsumerProcessingActivityVisibilityScope;
use App\Models\ConsumerProcessingActivity;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;
use JsonSerializable;

/**
 * @mixin ConsumerProcessingActivity
 */
class ConsumerProcessingActivityResource extends JsonResource
{
    const int COMMENT_PREVIEW_LENGTH = 32;

    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray(Request $request): array|Arrayable|JsonSerializable
    {
        $user = Auth::user();

        return [
            'id'               => $this->id,
            'name'             => $this->getName(),
            'summary'          => $this->activity_type === ConsumerProcessingActivityType::USER_COMMENT
                ? $this->getUserCommentSummary()
                : $this->summary,
            'comment'          => $this->comment,
            'related_activity' => $this->getRelatedActivity(),
            'editable'         => $user->id === $this->user_id,
            'public'           => $this->scope === ConsumerProcessingActivityVisibilityScope::SEND_TO_COMPANY,
            'created_at'       => $this->created_at->timestamp,
        ];
    }

    private function getName(): string
    {
        $name = $this->user?->name;

        return preg_match("/^automated/i", $name) || !$name
            ? 'System User'
            : $name;
    }

    /**
     * @return string
     */
    private function getUserCommentSummary(): string
    {
        $preview = strlen($this->comment) > self::COMMENT_PREVIEW_LENGTH
            ? substr($this->comment, 0, self::COMMENT_PREVIEW_LENGTH) . '...'
            : $this->comment;

        return $this->activity_type->getCommentTitle($preview);
    }

    /**
     * @return array|null
     */
    private function getRelatedActivity(): ?array
    {
        return $this->activity_type->hasRelatedActivity()
            ? [ 'id' => $this->activity_id, 'type' => $this->activity_type->value]
            : null;
    }
}