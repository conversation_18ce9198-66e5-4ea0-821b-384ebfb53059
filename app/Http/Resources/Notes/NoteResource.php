<?php

namespace App\Http\Resources\Notes;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Note;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Client\Request;

/**
 * @mixin Note
 */
class NoteResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        $author = $this->{Note::RELATION_AUTHOR};

        $isMe = $author?->{User::FIELD_ID} === auth()->user()->id;

        return [
            'id'            => $this->{Note::FIELD_ID},
            'author'        => $author->{User::FIELD_NAME} . ($isMe ? ' (Me)' : ''),
            'created_at'    => Carbon::parse($this->{Note::FIELD_CREATED_AT})->format('M d, Y A h:m:s'),
            'content'       => $this->{Note::FIELD_CONTENT},
        ];
    }
}
