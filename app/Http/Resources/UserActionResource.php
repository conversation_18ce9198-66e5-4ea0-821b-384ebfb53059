<?php

namespace App\Http\Resources;

use App\Models\UserAction;
use App\Models\ActionTag;
use App\Models\UserActionTag;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;
use JsonSerializable;

/**
 * Class UserActionResource
 * @mixin UserAction
 */
class UserActionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {

        return [
            'id' => $this->id,
            'subject' => $this->subject,
            'message' => Str::markdown($this->message),
            'user_id' => $this->{UserAction::FIELD_USER_ID},
            'created_timestamp' => $this->created_at?->timestamp,
            'updated_timestamp' => $this->updated_at?->timestamp,
            'pinned' => $this->pinned,
            'display_date' => $this->{UserAction::FIELD_DISPLAY_DATE}?->format('Y-m-d'),
            'tags' => $this->{UserAction::RELATION_TAGS} ? array_column($this->{UserAction::RELATION_TAGS}->toArray(), UserActionTag::FIELD_USER_ID) : [],
            'tag_by_email' => (bool)$this->{UserAction::FIELD_TAG_BY_EMAIL},

        ];
    }
}
