<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RoleConfigurationResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'role' => [
                'id' => $this->role->id,
                'name' => str($this->role->name)->headline()
            ],
            'user' => $this->user->only(['id', 'name']),
            'configurations' => ConfigurationResource::collection(
                collect($this->data)->map(fn($value, $key) => [
                    'key' => $key,
                    'value' => $value
                ])->values()
            )
        ];
    }
}
