<?php

namespace App\Http\Resources;

use App\Models\UserPreset;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin UserPreset
 */
class UserPresetResource extends JsonResource
{
    const FIELD_ID = 'id';
    const FIELD_NAME = 'name';
    const FIELD_KEY = 'key';
    const FIELD_TYPE = 'type';
    const FIELD_CATEGORY = 'category';
    const FIELD_VALUE = 'value';
    const FIELD_CREATED_BY = 'created_by';

    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        return [
            self::FIELD_ID        => $this->id,
            self::FIELD_NAME      => $this->name,
            self::FIELD_KEY       => $this->key,
            self::FIELD_TYPE      => $this->type,
            self::FIELD_CATEGORY  => $this->category,
            self::FIELD_VALUE     => $this->value,

            self::FIELD_CREATED_BY    => [
                'name' => $this->user->name,
            ],
        ];
    }
}
