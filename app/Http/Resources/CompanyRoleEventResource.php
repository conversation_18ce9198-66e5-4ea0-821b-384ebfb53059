<?php

namespace App\Http\Resources;

use App\Enums\ActivityLog\ActivityLogName;
use App\Helpers\CarbonHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\User;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Str;
use Spatie\Activitylog\Models\Activity;

/**
 * @mixin Activity
 */
class CompanyRoleEventResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        $causer = $this->causer?->name ?? 'System';

        $properties = $this->properties;

        $userId = $properties->get('user_id');

        $manager = User::query()->findOrFail($userId);

        $title = $this->log_name === ActivityLogName::COMPANY_ROLE_UNASSIGNED->value
            ? 'Unassigned from '. $manager->name : 'Assigned to ' . $manager->name;

        return [
            'title' => $title,
            'date'    => $this->created_at ? CarbonHelper::parse($this->created_at)->toFormat() : 'Unknown',
            'payload' => [
                "Author: " . $causer,
                "Reason: " . Str::headline($this->description)
            ],
        ];
    }
}
