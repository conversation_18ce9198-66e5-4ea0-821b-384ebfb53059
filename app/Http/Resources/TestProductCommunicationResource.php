<?php

namespace App\Http\Resources;

use App\Models\TestProductCommunication;
use App\Repositories\Odin\TestLeadApiRepository;
use Carbon\Carbon;
use Carbon\CarbonInterface;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Support\Collection;

class TestProductCommunicationResource extends ResourceCollection
{
    const FIELD_ID             = 'id';
    const FIELD_COMMUNICATIONS = 'communications';
    const FIELD_EVENTS         = 'events';
    const FIELD_DATE           = 'date';
    const FIELD_DISPLAY_DATE   = 'display_date';
    const FIELD_EVENT          = 'event';
    const FIELD_CONTENT        = 'content';
    const FIELD_DATE_DIFF      = 'date_diff';
    const FIELD_TYPE           = 'type';
    const FIELD_FRAUD_SCORE    = 'fraud_score';
    const PHONE                = 'phone';
    const COMMUNICATION_ID     = 'communication_id';
    const FIELD_FILE_DATE      = 'file_date';
    const FIELD_EVENT_VERBOSE  = [
        'created_at'   => "Lead successfully created.",
        'delivered_at' => "Lead successfully delivered to company.",
        'reveal_at'    => "Lead revealed.",
        'expire_at'    => "Lead expired."
    ];

    /**
     * Transform the resource collection into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        $data = $this->formatAndMergeCommunicationsAndEvents(
            $this->resource->get(self::FIELD_COMMUNICATIONS, collect()),
            $this->resource->get(self::FIELD_EVENTS, collect())
        )->sortBy(self::FIELD_DATE)->values();

        return $this->addDiffs($data)->toArray();
    }

    /**
     * Formats a given communication/event into the expected output for the frontend.
     *
     * @param int|null $id
     * @param Carbon $date
     * @param string $event
     * @param array|null $content
     * @param string $type
     * @param int|null $fraud_score
     * @return array
     */
    protected function formatDataForFrontend(int|null    $id,
                                             Carbon      $date,
                                             string      $event,
                                             ?array      $content,
                                             string      $type,
                                             int|null $fraud_score
    ): array
    {
        return [
            self::FIELD_ID           => $id,
            self::FIELD_DISPLAY_DATE => $date->setTimezone('MST')->format('m/d/Y h:i A') . " (MST)" ,
            self::FIELD_FILE_DATE    => $date->setTimezone('MST')->format('m-d-Y'),
            self::FIELD_DATE         => $date,
            self::FIELD_EVENT        => $event,
            self::FIELD_CONTENT      => $content,
            self::FIELD_TYPE         => $type,
            self::FIELD_FRAUD_SCORE  => $fraud_score
        ];
    }

    /**
     * Merges, and formats communications and events to be compliant for the front end.
     *
     * @param Collection $communications
     * @param Collection $events
     * @return Collection
     */
    protected function formatAndMergeCommunicationsAndEvents(Collection $communications, Collection $events): Collection
    {
        return $communications->map(
            fn(TestProductCommunication $communication) => $this->formatDataForFrontend(
                $communication->id,
                $communication->created_at,
                "Lead contacted via {$communication->communication_type->toName()} by {$communication->from}",
                $communication->content,
                $communication->communication_type->toName(),
                $communication->fraud_score
            )
        )->merge($events->map(fn(array $event) => $this->formatDataForFrontend(
            null,
            $event[self::FIELD_DATE],
            self::FIELD_EVENT_VERBOSE[$event[TestLeadApiRepository::TEST_LEAD_EVENT_FIELD_EVENT]],
            null,
            self::FIELD_EVENT,
            null,
        )));
    }

    /**
     * Adds the time difference since the last event to each item.
     *
     * @param Collection $data
     * @return Collection
     */
    protected function addDiffs(Collection $data): Collection
    {
        return $data->map(function(array $item, int $index) use ($data) {
            if($index > 0)
                $item[self::FIELD_DATE_DIFF] = $item[self::FIELD_DATE]->diffForHumans(
                    $data[$index - 1][self::FIELD_DATE],
                    ['syntax' => CarbonInterface::DIFF_ABSOLUTE, 'short' => null, 'parts' => 3]
                );

            return collect($item)->except([self::FIELD_DATE])->toArray();
        });
    }
}
