<?php

namespace App\Http\Resources\MarketingCampaign;

use App\Enums\MarketingCampaigns\MarketingCampaignStatus;
use App\Enums\MarketingCampaigns\MarketingCampaignType;
use App\Helpers\CarbonHelper;
use App\Helpers\NumberHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\MarketingCampaign;
use Carbon\Carbon;
use Carbon\CarbonInterface;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Str;

/**
 * @mixin MarketingCampaign
 */
class ListMarketingCampaignsResource extends BaseJsonResource
{
    const string ID          = 'id';
    const string TYPE        = 'type';
    const string STATUS      = 'status';
    const string PROCESSING  = 'processing';
    const string NAME        = 'name';
    const string DESCRIPTION = 'description';
    const string SENT_AT     = 'sent_at';
    const string SCHEDULE    = 'schedule';
    const string METRICS     = 'metrics';
    const string PAUSABLE    = 'pausable';
    const string CADENCE     = 'cadence';
    const string REVENUE     = 'revenue';
    const string HAS_LOGS    = 'has_logs';

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        $metrics = $this->type->internal()
            ? new InternalEmailMarketingCampaignMetricsResource($this->resource)
            : new EmailMarketingCampaignMetricsResource($this->resource);

        $schedule = null;
        if (!empty($this->{MarketingCampaign::FIELD_SENT_AT})
            && Carbon::parse($this->{MarketingCampaign::FIELD_SENT_AT})->gt(now())) {
            $schedule = CarbonHelper::parse($this->{MarketingCampaign::FIELD_SENT_AT})->diffForHumans(now(), CarbonInterface::DIFF_RELATIVE_TO_NOW);
        }

        $cadence = null;
        if ($this->type->drip()) {
            $cadence = $this->{MarketingCampaign::FIELD_CONFIGURATION}['span_value'] . ' ' . $this->{MarketingCampaign::FIELD_CONFIGURATION}['span_type'] . 's' . ' after sign-up';
        }

        return [
            self::ID          => $this->{MarketingCampaign::FIELD_ID},
            self::TYPE        => Str::headline($this->{MarketingCampaign::FIELD_TYPE}->value),
            self::STATUS      => $this->{MarketingCampaign::FIELD_STATUS},
            self::PROCESSING  => $this->{MarketingCampaign::FIELD_PROCESSING},
            self::NAME        => $this->{MarketingCampaign::FIELD_NAME},
            self::DESCRIPTION => $this->{MarketingCampaign::FIELD_DESCRIPTION},
            self::SENT_AT     => empty($this->{MarketingCampaign::FIELD_SENT_AT}) ? 'Not Sent' : CarbonHelper::parse($this->{MarketingCampaign::FIELD_SENT_AT})->toFormat(),
            self::SCHEDULE    => $schedule,
            self::METRICS     => $metrics,
            self::PAUSABLE    => $this->status === MarketingCampaignStatus::ACTIVE || $this->status === MarketingCampaignStatus::PAUSED,
            self::CADENCE     => $cadence,
            self::REVENUE     => NumberHelper::currency(+$this->revenue),
            self::HAS_LOGS    => $this->logs()->exists(),
        ];
    }
}
