<?php

namespace App\Http\Resources\MarketingCampaign;

use App\Http\Resources\Odin\ListOptionResource;
use App\Models\MarketingCampaign;

/**
 * @mixin MarketingCampaign
 */
class MarketingCampaignListOptionResource extends ListOptionResource
{
    public function getId(): string
    {
        return $this->{MarketingCampaign::FIELD_ID};
    }

    public function getName(): string
    {
        return $this->{MarketingCampaign::FIELD_NAME};
    }

    public function getPayload(): array
    {
        return [
            'status' => $this->{MarketingCampaign::FIELD_STATUS}
        ];
    }
}
