<?php

namespace App\Http\Resources\MarketingCampaign;

use App\Helpers\CarbonHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\MarketingLog;
use Illuminate\Http\Client\Request;

/**
 * @mixin MarketingLog
 */
class GetMarketingLogResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id'          => $this->{MarketingLog::FIELD_ID},
            'message'     => $this->{MarketingLog::FIELD_MESSAGE},
            'namespace'   => $this->{MarketingLog::FIELD_NAMESPACE},
            'level'       => $this->{MarketingLog::FIELD_LEVEL},
            'stack_trace' => $this->{MarketingLog::FIELD_STACK_TRACE},
            'context'     => $this->{MarketingLog::FIELD_CONTEXT},
            'created_at'  => CarbonHelper::parse($this->{MarketingLog::FIELD_CREATED_AT})->toFormat(),
        ];
    }
}
