<?php

namespace App\Http\Resources\MarketingCampaign;

use App\DTO\MarketingCampaign\CampaignMetrics;
use App\Helpers\CarbonHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\MarketingCampaign;
use Illuminate\Http\Client\Request;

/**
 * @mixin MarketingCampaign
 */
class BaseMarketingCampaignMetricsResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        $metrics = CampaignMetrics::fromMarketingCampaign($this->resource);

        return [
            'revalidated'      => $metrics->getRevalidates(),
            'targeted'         => $metrics->getTargets(),
            'visits'           => $metrics->getVisits(),
            'last_revalidated' => $metrics->getLastRevalidatedAt() ? CarbonHelper::parse($metrics->getLastRevalidatedAt()->toIso8601String())->toFormat() : 'Never',
            'last_updated'     => $metrics->getLastUpdatedAt() ? CarbonHelper::parse($metrics->getLastUpdatedAt()->toIso8601String())->toFormat() : 'Never'
        ];
    }
}
