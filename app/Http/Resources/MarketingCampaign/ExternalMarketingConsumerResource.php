<?php

namespace App\Http\Resources\MarketingCampaign;

use App\Http\Resources\Dashboard\AddressResource;
use App\Models\MarketingCampaignConsumer;
use App\Models\Odin\Address;
use App\Models\Odin\ConsumerProduct;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin MarketingCampaignConsumer
 */
class ExternalMarketingConsumerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        $consumer = $this->{MarketingCampaignConsumer::RELATION_CONSUMER};

        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = $consumer->consumerProducts()->first();

        /** @var Address $address */
        $address = $consumerProduct->{ConsumerProduct::RELATION_ADDRESS};

        $url = $consumerProduct?->consumerProductTracking?->url_start;

        return [
            'marketing_reference' => $this->{MarketingCampaignConsumer::FIELD_MARKETING_CAMPAIGN_REFERENCE},
            'first_name'          => $consumer->first_name,
            'last_name'           => $consumer->last_name,
            'email'               => $consumer->email,
            'phone'               => $consumer->phone,
            'address'             => new AddressResource($address),
            'url'                 => !empty($url) ? $this->getBaseUrl($url) : $url,
            'contact_requests'    => $consumerProduct->contact_requests,
            'revalidated'         => (bool)$this->{MarketingCampaignConsumer::FIELD_REVALIDATED_AT}
        ];
    }

    /**
     * @param string $url
     * @return string|null
     */
    public function getBaseUrl(string $url): ?string
    {
        $parsed = parse_url($url);
        return $parsed['scheme'] . '://' . $parsed['host'];
    }
}
