<?php

namespace App\Http\Resources\MarketingCampaign;

use App\Enums\MarketingCampaigns\MarketingLogRelationType;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\MarketingLogRelation;
use Illuminate\Http\Client\Request;

/**
 * @mixin MarketingLogRelation
 */
class ListMarketingLogRelationResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        $type = MarketingLogRelationType::fromClass(
            $this->{MarketingLogRelation::FIELD_RELATION_TYPE}
        );

        $name = $type->getRelationName($this->resource);

        return [
            'id'   => $this->{MarketingLogRelation::FIELD_RELATION_ID},
            'name' => $name,
            'type' => $type->getShortType(),
        ];
    }
}
