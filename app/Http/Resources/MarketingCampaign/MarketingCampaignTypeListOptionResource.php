<?php

namespace App\Http\Resources\MarketingCampaign;

use App\Enums\MarketingCampaigns\MarketingCampaignType;
use App\Http\Resources\Odin\ListOptionResource;
use Illuminate\Support\Str;

/**
 * @mixin MarketingCampaignType
 */
class MarketingCampaignTypeListOptionResource extends ListOptionResource
{
    public function getId(): string
    {
        return $this->value;
    }

    public function getName(): string
    {
        return Str::headline($this->value);
    }
}
