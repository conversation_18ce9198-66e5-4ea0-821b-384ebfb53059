<?php

namespace App\Http\Resources\MarketingCampaign;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\MarketingCampaignConsumer;
use App\Models\MarketingDomain;
use Illuminate\Http\Client\Request;

/**
 * @mixin MarketingDomain
 */
class ListMarketingDomainResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id'          => $this->{MarketingDomain::FIELD_ID},
            'name'        => $this->{MarketingDomain::FIELD_DOMAIN},
            'status'      => $this->{MarketingDomain::FIELD_STATUS},
            'sent_count'  => $this->sent_count,
        ];
    }
}
