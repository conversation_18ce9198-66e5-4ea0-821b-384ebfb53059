<?php

namespace App\Http\Resources\MarketingCampaign;

use App\Enums\MarketingCampaigns\MarketingCampaignCallbackType;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\MarketingCampaign;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Arr;
use stdClass;

/**
 * @mixin MarketingCampaign
 */
class MarketingCampaignResource extends BaseJsonResource
{
    const string ID                     = 'id';
    const string STATUS                 = 'status';
    const string NAME                   = 'name';
    const string DESCRIPTION            = 'description';
    const string VALIDATION_TYPE        = 'validation_type';
    const string VALIDATION_TYPE_INPUTS = 'validation_type_inputs';
    const string TYPE                   = 'type';
    const string CONFIGURATION          = 'configuration';
    const string SENT_AT = 'sent_at';

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        $configuration = empty($this->{MarketingCampaign::FIELD_CONFIGURATION}) ? [] : $this->{MarketingCampaign::FIELD_CONFIGURATION};

        return [
            self::ID                     => $this->{MarketingCampaign::FIELD_ID},
            self::STATUS                 => $this->{MarketingCampaign::FIELD_STATUS},
            self::NAME                   => $this->{MarketingCampaign::FIELD_NAME},
            self::DESCRIPTION            => $this->{MarketingCampaign::FIELD_DESCRIPTION},
            self::VALIDATION_TYPE        => Arr::get($this->{MarketingCampaign::FIELD_CALLBACK_PAYLOAD}, 'type', MarketingCampaignCallbackType::BASE->value),
            self::VALIDATION_TYPE_INPUTS => Arr::get($this->{MarketingCampaign::FIELD_CALLBACK_PAYLOAD}, 'data', new stdClass()),
            self::TYPE                   => $this->{MarketingCampaign::FIELD_TYPE}->value,
            self::CONFIGURATION          => [
                self::SENT_AT => $this->{MarketingCampaign::FIELD_SENT_AT},
                ...$configuration
            ]
        ];
    }
}
