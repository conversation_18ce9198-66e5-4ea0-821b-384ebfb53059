<?php

namespace App\Http\Resources\MarketingCampaign;

use App\DTO\MarketingCampaign\InternalEmailCampaignMetrics;
use App\Models\MarketingCampaign;
use Illuminate\Http\Client\Request;

/**
 * @mixin MarketingCampaign
 */
class InternalEmailMarketingCampaignMetricsResource extends BaseMarketingCampaignMetricsResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        $metrics = InternalEmailCampaignMetrics::fromMarketingCampaign($this->resource);

        return [
            'sent'      => [
                'count' => $metrics->getSentCount(),
                'percentage' => $this->getPercentage($metrics->getSentCount(), $metrics->getTargets()),
            ],
            'delivered' => [
                'count' => $metrics->getDeliveredCount(),
                'percentage' => $this->getPercentage($metrics->getDeliveredCount(), $metrics->getTargets()),
            ],
            'failed'    => [
                'count' => $metrics->getFailedCount(),
                'percentage' => $this->getPercentage($metrics->getFailedCount(), $metrics->getTargets()),
            ],
            'opened'     => [
                'count' => $metrics->getOpenedCount(),
                'percentage' => $this->getPercentage($metrics->getOpenedCount(), $metrics->getDeliveredCount()),
            ],
            'clicked'    => [
                'count' => $metrics->getClickedCount(),
                'percentage' => $this->getPercentage($metrics->getClickedCount(), $metrics->getDeliveredCount()),
            ],
            'leads' => [
                'count' => $metrics->getRevalidates(),
                'percentage' => $this->getPercentage($metrics->getRevalidates(), $metrics->getDeliveredCount()),
            ],
            ...parent::toArray($request)
        ];
    }

    public function getPercentage(?int $numerator, ?int $denominator = null): ?float
    {
        if ($denominator > 0) {
            return round(($numerator / $denominator) * 100, 2);
        } else {
            return null;
        }
    }
}
