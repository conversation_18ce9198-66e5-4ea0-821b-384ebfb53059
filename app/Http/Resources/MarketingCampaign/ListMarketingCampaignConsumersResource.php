<?php

namespace App\Http\Resources\MarketingCampaign;

use App\Helpers\CarbonHelper;
use App\Helpers\NumberHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\MarketingCampaign;
use App\Models\MarketingCampaignConsumer;
use App\Models\Odin\Consumer;
use Illuminate\Http\Client\Request;

/**
 * @mixin MarketingCampaignConsumer
 */
class ListMarketingCampaignConsumersResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->{MarketingCampaignConsumer::FIELD_ID},
            'name' => $this?->{MarketingCampaignConsumer::RELATION_CONSUMER}?->getFullName() ?? 'Unknown',
            'status' => $this->{MarketingCampaignConsumer::FIELD_STATUS}->value,
            'email' => $this?->{MarketingCampaignConsumer::RELATION_CONSUMER}->{Consumer::FIELD_EMAIL} ?? 'Unknown',
            'campaign' => $this?->{MarketingCampaignConsumer::RELATION_MARKETING_CAMPAIGN}->{MarketingCampaign::FIELD_NAME} ?? 'Unknown',
            'sent_at' => empty($this->{MarketingCampaignConsumer::FIELD_SENT_AT}) ? 'Not Sent' : CarbonHelper::parse($this->{MarketingCampaignConsumer::FIELD_SENT_AT})->toFormat(),
            'delivered_at' => empty($this->{MarketingCampaignConsumer::FIELD_DELIVERED_AT}) ? 'Not Delivered' : CarbonHelper::parse($this->{MarketingCampaignConsumer::FIELD_DELIVERED_AT})->toFormat(),
            'opened_at' => empty($this->{MarketingCampaignConsumer::FIELD_OPENED_AT}) ? 'Not Opened' : CarbonHelper::parse($this->{MarketingCampaignConsumer::FIELD_OPENED_AT})->toFormat(),
            'clicked_at' => empty($this->{MarketingCampaignConsumer::FIELD_CLICKED_AT}) ? 'Not Clicked' : CarbonHelper::parse($this->{MarketingCampaignConsumer::FIELD_CLICKED_AT})->toFormat(),
            'revalidated' => empty($this->{MarketingCampaignConsumer::FIELD_REVALIDATED_AT}) ? 'Not Revalidated' : CarbonHelper::parse($this->{MarketingCampaignConsumer::FIELD_REVALIDATED_AT})->toFormat(),
            'revenue' => NumberHelper::currency(+$this->revenue),
            'cloned_consumer_product_id' => $this->cloned_consumer_product_id,
            'has_logs' => $this->logs()->exists(),
            $this->mergeWhen($this->cloned_consumer_product_id, ['link' => config('app.url') . "/consumer-product/?consumer_product_id=" . $this->cloned_consumer_product_id])
        ];
    }
}
