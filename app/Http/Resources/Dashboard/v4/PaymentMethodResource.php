<?php

namespace App\Http\Resources\Dashboard\v4;

use App\DTO\Billing\PaymentMethods\PaymentMethodDTO;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Collection;
use JsonSerializable;

/**
 * @mixin PaymentMethodDTO
 */
class PaymentMethodResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray(Request $request): array|Arrayable|JsonSerializable
    {
        /** @var Collection<PaymentMethodDTO> $paymentMethods */
        $paymentMethods = $this->resource;

        $paymentMethods = $paymentMethods->sortByDesc(function (PaymentMethodDTO $paymentMethod) {
            return $paymentMethod->getIsDefault();
        })->values();

        $methods = [];

        foreach ($paymentMethods as $idx => $paymentMethod) {
            $methods[] = [
                'id'           => $paymentMethod->getId(),
                'method'       => $paymentMethod->getType(),
                'brand'        => $paymentMethod->getBrand(),
                'last4'        => $paymentMethod->getLast4(),
                'expiry'       => $paymentMethod->getExpiry(),
                'expiry_month' => $paymentMethod->getExpiryMonth(),
                'expiry_year'  => $paymentMethod->getExpiryYear(),
                'name'         => $paymentMethod->getName(),
                'address_1'    => $paymentMethod->getAddress()->getLineOne(),
                'address_2'    => $paymentMethod->getAddress()->getLineTwo(),
                'city'         => $paymentMethod->getAddress()->getCity(),
                'state'        => $paymentMethod->getAddress()->getState(),
                'zipcode'      => $paymentMethod->getAddress()->getPostCode(),
                'country'      => $paymentMethod->getAddress()->getCountry(),
                'is_default'   => $idx === 0,
                'status'       => null,
            ];
        }

        return $methods;
    }
}
