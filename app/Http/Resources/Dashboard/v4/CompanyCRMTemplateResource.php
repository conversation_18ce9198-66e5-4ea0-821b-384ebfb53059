<?php

namespace App\Http\Resources\Dashboard\v4;

use App\Campaigns\Delivery\CRM\BaseCRMDeliverer;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModule;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleCRM;
use App\Models\Campaigns\Modules\Delivery\CompanyCRMTemplate;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use JsonSerializable;
use Throwable;

/**
 * @mixin CompanyCRMTemplate
 */
class CompanyCRMTemplateResource extends BaseCRMDelivererResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        try { // prevent unfetchable campaign if a crm type is ever disabled
            $crmType = $this->crm_type;
        }
        catch(Throwable $e) {
            logger()->warning("Unknown crm_type in crm integration id: $this->id");

            return [];
        }
        $fieldSetup = $this->crm_type->getFieldConfiguration($this->resource);

        return [
            'id'               => $this->id,
            'display_name'     => $this->display_name,
            'crm_type'         => $crmType,
            'crm_type_display' => $crmType->displayName(),
            'payload'          => $this->hydrateFields($fieldSetup, $this->payload, $crmType),
            'interactables'    => $fieldSetup[BaseCRMDeliverer::INTERACTABLES_KEY] ?? null,
            'campaigns'        => $this->getActiveCampaignReferences(),
        ];
    }

    private function getActiveCampaignReferences(): array
    {
        return $this->campaignDeliveries()
            ->join(CompanyCampaignDeliveryModule::TABLE, CompanyCampaignDeliveryModule::TABLE .'.'. CompanyCampaignDeliveryModule::FIELD_ID, '=', CompanyCampaignDeliveryModuleCRM::FIELD_MODULE_ID)
            ->join(CompanyCampaign::TABLE, CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID, '=', CompanyCampaignDeliveryModule::TABLE .'.'. CompanyCampaignDeliveryModule::FIELD_CAMPAIGN_ID)
            ->pluck(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_REFERENCE)
            ->toArray();
    }
}
