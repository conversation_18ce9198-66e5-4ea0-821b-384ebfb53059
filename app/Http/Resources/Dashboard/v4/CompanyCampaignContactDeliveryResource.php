<?php

namespace App\Http\Resources\Dashboard\v4;

use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleContact;
use App\Models\Odin\CompanyUser;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin CompanyCampaignDeliveryModuleContact
 */
class CompanyCampaignContactDeliveryResource extends JsonResource
{
    const PAYLOAD_NAME  = 'name';

    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        $contact = $this->contact;

        return [
            CompanyCampaignDeliveryModuleContact::FIELD_CONTACT_ID   => $this->contact_id,
            CompanyCampaignDeliveryModuleContact::FIELD_ACTIVE       => $this->active,
            CompanyCampaignDeliveryModuleContact::FIELD_SMS_ACTIVE   => $this->sms_active,
            CompanyCampaignDeliveryModuleContact::FIELD_EMAIL_ACTIVE => $this->email_active,
            CompanyUser::FIELD_CELL_PHONE                            => $contact->cell_phone ?? "",
            CompanyUser::FIELD_EMAIL                                 => $contact->email ?? "",
            self::PAYLOAD_NAME                                       => $contact?->completeName() ?? "",
        ];
    }
}
