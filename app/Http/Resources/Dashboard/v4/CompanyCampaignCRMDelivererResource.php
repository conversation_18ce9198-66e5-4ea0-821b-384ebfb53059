<?php

namespace App\Http\Resources\Dashboard\v4;

use App\Campaigns\Delivery\CRM\BaseCRMDeliverer;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleCRM;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use JsonSerializable;
use Throwable;

/**
 * @mixin CompanyCampaignDeliveryModuleCRM
 */
class CompanyCampaignCRMDelivererResource extends BaseCRMDelivererResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        try { // prevent unfetchable campaign if a crm type is ever disabled
            $crmType = $this->crm_type;
        }
        catch(Throwable $e) {
            logger()->warning("Unknown crm_type in crm integration id: $this->id");

            return [];
        }
        $fieldSetup = $this->crm_type->getFieldConfiguration($this->resource);

        return [
            'id'               => $this->id,
            'display_name'     => $this->display_name,
            'campaign_name'    => $this->campaign_name ?? "",
            'crm_type'         => $crmType,
            'crm_type_display' => $crmType->displayName(),
            'active'           => $this->active,
            'payload'          => $this->hydrateFields($fieldSetup, $this->payload, $crmType),
            'interactables'    => $fieldSetup[BaseCRMDeliverer::INTERACTABLES_KEY] ?? null,
            'template_id'      => $this->template_id,
        ];
    }
}
