<?php

namespace App\Http\Resources\Dashboard\v4;

use App\Enums\Billing\InvoiceStates;
use App\Enums\Billing\PaymentMethodServices;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Billing\Invoice;
use App\Models\Legacy\EloquentInvoice;
use App\Services\Billing\BillingLogService;
use App\Services\CloudStorage\GoogleCloudStorageService;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use JsonSerializable;

/**
 * @mixin Invoice
 */
class InvoiceResourceV4 extends BaseJsonResource
{
    const string ID         = 'id';
    const string CREATED_AT = 'created_at';
    const string DUE_AT     = 'due_at';
    const string STATUS     = 'status';
    const string TOTAL      = 'total';
    const string PDF        = 'pdf';

    /**
     * @return array
     * @throws BindingResolutionException
     */
    public function formatA20Invoice(): array
    {
        $googleCloudStorageService = app()->make(GoogleCloudStorageService::class);
        $url = null;

        $invoice = Invoice::query()->where('id', $this->id)->first();

        if (filled($invoice->invoice_url)) {
            try {
                $url = $googleCloudStorageService->generateSignedUrl(config('services.google.storage.buckets.invoices'), $invoice->invoice_url);
            } catch (Exception $exception) {
                BillingLogService::logException(
                    exception: $exception,
                    namespace: 'invoice_dashboard_resource',
                    context  : [
                        'invoice_id' => $invoice->id,
                    ]
                );
            }
        }

        return [
            self::ID         => $this->id,
            self::CREATED_AT => Carbon::parse($this->created_at)->unix() * 1000,
            self::DUE_AT     => Carbon::parse($this->due_at)->unix() * 1000,
            self::STATUS     => InvoiceStates::tryFrom($this->status)->value,
            'payment_method' => $invoice->billingProfile?->payment_method,
            self::PDF        => $url,
            'url'            => $this->invoice_url,
            self::TOTAL      => $invoice->getInvoiceItemsTotal() / 100,
            'source'         => $this->source
        ];
    }

    /**
     * @return array
     */
    public function formatLegacyInvoice(): array
    {
        return [
            'id'             => $this->id,
            self::CREATED_AT => Carbon::parse($this->created_at)->unix() * 1000,
            self::DUE_AT     => Carbon::parse($this->due_at)->unix() * 1000,
            'status'         => $this->status,
            'payment_method' => PaymentMethodServices::MANUAL->value,
            self::TOTAL      => EloquentInvoice::query()->findOrFail($this->id)->getTotalPrice(),
            'source'         => $this->source
        ];
    }

    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     * @throws BindingResolutionException
     */
    public function toArray(Request $request): array|Arrayable|JsonSerializable
    {
        if ($this->source === 'a20') {
            return $this->formatA20Invoice();
        }

        return $this->formatLegacyInvoice();
    }
}
