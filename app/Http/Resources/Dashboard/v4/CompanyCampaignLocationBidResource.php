<?php

namespace App\Http\Resources\Dashboard\v4;

use App\Campaigns\Modules\Bidding\ProductBiddingModule;
use App\Models\Odin\ProductCountyBidPrice;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin ProductCountyBidPrice
 */
class CompanyCampaignLocationBidResource extends JsonResource
{
    const SALE_TYPE_KEY = 'sale_type_key';

    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        return [
            ProductBiddingModule::PAYLOAD_LOCATION_ID      => $this->county_location_id,
            ProductBiddingModule::PAYLOAD_QUALITY_TIER     => $this->qualityTier->name,
            ProductBiddingModule::PAYLOAD_PROPERTY_TYPE    => $this->propertyType->name,
            ProductBiddingModule::PAYLOAD_BID              => $this->price,
            self::SALE_TYPE_KEY                            => $this->saleType->key,
            ProductCountyBidPrice::FIELD_STATE_LOCATION_ID => $this->state_location_id,
        ];
    }
}
