<?php

namespace App\Http\Resources\Dashboard\v4;

use App\Campaigns\Delivery\CRM\BaseCRMDeliverer;
use App\Campaigns\Delivery\CRM\Enums\CRMType;
use App\Campaigns\Delivery\CRM\Insightly\InsightlyApiVersion;
use App\Campaigns\Delivery\CRM\Insightly\InsightlyCRMDeliveryService;
use App\Campaigns\Delivery\CRM\JobNimbus\JobNimbusDeliveryService;
use App\Campaigns\Delivery\CRM\ParseableEmail\ParseableEmailCRMDeliveryService;
use App\Campaigns\Delivery\CRM\WebForm\StandardWebFormCRMDeliverer;
use Illuminate\Http\Resources\Json\JsonResource;

abstract class BaseCRMDelivererResource extends JsonResource
{
    /**
     * Set these default values if no concrete value exists.
     */
    protected array $defaultFieldValues = [
        CRMType::STANDARD_WEB_FORM->value => [
            BaseCRMDeliverer::SYSTEM_FIELDS_KEY => [
                StandardWebFormCRMDeliverer::FIELD_SEND_FORMAT => StandardWebFormCRMDeliverer::DEFAULT_VALUE_SEND_FORMAT,
            ],
        ],
        CRMType::JOB_NIMBUS->value => [
            BaseCRMDeliverer::SYSTEM_FIELDS_KEY => [
                JobNimbusDeliveryService::ENDPOINT_KEY => JobNimbusDeliveryService::DEFAULT_ENDPOINT,
            ],
        ],
        CRMType::INSIGHTLY->value => [
            BaseCRMDeliverer::SYSTEM_FIELDS_KEY => [
                InsightlyCRMDeliveryService::FIELD_API_VERSION => InsightlyApiVersion::VERSION_3_1->value,
            ],
        ],
        CRMType::PARSEABLE_EMAIL->value => [
            BaseCRMDeliverer::SYSTEM_FIELDS_KEY => [
                ParseableEmailCRMDeliveryService::SYSTEM_FIELD_SUBJECT => ParseableEmailCRMDeliveryService::DEFAULT_EMAIL_SUBJECT,
            ],
        ],
    ];

    /**
     * @param CRMType $crmType
     * @param string $groupKey
     * @param string $fieldKey
     * @return mixed
     */
    protected function getDefaultValue(CRMType $crmType, string $groupKey, string $fieldKey): mixed
    {
        if (array_key_exists($crmType->value, $this->defaultFieldValues)) {
            if (array_key_exists($groupKey, $this->defaultFieldValues[$crmType->value])) {
                return $this->defaultFieldValues[$crmType->value][$groupKey][$fieldKey] ?? null;
            }
        }

        return null;
    }

    /**
     * @param array $fieldSetup
     * @param array $fieldGroupData
     * @param CRMType $crmType
     * @return array
     */
    protected function hydrateFields(array $fieldSetup, array $fieldGroupData, CRMType $crmType): array
    {
        $hasDefaultValues = array_key_exists($crmType->value, $this->defaultFieldValues);
        $output = [];
        $baseFieldGroups = [
            BaseCRMDeliverer::SYSTEM_FIELDS_KEY,
            BaseCRMDeliverer::ADDITIONAL_FIELDS_KEY,
        ];

        $customFieldGroups = [
            BaseCRMDeliverer::CUSTOM_FIELDS_KEY,
            BaseCRMDeliverer::HEADERS_KEY,
            BaseCRMDeliverer::JSON_FIELDS_KEY,
        ];

        foreach($baseFieldGroups as $fieldGroup) {
            $dataHasGroup = array_key_exists($fieldGroup, $fieldGroupData);
            $hydrated = array_map(function($field) use ($fieldGroupData, $fieldGroup, $dataHasGroup, $hasDefaultValues, $crmType) {
                $existingField = $dataHasGroup
                    ? array_values(array_filter($fieldGroupData[$fieldGroup], function($existingField) use ($field) {
                        return $existingField[BaseCRMDeliverer::CRM_FIELD_KEY] === $field[BaseCRMDeliverer::CRM_FIELD_KEY];}))
                    : null;

                $existingValue = $existingField
                    ? $existingField[0][BaseCRMDeliverer::CRM_FIELD_VALUE]
                    : null;

                $defaultValue = $hasDefaultValues && !$existingValue
                    ? $this->getDefaultValue($crmType, $fieldGroup, $field[BaseCRMDeliverer::CRM_FIELD_KEY])
                    : null;

                $field[BaseCRMDeliverer::CRM_FIELD_VALUE] = $existingValue ? :$defaultValue;

                return $field;
            }, ($fieldSetup[$fieldGroup] ?? []));

            $output[$fieldGroup] = $hydrated;
        }

        foreach($customFieldGroups as $customFieldGroup) {
            $output[$customFieldGroup] = $fieldGroupData[$customFieldGroup] ?? [];
        }

        return $output;
    }
}
