<?php

namespace App\Http\Resources\Dashboard;

use App\Enums\InvoiceAssociatedProductStatusEnum;
use App\Models\Legacy\EloquentInvoice;
use Carbon\Carbon;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin EloquentInvoice
 */
class InvoiceAssociatedProductsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        $stringStatus = InvoiceAssociatedProductStatusEnum::getStatus($this->status);
        return [
            'consumer_product_id' => $this->consumer_product_id,
            'delivered_at' => Carbon::parse($this->delivered_at)->format('Y-m-d'),
            'name' => $this->first_name . " " . $this->last_name,
            'legacy_id' => $this->legacy_id,
            'status' => $stringStatus
        ];
    }
}