<?php

namespace App\Http\Resources\Dashboard;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BusinessContactResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'type' => str($this->role->name)->headline(),
            'name' => $this->user->name,
            'email' => $this->user->email,
            'phone' => $this->user->primaryPhone()?->phone,
        ];
    }
}
