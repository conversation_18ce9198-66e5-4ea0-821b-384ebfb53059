<?php

namespace App\Http\Resources\Dashboard;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class CrmDeliveryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        return [
            'id'                    => $this['id'],
            'name'                  => $this['name'],
            'crm'                   => $this->transformCrm($this['crm'] ?? null),
            'integration_fields'    => $this->transformCrmFieldData($this['integration_fields'] ?? null)
        ];
    }

    private function transformCrm(?array $crmData): array
    {
        if ($crmData) {
            return [
                'id'        => $crmData['id'],
                'name'      => $crmData['display_name'],
                'key'       => $crmData['name'],
                'status'    => $crmData['status'],
            ];
        }
        else return [];
    }

    private function transformCrmFieldData(?array $fieldData): array
    {
        if ($fieldData) {
            $systemFields = $fieldData[0];
            $fields = $fieldData[1];
            $customFields = $fieldData[2] ?? [];
            return [
                'system_fields' => $systemFields['value'] ?? [],
                'fields'        => $fields['value'] ?? [],
                'custom_fields' => $customFields['value'] ?? [],
            ];
        }
        else return [];
    }

}
