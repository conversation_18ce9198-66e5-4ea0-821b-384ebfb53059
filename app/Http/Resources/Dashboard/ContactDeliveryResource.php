<?php

namespace App\Http\Resources\Dashboard;

use App\Models\Legacy\LeadCampaignDeliveryMethod;
use App\Models\Legacy\LeadDeliveryMethod;
use App\Models\Odin\CompanyUser;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin LeadCampaignDeliveryMethod
 */
class ContactDeliveryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        $isCampaignMethod = $this->lead_delivery_method_id > 0;

        $leadDeliveryId = $isCampaignMethod ? $this->lead_delivery_method_id : $this->{LeadDeliveryMethod::FIELD_ID};

        $contact = $this->companyContact ?? $this->{LeadCampaignDeliveryMethod::RELATION_LEAD_DELIVERY_METHOD}?->{LeadDeliveryMethod::RELATION_COMPANY_CONTACT} ?? null;

        if (!$contact) return [];

        return [
            'id'                => $contact->id,
            'legacy_id'         => $contact->legacy_id,
            'delivery_method_id'=> $leadDeliveryId,
            'name'              => $contact->completeName(),
            'email'             => $contact->{CompanyUser::FIELD_EMAIL},
            'cell_phone'        => $contact->{CompanyUser::FIELD_CELL_PHONE},
            'sms_active'        => $isCampaignMethod ? str_contains(strtolower($this->{LeadCampaignDeliveryMethod::FIELD_TYPE}), 'sms') : false,
            'email_active'      => $isCampaignMethod ? str_contains(strtolower($this->{LeadCampaignDeliveryMethod::FIELD_TYPE}), 'email') : false,
        ];

    }
}
