<?php

namespace App\Http\Resources\Dashboard\LocalityData;

use App\Models\Legacy\Location;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin Location
 */
class LocalityZipCodeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        return [
            'id'            => $this->id,
            'state_key'     => $this->state_abbr,
            'county_key'    => $this->county_key,
            'city_name'     => $this->city,
            'city_key'      => $this->city_key,
            'zip_code'      => $this->zip_code,
        ];
    }
}
