<?php

namespace App\Http\Resources\Dashboard\LocalityData;

use App\Models\Legacy\Location;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin Location
 */
class LocalityStateResource extends JsonResource
{
    const string KEY_TOTAL_COUNTIES  = 'total_counties';
    const string KEY_TOTAL_ZIP_CODES = 'total_zip_codes';
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        return [
            'id'                      => $this->id,
            'state_name'              => $this->state,
            'state_key'               => $this->state_abbr,
            self::KEY_TOTAL_COUNTIES  => $this->{self::KEY_TOTAL_COUNTIES} ?? 0,
            self::KEY_TOTAL_ZIP_CODES => $this->{self::KEY_TOTAL_ZIP_CODES} ?? 0,
        ];
    }
}
