<?php

namespace App\Http\Resources\Dashboard\LocalityData;

use App\Models\Legacy\Location;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin Location
 */
class LocalityCountyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        return [
            'id'                => $this->id,
            'state_key'         => $this->state_abbr,
            'county_name'       => $this->county,
            'county_key'        => $this->county_key,
            'total_zip_codes'   => $this->total_zip_codes ?? 0
        ];
    }
}
