<?php

namespace App\Http\Resources\Dashboard;

use App\Enums\Odin\Product;
use App\Http\Resources\Dashboard\LocalityData\LocalityZipCodeResource;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignLeadCategory;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Legacy\LeadCategory;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ProductCampaignBudget;
use App\Models\Odin\ProductCampaignSchedule;
use App\Models\Odin\PropertyType;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Collection;
use JsonSerializable;

/**
 * @mixin LeadCampaign
 */
class CampaignDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        /** @var ProductCampaign $productCampaign */
        $productCampaign = ProductCampaign::query()->where(ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID, $this->{LeadCampaign::ID})->firstOrFail();

        return [
            'id'                             => $this->{LeadCampaign::UUID},
            'name'                           => $this->{LeadCampaign::NAME},
            'max_daily_spend'                => $this->max_daily_spend,
            'max_daily_leads'                => $this->max_daily_lead,
            'daily_limit_type'               => $this->getDailyLimitType(),
            'status'                         => $this->getCampaignStatus($productCampaign, $this->{LeadCampaign::STATUS}),
            'allow_non_budget_premium_leads' => $this->{LeadCampaign::ALLOW_NON_BUDGET_PREMIUM_LEADS},
            'property_types'                 => $this->transformPropertyTypes($this->leadCampaignLeadCategories),
            'optional_leads_types'           => $this->transformSalesTypes($this->leadSalesTypeConfigurations),
            'zip_codes'                      => $this->transformZipCodes($this->leadCampaignZipLocations()->with('location')->get()),
            'appointment_budgets'            => $this->getAppointmentBudget($this->{LeadCampaign::ID}),
            'schedules'                      => $this->getScheduleIds($productCampaign),
        ];
    }

    /**
     * @param ProductCampaign $productCampaign
     * @return array
     */
    protected function getScheduleIds(ProductCampaign $productCampaign): array
    {
        return $productCampaign->schedules()->get()
            ->map(fn(ProductCampaignSchedule $schedule) => $schedule->schedule_id)
            ->toArray();
    }

    /**
     * @param ProductCampaign $productCampaign
     * @param bool $legacyStatus
     *
     * @return bool
     */
    protected function getCampaignStatus(ProductCampaign $productCampaign, bool $legacyStatus): bool
    {
        if ($productCampaign->product->name === Product::LEAD->value) return $legacyStatus;

        return $productCampaign->status;
    }

    /**
     * @param int $legacyCampaignId
     *
     * @return array|null
     */
    protected function getAppointmentBudget(int $legacyCampaignId): array|null
    {
        /** @var ProductCampaign $productCampaign */
        $productCampaign = ProductCampaign::query()->where(ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID, $legacyCampaignId)->firstOrFail();

        if ($productCampaign->product->name === Product::LEAD->value) return null;

        return $productCampaign->budgets->map(fn(ProductCampaignBudget $budget) => [
            'quality_tier' => $budget->{ProductCampaignBudget::FIELD_QUALITY_TIER},
            'budget_type' => $budget->{ProductCampaignBudget::FIELD_VALUE_TYPE},
            'budget_value' => $budget->{ProductCampaignBudget::FIELD_VALUE},
            'category' => $budget->{ProductCampaignBudget::FIELD_CATEGORY},
            'status' => $budget->{ProductCampaignBudget::FIELD_STATUS},
        ])->toArray();
    }

    /**
     * @return string
     */
    private function getDailyLimitType(): string
    {
        if ($this->max_daily_spend > 0) return 'spend';
        else if ($this->max_daily_lead > 0) return 'leads';
        else return '';

    }

    /**
     * @param Collection $zipCodeLocations
     * @return array
     */
    private function transformZipCodes(Collection $zipCodeLocations): array
    {
        $output = [];
        $zipCodeLocations->each(function($location) use(&$output) {
            $output[] = new LocalityZipCodeResource($location->location);
        });
        return $output;
    }

    /**
     * Ddd optional sales type data to the campaign
     * @param Collection $salesTypeConfigurations
     * @return array
     */
    private function transformSalesTypes(Collection $salesTypeConfigurations): array
    {
        $output = [];
        $salesTypeConfigurations
            ->each(function(LeadCampaignSalesTypeConfiguration $salesType) use (&$output) {
                if (!$salesType->leadSalesType->default_type) {
                    $output[strtolower($salesType->leadSalesType->key_value)] = [
                        'id' => $salesType->leadSalesType->id,
                        'max_daily_leads' => $salesType->max_daily_lead,
                        'max_daily_spend' => $salesType->max_daily_spend,
                        'status' => $salesType->status,
                    ];
                }
            });
        return $output;
    }

    /**
     * @param Collection $leadCategoryTypes
     * @return array
     */
    private function transformPropertyTypes(Collection $leadCategoryTypes): array
    {
        $leadCategoriesNames = LeadCategory::query()
            ->whereIn(LeadCategory::ID, $leadCategoryTypes->pluck(LeadCampaignLeadCategory::LEAD_CATEGORY_ID)->toArray())
            ->pluck(LeadCategory::NAME)
            ->toArray();

        return PropertyType::query()
            ->whereIn(PropertyType::FIELD_NAME, $leadCategoriesNames)
            ->pluck(PropertyType::FIELD_ID)
            ->unique()
            ->toArray();
    }
}
