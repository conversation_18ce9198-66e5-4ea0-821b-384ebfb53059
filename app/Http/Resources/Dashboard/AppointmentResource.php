<?php

namespace App\Http\Resources\Dashboard;

use App\Models\Odin\ProductAppointment;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin ProductAppointment
 */
class AppointmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        return [
            'appointment_date'          => $this->appointment_date,
            'appointment_time'          => $this->appointment_time,
            'appointment_type'          => $this->appointment_type,
        ];
    }
}
