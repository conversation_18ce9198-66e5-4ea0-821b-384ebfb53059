<?php

namespace App\Http\Resources\Dashboard;

use App\Models\Legacy\EloquentInvoice;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin EloquentInvoice
 */
class InvoiceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        return [
            'id'            => $this->invoiceid,
            'created_at'    => $this->timestampadded*1000,
            'due_at'        => $this->timestamppaymentdue*1000,
            'status'        => $this->status,
            'total'         => $this->getTotalPrice(),
        ];
    }

}
