<?php

namespace App\Http\Resources\Dashboard;

use App\Enums\Campaigns\CampaignStatus;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class InactiveCampaignsResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'type' => CampaignStatus::tryFrom($this->type)->getDisplayName(),
            'since' => $this->since,
            'reactivates' => $this->reactivates,
            'reason' => $this->reason,
            'campaign' => [
                'id' => $this->campaign_id,
                'name' => $this->campaign_name,
                'spend' => $this->spend,
            ],
            'company' => [
                'id' => $this->company_id,
                'name' => $this->company_name,
            ],
        ];
    }
}
