<?php

namespace App\Http\Resources\Dashboard;

use App\Models\Odin\Industry;
use App\Models\License;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin License
 */
class LicenseResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        return [
            'id'            => $this->id,
            'name'          => $this->name,
            'industry_id'   => $this->industry_id,
            'industry'      => $this->industry?->{Industry::FIELD_NAME},
            'issued_at'     => $this->issued_at?->getTimestampMs(),
            'expires_at'    => $this->expires_at?->getTimestampMs(),
            'url'           => $this->url,
            'state'         => $this->state,
            'country'       => $this->country,
            'license_number'=> $this->license_number,
            'type'          => $this->type,
        ];
    }
}
