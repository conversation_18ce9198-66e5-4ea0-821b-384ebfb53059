<?php

namespace App\Http\Resources\Dashboard;

use App\Models\Odin\Address;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin Address
 */
class AddressResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        return [
            'id'        => $this->id,
            'address_1' => $this->address_1,
            'address_2' => $this->address_2,
            'city'      => $this->city,
            'state'     => $this->state,
            'zip_code'  => $this->zip_code,
            'country'   => $this->country,
        ];
    }
}
