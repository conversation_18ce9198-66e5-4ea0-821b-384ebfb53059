<?php

namespace App\Http\Resources\Dashboard;

use App\Models\Odin\Address;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin Address
 */
class TransactionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        return [
            'id'        => $this['transactionid'],
            'type'      => $this['type'],
            'status'    => $this['status'],
            'paid_on'   => $this['timestampupdated'] * 1000,
            'amount'    => $this['amount'],
        ];
    }
}
