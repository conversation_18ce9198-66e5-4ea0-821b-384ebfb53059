<?php

namespace App\Http\Resources\Dashboard;

use App\Enums\Odin\GlobalConfigurableFields;
use App\Models\Odin\Company;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Company
 */
class CompanyProgressResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            "status" => true,
            "items"  => [
                $this->checkLogoAndDescription(),
                $this->checkContacts(),
                $this->checkAddresses(),
                $this->checkServices()
            ]
        ];
    }

    protected function checkLogoAndDescription(): array
    {
        return [
            "title" => "Logo and description have been added",
            "completed" => strlen(trim($this->link_to_logo)) > 0 && strlen(trim($this->data->payload[GlobalConfigurableFields::DESCRIPTION->value] ?? "")) > 0
        ];
    }

    protected function checkContacts(): array
    {
        return [
            "title" => "Contact details have been added",
            "completed" => $this->users()->count() > 0
        ];
    }

    protected function checkAddresses(): array
    {
        return [
            "title" => "Address(es) has been added",
            "completed" => $this->locations()->count() > 0
        ];
    }

    protected function checkServices(): array
    {
        return [
            "title" => "Services have been added",
            "completed" => $this->services()->count() > 0
        ];
    }
}
