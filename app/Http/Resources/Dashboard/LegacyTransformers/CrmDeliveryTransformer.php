<?php

namespace App\Http\Resources\Dashboard\LegacyTransformers;

use App\Models\Legacy\LeadCampaign;

class CrmDeliveryTransformer
{

    /**
     * Transform a Fixr Dashboard crm delivery for integration storage
     * @param array $data
     * @return array
     */
    public function transform(array $data): array
    {
        return [
            'name'              => $data['name'],
            'crm_id'            => $data['crm']['id'],
            'lead_campaign_id'  => $data['lead_campaign_id'] ?? null,
            'crm_integration_fields' => [
                [
                    'name'  => 'system_fields',
                    'value' => $data['integration_fields']['system_fields']
                ],
                [
                    'name'  => 'fields',
                    'value' => $data['integration_fields']['fields']
                ],
                [
                    'name'  => 'custom_fields',
                    'value' => $data['integration_fields']['custom_fields']
                ],
            ]
        ];
    }
}
