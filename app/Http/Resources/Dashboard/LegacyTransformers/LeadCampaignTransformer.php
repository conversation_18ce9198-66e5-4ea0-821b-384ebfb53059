<?php

namespace App\Http\Resources\Dashboard\LegacyTransformers;

use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCategory;
use App\Models\Legacy\LeadSalesType;
use App\Models\Odin\PropertyType;

class LeadCampaignTransformer
{

    /**
     * Transform a Fixr Dashboard campaign for integration storage
     *
     * @param array $data
     * @param bool $appointmentCampaign
     *
     * @return array
     */
    public function transform(array $data, bool $appointmentCampaign = false): array
    {
        $dailyLimitType = preg_match("/^lead/i", $data['daily_limit_type'])
            ? 'lead'
            : 'spend';

        $transform = [
            LeadCampaign::NAME                              => $data['name'],
            LeadCampaign::STATUS                            => $data['status'],
            LeadCampaign::ALLOW_NON_BUDGET_PREMIUM_LEADS    => $data[LeadCampaign::ALLOW_NON_BUDGET_PREMIUM_LEADS],
            'lead_campaign_location_ids'                    => $data['zip_codes'],
            'lead_campaign_lead_category_ids'               => $this->transformPropertyTypeIds($data['property_types']),
            'lead_sales_type_configurations'                => $this->transformSalesTypes($data['optional_leads_types'], $dailyLimitType),
            'lead_campaign_contact_deliveries'              => $this->transformContactDeliveries($data['contact_deliveries']),
            'lead_campaign_crm_integration_ids'             => $this->transformCrmDeliveryIds($data['crm_deliveries']),
            'appointment_campaign'                          => $appointmentCampaign,
        ];
        $this->appendDailyLimits($transform, $data, $dailyLimitType);

        return $transform;
    }

    private function transformPropertyTypeIds(array $propertyTypeIds): array
    {
        $propertyTypeNames = PropertyType::query()
            ->whereIn(PropertyType::FIELD_ID, $propertyTypeIds)
            ->pluck(PropertyType::FIELD_NAME)
            ->toArray();

        return LeadCategory::query()
            ->whereIn(LeadCategory::NAME, $propertyTypeNames)
            ->pluck(LeadCategory::ID)
            ->toArray();
    }

    /**
     * @param array $outputData
     * @param array $campaignData
     * @param string $dailyLimitType
     * @return void
     */
    private function appendDailyLimits(array &$outputData, array $campaignData, string $dailyLimitType): void
    {
        $outputData[LeadCampaign::MAX_DAILY_LEAD] = $dailyLimitType === 'lead' ? $campaignData['max_daily_leads'] : null;
        $outputData[LeadCampaign::MAX_DAILY_SPEND] = $dailyLimitType === 'spend' ? $campaignData['max_daily_spend'] : null;
    }

    private function transformCrmDeliveryIds(array $ids): array
    {
        return collect($ids)->reduce(fn($output, $val, $key) => $val ? [ ...$output, $key ] : $output, []);
    }

    /**
     * Resolve legacy ids
     * @param array $deliveries
     * @return array
     */
    private function transformContactDeliveries(array $deliveries): array
    {
        $deliveries = array_map(fn($delivery) => [ ...$delivery, 'contact_id' => $delivery['legacy_id'] ?? null ], $deliveries);
        return array_filter($deliveries, fn($contact) => $contact['contact_id'] !== null);
    }

    /**
     * Legacy expects all the static sales types, which dashboard doesn't send
     * @param array $optionalSalesTypeData
     * @param string $dailyLimitType
     * @return array[]
     */
    private function transformSalesTypes(array $optionalSalesTypeData, string $dailyLimitType): array
    {
        $requiredSalesTypes =  [
            [
                'lead_sales_type_id'  => 1,
                'max_daily_spend'     => null,
                'max_daily_lead'      => null,
                'status'              => true,
            ],
            [
                'lead_sales_type_id'  => 2,
                'max_daily_spend'     => null,
                'max_daily_lead'      => null,
                'status'              => true,
            ],
            [
                'lead_sales_type_id'  => 3,
                'max_daily_spend'     => null,
                'max_daily_lead'      => null,
                'status'              => true,
            ],
            [
                'lead_sales_type_id'  => 4,
                'max_daily_spend'     => null,
                'max_daily_lead'      => null,
                'status'              => true,
            ],
        ];

        foreach ($optionalSalesTypeData as $key => $type) {
            $legacySalesType = LeadSalesType::query()
                ->where('key_value', $key)
                ->first();

            $requiredSalesTypes[] = [
                'lead_sales_type_id'  => $legacySalesType->{LeadSalesType::ID},
                'max_daily_spend'     => $dailyLimitType === 'spend' ? $type['max_daily_spend'] : null,
                'max_daily_lead'      => $dailyLimitType === 'lead' ? $type['max_daily_leads'] : null,
                'status'              => $type['status'] ?? false,
            ];
        }
        return $requiredSalesTypes;
    }
}
