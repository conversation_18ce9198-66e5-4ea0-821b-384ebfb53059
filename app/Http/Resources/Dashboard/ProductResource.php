<?php

namespace App\Http\Resources\Dashboard;

use App\Enums\Odin\AppointmentCancellationReason;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\Odin\Industry;
use App\Enums\Odin\Product;
use App\Enums\RejectionReasons;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\Legacy\EloquentInvoiceItem;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Odin\Address;
use App\Models\Odin\Product as ProductModel;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCancellation;
use App\Models\Odin\ProductRejection;
use App\Models\SaleType;
use Carbon\Carbon;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;
use JsonSerializable;

/**
 * @mixin ProductAssignment
 */
class ProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        $consumerProduct = $this->consumerProduct;
        $consumerProductTracking = $consumerProduct?->consumerProductTracking;
        $address = $consumerProduct?->address;
        $consumer = $consumerProduct?->consumer;
        $data = $consumerProduct->consumerProductData->toArray();
        $appointment = $this->appointment ?? null;
        $product = $consumerProduct->serviceProduct->product;

        $isSingleProductSale = !!$this->singleProductSale;
        $rejectionData = $this->getRejectionData($isSingleProductSale);

        $optInName = null;

        if($this->optInCompany) {
            $optInName = $this->optInCompany->companyOptInName ? $this->optInCompany->companyOptInName->name : $this->optInCompany->company->name;
        }

        return [
            'id'                  => $this->id,
            'legacy_id'           => $this->legacy_id,
            'consumer_product_id' => $consumerProduct->id,
            'property_type_id'    => $consumerProduct->property_type_id,
            'date_sent'           => $this->delivered_at->getTimestampMs(),
            'status'              => $this->getProductAssignmentStatus($this),
            'source'              => $consumerProductTracking->website?->name ?? "Fixr",
            'name'                => "$consumer->first_name $consumer->last_name",
            'address'             => $address?->getFullAddress() ?? "-",
            'street_address'      => $address?->getFullStreetAddress() ?? "-",
            'city'                => $address?->{Address::FIELD_CITY} ?? "-",
            'state'               => $address?->{Address::FIELD_STATE} ?? "-",
            'zip_code'            => $address?->{Address::FIELD_ZIP_CODE} ?? "-",
            'email'               => $consumer->email,
            'phone'               => $this->proxy_phone && $this->proxy_phone_active ? $this->proxy_phone : $consumer->phone,
            'cost'                => $this->cost,
            'effective_cost'      => $this->chargeable ? $this->cost : 0,
            'chargeable'          => $this->chargeable,
            'exclude_budget'      => $this->exclude_budget,
            'best_time_to_call'   => $this->consumerProduct->consumerProductData->payload?->{GlobalConfigurableFields::BEST_TIME_TO_CALL} ?? $this->payload?->{GlobalConfigurableFields::BEST_TIME_TO_CALL_OTHER} ?? "",
            'invoice_id'          => $this->getInvoiceId(),
            'sale_type'           => $this->saleType?->name ?? "",
            'campaign_name'       => $this->{ProductAssignment::RELATION_BUDGET}?->{Budget::RELATION_BUDGET_CONTAINER}?->{BudgetContainer::RELATION_CAMPAIGN}?->{CompanyCampaign::FIELD_NAME},
            'data'                => array_filter($data, fn($key) => $key !== 'id', ARRAY_FILTER_USE_KEY),
            'appointment'         => $appointment ? new AppointmentResource($appointment) : [],
            'appointment_status'  => $this->getAppointmentStatus($this),
            'single_product_sale' => $isSingleProductSale,
            'demoted_status'      => $this->getDemotedStatus($product),
            'opt_in_name'         => $optInName,
            ...$rejectionData,
        ];
    }

    /**
     * @return int|null
     */
    public function getInvoiceId(): ?int
    {
        return $this->odinInvoiceItem?->invoice_id ?? $this->invoiceItem?->invoiceid;
    }

    /**
     * @param bool $isSingleProductSale
     * @return array
     */
    private function getRejectionData(bool $isSingleProductSale): array
    {
        if ($isSingleProductSale) {
            return [];
        }

        return [
            'rejection_expiry' => $this->rejection_expiry->getTimestampMs(),
            'rejection_timer'  => $this->getRejectionTimeRemaining($this->rejection_expiry),
            'rejected'         => !!$this->productRejections->count(),
            'rejection_reason' => $this->getRejectionReason(),
            'rejected_time'    => $this->getRejectedTime()
        ];
    }

    /**
     * @return string
     */
    private function getRejectionReason(): string
    {
        $rejectionReason = $this->productRejections->whereNull(ProductRejection::FIELD_DELETED_AT)?->first()?->{ProductRejection::FIELD_REASON};

        if (empty($rejectionReason)) {
            return '';
        }

        $rejectionReasonParts = explode('|', $rejectionReason);

        $primaryReason = RejectionReasons::tryFrom($rejectionReasonParts[0])?->getText() ?? RejectionReasons::OTHER->getText();

        $note = $rejectionReasonParts[1];

        return "{$primaryReason}" . (!empty($note) ? ": {$note}" : "");
    }

    /**
     * @return int|null
     */
    private function getRejectedTime(): ?int
    {
        return $this->productRejections->whereNull(ProductRejection::FIELD_DELETED_AT)?->first()?->{ProductRejection::CREATED_AT}?->timestamp ?? null;
    }

    /**
     * @param Carbon $rejectionExpiry
     * @return int|null
     */
    private function getRejectionTimeRemaining(Carbon $rejectionExpiry): ?int
    {
        return Carbon::now() > $rejectionExpiry
            ? null
            : (int)Carbon::now()->diffInMilliseconds($rejectionExpiry, true);
    }

    /**
     * @param ProductResource $product
     * @return string
     */
    private function getProductAssignmentStatus(ProductResource $product): string
    {
        if ($product->productRejections->count() > 0) {
            return 'rejected';
        } else if ($product->chargeable) {
            return 'purchased';
        } else return 'non_chargeable';
    }

    /**
     * @param ProductResource $product
     *
     * @return string
     */
    private function getAppointmentStatus(ProductResource $product): string
    {
        if ($product->productRejections->isNotEmpty())
            return 'rejected';

        if ($product->productCancellations->isNotEmpty()) {
            /** @var ProductCancellation $productCancellation */
            $productCancellation = $product->productCancellations->first();

            if ($productCancellation->reason == AppointmentCancellationReason::RESCHEDULED)
                return 'rescheduled';

            return 'canceled';
        }

        return 'purchased';
    }

    /**
     * @param ProductModel $product
     * @return string|null
     */
    private function getDemotedStatus(ProductModel $product): ?string
    {
        if ($this->parent_product_id) {
            /** @var ProductModel $parentProduct */
            $parentProduct = ProductModel::query()->find($this->parent_product_id);
            if ($parentProduct->name === Product::APPOINTMENT->value && $product->name === Product::LEAD->value)
                return "Lead from cancelled appointment";
        }

        return null;
    }
}
