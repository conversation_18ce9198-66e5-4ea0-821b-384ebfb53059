<?php

namespace App\Http\Resources\Dashboard;

use App\Models\Odin\CompanyLocation;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin CompanyLocation
 */
class CompanyLocationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        $address = $this->address;

        return [
            'id'        => $this->id,
            'name'      => $this->name,
            'phone'     => $this->phone,
            'is_primary' => $this->is_primary,
            'address'   => new AddressResource($address),
        ];
    }
}
