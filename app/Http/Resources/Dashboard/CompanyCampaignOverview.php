<?php

namespace App\Http\Resources\Dashboard;

use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Company;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin Company
 */
class CompanyCampaignOverview extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request)
    {
        return [
            "status" => true,
            "campaign_count" => $this->campaigns()->count(), // TODO: Investigate once using A2 models and scope to services.
            "paused_campaigns" => $this->campaigns()->where(LeadCampaign::STATUS, LeadCampaign::STATUS_INACTIVE)->count(),
        ];
    }
}
