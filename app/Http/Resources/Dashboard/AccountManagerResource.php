<?php

namespace App\Http\Resources\Dashboard;

use App\Models\AccountManager;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin AccountManager
 */
class AccountManagerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request)
    {
        return [
            "name" => $this->user->name,
            "email" => $this->user->email,
            "phone" => $this->user->primaryPhone()?->phone,
            "meetingUrl" => null, // TODO: This
        ];
    }
}
