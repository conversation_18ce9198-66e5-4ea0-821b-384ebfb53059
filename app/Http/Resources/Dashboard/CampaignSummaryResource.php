<?php

namespace App\Http\Resources\Dashboard;

use App\Enums\Odin\Product as ProductEnum;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignLeadCategory;
use App\Models\Legacy\LeadCategory;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ProductCampaignBudget;
use App\Models\Odin\PropertyType;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Collection;
use JsonSerializable;

/**
 * @mixin LeadCampaign
 */
class CampaignSummaryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        return [
            'id'                        => $this->uuid,
            'name'                      => $this->name,
            'budgets'                   => $this->getCampaignBudget(),
            'status'                    => $this->getCampaignStatus($this->product_campaign_id, $this->status),
            'property_types'            => $this->getPropertyTypes($this->leadCampaignLeadCategories),
            'state_locations_count'     => $this->lead_campaign_state_locations_count ?? 0,
            'zip_locations_count'       => $this->lead_campaign_zip_locations_count ?? 0,
            'statistics'                => $this->statistics ?? [],
            'product_campaign_id'       => $this->product_campaign_id ?? null,
            'reactivate_date'           => date('d M, Y - h:i A', strtotime($this->reactivate_date)),
        ];
    }

    protected function getCampaignBudget(): array
    {
        $productName = $this->productCampaign?->product->name ?? null;

        return match($productName) {
            ProductEnum::APPOINTMENT->value => $this->productCampaign->budgets->mapWithKeys(fn(ProductCampaignBudget $budget) => [
                    $budget->quality_tier->value => $budget->formatted_budget ?? "",
                ])->toArray(),
            default                         => [
                'daily_spend'     => $this->daily_spend,
                'max_daily_spend' => $this->max_daily_spend,
                'max_daily_lead'  => $this->max_daily_lead,
            ]
        };
    }

    /**
     * @param int $productCampaignId
     * @param bool $legacyStatus
     *
     * @return bool
     */
    protected function getCampaignStatus(int $productCampaignId, bool $legacyStatus): bool
    {
        /** @var ProductCampaign $productCampaign */
        $productCampaign = ProductCampaign::query()->findOrFail($productCampaignId);

        if ($productCampaign->product->name === ProductEnum::LEAD->value) return $legacyStatus;

        return $productCampaign->status;
    }

    private function getPropertyTypes(Collection $leadCategories): array
    {
        $ids = $leadCategories->pluck(LeadCampaignLeadCategory::LEAD_CATEGORY_ID)->toArray();
        $names = LeadCategory::query()
            ->whereIn(LeadCategory::ID, $ids)
            ->pluck(LeadCategory::NAME)
            ->toArray();

        return PropertyType::query()
            ->whereIn(PropertyType::FIELD_NAME, $names)
            ->get()
            ->map(fn(PropertyType $propertyType) => [
                'id'    => $propertyType->id,
                'name'  => $propertyType->name
            ])->toArray();
    }
}
