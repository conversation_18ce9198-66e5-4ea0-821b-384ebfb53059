<?php

namespace App\Http\Resources\Dashboard;

use App\Enums\CompanyMediaAssetType;
use App\Models\Odin\CompanyMediaAsset;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;
use Exception;

/**
 * @mixin CompanyMediaAsset
 */
class CompanyMediaAssetResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     * @throws Exception
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        return [
            'id'    => $this->id,
            'name'  => $this->name,
            'url'   => $this->url,
            'type'  => CompanyMediaAssetType::displayName($this->type),
        ];
    }
}
