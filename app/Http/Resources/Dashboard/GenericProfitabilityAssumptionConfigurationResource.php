<?php

namespace App\Http\Resources\Dashboard;

use App\Models\Odin\GenericProfitabilityAssumptionConfiguration;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin GenericProfitabilityAssumptionConfiguration
 */
class GenericProfitabilityAssumptionConfigurationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param  Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id'                            => $this->id,
            'percentage_leads_successful'   => $this->percentage_leads_successful,
            'average_lead_revenue'          => $this->average_lead_revenue,
            'average_job_cost'              => $this->average_job_cost,
            'labour_materials_cost'         => $this->labour_materials_cost,
            'company'                       => $this->company?->name,
            'service'                       => $this->service?->slug,
            'industry_service_id'           => $this->industry_service_id,
            'is_default'                    => $this->is_default
        ];
    }
}
