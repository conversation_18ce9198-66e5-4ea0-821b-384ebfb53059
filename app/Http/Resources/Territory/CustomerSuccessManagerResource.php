<?php

namespace App\Http\Resources\Territory;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Territory\CustomerSuccessManager;
use App\Models\User;
use Illuminate\Http\Client\Request;

/**
 * @mixin CustomerSuccessManager
 */
class CustomerSuccessManagerResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id'            => $this->{CustomerSuccessManager::FIELD_ID},
            'name'          => $this->{CustomerSuccessManager::RELATION_USER}->{User::FIELD_NAME},
            'email'         => $this->{CustomerSuccessManager::RELATION_USER}->{User::FIELD_EMAIL},
            'created_by'    => $this->{CustomerSuccessManager::FIELD_CREATED_BY_ID},
        ];
    }
}
