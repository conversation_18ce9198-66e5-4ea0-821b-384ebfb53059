<?php

namespace App\Http\Resources;

use App\Models\CompanyManagerAssignmentRequest;
use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin CompanyManagerAssignmentRequest
 */
class CompanyManagerAssignmentRequestResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $currentOwner = $this->company->currentlyAssigned($this->role->name);

        return [
            CompanyManagerAssignmentRequest::FIELD_ID => $this->id,
            CompanyManagerAssignmentRequest::FIELD_STATUS => $this->status,
            'role' => str($this->role->name)->headline(),
            'user' => $this->user->only([User::FIELD_ID, User::FIELD_NAME]),
            'company' => [
                Company::FIELD_ID => $this->company->id,
                Company::FIELD_NAME => $this->company->name,
                'profile_link' => $this->company->getAdminProfileUrl(),
            ],
            'current_owner' => $this->when($request->isMethod('get'), $currentOwner?->name),
            'requested_by_current_owner' => $this->when($request->isMethod('get'), $currentOwner?->is($this->user) ?? false),
            'date_decided' => $this->decided_at?->setTimezone($request->user()->timezone?->value ?? 'UTC')->format('F d, Y h:i a T'),
            'deciding_user' => $this->deciding_user?->only(['id', 'name']),
            'date_requested' => $this->created_at->setTimezone($request->user()->timezone?->value ?? 'UTC')->format('F d, Y h:i a T')
        ];
    }
}
