<?php

namespace App\Http\Resources;

use App\Models\Alert;
use App\Models\AlertRecipient;
use App\Models\Odin\CompanyUser;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;

/**
 * @mixin Alert
 */
class AlertResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //todo: implement internal/external alerts
        return [
            'id' => $this->id,
            'type' => $this->type,
            'type_read' => $this->type->toReadableString(),
            'active' => $this->active,
            'payload' => $this->payload,
            'last_alerted_at' => $this->last_alerted_at?->timestamp,
            'recipients' => $this->recipients->map(fn(AlertRecipient $recipient) => [
                'id' => $recipient->notifiable->id,
                'model' => $recipient->notifiable::class,
                'phone' => $this->getRecipientPhone($recipient->notifiable),
                'email' => $recipient->notifiable->email,
                'name' => $this->getRecipientName($recipient->notifiable),
                'channel' => $recipient->channel
            ])
        ];
    }

    /**
     * @param mixed $recipient
     *
     * @return string|null
     */
    protected function getRecipientPhone(mixed $recipient): ?string
    {
        return match ($recipient::class) {
            User::class => $recipient->phones()->first()?->phone,
            CompanyUser::class => $recipient->cell_phone,
            default => null
        };
    }

    /**
     * @param mixed $recipient
     *
     * @return string|null
     */
    protected function getRecipientName(mixed $recipient): ?string
    {
        return match ($recipient::class) {
            User::class => $recipient->name,
            CompanyUser::class => $recipient->completeName(),
            default => null
        };
    }
}
