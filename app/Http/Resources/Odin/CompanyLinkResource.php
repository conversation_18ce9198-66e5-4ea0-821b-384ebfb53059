<?php

namespace App\Http\Resources\Odin;

use App\Models\Odin\Company;
use App\Models\Odin\ProductAssignment;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;
use function Clue\StreamFilter\fun;

/**
 * Class CompanyLinkResource
 *
 */
class CompanyLinkResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        $relatedCompany =  (integer)$request->route('company_id') === $this->company_id_one ? $this->companyTwo : $this->companyOne;

        return [
            'id'             => $this->id,
            'linked_company' => $this->getCompanyFields($relatedCompany),
            'comment'        => $this->comment,
            'created_at'     => $this->created_at,
            'updated_at'     => $this->updated_at,
        ];
    }

    private function getCompanyFields(Company $company){
        return [
            'name' => $company->{Company::FIELD_NAME},
            'id'    =>$company->{Company::FIELD_ID},
            'status' => Company::STATUSES_STRING[$company->status],
            'industries' => $company->industries->map(function ($industry) {
                return [
                    'name' => $industry->name
                ];
            }),
        ];
    }
}
