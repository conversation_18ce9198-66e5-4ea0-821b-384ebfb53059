<?php

namespace App\Http\Resources\Odin;

use App\Models\Odin\RulesetScore;
use Illuminate\Http\Client\Request;
use Illuminate\Http\Resources\Json\JsonResource;


class CompanyQualityScoreResource extends JsonResource
{
    const FIELD_COMPANY_QUALITY_SCORE_DATA = 'score_data';
    const FIELD_QUALITY_SCORE_CALCULATED_AT = 'calculated_at';
    const FIELD_QUALITY_SCORE = 'score';
    const FIELD_RULESET = 'ruleset';

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            self::FIELD_RULESET                         => new RulesetResource($this->ruleset),
            self::FIELD_COMPANY_QUALITY_SCORE_DATA      => $this->{RulesetScore::FIELD_DATA},
            self::FIELD_QUALITY_SCORE_CALCULATED_AT     => $this->{RulesetScore::FIELD_CALCULATED_AT},
            self::FIELD_QUALITY_SCORE                   => $this->{RulesetScore::FIELD_SCORE},
        ];
    }
}
