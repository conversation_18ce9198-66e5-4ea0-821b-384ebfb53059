<?php

namespace App\Http\Resources\Odin;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\Json\PaginatedResourceResponse;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class BaseJsonResource extends JsonResource
{
    const int  DEFAULT_PAGE     = 1;
    const int  DEFAULT_PER_PAGE = 10;
    const bool DEFAULT_ALL      = false;

    /**
     * Get the per page number
     * @param int|null $perPage
     * @return int
     */
    public static function getPerPage(?int $perPage = null): int
    {
        $perPage = intval($perPage
            ?? request()->input('perPage')
            ?? request()->input('per_page')
            ?? self::DEFAULT_PER_PAGE);

        return $perPage <= 0 ? self::DEFAULT_PER_PAGE : $perPage;
    }

    /**
     * Get the current page number
     * @param int|null $page
     * @return int
     */
    private static function getPage(?int $page = null): int
    {
        $page = intval($page ?? request()->input('page') ?? self::DEFAULT_PAGE);

        return $page <= 0 ? self::DEFAULT_PAGE : $page;
    }

    /**
     * @param bool|null $all
     * @return bool
     */
    private static function getAll(?bool $all = null): bool
    {
        return $all ?? (bool)request()->input('all') ?? self::DEFAULT_ALL;
    }

    /**
     * @param LengthAwarePaginator $builder
     * @param int|null $perPage
     * @param int|null $page
     * @return array
     */
    static function paginate(mixed $builder, ?int $perPage = null, ?int $page = null): array
    {
        $page = self::getPage($page);
        $perPage = self::getPerPage($perPage);

        $response = self::collection($builder instanceof LengthAwarePaginator ? $builder : $builder->paginate($perPage, ['*'], 'page', $page));
        $response = new PaginatedResourceResponse($response);

        return json_decode($response->toResponse(request())->content(), true);
    }

    /**
     * if the request contains an 'all' field that is true, will return collection, else pagination.
     *
     * @param $builder
     * @param bool|null $all
     * @return array
     */
    static function format($builder, ?bool $all = null): array
    {
        $all = self::getAll($all);

        return $all ? self::formatToCollection($builder) : self::paginate($builder);
    }

    static function formatToCollection($builder): array
    {
        $collection = $builder instanceof Collection ? $builder : $builder?->get();

        return [
            'data' => self::collection($collection)->toArray(request())
        ];
    }
}
