<?php

namespace App\Http\Resources\Odin;

use App\Models\Odin\ConsumerProduct;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Number;
use JsonSerializable;

/**
 * @mixin ConsumerProduct
 */
class ConsumerProductSearchResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        $product = $this->serviceProduct?->product;
        $companies = $this->productAssignment?->map(fn($pa) =>
        $pa->company?->name ? [
            'name'  => $pa->company->name,
            'link'    => route('companies.index', $pa->company->id),
            'cost'  => Number::currency($pa->cost),
            'delivered' => $pa->delivered
        ] : "");

        return [
            'id'                    => $this->id,
            'product'               => $product?->name ?? "",
            'created_at'            => $this->created_at,
            'companies_assigned'    => $companies,
            'is_test_lead'          => !!$this->testProduct,
            'secondary_service'     => $this->getSecondaryService(),
        ];
    }

    protected function getSecondaryService(): ?string
    {
        return $this->is_secondary_service
            ? $this->serviceProduct?->service?->name ?? 'Unknown Service'
            : null;
    }

}
