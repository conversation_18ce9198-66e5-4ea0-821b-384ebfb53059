<?php

namespace App\Http\Resources\Odin;

use App\Enums\Odin\LocationSiloPageLocationType;
use App\Models\Legacy\Location;
use App\Models\Odin\LocationSiloPage;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin LocationSiloPage
 */
class LocationSiloPageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        $location = $this->location ?? null;
        $locationType = $this->location_type;

        return [
            'id'            => $this->id,
            'is_active'     => $this->is_active,
            'entry_slug'    => $this->entry_slug,
            'relative_path' => $this->relative_path,
            'location_type' => $this->location_type->value,
            'location'      => [
                'id'            => $location?->id ?? 0,
                'parent_id'     => $this?->parent_location_id ?? null,
                'name'          => $location ? $this->getLocationName($location, $locationType) : 'US',
            ],
        ];
    }

    private function getLocationName(Location $location, LocationSiloPageLocationType $locationType): string
    {
        return match($locationType) {
            LocationSiloPageLocationType::NATIONAL  => 'US',
            LocationSiloPageLocationType::STATE     => $location->state,
            LocationSiloPageLocationType::CITY      => $location->city,
        };
    }
}
