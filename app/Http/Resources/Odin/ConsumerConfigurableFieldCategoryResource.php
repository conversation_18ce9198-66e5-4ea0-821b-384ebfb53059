<?php

namespace App\Http\Resources\Odin;

use App\Models\Odin\ConsumerConfigurableFieldCategory;
use Illuminate\Http\Resources\Json\JsonResource;

class ConsumerConfigurableFieldCategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            ConsumerConfigurableFieldCategory::FIELD_ID => $this->{ConsumerConfigurableFieldCategory::FIELD_ID},
            ConsumerConfigurableFieldCategory::FIELD_NAME => $this->{ConsumerConfigurableFieldCategory::FIELD_NAME},
            ConsumerConfigurableFieldCategory::FIELD_SLUG => $this->{ConsumerConfigurableFieldCategory::FIELD_SLUG},
        ];
    }
}
