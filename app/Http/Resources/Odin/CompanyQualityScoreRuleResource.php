<?php

namespace App\Http\Resources\Odin;

use App\Models\Odin\CompanyQualityScoreRule;
use Illuminate\Http\Client\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CompanyQualityScoreRuleResource extends JsonResource
{
    const FIELD_ID              = 'id';
    const FIELD_NAME            = 'name';
    const FIELD_INDUSTRY        = 'industry';
    const FIELD_IS_PRODUCTION   = 'is_production';
    const FIELD_RULESET         = 'ruleset';
    const FIELD_TOTAL_POINTS    = 'total_points';
    const FIELD_CREATED_AT      = 'created_at';
    const FIELD_UPDATED_AT      = 'updated_at';

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            self::FIELD_ID              => $this->{CompanyQualityScoreRule::FIELD_ID},
            self::FIELD_NAME            => $this->{CompanyQualityScoreRule::FIELD_NAME},
            self::FIELD_IS_PRODUCTION   => !!$this->{CompanyQualityScoreRule::FIELD_IS_PRODUCTION},
            self::FIELD_INDUSTRY        => $this->{CompanyQualityScoreRule::RELATION_INDUSTRY},
            self::FIELD_RULESET         => $this->{CompanyQualityScoreRule::RELATION_RULESET},
            self::FIELD_TOTAL_POINTS    => $this->{CompanyQualityScoreRule::FIELD_TOTAL_POINTS}, //TODO - this should come from the ruleset table once the model is updated
            self::FIELD_CREATED_AT      => $this->{CompanyQualityScoreRule::FIELD_CREATED_AT},
            self::FIELD_UPDATED_AT      => $this->{CompanyQualityScoreRule::FIELD_UPDATED_AT},
        ];
    }
}
