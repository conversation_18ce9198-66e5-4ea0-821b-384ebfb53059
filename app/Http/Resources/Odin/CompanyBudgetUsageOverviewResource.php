<?php

namespace App\Http\Resources\Odin;

use App\Http\Resources\CompanyCampaign\CampaignBudgetUsageResource;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;


/**
 * @mixin array
 */
class CompanyBudgetUsageOverviewResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        return [
            'unlimited' => $this['unlimited'],
            'overall'   => round($this['average'] * 100, 2) . '%',
            'campaigns' => CampaignBudgetUsageResource::collection($this['data'])
        ];
    }
}
