<?php

namespace App\Http\Resources\Odin;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Carbon;
use JsonSerializable;

class PrivacyRequestRedactionRecordsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        return [
            'id'            => $this->id,
            'user_name'     => $this->initiator?->name,
            'model_id'      => $this->model_id,
            'model_type'    => $this->model_type,
            'status'        => $this->status,
            'updated_at'    => Carbon::parse($this->updated_at)->format('Y-m-d'),
        ];
    }
}