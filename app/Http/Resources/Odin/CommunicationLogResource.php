<?php

namespace App\Http\Resources\Odin;

use App\Enums\CommunicationRelationTypes;
use App\Models\Call;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Consumer;
use App\Services\Odin\ContactIdentification\ContactIdentificationService;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin Call
 */
class CommunicationLogResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     * @throws \Exception
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        if ($this->relation_type === CommunicationRelationTypes::COMPANY_USER->value && $this->companyUser) {
            $contact = [
                ContactIdentificationService::FIELD_CONTACT_NAME            => $this->companyUser->completeName(),
                ContactIdentificationService::FIELD_CONTACT_PHONE           => $this->companyUser->{CompanyUser::FIELD_CELL_PHONE},
                ContactIdentificationService::FIELD_CONTACT_RELATION_ID     => $this->companyUser->{CompanyUser::FIELD_ID},
                ContactIdentificationService::FIELD_CONTACT_RELATION_TYPE   => $this->relation_type,
                ContactIdentificationService::FIELD_RELATION_DATA           => [
                    ContactIdentificationService::FIELD_CONTACT_RELATION_DATA_NAME  => $this->companyUser->company->name,
                    ContactIdentificationService::FIELD_CONTACT_RELATION_DATA_ID    => $this->companyUser->company_id,
                ],
                ContactIdentificationService::FIELD_CONTACT_COMMENT         => $this->companyUser->company->name.' #'.$this->companyUser->company_id
            ];
        } elseif (in_array($this->relation_type, [CommunicationRelationTypes::CONSUMERS->value, CommunicationRelationTypes::LEAD->value, CommunicationRelationTypes::CONSUMER_PRODUCT->value])) {
            $consumer = $this->getConsumer();
            if ($consumer) {
                $contact = [
                    ContactIdentificationService::FIELD_CONTACT_NAME          => $consumer->getFullName(),
                    ContactIdentificationService::FIELD_CONTACT_PHONE         => $consumer->{Consumer::FIELD_PHONE},
                    ContactIdentificationService::FIELD_CONTACT_RELATION_ID   => $consumer->{Consumer::FIELD_ID},
                    ContactIdentificationService::FIELD_CONTACT_RELATION_TYPE => $this->relation_type,
                    ContactIdentificationService::FIELD_RELATION_DATA         => [
                        ContactIdentificationService::FIELD_CONTACT_RELATION_DATA_NAME => 'Consumer',
                        ContactIdentificationService::FIELD_CONTACT_RELATION_DATA_ID   => $consumer->id,
                    ],
                ];
            }
        }

        return [
            "call_duration_in_seconds"  => ($this->call_end && $this->call_start) ? (int) $this->call_end->diffInSeconds($this->call_start, true) : null,
            "communication_type"        => class_basename($this->resource),
            "relation_type"             => $this->relation_type,
            "other_number"              => $this->other_number,
            "relation_id"               => $this->relation_id,
            "created_at"                => $this->created_at,
            "result"                    => $this->result,
            "direction"                 => $this->direction,
            "phone_id"                  => $this->phone_id,
            "phone"                     => $this->phone,
            "id"                        => $this->id,
            "contact"                   => $contact ?? null,
        ];
    }
}
