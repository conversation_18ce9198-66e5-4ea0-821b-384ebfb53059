<?php

namespace App\Http\Resources\Odin;

use App\Enums\CompanyCampaignSource;
use App\Models\Campaigns\CompanyCampaign;

/**
 * @mixin CompanyCampaign
 */
class CompanyCampaignListOptionResource extends ListOptionResource
{
    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->{CompanyCampaign::FIELD_ID};
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->{CompanyCampaign::FIELD_NAME} . " (" . $this->service?->industry?->name  . ")";
    }

    /**
     * @return array
     */
    public function getPayload(): array
    {
        return [
            'source_description'    => CompanyCampaignSource::COMPANY_CAMPAIGN->getDescription(),
            'source'                => CompanyCampaignSource::COMPANY_CAMPAIGN->value,
            'status'                => $this->status
        ];
    }
}
