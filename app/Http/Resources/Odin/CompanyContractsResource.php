<?php

namespace App\Http\Resources\Odin;

use App\Models\CompanyContract;
use App\Models\Contract;
use App\Models\ContractKey;
use App\Models\Odin\Website;
use App\Services\CloudStorage\GoogleCloudStorageService;
use App\Services\CompanyContractService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class CompanyContractsResource extends JsonResource
{
    /**
     * Transform the resource collection into an array.
     *
     * @param Request $request
     *
     * @return array|Arrayable|JsonSerializable
     * @throws BindingResolutionException
     */
    public function toArray(Request $request): array|Arrayable|JsonSerializable
    {
        $companyContractService = app()->make(CompanyContractService::class);

        return [
            'id'                => $this->{CompanyContract::FIELD_ID},
            'created_at'        => $this->{CompanyContract::FIELD_CREATED_AT},
            'company_id'        => $this->{CompanyContract::FIELD_COMPANY_ID},
            'company_user_id'   => $this->{CompanyContract::FIELD_COMPANY_USER_ID},
            'contract_type'     => $this->{CompanyContract::FIELD_CONTRACT_TYPE},
            'agreed_at'         => $this->{CompanyContract::FIELD_AGREED_AT},
            'ip_address'        => $this->{CompanyContract::FIELD_IP_ADDRESS},
            'uuid'              => $this->{CompanyContract::FIELD_UUID},
            'company_user_name' => $this->{CompanyContract::RELATION_COMPANY_USER}?->completeName(),
            'contract_id'       => $this->{CompanyContract::FIELD_CONTRACT_ID},
            'signature_id'      => $this->{CompanyContract::FIELD_SIGNATURE_ID},
            'file_url'          => $companyContractService->getSignedContractFileUrl($this->{CompanyContract::FIELD_FILE_URL}),
            'contract'          => [
                'description'   => $this->{CompanyContract::RELATION_CONTRACT}?->{Contract::FIELD_DESCRIPTION},
                'key_name'      => $this->{CompanyContract::RELATION_CONTRACT}?->{Contract::RELATION_CONTRACT_KEY}?->{ContractKey::FIELD_NAME},
                'origin_name'   => $this->{CompanyContract::RELATION_CONTRACT}?->{Contract::RELATION_WEBSITE}?->{Website::FIELD_NAME},
                'status'        => $this->{CompanyContract::RELATION_CONTRACT}?->{Contract::FIELD_ACTIVE}
            ],
        ];
    }

}
