<?php
namespace App\Http\Resources\Odin;
use App\Models\Odin\Consumer;
use Illuminate\Contracts\Support\Arrayable;
use JsonSerializable;
/**
 * @mixin Consumer
 */
class LeadsReportFilterOptionsResource
{
    const string FILTER_LEAD_STATUSES     = 'lead_statuses';
    const string FILTER_DELIVERED_STATUSES = 'delivered_statuses';
    const string FILTER_CLASSIFICATION     = 'classification_statuses';
    const string FILTER_INDUSTRIES         = 'industries';
    const string FILTER_REQUESTED_AMOUNTS  = 'requested_amounts';
    const string FILTER_LEAD_TYPES         = 'lead_types';
    const string FILTER_LEAD_CATEGORIES    = 'lead_categories';
    const string FILTER_LOCATIONS_STATES   = 'locations_states';
    const string FILTER_LEAD_SOURCES       = 'lead_sources';
    const string FILTER_YEARS              = 'years';

    /**
     * @param $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        return [
            self::FILTER_LEAD_STATUSES          => $this->formatOptions($this->resource->get(self::FILTER_LEAD_STATUSES, array())),
            self::FILTER_DELIVERED_STATUSES     => $this->formatOptions($this->resource->get(self::FILTER_DELIVERED_STATUSES, array())),
            self::FILTER_CLASSIFICATION         => $this->formatOptions($this->resource->get(self::FILTER_CLASSIFICATION, array())),
            self::FILTER_INDUSTRIES             => $this->formatOptions($this->resource->get(self::FILTER_INDUSTRIES, array())),
            self::FILTER_REQUESTED_AMOUNTS      => $this->formatOptions($this->resource->get(self::FILTER_REQUESTED_AMOUNTS, array())),
            self::FILTER_LEAD_TYPES             => $this->formatOptions($this->resource->get(self::FILTER_LEAD_TYPES, array())),
            self::FILTER_LEAD_CATEGORIES        => $this->formatOptions($this->resource->get(self::FILTER_LEAD_CATEGORIES, array())),
            self::FILTER_LOCATIONS_STATES       => $this->formatOptions($this->resource->get(self::FILTER_LOCATIONS_STATES, array())),
            self::FILTER_LEAD_SOURCES           => $this->formatOptions($this->resource->get(self::FILTER_LEAD_SOURCES, array())),
            self::FILTER_YEARS                  => $this->formatOptions($this->resource->get(self::FILTER_YEARS, array())),
        ];
    }

    /**
     * @param array $options
     * @return array
     */
    protected function formatOptions(array $options): array
    {
        $filterOptions = [];
        foreach ($options as $optionId => $optionName) {
            $filterOptions[] = self::formatOption($optionName, (string)$optionId);
        }
        return $filterOptions;
    }

    /**
     * @param string $optionName
     * @param string $optionId
     * @return string[]
     */
    protected static function formatOption(string $optionName, string $optionId): array
    {
        return ['name' => $optionName, 'id' => $optionId];
    }
}
