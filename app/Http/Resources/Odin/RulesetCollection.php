<?php

namespace App\Http\Resources\Odin;

use App\Models\Ruleset;
use Illuminate\Http\Resources\Json\ResourceCollection;

/**
 * Class RulesetCollection
 * @mixin Ruleset
 */
class RulesetCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray($request): array
    {
        return parent::toArray($request);
    }
}
