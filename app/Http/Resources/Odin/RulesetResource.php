<?php

namespace App\Http\Resources\Odin;

use App\Http\Resources\OpportunityNotifications\OpportunityNotificationConfigResource;
use App\Models\Ruleset;
use App\Services\Odin\Ruleset\Factories\RulesFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Client\Request;
use Illuminate\Http\Resources\Json\JsonResource;


class RulesetResource extends JsonResource
{
    const FIELD_ID          = 'id';
    const FIELD_NAME        = 'name';
    const FIELD_RULES       = 'rules';
    const FIELD_SOURCE      = 'source';
    const FIELD_FILTER      = 'filter';
    const FIELD_TYPE        = 'type';
    const FIELD_CREATED_AT  = 'created_at';
    const FIELD_UPDATED_AT  = 'updated_at';
    const CONFIG_COUNT      = 'op_notification_config_count';
    const CQS_COUNT         = 'cqs_count';
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        $rulesData = array_map(fn ($rule) => $rule->toArray(), RulesFactory::getCompanyRules($this->resource));

        return [
            self::FIELD_ID           => $this->{Ruleset::FIELD_ID},
            self::FIELD_SOURCE       => $this->{Ruleset::FIELD_SOURCE},
            self::FIELD_FILTER       => $this->{Ruleset::FIELD_FILTER},
            self::FIELD_TYPE         => $this->{Ruleset::FIELD_TYPE},
            self::FIELD_NAME         => $this->{Ruleset::FIELD_NAME}  ?? "Unknown",
            self::FIELD_RULES        => $rulesData,
            self::FIELD_CREATED_AT   => $this->{Model::CREATED_AT}?->toDateTimeString(),
            self::FIELD_UPDATED_AT   => $this->{Model::UPDATED_AT}?->toDateTimeString(),
            self::CONFIG_COUNT       => OpportunityNotificationConfigResource::collection($this->{Ruleset::RELATION_NOTIFICATION_CONFIGS})->count(),
            self::CQS_COUNT          => CompanyQualityScoreResource::collection($this->{Ruleset::RELATION_CQS})->count(),
        ];
    }
}
