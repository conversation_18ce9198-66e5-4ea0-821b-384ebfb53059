<?php

namespace App\Http\Resources\Odin;

use App\Enums\ContactDirectionEnum;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Consumer;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin Consumer
 */
class CompanyUsersFiltersResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     * @throws \Exception
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        $name  = $this->resource;

        return [
            'id'        => $name,
            'name'      => $name,
        ];
    }
}