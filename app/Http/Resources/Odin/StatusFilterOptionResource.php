<?php

namespace App\Http\Resources\Odin;

use App\Enums\Billing\InvoiceStates;

/**
 * @mixin InvoiceStates
 */
class StatusFilterOptionResource extends ListOptionResource
{
    /**
     * @return string
     */
    public function getId(): string
    {
        return $this->value;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->getTitle();
    }

    /**
     * @return array
     */
    public function getPayload(): array
    {
        return [];
    }
}
