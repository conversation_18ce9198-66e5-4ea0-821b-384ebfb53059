<?php

namespace App\Http\Resources\Odin;

use App\DTO\LeadRefund\LeadRefundItemPayload;
use App\Enums\Billing\BillingVersion;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use Illuminate\Http\Client\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

/**
 * @mixin LeadRefundItemPayload
 */
class LeadRefundItemDataResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        $productAssignment = ProductAssignment::query()->findOrFail($this->getProductAssignmentId());
        $consumerProduct = $productAssignment->{ProductAssignment::RELATION_CONSUMER_PRODUCT};
        $consumer = $consumerProduct->{ConsumerProduct::RELATION_CONSUMER};

        $billingVersion = $productAssignment->getBillingVersion();

        $invoiceId = $billingVersion === BillingVersion::V2
            ? $productAssignment->odinInvoiceItem->invoice_id
            : $productAssignment?->invoiceItem?->invoiceid;

        $campaignName = Str::replace(
            'admin_2_generated_',
            '',
            $productAssignment
                ?->leadCampaignSalesTypeConfiguration
                ?->{LeadCampaignSalesTypeConfiguration::RELATION_LEAD_CAMPAIGN}
                ?->{LeadCampaign::NAME}
            ?? "");

        return [
            'billing_version'       => $billingVersion->value,
            'product_assignment_id' => $this->getProductAssignmentId(),
            'invoice_id'            => $invoiceId,
            'cost'                  => $this->getCost(),
            'refund_type'           => $this->getType(),
            'refund_reason'         => $this->getRefundReason(),
            'id'                    => $consumer->{Consumer::FIELD_ID},
            'legacy_id'             => $consumer->{Consumer::FIELD_LEGACY_ID},
            'industry'              => $consumerProduct->{ConsumerProduct::RELATION_INDUSTRY_SERVICE}->{IndustryService::RELATION_INDUSTRY}->{Industry::FIELD_NAME},
            'service'               => $consumerProduct->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::RELATION_SERVICE}->{IndustryService::FIELD_NAME},
            'contact'               => $consumer->getFullName(),
            'campaign'              => $campaignName,
            'address'               => $consumerProduct->{ConsumerProduct::RELATION_ADDRESS}?->getFullAddress(),
        ];
    }
}
