<?php

namespace App\Http\Resources\Odin;

use Illuminate\Http\Client\Request;
use Illuminate\Http\Resources\Json\JsonResource;

abstract class ListOptionResource extends JsonResource
{
    const string FIELD_ID      = 'id';
    const string FIELD_NAME    = 'name';
    const string FIELD_PAYLOAD = 'payload';

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            self::FIELD_ID      => $this->getId(),
            self::FIELD_NAME    => $this->getName(),
            self::FIELD_PAYLOAD => $this->getPayload()
        ];
    }

    /**
     * @return int|string
     */
    abstract public function getId(): int|string;

    /**
     * @return string
     */
    abstract public function getName(): string;

    /**
     * @return array|null
     */
    public function getPayload(): array|null
    {
        return null;
    }
}
