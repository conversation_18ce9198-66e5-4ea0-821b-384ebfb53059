<?php

namespace App\Http\Resources\Odin;

use App\Builders\Odin\ConsumerSearchBuilder;
use App\Enums\Odin\Product as ProductEnum;
use App\Enums\PermissionType;
use App\Models\AvailableBudget;
use App\Models\LeadProcessingReservedLead;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\User;
use App\Repositories\CommunicationRepository;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;
use JsonSerializable;

/**
 * @mixin Consumer
 */
class ConsumerSearchResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        $consumerName = $this->last_name
            ? "$this->first_name $this->last_name"
            : $this->first_name;

        $consumerProducts = $this
            ->consumerProducts()
            ->orderBy(ConsumerProduct::FIELD_ID)
            ->get()
            ->filter(fn(ConsumerProduct $consumerProduct) => $consumerProduct->serviceProduct?->product->name !== ProductEnum::APPOINTMENT->value);

        $leadProduct = $consumerProducts->firstWhere(fn(ConsumerProduct $consumerProduct) => $consumerProduct?->serviceProduct?->product?->name === ProductEnum::LEAD->value)
            ?? $consumerProducts->first()
            ?? [];

        $marketingStrategyType = $consumerProducts->first()->marketingStrategyType();

        $tracking    = $leadProduct?->consumerProductTracking;
        $address     = $leadProduct?->address;
        $website     = $tracking?->website;
        $adTrackType = $tracking?->ad_track_type
            ? "Campaign Type: $tracking->ad_track_type"
            : "";
        $service     = $leadProduct?->industryService;
        $industry    = $service?->industry ?? "";
        $mapUrl      = $address->latitude && $address->longitude
            ? "https://google.com/maps/?q=$address->latitude,$address->longitude&t=h&z=19&hl=en"
            : "";

        $contactRequests = $leadProduct?->contact_requests ?? "";

        $newestProduct = $consumerProducts->first();

        $status = $newestProduct ? ConsumerProduct::STATUS_TEXT[$newestProduct->status] : null;

        $leadStatus = $consumerProducts
            ->filter(fn(ConsumerProduct $consumerProduct) => $consumerProduct->serviceProduct?->product->name === ProductEnum::LEAD->value)
            ->first()?->status ?? ConsumerProduct::STATUS_UNSOLD;

        /** @var ConsumerProduct|null $directLeadsProduct */
        $directLeadsProduct = $consumerProducts
            ->filter(fn(ConsumerProduct $consumerProduct) => $consumerProduct->serviceProduct?->product->name === ProductEnum::DIRECT_LEADS->value)
            ->first();

        $withAvailableBudget = $this->{AvailableBudget::FIELD_AVAILABLE_CAMPAIGN_COUNT} !== null;

        $canCall = app(CommunicationRepository::class)->canCallConsumer($this->resource);

        return [
            'id'                  => $this->id,
            'lead_id'             => $leadProduct->id,
            'created_at'          => $this->created_at,
            'origin'              => $website->name ?? "",
            'name'                => $consumerName,
            'first_name'          => $this->first_name,
            'last_name'           => $this->last_name,
            'uuid'                => $this->reference,
            'industry'            => $industry->name ?? "No Industry",
            'service'             => $service->name ?? "No Service",
            'good_to_sell'        => $newestProduct->good_to_sell,
            'status'              => $status,
            'lead_status'         => ConsumerProduct::STATUS_TEXT[$leadStatus],
            'appointment_status'  => null,
            'direct_leads_status' => $directLeadsProduct ? ConsumerProduct::STATUS_TEXT[$directLeadsProduct->status] : null,
            'contact_requests'    => $contactRequests,
            'verified'            => in_array($this->classification, Consumer::VERIFIED_CLASSIFICATIONS),
            'ad_track'            => $adTrackType,
            'consumer_url'        => config('app.url') . "/consumer-product/?consumer_product_id=" . $leadProduct->id,
            'map_url'             => $mapUrl,
            'consumer_products'   => ConsumerProductSearchResource::collection($consumerProducts),
            'marketing_strategy'  => $marketingStrategyType->value,
            'phone'               => $this->phone,
            ...$this->getObfuscatedConsumerData($address),
            'available_campaigns' => $withAvailableBudget ? $this->{AvailableBudget::FIELD_AVAILABLE_CAMPAIGN_COUNT} : null,
            'available_budget'    => $withAvailableBudget ? $this->getAvailableLocationBudget() : null,
            'locked'              => $leadProduct::class === ConsumerProduct::class ? $this->getLeadLocked($leadProduct) : null,
            'can_call'            => $canCall,
            'last_contacted_at'   => $this->{ConsumerSearchBuilder::ALIAS_LAST_CONTACTED_AT} ?? null,
            'cloned'              => !empty($this->cloned_from_id)
        ];
    }

    /**
     * @param Address|null $address
     * @return array{email: string, phone: string, address: null|string}
     */
    protected function getObfuscatedConsumerData(?Address $address = null): array
    {
        /** @var User $user */
        $user = Auth::user();
        if ($user->hasPermissionTo(PermissionType::VIEW_LEAD_PII->value)) {
            $data = [
                'email'   => $this->email,
                'address' => $address?->getFullAddress(),
            ];
        } else {
            $data = [
                'email'   => obfuscateEmail(email: $this->email),
                'address' => $address->getObfuscatedAddress(),
            ];
        }

        return $data;
    }

    /**
     * @return int
     */
    protected function getAvailableLocationBudget(): int
    {
        return $this->{AvailableBudget::FIELD_UNLIMITED_BUDGET_COUNT} > 0
            ? -1
            : ($this->{AvailableBudget::FIELD_BUDGET_AVAILABLE_DOLLARS} ?? 0)
            + (($this->{AvailableBudget::FIELD_BUDGET_AVAILABLE_VOLUME} ?? 0) * 140);
    }

    /**
     * @param ConsumerProduct $leadProduct
     * @return string|null
     */
    protected function getLeadLocked(ConsumerProduct $leadProduct): ?string
    {
        /** @var ?LeadProcessingReservedLead $leadProcessingReservedLead */
        $leadProcessingReservedLead = $leadProduct->{ConsumerProduct::RELATION_RESERVED};

        if (empty($leadProcessingReservedLead)) {
            return null;
        } else {
            return $leadProcessingReservedLead->processor_id === LeadProcessingReservedLead::SYSTEM_ID ? 'system' : 'user';
        }
    }
}
