<?php

namespace App\Http\Resources\Odin;

use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin Consumer
 */
class ConsumerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     *
     * TODO: Fill out this resource as required public-facing data is needed.
     * Sensitive data shouldn't go out on this Resource
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        /** @var ConsumerProduct $product */
        $product = $this->consumerProducts->first();
        $data = $product->consumerProductData;
        $address = $product->address;

        return [
            'reference'     => $this->reference,
        ];
    }
}
