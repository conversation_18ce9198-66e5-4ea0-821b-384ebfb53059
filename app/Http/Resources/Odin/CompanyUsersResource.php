<?php

namespace App\Http\Resources\Odin;

use App\Enums\ContactDirectionEnum;
use App\Models\Call;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Consumer;
use App\Models\Text;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin Consumer
 */
class CompanyUsersResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     * @throws \Exception
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        $nameAndOrTitle = $this?->{CompanyUser::FIELD_TITLE} ? $this?->{CompanyUser::FIELD_TITLE} . ': ' . $this?->completeName() : $this?->completeName();

        return [
            'id'                    => $this?->{CompanyUser::FIELD_ID},
            'title_name'            => $nameAndOrTitle,
            'decision_maker'        => $this?->{CompanyUser::FIELD_IS_DECISION_MAKER} ? 'Decision Maker': null,
            'status'                => CompanyUser::STATUS_MAPPING[$this?->{CompanyUser::FIELD_STATUS}],
            'phone_cell'            => $this?->{CompanyUser::FIELD_FORMATTED_CELL_PHONE},
            'phone_office'          => $this?->{CompanyUser::FIELD_FORMATTED_OFFICE_PHONE},
            'phone_verified'        => (bool)$this?->{CompanyUser::FIELD_PHONE_VERIFIED_AT},
            'email'                 => $this?->{CompanyUser::FIELD_EMAIL},
            'email_verified'        => (bool)$this?->{CompanyUser::FIELD_EMAIL_VERIFIED_AT},
            'company_name'          => $this?->company?->{Company::FIELD_NAME},
            'company_id'            => $this?->{CompanyUser::FIELD_COMPANY_ID},
            'last_contact'          => $this?->getContactInfo(),
            'notes'                 => $this?->{CompanyUser::FIELD_NOTES}
        ];
    }

    /**
     * @param CompanyUser $companyUser
     * @param ?ContactDirectionEnum $direction
     * @return ?string
     */
    private function getDate(CompanyUser $companyUser, ?ContactDirectionEnum $direction): ?string
    {
        if (class_basename($companyUser->latestContactByDirection($direction)) === 'Call') {
            return $companyUser->latestContactByDirection($direction)?->{Call::FIELD_CALL_END}->format('m/d/Y');
        } else {
            return $companyUser->latestContactByDirection($direction)?->{Text::FIELD_CREATED_AT}->format('m/d/Y');
        }

    }

    /**
     * @return array|false
     */
    public function getContactInfo(): array|false
    {
        return $this?->latest_contact?->{'created_at'} ? [
            'date' => self::getDate($this?->resource, null),
            'direction' => $this?->latest_contact?->{'direction'},
            'outbound' => $this?->latestContactByDirection(ContactDirectionEnum::OUTBOUND)?->{'created_at'} ? [
                'date' => self::getDate($this?->resource, ContactDirectionEnum::OUTBOUND),
                'type' => class_basename($this?->latestContactByDirection(ContactDirectionEnum::OUTBOUND)),
            ] : false,
            'inbound' => $this?->latestContactByDirection(ContactDirectionEnum::INBOUND)?->{'created_at'} ? [
                'date' => self::getDate($this?->resource, ContactDirectionEnum::INBOUND),
                'type' => class_basename($this?->latestContactByDirection(ContactDirectionEnum::INBOUND)),
            ] : false,
        ] : false;
    }
}
