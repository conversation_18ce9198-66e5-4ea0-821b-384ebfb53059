<?php

namespace App\Http\Resources\Odin\Company\Status;

use App\Enums\Company\CompanySystemStatus;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin CompanySystemStatus
 */
class SystemStatusResource extends JsonResource
{

    public function toArray(Request $request): array
    {
        return [
            'value' => $this->value,
            'label' => $this->label(),
            //todo: context
        ];
    }

}
