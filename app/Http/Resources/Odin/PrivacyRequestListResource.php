<?php

namespace App\Http\Resources\Odin;

use App\Models\PrivacyRequest;
use Exception;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use JsonSerializable;

/**
 * @mixin PrivacyRequest
 */
class PrivacyRequestListResource extends BaseJsonResource
{
    const string ID         = 'id';
    const string UUID       = 'uuid';
    const string STATUS     = 'status';
    const string TITLE      = 'title';
    const string SOURCE     = 'source';
    const string EMAIL      = 'email';
    const string CREATED_AT = 'created_at';
    const string UPDATED_AT = 'updated_at';

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     * @throws Exception
     */
    public function toArray(Request $request): array|Arrayable|JsonSerializable
    {
        return [
            self::ID         => $this->id,
            self::UUID       => $this->uuid,
            self::STATUS     => [
                self::TITLE => $this->status->getTitle(),
                self::ID    => $this->status->value
            ],
            self::SOURCE     => $this->source,
            self::EMAIL      => isset($this->payload[self::EMAIL]) ? $this->payload[self::EMAIL] : 'N/A',
            self::CREATED_AT => Carbon::parse($this->created_at)->format('Y-m-d'),
            self::UPDATED_AT => Carbon::parse($this->updated_at)->format('Y-m-d'),
        ];
    }
}