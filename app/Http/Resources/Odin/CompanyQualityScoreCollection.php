<?php

namespace App\Http\Resources\Odin;

use App\Models\Odin\CompanyQualityScoreRule;
use Illuminate\Http\Resources\Json\ResourceCollection;

/**
 * Class CompanyQualityScoreCollection
 * @mixin CompanyQualityScoreRule
 */
class CompanyQualityScoreCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray($request): array
    {
        return parent::toArray($request);
    }
}
