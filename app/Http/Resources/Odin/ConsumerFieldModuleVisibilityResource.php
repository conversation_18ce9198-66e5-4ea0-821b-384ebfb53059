<?php

namespace App\Http\Resources\Odin;

use App\Models\Odin\ConsumerFieldModuleVisibility;
use Illuminate\Http\Client\Request;
use Illuminate\Http\Resources\Json\JsonResource;


class ConsumerFieldModuleVisibilityResource extends JsonResource
{
    const FIELD_CONSUMER_FIELD_CATEGORY = 'consumer_field_category';
    const FIELD_CONSUMER_FIELD_CATEGORY_ID = 'consumer_field_category_id';
    const FIELD_CONSUMER_FIELD_ID   = 'consumer_field_id';
    const FIELD_MODULE_TYPE         = 'module_type';
    const FIELD_FEATURE_TYPE        = 'feature_type';
    const FIELD_IS_VISIBLE          = 'is_visible';

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            self::FIELD_CONSUMER_FIELD_CATEGORY => $this->{ConsumerFieldModuleVisibility::FIELD_CONSUMER_FIELD_CATEGORY},
            self::FIELD_CONSUMER_FIELD_CATEGORY_ID => $this->{ConsumerFieldModuleVisibility::FIELD_CONSUMER_FIELD_CATEGORY_ID},
            self::FIELD_CONSUMER_FIELD_ID => $this->{ConsumerFieldModuleVisibility::FIELD_CONSUMER_FIELD_ID},
            self::FIELD_MODULE_TYPE => $this->{ConsumerFieldModuleVisibility::FIELD_MODULE_TYPE},
            self::FIELD_FEATURE_TYPE => $this->{ConsumerFieldModuleVisibility::FIELD_FEATURE_TYPE},
            self::FIELD_IS_VISIBLE => $this->{ConsumerFieldModuleVisibility::FIELD_IS_VISIBLE},
        ];
    }
}
