<?php

namespace App\Http\Resources\Mailbox;

use App\Enums\Mailbox\EmailRecipientType;
use App\Http\Resources\ContactIdentification\IdentifiedContactResource;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Mailbox\MailboxEmail;
use App\Models\Mailbox\MailboxEmailRecipient;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Collection;

/**
 * @mixin MailboxEmail
 */
class MailboxEmailResource extends BaseJsonResource
{
    const string FIELD_RECIPIENTS = 'recipients';

    public function toArray($request): array
    {
        $from = new IdentifiedContactResource($this->{MailboxEmail::RELATION_IDENTIFIED_CONTACT}, $this->resource);

        /** @var Collection $recipients */
        $recipients = $this->{MailboxEmail::RELATION_RECIPIENTS}->mapToGroups(fn(MailboxEmailRecipient $recipient) => [
            $recipient->type => new IdentifiedContactResource($recipient->{MailboxEmailRecipient::RELATION_IDENTIFIED_CONTACT}, $recipient)
        ]);

        return [
            'email_id'    => $this->{MailboxEmail::FIELD_ID},
            'from'        => $from,
            'to'          => $recipients->get(EmailRecipientType::TO->value, []),
            'bcc'         => $recipients->get(EmailRecipientType::BCC->value, []),
            'cc'          => $recipients->get(EmailRecipientType::CC->value, []),
            'subject'     => $this->{MailboxEmail::FIELD_SUBJECT},
            'snippet'     => $this->{MailboxEmail::FIELD_SNIPPET},
            'content'     => $this->{MailboxEmail::FIELD_CONTENT},
            'attachments' => MailboxEmailAttachmentResource::collection($this->{MailboxEmail::RELATION_ATTACHMENTS}),
            'sent_at'     => $this->{MailboxEmail::FIELD_SENT_AT},
            'sent_timestamp' => $this->getSentTimestamp()
        ];
    }


    /**
     * @return float|int|string|null
     */
    protected function getSentTimestamp(): float|int|string|null
    {
        try {
            return Carbon::parse($this->{MailboxEmail::FIELD_SENT_AT})->timestamp;
        } catch (Exception $exception) {
            return null;
        }
    }
}
