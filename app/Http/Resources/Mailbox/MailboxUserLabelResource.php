<?php

namespace App\Http\Resources\Mailbox;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Mailbox\MailboxUserLabel;
use Illuminate\Http\Client\Request;

/**
 * @mixin MailboxUserLabel
 */
class MailboxUserLabelResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id'    =>  $this->{MailboxUserLabel::FIELD_ID},
            'name'  =>  $this->{MailboxUserLabel::FIELD_NAME},
        ];
    }
}
