<?php

namespace App\Http\Resources\Mailbox;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Mailbox\MailboxEmailAttachment;
use Illuminate\Http\Client\Request;

/**
 * @mixin MailboxEmailAttachment
 */
class MailboxEmailAttachmentResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'filename'  => $this->{MailboxEmailAttachment::FIELD_EXTERNAL_FILENAME},
            'url'       => $this->{MailboxEmailAttachment::FIELD_URL},
            'type'      => $this->{MailboxEmailAttachment::FIELD_EXTERNAL_TYPE},
        ];
    }
}
