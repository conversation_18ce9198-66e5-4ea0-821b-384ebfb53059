<?php

namespace App\Http\Resources\Mailbox;

use App\Enums\Mailbox\EmailDirection;
use App\Enums\Mailbox\EmailRecipientType;
use App\Http\Resources\ContactIdentification\IdentifiedContactResource;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Mailbox\MailboxEmail;
use App\Models\Mailbox\MailboxEmailLabel;
use App\Models\Mailbox\MailboxEmailRecipient;
use App\Models\Mailbox\MailboxUserEmail;
use App\Services\Mailbox\Mail\MailProvider;
use Google\Service\Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

/**
 * TODO - Merge the MailboxEmailResource
 * @mixin MailboxUserEmail
 */
class MailboxUserEmailResource extends BaseJsonResource
{

    const string FIELD_QUERY                = 'query';
    const string FIELD_MAILBOX_USER_EMAIL   = 'mailbox_user_email';
    const string FIELD_MAILBOX_USER_EMAILS  = 'mailbox_user_emails';
    const string FIELD_MAILBOX_EMAIL        = 'mailbox_email';
    const string FIELD_RECIPIENTS           = 'recipients';
    const string FIELD_MAILBOX_EMAIL_LABELS = 'mailbox_email_labels';
    const string FIELD_THREAD_COUNT         = 'thread_count';

    public function toArray($request): array
    {
        /** @var MailboxUserEmail $mailboxUserEmail */
        $mailboxUserEmail = $this->resource->get(self::FIELD_MAILBOX_USER_EMAIL);
        $mailboxEmail = $this->resource->get(self::FIELD_MAILBOX_EMAIL);
        $recipients = collect($this->resource->get(self::FIELD_RECIPIENTS));
        $emailLabels = collect($this->resource->get(self::FIELD_MAILBOX_EMAIL_LABELS));
        $threadCount = $this->resource->get(self::FIELD_THREAD_COUNT);

        $subject = $mailboxEmail->subject;
        $snippet = $mailboxEmail->snippet;
        $content = $mailboxEmail->content;

        $from = new IdentifiedContactResource($mailboxEmail->identifiedContact, $mailboxEmail);
        $to = $recipients->filter(fn(MailboxEmailRecipient $recipient) => $recipient->type === EmailRecipientType::TO->value);
        $bcc = $recipients->filter(fn(MailboxEmailRecipient $recipient) => $recipient->type === EmailRecipientType::BCC->value);
        $cc = $recipients->filter(fn(MailboxEmailRecipient $recipient) => $recipient->type === EmailRecipientType::CC->value);

        $to = $to->map(fn($recipient) => new IdentifiedContactResource($recipient->identifiedContact, $recipient));
        $bcc = $bcc->map(fn($recipient) => new IdentifiedContactResource($recipient->identifiedContact, $recipient));
        $cc = $cc->map(fn($recipient) => new IdentifiedContactResource($recipient->identifiedContact, $recipient));

        $allRecipients = $to->merge($bcc, $cc);

        return [
            'uuid'           => $mailboxUserEmail->{MailboxUserEmail::FIELD_UUID},
            'is_inbox'       => $mailboxUserEmail->{MailboxUserEmail::FIELD_IS_INBOX},
            'is_starred'     => $mailboxUserEmail->{MailboxUserEmail::FIELD_IS_STARRED},
            'is_important'   => $mailboxUserEmail->{MailboxUserEmail::FIELD_IS_IMPORTANT},
            'is_archived'    => $mailboxUserEmail->{MailboxUserEmail::FIELD_IS_ARCHIVED},
            'is_read'        => $mailboxUserEmail->{MailboxUserEmail::FIELD_IS_READ},
            'external_id'    => $mailboxUserEmail->{MailboxUserEmail::FIELD_EXTERNAL_ID},
            'sent_at'        => $mailboxUserEmail->{MailboxUserEmail::FIELD_SENT_AT},
            'email_id'       => $mailboxEmail->{MailboxEmail::FIELD_ID},
            'relevant_email' => $mailboxEmail->{MailboxEmail::FIELD_DIRECTION} === EmailDirection::OUTBOUND->value ? $allRecipients : $from,
            'thread_count'   => $threadCount,
            'from'           => $from,
            'to'             => array_values($to->all()),
            'bcc'            => array_values($bcc->all()),
            'cc'             => array_values($cc->all()),
            'subject'        => $subject,
            'snippet'        => $snippet,
            'content'        => $content,
            'labels'         => MailboxUserLabelResource::collection($emailLabels),
            'attachments'    => []
        ];
    }


    /**
     * @param array $emailIds
     * @return array
     */
    protected function cacheAllRelatedMailboxEmails(array $emailIds): array
    {
        return MailboxEmail::query()
            ->whereIn(MailboxEmail::FIELD_ID, $emailIds)
            ->get()
            ->mapWithKeys(function (MailboxEmail $email) {
                return [
                    $email->{MailboxEmail::FIELD_ID} => $email
                ];
            })
            ->all();
    }

    /**
     * @param array $emailIds
     * @return array
     */
    protected function cacheAllRelatedMailboxRecipients(array $emailIds): array
    {
        return MailboxEmailRecipient::query()
            ->whereIn(MailboxEmailRecipient::FIELD_EMAIL_ID, $emailIds)
            ->get()
            ->reduce(function (array $prev, MailboxEmailRecipient $recipient) {
                if (!isset($prev[$recipient->{MailboxEmailRecipient::FIELD_EMAIL_ID}])) {
                    $prev[$recipient->{MailboxEmailRecipient::FIELD_EMAIL_ID}] = [];
                }

                $prev[$recipient->{MailboxEmailRecipient::FIELD_EMAIL_ID}][] = $recipient;

                return $prev;
            }, []);
    }

    /**
     * @param array $emailIds
     * @return array
     */
    public function cacheAllRelatedLabels(array $emailIds): array
    {
        return MailboxEmailLabel::query()
            ->whereIn(MailboxEmailLabel::FIELD_EMAIL_ID, $emailIds)
            ->get()
            ->reduce(function (array $prev, MailboxEmailLabel $label) {
                if (!isset($prev[$label->{MailboxEmailLabel::FIELD_EMAIL_ID}])) {
                    $prev[$label->{MailboxEmailRecipient::FIELD_EMAIL_ID}] = [];
                }

                $prev[$label->{MailboxEmailRecipient::FIELD_EMAIL_ID}][] = $label;

                return $prev;
            }, []);
    }

    /**
     * @param array $threadIds
     * @return array
     */
    public function cacheTotalEmailsPerThread(array $threadIds): array
    {
        return MailboxUserEmail::query()
            ->select([
                MailboxUserEmail::FIELD_EXTERNAL_THREAD_ID,
                DB::raw('count(' . MailboxUserEmail::FIELD_EMAIL_ID . ') as thread_count')
            ])
            ->whereIn(MailboxUserEmail::FIELD_EXTERNAL_THREAD_ID, $threadIds)
            ->groupBy(MailboxUserEmail::FIELD_EXTERNAL_THREAD_ID)
            ->get()
            ->mapWithKeys(function ($email) {
                return [
                    $email->{MailboxUserEmail::FIELD_EXTERNAL_THREAD_ID} => $email->thread_count
                ];
            })
            ->toArray();
    }

    /**
     * @param Collection $items
     * @return array
     * @throws Exception
     */
    public function postCollection(Collection $items): array
    {
        $externalEmailIds = collect($items)
            ->pluck(MailboxUserEmail::FIELD_EXTERNAL_ID);

        if ($externalEmailIds->count() === 0) return $items->toArray();

        $transformedItems = [];

        $uniqueEmailIds = $items->pluck(MailboxUserEmail::FIELD_EMAIL_ID)->unique()->all();

        $cachedMailboxEmails = $this->cacheAllRelatedMailboxEmails($uniqueEmailIds);
        $cachedRecipients = $this->cacheAllRelatedMailboxRecipients($uniqueEmailIds);
        $cachedLabels = $this->cacheAllRelatedLabels($uniqueEmailIds);

        $uniqueThreadIds = $items->values()->pluck(MailboxUserEmail::FIELD_EXTERNAL_THREAD_ID)->unique()->all();

        $cachedTotalThreadPerEmail = $this->cacheTotalEmailsPerThread($uniqueThreadIds);

        /** @var MailboxUserEmail $item */
        foreach ($items as $item) {
            $transformedItems[] = (new MailboxUserEmailResource(collect(
                [
                    MailboxUserEmailResource::FIELD_MAILBOX_USER_EMAIL   => $item,
                    MailboxUserEmailResource::FIELD_MAILBOX_EMAIL        => $cachedMailboxEmails[$item->{MailboxUserEmail::FIELD_EMAIL_ID}] ?? [],
                    MailboxUserEmailResource::FIELD_RECIPIENTS           => $cachedRecipients[$item->{MailboxUserEmail::FIELD_EMAIL_ID}] ?? [],
                    MailboxUserEmailResource::FIELD_MAILBOX_EMAIL_LABELS => $cachedLabels[$item->{MailboxUserEmail::FIELD_EMAIL_ID}] ?? [],
                    MailboxUserEmailResource::FIELD_THREAD_COUNT         => $cachedTotalThreadPerEmail[$item->{MailboxUserEmail::FIELD_EXTERNAL_THREAD_ID}] ?? 1
                ]
            )))->toArray(request());
        }

        return $transformedItems;
    }

    /**
     * @param int|null $perPage
     * @return array
     * @throws Exception
     */
    public function postPaginate(?int $perPage = null): array
    {
        /** @var Builder $query */
        $query = $this->resource->get(self::FIELD_QUERY);
        /** @var MailProvider $mailProvider */
        $paginated = $query->paginate(self::getPerPage($perPage));

        return array_merge($paginated->toArray(), [
            'data' => $this->postCollection(Collection::make($paginated->items()))
        ]);
    }
}
