<?php

namespace App\Http\Resources\FloorPrice;

use App\Models\PricingMargin;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin PricingMargin
 */
class PricingMarginResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'industry_service_id' => $this->industry_service_id,
            'exclusive_margin' => $this->exclusive_margin,
            'duo_margin' => $this->duo_margin,
            'trio_margin' => $this->trio_margin,
            'quad_margin' => $this->quad_margin
        ];
    }
}
