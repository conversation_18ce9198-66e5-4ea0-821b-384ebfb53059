<?php

namespace App\Http\Resources;

use App\Models\EmailTemplate;
use App\Models\TemplateSelector;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin TemplateSelector
 */
class TemplateSelectorResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'relation' => $this->relation->value,
            'type' => $this->template_type === EmailTemplate::class ? 'Email' : strtoupper($this->template->type->value),
            'purpose' => $this->purpose_key->getLabel(),
            'industry' => $this->industry?->name ?? 'Any',
            'template' => $this->template->name
        ];
    }
}
