<?php

namespace App\Http\Resources\Bundles;

use App\Models\Bundle;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * Class BundleResource
 * @mixin Bundle
 */
class BundleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        return [
            Bundle::FIELD_ID               => $this->{Bundle::FIELD_ID},
            Bundle::FIELD_TITLE            => $this->{Bundle::FIELD_TITLE},
            Bundle::FIELD_DESCRIPTION      => $this->{Bundle::FIELD_DESCRIPTION},
            Bundle::FIELD_COST             => $this->{Bundle::FIELD_COST},
            Bundle::FIELD_CREDIT           => $this->{Bundle::FIELD_CREDIT},
            Bundle::FIELD_NAME             => $this->{Bundle::FIELD_NAME},
            Bundle::FIELD_NOTE             => $this->{Bundle::FIELD_NOTE},
            Bundle::FIELD_ACTIVATED_AT     => $this->{Bundle::FIELD_ACTIVATED_AT},
            Bundle::FIELD_ACTIVE           => $this->is_active,
            Bundle::FIELD_INDUSTRY_ID      => $this->{Bundle::FIELD_INDUSTRY_ID},
            Bundle::FIELD_IS_AUTO_APPROVED => $this->{Bundle::FIELD_IS_AUTO_APPROVED},
            Bundle::FIELD_AUTO_APPLY_CREDITS => $this->{Bundle::FIELD_AUTO_APPLY_CREDITS},
            'industry_name'                => $this->{Bundle::RELATION_INDUSTRY}?->name ?? '',
        ];
    }
}
