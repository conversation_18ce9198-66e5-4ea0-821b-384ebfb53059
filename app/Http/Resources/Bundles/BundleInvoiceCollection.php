<?php

namespace App\Http\Resources\Bundles;

use App\Models\BundleInvoice;
use Illuminate\Http\Resources\Json\ResourceCollection;

/**
 * Class BundleCollection
 * @mixin BundleInvoice
 */
class BundleInvoiceCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray($request): array
    {
        return parent::toArray($request);
    }
}
