<?php

namespace App\Http\Resources\Bundles;

use App\Models\BundleInvoiceHistory;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * Class BundleResource
 * @mixin BundleInvoiceHistory
 */
class BundleInvoiceHistoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        return [
            BundleInvoiceHistory::FIELD_BUNDLE_INVOICE_ID => $this->{BundleInvoiceHistory::FIELD_BUNDLE_INVOICE_ID},
            BundleInvoiceHistory::FIELD_DESCRIPTION => $this->{BundleInvoiceHistory::FIELD_DESCRIPTION},
            BundleInvoiceHistory::FIELD_NOTE => $this->{BundleInvoiceHistory::FIELD_NOTE},
            BundleInvoiceHistory::CREATED_AT => $this->{BundleInvoiceHistory::CREATED_AT},
        ];
    }
}
