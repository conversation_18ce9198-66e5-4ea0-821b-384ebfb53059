<?php

namespace App\Http\Resources\Bundles;

use App\Models\Bundle;
use App\Models\BundleInvoice;
use App\Models\Legacy\EloquentInvoice;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use JsonSerializable;

/**
 * Class BundleResource
 * @mixin BundleInvoice
 */
class BundleInvoiceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        return [
            BundleInvoice::FIELD_ID           => $this->{BundleInvoice::FIELD_ID},
            BundleInvoice::FIELD_STATUS       => $this->status->getStatusString(),
            BundleInvoice::FIELD_NOTE         => $this->{BundleInvoice::FIELD_NOTE},
            BundleInvoice::FIELD_COST         => $this->{BundleInvoice::FIELD_COST},
            BundleInvoice::FIELD_CREDIT       => $this->{BundleInvoice::FIELD_CREDIT},
            BundleInvoice::FIELD_ISSUED_BY    => $this->{BundleInvoice::RELATION_ISSUED_BY}?->name ?? '',
            BundleInvoice::FIELD_ISSUED_AT    => $this->{BundleInvoice::FIELD_ISSUED_AT},
            BundleInvoice::FIELD_APPROVED_BY  => $this->{BundleInvoice::RELATION_APPROVED_BY}?->name ?? '',
            BundleInvoice::FIELD_APPROVED_AT  => $this->{BundleInvoice::FIELD_APPROVED_AT},
            BundleInvoice::FIELD_DENIED_BY    => $this->{BundleInvoice::RELATION_DENIED_BY}?->name ?? '',
            BundleInvoice::FIELD_DENIED_AT    => $this->{BundleInvoice::FIELD_DENIED_AT},
            BundleInvoice::FIELD_PAID_AT      => $this->{BundleInvoice::FIELD_PAID_AT},
            BundleInvoice::FIELD_CANCELLED_AT => $this->{BundleInvoice::FIELD_CANCELLED_AT},
            BundleInvoice::FIELD_CANCELLED_BY => $this->{BundleInvoice::RELATION_CANCELLED_BY}?->name ?? '',
            BundleInvoice::FIELD_FAILED_AT    => $this->{BundleInvoice::FIELD_FAILED_AT},
            BundleInvoice::FIELD_FAIL_REASON  => $this->{BundleInvoice::FIELD_FAIL_REASON},
            BundleInvoice::FIELD_BUNDLE_ID    => $this->{BundleInvoice::RELATION_BUNDLE}->id,
            'bundle_name'                     => $this->{BundleInvoice::RELATION_BUNDLE}->name,
            'is_auto_approved'                => $this->{BundleInvoice::RELATION_BUNDLE}->{Bundle::FIELD_IS_AUTO_APPROVED},
            'company_name'                    => $this->{BundleInvoice::RELATION_COMPANY}->name,
            'company_id'                      => $this->{BundleInvoice::RELATION_COMPANY}->id,
            'has_legacy_invoice_paid'         => $this->{BundleInvoice::RELATION_LEGACY_INVOICE}?->{EloquentInvoice::STATUS} === EloquentInvoice::VALUE_STATUS_PAID,
            'billing_version'                 => $this->billing_version,
            'payable_invoice_id'              => $this->billing_version === 'v2' ? $this->payable_invoice_id : null,
            'payload'                         => $this->payload,
            'billing_profile_ids'             => Arr::get($this->payload, 'billing_profile_ids')
        ];
    }
}
