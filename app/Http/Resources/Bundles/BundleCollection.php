<?php

namespace App\Http\Resources\Bundles;

use App\Models\Bundle;
use Illuminate\Http\Resources\Json\ResourceCollection;

/**
 * Class BundleResource
 * @mixin Bundle
 */
class BundleCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray($request): array
    {
        return parent::toArray($request);
    }
}
