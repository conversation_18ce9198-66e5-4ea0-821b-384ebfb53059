<?php

namespace App\Http\Resources\Bundles;

use App\Models\BundleInvoice;
use App\Models\BundleInvoiceHistory;
use Illuminate\Http\Resources\Json\ResourceCollection;

/**
 * Class BundleCollection
 * @mixin BundleInvoiceHistory
 */
class BundleInvoiceHistoryCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray($request): array
    {
        return parent::toArray($request);
    }
}
