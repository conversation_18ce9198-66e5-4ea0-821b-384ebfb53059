<?php

namespace App\Http\Resources\Companies\Sales;

use App\Models\Territory\RelationshipManager;
use App\Models\User;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * Class SuccessManagerResource
 * @mixin RelationshipManager
 */
class RelationshipManagerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray(Request $request): array|Arrayable|JsonSerializable
    {
        return
            [
                "id"                            => $this->{RelationshipManager::FIELD_ID},
                "name"                          => $this->{RelationshipManager::RELATION_USER}?->{User::FIELD_NAME} ?? 'User unavailable',
                "user_id"                       => $this->{RelationshipManager::FIELD_USER_ID},
            ];
    }
}
