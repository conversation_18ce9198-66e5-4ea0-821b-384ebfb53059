<?php

namespace App\Http\Resources\Calendar;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Calendar\CalendarEvent;
use App\Models\Conference\Conference;
use App\Models\Conference\ConferenceParticipant;

/**
 * @mixin CalendarEvent
 */
class CalendarEventResource extends BaseJsonResource
{
    public function toArray($request): array
    {
        $totalConferenceParticipants = $this->conferences->map(function (Conference $conference) {
            return $conference->participants->map(function (ConferenceParticipant $conferenceParticipant) {
                return $conferenceParticipant->name;
            });
        })->flatten()->unique()->count();

        return [
            'total_conference_participants' => $totalConferenceParticipants,
            'calendar_id'                   => $this->{CalendarEvent::FIELD_CALENDAR_ID},
            'user_id'                       => $this->{CalendarEvent::FIELD_USER_ID},
            'external_id'                   => $this->{CalendarEvent::FIELD_EXTERNAL_ID},
            'title'                         => $this->{CalendarEvent::FIELD_TITLE},
            'payload'                       => $this->{CalendarEvent::FIELD_PAYLOAD},
            'description'                   => $this->{CalendarEvent::FIELD_DESCRIPTION},
            'location'                      => $this->{CalendarEvent::FIELD_LOCATION},
            'status'                        => $this->{CalendarEvent::FIELD_STATUS},
            'conference_url'                => $this->{CalendarEvent::FIELD_CONFERENCE_URL},
            'last_conference_data_sync_at'  => $this->{CalendarEvent::FIELD_LAST_CONFERENCE_DATA_SYNC_AT},
            'timezone'                      => $this->{CalendarEvent::FIELD_TIMEZONE},
            'start_time'                    => $this->{CalendarEvent::FIELD_START_TIME},
            'end_time'                      => $this->{CalendarEvent::FIELD_END_TIME},
            'recurrence_rule'               => $this->{CalendarEvent::FIELD_RECURRENCE_RULE},
            'recurrence_data'               => $this->{CalendarEvent::FIELD_RECURRENCE_DATA},
            'created_at'                    => $this->{CalendarEvent::FIELD_CREATED_AT},
            'updated_at'                    => $this->{CalendarEvent::FIELD_UPDATED_AT},
            'deleted_at'                    => $this->{CalendarEvent::FIELD_DELETED_AT},
            'conferences'                   => ConferenceResource::collection($this->{CalendarEvent::RELATION_CONFERENCES}),
            'attendees'                     => CalendarEventAttendeeResource::collection($this->{CalendarEvent::RELATION_ATTENDEES}),
            'length'                        => round($this->conferences->sum(Conference::FIELD_DURATION_IN_SECONDS) / 60) . ' minutes',
        ];
    }
}
