<?php

namespace App\Http\Resources\Calendar;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Calendar\CalendarEvent;
use App\Models\Conference\Conference;
use Carbon\Carbon;

/**
 * @mixin Conference
 */
class ConferenceResource extends BaseJsonResource
{
    public function toArray($request): array
    {
        return [
            'calendar_event_id'   => $this->{Conference::FIELD_CALENDAR_EVENT_ID},
            'external_id'         => $this->{Conference::FIELD_EXTERNAL_ID},
            'user_id'             => $this->{Conference::FIELD_USER_ID},
            'start_time'          => $this->{Conference::FIELD_START_TIME},
            'end_time'            => $this->{Conference::FIELD_END_TIME},
            'expire_time'         => $this->{Conference::FIELD_EXPIRE_TIME},
            'duration_in_seconds' => $this->{Conference::FIELD_DURATION_IN_SECONDS},
            'status'              => $this->{Conference::FIELD_STATUS},
            'created_at'          => $this->{Conference::FIELD_CREATED_AT},
            'updated_at'          => $this->{Conference::FIELD_UPDATED_AT},
            'participants'        => ConferenceParticipantResource::collection($this->{Conference::RELATION_PARTICIPANTS}),
            'transcripts'         => ConferenceTranscriptResource::collection($this->{Conference::RELATION_TRANSCRIPTS}),
        ];
    }
}
