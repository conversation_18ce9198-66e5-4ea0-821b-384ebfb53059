<?php

namespace App\Http\Resources\Calendar;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Calendar\CalendarEvent;
use App\Models\Calendar\CalendarEventAttendee;
use App\Models\Conference\ConferenceParticipant;

/**
 * @mixin ConferenceParticipant
 */
class ConferenceParticipantResource extends BaseJsonResource
{
    public function toArray($request): array
    {
        return [
            'conference_id'       => $this->{ConferenceParticipant::FIELD_CONFERENCE_ID},
            'name'                => $this->{ConferenceParticipant::FIELD_NAME},
            'external_id'         => $this->{ConferenceParticipant::FIELD_EXTERNAL_ID},
            'earliest_start_time' => $this->{ConferenceParticipant::FIELD_EARLIEST_START_TIME},
            'latest_end_time'     => $this->{ConferenceParticipant::FIELD_LATEST_END_TIME},
            'duration_in_seconds' => $this->{ConferenceParticipant::FIELD_DURATION_IN_SECONDS},
            'created_at' => $this->{ConferenceParticipant::FIELD_CREATED_AT},
            'updated_at' => $this->{ConferenceParticipant::FIELD_UPDATED_AT},
        ];
    }
}
