<?php

namespace App\Http\Resources\Calendar;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Calendar\CalendarEvent;
use App\Models\Calendar\CalendarEventAttendee;

/**
 * @mixin CalendarEventAttendee
 */
class CalendarEventAttendeeResource extends BaseJsonResource
{
    public function toArray($request): array
    {
        return [
            'calendar_event_id'     => $this->{CalendarEventAttendee::FIELD_CALENDAR_EVENT_ID},
            'name'                  => $this->{CalendarEventAttendee::FIELD_NAME},
            'email'                 => $this->{CalendarEventAttendee::FIELD_EMAIL},
            'status'                => $this->{CalendarEventAttendee::FIELD_STATUS},
            'identified_contact_id' => $this->{CalendarEventAttendee::FIELD_IDENTIFIED_CONTACT_ID},
            'relation_type'         => $this->{CalendarEventAttendee::FIELD_RELATION_TYPE},
            'relation_id'           => $this->{CalendarEventAttendee::FIELD_RELATION_ID},
        ];
    }
}
