<?php

namespace App\Http\Resources\Calendar;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Calendar\CalendarEvent;
use App\Models\Calendar\CalendarEventAttendee;
use App\Models\Conference\ConferenceTranscript;
use App\Models\Conference\ConferenceTranscriptEntry;

/**
 * @mixin ConferenceTranscriptEntry
 */
class ConferenceTranscriptEntryResource extends BaseJsonResource
{
    public function toArray($request): array
    {
        return [
            'participant_id'            => $this->{ConferenceTranscriptEntry::RELATION_PARTICIPANT}->id,
            'participant_name'          => $this->{ConferenceTranscriptEntry::RELATION_PARTICIPANT}->name,
            'conference_transcript_id'  => $this->{ConferenceTranscriptEntry::FIELD_CONFERENCE_TRANSCRIPT_ID},
            'conference_participant_id' => $this->{ConferenceTranscriptEntry::FIELD_CONFERENCE_PARTICIPANT_ID},
            'external_id'               => $this->{ConferenceTranscriptEntry::FIELD_EXTERNAL_ID},
            'external_participant_id'   => $this->{ConferenceTranscriptEntry::FIELD_EXTERNAL_PARTICIPANT_ID},
            'start_time'                => $this->{ConferenceTranscriptEntry::FIELD_START_TIME},
            'end_time'                  => $this->{ConferenceTranscriptEntry::FIELD_END_TIME},
            'text'                      => $this->{ConferenceTranscriptEntry::FIELD_TEXT},
        ];
    }
}
