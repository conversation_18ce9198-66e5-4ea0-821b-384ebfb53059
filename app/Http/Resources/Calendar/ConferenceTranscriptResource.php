<?php

namespace App\Http\Resources\Calendar;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Calendar\CalendarEvent;
use App\Models\Calendar\CalendarEventAttendee;
use App\Models\Conference\ConferenceTranscript;

/**
 * @mixin ConferenceTranscript
 */
class ConferenceTranscriptResource extends BaseJsonResource
{
    public function toArray($request): array
    {
        return [
            'conference_id'                 => $this->{ConferenceTranscript::FIELD_CONFERENCE_ID},
            'external_id'                   => $this->{ConferenceTranscript::FIELD_EXTERNAL_ID},
            'end_time'                      => $this->{ConferenceTranscript::FIELD_END_TIME},
            'start_time'                    => $this->{ConferenceTranscript::FIELD_START_TIME},
            'duration_in_seconds'           => $this->{ConferenceTranscript::FIELD_DURATION_IN_SECONDS},
            'docs_destination_document_id'  => $this->{ConferenceTranscript::FIELD_DOCS_DESTINATION_DOCUMENT_ID},
            'docs_destination_document_url' => $this->{ConferenceTranscript::FIELD_DOCS_DESTINATION_DOCUMENT_URL},
            'entries'                       => ConferenceTranscriptEntryResource::collection($this->{ConferenceTranscript::RELATION_ENTRIES}),
        ];
    }
}
