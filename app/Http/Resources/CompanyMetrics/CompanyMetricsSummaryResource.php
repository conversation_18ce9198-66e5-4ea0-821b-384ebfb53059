<?php

namespace App\Http\Resources\CompanyMetrics;

use App\Helpers\CarbonHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\CompanyMetric;
use App\Services\CompanyMetrics\SemrushMetricsService;
use Illuminate\Http\Request;

/**
 * @mixin CompanyMetric
 */
class CompanyMetricsSummaryResource extends BaseJsonResource
{
    protected array $arrayColumns = [
        SemrushMetricsService::RESPONSE_COLUMN_DIRECT,
        SemrushMetricsService::RESPONSE_COLUMN_SEARCH_PAID,
        SemrushMetricsService::RESPONSE_COLUMN_SEARCH_ORGANIC,
        SemrushMetricsService::RESPONSE_COLUMN_SOCIAL_PAID,
        SemrushMetricsService::RESPONSE_COLUMN_SOCIAL_ORGANIC,
        SemrushMetricsService::RESPONSE_COLUMN_DISPLAY_AD,
        SemrushMetricsService::RESPONSE_COLUMN_REFERRAL,
    ];

    public function __construct($resource)
    {
        parent::__construct($resource);
    }

    /**
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        if(!$this->resource)
            return [
                'visits_data_share' => [],
            ];

        //total number of visits to the site
        $visitsColumnValue = $this->resource->{CompanyMetric::FIELD_REQUEST_RESPONSE}[SemrushMetricsService::RESPONSE_COLUMN_VISITS];

        $visitsDataShare = [];
        foreach($this->arrayColumns as $arrayColumn)
            $visitsDataShare[] = [
                'name' => $arrayColumn,
                'value' => $this->resource->{CompanyMetric::FIELD_REQUEST_RESPONSE}[$arrayColumn],
                'share' => number_format(((int)$this->resource->{CompanyMetric::FIELD_REQUEST_RESPONSE}[$arrayColumn] / (int)$visitsColumnValue) * 100, 2) . ' %',
            ];

        return [
            'metric_from_date'  => CarbonHelper::parse($this->resource->{CompanyMetric::FIELD_REQUEST_RESPONSE}[SemrushMetricsService::RESPONSE_COLUMN_DISPLAY_DATE])->toFormat(),
            'metric_to_date'    => CarbonHelper::parse($this->resource->{CompanyMetric::CREATED_AT})->toFormat(),
            'total_visits'      => $visitsColumnValue,
            'visits_data_share' => $visitsDataShare,
        ];

    }

}