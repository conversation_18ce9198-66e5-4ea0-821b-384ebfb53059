<?php

namespace App\Http\Resources\CompanyMetrics;

use App\Models\CompanyMetric;
use Carbon\Carbon;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Number;
use JsonSerializable;

class CompanyPaidPerClickMetricResource extends JsonResource
{
    const SPENT = 'spent';
    const PERIOD = 'period';
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        return [
            self::SPENT     => Number::currency($this->{CompanyMetric::FIELD_REQUEST_RESPONSE}['monthlySpend']),
            self::PERIOD    => Carbon::createFromDate($this->{CompanyMetric::FIELD_REQUEST_RESPONSE}['year'],$this->{CompanyMetric::FIELD_REQUEST_RESPONSE}['month'])->format('M Y'),
        ];
    }
}
