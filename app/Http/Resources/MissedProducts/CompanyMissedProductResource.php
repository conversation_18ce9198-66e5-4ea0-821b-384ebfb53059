<?php

namespace App\Http\Resources\MissedProducts;

use App\Enums\Timezone;
use App\Models\MissedProducts\MissedProduct;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ServiceProduct;
use App\Services\GoogleMapsHelperService;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * Class MissedProductResource
 * @mixin MissedProduct
 */
class CompanyMissedProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        $consumerProduct = $this->{MissedProduct::RELATION_CONSUMER_PRODUCT};
        $productName = $consumerProduct->{ConsumerProduct::RELATION_SERVICE_PRODUCT}?->{ServiceProduct::RELATION_PRODUCT}?->{Product::FIELD_NAME};
        $industry = $this->{MissedProduct::RELATION_INDUSTRY_SERVICE}?->{IndustryService::RELATION_INDUSTRY}?->{Industry::FIELD_NAME};
        $serviceName = $this->{MissedProduct::RELATION_INDUSTRY_SERVICE}?->{IndustryService::FIELD_NAME};
        $sellableLegs = $this->{MissedProduct::FIELD_SELLABLE_LEGS};
        $consumer = $consumerProduct->{ConsumerProduct::RELATION_CONSUMER};
        $address = $consumerProduct->{ConsumerProduct::RELATION_ADDRESS};

        return [
            'id'                     => $consumerProduct->{ConsumerProduct::FIELD_ID},
            'product_name'           => $productName,
            'industry'               => $industry,
            'requested_service'      => $serviceName,
            'appointments_requested' => $consumerProduct->{ConsumerProduct::FIELD_CONTACT_REQUESTS},
            'appointments_remaining' => $sellableLegs,
            'price'                  => $this->{MissedProduct::FIELD_PRICE},
            'consumer' => [
                'legacy_id' => $consumer->{Consumer::FIELD_LEGACY_ID},
                'id'        => $consumer->{Consumer::FIELD_ID},
                'name'      => $consumer->getFullName(),
                'email'     => obfuscateEmail($consumer->{Consumer::FIELD_EMAIL}),
                'phone'     => obfuscatePhone($consumer->{Consumer::FIELD_PHONE}),
                'verified'  => in_array($consumer?->{Consumer::FIELD_CLASSIFICATION}, Consumer::VERIFIED_CLASSIFICATIONS),
            ],
            'address'                => $address?->getObfuscatedAddress(),
            'map_url'                => GoogleMapsHelperService::generateGoogleMapsLink($address->{Address::FIELD_LATITUDE}, $address->{Address::FIELD_LONGITUDE}),
            'timezone'               => $address?->{Address::FIELD_UTC} ? Timezone::tryFrom($address?->{Address::FIELD_UTC})->toLocalFormat() : '',
            'missed_lead_created_at' => $this->{MissedProduct::FIELD_CREATED_AT}
        ];
    }
}
