<?php

namespace App\Http\Resources\MissedProducts;

use App\Models\MissedProducts\MissedProduct;
use App\Models\Odin\Address;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductData;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ServiceProduct;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * Class MissedProductResource
 * @mixin MissedProduct
 */
class MissedProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        $product = $this->{MissedProduct::RELATION_CONSUMER_PRODUCT};
        $productData = $product->{ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA}?->{ConsumerProductData::FIELD_PAYLOAD};
        $productName = $product->{ConsumerProduct::RELATION_SERVICE_PRODUCT}?->{ServiceProduct::RELATION_PRODUCT}?->{Product::FIELD_NAME};
        $price = $this->{MissedProduct::FIELD_PRICE};
        $industry = $this->{MissedProduct::RELATION_INDUSTRY_SERVICE}?->{IndustryService::RELATION_INDUSTRY}?->{Industry::FIELD_NAME};
        $serviceName = $this->{MissedProduct::RELATION_INDUSTRY_SERVICE}?->{IndustryService::FIELD_NAME};
        $sellableLegs = $this->{MissedProduct::FIELD_SELLABLE_LEGS};

        return [
            'id' => $product->{ConsumerProduct::FIELD_ID},
            'created_at' => $product->{ConsumerProduct::FIELD_CREATED_AT},
            'industry' => $industry,
            'product_name' => $productName,
            'requested_service' => $serviceName,
            'date_requested' => $productData['best_time_to_call']  ?? '',
            'contacts_requested' => $product->{ConsumerProduct::FIELD_CONTACT_REQUESTS},
            'assignments_remaining' => $sellableLegs,
            'price' => $price,
            'consumer_data' => $this->consumer_data,
            'zip_code' => $product->{ConsumerProduct::RELATION_ADDRESS}->{Address::FIELD_ZIP_CODE} // TODO: Remove this when missed leads checkout by counties is not dependant on product zipcodes directly
        ];
    }
}
