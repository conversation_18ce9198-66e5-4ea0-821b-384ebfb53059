<?php

namespace App\Http\Resources\MissedProducts;

use App\Models\MissedProducts\MissedProduct;
use Illuminate\Http\Resources\Json\ResourceCollection;

/**
 * Class MissedProductCollection
 * @mixin MissedProduct
 */
class MissedProductCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray($request): array
    {
        return parent::toArray($request);
    }
}
