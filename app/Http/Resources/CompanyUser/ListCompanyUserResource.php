<?php

namespace App\Http\Resources\CompanyUser;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Odin\CompanyUser;

/**
 * @mixin CompanyUser
 */
class ListCompanyUserResource extends BaseJsonResource
{
    public function toArray($request): array
    {
        return [
            'id'           => $this->id,
            'name'         => $this->completeName(),
            'status'       => $this->status,
            'email'        => $this->email,
            'cell_phone'   => $this->formatted_cell_phone,
            'office_phone' => $this->formatted_office_phone,
        ];
    }
}
