<?php

namespace App\Http\Resources;

use App\Http\Resources\Odin\BaseJsonResource;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use JsonSerializable;

/**
 * @mixin array
 */
class ConsumerSearchAggregatesResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        return [
            'average_installers_lead_sold' => round($this->average_installers_lead_sold,2),
            'premium_lead_count'           => $this->premium_lead_count,
            'standard_lead_count'          => $this->standard_lead_count,
        ];
    }
}
