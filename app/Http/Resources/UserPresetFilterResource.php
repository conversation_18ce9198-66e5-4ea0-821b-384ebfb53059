<?php

namespace App\Http\Resources;

use App\Models\UserPreset;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin UserPreset
 */
class UserPresetFilterResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        return [
            'id'        => $this->id,
            'name'      => $this->name,
            'value'     => $this->value,
        ];
    }
}
