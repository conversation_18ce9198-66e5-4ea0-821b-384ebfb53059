<?php

namespace App\Http\Resources;

use App\Models\Action;
use App\Models\ActionTag;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Legacy\EloquentUser;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;
use JsonSerializable;

/**
 * Class ActionResource
 * @mixin Action
 */
class ActionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        $to = $this->to();

        $toCompany = false;

        if ($to instanceof EloquentUser) {
            $toName = $to->name;
        }
        elseif ($to instanceof EloquentCompanyContact) {
            $toName = $to->name;
        }
        elseif ($to instanceof EloquentCompany) {
            $toName = $to->companyname;
            $toCompany = true;
        } elseif ($to instanceof Company) {
            $toName = $to->name;
            $toCompany = true;
        } elseif ($to instanceof CompanyUser) {
            $toName = $to->first_name;
        }
        else {
            $toName = null;
        }

        return [
            'id' => $this->id,
            'subject' => $this->subject,
            'message' => Str::markdown($this->message),
            'from'    => $this->from?->name ?? null,
            'from_user_id' => $this->{Action::FIELD_FROM_USER_ID},
            'to'      => $toName,
            'to_company' => $toCompany,
            'target_type' => $this->for_relation_type,
            'target_id' => $this->for_id,
            'created_timestamp' => $this->created_at?->timestamp,
            'updated_timestamp' => $this->updated_at?->timestamp,
            'pinned' => $this->pinned,
            'category' => $this->{Action::RELATION_TYPE_CATEGORY}?->name ?? '',
            'action_category_id' => $this->{Action::FIELD_CATEGORY_ID} ?? 0,
            'display_date' => $this->{Action::FIELD_DISPLAY_DATE}?->format('Y-m-d'),
            'tags' => $this->{Action::RELATION_TAGS} ? array_column($this->{Action::RELATION_TAGS}->toArray(), ActionTag::FIELD_USER_ID) : [],
            'tag_by_email' => (bool)$this->{Action::FIELD_TAG_BY_EMAIL},
            'previous_sales_status' => $this->previous_sales_status?->displayName() ?? null,
            'updated_sales_status' => $this->updated_sales_status?->displayName() ?? null,
        ];
    }
}
