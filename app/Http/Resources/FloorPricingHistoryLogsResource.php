<?php

namespace App\Http\Resources;

use App\DTO\FloorPricing\FloorPricingHistoryLog;
use App\Http\Resources\Odin\BaseJsonResource;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Number;


/**
 * @mixin FloorPricingHistoryLog
 */
class FloorPricingHistoryLogsResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     * @throws Exception
     */
    public function toArray(Request $request): array
    {
        return [
            "causer_id"   => $this->getCauserId(),
            "causer_name" => $this->getCauserName(),
            "county"      => $this->getCounty(),
            "county_key"  => $this->getCountyKey(),
            "date"        => Carbon::parse($this->getDate())->format('M d, Y h:i A'),
            "price_from"  => $this->getPriceFrom(),
            "price_to"    => $this->getPriceTo(),
            "sale_type"   => $this->getSaleType(),
            "state"       => $this->getState(),
            "state_abbr"  => $this->getStateAbbr(),
            "state_key"   => $this->getStateKey(),
        ];
    }
}
