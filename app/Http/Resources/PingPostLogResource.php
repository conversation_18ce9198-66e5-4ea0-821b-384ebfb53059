<?php

namespace App\Http\Resources;

use App\Enums\ConsumerProcessing\ConsumerProcessingActivityType;
use App\Enums\ConsumerProcessing\ConsumerProcessingActivityVisibilityScope;
use App\Models\ConsumerProcessingActivity;
use App\Models\PingPostPublishers\PingPostPublisher;
use App\Models\PingPostPublishers\PingPostPublisherLog;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;
use JsonSerializable;

/**
 * @mixin PingPostPublisherLog
 */
class PingPostLogResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray(Request $request): array|Arrayable|JsonSerializable
    {
        $mstTime = $this->{PingPostPublisherLog::FIELD_CREATED_AT}->subHours(7);
        return [
            'id'                    => $this->id,
            'publisher'             => $this->{PingPostPublisher::FIELD_NAME},
            'publisher_company_id'  => $this->{PingPostPublisher::FIELD_COMPANY_ID},
            'type'                  => $this->getType(),
            'log'                   => $this->{PingPostPublisherLog::FIELD_LOG},
            'date'                  => $mstTime->format('Y-m-d H:i:s'),
            'day'                   => $mstTime->format('Y-m-d'),
            'time'                  => $mstTime->format('H:i:s'),
        ];
    }

    /**
     * @return string
     */
    private function getType(): string
    {
        return match($this->{PingPostPublisherLog::FIELD_TYPE}) {
            PingPostPublisherLog::TYPE_LOG      => 'Info',
            PingPostPublisherLog::TYPE_ERROR    => 'Error',
            PingPostPublisherLog::TYPE_INITIAL  => 'Initial',
            default => '',
        };
    }
}
