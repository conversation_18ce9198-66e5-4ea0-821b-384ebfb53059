<?php

namespace App\Http\Resources;

use App\Http\Resources\Odin\BaseJsonResource;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Str;
use Spatie\Tags\Tag;

/**
 * @mixin Tag
 */
class TagResource extends BaseJsonResource
{
    const string FIELD_ID   = 'id';
    const string FIELD_NAME = 'name';

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            self::FIELD_ID => $this->id,
            self::FIELD_NAME => Str::headline($this->name),
        ];
    }
}
