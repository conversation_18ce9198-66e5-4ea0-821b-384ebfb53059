<?php

namespace App\Http\Resources\CompanyCampaign;

use App\Enums\Campaigns\Modules\Budget\BudgetType;
use App\Http\Resources\Odin\BaseJsonResource;
use Illuminate\Http\Request;
use Illuminate\Support\Number;

/**
 * @mixin array
 */
class CampaignBudgetUsageResource extends BaseJsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'campaign_id'          => $this['campaign_id'],
            'campaign_name'        => $this['campaign_name'],
            'budget_type'          => BudgetType::tryFrom($this['budget_type'])->getDisplayName(),
            'budget_usage_today'   => round($this['budget_usage_today'] * 100, 2) . '%',
        ];
    }

}