<?php

namespace App\Http\Resources;

use App\Enums\Odin\JobStatus;
use App\Models\Job;
use App\Models\Odin\JobTracking;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin JobTracking
 */
class JobTrackingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'status' => $this->status->getStatusName(),
            'status_timestamp' => $this->getTimestampForStatus(),
            'attempts' => $this->attempts
        ];
    }

    /**
     * @return float|int|string|null
     */
    protected function getTimestampForStatus(): float|int|string|null
    {
        return match ($this->status) {
            JobStatus::SCHEDULED => $this->getScheduledTime(),
            JobStatus::COMPLETED => $this->getCompletedTime(),
            default => null
        };
    }

    /**
     * @return float|int|string|null
     */
    protected function getCompletedTime(): float|int|string|null
    {
        return $this->updated_at?->timestamp;
    }

    /**
     * @return int|null
     */
    protected function getScheduledTime(): ?int
    {
        /** @var Job|null $job */
        $job = Job::query()->whereJsonContains(Job::FIELD_PAYLOAD . '->uuid', $this->job_uuid)->first();

        return $job ? $job->available_at->timestamp : $this->available_at;
    }
}
