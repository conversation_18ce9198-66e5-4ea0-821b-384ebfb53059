<?php

namespace App\Http\Resources;

use App\Enums\CompanyConfigurationEnum;
use App\Models\Odin\Consumer;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Consumer
 */
class CompanyConfigurationResource extends JsonResource
{
    const FIELD_KEY         = 'key';
    const FIELD_DESCRIPTION = 'description';
    const FIELD_VALUE       = 'value';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     * @throws \Exception
     */
    public function toArray($request): array
    {
        return $this->getConfigurationFieldsByEnum();
    }

    private function getConfigurationFieldsByEnum()
    {
        return collect($this->resource->getAttributes())->map(function ($value, $key) {
            $key = CompanyConfigurationEnum::tryFrom($key);

            if (!$key) {
                return null;
            }

            return [
                self::FIELD_KEY         => $key->value,
                self::FIELD_DESCRIPTION => $key->getDescription(),
                self::FIELD_VALUE       => $this->{$key->value} ?? null
            ];
        })->values()->filter()->toArray();
    }
}
