<?php

namespace App\Http\Resources\OpportunityNotifications;

use App\Models\Odin\ServiceProduct;
use Illuminate\Http\Resources\Json\ResourceCollection;

/**
 * Class ServiceProductCollection
 * @mixin ServiceProduct
 */
class ServiceProductCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray($request): array
    {
        return parent::toArray($request);
    }
}
