<?php

namespace App\Http\Resources\OpportunityNotifications;

use App\Enums\DayOfWeek;
use App\Models\MissedProducts\OpportunityNotificationConfig;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * Class OpportunityNotificationsConfigRequest
 * @mixin OpportunityNotificationConfig
 */
class OpportunityNotificationConfigResource extends JsonResource
{
    const string ATTEMPT_ON_DAYS_DISPLAY = 'days_display';

    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        return [
            OpportunityNotificationConfig::FIELD_ID                     => $this->id,
            OpportunityNotificationConfig::FIELD_TYPE                   => $this->type->value,
            'type_display'                                              => $this->type->getTitle(),
            OpportunityNotificationConfig::FIELD_NAME                   => $this->name,
            OpportunityNotificationConfig::FIELD_ATTEMPT_ON_DAYS        => $this->attempt_on_days,
            self::ATTEMPT_ON_DAYS_DISPLAY                               => $this->getDaysDisplay(),
            OpportunityNotificationConfig::FIELD_SEND_TIME              => $this->send_time,
            OpportunityNotificationConfig::FIELD_MAXIMUM_SEND_FREQUENCY => $this->maximum_send_frequency,
            OpportunityNotificationConfig::FIELD_LEAD_THRESHOLD         => $this->lead_threshold,
            OpportunityNotificationConfig::FIELD_CAMPAIGN_THRESHOLD     => $this->campaign_threshold,
            OpportunityNotificationConfig::FIELD_MAXIMUM_PROMO_PRODUCTS => $this->maximum_promo_products,
            OpportunityNotificationConfig::FIELD_MAXIMUM_DAYS_LAST_LEAD => $this->maximum_days_since_last_lead ?: null,
            OpportunityNotificationConfig::FIELD_DAYS_TO_QUERY          => $this->days_to_query,
            OpportunityNotificationConfig::FIELD_EXPIRES_AT             => $this->expires_at->toIso8601ZuluString(),
            OpportunityNotificationConfig::FIELD_FILTER_PRESET_ID       => $this->filter_preset_id,
            OpportunityNotificationConfig::FIELD_ACTIVE                 => $this->active,
            'total_sent'                                                => $this?->notifications_count ?? 0,
        ];
    }

    protected function getDaysDisplay(): array
    {
        return array_map(fn(int $v) => DayOfWeek::tryFrom($v)?->displayName() ?? '-', $this->attempt_on_days);
    }
}
