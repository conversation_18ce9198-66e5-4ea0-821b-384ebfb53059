<?php

namespace App\Http\Resources\OpportunityNotifications;

use App\Models\MissedProducts\OpportunityNotification;
use Illuminate\Http\Resources\Json\ResourceCollection;

/**
 * Class OpportunityNotificationsCollection
 * @mixin OpportunityNotification
 */
class OpportunityNotificationCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray($request): array
    {
        return parent::toArray($request);
    }
}
