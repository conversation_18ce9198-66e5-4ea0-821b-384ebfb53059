<?php

namespace App\Http\Resources\OpportunityNotifications;

use App\Models\MissedProducts\OpportunityNotification;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class OpportunityNotificationsCollection
 * @mixin OpportunityNotification
 */
class GetCompanyCountPreviewResource extends JsonResource
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->{Company::FIELD_ID},
            'legacy_id' => $this->{Company::FIELD_LEGACY_ID},
            'name' => $this->{Company::FIELD_NAME},
            'company_users' => $this->{Company::RELATION_USERS}->map(fn($user) => [
                'id' => $user->{CompanyUser::FIELD_ID},
                'name' => implode(' ', [$user->{CompanyUser::FIELD_FIRST_NAME}, $user->{CompanyUser::FIELD_LAST_NAME}]),
                'email' => $user->{CompanyUser::FIELD_EMAIL}
            ])
        ];
    }
}
