<?php

namespace App\Http\Resources\OpportunityNotifications;

use App\DataModels\Odin\Prices\BestRevenueScenarioDataModel;
use App\Models\Odin\Address;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductData;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ServiceProduct;
use App\Services\ProductPricing\BestRevenueScenario\BestRevenueScenarioServiceFactory;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;
use Throwable;

/**
 * Class ServiceProductResource
 * @mixin ServiceProduct
 */
class ServiceProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        return [
            ServiceProduct::FIELD_ID => $this->{ServiceProduct::FIELD_ID},
            ServiceProduct::RELATION_PRODUCT => $this->product?->{Product::FIELD_NAME},
            ServiceProduct::RELATION_SERVICE => $this->service?->{IndustryService::FIELD_NAME},
            'industry' => $this->service?->industry->{Industry::FIELD_NAME},
        ];
    }
}
