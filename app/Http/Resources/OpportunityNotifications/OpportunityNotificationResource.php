<?php

namespace App\Http\Resources\OpportunityNotifications;

use App\Models\MissedProducts\OpportunityNotification;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * Class OpportunityNotificationsResource
 * @mixin OpportunityNotification
 */
class OpportunityNotificationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        return [
            OpportunityNotification::FIELD_ID => $this->{OpportunityNotification::FIELD_ID},
            OpportunityNotification::FIELD_RECIPIENTS => $this->{OpportunityNotification::FIELD_RECIPIENTS},
            OpportunityNotification::FIELD_DELIVERY_METHOD => $this->{OpportunityNotification::FIELD_DELIVERY_METHOD}->getDeliveryMethodString(),
            OpportunityNotification::FIELD_SENT_AT => $this->{OpportunityNotification::FIELD_SENT_AT},
            OpportunityNotification::FIELD_COMPANY_ID => $this->{OpportunityNotification::RELATION_COMPANY}?->id,
            OpportunityNotification::FIELD_CONFIG_ID => $this->{OpportunityNotification::RELATION_CONFIG}?->id,
            OpportunityNotification::FIELD_VIEW_COUNT => $this->{OpportunityNotification::FIELD_VIEW_COUNT},
            OpportunityNotification::FIELD_CONTENT => $this->{OpportunityNotification::FIELD_CONTENT},
            'config_name' => $this->{OpportunityNotification::RELATION_CONFIG}?->name,
            'company_name' => $this->{OpportunityNotification::RELATION_COMPANY}?->name,
        ];
    }
}
