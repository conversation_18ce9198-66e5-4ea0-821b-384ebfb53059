<?php

namespace App\Http\Resources\OpportunityNotifications;

use App\Models\MissedProducts\OpportunityNotificationConfig;
use Illuminate\Http\Resources\Json\ResourceCollection;

/**
 * Class OpportunityNotificationsConfigCollection
 * @mixin OpportunityNotificationConfig
 */
class OpportunityNotificationConfigCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray($request): array
    {
        return parent::toArray($request);
    }
}
