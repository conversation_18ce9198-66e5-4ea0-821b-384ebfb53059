<?php

namespace App\Http\Resources\Reviews;

use App\Enums\Reviews\ReviewQuestion;
use App\Enums\Reviews\ReviewQuestionDataType;
use App\Models\ConsumerReviews\ReviewData;
use App\Models\Odin\Company;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;
use JsonSerializable;

/**
 * @mixin ReviewData
 */
class ReviewDataResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray(Request $request): array|Arrayable|JsonSerializable
    {
        return [
            ReviewData::FIELD_OVERALL_SCORE => $this->overall_score,
            ReviewData::FIELD_TITLE         => $this->title,
            ReviewData::FIELD_COMMENTS      => $this->comments,
            ReviewData::FIELD_DATA          => $this->getDataPayload()
        ];
    }

    /**
     * @return array
     */
    protected function getDataPayload(): array
    {
        $data = $this->data;
        $data[ReviewData::DATA_KEY_CUSTOM] = $this->processCustomDataPayload($data[ReviewData::DATA_KEY_CUSTOM] ?? []);

        return $data;
    }

    /**
     * @param array $customDataPayload
     * @return array
     */
    protected function processCustomDataPayload(array $customDataPayload): array
    {
        $output = [];
        foreach ($customDataPayload as $dataKey => $dataPayload) {
            $reviewQuestion = ReviewQuestion::tryFrom($dataKey) ?? null;
            if ($reviewQuestion && ($dataPayload[ReviewData::DATA_KEY_VALUE] ?? null) !== null && $reviewQuestion?->showToCompany() !== false) {
                $output[$dataKey] = $dataPayload;
                $output[$dataKey]['title'] = $reviewQuestion->getTitle() ?? Str::headline($dataKey);
                $output[$dataKey]['order'] = $reviewQuestion->getOrder() ?? null;
                $type = $reviewQuestion->getDataType();
                if ($type === ReviewQuestionDataType::COMPANY_ID) {
                    $output[$dataKey][ReviewData::DATA_KEY_VALUE] = Company::query()->find($dataPayload[ReviewData::DATA_KEY_VALUE])
                        ?->name ?? 'Unknown Company';
                }
                else if ($type === ReviewQuestionDataType::COMPANY_REFERENCE) {
                    $output[$dataKey][ReviewData::DATA_KEY_VALUE] = Company::query()
                        ->where(Company::FIELD_REFERENCE, $output[$dataKey][ReviewData::DATA_KEY_VALUE])
                        ->first()
                        ?->name ?? 'Unknown Company';
                }
            }
        }

        return $output;
    }

}