<?php

namespace App\Http\Resources\Reviews;

use App\Enums\CompanyConsumerReviewStatus;
use App\Models\ConsumerReviews\Review;
use Exception;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin Review
 */
class ReviewResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array|Arrayable|\JsonSerializable
     * @throws Exception
     */
    public function toArray(Request $request): array|Arrayable|JsonSerializable
    {
        return [
            'reference'          => $this->{Review::FIELD_UUID},
            'reviewer_name'      => htmlspecialchars_decode($this->{Review::RELATION_REVIEWER}->name),
            'reviewer_email'     => $this->reviewer?->email ?? '',
            'status'             => $this->{Review::FIELD_STATUS},
            'company_id'         => $this->company_id,
            'company_name'       => $this->company?->name ?? 'Unknown',
            'display_status'     => CompanyConsumerReviewStatus::label($this->{Review::FIELD_STATUS}),
            'created_at'         => $this->created_at->timestamp,
            'review_is_verified' => $this->{Review::FIELD_IS_VERIFIED},
            'industry'           => $this->industry?->slug,
            'website_name'       => $this->website?->name ?? '',
            'review_replies'     => ReviewReplyResource::collection($this->reviewReplies),
            'review_data'        => new ReviewDataResource($this->reviewData ?? []),
        ];
    }
}
