<?php

namespace App\Http\Resources\Reviews;

use App\Models\ConsumerReviews\ReviewReply;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin ReviewReply
 */
class ReviewReplyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray(Request $request): array|Arrayable|JsonSerializable
    {
        return [
            'comments'   => $this->comments,
            'user'       => $this->getUserPayload(),
            'created_at' => $this->created_at?->timestamp,
        ];
    }

    /**
     * @return array
     */
    protected function getUserPayload(): array
    {
        $displayName = $this->admin_user_id
            ? $this->custom_admin_name ?? $this->user->name
            : $this->companyUser?->completeName() ?? '';

        return [
            'name' => $displayName,
        ];
    }
}