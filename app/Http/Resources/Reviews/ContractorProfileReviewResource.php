<?php

namespace App\Http\Resources\Reviews;

use App\Models\ConsumerReviews\Review;
use App\Models\ConsumerReviews\ReviewData;
use Exception;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin Review
 */
class ContractorProfileReviewResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array|Arrayable|\JsonSerializable
     * @throws Exception
     */
    public function toArray(Request $request): array|Arrayable|JsonSerializable
    {
        return [
            'title'    => htmlspecialchars_decode($this->reviewData->title),
            'content'  => htmlspecialchars_decode($this->reviewData->comments),
            'name'     => htmlspecialchars_decode($this->reviewer->name),
            'location' => $this->reviewData->data->{ReviewData::DATA_KEY_DISPLAY_LOCATION} ?? "N/A",
            'date'     => $this->reviewData->created_at,
            'overall_score' => $this->reviewData->overall_score,
        ];
    }
}
