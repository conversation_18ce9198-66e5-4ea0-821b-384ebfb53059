<?php

namespace App\Http\Resources\Reviews;

use App\Models\ConsumerReviews\CompanyRating;
use App\Models\ConsumerReviews\ReviewData;
use Exception;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin CompanyRating
 */
class ContractorProfileRatingsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array|Arrayable|\JsonSerializable
     * @throws Exception
     */
    public function toArray(Request $request): array|Arrayable|JsonSerializable
    {
        return [
            'review_rating' => $this->average_score ?? 0,
            'review_count'  => $this->num_reviews ?? 0,
            'five_star'     => $this->data["count_breakdown"]["5 Star"] ?? 0,
            'four_star'     => $this->data["count_breakdown"]["4 Star"] ?? 0,
            'three_star'    => $this->data["count_breakdown"]["3 Star"] ?? 0,
            'two_star'      => $this->data["count_breakdown"]["2 Star"] ?? 0,
            'one_star'      => $this->data["count_breakdown"]["1 Star"] ?? 0,
        ];
    }
}
