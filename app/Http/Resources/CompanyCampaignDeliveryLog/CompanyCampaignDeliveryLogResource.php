<?php

namespace App\Http\Resources\CompanyCampaignDeliveryLog;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryLog;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleCRM;
use App\Models\Odin\Company;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Carbon;

/**
 * @mixin CompanyCampaignDeliveryLog
 */
class CompanyCampaignDeliveryLogResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        /** @var CompanyCampaign|null $campaign */
        $campaign = $this->campaign()->withTrashed()->first();

        /** @var Company $company */
        $company = $campaign?->{CompanyCampaign::RELATION_COMPANY} ?? null;

        /** @var ConsumerProduct $product */
        $product = $this->{CompanyCampaignDeliveryLog::RELATION_PRODUCT} ?? null;

        /** @var Consumer $consumer */
        $consumer = $product->{ConsumerProduct::RELATION_CONSUMER} ?? null;

        /** @var CompanyCampaignDeliveryModuleCRM $crm */
        $crm = $this->{CompanyCampaignDeliveryLog::RELATION_CRM} ?? null;

        return [
            'id'               => $this->id,
            'date'             => Carbon::parse($this->created_at)->format('F j, Y H:i'),
            'company'          => $company ? [
                                                'id'   => $company->{Company::FIELD_ID} ?? 'N/A',
                                                'name' => $company->{Company::FIELD_NAME} ?? 'N/A',
                                            ] : null,
            'campaign'         => $campaign ? [
                                                'id'     => $campaign->{CompanyCampaign::FIELD_ID} ?? 'N/A',
                                                'name'   => $campaign->{CompanyCampaign::FIELD_NAME} ?? 'N/A',
                                                'status' => $campaign->{CompanyCampaign::FIELD_STATUS}?->getDisplayName() ?? 'N/A'
                                            ] : null,
            'consumer_product' => $product ? [
                                                'id'     => $product->{ConsumerProduct::FIELD_ID} ?? 'N/A',
                                                'name'   => $consumer?->getFullName() ?? 'N/A',
                                                'status' => ConsumerProduct::STATUS_TEXT[$product->{ConsumerProduct::FIELD_STATUS}] ?? 'Unknown'
                                            ] : null,
            'crm_display_name' => $crm?->display_name ?? 'N/A',
            'success'          => $this->success ? 'Success' : 'Failure',
            'payload'          => $this->payload,
        ];
    }

}
