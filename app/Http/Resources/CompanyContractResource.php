<?php

namespace App\Http\Resources;

use App\Helpers\CarbonHelper;
use App\Models\CompanyContract;
use App\Models\Odin\CompanyUser;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin CompanyContract
 */
class CompanyContractResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return [
            'id'              => $this->{CompanyContract::FIELD_ID},
            'date'            => CarbonHelper::parse($this->created_at)->toFormat(),
            'user_id'         => $this->{CompanyContract::FIELD_COMPANY_USER_ID},
            'ip_address'      => $this->{CompanyContract::FIELD_IP_ADDRESS},
            'user_first_name' => $this->{CompanyContract::RELATION_COMPANY_USER}?->{CompanyUser::FIELD_FIRST_NAME},
            'user_last_name'  => $this->{CompanyContract::RELATION_COMPANY_USER}?->{CompanyUser::FIELD_LAST_NAME},
            'source'          => 'a20'
        ];
    }
}
