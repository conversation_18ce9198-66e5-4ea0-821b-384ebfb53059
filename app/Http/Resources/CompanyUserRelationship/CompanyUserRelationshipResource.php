<?php

namespace App\Http\Resources\CompanyUserRelationship;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\CompanyUserRelationship;
use Illuminate\Http\Request;

/**
 * @mixin CompanyUserRelationship
 */
class CompanyUserRelationshipResource extends BaseJsonResource
{
    /**
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return [
            'id'                  => $this->id,
            'name'                => $this->user->name ?? 'Unknown',
            'commissionable_from' => $this->commissionable_at,
            'commissionable_to'   => $this->commissionable_to,
            'deleted_at'          => $this->deleted_at,
            'created_at'          => $this->created_at,
            'active'              => !$this->deleted_at,
            'role'                => $this->role->name ?? 'Unknown',
        ];
    }
}
