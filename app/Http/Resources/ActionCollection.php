<?php

namespace App\Http\Resources;

use App\Models\Action;
use Illuminate\Http\Resources\Json\ResourceCollection;

/**
 * Class ActionCollection
 * @mixin Action
 */
class ActionCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray($request): array
    {
        return parent::toArray($request);
    }
}
