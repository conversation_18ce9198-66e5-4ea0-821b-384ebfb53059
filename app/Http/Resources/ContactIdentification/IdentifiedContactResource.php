<?php

namespace App\Http\Resources\ContactIdentification;

use App\Enums\ContactIdentification\IdentifiableModelType;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\ContactIdentification\IdentifiedContact;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Client\Request;

/**
 * @mixin IdentifiedContact
 */
class IdentifiedContactResource extends BaseJsonResource
{
    public function __construct($resource, protected Model $relatedModel)
    {
        parent::__construct($resource);
    }

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     * @throws \Exception
     */
    public function toArray($request): array
    {
        if (!isset($this->id)) {
            return [
                'related_model_id'      => $this->relatedModel->id,
                'related_model_type'    => IdentifiableModelType::fromModelClass($this->relatedModel::class),
                'identified_contact_id' => null,
                'identifier_value'      => 'unknown',
                'identifier_field_type' => null,
                'identification_status' => 'unknown',
                'nominated_contact'     => [],
                'possible_contacts'     => []
            ];
        }

        $possibleContacts = $this->possibleContacts->take(100);

        return [
            'related_model_id'      => $this->relatedModel->id,
            'related_model_type'    => IdentifiableModelType::fromModelClass($this->relatedModel::class),
            'identified_contact_id' => $this->id,
            'identifier_value'      => $this->identifier_value,
            'identifier_field_type' => $this->identifier_field_type,
            'identification_status' => $this->identification_status,
            'nominated_contact'     => new PossibleContactResource($this->nominatedContact),
            'possible_contacts'     => PossibleContactResource::collection($possibleContacts)
        ];
    }
}
