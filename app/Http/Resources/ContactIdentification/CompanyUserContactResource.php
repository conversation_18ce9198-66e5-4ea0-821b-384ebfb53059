<?php

namespace App\Http\Resources\ContactIdentification;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Odin\CompanyUser;
use Illuminate\Http\Client\Request;

/**
 * @mixin CompanyUser
 */
class CompanyUserContactResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'name'              => $this->completeName(),
            'first_name'        => $this->first_name,
            'last_name'         => $this->last_name,
            'title'             => $this->title,
            'department'        => $this->department,
            'phone'             => $this->cell_phone,
            'office_phone'      => $this->office_phone,
            'email'             => $this->email,
            'company'           => [
                'id'    => $this->company?->id ?? 'Unknown',
                'name'  => $this->company?->name ?? 'Unknown',
            ]
        ];
    }
}
