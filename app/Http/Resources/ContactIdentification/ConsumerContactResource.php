<?php

namespace App\Http\Resources\ContactIdentification;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Odin\Consumer;
use Illuminate\Http\Client\Request;

/**
 * @mixin Consumer
 */
class ConsumerContactResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'name'              => $this->getFullName(),
            'email'             => $this->email,
            'product'           => 'Lead'
        ];
    }
}
