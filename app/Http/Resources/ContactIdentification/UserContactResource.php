<?php

namespace App\Http\Resources\ContactIdentification;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\User;
use Illuminate\Http\Client\Request;

/**
 * @mixin User
 */
class UserContactResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'name'              => $this->name,
            'email'              => $this->email,
            'role'              => ''
        ];
    }
}
