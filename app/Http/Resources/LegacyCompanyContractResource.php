<?php

namespace App\Http\Resources;

use App\Helpers\CarbonHelper;
use App\Models\Legacy\EloquentHistoryLog;
use App\Models\Legacy\EloquentUser;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin EloquentHistoryLog
 */
class LegacyCompanyContractResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return [
            'id'              => $this->{EloquentHistoryLog::ID},
            'date'            => CarbonHelper::parse($this->date)->toFormat(),
            'user_id'         => $this->userid,
            'ip_address'      => $this->ipaddress,
            'user_first_name' => $this->{EloquentHistoryLog::RELATION_USER}->{EloquentUser::FIRST_NAME},
            'user_last_name'  => $this->{EloquentHistoryLog::RELATION_USER}->{EloquentUser::LAST_NAME},
            'source'          => 'legacy'
        ];
    }
}
