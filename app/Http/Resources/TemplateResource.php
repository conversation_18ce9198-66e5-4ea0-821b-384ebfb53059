<?php

namespace App\Http\Resources;

use App\Models\Template;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Template
 */
class TemplateResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'type' => $this->type,
            'name' => $this->name,
            'description' => $this->description,
            'subject' => $this->subject,
            'content' => $this->content,
            'updated_at' => $this->updated_at,
            'owner' => [
                'name' => $this->owner?->name ?? 'Unknown',
                'email' => $this->owner?->email ?? 'Unknown'
            ]
        ];
    }
}
