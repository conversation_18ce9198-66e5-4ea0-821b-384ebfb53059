<?php

namespace App\Http;

use App\Http\Middleware\ActivityLogCauser;
use App\Http\Middleware\Appointments\AuthenticateConsumerAppointment;
use App\Http\Middleware\Appointments\CheckAppointmentCancellable;
use App\Http\Middleware\Appointments\CheckAppointmentHost;
use App\Http\Middleware\Appointments\ValidateAppointmentKeyCode;
use App\Http\Middleware\AuthenticateAffiliatesPortal;
use App\Http\Middleware\AuthenticateClientDashboard;
use App\Http\Middleware\AuthenticateClientToken;
use App\Http\Middleware\AuthenticatePingPostAffiliate;
use App\Http\Middleware\AuthenticateWithBearerToken;
use App\Http\Middleware\AuthorizeTwilioWebhook;
use App\Http\Middleware\CompanyRegistration\HasRegistrationReference;
use App\Http\Middleware\CompanyRegistration\HasValidatedRegistrationReference;
use App\Http\Middleware\DashboardApiAuth;
use App\Http\Middleware\Odin\OdinResourceApiMiddleware;
use App\Http\Middleware\Odin\VerifyExternalWebsiteToken;
use App\Http\Middleware\RateLimitMiddleware;
use App\Http\Middleware\RegisterAdmin2ActioningUser;
use App\Http\Middleware\SanitizeRequestDataMiddleware;
use App\Http\Middleware\ValidateAccountManager;
use App\Http\Middleware\VerifyCsrfToken;
use App\Http\Middleware\VerifyLegacyAdminBearerToken;
use Illuminate\Foundation\Http\Kernel as HttpKernel;
use Laravel\Sanctum\Http\Middleware\CheckAbilities;
use Laravel\Sanctum\Http\Middleware\CheckForAnyAbility;

class Kernel extends HttpKernel
{
    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array<int, class-string|string>
     */
    protected $middleware = [
        // \App\Http\Middleware\TrustHosts::class,
        \App\Http\Middleware\TrustProxies::class,
        \Illuminate\Http\Middleware\HandleCors::class,
        \App\Http\Middleware\PreventRequestsDuringMaintenance::class,
        \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
        \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array<string, array<int, class-string|string>>
     */
    protected $middlewareGroups = [
        'auth' => [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\TrimStrings::class,
            \App\Http\Middleware\Verify2FAMiddleware::class,
        ],

        'web' => [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\TrimStrings::class,
            \App\Http\Middleware\Authenticate::class,
            \App\Http\Middleware\Verify2FAMiddleware::class,
        ],

        'public-web' => [
            \App\Http\Middleware\TrimStrings::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ],

        'internal-api' => [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\TrimStrings::class,
            \App\Http\Middleware\Authenticate::class,
            ActivityLogCauser::class,
        ],

        'public-api' => [
            'throttle:api',
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\TrimStrings::class,
        ],

        'api' => [
            // \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
            // 'throttle:api',
            \App\Http\Middleware\TrimStrings::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ],
    ];

    /**
     * The application's route middleware.
     *
     * These middleware may be assigned to groups or used individually.
     *
     * @var array<string, class-string|string>
     */
    protected $routeMiddleware = [
        'auth' => \App\Http\Middleware\Authenticate::class,
        'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'auth.session' => \Illuminate\Session\Middleware\AuthenticateSession::class,
        'auth.bearer' => AuthenticateWithBearerToken::class,
        'cache.headers' => \Illuminate\Http\Middleware\SetCacheHeaders::class,
        'can' => \Illuminate\Auth\Middleware\Authorize::class,
        'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
        'password.confirm' => \Illuminate\Auth\Middleware\RequirePassword::class,
        'signed' => \Illuminate\Routing\Middleware\ValidateSignature::class,
        'signed-url' => \Spatie\UrlSigner\Laravel\Middleware\ValidateSignature::class,
        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'verified' => \Illuminate\Auth\Middleware\EnsureEmailIsVerified::class,

        'role' => \Spatie\Permission\Middleware\RoleMiddleware::class,
        'permission' => \Spatie\Permission\Middleware\PermissionMiddleware::class,
        'role_or_permission' => \Spatie\Permission\Middleware\RoleOrPermissionMiddleware::class,
        'authorize_twilio' => AuthorizeTwilioWebhook::class,

        'legacy_admin_bearer_token' => VerifyLegacyAdminBearerToken::class,

        'authenticate_client_dashboard' => AuthenticateClientDashboard::class,
        'validate_appointment_key_code' => ValidateAppointmentKeyCode::class,
        'authenticate_consumer_appointment' => AuthenticateConsumerAppointment::class,
        'check_appointment_host' => CheckAppointmentHost::class,
        'check_appointment_cancellable' => CheckAppointmentCancellable::class,

        'external_website_token' => VerifyExternalWebsiteToken::class,
        'authenticate-client-token' => AuthenticateClientToken::class,

        'odin_resource' => OdinResourceApiMiddleware::class,

        'registration-reference' => HasRegistrationReference::class,
        'registration-validated' => HasValidatedRegistrationReference::class,

        'dashboard-auth' => DashboardApiAuth::class,
        'rate-limit' => RateLimitMiddleware::class,
        'sanitize'   => SanitizeRequestDataMiddleware::class,
        'protect_from_impersonation' => \App\Http\Middleware\ProtectImpersonationSensitiveRoutes::class,

        'validate_account_manager' => ValidateAccountManager::class,
        'register-admin-actioning-user' => RegisterAdmin2ActioningUser::class,
        'model-scope-middleware' => \App\Http\Middleware\ModelScopeMiddleware::class,

        'a-leads-middleware' => AuthenticatePingPostAffiliate::class,

        'authenticate-affiliates-portal' => AuthenticateAffiliatesPortal::class,
    ];
}
