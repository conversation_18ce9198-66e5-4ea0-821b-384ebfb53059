<?php

namespace App\Http\Controllers\CompanyMetrics;

use App\Enums\CompanyMetrics\CompanyMetricRequestTypes;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Resources\CompanyMetrics\CompanyMetricsSummaryResource;
use App\Jobs\CompanyMetrics\GetCompanyDomainMetricsJob;
use App\Models\CompanyMetric;
use App\Models\Odin\Company;
use App\Services\CompanyMetrics\SemrushMetricsService;
use App\Services\CompanyMetricsService\CompanyMetricsService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Number;

class CompanyMetricsController extends APIController
{
    const string AVG_SPEND              = 'avg_spend';
    const string BREAKDOWN              = 'breakdown';
    const string STATUS                 = 'status';
    const string MESSAGE                = 'message';
    const string LAST_CALCULATED_AT     = 'last_calculated_at';
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected CompanyMetricsService $companyMetricsService,

    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param int $companyId
     * @return JsonResponse
     */
    public function getCompanyPpcSpendData(int $companyId): JsonResponse
    {
        $ppc = $this->companyMetricsService->getCompanyMetrics($companyId,  CompanyMetricRequestTypes::PPC_SPEND);

        $latestMetrics = $this->companyMetricsService->getLatestCompanyMetric($companyId,  CompanyMetricRequestTypes::PPC_SPEND);

        return $this->formatResponse([
            self::STATUS                => !$ppc->isEmpty(),
            self::AVG_SPEND             => !$ppc->isEmpty() ? Number::currency($this->companyMetricsService->getAverageCompanyPpcSpend(ppcSpendCollection: $ppc)) : null,
            self::BREAKDOWN             => !$ppc->isEmpty() ? CompanyMetricRequestTypes::PPC_SPEND->getResourceCollection($ppc) : [],
            self::LAST_CALCULATED_AT    => $latestMetrics ? Carbon::parse($latestMetrics->{CompanyMetric::FIELD_UPDATED_AT})->format('F j Y') : null,
        ]);
    }

    /**
     * @param int $companyId
     * @return JsonResponse
     */
    public function getCompanyAveragePpcSpend(int $companyId): JsonResponse
    {
        $ppc = $this->companyMetricsService->getCompanyMetrics($companyId, CompanyMetricRequestTypes::PPC_SPEND);

        return $this->formatResponse([
            self::AVG_SPEND => $this->companyMetricsService->getAverageCompanyPpcSpend($ppc)
        ]);
    }

    public function getSummary(Company $company): JsonResponse
    {
        return $this->formatResponse([
            'metrics' =>new CompanyMetricsSummaryResource(
                $this->companyMetricsService->getLatestCompanyMetric($company->id, CompanyMetricRequestTypes::TRAFFIC_SUMMARY)
            )
        ]);
    }

    public function getMetrics(Company $company, GetCompanyDomainMetricsJob $companyDomainMetricsJob): JsonResponse
    {
        $companyDomainMetricsJob::dispatchSync($company);

        return $this->formatResponse([
            'status' => true
        ]);
    }

    public function getApiCount(Company $company, SemrushMetricsService $semrushMetricsService): JsonResponse
    {
        return $this->formatResponse([
            'count' => $semrushMetricsService->getApiCount()
        ]);
    }

    public function getPermitMetrics(Company $company, CompanyMetricsService $companyMetricsService): JsonResponse
    {
        return $this->formatResponse([
            'metrics' => $companyMetricsService->getPermitMetricsForCompany($company, now()->subDays(30))
        ]);
    }
}
