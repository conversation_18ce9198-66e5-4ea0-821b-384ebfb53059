<?php

namespace App\Http\Controllers;

use App\Enums\ContactIdentification\ContactType;
use App\Enums\ContactIdentification\IdentifiableModelType;
use App\Enums\ContactIdentification\SearchableFieldType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\ContactIdentification\IdentifyAndNominateContactRequest;
use App\Http\Requests\ContactIdentification\NominateContactRequest;
use App\Http\Resources\ContactIdentification\IdentifiedContactResource;
use App\Models\ContactIdentification\IdentifiedContact;
use App\Models\ContactIdentification\PossibleContact;
use App\Services\ContactIdentification\ContactIdentificationApiService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use phpDocumentor\GraphViz\Exception;

class ContactIdentificationController extends APIController
{
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected ContactIdentificationApiService $contactIdentificationApiService
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param IdentifyAndNominateContactRequest $request
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function identifyAndNominateContact(IdentifyAndNominateContactRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $modelType = IdentifiableModelType::tryFrom(Arr::get($validated, IdentifyAndNominateContactRequest::FIELD_MODEL_TYPE));
        $modelId = Arr::get($validated, IdentifyAndNominateContactRequest::FIELD_MODEL_ID);
        $model = app()->make($modelType->getModelClass());
        $entity = $model->findOrFail($modelId);

        $identifiedContact = $this->contactIdentificationApiService->identifyAndNominateContact(
            ContactType::tryFrom(Arr::get($validated, IdentifyAndNominateContactRequest::FIELD_CONTACT_RELATION_TYPE)),
            Arr::get($validated, IdentifyAndNominateContactRequest::FIELD_CONTACT_RELATION_ID),
            Arr::get($validated, IdentifyAndNominateContactRequest::FIELD_IDENTIFIER_VALUE),
            SearchableFieldType::tryFrom(Arr::get($validated, IdentifyAndNominateContactRequest::FIELD_IDENTIFIER_FIELD_TYPE)),
            $entity,
        );

        return $this->formatResponse([
            'status' => true,
            'entity' => new IdentifiedContactResource($identifiedContact, $entity)
        ]);
    }

    /**
     * @param NominateContactRequest $request
     * @param IdentifiedContact $identifiedContact
     * @return JsonResponse
     */
    public function nominateContact(NominateContactRequest $request): JsonResponse
    {
        $validated = $request->validated();

        /** @var PossibleContact $possibleContact */
        $possibleContact = PossibleContact::query()->findOrFail(Arr::get($validated, NominateContactRequest::FIELD_CONTACT_ID));
        /** @var IdentifiedContact $identifiedContact */
        $identifiedContact = IdentifiedContact::query()->findOrFail(Arr::get($validated, NominateContactRequest::FIELD_IDENTIFIED_CONTACT_ID));

        if ($possibleContact->{PossibleContact::FIELD_IDENTIFIED_CONTACT_ID} !== $identifiedContact->{IdentifiedContact::FIELD_ID}) {
            throw new Exception('Update cross contacts not allowed');
        }

        $status = $this->contactIdentificationApiService->nominateContact(
            $identifiedContact,
            $possibleContact
        );


        return $this->formatResponse([
            'status' => true,
            'entity' => new IdentifiedContactResource($identifiedContact, $relatedModel)
        ]);
    }
}
