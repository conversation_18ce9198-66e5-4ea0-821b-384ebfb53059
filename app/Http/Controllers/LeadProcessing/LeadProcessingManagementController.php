<?php


namespace App\Http\Controllers\LeadProcessing;


use App\Contracts\Services\Communication\CommunicationContract;
use App\Http\Controllers\Controller;
use Illuminate\Contracts\View\View;

use Illuminate\Http\JsonResponse;
use Twilio\Rest\Client;

class LeadProcessingManagementController extends Controller
{
    public function index(): View
    {
        return view('lead-processing-management');
    }
}
