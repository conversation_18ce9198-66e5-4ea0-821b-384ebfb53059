<?php

namespace App\Http\Controllers\DashboardAPI\V4;

use App\Enums\Alert\AlertType;
use App\Http\Controllers\DashboardAPI\BaseDashboardApiController;
use App\Http\Requests\Dashboard\StoreAlertRequest;
use App\Http\Resources\AlertResource;
use App\Models\Alert;
use App\Models\Campaigns\CompanyCampaign;
use App\Repositories\Alert\AlertRepository;
use App\Services\Dashboard\DashboardAuthService;
use App\Services\Dashboard\JWT\DashboardJWTService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CampaignAlertController extends BaseDashboardApiController
{

    public function __construct(
        protected Request $request,
        protected DashboardAuthService $authService,
        protected DashboardJWTService $jwtService,
        protected AlertRepository $alertRepository
    )
    {
        parent::__construct($request, $authService, $jwtService);
    }

    /**
     * @param StoreAlertRequest $request
     *
     * @return JsonResponse
     ß*/
    public function createAlert(StoreAlertRequest $request): JsonResponse
    {
       $alert = $this->alertRepository->createOrUpdateCampaignAlert(
           companyCampaign: $this->getCampaign(),
           alertType: AlertType::from($request->validated(Alert::FIELD_TYPE)),
           payload: $request->validated(Alert::FIELD_PAYLOAD),
           active: $request->validated(Alert::FIELD_ACTIVE),
           recipients: $request->validated(StoreAlertRequest::RECIPIENTS)
       );

        return $this->formatResponse([
            'status' => true,
            'alert' => new AlertResource($alert)
        ]);
    }

    /**
     * @param StoreAlertRequest $request
     *
     * @return JsonResponse
     ß*/
    public function updateAlert(StoreAlertRequest $request): JsonResponse
    {
        $alert = $this->alertRepository->createOrUpdateCampaignAlert(
            companyCampaign: $this->getCampaign(),
            alertType: AlertType::from($request->validated(Alert::FIELD_TYPE)),
            payload: $request->validated(Alert::FIELD_PAYLOAD),
            active: $request->validated(Alert::FIELD_ACTIVE),
            recipients: $request->validated(StoreAlertRequest::RECIPIENTS),
            alert: $this->getAlert()
        );

        return $this->formatResponse([
            'status' => true,
            'alert' => new AlertResource($alert)
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function deleteAlert(): JsonResponse
    {
        return $this->formatResponse([
            'status' => $this->getAlert()->delete()
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function toggleActive(): JsonResponse
    {
        $alert = $this->getAlert();
        $alert->active = !$alert->active;

        return $this->formatResponse([
            'status' => $alert->save(),
            'active' => $alert->active
        ]);
    }

    /**
     * @return Alert
     */
    protected function getAlert(): Alert
    {
        return $this->alertRepository->findAlertForCampaignOrFail(
            $this->request->route('campaignReference'),
            $this->request->route('alertId')
        );
    }

    /**
     * @return CompanyCampaign
     */
    protected function getCampaign(): CompanyCampaign
    {
        return CompanyCampaign::query()
            ->where(CompanyCampaign::FIELD_REFERENCE, $this->request->route('campaignReference'))
            ->firstOrFail();
    }
}
