<?php

namespace App\Http\Controllers\DashboardAPI\V4;

use App\Builders\Odin\ProductAssignmentBuilder;
use App\Enums\Odin\Product as ProductEnum;
use App\Http\Controllers\DashboardAPI\BaseDashboardApiController;
use App\Http\Requests\Odin\ProductAssignmentSearchRequest;
use App\Http\Resources\Dashboard\ProductResource;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductRejection;
use App\Models\Odin\ServiceProduct;
use App\Services\Odin\ProductAssignmentService;
use App\Services\ProductRejectionCalculationService;
use Carbon\Carbon;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\JsonResponse;

class ProductAssignmentController extends BaseDashboardApiController
{
    const DEFAULT_PAGINATION_LIMIT = 10;

    const REQUEST_PRODUCT_ASSIGNMENT_ID = 'product_assignment_id';
    const REQUEST_REJECTION_DESCRIPTION = 'description';
    const REQUEST_DOWNLOAD              = 'download';

    const RESPONSE_STATUS              = 'status';
    const RESPONSE_PRODUCT_ASSIGNMENT  = 'product_assignment';
    const RESPONSE_PRODUCT_ASSIGNMENTS = 'product_assignments';
    const RESPONSE_TOTAL_SPEND         = 'total_spend';
    const RESPONSE_MESSAGE             = 'message';
    const RESPONSE_CAN_REJECT          = 'can_reject';

    public function search(ProductAssignmentSearchRequest $request): JsonResponse
    {
        $service = $this->getService();
        $product = $this->getProduct();
        $company = $this->getCompany();
        $serviceProduct = ServiceProduct::query()
            ->where(ServiceProduct::FIELD_PRODUCT_ID, $product->id)
            ->where(ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $service->id)
            ->firstOrFail();

        $searchParams = $request->safe()->toArray();
        $parentProductId = $this->getParentProductId($product);

        $query = ProductAssignmentBuilder::query()
            ->forServiceProduct($serviceProduct->id)
            ->forCompanyId($company->id)
            ->delivered(true)
            ->forCompanyCampaignReference($searchParams[ProductAssignmentSearchRequest::CAMPAIGN_REFERENCE] ?? null)
            ->forStateOrZip($searchParams[ProductAssignmentSearchRequest::TEXT_SEARCH_STATE_ZIP_CODE] ?? null)
            ->forFromDate($this->getFromDateFromRequest($searchParams))
            ->forToDate($this->getToDateFromRequest($searchParams))
            ->forName($searchParams[ProductAssignmentSearchRequest::TEXT_SEARCH_NAME_EMAIL] ?? null)
            ->forInvoiceId($searchParams[ProductAssignmentSearchRequest::INVOICE_ID] ?? null)
            ->forStatus($searchParams[ProductAssignmentSearchRequest::STATUS] ?? null)
            ->qualifiedOverBudget($searchParams[ProductAssignmentSearchRequest::OVER_BUDGET] ?? null)
            ->forLeadId($searchParams[ProductAssignmentSearchRequest::PRODUCT_ASSIGNMENT_ID] ?? null)
            ->withDashboardFieldsForServiceId($service->id)
            ->withDashboardFieldsForIndustryId($service->industry_id)
            ->withAddress(true)
            ->withTracking(true);
            // ->removeLegacyPreviewLeads(true);

        if ($parentProductId)
            $query->includeParentProductAndService($parentProductId, $service->id);

        if ($request->has(self::REQUEST_DOWNLOAD) && ($request->get(self::REQUEST_DOWNLOAD) === true || $request->get(self::REQUEST_DOWNLOAD) === 'true')) {
            ini_set('max_execution_time', '60');

            return $this->formatResponse([
                self::RESPONSE_STATUS              => true,
                self::RESPONSE_PRODUCT_ASSIGNMENTS => ProductResource::collection($query->get())->resource,
            ]);
        }

        $productAssignments = $query->paginate(
            $searchParams[ProductAssignmentSearchRequest::PER_PAGE] ?? self::DEFAULT_PAGINATION_LIMIT,
            ['*'],
            'page',
            $searchParams[ProductAssignmentSearchRequest::PAGE] ?? 1,
        );

        $totalSpend = $query
            ->withAppointment(false)
            ->forStatus('purchased')
            ->withAppointment(false)
            ->withAddress(false)
            ->withTracking(false)
            ->getSpend();

        return $this->formatResponse([
            self::RESPONSE_STATUS              => true,
            self::RESPONSE_PRODUCT_ASSIGNMENTS => ProductResource::collection($productAssignments)->resource,
            self::RESPONSE_TOTAL_SPEND         => $totalSpend,
        ]);
    }

    /**
     * @param ProductAssignmentService $productAssignmentService
     * @param ProductRejectionCalculationService $calculationService
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function reject(ProductAssignmentService $productAssignmentService, ProductRejectionCalculationService $calculationService): JsonResponse
    {
        $userId = $this->getUser()->id;
        $company = $this->getCompany();

        $validated = $this->request->validate([
            self::REQUEST_REJECTION_DESCRIPTION => 'string|nullable',
            ProductRejection::FIELD_REASON      => 'string',
        ]);

        $productAssignment = $this->getProductAssignment();

        $rejectionUpdate = [
            ProductRejection::FIELD_REASON          => $this->getRejectionReasonString($validated[ProductRejection::FIELD_REASON], $validated[self::REQUEST_REJECTION_DESCRIPTION] ?? ""),
            ProductRejection::FIELD_COMPANY_USER_ID => $userId,
        ];

        $success = $productAssignmentService->validateAndRejectProduct($company, $productAssignment, $rejectionUpdate);

        $canReject = $success
            ? $calculationService->companyCanManuallyRejectProduct($company, $productAssignment->consumerProduct->serviceProduct->product)
            : null;

        return $this->formatResponse([
            self::RESPONSE_STATUS             => $success,
            self::RESPONSE_PRODUCT_ASSIGNMENT => new ProductResource($productAssignment->refresh()),
            self::RESPONSE_CAN_REJECT         => $canReject,
        ]);
    }

    /**
     * @param ProductAssignmentService $productAssignmentService
     * @param ProductRejectionCalculationService $calculationService
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function unreject(ProductAssignmentService $productAssignmentService, ProductRejectionCalculationService $calculationService): JsonResponse
    {
        $company = $this->getCompany();
        $productAssignment = $this->getProductAssignment();

        $success = $productAssignmentService->unrejectProductAssignment($company, $productAssignment);
        $canReject = $success
            ? $calculationService->companyCanManuallyRejectProduct($company, $productAssignment->consumerProduct->serviceProduct->product)
            : null;

        return $this->formatResponse([
            self::RESPONSE_STATUS             => $success,
            self::RESPONSE_PRODUCT_ASSIGNMENT => new ProductResource($productAssignment->refresh()),
            self::RESPONSE_CAN_REJECT         => $canReject,
        ]);
    }

    /**
     * @param ProductAssignmentService $productAssignmentService
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function rejectionWillExceedThreshold(ProductAssignmentService $productAssignmentService): JsonResponse
    {
        $company = $this->getCompany();
        $productAssignment = $this->getProductAssignment();
        $productName = $productAssignment->consumerProduct->serviceProduct->product->name;

        $willExceedThreshold = $productAssignmentService->rejectionWillExceedThreshold($company, $productAssignment);
        $message = $willExceedThreshold
            ? "Warning: Rejecting this $productName could affect your chances of winning future {$productName}s, and prevent you from rejecting more {$productName}s for this period."
            : null;

        return $this->formatResponse([
            self::RESPONSE_STATUS  => true,
            self::RESPONSE_MESSAGE => $message,
        ]);
    }

    /**
     * @return ProductAssignment
     */
    private function getProductAssignment(): ProductAssignment
    {
        $valid = $this->request->validate([
            self::REQUEST_PRODUCT_ASSIGNMENT_ID => 'numeric'
        ]);

        /** @var ProductAssignment */
        return ProductAssignment::query()
            ->findOrFail($valid[self::REQUEST_PRODUCT_ASSIGNMENT_ID]);
    }

    /**
     * @param string $reason
     * @param string $description
     * @return string
     */
    private function getRejectionReasonString(string $reason, string $description): string
    {
        return $reason . "|" . $description;
    }

    /**
     * Currently only returns Appointment as a parent product for an Appointment query
     * @param Product $product
     * @return int|null
     */
    private function getParentProductId(Product $product): ?int
    {
        return $product->name === ProductEnum::APPOINTMENT->value
            ? $product->id
            : null;
    }

    /**
     * @param array $request
     *
     * @return Carbon|null
     */
    protected function getFromDateFromRequest(array $request): ?Carbon
    {
        $dateRange = $request[ProductAssignmentSearchRequest::DATE_RANGE] ?? [];
        $timestamp = $dateRange[0] ?? null;

        if (!$timestamp) {
            return null;
        }

        return Carbon::createFromTimestamp($timestamp / 1000)->startOfDay();
    }

    /**
     * @param array $request
     *
     * @return Carbon|null
     */
    protected function getToDateFromRequest(array $request): ?Carbon
    {
        $dateRange = $request[ProductAssignmentSearchRequest::DATE_RANGE] ?? [];
        $timestamp = $dateRange[1] ?? null;

        if (!$timestamp) {
            return null;
        }

        return Carbon::createFromTimestamp($timestamp / 1000)->endOfDay();
    }
}
