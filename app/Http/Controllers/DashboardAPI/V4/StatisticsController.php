<?php

namespace App\Http\Controllers\DashboardAPI\V4;

use App\Http\Controllers\DashboardAPI\BaseDashboardApiController;
use App\Services\Odin\ProductStatistics\ProductStatisticsService;
use Carbon\Carbon;
use Carbon\CarbonTimeZone;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class StatisticsController extends BaseDashboardApiController
{
    const string REQUEST_START_TIMESTAMP    = 'start_timestamp';
    const string REQUEST_SERVICE_FILTER     = 'services_filter';
    const string REQUEST_STATE_LOCATION_ID  = 'state_location_id';
    const string REQUEST_COUNTY_LOCATION_ID = 'county_location_id';

    const string RESPONSE_STATUS     = 'status';
    const string RESPONSE_GROUPS     = 'groups';
    const string RESPONSE_STATISTICS = 'statistics';

    /**
     * @param ProductStatisticsService $statisticsService
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getVolumeStatistics(ProductStatisticsService $statisticsService): JsonResponse
    {
        $company = $this->getCompany();
        $serviceProduct = $this->getServiceProduct();
        $startDate = $this->getStartDate();

        throw_if(is_null($serviceProduct), new ModelNotFoundException());

        $statisticsService->setTimezone($this->getTimezone());

        $data = $statisticsService->getCompanyVolumeStatistics(
            companyId: $company->id,
            serviceProductIds: [$serviceProduct->id],
            startDate: $startDate,
        );

        return $this->formatResponse([
            self::RESPONSE_STATUS => true,
            self::RESPONSE_GROUPS => $data
        ]);
    }

    /**
     * @param ProductStatisticsService $statisticsService
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getProfitabilityStatistics(ProductStatisticsService $statisticsService): JsonResponse
    {
        $company = $this->getCompany();
        $serviceProduct = $this->getServiceProduct();
        $startDate = $this->getStartDate();

        if ($serviceProduct === null)
            throw new ModelNotFoundException();

        $data = $statisticsService->getCompanyProfitabilityStatistics(
            companyId: $company->id,
            serviceProductIds: [$serviceProduct->id],
            startDate: $startDate,
        );

        return $this->formatResponse([
            self::RESPONSE_STATUS => true,
            self::RESPONSE_GROUPS => $data
        ]);
    }

    /**
     * @param ProductStatisticsService $statisticsService
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getDefaultAnalyticsStatistics(ProductStatisticsService $statisticsService): JsonResponse
    {
        $company = $this->getCompany();
        $serviceProduct = $this->getServiceProduct();
        $startDate = $this->getStartDate();

        $statisticsService->setTimezone($this->getTimezone());

        $combinedData = $statisticsService->getAnalyticsPageDefaultStatistics(
            companyId: $company->id,
            serviceProductId: $serviceProduct->id,
            startDate: $startDate,
        );

        return $this->formatResponse([
            self::RESPONSE_STATUS     => true,
            self::RESPONSE_STATISTICS => $combinedData,
        ]);
    }

    /**
     * @param ProductStatisticsService $statisticsService
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getLocationStatistics(ProductStatisticsService $statisticsService): JsonResponse
    {
        $company          = $this->getCompany();
        $serviceProduct   = $this->getServiceProduct();

        $validated = $this->request->validate([
            self::REQUEST_COUNTY_LOCATION_ID => ['numeric', 'nullable'],
            self::REQUEST_STATE_LOCATION_ID  => ['numeric', Rule::requiredIf(fn() => $this->request->get(self::REQUEST_COUNTY_LOCATION_ID) === null)]
        ]);
        $stateLocationId  = $validated[self::REQUEST_STATE_LOCATION_ID] ?? null;
        $countyLocationId = $validated[self::REQUEST_COUNTY_LOCATION_ID] ?? null;

        $stats = $statisticsService->getBiddingStatisticsByLocation(
            $company->id,
            $serviceProduct->id,
            $stateLocationId,
            $countyLocationId
        );

        return $this->formatResponse([
            self::RESPONSE_STATUS     => true,
            self::RESPONSE_STATISTICS => $stats,
        ]);
    }

    /**
     * @param ProductStatisticsService $statisticsService
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getPurchasedBreakdown(ProductStatisticsService $statisticsService): JsonResponse
    {
        $company = $this->getCompany();
        $serviceProduct = $this->getServiceProduct();

        $productTypeBreakdown = $statisticsService->getCompanyPurchasedTypeStatistics(
            companyId: $company->id,
            serviceProductId: $serviceProduct->id,
        );

        return $this->formatResponse([
            self::RESPONSE_STATUS     => true,
            self::RESPONSE_STATISTICS => $productTypeBreakdown,
        ]);
    }

    /**
     * @return Carbon
     */
    protected function getStartDate(): Carbon
    {
        $timezone = $this->getTimezone();

        $startDate = $this->request->whenHas(
            self::REQUEST_START_TIMESTAMP,
            fn ($param) => Carbon::createFromTimestamp($param, $timezone)->startOfDay(),
            fn () => Carbon::now($timezone)->subDays(30)->startOfDay()
        );

        return $startDate->timezone('UTC');
    }

    function getTimezone(): CarbonTimeZone {
        $offset = (int) $this->getCompany()?->primaryLocation?->address?->usZipCode?->utc ?? 0;

        return CarbonTimeZone::create($offset);
    }
}
