<?php

namespace App\Http\Controllers\DashboardAPI\V4;

use App\Enums\Campaigns\CampaignType;
use App\Enums\LoweredFloorPricePolicy;
use App\Http\Controllers\DashboardAPI\BaseDashboardApiController;
use App\Http\Requests\CompanyCampaigns\StoreCustomCampaignFloorPricingRequest;
use App\Http\Requests\Dashboard\DashboardPriceRequest;
use App\Jobs\CalculateCompanyCampaignLowBidFlagJob;
use App\Jobs\UpdateCampaignFloorPriceJob;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Legacy\Location;
use App\Models\Odin\IndustryService;
use App\Models\Odin\PropertyType;
use App\Models\Odin\ServiceProduct;
use App\Models\User;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\Campaigns\CompanyCampaignRepository;
use App\Services\Campaigns\CampaignPricingService;
use App\Services\Campaigns\CompanyCampaignService;
use App\Services\Campaigns\CustomCampaignFloorPricingService;
use App\Services\Campaigns\LoweredCustomFloorPricePolicyService;
use App\Services\Dashboard\DashboardAuthService;
use App\Services\Dashboard\JWT\DashboardJWTService;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Enums\Odin\PropertyType as PropertyTypeEnum;
use App\Enums\Odin\Product as ProductEnum;

class CampaignPriceController extends BaseDashboardApiController
{
    const string ROUTE_CAMPAIGN_REFERENCE    = 'campaignReference';

    const string REQUEST_STATE_LOCATION_KEY   = 'state_location_key';
    const string REQUEST_COUNTY_LOCATION_KEY  = 'county_location_key';
    const string REQUEST_ZIP_CODES            = 'zip_codes';
    const string REQUEST_PROPERTY_TYPES       = 'property_types';
    const string REQUEST_PRODUCT              = 'product_name';

    const string RESPONSE_ACTIVE_BID_COUNTIES = 'active_bid_counties';
    const string RESPONSE_STATUS              = 'status';
    const string RESPONSE_PRICES              = 'prices';
    const string RESPONSE_MESSAGE             = 'message';

    public function __construct(
        Request $request,
        DashboardAuthService $authService,
        DashboardJWTService $jwtService,
        private readonly CompanyCampaignRepository $companyCampaignRepository,
        private readonly LocationRepository $locationRepository,
        private readonly CampaignPricingService $campaignPricingService,
    )
    {
        parent::__construct($request, $authService, $jwtService);
    }

    /**
     * @param DashboardPriceRequest $request
     * @return JsonResponse
     */
    public function getStatePrices(
        DashboardPriceRequest $request
    ): JsonResponse
    {
        $data = $request->safe()->only([
            DashboardPriceRequest::STATE_ABBR,
        ]);

        $campaignUuid = $request->route(self::ROUTE_CAMPAIGN_REFERENCE, null);
        $campaign = $this->companyCampaignRepository->findOrFailByReference($campaignUuid);
        $serviceProductId = $this->companyCampaignRepository->getServiceProductFromCampaign($campaign)->{ServiceProduct::FIELD_ID};
        $stateLocationId = $this->locationRepository->getStateByStateAbbr($data[DashboardPriceRequest::STATE_ABBR])->{Location::ID};

        [$prices, $activeBidCounties] = $this->campaignPricingService->getStatePricing($serviceProductId, $stateLocationId, $campaign, $campaign);

        return $this->formatResponse([
            self::RESPONSE_STATUS              => !!$prices,
            self::RESPONSE_PRICES              => $prices,
            self::RESPONSE_ACTIVE_BID_COUNTIES => $activeBidCounties
        ]);
    }

    /**
     * @param DashboardPriceRequest $request
     * @return JsonResponse
     */
    public function getCountyPrices(
        DashboardPriceRequest $request
    ): JsonResponse
    {
        $campaignUuid = $request->route(self::ROUTE_CAMPAIGN_REFERENCE, null);
        $campaign = $this->companyCampaignRepository->findOrFailByReference($campaignUuid);
        $serviceProductId = $this->companyCampaignRepository->getServiceProductFromCampaign($campaign)->{ServiceProduct::FIELD_ID};
        $stateLocation = $this->locationRepository->getStateByStateAbbr($request->get(DashboardPriceRequest::STATE_ABBR));
        $countyLocationId = $this->locationRepository->getCounty($stateLocation->{Location::STATE_KEY}, $request->get(DashboardPriceRequest::COUNTY_KEY))->{Location::ID};

        $prices = $this->campaignPricingService->getCountyPricing($serviceProductId, $stateLocation->id, $countyLocationId, $campaign, $campaign->zip_code_targeted);

        return $this->formatResponse([
            self::RESPONSE_STATUS => !!$prices,
            self::RESPONSE_PRICES => $prices,
        ]);
    }

    /**
     * @param DashboardPriceRequest $request
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getStateFloorPrices(DashboardPriceRequest $request): JsonResponse
    {
        $serviceProductId = $this->getServiceProduct()?->id;

        $stateLocationKey = $request->get(self::REQUEST_STATE_LOCATION_KEY);
        $stateLocationId = $this->locationRepository->getStateByStateAbbr($stateLocationKey)?->id;
        $campaignType = $request->get(CompanyCampaign::FIELD_TYPE);
        $campaign = $campaignType !== null
            ? new CompanyCampaign([CompanyCampaign::FIELD_TYPE => CampaignType::tryFrom($campaignType)])
            : null;

        [$prices, $activeBidCounties] = $this->campaignPricingService->getStatePricing($serviceProductId, $stateLocationId, $campaign);

        return $this->formatResponse([
            self::RESPONSE_STATUS => !!$prices,
            self::RESPONSE_PRICES => $prices,
            self::RESPONSE_ACTIVE_BID_COUNTIES => $activeBidCounties
        ]);
    }

    /**
     * @param DashboardPriceRequest $request
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getCountyFloorPrices(DashboardPriceRequest $request): JsonResponse
    {
        $serviceProductId = $this->getServiceProduct()?->id;

        $stateLocationKey = $request->get(self::REQUEST_STATE_LOCATION_KEY);
        $countyLocationKey = $request->get(self::REQUEST_COUNTY_LOCATION_KEY);

        $stateLocation = $this->locationRepository->getStateByStateAbbr($stateLocationKey);
        $stateLocationId = $stateLocation?->id;
        $countyLocationId = $this->locationRepository->getCounty($stateLocation->state_key, $countyLocationKey)?->id;
        $campaignType = $request->get(CompanyCampaign::FIELD_TYPE);
        $campaign = $campaignType !== null
            ? new CompanyCampaign([CompanyCampaign::FIELD_TYPE => CampaignType::tryFrom($campaignType)])
            : null;

        $prices = $this->campaignPricingService->getCountyPricing($serviceProductId, $stateLocationId, $countyLocationId, $campaign);

        return $this->formatResponse([
            self::RESPONSE_STATUS => !!$prices,
            self::RESPONSE_PRICES => $prices,
        ]);
    }

    /**
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getPriceRangeForZipCodes(): JsonResponse
    {
        $zipCodes = $this->request->get(self::REQUEST_ZIP_CODES, []);
        $propertyTypes = $this->request->get(self::REQUEST_PROPERTY_TYPES) ?? [PropertyTypeEnum::RESIDENTIAL->value];

        $serviceProductId = $this->getServiceProduct()
            ?->id;
        $propertyTypeIds = PropertyType::query()
            ->whereIn(PropertyType::FIELD_NAME, $propertyTypes)
            ->pluck(PropertyType::FIELD_ID)
            ->toArray();
        $product = $this->getProduct();
        $groupByQualityTier = $product->name === ProductEnum::APPOINTMENT->value;

        $prices = $this->campaignPricingService->getPriceRangeForZipCodes($serviceProductId, $propertyTypeIds, $zipCodes, $groupByQualityTier);

        return $this->formatResponse([
            self::RESPONSE_STATUS   => !!$prices,
            self::RESPONSE_PRICES   => $prices
        ]);
    }

    /**
     * @param DashboardPriceRequest $request
     * @param CustomCampaignFloorPricingService $customCampaignFloorPricingService
     * @return JsonResponse
     */
    public function getCustomStateFloorPrices(DashboardPriceRequest $request, CustomCampaignFloorPricingService $customCampaignFloorPricingService): JsonResponse
    {
        $data = $request->safe()->only([DashboardPriceRequest::STATE_ABBR]);

        $campaignUuid = $request->route(self::ROUTE_CAMPAIGN_REFERENCE, null);
        $campaign = $this->companyCampaignRepository->findOrFailByReference($campaignUuid);
        $stateLocationId = $this->locationRepository->getStateByStateAbbr($data[DashboardPriceRequest::STATE_ABBR])->{Location::ID};

        [$prices, $activeCounties] = $customCampaignFloorPricingService->getCustomStateFloorPricing($campaign, $stateLocationId);

        return $this->formatResponse([
            self::RESPONSE_STATUS              => !!$prices,
            self::RESPONSE_PRICES              => $prices,
            self::RESPONSE_ACTIVE_BID_COUNTIES => $activeCounties
        ]);
    }

    /**
     * @param DashboardPriceRequest $request
     * @param CustomCampaignFloorPricingService $customCampaignFloorPricingService
     * @return JsonResponse
     */
    public function getCustomCountyFloorPrices(DashboardPriceRequest $request, CustomCampaignFloorPricingService $customCampaignFloorPricingService): JsonResponse
    {
        $data = $request->safe()->only([
            DashboardPriceRequest::STATE_ABBR,
            DashboardPriceRequest::COUNTY_KEY,
        ]);

        $campaignUuid = $request->route(self::ROUTE_CAMPAIGN_REFERENCE, null);
        $campaign = $this->companyCampaignRepository->findOrFailByReference($campaignUuid);
        $stateLocation = $this->locationRepository->getStateByStateAbbr($data[DashboardPriceRequest::STATE_ABBR]);
        $countyLocationId = $this->locationRepository->getCounty($stateLocation->state_key, $data[DashboardPriceRequest::COUNTY_KEY])?->id;

        $prices = $customCampaignFloorPricingService->getCustomCountyFloorPricing($campaign, $stateLocation->id, $countyLocationId);

        return $this->formatResponse([
            self::RESPONSE_STATUS => !!$prices,
            self::RESPONSE_PRICES => $prices
        ]);
    }

    /**
     * @param StoreCustomCampaignFloorPricingRequest $request
     * @param CustomCampaignFloorPricingService $customCampaignFloorPricingService
     * @param CompanyCampaignService $companyCampaignService
     * @return JsonResponse
     * @throws Exception
     */
    public function saveCustomFloorPrices(StoreCustomCampaignFloorPricingRequest $request, CustomCampaignFloorPricingService $customCampaignFloorPricingService): JsonResponse
    {
        $payload = $request->safe()->toArray();
        $campaign = $this->companyCampaignRepository->findOrFailByReference(
            $request->route(self::ROUTE_CAMPAIGN_REFERENCE)
        );

        $campaignsToUpdate = ($payload[StoreCustomCampaignFloorPricingRequest::REQUEST_BULK_APPLY_CHANGES] ?? false)
            ? $campaign->company->futureCampaigns()
                ->whereHas(CompanyCampaign::RELATION_SERVICE, fn(Builder $query) =>
                    $query->where(IndustryService::FIELD_INDUSTRY_ID, $campaign->service->industry_id)
                )->get()
            : collect([$campaign]);

        $loweredPricePolicy = LoweredFloorPricePolicy::tryFrom($payload[StoreCustomCampaignFloorPricingRequest::REQUEST_LOWERED_PRICE_POLICY] ?? -1);

        UpdateCampaignFloorPriceJob::dispatch(
            campaigns: $campaignsToUpdate,
            updatedPrices: $payload[StoreCustomCampaignFloorPricingRequest::REQUEST_PRICES] ?? [],
            useCustomFloorPrices: $payload[CompanyCampaign::FIELD_USES_CUSTOM_FLOOR_PRICES],
            loweredFloorPricePolicy: $loweredPricePolicy,
            notifyEmail: auth()->user()->{User::FIELD_EMAIL}
        );

        return $this->formatResponse([
            self::RESPONSE_STATUS  => true,
            self::RESPONSE_MESSAGE => null,
        ]);
    }
}
