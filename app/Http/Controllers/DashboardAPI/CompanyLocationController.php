<?php

namespace App\Http\Controllers\DashboardAPI;

use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Http\Controllers\API\CompaniesController;
use App\Http\Requests\Odin\StoreCompanyLocationRequest;
use App\Http\Resources\Dashboard\CompanyLocationResource;
use App\Models\Odin\CompanyLocation;
use App\Repositories\Odin\CompanyLocationRepository;
use App\Services\CompanyLocationService;
use App\Services\Dashboard\DashboardAuthService;
use App\Services\Dashboard\JWT\DashboardJWTService;
use App\Services\Workflows\WorkflowEventService;
use App\Workflows\Shortcodes\EventValueNameShortcode;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Client\RequestException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CompanyLocationController extends BaseDashboardApiController
{
    const RESPONSE_STATUS = 'status';
    const RESPONSE_LOCATION = 'location';

    public function __construct(
        Request                             $request,
        DashboardAuthService                $authService,
        DashboardJWTService                 $jwtService,
        protected CompanyLocationRepository $companyLocationRepository,
        protected CompanyLocationService    $companyLocationService,
        protected WorkflowEventService      $workflowEventService,
    )
    {
        parent::__construct($request, $authService, $jwtService);
    }

    /**
     * @return JsonResponse
     * @throws ModelNotFoundException
     */
    public function getCompanyLocations(): JsonResponse
    {
        $company = $this->getCompany();
        if($company === null)
            throw new ModelNotFoundException();

        return $this->formatResponse([
            self::RESPONSE_STATUS   => true,
            'locations'             => CompanyLocationResource::collection($company->locations)->toArray($this->request),
        ]);
    }

    /**
     * @param StoreCompanyLocationRequest $request
     * @return JsonResponse
     * @throws RequestException
     */
    public function createCompanyLocation(StoreCompanyLocationRequest $request): JsonResponse
    {
        $company = $this->getCompany();
        if($company === null)
            throw new ModelNotFoundException();

        $newLocation = $this->companyLocationService->createCompanyLocation($company, $request);

        /** Trigger workflow event for company address creation here */
        $this->triggerWorkflowEvent(
            $this->workflowEventService,
            EventCategory::COMPANIES,
            EventName::PROFILE_ADDRESS_CREATED,
            [
                EventValueNameShortcode::VALUE_NAME_KEY => $newLocation[CompanyLocation::FIELD_NAME]
            ]
        );

        return $this->formatResponse([
            self::RESPONSE_STATUS   => !!$newLocation,
            self::RESPONSE_LOCATION => new CompanyLocationResource($newLocation),
        ]);
    }

    /**
     * @param int $companyId
     * @param int $locationId
     * @param StoreCompanyLocationRequest $request
     * @return JsonResponse
     * @throws RequestException
     */
    public function updateCompanyLocation(int $companyId, int $locationId, StoreCompanyLocationRequest $request): JsonResponse
    {
        $company = $this->getCompany();
        if($company === null)
            throw new ModelNotFoundException();

        $companyLocation = $this->companyLocationRepository->findOrFail($locationId);
        $oldAddressArray = array_merge($companyLocation->toArray(), $companyLocation->address?->toArray() ?? []);

        $updatedLocation = $this->companyLocationService->updateCompanyLocation($company, $companyLocation, $request);

        if (!!$updatedLocation) {
            $newAddressArray = array_merge($updatedLocation->toArray(), $updatedLocation->address?->toArray() ?? []);
            $updates = array_diff($newAddressArray, $oldAddressArray);
            unset($updates[Model::UPDATED_AT]);

            /** Trigger workflow event for company address update here */
            $this->triggerWorkflowEvent(
                $this->workflowEventService,
                EventCategory::COMPANIES,
                EventName::PROFILE_ADDRESS_UPDATED,
                [
                    EventValueNameShortcode::VALUE_NAME_KEY => $updatedLocation[CompanyLocation::FIELD_NAME],
                    EventName::PROFILE_ADDRESS_UPDATED->getOldValueKey() => $this->buildDiffString(array_intersect_key($oldAddressArray, $updates)),
                    EventName::PROFILE_ADDRESS_UPDATED->getNewValueKey() => $this->buildDiffString($updates)
                ]
            );
        }

        return $this->formatResponse([
            self::RESPONSE_STATUS   => !!$updatedLocation,
            self::RESPONSE_LOCATION => new CompanyLocationResource($updatedLocation),
        ]);
    }

    /**
     * @param int $companyId
     * @param int $locationId
     * @return JsonResponse
     */
    public function deleteCompanyLocation(int $companyId, int $locationId): JsonResponse
    {
        $company = $this->getCompany();
        if($company === null)
            throw new ModelNotFoundException();

        $oldLocation = $this->companyLocationRepository->findOrFail($locationId);

        $success = $this->companyLocationService->deleteCompanyLocation($companyId, $locationId);

        if ($success) {
            /** Trigger workflow event for company address delete here */
            $this->triggerWorkflowEvent(
                $this->workflowEventService,
                EventCategory::COMPANIES,
                EventName::PROFILE_ADDRESS_DELETED,
                [
                    EventValueNameShortcode::VALUE_NAME_KEY => $oldLocation[CompanyLocation::FIELD_NAME]
                ]
            );
        }

        return $this->formatResponse([
            self::RESPONSE_STATUS => $success,
        ]);
    }

    /**
     * @param int $companyId
     * @param int $locationId
     * @return JsonResponse
     */
    public function makeCompanyLocationPrimary(int $companyId, int $locationId): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_STATUS => $this->companyLocationService->makeCompanyLocationPrimary($companyId, $locationId),
        ]);
    }
}
