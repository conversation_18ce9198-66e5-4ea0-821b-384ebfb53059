<?php

namespace App\Http\Controllers\DashboardAPI;

use App\Http\Requests\ConsumerReviews\CompanyConsumerReviewsRequest;
use App\Http\Requests\ConsumerReviews\StoreReviewReplyRequest;
use App\Http\Resources\Reviews\ReviewReplyResource;
use App\Http\Resources\Reviews\ReviewResource;
use App\Models\ConsumerReviews\CompanyRating;
use App\Models\ConsumerReviews\Review;
use App\Repositories\CompanyConsumerReviewRepository\CompanyConsumerReviewRepository;
use App\Repositories\CompanyRatingRepository;
use App\Services\CompanyConsumerReviews\CompanyConsumerReviewService;
use App\Transformers\CompanyConsumerReviewTransformer;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Validation\UnauthorizedException;

class ConsumerReviewsDashboardController extends BaseDashboardApiController
{
    const string REQUEST_LIMIT              = 'limit';
    const string REQUEST_OFFSET             = 'offset';
    const string RESPONSE_REVIEWS           = 'reviews';
    const string RESPONSE_STATUS            = 'status';
    const string RESPONSE_REVIEW_LINK       = 'review_link';
    const string RESPONSE_REVIEWS_PAGINATED = 'reviews_paginated';
    const string RESPONSE_RATING            = 'rating';
    const string RESPONSE_REVIEW_COUNT      = 'review_count';
    const string RESPONSE_REVIEW_REPLIES    = 'review_replies';

    /**
     * @param CompanyConsumerReviewsRequest $request
     * @param CompanyConsumerReviewService $companyConsumerReviewService
     * @param CompanyConsumerReviewTransformer $transformer
     * @return JsonResponse
     */
    public function getAllConsumerReviews(
        CompanyConsumerReviewsRequest $request,
        CompanyConsumerReviewService $companyConsumerReviewService,
        CompanyConsumerReviewTransformer $transformer,
    ): JsonResponse
    {
        $company = $this->getCompany();

        $filterData = $request->safe()->collect();
        $reviews =  $companyConsumerReviewService->getCompanyConsumerReviewsPaginated($filterData, $company->id);

        return $this->formatResponse([
            self::RESPONSE_STATUS                => true,
            self::RESPONSE_REVIEWS_PAGINATED     => ReviewResource::collection($reviews)->resource,
        ]);
    }

    /**
     * @param CompanyConsumerReviewRepository $companyConsumerReviewRepository
     * @param CompanyConsumerReviewTransformer $transformer
     * @return JsonResponse
     */
    public function getRecentConsumerReviews(
        CompanyConsumerReviewRepository $companyConsumerReviewRepository,
        CompanyConsumerReviewTransformer $transformer,
    ): JsonResponse
    {
        $company = $this->getCompany();

        $limit = $this->request->get(self::REQUEST_LIMIT);
        $offset = $this->request->get(self::REQUEST_OFFSET);

        $reviews = $transformer->transformReviewsForDashboard(
            $companyConsumerReviewRepository->getRecentApprovedConsumerReviews($company->id, $offset, $limit)
        );

        $domain = config('app.fixr_domain');
        $companySlug = Str::slug($company->name, "-");
        $reviewLink = "{$domain}/add-review/{$companySlug}/{$company->reference}";

        return $this->formatResponse([
            self::RESPONSE_STATUS       => (bool) $reviews,
            self::RESPONSE_REVIEWS      => $reviews,
            self::RESPONSE_REVIEW_LINK  => $reviewLink,
        ]);
    }

    /**
     * @param CompanyRatingRepository $companyRatingRepository
     * @return JsonResponse
     */
    public function getConsumerReviewRating(
        CompanyRatingRepository $companyRatingRepository
    ): JsonResponse
    {
        $company = $this->getCompany();

        $rating = $companyRatingRepository->getLatestCompanyRating($company->id);

        $reviewCount = 0;
        if (!is_null($rating)) {
            $reviewCount = $rating->{CompanyRating::FIELD_NUM_REVIEWS};
        }

        return $this->formatResponse([
            self::RESPONSE_RATING        => $rating,
            self::RESPONSE_REVIEW_COUNT  => $reviewCount,
        ]);
    }

    /**
     * @param int $companyId
     * @param Review $review
     * @param StoreReviewReplyRequest $request
     * @param CompanyConsumerReviewTransformer $transformer
     * @param CompanyConsumerReviewRepository $companyConsumerReviewRepository
     * @return JsonResponse
     */
    public function postConsumerReviewReply(
        int $companyId,
        Review $review,
        StoreReviewReplyRequest $request,
        CompanyConsumerReviewRepository $companyConsumerReviewRepository,
    ): JsonResponse
    {
        if ($this->getUser()->company_id !== $companyId)
            throw new UnauthorizedException("You do not belong to this company.");

        $validated = $request->validated();
        $reply = Arr::get($validated, StoreReviewReplyRequest::FIELD_REPLY);

        $reviewReply = $companyConsumerReviewRepository->postCompanyUserComment(
            $review,
            $reply,
            $this->getUser()->id,
        );
        $replies = ReviewReplyResource::collection($review->reviewReplies);
        return $this->formatResponse([
            self::RESPONSE_STATUS            => (bool) $reviewReply,
            self::RESPONSE_REVIEW_REPLIES    => $replies,
        ]);
    }
}
