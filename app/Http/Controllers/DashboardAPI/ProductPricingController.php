<?php

namespace App\Http\Controllers\DashboardAPI;

use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Product;
use App\Models\Odin\ServiceProduct;
use App\Repositories\Legacy\DatabaseLocationRepository;
use App\Services\Odin\Pricing\PricingService;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\JsonResponse;

class ProductPricingController extends BaseDashboardApiController
{
    const REQUEST_CAMPAIGN_UUID         = 'uuid';
    const REQUEST_PRODUCT_NAME          = 'product_name';
    const REQUEST_INDUSTRY_SERVICE_ID   = 'industry_service_id';
    const REQUEST_ZIPCODES              = 'zip_codes';

    const RESPONSE_STATUS               = 'status';
    const RESPONSE_PRICES               = 'prices';

    /**
     * @param PricingService $pricingService
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getPriceRangeForCampaign(PricingService $pricingService): JsonResponse
    {
        $uuid = $this->request->get(self::REQUEST_CAMPAIGN_UUID) ?? null;

        /** @var LeadCampaign $campaign */
        $campaign = LeadCampaign::query()
            ->where(LeadCampaign::UUID, $uuid)
            ->firstOrFail();

        $serviceProduct = $this->getServiceProduct();

        $prices = $pricingService->getPriceRangeForCampaign($serviceProduct, $campaign);

        return $this->formatResponse([
            self::RESPONSE_STATUS   => !!$prices,
            self::RESPONSE_PRICES   => $prices
        ]);
    }

    /**
     * @param PricingService $pricingService
     * @param DatabaseLocationRepository $locationRepository
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getPriceRangeForZipCodes(PricingService $pricingService, DatabaseLocationRepository $locationRepository): JsonResponse
    {
        $zips = collect($this->request->get(self::REQUEST_ZIPCODES) ?? []);

        $stateIds = collect($locationRepository->getStateByZipCodes($zips, true));
        $countyIds = collect($locationRepository->getCountiesByZipCodesExt($zips));

        $serviceProduct = $this->getServiceProduct();

        $prices = $pricingService->getPriceRangeForLocations($serviceProduct, $countyIds, $stateIds);

        return $this->formatResponse([
            self::RESPONSE_STATUS   => !!$prices,
            self::RESPONSE_PRICES   => $prices
        ]);
    }

    /**
     * @return ServiceProduct
     * @throws BindingResolutionException
     */
    protected function getServiceProduct(): ServiceProduct
    {
        $industryService = $this->getService();
        $productName = $this->request->get(self::REQUEST_PRODUCT_NAME);
        /** @var Product $product */
        $product = Product::query()->where(Product::FIELD_NAME, $productName)
            ->firstOrFail();

        /** @var ServiceProduct */
        return ServiceProduct::query()
            ->where(ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $industryService->id)
            ->where(ServiceProduct::FIELD_PRODUCT_ID, $product->id)
            ->firstOrFail();
    }

}
