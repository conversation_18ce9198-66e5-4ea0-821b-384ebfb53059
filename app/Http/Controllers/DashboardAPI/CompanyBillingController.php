<?php

namespace App\Http\Controllers\DashboardAPI;

use App\Builders\EloquentInvoiceBuilder;
use App\Campaigns\Modules\Legacy\LegacyModule;
use App\Contracts\Legacy\Payments\CreditCardPaymentMethodContract;
use App\Http\Requests\Dashboard\SearchInvoicesRequest;
use App\Http\Resources\Dashboard\InvoiceResource;
use App\Http\Resources\Dashboard\TransactionResource;
use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Company;
use App\Repositories\Legacy\InvoicesRepository;
use App\Services\Legacy\APIConsumer;
use App\Services\Legacy\Payments\CompanyBillingService;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Contracts\Container\BindingResolutionException;

class CompanyBillingController extends BaseDashboardApiController
{
    const FIELD_STRIPE_TOKEN = "stripe_token";
    const FIELD_PAYMENT_TYPE = "payment_type";
    const string UNPAID_INVOICE_ERROR_MESSAGE   = "You have unpaid invoices.";
    const string UNINVOICED_ERROR_MESSAGE       = "You have un-invoiced leads.";
    const string PAYMENT_METHOD_ERROR_MESSAGE   = "You cannot delete the only payment method on file.";

    /**
     * Returns the payment methods for a given company.
     *
     * @return JsonResponse
     */
    public function getPaymentMethods(): JsonResponse
    {
        $company = $this->getCompany();

        if ($company == null)
            throw new ModelNotFoundException();

        $methods = $company->paymentMethods();
        $methods = $methods?->exists() ? $methods->preferred() : null;

        if (!$methods) {
            return $this->formatResponse([
                "status"  => true,
                "methods" => []
            ]);
        }

        return $this->formatResponse([
            "status"  => true,
            "methods" => $this->formatPaymentMethods(collect($methods))
        ]);
    }

    public function addPaymentMethod(CompanyBillingService $service): JsonResponse
    {
        $company = $this->getCompany();

        if ($company == null)
            throw new ModelNotFoundException();

        $token = $this->request->get(self::FIELD_STRIPE_TOKEN);
        $type = $this->request->get(self::FIELD_PAYMENT_TYPE);

        if (!$token || !$type || strlen($token) <= 0 || strlen($type) <= 0)
            throw new ModelNotFoundException();

        $success = $service->addPaymentMethod(
            $company,
            $this->getUser()->email,
            $token,
            $type
        );

        return $this->formatResponse([
            "status"  => $success["success"],
            "message" => $success["message"]
        ]);
    }

    /**
     * @param CompanyBillingService $service
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function deletePaymentMethod(CompanyBillingService $service): JsonResponse
    {
        $company = $this->getCompany();

        if ($company == null)
            throw new ModelNotFoundException();

        $invoicesRepository = app()->make(InvoicesRepository::class);
        $invoiceSummary = $invoicesRepository->getCompanyInvoicesSummary($company->id);
        $paymentMethodsCount = $service->getPaymentMethodsCount($company);

        $validationMessages = [];

        if ($invoiceSummary['unpaid'] > 0) {
            $validationMessages[] = self::UNPAID_INVOICE_ERROR_MESSAGE;
        }

        if ($invoiceSummary['tobeinvoiced'] > 0) {
            $validationMessages[] = self::UNINVOICED_ERROR_MESSAGE;
        }

        if ($paymentMethodsCount <= 1) {
            $validationMessages[] = self::PAYMENT_METHOD_ERROR_MESSAGE;
        }

        if (!empty($validationMessages)) {
            return $this->formatResponse([
                'status' => false,
                'errors' => $validationMessages
            ]);
        }

        return $this->formatResponse([
            "status"  => $service->deletePaymentMethod($company, $this->request->route('cardId', null))
        ]);
    }

    public function makePaymentMethodPrimary(CompanyBillingService $service): JsonResponse
    {
        $company = $this->getCompany();

        if ($company == null)
            throw new ModelNotFoundException();

        return $this->formatResponse([
            "status"  => $service->makePaymentMethodPrimary($company, $this->request->route('cardId', null))
        ]);
    }

    public function searchInvoices(SearchInvoicesRequest $request): JsonResponse
    {
        $company = $this->getCompany();
        if ($company == null)
            throw new ModelNotFoundException();

        $fetchConfig = $request->get('with_config') == 1
            ? $this->getBillingConfig($company)
            : [];

        $searchOptions = $request->safe();

        $invoices = EloquentInvoiceBuilder::query()
            ->forLegacyCompanyId($company->legacy_id)
            ->forStatus($searchOptions['status'] ?? null)
            ->forFromDate($searchOptions['date_start'] ?? null)
            ->forToDate($searchOptions['date_end'] ?? null)
            ->forInvoiceNumber($searchOptions['invoice_id'] ?? null)
            ->paginate(10);

        return $this->formatResponse([
            'status'    => true,
            'invoices'  => InvoiceResource::collection($invoices),
            'total'     => $invoices->total(),
            'limit'     => $invoices->perPage(),
            'offset'    => ($invoices->currentPage() - 1) * $invoices->perPage(),
            'config_data'   =>  $fetchConfig
        ]);
    }

    /**
     * @param APIConsumer $integrationApi
     * @return JsonResponse
     * @throws \Exception
     */
    public function downloadInvoice(APIConsumer $integrationApi): JsonResponse
    {
        $company = $this->getCompany();
        if ($company == null)
            throw new ModelNotFoundException();

        $invoiceId = $this->request->route('invoiceId');
        if (!($invoiceId > 0)) {
            throw new ModelNotFoundException();
        }

        $legacyResponse = $integrationApi->get(
            "/fixr-dashboard/$company->reference/invoices/download/$invoiceId"
        )->json()['data'] ?? [];

        return $this->formatResponse([
            'status'    => $legacyResponse['status'] ?? false,
            'pdf'       => $legacyResponse['pdf'] ?? '',
            'filename'  => $legacyResponse['filename'] ?? null,
            'message'   => $legacyResponse['status'] ? null : 'Invoice could not be found.'
        ]);
    }


    /**
     * @param APIConsumer $integrationApi
     * @return JsonResponse
     * @throws \Exception
     */
    public function payInvoiceNow(APIConsumer $integrationApi): JsonResponse
    {
        $company = $this->getCompany();
        if ($company == null)
            throw new ModelNotFoundException();

        $invoiceId = $this->request->route('invoiceId');
        if (!($invoiceId > 0)) {
            throw new ModelNotFoundException();
        }

        try {
            $legacyResponse = $integrationApi->post(
                "/invoice/$invoiceId/pay-now", ['companyid' => $company->{Company::FIELD_ID}]
            )->json()['data'] ?? [];
        } catch (Exception $exception) {
            logger()->error($exception->getMessage());

            return $this->formatResponse([
                'status'    => $legacyResponse['status'] ?? false,
                'message'   => 'An unknown error has occurred, please contact your account manager.'
            ]);
        }

        return $this->formatResponse([
            'status'    => $legacyResponse['status'] ?? false,
            'paid'       => $legacyResponse['paid'] ?? false,
            'message'   => $legacyResponse['message'] ?: 'Invoice payment has failed, please contact your account manager.'
        ]);
    }

    /**
     * @param APIConsumer $integrationApi
     * @return JsonResponse
     * @throws Exception
     */
    public function getTransactionsForInvoice(APIConsumer $integrationApi): JsonResponse
    {
        $company = $this->getCompany();
        if ($company == null)
            throw new ModelNotFoundException();

        $invoiceId = $this->request->route('invoiceId');
        if (!($invoiceId > 0)) {
            throw new ModelNotFoundException();
        }

        $legacyResponse = $integrationApi->get(
            "/fixr-dashboard/$company->reference/invoices/transactions/$invoiceId"
        )->json()['data'] ?? [];

        return $this->formatResponse([
            'status'        => $legacyResponse['status'] ?? false,
            'transactions'  => TransactionResource::collection($legacyResponse['transactions'] ?? []),
        ]);
    }

    protected function formatPaymentMethods(Collection $methods): array
    {
        return $methods->map(function (CreditCardPaymentMethodContract $paymentMethod) {
            return [
                'id'           => $paymentMethod->id(),
                'method'       => $paymentMethod->type(),
                'brand'        => $paymentMethod->brand(),
                'last4'        => $paymentMethod->maskedNumber(),
                'expiry'       => $paymentMethod->expiry(),
                'expiry_month' => $paymentMethod->expiryMonth(),
                'expiry_year'  => $paymentMethod->expiryYear(),
                'name'         => $paymentMethod->name(),
                'address_1'    => $paymentMethod->addressLineOne(),
                'address_2'    => $paymentMethod->addressLineTwo(),
                'city'         => $paymentMethod->city(),
                'state'        => $paymentMethod->state(),
                'zipcode'      => $paymentMethod->zipCode(),
                'country'      => $paymentMethod->country(),
                'status'       => null,
            ];
        })->toArray();
    }

    /**
     * @param Company $company
     * @return array
     * @throws BindingResolutionException
     */
    private function getBillingConfig(Company $company): array
    {
        $repository = app()->make(InvoicesRepository::class);
        $integrationApiConsumer = app()->make(APIConsumer::class);
        $companyTotals = $repository->getCompanyInvoicesSummary($company->id);

        $legacyResponse = $integrationApiConsumer->get("/fixr-dashboard/$company->reference/invoices/uninvoiced-items")
            ->json()['data'] ?? [];
        $companyTotals['campaigns'] = $this->removeGeneratedCampaignPrefix($legacyResponse['campaigns']);
        $companyTotals['next_invoice'] = $legacyResponse['timestamp_next_invoice'] ?? [];
        $companyTotals['outstanding_credit'] = $legacyResponse['outstanding_credit'] ?? [];

        return [
            'company_summary'       => $companyTotals,
        ];
    }

    /**
     * @param array $campaignSummary
     * @return array
     */
    private function removeGeneratedCampaignPrefix(array $campaignSummary): array
    {
        foreach ($campaignSummary as &$campaign) {
            $campaign[LeadCampaign::NAME] = str_replace(LegacyModule::LEAD_CAMPAIGN_PREFIX, "", $campaign[LeadCampaign::NAME]);
        }

        return $campaignSummary;
    }
}
