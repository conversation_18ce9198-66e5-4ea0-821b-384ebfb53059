<?php

namespace App\Http\Controllers\DashboardAPI;

use App\Http\Requests\Dashboard\StoreCompanyUserDashboardRequest;
use App\Http\Resources\Dashboard\CompanyUserResource;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Repositories\Odin\CompanyUserRepository;
use App\Services\CompanyUserService;
use App\Services\Dashboard\DashboardAuthService;
use App\Services\Dashboard\JWT\DashboardJWTService;
use App\Services\Legacy\APIConsumer;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\UnauthorizedException;
use Exception;

class CompanyUserController extends BaseDashboardApiController
{
    const RESPONSE_STATUS = 'status';
    const RESPONSE_USER = 'company_user';
    const RESPONSE_USERS = 'company_users';

    public function __construct(
        Request                             $request,
        DashboardAuthService                $authService,
        DashboardJWTService                 $jwtService,
        protected CompanyUserRepository     $companyUserRepository,
        protected CompanyUserService        $companyUserService,
        protected APIConsumer               $integrationApiConsumer
    )
    {
        parent::__construct($request, $authService, $jwtService);
    }

    /**
     * @return JsonResponse
     */
    public function getCompanyUsers(): JsonResponse
    {
        $company = $this->getCompany();
        if($company === null)
            throw new ModelNotFoundException();

        return $this->formatResponse([
            self::RESPONSE_STATUS   => true,
            self::RESPONSE_USERS    => CompanyUserResource::collection(
                $company->users->filter(fn(CompanyUser $companyUser) => $companyUser->import_source === CompanyUser::IMPORT_SOURCE_MANUAL)->values()
            )->toArray($this->request),
        ]);
    }

    /**
     * @param StoreCompanyUserDashboardRequest $request
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function createCompanyUser(StoreCompanyUserDashboardRequest $request): JsonResponse
    {
        $company = $this->getCompany();
        if($company === null)
            throw new ModelNotFoundException();
        $validated = $request->safe()->all();

        if ($validated[CompanyUser::FIELD_IS_CONTACT]) {
            return $this->createCompanyContact($company, $validated);
        }
        else {
            $validated[CompanyUser::FIELD_AUTHENTICATION_TYPE] = CompanyUser::AUTHENTICATION_TYPE_ADMIN2;

            $newUser = $this->companyUserRepository->createCompanyUser($company->{Company::FIELD_ID}, $validated);
            if ($validated[CompanyUser::FIELD_PASSWORD]) {
                $this->companyUserService->updatePassword($newUser, $validated[CompanyUser::FIELD_PASSWORD]);
            }

            return $this->formatResponse([
                self::RESPONSE_STATUS => !!$newUser,
                self::RESPONSE_USER => new CompanyUserResource($newUser)
            ]);
        }
    }

    /**
     * @param int $companyId
     * @param int $companyUserId
     * @param StoreCompanyUserDashboardRequest $request
     * @return JsonResponse
     */
    public function updateCompanyUser(int $companyId, int $companyUserId, StoreCompanyUserDashboardRequest $request): JsonResponse
    {
        $company = $this->getCompany();
        if($company === null)
            throw new ModelNotFoundException();
        $companyUser = $this->companyUserRepository->findCompanyUserByIdOrFail($companyUserId);
        if ($companyUser->{CompanyUser::FIELD_COMPANY_ID} !== $companyId) throw new UnauthorizedException();

        $validated = $request->safe()->all();

        if ($validated[CompanyUser::FIELD_IS_CONTACT]) {
            return $this->updateCompanyContact($company->{Company::FIELD_REFERENCE}, $companyUser, $validated);
        }
        else {
            if ($validated[CompanyUser::FIELD_PASSWORD]) {
                $this->companyUserService->updatePassword($companyUser, $validated[CompanyUser::FIELD_PASSWORD]);
            }
            $updatedUser = $this->companyUserRepository->updateCompanyUser($company->{Company::FIELD_REFERENCE}, $companyUser, $validated, true);

            return $this->formatResponse([
                self::RESPONSE_STATUS => !!$updatedUser,
                self::RESPONSE_USER => new CompanyUserResource($updatedUser),
            ]);
        }
    }

    /**
     * @param int $companyId
     * @param int $companyUserId
     * @return JsonResponse
     * @throws Exception
     */
    public function deleteCompanyUser(int $companyId, int $companyUserId): JsonResponse
    {
        $company = $this->getCompany();
        if($company === null)
            throw new ModelNotFoundException();
        $companyUser = $this->companyUserRepository->findCompanyUserByIdOrFail($companyUserId);
        $companyCheck = $companyId === $companyUser->{CompanyUser::FIELD_COMPANY_ID};

        $isContact = $companyUser->is_contact;
        $legacyId = $companyUser->legacy_id;
        $deleted = $companyCheck && $this->companyUserRepository->deleteCompanyUser($company->{Company::FIELD_REFERENCE}, $companyUser);

        // If deleting a contact, clean up their lead delivery methods
        if ($deleted && $isContact) {
            $legacyResponse = $this->integrationApiConsumer->delete(
                "fixr-dashboard/$company->reference/company-contact-all-deliveries/$legacyId"
            )->json()['data'] ?? [];
            if (!$legacyResponse['status']) {
                logger()->error("CompanyUser with legacy ID $legacyId was deleted, but there was an error cleaning up their legacy LeadDeliveryMethods.");
            }
        }

        return $this->formatResponse([
            self::RESPONSE_STATUS => !!$deleted,
        ]);

    }

    /**
     * @param string $companyReference
     * @param CompanyUser $companyUser
     * @param array $validatedData
     * @return JsonResponse
     */
    private function updateCompanyContact(string $companyReference, CompanyUser $companyUser, array $validatedData): JsonResponse
    {
        $updatedUser = $this->companyUserRepository->updateCompanyContact($companyReference, $companyUser, $validatedData, true);

        return $this->formatResponse([
            self::RESPONSE_STATUS   => !!$updatedUser,
            self::RESPONSE_USER     => $updatedUser,
        ]);
    }

    /**
     * @param Company $company
     * @param array $validatedData
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    private function createCompanyContact(Company $company, array $validatedData): JsonResponse
    {
        $success = true;
        $message = null;
        $synchronous = $this->request->get('save_synchronously');

        if ($synchronous) {
            $newUser = $this->companyUserService->createCompanyContactSynchronously($company, $validatedData);
            $success = $newUser?->legacy_id > 0;
            if (!$success) $message = "Legacy service could not be reached for User creation.";
        }
        else {
            $result = $this->companyUserRepository->createCompanyContact($company, $validatedData, true);
            $newUser = $result['contact'];
            $success = $result['legacyStatus'];
        }

        return $this->formatResponse([
            self::RESPONSE_STATUS   => $newUser && $success,
            self::RESPONSE_USER     => new CompanyUserResource($newUser),
            'message'               => $message
        ]);
    }

}
