<?php

namespace App\Http\Controllers\DashboardAPI;

use App\Enums\Odin\Grouping;
use App\Http\Requests\Dashboard\BulkUpdateCampaignsRequest;
use App\Http\Requests\Dashboard\StoreCampaignRequest;
use App\Http\Requests\Dashboard\StoreCrmDeliveryRequest;
use App\Http\Resources\Dashboard\CampaignDetailResource;
use App\Http\Resources\Dashboard\CampaignSummaryResource;
use App\Http\Resources\Dashboard\ContactDeliveryResource;
use App\Http\Resources\Dashboard\CrmDeliveryResource;
use App\Http\Resources\Dashboard\CrmProviderResource;
use App\Http\Resources\Dashboard\LegacyTransformers\CrmDeliveryTransformer;
use App\Http\Resources\Dashboard\LegacyTransformers\LeadCampaignTransformer;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\Product;
use App\Enums\Odin\Product as ProductEnum;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\Company;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductCampaignBudget;
use App\Models\Odin\PropertyType;
use App\Models\SaleType;
use App\Repositories\LeadCampaignDeliveryMethodRepository;
use App\Repositories\Odin\CompanyRepository;
use App\Repositories\Odin\ProductCampaignRepository;
use App\Services\Dashboard\DashboardAuthService;
use App\Services\Dashboard\JWT\DashboardJWTService;
use App\Services\DatabaseHelperService;
use App\Services\Odin\Appointments\AppointmentCalendarIntegrationService;
use App\Services\Legacy\APIConsumer;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Validation\UnauthorizedException;
use App\Models\Legacy\LeadCampaignPauseReasonsList;
use Exception;

class CampaignController extends BaseDashboardApiController
{
    const RESPONSE_STATUS             = 'status';
    const RESPONSE_CAMPAIGNS          = 'campaigns';
    const RESPONSE_CAMPAIGN           = 'campaign';
    const RESPONSE_CONFIG_DATA        = 'config_data';
    const RESPONSE_CONTACT_DELIVERIES = 'contact_deliveries';
    const RESPONSE_CRM_DELIVERIES     = 'crm_deliveries';
    const RESPONSE_CRM_DELIVERY       = 'crm_delivery';
    const RESPONSE_MESSAGE            = 'message';
    const RESPONSE_PRODUCT            = 'product';
    const RESPONSE_SCHEDULES          = 'schedules';

    const REQUEST_PRODUCT             = 'product';
    const REQUEST_SCHEDULES           = 'schedules';

    public function __construct(
        Request                                        $request,
        DashboardAuthService                           $authService,
        DashboardJWTService                            $jwtService,
        protected CompanyRepository                    $companyRepository,
        protected APIConsumer                          $integrationApiConsumer,
        protected LeadCampaignTransformer              $leadCampaignTransformer,
        protected LeadCampaignDeliveryMethodRepository $leadCampaignDeliveryMethodRepository,
    )
    {
        parent::__construct($request, $authService, $jwtService);
    }

    public function getOverview(): JsonResponse
    {
        $company = $this->getCompany();
        if ($company === null) throw new ModelNotFoundException();

        $industryService = $this->getService();
        if (!$industryService) throw new ModelNotFoundException();

        $query = $company->campaigns();
        $pausedQuery = $company->campaigns()->where(LeadCampaign::TABLE . '.' . LeadCampaign::STATUS, LeadCampaign::STATUS_INACTIVE);

        //todo: get product from request
        /** @var Product $product */
        $product = Product::query()->firstOrFail();

        $scopedQuery = $this->addJoins($pausedQuery, $industryService, $product);

        return $this->formatResponse(
            [
                "status" => true,
                "campaign_count"    => $this->addJoins($query, $industryService, $product)->count(), // TODO: Investigate once using A2 models and scope to services.
                "paused_campaigns"  => $scopedQuery->count(),
                "campaign_ids"      => $scopedQuery->pluck('uuid'),
            ]
        );
    }

    /**
     * @param int $companyId
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getCampaigns(int $companyId): JsonResponse
    {
        $company = $this->getCompany();
        if ($company === null) throw new ModelNotFoundException();

        $industryService = $this->getService();
        if (!$industryService) throw new ModelNotFoundException();

        /** @var Product $product */
        $product = Product::query()->where(Product::FIELD_NAME, $this->request->get(self::REQUEST_PRODUCT))->firstOrFail();

        $query = $this->addJoins($company->campaigns(), $industryService, $product);

        $campaigns = $query
            ->select([
                ProductCampaign::TABLE .'.'. ProductCampaign::FIELD_ID . ' as product_campaign_id',
                DatabaseHelperService::readOnlyDatabase() .'.'. LeadCampaign::TABLE.'.*'
            ])->withCount(LeadCampaign::RELATION_LEAD_CAMPAIGN_ZIP_LOCATIONS)
            ->withCount(LeadCampaign::RELATION_LEAD_CAMPAIGN_STATE_LOCATIONS)
            ->with(LeadCampaign::RELATION_LEAD_CAMPAIGN_LEAD_CATEGORIES)
            ->when($product->name !== ProductEnum::LEAD->value, fn($query) =>
                $query->with([
                    LeadCampaign::RELATION_PRODUCT_CAMPAIGN,
                    LeadCampaign::RELATION_PRODUCT_CAMPAIGN .'.'. ProductCampaign::RELATION_BUDGETS
            ]))->get();

        $grouping = $product->name === ProductEnum::APPOINTMENT->value
            ? Grouping::APPOINTMENT_CAMPAIGN
            : Grouping::CAMPAIGN;

        $campaigns->each(fn(LeadCampaign $campaign) => $campaign->statistics = []);

        return $this->formatResponse([
            self::RESPONSE_STATUS      => true,
            self::RESPONSE_CAMPAIGNS   => CampaignSummaryResource::collection($campaigns),
            self::RESPONSE_CONFIG_DATA => $this->getCampaignConfigData($companyId),
            self::RESPONSE_PRODUCT     => $product->name
        ]);
    }

    protected function addJoins(Builder|HasMany $query, IndustryService $service, Product $product): Builder|HasMany
    {
        return $query->join(
            DatabaseHelperService::database() . '.' . ProductCampaign::TABLE,
            fn(JoinClause $join) => $join->on(
                DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaign::TABLE . '.' . LeadCampaign::ID,
                '=',
                DatabaseHelperService::database() . '.' . ProductCampaign::TABLE . '.' . ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID
            )->where(DatabaseHelperService::database() . '.' . ProductCampaign::TABLE . '.' . ProductCampaign::FIELD_INDUSTRY_SERVICE_ID, '=', $service->id)
                ->where(DatabaseHelperService::database() . '.' . ProductCampaign::TABLE . '.' . ProductCampaign::FIELD_PRODUCT_ID, '=', $product->id)

        );
    }

    /**
     * Get full campaign detail for editing
     * @param int $companyId
     * @param string $industry
     * @param string $service
     * @param string $campaignUuid
     * @return JsonResponse
     * @throws Exception
     */
    public function getCampaignDetail(int $companyId, string $industry, string $service, string $campaignUuid): JsonResponse
    {
        $company = $this->getCompany();
        if ($company === null) throw new ModelNotFoundException();

        $campaign = new CampaignDetailResource(LeadCampaign::query()
            ->where('uuid', $campaignUuid)
            ->firstOrFail()
        );
        $contactDeliveries = ContactDeliveryResource::collection($this->leadCampaignDeliveryMethodRepository->getDeliveryMethodsWithContactsForCampaign($campaign->id));
        $legacyResponse = $this->integrationApiConsumer->get(
            $this->getIntegrationBaseRoute($company, "crm-deliveries/campaign/$campaignUuid")
        )->json()['data'] ?? [];

        return $this->formatResponse([
            self::RESPONSE_STATUS             => !!$campaign,
            self::RESPONSE_CAMPAIGN           => $campaign,
            self::RESPONSE_CONTACT_DELIVERIES => $contactDeliveries,
            self::RESPONSE_CRM_DELIVERIES     => $legacyResponse['crm_deliveries'] ?? [],
        ]);
    }

    /**
     * Update a campaign and attached delivery methods by integration
     * @param int $companyId
     * @param string $industry
     * @param string $service
     * @param string $campaignUuid
     * @param StoreCampaignRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function updateCampaign(int $companyId, string $industry, string $service, string $campaignUuid, StoreCampaignRequest $request): JsonResponse
    {
        $company = $this->getCompany();
        if ($company === null) throw new ModelNotFoundException();

        /** @var LeadCampaign $legacyCampaign */
        $legacyCampaign = LeadCampaign::query()->where(LeadCampaign::UUID, $campaignUuid)->firstOrFail();

        /** @var ProductCampaign $productCampaign */
        $productCampaign = ProductCampaign::query()->where(ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID, $legacyCampaign->id)->firstOrFail();

        $transformed = $this->leadCampaignTransformer->transform($request->safe()->toArray(), !$this->leadCampaign($productCampaign->product));
        $this->attachLegacyUserId($transformed);

        if (!$this->leadCampaign($productCampaign->product)) {
            $transformed[LeadCampaign::STATUS] = LeadCampaign::STATUS_INACTIVE;
        }

        $legacyResponse = $this->integrationApiConsumer->patch(
            $this->getIntegrationBaseRoute($company, "campaigns/$campaignUuid/update"),
            $transformed
        )->json()['data'] ?? [];

        if (!$this->leadCampaign($productCampaign->product))
            $this->createOrUpdateProductCampaignBudget($request->safe()->toArray(), $campaignUuid);

        $this->updateProductCampaignSchedules($productCampaign->id, $request->get(self::REQUEST_SCHEDULES, []));

        return $this->formatResponse([
            self::RESPONSE_STATUS   => $legacyResponse[self::RESPONSE_STATUS] ?? false,
            self::RESPONSE_CAMPAIGN => $legacyResponse['uuid'] ?? false,
            self::RESPONSE_MESSAGE  => $legacyResponse['message'] ?? 'Legacy server error.'
        ]);
    }

    /**
     * Pause and unpause Campaigns
     * @param BulkUpdateCampaignsRequest $request
     * @return JsonResponse
     */
    public function bulkUpdateCampaigns(BulkUpdateCampaignsRequest $request, ProductCampaignRepository $productCampaignRepository): JsonResponse
    {
        $company = $this->getCompany();
        if ($company === null) throw new ModelNotFoundException();

        $validated = $request->all();
        $pauseOptions = $validated['options'] ?? [];
        $reactivateAt = $pauseOptions['reactivate_at'] ?? null;

        $legacyUpdate = [];

        foreach ($validated['campaigns'] as $campaignStatus) {
            /** @var LeadCampaign $legacyCampaign */
            $legacyCampaign = LeadCampaign::query()->where(LeadCampaign::UUID, $campaignStatus['id'])->firstOrFail();
            /** @var ProductCampaign $productCampaign */
            $productCampaign = ProductCampaign::query()->where(ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID, $legacyCampaign->id)->firstOrFail();

            if ($productCampaign->product->name === ProductEnum::LEAD->value) {
                $legacyUpdate[] = $campaignStatus;
            }

            $previousStatus =  $productCampaign->status;

            $productCampaignRepository->updateStatus(
                $productCampaign->id,
                $campaignStatus['status'],
                $company,
                $this->getUser()->id,
                $validated['options']['reason'] ?? null,
                $reactivateAt ?? 0,
            );

            if ((bool)$previousStatus === false && (bool)$productCampaign->status === true && $productCampaign->budgets->isNotEmpty())
                $this->updateCampaignBudgetStart($productCampaign->budgets);
        }

        if ($legacyUpdate) {
            $legacyUpdate = [
                'campaigns'  => $legacyUpdate,
                'options'    => $pauseOptions,
            ];

            $this->attachLegacyUserId($legacyUpdate);

            $legacyResponse = $this->integrationApiConsumer->patch(
                $this->getIntegrationBaseRoute($company, "campaigns/bulk-update-status"),
                $legacyUpdate
            )->json()['data'] ?? [];
        }

        return $this->formatResponse([
            self::RESPONSE_STATUS => !$legacyUpdate || $legacyResponse,
        ]);
    }

    /**
     * Create a new campaign via integration
     * @param StoreCampaignRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function createCampaign(StoreCampaignRequest $request): JsonResponse
    {
        $company = $this->getCompany();
        if ($company === null) throw new ModelNotFoundException();

        /** @var Product $product */
        $product = Product::query()->where(Product::FIELD_NAME, $this->request->get(self::REQUEST_PRODUCT))->firstOrFail();

        $transformed = $this->leadCampaignTransformer->transform($request->safe()->toArray(), !$this->leadCampaign($product));
        $this->attachLegacyUserId($transformed);

        if (!$this->leadCampaign($product)) {
            $transformed[LeadCampaign::STATUS] = LeadCampaign::STATUS_INACTIVE;
        }

        $legacyResponse = $this->integrationApiConsumer->post(
            $this->getIntegrationBaseRoute($company, 'campaigns/create'),
            $transformed
        )->json()['data'] ?? [];

        if(isset($legacyResponse['uuid'])) {
            $productCampaignId = $this->createOrUpdateAssociation($legacyResponse['uuid'], $company, $product->id);

            if (!$this->leadCampaign($product))
                $this->createOrUpdateProductCampaignBudget($request->safe()->toArray(), $legacyResponse['uuid']);

            if ($productCampaignId) {
                $this->updateProductCampaignSchedules($productCampaignId, $request->get(self::REQUEST_SCHEDULES, []));
            }
        }

        return $this->formatResponse([
            self::RESPONSE_STATUS   => $legacyResponse[self::RESPONSE_STATUS] ?? false,
            self::RESPONSE_CAMPAIGN => $legacyResponse['uuid'] ?? false,
        ]);
    }

    /**
     * @param Product $product
     *
     * @return bool
     */
    protected function leadCampaign(Product $product): bool
    {
        return $product->name === ProductEnum::LEAD->value;
    }

    protected function createOrUpdateAssociation(string $uuid, Company $company, int $productId): ?int
    {
        try {
            /** @var LeadCampaign $campaign */
            $campaign = LeadCampaign::query()->where(LeadCampaign::UUID, $uuid)->firstOrFail();
            $service = $this->getService();

            if(!$service) throw new ModelNotFoundException();

            /** @var ProductCampaign $productCampaign */
            $productCampaign = ProductCampaign::query()->updateOrCreate(
                [ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID => $campaign->id],
                [
                    ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID => $campaign->id,
                    ProductCampaign::FIELD_INDUSTRY_SERVICE_ID => $service->id,
                    ProductCampaign::FIELD_PRODUCT_ID => $productId,
                    ProductCampaign::FIELD_COMPANY_ID => $company->id,
                    ProductCampaign::FIELD_NAME => $campaign->name,
                    ProductCampaign::FIELD_STATUS => true
                ],
            );

            return $productCampaign->id;
        } catch (Exception $e) {
            logger()->error("Failed to create association: " . $e->getMessage());
            return null;
        }
    }

    /**
     * @param array $data
     * @param string $legacyUuid
     *
     * @return void
     */
    protected function createOrUpdateProductCampaignBudget(array $data, string $legacyUuid): void
    {
        /** @var LeadCampaign $campaign */
        $campaign = LeadCampaign::query()->where(LeadCampaign::UUID, $legacyUuid)->firstOrFail();

        /** @var ProductCampaign $productCampaign */
        $productCampaign = ProductCampaign::query()->where(ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID, $campaign->id)->firstOrFail();

        foreach ($data['appointment_budgets'] as $budget) {
            /** @var ProductCampaignBudget|null $productCampaignBudget */
            $productCampaignBudget = $productCampaign->budgets()->where(ProductCampaignBudget::FIELD_QUALITY_TIER, $budget['quality_tier'])->first();

            if (!$productCampaignBudget) {
                $productCampaign->budgets()->create([
                    ProductCampaignBudget::FIELD_QUALITY_TIER           => $budget['quality_tier'],
                    ProductCampaignBudget::FIELD_CATEGORY               => $budget['category'],
                    ProductCampaignBudget::FIELD_VALUE_TYPE             => $budget['budget_type'],
                    ProductCampaignBudget::FIELD_VALUE                  => $budget['budget_type'] !== ProductCampaignBudget::VALUE_TYPE_NO_LIMIT ? $budget['budget_value'] : 0,
                    ProductCampaign::FIELD_STATUS                       => $budget['status'],
                    ProductCampaignBudget::FIELD_BUDGET_START_TIMESTAMP => now(),
                    ProductCampaignBudget::FIELD_MAX_BUDGET_USAGE       => ProductCampaignBudget::DEFAULT_MAX_BUDGET_USAGE
                ]);
            } else {
                $productCampaignBudget->update([
                    ProductCampaignBudget::FIELD_CATEGORY               => $budget['category'],
                    ProductCampaignBudget::FIELD_VALUE_TYPE             => $budget['budget_type'],
                    ProductCampaignBudget::FIELD_VALUE                  => $budget['budget_type'] !== ProductCampaignBudget::VALUE_TYPE_NO_LIMIT ? $budget['budget_value'] : 0,
                    ProductCampaign::FIELD_STATUS                       => $budget['status'],
                    ProductCampaignBudget::FIELD_BUDGET_START_TIMESTAMP => $this->hasBudgetUpdated($productCampaignBudget, $budget) ? now() : $productCampaignBudget->budget_start_timestamp,
                    ProductCampaignBudget::FIELD_MAX_BUDGET_USAGE       => ProductCampaignBudget::DEFAULT_MAX_BUDGET_USAGE
                ]);
            }
        }
    }

    /**
     * @param int $companyId
     * @param string $industry
     * @param string $service
     * @param string $campaignUuid
     * @return JsonResponse
     * @throws Exception
     */
    public function deleteCampaign(int $companyId, string $industry, string $service, string $campaignUuid): JsonResponse
    {
        $company = $this->getCompany();
        if ($company === null) throw new ModelNotFoundException();
        $data = [];
        $this->attachLegacyUserId($data);
        $campaignId = LeadCampaign::query()
            ->where(LeadCampaign::UUID, $campaignUuid)
            ->first()
            ?->{LeadCampaign::ID};

        $legacyResponse = $this->integrationApiConsumer->delete(
            $this->getIntegrationBaseRoute($company, "campaigns/$campaignUuid/delete"),
            $data
        )->json()['data'] ?? [];
        $success = $legacyResponse['status'] ?? false;

        if ($success && $campaignId) {
            $this->cleanUpCampaignAssociations($campaignId);
        }

        return $this->formatResponse([
            self::RESPONSE_STATUS => $success
        ]);
    }

    /**
     * @return JsonResponse
     * @throws Exception
     */
    public function getCrmDeliveryDetail(): JsonResponse
    {
        $company = $this->getCompany();
        if ($company === null) throw new ModelNotFoundException();

        $crmDeliveryId = $this->request->route('crmDeliveryId');

        $legacyResponse = $this->integrationApiConsumer->get(
            $this->getIntegrationBaseRoute($company, "crm-deliveries/delivery/$crmDeliveryId")
        )->json()['data'] ?? [];

        $transform =  new CrmDeliveryResource($legacyResponse['crm_delivery'] ?? []);

        return $this->formatResponse([
            self::RESPONSE_STATUS       => $legacyResponse['status'] ?? false,
            self::RESPONSE_CRM_DELIVERY => $transform,
        ]);
    }

    /**
     * @param StoreCrmDeliveryRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function createCrmDeliveryForCampaign(StoreCrmDeliveryRequest $request, CrmDeliveryTransformer $transformer): JsonResponse
    {
        $company = $this->getCompany();
        if ($company === null) throw new ModelNotFoundException();

        $payload =  $transformer->transform($request->safe()->toArray());

        $legacyResponse = $this->integrationApiConsumer->post(
            $this->getIntegrationBaseRoute($company, "crm-deliveries/create"),
            $payload
        )->json()['data'] ?? [];

        return $this->formatResponse([
            self::RESPONSE_STATUS   => $legacyResponse['status'] ?? false,
            'id'                    => $legacyResponse['id'] ?? null
        ]);
    }

    /**
     * @param StoreCrmDeliveryRequest $request
     * @param CrmDeliveryTransformer $transformer
     * @return JsonResponse
     * @throws Exception
     */
    public function updateCrmDelivery(StoreCrmDeliveryRequest $request, CrmDeliveryTransformer $transformer): JsonResponse
    {
        $company = $this->getCompany();
        if ($company === null) throw new ModelNotFoundException();

        $payload =  $transformer->transform($request->safe()->toArray());
        $crmDeliveryId = $this->request->route('crmDeliveryId');

        $legacyResponse = $this->integrationApiConsumer->patch(
            $this->getIntegrationBaseRoute($company, "crm-deliveries/delivery/$crmDeliveryId"),
            $payload
        )->json()['data'] ?? [];

        return $this->formatResponse([
            self::RESPONSE_STATUS   => $legacyResponse['status'] ?? false,
        ]);
    }

    /**
     * @return JsonResponse
     * @throws Exception
     */
    public function deleteCrmDelivery(): JsonResponse
    {
        $company = $this->getCompany();
        if ($company === null) throw new ModelNotFoundException();

        $crmDeliveryId = $this->request->route('crmDeliveryId');
        $campaignUuid = $this->request->route('campaignUuid');

        $legacyResponse = $this->integrationApiConsumer->delete(
            $this->getIntegrationBaseRoute($company, "crm-deliveries/delivery/$campaignUuid/$crmDeliveryId"),
        )->json()['data'] ?? [];

        return $this->formatResponse([
            self::RESPONSE_STATUS   => $legacyResponse['status'] ?? false,
            self::RESPONSE_MESSAGE  => $legacyResponse['message'] ?? null,
        ]);
    }

    /**
     * @return JsonResponse
     * @throws Exception
     */
    public function deleteContactDelivery(): JsonResponse
    {
        $company = $this->getCompany();
        if ($company === null) throw new ModelNotFoundException();

        $campaignUuid = $this->request->route('campaignUuid');
        $leadDeliveryMethodId = $this->request->route('leadDeliveryMethodId');
        if (!$campaignUuid || !$leadDeliveryMethodId) throw new ModelNotFoundException;

        $legacyResponse = $this->integrationApiConsumer->delete(
            $this->getIntegrationBaseRoute($company, "campaigns/$campaignUuid/contact-delivery/$leadDeliveryMethodId")
        )->json()['data'] ?? [];

        return $this->formatResponse([
            self::RESPONSE_STATUS => !!($legacyResponse['status'] ?? null),
            self::RESPONSE_MESSAGE  => $legacyResponse['message'] ?? null,
        ]);
    }

    public function getZipCodesForCampaigns(): JsonResponse
    {
        $company = $this->getCompany();
        if ($company === null) throw new ModelNotFoundException();

        $uuids = $this->request->get('uuids');
        $ids = LeadCampaign::query()
            ->whereIn(LeadCampaign::UUID, $uuids)
            ->pluck(LeadCampaign::ID);
        $locationIds = LeadCampaignLocation::query()
            ->whereIn(LeadCampaignLocation::LEAD_CAMPAIGN_ID, $ids)
            ->pluck(LeadCampaignLocation::LOCATION_ID);
        $zipCodes = Location::query()
            ->whereIn(Location::ID, $locationIds)
            ->where(Location::TYPE, Location::TYPE_ZIP_CODE)
            ->pluck(Location::ZIP_CODE)
            ->unique();

        return $this->formatResponse([
            self::RESPONSE_STATUS => $zipCodes->count(),
            'zip_codes'           => $zipCodes,
        ]);
    }

    /**
     * @param ProductCampaign $productCampaign
     * @param array $scheduleIds
     * @return void
     * @throws BindingResolutionException
     */
    private function updateProductCampaignSchedules(int $productCampaignId, array $scheduleIds): void
    {
        $calendarService = app()->make(AppointmentCalendarIntegrationService::class);
        $calendarService->updateProductCampaignSchedules($productCampaignId, $scheduleIds);
    }

    /**
     * Load general campaign data
     * @param int $companyId
     * @throws Exception
     * @return array
     */
    private function getCampaignConfigData(int $companyId): array
    {
        $company = $this->getCompany();
        if ($company === null) throw new ModelNotFoundException();

        $propertyTypes = PropertyType::all()->toArray();
        $leadSalesTypes = SaleType::query()->whereNotIn(SaleType::FIELD_KEY, SaleType::DISABLED_LEAD_SALES_TYPES)->get()->toArray();
        $baseDeliveryMethods = $this->leadCampaignDeliveryMethodRepository->getBaseDeliveryMethodsForCompany($companyId)
            ->filter(fn($delivery) => $delivery->contact);
        $crmConfig = $this->integrationApiConsumer->get(
            $this->getIntegrationBaseRoute($company, 'crm-deliveries/crm-config')
        )->json()['data'] ?? [];
        $crmDeliveries = $this->integrationApiConsumer->get(
            $this->getIntegrationBaseRoute($company, 'crm-deliveries/company')
        )->json()['data'] ?? [];

        return [
            'leads_property_types' => $propertyTypes,
            'leads_sales_types'  => $leadSalesTypes,
            'contact_deliveries' => ContactDeliveryResource::collection($baseDeliveryMethods),
            'crm_providers'      => CrmProviderResource::collection($crmConfig['crm_integrations'] ?? []),
            'crm_deliveries'     => CrmDeliveryResource::collection($crmDeliveries['crm_deliveries'] ?? []),
            'pause_reasons'      => LeadCampaignPauseReasonsList::all()->pluck(LeadCampaignPauseReasonsList::FIELD_NAME, LeadCampaignPauseReasonsList::FIELD_KEY),
        ];
    }

    /**
     * If a Campaign is deleted, clean up the CampaignServiceAssociations
     * @param int $campaignId
     * @return bool
     */
    protected function cleanUpCampaignAssociations(int $campaignId): bool
    {
        if (LeadCampaign::query()->find($campaignId)) return false;

        ProductCampaign::query()
            ->where(ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID, $campaignId)
            ->delete();

        return true;
    }

    /**
     * @param Company $company
     * @param string $suffix
     * @return string|null
     */
    private function getIntegrationBaseRoute(Company $company, string $suffix = ''): ?string
    {
        $companyReference = $company->reference;
        return $companyReference
            ? "/fixr-dashboard/$companyReference/$suffix"
            : null;
    }

    /**
     * Legacy user id is required in some legacy services when going through integration
     * @param array $payload
     * @return void
     */
    private function attachLegacyUserId(array &$payload): void
    {
        $user = $this->getUser();
        if (!$user->legacy_id) {
            throw new UnauthorizedException("User does not have legacy authorisation.");
        }
        $payload['user_id'] = $user->legacy_id;
    }

    /**
     * @param Collection<ProductCampaignBudget> $campaignBudgets
     *
     * @return void
     */
    protected function updateCampaignBudgetStart(Collection $campaignBudgets): void
    {
        $campaignBudgets->each(function (ProductCampaignBudget $campaignBudget) {
            if ($campaignBudget->status) {
                $campaignBudget->budget_start_timestamp = now();

                $campaignBudget->save();
            }
        });
    }

    /**
     * @param ProductCampaignBudget $campaignBudget
     * @param array $newBudget
     *
     * @return bool
     */
    protected function hasBudgetUpdated(ProductCampaignBudget $campaignBudget, array $newBudget): bool
    {
        if ((bool)$campaignBudget->status === false && (bool)$newBudget['status'] === true)
            return true;
        if ($campaignBudget->value_type !== $newBudget['budget_type'])
            return true;
        if ($campaignBudget->value_type === $newBudget['budget_type'] && (float)$campaignBudget->value !== (float)$newBudget['budget_value'])
            return true;

        return false;
    }
}
