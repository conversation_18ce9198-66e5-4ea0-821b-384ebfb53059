<?php

namespace App\Http\Controllers\DashboardAPI;

use App\Enums\ContractKeys;
use App\Enums\ContractProvider;
use App\Enums\ContractType;
use App\Http\Resources\Dashboard\CompanyResource;
use App\Http\Resources\Dashboard\CompanyUserResource;
use App\Models\Contract;
use App\Models\ContractKey;
use App\Models\Odin\Website;
use App\Repositories\CompanyContractRepository;
use App\Services\Docusign\DocuSignService;
use Illuminate\Http\JsonResponse;

class UserController extends BaseDashboardApiController
{
    public function me(CompanyContractRepository $contractRepository): JsonResponse
    {
        $user = $this->getUser();
        $company = $user->company;
        $companyData = (new CompanyResource($company))->toArray($this->request);

        /** @var Website $website */
        $website = Website::query()->where(Website::FIELD_ABBREVIATION, ContractType::FIXR)->firstOrFail();

        /** @var ContractKey $contractKey */
        $contractKey = ContractKey::query()->where(ContractKey::FIELD_KEY, ContractKeys::TERMS_AND_CONDITIONS)->firstOrFail();

        $companyData['contract_accepted'] = !!$contractRepository->getValidCompanyUserContractByType($user, $contractKey, $website);

        return $this->formatResponse([
            "user"    => (new CompanyUserResource($user))->toArray($this->request),
            "company" => $companyData,
        ]);
    }

    /**
     * This will be called when we want to explicitly check if the contract was signed with docusign
     *
     * @param CompanyContractRepository $contractRepository
     * @param DocuSignService $docuSignService
     * @return JsonResponse
     */
    public function contract(CompanyContractRepository $contractRepository, DocuSignService $docuSignService): JsonResponse
    {
        $companyContract = $contractRepository->getMostRecentContract($this->getUser());

        if(!$companyContract)
            return $this->formatResponse([
                'status' => false
            ]);

        return $this->formatResponse([
            'status'            => true,
            'contract_accepted' => $docuSignService->hasUserSignedContract($companyContract)
        ]);
    }
}
