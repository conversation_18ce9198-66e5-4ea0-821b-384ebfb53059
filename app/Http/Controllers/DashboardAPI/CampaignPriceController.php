<?php

namespace App\Http\Controllers\DashboardAPI;

use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Http\Requests\Dashboard\DashboardPriceRequest;
use App\Jobs\DispatchPubSubEvent;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadSalesType;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\PropertyType;
use App\Models\Odin\QualityTier;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\ProductCampaignRepository;
use App\Repositories\Odin\ProductPriceRepository;
use Illuminate\Http\JsonResponse;

class CampaignPriceController extends BaseDashboardApiController
{
    /**
     * @param ProductCampaignRepository $productCampaignRepository
     * @param LocationRepository $locationRepository
     * @param ProductPriceRepository $priceRepository
     * @param DashboardPriceRequest $request
     *
     * @return JsonResponse
     */
    public function getStatePrices(
        ProductCampaignRepository $productCampaignRepository,
        LocationRepository $locationRepository,
        ProductPriceRepository $priceRepository,
        DashboardPriceRequest $request
    ): JsonResponse
    {
        $campaign = $productCampaignRepository->findCampaignByLegacyUuidOrFail($request->get(DashboardPriceRequest::CAMPAIGN_UUID));
        $stateLocation = $locationRepository->getStateByStateAbbr($request->get(DashboardPriceRequest::STATE_ABBR));

        /** @var PropertyType $propertyType */
        $propertyType = PropertyType::query()->findOrFail($request->get(DashboardPriceRequest::PROPERTY_TYPE_ID));

        $floorPrices = $priceRepository->getStateFloorPricesForServiceProduct(
            stateLocationId: $stateLocation->id,
            serviceProductId: $productCampaignRepository->getServiceProductFromCampaign($campaign)->id,
            propertyTypeId: $propertyType->id
        );

        $bidPrices = $priceRepository->getStateBidPricesForCampaignAndServiceProduct(
            stateLocationId: $stateLocation->id,
            productCampaignId: $campaign->id,
            serviceProductId: $productCampaignRepository->getServiceProductFromCampaign($campaign)->id,
            propertyTypeId: $propertyType->id
        );

       return $this->formatResponse([
           'status' => true,
           'prices' => $priceRepository->transformAndGroupPricesByQualityTier($floorPrices, $bidPrices)
       ]);
    }

    /**
     * @param ProductCampaignRepository $productCampaignRepository
     * @param LocationRepository $locationRepository
     * @param ProductPriceRepository $priceRepository
     * @param DashboardPriceRequest $request
     *
     * @return JsonResponse
     */
    public function getCountyPrices(
        ProductCampaignRepository $productCampaignRepository,
        LocationRepository $locationRepository,
        ProductPriceRepository $priceRepository,
        DashboardPriceRequest $request
    ): JsonResponse
    {
        $campaign = $productCampaignRepository->findCampaignByLegacyUuidOrFail($request->get(DashboardPriceRequest::CAMPAIGN_UUID));
        $stateLocation = $locationRepository->getStateByStateAbbr($request->get(DashboardPriceRequest::STATE_ABBR));
        $countyLocation = $locationRepository->getCounty($stateLocation->state_key, $request->get(DashboardPriceRequest::COUNTY_KEY));

        /** @var PropertyType $propertyType */
        $propertyType = PropertyType::query()->findOrFail($request->get(DashboardPriceRequest::PROPERTY_TYPE_ID));

        $floorPrices = $priceRepository->getCountyFloorPricesForServiceProduct(
            stateLocationId: $stateLocation->id,
            serviceProductId: $productCampaignRepository->getServiceProductFromCampaign($campaign)->id,
            countyLocationId: $countyLocation->id,
            propertyTypeId: $propertyType->id
        );

        $bidPrices = $priceRepository->getCountyBidPricesForCampaignAndServiceProduct(
            stateLocationId: $stateLocation->id,
            productCampaignId: $campaign->id,
            serviceProductId: $productCampaignRepository->getServiceProductFromCampaign($campaign)->id,
            countyLocationId: $countyLocation->id,
            propertyTypeId: $propertyType->id
        );

        return $this->formatResponse([
            'status' => true,
            'prices' => $priceRepository->transformAndGroupPricesByQualityTier($floorPrices, $bidPrices, true)
        ]);
    }

    /**
     * @param ProductCampaignRepository $productCampaignRepository
     * @param LocationRepository $locationRepository
     * @param ProductPriceRepository $priceRepository
     * @param DashboardPriceRequest $request
     *
     * @return JsonResponse
     */
    public function bidStatePrice(
        ProductCampaignRepository $productCampaignRepository,
        LocationRepository $locationRepository,
        ProductPriceRepository $priceRepository,
        DashboardPriceRequest $request
    ): JsonResponse
    {
        $campaign = $productCampaignRepository->findCampaignByLegacyUuidOrFail($request->get(DashboardPriceRequest::CAMPAIGN_UUID));
        $stateLocation = $locationRepository->getStateByStateAbbr($request->get(DashboardPriceRequest::STATE_ABBR));
        $price = $request->get(DashboardPriceRequest::PRICE);

        /** @var PropertyType $propertyType */
        $propertyType = PropertyType::query()->findOrFail($request->get(DashboardPriceRequest::PROPERTY_TYPE_ID));

        /** @var QualityTier $qualityTier */
        $qualityTier = QualityTier::query()->findOrFail($request->get(DashboardPriceRequest::QUALITY_TIER_ID));

        /** @var LeadSalesType $saleType */
        $saleType = LeadSalesType::query()->findOrFail($request->get(DashboardPriceRequest::SALE_TYPE_ID));

        $initialPrice = $priceRepository->updateStateBidPrice(
            $stateLocation->id,
            $campaign->id,
            $productCampaignRepository->getServiceProductFromCampaign($campaign)->id,
            $qualityTier->id,
            $propertyType->id,
            $saleType->id,
            $price,
            true,
            true
        );

        $this->dispatchBidEvent($initialPrice, $price, $stateLocation->id, $campaign);

        return $this->formatResponse([
            'status' => true
        ]);
    }

    /**
     * @param ProductCampaignRepository $productCampaignRepository
     * @param LocationRepository $locationRepository
     * @param ProductPriceRepository $priceRepository
     * @param DashboardPriceRequest $request
     *
     * @return JsonResponse
     */
    public function bidCountyPrice(
        ProductCampaignRepository $productCampaignRepository,
        LocationRepository $locationRepository,
        ProductPriceRepository $priceRepository,
        DashboardPriceRequest $request
    ): JsonResponse
    {
        $campaign = $productCampaignRepository->findCampaignByLegacyUuidOrFail($request->get(DashboardPriceRequest::CAMPAIGN_UUID));
        $stateLocation = $locationRepository->getStateByStateAbbr($request->get(DashboardPriceRequest::STATE_ABBR));
        $countyLocation = $locationRepository->getCounty($stateLocation->state_key, $request->get(DashboardPriceRequest::COUNTY_KEY));
        $price = $request->get(DashboardPriceRequest::PRICE);

        /** @var PropertyType $propertyType */
        $propertyType = PropertyType::query()->findOrFail($request->get(DashboardPriceRequest::PROPERTY_TYPE_ID));

        /** @var QualityTier $qualityTier */
        $qualityTier = QualityTier::query()->findOrFail($request->get(DashboardPriceRequest::QUALITY_TIER_ID));

        /** @var LeadSalesType $saleType */
        $saleType = LeadSalesType::query()->findOrFail($request->get(DashboardPriceRequest::SALE_TYPE_ID));

        $initialPrice = $priceRepository->updateCountyBidPrice(
            $stateLocation->id,
            $campaign->id,
            $productCampaignRepository->getServiceProductFromCampaign($campaign)->id,
            $countyLocation->id,
            $qualityTier->id,
            $propertyType->id,
            $saleType->id,
            $price,
            true,
        );

        $this->dispatchBidEvent($initialPrice, $price, $countyLocation->id, $campaign);

        return $this->formatResponse([
            'status' => true
        ]);
    }

    protected function dispatchBidEvent(?float $initialPrice, float $price, float $locationId, ProductCampaign $campaign): void
    {
        if ($initialPrice && $initialPrice !== $price) {
            $eventName = $initialPrice > $price
                ? EventName::BID_DECREASED
                : EventName::BID_INCREASED;
            $leadCampaign = $campaign->legacyCampaign;

            DispatchPubSubEvent::dispatch(EventCategory::CAMPAIGNS, $eventName, [
                'campaign_reference'    => $leadCampaign->{LeadCampaign::UUID},
                'company_reference'     => $leadCampaign->{LeadCampaign::RELATION_COMPANY}->{EloquentCompany::REFERENCE},
                'campaign_id'           => $leadCampaign->{LeadCampaign::ID},
                'product_campaign_id'   => $campaign->id,
                'old_bid'               => $initialPrice,
                'new_bid'               => $price,
                'location_id'           => $locationId
            ]);
        }
    }
}
