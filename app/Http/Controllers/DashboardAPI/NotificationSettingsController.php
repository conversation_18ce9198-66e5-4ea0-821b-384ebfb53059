<?php

namespace App\Http\Controllers\DashboardAPI;

use App\Http\Requests\Dashboard\StoreNotificationSettingsRequest;
use App\Http\Resources\Dashboard\LegacyTransformers\NotificationSettingLegacyTransformer;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Services\Dashboard\DashboardAuthService;
use App\Services\Dashboard\JWT\DashboardJWTService;
use App\Services\Legacy\APIConsumer;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Exception;

class NotificationSettingsController extends BaseDashboardApiController
{
    const RESPONSE_STATUS   = 'status';
    const RESPONSE_MESSAGE  = 'message';
    const RESPONSE_NOTIFICATION_SETTINGS = 'notification_settings';

    const ROUTE_INTEGRATION_PREFIX  = '/fixr-dashboard';
    const ROUTE_INTEGRATION_SUFFIX  = '/notification-settings';

    public function __construct(
        Request              $request,
        DashboardAuthService $authService,
        DashboardJWTService  $jwtService,
        protected APIConsumer $integrationApiConsumer,
        protected NotificationSettingLegacyTransformer $legacyTransformer,
    )
    {
        parent::__construct($request, $authService, $jwtService);
    }

    private function getIntegrationRoute(Company $company, ?string $manualSuffix = null): string
    {
        $suffix = $manualSuffix ?? self::ROUTE_INTEGRATION_SUFFIX;
        return self::ROUTE_INTEGRATION_PREFIX."/$company->reference".$suffix;
    }

    /**
     * @return JsonResponse
     * @throws Exception
     */
    public function getNotificationSettings(): JsonResponse
    {
        $company = $this->getCompany();
        if($company === null)
            throw new ModelNotFoundException();

        $notificationSettingsResponse = $this->integrationApiConsumer->get(
            $this->getIntegrationRoute($company)
        )?->json()['data'][0] ?? null;


        if (!$notificationSettingsResponse) {
            return $this->formatResponse([ self::RESPONSE_STATUS   => false ]);
        }

        $transformedData = $this->legacyTransformer->transformGetRequest($notificationSettingsResponse);

        return $this->formatResponse([
            self::RESPONSE_STATUS                   => !!$transformedData,
            self::RESPONSE_NOTIFICATION_SETTINGS    => $transformedData,
        ]);
    }

    /**
     * @param StoreNotificationSettingsRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function updateNotificationSettings(StoreNotificationSettingsRequest $request): JsonResponse
    {
        $company = $this->getCompany();
        if($company === null)
            throw new ModelNotFoundException();
        $user = $this->getUser();

        if (!$user->{CompanyUser::FIELD_LEGACY_ID}) {
            return $this->formatResponse([
                self::RESPONSE_STATUS   => false,
                self::RESPONSE_MESSAGE  => "User does not have legacy authorisation."
            ]);
        }

        $transformedNotificationRequest = [
            'contacts'  => $this->legacyTransformer->transformPutRequest($request->safe()->toArray()),
            'user_id'   => $user->legacy_id,
        ];

        $legacyResponse = $this->integrationApiConsumer->patch(
            $this->getIntegrationRoute($company),
            $transformedNotificationRequest
        )->json()['data'] ?? null;

        return $this->formatResponse($legacyResponse);
    }

}
