<?php

namespace App\Http\Controllers\DashboardAPI;

use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Http\Controllers\API\CompaniesController;
use App\Http\Controllers\Controller;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ServiceProduct;
use App\Models\User;
use App\Repositories\Odin\IndustryServiceRepository;
use App\Services\Dashboard\DashboardAuthService;
use App\Services\Dashboard\JWT\DashboardJWTService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use App\Services\Workflows\WorkflowEventService;
use App\Workflows\Shortcodes\InitiatorShortcode;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\UnauthorizedException;
use Illuminate\Contracts\Container\BindingResolutionException;

abstract class BaseDashboardApiController extends Controller
{
    public function __construct(
        protected Request              $request,
        protected DashboardAuthService $authService,
        protected DashboardJWTService  $jwtService,
    ) {
        session_write_close();
    }

    /**
     * Handles formatting the data as an accepted JsonResponse.
     *
     * @param array $data
     * @return JsonResponse
     */
    protected function formatResponse(array $data = []): JsonResponse
    {
        return response()->json(["data" => $data]);
    }

    protected function makeErrorResponse(array $messages = [], int $status = JsonResponse::HTTP_UNAUTHORIZED): JsonResponse
    {
        return response()->json(["messages" => $messages], $status);
    }

    /**
     * Returns the user making an action.
     *
     * @return CompanyUser|null
     */
    protected function getUser(): ?CompanyUser
    {
        /** @var CompanyUser|null $user */
        $user = CompanyUser::query()->find($this->authService->getUserId());

        return $user;
    }

    /**
     * @param bool $throwAuth
     * @return Company|null
     * @throws ModelNotFoundException
     */
    protected function getCompanyOrFail(bool $throwAuth = true): ?Company
    {
        $company = $this->getCompany($throwAuth);

        if ($company == null)
            throw new ModelNotFoundException();

        return $company;
    }

    protected function getCompany(bool $throwAuth = true): ?Company
    {
        $user = $this->getUser();
        if(!$user)
            return null;

        $companyId = $this->request->route('companyId', null) ?? $user->company_id;

        if(!$companyId)
            return null;

        if($user->company_id !== intval($companyId) && $throwAuth)
            throw new UnauthorizedException("You do not belong to this company.");

        /** @var Company $company */
        $company = Company::query()->findOrFail($companyId);

        return $company;
    }

    /**
     * Checks if a user is logged in.
     *
     * @return bool
     */
    protected function isLoggedIn(): bool
    {
        return $this->getUser() !== null;
    }

    /**
     * Returns the user currently shadowing the user.
     *
     * @return User|null
     */
    protected function getShadower(): ?User
    {
        /** @var User|null $user */
        $user = User::query()->find($this->authService->getShadowerId());

        return $user;
    }

    /**
     * Returns whether a user is shadowing another user.
     *
     * @return bool
     */
    protected function isShadowing(): bool
    {
        return $this->getShadower() !== null;
    }

    /**
     * Handles retrieving the industry service for a given user, based on the route param.
     *
     * @return IndustryService|null
     * @throws BindingResolutionException
     */
    protected function getService(): ?IndustryService
    {
        $service = $this->request->route('service', null);

        if($service === null)
            return null;

        /** @var IndustryServiceRepository $repository */
        $repository = app()->make(IndustryServiceRepository::class);

        return $repository->getIndustryServiceBySlug($service);
    }

    public function getProduct(): ?Product
    {
        $key = $this->request->route('productKey', null);

        if(!$key)
            return null;

        /** @var Product|null $product */
        $product = Product::query()->where(Product::FIELD_NAME, $key)->first();

        return $product;
    }

    /**
     * @return ServiceProduct|null
     * @throws BindingResolutionException
     */
    protected function getServiceProduct(): ?ServiceProduct
    {
        $product = $this->getProduct();
        $service = $this->getService();

        /** @var ServiceProduct|null */
        return ServiceProduct::query()
            ->where([
                ServiceProduct::FIELD_PRODUCT_ID => $product->id,
                ServiceProduct::FIELD_INDUSTRY_SERVICE_ID => $service->id,
            ])->first();
    }

    /**
     * @return CompanyCampaign
     */
    public function getCompanyCampaign(): CompanyCampaign
    {
        /** @type CompanyCampaign */
        return $this
            ->getCompany()
            ->futureCampaigns()
            ->where(CompanyCampaign::FIELD_ID, $this->request->route('campaignId', null))
            ->firstOrFail();
    }

    /**
     * Fires a workflow event for the given category and event name
     *
     * @param EventCategory $eventCategory Category of workflow event
     * @param EventName $eventName Name of workflow event
     * @param array $customPayloadData Custom data, ex. old and new values
     * @return void
     */
    protected function triggerWorkflowEvent(
        WorkflowEventService $workflowEventService,
        EventCategory $eventCategory,
        EventName $eventName,
        array $customPayloadData = []
    ): void
    {
        $defaultPayload =
        [
            CompaniesController::REQUEST_TYPE              => $eventCategory->value,
            CompaniesController::REQUEST_EVENT             => $eventName->value,
            CompaniesController::REQUEST_COMPANY_ID        => $this->getCompany()?->id,
            CompaniesController::REQUEST_COMPANY_REFERENCE => $this->getCompany()?->reference,
            InitiatorShortcode::PAYLOAD_KEY                => $this->getUser()?->legacy_id
        ];

        $payload = array_merge($defaultPayload, $customPayloadData);

        $workflowEventService->handle($eventCategory->value, $eventName->value, $payload);
    }

    /**
     * Builds a string list of updated values from given values and changes for workflow events
     *
     * @param array $values
     * @return string
     */
    protected function buildDiffString(array $values): string
    {
        $diffString = "";
        foreach ($values as $key => $value) {
            $diffString = $diffString . $key . ": " . $value . ", ";
        }

        return rtrim($diffString, ", ");
    }
}
