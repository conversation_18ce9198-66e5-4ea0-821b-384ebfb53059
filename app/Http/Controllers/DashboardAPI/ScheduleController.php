<?php

namespace App\Http\Controllers\DashboardAPI;

use App\Http\Controllers\API\ClientDashboard\AppointmentCalendarIntegrationController;
use App\Http\Requests\Dashboard\CreateStaticCalendarRequest;
use App\Http\Requests\Dashboard\GetCalendarEventsInDateRangeRequest;
use App\Http\Requests\Dashboard\UpdateIntegratedCalendarRequest;
use App\Services\Dashboard\DashboardAuthService;
use App\Services\Dashboard\JWT\DashboardJWTService;
use App\Services\Odin\Appointments\AppointmentCalendarIntegrationService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Throwable;

class ScheduleController extends BaseDashboardApiController
{
    const string RESPONSE_STATUS    = 'status';
    const string RESPONSE_SCHEDULES = 'schedules';

    const string REQUEST_SCHEDULE                      = 'schedule';
    const string REQUEST_NAME                          = 'name';
    const string REQUEST_TIMEZONE                      = 'timezone';
    const string REQUEST_ONLINE_DURATION               = 'online_duration';
    const string REQUEST_IN_HOME_DURATION              = 'in_home_duration';
    const string REQUEST_IN_HOME_BUFFER_BEFORE         = 'in_home_buffer_before';
    const string REQUEST_IN_HOME_BUFFER_AFTER          = 'in_home_buffer_after';
    const string REQUEST_ONLINE_BUFFER_BEFORE          = 'online_buffer_before';
    const string REQUEST_ONLINE_BUFFER_AFTER           = 'online_buffer_after';
    const string REQUEST_OVERLAPPING_EVENTS_ALLOWED    = 'overlapping_events_allowed';
    const string REQUEST_PLATFORM                      = 'platform';
    const string REQUEST_ITEMS                         = 'items';
    const string REQUEST_OVERRIDE_ITEMS                = 'override_items';
    const string REQUEST_CALENDAR_ID                   = 'id';
    const string REQUEST_START_DATE                    = 'start_date';
    const string REQUEST_END_DATE                      = 'end_date';
    const string REQUEST_IN_HOME_SAME_DAY_APPOINTMENTS = 'in_home_same_day_appointments';
    const string REQUEST_ONLINE_SAME_DAY_APPOINTMENTS  = 'online_same_day_appointments';

    public function __construct(
        Request $request,
        DashboardAuthService $authService,
        DashboardJWTService $jwtService,
        protected AppointmentCalendarIntegrationService $appointmentCalendarIntegrationService
    )
    {
        parent::__construct($request, $authService, $jwtService);
    }

    public function getAllCompanySchedules(): JsonResponse
    {
        $company = $this->getCompany();
        $user = $this->getUser();

        return $this->formatResponse([
            self::RESPONSE_STATUS    => true,
            self::RESPONSE_SCHEDULES => $this->appointmentCalendarIntegrationService->getAllCompanyCalendars($company->id, $user)
        ]);
    }

    public function createStaticCalendar(CreateStaticCalendarRequest $request): JsonResponse
    {
        $data = $request->safe();
        $user = $this->getUser();

        $response = $this->appointmentCalendarIntegrationService->createStaticCalendar(
            $user,
            $data[self::REQUEST_NAME],
            $data[self::REQUEST_TIMEZONE],
            $data[self::REQUEST_ONLINE_DURATION],
            $data[self::REQUEST_IN_HOME_DURATION],
            $data[self::REQUEST_IN_HOME_BUFFER_BEFORE],
            $data[self::REQUEST_IN_HOME_BUFFER_AFTER],
            $data[self::REQUEST_ONLINE_BUFFER_BEFORE],
            $data[self::REQUEST_ONLINE_BUFFER_AFTER],
            $data[AppointmentCalendarIntegrationController::REQUEST_IN_HOME_SAME_DAY_APPOINTMENTS],
            $data[AppointmentCalendarIntegrationController::REQUEST_ONLINE_SAME_DAY_APPOINTMENTS],
            $data[self::REQUEST_OVERLAPPING_EVENTS_ALLOWED]
        );

        return $this->formatResponse([
            self::RESPONSE_STATUS => !!$response,
        ]);
    }

    public function updateSchedule(UpdateIntegratedCalendarRequest $request): JsonResponse
    {
        $data = $request->safe();
        $user = $this->getUser();
        $calendarId = $this->request->get(self::REQUEST_CALENDAR_ID);
        if (!$calendarId) throw new ModelNotFoundException("Could not update the requested Schedule.");

        $success = $this->appointmentCalendarIntegrationService->saveCalendar(
            $calendarId,
            $user,
            $data[self::REQUEST_NAME],
            $data[self::REQUEST_TIMEZONE],
            $data[self::REQUEST_ONLINE_DURATION],
            $data[self::REQUEST_IN_HOME_DURATION],
            $data[self::REQUEST_IN_HOME_BUFFER_BEFORE],
            $data[self::REQUEST_IN_HOME_BUFFER_AFTER],
            $data[self::REQUEST_ONLINE_BUFFER_BEFORE],
            $data[self::REQUEST_ONLINE_BUFFER_AFTER],
            $data[AppointmentCalendarIntegrationController::REQUEST_IN_HOME_SAME_DAY_APPOINTMENTS],
            $data[AppointmentCalendarIntegrationController::REQUEST_ONLINE_SAME_DAY_APPOINTMENTS],
            $data[self::REQUEST_OVERLAPPING_EVENTS_ALLOWED],
            $data[self::REQUEST_PLATFORM],
            $data[self::REQUEST_ITEMS],
            $data[self::REQUEST_OVERRIDE_ITEMS]
        );

        return $this->formatResponse([
            self::RESPONSE_STATUS => $success,
        ]);
    }

    public function deleteSchedule(): JsonResponse
    {
        $user = $this->getUser();
        $calendarId = $this->request->get(self::REQUEST_CALENDAR_ID);
        if (!$calendarId) throw new ModelNotFoundException("Could not update the requested Schedule.");

        $success = $this->appointmentCalendarIntegrationService->deleteCalendar($calendarId, $user);

        return $this->formatResponse([
            self::RESPONSE_STATUS => $success,
        ]);
    }


    /**
     * @param GetCalendarEventsInDateRangeRequest $getCalendarEventsInDateRangeRequest
     * @return JsonResponse
     * @throws Throwable
     */
    public function getCalendarEventsInDateRange(GetCalendarEventsInDateRangeRequest $getCalendarEventsInDateRangeRequest): JsonResponse
    {
        $calendarId = $this->request->get(self::REQUEST_CALENDAR_ID);

        $data = $getCalendarEventsInDateRangeRequest->safe()->only([
            self::REQUEST_START_DATE,
            self::REQUEST_END_DATE
        ]);

        return $this->formatResponse([
            "status"          => true,
            "calendar_events" => $this->appointmentCalendarIntegrationService->getEventsForCalendarInDateRange(
                $calendarId,
                $data[self::REQUEST_START_DATE],
                $data[self::REQUEST_END_DATE],
                $this->getUser()
            )
        ]);
    }
}
