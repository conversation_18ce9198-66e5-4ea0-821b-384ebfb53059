<?php

namespace App\Http\Controllers\DashboardAPI;

use App\Http\Requests\Dashboard\StoreGenericProfitabilityAssumptionConfigurationRequest;
use App\Http\Resources\Dashboard\GenericProfitabilityAssumptionConfigurationResource;
use App\Repositories\Odin\GenericProfitabilityAssumptionConfigurationRepository;
use App\Services\Dashboard\DashboardAuthService;
use App\Services\Dashboard\JWT\DashboardJWTService;
use App\Services\Odin\GenericProfitabilityAssumptionConfigurationService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ProfitabilityAssumptionController extends BaseDashboardApiController
{
    const RESPONSE_STATUS                       = 'status';
    const RESPONSE_PROFITABILITY_CONFIGURATION  = 'profitability_configuration';
    const RESPONSE_PROFITABILITY_CONFIGURATIONS = 'profitability_configurations';

    /**
     * @param Request $request
     * @param DashboardAuthService $authService
     * @param DashboardJWTService $jwtService
     * @param GenericProfitabilityAssumptionConfigurationRepository $profitabilityAssumptionConfigurationRepository
     * @param GenericProfitabilityAssumptionConfigurationService $profitabilityAssumptionConfigurationService
     */
    public function __construct(
        Request                                                         $request,
        DashboardAuthService                                            $authService,
        DashboardJWTService                                             $jwtService,
        protected GenericProfitabilityAssumptionConfigurationRepository $profitabilityAssumptionConfigurationRepository,
        protected GenericProfitabilityAssumptionConfigurationService    $profitabilityAssumptionConfigurationService,
    )
    {
        parent::__construct($request, $authService, $jwtService);
    }

    /**
     * @param int $companyId
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getProfitabilityAssumption(int $companyId): JsonResponse
    {
        $industryService = $this->getService();

        if($industryService === null)
            throw new ModelNotFoundException();

        $profitabilityConfiguration = $this->profitabilityAssumptionConfigurationRepository->findByCompanyAndIndustryService($companyId, $industryService);

        return $this->formatResponse([
            self::RESPONSE_STATUS                       => !!$profitabilityConfiguration,
            self::RESPONSE_PROFITABILITY_CONFIGURATION  => $profitabilityConfiguration
                ? new GenericProfitabilityAssumptionConfigurationResource($profitabilityConfiguration)
                : [],
        ]);
    }

    public function getGlobalProfitabilityAssumptions(int $companyId): JsonResponse
    {
        $profitabilityConfigurations = $this->profitabilityAssumptionConfigurationRepository->getGlobalProfitabilityConfigurations($companyId);

        return $this->formatResponse([
            self::RESPONSE_STATUS                       => !!$profitabilityConfigurations,
            self::RESPONSE_PROFITABILITY_CONFIGURATIONS => GenericProfitabilityAssumptionConfigurationResource::collection($profitabilityConfigurations) ?? []
        ]);
    }

    /**
     * @param int $companyId
     * @param StoreGenericProfitabilityAssumptionConfigurationRequest $profitabilityRequest
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function updateProfitabilityAssumption(int $companyId, StoreGenericProfitabilityAssumptionConfigurationRequest $profitabilityRequest): JsonResponse
    {
        $validated = $profitabilityRequest->safe()->all();
        $industryService = $this->getService();

        if($industryService === null)
            throw new ModelNotFoundException();

        $updated = $this->profitabilityAssumptionConfigurationRepository->updateOrCreateProfitabilityConfiguration($companyId, $industryService, $validated);

        return $this->formatResponse([
            self::RESPONSE_STATUS                       => !!$updated,
            self::RESPONSE_PROFITABILITY_CONFIGURATION  => new GenericProfitabilityAssumptionConfigurationResource($updated)
        ]);
    }
}
