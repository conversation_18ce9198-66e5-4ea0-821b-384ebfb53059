<?php

namespace App\Http\Controllers\DashboardAPI;

use App\Builders\Odin\ProductAssignmentBuilder;
use App\Enums\Odin\Product as ProductEnum;
use App\Enums\RejectionReasons;
use App\Exceptions\CustomMessageException;
use App\Http\Requests\Dashboard\ConsumerProductSearchRequest;
use App\Http\Requests\RejectAppointmentRequest;
use App\Http\Resources\Dashboard\ProductResource;
use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use App\Repositories\Legacy\LeadSaleTypeRepository;
use App\Services\Legacy\APIConsumer;
use App\Services\MissedProducts\MissedProductService;
use App\Services\Odin\Appointments\AppointmentService;
use App\Services\Odin\Appointments\MultiIndustryAppointmentService;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\UnauthorizedException;
use Throwable;

class ProductController extends BaseDashboardApiController
{
    const REQUEST_SALES_TYPES       = 'sales_type';

    const RESPONSE_STATUS               = 'status';
    const RESPONSE_PRODUCTS             = 'products';
    const RESPONSE_REJECTION_PERCENTAGE = 'rejection_percentage';
    const REQUEST_GLOBAL                = 'global';
    const RESPONSE_MESSAGE              = 'message';

    const REQUEST_REJECTION_REASON = 'rejection_reason';
    const REQUEST_REJECTION_NOTES = 'rejection_notes';

    public function getMissedReasons(MissedProductService $service): JsonResponse
    {
        $industryService = $this->getService();

        if ($industryService === null)
            throw new ModelNotFoundException();

        return $this->formatResponse([
            "status"  => true,
            "reasons" => $service->getReasonsForMissedProducts($this->getCompany(), $industryService)
        ]);
    }

    public function getConsumerProducts(ConsumerProductSearchRequest $request): JsonResponse
    {
        $company = $this->getCompany();
        $industryService = $this->getService();
        $productType = $this->request->route('productType');
        $product = Product::query()->where(Product::FIELD_NAME, $productType)->first();
        $purchaseStatus = $product?->name === ProductEnum::APPOINTMENT->value ? 'appointment_purchased' : 'purchased';
        $productId = $product?->id ?? 1;
        $withAppointments = ($product && $product->name === ProductEnum::APPOINTMENT->value);


        $serviceProductId = ServiceProduct::query()
            ->where(ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $industryService->id)
            ->where(ServiceProduct::FIELD_PRODUCT_ID, $productId)
            ->firstOrFail()
            ->{ServiceProduct::FIELD_ID};

        if ($company === null || $industryService === null)
            throw new ModelNotFoundException();

        $validSearchOptions = $request->safe();

        $campaignId = ($validSearchOptions['campaign_id'] ?? null)
            ? LeadCampaign::query()
                ->where(LeadCampaign::UUID, $validSearchOptions['campaign_id'])
                ->first()
                ?->{LeadCampaign::ID}
            : null;

        $query = ProductAssignmentBuilder::query()
            ->forCompanyId($company->id)
            ->forCampaignId($campaignId)
            ->delivered(true)
            ->forStatus($validSearchOptions['status'] ?? null)
            ->forServiceProduct($serviceProductId)
            ->forLeadId($validSearchOptions['lead_id'] ?? $validSearchOptions['appointment_id'] ?? null)
            ->forName($validSearchOptions['contact_name'] ?? null)
            ->forStateOrZip($validSearchOptions['address'] ?? null)
            ->forFromDate($validSearchOptions['date_start'] ?? null)
            ->forToDate($validSearchOptions['date_end'] ?? null)
            ->forInvoiceId($validSearchOptions['invoice_id'] ?? null)
            ->missedOnly($validSearchOptions['missed_only'] ?? null)
            ->qualifiedOverBudget($validSearchOptions['over_budget'] ?? null)
            ->withAddress(true)
            ->withTracking(true)
            ->withAppointment($withAppointments)
            ->withDashboardFieldsForServiceId($industryService->id);

        if ($validSearchOptions->has('download') && ($validSearchOptions['download'] === true || strtolower($validSearchOptions['download']) === 'true')) {
            return $this->formatResponse([
                'status'                => true,
                self::RESPONSE_PRODUCTS => ProductResource::collection($query->get())
            ]);
        }

        $products = $query->paginate(10);
        $totalSpend = $query
            ->withAppointment(false)
            ->withAddress(false)
            ->withTracking(false)
            ->forStatus($purchaseStatus)
            ->getSpend();

        return $this->formatResponse([
            'status'                => true,
            self::RESPONSE_PRODUCTS => ProductResource::collection($products),
            'total'                 => $products->total(),
            'limit'                 => $products->perPage(),
            'offset'                => ($products->currentPage() - 1) * $products->perPage(),
            'total_spend'           => $totalSpend
        ]);

    }

    /**
     * @param APIConsumer $integrationApiConsumer
     * @return JsonResponse
     * @throws Exception
     */
    public function checkRejectionQuota(APIConsumer $integrationApiConsumer): JsonResponse
    {
        $company = $this->getCompany();
        if ($company === null)
            throw new ModelNotFoundException();

        $legacyId = $this->request->route('legacyId');
        $legacyResponse = $integrationApiConsumer->get(
            $this->getBaseIntegrationRoute($company, "$legacyId/check-rejection-quota")
        )->json()['data'] ?? [];
        $errorMessage = key_exists('status', $legacyResponse) ? $legacyResponse['message'] ?? null : 'There was an error communicating with the legacy server.';

        return $this->formatResponse([
            self::RESPONSE_STATUS  => $legacyResponse[self::RESPONSE_STATUS] ?? false,
            'exceeds_warning'      => $legacyResponse['exceeds_warning'] ?? false,
            self::RESPONSE_MESSAGE => $errorMessage,
        ]);
    }

    /**
     * @param APIConsumer $integrationApiConsumer
     * @return JsonResponse
     * @throws Exception
     */
    public function rejectLead(APIConsumer $integrationApiConsumer): JsonResponse
    {
        $company = $this->getCompany();
        if ($company === null)
            throw new ModelNotFoundException();

        $legacyId = $this->request->route('legacyId');
        /** @var ?ConsumerProduct $product */
        $consumerProduct = ConsumerProduct::query()->whereHas(ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT, function ($query) use($legacyId) {
            $query->where(ProductAssignment::FIELD_LEGACY_ID, $legacyId);
        })->first();

        if ($consumerProduct?->isMissedProduct()) throw new Exception('You cannot reject a missed product');

        $payload = $this->request->all();
        $this->attachLegacyUserId($payload);

        $legacyResponse = $integrationApiConsumer->patch(
            $this->getBaseIntegrationRoute($company, "$legacyId/reject-lead"),
            $payload
        )->json()['data'] ?? [];
        $errorMessage = key_exists('status', $legacyResponse) ? $legacyResponse['message'] ?? null : 'There was an error communicating with the legacy server.';

        return $this->formatResponse([
            self::RESPONSE_STATUS               => $legacyResponse[self::RESPONSE_STATUS] ?? false,
            self::RESPONSE_REJECTION_PERCENTAGE => $legacyResponse[self::RESPONSE_REJECTION_PERCENTAGE] ?? null,
            self::RESPONSE_MESSAGE              => $errorMessage,
        ]);
    }

    /**
     * @param APIConsumer $integrationApiConsumer
     * @return JsonResponse
     * @throws Exception
     */
    public function unrejectLead(APIConsumer $integrationApiConsumer): JsonResponse
    {
        $company = $this->getCompany();
        if ($company === null) throw new ModelNotFoundException();

        $legacyId = $this->request->route('legacyId');
        /** @var ?ConsumerProduct $product */
        $consumerProduct = ConsumerProduct::query()->whereHas(ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT, function ($query) use($legacyId) {
            $query->where(ProductAssignment::FIELD_LEGACY_ID, $legacyId);
        })->first();

        if ($consumerProduct?->isMissedProduct()) throw new Exception('You cannot reject a missed product');

        $legacyResponse = $integrationApiConsumer->patch(
            $this->getBaseIntegrationRoute($company, "$legacyId/unreject-lead"),
        )->json()['data'] ?? [];
        $errorMessage = key_exists('status', $legacyResponse) ? $legacyResponse['message'] ?? null : 'There was an error communicating with the legacy server.';

        return $this->formatResponse([
            self::RESPONSE_STATUS               => $legacyResponse['status'] ?? false,
            self::RESPONSE_REJECTION_PERCENTAGE => $legacyResponse[self::RESPONSE_REJECTION_PERCENTAGE] ?? null,
            self::RESPONSE_MESSAGE              => $errorMessage,
        ]);

    }

    /**
     * @param int $companyId
     * @param string $industry
     * @param string $service
     * @param int $appointmentId
     * @param RejectAppointmentRequest $request
     * @param MultiIndustryAppointmentService $appointmentService
     *
     * @return JsonResponse
     */
    public function rejectAppointment(int $companyId, string $industry, string $service, int $appointmentId, RejectAppointmentRequest $request, MultiIndustryAppointmentService $appointmentService): JsonResponse
    {
        $errMsg = '';

        try {
            $data = $request->safe()->only([
                self::REQUEST_REJECTION_NOTES,
                self::REQUEST_REJECTION_REASON
            ]);

            return $this->formatResponse([
                "status" => $appointmentService->rejectAppointment(
                    $appointmentId,
                    $companyId,
                    RejectionReasons::from($data[self::REQUEST_REJECTION_REASON]),
                    $data[self::REQUEST_REJECTION_NOTES]
                )
            ]);
        }
        catch(Throwable $e) {
            DB::rollBack();

            if($e instanceof CustomMessageException) {
                $errMsg = $e->getMessage();
            }

            $errLocation = $e->getFile().'. Line: '.$e->getLine().'. ';

            AppointmentService::writeAppointmentLog(
                $errLocation."Reject appt err: ".substr($e->getMessage(), 0, 255),
                [
                    "company_id" => $companyId,
                    "product_assignment_id" => $appointmentId,
                    "trace" => base64_encode(gzcompress($e->getTraceAsString(), 9))
                ]
            );

            logger()->error($e);
        }

        return $this->formatResponse([
            "status" => false,
            "error" => $errMsg
        ]);
    }

    protected function getLeadSaleTypeIds(): ?array
    {
        /** @var LeadSaleTypeRepository $repo */
        $repo = app()->make(LeadSaleTypeRepository::class);
        $leadSaleTypeIds = [];
        $types = explode(',', $this->request->get(self::REQUEST_SALES_TYPES, ''));

        if ($types && count($types) > 0) {
            foreach ($types as $saleType) {
                $leadSalesType = $repo->getByKeyValue($saleType);
                if ($leadSalesType) {
                    $leadSaleTypeIds[] = $leadSalesType->id;
                }
            }
        }

        return count($leadSaleTypeIds) > 0 ? $leadSaleTypeIds : null;
    }

    /**
     * @param Company $company
     * @param ?string $suffix
     * @return string
     */
    private function getBaseIntegrationRoute(Company $company, ?string $suffix = ''): string
    {
        return "/fixr-dashboard/$company->reference/leads/$suffix";
    }

    /**
     * @param array $payload
     * @return void
     */
    private function attachLegacyUserId(array &$payload): void
    {
        $user = $this->getUser();
        if (!$user->legacy_id) {
            throw new UnauthorizedException("User does not have legacy authorisation.");
        }
        $payload['user_id'] = $user->legacy_id;
    }
}
