<?php

namespace App\Http\Controllers;

use App\Models\Odin\Industry;
use App\Models\Odin\Product;
use App\Models\Odin\Website;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;

class IndustryManagementController extends Controller
{
    protected array $breadcrumbData = [];
    protected Industry|null $industry = null;
    protected Website|null $website = null;
    protected Product|null $product = null;


    public function index(): Factory|View|Application
    {

        $this->breadcrumbData[] = [
            [
                'name' => 'Websites',
                'id' => 'websites',
            ]
        ];
        $this->industry = null;

        return $this->appendDataToView();
    }

    public function appendDataToView(): Factory|View|Application
    {
        $breadcrumbs = $this->breadcrumbData;
        $industry = $this->industry;
        $website = $this->website;
        $product = $this->product;

        return view('industry-management', compact('breadcrumbs', 'industry', 'website', 'product'));
    }

    /**
     *
     * @param String $global
     * @return Factory|View|Application
     */
    public function sortTab(String $global): Factory|View|Application
    {
        $this->breadcrumbData = [];
        $this->setBreadcrumbData($global);

        return $this->appendDataToView();
    }

    public function sortIndustryPath(String $industry = null, String $service = null): Factory|View|Application
    {
        $this->breadcrumbData = [];

        $this->setBreadcrumbData('industries');
        if($industry && $service)
        {
            $this->setBreadcrumbData($industry);
            $this->industry = Industry::query()->where(Industry::FIELD_NAME, $industry)->first();
            $this->setBreadcrumbData($service);
        }

        return $this->appendDataToView();
    }

    public function sortWebsitePath(String $website, String $property): Factory|View|Application
    {
        $this->breadcrumbData = [];

        $this->setBreadcrumbData('websites');
        if($website && $property)
        {
            $this->setBreadcrumbData($website);
            $this->website = Website::query()->where(Website::FIELD_NAME, 'like', $website)->first();
            $this->setBreadcrumbData($property);
        }

        return $this->appendDataToView();
    }

    public function sortProductPath(String $product, String $service): Factory|View|Application
    {
        $this->breadcrumbData = [];

        $this->setBreadcrumbData('products');
        if($product && $service)
        {
            $this->setBreadcrumbData($product);
            $this->product = Product::query()->where(Product::FIELD_NAME, 'like', $product)->first();
            $this->setBreadcrumbData($service);
        }

        return $this->appendDataToView();
    }

    public function setBreadcrumbData(String $breadcrumb) : void
    {
        $this->breadcrumbData[] = [
            'name' => ucwords(str_replace('-', ' ', $breadcrumb)),
            'id' => $breadcrumb,
        ];
    }

}
