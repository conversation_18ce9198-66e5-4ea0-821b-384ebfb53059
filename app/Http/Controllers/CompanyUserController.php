<?php

namespace App\Http\Controllers;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\StoreCompanyUserRequest;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Repositories\CompanyContactRepository;
use App\Repositories\Odin\CompanyRepository;
use App\Repositories\Odin\CompanyUserRepository;
use App\Services\CompanyUserService;
use App\Transformers\Odin\CompanyUserTransformer;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
Use App\Http\Resources\Dashboard\CompanyUserResource;

class CompanyUserController extends APIController
{
    const MODULE_PERMISSION = 'companies';
    const ACTIVITY_LIMIT_PER_PAGE = 10;

    const REQUEST_NEW_USER = 'new_user';
    const REQUEST_PASSWORD = 'password';

    const RESPONSE_STATUS = 'status';
    const RESPONSE_CONTACTS = 'contacts';
    const RESPONSE_USERS = 'users';
    const RESPONSE_MESSAGE = 'message';

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param CompanyUserRepository $companyUserRepository
     * @param CompanyUserService $companyUserService
     * @param CompanyUserTransformer $companyUserTransformer
     * @param CompanyRepository $companyRepository
     */
    public function __construct
    (
        Request                          $request,
        JsonAPIResponseFactory           $apiResponseFactory,
        protected CompanyUserRepository  $companyUserRepository,
        protected CompanyUserService     $companyUserService,
        protected CompanyUserTransformer $companyUserTransformer,
        protected CompanyRepository      $companyRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param int $companyId
     * @return JsonResponse
     */
    public function getCompanyContacts(int $companyId): JsonResponse
    {
        $company = $this->companyRepository->findOrFail($companyId);
        $companyUsers = $this->companyUserRepository->getCompanyContactsAgainstCompanyId($company->{Company::FIELD_ID});

        $data = [
            self::RESPONSE_STATUS => true,
            self::RESPONSE_CONTACTS => CompanyUserResource::collection($companyUsers),
        ];

        return $this->formatResponse($data);
    }

    /**
     * @param int $companyId
     * @return JsonResponse
     */
    public function getPaginatedUsers(int $companyId): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_STATUS => true,
            self::RESPONSE_USERS => $this->companyUserRepository->getCompanyUsersAgainstCompanyId($companyId)
                ->paginate(self::ACTIVITY_LIMIT_PER_PAGE, ['*'], 'page', $this->request->get('page'))
                ->through(fn(CompanyUser $companyUser) => $this->companyUserTransformer->transformUser($companyUser))
        ]);
    }

    /**
     * @param int $companyId
     * @return JsonResponse
     */
    public function getUsers(int $companyId): JsonResponse
    {
        $company = $this->companyRepository->findOrFail($companyId);
        $companyUsers = $this->companyUserRepository->getCompanyUsersAgainstCompanyId($company->{Company::FIELD_ID})->get();

        $data = [
            self::RESPONSE_STATUS => true,
            self::RESPONSE_USERS => CompanyUserResource::collection($companyUsers),
        ];

        return $this->formatResponse($data);
    }

    /**
     * @param int $companyId
     * @param StoreCompanyUserRequest $request
     * @return JsonResponse
     */
    public function createCompanyContact(int $companyId, StoreCompanyUserRequest $request): JsonResponse
    {
        $company = $this->companyRepository->findOrFail($companyId);

        $data = $request->safe()->all();
        $data[CompanyUser::FIELD_CAN_LOG_IN] = false;

        return $this->formatResponse(
            $this->companyUserRepository->createCompanyContact($company, $data, true)
        );
    }

    /**
     * @param int $companyId
     * @param int $contactId
     * @param StoreCompanyUserRequest $request
     * @return JsonResponse
     */
    public function updateCompanyContact(int $companyId, int $contactId, StoreCompanyUserRequest $request): JsonResponse
    {
        $company = $this->companyRepository->findOrFail($companyId);
        $contact = $this->companyUserRepository->getCompanyContact($companyId, $contactId);
        return $this->formatResponse([
            self::RESPONSE_STATUS => $this->companyUserRepository->updateCompanyContact(
                $company->{Company::FIELD_REFERENCE}, $contact, $request->safe()->all())
        ]);
    }

    public function getCompanyContact(int $companyId, int $contactId)
    {
        return $this->formatResponse([
            self::RESPONSE_STATUS   => 'true',
            'contact'               => $this->companyUserRepository->getCompanyContact($companyId, $contactId)
        ]);
    }

    /**
     * @param int $companyId
     * @param int $userId
     * @param StoreCompanyUserRequest $request
     * @return JsonResponse
     */
    public function updateCompanyUser(int $companyId, int $userId, StoreCompanyUserRequest $request): JsonResponse
    {
        $company = $this->companyRepository->findOrFail($companyId);
        $user = $this->companyUserRepository->getCompanyUser($companyId, $userId);
        $data = $request->safe()->except(CompanyUser::FIELD_PASSWORD);
        return $this->formatResponse([
            self::RESPONSE_STATUS => $this->companyUserRepository->updateCompanyUser(
                $company->{Company::FIELD_REFERENCE}, $user, $data)
        ]);
    }

    /**
     * @param int $companyId
     * @param int $userId
     * @return JsonResponse
     */
    public function deleteCompanyUser(int $companyId, int $userId): JsonResponse
    {
        $company = $this->companyRepository->findOrFail($companyId);
        $user = $this->companyUserRepository->getCompanyUserOrCompanyContact($companyId, $userId);

        return $this->formatResponse([
            self::RESPONSE_STATUS => $this->companyUserRepository->deleteCompanyUser($company->{Company::FIELD_REFERENCE}, $user)
        ]);
    }

    /**
     * This route applies Admin2.0 authentication to the created CompanyUser - do not use for any sync or integration services
     * @param int $companyId
     * @param StoreCompanyUserRequest $request
     * @return JsonResponse
     */
    public function createCompanyUser(int $companyId, StoreCompanyUserRequest $request): JsonResponse
    {
        $company = $this->companyRepository->findOrFail($companyId);
        $validated = $request->safe()->all();

        $validated[CompanyUser::FIELD_AUTHENTICATION_TYPE] = CompanyUser::AUTHENTICATION_TYPE_ADMIN2;
        $validated[CompanyUser::FIELD_CAN_LOG_IN] = true;

        $newUser = $this->companyUserRepository->createCompanyUser($company->{Company::FIELD_ID}, $validated);
        $passwordApplied = $this->companyUserService->updatePassword($newUser, $validated[self::REQUEST_PASSWORD]);

        return $this->formatResponse([
            self::RESPONSE_STATUS => $passwordApplied,
        ]);
    }

    /**
     * @param int $companyId
     * @param int $userId
     * @return JsonResponse
     */
    public function resetCompanyUserPassword(int $companyId, int $userId): JsonResponse
    {
        $user = $this->companyUserRepository->getCompanyUser($companyId, $userId);
        return $this->formatResponse(
            $this->companyUserService->resetCompanyUserPassword($user),
        );
    }

    /**
     * @param int $companyId
     * @param int $userId
     * @return JsonResponse
     */
    public function updateCompanyUserPassword(int $companyId, int $userId): JsonResponse
    {
        $user = $this->companyUserRepository->getCompanyUser($companyId, $userId);
        $minPassword = CompanyUser::AUTHENTICATION_MINIMUM_PASSWORD_LENGTH;
        $validated = $this->request->validate([
            self::REQUEST_PASSWORD => ['required', 'string', "min:$minPassword", 'max:64']
        ]);

        return $this->formatResponse([
            self::RESPONSE_STATUS => $this->companyUserService->updatePassword($user, $validated[self::REQUEST_PASSWORD]),
        ]);
    }

    /**
     * @param int $companyId
     * @param int $contactId
     * @param CompanyContactRepository $contactRepository
     * @return JsonResponse
     */
    public function toggleContactPin(int $companyId, int $contactId, CompanyContactRepository $contactRepository): JsonResponse
    {
        $contact = CompanyUser::query()->where(['id' => $contactId, 'company_id' => $companyId])->firstOrFail();

        return $this->formatResponse([
            'status' => $contactRepository->toggleContactPin($contact->{CompanyUser::FIELD_ID})
        ]);
    }
}
