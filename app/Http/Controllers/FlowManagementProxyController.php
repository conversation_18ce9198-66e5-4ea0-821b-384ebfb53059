<?php

namespace App\Http\Controllers;

use App\Enums\Flows\RevisionType;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\PermissionType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\PublishRevisionRequest;
use App\Http\Requests\StoreFlowRequest;
use App\Models\Firestore\Flows\Revision;
use App\Models\Odin\IndustryService;
use App\Models\User;
use App\Services\FlowRevisionService;
use App\Services\FlowService;
use App\Services\Odin\ConfigurableFieldsService;
use App\Services\Workflows\FirestoreProxyService;
use Exception;
use Illuminate\Http\Client\Response;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\UnauthorizedException;
use Illuminate\Validation\ValidationException;
use Illuminate\View\View;

class FlowManagementProxyController extends APIController
{
    const REQUEST_COPY_TO_FLOW       = 'to_flow';
    const REQUEST_WORKING_REVISION   = 'working';
    const REQUEST_RENAME_OLD_NAME    = 'old_name';
    const REQUEST_RENAME_NEW_NAME    = 'new_name';

    const RESPONSE_STATUS            = 'status';
    const RESPONSE_MEDIA_LINK        = 'media_link';
    const RESPONSE_CONSUMER_FIELDS   = 'consumer_fields';
    const RESPONSE_DATA              = 'data';
    const RESPONSE_REVISION_META     = 'revision_meta';
    const RESPONSE_FLOW_REVISIONS    = 'revisions';

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected FirestoreProxyService $firestoreProxyService,
        protected FlowService $flowService
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * Get metadata for all Revisions of all Flows
     * @return JsonResponse
     */
    public function getRevisionMetaData(): JsonResponse
    {
        $response = $this->firestoreProxyService->get('/revisions')->json();
        $firebaseData = $response[self::RESPONSE_DATA][self::RESPONSE_REVISION_META];

        foreach ($firebaseData as &$flowGroup) {
            if (Arr::has($flowGroup, self::RESPONSE_FLOW_REVISIONS)) {
                foreach($flowGroup[self::RESPONSE_FLOW_REVISIONS] as &$revision) {
                    $this->addUserName($revision);
                }
            }
        }

        return $this->formatResponse([
            self::RESPONSE_STATUS           => true,
            self::RESPONSE_REVISION_META    => $firebaseData,
        ]);
    }

    /**
     * @throws ValidationException
     */
    public function getFlowOptions(): JsonResponse
    {
        $data = $this->flowService->getFlowOptions();

        return $this->formatResponse($data);
    }

    /**
     * Create a new Flow, e.g. "SolarCalculator", which will then contain the different revisions of the Calculator/Flow
     * @param StoreFlowRequest $request
     * @return Response
     */
    public function createNewFlow(StoreFlowRequest $request): Response
    {
        $validated = $request->safe()->toArray();

        return $this->firestoreProxyService->post('/flows/create-flow', $validated);
    }

    /**
     * Update a Flow - metadata only
     * @param StoreFlowRequest $request
     * @return Response
     */
    public function updateFlow(StoreFlowRequest $request): Response
    {
        $validated = $request->safe()->toArray();
        $flowId = $request->route('flowId');
        $this->addActioningUserToData($validated);

        return $this->firestoreProxyService->patch("/flows/$flowId/update-flow", $validated);
    }

    /**
     * @param PublishRevisionRequest $request
     * @return Response
     * @throws Exception
     */
    public function createNewRevision(PublishRevisionRequest $request): Response
    {
        $validated = $request->safe()->toArray();
        $this->addActioningUserToData($validated[Revision::REVISION_DATA]);
        $flowId = $request->route('flowId');

        return $this->firestoreProxyService->post("revisions/$flowId/create-new-revision", $validated);
    }

    /**
     * @param PublishRevisionRequest $request
     * @return Response
     * @throws Exception
     */
    public function publishRevision(PublishRevisionRequest $request): Response
    {
        $validated = $request->safe()->toArray();
        $flowId = $request->route('flowId');
        $revisionId = $request->route('revisionUuid');
        $this->addActioningUserToData($validated[Revision::REVISION_DATA]);

        return $this->firestoreProxyService->post("/revisions/$flowId/$revisionId/publish", $validated);
    }

    /**
     * @param PublishRevisionRequest $request
     * @return Response
     * @throws Exception
     */
    public function saveRevision(PublishRevisionRequest $request): Response
    {
        $validated = $request->safe()->toArray();
        $flowId = $request->route('flowId');
        $revisionId = $request->route('revisionUuid');
        $this->addActioningUserToData($validated[Revision::REVISION_DATA]);

        $response = $this->firestoreProxyService->put("/revisions/$flowId/$revisionId/save", $validated);

        return $response;
    }

    /**
     * Only used by V2
     * @param PublishRevisionRequest $request
     * @return Response
     * @throws Exception
     */
    public function createNewVariant(PublishRevisionRequest $request): Response
    {
        $validated = $request->safe()->toArray();
        $flowId = $request->route('flowId');
        $revisionId = $request->route('revisionUuid');
        $this->addActioningUserToData($validated[Revision::REVISION_DATA]);

        return $this->firestoreProxyService->post("/revisions/$flowId/$revisionId/new-variant", $validated);
    }

    /**
     * @return Response
     */
    public function checkFlowIdIsValid(): Response
    {
        $flowId = $this->request->route('flowId');

        return $this->firestoreProxyService->get("/flows/$flowId/is-valid");
    }

    /**
     * @return Response
     * @throws Exception
     */
    public function setProductionRevision(): Response
    {
        $flowId = $this->request->route('flowId');
        $revisionId = $this->request->route('revisionUuid');

        return $this->firestoreProxyService->patch("/flows/$flowId/$revisionId/set-production-revision");
    }

    /**
     * @param FlowRevisionService $flowRevisionService
     * @return JsonResponse
     * @throws Exception
     */
    public function uploadImage(FlowRevisionService $flowRevisionService): JsonResponse
    {
        $file = $this->request->file('file');
        $flowId = $this->request->route('flowId');

        $fileUrl = $flowRevisionService->uploadImage($file, $flowId);

        return $this->formatResponse([
            self::RESPONSE_STATUS       => !!$fileUrl,
            self::RESPONSE_MEDIA_LINK   => $fileUrl
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getIndustryServices(): JsonResponse
    {
        $industryServices = IndustryService::all();

        return $this->formatResponse([
            'services' => $industryServices->map(fn(IndustryService $industryServices) => [
                'industry' => $industryServices->industry->name,
                'industry_slug' => $industryServices->industry->slug,
                'service' => $industryServices->name,
                'service_slug' => $industryServices->slug
            ])
        ]);
    }

    /**
     * @param string $flowId
     * @param string $revisionId
     * @return Response
     * @throws Exception
     */
    public function copyRevision(string $flowId, string $revisionId): Response
    {
        $targetMeta = $this->request->validate([
            Revision::NAME              => ['string', 'min:4', 'max:64', 'required'],
            Revision::DESCRIPTION       => ['string', 'max:256', 'nullable'],
            Revision::PARENT_REVISION   => $revisionId,
            Revision::TYPE              => RevisionType::VARIANT->value,
        ]);
        $targetMeta[self::REQUEST_COPY_TO_FLOW] = $this->request->get(self::REQUEST_COPY_TO_FLOW);
        $targetMeta[self::REQUEST_WORKING_REVISION] = $this->request->get(self::REQUEST_WORKING_REVISION, false);
        $this->addActioningUserToData($targetMeta);

        return $this->firestoreProxyService->post("/revisions/$flowId/$revisionId/copy-to", $targetMeta);
    }

    public function deleteRevision(string $flowId, string $revisionId): Response
    {
        $this->userCanDelete();

        return $this->firestoreProxyService->delete("/revisions/$flowId/$revisionId");
    }

    public function deleteRevisionGroup(string $flowId, string $revisionGroupName): Response
    {
        $this->userCanDelete();

        $escaped = rawurlencode($revisionGroupName);

        return $this->firestoreProxyService->delete("revisions/$flowId/$escaped/delete-group");
    }

    public function undeleteRevision(string $flowId, string $revisionId): Response
    {
        $this->userCanDelete();

        return $this->firestoreProxyService->patch("revisions/$flowId/$revisionId/undelete");
    }

    public function hardDeleteRevision(string $flowId, string $revisionId): Response
    {
        $this->userCanDelete();

        return $this->firestoreProxyService->delete("revisions/$flowId/$revisionId/hard-delete");
    }

    public function renameRevisionGroup(string $flowId): Response
    {
        $validated = $this->request->validate([
            self::REQUEST_RENAME_OLD_NAME  => ['string', 'required'],
            self::REQUEST_RENAME_NEW_NAME  => ['string', 'min:4', 'max:64', 'required'],
        ]);

        return $this->firestoreProxyService->patch("revisions/$flowId/rename-group", $validated);
    }

    public function handleDeletedRevisionAccess(string $revisionId): void
    {
        $this->firestoreProxyService->patch("revisions/deleted-revision-accessed/$revisionId");
    }

    public function previewRevision(string $flowId, string $revisionId): View
    {
        $working = !!$this->request->get('working');
        $previewMode = true;

        return view('flow-client', compact('revisionId', 'flowId', 'working', 'previewMode'));
    }

    /**
     * @param ConfigurableFieldsService $configurableFieldsService
     * @return JsonResponse
     * @throws Exception
     */
    public function getConsumerFields(ConfigurableFieldsService $configurableFieldsService): JsonResponse
    {
        /** @var IndustryService $service */
        $service = IndustryService::query()
            ->where(IndustryService::FIELD_SLUG, $this->request->get('service_slug', ''))
            ->firstOrFail();

        $industry = $service->industry;

        $serviceFields = $configurableFieldsService->getFields(
            ConfigurableFieldsService::TYPE_CONSUMER ,
            ConfigurableFieldsService::CATEGORY_SERVICE,
            $service->id,
        );

        $industryFields = $configurableFieldsService->getFields(
            ConfigurableFieldsService::TYPE_CONSUMER ,
            ConfigurableFieldsService::CATEGORY_INDUSTRY,
            $industry->id,
        );

        $fields = [
            'global'        => GlobalConfigurableFields::cases(),
            'industry'      => $industryFields->pluck('key'),
            'service'       => $serviceFields->pluck('key'),
        ];

        return $this->formatResponse([
            self::RESPONSE_STATUS           => true,
            self::RESPONSE_CONSUMER_FIELDS  => $fields,
        ]);
    }

    /**
     * Attach User ID to Revision requests
     * @param $validatedData
     * @return void
     */
    private function addActioningUserToData(&$validatedData): void
    {
        /** @var User $user */
        $user = Auth::user();
        $validatedData[Revision::ACTIONED_BY] = $user->id;
    }

    /**
     * @param array $data
     * @return void
     */
    private function addUserName(array &$data): void
    {
        if ($data[Revision::ACTIONED_BY] ?? null) {
            $user = User::query()->find($data[Revision::ACTIONED_BY]);
            $data[Revision::ACTIONED_BY] = $user?->name ?? "-";
        }
    }

    /**
     * @return void
     */
    private function userCanDelete(): void
    {
        /** @var User $user */
        $user = Auth::user();
        if (!$user->hasPermissionTo(PermissionType::FLOW_MANAGEMENT_DELETE->value)) {
            throw new UnauthorizedException("User does not have permission to delete Revisions");
        }
    }
}
