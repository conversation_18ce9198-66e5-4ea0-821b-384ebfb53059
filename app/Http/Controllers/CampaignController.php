<?php

namespace App\Http\Controllers;

use App\Enums\Campaigns\CampaignStatus;
use App\Enums\CompanyConsolidatedStatus;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModule;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\Industry;
use App\Models\Odin\ProductAssignment;
use App\Services\DatabaseHelperService;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;

class CampaignController extends Controller
{
    public function index(Request $request): View|Factory|Application
    {
        $request->validate([
            'zip_code' => ['nullable', 'string'],
            'industries' => ['nullable', 'array'],
            'industries.*' => ['exists:industries,id'],
        ]);

        $query = CompanyCampaign::query()->with(['service.industry', 'company']);

        $query->whereHas('company', fn (Builder $query) => $query->where('consolidated_status', CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS));

        $zip_code = $request->get('zip_code');

        $this->filterByZipCode($query, $zip_code);

        $industries = $request->array('industries');

        if ($industries && count($industries)) {
            $query->whereHas('service', function (Builder $query) use ($industries) {
                $query->whereIn('industry_services.industry_id', $industries);
            });
        }

        $this->applyDateOfLastLeadPurchased($query);

        $campaigns = $query->paginate()->through(function (CompanyCampaign $campaign) {
            $campaign->date_of_last_lead_purchased = Carbon::parse($campaign->date_of_last_lead_purchased);

            return $campaign;
        });

        $industryOptions = Industry::whereHas('services', static function (Builder $query) {
            $query->whereHas('companyCampaigns');
        })->get();

        return view('campaigns.index', compact('campaigns', 'zip_code', 'industries', 'industryOptions'));
    }

    private function filterByZipCode(Builder $query, ?string $zipCode): void
    {
        $query->whereExists(function (QueryBuilder $query) use ($zipCode) {
            $query->from(CompanyCampaignLocationModule::TABLE)
                ->whereColumn('company_campaign_id', '=', CompanyCampaign::TABLE.'.id');

            $query->whereExists(function (QueryBuilder $query) use ($zipCode) {
                $query->from(CompanyCampaignLocationModuleLocation::TABLE)
                    ->whereColumn('module_id', '=', CompanyCampaignLocationModule::TABLE.'.id');

                $query->whereExists(function (QueryBuilder $query) use ($zipCode) {
                    $locations = Location::TABLE;

                    $query->from($locations)
                        ->whereColumn("$locations.id", '=',
                            CompanyCampaignLocationModuleLocation::TABLE.'.location_id');

                    $query->where("$locations.zip_code", $zipCode);
                });
            });
        });

        $query->where('status', CampaignStatus::ACTIVE->value);
    }

    private function applyDateOfLastLeadPurchased(Builder $query): void
    {
        $productAssignmentQuery = ProductAssignment::query()->selectRaw('company_id, max(created_at) as date_of_last_lead_purchased')->groupBy('company_id');

        $query->leftJoinSub($productAssignmentQuery, 'product_assignments_grouped_by_company_id',
            'product_assignments_grouped_by_company_id.company_id', '=', 'company_campaigns.company_id');

        $query->addSelect([
            'company_campaigns.*',
            'product_assignments_grouped_by_company_id.date_of_last_lead_purchased',
        ]);
    }
}
