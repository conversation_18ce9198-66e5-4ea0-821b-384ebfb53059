<?php

namespace App\Http\Controllers;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\SalesOverviewRequest;
use App\Http\Resources\Calendar\DemoResource;
use App\Http\Resources\Mailbox\MailboxEmailResource;
use App\Http\Resources\SalesOverview\CallResource;
use App\Http\Resources\SalesOverview\SalesOverviewResource;
use App\Http\Resources\SalesOverview\TextResource;
use App\Models\Calendar\Demo;
use App\Models\Conference\Conference;
use App\Models\Odin\Company;
use App\Models\User;
use App\Repositories\UserRepository;
use App\Services\DemoService;
use App\Services\Filterables\BaseFilterableService;
use App\Services\Filterables\SalesOverview\DemoFilterableService;
use App\Services\SalesOverviewService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\StreamedResponse;

class SalesOverviewController extends APIController
{
    const string STATUS   = 'status';
    const string DEMOS    = 'demos';
    const string PER_PAGE = 'per_page';
    const string PAGE     = 'page';

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected SalesOverviewService $salesOverviewService
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param SalesOverviewRequest $request
     * @return array
     */
    public function getSalesOverview(SalesOverviewRequest $request): array
    {
        $filters = $request->validated();

        $data = $this->salesOverviewService->getSalesOverviewQuery(
            sortBy   : Arr::get($filters, 'sort_by', []),
            dateRange: Arr::get($filters, 'date_range', []),
            userId   : Arr::get($filters, 'user_id'),
        );

        return SalesOverviewResource::paginate($data);
    }

    /**
     * @param User $user
     * @param SalesOverviewRequest $request
     *
     * @return AnonymousResourceCollection
     */
    public function getUserCalls(User $user, SalesOverviewRequest $request): AnonymousResourceCollection
    {
        return CallResource::collection(
            $this->salesOverviewService->getQueryForCalls(
                userId   : $user->id,
                dateRange: $request->validated('date_range') ?? [],
                direction: $request->validated('direction')
            )
                ->latest()
                ->paginate($this->request->get(self::PER_PAGE, 25))
        );
    }

    /**
     * @param User $user
     * @param SalesOverviewRequest $request
     *
     * @return AnonymousResourceCollection
     */
    public function getUserTexts(User $user, SalesOverviewRequest $request): AnonymousResourceCollection
    {
        return TextResource::collection(
            $this->salesOverviewService->getQueryForTexts(
                userId   : $user->id,
                dateRange: $request->validated('date_range') ?? [],
                direction: $request->validated('direction')
            )
                ->latest()
                ->paginate($this->request->get(self::PER_PAGE, 25))
        );
    }

    /**
     * @param User $user
     * @param SalesOverviewRequest $request
     *
     * @return AnonymousResourceCollection
     */
    public function getUserEmails(User $user, SalesOverviewRequest $request): AnonymousResourceCollection
    {
        return MailboxEmailResource::collection(
            $this->salesOverviewService->getQueryForEmails(
                userId   : $user->id,
                dateRange: $request->validated('date_range') ?? [],
                direction: $request->validated('direction')
            )
                ->latest()
                ->paginate($this->request->get(self::PER_PAGE, 25))
        );
    }

    /**
     * @param User $user
     * @param SalesOverviewRequest $request
     * @return JsonResponse
     */
    public function getUserDemos(User $user, SalesOverviewRequest $request): JsonResponse
    {
        return $this->formatResponse([
            'demos' => DemoResource::paginate(
                $this->salesOverviewService->getDemos(
                    userId   : $user->id,
                    dateRange: $request->validated('date_range', []),
                )),
            'user'  => [
                'id'   => $user->{User::FIELD_ID},
                'name' => $user->{User::FIELD_NAME}
            ]
        ]);
    }

    public function getDemoFilters(DemoFilterableService $demoFilterableService): JsonResponse
    {
        $options = $demoFilterableService->getDisplayData();

        return $this->formatResponse([
            self::STATUS                              => !!$options,
            BaseFilterableService::KEY_FILTER_OPTIONS => $options,
        ]);
    }

    public function searchDemos(DemoFilterableService $demoFilterableService, DemoService $demoService): JsonResponse
    {
        $filterOptions = $this->request->get(BaseFilterableService::KEY_FILTERS, []);
        $userId        = $this->request->get(Demo::FIELD_USER_ID);
        $perPage       = $this->request->get(self::PER_PAGE, 25);
        $page          = $this->request->get(self::PAGE, 1);

        $demos = $demoService->getFilteredDemoQuery(
            userId: $userId,
            filters: $filterOptions,
            companyId: $this->request->get(Demo::FIELD_COMPANY_ID)
        )
           ->paginate(perPage: $perPage, page: $page);

        return $this->formatResponse([
            self::STATUS => !!$demos,
            self::DEMOS  => DemoResource::collection($demos)->resource,
        ]);
    }

    /**
     * @param DemoService $demoService
     *
     * @return StreamedResponse
     */
    public function exportDemos(DemoService $demoService): StreamedResponse
    {
        $CSVHeader = ['Host', 'Attendees','Has External Participants', 'Company', 'Link', 'Sales Development Representative', 'Status', 'Length', 'Start Time'];
        $handle = fopen('php://output', 'w');

        fputcsv($handle, $CSVHeader);

        $demoService->getFilteredDemoQuery(
            userId: $this->request->get(Demo::FIELD_USER_ID),
            filters: $this->request->get(BaseFilterableService::KEY_FILTERS, []),
            companyId: $this->request->get(Demo::FIELD_COMPANY_ID)
        )->chunk(100, function (Collection $demos) use ($handle) {
            /** @var Demo $demo */
            foreach ($demos as $demo) {
                fputcsv($handle, [
                    $demo->user?->name,
                    $demo->calendarEvent->attendees()->count(),
                    $demo->has_external_participants ? 'YES' : 'NO',
                    $demo->company?->name,
                    $demo->company ? config('app.url') . "/companies/{$demo->company->id}" : '',
                    $demo->company?->salesDevelopmentRepresentative?->name,
                    Str::headline($demo->status ?? 'unknown'),
                    round($demo->calendarEvent->conferences()->sum(Conference::FIELD_DURATION_IN_SECONDS) / 60) . ' minutes',
                    $demo->calendarEvent->start_time->timezone('America/Denver')->toDateTimeString() . ' (America/Denver)'
                ]);
            }
        });

        return response()->stream(function () use ($handle) {
            fclose($handle);
        }, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment;',
            'Pragma' => 'no-cache',
            'Expires' => '0',
        ]);
    }

    public function searchUsers(UserRepository $userRepository): JsonResponse
    {
        $searchText = $this->request->get('search', '');
        $excludeIds = $this->request->get('exclude_ids', []);
        $users = $userRepository->searchUsersForAutocomplete($searchText, [User::FIELD_ID, User::FIELD_NAME], $excludeIds);

        return $this->formatResponse([
            self::STATUS => true,
            User::TABLE  => $users->get()->toArray(),
        ]);
    }

    public function associateCompanyWithDemo(): JsonResponse
    {
        $company = Company::query()->findOrFail($this->request->get('company_id'));
        $demo = Demo::query()->findOrFail($this->request->get('demo_id'));

        return $this->formatResponse([
            self::STATUS           => $demo->update([Demo::FIELD_COMPANY_ID => $company->id]),
            Demo::RELATION_COMPANY => [
                Company::FIELD_ID   => $company?->id,
                Company::FIELD_NAME => $company?->name,
            ],
        ]);
    }
}
