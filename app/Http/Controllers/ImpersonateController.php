<?php

namespace App\Http\Controllers;

use App\Services\ImpersonateService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;

class ImpersonateController extends Controller
{
    /**
     * @param ImpersonateService $service
     */
    public function __construct(protected ImpersonateService $service) {}

    /**
     * <PERSON>les impersonating a user.
     *
     * @param int $userId
     * @return RedirectResponse
     */
    public function impersonate(int $userId): RedirectResponse
    {
        $this->service->impersonate($userId);

        return response()->redirectTo("/");
    }

    /**
     * <PERSON>les clearing the impersonation.
     *
     * @return RedirectResponse
     */
    public function clear(): RedirectResponse
    {
        $this->service->clear();

        return response()->redirectTo("/");
    }
}
