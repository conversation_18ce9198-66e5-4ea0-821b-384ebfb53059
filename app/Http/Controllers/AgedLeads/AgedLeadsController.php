<?php

namespace App\Http\Controllers\AgedLeads;

use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\MarketingLogType;
use App\Events\MarketingCampaignConsumerRevalidated;
use App\Events\MarketingCampaignPageViewed;
use App\Exceptions\Shortcode\ShortcodeReplacementFailed;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\MarketingCampaign\GetMarketingCampaignConsumerDataRequest;
use App\Http\Resources\MarketingCampaign\ExternalAvailableCompaniesResource;
use App\Http\Resources\MarketingCampaign\ExternalMarketingConsumerResource;
use App\Jobs\MarketingCampaign\ProcessDirectConsumerAllocation;
use App\Jobs\MarketingCampaign\ProcessMarketingCampaignResponse;
use App\Models\MarketingCampaign;
use App\Models\MarketingCampaignConsumer;
use App\Models\Odin\Consumer;
use App\Repositories\MarketingCampaign\MarketingCampaignConsumerRepository;
use App\Repositories\Odin\ProductAssignmentRepository;
use App\Services\Consumer\ConsumerService;
use App\Services\MarketingCampaign\CallbackPayload\BaseCallbackPayload;
use App\Services\MarketingCampaign\CallbackPayload\DirectAllocationPayload;
use App\Services\MarketingCampaign\CallbackPayload\SolarValidatePayload;
use App\Services\MarketingCampaign\MarketingCampaignConsumerService;
use App\Services\MarketingCampaign\MarketingCampaignService;
use App\Services\MarketingCampaign\MarketingLogService;
use App\Services\Odin\TopCompanies\TopCompaniesService;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AgedLeadsController extends APIController
{
    const string RESPONSE_STATUS = 'status';

    public function __construct(
        Request                                       $request,
        JsonAPIResponseFactory                        $apiResponseFactory,
        protected MarketingCampaignConsumerRepository $marketingCampaignConsumerRepository,
        protected TopCompaniesService                 $topCompaniesService,
        protected ProductAssignmentRepository         $productAssignmentRepository,
        protected MarketingCampaignConsumerService    $marketingCampaignConsumerService,
        protected ConsumerService                     $consumerService,
        protected MarketingCampaignService            $marketingCampaignService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param string $marketingCampaignConsumerReference
     * @return JsonResponse
     * @deprecated New Marketing Campaigns should be parsing the consumer ref and external campaign id
     */
    public function getByMarketingCampaignConsumerReference(string $marketingCampaignConsumerReference): JsonResponse
    {
        try {
            $marketingCampaignConsumer = $this->marketingCampaignConsumerRepository
                ->getByMarketingCampaignConsumerReference(
                    $marketingCampaignConsumerReference
                );
        } catch (ModelNotFoundException $exception) {
            logger()->error($exception->getMessage());
            return $this->formatResponse([
                self::RESPONSE_STATUS => false,
            ]);
        }

        return $this->getMarketingConsumerData($marketingCampaignConsumer);
    }

    /**
     * @param string $marketingCampaignConsumerReference
     * @param Request $request
     * @return JsonResponse
     * @throws Exception
     */
    public function allocateConsumer(string $marketingCampaignConsumerReference, Request $request): JsonResponse
    {
        try {
            $marketingCampaignConsumer = $this->marketingCampaignConsumerRepository
                ->getByMarketingCampaignConsumerReference(
                    $marketingCampaignConsumerReference
                );
        } catch (ModelNotFoundException $exception) {
            logger()->error($exception->getMessage());
            return $this->formatResponse([
                self::RESPONSE_STATUS => false,
            ]);
        }

        //todo: campaign / company ids should get moved inside generic response field at some point
        $campaignIds = $request->get('campaign_ids', []);
        $companyIds  = $request->get('company_ids', []);
        $response    = $request->get('response', []);

        $responseData = [
            'campaign_ids' => $campaignIds,
            'company_ids' => $companyIds,
            ...$response,
        ];

        ProcessMarketingCampaignResponse::dispatch(
            $marketingCampaignConsumerReference,
            $responseData,
        );

        MarketingCampaignConsumerRevalidated::dispatch($marketingCampaignConsumer->{MarketingCampaignConsumer::FIELD_ID});

        return $this->formatResponse([
            self::RESPONSE_STATUS => true,
        ]);
    }

    /**
     * @param GetMarketingCampaignConsumerDataRequest $request
     * @param string $consumerReference
     * @return JsonResponse
     */
    public function getByConsumerAndCampaignReference(GetMarketingCampaignConsumerDataRequest $request, string $consumerReference): JsonResponse
    {
        $validated = $request->safe()->collect();
        $externalCampaignId = $validated->get(GetMarketingCampaignConsumerDataRequest::MARKETING_CAMPAIGN_EXTERNAL_REFERENCE);

        try {
            Consumer::query()->where(Consumer::FIELD_REFERENCE, $consumerReference)->firstOrFail();

            $marketingCampaignConsumer = $this->marketingCampaignConsumerService->retrieveOrCreateMarketingConsumer(
                $consumerReference,
                $externalCampaignId
            );

        } catch (ModelNotFoundException $exception) {
            logger()->error($exception->getMessage());
            return $this->formatResponse([
                self::RESPONSE_STATUS => false,
            ]);
        }

        return $this->getMarketingConsumerData($marketingCampaignConsumer);
    }

    /**
     * @param MarketingCampaignConsumer $marketingCampaignConsumer
     * @return JsonResponse
     */
    public function getMarketingConsumerData(MarketingCampaignConsumer $marketingCampaignConsumer): JsonResponse
    {
        /** @var MarketingCampaign $marketingCampaign */
        $marketingCampaign = $marketingCampaignConsumer->{MarketingCampaignConsumer::RELATION_MARKETING_CAMPAIGN};

        /** @var Consumer $consumer */
        $consumer = $marketingCampaignConsumer->{MarketingCampaignConsumer::RELATION_CONSUMER};

        $payload = $this->marketingCampaignService->getMarketingCampaignPayload($marketingCampaign);

        $topCampaignsAndCompanies = collect();

        if ($payload instanceof DirectAllocationPayload && empty($marketingCampaignConsumer->{MarketingCampaignConsumer::FIELD_REVALIDATED_AT})) {
            ProcessDirectConsumerAllocation::dispatch($marketingCampaignConsumer->{MarketingCampaignConsumer::FIELD_ID});

            MarketingCampaignConsumerRevalidated::dispatch($marketingCampaignConsumer->{MarketingCampaignConsumer::FIELD_ID});
        } else if (!$payload instanceof SolarValidatePayload) {
            $topCampaignsAndCompanies = $this->consumerService->getTopCompaniesAndCampaignsForConsumer($consumer);
        }

        try {
            $rendered = $payload->render($marketingCampaignConsumer);
        } catch (ShortcodeReplacementFailed $e) {
            MarketingLogService::log(
                message: $e->getMessage(),
                namespace: MarketingLogType::CALLBACK_RENDER_FAILED,
                level:LogLevel::ERROR,
                context: [
                    'marketing_campaign_id' => $marketingCampaign->id,
                    'marketing_campaign_consumer_id' => $marketingCampaignConsumer->id,
                ],
                relations: [
                    $marketingCampaign,
                    $marketingCampaignConsumer,
                ]
            );
            $payload = BaseCallbackPayload::fromMarketingCampaign($marketingCampaign);
            $rendered = $payload->render($marketingCampaignConsumer);
        }

        MarketingCampaignPageViewed::dispatch($marketingCampaignConsumer->{MarketingCampaignConsumer::FIELD_ID});

        return $this->formatResponse([
            self::RESPONSE_STATUS => !!$consumer,
            'consumer'            => new ExternalMarketingConsumerResource($marketingCampaignConsumer),
            'companies'           => ExternalAvailableCompaniesResource::collection($topCampaignsAndCompanies),
            'callback'            => [
                'type'   => $payload->getType()->value,
                'render' => $rendered
            ]
        ]);
    }
}
