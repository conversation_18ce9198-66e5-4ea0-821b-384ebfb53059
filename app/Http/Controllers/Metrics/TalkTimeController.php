<?php

namespace App\Http\Controllers\Metrics;

use App\Enums\Metrics\MetricFormat;
use App\Enums\Metrics\MetricRange;
use App\Models\Call;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class TalkTimeController extends MetricController
{
    public function format(): MetricFormat
    {
        return MetricFormat::SECONDS;
    }

    public function calculate(MetricRange $range): Collection
    {
        $phones = Auth::user()->phones->pluck('id');

        $select = 'TIMESTAMPDIFF(SECOND, call_start, call_end) as duration';

        return collect([
            'previous' => Call::selectRaw($select)
                ->whereBetween('call_start', $range->previous())
                ->whereIn('phone_id', $phones)
                ->pluck('duration')
                ->sum(),
            'current' => Call::selectRaw($select)
                ->whereBetween('call_start', $range->current())
                ->whereIn('phone_id', $phones)
                ->pluck('duration')
                ->sum(),
        ]);
    }
}
