<?php

namespace App\Http\Controllers\Metrics;

use App\Enums\Metrics\MetricFormat;
use App\Enums\Metrics\MetricRange;
use App\Http\Controllers\Controller;
use App\Http\Requests\Metrics\MetricRequest;
use Illuminate\Support\Collection;

abstract class MetricController extends Controller
{
    public function __invoke(MetricRequest $request)
    {
        $metric = $this->calculate(MetricRange::from($request->safe()->collect()->get('range')));

        return response()->json([
            'data' => [
                'format' => $this->format(),
                'current' => round($metric->get('current'), 2),
                'change' => $this->determineRawChange($metric),
                'percentage' => $this->determinePercentageChange($metric),
            ],
        ]);
    }

    private function determineRawChange(Collection $metric) {
        $previous = $metric->get('previous');
        $current = $metric->get('current');

        return round($current - $previous, 2);
    }

    private function determinePercentageChange(Collection $metric)
    {
        $previous = $metric->get('previous');
        $current = $metric->get('current');

        $percentage = $previous > 0 ? (($current - $previous) / $previous * 100) : $current * 100;

        return round(abs($percentage), 2);
    }

    abstract public function calculate(MetricRange $range): Collection;

    abstract public function format(): MetricFormat;
}
