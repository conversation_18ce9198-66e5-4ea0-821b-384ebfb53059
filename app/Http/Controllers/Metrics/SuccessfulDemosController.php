<?php

namespace App\Http\Controllers\Metrics;

use App\Enums\Metrics\MetricFormat;
use App\Enums\Metrics\MetricRange;
use App\Enums\Prospects\CloserDemoResolution;
use App\Models\Prospects\CloserDemo;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class SuccessfulDemosController extends MetricController
{
    public function format(): MetricFormat
    {
        return MetricFormat::NUMBER;
    }

    public function calculate(MetricRange $range): Collection
    {
        return collect([
            'previous' => CloserDemo::whereBelongsTo(Auth::user())
                ->whereBetween('demo_at', $range->previous())
                ->whereResolution(CloserDemoResolution::SUCCESSFUL)
                ->count(),
            'current' => CloserDemo::whereBelongsTo(Auth::user())
                ->whereBetween('demo_at', $range->current())
                ->whereResolution(CloserDemoResolution::SUCCESSFUL)
                ->count(),
        ]);
    }
}
