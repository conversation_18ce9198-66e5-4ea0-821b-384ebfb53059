<?php

namespace App\Http\Controllers\Metrics;

use App\Enums\Metrics\MetricFormat;
use App\Enums\Metrics\MetricRange;
use App\Models\Call;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class TotalCallsController extends MetricController
{
    public function format(): MetricFormat
    {
        return MetricFormat::NUMBER;
    }

    public function calculate(MetricRange $range): Collection
    {
        $phones = Auth::user()->phones->pluck('id');

        return collect([
            'previous' => Call::whereBetween('call_start', $range->previous())
                ->whereIn('phone_id', $phones)
                ->count(),
            'current' => Call::whereBetween('call_start', $range->current())
                ->whereIn('phone_id', $phones)
                ->count(),
        ]);
    }
}
