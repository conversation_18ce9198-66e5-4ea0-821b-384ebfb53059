<?php

namespace App\Http\Controllers\Metrics;

use App\Enums\Metrics\MetricRange;
use App\Http\Controllers\Controller;

class RangeController extends Controller
{
    public function __invoke()
    {
        return response()->json([
            'data' => collect(MetricRange::cases())->mapWithKeys(fn ($range) => [
                $range->value => str($range->name)->replace('_', ' ')->title()->value(),
            ]),
        ]);
    }
}
