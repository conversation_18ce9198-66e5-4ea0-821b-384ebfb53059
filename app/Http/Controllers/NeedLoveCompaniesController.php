<?php

namespace App\Http\Controllers;

use App\Http\Controllers\API\APIController;
use App\Models\Odin\Company;
use App\Models\User;
use App\Services\NeedLoveCompaniesService;
use Illuminate\Http\JsonResponse;

class NeedLoveCompaniesController extends ApiController
{
    /**
     * @return JsonResponse
     */
    public function getNeedLoveCompanies(): JsonResponse
    {
        /** @var User $user */
        $user      = auth()->user();
        $companies = NeedLoveCompaniesService::getCompaniesNeedingTouchByUser($user);

        return $this->formatResponse($companies->map(function (Company $company) {
            return [
                'company_name' => $company->name,
                'profile_url'  => $company->getAdminProfileUrl(),
                'last_activity' => $company->last_activity,
                'probation_end_date' => $company->probation_end_date,
                'next_task_date' => $company->next_task_date,
            ];
        })->toArray());
    }
}
