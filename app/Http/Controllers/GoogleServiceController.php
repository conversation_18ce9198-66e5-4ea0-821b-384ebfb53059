<?php

namespace App\Http\Controllers;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Models\User;
use App\Services\GoogleServicesService;
use Exception;
use Illuminate\Http\Request;

class GoogleServiceController extends APIController
{

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected GoogleServicesService $googleServicesService
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * TODO - Add resource and mailbox
     * @return array
     * @throws Exception
     */
    public function listUserServiceIntegrations(): array
    {
        /** @var User $user */
        $user = auth()->user();

        return $this->googleServicesService->getServiceUrls($user);
    }
}
