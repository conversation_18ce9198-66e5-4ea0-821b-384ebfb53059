<?php

namespace App\Http\Controllers;

use App\Http\Controllers\API\APIController;
use App\Http\Resources\Billing\ListTagResource;
use App\Http\Resources\TagResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Spatie\Tags\Tag;

class TagController extends APIController
{
    const string FIELD_STATUS = 'status';
    const string FIELD_TAGS   = 'tags';

    /**
     * @return JsonResponse
     */
    public function getAll(): JsonResponse
    {
        return $this->formatResponse([
            self::FIELD_STATUS => true,
            self::FIELD_TAGS   => TagResource::collection(Tag::all()),
        ]);
    }

    /**
     * @return AnonymousResourceCollection
     */
    public function list()
    {
        return ListTagResource::collection(Tag::all()->unique('slug'));
    }
}
