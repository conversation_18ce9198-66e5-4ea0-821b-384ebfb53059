<?php

namespace App\Http\Controllers;

use App\DTO\Notes\CreateNoteParam;
use App\Enums\Notes\NoteRelationType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Notes\CreateNoteRequest;
use App\Http\Requests\Notes\GetNotesRequest;
use App\Http\Resources\Notes\NoteResource;
use App\Services\NoteService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class NoteController extends APIController
{
    const RESPONSE_STATUS   = 'status';
    const RESPONSE_NOTES    = 'notes';
    const RESPONSE_NOTE     = 'note';

    public function __construct(Request $request, JsonAPIResponseFactory $apiResponseFactory, protected NoteService $noteService)
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param GetNotesRequest $request
     * @return JsonResponse
     */
    public function getNotes(GetNotesRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $notes = $this->noteService->getNotesForEntity(
            Arr::get($validated, GetNotesRequest::FIELD_RELATION_ID),
            NoteRelationType::tryFrom(Arr::get($validated, GetNotesRequest::FIELD_RELATION_TYPE)),
        );

        return $this->formatResponse([
            self::RESPONSE_STATUS   => true,
            self::RESPONSE_NOTES    => NoteResource::collection($notes)
        ]);
    }

    /**
     * @param CreateNoteRequest $request
     * @return JsonResponse
     */
    public function createNote(CreateNoteRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $createNoteParam = new CreateNoteParam(
            Arr::get($validated, CreateNoteRequest::FIELD_CONTENT),
            Arr::get($validated, CreateNoteRequest::FIELD_RELATION_ID),
            NoteRelationType::tryFrom(Arr::get($validated, CreateNoteRequest::FIELD_RELATION_TYPE)),
            auth()->user()->id,
            Arr::get($validated, CreateNoteRequest::FIELD_PARENT_ID),
        );

        return $this->formatResponse([
            self::RESPONSE_STATUS   => true,
            self::RESPONSE_NOTE     => new NoteResource($this->noteService->createNote($createNoteParam))
        ]);
    }
}
