<?php

namespace App\Http\Controllers;

use App\Http\Controllers\DashboardAPI\ScheduleController;
use App\Models\Campaigns\CompanyCampaignSchedule;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\User;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class CompanyCampaignScheduleController extends ScheduleController
{
    const string RESPONSE_SHADOWING_USER = 'shadowing_user';

    public function getAllCompanySchedules(): JsonResponse
    {
        $company = $this->getCompany();
        $user = $this->getUser();

        return $this->formatResponse([
            self::RESPONSE_STATUS         => true,
            self::RESPONSE_SHADOWING_USER => $user,
            self::RESPONSE_SCHEDULES      => $this->appointmentCalendarIntegrationService->getAllCompanyCalendars($company->id, $user)
        ]);
    }

    public function deleteSchedule(): JsonResponse
    {
        $user = $this->getUser();
        $calendarId = $this->request->get(self::REQUEST_CALENDAR_ID);
        if (!$calendarId) throw new ModelNotFoundException("Could not update the requested Schedule.");

        $success = $this->appointmentCalendarIntegrationService->deleteCalendar($calendarId, $user);
        if ($success) {
            CompanyCampaignSchedule::query()
                ->where(CompanyCampaignSchedule::FIELD_SCHEDULE_ID, $calendarId)
                ->delete();
        }

        return $this->formatResponse([
            self::RESPONSE_STATUS => $success,
        ]);
    }

    protected function getCompany(bool $throwAuth = true): Company
    {
        $companyId = $this->request->route('companyId');

        /** @var Company */
        return Company::query()
            ->findOrFail($companyId);
    }

    protected function getUser(): ?CompanyUser
    {
        $userId = $this->request->route('userId');

        /** @var $user CompanyUser */
        $user = $userId > 0
            ? CompanyUser::query()->findOrFail($userId)
            : $this->getCompanyUserForCompany();

        return $user;
    }

    protected function getShadower(): ?User
    {
        /** @var User|null $user */
        $user = Auth::user();

        return $user;
    }

    /**
     * @return CompanyUser
     */
    private function getCompanyUserForCompany(): CompanyUser
    {
        $company = $this->getCompany();

        /** @var CompanyUser */
        return $company->users()
            ->where(CompanyUser::FIELD_CAN_LOG_IN, true)
            ->firstOrFail();
    }
}
