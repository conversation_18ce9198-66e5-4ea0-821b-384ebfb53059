<?php

namespace App\Http\Controllers\API;

use App\Enums\TaskCategory;
use App\Factories\JsonAPIResponseFactory;
use App\Models\Legacy\Location;
use App\Models\Sales\Task;
use App\Repositories\Legacy\CompanyRepository;
use App\Repositories\LocationRepository;
use App\Services\CompanyFilteringService;
use App\Services\NonPurchasingServiceAreaService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use InvalidArgumentException;
use Spatie\Permission\Models\Role;

class NonPurchasingServiceAreaController extends APIController
{
    const TASK_ID = 'task_id';

    const INDUSTRY = 'industry';

    const ZIP_CODE = 'zip_code';

    const COUNTY_KEY = 'county_key';

    const STATE_KEY = 'state_key';

    protected CompanyFilteringService $companyFilteringService;

    protected NonPurchasingServiceAreaService $service;

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
    ) {
        parent::__construct($request, $apiResponseFactory);
        $this->companyFilteringService = resolve(CompanyFilteringService::class, [
            'request' => $this->request,
        ]);
    }

    public function getCompanyCountInCounty(): JsonResponse
    {
        $industry = $this->request->get(self::INDUSTRY);
        $countyKey = $this->request->get(self::COUNTY_KEY);
        $stateKey = $this->request->get(self::STATE_KEY);

        return $this->formatResponse([
            'count' => $this->service->getCompanyCountInCounty($countyKey, $stateKey, $industry),
        ]);
    }

    public function getCompaniesInCountyByZipCode(
        LocationRepository $locationRepository,
        CompanyRepository $companyRepository,
        CompanyFilteringService $companyFilteringService
    ): JsonResponse {
        Role::findOrCreate('customer-success-manager');

        $data = $this->request->validate([
            self::ZIP_CODE => ['string', 'required'],
            self::INDUSTRY => ['string', 'required'],
            self::TASK_ID => ['nullable', 'numeric'],
        ]);

        if ($data[self::TASK_ID] ?? false) {
            $filterByAccountManager = Task::query()->find($data[self::TASK_ID])?->taskCategory?->name !== TaskCategory::CATEGORY_HUNTING->value;
        }

        $countyLocation = $locationRepository->getCountyFromZipcode($data[self::ZIP_CODE]);

        if ($countyLocation === null) {
            return $this->formatResponse([
                'companies' => collect(),
                'message' => 'County not found',
            ], 400);
        }

        if ($countyLocation instanceof Location) {
            try {
                $query = $companyRepository->getCompaniesAgainstNonPurchasingLocationQuery($countyLocation, $data[self::INDUSTRY], $filterByAccountManager ?? false);
            } catch (InvalidArgumentException $invalidArgumentException) {
                return $this->formatResponse([
                    'companies' => collect(),
                    'message' => $invalidArgumentException->getMessage(),
                ], 400);
            }
        }

        $requestVariables = $companyFilteringService->getAmountOfPurchasedLeadsFilterRequestVariables();

        $companies = $companyFilteringService->appendAttributes($query, $requestVariables, null, $this->request);

        return $this->formatResponse([
            'companies' => $companies,
            'message' => $this->getResponseMessage($companies),
        ]);
    }

    public function getCompaniesInCounty(): JsonResponse
    {
        $industry = $this->request->get(self::INDUSTRY);
        $countyKey = $this->request->get(self::COUNTY_KEY);
        $stateKey = $this->request->get(self::STATE_KEY);

        $collection = $this->service->getCompaniesInCounty($countyKey, $stateKey, $industry, true, $this->request);

        return $this->formatResponse([
            'companies' => $collection,
            'message' => $this->getResponseMessage($collection),
        ]);
    }

    private function getResponseMessage(Collection|LengthAwarePaginator $collection): string
    {
        if ($collection->isEmpty()) {
            return 'No companies found.';
        }

        return 'Companies retrieved successfully.';
    }
}
