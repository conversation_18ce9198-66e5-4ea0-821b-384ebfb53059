<?php

namespace App\Http\Controllers\API\Appointments;

use App\Enums\Odin\AppointmentCancellationReason;
use App\Enums\Odin\Industry;
use App\Enums\Odin\ProductAssignmentStatus;
use App\Enums\Odin\QualityTier;
use App\Enums\RejectionReasons;
use App\Exceptions\CustomMessageException;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\CancelAppointmentRequest;
use App\Http\Requests\RejectAppointmentRequest;
use App\Http\Requests\SearchAppointmentsRequest;
use App\Models\AppointmentDelivery;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyConfiguration;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAppointment;
use App\Models\Odin\ProductAssignment;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\CompanyRepository;
use App\Repositories\Odin\ProductCampaignRepository;
use App\Services\Odin\Appointments\AppointmentService;
use App\Services\Odin\Appointments\MultiIndustryAppointmentService;
use App\Transformers\API\AppointmentAPITransformer;
use App\Transformers\API\ProductCampaignAPITransformer;
use App\Transformers\ReferenceLists\StateTransformer;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Throwable;

class AppointmentController extends APIController
{
    const REQUEST_PER_PAGE = 'per_page';
    const REQUEST_APPOINTMENT_ID = 'appointment_id';
    const REQUEST_CAMPAIGN_ID = 'campaign_id';
    const REQUEST_STATUS = 'status';
    const REQUEST_INVOICE_ID = 'invoice_id';
    const REQUEST_CONTACT_SEARCH = 'contact_search';
    const REQUEST_ADDRESS_SEARCH = 'address_search';
    const REQUEST_STATE_ABBR = 'state_abbr';
    const REQUEST_CITY = 'city';
    const REQUEST_ZIP = 'zip';
    const REQUEST_START_DATE = 'start_date';
    const REQUEST_END_DATE = 'end_date';
    const REQUEST_APPOINTMENT_CATEGORY = 'appointment_category';

    const REQUEST_REJECTION_REASON = 'rejection_reason';
    const REQUEST_REJECTION_NOTES = 'rejection_notes';

    const REQUEST_CANCELLATION_REASON = 'cancellation_reason';
    const REQUEST_CANCELLATION_NOTE = 'cancellation_note';
    const REQUEST_RESCHEDULED_DATE = 'rescheduled_date';

    const REQUEST_APPOINTMENT_CODE = AppointmentService::APPOINTMENT_CODE;
    const REQUEST_APPOINTMENT_KEY = AppointmentService::APPOINTMENT_KEY;

    public function __construct(
        Request                                    $request,
        JsonAPIResponseFactory                     $apiResponseFactory,
        protected readonly CompanyRepository         $companyRepository,
        protected readonly AppointmentService        $appointmentService,
        protected readonly AppointmentAPITransformer $transformer,
        protected MultiIndustryAppointmentService $multiIndustryAppointmentService
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param int $legacyCompanyId
     * @param LocationRepository $locationRepository
     * @param ProductCampaignRepository $productCampaignRepository
     * @param ProductCampaignAPITransformer $productCampaignAPITransformer
     * @param StateTransformer $stateTransformer
     * @return JsonResponse
     */
    public function initSearchOptions(
        int $legacyCompanyId,
        LocationRepository $locationRepository,
        ProductCampaignRepository $productCampaignRepository,
        ProductCampaignAPITransformer $productCampaignAPITransformer,
        StateTransformer $stateTransformer
    ): JsonResponse
    {
        return $this->formatResponse([
            'status' => true,
            'states' => $stateTransformer->transformStates($locationRepository->getstates()),
            'appointment_statuses' => array_keys(ProductAssignmentStatus::all()),
            'campaigns' => $productCampaignAPITransformer->transformProductCampaigns(
                $productCampaignRepository->getCompanyCampaigns(
                    $this->companyRepository->findByLegacyIdOrFail($legacyCompanyId)->{Company::FIELD_ID}
                )
            )
        ]);
    }

    /**
     * @param int $legacyCompanyId
     * @param SearchAppointmentsRequest $searchAppointmentsRequest
     * @return JsonResponse
     * @throws Exception
     */
    public function search(int $legacyCompanyId, SearchAppointmentsRequest $searchAppointmentsRequest): JsonResponse
    {
        $data = $searchAppointmentsRequest->safe()->only([
            self::REQUEST_PER_PAGE,
            self::REQUEST_APPOINTMENT_ID,
            self::REQUEST_CAMPAIGN_ID,
            self::REQUEST_STATUS,
            self::REQUEST_INVOICE_ID,
            self::REQUEST_CONTACT_SEARCH,
            self::REQUEST_ADDRESS_SEARCH,
            self::REQUEST_STATE_ABBR,
            self::REQUEST_CITY,
            self::REQUEST_ZIP,
            self::REQUEST_START_DATE,
            self::REQUEST_END_DATE,
            self::REQUEST_APPOINTMENT_CATEGORY
        ]);

        $company = $this->companyRepository->findByLegacyIdOrFail($legacyCompanyId);

        $appointments = $this->appointmentService->getCompanyAppointmentsPaginated(
            $company->{Company::FIELD_ID},
            $data[self::REQUEST_APPOINTMENT_ID] ?? 0,
            $data[self::REQUEST_CAMPAIGN_ID] ?? 0,
            $data[self::REQUEST_STATUS] ?? 0,
            $data[self::REQUEST_INVOICE_ID] ?? 0,
            $data[self::REQUEST_CONTACT_SEARCH] ?? '',
            $data[self::REQUEST_ADDRESS_SEARCH] ?? '',
            $data[self::REQUEST_STATE_ABBR] ?? '',
            $data[self::REQUEST_CITY] ?? '',
            $data[self::REQUEST_ZIP] ?? '',
            $data[self::REQUEST_START_DATE] ?? 0,
            $data[self::REQUEST_END_DATE] ?? 0,
            QualityTier::tryFrom($data[self::REQUEST_APPOINTMENT_CATEGORY] ?? ''),
            $data[self::REQUEST_PER_PAGE] ?? 10
        );

        $totalSpend = $this->appointmentService->getTotalSpend(
            $company->{Company::FIELD_ID},
            $data[self::REQUEST_APPOINTMENT_ID] ?? 0,
            $data[self::REQUEST_CAMPAIGN_ID] ?? 0,
            ProductAssignmentStatus::SOLD->value,
            $data[self::REQUEST_INVOICE_ID] ?? 0,
            $data[self::REQUEST_CONTACT_SEARCH] ?? '',
            $data[self::REQUEST_ADDRESS_SEARCH] ?? '',
            $data[self::REQUEST_STATE_ABBR] ?? '',
            $data[self::REQUEST_CITY] ?? '',
            $data[self::REQUEST_ZIP] ?? '',
            $data[self::REQUEST_START_DATE] ?? 0,
            $data[self::REQUEST_END_DATE] ?? 0,
            QualityTier::tryFrom($data[self::REQUEST_APPOINTMENT_CATEGORY] ?? '')
        );

        $appointmentItems = $appointments->getCollection();
        $totalCost = $appointmentItems->sum(ProductAssignment::FIELD_COST);
        $appointments->setCollection(collect($this->transformer->transformAppointments($appointmentItems)));

        return $this->formatResponse([
            'status' => true,
            'appointments' => $appointments,
            'total_cost' => $totalCost,
            'total_spend' => $totalSpend
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getCancellationReasonOptions(): JsonResponse
    {
        return $this->formatResponse([
            "status" => true,
            "cancellation_reason_options" => AppointmentCancellationReason::displayNames()
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getConsumerAppointment(Request $request): JsonResponse
    {
        $apptKey = $request->get(self::REQUEST_APPOINTMENT_KEY);
        $apptCode = $request->get(self::REQUEST_APPOINTMENT_CODE);

        $apptDelivery = AppointmentDelivery::query()
            ->with([
                AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT.'.'.ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_APPOINTMENT,
                AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT.'.'.ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_ADDRESS,
                AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT.'.'.ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_CONSUMER,
                AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT.'.'.ProductAssignment::RELATION_COMPANY
            ])
            ->where(AppointmentDelivery::FIELD_CONSUMER_TOKEN, $apptKey)
            ->where(AppointmentDelivery::FIELD_CONSUMER_CODE, $apptCode)
            ->firstOrFail();

        $appointment = $apptDelivery->{AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT}->{ProductAssignment::RELATION_CONSUMER_PRODUCT}->{ConsumerProduct::RELATION_APPOINTMENT};

        /** @var Address $address */
        $address = $apptDelivery->{AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT}->{ProductAssignment::RELATION_CONSUMER_PRODUCT}->{ConsumerProduct::RELATION_ADDRESS};

        $tzOffset = (string) $address->{Address::FIELD_UTC};

        $appointmentDateTime = Carbon::createFromFormat('Y-m-d H:i:s', $appointment->{ProductAppointment::APPOINTMENT}, $tzOffset)->format("m/d/Y g:i A");

        $consumerFirstName = $apptDelivery->{AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT}->{ProductAssignment::RELATION_CONSUMER_PRODUCT}->{ConsumerProduct::RELATION_CONSUMER}->{Consumer::FIELD_FIRST_NAME};

        return $this->formatResponse([
            "status" => true,
            "consumer_first_name" => $consumerFirstName,
            "company_name" => $apptDelivery->{AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT}->{ProductAssignment::RELATION_COMPANY}->{Company::FIELD_NAME},
            "appointment_time" => $appointmentDateTime,
            "city_state_zip" => "{$address->{Address::FIELD_CITY}}, {$address->{Address::FIELD_STATE}} {$address->{Address::FIELD_ZIP_CODE}}",
            "appointment_type" => $appointment->{ProductAppointment::APPOINTMENT_TYPE},
            "is_rescheduled" => !empty($appointment->{ProductAppointment::ORIGINAL_APPOINTMENT_ID})
        ]);
    }

    /**
     * @param CancelAppointmentRequest $cancelAppointmentRequest
     * @return JsonResponse
     */
    public function cancelConsumerAppointment(
        CancelAppointmentRequest $cancelAppointmentRequest
    ): JsonResponse
    {
        $errMsg = 'There was an error';

        try {
            $data = $cancelAppointmentRequest->safe()->only([
                self::REQUEST_APPOINTMENT_KEY,
                self::REQUEST_APPOINTMENT_CODE,
                self::REQUEST_CANCELLATION_REASON,
                self::REQUEST_CANCELLATION_NOTE,
                self::REQUEST_RESCHEDULED_DATE
            ]);

            $appointmentDelivery = $this->appointmentService->getAppointmentDeliveryByKeyAndCode(
                $data[self::REQUEST_APPOINTMENT_KEY],
                $data[self::REQUEST_APPOINTMENT_CODE]
            );

            $productAssignmentId = $appointmentDelivery->{AppointmentDelivery::FIELD_PRODUCT_ASSIGNMENT_ID};
            $industry = $appointmentDelivery->productAssignment->consumerProduct->industryService->industry->name;

            if (in_array($industry, [Industry::SOLAR->value, Industry::ROOFING->value])) {
                $status = $this->appointmentService->cancelOrRescheduleConsumerAppointment(
                    $data[self::REQUEST_APPOINTMENT_KEY],
                    $data[self::REQUEST_APPOINTMENT_CODE],
                    AppointmentCancellationReason::from($data[self::REQUEST_CANCELLATION_REASON]),
                    $data[self::REQUEST_CANCELLATION_NOTE] ?? "",
                    $data[self::REQUEST_RESCHEDULED_DATE],
                );
            } else {
                $status = $this->multiIndustryAppointmentService->cancelOrRescheduleConsumerAppointment(
                    $data[self::REQUEST_APPOINTMENT_KEY],
                    $data[self::REQUEST_APPOINTMENT_CODE],
                    AppointmentCancellationReason::from($data[self::REQUEST_CANCELLATION_REASON]),
                    $data[self::REQUEST_CANCELLATION_NOTE] ?? "",
                    $data[self::REQUEST_RESCHEDULED_DATE],
                );
            }

            return $this->formatResponse([
                "status" => $status
            ]);
        }
        catch(Throwable $e) {
            if($e instanceof CustomMessageException) {
                $errMsg = $e->getMessage();
            }

            $errLocation = $e->getFile().'. Line: '.$e->getLine().'. ';

            AppointmentService::writeAppointmentLog(
                $errLocation."Cancel consumer appt err: ".substr($e->getMessage(), 0, 255),
                [
                    "product_assignment_id" => $productAssignmentId ?? 0,
                    "trace" => base64_encode(gzcompress($e->getTraceAsString(), 9))
                ]
            );

            logger()->error($e);
        }

        return $this->formatResponse([
            "status" => false,
            "error" => $errMsg
        ]);
    }

    /**
     * @param int $legacyCompanyId
     * @param int $productAssignmentId
     * @param RejectAppointmentRequest $rejectAppointmentRequest
     * @return JsonResponse
     */
    public function rejectAppointment(
        int $legacyCompanyId,
        int $productAssignmentId,
        RejectAppointmentRequest $rejectAppointmentRequest
    ): JsonResponse
    {
        $errMsg = '';

        try {
            $companyId = $this->companyRepository->findByLegacyIdOrFail($legacyCompanyId)->{Company::FIELD_ID};

            $data = $rejectAppointmentRequest->safe()->only([
                self::REQUEST_REJECTION_NOTES,
                self::REQUEST_REJECTION_REASON
            ]);

            return $this->formatResponse([
                "status" => $this->appointmentService->rejectAppointment(
                    $productAssignmentId,
                    $companyId,
                    RejectionReasons::from($data[self::REQUEST_REJECTION_REASON]),
                    $data[self::REQUEST_REJECTION_NOTES]
                )
            ]);
        }
        catch(Throwable $e) {
            DB::rollBack();

            if($e instanceof CustomMessageException) {
                $errMsg = $e->getMessage();
            }

            $errLocation = $e->getFile().'. Line: '.$e->getLine().'. ';

            AppointmentService::writeAppointmentLog(
                $errLocation."Reject appt err: ".substr($e->getMessage(), 0, 255),
                [
                    "company_id" => $companyId,
                    "product_assignment_id" => $productAssignmentId,
                    "trace" => base64_encode(gzcompress($e->getTraceAsString(), 9))
                ]
            );

            logger()->error($e);
        }

        return $this->formatResponse([
            "status" => false,
            "error" => $errMsg
        ]);
    }

    public function appointmentsActive(int $legacyCompanyId): JsonResponse
    {
        return $this->formatResponse([
            "status" => true,
            "appointments_active" => $this->companyRepository->findByLegacyIdOrFail($legacyCompanyId)->{Company::RELATION_CONFIGURATION}?->{CompanyConfiguration::FIELD_APPOINTMENTS_ACTIVE} ?? false
        ]);
    }

    public function hasSoldAppointments(int $legacyCompanyId): JsonResponse
    {
        return $this->formatResponse([
            "status" => true,
            "has_sold_appointments" => $this->appointmentService->hasSoldAppointments($this->companyRepository->findByLegacyIdOrFail($legacyCompanyId))
        ]);
    }
}
