<?php

namespace App\Http\Controllers\API;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Requests\ProfitabilitySimulatorRequest;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\Legacy\Location;
use App\Services\ProfitabilitySimulatorService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ProfitabilitySimulatorController extends APIController
{
    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param ProfitabilitySimulatorService $profitabilityReportService
     */
    public function __construct(
        Request                    $request,
        JsonAPIResponseFactory     $apiResponseFactory,
        protected ProfitabilitySimulatorService $profitabilityReportService
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    public function getSimulatedData(ProfitabilitySimulatorRequest $request): JsonResponse
    {
        set_time_limit(60);

        $data = $request->safe()->all();

        $data = $this->profitabilityReportService->getData($data);

        return $this->formatResponse($data);
    }

    public function getCampaignData(CompanyCampaign $campaign): JsonResponse
    {
        $zipCodeLocationIds = $campaign->locationModule->locations->pluck(CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID)->toArray();
        $locations          = Location::whereIn(Location::ID, $zipCodeLocationIds)
            ->get()
            ->each(function (Location $location) {
                $location->city_name = $location->city;
                $location->state_key = $location->state_abbr;
            })
            ->toArray();

        return $this->formatResponse([
            'locations' => $locations,
            'industry' => $campaign->service->industry->name
        ]);
    }
}
