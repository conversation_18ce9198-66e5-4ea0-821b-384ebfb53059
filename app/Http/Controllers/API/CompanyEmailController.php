<?php

namespace App\Http\Controllers\API;

use App\Jobs\Mailbox\ImportCompanyEmailsJob;
use Exception;
use Illuminate\Http\JsonResponse;

class CompanyEmailController extends APIController
{
    /**
     * @throws Exception
     */
    public function importEmails(int $company_id): JsonResponse
    {
        ImportCompanyEmailsJob::dispatch($company_id);

        return $this->formatResponse([
            'status' => true
        ]);
    }

}
