<?php

namespace App\Http\Controllers\API;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\CompanyOptInName;
use App\Repositories\Odin\CompanyRepository;
use App\Services\CompanyOptInNameService;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class OptInNameController extends APIController
{
    const string OPT_IN_NAMES = 'opt_in_names';
    const string ACTIVE = 'active';
    const string STATUS = 'status';

    /**
     * @param int $companyId
     * @param CompanyRepository $repository
     *
     * @return JsonResponse
     */
    public function getCompanyOptInNames(int $companyId, CompanyRepository $repository): JsonResponse
    {
        $company = $repository->findOrFail($companyId);

        return $this->formatResponse([
            self::STATUS => true,
            self::ACTIVE => CompanyOptInNameService::getOptInNameByCompany($company)?->name ?? $company->name,
            self::OPT_IN_NAMES => $company->optInNames->map(fn(CompanyOptInName $optInName) => $optInName->only(CompanyOptInName::FIELD_ID, CompanyOptInName::FIELD_NAME))
        ]);
    }

    /**
     * @param int $companyId
     * @param CompanyRepository $repository
     *
     * @return JsonResponse
     */
    public function deleteActiveOptInName(int $companyId, CompanyRepository $repository): JsonResponse
    {
        $company = $repository->findOrFail($companyId);
        $company->active_opt_in_name_id = null;

        return $this->formatResponse([
            self::STATUS => $company->save()
        ]);
    }



    /**
     * @param int $companyId
     * @param int $optInNameId
     * @param CompanyRepository $repository
     *
     * @return JsonResponse
     */
    public function setActiveOptIn(int $companyId, int $optInNameId, CompanyRepository $repository): JsonResponse
    {
        $company = $repository->findOrFail($companyId);
        /** @var CompanyOptInName $optInName */
        $optInName = $company->optInNames()->where(CompanyOptInName::FIELD_ID, $optInNameId)->firstOrFail();
        $company->active_opt_in_name_id = $optInName->id;

        return $this->formatResponse([
            self::STATUS => $company->save()
        ]);
    }

    /**
     * @param int $companyId
     * @param CompanyRepository $repository
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function updateOptInNames(int $companyId, CompanyRepository $repository): JsonResponse
    {
        $company = $repository->findOrFail($companyId);

        $this->validate($this->request, [
            self::OPT_IN_NAMES => 'required|array'
        ]);

        foreach ($this->request->get(self::OPT_IN_NAMES) as $optInName) {
            $repository->saveOptInName(
                company: $company,
                optInName: $optInName[CompanyOptInName::FIELD_NAME],
                optInNameId: $optInName[CompanyOptInName::FIELD_ID]
            );
        }

        return $this->formatResponse([
            self::STATUS => true
        ]);
    }

    /**
     * @param int $companyId
     * @param CompanyCampaign $companyCampaign
     * @param int $optInNameId
     * @param CompanyRepository $repository
     *
     * @return JsonResponse
     */
    public function setCampaignActiveOptIn(int $companyId, CompanyCampaign $companyCampaign, int $optInNameId, CompanyRepository $repository): JsonResponse
    {
        $company = $repository->findOrFail($companyId);
        /** @var CompanyOptInName $optInName */
        $optInName = $company->optInNames()->where(CompanyOptInName::FIELD_ID, $optInNameId)->firstOrFail();
        $companyCampaign->active_opt_in_name_id = $optInName->id;

        return $this->formatResponse([
            self::STATUS => $companyCampaign->save()
        ]);
    }

    /**
     * @param int $companyId
     * @param CompanyCampaign $companyCampaign
     *
     * @return JsonResponse
     */
    public function deleteCampaignActiveOptInName(int $companyId, CompanyCampaign $companyCampaign): JsonResponse
    {
        $companyCampaign->active_opt_in_name_id = null;
        return $this->formatResponse([
           self::STATUS => $companyCampaign->save()
        ]);
    }
}
