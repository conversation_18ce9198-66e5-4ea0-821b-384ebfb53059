<?php

namespace App\Http\Controllers\API;

use App\Builders\ActivityBuilder;
use App\Enums\ActivityType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Requests\GetCompanyActivitiesRequest;
use App\Http\Requests\GetCompanyActivityConversationRequest;
use App\Http\Requests\StoreActivityConversationRequest;
use App\Models\ActivityFeed;
use App\Models\Text;
use App\Repositories\ActivityConversationRepository;
use App\Repositories\ActivityFeedRepository;
use App\Repositories\Odin\CompanyRepository;
use App\Transformers\ActivityConversationTransformer;
use App\Transformers\ActivityTransformer;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;

class CompanyActivityController extends APIController
{
    const MODULE_PERMISSION = 'companies';

    const REQUEST_COMPANY_ID                                     = 'company_id';
    const REQUEST_USER_ID                                        = 'user_id';
    const REQUEST_PARENT_ID                                      = 'parent_id';
    const REQUEST_COMMENT                                        = 'comment';
    const REQUEST_ACTIVITY                                       = 'activity';
    const REQUEST_PERIOD_START_DATE                              = 'start_date';
    const REQUEST_PERIOD_END_DATE                                = 'end_date';
    const REQUEST_TYPE                                           = 'type';
    const REQUEST_SEARCH_QUERY                                   = 'query';
    const REQUEST_SORT_BY                                        = 'sort_by';
    const REQUEST_CADENCE                                        = 'cadence';

    const ACTIVITY_FEED_PER_PAGE = 10;

    const ITEM_FIRST_CREATED_AT = 'item_first_created_at';
    const ITEM_LAST_UPDATED_AT  = 'item_last_updated_at';

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param ActivityFeedRepository $activityFeedRepository
     * @param CompanyRepository $companyRepository
     * @param ActivityConversationRepository $activityConversationRepository
     */
    public function __construct(
        Request                                  $request,
        JsonAPIResponseFactory                   $apiResponseFactory,
        protected ActivityFeedRepository         $activityFeedRepository,
        protected ActivityConversationRepository $activityConversationRepository,
        protected CompanyRepository              $companyRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param GetCompanyActivitiesRequest $request
     * @param ActivityTransformer $transformer
     * @return JsonResponse
     */
    public function getActivities(GetCompanyActivitiesRequest $request, ActivityTransformer $transformer): JsonResponse
    {
        $filters = $request->safe()->collect();

        /** @var ActivityType $activityType */
        $activityType = ActivityType::tryFrom($filters->get(self::REQUEST_TYPE));

        if ($filters->get(self::REQUEST_TYPE) && !$activityType) throw new BadRequestException();

        /** @var ActivityBuilder|null $activitiesQuery */
        $activitiesBuilder = $this->activityFeedRepository->getActivityFeeds(
            companyId: $filters->get(self::REQUEST_COMPANY_ID),
            userId: $filters->get(self::REQUEST_USER_ID),
            searchQuery: $filters->get(self::REQUEST_SEARCH_QUERY),
            type: $activityType,
            startDate: $filters->get(self::REQUEST_PERIOD_START_DATE)
                ? Carbon::createFromFormat('Y-m-d', $filters->get(self::REQUEST_PERIOD_START_DATE))
                : null,
            endDate: $filters->get(self::REQUEST_PERIOD_END_DATE)
                ? Carbon::createFromFormat('Y-m-d', $filters->get(self::REQUEST_PERIOD_END_DATE))
                : null,
            sortOrder: $filters->get(self::REQUEST_SORT_BY),
            cadence: $filters->get(self::REQUEST_CADENCE),
        );

        $paginatedActivities =
            $activitiesBuilder
                ->selectRaw(sprintf(
                    "%s, %s",
                    "MIN(" . ActivityFeed::FIELD_CREATED_AT . ") AS " . self::ITEM_FIRST_CREATED_AT,
                    "MAX(" . ActivityFeed::FIELD_UPDATED_AT . ") AS " . self::ITEM_LAST_UPDATED_AT
                ))
                ->groupBy([
                    ActivityFeed::FIELD_ITEM_TYPE,
                    ActivityFeed::FIELD_ITEM_ID
                ])
                ->paginate(self::ACTIVITY_FEED_PER_PAGE)
                ->through(fn(ActivityFeed $activity) => $transformer->transformActivity($activity));

        return $this->formatResponse([
            'status'     => true,
            'activities' => $paginatedActivities,
            'total'      => $activitiesBuilder->count(),
        ]);
    }

    /**
     * @param StoreActivityConversationRequest $request
     * @return JsonResponse
     */
    public function addActivityConversation(StoreActivityConversationRequest $request): JsonResponse
    {
        $requestData = $request->safe()->collect();

        return $this->formatResponse([
            'status' => !!$this->activityConversationRepository->addActivityConversation(
                $requestData->get(self::REQUEST_ACTIVITY),
                $requestData->get(self::REQUEST_PARENT_ID) ?? 0,
                Auth::id(),
                $requestData->get(self::REQUEST_COMMENT)
            )
        ]);
    }

    /**
     * @param GetCompanyActivityConversationRequest $request
     * @param ActivityConversationTransformer $transformer
     * @return JsonResponse
     */
    public function getActivityConversations(
        GetCompanyActivityConversationRequest $request,
        ActivityConversationTransformer       $transformer
    ): JsonResponse
    {
        $requestData = $request->safe()->collect();

        /** @var ActivityFeed|null $activity */
        $activity = $this->activityFeedRepository->findActivityByCompanyIdAndActivityId(
            $requestData->get(self::REQUEST_COMPANY_ID),
            $requestData->get(self::REQUEST_ACTIVITY)
        );

        if (!$activity) throw new BadRequestException("The system couldn't find the requested combination of company and activity IDs.");

        return $this->formatResponse([
            'status'        => true,
            'conversations' => $transformer->transformConversations(
                $this->activityConversationRepository->getActivityConversations($activity->{ActivityFeed::FIELD_ID})
            )
        ]);
    }

    /**
     * @param GetCompanyActivitiesRequest $request
     * @return JsonResponse
     */
    public function getActivitiesOverview(GetCompanyActivitiesRequest $request): JsonResponse
    {
        $requestData = $request->safe()->collect();

        return $this->formatResponse([
            'status'   => true,
            'overview' => $this->activityFeedRepository->getActivityFeedOverview(
                companyId: $requestData->get(self::REQUEST_COMPANY_ID),
                userId: $requestData->get(self::REQUEST_USER_ID) ?? null
            )
        ]);
    }

    /**
     * Get SMS history for given number, for Activity Feed display
     * @param int $companyId
     * @param string $otherNumber
     * @return JsonResponse
     */
    public function getSmsHistoryForActivityFeed(int $companyId, string $otherNumber): JsonResponse
    {
        $messages = Text::query()
            ->with('phone')
            ->where(Text::FIELD_OTHER_NUMBER, $otherNumber)
            ->oldest()
            ->get();

        return $this->formatResponse([
            'number'   => $otherNumber,
            'status'   => true,
            'messages' => $messages,
        ]);

    }
}

