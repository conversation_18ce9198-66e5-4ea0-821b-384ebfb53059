<?php

namespace App\Http\Controllers\API;

use App\Http\Resources\CompanyManagerAssignmentRequestResource;
use App\Models\CompanyManagerAssignmentRequest as CompanyManagerAssignmentRequestModel;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Spatie\Permission\Models\Role;

/**
 * @see resources/js/vue/components/SalesManagement/services/api/ownership-api.js
 */
class CompanyManagerAssignmentRequestController extends APIController
{
    public function getPendingRequests(): JsonResponse
    {
        $query = CompanyManagerAssignmentRequestModel::query()->where(CompanyManagerAssignmentRequestModel::FIELD_STATUS,
            'pending');

        $query->update([CompanyManagerAssignmentRequestModel::FIELD_IS_UNREAD => false]);

        return $this->formatResponse(['requests' => CompanyManagerAssignmentRequestResource::collection($query->get())]);
    }

    public function getRequestHistory(): JsonResponse
    {
        $query = CompanyManagerAssignmentRequestModel::query()->where(CompanyManagerAssignmentRequestModel::FIELD_STATUS,
            '<>', 'pending')->orderByDesc('created_at');

        return $this->formatResponse(['requests' => CompanyManagerAssignmentRequestResource::collection($query->get())]);
    }

    public function approve(CompanyManagerAssignmentRequestModel $request): CompanyManagerAssignmentRequestResource
    {
        $request->company->assign($request->user)->as($request->role->name);

        $request->update([
            CompanyManagerAssignmentRequestModel::FIELD_STATUS => 'approved',
            CompanyManagerAssignmentRequestModel::FIELD_IS_UNREAD => false,
            'deciding_user_id' => auth()->id(),
            'decided_at' => Carbon::now()
        ]);

        return CompanyManagerAssignmentRequestResource::make($request->refresh());
    }

    public function deny(CompanyManagerAssignmentRequestModel $request): CompanyManagerAssignmentRequestResource
    {
        $request->update([
            CompanyManagerAssignmentRequestModel::FIELD_STATUS => 'denied',
            CompanyManagerAssignmentRequestModel::FIELD_IS_UNREAD => false,
            'deciding_user_id' => auth()->id(),
            'decided_at' => Carbon::now()
        ]);

        return CompanyManagerAssignmentRequestResource::make($request->refresh());
    }

    public function getMyRequests(): JsonResponse
    {
        $requests = CompanyManagerAssignmentRequestResource::collection(
            CompanyManagerAssignmentRequestModel::query()->where(CompanyManagerAssignmentRequestModel::FIELD_USER_ID,
                auth()->id())->get(),
        );

        return $this->formatResponse(compact('requests'));
    }

    public function requestAssignment(Request $request): CompanyManagerAssignmentRequestResource
    {
        $request->validate([
            'company_id' => 'required|integer|exists:companies,id,deleted_at,NULL',
            'role' => 'required|string|in:account-manager,business-development-manager,customer-success-manager,onboarding-manager,sales-development-representative|exists:roles,name',
        ]);

        $companyManagerAssignmentRequest = CompanyManagerAssignmentRequestModel::create([
            CompanyManagerAssignmentRequestModel::FIELD_USER_ID => auth()->id(),
            CompanyManagerAssignmentRequestModel::FIELD_COMPANY_ID => $request->get('company_id'),
            CompanyManagerAssignmentRequestModel::FIELD_ROLE_ID => Role::findByName($request->get('role'))->id,
        ]);

        return CompanyManagerAssignmentRequestResource::make($companyManagerAssignmentRequest->refresh());
    }
}
