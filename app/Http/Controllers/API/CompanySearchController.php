<?php

namespace App\Http\Controllers\API;

use App\Builders\Odin\Company\CompanySearchBuilder;
use App\Builders\Odin\CompanyBuilder;
use App\Builders\Odin\ConsumerSearchBuilder;
use App\Constants\Company\Search\FilterRequestParams;
use App\Enums\UserPresetType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Requests\Odin\CompanySearchRequest;
use App\Http\Requests\Odin\ConsumerSearchRequest;
use App\Http\Requests\SearchCompaniesByNameOrIdRequest;
use App\Http\Requests\SearchCompaniesRequest;
use App\Http\Requests\StorePresetFilterRequest;
use App\Http\Requests\Workflows\WorkflowTaskRequest;
use App\Http\Resources\Odin\CompanySearchResource;
use App\Http\Resources\Odin\ConsumerSearchResource;
use App\Http\Resources\UserPresetFilterResource;
use App\Jobs\Tasks\CreateTasksJob;
use App\Models\ActivityFeed;
use App\Models\Cadence\CadenceRoutine;
use App\Models\Cadence\CompanyCadenceRoutine;
use App\Models\CompanyMetric;
use App\Models\ComputedRejectionStatistic;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\CompanyReview;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Product;
use App\Models\Sales\Task;
use App\Models\User;
use App\Models\UserPreset;
use App\Repositories\Legacy\CompanyCRMRepository;
use App\Repositories\Legacy\CompanyRepository as LegacyCompanyRepository;
use App\Repositories\Legacy\LegacyUserRepository;
use App\Repositories\Legacy\QuoteCompanyRepository;
use App\Repositories\Odin\CompanyRepository;
use App\Repositories\UserPresetRepository;
use App\Services\Filterables\Company\CompanyFilterableService;
use App\Services\Filterables\Consumer\ConsumerFilterableService;
use App\Services\Search\CompanySearchService as OtherCompanySearchService;
use App\Services\Sortable\Company\CompanySortableService;
use App\Services\UserPresetService;
use App\Transformers\Odin\CompanySearchTransformer;
use App\Transformers\Odin\CompanyTransformer;
use App\Transformers\Search\CompanySearchAutoselectTransformer;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\PaginatedResourceResponse;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class CompanySearchController extends APIController
{
    const REQUEST_INDUSTRY_ID        = 'industry_id';

    const CAMPAIGN_STATUS_ACTIVE          = "active";
    const CAMPAIGN_STATUS_ONE_PAUSED      = "one_paused";
    const CAMPAIGN_STATUS_PAUSED          = "paused";
    const CAMPAIGN_STATUS_OVER_BUDGET     = "over_budget";
    const CAMPAIGN_STATUS_ONE_OVER_BUDGET = "one_over_budget";
    const RESPONSE_FILTER_UPDATES = 'filter_updates';

    const CAMPAIGN_STATUS_NAMES = [
        self::CAMPAIGN_STATUS_ACTIVE          => "Active",
        self::CAMPAIGN_STATUS_ONE_PAUSED      => "At Least One Campaign Paused",
        self::CAMPAIGN_STATUS_PAUSED          => "All Campaigns Paused",
        self::CAMPAIGN_STATUS_OVER_BUDGET     => "All Campaigns Over Budget",
        self::CAMPAIGN_STATUS_ONE_OVER_BUDGET => "At Least One Campaign Over Budget"
    ];

    const REQUEST_NAME_TYPE = 'nameType';
    const REQUEST_QUERY = 'query';

    const REQUEST_NAME = 'name';
    const REQUEST_PRESET_VALUE      = 'value';
    const REQUEST_FILTERS = 'filters';

    const REQUEST_SEARCH_BY_TEXT = 'search_text';
    const REQUEST_SEARCH_BY_CONTACT = 'search_contact';

    const REQUEST_PER_PAGE = 'per_page';
    const REQUEST_PAGE = 'page';
    const REQUEST_SORT_ARRAY = 'sort_array';
    const DEFAULT_PER_PAGE = 10;

    const REQUEST_RETURN_FULL_QUERY = 'return_all_results';

    const RESPONSE_STATUS         = 'status';
    const RESPONSE_FILTER_OPTIONS = 'filter_options';
    const RESPONSE_PRESETS        = 'presets';
    const RESPONSE_COMPANIES      = 'companies';

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param CompanyRepository $companyRepository
     * @param LegacyUserRepository $legacyUserRepository
     * @param LegacyCompanyRepository $legacyCompanyRepository
     * @param CompanySearchTransformer $companySearchTransformer
     * @param CompanyFilterableService $companyFilterableService
     * @param UserPresetRepository $userPresetRepository
     * @param UserPresetService $userPresetService
     * @param QuoteCompanyRepository $quoteCompanyRepository
     * @param CompanyCRMRepository $companyCRMRepository
     */
    public function __construct(
        protected Request                  $request,
        protected JsonApiResponseFactory   $apiResponseFactory,
        public CompanyRepository           $companyRepository,
        public LegacyUserRepository        $legacyUserRepository,
        public LegacyCompanyRepository     $legacyCompanyRepository,
        protected CompanySearchTransformer $companySearchTransformer,
        protected CompanyFilterableService $companyFilterableService,
        protected UserPresetRepository     $userPresetRepository,
        protected UserPresetService        $userPresetService,
        protected QuoteCompanyRepository   $quoteCompanyRepository,
        protected CompanyCRMRepository     $companyCRMRepository
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @return ResourceCollection
     */
    public function getCompanyPresetFilters(): ResourceCollection
    {
        /** @var User $user */
        $user = Auth::user();
        $presets = $this->userPresetRepository
            ->getPresetsByTypeAndCategory(UserPresetType::FILTER, CompanyFilterableService::FILTERABLE_CATEGORY, $user->id)
            ->map(function (UserPreset $preset) {
                $filters = $preset->value;
                return $preset;
            });

        return UserPresetFilterResource::collection($presets);
    }

    public function getFilterOptions(): JsonResponse
    {
        $options = $this->companyFilterableService->getDisplayData();

        return $this->formatResponse([
            self::RESPONSE_STATUS           => !!$options,
            self::RESPONSE_FILTER_OPTIONS   => $options,
            self::RESPONSE_PRESETS          => $this->getCompanyPresetFilters(),
        ]);
    }

    public function search(CompanySearchRequest $request): JsonResponse
    {
        $perPage         = $request->get(self::REQUEST_PER_PAGE, self::DEFAULT_PER_PAGE);
        $page            = $request->get(self::REQUEST_PAGE, 1);
        $returnFullQuery = $request->get(self::REQUEST_RETURN_FULL_QUERY);

        $query = $this->getCompanySearchQuery($request);

        $query->with($this->getCompanySearchRelationsArray());

        $products = Product::get();

        $this->companySearchTransformer->setProducts($products);

        // for CSV download of full query results, this will not update front end display
        if ($returnFullQuery) return $this->formatResponse([
            self::RESPONSE_STATUS    => true,
            self::RESPONSE_COMPANIES => $this->getResponseCompaniesForSearchCSVExport($query->get())
        ]);

        $paginated = $query->paginate($perPage, ['*'], 'page', $page);

        return $this->formatResponse([
            self::RESPONSE_STATUS    => true,
            self::RESPONSE_COMPANIES => $this->parsePaginatedCompaniesForSearchResponse($paginated, $request)
        ]);
    }

    public function savePreset(StorePresetFilterRequest $request): JsonResponse
    {
        $presetValue = $request->get(self::REQUEST_PRESET_VALUE);
        $name = $request->get('name', 'User Filter');

        /** @var User $user */
        $user = Auth::user();
        $success = !!$this->userPresetRepository->savePreset(
            $user->id,
            UserPresetType::FILTER,
            $name,
            $presetValue,
            CompanyFilterableService::FILTERABLE_CATEGORY,
        );

        return $this->formatResponse([
            self::RESPONSE_STATUS => $success,
            self::RESPONSE_PRESETS => $this->getCompanyPresetFilters(),
        ]);
    }

    public function deletePreset(): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();
        $name = $this->request->get(self::REQUEST_NAME);

        $deleted = $this->userPresetRepository->deletePreset($user->id, UserPresetType::FILTER, $name,
            CompanyFilterableService::FILTERABLE_CATEGORY);

        return $this->formatResponse([
            self::RESPONSE_STATUS => $deleted,
            self::RESPONSE_PRESETS => $this->getCompanyPresetFilters(),
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getSearchOptions(): JsonResponse
    {
        session_write_close();

        return $this->formatResponse([
            "status" => true,
            "options" => $this->companyRepository->getCompaniesSearchOptions()
        ]);
    }

    /**
     * Returns a list of companies queried by companyId or companyName
     *
     * @param SearchCompaniesByNameOrIdRequest $request
     * @param OtherCompanySearchService $companySearchService
     * @param CompanySearchAutoselectTransformer $transformer
     * @return JsonResponse $companies
     */
    public function searchCompaniesByNameOrId(
        SearchCompaniesByNameOrIdRequest $request,
        OtherCompanySearchService $companySearchService,
        CompanySearchAutoselectTransformer $transformer
    ): JsonResponse {
        session_write_close();
        $query = $request->safe()->only(self::REQUEST_QUERY);
        $companies = $transformer->transform($companySearchService->search(implode($query)));
        return $this->formatResponse([
            'status' => true,
            'companies' => $companies
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getCompanyNames(): JsonResponse
    {
        $nameType = $this->request->input(self::REQUEST_NAME_TYPE, '');
        $searchQuery = $this->request->input(self::REQUEST_QUERY, '');

        if (empty($nameType)
            || empty($searchQuery)
            || !in_array($nameType, [Company::FIELD_ENTITY_NAME, Company::FIELD_NAME])) {
            return $this->formatResponse([
                "status" => false
            ]);
        }

        $companyFieldToQuery = $nameType === Company::FIELD_ENTITY_NAME
            ? Company::FIELD_ENTITY_NAME
            : Company::FIELD_NAME;

        $companies = Company::selectRaw(implode(',', [
            Company::FIELD_STATUS,
            Company::FIELD_ID,
            Company::FIELD_NAME,
            Company::FIELD_ENTITY_NAME
        ]))
            ->where($companyFieldToQuery, 'LIKE', "%{$searchQuery}%")
            ->orderBy($companyFieldToQuery)
            ->take(20)
            ->get();

        $returnCompanies = [];
        /** @var Company $company */
        foreach($companies as $company) {
            $returnCompanies[] = [
                'id'          => $company->{Company::FIELD_ID},
                'name'        => "{$company->{Company::FIELD_ID}}: {$company->{$companyFieldToQuery}}",
                'companyName' => $company->{$companyFieldToQuery}
            ];
        }

        return $this->formatResponse([
            "status" => true,
            "companies" => $returnCompanies
        ]);
    }

    /**
     * @param CompanyTransformer $transformer
     *
     * @return JsonResponse
     */
    public function getAdminCompanies(CompanyTransformer $transformer): JsonResponse
    {
        $companyIdOrName = $this->request->get(FilterRequestParams::REQUEST_COMPANY_NAME);

        $query = CompanyBuilder::query()->forCompanyName($companyIdOrName);

        if ($this->request->get(self::REQUEST_INDUSTRY_ID)) {
            $query->forIndustryId($this->request->get(self::REQUEST_INDUSTRY_ID));
        }

        return $this->formatResponse([
            'status'    => true,
            'companies' => $transformer->transformCompanies($query->get())
        ]);
    }

    public function getCampaignStatuses(): JsonResponse
    {
        return $this->formatResponse([
            'status' => true,
            'campaign_statuses' => self::CAMPAIGN_STATUS_NAMES
        ]);
    }

    public function getPaymentMethods(): JsonResponse
    {
        return $this->formatResponse(([
            'status' => true,
            'payment_methods' => EloquentCompany::PAYMENT_SOURCES
        ]));
    }

    /**
     * @param CompanySearchRequest $request
     * @param WorkflowTaskRequest $taskRequest
     *
     * @return JsonResponse
     */
    public function createTasksFromCompanySearch(CompanySearchRequest $request, WorkflowTaskRequest $taskRequest): JsonResponse
    {
        $taskRequestData = $taskRequest->safe()->collect();
        $allCompaniesRequested = $request->get('company_data', [])['all'] ?? false;

        if ($allCompaniesRequested === true) {
            $query = $this->getCompanySearchQuery($request);
            $companyIds = $query
                ->select(Company::TABLE . '.' . Company::FIELD_ID)
                ->get()
                ->pluck(Company::FIELD_ID)
                ->unique()
                ->toArray();
        } else {
            $companyIds = $this->getCompanyIds(collect(Arr::get($this->request->get('company_data', []), 'data.companies', [])));
        }

        if (count($companyIds) > 0) {
            CreateTasksJob::dispatch(
                Auth::id(),
                $taskRequestData->get(Task::FIELD_SUBJECT),
                $taskRequestData->get(Task::FIELD_TASK_TYPE_ID),
                $taskRequestData->get(Task::FIELD_PRIORITY),
                $taskRequestData->get(Task::FIELD_TASK_CATEGORY_ID),
                $taskRequestData->get(Task::FIELD_AVAILABLE_AT),
                $companyIds
            );
        }

        return $this->formatResponse([
            'status' => true
        ]);
    }

    /**
     * @param CompanySearchRequest $request
     *
     * @return Builder
     */
    protected function getCompanySearchQuery(CompanySearchRequest $request): Builder
    {
        $filterOptions = $request->get(self::REQUEST_FILTERS);
        $searchText = $request->get(self::REQUEST_SEARCH_BY_TEXT);
        $searchContact = $request->get(self::REQUEST_SEARCH_BY_CONTACT);

        $sortArray = $request->get(self::REQUEST_SORT_ARRAY, []);

        $query = CompanySearchBuilder::query()
            ->searchText($searchText)
            ->searchContact($searchContact)
            ->getQuery();

        $this->companyFilterableService->runQuery($filterOptions, $query);

        if (!empty($sortArray)) {
            $companySortableService = new CompanySortableService($sortArray);

            $query = $companySortableService->getQuery($query);
        }

        return $query;
    }

    public function getFilterOptionUpdates(ConsumerSearchRequest $request): JsonResponse
    {
        $filterOptions = $request->get(self::REQUEST_FILTERS);

        $this->companyFilterableService->runQuery($filterOptions);
        $filterUpdates = $this->companyFilterableService->getFilterOptionUpdates();

        return $this->formatResponse([
            self::RESPONSE_STATUS         => true,
            self::RESPONSE_FILTER_UPDATES => $filterUpdates ?? null
        ]);
    }

    private function getCompanySearchRelationsArray(): array
    {
        return [
            Company::RELATION_REVIEWS => function ($with) {
                $with->select([
                      CompanyReview::FIELD_COMPANY_ID
                  ]);
            },
            Company::RELATION_SERVICES,
            Company::RELATION_INDUSTRIES,
            Company::RELATION_LOCATIONS => function ($with) {
                $with->select([
                      CompanyLocation::FIELD_COMPANY_ID,
                      CompanyLocation::FIELD_ADDRESS_ID
                  ]);
            },
            Company::RELATION_LOCATIONS.'.'.CompanyLocation::RELATION_ADDRESS => function ($with) {
                $with->select([
                      Address::FIELD_ID,
                      Address::FIELD_ADDRESS_1,
                      Address::FIELD_ADDRESS_2,
                      Address::FIELD_CITY,
                      Address::FIELD_STATE,
                      Address::FIELD_ZIP_CODE
                  ]);
            },
            Company::RELATION_CADENCE_ROUTINES => function ($with) {
                $with->select([
                      CompanyCadenceRoutine::FIELD_COMPANY_ID,
                      CompanyCadenceRoutine::FIELD_CADENCE_ROUTINE_ID
                  ]);
            },
            Company::RELATION_CADENCE_ROUTINES.'.'.CompanyCadenceRoutine::RELATION_CADENCE_ROUTINE => function ($with) {
                $with->select([
                      CadenceRoutine::FIELD_ID,
                      CadenceRoutine::FIELD_NAME
                  ]);
            },
            Company::RELATION_CAMPAIGNS => function ($with) {
                $with->select([
                      LeadCampaign::COMPANY_ID,
                      LeadCampaign::STATUS
                  ]);
            },
            Company::RELATION_DATA
        ];
    }

    /**
     * @param Collection $items
     * @return AnonymousResourceCollection
     */
    private function getResponseCompaniesForSearchCSVExport(
        Collection                     $items
    ): AnonymousResourceCollection
    {
        [
            $companiesLastUpdated,
            $deliveredChargeableAndBudgetQuoteCompanies,
            $mostRecentQuoteCompaniesByCompanyIds,
            $computedRejectionPercentageStatistics,
            $activityFeeds,
            $companyMetrics
        ] = $this->getCommonLogicForCompanySearchAndCsvExportResponse($items);

        $this->companySearchTransformer->setCompaniesLastUpdated($companiesLastUpdated);
        $this->companySearchTransformer->setDeliveredChargeableAndBudgetQuoteCompanies($deliveredChargeableAndBudgetQuoteCompanies);
        $this->companySearchTransformer->setMostRecentQuoteCompaniesByCompanyIds($mostRecentQuoteCompaniesByCompanyIds);
        $this->companySearchTransformer->setComputedRejectionPercentageStatistics($computedRejectionPercentageStatistics);
        $this->companySearchTransformer->setActivityFeeds($activityFeeds);
        $this->companySearchTransformer->setCompanyMetrics($companyMetrics);

        $companies = collect();

        foreach ($items as $item) {
            $item = $this->companySearchTransformer->transform(
                $item,
            );

            $companies->push($item);
        }

        return CompanySearchResource::collection($companies);
    }

    /**
     * @param Collection|\Illuminate\Support\Collection $items
     * @return array
     */
    private function getCommonLogicForCompanySearchAndCsvExportResponse(Collection|\Illuminate\Support\Collection $items)
    {
        $companyIds = $this->getCompanyIds($items);

        $companyLegacyIds = $this->getLegacyCompanyIds($items);

        $companiesLastUpdated = collect($this->companyCRMRepository->getCompaniesLastUpdated($companyLegacyIds));

        $mostRecentQuoteCompaniesByCompanyIds = $this->quoteCompanyRepository->getMostRecentQuoteCompaniesByCompanyIds($companyLegacyIds);

        $deliveredChargeableAndBudgetQuoteCompanies = [];

        if(!isset($items->first()->lead_cost_one)) {
            $deliveredChargeableAndBudgetQuoteCompanies = $this->quoteCompanyRepository->getRevenueForCompanies($companyLegacyIds, Carbon::today()->subDays(29)->timestamp);
        }

        $computedRejectionPercentageStatistics = ComputedRejectionStatistic::query()
            ->whereIn(ComputedRejectionStatistic::FIELD_COMPANY_ID, $companyIds)
            ->get();

        $activityFeeds = ActivityFeed::query()
            ->whereIn(ActivityFeed::FIELD_COMPANY_ID, $companyIds)
            ->get();

        $companyMetrics = CompanyMetric::query()
            ->whereIn(CompanyMetric::FIELD_COMPANY_ID, $companyIds)
            ->get();

        return [
            $companiesLastUpdated,
            $deliveredChargeableAndBudgetQuoteCompanies,
            $mostRecentQuoteCompaniesByCompanyIds,
            $computedRejectionPercentageStatistics,
            $activityFeeds,
            $companyMetrics
        ];
    }

    /**
     * @param LengthAwarePaginator $paginated
     * @param CompanySearchRequest $request
     * @return mixed
     */
    private function parsePaginatedCompaniesForSearchResponse(
        LengthAwarePaginator           $paginated,
        CompanySearchRequest           $request
    ): array
    {
        $items = $paginated->items();

        $items = collect($items);

        [
            $companiesLastUpdated,
            $deliveredChargeableAndBudgetQuoteCompanies,
            $mostRecentQuoteCompaniesByCompanyIds,
            $computedRejectionPercentageStatistics,
            $activityFeeds,
            $companyMetrics
        ] = $this->getCommonLogicForCompanySearchAndCsvExportResponse($items);

        $this->companySearchTransformer->setCompaniesLastUpdated($companiesLastUpdated);
        $this->companySearchTransformer->setDeliveredChargeableAndBudgetQuoteCompanies($deliveredChargeableAndBudgetQuoteCompanies);
        $this->companySearchTransformer->setMostRecentQuoteCompaniesByCompanyIds($mostRecentQuoteCompaniesByCompanyIds);
        $this->companySearchTransformer->setComputedRejectionPercentageStatistics($computedRejectionPercentageStatistics);
        $this->companySearchTransformer->setActivityFeeds($activityFeeds);
        $this->companySearchTransformer->setCompanyMetrics($companyMetrics);

        $result = $paginated->through(function ($item) {
            return $this->companySearchTransformer->transform(
                $item,
            );
        });

        $resourceCollection = CompanySearchResource::collection($result);

        $response = new PaginatedResourceResponse($resourceCollection);

        return json_decode($response->toResponse($request)->content(), true);
    }

    /**
     * @param Collection|\Illuminate\Support\Collection $items
     * @return array
     */
    private function getCompanyIds(Collection|\Illuminate\Support\Collection $items): array
    {
        return $items->pluck(Company::FIELD_ID)->toArray();
    }

    /**
     * @param Collection|\Illuminate\Support\Collection $items
     * @return array
     */
    private function getLegacyCompanyIds(Collection|\Illuminate\Support\Collection $items): array
    {
        return $items->pluck(Company::FIELD_LEGACY_ID)->toArray();
    }
}
