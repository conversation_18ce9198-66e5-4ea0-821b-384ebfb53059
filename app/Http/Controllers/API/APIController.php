<?php

namespace App\Http\Controllers\API;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\UnauthorizedException;

class APIController extends Controller
{
    /** @var JsonAPIResponseFactory $apiResponseFactory */
    protected JsonAPIResponseFactory $apiResponseFactory;

    /** @var Request $request */
    protected Request $request;

    public function __construct(Request $request, JsonAPIResponseFactory $apiResponseFactory)
    {
        $this->apiResponseFactory = $apiResponseFactory;
        $this->request            = $request;
    }

    /**
     * <PERSON>les formatting the data as an accepted JsonResponse.
     *
     * @param array $data
     * @return JsonResponse
     */
    protected function formatResponse(array $data = [], int $statusCode = 200): JsonResponse
    {
        return response()->json(["data" => $data], $statusCode);
    }

    /**
     * Determines if the user is authorized to access a module or has permissions for that.
     *
     * @param string $permission
     * @return void
     */
    protected function performAuthorizationCheck(string $permission): void
    {
        /** @var User $user */
        $user = Auth::user();
        if(!$user->hasPermissionTo($permission))
            throw new UnauthorizedException("The request made is unauthorized.");
    }
}
