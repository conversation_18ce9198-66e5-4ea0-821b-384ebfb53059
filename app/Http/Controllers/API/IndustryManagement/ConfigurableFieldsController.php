<?php

namespace App\Http\Controllers\API\IndustryManagement;

use App\DataModels\Odin\CompanyUserFieldDataModel;
use App\Enums\Odin\CompanyConfigurableFieldCategory;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\ConfigurableFields\ListConfigurableFieldsRequest;
use App\Http\Requests\ConfigurableFields\SaveConfigurableFieldsRequest;
use App\Http\Resources\Odin\ConsumerConfigurableFieldCategoryResource;
use App\Models\Odin\CompanyUserField;
use App\Models\Odin\ConfigurableFieldType;
use App\Services\Odin\ConfigurableFieldsService;
use App\Transformers\Odin\CompanyUserDataTransformer;
use Exception;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\JsonResponse;

class ConfigurableFieldsController extends APIController
{
    const REQUEST_CATEGORY = 'category';
    const REQUEST_TYPE = 'type';
    const REQUEST_CATEGORY_ID = 'category_id';
    const REQUEST_FIELDS = 'fields';

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        private ConfigurableFieldsService $configurableFieldsService
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param ListConfigurableFieldsRequest $listConfigurableFieldsRequest
     * @return JsonResponse
     * @throws Exception
     */
    public function list(ListConfigurableFieldsRequest $listConfigurableFieldsRequest): JsonResponse
    {
        $data = $listConfigurableFieldsRequest->safe()->only([
            self::REQUEST_TYPE,
            self::REQUEST_CATEGORY,
            self::REQUEST_CATEGORY_ID
        ]);

        return $this->formatResponse([
            'status' => true,
            'fields' => $this->configurableFieldsService->getFields(
                $data[self::REQUEST_TYPE],
                $data[self::REQUEST_CATEGORY],
                $data[self::REQUEST_CATEGORY_ID] ?? 0
            ),
            'field_columns' => $this->configurableFieldsService->getModelColumns($data[self::REQUEST_TYPE], $data[self::REQUEST_CATEGORY])
        ]);
    }

    /**
     * @param SaveConfigurableFieldsRequest $saveConfigurableFieldsRequest
     * @return JsonResponse
     * @throws Exception
     */
    public function save(SaveConfigurableFieldsRequest $saveConfigurableFieldsRequest): JsonResponse
    {
        $data = $saveConfigurableFieldsRequest->safe()->only([
            self::REQUEST_TYPE,
            self::REQUEST_CATEGORY,
            self::REQUEST_CATEGORY_ID,
            self::REQUEST_FIELDS
        ]);

        $this->configurableFieldsService->saveFields(
            collect($data[self::REQUEST_FIELDS]),
            $data[self::REQUEST_TYPE],
            $data[self::REQUEST_CATEGORY],
            $data[self::REQUEST_CATEGORY_ID] ?? 0
        );

        return $this->formatResponse([
            'status' => true
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getTypeCategories(): JsonResponse
    {
        $typeCategories = [];
        foreach(ConfigurableFieldsService::MODELS as $type => $categories) {
            $typeCategories[$type] = array_keys($categories);
        }

        return $this->formatResponse([
            'status' => true,
            'type_categories' => $typeCategories
        ]);
    }

    public function getFieldTypes(): JsonResponse
    {
        return $this->formatResponse([
            'status' => true,
            'field_types' => $this->configurableFieldsService->getFieldTypes()
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getCompanyFieldCategories(): JsonResponse
    {
        return $this->formatResponse([
            'status' => true,
            'categories' => CompanyConfigurableFieldCategory::allCategories()
        ]);
    }

    /**
     * Returns all consumer configurable field categories from database
     *
     * @return JsonResponse
     */
    public function getConsumerConfigurableFieldCategories(): JsonResponse
    {
        $categories = $this->configurableFieldsService->getAllConsumerConfigurableFieldCategories();

        return $this->formatResponse([
            'status'     => true,
            'categories' => collect(ConsumerConfigurableFieldCategoryResource::collection($categories))->toArray()
        ]);
    }

    public function getCompanyUserFields(): JsonResponse
    {
        return $this->formatResponse([
            'status'     => true,
            'fields' => CompanyUserField::query()->get()->map(fn(CompanyUserField $field) => new CompanyUserFieldDataModel(
                $field->name,
                $field->key,
                $field->fieldType->type,
                $field->hidden,
                // todo dynamically get the best default value given the type - add to the CompanyUserDataTransformer
                $field->fieldType->type  === ConfigurableFieldType::TYPE_BOOLEAN ? false : CompanyUserDataTransformer::DEFAULT_VALUE
            ))
        ]);
    }
}
