<?php

namespace App\Http\Controllers\API\IndustryManagement;

use App\Http\Controllers\API\APIController;
use App\Http\Requests\Odin\StoreWebsiteApiKeyRequest;
use App\Http\Requests\Odin\StoreWebsiteRequest;
use App\Http\Requests\Odin\UpdateWebsiteApiKeyRequest;
use App\Models\Odin\Website;
use App\Models\Odin\WebsiteApiKey;
use App\Models\User;
use App\Repositories\Odin\WebsiteRepository;
use App\Transformers\Odin\WebsiteApiKeyTransformer;
use App\Transformers\Odin\WebsiteTransformer;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\UnauthorizedException;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;

class WebsiteController extends APIController
{
    /**
     * @param WebsiteTransformer $transformer
     *
     * @return JsonResponse
     */
    public function index(WebsiteTransformer $transformer): JsonResponse
    {
        return $this->formatResponse([
            'websites' => $transformer->transformAll(Website::query()->latest()->get())
        ]);
    }

    /**
     * @param StoreWebsiteRequest $request
     * @param WebsiteTransformer $transformer
     * @param WebsiteRepository $repository
     *
     * @return JsonResponse
     */
    public function createWebsite(StoreWebsiteRequest $request, WebsiteTransformer $transformer, WebsiteRepository $repository): JsonResponse
    {
        $inputs = $request->safe()->collect();

        return $this->formatResponse([
            'website' => $transformer->transformWebsite(
                $repository->updateOrCreateWebsite(
                    $inputs->get(Website::FIELD_NAME),
                    $inputs->get(Website::FIELD_URL),
                    $inputs->get(Website::FIELD_ABBREVIATION, ""),
                    $inputs->get(Website::FIELD_CP_DOMAIN, ""),
                )
            )
        ]);
    }

    /**
     * @param Website $website
     * @param StoreWebsiteRequest $request
     * @param WebsiteTransformer $transformer
     * @param WebsiteRepository $repository
     *
     * @return JsonResponse
     */
    public function updateWebsite(Website $website, StoreWebsiteRequest $request, WebsiteTransformer $transformer, WebsiteRepository $repository): JsonResponse
    {
        $inputs = $request->safe()->collect();

        return $this->formatResponse([
            'website' => $transformer->transformWebsite(
                $repository->updateOrCreateWebsite(
                    $inputs->get(Website::FIELD_NAME),
                    $inputs->get(Website::FIELD_URL),
                    $inputs->get(Website::FIELD_ABBREVIATION, ""),
                    $inputs->get(Website::FIELD_CP_DOMAIN, ""),
                    $website->id
                )
            )
        ]);
    }

    /**
     * @param Website $website
     * @param WebsiteRepository $repository
     *
     * @return JsonResponse
     */
    public function deleteWebsite(Website $website, WebsiteRepository $repository): JsonResponse
    {
        $this->authorizeRequest();

        return $this->formatResponse([
            'status' => $repository->deleteWebsite($website)
        ]);
    }

    /**
     * @param Website $website
     * @param WebsiteApiKeyTransformer $transformer
     *
     * @return JsonResponse
     */
    public function getApiKeys(Website $website, WebsiteApiKeyTransformer $transformer): JsonResponse
    {
        return $this->formatResponse([
            'api_keys' => $transformer->transformAll($website->apiKeys()->latest()->get())
        ]);
    }

    /**
     * @param Website $website
     * @param WebsiteRepository $repository
     * @param WebsiteApiKeyTransformer $transformer
     * @param StoreWebsiteApiKeyRequest $request
     *
     * @return JsonResponse
     */
    public function createApiKey(Website $website, WebsiteRepository $repository, WebsiteApiKeyTransformer $transformer, StoreWebsiteApiKeyRequest $request): JsonResponse
    {
        return $this->formatResponse([
            'api_key' => $transformer->transformApiKey($repository->addWebsiteApiKey($website, $request->validated()))
        ]);
    }

    /**
     * @param Website $website
     * @param WebsiteRepository $repository
     * @param WebsiteApiKeyTransformer $transformer
     * @param StoreWebsiteApiKeyRequest $request
     *
     * @return JsonResponse
     */
    public function updateApiKey(Website $website, WebsiteApiKey $websiteApiKey, WebsiteRepository $repository, WebsiteApiKeyTransformer $transformer, UpdateWebsiteApiKeyRequest $request): JsonResponse
    {
        return $this->formatResponse([
            'api_key' => $transformer->transformApiKey($repository->updateWebsiteApiKeyOrigins($websiteApiKey, $request->validated()))
        ]);
    }

    /**
     * @param Website $website
     * @param WebsiteApiKey $websiteApiKey
     * @param WebsiteRepository $repository
     *
     * @return JsonResponse
     */
    public function enableApiKey(Website $website, WebsiteApiKey $websiteApiKey, WebsiteRepository $repository): JsonResponse
    {
        $this->authorizeRequest();

        if ($websiteApiKey->website_id !== $website->id) throw new BadRequestException();

        return $this->formatResponse([
            'status' => $repository->updateWebsiteApiKeyStatus($websiteApiKey, true)
        ]);
    }

    /**
     * @param Website $website
     * @param WebsiteApiKey $websiteApiKey
     * @param WebsiteRepository $repository
     *
     * @return JsonResponse
     */
    public function disableApiKey(Website $website, WebsiteApiKey $websiteApiKey, WebsiteRepository $repository): JsonResponse
    {
        $this->authorizeRequest();

        if ($websiteApiKey->website_id !== $website->id) throw new BadRequestException();

        return $this->formatResponse([
            'status' => $repository->updateWebsiteApiKeyStatus($websiteApiKey, false)
        ]);
    }

    /**
     * @return void
     */
    protected function authorizeRequest(): void
    {
        /** @var User $user */
        $user = Auth::user();

        if (!$user->hasPermissionTo('industry-management')) throw new UnauthorizedException('User does not have permission');
    }
}
