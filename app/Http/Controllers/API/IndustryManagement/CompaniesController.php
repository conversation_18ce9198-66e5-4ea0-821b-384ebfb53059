<?php

namespace App\Http\Controllers\API\IndustryManagement;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyService;
use App\Models\Odin\IndustryService;
use App\Models\User;
use App\Repositories\Odin\CompanyIndustryServiceRepository;
use App\Transformers\Odin\IndustryServiceTransformer;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\UnauthorizedException;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;
use Exception;

class CompaniesController extends APIController
{

    public function __construct(
        public Request $request,
        public JsonAPIResponseFactory $apiResponseFactory,
        protected CompanyIndustryServiceRepository $companyIndustryServiceRepository,
    ) {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param Company $company
     * @param IndustryServiceTransformer $transformer
     *
     * @return JsonResponse
     */
    public function getServices(Company $company, IndustryServiceTransformer $transformer): JsonResponse
    {
        return $this->formatResponse([
            'services' => $transformer->transform($company->services)
        ]);
    }

    /**
     * @param Company $company
     * @param IndustryService $industryService
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function addService(Company $company, IndustryService $industryService): JsonResponse
    {
        $this->authorizeRequest();

        if ($company->services()
            ->where(CompanyService::FIELD_COMPANY_ID, $company->id)
            ->where(CompanyService::FIELD_INDUSTRY_SERVICE_ID, $industryService->id)
            ->first())
            throw new BadRequestException('The service has already been added');

        // The legacy requires a list of all active services (including the newly requested service) to sync & refresh services for the given company
        $activeIndustryServicesOfACompany = $company->services()->pluck(CompanyService::FIELD_INDUSTRY_SERVICE_ID);
        $activeIndustryServicesOfACompany->push($industryService->{IndustryService::FIELD_ID});

        return $this->formatResponse([
            'status' => !!$this->companyIndustryServiceRepository->setupServicesForCompany(
                company            : $company,
                industryServiceIds : $activeIndustryServicesOfACompany->toArray()
            ),
        ]);
    }

    /**
     * @param Company $company
     * @param IndustryService $industryService
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function deleteService(Company $company, IndustryService $industryService): JsonResponse
    {
        $this->authorizeRequest();

        return $this->formatResponse([
            'status' => $this->companyIndustryServiceRepository->removeCompanyService($company, $industryService),
        ]);
    }

    /**
     * @return void
     */
    protected function authorizeRequest(): void
    {
        /** @var User $user */
        $user = Auth::user();

        if (!$user->hasPermissionTo('industry-management')) throw new UnauthorizedException('User does not have permission');
    }
}
