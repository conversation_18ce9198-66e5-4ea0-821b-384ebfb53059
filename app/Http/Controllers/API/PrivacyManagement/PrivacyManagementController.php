<?php

namespace App\Http\Controllers\API\PrivacyManagement;

use App\Enums\PrivacyManagement\PrivacyRequestStatuses;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\PrivacyManagement\CreateNewPrivacyRequest;
use App\Http\Requests\PrivacyManagement\GetPrivacyRequestRequest;
use App\Http\Requests\PrivacyManagement\GetPrivacyRequestsRequest;
use App\Http\Requests\PrivacyManagement\RedactPpiRequest;
use App\Http\Requests\PrivacyManagement\ScanSystemRequest;
use App\Http\Resources\Odin\PrivacyRequestListResource;
use App\Http\Resources\Odin\PrivacyRequestResource;
use App\Jobs\PrivacyRequestRedactJob;
use App\Jobs\PrivacyRequestSearchJob;
use App\Models\PrivacyRequest;
use App\Services\PrivacyManagement\PrivacyManagementService;
use HeapsGoodServices\PrivacyRequest\Services\PrivacyRequestService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Exception;

class PrivacyManagementController extends APIController
{
    const string STATUS  = 'status';
    const string MESSAGE = 'message';

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param PrivacyManagementService $privacyManagementService
     */
    public function __construct(
        protected Request                  $request,
        protected JsonAPIResponseFactory   $apiResponseFactory,
        protected PrivacyManagementService $privacyManagementService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param GetPrivacyRequestsRequest $request
     * @return array
     */
    public function getPrivacyRequests(GetPrivacyRequestsRequest $request): array
    {
        $validated = $request->validated();

        $privacyRequests = $this->privacyManagementService->getPrivacyRequests(filters: $validated);

        return PrivacyRequestListResource::paginate($privacyRequests, $validated[GetPrivacyRequestsRequest::REQUEST_PER_PAGE], $validated[GetPrivacyRequestsRequest::REQUEST_PAGE]);
    }

    /**
     * @param GetPrivacyRequestRequest $request
     * @return PrivacyRequestResource
     */
    public function getPrivacyRequest(GetPrivacyRequestRequest $request): PrivacyRequestResource
    {
        $validated = $request->validated();

        $request = $this->privacyManagementService->getPrivacyRequest(requestId: Arr::get($validated, GetPrivacyRequestRequest::REQUEST_ID));

        return new PrivacyRequestResource($request);
    }

    /**
     * @param CreateNewPrivacyRequest $request
     * @return JsonResponse
     */
    public function createNewPrivacyRequest(CreateNewPrivacyRequest $request): JsonResponse
    {
        $validated = $request->validated();

        return $this->formatResponse([
            self::STATUS  => $this->privacyManagementService->createPrivacyRequest($validated),
            self::MESSAGE => 'Successfully created new privacy request.'
        ]);
    }

    /**
     * @param ScanSystemRequest $request
     * @param PrivacyRequest $privacyRequest
     * @return JsonResponse
     */
    public function scanSystem(ScanSystemRequest $request, PrivacyRequest $privacyRequest): JsonResponse
    {
        PrivacyRequestSearchJob::dispatch($privacyRequest);

        return $this->formatResponse([
            self::STATUS  => true,
            self::MESSAGE => 'Scanning Database for PPI, may take a few minutes.'
        ]);
    }

    /**
     * @param RedactPpiRequest $request
     * @return JsonResponse
     */
    public function redactPpi(RedactPpiRequest $request, PrivacyRequest $privacyRequest): JsonResponse
    {
        $validated = $request->validated();

        PrivacyRequestRedactJob::dispatch(
            $privacyRequest,
            $validated[RedactPpiRequest::WEBSITE],
            $validated[RedactPpiRequest::MODEL],
            $validated[RedactPpiRequest::DATA],
            Auth::user()->id,
        );

        return $this->formatResponse([
            self::STATUS  => true,
            self::MESSAGE => 'Redacting PPI From Database, may take a few minutes.'
        ]);
    }

    /**
     * @param PrivacyRequest $privacyRequest
     * @param CreateNewPrivacyRequest $request
     * @return JsonResponse
     */
    public function updatePrivacyRequest(PrivacyRequest $privacyRequest, CreateNewPrivacyRequest $request): JsonResponse
    {
        $validated = $request->validated();

        return $this->formatResponse([
            self::STATUS  => $this->privacyManagementService->updatePrivacyRequest($privacyRequest, $validated),
            self::MESSAGE => 'Successfully updated privacy request.'
        ]);
    }


    /**
     * @param PrivacyRequest $privacyRequest
     * @param PrivacyRequestService $privacyRequestService
     * @return JsonResponse
     * @throws Exception
     */
    public function finishRedactPpi(PrivacyRequest $privacyRequest, PrivacyRequestService $privacyRequestService): JsonResponse
    {
        $privacyRequest->payload = $privacyRequestService->redactRecord($privacyRequest->payload);
        $privacyRequest->status = PrivacyRequestStatuses::DONE;
        $privacyRequest->save();

        return $this->formatResponse([
            self::STATUS  => true,
            self::MESSAGE => 'Redacting the privacy request'
        ]);
    }
}
