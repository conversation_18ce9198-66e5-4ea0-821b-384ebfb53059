<?php

namespace App\Http\Controllers\API\EstimatedRevenuePerLeadByLocation;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Repositories\EstimatedRevenuePerLeadByLocationRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class EstimatedRevenuePerLeadByLocationController extends APIController
{
    /** @var EstimatedRevenuePerLeadByLocationRepository $repository */
    private EstimatedRevenuePerLeadByLocationRepository $repository;

    const REQUEST_INDUSTRY             = 'industry';

    // we should consolidate where we get the Industry types to the Industry model after reviewing the difference between 'roofer' and 'roofing'
    const INDUSTRY_TYPE_SOLAR = 'solar';
    const INDUSTRY_TYPE_ROOFING = 'roofer';

    const INDUSTRY_TYPES = [
        self::INDUSTRY_TYPE_SOLAR,
        self::INDUSTRY_TYPE_ROOFING,
    ];

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param EstimatedRevenuePerLeadByLocationRepository $repository
     */
    public function __construct(
        Request                                     $request,
        JsonAPIResponseFactory                      $apiResponseFactory,
        EstimatedRevenuePerLeadByLocationRepository $repository
    )
    {
        $this->repository = $repository;

        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @return JsonResponse
     */
    public function getEstimatedRevenuePerLead(): JsonResponse
    {
        $data = $this->repository->getDataGroupedByStateAndRevenue();

        return $this->formatResponse($data->toArray());
    }
}
