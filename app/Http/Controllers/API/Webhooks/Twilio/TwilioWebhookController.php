<?php

namespace App\Http\Controllers\API\Webhooks\Twilio;

use App\Http\Controllers\Controller;
use App\Repositories\CommunicationRepository;
use App\Services\CallForwardingService;
use App\Services\Communication\CommunicationService;
use App\Services\Communication\TwilioCommunicationService;
use App\Services\ConsumerRevalidationService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Str;
use Throwable;
use Twilio\TwiML\MessagingResponse;
use Twilio\TwiML\VoiceResponse;

class TwilioWebhookController extends Controller
{
    const REQUEST_DIRECTION          = 'Direction';
    const REQUEST_DIAL_STATUS        = 'DialCallStatus';
    const REQUEST_FROM               = 'From';
    const REQUEST_TO                 = 'To';
    const REQUEST_CALLER             = 'Caller';
    const REQUEST_DIAL_CALL_SID      = 'DialCallSid';
    const REQUEST_CALL_SID           = 'CallSid';
    const REQUEST_RECORDING_URL      = 'RecordingUrl';
    const REQUEST_RECORDING_DURATION = 'RecordingDuration';
    const REQUEST_CALL_STATUS        = 'CallStatus';

    const DIAL_STATUS_NO_ANSWER = 'no-answer';
    const DIAL_STATUS_BUSY      = 'busy';
    const DIAL_STATUS_COMPLETED = 'completed';

    const DIRECTION_OUTGOING = 'outbound';
    const DIRECTION_INBOUND  = 'inbound';

    const VOICEMAIL_KEY      = 'Digits';
    const CAll_RECORDING_KEY = 'RecordingSid';

    /**
     * Handles an incoming voice notification from twilio
     *
     * @param TwilioCommunicationService $service
     * @return JsonResponse
     */
    public function voice(TwilioCommunicationService $service): Response
    {
        if (request()->post('Outgoing', 'false') === 'true')
            return $this->outgoingCall($service);

        return $this->incomingCall($service);
    }

    public function callStatus(CommunicationRepository $repository, TwilioCommunicationService $service, CommunicationService $communicationService): Response
    {
        $toNumber = Str::replace("+1", "", request()->post(self::REQUEST_TO));

        if (in_array(request()->post(self::REQUEST_DIAL_STATUS), [self::DIAL_STATUS_NO_ANSWER, self::DIAL_STATUS_BUSY])){
            $user = $repository->getUserByNumber($toNumber);
            return $user?->userVoicemailMessage?->getTwilioResponse() ?? $this->voicemail($service);
        }

        $response = new VoiceResponse();
        $response->hangup();

        if (request()->has(self::VOICEMAIL_KEY)) {
            $communicationService->recordVoicemail(
                Str::replace("+1", "", request()->post(self::REQUEST_FROM)),
                $toNumber,
                request()->post(self::REQUEST_CALL_SID),
                request()->post(self::REQUEST_RECORDING_URL),
                request()->post(self::REQUEST_RECORDING_DURATION)
            );
        } elseif (request()->has(self::CAll_RECORDING_KEY)) {

            // The sId we use to track calls is sent in different columns, depending if the call is inbound or outbound
            // the only way to distinguish between inbound and outbound is by the presence of "client:" in "Caller"
            $sId = str_contains(request()->post(self::REQUEST_CALLER), 'client:' ) ?
                request()->post(self::REQUEST_CALL_SID) :
                request()->post(self::REQUEST_DIAL_CALL_SID);

            $communicationService->recordCall(
                $sId,
                request()->post(self::CAll_RECORDING_KEY),
                request()->post(self::REQUEST_RECORDING_URL),
                request()->post(self::REQUEST_RECORDING_DURATION)
            );
        }

        return response($response->asXML());
    }

    protected function voicemail(TwilioCommunicationService $service): Response
    {
        $response = new VoiceResponse();
        $response->play(url("/audio/generic-voicemail.mp3"));
        // $response->say("Thanks for calling Solar Reviews. Sorry we couldn't answer your call at this time. Please leave your name and number after the tone, and we'll contact you as soon as possible.");
        $response->record(["timeout" => 30, "transcribe" => true]);
        $response->hangup();

        return response($response->asXML());
    }

    /**
     * Handles an incoming SMS notification from twilio.
     *
     * @param TwilioCommunicationService $service
     * @return Response
     * @throws Throwable
     */
    public function sms(TwilioCommunicationService $service): Response
    {
        $service->handleIncomingSMS(request()->post());
        $response = new MessagingResponse();

        return response($response->asXML());
    }


    /**
     * @param ConsumerRevalidationService $consumerRevalidationService
     *
     * @return Response
     * @throws BindingResolutionException
     */
    public function consumerSMS(ConsumerRevalidationService $consumerRevalidationService): Response
    {
        $consumerRevalidationService->handleConsumerReply(request()->post());

        return response((new MessagingResponse())->asXML());
    }

    /**
     * @return Response
     */
    public function recycledLeadsSms(): Response
    {
        return response((new MessagingResponse())->asXML());
    }

    protected function outgoingCall(TwilioCommunicationService $service): Response
    {
        $response = new VoiceResponse();
        $response->dial(
            request()->post(self::REQUEST_TO),
            [
                "callerId" => request()->post(self::REQUEST_FROM),
                "record"   => "record-from-ringing-dual",
                "action"   => route("twilio-call-status"),
                "method"   => "POST",
            ]
        );

        return response($response->asXML());
    }

    protected function incomingCall(TwilioCommunicationService $service): Response
    {
        $from = Str::replace("+1", "", request()->post(self::REQUEST_FROM));
        $to = Str::replace("+1", "", request()->post(self::REQUEST_TO));
        $response = new VoiceResponse();
        $callForwardingService = app()->make(CallForwardingService::class);
        $forwardingNumber = $callForwardingService->getForwardingNumber($to);
        $dial = $response->dial('', [
            "callerId" => $from,
            "record"   => "record-from-ringing-dual",
            "action"   => route("twilio-call-status"),
            "method"   => "POST",
            "timeout"  => 20
        ]);
        $dial->client("incoming_" . $to);
        if($forwardingNumber){$dial->number($forwardingNumber);}

        return response($response->asXML());
    }
}
