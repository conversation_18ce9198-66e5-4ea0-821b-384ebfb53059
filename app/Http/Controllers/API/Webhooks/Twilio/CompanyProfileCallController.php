<?php

namespace App\Http\Controllers\API\Webhooks\Twilio;

use App\Http\Controllers\Controller;
use App\Models\CompanyProfileCall;
use App\Services\Companies\CompanyProfileCallService;
use Illuminate\Http\Response;
use Illuminate\Support\Str;
use Twilio\TwiML\VoiceResponse;

class CompanyProfileCallController extends Controller
{
    const string REQUEST_FROM = 'From';
    const string REQUEST_TO = 'To';
    const string REQUEST_BODY = 'Body';
    const string REQUEST_CALL_SID = 'CallSid';

    public function voice(CompanyProfileCallService $service): Response
    {
        $from = Str::replace('+1', '', request()->post(self::REQUEST_FROM));
        $proxyPhone = Str::replace('+1', '', request()->post(self::REQUEST_TO));
        $response = new VoiceResponse();
        $company = $service->findCompanyFromProxyPhone($proxyPhone);

        if(!$company) {
            return response($response->hangup()->asXML());
        }

        $companyPhone = $service->getCompanyPhone($company);

        if (!$companyPhone) {
            return response($response->hangup()->asXML());
        }

        $service->createCompanyProfileCall(
            companyId: $company->id,
            sid:  request()->post(self::REQUEST_CALL_SID),
            to: $companyPhone,
            from: $from,
            proxyPhone: $proxyPhone
        );

        $response->dial($companyPhone, [
            'callerId' => $from,
            'record'   => 'record-from-ringing-dual',
            'action'   => route('company-profile-call-status'),
            'method'   => 'POST',
            'timeout'  => 20
        ]);

        return response($response->asXML());
    }

    public function callStatus(CompanyProfileCallService $service): Response
    {
        $response = new VoiceResponse();

        $response->hangup();

        $companyProfileCall = $service->findCompanyProfileCallBySid(request()->post(self::REQUEST_CALL_SID));

        $companyProfileCall?->update([
            CompanyProfileCall::FIELD_CONTENT => array_merge($companyProfileCall->content ?? [], request()->post())
        ]);

        return response($response->asXML());
    }
}
