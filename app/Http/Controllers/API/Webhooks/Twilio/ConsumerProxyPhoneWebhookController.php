<?php

namespace App\Http\Controllers\API\Webhooks\Twilio;

use App\Enums\CommunicationType;
use App\Http\Controllers\Controller;
use App\Models\Odin\ProductAssignment;
use App\Repositories\Odin\ConsumerRepository;
use App\Repositories\Odin\ProductAssignmentRepository;
use App\Services\Communication\TwilioCommunicationService;
use App\Services\Consumer\ConsumerCommunicationService;
use App\Services\Odin\ProductAssignmentService;
use Illuminate\Http\Response;
use Illuminate\Support\Str;
use Throwable;
use Twilio\TwiML\MessagingResponse;
use Twilio\TwiML\VoiceResponse;

class ConsumerProxyPhoneWebhookController extends Controller
{
    const string REQUEST_FROM = 'From';
    const string REQUEST_TO = 'To';
    const string REQUEST_BODY = 'Body';
    const string REQUEST_CALL_SID = 'CallSid';
    const string REQUEST_MESSAGE_SID = 'MessageSid';

    /**
     * @param ProductAssignmentService $service
     * @param ConsumerCommunicationService $communicationService
     * @param TwilioCommunicationService $twilioCommunicationService
     *
     * @return Response
     */
    public function sms(ProductAssignmentService $service, ConsumerCommunicationService $communicationService, TwilioCommunicationService $twilioCommunicationService): Response
    {
        $response = new MessagingResponse();
        $to = Str::replace('+1', '', request()->post(self::REQUEST_TO));
        $productAssignment = $service->findProductAssignmentFromProxyPhone($to);
        $consumerPhone = $productAssignment?->consumerProduct->consumer->phone;

        if (!$consumerPhone) {
            return response($response->asXML());
        }

        $communicationService->createProductAssignmentCommunication(
            productAssignment: $productAssignment,
            type: CommunicationType::SMS,
            sid: request()->post(self::REQUEST_MESSAGE_SID),
            data: request()->post()
        );

        try {
            $twilioCommunicationService->sendSMS(
                from: config('services.twilio.recycled_leads_from_phone'),
                to: $consumerPhone,
                body: request()->post(self::REQUEST_BODY)
            );
        } catch (Throwable $e) {
            logger()->error("Failed to forward message to consumer $consumerPhone. Error: {$e->getMessage()}");
        }

        return response($response->asXML());
    }

    /**
     * @param ConsumerRepository $consumerRepository
     * @param ProductAssignmentRepository $productAssignmentRepository
     * @param ConsumerCommunicationService $communicationService
     *
     * @return Response
     */
    public function smsReply(
        ConsumerRepository $consumerRepository,
        ProductAssignmentRepository $productAssignmentRepository,
        ConsumerCommunicationService $communicationService
    ): Response
    {
        $consumer = $consumerRepository->findConsumerByPhone(request()->post(self::REQUEST_FROM));
        $response = new MessagingResponse();

        if (!$consumer) {
            return response($response->asXML());
        }

        $productAssignment = $productAssignmentRepository->getAllAssignmentsForConsumer($consumer)
            ->first(fn(ProductAssignment $productAssignment) => $productAssignment->proxy_phone_active && $productAssignment->proxy_phone);

        if (!$productAssignment) {
            return response($response->asXML());
        }

        $communicationService->createProductAssignmentCommunication(
            productAssignment: $productAssignment,
            type: CommunicationType::SMS,
            sid: request()->post(self::REQUEST_MESSAGE_SID),
            data: request()->post()
        );

        return response($response->asXML());
    }

    /**
     * @param ProductAssignmentService $service
     * @param ConsumerCommunicationService $communicationService
     *Ø
     * @return Response
     */
    public function voice(ProductAssignmentService $service, ConsumerCommunicationService $communicationService): Response
    {
        $from = Str::replace('+1', '', request()->post(self::REQUEST_FROM));
        $to = Str::replace('+1', '', request()->post(self::REQUEST_TO));
        $productAssignment = $service->findProductAssignmentFromProxyPhone($to);
        $consumerPhone = $productAssignment?->consumerProduct->consumer->phone;
        $response = new VoiceResponse();

        if (!$consumerPhone) {
            return response($response->hangup()->asXML());
        }

        $communicationService->createProductAssignmentCommunication(
            productAssignment: $productAssignment,
            type: CommunicationType::CALL,
            sid: request()->post(self::REQUEST_CALL_SID)
        );

        $response->dial($consumerPhone, [
            'callerId' => $from,
            'record'   => 'record-from-ringing-dual',
            'action'   => route('consumer-proxy-phone-call-status'),
            'method'   => 'POST',
            'timeout'  => 20
        ]);

        return response($response->asXML());
    }

    /**
     * @param ConsumerCommunicationService $service
     *
     * @return Response
     */
    public function callStatus(ConsumerCommunicationService $service): Response
    {
        $service->updateProductAssignmentCommunication(
            sid: request()->post(self::REQUEST_CALL_SID),
            data: request()->post()
        );

        $response = new VoiceResponse();

        $response->hangup();

        return response($response->asXML());
    }
}
