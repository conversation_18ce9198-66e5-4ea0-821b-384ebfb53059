<?php

namespace App\Http\Controllers\API\Webhooks;

use App\Http\Controllers\Controller;
use App\Services\TestProducts\Communication\TestProductCommunicationService;
use App\Services\TestProducts\TestProductService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Twilio\TwiML\MessagingResponse;
use Twilio\TwiML\VoiceResponse;

class TestProductWebhookController extends Controller
{
    public function __construct(
        protected TestProductService              $testProductService,
        protected TestProductCommunicationService $testProductCommunicationService,
        protected Request $request
    ) {}

    /**
     * Handle tracking of the incoming sms.
     *
     * @return Response
     */
    public function sms(): Response
    {
        $this->testProductCommunicationService->trackIncomingSMS($this->request->all());

        $response = new MessagingResponse();

        return response($response->asXML());
    }

    /**
     * Handle tracking of the incoming call.
     *
     * @return Response
     */
    public function voice(): Response
    {
        $this->testProductCommunicationService->trackIncomingCall($this->request->all());

        $response = new VoiceResponse();

        $response->say('The person you have dialed can’t take your call now. At the tone, please record your message.');
        $response->record(["timeout" => 30]);
        $response->hangup();

        return response($response->asXML());
    }

    /**
     * Handle tracking of the incoming email.
     *
     * @return Response
     */
    public function email(): Response
    {
        $this->testProductCommunicationService->trackNewEmail($this->request->all());

        return response("Email Stored", 200);
    }
}
