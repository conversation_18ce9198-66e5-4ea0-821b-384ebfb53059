<?php

namespace App\Http\Controllers\API\Webhooks;

use App\Http\Controllers\Controller;
use App\Services\Mailbox\MailboxWebhookService;
use Exception;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Redirector;
use Illuminate\Support\Arr;

class MailboxWebhookController extends Controller
{

    public function __construct(protected MailboxWebhookService $mailboxWebhookService)
    {

    }

    /**
     * Triggered by the mail provider when OAuth is successfully accomplished.
     * It should store the user's token in our database and dispatch a job to set up a listener for new emails.
     *
     * @param Request $request
     * @return Application|RedirectResponse|Redirector
     * @throws Exception
     */
    public function handleOAuthCallback(Request $request)
    {
        // TODO - Change this code to not depend on google
        $param = json_decode(safeBase64Decode($request->get('state')), true);

        $userId = $param['userId'];
        $code = $request->get('code');

        $this->mailboxWebhookService->getMailboxAccessToken($userId, $code);

        return redirect('mailbox');
    }

    /**
     * Triggered by the mail provider when a user receives an email
     * It should store the email in our database
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function handleEmailEvent(Request $request)
    {
        // TODO - Change this code to not depend on google
        $message = $request->get('message');
        $decoded = json_decode(safeBase64Decode(Arr::get($message, 'data')), true);

        $emailAddress = Arr::get($decoded, 'emailAddress');
        $historyId = Arr::get($decoded, 'historyId');

        $this->mailboxWebhookService->dispatchJobToImportReceivedEmail($emailAddress, $historyId);

        return response()->json();
    }
}
