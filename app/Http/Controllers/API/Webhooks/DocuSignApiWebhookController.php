<?php

namespace App\Http\Controllers\API\Webhooks;

use App\Http\Controllers\Controller;
use App\Services\Docusign\DocuSignService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Exception;

class DocuSignApiWebhookController extends Controller
{
    /**
     * @param Request $request
     * @param DocuSignService   $docuSignService
     */
    public function __construct(
        protected Request           $request,
        protected DocuSignService   $docuSignService
    ) {}

    /**
     * @return Response
     */
    public function handleEvent(): Response
    {
        try {
            $this->docuSignService->handleCallbackEvent($this->request);
        } catch (Exception) {
            return response('Fail', 500);
        }

        return response("API event received", 200);
    }
}

