<?php

namespace App\Http\Controllers\API\Webhooks\Subscriptions;

use App\Enums\Odin\PubSubOrigin;
use App\Http\Controllers\Controller;
use App\Services\PubSub\PubSubMiddlewareService;
use App\Services\PubSub\SubscriptionService;
use Illuminate\Http\JsonResponse;
use \Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Facades\App;

class SubscriptionController extends Controller
{
    public function __construct(
        protected PubSubMiddlewareService $middlewareService
    ) {}

    /**
     * @param SubscriptionService $service
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function handleEvent(SubscriptionService $service): JsonResponse
    {
        $event = json_decode(base64_decode(request()->all()["message"]["data"]), true);
        $event['publish_time'] = request()->all()["message"]["publishTime"];

        $origin = $this->getEventOrigin($event);
        if ($origin) {
            $event = $this->middlewareService->process($event, $origin);
        }

        if(!App::isProduction() && config('services.google.pubsub.debug')) {
            logger()->info($event);
        }

        $service->handle($event);

        return response()->json();
    }

    /**
     * @param array $event
     * @return PubSubOrigin|null
     */
    private function getEventOrigin(array $event): ?PubSubOrigin
    {
        $originString = $event["origin"] ?? null;
        return $originString
            ? PubSubOrigin::tryFrom($originString)
            : null;
    }


}
