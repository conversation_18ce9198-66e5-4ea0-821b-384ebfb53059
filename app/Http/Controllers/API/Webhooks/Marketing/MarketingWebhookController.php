<?php

namespace App\Http\Controllers\API\Webhooks\Marketing;

use App\Contracts\Marketing\MarketingEventInterpreterFactory;
use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\EventInterpreter;
use App\Enums\MarketingCampaigns\MarketingLogType;
use App\Http\Controllers\Controller;
use App\Jobs\HandleEmailEvent;
use App\Services\MarketingCampaign\MarketingCampaignService;
use App\Services\MarketingCampaign\MarketingLogService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class MarketingWebhookController extends Controller
{
    const string EVENT_SOURCE = 'source';
    public function __construct(
        protected MarketingCampaignService $marketingCampaignService
    )
    {}

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function handleEmailEvent(Request $request): JsonResponse
    {
        $source = EventInterpreter::tryFrom(
            $request->query(self::EVENT_SOURCE, EventInterpreter::MAILCHIMP->value)
        );

        $eventInterpreter = MarketingEventInterpreterFactory::make($source);

        $event = $eventInterpreter->interpret($request);

        if ($event) {
            HandleEmailEvent::dispatch($event);
        } else {
            MarketingLogService::log(
                message: "Unknown Marketing Webhook Event",
                namespace: MarketingLogType::WEBHOOK,
                level: LogLevel::ALERT,
                context: $request->all(),
            );
        }

        return response()->json();
    }

    public function handleSMSEvent(Request $request): JsonResponse
    {
        $eventInterpreter = MarketingEventInterpreterFactory::make(EventInterpreter::TWILIO);

        $event = $eventInterpreter->interpret($request);
        if ($event) {
            HandleEmailEvent::dispatch($event);
        } else {
            MarketingLogService::log(
                message: "Unknown Marketing Webhook Event",
                namespace: MarketingLogType::WEBHOOK,
                level: LogLevel::ALERT,
                context: $request->all(),
            );
        }

        return response()->json();
    }
}
