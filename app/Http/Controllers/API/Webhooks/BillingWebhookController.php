<?php

namespace App\Http\Controllers\API\Webhooks;

use App\Contracts\Billing\PaymentGatewayEventInterpreterContract;
use App\Http\Controllers\Controller;
use App\Jobs\ProcessBillingWebhook;
use App\Services\Billing\BillingLogService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class BillingWebhookController extends Controller
{
    public function __construct(
        protected PaymentGatewayEventInterpreterContract $paymentGatewayEventInterpreterContract,
    )
    {

    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Exception
     */
    public function handleEvent(Request $request): JsonResponse
    {
        try {
            $event = $this->paymentGatewayEventInterpreterContract->interpret($request);

            if (!empty($event)) {
                ProcessBillingWebhook::dispatch($event);
            }
        } catch (Exception $exception) {
            BillingLogService::logException(
                exception: $exception,
                namespace: 'payment_gateway_webhook',
                context  : $request->all(),
            );
        }

        return response()->json();
    }
}
