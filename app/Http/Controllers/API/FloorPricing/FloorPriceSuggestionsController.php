<?php

namespace App\Http\Controllers\API\FloorPricing;

use App\Enums\LoweredFloorPricePolicy;
use App\Enums\Odin\Product;
use App\Enums\Odin\PropertyType;
use App\Enums\Odin\QualityTier;
use App\Enums\Odin\SaleTypes;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\FloorPrice\ApplySuggestedPriceRequest;
use App\Http\Requests\FloorPrice\UpdatePricingMarginRequest;
use App\Http\Resources\FloorPrice\PricingMarginResource;
use App\Models\Legacy\Location;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\PricingMargin;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\ProductRepository;
use App\Repositories\Odin\ServiceProductRepository;
use App\Services\FloorPrices\FloorPricesSuggestionService;
use App\Services\FloorPrices\PricingMarginService;
use App\Services\Odin\Pricing\FloorPricingManagementService;
use App\Transformers\Odin\IndustryTransformer;
use Exception;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;

class FloorPriceSuggestionsController extends APIController
{
    const string RESPONSE_STATUS = 'status';
    const string RESPONSE_STATES = 'states';
    const string RESPONSE_CONFIGURATION = 'configuration';
    const string RESPONSE_QUALITY_TIERS = 'quality_tiers';
    const string RESPONSE_PROPERTY_TYPES = 'property_types';
    const string RESPONSE_INDUSTRIES = 'industries';
    const string RESPONSE_SUGGESTED_PRICES = 'suggested_prices';
    const string RESPONSE_PRICING_MARGIN = 'pricing_margin';
    const string RESPONSE_CAMPAIGN_STATS = 'campaign_stats';
    const string RESPONSE_LEGS_SOLD_STATS = 'legs_sold_stats';

    /**
     * @param LocationRepository $locationRepository
     * @param IndustryTransformer $industryTransformer
     *
     * @return JsonResponse
     */
    public function getStateInitialData(LocationRepository $locationRepository, IndustryTransformer $industryTransformer): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_STATUS => true,
            self::RESPONSE_CONFIGURATION => $this->getConfigurations($locationRepository, $industryTransformer)
        ]);
    }

    /**
     * @param Industry $industry
     * @param IndustryService $industryService
     * @param string $stateKey
     * @param LocationRepository $locationRepository
     * @param FloorPricesSuggestionService $floorPricesSuggestionService
     *
     * @return JsonResponse
     */
    public function getFloorPriceSuggestions(
        Industry $industry,
        IndustryService $industryService,
        string $stateKey,
        LocationRepository $locationRepository,
        FloorPricesSuggestionService $floorPricesSuggestionService
    ): JsonResponse
    {
        if ($industry->id !== $industryService->industry_id) {
            throw new BadRequestException('Invalid Industry/Service');
        }

        return $this->formatResponse([
            self::RESPONSE_STATUS => true,
            self::RESPONSE_SUGGESTED_PRICES => $floorPricesSuggestionService->calculateFloorPriceSuggestionForStateAndIndustry(
                industry: $industry,
                industryService: $industryService,
                stateLocation: $locationRepository->getState($stateKey),
                startDate: now()->subDays(91)->startOfDay(),
                endDate: now()->subDay()->startOfDay()
            )
        ]);
    }

    /**
     * @param Industry $industry
     * @param IndustryService $industryService
     * @param ApplySuggestedPriceRequest $request
     * @param ServiceProductRepository $serviceProductRepository
     * @param FloorPricingManagementService $floorPricingManagementService
     * @param LocationRepository $locationRepository
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function applySuggestedPrices(
        Industry $industry,
        IndustryService $industryService,
        ApplySuggestedPriceRequest $request,
        ServiceProductRepository $serviceProductRepository,
        FloorPricingManagementService $floorPricingManagementService,
        LocationRepository $locationRepository
    ): JsonResponse
    {
        if ($industryService->industry->id !== $industry->id) {
            throw new BadRequestException('Invalid Industry/Service');
        }

        $product = Product::from($request->validated(ApplySuggestedPriceRequest::PRODUCT))->model();
        $qualityTier = QualityTier::from($request->validated(ApplySuggestedPriceRequest::QUALITY_TIER))->model();
        $propertyType = PropertyType::from($request->validated(ApplySuggestedPriceRequest::PROPERTY_TYPE))->model();
        $serviceProduct = $serviceProductRepository->findServiceProductByIndustryServiceAndProductOrFail($industryService->id, $product->id);

        foreach ($request->validated(ApplySuggestedPriceRequest::PRICES) as $price) {
            /** @var Location $location */
            $location = Location::query()->findOrFail($price[ApplySuggestedPriceRequest::LOCATION_ID]);

            if ($location->type === Location::TYPE_STATE) {
                $this->updateStatePrice(
                    service: $floorPricingManagementService,
                    price: $price,
                    stateLocationId: $location->id,
                    serviceProductId: $serviceProduct->id,
                    qualityTierId: $qualityTier->id,
                    propertyTypeId: $propertyType->id,
                    policy: $this->preparePolicy(LoweredFloorPricePolicy::from($request->validated(ApplySuggestedPriceRequest::PRICE_POLICY)))
                );
            } else if ($location ->type === Location::TYPE_COUNTY) {
                $this->updateCountyPrice(
                    service: $floorPricingManagementService,
                    price: $price,
                    sateLocationId: $locationRepository->getState($location->state_key)->id,
                    countyLocationId: $location->id,
                    serviceProductId: $serviceProduct->id,
                    qualityTierId: $qualityTier->id,
                    propertyTypeId: $propertyType->id,
                    policy: $this->preparePolicy(LoweredFloorPricePolicy::from($request->validated(ApplySuggestedPriceRequest::PRICE_POLICY)))
                );
            }
        }

        return $this->formatResponse([
            self::RESPONSE_STATUS => true
        ]);
    }

    /**
     * @param Industry $industry
     * @param IndustryService $industryService
     * @param PricingMarginService $pricingMarginService
     *
     * @return JsonResponse
     */
    public function getPriceMargin(Industry $industry, IndustryService $industryService, PricingMarginService $pricingMarginService): JsonResponse
    {
        if ($industryService->industry->id !== $industry->id) {
            throw new BadRequestException('Invalid Industry/Service');
        }

        return $this->formatResponse([
            self::RESPONSE_STATUS => true,
            self::RESPONSE_PRICING_MARGIN => new PricingMarginResource(
                $pricingMarginService->findByIndustryServiceOrNew($industryService->id)
            )
        ]);
    }

    /**
     * @param PricingMargin $pricingMargin
     * @param PricingMarginService $pricingMarginService
     * @param UpdatePricingMarginRequest $request
     *
     * @return JsonResponse
     */
    public function updatePriceMargin(PricingMargin $pricingMargin, PricingMarginService $pricingMarginService, UpdatePricingMarginRequest $request): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_STATUS => $pricingMarginService->update(
                pricingMargin: $pricingMargin,
                exclusiveMargin: $request->validated(UpdatePricingMarginRequest::EXCLUSIVE),
                duoMargin: $request->validated(UpdatePricingMarginRequest::DUO),
                trioMargin: $request->validated(UpdatePricingMarginRequest::TRIO),
                quadMargin: $request->validated(UpdatePricingMarginRequest::QUAD)
            )
        ]);
    }

    public function getStatistics(
        Industry $industry,
        IndustryService $industryService,
        string $stateKey,
        FloorPricesSuggestionService $floorPricesSuggestionService,
        LocationRepository $locationRepository,
        ProductRepository $productRepository,
        ServiceProductRepository $serviceProductRepository
    ): JsonResponse
    {
        if ($industryService->industry->id !== $industry->id) {
            throw new BadRequestException('Invalid Industry/Service');
        }

        $stateLocation = $locationRepository->getState($stateKey);
        $product = $productRepository->getProductByName(Product::LEAD->value);
        $serviceProduct = $serviceProductRepository->findServiceProductByIndustryServiceAndProductOrFail($industryService->id, $product->id);

        return $this->formatResponse([
            self::RESPONSE_STATUS => true,
            self::RESPONSE_CAMPAIGN_STATS => $floorPricesSuggestionService->getCampaignStatistics($industryService, $stateLocation, $product),
            self::RESPONSE_LEGS_SOLD_STATS => $floorPricesSuggestionService->getLegsSoldStatistics(
                $serviceProduct,
                $stateLocation,
                now()->subDays(31)->startOfDay(),
                now()->subDay()->startOfDay()
            )
        ]);
    }

    /**
     * @param LocationRepository $locationRepository
     * @param IndustryTransformer $industryTransformer
     *
     * @return array
     */
    protected function getConfigurations(LocationRepository $locationRepository, IndustryTransformer $industryTransformer): array
    {
        return [
            self::RESPONSE_STATES => $locationRepository->getStates(),
            self::RESPONSE_QUALITY_TIERS => [QualityTier::STANDARD->value],
            self::RESPONSE_PROPERTY_TYPES => [PropertyType::RESIDENTIAL->value],
            self::RESPONSE_INDUSTRIES => $industryTransformer->transform(Industry::query()->get()->load(Industry::RELATION_SERVICES)),
        ];
    }

    /**
     * @param FloorPricingManagementService $service
     * @param array $price
     * @param int $stateLocationId
     * @param int $serviceProductId
     * @param int $qualityTierId
     * @param int $propertyTypeId
     * @param array $policy
     *
     * @return void
     * @throws Exception
     */
    protected function updateStatePrice(
        FloorPricingManagementService $service,
        array $price,
        int $stateLocationId,
        int $serviceProductId,
        int $qualityTierId,
        int $propertyTypeId,
        array $policy
    ): void
    {
        $service->updateScopedStatePrices(
            priceUpdates: $this->preparePrices($price),
            stateLocationId: $stateLocationId,
            serviceProductId: $serviceProductId,
            qualityTierId: $qualityTierId,
            propertyTypeId: $propertyTypeId,
            loweredPricePolicy: $policy
        );
    }

    /**
     * @param FloorPricingManagementService $service
     * @param array $price
     * @param int $sateLocationId
     * @param int $countyLocationId
     * @param int $serviceProductId
     * @param int $qualityTierId
     * @param int $propertyTypeId
     * @param array $policy
     *
     * @return void
     * @throws Exception
     */
    protected function updateCountyPrice(
        FloorPricingManagementService $service,
        array $price,
        int $sateLocationId,
        int $countyLocationId,
        int $serviceProductId,
        int $qualityTierId,
        int $propertyTypeId,
        array $policy
    ): void
    {
        $service->updateScopedCountyPrices(
            priceUpdates: $this->preparePrices($price),
            stateLocationId: $sateLocationId,
            countyLocationId: $countyLocationId,
            serviceProductId: $serviceProductId,
            qualityTierId: $qualityTierId,
            propertyTypeId: $propertyTypeId,
            loweredPricePolicy: $policy
        );
    }

    /**
     * @param array $price
     *
     * @return array
     */
    protected function preparePrices(array $price): array
    {
        $availablePrices = [];

        foreach (SaleTypes::allSaleTypeKeys() as $key) {
            if (array_key_exists($key, $price)) {
                $availablePrices[$key] = $price[$key];
            }
        }

        return  $availablePrices;
    }

    /**
     * @param LoweredFloorPricePolicy $loweredFloorPricePolicy
     *
     * @return array
     */
    protected function preparePolicy(LoweredFloorPricePolicy $loweredFloorPricePolicy): array
    {
        $policy [FloorPricingController::REQUEST_LOWERED_PRICE_POLICY_TYPE] = $loweredFloorPricePolicy->value;

        if ($loweredFloorPricePolicy === LoweredFloorPricePolicy::CREATE_CAMPAIGN_BIDS) {
            $policy [LoweredFloorPricePolicy::MODIFY_CAMPAIGNS_RECENCY_THRESHOLD] = 30; //todo: may need user input
        }

        return $policy;
    }
}
