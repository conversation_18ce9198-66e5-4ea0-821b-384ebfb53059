<?php

namespace App\Http\Controllers\API\FloorPricing;

use App\Enums\Odin\Product as ProductEnum;
use App\Enums\Odin\PropertyType as PropertyTypeEnum;
use App\Enums\Odin\QualityTier as QualityTierEnum;
use App\Enums\Odin\Region;
use App\Enums\Odin\SaleTypes;
use App\Enums\PermissionType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\GetFloorPriceHistoryRequest;
use App\Http\Requests\GetFloorPricingRequest;
use App\Http\Requests\Odin\StoreFloorPricingRequest;
use App\Http\Resources\FloorPricingHistoryLogsResource;
use App\Models\Odin\FloorPriceFormula;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ServiceProduct;
use App\Models\User;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\IndustryRepository;
use App\Repositories\Odin\IndustryServiceRepository;
use App\Repositories\Odin\ProductRepository;
use App\Services\Odin\Pricing\FloorPricingHistoryService;
use App\Services\Odin\Pricing\FloorPricingManagementService;
use App\Transformers\Odin\FloorPricingManagementTransformer;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Models\Odin\QualityTier;
Use App\Models\Odin\PropertyType;
use App\Models\Odin\Product;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\UnauthorizedException;


class FloorPricingController extends APIController
{
    const string MODULE_PERMISSION                 = PermissionType::MINIMUM_PRICE_MANAGEMENT_VIEW->value;

    const string REQUEST_PRODUCT                   = 'product';
    const string REQUEST_PROPERTY_TYPE             = 'property_type';
    const string REQUEST_STATE_ID                  = 'state';
    const string REQUEST_QUALITY_TIER              = 'quality_tier';
    const string REQUEST_INDUSTRY_SERVICE_ID       = 'industry_service_id';
    const string REQUEST_FROM_INDUSTRY_SERVICE_ID  = 'from_industry_service_id';
    const string REQUEST_TO_INDUSTRY_SERVICE_ID    = 'to_industry_service_id';
    const string REQUEST_REGION_TYPE               = 'region_type';
    const string REQUEST_PRICES                    = 'prices';
    const string REQUEST_STATE_LOCATION_ID         = 'state_location_id';
    const string REQUEST_COUNTY_LOCATION_ID        = 'county_location_id';
    const string REQUEST_UPDATE_INHERITED_PRICE    = 'update_inherited_price';
    const string REQUEST_DEFAULT_PRICING           = 'default_pricing';
    const string REQUEST_LOWERED_PRICE_POLICY      = 'lowered_price_policy';
    const string REQUEST_LOWERED_PRICE_POLICY_TYPE = 'policy_type';

    const string RESPONSE_STATUS               = 'status';
    const string RESPONSE_INDUSTRIES           = 'industries';
    const string RESPONSE_SERVICES             = 'services';
    const string RESPONSE_PRICES               = 'prices';
    const string RESPONSE_USE_FORMULAS         = 'use_formulas';
    const string RESPONSE_CONFIGURATION        = 'configuration';
    const string RESPONSE_VALID_SALE_TYPES     = 'sale_types';
    const string RESPONSE_NEEDS_INITIALISING   = 'needs_initialising';
    const string RESPONSE_NEEDS_REPAIR         = 'needs_repair';
    const string RESPONSE_DEFAULT_FORMULA      = 'default_formula';
    const string RESPONSE_QUALITY_TIERS        = 'quality_tiers';
    const string RESPONSE_PROPERTY_TYPES       = 'property_types';
    const string RESPONSE_DEFAULT_PRICING      = 'default_pricing';
    const string RESPONSE_MESSAGE              = 'message';

    public function __construct(
        Request                              $request,
        JsonAPIResponseFactory               $apiResponseFactory,
        public IndustryRepository            $industryRepository,
        public ProductRepository             $productRepository,
        public IndustryServiceRepository     $industryServiceRepository,
        public LocationRepository            $locationRepository,
        public FloorPricingManagementService $floorPricingService,
        public FloorPricingHistoryService $floorPricingHistoryService,
    ){
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param GetFloorPricingRequest $request
     * @return JsonResponse
     */
    public function getFloorPricing(GetFloorPricingRequest $request): JsonResponse
    {
        $filters = $request->safe()->collect();

        $productId = $this->getProductId($filters[self::REQUEST_PRODUCT] ?: ProductEnum::LEAD);
        $serviceProductId = $this->getServiceProductId($productId, $filters[self::REQUEST_INDUSTRY_SERVICE_ID]);
        $qualityTierId = $this->getQualityTierId($filters[self::REQUEST_QUALITY_TIER] ?? null, $productId);
        $propertyTypeId = $this->getPropertyTypeId($filters[self::REQUEST_PROPERTY_TYPE] ?? null);

        $stateLocationId = $filters[self::REQUEST_STATE_ID] ?? null;

        if (!$stateLocationId)
            [$prices, $needsRepair] = array_values($this->floorPricingService->getStatePrices($serviceProductId, $qualityTierId, $propertyTypeId, true));
        else
            $prices = $this->floorPricingService->getCountyPrices($serviceProductId, $qualityTierId, $propertyTypeId, $stateLocationId);

        return $this->formatResponse([
            self::RESPONSE_PRICES        => $prices,
            self::RESPONSE_CONFIGURATION => $this->getFrontEndConfiguration($serviceProductId, $needsRepair ?? false, count($prices)),
        ]);
    }

    /**
     * @param StoreFloorPricingRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function updateFloorPricing(StoreFloorPricingRequest $request): JsonResponse
    {
        $validated = $request->safe()->toArray();
        $regionType = $validated[self::REQUEST_REGION_TYPE];
        $productId = $this->getProductId($validated[self::REQUEST_PRODUCT] ?: ProductEnum::LEAD);
        $qualityTierId = $this->getQualityTierId($validated[self::REQUEST_QUALITY_TIER] ?? null, $productId);
        $propertyTypeId = $this->getPropertyTypeId($validated[self::REQUEST_PROPERTY_TYPE]?: PropertyTypeEnum::RESIDENTIAL);
        $serviceProductId = $this->getServiceProductId($productId, $validated[self::REQUEST_INDUSTRY_SERVICE_ID]);
        $stateLocationId = $validated[self::REQUEST_STATE_LOCATION_ID];
        $countyLocationId = $validated[self::REQUEST_COUNTY_LOCATION_ID];
        $updateInheritedPrice = ($validated[self::REQUEST_UPDATE_INHERITED_PRICE] && $regionType === Region::COUNTY->value);

        $priceUpdate = $this->transformScopedPriceUpdate($validated[self::REQUEST_PRICES]);

        $loweredFloorPolicy = $validated[self::REQUEST_LOWERED_PRICE_POLICY] ?? null;

        $updatedPrices = match ($regionType) {
            Region::NATIONAL->value => $this->floorPricingService->updateScopedNationalPrices($priceUpdate, $serviceProductId, $qualityTierId, $propertyTypeId),
            Region::STATE->value    => $this->floorPricingService->updateScopedStatePrices($priceUpdate, $stateLocationId, $serviceProductId, $qualityTierId, $propertyTypeId, $loweredFloorPolicy),
            Region::COUNTY->value   => $this->floorPricingService->updateScopedCountyPrices($priceUpdate, $stateLocationId, $countyLocationId, $serviceProductId, $qualityTierId, $propertyTypeId, $loweredFloorPolicy, $updateInheritedPrice),
            default                 => null,
        };

        return $this->formatResponse([
            self::RESPONSE_STATUS   => $updatedPrices != null,
            self::REQUEST_PRICES    => $updatedPrices,
        ]);
    }

    /**
     * @param GetFloorPricingRequest $request
     * @return JsonResponse
     */
    public function initialiseFloorPricing(GetFloorPricingRequest $request): JsonResponse
    {
        $validated = $request->safe()->toArray();

        /** @var Product $product */
        $product = Product::query()
            ->where(Product::FIELD_NAME, $validated[self::REQUEST_PRODUCT])
            ->firstOrFail();

        $serviceProductId = ServiceProduct::query()
            ->where(ServiceProduct::FIELD_PRODUCT_ID, $product->id)
            ->where(ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $validated[self::REQUEST_INDUSTRY_SERVICE_ID])
            ->firstOrFail()
            ->id;

        $qualityTierId = $this->getQualityTierId($validated[self::REQUEST_QUALITY_TIER] ?? null, $product->id);
        $propertyTypeId = $this->getPropertyTypeId($validated[self::REQUEST_PROPERTY_TYPE]);

        $pricingInitialised = $this->floorPricingService->initialisePricingForServiceProduct($serviceProductId);
        $prices = $pricingInitialised
            ? $this->floorPricingService->getStatePrices($serviceProductId, $qualityTierId, $propertyTypeId)
            : [];

        return $this->formatResponse([
            self::RESPONSE_STATUS => $pricingInitialised,
            self::RESPONSE_PRICES => $prices,
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getDefaultFloorPricing(): JsonResponse
    {
        $table = $this->floorPricingService->getDefaultPricingTable();

        return $this->formatResponse([
            self::RESPONSE_STATUS          => !!$table,
            self::RESPONSE_DEFAULT_PRICING => $table,
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function saveDefaultFloorPricing(): JsonResponse
    {
        $this->checkUserHasModulePermission();

        $payload = $this->request->get(self::REQUEST_DEFAULT_PRICING);
        $userId = Auth::user()->id;

        $success = $this->floorPricingService->saveDefaultPricingTable($payload, $userId);

        return $this->formatResponse([
            self::RESPONSE_STATUS => $success,
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function importFloorPricing(): JsonResponse
    {
        $this->checkUserHasModulePermission();

        $productId = $this->getProductId($this->request->get(self::REQUEST_PRODUCT));
        $fromServiceProductId = $this->getServiceProductId($productId, $this->request->get(self::REQUEST_FROM_INDUSTRY_SERVICE_ID));
        $toServiceProductId = $this->getServiceProductId($productId, $this->request->get(self::REQUEST_TO_INDUSTRY_SERVICE_ID));

        return $this->formatResponse([
            self::RESPONSE_STATUS => $this->floorPricingService->importAllPricesFromServiceProduct($fromServiceProductId, $toServiceProductId)
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function repairFloorPricing(): JsonResponse
    {
        $this->checkUserHasModulePermission();

        $productName = $this->request->get(self::REQUEST_PRODUCT);
        $productId = $this->getProductId($productName);
        $serviceProductId = $this->getServiceProductId($productId, $this->request->get(self::REQUEST_INDUSTRY_SERVICE_ID));

        [$result, $message] = $this->floorPricingService->repairPricesForServiceProduct($serviceProductId);

        return $this->formatResponse([
            self::RESPONSE_STATUS  => $result,
            self::RESPONSE_MESSAGE => $message,
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getExportableIndustryServices(): JsonResponse
    {
        $productId = $this->getProductId($this->request->get(self::REQUEST_PRODUCT));

        [$industries, $services] = $this->floorPricingService->getExportableIndustryServices($productId);

        return $this->formatResponse([
            self::RESPONSE_STATUS     => true,
            self::RESPONSE_INDUSTRIES => $industries,
            self::RESPONSE_SERVICES   => $services,
        ]);
    }

    /**
     * @param int $serviceProductId
     * @param bool $needsRepair
     * @param int $pricesCount
     * @return array
     */
    protected function getFrontEndConfiguration(int $serviceProductId, bool $needsRepair, int $pricesCount): array
    {
        return [
            self::RESPONSE_USE_FORMULAS       => FloorPricingManagementService::shouldUseFormulaPricing($serviceProductId),
            self::RESPONSE_VALID_SALE_TYPES   => SaleTypes::byServiceProductId($serviceProductId, SaleTypes::RETURN_TYPE_KEY),
            self::RESPONSE_DEFAULT_FORMULA    => FloorPriceFormula::defaultAppointmentFormula()->toArray(),
            self::RESPONSE_QUALITY_TIERS      => QualityTierEnum::byServiceProductId($serviceProductId),
            self::RESPONSE_PROPERTY_TYPES     => array_values(PropertyTypeEnum::cases()),
            self::RESPONSE_NEEDS_INITIALISING => $pricesCount < 1,
            self::RESPONSE_NEEDS_REPAIR       => $pricesCount && $needsRepair,
        ];
    }

    /**
     * @param int $productId
     * @return int
     */
    protected function getDefaultQualityTierId(int $productId): int
    {
        $product = ProductEnum::tryFrom(Product::query()->findOrFail($productId)->name);
        $defaultTier = match($product) {
            ProductEnum::APPOINTMENT => QualityTierEnum::ONLINE,
            default                  => QualityTierEnum::STANDARD,
        };

        return QualityTier::query()
            ->where(QualityTier::FIELD_NAME, $defaultTier->value)
            ->firstOrFail()
            ->id;
    }

    /**
     * @return int
     */
    protected function getDefaultPropertyTypeId(): int
    {
        return PropertyType::query()
            ->where(PropertyType::FIELD_NAME, PropertyTypeEnum::RESIDENTIAL)
            ->firstOrFail()
            ->id;
    }


    /**
     * @param array $prices
     * @return array
     */
    protected function transformScopedPriceUpdate(array $prices): array
    {
        $validKeys = SaleTypes::allSaleTypeKeys();
        return collect($prices)->reduce(function($output, $price, $saleTypeKey) use (&$validKeys) {
            if (in_array($saleTypeKey, $validKeys))
                $output[$saleTypeKey] = $price[FloorPricingManagementTransformer::FLOOR_PRICING_KEY_EXPLICIT_PRICE] ?? null;

            return $output;
        }, []);
    }

    /**
     * @param string $productName
     * @return int
     */
    protected function getProductId(string $productName): int
    {
        return Product::query()->where(Product::FIELD_NAME, $productName)
            ->firstOrFail()
            ->id;
    }

    /**
     * @param int $productId
     * @param int $industryServiceId
     * @return int
     */
    protected function getServiceProductId(int $productId, int $industryServiceId): int
    {
        return ServiceProduct::query()
            ->where(ServiceProduct::FIELD_PRODUCT_ID, $productId)
            ->where(ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $industryServiceId)
            ->firstOrFail()
            ->id;
    }

    /**
     * @param string|null $propertyTypeName
     * @return int
     */
    protected function getPropertyTypeId(?string $propertyTypeName): int
    {
        return $propertyTypeName
            ? PropertyType::query()->where(Product::FIELD_NAME, $propertyTypeName)
                ->firstOrFail()
                ->id
            : $this->getDefaultPropertyTypeId();
    }

    /**
     * @param string|null $qualityTierName
     * @param int $productId
     * @return int
     */
    protected function getQualityTierId(?string $qualityTierName, int $productId): int
    {
        return $qualityTierName
            ? QualityTier::query()->where(QualityTier::FIELD_NAME, $qualityTierName)
                ->firstOrFail()
                ->id
            : $this->getDefaultQualityTierId($productId);
    }

    /**
     * @return void
     */
    protected function checkUserHasModulePermission(): void
    {
        /** @var User $user */
        $user = Auth::user();

        if (!$user->hasPermissionTo(self::MODULE_PERMISSION))
            throw new UnauthorizedException("User is not authorised for this action.");
    }

    /**
     * @param GetFloorPriceHistoryRequest $request
     * @return JsonResponse
     */
    public function getFloorPriceHistory(GetFloorPriceHistoryRequest $request): JsonResponse
    {
        $filters = $request->validated();

        $productId = $this->getProductId(Arr::get($filters, self::REQUEST_PRODUCT, ProductEnum::LEAD->value));
        $serviceProductId = $this->getServiceProductId($productId, Arr::get($filters, self::REQUEST_INDUSTRY_SERVICE_ID));
        $qualityTierId = $this->getQualityTierId(Arr::get($filters, self::REQUEST_QUALITY_TIER), $productId);
        $propertyTypeId = $this->getPropertyTypeId(Arr::get($filters, self::REQUEST_PROPERTY_TYPE));
        $stateLocationId = Arr::get($filters, self::REQUEST_STATE_ID);

        $historyLogs = $this->floorPricingHistoryService->getFloorPriceHistoryLogs(
            serviceProductId: $serviceProductId,
            qualityTierId   : $qualityTierId,
            propertyTypeId  : $propertyTypeId,
            stateLocationId : $stateLocationId,
        );

        return $this->formatResponse([
            'history' => FloorPricingHistoryLogsResource::collection($historyLogs)
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getIndustryServicesByProduct(): JsonResponse
    {
        $industryServices = Product::query()
            ->join(ServiceProduct::TABLE, ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_PRODUCT_ID, '=', Product::TABLE .'.'. Product::FIELD_ID)
            ->join(IndustryService::TABLE, IndustryService::TABLE .'.'. IndustryService::FIELD_ID, '=', ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_INDUSTRY_SERVICE_ID)
            ->join(Industry::TABLE, Industry::TABLE .'.'. Industry::FIELD_ID, '=', IndustryService::TABLE .'.'. IndustryService::FIELD_INDUSTRY_ID)
            ->select([IndustryService::TABLE.".name as industry_service_name",
                IndustryService::TABLE.".id as industry_service_id",
                Product::TABLE.".name as product_name",
                Product::TABLE.".id as product_id",
                Industry::TABLE.".id as industry_id",
                Industry::TABLE.".name as industry_name"
            ])->get()
            ->groupBy(['product_name', 'industry_name']);

        return $this->formatResponse([
            'status'    => true,
            'services'  => $industryServices,
        ]);
    }
}
