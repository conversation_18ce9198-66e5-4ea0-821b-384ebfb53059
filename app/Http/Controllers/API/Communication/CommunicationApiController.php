<?php

namespace App\Http\Controllers\API\Communication;

use App\Contracts\Services\Communication\CommunicationContract;
use App\Enums\CommunicationRelationTypes;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\GetContactUrlRequest;
use App\Http\Requests\Odin\CreateOutboundSMSRequest;
use App\Http\Requests\Odin\GetCommunicationLogFurtherDataRequest;
use App\Http\Requests\Odin\GetCommunicationLogsRequest;
use App\Http\Resources\Odin\CommunicationLogResource;
use App\Http\Requests\Odin\LookupContactCommunicationRequest;
use App\Http\Requests\Odin\UpdateCallEntityRelationRequest;
use App\Http\Requests\Odin\UpdateOutboundCallRequest;
use App\Http\Resources\SalesOverview\TextResource;
use App\Models\Call;
use App\Models\Phone;
use App\Models\Voicemail;
use App\Models\User;
use App\Repositories\CommunicationRepository;
use App\Services\Communication\CommunicationService;
use App\Services\PubSub\PubSubService;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Throwable;

class CommunicationApiController extends APIController
{

    const SERVICE_NAME       = 'service_name';
    const USER_PHONE_NUMBER  = 'user_phone_number';
    const OTHER_PHONE_NUMBER = 'other_phone_number';
    const EXTERNAL_REFERENCE = 'external_reference';
    const CALL_RESULT        = 'call_result';
    const REQUEST_OTHER      = 'other';
    const SMS_BODY           = 'sms_body';
    const RELATION_TYPE      = 'relation_type';
    const RELATION_ID        = 'relation_id';

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected CommunicationRepository $repository,
        protected CommunicationService $service
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    public function updateOutboundCall(UpdateOutboundCallRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $saved = $this->service->updateOrCreateOutboundCall(
            $validated[self::SERVICE_NAME],
            $validated[self::USER_PHONE_NUMBER],
            $validated[self::OTHER_PHONE_NUMBER],
            $validated[self::EXTERNAL_REFERENCE],
            $validated[self::CALL_RESULT],
            $validated[self::RELATION_TYPE] ?? null,
            $validated[self::RELATION_ID] ?? null
        );

        return $this->formatResponse([
            'call_log_id' => $saved->{Call::FIELD_ID}
        ]);
    }

    /**
     * @param PubSubService $pubSubService
     */
    public function updateInboundCall(PubSubService $pubSubService): JsonResponse
    {
        $this->request->validate([
            self::SERVICE_NAME       => ['required', Rule::in(Call::EXTERNAL_TYPES)],
            self::USER_PHONE_NUMBER  => ['required'],
            self::OTHER_PHONE_NUMBER => ['required'],
            self::EXTERNAL_REFERENCE => ['required'],
            self::CALL_RESULT        => ['required', Rule::in(Call::RESULTS)]
        ]);

        $callLogId = $this->service->updateOrCreateInboundCall(
            $this->request->get(self::SERVICE_NAME),
            $this->request->get(self::USER_PHONE_NUMBER),
            $this->request->get(self::OTHER_PHONE_NUMBER),
            $this->request->get(self::EXTERNAL_REFERENCE),
            $this->request->get(self::CALL_RESULT)
        );

        if($callLogId > 0
        && in_array($this->request->get(self::CALL_RESULT), [Call::RESULT_BUSY, Call::RESULT_MISSED, Call::RESULT_VOICEMAIL], true)) {
            $call = Call::query()->with(Call::RELATION_PHONE)->findOrFail($callLogId);

            $pubSubService->handle(
                EventCategory::INTERNAL->value,
                EventName::MISSED_CALL->value,
                [
                    'relation_type' => $call?->{Call::FIELD_RELATION_TYPE},
                    'relation_id' => $call?->{Call::FIELD_RELATION_ID},
                    'from_phone' => $call->{Call::FIELD_OTHER_NUMBER},
                    'to_phone' => $call->{Call::RELATION_PHONE}->{Phone::FIELD_PHONE},
                    'company_reference' => $call?->{Call::FIELD_RELATION_TYPE} === Call::RELATION_COMPANY ? (int) $call?->{Call::RELATION_COMPANY}?->reference : '',
                    'lead_id' => $call?->{Call::FIELD_RELATION_TYPE} === Call::RELATION_LEAD ? (int) $call?->{Call::FIELD_RELATION_ID} : 0
                ]
            );
        }

        return $this->formatResponse([
            'call_log_id' => $callLogId
        ]);
    }

    /**
     * Lookup a caller by their number.
     *
     * @param LookupContactCommunicationRequest $request
     * @return JsonResponse
     */
    public function lookupCaller(LookupContactCommunicationRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $number = $validated[LookupContactCommunicationRequest::FIELD_OTHER];

        /** @var User $user */
        $user = Auth::user();

        $result = $this->service->identifyContactByPhoneNumber($user, $number);


        return $this->formatResponse($result);
    }

    /**
     * Returns the voicemail for a given user.
     *
     * @return JsonResponse
     */
    public function getVoicemails(): JsonResponse
    {
        return $this->formatResponse(
            $this->repository->getVoicemailsByUserId(Auth::id())->toArray()
        );
    }

    /**
     * @return JsonResponse
     * @throws Throwable
     */
    public function createOutboundSMS(CreateOutboundSMSRequest $request): JsonResponse
    {
        $validated = $request->validated();
        /** @var User $user */
        $user = Auth::user();
        return $this->service->deliverSMSFromUser(
                $user,
                $validated[self::OTHER_PHONE_NUMBER],
                $validated[self::SMS_BODY],
                $validated[self::RELATION_TYPE] ?? null,
                $validated[self::RELATION_ID] ?? null,
            );
    }

    /**
     * @param int $voicemailId
     * @return JsonResponse
     */
    public function markVoicemailHeard(int $voicemailId): JsonResponse
    {
        if($this->repository->getVoicemailsByUserId(Auth::id())->pluck(Voicemail::FIELD_ID)->contains($voicemailId)){
            return $this->formatResponse([$this->repository->markVoicemailRead($voicemailId)]);
        }else{
            return $this->formatResponse([false]);
        }
    }

    /**
     * @param int $leadId
     * @return JsonResponse
     */
    public function getCallRecordingsForLead(int $leadId): JsonResponse
    {
        $recordings = $this->repository->getCallRecordingsByLeadId($leadId);
        return $this->formatResponse($recordings->toArray());
    }

    /**
     * Get user communication logs
     * @param GetCommunicationLogsRequest $request
     * @return AnonymousResourceCollection
     * @throws Exception
     */
    public function getLogs(GetCommunicationLogsRequest $request): AnonymousResourceCollection
    {
        $filters = $request->validated();

        /** @var User $user */
        $user = Auth::user();

        /** @var int[] $allUniqueUserPhonesIds */
        $allUniqueUserPhonesIds = $user->phones(true)
            ->select(Phone::TABLE . '.' . Phone::FIELD_ID)
            ->distinct()
            ->get()
            ->pluck(Phone::FIELD_ID)
            ->toArray();

        abort_if(empty($allUniqueUserPhonesIds), 404, 'No phones found for user');

        $logs = $this->repository->getLogsByPhones($allUniqueUserPhonesIds, $filters);

        return CommunicationLogResource::collection($logs);
    }

    /**
     * Retrieve additional data the log to enable users to identify the contact
     * @param GetCommunicationLogFurtherDataRequest $request
     * @return array
     * @throws Exception
     */
    public function getLogFurtherData(GetCommunicationLogFurtherDataRequest $request): array
    {
        $filters = $request->validated();

        return $this->service->getLogFurtherData($filters['id'], $filters['type']);
    }

    /**
     * Update communication log relation
     */
    public function updateCallEntityRelation(UpdateCallEntityRelationRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $this->service->updateCallEntityRelation($validated);

        return $this->formatResponse([
            'success' => true
        ]);
    }

    /**
     * Return text messages for a given phone number
     * @param string $phoneNumber
     * @return JsonResponse
     */
    public function getSMSHistoryForPhoneNumber(string $phoneNumber): JsonResponse
    {
        $texts = $this->repository->getSMSHistoryForNumber($phoneNumber);

        return $this->formatResponse([
            "status"   => true,
            "messages" => TextResource::collection($texts),
        ]);
    }

    /**
     * @param GetContactUrlRequest $request
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getContactUrlByPhoneNumber(GetContactUrlRequest $request): JsonResponse
    {
        $validated = $request->validated();

        /** @var CommunicationService $communicationService */
        $communicationService = app()->make(CommunicationService::class);

        $relation = $communicationService->getRedirectUrl(
            CommunicationRelationTypes::tryFrom(Arr::get($validated, GetContactUrlRequest::RELATION_TYPE)),
            Arr::get($validated, GetContactUrlRequest::RELATION_ID),
        );

        return $this->formatResponse([
            "status" => !!$relation,
            "url"    => $relation,
        ]);
    }

    /**
     * Handles generating an auth token for a given lead processor.
     *
     * @param CommunicationContract   $contract
     * @return JsonResponse
     */
    public function token(CommunicationContract $contract): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();
        $phoneNumber = $user->primaryPhone()->phone ?? '';

        return $this->formatResponse([
            "status" => true,
            "token" => $contract->retrieveWebPhoneToken([$phoneNumber]),
            "number" => $phoneNumber
        ]);
    }
}
