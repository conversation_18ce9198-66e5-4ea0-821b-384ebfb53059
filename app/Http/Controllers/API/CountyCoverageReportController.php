<?php

namespace App\Http\Controllers\API;

use App\Enums\Odin\Industry;
use App\Models\Odin\Industry as IndustryModel;
use App\Enums\Reports\CountyCoverageReport\CountyCoverageReportColumnEnum;
use App\Enums\UserPresetType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Requests\Reports\CountyCoverageReportRequest;
use App\Http\Requests\StorePresetFilterRequest;
use App\Http\Resources\UserPresetFilterResource;
use App\Models\User;
use App\Repositories\EstimatedRevenuePerLeadByLocationRepository;
use App\Repositories\UserPresetRepository;
use App\Services\Filterables\Reports\CountyCoverageReport\CountyCoverageReportFilterableService;
use App\Services\Reports\CountyCoverageReportService;
use App\Services\UserPresetService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\StreamedResponse;

class CountyCoverageReportController extends APIController
{
    const string REQUEST_NAME               = 'name';
    const string REQUEST_VALUE              = 'value';
    const string RESPONSE_STATUS            = 'status';
    const string RESPONSE_MESSAGE           = 'message';
    const string RESPONSE_PRESETS           = 'presets';
    const string RESPONSE_INDUSTRIES        = 'industries';
    const string RESPONSE_COLUMNS           = 'columns';
    const string RESPONSE_ORDER             = 'column_order';
    const string RESPONSE_FILTERS           = 'filters';
    const string RESPONSE_FILTER_UPDATES    = 'filter_updates';

    const string RESPONSE_REPORT            = 'report';

    const string RESPONSE_ID    = 'id';
    const string RESPONSE_NAME  = 'name';

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param CountyCoverageReportService $countyCoverageReportService
     * @param CountyCoverageReportFilterableService $countyCoverageReportFilterableService
     * @param UserPresetRepository $userPresetRepository
     * @param UserPresetService $userPresetService
     */
    public function __construct(
        Request                                         $request,
        JsonAPIResponseFactory                          $apiResponseFactory,
        protected CountyCoverageReportService           $countyCoverageReportService,
        protected CountyCoverageReportFilterableService $countyCoverageReportFilterableService,
        protected UserPresetRepository                  $userPresetRepository,
        protected UserPresetService                     $userPresetService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @return JsonResponse
     */
    public function getReportOptions(): JsonResponse
    {
        $filters = $this->countyCoverageReportFilterableService->getDisplayData();
        $industries = [];
        foreach (Industry::slugsList() as $key => $value) {
            if (in_array(Industry::from($value), CountyCoverageReportColumnEnum::activeIndustries())) {
                $industries[] = [
                    self::RESPONSE_ID => $key,
                    self::RESPONSE_NAME => $value
                ];
            }
        }

        return $this->formatResponse([
            self::RESPONSE_INDUSTRIES   => $industries,
            self::RESPONSE_COLUMNS      => CountyCoverageReportColumnEnum::getFormattedCases(),
            self::RESPONSE_ORDER        => CountyCoverageReportColumnEnum::getColumnsOrder(),
            self::RESPONSE_FILTERS      => $filters,
            self::RESPONSE_PRESETS      => $this->getUserPresets(),
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getFilterOptionUpdates(Request $request): JsonResponse
    {
        $filterOptions = $request->get(CountyCoverageReportRequest::FILTERS);

        $this->countyCoverageReportFilterableService->runQuery($filterOptions);
        $filters = $this->countyCoverageReportFilterableService->getFilterOptionUpdates();

        return $this->formatResponse([
            self::RESPONSE_STATUS         => true,
            self::RESPONSE_FILTER_UPDATES => $filters ?? null
        ]);
    }

    /**
     * @param CountyCoverageReportRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function getReport(CountyCoverageReportRequest $request): JsonResponse
    {
        set_time_limit(60);

        $data = $request->safe()->all();

        $report = $this->countyCoverageReportService->buildReport(
            industry:   $request->getIndustry(),
            filters:    $request->getFilters(),
            columns:    $request->getColumns(),
            companyId:  $data[CountyCoverageReportRequest::COMPANY] ?? null,
            campaigns:  $data[CountyCoverageReportRequest::CAMPAIGNS] ?? null,
        );

        return $this->formatResponse([
            self::RESPONSE_STATUS   => true,
            self::RESPONSE_REPORT   => $report,
            self::RESPONSE_COLUMNS  => CountyCoverageReportColumnEnum::getFormattedCases(),
            self::RESPONSE_ORDER    => CountyCoverageReportColumnEnum::getColumnsOrder(),
        ]);
    }

    /**
     * @param StorePresetFilterRequest $request
     * @return JsonResponse
     */
    public function savePreset(StorePresetFilterRequest $request): JsonResponse
    {
        $presetValue = $request->get(self::REQUEST_VALUE);
        $name = $request->get('name', 'User Filter');

        /** @var User $user */
        $user = Auth::user();
        $success = !!$this->userPresetRepository->savePreset(
            $user->id,
            UserPresetType::FILTER,
            $name,
            $presetValue,
            CountyCoverageReportFilterableService::FILTERABLE_CATEGORY,
        );
        return $this->formatResponse([
            self::RESPONSE_STATUS => $success,
            self::RESPONSE_PRESETS => $this->getUserPresets(),
        ]);
    }

    /**
     * @return ResourceCollection
     */
    public function getUserPresets(): ResourceCollection
    {
        /** @var User $user */
        $user = Auth::user();
        $presets = $this->userPresetRepository->getPresetsByTypeAndCategory(UserPresetType::FILTER,
            CountyCoverageReportFilterableService::FILTERABLE_CATEGORY, $user->id);

        return UserPresetFilterResource::collection($presets);
    }

    /**
     * @return JsonResponse
     */
    public function deletePreset(): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();
        $name = $this->request->get(self::REQUEST_NAME);

        $deleted = $this->userPresetRepository->deletePreset(
            $user->id,
            UserPresetType::FILTER,
            $name,
            CountyCoverageReportFilterableService::FILTERABLE_CATEGORY,
        );

        return $this->formatResponse([
            self::RESPONSE_STATUS => $deleted,
            self::RESPONSE_PRESETS => $this->getUserPresets(),
        ]);
    }

    /**
     * @param EstimatedRevenuePerLeadByLocationRepository $erplRepository
     * @param string $industry
     * @return StreamedResponse
     */
    public function downloadErplZipCodes(EstimatedRevenuePerLeadByLocationRepository $erplRepository, string $industry): StreamedResponse
    {
        $data = $erplRepository->getErplZipData(Industry::fromSlug($industry)->model()->{IndustryModel::FIELD_ID});

        $handle = fopen('php://output', 'w');
        foreach ($data as $row) {
            fputcsv($handle, $row);
        }

        return response()->stream(function () use ($handle) {
            fclose($handle);
        }, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment;',
            'Pragma' => 'no-cache',
            'Expires' => '0',
        ]);
    }
}
