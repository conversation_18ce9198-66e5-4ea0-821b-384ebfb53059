<?php

namespace App\Http\Controllers\API\CompanyRegistration;

use App\Events\CompanyRegistration\V3\PhoneVerified;
use App\Events\CompanyRegistration\V3\RegistrationCompleted;
use App\Events\CompanyRegistration\V3\RegistrationStarted;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\CompanyRegistration\V3\CreateNewBuyerProspectRequest;
use App\Http\Requests\CompanyRegistration\V3\UpdateNewBuyerProspectRequest;
use App\Jobs\CompanyRegistration\V3\SendRegistrationEmailVerificationEmail;
use App\Models\Legacy\Location;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Prospects\NewBuyerProspect;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\IndustryRepository;
use App\Services\CompanyRegistration\CompanyRegistrationV3Service;
use App\Services\Dashboard\DashboardLoginTokenService;
use App\Services\Twilio\TwilioVerify;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;
use Twilio\Exceptions\TwilioException;

class CompanyRegistrationV3Controller extends APIController
{
    const string RESPONSE_STATES = 'states';
    const string RESPONSE_INDUSTRIES = 'industries';
    const string RESPONSE_STATUS = 'status';
    const string RESPONSE_REFERENCE = 'reference';
    const string RESPONSE_USER_REFERENCE = 'user_reference';
    const string RESPONSE_EMAIL_CAN_LOG_IN = 'email_can_log_in';
    const string RESPONSE_IS_DUPLICATE = 'is_duplicate';

    const string REQUEST_CREATE_USER = 'create_user';
    const string REQUEST_PASSWORD = 'password';
    const string REQUEST_CODE = 'code';
    const string REQUEST_TOKEN = 'token';

    /**
     * @param LocationRepository $locationRepository
     * @param IndustryRepository $industryRepository
     *
     * @return JsonResponse
     */
    public function initRegistration(LocationRepository $locationRepository, IndustryRepository $industryRepository): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_STATES => $locationRepository->getStates()->map(fn(Location $stateLocation) => [
                'abbr' => $stateLocation->state_abbr,
                'name' => $stateLocation->state
            ])->sort()->toArray(),
            self::RESPONSE_INDUSTRIES => $industryRepository->getIndustriesWithServicesForRegistration()->map(
                fn(Industry $industry) => [
                    'name' => $industry->name,
                    'slug' => $industry->slug,
                    'services' => $industry->services->map(
                        fn(IndustryService $service) => [
                            'name' => $service->name,
                            'slug' => $service->slug
                        ]
                    )
                ]
            )
        ]);
    }

    /**
     * @param CreateNewBuyerProspectRequest $request
     * @param CompanyRegistrationV3Service $service
     * @param TwilioVerify $twilioVerify
     *
     * @return JsonResponse
     * @throws TwilioException
     */
    public function createBuyerProspect(CreateNewBuyerProspectRequest $request, CompanyRegistrationV3Service $service, TwilioVerify $twilioVerify): JsonResponse
    {
        $emailCanLogin = $service->emailCanLogin($request->validated(NewBuyerProspect::SOURCE_KEY_EMAIL));

        if ($emailCanLogin) {
            try {
                $twilioVerify->sendVerificationCode($request->validated(NewBuyerProspect::SOURCE_KEY_PHONE));
            } catch (Exception $exception) {
                logger()->error("Failed to send phone verification text. error: {$exception->getMessage()}");

                return response()->json([
                    'message' => 'Phone number appears to be invalid. Please re-enter and try again'
                ], 422);
            }
        }

        $newBuyerProspect = $service->createNewBuyerProspect($request);

        // Temporary fix: The queue frontend currently uses the 'external_reference' field instead of 'reference'.
        // TODO: Update the queue to use 'reference' directly and remove this assignment.
        $newBuyerProspect->external_reference = $newBuyerProspect->reference;
        $newBuyerProspect->save();

        RegistrationStarted::dispatch($newBuyerProspect);

        return $this->formatResponse([
            self::RESPONSE_STATUS => true,
            self::RESPONSE_REFERENCE => $newBuyerProspect->reference,
            self::RESPONSE_EMAIL_CAN_LOG_IN => $emailCanLogin,
            self::RESPONSE_IS_DUPLICATE => $service->duplicateCompany($newBuyerProspect)
        ]);
    }

    /**
     * @param UpdateNewBuyerProspectRequest $request
     * @param CompanyRegistrationV3Service $service
     * @param NewBuyerProspect $newBuyerProspect
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function updateBuyerProspect(UpdateNewBuyerProspectRequest $request, CompanyRegistrationV3Service $service, NewBuyerProspect $newBuyerProspect): JsonResponse
    {
        if ($this->request->has(self::REQUEST_CREATE_USER)) {
            $service->updateBuyerProspect($request, $newBuyerProspect);

            return $this->handleUserCreate($newBuyerProspect, $service);
        }

        return $this->formatResponse([
            self::RESPONSE_STATUS => $service->updateBuyerProspect($request, $newBuyerProspect)
        ]);
    }

    /**
     * @param NewBuyerProspect $newBuyerProspect
     * @param TwilioVerify $twilioVerify
     *
     * @return JsonResponse
     * @throws TwilioException
     */
    public function sendPhoneVerificationCode(NewBuyerProspect $newBuyerProspect, TwilioVerify $twilioVerify): JsonResponse
    {
        $twilioVerify->sendVerificationCode($newBuyerProspect->source_data[NewBuyerProspect::SOURCE_KEY_PHONE]);

        return $this->formatResponse();
    }

    /**
     * @param CompanyUser $companyUser
     * @param DashboardLoginTokenService $dashboardLoginTokenService
     *
     * @return JsonResponse
     */
    public function getDashboardLoginToken(CompanyUser $companyUser, DashboardLoginTokenService $dashboardLoginTokenService): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_STATUS => true,
            self::REQUEST_TOKEN => $dashboardLoginTokenService->generateToken($companyUser)
        ]);
    }

    /**
     * @param NewBuyerProspect $newBuyerProspect
     * @param TwilioVerify $twilioVerify
     *
     * @return JsonResponse
     * @throws TwilioException
     */
    public function verifyPhone(NewBuyerProspect $newBuyerProspect, TwilioVerify $twilioVerify): JsonResponse
    {
        $status = $twilioVerify->verifySMSCode($newBuyerProspect->source_data[NewBuyerProspect::SOURCE_KEY_PHONE], $this->request->get(self::REQUEST_CODE, ''));

        if ($status) {
            $newBuyerProspect->source_data = [...$newBuyerProspect->source_data, NewBuyerProspect::SOURCE_KEY_PHONE_VERIFIED => true];
            $newBuyerProspect->save();
            PhoneVerified::dispatch($newBuyerProspect);
            SendRegistrationEmailVerificationEmail::dispatch($newBuyerProspect);
        }

        return $this->formatResponse([
            self::RESPONSE_STATUS => $status
        ]);
    }

    /**
     * @param NewBuyerProspect $newBuyerProspect
     * @param CompanyRegistrationV3Service $service
     *
     * @return JsonResponse
     */
    public function verifyEmail(NewBuyerProspect $newBuyerProspect, CompanyRegistrationV3Service $service): JsonResponse
    {
        if ($service->emailIsRegistered($newBuyerProspect)) {
            return $this->formatResponse([
                self::RESPONSE_STATUS => 'registered'
            ]);
        }

        $newBuyerProspect->source_data = [...$newBuyerProspect->source_data, NewBuyerProspect::SOURCE_KEY_EMAIL_VERIFIED => true];

        $newBuyerProspect->save();

        return $this->formatResponse([
            self::RESPONSE_STATUS => 'verified'
        ]);
    }

    /**
     * @param NewBuyerProspect $newBuyerProspect
     * @param CompanyRegistrationV3Service $service
     *
     * @return JsonResponse
     */
    public function getLeadPrices(NewBuyerProspect $newBuyerProspect, CompanyRegistrationV3Service $service): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_STATUS => true,
            'prices' => $service->getLeadPrices($newBuyerProspect)
        ]);
    }

    /**
     * @param NewBuyerProspect $newBuyerProspect
     * @param CompanyRegistrationV3Service $service
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    protected function handleUserCreate(NewBuyerProspect $newBuyerProspect, CompanyRegistrationV3Service $service): JsonResponse
    {
        $this->validate($this->request, [
            self::REQUEST_PASSWORD => 'string|min:8|confirmed'
        ]);

        if (!$service->emailCanLogin($newBuyerProspect->source_data[NewBuyerProspect::SOURCE_KEY_EMAIL])) {
            throw new BadRequestException('Email is not valid');
        }

        $company = $service->createCompanyAndUser($newBuyerProspect, $this->request->get(self::REQUEST_PASSWORD));

        $this->syncToLegacy($company, $company->users()->first(), $service);

        RegistrationCompleted::dispatch($company);

        return $this->formatResponse([
            self::RESPONSE_STATUS => true,
            self::RESPONSE_USER_REFERENCE => $company->users()->first()->{CompanyUser::FIELD_REFERENCE}
        ]);
    }

    /**
     * @param Company $company
     * @param CompanyUser $companyUser
     * @param CompanyRegistrationV3Service $service
     *
     * @return void
     */
    protected function syncToLegacy(Company $company, CompanyUser $companyUser, CompanyRegistrationV3Service $service): void
    {
        $service->syncToLegacy($company, $companyUser);
    }
}
