<?php

namespace App\Http\Controllers\API\CompanyRegistration;

use App\Enums\CompanyUserVerificationMethod;
use App\Enums\ContractKeys;
use App\Enums\ContractType;
use App\Enums\EventCategory;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\Odin\SolarConfigurableFields;
use App\Factories\JsonAPIResponseFactory;
use App\Enums\EventName;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\AddServiceAreaFormRequest;
use App\Http\Requests\Bundles\CompanyRegistrationBundleRequest;
use App\Http\Requests\Bundles\PurchaseBundleRequest;
use App\Http\Requests\CompanyRegistration\AcceptCompanyContractRequest;
use App\Http\Requests\CompanyRegistration\GetCompanyContractRequest;
use App\Http\Resources\Bundles\BundleCollection;
use App\Jobs\CheckCompanyLeftRegistration;
use App\Models\Bundle;
use App\Models\BundleInvoice;
use App\Models\CompanyContract;
use App\Models\ContractKey;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Industry;
use App\Models\Odin\Website;
use App\Repositories\BundleManagement\BundleInvoiceRepository;
use App\Repositories\BundleManagement\BundleRepository;
use App\Repositories\CompanyContractRepository;
use App\Repositories\Legacy\CompanyAddressRepository;
use App\Repositories\Odin\CompanyIndustryServiceRepository;
use App\Repositories\Odin\CompanyLocationRepository;
use App\Repositories\Odin\CompanyRepository;
use App\Repositories\Odin\CompanyUserRepository;
use App\Rules\Phone;
use App\Services\BundleManagement\BundleService;
use App\Services\CompanyContractService;
use App\Services\CompanyLocationService;
use App\Services\CompanyRegistration\CompanyRegistrationService;
use App\Services\Dashboard\DashboardLoginTokenService;
use App\Services\Google\GeocodingService;
use App\Services\HelperService;
use App\Services\Legacy\LeadPriceService;
use App\Services\Legacy\Payments\CompanyBillingService;
use App\Services\Odin\API\OdinAuthoritativeAPILegacySyncService;
use App\Services\PubSub\PubSubService;
use App\Services\Verification\CompanyUserVerificationService;
use App\Services\CompanyRegistration\CompanyRegistrationSyncService;
use App\Transformers\Legacy\CompanyLocationToLegacyAddressTransformer;
use Exception;
use http\Exception\InvalidArgumentException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Client\RequestException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Spatie\LaravelPdf\Enums\Unit;
use Spatie\LaravelPdf\Facades\Pdf;

class CompanyRegistrationController extends APIController
{

    const REQUEST_COMPANY_REFERENCE     = 'company_reference';
    const REQUEST_COMPANY_NAME          = 'company_name';
    const REQUEST_ENTITY_NAME           = 'entity_name';
    const REQUEST_FIRST_NAME            = 'first_name';
    const REQUEST_LAST_NAME             = 'last_name';
    const REQUEST_EMAIL                 = 'email';
    const REQUEST_PASSWORD              = 'password';
    const REQUEST_PHONE                 = 'phone';
    const REQUEST_VERIFICATION_CODE     = 'verification_code';
    const REQUEST_DEPARTMENT            = 'department';
    const REQUEST_WATCHDOG_ID           = 'watchdog_id';
    const REQUEST_COMPANY_ID            = 'company_id';
    const REQUEST_COMPANY_SERVICES      = 'company_services';
    const REQUEST_COMPANY_INDUSTRIES    = 'company_industries';
    const REQUEST_COMPANY_LOCATION      = 'company_location';
    const REQUEST_LOCATION_NAME         = 'name';
    const REQUEST_LOCATION_PHONE        = 'phone';
    const REQUEST_COMPANY_SERVICE_AREA  = 'service_area';
    const REQUEST_ADDRESS_LINE_1        = 'address_1';
    const REQUEST_ADDRESS_LINE_2        = 'address_2';
    const REQUEST_ADDRESS_CITY          = 'city';
    const REQUEST_ADDRESS_STATE         = 'state';
    const REQUEST_ADDRESS_COUNTRY       = 'country';
    const REQUEST_ADDRESS_ZIPCODE       = 'zip_code';
    const REQUEST_STRIPE_TOKEN          = 'stripe_token';
    const REQUEST_LEAD_BUDGET           = 'lead_budget';
    const REQUEST_COMPANY_WEBSITE       = 'website';
    const REQUEST_COMPANY_LOGO          = 'link_to_logo';
    const REQUEST_COMPANY_EMAIL         = GlobalConfigurableFields::SALES_EMAIL;
    const REQUEST_SUPPORT_EMAIL         = GlobalConfigurableFields::TECH_SUPPORT_EMAIL;
    const REQUEST_COMPANY_DESCRIPTION   = GlobalConfigurableFields::DESCRIPTION;
    const REQUEST_YEAR_STARTED_BUSINESS = GlobalConfigurableFields::YEAR_STARTED_BUSINESS;
    const REQUEST_USER_REFERENCE        = 'user_reference';
    const REQUEST_USER_LEGACY_ID        = 'user_legacy_id';
    const REQUEST_REGISTRATION_ORIGIN   = 'registration_origin';
    const REQUEST_AGREED_CONTRACT       = 'agreed_contract';
    const REQUEST_CONTRACT_TYPE         = 'contract_type';
    const REQUEST_CONTRACT_REFERENCE    = 'contract_reference';

    const string REQUEST_CONTRACT_SIGNATURE_ID = 'contract_signature_id';
    const string REQUEST_CONTRACT_APP_ID       = 'contract_app_id';

    const REQUEST_EMPLOYEE_NUMBER               = 'employee_number';
    const REQUEST_REVENUE              = 'revenue';
    const REQUEST_BUYING_LEADS         = 'buying_leads';

    const EVENT_DATA_COMPANY_ID         = 'company_id';
    const EVENT_DATA_COMPANY_REFERENCE  = 'company_reference';
    const COMPANY_INCOMPLETE_REGISTRATION_DELAY = 10800;

    const PERMITTED_RADIUS_LIST = [5, 10, 20, 30, 50];

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected CompanyRegistrationService $companyRegistrationService,
        protected CompanyRepository $companyRepository,
        protected CompanyUserRepository $userRepository,
        protected CompanyLocationRepository $companyLocationRepository,
        protected CompanyUserVerificationService $verificationService,
        protected CompanyRegistrationSyncService $syncService,
        protected LeadPriceService $leadPriceService,
        protected CompanyContractRepository $companyContractRepository,
        protected CompanyContractService $contractContractService,
        protected BundleRepository $bundleRepository,
        protected BundleInvoiceRepository $bundleInvoiceRepository,
        protected BundleService $bundleService,
        protected HelperService $helperService,
        protected GeocodingService $geocodingService
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param CompanyRegistrationService $companyRegistrationService
     * @return JsonResponse
     * @throws ValidationException
     * @throws Exception
     */
    public function createBaseCompany(CompanyRegistrationService $companyRegistrationService, PubSubService $pubSubService): JsonResponse
    {
        $this->validate($this->request, [
            self::REQUEST_COMPANY_NAME    => "string|required",
            self::REQUEST_ENTITY_NAME     => "string|required",
            self::REQUEST_FIRST_NAME      => "string|required",
            self::REQUEST_LAST_NAME       => "string|required",
            self::REQUEST_EMAIL           => "email|required|unique:".CompanyUser::TABLE.",".CompanyUser::FIELD_EMAIL,
            self::REQUEST_DEPARTMENT      => "string|nullable",
            self::REQUEST_WATCHDOG_ID     => "string|nullable",
            self::REQUEST_COMPANY_WEBSITE => "string|nullable",
        ]);

        $company = $companyRegistrationService->registerBaseCompany(
            name: $this->request->get(self::REQUEST_COMPANY_NAME),
            entityName: $this->request->get(self::REQUEST_ENTITY_NAME),
            watchDogId: $this->request->get(self::REQUEST_WATCHDOG_ID),
            website: $this->request->get(self::REQUEST_COMPANY_WEBSITE),
            registrationDomain: $this->getRegistrationDomain()
        );

        $user = $companyRegistrationService->addCompanyUser(
            $company,
            $this->request->get(self::REQUEST_FIRST_NAME),
            $this->request->get(self::REQUEST_LAST_NAME),
            $this->request->get(self::REQUEST_EMAIL),
            $this->request->get(self::REQUEST_PASSWORD),
            $this->request->get(self::REQUEST_DEPARTMENT),
        );

        if ($company?->{Company::FIELD_REFERENCE}) {
            $legacyData = [
                'company'   => $this->syncService->transformForLegacy(Company::class, $company->toArray()),
                'user'      => $this->syncService->transformForLegacy(CompanyUser::class, $user->toArray()),
            ];

            // Do a synchronous request back to legacy to ensure we have legacy_id set, otherwise
            //   the payment request fails later in registration
            $legacyResponse = $this->syncService->post(
                '/create-company-and-user',
                $legacyData,
                3,
                100,
                10
            )?->json() ?? [];

            if ($legacyResponse['status'] ?? false) {
                $company->update([Company::FIELD_LEGACY_ID => $legacyResponse['legacy_company_id']]);
                $user->update([CompanyUser::FIELD_LEGACY_ID => $legacyResponse['legacy_user_id']]);
            }
            else {
                logger()->error("Company Registration of '$company->name' could not continue due to a failure to sync the Company/User to legacy.");
                $company->delete();
                $user->forceDelete();
                throw new Exception("There was an error reaching the legacy server. Please try re-submitting.");
            }
        }

        if ($user?->{CompanyUser::FIELD_EMAIL}) {
            $sentVerification = $this->verificationService->sendVerificationNotification($user, CompanyUserVerificationMethod::EMAIL, true);
        }

        $pubSubService->handle(
            EventCategory::COMPANIES->value,
            EventName::STARTED_REGISTRATION->value,
            [
                self::EVENT_DATA_COMPANY_ID         => $company->id,
                self::EVENT_DATA_COMPANY_REFERENCE  => $company->reference,
            ]
        );

        CheckCompanyLeftRegistration::dispatch($company->id, $company->reference)
            ->delay(self::COMPANY_INCOMPLETE_REGISTRATION_DELAY);

        return $this->formatResponse([
            self::REQUEST_COMPANY_REFERENCE  => $company->{Company::FIELD_REFERENCE} ?? '',
            self::REQUEST_USER_REFERENCE     => $user->{CompanyUser::FIELD_REFERENCE} ?? '',
            'verification_sent' => $sentVerification ?? false,
        ]);
    }

    /**
     * Return a list of Industry names the Company is attached to
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getCompanyIndustries(): JsonResponse
    {
        $this->validate($this->request, [
            self::REQUEST_COMPANY_REFERENCE  => 'required|string|size:10'
        ]);
        $company = $this->companyRepository->findByLegacyReferenceOrFail($this->request->get(self::REQUEST_COMPANY_REFERENCE));
        $industries = $company->{Company::RELATION_INDUSTRIES}?->pluck(Industry::FIELD_NAME) ?? collect();
        $allIndustries = Industry::all()->pluck(Industry::FIELD_NAME);

        return $this->formatResponse([
            'status'     => true,
            'active_industries' => $industries->toArray(),
            'all_industries'    => $allIndustries->toArray(),
        ]);
    }

    /**
     * @param CompanyIndustryServiceRepository $serviceRepository
     * @return JsonResponse
     * @throws ValidationException
     * @throws Exception
     */
    public function updateCompanyIndustries(CompanyIndustryServiceRepository $serviceRepository): JsonResponse
    {

        $this->validate($this->request, [
            self::REQUEST_COMPANY_REFERENCE  => 'required|string|size:10',
            self::REQUEST_COMPANY_INDUSTRIES => 'array',
        ]);

        $company = $this->companyRepository->findByLegacyReferenceOrFail($this->request->get(self::REQUEST_COMPANY_REFERENCE));
        $industriesRequested = $this->request->get(self::REQUEST_COMPANY_INDUSTRIES);

        $industriesAdded = $this->companyRegistrationService->refreshCompanyIndustriesByNames($company, $industriesRequested);

        return $this->formatResponse([
            'status'        => count($industriesRequested) === $industriesAdded,
            'message'       => count($industriesRequested) === $industriesAdded
                ? ''
                : 'Not all requested Industries were added successfully.'
        ]);
    }

    /**
     * Get a Company's IndustryServices
     * Only returns names & slugs, grouped by Industry
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getCompanyServices(): JsonResponse
    {
        $this->validate($this->request,[
            self::REQUEST_COMPANY_REFERENCE => 'required|string|size:10',
        ]);
        $company = $this->companyRepository->findByLegacyReferenceOrFail($this->request->get(self::REQUEST_COMPANY_REFERENCE));

        $servicesGroupedByIndustry = $this->companyRegistrationService->getCompanyServiceNamesByIndustry($company);

        return $this->formatResponse([
            'status'          => true,
            'active_services' => $servicesGroupedByIndustry['activeServices'] ?? [],
            'all_services'    => $servicesGroupedByIndustry['allServices'] ?? [],
        ]);
    }

    /**
     * Accepts a list of IndustryService slugs and updates the Company
     * Performs an absolute update - any Services not in Request will be detached
     * This will also attach any missing Industries required by selected Services
     * @return JsonResponse
     * @throws ValidationException
     * @throws Exception
     */
    public function updateCompanyServices(): JsonResponse
    {
        $this->validate($this->request, [
            self::REQUEST_COMPANY_REFERENCE => 'required|string|size:10',
            self::REQUEST_COMPANY_SERVICES  => 'array',
        ]);
        $company = $this->companyRepository->findByLegacyReferenceOrFail($this->request->get(self::REQUEST_COMPANY_REFERENCE));
        $requestedServices = $this->request->get(self::REQUEST_COMPANY_SERVICES);

        $servicesAdded = $this->companyRegistrationService->refreshCompanyServicesBySlugs($company, $requestedServices);

        return $this->formatResponse([
            'status'    => count($requestedServices) === $servicesAdded,
            'message'   => count($requestedServices) === $servicesAdded
                ? ''
                : 'Not all requested Services were added.',
        ]);
    }

    /**
     * Re-send verification email to a user if required
     * @return JsonResponse
     * @throws ValidationException
     */
    public function sendEmailVerification(): JsonResponse
    {
        $this->validate($this->request, [
            self::REQUEST_EMAIL      => 'required|email',
        ]);

        $user = $this->userRepository->findCompanyUserByEmailOrFail($this->request->get(self::REQUEST_EMAIL));
        $verificationSent = $this->verificationService->sendVerificationNotification($user, CompanyUserVerificationMethod::EMAIL);

        return $this->formatResponse([
            'verification_sent'     => $verificationSent,
        ]);
    }

    /**
     * @param int $id
     * @return JsonResponse
     */
    public function verifyEmailAddress(int $id): JsonResponse
    {
        $user = $this->userRepository->findCompanyUserByIdOrFail($id);

        $status = $this->verificationService->verifyCompanyUser($user, CompanyUserVerificationMethod::EMAIL);

        return $this->formatResponse([
            'status'    => $status ?? false,
        ]);
    }


    /**
     * Check for similar companies during company registration
     * @return JsonResponse
     * @throws ValidationException
     */
    public function checkSimilarCompanies(): JsonResponse {
        $this->validate($this->request, [
            self::REQUEST_COMPANY_NAME    => 'required|string',
            self::REQUEST_COMPANY_WEBSITE => 'nullable|string',
        ]);
        $similarCompanies = $this->companyRegistrationService->checkSimilarCompanies($this->request->get(self::REQUEST_COMPANY_NAME), $this->request->get(self::REQUEST_COMPANY_WEBSITE));

        return $this->formatResponse([
            'status'    => true,
            'companies' => $similarCompanies,
        ]);
    }

    /**
     * @return JsonResponse
     * @throws ModelNotFoundException | Exception
     */
    public function checkRegistrationStatus(): JsonResponse
    {
        $this->validate($this->request, [
            self::REQUEST_COMPANY_REFERENCE    => 'required|integer',
        ]);
        $company = $this->companyRepository->findByLegacyReferenceOrFail($this->request->get(self::REQUEST_COMPANY_REFERENCE));

        $registrationCheckResults = $this->companyRegistrationService->doCompanyRegistrationChecks($company);

        return $this->formatResponse([
            'status' => true,
            ...$registrationCheckResults
        ]);
    }

    /**
     * @return JsonResponse
     * @throws ValidationException
     */
    public function sendPhoneVerification(): JsonResponse
    {
        $this->validate($this->request, [
            self::REQUEST_COMPANY_REFERENCE => 'required|string',
            self::REQUEST_PHONE => [new Phone()]
        ]);

        $company = $this->companyRepository->findByReferenceOrFail($this->request->get(self::REQUEST_COMPANY_REFERENCE));

        /** @var CompanyUser $user */
        $user = $company->users()->firstOrFail();

        $user->cell_phone = $this->request->get(self::REQUEST_PHONE);

        $user->save();

        return $this->formatResponse([
            'status' => $this->verificationService->sendVerificationNotification($user, CompanyUserVerificationMethod::SMS)
        ]);
    }

    /**
     * @return JsonResponse
     * @throws ValidationException
     */
    public function verifyPhone(): JsonResponse
    {
        $this->validate($this->request, [
            self::REQUEST_COMPANY_REFERENCE => 'required|string',
            self::REQUEST_VERIFICATION_CODE => 'required|digits:4'
        ]);

        $company = $this->companyRepository->findByReferenceOrFail($this->request->get(self::REQUEST_COMPANY_REFERENCE));

        /** @var CompanyUser $user */
        $user = $company->users()->firstOrFail();

        return $this->formatResponse([
            'status' => $this->verificationService->verifyCompanyUser($user, CompanyUserVerificationMethod::SMS, $this->request->get(self::REQUEST_VERIFICATION_CODE))
        ]);
    }

    /**
     * @param CompanyLocationToLegacyAddressTransformer $transformer
     * @param CompanyAddressRepository $repository
     * @param CompanyLocationService $companyLocationService
     *
     * @return JsonResponse
     * @throws ValidationException
     * @throws RequestException
     */
    public function addLocationToCompany(CompanyLocationToLegacyAddressTransformer $transformer, CompanyAddressRepository $repository, CompanyLocationService $companyLocationService): JsonResponse
    {
        $validated = $this->validate($this->request, [
            self::REQUEST_COMPANY_REFERENCE     => 'required|string',
            self::REQUEST_LOCATION_NAME         => 'required|string',
            self::REQUEST_LOCATION_PHONE        => 'string|nullable',
            self::REQUEST_ADDRESS_LINE_1        => 'required|string',
            self::REQUEST_ADDRESS_LINE_2        => 'string|nullable',
            self::REQUEST_ADDRESS_CITY          => 'required|string',
            self::REQUEST_ADDRESS_STATE         => 'required|string',
            self::REQUEST_ADDRESS_ZIPCODE       => 'required|string',
            self::REQUEST_ADDRESS_COUNTRY       => 'required|string|size:2',
        ]);

        $validated = $companyLocationService->addLatLngToAddress($validated, false);
        $company = $this->companyRepository->findByLegacyReferenceOrFail($this->request->get(self::REQUEST_COMPANY_REFERENCE));

        /** @var CompanyLocation|null $location */
        $location = $this->companyRegistrationService->addLocationToCompany($company, $validated);

        if ($location)
            $this->syncAddressToLegacy($transformer, $repository, $location, $company);

        return $this->formatResponse([
            'status'    => !!$location,
            'location'  => $location,
        ]);
    }

    /**
     * @param AddServiceAreaFormRequest $request
     * @return JsonResponse
     */
    public function addLServiceAreaToCompany(AddServiceAreaFormRequest $request): JsonResponse
    {
        $requestedData = $request->safe()->collect();

        $company         = $this->companyRepository->findByLegacyReferenceOrFail($requestedData->get(self::REQUEST_COMPANY_REFERENCE));
        $companyLocation = $this->companyLocationRepository->findOrFail($requestedData->get(self::REQUEST_COMPANY_LOCATION));

        if($company->{Company::FIELD_ID !== $companyLocation->{CompanyLocation::FIELD_COMPANY_ID}})
            throw new InvalidArgumentException('The requested company or location ID is incorrect!');

        return $this->formatResponse([
            'status' => $this->companyRegistrationService->addServiceAreaToCompany($company, $companyLocation, $requestedData->get(self::REQUEST_COMPANY_SERVICE_AREA))
        ]);
    }

    /**
     * @return JsonResponse
     * @throws ValidationException
     * @throws Exception
     */
    public function addPaymentMethodToCompany(CompanyBillingService $billingService): JsonResponse
    {
        $validated = $this->validate($this->request, [
            self::REQUEST_COMPANY_REFERENCE     => 'required|string|size:10',
            self::REQUEST_STRIPE_TOKEN          => 'required',
            self::REQUEST_EMAIL                 => 'required|email',
            self::REQUEST_USER_REFERENCE        => 'string|required',
        ]);
        $company = $this->companyRepository->findByLegacyReferenceOrFail($validated[self::REQUEST_COMPANY_REFERENCE]);
        $user = CompanyUser::query()->where(CompanyUser::FIELD_REFERENCE, $validated[self::REQUEST_USER_REFERENCE])->firstOrFail();
        $validated[self::REQUEST_USER_LEGACY_ID] = $user->{CompanyUser::FIELD_LEGACY_ID} ?? null;

        $result = $billingService->addPaymentMethod(
            $company,
            $user->{CompanyUser::FIELD_EMAIL},
            $validated[self::REQUEST_STRIPE_TOKEN],
            'card'
        );

        if ($result['success']) {
            $this->companyRegistrationService->updateCompanyDetails(
                $company,
                [ Company::FIELD_STATUS => Company::STATUS_PENDING_APPROVAL ]
            );
        }

        return $this->formatResponse([
            'status'    => $result['success'] ?? false,
            'message'   => $result['success'] ? null : ($result['message'] ?? 'There was an error adding the payment method.'),
        ]);

    }

    /**
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getCompanyLocations(): JsonResponse
    {
        $this->validate($this->request, [
            self::REQUEST_COMPANY_REFERENCE => 'required|string|size:10'
        ]);
        $company = $this->companyRepository->findByLegacyReferenceOrFail($this->request->get(self::REQUEST_COMPANY_REFERENCE));

        return $this->formatResponse([
            'status'    => true,
            'locations' => $company->{Company::RELATION_LOCATIONS}()?->with(CompanyLocation::RELATION_ADDRESS)->get() ?? [],
        ]);
    }

    /**
     * @return JsonResponse
     * @throws ValidationException
     */
    public function updateCompanyDetails(): JsonResponse
    {
        $validated = $this->validate($this->request, [
            self::REQUEST_COMPANY_REFERENCE         => 'required|string|size:10',
            self::REQUEST_COMPANY_WEBSITE           => 'nullable|string',
            self::REQUEST_COMPANY_EMAIL->value      => 'nullable|string',
            self::REQUEST_SUPPORT_EMAIL->value      => 'nullable|string',
            self::REQUEST_COMPANY_DESCRIPTION->value=> 'nullable|string',
            self::REQUEST_YEAR_STARTED_BUSINESS->value => 'nullable|string',
        ]);
        $company = $this->companyRepository->findByLegacyReferenceOrFail($validated[self::REQUEST_COMPANY_REFERENCE]);

        return $this->formatResponse([
            'status'    => !!$this->companyRegistrationService->updateCompanyDetails($company, $validated),
        ]);
    }

    /**
     * @return JsonResponse
     * @throws ValidationException
     * @throws Exception
     */
    public function getPriceRangeSalesTypeForDefaultCampaign(): JsonResponse
    {
        $validated = $this->validate($this->request, [
            self::REQUEST_COMPANY_REFERENCE  => 'required|string|size:10'
        ]);

        /** @var Company $company */
        $company = $this->companyRepository->findByLegacyReferenceOrFail($validated[self::REQUEST_COMPANY_REFERENCE]);

        $companyIndustry = $company->{Company::RELATION_INDUSTRIES}->first()?->{Industry::FIELD_NAME};

        /** @var \App\Enums\Odin\Industry|null $industry */
        $industry = $companyIndustry
            ? \App\Enums\Odin\Industry::tryFrom($companyIndustry)
            : null;

        if(!$industry) throw new InvalidArgumentException("The company must be servicing in one of the industries.");

        $priceRange = $this->leadPriceService->getPriceRangeSalesTypeForDefaultCampaign($company->{Company::FIELD_REFERENCE}, $industry);

        return $this->formatResponse([
            'status'      => !!$priceRange,
            'price_range' => $priceRange
        ]);
    }

    /**
     * @return JsonResponse
     * @throws ValidationException
     */
    public function setLeadBudgetOfCreatedCompany(): JsonResponse
    {
        $validated = $this->validate($this->request, [
            self::REQUEST_COMPANY_REFERENCE  => 'required|string|size:10',
            self::REQUEST_LEAD_BUDGET        => 'required|integer|min:1'
        ]);

        /** @var Company $company */
        $company = $this->companyRepository->findByLegacyReferenceOrFail($validated[self::REQUEST_COMPANY_REFERENCE]);

        return $this->formatResponse([
            'status'  => !! $this->companyRegistrationService->setLeadBudgetOfCreatedCompany(
                $company->{Company::FIELD_REFERENCE},
                $this->request->get(self::REQUEST_LEAD_BUDGET)
            )
        ]);
    }

    /**
     * @param GetCompanyContractRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function getCompanyContract(GetCompanyContractRequest $request): JsonResponse
    {
        $data = $request->safe()->all();

        $company = $this->companyRepository->findByReferenceOrFail($data[self::REQUEST_COMPANY_REFERENCE]);
        $user = $this->userRepository->getCompanyUserByReference($data[self::REQUEST_USER_REFERENCE]);

        /** @var Website $website */
        $website = Website::query()->where(Website::FIELD_ABBREVIATION, $data[self::REQUEST_REGISTRATION_ORIGIN])->first();
        /** @var ContractKey $contractKey */
        $contractKey = ContractKey::query()->where(ContractKey::FIELD_KEY, $data[self::REQUEST_CONTRACT_TYPE])->first();
        $appId = $data[self::REQUEST_CONTRACT_APP_ID];

        $ip = $this->helperService->getIpFromRequest($this->request);

        return $this->formatResponse(
            $this->companyRegistrationService->getCompanyContract($company, $user, $contractKey, $website, $ip, $appId) ?? [],
        );
    }

    public function downloadCompanyContract(): JsonResponse
    {
        $validated = $this->validate($this->request, [
            self::REQUEST_CONTRACT_REFERENCE    => 'required|uuid',
        ]);

        $contract = $this->companyContractRepository->findCompanyContractByUuidOrFail($validated[self::REQUEST_CONTRACT_REFERENCE]);

        return $this->formatResponse([
            'status'    => true,
            'pdf'       => Pdf::html($contract->contract)
                ->margins(20, 10, 0, 10, Unit::Pixel)
                ->name('Terms-and-Conditions.pdf')
                ->base64()
        ]);
    }

    /**
     * @param PubSubService $pubSubService
     * @param AcceptCompanyContractRequest $request
     * @return JsonResponse
     */
    public function acceptCompanyContract(PubSubService $pubSubService, AcceptCompanyContractRequest $request): JsonResponse
    {
        $data = $request->safe()->all();

        $company = $this->companyRepository->findByLegacyReferenceOrFail($data[self::REQUEST_COMPANY_REFERENCE]);
        $companyUser = $this->userRepository->getCompanyUserByReference($data[self::REQUEST_USER_REFERENCE]);
        $companyContract = $this->companyContractRepository->findCompanyContractByUuidOrFail($data[self::REQUEST_CONTRACT_REFERENCE]);

        $contractUpdated = $this->contractContractService->agreeToContract(company: $company,companyUser: $companyUser, companyContract: $companyContract);

        if ($contractUpdated ) {
            $contractKey = $companyContract->contractModel->contractKey->key;
            switch ($contractKey) {
                case ContractKeys::TERMS_AND_CONDITIONS->value:
                    $this->companyRegistrationService->markCompanyAsPending($company);
                    $pubSubService->handle(
                        EventCategory::COMPANIES->value,
                        EventName::COMPLETED_REGISTRATION->value,
                        [
                            self::EVENT_DATA_COMPANY_ID => $company->id,
                            self::EVENT_DATA_COMPANY_REFERENCE => $company->reference,
                        ]
                    );
                    break;
                case ContractKeys::SITE_ACCESS_AGREEMENT->value:
                    //todo accepted dashboard contract
                    break;
                case ContractKeys::CREDIT_CARD_CONSENT->value:
                    //todo accepted the credit card consent
                    break;
                case ContractKeys::LEAD_BUYING_AGREEMENT->value:
                    //todo
                    break;
            }
        }

        return $this->formatResponse([
            'status'    => $contractUpdated,
        ]);
    }

    /**
     * @return JsonResponse
     * @throws ValidationException
     */
    public function setLeadContactOfCreatedCompany(): JsonResponse
    {
        $validated = $this->validate($this->request, [
            self::REQUEST_COMPANY_REFERENCE  => 'required|string|size:10',
            self::REQUEST_FIRST_NAME         => 'required|string',
            self::REQUEST_LAST_NAME          => 'required|string',
            self::REQUEST_EMAIL              => 'required|email',
            self::REQUEST_PHONE              => [new Phone()]
        ]);

        /** @var Company $company */
        $company = $this->companyRepository->findByLegacyReferenceOrFail($validated[self::REQUEST_COMPANY_REFERENCE]);

        return $this->formatResponse([
            'status'  => !! $this->companyRegistrationService->setLeadContactOfCreatedCompany($company, $validated)
        ]);
    }

    /**
     * @return JsonResponse
     * @throws ValidationException
     */
    public function updateCompanyBusinessDetails(): JsonResponse
    {
        $validated = $this->validate($this->request, [
            self::REQUEST_COMPANY_REFERENCE  => 'required|string|size:10',
            self::REQUEST_EMPLOYEE_NUMBER => 'nullable|numeric',
            self::REQUEST_REVENUE => 'nullable|numeric',
            self::REQUEST_BUYING_LEADS => 'nullable|in:yes,no'
        ]);

        $company = $this->companyRepository->findByLegacyReferenceOrFail($validated[self::REQUEST_COMPANY_REFERENCE]);

        return $this->formatResponse([
            'status' => $this->companyRepository->updateCompanyData($company, [
                SolarConfigurableFields::EMPLOYEE_COUNT->value => $validated[self::REQUEST_EMPLOYEE_NUMBER] ?? null,
                SolarConfigurableFields::REVENUE_IN_THOUSANDS->value => $validated[self::REQUEST_REVENUE] ?? null,
                SolarConfigurableFields::BUYING_LEADS->value => $validated[self::REQUEST_BUYING_LEADS] ?? null
            ])
        ]);
    }

    public function generateDashboardToken(DashboardLoginTokenService $tokenService): JsonResponse
    {
        $userReference = $this->request->get(self::REQUEST_USER_REFERENCE);
        $user = $this->userRepository->getCompanyUserByReference($userReference);
        $token = $tokenService->generateToken($user);
        return $this->formatResponse([
            'status'    => !!$token,
            'token'     => $token,
        ]);
    }

    /**
     * Return all bundles that should be displayed to customers.
     *
     * @param CompanyRegistrationBundleRequest $request
     * @return JsonResponse
     */
    public function getBundles(CompanyRegistrationBundleRequest $request): JsonResponse
    {
        $data = $request->safe()->all();
        $industrySlug = $data[CompanyRegistrationBundleRequest::REQUEST_INDUSTRY_SLUG] ?? null;
        $bundles = $industrySlug ? $this->bundleRepository->getBundlesByIndustry($industrySlug) : $this->bundleRepository->getActiveNonIndustryBundles();

        return $this->formatResponse([
            'bundles' => new BundleCollection($bundles)
        ]);
    }

    /**
     * @param Bundle $bundle
     * @param PurchaseBundleRequest $request
     * @return JsonResponse
     */
    public function purchaseBundle(Bundle $bundle, PurchaseBundleRequest $request): JsonResponse
    {
        $data = $request->safe()->all();
        [
            'status' => $status,
            'message' => $message
        ] = $this->bundleService->processPurchasedBundle($data, $bundle);

        return $this->formatResponse([
            'status' => $status,
            'message' => $message
        ]);
    }

    /**
     * @param CompanyLocationToLegacyAddressTransformer $transformer
     * @param CompanyAddressRepository $repository
     * @param CompanyLocation $location
     * @param Company $company
     *
     * @return void
     */
    protected function syncAddressToLegacy(CompanyLocationToLegacyAddressTransformer $transformer, CompanyAddressRepository $repository, CompanyLocation $location, Company $company): void
    {
        try {
            $legacyAddressData = $transformer->transformCompanyLocationToLegacyAddress($location);
            $repository->createCompanyAddress($company->{Company::FIELD_REFERENCE}, $legacyAddressData);
        } catch (Exception $e) {
            logger()->error("Failed to create address in legacy. Error: {$e->getMessage()}");
        }
    }

    /**
     * @return string|null
     */
    protected function getRegistrationDomain(): string|null
    {
        $requestOrigin = $this->request->header('origin', 'https://fixr.com');
        $host = parse_url($requestOrigin, PHP_URL_HOST);

        if (!$host) {
            return null;
        }

        return preg_replace('/^www\./', '', $host);
    }
}

