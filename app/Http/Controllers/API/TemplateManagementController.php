<?php

namespace App\Http\Controllers\API;

use App\Enums\TemplateManagement\TemplatePurposeKey;
use App\Enums\TemplateManagement\TemplateRelation;
use App\Enums\TemplateManagement\TemplateType;
use App\Http\Requests\StoreTemplateRequest;
use App\Http\Requests\StoreTemplateSelectorRequest;
use App\Http\Resources\TemplateResource;
use App\Http\Resources\TemplateSelectorResource;
use App\Models\EmailTemplate;
use App\Models\Odin\Industry;
use App\Models\Template;
use App\Models\TemplateSelector;
use App\Repositories\TemplateRepository;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;

class TemplateManagementController extends APIController
{
    /**
     * @param TemplateType $templateType
     * @param TemplateRepository $templateRepository
     *
     * @return JsonResponse
     */
    public function getTemplates(TemplateType $templateType, TemplateRepository $templateRepository): JsonResponse
    {
        return $this->formatResponse([
            'status' => true,
            'templates' => TemplateResource::collection($templateRepository->getTemplatesByType($templateType))
        ]);
    }

    /**
     * @param TemplateType $templateType
     * @param StoreTemplateRequest $request
     * @param TemplateRepository $repository
     *
     * @return JsonResponse
     */
    public function createTemplate(TemplateType $templateType, StoreTemplateRequest $request, TemplateRepository $repository): JsonResponse
    {
        return $this->formatResponse([
            'status' => !! $repository->createTemplate($templateType, $request->safe()->toArray())
        ]);
    }

    /**
     * @param TemplateType $templateType
     * @param Template $template
     * @param StoreTemplateRequest $request
     * @param TemplateRepository $repository
     *
     * @return JsonResponse
     */
    public function updateTemplate(TemplateType $templateType, Template $template, StoreTemplateRequest $request, TemplateRepository $repository): JsonResponse
    {
        if ($template->type !== $templateType) {
            throw new BadRequestException('Invalid template type');
        }

        return $this->formatResponse([
            'status' => $repository->updateTemplate($template, $request->safe()->toArray())
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getTemplateSelectorData(): JsonResponse
    {
        return $this->formatResponse([
            'industries' => Industry::query()->get()->map(fn(Industry $industry) => ['id' => $industry->id, 'name' => $industry->name]),
            'templates' => EmailTemplate::query()->get()->map(fn(EmailTemplate $template) => ['id' => $template->id, 'name' => $template->name, 'type' => 'email'])->merge(
                Template::query()->get()->map(fn(Template $template) => ['id' => $template->id, 'name' => $template->name, 'type' => $template->type->value])
            )
        ]);
    }

    /**
     * @param TemplateRelation $templateRelation
     * @param TemplateRepository $repository
     *
     * @return JsonResponse
     */
    public function getTemplateSelectors(TemplateRelation $templateRelation, TemplateRepository $repository): JsonResponse
    {
        return $this->formatResponse([
            'selections' => TemplateSelectorResource::collection($repository->getTemplateSelectors($templateRelation))
        ]);
    }

    /**
     * @param TemplateRelation $templateRelation
     * @param StoreTemplateSelectorRequest $request
     * @param TemplateRepository $repository
     *
     * @return JsonResponse
     */
    public function saveTemplateSelector(TemplateRelation $templateRelation, StoreTemplateSelectorRequest $request, TemplateRepository $repository): JsonResponse
    {
        return $this->formatResponse([
            'status' => !! $repository->createTemplateSelector(
                relation: $templateRelation,
                purposeKey: TemplatePurposeKey::from($request->validated(TemplateSelector::FIELD_PURPOSE_KEY)),
                template: $repository->getTemplateModelFromTypeAndId(
                    $request->validated(StoreTemplateSelectorRequest::TEMPLATE_TYPE),
                    $request->validated(TemplateSelector::FIELD_TEMPLATE_ID)
                ),
                industryId: $request->validated(TemplateSelector::FIELD_INDUSTRY_ID)
            )
        ]);
    }

    /**
     * @param TemplateRelation $templateRelation
     * @param TemplateSelector $templateSelector
     *
     * @return JsonResponse
     */
    public function deleteTemplateSelector(TemplateRelation $templateRelation, TemplateSelector $templateSelector): JsonResponse
    {
        if ($templateSelector->relation !== $templateRelation) {
            throw new BadRequestException();
        }

        return $this->formatResponse([
            'status' => $templateSelector->delete()
        ]);
    }
}
