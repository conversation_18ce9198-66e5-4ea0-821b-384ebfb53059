<?php

namespace App\Http\Controllers\API\OutreachCadence;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\OutreachCadence\UpdateCadenceRoutineRequest;
use App\Models\User;
use App\Repositories\OutreachCadence\CadenceRoutineConfigurationRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CadenceRoutineConfigurationController extends APIController
{

    const REQUEST_ROUTINE_ID      = 'routine_id';
    const REQUEST_NEW_GROUP_ORDER = 'new_group_order';

    public function __construct(
        protected Request                               $request,
        protected JsonAPIResponseFactory                $apiResponseFactory,
        protected CadenceRoutineConfigurationRepository $routineRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @return JsonResponse
     */
    public function reorderActions(): JsonResponse
    {
        $validated        = $this->request->validate([self::REQUEST_ROUTINE_ID => 'required|numeric', self::REQUEST_NEW_GROUP_ORDER => 'required|array']);
        $routineId        = $validated[self::REQUEST_ROUTINE_ID];
        // todo validate user can edit this routine
        $newGroupOrder = $validated[self::REQUEST_NEW_GROUP_ORDER];
        $this->routineRepository->updateActionOrders($newGroupOrder);
        return $this->formatResponse();
    }

    /**
     * @return JsonResponse
     */
    public function cloneRoutine(): JsonResponse
    {
        $validated = $this->request->validate([self::REQUEST_ROUTINE_ID => 'required|numeric']);
        /** @var User $user */
        $user = Auth::user();
        $this->routineRepository->cloneRoutine($validated[self::REQUEST_ROUTINE_ID], $user);
        return $this->getAllAvailableRoutineConfigurations();
    }

    /**
     * @return JsonResponse
     */
    public function getOwnedRoutineConfigurations(): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();
        return $this->formatResponse($this->routineRepository->getRoutineConfigs($user)->toArray());
    }

    /**
     * @return JsonResponse
     */
    public function getAllAvailableRoutineConfigurations(): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();
        return $this->formatResponse($this->routineRepository->getRoutineConfigs($user, true)->toArray());
    }


    /**
     * @param UpdateCadenceRoutineRequest $request
     * @return JsonResponse
     */
    public function createRoutineConfiguration(UpdateCadenceRoutineRequest $request): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();
        $this->routineRepository->createRoutineConfig($request->validated(), $user?->id);
        return $this->getAllAvailableRoutineConfigurations();
    }

    /**
     * @param UpdateCadenceRoutineRequest $request
     * @param int $routineId
     * @return JsonResponse
     */
    public function updateRoutineConfiguration(UpdateCadenceRoutineRequest $request, int $routineId): JsonResponse
    {
        $this->routineRepository->updateRoutineConfig($request->validated(), $routineId);
        return $this->getAllAvailableRoutineConfigurations();
    }

    /**
     * @param int $routineId
     * @return JsonResponse
     */
    public function deleteRoutineConfiguration(int $routineId): JsonResponse
    {
        $this->routineRepository->deleteRoutineConfig($routineId);
        return $this->getAllAvailableRoutineConfigurations();
    }

}
