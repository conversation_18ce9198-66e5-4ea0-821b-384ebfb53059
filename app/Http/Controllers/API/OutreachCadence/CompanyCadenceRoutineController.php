<?php

namespace App\Http\Controllers\API\OutreachCadence;

use App\Builders\Odin\Company\CompanySearchBuilder;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Controllers\API\CompanySearchController;
use App\Models\Cadence\CompanyCadenceRoutine;
use App\Http\Requests\SearchCompaniesRequest;
use App\Models\Odin\Company;
use App\Repositories\OutreachCadence\CadenceUserContactRepository;
use App\Repositories\OutreachCadence\CompanyCadenceRoutineRepository;
use App\Services\CompanySearchService;
use App\Services\Filterables\Company\CompanyFilterableService;
use App\Services\OutreachCadence\CompanyCadenceRoutineService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class CompanyCadenceRoutineController extends APIController
{
    const COMPANY_IDS                     = 'companyIds';
    const SELECTED_CADENCE_ROUTINE_ID     = 'selectedCadenceRoutineId';
    const TERMINATED_COUNT                = 'terminated_count';
    const COMPANIES_WITH_ONGOING_ROUTINES = 'companies_with_ongoing_routines';
    const ROUTINES_INITIALIZED            = 'routines_initialized';
    const COMPANY_SEARCH_PARAMS           = 'companySearchParams';
    const APPLY_TO_ALL_COMPANIES          = 'allCompanies';
    const ERRORS                          = 'errors';
    const USE_ACCOUNT_MANAGER             = 'useAccountManager';

    const GROUP_KEY_ONGOING         = 'ongoing';
    const GROUP_KEY_CONCLUDED       = 'concluded';
    const GROUP_KEY_TERMINATED      = 'terminated';

    const MAX_MASS_ASSIGNMENT_COUNT = 10_000;

    public function __construct(
        Request                                   $request,
        JsonAPIResponseFactory                    $apiResponseFactory,
        protected CompanyCadenceRoutineRepository $repository,
        protected CompanyCadenceRoutineService    $service,
        protected CadenceUserContactRepository    $contactRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @return JsonResponse
     */
    public function getCompanyRoutines(): JsonResponse
    {
        // todo: handle filter params
        return $this->formatResponse($this->repository->getCompanyRoutines()->toArray());
    }

    /**
     * @param int $companyId
     * @return JsonResponse
     */
    public function findCompanyRoutines(int $companyId): JsonResponse
    {
        $routines = $this->repository->findRoutinesByCompanyId($companyId);

        $groupedRoutines = [
            self::GROUP_KEY_ONGOING    => $routines->where(CompanyCadenceRoutine::FIELD_STATUS, CompanyCadenceRoutine::STATUS_PENDING)
                                                   ->whereNull(CompanyCadenceRoutine::FIELD_DELETED_AT)
                                                   ->sortByDesc(Model::UPDATED_AT)
                                                   ->toArray(),
            self::GROUP_KEY_CONCLUDED  => $routines->where(CompanyCadenceRoutine::FIELD_STATUS, CompanyCadenceRoutine::STATUS_CONCLUDED)
                                                   ->whereNull(CompanyCadenceRoutine::FIELD_DELETED_AT)
                                                   ->sortByDesc(Model::UPDATED_AT)
                                                   ->toArray(),
            self::GROUP_KEY_TERMINATED => $routines->whereNotNull(CompanyCadenceRoutine::FIELD_DELETED_AT)
                                                   ->sortByDesc(CompanyCadenceRoutine::FIELD_DELETED_AT)
                                                   ->toArray(),
        ];

        return $this->formatResponse($groupedRoutines);
    }

    /**
     * @param int $routineId
     * @return JsonResponse
     */
    public function getRoutineContacts(int $routineId): JsonResponse
    {
        return $this->formatResponse($this->contactRepository->getRoutineContacts($routineId)->toArray());
    }

    /**
     * @param int $routineId
     * @return JsonResponse
     */
    public function updateRoutineContacts(int $routineId): JsonResponse
    {
        $validated = $this->request->validate(['contact-updates' => 'array|required']);
        $this->contactRepository->updateRoutineContacts($routineId, $validated['contact-updates']);
        return $this->formatResponse();
    }

    /**
     * @param int $groupId
     * @return JsonResponse
     */
    public function skipGroup(int $groupId): JsonResponse
    {
        $this->repository->skipGroupById($groupId);
        return $this->formatResponse();
    }

    /**
     * @param int $groupId
     * @return JsonResponse
     */
    public function unSkipGroup(int $groupId): JsonResponse
    {
        $this->repository->unSkipGroupById($groupId);
        return $this->formatResponse();
    }

    /**
     * @param $routineId
     * @return JsonResponse
     */
    public function terminateRoutine($routineId): JsonResponse
    {
        $this->repository->deleteRoutineById($routineId);
        return $this->formatResponse();
    }

    /**
     * @return JsonResponse
     * @throws ValidationException
     */
    public function massAssign(): JsonResponse
    {
        return $this->massAssignCommonLogic();
    }

    /**
     * @return JsonResponse
     * @throws ValidationException
     */
    public function massAssignV2(): JsonResponse
    {
        return $this->massAssignCommonLogic(true);
    }

    /**
     * @return JsonResponse
     * @throws ValidationException
     */
    public function massTerminate(): JsonResponse
    {
        return $this->massTerminateCommonLogic();
    }

    /**
     * @return JsonResponse
     * @throws ValidationException
     */
    public function massTerminateV2(): JsonResponse
    {
        return $this->massTerminateCommonLogic(true);
    }

    /**
     * @param array $companySearchParams
     * @param bool $isV2
     * @return array
     * @throws ValidationException
     */
    private function getAllCompanyIdsFromCompanySearchCriteria(array $companySearchParams, bool $isV2): array
    {
        if ($isV2) {
            /** @var CompanyFilterableService $companyFilterableService */
            $companyFilterableService = app(CompanyFilterableService::class);

            $filterOptions = $companySearchParams[CompanySearchController::REQUEST_FILTERS];
            $searchText    = $companySearchParams[CompanySearchController::REQUEST_SEARCH_BY_TEXT];
            $searchContact = $companySearchParams[CompanySearchController::REQUEST_SEARCH_BY_CONTACT];

            $query = CompanySearchBuilder::query()
                                         ->searchText($searchText)
                                         ->searchContact($searchContact)
                                         ->getQuery();

            $query = $companyFilterableService->runQuery($filterOptions, $query);

            return $query->get()->pluck(Company::FIELD_ID)->toArray();
        } else {
            /** @var SearchCompaniesRequest $searchCompaniesRequest */
            $searchCompaniesRequest = app(SearchCompaniesRequest::class);
            $searchCompaniesRequest->request->replace($companySearchParams);

            /** @var CompanySearchService $companySearchService */
            $companySearchService = app(CompanySearchService::class);
            return $companySearchService->getAllCompanyIds($searchCompaniesRequest);
        }

    }

    /**
     * @param bool $isV2
     * @return JsonResponse
     * @throws ValidationException
     */
    private function massAssignCommonLogic(bool $isV2 = false): JsonResponse
    {
        $validated = $this->request->validate([
            self::COMPANY_IDS                 => 'array',
            self::COMPANY_SEARCH_PARAMS       => 'array|required_if:' . self::APPLY_TO_ALL_COMPANIES . ',true',
            self::APPLY_TO_ALL_COMPANIES      => 'boolean',
            self::SELECTED_CADENCE_ROUTINE_ID => 'numeric',
            self::USE_ACCOUNT_MANAGER         => 'boolean',
        ]);

        $companyIds          = $validated[self::COMPANY_IDS];
        $companySearchParams = $validated[self::COMPANY_SEARCH_PARAMS];
        $applyToAllCompanies = $validated[self::APPLY_TO_ALL_COMPANIES];

        // If $applyToAllCompanies is true, we will use the company search criteria to mock a company search request in order to get company ids
        $companyIds = $applyToAllCompanies ? $this->getAllCompanyIdsFromCompanySearchCriteria($companySearchParams, $isV2) : $companyIds;

        // A company can only have one active routine - remove companies with ongoing routines
        $companiesWithOngoingRoutines = $this->repository->companiesWithOngoingRoutines($companyIds);
        $companyIds                   = array_diff($companyIds, $companiesWithOngoingRoutines->pluck(Company::FIELD_ID)->toArray());

        if(count($companyIds) > self::MAX_MASS_ASSIGNMENT_COUNT){return $this->searchTooBroadResponse();}

        $this->service->initializeCompanyRoutines($companyIds, $validated[self::SELECTED_CADENCE_ROUTINE_ID], $validated[self::USE_ACCOUNT_MANAGER]);

        return $this->formatResponse([
            self::COMPANIES_WITH_ONGOING_ROUTINES => $companiesWithOngoingRoutines,
            self::ROUTINES_INITIALIZED            => count($companyIds),
            self::ERRORS                          => []
        ]);
    }

    /**
     * @param bool $isV2
     * @return JsonResponse
     * @throws ValidationException
     */
    private function massTerminateCommonLogic(bool $isV2 = false): JsonResponse
    {
        $validated = $this->request->validate([
            self::COMPANY_IDS            => 'array',
            self::COMPANY_SEARCH_PARAMS  => 'array|required_if:' . self::APPLY_TO_ALL_COMPANIES . ',true',
            self::APPLY_TO_ALL_COMPANIES => 'boolean'
        ]);

        $companyIds          = $validated[self::COMPANY_IDS];
        $companySearchParams = $validated[self::COMPANY_SEARCH_PARAMS];
        $applyToAllCompanies = $validated[self::APPLY_TO_ALL_COMPANIES];

        // If $applyToAllCompanies is true, we will use the company search criteria to mock a company search request in order to get company ids
        $companyIds = $applyToAllCompanies ? $this->getAllCompanyIdsFromCompanySearchCriteria($companySearchParams, $isV2) : $companyIds;

        if(count($companyIds) > self::MAX_MASS_ASSIGNMENT_COUNT){return $this->searchTooBroadResponse();}

        return $this->formatResponse([
            self::TERMINATED_COUNT => $this->repository->terminateOngoingCompanyRoutinesByCompanyIds($companyIds),
            self::ERRORS           => []
        ]);
    }

    /**
     * @return JsonResponse
     */
    private function searchTooBroadResponse(): JsonResponse
    {
        return $this->formatResponse([
            self::ERRORS                          => ['Company query too broad. Must not yield more than ' . self::MAX_MASS_ASSIGNMENT_COUNT . ' results.'],
            self::COMPANIES_WITH_ONGOING_ROUTINES => [],
            self::ROUTINES_INITIALIZED            => 0,
            self::TERMINATED_COUNT                => 0,
        ]);
    }
}
