<?php

namespace App\Http\Controllers\API\Advertising;

use App\Contracts\Services\Advertising\AdvertisingTokenAuthServiceContract;
use App\Enums\Advertising\Advertiser;
use App\Enums\Advertising\Advertiser as AdvertiserEnum;
use App\Enums\Advertising\AdvertisingCostMetric;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Enums\Advertising\AdvertisingResolution;
use App\Enums\Odin\Industry;
use App\Http\Requests\Advertising\TieredAdvertisingCampaignsRequest;
use App\Http\Requests\Advertising\TieredAdvertisingCreateInstanceRequest;
use App\Http\Requests\Advertising\TieredAdvertisingDeleteInstanceRequest;
use App\Http\Requests\Advertising\TieredAdvertisingInstanceLocationsUpdateRequest;
use App\Http\Requests\Advertising\TieredAdvertisingUpdateRequest;
use App\Http\Resources\Advertising\TieredAdvertisingCampaignLocationResource;
use App\Http\Resources\Advertising\TieredAdvertisingCampaignResource;
use App\Jobs\Advertising\UpdateTieredAdvertisingCampaignLocationsJob;
use App\Models\Odin\Industry as IndustryModel;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Advertising\AdCostBackfillRequest;
use App\Http\Requests\Advertising\DeleteAccountRequest;
use App\Http\Requests\Advertising\GetCampaignsRequest;
use App\Http\Requests\Advertising\HandleTokenAuthRedirectRequest;
use App\Http\Requests\Advertising\UpdateCampaignRequest;
use App\Http\Requests\Advertising\SaveAccountRequest;
use App\Models\AdvertisingAccount;
use App\Models\AdvertisingCampaign;
use App\Models\AdvertisingCampaignAutomationParameter;
use App\Models\LockedAdvertisingCampaignLocation;
use App\Models\TieredAdvertisingConfiguration;
use App\Models\TieredAdvertisingInstance;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\IndustryRepository;
use App\Services\Advertising\AdvertisingAccountService;
use App\Services\Advertising\AdvertisingCampaignService;
use App\Services\Advertising\Authentication\AdvertisingAuthServiceFactory;
use App\Services\Advertising\Campaigns\AdvertisingServiceFactory;
use App\Services\Advertising\TieredAdvertisingService;
use App\Transformers\ReferenceLists\StateTransformer;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;

class AdvertisingAPIController extends APIController
{
    const REQUEST_CAMPAIGNS = 'campaigns';
    const REQUEST_PAGE = 'page';
    const REQUEST_PER_PAGE = 'per_page';
    const REQUEST_SEARCH_STATE_LOCATION_ID = 'search_state_location_id';
    const REQUEST_COST_METRIC = 'cost_metric';
    const REQUEST_CAMPAIGN_STATUS = 'campaign_status';
    const REQUEST_PAGE_TOKENS = 'page_tokens';
    const REQUEST_CAMPAIGN_NAME = 'campaign_name';

    const REQUEST_RUN_INTERVAL = AdvertisingCampaignService::CAMPAIGN_RUN_INTERVAL;
    const REQUEST_AUTOMATION_PARAMETERS = AdvertisingCampaignService::CAMPAIGN_AUTOMATION_PARAMETERS;
    const REQUEST_AUTOMATED = AdvertisingCampaignService::CAMPAIGN_AUTOMATED;
    const REQUEST_LOCATIONS = AdvertisingCampaignService::CAMPAIGN_LOCATIONS;

    const REQUEST_OAUTH_CODE = 'code';
    const REQUEST_OAUTH_STATE = 'state';

    const REQUEST_WEBSITES = 'websites';

    const REQUEST_ADVERTISER = 'advertiser';

    /**
     * @throws Exception
     */
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param GetCampaignsRequest $getCampaignsRequest
     * @param string $platform
     * @param $accountId
     * @return JsonResponse
     * @throws Exception
     */
    public function getCampaignsPaginated(GetCampaignsRequest $getCampaignsRequest, string $platform, $accountId): JsonResponse
    {
        $data = $getCampaignsRequest->safe()->only([
            self::REQUEST_PAGE,
            self::REQUEST_PER_PAGE,
            self::REQUEST_SEARCH_STATE_LOCATION_ID,
            self::REQUEST_COST_METRIC,
            self::REQUEST_CAMPAIGN_STATUS,
            self::REQUEST_CAMPAIGN_NAME,
            self::REQUEST_PAGE_TOKENS
        ]);

        if(!empty($data[self::REQUEST_PAGE_TOKENS])
        && is_string($data[self::REQUEST_PAGE_TOKENS])) {
            $data[self::REQUEST_PAGE_TOKENS] = json_decode($data[self::REQUEST_PAGE_TOKENS], true);
        }

        return $this->formatResponse([
            "status" => true,
            "campaigns" => AdvertisingServiceFactory::make($platform)->getCampaignsPaginated(
                $accountId,
                $data[self::REQUEST_PAGE],
                $data[self::REQUEST_PER_PAGE],
                $data[self::REQUEST_SEARCH_STATE_LOCATION_ID],
                $data[self::REQUEST_COST_METRIC],
                $data[self::REQUEST_CAMPAIGN_STATUS],
                $data[self::REQUEST_CAMPAIGN_NAME],
                $data[self::REQUEST_PAGE_TOKENS]
            )->toArray(),
            "page_tokens" => $data[self::REQUEST_PAGE_TOKENS]
        ]);
    }

    /**
     * @param UpdateCampaignRequest $updateCampaignRequest
     * @param AdvertisingCampaignService $advertisingCampaignService
     * @param string $platform
     * @param $accountId
     * @return JsonResponse
     * @throws Exception
     */
    public function updateCampaigns(UpdateCampaignRequest $updateCampaignRequest, AdvertisingCampaignService $advertisingCampaignService, string $platform, $accountId): JsonResponse
    {
        $campaigns = collect($updateCampaignRequest->safe()->offsetGet(self::REQUEST_CAMPAIGNS))->keyBy(AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID);

        foreach($campaigns as $campaignId => $campaign) {
            $secondsInUnit = match($campaign[AdvertisingCampaign::FIELD_RUN_INTERVAL_DISPLAY_UNIT]) {
                AdvertisingCampaign::RUN_INTERVAL_WEEKS => 604800,
                AdvertisingCampaign::RUN_INTERVAL_HOURS => 3600,
                AdvertisingCampaign::RUN_INTERVAL_MINUTES => 60,
                default => throw new Exception("Invalid run interval unit: ".$campaign[AdvertisingCampaign::FIELD_RUN_INTERVAL_DISPLAY_UNIT])
            };

            $campaign[AdvertisingCampaign::FIELD_RUN_INTERVAL_SECS] = $campaign[self::REQUEST_RUN_INTERVAL] * $secondsInUnit;
            $campaigns->put($campaignId, $campaign);
        }

        $advertisingCampaignService->saveCampaignAutomationStatuses($campaigns, $platform, $accountId);
        $advertisingCampaignService->saveAutomationParameters($campaigns, $platform, $accountId);
        $advertisingCampaignService->saveLockedCampaignLocations($campaigns, $platform, $accountId);

        return $this->formatResponse([
            "status" => true
        ]);
    }

    /**
     * @param LocationRepository $locationRepository
     * @param StateTransformer $stateTransformer
     * @return JsonResponse
     */
    public function initReferenceLists(LocationRepository $locationRepository, StateTransformer $stateTransformer): JsonResponse
    {
        $platforms = [];
        foreach(AdvertisingServiceFactory::PLATFORM_SERVICE as $platformService) {
            $platforms[] = $platformService['platform']->value;
        }

        return $this->formatResponse([
            "status" => true,
            "parameter_options" => AdvertisingCampaignAutomationParameter::PARAMETER_OPTIONS,
            "platforms" => $platforms,
            "is_advertising_admin" => Auth::user()->hasPermissionTo("advertising-admin"),
            "states" => $stateTransformer->transformStates($locationRepository->getStates()),
            "operators" => AdvertisingCampaignAutomationParameter::OPERATORS,
            "cost_metric_options" => array_keys(AdvertisingCostMetric::all()),
            'location_locked_options' => [
                LockedAdvertisingCampaignLocation::TARGETED_NONE => 'Lock: Neither',
                LockedAdvertisingCampaignLocation::TARGETED_INCLUDE => 'Lock: Included',
                LockedAdvertisingCampaignLocation::TARGETED_EXCLUDE => 'Lock: Excluded'
            ]
        ]);
    }

    /**
     * @param string $platform
     * @return JsonResponse
     * @throws Exception
     */
    public function getPlatformReferenceLists(string $platform): JsonResponse
    {
        if(!in_array($platform, array_keys(AdvertisingPlatform::all()), true)) {
            throw new Exception(__METHOD__.": Invalid platform - $platform");
        }

        $adService = AdvertisingServiceFactory::make($platform);

        return $this->formatResponse([
            "status" => true,
            "campaign_statuses" => $adService->getCampaignStatusOptions()->toArray()
        ]);
    }

    /**
     * @param string $platform
     * @param Request $request
     * @param AdvertisingAccountService $advertisingAccountService
     * @return JsonResponse
     */
    public function getPaginatedAccounts(string $platform, Request $request, AdvertisingAccountService $advertisingAccountService): JsonResponse
    {
        return $this->formatResponse([
            "status" => true,
            "accounts" => $advertisingAccountService->getAccountsPaginated($platform, (int) $request->input('page'))
        ]);
    }

    /**
     * @param string $platform
     * @param AdvertisingAccountService $advertisingAccountService
     * @return JsonResponse
     */
    public function getAccounts(string $platform, AdvertisingAccountService $advertisingAccountService): JsonResponse
    {
        return $this->formatResponse([
            "status" => true,
            "accounts" => $advertisingAccountService->getAccounts($platform)
        ]);
    }

    /**
     * @param string $platform
     * @param SaveAccountRequest $saveAccountRequest
     * @param AdvertisingAccountService $advertisingAccountService
     * @return JsonResponse
     * @throws Exception
     */
    public function saveAccount(string $platform, SaveAccountRequest $saveAccountRequest, AdvertisingAccountService $advertisingAccountService): JsonResponse
    {
        $data = $saveAccountRequest->safe()->only([
            AdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID,
            AdvertisingAccount::FIELD_NAME,
            AdvertisingAccount::FIELD_INDUSTRY,
            AdvertisingAccount::FIELD_TRACKS_CONVERSIONS,
            AdvertisingAccount::FIELD_UPLOAD_CONVERSIONS_INTERVAL_HOURS,
            self::REQUEST_ADVERTISER,
            self::REQUEST_WEBSITES
        ]);

        return $this->formatResponse([
            "status" => $advertisingAccountService->saveAccount(
                $platform,
                $data[AdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID],
                $data[AdvertisingAccount::FIELD_NAME],
                $data[AdvertisingAccount::FIELD_INDUSTRY],
                $data[AdvertisingAccount::FIELD_TRACKS_CONVERSIONS],
                $data[AdvertisingAccount::FIELD_UPLOAD_CONVERSIONS_INTERVAL_HOURS],
                AdvertiserEnum::fromKey($data[self::REQUEST_ADVERTISER]),
                $data[self::REQUEST_WEBSITES]
            )
        ]);
    }

    /**
     * @param AdvertisingAccountService $advertisingAccountService
     * @param DeleteAccountRequest $deleteAccountRequest
     * @param string $platform
     * @param $accountId
     * @return JsonResponse
     */
    public function deleteAccount(
        AdvertisingAccountService $advertisingAccountService,
        DeleteAccountRequest $deleteAccountRequest,
        string $platform,
        $accountId
    ): JsonResponse
    {
        return $this->formatResponse([
            "status" => $advertisingAccountService->deleteAllAccountInfo($platform, $accountId)
        ]);
    }

    /**
     * @return JsonResponse
     * @throws Exception
     */
    public function getTokenAuthPlatforms(): JsonResponse
    {
        $platforms = [];

        foreach(AdvertisingAuthServiceFactory::PLATFORM_SERVICE as $platformService) {
            if(is_subclass_of($platformService['service'], AdvertisingTokenAuthServiceContract::class)) {
                $platforms[] = $platformService['platform']->value;
            }
        }

        return $this->formatResponse([
            "status" => true,
            "platforms" => $platforms
        ]);
    }

    /**
     * @param string $platform
     * @return JsonResponse
     * @throws Exception
     */
    public function getTokenAuthEndpoint(string $platform): JsonResponse
    {
        return $this->formatResponse([
            "status" => true,
            "endpoint" => AdvertisingAuthServiceFactory::make($platform)->getTokenAuthorizationUri()
        ]);
    }

    /**
     * @param HandleTokenAuthRedirectRequest $handleTokenAuthRedirectRequest
     * @param string $platform
     * @return View
     * @throws Exception
     */
    public function handleTokenAuthRedirect(
        HandleTokenAuthRedirectRequest $handleTokenAuthRedirectRequest,
        string                         $platform
    ): View
    {
        $res = AdvertisingAuthServiceFactory::make($platform)->getAccessTokens($handleTokenAuthRedirectRequest->fullUrl());

        return view('display-message', ['message' => $res ? "Authentication successful. You may now close this window." : "Authentication error."]);
    }

    /**
     * @return JsonResponse
     */
    public function getAllPlatforms(): JsonResponse
    {
        return $this->formatResponse([
            "status" => true,
            "platforms" => array_keys(AdvertisingPlatform::all())
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getAdvertisers(): JsonResponse
    {
        return $this->formatResponse([
            "status" => true,
            "advertisers" => AdvertiserEnum::advertisersByKey()
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getAdCostParameters(): JsonResponse
    {
        return $this->formatResponse([
            "status"        => true,
            "advertisers"   => AdvertiserEnum::advertisersByKey(),
            "platforms"     => array_keys(AdvertisingPlatform::all()),
            "industries"    => [
                Industry::SOLAR->getSlug(),
                Industry::ROOFING->getSlug(),
                Industry::WINDOWS->getSlug(),
                Industry::SIDING->getSlug(),
                Industry::BATHROOMS->getSlug(),
                Industry::KITCHENS->getSlug(),
            ],
            "resolutions"   => [AdvertisingResolution::STATE->value, AdvertisingResolution::COUNTY->value],
        ]);
    }

    /**
     * @param AdCostBackfillRequest $request
     * @return JsonResponse
     */
    public function adCostBackfill(AdCostBackfillRequest $request): JsonResponse
    {
        $parameters = $request->safe()->toArray();

        $exitCode = Artisan::call('update:ad-cost-data', [
            '--start-date'      => $parameters[AdCostBackfillRequest::DATE_RANGE][0],
            '--end-date'        => $parameters[AdCostBackfillRequest::DATE_RANGE][1],
            '--industries'      => implode(',', $parameters[AdCostBackfillRequest::INDUSTRIES]),
            '--ad-sources'      => implode(',', $parameters[AdCostBackfillRequest::PLATFORMS]),
            '--advertisers'     => implode(',', $parameters[AdCostBackfillRequest::ADVERTISERS]),
            '--resolutions'     => implode(',', $parameters[AdCostBackfillRequest::RESOLUTIONS]),
            '--clear-section'   => $parameters[AdCostBackfillRequest::CLEAR_SECTION],
            '-q'                => true,
        ]);

        return $this->formatResponse([
            "status" => $exitCode === 0,
        ]);
    }

    /**
     * @param IndustryRepository $industryRepository
     * @return JsonResponse
     */
    public function getTieredAdsOptions(IndustryRepository $industryRepository): JsonResponse
    {
        $platforms = array_map(function ($platform) {return $platform->value;}, AdvertisingPlatform::cases());
        $industries = $industryRepository->getIndustriesWithFutureCampaignsActive()->toArray();
        $industries = array_map(function ($industry) {
            return [
                'id'    => $industry[IndustryModel::FIELD_SLUG],
                'name'  => $industry[IndustryModel::FIELD_NAME],
            ];
        }, $industries);


        return $this->formatResponse([
            "platforms"             => $platforms,
            "industries"            => $industries,
            "advertisers"           => Advertiser::getAsIdNameArray(),
            "is_advertising_admin"  => Auth::user()->hasPermissionTo("advertising-admin"),
            "status"                => true,
        ]);
    }

    /**
     * @param TieredAdvertisingService $tieredAdvertisingService
     * @param string $platform
     * @param string $industry
     * @param TieredAdvertisingCampaignsRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function getTieredAdsCampaigns(
        TieredAdvertisingService $tieredAdvertisingService,
        string $platform,
        string $industry,
        TieredAdvertisingCampaignsRequest $request
    ): JsonResponse
    {
        $industryId = Industry::fromSlug($industry)->model()->{IndustryModel::FIELD_ID};
        $instanceId = $request->validated()[TieredAdvertisingCampaignsRequest::INSTANCE] ?? null;

        $instanceOptions = $tieredAdvertisingService->getInstanceOptions($platform, $industryId);
        if (count($instanceOptions) > 0 && $instanceId === null) {
            $instanceId = $instanceOptions[0]['id'];
        } else if ($instanceId !== null) {
            $instanceId = (int) $instanceId;
        }

        $config = $tieredAdvertisingService->getConfigModel($platform, $industryId, $instanceId);

        return $this->formatResponse([
            "campaigns"         => TieredAdvertisingCampaignResource::collection($tieredAdvertisingService->getTieredAdvertisingCampaigns($platform, $industry, $instanceId)),
            "enabled"           => $config?->{TieredAdvertisingConfiguration::FIELD_ENABLED} ?? false,
            "update_frequency"  => $config?->{TieredAdvertisingConfiguration::FIELD_UPDATE_FREQUENCY_MINUTES} ?? TieredAdvertisingService::DEFAULT_FREQUENCY_MINS,
            "roas"              => $config?->{TieredAdvertisingConfiguration::FIELD_ROAS} ?? TieredAdvertisingService::DEFAULT_ROAS,
            "advertiser"        => $config?->{TieredAdvertisingConfiguration::FIELD_ADVERTISER} ?? TieredAdvertisingService::DEFAULT_ADVERTISER,
            "instance"          => $instanceId,
            "instance_options"  => $instanceOptions,
            "status"            => true,
        ]);
    }

    /**
     * @param TieredAdvertisingUpdateRequest $request
     * @param TieredAdvertisingService $tieredAdvertisingService
     * @param string $platform
     * @param string $industry
     * @return JsonResponse
     * @throws Exception
     */
    public function updateTieredAdsCampaigns(
        TieredAdvertisingUpdateRequest $request,
        TieredAdvertisingService $tieredAdvertisingService,
        string $platform,
        string $industry,
    ): JsonResponse
    {
        $validated = $request->validated();
        $industryId = Industry::fromSlug($industry)->model()->{IndustryModel::FIELD_ID};

        $messages = $tieredAdvertisingService->updateCampaigns(
            platform: $platform,
            industrySlug: $industry,
            campaigns: $validated[TieredAdvertisingUpdateRequest::CAMPAIGNS],
            roas: $validated[TieredAdvertisingUpdateRequest::ROAS],
            updateFrequency: $validated[TieredAdvertisingUpdateRequest::UPDATE_FREQUENCY],
            enabled: $validated[TieredAdvertisingUpdateRequest::ENABLED],
            advertiser: $validated[TieredAdvertisingUpdateRequest::ADVERTISER],
            instanceId: $validated[TieredAdvertisingUpdateRequest::INSTANCE_ID] ?? null,
        );

        if (count($messages) === 0) {
            $status = true;
        } else {
            $status = false;
        }

        $config = $tieredAdvertisingService->getConfigModel($platform, $industryId, $validated[TieredAdvertisingUpdateRequest::INSTANCE_ID] ?? null);

        return $this->formatResponse([
            "campaigns"         => TieredAdvertisingCampaignResource::collection($tieredAdvertisingService->getTieredAdvertisingCampaigns($platform, $industry, $validated[TieredAdvertisingUpdateRequest::INSTANCE_ID])),
            "enabled"           => $config?->{TieredAdvertisingConfiguration::FIELD_ENABLED} ?? false,
            "update_frequency"  => $config?->{TieredAdvertisingConfiguration::FIELD_UPDATE_FREQUENCY_MINUTES} ?? TieredAdvertisingService::DEFAULT_FREQUENCY_MINS,
            "roas"              => $config?->{TieredAdvertisingConfiguration::FIELD_ROAS} ?? TieredAdvertisingService::DEFAULT_ROAS,
            "advertiser"        => $config?->{TieredAdvertisingConfiguration::FIELD_ADVERTISER} ?? TieredAdvertisingService::DEFAULT_ADVERTISER,
            "status"            => $status,
            "message"           => implode(' ', $messages),
        ]);
    }

    /**
     * @param TieredAdvertisingCreateInstanceRequest $request
     * @param TieredAdvertisingService $tieredAdvertisingService
     * @param string $platform
     * @param string $industry
     * @return JsonResponse
     */
    public function assignTieredAdsInstance(
        TieredAdvertisingCreateInstanceRequest $request,
        TieredAdvertisingService $tieredAdvertisingService,
        string $platform,
        string $industry,
    ): JsonResponse
    {
        $validated = $request->validated();
        $industryId = Industry::fromSlug($industry)->model()->{IndustryModel::FIELD_ID};

        // Create instance
        $instanceArray = $tieredAdvertisingService->createInstance(
            $platform,
            $industryId,
            $validated[TieredAdvertisingCreateInstanceRequest::INSTANCE_NAME],
            $validated[TieredAdvertisingCreateInstanceRequest::ASSIGN_EXISTING],
        );

        // Check creation status
        if ($instanceArray[TieredAdvertisingService::MESSAGE] ?? false) {
            return $this->formatResponse([
                "status"    => false,
                "message"   => $instanceArray[TieredAdvertisingService::MESSAGE],
            ]);
        }

        // Successful instance creation
        return $this->formatResponse([
            "status"        => true,
            "instance_id"   => $instanceArray[TieredAdvertisingService::INSTANCE]->{TieredAdvertisingInstance::FIELD_ID},
        ]);
    }

    /**
     * @param TieredAdvertisingDeleteInstanceRequest $request
     * @param TieredAdvertisingService $tieredAdvertisingService
     * @param string $platform
     * @param string $industry
     * @return JsonResponse
     */
    public function deleteTieredAdsInstance(
        TieredAdvertisingDeleteInstanceRequest $request,
        TieredAdvertisingService $tieredAdvertisingService,
        string $platform,
        string $industry,
    ): JsonResponse
    {
        $validated = $request->validated();
        $industryId = Industry::fromSlug($industry)->model()->{IndustryModel::FIELD_ID};

        $instanceArray = $tieredAdvertisingService->deleteInstance($platform, $industryId, $validated[TieredAdvertisingDeleteInstanceRequest::INSTANCE_ID]);

        if ($instanceArray[TieredAdvertisingService::MESSAGE] ?? false) {
            return $this->formatResponse([
                "status"    => false,
                "message"   => $instanceArray[TieredAdvertisingService::MESSAGE],
            ]);
        }

        return $this->formatResponse([
            "status" => true,
        ]);
    }

    /**
     * @param TieredAdvertisingInstanceLocationsUpdateRequest $request
     * @param string $platform
     * @param string $industry
     * @return JsonResponse
     */
    public function triggerLocationsUpdate(
        TieredAdvertisingInstanceLocationsUpdateRequest $request,
        string $platform,
        string $industry,
    ): JsonResponse
    {
        $validated = $request->validated();

        UpdateTieredAdvertisingCampaignLocationsJob::dispatch(
            platform: $platform,
            industry: $industry,
            forceUpdate: true,
            instanceId: $validated[TieredAdvertisingInstanceLocationsUpdateRequest::INSTANCE_ID],
        );

        return $this->formatResponse([
            "status" => true,
        ]);
    }

    /**
     * @param TieredAdvertisingService $tieredAdvertisingService
     * @param int $campaignId
     * @return JsonResponse
     */
    public function getTieredAdsCampaignLocations(TieredAdvertisingService $tieredAdvertisingService, int $campaignId): JsonResponse
    {
        $locations = $tieredAdvertisingService->getCampaignLocations($campaignId);
        return $this->formatResponse([
            "locations" => TieredAdvertisingCampaignLocationResource::collection($locations),
            "status"    => true,
        ]);
    }
}
