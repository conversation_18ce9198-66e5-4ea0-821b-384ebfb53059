<?php

namespace App\Http\Controllers\API\Workflows;

use App\DataModels\Workflows\TaskResultDataModel;
use App\Enums\CompanySalesStatus;
use App\Enums\EventCategory;
use App\Enums\SupportedTimezones;
use App\Enums\TaskModules;
use App\Enums\TaskResultType;
use App\Enums\Team;
use App\Enums\EventName;
use App\Events\Workflows\TaskConcludedEvent;
use App\Events\Workflows\TaskReassignedEvent;
use App\Events\Workflows\TaskRescheduledEvent;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\SearchCompaniesRequest;
use App\Http\Requests\UpdateActionRequest;
use App\Http\Requests\Workflows\CreateWorkflowRequest;
use App\Http\Requests\Workflows\MuteUnmuteTasksRequest;
use App\Http\Requests\Workflows\WorkflowTaskRequest;
use App\Http\Requests\Workflows\GetTasksRequest;
use App\Http\Resources\ActionResource;
use App\Jobs\CalculateTaskTimezoneJob;
use App\Jobs\Tasks\CreateTasksJob;
use App\Jobs\Workflows\RunWorkflowPipeline;
use App\Models\Action;
use App\Models\ActionTag;
use App\Models\Odin\Company;
use App\Models\ActionCategory;
use App\Models\Legacy\EloquentCompany;
use App\Models\RunningWorkflow;
use App\Models\Sales\Task;
use App\Models\TaskNote;
use App\Models\User;
use App\Models\Workflow;
use App\Models\WorkflowAction;
use App\Models\WorkflowEvent;
use App\Repositories\ActionRepository;
use App\Repositories\PubSubEventRepository;
use App\Repositories\TaskRepository;
use App\Services\CompanySearchService;
use App\Services\Sales\WorkflowTasksService;
use App\Services\Workflows\RunningWorkflowService;
use App\Services\Workflows\WorkflowProcessingService;
use App\Transformers\TaskNotesTransformer;
use App\Transformers\Workflows\TaskModuleTransformer;
use App\Transformers\Workflows\TasksTransformer;
use App\Transformers\Workflows\TeamTransformer;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;
use Throwable;


class TaskController extends APIController
{
    const OPTION             = 'option';
    const OPTION_ONE_HOUR    = 'one_hour';
    const OPTION_ONE_DAY     = 'one_day';
    const OPTION_ONE_WEEK    = 'one_week';
    const OPTION_ONE_MONTH   = 'one_month';
    const OPTION_CUSTOM_DATE = 'custom_date_time';
    const NOTE               = 'note';
    const USER_ID            = 'user_id';
    const TASK_ID            = 'task_id';
    const COMPANY_ID         = 'company_id';
    const COMPANY_REFERENCE  = 'company_reference';
    const TASKS              = 'tasks';
    const SUBJECT            = 'subject';
    const ACTION_TYPE        = 'action_type';
    const ALL_USER           = 'all_user';

    const ACTION_TYPE_CRM       = 'crm';
    const ACTION_TYPE_TASK_NOTE = 'task_note';
    const ACTION_NO_ACTION      = 'no_action';

    const REQUEST_SORT_COL = 'sort_col';
    const REQUEST_SORT_DIR = 'sort_dir';
    const REQUEST_DATE     = 'date';

    const REQUEST_PER_PAGE   = 'perPage';
    const REQUEST_UPDATE_SALES_STATUS = 'update_sales_status';
    const DEFAULT_PAGINATION = 100;
    const COMPANY_DATA = "company_data";

    const SORTABLE_COLUMNS = [
        Task::FIELD_SUBJECT,
        Task::FIELD_PRIORITY,
        Task::FIELD_AVAILABLE_AT,
        Task::FIELD_COMPLETED_AT,
        Task::FIELD_ASSIGNED_USER_ID
    ];

    const REQUEST_ACTION_ID = 'action_id';

    public function __construct(Request $request, JsonAPIResponseFactory $apiResponseFactory, protected TaskRepository $repository, protected CompanySearchService $companySearchService) { parent::__construct($request, $apiResponseFactory); }

    /**
     * @param WorkflowTasksService $service
     * @param TaskModuleTransformer $moduleTransformer
     * @param TaskNotesTransformer $notesTransformer
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getTask(WorkflowTasksService $service, TaskModuleTransformer $moduleTransformer, TaskNotesTransformer $notesTransformer): JsonResponse
    {
        $this->validate($this->request, [
            self::TASK_ID => 'exists:' . Task::TABLE . ',' . Task::FIELD_ID
        ]);

        /** @var Task|null $task */
        if ($this->request->has(self::TASK_ID)) {
            $task = Task::query()->findOrFail($this->request->get(self::TASK_ID));
        } else {
            $task = Task::query()->where(Task::FIELD_ASSIGNED_USER_ID, Auth::id())
                ->where(Task::FIELD_AVAILABLE_AT, '<=', Carbon::now())
                ->where(Task::FIELD_COMPLETED, false)
                ->whereHas(Task::RELATION_RUNNING_WORKFLOW)
                ->orderBy(Task::FIELD_PRIORITY, 'DESC')
                ->first();
        }

        return $this->formatResponse([
            "status"              => true,
            "task"                => $task,
            "notes"               => $task?->taskNotes()->latest()->get()->map(fn(TaskNote $note) => $notesTransformer->transformTaskNote($note))->toArray() ?? [],
            "task_payload"        => $task && ($task->runningWorkflow || $task->completedWorkflow) ? $service->getTaskPayload($task) : null,
            "modules"             => $moduleTransformer->transformModules(collect($task?->taskType->modules ?? [])->map(fn($item) => TaskModules::from($item))),
            "running_workflow_id" => $task?->running_workflow_id,
        ]);
    }

    /**
     * @param int $taskId
     * @param int $runningWorkflowId
     * @return JsonResponse
     * @throws ValidationException
     */
    public function updateRunningWorkflow(int $taskId, int $runningWorkflowId, WorkflowProcessingService $processingService, TaskRepository $repository): JsonResponse
    {
        $status = $this->request->input('status');
        $value = $this->request->input('value');

        $validator = Validator::make(
            [
                RunningWorkflow::FIELD_STATUS => $status,
            ],
            [
                RunningWorkflow::FIELD_STATUS => ['numeric', 'required'] //TODO: update statuses, add support for other result types (select/text box/etc)
            ]
        );

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        // TODO: Move to repository.
        /** @var WorkflowAction $node */
        $node = WorkflowAction::query()->find($status);

        /** @var RunningWorkflow $workflow */
        $workflow = RunningWorkflow::query()->findOrFail($runningWorkflowId);
        $workflow->payload->set('last_chosen_result', $status);
        $workflow->payload->set('last_result_value', $value);
        $workflow->payload->set('user_actioning', Auth::id());
        $workflow->payload->set('last_task', $taskId);
        $workflow->save();

        /** @var Task|null $task */
        $task = Task::query()->find($taskId);

        if($node && $node->payload["type"] === "select" && $value > 0) {
            $processingService->dispatchNewRunningWorkflow($value, $workflow->payload);
        } else if ($node && $node->payload["type"] === "reschedule" && $task && isset($node->payload['options']['times'])) {
            if($task->reschedule_count < count($node->payload['options']['times'])) {
                if($value && strlen(trim($value)) > 0) {
                    $repository->addTaskNote($task, Auth::id(), $value);
                }

                return $this->formatResponse([
                    "status" => $repository->rescheduleTask($task, $node->payload['options']['times'][$task->reschedule_count]),
                ]);
            }
        } else if ($node && $node->payload['type'] === 'text' && strlen(trim($value)) > 0) {
            $repository->addTaskNote($task, Auth::id(), $value);
        }

        RunWorkflowPipeline::dispatch($workflow);

        $this->repository->completeTask($task);

        return $this->formatResponse([
            "status" => true,
        ]);
    }

    /**
     * @param Task $task
     * @param TaskRepository $repository
     *
     * @return JsonResponse
     */
    public function updateManualTask(Task $task, TaskRepository $repository): JsonResponse
    {
        $type  = $this->request->get('type');
        $value = $this->request->get('value');

        switch ($type) {
            case TaskResultType::TEXT->value:
                $repository->addTaskNote($task, Auth::id(), $value);
                break;
            case TaskResultType::TERMINATE_CADENCE->value:
                $repository->terminateAssociatedCadenceRoutine($task);
                break;
            case TaskResultType::REATTEMPT_FAILED_ACTION->value:
                $repository->resetLastFailedGroupForAssociatedCadenceRoutine($task);
                break;
        }

        TaskConcludedEvent::dispatch($task->id, TaskConcludedEvent::CONCLUSION_COMPLETED);

        return $this->formatResponse([
            "status" => $repository->completeTask($task)
        ]);
    }

    /**
     * @param Task $task
     *
     * @return JsonResponse
     */
    public function skipTask(Task $task): JsonResponse
    {
        $task->available_at = Carbon::now()->addHour();
        $task->save();
        TaskRescheduledEvent::dispatch($task->id, $task->available_at);

        return $this->formatResponse([
            'status' => true
        ]);
    }

    /**
     * @param int|null $taskId
     * @param UpdateActionRequest $request
     * @param ActionRepository $repository
     *
     * @return JsonResponse
     */
    public function saveAction(?int $taskId, UpdateActionRequest $request, ActionRepository $repository): JsonResponse
    {
        $data = $request->safe()->collect();

        /** @var Task $task */
        $task = Task::query()->find($taskId);
        $action = $repository->updateOrCreate(
            Auth::user()->id,
            $data->get(Action::FIELD_SUBJECT),
            $data->get(Action::FIELD_MESSAGE),
            $data->get(UpdateActionRequest::REQUEST_ACTION_ID),
            $data->get(Action::FIELD_FOR_ID),
            $data->get(Action::FIELD_FOR_RELATION_TYPE),
            $task?->{Task::FIELD_ID},
            $data->get(Action::FIELD_CATEGORY_ID),
            $data->get(Action::FIELD_DISPLAY_DATE),
            $data->get(Action::FIELD_TAG_BY_EMAIL)
        );

        if (!is_null($data->get(Action::RELATION_TAGS))) {
            $repository->editActionTags($action, $data->get(Action::RELATION_TAGS));
        }

        if(!is_null($data->get(self::REQUEST_UPDATE_SALES_STATUS)) && intval($data->get(self::REQUEST_UPDATE_SALES_STATUS)) !== -1) {
            try {
                $this->handleSalesStatusChange($action, CompanySalesStatus::from(intval($data->get(self::REQUEST_UPDATE_SALES_STATUS))));
            } catch(Exception $e) {}
        }

        return $this->formatResponse([
            'status' => true,
            'action' => new ActionResource($action)
        ]);
    }

    /**
     * Handles updating sales status.
     *
     * @param Action $action
     * @param CompanySalesStatus $status
     * @return void
     */
    protected function handleSalesStatusChange(Action $action, CompanySalesStatus $status): void
    {
        if($action->for_id === null || $action->for_relation_type !== Action::RELATION_TYPE_COMPANY)
            return;

        /** @var Company $company */
        $company = Company::query()->find($action->for_id);

        if(!$company || $company->sales_status === $status->value)
            return;

        $action->previous_sales_status = $company->sales_status;
        $action->updated_sales_status = $status;
        $action->save();

        $company->sales_status = $status;
        $company->save();
    }

    /**
     * @param Task $task
     * @param TaskRepository $taskRepository
     * @return JsonResponse
     */
    public function rescheduleTask(Task $task, TaskRepository $taskRepository): JsonResponse
    {
        $available_at = $this->availableDatesOptions(
            $this->request->get(self::OPTION),
            $this->request->get(Task::FIELD_AVAILABLE_AT)
        );

        $taskRepository->updateRescheduleTask($task, $available_at, $this->request->input('note') ?? "");

        return $this->formatResponse(['status' => true]);
    }

    /**
     * @param int $taskId
     *
     * @return JsonResponse
     */
    public function getTaskNotes(int $taskId, TaskNotesTransformer $transformer): JsonResponse
    {
        $task = Task::query()->findOrFail($taskId);
        return $this->formatResponse([
            "taskNotes" => $transformer->transformCampaigns($task->taskNotes()->get())
        ]);
    }

    /**
     * @param Task $task
     * @param TaskNotesTransformer $transformer
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function addTaskNotes(Task $task, TaskNotesTransformer $transformer): JsonResponse
    {
        $this->validate($this->request, [
            self::NOTE => 'required|string'
        ]);

        /** @var TaskNote $taskNote */
        $taskNote = $task->taskNotes()->create([
            TaskNote::FIELD_NOTE => $this->request->get(self::NOTE),
            TaskNote::FIELD_USER_ID => Auth::id()
        ]);

        return $this->formatResponse([
            'status' => true,
            'task_note' => $transformer->transformTaskNote($taskNote)
        ]);
    }

    /**
     * @param Task $task
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function reassignTask(Task $task): JsonResponse
    {
        $this->validate($this->request, [
            self::USER_ID => 'exists:' . User::TABLE . ',' . User::FIELD_ID
        ]);

        $previousTaskAssignee = $task->assigned_user_id;

        $userId = Auth::id();

        if($previousTaskAssignee != $userId) {
            return response()->json(['message' => 'Only the assigned user can reassign a task to another user.'], 401);
        }

        $task->assigned_user_id = $this->request->get(self::USER_ID);
        TaskReassignedEvent::dispatch($previousTaskAssignee, $task);
        return $this->formatResponse(['status' => $task->save()]);
    }

    /**
     * @param TeamTransformer $transformer
     *
     * @return JsonResponse
     */
    public function getTeams(TeamTransformer $transformer): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();
        $team = [];

        $team[] = $transformer->transformTeam(Team::ACCOUNT_MANAGER);
        $team[] = $transformer->transformTeam(Team::SUPPORT_OFFICER);
        $team[] = $transformer->transformTeam(Team::HUNTER);

        return $this->formatResponse($team);
    }

    /**
     * @param GetTasksRequest $request
     * @param TasksTransformer $transformer
     * @return JsonResponse
     */
    public function getAllTasks(GetTasksRequest $request, TasksTransformer $transformer): JsonResponse
    {
        $requestedFilters = $request->safe()->collect();

        $tasks = $this->repository->getAllTasksForUser(
            $requestedFilters->get(self::ALL_USER) === 'true' ? null : Auth::id(),
            $requestedFilters->get(Task::FIELD_SUBJECT),
            $requestedFilters->get(Task::FIELD_PRIORITY),
            $requestedFilters->get(self::REQUEST_SORT_COL),
            $requestedFilters->get(self::REQUEST_SORT_DIR),
            $requestedFilters->get(self::COMPANY_ID),
            $requestedFilters->get(Task::FIELD_TASK_CATEGORY_ID),
            SupportedTimezones::tryFrom($request->get('timezone', "all")),
            $requestedFilters->has(self::REQUEST_DATE) ? Carbon::createFromFormat('Y-m-d', $requestedFilters->get(self::REQUEST_DATE)) : null,
        );

        return $this->formatResponse([
            'status' => true,
            'tasks'  => $tasks
                ->paginate($this->request->get(self::REQUEST_PER_PAGE, self::DEFAULT_PAGINATION))
                ->through(fn (Task $task) => $transformer->transform($task))
        ]);
    }

    /**
     * @param GetTasksRequest $request
     * @param TasksTransformer $transformer
     * @return JsonResponse
     */
    public function getTodayTasks(GetTasksRequest $request, TasksTransformer $transformer): JsonResponse
    {
        $requestedFilters = $request->safe()->collect();

        $tasks = $this->repository->getTodayTasksForUser(
            $requestedFilters->get(self::ALL_USER) === 'true' ? null : Auth::id(),
            $requestedFilters->get(Task::FIELD_SUBJECT),
            $requestedFilters->get(Task::FIELD_PRIORITY),
            $requestedFilters->get(self::REQUEST_SORT_COL),
            $requestedFilters->get(self::REQUEST_SORT_DIR),
            $requestedFilters->get(self::COMPANY_ID),
            $requestedFilters->get(Task::FIELD_TASK_CATEGORY_ID),
            SupportedTimezones::tryFrom($request->get('timezone', "all"))
        );

        return $this->formatResponse([
            'status' => true,
            'tasks'  => $tasks
                ->paginate($this->request->get(self::REQUEST_PER_PAGE, self::DEFAULT_PAGINATION))
                ->through(fn (Task $task) => $transformer->transform($task))
        ]);
    }

    /**
     * @param GetTasksRequest $request
     * @param TasksTransformer $transformer
     * @return JsonResponse
     */
    public function getOverdueTasks(GetTasksRequest $request, TasksTransformer $transformer): JsonResponse
    {
        $requestedFilters = $request->safe()->collect();

        $tasks = $this->repository->getOverdueTasksForUser(
            $requestedFilters->get(self::ALL_USER) === 'true' ? null : Auth::id(),
            $requestedFilters->get(Task::FIELD_SUBJECT),
            $requestedFilters->get(Task::FIELD_PRIORITY),
            $requestedFilters->get(self::REQUEST_SORT_COL),
            $requestedFilters->get(self::REQUEST_SORT_DIR),
            $requestedFilters->get(self::COMPANY_ID),
            $requestedFilters->get(Task::FIELD_TASK_CATEGORY_ID),
            SupportedTimezones::tryFrom($request->get('timezone', "all")),
            $requestedFilters->has(self::REQUEST_DATE) ? Carbon::createFromFormat('Y-m-d', $requestedFilters->get(self::REQUEST_DATE)) : null,
        );

        return $this->formatResponse([
            'status' => true,
            'tasks'  => $tasks
                ->paginate($this->request->get(self::REQUEST_PER_PAGE, self::DEFAULT_PAGINATION))
                ->through(fn (Task $task) => $transformer->transform($task))
        ]);
    }

    /**
     * @param GetTasksRequest $request
     * @param TasksTransformer $transformer
     * @return JsonResponse
     */
    public function getUpcomingTasks(GetTasksRequest $request, TasksTransformer $transformer): JsonResponse
    {
        $requestedFilters = $request->safe()->collect();

        $tasks = $this->repository->getUpcomingTasksForUser(
            $requestedFilters->get(self::ALL_USER) === 'true' ? null : Auth::id(),
            $requestedFilters->get(Task::FIELD_SUBJECT),
            $requestedFilters->get(Task::FIELD_PRIORITY),
            $requestedFilters->get(self::REQUEST_SORT_COL),
            $requestedFilters->get(self::REQUEST_SORT_DIR),
            $requestedFilters->get(self::COMPANY_ID),
            $requestedFilters->get(Task::FIELD_TASK_CATEGORY_ID),
            SupportedTimezones::tryFrom($request->get('timezone', "all"))
        );

        return $this->formatResponse([
            'status' => true,
            'tasks'  => $tasks
                ->paginate($this->request->get(self::REQUEST_PER_PAGE, self::DEFAULT_PAGINATION))
                ->through(fn (Task $task) => $transformer->transform($task))
        ]);
    }

    /**
     * @param GetTasksRequest $request
     * @param TasksTransformer $transformer
     * @return JsonResponse
     */
    public function getCompletedTasks(GetTasksRequest $request, TasksTransformer $transformer): JsonResponse
    {
        $requestedFilters = $request->safe()->collect();

        $tasks = $this->repository->getCompletedTasksForUser(
            $requestedFilters->get(self::ALL_USER) === 'true' ? null : Auth::id(),
            $requestedFilters->get(Task::FIELD_SUBJECT),
            $requestedFilters->get(Task::FIELD_PRIORITY),
            $requestedFilters->get(self::REQUEST_SORT_COL),
            $requestedFilters->get(self::REQUEST_SORT_DIR),
            $requestedFilters->get(self::COMPANY_ID),
            $requestedFilters->get(Task::FIELD_TASK_CATEGORY_ID),
            SupportedTimezones::tryFrom($request->get('timezone', "all"))
        );

        return $this->formatResponse([
            'status' => true,
            'tasks'  => $tasks
                ->paginate($this->request->get(self::REQUEST_PER_PAGE, self::DEFAULT_PAGINATION))
                ->through(fn (Task $task) => $transformer->transform($task))
        ]);
    }

    /**
     * @param GetTasksRequest $request
     * @param TasksTransformer $transformer
     * @return JsonResponse
     */
    public function getMutedTasks(GetTasksRequest $request, TasksTransformer $transformer): JsonResponse
    {
        $requestedFilters = $request->safe()->collect();

        $tasks = $this->repository->getMutedTasksForUser(
            $requestedFilters->get(self::ALL_USER) === 'true' ? null : Auth::id(),
            $requestedFilters->get(Task::FIELD_SUBJECT),
            $requestedFilters->get(Task::FIELD_PRIORITY),
            $requestedFilters->get(self::REQUEST_SORT_COL),
            $requestedFilters->get(self::REQUEST_SORT_DIR),
            $requestedFilters->get(self::COMPANY_ID),
            $requestedFilters->get(Task::FIELD_TASK_CATEGORY_ID),
            SupportedTimezones::tryFrom($request->get('timezone', "all"))
        );

        return $this->formatResponse([
            'status' => true,
            'tasks'  => $tasks
                ->paginate($this->request->get(self::REQUEST_PER_PAGE, self::DEFAULT_PAGINATION))
                ->through(fn (Task $task) => $transformer->transform($task))
        ]);
    }

    /**
     * Returns the task overview
     *
     * @param GetTasksRequest $request
     *
     * @return JsonResponse
     */
    public function getTaskOverview(GetTasksRequest $request): JsonResponse
    {
        $companyId = $request->safe()->collect()->get(self::COMPANY_ID);

        $overview = $this->repository->getCountOverview(
            $request->safe()->offsetGet(self::ALL_USER) === 'true' ? null : Auth::id(),
            $companyId,
        );

        return $this->formatResponse([
            'status' => true,
            'overview' => $overview,
        ]);
    }

    /**
     * Returns the task overview
     *
     * @param GetTasksRequest $request
     *
     * @return JsonResponse
     */
    public function getMinimalTaskCounts(GetTasksRequest $request): JsonResponse
    {
        $companyId = $request->safe()->collect()->get(self::COMPANY_ID);

        $counts = $this->repository->getMinimalCounts(
            null,
            $companyId,
        );

        return $this->formatResponse([
             'status' => true,
             'counts' => $counts,
         ]);
    }

    /**
     * @param TaskRepository $taskRepository
     * @param WorkflowTaskRequest $taskRequest
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function createTaskForCompany(TaskRepository $taskRepository, WorkflowTaskRequest $taskRequest): JsonResponse
    {
        $requestData = $taskRequest->safe()->collect();

        try {
            $taskRepository->createTask(
                $requestData->get(Task::FIELD_SUBJECT),
                $requestData->get(Task::FIELD_TASK_TYPE_ID),
                $requestData->get(Task::FIELD_PRIORITY),
                $requestData->get(Task::FIELD_TASK_CATEGORY_ID),
                $requestData->get(Task::FIELD_AVAILABLE_AT),
                $requestData->get(self::COMPANY_ID)
            );
        } catch (Exception $exception) {
            return $this->formatResponse([
                'status' => false,
                'message' => $exception->getMessage()
            ], 400);
        }

        return $this->formatResponse([
            'status' => true
        ]);
    }

    /**
     * Create Tasks in Bulk
     * @param TaskRepository $taskRepository
     * @param WorkflowTaskRequest $taskRequest
     * @param SearchCompaniesRequest $searchRequest
     * @return JsonResponse
     */
    public function createTasksFromCompanySearch(TaskRepository $taskRepository, WorkflowTaskRequest $taskRequest, SearchCompaniesRequest $searchRequest): JsonResponse
    {
        try {
            $taskRequestData = $taskRequest->safe()->collect();

            $companyIds = $this->companySearchService->getCompanyIds(
                $searchRequest,
                $searchRequest->get(self::COMPANY_DATA)
            );

            CreateTasksJob::dispatch(
                Auth::id(),
                $taskRequestData->get(Task::FIELD_SUBJECT),
                $taskRequestData->get(Task::FIELD_TASK_TYPE_ID),
                $taskRequestData->get(Task::FIELD_PRIORITY),
                $taskRequestData->get(Task::FIELD_TASK_CATEGORY_ID),
                $taskRequestData->get(Task::FIELD_AVAILABLE_AT),
                $companyIds
            );
        } catch (Exception $exception) {
            return $this->formatResponse([
                'status' => false,
                'message' => $exception->getMessage()
            ], 400);
        }

        return $this->formatResponse([
            'status' => true
        ]);
    }

    /**
     * @param Task $task
     * @return JsonResponse
     * @throws ValidationException
     */
    public function updateSubject(Task $task): JsonResponse
    {
        $this->validate($this->request,  [
            Task::FIELD_SUBJECT => 'required|string|max:255'
        ]);
        $subject = $this->request->get(Task::FIELD_SUBJECT);
        $task->subject = $subject;
        $task->save();

        return $this->formatResponse([
            "status" => true,
        ]);
    }

    /**
     * @param Task $task
     * @return JsonResponse
     * @throws ValidationException
     */
    public function updatePriority(Task $task): JsonResponse
    {
        $this->validate($this->request,  [
            Task::FIELD_PRIORITY => 'required|integer|between:1,4'
        ]);
        $priority = $this->request->get(Task::FIELD_PRIORITY);
        $task->priority = $priority;
        $task->save();

        return $this->formatResponse([
            "status" => true,
        ]);
    }

    /**
     * Reschedules multiple tasks.
     *
     * @param TaskRepository $taskRepository
     * @return JsonResponse
     * @throws ValidationException
     */
    public function batchReschedule(TaskRepository $taskRepository): JsonResponse
    {
        $this->validate($this->request, [
            self::TASKS   => 'required|array',
            self::TASKS . '.*' => 'integer',
        ]);

        $available_at = $this->availableDatesOptions(
            $this->request->get(self::OPTION),
            $this->request->get(Task::FIELD_AVAILABLE_AT)
        );

        $tasks = Task::query()->whereIn(Task::FIELD_ID, $this->request->get(self::TASKS))->get();

        $taskRepository->bulkRescheduleTasks($tasks, $available_at, $this->request->input('note'));

        return $this->formatResponse([
            "status" => true,
        ]);
    }

    /**
     *
     * @param string $date_options
     * @param Carbon|string|null $available_at
     * @return Carbon|string
     */
    private function availableDatesOptions(string $date_options, Carbon|string|null $available_at) : Carbon|string
    {
        switch ($date_options) {
            case self::OPTION_ONE_HOUR:
                $available_at = Carbon::now()->addHour();
                break;
            case self::OPTION_ONE_DAY:
                $available_at = Carbon::now()->addDay();
                break;
            case self::OPTION_ONE_WEEK:
                $available_at = Carbon::now()->addWeek();
                break;
            case self::OPTION_ONE_MONTH:
                $available_at = Carbon::now()->addMonth();
                break;
            case self::OPTION_CUSTOM_DATE:
                $available_at = Carbon::parse($available_at)->format('Y-m-d H:i');
                break;
        }

        return $available_at;
    }


    /**
     * @param TaskRepository $taskRepository
     * @param ActionRepository $actionRepository
     * @param RunningWorkflowService $runningWorkflowService
     *
     * @return JsonResponse
     * @throws Throwable
     * @throws ValidationException
     */
    public function batchComplete(TaskRepository $taskRepository, ActionRepository $actionRepository, RunningWorkflowService $runningWorkflowService): JsonResponse
    {
        $this->validate($this->request, [
            self::TASKS   => 'required|array',
            self::TASKS . '.*' => 'integer',
            self::NOTE    => [
                Rule::requiredIf(fn() => $this->request->get(self::ACTION_TYPE) !== self::ACTION_NO_ACTION),
                'string'
            ],
            self::SUBJECT => [
                Rule::requiredIf(fn() => $this->request->get(self::ACTION_TYPE) === self::ACTION_TYPE_CRM),
                'string'
            ]
        ]);

        $query = Task::query()->with([Task::RELATION_RUNNING_WORKFLOW])->whereIn(Task::FIELD_ID, $this->request->get(self::TASKS));

        $taskRepository->completeTasks($query);

        /** @var Collection<Task> $tasks */
        $tasks = $query->get();

        $workflowsToComplete = collect();

        foreach ($tasks as $task) {
            TaskConcludedEvent::dispatch($task->id, TaskConcludedEvent::CONCLUSION_COMPLETED);

            switch ($this->request->get(self::ACTION_TYPE)) {
                case self::ACTION_TYPE_CRM:
                    $actionRepository->createAction(
                        Auth::id(),
                        $this->request->get(self::SUBJECT),
                        $this->request->get(self::NOTE),
                        $this->getForId($task),
                        Action::RELATION_TYPE_COMPANY,
                        $task->id
                    );
                    break;
                case self::ACTION_TYPE_TASK_NOTE:
                    $taskRepository->addTaskNote($task, Auth::id(), $this->request->get(self::NOTE));
                    break;
                case self::ACTION_NO_ACTION:
                default:
                    break;
            }

            if ($task->runningWorkflow) $workflowsToComplete->push($task->runningWorkflow);
        }

        $runningWorkflowService->completeWorkflows($workflowsToComplete);

        return $this->formatResponse([
            "status" => true,
        ]);
    }

    /**
     * @param Task $task
     *
     * @return JsonResponse
     */
    public function deleteManualTask(Task $task): JsonResponse
    {
        if (!$task->manual || $task->completed) throw new BadRequestException('Bad request');

        TaskConcludedEvent::dispatch($task->id, TaskConcludedEvent::CONCLUSION_DELETED);

        return $this->formatResponse([
            'status' => !!$task->delete()
        ]);
    }

    /**
     * @param Task $task
     *
     * @return int|null
     */
    protected function getForId(Task $task): int|null
    {
        if ($task->runningWorkflow) {
            $companyId        = $task->runningWorkflow->payload?->event?->get(self::COMPANY_ID);
            $companyReference = $task->runningWorkflow->payload?->event?->get(self::COMPANY_REFERENCE);

            if ($companyId) return $companyId;

            return Company::query()->where(Company::FIELD_REFERENCE, $companyReference)->first()?->id;
        }

        return $task->payload ? $task->payload[self::COMPANY_ID] ?? null : null;
    }

    /**
     * @param MuteUnmuteTasksRequest $request
     * @param bool $mute
     * @return JsonResponse
     */
    public function muteOrUnmuteTasksByAction(MuteUnmuteTasksRequest $request, bool $mute): JsonResponse
    {
        $actionId = $request->getActionId();
        $mute = filter_var($mute, FILTER_VALIDATE_BOOLEAN);

        $runningWorkFlowIds = $this->repository->getRunningWorkflowIdsByActionId($actionId);
        $updatedCount = $this->repository->updateTasksMuteStatus($runningWorkFlowIds, $mute);

        return $this->formatResponse([
            'status' => (bool) $updatedCount,
            'updated_count' => $updatedCount
        ]);
    }

}
