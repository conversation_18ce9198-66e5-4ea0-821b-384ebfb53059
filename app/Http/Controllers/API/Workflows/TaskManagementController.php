<?php

namespace App\Http\Controllers\API\Workflows;

use App\Enums\DynamicPriorityTypes;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Enums\TaskModules;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\CreateTaskTypeRequest;
use App\Http\Requests\UpdateTaskTypeRequest;
use App\Http\Requests\Workflows\CreateWorkflowActionRequest;
use App\Http\Requests\Workflows\CreateWorkflowEventRequest;
use App\Http\Requests\Workflows\CreateWorkflowRequest;
use App\Http\Requests\Workflows\CreateWorkflowTestEventRequest;
use App\Http\Requests\Workflows\DeleteWorkflowRequest;
use App\Http\Requests\Workflows\StoreTaskCategoryRequest;
use App\Http\Requests\Workflows\UpdateWorkflowActionRequest;
use App\Http\Requests\Workflows\UpdateWorkflowNameRequest;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Company;
use App\Models\TaskCategory;
use App\Models\TaskType;
use App\Models\Workflow;
use App\Models\WorkflowAction;
use App\Models\WorkflowEvent;
use App\Repositories\PubSubEventRepository;
use App\Repositories\Workflows\WorkflowRepository;
use App\Services\Workflows\WorkflowEventService;
use App\Transformers\Workflows\TaskCategoryTransformer;
use App\Transformers\Workflows\TaskModuleTransformer;
use App\Transformers\Workflows\TaskTypeTransformer;
use App\Transformers\Workflows\WorkflowActionTransformer;
use App\Transformers\Workflows\WorkflowEventTransformer;
use App\Transformers\Workflows\WorkflowTransformer;
use App\Workflows\Shortcodes\WorkflowShortcodeServiceFactory;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use function PHPUnit\Framework\isEmpty;

class TaskManagementController extends APIController
{

    const REQUEST_COMPANY_ID        = 'company_id';
    const REQUEST_CAMPAIGN_ID       = 'campaign_id';
    const REQUEST_LEAD_ID           = 'lead_id';
    const REQUEST_EVENT_CATEGORY    = 'event_category';
    const REQUEST_EVENT_NAME        = 'event_name';

    const TEST_EVENT_FIELD_MAP = [
        self::REQUEST_EVENT_CATEGORY    =>     "type",
        self::REQUEST_EVENT_NAME        =>     "event",
    ];

    /**
     * @param PubSubEventRepository $pubSubEventRepository
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function getEventCategories(PubSubEventRepository $pubSubEventRepository, Request $request): JsonResponse
    {
        return $this->formatResponse($pubSubEventRepository->getAllEvents());
    }

    /**
     * @param CreateWorkflowEventRequest $request
     *
     * @return JsonResponse
     */
    public function createWorkflowEvent(CreateWorkflowEventRequest $request): JsonResponse
    {
        /** @var WorkflowEvent $workflowEvent */
        $workflowEvent = WorkflowEvent::query()->create($request->safe()->only([
            WorkflowEvent::FIELD_EVENT_CATEGORY,
            WorkflowEvent::FIELD_EVENT_NAME,
        ]));

        return $this->formatResponse([$workflowEvent->id]);
    }

    /**
     * @return JsonResponse
     */
    public function getEvents(WorkflowEventTransformer $transformer): JsonResponse
    {
        $query = WorkflowEvent::query();

        if ($this->request->get(WorkflowEvent::FIELD_EVENT_CATEGORY)) {
            $query->where(WorkflowEvent::FIELD_EVENT_CATEGORY, $this->request->get(WorkflowEvent::FIELD_EVENT_CATEGORY));
        }

        if ($this->request->get(WorkflowEvent::FIELD_EVENT_NAME)) {
            $query->where(WorkflowEvent::FIELD_EVENT_NAME, 'LIKE', '%'.$this->request->get(WorkflowEvent::FIELD_EVENT_NAME).'%');
        }

        return $this->formatResponse($transformer->transformEvents($query->get()));
    }

    /**
     * Handles the creation of a workflow.
     *
     * @param CreateWorkflowRequest $request
     * @return JsonResponse
     */
    public function createWorkflow(CreateWorkflowRequest $request): JsonResponse
    {
        /** @var Workflow $workflow */
        $workflow = Workflow::query()->create($request->safe()->only([
            Workflow::FIELD_WORKFLOW_EVENT_ID,
            Workflow::FIELD_ENTRY_ACTION_ID,
            Workflow::FIELD_NAME,
            Workflow::FIELD_GENERIC,
        ]));

        return $this->formatResponse([
            "status"            => true,
            "id"                => $workflow->id,
            "name"              => $workflow->name,
            "workflow_event_id" => $workflow->workflow_event_id,
        ]);
    }

    /**
     * Handles the deletion of a workflow.
     *
     * @param DeleteWorkflowRequest $request
     * @return JsonResponse
     */
    public function deleteWorkflow(DeleteWorkflowRequest $request): JsonResponse
    {
        /** @var Workflow $workflow */
        $workflow = Workflow::query()->findOrFail($request->safe()->collect()->get('id'));

        $workflow->actions()->delete();

        return $this->formatResponse([
            "status" => $workflow->delete(),
        ]);
    }

    /**
     * Handles renaming a workflow
     *
     * @param UpdateWorkflowNameRequest $request
     * @return JsonResponse
     */
    public function updateWorkflowName(int $workflowId, UpdateWorkflowNameRequest $request) : JsonResponse
    {
        /** @var Workflow $workflow */
        $workflow      = Workflow::query()->findOrFail($workflowId);
        $requestedData = $request->safe()->collect();

        $workflow->name = $requestedData->get(Workflow::FIELD_NAME);
        $workflow->save();

        return $this->formatResponse([
            "status" => true,
        ]);
    }

    /**
     * @param Workflow $workflow
     *
     * @return JsonResponse
     */
    public function saveWorkflowAsTemplate(Workflow $workflow): JsonResponse
    {
        $workflow->workflow_event_id = null;
        $workflow->generic           = true;

        return $this->formatResponse([
            'status' => $workflow->save()
        ]);
    }

    /**
     * Returns the list of workflows for a given event.
     *
     * @param int $eventId
     * @param WorkflowTransformer $transformer
     * @return JsonResponse
     */
    public function getWorkflows(int $eventId, WorkflowRepository $repository, WorkflowTransformer $transformer): JsonResponse
    {
        return $this->formatResponse([
            "workflows" => $transformer->transformWorkflows(
                $eventId === 0 ? $repository->getGenericWorkflows() : $repository->getWorkflowsForEvent($eventId)
            ),
        ]);
    }

    /**
     * Create workflow action
     *
     * @param int $workflowId
     * @param CreateWorkflowActionRequest $request
     * @param WorkflowActionTransformer $actionTransformer
     * @return JsonResponse
     */
    public function createWorkflowAction(int $workflowId, CreateWorkflowActionRequest $request, WorkflowActionTransformer $actionTransformer): JsonResponse
    {
        /** @var Workflow $workflow */
        $workflow      = Workflow::query()->findOrFail($workflowId);
        $requestedData = $request->safe()->collect();

        /** @var WorkflowAction $workflowAction */
        $workflowAction = $workflow->actions()->create([
            WorkflowAction::FIELD_DISPLAY_NAME       => $requestedData->get(WorkflowAction::FIELD_DISPLAY_NAME),
            WorkflowAction::FIELD_ACTION_TYPE        => $requestedData->get(WorkflowAction::FIELD_ACTION_TYPE),
            WorkflowAction::FIELD_PAYLOAD            => $requestedData->get(WorkflowAction::FIELD_PAYLOAD),
            WorkflowAction::FIELD_PREVIOUS_NODE_ID   => $requestedData->get(WorkflowAction::FIELD_PREVIOUS_NODE_ID) ?? 0,
            WorkflowAction::FIELD_PREVIOUS_NODE_TYPE => $requestedData->get(WorkflowAction::FIELD_PREVIOUS_NODE_TYPE) ?? '',
        ]);

        // Update the payload array with the action ID
        $workflowAction->payload = array_merge(
            $workflowAction->payload ?? [],
            ['action_id' => $workflowAction->id]
        );
        $workflowAction->save();

        if (!$workflow->entry_action_id) {
            $workflow->entry_action_id = $workflowAction->id;
            $workflow->save();
        }

        return $this->formatResponse([
            "status" => true,
            "action" => $actionTransformer->transform($workflowAction),
        ]);
    }

    /**
     * @param int $workflowId
     * @param int $actionId
     * @param UpdateWorkflowActionRequest $actionRequest
     * @param WorkflowActionTransformer $transformer
     *
     * @return JsonResponse
     */
    public function updateWorkflowAction(
        int $workflowId,
        int $actionId,
        UpdateWorkflowActionRequest $actionRequest,
        WorkflowActionTransformer $transformer
    ): JsonResponse {
        /** @var WorkflowAction $workflowAction */
        $workflowAction = WorkflowAction::query()
                                        ->where(WorkflowAction::FIELD_WORKFLOW_ID, $workflowId)
                                        ->where(WorkflowAction::FIELD_ID, $actionId)
                                        ->firstOrFail();

        $workflowAction->update($actionRequest->safe()->all());

        // Ensure action_id is included in the payload
        $workflowAction->payload = array_merge(
            $workflowAction->payload ?? [],
            ['action_id' => $workflowAction->id]
        );
        $workflowAction->save();

        return $this->formatResponse([
            "status" => true,
            "action" => $transformer->transform($workflowAction),
        ]);
    }

    /**
     * @param int $workflowId
     * @param int $actionId
     * @return JsonResponse
     */
    public function deleteWorkflowAction(int $workflowId, int $actionId) : JsonResponse
    {
        /** @var Workflow $workflow */
        $workflow      = Workflow::query()->findOrFail($workflowId);

        /** @var WorkflowAction $workflowAction */
        $workflowAction = WorkflowAction::query()
            ->where(WorkflowAction::FIELD_WORKFLOW_ID, $workflowId)
            ->where(WorkflowAction::FIELD_ID, $actionId)
            ->firstOrFail();

        if($workflow->entry_action_id === $workflowAction->id) {
            if($workflowAction->siblings->isEmpty()) {
                $workflow->entry_action_id = null;
            } else {
                $workflow->entry_action_id = $workflowAction->siblings()->first()->{WorkflowAction::FIELD_ID};
            }
            $workflow->save();
        } else {
            $workflowActionSibling = WorkflowAction::query()
                ->where(WorkflowAction::FIELD_WORKFLOW_ID, $workflowId)
                ->where(WorkflowAction::FIELD_PREVIOUS_NODE_ID, $workflowAction->{WorkflowAction::FIELD_ID})
                ->where(WorkflowAction::FIELD_PREVIOUS_NODE_TYPE, WorkflowAction::PARENT_TYPE_SIBLING)
                ->first();

            if($workflowActionSibling) {
                $workflowActionSibling->{WorkflowAction::FIELD_PREVIOUS_NODE_ID} = $workflowAction->{WorkflowAction::FIELD_PREVIOUS_NODE_ID};
                $workflowActionSibling->save();
            }
        }

        $workflowAction->children->map(function (WorkflowAction $action) {
            $action->delete();
        });

        $workflowAction->delete();

        return $this->formatResponse([
            "status" => true,
        ]);
    }

    /**
     * Returns the list of available shortcodes.
     * If event_name is provided on the request, shortcodes will filter out any conditional shortcodes which fail their predicate method
     * @param WorkflowShortcodeServiceFactory $workflowShortcodeServiceFactory
     * @return JsonResponse
     * @throws Exception
     */
    public function getWorkflowShortcodes(WorkflowShortcodeServiceFactory $workflowShortcodeServiceFactory): JsonResponse
    {
        $event = EventName::tryFrom($this->request->get(self::REQUEST_EVENT_NAME) ?? '');
        $shortcodes = $workflowShortcodeServiceFactory::makeService(WorkflowShortcodeServiceFactory::WORKFLOW_DRIVER)->getShortcodes($event);

        return $this->formatResponse([ ...$shortcodes ]);
    }

    /**
     * Returns the list of task types.
     *
     * @param TaskTypeTransformer $transformer
     * @return JsonResponse
     */
    public function getTaskTypes(TaskTypeTransformer $transformer): JsonResponse
    {
        return $this->formatResponse([
            "task_types" => $transformer->transformTaskTypes(TaskType::all()),
        ]);
    }

    /**
     * Handles the creation of a task type.
     *
     * @param CreateTaskTypeRequest $request
     * @return JsonResponse
     */
    public function createTaskType(CreateTaskTypeRequest $request): JsonResponse
    {
        $taskType = TaskType::query()->create($request->safe()->only([
            TaskType::FIELD_NAME,
        ]));

        return $this->formatResponse(["status" => true]);
    }

    /**
     * Deletes a given task type.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function deleteTaskType(int $id): JsonResponse
    {
        TaskType::query()->find($id)?->delete();

        return $this->formatResponse(["status" => true]);
    }

    /**
     * Deletes a given task type.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function updateTaskType(int $id, UpdateTaskTypeRequest $request): JsonResponse
    {
        $taskType = TaskType::query()->find($id);

        if($taskType) {
            $taskType->update($request->safe()->only([
                TaskType::FIELD_NAME,
                TaskType::FIELD_MODULES,
            ]));
        }

        return $this->formatResponse(["status" => true]);
    }

    /**
     * Returns the list of modules available to the system.
     *
     * @param TaskModuleTransformer $transformer
     * @return JsonResponse
     */
    public function getModules(TaskModuleTransformer $transformer): JsonResponse
    {
        return $this->formatResponse(["modules" => $transformer->transformModules(collect(TaskModules::cases()))]);
    }

    /**
     * @param CreateWorkflowTestEventRequest $request
     * @param WorkflowEventService $service
     * @return JsonResponse
     */

    public function createTestEvent(CreateWorkflowTestEventRequest $request, WorkflowEventService $service): JsonResponse
    {
        $requestedData = $request->safe()->collect();

        $category   = $requestedData->get(WorkflowEvent::FIELD_EVENT_CATEGORY);
        $event      = $requestedData->get(WorkflowEvent::FIELD_EVENT_NAME);
        $extraData = array_filter($requestedData->except([self::REQUEST_EVENT_CATEGORY, self::REQUEST_EVENT_NAME])->toArray());

        $data = [
            self::TEST_EVENT_FIELD_MAP[self::REQUEST_EVENT_CATEGORY] => $category,
            self::TEST_EVENT_FIELD_MAP[self::REQUEST_EVENT_NAME] => $event,
            ...$extraData
        ];

        if($requestedData->has(self::REQUEST_COMPANY_ID)) {
            $company = Company::findOrFail($requestedData->get(self::REQUEST_COMPANY_ID));
            $data['company_reference'] = $company->{Company::FIELD_REFERENCE};
        }

        $service->handle($category, $event, $data);

        return $this->formatResponse(["status" => true]);
    }

    /**
     * @param TaskCategoryTransformer $transformer
     *
     * @return JsonResponse
     */
    public function getTaskCategories(TaskCategoryTransformer $transformer): JsonResponse
    {
        return $this->formatResponse([
            'categories' => $transformer->transformTaskCategories(
                TaskCategory::query()->orderBy(TaskCategory::NAME)->get()
            )
        ]);
    }

    /**
     * @param StoreTaskCategoryRequest $request
     *
     * @return JsonResponse
     */
    public function createTaskCategory(StoreTaskCategoryRequest $request): JsonResponse
    {
        return $this->formatResponse([
            'status' => !!TaskCategory::query()->create($request->safe([TaskCategory::NAME]))
        ]);
    }

    /**
     * @param TaskCategory $taskCategory
     * @param StoreTaskCategoryRequest $request
     *
     * @return JsonResponse
     */
    public function updateTaskCategory(TaskCategory $taskCategory, StoreTaskCategoryRequest $request): JsonResponse
    {
        $taskCategory->name = $request->safe()->collect()->get(TaskCategory::NAME);

        return $this->formatResponse([
            'status' => $taskCategory->save()
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getDynamicPriorityTypes(): JsonResponse
    {
        return $this->formatResponse([
            'types' => DynamicPriorityTypes::getMapping()
        ]);
    }
}
