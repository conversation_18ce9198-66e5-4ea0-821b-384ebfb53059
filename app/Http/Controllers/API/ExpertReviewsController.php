<?php

namespace App\Http\Controllers\API;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Requests\Odin\StoreCompanyExpertReviewRequest;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyExpertReview;
use App\Repositories\CompanyExpertReviewsRepository;
use App\Repositories\Odin\CompanyRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ExpertReviewsController extends APIController
{

    const REQUEST_BODY  = 'body';

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param CompanyRepository $companyRepository
     * @param CompanyExpertReviewsRepository $companyExpertReviewsRepository
     */
    public function __construct(
        Request                                  $request,
        JsonAPIResponseFactory                   $apiResponseFactory,
        protected CompanyRepository              $companyRepository,
        protected CompanyExpertReviewsRepository $companyExpertReviewsRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param int $companyId
     * @return JsonResponse
     */
    public function getExpertReviewsForCompany(int $companyId): JsonResponse
    {
        $company = $this->companyRepository->findOrFail($companyId);
        return $this->formatResponse([
            'reviews' => $this->companyExpertReviewsRepository->getExpertActiveReviewsForCompany($company->id),
        ]);
    }

    /**
     * @param int $companyId
     * @param StoreCompanyExpertReviewRequest $request
     * @return JsonResponse
     */
    public function createExpertReview(int $companyId, StoreCompanyExpertReviewRequest $request): JsonResponse
    {
        $filteredRequest = $request->safe()->collect();
        $company = $this->companyRepository->findOrFail($companyId);

        return $this->formatResponse([
            'status' => $this->companyExpertReviewsRepository->createOrUpdateCompanyExpertReview(
                $company->{Company::FIELD_ID},
                $filteredRequest->get(self::REQUEST_BODY),
            ),
        ]);
    }

    /**
     * @param int $companyId
     * @param int $reviewId
     * @return JsonResponse
     */
    public function UpdateExpertReview(int $companyId, int $reviewId, StoreCompanyExpertReviewRequest $request): JsonResponse
    {
        $filteredRequest = $request->safe()->collect();
        $company = $this->companyRepository->findOrFail($companyId);
        $this->companyExpertReviewsRepository->findOrFail($reviewId)->delete();

        return $this->formatResponse([
            'status' => $this->companyExpertReviewsRepository->createOrUpdateCompanyExpertReview(
                $company->{Company::FIELD_ID},
                $filteredRequest->get(self::REQUEST_BODY),
            ),
        ]);
    }

    /**
     * @param int $companyId
     * @param int $reviewId
     * @return JsonResponse
     */
    public function DeleteExpertReview(int $companyId, int $reviewId): JsonResponse
    {
        $this->companyRepository->findOrFail($companyId);
        $expertReview = $this->companyExpertReviewsRepository->findOrFail($reviewId);
        return $this->formatResponse([
            'status' => $expertReview->delete()
        ]);
    }

    /**
     * @param int $companyId
     * @return JsonResponse
     */
    public function getExpertReviewHistoryForCompany(int $companyId): JsonResponse
    {
        $company = $this->companyRepository->findOrFail($companyId);
        return $this->formatResponse([
            'history' => $this->companyExpertReviewsRepository->getExpertReviewHistoryForCompany($company->id)
        ]);
    }

}