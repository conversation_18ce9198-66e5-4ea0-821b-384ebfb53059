<?php

namespace App\Http\Controllers\API;

use App\Enums\LeadsReport\LeadsReportColumnEnum;
use App\Enums\LeadsReport\LeadsReportGroupEnum;
use App\Enums\PermissionType;
use App\Enums\SupportedTimezones;
use App\Enums\UserPresetType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Requests\LeadsReportRequest;
use App\Http\Requests\StorePresetFilterRequest;
use App\Http\Resources\UserPresetFilterResource;
use App\Models\User;
use App\Repositories\UserPresetRepository;
use App\Services\Filterables\LeadsReport\LeadsReportFilterableService;
use App\Services\LeadsReportService;
use App\Services\UserPresetService;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Support\Facades\Auth;

class LeadsReportController extends APIController
{
    const string REQUEST_NAME               = 'name';
    const string REQUEST_VALUE              = 'value';
    const string RESPONSE_STATUS            = 'status';
    const string RESPONSE_MESSAGE           = 'message';
    const string RESPONSE_PRESETS           = 'presets';
    const string RESPONSE_GROUPS            = 'groups';
    const string RESPONSE_COLUMNS           = 'columns';
    const string RESPONSE_ORDER             = 'column_order';
    const string RESPONSE_FILTERS           = 'filters';
    const string RESPONSE_LOCATIONS         = 'leads_locations';
    const string RESPONSE_FILTER_UPDATES    = 'filter_updates';

    const string RESPONSE_REPORT            = 'report';
    const string RESPONSE_COMPARE           = 'compare';
    const string FILTER_ID                  = 'id';
    const string FILTER_OPTIONS             = 'options';

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param LeadsReportService $leadsReportService
     * @param LeadsReportFilterableService $leadsReportFilterableService
     * @param UserPresetRepository $userPresetRepository
     * @param UserPresetService $userPresetService
     */
    public function __construct(
        Request                                 $request,
        JsonAPIResponseFactory                  $apiResponseFactory,
        protected LeadsReportService            $leadsReportService,
        protected LeadsReportFilterableService  $leadsReportFilterableService,
        protected UserPresetRepository          $userPresetRepository,
        protected UserPresetService             $userPresetService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @return JsonResponse
     */
    public function getLeadsReportOptions(): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();
        $limitedLocations = null;
        $filters = $this->leadsReportFilterableService->getDisplayData();

        if (!$user->hasPermissionTo(PermissionType::LEADS_REPORT_VIEW->value)) {
            return $this->formatResponse([
                self::RESPONSE_MESSAGE  => 'User does not have leads-report/view permission.',
                self::RESPONSE_STATUS   => false,
            ]);
        }

        return $this->formatResponse([
            self::RESPONSE_GROUPS       => LeadsReportGroupEnum::getAllGroups(),
            self::RESPONSE_COLUMNS      => LeadsReportColumnEnum::getFormattedCases(),
            self::RESPONSE_ORDER        => LeadsReportColumnEnum::getColumnsOrder(),
            self::RESPONSE_FILTERS      => $filters,
            self::RESPONSE_LOCATIONS    => $limitedLocations,
            self::RESPONSE_PRESETS      => $this->getUserPresets(),
        ]);
    }

    /**
     * @param LeadsReportRequest $request
     * @return JsonResponse
     */
    public function getFilterOptionUpdates(Request $request): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();
        $filterOptions = $request->get(LeadsReportRequest::FILTERS);

        $this->leadsReportFilterableService->runQuery($filterOptions);
        $filters = $this->leadsReportFilterableService->getFilterOptionUpdates();

        if (!$user->hasPermissionTo(PermissionType::LEADS_REPORT_VIEW->value)) {
            return $this->formatResponse([
                self::RESPONSE_MESSAGE  => 'User does not have leads-report/view permission.',
                self::RESPONSE_STATUS   => false,
            ]);
        }

        return $this->formatResponse([
            self::RESPONSE_STATUS         => true,
            self::RESPONSE_FILTER_UPDATES => $filters ?? null
        ]);
    }

    /**
     * @param LeadsReportRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function getLeadsReport(LeadsReportRequest $request): JsonResponse
    {
        set_time_limit(60);

        $data = $request->safe()->all();

        /** @var User $user */
        $user = Auth::user();
        $limitedLocations = null;

        if (!$user->hasPermissionTo(PermissionType::LEADS_REPORT_VIEW->value)) {
            return $this->formatResponse([
                self::RESPONSE_MESSAGE  => 'User does not have leads-report/view permission.',
                self::RESPONSE_STATUS   => false,
            ]);
        }

        $reports = $this->leadsReportService->buildReport(
            startDate:          Carbon::parse($data[LeadsReportRequest::BASE_START], SupportedTimezones::MOUNTAIN->value)->setTimeZone(config('app.timezone')),
            endDate:            Carbon::parse($data[LeadsReportRequest::BASE_END], SupportedTimezones::MOUNTAIN->value)->setTimeZone(config('app.timezone')),
            startCompareDate:   array_key_exists(LeadsReportRequest::COMPARE_START, $data) ? Carbon::parse($data[LeadsReportRequest::COMPARE_START], SupportedTimezones::MOUNTAIN->value)->setTimeZone(config('app.timezone')) : null,
            endCompareDate:     array_key_exists(LeadsReportRequest::COMPARE_END, $data) ? Carbon::parse($data[LeadsReportRequest::COMPARE_END], SupportedTimezones::MOUNTAIN->value)->setTimeZone(config('app.timezone')) : null,
            group:              LeadsReportGroupEnum::from($data[LeadsReportRequest::GROUP]),
            filters:            $request->getFilters(),
            columns:            $request->getColumns(),
            limitedLocations:   $limitedLocations,
            companyId:          $data[LeadsReportRequest::COMPANY] ?? null,
            campaigns:          $data[LeadsReportRequest::CAMPAIGNS] ?? null,
        );

        return $this->formatResponse([
            self::RESPONSE_STATUS   => true,
            self::RESPONSE_REPORT   => $reports[LeadsReportService::BASE_REPORT],
            self::RESPONSE_COMPARE  => $reports[LeadsReportService::COMPARE_REPORT],
            self::RESPONSE_COLUMNS  => LeadsReportColumnEnum::getFormattedCases(),
            self::RESPONSE_ORDER    => LeadsReportColumnEnum::getColumnsOrder(),
        ]);
    }

    /**
     * @param StorePresetFilterRequest $request
     * @return JsonResponse
     */
    public function savePreset(StorePresetFilterRequest $request): JsonResponse
    {
        $presetValue = $request->get(self::REQUEST_VALUE);
        $name = $request->get('name', 'User Filter');

        /** @var User $user */
        $user = Auth::user();
        $success = !!$this->userPresetRepository->savePreset(
            $user->id,
            UserPresetType::FILTER,
            $name,
            $presetValue,
            LeadsReportFilterableService::FILTERABLE_CATEGORY,
        );
        return $this->formatResponse([
            self::RESPONSE_STATUS => $success,
            self::RESPONSE_PRESETS => $this->getUserPresets(),
        ]);
    }

    /**
     * @return ResourceCollection
     */
    public function getUserPresets(): ResourceCollection
    {
        /** @var User $user */
        $user = Auth::user();
        $presets = $this->userPresetRepository->getPresetsByTypeAndCategory(UserPresetType::FILTER,
            LeadsReportFilterableService::FILTERABLE_CATEGORY, $user->id);

        return UserPresetFilterResource::collection($presets);
    }

    /**
     * @return JsonResponse
     */
    public function deletePreset(): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();
        $name = $this->request->get(self::REQUEST_NAME);

        $deleted = $this->userPresetRepository->deletePreset(
            $user->id,
            UserPresetType::FILTER,
            $name,
            LeadsReportFilterableService::FILTERABLE_CATEGORY,
        );

        return $this->formatResponse([
            self::RESPONSE_STATUS => $deleted,
            self::RESPONSE_PRESETS => $this->getUserPresets(),
        ]);
    }
}
