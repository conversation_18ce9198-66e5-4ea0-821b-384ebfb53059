<?php

namespace App\Http\Controllers\API;

use App\Console\Commands\CompanyMergeTool\CompanyMergeService;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\PermissionType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Resources\Companies\CompanyMergeRecordResource;
use App\Models\CompanyMergeRecord;
use App\Models\Odin\Company;
use App\Models\Odin\IndustryService;
use App\Models\User;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\UnauthorizedException;

class CompanyMergeController extends APIController
{
    const string MODULE_PERMISSION = PermissionType::COMPANY_MERGE->value;

    const string REQUEST_DRY_RUN           = 'dry_run';
    const string REQUEST_MERGE_RECORD_ID   = 'company_merge_record_id';
    const string REQUEST_SOURCE_COMPANY_ID = 'source_company_id';

    const string RESPONSE_STATUS  = 'status';
    const string RESPONSE_MERGES  = 'merges';
    const string RESPONSE_MESSAGE = 'message';

    public function __construct(
        Request                           $request,
        JsonAPIResponseFactory            $apiResponseFactory,
        protected CompanyMergeService     $companyMergeService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @return JsonResponse
     */
    public function getPastMerges(): JsonResponse
    {
        $this->checkUserHasPermission();

        $targetCompanyId = $this->request->route('company_id');
        $pastMerges = $this->getPastMergesCollection($targetCompanyId);

        return $this->formatResponse([
            self::RESPONSE_STATUS => true,
            self::RESPONSE_MERGES => CompanyMergeRecordResource::collection($pastMerges),
        ]);
    }

    /**
     * @return JsonResponse
     * @throws Exception
     */
    public function undoMerge(): JsonResponse
    {
        $this->checkUserHasPermission();

        $targetCompanyId = (int) $this->request->route('company_id');
        $mergeRecordId = $this->request->input(self::REQUEST_MERGE_RECORD_ID, null);
        $dryRun = $this->request->get(self::REQUEST_DRY_RUN, false);

        $mergeRecord = CompanyMergeRecord::query()
            ->findOrFail($mergeRecordId);

        if (!$mergeRecord || $mergeRecord->target_company_id !== $targetCompanyId)
            throw new Exception("Invalid undo record, cannot proceed.");

        try {
            $result = $this->companyMergeService->handleUndo($mergeRecord->source_company_id, $mergeRecord->target_company_id, $dryRun);
        }
        catch(Exception $e) {
            $result = [self::RESPONSE_STATUS => false];
            $message = $e->getMessage();
        }

        return $this->formatResponse([
            ...$result,
            self::RESPONSE_MESSAGE => $message ?? null,
            self::RESPONSE_MERGES => $result && !$dryRun
                ? CompanyMergeRecordResource::collection($this->getPastMergesCollection($targetCompanyId))
                : null,
        ]);
    }

    /**
     * @throws Exception
     */
    public function newMerge(): JsonResponse
    {
        $this->checkUserHasPermission();

        $targetCompanyId = (int) $this->request->route('company_id');
        $sourceCompanyId = (int) $this->request->get(self::REQUEST_SOURCE_COMPANY_ID);
        if ($sourceCompanyId === $targetCompanyId)
            throw new Exception("Cannot merge a company into itself");
        /** @var Company $sourceCompany */
        $sourceCompany = Company::query()
            ->findOrFail($sourceCompanyId);
        /** @var Company $targetCompany */
        $targetCompany = Company::query()
            ->findOrFail($targetCompanyId);
        $dryRun = $this->request->get(self::REQUEST_DRY_RUN, false);

        $this->checkCompaniesAreEligible($sourceCompany, $targetCompany);

        try {
            $result = $this->companyMergeService->handleMerge($sourceCompanyId, $targetCompanyId, $dryRun);
        }
        catch(Exception $e) {
            $result = [self::RESPONSE_STATUS => false];
            $message = $e->getMessage();
        }

        return $this->formatResponse([
            ...$result,
            self::RESPONSE_MESSAGE => $message ?? null,
            self::RESPONSE_MERGES => $result && !$dryRun
                ? CompanyMergeRecordResource::collection($this->getPastMergesCollection($targetCompanyId))
                : null,
        ]);
    }

    protected function getPastMergesCollection(int $targetCompanyId): Collection
    {
        return CompanyMergeRecord::query()
            ->where(CompanyMergeRecord::FIELD_TARGET_COMPANY_ID, $targetCompanyId)
            ->with([
                CompanyMergeRecord::RELATION_SOURCE_COMPANY,
                CompanyMergeRecord::RELATION_TARGET_COMPANY,
            ])->get();
    }

    /**
     * @param Company $sourceCompany
     * @param Company $targetCompany
     * @return void
     * @throws Exception
     */
    private function checkCompaniesAreEligible(Company $sourceCompany, Company $targetCompany): void
    {
        $alreadyMerged = CompanyMergeRecord::query()
            ->where(CompanyMergeRecord::FIELD_SOURCE_COMPANY_ID, $sourceCompany->id)
            ->exists();

        if ($alreadyMerged)
            throw new Exception("The source company has already been merged to $targetCompany->id.");
    }

    /**
     * @return void
     */
    private function checkUserHasPermission(): void
    {
        /** @var User $user */
        $user = Auth::user();

        if (!$user->hasPermissionTo(self::MODULE_PERMISSION))
            throw new UnauthorizedException("User does not have the required permission.");
    }
}
