<?php

namespace App\Http\Controllers\API;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Resources\Odin\PermissionOptionResource;
use App\Http\Resources\Odin\RoleOptionResource;
use App\Services\Odin\Logging\LoggerService;
use App\Services\Odin\Logging\PermissionLoggerService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RolesPermissionsManagementController extends APIController
{
    const NAME = 'name';
    const PERMISSIONS = 'permissions';

    public function __construct(Request $request, JsonAPIResponseFactory $apiResponseFactory, protected PermissionLoggerService $permissionLoggerService)
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @return JsonResponse
     */
    public function getPermissions(): JsonResponse
    {
        return $this->formatResponse([
            'permissions' => Permission::all()->load('roles')
        ]);
    }

    /**
     * @return JsonResponse
     * @throws ValidationException
     */
    public function addPermission(): JsonResponse
    {
        $this->validatePermission();

        $permission = Permission::create($this->request->only(self::NAME));
        $this->permissionLoggerService->logCreatePermission($permission);

        return $this->formatResponse([
            'permission' => $permission->load('roles')
        ]);
    }

    /**
     * @param Permission $permission
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function updatePermission(Permission $permission): JsonResponse
    {
        $this->validatePermission($permission);
        $oldPermission = $permission->name;
        $permission->update($this->request->only(self::NAME));
        $newPermission = $permission->name;
        $this->permissionLoggerService->logUpdatePermission($oldPermission, $newPermission);

        return $this->formatResponse([
            'permission' => $permission->load('roles')
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getRoles(): JsonResponse
    {
        return $this->formatResponse([
            'roles' => Role::all()->load('permissions')
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getRoleOptions(): JsonResponse
    {
        return $this->formatResponse([
            'roles' => RoleOptionResource::collection(Role::query()->orderBy('name')->get())
        ]);
    }

    public function getPermissionOptions(): JsonResponse
    {
        return $this->formatResponse([
            'permissions' => PermissionOptionResource::collection(Permission::query()->orderBy('name')->get())
        ]);
    }

    /**
     * @return JsonResponse
     * @throws ValidationException
     */
    public function addRole(): JsonResponse
    {
        $this->validateRole();

        /** @var  Role $role */
        $role = Role::create($this->request->only(self::NAME));

        $this->permissionLoggerService->logCreateRole($role);

        $role->syncPermissions($this->request->get(self::PERMISSIONS));

        return $this->formatResponse([
            'role' => $role->load('permissions')
        ]);
    }

    /**
     * @param Role $role
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function updateRole(Role $role): JsonResponse
    {
        $this->validateRole($role);

        $oldRoleName = $role->name;

        $role->update($this->request->only(self::NAME));

        $newRoleName = $role->name;

        $role->syncPermissions($this->request->get(self::PERMISSIONS));

        $this->permissionLoggerService->logUpdateRoleName($oldRoleName, $newRoleName);

        return $this->formatResponse([
            'role' => $role->load('permissions')
        ]);
    }

    /**
     * @param Role $role
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function syncPermissions(Role $role): JsonResponse
    {
        $this->validateRole($role);

        $oldPermissions = $role->permissions->pluck('id')->all();

        $role->syncPermissions($this->request->get(self::PERMISSIONS));

        $newPermissions = $role->permissions->pluck('id')->all();

        $this->permissionLoggerService->logRolePermissionChange($this->request->get(self::NAME), $oldPermissions, $newPermissions);

        return $this->formatResponse([
            'role' => $role->load('permissions')
        ]);
    }

    /**
     * @throws ValidationException
     */
    protected function validatePermission(?Permission $permission = null)
    {
        $this->validate($this->request, [
            self::NAME => [
                'required',
                'string',
                'max:255',
                $permission ? Rule::unique(Permission::class, 'name')->ignore($permission->id) : Rule::unique(Permission::class, 'name')
            ]
        ]);
    }

    /**
     * @throws ValidationException
     */
    protected function validateRole(?Role $role = null)
    {
        $this->validate($this->request, [
            self::NAME => [
                'required',
                'string',
                'max:255',
                $role ? Rule::unique(Role::class, 'name')->ignore($role->id) : Rule::unique(Role::class, 'name')
            ],
            self::PERMISSIONS => 'required|array',
            self::PERMISSIONS . '.*' => 'exists:' . Permission::class . ',id'
        ]);
    }
}
