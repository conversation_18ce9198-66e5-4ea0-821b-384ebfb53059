<?php

namespace App\Http\Controllers\API\Reports;

use App\Http\Controllers\Controller;
use App\Http\Requests\Reports\RevenueStreamsReportRequest;
use App\Services\Filterables\Reports\RevenueStreamsReport\RevenueStreamsReportFilterableService;
use App\Services\Reports\RevenueStreamsReportService;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class RevenueStreamsController extends Controller
{
    /**
     * @param RevenueStreamsReportFilterableService $filterService
     * @return array
     */
    public function getReportOptions(RevenueStreamsReportFilterableService $filterService): array
    {
        return [
            'filters' => $filterService->getDisplayData(),
        ];
    }

    /**
     * @param Request $request
     * @param RevenueStreamsReportFilterableService $filterService
     * @return null[]
     */
    public function getFilterOptionUpdates(Request $request, RevenueStreamsReportFilterableService $filterService): array
    {
        $filterOptions = $request->get('filters');

        $filterService->runQuery($filterOptions);
        $filterService->getFilterOptionUpdates();

        return [
            'filters' => $filters ?? null
        ];
    }

    /**
     * @param RevenueStreamsReportRequest $request
     * @param RevenueStreamsReportService $revenueStreamsReportService
     * @return array
     */
    public function getReportData(RevenueStreamsReportRequest $request, RevenueStreamsReportService $revenueStreamsReportService): array
    {
        $filters = $request->validated();

        return [
            'data' => $revenueStreamsReportService->getRevenueStreamReportData(
                dateRange: Arr::get($filters, RevenueStreamsReportRequest::FIELD_DATE_RANGE),
                filters: $request->getFilters(),
            ),
        ];
    }
}
