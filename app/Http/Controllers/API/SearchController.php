<?php

namespace App\Http\Controllers\API;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Resources\Odin\CompanyCampaignListOptionResource;
use App\Repositories\Odin\Campaigns\CompanyCampaignRepository;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Services\Search\SearchService;
use App\Transformers\Odin\ConsumerProductSearchTransformer;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SearchController extends APIController
{
    const REQUEST_QUERY = 'query';

    public function __construct(Request $request, JsonAPIResponseFactory $apiResponseFactory, protected SearchService $service) { parent::__construct($request, $apiResponseFactory); }

    /**
     * Performs the search.
     *
     * @param string $query
     * @return JsonResponse
     */
    public function search(string $query): JsonResponse
    {
        return $this->formatResponse(
            $this->service->search($query)->toArray()
        );
    }

    /**
     * @param int $companyId
     * @param CompanyCampaignRepository $productCampaignRepository
     * @return JsonResponse
     */
    public function searchCompanyCampaigns(
        int $companyId,
        CompanyCampaignRepository $productCampaignRepository,
    ): JsonResponse
    {
        $query = $this->request->get(self::REQUEST_QUERY, '');

        $campaigns = $productCampaignRepository->searchCompanyCampaigns($companyId, $query);

        return $this->formatResponse([
            "status" => true,
            "results" => CompanyCampaignListOptionResource::collection($campaigns),
        ]);
    }

    /**
     * @param ConsumerProductRepository $consumerProductRepository
     * @param ConsumerProductSearchTransformer $consumerProductSearchTransformer
     * @return JsonResponse
     */
    public function searchConsumerProducts(ConsumerProductRepository $consumerProductRepository, ConsumerProductSearchTransformer $consumerProductSearchTransformer): JsonResponse
    {
        $query = $this->request->get(self::REQUEST_QUERY, '');

        return $this->formatResponse([
            "status" => true,
            "results" => $consumerProductSearchTransformer->transform($consumerProductRepository->searchConsumerProducts($query))
        ]);
    }
}
