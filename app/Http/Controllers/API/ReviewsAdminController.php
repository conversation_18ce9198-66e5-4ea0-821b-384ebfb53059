<?php

namespace App\Http\Controllers\API;

use App\Enums\PermissionType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Requests\ConsumerReviews\SearchCompanyConsumerReviewsRequests;
use App\Http\Requests\ConsumerReviews\StoreReviewReplyRequest;
use App\Http\Resources\Odin\ConsumerSearchResource;
use App\Http\Resources\Reviews\ReviewReplyResource;
use App\Http\Resources\Reviews\ReviewResource;
use App\Jobs\CalculateCompanyRatingsJob;
use App\Models\ConsumerReviews\Review;
use App\Models\User;
use App\Repositories\CompanyConsumerReviewRepository\CompanyConsumerReviewRepository;
use App\Repositories\CompanyRatingRepository;
use App\Services\CompanyConsumerReviews\CompanyConsumerReviewService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\UnauthorizedException;

class ReviewsAdminController extends APIController
{
    const string PERMISSION_MANAGE                 = PermissionType::CONSUMER_REVIEWS_MANAGE->value;
    const string RESPONSE_STATUS                   = 'status';
    const int    ACTIVITY_LIMIT_PER_PAGE           = 10;
    const string REQUEST_REVIEW_SCORE              = 'score';
    const string REQUEST_REVIEW_DATE               = 'date';
    const string REQUEST_REVIEW_SEARCH             = 'text';
    const string RESPONSE_COMPANY_CONSUMER_REVIEWS = 'company_consumer_reviews';
    const string RESPONSE_RELATED_REVIEWS          = 'related_reviews';
    const string RESPONSE_ASSOCIATED_CONSUMERS     = 'associated_consumers';
    const string RESPONSE_REVIEW_REPLIES           = 'review_replies';

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param CompanyConsumerReviewService $companyConsumerReviewService
     * @param CompanyConsumerReviewRepository $companyConsumerReviewRepository
     * @param CompanyRatingRepository $companyRatingRepository
     */
    public function __construct(
        Request                                 $request,
        JsonAPIResponseFactory                  $apiResponseFactory,
        public CompanyConsumerReviewService     $companyConsumerReviewService,
        public CompanyConsumerReviewRepository  $companyConsumerReviewRepository,
        public CompanyRatingRepository          $companyRatingRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param SearchCompanyConsumerReviewsRequests $request
     * @return JsonResponse
     */
    public function getReviews(SearchCompanyConsumerReviewsRequests $request): JsonResponse
    {
        $searchParams = $request->safe()->collect();
        $companyId = $request->get('company_id');

        $reviews = $this->companyConsumerReviewRepository->getAllConsumerReviews(
            companyId: $companyId,
            status: $searchParams->get(self::RESPONSE_STATUS) ?? null,
            score: $searchParams->get(self::REQUEST_REVIEW_SCORE) ?? null,
            text: $searchParams->get(self::REQUEST_REVIEW_SEARCH) ?? null,
            verified: $searchParams->get(SearchCompanyConsumerReviewsRequests::REQUEST_VERIFIED) ?? null,
        )->paginate(self::ACTIVITY_LIMIT_PER_PAGE, ['*'], 'page', $this->request->get('page'));

        return $this->formatResponse([
            self::RESPONSE_COMPANY_CONSUMER_REVIEWS => ReviewResource::collection($reviews)->resource,
        ]);
    }

    /**
     * @param Review $review
     * @return JsonResponse
     */
    public function getReviewDetails(Review $review): JsonResponse
    {
        $relatedReviews = $this->companyConsumerReviewRepository->getRelatedConsumerReviews($review->id)->get();
        $associatedConsumers = $this->companyConsumerReviewRepository->getAssociatedConsumers($review->id);

        return $this->formatResponse([
            self::RESPONSE_RELATED_REVIEWS => ReviewResource::collection($relatedReviews),
            self::RESPONSE_ASSOCIATED_CONSUMERS => ConsumerSearchResource::collection($associatedConsumers),
        ]);
    }

    /**
     * @param Review $review
     * @return JsonResponse
     */
    public function approveReview(Review $review): JsonResponse
    {
        $this->checkManagementPermission();
        $review = $this->companyConsumerReviewRepository->approveReview($review);

        CalculateCompanyRatingsJob::dispatch([$review->company_id]);

        return $this->formatResponse([
            self::RESPONSE_STATUS => (bool) $review
        ]);
    }

    /**
     * @param Review $review
     * @return JsonResponse
     */
    public function declineReview(Review $review): JsonResponse
    {
        $this->checkManagementPermission();
        $review = $this->companyConsumerReviewRepository->declineReview($review);

        return $this->formatResponse([
            self::RESPONSE_STATUS => (bool) $review
        ]);
    }

    /**
     * @param Review $review
     * @return JsonResponse
     */
    public function setPendingReview(Review $review): JsonResponse
    {
        $this->checkManagementPermission();
        $review = $this->companyConsumerReviewRepository->setPendingReview($review);

        CalculateCompanyRatingsJob::dispatch([$review->company_id]);

        return $this->formatResponse([
            self::RESPONSE_STATUS => (bool) $review
        ]);
    }

    /**
     * @param Review $review
     * @param StoreReviewReplyRequest $request
     * @return JsonResponse
     */
    public function postReviewReply(Review $review, StoreReviewReplyRequest $request): JsonResponse
    {
        $this->checkManagementPermission();
        $validated = $request->validated();
        $reply = Arr::get($validated, StoreReviewReplyRequest::FIELD_REPLY);
        $reviewReply = $this->companyConsumerReviewRepository->postAdminComment($review, $reply);

        return $this->formatResponse([
            self::RESPONSE_STATUS            => (bool) $reviewReply,
            self::RESPONSE_REVIEW_REPLIES    => ReviewReplyResource::collection($review->refresh()->reviewReplies),
        ]);
    }

    /**
     * @return void
     */
    protected function checkManagementPermission(): void
    {
        /** @var User $user */
        $user = Auth::user();
        if (!$user->hasPermissionTo(self::PERMISSION_MANAGE))
            throw new UnauthorizedException("You don't have permission to perform this action.");
    }
}
