<?php

namespace App\Http\Controllers\API\Affiliates\Internal;

use App\Builders\Affiliates\AffiliateReportBuilder;
use App\Enums\Affiliate\PayoutStrategyTypeEnum;
use App\Enums\Affiliate\ReportGrouping;
use App\Enums\PermissionType;
use App\Exceptions\CustomValidationException;
use App\Factories\JsonAPIResponseFactory;
use App\Helpers\CarbonHelper;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Affiliate\BaseAffiliateRequest;
use App\Http\Requests\Affiliate\CreateAffiliateRequest;
use App\Http\Requests\Affiliate\GetLeadDetailsRequest;
use App\Http\Requests\Affiliate\ListAffiliatesRequest;
use App\Http\Requests\Affiliate\UpdateAffiliateRequest;
use App\Http\Resources\Affiliates\AffiliateReportResource;
use App\Http\Resources\Affiliates\AffiliateResource;
use App\Http\Resources\Affiliates\ConsumerProductResource;
use App\Models\Affiliates\Affiliate;
use App\Models\Odin\Industry;
use App\Models\User;
use App\Services\Affiliate\AffiliateLoginTokenService;
use App\Services\Affiliate\AffiliateService;
use App\Services\Affiliate\ConsumerProductAffiliateService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class AffiliateController extends APIController
{
    const int DEFAULT_SHADOW_EXPIRY = 10;
    public function __construct(
        Request                                   $request,
        JsonAPIResponseFactory                    $apiResponseFactory,
        protected ConsumerProductAffiliateService $consumerProductAffiliateService,
        protected AffiliateLoginTokenService      $loginTokenService,
        protected AffiliateService                $service,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param ListAffiliatesRequest $request
     * @return array
     * @throws Exception
     */
    public function index(ListAffiliatesRequest $request): array
    {
        $validated = $request->validated();

        $dateRange = Arr::get($validated, ListAffiliatesRequest::REQUEST_DATE_RANGE);

        $data = AffiliateReportBuilder::query()
            ->fromDate(CarbonHelper::toTimezone(Arr::get($dateRange, ListAffiliatesRequest::REQUEST_FROM)))
            ->toDate(CarbonHelper::toTimezone(Arr::get($dateRange, ListAffiliatesRequest::REQUEST_TO)))
            ->name(Arr::get($validated, ListAffiliatesRequest::REQUEST_NAME))
            ->industries(Arr::get($validated, ListAffiliatesRequest::REQUEST_INDUSTRIES))
            ->grouping(ReportGrouping::AFFILIATE)
            ->sorting(Arr::get($validated, ListAffiliatesRequest::REQUEST_SORT_BY, []))
            ->getQuery();

        return AffiliateReportResource::format($data);
    }

    /**
     * @param CreateAffiliateRequest $request
     * @return JsonResponse
     */
    public function store(CreateAffiliateRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $strategy = Arr::get($validated, BaseAffiliateRequest::REQUEST_PAYOUT_STRATEGY);

        $authorId = Auth::user()?->id;

        $this->service->initialiseAffiliate(
            name: Arr::get($validated, CreateAffiliateRequest::REQUEST_NAME),
            type: PayoutStrategyTypeEnum::tryFrom(Arr::get($strategy, BaseAffiliateRequest::STRATEGY_TYPE)),
            value:Arr::get($strategy, BaseAffiliateRequest::STRATEGY_VALUE),
            authorId: $authorId,
        );

        return $this->formatResponse([], 201);
    }

    /**
     * @param Affiliate $affiliate
     * @param UpdateAffiliateRequest $request
     * @return JsonResponse
     */
    public function update(Affiliate $affiliate, UpdateAffiliateRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $strategy = Arr::get($validated, BaseAffiliateRequest::REQUEST_PAYOUT_STRATEGY);

        $this->service->updateAffiliate(
            affiliate: $affiliate,
            name: Arr::get($validated, UpdateAffiliateRequest::REQUEST_NAME, $affiliate->name),
            type: PayoutStrategyTypeEnum::tryFrom(Arr::get($strategy, BaseAffiliateRequest::STRATEGY_TYPE, $affiliate->strategy->type->value)),
            value: Arr::get($strategy, BaseAffiliateRequest::STRATEGY_VALUE, $affiliate->strategy->value),
        );

        return $this->formatResponse([], 204);
    }

    /**
     * @param Affiliate $affiliate
     * @return JsonResponse
     * @throws CustomValidationException
     */
    public function destroy(Affiliate $affiliate): JsonResponse
    {
        /** @var User|null $user */
        $user = Auth::user();

        if (!$user->hasPermissionTo(PermissionType::AFFILIATES_DELETE->value)) {
            throw new CustomValidationException('User does not have permission to delete affiliate');
        }

        DB::transaction(function () use ($affiliate) {
            $affiliate->campaigns()->delete();
            $affiliate->categories()->delete();
            $affiliate->delete();
        });

        return $this->formatResponse([], 204);
    }

    /**
     * @param Affiliate $affiliate
     * @param GetLeadDetailsRequest $request
     * @return ResourceCollection
     */
    public function getLeadDetails(
        Affiliate             $affiliate,
        GetLeadDetailsRequest $request
    ): ResourceCollection
    {
        $validated = $request->validated();

        $dateRange = Arr::get($validated, GetLeadDetailsRequest::REQUEST_DATE_RANGE);
        $consumerName = Arr::get($validated, GetLeadDetailsRequest::CONSUMER_NAME);

        $consumerProducts = $this->consumerProductAffiliateService->getConsumerProductAffiliateQuery(
            affiliateId: $affiliate->id,
            campaignId: Arr::get($validated, GetLeadDetailsRequest::CAMPAIGN_ID),
            timestampFrom: $dateRange ? CarbonHelper::toTimezone(Arr::get($dateRange, GetLeadDetailsRequest::REQUEST_FROM))->startOfDay()->timestamp : null,
            timestampTo: $dateRange ? CarbonHelper::toTimezone(Arr::get($dateRange, GetLeadDetailsRequest::REQUEST_TO))->endOfDay()->timestamp : null,
            consumerName: $consumerName,
        )->paginate(Arr::get($validated, GetLeadDetailsRequest::PER_PAGE));

        return ConsumerProductResource::collection($consumerProducts);
    }

    /**
     * @param Affiliate $affiliate
     * @return JsonResponse
     * @throws CustomValidationException
     */
    public function getShadowUrl(
        Affiliate $affiliate,
    ): JsonResponse
    {
        /** @var User|null $shadow */
        $shadow = Auth::user();

        if (!$shadow->hasPermissionTo(PermissionType::AFFILIATES_SHADOW->value)) {
            throw new CustomValidationException('User does not have permission to shadow affiliate');
        }

        $token = $this->loginTokenService->generateToken($affiliate, $shadow, self::DEFAULT_SHADOW_EXPIRY);

        $url = Str::of(config('services.affiliates_portal_api.url'))
            ->before('/api')
            ->append('/shadow?token='.$token);

        return $this->formatResponse([
            'url' => $url
        ]);
    }

    /**
     * @param Affiliate $affiliate
     * @return AffiliateResource
     */
    public function show(
        Affiliate $affiliate,
    ): AffiliateResource
    {
        return new AffiliateResource($affiliate);
    }

    /**
     * @return JsonResponse
     */
    public function filters(): JsonResponse
    {
        return $this->formatResponse([
            'industries' => Industry::query()->select('id', 'name')->get(),
        ]);
    }
}
