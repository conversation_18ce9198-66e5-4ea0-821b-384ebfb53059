<?php

namespace App\Http\Controllers\API\Affiliates\Internal;

use App\Builders\Affiliates\AffiliateReportBuilder;
use App\Enums\Affiliate\ReportGrouping;
use App\Factories\JsonAPIResponseFactory;
use App\Helpers\CarbonHelper;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Advertising\UpdateCampaignRequest;
use App\Http\Requests\Affiliate\ListAffiliatesRequest;
use App\Http\Requests\Affiliate\UpdateAffiliateCampaignRequest;
use App\Http\Requests\Affiliate\UpdateAffiliateRequest;
use App\Http\Resources\Affiliates\CampaignReportResource;
use App\Http\Resources\Affiliates\CampaignResource;
use App\Models\Affiliates\Affiliate;
use App\Models\Affiliates\Campaign;
use App\Repositories\Affiliate\CampaignRepository;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class CampaignController extends APIController
{
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected CampaignRepository $campaignRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param Affiliate $affiliate
     * @param ListAffiliatesRequest $request
     * @return array
     * @throws Exception
     */
    public function index(Affiliate $affiliate, ListAffiliatesRequest $request): array
    {
        $validated = $request->validated();

        $dateRange = Arr::get($validated, ListAffiliatesRequest::REQUEST_DATE_RANGE);

        $data = AffiliateReportBuilder::query()
            ->affiliateIds([$affiliate->id])
            ->fromDate(CarbonHelper::toTimezone(Arr::get($dateRange, ListAffiliatesRequest::REQUEST_FROM)))
            ->toDate(CarbonHelper::toTimezone(Arr::get($dateRange, ListAffiliatesRequest::REQUEST_TO)))
            ->name(Arr::get($validated, ListAffiliatesRequest::REQUEST_NAME))
            ->industries(Arr::get($validated, ListAffiliatesRequest::REQUEST_INDUSTRIES))
            ->grouping(ReportGrouping::CAMPAIGN)
            ->sorting(Arr::get($validated, ListAffiliatesRequest::REQUEST_SORT_BY, []))
            ->getQuery();

        return CampaignReportResource::format($data);
    }

    /**
     * @param Affiliate $affiliate
     * @param Campaign $campaign
     * @return CampaignResource
     */
    public function show(Affiliate $affiliate, Campaign $campaign): CampaignResource
    {
        return new CampaignResource($campaign);
    }

    /**
     * @param Affiliate $affiliate
     * @param Campaign $campaign
     * @param UpdateAffiliateCampaignRequest $request
     * @return JsonResponse
     */
    public function update(
        Affiliate $affiliate,
        Campaign $campaign,
        UpdateAffiliateCampaignRequest $request
    ): JsonResponse
    {
        $validated = $request->validated();

        $this->campaignRepository->update(
            campaign: $campaign,
            name: Arr::get($validated, UpdateAffiliateCampaignRequest::REQUEST_NAME),
            accountId: $request->has(UpdateAffiliateCampaignRequest::REQUEST_ACCOUNT_ID)
                ? Arr::get($validated, UpdateAffiliateCampaignRequest::REQUEST_ACCOUNT_ID)
                : $campaign->account_id,
            campaignId: $request->has(UpdateAffiliateCampaignRequest::REQUEST_CAMPAIGN_ID)
                ? Arr::get($validated, UpdateAffiliateCampaignRequest::REQUEST_CAMPAIGN_ID)
                : $campaign->campaign_id,
        );

        return $this->formatResponse([], 204);
    }
}
