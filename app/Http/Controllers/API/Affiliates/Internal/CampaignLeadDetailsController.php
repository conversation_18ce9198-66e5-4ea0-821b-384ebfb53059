<?php

namespace App\Http\Controllers\API\Affiliates\Internal;

use App\Factories\JsonAPIResponseFactory;
use App\Helpers\CarbonHelper;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Affiliate\GetLeadDetailsRequest;
use App\Http\Resources\Affiliates\ConsumerProductResource;
use App\Models\Affiliates\Affiliate;
use App\Models\Affiliates\Campaign;
use App\Services\Affiliate\ConsumerProductAffiliateService;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class CampaignLeadDetailsController extends APIController
{

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected ConsumerProductAffiliateService $affiliateService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param Affiliate $affiliate
     * @param Campaign $campaign
     * @param GetLeadDetailsRequest $request
     * @return array
     */
    public function index(
        Affiliate $affiliate,
        Campaign $campaign,
        GetLeadDetailsRequest $request,

    ): array
    {
        $validated = $request->validated();

        $dateRange = Arr::get($validated, GetLeadDetailsRequest::REQUEST_DATE_RANGE);

        $consumerProducts = $this
            ->affiliateService
            ->getConsumerProductAffiliateQuery(
                affiliateId: $affiliate->id,
                campaignId: $campaign->id,
                timestampFrom: $dateRange ? CarbonHelper::toTimezone(Arr::get($dateRange, GetLeadDetailsRequest::REQUEST_FROM))->startOfDay()->timestamp : null,
                timestampTo: $dateRange ? CarbonHelper::toTimezone(Arr::get($dateRange, GetLeadDetailsRequest::REQUEST_TO))->endOfDay()->timestamp : null,
            );

        return ConsumerProductResource::format($consumerProducts);
    }
}
