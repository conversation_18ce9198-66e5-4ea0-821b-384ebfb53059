<?php

namespace App\Http\Controllers\API\Affiliates\Internal;

use App\Actions\AffiliatesPortal\GetStatisticsAction;
use App\Http\Controllers\API\APIController;
use App\Http\Resources\Affiliates\StatisticsResource;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class StatisticsController extends APIController
{
    /**
     * @param GetStatisticsAction $action
     * @return JsonResponse
     * @throws Exception
     */
    public function __invoke(GetStatisticsAction $action): JsonResponse
    {
        extract($this->validate(request(), [
            'start' => ['required', 'integer', 'min:0'],
            'end' => ['required', 'integer', 'gt:start'],
            'ids' => ['required', 'array'],
            'type' => ['required', 'string', Rule::in(['affiliates', 'campaigns'])]
        ]));

        $statistics = $action->handle($start, $end, $ids, $type);

        return $this->formatResponse(StatisticsResource::collection($statistics)->resolve());
    }
}
