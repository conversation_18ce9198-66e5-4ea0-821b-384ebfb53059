<?php

namespace App\Http\Controllers\API\Affiliates;

use App\Enums\Affiliate\PayoutStrategyTypeEnum;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Resources\Affiliates\PayoutStrategyEventResource;
use App\Http\Resources\Affiliates\PayoutStrategyListOptionResource;
use App\Models\Affiliates\Affiliate;
use App\Models\Affiliates\PayoutStrategy;
use App\Services\Affiliate\PayoutStrategyService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class PayoutStrategyController extends ApiController
{
    public function __construct(
        Request                         $request,
        JsonAPIResponseFactory          $apiResponseFactory,
        protected PayoutStrategyService $payoutStrategyService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    public function types(): AnonymousResourceCollection
    {
        return PayoutStrategyListOptionResource::collection(
            PayoutStrategyTypeEnum::cases()
        );
    }

    /**
     * @param Affiliate $affiliate
     * @return AnonymousResourceCollection
     */
    public function getStrategyTimeline(Affiliate $affiliate): AnonymousResourceCollection
    {
        $strategies = $affiliate->strategies()
            ->with(PayoutStrategy::RELATION_AUTHOR)
            ->orderByDesc(PayoutStrategy::FIELD_ID)
            ->get();

        return PayoutStrategyEventResource::collection($strategies);
    }

}
