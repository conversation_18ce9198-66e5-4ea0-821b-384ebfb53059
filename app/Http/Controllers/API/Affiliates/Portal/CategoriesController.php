<?php

namespace App\Http\Controllers\API\Affiliates\Portal;

use App\Http\Controllers\API\APIController;
use App\Http\Resources\Affiliates\CategoryCollection;
use App\Http\Resources\Affiliates\CategoryResource;
use App\Models\Affiliates\Affiliate;
use App\Models\Affiliates\Category;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Validation\ValidationException;

class CategoriesController extends APIController
{
    const string REQUEST_NAME = 'name';

    /**
     * @param Affiliate $affiliate
     * @return ResourceCollection
     */
    public function index(Affiliate $affiliate): ResourceCollection
    {
        return new CategoryCollection($affiliate->{Affiliate::RELATION_CATEGORIES});
    }

    /**
     * @param Affiliate $affiliate
     * @param Category $category
     * @return JsonResource
     */
    public function show(Affiliate $affiliate, Category $category): JsonResource
    {
        return new CategoryResource($category);
    }

    /**
     * @param Affiliate $affiliate
     * @return JsonResponse
     * @throws ValidationException
     */
    public function store(Affiliate $affiliate): JsonResponse
    {
        $data = $this->validate(
            $this->request,
            [
                self::REQUEST_NAME => ['required', 'string'],
            ],
            [
                self::REQUEST_NAME => "Invalid name"
            ]
        );

        return $this->formatResponse(['id' => $affiliate->{Affiliate::RELATION_CATEGORIES}()->create($data)->id], 201);
    }

    /**
     * @param Affiliate $affiliate
     * @param Category $category
     * @return JsonResponse
     * @throws ValidationException
     */
    public function update(Affiliate $affiliate, Category $category): JsonResponse
    {
        $data = $this->validate(
            $this->request,
            [
                self::REQUEST_NAME => ['required', 'string'],
            ],
            [
                self::REQUEST_NAME => "Invalid name"
            ]
        );

        $category
            ->update([
                Category::FIELD_NAME => $data[self::REQUEST_NAME]
            ]);

        return $this->formatResponse([], 204);
    }

    /**
     * @param Affiliate $affiliate
     * @param Category $category
     * @return JsonResponse
     * @throws Exception
     */
    public function destroy(Affiliate $affiliate, Category $category): JsonResponse
    {
        $category->delete();

        return $this->formatResponse([], 204);
    }
}
