<?php

namespace App\Http\Controllers\API\Affiliates\Portal;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Resources\Affiliates\ConsumerProductResource;
use App\Models\Affiliates\Affiliate;
use App\Models\Affiliates\Campaign;
use App\Services\Affiliate\ConsumerProductAffiliateService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Validation\ValidationException;

class CampaignLeadDetailsController extends APIController
{

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected ConsumerProductAffiliateService $affiliateService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param Affiliate $affiliate
     * @param Campaign $campaign
     * @return ResourceCollection
     * @throws ValidationException
     */
    public function index(Affiliate $affiliate, Campaign $campaign): ResourceCollection
    {
        extract($this->validate(request(), [
            'page' => ['required', 'integer', 'min:1'],
            'perPage' => ['required', 'integer', 'min:10'],
            'start' => ['required', 'integer', 'min:0'],
            'end' => ['required', 'integer', 'gt:start'],
        ]));

        $consumerProducts = $this
            ->affiliateService
            ->getConsumerProductAffiliateQuery(
                affiliateId: $affiliate->id,
                campaignId: $campaign->id,
                timestampFrom: $start,
                timestampTo: $end,
            )
            ->paginate($perPage);

        return ConsumerProductResource::collection($consumerProducts);
    }
}
