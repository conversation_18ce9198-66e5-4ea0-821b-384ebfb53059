<?php

namespace App\Http\Controllers\API\Affiliates\Portal;

use App\Actions\AffiliatesPortal\GetStatisticsAction;
use App\Http\Controllers\API\APIController;
use App\Http\Resources\Affiliates\CampaignResource;
use App\Http\Resources\Affiliates\StatisticsResource;
use App\Models\Affiliates\Affiliate;
use App\Models\Affiliates\Campaign;
use App\Models\Affiliates\Category;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class CampaignController extends APIController
{
    const string REQUEST_NAME = 'name';
    const string REQUEST_STATUS = 'status';
    const string REQUEST_CATEGORY_ID = 'category_id';

    /**
     * @param Affiliate $affiliate
     * @param GetStatisticsAction $action
     * @return JsonResponse
     * @throws ValidationException
     */
    public function index(Affiliate $affiliate, GetStatisticsAction $action): JsonResponse
    {
        extract($this->validate(request(), [
            'page' => ['required', 'integer', 'min:1'],
            'perPage' => ['required', 'integer', 'min:10'],
            'start' => ['required', 'integer', 'min:0'],
            'end' => ['required', 'integer', 'gt:start'],
            'searchName' => ['present', 'nullable', 'string']
        ]));

        $campaigns = $affiliate
            ->{Affiliate::RELATION_CAMPAIGNS}()
            ->when(!empty($searchName), fn ($query) => $query->where(Campaign::FIELD_NAME, 'like', "%{$searchName}%"))
            ->select(['id', 'name', 'category_id', 'status'])
            ->paginate($perPage);

        $statistics = $action->handle($start, $end, $campaigns->getCollection()->pluck('id')->toArray(), 'campaigns');

        return $this->formatResponse([
            "campaigns" => $campaigns,
            "statistics" => StatisticsResource::collection($statistics),
        ]);
    }

    /**
     * @param Affiliate $affiliate
     * @return JsonResponse
     * @throws ValidationException
     */
    public function store(Affiliate $affiliate): JsonResponse
    {
        $data = $this->validate(
            $this->request,
            [
                self::REQUEST_NAME => ['required', 'string'],
                self::REQUEST_STATUS => ['required', 'boolean'],
                self::REQUEST_CATEGORY_ID => ['required', 'integer', 'exists:'.Category::TABLE.','.Category::FIELD_ID],
            ],
            [
                self::REQUEST_NAME => "Invalid name",
                self::REQUEST_STATUS => "Invalid status",
                self::REQUEST_CATEGORY_ID => "Invalid category"
            ]
        );

        $id = Campaign::query()->create([
            Campaign::FIELD_AFFILIATE_ID => $affiliate->id,
            Campaign::FIELD_NAME => $data[self::REQUEST_NAME],
            Campaign::FIELD_STATUS => $data[self::REQUEST_STATUS],
            Campaign::FIELD_CATEGORY_ID => $data[self::REQUEST_CATEGORY_ID]
        ])->id;

        return $this->formatResponse(['id' => $id], 201);
    }

    /**
     * @param Affiliate $affiliate
     * @param Campaign $campaign
     * @return CampaignResource
     */
    public function show(Affiliate $affiliate, Campaign $campaign): CampaignResource
    {
        return new CampaignResource($campaign);
    }

    /**
     * @param Affiliate $affiliate
     * @param Campaign $campaign
     * @return JsonResponse
     * @throws ValidationException
     */
    public function update(Affiliate $affiliate, Campaign $campaign): JsonResponse
    {
        $data = $this->validate(
            $this->request,
            [
                self::REQUEST_NAME => ['required', 'string'],
                self::REQUEST_STATUS => ['required', 'boolean'],
                self::REQUEST_CATEGORY_ID => ['required', 'integer', 'exists:'.Category::TABLE.','.Category::FIELD_ID],
            ],
            [
                self::REQUEST_NAME => "Invalid name",
                self::REQUEST_STATUS => "Invalid status",
                self::REQUEST_CATEGORY_ID => "Invalid category"
            ]
        );

        $campaign
            ->update([
                Campaign::FIELD_AFFILIATE_ID => $affiliate->id,
                Campaign::FIELD_NAME => $data[self::REQUEST_NAME],
                Campaign::FIELD_STATUS => $data[self::REQUEST_STATUS],
                Campaign::FIELD_CATEGORY_ID => $data[self::REQUEST_CATEGORY_ID]
            ]);

        return $this->formatResponse([], 204);
    }

    /**
     * @param Affiliate $affiliate
     * @param Campaign $campaign
     * @return JsonResponse
     * @throws Exception
     */
    public function destroy(Affiliate $affiliate, Campaign $campaign): JsonResponse
    {
        $campaign->delete();

        return $this->formatResponse([], 204);
    }
}
