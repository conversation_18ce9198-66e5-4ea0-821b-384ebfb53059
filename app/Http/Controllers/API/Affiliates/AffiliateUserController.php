<?php

namespace App\Http\Controllers\API\Affiliates;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Resources\Affiliates\AffiliateUserResource;
use App\Models\Affiliates\Affiliate;
use App\Utilities\AffiliatesPortal\ApiClient;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class AffiliateUserController extends APIController
{
    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param ApiClient $client
     */
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        private readonly ApiClient $client
    ) {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param Affiliate $affiliate
     * @return ResourceCollection
     * @throws ConnectionException
     */
    public function index(Affiliate $affiliate): ResourceCollection
    {
        return AffiliateUserResource::collection($this->client->getUsers($affiliate->uuid));
    }

    /**
     * @param Affiliate $affiliate
     * @return JsonResponse
     * @throws ConnectionException
     */
    public function store(Affiliate $affiliate): JsonResponse
    {
        $data = array_merge(
            request()->only([
                'name',
                'email',
                'view_lead_details'
            ]),
            [
                'affiliate_id' => $affiliate->uuid
            ]
        );

        return $this->formatResponse(['id' => $this->client->createUser($data)], 201);
    }

    /**
     * @param Affiliate $affiliate
     * @param int $user
     * @return JsonResponse
     * @throws ConnectionException
     */
    public function update(Affiliate $affiliate, int $user): JsonResponse
    {
        $this->client->updateUser($user, request()->only(['name', 'email', 'view_lead_details']));

        return $this->formatResponse([], 204);
    }

    /**
     * @param Affiliate $affiliate
     * @param int $user
     * @return JsonResponse
     * @throws ConnectionException
     */
    public function destroy(Affiliate $affiliate, int $user): JsonResponse
    {
        $this->client->deleteUser($user);

        return $this->formatResponse([], 204);
    }
}
