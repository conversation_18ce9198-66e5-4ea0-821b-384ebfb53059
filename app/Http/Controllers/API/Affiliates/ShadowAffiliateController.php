<?php

namespace App\Http\Controllers\API\Affiliates;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Dashboard\LoginWithTokenRequest;
use App\Services\Affiliate\AffiliateLoginTokenService;
use Carbon\Carbon;
use Exception;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Throwable;

class ShadowAffiliateController extends APIController
{
    const string JWT_ALGORITHM            = 'HS256';

    public function __construct(
        Request                              $request,
        JsonAPIResponseFactory               $apiResponseFactory,
        protected AffiliateLoginTokenService $loginTokenService,

    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @throws Exception|Throwable
     */
    public function __invoke(LoginWithTokenRequest $request): JsonResponse
    {
        $signingKey = new Key(config('dashboard.jwt.signing_key'), self::JWT_ALGORITHM);

        $decoded = (array)JWT::decode($request->validated()[LoginWithTokenRequest::FIELD_TOKEN],$signingKey);

        $expiryTimestamp = Arr::get($decoded, 'expiry');

        throw_if(
            empty($expiryTimestamp) || now()->gt(Carbon::parse($expiryTimestamp)),
            new Exception("Login token is expired, please login with email and password.")
        );

        return $this->formatResponse($decoded);
    }

}
