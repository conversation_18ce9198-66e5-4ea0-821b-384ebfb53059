<?php

namespace App\Http\Controllers\API\Territory;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Territory\GetCompanyCustomerSuccessManagerRequest;
use App\Http\Resources\Territory\CustomerSuccessManagerResource;
use App\Services\Territory\CustomerSuccessManagerService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class CustomerSuccessManagerController extends APIController
{
    const string STATUS                             = 'status';
    const string REQUEST_COMPANY_ID                 = 'company_id';
    const string RESPONSE_CUSTOMER_SUCCESS_MANAGERS = 'customer_success_managers';


    public function __construct(
        Request                                 $request,
        JsonAPIResponseFactory                  $apiResponseFactory,
        protected CustomerSuccessManagerService $customerSuccessManagerService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }


    /**
     * @param GetCompanyCustomerSuccessManagerRequest $request
     * @return JsonResponse
     */
    public function getCompanyCustomerSuccessManagers(GetCompanyCustomerSuccessManagerRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $customerSuccessManagers = $this->customerSuccessManagerService->getCompanyCustomerSuccessManagers(
            companyId: Arr::get($validated, GetCompanyCustomerSuccessManagerRequest::REQUEST_COMPANY_ID)
        );

        return $this->formatResponse([
            self::RESPONSE_CUSTOMER_SUCCESS_MANAGERS => CustomerSuccessManagerResource::collection($customerSuccessManagers)
        ]);
    }

}
