<?php

namespace App\Http\Controllers\API;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\Webhooks\Twilio\TwilioWebhookController;
use App\Http\Requests\Odin\GetTestLeadsRequest;
use App\Http\Requests\PhoneFraudScoreRequest;
use App\Http\Resources\TestProduct\TestProductResourceCollection;
use App\Http\Resources\TestProductCommunicationResource;
use App\Repositories\Odin\TestLeadApiRepository;
use App\Services\Communication\TwilioCommunicationService;
use App\Services\TestLead\TestLeadService;
use App\Services\TestProducts\Communication\TestProductCommunicationService;
use GPBMetadata\Google\Api\Auth;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class TestLeadApiController extends APIController
{
    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param TestLeadApiRepository $testLeadApiRepository
     * @param TestLeadService $testLeadService
     */
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected TestLeadApiRepository $testLeadApiRepository,
        protected TestLeadService $testLeadService,
        protected TestProductCommunicationService $testProductCommunicationService
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param GetTestLeadsRequest $request
     * @return TestProductResourceCollection
     */
    public function getTestLeads(GetTestLeadsRequest $request): TestProductResourceCollection
    {
        $validated = $request->validated();

        return new TestProductResourceCollection(
            $this->testLeadService->getTestProducts($validated)
        );
    }

    /**
     * @param int $testLeadId
     * @return JsonResponse
     */
    public function getTestLeadCommunications(int $testLeadId): JsonResponse
    {
        $testProduct = $this->testLeadApiRepository->getTestLead($testLeadId);

        if (!isset($testProduct)) {
            return $this->formatResponse(['status' => false, 'message' => 'Test Lead could not be found.']);
        }

        $resource = new TestProductCommunicationResource(collect([
            TestProductCommunicationResource::FIELD_EVENTS         => $this->testLeadApiRepository->getTestLeadEvents($testProduct),
            TestProductCommunicationResource::FIELD_COMMUNICATIONS => $this->testLeadApiRepository->getTestLeadCommunications($testProduct)
        ]));

        return $this->formatResponse([
            "status"            => true,
            "communications"    => $resource->toArray(request())
        ]);
    }

    /**
     * @param int $testLeadId
     * @return JsonResponse
     */
    public function exportTestLeadCommunications(int $testLeadId): JsonResponse
    {
        $testProduct = $this->testLeadApiRepository->getTestLead($testLeadId);

        if (!isset($testProduct)) {
            return $this->formatResponse(['status' => false, 'message' => 'Test Lead could not be found.']);
        }

        $resource = new TestProductCommunicationResource(collect([
            TestProductCommunicationResource::FIELD_EVENTS         => $this->testLeadApiRepository->getTestLeadEvents($testProduct),
            TestProductCommunicationResource::FIELD_COMMUNICATIONS => $this->testLeadApiRepository->getTestLeadCommunications($testProduct)
        ]));

        $communications = $resource->toArray(request());

        if (empty($communications)) {
            return $this->formatResponse(['status' => false, 'message' => 'No communications found for test lead.']);
        }

        // File name contains test lead id, dates of first and last communications
        $startDate = reset($communications)[TestProductCommunicationResource::FIELD_FILE_DATE];
        $endDate = end($communications)[TestProductCommunicationResource::FIELD_FILE_DATE];
        $csvFileName = "test-lead-{$testLeadId}_{$startDate}_{$endDate}.csv";

        // Column names
        $csvContent = "leadID,Date/Time,Event,Content\n";

        // Fill data for each communication
        foreach ($communications as $communication) {
            $csvContent .= "$testLeadId,";
            $csvContent .= $communication[TestProductCommunicationResource::FIELD_DISPLAY_DATE].",";
            $csvContent .= $communication[TestProductCommunicationResource::FIELD_EVENT].",";
            $content = $communication[TestProductCommunicationResource::FIELD_CONTENT];
            switch ($communication[TestProductCommunicationResource::FIELD_TYPE]) {
                case 'call':
                    if ($content[TwilioWebhookController::REQUEST_CALL_STATUS] === 'completed') {
                        $csvContent .= $content[TwilioWebhookController::REQUEST_RECORDING_DURATION]." second call at ";
                        $csvContent .= $communication[TestProductCommunicationResource::FIELD_DISPLAY_DATE];
                    } else {
                        $csvContent .= "Missed call at ".$communication[TestProductCommunicationResource::FIELD_DISPLAY_DATE];
                    }
                    break;
                case 'email':
                    // Add new line characters to html line break tags, then strip html tags and clean string for csv format
                    $csvContent .= $this->csvString(strip_tags(preg_replace('/<(br)[^>]*>/', "$0\n", $content[TwilioCommunicationService::REQUEST_BODY])));
                    break;
                case 'sms':
                    $csvContent .= $this->csvString($content[TwilioCommunicationService::REQUEST_BODY]);
                    break;
            }
            $csvContent .= "\n";
        }

        return $this->formatResponse([
            'success'       => true,
            'csvFileName'   => $csvFileName,
            'csvContent'    => $csvContent,
        ]);
    }

    /**
     * @return array
     */
    public function getTestLeadTableFilters(): array
    {
        return $this->testLeadApiRepository->getTestLeadTableFilters();
    }


    /**
     * @param PhoneFraudScoreRequest $request
     * @return JsonResponse
     */
    public function getPhoneFraudScore(PhoneFraudScoreRequest $request): JsonResponse
    {
        $requestedData = $request->safe()->collect();
        $fraud_score   = $this->testProductCommunicationService->getFraudScore($requestedData->get(TestProductCommunicationResource::PHONE));
        $this->testLeadApiRepository->updateFraudScore($requestedData->get(TestProductCommunicationResource::COMMUNICATION_ID), $fraud_score);

        return $this->formatResponse([
            'status'      => true,
            'fraud_score' => $fraud_score
        ]);
    }

    /**
     * @param string $input
     * @param string $delimiter
     * @param string $enclosure
     * @return string
     */
    protected function csvString(string $input, string $delimiter = ',', string $enclosure = '"') : string {
        // Escape any double quotes inside the string by doubling them
        $escapedInput = str_replace($enclosure, $enclosure . $enclosure, $input);

        // Enclose the string in double quotes if it contains the delimiter, enclosure, or newline characters
        if (str_contains($escapedInput, $delimiter) ||
            str_contains($escapedInput, $enclosure) ||
            str_contains($escapedInput, "\n")       ||
            str_contains($escapedInput, "\r")) {
            $escapedInput = $enclosure . $escapedInput . $enclosure;
        }

        return $escapedInput;
    }
}
