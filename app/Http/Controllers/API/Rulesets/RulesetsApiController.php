<?php

namespace App\Http\Controllers\API\Rulesets;

use App\Enums\PermissionType;
use App\Factories\JsonAPIResponseFactory;
use App\Factories\RulesetFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Rulesets\GetAllRulesetRequest;
use App\Http\Requests\Rulesets\StoreRulesetRequest;
use App\Http\Resources\Odin\RulesetResource;
use App\Models\Ruleset;
use App\Repositories\Rulesets\RulesetsRepository;
use App\Transformers\RulesetTransformer;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class RulesetsApiController extends APIController
{
    const REQUEST_NAME    = 'name';
    const REQUEST_FILTER  = 'filter';
    const REQUEST_SOURCE  = 'source';
    const REQUEST_TYPE    = 'type';
    const REQUEST_RULESET = 'ruleset';
    const REQUEST_RULES = 'rules';

    const RESPONSE_KEY_STATUS    = 'status';
    const RESPONSE_KEY_RULESETS  = 'rulesets';
    const RESPONSE_KEY_TEMPLATE  = 'template';

    /**
     * @param Request                $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param RulesetsRepository     $rulesetRepository
     */
    public function __construct(
        Request                                $request,
        JsonAPIResponseFactory                 $apiResponseFactory,
        protected readonly RulesetsRepository  $rulesetRepository,
    )
    { parent::__construct($request, $apiResponseFactory); }

    /**
     * Handles fetching a list of all rulesets.
     *
     * @param GetAllRulesetRequest $request
     * @param RulesetTransformer $transformer
     * @return JsonResponse
     */
    public function index(GetAllRulesetRequest $request): JsonResponse
    {
        $this->performAuthorizationCheck(PermissionType::RULESET_MANAGEMENT->value);

        $rulesets = $this->rulesetRepository->getAll($request->validated());

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS    => true,
            self::RESPONSE_KEY_RULESETS  => RulesetResource::collection($rulesets),
        ]);
    }

    /**
     * Adds a new ruleset in the system based on the requested params.
     *
     * @param StoreRulesetRequest $request
     * @param RulesetTransformer  $transformer
     * @return JsonResponse
     */
    public function createRuleset(StoreRulesetRequest $request, RulesetTransformer $transformer): JsonResponse
    {
        $requestData = $request->safe()->collect();

        $ruleset = $this->rulesetRepository->updateOrCreateRuleset(
            name    : $requestData->get(self::REQUEST_NAME),
            type    : $requestData->get(self::REQUEST_TYPE),
            source  : $requestData->get(self::REQUEST_SOURCE),
            filter  : $requestData->get(self::REQUEST_FILTER) ?? [],
            payload : $requestData->get(self::REQUEST_RULES),
        );

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS => true,
            self::REQUEST_RULESET     => $transformer->transformRuleset($ruleset)
        ]);
    }

    /**
     * Handles updating the requested ruleset based on the provided dataset.
     *
     * @param Ruleset             $ruleset
     * @param StoreRulesetRequest $request
     * @return JsonResponse
     */
    public function updateRuleset(Ruleset $ruleset, StoreRulesetRequest $request): JsonResponse
    {
        $requestData = $request->safe()->collect();

        $ruleset = $this->rulesetRepository->updateOrCreateRuleset(
            name    : $requestData->get(self::REQUEST_NAME),
            type    : $requestData->get(self::REQUEST_TYPE),
            source  : $requestData->get(self::REQUEST_SOURCE),
            filter  : $requestData->get(self::REQUEST_FILTER) ?? [],
            payload : $requestData->get(self::REQUEST_RULES),
            id      : $ruleset->{Ruleset::FIELD_ID},
        );

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS => !! $ruleset
        ]);
    }

    /**
     * Removes the requested ruleset from the system.
     *
     * @param Ruleset $ruleset
     * @return JsonResponse
     */
    public function deleteRuleset(Ruleset $ruleset): JsonResponse
    {
        $this->performAuthorizationCheck(PermissionType::RULESET_MANAGEMENT->value);

        $status = $this->rulesetRepository->deleteRuleset($ruleset);

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS => $status
        ]);
    }

    /**
     * Returns the base template carrying set of rules.
     *
     * @return JsonResponse
     */
    public function getTemplate(): JsonResponse
    {
        $template = RulesetFactory::generateTemplate();

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS    => true,
            self::RESPONSE_KEY_TEMPLATE  => $template
        ]);
    }
}
