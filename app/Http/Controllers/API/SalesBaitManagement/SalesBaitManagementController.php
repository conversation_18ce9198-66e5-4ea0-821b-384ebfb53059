<?php

namespace App\Http\Controllers\API\SalesBaitManagement;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\RestrictSalesBaitCompanyRequest;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\Location;
use App\Models\SalesBaitConfiguration;
use App\Models\SalesBaitLead;
use App\Repositories\LocationRepository;
use App\Repositories\SalesBait\SalesBaitRepository;
use App\Services\SalesBait\SalesBaitConfigurationService;
use App\Services\SalesBait\SalesBaitLeadService;
use App\Transformers\Sales\SalesBaitRestrictedCompanyTransformer;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class SalesBaitManagementController extends APIController
{
    const REQUEST_INDUSTRY      = 'industry';
    const REQUEST_LOCATIONS     = 'locations';
    const REQUEST_START_DATE    = 'start_date';
    const REQUEST_END_DATE      = 'end_date';
    const INDUSTRY_SOLAR        = 'solar';
    const INDUSTRY_ROOFER       = 'roofing';

    public function __construct(
        Request                              $request,
        JsonAPIResponseFactory               $apiResponseFactory,
        public SalesBaitConfigurationService $configurationService,
        public SalesBaitLeadService          $salesBaitLeadService,
        public LocationRepository            $locationRepository
    ) { parent::__construct($request, $apiResponseFactory); }

    /**
     * Handles loading the sales bait configuration.
     *
     * @return JsonResponse
     */
    public function configuration(): JsonResponse
    {
        return $this->formatResponse([
            "solar"   => $this->configurationService
                ->getGlobalConfiguration(SalesBaitConfiguration::INDUSTRY_SOLAR, [SalesBaitConfiguration::RELATION_LOCATION])
                ->sortBy(SalesBaitConfiguration::RELATION_LOCATION . '.' . Location::STATE_KEY)
                ->values(),
            "roofing" => $this->configurationService
                ->getGlobalConfiguration(SalesBaitConfiguration::INDUSTRY_ROOFING, [SalesBaitConfiguration::RELATION_LOCATION])
                ->sortBy(SalesBaitConfiguration::RELATION_LOCATION . '.' . Location::STATE_KEY)
                ->values()
        ]);
    }

    public function stateConfiguration(string $industry, int $stateLocationId): JsonResponse
    {
        return $this->formatResponse(
            $this->configurationService
                ->getCountyConfiguration($stateLocationId, $industry, [SalesBaitConfiguration::RELATION_LOCATION])
                ->sortBy(SalesBaitConfiguration::RELATION_LOCATION . '.' . Location::COUNTY_KEY)
                ->values()
                ->toArray()
        );
    }

    /**
     * Handles loading the sales baits
     *
     * @return JsonResponse
     */
    public function salesBaits(): JsonResponse
    {
        $state      = $this->request->get(Location::STATE, null);
        $county     = $this->request->get(Location::COUNTY, null);
        $industry   = $this->request->get(self::REQUEST_INDUSTRY, null);
        $startDate  = $this->request->get(self::REQUEST_START_DATE, null);
        $endDate    = $this->request->get(self::REQUEST_END_DATE, null);

        if(!$this->validateFilterInputs($state, $county, $industry, $startDate, $endDate)){
            return $this->formatResponse([
                "status" => false
            ]);
        }

        if ($industry == self::INDUSTRY_SOLAR)
            $industry = EloquentCompany::TYPE_INSTALLER;
        elseif ($industry == self::INDUSTRY_ROOFER)
            $industry = EloquentCompany::TYPE_ROOFER;

        $salesBaits = $this->salesBaitLeadService->getFilteredSalesBaitsWithCompanyInformation($state, $county, $industry, $startDate, $endDate)
            ->paginate(5, ['*'], 'page', $this->request->get('page'));

        return $this->formatResponse([
            "salesBaits"        =>      $salesBaits,
        ]);
    }

    /**
     * Gathers statistics of sales bait to show on metrics
     *
     * @return JsonResponse
     */
    public function statistics(): JsonResponse
    {
        return $this->formatResponse([
            "salesbaits"        =>      $this->salesBaitLeadService->getTotalSalesBaits(),
            "clicks"            =>      $this->salesBaitLeadService->getTotalClicks(),
            "actionsTaken"      =>      $this->salesBaitLeadService->getTotalActionsTaken()
        ]);
    }

    /**
     * Prepares custom statistics for sales bait metrics based on the requested dataset (filters)
     *
     * @return JsonResponse
     */
    public function customStatistics(): JsonResponse
    {
        $state      = $this->request->get(Location::STATE, null);
        $county     = $this->request->get(Location::COUNTY, null);
        $industry   = $this->request->get(self::REQUEST_INDUSTRY, null);
        $startDate  = $this->request->get(self::REQUEST_START_DATE, null);
        $endDate    = $this->request->get(self::REQUEST_END_DATE, null);

        if(!$this->validateFilterInputs($state, $county, $industry, $startDate, $endDate)){
            return $this->formatResponse([
                "status" => false
            ]);
        }

        if ($industry == self::INDUSTRY_SOLAR)
            $industry = EloquentCompany::TYPE_INSTALLER;
        elseif ($industry == self::INDUSTRY_ROOFER)
            $industry = EloquentCompany::TYPE_ROOFER;

        $salesBaitFilteredCollection = $this->salesBaitLeadService->getFilteredSalesBaits(
            $state,
            $county,
            $industry,
            $startDate ? Carbon::parse($startDate)->format('Y-m-d') : null,
            $endDate ? Carbon::parse($endDate)->format('Y-m-d') : null
        );

        return $this->formatResponse(
            [
                "salesbaits"        => $salesBaitFilteredCollection->count(),
                "clicks"            => $salesBaitFilteredCollection->sum(SalesBaitLead::FIELD_CLICKS),
                "actionsTaken"      => $salesBaitFilteredCollection->sum(SalesBaitLead::FIELD_ACTIONS_TAKEN)
            ]
        );
    }

    public function save(string $industry): JsonResponse
    {
        $industryEnabled = $this->request->get(self::REQUEST_INDUSTRY, null);
        $locations = $this->request->get(self::REQUEST_LOCATIONS, null);

        if($industryEnabled !== null)
            SalesBaitConfiguration::query()
                ->where(SalesBaitConfiguration::FIELD_TYPE, SalesBaitConfiguration::TYPE_NATIONAL)
                ->where(SalesBaitConfiguration::FIELD_INDUSTRY, $industry)
                ->update([SalesBaitConfiguration::FIELD_ENABLED => $industryEnabled]);

        if($locations !== null) {
            $data = [];
            foreach($locations as $location)
                $data[] = [
                    SalesBaitConfiguration::FIELD_ID => $location[SalesBaitConfiguration::FIELD_ID],
                    SalesBaitConfiguration::FIELD_TYPE => $location[SalesBaitConfiguration::FIELD_TYPE],
                    SalesBaitConfiguration::FIELD_LOCATION_ID => $location[SalesBaitConfiguration::FIELD_LOCATION_ID],
                    SalesBaitConfiguration::FIELD_ENABLED => $location[SalesBaitConfiguration::FIELD_ENABLED],
                    SalesBaitConfiguration::FIELD_INDUSTRY => $industry,
                ];

            SalesBaitConfiguration::query()->upsert(
                $data,
                [SalesBaitConfiguration::FIELD_ID],
                [SalesBaitConfiguration::FIELD_ENABLED]
            );
        }

        return $this->formatResponse([]);
    }

    /**
     * @return bool
     * @throws ValidationException
     */
    protected function validateFilterInputs($state, $county, $industry, $startDate, $endDate): bool {
        $validator = Validator::make(
            [
                Location::STATE             => $state,
                Location::COUNTY            => $county,
                self::REQUEST_INDUSTRY      => $industry,
                self::REQUEST_START_DATE    => $startDate,
                self::REQUEST_END_DATE      => $endDate
            ],
            [
                Location::STATE             => 'nullable|string|min:2',
                Location::COUNTY            => 'nullable|string|min:2',
                self::REQUEST_INDUSTRY      => 'nullable|in:'.self::INDUSTRY_SOLAR.','.self::INDUSTRY_ROOFER,
                self::REQUEST_START_DATE    => 'nullable|date',
                self::REQUEST_END_DATE      => 'nullable|date'
            ]
        );

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return true;
    }

    protected function getCompaniesSentSalesBait($leadId): JsonResponse
    {
        $companies = $this->salesBaitLeadService->getCompaniesSentSalesBait($leadId);
        return $this->formatResponse([
            'companies' => $companies,
        ]);
    }

    /**
     * Returns all restricted companies.
     *
     * @param SalesBaitRepository $repository
     * @param SalesBaitRestrictedCompanyTransformer $transformer
     * @return JsonResponse
     */
    public function getRestrictedCompanies(SalesBaitRepository $repository, SalesBaitRestrictedCompanyTransformer $transformer): JsonResponse
    {
        return $this->formatResponse([
            "companies" => $transformer->transformAll($repository->getRestrictedCompanies())
        ]);
    }

    /**
     * Handles the restricting of a company to receive sales baits.
     *
     * @param RestrictSalesBaitCompanyRequest $request
     * @param SalesBaitRepository $repository
     * @return JsonResponse
     */
    public function restrictCompany(RestrictSalesBaitCompanyRequest $request, SalesBaitRepository $repository): JsonResponse
    {
        return $this->formatResponse([
            "status" => $repository->restrictCompany($request->safe()->offsetGet(RestrictSalesBaitCompanyRequest::REQUEST_COMPANY_ID))
        ]);
    }
}
