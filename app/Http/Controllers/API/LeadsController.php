<?php

namespace App\Http\Controllers\API;

use App\Factories\JsonAPIResponseFactory;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCategory;
use App\Models\Legacy\Location;
use App\Repositories\Legacy\QuoteRepository;
use App\Repositories\LocationRepository;
use App\Transformers\Legacy\QuoteTransformer;
use App\Models\Odin\ConsumerProduct;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class LeadsController extends APIController
{
    const ZIP_CODE   = 'zip_code';
    const COUNTY_KEY = 'county_key';
    const STATE_KEY  = 'state_key';
    const INDUSTRY   = 'industry';
    const DAYS       = 'days';

    /** @var LocationRepository $locationRepository */
    protected LocationRepository $locationRepository;

    public function __construct(Request $request, JsonAPIResponseFactory $apiResponseFactory, LocationRepository $locationRepository)
    {
        parent::__construct($request, $apiResponseFactory);
        $this->locationRepository = $locationRepository;
    }

    /**
     * @param int $leadId
     *
     * @return JsonResponse
     */
    public function getCompaniesSoldTo(int $leadId): JsonResponse
    {
        $quoteCompanies = EloquentQuoteCompany::query()
            ->where(EloquentQuoteCompany::QUOTE_ID, $leadId)
            ->get()
            ->map(fn(EloquentQuoteCompany $eloquentQuoteCompany) => [
                'company_name' => $eloquentQuoteCompany->company->companyname,
                'cost' => $eloquentQuoteCompany->cost,
                'chargeable' => $eloquentQuoteCompany->chargeable ? 'Yes' : 'No',
                'delivered' => $eloquentQuoteCompany->delivered ? 'Yes' : 'No',
                'date' => $eloquentQuoteCompany->{EloquentQuote::TIMESTAMP_ADDED} ?
                    Carbon::parse($eloquentQuoteCompany->{EloquentQuote::TIMESTAMP_ADDED})->toIso8601String() : null,
                'date_delivered' => $eloquentQuoteCompany->{EloquentQuoteCompany::TIMESTAMP_DELIVERED} ?
                    Carbon::parse($eloquentQuoteCompany->{EloquentQuoteCompany::TIMESTAMP_DELIVERED})->toIso8601String() : null,
            ]);

        return $this->formatResponse($quoteCompanies->toArray());
    }

    public function getLeadStatuses(): JsonResponse
    {
        return $this->formatResponse([
            'statuses' => [
                ['name' => EloquentQuote::VALUE_STATUS_ALLOCATED, 'id' => ConsumerProduct::STATUS_ALLOCATED],
                ['name' => EloquentQuote::VALUE_STATUS_CANCELLED, 'id' => ConsumerProduct::STATUS_CANCELLED],
                ['name' => EloquentQuote::VALUE_STATUS_INITIAL, 'id' => ConsumerProduct::STATUS_INITIAL],
                ['name' => 'unsold', 'id' => ConsumerProduct::STATUS_UNSOLD],
                ['name' => EloquentQuote::VALUE_STATUS_UNDER_REVIEW, 'id' => ConsumerProduct::STATUS_UNDER_REVIEW]
            ]
        ]);
    }

    public function getLeadCategories(): JsonResponse
    {
        return $this->formatResponse([
            LeadCategory::CATEGORY_NAME_RESIDENTIAL,
            LeadCategory::CATEGORY_NAME_COMMERCIAL
        ]);
    }

    /**
     * @param QuoteRepository $quoteRepository
     * @param QuoteTransformer $transformer
     *
     * @return JsonResponse
     */
    public function getUnsoldLeadsInCounty(QuoteRepository $quoteRepository, QuoteTransformer $transformer): JsonResponse
    {
        $countyKey = $this->request->get(self::COUNTY_KEY);
        $stateKey  = $this->request->get(self::STATE_KEY);
        $industry  = $this->request->get(self::INDUSTRY, EloquentQuote::LEAD_INDUSTRY_SOLAR);
        $days      = $this->request->get(self::DAYS, 2);

        $zipCodes = $this->locationRepository->getZipCodesInCounty($countyKey, $stateKey)->pluck(Location::ZIP_CODE);


        return $this->formatResponse([
            'leads' => $transformer->transformUnsoldLeads(
                $quoteRepository->filterSMSVerifiedLeads(
                    $quoteRepository->getUnsoldLeadsInCountyByZipCodesAndIndustry($zipCodes, now()->subDays($days)->startOfDay(), $industry)
                        ->load(EloquentQuote::RELATION_ADDRESS)
                )->values()
            )
        ]);
    }

    /**
     * @param QuoteRepository $quoteRepository
     * @param QuoteTransformer $transformer
     *
     * @return JsonResponse
     */
    public function getUndersoldLeadsInCounty(QuoteRepository $quoteRepository, QuoteTransformer $transformer): JsonResponse
    {
        $countyKey = $this->request->get(self::COUNTY_KEY);
        $stateKey  = $this->request->get(self::STATE_KEY);
        $industry  = $this->request->get(self::INDUSTRY, EloquentQuote::LEAD_INDUSTRY_SOLAR);
        $days      = $this->request->get(self::DAYS, 2);

        $zipCodes = $this->locationRepository->getZipCodesInCounty($countyKey, $stateKey)->pluck(Location::ZIP_CODE);

        return $this->formatResponse([
            'leads' => $transformer->transformUndersoldLeads(
                $quoteRepository->filterSMSVerifiedLeads(
                    $quoteRepository->getUndersoldLeadsInCountyByZipCodesAndIndustry($zipCodes, now()->subDays($days)->startOfDay(), $industry)
                        ->load(EloquentQuote::RELATION_ADDRESS)
                )->values()
            )
        ]);
    }

    /**
     * @param QuoteRepository $quoteRepository
     * @param QuoteTransformer $transformer
     *
     * @return JsonResponse
     */
    public function getUnsoldLeadsInCountyByZipCode(QuoteRepository $quoteRepository, QuoteTransformer $transformer): JsonResponse
    {
        $zipCode = $this->request->get(self::ZIP_CODE);
        $industry = $this->request->get(self::INDUSTRY, EloquentQuote::LEAD_INDUSTRY_SOLAR);
        $days = $this->request->get(self::DAYS, 2);

        $zipCodes = $this->locationRepository->getZipCodesInCountyByZipCode($zipCode)->pluck(Location::ZIP_CODE);

        return $this->formatResponse([
            'leads' => $transformer->transformUnsoldLeads(
                $quoteRepository->filterSMSVerifiedLeads(
                    $quoteRepository->getUnsoldLeadsInCountyByZipCodesAndIndustry($zipCodes, now()->subDays($days)->startOfDay(), $industry)
                        ->load(EloquentQuote::RELATION_ADDRESS)
                )->values()
            )
        ]);
    }

    /**
     * @param QuoteRepository $quoteRepository
     * @param QuoteTransformer $transformer
     *
     * @return JsonResponse
     */
    public function getUndersoldLeadsInCountyByZipCode(QuoteRepository $quoteRepository, QuoteTransformer $transformer): JsonResponse
    {
        $zipCode = $this->request->get(self::ZIP_CODE);
        $industry = $this->request->get(self::INDUSTRY, EloquentQuote::LEAD_INDUSTRY_SOLAR);

        $zipCodes = $this->locationRepository->getZipCodesInCountyByZipCode($zipCode)->pluck(Location::ZIP_CODE);

        $days = $this->request->get(self::DAYS, 2);

        return $this->formatResponse([
            'leads' => $transformer->transformUndersoldLeads(
                $quoteRepository->filterSMSVerifiedLeads(
                    $quoteRepository->getUndersoldLeadsInCountyByZipCodesAndIndustry($zipCodes, now()->subDays($days)->startOfDay(), $industry)
                        ->load(EloquentQuote::RELATION_ADDRESS)
                )->values()
            )
        ]);
    }
}
