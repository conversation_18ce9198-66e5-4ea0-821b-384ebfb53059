<?php

namespace App\Http\Controllers\API\ClientDashboard;

use App\Enums\Odin\BudgetCategory;
use App\Enums\Odin\Product;
use App\Enums\Odin\Product as ProductEnum;
use App\Enums\Odin\QualityTier;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Requests\Dashboard\UpdateAppointmentCampaignSchedulesRequest;
use App\Http\Requests\Dashboard\UpdateAppointmentCampaignStatusRequest;
use App\Http\Requests\Dashboard\UpdateAppointmentsCampaignBudgetRequest;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignLocation;
use App\Models\Odin\Company;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ProductCampaignBudget;
use App\Models\Odin\ProductCampaignSchedule;
use App\Repositories\Odin\CompanyRepository;
use App\Repositories\Odin\ProductCampaignRepository;
use App\Services\Odin\Appointments\AppointmentCalendarIntegrationService;
use App\Services\Odin\Appointments\AppointmentService;
use App\Transformers\API\ProductCampaignAPITransformer;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Throwable;

class CampaignController extends BaseClientDashboardAPIController
{
    const REQUEST_STATUS = 'status';
    const REQUEST_REACTIVATE_AT_TIMESTAMP = 'reactivate_at_timestamp';

    const REQUEST_BUDGETS = 'budgets';

    const REQUEST_CALENDAR_IDS = 'calendar_ids';

    const CAMPAIGN_PRODUCT = 'product';

    const BUDGETS_PARENT_LEGACY_LEAD_CAMPAIGN_ID = 'parent_legacy_lead_campaign_id';
    const BUDGETS_ID = 'id';
    const BUDGETS_STATUS = 'status';
    const BUDGETS_DESCRIPTION = 'description';
    const BUDGETS_BUDGET_TYPE = 'budget_type';
    const BUDGETS_BUDGET_VALUE = 'budget_value';
    const BUDGETS_TIER = 'tier';
    const BUDGETS_CATEGORY = 'category';

    const REQUEST_LEAD_CATEGORY_IDS = 'lead_category_ids';

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param CompanyRepository $companyRepository
     * @param ProductCampaignRepository $campaignRepository
     * @param ProductCampaignAPITransformer $transformer
     */
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        CompanyRepository $companyRepository,
        private readonly ProductCampaignRepository $campaignRepository,
        private readonly ProductCampaignAPITransformer $transformer
    )
    {
        parent::__construct($request, $apiResponseFactory, $companyRepository);
    }

    /**
     * @param int $legacyCompanyId
     * @return JsonResponse
     */
    public function getAll(int $legacyCompanyId): JsonResponse
    {
        $campaigns = $this->campaignRepository->getAllByLegacyCompanyId($legacyCompanyId);

        return $this->formatResponse($this->transformer->transformProductCampaigns($campaigns));
    }

    /**
     * @param int $legacyCompanyId
     * @param string $legacyCampaignUuid
     * @param string $product
     * @return JsonResponse
     */
    public function getProductCampaignByLegacyUuid(int $legacyCompanyId, string $legacyCampaignUuid, string $product): JsonResponse
    {
        $campaign = $this->campaignRepository->getByLegacyCampaignUuid($legacyCampaignUuid, ProductEnum::from($product));

        return $this->formatResponse($campaign ? $this->transformer->transformProductCampaign($campaign) : []);
    }

    /**
     * @param int $legacyCompanyId
     * @param string $legacyCampaignUuid
     * @param UpdateAppointmentsCampaignBudgetRequest $updateAppointmentsCampaignBudgetRequest
     * @return JsonResponse
     */
    public function updateAppointmentsCampaignBudgetByLegacyUuid(
        int $legacyCompanyId,
        string $legacyCampaignUuid,
        UpdateAppointmentsCampaignBudgetRequest $updateAppointmentsCampaignBudgetRequest
    ): JsonResponse
    {
        $data = $updateAppointmentsCampaignBudgetRequest->safe()->only([
            self::REQUEST_BUDGETS
        ]);

        $appointmentCampaign = $this->campaignRepository->getByLegacyCampaignUuid($legacyCampaignUuid, ProductEnum::APPOINTMENT, []);

        if(empty($appointmentCampaign)) {
            $appointmentCampaign = $this->campaignRepository->createFromLegacyCampaign($legacyCampaignUuid, ProductEnum::APPOINTMENT);
        }

        $now = Carbon::now();

        foreach($data[self::REQUEST_BUDGETS] as $budget) {
            $productCampaignBudget = ProductCampaignBudget::firstOrNew([
                ProductCampaignBudget::FIELD_PRODUCT_CAMPAIGN_ID => $appointmentCampaign->{ProductCampaign::FIELD_ID},
                ProductCampaignBudget::FIELD_QUALITY_TIER => QualityTier::from($budget[self::BUDGETS_TIER]),
                ProductCampaignBudget::FIELD_CATEGORY => BudgetCategory::VERIFIED->value
            ]);

            $budgetChanged = false;

            if((bool) $productCampaignBudget->status === false && (bool) $budget[self::BUDGETS_STATUS] === true) {
                $budgetChanged = true;
            }
            else if($productCampaignBudget->{ProductCampaignBudget::FIELD_VALUE_TYPE} !== $budget[self::BUDGETS_BUDGET_TYPE]) {
                $budgetChanged = true;
            }
            else if($productCampaignBudget->{ProductCampaignBudget::FIELD_VALUE_TYPE} === $budget[self::BUDGETS_BUDGET_TYPE]
                && (float) $productCampaignBudget->{ProductCampaignBudget::FIELD_VALUE} !== (float) $budget[self::BUDGETS_BUDGET_VALUE]) {
                $budgetChanged = true;
            }

            if($budgetChanged) {
                $productCampaignBudget->{ProductCampaignBudget::FIELD_BUDGET_START_TIMESTAMP} = $now;
            }

            $productCampaignBudget->{ProductCampaignBudget::FIELD_VALUE} = $budget[self::BUDGETS_BUDGET_VALUE];
            $productCampaignBudget->{ProductCampaignBudget::FIELD_VALUE_TYPE} = $budget[self::BUDGETS_BUDGET_TYPE];
            $productCampaignBudget->{ProductCampaignBudget::FIELD_STATUS} = $budget[self::BUDGETS_STATUS];

            $productCampaignBudget->save();
        }

        return $this->formatResponse([
            "status" => true
        ]);
    }

    /**
     * @param int $legacyCompanyId
     * @param string $legacyCampaignUuid
     * @param UpdateAppointmentCampaignStatusRequest $updateAppointmentCampaignStatusRequest
     * @param AppointmentCalendarIntegrationService $appointmentCalendarIntegrationService
     * @return JsonResponse
     * @throws Exception|Throwable
     */
    public function updateAppointmentCampaignStatusByUuid(
        int $legacyCompanyId,
        string $legacyCampaignUuid,
        UpdateAppointmentCampaignStatusRequest $updateAppointmentCampaignStatusRequest,
        AppointmentCalendarIntegrationService $appointmentCalendarIntegrationService
    ): JsonResponse
    {
        $appointmentCampaign = $this->campaignRepository->getByLegacyCampaignUuid($legacyCampaignUuid, ProductEnum::APPOINTMENT, []);

        if(empty($appointmentCampaign)) {
            return $this->formatResponse([
                "status" => false,
                "msg" => "Appointments budget must be set in order to activate appointments"
            ]);
        }

        $data = $updateAppointmentCampaignStatusRequest->safe()->only([self::REQUEST_STATUS, self::REQUEST_REACTIVATE_AT_TIMESTAMP]);

        if($data[self::REQUEST_STATUS]) {
            if($appointmentCampaign->{ProductCampaign::RELATION_BUDGETS}->where(ProductCampaign::FIELD_STATUS, true)->count() <= 0) {
                return $this->formatResponse([
                    "status" => false,
                    "msg" => "At least one appointment budget must be active"
                ]);
            }

            if($appointmentCampaign->{ProductCampaign::RELATION_SCHEDULES}->count() <= 0) {
                return $this->formatResponse([
                    "status" => false,
                    "msg" => "At least one appointments calendar must be active"
                ]);
            }

            if(
                !$appointmentCalendarIntegrationService->checkCalendarsHaveTimeSlots(
                    $appointmentCampaign->{ProductCampaign::RELATION_SCHEDULES}->pluck(ProductCampaignSchedule::FIELD_SCHEDULE_ID)->toArray(),
                    $this->getLoggedInCompanyUser()
                )
            ) {
                return $this->formatResponse([
                    "status" => false,
                    "msg" => "Appointment calendars must have available days/times"
                ]);
            }
        }

        return $this->formatResponse([
            "status" => $this->campaignRepository->updateStatus(
                $appointmentCampaign->{ProductCampaign::FIELD_ID},
                $data[self::REQUEST_STATUS],
                $appointmentCampaign->company,
                $this->getLoggedInCompanyUser()->id,
                null, // Pause reason is not present is this request used by the Legacy dashboards
                $data[self::REQUEST_REACTIVATE_AT_TIMESTAMP],
            )
        ]);
    }

    /**
     * @param int $legacyCompanyId
     * @param string $legacyCampaignUuid
     * @param UpdateAppointmentCampaignSchedulesRequest $updateAppointmentCampaignSchedulesRequest
     * @param AppointmentCalendarIntegrationService $appointmentCalendarIntegrationService
     * @return JsonResponse
     * @throws Throwable
     */
    public function updateAppointmentCampaignSchedulesByUuid(
        int $legacyCompanyId,
        string $legacyCampaignUuid,
        UpdateAppointmentCampaignSchedulesRequest $updateAppointmentCampaignSchedulesRequest,
        AppointmentCalendarIntegrationService $appointmentCalendarIntegrationService
    ): JsonResponse
    {
        $appointmentCampaign = $this->campaignRepository->getByLegacyCampaignUuid($legacyCampaignUuid, ProductEnum::APPOINTMENT, []);

        if(empty($appointmentCampaign)) {
            return $this->formatResponse([
                "status" => false
            ]);
        }

        $calendarIds = $updateAppointmentCampaignSchedulesRequest->safe()->offsetGet(self::REQUEST_CALENDAR_IDS);

        return $this->formatResponse([
            "status" => $appointmentCalendarIntegrationService->updateProductCampaignSchedules($appointmentCampaign->{ProductCampaign::FIELD_ID}, $calendarIds)
        ]);
    }

    /**
     * @param int $legacyCompanyId
     * @param string $legacyCampaignUuid
     * @param AppointmentService $appointmentService
     * @return JsonResponse
     */
    public function getAppointmentCampaignPrices(int $legacyCompanyId, string $legacyCampaignUuid, AppointmentService $appointmentService): JsonResponse
    {
        $appointmentCampaign = $this->campaignRepository->getByLegacyCampaignUuid(
            $legacyCampaignUuid,
            ProductEnum::APPOINTMENT,
            [
                ProductCampaign::RELATION_LEGACY_CAMPAIGN.'.'.LeadCampaign::RELATION_LEAD_CAMPAIGN_COUNTY_LOCATIONS.'.'.LeadCampaignLocation::RELATION_LOCATION,
                ProductCampaign::RELATION_COMPANY.'.'.Company::RELATION_LEGACY_COMPANY
            ]
        );

        if(empty($appointmentCampaign)) {
            /** @var LeadCampaign|null $leadCampaign */
            $leadCampaign = LeadCampaign::query()
                ->where(LeadCampaign::UUID, $legacyCampaignUuid)
                ->first();

            if (empty($leadCampaign)) {
                return $this->formatResponse([
                    "status" => false
                ]);
            }
            else {
                return $this->formatResponse([
                    "status" => true,
                    "prices" => $appointmentService->getAppointmentCampaignPricesFromLegacyCampaign($leadCampaign, $this->request->input(self::REQUEST_LEAD_CATEGORY_IDS)),
                ]);
            }
        }

        return $this->formatResponse([
            "status" => true,
            "prices" => $appointmentService->getAppointmentCampaignPriceRanges($appointmentCampaign, $this->request->input(self::REQUEST_LEAD_CATEGORY_IDS))
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getUnrestrictedZipCodeStatus() {
        $legacyCompanyId = $this->request->route('legacyCompanyId');
        /** @var Company $company */
        $company = Company::query()
            ->where(Company::FIELD_LEGACY_ID, $legacyCompanyId)
            ->firstOrFail();
        $unrestrictedZipCodes = $company->configuration?->unrestricted_zip_code_targeting ?? false;

        return $this->formatResponse([
            'status'                          => true,
            'unrestricted_zip_code_targeting' => $unrestrictedZipCodes
        ]);
    }
}
