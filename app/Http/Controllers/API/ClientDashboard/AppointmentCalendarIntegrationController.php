<?php

namespace App\Http\Controllers\API\ClientDashboard;

use App\Enums\CalendarPlatform;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Requests\Dashboard\CreateStaticCalendarRequest;
use App\Http\Requests\Dashboard\GetCalendarEventsInDateRangeRequest;
use App\Http\Requests\Dashboard\UpdateIntegratedCalendarRequest;
use App\Models\Odin\Company;
use App\Repositories\Odin\CompanyRepository;
use App\Services\Odin\Appointments\AppointmentCalendarIntegrationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Throwable;

class AppointmentCalendarIntegrationController extends BaseClientDashboardAPIController
{
    const REQUEST_NAME = 'name';
    const REQUEST_TIMEZONE = 'timezone';
    const REQUEST_ONLINE_DURATION = 'online_duration';
    const REQUEST_IN_HOME_DURATION = 'in_home_duration';
    const REQUEST_IN_HOME_BUFFER_BEFORE = 'in_home_buffer_before';
    const REQUEST_IN_HOME_BUFFER_AFTER = 'in_home_buffer_after';
    const REQUEST_ONLINE_BUFFER_BEFORE = 'online_buffer_before';
    const REQUEST_ONLINE_BUFFER_AFTER = 'online_buffer_after';
    const REQUEST_IN_HOME_SAME_DAY_APPOINTMENTS = 'in_home_same_day_appointments';
    const REQUEST_ONLINE_SAME_DAY_APPOINTMENTS = 'online_same_day_appointments';
    const REQUEST_OVERLAPPING_EVENTS_ALLOWED = 'overlapping_events_allowed';

    const REQUEST_PLATFORM = 'platform';
    const REQUEST_ITEMS = 'items';
    const REQUEST_OVERRIDE_ITEMS = 'override_items';

    const REQUEST_START_DATE = 'start_date';
    const REQUEST_END_DATE = 'end_date';

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param CompanyRepository $companyRepository
     * @param AppointmentCalendarIntegrationService $appointmentCalendarIntegrationService
     */
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected CompanyRepository $companyRepository,
        private readonly AppointmentCalendarIntegrationService $appointmentCalendarIntegrationService
    )
    {
        parent::__construct($request, $apiResponseFactory, $companyRepository);
    }

    /**
     * @param int $legacyCompanyId
     * @param CalendarPlatform $platform
     * @return JsonResponse
     * @throws Throwable
     */
    public function getOauthAuthorizationUri(int $legacyCompanyId, CalendarPlatform $platform): JsonResponse
    {
        return $this->formatResponse([
            "status" => true,
            "uri" => $this->appointmentCalendarIntegrationService->getOauthAuthorizationUri($platform, $this->getLoggedInCompanyUser())
        ]);
    }

    /**
     * @param CalendarPlatform $platform
     * @return View
     * @throws Throwable
     */
    public function handleOauthRedirect(CalendarPlatform $platform): View
    {
        $res = $this->appointmentCalendarIntegrationService->handleOauthRedirect($platform, request()->query());

        return view(
            'display-message',
            [
                'message' => $res ? "Authentication successful. You may now close this window." : "Authentication error."
            ]
        );
    }

    /**
     * @param int $legacyCompanyId
     * @return JsonResponse
     * @throws Throwable
     */
    public function listAllCalendarIntegrations(int $legacyCompanyId): JsonResponse
    {
        return $this->formatResponse([
            "status" => true,
            "calendars" => $this->appointmentCalendarIntegrationService->getAllCompanyCalendars($this->companyRepository->findByLegacyIdOrFail($legacyCompanyId)->{Company::FIELD_ID}, $this->getLoggedInCompanyUser())
        ]);
    }

    /**
     * @param CreateStaticCalendarRequest $createStaticCalendarRequest
     * @return JsonResponse
     * @throws Throwable
     */
    public function createStaticCalendar(CreateStaticCalendarRequest $createStaticCalendarRequest): JsonResponse
    {
        $data = $createStaticCalendarRequest->safe()->only([
            self::REQUEST_NAME,
            self::REQUEST_TIMEZONE,
            self::REQUEST_ONLINE_DURATION,
            self::REQUEST_IN_HOME_DURATION,
            self::REQUEST_ONLINE_BUFFER_AFTER,
            self::REQUEST_ONLINE_BUFFER_BEFORE,
            self::REQUEST_IN_HOME_BUFFER_AFTER,
            self::REQUEST_IN_HOME_BUFFER_BEFORE,
            self::REQUEST_IN_HOME_SAME_DAY_APPOINTMENTS,
            self::REQUEST_ONLINE_SAME_DAY_APPOINTMENTS,
            self::REQUEST_OVERLAPPING_EVENTS_ALLOWED
        ]);

        return $this->formatResponse([
            "status" => true,
            "calendar_id" => $this->appointmentCalendarIntegrationService->createStaticCalendar(
                $this->getLoggedInCompanyUser(),
                $data[self::REQUEST_NAME],
                $data[self::REQUEST_TIMEZONE],
                $data[self::REQUEST_ONLINE_DURATION],
                $data[self::REQUEST_IN_HOME_DURATION],
                $data[self::REQUEST_IN_HOME_BUFFER_BEFORE],
                $data[self::REQUEST_IN_HOME_BUFFER_AFTER],
                $data[self::REQUEST_ONLINE_BUFFER_BEFORE],
                $data[self::REQUEST_ONLINE_BUFFER_AFTER],
                $data[self::REQUEST_IN_HOME_SAME_DAY_APPOINTMENTS],
                $data[self::REQUEST_ONLINE_SAME_DAY_APPOINTMENTS],
                $data[self::REQUEST_OVERLAPPING_EVENTS_ALLOWED]
            )
        ]);
    }

    /**
     * @param int $legacyCompanyId
     * @param int $calendarId
     * @return JsonResponse
     * @throws Throwable
     */
    public function getCalendarById(int $legacyCompanyId, int $calendarId): JsonResponse
    {
        return $this->formatResponse([
            "status" => true,
            "calendar" => $this->appointmentCalendarIntegrationService->getCalendarById($calendarId, $this->getLoggedInCompanyUser())
        ]);
    }

    /**
     * @param int $legacyCompanyId
     * @param int $calendarId
     * @param UpdateIntegratedCalendarRequest $updateIntegratedCalendarRequest
     * @return JsonResponse
     */
    public function saveCalendar(int $legacyCompanyId, int $calendarId, UpdateIntegratedCalendarRequest $updateIntegratedCalendarRequest): JsonResponse
    {
        $data = $updateIntegratedCalendarRequest->safe()->only([
            self::REQUEST_NAME,
            self::REQUEST_TIMEZONE,
            self::REQUEST_PLATFORM,
            self::REQUEST_ITEMS,
            self::REQUEST_OVERRIDE_ITEMS,
            self::REQUEST_ONLINE_DURATION,
            self::REQUEST_IN_HOME_DURATION,
            self::REQUEST_ONLINE_BUFFER_AFTER,
            self::REQUEST_ONLINE_BUFFER_BEFORE,
            self::REQUEST_IN_HOME_BUFFER_AFTER,
            self::REQUEST_IN_HOME_BUFFER_BEFORE,
            self::REQUEST_IN_HOME_SAME_DAY_APPOINTMENTS,
            self::REQUEST_ONLINE_SAME_DAY_APPOINTMENTS,
            self::REQUEST_OVERLAPPING_EVENTS_ALLOWED
        ]);

        return $this->formatResponse([
            "status" => $this->appointmentCalendarIntegrationService->saveCalendar(
                $calendarId,
                $this->getLoggedInCompanyUser(),
                $data[self::REQUEST_NAME],
                $data[self::REQUEST_TIMEZONE],
                $data[self::REQUEST_ONLINE_DURATION],
                $data[self::REQUEST_IN_HOME_DURATION],
                $data[self::REQUEST_IN_HOME_BUFFER_BEFORE],
                $data[self::REQUEST_IN_HOME_BUFFER_AFTER],
                $data[self::REQUEST_ONLINE_BUFFER_BEFORE],
                $data[self::REQUEST_ONLINE_BUFFER_AFTER],
                $data[self::REQUEST_IN_HOME_SAME_DAY_APPOINTMENTS],
                $data[self::REQUEST_ONLINE_SAME_DAY_APPOINTMENTS],
                $data[self::REQUEST_OVERLAPPING_EVENTS_ALLOWED],
                $data[self::REQUEST_PLATFORM],
                $data[self::REQUEST_ITEMS],
                $data[self::REQUEST_OVERRIDE_ITEMS]
            )
        ]);
    }

    /**
     * @param int $legacyCompanyId
     * @param int $calendarId
     * @return JsonResponse
     */
    public function deleteCalendar(int $legacyCompanyId, int $calendarId): JsonResponse
    {
        return $this->formatResponse([
            "status" => $this->appointmentCalendarIntegrationService->deleteCalendar($calendarId, $this->getLoggedInCompanyUser())
        ]);
    }

    /**
     * @param GetCalendarEventsInDateRangeRequest $getCalendarEventsInDateRangeRequest
     * @param int $legacyCompanyId
     * @param int $calendarId
     * @return JsonResponse
     * @throws Throwable
     */
    public function getCalendarEventsInDateRange(GetCalendarEventsInDateRangeRequest $getCalendarEventsInDateRangeRequest, int $legacyCompanyId, int $calendarId): JsonResponse
    {
        $data = $getCalendarEventsInDateRangeRequest->safe()->only([
            self::REQUEST_START_DATE,
            self::REQUEST_END_DATE
        ]);

        return $this->formatResponse([
            "status" => true,
            "calendar_events" => $this->appointmentCalendarIntegrationService->getEventsForCalendarInDateRange(
                $calendarId,
                $data[self::REQUEST_START_DATE],
                $data[self::REQUEST_END_DATE],
                $this->getLoggedInCompanyUser()
            )
        ]);
    }
}
