<?php

namespace App\Http\Controllers\API\ClientDashboard;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Models\Legacy\EloquentUser;
use App\Models\Odin\CompanyUser;
use App\Repositories\Odin\CompanyRepository;
use App\Repositories\Odin\CompanyUserRepository;
use Illuminate\Http\Request;

abstract class BaseClientDashboardAPIController extends APIController
{

    public function __construct(Request $request, JsonAPIResponseFactory $apiResponseFactory, protected CompanyRepository $companyRepository)
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param int $legacyCompanyId
     * @return int|null
     */
    protected function convertCompanyId(int $legacyCompanyId): ?int
    {
        return $this->companyRepository->findByLegacyIdOrFail($legacyCompanyId)?->id;
    }

    /**
     * @return EloquentUser
     */
    protected function getLoggedInUser(): EloquentUser
    {
        return app(EloquentUser::class);
    }

    /**
     * @return CompanyUser
     */
    protected function getLoggedInCompanyUser(): CompanyUser
    {
        $legacyUser = $this->getLoggedInUser();

        return app(CompanyUserRepository::class)->getCompanyUserByLegacyIdAndCompanyIdOrFail(
            $legacyUser->{EloquentUser::ID},
            $this->convertCompanyId($legacyUser->{EloquentUser::COMPANY_ID})
        );
    }
}
