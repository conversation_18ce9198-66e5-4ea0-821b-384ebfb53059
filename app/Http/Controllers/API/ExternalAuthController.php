<?php

namespace App\Http\Controllers\API;

use App\Services\AuthTokenService;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class ExternalAuthController extends APIController
{
    const TOKEN = 'token';

    /**
     * @param AuthTokenService $authTokenService
     *
     * @return JsonResponse
     */
    public function getToken(AuthTokenService $authTokenService): JsonResponse
    {
        $this->performAuthorizationCheck('cms');

        return $this->formatResponse([
            'token' => $authTokenService->setSigningKey(config('services.jwt.signing_keys.auth_token'))->generateToken()
        ]);
    }

    /**
     * @param AuthTokenService $authTokenService
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function validateAuthToken(AuthTokenService $authTokenService): JsonResponse
    {
        $this->validate($this->request, [
            self::TOKEN => 'required|string'
        ]);

        $user = $authTokenService
            ->setSigningKey(config('services.jwt.signing_keys.auth_token'))
            ->getUserFromToken($this->request->get(self::TOKEN));

        return $this->formatResponse([
            'user_email' => $user->email
        ]);
    }
}
