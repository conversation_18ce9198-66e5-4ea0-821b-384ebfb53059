<?php

namespace App\Http\Controllers\API\ProductProcessing\Processing;

use App\Enums\ConsumerProductChannel;
use App\Enums\ConsumerProcessing\ConsumerProcessingActivityVisibilityScope;
use App\Http\Requests\ApproveConsumerProductRequest;
use App\Http\Requests\RefreshAgedLeadQueueRequest;
use App\Jobs\AgedQueue\PopulateAgedQueueJob;
use App\Models\LeadProcessingPendingReview;
use App\Models\LeadProcessingReservedLead;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Models\RecycledLeads\RecycledLead;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Repositories\Odin\ProductProcessing\ProductProcessingAgedQueueRepository;
use App\Repositories\Odin\ProductProcessing\ProductProcessingRepository;
use App\Repositories\RecycledLeads\RecycledLeadsRepository;
use App\Services\ConsumerProcessingActivityService;
use App\Services\Odin\ProductProcessing\ProductProcessingService;
use App\Transformers\LeadProcessing\LeadProcessingHistoryTransformer;
use App\Transformers\Odin\ConsumerProductProcessingTransformer;
use Exception;
use Illuminate\Http\JsonResponse;
use Throwable;

class ProductProcessingAPIController extends ProductProcessingBaseAPIController
{
    const string REQUEST_PREVIOUS_PRODUCT_ID    = 'previous_product_id';
    const string REQUEST_SPECIFIC_PRODUCT_ID    = 'specific_product_id';
    const string REQUEST_REASON                 = 'reason';
    const string REQUEST_COMMENT                = 'comment';
    const string REQUEST_BEST_TIME_TO_CONTACT   = 'best_time_to_contact';
    const string REQUEST_PUBLIC_COMMENT         = 'public_comment';
    const string REQUEST_REMOVE_PUBLIC_COMMENTS = 'remove_public_comments';
    const string FALLBACK_QUEUE                 = 'display';

    /**
     * @param ProductProcessingService $processingService
     * @param ConsumerProductProcessingTransformer $consumerProductProcessingTransformer
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function getNextProduct(ProductProcessingService $processingService, ConsumerProductProcessingTransformer $consumerProductProcessingTransformer): JsonResponse
    {
        $previousProductId = $this->request->input(self::REQUEST_PREVIOUS_PRODUCT_ID);
        $specificProductId = $this->request->input(self::REQUEST_SPECIFIC_PRODUCT_ID);
        $processor         = $this->getLeadProcessor();

        if($processor === null) {
            return $this->formatResponse([
                'status' => false,
                'product' => null,
                'retry' => false,
                'msg' => 'You are not a processor.'
            ]);
        }

        $product = $processingService->getNextProduct(
            processor: $processor,
            specificProductId: $specificProductId,
            previousProductId: $previousProductId
        );

        if (!empty($specificProductId) && $product === null) {
            $product = ConsumerProduct::query()->findOrFail($specificProductId);
            return $this->formatResponse([
                'status'  => true,
                'lead' => $consumerProductProcessingTransformer->transform($product, $this->getQueueForProduct($product)),
                'retry'   => false,
                'read_only' => true,
                'locked_by_user' => LeadProcessingReservedLead::query()->where(LeadProcessingReservedLead::FIELD_CONSUMER_PRODUCT_ID, $product->id)->first()->processor?->user,
            ]);
        }

        $retry = false;

        if ($product !== null) {
            $retry = !$processingService->reserveProduct($product, $processor);
            if ($retry) {
                $product = null;
            }
        }

        return $this->formatResponse([
            'status'  => true,
            'lead' => $product ? $consumerProductProcessingTransformer->transform($product, $specificProductId ? $this->getQueueForProduct($product) : $processor->team->primaryQueue->primary_status) : null,
            'retry'   => $retry,
        ]);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param ProductProcessingService $productProcessingService
     * @param ConsumerProductRepository $consumerProductRepository
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function cancel(ConsumerProduct $consumerProduct, ProductProcessingService $productProcessingService, ConsumerProductRepository $consumerProductRepository): JsonResponse
    {
        if (!$this->isProductReservedByProcessor($consumerProduct, $productProcessingService)) {
            return $this->formatResponse([
                'status' => false,
                'msg'    => 'You have not locked this lead. Please refresh the page and try again.'
            ]);
        }

        $reason = $this->request->get(self::REQUEST_REASON);
        $publicComment = !!$this->request->get(self::REQUEST_PUBLIC_COMMENT);
        $comment = $this->request->get(self::REQUEST_COMMENT);

        return $this->formatResponse([
            "status" => $this->productProcessingService->cancelLead(
                consumerProduct: $consumerProduct,
                processor:  $this->getLeadProcessor(),
                reason:  $reason,
                comment: $comment,
                publicComment:  $publicComment,
                )
                && $consumerProductRepository->updateConsumerProductStatus($consumerProduct, ConsumerProduct::STATUS_CANCELLED)
        ]);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param ProductProcessingService $productProcessingService
     * @param ConsumerProductRepository $consumerProductRepository
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function moveToPendingReview(ConsumerProduct $consumerProduct, ProductProcessingService $productProcessingService, ConsumerProductRepository $consumerProductRepository): JsonResponse
    {
        if (!$this->isProductReservedByProcessor($consumerProduct, $productProcessingService)) {
            return $this->formatResponse([
                'status' => false,
                'msg'    => 'You have not locked this lead. Please refresh the page and try again.'
            ]);
        }

        $reason = $this->request->get(self::REQUEST_REASON);
        $comment = $this->request->get(self::REQUEST_COMMENT);
        $publicComment = !!$this->request->get(self::REQUEST_PUBLIC_COMMENT);

        return $this->formatResponse([
            "status" => $consumerProductRepository->updateConsumerProductStatus($consumerProduct, ConsumerProduct::STATUS_PENDING_REVIEW)
                        && $this->productProcessingService->moveToPendingReview(
                            consumerProduct: $consumerProduct,
                            processor: $this->getLeadProcessor(),
                            reason: $reason,
                            comment: $comment,
                            publicComment: $publicComment,
                )
        ]);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param ProductProcessingService $productProcessingService
     * @param ConsumerProductRepository $consumerProductRepository
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function moveToUnderReview(ConsumerProduct $consumerProduct, ProductProcessingService $productProcessingService, ConsumerProductRepository $consumerProductRepository): JsonResponse
    {
        if (!$this->isProductReservedByProcessor($consumerProduct, $productProcessingService)) {
            return $this->formatResponse([
                'status' => false,
                'msg'    => 'You have not locked this lead. Please refresh the page and try again.'
            ]);
        }

        $reason = $this->request->get(self::REQUEST_REASON);
        $publicComment = !!$this->request->get(self::REQUEST_PUBLIC_COMMENT);
        $comment = $this->request->get(self::REQUEST_COMMENT);

        return $this->formatResponse([
            "status" => $consumerProductRepository->updateConsumerProductStatus($consumerProduct, ConsumerProduct::STATUS_UNDER_REVIEW)
                        && $this->productProcessingService->moveToUnderReview(
                            consumerProduct: $consumerProduct,
                            processor: $this->getLeadProcessor(),
                            reason: $reason,
                            comment: $comment,
                            publicComment: $publicComment,
                )
        ]);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param ProductProcessingService $productProcessingService
     * @param ProductProcessingAgedQueueRepository $agedQueueRepository
     *
     * @return JsonResponse
     */
    public function skippedInAgedQueue(
        ConsumerProduct $consumerProduct,
        ProductProcessingService $productProcessingService,
        ProductProcessingAgedQueueRepository $agedQueueRepository
    ): JsonResponse
    {
        if (!$this->isProductReservedByProcessor($consumerProduct, $productProcessingService)) {
            return $this->formatResponse([
                'status' => false,
                'msg'    => 'You have not locked this lead. Please refresh the page and try again.'
            ]);
        }
        $processor = $this->getLeadProcessor();

        return $this->formatResponse([
            'status' => $agedQueueRepository->skipProduct($consumerProduct, $this->getLeadProcessor(), $this->request->get(self::REQUEST_REASON))
                && $productProcessingService->releaseProduct($consumerProduct, $processor)
        ]);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param ProductProcessingAgedQueueRepository $agedQueueRepository
     *
     * @return JsonResponse
     */
    public function getAgedQueueSkipReasons(ConsumerProduct $consumerProduct, ProductProcessingAgedQueueRepository $agedQueueRepository): JsonResponse
    {
        return $this->formatResponse([
            'status' => true,
            'reasons' => $agedQueueRepository->getSkipReasons($consumerProduct)
        ]);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param ProductProcessingRepository $productProcessingRepository
     * @param RecycledLeadsRepository $recycledLeadsRepository
     *
     * @return JsonResponse
     */
    public function removeFromQueue(ConsumerProduct $consumerProduct, ProductProcessingRepository $productProcessingRepository, RecycledLeadsRepository $recycledLeadsRepository): JsonResponse
    {
        $recycledLeadsRepository->createRecycledLeadForConsumerProduct($consumerProduct, RecycledLead::STATUS_CANCELED);

        return $this->formatResponse([
            'status' => $productProcessingRepository->removeAged($consumerProduct)
        ]);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param ProductProcessingRepository $productProcessingRepository
     *
     * @return JsonResponse
     */
    public function removeFromAffiliateQueue(ConsumerProduct $consumerProduct, ProductProcessingRepository $productProcessingRepository): JsonResponse
    {
        return $this->formatResponse([
            'status' => $productProcessingRepository->removeAffiliate($consumerProduct)
        ]);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param ProductProcessingService $productProcessingService
     *
     * @return bool
     */
    protected function isProductReservedByProcessor(ConsumerProduct $consumerProduct, ProductProcessingService $productProcessingService): bool
    {
        $processor = $this->getLeadProcessor();

        return $processor && $productProcessingService->isProductReservedByProcessor($consumerProduct, $processor);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param ProductProcessingService $productProcessingService
     * @param ConsumerProductRepository $consumerProductRepository
     * @param ApproveConsumerProductRequest $request
     * @param RecycledLeadsRepository $recycledLeadsRepository
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function approveProduct(
        ConsumerProduct $consumerProduct,
        ProductProcessingService $productProcessingService,
        ConsumerProductRepository $consumerProductRepository,
        ApproveConsumerProductRequest $request,
        RecycledLeadsRepository $recycledLeadsRepository
    ): JsonResponse
    {
        try {
            if (!$this->isProductReservedByProcessor($consumerProduct, $productProcessingService)) {
                return $this->formatResponse([
                    "status" => false,
                    "msg"    => "You have not locked this lead. Please refresh the page and try again."
                ]);
            }

            $reason                 = $this->request->get(self::REQUEST_REASON);
            $bestTimeToContact      = $this->request->get(self::REQUEST_BEST_TIME_TO_CONTACT);
            $comment                = $this->request->get(self::REQUEST_COMMENT);
            $publicComment          = !!$this->request->get(self::REQUEST_PUBLIC_COMMENT);
            $removePublicCommentIds = $this->request->get(self::REQUEST_REMOVE_PUBLIC_COMMENTS, null);
            $excludedCompanies      = null;

            $assignments = $consumerProduct->productAssignment()->where(ProductAssignment::FIELD_DELIVERED, true)->get();

            if ($consumerProduct->cloned_from_id) {
                $original = $consumerProduct->clonedFrom;

                $assignments = $assignments->merge(
                    $original->productAssignment()->where(ProductAssignment::FIELD_DELIVERED, true)->get()
                );

                $clones = $original->clones;

                //one clone means just this current consumerProduct
                if ($clones->count() > 1) {
                    foreach ($clones as $clone) {
                        $productAssignments = $clone->productAssignment()->where(ProductAssignment::FIELD_DELIVERED, true)->get();

                        if ($productAssignments->isNotEmpty()) {
                            $assignments = $assignments->merge(
                                $clone->productAssignment()->where(ProductAssignment::FIELD_DELIVERED, true)->get()
                            );
                        }
                    }
                }

                $assignments = $assignments->unique();
            }

            if ($assignments->isNotEmpty()) {
                $excludedCompanies = $assignments->pluck(ProductAssignment::FIELD_COMPANY_ID)->toArray();
            }

            if ($request->validated(ApproveConsumerProductRequest::REQUEST_CLONE, false)) {
                $recycledLeadsRepository->createRecycledLeadForConsumerProduct($consumerProduct, RecycledLead::STATUS_SOLD);
                $consumerProduct = $recycledLeadsRepository->cloneConsumerProduct($consumerProduct, ConsumerProductChannel::REWORKED_AGED_LEADS);
                $productProcessingService->reserveProduct($consumerProduct, $this->getLeadProcessor());
            }

            if ($removePublicCommentIds) {
                $consumerProcessingActivityService = app(consumerProcessingActivityService::class);
                $consumerProcessingActivityService->modifyCommentScopes($consumerProduct, $removePublicCommentIds, ConsumerProcessingActivityVisibilityScope::INTERNAL);
            }

            return $this->formatResponse([
                "status" => $consumerProductRepository->updateConsumerProductStatus($consumerProduct, ConsumerProduct::STATUS_PENDING_ALLOCATION)
                    && $productProcessingService->approveProduct(
                        consumerProduct: $consumerProduct,
                        processor: $this->getLeadProcessor(),
                        reason: $reason,
                        bestTimeToContact: $bestTimeToContact,
                        comment: $comment,
                        publicComment: $publicComment,
                        excludedCompanies: $excludedCompanies
                    ) && $consumerProductRepository->markConsumerProductGoodToSell($consumerProduct)
            ]);
        }
        catch(Throwable $e) {
            $errLocation = $e->getFile().'. Line: '.$e->getLine().'. ';

            $productProcessingService->logStatus(
                $errLocation."Product approval err: ".substr($e->getMessage(), 0, 255),
                [
                    "consumer_product_id" => $consumerProduct->{ConsumerProduct::FIELD_ID},
                    "trace" => base64_encode(gzcompress($e->getTraceAsString(), 9))
                ]
            );

            throw $e;
        }
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param ProductProcessingService $productProcessingService
     *
     * @return JsonResponse
     */
    public function heartbeat(ConsumerProduct $consumerProduct, ProductProcessingService $productProcessingService): JsonResponse
    {
        if (!$this->isProductReservedByProcessor($consumerProduct, $productProcessingService))
            return $this->formatResponse(["status" => false]);

        $this->productProcessingService->heartbeat($consumerProduct, $this->getLeadProcessor());

        return $this->formatResponse([
            "status" => true
        ]);
    }

    public function updateStatusReason(ConsumerProduct $consumerProduct): JsonResponse
    {
        if (
            !$this->productProcessingService->isProductReservedByProcessor($consumerProduct, $this->getLeadProcessor())
            || !$consumerProduct->pendingReview
        ) {
            return $this->formatResponse(["status" => false]);
        }

        $validated = $this->request->validate([LeadProcessingPendingReview::FIELD_REASON => 'required|string|max:255']);

        return $this->formatResponse([
            "status" => $this->productProcessingRepository->updatePendingReviewReason($consumerProduct, $validated[LeadProcessingPendingReview::FIELD_REASON]),
        ]);
    }

    /**
     * @param LeadProcessingHistoryTransformer $leadProcessingHistoryTransformer
     * @return JsonResponse
     */
    public function getHistory(LeadProcessingHistoryTransformer $leadProcessingHistoryTransformer): JsonResponse
    {
        if (!($processor = $this->getLeadProcessor())) {
            return $this->formatResponse(["status" => false]);
        }

        $entries = $this->productProcessingRepository->getProcessorHistory($processor);

        return $this->formatResponse([
            "status"  => true,
            "history" => $leadProcessingHistoryTransformer->transformLeadProcessingHistoryEntries($entries)
        ]);
    }

    /**
     * Handles returning the correct queue for a given lead.
     *
     * @param ConsumerProduct|null $consumerProduct
     *
     * @return string
     */
    protected function getQueueForProduct(?ConsumerProduct $consumerProduct = null): string
    {
        if ($consumerProduct === null)
            return self::FALLBACK_QUEUE;

        return match ($consumerProduct->status) {
            ConsumerProduct::STATUS_UNDER_REVIEW => 'under_review',
            ConsumerProduct::STATUS_PENDING_REVIEW => "pending_review",
            ConsumerProduct::STATUS_INITIAL => 'initial',
            default => self::FALLBACK_QUEUE,
        };
    }


    /**
     * @param RefreshAgedLeadQueueRequest $request
     * @return JsonResponse
     */
    protected function refreshAgedQueue(RefreshAgedLeadQueueRequest $request): JsonResponse
    {
        $request->validated();

        // Should we do this sync?
        PopulateAgedQueueJob::dispatch();

        return $this->formatResponse([
            'status' => true
        ]);
    }
}
