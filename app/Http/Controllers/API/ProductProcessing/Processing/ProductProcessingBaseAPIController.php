<?php

namespace App\Http\Controllers\API\ProductProcessing\Processing;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Models\LeadProcessingConfiguration;
use App\Models\LeadProcessingTeam;
use App\Models\LeadProcessor;
use App\Repositories\Odin\ProductProcessing\ProductProcessingRepository;
use App\Services\Communication\CommunicationService;
use App\Services\Odin\ProductProcessing\ProductProcessingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ProductProcessingBaseAPIController extends APIController
{
    private LeadProcessor|null $cachedLeadProcessor = null;

    public function __construct(
        protected Request                     $request,
        protected JsonAPIResponseFactory      $apiResponseFactory,
        protected ProductProcessingRepository $productProcessingRepository,
        protected ProductProcessingService    $productProcessingService,
        protected CommunicationService        $communicationService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @return JsonResponse
     */
    public function getQueue(): JsonResponse
    {
        $processor = $this->getLeadProcessor();

        if (!$processor) {
            return $this->formatResponse([
                "status"       => false,
                "no_processor" => true
            ]);
        } else if ($processor->lead_processing_team_id === LeadProcessingTeam::NO_TEAM_ASSIGNED_ID) {
            return $this->formatResponse([
                "status"  => false,
                "no_team" => true
            ]);
        }

        return $this->formatResponse([
            "status" => true,
            "queue"  => $processor->team->primaryQueue->primary_status
        ]);
    }

    /**
     * @return LeadProcessor|null
     */
    protected function getLeadProcessor(): ?LeadProcessor
    {
        if (!$this->cachedLeadProcessor)
            $this->cachedLeadProcessor = $this->productProcessingRepository->getLeadProcessorByUserId(Auth::user()->id);

        return $this->cachedLeadProcessor;
    }

    /**
     * @return JsonResponse
     */
    public function getTimeConfigurations(): JsonResponse
    {
        $configs = $this->productProcessingRepository->getLeadProcessingConfiguration()?->toArray();

        return $this->formatResponse([
            "status"  => true,
            "configs" => [
                LeadProcessingConfiguration::FIELD_MINIMUM_REVIEW_TIME              => $configs[LeadProcessingConfiguration::FIELD_MINIMUM_REVIEW_TIME],
                LeadProcessingConfiguration::FIELD_CHECK_NEXT_LEAD_INTERVAL_SECONDS => $configs[LeadProcessingConfiguration::FIELD_CHECK_NEXT_LEAD_INTERVAL_SECONDS]
            ]
        ]);
    }

}