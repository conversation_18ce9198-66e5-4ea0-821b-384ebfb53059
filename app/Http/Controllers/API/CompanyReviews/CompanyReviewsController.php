<?php

namespace App\Http\Controllers\API\CompanyReviews;

use App\Http\Controllers\API\APIController;
use App\Http\Requests\CompanyReviewUpdateRequest;
use App\Http\Requests\Odin\GetCompanyReviewsRequest;
use App\Http\Resources\Reviews\ReviewResource;
use App\Models\Odin\CompanyReview;
use App\Repositories\CompanyReviewsRepository;
use App\Services\CompanyReviewsService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class CompanyReviewsController extends APIController
{
    public function __construct(
        Request $request,
        \App\Factories\JsonAPIResponseFactory $apiResponseFactory,
        private CompanyReviewsService $companyReviewsService,
        protected CompanyReviewsRepository $companyReviewsRepository,

    )
    {
        parent::__construct($request, $apiResponseFactory);
    }


    /**
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): \Illuminate\Http\Resources\Json\AnonymousResourceCollection
    {
        $perPage = $request->get('per_page') ?? 10;

        return ReviewResource::collection($this->companyReviewsService->all(true, $perPage));
    }

    /**
     * @param int $id
     * @param CompanyReviewUpdateRequest $companyReviewUpdateRequest
     * @return JsonResponse
     */
    public function update(int $id, CompanyReviewUpdateRequest $companyReviewUpdateRequest): JsonResponse
    {
        $data = $companyReviewUpdateRequest->safe()->only([
            CompanyReview::FIELD_STATUS,
            CompanyReview::FIELD_EMAIL_VALIDATED,
            CompanyReview::FIELD_PHONE_VALIDATED
        ]);

        $success = $this->companyReviewsService->update(
            $id,
            $data[CompanyReview::FIELD_EMAIL_VALIDATED] ?? null,
            $data[CompanyReview::FIELD_PHONE_VALIDATED] ?? null,
            $data[CompanyReview::FIELD_STATUS] ?? null
        );

        return $this->formatResponse([
            'status' => $success
        ]);
    }

    public function getStatuses(): JsonResponse
    {
        return $this->formatResponse([
            'status' => true,
            'statuses' => CompanyReview::STATUS_NAMES
        ]);
    }

    public function getCompanyReviews(GetCompanyReviewsRequest $request): AnonymousResourceCollection
    {
        $validated = $request->validated();
        $companyReviews = $this->companyReviewsRepository->getCompanyReviews($validated);
        return ReviewResource::collection($companyReviews);
    }
}
