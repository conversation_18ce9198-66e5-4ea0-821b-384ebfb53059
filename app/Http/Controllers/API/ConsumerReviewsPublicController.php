<?php

namespace App\Http\Controllers\API;

use App\DTO\ConsumerReviews\CreateReviewParam;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Requests\ConsumerReviews\CreateReviewRequest;
use App\Http\Requests\ConsumerReviews\CreateReviewWithAttachmentsRequest;
use App\Models\ConsumerReviews\Review;
use App\Models\ConsumerReviews\ReviewData;
use App\Models\Legacy\Location;
use App\Models\Odin\Company;
use App\Http\Requests\ConsumerReviews\VerifyEmailAddressRequest;
use App\Http\Requests\ConsumerReviews\VerifyPhoneRequest;
use App\Models\ConsumerReviews\Reviewer;
use App\Models\Odin\Website;
use App\Repositories\Odin\CompanyRepository;
use App\Repositories\Odin\CompanyUserRepository;
use App\Repositories\ReviewRepository;
use App\Services\ConsumerReviews\ReviewService;
use App\Services\Workflows\WorkflowEventService;
use App\Workflows\Shortcodes\ReviewIdShortcode;
use App\Workflows\Shortcodes\ReviewRatingShortcode;
use App\Services\Verification\CompanyUserVerificationService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Symfony\Component\Routing\Exception\ResourceNotFoundException;
use Twilio\Exceptions\TwilioException;

class ConsumerReviewsPublicController extends APIController
{
    const string REVIEW                     = 'review';
    const string RESPONSE_STATUS            = 'status';
    const string RESPONSE_MESSAGE           = 'message';
    const string EVENT_KEY                  = 'event';
    const string REQUEST_TYPE               = 'type';
    const string REQUEST_COMPANY            = 'company';
    const string REQUEST_COMPANIES          = 'companies';
    const string REQUEST_COMPANY_ID         = 'company_id';
    const string REQUEST_COMPANY_REFERENCE  = 'company_reference';
    const string RESPONSE_USER_REFERENCE    = 'user_reference';

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param ReviewRepository $reviewRepository
     * @param CompanyRepository $companyRepository
     * @param CompanyUserRepository $companyUserRepository
     * @param ReviewService $reviewService
     * @param CompanyUserVerificationService $verificationService
     * @param WorkflowEventService $workflowEventService
     */
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected ReviewRepository $reviewRepository,
        protected CompanyRepository $companyRepository,
        protected CompanyUserRepository $companyUserRepository,
        protected ReviewService $reviewService,
        protected CompanyUserVerificationService $verificationService,
        protected WorkflowEventService  $workflowEventService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param CreateReviewRequest $request
     * @return JsonResponse
     */
    public function createReview(
        CreateReviewRequest $request
    ): JsonResponse
    {
        $validated = $request->validated();

        /** @var CreateReviewParam $createReviewParam */
        $createReviewParam = CreateReviewParam::fromArray($validated);
        $company = $this->companyRepository->findByReferenceOrFail($createReviewParam->getCompanyReference());

        if ($this->checkDuplicateReview($createReviewParam)) {
            return $this->formatResponse([
                self::RESPONSE_STATUS  => false,
                self::RESPONSE_MESSAGE => 'DUPLICATE_REVIEW'
            ]);
        }

        $review = $this->reviewService->createReview($company, $createReviewParam);

        return $this->formatResponse([
            self::RESPONSE_STATUS   => (bool) $review,
            self::REVIEW            => $review
        ]);
    }

    /**
     * @param CreateReviewWithAttachmentsRequest $request
     * @return JsonResponse
     */
    public function createReviewWithAttachments(CreateReviewWithAttachmentsRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $files = $validated[CreateReviewWithAttachmentsRequest::KEY_ATTACHMENTS] ?? [];
        $reviewData = $validated[CreateReviewWithAttachmentsRequest::KEY_REVIEW_DATA];
        $origin = Website::query()
            ->where(Website::FIELD_ABBREVIATION, $reviewData[CreateReviewRequest::FIELD_REGISTRATION_ORIGIN])
            ->first();

        /** @var CreateReviewParam $createReviewParam */
        $createReviewParam = CreateReviewParam::fromArray($reviewData);
        $company = $this->companyRepository->findByReferenceOrFail($createReviewParam->getCompanyReference());

        $location = $this->getZipCodeLocation($createReviewParam->getZipCode());

        if (!$location) {
            return $this->formatResponse([
                self::RESPONSE_STATUS  => false,
                self::RESPONSE_MESSAGE => 'INVALID_ZIP_CODE',
            ]);
        }

        $createReviewParam->setDisplayLocation("$location->city, $location->state_abbr");
        $createReviewParam->setCompanyLocationId($this->findCompanyLocation($company, $reviewData[CreateReviewRequest::FIELD_COMPANY_LOCATION_INDEX] ?? null));

        if ($this->checkDuplicateReview($createReviewParam)) {
            //Unsure if we want to return an error to the frontend here.
            // It might just encourage someone to just resend it with a fake email address?
            // Returning a 200 with status => false for now, so the requesting service can decide whether to alert the user
            return $this->formatResponse([
                self::RESPONSE_STATUS  => false,
                self::RESPONSE_MESSAGE => 'DUPLICATE_REVIEW',
            ]);
        }

        $review = $this->reviewService->createReview($company, $createReviewParam);
        $uploadResponses = $this->reviewService->handleAttachments($files);

        if ($uploadResponses)
            $this->reviewRepository->updateReviewDataPayload($review->reviewData, [
                ReviewData::DATA_KEY_ATTACHMENTS => $uploadResponses,
            ]);

        if ($review?->reviewer)
                $this->reviewService->sendEmailVerification($review->reviewer, $review->uuid, $origin);

        return $this->formatResponse([
            self::RESPONSE_STATUS   => !!$review,
        ]);
    }

    /**
     * @param Review $review
     * @return JsonResponse
     */
    public function getReview(Review $review): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_STATUS   => (bool) $review,
            self::REVIEW            => $review
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function verifyZipCode(): JsonResponse
    {
        $zipCode = $this->request->get('zip_code', '');
        $location = $this->getZipCodeLocation($zipCode);

        return $this->formatResponse([
            self::RESPONSE_STATUS => !!$location,
            Location::STATE       => $location?->state_abbr,
            Location::CITY        => $location?->city,
        ]);
    }

    /**
     * @param Review $review
     * @return JsonResponse
     * @throws Exception
     */
    public function sendPhoneVerification(Review $review): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_STATUS           => $this->reviewService->sendPhoneVerification($review->reviewer),
            self::RESPONSE_USER_REFERENCE   => $review->reviewer?->{Reviewer::FIELD_REFERENCE},
        ]);
    }

    /**
     * @param Review $review
     * @param VerifyPhoneRequest $request
     * @return JsonResponse
     * @throws TwilioException
     */
    public function verifyPhone(Review $review, VerifyPhoneRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $verificationCode = Arr::get($validated, VerifyPhoneRequest::FIELD_VERIFICATION_CODE);

        $verified = $this->reviewService->verifyPhone($review->reviewer, $verificationCode, $review->uuid);

        if ($verified) {
            $this->triggerReviewWorkflowEvent($review, EventName::CREATED);
        }

        return $this->formatResponse([
            self::RESPONSE_STATUS => $verified,
        ]);
    }

    /**
     * @param Review $review
     * @return JsonResponse
     * @throws Exception
     */
    public function sendEmailVerification(Review $review): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_STATUS => $this->reviewService->sendEmailVerification($review->reviewer, $review->uuid),
        ]);
    }

    /**
     * @param Review $review
     * @param VerifyEmailAddressRequest $request
     * @return JsonResponse
     */
    public function verifyEmailAddress(Review $review, VerifyEmailAddressRequest $request): JsonResponse
    {
        $validated = $request->validated();
        if ($review->reviewer->is_email_verified)
            return $this->formatResponse([
                self::RESPONSE_STATUS => true
            ]);

        $token = Arr::get($validated, VerifyEmailAddressRequest::FIELD_TOKEN);

        $verified = $this->reviewService->verifyEmail($review->reviewer, $token, $review->uuid);

        if ($verified) {
            $this->triggerReviewWorkflowEvent($review, EventName::CREATED);
        }

        return $this->formatResponse([
            self::RESPONSE_STATUS => $verified
        ]);
    }

    public function getCompanyDetails(): JsonResponse
    {
        $reference = $this->request->get('reference');

        if (!$reference)
            throw new ResourceNotFoundException("A reference is required");

        $company = Company::query()
            ->select([
                Company::TABLE .'.'. Company::FIELD_REFERENCE,
                Company::TABLE .'.'. Company::FIELD_NAME,
            ])->where(Company::TABLE .'.'. Company::FIELD_REFERENCE, $reference)
            ->first();

        return $this->formatResponse([
            self::RESPONSE_STATUS => !!$company,
            self::REQUEST_COMPANY => $company->toArray(),
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function searchCompanies(): JsonResponse
    {
        $searchText = trim($this->request->get('search', ''));

        if ($searchText) {
            $companies = Company::query()
                ->select([
                    Company::TABLE .'.'. Company::FIELD_NAME,
                    Company::TABLE .'.'. Company::FIELD_REFERENCE
                ])->where(Company::FIELD_NAME, 'like', '%' . $searchText . '%')
                ->limit(20)
                ->get()
                ->toArray();
        }

        return $this->formatResponse([
            self::RESPONSE_STATUS   => $searchText,
            self::REQUEST_COMPANIES => $companies ?? [],
        ]);
    }

    /**
     * @param string $zipCode
     * @return Location|null
     */
    protected function getZipCodeLocation(string $zipCode): ?Location
    {
        return Location::query()
            ->select([Location::STATE_ABBREVIATION, Location::CITY])
            ->where(Location::ZIP_CODE, $zipCode)
            ->where(Location::TYPE, Location::TYPE_ZIP_CODE)
            ->first()
            ?? null;
    }

    /**
     * @param CreateReviewParam $params
     * @return bool
     */
    protected function checkDuplicateReview(CreateReviewParam $params): bool
    {
        $method = $params->getReviewerContactMethod();
        $value = $method === 'email'
            ? $params->getReviewerEmail()
            : $params->getReviewerPhone();
        $companyReference = $params->getCompanyReference();

        return $this->reviewService->checkForExistingReview($companyReference, $value, $method);
    }

    /**
     * Fires a workflow event for review
     *
     * @param Review $review
     * @param EventName $event
     * @return void
     */
    protected function triggerReviewWorkflowEvent(Review $review, EventName $event): void
    {
        $workflowPayload =
            [
                self::REQUEST_TYPE                          => EventCategory::REVIEWS->value,
                self::EVENT_KEY                             => $event->value,
                self::REQUEST_COMPANY_ID                    => $review->company->{Company::FIELD_ID},
                self::REQUEST_COMPANY_REFERENCE             => $review->company->{Company::FIELD_REFERENCE},
                ReviewIdShortcode::REVIEW_ID_KEY            => $review->{Review::FIELD_ID},
                ReviewRatingShortcode::REVIEW_RATING_KEY    => $review->reviewData->{ReviewData::FIELD_OVERALL_SCORE},
            ];

        $this->workflowEventService->handle(EventCategory::REVIEWS->value, $event->value, $workflowPayload);
    }

    /**
     * @param Company $company
     * @param string|int|null $locationIndex
     * @return int|null
     */
    protected function findCompanyLocation(Company $company, string|int|null $locationIndex): ?int
    {
        return $locationIndex === null
            ? null
            : $company->locations()
                ->offset($locationIndex)
                ->first()
                ?->id ?? null;
    }
}
