<?php

namespace App\Http\Controllers\API;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Requests\PingPostAffiliatesCreateLeadRequest;
use App\Jobs\ProcessPingPostAffiliateLeadJob;
use App\Services\Odin\PingPostAffiliateService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PingPostAffiliatesApiController extends APIController {

    public function __construct(
        Request                             $request,
        JsonAPIResponseFactory              $apiResponseFactory,
        protected PingPostAffiliateService  $pingPostAffiliateService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param PingPostAffiliatesCreateLeadRequest $request
     * @return JsonResponse
     */
    public function createPingPostAffiliateLead(PingPostAffiliatesCreateLeadRequest $request): JsonResponse
    {
        $requestData = $request->all();

        // Get API Key ID (Authentication Middleware stores in request)
        $apiKeyId = $request->get(PingPostAffiliatesCreateLeadRequest::API_KEY_ID);
        if (!$apiKeyId) {
            return $this->formatResponse([
                "status"    => false,
                "message"   => 'API Key Authentication Failed.',
            ]);
        }

        // Get Affiliate model
        $affiliate = $this->pingPostAffiliateService->getAffiliateFromApiKeyId($apiKeyId);
        $pingPostRequestModel = $this->pingPostAffiliateService->createRequestModel($affiliate->id, $apiKeyId, $request);

        // Validate new ping post lead
        $response = $this->pingPostAffiliateService->validateNewLead($requestData, $affiliate);

        // If validated, process new lead data
        if ($response['status']) {
            dispatch(new ProcessPingPostAffiliateLeadJob($requestData, $affiliate, $pingPostRequestModel));
        }

        // Store response and send back
        $statusCode = $response['status'] ? 200 : 500;
        $this->pingPostAffiliateService->setRequestResponse($pingPostRequestModel, $response);
        return $this->formatResponse($response, $statusCode);
    }
}
