<?php

namespace App\Http\Controllers\API\QAAutomation;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\QAAutomation\UpdateQAAutomationIndustryServiceRequest;
use App\Http\Requests\QAAutomation\UpdateQARulesRequest;
use App\Http\Resources\QAAutomation\QAAutomationIndustryServiceResource;
use App\Models\QAAutomation\QAAutomationIndustryService;
use App\Models\QAAutomation\QAAutomationRule;
use App\Repositories\QAAutomationRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class QAAutomationController extends APIController
{
    const string RESPONSE_INDUSTRY_SERVICE_CONFIGURATIONS = 'industry_service_configurations';
    const string RESPONSE_RULES = 'rules';

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param QAAutomationRepository $qaAutomationRepository
     */
    public function __construct(
        Request                                 $request,
        JsonAPIResponseFactory                  $apiResponseFactory,
        protected QAAutomationRepository        $qaAutomationRepository
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    public function getQAIndustryServiceConfigurations(): JsonResponse
    {
        $qAIndustryServices = $this->qaAutomationRepository->getQAIndustryServiceConfigurations();

        return $this->formatResponse([
            self::RESPONSE_INDUSTRY_SERVICE_CONFIGURATIONS => QAAutomationIndustryServiceResource::collection($qAIndustryServices)
        ]);
    }
    public function createQAIndustryServiceConfigurations(UpdateQAAutomationIndustryServiceRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $this->qaAutomationRepository->updateQAIndustryServiceConfiguration(
            industryServiceId: Arr::get($validated, UpdateQAAutomationIndustryServiceRequest::REQUEST_INDUSTRY_SERVICE_ID),
            enabled: Arr::get($validated, UpdateQAAutomationIndustryServiceRequest::REQUEST_ENABLED),
            type: Arr::get($validated, UpdateQAAutomationIndustryServiceRequest::REQUEST_TYPE),
        );

        return $this->formatResponse([], 204);
    }

    public function updateQAIndustryServiceConfigurations(int $id, UpdateQAAutomationIndustryServiceRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $this->qaAutomationRepository->updateQAIndustryServiceConfiguration(
            industryServiceId: Arr::get($validated, UpdateQAAutomationIndustryServiceRequest::REQUEST_INDUSTRY_SERVICE_ID),
            enabled: Arr::get($validated, UpdateQAAutomationIndustryServiceRequest::REQUEST_ENABLED),
            type: Arr::get($validated, UpdateQAAutomationIndustryServiceRequest::REQUEST_TYPE),
            qaIndustryServiceId: $id,
        );

        return $this->formatResponse([], 204);
    }

    public function deleteQAIndustryServiceConfigurations(int $id): JsonResponse
    {
        $QAAutomationIndustryService = $this->qaAutomationRepository->findByIdOrFail($id);
        $QAAutomationIndustryService->delete();

        return $this->formatResponse([], 204);
    }

    public function getQARules(): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_RULES => $this->qaAutomationRepository->getQARules()
        ]);
    }

    public function createQARule(UpdateQARulesRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $this->qaAutomationRepository->updateQARule(
            fields: Arr::get($validated, UpdateQARulesRequest::REQUEST_FIELDS),
            enabled: Arr::get($validated, UpdateQARulesRequest::REQUEST_ENABLED),
            matchSuccess: Arr::get($validated, UpdateQARulesRequest::REQUEST_MATCH_SUCCESS),
            expression: Arr::get($validated, UpdateQARulesRequest::REQUEST_EXPRESSION),
            type: Arr::get($validated, UpdateQARulesRequest::REQUEST_TYPE),
            data: Arr::get($validated, UpdateQARulesRequest::REQUEST_DATA),
        );
        return $this->formatResponse([], 204);
    }

    public function updateQARule(int $id, UpdateQARulesRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $this->qaAutomationRepository->updateQARule(
            fields: Arr::get($validated, UpdateQARulesRequest::REQUEST_FIELDS),
            enabled: Arr::get($validated, UpdateQARulesRequest::REQUEST_ENABLED),
            matchSuccess: Arr::get($validated, UpdateQARulesRequest::REQUEST_MATCH_SUCCESS),
            expression: Arr::get($validated, UpdateQARulesRequest::REQUEST_EXPRESSION),
            type: Arr::get($validated, UpdateQARulesRequest::REQUEST_TYPE),
            data: Arr::get($validated, UpdateQARulesRequest::REQUEST_DATA),
            qaRuleId: $id,
        );

        return $this->formatResponse([], 204);
    }

    public function deleteQARule(int $ruleId): JsonResponse
    {
        $this->qaAutomationRepository->deleteQARule($ruleId);
        return $this->formatResponse([], 204);
    }


}
