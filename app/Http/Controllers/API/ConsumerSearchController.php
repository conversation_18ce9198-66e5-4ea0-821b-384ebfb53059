<?php

namespace App\Http\Controllers\API;

use App\Builders\Odin\ConsumerSearchBuilder;
use App\Enums\UserPresetType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Requests\Odin\ConsumerSearchAggregatesRequest;
use App\Http\Requests\Odin\ConsumerSearchRequest;
use App\Http\Requests\StorePresetFilterRequest;
use App\Http\Resources\ConsumerSearchAggregatesResource;
use App\Http\Resources\Odin\ConsumerSearchResource;
use App\Http\Resources\UserPresetFilterResource;
use App\Models\User;
use App\Models\UserPreset;
use App\Repositories\Odin\ProductProcessing\ProductProcessingRepository;
use App\Repositories\UserPresetRepository;
use App\Services\Filterables\BaseFilterableService;
use App\Services\Filterables\Consumer\ConsumerFilterableService;
use App\Services\UserPresetService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\CollectsResources;
use Illuminate\Http\Resources\Json\PaginatedResourceResponse;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;

class ConsumerSearchController extends APIController
{
    use CollectsResources;

    const string MODULE_PERMISSION = 'consumer-search';

    const int DEFAULT_PER_PAGE = 10;

    const string RESPONSE_STATUS         = 'status';
    const string RESPONSE_CONSUMERS      = 'consumers';

    const string REQUEST_SEARCH_BY_ID   = 'search_id';
    const string REQUEST_SEARCH_BY_TEXT = 'search_text';
    const string REQUEST_PER_PAGE       = 'per_page';
    const string REQUEST_PAGE           = 'page';
    const string REQUEST_PRESET_VALUE   = 'value';
    const string REQUEST_NAME           = 'name';
    const string REQUEST_COMPANY_ID     = 'company_id';
    const string REQUEST_CAMPAIGN_IDS   = 'campaign_id';
    const string REQUEST_SOLD           = 'sold';

    protected ConsumerFilterableService $consumerFilterableService;

    public function __construct(
        Request                               $request,
        JsonAPIResponseFactory                $apiResponseFactory,
        protected UserPresetRepository        $userPresetRepository,
        protected UserPresetService           $userPresetService,
        protected ProductProcessingRepository $productProcessingRepository
    )
    {
        parent::__construct($request, $apiResponseFactory);

        $this->middleware(function ($request, $next) {
            $this->consumerFilterableService = new ConsumerFilterableService(
                authUser: $request->user(),
            );

            return $next($request);
        });
    }

    /**
     * @return JsonResponse
     */
    public function getFilterOptions(): JsonResponse
    {
        $options = $this->consumerFilterableService->getDisplayData();

        return $this->formatResponse([
            self::RESPONSE_STATUS                     => !!$options,
            BaseFilterableService::KEY_FILTER_OPTIONS => $options,
            BaseFilterableService::KEY_PRESETS        => $this->getConsumerPresetFilters(),
        ]);
    }

    /**
     * @param ConsumerSearchRequest $request
     * @return JsonResponse
     */
    public function getFilterOptionUpdates(ConsumerSearchRequest $request): JsonResponse
    {
        $filterOptions = $request->get(BaseFilterableService::KEY_FILTERS);

        $this->consumerFilterableService->runQuery($filterOptions);
        $filterUpdates = $this->consumerFilterableService->getFilterOptionUpdates();

        return $this->formatResponse([
            self::RESPONSE_STATUS                     => true,
            BaseFilterableService::KEY_FILTER_UPDATES => $filterUpdates ?? null
        ]);
    }

    /**
     * @param ConsumerSearchRequest $request
     * @return JsonResponse
     */
    public function search(ConsumerSearchRequest $request): JsonResponse
    {
        $filterOptions = $request->get(BaseFilterableService::KEY_FILTERS, []);
        $searchId      = $request->get(self::REQUEST_SEARCH_BY_ID);
        $searchText    = $request->get(self::REQUEST_SEARCH_BY_TEXT);
        $perPage       = $request->get(self::REQUEST_PER_PAGE, self::DEFAULT_PER_PAGE);
        $page          = $request->get(self::REQUEST_PAGE, 1);

        $companyId   = $request->get(self::REQUEST_COMPANY_ID);
        $campaignIds = $request->get(self::REQUEST_CAMPAIGN_IDS);
        $soldTo      = $request->get(self::REQUEST_SOLD);

        //leads older than 3 months cannot be fetched without permission
        if (!key_exists('consumer-date-range', $filterOptions)) {
            $filterOptions['consumer-date-range'] = ['from' => null, 'to' => null];
        }

        $query = ConsumerSearchBuilder::query()
            ->searchId($searchId)
            ->searchText($searchText)
            ->companyId($companyId)
            ->campaignIds($campaignIds)
            ->soldToCompany($soldTo)
            ->getQuery()
            ->orderBy(
                'created_at',
                $request->input(ConsumerSearchRequest::REQUEST_SORT_DATE) ?? 'desc'
            );

        $query = $this->consumerFilterableService->runQuery($filterOptions, $query);

        $filterUpdates = $this->consumerFilterableService->getFilterOptionUpdates();

        $paginated          = $query->paginate($perPage, ['*'], 'page', $page);
        $resourceCollection = ConsumerSearchResource::collection($paginated);
        $response           = new PaginatedResourceResponse($resourceCollection);

        return $this->formatResponse([
            self::RESPONSE_STATUS                     => true,
            self::RESPONSE_CONSUMERS                  => json_decode($response->toResponse($request)->content(), true),
            BaseFilterableService::KEY_FILTER_UPDATES => $filterUpdates ?? null
        ]);
    }

    /**
     * @param StorePresetFilterRequest $request
     * @return JsonResponse
     */
    public function savePreset(StorePresetFilterRequest $request): JsonResponse
    {
        $presetValue = $request->get(self::REQUEST_PRESET_VALUE);
        $name        = $request->get('name', 'User Filter');

        if (Arr::has($presetValue, 'consumer-date-range')) {
            $presetValue['consumer-date-range'] = $this->userPresetService->transformDateToDelta($presetValue['consumer-date-range']);
        }

        /** @var User $user */
        $user    = Auth::user();
        $success = !!$this->userPresetRepository->savePreset(
            $user->id,
            UserPresetType::FILTER,
            $name,
            $presetValue,
            ConsumerFilterableService::FILTERABLE_CATEGORY,
        );

        return $this->formatResponse([
            self::RESPONSE_STATUS              => $success,
            BaseFilterableService::KEY_PRESETS => $this->getConsumerPresetFilters(),
        ]);
    }

    /**
     * @return ResourceCollection
     */
    public function getConsumerPresetFilters(): ResourceCollection
    {
        /** @var User $user */
        $user    = Auth::user();
        $presets = $this->userPresetRepository->getPresetsByTypeAndCategory(UserPresetType::FILTER, ConsumerFilterableService::FILTERABLE_CATEGORY, $user->id)
            ->map(function (UserPreset $preset) {
                $filters = $preset->value;
                if (Arr::has($filters, 'consumer-date-range')) {
                    $filters['consumer-date-range'] = $this->userPresetService->transformDeltaToDate($filters['consumer-date-range']);
                    $preset->value                  = $filters;
                }
                return $preset;
            });

        return UserPresetFilterResource::collection($presets);
    }

    /**
     * @return JsonResponse
     */
    public function deletePreset(): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();
        $name = $this->request->get(self::REQUEST_NAME);

        $deleted = $this->userPresetRepository->deletePreset($user->id, UserPresetType::FILTER, $name, ConsumerFilterableService::FILTERABLE_CATEGORY);

        return $this->formatResponse([
            self::RESPONSE_STATUS              => $deleted,
            BaseFilterableService::KEY_PRESETS => $this->getConsumerPresetFilters(),
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function checkLeadProcessor(): JsonResponse
    {
        /** @var User $user */
        $userId        = Auth::user()->id;
        $leadProcessor = $this->productProcessingRepository->getLeadProcessorByUserId($userId);

        return $this->formatResponse(([
            'isLeadProcessor' => $leadProcessor !== null ? true : false,
        ]));
    }

    /**
     * @param ConsumerSearchAggregatesRequest $request
     * @return JsonResponse
     */
    public function getConsumerSearchAggregates(ConsumerSearchAggregatesRequest $request): JsonResponse
    {
        $filterOptions = $request->get(BaseFilterableService::KEY_FILTERS);
        $searchId      = $request->get(self::REQUEST_SEARCH_BY_ID);
        $searchText    = $request->get(self::REQUEST_SEARCH_BY_TEXT);

        $companyId   = $request->get(self::REQUEST_COMPANY_ID);
        $campaignIds = $request->get(self::REQUEST_CAMPAIGN_IDS);
        $sold = $request->get(self::REQUEST_SOLD);

        //leads older than 3 months cannot be fetched without permission
        if (!key_exists('consumer-date-range', $filterOptions)) {
            $filterOptions['consumer-date-range'] = ['from' => null, 'to' => null];
        }

        $query = ConsumerSearchBuilder::query()
            ->searchId($searchId)
            ->searchText($searchText)
            ->companyId($companyId)
            ->campaignIds($campaignIds)
            ->soldToCompany($sold)
            ->getAggregatesQuery();

        $aggregates = $this->consumerFilterableService->runQuery($filterOptions, $query)->first();

        return $this->formatResponse([
            'status' => true,
            'data'   => new ConsumerSearchAggregatesResource($aggregates),
        ]);
    }
}
