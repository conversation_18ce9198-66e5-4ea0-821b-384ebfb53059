<?php

namespace App\Http\Controllers\API;

use App\Factories\JsonAPIResponseFactory;
use App\Models\Odin\CompanyConfiguration;
use App\Repositories\CompanyConfigurationRepository;
use App\Repositories\Odin\CompanyRepository;
use App\Services\Companies\CompanyZipCodeExceptionService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * This should be updated to an A2 model once Solar is switched to future campaigns, and the legacy call removed
 */
class CompanyZipCodeExceptionsController extends APIController
{
    const string REQUEST_PARAM_UPDATE_PAYLOAD = 'payload';
    const string RESPONSE_EXCEPTIONS          = 'exceptions';
    const string RESPONSE_UNRESTRICTED        = 'unrestricted_zip_code_targeting';

    const string RESPONSE_STATUS              = 'status';

    public function __construct(
        Request                                  $request,
        JsonAPIResponseFactory                   $apiResponseFactory,
        protected CompanyRepository              $companyRepository,
        protected CompanyZipCodeExceptionService $zipCodeExceptionService,
        protected CompanyConfigurationRepository $companyConfigurationRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param int $company_id
     * @return JsonResponse
     * @throws Exception
     */
    public function get(int $company_id): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_EXCEPTIONS   => $this->zipCodeExceptionService->getCompanyExceptionsKeyedByState($company_id),
            self::RESPONSE_UNRESTRICTED => $company->configuration?->unrestricted_zip_code_targeting ?? false,
        ]);
    }

    /**
     * @param int $company_id
     * @return JsonResponse
     * @throws Exception
     */
    public function update(int $company_id): JsonResponse
    {
        $safePayload = $this->request->validate([self::REQUEST_PARAM_UPDATE_PAYLOAD => 'array']);
        $this->zipCodeExceptionService->updateExceptions($company_id, $safePayload[self::REQUEST_PARAM_UPDATE_PAYLOAD]);

        return $this->formatResponse();
    }

    /**
     * @return JsonResponse
     */
    public function toggleUnrestrictedZipCodeTargeting(): JsonResponse
    {
        $companyId = $this->request->route('company_id');
        $enable = $this->request->get(CompanyConfiguration::FIELD_UNRESTRICTED_ZIP_CODE_TARGETING);

        $updated = !!$this->companyConfigurationRepository->updateOrCreateCompanyConfiguration(
            companyId: $companyId,
            unrestrictedZipCodeTargeting: $enable,
        );

        return $this->formatResponse([
            self::RESPONSE_STATUS => $updated,
        ]);
    }
}
