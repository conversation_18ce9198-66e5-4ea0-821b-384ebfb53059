<?php

namespace App\Http\Controllers\API;

use App\Enums\UserPresetType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Requests\StorePresetFilterRequest;
use App\Http\Resources\UserPresetFilterResource;
use App\Models\User;
use App\Models\UserPreset;
use App\Repositories\Legacy\CompanyRepository as LegacyCompanyRepository;
use App\Repositories\Legacy\LegacyUserRepository;
use App\Repositories\Odin\CompanyRepository;
use App\Repositories\UserPresetRepository;
use App\Services\Filterables\CompanyServicingArea\CompanyServicingAreaFilterableService;
use App\Services\UserPresetService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Support\Facades\Auth;

class CompaniesServicingAreaController extends APIController
{
    const REQUEST_NAME = 'name';
    const REQUEST_PRESET_VALUE = 'value';

    const REQUEST_PER_PAGE = 'per_page';
    const REQUEST_PAGE = 'page';

    const RESPONSE_STATUS = 'status';
    const RESPONSE_FILTER_OPTIONS = 'filter_options';
    const RESPONSE_PRESETS = 'presets';

    /**
     * @param  Request  $request
     * @param  JsonAPIResponseFactory  $apiResponseFactory
     * @param  CompanyRepository  $companyRepository
     * @param  LegacyUserRepository  $legacyUserRepository
     * @param  LegacyCompanyRepository  $legacyCompanyRepository
     * @param  CompanyServicingAreaFilterableService  $CompanyServicingAreaFilterableService
     * @param  UserPresetRepository  $userPresetRepository
     * @param  UserPresetService  $userPresetService
     */
    public function __construct(
        protected Request $request,
        protected JsonApiResponseFactory $apiResponseFactory,
        public CompanyRepository $companyRepository,
        public LegacyUserRepository $legacyUserRepository,
        public LegacyCompanyRepository $legacyCompanyRepository,
        protected CompanyServicingAreaFilterableService $CompanyServicingAreaFilterableService,
        protected UserPresetRepository $userPresetRepository,
        protected UserPresetService $userPresetService,
    ) {
        parent::__construct($request, $apiResponseFactory);
    }

    public function getFilterOptions(): JsonResponse
    {
        $options = $this->CompanyServicingAreaFilterableService->getDisplayData();

        return $this->formatResponse([
            self::RESPONSE_STATUS => !!$options,
            self::RESPONSE_FILTER_OPTIONS => $options,
            self::RESPONSE_PRESETS => $this->getCompanyPresetFilters(),
        ]);
    }

    /**
     * @return ResourceCollection
     */
    public function getCompanyPresetFilters(): ResourceCollection
    {
        /** @var User $user */
        $user = Auth::user();
        $presets = $this->userPresetRepository->getPresetsByTypeAndCategory(UserPresetType::FILTER,
            CompanyServicingAreaFilterableService::FILTERABLE_CATEGORY, $user->id)
            ->map(function (UserPreset $preset) {
                $filters = $preset->value;
                return $preset;
            });

        return UserPresetFilterResource::collection($presets);
    }

    public function savePreset(StorePresetFilterRequest $request): JsonResponse
    {
        $presetValue = $request->get(self::REQUEST_PRESET_VALUE);
        $name = $request->get('name', 'User Filter');

        /** @var User $user */
        $user = Auth::user();
        $success = !!$this->userPresetRepository->savePreset(
            $user->id,
            UserPresetType::FILTER,
            $name,
            $presetValue,
            CompanyServicingAreaFilterableService::FILTERABLE_CATEGORY,
        );

        return $this->formatResponse([
            self::RESPONSE_STATUS => $success,
            self::RESPONSE_PRESETS => $this->getCompanyPresetFilters(),
        ]);
    }

    public function deletePreset(): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();
        $name = $this->request->get(self::REQUEST_NAME);

        $deleted = $this->userPresetRepository->deletePreset($user->id, UserPresetType::FILTER, $name,
            CompanyServicingAreaFilterableService::FILTERABLE_CATEGORY);

        return $this->formatResponse([
            self::RESPONSE_STATUS => $deleted,
            self::RESPONSE_PRESETS => $this->getCompanyPresetFilters(),
        ]);
    }
}
