<?php

namespace App\Http\Controllers\API;

use App\Builders\Odin\CompanyCommunicationsBuilder;
use App\Models\Odin\Company;
use App\Models\Odin\IndustryService;
use App\Models\Prospects\NewBuyerProspect;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class NewRegistrationsDashboardApiController extends APIController
{
    /**
     * @return array
     */
    private static function getAllManagerOptions(): array
    {
        $users = User::role(['business-development-manager', 'sales-development-representative']);
        return $users->pluck('id', 'name')->toArray();
    }

    public function getProspects(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 25);
        $page    = $request->get('page', 1);

        $sortField     = $request->get('sort_field', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');

        $query = DB::table('new_buyer_prospects')
            ->leftJoin('companies', 'new_buyer_prospects.company_id', '=', 'companies.id')
            ->leftJoin('company_user_relationships', 'companies.id', '=', 'company_user_relationships.company_id')
            ->leftJoin('users as company_user', 'company_user_relationships.user_id', '=', 'company_user.id')
            ->leftJoin('users as prospect_user', 'new_buyer_prospects.user_id', '=', 'prospect_user.id')
            ->where('new_buyer_prospects.source', 'registration')
            ->select([
                'new_buyer_prospects.*',
                DB::raw('if(new_buyer_prospects.company_id > 0, company_user.id, new_buyer_prospects.user_id) as final_user_id'),
                DB::raw('if(new_buyer_prospects.company_id > 0, company_user.name, prospect_user.name) as final_user_name')
            ])
            ->groupBy('new_buyer_prospects.company_id');

        // Filters
        $filterInputs = $request->get('filter_inputs', '{}');
        $filterInputs = json_decode($filterInputs, true);
        if (key_exists('manager', $filterInputs))
            $query->whereRaw('if(new_buyer_prospects.company_id > 0, company_user.id, new_buyer_prospects.user_id) = ?', [$filterInputs['manager']]);
        if (key_exists('resolution', $filterInputs))
            $query->where('new_buyer_prospects.resolution', $filterInputs['resolution']);


        switch ($sortField) {
            case 'manager_assigned':
                $query->orderBy('final_user_name', $sortDirection);
                break;

            case 'created_at':
                $query->orderBy('new_buyer_prospects.created_at', $sortDirection);
                break;

            case 'company_name':
                $query->orderBy('new_buyer_prospects.company_name', $sortDirection);
                break;

            case 'status':
                $query->orderBy('new_buyer_prospects.status', $sortDirection);
                break;

            default:
                $query->orderBy('new_buyer_prospects.' . $sortField, $sortDirection);
                break;
        }

        $paginated = $query->paginate($perPage, ['*'], 'page', $page);

        $paginated->setCollection(
            $paginated->getCollection()->map(function ($prospect) {
                $prospectArray = (array) $prospect;

                $company = null;
                if (!empty($prospectArray['company_id'])) {
                    $company = Company::find($prospectArray['company_id']);
                }

                $prospectArray['last_contact_date'] = $this->getLastContactDateForCompany($company);

                return (object) $prospectArray;
            })
        );

        return response()->json([
            'results' => $paginated,
            'filterOptions' => [
                'managers' => self::getAllManagerOptions()
            ]
        ]);
    }

    /**
     * @param Company|null $company
     * @return Carbon|null
     */
    private function getLastContactDateForCompany(?Company $company): ?string
    {
        if (!$company)
            return null;

        $communications = app(CompanyCommunicationsBuilder::class);

        // todo: N+1 - performance drain
        return $communications->forCompany($company)->first()?->contact_date;
    }

}
