<?php

namespace App\Http\Controllers\API\OpportunityNotifications;

use App\Enums\OpportunityNotifications\OpportunityNotificationConfigType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\OpportunityNotifications\DestroyOpportunityNotificationConfigRequest;
use App\Http\Requests\OpportunityNotifications\OpportunityNotificationConfigRequest;
use App\Http\Requests\OpportunityNotifications\SearchOpportunityNotificationOptionsRequest;
use App\Http\Requests\OpportunityNotifications\StoreOpportunityNotificationConfigRequest;
use App\Http\Resources\OpportunityNotifications\GetCompanyCountPreviewResource;
use App\Http\Resources\OpportunityNotifications\OpportunityNotificationConfigResource;
use App\Jobs\OpportunityNotifications\ProcessCampaignSummaryOpportunityNotificationsQueueJob;
use App\Models\EmailTemplate;
use App\Models\MissedProducts\OpportunityNotificationConfig;
use App\Models\Odin\Company;
use App\Models\UserPreset;
use App\Repositories\MissedProducts\OpportunityNotificationConfigRepository;
use App\Services\OpportunityNotifications\OpportunityNotificationCompanyFilterableService;
use App\Services\OpportunityNotifications\OpportunityNotificationService;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class OpportunityNotificationsConfigApiController extends APIController
{
    const REQUEST_RULESET = 'ruleset';

    public function __construct(
        Request                                    $request,
        JsonAPIResponseFactory                     $apiResponseFactory,
        protected OpportunityNotificationConfigRepository $notificationConfigRepository,
        protected OpportunityNotificationService $notificationService
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param OpportunityNotificationConfigRequest $request
     * @return JsonResponse
     */
    public function index(OpportunityNotificationConfigRequest $request): JsonResponse
    {
        session_write_close();
        $searchParams = $request->safe()->collect();

        return $this->formatResponse([
            "status" => true,
            "configs_paginated" => $this->notificationConfigRepository->getConfigsWithSearchFilters(
                name: $searchParams->get(OpportunityNotificationConfig::FIELD_NAME),
                maxFrequency: $searchParams->get(OpportunityNotificationConfig::FIELD_MAXIMUM_SEND_FREQUENCY),
                sendTime: $searchParams->get(OpportunityNotificationConfig::FIELD_SEND_TIME),
                leadThreshold: $searchParams->get(OpportunityNotificationConfig::FIELD_LEAD_THRESHOLD),
                active: $searchParams->get(OpportunityNotificationConfig::FIELD_ACTIVE),
                sortCol: $searchParams->get(OpportunityNotificationConfigRequest::REQUEST_SORT_COL),
                sortDir: $searchParams->get(OpportunityNotificationConfigRequest::REQUEST_SORT_DIR),
                countingRelations: [OpportunityNotificationConfig::RELATION_NOTIFICATIONS],
            )
                ->paginate(
                    $searchParams->get(
                        OpportunityNotificationConfigRequest::REQUEST_PER_PAGE),
                    ['*'],
                    'page',
                    $searchParams->get(OpportunityNotificationConfigRequest::REQUEST_PAGE)
                )
                ->through(fn(OpportunityNotificationConfig $config) => new OpportunityNotificationConfigResource($config))
        ]);
    }

    /**
     * Return opn configurations filtered by name as select options
     * @param SearchOpportunityNotificationOptionsRequest $request
     * @return JsonResponse
     */
    public function searchOptions(SearchOpportunityNotificationOptionsRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $name = $validated[SearchOpportunityNotificationOptionsRequest::REQUEST_NAME];

        return $this->formatResponse([
            "status" => true,
            "configurations" => !empty($name) ? $this->notificationConfigRepository->getConfigsWithSearchFilters($name)
                ->limit(10)
                ->get()
                ->map(fn(OpportunityNotificationConfig $config) => [
                    'id' => $config->{OpportunityNotificationConfig::FIELD_ID},
                    'name' => $config->{OpportunityNotificationConfig::FIELD_NAME}
                ]) : []
        ]);
    }

    /**
     * @param StoreOpportunityNotificationConfigRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function addOpportunityNotificationConfig(StoreOpportunityNotificationConfigRequest $request): JsonResponse
    {
        $data = $request->safe()->toArray();
        $data[OpportunityNotificationConfig::FIELD_EXPIRES_AT] = new Carbon($data[OpportunityNotificationConfig::FIELD_EXPIRES_AT]);
        $this->addUserToPayload($data, true);

        $type = OpportunityNotificationConfigType::tryFrom($data[OpportunityNotificationConfig::FIELD_TYPE]);
        if ($type === OpportunityNotificationConfigType::BDM_COMPANIES && OpportunityNotificationConfig::query()->where(OpportunityNotificationConfig::FIELD_TYPE, OpportunityNotificationConfigType::BDM_COMPANIES)->exists())
            throw new Exception("Only one BDM notification config can exist.");

        $config = $this->notificationConfigRepository->createNotificationConfig($data);

        return $this->formatResponse([
            "status" => true,
            "config" => new OpportunityNotificationConfigResource($config)
        ]);
    }

    /**
     * @param OpportunityNotificationConfig $notificationConfig
     * @param StoreOpportunityNotificationConfigRequest $request
     * @return JsonResponse
     */
    public function updateOpportunityNotificationConfig(
        OpportunityNotificationConfig $notificationConfig,
        StoreOpportunityNotificationConfigRequest $request
    ): JsonResponse {
        $data = $request->safe()->toArray();
        $this->addUserToPayload($data);
        if ($data[OpportunityNotificationConfig::FIELD_EXPIRES_AT] ?? null)
            $data[OpportunityNotificationConfig::FIELD_EXPIRES_AT] = new Carbon($data[OpportunityNotificationConfig::FIELD_EXPIRES_AT]);

        $config = $this->notificationConfigRepository->updateNotificationConfig($notificationConfig, $data);

        return $this->formatResponse([
            "status" => true,
            "config" => new OpportunityNotificationConfigResource($config)
        ]);
    }


    /**
     * @param OpportunityNotificationConfig $notificationConfig
     * @param DestroyOpportunityNotificationConfigRequest $request
     * @return JsonResponse
     */
    public function deleteOpportunityNotificationConfig(
        OpportunityNotificationConfig $notificationConfig,
        DestroyOpportunityNotificationConfigRequest $request
    ): JsonResponse {
        return $this->formatResponse([
            "status" => !!$this->notificationConfigRepository->deleteNotificationConfig($notificationConfig),
        ]);
    }


    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getEmailTemplateOptions(
        Request $request
    ): JsonResponse {
        try {
            return $this->formatResponse([
                "status" => true,
                "options" => $this->notificationConfigRepository->getEmailTemplateOptions(),
            ]);
        } catch (Exception $exception) {
            return $this->formatResponse([
                "message" => $exception->getMessage(),
            ]);
        }
    }


    /**
     * @param OpportunityNotificationConfig $notificationConfig
     * @param OpportunityNotificationService $opportunityNotificationService
     * @return JsonResponse
     */
    public function sendEmails(OpportunityNotificationConfig $notificationConfig, OpportunityNotificationService $opportunityNotificationService): JsonResponse {
        $config = $this->request->route('notificationConfig');
        if ($config->type === OpportunityNotificationConfigType::BDM_COMPANIES) {
            $company = Company::query()->findOrFail($this->request->get('company_id'));
            $result = $opportunityNotificationService->sendNotification([$company->users()->first()->id], $company->id, $config->id, true);
        }
        else {
            $result = ProcessCampaignSummaryOpportunityNotificationsQueueJob::dispatch(Collection::make([$notificationConfig]), true, 10);
        }

        return $this->formatResponse([
            "status" => $result,
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getTestEmailRecipients(Request $request): JsonResponse {
        return $this->formatResponse([
            "status" => true,
            "recipients" => config('app.outgoing_communication.test_email')
        ]);
    }

    /**
     * Return the count of companies that will receive the notification
     * @param UserPreset $filterPreset
     * @return JsonResponse
     * @throws Exception
     */
    public function getCompanyCountPreview(UserPreset $filterPreset): JsonResponse
    {
        /** @var OpportunityNotificationCompanyFilterableService $service */
        $service = app(OpportunityNotificationCompanyFilterableService::class);
        $query = $service->query($filterPreset);
        $count = $query->exists() ? $query->count() : 0;
        $companies = $query->limit(5)->get();

        return $this->formatResponse([
            "status"            => true,
            "total_companies"   => $count,
            "preview_data"     => GetCompanyCountPreviewResource::collection($companies)
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getEmailTemplatePreview(Request $request): JsonResponse
    {
        $companyUserId = $request->get('company_user_id');
        $opportunityConfigurationId = $request->get('opportunity_configuration_id');

        return $this->formatResponse([
            "status" => true,
            "preview_content" => $this->notificationService->generateEmailTemplatePreview($companyUserId, $opportunityConfigurationId)
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getConfigTypes(): JsonResponse
    {
        return $this->formatResponse([
            "status" => true,
            "config" => OpportunityNotificationConfigType::getFrontendData(),
        ]);
    }

        /**
     * @param array $payload
     * @param bool $withCreatedBy
     * @return void
     */
    protected function addUserToPayload(array &$payload, bool $withCreatedBy = false): void
    {
        $user = Auth::user();
        $payload[OpportunityNotificationConfig::FIELD_UPDATED_BY] = $user->id;
        if ($withCreatedBy)
            $payload[OpportunityNotificationConfig::FIELD_CREATED_BY] = $user->id;
    }
}
