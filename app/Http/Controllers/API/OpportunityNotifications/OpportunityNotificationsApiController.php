<?php

namespace App\Http\Controllers\API\OpportunityNotifications;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Odin\GetMissedLeadsByCompanyRequest;
use App\Http\Requests\OpportunityNotifications\OpportunityNotificationRequest;
use App\Http\Requests\OpportunityNotifications\TestOpportunityRequest;
use App\Http\Resources\MissedProducts\CompanyMissedProductResource;
use App\Http\Resources\OpportunityNotifications\OpportunityNotificationResource;
use App\Models\MissedProducts\MissedProduct;
use App\Models\MissedProducts\OpportunityNotification;
use App\Models\Odin\Company;
use App\Models\Ruleset;
use App\Repositories\MissedProductRepository;
use App\Repositories\MissedProducts\OpportunityNotificationRepository;
use App\Services\Odin\CompanyRulesetService;
use App\Services\OpportunityNotifications\OpportunityNotificationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;

class OpportunityNotificationsApiController extends APIController
{
    const string RESPONSE_COUNT    = 'count';
    const string RESPONSE_STATUS   = 'status';
    const string RESPONSE_PRODUCTS = 'products';

    public function __construct(
        Request                                     $request,
        JsonAPIResponseFactory                      $apiResponseFactory,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param OpportunityNotificationRequest $request
     * @param OpportunityNotificationRepository $notificationRepository
     * @return JsonResponse
     */
    public function index(OpportunityNotificationRequest $request, OpportunityNotificationRepository $notificationRepository): JsonResponse
    {
        session_write_close();
        $searchParams = $request->safe()->collect();

        return $this->formatResponse([
            "status" => true,
            "notifications_paginated" => $notificationRepository->getNotificationsWithSearchFilters(
                $searchParams->get(OpportunityNotificationRequest::REQUEST_RECIPIENTS),
                $searchParams->get(OpportunityNotificationRequest::REQUEST_CONTENT),
                $searchParams->get(OpportunityNotificationRequest::REQUEST_DELIVERY_METHOD),
                $searchParams->get(OpportunityNotificationRequest::REQUEST_SENT_AT),
                $searchParams->get(OpportunityNotificationRequest::REQUEST_COMPANY_NAME),
                $searchParams->get(OpportunityNotificationRequest::REQUEST_SORT_COL),
                $searchParams->get(OpportunityNotificationRequest::REQUEST_SORT_DIR),
                $searchParams->get(OpportunityNotificationRequest::REQUEST_CONFIGURATION_ID),
            )
            ->paginate(
                $searchParams->get(
                    OpportunityNotificationRequest::REQUEST_PER_PAGE),
                ['*'],
                'page',
                $searchParams->get(OpportunityNotificationRequest::REQUEST_PAGE)
            )
            ->through(fn(OpportunityNotification $notification) => new OpportunityNotificationResource($notification))
        ]);
    }

    /**
     * Route created to test the ruleset
     * @param TestOpportunityRequest $request
     * @param CompanyRulesetService $companyRulesetService
     * @return JsonResponse
     */
    public function testRuleset(TestOpportunityRequest $request, CompanyRulesetService $companyRulesetService): JsonResponse
    {
        $payload = $request->validated();
        $ruleset = new Ruleset($payload['ruleset']);

        $companies = $companyRulesetService->getOpportunityNotificationRulesetQuery($ruleset)
            ->limit(10)
            ->get()
            ->map(fn(Company $company) => [
                "id"   => $company->id,
                "name" => $company->name
            ]);

        return $this->formatResponse([
            "status" => true,
            "companies" => $companies
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function unsubscribeUser(): JsonResponse
    {
        $reference = $this->request->get('contact_ref');
        /** @var OpportunityNotificationService $service */
        $service = app(OpportunityNotificationService::class);

        return $this->formatResponse([
            self::RESPONSE_STATUS => $reference && $service->unsubscribeUser($reference),
        ]);
    }

    /**
     * @param MissedProductRepository $missedProductRepository
     * @return JsonResponse
     */
    public function getAvailableMissedProductCount(MissedProductRepository $missedProductRepository): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_STATUS => true,
            self::RESPONSE_COUNT  => $missedProductRepository->getMissedProductsForExistingCompanyQuery($this->getCompanyFromRoute()->id)->count(),
        ]);
    }

    /**
     * @param GetMissedLeadsByCompanyRequest $request
     * @param MissedProductRepository $missedProductRepository
     * @return JsonResponse
     */
    public function getCompanyMissedProducts(GetMissedLeadsByCompanyRequest $request, MissedProductRepository $missedProductRepository): JsonResponse
    {
        $company = Company::query()
            ->findOrFail($request->route('company_id'));
        $filterOptions = $request->safe()->toArray();
        $from = new Carbon($filterOptions[GetMissedLeadsByCompanyRequest::FIELD_DATE_RANGE][0]) ?? null;
        $products = $missedProductRepository->getMissedProductsForExistingCompanyQuery($company->id, $from)
            ->paginate(
                perPage: $filterOptions[GetMissedLeadsByCompanyRequest::FIELD_PER_PAGE] ?? 25,
                page: $filterOptions[GetMissedLeadsByCompanyRequest::FIELD_PAGE] ?? 1
            )->through(fn(MissedProduct $product) => new CompanyMissedProductResource($product));

        return $this->formatResponse([
            self::RESPONSE_STATUS   => true,
            self::RESPONSE_PRODUCTS => $products,
        ]);
    }

    /**
     * @return Company
     */
    protected function getCompanyFromRoute(): Company
    {
        /** @var Company */
        return Company::query()
            ->findOrFail($this->request->route('companyId'));
    }
}
