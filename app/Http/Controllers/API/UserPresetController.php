<?php

namespace App\Http\Controllers\API;

use App\Enums\UserPresetType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Resources\UserPresetResource;
use App\Repositories\UserPresetRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\CollectsResources;

class UserPresetController extends APIController
{
    use CollectsResources;

    const FIELD_STATUS = 'status';
    const FIELD_PRESETS = 'presets';

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected UserPresetRepository     $userPresetRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    public function getFilterPresets(Request $request): JsonResponse
    {
        $category = $request->get('category');

        $presets = $this->userPresetRepository
            ->getPresetsByTypeAndCategory(UserPresetType::FILTER, $category);

        return $this->formatResponse([
            self::FIELD_STATUS => true,
            self::FIELD_PRESETS => UserPresetResource::collection($presets)
        ]);
    }
}
