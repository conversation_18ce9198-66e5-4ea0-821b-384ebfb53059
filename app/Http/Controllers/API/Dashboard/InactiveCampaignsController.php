<?php

namespace App\Http\Controllers\API\Dashboard;

use App\Enums\Campaigns\CampaignStatus;
use App\Enums\Company\CompanySystemStatus;
use App\Http\Controllers\Controller;
use App\Http\Resources\Dashboard\InactiveCampaignsResource;
use App\Models\Campaigns\CampaignReactivation;

class InactiveCampaignsController extends Controller
{
    public function __invoke()
    {
        $selects = collect([
            'campaign_reactivations.id as id',
            'campaign_reactivations.reason as reason',
            'campaign_reactivations.created_at as since',
            'campaign_reactivations.reactivate_at as reactivates',
            'company_campaigns.id as campaign_id',
            'company_campaigns.name as campaign_name',
            'company_campaigns.status as type',
            'companies.id as company_id',
            'companies.name as company_name',
            'COALESCE(SUM(product_assignments.cost), 0) as spend',
        ]);

        return InactiveCampaignsResource::collection(
            CampaignReactivation::selectRaw($selects->implode(', '))
                ->join('company_campaigns', 'campaign_reactivations.campaign_id', 'company_campaigns.id')
                ->join('companies', 'company_campaigns.company_id', 'companies.id')
                ->leftJoin('budget_containers', 'company_campaigns.id', 'budget_containers.company_campaign_id')
                ->leftJoin('budgets', 'budget_containers.id', 'budgets.budget_container_id')
                ->leftJoin('product_assignments', function ($join) {
                    $join->on('budgets.id', 'product_assignments.budget_id')
                        ->whereChargeable(true)
                        ->whereDelivered(true);
                    // ->whereBetween('product_assignments.delivered_at', now()->subWeek(), now());
                })->where('companies.system_status', CompanySystemStatus::ELIGIBLE)
                ->whereIn('company_campaigns.status', [CampaignStatus::PAUSED_PERMANENTLY, CampaignStatus::PAUSED_TEMPORARILY])
                ->whereIn('companies.id', auth()->user()->accountManagerCompanies->pluck('id'))
                ->groupBy('campaign_id')
                ->get()
                ->sortBy([
                    ['spend', 'desc'],
                    ['type', 'asc'],
                    ['since', 'asc'],
                ])
        );
    }
}
