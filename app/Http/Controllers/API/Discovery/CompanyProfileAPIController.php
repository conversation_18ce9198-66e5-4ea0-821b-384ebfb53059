<?php

namespace App\Http\Controllers\API\Discovery;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Discovery\ListTopCompaniesRequest;
use App\Http\Requests\Discovery\StoreCompanyProfileRequest;
use App\Http\Resources\CompanyProfile\CompanyListingResource;
use App\Http\Resources\CompanyProfile\CompanyProfileResource;
use App\Repositories\CompanyProfile\CompanyProfileRepository;
use App\Services\CompanyProfile\CompanyProfileSyncService;
use App\Services\CompanyProfile\TopCompanyProfileService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class CompanyProfileAPIController extends APIController
{
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected CompanyProfileSyncService $companyProfileSyncService,
        protected CompanyProfileRepository $companyProfileRepository,
        protected TopCompanyProfileService $topCompanyProfileService
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    public function listTopCompanies(ListTopCompaniesRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $companies = $this->topCompanyProfileService->listTopCompanyProfiles(
            industryServiceSlug   : Arr::get($validated, ListTopCompaniesRequest::FIELD_INDUSTRY_SERVICE_SLUG),
            countryAbbreviation   : Arr::get($validated, ListTopCompaniesRequest::FIELD_COUNTRY_ABBREVIATION),
            stateAbbreviation     : Arr::get($validated, ListTopCompaniesRequest::FIELD_STATE_ABBREVIATION),
            countyKey             : Arr::get($validated, ListTopCompaniesRequest::FIELD_COUNTY_KEY),
            prioritizedCompanyIds: Str::of(Arr::get($validated, ListTopCompaniesRequest::FIELD_PRIORITIZED_COMPANY_IDS, ''))
                ->explode(',')
                ->unique()
                ->filter()
                ->toArray(),
        );

        return response()->json([
            'companies' => CompanyListingResource::collection($companies)
        ]);
    }

    public function store(StoreCompanyProfileRequest $request): JsonResponse
    {
        $companyProfileDTO = $request->validated();

        $this->companyProfileSyncService->execute(profileDTO: $companyProfileDTO);

        //todo: Call FIXR.com service to create pages

        return response()->json();
    }

    public function show(string $profileSlug): JsonResponse
    {
        $profile = $this->companyProfileRepository->find(
            profileSlug: $profileSlug,
            published  : true,
        );

        return $this->formatResponse([
            'status'  => (bool)$profile,
            'profile' => $profile ? new CompanyProfileResource($profile) : null,
        ]);
    }

}
