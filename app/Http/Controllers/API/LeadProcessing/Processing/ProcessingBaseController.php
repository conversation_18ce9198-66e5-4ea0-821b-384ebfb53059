<?php

namespace App\Http\Controllers\API\LeadProcessing\Processing;

use App\Enums\Odin\VerificationServiceTypes;
use App\Http\Controllers\API\LeadProcessing\LeadProcessingBaseAPIController;
use App\Jobs\CreateInitialLead;
use App\Models\LeadProcessingConfiguration;
use App\Models\LeadProcessingTeam;
use App\Models\Legacy\EloquentQuote;
use App\Models\Odin\ConsumerProduct;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Repositories\OfficeHoursRepository;
use App\Services\LeadVerification\LeadVerificationServiceFactory;
use App\Services\Odin\ConsumerProductVerification\ConsumerProductVerificationServiceFactory;
use App\Transformers\LeadProcessing\LeadProcessingHistoryTransformer;
use App\Transformers\LeadProcessing\LeadProcessingTransformer;
use App\Transformers\Odin\ConsumerProductVerificationTransformer;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;
use Throwable;

/**
 * The base controller that all controllers in the processing api should inherit.
 */
class ProcessingBaseController extends LeadProcessingBaseAPIController
{
    const MODULE_PERMISSION = "lead-processing";

    const REQUEST_REASON               = 'reason';
    const REQUEST_BEST_TIME_TO_CONTACT = 'best_time_to_contact';
    const REQUEST_DATA                 = 'data';
    const REQUEST_PUBLIC_COMMENT       = 'public_comment';
    const REQUEST_STATUS_REASON        = 'status_reason';
    const REQUEST_BODY                 = 'body';
    const REQUEST_PREVIOUS_LEAD_ID     = 'previous_lead_id';
    const REQUEST_SPECIFIC_LEAD_ID     = 'specific_lead_id';

    const REQUEST_UUID = 'uuid';

    const LAST_LEAD_CREATION_TIMESTAMP = 'last_lead_creation_timestamp';

    const FALLBACK_QUEUE = 'display';

    /**
     * Facilitates the retrieval of getting the next lead in the queue.
     * @param LeadProcessingTransformer $transformer
     * @return JsonResponse
     * @throws Exception
     */
    public function getNextLead(LeadProcessingTransformer $transformer): JsonResponse
    {
        $previousLeadId = $this->request->input(self::REQUEST_PREVIOUS_LEAD_ID, null);
        $specificLeadId = $this->request->input(self::REQUEST_SPECIFIC_LEAD_ID, null);
        $processor = $this->getLeadProcessor();

        $lead = $this->leadProcessingService->getNextLead($processor, $specificLeadId, $previousLeadId);

        if(!empty($specificLeadId)
        && $lead === null) {
            return $this->formatResponse([
                "status" => false,
                "lead"   => null,
                "retry"  => false,
                "msg"    => "Could not retrieve specific lead. It may be locked by someone else."
            ]);
        }

        $retry = false;
        if ($lead !== null) {
            $retry = !$this->leadProcessingService->reserveLead($lead, $processor);
            if ($retry) {
                $lead = null;
            }
        }

        return $this->formatResponse([
            "status" => true,
            "lead"   => $lead ? $transformer->transformLead($lead, $specificLeadId ? $this->getQueueForLead($lead) : $processor->team->primaryQueue->primary_status) : null,
            "retry"  => $retry
        ]);
    }

    /**
     * Returns the lead basic data
     *
     * @param int $id
     * @param LeadProcessingTransformer $transformer
     * @return JsonResponse
     */
    public function getLeadBasicInfo(int $id, LeadProcessingTransformer $transformer): JsonResponse
    {
        $this->performAuthorizationCheck(self::MODULE_PERMISSION);

        /** @var EloquentQuote $lead */
        $lead = EloquentQuote::query()->where(EloquentQuote::ID, $id)->firstOrFail();

        return $this->formatResponse([
            "status" => true,
            "lead"   => $transformer->formatBasicData($lead, "")
        ]);
    }

    /**
     * TODO: this method will be no more in use once we're fully relying on the new model structure everywhere i-e consumer/product instead of lead
     * and for that, we've already got an API built based on the new model structure in the consumer controller as `getProductVerification`
     *
     * @param int $id
     * @param string $service
     * @param LeadProcessingTransformer $transformer
     * @return JsonResponse
     * @throws Exception
     */
    public function getLeadVerification(int $id, string $service, LeadProcessingTransformer $transformer): JsonResponse
    {
        $this->performAuthorizationCheck(self::MODULE_PERMISSION);

        $lead = EloquentQuote::query()->findOrFail($id);

        $verificationData = LeadVerificationServiceFactory::makeService($service)->verifyLead($lead);

        return $this->formatResponse([
            "status" => true,
            "verification_data" => $transformer->formatLeadVerification($service, $verificationData)
        ]);
    }

    /**
     * Returns the related activity data for the requested lead.
     *
     * @param int $id
     * @param LeadProcessingTransformer $transformer
     * @return JsonResponse
     */
    public function getLeadRelatedActivity(int $id, LeadProcessingTransformer $transformer): JsonResponse
    {
        $this->performAuthorizationCheck(self::MODULE_PERMISSION);

        return $this->formatResponse([
            "status"             => true,
            "related_activity"   => $transformer->formatRelatedActivity($this->leadRepository->findOrFail($id))
        ]);
    }

    /**
     * Handles returning the correct queue for a given lead.
     *
     * @param EloquentQuote|null $lead
     * @return string
     */
    protected function getQueueForLead(?EloquentQuote $lead = null): string
    {
        if ($lead === null)
            return self::FALLBACK_QUEUE;

        switch ($lead->status) {
            case EloquentQuote::VALUE_STATUS_UNDER_REVIEW:
                return 'under_review';

            case EloquentQuote::VALUE_STATUS_INITIAL:
                if ($lead->pendingReview !== null)
                    return "pending_review";

                return "initial";
        }

        return self::FALLBACK_QUEUE;
    }

    /**
     * Facilitates the cancelling of a lead.
     *
     * @param int $leadId
     * @return JsonResponse
     * @throws Exception
     */
    public function cancel(int $leadId): JsonResponse
    {
        if (!$this->isLeadReservedByProcessor($lead = $this->leadRepository->findOrFail($leadId))) {
            return $this->formatResponse(["status" => false, "msg" => "You have not locked this lead. Please refresh the page and try again."]);
        }

        $reason = $this->request->get(self::REQUEST_REASON, null);
        $comment = $this->request->get(self::REQUEST_PUBLIC_COMMENT, null);

        return $this->formatResponse([
            "status" => $this->leadProcessingService->cancelLead($lead, $this->getLeadProcessor(), $reason, $comment)
        ]);
    }

    /**
     * @param int $leadId
     * @return JsonResponse
     * @throws ValidationException
     */
    public function updateBasicInfo(int $leadId): JsonResponse
    {
        if (!$this->isLeadReservedByProcessor($lead = $this->leadRepository->findOrFail($leadId)))
            return $this->formatResponse(["status" => false]);

        // update lead here
        $data = collect($this->request->get(self::REQUEST_DATA, []));

        return $this->formatResponse([
            "status" => $this->leadProcessingService->updateBasicInfo($lead, $data)
        ]);
    }

    /**
     * @param int $leadId
     * @return JsonResponse
     * @throws ValidationException
     */
    public function updateStatusReason(int $leadId): JsonResponse
    {
        if (!$this->isLeadReservedByProcessor($lead = $this->leadRepository->findOrFail($leadId))) {
            return $this->formatResponse(["status" => false]);
        }

        // update lead here
        $reason = trim($this->request->input(self::REQUEST_DATA . '.' . self::REQUEST_STATUS_REASON, ''));

        $validator = Validator::make(
            [
                self::REQUEST_STATUS_REASON => $reason
            ],
            [
                self::REQUEST_STATUS_REASON => 'required|string|max:255'
            ]
        );

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $this->formatResponse([
            "status" => $this->leadProcessingService->updatePendingReviewStatusReason($lead, $reason)
        ]);
    }

    /**
     * Marks an Initial lead as needing Further Review
     *
     * @param int $leadId
     * @return JsonResponse
     * @throws Exception
     */
    public function markAsPendingReview(int $leadId): JsonResponse
    {
        if (!$this->isLeadReservedByProcessor($lead = $this->leadRepository->findOrFail($leadId))) {
            return $this->formatResponse(["status" => false, "msg" => "You have not locked this lead. Please refresh the page and try again."]);
        }

        $reason = $this->request->get(self::REQUEST_REASON, null);

        return $this->formatResponse([
            "status" => $this->leadProcessingService->markLeadAsPendingReview($lead, $this->getLeadProcessor(), $reason)
        ]);
    }

    /**
     * Marks a pending review lead as under review.
     *
     * @param int $leadId
     * @return JsonResponse
     * @throws Exception
     */
    public function markAsUnderReview(int $leadId): JsonResponse
    {
        if (!$this->isLeadReservedByProcessor($lead = $this->leadRepository->findOrFail($leadId))) {
            return $this->formatResponse(["status" => false, "msg" => "You have not locked this lead. Please refresh the page and try again."]);
        }

        $reason = $this->request->get(self::REQUEST_REASON, null);
        $comment = $this->request->get(self::REQUEST_PUBLIC_COMMENT, null);

        return $this->formatResponse([
            "status" => $this->leadProcessingService->markLeadAsUnderReview($lead, $this->getLeadProcessor(), $reason, $comment)
        ]);
    }

    /**
     * Handles the heartbeat for a given lead id/processor.
     *
     * @param int $leadId
     * @return JsonResponse
     */
    public function heartbeat(int $leadId): JsonResponse
    {
        if (!$this->isLeadReservedByProcessor($lead = $this->leadRepository->findOrFail($leadId)))
            return $this->formatResponse(["status" => false]);

        $this->leadProcessingService->heartbeat($lead, $this->getLeadProcessor());

        return $this->formatResponse([
            "status" => true
        ]);
    }

    /**
     * Handles retrieving the queue for a given lead processor.
     *
     * @return JsonResponse
     */
    public function getQueue(): JsonResponse
    {
        $processor = $this->getLeadProcessor();

        if (!$processor) {
            return $this->formatResponse([
                "status"       => false,
                "no_processor" => true
            ]);
        } else if ($processor->lead_processing_team_id === LeadProcessingTeam::NO_TEAM_ASSIGNED_ID) {
            return $this->formatResponse([
                "status"  => false,
                "no_team" => true
            ]);
        }

        return $this->formatResponse([
            "status" => true,
            "queue"  => $processor->team->primaryQueue->primary_status
        ]);
    }

    /**
     * @param int $leadId
     * @return JsonResponse
     * @throws Exception
     */
    public function approve(int $leadId): JsonResponse
    {
        if (!$this->isLeadReservedByProcessor($lead = $this->leadRepository->findOrFail($leadId))) {
            return $this->formatResponse(["status" => false, "msg" => "You have not locked this lead. Please refresh the page and try again."]);
        }

        $reason = $this->request->get(self::REQUEST_REASON, null);
        $bestTimeToContact = $this->request->get(self::REQUEST_BEST_TIME_TO_CONTACT, null);
        $comment = $this->request->get(self::REQUEST_PUBLIC_COMMENT, null);

        return $this->formatResponse([
            "status" => $this->leadProcessingService->approveLead($lead, $this->getLeadProcessor(), $reason, $bestTimeToContact, $comment)
        ]);
    }

    /**
     * @param LeadProcessingHistoryTransformer $leadProcessingHistoryTransformer
     * @return JsonResponse
     */
    public function getHistory(LeadProcessingHistoryTransformer $leadProcessingHistoryTransformer): JsonResponse
    {
        if (!($processor = $this->getLeadProcessor())) {
            return $this->formatResponse(["status" => false]);
        }

        $entries = $this->leadProcessingRepository->getProcessorHistory($processor);

        return $this->formatResponse([
            "status"  => true,
            "history" => $leadProcessingHistoryTransformer->transformLeadProcessingHistoryEntries($entries)
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function createInitialLead(Request $request): JsonResponse
    {
        $uuid = $request->input(self::REQUEST_UUID);

        $request->validate(
            [
                self::REQUEST_UUID => ['required', 'string', 'size:36', 'alpha_dash']
            ],
            [
                self::REQUEST_UUID => $uuid
            ]
        );

        $createInitialLeadJob = new CreateInitialLead($uuid);
        if (EloquentQuote::where(EloquentQuote::REFERENCE, $uuid)->exists()) {
            $this->dispatchSync($createInitialLeadJob);
        } else {
            $this->dispatch(
                $createInitialLeadJob->delay(
                    now()->addMinutes(1)
                )
            );
        }

        return $this->formatResponse([
            "status" => true
        ]);
    }

    /**
     * Return a 500 HTTP response if there has been no lead received in X amount of minutes
     * @param OfficeHoursRepository $officeHoursRepository
     * @return Response
     */
    public function checkLastLeadCreationTime(OfficeHoursRepository $officeHoursRepository): Response
    {
        $interval = LeadProcessingConfiguration::query()->first()->{LeadProcessingConfiguration::FIELD_LAST_LEAD_CREATED_INTERVAL_MIN} ?? LeadProcessingConfiguration::LAST_LEAD_CREATED_INTERVAL_DEFAULT;
        $lastLeadCreatedTimestamp = (int) Cache::get(self::LAST_LEAD_CREATION_TIMESTAMP);
        $statusCode = !$lastLeadCreatedTimestamp || (now()->timestamp - $lastLeadCreatedTimestamp) >= ($interval * 60) ?  ResponseAlias::HTTP_INTERNAL_SERVER_ERROR : ResponseAlias::HTTP_OK;
        if (!$officeHoursRepository->getOfficeOpenForTimeUTC(Carbon::now('UTC'))) {
            $statusCode = ResponseAlias::HTTP_OK;
        }
        return response()->noContent($statusCode);
    }

    public function createOutboundSMS(int $leadId): JsonResponse
    {
        $this->validate($this->request, [self::REQUEST_BODY => "required|string"]);

        $processor = $this->getLeadProcessor();
        $lead = $this->leadRepository->findOrFail($leadId);
        $toNumber = $lead->address->phone;
        $body = $this->request->get(self::REQUEST_BODY, null);

        $this->leadCommunicationService->createOutboundSMS($leadId, $processor, $toNumber, $body);

        return $this->formatResponse([
            "status" => true
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getTimeConfigurations(): JsonResponse
    {
        $configs = $this->leadProcessingRepository->getLeadProcessingConfiguration()?->toArray();

        return $this->formatResponse([
            "status"  => true,
            "configs" => [
                LeadProcessingConfiguration::FIELD_MINIMUM_REVIEW_TIME              => $configs[LeadProcessingConfiguration::FIELD_MINIMUM_REVIEW_TIME],
                LeadProcessingConfiguration::FIELD_CHECK_NEXT_LEAD_INTERVAL_SECONDS => $configs[LeadProcessingConfiguration::FIELD_CHECK_NEXT_LEAD_INTERVAL_SECONDS]
            ]
        ]);
    }
}
