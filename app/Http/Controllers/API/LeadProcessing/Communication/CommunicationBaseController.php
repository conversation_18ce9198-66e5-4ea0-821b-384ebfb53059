<?php

namespace App\Http\Controllers\API\LeadProcessing\Communication;

use App\Contracts\Services\Communication\CommunicationContract;
use App\Enums\CommunicationRelationTypes;
use App\Http\Controllers\API\LeadProcessing\LeadProcessingBaseAPIController;
use App\Http\Requests\GetContactUrlRequest;
use App\Models\User;
use App\Repositories\LeadProcessing\LeadCommunicationRepository;
use App\Services\Communication\CommunicationService;
use App\Transformers\LeadProcessing\CommunicationTransformer;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;

class CommunicationBaseController extends LeadProcessingBaseAPIController
{
    const REQUEST_OTHER_NUMBER = 'other_number';

    /**
     * Return text messages for a given phone number
     * @param string $phoneNumber
     * @return JsonResponse
     */
    public function getSMSHistoryForPhoneNumber(string $phoneNumber): JsonResponse
    {
        return $this->formatResponse([
            "status" => true,
            "messages" => $this->leadCommunicationService->getSMSHistoryByPhoneNumber($phoneNumber)
        ]);
    }

    public function getContactUrlByPhoneNumber(GetContactUrlRequest $request): JsonResponse
    {
        $validated = $request->validated();

        /** @var CommunicationService $communicationService */
        $communicationService = app()->make(CommunicationService::class);

        $relation = $communicationService->getRedirectUrl(
            CommunicationRelationTypes::tryFrom(Arr::get($validated, GetContactUrlRequest::RELATION_TYPE)),
            Arr::get($validated, GetContactUrlRequest::RELATION_ID),
        );

        return $this->formatResponse([
            "status" => !!$relation,
            "url" => $relation,
        ]);
    }

    /**
     * Handles generating an auth token for a given lead processor.
     *
     * @param CommunicationContract   $contract
     * @return JsonResponse
     */
    public function token(CommunicationContract $contract): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();
        $phoneNumber = $user->primaryPhone()->phone ?? '';

        return $this->formatResponse([
            "status" => true,
            "token" => $contract->retrieveWebPhoneToken([$phoneNumber]),
            "number" => $phoneNumber
        ]);
    }

    /**
     * @throws \Illuminate\Validation\ValidationException
     */
    public function lookupLead(CommunicationTransformer $transformer): JsonResponse
    {
        $this->leadLookupValidator();

        return $this->formatResponse([
            "status" => true,
            "lead" => $transformer->transformLeadDetails(
                $this->leadCommunicationService->getLeadDetails(
                    $this->request->get(self::REQUEST_OTHER_NUMBER)
                )
            )
        ]);
    }

    /**
     * @return void
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function leadLookupValidator(): void
    {
        $rules = [
            self::REQUEST_OTHER_NUMBER => 'required|string', // possible validation for phone number
        ];

        $this->validate(request(), $rules);
    }
}
