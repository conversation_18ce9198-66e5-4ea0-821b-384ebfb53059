<?php

namespace App\Http\Controllers\API\LeadProcessing\ConsumerController;

use App\Builders\Odin\ConsumerProductBuilder;
use App\ConsumerProcessing\Repositories\AvailableCompanyCampaignRepository;
use App\ConsumerProcessing\Services\ConsumerProductAllocationService;
use App\ConsumerProcessing\Services\ConsumerProjectProcessingService;
use App\Contracts\ProductAssignment\MultiProductAssignmentStrategyContract;
use App\DataModels\ProductAssignment\ProposedProductAssignment;
use App\Enums\ConsumerProcessing\ConsumerProcessingActivityType;
use App\Enums\ConsumerProcessing\ConsumerProcessingActivityVisibilityScope;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\Odin\JobStatus;
use App\Enums\Odin\JobTrackingRelation;
use App\Enums\Odin\PropertyType;
use App\Enums\Odin\QualityTier;
use App\Enums\Odin\SaleTypes;
use App\Enums\Odin\SolarConfigurableFields;
use App\Enums\Odin\VerificationServiceTypes;
use App\Enums\PingPostVariableEnum;
use App\Events\ConsumerProcessing\ConsumerProductApprovedEvent;
use App\Http\Controllers\API\ProductProcessing\Processing\ProductProcessingBaseAPIController;
use App\Http\Requests\CompanyCampaigns\AllocateConsumerProductRequest;
use App\Http\Requests\CompanyCampaigns\PriceForCampaignsRequest;
use App\Http\Requests\CompanyCampaigns\UpdateSaleTypeRequest;
use App\Http\Requests\Odin\StoreConsumerProductBasicInfoRequest;
use App\Http\Requests\Odin\StoreConsumerProductContactInfoRequest;
use App\Http\Requests\Odin\StoreConsumerProductUtilityInfoRequest;
use App\Http\Requests\Odin\UpdateAffiliateLeadInternalTrackingRequest;
use App\Http\Requests\StoreConsumerProcessingCommentRequest;
use App\Http\Resources\AvailableCampaignsResourceCollection;
use App\Http\Resources\ConsumerProcessingActivityResource;
use App\Http\Resources\JobTrackingResourceCollection;
use App\Http\Resources\LeadProcessing\ConsumerProductBasicInfoResource;
use App\Http\Resources\PingPostLogResource;
use App\Http\Resources\ProductAssignmentResourceCollection;
use App\Http\Resources\ProposedProductAssignmentResourceCollection;
use App\Http\Resources\SalesOverview\CallResource;
use App\Http\Resources\SalesOverview\TextResource;
use App\Jobs\PingPostPublishJob;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\ConsumerProcessingActivity;
use App\Models\Odin\Consumer;
use App\Models\Odin\CompanyReview;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductData;
use App\Models\Odin\Industry;
use App\Models\Odin\JobTracking;
use App\Models\Odin\ProductAppointment;
use App\Models\Odin\ProductAssignment;
use App\Models\SaleType;
use App\Models\User;
use App\Repositories\ConsumerProcessingActivityRepository;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\Campaigns\CompanyCampaignRepository;
use App\Repositories\Odin\ConsumerConfigurableFieldCategoryRepository;
use App\Repositories\Odin\ConsumerProductDataRepository;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Repositories\Odin\JobTrackingRepository;
use App\Repositories\Odin\ProductAssignmentRepository;
use App\Repositories\Odin\ReviewRepository;
use App\Repositories\Odin\SaleTypeRepository;
use App\Services\Campaigns\ProductBiddingService;
use App\Services\ConsumerProcessingActivityService;
use App\Services\Odin\PingPostAffiliateService;
use App\Services\Odin\PingPostPublishing\PingPostPublishService;
use App\Services\Odin\ProductProcessing\ProductProcessingService;
use App\Transformers\Odin\AddressTransformer;
use App\Transformers\Odin\CompanyReviewsTransformer;
use App\Repositories\Odin\ConsumerRepository;
use App\Transformers\Odin\ConsumerProductBasicInfoTransformer;
use App\Transformers\Odin\ConsumerProductDataTransformer;
use App\Transformers\Odin\ConsumerProductContactInfoTransformer;
use App\Transformers\Odin\ConsumerProductPreviousLeadTransformer;
use App\Transformers\Odin\ConsumerProductTaskviewTransformer;
use App\Transformers\Odin\ConsumerProductVerificationTransformer;
use App\Transformers\Odin\ProductAppointmentsTransformer;
use App\Transformers\Odin\ProductAssignmentTransformer;
use Carbon\Carbon;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Exception;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;

class ConsumerProductApiController extends ProductProcessingBaseAPIController
{
    const string MODULE_PERMISSION = "lead-processing";

    const string REQUEST_ZIP_CODE   = 'zip_code';
    const string REQUEST_INDUSTRY   = 'industry';
    const string REQUEST_DAYS_LIMIT = 'days';

    const string RESPONSE_KEY_STATUS               = 'status';
    const string RESPONSE_KEY_BASIC_INFO           = 'consumer_product_basic_info';
    const string RESPONSE_KEY_PREVIOUS_LEADS       = 'previous_consumer_products';
    const string RESPONSE_KEY_CONTACT_INFO         = 'consumer_contact';
    const string RESPONSE_KEY_UTILITY              = 'consumer_product_utility';
    const string RESPONSE_KEY_DATE_CREATED         = 'product_date_created';
    const string RESPONSE_KEY_DATE_UPDATED         = 'product_date_updated';
    const string RESPONSE_KEY_PRODUCT_ID           = 'product_id';
    const string RESPONSE_KEY_CONSUMER_ID          = 'consumer_id';
    const string RESPONSE_KEY_PAYLOAD_DATA         = 'display_payload_data';
    const string RESPONSE_LEADS                    = 'leads';
    const string RESPONSE_KEY_TEST_PRODUCT         = 'test_product';
    const string RESPONSE_ASSIGNMENTS              = 'assignments';
    const string RESPONSE_PRICES                   = 'prices';
    const string RESPONSE_COMMENTS                 = 'comments';
    const string RESPONSE_ACTIVITY                 = 'activity';
    const string RESPONSE_ACTIVITY_TYPE            = 'activity_type';

    const string RESPONSE_ASSIGNMENTS_ASSIGNED  = 'assigned';
    const string RESPONSE_ASSIGNMENTS_PROPOSED  = 'proposed';
    const string RESPONSE_ASSIGNMENTS_AVAILABLE = 'available';
    const string RESPONSE_ALLOCATION_JOB_STATUS = 'allocation_job_status';
    const string RESPONSE_CONSUMER_PRODUCT      = 'consumer_product';
    const string RESPONSE_RESERVED_BY           = 'reserved_by';
    const string RESPONSE_ERROR                 = 'error';

    const string CONSUMER_UPDATE                   = 'consumerUpdate';
    const string CONSUMER_PRODUCT_UPDATE           = 'consumerProductUpdate';
    const string APPOINTMENT_TYPE                  = 'appointment_type';
    const string APPOINTMENT_DATE                  = 'appointment_date';
    const string APPOINTMENT_TIME                  = 'appointment_time';

    const string RESPONSE_KEY_ADDRESS              = 'address';

    /**
     * Returns basic info for a Consumer
     *
     * @param int $consumerProductId
     * @param ConsumerProductBasicInfoTransformer $consumerProductBasicInfoTransformer
     * @param ConsumerProductRepository $consumerProductRepository
     * @return JsonResponse
     */
    public function getConsumerProductBasicInfo(int $consumerProductId, ConsumerProductBasicInfoTransformer $consumerProductBasicInfoTransformer, ConsumerProductRepository $consumerProductRepository): JsonResponse
    {
        $consumerProduct = $consumerProductRepository->findOrFail($consumerProductId);

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS     => true,
            self::RESPONSE_KEY_BASIC_INFO => $consumerProductBasicInfoTransformer->transform($consumerProduct),
        ]);
    }

    /**
     * Get a Consumer's other ConsumerProducts from a ConsumerProductId
     *
     * @param int $consumerProductId
     * @param ConsumerProductPreviousLeadTransformer $previousLeadTransformer
     * @param ConsumerProductRepository $consumerProductRepository
     * @return JsonResponse
     * @throws ModelNotFoundException
     */
    public function getOtherConsumerProducts(int $consumerProductId, ConsumerProductPreviousLeadTransformer $previousLeadTransformer, ConsumerProductRepository $consumerProductRepository): JsonResponse
    {
        $consumerProduct = $consumerProductRepository->findOrFail($consumerProductId);
        $leadsPerPage = 10;

        $otherConsumerProducts = $consumerProductRepository
            ->getDuplicateConsumerProductsQuery(
                $consumerProduct,
                [
                    ConsumerProduct::RELATION_CONSUMER => function ($query) {
                        $query->select(Consumer::FIELD_ID, Consumer::FIELD_FIRST_NAME, Consumer::FIELD_LAST_NAME, Consumer::FIELD_WEBSITE_ID);
                    }
                ]
            )
            ->cursorPaginate($leadsPerPage, ['*'], 'page', $this->request->get('page'))
            ->through(fn(ConsumerProduct $consumerProduct) => $previousLeadTransformer->transform($consumerProduct));

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS         => true,
            self::RESPONSE_KEY_PREVIOUS_LEADS => $otherConsumerProducts,
        ]);

    }

    /**
     * Get info for ConsumerProduct Contact module
     *
     * @param int $consumerProductId
     * @param ConsumerProductRepository $consumerProductRepository
     * @param ConsumerProductContactInfoTransformer $contactTransformer
     * @return JsonResponse
     * @throws ModelNotFoundException
     * @throws Exception
     */
    public function getConsumerProductContactInfo(int $consumerProductId, ConsumerProductRepository $consumerProductRepository, ConsumerProductContactInfoTransformer $contactTransformer): JsonResponse
    {
        $consumerProduct = $consumerProductRepository->findOrFail($consumerProductId);
        $transform = $contactTransformer->transform($consumerProduct);

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS           => true,
            self::RESPONSE_KEY_CONTACT_INFO     => $transform
        ]);
    }

    /**
     * Update basic info on ConsumerProduct / ConsumerProductData
     *
     * @param int $consumerProductId
     * @param StoreConsumerProductBasicInfoRequest $request
     * @param ConsumerProductRepository $consumerProductRepository
     * @return JsonResponse
     * @throws Exception
     */
    public function updateConsumerProductBasicInfo(int $consumerProductId, StoreConsumerProductBasicInfoRequest $request, ConsumerProductRepository $consumerProductRepository): JsonResponse
    {
        $filteredRequest = $request->safe()->collect();
        $modelKeys = [ConsumerProduct::FIELD_CONTACT_REQUESTS];
        $payloadKeys = [
            GlobalConfigurableFields::BEST_TIME_TO_CALL->value,
            GlobalConfigurableFields::BEST_TIME_TO_CALL_OTHER->value
        ];

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS => $consumerProductRepository->updateConsumerProductModelAndPayloadById($consumerProductId, $filteredRequest, $modelKeys, $payloadKeys)
        ]);
    }

    /**
     * @param int $consumerProductId
     * @param ConsumerProductRepository $consumerProductRepository
     * @param ReviewRepository $reviewRepository
     * @param CompanyReviewsTransformer $companyReviewsTransformer
     * @return JsonResponse
     * @throws ModelNotFoundException
     */
    public function getReviewsFromConsumerProductId(
        int $consumerProductId,
        ConsumerProductRepository $consumerProductRepository,
        ReviewRepository $reviewRepository,
        CompanyReviewsTransformer $companyReviewsTransformer,
    ): JsonResponse
    {
        $consumerProduct = $consumerProductRepository->findOrFail($consumerProductId);
        $consumerId = $consumerProduct->{ConsumerProduct::FIELD_CONSUMER_ID};
        $consumerProductIpAddress = $consumerProduct->{ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA}?->{ConsumerProductData::FIELD_PAYLOAD}[GlobalConfigurableFields::IP_ADDRESS->value] ?? '';
        $reviews = $reviewRepository->getRelatedReviewsByConsumerOrIpAddress($consumerId, $consumerProductIpAddress);

        $filterTransformByKeys = [ CompanyReview::FIELD_ID, Model::CREATED_AT, CompanyReview::RELATION_COMPANY ];
        $reviewsPerPage = 10;

        return $this->formatResponse([
            'status'    => true,
            'reviews' => $reviews
                ->paginate($reviewsPerPage, ['*'], 'page', $this->request->get('page'))
                ->through(fn(CompanyReview $review) => $companyReviewsTransformer->transformCompanyReview($review, $filterTransformByKeys))
            ]);
    }

    /**
     * Return solar utility data for ConsumerProduct module
     *
     * @param int $consumerProductId
     * @param ConsumerProductRepository $consumerProductRepository
     * @param ConsumerProductDataTransformer $payloadTransformer
     * @return JsonResponse
     * @throws ModelNotFoundException
     */
    public function getConsumerProductUtilityInfo(int $consumerProductId, ConsumerProductRepository $consumerProductRepository, ConsumerProductDataTransformer $payloadTransformer): JsonResponse
    {
        $consumerProduct = $consumerProductRepository->findOrFail($consumerProductId);
        $requiredPayloadKeys = [
            SolarConfigurableFields::UTILITY_ID->value,
            SolarConfigurableFields::UTILITY_NAME->value,
            SolarConfigurableFields::ELECTRIC_COST->value,
        ];
        $responseData = $payloadTransformer->getConsumerProductDataByKeysArray($consumerProduct, $requiredPayloadKeys);

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS   => true,
            self::RESPONSE_KEY_UTILITY  => $responseData
        ]);
    }

    /**
     * Update utility info for a consumer product.
     *
     * @param int $consumerProductId
     * @param StoreConsumerProductUtilityInfoRequest $request
     * @param ConsumerProductRepository $consumerProductRepository
     * @return JsonResponse
     * @throws Exception
     */
    public function updateConsumerProductUtilityInfo(int $consumerProductId, StoreConsumerProductUtilityInfoRequest $request, ConsumerProductRepository $consumerProductRepository): JsonResponse
    {
        $filteredRequest = $request->safe()->collect();
        $payloadKeys = [
            SolarConfigurableFields::ELECTRIC_COST->value
        ];

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS => $consumerProductRepository->updateConsumerProductModelAndPayloadById(
                $consumerProductId,
                $filteredRequest,
                [],
                $payloadKeys
            )
        ]);
    }

    /**
     * Update contact info on Consumer model, ConsumerProduct model or ConsumerProductData payload
     *
     * @param int $consumerProductId
     * @param StoreConsumerProductContactInfoRequest $request
     * @param ConsumerProductRepository $consumerProductRepository
     * @param ConsumerRepository $consumerRepository
     * @return JsonResponse
     * @throws Exception
     */
    public function updateConsumerProductContactInfo(int $consumerProductId, StoreConsumerProductContactInfoRequest $request, ConsumerProductRepository $consumerProductRepository, ConsumerRepository $consumerRepository): JsonResponse
    {
        $filteredRequest = $request->safe()->collect();
        $consumer = $consumerProductRepository->findOrFail($consumerProductId)?->{ConsumerProduct::RELATION_CONSUMER};

        $consumerKeys = [ Consumer::FIELD_FIRST_NAME, Consumer::FIELD_LAST_NAME, Consumer::FIELD_EMAIL, Consumer::FIELD_PHONE ];
        $consumerProductKeys = [];
        $consumerProductPayloadKeys = [ GlobalConfigurableFields::OWN_PROPERTY->value ];

        // split update data into Consumer / ConsumerProduct & ConsumerProductData, according to key arrays above
        $splitUpdate = $filteredRequest->reduce(function($output, $value, $key) use ($consumerKeys, $consumerProductKeys, $consumerProductPayloadKeys) {
            if (in_array($key, $consumerKeys)) $output[self::CONSUMER_UPDATE][$key] = $value;
            else if (in_array($key, [ ...$consumerProductKeys, ...$consumerProductPayloadKeys ])) $output[self::CONSUMER_PRODUCT_UPDATE][$key] = $value;
            return $output;
        }, [ self::CONSUMER_UPDATE => [], self::CONSUMER_PRODUCT_UPDATE => [] ]);

        $updatedConsumer = (count($splitUpdate[self::CONSUMER_UPDATE]) === 0)
            || $consumerRepository->updateConsumerModel($consumer, $splitUpdate[self::CONSUMER_UPDATE], true);
        $updatedConsumerProduct = (count($splitUpdate[self::CONSUMER_PRODUCT_UPDATE]) === 0)
            || $consumerProductRepository->updateConsumerProductModelAndPayloadById($consumerProductId, collect($splitUpdate[self::CONSUMER_PRODUCT_UPDATE]), $consumerProductKeys, $consumerProductPayloadKeys);

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS => $updatedConsumer || $updatedConsumerProduct
        ]);
    }

    /**
     * * Returns the list of verification checks for the requested product.
     *
     * @param ConsumerProduct                        $consumerProduct
     * @param string                                 $service
     * @param ConsumerProductRepository              $consumerProductRepository
     * @param ConsumerProductVerificationTransformer $transformer
     * @return JsonResponse
     * @throws Exception
     */
    public function getProductVerification(ConsumerProduct $consumerProduct, string $service, ConsumerProductRepository $consumerProductRepository, ConsumerProductVerificationTransformer $transformer): JsonResponse
    {
        $this->performAuthorizationCheck(self::MODULE_PERMISSION);

        /** @var VerificationServiceTypes $requestedService */
        $requestedService = VerificationServiceTypes::tryFrom($service);
        if(!$requestedService) throw new BadRequestException();

        $verifiedData = match ($requestedService) {
            VerificationServiceTypes::IP_QUALITY_SCORE  => $consumerProductRepository->getIPQualityScore($consumerProduct),
            VerificationServiceTypes::IDENTITY_CHECK    => $consumerProductRepository->getConsumerProductVerificationDetails($consumerProduct),
            default                                     => []
        };

        return $this->formatResponse([
            "status"                 => (bool)$verifiedData,
            "product_verification"   => $transformer->transform($requestedService, $verifiedData)
        ]);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @return JsonResponse
     */
    public function getConsumerProductDateInfo(ConsumerProduct $consumerProduct): JsonResponse
    {
        return $this->formatResponse([
            "status"                => true,
            "product_date_created"  => $consumerProduct->created_at?->toIso8601String(),
            "product_date_updated"  => $consumerProduct->updated_at?->toIso8601String()
        ]);
    }

    /**
     * Return all consumer product data to display on the lead processing screen.
     * Includes data for these frontend modules:
     * - Date Info
     * - Lead Info
     * - Contact
     * - Utility
     *
     * @param ConsumerProduct $consumerProduct
     * @param ConsumerProductDataTransformer $payloadTransformer
     * @param ConsumerProductBasicInfoTransformer $consumerProductBasicInfoTransformer
     * @param ConsumerConfigurableFieldCategoryRepository $fieldRepository
     * @param ConsumerProductDataRepository $consumerProductDataRepository
     * @param AddressTransformer $addressTransformer
     * @return JsonResponse
     */
    public function getProductLeadProcessingData(
        ConsumerProduct                             $consumerProduct,
        ConsumerProductDataTransformer              $payloadTransformer,
        ConsumerProductBasicInfoTransformer         $consumerProductBasicInfoTransformer,
        ConsumerConfigurableFieldCategoryRepository $fieldRepository,
        ConsumerProductDataRepository               $consumerProductDataRepository,
        AddressTransformer                          $addressTransformer,
    ): JsonResponse
    {
        $requiredPayloadKeys = [
            SolarConfigurableFields::UTILITY_ID->value,
            SolarConfigurableFields::UTILITY_NAME->value,
            SolarConfigurableFields::ELECTRIC_COST->value,
        ];

        $utilityData = $payloadTransformer->getConsumerProductDataByKeysArray($consumerProduct, $requiredPayloadKeys);
        $contactData = new ConsumerProductBasicInfoResource($consumerProduct);
        $mappedPayloadData = $payloadTransformer->getPayloadDataToDisplay($consumerProduct, $fieldRepository, $consumerProductDataRepository);

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS           => true,
            self::RESPONSE_KEY_CONSUMER_ID      => $consumerProduct->consumer_id,
            self::RESPONSE_KEY_PRODUCT_ID       => $consumerProduct->id,
            self::RESPONSE_KEY_DATE_CREATED     => $consumerProduct->created_at?->toIso8601String(),
            self::RESPONSE_KEY_DATE_UPDATED     => $consumerProduct->updated_at?->toIso8601String(),
            self::RESPONSE_KEY_PAYLOAD_DATA     => $mappedPayloadData,
            self::RESPONSE_KEY_UTILITY          => $utilityData,
            self::RESPONSE_KEY_CONTACT_INFO     => $contactData,
            self::RESPONSE_KEY_BASIC_INFO       => $consumerProductBasicInfoTransformer->transform($consumerProduct),
            self::RESPONSE_KEY_ADDRESS          => $addressTransformer->transformAddress($consumerProduct->address),
            self::RESPONSE_KEY_TEST_PRODUCT     => (bool) $consumerProduct->testProduct,
            self::RESPONSE_COMMENTS             => ConsumerProcessingActivityResource::collection($consumerProduct->processingActivities),
        ]);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @return JsonResponse
     */
    public function getConsumerProductComments(ConsumerProduct $consumerProduct): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS  => true,
            self::RESPONSE_COMMENTS    => $this->getPaginatedConsumerProcessingActivities($consumerProduct, $this->request->get('page', 1)),
        ]);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param int $page
     * @return LengthAwarePaginator
     */
    protected function getPaginatedConsumerProcessingActivities(ConsumerProduct $consumerProduct, int $page = 1): LengthAwarePaginator
    {
        $comments = $consumerProduct->processingActivities()
            ->with(ConsumerProcessingActivity::RELATION_USER)
            ->paginate(10, ['*'], 'page', $page);

        return ConsumerProcessingActivityResource::collection($comments)->resource;
    }

    /**
     * @param StoreConsumerProcessingCommentRequest $request
     * @param ConsumerProduct $consumerProduct
     * @param ConsumerProcessingActivityService $consumerProcessingActivityService
     * @return JsonResponse
     */
    public function addComment(StoreConsumerProcessingCommentRequest $request, ConsumerProduct $consumerProduct, ConsumerProcessingActivityService $consumerProcessingActivityService): JsonResponse
    {
        $validated = $request->safe()->toArray();

        /** @var User $user */
        $user = Auth::user();

        $comment = $consumerProcessingActivityService->createActivity(
            consumerProduct: $consumerProduct,
            type: ConsumerProcessingActivityType::USER_COMMENT,
            user: $user,
            comment: $validated[ConsumerProcessingActivity::FIELD_COMMENT] ?? '',
            scope: ConsumerProcessingActivityVisibilityScope::tryFrom($validated[ConsumerProcessingActivity::FIELD_SCOPE]) ?? null,
        );

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS                 => !!$comment,
            ConsumerProcessingActivity::FIELD_COMMENT => new ConsumerProcessingActivityResource($comment ?? []),
        ]);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param ProductAppointmentsTransformer $transformer
     *
     * @return JsonResponse
     */
    public function getProductAppointments(ConsumerProduct $consumerProduct, ProductAppointmentsTransformer $transformer): JsonResponse
    {
        return $this->formatResponse([
            'appointments' => $transformer->transform($consumerProduct->{ConsumerProduct::RELATION_LEAD_APPOINTMENTS})
        ]);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param ProductAppointmentsTransformer $transformer
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function createAppointment(ConsumerProduct $consumerProduct, ProductAppointmentsTransformer $transformer): JsonResponse
    {
        $this->validate($this->request, [
            self::APPOINTMENT_TYPE => ['required', new Enum(QualityTier::class)],
            self::APPOINTMENT_DATE => ['required','date_format:Y-m-d'],
            self::APPOINTMENT_TIME => ['required','date_format:H:i:s']
        ]);

        /** @var ProductAppointment $appointment */
        $appointment = ProductAppointment::query()->updateOrCreate([
            ProductAppointment::LEAD_CONSUMER_PRODUCT_ID => $consumerProduct->{ConsumerProduct::FIELD_ID},
            ProductAppointment::APPOINTMENT_TYPE => $this->request->get(self::APPOINTMENT_TYPE),
            ProductAppointment::APPOINTMENT_DATE => Carbon::parse($this->request->get(self::APPOINTMENT_DATE))->format(ProductAppointment::DATE_FORMAT),
            ProductAppointment::APPOINTMENT_TIME => Carbon::createFromTimeString($this->request->get(self::APPOINTMENT_TIME))->format(ProductAppointment::TIME_FORMAT),
        ]);

        return $this->formatResponse([
            'appointment' => $transformer->transformAppointment($appointment)
        ]);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param int $appointmentId
     *
     * @return JsonResponse
     */
    public function deleteAppointment(ConsumerProduct $consumerProduct, int $appointmentId): JsonResponse
    {
        $appointment = $consumerProduct->leadAppointments()->findOrFail($appointmentId);

        return $this->formatResponse([
            'status' => !!$appointment->delete()
        ]);
    }

    public function getUnsoldProductsInCountyByZipCode(ConsumerProductTaskviewTransformer $transformer): JsonResponse
    {
        $zipCode = $this->request->get(self::REQUEST_ZIP_CODE);
        $industry = $this->request->get(self::REQUEST_INDUSTRY);
        $days = $this->request->get(self::REQUEST_DAYS_LIMIT, 2);

        $industry = Industry::query()->where(Industry::FIELD_SLUG, $industry)->first();

        $unsold = ConsumerProductBuilder::query()
            ->withAssignmentCount()
            ->forIndustryId($industry?->id)
            ->forFromDate(now()->subDays($days))
            ->forZipCodes([$zipCode])
            ->get()
            ->filter(fn(ConsumerProduct $consumerProduct) => $consumerProduct->product_assignment_count === 0);

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS => true,
            self::RESPONSE_LEADS      => $transformer->transform($unsold)
        ]);
    }

    public function getUndersoldProductsInCountyByZipCode(ConsumerProductTaskviewTransformer $transformer): JsonResponse
    {
        $zipCode = $this->request->get(self::REQUEST_ZIP_CODE);
        $industry = $this->request->get(self::REQUEST_INDUSTRY);
        $days = $this->request->get(self::REQUEST_DAYS_LIMIT, 2);

        $industry = Industry::query()->where(Industry::FIELD_SLUG, $industry)->first();

        $undersold = ConsumerProductBuilder::query()
            ->withAssignmentCount()
            ->forIndustryId($industry?->id)
            ->forFromDate(now()->subDays($days))
            ->forZipCodes([$zipCode])
            ->get()
            ->filter(fn(ConsumerProduct $consumerProduct) => $consumerProduct->product_assignment_count > 0 && $consumerProduct->product_assignment_count < $consumerProduct->contact_requests);

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS => true,
            self::RESPONSE_LEADS      => $transformer->transform($undersold)
        ]);
    }

    /**
     * Retrieves a list of assignments for the given consumer product ID.
     *
     * @param int                          $consumerProductId
     * @param ConsumerProductRepository    $consumerProductRepository
     * @param ProductAssignmentRepository  $productAssignmentRepository
     * @param ProductAssignmentTransformer $productAssignmentTransformer
     * @return JsonResponse
     */
    public function getConsumerProductAssignments(
        int                          $consumerProductId,
        ConsumerProductRepository    $consumerProductRepository,
        ProductAssignmentRepository  $productAssignmentRepository,
        ProductAssignmentTransformer $productAssignmentTransformer,
    ): JsonResponse
    {
        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = $consumerProductRepository->findOrFail($consumerProductId);

        $productAssignments = $productAssignmentRepository->getByConsumerProduct($consumerProduct);

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS   => true,
            self::RESPONSE_ASSIGNMENTS  => $productAssignmentTransformer->transformAll($productAssignments)
        ]);
    }


    /**
     * Get the assignments (existing and proposed) for a consumer product of future industry
     *
     * @param ConsumerProduct $consumerProduct
     * @param ConsumerProjectProcessingService $processingService
     * @param MultiProductAssignmentStrategyContract $assignmentStrategyContract
     * @param JobTrackingRepository $jobTrackingRepository
     * @param ProductAssignmentRepository $productAssignmentRepository
     * @param ConsumerProductBasicInfoTransformer $consumerProductBasicInfoTransformer
     * @param ConsumerProductRepository $consumerProductRepository
     *
     * @return JsonResponse
     */
    public function getProductProposedAssignments(
        ConsumerProduct $consumerProduct,
        ConsumerProjectProcessingService $processingService,
        MultiProductAssignmentStrategyContract $assignmentStrategyContract,
        JobTrackingRepository $jobTrackingRepository,
        ProductAssignmentRepository $productAssignmentRepository,
        ConsumerProductBasicInfoTransformer $consumerProductBasicInfoTransformer,
        ConsumerProductRepository $consumerProductRepository
    ): JsonResponse
    {
        if (!$consumerProduct->serviceProduct->service->industry->industryConfiguration?->future_campaigns_active) {
            return $this->formatResponse([
                self::RESPONSE_KEY_STATUS   => false,
                self::RESPONSE_ASSIGNMENTS  => []
            ]);
        }

        $assignments = $productAssignmentRepository->getAllAssignmentsForConsumer($consumerProduct->consumer);
        $consumerProject = $processingService->prepareConsumerProject($consumerProduct->consumer, $consumerProduct->address);
        $potentialCampaigns = $processingService->getAvailableCampaigns($consumerProject);
        $proposedAssignments = collect([]);

        if ($potentialCampaigns->isNotEmpty()) {
            $proposedAssignments = $assignmentStrategyContract->calculate(
                $consumerProject,
                $potentialCampaigns,
                $processingService->getPotentialProductTypes($consumerProject),
                []
            )->filter(fn(ProposedProductAssignment $proposedProductAssignment) => !$proposedProductAssignment->isExistingAssignment);
        }

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS            => true,
            self::RESPONSE_ASSIGNMENTS           => [
                self::RESPONSE_ASSIGNMENTS_ASSIGNED  => new ProductAssignmentResourceCollection($assignments),
                self::RESPONSE_ASSIGNMENTS_PROPOSED  => new ProposedProductAssignmentResourceCollection($proposedAssignments),
                self::RESPONSE_ASSIGNMENTS_AVAILABLE => new AvailableCampaignsResourceCollection(
                    $this->getAllAvailableCampaigns($consumerProduct, $proposedAssignments, $assignments)
                ),
                self::RESPONSE_RESERVED_BY => $consumerProductRepository->getReservedByUser($consumerProduct->id)?->only([User::FIELD_ID, User::FIELD_NAME])
            ],
            self::RESPONSE_ALLOCATION_JOB_STATUS => new JobTrackingResourceCollection(
                $jobTrackingRepository->getJobTrackingByRelationAndId(JobTrackingRelation::CONSUMER_PRODUCT, $consumerProduct->id)
            ),
            self::RESPONSE_CONSUMER_PRODUCT => $consumerProductBasicInfoTransformer->transform($consumerProduct)
        ]);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param PriceForCampaignsRequest $request
     * @param CompanyCampaignRepository $companyCampaignRepository
     * @param LocationRepository $locationRepository
     * @param ProductBiddingService $productBiddingService
     *
     * @return JsonResponse
     */
    public function getPriceForCampaign(
        ConsumerProduct $consumerProduct,
        PriceForCampaignsRequest $request,
        CompanyCampaignRepository $companyCampaignRepository,
        LocationRepository $locationRepository,
        ProductBiddingService $productBiddingService
    ): JsonResponse
    {
        $zipCodeLocation = $locationRepository->getZipCode($consumerProduct->address->zip_code);
        $countyLocation = $locationRepository->getCounty($zipCodeLocation->state_key, $zipCodeLocation->county_key);
        $stateLocation = $locationRepository->getState($zipCodeLocation->state_key);

        $propertyTypeId = PropertyType::RESIDENTIAL->model()->id;
        $qualityTierId  = QualityTier::STANDARD->model()->id;
        $salesTypeId    = SaleType::query()
            ->where(SaleType::FIELD_NAME, $request->validated(PriceForCampaignsRequest::SALE_TYPE))
            ->first()->id;

        $prices = $companyCampaignRepository->findMany(
            $request->validated(PriceForCampaignsRequest::CAMPAIGN_IDS)
        )->map(fn(CompanyCampaign $campaign) => [
            'campaign_id' => $campaign->id,
            'price'       => $productBiddingService->getProductBid(
                companyCampaign: $campaign,
                countyLocationId: $countyLocation->id,
                stateLocationId: $stateLocation->id,
                propertyTypeId: $propertyTypeId,
                qualityTierId: $qualityTierId, //todo: handle quality tier
                salesTypeId: $salesTypeId
            )
        ]);

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS => true,
            self::RESPONSE_PRICES => $prices
        ]);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param AllocateConsumerProductRequest $request
     * @param ConsumerProductAllocationService $consumerProductAllocationService
     * @param JobTrackingRepository $jobTrackingRepository
     * @param ConsumerProductRepository $consumerProductRepository
     * @param ProductProcessingService $processingService
     * @param Dispatcher $dispatcher
     *
     * @return JsonResponse
     * @throws BindingResolutionException
     * @throws Exception
     */
    public function allocate(
        ConsumerProduct $consumerProduct,
        AllocateConsumerProductRequest $request,
        ConsumerProductAllocationService $consumerProductAllocationService,
        JobTrackingRepository $jobTrackingRepository,
        ConsumerProductRepository $consumerProductRepository,
        ProductProcessingService $processingService,
        Dispatcher $dispatcher
    ): JsonResponse
    {
        $reservedBy = $consumerProductRepository->getReservedByUser($consumerProduct->id);

        if ($reservedBy) {
            return $this->formatResponse([
                self::RESPONSE_KEY_STATUS => false,
                self::RESPONSE_ERROR => "Lead is reserved by $reservedBy->name"
            ]);
        }

        if (
            $jobTrackingRepository->getJobTrackingByRelationAndId(JobTrackingRelation::CONSUMER_PRODUCT, $consumerProduct->id)
            ->first(fn(JobTracking $jobTracking) => $jobTracking->status === JobStatus::SCHEDULED)
        ) {
            return $this->formatResponse([
                self::RESPONSE_KEY_STATUS => false,
                self::RESPONSE_ERROR => 'An allocation is scheduled'
            ]);
        }

        if ($consumerProductAllocationService->isDuplicateAllocations($consumerProduct, $request->validated(AllocateConsumerProductRequest::CAMPAIGN_IDS))) {
            return $this->formatResponse([
                self::RESPONSE_KEY_STATUS => false,
                self::RESPONSE_ERROR => 'Some selected companies already have this lead allocated.'
            ]);
        }

        if (!$consumerProduct->good_to_sell) {
            $consumerProduct->good_to_sell = true;
            $consumerProduct->save();
        }

        $processor = $this->getLeadProcessor();

        $processingService->reserveProduct($consumerProduct, $processor);
        $processingService->markProductAsAllocated(
            consumerProduct: $consumerProduct,
            processor: $processor,
            reason: 'Manually allocated'
        );

        $allocated = $consumerProductAllocationService->allocateConsumerProduct(
            consumerProduct: $consumerProduct,
            campaignIds: $request->validated(AllocateConsumerProductRequest::CAMPAIGN_IDS),
            saleType: SaleTypes::from($request->validated(AllocateConsumerProductRequest::SALE_TYPE)),
            qualityTier: QualityTier::STANDARD
        );

        $dispatcher->dispatch(new ConsumerProductApprovedEvent(
            $consumerProduct,
            $processor,
            'Manually allocated'
        ));

        $processingService->releaseProduct($consumerProduct, $processor);

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS => $allocated,
            self::RESPONSE_ERROR => null
        ]);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param ProductAssignment $productAssignment
     * @param UpdateSaleTypeRequest $request
     * @param SaleTypeRepository $saleTypeRepository
     * @param ProductAssignmentRepository $productAssignmentRepository
     * @param ProductBiddingService $biddingService
     * @param LocationRepository $locationRepository
     *
     * @return JsonResponse
     */
    public function updateSaleType(
        ConsumerProduct $consumerProduct,
        ProductAssignment $productAssignment,
        UpdateSaleTypeRequest $request,
        SaleTypeRepository $saleTypeRepository,
        ProductAssignmentRepository $productAssignmentRepository,
        ProductBiddingService $biddingService,
        LocationRepository $locationRepository
    ): JsonResponse
    {
        if ($consumerProduct->id !== $productAssignment->consumer_product_id) {
            throw new BadRequestException('Invalid ProductAssignment');
        }

        if ($productAssignment->invoiceItem()->exists()) {
            return $this->formatResponse([
                self::RESPONSE_KEY_STATUS => false,
                self::RESPONSE_ERROR => 'Invoice has been generated for the assignment'
            ]);
        }

        $saleType = $saleTypeRepository->getSalesTypeByName($request->validated(UpdateSaleTypeRequest::SALE_TYPE));


        if ($productAssignment->saleType->id === $saleType->id) {
            return $this->formatResponse([
                self::RESPONSE_KEY_STATUS => true,
                self::RESPONSE_ERROR => null
            ]);
        }

        $zipCodeLocation = $locationRepository->getZipCode($consumerProduct->address->zip_code);

        $propertyTypeId = $productAssignment->budget->budgetContainer->campaign->campaignPropertyTypes->first()?->id ?? PropertyType::RESIDENTIAL->model()->id;
        $qualityTierId  = $productAssignment->qualityTier?->id ?? QualityTier::STANDARD->model()->id;
        $salesTypeId    = SaleType::query()
            ->where(SaleType::FIELD_NAME, $request->validated(PriceForCampaignsRequest::SALE_TYPE))
            ->first()->id;

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS => $productAssignmentRepository->updateSaleTypeAndPrice(
                $productAssignment,
                $saleType,
                $biddingService->getProductBid(
                    companyCampaign: $productAssignment->budget->budgetContainer->campaign,
                    countyLocationId: $locationRepository->getCounty($zipCodeLocation->state_key, $zipCodeLocation->county_key)->id,
                    stateLocationId: $locationRepository->getState($zipCodeLocation->state_key)->id,
                    propertyTypeId: $propertyTypeId,
                    qualityTierId: $qualityTierId,
                    salesTypeId: $salesTypeId
                )
            ),
            self::RESPONSE_ERROR => null
        ]);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param ProductProcessingService $productProcessingService
     * @return JsonResponse
     * @throws Exception
     */
    public function lock(
        ConsumerProduct $consumerProduct,
        ProductProcessingService $productProcessingService,
    ): JsonResponse
    {
        $processor = $this->getLeadProcessor();

        return $this->formatResponse([
            'status' => $productProcessingService->reserveProduct($consumerProduct, $processor)
        ]);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param PingPostPublishService $pingPostPublishService
     * @return JsonResponse
     */
    public function getPingPostLogs(
        ConsumerProduct $consumerProduct,
        PingPostPublishService $pingPostPublishService,
    ): JsonResponse
    {
        $logs = $pingPostPublishService->getLogs($consumerProduct->id);
        $validationErrors = $pingPostPublishService->getValidationErrors($consumerProduct->id);

        $available = true;
        if (count($validationErrors) > 0)
            $available = false;

        return $this->formatResponse([
            'status'    => true,
            'logs'      => PingPostLogResource::collection($logs),
            'available' => $available,
            'validation_errors' => $validationErrors,
        ]);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @return JsonResponse
     */
    public function pingPostPingOnly(
        ConsumerProduct $consumerProduct,
    ): JsonResponse
    {
        PingPostPublishJob::dispatchSync($consumerProduct->id, [
            PingPostVariableEnum::ENABLED->value => true,
            PingPostVariableEnum::PING_ONLY_MODE->value => true,
            PingPostVariableEnum::AUTOMATION_USER_ID->value => Auth::user()->id,
        ]);
        return $this->formatResponse([
            'status'    => true,
        ]);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @return JsonResponse
     */
    public function pingPost(
        ConsumerProduct $consumerProduct,
    ): JsonResponse
    {
        PingPostPublishJob::dispatchSync($consumerProduct->id, [
            PingPostVariableEnum::ENABLED->value => true,
            PingPostVariableEnum::AUTOMATION_USER_ID->value => Auth::user()->id,
        ]);

        return $this->formatResponse([
            'status'    => true,
        ]);
    }


    /**
     * @param ConsumerProduct $consumerProduct
     * @param PingPostAffiliateService $pingPostAffiliateService
     * @return JsonResponse
     */
    public function getAffiliateInfo(
        ConsumerProduct $consumerProduct,
        PingPostAffiliateService $pingPostAffiliateService,
    ): JsonResponse
    {
        $data = $pingPostAffiliateService->getAffiliateInfo($consumerProduct);
        return $this->formatResponse([
            'status'    => true,
            'info'      => $data[PingPostAffiliateService::INFO],
            'manual'    => $data[PingPostAffiliateService::MANUAL],
            'name'      => $data[PingPostAffiliateService::NAME],
        ]);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param UpdateAffiliateLeadInternalTrackingRequest $request
     * @param PingPostAffiliateService $pingPostAffiliateService
     * @return JsonResponse
     */
    public function saveAffiliateManualTracking(
        ConsumerProduct $consumerProduct,
        UpdateAffiliateLeadInternalTrackingRequest $request,
        PingPostAffiliateService $pingPostAffiliateService,
    ): JsonResponse
    {
        $manualFields = $request->safe()->collect()[UpdateAffiliateLeadInternalTrackingRequest::MANUAL_FIELDS];

        return $this->formatResponse([
            'status' => $pingPostAffiliateService->updateManualTracking($consumerProduct->id, $manualFields),
        ]);
    }

    /**
     * @param StoreConsumerProcessingCommentRequest $request
     * @param ConsumerProcessingActivityRepository $consumerProcessingActivityRepository
     * @return JsonResponse
     */
    public function updateComment(StoreConsumerProcessingCommentRequest $request, ConsumerProcessingActivityRepository $consumerProcessingActivityRepository): JsonResponse
    {
        $validated = $request->safe()->toArray();
        /** @var User $user */
        $user = Auth::user();
        $consumerProcessingActivity = ConsumerProcessingActivity::query()
            ->where(ConsumerProcessingActivity::FIELD_ID, $validated[ConsumerProcessingActivity::FIELD_ID])
            ->where(ConsumerProcessingActivity::FIELD_USER_ID, $user->id)
            ->firstOrFail();

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS                 => $consumerProcessingActivityRepository->update($consumerProcessingActivity, $validated),
            ConsumerProcessingActivity::FIELD_COMMENT => new ConsumerProcessingActivityResource($consumerProcessingActivity->refresh()),
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getRelatedActivity(): JsonResponse
    {
        $consumerProcessingActivity = ConsumerProcessingActivity::query()
            ->findOrFail($this->request->get(ConsumerProcessingActivity::FIELD_ID));
        $relatedActivity = $consumerProcessingActivity->activity_type->hasRelatedActivity()
            ? $consumerProcessingActivity->activity
            : null;
        $resource = match($consumerProcessingActivity->activity_type) {
            ConsumerProcessingActivityType::CALL => new CallResource($relatedActivity),
            ConsumerProcessingActivityType::TEXT => new TextResource($relatedActivity),
            default => null,
        };

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS    => true,
            self::RESPONSE_ACTIVITY_TYPE => $consumerProcessingActivity->activity_type->value,
            self::RESPONSE_ACTIVITY      => $resource,
        ]);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param Collection $proposedAssignments
     * @param Collection $assignments
     *
     * @return Collection<CompanyCampaign>
     */
    protected function getAllAvailableCampaigns(ConsumerProduct $consumerProduct, Collection $proposedAssignments, Collection $assignments): Collection
    {
        /** @var AvailableCompanyCampaignRepository $availableCampaignRepository */
        $availableCampaignRepository = app(AvailableCompanyCampaignRepository::class);

        $excludeCompanyIds = collect(
            array_merge(
                $assignments->map(fn(ProductAssignment $assignment) => $assignment->company_id)->toArray(),
                $proposedAssignments->map(fn(ProposedProductAssignment $assignment) => $assignment->companyId)->toArray()
            )
        );

        return $availableCampaignRepository->getAvailableCampaignsForServiceProductAndZipCode(
            service: $consumerProduct->industryService,
            zipCode: $consumerProduct->address->zip_code,
            excludedCompanies: $excludeCompanyIds->unique()->toArray()
        );
    }
}
