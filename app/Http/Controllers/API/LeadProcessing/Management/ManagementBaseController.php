<?php

namespace App\Http\Controllers\API\LeadProcessing\Management;

use App\Enums\Alert\AlertType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\ProductProcessing\Processing\ProductProcessingBaseAPIController;
use App\Models\Alert;
use App\Models\AlertRecipient;
use App\Models\LeadProcessingCallingTimeZoneConfiguration;
use App\Models\LeadProcessingConfiguration;
use App\Models\LeadProcessingQueueConfiguration;
use App\Models\LeadProcessingTeam;
use App\Models\LeadProcessingTimeZoneConfiguration;
use App\Models\LeadProcessor;
use App\Models\Odin\Industry;
use App\Models\User;
use App\Repositories\Alert\AlertRepository;
use App\Repositories\Odin\ProductProcessing\ProductProcessingManagementRepository;
use App\Repositories\Odin\ProductProcessing\ProductProcessingQueueRepository;
use App\Repositories\Odin\ProductProcessing\ProductProcessingRepository;
use App\Repositories\UserRepository;
use App\Services\Communication\CommunicationService;
use App\Services\Odin\ProductProcessing\ProductProcessingService;
use App\Transformers\Odin\IndustryTransformer;
use App\Transformers\LeadProcessing\CallingTimeZoneConfigurationTransformer;
use App\Transformers\LeadProcessing\LeadProcessorTransformer;
use App\Transformers\LeadProcessing\QueueTransformer;
use App\Transformers\LeadProcessing\TeamTransformer;
use App\Transformers\LeadProcessing\TimeZoneConfigurationTransformer;
use App\Transformers\LeadProcessing\UserTransformer;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

/**
 * The base controller that all controllers in the managing api should inherit.
 */
class ManagementBaseController extends ProductProcessingBaseAPIController
{
    const string REQUEST_NAME        = 'name';
    const string REQUEST_STATUS      = 'status';
    const string REQUEST_QUEUE_ID    = 'queue_id';
    const string REQUEST_OFFSET      = 'offset';
    const string REQUEST_USER_ID     = 'user_id';
    const string REQUEST_TEAM_ID     = 'team_id';
    const string REQUEST_PHONE_ID    = 'phone_id';
    const string REQUEST_INDUSTRY    = 'industries';
    const string REQUEST_INDUSTRY_ID = 'industry_id';
    const string REQUEST_UTC_OFFSET  = 'utc_offset';

    const string REQUEST_ALERT_TYPE = 'alert_type';
    const string REQUEST_ALERT_PAYLOAD = 'alert_payload';
    const string REQUEST_ALERT_RECIPIENTS = 'alert_recipients';
    const string REQUEST_ALERT_ACTIVE = 'alert_active';

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        ProductProcessingRepository $productProcessingRepository,
        ProductProcessingService $productProcessingService,
        CommunicationService $communicationService,
        protected ProductProcessingManagementRepository $managementRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory, $productProcessingRepository, $productProcessingService, $communicationService);
    }

    /**
     * Returns the list of lead processing queues.
     *
     * @param QueueTransformer $transformer
     * @return JsonResponse
     */
    public function getQueues(QueueTransformer $transformer): JsonResponse
    {
        $queues = $this->managementRepository->getAllQueues();

        return $this->formatResponse([
            "status" => true,
            "queues" => $transformer->transformQueueConfigurations($queues)
        ]);
    }

    /**
     * Handles creating a queue.
     *
     * @return JsonResponse
     */
    public function createQueue(): JsonResponse
    {
        if (!$this->managementRepository->createQueue($this->request->get(self::REQUEST_NAME, ""), $this->request->get(self::REQUEST_STATUS, "")))
            return $this->formatResponse(["status" => false]);

        return $this->formatResponse([
            "status" => true,
        ]);
    }

    /**
     * Handles updating a given queue.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function updateQueue(int $id): JsonResponse
    {
        if (!$this->managementRepository->updateQueue($id, $this->request->get(self::REQUEST_NAME, ""), $this->request->get(self::REQUEST_STATUS, "")))
            return $this->formatResponse(["status" => false]);

        return $this->formatResponse([
            "status" => true,
        ]);
    }

    /**
     * Handles deleting a queue.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function deleteQueue(int $id): JsonResponse
    {
        if (!$this->managementRepository->deleteQueue($id))
            return $this->formatResponse(["status" => false]);

        return $this->formatResponse([
            "status" => true,
        ]);
    }

    /**
     * Returns a list of the lead processing teams.
     *
     * @param TeamTransformer $transformer
     * @return JsonResponse
     */
    public function getTeams(TeamTransformer $transformer): JsonResponse
    {
        $teams = $this->managementRepository->getTeams();

        return $this->formatResponse([
            "status" => true,
            "teams"  => $transformer->transformLeadProcessingTeams($teams)
        ]);
    }

    /**
     * Returns the data for a given lead processing team.
     *
     * @param int $id
     * @param TeamTransformer $transformer
     * @return JsonResponse
     */
    public function getTeam(int $id, TeamTransformer $transformer): JsonResponse
    {
        if (!($team = $this->managementRepository->getTeam($id)))
            return $this->formatResponse(["status" => false]);

        return $this->formatResponse([
            "status" => true,
            "team"   => $transformer->transformLeadProcessingTeam($team)
        ]);
    }

    /**
     * Handles creating a team.
     *
     * @return JsonResponse
     */
    public function createTeam(): JsonResponse
    {
        $name     = trim($this->request->get(self::REQUEST_NAME, ""));
        $queueId  = (int)$this->request->get(self::REQUEST_QUEUE_ID, 0);
        $offset   = (int)$this->request->get(self::REQUEST_OFFSET, 0);
        $industries = $this->request->get(self::REQUEST_INDUSTRY, []);

        if (!$this->managementRepository->createTeam($name, $queueId, $offset, $industries)) {
            return $this->formatResponse(["status" => false]);
        }

        return $this->formatResponse([
            "status" => true,
        ]);
    }

    /**
     * Handles updating a team.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function updateTeam(int $id): JsonResponse
    {
        $name     = trim($this->request->get(self::REQUEST_NAME, ""));
        $queueId  = (int)$this->request->get(self::REQUEST_QUEUE_ID, 0);
        $offset   = (int)$this->request->get(self::REQUEST_OFFSET, 0);
        $industries = $this->request->get(self::REQUEST_INDUSTRY, []);

        if (!$this->managementRepository->updateTeam($id, $name, $queueId, $offset, $industries)) {
            return $this->formatResponse(["status" => false]);
        }

        return $this->formatResponse([
            "status" => true,
        ]);
    }

    /**
     * Handles deleting a team.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function deleteTeam(int $id): JsonResponse
    {
        if (!$this->managementRepository->deleteTeam($id))
            return $this->formatResponse(["status" => false]);

        return $this->formatResponse([
            "status" => true,
        ]);
    }

    /**
     * Returns a list of the lead processors.
     *
     * @param LeadProcessorTransformer $transformer
     * @return JsonResponse
     */
    public function getProcessors(LeadProcessorTransformer $transformer): JsonResponse
    {
        $processors = $this->managementRepository->getProcessors();

        return $this->formatResponse([
            "status"     => true,
            "processors" => $transformer->transformProcessors($processors)
        ]);
    }

    /**
     * Returns the data for a given lead processor.
     *
     * @param int $id
     * @param LeadProcessorTransformer $transformer
     * @return JsonResponse
     */
    public function getProcessor(int $id, LeadProcessorTransformer $transformer): JsonResponse
    {
        if (!($processor = $this->managementRepository->getProcessor($id)))
            return $this->formatResponse(["status" => false]);

        return $this->formatResponse([
            "status"    => true,
            "processor" => $transformer->transformProcessor($processor)
        ]);
    }

    /**
     * Handles creating a processor.
     *
     * @return JsonResponse
     */
    public function createProcessor(): JsonResponse
    {
        $userId = (int)$this->request->get(self::REQUEST_USER_ID, 0);
        $teamId = (int)$this->request->get(self::REQUEST_TEAM_ID, LeadProcessingTeam::NO_TEAM_ASSIGNED_ID);
        $phoneId = (int)$this->request->get(self::REQUEST_PHONE_ID, -1);

        if (!empty($this->managementRepository->getProcessorByUserId($userId))) {
            return $this->formatResponse(["status" => false]);
        }

        if (!$this->managementRepository->createProcessor($userId, $teamId, $phoneId)) {
            return $this->formatResponse(["status" => false]);
        }

        return $this->formatResponse([
            "status" => true,
        ]);
    }

    /**
     * Handles updating a processor.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function updateProcessor(int $id): JsonResponse
    {
        $userId = (int)$this->request->get(self::REQUEST_USER_ID, 0);
        $teamId = (int)$this->request->get(self::REQUEST_TEAM_ID, LeadProcessingTeam::NO_TEAM_ASSIGNED_ID);
        $phoneId = (int)$this->request->get(self::REQUEST_PHONE_ID, -1);

        $currentProcessor = $this->managementRepository->getProcessor($id);

        if ($currentProcessor->{LeadProcessor::FIELD_USER_ID} !== $userId
            && !empty($this->managementRepository->getProcessorByUserId($userId))) {
            return $this->formatResponse(["status" => false]);
        }

        if (!$this->managementRepository->updateProcessor($id, $userId, $teamId, $phoneId)) {
            return $this->formatResponse(["status" => false]);
        }

        return $this->formatResponse([
            "status" => true,
        ]);
    }

    /**
     * Handles deleting a processor.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function deleteProcessor(int $id): JsonResponse
    {
        if (!$this->managementRepository->deleteProcessor($id))
            return $this->formatResponse(["status" => false]);

        return $this->formatResponse([
            "status" => true,
        ]);
    }

    /**
     * @param ProductProcessingQueueRepository $queueRepository
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function getStatistics(ProductProcessingQueueRepository $queueRepository): JsonResponse
    {
        $industryId = $this->request->get(self::REQUEST_INDUSTRY_ID);
        $utcOffset = $this->request->get(self::REQUEST_UTC_OFFSET);

        if ($utcOffset) {
            $utcOffset = $queueRepository->isActiveDST() ? $utcOffset + 1 : $utcOffset;
            $utcOffsets = [[(int) $utcOffset]];
        } else {
            $utcOffsets = [-4, -5, -6, -7, -8, -9, -10]; // All US UTC offsets
        }

        $statistics = $industryId > -1
            ? $this->managementRepository->getIndustryStatistics($industryId, $utcOffsets)
            : [];

        return $this->formatResponse([
            "status"     => !!$statistics,
            "statistics" => $statistics
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function searchAdminUsers(
        Request $request,
        UserRepository $userRepository,
        UserTransformer $userTransformer
    ): JsonResponse
    {
        $name = trim($request->input("name", ""));

        $validator = Validator::make(
            ['name' => $name],
            ['name' => 'required|string|regex:/^[a-zA-Z\s]+$/']
        );

        if ($validator->fails()) {
            return $this->formatResponse([
                "status" => false
            ]);
        }

        $users = $userRepository->getAdminUsersByName($name, [User::FIELD_ID, User::FIELD_NAME]);

        return $this->formatResponse([
            "status" => true,
            "users"  => $userTransformer->transformUsers($users)
        ]);
    }

    /**
     * @param IndustryTransformer $industryTransformer
     * @return JsonResponse
     */
    public function getIndustries(IndustryTransformer $industryTransformer): JsonResponse
    {
        return $this->formatResponse([
            "status"     => true,
            "industries" => $industryTransformer->transform(Industry::all())
        ]);
    }

    /**
     * Handles fetching the bounced leads for display on the client side.
     * @return JsonResponse
     */
    public function getBouncedLeads(): JsonResponse
    {
        $leads = $this->managementRepository->getBouncedLeads();

        return $this->formatResponse([
            "status" => true,
            "leads"  => $leads->toArray(),
        ]);
    }

    /**
     * @param TimeZoneConfigurationTransformer $sellingTransformer
     * @param CallingTimeZoneConfigurationTransformer $callingTransformer
     * @return JsonResponse
     */
    public function getGlobalConfigs(TimeZoneConfigurationTransformer $sellingTransformer, CallingTimeZoneConfigurationTransformer $callingTransformer): JsonResponse
    {
        $configs = $this->productProcessingRepository->getLeadProcessingConfiguration()?->toArray();

        $configs[LeadProcessingTimeZoneConfiguration::TABLE] =
            $sellingTransformer->transformTimeZones($this->managementRepository->getAllTimezoneConfigurations());
        $configs[LeadProcessingCallingTimeZoneConfiguration::TABLE] =
            $callingTransformer->transformTimeZones($this->managementRepository->getAllCallingTimezoneConfigurations());

        return $this->formatResponse([
            "status"  => true,
            "configs" => $configs
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function saveGlobalConfigs(Request $request): JsonResponse
    {
        $timeZoneOpeningDelayMin = (int)$request->input(LeadProcessingConfiguration::FIELD_TIME_ZONE_OPENING_DELAY_IN_MINUTES, LeadProcessingConfiguration::TIME_ZONE_OPENING_DELAY_DEFAULT);
        $leadRecencyThresholdSec = (int)$request->input(LeadProcessingConfiguration::FIELD_LEAD_RECENCY_THRESHOLD_IN_SECONDS, LeadProcessingConfiguration::LEAD_RECENCY_THRESHOLD_DEFAULT);
        $minimumReviewTime       = (int)$request->input(LeadProcessingConfiguration::FIELD_MINIMUM_REVIEW_TIME, LeadProcessingConfiguration::MINIMUM_REVIEW_TIME_DEFAULT);
        $leadProcessableDelaySec = (int)$request->input(LeadProcessingConfiguration::FIELD_LEAD_PROCESSABLE_DELAY_SECONDS, LeadProcessingConfiguration::LEAD_PROCESSABLE_DELAY_DEFAULT);
        $checkNextLeadInterval   = (int)$request->input(LeadProcessingConfiguration::FIELD_CHECK_NEXT_LEAD_INTERVAL_SECONDS, LeadProcessingConfiguration::CHECK_NEXT_LEAD_INTERVAL_DEFAULT);
        $lastLeadCreatedInterval = (int)$request->input(LeadProcessingConfiguration::FIELD_LAST_LEAD_CREATED_INTERVAL_MIN, LeadProcessingConfiguration::LAST_LEAD_CREATED_INTERVAL_DEFAULT);
        $timezoneConfigurations  = array_filter((array)$request->input(LeadProcessingTimeZoneConfiguration::TABLE, []));
        $callingTimezoneConfigurations = array_filter((array)$request->input(LeadProcessingCallingTimeZoneConfiguration::TABLE, []));

        $validator = Validator::make(
            [
                LeadProcessingConfiguration::FIELD_TIME_ZONE_OPENING_DELAY_IN_MINUTES => $timeZoneOpeningDelayMin,
                LeadProcessingConfiguration::FIELD_LEAD_RECENCY_THRESHOLD_IN_SECONDS  => $leadRecencyThresholdSec,
                LeadProcessingConfiguration::FIELD_MINIMUM_REVIEW_TIME                => $minimumReviewTime,
                LeadProcessingConfiguration::FIELD_LEAD_PROCESSABLE_DELAY_SECONDS     => $leadProcessableDelaySec,
                LeadProcessingConfiguration::FIELD_CHECK_NEXT_LEAD_INTERVAL_SECONDS   => $checkNextLeadInterval,
                LeadProcessingConfiguration::FIELD_LAST_LEAD_CREATED_INTERVAL_MIN     => $lastLeadCreatedInterval
            ],
            [
                LeadProcessingConfiguration::FIELD_TIME_ZONE_OPENING_DELAY_IN_MINUTES => 'required|integer|min:0',
                LeadProcessingConfiguration::FIELD_LEAD_RECENCY_THRESHOLD_IN_SECONDS  => 'required|integer|min:0',
                LeadProcessingConfiguration::FIELD_MINIMUM_REVIEW_TIME                => 'required|integer|min:0',
                LeadProcessingConfiguration::FIELD_LEAD_PROCESSABLE_DELAY_SECONDS     => 'required|integer|min:0',
                LeadProcessingConfiguration::FIELD_CHECK_NEXT_LEAD_INTERVAL_SECONDS   => 'required|integer|min:0',
                LeadProcessingConfiguration::FIELD_LAST_LEAD_CREATED_INTERVAL_MIN     => 'required|integer|min:0'
            ]
        );

        if ($validator->fails()) {
            return $this->formatResponse([
                "status" => false
            ]);
        }

        $timezoneValidator = Validator::make(
            [
                //purposefully blank, data is set in the foreach loop
            ],
            [
                LeadProcessingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET => 'required|integer',
                LeadProcessingTimeZoneConfiguration::FIELD_NAME                => 'required|string',
                LeadProcessingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR     => 'required|integer|between:1,23',
                LeadProcessingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR    => 'required|integer|between:1,23'
            ]
        );

        foreach ($timezoneConfigurations as $configuration) {
            $timezoneValidator->setData([
                LeadProcessingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET => $configuration[LeadProcessingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET],
                LeadProcessingTimeZoneConfiguration::FIELD_NAME                => $configuration[LeadProcessingTimeZoneConfiguration::FIELD_NAME],
                LeadProcessingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR     => $configuration[LeadProcessingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR],
                LeadProcessingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR    => $configuration[LeadProcessingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR]
            ]);

            if ($timezoneValidator->fails()) {
                return $this->formatResponse([
                    "status" => false
                ]);
            }
        }

        foreach ($callingTimezoneConfigurations as $configuration) {
            $timezoneValidator->setData([
                LeadProcessingCallingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET => $configuration[LeadProcessingCallingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET],
                LeadProcessingCallingTimeZoneConfiguration::FIELD_NAME                => $configuration[LeadProcessingCallingTimeZoneConfiguration::FIELD_NAME],
                LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR     => $configuration[LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR],
                LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR    => $configuration[LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR]
            ]);

            if ($timezoneValidator->fails()) {
                return $this->formatResponse([
                    "status" => false
                ]);
            }
        }

        return $this->formatResponse([
            "status" => $this->managementRepository->saveLeadProcessingConfiguration($timeZoneOpeningDelayMin, $leadRecencyThresholdSec, $minimumReviewTime, $leadProcessableDelaySec, $checkNextLeadInterval, $lastLeadCreatedInterval)
                && $this->managementRepository->saveTimezoneConfigurations(collect($timezoneConfigurations))
                && $this->managementRepository->saveCallingTimezoneConfigurations(collect($callingTimezoneConfigurations))
        ]);
    }

    /**
     * @param LeadProcessingQueueConfiguration $leadProcessingQueueConfiguration
     *
     * @return JsonResponse
     */
    public function getAlerts(LeadProcessingQueueConfiguration $leadProcessingQueueConfiguration): JsonResponse
    {
        return $this->formatResponse([
            'alerts' => $leadProcessingQueueConfiguration->alerts()->get()->map(fn(Alert $alert) => [
                Alert::FIELD_ID => $alert->id,
                Alert::FIELD_TYPE => $alert->type->value,
                'type_readable' => $alert->type->toReadableString(),
                Alert::FIELD_PAYLOAD => $alert->payload,
                Alert::FIELD_ACTIVE => $alert->active,
                'recipients' => $alert->recipients->map(fn(AlertRecipient $alertRecipient) => $alertRecipient->notifiable->only(['id']))->pluck('id')
            ]),
            'recipients' => User::query()->get()->map(fn(User $user) => $user->only([User::FIELD_ID, User::FIELD_NAME]))
        ]);
    }

    /**
     * @param LeadProcessingQueueConfiguration $leadProcessingQueueConfiguration
     * @param AlertRepository $alertRepository
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function createAlert(LeadProcessingQueueConfiguration $leadProcessingQueueConfiguration, AlertRepository $alertRepository): JsonResponse
    {
        $this->validateAlertRequest();

        return $this->formatResponse([
            'status' => !! $alertRepository->createOrUpdateQueueAlert(
                queue: $leadProcessingQueueConfiguration,
                type: AlertType::from($this->request->get(self::REQUEST_ALERT_TYPE)),
                active: $this->request->get(self::REQUEST_ALERT_ACTIVE),
                recipients: $this->request->get(self::REQUEST_ALERT_RECIPIENTS),
                payload: $this->request->get(self::REQUEST_ALERT_PAYLOAD)
            )
        ]);
    }

    /**
     * @param LeadProcessingQueueConfiguration $leadProcessingQueueConfiguration
     * @param Alert $alert
     * @param AlertRepository $alertRepository
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function updateAlert(LeadProcessingQueueConfiguration $leadProcessingQueueConfiguration, Alert $alert, AlertRepository $alertRepository): JsonResponse
    {
        $this->validateAlertRequest();

        return $this->formatResponse([
            'status' => !! $alertRepository->createOrUpdateQueueAlert(
                queue: $leadProcessingQueueConfiguration,
                type: AlertType::from($this->request->get(self::REQUEST_ALERT_TYPE)),
                active: $this->request->get(self::REQUEST_ALERT_ACTIVE),
                recipients: $this->request->get(self::REQUEST_ALERT_RECIPIENTS),
                payload: $this->request->get(self::REQUEST_ALERT_PAYLOAD),
                alert: $alert
            )
        ]);
    }

    /**
     * @param LeadProcessingQueueConfiguration $leadProcessingQueueConfiguration
     * @param Alert $alert
     *
     * @return JsonResponse
     */
    public function deleteAlert(LeadProcessingQueueConfiguration $leadProcessingQueueConfiguration, Alert $alert): JsonResponse
    {
        return $this->formatResponse([
            'status' => !! $alert->delete()
        ]);
    }

    /**
     * @return void
     * @throws ValidationException
     */
    protected function validateAlertRequest(): void
    {
        $this->validate($this->request, [
            self::REQUEST_ALERT_TYPE => ['required', Rule::enum(AlertType::class)],
            self::REQUEST_ALERT_PAYLOAD => 'required:array',
            self::REQUEST_ALERT_RECIPIENTS => 'required:array',
            self::REQUEST_ALERT_ACTIVE => 'required:boolean'
        ]);
    }
}
