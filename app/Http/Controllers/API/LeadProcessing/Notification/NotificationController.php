<?php

namespace App\Http\Controllers\API\LeadProcessing\Notification;

use App\Http\Controllers\API\ProductProcessing\Processing\ProductProcessingBaseAPIController;
use App\Models\LeadProcessor;
use App\Repositories\NotificationRepository;
use App\Services\Broadcasting\PusherNotificationBroadcaster;
use App\Services\LeadProcessing\LeadProcessorNotificationService;
use App\Transformers\LeadProcessing\LeadProcessorNotificationTransformer;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\UnauthorizedException;

class NotificationController extends ProductProcessingBaseAPIController
{
    const REQUEST_PROCESSOR_ID = 'processor_id';
    const REQUEST_USER_ID = 'user_id';
    const REQUEST_SOCKET_ID    = 'socket_id';
    const REQUEST_CHANNEL_NAME = 'channel_name';
    const REQUEST_TYPE = 'type';

    /**
     * Handles authenticating a user against the pusher auth.
     *
     * @param PusherNotificationBroadcaster $pusher
     * @return JsonResponse
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     * @throws \Pusher\PusherException
     */
    public function authenticate(PusherNotificationBroadcaster $pusher): JsonResponse
    {

        $processorId = request()->get(self::REQUEST_USER_ID, null);
        $socket      = request()->get(self::REQUEST_SOCKET_ID);
        $channel     = request()->get(self::REQUEST_CHANNEL_NAME);
        $channelId   = intval(collect(explode('-', $channel))->last());

        if(Auth::id() !== intval($processorId)) {
            throw new UnauthorizedException();
        }

        return response()
            ->json(json_decode($pusher->getPusherInstance()->socketAuth($channel, $socket)));
    }

    /**
     * Returns recent notifications 100 at a time for the requesting processor
     *
     * @param LeadProcessorNotificationService $notificationService
     * @param LeadProcessorNotificationTransformer $transformer
     * @param NotificationRepository $notificationRepository
     * @param Request|null $request
     * @return JsonResponse
     */
    public function getRecentNotificationsNextPaginated(LeadProcessorNotificationService $notificationService,
                                                    LeadProcessorNotificationTransformer $transformer,
                                                    NotificationRepository $notificationRepository,
                                                    ?Request $request): JsonResponse
    {
        $offsetVal = null;

        if ($request->query->has(0)) {$offset = $request->query->get(0);

            if (!empty($offset)) {
                $offsetVal = $offset;
            }
        }

        $userId                 = Auth::id();
        $processorId            = $this->getLeadProcessor()?->{LeadProcessor::FIELD_ID};
        $processorNotifications = $processorId ? $notificationService->getRecentProcessorNotifications($processorId) : collect();
        $userNotifications      = $notificationRepository->getNotificationsForUserPaginated($userId, $offsetVal);
        $userNotificationsTotalCount = $notificationRepository->getTotalNotificationCount($userId);
        $userNotificationsCount = $notificationRepository->getUnreadNotificationCount($userId);

        return $this->formatResponse([
            "result"        => true,
            "notifications" => $transformer->transformGenericNotifications($processorNotifications->concat($userNotifications)->toBase()->sortByDesc(Model::CREATED_AT)),
            "total_count"   => $userNotificationsTotalCount,
            "unread_count"  => $userNotificationsCount,
        ]);
    }

    /**
     * Handles updating a notification and marking it as read.
     *
     * @param LeadProcessorNotificationService $service
     * @param int $id
     * @return JsonResponse
     */
    public function markAsRead(LeadProcessorNotificationService $service, int $id, NotificationRepository $notificationRepository): JsonResponse
    {
        $processorId = $this->getLeadProcessor()?->id;
        return $this->formatResponse([
            "result" => $service->markNotificationAsRead($id, $processorId, request()->get('type')),
            "unread_count" => $notificationRepository->getUnreadNotificationCount(Auth::id())
        ]);
    }

    /**
     * @param LeadProcessorNotificationService $service
     * @return JsonResponse
     */
    public function markAllNotificationsAsRead(LeadProcessorNotificationService $service): JsonResponse
    {
        return $this->formatResponse([
            "result" => $service->markAllNotificationsAsRead($this->getLeadProcessor()?->id),
        ]);
    }
}
