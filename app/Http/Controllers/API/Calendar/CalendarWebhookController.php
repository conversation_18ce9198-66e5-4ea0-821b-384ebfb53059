<?php

namespace App\Http\Controllers\API\Calendar;

use App\Http\Controllers\Controller;
use App\Jobs\Calendar\HandleCalendarEventJob;

class CalendarWebhookController extends Controller
{
    public function __construct()
    {

    }

    /**
     * @return array
     */
    public function handleEvent(): array
    {
        $channelId = request()->header('x-goog-channel-id');
        $state = request()->header('x-goog-resource-state');

        // state is sync when it just got created
        if ($state === 'exists') {
            HandleCalendarEventJob::dispatch($channelId);
        }

        return [];
    }
}
