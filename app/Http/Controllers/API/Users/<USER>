<?php

namespace App\Http\Controllers\API\Users;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\ListUsersRequest;
use App\Http\Requests\SearchUsersRequest;
use App\Http\Requests\UpdateUserActionRequest;
use App\Http\Resources\Odin\UserResource;
use App\Http\Resources\UserActionResource;
use App\Models\User;
use App\Models\UserAction;
use App\Repositories\UserActionRepository;
use App\Repositories\UserRepository;
use App\Services\Odin\UserService;
use App\Transformers\UserTransformer;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class UserBaseAPIController extends APIController
{
    const REQUEST_FILTER_BASIC_INFO = 'filter_basic_info';
    const REQUEST_SEARCH_QUERY      = 'query';

    //todo: permission needs to be updated
    const MODULE_PERMISSION = 'dashboard';

    const ACTIVITY_LIMIT_PER_PAGE       = 10;

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param UserRepository $repository
     * @param UserService $userService
     */
    public function __construct(
        Request                  $request,
        JsonAPIResponseFactory   $apiResponseFactory,
        protected UserRepository $repository,
        protected UserService $userService
    ) { parent::__construct($request, $apiResponseFactory); }

    /**
     * @param Request $request
     * @param UserTransformer $userTransformer
     * @return JsonResponse
     */
    public function getUsers(Request $request, UserTransformer $userTransformer) : JsonResponse
    {
        $users = $this->repository->getUsers();

        return $this->formatResponse([
            "status" => true,
            "users" => $request->input(self::REQUEST_FILTER_BASIC_INFO) ?
                $userTransformer->transformUsers($users) :
                UserResource::collection($users)
        ]);
    }

    /**
     * @param SearchUsersRequest $request
     * @param UserTransformer    $transformer
     * @return JsonResponse
     */
    public function searchUsersByNameOrId(SearchUsersRequest $request, UserTransformer $transformer): JsonResponse
    {
        $searchRequest = $request->safe()->collect();

        /** @var Collection<User> $users */
        $users = $this->repository->searchUsersByNameOrId($searchRequest->get(self::REQUEST_SEARCH_QUERY));

        if($searchRequest->get(self::REQUEST_FILTER_BASIC_INFO)) {
            $users = $transformer->transformUsers($users);
        }

        return $this->formatResponse([
            "status" => true,
            "users"  => $users
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getUserRolesAndPermissions(): JsonResponse
    {
        /** @var User|null $user */
        $user = Auth::user();

        return $this->formatResponse([
            'roles'       => $user->roles()->get()->map(fn(Role $role) => $role->name),
            'permissions' => $user->getAllPermissions()->map(fn(Permission $permission) => $permission->name),
            'ids'         => $this->getUserIds($user),
        ]);
    }

    /**
     * Retrieve user's role ids
     * @param User $user
     * @return array
     */
    private function getUserIds(User $user): array
    {
        return [
            'user' => $user->id,
            'accountManager' => null,
            'successManager' => $user->successManager?->id ?? null,
        ];
    }

    public function saveUserAction(?int $userId, UpdateUserActionRequest $request, UserActionRepository $repository) : JsonResponse
    {
        $data = $request->safe()->collect();

        $userAction = $repository->updateOrCreate(
            $userId,
            $data->get(UserAction::FIELD_SUBJECT),
            $data->get(UserAction::FIELD_MESSAGE),
            $data->get(UpdateUserActionRequest::REQUEST_ACTION_ID),
            $data->get(UserAction::FIELD_DISPLAY_DATE),
            $data->get(UserAction::FIELD_TAG_BY_EMAIL)
        );

        if (!is_null($data->get(UserAction::RELATION_TAGS))) {
            $repository->editActionTags($userAction, $data->get(UserAction::RELATION_TAGS));
        }

        return $this->formatResponse([
            'status' => true,
            'action' => new UserActionResource($userAction)
        ]);
    }

    public function getUserActions(int $userId): JsonResponse
    {
        /** @var User $user */
        $user = User::query()->findOrFail($userId);

        return $this->formatResponse([
            "status"  => true,
            "actions" => $user->actions()
                ->paginate(self::ACTIVITY_LIMIT_PER_PAGE, ['*'], 'page', $this->request->get('page'))
                ->through(fn(UserAction $action) => new UserActionResource($action))
        ]);
    }

    /**
     * @param ListUsersRequest $request
     * @return AnonymousResourceCollection
     */
    public function listUsers(ListUsersRequest $request): AnonymousResourceCollection
    {
        $validated = $request->validated();

        $users = $this->userService->listUsers(
            showDeactivated : Arr::get($validated, ListUsersRequest::REQUEST_SHOW_DEACTIVATED),
            userDetail      : Arr::get($validated, ListUsersRequest::REQUEST_USER_DETAIL),
        );

        return UserResource::collection($users);

    }

    /**
     * @return UserResource
     */
    public function getLoggedUser(): UserResource
    {
        return new UserResource(auth()->user());
    }
}
