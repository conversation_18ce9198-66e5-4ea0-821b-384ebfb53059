<?php

namespace App\Http\Controllers\API\Users\Management;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\Users\UserBaseAPIController;
use App\Http\Requests\Odin\SaveUserRequest;
use App\Models\Phone;
use App\Models\User;
use App\Repositories\LeadProcessing\LeadCommunicationRepository;
use App\Repositories\UserRepository;
use App\Services\Odin\API\OdinAuthoritativeAPILegacySyncService;
use App\Services\Odin\UserService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class UserManagementAPIController extends UserBaseAPIController
{
    const REQUEST_NAME = 'name';
    const REQUEST_EMAIL = 'email';
    const REQUEST_ROLES = 'roles';
    const REQUEST_PHONE = 'phone';
    const REQUEST_LEGACY_USER_ID = 'legacy_user_id';
    const REQUEST_SLACK_USERNAME = 'slack_username';

    const REQUEST_CREATE_LEGACY_USER = 'create_legacy_user';
    const REQUEST_FORCE_TWO_FACTOR = 'force_two_factor_auth';

    const RESPONSE_KEY_STATUS = 'status';

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        UserRepository $repository,
        UserService $userService,
        protected OdinAuthoritativeAPILegacySyncService $legacySyncService
    )
    {
        parent::__construct($request, $apiResponseFactory, $repository, $userService);
    }

    /**
     * Handles returning the available numbers.
     *
     * @param LeadCommunicationRepository $repository
     * @return JsonResponse
     */
    public function getAvailableNumbers(LeadCommunicationRepository $repository): JsonResponse
    {
        $numbers = $repository->getAvailableNumbers()->map(fn(Phone $phone) => ["id" => $phone->id, "number" => $phone->phone]);

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS => true,
            "numbers" => $numbers
        ]);
    }

    /**
     * @param SaveUserRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function createUser(SaveUserRequest $request): JsonResponse
    {
        $payload = $request->validated();

        $this->userService->saveUser(
            name          : Arr::get($payload, SaveUserRequest::FIELD_NAME),
            email         : Arr::get($payload, SaveUserRequest::FIELD_EMAIL),
            force2fa      : Arr::get($payload, SaveUserRequest::FIELD_FORCE_TWO_FACTOR_AUTH),
            createInLegacy: Arr::get($payload, SaveUserRequest::FIELD_CREATE_LEGACY_USER, false),
            roles         : Arr::get($payload, SaveUserRequest::FIELD_ROLES),
            phoneId       : Arr::get($payload, SaveUserRequest::FIELD_PHONE),
            legacyId      : Arr::get($payload, SaveUserRequest::FIELD_LEGACY_USER_ID),
            slackUsername : Arr::get($payload, SaveUserRequest::FIELD_SLACK_USERNAME),
            permissions   : Arr::get($payload, SaveUserRequest::FIELD_PERMISSIONS)
        );

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS => true
        ]);
    }

    /**
     * @param SaveUserRequest $request
     * @param User $user
     * @return JsonResponse
     * @throws Exception
     */
    public function updateUser(SaveUserRequest $request, User $user): JsonResponse
    {
        $payload = $request->validated();

        $this->userService->saveUser(
            name          : Arr::get($payload, SaveUserRequest::FIELD_NAME),
            email         : Arr::get($payload, SaveUserRequest::FIELD_EMAIL),
            force2fa      : Arr::get($payload, SaveUserRequest::FIELD_FORCE_TWO_FACTOR_AUTH),
            createInLegacy: Arr::get($payload, SaveUserRequest::FIELD_CREATE_LEGACY_USER, false),
            roles         : Arr::get($payload, SaveUserRequest::FIELD_ROLES),
            phoneId       : Arr::get($payload, SaveUserRequest::FIELD_PHONE),
            legacyId      : Arr::get($payload, SaveUserRequest::FIELD_LEGACY_USER_ID),
            slackUsername : Arr::get($payload, SaveUserRequest::FIELD_SLACK_USERNAME),
            user          : $user,
            permissions   : Arr::get($payload, SaveUserRequest::FIELD_PERMISSIONS)
        );

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS => true
        ]);
    }


    /**
     * @param User $user
     * @return JsonResponse
     */
    public function deleteUser(User $user) : JsonResponse
    {
        $user->delete();

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS => true
        ]);
    }
}
