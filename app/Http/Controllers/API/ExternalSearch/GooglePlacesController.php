<?php

namespace App\Http\Controllers\API\ExternalSearch;

use App\Enums\Odin\Industry;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\GetCompaniesByCountyRequest;
use App\Http\Requests\GetCompaniesByZipCodeRequest;
use App\Models\Legacy\Location;
use App\Repositories\LocationRepository;
use App\Services\ExternalSearch\GooglePlacesService;
use Google\Cloud\Core\Exception\BadRequestException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class GooglePlacesController extends APIController
{
    const REQUEST_COUNTY_KEY  = 'county_key';
    const REQUEST_STATE_KEY   = 'state_key';
    const REQUEST_INDUSTRY    = 'industry';
    const REQUEST_ZIPCODE     = 'zip_code';

    /** @var GooglePlacesService $googlePlacesService */
    protected GooglePlacesService $googlePlacesService;

    /** @var LocationRepository $locationRepository */
    protected LocationRepository $locationRepository;

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param GooglePlacesService $googlePlacesService
     * @param LocationRepository $locationRepository
     */
    public function __construct(
        Request                $request,
        JsonAPIResponseFactory $apiResponseFactory,
        GooglePlacesService    $googlePlacesService,
        LocationRepository     $locationRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);

        $this->googlePlacesService = $googlePlacesService;
        $this->locationRepository = $locationRepository;
    }

    /**
     * @param GetCompaniesByZipCodeRequest $request
     * @return JsonResponse
     */
    public function getCompaniesByZipCode(GetCompaniesByZipCodeRequest $request): JsonResponse
    {
        $requestedData = $request->safe()->collect();

        $industry = $requestedData->get(self::REQUEST_INDUSTRY) ?? Industry::SOLAR->getSlug();
        $zipCode  = $requestedData->get(self::REQUEST_ZIPCODE);

        $companies = $this->googlePlacesService->getResultsByZipCodeAndIndustry($zipCode, $industry);

        return $this->formatResponse([
            'companies' => $companies
        ]);
    }

    /**
     * @param GetCompaniesByCountyRequest $request
     * @return JsonResponse
     * @throws BadRequestException
     */
    public function getCompaniesByCounty(GetCompaniesByCountyRequest $request): JsonResponse
    {
        $requestedData = $request->safe()->collect();

        $industry  = $requestedData->get(self::REQUEST_INDUSTRY) ?? Industry::SOLAR->getSlug();
        $countyKey = $requestedData->get(self::REQUEST_COUNTY_KEY);
        $stateKey  = $requestedData->get(self::REQUEST_STATE_KEY);

        $zipCode = $this->locationRepository->getZipCodesInCounty($countyKey, $stateKey)->first()?->{Location::ZIP_CODE};
        if(!$zipCode) {
            throw new BadRequestException("Oops, an invalid combination of county `$countyKey` & state `$stateKey` detected.");
        }

        $companies = $this->googlePlacesService->getResultsByZipCodeAndIndustry($zipCode, $industry, true);

        return $this->formatResponse([
            'companies' => $companies
        ]);
    }
}
