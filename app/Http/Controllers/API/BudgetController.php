<?php

namespace App\Http\Controllers\API;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Resources\Odin\CompanyBudgetUsageOverviewResource;
use App\Services\Campaigns\CompanyCampaignService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class BudgetController extends APIController
{
    public function __construct(Request $request, JsonAPIResponseFactory $apiResponseFactory, protected CompanyCampaignService $companyCampaignService)
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param int $companyId
     * @return JsonResponse
     */
    public function getCompanyBudgetUsage(int $companyId): JsonResponse
    {
        $usage = $this->companyCampaignService->getCompanyBudgetUsage(companyId: $companyId);

        return $this->formatResponse([
            'data' => new CompanyBudgetUsageOverviewResource($usage)
        ]);
    }

}