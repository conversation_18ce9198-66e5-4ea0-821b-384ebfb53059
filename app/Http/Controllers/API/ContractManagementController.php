<?php

namespace App\Http\Controllers\API;

use App\Enums\AuditLogType;
use App\Enums\ContractKeys;
use App\Enums\ContractType;
use App\Enums\Odin\OriginDomain;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Requests\GetContractsRequest;
use App\Http\Requests\StoreContractsRequest;
use App\Http\Requests\UploadCompanyContactRequest;
use App\Models\Contract;
use App\Models\ContractKey;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Website;
use App\Models\User;
use App\Repositories\AuditLogRepository;
use App\Repositories\ContractsRepository;
use App\Services\CompanyContractService;
use App\Services\ContractService;
use App\Services\Docusign\DocuSignService;
use App\Transformers\ContractTransformer;
use DocuSign\eSign\Client\ApiException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Exception;
use Illuminate\Support\Facades\Auth;

class ContractManagementController extends APIController
{
    const string REQUEST_FIELD_FILE                  = 'file';
    const string REQUEST_FIELD_TITLE                 = 'title';
    const string REQUEST_FIELD_SUBJECT               = 'subject';
    const string REQUEST_FIELD_MESSAGE               = 'message';
    const string RESPONSE_FIELD_STATUS               = 'status';
    const string RESPONSE_FIELD_CONTRACTS            = 'contracts';

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param DocuSignService $docuSignService
     * @param ContractsRepository $contractRepository
     * @param ContractService $contractService
     */
    public function __construct(
        protected Request                $request,
        protected JsonApiResponseFactory $apiResponseFactory,
        protected DocuSignService        $docuSignService,
        protected ContractsRepository    $contractRepository,
        protected ContractService        $contractService,
        protected CompanyContractService $companyContractService,
        protected AuditLogRepository     $auditLogRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param GetContractsRequest $request
     * @param ContractTransformer $transformer
     * @return JsonResponse
     */
    public function getContracts(GetContractsRequest $request, ContractTransformer $transformer): JsonResponse
    {
        $status = $request->safe()->collect()->get(self::RESPONSE_FIELD_STATUS);

        return $this->formatResponse([
            self::RESPONSE_FIELD_STATUS => true,
            self::RESPONSE_FIELD_CONTRACTS => $transformer->transformContracts(contracts: $status ? $this->contractRepository->getContractsForStatus($status) : $this->contractRepository->getAllContracts()),
        ]);
    }

    /**
     * @param Contract $contract
     * @return JsonResponse
     */
    public function getContractFilesAsUrl(Contract $contract): JsonResponse
    {
        //todo allow downloading contract
        return $this->formatResponse([
            self::RESPONSE_FIELD_STATUS => true,
            self::REQUEST_FIELD_FILE => ""
        ]);
    }

    /**
     * @param Contract $contract
     * @param StoreContractsRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function updateContract(Contract $contract, StoreContractsRequest $request): JsonResponse
    {
        $data = $request->safe()->collect()->toArray();

        return $this->formatResponse([
            self::RESPONSE_FIELD_STATUS => $this->contractService->updateContractFromDataPayload(contract: $contract, data: $data)
        ]);
    }

    /**
     * @param Contract $contract
     * @return JsonResponse
     * @throws Exception
     */
    public function deleteContract(Contract $contract): JsonResponse
    {
        if ($contract->active)
            throw new Exception('You cannot delete active contracts');

        return $this->formatResponse([
            self::RESPONSE_FIELD_STATUS => $contract->delete(),
        ]);
    }

    /**
     * @param Contract $contract
     * @return JsonResponse
     * @throws Exception
     */
    public function activateContract(Contract $contract): JsonResponse
    {
        if (!$contract->website || !$contract->contractKey)
            throw new Exception('Contracts must have a type and origin');

        return $this->formatResponse([
            self::RESPONSE_FIELD_STATUS => $this->contractService->activateContract(contract: $contract)
        ]);
    }

    /**
     * @param CompanyUser $companyUser
     * @param Contract $contract
     * @return JsonResponse
     * @throws ApiException
     */
    public function sendSignatureRequest(Contract $contract, CompanyUser $companyUser): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_FIELD_STATUS => $this->docuSignService->sendSignatureRequestWithTemplate(companyUser: $companyUser, contract: $contract)
        ]);
    }

    /**
     * @param CompanyUser $companyUser
     * @param UploadCompanyContactRequest $request
     * @return JsonResponse
     */
    public function uploadCompanyContract(CompanyUser $companyUser, UploadCompanyContactRequest $request): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();

        // assign to the active contract so the user can login - even though they haven't signed this contract
        // this contract is reserved for custom signing
        $contract = $this->contractRepository->getActiveContractForWebsiteAndKey(
            websiteId: Website::query()->where(Website::FIELD_ABBREVIATION, OriginDomain::SOLAR_REVIEWS->getAbbreviation())->firstOrFail()?->id,
            contract_key_id: ContractKey::query()->where(ContractKey::FIELD_KEY, ContractKeys::LEAD_BUYING_AGREEMENT)->firstOrFail()?->id
        );

        //create a company contract - no signature Id because it doesnt exist in docusign
        $companyContract = $this->companyContractService->createNewContract(
            company: $companyUser->company,
            companyUser: $companyUser,
            contractType: ContractType::FIXR,
            ip: 'N/A',
            contract: $contract,
        );

        $companyContract->agree();

        $this->auditLogRepository->createAuditLogForActorAndModel($companyContract, $user, AuditLogType::CONTRACT_SIGNED);

        return $this->formatResponse([
            self::RESPONSE_FIELD_STATUS => $this->companyContractService->uploadContractToGoogleBucket($companyContract, $request->file(UploadCompanyContactRequest::REQUEST_FILE))
        ]);
    }
}
