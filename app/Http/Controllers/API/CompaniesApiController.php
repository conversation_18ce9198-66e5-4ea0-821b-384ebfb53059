<?php

namespace App\Http\Controllers\API;

use App\Enums\Odin\Product;
use App\Models\Campaigns\CompanyCampaign;
use App\Repositories\Odin\Campaigns\CompanyCampaignRepository;
use App\Repositories\Odin\CompanyRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class CompaniesApiController extends APIController
{
    const string REQUEST_CAMPAIGNS = 'campaigns';
    const string REQUEST_COMPANY_OPT_IN_NAME = 'company_opt_in_name';

    /**
     * @param string $companyReference
     * @param CompanyRepository $companyRepository
     *
     * @return JsonResponse
     */
    public function getCampaigns(string $companyReference, CompanyRepository $companyRepository): JsonResponse
    {
        $company = $companyRepository->findByReferenceOrFail($companyReference);

        return $this->formatResponse([
            'company' => [
                'id' => $company->id,
                'name' => $company->name,
                'entity_name' => $company->entity_name,
                'opt_in_name' => $company->activeOptInName?->name
            ],
            'campaigns' => $company->futureCampaigns
                ->map(fn(CompanyCampaign $campaign) => [
                    'id' => $company->id,
                    'reference' => $campaign->reference,
                    'industry' => $campaign->service->industry->name,
                    'product' => $campaign->product->name,
                    'name' => $campaign->name,
                    'status' => $campaign->status->getDisplayName(),
                    'opt_in_name' => $campaign->activeOptInName?->name
                ]),
            'relationship_manager' => $company->relationshipManager ? [
                'email' => $company->relationshipManager->user->email,
                'phone' => $company->relationshipManager->user->primaryPhone()?->phone
            ] : null
        ]);
    }

    /**
     * @param string $companyReference
     * @param CompanyRepository $companyRepository
     * @param CompanyCampaignRepository $campaignRepository
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function saveOptInNames(string $companyReference, CompanyRepository $companyRepository, CompanyCampaignRepository $campaignRepository): JsonResponse
    {
        $company = $companyRepository->findByReferenceOrFail($companyReference);

        $validated = $this->validate($this->request, [
            self::REQUEST_CAMPAIGNS => 'required|array',
            self::REQUEST_CAMPAIGNS . '.*.reference' => 'required|string',
            self::REQUEST_CAMPAIGNS . '.*.opt_in_name' => 'required|string'
        ]);

        foreach ($validated[self::REQUEST_CAMPAIGNS] as $campaign) {
            $campaignRepository->saveOptInName($campaign['opt_in_name'], $campaign['reference']);
        }

        return $this->formatResponse([
           'status' => true
        ]);
    }

    /**
     * @param string $companyReference
     * @param CompanyRepository $companyRepository
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function saveCompanyOptInName(string $companyReference, CompanyRepository $companyRepository): JsonResponse
    {
        $company = $companyRepository->findByReferenceOrFail($companyReference);

        $this->validate($this->request, [
            self::REQUEST_COMPANY_OPT_IN_NAME => 'required|string'
        ]);

        $companyRepository->saveOptInName($company, $this->request->get(self::REQUEST_COMPANY_OPT_IN_NAME));
        $company->active_opt_in_name_id = $company->optInNames()->latest()->first()->id;

        return $this->formatResponse([
            'status' => $company->save()
        ]);
    }
}
