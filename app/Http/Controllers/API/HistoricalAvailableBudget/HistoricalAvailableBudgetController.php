<?php

namespace App\Http\Controllers\API\HistoricalAvailableBudget;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\Industry;
use App\Repositories\HistoricalAvailableLocationBudgetRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Traits\EnumeratesValues;

class HistoricalAvailableBudgetController extends APIController
{
    /** @var HistoricalAvailableLocationBudgetRepository $repository */
    private HistoricalAvailableLocationBudgetRepository $repository;

    const REQUEST_INDUSTRY             = 'industry';
    const REQUEST_PRIMARY_TIMESTAMP    = 'primary_timestamp';
    const REQUEST_COMPARISON_TIMESTAMP = 'comparison_timestamp';
    const COMPANY_IDS                  = 'company_ids';

    // we should consolidate where we get the Industry types to the Industry model after reviewing the difference between 'roofer' and 'roofing'
    const INDUSTRY_TYPE_SOLAR = 'solar';
    const INDUSTRY_TYPE_ROOFING = 'roofer';

    const INDUSTRY_TYPES = [
        self::INDUSTRY_TYPE_SOLAR,
        self::INDUSTRY_TYPE_ROOFING,
    ];

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param HistoricalAvailableLocationBudgetRepository $repository
     */
    public function __construct(
        Request                                     $request,
        JsonAPIResponseFactory                      $apiResponseFactory,
        HistoricalAvailableLocationBudgetRepository $repository
    )
    {
        $this->repository = $repository;
        ini_set('memory_limit', '2048M');

        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @return JsonResponse
     */
    public function getBudgets(): JsonResponse
    {
        $validator = Validator::make($this->request->all(), [
            self::REQUEST_INDUSTRY             => 'nullable|string|in:'.join(",",self::INDUSTRY_TYPES),
            self::REQUEST_PRIMARY_TIMESTAMP    => 'nullable|string|max:10',
            self::REQUEST_COMPARISON_TIMESTAMP => 'nullable|string|max:10',
        ]);

        if($validator->fails()) {
            return $this->apiResponseFactory->makeValidationErrorResponse($validator);
        }

        $industry            = $this->request->get(self::REQUEST_INDUSTRY);
        $primaryTimestamp    = $this->request->get(self::REQUEST_PRIMARY_TIMESTAMP);
        $comparisonTimestamp = $this->request->get(self::REQUEST_COMPARISON_TIMESTAMP);

        if ($comparisonTimestamp) {
            $data = $this->repository->getComparisonTimeData($industry, $primaryTimestamp, $comparisonTimestamp);
        } else {
            $data = $this->repository->getSingleTimeData($industry, $primaryTimestamp);
        }

        return $this->formatResponse($data->toArray());
    }

    /**
     * @return JsonResponse
     */
    public function getDateOptions(): JsonResponse
    {
        return $this->formatResponse($this->repository->getDateOptions());
    }

    public function getCompanyDetails(): JsonResponse
    {
        $companyIds = $this->request->get(self::COMPANY_IDS);

        $data = $this->repository->getCompanyDetails($companyIds);

        return $this->formatResponse($data->toArray());
    }
}
