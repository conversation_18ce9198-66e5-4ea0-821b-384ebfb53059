<?php

namespace App\Http\Controllers\API\BundleManagement;

use App\Enums\RoleType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\SearchBundleInvoicesRequest;
use App\Http\Resources\Bundles\BundleInvoiceResource;
use App\Models\BundleInvoice;
use App\Models\User;
use App\Repositories\BundleManagement\BundleInvoiceRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class BundleInvoiceSearchController extends APIController
{

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param BundleInvoiceRepository $invoiceRepository
     */
    public function __construct(
        protected Request                $request,
        protected JsonApiResponseFactory $apiResponseFactory,
        public BundleInvoiceRepository   $invoiceRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param SearchBundleInvoicesRequest $searchBundleInvoicesRequest
     * @return JsonResponse
     */
    public function getInvoices(SearchBundleInvoicesRequest $searchBundleInvoicesRequest): JsonResponse
    {
        session_write_close();
        $searchParams = $searchBundleInvoicesRequest->safe()->collect();

        /** @var User $user */
        $user = Auth::user();
        $showAutoApprovedOnly = !$user->hasRole(RoleType::BUNDLE_ADMIN->value);

        return $this->formatResponse([
            "status" => true,
            "invoices_paginated" => $this->invoiceRepository->getInvoicesWithSearchFilters(
                $searchParams->get(SearchBundleInvoicesRequest::REQUEST_BUNDLE_NAME),
                $searchParams->get(SearchBundleInvoicesRequest::REQUEST_COST_FROM),
                $searchParams->get(SearchBundleInvoicesRequest::REQUEST_COST_TO),
                $searchParams->get(SearchBundleInvoicesRequest::REQUEST_CREDIT_FROM),
                $searchParams->get(SearchBundleInvoicesRequest::REQUEST_CREDIT_TO),
                $searchParams->get(SearchBundleInvoicesRequest::REQUEST_STATUS),
                $searchParams->get(SearchBundleInvoicesRequest::REQUEST_ISSUED_AT),
                $searchParams->get(SearchBundleInvoicesRequest::REQUEST_COMPANY_NAME),
                $searchParams->get(SearchBundleInvoicesRequest::REQUEST_SORT_COL),
                $searchParams->get(SearchBundleInvoicesRequest::REQUEST_SORT_DIR),
                $showAutoApprovedOnly,
            )
            ->paginate(
                $searchParams->get(
                    SearchBundleInvoicesRequest::REQUEST_PER_PAGE),
                ['*'],
                'page',
                $searchParams->get(SearchBundleInvoicesRequest::REQUEST_PAGE)
            )
            ->through(fn(BundleInvoice $invoice) => new BundleInvoiceResource($invoice))
        ]);
    }
}
