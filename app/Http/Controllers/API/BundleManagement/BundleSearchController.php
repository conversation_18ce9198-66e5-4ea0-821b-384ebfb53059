<?php

namespace App\Http\Controllers\API\BundleManagement;

use App\Enums\RoleType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\SearchBundlesRequest;
use App\Http\Resources\Bundles\BundleResource;
use App\Models\Bundle;
use App\Models\User;
use App\Repositories\BundleManagement\BundleRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class BundleSearchController extends APIController
{

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param BundleRepository $bundleRepository
     */
    public function __construct(
        protected Request                $request,
        protected JsonApiResponseFactory $apiResponseFactory,
        public BundleRepository          $bundleRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param SearchBundlesRequest $searchBundlesRequest
     * @return JsonResponse
     */
    public function getBundles(SearchBundlesRequest $searchBundlesRequest): JsonResponse
    {
        session_write_close();
        $searchParams = $searchBundlesRequest->safe()->collect();

        /** @var User $user */
        $user = Auth::user();

        $showAutoApprovedOnly = !$user->hasRole(RoleType::BUNDLE_ADMIN->value);

        return $this->formatResponse([
            "status" => true,
            "bundles_paginated" => $this->bundleRepository->getBundlesWithSearchFilters(
                $searchParams->get(SearchBundlesRequest::REQUEST_NAME),
                $searchParams->get(SearchBundlesRequest::REQUEST_COST_FROM),
                $searchParams->get(SearchBundlesRequest::REQUEST_COST_TO),
                $searchParams->get(SearchBundlesRequest::REQUEST_CREDIT_FROM),
                $searchParams->get(SearchBundlesRequest::REQUEST_CREDIT_TO),
                $searchParams->get(SearchBundlesRequest::REQUEST_ACTIVE),
                $searchParams->get(SearchBundlesRequest::REQUEST_INDUSTRY_ID),
                $searchParams->get(SearchBundlesRequest::REQUEST_SORT_COL),
                $searchParams->get(SearchBundlesRequest::REQUEST_SORT_DIR),
                $showAutoApprovedOnly,
            )
            ->paginate(
                $searchParams->get(SearchBundlesRequest::REQUEST_PER_PAGE),
                ['*'],
                'page',
                $searchParams->get(SearchBundlesRequest::REQUEST_PAGE)
            )
            ->through(fn(Bundle $bundle) => new BundleResource($bundle))
        ]);
    }
}
