<?php

namespace App\Http\Controllers\API\BundleManagement\v2;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\BundleManagement\BundleInvoiceApiController;
use App\Http\Requests\Bundles\BundleInvoiceTransitionRequest;
use App\Models\BundleInvoice;
use App\Models\User;
use App\Repositories\BundleManagement\BundleInvoiceRepository;
use App\Services\BundleManagement\v2\BundleInvoiceService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Throwable;

class BundleInvoiceApiControllerV2 extends BundleInvoiceApiController
{

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        BundleInvoiceRepository $bundleInvoiceRepository,
        protected BundleInvoiceService $bundleInvoiceService,
    )
    {
        parent::__construct(
            $request,
            $apiResponseFactory,
            $bundleInvoiceService,
            $bundleInvoiceRepository,
        );
    }

    public function executeStatusTransition(BundleInvoice $bundleInvoice, BundleInvoiceTransitionRequest $request): JsonResponse
    {
        $data = $request->safe();
        /** @var User $user */
        $user = Auth::user();

        $response = [
            'status' => false,
        ];

        try {
            $response['status'] = $this->bundleInvoiceService->requestInvoiceTransition(
                bundleInvoice: $bundleInvoice,
                transition   : $data[BundleInvoiceTransitionRequest::REQUEST_TRANSITION],
                user         : $user,
                options      : $data->toArray(),
            );
        } catch (Throwable $exception) {
            $response = [
                'status'  => false,
                'message' => $exception->getMessage()
            ];
        }

        return $this->formatResponse($response);
    }
}
