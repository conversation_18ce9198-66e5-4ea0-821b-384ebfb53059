<?php

namespace App\Http\Controllers\API\BundleManagement;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Bundles\DeleteBundleInvoiceRequest;
use App\Http\Requests\Bundles\BundleInvoiceRequest;
use App\Http\Requests\Bundles\BundleInvoiceTransitionRequest;
use App\Http\Requests\Bundles\StoreBundleInvoiceRequest;
use App\Http\Resources\Bundles\BundleInvoiceCollection;
use App\Http\Resources\Bundles\BundleInvoiceHistoryCollection;
use App\Http\Resources\Bundles\BundleInvoiceResource;
use App\Models\Bundle;
use App\Models\BundleInvoice;
use App\Models\User;
use App\Repositories\BundleManagement\BundleInvoiceHistoryRepository;
use App\Repositories\BundleManagement\BundleInvoiceRepository;
use App\Services\BundleManagement\v2\BundleInvoiceService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Throwable;


class BundleInvoiceApiController extends APIController
{

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected BundleInvoiceService $bundleInvoiceService,
        private readonly BundleInvoiceRepository $bundleInvoiceRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param BundleInvoiceRequest $request
     * @return JsonResponse
     */
    public function index(BundleInvoiceRequest $request): JsonResponse
    {
        $invoiceQuery = BundleInvoice::query()->orderBy(BundleInvoice::CREATED_AT, 'DESC');

        if ($request->get('bundleId')) {
            $invoiceQuery->where(BundleInvoice::FIELD_BUNDLE_ID, '=', (int)$request->get('bundleId'));
        }

        return $this->formatResponse([
            "status" => true,
            "bundleInvoices" => new BundleInvoiceCollection($invoiceQuery->get())
        ]);
    }

    /**
     * @param StoreBundleInvoiceRequest $request
     * @return JsonResponse
     */
    public function addBundleInvoice(StoreBundleInvoiceRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $bundleId = Arr::get($validated, StoreBundleInvoiceRequest::FIELD_BUNDLE_ID);

        $bundle = Bundle::query()->findOrFail($bundleId);

        $invoice = $this->bundleInvoiceRepository->createNewInvoice(
            bundleId      : $bundleId,
            companyId     : Arr::get($validated, StoreBundleInvoiceRequest::FIELD_COMPANY_ID),
            billingVersion: Arr::get($validated, StoreBundleInvoiceRequest::FIELD_BILLING_VERSION),
            payload       : [
                'billing_profile_ids' => Arr::get($validated, StoreBundleInvoiceRequest::FIELD_BILLING_PROFILE_IDS)
            ]
        );

        // Should we handle legacy ?
        if ($bundle->{Bundle::FIELD_IS_AUTO_APPROVED}) {
            $this->bundleInvoiceService->createPayableLegacyInvoice(
                bundleInvoice: $invoice,
                user         : auth()->user()
            );
        }

        return $this->formatResponse([
            "status" => true,
            "invoice" => new BundleInvoiceResource($invoice)
        ]);
    }

    /**
     * @param string $id
     * @param BundleInvoiceTransitionRequest $request
     * @return JsonResponse
     */
    public function executeStatusTransitionFromId(string $id, BundleInvoiceTransitionRequest $request): JsonResponse
    {
        /** @var BundleInvoice $bundleInvoice */
        $bundleInvoice = BundleInvoice::query()->where(['id'=>$id])->firstOrFail();
        return $this->executeStatusTransition($bundleInvoice, $request);
    }

    /**
     * @param int $invoiceId
     * @param BundleInvoiceTransitionRequest $request
     * @param BundleInvoiceHistoryRepository $bundleInvoiceHistoryRepository
     *
     * @return JsonResponse
     */
    public function executeStatusTransitionFromInvoiceId(int $invoiceId, BundleInvoiceTransitionRequest $request, BundleInvoiceHistoryRepository $bundleInvoiceHistoryRepository): JsonResponse
    {
        /** @var BundleInvoice $bundleInvoice */
        $bundleInvoice = BundleInvoice::query()->where([BundleInvoice::FIELD_PAYABLE_INVOICE_ID => $invoiceId])->firstOrFail();
        $transition = $request->get(BundleInvoiceTransitionRequest::REQUEST_TRANSITION, '');

        if (!$this->bundleInvoiceRepository->canTransitionInvoiceStatus($bundleInvoice, $transition)) {
            $bundleInvoiceHistoryRepository->createInvoiceHistory(
                $bundleInvoice->id,
                "Legacy transition request to {$transition} was failed as the bundle invoice is in invalid status"
            );

            return $this->formatResponse([
                'status' => true
            ]);
        }

        return $this->executeStatusTransition($bundleInvoice, $request);
    }

    /**
     * @param BundleInvoice $bundleInvoice
     * @param BundleInvoiceTransitionRequest $request
     * @return JsonResponse
     */
    public function executeStatusTransition(BundleInvoice $bundleInvoice, BundleInvoiceTransitionRequest $request): JsonResponse
    {
        $data = $request->safe();
        /** @var User $user */
        $user = Auth::user();

        try {
            return $this->formatResponse([
                'status' => !!$this->bundleInvoiceRepository->transitionInvoiceStatus(
                    $bundleInvoice,
                    $data[BundleInvoiceTransitionRequest::REQUEST_TRANSITION],
                    $user,
                    $data->toArray()
                )
            ]);
        } catch (Throwable $exception) {
            return $this->formatResponse([
                'status' => false,
                'message' => $exception->getMessage()
            ]);
        }
    }

    /**
     * @param BundleInvoice $bundleInvoice
     * @param BundleInvoiceRequest $request
     * @return JsonResponse
     */
    public function getBundleInvoiceHistory(BundleInvoice $bundleInvoice, BundleInvoiceRequest $request): JsonResponse
    {
        return $this->formatResponse([
            'status' => true,
            'historyRecords' => new BundleInvoiceHistoryCollection($bundleInvoice->{BundleInvoice::RELATION_HISTORY}),
        ]);
    }

    /**
     * @param BundleInvoice $bundleInvoice
     * @param DeleteBundleInvoiceRequest $request
     * @return JsonResponse
     */
    public function deleteBundleInvoice(BundleInvoice $bundleInvoice, DeleteBundleInvoiceRequest $request): JsonResponse
    {
        $request->safe();

        return $this->formatResponse([
            'status' => $this->bundleInvoiceRepository->deleteInvoice($bundleInvoice),
        ]);
    }
}
