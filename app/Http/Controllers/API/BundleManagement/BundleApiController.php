<?php

namespace App\Http\Controllers\API\BundleManagement;

use App\Enums\RoleType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Bundles\DeleteBundleRequest;
use App\Http\Requests\Bundles\BundleRequest;
use App\Http\Requests\Bundles\StoreBundleRequest;
use App\Http\Requests\Bundles\UpdateBundleRequest;
use App\Http\Resources\Bundles\BundleCollection;
use App\Http\Resources\Bundles\BundleResource;
use App\Models\Bundle;
use App\Repositories\BundleManagement\BundleRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class BundleApiController extends APIController
{
    public function __construct(
        Request                                    $request,
        JsonAPIResponseFactory                     $apiResponseFactory,
        private readonly BundleRepository          $bundleRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param BundleRequest $request
     * @return JsonResponse
     */
    public function index(BundleRequest $request): JsonResponse
    {
        $user = Auth::user();
        $autoApprovedOnly = !$user->hasRole(RoleType::BUNDLE_ADMIN->value);

        $bundles = $this->bundleRepository->getBundlesWithSearchFilters(
            active: true,
            autoApprovedOnly: $autoApprovedOnly
        )->get();

        return $this->formatResponse([
            "status" => true,
            "bundles" => new BundleCollection($bundles)
        ]);
    }

    /**
     * @param StoreBundleRequest $request
     * @return JsonResponse
     */
    public function addBundle(StoreBundleRequest $request): JsonResponse
    {
        $data = $request->safe();
        $bundle = $this->bundleRepository->createBundle(
            $data[Bundle::FIELD_NAME],
            $data[Bundle::FIELD_TITLE],
            $data[Bundle::FIELD_DESCRIPTION],
            $data[Bundle::FIELD_COST],
            $data[Bundle::FIELD_CREDIT],
            $data[Bundle::FIELD_NOTE] ?? null,
            $request->user(),
            $data['active'] ?? false,
            $data[Bundle::FIELD_INDUSTRY_ID] ?? NULL
        );

        return $this->formatResponse([
            "status" => true,
            "bundle" => new BundleResource($bundle)
        ]);
    }

    /**
     * @param Bundle $bundle
     * @param UpdateBundleRequest $request
     * @return JsonResponse
     */
    public function updateBundle(Bundle $bundle, UpdateBundleRequest $request): JsonResponse
    {
        $data = $request->safe();
        $bundle = $this->bundleRepository->updateBundle(
            $bundle,
            $data[Bundle::FIELD_NAME],
            $data[Bundle::FIELD_TITLE],
            $data[Bundle::FIELD_DESCRIPTION],
            $data[Bundle::FIELD_COST],
            $data[Bundle::FIELD_CREDIT],
            $data[Bundle::FIELD_NOTE] ?? null,
            $request->user(),
            $data['active'] ?? false,
            $data[Bundle::FIELD_INDUSTRY_ID] ?? NULL
        );

        return $this->formatResponse([
            "status" => true,
            "bundle" => new BundleResource($bundle)
        ]);
    }

    /**
     * @param Bundle $bundle
     * @param DeleteBundleRequest $request
     * @return JsonResponse
     */
    public function deleteBundle(Bundle $bundle, DeleteBundleRequest $request): JsonResponse
    {
        $request->safe();

        return $this->formatResponse([
            'status' => $this->bundleRepository->deleteBundle($bundle),
        ]);
    }
}
