<?php

namespace App\Http\Controllers\API;

use App\Enums\ActivityLog\ActivityLogSubjectType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Requests\ActivityLogRequest;
use App\Http\Requests\CreateActivityLogRequest;
use App\Http\Resources\ActivityLogResource;
use App\Repositories\ActivityLog\ActivityLogRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Spatie\Activitylog\Models\Activity;

class ActivityLogController extends APIController
{
    public function __construct(
        Request                      $request,
        JsonAPIResponseFactory       $apiResponseFactory,
        protected ActivityLogRepository $activityLogRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    const string REQUEST_ACTIVITY_SUBJECT_TYPE = 'subject_type';
    const string REQUEST_ACTIVITY_SUBJECT_ID   = 'subject_id';
    const string REQUEST_ACTIVITY_EVENTS       = 'events';
    const string RESPONSE_ACTIVITY_LOGS        = 'activity_logs';

    public function getActivityLogs(ActivityLogRequest $request): JsonResponse
    {
        $activityLogs = Activity::query()
            ->select([
                'activity_log.*',
                DB::raw('count(id) as batch_count'),
            ])->where(self::REQUEST_ACTIVITY_SUBJECT_TYPE, $request->validated(self::REQUEST_ACTIVITY_SUBJECT_TYPE))
            ->where(self::REQUEST_ACTIVITY_SUBJECT_ID, $request->validated(self::REQUEST_ACTIVITY_SUBJECT_ID))
            ->whereIn('event', $request->validated(self::REQUEST_ACTIVITY_EVENTS))
            ->groupBy(DB::raw('COALESCE(batch_uuid, id)'))
            ->orderByDesc(Activity::UPDATED_AT)
            ->get();

        return $this->formatResponse([
            self::RESPONSE_ACTIVITY_LOGS => ActivityLogResource::collection($activityLogs),
        ]);
    }

    /**
     * @param CreateActivityLogRequest $request
     * @return JsonResponse
     */
    public function createNewLog(CreateActivityLogRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $activity = $this->activityLogRepository->createActivityLog(
            logName: Arr::get($validated, CreateActivityLogRequest::LOG_NAME),
            description: Arr::get($validated, CreateActivityLogRequest::DESCRIPTION),
            subjectType: ActivityLogSubjectType::tryFrom(Arr::get($validated, CreateActivityLogRequest::SUBJECT_TYPE))?->value,
            subjectId: Arr::get($validated, CreateActivityLogRequest::SUBJECT_ID),
            properties: Arr::get($validated, CreateActivityLogRequest::PROPERTIES),
        );

        return $this->formatResponse([
            'status' => !!$activity,
        ]);
    }

    public function getBatchDetails(ActivityLogRepository $repository): JsonResponse
    {
        $batch = $repository->getBatchedActivityLogs($this->request->route('uuid'));
        $transform = $repository->transformBatchedLogs($batch);

        return $this->formatResponse([
            self::RESPONSE_ACTIVITY_LOGS => ActivityLogResource::collection($transform),
        ]);
    }
}
