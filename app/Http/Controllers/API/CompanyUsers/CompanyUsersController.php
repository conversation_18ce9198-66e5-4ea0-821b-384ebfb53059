<?php
namespace App\Http\Controllers\API\CompanyUsers;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Odin\GetCompanyUsersRequest;
use App\Http\Resources\Odin\CompanyUsersFiltersResource;
use App\Http\Resources\Odin\CompanyUsersResource;
use App\Repositories\CompanyUsersRepository\CompanyUsersRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class CompanyUsersController extends APIController
{
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected CompanyUsersRepository $companyUsersRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param GetCompanyUsersRequest $request
     * @return AnonymousResourceCollection
     */
    public function getCompanyUsers(GetCompanyUsersRequest $request): AnonymousResourceCollection
    {
        $validated = $request->validated();

        $companyUsers = $this->companyUsersRepository->getCompanyUsers($validated);

        return CompanyUsersResource::collection($companyUsers);
    }

    /**
     * @return JsonResponse
     */
    public function getFilterOptions(): JsonResponse
    {
        $filterOptions = $this->companyUsersRepository->getAllDistinctCompanyUserTitles();

        return $this->formatResponse([
            'status'            => true,
            'filter_options'    => CompanyUsersFiltersResource::collection($filterOptions)
        ]);

    }
}