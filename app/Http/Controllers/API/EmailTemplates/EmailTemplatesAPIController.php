<?php
namespace App\Http\Controllers\API\EmailTemplates;

use App\Enums\EmailTemplateScope;
use App\Enums\EmailTemplateType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\EmailTemplates\EmailTemplateBackgroundDuplicateRequest;
use App\Http\Requests\EmailTemplates\EmailTemplateBackgroundRequest;
use App\Http\Requests\EmailTemplates\EmailTemplateDuplicateRequest;
use App\Http\Requests\EmailTemplates\EmailTemplateImageRequest;
use App\Http\Requests\EmailTemplates\EmailTemplateRequest;
use App\Http\Requests\EmailTemplates\EmailTemplatesShortcodesRequest;
use App\Http\Requests\SearchEmailTemplatesRequest;
use App\Http\Resources\EmailTemplates\EmailTemplateListOptionResource;
use App\Jobs\SendEmailCalculatorResultToConsumer;
use App\Models\EmailTemplate;
use App\Models\EmailTemplateBackground;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\TestProduct;
use App\Models\User;
use App\Services\EmailTemplates\EmailTemplateBackgroundService;
use App\Services\EmailTemplates\EmailTemplateImageService;
use App\Services\EmailTemplates\EmailTemplateService;
use App\Services\EmailTemplates\TemplateOptions\EmailTemplateOptionsFactory;
use App\Services\EnginesService;
use App\Services\Filterables\EmailTemplates\EmailTemplatesFilterableService;
use App\Services\Shortcode\ShortcodeImplementation\EmailMarketingShortcodeUseCase;
use App\Services\Shortcode\ShortcodeImplementation\MissedProductsShortcodeUseCase;
use App\Services\Shortcode\ShortcodeImplementation\ProspectShortcodeUseCase;
use App\Services\TestLead\TestLeadService;
use App\Services\TestProducts\TestProductService;
use App\Transformers\EmailTemplates\TemplateBackgroundsListTransformer;
use App\Transformers\EmailTemplates\TemplateBackgroundTransformer;
use App\Transformers\EmailTemplates\TemplatesListTransformer;
use App\Transformers\EmailTemplates\TemplateTransformer;
use App\Workflows\Shortcodes\WorkflowShortcodeServiceFactory;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\PaginatedResourceResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;
use phpDocumentor\Descriptor\Interfaces\FunctionInterface;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;
use Throwable;

class EmailTemplatesAPIController extends APIController
{
    const REQUEST_CONTENT       = 'content';
    const REQUEST_NAME          = 'name';
    const REQUEST_INDUSTRY_ID   = 'industry_id';
    const REQUEST_BACKGROUND_ID = 'background_id';
    const REQUEST_HEADER        = 'header';
    const REQUEST_FOOTER        = 'footer';

    const REQUEST_IMAGE_NAME       = 'image_name';
    const REQUEST_IMAGE_DATA_URL   = 'image_data_url';
    const REQUEST_TEMPLATE_ID      = 'template_id';
    const REQUEST_TEMPLATE_TYPE    = 'template_type';
    const TEMPLATE_TYPE_CONTENT    = 'content';
    const TEMPLATE_TYPE_BACKGROUND = 'background';
    const RESPONSE_TEMPLATE = 'template';

    const string RESPONSE_STATUS            = 'status';
    const string RESPONSE_TEMPLATES         = 'templates';
    const string RESPONSE_FILTER_OPTIONS    = 'filter_options';
    const string RESPONSE_SHORTCODES        = 'shortcodes';
    const string RESPONSE_ENGINE_OPTIONS    = 'engine_options';

    protected EmailTemplatesFilterableService $emailTemplateFilterableService;

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param EmailTemplateService $emailTemplateService
     * @param EmailTemplateBackgroundService $emailTemplateBackgroundService
     */
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        private EmailTemplateService $emailTemplateService,
        private EmailTemplateBackgroundService $emailTemplateBackgroundService,
    )
    {
        parent::__construct($request, $apiResponseFactory);

        $this->middleware(function ($request, $next) {
            $this->emailTemplateFilterableService = new EmailTemplatesFilterableService(
                authUser: $request->user(),
            );

            return $next($request);
        });
    }

    public function getFilterOptions(): JsonResponse
    {
        $options = $this->emailTemplateFilterableService->getDisplayData();

        return $this->formatResponse([
            self::RESPONSE_STATUS           => !!$options,
            self::RESPONSE_FILTER_OPTIONS   => $options,
        ]);
    }

    public function search(SearchEmailTemplatesRequest $request, TemplatesListTransformer $templatesListTransformer): JsonResponse
    {
        $filterOptions = $request->get(SearchEmailTemplatesRequest::REQUEST_FILTERS);
        $searchString = $request->get(SearchEmailTemplatesRequest::REQUEST_SEARCH);

        $query = $this->emailTemplateFilterableService->runQuery($filterOptions, EmailTemplate::query());

        if($searchString)
            $query = $query->where(EmailTemplate::FIELD_NAME, 'LIKE', "%{$searchString}%");

        $templatesPaginated =  $query->paginate($request->get(SearchEmailTemplatesRequest::REQUEST_PER_PAGE));

        return $this->formatResponse([
            self::RESPONSE_STATUS           => true,
            self::RESPONSE_TEMPLATES        => $templatesPaginated->through(function(EmailTemplate $template) use ($templatesListTransformer) {
                return $templatesListTransformer->transformTemplate($template);
            })
        ]);
    }

    /**
     * @param SearchEmailTemplatesRequest $request
     * @return AnonymousResourceCollection
     */
    public function listEmailTemplates(SearchEmailTemplatesRequest $request): AnonymousResourceCollection
    {
        $filterOptions = $request->get(SearchEmailTemplatesRequest::REQUEST_FILTERS);

        $templates = $this->emailTemplateFilterableService
            ->runQuery($filterOptions, EmailTemplate::query())
            ->get();

        return EmailTemplateListOptionResource::collection($templates);
    }

    /**
     * @param EmailTemplatesShortcodesRequest $emailTemplatesShortcodesRequest
     * @param WorkflowShortcodeServiceFactory $workflowShortcodeServiceFactory
     * @param EnginesService $enginesService
     * @return JsonResponse
     * @throws Exception
     */
    public function getShortcodes(EmailTemplatesShortcodesRequest $emailTemplatesShortcodesRequest, WorkflowShortcodeServiceFactory $workflowShortcodeServiceFactory, EnginesService $enginesService): JsonResponse
    {
        $templateType = (int) $emailTemplatesShortcodesRequest->get(EmailTemplate::FIELD_TYPE);
        $industryId = (int) $emailTemplatesShortcodesRequest->get(EmailTemplate::FIELD_INDUSTRY_ID);
        $engineName = $emailTemplatesShortcodesRequest->get(EmailTemplate::FIELD_ENGINE);

        if($templateType && $templateType === EmailTemplateType::STATUS_CALCULATOR_RESULTS->value && $engineName) {
            $shortcodes =  $enginesService->getEngineOutputsOptions($engineName);
        }
        else if ($templateType === EmailTemplateType::STATUS_EMAIL_MARKETING->value) {
            $shortcodes = EmailMarketingShortcodeUseCase::getShortcodesList();
        }
        else if ($templateType === EmailTemplateType::BDM_PROSPECTING_EMAIL->value) {
            $shortcodes = ProspectShortcodeUseCase::getShortcodesList();
        }
        else if (in_array($templateType, [EmailTemplateType::STATUS_MISSED_PRODUCTS_BDM->value, EmailTemplateType::STATUS_MISSED_PRODUCTS_SUMMARY->value])) {
            $shortcodes = MissedProductsShortcodeUseCase::getShortcodesList();
        }
        else {
            $shortcodes = ($workflowShortcodeServiceFactory::makeService(WorkflowShortcodeServiceFactory::WORKFLOW_DRIVER))->getShortcodes();
        }

        return $this->formatResponse([
            self::RESPONSE_STATUS       => true,
            self::RESPONSE_SHORTCODES   => $shortcodes
        ]);
    }

    /**
     * @param EmailTemplatesShortcodesRequest $emailTemplatesShortcodesRequest
     * @return array
     */
    public function getTemplateOptions(EmailTemplatesShortcodesRequest $emailTemplatesShortcodesRequest): array
    {
        $templateType = (int) $emailTemplatesShortcodesRequest->get(EmailTemplate::FIELD_TYPE);

        $options = EmailTemplateOptionsFactory::make(EmailTemplateType::tryFrom($templateType));

        if (!$options) {
            return [];
        }

        return [
            'options' => $options->getOptions()
        ];
    }

    public function getEngineOptions(EnginesService $enginesService): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_STATUS           => true,
            self::RESPONSE_ENGINE_OPTIONS   => $enginesService->getAvailableEngineOptions()
        ]);
    }

    /**
     * @param Request $request
     * @param TemplatesListTransformer $templatesListTransformer
     * @return JsonResponse
     */
    public function getUserEmailTemplates(Request $request, TemplatesListTransformer $templatesListTransformer): JsonResponse
    {
        $scope = $request->input('scope');

        $scopeEnum = empty($scope) ? null : EmailTemplateScope::from($scope);

        return $this->formatResponse([
            "status" => true,
            "templates" => $this->emailTemplateService->getPaginatedUserTemplates($scopeEnum)->through(function(EmailTemplate $template) use ($templatesListTransformer) {
                return $templatesListTransformer->transformTemplate($template);
            })
        ]);
    }

    /**
     * @param TemplatesListTransformer $templatesListTransformer
     *
     * @return JsonResponse
     */
    public function getAllEmailTemplates(TemplatesListTransformer $templatesListTransformer): JsonResponse
    {
        return $this->formatResponse([
            "status" => true,
            "templates" => $this->emailTemplateService->getAllEmailTemplates()->map(function(EmailTemplate $template) use ($templatesListTransformer) {
                return $templatesListTransformer->transformTemplate($template);
            })
        ]);
    }

    /**
     * @param Request $request
     * @param TemplateBackgroundsListTransformer $templateBackgroundsListTransformer
     * @return JsonResponse
     */
    public function getUserEmailBackgroundTemplates(Request $request, TemplateBackgroundsListTransformer $templateBackgroundsListTransformer): JsonResponse
    {
        $scope = $request->input('scope');

        $scopeEnum = empty($scope) ? null : EmailTemplateScope::from($scope);

        return $this->formatResponse([
            "status" => true,
            "templates" => $this->emailTemplateBackgroundService->getPaginatedBackgroundTemplates($scopeEnum)->through(function(EmailTemplateBackground $template) use ($templateBackgroundsListTransformer) {
                return $templateBackgroundsListTransformer->transformTemplate($template);
            })
        ]);
    }

    /**
     * @param TemplateBackgroundsListTransformer $templateBackgroundsListTransformer
     * @return JsonResponse
     * @throws Exception
     */
    public function getUserEmailBackgroundTemplatesByIndustry(TemplateBackgroundsListTransformer $templateBackgroundsListTransformer): JsonResponse
    {
        $industryId = $this->request->input(self::REQUEST_INDUSTRY_ID);
        $validator  = Validator::make(
            [ self::REQUEST_INDUSTRY_ID  => $industryId ],
            [ self::REQUEST_INDUSTRY_ID  => 'integer|nullable|exists:' . Industry::TABLE . ',' . Industry::FIELD_ID ]
        );

        if($validator->fails()) {
            throw new BadRequestException("The requested industry '$industryId' is deemed invalid.");
        }

        return $this->formatResponse([
            "status" => true,
            "templates" => $templateBackgroundsListTransformer->transformTemplates($this->emailTemplateBackgroundService->getUserBackgroundTemplatesByIndustry($industryId))
        ]);
    }

    /**
     * @param int $templateId
     * @param TemplateTransformer $templateTransformer
     * @return JsonResponse
     * @throws Exception
     */
    public function getEmailTemplate(int $templateId, TemplateTransformer $templateTransformer): JsonResponse
    {
        return $this->formatResponse([
            "status" => true,
            "template" => $templateTransformer->transformTemplate($this->emailTemplateService->getTemplateById($templateId))
        ]);
    }

    /**
     * @param int $templateId
     * @param TemplateBackgroundTransformer $templateBackgroundTransformer
     * @return JsonResponse
     * @throws Exception
     */
    public function getEmailTemplateBackground(int $templateId, TemplateBackgroundTransformer $templateBackgroundTransformer): JsonResponse
    {
        return $this->formatResponse([
            "status" => true,
            "template" => $templateBackgroundTransformer->transformTemplate($this->emailTemplateBackgroundService->getBackgroundTemplateById($templateId))
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function previewEmailTemplate(Request $request): JsonResponse
    {
        return $this->formatResponse([
            "status" => true,
            "preview_html" => $this->emailTemplateService->convertTemplateMarkdownToHtml($request->input(self::REQUEST_CONTENT) ?? '', $request->input(self::REQUEST_BACKGROUND_ID))
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function previewEmailBackgroundTemplate(Request $request): JsonResponse
    {
        return $this->formatResponse([
            "status" => true,
            "preview_html" => $this->emailTemplateBackgroundService->convertTemplateMarkdownToHtml(
                $request->input(self::REQUEST_HEADER) ?? '',
                $request->input(self::REQUEST_FOOTER) ?? '',
            )
        ]);
    }

    /**
     * @param EmailTemplateRequest $emailTemplateRequest
     * @return JsonResponse
     */
    public function createEmailTemplate(EmailTemplateRequest $emailTemplateRequest): JsonResponse
    {
        $data = $emailTemplateRequest->safe()->only([
            EmailTemplate::FIELD_NAME,
            EmailTemplate::FIELD_SUBJECT,
            EmailTemplate::FIELD_CONTENT,
            EmailTemplate::FIELD_PERSONAL,
            EmailTemplate::FIELD_TYPE,
            EmailTemplate::FIELD_INDUSTRY_ID,
            EmailTemplate::FIELD_BACKGROUND_ID,
            EmailTemplate::FIELD_ENGINE,
            EmailTemplate::FIELD_ACTIVE,
        ]);

        $emailTemplate = $this->emailTemplateService->createTemplate(
            ownerUserId: Auth::id(),
            name: $data[EmailTemplate::FIELD_NAME],
            subject: $data[EmailTemplate::FIELD_SUBJECT],
            content: $data[EmailTemplate::FIELD_CONTENT],
            personal: $data[EmailTemplate::FIELD_PERSONAL],
            type: $data[EmailTemplate::FIELD_TYPE],
            active: $data[EmailTemplate::FIELD_ACTIVE],
            industryId: $data[EmailTemplate::FIELD_INDUSTRY_ID] ?? null,
            backgroundId: $data[EmailTemplate::FIELD_BACKGROUND_ID],
            engine: $data[EmailTemplate::FIELD_ENGINE]?? null,
        );

        $updateTime = $emailTemplate->{EmailTemplate::FIELD_CREATED_AT};

        return $this->formatResponse([
            "status" => !empty($emailTemplate),
            "template_id" => $emailTemplate->{EmailTemplate::FIELD_ID},
            'owner_name' => $this->prepareOwnerName($emailTemplate->{EmailTemplate::RELATION_OWNER}),
            "last_updated" => $updateTime->format('Y-m-d H:i:s')." ".strtoupper($updateTime->timezone->getAbbreviatedName())
        ]);
    }

    /**
     * @param EmailTemplateDuplicateRequest $request
     * @param TemplateTransformer           $templateTransformer
     * @return JsonResponse
     * @throws Exception
     */
    public function duplicateEmailTemplate(EmailTemplateDuplicateRequest $request, TemplateTransformer $templateTransformer): JsonResponse
    {
        $name       = $request->get(self::REQUEST_NAME);
        $templateId = $request->get(self::REQUEST_TEMPLATE_ID);

        $givenTemplate = $this->emailTemplateService->getTemplateById($templateId);
        $userId        = Auth::id();

        if($givenTemplate->{EmailTemplate::FIELD_OWNER_USER_ID} !== $userId
            && !! $givenTemplate->{EmailTemplate::FIELD_PERSONAL}
        ) {
            throw new Exception("You're not authorized to access the template.");
        }

        /** @var EmailTemplate $newTemplate */
        $newTemplate = $this->emailTemplateService->createDuplicateTemplate(
            ownerUserId         : $userId,
            name                : $name,
            templateToDuplicate : $givenTemplate,
        );

        return $this->formatResponse([
            self::RESPONSE_STATUS   => !! $newTemplate,
            self::RESPONSE_TEMPLATE => $templateTransformer->transformTemplate($newTemplate),
        ]);
    }

    /**
     * @param EmailTemplateBackgroundRequest $emailTemplateBackgroundRequest
     * @return JsonResponse
     */
    public function createEmailBackgroundTemplate(EmailTemplateBackgroundRequest $emailTemplateBackgroundRequest): JsonResponse
    {
        $data = $emailTemplateBackgroundRequest->safe()->only([
            EmailTemplateBackground::FIELD_NAME,
            EmailTemplateBackground::FIELD_HEADER,
            EmailTemplateBackground::FIELD_FOOTER,
            EmailTemplateBackground::FIELD_PERSONAL,
            EmailTemplateBackground::FIELD_INDUSTRY_ID,
        ]);

        $emailTemplateBackground = $this->emailTemplateBackgroundService->createTemplate(
            Auth::id(),
            $data[EmailTemplateBackground::FIELD_NAME],
            $data[EmailTemplateBackground::FIELD_HEADER],
            $data[EmailTemplateBackground::FIELD_FOOTER],
            $data[EmailTemplateBackground::FIELD_PERSONAL],
            $data[EmailTemplateBackground::FIELD_INDUSTRY_ID] ?? null,
        );

        $updateTime = $emailTemplateBackground->{EmailTemplateBackground::FIELD_CREATED_AT};

        return $this->formatResponse([
            "status" => !empty($emailTemplateBackground),
            "template_id" => $emailTemplateBackground->{EmailTemplateBackground::FIELD_ID},
            'owner_name' => $this->prepareOwnerName($emailTemplateBackground->{EmailTemplateBackground::RELATION_OWNER}),
            "last_updated" => $updateTime->format('Y-m-d H:i:s')." ".strtoupper($updateTime->timezone->getAbbreviatedName())
        ]);
    }

    /**
     * @param EmailTemplateBackgroundDuplicateRequest $request
     * @param TemplateBackgroundTransformer           $templateBackgroundTransformer
     * @return JsonResponse
     * @throws Exception
     */
    public function duplicateEmailBackgroundTemplate(EmailTemplateBackgroundDuplicateRequest $request, TemplateBackgroundTransformer $templateBackgroundTransformer): JsonResponse
    {
        $name                 = $request->get(self::REQUEST_NAME);
        $templateBackgroundId = $request->get(self::REQUEST_BACKGROUND_ID);

        $givenTemplateBackground = $this->emailTemplateBackgroundService->getBackgroundTemplateById($templateBackgroundId);
        $userId                  = Auth::id();

        if($givenTemplateBackground->{EmailTemplateBackground::FIELD_OWNER_USER_ID} !== $userId
            && !! $givenTemplateBackground->{EmailTemplateBackground::FIELD_PERSONAL}
        ) {
            throw new Exception("You're not authorized to access the template.");
        }

        /** @var EmailTemplateBackground $newTemplateBackground */
        $newTemplateBackground = $this->emailTemplateBackgroundService->createDuplicateTemplate(
            ownerUserId         : $userId,
            name                : $name,
            templateToDuplicate : $givenTemplateBackground,
        );

        return $this->formatResponse([
            self::RESPONSE_STATUS   => !! $newTemplateBackground,
            self::RESPONSE_TEMPLATE => $templateBackgroundTransformer->transformTemplate($newTemplateBackground),
        ]);
    }

    /**
     * @param User|null $owner
     * @return string
     */
    protected function prepareOwnerName(?User $owner = null): string
    {
        return !empty($owner)
            ? $owner->{User::FIELD_NAME}." (".$owner->{User::FIELD_EMAIL}.")"
            : 'Unknown';
    }

    /**
     * @param int $templateId
     * @param EmailTemplateRequest $emailTemplateRequest
     * @return JsonResponse
     * @throws Exception
     */
    public function updateEmailTemplate(int $templateId, EmailTemplateRequest $emailTemplateRequest): JsonResponse
    {
        $data = $emailTemplateRequest->safe()->only([
            EmailTemplate::FIELD_NAME,
            EmailTemplate::FIELD_SUBJECT,
            EmailTemplate::FIELD_CONTENT,
            EmailTemplate::FIELD_PERSONAL,
            EmailTemplate::FIELD_TYPE,
            EmailTemplate::FIELD_INDUSTRY_ID,
            EmailTemplate::FIELD_BACKGROUND_ID,
            EmailTemplate::FIELD_ENGINE,
            EmailTemplate::FIELD_ACTIVE,
            EmailTemplate::FIELD_PAYLOAD,
        ]);

        $userId = Auth::id();

        $emailTemplate = $this->emailTemplateService->getTemplateById($templateId);

        if($emailTemplate->personal && $emailTemplate->{EmailTemplate::FIELD_OWNER_USER_ID} !== $userId) {
            throw new Exception("Unauthorized: User is not the template owner. Template Owner Id: $emailTemplate->owner_user_id, User: $userId");
        }

        $success = $this->emailTemplateService->updateTemplate(
            id: $templateId,
            ownerUserId: $emailTemplate->owner_user_id,
            name: $data[EmailTemplate::FIELD_NAME],
            subject: $data[EmailTemplate::FIELD_SUBJECT],
            content: $data[EmailTemplate::FIELD_CONTENT],
            personal: $data[EmailTemplate::FIELD_PERSONAL],
            type: $data[EmailTemplate::FIELD_TYPE],
            active: $data[EmailTemplate::FIELD_ACTIVE],
            industryId: $data[EmailTemplate::FIELD_INDUSTRY_ID] ?? null,
            backgroundId: $data[EmailTemplate::FIELD_BACKGROUND_ID],
            engine: $data[EmailTemplate::FIELD_ENGINE]?? null,
            payload: $data[EmailTemplate::FIELD_PAYLOAD]?? [],
        );

        $updateTime = EmailTemplate::findOrFail($templateId)->{EmailTemplate::FIELD_UPDATED_AT};

        return $this->formatResponse([
            "status" => $success,
            "last_updated" => $updateTime->format('Y-m-d H:i:s')." ".strtoupper($updateTime->timezone->getAbbreviatedName())
        ]);
    }

    /**
     * @param int $backgroundId
     * @param EmailTemplateBackgroundRequest $emailTemplateBackgroundRequest
     * @return JsonResponse
     * @throws Exception
     */
    public function updateEmailTemplateBackground(int $backgroundId, EmailTemplateBackgroundRequest $emailTemplateBackgroundRequest): JsonResponse
    {
        $data = $emailTemplateBackgroundRequest->safe()->only([
            EmailTemplateBackground::FIELD_NAME,
            EmailTemplateBackground::FIELD_HEADER,
            EmailTemplateBackground::FIELD_FOOTER,
            EmailTemplateBackground::FIELD_PERSONAL,
            EmailTemplateBackground::FIELD_INDUSTRY_ID,
        ]);

        $userId = Auth::id();

        $backgroundTemplate = $this->emailTemplateBackgroundService->getBackgroundTemplateById(id: $backgroundId);

        if($backgroundTemplate->personal && $backgroundTemplate->{EmailTemplate::FIELD_OWNER_USER_ID} !== $userId) {
            throw new Exception("Unauthorized: User is not the template owner. Template Owner Id: $backgroundTemplate->owner_user_id, User: $userId");
        }

        $success = $this->emailTemplateBackgroundService->updateTemplate(
            $backgroundId,
            $backgroundTemplate->owner_user_id,
            $data[EmailTemplateBackground::FIELD_NAME],
            $data[EmailTemplateBackground::FIELD_HEADER],
            $data[EmailTemplateBackground::FIELD_FOOTER],
            $data[EmailTemplateBackground::FIELD_PERSONAL],
            $data[EmailTemplateBackground::FIELD_INDUSTRY_ID] ?? null,
        );

        $updateTime = EmailTemplateBackground::findOrFail($backgroundId)->{EmailTemplateBackground::FIELD_UPDATED_AT};

        return $this->formatResponse([
            "status" => $success,
            "last_updated" => $updateTime->format('Y-m-d H:i:s')." ".strtoupper($updateTime->timezone->getAbbreviatedName())
        ]);
    }

    /**
     * @param int $templateId
     * @return JsonResponse
     */
    public function deleteEmailTemplate(int $templateId): JsonResponse
    {
        return $this->formatResponse([
            "status" => $this->emailTemplateService->deleteTemplate($templateId)
        ]);
    }

    /**
     * @param int $templateId
     * @return JsonResponse
     */
    public function deleteEmailBackgroundTemplate(int $templateId): JsonResponse
    {
        return $this->formatResponse([
            "status" => $this->emailTemplateBackgroundService->deleteBackgroundTemplate($templateId)
        ]);
    }

    /**
     * @param EmailTemplateImageRequest $emailTemplateImageRequest
     * @param EmailTemplateImageService $emailTemplateImageService
     * @return JsonResponse
     * @throws Exception
     */
    public function saveTemplateImage(EmailTemplateImageRequest $emailTemplateImageRequest, EmailTemplateImageService $emailTemplateImageService): JsonResponse
    {
        $data = $emailTemplateImageRequest->safe()->only([
            self::REQUEST_IMAGE_NAME,
            self::REQUEST_IMAGE_DATA_URL,
            self::REQUEST_TEMPLATE_ID,
            self::REQUEST_TEMPLATE_TYPE
        ]);

        $storageObject = $emailTemplateImageService->uploadImageDataUrlToCloudStorage($data[self::REQUEST_TEMPLATE_ID], $data[self::REQUEST_IMAGE_NAME], $data[self::REQUEST_IMAGE_DATA_URL]);

        return $this->formatResponse([
            "status" => $storageObject->exists(),
            "filename" => $data[self::REQUEST_IMAGE_NAME]
        ]);
    }

    /**
     * Send a test email of the template to the users email address
     * todo Allow for multiple types of email templates
     *
     * @param int $templateId
     * @param TestProductService $testProductService
     * @return JsonResponse
     * @throws Exception
     */
    public function sendTestEmail(int $templateId, TestProductService $testProductService): JsonResponse
    {
        $emailTemplate = $this->emailTemplateService->getTemplateById($templateId);

        //global industry
        if(is_null($emailTemplate->industry_id))
            throw new Exception("Template must have a specific industry set.");

        //todo create data and relation to existing consumer
        $testProduct = new TestProduct();
        $testProduct->email = Auth::user()?->email;
        $testProduct->save();

        /** todo create fake data payload for the engine outputs */
        SendEmailCalculatorResultToConsumer::dispatch($testProduct, [], $emailTemplate->engine);

        return $this->formatResponse([
            self::RESPONSE_STATUS => true,
        ]);
    }
}
