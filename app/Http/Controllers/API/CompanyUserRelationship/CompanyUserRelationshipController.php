<?php

namespace App\Http\Controllers\API\CompanyUserRelationship;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\CompanyUserRelationship\ListCompanyUserRelationshipRequest;
use App\Http\Requests\CompanyUserRelationship\UpdateCompanyUserRelationshipRequest;
use App\Http\Resources\CompanyUserRelationship\CompanyUserRelationshipResource;
use App\Http\Resources\Odin\RoleOptionResource;
use App\Models\CompanyUserRelationship;
use App\Repositories\CompanyUserRelationship\CompanyUserRelationshipRepository;
use App\Services\CompanyUserRelationship\CompanyUserRelationshipService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Arr;

class CompanyUserRelationshipController extends APIController
{
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected CompanyUserRelationshipService $companyUserRelationshipService,
        protected CompanyUserRelationshipRepository $companyUserRelationshipRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param ListCompanyUserRelationshipRequest $request
     * @return array
     */
    public function list(ListCompanyUserRelationshipRequest $request): array
    {
        $validated = $request->validated();

        $query = $this->companyUserRelationshipService->list(
            companyId: Arr::get($validated, ListCompanyUserRelationshipRequest::COMPANY_ID),
            name: Arr::get($validated, ListCompanyUserRelationshipRequest::NAME),
            roles: Arr::get($validated, ListCompanyUserRelationshipRequest::ROLES),
            active: Arr::get($validated, ListCompanyUserRelationshipRequest::ACTIVE),
        )->latest();

        return CompanyUserRelationshipResource::paginate($query);
    }

    /**
     * @return AnonymousResourceCollection
     */
    public function listCompanyUserRelationshipRoles(): AnonymousResourceCollection
    {
        $roles = $this->companyUserRelationshipRepository->listRoles();

        return RoleOptionResource::collection($roles);
    }

    /**
     * @param int $id
     * @return CompanyUserRelationshipResource
     */
    public function getCompanyUserRelationship(int $id): CompanyUserRelationshipResource
    {
        $cur = CompanyUserRelationship::withTrashed()->findOrFail($id);

        return new CompanyUserRelationshipResource($cur);
    }

    /**
     * @param int $id
     * @param UpdateCompanyUserRelationshipRequest $request
     * @return JsonResponse
     */
    public function updateCompanyUserRelationship(int $id, UpdateCompanyUserRelationshipRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $cur = CompanyUserRelationship::withTrashed()->findOrFail($id);

        $commissionableAt = Arr::get($validated, UpdateCompanyUserRelationshipRequest::COMMISSIONABLE_FROM);
        $commissionableAt = $commissionableAt ? Carbon::parse($commissionableAt) : $commissionableAt;

        $commissionableTo = Arr::get($validated, UpdateCompanyUserRelationshipRequest::COMMISSIONABLE_TO);
        $commissionableTo = $commissionableTo ? Carbon::parse($commissionableTo) : $commissionableTo;

        return $this->formatResponse([
            'status' => $this->companyUserRelationshipService->update(
                companyUserRelationship: $cur,
                commissionableAt: $commissionableAt,
                commissionableTo: $commissionableTo,
            ),
        ]);
    }

}
