<?php

namespace App\Http\Controllers\API;

use App\Services\TinymceFileUploadService;
use Exception;
use Illuminate\Http\JsonResponse;

class TinymceEditorController extends APIController
{
    const REQUEST_FILE = 'file';

    /**
     * @param TinymceFileUploadService $tinymceFileUploadService
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function uploadFile(TinymceFileUploadService $tinymceFileUploadService): JsonResponse
    {
        $this->validate($this->request, [
            self::REQUEST_FILE => 'mimetypes:image/jpeg,image/png,image/gif,audio/mpeg,video/mp4,video/mpeg,application/pdf|max:51200'
        ]);

        return $this->formatResponse([
            'url' => $tinymceFileUploadService->upload($this->request->file(self::REQUEST_FILE))
        ]);
    }
}
