<?php
namespace App\Http\Controllers;

use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Repositories\Odin\ConsumerRepository;
use App\Transformers\Odin\ConsumerProductProcessingTransformer;
use Carbon\Carbon;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use Illuminate\Validation\UnauthorizedException;

class ConsumerProductController extends Controller
{
    const string CONSUMER_PRODUCT_ID = 'consumer_product_id';
    const string CONSUMER_ID = 'consumer_id';
    const string PRODUCT_ASSIGNMENT_ID = 'product_assignment_id';

    public function __construct(protected Request $request) {}

    /**
     * @param ConsumerProductRepository $consumerProductRepository
     *
     * @return View
     */
    public function index(
        ConsumerProductRepository $consumerProductRepository,
        ConsumerProductProcessingTransformer $consumerProductProcessingTransformer,
        ConsumerRepository $consumerRepository): View
    {
        /** @var User $user */
        $user = Auth::user();

        try {
            if (!$user->hasPermissionTo('consumer-product/view')) {
                throw new UnauthorizedException('You do not have permission to access this page');
            }

            if($this->request->has(self::CONSUMER_ID))
                $consumerProduct = $consumerRepository->findOrFail($this->request->get(self::CONSUMER_ID))->consumerProducts()->first();
            else if ($this->request->has(self::CONSUMER_PRODUCT_ID))
                $consumerProduct = $consumerProductRepository->findOrFail($this->request->get(self::CONSUMER_PRODUCT_ID));
            else
                $consumerProduct = $consumerProductRepository->findByProductAssignmentIdOrFail($this->request->get(self::PRODUCT_ASSIGNMENT_ID));

            if ($this->isLeadOlderThan90Days($consumerProduct) && !$user->hasPermissionTo('consumer-product/view-leads-longer-than-90-days')){
                throw new UnauthorizedException('You do not have permission to view leads older than 90 days');
            }

            return view('consumer-product', [
                'consumerProduct' => $consumerProduct,
                'futureCampaign' => $consumerProduct->industryService->industry->industryConfiguration?->future_campaigns_active ?? false,
                'leadBasic' => $consumerProductProcessingTransformer->transform($consumerProduct, 'display')
            ]);
        } catch (UnauthorizedException $exception) {
            return view('consumer-product', ['error' => $exception->getMessage()]);
        }
    }

    /**
     * @param ConsumerProduct $lead
     *
     * @return bool
     */
    private function isLeadOlderThan90Days(ConsumerProduct $lead): bool
    {
        // Since 90 days strict permission is in place, don't show up a lead if its date is not confirmed.
        if(empty($lead->created_at)) {
            return true;
        }

        $createdAt = Carbon::parse($lead->created_at);
        $now = Carbon::now();

        return $createdAt->diffInDays($now, true) > 90;
    }
}
