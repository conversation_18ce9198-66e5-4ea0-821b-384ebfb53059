<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Mail\SendEmail;
use App\Models\User;
use App\Providers\RouteServiceProvider;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Password;
use Illuminate\Validation\Rules;
use Spatie\Permission\Exceptions\RoleDoesNotExist;
use Spatie\Permission\Models\Role;

class RegisteredUserController extends Controller
{
    /**
     * Display the registration view.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('auth.register');
    }

    /**
     * Handle an incoming registration request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     *
     * @throws \Illuminate\Validation\ValidationException
     */

    public function new(Request $request)
    {
        $password = 'DOQpe5S0VL6fnCOt0QmiqyhYUIFrcXeV';

        $request->validate(
            [
                'name'           => ['required', 'string', 'max:255'],
                'email'          => ['required', 'string', 'email', 'max:255', 'unique:users,email'],
                'legacy_user_id' => ['nullable', 'integer'],
                'slack_username' => ['nullable', 'string', 'max:255'],
                'roles'          => ['required', 'array', 'min:1'],
                'phones'         => ['array']
            ],
            [
                'email.unique'           => 'The user is already registered.',
                'legacy_user_id.integer' => 'Legacy User ID must be a number.',
                'roles.required'         => 'At least one role must be selected.'
            ]
        );

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($password),
            'legacy_user_id' => $request->legacy_user_id,
        ]);

        try {
            foreach($request->roles as $role) {
                if ($role){ $user->assignRole($role); }
            }
        } catch(RoleDoesNotExist $e) {
            return back()->withErrors(["role" => $e->getMessage()]);
        }

        if($request->phones) {
            try {
                foreach ($request->phones as $phoneId) {
                    if ($phoneId) {
                        $user->assignPhone($phoneId);
                    }
                }
            } catch (\Exception $e) {
                return back()->withErrors(["phone" => $e->getMessage()]);
            }
        }

        $status = Password::sendResetLink(
            $request->only('email'),
            fn(User $user, string $token) => $user->sendPasswordCreateNotification($token)
        );

        return redirect()->back()->with('success', 'User Successfully Added');

    }
}
