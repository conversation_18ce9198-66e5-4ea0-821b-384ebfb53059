<?php

namespace App\Http\Controllers;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Services\Odin\CompanyQualityScoreRuleService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CompanyQualityScoreConfigurationController extends APIController
{
    const MODULE_PERMISSION         = 'company_quality_score_management';

    const REQUEST_GLOBAL            = 'global';
    const REQUEST_INDUSTRY_ID       = 'industry_id';
    const REQUEST_TEST_COMPANIES    = 'test_company_ids';
    const REQUEST_RULE_ID           = 'rule_id';

    const RESPONSE_STATUS           = 'status';
    const RESPONSE_MESSAGE          = 'message';
    const RESPONSE_CONFIGURATIONS   = 'configurations';
    const RESPONSE_RULE_OPTIONS     = 'rule_options';
    const RESPONSE_TEST_COMPANIES   = 'test_companies';

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected CompanyQualityScoreRuleService $companyQualityScoreRuleService
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    public function getConfigurations(): JsonResponse
    {
        $configurations = $this->companyQualityScoreRuleService->getConfigurations();

        return $this->formatResponse([
            self::RESPONSE_STATUS           => !!$configurations,
            self::RESPONSE_CONFIGURATIONS   => $configurations,
            self::RESPONSE_RULE_OPTIONS  => $this->companyQualityScoreRuleService->getProductionRules(),
        ]);
    }

    public function updateTestCompanies(): JsonResponse
    {
        $isGlobal = !!$this->request->get(self::REQUEST_GLOBAL);
        $industryId = $this->request->get(self::REQUEST_INDUSTRY_ID);
        $companyIds = $this->request->get(self::REQUEST_TEST_COMPANIES);

        if (!$isGlobal && !$industryId) {
            throw new ModelNotFoundException("An industry ID must be supplied unless updating the Global Configuration");
        }

        $success = $this->companyQualityScoreRuleService->setTestCompanyIds($industryId, $companyIds, $isGlobal);

        return $this->formatResponse([
            self::RESPONSE_STATUS   => $success,
        ]);
    }

    public function updateIndustryRule(): JsonResponse
    {
        $isGlobal = !!$this->request->get(self::REQUEST_GLOBAL);
        $industryId = $this->request->get(self::REQUEST_INDUSTRY_ID);
        $ruleId = $this->request->get(self::REQUEST_RULE_ID);

        $success = $isGlobal
            ? $this->companyQualityScoreRuleService->setGlobalRule($ruleId)
            : $this->companyQualityScoreRuleService->setIndustryRule($industryId, $ruleId);

        return $this->formatResponse([
            self::RESPONSE_STATUS   => $success
        ]);
    }

    public function getTestCompanies(): JsonResponse
    {
        $industryId = intval($this->request->route('industryId'));

        $testCompanies = $industryId === 0
            ? $this->companyQualityScoreRuleService->getTestCompanies(null, true)
            : $this->companyQualityScoreRuleService->getTestCompanies($industryId);

        return $this->formatResponse([
            self::RESPONSE_STATUS   => !!$testCompanies,
            self::RESPONSE_TEST_COMPANIES   => $testCompanies,
        ]);
    }


}
