<?php

namespace App\Http\Controllers\FlowEngines;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\FlowEngines\DeleteConfigurableVariableDataRequest;
use App\Http\Requests\FlowEngines\GetConfigurableVariableDataRequest;
use App\Http\Requests\FlowEngines\SaveConfigurableVariableDataRequest;
use App\Services\Engines\Client\ConfigurableEngineVariableClientFactory;
use App\Services\Engines\Client\ConfigurableEngineVariableClientService;
use App\Services\LocationService;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\RequestException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class ConfigurableVariableController extends APIController
{
    protected ConfigurableEngineVariableClientService $configurableEngineVariableClientService;

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected LocationService $locationService,
    )
    {
        $this->configurableEngineVariableClientService = ConfigurableEngineVariableClientFactory::make('api');

        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param GetConfigurableVariableDataRequest $request
     * @return JsonResponse
     * @throws ConnectionException
     * @throws RequestException
     */
    public function getConfigurableVariableData(GetConfigurableVariableDataRequest $request): JsonResponse
    {
        $filter = $request->validated();

        $data = $this->configurableEngineVariableClientService->getConfigurableEngineVariables(
            state : Arr::get($filter, GetConfigurableVariableDataRequest::STATE),
            county: Arr::get($filter, GetConfigurableVariableDataRequest::COUNTY),
            engine: Arr::get($filter, GetConfigurableVariableDataRequest::ENGINE),
            key   : Arr::get($filter, GetConfigurableVariableDataRequest::KEY),
        );

        return $this->formatResponse($data);
    }


    /**
     * @param SaveConfigurableVariableDataRequest $request
     * @return JsonResponse
     * @throws ConnectionException
     * @throws RequestException
     */
    public function saveConfigurableVariable(SaveConfigurableVariableDataRequest $request): JsonResponse
    {
        $payload = $request->validated();

        $data = $this->configurableEngineVariableClientService->saveConfigurableEngineVariables($payload);

        return response()->json($data);
    }


    /**
     * @param int $variableId
     * @param DeleteConfigurableVariableDataRequest $request
     * @return JsonResponse
     * @throws ConnectionException
     * @throws RequestException
     */
    public function deleteConfigurableVariable(int $variableId, DeleteConfigurableVariableDataRequest $request): JsonResponse
    {
        $request->validated();

        $data = $this->configurableEngineVariableClientService->deleteConfigurableEngineVariables($variableId);

        return $this->formatResponse($data);
    }
}
