<?php

namespace App\Http\Controllers;

use App\Enums\Team\TeamType as TeamTypeEnum;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\StoreTeamRequest;
use App\Http\Resources\Teams\TeamResource;
use App\Http\Resources\Teams\TeamTypeResource;
use App\Models\Teams\Team;
use App\Models\Teams\TeamLeader;
use App\Models\Teams\TeamMember;
use App\Models\Teams\TeamType;
use App\Models\User;
use App\Repositories\TeamRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class TeamManagementController extends APIController
{
    const MODULE_PERMISSION = 'team-management';

    const REQUEST_UPDATE    = 'update';
    const REQUEST_TEAM_TYPE = 'team_type';

    const RESPONSE_TEAMS        = 'teams';
    const RESPONSE_STATUS       = 'status';
    const RESPONSE_TEAM_TYPES   = 'team_types';

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected TeamRepository $teamRepository,
    )
    {
        parent::__construct(
            $request,
            $apiResponseFactory,
        );
    }

    /**
     * @return JsonResponse
     */
    public function getAllTeams(): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_STATUS => true,
            self::RESPONSE_TEAMS  => TeamResource::collection(Team::all()),
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getTeamTypes(): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_STATUS      => true,
            self::RESPONSE_TEAM_TYPES  => TeamTypeResource::collection(TeamType::all()),
        ]);
    }

    /**
     * @param StoreTeamRequest $request
     * @return JsonResponse
     */
    public function createNewTeam(StoreTeamRequest $request): JsonResponse
    {
        $teamData = $request->safe()->collect();
        $memberData = collect($request->get(Team::RELATION_TEAM_MEMBERS));

        $teamCreated = $this->teamRepository->createTeamFromLeader(
            $teamData->get(Team::RELATION_TEAM_TYPE)[TeamType::FIELD_ID],
            $teamData->get(Team::FIELD_NAME),
            $teamData->get(Team::FIELD_DESCRIPTION) ?? "",
            $teamData->get(Team::RELATION_TEAM_LEADER)[TeamLeader::FIELD_USER_ID],
            $teamData->get(Team::RELATION_TEAM_LEADER)[TeamLeader::FIELD_TITLE] ?? ""
        );
        if ($teamCreated) {
            $memberData->each(fn(array $member) => $this->teamRepository->createTeamMemberFromUser(
                $teamCreated,
                $member[TeamMember::FIELD_USER_ID],
                $member[TeamMember::FIELD_TITLE] ?? "")
            );
        }

        return $this->formatResponse([
            self::RESPONSE_STATUS  => !!$teamCreated,
        ]);
    }

    /**
     * @param StoreTeamRequest $request
     * @return JsonResponse
     */
    public function updateTeam(StoreTeamRequest $request): JsonResponse
    {
        $validated = $request->safe()->collect();
        $teamData = $validated->toArray();
        $leaderData = $validated->get(Team::RELATION_TEAM_LEADER) ?? [];
        $memberData = $request->get(Team::RELATION_TEAM_MEMBERS) ?? [];

        return $this->formatResponse([
            self::RESPONSE_STATUS  => $this->teamRepository->updateTeam($teamData, $leaderData, $memberData),
        ]);
    }

    /**
     * @param int $teamId
     * @return JsonResponse
     */
    public function deleteTeam(int $teamId): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_STATUS  => $this->teamRepository->deleteTeam($teamId),
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getLeaderTeams(): JsonResponse
    {
        $userId = Auth::user()?->{User::FIELD_ID} ?? null;
        $teamType = $this->request->get(self::REQUEST_TEAM_TYPE) ?? null;
        $teamTypeId = $teamType
            ? TeamType::query()->where(TeamType::FIELD_NAME, $teamType)->first()?->{TeamType::FIELD_ID}
            : null;

        return $this->formatResponse([
            self::RESPONSE_STATUS => !!$userId,
            self::RESPONSE_TEAMS  => TeamResource::collection($this->teamRepository->findTeamsByLeader($userId, $teamTypeId)),
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getSalesTeams(): JsonResponse
    {
        $teamTypeId = TeamType::query()
            ->where(TeamType::FIELD_NAME, TeamTypeEnum::SALES)
            ->first()
            ?->{TeamType::FIELD_ID};

        /** @var Collection<Team>|null $teams */
        $teams = $teamTypeId
            ? $this->teamRepository->findTeamsByType($teamTypeId)
            : null;

        return $this->formatResponse([
            self::RESPONSE_STATUS => !!$teams,
            self::RESPONSE_TEAMS  => TeamResource::collection($teams),
        ]);
    }
}
