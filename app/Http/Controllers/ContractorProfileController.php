<?php

namespace App\Http\Controllers;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Models\CompanySlug;
use App\Models\ContractorProfile\ContractorProfile;
use App\Models\Odin\Company;
use App\Repositories\CompanySlugRepository;
use App\Repositories\ContractorProfileRepository;
use App\Services\CompanySlugService;
use App\Services\ContractorProfileService;
use App\Transformers\ContractorProfile\ContractorProfileCompanyTransformer;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ContractorProfileController extends APIController
{

    const string PARAM_SLUG              = 'slug';
    const string PARAM_COMPANY_ID        = 'company_id';
    const string PARAM_TIMEZONE          = 'timezone';
    const string PARAM_TIMES             = 'times';
    const string PARAM_BRANDS_SOLD       = 'brands_sold';
    const string PARAM_LICENSES          = 'licenses';
    const string PARAM_CERTIFICATIONS    = 'certifications';
    const string PARAM_INTRODUCTION      = 'introduction';
    const string PARAM_CUSTOMERS_LIKE    = 'customers_like';
    const string PARAM_CUSTOMERS_DISLIKE = 'customers_dislike';
    const string PARAM_EXPERT_RATING     = 'expert_rating';
    const string PARAM_SERVICES          = 'services';

    const string RESPONSE_CODE            = 'response_code';
    const string RESPONSE_CONTRACTOR_DATA = 'contractor_data';
    const string RESPONSE_DISPLAY_PROFILE = 'display_profile';

    const string REDIRECT_PATH  = 'redirect_path';


    public function __construct(
        protected Request                             $request,
        protected JsonAPIResponseFactory              $apiResponseFactory,
        protected CompanySlugService                  $slugService,
        protected CompanySlugRepository               $slugRepository,
        protected ContractorProfileService            $profileService,
        protected ContractorProfileCompanyTransformer $companyTransformer,
        protected ContractorProfileRepository         $profileRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getProfileEntry(Request $request): JsonResponse
    {
        $slug        = $request->get(self::PARAM_SLUG);
        $companySlug = $this->slugRepository->getCompanySlug($slug);

        if (!$companySlug || !$this->profileService->shouldDisplay($companySlug->company)) {
            return $this->getInvalidPageResponse();
        }

        if ($companySlug->redirect_company_slug_id) {
            $redirectSlug = $this->slugService->getFinalRedirectSlug($companySlug);
            return $this->getRedirectResponse($redirectSlug);
        }

        return $this->getValidPageResponse($companySlug->company, $request);
    }

    /**
     * @return JsonResponse
     */
    private function getInvalidPageResponse(): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_CODE => 404
        ]);
    }

    private function getRedirectResponse(CompanySlug $redirectSlug): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_CODE => 302,
            self::REDIRECT_PATH => '/contractors/' . $redirectSlug->slug,
        ]);
    }

    private function getValidPageResponse(Company $company, Request $request): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_CODE            => 200,
            self::RESPONSE_CONTRACTOR_DATA => $this->companyTransformer->transform($company, $request),
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function toggleDisplay(Request $request): JsonResponse
    {
        $companyId = ($request->validate([self::PARAM_COMPANY_ID => 'required|numeric']))[self::PARAM_COMPANY_ID];
        return $this->formatResponse([self::RESPONSE_DISPLAY_PROFILE => $this->profileService->toggleDisplayProfile($companyId)]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getBusinessHours(Request $request): JsonResponse
    {
        $companyId = ($request->validate([self::PARAM_COMPANY_ID => 'required|numeric']))[self::PARAM_COMPANY_ID];
        return $this->formatResponse($this->profileRepository->getBusinessHours($companyId));
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function updateBusinessHours(Request $request): JsonResponse
    {
        $validated = $request->validate([
            self::PARAM_COMPANY_ID => 'required|numeric',
            self::PARAM_TIMEZONE   => 'required|string',
            self::PARAM_TIMES      => 'required|array',
        ]);

        $this->profileService->updateBusinessHours(
            $validated[self::PARAM_COMPANY_ID],
            $validated[self::PARAM_TIMEZONE],
            $validated[self::PARAM_TIMES]
        );

        return $this->formatResponse();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getBrandsAndServices(Request $request): JsonResponse
    {
        $companyId = ($request->validate([self::PARAM_COMPANY_ID => 'required|numeric']))[self::PARAM_COMPANY_ID];
        return $this->formatResponse([
            self::PARAM_BRANDS_SOLD => $this->profileRepository->getBrandsSold($companyId),
            self::PARAM_SERVICES => $this->profileRepository->getServicesOffered($companyId),
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function updateBrandsAndServices(Request $request): JsonResponse
    {
        $validated = $request->validate([
            self::PARAM_COMPANY_ID  => 'required|numeric',
            self::PARAM_BRANDS_SOLD => 'present|array',
            self::PARAM_SERVICES    => 'present|array',
        ]);

        $this->profileRepository->updateBrandsSold(
            $validated[self::PARAM_COMPANY_ID],
            $validated[self::PARAM_BRANDS_SOLD],
        );

        $this->profileRepository->updateServicesOffered(
            $validated[self::PARAM_COMPANY_ID],
            $validated[self::PARAM_SERVICES],
        );

        return $this->formatResponse();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getCredentials(Request $request): JsonResponse
    {
        $companyId = ($request->validate([self::PARAM_COMPANY_ID => 'required|numeric']))[self::PARAM_COMPANY_ID];
        return $this->formatResponse($this->profileRepository->getCredentials($companyId));
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function updateCredentials(Request $request): JsonResponse
    {
        $validated = $request->validate([
            self::PARAM_COMPANY_ID     => 'required|numeric',
            self::PARAM_LICENSES       => 'present|array',
            self::PARAM_CERTIFICATIONS => 'present|array',
        ]);

        $this->profileRepository->updateCredentials(
            $validated[self::PARAM_COMPANY_ID],
            $validated[self::PARAM_LICENSES],
            $validated[self::PARAM_CERTIFICATIONS]
        );
        return $this->formatResponse();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getContent(Request $request): JsonResponse
    {
        $companyId = ($request->validate([self::PARAM_COMPANY_ID => 'required|numeric']))[self::PARAM_COMPANY_ID];
        $profile   = $this->profileRepository->findByCompanyId($companyId);
        return $this->formatResponse([
            self::PARAM_EXPERT_RATING     => $profile->rating,
            self::PARAM_INTRODUCTION      => $profile->introduction,
            self::PARAM_CUSTOMERS_LIKE    => $profile->customers_like,
            self::PARAM_CUSTOMERS_DISLIKE => $profile->customers_dislike,
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function updateContent(Request $request): JsonResponse
    {
        $validated = $request->validate([
            self::PARAM_COMPANY_ID        => 'required|numeric',
            self::PARAM_EXPERT_RATING     => 'present|numeric|between:0.0,5.0',
            self::PARAM_INTRODUCTION      => 'present',
            self::PARAM_CUSTOMERS_LIKE    => 'present',
            self::PARAM_CUSTOMERS_DISLIKE => 'present',
        ]);
        $profile   = $this->profileRepository->findByCompanyId($validated[self::PARAM_COMPANY_ID]);
        $profile->update([
            ContractorProfile::FIELD_RATING     => $validated[self::PARAM_EXPERT_RATING],
            ContractorProfile::FIELD_INTRODUCTION      => $validated[self::PARAM_INTRODUCTION],
            ContractorProfile::FIELD_CUSTOMERS_LIKE    => $validated[self::PARAM_CUSTOMERS_LIKE],
            ContractorProfile::FIELD_CUSTOMERS_DISLIKE => $validated[self::PARAM_CUSTOMERS_DISLIKE],
        ]);
        return $this->formatResponse();
    }

}
