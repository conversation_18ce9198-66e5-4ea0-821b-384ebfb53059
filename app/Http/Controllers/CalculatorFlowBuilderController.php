<?php

namespace App\Http\Controllers;

use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class CalculatorFlowBuilderController extends Controller
{
    const FLOW_ID = 'flowId';
    const REVISION_ID = 'revisionId';
    const VERSION = 'version';

    /**
     * @param Request $request
     *
     * @return Factory|View|Application
     * @throws ValidationException
     */
    public function flowEditor(Request $request): Factory|View|Application
    {
        $this->validate($request, [
            self::FLOW_ID => 'string',
            self::REVISION_ID => 'string',
            self::VERSION   => 'string'
        ]);

        $flowId = $request->get(self::FLOW_ID);
        $revisionId = $request->get(self::REVISION_ID);
        $version = $request->get(self::VERSION);

        return view('calculator-builder', compact('flowId', 'revisionId', 'version'));
    }
}
