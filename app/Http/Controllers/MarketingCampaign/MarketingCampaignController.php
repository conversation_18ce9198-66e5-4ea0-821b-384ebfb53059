<?php

namespace App\Http\Controllers\MarketingCampaign;

use App\Contracts\Services\MarketingCampaign\InternalEmailMarketingService;
use App\DTO\EmailService\OutgoingEmailDTO;
use App\DTO\SMS;
use App\Enums\GlobalConfigurationKey;
use App\Enums\MarketingCampaigns\GlobalConfigurationMarketingField;
use App\Enums\MarketingCampaigns\MarketingCampaignCallbackType;
use App\Enums\MarketingCampaigns\MarketingCampaignStatus;
use App\Enums\MarketingCampaigns\MarketingCampaignType;
use App\Exceptions\CustomValidationException;
use App\Exceptions\MarketingCampaign\EmailBatchSendFailedException;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\MarketingCampaign\CreateMarketingCampaignRequest;
use App\Http\Requests\MarketingCampaign\GetMarketingCampaignOptionsRequest;
use App\Http\Requests\MarketingCampaign\ListMarketingCampaignsRequest;
use App\Http\Requests\MarketingCampaign\ListMarketingCampaignTypeRequest;
use App\Http\Requests\MarketingCampaign\SendTestMarketingEmailRequest;
use App\Http\Requests\MarketingCampaign\SendTestMarketingSMSRequest;
use App\Http\Requests\MarketingCampaign\UpdateMarketingCampaignRequest;
use App\Http\Requests\MarketingCampaign\UpdateMarketingCampaignTargetsRequest;
use App\Http\Resources\MarketingCampaign\ListMarketingCampaignsResource;
use App\Http\Resources\MarketingCampaign\MarketingCampaignCallbackListOptionResource;
use App\Http\Resources\MarketingCampaign\MarketingCampaignListOptionResource;
use App\Http\Resources\MarketingCampaign\MarketingCampaignResource;
use App\Http\Resources\MarketingCampaign\MarketingCampaignTypeListOptionResource;
use App\Jobs\MarketingCampaign\SendTestMarketingEmail;
use App\Jobs\MarketingCampaign\UpdateEmailMarketingCampaignMetrics;
use App\Jobs\SendSMS;
use App\Models\EmailTemplate;
use App\Models\MarketingCampaign;
use App\Models\User;
use App\Repositories\GlobalConfiguration\GlobalConfigurationRepository;
use App\Repositories\MarketingCampaign\MarketingCampaignRepository;
use App\Services\Filterables\EmailMarketing\MarketingConsumerFilterableService;
use App\Services\MarketingCampaign\DripCampaignService;
use App\Services\MarketingCampaign\MarketingCampaignService;
use App\Services\QueueHelperService;
use App\Services\Shortcode\EmailTemplateShortcodeReplacerService;
use App\Services\Shortcode\ShortcodeImplementation\EmailMarketingShortcodeUseCase;
use App\Services\Shortcode\ShortcodeReplacerService;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;

class MarketingCampaignController extends APIController
{
    const string RESPONSE_STATUS = 'status';
    const string RESPONSE_FILTER_OPTIONS = 'filter_options';

    public function __construct(
        Request                               $request,
        JsonAPIResponseFactory                $apiResponseFactory,
        protected MarketingCampaignService    $service,
        protected MarketingCampaignRepository $repository,
        protected ShortcodeReplacerService    $replacerService,
        protected EmailTemplateShortcodeReplacerService $templateReplacerService,
        protected MarketingConsumerFilterableService $consumerFilterableService,
        protected DripCampaignService $dripService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param ListMarketingCampaignsRequest $request
     * @return array
     */
    public function listMarketingCampaigns(ListMarketingCampaignsRequest $request): array
    {
        $validated = $request->validated();

        $campaigns = $this->service->listMarketingCampaigns(
            status: MarketingCampaignStatus::tryFrom(Arr::get($validated, ListMarketingCampaignsRequest::REQUEST_STATUS)),
            name: Arr::get($validated, ListMarketingCampaignsRequest::REQUEST_NAME),
            types: Arr::get($validated, ListMarketingCampaignsRequest::REQUEST_TYPE),
        )->with(MarketingCampaign::RELATION_LOGS);

        return ListMarketingCampaignsResource::format($campaigns);
    }

    /**
     * @param GetMarketingCampaignOptionsRequest $request
     * @return JsonResponse
     */
    public function getMarketingCampaignOptions(GetMarketingCampaignOptionsRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $statuses = Arr::get($validated, GetMarketingCampaignOptionsRequest::REQUEST_STATUSES);
        $statuses = collect($statuses)->map(fn(string $status) => MarketingCampaignStatus::from($status))->all();
        $campaigns = $this->repository->getMarketingCampaignListOptions(
            statuses: $statuses,
            type: Arr::get($validated, GetMarketingCampaignOptionsRequest::REQUEST_TYPE)
        );

        return $this->formatResponse([
            'data' => MarketingCampaignListOptionResource::collection($campaigns),
        ]);
    }

    /**
     * @param CreateMarketingCampaignRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function createMarketingCampaign(CreateMarketingCampaignRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $campaign      = Arr::get($validated, UpdateMarketingCampaignRequest::REQUEST_CAMPAIGN);
        $filters       = Arr::get($validated, CreateMarketingCampaignRequest::REQUEST_FILTERS);
        $configuration = Arr::get($campaign, UpdateMarketingCampaignRequest::REQUEST_CAMPAIGN_CONFIGURATION);

        $authorId = Auth::user()->{User::FIELD_ID};

        $campaign = $this->service->createMarketingCampaign(
            authorId                             : $authorId,
            name                                 : Arr::get($campaign, CreateMarketingCampaignRequest::REQUEST_NAME),
            consumerFilters                      : $filters,
            type                                 : MarketingCampaignType::tryFrom(Arr::get($campaign, UpdateMarketingCampaignRequest::REQUEST_CAMPAIGN_TYPE)),
            description                          : Arr::get($campaign, UpdateMarketingCampaignRequest::REQUEST_DESCRIPTION),
            campaignCallbackType                 : MarketingCampaignCallbackType::tryFrom(Arr::get($campaign, UpdateMarketingCampaignRequest::REQUEST_VALIDATION_TYPE)),
            marketingCampaignCallbackCustomInputs: Arr::get($campaign, UpdateMarketingCampaignRequest::REQUEST_VALIDATION_TYPE_INPUTS),
            configuration                        : $configuration,
        );

        return $this->formatResponse([
            'status' => !!$campaign,
        ]);
    }

    /**
     * @param UpdateMarketingCampaignTargetsRequest $request
     * @return JsonResponse
     */
    public function updateMarketingCampaignTargets(UpdateMarketingCampaignTargetsRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $campaign = Arr::get($validated, UpdateMarketingCampaignRequest::REQUEST_CAMPAIGN);
        $filters  = Arr::get($validated, CreateMarketingCampaignRequest::REQUEST_FILTERS);

        $authorId = Auth::user()->{User::FIELD_ID};

        $campaign = $this->service->updateMarketingCampaignTargets(
            authorId           : $authorId,
            marketingCampaignId: Arr::get($campaign, UpdateMarketingCampaignTargetsRequest::REQUEST_MARKETING_CAMPAIGN_ID),
            filters            : $filters,
        );

        return $this->formatResponse([
            'status' => !!$campaign
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getMarketingCampaignCallbackTypes(): JsonResponse
    {
        return $this->formatResponse([
            'types' => MarketingCampaignCallbackListOptionResource::collection(MarketingCampaignCallbackType::cases())
        ]);
    }

    /**
     * @param ListMarketingCampaignTypeRequest $request
     * @return JsonResponse
     */
    public function getMarketingCampaignTypes(ListMarketingCampaignTypeRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $include = array_map(
            fn($value) => MarketingCampaignType::tryFrom($value),
            Arr::get($validated, ListMarketingCampaignTypeRequest::TYPE, [])
        );

        return $this->formatResponse([
            'types' => MarketingCampaignTypeListOptionResource::collection($include ?: MarketingCampaignType::cases())
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getMarketingCampaignShortcodes(): JsonResponse
    {
        $shortcodes = EmailMarketingShortcodeUseCase::getShortcodesList();

        return $this->formatResponse([
            'shortcodes' => $shortcodes,
        ]);
    }

    /**
     * @param int $marketingCampaignId
     * @return JsonResponse
     */
    public function getMarketingCampaign(int $marketingCampaignId): JsonResponse
    {
        $campaign = $this->repository->getMarketingCampaignById($marketingCampaignId);

        return $this->formatResponse([
            'campaign' => new MarketingCampaignResource($campaign)
        ]);
    }

    /**
     * @param int $marketingCampaignId
     * @param UpdateMarketingCampaignRequest $request
     * @return JsonResponse
     */
    public function updateMarketingCampaign(int $marketingCampaignId, UpdateMarketingCampaignRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $campaignUpdates = Arr::get($validated, UpdateMarketingCampaignRequest::REQUEST_CAMPAIGN);

        $configuration = Arr::get($campaignUpdates, UpdateMarketingCampaignRequest::REQUEST_CAMPAIGN_CONFIGURATION);

        $campaign = $this->repository->getMarketingCampaignById($marketingCampaignId);

        $payload = $this->service->createMarketingCampaignPayload(
            callbackType                   : MarketingCampaignCallbackType::tryFrom(Arr::get($campaignUpdates, UpdateMarketingCampaignRequest::REQUEST_VALIDATION_TYPE)),
            marketingCampaignCallbackInputs: Arr::get($campaignUpdates, UpdateMarketingCampaignRequest::REQUEST_VALIDATION_TYPE_INPUTS),
        );

        $authorId = Auth::user()->id;

        $updated = $this->repository->updateMarketingCampaign(
            marketingCampaign: $campaign,
            authorId         : $authorId,
            description      : Arr::get($campaignUpdates, UpdateMarketingCampaignRequest::REQUEST_DESCRIPTION),
            sentAt           : Arr::get($configuration, 'sent_at') ? Carbon::parse(Arr::get($configuration, 'sent_at')) : null,
            callbackPayload  : $payload,
            configuration    : $configuration
        );

        return $this->formatResponse([
            'status' => $updated,
        ]);
    }

    /**
     * @param int $marketingCampaignId
     * @return JsonResponse
     */
    public function updateMarketingCampaignMetrics(int $marketingCampaignId): JsonResponse
    {
        UpdateEmailMarketingCampaignMetrics::dispatch($marketingCampaignId);

        return $this->formatResponse([
            'status' => true,
        ]);
    }

    /**
     * @param int $marketingCampaignId
     * @return JsonResponse
     * @throws Exception
     */
    public function toggleMarketingCampaignStatus(int $marketingCampaignId): JsonResponse
    {
        $marketingCampaign = MarketingCampaign::query()->findOrFail($marketingCampaignId);

        $status = $marketingCampaign->{MarketingCampaign::FIELD_STATUS};

        $marketingCampaign->update([
            MarketingCampaign::FIELD_STATUS => $status === MarketingCampaignStatus::PAUSED
                ? MarketingCampaignStatus::ACTIVE
                : MarketingCampaignStatus::PAUSED
        ]);

        return $this->formatResponse([
            'status' => true,
        ]);
    }

    /**
     * @throws BindingResolutionException
     * @throws EmailBatchSendFailedException
     */
    public function sendTestMarketingEmail(SendTestMarketingEmailRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $emailTemplateId = Arr::get($validated, SendTestMarketingEmailRequest::EMAIL_TEMPLATE_ID);
        $fromName = Arr::get($validated, SendTestMarketingEmailRequest::FROM_NAME);
        $fromEmail = Arr::get($validated, SendTestMarketingEmailRequest::FROM_EMAIL);
        $toEmails = Arr::get($validated, SendTestMarketingEmailRequest::TO_EMAIL);

        $emailTemplate = EmailTemplate::query()->findOrFail($emailTemplateId);

        $usedShortcodes = $this->templateReplacerService->getUsedShortcodes($emailTemplate);

        $useCase = EmailMarketingShortcodeUseCase::mock();

        $compiledShortcodes = $useCase->compile($usedShortcodes)->toArray();

        foreach ($toEmails as $toEmail) {
            $target = new OutgoingEmailDTO(
                toEmail: $toEmail,
                fromEmail: $fromEmail,
                fromUsername: $fromName,
                subject: $emailTemplate->subject,
                body: $emailTemplate->content,
                shortcodes: $compiledShortcodes,
            );

            SendTestMarketingEmail::dispatch(
                $target,
                $emailTemplate,
                $fromName,
                $fromEmail,
            );
        }

        return $this->formatResponse([
            'status' => true,
        ]);

    }

    /**
     * @param SendTestMarketingSMSRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function sendTestMarketingSMS(SendTestMarketingSMSRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $globalConfigurationRepository = app(GlobalConfigurationRepository::class);
        $marketingConfig = $globalConfigurationRepository->getConfigurationPayload(GlobalConfigurationKey::MARKETING)?->toArray()['data'];

        $marketingMessagingServiceID        = $marketingConfig[GlobalConfigurationMarketingField::SMS_SERVICE_ID->value] ?? null;

        if(empty($marketingMessagingServiceID)){
            throw new CustomValidationException('Marketing Service ID has not been set in the global configuration');
        }

        $message = Arr::get($validated, SendTestMarketingSMSRequest::MESSAGE);

        $useCase = EmailMarketingShortcodeUseCase::mock();

        $shortcodeService = new ShortcodeReplacerService();

        $usedShortcodes = $shortcodeService->getAllShortcodes(rawText: $message);

        $compiledShortcodes = $useCase->compile($usedShortcodes)->toArray();

        $replaced = $shortcodeService->process(rawText: $message, shortcodeMap: $compiledShortcodes);

        $sms = new SMS(
            toPhone: Arr::get($validated, SendTestMarketingSMSRequest::TO_PHONE),
            from: $marketingMessagingServiceID,
            message: $replaced,
            fromType: SMS::FROM_TYPE_SERVICE,
            meta: [
                'shortenUrls' => true,
            ]
        );

        SendSMS::dispatch($sms, false)->onQueue(QueueHelperService::QUEUE_NAME_MARKETING);

        return $this->formatResponse([
            'status' => true,
        ]);
    }

    /**
     * @param MarketingCampaign $marketingCampaign
     * @return JsonResponse
     */
    public function deleteMarketingCampaign(MarketingCampaign $marketingCampaign): JsonResponse
    {
        $deleted = $this->service->deleteMarketingCampaign(campaign: $marketingCampaign);

        return $this->formatResponse([
            'status' => $deleted,
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getFilterOptions(): JsonResponse
    {
        $options = $this->consumerFilterableService->getDisplayData();

        return $this->formatResponse([
            self::RESPONSE_STATUS         => !!$options,
            self::RESPONSE_FILTER_OPTIONS => $options,
        ]);
    }

    /**
     * @throws Exception
     */
    public function getEstimate(Request $request): JsonResponse
    {
        $configuration = json_decode($request->input('configuration'), true);

        $spanType = Arr::get($configuration, 'span_type');
        $spanValue = Arr::get($configuration, 'span_value');

        $filters = json_decode($request->input('filters'), true);

        $startAt = Arr::get($configuration, 'start_at');

        $startAt = $startAt ? Carbon::parse($startAt) : now();

        $days = $this->dripService->processAnniversary(
            $spanType,
            $spanValue,
        );

        $estimate = $this->dripService->getEstimate(
            $filters,
            $startAt,
            $days,
        );

        return $this->formatResponse([
            'estimate' => $estimate,
        ]);
    }

}
