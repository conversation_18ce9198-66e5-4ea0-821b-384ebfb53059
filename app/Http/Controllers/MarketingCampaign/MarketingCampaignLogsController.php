<?php

namespace App\Http\Controllers\MarketingCampaign;

use App\Enums\MarketingCampaigns\MarketingLogRelationType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\MarketingCampaign\ListMarketingCampaignLogsRequest;
use App\Http\Resources\MarketingCampaign\GetMarketingLogResource;
use App\Http\Resources\MarketingCampaign\ListMarketingLogResource;
use App\Services\MarketingCampaign\MarketingLogService;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class MarketingCampaignLogsController extends APIController
{

    public function __construct(
        Request                       $request,
        JsonAPIResponseFactory        $apiResponseFactory,
        protected MarketingLogService $service,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param ListMarketingCampaignLogsRequest $request
     * @return array
     */
    public function listMarketingLogs(ListMarketingCampaignLogsRequest $request): array
    {
        $validated = $request->validated();
        $consumers = $this->service->listLogs(
            levels: Arr::get($validated, ListMarketingCampaignLogsRequest::REQUEST_LEVELS),
            message: Arr::get($validated, ListMarketingCampaignLogsRequest::REQUEST_MESSAGE),
            relationId: Arr::get($validated, ListMarketingCampaignLogsRequest::REQUEST_RELATION_ID),
            relationType: MarketingLogRelationType::tryFrom(Arr::get($validated, ListMarketingCampaignLogsRequest::REQUEST_RELATION_TYPE)),
        );

        return ListMarketingLogResource::format($consumers);
    }

    /**
     * @param int $logId
     * @return GetMarketingLogResource
     */
    public function getMarketingLog(int $logId): GetMarketingLogResource
    {
        $log = $this->service->getLog(
            logId: $logId,
        );

        return new GetMarketingLogResource($log);
    }
}
