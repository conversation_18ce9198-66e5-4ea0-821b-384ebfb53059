<?php

namespace App\Http\Controllers\MarketingCampaign;

use App\Enums\Emails\DomainStatus;
use App\Enums\MarketingCampaigns\MarketingCampaignType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\MarketingCampaign\ListMarketingSendingDomainsRequest;
use App\Http\Resources\MarketingCampaign\ListMarketingDomainResource;
use App\Models\MarketingDomain;
use App\Services\MarketingCampaign\MarketingDomainService;
use Carbon\Carbon;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class MarketingDomainController extends APIController
{
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected MarketingDomainService $service,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function syncDomains(): JsonResponse
    {
        $this->service->sync(MarketingCampaignType::INTERNAL_EMAIL);

        return $this->formatResponse([
            'status' => true,
        ]);
    }

    /**
     * @param ListMarketingSendingDomainsRequest $request
     * @return array
     */
    public function index(ListMarketingSendingDomainsRequest $request): array
    {
        $validated = $request->validated();
        $maxSentCutoff = Arr::get($validated, ListMarketingSendingDomainsRequest::FIELD_MAX_SENT_CUTOFF);
        $maxSentCutoff = $maxSentCutoff ? Carbon::createFromTimestamp($maxSentCutoff) : null;

        $domains = $this->service->list(
            status: DomainStatus::tryFrom(Arr::get($validated, ListMarketingSendingDomainsRequest::FIELD_STATUS)),
            maxSentCutoff: $maxSentCutoff,
        )->with(MarketingDomain::RELATION_MARKETING_CONSUMERS);

        return ListMarketingDomainResource::format($domains);
    }

}
