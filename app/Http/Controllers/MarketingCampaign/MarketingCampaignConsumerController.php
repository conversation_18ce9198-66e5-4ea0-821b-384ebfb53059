<?php

namespace App\Http\Controllers\MarketingCampaign;

use App\Enums\MarketingCampaigns\MarketingCampaignConsumerStatus;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\MarketingCampaign\ListMarketingCampaignConsumersRequest;
use App\Http\Resources\MarketingCampaign\ListMarketingCampaignConsumersResource;
use App\Models\MarketingCampaignConsumer;
use App\Services\MarketingCampaign\MarketingCampaignConsumerService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Arr;

class MarketingCampaignConsumerController extends APIController
{

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected MarketingCampaignConsumerService $service,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param ListMarketingCampaignConsumersRequest $request
     * @return array
     */
    public function listMarketingConsumers(ListMarketingCampaignConsumersRequest $request): array
    {
        $validated = $request->validated();
        $consumers = $this->service->listMarketingCampaigns(
            marketingCampaignId: Arr::get($validated, ListMarketingCampaignConsumersRequest::REQUEST_MARKETING_CAMPAIGN_ID),
            name: Arr::get($validated, ListMarketingCampaignConsumersRequest::REQUEST_NAME),
            status: MarketingCampaignConsumerStatus::tryFrom(Arr::get($validated, ListMarketingCampaignConsumersRequest::REQUEST_STATUS)),
            revalidated: Arr::get($validated, ListMarketingCampaignConsumersRequest::REQUEST_REVALIDATED),
        )->with(
            MarketingCampaignConsumer::RELATION_CONSUMER,
            MarketingCampaignConsumer::RELATION_MARKETING_CAMPAIGN
        )->orderByDesc(MarketingCampaignConsumer::FIELD_ID);

        return ListMarketingCampaignConsumersResource::format($consumers);
    }


}
