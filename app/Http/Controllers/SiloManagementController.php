<?php

namespace App\Http\Controllers;

use App\Enums\Odin\LocationSiloPageLocationType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Odin\CreateSiloRequest;
use App\Http\Requests\Odin\UpdateLocationSiloPageRequest;
use App\Http\Requests\Odin\UpdateSiloRequest;
use App\Http\Resources\Odin\SiloResource;
use App\Models\Odin\LocationSiloPage;
use App\Models\Odin\Silo;
use App\Models\User;
use App\Repositories\Odin\LocationSiloPageRepository;
use App\Repositories\Odin\SiloRepository;
use App\Services\Odin\SiloService;
use App\Services\Odin\SiloShortcodeService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\UnauthorizedException;

class SiloManagementController extends APIController
{
    const PERMISSION_VIEW           = 'silo-management/view';
    const PERMISSION_UPDATE         = 'silo-management/update';
    const PERMISSION_CREATE         = 'silo-management/create';
    const PERMISSION_DELETE         = 'silo-management/delete';

    const RESPONSE_STATUS           = 'status';
    const RESPONSE_LOCATION_TYPES   = 'silo_location_types';
    const RESPONSE_SILO             = 'silo';
    const RESPONSE_SILOS            = 'silos';
    const RESPONSE_SHORTCODES       = 'shortcodes';

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected SiloService $siloService,
        protected SiloRepository $siloRepository,
        protected LocationSiloPageRepository $locationSiloPageRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @return JsonResponse
     */
    public function getSiloLocationTypes(): JsonResponse
    {
        $types = collect();
        foreach(LocationSiloPageLocationType::cases() as $case) {
            $types->push([
                'name' => strtolower($case->name),
                'id' => $case->value,
                'required' => $case === LocationSiloPageLocationType::NATIONAL
            ]);
        }

        return $this->formatResponse([
            self::RESPONSE_STATUS           => $types->count(),
            self::RESPONSE_LOCATION_TYPES   => $types,
        ]);
    }

    /**
     * @param CreateSiloRequest $request
     * @return JsonResponse
     */
    public function createNewSiloWithLocations(CreateSiloRequest $request): JsonResponse
    {
        $siloData = $request->collect();
        $newSilo = $this->siloService->createSiloAndLocations($siloData);

        return $this->formatResponse([
            self::RESPONSE_STATUS   => !!$newSilo,
            self::RESPONSE_SILO     => $newSilo,
        ]);
    }

    /**
     * @param SiloShortcodeService $shortcodeService
     * @return JsonResponse
     */
    public function getSiloShortcodes(SiloShortcodeService $shortcodeService): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_STATUS       => true,
            self::RESPONSE_SHORTCODES   => $shortcodeService->getShortcodes(),
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getAllSilos(): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();
        if (!$user->hasPermissionTo(self::PERMISSION_VIEW)) {
            throw new UnauthorizedException("User lacks the required permission to view Silos.");
        }

        $silos = $this->siloRepository->getAllSilos();

        return $this->formatResponse([
            self::RESPONSE_STATUS   => true,
            self::RESPONSE_SILOS    => SiloResource::collection($silos),
        ]);
    }

    /**
     * @param UpdateLocationSiloPageRequest $request
     * @return JsonResponse
     */
    public function updateLocationSiloPage(UpdateLocationSiloPageRequest $request, SiloShortcodeService $shortcodeService): JsonResponse
    {
        $validated = $request->safe()->toArray();
        $locationPageId = $request->get(LocationSiloPage::FIELD_ID)
            ?? LocationSiloPage::query()
                ->where(LocationSiloPage::FIELD_SILO_ID, $request->get(LocationSiloPage::FIELD_SILO_ID))
                ->where(LocationSiloPage::FIELD_LOCATION_ID, $request->get(LocationSiloPage::FIELD_LOCATION_ID))
                ->firstOrFail()
                ->id;

        $validated[LocationSiloPage::FIELD_RELATIVE_PATH] = $shortcodeService->handleLocationSiloShortcode($locationPageId, $validated[LocationSiloPage::FIELD_RELATIVE_PATH]);

        $updatedSilo = $this->locationSiloPageRepository->updateLocationPageSiloById($locationPageId, $validated);

        return $this->formatResponse([
            self::RESPONSE_STATUS   => !!$updatedSilo,
            self::RESPONSE_SILO     => new SiloResource($updatedSilo),
        ]);
    }

    /**
     * @param UpdateSiloRequest $request
     * @return JsonResponse
     */
    public function updateSilo(UpdateSiloRequest $request): JsonResponse
    {
        $validated = $request->safe()->toArray();
        $updatedSilo = $this->siloRepository->updateSiloById($validated[Silo::FIELD_ID], $validated);

        return $this->formatResponse([
            self::RESPONSE_STATUS   => !!$updatedSilo,
            self::RESPONSE_SILO     => new SiloResource($updatedSilo),
        ]);
    }
}
