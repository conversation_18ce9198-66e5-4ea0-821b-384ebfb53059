<?php

namespace App\Http\Controllers;

use Illuminate\Contracts\View\View;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RolesPermissionsManagementController extends Controller
{
    /**
     * @return View
     */
    public function index(): View
    {
        $roles = Role::all()->load('permissions');
        $permissions = Permission::all()->load('roles');

        return view('roles-permissions-management', compact('roles', 'permissions'));
    }
}
