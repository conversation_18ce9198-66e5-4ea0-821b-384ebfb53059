<?php

namespace App\Http\Controllers;

use App\Enums\ActivityLog\ActivityLogSubjectType;
use App\Http\Controllers\API\APIController;
use App\Http\Resources\CompanyRoleEventResource;
use App\Models\CompanyUserRelationship;
use App\Models\Odin\Company;
use App\Repositories\ActivityLog\ActivityLogRepository;
use Illuminate\Database\Query\Builder;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Spatie\Permission\Models\Role;

/**
 * @see resources/js/vue/components/Companies/components/Territory/components/CompanyRoleHistory.vue
 * @see resources/js/vue/components/Companies/components/Territory/services/api.js
 */
class CompanyRoleHistoryController extends APIController
{
    public function __invoke(Company $company, ActivityLogRepository $activityLogRepository): AnonymousResourceCollection
    {
        $role = Role::findByName(
            request()->validate([
                'role' => [Rule::exists(Role::class, 'name')],
            ])['role']
        );

        return CompanyRoleEventResource::collection(
            $activityLogRepository
                ->getActivityLogs(
                    ['company_role_assigned', 'company_role_unassigned'],
                    ActivityLogSubjectType::COMPANY,
                    $company->id
                )->where(function ($query) use ($role) {
                    $query->where('properties->role_id', $role->id)
                        ->orWhere('properties->type', Str::snake(str_replace('-', ' ', $role->name))); //todo: migrate old role activity logs and we can delete this line
                })
                ->whereExists(fn(Builder $query) => $query->select(DB::raw(1))
                    ->from(CompanyUserRelationship::TABLE)
                    ->where(CompanyUserRelationship::FIELD_COMPANY_ID, $company->id)
                    ->where(CompanyUserRelationship::FIELD_ROLE_ID, $role->id)
                )
                ->orderByDesc(ActivityLogRepository::ID)
                ->get()
        );
    }
}
