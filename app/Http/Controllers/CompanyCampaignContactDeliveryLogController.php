<?php

namespace App\Http\Controllers;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\CompanyCampaignDeliveryLog\ListContactDeliveryLogsRequest;
use App\Http\Resources\CompanyCampaignContactDeliveryLog\CompanyCampaignContactDeliveryLogResource;
use App\Services\CompanyCampaignContactDeliveryLogService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Arr;

class CompanyCampaignContactDeliveryLogController extends APIController
{
    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param CompanyCampaignContactDeliveryLogService $companyContactDeliveryLogService
     */
    public function __construct(
        Request                                            $request,
        JsonAPIResponseFactory                             $apiResponseFactory,
        protected CompanyCampaignContactDeliveryLogService $companyContactDeliveryLogService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param ListContactDeliveryLogsRequest $request
     * @return AnonymousResourceCollection
     */
    public function listContactDeliveryLogs(ListContactDeliveryLogsRequest $request): AnonymousResourceCollection
    {
        $validated = $request->validated();

        $contactDeliveryLogs = $this->companyContactDeliveryLogService->listContactDeliveryLogs(
            perPage             : Arr::get($validated, ListContactDeliveryLogsRequest::REQUEST_PER_PAGE, 10),
            page                : Arr::get($validated, ListContactDeliveryLogsRequest::REQUEST_PAGE, 1),
            companyId           : Arr::get($validated, ListContactDeliveryLogsRequest::REQUEST_COMPANY_ID),
            succeeded           : Arr::get($validated, ListContactDeliveryLogsRequest::REQUEST_DELIVERY_STATUS),
            campaign            : Arr::get($validated, ListContactDeliveryLogsRequest::REQUEST_CAMPAIGN),
            consumerProductId   : Arr::get($validated, ListContactDeliveryLogsRequest::REQUEST_CONSUMER_PRODUCT_ID),
            dateRange           : Arr::get($validated, key: ListContactDeliveryLogsRequest::REQUEST_DATE),
            invoiceId           : Arr::get($validated, ListContactDeliveryLogsRequest::REQUEST_INVOICE_ID)
        );

        return CompanyCampaignContactDeliveryLogResource::collection($contactDeliveryLogs);
    }

    public function exportDeliveryLogs(ListContactDeliveryLogsRequest $request): AnonymousResourceCollection
    {
        $validated = $request->validated();

        return CompanyCampaignContactDeliveryLogResource::collection($this->companyContactDeliveryLogService->listAllContactDeliveryLogs(
            companyId: Arr::get($validated, ListContactDeliveryLogsRequest::REQUEST_COMPANY_ID),
            succeeded: Arr::get($validated, ListContactDeliveryLogsRequest::REQUEST_DELIVERY_STATUS),
            campaign: Arr::get($validated, ListContactDeliveryLogsRequest::REQUEST_CAMPAIGN),
            consumerProductId: Arr::get($validated, ListContactDeliveryLogsRequest::REQUEST_CONSUMER_PRODUCT_ID),
            dateRange: Arr::get($validated, key: ListContactDeliveryLogsRequest::REQUEST_DATE),
            invoiceId: Arr::get($validated, ListContactDeliveryLogsRequest::REQUEST_INVOICE_ID)
        ));
    }
}

