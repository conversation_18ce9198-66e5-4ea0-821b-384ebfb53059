<?php

namespace App\Http\Controllers\Watchdog;

use App\Actions\GetWatchdogPlaybackUrl;
use App\Http\Controllers\Controller;
use App\Models\Odin\ProductAssignment;

class VideoUrlFromProductAssignmentController extends Controller
{
    public function __invoke(ProductAssignment $productAssignment, GetWatchdogPlaybackUrl $action)
    {
        return $action->handle($productAssignment->consumer->reference);
    }
}
