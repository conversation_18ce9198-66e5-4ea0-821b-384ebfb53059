<?php

namespace App\Http\Controllers\Watchdog;

use App\Actions\GetWatchdogPlaybackUrl;
use App\Http\Controllers\Controller;
use App\Models\Odin\ConsumerProduct;

class VideoUrlFromConsumerProductController extends Controller
{
    public function __invoke(ConsumerProduct $consumerProduct, GetWatchdogPlaybackUrl $action)
    {
        return $action->handle($consumerProduct->consumer->reference);
    }
}
