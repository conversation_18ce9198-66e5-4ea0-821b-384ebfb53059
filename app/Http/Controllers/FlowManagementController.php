<?php

namespace App\Http\Controllers;

use App\Enums\Flows\RevisionType;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\PublishRevisionRequest;
use App\Http\Requests\StoreFlowRequest;
use App\Http\Requests\StoreRevisionRequest;
use App\Models\Firestore\Flows\Flow;
use App\Models\Firestore\Flows\Revision;
use App\Models\Odin\IndustryService;
use App\Models\User;
use App\Repositories\Flows\FlowRepository;
use App\Repositories\Flows\RevisionRepository as RevisionRepositoryV1;
use App\Repositories\Flows\v2\RevisionRepository as RevisionRepository;
use App\Repositories\Flows\v2\RevisionService;
use App\Services\FlowRevisionService;
use App\Services\Odin\ConfigurableFieldsService;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\UnauthorizedException;
use Illuminate\View\View;

class FlowManagementController extends APIController
{
    const REQUEST_FLOW               = 'flow';
    const REQUEST_REVISION_DATA_TYPE = 'revision_data_type';
    const REQUEST_NEW_REVISION_NAME  = 'new_revision_name';
    const REQUEST_NEW_DESCRIPTION    = 'new_description';
    const REQUEST_COPY_TO_FLOW       = 'to_flow';
    const REQUEST_V2_REVISION_DATA   = 'flow';
    const REQUEST_V2_SLIDE_DATA      = 'slides';
    const REQUEST_WORKING_REVISION   = 'working';
    const REQUEST_IMPORT_V1          = 'import_v1';

    const RESPONSE_STATUS            = 'status';
    const RESPONSE_REVISION_META     = 'revision_meta';
    const RESPONSE_REVISION          = 'revision';
    const RESPONSE_MESSAGE           = 'message';
    const RESPONSE_MEDIA_LINK        = 'media_link';
    const RESPONSE_CONSUMER_FIELDS   = 'consumer_fields';

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected RevisionRepositoryV1 $revisionRepositoryV1,
        protected FlowRepository $flowRepository,
        protected RevisionRepository $revisionRepository,
        protected RevisionService $revisionService
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * Get metadata for all Revisions of all Flows
     * @return JsonResponse
     */
    public function getRevisionMetaData(): JsonResponse
    {
        $combinedMetadata = $this->revisionRepository->getRevisionMetadata(true);

        return $this->formatResponse([
            self::RESPONSE_STATUS        => true,
            self::RESPONSE_REVISION_META => $combinedMetadata,
        ]);
    }

    /**
     * Get a specific Flow payload
     * This can return an unpublished version and should only be used internally
     * @return JsonResponse
     */
    public function getRevisionData(): JsonResponse
    {
        $flowId = $this->request->route('flowId');
        $revisionId = $this->request->route('revisionUuid');
        $isLegacy = $this->revisionService->revisionIdIsLegacy($revisionId);
        $currentVersion = !($this->request->get(self::REQUEST_REVISION_DATA_TYPE) === Revision::DATA_WORKING);

        $revisionData = $isLegacy
            ? $this->revisionRepositoryV1->getRevisionPayload($flowId, $revisionId, $currentVersion)
            : $this->revisionRepository->getRevisionPayload($flowId, $revisionId, $currentVersion);

        return $this->formatResponse([
            self::RESPONSE_STATUS   => !!$revisionData,
            self::RESPONSE_REVISION => $revisionData,
        ]);
    }

    /**
     * Create a new Flow, e.g. "SolarCalculator", which will then contain the different revisions of the Calculator/Flow
     * @param StoreFlowRequest $request
     * @return JsonResponse
     */
    public function createNewFlow(StoreFlowRequest $request): JsonResponse
    {
        $validated = $request->safe()->toArray();

        if ($this->flowRepository->checkFlowNameExists($validated[Flow::NAME])) {
            return $this->formatResponse([
                self::RESPONSE_STATUS  => false,
                self::RESPONSE_MESSAGE => "The Flow name already exists. Please enter a unique name"
            ]);
        }

        return $this->formatResponse([
            self::RESPONSE_STATUS   => $this->flowRepository->create($validated),
        ]);
    }

    /**
     * Update a Flow - metadata only
     * @param StoreFlowRequest $request
     * @return JsonResponse
     */
    public function updateFlow(StoreFlowRequest $request): JsonResponse
    {
        $validated = $request->safe()->toArray();
        $flowId = $request->route('flowId');

        return $this->formatResponse([
            self::RESPONSE_STATUS   => $this->flowRepository->update($flowId, $validated),
        ]);
    }

    /**
     * @param StoreRevisionRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function createNewRevision(StoreRevisionRequest $request): JsonResponse
    {
        $validated = $request->safe()->toArray();
        $this->addActioningUserToData($validated);
        $flowId = $request->route('flowId');
        $validated[Revision::TYPE] = RevisionType::VARIANT->value;

        $metaData = $this->revisionRepositoryV1->saveNewVariant($flowId, $validated);

        return $this->formatResponse([
            self::RESPONSE_STATUS           => !!$metaData,
            self::RESPONSE_REVISION_META    => $metaData,
        ]);
    }

    /**
     * Updates an existing Revision - metadata only
     * @param StoreRevisionRequest $request
     * @return JsonResponse
     */
    public function updateRevisionMeta(StoreRevisionRequest $request): JsonResponse
    {
        $validated = $request->safe()->toArray();
        $flowId = $request->route('flowId');
        $revisionId = $request->route('revisionUuid');
        $this->addActioningUserToData($validated);

        return $this->formatResponse([
            self::RESPONSE_STATUS   => $this->revisionRepositoryV1->update($flowId, $revisionId, $validated),
        ]);
    }

    /**
     * @param PublishRevisionRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function publishRevision(PublishRevisionRequest $request): JsonResponse
    {
        $validated = $request->safe()->toArray();
        $flowId = $request->route('flowId');
        $revisionId = $request->route('revisionUuid');
        $this->addActioningUserToData($validated);

        $schema = $validated[Revision::SCHEMA] ?? 1;

        $publishedMeta = $schema >= 2
            ? $this->revisionRepository->publish($flowId, $revisionId, $validated)
            : $this->revisionRepositoryV1->publish($flowId, $revisionId, $validated[Revision::REVISION_DATA]);

        return $this->formatResponse([
            self::RESPONSE_STATUS        => !!$publishedMeta,
            self::RESPONSE_REVISION_META => $publishedMeta
        ]);
    }

    /**
     * @param PublishRevisionRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function saveRevision(PublishRevisionRequest $request): JsonResponse
    {
        $validated = $request->safe()->toArray();
        $flowId = $request->route('flowId');
        $revisionId = $request->route('revisionUuid');
        $this->addActioningUserToData($validated[Revision::REVISION_DATA]);

        $schema = $validated[Revision::SCHEMA] ?? 1;

        $saved = $schema >= 2
            ? $this->revisionRepository->save($flowId, $revisionId, $validated)
            : $this->revisionRepositoryV1->save($flowId, $revisionId, $validated[Revision::REVISION_DATA]);

        return $this->formatResponse([
            self::RESPONSE_STATUS   => !!$saved,
            self::RESPONSE_REVISION_META    => $saved
        ]);
    }

    /**
     * Only used by V2
     * @param PublishRevisionRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function createNewVariant(PublishRevisionRequest $request): JsonResponse
    {
        $validated = $request->safe()->toArray();
        $flowId = $request->route('flowId');
        $revisionId = $request->route('revisionUuid');
        $this->addActioningUserToData($validated);
        $newRevisionName = $validated[Revision::REVISION_DATA][Revision::NAME] ?? null;
        $importV1 = $validated[Revision::REVISION_DATA][Revision::CONVERT_V1] ?? false;

        if (!$this->revisionRepository->newVariantNameIsUnique($flowId, $newRevisionName)) {
            return $this->formatResponse([
                self::RESPONSE_STATUS     => false,
                self::RESPONSE_MESSAGE    => "The name '".$newRevisionName."' is already in use.",
            ]);
        }

        $savedRevision = $this->revisionRepository->saveAsVariant($flowId, $revisionId, $validated, $importV1);

        return $this->formatResponse([
            self::RESPONSE_STATUS           => !!$savedRevision,
            self::RESPONSE_REVISION_META    => $savedRevision,
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function checkFlowIdIsValid(): JsonResponse
     {
         $flowId = $this->request->route('flowId');

         return $this->formatResponse([
             self::RESPONSE_STATUS  => $this->flowRepository->checkFlowNameExists($flowId),
         ]);
     }

    /**
     * Delete a flow - destroys version history, admin only
     * @return JsonResponse
     */
    public function deleteRevision(): JsonResponse
    {
        $flowId = $this->request->route('flowId');
        $revisionId = $this->request->route('revisionUuid');

        return $this->formatResponse([
            self::RESPONSE_STATUS   => $this->revisionRepositoryV1->delete($flowId, $revisionId),
        ]);
    }

    /**
     * @return JsonResponse
     * @throws Exception
     */
    public function setProductionRevision(): JsonResponse
    {
        $flowId = $this->request->route('flowId');
        $revisionId = $this->request->route('revisionUuid');

        return $this->formatResponse([
            self::RESPONSE_STATUS   => $this->flowRepository->setProductionRevision($flowId, $revisionId),
        ]);
    }

    /**
     * @param FlowRevisionService $flowRevisionService
     * @return JsonResponse
     * @throws Exception
     */
    public function uploadImage(FlowRevisionService $flowRevisionService): JsonResponse
    {
        $file = $this->request->file('file');
        $flowId = $this->request->route('flowId');

        $fileUrl = $flowRevisionService->uploadImage($file, $flowId);

        return $this->formatResponse([
            self::RESPONSE_STATUS       => !!$fileUrl,
            self::RESPONSE_MEDIA_LINK   => $fileUrl
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getIndustryServices(): JsonResponse
    {
        $industryServices = IndustryService::all();

        return $this->formatResponse([
            'services' => $industryServices->map(fn(IndustryService $industryServices) => [
                'industry' => $industryServices->industry->name,
                'industry_slug' => $industryServices->industry->slug,
                'service' => $industryServices->name,
                'service_slug' => $industryServices->slug
            ])
        ]);
    }

    /**
     * @param string $flowId
     * @param string $revisionId
     * @return JsonResponse
     * @throws Exception
     */
    public function copyRevision(string $flowId, string $revisionId): JsonResponse
    {
        $toFlow = $this->request->get(self::REQUEST_COPY_TO_FLOW);
        $workingRevision = $this->request->get(self::REQUEST_WORKING_REVISION, false);

        $targetMeta = $this->request->validate([
            Revision::NAME              => ['string', 'min:4', 'max:64', 'required'],
            Revision::DESCRIPTION       => ['string', 'max:256', 'nullable'],
            Revision::PARENT_REVISION   => $revisionId,
            Revision::TYPE              => RevisionType::VARIANT->value,
        ]);

        if (!$this->revisionRepositoryV1->newVariantNameIsUnique($flowId, $revisionId)) {
            return $this->formatResponse([
                self::RESPONSE_STATUS  => false,
                self::RESPONSE_MESSAGE => "The name '".$targetMeta[Revision::NAME]."' is already in use"
            ]);
        }

        if (!$this->flowRepository->flowExists($toFlow)) {
            throw new ModelNotFoundException("The target Flow does not exist");
        }

        $success = $this->revisionRepository->copyRevision($flowId, $toFlow, $revisionId, $workingRevision, $targetMeta);

        return $this->formatResponse([
            self::RESPONSE_STATUS   => $success
        ]);
    }

    public function previewRevision(string $flowId, string $revisionId): View
    {
        $working = !!$this->request->get('working');

        return view('flow-client', compact('revisionId', 'flowId', 'working'));
    }

    /**
     * @param ConfigurableFieldsService $configurableFieldsService
     * @return JsonResponse
     * @throws Exception
     */
    public function getConsumerFields(ConfigurableFieldsService $configurableFieldsService): JsonResponse
    {
        /** @var IndustryService $service */
        $service = IndustryService::query()
            ->where(IndustryService::FIELD_SLUG, $this->request->get('service_slug', ''))
            ->firstOrFail();

        $industry = $service->industry;

        $serviceFields = $configurableFieldsService->getFields(
            ConfigurableFieldsService::TYPE_CONSUMER ,
            ConfigurableFieldsService::CATEGORY_SERVICE,
            $service->id,
        );

        $industryFields = $configurableFieldsService->getFields(
            ConfigurableFieldsService::TYPE_CONSUMER ,
            ConfigurableFieldsService::CATEGORY_INDUSTRY,
            $industry->id,
        );

        $fields = [
            'global'        => GlobalConfigurableFields::cases(),
            'industry'      => $industryFields->pluck('key'),
            'service'       => $serviceFields->pluck('key'),
        ];

        return $this->formatResponse([
            self::RESPONSE_STATUS           => true,
            self::RESPONSE_CONSUMER_FIELDS  => $fields,
        ]);
    }

    /**
     * Attach User ID to Revision requests
     * @param $validatedData
     * @return void
     */
    private function addActioningUserToData(&$validatedData): void
    {
        /** @var User $user */
        $user = Auth::user();
        $validatedData[Revision::ACTIONED_BY] = $user->id;
    }

}
