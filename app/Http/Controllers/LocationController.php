<?php

namespace App\Http\Controllers;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Services\LocationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class LocationController extends APIController
{
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected LocationService $locationService
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @return JsonResponse
     */
    public function getStatesWithCounties(): JsonResponse
    {
        return $this->formatResponse([
            'data' => $this->locationService->getStateWithCounties(),
        ]);
    }
}
