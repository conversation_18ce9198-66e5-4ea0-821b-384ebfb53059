<?php

namespace App\Http\Controllers\Billing;

use App\Enums\Billing\PaymentMethodServices;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Billing\BillingProfile\CreateBillingProfilesRequest;
use App\Http\Requests\Billing\BillingProfile\GetBillingProfilesRequest;
use App\Http\Requests\Billing\BillingProfile\UpdateBillingProfilesRequest;
use App\Http\Resources\Billing\BillingProfileResource;
use App\Models\Billing\BillingProfile;
use App\Models\Odin\Company;
use App\Services\Billing\BillingActionApprovalService;
use App\Services\Billing\BillingProfile\BillingProfileService;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Validation\ValidationException;

class BillingProfileController extends APIController
{
    const string FIELD_STATUS = 'status';

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param BillingProfileService $billingProfileService
     * @param BillingActionApprovalService $billingActionApprovalService
     */
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected BillingProfileService $billingProfileService,
        protected BillingActionApprovalService $billingActionApprovalService
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param CreateBillingProfilesRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function createBillingProfile(CreateBillingProfilesRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $frequencyType = Arr::get($validated, CreateBillingProfilesRequest::FIELD_FREQUENCY_TYPE);
        $companyId = Arr::get($validated, CreateBillingProfilesRequest::FIELD_COMPANY_ID);
        $default = Arr::get($validated, CreateBillingProfilesRequest::FIELD_DEFAULT);
        $processAuto = Arr::get($validated, CreateBillingProfilesRequest::FIELD_PROCESS_AUTO);
        $thresholdInDollars = Arr::get($validated, CreateBillingProfilesRequest::FIELD_THRESHOLD);
        $maxAllowedChargeAttempts = Arr::get($validated, CreateBillingProfilesRequest::FIELD_CHARGE_ATTEMPTS);
        $frequencyData = Arr::get($validated, CreateBillingProfilesRequest::FIELD_FREQUENCY_DATA);
        $paymentMethodId = Arr::get($validated, CreateBillingProfilesRequest::FIELD_PAYMENT_METHOD_ID);
        $paymentMethod = Arr::get($validated, CreateBillingProfilesRequest::FIELD_PAYMENT_METHOD);
        $dueInDays = Arr::get($validated, CreateBillingProfilesRequest::FIELD_DUE_IN_DAYS);
        $invoiceTemplateId = Arr::get($validated, CreateBillingProfilesRequest::FIELD_INVOICE_TEMPLATE_ID);
        $name = Arr::get($validated, CreateBillingProfilesRequest::FIELD_NAME);

        $campaignIds = Arr::get($validated, CreateBillingProfilesRequest::FIELD_CAMPAIGNS);

        $this->billingProfileService->createBillingProfileViaApprovals(
            companyId         : $companyId,
            paymentMethod     : PaymentMethodServices::tryFrom($paymentMethod),
            frequencyType     : $frequencyType,
            frequencyData     : $frequencyData,
            default           : $default,
            processAuto       : $processAuto,
            thresholdInDollars: $thresholdInDollars,
            maxChargeAttempts : $maxAllowedChargeAttempts,
            user              : auth()->user(),
            campaignIds       : $campaignIds,
            paymentMethodId   : $paymentMethodId,
            dueInDays         : $dueInDays,
            invoiceTemplateId : $invoiceTemplateId,
            name              : $name
        );

        return $this->formatResponse([
            self::FIELD_STATUS => true
        ]);
    }

    /**
     * @param GetBillingProfilesRequest $request
     * @return array
     */
    public function getBillingProfiles(GetBillingProfilesRequest $request): array
    {
        $validated = $request->validated();

        $billingProfiles = $this->billingProfileService->getBillingProfiles(
            companyId       : Arr::get($validated, GetBillingProfilesRequest::COMPANY_ID),
            showArchived    : Arr::get($validated, GetBillingProfilesRequest::SHOW_ARCHIVED),
            filterByUserRole: auth()->user(),
            ids             : Arr::get($validated, 'ids', []),
        )->with(BillingProfile::RELATION_CAMPAIGNS);


        return BillingProfileResource::format($billingProfiles);
    }


    /**
     * @param GetBillingProfilesRequest $request
     * @param BillingProfile $billingProfile
     * @return BillingProfileResource
     */
    public function getBillingProfile(GetBillingProfilesRequest $request, BillingProfile $billingProfile): BillingProfileResource
    {
        $request->validated();

        return new BillingProfileResource($billingProfile);
    }

    /**
     * @param BillingProfile $billingProfile
     * @param UpdateBillingProfilesRequest $request
     * @return JsonResponse
     * @throws BindingResolutionException
     * @throws ValidationException
     */
    public function updateBillingProfile(BillingProfile $billingProfile, UpdateBillingProfilesRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $frequencyType = Arr::get($validated, UpdateBillingProfilesRequest::FIELD_FREQUENCY_TYPE);
        $default = Arr::get($validated, UpdateBillingProfilesRequest::FIELD_DEFAULT);
        $campaignIds = Arr::get($validated, UpdateBillingProfilesRequest::FIELD_CAMPAIGNS);
        $processAuto = Arr::get($validated, CreateBillingProfilesRequest::FIELD_PROCESS_AUTO);
        $threshold = Arr::get($validated, CreateBillingProfilesRequest::FIELD_THRESHOLD);
        $chargeAttempts = Arr::get($validated, CreateBillingProfilesRequest::FIELD_CHARGE_ATTEMPTS);
        $frequencyData = Arr::get($validated, CreateBillingProfilesRequest::FIELD_FREQUENCY_DATA);
        $paymentMethodId = Arr::get($validated, CreateBillingProfilesRequest::FIELD_PAYMENT_METHOD_ID);
        $paymentMethod = Arr::get($validated, CreateBillingProfilesRequest::FIELD_PAYMENT_METHOD);
        $dueInDays = Arr::get($validated, UpdateBillingProfilesRequest::FIELD_DUE_IN_DAYS);
        $invoiceTemplateId = Arr::get($validated, UpdateBillingProfilesRequest::FIELD_INVOICE_TEMPLATE_ID);
        $name = Arr::get($validated, UpdateBillingProfilesRequest::FIELD_NAME);

        $this->billingProfileService->updateBillingProfileViaApprovals(
            billingProfile    : $billingProfile,
            frequencyType     : $frequencyType,
            frequencyData     : $frequencyData,
            default           : $default,
            processAuto       : $processAuto,
            thresholdInDollars: $threshold,
            maxChargeAttempts : $chargeAttempts,
            user              : auth()->user(),
            paymentMethod     : $paymentMethod,
            campaignIds       : $campaignIds,
            paymentMethodId   : $paymentMethodId,
            dueInDays         : $dueInDays,
            invoiceTemplateId : $invoiceTemplateId,
            name              : $name,
        );

        return $this->formatResponse([
            self::FIELD_STATUS => true
        ]);
    }

    /**
     * @param BillingProfile $billingProfile
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function archive(BillingProfile $billingProfile): JsonResponse
    {
        $this->billingProfileService->archive(
            profile: $billingProfile,
            user   : auth()->user()
        );

        return $this->formatResponse([
            self::FIELD_STATUS => true
        ]);
    }

    /**
     * @param BillingProfile $billingProfile
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function restore(BillingProfile $billingProfile): JsonResponse
    {
        $this->billingProfileService->restore(
            profile: $billingProfile,
            user   : auth()->user()
        );

        return $this->formatResponse([
            self::FIELD_STATUS => true
        ]);
    }

    /**
     * @param Company $company
     * @return JsonResponse
     */
    public function getCompanyBillingProfiles(Company $company): JsonResponse
    {
        $billingProfiles = $this->billingProfileService->getCompanyPaymentMethods(
            companyId: $company->id
        );

        $paymentMethods = $billingProfiles
            ->pluck(BillingProfile::FIELD_PAYMENT_METHOD)
            ->unique();

        return $this->formatResponse([
            'payment_methods'  => $paymentMethods,
            'billing_profiles' => BillingProfileResource::collection($billingProfiles),
        ]);
    }
}
