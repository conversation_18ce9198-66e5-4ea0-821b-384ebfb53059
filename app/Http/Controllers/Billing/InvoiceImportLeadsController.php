<?php

namespace App\Http\Controllers\Billing;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Billing\LeadsImportRequest;
use App\Services\Billing\InvoiceLeadsImportService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Validation\ValidationException;
use League\Csv\Exception;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Throwable;

class InvoiceImportLeadsController extends APIController
{
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected InvoiceLeadsImportService $invoiceLeadsImportService
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param LeadsImportRequest $leadsImportRequest
     * @return JsonResponse
     * @throws ValidationException
     * @throws Exception
     * @throws Throwable
     */
    public function getInvoiceLeadsImportData(LeadsImportRequest $leadsImportRequest): JsonResponse
    {
        $data = $leadsImportRequest->validated();

        $response = $this->invoiceLeadsImportService->getImportLeadsData(
            companyId: Arr::get($data, LeadsImportRequest::FIELD_COMPANY_ID),
            file     : Arr::get($data, LeadsImportRequest::FIELD_FILE),
        );

        return $this->formatResponse($response);
    }

    /**
     * @param LeadsImportRequest $leadsImportRequest
     * @return JsonResponse
     * @throws Exception
     * @throws Throwable
     * @throws ValidationException
     */
    public function importLeadsViaCsv(LeadsImportRequest $leadsImportRequest): BinaryFileResponse
    {
        $data = $leadsImportRequest->validated();

        $filepath = $this->invoiceLeadsImportService->importLeads(
            companyId: Arr::get($data, LeadsImportRequest::FIELD_COMPANY_ID),
            file     : Arr::get($data, LeadsImportRequest::FIELD_FILE),
            user     : auth()->user()
        );

        return response()->download($filepath)->deleteFileAfterSend();
    }
}
