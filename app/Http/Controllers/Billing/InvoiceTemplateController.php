<?php

namespace App\Http\Controllers\Billing;

use App\Enums\Billing\InvoiceTemplateModelType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Billing\InvoiceTemplate\SaveInvoiceTemplateRequest;
use App\Http\Requests\Billing\InvoiceTemplate\GetInvoiceTemplateModelTypesRequest;
use App\Http\Requests\Billing\InvoiceTemplate\ListInvoiceTemplatesRequest;
use App\Http\Requests\Billing\InvoiceTemplate\SearchModelTypeRequest;
use App\Http\Resources\Billing\InvoiceTemplate\InvoiceTemplateModelTypeResource;
use App\Http\Resources\Billing\InvoiceTemplate\InvoiceTemplateResource;
use App\Http\Resources\Odin\SelectionOptionResource;
use App\Models\Billing\InvoiceTemplate;
use App\Models\User;
use App\Services\Billing\InvoiceTemplate\InvoiceTemplateModelSearchService;
use App\Services\Billing\InvoiceTemplate\InvoiceTemplateService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Validation\ValidationException;

class InvoiceTemplateController extends APIController
{
    const string FIELD_STATUS            = 'status';
    const string FIELD_INVOICE_TEMPLATE  = 'invoice_template';
    const string FIELD_INVOICE_TEMPLATES = 'invoice_templates';
    const string FIELD_SEARCHABLE_MODELS = 'searchable_models';
    const string FIELD_MODEL_OPTIONS     = 'model_options';
    const string FIELD_HTML_PREVIEW      = 'html_preview';

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param InvoiceTemplateService $service
     * @param InvoiceTemplateModelSearchService $invoiceTemplateModelSearchService
     */
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected InvoiceTemplateService $service,
        protected InvoiceTemplateModelSearchService $invoiceTemplateModelSearchService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param GetInvoiceTemplateModelTypesRequest $request
     * @return JsonResponse
     */
    public function getSearchableModelTypes(GetInvoiceTemplateModelTypesRequest $request): JsonResponse
    {
        $request->validated();

        $modelTypes = InvoiceTemplateModelType::cases();

        return $this->formatResponse([
            self::FIELD_STATUS            => true,
            self::FIELD_SEARCHABLE_MODELS => InvoiceTemplateModelTypeResource::collection($modelTypes)
        ]);
    }

    /**
     * @param SearchModelTypeRequest $request
     * @return JsonResponse
     */
    public function searchModels(SearchModelTypeRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $options = $this->invoiceTemplateModelSearchService->searchModels(
            modelType: InvoiceTemplateModelType::tryFrom(Arr::get($validated, SearchModelTypeRequest::FIELD_TYPE)),
            query    : Arr::get($validated, SearchModelTypeRequest::FIELD_QUERY)
        );

        return $this->formatResponse([
            self::FIELD_STATUS        => true,
            self::FIELD_MODEL_OPTIONS => SelectionOptionResource::collection($options)
        ]);
    }


    /**
     * @param ListInvoiceTemplatesRequest $request
     * @return JsonResponse
     */
    public function listInvoiceTemplates(ListInvoiceTemplatesRequest $request): JsonResponse
    {
        $request->validated();

        $invoiceTemplatesQuery = $this->service->getListInvoiceTemplatesQuery();

        return $this->formatResponse([
            self::FIELD_STATUS            => true,
            self::FIELD_INVOICE_TEMPLATES => InvoiceTemplateResource::paginate($invoiceTemplatesQuery)
        ]);
    }

    /**
     * @param Request $request
     * @return array
     */
    public function getInvoiceTemplateOptions(Request $request): array
    {
        $invoiceTemplatesQuery = $this->service->getListInvoiceTemplatesQuery();

        return InvoiceTemplateResource::format(
            builder: $invoiceTemplatesQuery,
            all: true
        );
    }

    /**
     * @param SaveInvoiceTemplateRequest $request
     * @return JsonResponse
     * @throws ValidationException|Exception
     */
    public function createInvoiceTemplate(SaveInvoiceTemplateRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $invoiceTemplate = $this->service->createInvoiceTemplate(
            name       : Arr::get($validated, SaveInvoiceTemplateRequest::FIELD_NAME),
            isGlobal   : Arr::get($validated, SaveInvoiceTemplateRequest::FIELD_IS_GLOBAL, false),
            props      : Arr::get($validated, SaveInvoiceTemplateRequest::FIELD_PROPS, []),
            createdById: auth()->user()->{User::FIELD_ID},
            modelType  : InvoiceTemplateModelType::tryFrom(Arr::get($validated, SaveInvoiceTemplateRequest::FIELD_MODEL_TYPE)),
            modelId    : Arr::get($validated, SaveInvoiceTemplateRequest::FIELD_MODEL_ID)
        );

        return $this->formatResponse([
            self::FIELD_STATUS           => true,
            self::FIELD_INVOICE_TEMPLATE => new InvoiceTemplateResource($invoiceTemplate)
        ]);
    }


    /**
     * @param InvoiceTemplate $template
     * @param SaveInvoiceTemplateRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function updateInvoiceTemplate(InvoiceTemplate $template, SaveInvoiceTemplateRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $invoiceTemplate = $this->service->updateInvoiceTemplate(
            id       : $template->{InvoiceTemplate::FIELD_ID},
            name     : Arr::get($validated, SaveInvoiceTemplateRequest::FIELD_NAME),
            isGlobal : Arr::get($validated, SaveInvoiceTemplateRequest::FIELD_IS_GLOBAL, false),
            props    : Arr::get($validated, SaveInvoiceTemplateRequest::FIELD_PROPS, []),
            modelType: InvoiceTemplateModelType::tryFrom(Arr::get($validated, SaveInvoiceTemplateRequest::FIELD_MODEL_TYPE)),
            modelId  : Arr::get($validated, SaveInvoiceTemplateRequest::FIELD_MODEL_ID),
        );

        return $this->formatResponse([
            self::FIELD_STATUS           => true,
            self::FIELD_INVOICE_TEMPLATE => new InvoiceTemplateResource($invoiceTemplate)
        ]);
    }

    /**
     * @param InvoiceTemplate $template
     * @return JsonResponse
     * @throws Exception
     */
    public function deleteInvoiceTemplate(InvoiceTemplate $template): JsonResponse
    {
        return $this->formatResponse([
            self::FIELD_STATUS => $this->service->deleteInvoiceTemplate($template)
        ]);
    }

    /**
     * @param InvoiceTemplate $template
     * @return JsonResponse
     * @throws ValidationException
     */
    public function previewTemplate(InvoiceTemplate $template): JsonResponse
    {
        return $this->formatResponse([
            self::FIELD_STATUS       => true,
            self::FIELD_HTML_PREVIEW => $this->service->previewPdfTemplateHtml($template)
        ]);
    }
}
