<?php

namespace App\Http\Controllers\Billing;

use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Billing\ListInvoiceCollectionsRequest;
use App\Http\Requests\Billing\GetInvoiceWriteOffsRequest;
use App\Http\Requests\Billing\InvoiceCollections\IssueInvoiceToCollectionRequest;
use App\Http\Requests\Billing\InvoiceCollections\UpdateInvoiceCollectionsDataRequest;
use App\Http\Resources\Billing\InvoiceCollectionsResource;
use App\Models\User;
use App\Services\Billing\InvoiceCollections\InvoiceCollectionsService;
use App\Services\Billing\InvoiceService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;

class InvoiceCollectionsController extends APIController
{

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected InvoiceCollectionsService $invoiceCollectionService,
        protected InvoiceService $invoiceService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param IssueInvoiceToCollectionRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function issueInvoiceToCollections(IssueInvoiceToCollectionRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $user = Auth::user();

        $invoice = $this->invoiceService->getInvoice(Arr::get($validated, IssueInvoiceToCollectionRequest::FIELD_INVOICE_ID));

        $this->invoiceCollectionService->issueInvoiceToCollections(
            invoice   : $invoice,
            authorType: InvoiceEventAuthorTypes::USER,
            authorId  : $user->{User::FIELD_ID}
        );

        return $this->formatResponse([
            'status' => true,
        ]);
    }

    /**
     * @param UpdateInvoiceCollectionsDataRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function updateInvoiceCollections(UpdateInvoiceCollectionsDataRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $user = Auth::user();

        $invoice = $this->invoiceService->getInvoice(Arr::get($validated, UpdateInvoiceCollectionsDataRequest::FIELD_INVOICE_ID));

        $this->invoiceCollectionService->updateInvoiceCollections(
            invoice        : $invoice,
            authorType     : InvoiceEventAuthorTypes::USER->value,
            amountRecovered: Arr::get($validated, UpdateInvoiceCollectionsDataRequest::FIELD_AMOUNT_RECOVERED),
            recoveryStatus : Arr::get($validated, UpdateInvoiceCollectionsDataRequest::FIELD_RECOVERY_STATUS),
            authorId       : $user->{User::FIELD_ID},
        );


        return $this->formatResponse([
            'status' => true,
        ]);
    }


    /**
     * @param ListInvoiceCollectionsRequest $request
     * @return array
     */
    public function listInvoiceToCollections(ListInvoiceCollectionsRequest $request): array
    {
        $filters = $request->validated();

        $dateRange = json_decode(Arr::get($filters, ListInvoiceCollectionsRequest::FIELD_DATE_RANGE, '')) ?? [];

        $query = $this->invoiceCollectionService->list(
            invoiceId: Arr::get($filters, ListInvoiceCollectionsRequest::FIELD_INVOICE_ID),
            companyId: Arr::get($filters, ListInvoiceCollectionsRequest::FIELD_COMPANY_ID),
            dateFrom : $dateRange?->from,
            dateTo   : $dateRange?->to,
            status   : Arr::get($filters, ListInvoiceCollectionsRequest::FIELD_STATUS, []),
        );

        return InvoiceCollectionsResource::format($query);
    }
}
