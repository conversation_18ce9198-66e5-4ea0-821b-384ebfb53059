<?php

namespace App\Http\Controllers\Billing;

use App\Builder\Billing\CompanyRevenueBuilder;
use App\Http\Requests\Billing\GetCompanyRevenueGraphDataRequest;
use App\Http\Resources\Billing\CompanyRevenueGraphResource;
use App\Http\Resources\Billing\CompanyRevenueGraphSummaryResource;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use Illuminate\Support\Arr;

class CompanyRevenueController
{
    /**
     * @param int $company
     * @param GetCompanyRevenueGraphDataRequest $request
     * @return CompanyRevenueGraphResource
     */
    function getRevenueGraphData(int $company, GetCompanyRevenueGraphDataRequest $request): CompanyRevenueGraphResource
    {
        $validated = $request->validated();

        $industryId = Arr::get($validated, GetCompanyRevenueGraphDataRequest::FIELD_INDUSTRY_ID);
        $period = Arr::get($validated, GetCompanyRevenueGraphDataRequest::FIELD_PERIOD);
        $duration = Arr::get($validated, GetCompanyRevenueGraphDataRequest::FIELD_DURATION);

        $graphData = CompanyRevenueBuilder::query()
            ->setPeriodDuration($duration)
            ->setPeriod($period)
            ->setCompanyId($company)
            ->setIndustryId($industryId)
            ->toGraph();

        $industryOptions = CompanyCampaign::withTrashed()
            ->select([
                Industry::TABLE . '.' . Industry::FIELD_ID . ' as id',
                Industry::TABLE . '.' . Industry::FIELD_NAME . ' as name',
            ])
            ->join(
                IndustryService::TABLE,
                IndustryService::TABLE . '.' . IndustryService::FIELD_ID,
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_SERVICE_ID
            )
            ->join(
                Industry::TABLE,
                Industry::TABLE . '.' . Industry::FIELD_ID,
                IndustryService::TABLE . '.' . IndustryService::FIELD_INDUSTRY_ID
            )
            ->groupBy(Industry::TABLE . '.' . Industry::FIELD_ID)
            ->where(CompanyCampaign::FIELD_COMPANY_ID, $company)
            ->get();

        return new CompanyRevenueGraphResource([
            'data'             => $graphData,
            'industry_options' => $industryOptions
        ]);
    }

    /**
     * @param int $company
     * @param GetCompanyRevenueGraphDataRequest $request
     * @return array
     */
    function getRevenueSummary(int $company, GetCompanyRevenueGraphDataRequest $request): CompanyRevenueGraphSummaryResource
    {
        $validated = $request->validated();

        $companyRevenueService = new CompanyRevenueBuilder($company);

        return new CompanyRevenueGraphSummaryResource($companyRevenueService->getSummary());
    }
}
