<?php

namespace App\Http\Controllers\Billing;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Billing\ListChargebacksRequest;
use App\Http\Requests\Billing\GetInvoiceChargebacksRequest;
use App\Http\Resources\Billing\InvoiceChargeback\InvoiceChargebackResource;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceDispute;
use App\Services\Billing\InvoiceDisputeService;
use App\Services\Filterables\SalesOverview\DemoDateFilterable;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class InvoiceChargebackController extends APIController
{
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected InvoiceDisputeService $invoiceDisputeService
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param ListChargebacksRequest $request
     * @return array
     */
    public function getChargebacks(ListChargebacksRequest $request): array
    {
        $filters = $request->validated();

        $dateRange = json_decode(Arr::get($filters, ListChargebacksRequest::FIELD_DATE_RANGE, '')) ?? [];

        $query = $this->invoiceDisputeService->list(
            invoiceId: Arr::get($filters, ListChargebacksRequest::FIELD_INVOICE_ID),
            status   : Arr::get($filters, ListChargebacksRequest::FIELD_STATUS),
            companyId: Arr::get($filters, ListChargebacksRequest::FIELD_COMPANY_ID),
            dateFrom : $dateRange?->from,
            dateTo   : $dateRange?->to,
        );

        return InvoiceChargebackResource::paginate($query);
    }

    /**
     * @param int $invoiceId
     * @param GetInvoiceChargebacksRequest $request
     * @return JsonResponse
     */
    public function getInvoiceChargebacks(int $invoiceId, GetInvoiceChargebacksRequest $request): JsonResponse
    {
        $request->validated();

        $chargebacks = $this->invoiceDisputeService->list(
            invoiceId: $invoiceId
        )->get();

        $totalChargeback = $chargebacks->sum(InvoiceDispute::FIELD_AMOUNT);

        return $this->formatResponse([
            'chargebacks' => InvoiceChargebackResource::collection($chargebacks),
            'aggregate'   => [
                'total_chargeback' => $totalChargeback
            ],
        ]);
    }

}
