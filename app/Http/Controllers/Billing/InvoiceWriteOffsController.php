<?php

namespace App\Http\Controllers\Billing;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Billing\ListChargebacksRequest;
use App\Http\Requests\Billing\GetInvoiceWriteOffsRequest;
use App\Http\Resources\Billing\InvoiceWriteOffsResource;
use App\Services\Billing\InvoiceWriteOffService;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class InvoiceWriteOffsController extends APIController
{

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected InvoiceWriteOffService $invoiceWriteOffService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @return array
     */
    public function listWriteOffs(GetInvoiceWriteOffsRequest $request): array
    {
        $filters = $request->validated();

        $dateRange = json_decode(Arr::get($filters, GetInvoiceWriteOffsRequest::FIELD_DATE_RANGE, '')) ?? [];

        $query = $this->invoiceWriteOffService->listWriteOffs(
            invoiceId: Arr::get($filters, GetInvoiceWriteOffsRequest::FIELD_INVOICE_ID),
            companyId: Arr::get($filters, GetInvoiceWriteOffsRequest::FIELD_COMPANY_ID),
            dateFrom : $dateRange?->from,
            dateTo   : $dateRange?->to,
        );

        return InvoiceWriteOffsResource::format($query);
    }
}
