<?php

namespace App\Http\Controllers\Billing;

use App\Enums\ApprovableAction\ApprovableActionRelationTypes;
use App\Enums\Billing\ApprovableActionType;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Factories\JsonAPIResponseFactory;
use App\Helpers\CarbonHelper;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Billing\MakeInvoicePaymentRequestRequest;
use App\Http\Resources\Billing\InvoicePaymentResource;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoicePayment;
use App\Models\Billing\InvoiceSnapshot;
use App\Models\User;
use App\Services\Billing\BillingActionApprovalService;
use App\Services\Billing\InvoicePaymentService;
use App\Services\Billing\InvoiceTransactions\InvoiceTransactionService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class InvoicePaymentsController extends APIController
{
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected InvoiceTransactionService $service,
        protected InvoicePaymentService $invoicePaymentService,
        protected BillingActionApprovalService $billingActionApprovalService
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param int $invoiceId
     * @return JsonResponse
     */
    public function getInvoicePayments(int $invoiceId): JsonResponse
    {
        $payments = $this->invoicePaymentService->getInvoicePayments($invoiceId);

        $invoice = Invoice::query()->findOrFail($invoiceId);

        $totalPaid = $invoice->lastSnapshot()?->{InvoiceSnapshot::FIELD_TOTAL_PAID} ?? 0;

        return $this->formatResponse([
            'payments' => InvoicePaymentResource::collection($payments),
            'total'    => $totalPaid,
        ]);
    }

    /**
     * @throws Exception
     */
    public function cancelInvoicePayment(InvoicePayment $invoicePayment): JsonResponse
    {
        $this->invoicePaymentService->cancelInvoicePayment(
            invoicePayment: $invoicePayment,
            authorType    : InvoiceEventAuthorTypes::USER,
            authorId      : auth()->user()->id
        );

        return $this->formatResponse([
            'status' => true
        ]);
    }


    /**
     * @throws Exception
     */
    public function makeInvoicePaymentRequest(Invoice $invoice, MakeInvoicePaymentRequestRequest $request): JsonResponse
    {
        $data = $request->validated();

        $user = auth()->user();

        $billingProfileId = Arr::get($data, MakeInvoicePaymentRequestRequest::FIELD_BILLING_PROFILE_ID);

        $date = Arr::get($data, MakeInvoicePaymentRequestRequest::FIELD_DATE);

        $date = $date ? CarbonHelper::parseWithTimezone($date)->utc()->toString() : now()->utc()->toString();

        $billingProfile = BillingProfile::query()
            ->findOrFail($billingProfileId);

        $this->billingActionApprovalService->requestActionExecution(
            referenceId  : $invoice->{Invoice::FIELD_UUID},
            modelId      : $invoice->{Invoice::FIELD_ID},
            type         : ApprovableActionRelationTypes::INVOICES,
            action       : ApprovableActionType::MAKE_INVOICE_PAYMENT,
            requesterType: InvoiceEventAuthorTypes::USER,
            requesterId  : $user->id,
            arguments    : [
                "invoiceId"        => $invoice->id,
                "amount"           => Arr::get($data, MakeInvoicePaymentRequestRequest::FIELD_AMOUNT),
                "authorType"       => InvoiceEventAuthorTypes::USER->value,
                "authorId"         => $user->{User::FIELD_ID},
                "billingProfileId" => $billingProfileId,
                "paymentMethod"    => $billingProfile->{BillingProfile::FIELD_PAYMENT_METHOD}->value,
                "outstanding"      => $invoice->getTotalOutstanding(),
                "date"             => $date,
            ],
        );

        return $this->formatResponse([
            'status' => true
        ]);
    }
}
