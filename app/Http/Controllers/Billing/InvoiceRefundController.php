<?php

namespace App\Http\Controllers\Billing;

use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Billing\IssueRefundRequest;
use App\Http\Requests\Billing\ListInvoiceRefundsRequest;
use App\Http\Resources\Billing\InvoiceItemDTOResource;
use App\Http\Resources\Billing\InvoiceRefundDataDTOResource;
use App\Http\Resources\Billing\InvoiceRefunds\InvoiceRefundResource;
use App\Models\Billing\InvoiceSnapshot;
use App\Models\User;
use App\Repositories\Billing\InvoiceRefund\InvoiceRefundRepository;
use App\Services\Billing\InvoiceRefunds\InvoiceRefundService;
use App\Services\Billing\InvoiceService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;

class InvoiceRefundController extends APIController
{
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected InvoiceRefundService $refundService,
        protected InvoiceRefundRepository $refundRepository,
        protected InvoiceService $invoiceService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param ListInvoiceRefundsRequest $request
     * @return AnonymousResourceCollection
     */
    public function listInvoiceRefunds(ListInvoiceRefundsRequest $request): AnonymousResourceCollection
    {
        $validated = $request->validated();

        $refunds = $this->refundService->listInvoiceRefunds(
            page        : Arr::get($validated, ListInvoiceRefundsRequest::FIELD_PAGE, 1),
            perPage     : Arr::get($validated, ListInvoiceRefundsRequest::FIELD_PER_PAGE, 10),
            refundStatus: Arr::get($validated, ListInvoiceRefundsRequest::FIELD_REFUND_STATUS),
            invoiceId   : Arr::get($validated, ListInvoiceRefundsRequest::FIELD_INVOICE_ID),
            minimumValue: Arr::get($validated, ListInvoiceRefundsRequest::FIELD_MINIMUM),
            maximumValue: Arr::get($validated, ListInvoiceRefundsRequest::FIELD_MAXIMUM),
        );

        return InvoiceRefundResource::collection($refunds);
    }

    /**
     * @param int $invoiceId
     * @return JsonResponse
     */
    public function getInvoiceRefunds(int $invoiceId): JsonResponse
    {
        $invoice = $this->invoiceService->getInvoice(invoiceId: $invoiceId);

        $refundableItems = $this->refundService->getRefundableItems(invoice: $invoice);

        $refundData = $this->refundService->getInvoiceRefundData(invoice: $invoice);

        $latestSnapshot = $invoice->latestSnapshot();

        return $this->formatResponse([
            'refunds'    => InvoiceRefundDataDTOResource::collection($refundData),
            'aggregates' => [
                'paid'     => $latestSnapshot?->{InvoiceSnapshot::FIELD_TOTAL_PAID},
                'refunded' => $latestSnapshot?->{InvoiceSnapshot::FIELD_TOTAL_REFUNDED},
            ],
            'refundable' => InvoiceItemDTOResource::collection($refundableItems),
        ]);
    }

    /**
     * @param int $invoiceId
     * @param IssueRefundRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function issueRefund(int $invoiceId, IssueRefundRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $invoice = $this->invoiceService->getInvoice($invoiceId);

        /** @var User $user */
        $user = Auth::user();

        $this->refundService->validateAndIssueRefundRequest(
            invoice     : $invoice,
            refundItems : Arr::get($validated, IssueRefundRequest::REFUND_ITEMS),
            refundReason: Arr::get($validated, IssueRefundRequest::REFUND_REASON),
            authorType  : InvoiceEventAuthorTypes::USER,
            authorId    : $user->id,
            customAmount: Arr::get($validated, IssueRefundRequest::CUSTOM_AMOUNT),
        );

        return $this->formatResponse([
            'status' => true,
        ]);
    }

}
