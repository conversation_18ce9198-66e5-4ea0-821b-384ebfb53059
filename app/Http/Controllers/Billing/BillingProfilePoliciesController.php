<?php

namespace App\Http\Controllers\Billing;

use App\Enums\Billing\BillingPolicyActionType;
use App\Enums\Billing\BillingPolicyEventType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Billing\BillingProfilePolicy\BatchUpdateBillingProfilePolicyRequest;
use App\Http\Requests\Billing\BillingProfilePolicy\ListBillingProfilePolicyRequest;
use App\Http\Requests\Billing\BillingProfilePolicy\SaveBillingProfilePolicyRequest;
use App\Http\Resources\Billing\BillingProfilePolicyEventResource;
use App\Http\Resources\Billing\BillingProfilePolicyResource;
use App\Models\Billing\BillingProfilePolicy;
use App\Services\Billing\BillingProfilePolicyService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class BillingProfilePoliciesController extends APIController
{
    const string FIELD_STATUS                   = 'status';
    const string FIELD_BILLING_PROFILE_POLICIES = 'billing_profile_policies';
    const string FIELD_EVENTS                   = 'events';

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected BillingProfilePolicyService $billingProfilePolicyService
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param ListBillingProfilePolicyRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function listGlobalPolicies(ListBillingProfilePolicyRequest $request): JsonResponse
    {
        $request->validated();

        $globalPolicies = $this->billingProfilePolicyService->listGlobalPolicies();

        return $this->formatResponse([
            self::FIELD_STATUS                   => true,
            self::FIELD_BILLING_PROFILE_POLICIES => BillingProfilePolicyResource::collection($globalPolicies),
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function listAvailableEvents(): JsonResponse
    {
        $events = BillingPolicyEventType::cases();

        return $this->formatResponse([
            self::FIELD_STATUS => true,
            self::FIELD_EVENTS => BillingProfilePolicyEventResource::collection($events),
        ]);
    }

    /**
     * @param BillingProfilePolicy $profilePolicy
     * @return JsonResponse
     */
    public function deleteProfilePolicy(BillingProfilePolicy $profilePolicy): JsonResponse
    {
        return $this->formatResponse([
            self::FIELD_STATUS => $this->billingProfilePolicyService->deleteProfilePolicy($profilePolicy),
        ]);
    }

    /**
     * @param SaveBillingProfilePolicyRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function createProfilePolicy(SaveBillingProfilePolicyRequest $request): JsonResponse
    {
        $validated = $request->validated();

        return $this->formatResponse([
            self::FIELD_STATUS => $this->billingProfilePolicyService->saveProfilePolicy(
                eventClass : BillingPolicyEventType::tryFrom(Arr::get($validated, SaveBillingProfilePolicyRequest::FIELD_EVENT)),
                actionClass: BillingPolicyActionType::tryFrom(Arr::get($validated, SaveBillingProfilePolicyRequest::FIELD_ACTION)),
            ),
        ]);
    }

    /**
     * @param BatchUpdateBillingProfilePolicyRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function batchUpdate(BatchUpdateBillingProfilePolicyRequest $request): JsonResponse
    {
        $validated = $request->validated();

        return $this->formatResponse([
            self::FIELD_STATUS => $this->billingProfilePolicyService->batchUpdateProfilePolicies(
                policies: Arr::get($validated, BatchUpdateBillingProfilePolicyRequest::FIELD_POLICIES),
            ),
        ]);
    }
}
