<?php

namespace App\Http\Controllers\Billing;

use App\Enums\Billing\ApprovalStatus;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Billing\InvoiceActionRequest\GetInvoiceActionRequestRequest;
use App\Http\Requests\Billing\InvoiceActionRequest\ReviewActionApprovalRequest;
use App\Http\Requests\Billing\InvoiceActionRequest\ListInvoiceActionRequestRequest;
use App\Http\Resources\Billing\ActionApprovalResource;
use App\Models\Billing\ActionApproval;
use App\Services\Billing\ActionApprovalService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class ActionApprovalController extends APIController
{
    const string STATUS  = 'status';
    const string MESSAGE = 'message';

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected ActionApprovalService $actionApprovalService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param ListInvoiceActionRequestRequest $request
     * @return array
     */
    public function getActionApprovalRequests(ListInvoiceActionRequestRequest $request): array
    {
        $validated = $request->validated();
        $query = $this->actionApprovalService->getListQuery(
            reviewedBy          : Arr::get($validated, ListInvoiceActionRequestRequest::FIELD_REVIEWED_BY_ID),
            requestedBy         : Arr::get($validated, ListInvoiceActionRequestRequest::FIELD_REQUESTED_BY_ID),
            status              : Arr::get($validated, ListInvoiceActionRequestRequest::FIELD_STATUS),
            relatedTypes        : Arr::get($validated, ListInvoiceActionRequestRequest::FIELD_RELATED_TYPES, []),
            relatedIds          : Arr::get($validated, ListInvoiceActionRequestRequest::FIELD_RELATED_IDS, []),
            orderBy             : Arr::get($validated, ListInvoiceActionRequestRequest::FIELD_ORDER_BY, []),
            companyId           : Arr::get($validated, ListInvoiceActionRequestRequest::FIELD_COMPANY_ID),
            requestedActionTypes: Arr::get($validated, ListInvoiceActionRequestRequest::FIELD_ACTION_TYPES, []),
        );

        return ActionApprovalResource::format($query);
    }

    /**
     * @param GetInvoiceActionRequestRequest $request
     * @param int $id
     * @return ActionApprovalResource
     */
    public function getActionApprovalRequest(GetInvoiceActionRequestRequest $request, int $id): ActionApprovalResource
    {
        $request->validated();

        $invoiceAction = $this->actionApprovalService->getInvoiceActionRequest($id);

        return new ActionApprovalResource($invoiceAction);
    }

    /**
     * @param ActionApproval $actionRequest
     * @param ReviewActionApprovalRequest $request
     * @return array
     * @throws Exception
     */
    public function reviewActionRequest(
        ActionApproval $actionRequest,
        ReviewActionApprovalRequest $request
    ): array
    {
        $validated = $request->validated();

        $this->actionApprovalService->reviewAction(
            invoiceActionRequest: $actionRequest,
            reviewedByUserId    : auth()->user()->id,
            status              : ApprovalStatus::tryFrom(Arr::get($validated, ReviewActionApprovalRequest::FIELD_STATUS)),
            reason              : Arr::get($validated, ReviewActionApprovalRequest::FIELD_REASON),
        );

        return [
            'status' => true
        ];
    }

    /**
     * @param ActionApproval $actionRequest
     * @return array
     * @throws Exception
     */
    public function cancel(ActionApproval $actionRequest): array
    {
        $this->actionApprovalService->cancel(
            actionApproval: $actionRequest,
            user              : auth()->user(),
        );

        return [
            'status' => true
        ];
    }
}
