<?php

namespace App\Http\Controllers\Billing;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Billing\GetCompanyBillingProfilesRequest;
use App\Http\Resources\Billing\CompanyPaymentMethodResource;
use App\Models\Odin\Company;
use App\Services\Billing\CompanyPaymentMethodService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Arr;

class CompanyPaymentMethodController extends APIController
{
    const string FIELD_STATUS = 'status';

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param CompanyPaymentMethodService $companyPaymentMethodService
     */
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected CompanyPaymentMethodService $companyPaymentMethodService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param Company $company
     * @param GetCompanyBillingProfilesRequest $request
     * @return AnonymousResourceCollection
     */
    public function getCompanyBillingProfiles(Company $company, GetCompanyBillingProfilesRequest $request): AnonymousResourceCollection
    {
        $validated = $request->validated();

        $paymentMethods = $this->companyPaymentMethodService->getCompanyBillingProfiles(
            companyId: $company->id,
            type     : Arr::get($validated, GetCompanyBillingProfilesRequest::FIELD_TYPE)
        );

        return CompanyPaymentMethodResource::collection($paymentMethods);
    }
}
