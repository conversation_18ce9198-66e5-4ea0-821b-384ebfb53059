<?php

namespace App\Http\Controllers\Billing;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Billing\InvoiceCredits\ApplyCreditToInvoiceRequest;
use App\Http\Resources\Billing\InvoiceCredits\InvoiceCreditResource;
use App\Models\Billing\Invoice;
use App\Models\User;
use App\Services\Billing\CreditService;
use App\Services\Billing\InvoiceCredits\InvoiceCreditsService;
use App\Services\Billing\InvoiceService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Arr;

class InvoiceCreditController extends APIController
{
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected InvoiceCreditsService $invoiceCreditsService,
        protected CreditService $creditService,
        protected InvoiceService $invoiceService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param Invoice $invoiceId
     * @return AnonymousResourceCollection
     */
    public function getInvoiceCredits(Invoice $invoiceId): AnonymousResourceCollection
    {
        $invoiceCredits = $this->invoiceCreditsService->getInvoiceCredits($invoiceId);

        return InvoiceCreditResource::collection($invoiceCredits);
    }

    /**
     * @throws Exception
     */
    public function applyCredit(Invoice $invoiceId, ApplyCreditToInvoiceRequest $request): void
    {
        $data = $request->validated();

        /** @var User $user */
        $user = auth()->user();

        $this->invoiceCreditsService->applyCreditToInvoice(
            invoice   : $invoiceId,
            creditType: Arr::get($data, ApplyCreditToInvoiceRequest::FIELD_TYPE),
            amount    : Arr::get($data, ApplyCreditToInvoiceRequest::FIELD_AMOUNT),
            author    : $user,
        );
    }
}
