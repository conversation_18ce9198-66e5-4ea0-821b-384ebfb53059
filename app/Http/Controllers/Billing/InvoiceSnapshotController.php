<?php

namespace App\Http\Controllers\Billing;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Billing\BillingManagement\GetBundlesSoldRequest;
use App\Http\Requests\Billing\BillingManagement\GetFailedPaymentsRequest;
use App\Http\Requests\Billing\BillingManagement\GetInvoiceStatusesRequest;
use App\Http\Requests\Billing\BillingManagement\GetPaidInvoiceItemByType;
use App\Http\Requests\Billing\BillingManagement\GetRevenueComparisonRequest;
use App\Http\Requests\Billing\BillingManagement\GetTotalInvoicesValueByStatus;
use App\Http\Resources\Billing\BillingManagement\GetFailedPaymentsResource;
use App\Http\Resources\Billing\BillingManagement\GetGraphableRevenueComparisonResource;
use App\Http\Resources\Billing\BillingManagement\GetInvoiceStatusCountResource;
use App\Http\Resources\Billing\BillingManagement\GetInvoiceTotalResource;
use App\Http\Resources\Billing\BillingManagement\GetSoldBundlesResource;
use App\Http\Resources\Billing\BillingManagement\PaidInvoiceItemsByTypeResource;
use App\Models\Billing\InvoiceSnapshot;
use App\Repositories\BundleManagement\BundleInvoiceRepository;
use App\Services\Billing\InvoiceItemService;
use App\Services\Billing\InvoiceService;
use App\Services\Billing\InvoiceSnapshotService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Carbon;

class InvoiceSnapshotController extends APIController
{
    const string STATUS = 'status';
    const string DATA   = 'data';

    public function __construct(
        Request                          $request,
        JsonAPIResponseFactory           $apiResponseFactory,
        protected InvoiceSnapshotService $invoiceSnapshotService,
        protected InvoiceItemService     $invoiceItemService,
        protected InvoiceService         $invoiceService,
        protected BundleInvoiceRepository $bundleInvoiceRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    public function getTotalInvoicesValueByStatus(GetTotalInvoicesValueByStatus $request): JsonResponse
    {
        $validated = $request->validated();

        $invoices = $this->invoiceSnapshotService->getInvoiceSnapshots($validated);

        return $this->formatResponse([
            self::STATUS => true,
            self::DATA   => new GetInvoiceTotalResource($invoices),
        ]);
    }

    /**
     * @param GetRevenueComparisonRequest $request
     * @return JsonResponse
     */
    public function getRevenueComparison(GetRevenueComparisonRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $revenueComparison = $this->invoiceSnapshotService->getRevenueComparison(
            startDate: Carbon::parse($validated[GetRevenueComparisonRequest::START_DATE]),
            endDate  : Carbon::parse($validated[GetRevenueComparisonRequest::END_DATE])
        );

        return $this->formatResponse([
            self::STATUS => true,
            self::DATA   => new GetGraphableRevenueComparisonResource($revenueComparison)
        ]);
    }

    public function getPaidInvoiceItemValueByType(GetPaidInvoiceItemByType $request): JsonResponse
    {
        $validated = $request->validated();

        $paidInvoiceIds = $this->invoiceSnapshotService->getInvoiceSnapshots($validated)->pluck('invoice_id')->toArray();
        $invoiceItems   = $this->invoiceItemService->getInvoiceItemsByInvoiceIds(invoiceIds: $paidInvoiceIds);
        $formatted      = $this->invoiceItemService->groupInvoiceItemsByType($invoiceItems);

        return $this->formatResponse([
            self::STATUS => true,
            self::DATA   => new PaidInvoiceItemsByTypeResource($formatted)
        ]);
    }

    /**
     * @param GetFailedPaymentsRequest $request
     * @return AnonymousResourceCollection
     */
    public function getFailedPayments(GetFailedPaymentsRequest $request): AnonymousResourceCollection
    {
        $validated = $request->validated();

        $failedInvoices = $this->invoiceSnapshotService->getInvoiceSnapshots($validated, true);

        return GetFailedPaymentsResource::collection($failedInvoices);
    }

    public function getInvoiceStatuses(GetInvoiceStatusesRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $invoiceStatusCount = $this->invoiceService->getInvoiceCountByType($validated);

        return $this->formatResponse([
            self::STATUS => true,
            self::DATA   => new GetInvoiceStatusCountResource($invoiceStatusCount),
        ]);
    }

    public function getBundlesSold(GetBundlesSoldRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $invoiceIds = $this->invoiceSnapshotService->getInvoiceSnapshots($validated)->pluck(InvoiceSnapshot::FIELD_INVOICE_ID)->toArray();

        $bundleInvoices = $this->bundleInvoiceRepository->getSoldActiveBundlesCount($invoiceIds);

        return $this->formatResponse([
            self::STATUS => true,
            self::DATA   => new GetSoldBundlesResource($bundleInvoices)
        ]);
    }

}
