<?php

namespace App\Http\Controllers\Billing;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Billing\InvoiceTransaction\ListInvoiceTransactionsRequest;
use App\Http\Resources\Billing\InvoiceTransaction\InvoiceTransactionResource;
use App\Models\Billing\Invoice;
use App\Services\Billing\InvoiceTransactions\InvoiceTransactionService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Arr;

class InvoiceTransactionController extends APIController
{
    const string RETURN_STATUS  = 'status';
    const string RETURN_FILTERS = 'filters';

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected InvoiceTransactionService $service,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param ListInvoiceTransactionsRequest $request
     * @return AnonymousResourceCollection
     */
    public function listInvoiceTransactions(ListInvoiceTransactionsRequest $request): AnonymousResourceCollection
    {
        $filters = $request->validated();

        $transactions = $this->service->listInvoiceTransactions(
            page             : Arr::get($filters, ListInvoiceTransactionsRequest::FIELD_PAGE, 1),
            perPage          : Arr::get($filters, ListInvoiceTransactionsRequest::FIELD_PER_PAGE, 10),
            types            : Arr::get($filters, ListInvoiceTransactionsRequest::FIELD_TRANSACTION_TYPES),
            scenarios        : Arr::get($filters, ListInvoiceTransactionsRequest::FIELD_TRANSACTION_SCENARIOS),
            externalReference: Arr::get($filters, ListInvoiceTransactionsRequest::FIELD_EXTERNAL_REFERENCE),
            invoiceId        : Arr::get($filters, ListInvoiceTransactionsRequest::FIELD_INVOICE_ID),
            minimumValue     : Arr::get($filters, ListInvoiceTransactionsRequest::FIELD_MINIMUM),
            maximumValue     : Arr::get($filters, ListInvoiceTransactionsRequest::FIELD_MAXIMUM),
        );

        return InvoiceTransactionResource::collection($transactions);
    }

    /**
     * @param int $invoiceId
     * @param ListInvoiceTransactionsRequest $request
     * @return AnonymousResourceCollection
     * @throws Exception
     */
    public function getInvoiceTransactions(int $invoiceId, ListInvoiceTransactionsRequest $request): AnonymousResourceCollection
    {
        $filters = $request->validated();

        $invoice = Invoice::query()->where(Invoice::FIELD_ID, $invoiceId)->firstOrFail();

        $transactions = $this->service->getInvoiceTransactions(
            invoiceUuid: $invoice->{Invoice::FIELD_UUID}
        );

        return InvoiceTransactionResource::collection($transactions);
    }

    /**
     * @return JsonResponse
     */
    public function getInvoiceTransactionFilters(): JsonResponse
    {
        return $this->formatResponse([
            self::RETURN_STATUS  => true,
            self::RETURN_FILTERS => $this->service->getInvoiceTransactionFilters()
        ]);
    }

}
