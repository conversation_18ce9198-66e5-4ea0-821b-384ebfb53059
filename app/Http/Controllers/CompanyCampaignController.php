<?php

namespace App\Http\Controllers;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\CompanyCampaigns\CompanyCampaignRequest;
use App\Http\Resources\Billing\AssociatedCompanyCampaignsResource;
use App\Models\Billing\BillingProfile;
use App\Models\Campaigns\CompanyCampaign;
use App\Repositories\Odin\Campaigns\CompanyCampaignRepository;
use App\Services\Billing\BillingProfile\BillingProfileService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class CompanyCampaignController extends APIController
{
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected CompanyCampaignRepository $companyCampaignRepository,
        protected BillingProfileService $billingProfileService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param CompanyCampaignRequest $request
     * @return JsonResponse
     */
    public function getCompanyCampaigns(CompanyCampaignRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $billingProfileId = Arr::get($validated, CompanyCampaignRequest::BILLING_PROFILE_ID);
        $companyId = Arr::get($validated, CompanyCampaignRequest::COMPANY_ID);

        /** @var ?BillingProfile $billingProfile */
        $billingProfile = $billingProfileId ? $this->billingProfileService->getBillingProfile(
            companyId: $companyId,
            id       : $billingProfileId
        ) : null;

        /** @var Collection<CompanyCampaign> $associatedBillingProfileCampaigns */
        $associatedBillingProfileCampaigns = $billingProfile?->campaigns ?? collect();

        /** @var Collection<CompanyCampaign> $campaigns */
        $campaigns = $this->companyCampaignRepository->getCompanyCampaignsQuery(
            companyId: $companyId,
        )->get();

        return $this->formatResponse([
            'status'    => !!$campaigns,
            'campaigns' => new AssociatedCompanyCampaignsResource(collect([
                AssociatedCompanyCampaignsResource::FIELD_HAYSTACK_CAMPAIGNS => $campaigns,
                AssociatedCompanyCampaignsResource::FIELD_NEEDLE_CAMPAIGNS   => $associatedBillingProfileCampaigns
            ]))
        ]);
    }

}
