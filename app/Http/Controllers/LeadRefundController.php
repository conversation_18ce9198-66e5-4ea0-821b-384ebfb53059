<?php

namespace App\Http\Controllers;

use App\DTO\LeadRefund\LeadRefundItemPayload;
use App\DTO\LeadRefund\LeadRefundPayload;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Refunds\AddCommentToLeadRefundRequest;
use App\Http\Requests\Refunds\CreateLeadRefundRequest;
use App\Http\Requests\Refunds\GetLeadRefundDataRequest;
use App\Http\Requests\Refunds\ListLeadRefundsRequest;
use App\Http\Requests\Refunds\ReviewLeadRefundRequest;
use App\Http\Resources\LeadRefundApprovalResource;
use App\Http\Resources\Odin\LeadRefundItemDataResource;
use App\Models\LeadRefund;
use App\Services\LeadRefundService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class LeadRefundController extends APIController
{
    const string RESPONSE_STATUS = 'status';

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected LeadRefundService $leadRefundService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param GetLeadRefundDataRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function getLeadRefundData(GetLeadRefundDataRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $data = $this->leadRefundService->getRefundLeadData(
            productAssignmentIds: Arr::get($validated, GetLeadRefundDataRequest::FIELD_PRODUCT_ASSIGNMENT_IDS)
        );

        return $this->formatResponse([
            'refund_data' => LeadRefundItemDataResource::collection($data),
        ]);
    }

    /**
     * @param CreateLeadRefundRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function create(CreateLeadRefundRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $leadRefund = new LeadRefundPayload(
            authorId: auth()->user()->id
        );

        foreach (Arr::get($validated, CreateLeadRefundRequest::FIELD_ITEMS) as $item) {
            $refundItem = new LeadRefundItemPayload(
                productAssignmentId: $item[CreateLeadRefundRequest::FIELD_ITEM_PRODUCT_ASSIGNMENT_ID],
                cost               : $item[CreateLeadRefundRequest::FIELD_ITEM_COST],
                refundType         : $item[CreateLeadRefundRequest::FIELD_ITEM_REFUND_TYPE],
                refundReason       : $item[CreateLeadRefundRequest::FIELD_ITEM_REFUND_REASON],
            );

            $leadRefund->addItem($refundItem);
        }

        $this->leadRefundService->createRefundRequest($leadRefund);

        return $this->formatResponse([
            self::RESPONSE_STATUS => true,
        ]);
    }

    /**
     * @param ListLeadRefundsRequest $request
     * @return array
     */
    public function listRefunds(ListLeadRefundsRequest $request): array
    {
        $validated = $request->validated();

        $query = $this->leadRefundService->getLeadRefundQuery(
            companyId     : Arr::get($validated, ListLeadRefundsRequest::FIELD_COMPANY_ID),
            reviewedBy    : Arr::get($validated, ListLeadRefundsRequest::FIELD_REVIEWED_BY),
            requestedBy   : Arr::get($validated, ListLeadRefundsRequest::FIELD_REQUESTED_BY),
            status        : Arr::get($validated, ListLeadRefundsRequest::FIELD_STATUS),
            leadIdLegacyId: Arr::get($validated, ListLeadRefundsRequest::FIELD_LEAD_ID_LEGACY_ID),
        );

        return LeadRefundApprovalResource::paginate($query);
    }

    /**
     * @param ListLeadRefundsRequest $request
     * @param LeadRefund $approval
     * @return LeadRefundApprovalResource
     */
    public function showApproval(ListLeadRefundsRequest $request, LeadRefund $approval): LeadRefundApprovalResource
    {
        $request->validated();
        return new LeadRefundApprovalResource($approval);
    }

    /**
     * @param ReviewLeadRefundRequest $request
     * @param LeadRefund $approval
     * @return array
     */
    public function reviewApproval(ReviewLeadRefundRequest $request, LeadRefund $approval): array
    {
        $validated = $request->validated();

        $this->leadRefundService->reviewApproval(
            approval  : $approval,
            reviewerId: auth()->user()->id,
            items     : Arr::get($validated, ReviewLeadRefundRequest::FIELD_ITEMS, []),
            comments  : Arr::get($validated, ReviewLeadRefundRequest::FIELD_COMMENTS, []),
        );

        return [
            self::RESPONSE_STATUS => true
        ];
    }


    /**
     * @param AddCommentToLeadRefundRequest $request
     * @param LeadRefund $approval
     * @return array
     */
    public function addCommentsToApproval(AddCommentToLeadRefundRequest $request, LeadRefund $approval): array
    {
        $validated = $request->validated();

        $this->leadRefundService->addComments(
            refund  : $approval,
            comments: Arr::get($validated, AddCommentToLeadRefundRequest::FIELD_COMMENTS, []),
            userId  : auth()->user()->id,
        );

        return [
            self::RESPONSE_STATUS => true
        ];
    }
}
