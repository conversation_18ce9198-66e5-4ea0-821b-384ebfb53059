<?php

namespace App\Http\Controllers;

use App\Models\Phone;
use App\Repositories\LeadProcessing\LeadCommunicationRepository;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\DB;
use function view;

class ActivateController
{
    public function index(LeadCommunicationRepository $communicationRepository): View
    {
        $roles = DB::table('roles')->get();
        $availableNumbers = $communicationRepository->getAvailableNumbers()->map(fn(Phone $phone) => ["id" => $phone->id, "number" => $phone->phone]);

        return view('activate', ['roles' => $roles, 'availableNumbers' => $availableNumbers]);
    }
}
