<?php

namespace App\Http\Controllers\Companies;

use App\Enums\ActivityLog\ActivityLogName;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Company\DeleteCompanyRequest;
use App\Http\Resources\CompanyUser\ListCompanyUserResource;
use App\Http\Resources\SalesOverview\CallResource;
use App\Http\Resources\SalesOverview\TextResource;
use App\Jobs\DeleteCompany as DeleteCompanyJob;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Repositories\ActivityLog\ActivityLogRepository;
use App\Repositories\CallRepository;
use App\Repositories\Odin\CompanyLocationRepository;
use App\Repositories\Odin\CompanyUserRepository;
use App\Repositories\TextRepository;
use App\Services\Companies\Delete\CompanyDeleteService;
use App\Services\Roles\CompanyRoleNotificationService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class CompanyDeleteController extends APIController
{
    public function __construct(
        Request                             $request,
        JsonAPIResponseFactory              $apiResponseFactory,
        protected CompanyUserRepository     $companyUserRepository,
        protected TextRepository            $textRepository,
        protected CallRepository            $callRepository,
        protected CompanyLocationRepository $companyLocationRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    public function info(Company $company): JsonResponse
    {
        $contacts = $this->companyUserRepository->list(companyId: $company->id)->get();

        $locations = $this->companyLocationRepository->list(companyId: $company->id)->get();

        $relations = $contacts->merge($locations)->push($company)->all();

        $texts = $this->textRepository->list(relations: $relations)->get();
        $calls = $this->callRepository->list(relations: $relations)->get();

        return $this->formatResponse([
            'calls'    => [
                'title' => 'Calls',
                'total' => $calls->count(),
                'data'  => CallResource::collection($calls)
            ],
            'texts'    => [
                'title' => 'Texts',
                'total' => $texts->count(),
                'data'  => TextResource::collection($texts)
            ],
            'contacts' => [
                'title' => 'Company Contacts',
                'total' => $contacts->count(),
                'data' => ListCompanyUserResource::collection($contacts),
            ],
        ]);
    }

    public function validateDelete(Company $company): JsonResponse
    {
        $deleteService = new CompanyDeleteService($company);
        $deleteService->validate();
        return $this->formatResponse([
            'deletable' =>$deleteService->isDeletable(),
            'data' => $deleteService->getValidationResults(),
        ]);
    }

    public function impact(Company $company): JsonResponse
    {
        $deleteService = new CompanyDeleteService($company);

        return $this->formatResponse([
            'data' => $deleteService->previewDelete(),
        ]);
    }

    public function dispatchDelete(Company $company, DeleteCompanyRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $reason = Arr::get($validated, DeleteCompanyRequest::REQUEST_DELETION_REASON);

        $deleteService = new CompanyDeleteService($company);

        $deleteService->markForDeletion();

        $reason = $reason ?? 'None Provided';

        app(ActivityLogRepository::class)->createActivityLog(
            logName: ActivityLogName::COMPANY_DELETION_QUEUED->value,
            description: $reason,
            subjectType: $company::class,
            subjectId: $company->id,
            properties: [
                'name' => $company->name,
                'contacts' => $company?->users->map(function (CompanyUser $user) {
                    return [
                        'name' => $user->first_name . ' ' . $user->last_name,
                        'email' => $user->email,
                        'cell_phone' => $user->formatted_cell_phone,
                        'office_phone' => $user->formatted_office_phone,
                    ];
                })
            ]
        );

        DeleteCompanyJob::dispatch($company->id)->delay(Carbon::now()->addDay());

        return $this->formatResponse([
            'status' => true,
        ]);
    }

    public function cancel(Company $company): JsonResponse
    {
        $deleteService = new CompanyDeleteService($company);

        $cancelled = $deleteService->removeMarkedForDeletion();

        return $this->formatResponse([
            'status' => $cancelled,
        ]);
    }
}
