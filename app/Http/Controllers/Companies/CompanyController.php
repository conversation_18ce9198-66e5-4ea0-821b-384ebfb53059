<?php

namespace App\Http\Controllers\Companies;

use App\Enums\CampaignStatus;
use App\Enums\Company\CompanyAdminStatus;
use App\Enums\Odin\StateAbbreviation;
use App\Http\Controllers\Controller;
use App\Http\Resources\Roles\OnboardingManagerResource;
use App\Http\Resources\Roles\AccountManagerResource;
use App\Http\Resources\Roles\SalesDevelopmentRepresentativeResource;
use App\Models\ActionCategory;
use App\Models\Odin\Company;
use App\Models\Odin\Industry;
use App\Repositories\SuccessManagerRepository;
use App\Services\Companies\Delete\CompanyDeleteService;
use App\Transformers\Odin\CompanyProfileTransformer;
use App\Transformers\Odin\IndustryTransformer;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Http\Resources\Roles\BusinessDevelopmentManagerResource;
use App\Http\Resources\Roles\CustomerSuccessManagerResource;

class CompanyController extends Controller
{
    const SEARCH_USER_ID           = 'user_id';
    const SEARCH_INDUSTRIES        = 'industries';
    const SEARCH_ACCOUNT_MANAGERS  = 'account_managers';
    const SEARCH_SUCCESS_MANAGERS  = 'success_managers';
    const SEARCH_US_STATES         = 'us_states';
    const SEARCH_CAMPAIGN_STATUSES = 'campaign_statuses';
    const SEARCH_USER_PERMISSIONS  = 'user_permissions';

    /**
     * Returns the page for showing a company if one exists.
     *
     * @param Request $request
     * @param CompanyProfileTransformer $transformer
     * @param IndustryTransformer $industryTransformer
     *
     * @return Response|RedirectResponse
     */
    public function get(
        Request                   $request,
        CompanyProfileTransformer $transformer,
        IndustryTransformer       $industryTransformer,
    ): Response|RedirectResponse
    {
        if ($request->has('redirect_id')) {
            return redirect()->route('companies.index', ['id' => $request->get('redirect_id')]);
        }

        $company = $request->get('company');

        // todo: redirect if no company found
        return response()->view('companies.index', [
            'company'                         => $transformer->transformCompanyProfileOverview($company),
            'accountManagers'                 => AccountManagerResource::collection(User::accountManagerRole()->get()),
            'successManagers'                 => CustomerSuccessManagerResource::collection(User::customerSuccessManagerRole()->get()),
            'businessDevelopmentManagers'     => BusinessDevelopmentManagerResource::collection(User::businessDevelopmentManagerRole()->get()),
            'onboardingManagers'              => OnboardingManagerResource::collection(User::onboardingManagerRole()->get()),
            'salesDevelopmentRepresentatives' => SalesDevelopmentRepresentativeResource::collection(User::salesDevelopmentRepresentativeRole()->get()),
            'industries'                      => $industryTransformer->transform(Industry::all()),
            'actionCategories'                => ActionCategory::select('id', 'name')->get(),
            'adminStatuses'                   => $this->getAdminOptions($company),
        ]);
    }

    /**
     * Returns the page for searching companies.
     *
     * @param SuccessManagerRepository $successManagerRepository
     * @return Response
     */
    public function search(SuccessManagerRepository $successManagerRepository): Response
    {
        $sortedSuccessManagers = $successManagerRepository->getAlphabetizedSuccessManagersWithClients();

        $currentUser = Auth::user();

        $currentUserPermissions = $currentUser->getAllPermissions()->toArray();

        return response()->view('companies.search', [
            self::SEARCH_USER_ID           => Auth::id(),
            self::SEARCH_INDUSTRIES        => Industry::all(),
            self::SEARCH_ACCOUNT_MANAGERS  => AccountManagerResource::collection(User::accountManagerRole()->get()),
            self::SEARCH_SUCCESS_MANAGERS  => $sortedSuccessManagers,
            self::SEARCH_US_STATES         => StateAbbreviation::asSelectArray(),
            self::SEARCH_CAMPAIGN_STATUSES => CampaignStatus::asSelectArray(),
            self::SEARCH_USER_PERMISSIONS  => $currentUserPermissions,
        ]);
    }

    /**
     * @param Company $company
     *
     * @return array
     */
    protected function getAdminOptions(Company $company): array
    {
        return $company->getAdminStatusOptionsForUser()->map(fn(CompanyAdminStatus $adminStatus) => [
            'id'   => $adminStatus->value,
            'name' => $adminStatus->label()
        ])
            ->sortby(fn(array $status) => $status['name'])
            ->values()
            ->toArray();
    }

    public function cancelDelete(int $companyId) : RedirectResponse
    {
        try {
            $company = Company::query()->findOrFail($companyId);

            $service = new CompanyDeleteService($company);
            $service->removeMarkedForDeletion();

            session()->flash('status', 'Company deletion Cancelled');

            return redirect()->route('companies.index', ['id' => $companyId]);
        } catch (ModelNotFoundException $e) {
            session()->flash('status', 'Company has already been deleted');
            return redirect()->route('companies.search');
        }
    }
}
