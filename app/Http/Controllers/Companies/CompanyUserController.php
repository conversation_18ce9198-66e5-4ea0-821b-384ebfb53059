<?php
namespace App\Http\Controllers\Companies;

use App\Http\Controllers\Controller;
use App\Http\Resources\Dashboard\CompanyResource;
use App\Http\Resources\Dashboard\CompanyUserResource;
use App\Models\Action;
use App\Repositories\Odin\CompanyRepository;
use App\Repositories\Odin\CompanyUserRepository;
use Illuminate\Http\Response;
class CompanyUserController extends Controller
{
    /**
     * Returns the page for showing a company user if one exists.
     *
     * @param int $companyUserId
     * @param CompanyUserRepository $companyUserRepository
     * @param CompanyRepository $companyRepository
     * @return Response
     */
    public function index(
        int $companyUserId,
        CompanyUserRepository $companyUserRepository,
        CompanyRepository $companyRepository,
    ): Response
    {
        $companyUser = $companyUserRepository->findCompanyUserByIdOrFail($companyUserId);
        $companyData = $companyRepository->findOrFail($companyUser->company_id);

        // Retrieve counts of office phone calls and cell phone calls
        $officePhoneCallsCount = $companyUser->office_phone_calls()->count();
        $cellPhoneCallsCount = $companyUser->cell_phone_calls()->count();

        // Append counts onto the $companyUser object
        $companyUser->office_phone_calls_count = $officePhoneCallsCount;
        $companyUser->cell_phone_calls_count = $cellPhoneCallsCount;

        return response()->view('companies.company-user', [
            'companyUser' => new CompanyUserResource($companyUser),
            'companyData' => $companyData
        ]);
    }
}
