<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\UserFilterPreset;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class UserSettingsController extends Controller
{
    public function index(): View
    {
        /** @var User $user */
        $user = Auth::user();
        return view('user-settings', ['user' => $user]);
    }

    /**
     * Renders user settings page
     *
     * @return View
     */

    public function filterPresets(): View
    {
        /** @var User $user */
        $user = Auth::user();

        /** @var UserFilterPreset $filterPreset */
        $filterPreset = $user->filterPreset['data'] ?? json_encode(new \stdClass());

        return view('filter-presets', ['user' => $user, 'filterPreset' => $filterPreset]);
    }

    public function editProfile(): View
    {
        /** @var User $user */
        $userId = Auth::user()->id;

        $user = User::query()
            ->select(DB::raw('users.id, users.name, users.email'))
            ->with([
                'roles' => function ($query) {
                    $query->with('permissions');
                },
                'phones',
            ])
            ->leftJoin('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
            ->leftJoin('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->whereRaw('model_has_roles.model_type like "%\User%" and users.id = ?', [$userId])
            ->groupBy('users.id')
            ->orderBy('users.id')
            ->firstOrFail();

        $userRoleAndPermissions = [];

        foreach ($user->roles as $role) {
            $rolePermissions = $role->permissions->pluck('name')->toArray();
            $userRoleAndPermissions[$role->name] = $rolePermissions;
        }

        $user->phoneNumbers = $user->phones->pluck('phone')->toArray();

        return view('edit-user-profile', [
            'user' => $user,
            'userRoleAndPermissions' => $userRoleAndPermissions,
        ]);
    }
}
