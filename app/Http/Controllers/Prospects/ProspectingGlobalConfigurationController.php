<?php

namespace App\Http\Controllers\Prospects;

use App\DataModels\Odin\ConfigurableFieldDataModel;
use Illuminate\Http\JsonResponse;
use App\Models\GlobalConfiguration;
use App\Http\Controllers\Controller;
use App\Http\Requests\Prospects\UpdateGlobalConfig;
use App\Http\Resources\ConfigurationResource;

class ProspectingGlobalConfigurationController extends Controller
{
    public function index()
    {
        $globalConfig = GlobalConfiguration::whereConfigurationKey('role_configurations')->first();

        if ($globalConfig) {
            $configurations = $globalConfig->configuration_payload->data->map(function ($value, $key) {
                return [
                    'key' => $key,
                    'value' => $value
                ];
            })->values();

            return ConfigurationResource::collection($configurations);
        }

        return response()->json(status: 204);
    }

    public function update(UpdateGlobalConfig $request): JsonResponse
    {
        $data = $request->safe()->collect('configs')->flatMap(fn($config) => [$config['key'] => $config['value']])->all();

        $globalConfig = GlobalConfiguration::whereConfigurationKey('role_configurations')->firstOrFail();

        $globalConfig->update([
            'configuration_payload' => new ConfigurableFieldDataModel($globalConfig->configuration_payload->data->merge($data))
        ]);

        return response()->json(status: 204);
    }
}
