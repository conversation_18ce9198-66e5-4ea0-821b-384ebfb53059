<?php

namespace App\Http\Controllers\Prospects;

use App\Models\RoleConfiguration;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Prospects\CreateUserConfig;
use App\Http\Requests\Prospects\UpdateUserConfig;
use App\Http\Resources\RoleConfigurationResource;
use App\Http\Controllers\Prospects\ProspectingConfigController;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class ProspectingUserConfigurationController extends ProspectingConfigController
{
    public function index(): AnonymousResourceCollection
    {
        $data = RoleConfiguration::whereHas('role', fn ($q) => $q->whereIn('name', $this->roles))->get();

        return RoleConfigurationResource::collection($data);
    }

    function store(CreateUserConfig $request) {
        $data = $request->fluent();

        RoleConfiguration::create([
            'user_id' => $data->user_id,
            'role_id' => $data->role_id,
            'data' => collect($request->configs)
                ->flatMap(fn ($config) => [$config['key'] => $config['value']])
                ->all()
        ]);

        return response()->json(status: 201);
    }

    public function update(RoleConfiguration $roleConfiguration, UpdateUserConfig $request): JsonResponse
    {
        $data = $request->collect('configs')
            ->flatMap(fn ($config) => [$config['key'] => $config['value']])
            ->all();

        $roleConfiguration->update(compact('data'));

        return response()->json(status: 204);
    }
}
