<?php

namespace App\Http\Controllers;

use App\Enums\PermissionType;
use App\Models\GlobalConfiguration;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use App\Services\GlobalConfigurationService;

class GlobalConfigurationsManagementController extends Controller
{
    /**
     * @return View
     */
    public function index(): View
    {
        $configurations = GlobalConfiguration::all()->map(function ($configuration) {
            return GlobalConfigurationService::prepareConfigurationResponse($configuration);
        });

        $hasEditRights = Auth::user()->hasPermissionTo(PermissionType::GLOBAL_CONFIGURATIONS_EDIT->value);

        return view('global-configurations-management', compact( 'hasEditRights', 'configurations'));
    }
}
