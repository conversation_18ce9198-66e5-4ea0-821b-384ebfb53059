<?php

namespace App\Http\Controllers;

use App\Enums\CompanyConfigurationEnum;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\SaveCompanyConfigurationRequest;
use App\Http\Resources\CompanyConfigurationResource;
use App\Models\Odin\CompanyConfiguration;
use App\Repositories\CompanyConfigurationRepository;
use App\Services\CompanyConfigurationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CompanyConfigurationController extends APIController
{
    const RESPONSE_STATUS                 = 'status';
    const RESPONSE_COMPANY_CONFIGURATIONS = 'company_configurations';

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected CompanyConfigurationService $companyConfigurationService
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    public function getConfiguration(int $companyId): JsonResponse
    {
        $companyConfiguration = $this->companyConfigurationService->getCompanyConfiguration($companyId);

        $filteredConfiguration = $this->companyConfigurationService->filterConfigurationsUserHasAccess(
            configAttributes: $companyConfiguration->getAttributes(),
            user            : auth()->user()
        );

        $companyConfiguration = new CompanyConfiguration();
        $companyConfiguration->fill($filteredConfiguration);

        return $this->formatResponse([
            self::RESPONSE_STATUS                 => true,
            self::RESPONSE_COMPANY_CONFIGURATIONS => new CompanyConfigurationResource($companyConfiguration)
        ]);
    }


    public function saveConfiguration(int $companyId, SaveCompanyConfigurationRequest $request, CompanyConfigurationRepository $repository): JsonResponse
    {
        $validated = $request->validated();

        $filteredConfiguration = $this->companyConfigurationService->filterConfigurationsUserHasAccess(
            configAttributes: $validated,
            user            : auth()->user()
        );

        $companyConfiguration = $this->companyConfigurationService->saveCompanyConfiguration(
            $companyId,
            allowLeadsNoCc             : $filteredConfiguration[CompanyConfigurationEnum::ALLOW_LEADS_NO_CC->value] ?? null,
            enableTcpaPlayback         : $filteredConfiguration[CompanyConfigurationEnum::ENABLE_TCPA_PLAYBACK->value] ?? null,
            neverExceedBudget          : $filteredConfiguration[CompanyConfigurationEnum::NEVER_EXCEED_BUDGET->value] ?? null,
            disallowRanking            : $filteredConfiguration[CompanyConfigurationEnum::DISALLOW_RANKING->value] ?? null,
            receiveOffHourLeads        : $filteredConfiguration[CompanyConfigurationEnum::RECEIVE_OFF_HOUR_LEADS->value] ?? null,
            appointmentsActive         : $filteredConfiguration[CompanyConfigurationEnum::APPOINTMENTS_ACTIVE->value] ?? null,
            miAppointmentsActive       : $filteredConfiguration[CompanyConfigurationEnum::MI_APPOINTMENTS_ACTIVE->value] ?? null,
            requireAppointmentsCalendar: $filteredConfiguration[CompanyConfigurationEnum::REQUIRE_APPOINTMENTS_CALENDAR->value] ?? null,
            missedProductsActive       : $filteredConfiguration[CompanyConfigurationEnum::MISSED_PRODUCTS_ACTIVE->value] ?? null,
            reviewsEnabled             : $filteredConfiguration[CompanyConfigurationEnum::REVIEWS_ENABLED->value] ?? null,
            acceptUnderReviewLeads     : $filteredConfiguration[CompanyConfigurationEnum::ACCEPT_UNDER_REVIEW_LEADS->value] ?? null,
        );

        $repository->updateCompanyConfiguration($companyConfiguration, [
            CompanyConfiguration::FIELD_CAMPAIGN_ALERT_ENABLED => $request->validated(CompanyConfigurationEnum::FIELD_CAMPAIGN_ALERT_ENABLED->value),
            CompanyConfiguration::FIELD_PAUSE_CAMPAIGN_ON_THRESHOLD_EXCEEDED => $request->validated(CompanyConfigurationEnum::PAUSE_CAMPAIGN_ON_THRESHOLD_EXCEEDED->value),
            CompanyConfiguration::FIELD_CONSUMER_PROXY_PHONE_ENABLED => $request->validated(CompanyConfigurationEnum::CONSUMER_PROXY_PHONE_ENABLED->value),
        ]);

        return $this->formatResponse([
            self::RESPONSE_STATUS                 => true,
            self::RESPONSE_COMPANY_CONFIGURATIONS => new CompanyConfigurationResource($companyConfiguration)
        ]);
    }
}
