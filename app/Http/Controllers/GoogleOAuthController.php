<?php

namespace App\Http\Controllers;

use App\Services\GoogleOAuthService;
use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Arr;
use Throwable;

class GoogleOAuthController extends Controller
{
    public function __construct(
        protected GoogleOAuthService $googleOAuthService
    )
    {

    }

    /**
     * @return array|RedirectResponse
     */
    public function handleOAuthCallback(): array|RedirectResponse
    {
        try {
            $param = json_decode(safeBase64Decode(request()->get('state')), true);

            $this->googleOAuthService->createUserToken(
                code   : request()->get('code'),
                scopes : request()->get('scope'),
                userId : Arr::get($param, 'user_id'),
                service: Arr::get($param, 'service'),
            );

        } catch (Exception $exception) {
            logger()->error($exception);
        } catch (Throwable $throwable) {
            logger()->error($throwable);
        }

        return redirect('dashboard');
    }
}
