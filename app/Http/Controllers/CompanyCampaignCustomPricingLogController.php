<?php

namespace App\Http\Controllers;

use App\DTO\FloorPricing\FloorPricingHistoryLog;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\FloorPrice\ListCustomCampaignCustomPricingLogsRequest;
use App\Http\Resources\CompanyCampaignCustomPricingLogResource;
use App\Http\Resources\FloorPricingHistoryLogsResource;
use App\Services\CompanyCampaignCustomPricingLogService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class CompanyCampaignCustomPricingLogController extends APIController
{
    const string STATE_FLOOR_PRICING_LOGS = 'state_floor_pricing_logs';
    const string COUNTY_FLOOR_PRICING_LOGS = 'county_floor_pricing_logs';

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param CompanyCampaignCustomPricingLogService $companyCampaignCustomPricingLogService
     */
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected CompanyCampaignCustomPricingLogService $companyCampaignCustomPricingLogService
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param ListCustomCampaignCustomPricingLogsRequest $request
     * @return JsonResponse
     */
    public function listCompanyCampaignCustomStatePricingLogs(ListCustomCampaignCustomPricingLogsRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $customStatePricingLogs = $this->companyCampaignCustomPricingLogService->listCustomStatePricingLogs(
            campaign    : Arr::get($validated, ListCustomCampaignCustomPricingLogsRequest::REQUEST_CAMPAIGN),
            companyId   : Arr::get($validated, ListCustomCampaignCustomPricingLogsRequest::REQUEST_COMPANY_COMPANY_ID)
        );

        return $this->formatResponse([
           self::STATE_FLOOR_PRICING_LOGS => CompanyCampaignCustomPricingLogResource::collection($customStatePricingLogs),
       ]);
    }

    /**
     * @param ListCustomCampaignCustomPricingLogsRequest $request
     * @return JsonResponse
     */
    public function listCompanyCampaignCustomCountyPricingLogs(ListCustomCampaignCustomPricingLogsRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $customCountyPricingLogs = $this->companyCampaignCustomPricingLogService->listCustomCountyPricingLogs(
            campaign    : Arr::get($validated, ListCustomCampaignCustomPricingLogsRequest::REQUEST_CAMPAIGN),
            companyId   : Arr::get($validated, ListCustomCampaignCustomPricingLogsRequest::REQUEST_COMPANY_COMPANY_ID)
        );

        return $this->formatResponse([
            self::COUNTY_FLOOR_PRICING_LOGS => CompanyCampaignCustomPricingLogResource::collection($customCountyPricingLogs),
        ]);
    }
}
