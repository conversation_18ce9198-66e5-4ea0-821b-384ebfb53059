<?php

namespace App\Http\Controllers\Odin\API;

use App\Enums\Odin\Product;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Odin\Engines\GetTopCompaniesRequest;
use App\Http\Resources\TopCompanies\TopCompaniesResourceCollection;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Legacy\Location;
use App\Models\Odin\Company;
use App\Repositories\Odin\IndustryServiceRepository;
use App\Services\Odin\API\FlowEnginesService;
use App\Services\Odin\TopCompanies\TopCompaniesService;
use App\Transformers\OptInTransformer;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;

class FlowEnginesController extends APIController
{
    const int OPT_IN_CAMPAIGN_LIMIT = 4;

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected FlowEnginesService $flowEnginesService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param string $industryServiceSlug
     * @param string $zipCode
     * @param int $limit
     * @param array $companiesExcluded
     * @return Collection
     */
    private static function getNonPurchasingCompanies(string $industryServiceSlug, string $zipCode, int $limit, array $companiesExcluded): Collection
    {
        /** @var IndustryServiceRepository $industryServiceRepository */
        $industryServiceRepository = app(IndustryServiceRepository::class);

        /** @var TopCompaniesService $topCompaniesService */
        $topCompaniesService = app(TopCompaniesService::class);

        $industryService = $industryServiceRepository->findBySlugOrFail($industryServiceSlug);
        return $topCompaniesService->getTopNonPurchasingCompanies(
            industryService: $industryService,
            zipCode: $zipCode,
            limit: $limit,
            companiesExcluded: $companiesExcluded
        );
    }

    /**
     * @param GetTopCompaniesRequest $request
     * @param TopCompaniesService $topCompaniesService
     * @return JsonResponse
     */
    public function getActiveCampaignsForOptIn(GetTopCompaniesRequest $request, TopCompaniesService $topCompaniesService): JsonResponse
    {
        $campaigns = $topCompaniesService->getTopCampaignsForServiceAndZipCode(
            industryServiceSlug: $request->validated(GetTopCompaniesRequest::REQUEST_INDUSTRY_SERVICE),
            zipCode: $request->validated(GetTopCompaniesRequest::REQUEST_ZIP_CODE),
            limit: self::OPT_IN_CAMPAIGN_LIMIT
        );

        $companies = null;
        if($campaigns->count() < self::OPT_IN_CAMPAIGN_LIMIT) {
            $companies = self::getNonPurchasingCompanies(
                industryServiceSlug: $request->validated(GetTopCompaniesRequest::REQUEST_INDUSTRY_SERVICE),
                zipCode: $request->validated(GetTopCompaniesRequest::REQUEST_ZIP_CODE),
                limit: self::OPT_IN_CAMPAIGN_LIMIT - $campaigns->count(),
                companiesExcluded: $campaigns->pluck(CompanyCampaign::FIELD_COMPANY_ID)->toArray()
            );
        }

        return $this->formatResponse([
            'status'    => true,
            'opt-ins' => OptInTransformer::transform($campaigns, $companies)
        ]);
    }

}
