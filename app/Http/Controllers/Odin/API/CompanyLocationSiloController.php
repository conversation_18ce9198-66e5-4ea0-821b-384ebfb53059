<?php

namespace App\Http\Controllers\Odin\API;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Odin\API\CompanyLocationSiloDataRequest;
use App\Http\Requests\Odin\API\CompanyLocationSiloEntryRequest;
use App\Jobs\RefreshLocationSiloResultsCache;
use App\Repositories\Odin\WebsiteRepository;
use App\Services\Odin\API\CompanyLocationSiloService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class CompanyLocationSiloController extends APIController
{

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param CompanyLocationSiloService $siloService
     * @param WebsiteRepository $websiteRepository
     */
    public function __construct(
        Request                $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected CompanyLocationSiloService $siloService,
        protected WebsiteRepository $websiteRepository
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param CompanyLocationSiloEntryRequest $request
     * @return JsonResponse
     */
    public function getEntry(CompanyLocationSiloEntryRequest $request): JsonResponse
    {
        $requestFullPath = $request->get(CompanyLocationSiloEntryRequest::FIELD_REQUEST_FULL_PATH);

        $data = $this->siloService->getEntryData($requestFullPath, $request->get(CompanyLocationSiloService::WEBSITE_KEY, CompanyLocationSiloService::DEFAULT_WEBSITE_KEY));

        return $this->formatResponse($data);
    }

    /**
     * @param CompanyLocationSiloDataRequest $request
     * @return JsonResponse
     */
    public function getSiloData(CompanyLocationSiloDataRequest $request): JsonResponse
    {
        $locationSiloPageId = $request->get(CompanyLocationSiloDataRequest::FIELD_LOCATION_SILO_PAGE_ID);
        $dataRequests       = $request->get(CompanyLocationSiloDataRequest::FIELD_COMPONENT_DATA_REQUESTS);

        $data = $this->siloService->getDataFromRepositories($locationSiloPageId, $dataRequests, false);

        return $this->formatResponse($data);
    }
}
