<?php

namespace App\Http\Controllers\Odin\API;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Odin\API\ComponentDataRequest;
use App\Repositories\Odin\WebsiteRepository;
use App\Services\Odin\API\ComponentDataService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ComponentDataController extends APIController
{

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param ComponentDataService $dataService
     * @param WebsiteRepository $websiteRepository
     */
    public function __construct(
        Request                $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected ComponentDataService $dataService,
        protected WebsiteRepository $websiteRepository
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param ComponentDataRequest $request
     * @return JsonResponse
     */
    public function getComponentData(ComponentDataRequest $request): JsonResponse
    {
        $dataRequests = $request->get(ComponentDataRequest::FIELD_COMPONENT_DATA_REQUESTS);

        $data = $this->dataService->getDataFromRepositories($dataRequests);

        return $this->formatResponse($data);
    }
}
