<?php

namespace App\Http\Controllers\Odin\API;

use App\Enums\Odin\ConsumerFieldType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Odin\GetFieldsVisibilityByFieldCategoryRequest;
use App\Http\Requests\Odin\SaveConsumerFieldVisibilityRequest;
use App\Models\Odin\IndustryService;
use App\Services\Odin\ConsumerFieldModuleVisibilityAPIService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ConsumerFieldModuleVisibilityController extends APIController
{
    const FIELD_STATUS = 'status';
    const FIELD_FIELDS = 'fields';
    const FIELD_MODULES = 'modules';

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected ConsumerFieldModuleVisibilityAPIService $consumerFieldModuleVisibilityService
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    public function getFieldsVisibilityByFieldCategory(GetFieldsVisibilityByFieldCategoryRequest $request): JsonResponse
    {
        $systemModules = \App\Enums\Odin\SystemModule::cases();
        $modules = [];

        foreach ($systemModules as $module) {
            $modules[] = [
                "id" => $module->value,
                "name" => $module->getDescription(),
                "features" => collect($module->getFeatures())->map(fn ($feature) => ["id" => $feature->value, "name" => $feature->getDescription()])
            ];
        }

        $data = $request->validated();

        $category = ConsumerFieldType::tryFrom($data[GetFieldsVisibilityByFieldCategoryRequest::FIELD_CATEGORY]);
        $categoryId = $category === ConsumerFieldType::SERVICE
            ? IndustryService::query()->where(IndustryService::FIELD_SLUG, $data[GetFieldsVisibilityByFieldCategoryRequest::FIELD_CATEGORY_ID])->first()->id
            : $data[GetFieldsVisibilityByFieldCategoryRequest::FIELD_CATEGORY_ID];

        $fieldsVisibility = $this->consumerFieldModuleVisibilityService
            ->getAll(
                $category,
                $categoryId
            );

        return $this->formatResponse([
            self::FIELD_STATUS => true,
            self::FIELD_FIELDS => $fieldsVisibility,
            self::FIELD_MODULES => $modules,
        ]);
    }

    public function saveMany(SaveConsumerFieldVisibilityRequest $request): JsonResponse
    {
        $data = $request->validated();

        $category = $request->get('category');
        $categoryId = $category === 'service'
            ? IndustryService::query()->where(IndustryService::FIELD_SLUG, $request->get('category_id'))->first()->id
            : $request->get('category_id');


        $this->consumerFieldModuleVisibilityService->saveMany(
            $category,
            $categoryId,
            $data[SaveConsumerFieldVisibilityRequest::FIELD_FIELDS]
        );

        return $this->formatResponse([
            self::FIELD_STATUS => true
        ]);
    }
}
