<?php

namespace App\Http\Controllers\Odin\API;

use App\DTO\Mailbox\ListUserEmailsParam;
use App\Enums\Mailbox\EmailModificationAction;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Mailbox\DeleteEmailRequest;
use App\Http\Requests\Mailbox\ForwardEmailRequest;
use App\Http\Requests\Mailbox\ListEmailsRequest;
use App\Http\Requests\Mailbox\ModifyEmailRequest;
use App\Http\Requests\Mailbox\ReplyEmailRequest;
use App\Http\Requests\Mailbox\SendEmailRequest;
use App\Http\Resources\Mailbox\MailboxUserEmailResource;
use App\Http\Resources\Mailbox\MailboxUserLabelResource;
use App\Jobs\Mailbox\SyncUserMailboxJob;
use App\Models\User;
use App\Repositories\Mailbox\MailboxUserTokenRepository;
use App\Services\Mailbox\Mail\MailProviderFactory;
use App\Services\Mailbox\MailboxApiService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class MailboxUserEmailController extends APIController
{
    const FIELD_STATUS                = 'status';
    const FIELD_EMAILS                = 'emails';
    const FIELD_IDENTIFIED_RECIPIENTS = 'identified_recipients';

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param MailboxApiService $mailboxApiService
     * @param MailboxUserTokenRepository $mailboxUserRepository
     */
    public function __construct(
        Request                              $request,
        JsonAPIResponseFactory               $apiResponseFactory,
        protected MailboxApiService          $mailboxApiService,
        protected MailboxUserTokenRepository $mailboxUserRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * List users' emails
     * @param ListEmailsRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function listEmails(ListEmailsRequest $request): JsonResponse
    {
        $validated = $request->validated();
        /** @var User $user */
        $user = auth()->user();

        $emailsQuery = $this->mailboxApiService
            ->getListEmailsQuery(
                $user->{User::FIELD_ID},
                new ListUserEmailsParam(
                    Arr::get($validated, ListEmailsRequest::FIELD_TAB),
                    Arr::get($validated, ListEmailsRequest::FIELD_QUERY),
                    Arr::get($validated, ListEmailsRequest::FIELD_UUID),
                    Arr::get($validated, ListEmailsRequest::FIELD_COMPANY_ID),
                )
            );
        $resource = new MailboxUserEmailResource(collect(
            [
                MailboxUserEmailResource::FIELD_QUERY          => $emailsQuery,
            ]
        ));

        $postPaginated = $resource->postPaginate();

        return $this->formatResponse([
            self::FIELD_STATUS => true,
            self::FIELD_EMAILS => $postPaginated
        ]);
    }

    /**
     * Get users' emails related by thread
     * @param string $userEmailUuid
     * @return JsonResponse
     * @throws \Exception
     */
    public function getEmailDetailData(string $userEmailUuid): JsonResponse
    {
        /** @var User $user */
        $user = auth()->user();

        $emails = $this->mailboxApiService->getEmailsRelatedByThread($userEmailUuid);

        $resource = new MailboxUserEmailResource(collect(
            [
                MailboxUserEmailResource::FIELD_MAILBOX_USER_EMAILS     => $emails,
            ]
        ));

        $transformedEmails = $resource->postCollection($emails);

        $identifiedRecipients = [];

        foreach ($transformedEmails as $email) {
            $recipients = array_merge($email['to'], $email['bcc'], $email['cc']);
            $recipients[] = $email['from'];
            foreach ($recipients as $item) {
                if (!isset($identifiedRecipients[$item['related_model_id']]) && $item?->identifier_value !== $user->email) {
                    $identifiedRecipients[$item->identifier_value] = $item;
                }
            }
        }

        return $this->formatResponse([
            self::FIELD_STATUS                  => true,
            self::FIELD_EMAILS                  => $resource->postCollection($emails),
            self::FIELD_IDENTIFIED_RECIPIENTS   => array_values($identifiedRecipients)
        ]);
    }

    /**
     * @param DeleteEmailRequest $request
     * @return JsonResponse
     */
    public function deleteEmails(DeleteEmailRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $emailIds = Arr::get($validated, DeleteEmailRequest::FIELD_EMAIL_IDS);

        return $this->formatResponse([
            $this->mailboxApiService->deleteEmails($emailIds, Arr::get($validated, DeleteEmailRequest::FIELD_DELETE_THREAD, false))
        ]);
    }

    /**
     * Send user email
     * @param SendEmailRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function sendEmail(SendEmailRequest $request): JsonResponse
    {
        $validated = $request->validated();

        /** @var User $user */
        $user = auth()->user();
        return $this->formatResponse([
            self::FIELD_STATUS => $this->mailboxApiService
                ->sendEmail(
                    $user,
                    Arr::get($validated, SendEmailRequest::FIELD_TO),
                    Arr::get($validated, SendEmailRequest::FIELD_SUBJECT),
                    Arr::get($validated, SendEmailRequest::FIELD_CONTENT),
                    Arr::get($validated, SendEmailRequest::FIELD_BCC, []),
                    Arr::get($validated, SendEmailRequest::FIELD_CC, []),
                )
        ]);
    }

    /**
     * @param string $emailUuidToReply
     * @param ReplyEmailRequest $request
     * @return JsonResponse
     */
    public function replyEmail(string $emailUuidToReply, ReplyEmailRequest $request): JsonResponse
    {
        $validated = $request->validated();

        /** @var User $from */
        $user = auth()->user();

        return $this->formatResponse([
            self::FIELD_STATUS => $this->mailboxApiService
                ->replyEmail(
                    $emailUuidToReply,
                    $user,
                    Arr::get($validated, ReplyEmailRequest::FIELD_TO),
                    Arr::get($validated, ReplyEmailRequest::FIELD_CONTENT),
                    Arr::get($validated, ReplyEmailRequest::FIELD_BCC, []),
                    Arr::get($validated, ReplyEmailRequest::FIELD_CC, []),
                )
        ]);
    }

    /**
     * @param string $emailUuidToForward
     * @param ForwardEmailRequest $request
     * @return JsonResponse
     */
    public function forwardEmail(string $emailUuidToForward, ForwardEmailRequest $request): JsonResponse
    {
        $validated = $request->validated();

        /** @var User $user */
        $user = auth()->user();

        return $this->formatResponse([
            self::FIELD_STATUS => $this->mailboxApiService
                ->forwardEmail(
                    $emailUuidToForward,
                    $user,
                    Arr::get($validated, ForwardEmailRequest::FIELD_TO),
                    Arr::get($validated, ForwardEmailRequest::FIELD_CONTENT),
                    Arr::get($validated, ForwardEmailRequest::FIELD_BCC, []),
                    Arr::get($validated, ForwardEmailRequest::FIELD_CC, []),
                )
        ]);
    }

    /**
     * Generates the consent screen URL if user hasn't provided consent yet
     * @return JsonResponse
     */
    public function generateUrlForPermissionToMailbox(): JsonResponse
    {
        /** @var User $user */
        $user = auth()->user();

        $emailToken = $this->mailboxUserRepository->getLatestUserEmailToken($user);

        return $this->formatResponse([
            self::FIELD_STATUS => true,
            'has_token' => !!$emailToken,
            'url'       => !$emailToken ? $this->mailboxApiService->generateUrlForPermissionToMailbox($user->{User::FIELD_ID}) : null
        ]);
    }

    /**
     * @param ModifyEmailRequest $request
     * @return JsonResponse
     */
    public function modifyEmails(ModifyEmailRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $modificationAction = EmailModificationAction::tryFrom(Arr::get($validated, ModifyEmailRequest::FIELD_ACTION));
        $emailIds = Arr::get($validated, ModifyEmailRequest::FIELD_EMAIL_IDS);

        return $this->formatResponse([
            $this->mailboxApiService->modifyEmails($modificationAction, $emailIds)
        ]);
    }

    public function getLabels(): JsonResponse
    {
        /** @var User $user */
        $user = auth()->user();

        return $this->formatResponse([
            self::FIELD_STATUS  => 'true',
            'labels'            => MailboxUserLabelResource::collection($this->mailboxApiService->getLabels($user->{User::FIELD_ID}))
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getUserEmailSignature(): JsonResponse
    {
        /** @var User $user */
        $user = auth()->user();

        return $this->formatResponse([
            'signature' => $this->mailboxApiService->getUserEmailSignature($user)
        ]);
    }
}
