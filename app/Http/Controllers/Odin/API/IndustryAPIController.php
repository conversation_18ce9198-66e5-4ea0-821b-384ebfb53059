<?php

namespace App\Http\Controllers\Odin\API;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\IndustryWebsite;
use App\Models\Odin\Website;
use App\Repositories\Odin\IndustryRepository;
use App\Services\Odin\WebsiteAuthenticationService;
use App\Transformers\Odin\CompanyTransformer;
use App\Transformers\Odin\IndustryServiceTransformer;
use App\Transformers\Odin\IndustryTransformer;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;
use \Illuminate\Contracts\Container\BindingResolutionException;

class IndustryAPIController extends APIController
{
    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param IndustryRepository $industryRepository
     * @param WebsiteAuthenticationService $websiteAuthenticationService
     */
    public function __construct(
        Request                                $request,
        JsonAPIResponseFactory                 $apiResponseFactory,
        protected IndustryRepository           $industryRepository,
        protected WebsiteAuthenticationService $websiteAuthenticationService
    ) { parent::__construct($request, $apiResponseFactory); }

    /**
     * Returns list of industries along with their details against the requesting website.
     *
     * @param IndustryTransformer $transformer
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getIndustries(IndustryTransformer $transformer): JsonResponse
    {
        $industries = $this->getWebsiteIndustries();

        return $this->formatResponse([
            'status'     => 'true',
            'industries' => $industries
                ? $transformer->transform($this->industryRepository->getIndustries(true, true, true, $industries))
                : []
        ]);
    }

    /**
     * Returns details of a specific industry which is also supported by the requesting website.
     *
     * @param Industry $industry
     * @param IndustryTransformer $transformer
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getIndustryDetail(Industry $industry, IndustryTransformer $transformer): JsonResponse
    {
        $this->validateRequest($industry);

        return $this->formatResponse([
            'status'   => 'true',
            'industry' => $transformer->transform(
                $this->industryRepository->getIndustryDetail($industry->id,true, true, true)
            )
        ]);
    }

    /**
     * Returns list of companies against a specific industry which is also supported by the requesting website.
     *
     * @param Industry $industry
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getIndustryCompanies(Industry $industry): JsonResponse
    {
        $this->validateRequest($industry);

        return $this->formatResponse([
            'status'    => 'true',
            'companies' => []
        ]);
    }

    /**
     * Returns list of services against a specific industry which is also supported by the requesting website.
     *
     * @param Industry $industry
     * @param IndustryServiceTransformer $transformer
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getIndustryServices(Industry $industry, IndustryServiceTransformer $transformer): JsonResponse
    {
        $this->validateRequest($industry);

        return $this->formatResponse([
            'status'   => 'true',
            'services' => $transformer->transform(
                $this->industryRepository->getIndustryServices($industry->id, true)
            )
        ]);
    }

    /**
     * Returns list of companies against a specific service which is also supported by the requesting website.
     *
     * @param Industry $industry
     * @param IndustryService $industryService
     * @param CompanyTransformer $transformer
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getIndustryServicedCompanies(Industry $industry, IndustryService $industryService, CompanyTransformer $transformer): JsonResponse
    {
        $this->validateRequest($industry, $industryService);

        return $this->formatResponse([
            'status'    => 'true',
            'companies' => $transformer->transformCompanies(
                $this->industryRepository->getIndustryServicedCompanies($industryService->id)
            )
        ]);
    }

    /**
     * Takes care of validating a request (industry and service) against the requesting website.
     *
     * @param Industry $industry
     * @param IndustryService|null $industryService
     * @return void
     * @throws BindingResolutionException
     */
    protected function validateRequest(Industry $industry, ?IndustryService $industryService = null): void
    {
        if(!in_array($industry->id, $this->getWebsiteIndustries()))
            throw new BadRequestException();

        if($industryService
            && !in_array($industryService->id, $industry->services()->pluck(IndustryService::FIELD_ID)->toArray()))
            throw new BadRequestException();
    }

    /**
     * Returns list of industries against the requesting website.
     *
     * @return array
     * @throws BindingResolutionException
     */
    protected function getWebsiteIndustries(): array
    {
        if(!$this->websiteAuthenticationService->isWebsiteAuthenticated()) throw new BadRequestException();

        /** @var Website $website */
        $website = $this->websiteAuthenticationService->getAuthenticatedWebsite();

        /** @var IndustryWebsite $industries */
        $industries = IndustryWebsite::query()
            ->where(IndustryWebsite::FIELD_WEBSITE_ID, $website->id)
            ->pluck(IndustryWebsite::FIELD_INDUSTRY_ID);

        return $industries->toArray();
    }
}
