<?php

namespace App\Http\Controllers\Odin\API\Engines;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Odin\Engines\LegacySolarEngineRequest;
use App\Repositories\Legacy\SEUtilityRepository;
use App\Repositories\Legacy\UtilityRepository;
use App\Repositories\Odin\Engines\LegacySolarEngineRepository;
use App\Transformers\Legacy\UtilityTransformer;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class LegacySolarEngineController extends APIController
{
    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param LegacySolarEngineRepository $legacySolarEngineRepository
     * @param SEUtilityRepository $utilityRepository
     * @param UtilityTransformer $utilityTransformer
     */
    public function __construct(
        Request                $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected LegacySolarEngineRepository $legacySolarEngineRepository,
        protected SEUtilityRepository $utilityRepository,
        protected UtilityTransformer $utilityTransformer
    ) { parent::__construct($request, $apiResponseFactory); }

    /**
     * @param LegacySolarEngineRequest $request
     * @return JsonResponse
     */
    public function getEstimate(LegacySolarEngineRequest $request): JsonResponse
    {
        $params = $request->safe()->collect();

        $zipCode     = $params->get(LegacySolarEngineRequest::FIELD_ZIP_CODE);
        $utilityUuid = $params->get(LegacySolarEngineRequest::FIELD_UTILITY_UUID, null);
        $monthlyBill = intval($params->get(LegacySolarEngineRequest::FIELD_MONTHLY_BILL));

        if($utilityUuid == null) {
            $utilities = $this->utilityRepository->getUtilitiesByZipCode($zipCode);

            $utilityUuid = $utilities->first()->uuid;
        }

        return $this->formatResponse([
            'analysisResult'    => $this->legacySolarEngineRepository->getResult($zipCode, $utilityUuid, $monthlyBill),
            'incentives'        => $this->legacySolarEngineRepository->getIncentivesByUtilityUuid($utilityUuid),
            'medianCostPerWatt' => $this->legacySolarEngineRepository->getSystemCost($zipCode)
        ]);
    }

    /**
     * @param string $zipCode
     * @return JsonResponse
     */
    public function getUtilitiesByZipCode(string $zipCode): JsonResponse
    {
        return $this->getUtilities($zipCode);
    }

    /**
     * @param string $zipCode
     * @return JsonResponse
     */
    public function getFallbackUtilitiesByZipCode(string $zipCode): JsonResponse
    {
        return $this->getUtilities($zipCode, true);
    }

    /**
     * @param string $zipCode
     * @param bool $skipFirstResults
     * @return JsonResponse
     */
    private function getUtilities(string $zipCode, bool $skipFirstResults = false): JsonResponse
    {
        $utilities = $this->utilityRepository->getUtilitiesByZipCode($zipCode, $skipFirstResults);
        return $this->formatResponse($this->utilityTransformer->transformUtilities($utilities));
    }
}
