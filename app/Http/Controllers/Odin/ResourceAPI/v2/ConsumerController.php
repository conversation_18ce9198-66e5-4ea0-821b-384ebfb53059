<?php

namespace App\Http\Controllers\Odin\ResourceAPI\v2;

use App\Enums\ConsumerProductChannel;
use App\Enums\GlobalConfigurationKey;
use App\Enums\Odin\API\FieldClassification;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\Odin\Product;
use App\Http\Requests\Odin\v2\StoreConsumerProductTrackingAndAffiliateRequest;
use App\Http\Requests\Odin\v2\StoreConsumerRequest;
use App\Jobs\ConsumerProcessing\CreateInitialProductJob;
use App\Jobs\QAAutomation\QAAutomationApproveConsumerProductJob;
use App\Jobs\QAAutomation\QAAutomationApproveConsumerProductJobDelay;
use App\Jobs\SaveLegacyAffiliateAndTracking;
use App\Jobs\SendEmailCalculatorResultToConsumer;
use App\Jobs\SendConsumerEmailVerificationEmail;
use App\Models\Odin\Address;
use App\Http\Resources\Odin\ConsumerResource;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ServiceProduct;
use App\Models\Odin\Website;
use App\Models\WatchdogVideo;
use App\Repositories\Legacy\ZipCodeSetRepository;
use App\Repositories\Odin\AddressRepository;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Repositories\Odin\ConsumerRepository;
use App\Repositories\Odin\ServiceProductRepository;
use App\Services\EnginesService;
use App\Services\ConsumerService;
use App\Services\GlobalConfigurationService;
use App\Services\Odin\API\OdinAuthoritativeAPILegacySyncService;
use App\Services\Odin\API\OdinAuthoritativeAPIService;
use App\Services\QAAutomation\QAAutomationService;
use App\Transformers\Odin\v2\EloquentQuoteAffiliateTrackingTransformer;
use App\Transformers\Odin\v2\EloquentQuoteTransformer;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Exception;
use Illuminate\Support\Arr;

/**
 * Odin v2 controller:
 * Handle lead creation directly through Admin2.0 as single source of truth
 * Sync back to legacy where required
 */
class ConsumerController extends BaseAPIController
{
    const RESPONSE_STATUS               = 'status';
    const RESPONSE_UUID                 = 'uuid';
    const RESPONSE_CONSUMER             = 'consumer';
    const RESPONSE_LEGACY_ID            = 'legacy_id';
    const RESPONSE_LEGACY_UUID          = 'legacy_uuid';
    const RESPONSE_ELOQUENT_QUOTE       = 'eloquent_quote';
    const RESPONSE_ELOQUENT_ADDRESS     = 'eloquent_address';
    const RESPONSE_ELOQUENT_LATITUDE    = 'eloquent_latitude';
    const RESPONSE_ELOQUENT_LONGITUDE   = 'eloquent_longitude';

    const string REQUEST_TOKEN = 'token';

    /**
     * @param Request $request
     * @param OdinAuthoritativeAPIService $service
     * @param ConsumerRepository $consumerRepository
     * @param ConsumerProductRepository $consumerProductRepository
     * @param AddressRepository $addressRepository
     * @param EloquentQuoteTransformer $quoteTransformer
     * @param OdinAuthoritativeAPILegacySyncService $legacySyncService
     * @param ZipCodeSetRepository $zipCodeSetRepository
     * @param EnginesService $enginesService
     * @param QAAutomationService $qAAutomationService
     * @param GlobalConfigurationService $globalConfigurationService
     */
    public function __construct(
        Request                             $request,
        OdinAuthoritativeAPIService         $service,
        protected ConsumerRepository        $consumerRepository,
        protected ConsumerProductRepository $consumerProductRepository,
        protected AddressRepository         $addressRepository,
        protected EloquentQuoteTransformer  $quoteTransformer,
        protected OdinAuthoritativeAPILegacySyncService $legacySyncService,
        protected ZipCodeSetRepository      $zipCodeSetRepository,
        protected EnginesService            $enginesService,
        protected QAAutomationService       $qAAutomationService,
        protected GlobalConfigurationService $globalConfigurationService,
    )
    {
        parent::__construct($request, $service);
    }

    /**
     * @param StoreConsumerRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function createConsumer(StoreConsumerRequest $request): JsonResponse
    {
        $payload = $request->all();
        $hasSecondaryServices = !empty($payload[StoreConsumerRequest::SECONDARY_SERVICES] ?? null);

        $industrySlug = $payload[StoreConsumerRequest::INDUSTRY] ?? null;

        if (!$industrySlug) {
            /** @var IndustryService $service */
            $service = IndustryService::query()->where(IndustryService::FIELD_SLUG,  $payload[StoreConsumerRequest::SERVICE])->firstOrFail();

            $industrySlug = $service->industry->slug;
        }

        $industryService = $this->getIndustryService($industrySlug, $payload[StoreConsumerRequest::SERVICE]);

        if (Arr::has($payload, StoreConsumerRequest::CONSUMER_KEY .'.'. GlobalConfigurableFields::ORIGIN->value)) {
            $this->addOriginWebsiteIdToConsumer($payload, Arr::get($payload, StoreConsumerRequest::CONSUMER_KEY .'.'. GlobalConfigurableFields::ORIGIN->value));
        }

        $this->checkPhoneClassification($payload);
        $this->checkPhoneVerification($payload);

        /** @var Consumer $newConsumer */
        $newConsumer = $this->service->create(FieldClassification::CONSUMER->value, $payload, $industryService, true, $hasSecondaryServices);

        if ($request->filled('watchdog_video_id')) {
            \Sentry\configureScope(function (\Sentry\State\Scope $scope): void {
                $scope->setTag('area', 'watchdog');
            });

            WatchdogVideo::updateOrCreate([
                'watchdog_video_id' => $request->get('watchdog_video_id'),
                'consumer_id' => $newConsumer->id,
            ]);
        }


        $this->saveConsumerProductChannel($newConsumer->consumerProducts->first());
        $this->service->saveAppointments($newConsumer, Arr::get($payload, StoreConsumerRequest::CONSUMER_KEY . '.' . StoreConsumerRequest::APPOINTMENTS, []));
        $this->saveAddressUtc($newConsumer);
        $this->service->saveConsumerOptInCompaniesIfRequested($newConsumer, $payload, false);

        if (!Arr::has($payload, StoreConsumerRequest::CONSUMER_KEY . '.' . ConsumerProduct::FIELD_CONTACT_REQUESTS)) {
            $this->consumerProductRepository->setDefaultContactRequestsForMultiIndustryLead($newConsumer);
        }

        $legacyConsumer = $this->quoteTransformer->transform($newConsumer, true);

        $legacyResponse = $this->legacySyncService->post('/consumer/create', $legacyConsumer)?->json();
        $legacyStatus = $legacyResponse[self::RESPONSE_STATUS] ?? null;

        if ($legacyStatus) {
            $newConsumer->update([
                Consumer::FIELD_LEGACY_ID => $legacyResponse[self::RESPONSE_ELOQUENT_QUOTE][self::RESPONSE_LEGACY_ID]
            ]);
            /** @var Address $newAddress */
            $newAddress = $newConsumer->consumerProducts->first()->address;

            $addressUpdate = [Address::FIELD_LEGACY_ID    => $legacyResponse[self::RESPONSE_ELOQUENT_ADDRESS][self::RESPONSE_LEGACY_ID]];
            if (!$newAddress->longitude || !$newAddress->latitude) {
                $addressUpdate = [ ...$addressUpdate,
                    Address::FIELD_LATITUDE  => $legacyResponse[self::RESPONSE_ELOQUENT_ADDRESS][self::RESPONSE_ELOQUENT_LATITUDE] ?? 0.0,
                    Address::FIELD_LONGITUDE => $legacyResponse[self::RESPONSE_ELOQUENT_ADDRESS][self::RESPONSE_ELOQUENT_LONGITUDE] ?? 0.0,
                ];
            }
            $newAddress->update($addressUpdate);

            $this->service->saveSecondaryServices($newConsumer, $payload);

            $this->createInitialQueue($newConsumer, $legacyResponse[self::RESPONSE_ELOQUENT_QUOTE][self::RESPONSE_LEGACY_UUID]);
        }
        else {
            $this->legacySyncService->handleSyncError("Consumer/create failed to sync back to legacy, ID: $newConsumer->id");
        }

        SaveLegacyAffiliateAndTracking::dispatch($newConsumer, $payload);

        // QA Automation entry if SMS verified and initial status for consumer
        $hasMaxContactRequests = !!Arr::get($payload, StoreConsumerRequest::CONSUMER_KEY .'.'. ConsumerProduct::FIELD_CONTACT_REQUESTS, false);
        $this->checkQaAutomation($newConsumer, $hasMaxContactRequests);

        return $this->formatResponse([
            self::RESPONSE_STATUS   => !!$legacyStatus,
            self::RESPONSE_UUID     => $newConsumer->reference
        ]);
    }

    /**
     * @return JsonResponse
     * @throws Exception
     */
    public function updateConsumer(StoreConsumerRequest $request): JsonResponse
    {
        $payload = $this->request->all();
        $baseConsumer = $this->consumerRepository->findByReferenceOrFail($this->request->route(self::RESPONSE_UUID));

        $updatedSecondaryServices = $payload[StoreConsumerRequest::SECONDARY_SERVICES] ?? null;
        if ($updatedSecondaryServices) {
            $this->service->saveSecondaryServices($baseConsumer, $payload);
            $baseConsumer->refresh();
        }

        $serviceSlug = ($payload[StoreConsumerRequest::CONSUMER_KEY] ?? null)
            ? $payload[StoreConsumerRequest::CONSUMER_KEY][StoreConsumerRequest::SERVICE] ?? $payload[StoreConsumerRequest::SERVICE] ?? null
            : $payload[StoreConsumerRequest::SERVICE] ?? null;

        if ($serviceSlug) {
            $industryService = $this->getIndustryService($payload[StoreConsumerRequest::INDUSTRY] ?? '', $serviceSlug);
            if ($industryService->id !== $baseConsumer->consumerProducts->first()?->{ConsumerProduct::RELATION_INDUSTRY_SERVICE}->id) {
                $this->updateIndustryService($baseConsumer, $industryService);
            }
        }
        else {
            $industryService = $baseConsumer->consumerProducts->first()?->{ConsumerProduct::RELATION_INDUSTRY_SERVICE};
        }

        if (!$baseConsumer->phone)
            $this->checkPhoneClassification($payload);

        if (!$baseConsumer->getIsValidPhoneAttribute())
            $this->checkPhoneVerification($payload);

        $success = $this->service->update(FieldClassification::CONSUMER->value, $baseConsumer->id, $payload, $industryService, true, $baseConsumer->hasSecondaryServices());

        $this->service->saveAppointments($baseConsumer, Arr::get($payload, StoreConsumerRequest::CONSUMER_KEY . '.' . StoreConsumerRequest::APPOINTMENTS, []));
        $this->service->saveConsumerOptInCompaniesIfRequested($baseConsumer, $payload, false);

        $legacyConsumer = $this->quoteTransformer->transform($baseConsumer->refresh(), true);

        $legacyResponse = $this->legacySyncService->patch('/consumer/update', $legacyConsumer)?->json();
        if (!($legacyResponse[self::RESPONSE_STATUS] ?? false)) {
            $this->legacySyncService->handleSyncError("Failed to sync consumer/update, ID: $baseConsumer->id");
        }

        SaveLegacyAffiliateAndTracking::dispatch($baseConsumer, $payload);

        // QA Automation entry if SMS verified and initial status for consumer
        $hasMaxContactRequests = !!Arr::get($payload, StoreConsumerRequest::CONSUMER_KEY .'.'. ConsumerProduct::FIELD_CONTACT_REQUESTS, false);
        $this->checkQaAutomation($baseConsumer, $hasMaxContactRequests);

        return $this->formatResponse([
            self::RESPONSE_STATUS   => $success && Arr::get($legacyResponse, self::RESPONSE_STATUS),
        ]);
    }

    /**
     * @param Consumer $consumer
     * @param bool $hasMaxContactRequests
     * @return void
     */
    public function checkQaAutomation(Consumer $consumer, bool $hasMaxContactRequests = false): void
    {
        if ($consumer->status_reason !== 'Duplicate') {
            $consumerProducts = $consumer->consumerProducts;
            foreach ($consumerProducts as $cp) {
                if ($this->qAAutomationService->qualifyConsumerProduct($cp->id)) {
                    if ($hasMaxContactRequests) {
                        QAAutomationApproveConsumerProductJob::dispatch($cp->id);
                    } else {
                        //lock lead to the system - so it doesnt get stolen during delay
                        $this->qAAutomationService->checkNotReservedAndReserveProduct($cp);

                        $qaAutomationConfig = $this->globalConfigurationService->getConfigData(GlobalConfigurationKey::QA_AUTOMATION);
                        $delaySeconds = $qaAutomationConfig[QAAutomationService::MAX_CONTACT_DELAY_SECONDS] ?? 120;
                        QAAutomationApproveConsumerProductJobDelay::dispatch($cp->id)->delay($delaySeconds);
                    }
                }
            }
        }
    }

    /**
     * @param StoreConsumerProductTrackingAndAffiliateRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function attachAffiliate(StoreConsumerProductTrackingAndAffiliateRequest $request, EloquentQuoteAffiliateTrackingTransformer $legacyTransformer): JsonResponse
    {
        $payload = $request->validated();
        $baseConsumer = $this->consumerRepository->findByReferenceOrFail($this->request->route(self::RESPONSE_UUID));
        $industryService = $baseConsumer->consumerProducts->first()?->{ConsumerProduct::RELATION_INDUSTRY_SERVICE} ?? $this->getIndustryService($payload[StoreConsumerRequest::INDUSTRY] ?? '', $payload[StoreConsumerRequest::SERVICE] ?? '');

        $updatedConsumer = $this->service->update(
            FieldClassification::CONSUMER_TRACKING->value,
            $baseConsumer->id,
            $payload,
            $industryService,
            true,
        );

        $legacyPayload = $legacyTransformer->transform($baseConsumer->refresh());
        $legacyResponse = $this->legacySyncService->patch('/consumer/affiliate-tracking', $legacyPayload)?->json();
        if (!($legacyResponse[self::RESPONSE_STATUS] ?? false)) {
            $this->legacySyncService->handleSyncError("Failed to sync consumer/attach-affiliate, ID: $baseConsumer->id");
        }

        return $this->formatResponse([
            self::RESPONSE_STATUS       => $updatedConsumer && ($legacyResponse[self::RESPONSE_STATUS] ?? false)
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getConsumerSummary(): JsonResponse
    {
        $consumer = $this->consumerRepository->findByReferenceOrFail($this->request->route(self::RESPONSE_UUID), true);

        return $this->formatResponse([
            self::RESPONSE_STATUS   => true,
            self::RESPONSE_CONSUMER => new ConsumerResource($consumer)
        ]);
    }

    /**
     * @param string $uuid
     *
     * @return JsonResponse
     */
    public function sendVerificationEmail(string $uuid): JsonResponse
    {
        $consumer = $this->consumerRepository->findByReferenceOrFail($uuid);

        SendConsumerEmailVerificationEmail::dispatch($consumer);

        return $this->formatResponse();
    }


    /**
     * @param ConsumerService $consumerService
     * @param ConsumerRepository $consumerRepository
     *
     * @return JsonResponse
     */
    public function verifyConsumerEmail(ConsumerService $consumerService, ConsumerRepository $consumerRepository): JsonResponse
    {
        $token = $this->request->get(self::REQUEST_TOKEN);

        if (!$token) {
            return response()->json([self::RESPONSE_STATUS => 'fail'], 400);
        }

        $validated = $consumerService->validateConsumerToken($token);

        if (!$validated) {
            return response()->json([self::RESPONSE_STATUS => 'fail'], 400);
        }

        $consumer = $consumerRepository->findByReferenceOrFail($validated[Consumer::FIELD_REFERENCE]);

        if ($consumer->auth->email_verified) {
            return response()->json([self::RESPONSE_STATUS => 'already_verified']);
        }

        $consumer->auth->email_verified = true;
        $consumer->auth->email_verified_at = now();
        $consumer->auth->save();

        return response()->json([self::RESPONSE_STATUS => 'success']);
    }

    /**
     * @param array $payload
     * @return void
     */
    protected function checkPhoneClassification(array &$payload): void
    {
        if (Arr::has($payload, StoreConsumerRequest::CONSUMER_KEY . '.' . Consumer::FIELD_PHONE)
            && Arr::get($payload, StoreConsumerRequest::CONSUMER_KEY . '.' . Consumer::FIELD_PHONE)) {
            Arr::set(
                $payload,
                StoreConsumerRequest::CONSUMER_KEY . '.' . Consumer::FIELD_CLASSIFICATION,
                Consumer::CLASSIFICATION_UNVERIFIED_PHONE
            );
        } else {
            Arr::set(
                $payload,
                StoreConsumerRequest::CONSUMER_KEY . '.' . Consumer::FIELD_CLASSIFICATION,
                Consumer::CLASSIFICATION_EMAIL_ONLY
            );
        }
    }

    /**
     * @param array $payload
     * @return void
     */
    protected function checkPhoneVerification(array &$payload): void
    {
        if (Arr::has($payload, StoreConsumerRequest::CONSUMER_KEY . '.' . StoreConsumerRequest::PHONE_VERIFIED)) {
            $this->setConsumerClassificationFromPhoneStatus($payload);
        }
    }

    protected function updateIndustryService(Consumer $baseConsumer, IndustryService $industryService): bool
    {
        $leadProduct = \App\Models\Odin\Product::query()->where(\App\Models\Odin\Product::FIELD_NAME, Product::LEAD)->firstOrFail();
        $serviceProduct = ServiceProduct::query()->where([
            ServiceProduct::FIELD_INDUSTRY_SERVICE_ID   => $industryService->id,
            ServiceProduct::FIELD_PRODUCT_ID            => $leadProduct->id,
            ])->first();

        return $serviceProduct && $baseConsumer->consumerProducts()->first()?->update([ConsumerProduct::FIELD_SERVICE_PRODUCT_ID => $serviceProduct->id]);
    }

    /**
     * @param array $payload
     *
     * @return void
     */
    protected function setConsumerClassificationFromPhoneStatus(array &$payload): void
    {
        $phoneVerified = Arr::get($payload, StoreConsumerRequest::CONSUMER_KEY . '.' . StoreConsumerRequest::PHONE_VERIFIED, false);
        $classification = Consumer::CLASSIFICATION_UNVERIFIED_PHONE;

        if ($phoneVerified) $classification = Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_SMS;

        Arr::set(
            $payload,
            StoreConsumerRequest::CONSUMER_KEY . '.' . Consumer::FIELD_CLASSIFICATION,
            $classification
        );
    }

    protected function addOriginWebsiteIdToConsumer(array &$payload, string $originKey): void
    {
        /** @var int $websiteId */
        $websiteId = Website::query()
            ->where(Website::FIELD_ABBREVIATION, $originKey)
            ->first()
            ?->id;

        if ($websiteId) Arr::set(
            $payload,
            StoreConsumerRequest::CONSUMER_KEY .'.'. Consumer::FIELD_WEBSITE_ID,
            $websiteId
        );
    }

    /**
     * @param Consumer $consumer
     *
     * @return void
     */
    protected function saveAddressUtc(Consumer $consumer): void
    {
        if (!$consumer->consumerProducts->first()?->address) return;

        /** @var Address $address */
        $address = $consumer->consumerProducts->first()->address;

        $address->utc = $this->zipCodeSetRepository->getUtc($address->zip_code);

        $address->save();
    }

    /**
     * @param Consumer $consumer
     * @param string $leadReference
     *
     * @return void
     */
    protected function createInitialQueue(Consumer $consumer, string $leadReference): void
    {
        if ($consumer->classification === Consumer::CLASSIFICATION_EMAIL_ONLY) {
            return;
        }

        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = $consumer->consumerProducts->first();

        if ($this->industryServiceHasDirectLeadsProductEnabled($consumerProduct->serviceProduct)) {
            //Delay for 5 minutes to confirm if the lead is a Direct Leads (the consumer should have given consent to companies, if available, by this time).
            CreateInitialProductJob::dispatch($consumerProduct->id)->delay(5 * 60);
        } else {
            CreateInitialProductJob::dispatch($consumerProduct->id);
        }
    }

    /**
     * @param ServiceProduct $serviceProduct
     *
     * @return bool
     */
    protected function industryServiceHasDirectLeadsProductEnabled(ServiceProduct $serviceProduct): bool
    {
        /** @var ServiceProductRepository $repository */
        $repository = app(ServiceProductRepository::class);

        return !!$repository->getServiceProductByProductAndIndustryService($serviceProduct->industry_service_id, Product::DIRECT_LEADS);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     *
     * @return void
     */
    protected function saveConsumerProductChannel(ConsumerProduct $consumerProduct): void
    {
        $consumerProduct->channel = ConsumerProductChannel::DIRECT_FROM_CALCULATORS;
        $consumerProduct->save();
    }

    /**
     * @return JsonResponse
     */
    public function sendCalculatorResultsToConsumer(): JsonResponse
    {
        $payload = $this->request->all();

        $baseConsumer = $this->consumerRepository->findByReferenceOrFail($this->request->route(self::RESPONSE_UUID));

        $engine = $this->enginesService->checkEngineOutputExistsInConsumerPayload($payload);
        if($engine)
            SendEmailCalculatorResultToConsumer::dispatch($baseConsumer, $payload, $engine);

        return $this->formatResponse([
            self::RESPONSE_STATUS => true
        ]);
    }
}
