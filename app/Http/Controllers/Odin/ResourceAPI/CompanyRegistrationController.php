<?php

namespace App\Http\Controllers\Odin\ResourceAPI;

use App\Enums\Odin\API\FieldClassification;
use App\Enums\Odin\API\FieldType;
use App\Events\CompanyContractSigned;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Repositories\Odin\CompanyRepository;
use App\Services\Odin\API\OdinAPIService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Throwable;

class CompanyRegistrationController extends BaseAPIController
{
    const REQUEST_USER              = 'user';
    const REQUEST_COMPANY           = 'company';
    const REQUEST_LEGACY_ID         = 'legacy_id';
    const REQUEST_ADDRESS           = 'address';
    const REQUEST_COMPANY_LOCATION  = 'company_location';
    const LEGACY_COMPANY_TYPE       = 'type';
    const DEFAULT_LEGACY_TYPE       = 'installer';

    public function __construct(
        Request $request,
        OdinAPIService $service,
        protected CompanyRepository $companyRepository
    )
    {
        parent::__construct($request, $service);
    }

    /**
     * Handles creating the base company.
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function createBaseCompany(): JsonResponse
    {
        $this->validate(
            $this->request,
            [
                self::REQUEST_COMPANY   => 'required|array',
                self::REQUEST_USER      => 'required|array',
                self::REQUEST_LEGACY_ID => 'numeric',
            ]
        );

        $legacyId = $this->request->get(self::REQUEST_LEGACY_ID, null);
        $user     = $this->request->get(self::REQUEST_USER);
        $company  = $this->request->get(self::REQUEST_COMPANY);

        $company[Company::FIELD_ID] = [
            'key' => Company::FIELD_ID,
            'value' => $legacyId,
            'type' => FieldType::MODEL->value,
            'classification' => FieldClassification::COMPANY->value,
        ];

        /** @var ?Company $companyExists */
        $companyExists = Company::query()
            ->where(Company::FIELD_LEGACY_ID, $legacyId)
            ->first();

        if ($companyExists) {
            return $this->handleDuplicateRegistrationCompany($companyExists, $user);
        }

        $companySuccess = $this->service->create($company);

        if($companySuccess && isset($user["company_id"])) {
            $company = $this->companyRepository->getCompanyByLegacyIdOrFail($legacyId);
            $user["company_id"]["value"] = $company->{Company::FIELD_ID};
        }

        return $this->formatResponse([
            "status" => $this->service->create($user) && $companySuccess,
        ]);
    }

    /**
     * Handles create a company Location
     * @param CompanyRepository $repository
     * @return JsonResponse
     * @throws \Illuminate\Validation\ValidationException
     */
    public function createCompanyLocation(): JsonResponse
    {
        $this->validate(
            $this->request,
            [
                self::REQUEST_ADDRESS          => 'required|array',
                self::REQUEST_COMPANY_LOCATION  => 'required|array',
            ]
        );

        $companyLocation = $this->request->get(self::REQUEST_COMPANY_LOCATION);
        $address = $this->request->get(self::REQUEST_ADDRESS);

        $locationSuccess = $this->service->create($address);

        return $this->formatResponse([
            "status" => $this->service->create($companyLocation) && $locationSuccess
        ]);
    }

    /**
     * Find a Company by legacy ID, then update the model/field types
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    protected function updateCompanyByLegacyId(): JsonResponse
    {

        $this->validate(
            $this->request,
            [
                self::REQUEST_COMPANY => 'required|array',
                self::REQUEST_LEGACY_ID => 'required|numeric',
            ]
        );

        $companyUpdateData = $this->request->get(self::REQUEST_COMPANY);
        $legacyId = $this->request->get(self::REQUEST_LEGACY_ID);

        $company = $this->companyRepository->getCompanyByLegacyIdOrFail($legacyId);
        $companyUpdated = $this->service->update($company->{Company::FIELD_ID}, $companyUpdateData);
        $hasNoService = !count($company->{Company::RELATION_SERVICES});

        if ($companyUpdated && (key_exists(self::LEGACY_COMPANY_TYPE, $companyUpdateData) || $hasNoService)) {
            $companyType = $hasNoService
                ? self::DEFAULT_LEGACY_TYPE
                : $companyUpdateData[self::LEGACY_COMPANY_TYPE]['value'];
            try {
                $this->companyRepository->updateIndustryAndServiceRelationsFromLegacyCompanyType($company, $companyType);
            }
            catch(Throwable $err) {
                logger()->warning("Error creating Industries / Industry Services for Company id '{$company->{Company::FIELD_ID}}'\n\t".$err->getMessage());
            }
        }

        return $this->formatResponse([
            "status" => $companyUpdated,
        ]);
    }

    /**
     * Handles update of Company Status after accepting the contract agreement in www registration
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function acceptLeadContract(): JsonResponse
    {
        $legacyId = $this->request->get(self::REQUEST_LEGACY_ID);

        $company = $this->companyRepository->getCompanyByLegacyIdOrFail($legacyId);

        CompanyContractSigned::dispatch($company->id);

        return $this->updateCompanyByLegacyId();
    }

    /**
     * Handles update of Company Details during Company registration
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function updateCompanyDetails(): JsonResponse
    {
        return $this->updateCompanyByLegacyId();
    }

    /**
     * Handles update of company logo
     *
     * @param CompanyRepository $repository
     * @return JsonResponse
     * @throws \Illuminate\Validation\ValidationException
     */
    public function addCompanyLogo(CompanyRepository $repository): JsonResponse
    {
        $this->validate(
            $this->request,
            [
                self::REQUEST_COMPANY => 'required|array',
                self::REQUEST_LEGACY_ID => 'required|numeric',
            ]
        );

        $company = $this->request->get(self::REQUEST_COMPANY);
        $legacyId = $this->request->get(self::REQUEST_LEGACY_ID);
        $odinId = $repository->getCompanyByLegacyIdOrFail($legacyId)?->id;

        return $this->formatResponse([
            "status" => $this->service->update($odinId, $company),
        ]);

    }

    /**
     * Handle duplicate company registrations being requested from Legacy registration
     * Check if the CompanyUser exists in case the previous sync failed part way through
     * @param Company $company
     * @param array $userData
     * @return JsonResponse
     */
    private function handleDuplicateRegistrationCompany(Company $company, array $userData): JsonResponse
    {
        $email = isset($userData["email"])
            ? $userData["email"]["value"] ?? null
            : null;

        if ($email) {
            $userAlreadyCreated = $company->users()
                ->where(CompanyUser::FIELD_EMAIL, $email)
                ->first();

            if (!$userAlreadyCreated) {
                $user["company_id"]["value"] = $company->{Company::FIELD_ID};
                if ($this->service->create($user)) {
                    $userName = $user["first_name"]["value"] ?? "???";
                    logger()->warning("Odin/v1 - a sync event for a Company with LegacyID $company->legacy_id was received. The ID is already taken, but a new CompanyUser for '$userName' has been created on this Company.");
                }
            }
        }

        return $this->formatResponse([
            'status'    => true
        ]);
    }
}
