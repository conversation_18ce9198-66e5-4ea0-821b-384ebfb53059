<?php

namespace App\Http\Controllers\Odin\ResourceAPI;

use App\Http\Controllers\Controller;
use App\Services\Odin\API\OdinAPIService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class BaseAPIController extends Controller
{
    public function __construct(
        protected Request $request,
        protected OdinAPIService $service
    ) {}

    /**
     * <PERSON><PERSON> formatting the data as an accepted JsonResponse.
     *
     * @param array $data
     * @return JsonResponse
     */
    protected function formatResponse(array $data = []): JsonResponse
    {
        return response()->json(["data" => $data]);
    }
}
