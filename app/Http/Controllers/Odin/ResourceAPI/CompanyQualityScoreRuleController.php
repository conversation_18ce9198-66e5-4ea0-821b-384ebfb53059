<?php

namespace App\Http\Controllers\Odin\ResourceAPI;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\CompanyQualityScoreRules\SaveCompanyQualityScoreRuleRequest;
use App\Http\Requests\CompanyQualityScoreRules\TestCompanyQualityScoreRuleRequest;
use App\Http\Resources\Odin\CompanyQualityScoreRuleResource;
use App\Models\Odin\CompanyQualityScoreRule;
use App\Repositories\Odin\CompanyQualityScoreRuleRepository;
use App\Services\Odin\CompanyQualityScoreRuleService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Exception;

class CompanyQualityScoreRuleController extends APIController
{
    public function __construct(
        Request                                                   $request,
        JsonAPIResponseFactory                                    $apiResponseFactory,
        protected readonly CompanyQualityScoreRuleRepository      $companyQualityScoreRuleRepository,
        protected readonly CompanyQualityScoreRuleService         $companyQualityScoreRuleService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * List all company score rules
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $companyQualityScoreRules = $this->companyQualityScoreRuleRepository->getAll();

        return $this->formatResponse([
            "status" => true,
            "company_quality_score_rules" => CompanyQualityScoreRuleResource::collection($companyQualityScoreRules)
        ]);
    }

    /**
     * Store a company quality score rule
     * @param SaveCompanyQualityScoreRuleRequest $request
     * @return JsonResponse
     */
    public function store(SaveCompanyQualityScoreRuleRequest $request): JsonResponse
    {
        $payload = $request->validated();

        $companyQualityScoreRule = $this->companyQualityScoreRuleRepository->store($payload);

        return $this->formatResponse([
            "status" => true,
            "company_quality_score_rule" => new CompanyQualityScoreRuleResource($companyQualityScoreRule)
        ]);
    }

    /**
     * Update a company quality score rule
     * @param SaveCompanyQualityScoreRuleRequest $request
     * @param CompanyQualityScoreRule $companyQualityScoreRule
     * @return JsonResponse
     */
    public function update(SaveCompanyQualityScoreRuleRequest $request, CompanyQualityScoreRule $companyQualityScoreRule): JsonResponse
    {
        $payload = $request->validated();

        $status = $this->companyQualityScoreRuleRepository->update($companyQualityScoreRule, $payload);

        return $this->formatResponse([
            "status" => $status
        ]);
    }

    /**
     * Delete company quality score rule
     * @param CompanyQualityScoreRule $companyQualityScoreRule
     * @return JsonResponse
     * @throws Exception
     */
    public function delete(CompanyQualityScoreRule $companyQualityScoreRule): JsonResponse
    {
        $status = $this->companyQualityScoreRuleRepository->delete($companyQualityScoreRule);

        return $this->formatResponse([
            "status" => $status
        ]);
    }

    /**
     * Test ruleset
     * @param TestCompanyQualityScoreRuleRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function testRule(TestCompanyQualityScoreRuleRequest $request): JsonResponse
    {
        $payload = $request->validated();

        $result = $this->companyQualityScoreRuleService->testRule(
            $payload[TestCompanyQualityScoreRuleRequest::FIELD_COMPANY_IDS],
            $payload[TestCompanyQualityScoreRuleRequest::FIELD_RULESET],
        );

        return $this->formatResponse([
            "status" => true,
            "result" => $result
        ]);
    }


    /**
     * Trigger the calculation of the quality score for companies given a configuration
     * @param CompanyQualityScoreRule $companyQualityScoreRule
     * @return JsonResponse
     */
    public function trigger(CompanyQualityScoreRule $companyQualityScoreRule): JsonResponse
    {
        $result = $this->companyQualityScoreRuleService->trigger(
            $companyQualityScoreRule
        );

        return $this->formatResponse([
            "status" => $result,
        ]);
    }
}

