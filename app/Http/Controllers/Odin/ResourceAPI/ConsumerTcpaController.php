<?php

namespace App\Http\Controllers\Odin\ResourceAPI;

use App\Repositories\Odin\ConsumerRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class ConsumerTcpaController extends BaseAPIController
{
    const REQUEST_CONSUMER_TCPA_RECORD = 'tcpa_record';

    /**
     * <PERSON><PERSON> creating the Consumer Tracking model
     *
     * @param ConsumerRepository $consumerRepository
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function createTcpaRecord(): JsonResponse
    {
        $this->validate(
            $this->request,
            [
                self::REQUEST_CONSUMER_TCPA_RECORD => 'required|array'
            ]
        );

        $consumerTcpaRecord = $this->request->get(self::REQUEST_CONSUMER_TCPA_RECORD);

        return $this->formatResponse([
            "status" => $this->service->create($consumerTcpaRecord),
        ]);

    }
}
