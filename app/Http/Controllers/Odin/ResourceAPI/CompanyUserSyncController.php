<?php

namespace App\Http\Controllers\Odin\ResourceAPI;

use App\Models\Odin\CompanyUser;
use App\Repositories\Odin\CompanyRepository;
use App\Repositories\Odin\CompanyUserRepository;
use App\Services\Odin\API\OdinAPIService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class CompanyUserSyncController extends BaseAPIController
{
    const REQUEST_COMPANY_USER      = 'company_user';
    const REQUEST_REFERENCE         = 'reference';
    const REQUEST_LEGACY_ID         = 'legacy_id';

    /**
     * @param Request $request
     * @param OdinAPIService $service
     * @param CompanyUserRepository $userRepository
     * @param CompanyRepository $companyRepository
     */
    public function __construct(
        Request                     $request,
        OdinAPIService              $service,
        protected CompanyUserRepository $userRepository,
        protected CompanyRepository $companyRepository
    )
    {
        parent::__construct($request, $service);
    }

    /**
     * When a contact is created on A2, then synced backward to SR,
     * <PERSON> will reply with the ID it used for the EloquentCompanyContact model
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function assignLegacyFieldsAfterBackwardSync(): JsonResponse
    {
        $this->validate($this->request, [
            self::REQUEST_COMPANY_USER  => ['required', 'array'],
            self::REQUEST_REFERENCE     => ['required', 'string', Rule::exists(CompanyUser::TABLE, CompanyUser::FIELD_REFERENCE)]
        ]);

        $data      = $this->request->get(self::REQUEST_COMPANY_USER);
        $reference = $this->request->get(self::REQUEST_REFERENCE);

        /** @var CompanyUser $companyUser */
        $companyUser = $this->userRepository->getCompanyUserByReference($reference);

        return $this->formatResponse([
            'status' => $this->service->update($companyUser->{CompanyUser::FIELD_ID}, $data)
        ]);
    }

    /**
     * Assign legacy ID only via direct attribute, not using Odin Field models
     * @return JsonResponse
     * @throws ValidationException
     */
    public function syncLegacyId(): JsonResponse
    {
        $this->validate($this->request, [
            self::REQUEST_LEGACY_ID         => 'required|numeric',
            self::REQUEST_REFERENCE         => 'required|string',
        ]);
        $legacyId  = $this->request->get(self::REQUEST_LEGACY_ID);
        $reference = $this->request->get(self::REQUEST_REFERENCE);

        $companyUser = $this->userRepository->getCompanyUserByReference($reference);

        if (!$companyUser->{CompanyUser::FIELD_ID} || !$legacyId) {
            return $this->formatResponse([
                'success'   => false,
                'message'   => "Sync job failed - User not found, or bad legacy id '{$legacyId}'"
            ]);
        }

        logger()->info("Assigning legacy id $legacyId to user {$reference}");
        $success = $companyUser->update([ CompanyUser::FIELD_LEGACY_ID  => $legacyId ]);

        return $this->formatResponse([
            'status'    => $success
        ]);
    }

}
