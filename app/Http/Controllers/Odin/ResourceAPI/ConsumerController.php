<?php

namespace App\Http\Controllers\Odin\ResourceAPI;

use App\Models\Legacy\EloquentQuote;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Repositories\Odin\AddressRepository;
use App\Repositories\Odin\ConsumerProductDataRepository;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Repositories\Odin\ConsumerRepository;
use App\Repositories\Odin\ProductAssignmentRepository;
use App\Services\Odin\API\OdinAPIService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class ConsumerController extends BaseAPIController
{
    const REQUEST_CONSUMER           = 'consumer';
    const REQUEST_ADDRESS            = 'address';
    const REQUEST_AFFILIATE          = 'affiliate';
    const REQUEST_ROOF_DETAILS       = 'roof_details';
    const REQUEST_LEGACY_ADDRESS_ID  = 'legacy_address_id';
    const REQUEST_LEGACY_LEAD_ID     = 'legacy_lead_id';
    const REQUEST_LEGACY_ID          = 'legacy_id';
    const REQUEST_LEGACY_LEAD_REFERENCE   = 'reference';

    /**
     * @param Request $request
     * @param OdinAPIService $service
     * @param ConsumerRepository $consumerRepository
     * @param ConsumerProductRepository $consumerProductRepository
     * @param AddressRepository $addressRepository
     * @param ProductAssignmentRepository $productAssignmentRepository
     */
    public function __construct(
        Request                               $request,
        OdinAPIService                        $service,
        protected ConsumerRepository          $consumerRepository,
        protected ConsumerProductRepository   $consumerProductRepository,
        protected AddressRepository           $addressRepository,
        protected ProductAssignmentRepository $productAssignmentRepository,
    ) { parent::__construct($request, $service); }

    /**
     * @return JsonResponse
     * @throws ValidationException
     */
    public function createConsumer(): JsonResponse
    {
        $this->validate($this->request, [
            self::REQUEST_CONSUMER          => 'required|array',
            self::REQUEST_ADDRESS           => 'required|array',
            self::REQUEST_ROOF_DETAILS      => 'array',
            self::REQUEST_LEGACY_ADDRESS_ID => 'integer',
            self::REQUEST_LEGACY_LEAD_ID    => 'integer'
        ]);

        $legacyAddressId  = $this->request->get(self::REQUEST_LEGACY_ADDRESS_ID);
        $legacyConsumerId = $this->request->get(self::REQUEST_LEGACY_LEAD_ID);
        $consumer         = $this->request->get(self::REQUEST_CONSUMER);
        $address          = $this->request->get(self::REQUEST_ADDRESS);
        $roofDetails      = $this->request->get(self::REQUEST_ROOF_DETAILS);
        $consumer         = $roofDetails ? array_merge($consumer, $roofDetails) : $consumer;
        $addressStatus    = $this->service->create($address);
        $consumerStatus   = $this->service->create($consumer);

        if ($addressStatus && $consumerStatus && $legacyAddressId && $legacyConsumerId) {
            $address  = $this->addressRepository->findByLegacyIdOrFail($legacyAddressId);
            $consumer = $this->consumerRepository->findByLegacyIdOrFail($legacyConsumerId);

            /** @var ConsumerProduct $consumerProduct */
            $consumerProduct = $consumer->consumerProducts()->latest()->first();

            $consumerProduct->address_id       = $address->id;
            $consumerProduct->contact_requests = $consumer->max_contact_requests;

            $consumerProduct->save();
        }

        return $this->formatResponse([
            'status' => $addressStatus && $consumerStatus
        ]);
    }

    /**
     * @return JsonResponse
     * @throws ValidationException
     */
    public function createAffiliateRecord(): JsonResponse
    {
        $this->validate($this->request, [
            self::REQUEST_AFFILIATE => 'required|array'
        ]);

        return $this->formatResponse([
            'status' => $this->service->create($this->request->get(self::REQUEST_AFFILIATE))
        ]);
    }

    /**
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getConsumerId(): JsonResponse
    {
        $this->validate($this->request, [
            self::REQUEST_LEGACY_ID => 'required|integer',
        ]);
        $consumer = $this->consumerRepository->findByLegacyIdOrFail($this->request->get(self::REQUEST_LEGACY_ID));
        return $this->formatResponse([
            'status' => true,
            'id' => $consumer->{Consumer::FIELD_ID},
            'consumer_product_id' => $consumer->consumerProducts()->latest()->first()->{ConsumerProduct::FIELD_ID},
            'reference' => $consumer->{Consumer::FIELD_REFERENCE}
        ]);
    }

    /**
     * @param ConsumerProductDataRepository $productDataRepository
     * @param ConsumerProductRepository $productRepository
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getConsumerProductDetails(ConsumerProductDataRepository $productDataRepository, ConsumerProductRepository $productRepository): JsonResponse
    {
        $this->validate($this->request, [
            self::REQUEST_LEGACY_LEAD_REFERENCE => 'required|string'
        ]);

        /** @var EloquentQuote $lead */
        $lead = EloquentQuote::query()->where(EloquentQuote::REFERENCE, $this->request->get(self::REQUEST_LEGACY_LEAD_REFERENCE))->firstOrFail();

        $consumerProduct = $productRepository->getConsumerProductByLegacyLeadId($lead->quoteid);

        return $this->formatResponse([
            'status' => true,
            'industry' => $consumerProduct->serviceProduct->service->industry->name,
            'service' => $consumerProduct->serviceProduct->service->name,
            'industry_data' => $productDataRepository->getConsumerProductDataForCompany($consumerProduct)
        ]);
    }

    /**
     * @param int $leadId
     * @return JsonResponse
     */
    public function getNextAllocationTime(int $leadId): JsonResponse
    {
        $consumer = $this->consumerRepository->findByLegacyIdOrFail($leadId);

        return $this->formatResponse([
            'status' => true,
            'next_allocation_time' => $this->consumerRepository->getNextAllocationTime($consumer)
        ]);
    }
}
