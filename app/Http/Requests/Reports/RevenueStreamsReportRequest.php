<?php

namespace App\Http\Requests\Reports;

use App\Enums\PermissionType;
use App\Models\User;
use App\Rules\OrderFormat;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class RevenueStreamsReportRequest extends FormRequest
{
    const string FIELD_DATE_RANGE = 'date_range';
    const string FIELD_FILTERS    = 'filters';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();
        return $user->hasPermissionTo(PermissionType::REVENUE_STREAMS_REPORT_VIEW->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::FIELD_DATE_RANGE => ['nullable', 'array'],
            self::FIELD_FILTERS => ['nullable', 'string'],
        ];
    }

    /**
     * @return array
     */
    public function getFilters(): mixed
    {
        return $this->input(self::FIELD_FILTERS) ? json_decode($this->input(self::FIELD_FILTERS)) : [];
    }

}
