<?php

namespace App\Http\Requests\Reports;

use App\Enums\LeadsReport\LeadsReportColumnEnum;
use App\Enums\LeadsReport\LeadsReportGroupEnum;
use App\Enums\Odin\Industry;
use App\Enums\Reports\CountyCoverageReport\CountyCoverageReportColumnEnum;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CountyCoverageReportRequest extends FormRequest
{
    const string INDUSTRY   = 'industry';
    const string FILTERS    = 'filters';
    const string COLUMNS    = 'columns';
    const string COMPANY    = 'company';
    const string CAMPAIGNS  = 'campaigns';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::INDUSTRY  => ['required', 'string', Rule::in(array_keys(Industry::slugsList()))],
            self::FILTERS   => ['string'],
            self::COLUMNS   => ['array'],
            self::COMPANY   => ['numeric', 'nullable'],
            self::CAMPAIGNS => ['array', 'nullable'],
        ];
    }

    /**
     * @return array
     */
    public function getColumns(): array
    {
        return $this->input(self::COLUMNS) ?
            array_map(function($columnString) { return CountyCoverageReportColumnEnum::from($columnString); }, $this->input(self::COLUMNS)) :
            CountyCoverageReportColumnEnum::getDefaultColumns();
    }

    /**
     * @return array
     */
    public function getFilters(): mixed
    {
        return $this->input(self::FILTERS) ? json_decode($this->input(self::FILTERS)) : [];
    }

    /**
     * @return Industry
     */
    public function getIndustry(): Industry
    {
        return $this->input(self::INDUSTRY) ? Industry::fromSlug($this->input(self::INDUSTRY)) : Industry::SOLAR;
    }
}
