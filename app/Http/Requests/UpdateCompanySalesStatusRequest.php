<?php

namespace App\Http\Requests;

use App\Enums\CompanySalesStatus;
use App\Models\Odin\Company;
use App\Repositories\Odin\CompanyRepository;
use App\Services\UserAuthorizationService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateCompanySalesStatusRequest extends FormRequest
{
    const REQUEST_COMPANY_ID = 'company_id';

    public function __construct(
        protected UserAuthorizationService $userAuthorizationService,
        protected CompanyRepository $companyRepository,
        array $query = [],
        array $request = [],
        array $attributes = [],
        array $cookies = [],
        array $files = [],
        array $server = [],
              $content = null)
    {
        parent::__construct($query, $request, $attributes, $cookies, $files, $server, $content);
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return
            [
                Company::FIELD_SALES_STATUS => ['required', new Enum(CompanySalesStatus::class)],
            ];
    }
}
