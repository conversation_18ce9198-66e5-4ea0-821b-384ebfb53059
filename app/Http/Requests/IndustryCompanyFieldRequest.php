<?php

namespace App\Http\Requests;

use App\Enums\Odin\CompanyConfigurableFieldCategory;
use App\Http\Controllers\API\IndustryManagement\IndustryManagementAPIController;
use App\Models\Odin\ConfigurableFieldType;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryCompanyField;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rules\Enum;

class IndustryCompanyFieldRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo('industry-management');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return
            [
                IndustryManagementAPIController::REQUEST_INDUSTRY =>
                    [
                        "required",
                        "in:" . implode(',', Industry::query()->distinct()->pluck(Industry::FIELD_ID)->toArray())
                    ],
                IndustryManagementAPIController::REQUEST_INDUSTRY_COMPANY_FIELD =>
                    [
                        "nullable",
                        "in:" . implode(',', IndustryCompanyField::query()->distinct()->pluck(IndustryCompanyField::FIELD_ID)->toArray())
                    ],
                IndustryCompanyField::FIELD_NAME =>
                    [
                        "required",
                        "string",
                        "max:255"
                    ],
                IndustryCompanyField::FIELD_KEY =>
                    [
                        "required",
                        "string",
                        "max:255"
                    ],
                IndustryCompanyField::FIELD_TYPE =>
                    [
                        "required",
                        "in:" . implode(',', ConfigurableFieldType::query()->distinct()->pluck(ConfigurableFieldType::FIELD_ID)->toArray())
                    ],
                IndustryCompanyField::FIELD_SHOW_ON_PROFILE =>
                    [
                        "required",
                        "bool"
                    ],
                IndustryCompanyField::FIELD_SHOW_ON_DASHBOARD =>
                    [
                        "required",
                        "bool"
                    ],
                IndustryCompanyField::FIELD_PAYLOAD =>
                    [
                        "array"
                    ],
                IndustryCompanyField::FIELD_CATEGORY => [
                    'required',
                    new Enum(CompanyConfigurableFieldCategory::class)
                ]
            ];
    }

    /**
     * @return void
     */
    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                IndustryManagementAPIController::REQUEST_INDUSTRY               => $this->route()->parameter(IndustryManagementAPIController::REQUEST_INDUSTRY),
                IndustryManagementAPIController::REQUEST_INDUSTRY_COMPANY_FIELD => $this->route()->parameter(IndustryManagementAPIController::REQUEST_INDUSTRY_COMPANY_FIELD)
            ]
        );
    }
}

