<?php

namespace App\Http\Requests;

use App\Http\Controllers\API\Users\UserBaseAPIController;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class SearchUsersRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(UserBaseAPIController::MODULE_PERMISSION);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            UserBaseAPIController::REQUEST_SEARCH_QUERY      => ['required', 'string'],
            UserBaseAPIController::REQUEST_FILTER_BASIC_INFO => ['required', 'boolean']
        ];
    }
}
