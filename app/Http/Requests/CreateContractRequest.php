<?php

namespace App\Http\Requests;

use App\Enums\PermissionType;
use App\Http\Controllers\API\ContractManagementController;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class CreateContractRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::CONTRACT_MANAGEMENT_EDIT->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            ContractManagementController::REQUEST_FIELD_FILE    => 'required|file|mimes:pdf|max:10000',
            ContractManagementController::REQUEST_FIELD_TITLE   => 'required|string',
            ContractManagementController::REQUEST_FIELD_SUBJECT => 'required|string',
            ContractManagementController::REQUEST_FIELD_MESSAGE => 'required|string',
        ];
    }

}