<?php

namespace App\Http\Requests;

use App\Http\Controllers\API\CompaniesController;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class GetCampaignBudgetUsageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'page'        => 'nullable|numeric|min:1',
            'perPage'     => 'nullable|numeric|min:1|max:100',
            'sortBy'      => 'nullable|string',
            'sortDir'     => 'nullable|' . Rule::in('asc', 'desc')
        ];
    }
}
