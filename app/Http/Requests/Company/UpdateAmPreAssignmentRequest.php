<?php

namespace App\Http\Requests\Company;

use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UpdateAmPreAssignmentRequest extends FormRequest
{
    const string REQUEST_AM_ID = 'amId';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::user()->hasPermissionTo(PermissionType::COMPANY_PRE_ASSIGN_AM->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::REQUEST_AM_ID => [
                'nullable',
                'exists' => Rule::exists(User::TABLE, User::FIELD_ID)
            ]
        ];
    }
}
