<?php

namespace App\Http\Requests\Company;

use App\Enums\RoleType;
use App\Models\Odin\Company;
use App\Models\User;
use App\Services\UserAuthorizationService;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateCompanyManagerRequest extends FormRequest
{
    const string REQUEST_MANAGER_ID = 'manager_id';
    const string REQUEST_COMMISSIONABLE_FROM = 'commissionable_from';
    const string REQUEST_COMMISSIONABLE_TO = 'commissionable_to';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $authorizationService = app(UserAuthorizationService::class);
        $company = Company::query()->findOrFail($this->route('company_id'));

        return match ($this->route('roleType')) {
            RoleType::CUSTOMER_SUCCESS_MANAGER => $authorizationService->canEditSuccessManager($company),
            RoleType::BUSINESS_DEVELOPMENT_MANAGER => $authorizationService->canEditBusinessDevelopmentManager($company),
            RoleType::ACCOUNT_MANAGER => $authorizationService->canEditAccountManager($company),
            RoleType::ONBOARDING_MANAGER => $authorizationService->canEditOnboardingManager($company),
            RoleType::SALES_DEVELOPMENT_REPRESENTATIVE => $authorizationService->canEditSalesDevelopmentRepresentative($company),
            default => false
        };
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::REQUEST_MANAGER_ID => ['nullable', Rule::exists(User::TABLE, User::FIELD_ID)],
            self::REQUEST_COMMISSIONABLE_FROM => ['nullable', 'date'],
            self::REQUEST_COMMISSIONABLE_TO => ['nullable', 'date'],
        ];
    }
}
