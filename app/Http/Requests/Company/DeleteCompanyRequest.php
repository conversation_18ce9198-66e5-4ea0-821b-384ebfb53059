<?php

namespace App\Http\Requests\Company;

use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class DeleteCompanyRequest extends FormRequest
{
    const string REQUEST_DELETION_REASON = 'reason';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::COMPANY_DELETE->value);
    }


    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            self::REQUEST_DELETION_REASON => ['nullable', 'string'],
        ];
    }
}
