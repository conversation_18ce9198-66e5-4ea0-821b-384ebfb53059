<?php

namespace App\Http\Requests;

use App\Enums\PermissionType;
use App\Models\GlobalConfiguration;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreGlobalConfigurationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();
        return $user->hasPermissionTo(PermissionType::GLOBAL_CONFIGURATIONS_EDIT->value);
    }

    /**
     * Mutate data before passing to validation
     *
     * @return void
     */
    public function prepareForValidation(): void
    {
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $configuration = $this->route()->parameter('configuration');
        $unique = Rule::unique(GlobalConfiguration::TABLE, GlobalConfiguration::FIELD_CONFIGURATION_KEY);

        if ($configuration instanceof GlobalConfiguration) $unique->ignore($configuration->id);

        return
            [
                GlobalConfiguration::FIELD_CONFIGURATION_PAYLOAD => [
                    'required'
                ],
                GlobalConfiguration::FIELD_CONFIGURATION_KEY => [
                    'required',
                    'string',
                    $unique
                ]
            ];
    }

}
