<?php

namespace App\Http\Requests\Notes;

use App\Enums\Notes\NoteRelationType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class GetNotesRequest extends FormRequest
{
    const FIELD_RELATION_ID   = 'relation_id';
    const FIELD_RELATION_TYPE = 'relation_type';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            self::FIELD_RELATION_ID   => ['required', 'int'],
            self::FIELD_RELATION_TYPE => ['required', 'string', Rule::in(NoteRelationType::values())],
        ];
    }
}
