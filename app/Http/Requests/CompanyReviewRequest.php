<?php

namespace App\Http\Requests;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyReview;
use App\Models\User;
use App\Rules\Phone;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class CompanyReviewRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo('industry-management');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return
            [
                CompanyReview::FIELD_COMPANY_ID =>
                    [
                        "required",
                        "integer",
                        "in:" . implode(',', Company::query()->distinct()->pluck(Company::FIELD_ID)->toArray())
                    ],
                CompanyReview::FIELD_REL_ID =>
                    [
                        "nullable",
                        "integer"
                    ],
                CompanyReview::FIELD_REL_TYPE =>
                    [
                        "nullable",
                        "integer",
                        "in:" . implode(',', CompanyReview::RELATION_TYPES)
                    ],
                CompanyReview::FIELD_FIRST_NAME =>
                    [
                        "required",
                        "string",
                        "max:255"
                    ],
                CompanyReview::FIELD_LAST_NAME =>
                    [
                        "required",
                        "string",
                        "max:255"
                    ],
                CompanyReview::FIELD_EMAIL =>
                    [
                        "nullable",
                        "string",
                        "max:255"
                    ],
                CompanyReview::FIELD_PHONE =>
                    [
                        "nullable",
                        new Phone
                    ],
                CompanyReview::FIELD_TITLE =>
                    [
                        "required",
                        "string",
                        "max:255"
                    ],
                CompanyReview::FIELD_BODY =>
                    [
                        "required",
                        "string"
                    ],
                CompanyReview::FIELD_OVERALL_SCORE =>
                    [
                        "required",
                        "integer",
                        "between:0,5"
                    ],
                CompanyReview::FIELD_STATUS =>
                    [
                        "nullable",
                        "integer",
                        "in:" . implode(',', CompanyReview::STATUSES)
                    ]
            ];
    }
}

