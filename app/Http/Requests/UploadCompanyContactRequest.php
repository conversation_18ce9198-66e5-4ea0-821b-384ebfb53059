<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UploadCompanyContactRequest extends FormRequest
{
    const string REQUEST_FILE = 'file';

    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::REQUEST_FILE => [
                'required',
                'file',
                'mimes:pdf',
                'max:10000',
            ]
        ];
    }

}