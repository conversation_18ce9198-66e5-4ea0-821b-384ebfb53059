<?php

namespace App\Http\Requests;

use App\Enums\Odin\Industry;
use App\Http\Controllers\API\ExternalSearch\GooglePlacesController;
use Illuminate\Foundation\Http\FormRequest;

class GetCompaniesByCountyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** todo- investigate permissions */

        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return
        [
            GooglePlacesController::REQUEST_COUNTY_KEY => 'required|string',
            GooglePlacesController::REQUEST_STATE_KEY  => 'required|string',
            GooglePlacesController::REQUEST_INDUSTRY   => 'nullable|string|in:' . implode(',', Industry::allIndustriesBySlug()),
        ];
    }
}
