<?php

namespace App\Http\Requests\FlowEngines;

use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;

class GetConfigurableVariableDataRequest extends FormRequest
{
    const string STATE  = 'state';
    const string COUNTY = 'county';
    const string ENGINE = 'engine';
    const string KEY    = 'variable_key';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = auth()->user();
        return $user->hasPermissionTo(PermissionType::FLOW_ENGINES_CONFIGURABLE_VARIABLES_VIEW);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            self::STATE  => 'sometimes|string',
            self::COUNTY => 'sometimes|string',
            self::ENGINE => 'sometimes|string',
            self::KEY    => 'sometimes|string',
        ];
    }
}

