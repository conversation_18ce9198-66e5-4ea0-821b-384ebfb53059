<?php

namespace App\Http\Requests\FlowEngines;

use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;

class DeleteConfigurableVariableDataRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = auth()->user();
        return $user->hasPermissionTo(PermissionType::FLOW_ENGINES_CONFIGURABLE_VARIABLES_DELETE);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [];
    }
}

