<?php

namespace App\Http\Requests\FlowEngines;

use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;

class SaveConfigurableVariableDataRequest extends FormRequest
{

    const string FIELD_ENGINE_NAME         = 'engine_name';
    const string FIELD_VARIABLE_CATEGORY   = 'variable_category';
    const string FIELD_VARIABLE_TYPE       = 'variable_type';
    const string FIELD_VARIABLE_IDENTIFIER = 'variable_identifier';
    const string FIELD_VARIABLE_KEY        = 'variable_key';
    const string FIELD_VARIABLE_VALUE      = 'variable_value';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = auth()->user();
        return $user->hasPermissionTo(PermissionType::FLOW_ENGINES_CONFIGURABLE_VARIABLES_SAVE);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            self::FIELD_ENGINE_NAME         => 'required|string',
            self::FIELD_VARIABLE_CATEGORY   => 'required|string',
            self::FIELD_VARIABLE_TYPE       => 'required|string',
            self::FIELD_VARIABLE_IDENTIFIER => 'required|string',
            self::FIELD_VARIABLE_KEY        => 'required|string',
            self::FIELD_VARIABLE_VALUE      => 'required',
        ];
    }
}

