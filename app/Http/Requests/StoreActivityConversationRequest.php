<?php

namespace App\Http\Requests;

use App\Http\Controllers\API\CompanyActivityController;
use App\Models\ActivityConversation;
use App\Models\ActivityFeed;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreActivityConversationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(CompanyActivityController::MODULE_PERMISSION);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            CompanyActivityController::REQUEST_ACTIVITY  => [
                'required',
                Rule::exists(ActivityFeed::TABLE, ActivityFeed::FIELD_ID)
            ],
            CompanyActivityController::REQUEST_PARENT_ID => [
                'nullable',
                Rule::exists(ActivityConversation::TABLE, ActivityConversation::FIELD_ID)
            ],
            CompanyActivityController::REQUEST_COMMENT   => [
                'required',
                'string'
            ]
        ];
    }

    /**
     * @return void
     */
    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                CompanyActivityController::REQUEST_ACTIVITY => $this->route()->parameter(CompanyActivityController::REQUEST_ACTIVITY)
            ]
        );
    }
}
