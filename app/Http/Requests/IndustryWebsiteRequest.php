<?php

namespace App\Http\Requests;

use App\Http\Controllers\API\IndustryManagement\IndustryManagementAPIController;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryWebsite;
use App\Models\Odin\Website;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class IndustryWebsiteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo('industry-management');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return
            [
                IndustryManagementAPIController::REQUEST_INDUSTRY =>
                    [
                        "required",
                        "in:" . implode(',', Industry::query()->distinct()->pluck(Industry::FIELD_ID)->toArray())
                    ],
                IndustryManagementAPIController::REQUEST_INDUSTRY_WEBSITE =>
                    [
                        "nullable",
                        "in:" . implode(',', IndustryWebsite::query()->distinct()->pluck(IndustryWebsite::FIELD_ID)->toArray())
                    ],
                IndustryWebsite::FIELD_WEBSITE_ID =>
                    [
                        "required",
                        "in:" . implode(',', Website::query()->distinct()->pluck(Website::FIELD_ID)->toArray())
                    ],
                IndustryWebsite::FIELD_SLUG =>
                    [
                        "required",
                        "string",
                        "max:255"
                    ]
            ];
    }

    /**
     * @return void
     */
    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                IndustryManagementAPIController::REQUEST_INDUSTRY         => $this->route()->parameter(IndustryManagementAPIController::REQUEST_INDUSTRY),
                IndustryManagementAPIController::REQUEST_INDUSTRY_WEBSITE => $this->route()->parameter(IndustryManagementAPIController::REQUEST_INDUSTRY_WEBSITE)
            ]
        );
    }
}

