<?php

namespace App\Http\Requests\OpportunityNotifications;

use App\Enums\OpportunityNotifications\OpportunityNotificationConfigType;
use App\Enums\PermissionType;
use App\Models\MissedProducts\OpportunityNotificationConfig;
use App\Models\User;
use App\Models\UserPreset;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreOpportunityNotificationConfigRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->can(PermissionType::OPPORTUNITY_NOTIFICATION->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $isBDMConfig = $this->get('type') === OpportunityNotificationConfigType::BDM_COMPANIES->value;

        return [
            OpportunityNotificationConfig::FIELD_FILTER_PRESET_ID       => [Rule::requiredIf(!$isBDMConfig), 'nullable', 'numeric', 'exists:' . UserPreset::TABLE . ',' . UserPreset::FIELD_ID],
            OpportunityNotificationConfig::FIELD_NAME                   => ['sometimes', 'string'],
            OpportunityNotificationConfig::FIELD_TYPE                   => ['numeric'],
            OpportunityNotificationConfig::FIELD_ATTEMPT_ON_DAYS        => ['sometimes', 'array'],
            OpportunityNotificationConfig::FIELD_SEND_TIME              => ['sometimes', 'string', 'nullable'],
            OpportunityNotificationConfig::FIELD_MAXIMUM_SEND_FREQUENCY => ['numeric', 'min:1', 'max:255'],
            OpportunityNotificationConfig::FIELD_ACTIVE                 => ['sometimes', 'boolean'],
            OpportunityNotificationConfig::FIELD_LEAD_THRESHOLD         => ['nullable', 'numeric'],
            OpportunityNotificationConfig::FIELD_CAMPAIGN_THRESHOLD     => ['nullable', 'numeric'],
            OpportunityNotificationConfig::FIELD_MAXIMUM_PROMO_PRODUCTS => ['nullable', 'numeric'],
            OpportunityNotificationConfig::FIELD_MAXIMUM_DAYS_LAST_LEAD => ['nullable', Rule::requiredIf(!$isBDMConfig), 'numeric', 'min:7', 'max:120'],
            OpportunityNotificationConfig::FIELD_DAYS_TO_QUERY          => ['numeric', 'min:1', 'max:31'],
            OpportunityNotificationConfig::FIELD_EXPIRES_AT             => [Rule::requiredIf($this->method === self::METHOD_POST && !$isBDMConfig), 'date'],
        ];
    }
}
