<?php

namespace App\Http\Requests\OpportunityNotifications;

use App\Models\MissedProducts\OpportunityNotificationConfig;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class OpportunityNotificationConfigRequest extends FormRequest
{
    const string MODULE_PERMISSION = 'opportunity-notifications';
    
    const string REQUEST_PER_PAGE = 'per_page';
    const string REQUEST_PAGE = 'page';
    const string REQUEST_SORT_COL = 'sort_col';
    const string REQUEST_SORT_DIR = 'sort_dir';
    const array SORTABLE_COLUMNS = [
        OpportunityNotificationConfig::CREATED_AT,
        OpportunityNotificationConfig::FIELD_ACTIVE,
        OpportunityNotificationConfig::FIELD_NAME,
        OpportunityNotificationConfig::FIELD_ATTEMPT_ON_DAYS,
        OpportunityNotificationConfig::FIELD_SEND_TIME,
        OpportunityNotificationConfig::FIELD_EXPIRES_AT,
        OpportunityNotificationConfig::FIELD_ACTIVE,
        OpportunityNotificationConfig::FIELD_MAXIMUM_SEND_FREQUENCY,
        OpportunityNotificationConfig::FIELD_LEAD_THRESHOLD,
    ];

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->can(self::MODULE_PERMISSION);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            OpportunityNotificationConfig::FIELD_NAME                   => ['sometimes', 'string', 'nullable'],
            OpportunityNotificationConfig::FIELD_ATTEMPT_ON_DAYS        => ['array', 'min:1', 'nullable'],
            OpportunityNotificationConfig::FIELD_SEND_TIME              => ['sometimes', 'string', 'nullable'],
            OpportunityNotificationConfig::FIELD_MAXIMUM_SEND_FREQUENCY => ['numeric', 'min:1', 'max:255'],
            OpportunityNotificationConfig::FIELD_ACTIVE                 => ['sometimes', 'boolean', 'nullable'],
            OpportunityNotificationConfig::FIELD_LEAD_THRESHOLD         => ['nullable', 'numeric', 'nullable'],
            self::REQUEST_PER_PAGE                                      => ['present', 'integer', Rule::in(25, 50, 100)],
            self::REQUEST_PAGE                                          => ['present', 'integer'],
            self::REQUEST_SORT_COL                                      => ['present', 'string', Rule::in(self::SORTABLE_COLUMNS)],
            self::REQUEST_SORT_DIR                                      => ['present', 'string', Rule::in(["asc", "desc"])],
        ];
    }
}
