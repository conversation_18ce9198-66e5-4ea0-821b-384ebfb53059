<?php

namespace App\Http\Requests\OpportunityNotifications;

use App\Models\MissedProducts\OpportunityNotification;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class OpportunityNotificationRequest extends FormRequest
{
    const MODULE_PERMISSION = 'opportunity-notifications';

    const REQUEST_RECIPIENTS = OpportunityNotification::FIELD_RECIPIENTS;
    const REQUEST_CONTENT = OpportunityNotification::FIELD_CONTENT;
    const REQUEST_DELIVERY_METHOD = OpportunityNotification::FIELD_DELIVERY_METHOD;
    const REQUEST_SENT_AT = OpportunityNotification::FIELD_SENT_AT;
    const REQUEST_COMPANY_NAME = 'company_name';
    const REQUEST_PER_PAGE = 'per_page';
    const REQUEST_PAGE = 'page';
    const REQUEST_SORT_COL = 'sort_col';
    const REQUEST_SORT_DIR = 'sort_dir';
    const REQUEST_CONFIGURATION_ID = 'configuration_id';
    const SORTABLE_COLUMNS = [
        OpportunityNotification::CREATED_AT,
        self::REQUEST_COMPANY_NAME,
        self::REQUEST_RECIPIENTS,
        self::REQUEST_CONTENT,
        self::REQUEST_DELIVERY_METHOD,
        self::REQUEST_SENT_AT,
        self::REQUEST_COMPANY_NAME,
    ];

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->can(self::MODULE_PERMISSION);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            self::REQUEST_RECIPIENTS => ['sometimes', 'string', 'nullable'],
            self::REQUEST_CONTENT => ['sometimes', 'string', 'nullable'],
            self::REQUEST_DELIVERY_METHOD => ['sometimes', 'string', 'nullable'],
            self::REQUEST_SENT_AT => ['sometimes', 'array', 'nullable'],
            self::REQUEST_COMPANY_NAME => ['sometimes', 'string', 'nullable'],
            self::REQUEST_PER_PAGE => ['present', 'integer', Rule::in(25, 50, 100)],
            self::REQUEST_PAGE => ['present', 'integer'],
            self::REQUEST_SORT_COL => ['present', 'string', Rule::in(self::SORTABLE_COLUMNS)],
            self::REQUEST_SORT_DIR => ['present', 'string', Rule::in(["asc", "desc"])],
            self::REQUEST_CONFIGURATION_ID => ['sometimes', 'numeric'],
        ];
    }
}
