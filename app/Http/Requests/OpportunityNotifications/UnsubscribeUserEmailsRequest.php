<?php

namespace App\Http\Requests\OpportunityNotifications;

use Illuminate\Foundation\Http\FormRequest;

class UnsubscribeUserEmailsRequest extends FormRequest
{
    const REQUEST_CONTACT_REF = 'contact_ref';

    /**
     * Set to true for everyone to unsubscribe without being logged in.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            self::REQUEST_CONTACT_REF => ['required', 'string'],
        ];
    }
}
