<?php

namespace App\Http\Requests\OpportunityNotifications;

use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class SearchOpportunityNotificationOptionsRequest extends FormRequest
{
    const REQUEST_NAME = 'name';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->can(PermissionType::OPPORTUNITY_NOTIFICATION->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            self::REQUEST_NAME => ['required'],
        ];
    }
}
