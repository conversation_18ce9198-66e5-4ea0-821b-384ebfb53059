<?php

namespace App\Http\Requests\API;

use App\Models\Odin\ConsumerProduct;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CloneLeadRequest extends FormRequest
{
    const string CONSUMER_PRODUCT_ID = 'consumer_product_id';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::CONSUMER_PRODUCT_ID => 'required'
        ];
    }
}
