<?php

namespace App\Http\Requests\ConfigurableFields;

use App\Http\Controllers\API\IndustryManagement\ConfigurableFieldsController;
use App\Models\User;
use App\Rules\InputTypes;
use App\Services\Odin\ConfigurableFieldsService;
use Exception;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class SaveConfigurableFieldsRequest extends FormRequest
{
    const INDUSTRY_SERVICE_ID = 'industry_service_id';
    const INDUSTRY_ID         = 'industry_id';
    const NAME                = 'name';
    const TYPE                = 'type';
    const KEY                 = 'key';
    const PAYLOAD             = 'payload';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->can('industry-management');
    }

    /**
     * @return string[]
     */
    public function messages(): array
    {
        $messages = [
            ConfigurableFieldsController::REQUEST_CATEGORY.".*" => 'Invalid category',
            ConfigurableFieldsController::REQUEST_TYPE.".*" => 'Invalid type',
            ConfigurableFieldsController::REQUEST_CATEGORY_ID.".*" => 'Invalid category ID',
        ];

        foreach (request()->input(ConfigurableFieldsController::REQUEST_FIELDS) as $line => $requestData) {
            foreach ($requestData as $input => $value) {
                if($input === self::NAME) {
                    $messages[ConfigurableFieldsController::REQUEST_FIELDS . '.' . $line . '.' . $input . '.distinct'] = 'The name `'. $value .'` is duplicate in category';
                    $messages[ConfigurableFieldsController::REQUEST_FIELDS . '.' . $line . '.' . $input . '.*'] = 'Invalid or missing name at line ' . ($line + 1);
                }
                elseif($input === self::TYPE) {
                    $messages[ConfigurableFieldsController::REQUEST_FIELDS . '.' . $line . '.' . $input . '.*'] = 'Invalid or missing type at line ' . ($line + 1);
                }
                elseif($input === self::KEY) {
                    $messages[ConfigurableFieldsController::REQUEST_FIELDS . '.' . $line . '.' . $input . '.alpha_dash'] = 'The key must consist of only letters, numbers, dashes, and underscores';
                    $messages[ConfigurableFieldsController::REQUEST_FIELDS . '.' . $line . '.' . $input . '.required'] = 'Missing key at line ' . ($line + 1);
                    $messages[ConfigurableFieldsController::REQUEST_FIELDS . '.' . $line . '.' . $input . '.unique'] = 'The key `'. $value .'` is already taken';
                    $messages[ConfigurableFieldsController::REQUEST_FIELDS . '.' . $line . '.' . $input . '.distinct'] = 'The key `'. $value .'` is duplicate in category';
                    $messages[ConfigurableFieldsController::REQUEST_FIELDS . '.' . $line . '.' . $input . '.not_in'] = 'The Key ' . $input . ' is already used in a sibling category at line ' . ($line + 1);
                }
                elseif($input === self::PAYLOAD) {
                    $messages[ConfigurableFieldsController::REQUEST_FIELDS . '.' . $line . '.' . $input . '.*'] = '';
                }
                elseif($input === self::INDUSTRY_SERVICE_ID) {
                    $messages[ConfigurableFieldsController::REQUEST_FIELDS . '.' . $line . '.' . $input . '.*'] = 'Invalid service';
                }
                elseif($input === self::INDUSTRY_ID) {
                    $messages[ConfigurableFieldsController::REQUEST_FIELDS . '.' . $line . '.' . $input . '.*'] = 'Invalid industry';
                }
            }
        }

        return $messages;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     * @throws Exception
     */
    public function rules(ConfigurableFieldsService $configurableFieldsService)
    {
        $type = request()->input(ConfigurableFieldsController::REQUEST_TYPE);
        $category = request()->input(ConfigurableFieldsController::REQUEST_CATEGORY);

        $requiredIfFieldsRule = Rule::requiredIf(!empty(request()->input(ConfigurableFieldsController::REQUEST_FIELDS)));

        $optionalColumnRules = [];

        // Name must be distinct within this category
        $nameColumnRule = [
            ConfigurableFieldsController::REQUEST_FIELDS.".*.name" => [
                $requiredIfFieldsRule,
                'distinct:strict,ignore_case',
                'string',
                'max:255',
            ]
        ];

        // Key must be distinct within this category
        $localKeyColumnRule = [
            $requiredIfFieldsRule,
            'distinct:strict,ignore_case',
            'string',
            'alpha_dash',
        ];
        $siblingKeyColumnRule = [];

        if(!empty(request()->input(ConfigurableFieldsController::REQUEST_FIELDS))) {
            $columns = $configurableFieldsService->getModelColumns($type, $category)->toArray();

            $optionalColumns = array_diff($columns, ['name', 'key', 'type', 'payload']);

            foreach($optionalColumns as $optionalColumn) {
                $optionalColumnRules[ConfigurableFieldsController::REQUEST_FIELDS.'.*.'.$optionalColumn] = ['present', 'nullable', new InputTypes(['string', 'integer', 'boolean'])];
            }

            // Keys should also be unique across all categories which target the same model->payload
            // Grab all keys from 'sibling categories' and make sure the they aren't already in use
            $siblingCategoriesForRequestType = collect(array_keys(ConfigurableFieldsService::MODELS[$type]))
                ->filter(fn($typeCategory) => $typeCategory !== $category);

            $allSiblingKeys = $siblingCategoriesForRequestType
                ->reduce(function($output, $siblingCategory) use ($configurableFieldsService, &$keysInSiblingCategories, $type) {
                    $siblingModel = $configurableFieldsService->determineModel($type, $siblingCategory);
                    $siblingKeys = $siblingModel->newQuery()
                        ->pluck('key');
                    return $output->push($siblingKeys);
                }, collect())
                ->toArray();

            if(count($allSiblingKeys) > 0)
                $siblingKeyColumnRule = [ Rule::notIn(...$allSiblingKeys) ];
        }

        $keyColumnRule = [
            ConfigurableFieldsController::REQUEST_FIELDS.".*.key" => [
                ...$localKeyColumnRule,
                ...$siblingKeyColumnRule,
            ]
        ];

        return array_merge(
            $nameColumnRule,
            $keyColumnRule,
            $optionalColumnRules,
            [
                ConfigurableFieldsController::REQUEST_CATEGORY => ['required', 'string', Rule::in(ConfigurableFieldsService::CATEGORIES)],
                ConfigurableFieldsController::REQUEST_TYPE => ['required', 'string', Rule::in(ConfigurableFieldsService::TYPES)],
                ConfigurableFieldsController::REQUEST_CATEGORY_ID => ['present', 'nullable', 'integer', 'min:1'],

                ConfigurableFieldsController::REQUEST_FIELDS => ['array', 'present'],

                ConfigurableFieldsController::REQUEST_FIELDS.".*.type" => [$requiredIfFieldsRule, new InputTypes(['string', 'integer'])],
                ConfigurableFieldsController::REQUEST_FIELDS.".*.payload" => ['sometimes', 'array'],

                ConfigurableFieldsController::REQUEST_FIELDS . ".*." . self::INDUSTRY_SERVICE_ID => [
                    'integer',
                    Rule::requiredIf(function () {
                        return !empty(request()->input(ConfigurableFieldsController::REQUEST_FIELDS))
                            && request()->input(ConfigurableFieldsController::REQUEST_CATEGORY) === ConfigurableFieldsService::CATEGORY_SERVICE;
                    })
                ],
                ConfigurableFieldsController::REQUEST_FIELDS . ".*." . self::INDUSTRY_ID => [
                    'integer',
                    Rule::requiredIf(function () {
                        return !empty(request()->input(ConfigurableFieldsController::REQUEST_FIELDS))
                            && request()->input(ConfigurableFieldsController::REQUEST_CATEGORY) === ConfigurableFieldsService::CATEGORY_INDUSTRY;
                    })
                ],
            ]
        );

    }
}
