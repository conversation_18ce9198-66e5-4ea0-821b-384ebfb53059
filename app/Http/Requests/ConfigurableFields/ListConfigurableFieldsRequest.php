<?php

namespace App\Http\Requests\ConfigurableFields;

use App\Http\Controllers\API\IndustryManagement\ConfigurableFieldsController;
use App\Models\User;
use App\Services\Odin\ConfigurableFieldsService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class ListConfigurableFieldsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->can('industry-management');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            ConfigurableFieldsController::REQUEST_CATEGORY => ['required', 'string', Rule::in(ConfigurableFieldsService::CATEGORIES)],
            ConfigurableFieldsController::REQUEST_TYPE => ['required', 'string', Rule::in(ConfigurableFieldsService::TYPES)],
            ConfigurableFieldsController::REQUEST_CATEGORY_ID => ['sometimes', 'nullable', 'integer', 'min:1']
        ];
    }
}
