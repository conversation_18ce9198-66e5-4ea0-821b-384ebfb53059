<?php

namespace App\Http\Requests\CompanyCampaignDeliveryLog;

use App\Enums\PermissionType;
use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class ListContactDeliveryLogsRequest extends FormRequest
{
    const string REQUEST_PAGE                   = 'page';
    const string REQUEST_PER_PAGE               = 'perPage';
    const string REQUEST_CAMPAIGN               = 'campaign';
    const string REQUEST_DELIVERY_STATUS        = 'delivery_status';
    const string REQUEST_COMPANY_ID             = 'company_id';
    const string REQUEST_CONSUMER_PRODUCT_ID    = 'consumer_product';
    const string REQUEST_DATE                   = 'date';
    const string REQUEST_DATE_FROM              = 'date.from';
    const string REQUEST_DATE_TO                = 'date.to';
    const string REQUEST_INVOICE_ID             = 'invoice_id';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();
        return $user->hasPermissionTo(PermissionType::COMPANY_CAMPAIGN_DELIVERY_LOGS_VIEW ->value);
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        $paginationRules = [
            self::REQUEST_PAGE     => 'nullable|numeric|min:1',
            self::REQUEST_PER_PAGE => 'nullable|numeric|min:1|max:100',
        ];

        return [
            ...$paginationRules,
            self::REQUEST_CAMPAIGN              => ['string', 'nullable', 'sometimes'],
            self::REQUEST_CONSUMER_PRODUCT_ID   => ['integer', 'nullable', 'sometimes'],
            self::REQUEST_DELIVERY_STATUS       => ['boolean', 'nullable', 'sometimes'],
            self::REQUEST_COMPANY_ID            => ['integer', 'nullable', 'sometimes', 'exists:' . Company::TABLE .','. Company::FIELD_ID],
            self::REQUEST_DATE_FROM             => ['date', 'nullable', 'sometimes'],
            self::REQUEST_DATE_TO               => ['date', 'nullable', 'sometimes'],
            self::REQUEST_INVOICE_ID            => ['integer', 'nullable', 'sometimes'],
        ];
    }
}
