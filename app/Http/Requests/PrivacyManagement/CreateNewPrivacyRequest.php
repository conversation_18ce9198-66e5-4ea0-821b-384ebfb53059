<?php

namespace App\Http\Requests\PrivacyManagement;

use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class CreateNewPrivacyRequest extends FormRequest
{
    const string FIRST_NAME  = 'first_name';
    const string LAST_NAME   = 'last_name';
    const string EMAIL       = 'email';
    const string PHONE       = 'phone';
    const string ADDRESS     = 'address';
    const string SOURCE      = 'source';
    const string DESCRIPTION = 'description';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::PRIVACY_MANAGEMENT_REQUEST_CREATE->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::FIRST_NAME  => ['sometimes', 'nullable', 'string'],
            self::LAST_NAME   => ['sometimes', 'nullable', 'string'],
            self::EMAIL       => ['sometimes', 'nullable', 'email'],
            //todo for phone we want no formatting and no area code eg. 5203787197
            self::PHONE       => ['sometimes', 'nullable', 'string'],
            self::ADDRESS     => ['sometimes', 'nullable', 'string'],
            self::SOURCE      => ['required', 'string'],
            self::DESCRIPTION => ['sometimes', 'nullable', 'string'],
        ];
    }
}

