<?php

namespace App\Http\Requests\PrivacyManagement;

use App\Enums\PermissionType;
use App\Enums\PrivacyManagement\PrivacyRequestStatuses;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class GetPrivacyRequestsRequest extends FormRequest
{
    const string REQUEST_PAGE     = 'page';
    const string REQUEST_PER_PAGE = 'perPage';
    const string REQUEST_SEARCH   = 'search';
    const string REQUEST_STATUS   = 'status';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::PRIVACY_MANAGEMENT_VIEW->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $paginationRules = [
            self::REQUEST_PAGE     => 'nullable|numeric|min:1',
            self::REQUEST_PER_PAGE => 'nullable|numeric|min:1|max:100',
        ];

        return [
            ...$paginationRules,
            self::REQUEST_SEARCH => 'sometimes|nullable|string',
            self::REQUEST_STATUS => ['sometimes', 'nullable', Rule::enum(PrivacyRequestStatuses::class)]
        ];
    }
}

