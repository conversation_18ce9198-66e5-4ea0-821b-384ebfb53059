<?php

namespace App\Http\Requests;

use App\Enums\CompanyConsolidatedStatus;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\PermissionType;
use App\Enums\RoleType;
use App\Http\Controllers\API\CompaniesController;
use App\Models\Legacy\EloquentCompany;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\Industry;
use App\Models\User;
use App\Repositories\Odin\CompanyRepository;
use App\Services\UserAuthorizationService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class PatchCompanyBasicInfoRequest extends FormRequest
{
    /**
     * @return bool
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        if (!$user->hasAnyRole(RoleType::ADMIN->value,
            RoleType::SALES_MANAGER->value,
            RoleType::ACCOUNT_MANAGER->value,
            RoleType::ONBOARDING_MANAGER->value,
            RoleType::BUSINESS_DEVELOPMENT_MANAGER->value)) {
            return false;
        }

        if ($user->cannot('companies')) {
            return false;
        }

        return true;
    }

    /**
     * @return string[]
     */
    public function rules(): array
    {
        /** @var User $user */
        $user = Auth::user();

        return [
            EloquentCompany::COMPANY_NAME                                         => "required|string|min:2",
            Company::FIELD_NAME                                                   => "required|string|min:2",
            EloquentCompany::WEBSITE                                              => "required|string|max:255",
            Company::FIELD_SOURCED_FROM                                           => "nullable|string",
            CompaniesController::REQUEST_PRESCREENED                              => "required|boolean",
            EloquentCompany::EMPLOYEE_COUNT                                       => "nullable|numeric",
            EloquentCompany::REVENUE_IN_THOUSANDS                                 => "nullable|numeric",
            CompaniesController::REQUEST_COMPANY_INDUSTRIES                       => 'nullable|array|exists:' . Industry::TABLE . ',' . Industry::FIELD_ID,
            CompanyLocation::RELATION_ADDRESS . '.' . CompanyLocation::FIELD_NAME => 'nullable|string',
            CompanyLocation::RELATION_ADDRESS . '.' . Address::FIELD_ADDRESS_1    => 'nullable|string',
            CompanyLocation::RELATION_ADDRESS . '.' . Address::FIELD_CITY         => 'nullable|string',
            CompanyLocation::RELATION_ADDRESS . '.' . Address::FIELD_STATE        => 'nullable|string',
            CompanyLocation::RELATION_ADDRESS . '.' . Address::FIELD_ZIP_CODE     => 'nullable|string',
            Company::FIELD_ADMIN_APPROVED                                         => 'nullable|boolean',
            Company::FIELD_ADMIN_LOCKED                                           => 'nullable|boolean',
            Company::FIELD_STATUS                                                 => ['nullable', 'numeric', Rule::excludeIf(fn() => !$user->hasPermissionTo(PermissionType::COMPANY_BASIC_STATUS_EDIT))],
            Company::FIELD_ADMIN_STATUS                                           => ['nullable', 'numeric'],
            GlobalConfigurableFields::QR_TOP_500_COMPANY->value                   => 'nullable|boolean',
            CompaniesController::REQUEST_ADMIN_STATUS_CHANGE_REASON               => 'nullable|string'
        ];
    }
}
