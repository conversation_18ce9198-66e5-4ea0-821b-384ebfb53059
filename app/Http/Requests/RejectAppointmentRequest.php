<?php

namespace App\Http\Requests;

use App\Enums\RejectionReasons;
use App\Http\Controllers\API\Appointments\AppointmentController;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class RejectAppointmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            AppointmentController::REQUEST_REJECTION_REASON => ['required', new Enum(RejectionReasons::class)],
            AppointmentController::REQUEST_REJECTION_NOTES => ['required', 'string', 'max:250']
        ];
    }
}
