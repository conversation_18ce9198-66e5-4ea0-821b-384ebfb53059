<?php

namespace App\Http\Requests;

use App\Enums\PermissionType;
use App\Models\ConsumerProcessingActivity;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class StoreConsumerProcessingCommentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::LEAD_PROCESSING->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            ConsumerProcessingActivity::FIELD_ID      => 'numeric',
            ConsumerProcessingActivity::FIELD_COMMENT => 'string|max:1023|nullable',
            ConsumerProcessingActivity::FIELD_SCOPE   => 'nullable|numeric',
        ];
    }
}