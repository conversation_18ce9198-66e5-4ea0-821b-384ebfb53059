<?php

namespace App\Http\Requests\CompanyUserRelationship;

use App\Enums\PermissionType;
use App\Models\CompanyUserRelationship;
use App\Models\Role;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class ListCompanyUserRelationshipRequest extends FormRequest
{
    const string COMPANY_ID = 'company_id';
    const string NAME       = 'name';
    const string ROLES      = 'roles';
    const string PAGE       = 'page';
    const string PER_PAGE   = 'perPage';
    const string ACTIVE     = 'active';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::COMPANY_COMMISSION_VIEW->value);
    }

    public function prepareForValidation(): void
    {
        if (!is_null($this->input('active'))) {
            $this->merge([
                'active' => filter_var($this->input('active'), FILTER_VALIDATE_BOOLEAN),
            ]);
        }
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            self::PAGE         => ['integer'],
            self::PER_PAGE     => ['integer', 'min:1', 'max:100'],
            self::NAME         => ['string'],
            self::COMPANY_ID   => [Rule::exists(CompanyUserRelationship::class)],
            self::ROLES . '.*' => [Rule::exists(Role::class, Role::FIELD_NAME)],
            self::ACTIVE       => ['boolean'],
        ];
    }
}
