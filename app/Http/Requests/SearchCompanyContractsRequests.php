<?php

namespace App\Http\Requests;

use App\Enums\PermissionType;
use App\Http\Controllers\API\CompaniesController;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class SearchCompanyContractsRequests extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        /** @var User $user */
        $user = Auth::user();
        return $user->hasPermissionTo(PermissionType::CONTRACT_MANAGEMENT_VIEW->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            CompaniesController::REQUEST_CONTRACT_NAME => ['nullable', 'string'],
            CompaniesController::REQUEST_CONTRACT_TYPE => ['nullable', 'string'],
        ];
    }
}
