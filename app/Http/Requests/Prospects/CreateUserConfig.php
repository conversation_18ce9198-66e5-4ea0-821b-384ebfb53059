<?php

namespace App\Http\Requests\Prospects;

use Closure;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\ValidationRule;

class CreateUserConfig extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('update-prospecting-configurations');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'user_id' => [
                'required',
                'numeric',
                'exists:users,id',
            ],
            'role_id' => [
                'required',
                'exists:roles,id',
                function (string $attribute, mixed $value, Closure $fail) {
                    $user = User::with('roles')->find($this->user_id);

                    if($user && $user->roles->pluck('id')->doesntContain($this->role_id)) {
                        $fail('You cannot configure a role that does not belong to this user');
                    }
                },
            ],
            'configs' => 'required|array',
            'configs.*.key' => 'required|string',
            'configs.*.value' => 'present',
        ];
    }

    public function messages(): array
    {
        return [
            'user_id' => [
                'required' => 'Please select a user',
                'numeric' => 'Please select a valid user',
                'exists' => 'Please select a valid user',
            ],
            'role_id' => [
                'required' => 'Please select a role',
                'exists' => 'Please select a valid role',
            ],
            'configs' => [
                'array' => 'Please select valid configuration options',
            ],
        ];
    }
}
