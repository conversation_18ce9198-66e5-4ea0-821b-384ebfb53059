<?php

namespace App\Http\Requests\Prospects;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class SendProspectingEmailRequest extends FormRequest
{
    const string EMAIL = 'email';
    const string SUBJECT = 'subject';
    const string CONTENT = 'content';
    const string CC = 'cc';
    const string BCC = 'bcc';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::EMAIL => 'required|email',
            self::SUBJECT => 'required',
            self::CONTENT => 'required',
            self::CC => 'array',
            self::BCC => 'array',
        ];
    }
}
