<?php

namespace App\Http\Requests\Prospects;

use App\Models\Prospects\NewBuyerProspect;
use App\Services\Prospects\ProspectIndustriesService;
use Illuminate\Foundation\Http\FormRequest;

class UpdateProspectRequest extends FormRequest
{

    public function prepareForValidation()
    {
        $this->merge([
            NewBuyerProspect::FIELD_INDUSTRY_SERVICE_IDS => ProspectIndustriesService::convertIndustryIdsToServiceIds($this->input('industry_ids')),
        ]);
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            NewBuyerProspect::FIELD_COMPANY_NAME              => 'string',
            NewBuyerProspect::FIELD_COMPANY_WEBSITE           => 'nullable|string',
            NewBuyerProspect::FIELD_COMPANY_PHONE             => 'nullable|string',
            NewBuyerProspect::FIELD_ADDRESS_STREET            => 'nullable|string',
            NewBuyerProspect::FIELD_ADDRESS_CITY_KEY          => 'nullable|string',
            NewBuyerProspect::FIELD_ADDRESS_STATE_ABBR        => 'nullable|string',
            NewBuyerProspect::FIELD_DECISION_MAKER_FIRST_NAME => 'nullable|string',
            NewBuyerProspect::FIELD_DECISION_MAKER_LAST_NAME  => 'nullable|string',
            NewBuyerProspect::FIELD_DECISION_MAKER_EMAIL      => 'nullable|string',
            NewBuyerProspect::FIELD_DECISION_MAKER_PHONE      => 'nullable|string',
            NewBuyerProspect::FIELD_NOTES                     => 'nullable|string',
            NewBuyerProspect::FIELD_INDUSTRY_SERVICE_IDS      => 'nullable|array',
            'demo'                                            => 'nullable|array',
            NewBuyerProspect::FIELD_DECISION_MAKER_CONFIRMED  => 'nullable|boolean',
        ];
    }
}
