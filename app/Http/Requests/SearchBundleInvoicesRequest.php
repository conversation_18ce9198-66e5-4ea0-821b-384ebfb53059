<?php

namespace App\Http\Requests;

use App\Http\Controllers\API\BundleManagement\BundleInvoiceSearchController;
use App\Http\Controllers\API\BundleManagement\BundleSearchController;
use App\Models\BundleInvoice;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class SearchBundleInvoicesRequest extends FormRequest
{
    const REQUEST_BUNDLE_NAME = 'bundle_name';
    const REQUEST_COST_FROM = 'cost_from';
    const REQUEST_COST_TO = 'cost_to';
    const REQUEST_CREDIT_FROM = 'credit_from';
    const REQUEST_CREDIT_TO = 'credit_to';
    const REQUEST_STATUS = BundleInvoice::FIELD_STATUS;
    const REQUEST_ISSUED_AT = BundleInvoice::FIELD_ISSUED_AT;
    const REQUEST_COMPANY_NAME = 'company_name';
    const REQUEST_PER_PAGE = 'per_page';
    const REQUEST_PAGE = 'page';
    const REQUEST_SORT_COL = 'sort_col';
    const REQUEST_SORT_DIR = 'sort_dir';
    const SORTABLE_COLUMNS = [
        BundleInvoice::FIELD_STATUS,
        BundleInvoice::FIELD_COST,
        BundleInvoice::FIELD_CREDIT,
        BundleInvoice::FIELD_ISSUED_AT,
        BundleInvoice::FIELD_APPROVED_AT,
        BundleInvoice::CREATED_AT,
        self::REQUEST_COMPANY_NAME,
        self::REQUEST_BUNDLE_NAME,
    ];


    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->can(['view-bundles']);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::REQUEST_BUNDLE_NAME => ['sometimes', 'string', 'nullable'],
            self::REQUEST_COST_FROM => ['sometimes', 'numeric', 'nullable'],
            self::REQUEST_COST_TO => ['sometimes', 'numeric', 'nullable'],
            self::REQUEST_CREDIT_FROM => ['sometimes', 'numeric', 'nullable'],
            self::REQUEST_CREDIT_TO => ['sometimes', 'numeric', 'nullable'],
            self::REQUEST_ISSUED_AT => ['sometimes', 'array', 'nullable'],
            self::REQUEST_STATUS => ['sometimes', 'nullable'],
            self::REQUEST_COMPANY_NAME => ['sometimes', 'string', 'nullable'],
            self::REQUEST_PER_PAGE => ['present', 'integer', Rule::in(25, 50, 100)],
            self::REQUEST_PAGE => ['present', 'integer'],
            self::REQUEST_SORT_COL => ['present', 'string', Rule::in(self::SORTABLE_COLUMNS)],
            self::REQUEST_SORT_DIR => ['present', 'string', Rule::in(["asc", "desc"])],
        ];
    }
}
