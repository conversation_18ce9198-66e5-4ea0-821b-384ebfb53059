<?php

namespace App\Http\Requests;

use App\Http\Controllers\CompanyUserController;
use App\Models\Odin\CompanyUser;
use App\Models\User;
use App\Rules\Phone;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreCompanyUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();
        return $user->hasPermissionTo(CompanyUserController::MODULE_PERMISSION);
    }

    /**
     * Mutate data before passing to validation
     *
     * @return void
     */
    public function prepareForValidation(): void
    {
        $this->merge([
            CompanyUser::FIELD_OFFICE_PHONE => Phone::preparePhoneNumberForValidation($this->{CompanyUser::FIELD_OFFICE_PHONE}),
            CompanyUser::FIELD_CELL_PHONE   => Phone::preparePhoneNumberForValidation($this->{CompanyUser::FIELD_CELL_PHONE}),
            CompanyUser::FIELD_LAST_NAME    => $this->{CompanyUser::FIELD_LAST_NAME} ?? "",
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $minPassword = CompanyUser::AUTHENTICATION_MINIMUM_PASSWORD_LENGTH;
        $passwordRequired = request()->{CompanyUser::FIELD_AUTHENTICATION_TYPE} === CompanyUser::AUTHENTICATION_TYPE_ADMIN2
            && $this->isMethod('post')
            && !request()->get(CompanyUser::FIELD_IS_CONTACT);

        return
            [
                CompanyUser::FIELD_FIRST_NAME => 'required|string|max:64',
                CompanyUser::FIELD_LAST_NAME => 'string|max:64',
                CompanyUser::FIELD_TITLE => 'nullable|string|max:64',
                CompanyUser::FIELD_STATUS => ['sometimes', 'boolean'],
                CompanyUser::FIELD_EMAIL => ['nullable', 'email', Rule::requiredIf(fn() => !request()->get(CompanyUser::FIELD_CELL_PHONE) && !request()->get(CompanyUser::FIELD_OFFICE_PHONE))],
                CompanyUser::FIELD_CELL_PHONE => ['nullable', new Phone, Rule::requiredIf(fn() => !request()->get(CompanyUser::FIELD_OFFICE_PHONE) && !request()->get(CompanyUser::FIELD_EMAIL))],
                CompanyUser::FIELD_OFFICE_PHONE => ['nullable', new Phone, Rule::requiredIf(fn() => !request()->get(CompanyUser::FIELD_CELL_PHONE) && !request()->get(CompanyUser::FIELD_EMAIL))],
                CompanyUser::FIELD_NOTES => ['nullable', 'string'],
                CompanyUser::FIELD_PASSWORD => ['nullable', 'sometimes', 'string', "min:$minPassword", 'max:64', Rule::requiredIf(fn() => $passwordRequired)],
                CompanyUser::FIELD_IS_CONTACT => 'required|boolean',
                CompanyUser::FIELD_IS_DECISION_MAKER => 'boolean',
                CompanyUser::FIELD_CAN_RECEIVE_PROMOTIONS => 'boolean',
                CompanyUser::FIELD_MUST_RESET_PASSWORD => 'boolean',
                'user_fields' => '',
                CompanyUser::FIELD_DNC_CONTACT => 'boolean',
            ];
    }
}
