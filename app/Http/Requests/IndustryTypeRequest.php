<?php

namespace App\Http\Requests;

use App\Http\Controllers\API\IndustryManagement\IndustryManagementAPIController;
use App\Models\Odin\GlobalType;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class IndustryTypeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo('industry-management');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return
            [
                IndustryManagementAPIController::REQUEST_INDUSTRY =>
                    [
                        "required",
                        "in:" . implode(',', Industry::query()->distinct()->pluck(Industry::FIELD_ID)->toArray())
                    ],
                IndustryManagementAPIController::REQUEST_INDUSTRY_TYPE =>
                    [
                        "nullable",
                        "in:" . implode(',', IndustryType::query()->distinct()->pluck(IndustryType::FIELD_ID)->toArray())
                    ],
                IndustryType::FIELD_GLOBAL_TYPE_ID =>
                    [
                        "required",
                        "in:" . implode(',', GlobalType::query()->distinct()->pluck(GlobalType::FIELD_ID)->toArray())
                    ],
                IndustryType::FIELD_NAME =>
                    [
                        "required",
                        "string",
                        "max:255"
                    ],
                IndustryType::FIELD_KEY =>
                    [
                        "required",
                        "string",
                        "max:255"
                    ]
            ];
    }

    /**
     * @return void
     */
    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                IndustryManagementAPIController::REQUEST_INDUSTRY       => $this->route()->parameter(IndustryManagementAPIController::REQUEST_INDUSTRY),
                IndustryManagementAPIController::REQUEST_INDUSTRY_TYPE  => $this->route()->parameter(IndustryManagementAPIController::REQUEST_INDUSTRY_TYPE)
            ]
        );
    }
}
