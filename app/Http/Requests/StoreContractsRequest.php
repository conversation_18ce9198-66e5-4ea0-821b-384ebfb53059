<?php

namespace App\Http\Requests;

use App\Enums\ContractKeys;
use App\Enums\PermissionType;
use App\Models\Contract;
use App\Models\ContractKey;
use App\Models\Odin\Website;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;

class StoreContractsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::CONTRACT_MANAGEMENT_EDIT->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            Contract::FIELD_CONTRACT_KEY_ID         => [
                'required',
                'integer',
                'in:' . implode(',', ContractKey::query()->distinct()->pluck(ContractKey::FIELD_ID)->toArray())
            ],
            Contract::FIELD_DESCRIPTION => ['sometimes', 'string'],
            Contract::FIELD_WEBSITE_ID  => [
                'required',
                'integer',
                'in:' . implode(',', Website::query()->distinct()->pluck(Website::FIELD_ID)->toArray())
            ],
            Contract::FIELD_ACTIVE      => ['required', 'boolean']
        ];
    }

}