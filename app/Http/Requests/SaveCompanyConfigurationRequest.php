<?php

namespace App\Http\Requests;

use App\Enums\CompanyConfigurationEnum;
use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class SaveCompanyConfigurationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::COMPANY_CONFIGURE->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return $this->getValidationRulesByEnum();
    }

    private function getValidationRulesByEnum(){
        return array_reduce(CompanyConfigurationEnum::cases(), function ($prev, $key) {
            $prev[$key->value] = ['sometimes', 'boolean'];
            return $prev;
        }, []);
    }
}

