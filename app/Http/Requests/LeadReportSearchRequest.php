<?php

namespace App\Http\Requests;

use App\Http\Controllers\API\LeadProcessing\LeadProcessingReportApiController;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class LeadReportSearchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            LeadProcessingReportApiController::START_DATE => ["required", "string"],
            LeadProcessingReportApiController::END_DATE   => ["required", "string"],
        ];
    }
}
