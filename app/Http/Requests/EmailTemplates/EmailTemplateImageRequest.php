<?php

namespace App\Http\Requests\EmailTemplates;

use App\Enums\PermissionType;
use App\Http\Controllers\API\EmailTemplates\EmailTemplatesAPIController;
use App\Models\EmailTemplate;
use App\Models\EmailTemplateBackground;
use App\Models\User;
use App\Services\EmailTemplates\EmailTemplateImageService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class EmailTemplateImageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->can(PermissionType::EMAIL_TEMPLATE->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $templateType = $this->input(EmailTemplatesAPIController::REQUEST_TEMPLATE_TYPE);

        $table = '';
        $id = '';
        if($templateType === EmailTemplatesAPIController::TEMPLATE_TYPE_CONTENT) {
            $table = EmailTemplate::TABLE;
            $id = EmailTemplate::FIELD_ID;
        }
        else if($templateType === EmailTemplatesAPIController::TEMPLATE_TYPE_BACKGROUND) {
            $table = EmailTemplateBackground::TABLE;
            $id = EmailTemplateBackground::FIELD_ID;
        }

        return [
            EmailTemplatesAPIController::REQUEST_IMAGE_NAME => ['required', 'string', 'regex:/^'.EmailTemplateImageService::IMAGE_NAME_REGEX."$/"],
            EmailTemplatesAPIController::REQUEST_IMAGE_DATA_URL => ['required', 'string', 'regex:'.EmailTemplateImageService::IMAGE_DATA_URL_REGEX],
            EmailTemplatesAPIController::REQUEST_TEMPLATE_ID => ['required', 'integer', Rule::exists($table, $id)],
            EmailTemplatesAPIController::REQUEST_TEMPLATE_TYPE => ['required', 'string', Rule::in([EmailTemplatesAPIController::TEMPLATE_TYPE_CONTENT, EmailTemplatesAPIController::TEMPLATE_TYPE_BACKGROUND])]
        ];
    }
}
