<?php

namespace App\Http\Requests\EmailTemplates;

use App\Enums\PermissionType;
use App\Enums\RoleType;
use App\Http\Controllers\API\EmailTemplates\EmailTemplatesAPIController;
use App\Models\EmailTemplateBackground;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class EmailTemplateBackgroundDuplicateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasRole(RoleType::ADMIN->value)
            || $user->can(PermissionType::EMAIL_TEMPLATE->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            EmailTemplatesAPIController::REQUEST_NAME           => [
                'string',
                'required',
                'max:255',
            ],
            EmailTemplatesAPIController::REQUEST_BACKGROUND_ID  => [
                'integer',
                'required',
                Rule::exists(EmailTemplateBackground::TABLE, EmailTemplateBackground::FIELD_ID),
            ],
        ];
    }
}
