<?php

namespace App\Http\Requests\EmailTemplates;

use App\Enums\EmailTemplateType;
use App\Enums\PermissionType;
use App\Models\EmailTemplate;
use App\Models\EmailTemplateBackground;
use App\Models\Odin\Industry;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class EmailTemplateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->can(PermissionType::EMAIL_TEMPLATE->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $templateType = EmailTemplateType::tryFrom($this->input('type'));

        $isCalculatorTemplate = $templateType === EmailTemplateType::STATUS_CALCULATOR_RESULTS;

        return [
            EmailTemplate::FIELD_NAME           => ['string', 'required'],
            EmailTemplate::FIELD_SUBJECT        => ['string', 'required'],
            EmailTemplate::FIELD_CONTENT        => ['string', 'required'],
            EmailTemplate::FIELD_PERSONAL       => ['boolean', 'required'],
            EmailTemplate::FIELD_TYPE           => [Rule::enum(EmailTemplateType::class)],
            EmailTemplate::FIELD_INDUSTRY_ID    => [
                'integer',
                'nullable',
                Rule::exists(Industry::TABLE, Industry::FIELD_ID),
            ],
            EmailTemplate::FIELD_BACKGROUND_ID  => [
                'integer',
                'nullable',
                Rule::exists(EmailTemplateBackground::TABLE, EmailTemplateBackground::FIELD_ID)
            ],
            ...($isCalculatorTemplate ? [EmailTemplate::FIELD_ENGINE         => ['string', 'sometimes']] : []),
            EmailTemplate::FIELD_ACTIVE         => ['boolean', 'sometimes'],
            EmailTemplate::FIELD_PAYLOAD        => ['sometimes', 'array'],
        ];
    }
}
