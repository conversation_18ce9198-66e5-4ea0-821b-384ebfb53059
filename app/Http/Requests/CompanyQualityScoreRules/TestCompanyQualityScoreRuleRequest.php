<?php

namespace App\Http\Requests\CompanyQualityScoreRules;

use App\Http\Requests\Rulesets\StoreRulesetRequest;
use Illuminate\Foundation\Http\FormRequest;

class TestCompanyQualityScoreRuleRequest extends FormRequest
{
    const FIELD_COMPANY_IDS  = "company_ids";
    const FIELD_RULESET      = "ruleset";

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
       return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $rulesetSaveRules = $this->getRulesetSaveRules();

        return [
            self::FIELD_COMPANY_IDS          => "required|array|max:20",
            self::FIELD_COMPANY_IDS . ".*"   => "required|integer",

            self::FIELD_RULESET => "required|array",
            ...$rulesetSaveRules
        ];
    }


    /**
     * Get ruleset save rules
     * @return array
     */
    private function getRulesetSaveRules(): array
    {
        $rulesetRules = [];

        foreach((new StoreRulesetRequest())->rules() as $ruleKey => $ruleValue) {
            $rulesetRules[self::FIELD_RULESET . "." . $ruleKey] = $ruleValue;
        }

        return $rulesetRules;
    }
}
