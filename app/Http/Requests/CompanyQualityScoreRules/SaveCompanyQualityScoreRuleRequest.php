<?php

namespace App\Http\Requests\CompanyQualityScoreRules;

use App\Enums\PermissionType;
use App\Models\Odin\Industry;
use App\Models\Ruleset;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class SaveCompanyQualityScoreRuleRequest extends FormRequest
{
    const FIELD_INDUSTRY_ID = "industry_id";
    const FIELD_NAME = "name";
    const FIELD_IS_PRODUCTION = "is_production";
    const FIELD_TOTAL_POINTS = "total_points";
    const FIELD_RULE_ID = "rule_id";

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::COMPANY_QUALITY_SCORE_MANAGEMENT->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::FIELD_RULE_ID              => "required|integer|exists:" . Ruleset::TABLE . "," . Ruleset::FIELD_ID,
            self::FIELD_NAME                 => "required",
            self::FIELD_IS_PRODUCTION        => "required|boolean",
        ];
    }
}
