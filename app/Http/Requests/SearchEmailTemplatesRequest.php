<?php

namespace App\Http\Requests;

use App\Models\Bundle;
use App\Models\EmailTemplate;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class SearchEmailTemplatesRequest extends FormRequest
{
    const string REQUEST_SEARCH     = 'search';
    const string REQUEST_FILTERS    = 'filters';
    const string REQUEST_PER_PAGE   = 'per_page';
    const string REQUEST_PAGE       = 'page';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        //todo
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::REQUEST_SEARCH        => 'nullable|sometimes|string',
            self::REQUEST_FILTERS       => 'array',
            self::REQUEST_PER_PAGE      => 'numeric|nullable',
            self::REQUEST_PAGE          => 'numeric|nullable',
        ];
    }

}