<?php

namespace App\Http\Requests;

use App\Repositories\Legacy\LegacyUserRepository;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Gate;
use Illuminate\Validation\UnauthorizedException;

class SearchCompaniesByNameOrIdRequest extends FormRequest
{
    const QUERY_FIELD = 'query';

    /**
     * Determine if the user is authorized to make this request.
     * @TODO refactor checkUserCompanyIsAdmin to be used in guards and policies rather than controllers
     * @param LegacyUserRepository $legacyUserRepository
     * @return bool
     */
    public function authorize(LegacyUserRepository $legacyUserRepository): bool
    {
        // TODO: Investigate company specific permissions.
        return Gate::check("dashboard");
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::QUERY_FIELD => ['required','string','min:3']
        ];
    }
}
