<?php

namespace App\Http\Requests\ContactIdentification;

use App\Enums\ContactIdentification\IdentifiableModelType;
use Exception;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class NominateContactRequest extends FormRequest
{
    const FIELD_CONTACT_ID = 'contact_id';
    const FIELD_IDENTIFIED_CONTACT_ID   = 'identified_contact_id';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     * @throws Exception
     */
    public function rules(): array
    {
        return [
            self::FIELD_CONTACT_ID              => 'required|int',
            self::FIELD_IDENTIFIED_CONTACT_ID   => 'required|int',
            self::FIELD_MODEL_TYPE              => 'required|string|' . Rule::in(IdentifiableModelType::values()),
            self::FIELD_MODEL_ID                => 'required|int',
        ];
    }
}
