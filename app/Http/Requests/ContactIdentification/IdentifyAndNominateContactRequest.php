<?php

namespace App\Http\Requests\ContactIdentification;

use App\Enums\ContactIdentification\ContactType;
use App\Enums\ContactIdentification\IdentifiableModelType;
use App\Enums\ContactIdentification\SearchableFieldType;
use Exception;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class IdentifyAndNominateContactRequest extends FormRequest
{
    const FIELD_CONTACT_RELATION_ID   = 'contact_relation_id';
    const FIELD_CONTACT_RELATION_TYPE = 'contact_relation_type';
    const FIELD_IDENTIFIER_VALUE      = 'identifier_value';
    const FIELD_IDENTIFIER_FIELD_TYPE = 'identifier_field_type';
    const FIELD_MODEL_TYPE            = 'model_type';
    const FIELD_MODEL_ID              = 'model_id';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     * @throws Exception
     */
    public function rules(): array
    {
        return [
            self::FIELD_CONTACT_RELATION_ID     => 'required|int',
            self::FIELD_CONTACT_RELATION_TYPE   => 'required|string|' . Rule::in(ContactType::values()),
            self::FIELD_IDENTIFIER_VALUE        => 'required|string',
            self::FIELD_IDENTIFIER_FIELD_TYPE   => 'required|string|' . Rule::in(SearchableFieldType::values()),
            self::FIELD_MODEL_TYPE              => 'required|string|' . Rule::in(IdentifiableModelType::values()),
            self::FIELD_MODEL_ID                => 'required|int',
        ];
    }
}
