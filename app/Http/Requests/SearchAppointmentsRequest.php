<?php

namespace App\Http\Requests;

use App\Enums\Odin\ProductAssignmentStatus;
use App\Enums\Odin\QualityTier;
use App\Http\Controllers\API\Appointments\AppointmentController;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SearchAppointmentsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        //TODO: permissions
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            AppointmentController::REQUEST_PER_PAGE  => ['required', 'integer', 'min:1'],
            AppointmentController::REQUEST_APPOINTMENT_ID  => ['sometimes', 'nullable', 'integer'],
            AppointmentController::REQUEST_CAMPAIGN_ID  => ['sometimes', 'nullable', 'integer'],
            AppointmentController::REQUEST_STATUS  => ['sometimes', 'nullable', 'string', Rule::in(array_keys(ProductAssignmentStatus::all()))],
            AppointmentController::REQUEST_INVOICE_ID  => ['sometimes', 'nullable', 'integer'],
            AppointmentController::REQUEST_CONTACT_SEARCH  => ['sometimes', 'nullable', 'string'],
            AppointmentController::REQUEST_ADDRESS_SEARCH  => ['sometimes', 'nullable', 'string'],
            AppointmentController::REQUEST_STATE_ABBR  => ['sometimes', 'nullable', 'string'],
            AppointmentController::REQUEST_CITY  => ['sometimes', 'nullable', 'string'],
            AppointmentController::REQUEST_ZIP  => ['sometimes', 'nullable', 'string'],
            AppointmentController::REQUEST_START_DATE  => ['sometimes', 'nullable', 'integer'],
            AppointmentController::REQUEST_END_DATE  => ['sometimes', 'nullable', 'integer'],
            AppointmentController::REQUEST_APPOINTMENT_CATEGORY  => ['sometimes', 'nullable', 'string', Rule::in([QualityTier::IN_HOME->value, QualityTier::ONLINE->value])]
        ];
    }
}
