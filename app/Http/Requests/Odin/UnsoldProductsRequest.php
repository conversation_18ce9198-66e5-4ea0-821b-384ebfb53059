<?php

namespace App\Http\Requests\Odin;

use App\Enums\Odin\SaleTypes;
use App\Models\MissedProducts\MissedProduct;
use App\Models\MissedProducts\OpportunityNotificationConfig;
use App\Models\Odin\Company;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ServiceProduct;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UnsoldProductsRequest extends FormRequest
{
    const REQUEST_COMPANY_ID = 'companyId';
    const REQUEST_COMPANY_REF = 'company_ref';
    const REQUEST_INDUSTRY = 'industry';
    const REQUEST_SERVICE = 'service';
    const REQUEST_DATE_RANGE = 'date_range';
    const REQUEST_PRICE_FROM = 'price_from';
    const REQUEST_PRICE_TO = 'price_to';
    const REQUEST_CITY = 'city';
    const REQUEST_PRICE_TYPE = 'price_type';
    const REQUEST_INCLUDE_SOLD_COUNT = 'include_sold_count';
    const REQUEST_PRODUCT_IDS = 'product_ids';
    const REQUEST_PRODUCT_TYPE = 'product_type';
    const REQUEST_INDUSTRY_SERVICE_ID = 'industry_service_id';
    const REQUEST_SERVICE_PRODUCT_ID = 'service_product_id';
    const REQUEST_PER_PAGE = 'per_page';
    const REQUEST_PAGE = 'page';
    const REQUEST_ZIP_CODE = 'zip_code';
    const REQUEST_OPPORTUNITY_CONFIG_UUID = 'opportunity_config_uuid';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::REQUEST_PRICE_TO              => ['sometimes', 'numeric', 'nullable'],
            self::REQUEST_PRICE_FROM            => ['sometimes', 'numeric', 'nullable'],
            self::REQUEST_DATE_RANGE            => ['sometimes', 'array', 'nullable'],
            self::REQUEST_CITY                  => ['sometimes', 'string', 'nullable'],
            self::REQUEST_INCLUDE_SOLD_COUNT    => ['sometimes', 'boolean', 'nullable'],
            self::REQUEST_PER_PAGE              => ['sometimes', 'numeric', 'nullable'],
            self::REQUEST_PAGE                  => ['sometimes', 'numeric', 'nullable'],
            self::REQUEST_ZIP_CODE              => ['sometimes', 'string', 'nullable'],
            self::REQUEST_PRICE_TYPE            => [
                'sometimes',
                'string',
                'nullable',
                Rule::in(SaleTypes::allSaleTypeKeys())
            ],
            self::REQUEST_COMPANY_ID            => [
                'sometimes',
                'numeric',
                'nullable',
                Rule::exists(Company::TABLE, Company::FIELD_ID),
            ],
            self::REQUEST_COMPANY_REF            => [
                'sometimes',
                'string',
                'nullable',
                Rule::exists(Company::TABLE, Company::FIELD_REFERENCE),
            ],
            self::REQUEST_INDUSTRY              => [
                'sometimes',
                'string',
                'nullable',
                Rule::exists(Industry::TABLE, Industry::FIELD_SLUG),
            ],
            self::REQUEST_SERVICE               => [
                'sometimes',
                'string',
                'nullable',
                Rule::exists(IndustryService::TABLE, IndustryService::FIELD_SLUG),
            ],
            self::REQUEST_INDUSTRY_SERVICE_ID   => [
                'sometimes',
                'numeric',
                'nullable',
                Rule::exists(IndustryService::TABLE, IndustryService::FIELD_ID),
            ],
            self::REQUEST_SERVICE_PRODUCT_ID    => [
                'sometimes',
                'numeric',
                'nullable',
                Rule::exists(ServiceProduct::TABLE, ServiceProduct::FIELD_ID),
            ],
            self::REQUEST_PRODUCT_IDS . '*'     => [
                'sometimes',
                'numeric',
                'nullable',
                Rule::exists(MissedProduct::TABLE, MissedProduct::FIELD_ID),
            ],
            self::REQUEST_OPPORTUNITY_CONFIG_UUID  => [
                'sometimes',
                'string',
                Rule::exists(OpportunityNotificationConfig::TABLE, OpportunityNotificationConfig::FIELD_UUID)
            ],
        ];
    }

    /**
     * @return void
     */
    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                self::REQUEST_COMPANY_ID    => $this->route()->parameter(self::REQUEST_COMPANY_ID),
                self::REQUEST_INDUSTRY      => $this->route()->parameter(self::REQUEST_INDUSTRY),
                self::REQUEST_SERVICE       => $this->route()->parameter(self::REQUEST_SERVICE),
            ]
        );
    }
}
