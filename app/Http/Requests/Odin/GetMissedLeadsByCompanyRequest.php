<?php

namespace App\Http\Requests\Odin;

use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class GetMissedLeadsByCompanyRequest extends FormRequest
{
    const string FIELD_STATES     = 'states';
    const string FIELD_COUNTIES   = 'counties';
    const string FIELD_ZIPCODE    = 'zipcode';
    const string FIELD_DATE_RANGE = 'date_range';

    const string FIELD_PER_PAGE = 'per_page';
    const string FIELD_PAGE     = 'page';

    /**
     * Determine if the user is authorized to make this request.
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::COMPANY->value);
    }

    /**
     * Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::FIELD_STATES     => ['sometimes', 'array'],
            self::FIELD_COUNTIES   => ['sometimes', 'array'],
            self::FIELD_DATE_RANGE => ['sometimes', 'array'],
            self::FIELD_ZIPCODE    => ['sometimes', 'string'],
            self::FIELD_PER_PAGE   => ['sometimes', 'numeric'],
            self::FIELD_PAGE       => ['sometimes', 'numeric'],
        ];
    }

}
