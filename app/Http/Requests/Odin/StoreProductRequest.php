<?php

namespace App\Http\Requests\Odin;

use App\Models\Odin\Product;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo('industry-management');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $product = $this->route()->parameter('product');
        $unique  = Rule::unique(Product::TABLE);

        if ($product instanceof Product) $unique = Rule::unique(Product::TABLE)->ignore($product->id);

        return [
            Product::FIELD_NAME  => [
                'required',
                'max:255',
                $unique
            ]
        ];
    }
}
