<?php

namespace App\Http\Requests\Odin\Engines;

use App\Models\Legacy\EloquentUtility;
use App\Services\DatabaseHelperService;
use Illuminate\Foundation\Http\FormRequest;

class LegacySolarEngineRequest extends FormRequest
{
    const FIELD_ZIP_CODE            = 'zip_code';
    const FIELD_UTILITY_UUID        = 'utility_uuid';
    const FIELD_MONTHLY_BILL        = 'monthly_bill';
    const FIELD_TARGET_USAGE_OFFSET = 'target_usage_offset';
    const FIELD_AZIMUTH             = 'azimuth';
    const FIELD_TILT                = 'tilt';

    // These mins / maxs are practical but opinionated. Can be changed
    const MONTHLY_BILL_MIN        = '50';
    const MONTHLY_BILL_MAX        = '600';
    const TARGET_USAGE_OFFSET_MIN = '1';
    const TARGET_USAGE_OFFSET_MAX = '110';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::FIELD_ZIP_CODE            => 'required|digits:5',
            self::FIELD_UTILITY_UUID        => 'string',
            self::FIELD_MONTHLY_BILL        => 'required|integer|between:' . self::MONTHLY_BILL_MIN .','. self::MONTHLY_BILL_MAX,
            self::FIELD_TARGET_USAGE_OFFSET => 'integer|between:' . self::TARGET_USAGE_OFFSET_MIN .','. self::TARGET_USAGE_OFFSET_MAX,
            self::FIELD_AZIMUTH             => 'integer|between:0,359',
            self::FIELD_TILT                => 'integer|between:0,90'
        ];
    }
}
