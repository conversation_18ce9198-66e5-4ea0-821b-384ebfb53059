<?php

namespace App\Http\Requests\Odin;

use App\Http\Controllers\API\CompaniesController;
use App\Models\Odin\Company;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class CreateBaseCompanyFormRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(CompaniesController::MODULE_PERMISSION);
    }

    /**
     * Get the validation rules that apply to the request
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return
        [
            CompaniesController::REQUEST_COMPANY_NAME               => "string|required|unique:" . Company::TABLE . "," . Company::FIELD_NAME,
            CompaniesController::REQUEST_COMPANY_ENTITY_NAME        => "string|required",
            CompaniesController::REQUEST_COMPANY_WATCHDOG_ID        => "string|nullable",
            CompaniesController::REQUEST_COMPANY_WEBSITE            => "string|nullable",
            CompaniesController::REQUEST_COMPANY_INDUSTRIES. '.*'   => "integer|nullable|exists:" . Industry::TABLE . ',' . Industry::FIELD_ID,
            CompaniesController::REQUEST_COMPANY_SERVICES. '.*'     => "integer|nullable|exists:" . IndustryService::TABLE . ',' . IndustryService::FIELD_ID,
        ];
    }


}
