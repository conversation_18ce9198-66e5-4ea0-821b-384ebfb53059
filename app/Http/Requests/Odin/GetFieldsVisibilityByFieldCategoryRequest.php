<?php

namespace App\Http\Requests\Odin;

use App\Enums\Odin\ConsumerFieldType;
use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class GetFieldsVisibilityByFieldCategoryRequest extends FormRequest
{
    const FIELD_CATEGORY = 'category';
    const FIELD_CATEGORY_ID = 'category_id';

    /**
     * Determine if the user is authorized to make this request.
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->can(PermissionType::INDUSTRY_MANAGEMENT->value);
    }

    /**
     * Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::FIELD_CATEGORY        => ['required', Rule::in(ConsumerFieldType::getValues())],
            self::FIELD_CATEGORY_ID     => ['required'],
        ];
    }
}
