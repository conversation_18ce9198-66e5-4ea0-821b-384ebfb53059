<?php

namespace App\Http\Requests\Odin;

use Illuminate\Foundation\Http\FormRequest;

class SyncCampaignNameOdinRequest extends FormRequest
{
    const FIELD_LEGACY_CAMPAIGN_REFERENCE = 'legacy_campaign_reference';

    const FIELD_NAME = 'name';

    /**
     * Determine if the user is authorized to make this request.
     * @return bool
     */
    public function authorize(): true
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::FIELD_LEGACY_CAMPAIGN_REFERENCE   => 'required|string',
            self::FIELD_NAME                        => 'required|string',
        ];
    }

}
