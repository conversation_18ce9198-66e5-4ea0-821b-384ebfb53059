<?php

namespace App\Http\Requests\Odin;

use App\Enums\PermissionType;
use App\Http\Controllers\CompanyCampaignWizardController;
use App\Http\Requests\Odin\v2\BaseOdinRequest;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class CompanyCampaignSearchRequest extends BaseOdinRequest
{
    /**
     * Determine if the user is authorized to make this request.
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::CAMPAIGNS_VIEW->value);
    }

    /**
     * Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            CompanyCampaign::FIELD_STATUS                               => ['numeric', 'nullable'],
            self::REQUEST_PAGE                                          => ['numeric', 'nullable'],
            self::REQUEST_PER_PAGE                                      => ['numeric', 'nullable'],
            self::REQUEST_INDUSTRY                                      => ['string'],
            self::REQUEST_INDUSTRIES                                    => ['string', 'array'],
            self::REQUEST_PRODUCT                                       => ['string'],
            CompanyCampaignWizardController::REQUEST_ALL_CAMPAIGNS_LIST => ['nullable', 'boolean'],
        ];
    }

}
