<?php
namespace App\Http\Requests\Odin;

use Illuminate\Foundation\Http\FormRequest;

class GetCompanyReviewsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    public function prepareForValidation(): void
    {
        if ($this->has('company_id')) {
            $this->merge([
                'company_id' =>  (int)$this->company_id,
            ]);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $paginationRules = [
            'page'                     => 'nullable|numeric|min:1',
            'perPage'                  => 'nullable|numeric|min:1|max:100',
        ];

        return [
            ...$paginationRules,
            'company_id'                => 'nullable|integer',
            'reviewer_details'          => 'nullable|string',
        ];
    }
}
