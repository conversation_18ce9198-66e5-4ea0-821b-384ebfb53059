<?php

namespace App\Http\Requests\Odin;

use App\Enums\CompanyCampaignSource;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateTestLeadRequest extends FormRequest
{
    const FIELD_CAMPAIGN_ID = 'campaign_id';
    const FIELD_SOURCE      = 'source';
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        //todo create permission for test leads
        return true;
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::FIELD_CAMPAIGN_ID  => 'required|numeric',
            self::FIELD_SOURCE       => 'required|string|' . Rule::in(CompanyCampaignSource::getValues()),
        ];
    }
}
