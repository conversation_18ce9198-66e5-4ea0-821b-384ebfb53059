<?php

namespace App\Http\Requests\Odin;

use App\Exceptions\ValidatorException;
use App\Http\Controllers\API\LeadProcessing\LeadProcessingReportApiController;
use App\Models\Odin\ConsumerProduct;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;


class ReserveUnsoldProductsRequest extends FormRequest
{
    const REQUEST_COMPANY_ID = 'companyId';
    const REQUEST_SELECTED_LEADS = 'selectedLeads';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            self::REQUEST_COMPANY_ID => ['required', 'numeric'],
            self::REQUEST_SELECTED_LEADS => ['required', 'array'],
            self::REQUEST_SELECTED_LEADS . '.*' =>  [
                Rule::exists(ConsumerProduct::TABLE,ConsumerProduct::FIELD_ID)->where(function ($query) {
                    $query->where(ConsumerProduct::FIELD_STATUS, '=', ConsumerProduct::STATUS_UNSOLD);
                }),
            ]
        ];
    }
}
