<?php

namespace App\Http\Requests\Odin;

use App\Models\Odin\Address;
use App\Models\Odin\CompanyLocation;
use App\Rules\Phone;
use Illuminate\Foundation\Http\FormRequest;

class GetCompanyUsersRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    public function prepareForValidation(): void
    {
        if ($this->has('user_status')) {
            $this->merge([
                'user_status' =>  $this->user_status === '1',
            ]);
        }
        if ($this->has('decision_maker')) {
            $this->merge([
                'decision_maker' =>  $this->decision_maker === '1',
            ]);
        }
        if ($this->has('company_id')) {
            $this->merge([
                'company_id' =>  (int)$this->company_id,
            ]);
        }
        if ($this->has('last_contacted')) {
            $this->merge([
                'last_contacted' => json_decode($this->input('last_contacted'), true)
            ]);
        }
        if ($this->has('total_calls_range')) {
            $this->merge([
                'total_calls_range' => json_decode($this->input('total_calls_range'), true)
            ]);
        }

    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $paginationRules = [
            'page'                     => 'nullable|numeric|min:1',
            'perPage'                  => 'nullable|numeric|min:1|max:100',
        ];

        return [
            ...$paginationRules,

            'decision_maker'            => 'nullable|bool',
            'user_status'               => 'nullable|bool',
            'total_calls_range'          => 'nullable|array',
            'user_title'                => 'nullable|array',
            'last_contacted'            => 'nullable|array',
            'company_id'                => 'nullable|integer',
            'user_details'              => 'nullable|string',
        ];
    }
}
