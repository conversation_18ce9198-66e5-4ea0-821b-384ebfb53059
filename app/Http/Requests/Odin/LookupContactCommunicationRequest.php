<?php

namespace App\Http\Requests\Odin;

use App\Models\Odin\Address;
use App\Models\Odin\CompanyLocation;
use App\Rules\Phone;
use Illuminate\Foundation\Http\FormRequest;

class LookupContactCommunicationRequest extends FormRequest
{
    const FIELD_OTHER = 'other';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::FIELD_OTHER => 'required|string',
        ];
    }
}
