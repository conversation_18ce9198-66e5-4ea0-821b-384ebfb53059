<?php

namespace App\Http\Requests\Odin;

use App\Http\Controllers\API\Users\Management\UserManagementAPIController;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreUserRequest extends FormRequest
{
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasRole('admin');
    }

    /**
     * Get the validation rules that apply to the request
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $user = $this->route()->parameter('user');
        $unique = !($user instanceof User) ? 'unique:users,email' : '';

        return [
            UserManagementAPIController::REQUEST_NAME => ['required', 'string', 'max:255'],
            UserManagementAPIController::REQUEST_EMAIL => ['required', 'string', 'email', 'max:255', $unique],
            UserManagementAPIController::REQUEST_LEGACY_USER_ID => ['exclude_if:'.UserManagementAPIController::REQUEST_CREATE_LEGACY_USER.',true', 'required', 'integer'],
            UserManagementAPIController::REQUEST_ROLES=> ['required', 'array', 'min:1'],
            UserManagementAPIController::REQUEST_PHONE => ['sometimes', 'integer'],
            UserManagementAPIController::REQUEST_CREATE_LEGACY_USER => ['required', 'bool'],
            UserManagementAPIController::REQUEST_FORCE_TWO_FACTOR => ['required', 'bool'],
            UserManagementAPIController::REQUEST_SLACK_USERNAME => ['nullable', 'string', 'max:255'],
        ];
    }

    public function messages(): array
    {
        return [
            UserManagementAPIController::REQUEST_EMAIL.'.unique' => 'The user is already registered.',
            UserManagementAPIController::REQUEST_LEGACY_USER_ID.'.integer' => 'Legacy User ID must be a number.',
            UserManagementAPIController::REQUEST_LEGACY_USER_ID.'.exclude_if' => 'You must supply a legacy user id or create one',
            UserManagementAPIController::REQUEST_ROLES.'.required' => 'At least one role must be selected.',
        ];
    }

}
