<?php

namespace App\Http\Requests\Odin;

use App\Enums\Odin\GlobalConfigurableFields;
use App\Models\Odin\Consumer;
use App\Models\User;
use App\Rules\Phone;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class StoreConsumerProductContactInfoRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo('lead-processing');
    }

    /**
     * Get the validation rules that apply to the request
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            Consumer::FIELD_FIRST_NAME                      => 'string',
            Consumer::FIELD_LAST_NAME                       => 'string|nullable',
            Consumer::FIELD_EMAIL                           => 'string|email|nullable',
            Consumer::FIELD_PHONE                           => [new Phone()],
            GlobalConfigurableFields::OWN_PROPERTY->value   => 'in:yes,no',
        ];
    }


}
