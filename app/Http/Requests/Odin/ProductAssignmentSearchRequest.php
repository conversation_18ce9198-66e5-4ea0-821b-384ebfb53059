<?php

namespace App\Http\Requests\Odin;

use Illuminate\Foundation\Http\FormRequest;

/**
 *
 */
class ProductAssignmentSearchRequest extends FormRequest
{
    const string DATE_FROM                  = 'date_from';
    const string DATE_TO                    = 'date_to';
    const string TEXT_SEARCH_NAME_EMAIL     = 'name_email';
    const string TEXT_SEARCH_STATE_ZIP_CODE = 'state_zip_code';
    const string INVOICE_ID                 = 'invoice_id';
    const string CAMPAIGN_REFERENCE         = 'campaign_reference';
    const string PRODUCT_ASSIGNMENT_ID      = 'product_assignment_id';
    const string STATUS                     = 'status';
    const string OVER_BUDGET                = 'over_budget';
    const string PAGE                       = 'page';
    const string PER_PAGE                   = 'per_page';
    const string DATE_RANGE                 = 'date_range';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::DATE_FROM                  => 'date|nullable',
            self::DATE_TO                    => 'date|nullable',
            self::TEXT_SEARCH_NAME_EMAIL     => 'string|nullable',
            self::TEXT_SEARCH_STATE_ZIP_CODE => 'string|nullable',
            self::INVOICE_ID                 => 'numeric|nullable',
            self::CAMPAIGN_REFERENCE         => 'string|nullable',
            self::STATUS                     => 'string|nullable',
            self::OVER_BUDGET                => 'boolean|nullable',
            self::PAGE                       => 'numeric|nullable',
            self::PER_PAGE                   => 'numeric|nullable',
            self::PRODUCT_ASSIGNMENT_ID      => 'numeric|nullable',
            self::DATE_RANGE                 => 'array|nullable',
        ];
    }
}
