<?php

namespace App\Http\Requests\Odin\API;

use Illuminate\Foundation\Http\FormRequest;

class CompanyLocationSiloEntryRequest extends FormRequest
{
    const FIELD_REQUEST_FULL_PATH = 'request_full_path';

    const FIELD_LOCATION_SILO_PAGE_ID = 'location_silo_page_id';
    const FIELD_COLLECTION_HANDLE     = 'collection_handle';
    const FIELD_ENTRY_SLUG            = 'entry_slug';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::FIELD_REQUEST_FULL_PATH => ['required', 'string']
        ];
    }
}
