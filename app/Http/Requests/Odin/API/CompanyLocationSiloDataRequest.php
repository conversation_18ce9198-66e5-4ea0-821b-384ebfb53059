<?php

namespace App\Http\Requests\Odin\API;

use Illuminate\Foundation\Http\FormRequest;

class CompanyLocationSiloDataRequest extends FormRequest
{
    const FIELD_ID                                    = 'id';
    const FIELD_INDUSTRIES                            = 'industries';
    const FIELD_INDUSTRIES_LOGIC                      = 'industries_logic';
    const FIELD_SERVICES                              = 'services';
    const FIELD_SERVICES_LOGIC                        = 'services_logic';
    const FIELD_COMPONENT_DATA_REQUESTS               = 'component_data_requests';
    const FIELD_TOTAL_COMPANY_LIMIT                   = 'total_company_limit';
    const FIELD_MINIMUM_COMPANY_COUNT                 = 'minimum_company_count';
    const FIELD_MANUAL_COMPANY_IDS                    = 'manual_company_ids';
    const FIELD_SORTING_LOGIC                         = 'sorting_logic';
    const REQUIRE_ACTIVE_CAMPAIGN_SERVICE_AREA        = 'require_active_campaign_service_areas';
    const FIELD_REQUIRE_ACTIVE_BUYERS                 = 'require_active_buyers';
    const FIELD_REQUIRE_ACTIVE_BUYERS_IN_FALLBACK     = 'require_active_buyers_in_fallback';
    const FIELD_REQUIRE_FILTERS_IN_FALLBACK           = 'require_filters_in_fallback';
    const FIELD_EXCLUDED_COMPANY_IDS                  = 'excluded_company_ids';
    const FIELD_EXCLUDE_USED_COMPANIES                = 'exclude_used_companies';
    const FIELD_REQUIRE_LATEST_REVIEWS                = 'require_latest_reviews';
    const REQUEST_FIELD_REQUIRE_EXPERT_RATING_FACTORS = 'require_expert_rating_factors';
    const REQUEST_FIELD_REQUIRE_OFFICE_LOCATIONS      = 'require_office_locations';
    const FIELD_EXPERT_RATING_FILTER                  = 'expert_rating_filter';
    const FIELD_EMPLOYEE_COUNT_FILTER                 = 'employee_count_filter';
    const FIELD_ESTIMATED_REVENUE_FILTER              = 'estimated_revenue_filter';
    const FIELD_CONSUMER_RATING_FILTER                = 'consumer_rating_filter';
    const FIELD_CONSUMER_REVIEW_COUNT_FILTER          = 'consumer_review_count_filter';
    const FIELD_COST_OF_PRODUCTS_PURCHASED_FILTER     = 'cost_of_products_purchased_filter';
    const FIELD_FALLBACK_RATING_FILTER                = 'fallback_rating_filter';
    const FIELD_FALLBACK_REVIEW_COUNT_FILTER          = 'fallback_review_count_filter';
    const FIELD_NAME_FILTER                           = 'name_filter';

    const FILTER_OPTIONS = [
      self::FIELD_EXPERT_RATING_FILTER,
      self::FIELD_EMPLOYEE_COUNT_FILTER,
      self::FIELD_ESTIMATED_REVENUE_FILTER,
      self::FIELD_CONSUMER_RATING_FILTER,
      self::FIELD_CONSUMER_REVIEW_COUNT_FILTER,
      self::FIELD_COST_OF_PRODUCTS_PURCHASED_FILTER,
      self::FIELD_FALLBACK_RATING_FILTER,
      self::FIELD_FALLBACK_REVIEW_COUNT_FILTER,
    ];

    const SORTING_LOGIC_COST_OF_PRODUCTS_PURCHASED       = 'cost_of_products_purchased';
    const SORTING_LOGIC_VOLUME_OF_PRODUCTS_PURCHASED     = 'volume_of_products_purchased';
    const SORTING_LOGIC_EXPERT_RATING                    = 'expert_rating';
    const SORTING_LOGIC_CONTRACTOR_PROFILE_EXPERT_RATING = 'contractor_expert_rating';
    const SORTING_LOGIC_FALLBACK_RATING                  = 'fallback_rating';
    const SORTING_LOGIC_EMPLOYEE_COUNT                   = 'employee_count';
    const SORTING_LOGIC_CONSUMER_RATING                  = 'consumer_rating';
    const SORTING_LOGIC_ESTIMATED_REVENUE                = 'estimated_revenue';
    const SORTING_LOGIC_REVIEW_COUNT                     = 'consumer_review_count';

    const FIELD_LOCATION_SILO_PAGE_ID                    = 'location_silo_page_id';
    const FIELD_INDUSTRY_SLUG                            = 'industry_slug';
    const FIELD_FLOW_BUILDER_FLOW                        = 'flow_builder_flow';
    const FIELD_FLOW_BUILDER_REVISION                    = 'flow_builder_revision';
    const FIELD_SHOW_PARTNERS_FIRST                      = 'show_partners_first';
    const FIELD_QUOTE_REDIRECT_URL                       = 'quote_redirect_url';
    const FIELD_FIXR_PROFILE_REQUIRED                    = 'fixr_profile_required';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::FIELD_LOCATION_SILO_PAGE_ID   => ['required', 'numeric'],
            self::FIELD_COMPONENT_DATA_REQUESTS => 'array',
        ];
    }
}
