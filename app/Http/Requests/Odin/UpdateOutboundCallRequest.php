<?php

namespace App\Http\Requests\Odin;

use App\Models\Call;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateOutboundCallRequest extends FormRequest
{
    const SERVICE_NAME       = 'service_name';
    const USER_PHONE_NUMBER  = 'user_phone_number';
    const OTHER_PHONE_NUMBER = 'other_phone_number';
    const EXTERNAL_REFERENCE = 'external_reference';
    const CALL_RESULT        = 'call_result';
    const RELATION_TYPE      = 'relation_type';
    const RELATION_ID        = 'relation_id';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::SERVICE_NAME       => ['required', Rule::in(Call::EXTERNAL_TYPES)],
            self::USER_PHONE_NUMBER  => ['required'],
            self::OTHER_PHONE_NUMBER => ['required'],
            self::EXTERNAL_REFERENCE => ['required'],
            self::CALL_RESULT        => ['required', Rule::in(Call::RESULTS)],
            self::RELATION_TYPE      => ['string','nullable'],
            self::RELATION_ID        => ['numeric','nullable'],
        ];
    }
}
