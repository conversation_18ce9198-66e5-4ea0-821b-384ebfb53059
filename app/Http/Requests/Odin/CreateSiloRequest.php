<?php

namespace App\Http\Requests\Odin;

use App\Enums\Odin\LocationSiloPageLocationType;
use App\Http\Controllers\SiloManagementController;
use App\Models\Odin\LocationSiloPage;
use App\Models\Odin\Silo;
use App\Models\User;
use App\Services\Odin\SiloService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class CreateSiloRequest extends FormRequest
{
    const ENTRY_SLUG_COUNTRY    = 'entry_slug_country';
    const ENTRY_SLUG_STATE      = 'entry_slug_state';
    const ENTRY_SLUG_CITY       = 'entry_slug_city';
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(SiloManagementController::PERMISSION_CREATE);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $locationTypeValues = array_map(fn($case) => $case->value, LocationSiloPageLocationType::cases());

        return [
            Silo::FIELD_NAME                        => ['string', 'min:3', 'max:128'],
            Silo::FIELD_ROOT_PATH                   => ['string', 'min:1', 'max:64'],
            Silo::FIELD_COLLECTION_HANDLE           => ['string', 'min:1', 'max:64'],
            Silo::FIELD_WEBSITE_ID                  => ['numeric', 'required'],
            Silo::FIELD_INDUSTRY_ID                 => ['numeric', 'required'],
            Silo::FIELD_INDUSTRY_SERVICE_ID         => ['numeric', 'nullable'],
            self::ENTRY_SLUG_COUNTRY                => ['string', 'min:1', 'max:64', 'required'],
            self::ENTRY_SLUG_STATE                  => ['nullable', 'string', 'min:1', 'max:64', Rule::requiredIf(fn() => $this->get('location_type') > 1)],
            self::ENTRY_SLUG_CITY                   => ['nullable', 'string', 'min:1', 'max:64', Rule::requiredIf(fn() => $this->get('location_type') > 2)],
            LocationSiloPage::FIELD_LOCATION_TYPE   => ['numeric', Rule::in($locationTypeValues)],
            LocationSiloPage::FIELD_IS_ACTIVE       => ['boolean', 'nullable'],
            LocationSiloPage::FIELD_RELATIVE_PATH   => ['string', 'nullable', 'max:64'],
            SiloService::LOCATION_IDS               => ['array'],
            SiloService::LOCATION_IDS .'.*'         => ['numeric'],
            Silo::FIELD_FLOW_ID                     => ['string', 'nullable', 'max:64'],
            Silo::FIELD_REVISION_ID                 => ['string', 'nullable'], // uuid failing for v2_xxxx_xxxx_.. format
        ];
    }

    /**
     * @return void
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            Silo::FIELD_ROOT_PATH                   => $this->checkRootPrefix($this->{Silo::FIELD_ROOT_PATH}),
        ]);
    }

    /**
     * @param string $root
     * @return string
     */
    private function checkRootPrefix(string $root): string
    {
        return str_starts_with($root, "/")
            ? $root
            : "/$root";
    }
}
