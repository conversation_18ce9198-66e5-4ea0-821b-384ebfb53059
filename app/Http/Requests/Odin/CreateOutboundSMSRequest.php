<?php

namespace App\Http\Requests\Odin;

use App\Models\Call;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateOutboundSMSRequest extends FormRequest
{
    const OTHER_PHONE_NUMBER = 'other_phone_number';
    const SMS_BODY = 'sms_body';
    const RELATION_TYPE = 'relation_type';
    const RELATION_ID = 'relation_id';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::OTHER_PHONE_NUMBER => ['required'],
            self::SMS_BODY           => ['required'],
            self::RELATION_TYPE      => ['string','nullable'],
            self::RELATION_ID        => ['numeric','nullable'],
        ];
    }
}
