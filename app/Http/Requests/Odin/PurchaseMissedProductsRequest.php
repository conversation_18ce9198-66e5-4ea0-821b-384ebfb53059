<?php

namespace App\Http\Requests\Odin;

use App\Models\MissedProducts\MissedProduct;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;


class PurchaseMissedProductsRequest extends FormRequest
{
    const REQUEST_COMPANY_ID = 'company_id';
    const REQUEST_CAMPAIGN_ID = 'campaign_id';
    const REQUEST_PRODUCT_IDS = 'product_ids';
    const REQUEST_DEACTIVATE_CAMPAIGN = 'deactivate_campaign';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            self::REQUEST_CAMPAIGN_ID => ['required', 'string'],
            self::REQUEST_COMPANY_ID => ['sometimes', 'numeric', 'nullable'], // can be passed through route params
            self::REQUEST_PRODUCT_IDS => ['required', 'array' , Rule::exists(MissedProduct::TABLE,MissedProduct::FIELD_CONSUMER_PRODUCT_ID)],
            self::REQUEST_DEACTIVATE_CAMPAIGN => ['sometimes', 'boolean', 'nullable'],
        ];
    }
}
