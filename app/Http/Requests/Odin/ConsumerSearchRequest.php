<?php

namespace App\Http\Requests\Odin;

use App\Http\Controllers\API\ConsumerSearchController;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class ConsumerSearchRequest extends FormRequest
{
    const string REQUEST_FILTERS     = 'filters';
    const string REQUEST_SEARCH_ID   = 'search_id';
    const string REQUEST_SEARCH_TEXT = 'search_text';
    const string REQUEST_PER_PAGE    = 'per_page';
    const string REQUEST_PAGE        = 'page';
    const string REQUEST_SORT_DATE   = 'sort.date';

    /**
     * Determine if the user is authorized to make this request.
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(ConsumerSearchController::MODULE_PERMISSION);
    }

    /**
     * Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::REQUEST_FILTERS     => 'array',
            self::REQUEST_SEARCH_ID   => 'integer|nullable',
            self::REQUEST_SEARCH_TEXT => 'string|nullable',
            self::REQUEST_PER_PAGE    => 'numeric|nullable',
            self::REQUEST_PAGE        => 'numeric|nullable',
            self::REQUEST_SORT_DATE   => 'string|nullable|in:asc,desc',
        ];
    }

}
