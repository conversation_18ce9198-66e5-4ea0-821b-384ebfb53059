<?php

namespace App\Http\Requests\Odin;

use App\Enums\Odin\GlobalConfigurableFields;
use App\Models\Odin\ConsumerProduct;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class StoreConsumerProductBasicInfoRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo('lead-processing');
    }

    /**
     * Get the validation rules that apply to the request
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            ConsumerProduct::FIELD_CONTACT_REQUESTS                  => 'numeric',
            GlobalConfigurableFields::BEST_TIME_TO_CALL->value       => 'string|nullable',
            GlobalConfigurableFields::BEST_TIME_TO_CALL_OTHER->value => 'string|nullable',
        ];
    }


}
