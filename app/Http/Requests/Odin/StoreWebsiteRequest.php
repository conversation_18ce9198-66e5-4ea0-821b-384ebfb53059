<?php

namespace App\Http\Requests\Odin;

use App\Models\Odin\Website;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreWebsiteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo('industry-management');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $website = $this->route()->parameter('website');
        $unique  = Rule::unique(Website::TABLE);

        if ($website instanceof Website) $unique = Rule::unique(Website::TABLE)->ignore($website->id);

        return [
            Website::FIELD_NAME => 'required|string|max:255',
            Website::FIELD_URL  => [
                'required',
                'url',
                $unique
            ],
            Website::FIELD_ABBREVIATION => 'required|string|max:16',
            Website::FIELD_CP_DOMAIN => [
                'url'
            ]
        ];
    }
}
