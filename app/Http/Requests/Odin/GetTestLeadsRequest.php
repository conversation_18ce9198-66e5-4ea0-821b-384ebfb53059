<?php

namespace App\Http\Requests\Odin;

use App\Enums\TestProducts\ProductAuthorEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class GetTestLeadsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $paginationRules = [
            'page'      => 'nullable|numeric|min:1',
            'per_page'  => 'nullable|numeric|min:1|max:100',
        ];

        return [
            ...$paginationRules,

            'company_name'   => 'nullable|string',
            'campaign_name'  => 'nullable|string',
            'industry_types' => 'nullable|array',
            'service_types'  => 'nullable|array',
            'no_pagination'  => 'sometimes|int',
            'product_author' => 'nullable',
        ];
    }
}
