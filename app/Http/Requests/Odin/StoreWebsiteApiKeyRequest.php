<?php

namespace App\Http\Requests\Odin;

use App\Models\Odin\WebsiteApiKey;
use App\Models\Odin\WebsiteApiKeyOrigin;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class StoreWebsiteApiKeyRequest extends FormRequest
{

    /**
    Validates the following domains:
      *
      *.test.com
      test.com
      foo.test.com
      xn--nnx388a.com
      192.168.99.100
     */
    const ORIGIN_DOMAIN_VALIDATION_REGEX = '/^((\*)|((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)|((\*\.)?([a-zA-Z0-9-]+\.){0,5}[a-zA-Z0-9-][a-zA-Z0-9-]+\.[a-zA-Z]{2,63}?))$/';


    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo('industry-management');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            WebsiteApiKey::FIELD_NAME => 'required|string|max:255',
            ...$this->getOriginRules(),
        ];
    }


    public function messages(): array
    {
        return [
            WebsiteApiKey::RELATION_ORIGINS . '.*.' . WebsiteApiKeyOrigin::FIELD_ORIGIN . '.regex' => 'Invalid origin.' // TODO - Make it more clear
        ];
    }


    /**
     * Get Origin's validation rules.
     *
     * @return array
     */
    protected function getOriginRules(): array
    {
        return [
            WebsiteApiKey::RELATION_ORIGINS => ['required', 'array'],
            WebsiteApiKey::RELATION_ORIGINS . '.*.' . WebsiteApiKeyOrigin::FIELD_ORIGIN => ['required', 'regex:'.self::ORIGIN_DOMAIN_VALIDATION_REGEX,  'distinct'],
        ];
    }
}


