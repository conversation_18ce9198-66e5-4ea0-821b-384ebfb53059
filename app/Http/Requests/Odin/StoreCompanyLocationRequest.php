<?php

namespace App\Http\Requests\Odin;

use App\Models\Odin\Address;
use App\Models\Odin\CompanyLocation;
use App\Rules\Phone;
use Illuminate\Foundation\Http\FormRequest;

class StoreCompanyLocationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $addressValidation = [
            CompanyLocation::RELATION_ADDRESS.".".Address::FIELD_ADDRESS_1  => 'required|string|max:255',
            CompanyLocation::RELATION_ADDRESS.".".Address::FIELD_ADDRESS_2  => 'nullable|string|max:255',
            CompanyLocation::RELATION_ADDRESS.".".Address::FIELD_CITY       => 'required|string|max:64',
            CompanyLocation::RELATION_ADDRESS.".".Address::FIELD_ZIP_CODE   => 'required|string|max:5',
            CompanyLocation::RELATION_ADDRESS.".".Address::FIELD_STATE      => 'required|string|max:2',
        ];

        return [
            CompanyLocation::FIELD_ID           => 'nullable|numeric',
            CompanyLocation::FIELD_ADDRESS_ID   => 'nullable|numeric',
            CompanyLocation::FIELD_NAME         => 'required|string|max:64',
            CompanyLocation::FIELD_PHONE        => [new Phone, 'nullable'],
            ...$addressValidation,
        ];
    }
}
