<?php

namespace App\Http\Requests\Odin;

use App\Enums\PermissionType;
use App\Http\Controllers\API\CompanySearchController;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class CompanySearchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     * @return bool
     */
    public function authorize(): bool
    {
        if ($this->request->get(CompanySearchController::REQUEST_RETURN_FULL_QUERY)) {
            /** @var User $user */
            $user = Auth::user();
            return $user->hasPermissionTo(PermissionType::COMPANY_EXPORT_DATA->value);
        }

        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'filters'                => 'array',
            'search_text'            => 'string|nullable',
            'search_contact'         => 'string|nullable',
            'per_page'               => 'numeric|nullable',
            'page'                   => 'numeric|nullable',
            'sort_array'             => 'array|nullable',
            'sort_array.*.id'        => 'string|nullable',
            'sort_array.*.direction' => 'in:asc,desc|nullable',
            'return_all_results'     => 'boolean|nullable',
        ];
    }

}
