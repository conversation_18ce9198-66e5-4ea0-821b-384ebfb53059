<?php

namespace App\Http\Requests\Odin\v2;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Foundation\Http\FormRequest;

class BaseOdinRequest extends FormRequest
{
    const REQUEST_PER_PAGE      = 'perPage';
    const REQUEST_PAGE          = 'page';
    const REQUEST_SERVICE       = 'service';
    const REQUEST_SERVICES      = 'services';
    const REQUEST_INDUSTRY      = 'industry';
    const REQUEST_INDUSTRIES    = 'industries';
    const REQUEST_PRODUCT       = 'product';
    const REQUEST_PRODUCTS      = 'products';

    /**
     * Return an error bag with a JSON response, instead of a redirect, so any front end can handle errors
     * @param Validator $validator
     * @return void
     */
    protected function failedValidation(Validator $validator): void
    {
        throw new HttpResponseException(response()->json(["errors" => $validator->errors()->all() ], 422));
    }
}
