<?php

namespace App\Http\Requests\Odin\v2;

use App\Enums\Odin\API\FieldClassification;
use App\Models\Odin\ConsumerProductAffiliateRecord;
use App\Models\Odin\ConsumerProductTracking;

class StoreConsumerProductTrackingAndAffiliateRequest extends BaseOdinRequest
{
    /**
     * Determine if the user is authorized to make this request
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request
     * Odin v2 - includes model keys for Address, Consumer and ConsumerProduct
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $tracking = FieldClassification::CONSUMER_TRACKING->value;
        $affiliate = FieldClassification::CONSUMER_AFFILIATE->value;

        return [
            $affiliate .'.'. ConsumerProductAffiliateRecord::FIELD_CAMPAIGN_ID  => 'numeric|required',
            $affiliate .'.'. ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID => 'numeric|required',
            $affiliate .'.'. ConsumerProductAffiliateRecord::FIELD_TRACK_NAME   => 'string|nullable|max:64',
            $affiliate .'.'. ConsumerProductAffiliateRecord::FIELD_TRACK_CODE   => 'string|nullable|max:32',

            $tracking .'.'. ConsumerProductTracking::URL_START          => 'string|required|max:128',
            $tracking .'.'. ConsumerProductTracking::URL_CONVERT        => 'string|required|max:128',
            $tracking .'.'. ConsumerProductTracking::CALCULATOR_SOURCE  => 'string|required|max:128',
            $tracking .'.'. ConsumerProductTracking::WEBSITE_ID         => 'numeric|nullable',
            $tracking .'.'. ConsumerProductTracking::AD_TRACK_TYPE      => 'string|nullable|max:32',
            $tracking .'.'. ConsumerProductTracking::AD_TRACK_CODE      => 'string|nullable|max:32',
        ];
    }

}
