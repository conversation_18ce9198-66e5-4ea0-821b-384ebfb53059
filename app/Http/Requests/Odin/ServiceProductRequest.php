<?php

namespace App\Http\Requests\Odin;

use App\Models\Odin\ConsumerProduct;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;


class ServiceProductRequest extends FormRequest
{
    const REQUEST_COMPANY_REF = 'companyRef';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            self::REQUEST_COMPANY_REF => ['required', 'string'],
        ];
    }
}
