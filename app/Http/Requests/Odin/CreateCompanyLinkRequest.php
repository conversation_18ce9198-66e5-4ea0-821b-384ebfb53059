<?php

namespace App\Http\Requests\Odin;

use App\Enums\PermissionType;
use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class CreateCompanyLinkRequest extends FormRequest
{
    const REQUEST_COMPANY_ID = 'company_id';
    const REQUEST_OTHER_COMPANY_ID = 'other_company_id';
    const REQUEST_COMMENT = 'comment';


    /**
     * Determine if the user is authorized to make this request
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::COMPANY_LINK_CREATE->value);
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            self::REQUEST_COMPANY_ID        => ['required', 'numeric', Rule::exists(Company::TABLE, Company::FIELD_ID)],
            self::REQUEST_OTHER_COMPANY_ID  => ['required', 'numeric', Rule::exists(Company::TABLE, Company::FIELD_ID)],
            self::REQUEST_COMMENT           => ['nullable', 'string']
        ];
    }
}
