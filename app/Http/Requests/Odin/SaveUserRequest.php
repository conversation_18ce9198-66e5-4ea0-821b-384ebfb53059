<?php

namespace App\Http\Requests\Odin;

use App\Enums\RoleType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class SaveUserRequest extends FormRequest
{
    const string FIELD_NAME                  = 'name';
    const string FIELD_EMAIL                 = 'email';
    const string FIELD_ROLES                 = 'roles';
    const string FIELD_PERMISSIONS           = 'permissions';
    const string FIELD_LEGACY_USER_ID        = 'legacy_user_id';
    const string FIELD_FORCE_TWO_FACTOR_AUTH = 'force_two_factor_auth';
    const string FIELD_PHONE                 = 'phone';
    const string FIELD_CREATE_LEGACY_USER    = 'create_legacy_user';
    const string FIELD_SLACK_USERNAME        = 'slack_username';
    const string FIELD_USES_MAILBOX          = 'uses_mailbox';

    /**
     * Determine if the user is authorized to make this request
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasRole(RoleType::ADMIN);
    }

    /**
     * Get the validation rules that apply to the request
     *
     * @return array<string, mixed>
     */
    public function rules(Request $request): array
    {
        return [
            self::FIELD_NAME                  => ['required', 'string', 'max:255'],
            self::FIELD_EMAIL                 => $this->getEmailValidationRules($request),
            self::FIELD_ROLES                 => ['required', 'array', 'min:1'],
            self::FIELD_PERMISSIONS           => ['array', 'exists:permissions,name'],
            self::FIELD_PHONE                 => ['nullable', 'sometimes', 'integer'],
            self::FIELD_LEGACY_USER_ID        => ['nullable', 'integer'],
            self::FIELD_FORCE_TWO_FACTOR_AUTH => ['sometimes', 'boolean'],
            self::FIELD_CREATE_LEGACY_USER    => ['sometimes', 'boolean'],
            self::FIELD_SLACK_USERNAME        => ['nullable', 'string', 'max:255'],
            self::FIELD_USES_MAILBOX          => ['sometimes', 'boolean'],
        ];
    }

    /**
     * @param Request $request
     * @return string[]
     */
    private function getEmailValidationRules(Request $request): array
    {

        $rules = ['required', 'string', 'email', 'max:255'];

        /** @var User $user */
        $user = $this->route('user');

        $uniqueRule = Rule::unique(User::TABLE)
            ->where(User::FIELD_EMAIL, $request->get(self::FIELD_EMAIL));

        if ($user) {
            $uniqueRule->whereNot(User::FIELD_ID, $user->{User::FIELD_ID});
        }

        $rules[] = $uniqueRule;

        return $rules;
    }

    /**
     * @return array
     */
    public function messages(): array
    {
        return [
            ...parent::messages(),
            self::FIELD_EMAIL . '.unique'           => 'The user is already registered.',
            self::FIELD_LEGACY_USER_ID . '.integer' => 'Legacy User ID must be a number.',
            self::FIELD_ROLES . '.required'         => 'At least one role must be selected.',
        ];
    }
}
