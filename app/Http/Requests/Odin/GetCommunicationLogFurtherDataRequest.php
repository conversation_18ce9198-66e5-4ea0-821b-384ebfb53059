<?php

namespace App\Http\Requests\Odin;

use Illuminate\Foundation\Http\FormRequest;

class GetCommunicationLogFurtherDataRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'type'      => 'required|string',
            'id'        => 'required|string',
        ];
    }
}
