<?php

namespace App\Http\Requests\Odin;

use App\Models\Odin\WebsiteApiKey;
use App\Models\Odin\WebsiteApiKeyOrigin;

class UpdateWebsiteApiKeyRequest extends StoreWebsiteApiKeyRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        // Only validates and updates the origins
        return [
            ...$this->getOriginRules(),
            WebsiteApiKey::RELATION_ORIGINS . '.*.' . WebsiteApiKeyOrigin::FIELD_ID => ['sometimes', 'numeric'],
        ];
    }
}


