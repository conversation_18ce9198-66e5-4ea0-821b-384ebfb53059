<?php

namespace App\Http\Requests\Odin;

use App\Http\Controllers\SiloManagementController;
use App\Models\Odin\Silo;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdateSiloRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(SiloManagementController::PERMISSION_UPDATE);
    }

    /**
     * Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            Silo::FIELD_ID                          => ['numeric', 'required'],
            Silo::FIELD_NAME                        => ['string', 'min:3', 'max:128'],
            Silo::FIELD_ROOT_PATH                   => ['string', 'min:1', 'max:64'],
            Silo::FIELD_COLLECTION_HANDLE           => ['string', 'min:1', 'max:64'],
            Silo::FIELD_IS_ACTIVE                   => ['boolean', 'nullable'],
            Silo::FIELD_FLOW_ID                     => ['string', 'nullable', 'max:64'],
            Silo::FIELD_REVISION_ID                 => ['uuid', 'nullable'],
        ];
    }

    /**
     * @return void
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            Silo::FIELD_ROOT_PATH => $this->checkRootPrefix($this->{Silo::FIELD_ROOT_PATH}),
        ]);
    }

    /**
     * @param string $root
     * @return string
     */
    protected function checkRootPrefix(string $root): string
    {
        return str_starts_with($root, "/")
            ? $root
            : "/$root";
    }
}
