<?php

namespace App\Http\Requests\Odin;

use App\Enums\PermissionType;
use App\Http\Controllers\API\ExpertReviewsController;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class StoreCompanyExpertReviewRequest extends FormRequest
{
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();
        return $user->hasPermissionTo(PermissionType::EXPERT_REVIEWS_UPDATE->value) || $user->hasPermissionTo(PermissionType::EXPERT_REVIEWS_CREATE->value);
    }
    /**
     * Get the validation rules that apply to the request
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            ExpertReviewsController::REQUEST_BODY => ['required', 'string'],
        ];
    }
}
