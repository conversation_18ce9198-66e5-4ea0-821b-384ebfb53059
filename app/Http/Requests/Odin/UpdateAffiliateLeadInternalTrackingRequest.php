<?php

namespace App\Http\Requests\Odin;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class UpdateAffiliateLeadInternalTrackingRequest extends FormRequest
{
    const string MANUAL_FIELDS = 'manual';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::MANUAL_FIELDS => 'required|array',
        ];
    }
}
