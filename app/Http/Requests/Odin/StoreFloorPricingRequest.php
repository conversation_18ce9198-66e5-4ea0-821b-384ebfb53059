<?php

namespace App\Http\Requests\Odin;

use App\Enums\Odin\Region;
use App\Http\Controllers\API\FloorPricing\FloorPricingController;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreFloorPricingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(FloorPricingController::MODULE_PERMISSION);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            FloorPricingController::REQUEST_REGION_TYPE            => ['required', 'string', Rule::in([Region::STATE->value, Region::COUNTY->value, Region::NATIONAL->value])],
            FloorPricingController::REQUEST_PRICES                 => ['required', 'array'],
            FloorPricingController::REQUEST_PROPERTY_TYPE          => ['required', 'string', 'max:63'],
            FloorPricingController::REQUEST_QUALITY_TIER           => ['required', 'string', 'max:63'],
            FloorPricingController::REQUEST_INDUSTRY_SERVICE_ID    => ['required', 'int'],
            FloorPricingController::REQUEST_PRODUCT                => ['required', 'string', 'max:63'],
            FloorPricingController::REQUEST_STATE_LOCATION_ID      => ['int', 'nullable'],
            FloorPricingController::REQUEST_COUNTY_LOCATION_ID     => ['int', 'nullable'],
            FloorPricingController::REQUEST_UPDATE_INHERITED_PRICE => ['boolean', 'nullable'],
            FloorPricingController::REQUEST_LOWERED_PRICE_POLICY   => ['array', 'nullable'],
            FloorPricingController::REQUEST_LOWERED_PRICE_POLICY
                .'.'. FloorPricingController::REQUEST_LOWERED_PRICE_POLICY_TYPE => ['numeric'],
            FloorPricingController::REQUEST_LOWERED_PRICE_POLICY.'.*' => ['nullable']
        ];
    }
}
