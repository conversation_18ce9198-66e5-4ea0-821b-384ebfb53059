<?php

namespace App\Http\Requests\Odin;

use App\Models\Call;
use Illuminate\Foundation\Http\FormRequest;

class UpdateCallEntityRelationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'relation_type'   => 'string|nullable|required_with:relation_id',
            'relation_id'     => 'numeric|nullable|required_with:relation_type',
            'note'            => 'string|nullable|max:500|min:1|required_without:relation_id|required_without:relation_type',
            'id'              => 'required|numeric',
            'type'            => 'required|string'
        ];
    }
}
