<?php

namespace App\Http\Requests\Odin;

use App\Http\Controllers\API\ConsumerSearchController;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class ConsumerSearchAggregatesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(ConsumerSearchController::MODULE_PERMISSION);
    }

    /**
     * Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'filters'       => 'array',
            'search_id'     => 'string|nullable',
            'search_text'   => 'string|nullable',
        ];
    }

}
