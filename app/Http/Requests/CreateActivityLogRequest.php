<?php

namespace App\Http\Requests;

use App\Enums\ActivityLog\ActivityLogSubjectType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateActivityLogRequest extends FormRequest
{
    const string DESCRIPTION  = 'description';
    const string LOG_NAME     = 'log_name';
    const string SUBJECT_TYPE = 'subject_type';
    const string SUBJECT_ID   = 'subject_id';
    const string PROPERTIES   = 'properties';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return
            [
                self::DESCRIPTION  => ['string'],
                self::LOG_NAME     => ['string'],
                self::SUBJECT_TYPE => ['nullable', 'sometimes', Rule::enum(ActivityLogSubjectType::class)],
                self::SUBJECT_ID   => ['nullable', 'sometimes'],
                self::PROPERTIES   => ['nullable', 'sometimes'],
            ];
    }

}