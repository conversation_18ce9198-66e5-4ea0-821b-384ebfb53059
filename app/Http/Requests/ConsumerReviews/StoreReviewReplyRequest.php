<?php

namespace App\Http\Requests\ConsumerReviews;

use Illuminate\Foundation\Http\FormRequest;

class StoreReviewReplyRequest extends FormRequest
{
    const string FIELD_REPLY = 'reply';
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize() : bool
    {
        return true;
    }

    /**
     * @return array[]
     */
    public function rules() : array
    {
        return [
            self::FIELD_REPLY => 'required|string|max:255',
        ];
    }
}
