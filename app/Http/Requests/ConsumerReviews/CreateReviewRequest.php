<?php

namespace App\Http\Requests\ConsumerReviews;

use App\Repositories\Odin\CompanyRepository;
use App\Repositories\ReviewRepository;
use Illuminate\Foundation\Http\FormRequest;

class CreateReviewRequest extends FormRequest
{
    const string FIELD_COMPANY_REFERENCE       = 'company_reference';
    const string FIELD_COMPANY_LOCATION_INDEX  = 'company_location_index';
    const string FIELD_INDUSTRY_ID             = 'industry_id';
    const string FIELD_INDUSTRY_SERVICE_ID     = 'industry_service_id';
    const string FIELD_REVIEWER_NAME           = 'reviewer_name';
    const string FIELD_REVIEWER_CONTACT_METHOD = 'reviewer_contact_method';
    const string FIELD_REVIEWER_EMAIL          = 'reviewer_email';
    const string FIELD_REVIEWER_PHONE          = 'reviewer_phone';
    const string FIELD_REVIEW_TITLE            = 'review_title';
    const string FIELD_REVIEW_COMMENTS         = 'review_comments';
    const string FIELD_REVIEW_OVERALL_SCORE    = 'review_overall_score';
    const string FIELD_REGISTRATION_ORIGIN     = 'registration_origin';
    const string FIELD_CUSTOM_REVIEW_DATA      = 'custom_review_data';
    const string FIELD_REVIEWER_IP             = 'reviewer_ip';
    const string FIELD_CONSUMER_REVIEW_TOKEN   = 'consumer_review_token';
    const string FIELD_ZIP_CODE                = 'zip_code';
    const string FIELD_COMPANY_LOCATION_ID     = 'company_location_id';
    const string FIELD_INDUSTRY_SERVICE_SLUG   = 'industry_service_slug';
    const string RESPONSE_STATUS               = 'status';
    const string RESPONSE_MESSAGE              = 'message';
    const string RESPONSE_ERRORS               = 'errors';

    public function __construct(
        public ReviewRepository  $reviewRepository,
        public CompanyRepository $companyRepository,
    )
    {
        parent::__construct();
    }

    /**
     * Determine if the user is authorized to make this request.
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     *  Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::FIELD_COMPANY_REFERENCE       => 'required',
            self::FIELD_COMPANY_LOCATION_INDEX  => 'nullable|numeric',
            self::FIELD_REVIEWER_NAME           => 'required',
            self::FIELD_REVIEWER_CONTACT_METHOD => 'required|in:email,phone',
            self::FIELD_REVIEWER_EMAIL          => 'required_if:reviewer.reviewer_contact_method,email',
            self::FIELD_REVIEWER_PHONE          => 'required_if:reviewer.reviewer_contact_method,phone',
            self::FIELD_REVIEW_TITLE            => 'string|nullable',
            self::FIELD_REVIEW_COMMENTS         => 'required',
            self::FIELD_REVIEW_OVERALL_SCORE    => 'required|numeric|between:0,5',
            self::FIELD_REGISTRATION_ORIGIN     => 'required|string',
            self::FIELD_CUSTOM_REVIEW_DATA      => 'array|nullable',
            self::FIELD_REVIEWER_IP             => 'string|nullable',
            self::FIELD_ZIP_CODE                => 'required|string|size:5'
        ];
    }
}
