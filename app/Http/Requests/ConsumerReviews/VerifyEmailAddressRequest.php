<?php
namespace App\Http\Requests\ConsumerReviews;

use Illuminate\Foundation\Http\FormRequest;

class VerifyEmailAddressRequest extends FormRequest
{
    const string FIELD_TOKEN = 'token';

    /**
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            self::FIELD_TOKEN => 'required|string',
        ];
    }
}
