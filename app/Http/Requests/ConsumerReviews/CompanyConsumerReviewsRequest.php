<?php

namespace App\Http\Requests\ConsumerReviews;

use Illuminate\Foundation\Http\FormRequest;

class CompanyConsumerReviewsRequest extends FormRequest
{
    const string REQUEST_PAGE           = 'page';
    const string REQUEST_PER_PAGE       = 'per_page';
    const string REQUEST_SORT_BY        = 'sort_by';
    const string REQUEST_STATUS         = 'status';
    const string REQUEST_SCORE          = 'score';
    const string REQUEST_REVIEW_TEXT    = 'text';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>s
     */
    public function rules(): array
    {
        return [
            self::REQUEST_PAGE            => ['sometimes', 'numeric', 'nullable'],
            self::REQUEST_PER_PAGE        => ['sometimes', 'numeric', 'nullable'],
            self::REQUEST_SORT_BY         => ['sometimes', 'string', 'nullable'],
            self::REQUEST_STATUS          => ['sometimes', 'numeric', 'between:-1,1', 'nullable'],
            self::REQUEST_SCORE           => ['sometimes', 'integer', 'between:0,5', 'nullable'],
            self::REQUEST_REVIEW_TEXT     => ['sometimes', 'string', 'nullable', 'max:255'],
        ];
    }
}
