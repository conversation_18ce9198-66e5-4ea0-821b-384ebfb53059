<?php

namespace App\Http\Requests\ConsumerReviews;

class CreateReviewWithAttachmentsRequest extends CreateReviewRequest
{
    const string KEY_ATTACHMENTS = 'files';
    const string KEY_REVIEW_DATA = 'review_data';

    /**
     *  Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $reviewDataRules = parent::rules();

        return [
            self::KEY_ATTACHMENTS        => 'array|nullable',
            self::KEY_ATTACHMENTS . '.*' => 'file|mimes:jpeg,jpg,bmp,png,pdf,gif|max:5120',
            self::KEY_REVIEW_DATA        => 'array',
            ...$this->getNestedValidationRules(self::KEY_REVIEW_DATA, $reviewDataRules),
        ];
    }

    /**
     * @return void
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            self::KEY_REVIEW_DATA => json_decode($this->get(self::KEY_REVIEW_DATA), true),
        ]);
    }

    protected function getNestedValidationRules(string $parentKey, array &$rules): array
    {
        $output = [];
        foreach($rules as $key => $rule)
            $output["$parentKey.$key"] = $rule;

        return $output;
    }
}