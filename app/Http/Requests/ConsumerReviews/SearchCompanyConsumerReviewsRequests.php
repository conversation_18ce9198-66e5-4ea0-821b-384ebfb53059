<?php

namespace App\Http\Requests\ConsumerReviews;

use App\Models\ConsumerReviews\Review;
use Illuminate\Foundation\Http\FormRequest;

class SearchCompanyConsumerReviewsRequests extends FormRequest
{
    const string REQUEST_PAGE           = 'page';
    const string REQUEST_STATUS         = 'status';
    const string REQUEST_SCORE          = 'score';
    const string REQUEST_DATE           = 'date';
    const string REQUEST_TEXT           = 'text';
    const string REQUEST_VERIFIED       = 'verified';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize() : bool
    {
        return true;
    }

    /**
     * @return array[]
     */
    public function rules() : array
    {
        return [
            Review::FIELD_COMPANY_ID => ['numeric', 'nullable'],
            self::REQUEST_STATUS     => ['integer', 'between:-1,1', 'nullable'],
            self::REQUEST_SCORE      => ['integer', 'between:0,5', 'nullable'],
            self::REQUEST_DATE       => ['date', 'nullable'],
            self::REQUEST_TEXT       => ['string', 'nullable', 'max:255'],
            self::REQUEST_VERIFIED   => ['boolean', 'nullable'],
            self::REQUEST_PAGE       => ['integer', 'nullable'],
        ];
    }
}
