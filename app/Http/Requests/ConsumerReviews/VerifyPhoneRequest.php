<?php
namespace App\Http\Requests\ConsumerReviews;

use Illuminate\Foundation\Http\FormRequest;

class VerifyPhoneRequest extends FormRequest
{
    const string FIELD_VERIFICATION_CODE = 'verification_code';

    /**
     * Determine if the user is authorized to make this request.
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     *  Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::FIELD_VERIFICATION_CODE => 'required|digits:4',
        ];
    }
}
