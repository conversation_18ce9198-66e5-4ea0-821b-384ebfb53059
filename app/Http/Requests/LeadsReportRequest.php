<?php

namespace App\Http\Requests;

use App\Enums\LeadsReport\LeadsReportColumnEnum;
use App\Enums\LeadsReport\LeadsReportGroupEnum;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class LeadsReportRequest extends FormRequest
{
    const string BASE_START         = 'base_start';
    const string BASE_END           = 'base_end';
    const string COMPARE_START      = 'compare_start';
    const string COMPARE_END        = 'compare_end';
    const string GROUP              = 'group';
    const string FILTERS            = 'filters';
    const string COLUMNS            = 'columns';
    const string COMPANY            = 'company';
    const string CAMPAIGNS          = 'campaigns';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::BASE_START        => ['required', 'date'],
            self::BASE_END          => ['required', 'date'],
            self::COMPARE_START     => ['sometimes', 'date'],
            self::COMPARE_END       => ['sometimes', 'date'],
            self::GROUP             => ['required', 'string', Rule::in(LeadsReportGroupEnum::getAllGroups())],
            self::FILTERS           => ['string'],
            self::COLUMNS           => ['array'],
            self::COMPANY           => ['numeric', 'nullable'],
            self::CAMPAIGNS         => ['array', 'nullable'],
        ];
    }

    /**
     * @return array
     */
    public function getColumns(): array
    {
        return $this->input(self::COLUMNS) ?
            array_map(function($columnString) { return LeadsReportColumnEnum::from(intval($columnString)); }, $this->input(self::COLUMNS)) :
            LeadsReportColumnEnum::getDefaultColumns();
    }

    /**
     * @return array
     */
    public function getFilters(): mixed
    {
        return $this->input(self::FILTERS) ? json_decode($this->input(self::FILTERS)) : [];
    }
}
