<?php

namespace App\Http\Requests\Mailbox;

use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class SendEmailRequest extends FormRequest
{
    const FIELD_CONTENT = 'content';
    const FIELD_SUBJECT = 'subject';
    const FIELD_TO = 'to';
    const FIELD_BCC = 'bcc';
    const FIELD_CC = 'cc';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::MAILBOX_SEND_EMAILS->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::FIELD_CONTENT   =>  'required',
            self::FIELD_SUBJECT   =>  'required|max:255',
            self::FIELD_TO        =>  'required|array|min:1',
            self::FIELD_TO.'.*'   =>  'required',
            self::FIELD_BCC       =>  'sometimes|array',
            self::FIELD_BCC.'.*'  =>  'required',
            self::FIELD_CC        =>  'sometimes|array',
            self::FIELD_CC.'.*'   =>  'required',
        ];
    }
}

