<?php

namespace App\Http\Requests\Mailbox;

use App\Enums\Mailbox\EmailCategory;
use App\Enums\PermissionType;
use App\Models\User;
use App\Rules\MailboxTabs;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class DeleteEmailRequest extends FormRequest
{
    const FIELD_DELETE_THREAD = 'delete_thread';
    const FIELD_EMAIL_IDS     = 'email_ids';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::MAILBOX_LIST_EMAILS->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::FIELD_EMAIL_IDS                => ['array'],
            self::FIELD_DELETE_THREAD            => ['boolean'],
        ];
    }
}

