<?php

namespace App\Http\Requests\Discovery;

use Illuminate\Foundation\Http\FormRequest;

class ListTopCompaniesRequest extends FormRequest
{
    const string FIELD_INDUSTRY_SERVICE_SLUG   = 'industry_service_slug';
    const string FIELD_COUNTRY_ABBREVIATION    = 'country_abbreviation';
    const string FIELD_STATE_ABBREVIATION      = 'state_abbreviation';
    const string FIELD_COUNTY_KEY              = 'county_key';
    const string FIELD_PRIORITIZED_COMPANY_IDS = 'prioritized_company_ids';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            self::FIELD_INDUSTRY_SERVICE_SLUG   => ['required', 'string'],
            self::FIELD_COUNTRY_ABBREVIATION    => ['string'],
            self::FIELD_STATE_ABBREVIATION      => ['string'],
            self::FIELD_COUNTY_KEY              => ['string'],
            self::FIELD_PRIORITIZED_COMPANY_IDS => ['string']
        ];
    }
}
