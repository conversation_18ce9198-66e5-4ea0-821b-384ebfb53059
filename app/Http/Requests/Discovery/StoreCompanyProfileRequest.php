<?php

namespace App\Http\Requests\Discovery;

use App\DTO\CompanyProfile\CompanyProfileAddressDTO;
use App\DTO\CompanyProfile\CompanyProfileDTO;
use App\DTO\CompanyProfile\CompanyProfileReviewDTO;
use App\DTO\CompanyProfile\CompanyProfileReviewSummaryDTO;
use App\Models\Odin\IndustryService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;

class StoreCompanyProfileRequest extends FormRequest
{
    const string NAME                      = 'name';
    const string CATEGORIES                = 'categories';
    const string DESCRIPTION               = 'description';
    const string GENERAL_INFO              = 'general_info';
    const string HOURS                     = 'hours';
    const string WEBSITES                  = 'websites';
    const string DOMAINS                   = 'domains';
    const string PHONES                    = 'phones';
    const string ADDRESSES                 = 'addresses';
    const string YEARS_IN_BUSINESS         = 'years_in_business';
    const string RATING                    = 'rating';
    const string PAID                      = 'paid';
    const string LOGO                      = 'logo';
    const string EMAIL                     = 'email';
    const string OTHER_LINKS               = 'other_links';
    const string SOCIAL_LINKS              = 'social_links';
    const string DISCOVERY_SCORE           = 'discovery_score';
    const string REVIEWS                   = 'reviews';
    const string SLOGAN                    = 'slogan';
    const string BRANDS                    = 'brands';
    const string PAYMENT_METHODS           = 'payment_methods';
    const string AKA                       = 'aka';
    const string CATEGORY_SERVICES         = 'category_services';
    const string CLAIMED                   = 'claimed';
    const string BBB_DATA                  = 'bbb_data';
    const string REVIEW_TEXT               = 'text';
    const string REVIEW_RATING             = 'rating';
    const string REVIEW_DATE               = 'date';
    const string REVIEW_AUTHOR             = 'author';
    const string REVIEW_HELPFUL            = 'helpful';
    const string REVIEW_ADDITIONAL_RATINGS = 'additional_ratings';
    const string REVIEW_SOURCE             = 'source';
    const string ADDRESS_LINE_ONE          = 'line_one';
    const string ADDRESS_LINE_TWO          = 'line_two';
    const string ADDRESS_CITY              = 'city';
    const string ADDRESS_COUNTY            = 'county';
    const string ADDRESS_RAW               = 'raw';
    const string ADDRESS_STATE             = 'state';
    const string ADDRESS_POST_CODE         = 'post_code';
    const string ADDRESS_COUNTRY           = 'country';
    const string INDUSTRY_SERVICE_SLUG     = 'industry_service';


    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $reviewValidation = [
            self::REVIEWS . '.*.' . self::REVIEW_TEXT               => ['string'],
            self::REVIEWS . '.*.' . self::REVIEW_RATING             => ['numeric'],
            self::REVIEWS . '.*.' . self::REVIEW_DATE               => ['date'],
            self::REVIEWS . '.*.' . self::REVIEW_AUTHOR             => ['string'],
            self::REVIEWS . '.*.' . self::REVIEW_HELPFUL            => ['integer'],
            self::REVIEWS . '.*.' . self::REVIEW_ADDITIONAL_RATINGS => ['array'],
            self::REVIEWS . '.*.' . self::REVIEW_SOURCE             => ['string'],
        ];

        $addressValidation = [
            self::ADDRESSES . '.*.' . self::ADDRESS_LINE_ONE  => ['string'],
            self::ADDRESSES . '.*.' . self::ADDRESS_LINE_TWO  => ['string'],
            self::ADDRESSES . '.*.' . self::ADDRESS_CITY      => ['string'],
            self::ADDRESSES . '.*.' . self::ADDRESS_COUNTY    => ['string'],
            self::ADDRESSES . '.*.' . self::ADDRESS_RAW       => ['required', 'string'],
            self::ADDRESSES . '.*.' . self::ADDRESS_STATE     => ['string'],
            self::ADDRESSES . '.*.' . self::ADDRESS_POST_CODE => ['required'],
            self::ADDRESSES . '.*.' . self::ADDRESS_COUNTRY   => ['string'],
        ];


        return [
            self::DOMAINS               => ['required', 'array'],
            'review_summary'            => ['array'],
            'review_summary.positives'  => ['string'],
            'review_summary.negatives'  => ['string'],
            'review_summary.overview'   => ['string'],
            'review_summary.source'     => ['strings'],
            self::NAME                  => ['required', 'string'],
            self::CATEGORIES            => ['array'],
            self::DESCRIPTION           => ['nullable', 'string'],
            self::GENERAL_INFO          => ['nullable', 'string'],
            self::HOURS                 => ['array'],
            self::WEBSITES              => ['required', 'array'],
            self::PHONES                => ['array'],
            self::ADDRESSES             => ['required', 'array'],
            self::YEARS_IN_BUSINESS     => ['integer'],
            self::RATING                => ['numeric'],
            self::PAID                  => ['boolean'],
            self::LOGO                  => ['nullable', 'string'],
            self::EMAIL                 => ['nullable', 'string'],
            self::OTHER_LINKS           => ['array'],
            self::SOCIAL_LINKS          => ['array'],
            self::DISCOVERY_SCORE       => ['integer'],
            self::REVIEWS               => ['array'],
            self::SLOGAN                => ['nullable', 'string'],
            self::BRANDS                => ['nullable', 'array'],
            self::PAYMENT_METHODS       => ['array'],
            self::AKA                   => ['nullable', 'string'],
            self::CATEGORY_SERVICES     => ['array'],
            self::CLAIMED               => ['boolean'],
            self::BBB_DATA              => ['array'],
            self::INDUSTRY_SERVICE_SLUG => ['required', 'exists:' . IndustryService::TABLE . ',' . IndustryService::FIELD_SLUG],
            ...$reviewValidation,
            ...$addressValidation,
        ];
    }

    public function validated($key = null, $default = null): CompanyProfileDTO
    {
        $validated = parent::validated($key, $default);

        $reviews = array_map(function ($item) {
            return new CompanyProfileReviewDTO(
                text             : Arr::get($item, self::REVIEW_TEXT),
                rating           : Arr::get($item, self::REVIEW_RATING),
                date             : Carbon::parse(Arr::get($item, self::REVIEW_DATE)),
                author           : Arr::get($item, self::REVIEW_AUTHOR),
                helpful          : Arr::get($item, self::REVIEW_HELPFUL),
                additionalRatings: Arr::get($item, self::REVIEW_ADDITIONAL_RATINGS),
                source           : Arr::get($item, self::REVIEW_SOURCE)
            );
        }, Arr::get($validated, self::REVIEWS, []));

        $addresses = array_map(function ($item) {
            return new CompanyProfileAddressDTO(
                raw      : Arr::get($item, self::ADDRESS_RAW),
                line_one : Arr::get($item, self::ADDRESS_LINE_ONE),
                line_two : Arr::get($item, self::ADDRESS_LINE_TWO),
                city     : Arr::get($item, self::ADDRESS_CITY),
                county   : Arr::get($item, self::ADDRESS_COUNTY),
                state    : Arr::get($item, self::ADDRESS_STATE),
                post_code: Arr::get($item, self::ADDRESS_POST_CODE),
                country  : Arr::get($item, self::ADDRESS_COUNTRY),
            );
        }, Arr::get($validated, self::ADDRESSES, []));

        $reviewSummary = new CompanyProfileReviewSummaryDTO(
            positives: Arr::get($validated, 'review_summary.positives'),
            negatives: Arr::get($validated, 'review_summary.negatives'),
            overview : Arr::get($validated, 'review_summary.overview'),
            source   : Arr::get($validated, 'review_summary.source'),
        );

        return new CompanyProfileDTO(
            name            : Arr::get($validated, self::NAME),
            categories      : Arr::get($validated, self::CATEGORIES, []),
            industryService : Arr::get($validated, self::INDUSTRY_SERVICE_SLUG),
            discoveryScore  : Arr::get($validated, self::DISCOVERY_SCORE, 0),
            hours           : Arr::get($validated, self::HOURS, []),
            websites        : Arr::get($validated, self::WEBSITES),
            domains         : Arr::get($validated, self::DOMAINS, []),
            phones          : Arr::get($validated, self::PHONES),
            addresses       : $addresses,
            otherLinks      : Arr::get($validated, self::OTHER_LINKS, []),
            socialLinks     : Arr::get($validated, self::SOCIAL_LINKS, []),
            reviews         : $reviews,
            brands          : Arr::get($validated, self::BRANDS, []),
            paymentMethods  : Arr::get($validated, self::PAYMENT_METHODS, []),
            categoryServices: Arr::get($validated, self::CATEGORY_SERVICES, []),
            bbbData         : Arr::get($validated, self::BBB_DATA, []),
            description     : Arr::get($validated, self::DESCRIPTION),
            generalInfo     : Arr::get($validated, self::GENERAL_INFO),
            yearsInBusiness : Arr::get($validated, self::YEARS_IN_BUSINESS),
            rating          : Arr::get($validated, self::RATING),
            paid            : Arr::get($validated, self::PAID),
            logo            : Arr::get($validated, self::LOGO),
            email           : Arr::get($validated, self::EMAIL),
            slogan          : Arr::get($validated, self::SLOGAN),
            aka             : Arr::get($validated, self::AKA),
            claimed         : Arr::get($validated, self::CLAIMED),
            reviewSummary   : $reviewSummary
        );
    }
}
