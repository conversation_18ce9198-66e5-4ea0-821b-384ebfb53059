<?php

namespace App\Http\Requests;

use App\Enums\PermissionType;
use App\Enums\RoleType;
use App\Http\Controllers\API\CompaniesController;
use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreCompanyMediaAssetsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::COMPANY->value)
            || $user->hasRole(RoleType::ADMIN->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            CompaniesController::REQUEST_COMPANY_ID                  => [
                'required',
                Rule::exists(Company::TABLE, Company::FIELD_ID),
            ],
            CompaniesController::REQUEST_COMPANY_MEDIA_ASSETS        => [
                'required',
                'array',
            ],
            CompaniesController::REQUEST_COMPANY_MEDIA_ASSETS . '.*' => [
                'file',
                'image',
                'max:8000000',
            ],
        ];
    }

    /**
     * @return void
     */
    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                CompaniesController::REQUEST_COMPANY_ID => $this->route()->parameter(CompaniesController::REQUEST_COMPANY_ID),
            ]
        );
    }
}
