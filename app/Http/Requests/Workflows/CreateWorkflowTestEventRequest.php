<?php

namespace App\Http\Requests\Workflows;

use App\Enums\EventCategory;
use App\Http\Controllers\API\Workflows\TaskManagementController;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\LeadCampaign;
use App\Models\User;
use App\Models\WorkflowEvent;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class CreateWorkflowTestEventRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasRole('admin');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            WorkflowEvent::FIELD_EVENT_CATEGORY => [
                'required',
                'in:' . implode(',', EventCategory::workFlowCategories())
            ],
            WorkflowEvent::FIELD_EVENT_NAME => [
                'required',
                'string'
            ],
            TaskManagementController::REQUEST_COMPANY_ID  => [
                'integer'
            ],
            TaskManagementController::REQUEST_CAMPAIGN_ID => [
                'nullable',
                'integer',
                'exists:readonly.tbl_lead_campaigns,id'
            ],
            TaskManagementController::REQUEST_LEAD_ID => [
                'nullable',
                'integer',
                'exists:readonly.tblquote,quoteid'
            ]
        ];
    }
}
