<?php

namespace App\Http\Requests\Workflows;

use App\Http\Controllers\API\Workflows\TaskController;
use App\Models\Sales\Task;
use App\Models\TaskCategory;
use App\Models\TaskType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class WorkflowTaskRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo("tasks");
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            Task::FIELD_ASSIGNED_USER_ID => Rule::exists(User::TABLE, User::FIELD_ID),
            Task::FIELD_AVAILABLE_AT => ['required', 'date_format:Y-m-d H:i'],
            Task::FIELD_TASK_TYPE_ID => ['required', 'exists:' . TaskType::class . ',id'],
            Task::FIELD_SUBJECT => ['required', 'string'],
            Task::FIELD_PRIORITY => ['required', 'integer'],
            Task::FIELD_TASK_CATEGORY_ID => Rule::exists(TaskCategory::TABLE, TaskCategory::ID),
            TaskController::COMPANY_DATA . '.data.companies' => 'array|min:1|required_if:company_data.all,false',
            TaskController::COMPANY_ID => ['nullable','integer']
        ];
    }

    public function messages()
    {
        return [
            TaskController::COMPANY_ID . '.' . 'integer' => 'A valid company is required.'
        ];
    }
}
