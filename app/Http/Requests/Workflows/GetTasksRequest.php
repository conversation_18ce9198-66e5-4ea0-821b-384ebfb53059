<?php

namespace App\Http\Requests\Workflows;

use App\Http\Controllers\API\Workflows\TaskController;
use App\Models\Sales\Task;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class GetTasksRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {

        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo("tasks");
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            Task::FIELD_SUBJECT => [
                'nullable',
                'string'
            ],
            Task::FIELD_PRIORITY => [
                'nullable',
                'in:' . implode(',', Task::query()->distinct()->pluck(Task::FIELD_PRIORITY)->toArray())
            ],
            TaskController::REQUEST_SORT_COL => [
                'nullable',
                'in:' . implode(',', TaskController::SORTABLE_COLUMNS)
            ],
            TaskController::REQUEST_SORT_DIR => [
                'nullable',
                'in:asc,desc'
            ],
            TaskController::ALL_USER => ['nullable', 'in:true,false'],
            TaskController::COMPANY_ID => [
                'nullable',
            ],
            Task::FIELD_TASK_CATEGORY_ID => ['nullable', 'integer'],
            "timezone" => ['nullable', 'string'],
            TaskController::REQUEST_DATE => ['nullable', 'date_format:Y-m-d']
        ];
    }
}
