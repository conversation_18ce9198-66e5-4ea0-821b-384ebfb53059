<?php

namespace App\Http\Requests\Workflows;

use App\Enums\ActionType;
use App\Models\User;
use App\Models\Workflow;
use App\Models\WorkflowAction;
use App\Models\WorkflowEvent;
use App\Rules\ValidateShortcodes;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rules\Enum;

class UpdateWorkflowActionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasRole('admin');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            WorkflowAction::FIELD_DISPLAY_NAME => ['required', 'string','max:255', new ValidateShortcodes],
            WorkflowAction::FIELD_ACTION_TYPE => ['required', new Enum(ActionType::class)],
            WorkflowAction::FIELD_PAYLOAD => ['nullable', 'array', new ValidateShortcodes],
            WorkflowAction::FIELD_WORKFLOW_ID => ['numeric', 'nullable'],
            WorkflowEvent::FIELD_EVENT_NAME   => ['string', 'nullable']
        ];
    }

    /**
     * Attempt to find the EventName for the parent WorkflowEvent for use with shortcode validation
     * Conditional Shortcode validation will be skipped if no event name is found
     * @return void
     */
    protected function prepareForValidation(): void
    {
        $parentWorkflowEventName = Workflow::query()
            ->find($this->{WorkflowAction::FIELD_WORKFLOW_ID})
            ?->{Workflow::RELATION_WORKFLOW_EVENT}
            ?->{WorkflowEvent::FIELD_EVENT_NAME};
        $this->mergeIfMissing([
            WorkflowEvent::FIELD_EVENT_NAME => $parentWorkflowEventName ?? '',
        ]);
    }
}
