<?php

namespace App\Http\Requests\Workflows;

use App\Enums\EventCategory;
use App\Models\User;
use App\Models\WorkflowEvent;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class CreateWorkflowEventRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasRole('admin');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            WorkflowEvent::FIELD_EVENT_CATEGORY => [
                'required',
                'in:' . implode(',', EventCategory::workFlowCategories()),
                Rule::unique(WorkflowEvent::TABLE)->where(fn($query) => $query->where(WorkflowEvent::FIELD_EVENT_NAME, request()->get(WorkflowEvent::FIELD_EVENT_NAME)))
            ],
            WorkflowEvent::FIELD_EVENT_NAME => [
                'required',
                'string',
                Rule::unique(WorkflowEvent::TABLE)->where(fn($query) => $query->where(WorkflowEvent::FIELD_EVENT_CATEGORY, request()->get(WorkflowEvent::FIELD_EVENT_CATEGORY)))
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        $messages =  parent::messages();

        $messages[WorkflowEvent::FIELD_EVENT_CATEGORY . '.' . 'unique'] = 'The event category and name have already been taken';
        $messages[WorkflowEvent::FIELD_EVENT_NAME . '.' . 'unique'] = 'The event name and category have already been taken';

        return $messages;
    }
}
