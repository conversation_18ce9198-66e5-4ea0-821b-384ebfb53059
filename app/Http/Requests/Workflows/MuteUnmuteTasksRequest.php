<?php

namespace App\Http\Requests\Workflows;


use Illuminate\Foundation\Http\FormRequest;

class MuteUnmuteTasksRequest extends FormRequest
{
    const string REQUEST_ACTION_ID = 'action_id';
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::REQUEST_ACTION_ID => 'required|integer|exists:workflow_actions,id',
        ];
    }

    /**
     * @return int
     */
    public function getActionId(): int
    {
        return $this->input(self::REQUEST_ACTION_ID);
    }
}
