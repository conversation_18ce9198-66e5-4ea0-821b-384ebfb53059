<?php

namespace App\Http\Requests\Workflows;

use App\Enums\ActionType;
use App\Models\WorkflowAction;
use App\Models\WorkflowEvent;
use App\Rules\ValidateShortcodes;
use Illuminate\Validation\Rules\Enum;

class CreateWorkflowActionRequest extends UpdateWorkflowActionRequest
{
    /**
     * Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            WorkflowAction::FIELD_DISPLAY_NAME => ['required', 'string','max:255', new ValidateShortcodes],
            WorkflowAction::FIELD_ACTION_TYPE => ['required', new Enum(ActionType::class)],
            WorkflowAction::FIELD_PAYLOAD => ['nullable', 'array', new ValidateShortcodes],
            WorkflowAction::FIELD_PREVIOUS_NODE_ID => 'nullable|exists:' . WorkflowAction::TABLE . ',' . WorkflowAction::FIELD_ID,
            WorkflowAction::FIELD_PREVIOUS_NODE_TYPE => 'nullable|in:' . WorkflowAction::PARENT_TYPE_PARENT . ',' . WorkflowAction::PARENT_TYPE_SIBLING,
            WorkflowAction::FIELD_WORKFLOW_ID => ['numeric', 'nullable'],
            WorkflowEvent::FIELD_EVENT_NAME   => ['string', 'nullable']
        ];
    }

}
