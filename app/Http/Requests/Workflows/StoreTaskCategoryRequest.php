<?php

namespace App\Http\Requests\Workflows;

use App\Models\TaskCategory;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreTaskCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return Auth::user()->hasPermissionTo("sales-management");
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $taskCategory = $this->route()->parameter('taskCategory');
        $unique       = Rule::unique(TaskCategory::TABLE);

        if ($taskCategory instanceof TaskCategory) $unique->ignore($taskCategory->id);

        return [
            TaskCategory::NAME => [
                'required',
                $unique
            ]
        ];
    }
}
