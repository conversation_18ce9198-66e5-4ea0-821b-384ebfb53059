<?php

namespace App\Http\Requests\Workflows;

use App\Models\User;
use App\Models\WorkflowAction;
use \Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class WorkflowActionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo("tasks");
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            WorkflowAction::FIELD_PREVIOUS_NODE_ID   => ['required', 'integer'],
            WorkflowAction::FIELD_PREVIOUS_NODE_TYPE => ['required', 'string', 'alpha_dash'],
            WorkflowAction::FIELD_DISPLAY_NAME       => ['required', 'string', 'alpha_dash'],
            WorkflowAction::FIELD_ACTION_TYPE        => ['required', 'string', 'alpha_dash']
        ];
    }
}
