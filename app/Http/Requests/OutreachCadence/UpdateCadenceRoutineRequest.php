<?php

namespace App\Http\Requests\OutreachCadence;

use App\Models\Cadence\BaseModel;
use App\Models\Cadence\CadenceRoutine;
use App\Models\Cadence\CadenceScheduledGroup;
use App\Models\Cadence\CadenceScheduledGroupAction;
use Illuminate\Foundation\Http\FormRequest;

class UpdateCadenceRoutineRequest extends FormRequest
{
    const SCHEDULED_GROUPS = 'scheduled_groups';
    const ACTIONS          = 'actions';
    const DELETE           = 'delete';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            CadenceRoutine::FIELD_NAME                                                                                  => 'string',
            CadenceRoutine::FIELD_CONTACT_DECISION_MAKERS_ONLY                                                          => 'boolean',
            CadenceRoutine::FIELD_CONTACT_ON_WEEKDAYS_ONLY                                                              => 'boolean',
            CadenceRoutine::FIELD_GLOBAL                                                                                => 'boolean',
            CadenceRoutine::FIELD_DOMAIN                                                                                => 'string',
            self::SCHEDULED_GROUPS                                                                                      => 'array',
            self::SCHEDULED_GROUPS . '.*.' . BaseModel::FIELD_ID                                                        => 'numeric',
            self::SCHEDULED_GROUPS . '.*.' . CadenceScheduledGroup::FIELD_ORDINAL_VALUE                                 => 'nullable|numeric',
            self::SCHEDULED_GROUPS . '.*.' . CadenceScheduledGroup::FIELD_EXECUTION_DELAY_MINUTES                       => 'numeric',
            self::SCHEDULED_GROUPS . '.*.' . CadenceScheduledGroup::FIELD_EXECUTION_TIME_EXACT                          => 'nullable|string',
            self::SCHEDULED_GROUPS . '.*.' . CadenceScheduledGroup::FIELD_EXECUTION_TIME_WINDOW_START                   => 'nullable|string',
            self::SCHEDULED_GROUPS . '.*.' . CadenceScheduledGroup::FIELD_EXECUTION_TIME_WINDOW_END                     => 'nullable|string',
            self::SCHEDULED_GROUPS . '.*.' . self::ACTIONS                                                              => 'array',
            self::SCHEDULED_GROUPS . '.*.' . self::ACTIONS . '*' . CadenceScheduledGroupAction::FIELD_ACTION_TYPE       => 'string',
            self::SCHEDULED_GROUPS . '.*.' . self::ACTIONS . '*' . CadenceScheduledGroupAction::FIELD_TASK_TEMPLATE_ID  => 'numeric',
            self::SCHEDULED_GROUPS . '.*.' . self::ACTIONS . '*' . CadenceScheduledGroupAction::FIELD_SMS_TEMPLATE_ID   => 'numeric',
            self::SCHEDULED_GROUPS . '.*.' . self::ACTIONS . '*' . CadenceScheduledGroupAction::FIELD_EMAIL_TEMPLATE_ID => 'numeric',
            self::SCHEDULED_GROUPS . '.*.' . self::DELETE                                                               => 'boolean',
        ];
    }
}
