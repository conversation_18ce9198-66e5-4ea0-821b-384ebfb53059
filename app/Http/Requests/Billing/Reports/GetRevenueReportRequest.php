<?php

namespace App\Http\Requests\Billing\Reports;

use App\Enums\Billing\InvoiceTransactionScenario;
use App\Enums\Billing\InvoiceTransactionType;
use App\Enums\PermissionType;
use App\Models\User;
use App\Rules\OrderFormat;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class GetRevenueReportRequest extends FormRequest
{
    const string FIELD_DATE_RANGE            = 'date_range';
    const string FIELD_COMPANY_ID            = 'company_id';
    const string FIELD_INVOICE_STATUS        = 'invoice_status';
    const string FIELD_TRANSACTION_SCENARIOS = 'transaction_scenarios';
    const string FIELD_TRANSACTION_TYPES     = 'transaction_types';
    const string FIELD_ALL                   = 'all';
    const string FIELD_PER_PAGE              = 'perPage';
    const string FIELD_SORT_BY               = 'sort_by';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();
        return $user->hasPermissionTo(PermissionType::BILLING_REPORTS_REVENUE_VIEW->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::FIELD_DATE_RANGE                   => ['nullable', 'array'],
            self::FIELD_COMPANY_ID                   => ['nullable', 'numeric'],
            self::FIELD_INVOICE_STATUS               => ['nullable', 'string'],
            self::FIELD_TRANSACTION_SCENARIOS        => ['sometimes', 'array'],
            self::FIELD_TRANSACTION_SCENARIOS . '.*' => ['sometimes', Rule::enum(InvoiceTransactionScenario::class)],
            self::FIELD_TRANSACTION_TYPES            => ['sometimes', 'array'],
            self::FIELD_TRANSACTION_TYPES . '.*'     => ['sometimes'],
            self::FIELD_ALL                          => ['sometimes', 'boolean'],
            'page'                                   => ['sometimes', 'numeric'],
            self::FIELD_PER_PAGE                     => ['sometimes', 'numeric'],
            self::FIELD_SORT_BY                      => ['sometimes', 'array'],
            self::FIELD_SORT_BY . '.*'               => ['required', 'string', new OrderFormat()],
        ];
    }
}
