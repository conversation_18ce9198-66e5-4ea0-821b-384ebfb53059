<?php

namespace App\Http\Requests\Billing\Reports;

use App\Enums\Billing\InvoiceReportsGrouping;
use App\Enums\PermissionType;
use App\Models\User;
use App\Rules\OrderFormat;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class GetInvoiceBalanceReportRequest extends FormRequest
{
    const string FIELD_PAGE                                 = 'page';
    const string FIELD_PER_PAGE                             = 'per_page';
    const string FIELD_GROUPED_BY                           = 'grouped_by';
    const string FIELD_ACCOUNT_MANAGER_USER_ID              = 'account_manager_user_id';
    const string FIELD_ONBOARDING_MANAGER_USER_ID           = 'onboarding_manager_user_id';
    const string FIELD_BUSINESS_DEVELOPMENT_MANAGER_USER_ID = 'business_development_manager_user_id';
    const string FIELD_INDUSTRY_ID                          = 'industry_id';
    const string FIELD_COMPANY_ID                           = 'company_id';
    const string FIELD_SORT_BY                              = 'sort_by';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();
        return $user->hasPermissionTo(PermissionType::BILLING_REPORTS_BALANCE_VIEW->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'page'                                 => 'numeric',
            'per_page'                             => 'numeric',
            'grouped_by'                           => 'string|' . Rule::in(InvoiceReportsGrouping::cases()),
            'account_manager_user_id'              => 'numeric',
            'onboarding_manager_user_id'           => 'numeric',
            'business_development_manager_user_id' => 'numeric',
            'industry_id'                          => 'array',
            'industry_id.*'                        => 'numeric',
            'company_id'                           => 'numeric',
            self::FIELD_SORT_BY                    => ['sometimes', 'array'],
            self::FIELD_SORT_BY . '.*'             => ['required', 'string', new OrderFormat()],
        ];
    }
}
