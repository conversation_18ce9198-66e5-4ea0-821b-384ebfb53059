<?php

namespace App\Http\Requests\Billing\Reports;

use App\Enums\Billing\InvoiceReportsGrouping;
use App\Enums\PermissionType;
use App\Models\User;
use App\Rules\OrderFormat;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class GetReceivableInvoicesReportRequest extends FormRequest
{
    const string FIELD_DATE                                 = 'date';
    const string FIELD_COMPANY_ID                           = 'company_id';
    const string FIELD_INVOICE_STATUS                       = 'invoice_status';
    const string FIELD_ONBOARDING_MANAGER_USER_ID           = 'onboarding_manager_user_id';
    const string FIELD_ACCOUNT_MANAGER_USER_ID              = 'account_manager_user_id';
    const string FIELD_BUSINESS_DEVELOPMENT_MANAGER_USER_ID = 'business_development_manager_user_id';
    const string FIELD_SORT_BY                              = 'sort_by';
    const string FIELD_INDUSTRY_IDS                         = 'industry_ids';
    const string FIELD_ALL                                  = 'all';
    const string FIELD_PAGE                                 = 'page';
    const string FIELD_PER_PAGE                             = 'per_page';
    const string FIELD_REFERENCE                            = 'grouped_by';


    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();
        return $user->hasPermissionTo(PermissionType::BILLING_REPORTS_RECEIVABLE_VIEW->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::FIELD_DATE                                 => 'string',
            self::FIELD_REFERENCE                            => ['string'],
            self::FIELD_COMPANY_ID                           => 'numeric',
            self::FIELD_ONBOARDING_MANAGER_USER_ID           => 'numeric',
            self::FIELD_ACCOUNT_MANAGER_USER_ID              => 'numeric',
            self::FIELD_BUSINESS_DEVELOPMENT_MANAGER_USER_ID => 'numeric',
            self::FIELD_INVOICE_STATUS                       => 'string',
            self::FIELD_ALL                                  => 'bool',
            self::FIELD_PAGE                                 => ['sometimes', 'numeric'],
            self::FIELD_PER_PAGE                             => ['sometimes', 'numeric'],
            self::FIELD_SORT_BY                              => ['sometimes', 'array'],
            self::FIELD_SORT_BY . '.*'                       => ['required', 'string', new OrderFormat()],
            self::FIELD_INDUSTRY_IDS                         => 'array',
            self::FIELD_INDUSTRY_IDS . '.*'                  => 'numeric',
        ];
    }
}
