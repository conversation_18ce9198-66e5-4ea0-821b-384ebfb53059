<?php

namespace App\Http\Requests\Billing\InvoiceTransaction;

use App\Enums\Billing\InvoiceTransactionScenario;
use App\Enums\Billing\InvoiceTransactionType;
use App\Enums\PermissionType;
use App\Models\Billing\Invoice;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class ListInvoiceTransactionsRequest extends FormRequest
{
    const string FIELD_PAGE                  = 'page';
    const string FIELD_PER_PAGE              = 'per_page';
    const string FIELD_TRANSACTION_SCENARIOS = 'transaction_scenarios';
    const string FIELD_TRANSACTION_TYPES     = 'transaction_types';
    const string FIELD_EXTERNAL_REFERENCE    = 'external_reference';
    const string FIELD_INVOICE_ID            = 'invoice_id';
    const string FIELD_MINIMUM               = 'minimum';
    const string FIELD_MAXIMUM               = 'maximum';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::BILLING_INVOICE_TRANSACTIONS_VIEW->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule>
     */
    public function rules(): array
    {
        return [
            self::FIELD_PAGE                         => ['nullable', 'numeric', 'min:1'],
            self::FIELD_PER_PAGE                     => ['nullable', 'numeric', 'min:1', 'max:100'],
            self::FIELD_TRANSACTION_SCENARIOS        => ['sometimes', 'array'],
            self::FIELD_TRANSACTION_SCENARIOS . '.*' => ['sometimes', Rule::enum(InvoiceTransactionScenario::class)],
            self::FIELD_TRANSACTION_TYPES            => ['sometimes', 'array'],
            self::FIELD_TRANSACTION_TYPES . '.*'     => ['sometimes', Rule::enum(InvoiceTransactionType::class)],
            self::FIELD_EXTERNAL_REFERENCE           => ['sometimes', 'string'],
            self::FIELD_INVOICE_ID                   => ['sometimes', 'exists:' . Invoice::TABLE . ',' . Invoice::FIELD_ID],
            self::FIELD_MINIMUM                      => ['sometimes', 'numeric'],
            self::FIELD_MAXIMUM                      => ['sometimes', 'numeric'],
        ];
    }
}
