<?php

namespace App\Http\Requests\Billing;

use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class GetOverdueInvoicesReportRequest extends FormRequest
{
    const string FIELD_DATE           = 'date';
    const string FIELD_COMPANY_ID     = 'company_id';
    const string FIELD_INVOICE_ID     = 'invoice_id';
    const string FIELD_PAYMENT_STATUS = 'payment_status';
    const string FIELD_PAYMENT_METHOD = 'payment_method';
    const string FIELD_REFERENCE      = 'reference';
    const string FIELD_SORT_BY        = 'sort_by';
    const string FIELD_PAGE           = 'page';
    const string FIELD_PER_PAGE       = 'per_page';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::BILLING_VIEW_INVOICE->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::FIELD_DATE           => 'string',
            self::FIELD_COMPANY_ID     => 'numeric',
            self::FIELD_INVOICE_ID     => 'numeric',
            self::FIELD_SORT_BY        => 'array',
            self::FIELD_PAGE           => 'numeric',
            self::FIELD_PER_PAGE       => 'numeric',
            self::FIELD_PAYMENT_STATUS => 'string',
            self::FIELD_PAYMENT_METHOD => 'string',
            self::FIELD_REFERENCE      => 'string'
        ];
    }
}
