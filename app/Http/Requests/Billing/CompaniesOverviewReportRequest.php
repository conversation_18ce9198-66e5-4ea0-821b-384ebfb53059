<?php

namespace App\Http\Requests\Billing;

use App\Enums\PermissionType;
use App\Enums\RoleType;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class CompaniesOverviewReportRequest extends FormRequest
{
    const string FIELD_COMPANY_ID = 'company_id';
    const string FIELD_SORT_BY    = 'sort_by';
    const string FIELD_PAGE       = 'page';
    const string FIELD_PER_PAGE   = 'per_page';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::BILLING_OVERVIEW_VIEW->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::FIELD_PAGE       => 'numeric',
            self::FIELD_PER_PAGE   => 'numeric',
            self::FIELD_COMPANY_ID => 'numeric',
            self::FIELD_SORT_BY    => 'array'
        ];
    }
}
