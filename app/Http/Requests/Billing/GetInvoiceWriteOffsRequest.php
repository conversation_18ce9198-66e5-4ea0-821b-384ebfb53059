<?php

namespace App\Http\Requests\Billing;

use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class GetInvoiceWriteOffsRequest extends FormRequest
{
    const string FIELD_INVOICE_ID = 'invoice_id';
    const string FIELD_COMPANY_ID = 'company_id';
    const string FIELD_STATUS     = 'status';
    const string FIELD_DATE_RANGE = 'date_range';
    const string FIELD_ALL        = 'all';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::BILLING_REPORTS_WRITE_OFFS_VIEW->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::FIELD_INVOICE_ID => 'integer',
            self::FIELD_COMPANY_ID => 'integer',
            self::FIELD_STATUS     => 'array',
            self::FIELD_DATE_RANGE => 'string',
            self::FIELD_ALL        => 'nullable',
        ];
    }
}
