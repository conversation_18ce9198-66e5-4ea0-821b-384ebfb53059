<?php

namespace App\Http\Requests\Billing;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class LeadsImportRequest extends FormRequest
{

    const string FIELD_COMPANY_ID = 'company_id';
    const string FIELD_FILE       = 'file';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'file'       => 'required|file|mimes:csv',
            'company_id' => 'required|integer|exists:companies,id',
        ];
    }
}
