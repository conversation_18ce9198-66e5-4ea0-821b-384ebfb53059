<?php

namespace App\Http\Requests\Billing\InvoiceTemplate;

use App\Enums\Billing\InvoiceTemplateModelType;
use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class SearchModelTypeRequest extends FormRequest
{
    const string FIELD_QUERY = 'query';
    const string FIELD_TYPE  = 'model_type';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::BILLING_INVOICE_TEMPLATES_VIEW->value);
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule>
     */
    public function rules(): array
    {
        return [
            self::FIELD_QUERY => 'required|string',
            self::FIELD_TYPE  => 'required|' . Rule::in(InvoiceTemplateModelType::all())
        ];
    }
}
