<?php

namespace App\Http\Requests\Billing\BillingProfilePolicy;

use App\Enums\Billing\BillingPolicyActionType;
use App\Enums\Billing\BillingPolicyEventType;
use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class BatchUpdateBillingProfilePolicyRequest extends FormRequest
{
    const string FIELD_POLICIES = 'policies';

    /**
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::BILLING_PROFILE_POLICIES_SAVE->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule>
     */
    public function rules(): array
    {
        return [
            self::FIELD_POLICIES                           => 'required|array',
            self::FIELD_POLICIES . '.*.id'                 => 'required|int',
            self::FIELD_POLICIES . '.*.event_slug'         => 'required|string|' . Rule::in(BillingPolicyEventType::all()),
            self::FIELD_POLICIES . '.*.action_slug'        => 'required|string|' . Rule::in(BillingPolicyActionType::all()),
            self::FIELD_POLICIES . '.*.billing_profile_id' => 'nullable|number',
            self::FIELD_POLICIES . '.*.action_data'         => 'nullable|array',
        ];
    }
}
