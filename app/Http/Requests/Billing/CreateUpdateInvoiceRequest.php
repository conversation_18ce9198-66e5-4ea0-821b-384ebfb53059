<?php

namespace App\Http\Requests\Billing;

use App\Enums\Billing\InvoiceItemTypes;
use App\Enums\Billing\InvoiceStates;
use App\Enums\PermissionType;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceItem;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class CreateUpdateInvoiceRequest extends FormRequest
{
    const string INVOICE_ID      = 'id';
    const string INVOICE_UUID    = 'uuid';
    const string COMPANY_ID      = 'company_id';
    const string ISSUE_DATE      = 'issue_date';
    const string DUE_DATE        = 'due_date';
    const string BILLING_ACCOUNT = 'billing_account';
    const string PROCESS_AUTO    = 'process_auto';
    const string PREPAYMENT      = 'prepayment';
    const string NOTE            = 'note';
    const string DESCRIPTION     = 'description';
    const string QUANTITY        = 'quantity';
    const string UNIT_PRICE      = 'unit_price';
    const string BILLABLE_TYPE   = 'billable_type';
    const string BILLABLE_ID     = 'billable_id';
    const string ITEMS           = 'items';
    const string STATUS          = 'status';
    const string ITEM_ID         = 'invoice_item_id';
    const string TAGS            = 'tags';


    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::BILLING_CREATE_INVOICE->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::INVOICE_ID                          => 'sometimes|exists:' . Invoice::TABLE . ',' . Invoice::FIELD_ID,
            self::INVOICE_UUID                        => 'sometimes|exists:' . Invoice::TABLE . ',' . Invoice::FIELD_UUID,
            self::COMPANY_ID                          => 'required|int',
            self::ISSUE_DATE                          => 'required|date',
            self::DUE_DATE                            => 'required|date',
            self::BILLING_ACCOUNT                     => 'required|array',
            self::TAGS                                => 'array',
            self::PROCESS_AUTO                        => 'required|boolean',
            self::PREPAYMENT                          => 'required|boolean',
            self::NOTE                                => 'nullable|string',
            self::STATUS                              => ['required', Rule::enum(InvoiceStates::class)],
            self::ITEMS                               => 'required|array|min:1',
            self::ITEMS . '.*.' . self::DESCRIPTION   => 'required|string',
            self::ITEMS . '.*.' . self::QUANTITY      => 'required|numeric|min:1',
            self::ITEMS . '.*.' . self::UNIT_PRICE    => 'required|numeric',
            self::ITEMS . '.*.' . self::BILLABLE_TYPE => ['required', Rule::enum(InvoiceItemTypes::class)],
            self::ITEMS . '.*.' . self::BILLABLE_ID   => ['required', 'integer'],
            self::ITEMS . '.*.' . self::ITEM_ID       => ['sometimes', 'nullable', 'exists:' . InvoiceItem::TABLE . ',' . InvoiceItem::FIELD_ID]
        ];
    }

    public function messages(): array
    {
        return [
            ...parent::messages(),
            self::ITEMS . '.*.' . self::DESCRIPTION => 'Description is required in all items',
            self::ITEMS . '.*.' . self::UNIT_PRICE  => 'All items must have a price',
            self::ITEMS . '.*.' . self::QUANTITY    => 'Item quantity must be a valid number',
        ];
    }
}
