<?php

namespace App\Http\Requests\Billing\BillingProfile;

use App\Enums\PermissionType;
use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class GetBillingProfilesRequest extends FormRequest
{
    const string PAGE            = 'page';
    const string PER_PAGE        = 'perPage';
    const string BILLING_CONTACT = 'billing_contact';
    const string COMPANY_ID      = 'company_id';
    const string SHOW_ARCHIVED   = 'show_archived';
    const string IDS             = 'ids';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::BILLING_BILLING_PROFILES_VIEW->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule>
     */
    public function rules(): array
    {
        return [
            self::PAGE            => 'nullable|numeric|min:1',
            self::PER_PAGE        => 'nullable|numeric|min:1|max:100',
            self::BILLING_CONTACT => 'nullable|string',
            self::SHOW_ARCHIVED   => 'nullable|string',
            self::COMPANY_ID      => 'nullable|numeric|exists:' . Company::TABLE . ',' . Company::FIELD_ID,
            self::IDS             => 'nullable|array',
        ];
    }
}
