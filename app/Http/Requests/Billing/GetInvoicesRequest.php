<?php

namespace App\Http\Requests\Billing;

use App\Enums\Billing\InvoiceStates;
use App\Enums\PermissionType;
use App\Models\Billing\Invoice;
use App\Models\Odin\Company;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class GetInvoicesRequest extends FormRequest
{
    const string DATE_FORMAT = 'Y-m-d H:i:s';
    const string COMPANY_ID  = 'companyId';

    const string PAGE     = 'page';
    const string PER_PAGE = 'perPage';

    const string INVOICE_ID   = 'invoiceId';
    const string INVOICE_UUID = 'invoiceUuid';

    const string START_DATE = 'startDate';
    const string END_DATE   = 'endDate';
    const string STATUS     = 'status';
    const string TAGS       = 'tags';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::BILLING_INVOICES_VIEW->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::COMPANY_ID    => 'nullable|exists:' . Company::TABLE . ',' . Company::FIELD_ID,
            self::INVOICE_ID    => 'nullable|exists:' . Invoice::TABLE . ',' . Invoice::FIELD_ID,
            self::INVOICE_UUID  => 'nullable|uuid|exists:' . Invoice::TABLE . ',' . Invoice::FIELD_UUID,
            self::STATUS        => 'nullable|array',
            self::STATUS . '.*' => Rule::enum(InvoiceStates::class),
            self::START_DATE    => 'nullable|string',
            self::END_DATE      => 'nullable|string',
            self::PAGE          => 'nullable|numeric|min:1',
            self::PER_PAGE      => 'nullable|numeric|min:1|max:100',
            self::TAGS          => 'nullable|array',
            'sort_by'           => 'nullable|array',
        ];
    }

    public function messages(): array
    {
        return [
            ...parent::messages(),
            self::STATUS . '.*' => 'A valid status must be selected.'
        ];
    }
}
