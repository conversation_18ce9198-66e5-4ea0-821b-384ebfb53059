<?php

namespace App\Http\Requests\Billing\InvoiceActionRequest;

use App\Enums\ApprovableAction\ApprovableActionRelationTypes;
use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class ListInvoiceActionRequestRequest extends FormRequest
{
    const string FIELD_REQUESTED_BY_ID = 'requested_by_id';
    const string FIELD_REVIEWED_BY_ID  = 'reviewed_by_id';
    const string FIELD_STATUS          = 'status';
    const string FIELD_RELATED_IDS     = 'related_ids';
    const string FIELD_RELATED_TYPES   = 'related_types';
    const string FIELD_ORDER_BY        = 'order_by';
    const string FIELD_COMPANY_ID      = 'company_id';
    const string FIELD_ACTION_TYPES    = 'action_types';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();
        return $user->hasPermissionTo(PermissionType::BILLING_ACTION_REQUESTS_VIEW->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::FIELD_REQUESTED_BY_ID      => 'string',
            self::FIELD_REVIEWED_BY_ID       => 'string',
            self::FIELD_STATUS               => 'string',
            self::FIELD_RELATED_IDS          => 'array',
            self::FIELD_RELATED_IDS . '.*'   => 'required|numeric',
            self::FIELD_RELATED_TYPES        => 'array',
            self::FIELD_RELATED_TYPES . '.*' => 'required|' . Rule::in(ApprovableActionRelationTypes::cases()),
            self::FIELD_ORDER_BY             => 'array',
            self::FIELD_ACTION_TYPES         => 'array',
            self::FIELD_COMPANY_ID           => 'string',
            self::FIELD_ORDER_BY . '.*'      => [
                'string',
                function ($attribute, $value, $fail) {
                    foreach ($value as $item) {
                        // Match "field:asc" or "field:desc"
                        if (!preg_match('/^[a-zA-Z_]+:(asc|desc)$/', $item)) {
                            $fail("The {$attribute} value '{$item}' is invalid.");
                        }
                    }
                },
            ],
        ];
    }
}
