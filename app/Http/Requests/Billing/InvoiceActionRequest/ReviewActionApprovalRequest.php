<?php

namespace App\Http\Requests\Billing\InvoiceActionRequest;

use App\Enums\Billing\ApprovalStatus;
use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class ReviewActionApprovalRequest extends FormRequest
{
    const string FIELD_STATUS = 'status';
    const string FIELD_REASON = 'reason';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();
        return $user->hasPermissionTo(PermissionType::BILLING_ACTION_APPROVALS_REVIEW->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::FIELD_STATUS => 'required|' . Rule::in(ApprovalStatus::cases()),
            self::FIELD_REASON => 'string|nullable',
        ];
    }
}
