<?php

namespace App\Http\Requests\Billing\BillingManagement;

use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;

class GetBundlesSoldRequest extends FormRequest
{
    const string START_DATE   = 'startDate';
    const string END_DATE     = 'endDate';

    public function prepareForValidation(): void
    {
        $start = $this->startDate;
        $end   = $this->endDate;

        $this->merge([
            self::START_DATE   => Carbon::parse(str_replace('"', '', $start))->format('Y-m-d H:i:s'),
            self::END_DATE     => Carbon::parse(str_replace('"', '', $end))->format('Y-m-d H:i:s'),
        ]);
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::BILLING_MANAGEMENT_VIEW->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::START_DATE   => 'sometimes|string',
            self::END_DATE     => 'sometimes|string',
        ];
    }
}
