<?php

namespace App\Http\Requests\Billing\BillingManagement;

use App\Enums\Billing\InvoiceStates;
use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class GetFailedPaymentsRequest extends FormRequest
{
    const string START_DATE = 'startDate';
    const string END_DATE   = 'endDate';
    const string STATUS     = 'status';
    const string PAGE       = 'page';
    const string PER_PAGE   = 'perPage';

    public function prepareForValidation()
    {
        $start = $this->startDate;
        $end   = $this->endDate;

        $this->merge([
            self::START_DATE => Carbon::parse(str_replace('"', '', $start))->format('Y-m-d H:i:s'),
            self::END_DATE   => Carbon::parse(str_replace('"', '', $end))->format('Y-m-d H:i:s'),
            self::STATUS     => InvoiceStates::FAILED->value
        ]);
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::BILLING_MANAGEMENT_VIEW->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::START_DATE => 'sometimes|string',
            self::END_DATE   => 'sometimes|string',
            self::STATUS     => ['sometimes', 'string', Rule::enum(InvoiceStates::class)],
            self::PAGE       => 'nullable|numeric|min:1',
            self::PER_PAGE   => 'nullable|numeric|min:1|max:100',
        ];
    }
}
