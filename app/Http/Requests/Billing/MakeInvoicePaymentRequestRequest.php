<?php

namespace App\Http\Requests\Billing;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class MakeInvoicePaymentRequestRequest extends FormRequest
{
    const string FIELD_BILLING_PROFILE_ID = 'billing_profile_id';
    const string FIELD_AMOUNT             = 'amount';
    const string FIELD_DATE               = 'date';

    /**
     * TODO - Check
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::FIELD_BILLING_PROFILE_ID => 'required|numeric',
            self::FIELD_AMOUNT             => 'required|numeric|min:1',
            self::FIELD_DATE               => 'nullable|date',
        ];
    }
}
