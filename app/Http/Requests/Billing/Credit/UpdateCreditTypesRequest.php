<?php

namespace App\Http\Requests\Billing\Credit;

use App\Enums\PermissionType;
use App\Models\Billing\CreditType;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdateCreditTypesRequest extends FormRequest
{
    const string ACTIVE            = 'active';
    const string CASH              = 'cash';
    const string DESCRIPTION       = 'description';
    const string EXPIRES_IN_DAYS   = 'expires_in_days';
    const string LINE_ITEM_TEXT    = 'line_item_text';
    const string NAME              = 'name';
    const string CONSUMPTION_ORDER = 'consumption_order';
    const string SLUG              = 'slug';
    const string ID                = 'id';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();
        return $user->hasPermissionTo(PermissionType::BILLING_UPDATE_CREDIT_TYPE->value);
    }

    protected function prepareForValidation(): void
    {
        $data = $this->all();

        foreach ($data['credit_types'] as $index => $credit_type) {
            $data['credit_types'][$index][self::CONSUMPTION_ORDER] = $index + 1;
        }

        $this->replace($data);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule>
     */
    public function rules(): array
    {
        return [
            'credit_types.*.' . self::ID                => 'required|int',
            'credit_types.*.' . self::ACTIVE            => 'required|bool',
            'credit_types.*.' . self::CASH              => 'required|bool',
            'credit_types.*.' . self::DESCRIPTION       => 'required|string',
            'credit_types.*.' . self::EXPIRES_IN_DAYS   => 'nullable|integer|min:1|max:1000',
            'credit_types.*.' . self::LINE_ITEM_TEXT    => 'required|string',
            'credit_types.*.' . self::NAME              => 'required|string',
            'credit_types.*.' . self::CONSUMPTION_ORDER => 'integer|min:1',
            'credit_types.*.' . self::SLUG              => "string"
        ];
    }
}
