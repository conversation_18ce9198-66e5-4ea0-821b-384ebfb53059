<?php

namespace App\Http\Requests\Billing\Credit;

use App\Enums\PermissionType;
use App\Models\Billing\Credit;
use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class ExtendCreditRequest extends FormRequest
{
    const string COMPANY_ID = 'company_id';
    const string CREDIT_ID  = 'credit_id';
    const string NEW_DATE   = 'new_date';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::BILLING_EXTEND_COMPANY_CREDITS->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule>
     */
    public function rules(): array
    {
        return [
            self::COMPANY_ID => 'required|numeric|exists:' . Company::TABLE . ',' . Company::FIELD_ID,
            self::CREDIT_ID  => 'required|numeric|exists:' . Credit::TABLE . ',' . Credit::FIELD_ID,
            self::NEW_DATE   => 'required|after:today'
        ];
    }
}
