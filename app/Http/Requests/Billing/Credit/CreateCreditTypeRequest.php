<?php

namespace App\Http\Requests\Billing\Credit;

use App\Enums\PermissionType;
use App\Models\Billing\CreditType;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class CreateCreditTypeRequest extends FormRequest
{
    const string ACTIVE            = 'active';
    const string CASH              = 'cash';
    const string DESCRIPTION       = 'description';
    const string EXPIRES_IN_DAYS   = 'expires_in_days';
    const string EXPIRY            = 'expiry';
    const string LINE_ITEM_TEXT    = 'line_item_text';
    const string NAME              = 'name';
    const string CONSUMPTION_ORDER = 'consumption_order';
    const string SLUG              = 'slug';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();
        return $user->hasPermissionTo(PermissionType::BILLING_CREATE_CREDIT_TYPE->value);
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            self::SLUG              => Str::slug($this->name),
            self::CONSUMPTION_ORDER => CreditType::query()->max(CreditType::FIELD_CONSUMPTION_ORDER) + 1
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule>
     */
    public function rules(): array
    {
        $creditType = CreditType::TABLE;

        return [
            self::ACTIVE            => 'required|bool',
            self::CASH              => 'required|bool',
            self::DESCRIPTION       => 'required|string',
            self::EXPIRY            => 'required|bool',
            self::EXPIRES_IN_DAYS   => 'exclude_unless:' . self::EXPIRY . ',true|integer|min:1',
            self::LINE_ITEM_TEXT    => 'required|string',
            self::NAME              => 'required|string',
            self::CONSUMPTION_ORDER => 'sometimes|integer|min:0|max:1000',
            self::SLUG              => "string|unique:{$creditType}"
        ];
    }

    public function messages()
    {
        return [
            ...parent::messages(),
            self::SLUG . '.unique' => 'This name is already used'
        ];
    }
}
