<?php

namespace App\Http\Requests\Billing\Credit;

use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class GetCreditTypesRequest extends FormRequest
{
    const string FIELD_PER_PAGE = 'perPage';
    const string FIELD_PAGE     = 'page';
    const string FIELD_IS_CASH  = 'is_cash';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();
        return $user->hasPermissionTo(PermissionType::BILLING_VIEW_CREDIT_TYPE->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule>
     */
    public function rules(): array
    {
        return [
            self::FIELD_PAGE     => 'sometimes|int',
            self::FIELD_PER_PAGE => 'sometimes|int',
            self::FIELD_IS_CASH  => 'sometimes|bool'
        ];
    }
}
