<?php

namespace App\Http\Requests\Billing\Credit;

use App\Enums\PermissionType;
use App\Models\Billing\CreditType;
use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class AddCreditRequest extends FormRequest
{
    const string FIELD_TYPE       = 'type';
    const string FIELD_VALUE      = 'value';
    const string FIELD_COMPANY_ID = 'company_id';
    const string FIELD_NOTE       = 'note';
    const string FIELD_BILLING_PROFILE_IDS       = 'billing_profile_ids';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();
        return $user->hasPermissionTo(PermissionType::BILLING_APPLY_COMPANY_CREDITS->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule>
     */
    public function rules(): array
    {
        return [
            self::FIELD_VALUE      => 'required|decimal:0,2|min:1',
            self::FIELD_TYPE       => 'required|exists:' . CreditType::TABLE . ',' . CreditType::FIELD_SLUG,
            self::FIELD_COMPANY_ID => 'required|exists:' . Company::TABLE . ',' . Company::FIELD_ID,
            self::FIELD_NOTE       => 'required|max:255',
            self::FIELD_BILLING_PROFILE_IDS => 'array|nullable'
        ];
    }
}
