<?php

namespace App\Http\Requests\Billing;

use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class GetCompanyRevenueGraphDataRequest extends FormRequest
{
    const string FIELD_PERIOD      = 'period';
    const string FIELD_DURATION    = 'duration';
    const string FIELD_INDUSTRY_ID = 'industry_id';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::FIELD_PERIOD      => 'nullable',
            self::FIELD_DURATION    => 'nullable',
            self::FIELD_INDUSTRY_ID => 'nullable',
        ];
    }
}
