<?php

namespace App\Http\Requests\Billing;

use App\Enums\Billing\Refunds\RefundReason;
use App\Enums\PermissionType;
use App\Models\Billing\InvoiceItem;
use App\Models\Billing\InvoiceRefundItem;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class IssueRefundRequest extends FormRequest
{
    const string INVOICE_ID    = 'invoice_id';
    const string CUSTOM_AMOUNT = 'custom_amount_refunded';
    const string REFUND_ITEMS  = 'items';
    const string REFUND_REASON = 'refund_reason';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::BILLING_VIEW_INVOICE->value);
    }

    public function prepareForValidation(): void
    {
        $refundItems = collect($this->{self::REFUND_ITEMS});

        $customAmount = $this->{self::CUSTOM_AMOUNT};


        $itemIds = $refundItems->pluck('invoice_item_id')->toArray();

        $this->merge([
            self::REFUND_ITEMS => $itemIds
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::CUSTOM_AMOUNT       => 'numeric|nullable',
            self::REFUND_ITEMS        => 'array|nullable',
            self::REFUND_ITEMS . '.*' => ['exists:' . InvoiceItem::TABLE . ',' . InvoiceItem::FIELD_ID, 'unique:' . InvoiceRefundItem::TABLE . ',' . InvoiceRefundItem::FIELD_INVOICE_ITEM_ID],
            self::REFUND_REASON       => Rule::enum(RefundReason::class)
        ];
    }

    public function messages(): array
    {
        return [
            self::REFUND_ITEMS . '.*.exists:' . InvoiceItem::TABLE . ',' . InvoiceItem::FIELD_ID                          => 'Invoice Item does not exist',
            self::REFUND_ITEMS . '.*.unique:' . InvoiceRefundItem::TABLE . ',' . InvoiceRefundItem::FIELD_INVOICE_ITEM_ID => 'Invoice Item already associated with another refund',
        ];
    }
}
