<?php

namespace App\Http\Requests\Billing\InvoiceCredits;

use App\Enums\RoleType;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class ApplyCreditToInvoiceRequest extends FormRequest
{
    const string FIELD_AMOUNT = 'amount';
    const string FIELD_TYPE   = 'type';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();
        return $user->hasRole(RoleType::FINANCE_OWNER->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::FIELD_AMOUNT => 'required|numeric|min:1',
            self::FIELD_TYPE   => 'required|string',
        ];
    }
}
