<?php

namespace App\Http\Requests\Billing\InvoiceCollections;

use App\Enums\Billing\InvoiceCollectionsRecoveryStatus;
use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UpdateInvoiceCollectionsDataRequest extends FormRequest
{
    const string FIELD_INVOICE_ID       = 'invoice_id';
    const string FIELD_AMOUNT_RECOVERED = 'amount_recovered';
    const string FIELD_RECOVERY_STATUS  = 'recovery_status';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::BILLING_VIEW_INVOICE->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::FIELD_INVOICE_ID       => 'numeric',
            self::FIELD_AMOUNT_RECOVERED => 'numeric',
            self::FIELD_RECOVERY_STATUS  => 'string|' . Rule::in(InvoiceCollectionsRecoveryStatus::cases()),
        ];
    }
}
