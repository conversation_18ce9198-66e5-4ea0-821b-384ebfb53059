<?php

namespace App\Http\Requests\Billing;

use App\Enums\PermissionType;
use App\Models\Billing\Invoice;
use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class GetInvoiceRequest extends FormRequest
{
    const string INVOICE_ID     = 'invoice_id';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::BILLING_VIEW_INVOICE->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::INVOICE_ID   => 'sometimes|int|exists:' . Invoice::TABLE . ',' . Invoice::FIELD_ID,
        ];
    }
}
