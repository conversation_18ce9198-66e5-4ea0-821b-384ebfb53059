<?php

namespace App\Http\Requests;

use App\Enums\Flows\RevisionType;
use App\Models\Firestore\Flows\Revision;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreRevisionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo('flow-management');
    }

    /**
     * Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $revisionTypes = array_map(fn($v) => $v->value, RevisionType::cases());

        return [
            Revision::NAME              => ['string', 'min:4', 'max:64'],
            Revision::DESCRIPTION       => ['string', 'max:256', 'nullable'],
            Revision::TYPE              => [Rule::in($revisionTypes)],
            Revision::VERSION           => ['regex:/^\d+\.\d+$/'],
            Revision::PARENT_REVISION   => ['uuid', 'nullable'],
            Revision::REVISION_DATA     => ['string'],
        ];
    }

}
