<?php

namespace App\Http\Requests\QAAutomation;

use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdateQAAutomationIndustryServiceRequest extends FormRequest
{
    const string REQUEST_INDUSTRY_SERVICE_ID = 'industry_service_id';
    const string REQUEST_TYPE = 'type';
    const string REQUEST_ENABLED = 'enabled';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::QA_AUTOMATION_INDUSTRY_SERVICE_MANAGEMENT_EDIT->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::REQUEST_INDUSTRY_SERVICE_ID =>
                [
                    "sometimes",
                    "integer",
                ],
            self::REQUEST_ENABLED =>
                [
                    "sometimes",
                    "bool"
                ],
            self::REQUEST_TYPE =>
                [
                    "sometimes",
                    "integer",
                ],
        ];
    }

}