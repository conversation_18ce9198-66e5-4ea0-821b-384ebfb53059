<?php

namespace App\Http\Requests\QAAutomation;

use App\Enums\PermissionType;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyReview;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdateQARulesRequest extends FormRequest
{
    const string REQUEST_TYPE = 'type';
    const string REQUEST_EXPRESSION = 'expression';
    const string REQUEST_FIELDS = 'fields';
    const string REQUEST_ENABLED = 'enabled';
    const string REQUEST_MATCH_SUCCESS = 'match_success';
    const string REQUEST_DATA = 'data';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::QA_AUTOMATION_RULE_MANAGEMENT_EDIT->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::REQUEST_TYPE =>
                [
                    "required",
                    "integer",
                ],
            self::REQUEST_EXPRESSION =>
                [
                    "required",
                    "string",
                ],
            self::REQUEST_ENABLED =>
                [
                    "sometimes",
                    "bool"
                ],
            self::REQUEST_MATCH_SUCCESS =>
                [
                    "sometimes",
                    "bool"
                ],
            self::REQUEST_FIELDS =>
                [
                    "sometimes",
                    "array"
                ],
            self::REQUEST_DATA =>
                [
                    "sometimes",
                    "array"
                ],
        ];
    }
}
