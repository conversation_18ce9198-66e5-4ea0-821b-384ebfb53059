<?php

namespace App\Http\Requests;

use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentCompanyAddress;
use App\Rules\Phone;
use Illuminate\Foundation\Http\FormRequest;

class StoreCompanyAddressRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return
            [
                EloquentCompanyAddress::FIELD_IS_DEFAULT  => "nullable|bool",
                EloquentCompanyAddress::FIELD_NAME        => "nullable|string|max:255",
                EloquentAddress::ADDRESS1                 => "required|string|max:255",
                EloquentAddress::ADDRESS2                 => "nullable|string|max:64",
                EloquentAddress::CITY                     => "required|string|max:64",
                EloquentAddress::STATE                    => "required|string|max:2",
                EloquentAddress::ZIP_CODE                 => "required|string|max:5",
                EloquentAddress::PHONE                    => ["nullable", new Phone],
                EloquentAddress::FAX                      => "nullable|max:20"
            ];
    }
}
