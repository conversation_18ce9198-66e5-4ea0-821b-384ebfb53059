<?php

namespace App\Http\Requests\Bundles;

use Illuminate\Foundation\Http\FormRequest;

class PurchaseBundleRequest extends FormRequest
{
    const REQUEST_COMPANY_REFERENCE = 'company_reference';
    const REQUEST_REGISTRATION_ORIGIN   = 'registration_origin';

    /**
     * This request is open to the public FIXR and roofing calculator registration flows.
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            self::REQUEST_COMPANY_REFERENCE  => 'required|string|size:10',
            self::REQUEST_REGISTRATION_ORIGIN   => 'required|string',
        ];
    }
}
