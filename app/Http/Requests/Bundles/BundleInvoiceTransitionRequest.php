<?php

namespace App\Http\Requests\Bundles;

use App\Models\BundleInvoice;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class BundleInvoiceTransitionRequest extends FormRequest
{
    public const REQUEST_TRANSITION = 'transition';
    public const REQUEST_ALLOWED_TRANSITIONS = [
        BundleInvoice::TRANSITION_ISSUE,
        BundleInvoice::TRANSITION_PAID,
        BundleInvoice::TRANSITION_APPROVE,
        BundleInvoice::TRANSITION_DENY,
        BundleInvoice::TRANSITION_CANCEL,
        BundleInvoice::TRANSITION_FAIL,
    ];

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        /** @todo decide on a method of authorizing API calls where there is no user */
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            self::REQUEST_TRANSITION => ['required', 'string', Rule::in(self::REQUEST_ALLOWED_TRANSITIONS)],
            BundleInvoice::FIELD_FAIL_REASON => ['sometimes', 'string', 'max:200']
        ];
    }
}
