<?php

namespace App\Http\Requests\Bundles;

use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class StoreBundleInvoiceRequest extends FormRequest
{
    const string MODULE_PERMISSION = 'manage-bundle-invoices';

    const string FIELD_BUNDLE_ID           = 'bundle_id';
    const string FIELD_COMPANY_ID          = 'company_id';
    const string FIELD_PAYMENT_METHOD      = 'payment_method';
    const string FIELD_BILLING_VERSION     = 'billing_version';
    const string FIELD_BILLING_PROFILE_IDS = 'billing_profile_ids';


    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        /** @var User $user */
        $user = Auth::user();
        return $user->hasPermissionTo(self::MODULE_PERMISSION);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            self::FIELD_BUNDLE_ID           => ['required', 'numeric'],
            self::FIELD_COMPANY_ID          => ['required', 'numeric'],
            self::FIELD_BILLING_VERSION     => ['required', 'string'],
            self::FIELD_BILLING_PROFILE_IDS => ['nullable', 'array'],
            self::FIELD_PAYMENT_METHOD      => ['string'],
        ];
    }
}
