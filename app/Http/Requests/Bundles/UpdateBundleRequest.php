<?php

namespace App\Http\Requests\Bundles;

use App\Models\Bundle;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdateBundleRequest extends FormRequest
{
    const MODULE_PERMISSION = 'manage-bundles';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        /** @var User $user */
        $user = Auth::user();
        return $user->hasPermissionTo(self::MODULE_PERMISSION);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            Bundle::FIELD_NAME => ['required', 'string'],
            Bundle::FIELD_NOTE => ['nullable', 'string'],
            Bundle::FIELD_TITLE => ['required', 'string'],
            Bundle::FIELD_DESCRIPTION => ['nullable', 'string'],
            Bundle::FIELD_ACTIVATED_AT => ['nullable', 'string'],
            Bundle::FIELD_COST => ['required', 'numeric'],
            Bundle::FIELD_CREDIT => ['required', 'numeric', function($attribute, $value, $fail) {
                if ($value < $this->input(Bundle::FIELD_COST)) {
                    $fail('The credit amount cannot be less than the cost.');
                }
            }],
            'active' => ['nullable', 'boolean'],
            Bundle::FIELD_INDUSTRY_ID => ['nullable', 'numeric'],
        ];
    }
}
