<?php

namespace App\Http\Requests\Bundles;

use App\Models\Odin\Industry;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CompanyRegistrationBundleRequest extends FormRequest
{
    const REQUEST_INDUSTRY_SLUG = 'industry_slug';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            self::REQUEST_INDUSTRY_SLUG => ['sometimes', Rule::exists(Industry::class,Industry::FIELD_SLUG )],
        ];
    }
}
