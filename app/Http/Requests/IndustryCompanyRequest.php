<?php

namespace App\Http\Requests;

use App\Http\Controllers\API\IndustryManagement\IndustryManagementAPIController;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyIndustry;
use App\Models\Odin\Industry;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class IndustryCompanyRequest extends FormRequest
{
    /**
    * Determine if the user is authorized to make this request.
    *
    * @return bool
    */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo('industry-management');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return
            [
                IndustryManagementAPIController::REQUEST_INDUSTRY =>
                    [
                        "required",
                        "in:" . implode(',', Industry::query()->distinct()->pluck(Industry::FIELD_ID)->toArray())
                    ],
                CompanyIndustry::FIELD_COMPANY_ID =>
                    [
                        "required",
                        "in:" . implode(',', Company::query()->distinct()->pluck(Company::FIELD_ID)->toArray())
                    ]
            ];
    }

    /**
     * @return void
     */
    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                IndustryManagementAPIController::REQUEST_INDUSTRY => $this->route()->parameter(IndustryManagementAPIController::REQUEST_INDUSTRY)
            ]
        );
    }
}
