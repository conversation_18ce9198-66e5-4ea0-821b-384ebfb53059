<?php

namespace App\Http\Requests;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use Illuminate\Foundation\Http\FormRequest;
use App\Http\Controllers\API\CompanyRegistration\CompanyRegistrationController;

class AddServiceAreaFormRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** TODO- need to decide permissions */
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return
            [
                CompanyRegistrationController::REQUEST_COMPANY_REFERENCE    =>
                    [
                        "required",
                        "exists:" . Company::TABLE . ',' . Company::FIELD_REFERENCE
                    ],
                CompanyRegistrationController::REQUEST_COMPANY_LOCATION     =>
                    [
                        "required",
                        "exists:" . CompanyLocation::TABLE . ',' . CompanyLocation::FIELD_ID,
                    ],
                CompanyRegistrationController::REQUEST_COMPANY_SERVICE_AREA =>
                    [
                        "required",
                        "integer",
                        "in:" . implode(',', CompanyRegistrationController::PERMITTED_RADIUS_LIST)
                    ]
            ];
    }
}

