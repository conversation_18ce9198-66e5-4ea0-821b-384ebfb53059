<?php

namespace App\Http\Requests\Rulesets;

use App\Enums\Logical;
use App\Enums\PermissionType;
use App\Http\Controllers\API\Rulesets\RulesetsApiController;
use App\Models\User;
use App\Services\Odin\Ruleset\Enums\ComparisonType;
use App\Services\Odin\Ruleset\Enums\OperationType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreRulesetRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::RULESET_MANAGEMENT->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return
        [
            RulesetsApiController::REQUEST_NAME    => "required|string",
            RulesetsApiController::REQUEST_TYPE    => "required|string",
            RulesetsApiController::REQUEST_SOURCE  => "required|string",
            RulesetsApiController::REQUEST_FILTER  => "sometimes", // TODO - Validate based on the source
            ...$this->getRulesRules()
        ];
    }

    /**
     * Handles preparing validation rules for the incoming 'rules' payload.
     *
     * @return array
     */
    private function getRulesRules(): array
    {
        return
        [
            RulesetsApiController::REQUEST_RULES                      => "required|array|min:1",
            RulesetsApiController::REQUEST_RULES . ".*.rule_type"     => "required",
            RulesetsApiController::REQUEST_RULES . ".*.is_active"     => "required|boolean",

            RulesetsApiController::REQUEST_RULES . ".*.data.label"          => "required|string",
            RulesetsApiController::REQUEST_RULES . ".*.data.description"    => "required|string",

            RulesetsApiController::REQUEST_RULES . ".*.data.rule_data"                                    => "required|array",
            RulesetsApiController::REQUEST_RULES . ".*.data.rule_data.max_points"                         => "required|integer",
            RulesetsApiController::REQUEST_RULES . ".*.data.rule_data.historical_window_in_days"          => "sometimes",
            RulesetsApiController::REQUEST_RULES . ".*.data.rule_data.conditions"                         => "required|array|min:1",
            RulesetsApiController::REQUEST_RULES . ".*.data.rule_data.conditions.*.points"                => "required|integer|min:0",


            RulesetsApiController::REQUEST_RULES . ".*.data.rule_data.conditions.*.operations"                         => "required|array",
            RulesetsApiController::REQUEST_RULES . ".*.data.rule_data.conditions.*.operations.*.type"                  => "required",
            RulesetsApiController::REQUEST_RULES . ".*.data.rule_data.conditions.*.operations.*.comparison_field_name" => "sometimes",
            RulesetsApiController::REQUEST_RULES . ".*.data.rule_data.conditions.*.operations.*.logical_operator"      => "sometimes|" . Rule::in([Logical::AND->value,Logical::OR->value]),

            RulesetsApiController::REQUEST_RULES . ".*.data.rule_data.conditions.*.operations.*.reference"       => "required|array",
            RulesetsApiController::REQUEST_RULES . ".*.data.rule_data.conditions.*.operations.*.reference.start" => "sometimes|nullable", // |required_unless:rules.*.rule_data.conditions.*.operations.*.type," . OperationType::RATE->value,
            RulesetsApiController::REQUEST_RULES . ".*.data.rule_data.conditions.*.operations.*.reference.end"   => "sometimes|nullable|required_if:rules.*.rule_data.conditions.*.operations.*.type," . OperationType::BETWEEN->value,
            RulesetsApiController::REQUEST_RULES . ".*.data.rule_data.conditions.*.operations.*.reference.rate"  => "sometimes|nullable|required_if:rules.*.rule_data.conditions.*.operations.*.type," . OperationType::RATE->value,
        ];
    }
}
