<?php

namespace App\Http\Requests;

use App\Enums\LeadRefundStatus;
use App\Http\Controllers\API\CompaniesController;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class SearchCompanyLeadsRequests extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            CompaniesController::REQUEST_CAMPAIGN => ['string'],
            CompaniesController::REQUEST_CAMPAIGN_ID => ['string'],
            CompaniesController::REQUEST_STATUS => ['nullable'],
            CompaniesController::REQUEST_SERVICE => ['nullable'],
            CompaniesController::REQUEST_LEAD_ID => ['nullable', 'integer'],
            CompaniesController::REQUEST_NAME => ['nullable', 'string', 'min:3'],
            CompaniesController::REQUEST_STATE => ['nullable'],
            CompaniesController::REQUEST_PAGE => ['nullable', 'integer'],
            CompaniesController::REQUEST_DATE_FROM => ['nullable'],
            CompaniesController::REQUEST_DATE_TO => ['nullable'],
            CompaniesController::REQUEST_PRODUCT_ID => ['nullable'],
            CompaniesController::REQUEST_LEAD_REFUND_STATUS => ['nullable', Rule::in(LeadRefundStatus::cases())],
        ];
    }
}
