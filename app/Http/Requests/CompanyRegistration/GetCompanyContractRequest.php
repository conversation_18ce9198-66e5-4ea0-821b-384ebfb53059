<?php

namespace App\Http\Requests\CompanyRegistration;

use App\Http\Controllers\API\CompanyRegistration\CompanyRegistrationController;
use Illuminate\Foundation\Http\FormRequest;

class GetCompanyContractRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {

        return [
            CompanyRegistrationController::REQUEST_COMPANY_REFERENCE     => 'required|string|size:10',
            CompanyRegistrationController::REQUEST_USER_REFERENCE        => 'required|uuid',
            CompanyRegistrationController::REQUEST_CONTRACT_TYPE         => 'required|string',
            CompanyRegistrationController::REQUEST_REGISTRATION_ORIGIN   => 'required|string',
            CompanyRegistrationController::REQUEST_CONTRACT_APP_ID       => 'required|string'

        ];
    }

}