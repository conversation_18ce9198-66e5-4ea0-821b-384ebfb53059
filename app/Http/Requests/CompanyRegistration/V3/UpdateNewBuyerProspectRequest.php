<?php

namespace App\Http\Requests\CompanyRegistration\V3;

use App\Models\Prospects\NewBuyerProspect;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class UpdateNewBuyerProspectRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            NewBuyerProspect::SOURCE_KEY_COMPANY_TURNOVER => 'string',
            NewBuyerProspect::SOURCE_KEY_NUMBER_OF_EMPLOYEES => 'integer',
            NewBuyerProspect::SOURCE_KEY_USES_CRM => 'in:yes,no',
            NewBuyerProspect::SOURCE_KEY_YEARS_IN_BUSINESS => 'integer',
            NewBuyerProspect::SOURCE_KEY_WHO_CALLS_LEADS => 'string',
            NewBuyerProspect::SOURCE_KEY_BUYING_LEADS_FROM => 'string',
        ];
    }
}
