<?php

namespace App\Http\Requests\CompanyRegistration\V3;

use App\Models\Prospects\NewBuyerProspect;
use App\Rules\Phone;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class CreateNewBuyerProspectRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            NewBuyerProspect::FIELD_COMPANY_NAME          => 'required|string',
            NewBuyerProspect::SOURCE_KEY_WEBSITE          => 'required',
            NewBuyerProspect::SOURCE_KEY_BUYING_LEADS     => 'required|in:yes,no',
            NewBuyerProspect::SOURCE_KEY_EMAIL            => 'required|email',
            NewBuyerProspect::SOURCE_KEY_ENTITY_NAME      => 'required|string',
            NewBuyerProspect::SOURCE_KEY_INDUSTRIES       => 'required|array',
            NewBuyerProspect::SOURCE_KEY_JOB_ROLE         => 'nullable|string',
            NewBuyerProspect::SOURCE_KEY_FIRST_NAME       => 'required|string',
            NewBuyerProspect::SOURCE_KEY_LAST_NAME        => 'required|string',
            NewBuyerProspect::SOURCE_KEY_PHONE            => ['required', new Phone],
            NewBuyerProspect::SOURCE_KEY_STATES           => 'required|array',
            NewBuyerProspect::SOURCE_KEY_REGISTRATION_URL => 'required|url'
        ];
    }
}
