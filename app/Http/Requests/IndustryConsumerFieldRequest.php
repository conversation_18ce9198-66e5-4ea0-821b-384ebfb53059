<?php

namespace App\Http\Requests;

use App\Http\Controllers\API\IndustryManagement\IndustryManagementAPIController;
use App\Models\Odin\ConfigurableFieldType;
use App\Models\Odin\ConsumerConfigurableFieldCategory;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryConsumerField;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class IndustryConsumerFieldRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo("industry-management");
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        // Request params inject by laravel router
        $industry = $this->industry;
        $industryConsumerField = $this->industryConsumerField;

        return
            [
                IndustryManagementAPIController::REQUEST_INDUSTRY =>
                    [
                        "required",
                        "in:" . implode(",", Industry::query()->distinct()->pluck(Industry::FIELD_ID)->toArray())
                    ],
                IndustryManagementAPIController::REQUEST_INDUSTRY_CONSUMER_FIELD =>
                    [
                        "nullable",
                        "in:" . implode(",", IndustryConsumerField::query()->distinct()->pluck(IndustryConsumerField::FIELD_ID)->toArray())
                    ],

                // TODO - Refactor this temporary code to share validations with configurable fields service request
                // app/Http/Requests/ConfigurableFields/SaveConfigurableFieldsRequest.php
                IndustryConsumerField::FIELD_NAME =>
                    [
                        "required",
                        Rule::unique(IndustryConsumerField::TABLE)
                            ->where(fn($query) => $query->where(IndustryConsumerField::FIELD_INDUSTRY_ID, $industry))
                            ->ignore($industryConsumerField),
                        "string",
                        "max:255",
                    ],
                IndustryConsumerField::FIELD_KEY =>
                    [
                        "required",
                        "string",
                        "max:255",
                        Rule::unique(IndustryConsumerField::TABLE)
                            ->where(fn($query) => $query->where(IndustryConsumerField::FIELD_INDUSTRY_ID, $industry))
                            ->ignore($industryConsumerField),
                        "alpha_dash"
                    ],
                IndustryConsumerField::FIELD_TYPE =>
                    [
                        "required",
                        "in:" . implode(",", ConfigurableFieldType::query()->distinct()->pluck(ConfigurableFieldType::FIELD_ID)->toArray())
                    ],
                IndustryConsumerField::FIELD_CATEGORY_ID =>
                    [
                        "required",
                        "in:" . implode(",", ConsumerConfigurableFieldCategory::query()->distinct()->pluck(ConsumerConfigurableFieldCategory::FIELD_ID)->toArray())
                    ],
                IndustryConsumerField::FIELD_SEND_TO_COMPANY =>
                    [
                        "required",
                        "bool"
                    ],
                IndustryConsumerField::FIELD_SHOW_ON_DASHBOARD =>
                    [
                        "required",
                        "bool"
                    ]
            ];
    }

    /**
     * @return void
     */
    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                IndustryManagementAPIController::REQUEST_INDUSTRY                => $this->route()->parameter(IndustryManagementAPIController::REQUEST_INDUSTRY),
                IndustryManagementAPIController::REQUEST_INDUSTRY_CONSUMER_FIELD => $this->route()->parameter(IndustryManagementAPIController::REQUEST_INDUSTRY_CONSUMER_FIELD)
            ]
        );
    }
}

