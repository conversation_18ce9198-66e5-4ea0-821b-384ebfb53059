<?php

namespace App\Http\Requests;

use App\Enums\Odin\Product;
use App\Enums\Odin\PropertyType;
use App\Http\Controllers\API\FloorPricing\FloorPricingController;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class GetFloorPricingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(FloorPricingController::MODULE_PERMISSION);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            FloorPricingController::REQUEST_PRODUCT             => ['required', Rule::in(array_column(Product::cases(), 'value'))],
            FloorPricingController::REQUEST_INDUSTRY_SERVICE_ID => ['required', 'numeric'],
            FloorPricingController::REQUEST_PROPERTY_TYPE       => ['nullable', Rule::in(array_column(PropertyType::cases(), 'value'))],
            FloorPricingController::REQUEST_STATE_ID            => ['nullable', 'numeric'],
            FloorPricingController::REQUEST_QUALITY_TIER        => ['sometimes', 'string'],
        ];
    }
}
