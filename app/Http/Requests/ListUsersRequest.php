<?php

namespace App\Http\Requests;

use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class ListUsersRequest extends FormRequest
{
    const string REQUEST_SHOW_DEACTIVATED = 'show_deactivated';

    const string REQUEST_USER_DETAIL = 'user_detail';

    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();
        return $user->hasPermissionTo(PermissionType::USER_MANAGEMENT->value);

    }

    public function rules(): array
    {
        return [
            self::REQUEST_SHOW_DEACTIVATED  => ['boolean'],
            self::REQUEST_USER_DETAIL       => ['string', 'nullable', 'sometimes'],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            self::REQUEST_SHOW_DEACTIVATED => filter_var($this->input(self::REQUEST_SHOW_DEACTIVATED), FILTER_VALIDATE_BOOLEAN)
        ]);
    }
}
