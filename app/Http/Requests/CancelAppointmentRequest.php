<?php

namespace App\Http\Requests;

use App\Enums\Odin\AppointmentCancellationReason;
use App\Http\Controllers\API\Appointments\AppointmentController;
use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class CancelAppointmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * @return array
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function rules(): array
    {
        $cancellationReason = request()->get(AppointmentController::REQUEST_CANCELLATION_REASON);

        if($cancellationReason === AppointmentCancellationReason::RESCHEDULED->value) {
            $rescheduledDateRules = [
                'required',
                'integer',
                'gt:'.Carbon::now()->timestamp
            ];
        }
        else {
            $rescheduledDateRules = [
                'present',
                'integer',
                'size:0'
            ];
        }

        return [
            AppointmentController::REQUEST_APPOINTMENT_KEY => ['required', 'uuid', 'size:36'],
            AppointmentController::REQUEST_APPOINTMENT_CODE => ['required', 'string', 'size:6', 'regex:/^[0-9]+$/'],
            AppointmentController::REQUEST_CANCELLATION_REASON => ['required', new Enum(AppointmentCancellationReason::class)],
            AppointmentController::REQUEST_CANCELLATION_NOTE => ['nullable', 'string', 'max:250'],
            AppointmentController::REQUEST_RESCHEDULED_DATE => $rescheduledDateRules
        ];
    }
}
