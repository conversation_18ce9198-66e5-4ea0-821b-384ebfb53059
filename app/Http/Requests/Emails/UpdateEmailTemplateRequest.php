<?php

namespace App\Http\Requests\Emails;

use App\Models\EmailTemplate;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class UpdateEmailTemplateRequest extends FormRequest
{
    const string REQUEST_CONTENT       = 'content';
    const string REQUEST_SUBJECT       = 'subject';
    const string REQUEST_TEMPLATE_NAME = 'name';
    const string REQUEST_TEMPLATE_ID   = 'templateId';
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::REQUEST_CONTENT       => 'required',
            self::REQUEST_SUBJECT       => 'required',
            self::REQUEST_TEMPLATE_NAME => 'required',
            self::REQUEST_TEMPLATE_ID   => 'nullable|exists:' . EmailTemplate::TABLE . ',' . EmailTemplate::FIELD_ID
        ];
    }
}