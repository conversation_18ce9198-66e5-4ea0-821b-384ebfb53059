<?php

namespace App\Http\Requests;

use App\Constants\Company\Search\FilterRequestParams as RequestParams;
use App\DataModels\AmountOfLeadsPurchasedObject;
use App\Enums\Cadence;
use App\Enums\CampaignStatus;
use App\Enums\CompanyConsolidatedStatus;
use App\Enums\CompanySalesStatus;
use App\Enums\DateFormatOption;
use App\Enums\Logical;
use App\Enums\Odin\StateAbbreviation;
use App\Enums\Operator;
use App\Exceptions\ValidatorException;
use App\Models\User;
use App\Rules\SortQueryParam;
use Exception;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\Rules\Exists;
use Illuminate\Validation\ValidationException;

class SearchCompaniesRequest extends FormRequest
{
    protected AmountOfLeadsPurchasedObject $amountOfLeadsPurchasedObject;

    public function __construct(
        array $query = [],
        array $request = [],
        array $attributes = [],
        array $cookies = [],
        array $files = [],
        array $server = [],
        $content = null
    ) {
        parent::__construct($query, $request, $attributes, $cookies, $files, $server, $content);
        $this->amountOfLeadsPurchasedObject = app(AmountOfLeadsPurchasedObject::class);

    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->can(['companies']);
    }

    /**
     * @throws ValidationException
     */
    public function passedValidation(): void
    {
        if ($this->has(RequestParams::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT)) {
            $array = $this->input(RequestParams::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT);

            $this->amountOfLeadsPurchasedObject->setProperties($array);

            try {
                $this->amountOfLeadsPurchasedObject->validateProperties();
            } catch (ValidatorException $exception) {
                $this->validator->errors()->add($exception->getKey(), $exception->getMessage());
                throw new ValidationException($this->validator);
            } catch (Exception $exception) {
                throw ValidationException::withMessages([RequestParams::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT => $exception->getMessage()]);
            }

            $this->merge([RequestParams::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT => $this->amountOfLeadsPurchasedObject->toArray()]);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            RequestParams::REQUEST_STATUS_IDS => ['array', 'sometimes'],
            RequestParams::REQUEST_STATUS_IDS.'.*' => [new Enum(CompanyConsolidatedStatus::class)],
            RequestParams::REQUEST_SALES_STATUS_IDS => ['array', 'sometimes'],
            RequestParams::REQUEST_SALES_STATUS_IDS.'.*' => [new Enum(CompanySalesStatus::class)],
            RequestParams::REQUEST_STATE_ABBREVIATIONS => ['array', 'sometimes'],
            RequestParams::REQUEST_STATE_ABBREVIATIONS.'.*' => [new Enum(StateAbbreviation::class)],
            RequestParams::REQUEST_ACCOUNT_MANAGER_IDS => ['array', 'sometimes'],
            RequestParams::REQUEST_ACCOUNT_MANAGER_IDS.'.*' => [new Exists('account_managers', 'id')],
            RequestParams::REQUEST_INDUSTRY_IDS => ['array', 'sometimes'],
            RequestParams::REQUEST_INDUSTRY_IDS.'.*' => [new Exists('industries', 'id')],
            RequestParams::REQUEST_INDUSTRY_LOGICAL => [new Enum(Logical::class), 'sometimes', 'nullable'],
            RequestParams::REQUEST_OFFICE_LOCATION_IDS.'.*' => [new Enum(StateAbbreviation::class), 'sometimes'],
            RequestParams::REQUEST_OFFICE_LOCATION_LOGICAL => [new Enum(Logical::class), 'sometimes', 'nullable'],
            RequestParams::REQUEST_COMPANY_NAME => ['string', 'sometimes'],
            RequestParams::REQUEST_SUCCESS_MANAGER_IDS => ['array', 'sometimes'],
            RequestParams::REQUEST_SUCCESS_MANAGER_IDS.'.*' => [new Exists('success_managers', 'id')],
            RequestParams::REQUEST_PER_PAGE => ['integer', 'min:1', 'max:100', 'sometimes'],
            RequestParams::REQUEST_PAGE => ['integer', 'min:1', 'sometimes'],
            RequestParams::REQUEST_ORDER_BY => ['array', 'sometimes'],
            RequestParams::REQUEST_ORDER_BY.'.*' => ['string', 'sometimes', new SortQueryParam],
            RequestParams::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT => ['array', 'sometimes'],
            RequestParams::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT_FIRST_OPERATOR => [
                new Enum(Operator::class), 'sometimes'
            ],
            RequestParams::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT_FIRST_VALUE => ['integer', 'min:0', 'sometimes'],
            RequestParams::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT_SECOND_OPERATOR => [
                new Enum(Operator::class), 'nullable', 'sometimes'
            ],
            RequestParams::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT_SECOND_VALUE => [
                'integer', 'min:0', 'nullable', 'sometimes'
            ],
            RequestParams::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT_ACTIVE => ['boolean', 'required', 'sometimes'],
            RequestParams::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT_LOGICAL => [
                new Enum(Logical::class), 'nullable', 'sometimes'
            ],
            RequestParams::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT_FIRST_DATE_TOGGLED => [
                'boolean', 'nullable', 'sometimes'
            ],
            RequestParams::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT_SECOND_DATE_TOGGLED => [
                'boolean', 'nullable', 'sometimes'
            ],
            RequestParams::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT_FIRST_FROM_DATE => [
                'integer', 'nullable', 'sometimes'
            ],
            RequestParams::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT_FIRST_TO_DATE => [
                'integer', 'nullable', 'sometimes'
            ],
            RequestParams::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT_SECOND_FROM_DATE => [
                'integer', 'nullable', 'sometimes'
            ],
            RequestParams::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT_SECOND_TO_DATE => [
                'integer', 'nullable', 'sometimes'
            ],
            RequestParams::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT_DATE_FORMAT => [
                'string', 'nullable', 'sometimes', new Enum(DateFormatOption::class)
            ],
            RequestParams::REQUEST_CAMPAIGN => ['array', 'sometimes'],
            RequestParams::REQUEST_CAMPAIGN_BUDGET => ['array', 'sometimes'],
            RequestParams::REQUEST_CAMPAIGN_BUDGET_NO_LIMIT => ['array', 'sometimes'],
            RequestParams::REQUEST_CAMPAIGN_BUDGET_VOLUME => ['array', 'sometimes'],
            RequestParams::REQUEST_CAMPAIGN_BUDGET_COST => ['array', 'sometimes'],
            RequestParams::REQUEST_CAMPAIGN_BUDGET_NO_LIMIT_SELECTED => ['boolean', 'sometimes'],
            RequestParams::REQUEST_CAMPAIGN_BUDGET_VOLUME_INPUT => ['integer', 'min:0', 'sometimes'],
            RequestParams::REQUEST_CAMPAIGN_BUDGET_VOLUME_OPERATOR => [new Enum(Operator::class), 'sometimes'],
            RequestParams::REQUEST_CAMPAIGN_BUDGET_COST_INPUT => ['integer', 'min:0', 'sometimes'],
            RequestParams::REQUEST_CAMPAIGN_BUDGET_COST_OPERATOR => [new Enum(Operator::class), 'sometimes'],
            RequestParams::REQUEST_NEVER_EXCEEDS_BUDGET => ['boolean', 'sometimes'],
            RequestParams::REQUEST_CADENCE => [new Enum(Cadence::class), 'sometimes'],
            RequestParams::REQUEST_CAMPAIGN_STATUS_IDS => [
                'array', 'sometimes'
            ],
            RequestParams::REQUEST_CAMPAIGN_STATUS_IDS.'.*' => [new Enum(CampaignStatus::class)],
            RequestParams::REQUEST_CAMPAIGN_SERVICE_AREAS => ['array', 'sometimes'],
            RequestParams::REQUEST_CAMPAIGN_SERVICE_AREAS.'.*' => [new Enum(StateAbbreviation::class), 'sometimes'],
            RequestParams::REQUEST_CAMPAIGN_SERVICE_AREAS_LOGICAL => [new Enum(Logical::class), 'sometimes'],
            RequestParams::REQUEST_LEAD_REJECTION => [
                'array', 'sometimes'
            ],
            RequestParams::REQUEST_LEAD_REJECTION_FIRST_INPUT => ['integer', 'min:0', 'max:30', 'sometimes'],
            RequestParams::REQUEST_LEAD_REJECTION_SECOND_INPUT => ['integer', 'min:0', 'max:30', 'sometimes'],
            RequestParams::REQUEST_LEAD_REJECTION_FIRST_OPERATOR => [
                new Enum(Operator::class), 'sometimes'
            ],
            RequestParams::REQUEST_LEAD_REJECTION_SECOND_OPERATOR => [
                new Enum(Operator::class), 'sometimes'
            ],
            RequestParams::REQUEST_LEAD_REJECTION_LOGICAL => [new Enum(Logical::class), 'sometimes'],
            RequestParams::REQUEST_APPOINTMENT_REJECTION => [
                'array', 'sometimes'
            ],
            RequestParams::REQUEST_APPOINTMENT_REJECTION_FIRST_INPUT => [
                'integer', 'min:0', 'max:30', 'sometimes'
            ],
            RequestParams::REQUEST_APPOINTMENT_REJECTION_SECOND_INPUT => [
                'integer', 'min:0', 'max:30', 'sometimes'
            ],
            RequestParams::REQUEST_APPOINTMENT_REJECTION_FIRST_OPERATOR => [
                new Enum(Operator::class), 'sometimes'
            ],
            RequestParams::REQUEST_APPOINTMENT_REJECTION_SECOND_OPERATOR => [
                new Enum(Operator::class), 'sometimes'
            ],
            RequestParams::REQUEST_APPOINTMENT_REJECTION_LOGICAL => [new Enum(Logical::class), 'sometimes'],
            RequestParams::REQUEST_EMPLOYEE_COUNT => ['array', 'sometimes'],
            RequestParams::REQUEST_EMPLOYEE_COUNT_FIRST_INPUT => ['integer', 'sometimes', 'min:0'],
            RequestParams::REQUEST_EMPLOYEE_COUNT_FIRST_OPERATOR => [new Enum(Operator::class), 'sometimes'],
            RequestParams::REQUEST_EMPLOYEE_COUNT_SECOND_INPUT => ['integer', 'sometimes', 'min:0'],
            RequestParams::REQUEST_EMPLOYEE_COUNT_SECOND_OPERATOR => [new Enum(Operator::class), 'sometimes'],
            RequestParams::REQUEST_EMPLOYEE_COUNT_LOGICAL => [new Enum(Logical::class), 'sometimes'],
            RequestParams::REQUEST_ESTIMATED_REVENUE => ['array', 'sometimes'],
            RequestParams::REQUEST_ESTIMATED_REVENUE_FIRST_INPUT => ['integer', 'sometimes', 'min:0'],
            RequestParams::REQUEST_ESTIMATED_REVENUE_FIRST_OPERATOR => [new Enum(Operator::class), 'sometimes'],
            RequestParams::REQUEST_ESTIMATED_REVENUE_SECOND_INPUT => ['integer', 'sometimes', 'min:0'],
            RequestParams::REQUEST_ESTIMATED_REVENUE_SECOND_OPERATOR => [new Enum(Operator::class), 'sometimes'],
            RequestParams::REQUEST_ESTIMATED_REVENUE_LOGICAL => [new Enum(Logical::class), 'sometimes'],
            RequestParams::REQUEST_GOOGLE_RATING => ['array', 'sometimes'],
            RequestParams::REQUEST_GOOGLE_RATING_FIRST_INPUT => ['integer', 'sometimes', 'min:1', 'max:5'],
            RequestParams::REQUEST_GOOGLE_RATING_FIRST_OPERATOR => [new Enum(Operator::class), 'sometimes'],
            RequestParams::REQUEST_GOOGLE_RATING_SECOND_INPUT => ['integer', 'sometimes', 'min:1', 'max:5'],
            RequestParams::REQUEST_GOOGLE_RATING_SECOND_OPERATOR => [new Enum(Operator::class), 'sometimes'],
            RequestParams::REQUEST_GOOGLE_REVIEW_COUNT => ['array', 'sometimes'],
            RequestParams::REQUEST_GOOGLE_REVIEW_COUNT_FIRST_INPUT => ['integer', 'sometimes', 'min:0'],
            RequestParams::REQUEST_GOOGLE_REVIEW_COUNT_FIRST_OPERATOR => [new Enum(Operator::class), 'sometimes'],
            RequestParams::REQUEST_GOOGLE_REVIEW_COUNT_SECOND_INPUT => ['integer', 'sometimes', 'min:0'],
            RequestParams::REQUEST_GOOGLE_REVIEW_COUNT_SECOND_OPERATOR => [new Enum(Operator::class), 'sometimes'],
            RequestParams::REQUEST_GOOGLE_REVIEW_COUNT_LOGICAL => [new Enum(Logical::class), 'sometimes'],
            RequestParams::REQUEST_CONSUMER_RATING => ['array', 'sometimes'],
            RequestParams::REQUEST_CONSUMER_RATING_FIRST_INPUT => ['integer', 'sometimes', 'min:1', 'max:5'],
            RequestParams::REQUEST_CONSUMER_RATING_FIRST_OPERATOR => [new Enum(Operator::class), 'sometimes'],
            RequestParams::REQUEST_CONSUMER_RATING_SECOND_INPUT => ['integer', 'sometimes', 'min:1', 'max:5'],
            RequestParams::REQUEST_CONSUMER_RATING_SECOND_OPERATOR => [new Enum(Operator::class), 'sometimes'],
            RequestParams::REQUEST_CONSUMER_REVIEW_COUNT => ['array', 'sometimes'],
            RequestParams::REQUEST_CONSUMER_REVIEW_COUNT_FIRST_INPUT => ['integer', 'sometimes', 'min:0'],
            RequestParams::REQUEST_CONSUMER_REVIEW_COUNT_FIRST_OPERATOR => [new Enum(Operator::class), 'sometimes'],
            RequestParams::REQUEST_CONSUMER_REVIEW_COUNT_SECOND_INPUT => ['integer', 'sometimes', 'min:0'],
            RequestParams::REQUEST_CONSUMER_REVIEW_COUNT_SECOND_OPERATOR => [new Enum(Operator::class), 'sometimes'],
            RequestParams::REQUEST_CONSUMER_REVIEW_COUNT_LOGICAL => [new Enum(Logical::class), 'sometimes'],
        ];
    }

    protected function prepareForValidation(): void
    {
        $params = [
        ];

        $neverExceedsBudget = $this->toBoolean($this->get(RequestParams::REQUEST_NEVER_EXCEEDS_BUDGET));

        if (gettype($neverExceedsBudget) === 'boolean') {
            $params[RequestParams::REQUEST_NEVER_EXCEEDS_BUDGET] = $this->toBoolean($neverExceedsBudget);
        }

        $campaignsBudgetNoLimitSelected = $this->toBoolean(data_get($this->toArray(),
            RequestParams::REQUEST_CAMPAIGN_BUDGET_NO_LIMIT_SELECTED));
        $campaignsBudgetVolumeInput = intval(data_get($this->toArray(),
            RequestParams::REQUEST_CAMPAIGN_BUDGET_VOLUME_INPUT));
        $campaignsBudgetVolumeOperator = data_get($this->toArray(),
            RequestParams::REQUEST_CAMPAIGN_BUDGET_VOLUME_OPERATOR);
        $campaignsBudgetCostInput = intval(data_get($this->toArray(),
            RequestParams::REQUEST_CAMPAIGN_BUDGET_COST_INPUT));
        $campaignsBudgetCostOperator = data_get($this->toArray(),
            RequestParams::REQUEST_CAMPAIGN_BUDGET_COST_OPERATOR);

        if (gettype($campaignsBudgetNoLimitSelected) === 'boolean') {
            $params['campaign']['budget']['no_limit']['selected'] = $campaignsBudgetNoLimitSelected;
        }

        if (gettype($campaignsBudgetVolumeInput) === 'integer' && gettype($campaignsBudgetVolumeOperator) === 'string') {
            $params['campaign']['budget']['volume']['input'] = $campaignsBudgetVolumeInput;
            $params['campaign']['budget']['volume']['operator'] = $campaignsBudgetVolumeOperator;
        }

        if (gettype($campaignsBudgetCostInput) === 'integer' && gettype($campaignsBudgetCostOperator) === 'string') {
            $params['campaign']['budget']['cost']['input'] = $campaignsBudgetCostInput;
            $params['campaign']['budget']['cost']['operator'] = $campaignsBudgetCostOperator;
        }

        $this->merge($params);
    }

    /**
     * Convert to boolean
     *
     * @param $booleable
     * @return boolean
     */
    private function toBoolean($booleable): ?bool
    {
        if ($booleable === null) {
            return null;
        }

        return filter_var($booleable, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
    }
}
