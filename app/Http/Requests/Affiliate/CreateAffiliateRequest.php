<?php

namespace App\Http\Requests\Affiliate;

use App\Enums\PermissionType;
use App\Models\Affiliates\Affiliate;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class CreateAffiliateRequest extends BaseAffiliateRequest
{
    const string REQUEST_NAME = 'name';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::AFFILIATES_CREATE->value);
    }

    public function rules(): array
    {
        return [
            self::REQUEST_NAME => ['required', 'string', 'unique:' . Affiliate::TABLE . ',' . Affiliate::FIELD_NAME],
            ...parent::rules()
        ];
    }
}
