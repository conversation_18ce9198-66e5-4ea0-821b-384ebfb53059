<?php

namespace App\Http\Requests\Affiliate;

use App\Enums\Affiliate\PayoutStrategyTypeEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class BaseAffiliateRequest extends FormRequest
{
    const string REQUEST_PAYOUT_STRATEGY = 'strategy';
    const string STRATEGY_TYPE = 'type';
    const string STRATEGY_VALUE = 'value';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    public function prepareForValidation(): void
    {
        $strategyType = PayoutStrategyTypeEnum::tryFrom($this->input('strategy.type'))->getClass();

        $value = $strategyType->fromPresentationValue($this->input('strategy.value'));

        $strategy = $this->input('strategy');

        $strategy[self::STRATEGY_VALUE] = $value;

        $this->merge([
            self::REQUEST_PAYOUT_STRATEGY  => $strategy,
        ]);
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        $strategyType = PayoutStrategyTypeEnum::tryFrom($this->input('strategy.type'));

        $boundaryValidation = $strategyType->getClass()->valueBoundaries();

        $max = $boundaryValidation->getMax();
        $min = $boundaryValidation->getMin();

        return [
            self::REQUEST_PAYOUT_STRATEGY => ['required', 'array'],
            self::REQUEST_PAYOUT_STRATEGY . '.' . self::STRATEGY_TYPE => ['required', Rule::enum(PayoutStrategyTypeEnum::class)],
            self::REQUEST_PAYOUT_STRATEGY . '.' . self::STRATEGY_VALUE => ['required', 'integer', 'between:' . $min . ',' . $max],
        ];
    }
}
