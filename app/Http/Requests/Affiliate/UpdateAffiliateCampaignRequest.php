<?php

namespace App\Http\Requests\Affiliate;

use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdateAffiliateCampaignRequest extends FormRequest
{
    const string REQUEST_NAME        = 'name';
    const string REQUEST_ACCOUNT_ID  = 'account_id';
    const string REQUEST_CAMPAIGN_ID = 'campaign_id';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::AFFILIATES_UPDATE->value);
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            self::REQUEST_NAME        => ['string'],
            self::REQUEST_ACCOUNT_ID  => ['nullable', 'integer'],
            self::REQUEST_CAMPAIGN_ID => ['nullable', 'integer'],
        ];
    }
}
