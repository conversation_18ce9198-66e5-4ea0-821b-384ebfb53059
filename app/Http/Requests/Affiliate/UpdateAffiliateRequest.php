<?php

namespace App\Http\Requests\Affiliate;

use App\Enums\PermissionType;
use App\Models\Affiliates\Affiliate;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UpdateAffiliateRequest extends BaseAffiliateRequest
{
    const string REQUEST_AFFILIATE_ID = 'id';
    const string REQUEST_NAME = 'name';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::AFFILIATES_UPDATE->value);
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            self::REQUEST_AFFILIATE_ID => ['required', Rule::exists(Affiliate::TABLE, Affiliate::FIELD_ID)],
            self::REQUEST_NAME => ['required', 'string'],
            ...parent::rules()
        ];
    }
}
