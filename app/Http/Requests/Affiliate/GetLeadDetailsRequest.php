<?php

namespace App\Http\Requests\Affiliate;

use App\Models\Affiliates\Affiliate;
use App\Models\Affiliates\Campaign;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class GetLeadDetailsRequest extends FormRequest
{
    const string PAGE         = 'page';
    const string PER_PAGE     = 'perPage';
    const string CAMPAIGN_ID  = 'campaignId';
    const string AFFILIATE_ID = 'affiliateId';
    const string REQUEST_FROM = 'from';
    const string REQUEST_TO = 'to';
    const string REQUEST_DATE_RANGE = 'date_range';
    const string CONSUMER_NAME = 'consumer_name';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return true;
    }

    public function validator($factory)
    {
        return $factory->make(
            $this->sanitize(), $this->container->call([$this, 'rules']), $this->messages()
        );
    }

    public function sanitize(): array
    {
        $this->merge([
            self::REQUEST_DATE_RANGE => json_decode($this->input(self::REQUEST_DATE_RANGE), true)
        ]);
        return $this->all();
    }

    public function rules(): array
    {
        $paginationRules = [
            self::PAGE     => 'nullable|numeric|min:1',
            self::PER_PAGE => 'nullable|numeric|min:1|max:100',
        ];

        return [
            ...$paginationRules,
            self::REQUEST_DATE_RANGE => ['sometimes', 'nullable', 'array'],
            self::REQUEST_DATE_RANGE . '.' . self::REQUEST_FROM => ['sometimes', 'date'],
            self::REQUEST_DATE_RANGE . '.' . self::REQUEST_TO => ['sometimes', 'date'],
            self::CAMPAIGN_ID  => ['nullable', 'integer', 'exists:' . Campaign::TABLE . ',' . Campaign::FIELD_ID],
            self::AFFILIATE_ID => ['nullable', 'integer', 'exists:' . Affiliate::TABLE . ',' . Affiliate::FIELD_ID],
            self::CONSUMER_NAME => ['nullable', 'string', 'max:255'],
        ];
    }
}
