<?php

namespace App\Http\Requests;

use App\Models\Legacy\EloquentCompanyContact;
use App\Rules\Phone;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreCompanyContactRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        //todo: permission
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            EloquentCompanyContact::FIELD_FIRST_NAME => 'required|string|max:64',
            EloquentCompanyContact::FIELD_LAST_NAME => 'required|string|max:64',
            EloquentCompanyContact::FIELD_TITLE => 'string|max:64',
            EloquentCompanyContact::FIELD_EMAIL => ['nullable', 'email', Rule::requiredIf(fn() => !request()->get(EloquentCompanyContact::FIELD_MOBILE) && !request()->get(EloquentCompanyContact::FIELD_PHONE))],
            EloquentCompanyContact::FIELD_PHONE => ['nullable', new Phone, Rule::requiredIf(fn() => !request()->get(EloquentCompanyContact::FIELD_MOBILE) && !request()->get(EloquentCompanyContact::FIELD_EMAIL))],
            EloquentCompanyContact::FIELD_MOBILE => ['nullable', new Phone, Rule::requiredIf(fn() => !request()->get(EloquentCompanyContact::FIELD_PHONE) && !request()->get(EloquentCompanyContact::FIELD_EMAIL))]
        ];
    }
}
