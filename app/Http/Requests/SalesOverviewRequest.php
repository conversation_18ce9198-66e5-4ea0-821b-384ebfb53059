<?php

namespace App\Http\Requests;

use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class SalesOverviewRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasAnyPermission(PermissionType::SALES_OVERVIEW_VIEW->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'date_range' => 'array',
            'user_id'    => 'numeric',
            'sort_by'    => 'array',
            'direction'  => 'in:inbound,outbound'
        ];
    }
}
