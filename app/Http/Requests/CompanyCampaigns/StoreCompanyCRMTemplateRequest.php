<?php

namespace App\Http\Requests\CompanyCampaigns;

use App\Models\Campaigns\Modules\Delivery\CompanyCRMTemplate;
use Illuminate\Foundation\Http\FormRequest;

class StoreCompanyCRMTemplateRequest extends FormRequest
{
    const string PAYLOAD_CAMPAIGNS        = 'campaigns';
    const string PAYLOAD_SYNC_CAMPAIGNS   = 'sync_campaigns';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            CompanyCRMTemplate::FIELD_ID           => ['numeric', 'nullable'],
            CompanyCRMTemplate::FIELD_CRM_TYPE     => ['numeric'],
            CompanyCRMTemplate::FIELD_DISPLAY_NAME => ['string'],
            CompanyCRMTemplate::FIELD_PAYLOAD      => ['array'],
            self::PAYLOAD_SYNC_CAMPAIGNS           => ['boolean', 'nullable'],
            self::PAYLOAD_CAMPAIGNS                => ['nullable', 'array'],
        ];
    }
}
