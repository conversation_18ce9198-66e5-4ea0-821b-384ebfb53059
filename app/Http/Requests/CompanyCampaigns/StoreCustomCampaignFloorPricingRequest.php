<?php

namespace App\Http\Requests\CompanyCampaigns;

use App\Enums\PermissionType;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\CustomCampaignCountyFloorPrice;
use App\Models\Campaigns\CustomCampaignStateFloorPrice;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class StoreCustomCampaignFloorPricingRequest extends FormRequest
{
    const string REQUEST_PRICES               = 'prices';
    const string REQUEST_SALE_TYPE            = 'sale_type';
    const string REQUEST_PROPERTY_TYPE        = 'property_type';
    const string REQUEST_QUALITY_TIER         = 'quality_tier';
    const string REQUEST_BULK_APPLY_CHANGES   = 'bulk_apply_to_industry';
    const string REQUEST_LOWERED_PRICE_POLICY = 'lowered_floor_price_policy';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();
        return $user->hasPermissionTo(PermissionType::CAMPAIGNS_UPDATE);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            CompanyCampaign::FIELD_USES_CUSTOM_FLOOR_PRICES                                         => 'boolean',
            self::REQUEST_BULK_APPLY_CHANGES                                                        => 'boolean|nullable',
            self::REQUEST_LOWERED_PRICE_POLICY                                                      => 'int|nullable',
            self::REQUEST_PRICES                                                                    => 'array',
            self::REQUEST_PRICES . '.*.' . self::REQUEST_PROPERTY_TYPE                              => 'string',
            self::REQUEST_PRICES . '.*.' . self::REQUEST_QUALITY_TIER                               => 'string',
            self::REQUEST_PRICES . '.*.' . self::REQUEST_SALE_TYPE                                  => 'string',
            self::REQUEST_PRICES . '.*.' . CustomCampaignStateFloorPrice::FIELD_PRICE               => 'numeric|required',
            self::REQUEST_PRICES . '.*.' . CustomCampaignStateFloorPrice::FIELD_STATE_LOCATION_ID   => 'numeric|required',
            self::REQUEST_PRICES . '.*.' . CustomCampaignCountyFloorPrice::FIELD_COUNTY_LOCATION_ID => 'numeric|nullable'
        ];
    }
}
