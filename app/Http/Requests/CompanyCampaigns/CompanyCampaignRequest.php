<?php

namespace App\Http\Requests\CompanyCampaigns;

use App\Models\Billing\BillingProfile;
use App\Models\Campaigns\CompanyCampaign;
use Illuminate\Foundation\Http\FormRequest;

class CompanyCampaignRequest extends FormRequest
{
    const string COMPANY_ID         = 'company_id';
    const string BILLING_PROFILE_ID = 'profile_id';

    /**
     * Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::COMPANY_ID         => 'required|numeric',
            self::BILLING_PROFILE_ID => 'numeric|exists:' . BillingProfile::TABLE . ',' . BillingProfile::FIELD_ID
        ];
    }
}
