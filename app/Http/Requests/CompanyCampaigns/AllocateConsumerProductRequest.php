<?php

namespace App\Http\Requests\CompanyCampaigns;

use App\Enums\Odin\SaleTypes;
use App\Enums\PermissionType;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class AllocateConsumerProductRequest extends FormRequest
{
    const string CAMPAIGN_IDS = 'campaign_ids';
    const string SALE_TYPE = 'sale_type';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::LEAD_ALLOCATION_AND_ADJUSTMENT->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::CAMPAIGN_IDS        => 'required|array',
            self::CAMPAIGN_IDS . '.*' => 'exists:' . CompanyCampaign::class . ',' . CompanyCampaign::FIELD_ID,
            self::SALE_TYPE           => [
                'required',
                Rule::in([
                    SaleTypes::EXCLUSIVE->value,
                    SaleTypes::DUO->value,
                    SaleTypes::TRIO->value,
                    SaleTypes::QUAD->value,
                ])
            ]
        ];
    }
}
