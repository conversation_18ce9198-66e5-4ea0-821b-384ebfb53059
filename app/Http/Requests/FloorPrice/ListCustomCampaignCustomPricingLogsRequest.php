<?php

namespace App\Http\Requests\FloorPrice;

use App\Enums\PermissionType;
use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class ListCustomCampaignCustomPricingLogsRequest extends FormRequest
{
    const string REQUEST_CAMPAIGN               = 'campaign';
    const string REQUEST_COMPANY_COMPANY_ID     = 'company_id';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /**@var User $user */
        $user = Auth::user();
        return $user->hasPermissionTo(PermissionType::COMPANY_CAMPAIGN_CUSTOM_PRICING_LOGS_VIEW->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::REQUEST_CAMPAIGN => ['string', 'nullable', 'sometimes'],
            self::REQUEST_COMPANY_COMPANY_ID => ['integer', 'nullable', 'exists:' . Company::TABLE .','. Company::FIELD_ID],
        ];
    }

}
