<?php

namespace App\Http\Requests\FloorPrice;

use App\Enums\LoweredFloorPricePolicy;
use App\Enums\Odin\Product;
use App\Enums\Odin\PropertyType;
use App\Enums\Odin\QualityTier;
use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class ApplySuggestedPriceRequest extends FormRequest
{
    const string PRICE_POLICY = 'price_policy';
    const string PRICES = 'prices';
    const string PRODUCT = 'product';
    const string QUALITY_TIER = 'quality_tier';
    const string PROPERTY_TYPE = 'property_type';
    const string LOCATION_ID = 'location_id';
    const string EXCLUSIVE  = 'exclusive';
    const string DUO = 'duo';
    const string TRIO = 'trio';
    const string QUAD = 'quad';

    /**O
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::INDUSTRY_CONFIGURATION->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::PRICE_POLICY                       => ['required', Rule::enum(LoweredFloorPricePolicy::class)],
            self::PRICES                             => ['required', 'array'],
            self::PRICES . '.*.' . self::LOCATION_ID => ['required'],
            self::PRICES . '.*.' . self::EXCLUSIVE   => ['numeric'],
            self::PRICES . '.*.' . self::DUO         => ['numeric'],
            self::PRICES . '.*.' . self::TRIO        => ['numeric'],
            self::PRICES . '.*.' . self::QUAD        => ['numeric'],
            self::PRODUCT                            => ['required', Rule::enum(Product::class)],
            self::PROPERTY_TYPE                      => ['required', Rule::enum(PropertyType::class)],
            self::QUALITY_TIER                       => ['required', Rule::enum(QualityTier::class)]
        ];
    }
}
