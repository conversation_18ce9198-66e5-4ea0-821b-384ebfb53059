<?php

namespace App\Http\Requests\FloorPrice;

use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdatePricingMarginRequest extends FormRequest
{
    const string EXCLUSIVE = 'exclusive';
    const string DUO = 'duo';
    const string TRIO = 'trio';
    const string QUAD = 'quad';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::INDUSTRY_CONFIGURATION->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::EXCLUSIVE => ['required', 'numeric', 'min:1'],
            self::DUO => ['required', 'numeric', 'min:1'],
            self::TRIO => ['required', 'numeric', 'min:1'],
            self::QUAD => ['required', 'numeric', 'min:1'],
        ];
    }
}
