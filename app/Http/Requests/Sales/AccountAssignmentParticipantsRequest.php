<?php

namespace App\Http\Requests\Sales;

use App\Enums\PermissionType;
use App\Http\Controllers\API\Sales\SalesManagementAPIController;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class AccountAssignmentParticipantsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        /** @var User $user */
        $user = Auth::user();
        return $user->hasPermissionTo(PermissionType::SALES_MANAGEMENT);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return $this->method() === self::METHOD_GET
            ? []
            : [
                SalesManagementAPIController::REQUEST_ADD_USER_IDS    => 'array',
                SalesManagementAPIController::REQUEST_REMOVE_USER_IDS => 'array',
            ];
    }
}