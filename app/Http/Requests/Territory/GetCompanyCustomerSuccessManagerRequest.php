<?php

namespace App\Http\Requests\Territory;

use App\Models\Odin\Company;
use Illuminate\Foundation\Http\FormRequest;

class GetCompanyCustomerSuccessManagerRequest extends FormRequest
{
    const string REQUEST_COMPANY_ID = 'company_id';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::REQUEST_COMPANY_ID => 'required|integer|exists:' . Company::TABLE . ',' . Company::FIELD_ID,
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        $companyId = $this->route(self::REQUEST_COMPANY_ID);
        $this->merge([
            self::REQUEST_COMPANY_ID => $companyId,
        ]);
    }

    /**
     * Get the data to be validated from the request.
     *
     * @return array
     */
    public function validationData(): array
    {
        return $this->all();
    }
}
