<?php

namespace App\Http\Requests;

use App\Enums\ActivityType;
use App\Http\Controllers\API\CompanyActivityController;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class GetCompanyActivitiesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(CompanyActivityController::MODULE_PERMISSION);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            CompanyActivityController::REQUEST_COMPANY_ID                                     => [
                'required',
                // TODO: Once MI Released:
                // 'exists:' . Company::TABLE .','. Company::FIELD_ID
            ],
            CompanyActivityController::REQUEST_USER_ID                                        => [
                'nullable',
                'exists:' . User::TABLE . ',' . User::FIELD_ID
            ],
            CompanyActivityController::REQUEST_PERIOD_START_DATE                              => [
                'nullable',
                'date_format:Y-m-d'
            ],
            CompanyActivityController::REQUEST_PERIOD_END_DATE                                => [
                'nullable',
                'date_format:Y-m-d'
            ],
            CompanyActivityController::REQUEST_TYPE                                           => [
                'nullable',
                'string',
                Rule::in(ActivityType::allTypes())
            ],
            CompanyActivityController::REQUEST_SEARCH_QUERY                                   => [
                'nullable',
                'string'
            ],
            CompanyActivityController::REQUEST_SORT_BY                                        => [
                'nullable',
                'string'
            ],
            CompanyActivityController::REQUEST_CADENCE                                        => [
                'boolean'
            ]
        ];
    }

    /**
     * @return void
     */
    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                CompanyActivityController::REQUEST_COMPANY_ID => $this->route()->parameter(CompanyActivityController::REQUEST_COMPANY_ID),
                CompanyActivityController::REQUEST_CADENCE    => $this->toBoolean($this->{CompanyActivityController::REQUEST_CADENCE}),
            ]
        );
    }

    /**
     * @param mixed $booleable
     * @return bool
     */
    private function toBoolean(mixed $booleable): bool
    {
        return filter_var($booleable, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
    }
}
