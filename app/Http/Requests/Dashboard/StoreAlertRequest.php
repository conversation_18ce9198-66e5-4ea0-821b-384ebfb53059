<?php

namespace App\Http\Requests\Dashboard;

use App\Enums\Alert\AlertType;
use App\Models\Alert;
use App\Models\AlertRecipient;
use App\Models\Odin\CompanyUser;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreAlertRequest extends FormRequest
{
    const string RECIPIENTS = 'recipients';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            Alert::FIELD_ACTIVE => 'required|boolean',
            Alert::FIELD_TYPE => ['required', Rule::enum(AlertType::class)],
            Alert::FIELD_PAYLOAD => 'required:array',
            self::RECIPIENTS => 'required:array',
            self::RECIPIENTS . '.*.id' => 'exists:' . CompanyUser::TABLE,
            self::RECIPIENTS . '.*.channel' => 'in:' . AlertRecipient::CHANNEL_EMAIL . ',' . AlertRecipient::CHANNEL_SMS,
        ];
    }
}
