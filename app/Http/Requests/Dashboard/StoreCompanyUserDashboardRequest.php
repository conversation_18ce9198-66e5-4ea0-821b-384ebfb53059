<?php

namespace App\Http\Requests\Dashboard;

use App\Http\Requests\StoreCompanyUserRequest;
use App\Models\Odin\CompanyUser;

class StoreCompanyUserDashboardRequest extends StoreCompanyUserRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            ...parent::rules(),
            CompanyUser::FIELD_CAN_LOG_IN => ['boolean']
        ];

    }

}
