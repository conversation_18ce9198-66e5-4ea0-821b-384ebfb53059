<?php

namespace App\Http\Requests\Dashboard;

use Illuminate\Foundation\Http\FormRequest;

class LoginRequest extends FormRequest
{
    const FIELD_EMAIL    = 'email';
    const FIELD_PASSWORD = 'password';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            self::FIELD_EMAIL => 'required|email',
            self::FIELD_PASSWORD => 'required|string'
        ];
    }
}
