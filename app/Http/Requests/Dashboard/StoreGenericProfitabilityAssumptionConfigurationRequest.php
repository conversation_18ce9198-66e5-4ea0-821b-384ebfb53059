<?php

namespace App\Http\Requests\Dashboard;

use App\Models\Odin\GenericProfitabilityAssumptionConfiguration;
use Illuminate\Foundation\Http\FormRequest;

class StoreGenericProfitabilityAssumptionConfigurationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     * @return bool
     */
    public function authorize(): bool
    {
        //TODO: if permissions are attached to CompanyUsers
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            GenericProfitabilityAssumptionConfiguration::FIELD_PERCENTAGE_LEADS_SUCCESSFUL => 'numeric|max:100|min:0',
            GenericProfitabilityAssumptionConfiguration::FIELD_AVERAGE_LEAD_REVENUE        => 'numeric',
            GenericProfitabilityAssumptionConfiguration::FIELD_AVERAGE_JOB_COST            => 'numeric',
            GenericProfitabilityAssumptionConfiguration::FIELD_LABOUR_MATERIALS_COST       => 'numeric',
        ];
    }
}
