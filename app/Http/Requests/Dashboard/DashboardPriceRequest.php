<?php

namespace App\Http\Requests\Dashboard;

use App\Enums\Odin\StateAbbreviation;
use App\Models\Campaigns\CompanyCampaign;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;

class DashboardPriceRequest extends FormRequest
{
    const STATE_ABBR         = 'state_abbr';
    const COUNTY_KEY         = 'county_key';
    const PROPERTY_TYPE_ID   = 'property_type_id';
    const SALE_TYPE_ID       = 'sale_type_id';
    const QUALITY_TIER_ID    = 'quality_tier_id';
    const PRICE              = 'price';
    const PROPERTY_TYPE_NAME = 'property_type_name';
    const CAMPAIGN_UUID      = 'campaign_uuid';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::STATE_ABBR            => [new Enum(StateAbbreviation::class)],
            self::COUNTY_KEY            => 'string|nullable',
            self::PROPERTY_TYPE_ID      => 'integer',
            self::SALE_TYPE_ID          => [Rule::requiredIf(!$this->isMethod('get')), 'integer'],
            self::QUALITY_TIER_ID       => [Rule::requiredIf(!$this->isMethod('get')), 'integer'],
            self::PROPERTY_TYPE_NAME    => 'string|nullable',
            self::PRICE                 => [Rule::requiredIf(!$this->isMethod('get')), 'numeric'],
            self::CAMPAIGN_UUID         => 'string',
            CompanyCampaign::FIELD_TYPE => 'numeric|nullable',
        ];
    }
}
