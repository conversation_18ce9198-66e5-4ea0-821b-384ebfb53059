<?php

namespace App\Http\Requests\Dashboard;

use Illuminate\Foundation\Http\FormRequest;

class StoreNotificationSettingsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * TODO: rewrite when alerts moved over from legacy
     * Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            '*.id'              => 'numeric',
            '*.legacy_id'       => 'numeric',
            '*.alerts'          => 'array',
            '*.alerts.reviews'  => 'array',
            '*.alerts.invoices' => 'array',
            '*.alerts.outbid'   => 'array',
        ];
    }

}
