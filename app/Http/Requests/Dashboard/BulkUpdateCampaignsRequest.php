<?php

namespace App\Http\Requests\Dashboard;

use App\Models\Legacy\LeadCampaign;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class BulkUpdateCampaignsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            "campaigns"                  => 'array|required',
            "campaigns.*.".LeadCampaign::ID        => 'uuid|required',
            "campaigns.*.".LeadCampaign::STATUS    => 'boolean|nullable',
            "options"                    => 'array',
            "options.reactivate_at"      => 'numeric|nullable',
            "options.reason"             => 'string',
            "options.other"              => 'string|nullable',
        ];
    }
}
