<?php

namespace App\Http\Requests\Dashboard;

use App\Http\Controllers\DashboardAPI\CompanyController;
use Illuminate\Foundation\Http\FormRequest;

class AcceptCompanyContractRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            CompanyController::REQUEST_FIELD_CONTRACT_REFERENCE => 'required|uuid',
        ];
    }

}