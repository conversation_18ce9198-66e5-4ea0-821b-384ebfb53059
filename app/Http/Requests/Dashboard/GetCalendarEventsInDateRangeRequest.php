<?php

namespace App\Http\Requests\Dashboard;

use App\Http\Controllers\API\ClientDashboard\AppointmentCalendarIntegrationController;
use App\Models\Legacy\EloquentUser;
use Illuminate\Foundation\Http\FormRequest;

class GetCalendarEventsInDateRangeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $legacyDashboard = preg_match("/\/client-dashboard-api\//", $this->url());

        return $legacyDashboard
            ? app(EloquentUser::class)->active()
            : true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            AppointmentCalendarIntegrationController::REQUEST_START_DATE => ['required', 'date_format:Y-m-d'],
            AppointmentCalendarIntegrationController::REQUEST_END_DATE => ['required', 'date_format:Y-m-d']
        ];
    }
}
