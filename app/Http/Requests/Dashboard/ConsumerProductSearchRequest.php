<?php

namespace App\Http\Requests\Dashboard;

use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ConsumerProductSearchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * @return void
     */
    public function prepareForValidation(): void
    {
        $this->merge([
            'date_start'    => $this->date_start ?  Carbon::createFromTimestampMs($this->date_start) : null,
            'date_end'    => $this->date_end ?  Carbon::createFromTimestampMs($this->date_end)->addDay()->subSecond() : null,
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $validStatuses = [ 'purchased', 'non_chargeable', 'rejected', 'appointment_purchased' ];

        return [
            'date_start'    => 'date|nullable',
            'date_end'      => 'date|nullable',
            'lead_id'       => 'numeric|nullable',
            'appointment_id' => 'numeric|nullable',
            'missed_only'   => 'numeric|nullable',
            'contact_id'    => 'numeric|nullable',
            'contact_name'  => 'string|nullable',
            'status'        => [Rule::in($validStatuses), 'nullable'],
            'address'       => 'string|nullable',
            'invoice_id'    => 'numeric|nullable',
            'campaign_id'   => 'uuid|nullable',
            'lead_category' => 'numeric|nullable',
            'over_budget'   => 'numeric|nullable',
            'download'      => 'in:true,false'
        ];
    }
}
