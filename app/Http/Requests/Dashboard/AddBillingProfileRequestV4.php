<?php

namespace App\Http\Requests\Dashboard;

use Illuminate\Foundation\Http\FormRequest;

class AddBillingProfileRequestV4 extends FormRequest
{
    const string PAYMENT_TYPE = 'payment_type';
    const string STRIPE_TOKEN = 'stripe_token';

    const string FIELD_EXPIRY_MONTH = 'expiry_month';
    const string FIELD_EXPIRY_YEAR  = 'expiry_year';
    const string FIELD_NUMBER       = 'number';

    /**
     * Determine if the user is authorized to make this request.
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::PAYMENT_TYPE       => 'string',
            self::STRIPE_TOKEN       => 'string',
            self::FIELD_EXPIRY_MONTH => 'nullable',
            self::FIELD_EXPIRY_YEAR  => 'nullable',
            self::FIELD_NUMBER       => 'nullable',
        ];
    }
}





