<?php

namespace App\Http\Requests\Dashboard;

use App\Enums\Billing\InvoiceStates;
use App\Models\Billing\Invoice;
use App\Models\Odin\Company;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Carbon;

class SearchInvoicesRequestV4 extends FormRequest
{
    const string STATUS     = 'status';
    const string START_DATE = 'startDate';
    const string END_DATE   = 'endDate';
    const string PAGE       = 'page';
    const string PER_PAGE   = 'perPage';
    const string INVOICE_ID = 'invoiceId';
    const string COMPANY_ID = 'companyId';
    const string EXCLUDE    = 'exclude_status';

    /**
     * Determine if the user is authorized to make this request.
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * @return void
     */
    public function prepareForValidation(): void
    {
        $companyId = $this->route('companyId');

        $this->merge([
            self::START_DATE => $this->date_start ? Carbon::createFromTimestamp($this->date_start / 1000)->format('Y-m-d\TH:i:s.vP') : null,
            self::END_DATE   => $this->date_end ? Carbon::createFromTimestamp($this->date_end / 1000)->format('Y-m-d\TH:i:s.vP') : null,
            self::PAGE       => $this->page ?? 1,
            self::PER_PAGE   => 10,
            self::INVOICE_ID => $this->invoice_id,
            self::COMPANY_ID => $companyId,
            self::EXCLUDE    => [InvoiceStates::VOIDED->value, InvoiceStates::COLLECTION->value],
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::STATUS     => 'string|nullable',
            self::START_DATE => 'string|nullable',
            self::END_DATE   => 'string|nullable',
            self::PAGE       => 'integer',
            self::PER_PAGE   => 'integer',
            self::INVOICE_ID => 'numeric|nullable',
            self::COMPANY_ID => 'numeric|nullable',
            self::EXCLUDE    => 'array|nullable'
        ];
    }
}
