<?php

namespace App\Http\Requests\Dashboard;

use Illuminate\Foundation\Http\FormRequest;

class GetCountyZipCodesByZipCodeRequest extends FormRequest
{
    const FIELD_ZIP_CODES    = 'zip_codes';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::FIELD_ZIP_CODES => 'required',
        ];
    }
}
