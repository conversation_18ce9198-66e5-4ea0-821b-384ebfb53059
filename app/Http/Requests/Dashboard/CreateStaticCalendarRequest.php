<?php

namespace App\Http\Requests\Dashboard;

use App\Enums\Timezone;
use App\Http\Controllers\API\ClientDashboard\AppointmentCalendarIntegrationController;
use App\Models\Legacy\EloquentUser;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class CreateStaticCalendarRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $legacyDashboard = preg_match("/\/client-dashboard-api\//", $this->url());

        return $legacyDashboard
            ? app(EloquentUser::class)->active()
            : true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            AppointmentCalendarIntegrationController::REQUEST_NAME => [
                'required',
                'string'
            ],
            AppointmentCalendarIntegrationController::REQUEST_TIMEZONE => [
                'required',
                new Enum(Timezone::class)
            ],
            AppointmentCalendarIntegrationController::REQUEST_ONLINE_DURATION => ['required', 'integer', "min:1"],
            AppointmentCalendarIntegrationController::REQUEST_IN_HOME_DURATION => ['required', 'integer', "min:1"],
            AppointmentCalendarIntegrationController::REQUEST_ONLINE_BUFFER_AFTER => ['present', 'nullable', 'integer', 'min:0'],
            AppointmentCalendarIntegrationController::REQUEST_ONLINE_BUFFER_BEFORE => ['present', 'nullable', 'integer', 'min:0'],
            AppointmentCalendarIntegrationController::REQUEST_IN_HOME_BUFFER_AFTER => ['present', 'nullable', 'integer', 'min:0'],
            AppointmentCalendarIntegrationController::REQUEST_IN_HOME_BUFFER_BEFORE => ['present', 'nullable', 'integer', 'min:0'],
            AppointmentCalendarIntegrationController::REQUEST_IN_HOME_SAME_DAY_APPOINTMENTS => ['required', 'boolean'],
            AppointmentCalendarIntegrationController::REQUEST_ONLINE_SAME_DAY_APPOINTMENTS => ['required', 'boolean'],
            AppointmentCalendarIntegrationController::REQUEST_OVERLAPPING_EVENTS_ALLOWED => ['present', 'nullable', 'integer', 'min:0']
        ];
    }
}
