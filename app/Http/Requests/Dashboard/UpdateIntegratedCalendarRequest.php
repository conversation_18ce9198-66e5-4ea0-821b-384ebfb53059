<?php

namespace App\Http\Requests\Dashboard;

use App\Enums\CalendarPlatform;
use App\Enums\DayOfWeek;
use App\Enums\Timezone;
use App\Http\Controllers\API\ClientDashboard\AppointmentCalendarIntegrationController;
use App\Models\Legacy\EloquentUser;
use Carbon\Carbon;
use Exception;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateIntegratedCalendarRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        $legacyDashboard = preg_match("/\/client-dashboard-api\//", $this->url());

        return $legacyDashboard
            ? app(EloquentUser::class)->active()
            : true;    }

    /**
     * @return array
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function rules(): array
    {
        $items = request()->get(AppointmentCalendarIntegrationController::REQUEST_ITEMS);
        foreach($items as $day => $timeBlocks) {
            foreach($timeBlocks as $timeBlock) {
                if(Carbon::createFromTimeString($timeBlock["start"])->greaterThanOrEqualTo(Carbon::createFromTimeString($timeBlock["end"]))) {
                    throw new Exception("Start time isn't after end time for schedule item");
                }
            }
        }

        $overrideItems = request()->get(AppointmentCalendarIntegrationController::REQUEST_OVERRIDE_ITEMS);
        foreach($overrideItems as $overrideItem) {
            if(Carbon::createFromTimeString($overrideItem["start"])->greaterThanOrEqualTo(Carbon::createFromTimeString($overrideItem["end"]))) {
                throw new Exception("Start time isn't after end time for override item");
            }
        }

        $itemsRules = [];
        foreach(DayOfWeek::plurals() as $dayOfWeek) {
            $itemsRules[AppointmentCalendarIntegrationController::REQUEST_ITEMS.".{$dayOfWeek}.*"] = [
                'array:start,end'
            ];

            $itemsRules[AppointmentCalendarIntegrationController::REQUEST_ITEMS.".{$dayOfWeek}.*.start"] = [
                'date_format:G:i:s'
            ];

            $itemsRules[AppointmentCalendarIntegrationController::REQUEST_ITEMS.".{$dayOfWeek}.*.end"] = [
                'date_format:G:i:s'
            ];
        }

        return array_merge(
            $itemsRules,
            [
                AppointmentCalendarIntegrationController::REQUEST_NAME => [
                    'required',
                    'string'
                ],
                AppointmentCalendarIntegrationController::REQUEST_TIMEZONE => [
                    'required',
                    new Enum(Timezone::class)
                ],
                AppointmentCalendarIntegrationController::REQUEST_PLATFORM => [
                    'required',
                    new Enum(CalendarPlatform::class)
                ],
                AppointmentCalendarIntegrationController::REQUEST_ITEMS => [
                    'required',
                    'array:'.implode(',', array_values(DayOfWeek::plurals())),
                ],
                AppointmentCalendarIntegrationController::REQUEST_OVERRIDE_ITEMS => [
                    'present',
                    'array'
                ],
                AppointmentCalendarIntegrationController::REQUEST_OVERRIDE_ITEMS.'.*' => [
                    'array:start,end,date'
                ],
                AppointmentCalendarIntegrationController::REQUEST_OVERRIDE_ITEMS.'.*.start' => [
                    'date_format:G:i:s'
                ],
                AppointmentCalendarIntegrationController::REQUEST_OVERRIDE_ITEMS.'.*.end' => [
                    'date_format:G:i:s'
                ],
                AppointmentCalendarIntegrationController::REQUEST_OVERRIDE_ITEMS.'.*.date' => [
                    'date_format:Y-m-d'
                ],
                AppointmentCalendarIntegrationController::REQUEST_ONLINE_DURATION => ['required', 'integer', "min:1"],
                AppointmentCalendarIntegrationController::REQUEST_IN_HOME_DURATION => ['required', 'integer', "min:1"],
                AppointmentCalendarIntegrationController::REQUEST_ONLINE_BUFFER_AFTER => ['present', 'nullable', 'integer', 'min:0'],
                AppointmentCalendarIntegrationController::REQUEST_ONLINE_BUFFER_BEFORE => ['present', 'nullable', 'integer', 'min:0'],
                AppointmentCalendarIntegrationController::REQUEST_IN_HOME_BUFFER_AFTER => ['present', 'nullable', 'integer', 'min:0'],
                AppointmentCalendarIntegrationController::REQUEST_IN_HOME_BUFFER_BEFORE => ['present', 'nullable', 'integer', 'min:0'],
                AppointmentCalendarIntegrationController::REQUEST_IN_HOME_SAME_DAY_APPOINTMENTS => ['required', 'boolean'],
                AppointmentCalendarIntegrationController::REQUEST_ONLINE_SAME_DAY_APPOINTMENTS => ['required', 'boolean'],
                AppointmentCalendarIntegrationController::REQUEST_OVERLAPPING_EVENTS_ALLOWED => ['present', 'nullable', 'integer', 'min:0']
            ]
        );
    }
}
