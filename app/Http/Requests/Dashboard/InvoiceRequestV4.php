<?php

namespace App\Http\Requests\Dashboard;

use App\Models\Billing\Invoice;
use App\Models\Odin\Company;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Carbon;

class InvoiceRequestV4 extends FormRequest
{
    const string INVOICE_ID = 'invoiceId';
    /**
     * Determine if the user is authorized to make this request.
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * @return void
     */
    public function prepareForValidation(): void
    {
        $this->merge([
            self::INVOICE_ID => $this->invoice_id,
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::INVOICE_ID => 'numeric|nullable',
        ];
    }
}
