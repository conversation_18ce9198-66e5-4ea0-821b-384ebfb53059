<?php

namespace App\Http\Requests;

use App\Enums\PermissionType;
use App\Models\Template;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreTemplateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::SMS_TEMPLATE_EDIT->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            Template::FIELD_NAME => [
                'required',
                'string',
                'max:255',
                Rule::unique(Template::TABLE)->ignore($this->route('template'))
            ],
            Template::FIELD_DESCRIPTION => 'nullable|string|max:255',
            Template::FIELD_SUBJECT => 'nullable|string|max:255',
            Template::FIELD_CONTENT => 'required|string'
        ];
    }
}
