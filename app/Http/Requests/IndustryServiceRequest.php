<?php

namespace App\Http\Requests;

use App\Http\Controllers\API\IndustryManagement\IndustryManagementAPIController;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class IndustryServiceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo('industry-management');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return
            [
                IndustryManagementAPIController::REQUEST_INDUSTRY =>
                    [
                        "required",
                        "in:" . implode(',', Industry::query()->distinct()->pluck(Industry::FIELD_ID)->toArray())
                    ],
                IndustryManagementAPIController::REQUEST_INDUSTRY_SERVICE =>
                    [
                        "nullable",
                        "in:" . implode(',', IndustryService::query()->distinct()->pluck(IndustryService::FIELD_ID)->toArray())
                    ],
                IndustryService::FIELD_NAME =>
                    [
                        "required",
                        "string",
                        "max:255"
                    ],
                IndustryService::FIELD_SLUG =>
                    [
                        "required",
                        "string",
                        "max:255"
                    ],
                IndustryService::FIELD_SHOW_ON_WEBSITE =>
                    [
                        "required",
                        "bool"
                    ]
            ];
    }

    /**
     * @return void
     */
    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                IndustryManagementAPIController::REQUEST_INDUSTRY         => $this->route()->parameter(IndustryManagementAPIController::REQUEST_INDUSTRY),
                IndustryManagementAPIController::REQUEST_INDUSTRY_SERVICE => $this->route()->parameter(IndustryManagementAPIController::REQUEST_INDUSTRY_SERVICE)
            ]
        );
    }
}
