<?php

namespace App\Http\Requests;

use App\Http\Controllers\TeamManagementController;
use App\Models\Teams\Team;
use App\Models\Teams\TeamType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreTeamRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();
        return $user->hasPermissionTo(TeamManagementController::MODULE_PERMISSION);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {

        return [
            Team::FIELD_ID              => [ Rule::requiredIf(fn() => !!request()->get(TeamManagementController::REQUEST_UPDATE) === true) ],
            Team::FIELD_DESCRIPTION     => 'required|string|max:128',
            Team::RELATION_TEAM_TYPE.".".TeamType::FIELD_ID => 'required|numeric',
            Team::RELATION_TEAM_LEADER                      => 'nullable',
            Team::RELATION_TEAM_MEMBERS                     => 'nullable|array',
            Team::FIELD_PARENT_TEAM_ID                      => 'nullable|exists:' . Team::TABLE . ',' . Team::FIELD_ID
        ];
    }
}
