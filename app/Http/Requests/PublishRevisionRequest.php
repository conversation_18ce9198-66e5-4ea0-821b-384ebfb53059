<?php

namespace App\Http\Requests;

use App\Models\Firestore\Flows\Revision;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class PublishRevisionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo('flow-management');
    }

    /**
     * Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $legacy = $this->request->get(Revision::SCHEMA, 1) < 2;

        return [
            Revision::DESCRIPTION       => ['string', 'max:256', 'nullable'],
            Revision::REVISION_DATA     => [$legacy ? 'string' : 'array'],
            Revision::SLIDE_DATA        => ['array', 'nullable'],
            Revision::FLOW_DATA         => ['array', Rule::requiredIf(!$legacy)],
            Revision::SCHEMA            => ['numeric', Rule::requiredIf(!$legacy)],
        ];
    }

}
