<?php

namespace App\Http\Requests\SalesManagementAPI;

use App\Http\Controllers\API\Sales\Management\SalesManagementAPIController;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class SalesManagementAPIAdminSearchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return Auth::user()->hasPermissionTo("sales-management");
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            SalesManagementAPIController::REQUEST_NAME => ['required', 'string', 'regex:/^[a-zA-Z\s]+$/']
        ];
    }
}
