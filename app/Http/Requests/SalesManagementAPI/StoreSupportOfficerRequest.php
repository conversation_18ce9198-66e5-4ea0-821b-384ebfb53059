<?php

namespace App\Http\Requests\SalesManagementAPI;

use App\Models\SupportOfficer;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreSupportOfficerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return Auth::user()->hasPermissionTo("sales-management");
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $supportOfficer = $this->route()->parameter('supportOfficer');
        $unique         = Rule::unique(SupportOfficer::TABLE)->whereNull(SupportOfficer::FIELD_DELETED_AT);

        if ($supportOfficer instanceof SupportOfficer) $unique->ignore($supportOfficer->id);

        return [
            SupportOfficer::FIELD_USER_ID                 => [
                'required',
                'exists:' . User::TABLE . ',' . User::FIELD_ID,
                $unique
            ],
            SupportOfficer::FIELD_INCLUDED_IN_ROUND_ROBIN => 'required|boolean'
        ];
    }
}
