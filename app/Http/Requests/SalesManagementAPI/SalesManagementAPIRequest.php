<?php

namespace App\Http\Requests\SalesManagementAPI;

use App\Http\Controllers\API\Sales\Management\SalesManagementAPIController;
use App\Models\AccountManager;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class SalesManagementAPIRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return Auth::user()->hasPermissionTo("sales-management");
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            SalesManagementAPIController::REQUEST_USER_ID => ['integer', 'required', 'exists:mysql.'.User::TABLE.','.User::FIELD_ID],
            SalesManagementAPIController::REQUEST_INCLUDE_ST_RR => ['boolean'],
            SalesManagementAPIController::REQUEST_TYPE => ['string', 'required', 'alpha', Rule::in(array_values(AccountManager::TYPES))]
        ];
    }
}
