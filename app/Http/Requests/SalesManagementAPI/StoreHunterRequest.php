<?php

namespace App\Http\Requests\SalesManagementAPI;

use App\Models\Hunter;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreHunterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return Auth::user()->hasPermissionTo("sales-management");
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $hunter = $this->route()->parameter('hunter');
        $unique = Rule::unique(Hunter::TABLE)->whereNull(Hunter::FIELD_DELETED_AT);

        if ($hunter instanceof Hunter) $unique->ignore($hunter->id);

        return [
            Hunter::FIELD_USER_ID                 => [
                'required',
                'exists:' . User::TABLE . ',' . User::FIELD_ID,
                $unique
            ],
            Hunter::FIELD_INCLUDED_IN_ROUND_ROBIN => 'required|boolean',
            Hunter::RELATION_STATES               => 'required|array',
            Hunter::RELATION_INDUSTRIES           => 'required|array'
        ];
    }
}
