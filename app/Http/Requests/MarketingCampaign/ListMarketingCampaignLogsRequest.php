<?php

namespace App\Http\Requests\MarketingCampaign;

use App\Enums\Log\LogLevel;
use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class ListMarketingCampaignLogsRequest extends FormRequest
{
    const string REQUEST_PAGE     = 'page';
    const string REQUEST_PER_PAGE = 'perPage';
    const string REQUEST_LEVELS    = 'levels';
    const string REQUEST_MESSAGE  = 'message';
    const string REQUEST_RELATION_ID = 'relationId';
    const string REQUEST_RELATION_TYPE = 'relationType';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::MARKETING_CAMPAIGN_VIEW->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $paginationRules = [
            self::REQUEST_PAGE     => 'nullable|numeric|min:1',
            self::REQUEST_PER_PAGE => 'nullable|numeric|min:1|max:100',
        ];

        return [
            self::REQUEST_LEVELS   => ['nullable', 'array'],
            self::REQUEST_LEVELS .'.*'   => ['nullable', Rule::enum(LogLevel::class)],
            self::REQUEST_MESSAGE => ['nullable', 'string'],
            self::REQUEST_RELATION_ID => ['nullable', 'int'],
            self::REQUEST_RELATION_TYPE => ['nullable', 'string'],
            ...$paginationRules,
        ];
    }
}

