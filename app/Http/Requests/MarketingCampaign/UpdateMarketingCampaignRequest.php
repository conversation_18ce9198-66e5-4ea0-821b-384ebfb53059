<?php

namespace App\Http\Requests\MarketingCampaign;

use App\Enums\MarketingCampaigns\MarketingCampaignCallbackType;
use App\Enums\MarketingCampaigns\MarketingCampaignType;
use App\Enums\PermissionType;
use App\Models\User;
use App\Services\MarketingCampaign\CallbackPayload\DirectAllocationPayload;
use App\Services\MarketingCampaign\CallbackPayload\SimpleSingleSlideCallbackPayload;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UpdateMarketingCampaignRequest extends FormRequest
{
    const string REQUEST_CAMPAIGN               = 'campaign';
    const string REQUEST_DESCRIPTION            = 'description';
    const string REQUEST_VALIDATION_TYPE        = 'validation_type';
    const string REQUEST_VALIDATION_TYPE_INPUTS = 'validation_type_inputs';
    const string REQUEST_CAMPAIGN_CONFIGURATION = 'configuration';
    const string REQUEST_CAMPAIGN_TYPE          = 'type';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::MARKETING_CAMPAIGNS_EDIT->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $validationType = MarketingCampaignCallbackType::tryFrom($this->input('campaign.validation_type')) ?: MarketingCampaignCallbackType::BASE;
        $campaignType = MarketingCampaignType::tryFrom($this->input('campaign.type'));

        $campaignTypeValidation = $campaignType->getClass()->prefixedConfigValidation(prefix: self::REQUEST_CAMPAIGN .'.'. self::REQUEST_CAMPAIGN_CONFIGURATION);

        $inputValidator = $this->getValidationInputValidation($validationType);

        return [
            self::REQUEST_CAMPAIGN . '.' . self::REQUEST_DESCRIPTION            => ['nullable', 'string'],
            self::REQUEST_CAMPAIGN . '.' . self::REQUEST_VALIDATION_TYPE        => [Rule::enum(MarketingCampaignCallbackType::class)],
            self::REQUEST_CAMPAIGN . '.' . self::REQUEST_VALIDATION_TYPE_INPUTS => ['nullable', 'array'],
            self::REQUEST_CAMPAIGN . '.' . self::REQUEST_CAMPAIGN_TYPE          => [Rule::enum(MarketingCampaignType::class)],
            self::REQUEST_CAMPAIGN . '.' . self::REQUEST_CAMPAIGN_CONFIGURATION => ['sometimes', 'array'],
            ...$inputValidator,
            ...$campaignTypeValidation,
        ];
    }

    public function getValidationInputValidation(MarketingCampaignCallbackType $type): ?array
    {
        return match ($type) {
            MarketingCampaignCallbackType::SIMPLE_SINGLE_SLIDE => [
                self::REQUEST_CAMPAIGN . '.' . self::REQUEST_VALIDATION_TYPE_INPUTS . '.' . SimpleSingleSlideCallbackPayload::HEADING    => ['string'],
                self::REQUEST_CAMPAIGN . '.' . self::REQUEST_VALIDATION_TYPE_INPUTS . '.' . SimpleSingleSlideCallbackPayload::SUBHEADING => ['nullable', 'string'],
            ],
            MarketingCampaignCallbackType::DIRECT_ALLOCATION   => [
                self::REQUEST_CAMPAIGN . '.' . self::REQUEST_VALIDATION_TYPE_INPUTS . '.' . DirectAllocationPayload::HEADING => ['string'],
                self::REQUEST_CAMPAIGN . '.' . self::REQUEST_VALIDATION_TYPE_INPUTS . '.' . DirectAllocationPayload::BODY    => ['nullable', 'string'],
            ],
            MarketingCampaignCallbackType::BASE, MarketingCampaignCallbackType::SOLAR_VALIDATE                => []
        };
    }
}

