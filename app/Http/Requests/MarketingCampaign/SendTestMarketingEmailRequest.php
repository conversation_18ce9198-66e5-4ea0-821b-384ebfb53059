<?php

namespace App\Http\Requests\MarketingCampaign;

use App\Enums\PermissionType;
use App\Models\EmailTemplate;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class SendTestMarketingEmailRequest extends FormRequest
{

    const string EMAIL_TEMPLATE_ID = 'emailTemplateId';
    const string FROM_EMAIL = 'fromEmail';
    const string FROM_NAME = 'fromName';
    const string TO_EMAIL = 'toEmail';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::MARKETING_CAMPAIGNS_CREATE->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::EMAIL_TEMPLATE_ID => ['required', 'exists:' . EmailTemplate::TABLE . ',' . EmailTemplate::FIELD_ID],
            self::FROM_EMAIL => ['required'],
            self::FROM_NAME => ['required'],
            self::TO_EMAIL => ['required'],
        ];
    }
}

