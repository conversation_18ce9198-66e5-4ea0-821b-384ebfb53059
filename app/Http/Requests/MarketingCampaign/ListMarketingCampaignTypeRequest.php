<?php

namespace App\Http\Requests\MarketingCampaign;

use App\Enums\MarketingCampaigns\MarketingCampaignType;
use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class ListMarketingCampaignTypeRequest extends FormRequest
{
    const string TYPE = 'type';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::MARKETING_CAMPAIGN_VIEW->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::TYPE => ['nullable', 'array'],
            self::TYPE . '*' => ['nullable', Rule::enum(MarketingCampaignType::class)],
        ];
    }
}

