<?php

namespace App\Http\Requests\MarketingCampaign;

use App\Enums\MarketingCampaigns\MarketingCampaignConsumerStatus;
use App\Enums\PermissionType;
use App\Models\MarketingCampaign;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class ListMarketingCampaignConsumersRequest extends FormRequest
{
    const string REQUEST_NAME                  = 'name';
    const string REQUEST_MARKETING_CAMPAIGN_ID = 'campaign_id';
    const string REQUEST_PAGE                  = 'page';
    const string REQUEST_PER_PAGE              = 'perPage';
    const string REQUEST_STATUS                = 'status';
    const string REQUEST_REVALIDATED           = 'revalidated';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::MARKETING_CAMPAIGN_VIEW->value);
    }

    protected function prepareForValidation(): void
    {
        $revalidated = filled($this->{self::REQUEST_REVALIDATED}) ? filter_var($this->{self::REQUEST_REVALIDATED}, FILTER_VALIDATE_BOOLEAN) : null;

        $this->merge([
            self::REQUEST_REVALIDATED => $revalidated,
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $paginationRules = [
            self::REQUEST_PAGE     => 'nullable|numeric|min:1',
            self::REQUEST_PER_PAGE => 'nullable|numeric|min:1|max:100',
        ];

        return [
            self::REQUEST_MARKETING_CAMPAIGN_ID => ['nullable', 'exists:' . MarketingCampaign::TABLE . ',' . MarketingCampaign::FIELD_ID],
            self::REQUEST_NAME                  => ['nullable', 'string'],
            self::REQUEST_STATUS                => ['nullable', Rule::enum(MarketingCampaignConsumerStatus::class)],
            self::REQUEST_REVALIDATED           => ['nullable', 'boolean'],
            ...$paginationRules,
        ];
    }
}

