<?php

namespace App\Http\Requests\MarketingCampaign;

use App\Enums\PermissionType;
use App\Models\MarketingCampaign;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class CreateMarketingCampaignRequest extends UpdateMarketingCampaignRequest
{
    const string REQUEST_FILTERS                = 'filters';
    const string REQUEST_NAME                   = 'name';


    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::MARKETING_CAMPAIGNS_CREATE->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::REQUEST_FILTERS                                               => ['array'],
            self::REQUEST_CAMPAIGN                                              => ['array'],
            self::REQUEST_CAMPAIGN . '.' . self::REQUEST_NAME                   => ['string', 'unique:' . MarketingCampaign::TABLE . ',' . MarketingCampaign::FIELD_NAME],
            ...parent::rules()
        ];
    }
}

