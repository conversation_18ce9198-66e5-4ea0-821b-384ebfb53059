<?php

namespace App\Http\Requests\MarketingCampaign;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Str;

class GetMarketingCampaignConsumerDataRequest extends FormRequest
{
    const string MARKETING_CAMPAIGN_EXTERNAL_REFERENCE = 'campaign';

    protected function prepareForValidation(): void
    {
        $this->merge([
            self::MARKETING_CAMPAIGN_EXTERNAL_REFERENCE => Str::of(
                $this->query(self::MARKETING_CAMPAIGN_EXTERNAL_REFERENCE)
            )->trim()->trim('.')->lower()->toString()
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::MARKETING_CAMPAIGN_EXTERNAL_REFERENCE => ['uuid'],
        ];
    }

}
