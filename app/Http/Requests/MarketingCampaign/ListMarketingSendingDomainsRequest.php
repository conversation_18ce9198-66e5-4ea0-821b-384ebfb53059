<?php

namespace App\Http\Requests\MarketingCampaign;

use App\Enums\Emails\DomainStatus;
use App\Enums\MarketingCampaigns\MarketingCampaignType;
use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class ListMarketingSendingDomainsRequest extends FormRequest
{
    const string REQUEST_PAGE = 'page';
    const string REQUEST_PER_PAGE = 'perPage';
    const string FIELD_MAX_SENT_CUTOFF = 'send_count_cutoff';
    const string FIELD_STATUS = 'status';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $paginationRules = [
            self::REQUEST_PAGE     => ['nullable', 'numeric', 'min:1'],
            self::REQUEST_PER_PAGE => ['nullable', 'numeric', 'min:1', 'max:100'],
        ];

        return [
            ...$paginationRules,
            self::FIELD_MAX_SENT_CUTOFF => ['nullable','numeric'],
            self::FIELD_STATUS        => ['nullable', Rule::enum(DomainStatus::class)],
        ];
    }
}

