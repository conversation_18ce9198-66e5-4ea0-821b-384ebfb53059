<?php

namespace App\Http\Requests;

use App\Enums\PermissionType;
use App\Http\Controllers\API\ActivityLogController;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class ActivityLogRequest extends FormRequest
{
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::ACTIVITY_LOGS_CAMPAIGNS_VIEW);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return
            [
                ActivityLogController::REQUEST_ACTIVITY_SUBJECT_TYPE => ['required', 'string'],
                ActivityLogController::REQUEST_ACTIVITY_SUBJECT_ID => ['required', 'integer'],
                ActivityLogController::REQUEST_ACTIVITY_EVENTS => ['required', 'array']
            ];
    }

}
