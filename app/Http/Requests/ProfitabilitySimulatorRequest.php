<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProfitabilitySimulatorRequest extends FormRequest
{
    const string INDUSTRY      = 'industry';

    const string BUDGET              = 'budget';
    const string BUDGET_MIN          = 'budget_min';
    const string BUDGET_MAX          = 'budget_max';
    const string BUDGET_USE_NO_LIMIT = 'budget_use_no_limit';
    const string BUDGET_INTERVAL     = 'budget_interval';

    const string INCLUDE_WEEKENDS = 'include_weekends';
    const string LEAD_TYPES       = 'lead_types';
    const string FLAT_PRICES      = 'flat_prices';

    const string PRICE_MIN      = 'price_min';
    const string PRICE_MAX      = 'price_max';
    const string PRICE_INTERVAL = 'price_interval';
    const string PRICE_TYPE     = 'price_type';
    const string PRICE_VALUE    = 'price_value';

    const string START_DATE = 'start_date';
    const string END_DATE   = 'end_date';

    const string LOCATION_IDS        = 'location_ids';
    const string CAMPAIGN            = 'campaign';
    const string ORIGIN_CAMPAIGN_ID  = 'origin_campaign_id';
    const string AUXILIARY_CAMPAIGNS = 'aux_campaigns';

    const array PRICE_TYPES = [
        'percentage-discount',
        'dollar-discount',
        'flat'
    ];

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::INDUSTRY     => ['required', 'string', Rule::in(['solar', 'roofing', 'windows', 'hvac', 'siding'])],
            self::LOCATION_IDS => ['required', 'array'],
            self::START_DATE   => ['required', 'date'],
            self::END_DATE     => ['required', 'date'],
            self::LEAD_TYPES   => ['required', 'array', 'min:1', 'in:verified,unverified'],

            self::CAMPAIGN                                   => ['required', 'array'],
            self::CAMPAIGN . '.' . self::ORIGIN_CAMPAIGN_ID  => ['nullable', 'integer'],
            self::CAMPAIGN . '.' . self::BUDGET_MIN          => ['numeric', 'min:50', 'max:10000'],
            self::CAMPAIGN . '.' . self::BUDGET_MAX          => ['numeric', 'min:100', 'max:10000'],
            self::CAMPAIGN . '.' . self::BUDGET_USE_NO_LIMIT => ['required', 'boolean'],
            self::CAMPAIGN . '.' . self::BUDGET_INTERVAL     => ['numeric', 'min:50', 'max:1000'],
            self::CAMPAIGN . '.' . self::INCLUDE_WEEKENDS    => ['required', 'boolean'],
            self::CAMPAIGN . '.' . self::PRICE_MIN           => ['numeric', 'min:0', 'max:95'],
            self::CAMPAIGN . '.' . self::PRICE_MAX           => ['numeric', 'min:0', 'max:100'],
            self::CAMPAIGN . '.' . self::PRICE_INTERVAL      => ['numeric', 'min:0', 'max:50'],
            self::CAMPAIGN . '.' . self::PRICE_TYPE          => ['required', Rule::in(self::PRICE_TYPES)],
            self::CAMPAIGN . '.' . self::FLAT_PRICES         => ['array:exclusive,duo,trio,quad,unverified'],

            self::AUXILIARY_CAMPAIGNS                                  => ['nullable', 'array'],
            self::AUXILIARY_CAMPAIGNS . '.*.' . self::INCLUDE_WEEKENDS => ['required', 'boolean'],
            self::AUXILIARY_CAMPAIGNS . '.*.' . self::BUDGET           => ['numeric', 'min:0', 'max:10000'],
            self::AUXILIARY_CAMPAIGNS . '.*.' . self::PRICE_TYPE       => ['required', Rule::in(self::PRICE_TYPES)],
            self::AUXILIARY_CAMPAIGNS . '.*.' . self::PRICE_VALUE      => ['numeric', 'min:0', 'max:100'],
            self::AUXILIARY_CAMPAIGNS . '.*.' . self::FLAT_PRICES      => ['array:exclusive,duo,trio,quad,unverified'],
        ];
    }
}
