<?php

namespace App\Http\Requests;

use App\Models\Firestore\Flows\Flow;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreFlowRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo('flow-management');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            Flow::NAME                            => ['string', 'min:4', 'max:64', Rule::requiredIf($this->isMethod('post'))],
            Flow::DESCRIPTION                     => ['string', 'max:256', 'nullable'],
            Flow::VERSIONS                        => ['array'],
            Flow::VERSIONS .'.'. Flow::PRODUCTION => ['uuid'],
        ];
    }

}
