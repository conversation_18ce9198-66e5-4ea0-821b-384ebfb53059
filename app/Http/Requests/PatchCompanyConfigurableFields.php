<?php

namespace App\Http\Requests;

use App\Http\Controllers\API\CompaniesController;
use App\Models\Odin\ConfigurableFieldType;
use App\Models\Odin\GlobalCompanyField;
use App\Models\Odin\IndustryCompanyField;
use App\Models\Odin\ServiceCompanyField;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class PatchCompanyConfigurableFields extends FormRequest
{
    /**
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(CompaniesController::MODULE_PERMISSION);
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        $globalFields     = GlobalCompanyField::all()->pluck(GlobalCompanyField::FIELD_TYPE, GlobalCompanyField::FIELD_KEY)->toArray();
        $industryFields   = IndustryCompanyField::all()->pluck(IndustryCompanyField::FIELD_TYPE, IndustryCompanyField::FIELD_KEY)->toArray();
        $serviceFields    = ServiceCompanyField::all()->pluck(ServiceCompanyField::FIELD_TYPE, ServiceCompanyField::FIELD_KEY)->toArray();
        $permittedFields  = array_merge($globalFields, $industryFields, $serviceFields);

        $rules = [];

        foreach ($permittedFields as $field => $type) {
            $rules[$field] = [
                'sometimes',
                ConfigurableFieldType::query()->find($type)?->type ?? "String"
            ];
        }

        return $rules;
    }
}
