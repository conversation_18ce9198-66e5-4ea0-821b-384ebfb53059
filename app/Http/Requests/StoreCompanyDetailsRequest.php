<?php

namespace App\Http\Requests;

use App\Enums\RoleType;
use App\Models\CompanySlug;
use App\Models\Legacy\EloquentCompany;
use App\Models\Odin\Company;
use App\Models\Odin\IndustryService;
use App\Models\User;
use App\Repositories\Odin\CompanyRepository;
use App\Rules\Slug;
use App\Services\CompanySlugService;
use App\Services\UserAuthorizationService;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Random\RandomException;

class StoreCompanyDetailsRequest extends FormRequest
{
    const FIELD_SERVICES = 'services';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();
        if ($user->hasRole(RoleType::ADMIN->value)) {
            return true;
        }

        $userAuthorizationService = app(UserAuthorizationService::class);
        $company = app(CompanyRepository::class)->findOrFail((int) $this->route()->parameter('company_id'));

        return $userAuthorizationService->canUpdateCompanySalesData($company);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $company = app(CompanyRepository::class)->findOrFail((int) $this->route()->parameter('company_id'));

        /**
         * @var CompanySlug|null $activeCompanySlug
         */
        $activeCompanySlug = $company->{Company::RELATION_ACTIVE_COMPANY_SLUG};

        return
            [
                Company::FIELD_NAME                 => 'sometimes|string|max:255',
                Company::FIELD_ENTITY_NAME          => 'nullable|string|max:255',
                self::FIELD_SERVICES                => 'sometimes|array',
                self::FIELD_SERVICES . '.*'         => 'sometimes|in:' . implode(',', IndustryService::query()->get()->pluck(IndustryService::FIELD_ID)->toArray()),
                'type'                              => 'sometimes|in:' . implode(',', EloquentCompany::COMPANY_TYPES),
                'prescreened'                       => 'sometimes|boolean',
                'slug'                              => [
                    'nullable',
                    'string',
                    'max:255',
                    app(Slug::class),
                    'lowercase',
                    Rule::unique('company_slugs', 'slug')->ignore($activeCompanySlug?->id),
                ],
            ];
    }

    public function messages(): array
    {
        return [
            self::FIELD_SERVICES . '.*.in' => 'Invalid service id',
            'type.in'                      => 'Invalid company type',
            'slug.unique'                  => 'Slug already exists',
            'slug.max'                     => 'Slug must be less than 255 characters',
            'slug.lowercase'               => 'Slug must be lowercase',
            'slug.string'                  => 'Slug must be a string',
        ];
    }

    /**
     * @param  Validator  $validator
     * @return void
     * @throws RandomException
     */
    public function failedValidation(Validator $validator): void
    {
        $failed = $validator->failed();

        $failedDueToSlug = data_get($failed, 'slug', false);

        if (!!$failedDueToSlug) {
            $slugFailedDueToUniquenessConstraint = data_get($failedDueToSlug, 'Unique', false);

            if ($slugFailedDueToUniquenessConstraint) {
                $companySlugService = app(CompanySlugService::class);

                $companyName = $this->input(Company::FIELD_NAME);
                $attemptedSlug = $this->input('slug');

                $alternateSlugNames = $companySlugService->getAlternateSlugNames($companyName, $attemptedSlug);

                $validator->errors()->merge(
                    [
                        'slug' => count($alternateSlugNames) ? "Here's a list of slugs you can use " . implode(', ', $alternateSlugNames)
                            : 'A list of alternate slugs could not be generated at the time. Please try again.'
                    ]
                );
            }
        }

        parent::failedValidation($validator);
    }
}
