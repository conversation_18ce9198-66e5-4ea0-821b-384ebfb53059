<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreUserSearchFilterRequest extends FormRequest
{
    const REQUEST_FILTER_CATEGORY = "category";
    const REQUEST_FILTER_DATA = "data";

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::REQUEST_FILTER_CATEGORY => ['required', 'integer'],
            self::REQUEST_FILTER_DATA => ['required']
        ];
    }
}
