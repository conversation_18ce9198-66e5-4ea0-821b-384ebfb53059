<?php

namespace App\Http\Requests;

use App\Enums\PermissionType;
use App\Enums\TimePeriod;
use App\Models\EmailTemplate;
use App\Models\MissedProducts\MissedProductConfig;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class SaveMissedProductsConfigRequest extends FormRequest
{
    const FIELD_EXPIRATION_QUANTITY     = 'expiration_quantity';
    const FIELD_EXPIRATION_TYPE         = 'expiration_type';
    const FIELD_NOTIFICATION_COOLDOWN_IN_DAYS   = 'notification_cooldown_in_days';
    const FIELD_OVERRIDE_CAN_RECEIVE_PROMOTIONS   = 'override_can_receive_promotions';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::OPPORTUNITY_NOTIFICATION->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::FIELD_EXPIRATION_QUANTITY                 => ['sometimes', 'nullable', 'required_with:' . self::FIELD_EXPIRATION_TYPE, 'integer'],
            self::FIELD_EXPIRATION_TYPE                     => ['sometimes', 'nullable', 'required_with:' . self::FIELD_EXPIRATION_QUANTITY, 'string', Rule::in([TimePeriod::DAY->value, TimePeriod::WEEK->value, TimePeriod::MONTH->value, TimePeriod::YEAR->value])],
            self::FIELD_NOTIFICATION_COOLDOWN_IN_DAYS       => ['sometimes', 'nullable', 'integer'],
            MissedProductConfig::FIELD_PURCHASE_FAIL_EMAIL_TEMPLATE_ID => ['required', 'exists:' . EmailTemplate::TABLE . ',' . EmailTemplate::FIELD_ID],
            self::FIELD_OVERRIDE_CAN_RECEIVE_PROMOTIONS     => ['sometimes', 'bool']
        ];
    }
}
