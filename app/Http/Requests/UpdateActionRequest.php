<?php

namespace App\Http\Requests;

use App\Http\Controllers\API\Workflows\TaskController;
use App\Models\Action;
use App\Models\ActionCategory;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateActionRequest extends FormRequest
{
    public const REQUEST_ACTION_ID = 'action_id';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::REQUEST_ACTION_ID => 'sometimes|integer|min:0',
            Action::FIELD_FOR_ID => 'required|integer',
            Action::FIELD_FOR_RELATION_TYPE => 'required|in:'.implode(',', [Action::RELATION_TYPE_COMPANY, Action::RELATION_TYPE_COMPANY_CONTACT]),
            Action::FIELD_SUBJECT => 'required|string|max:255',
            Action::FIELD_MESSAGE => 'required|string',
            Action::FIELD_CATEGORY_ID => [ 'integer', 'required', Rule::in(ActionCategory::all()->pluck(ActionCategory::FIELD_ID)) ],
            Action::FIELD_DISPLAY_DATE => 'date|nullable',
            Action::RELATION_TAGS => [ 'array', 'nullable', Rule::exists(User::TABLE, User::FIELD_ID)],
            Action::FIELD_TAG_BY_EMAIL => 'sometimes|boolean',
            TaskController::REQUEST_UPDATE_SALES_STATUS => 'sometimes|integer|nullable'
        ];
    }
}
