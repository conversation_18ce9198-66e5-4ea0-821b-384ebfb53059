<?php

namespace App\Http\Requests;

use App\Models\UserAction;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateUserActionRequest extends FormRequest
{
    public const REQUEST_ACTION_ID = 'action_id';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::REQUEST_ACTION_ID => 'sometimes|integer|min:0',
            UserAction::FIELD_SUBJECT => 'required|string|max:255',
            UserAction::FIELD_MESSAGE => 'required|string',
            UserAction::FIELD_DISPLAY_DATE => 'date|nullable',
            UserAction::RELATION_TAGS => [ 'array', 'nullable', Rule::exists(User::TABLE, User::FIELD_ID)],
            UserAction::FIELD_TAG_BY_EMAIL => 'sometimes|boolean',
        ];
    }
}
