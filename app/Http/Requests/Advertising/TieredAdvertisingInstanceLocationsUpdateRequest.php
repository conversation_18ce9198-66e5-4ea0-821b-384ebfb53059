<?php

namespace App\Http\Requests\Advertising;

use Exception;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class TieredAdvertisingInstanceLocationsUpdateRequest extends FormRequest
{
    const string INSTANCE_ID = 'instance';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return Auth::user()->hasPermissionTo('advertising-admin');
    }

    /**
     * @return array
     * @throws Exception
     */
    public function rules()
    {
        return [
            $this::INSTANCE_ID => ['integer', 'nullable'],
        ];
    }
}
