<?php

namespace App\Http\Requests\Advertising;

use App\Enums\Advertising\AdvertisingPlatform;
use App\Enums\Odin\Industry;
use App\Http\Controllers\API\Advertising\AdvertisingAPIController;
use App\Enums\Advertising\Advertiser as AdvertiserEnum;
use App\Models\AdvertisingAccount;
use App\Models\Odin\Website;
use Exception;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;

class SaveAccountRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return Auth::user()->hasPermissionTo('advertising-admin');
    }

    /**
     * @return array
     * @throws Exception
     */
    public function rules()
    {
        if(!in_array($this->route()->parameter('platform'), array_keys(AdvertisingPlatform::all()))) {
            throw new Exception("Invalid platform");
        }

        $websiteIds = Website::query()->pluck(Website::FIELD_ID)->toArray();

        return [
            AdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID => ['string', 'required', 'max:255'],
            AdvertisingAccount::FIELD_NAME => ['string', 'required', 'regex:/[a-zA-Z0-9\-\_\s]+/', 'max:255'],
            AdvertisingAccount::FIELD_INDUSTRY => ['string', 'required', new Enum(Industry::class)],
            AdvertisingAPIController::REQUEST_ADVERTISER => ['string', 'required', Rule::in(array_keys(AdvertiserEnum::advertisersByKey()))],
            AdvertisingAccount::FIELD_TRACKS_CONVERSIONS => ['boolean', 'required'],
            AdvertisingAccount::FIELD_UPLOAD_CONVERSIONS_INTERVAL_HOURS => ['required', 'integer', (request()->get(AdvertisingAccount::FIELD_TRACKS_CONVERSIONS) ? 'min:1' : '')],
            AdvertisingAPIController::REQUEST_WEBSITES => ['array', 'required'],
            AdvertisingAPIController::REQUEST_WEBSITES.'.*' => [
                'required',
                'integer',
                Rule::in($websiteIds)
            ]
        ];
    }
}
