<?php

namespace App\Http\Requests\Advertising;

use Exception;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class TieredAdvertisingUpdateRequest extends FormRequest
{
    const string CAMPAIGNS          = 'campaigns';
    const string ROAS               = 'roas';
    const string UPDATE_FREQUENCY   = 'updateFrequency';
    const string ENABLED            = 'enabled';
    const string INSTANCE_ID        = 'instanceId';
    const string ADVERTISER         = 'advertiser';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return Auth::user()->hasPermissionTo('advertising-admin');
    }

    /**
     * @return array
     * @throws Exception
     */
    public function rules()
    {
        return [
            $this::CAMPAIGNS        => ['array', 'nullable'],
            $this::ROAS             => ['numeric', 'required'],
            $this::UPDATE_FREQUENCY => ['numeric', 'required'],
            $this::ENABLED          => ['boolean', 'required'],
            $this::INSTANCE_ID      => ['integer', 'nullable'],
            $this::ADVERTISER       => ['integer', 'required'],
        ];
    }
}
