<?php

namespace App\Http\Requests\Advertising;

use App\Enums\Advertising\AdvertisingPlatform;
use App\Enums\Advertising\AdvertisingCostMetric;
use App\Http\Controllers\API\Advertising\AdvertisingAPIController;
use App\Models\AdvertisingAccount;
use App\Rules\InputTypes;
use App\Services\Advertising\Campaigns\AdvertisingServiceFactory;
use Exception;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class GetCampaignsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return Auth::user()->hasPermissionTo("advertising");
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     * @throws Exception
     */
    public function rules()
    {
        $platform = $this->route()->parameter('platform');
        if(!in_array($platform, array_keys(AdvertisingPlatform::all()))) {
            throw new Exception("Invalid platform");
        }

        $adService = AdvertisingServiceFactory::make($platform);
        $campaignStatuses = $adService->getCampaignStatusOptions()->toArray();

        $accountExists = AdvertisingAccount::query()
            ->where(AdvertisingAccount::FIELD_PLATFORM, $platform)
            ->where(AdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID, $this->route()->parameter('accountId'))
            ->exists();

        if(!$accountExists) {
            throw new Exception("Invalid account ID");
        }

        return [
            AdvertisingAPIController::REQUEST_PAGE => ['required', 'integer', 'min:1'],
            AdvertisingAPIController::REQUEST_PER_PAGE => ['required', 'integer', 'min:1'],
            AdvertisingAPIController::REQUEST_SEARCH_STATE_LOCATION_ID => ['present', 'integer', 'nullable', 'min:0'],
            AdvertisingAPIController::REQUEST_COST_METRIC => ['present', 'string', 'nullable', Rule::in(array_merge(array_keys(AdvertisingCostMetric::all()), ['all']))],
            AdvertisingAPIController::REQUEST_CAMPAIGN_STATUS => ['present', 'string', 'nullable', Rule::in(array_merge(array_keys($campaignStatuses), ['all']))],
            AdvertisingAPIController::REQUEST_CAMPAIGN_NAME => ['present', 'string', 'nullable'],
            AdvertisingAPIController::REQUEST_PAGE_TOKENS => ['sometimes', 'nullable', new InputTypes(['array', 'string'])]
        ];
    }
}
