<?php

namespace App\Http\Requests\Advertising;

use App\Enums\Advertising\AdvertisingPlatform;
use App\Enums\Odin\Industry;
use App\Http\Controllers\API\Advertising\AdvertisingAPIController;
use App\Enums\Advertising\Advertiser as AdvertiserEnum;
use App\Models\AdvertisingAccount;
use App\Models\Odin\Website;
use Exception;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;

class AdCostBackfillRequest extends FormRequest
{
    const string DATE_RANGE     = 'date_range';
    const string PLATFORMS      = 'platforms';
    const string INDUSTRIES     = 'industries';
    const string ADVERTISERS    = 'advertisers';
    const string RESOLUTIONS    = 'resolutions';
    const string CLEAR_SECTION  = 'clear_section';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return Auth::user()->hasPermissionTo('advertising-admin');
    }

    /**
     * @return array
     * @throws Exception
     */
    public function rules()
    {
        return [
            $this::DATE_RANGE    => ['array', 'required'],
            $this::PLATFORMS     => ['array', 'required'],
            $this::INDUSTRIES    => ['array', 'required'],
            $this::ADVERTISERS   => ['array', 'required'],
            $this::RESOLUTIONS   => ['array', 'required'],
            $this::CLEAR_SECTION => ['boolean', 'required'],
        ];
    }
}
