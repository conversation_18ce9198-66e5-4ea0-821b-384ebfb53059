<?php

namespace App\Http\Requests\Advertising;

use App\Enums\Advertising\AdvertisingPlatform;
use App\Exceptions\CustomValidationException;
use App\Models\AdvertisingAccount;
use Exception;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Auth;

class DeleteAccountRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return Auth::user()->hasPermissionTo('advertising-admin');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     * @throws CustomValidationException
     */
    public function rules()
    {
        try {
            $platform = $this->route()->parameter('platform');
            if(!in_array($platform, array_keys(AdvertisingPlatform::all()))) {
                throw new CustomValidationException("Invalid platform");
            }

            $accountId = $this->route()->parameter('accountId');
            $accountExists = AdvertisingAccount::query()
                ->where(AdvertisingAccount::FIELD_PLATFORM, $platform)
                ->where(AdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID, $accountId)
                ->exists();

            if(!$accountExists) {
                throw new CustomValidationException("Invalid account ID");
            }
        }
        catch(Exception $e) {
            logger()->error(__METHOD__.": ".$e->getMessage());

            if($e instanceof CustomValidationException) {
                throw new HttpResponseException(response()->json(['error' => $e->getMessage()], 422));
            }
            else {
                throw $e;
            }
        }

        return [
            //
        ];
    }
}
