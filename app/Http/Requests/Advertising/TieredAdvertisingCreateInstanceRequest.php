<?php

namespace App\Http\Requests\Advertising;

use Exception;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class TieredAdvertisingCreateInstanceRequest extends FormRequest
{
    const string INSTANCE_NAME      = 'instance';
    const string ASSIGN_EXISTING    = 'assignExisting';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return Auth::user()->hasPermissionTo('advertising-admin');
    }

    /**
     * @return array
     * @throws Exception
     */
    public function rules()
    {
        return [
            $this::INSTANCE_NAME    => ['string', 'required'],
            $this::ASSIGN_EXISTING  => ['bool', 'required'],
        ];
    }
}
