<?php

namespace App\Http\Requests\Advertising;

use App\Enums\Advertising\AdvertisingPlatform;
use App\Exceptions\CustomValidationException;
use App\Http\Controllers\API\Advertising\AdvertisingAPIController;
use App\Models\AdvertisingAccount;
use App\Models\AdvertisingCampaign;
use App\Models\AdvertisingCampaignAutomationParameter;
use Exception;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Auth;

class UpdateCampaignRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return Auth::user()->hasPermissionTo("advertising");
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     * @throws Exception
     */
    public function rules()
    {
        try {
            $platform = $this->route()->parameter('platform');
            if(!in_array($platform, array_keys(AdvertisingPlatform::all()))) {
                throw new CustomValidationException("Invalid platform");
            }

            $accountId = $this->route()->parameter('accountId');
            $accountExists = AdvertisingAccount::query()
                ->where(AdvertisingAccount::FIELD_PLATFORM, $platform)
                ->where(AdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID, $accountId)
                ->exists();

            if(!$accountExists) {
                throw new CustomValidationException("Invalid account ID");
            }

            $campaigns = $this->get(AdvertisingAPIController::REQUEST_CAMPAIGNS, []);

            if(empty($campaigns)
            || !is_array($campaigns)) {
                throw new CustomValidationException("Missing campaigns");
            }

            foreach($campaigns as $campaign) {
                $campaignHasAllKeys = collect($campaign)->has([
                    AdvertisingCampaign::FIELD_PLATFORM,
                    AdvertisingCampaign::FIELD_PLATFORM_ACCOUNT_ID,
                    AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID,
                    AdvertisingCampaign::FIELD_RUN_INTERVAL_DISPLAY_UNIT,
                    AdvertisingAPIController::REQUEST_RUN_INTERVAL,
                    AdvertisingAPIController::REQUEST_AUTOMATION_PARAMETERS,
                    AdvertisingAPIController::REQUEST_AUTOMATED
                ]);

                if(!$campaignHasAllKeys) {
                    throw new CustomValidationException("Campaign missing keys");
                }

                if($campaign[AdvertisingCampaign::FIELD_PLATFORM] !== $platform) {
                    throw new CustomValidationException("Invalid campaign platform");
                }

                if($campaign[AdvertisingCampaign::FIELD_PLATFORM_ACCOUNT_ID] !== $accountId) {
                    throw new CustomValidationException("Invalid campaign account ID");
                }

                if(empty($campaign[AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID])
                && !is_string($campaign[AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID])
                && !is_numeric($campaign[AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID])) {
                    throw new CustomValidationException("Invalid campaign ID");
                }

                //Could be either a boolean or a number that casts to boolean
                if(!in_array($campaign[AdvertisingAPIController::REQUEST_AUTOMATED], [true, false])) {
                    throw new CustomValidationException("Invalid campaign automated status. Campaign ".$campaign[AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID]);
                }

                if($campaign[AdvertisingAPIController::REQUEST_AUTOMATED]
                && (
                    $campaign[AdvertisingAPIController::REQUEST_RUN_INTERVAL] < 1
                    || !is_integer($campaign[AdvertisingAPIController::REQUEST_RUN_INTERVAL])
                )) {
                    throw new CustomValidationException("Run interval must be a non-zero integer. Campaign ".$campaign[AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID]);
                }

                if($campaign[AdvertisingAPIController::REQUEST_AUTOMATED]
                && !in_array($campaign[AdvertisingCampaign::FIELD_RUN_INTERVAL_DISPLAY_UNIT], [AdvertisingCampaign::RUN_INTERVAL_HOURS, AdvertisingCampaign::RUN_INTERVAL_WEEKS, AdvertisingCampaign::RUN_INTERVAL_MINUTES], true)) {
                    throw new CustomValidationException("Invalid run interval unit. Campaign ".$campaign[AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID]);
                }

                if($campaign[AdvertisingAPIController::REQUEST_AUTOMATED]
                && empty($campaign[AdvertisingAPIController::REQUEST_AUTOMATION_PARAMETERS])) {
                    throw new CustomValidationException("Missing automation parameters. Campaign ".$campaign[AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID]);
                }

                foreach($campaign[AdvertisingAPIController::REQUEST_AUTOMATION_PARAMETERS] as $automationParameter) {
                    $parameterHasAllKeys = collect($automationParameter)->has([
                        AdvertisingCampaignAutomationParameter::FIELD_TYPE,
                        AdvertisingCampaignAutomationParameter::FIELD_STRATEGY,
                        AdvertisingCampaignAutomationParameter::FIELD_PARAMETER,
                        AdvertisingCampaignAutomationParameter::FIELD_OPERATOR,
                        AdvertisingCampaignAutomationParameter::FIELD_THRESHOLD,
                        AdvertisingCampaignAutomationParameter::FIELD_THRESHOLD_TYPE
                    ]);

                    if(!$parameterHasAllKeys) {
                        throw new CustomValidationException("Parameter is missing keys. Campaign ".$campaign[AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID]);
                    }

                    $type = $automationParameter[AdvertisingCampaignAutomationParameter::FIELD_TYPE];
                    $strategy = $automationParameter[AdvertisingCampaignAutomationParameter::FIELD_STRATEGY];
                    $parameter = $automationParameter[AdvertisingCampaignAutomationParameter::FIELD_PARAMETER];
                    $thresholdType = $automationParameter[AdvertisingCampaignAutomationParameter::FIELD_THRESHOLD_TYPE];

                    if(empty(AdvertisingCampaignAutomationParameter::PARAMETER_OPTIONS[$type][$strategy][$parameter])
                    || !in_array($thresholdType, AdvertisingCampaignAutomationParameter::PARAMETER_OPTIONS[$type][$strategy][$parameter], true)) {
                        throw new CustomValidationException("Invalid parameter options. Campaign ".$campaign[AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID]);
                    }

                    if(!is_integer($automationParameter[AdvertisingCampaignAutomationParameter::FIELD_THRESHOLD])
                    || $automationParameter[AdvertisingCampaignAutomationParameter::FIELD_THRESHOLD] < 0) {
                        throw new CustomValidationException("Threshold must be non-negative integer. Campaign ".$campaign[AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID]);
                    }

                    if(!in_array($automationParameter[AdvertisingCampaignAutomationParameter::FIELD_OPERATOR], AdvertisingCampaignAutomationParameter::OPERATORS, true)) {
                        throw new CustomValidationException('Invalid operator. Campaign '.$campaign[AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID]);
                    }
                }
            }
        }
        catch(Exception $e) {
            logger()->error(__METHOD__.": ".$e->getMessage());

            if($e instanceof CustomValidationException) {
                throw new HttpResponseException(response()->json(['error' => $e->getMessage()], 422));
            }
            else {
                throw $e;
            }
        }

        //Detailed validation performed above
        return [
            AdvertisingAPIController::REQUEST_CAMPAIGNS => ['array', 'required']
        ];
    }
}
