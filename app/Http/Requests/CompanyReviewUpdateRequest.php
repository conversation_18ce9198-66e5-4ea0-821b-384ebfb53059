<?php

namespace App\Http\Requests;

use App\Models\Odin\CompanyReview;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class CompanyReviewUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo('manage-company-reviews');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            CompanyReview::FIELD_STATUS => ['integer', Rule::in(array_keys(CompanyReview::STATUS_NAMES))],
            CompanyReview::FIELD_EMAIL_VALIDATED => ['boolean'],
            CompanyReview::FIELD_PHONE_VALIDATED => ['boolean']
        ];
    }
}
