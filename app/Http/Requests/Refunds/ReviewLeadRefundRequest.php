<?php

namespace App\Http\Requests\Refunds;

use App\Enums\LeadRefundItemStatus;
use App\Enums\RoleType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class ReviewLeadRefundRequest extends FormRequest
{
    const string FIELD_ITEMS    = 'items';
    const string FIELD_COMMENTS = 'comments';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasRole(RoleType::LEAD_REFUNDS_REVIEWER->value);
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            self::FIELD_ITEMS               => 'array',
            self::FIELD_ITEMS . '.*.id'     => 'required',
            self::FIELD_ITEMS . '.*.status' => 'required|string|' . Rule::in([LeadRefundItemStatus::APPROVED, LeadRefundItemStatus::REJECTED, LeadRefundItemStatus::MORE_INFORMATION_NEEDED, LeadRefundItemStatus::PENDING]),
            self::FIELD_COMMENTS            => 'array',
            self::FIELD_COMMENTS . '.*'     => 'required|string',
        ];
    }
}
