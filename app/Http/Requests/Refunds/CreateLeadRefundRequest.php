<?php

namespace App\Http\Requests\Refunds;

use App\Enums\RoleType;
use App\Models\Odin\ProductAssignment;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class CreateLeadRefundRequest extends FormRequest
{
    const string FIELD_ITEMS = 'items';

    const string FIELD_ITEM_PRODUCT_ASSIGNMENT_ID = 'product_assignment_id';
    const string FIELD_ITEM_REFUND_REASON         = 'refund_reason';
    const string FIELD_ITEM_REFUND_TYPE           = 'refund_type';
    const string FIELD_ITEM_COST                  = 'cost';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasAnyRole([
            RoleType::LEAD_REFUNDS_REVIEWER->value,
            RoleType::LEAD_REFUNDS_REQUESTER->value,
        ]);
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            self::FIELD_ITEMS                                                  => 'required|array',
            self::FIELD_ITEMS . '.*.' . self::FIELD_ITEM_PRODUCT_ASSIGNMENT_ID => 'required|exists:' . ProductAssignment::TABLE . ',id',
            self::FIELD_ITEMS . '.*.' . self::FIELD_ITEM_REFUND_REASON         => 'required|string',
            self::FIELD_ITEMS . '.*.' . self::FIELD_ITEM_REFUND_TYPE           => 'required|string',
            self::FIELD_ITEMS . '.*.' . self::FIELD_ITEM_COST                  => 'required|numeric',
        ];
    }
}
