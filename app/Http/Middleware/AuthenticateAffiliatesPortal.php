<?php

namespace App\Http\Middleware;

use Closure;
use Exception;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class AuthenticateAffiliatesPortal
{
    /**
     * @param Request $request
     * @param Closure $next
     * @return Response
     * @throws Throwable
     */
    public function handle(Request $request, Closure $next): Response
    {
        //TODO should this be hashed?
        throw_if(request()->bearerToken() !== config('services.affiliates_portal_api.token'), new Exception("Invalid token"));

        return $next($request);
    }
}
