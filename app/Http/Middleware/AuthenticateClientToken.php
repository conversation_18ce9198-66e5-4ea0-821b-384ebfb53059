<?php

namespace App\Http\Middleware;

use App\Models\ClientToken;
use App\Models\ClientTokenService;
use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AuthenticateClientToken
{
    const string INTEGRATION_TOKEN = 'x-integration-token';
    /**
     * @param Request $request
     * @param Closure $next
     * @param string $serviceKey
     * @return mixed
     */
    public function handle(Request $request, Closure $next, string $serviceKey): mixed
    {
        $clientToken = ClientToken::query()
            ->whereHas(ClientToken::RELATION_CLIENT_TOKEN_SERVICE, function ($query) use ($serviceKey) {
            $query->where(ClientTokenService::FIELD_SERVICE_KEY, $serviceKey);
        })->first()?->client_token;

        $incomingToken = $request->header(self::INTEGRATION_TOKEN);

        if (!$clientToken || $clientToken !== $incomingToken) {
            return new JsonResponse((['message' => 'Unauthorized integration request.']), 401);
        }

        return $next($request);
    }
}
