<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpFoundation\Response;

class RateLimitMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param Closure(Request): (Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $key = $request->ip();
        if (RateLimiter::tooManyAttempts($key, 5, 60)) {
            return new Response('Too many attempts.', 429);
        }

        RateLimiter::increment($key, 60, 1);
        return $next($request);
    }
}
