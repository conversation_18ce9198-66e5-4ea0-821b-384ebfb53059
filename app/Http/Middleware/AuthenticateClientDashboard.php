<?php

namespace App\Http\Middleware;

use App\Models\Legacy\EloquentUser;
use App\Services\Legacy\ClientTokenService;
use Closure;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Validation\UnauthorizedException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;

class AuthenticateClientDashboard
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure(Request): (Response|RedirectResponse) $next
     * @return JsonResponse|Response
     * @throws UnauthorizedException|Exception
     */
    public function handle(Request $request, Closure $next): JsonResponse|Response
    {
        $token = request()->header('X-CLIENT-BEARER');

        if (!is_string($token)) {
            throw new Exception('X-CLIENT-BEARER must be a string. ' . gettype($token) . ' given.');
        }

        $clientUserToken = app(ClientTokenService::class)->validate($token);
        if ($clientUserToken === false) {
            throw new UnauthorizedException('Unauthorized.');
        }

        // make global user
        app()->singleton(EloquentUser::class, function () use ($clientUserToken) {
            return EloquentUser::query()->find($clientUserToken->getUserId());
        });

        if (!app(EloquentUser::class)->active()) {
            throw new UnauthorizedException('Unauthorized.');
        }

        if ($clientUserToken->getAdminId()) {
            /** @var EloquentUser $adminUser */
            $adminUser = EloquentUser::query()->find($clientUserToken->getAdminId());

            if (!$adminUser || !$adminUser->active()) {
                throw new UnauthorizedException('Unauthorized.');
            }
        }

        return $next($request);
    }
}
