<?php

namespace App\Http\Middleware;

use App\Enums\Cookie;
use App\Models\User;
use App\Repositories\ActivityLog\ActivityLogRepository;
use App\Services\ImpersonateService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Spatie\Activitylog\Models\Activity;

class PiiDataLogger
{
    const string PII_REQUEST_LOG_NAME = "pii-data-reveal";
    const string PII_REQUEST_LOG_DESC = "User requested to see pii data";

    public function __construct(protected ActivityLogRepository $activityLogRepository)
    {
    }

    /**
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): mixed
    {
        $hasPiiToken = $request->cookie(Cookie::PII_TOKEN->value);

        if ($hasPiiToken) {
            $this->activityLogRepository->createActivityLog(
                logName: self::PII_REQUEST_LOG_NAME,
                description: self::PII_REQUEST_LOG_DESC,
                subjectType: $request->fullUrl(),
                properties: ['request_params'        => $request->all()],
            );
        }

        return $next($request);
    }
}
