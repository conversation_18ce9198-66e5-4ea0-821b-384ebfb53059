<?php

namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class Verify2FAMiddleware
{
    const SESSION_KEY = 'valid_2fa';

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if(str_contains($request->route()?->getName(), '2fa.'))
            return $next($request);

        /** @var User|null $user */
        $user = Auth::user();

        if($user !== null && ($user->force_two_factor_auth || $user->verified_2fa) && !Session::has('impersonator_id')) {
            if($user->force_two_factor_auth && $user->verified_2fa == false) {
                return response()->redirectTo('/2fa/setup');
            }

            if(!$request->session()->has('valid_2fa') || !boolval($request->session()->get(self::SESSION_KEY, false))) {
                return response()->redirectTo('/2fa/verify');
            }
        }

        return $next($request);
    }
}
