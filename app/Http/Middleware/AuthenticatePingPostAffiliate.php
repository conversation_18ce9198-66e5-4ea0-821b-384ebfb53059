<?php

namespace App\Http\Middleware;

use App\Http\Requests\PingPostAffiliatesCreateLeadRequest;
use App\Services\Odin\PingPostAffiliateService;
use Closure;
use Exception;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\UnauthorizedException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class AuthenticatePingPostAffiliate
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure(Request): (Response|RedirectResponse) $next
     * @return JsonResponse|Response
     * @throws UnauthorizedException|Exception
     */
    public function handle(Request $request, Closure $next): JsonResponse|Response
    {
        // Check API Key
        $key = request()->header('X-Api-Key');

        if (!is_string($key)) {
            return response()->json([
                'status'    => false,
                'message'   => 'X-Api-Key must be a string. ' . gettype($key) . ' given.'
            ], 403);
        }

        $service = app(PingPostAffiliateService::class);
        $apiKey = $service->findApiKey($key);
        if (!$apiKey) {
            return response()->json([
                'status'    => false,
                'message'   => 'Invalid API key'
            ], 403);
        }

        // Validate Request
        $validator = Validator::make($request->all(), $service->getRequestRules($apiKey->id));
        if ($validator->fails()) {
            return new JsonResponse([
                'status'    => false,
                'message'   => $validator->errors(),
            ], 422);
        }

        $request->attributes->add([PingPostAffiliatesCreateLeadRequest::API_KEY_ID => $apiKey->id]);

        return $next($request);
    }
}
