<?php

namespace App\Http\Middleware;

use App\Models\User;
use App\Services\ImpersonateService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Spatie\Activitylog\Facades\CauserResolver;
use Symfony\Component\HttpFoundation\Response;

class ActivityLogCauser
{
    /**
     * Handle an incoming request.
     *
     * @param Closure(Request): (Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (Session::has(ImpersonateService::SESSION_KEY)) {
            /** @var User|null $user */
            $user = User::query()->find(Session::get(ImpersonateService::SESSION_KEY));

            if ($user) {
                CauserResolver::setCauser($user);
            }
        }

        return $next($request);
    }
}
