<?php

namespace App\Http\Middleware\CompanyRegistration;

use App\Models\Odin\CompanyUser;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Validation\UnauthorizedException;

class HasValidatedRegistrationReference
{
    const REQUEST_USER_REFERENCE        = 'user_reference';

    /**
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): mixed
    {
        $userReference = $request->get(self::REQUEST_USER_REFERENCE);
        $user = $userReference
            ? CompanyUser::query()->where(CompanyUser::FIELD_REFERENCE, $userReference)->first()
            : null;
        $validated = $user && ($user->{CompanyUser::FIELD_PHONE_VERIFIED_AT} !== null);

        if ($validated) {
            return $next($request);
        }
        else if ($user) {
            throw new UnauthorizedException('User has not been validated.');
        }
        else {
            throw new UnauthorizedException('Unauthorized request, or invalid User.');
        }
    }
}
