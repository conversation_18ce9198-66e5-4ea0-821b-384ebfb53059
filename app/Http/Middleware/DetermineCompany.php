<?php

namespace App\Http\Middleware;

use App\Repositories\Odin\CompanyRepository;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class DetermineCompany
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $repository = app(CompanyRepository::class);

        $parameter = $request->route('id');

        if ($request->boolean('by_name') || !is_numeric($parameter)) {
            return $next($request->merge(['redirect_id' => $repository->getCompanyByNameOrFail($parameter)->id]));
        }

        return $next($request->merge(['company' => $repository->findOrFail($parameter)]));
    }
}
