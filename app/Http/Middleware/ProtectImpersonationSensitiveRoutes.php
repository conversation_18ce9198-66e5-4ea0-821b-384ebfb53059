<?php

namespace App\Http\Middleware;

use App\Services\ImpersonateService;
use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Nette\Schema\ValidationException;

class ProtectImpersonationSensitiveRoutes
{
    /**
     * @param Request $request
     * @param Closure $next
     * @param string|null $throwError
     * @return mixed
     */
    public function handle(Request $request, Closure $next, ?string $throwError = null): mixed
    {
        if (!Session::has(ImpersonateService::SESSION_KEY)) {
            return $next($request);
        }

        if(!isset($throwError)) {
            return response()->redirectTo('/');
        }

        return new JsonResponse((['error' => 'Cannot access route while impersonating']), 403);
    }
}
