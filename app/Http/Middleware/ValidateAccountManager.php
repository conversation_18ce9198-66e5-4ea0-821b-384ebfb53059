<?php

namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\UnauthorizedException;

class ValidateAccountManager
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure(Request): (Response|RedirectResponse) $next
     * @return JsonResponse|Response
     * @throws UnauthorizedException
     */
    public function handle(Request $request, Closure $next): JsonResponse|Response
    {
        if(empty(Auth::user()->{User::RELATION_ACCOUNT_MANAGER})) {
            throw new UnauthorizedException('Unauthorized.');
        }

        return $next($request);
    }
}
