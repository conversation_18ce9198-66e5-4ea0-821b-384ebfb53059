<?php

namespace App\Http\Middleware\Appointments;

use App\Http\Controllers\API\Appointments\AppointmentController;
use App\Models\AppointmentDelivery;
use App\Models\Odin\Address;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAppointment;
use App\Models\Odin\ProductAssignment;
use Carbon\Carbon;
use Closure;
use Illuminate\Http\Request;

class CheckAppointmentCancellable
{
    /**
     * @param Request $request
     * @param Closure $next
     * @return mixed|void
     */
    public function handle(Request $request, Closure $next)
    {
        $apptDelivery = AppointmentDelivery::query()
            ->where(AppointmentDelivery::FIELD_CONSUMER_TOKEN, $request->get(AppointmentController::REQUEST_APPOINTMENT_KEY))
            ->with([
                AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT.'.'.ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_APPOINTMENT => function($has) {
                    $has->where(ProductAppointment::ALLOCATED_AS_LEAD, false);
                },
                AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT.'.'.ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_ADDRESS
            ])
            ->whereHas(AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT, function($has) {
                $has
                    ->where(ProductAssignment::FIELD_CHARGEABLE, true)
                    ->where(ProductAssignment::FIELD_DELIVERED, true);
            })
            ->whereDoesntHave(AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT.'.'.ProductAssignment::RELATION_PRODUCT_CANCELLATIONS)
            ->whereDoesntHave(AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT.'.'.ProductAssignment::RELATION_PRODUCT_REJECTIONS)
            ->first();

        if(empty($apptDelivery)) {
            exit;
        }

        $consumerProduct = $apptDelivery->{AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT}->{ProductAssignment::RELATION_CONSUMER_PRODUCT};

        $appointment = $consumerProduct->{ConsumerProduct::RELATION_APPOINTMENT};

        $tzOffset = (string) $consumerProduct->{ConsumerProduct::RELATION_ADDRESS}->{Address::FIELD_UTC};

        $apptTime = Carbon::createFromFormat('Y-m-d H:i:s', $appointment->{ProductAppointment::APPOINTMENT}, $tzOffset);

        if(Carbon::now($tzOffset)->greaterThan($apptTime)) {
            exit;
        }

        return $next($request);
    }
}
