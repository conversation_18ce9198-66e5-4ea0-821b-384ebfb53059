<?php

namespace App\Http\Middleware\Appointments;

use App\Http\Controllers\API\Appointments\AppointmentController;
use App\Models\AppointmentDelivery;
use App\Models\Odin\Address;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAppointment;
use App\Models\Odin\ProductAssignment;
use Carbon\Carbon;
use Closure;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class AuthenticateConsumerAppointment
{
    /**
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $apptKey = $request->get(AppointmentController::REQUEST_APPOINTMENT_KEY);
        $apptCode = $request->get(AppointmentController::REQUEST_APPOINTMENT_CODE);

        $apptDelivery = AppointmentDelivery::query()
            ->with([
                AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT.'.'.ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_APPOINTMENT,
                AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT.'.'.ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_ADDRESS
            ])
            ->where(AppointmentDelivery::FIELD_CONSUMER_TOKEN, $apptKey)
            ->where(AppointmentDelivery::FIELD_CONSUMER_CODE, $apptCode)
            ->firstOrFail();

        $appointment = $apptDelivery->{AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT}->{ProductAssignment::RELATION_CONSUMER_PRODUCT}->{ConsumerProduct::RELATION_APPOINTMENT};

        if(empty($appointment)) {
            throw new ModelNotFoundException();
        }

        $tzOffset = (string) $apptDelivery->{AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT}->{ProductAssignment::RELATION_CONSUMER_PRODUCT}->{ConsumerProduct::RELATION_ADDRESS}->{Address::FIELD_UTC};

        $appointmentDateTime = Carbon::createFromFormat('Y-m-d H:i:s', $appointment->{ProductAppointment::APPOINTMENT}, $tzOffset);

        if(Carbon::now($tzOffset)->greaterThan($appointmentDateTime)) {
            throw new ModelNotFoundException("Appointment expired");
        }

        return $next($request);
    }
}
