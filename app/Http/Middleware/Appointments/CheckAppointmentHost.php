<?php

namespace App\Http\Middleware\Appointments;

use Closure;
use Illuminate\Http\Request;

class CheckAppointmentHost
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if($request->getScheme()."://".$request->getHost() !== config('app.solarreviews_domain.appointments')) {
            exit;
        }

        return $next($request);
    }
}
