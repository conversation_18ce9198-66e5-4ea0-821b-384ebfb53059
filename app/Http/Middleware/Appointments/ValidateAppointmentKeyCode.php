<?php

namespace App\Http\Middleware\Appointments;

use App\Http\Controllers\API\Appointments\AppointmentController;
use Closure;
use Illuminate\Http\Request;
use Validator;

class ValidateAppointmentKeyCode
{
    /**
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $apptKey = $request->get(AppointmentController::REQUEST_APPOINTMENT_KEY);
        $apptCode = $request->get(AppointmentController::REQUEST_APPOINTMENT_CODE);

        $validator = Validator::make(
            [
                AppointmentController::REQUEST_APPOINTMENT_KEY => $apptKey,
                AppointmentController::REQUEST_APPOINTMENT_CODE => $apptCode
            ],
            [
                AppointmentController::REQUEST_APPOINTMENT_KEY => ['required', 'uuid', 'size:36'],
                AppointmentController::REQUEST_APPOINTMENT_CODE => ['required', 'string', 'size:6', 'regex:/^[0-9]+$/']
            ]
        );

        if($validator->fails()) {
            abort(401);
        }

        return $next($request);
    }
}
