<?php

namespace App\Http\Middleware;

use App\Enums\GlobalConfigurationKey;
use App\Repositories\GlobalConfiguration\GlobalConfigurationRepository;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Validation\UnauthorizedException;
use Symfony\Component\HttpFoundation\Response;

class AuthenticateWithBearerToken
{
    /**
     * Handle an incoming request.
     *
     * @param Closure(Request): (Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!$this->authorized($request)) {
            throw new UnauthorizedException();
        }

        return $next($request);
    }

    protected function authorized(Request $request): bool
    {
        // Token validation is currently based on the global config.
        // TODO: Investigate alternative token management strategies.

        /** @var GlobalConfigurationRepository $repository */
        $repository = app(GlobalConfigurationRepository::class);
        $config = $repository->getConfigurationPayload(GlobalConfigurationKey::LEAD_REQUEST_AUTHORIZATION_TOKENS);
        $bearerToken = $request->bearerToken();

        if (!$config || !$bearerToken) {
            return false;
        }

        return $config->data->some(fn($token) => $token === $bearerToken);
    }
}
