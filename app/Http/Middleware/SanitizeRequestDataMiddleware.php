<?php

namespace App\Http\Middleware;

use App\Helpers\Sanitizer;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SanitizeRequestDataMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param Closure(Request): (Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $requestData = $request->all();
        $sanitizedData = $this->sanitizeData($requestData);
        $request->replace($sanitizedData);

        return $next($request);
    }

    /**
     * @param array $data
     * @return array
     */
    private function sanitizeData(array $data): array
    {
        foreach ($data as $key => $value) {
            $data[$key] = $this->sanitizeValue($value);
        }
        return $data;
    }

    /**
     * @param $value
     * @return mixed|string|null
     */
    private function sanitizeValue($value): mixed
    {
        if (is_string($value)) {
            if (filter_var($value, FILTER_VALIDATE_EMAIL)) {
                return Sanitizer::sanitizeEmail($value);
            }
            return Sanitizer::sanitizeString($value);
        } elseif (is_numeric($value)) {
            return Sanitizer::sanitizeNumber($value);
        } elseif (is_null($value)) {
            return null;
        }
        return $value;
    }
}
