<?php

namespace App\Http\Middleware;

use App\Models\Billing\Invoice;
use App\Models\Odin\CompanyUser;
use App\Models\Scopes\ExcludeDeletedScope;
use App\Models\Scopes\ExcludeDraftScope;
use App\Models\User;
use App\Services\Dashboard\DashboardAuthService;
use App\Services\Dashboard\JWT\DashboardJWTService;
use App\Services\Dashboard\JWT\DashboardTokenModel;
use Closure;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Validation\UnauthorizedException;

class DashboardApiAuth
{
    const FIELD_BEARER = 'X-CLIENT-BEARER';

    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure(Request): (Response|RedirectResponse) $next
     * @return Response|RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $token = $request->header(self::FIELD_BEARER, null);
        $service = $this->getJWTService();

        if (!$service->validate($token))
            throw new UnauthorizedException("Unauthorized.");

        $model = $service->decode($token);

        $this->validateUserIsActive($model)
            ->validateShadowerIsAllowed($model)
            ->applyAuth($model);

        $this->getAuthService()->setActioningUser();

        return $next($request);
    }

    /**
     * Helper function to get service.
     *
     * @return DashboardJWTService
     */
    protected function getJWTService(): DashboardJWTService
    {
        /** @var DashboardJWTService $service */
        $service = app()->make(DashboardJWTService::class);

        return $service;
    }

    /**
     * Helper function to get service.
     *
     * @return DashboardAuthService
     */
    protected function getAuthService(): DashboardAuthService
    {
        /** @var DashboardAuthService $service */
        $service = app()->make(DashboardAuthService::class);

        return $service;
    }

    protected function validateUserIsActive(DashboardTokenModel $model): self
    {
        /** @var CompanyUser|null $user */
        $user = CompanyUser::query()->find($model->getUserId());

        if (!$user || $user->status !== CompanyUser::STATUS_ACTIVE)
            throw new UnauthorizedException("Unauthorized");

        return $this;
    }

    protected function validateShadowerIsAllowed(DashboardTokenModel $model): self
    {
        if ($model->getShadowerId() !== null) {
            /** @var User|null $user */
            $user = User::query()->find($model->getShadowerId());

            // TODO: Check permissions.
            if (!$user)
                throw new UnauthorizedException("Unauthorized shadower");
        }

        return $this;
    }

    protected function applyAuth(DashboardTokenModel $model): self
    {
        $service = $this->getAuthService();

        $service->setUserId($model->getUserId())
            ->setShadowerId($model->getShadowerId())
            ->setToken($model);

        return $this;
    }
}
