<?php

namespace App\Http\Middleware\Odin;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Validation\UnauthorizedException;

class OdinResourceApiMiddleware
{
    /**
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): mixed
    {
        $token = base64_decode(preg_split("/\s/", $request->header('Authorization', ''))[1]);

        if(config('services.legacy_admin.auth_token') !== $token) {
            throw new UnauthorizedException('Unauthorized.');
        }

        return $next($request);
    }
}
