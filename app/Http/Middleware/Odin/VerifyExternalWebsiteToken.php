<?php

namespace App\Http\Middleware\Odin;

use App\Models\Odin\WebsiteApiKeyOrigin;
use Closure;
use App\Models\Odin\Website;
use App\Models\Odin\WebsiteApiKey;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\UnauthorizedException;

class VerifyExternalWebsiteToken
{
    const AUTH_HEADER    = 'Authorization';
    const ORIGIN_HEADER  = 'origin';

    /**
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): mixed
    {
        $requestAuthorizationToken  = $request->header(self::AUTH_HEADER);

        // Rejects if token is empty
        if (is_null($requestAuthorizationToken)
        || (is_string($requestAuthorizationToken)
            && strlen(trim($requestAuthorizationToken)) < 1)
        ) $this->throwUnauthorizedException();

        // Remove Bearer from token
        $cleanToken = preg_replace('/Bearer /i', '', $requestAuthorizationToken);

        /** @var WebsiteApiKey $websiteActiveKeys */
        $websiteApiKey = WebsiteApiKey::query()
            ->where([
                WebsiteApiKey::FIELD_KEY     => trim($cleanToken),
                WebsiteApiKey::FIELD_STATUS  => WebsiteApiKey::STATUS_ACTIVE
            ])
            ->first();

        if (!$websiteApiKey) $this->throwUnauthorizedException();

        $requestOrigin = $request->header(self::ORIGIN_HEADER);

        // Check if origin is in one of registered domains
        $this->checkOriginHasAccess($requestOrigin, $websiteApiKey);

        /** @var Website $website */
        $website = $websiteApiKey->website;

        // Log request
        try {
            $this->logRequest($request, $requestOrigin, $cleanToken);
        } catch (\Exception $e) {
            logger()->error("Failed to log request. Error: {$e->getMessage()}");
        }

        app()->singleton(Website::class, function () use ($website) {
            return $website;
        });

        return $next($request);
    }

    /**
     * @param string $requestOrigin
     * @param WebsiteApiKey $websiteApiKey
     * @return void
     */
    private function checkOriginHasAccess(string $requestOrigin, WebsiteApiKey $websiteApiKey): void
    {
        $websiteApiOrigins = $websiteApiKey->origins->pluck(WebsiteApiKeyOrigin::FIELD_ORIGIN)->toArray();

        // If website api key includes * wildcard it will allow all origins
        if (in_array('*', $websiteApiOrigins)) return;

        // Check if the current request origin is present in the list of allowed origins
        $allowed = collect($websiteApiOrigins)->contains(function ($origin) use ($requestOrigin) {
            return Str::is($origin, $requestOrigin);
        });

        if (!$allowed) $this->throwUnauthorizedException();
    }

    /**
     * @param Request $request
     * @param string $requestOrigin
     * @param string $cleanToken
     * @return void
     */
    private function logRequest(Request $request, string $requestOrigin, string $cleanToken): void
    {
        logger()->info(json_encode([
            'host'                  => $request->host(),
            'httpHost'              => $request->httpHost(),
            'schemeAndHttpHost'     => $request->schemeAndHttpHost(),
            'method'                => $request->method(),
            'ip'                    => $request->ip(),
            'fullUrl'               => $request->fullUrl(),
            'url'                   => $request->url(),
            'origin'                => $requestOrigin,
            'websiteApiToken'       => $cleanToken
        ]));
    }

    /**
     * @return void
     */
    protected function throwUnauthorizedException(): void
    {
        throw new UnauthorizedException("The request made is unauthorized.");
    }
}
