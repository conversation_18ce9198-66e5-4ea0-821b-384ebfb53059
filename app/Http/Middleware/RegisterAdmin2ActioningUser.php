<?php

namespace App\Http\Middleware;

use App\Enums\ActioningUserType;
use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

/**
 * For internal A2 requests, the actioning user will be the standard Auth logged in User
 * This is currently only active on future campaign routes in internal-api
 */
class RegisterAdmin2ActioningUser
{
    public function handle(Request $request, Closure $next)
    {
        /** @var User $user */
        $user = Auth::user();
        if ($user) {
            Auth::setActioningUserType(ActioningUserType::ADMIN2_USER);
            Auth::setActioningUserId($user->id);
            Auth::setActioningUserLegacyId($user->legacy_user_id);
        }

        return $next($request);
    }
}
