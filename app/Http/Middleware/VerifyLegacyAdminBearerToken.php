<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Validation\UnauthorizedException;

class VerifyLegacyAdminBearerToken
{
    public function handle(Request $request, Closure $next)
    {
        $token = base64_decode(preg_split("/\s/", $request->header('Authorization', ''))[1]);

        if(config('services.legacy_admin.auth_token') !== $token) {
            throw new UnauthorizedException('Unauthorized.');
        }

        return $next($request);
    }
}
