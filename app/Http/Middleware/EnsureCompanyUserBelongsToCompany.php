<?php

namespace App\Http\Middleware;

use App\Models\Odin\CompanyUser;
use App\Services\Dashboard\DashboardAuthService;
use Closure;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Http\Request;

class EnsureCompanyUserBelongsToCompany
{
    public function __construct(protected DashboardAuthService $dashboardAuthService)
    {
    }


    public function handle(Request $request, Closure $next)
    {
        $companyId = $request->route('companyId');
        $companyUserId = $this->dashboardAuthService->getUserId();

        $belongs = CompanyUser::query()
            ->where(CompanyUser::FIELD_ID, $companyUserId)
            ->where(CompanyUser::FIELD_COMPANY_ID, $companyId)
            ->exists();

        if ($belongs)  {
            return $next($request);
        }

        throw new AuthenticationException("You don't belong to this company");
    }
}
