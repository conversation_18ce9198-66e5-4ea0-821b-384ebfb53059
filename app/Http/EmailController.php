<?php

namespace App\Http;

use App\Enums\EmailTemplateType;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Emails\SendEmailRequest;
use App\Http\Requests\Emails\UpdateEmailTemplateRequest;
use App\Http\Requests\Prospects\SendProspectingEmailRequest;
use App\Jobs\Emails\SendGenericEmailJob;
use App\Mail\SendGenericEmail;
use App\Models\Email;
use App\Models\EmailTemplate;
use App\Models\Odin\CompanyUser;
use App\Models\Prospects\NewBuyerProspect;
use App\Models\User;
use App\Repositories\Mailbox\MailboxUserTokenRepository;
use App\Services\Emails\EmailContactService;
use App\Services\Emails\EmailContentBuilderService;
use App\Services\EmailTemplates\EmailTemplateService;
use App\Services\ImpersonateService;
use App\Services\Mailbox\MailboxApiService;
use App\Services\Shortcode\ShortcodeImplementation\PersonalEmailTemplateShortcodeUseCase;
use App\Services\Shortcode\ShortcodeImplementation\ProspectShortcodeUseCase;
use App\Services\Shortcode\ShortcodeReplacerService;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use ReflectionException;

class EmailController extends APIController
{
    const string REQUEST_CONTENT        = 'content';
    const string REQUEST_SUBJECT        = 'subject';
    const string RESPONSE_SHORTCODES    = 'shortcodes';
    const string RESPONSE_TEMPLATES     = 'templates';
    const string RESPONSE_TEMPLATE      = 'template';

    /**
     * @param int $emailTemplateType
     * @param EmailTemplateService $emailTemplateService
     * @param EmailContactService $emailContactService
     * @return JsonResponse
     */
    public function getEmailContent(
        int $emailTemplateType,
        EmailTemplateService $emailTemplateService,
        EmailContactService $emailContactService
    ): JsonResponse
    {
        /** @var User $user */
        $user = auth()->user();
        $emailTemplateType = EmailTemplateType::tryFrom($emailTemplateType);

        $contacts = $emailContactService->getEmailContacts(
            excludeEmail: auth()->user()->email
        );

        return response()->json([
            self::REQUEST_CONTENT     => $emailTemplateType->getDefaultEmailTemplateMarkdown(),
            self::REQUEST_SUBJECT     => $emailTemplateType->getDefaultEmailTemplateSubject(),
            self::RESPONSE_SHORTCODES => $emailTemplateType->getEmailTemplateShortcodeService(),
            self::RESPONSE_TEMPLATES  => $emailTemplateService->getUsersEmailTemplatesForTemplateType($user, $emailTemplateType),
            'contacts'                => $contacts,
        ]);
    }

    /**
     * @param int $emailTemplateType
     * @param UpdateEmailTemplateRequest $request
     * @param EmailTemplateService $templateService
     *
     * @return JsonResponse
     */
    public function saveEmailTemplate(int $emailTemplateType, UpdateEmailTemplateRequest $request, EmailTemplateService $templateService): JsonResponse
    {
        /** @var User $user */
        $user       = auth()->user();
        $emailTemplateType = EmailTemplateType::tryFrom($emailTemplateType);
        $templateId = $request->validated(UpdateEmailTemplateRequest::REQUEST_TEMPLATE_ID);

        //todo consolidate the update and create functions into one
        if ($templateId) {
            $templateService->updateTemplate(
                id: $templateId,
                ownerUserId: $user->id,
                name: $request->validated(UpdateEmailTemplateRequest::REQUEST_TEMPLATE_NAME),
                subject: $request->validated(UpdateEmailTemplateRequest::REQUEST_SUBJECT),
                content: $request->validated(UpdateEmailTemplateRequest::REQUEST_CONTENT),
                personal: true,
                type: $emailTemplateType->value,
                active: true
            );

            return response()->json();
        }

        return response()->json([
            self::RESPONSE_TEMPLATE => $templateService->createTemplate(
                ownerUserId: $user->id,
                name: $request->validated(UpdateEmailTemplateRequest::REQUEST_TEMPLATE_NAME),
                subject: $request->validated(UpdateEmailTemplateRequest::REQUEST_SUBJECT),
                content: $request->validated(UpdateEmailTemplateRequest::REQUEST_CONTENT),
                personal: true,
                type: $emailTemplateType->value,
                active: true
                )->only([
                    EmailTemplate::FIELD_ID,
                    EmailTemplate::FIELD_NAME,
                    EmailTemplate::FIELD_SUBJECT,
                    EmailTemplate::FIELD_CONTENT
                ])
        ]);
    }

    /**
     * @param int $emailTemplateType
     * @param SendEmailRequest $request
     * @param ShortcodeReplacerService $replacerService
     * @return JsonResponse
     * @throws ReflectionException
     * @throws Exception
     */
    public function getEmailPreview(int $emailTemplateType, SendEmailRequest $request, ShortcodeReplacerService $replacerService, EmailContentBuilderService $emailContentBuilderService): JsonResponse
    {
        /** @var User $user */
        $user = auth()->user();
        $emailTemplateType = EmailTemplateType::tryFrom($emailTemplateType);

        $shortcodeMap = $this->getShortcodeMapForTemplateType($emailTemplateType, $request->validated(SendEmailRequest::RECIPIENT_ID), $user);
        $subject      = $replacerService->process($request->validated(self::REQUEST_SUBJECT, '') ?? '', $shortcodeMap);
        $content = $emailContentBuilderService->buildContent(
            user        : $user,
            content     : $request->validated(self::REQUEST_CONTENT),
            shortcodeMap: $shortcodeMap
        );


        return response()->json([
            self::REQUEST_CONTENT => (new SendGenericEmail($subject, $user, $content))->render(),
            self::REQUEST_SUBJECT => $request->get(self::REQUEST_SUBJECT),
        ]);
    }

    /**
     * @param int $emailTemplateType
     * @param SendEmailRequest $request
     * @param ShortcodeReplacerService $replacerService
     * @param MailboxUserTokenRepository $mailboxUserTokenRepository
     * @param MailboxApiService $mailboxApiService
     * @param EmailContentBuilderService $emailContentBuilderService
     * @return JsonResponse
     * @throws Exception
     */
    public function sendEmail(
        int $emailTemplateType,
        SendEmailRequest $request,
        ShortcodeReplacerService $replacerService,
        MailboxUserTokenRepository $mailboxUserTokenRepository,
        MailboxApiService $mailboxApiService,
        EmailContentBuilderService $emailContentBuilderService
    ): JsonResponse
    {
        /** @var User $user */
        $user = auth()->user();
        $emailTemplateType = EmailTemplateType::tryFrom($emailTemplateType);

        $shortcodeMap = $this->getShortcodeMapForTemplateType($emailTemplateType, $request->validated(SendEmailRequest::RECIPIENT_ID), $user);
        $subject      = $replacerService->process($request->validated(self::REQUEST_SUBJECT, '') ?? '', $shortcodeMap);
        $content = $emailContentBuilderService->buildContent(
            user        : $user,
            content     : $request->validated(self::REQUEST_CONTENT),
            shortcodeMap: $shortcodeMap
        );

        $userMailBoxToken = $mailboxUserTokenRepository->getLatestUserEmailToken($user);

        //todo gmail strips out all the formatting
        if($userMailBoxToken) {
            // Parse markdown if it's not html
            $content = Str::contains($content, '<html>') ? $content : Str::markdown($content);

            $mailboxApiService->sendEmail(
                from: $user,
                to: [$request->validated(SendEmailRequest::EMAIL)],
                subject: $subject,
                content: $content,
                bcc: $request->validated(SendProspectingEmailRequest::BCC, []),
                cc: $request->validated(SendProspectingEmailRequest::CC, []),
            );
        }
        else {
            SendGenericEmailJob::dispatch(
                $request->validated(SendEmailRequest::EMAIL),
                $subject,
                $user,
                $content,
                $request->validated(SendProspectingEmailRequest::CC, []),
                $request->validated(SendProspectingEmailRequest::BCC, [])
            );

            // create email record - will create activity feed from listener
            Email::create([
                Email::FIELD_TO_ADDRESS => $request->validated(SendEmailRequest::EMAIL),
                Email::FIELD_FROM_ADDRESS => $user->email,
                Email::FIELD_SUBJECT => $subject,
                Email::FIELD_BODY => $content,
                Email::FIELD_TO_COMPANY_USER_ID => $request->validated(SendEmailRequest::RECIPIENT_ID),
                Email::FIELD_FROM_USER_ID => $user->id,
            ]);

            return response()->json([
                'message' => "You mailbox is not synced and so this email will not appear in your sent items. Please sync your mailbox."
            ]);
        }

        return response()->json();
    }


    /**
     * Build the shortcode map for each template type
     *
     * @param EmailTemplateType $emailTemplateType
     * @param mixed $recipientId
     * @param User $user
     * @return array
     */
    protected function getShortcodeMapForTemplateType(EmailTemplateType $emailTemplateType, mixed $recipientId, User $user): array
    {
        switch($emailTemplateType) {
            case EmailTemplateType::BDM_PROSPECTING_EMAIL:
                $newBuyerProspect = NewBuyerProspect::query()->where(NewBuyerProspect::FIELD_EXTERNAL_REFERENCE, $recipientId)->firstOrFail();
                return (new ProspectShortcodeUseCase($newBuyerProspect, $newBuyerProspect->user ?? $user))->compile()->toArray();
            case EmailTemplateType::STATUS_PERSONAL:
                $companyUser = CompanyUser::findOrFail($recipientId);
                return (new PersonalEmailTemplateShortcodeUseCase($companyUser, $user))->compile()->toArray();
            default:
                return [];
        }
    }
}
