<?php

namespace App\Providers;

use Illuminate\Support\Collection;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Str;
use Illuminate\Support\Stringable;

class MacroServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        Str::macro('base64UrlEncode', function(string $value) {
            return rtrim(strtr(base64_encode($value), '+/', '-_'), '=');
        });

        Stringable::macro('base64UrlEncode', function() {
            return new Stringable(rtrim(strtr(base64_encode($this->value), '+/', '-_'), '='));
        });

        Str::macro('formatPhoneNumber', function(string $value) {
            return preg_replace('/\D/', '', str_replace('+1', '', $value));
        });

        Stringable::macro('formatPhoneNumber', function() {
            return preg_replace('/\D/', '', str_replace('+1', '', $this->value));
        });

        Collection::macro('recursiveToArray', function() {
            $walk = function(Collection $in) use (&$walk) {
                $out = [];

                foreach($in as $key => $value) {
                    if($value instanceof Collection) {
                        $out[$key] = $walk($value);
                    }
                    else if(is_array($value)) {
                        $out[$key] = $walk(collect($value));
                    }
                    else {
                        $out[$key] = $value;
                    }
                }

                return $out;
            };

            return $walk($this);
        });
    }
}
