<?php

namespace App\Providers;

use App\Services\Workflows\WorkflowProcessingService;
use App\Visitors\AssignAccountManagerVisitor;
use App\Visitors\CreateTaskVisitor;
use Illuminate\Support\ServiceProvider;
use App\Visitors\HandlesNotificationVisitor;

class WorkflowVisitorProvider extends ServiceProvider
{
    const VISITORS = [
        HandlesNotificationVisitor::class,
        CreateTaskVisitor::class,
        AssignAccountManagerVisitor::class,
    ];
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->bind(WorkflowProcessingService::class, function() {
            return new WorkflowProcessingService(self::VISITORS);
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
