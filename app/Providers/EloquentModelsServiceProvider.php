<?php

namespace App\Providers;

use App\Enums\ActivityType;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Enums\Campaigns\CampaignExternalRelationType;
use App\Models\Action;
use App\Models\Call;
use App\Models\Email;
use App\Models\GoogleAdsGeoTarget;
use App\Models\Legacy\LeadCampaign;
use App\Models\Mailbox\MailboxEmail;
use App\Models\Sales\Task;
use App\Models\Text;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\ServiceProvider;

class EloquentModelsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot(): void
    {
        Relation::morphMap([
            //Advertising locations
            AdvertisingPlatform::GOOGLE->value => GoogleAdsGeoTarget::class,

            //Activity Feeds
            ActivityType::ACTION->value        => Action::class,
            ActivityType::CALL->value          => Call::class,
            ActivityType::TEXT->value          => Text::class,
            ActivityType::EMAIL->value         => Email::class,
            ActivityType::TASK->value          => Task::class,
            ActivityType::MAILBOX_EMAIL->value => MailboxEmail::class,

            CampaignExternalRelationType::LEGACY_LEAD_CAMPAIGN->value => LeadCampaign::class
        ]);
    }
}
