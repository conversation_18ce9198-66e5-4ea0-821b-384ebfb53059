<?php

namespace App\Providers;

use App\Contracts\Billing\PaymentGatewayEventInterpreterContract;
use App\Contracts\Campaigns\BidModificationStrategyContract;
use App\Contracts\Campaigns\ViableBudgetStrategyContract;
use App\Contracts\ProductAssignment\MultiProductAssignmentStrategyContract;
use App\Contracts\ProductAssignment\UnderSoldStrategyContract;
use App\Contracts\Repositories\BestRevenueRecordingRepositoryContract;
use App\Contracts\Repositories\QuoteCompanyRepositoryContract;
use App\Contracts\Repositories\QuoteRepositoryContract;
use App\Contracts\Repositories\ReviewRepositoryContract;
use App\Contracts\Repositories\SuperPremiumLimitsRepositoryContract;
use App\Contracts\Repositories\ZipCodeSetRepositoryContract;
use App\Contracts\Services\Campaigns\LeadCampaignAvailabilityServiceContract;
use App\Contracts\Services\Communication\CommunicationContract;
use App\Contracts\Services\CompanyMetricsServiceContract;
use App\Contracts\Services\LeadAggregatorsServiceContract;
use App\Contracts\Services\LeadDeliveryServiceContract;
use App\Enums\ActioningUserType;
use App\Contracts\Services\TaxServiceApiContract;
use App\Enums\CompanyMetrics\CompanyMetricSources;
use App\Enums\Odin\API\FieldClassification;
use App\Repositories\Legacy\BestRevenueRecordingRepository;
use App\Repositories\Legacy\QuoteCompanyRepository;
use App\Repositories\Legacy\QuoteRepository;
use App\Repositories\Legacy\ReviewRepository;
use App\Repositories\Legacy\SuperPremiumLimitsRepository;
use App\Repositories\Legacy\ZipCodeSetRepository;
use App\Services\Affiliate\PayoutService;
use App\Services\Affiliate\PayoutStrategyService;
use App\Services\Billing\DummyTaxServiceApi;
use App\Services\Billing\PaymentGateway\StripeEventInterpreter;
use App\Services\Communication\Factory as LeadCommunicationFactory;
use App\Services\Communication\TwilioCommunicationService;
use App\Services\CompanyMetrics\SimilarWebMetricsService;
use App\Services\CompanyMetrics\SpyFuMetricsService;
use App\Services\ConsumerService;
use App\Services\CustomActivityLogger;
use App\Services\Dashboard\DashboardAuthService;
use App\Services\Dashboard\JWT\DashboardJWTService;
use App\Services\Docusign\DocuSignService;
use App\Services\Legacy\Campaigns\LeadCampaignAvailabilityService;
use App\Services\Legacy\LeadAggregatorsService;
use App\Services\Legacy\LeadDeliveryService;
use App\Services\Odin\API\OdinAPIParsingService;
use App\Services\Odin\API\OdinAPIService;
use App\Services\Odin\API\OdinAuthoritativeAPIParsingService;
use App\Services\Odin\API\OdinAuthoritativeAPIService;
use App\Services\Search\CompanySearchService;
use App\Services\Search\Searchables\CompanySearchableService;
use App\Services\Search\Searchables\LeadSearchableService;
use App\Services\Search\Searchables\TaskSearchableService;
use App\Services\Search\SearchService;
use App\Services\Slack\DummySlackNotificationService;
use App\Services\Slack\SlackNotificationService;
use App\Services\Slack\SlackNotificationServiceContract;
use App\Services\Twilio\TwilioVerify;
use App\Strategies\Campaigns\StandardBidModificationStrategy;
use App\Strategies\Campaigns\StandardViableCampaignsStrategy;
use App\Strategies\ProductAssignment\StandardMultiProductAssignmentStrategy;
use App\Strategies\ProductAssignment\StandardUnderSoldStrategy;
use Google\Cloud\Logging\LoggingClient;
use Illuminate\Auth\SessionGuard;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\ServiceProvider;
use Pusher\Pusher;
use Spatie\Activitylog\ActivityLogger;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->bind(LeadCampaignAvailabilityServiceContract::class, LeadCampaignAvailabilityService::class);
        $this->app->bind(QuoteRepositoryContract::class, QuoteRepository::class);
        $this->app->bind(QuoteCompanyRepositoryContract::class, QuoteCompanyRepository::class);
        $this->app->bind(ReviewRepositoryContract::class, ReviewRepository::class);
        $this->app->bind(SuperPremiumLimitsRepositoryContract::class, SuperPremiumLimitsRepository::class);
        $this->app->bind(ZipCodeSetRepositoryContract::class, ZipCodeSetRepository::class);
        $this->app->bind(BestRevenueRecordingRepositoryContract::class, BestRevenueRecordingRepository::class);
        $this->app->bind(LeadAggregatorsServiceContract::class, LeadAggregatorsService::class);
        $this->app->bind(LeadDeliveryServiceContract::class, LeadDeliveryService::class);
        $this->app->bind(MultiProductAssignmentStrategyContract::class, StandardMultiProductAssignmentStrategy::class);
        $this->app->bind(UnderSoldStrategyContract::class, StandardUnderSoldStrategy::class);
        $this->app->bind(ViableBudgetStrategyContract::class, StandardViableCampaignsStrategy::class);
        $this->app->bind(BidModificationStrategyContract::class, StandardBidModificationStrategy::class);
        $this->app->bind(ActivityLogger::class, CustomActivityLogger::class);

        $this->app->bind(CompanyMetricsServiceContract::class, function () {
            return match (config('services.company_metrics.driver')) {
                CompanyMetricSources::SIMILAR_WEB->value    => new SimilarWebMetricsService(),
                default                                     => new SpyFuMetricsService(),
            };
        });

        $this->app->bind(TaxServiceApiContract::class, function () {
            return match (config('services.tax_service.driver')) {//todo: replace when we implement actual service
                default                                     => new DummyTaxServiceApi(),
            };
        });

        // TODO - Create dummy interpreter and enum
        $this->app->bind(PaymentGatewayEventInterpreterContract::class, function () {
            return match (config('services.payment_gateways.driver')) {
                default => app()->make(StripeEventInterpreter::class)
            };
        });

        $this->app->bind(OdinAPIService::class, function() {
            /** @var OdinAPIParsingService $parsingService */
            $parsingService = app()->make(OdinAPIParsingService::class);

            return new OdinAPIService(
                $parsingService,
                FieldClassification::getResourceServices()
            );
        });

        $this->app->bind(OdinAuthoritativeAPIService::class, function() {
            /** @var OdinAuthoritativeAPIParsingService $parsingService */
            $parsingService = app()->make(OdinAuthoritativeAPIParsingService::class);
            $payoutService = app()->make(PayoutService::class);
            $payoutStrategyService = app()->make(PayoutStrategyService::class);

            return new OdinAuthoritativeAPIService(
                $parsingService,
                $payoutService,
                $payoutStrategyService,
                FieldClassification::getResourceServicesV2()
            );
        });

        $this->app->bind(SearchService::class, function() {
            $searchables = collect([
                app()->make(CompanySearchableService::class),
                app()->make(LeadSearchableService::class),
                app()->make(TaskSearchableService::class),
            ]);

            return new SearchService($searchables);
        });

        $this->app->bind(CompanySearchService::class, function() {
            return new CompanySearchService(collect([ app()->make(CompanySearchableService::class)]));
        });

        $this->app->singleton(CommunicationContract::class, function () {
            return LeadCommunicationFactory::make(config('services.communication.driver'));
        });

        $this->app->singleton(TwilioCommunicationService::class, function () {
            return LeadCommunicationFactory::make('twilio');
        });

        $this->app->singleton(DashboardJWTService::class, function() {
            return new DashboardJWTService(
                config('dashboard.jwt.signing_key'),
                config('dashboard.jwt.default_expiry')
            );
        });

        $this->app->singleton(ConsumerService::class, fn() => new ConsumerService(config('services.jwt.signing_keys.consumer_token')));

        $this->app->singleton(DashboardAuthService::class);

        $this->app->singleton(Pusher::class, function () {
            return new Pusher(
                config('broadcasting.connections.pusher.key'),
                config('broadcasting.connections.pusher.secret'),
                config('broadcasting.connections.pusher.app_id'),
                config('broadcasting.connections.pusher.options')
            );
        });

        $this->app->singleton(LoggingClient::class, function() {
            $keyFile = null;
            try {
                $keyFile = json_decode(base64_decode(config('services.google.key_file')), true);
            } catch (\Exception $e) {}

            return new LoggingClient([
                "projectId" => config('services.google.logging.project_id'),
                "keyFile" => $keyFile
            ]);
        });

        if ($this->app->environment('local')) {
            $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
            $this->app->register(TelescopeServiceProvider::class);
        }

        $this->app->bind(TwilioVerify::class, function() {
           return new TwilioVerify(
               config('services.twilio.sid'),
               config('services.twilio.token'),
               config('services.twilio.verify_service_sid'),
           );
        });

        $this->app->bind(SlackNotificationServiceContract::class, function () {
            return $this->app->environment('local')
                ? new DummySlackNotificationService()
                : new SlackNotificationService();
        });

        $classes = [
            'docusign' => DocuSignService::class,
            'communication-contract' => CommunicationContract::class,
            'twilio-communication' => TwilioCommunicationService::class,
            'twilio-verify' => TwilioVerify::class
        ];

        foreach ($classes as $key => $class) {
            if (config('services.mock.' . $key)) {
                $this->app->bind($class, fn() => mock($class));
            }
        }

        /**
         * // TODO: remove once either AccountManagerService is implemented or its usages are refactored
         *
         * @see \App\Http\Controllers\API\Sales\SalesManagementAPIController
         */
        $this->app->bind('App\Services\AccountManagerService', fn() => mock('App\Services\AccountManagerService'));
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        if (config('app.env') !== 'local') {
            $this->app['request']->server->set('HTTPS','on'); // set requests to https for pagination
        }

        $this->extendSessionGuard();
    }

    /**
     * These added macro functions provide an interface for tracking the user and model
     *  who initiated a change during a request session
     * This is currently used for workflow events to track whether the user was an internal User,
     *  or a dashboard CompanyUser, or a User shadowing the dashboard
     * TODO: move these functions to an extended or new class instead of macros
     *
     * @return void
     */
    private function extendSessionGuard(): void
    {
        SessionGuard::macro('setActioningUserId', function (int $userId) {
            session()->put('actioning_user_id', $userId);
            session()->put(session()->get('actioning_user_id'));
        });

        SessionGuard::macro('setActioningUserLegacyId', function (?int $legacyUserId) {
            if ($legacyUserId)
                session()->put('actioning_user_legacy_id', $legacyUserId);
            else {
                // Log a warning if no legacy ID is found, as this will affect events coming back from Legacy
                $userType = session()->get('actioning_user_type') ?? '';
                $model = ActioningUserType::tryFrom($userType)?->getModel() ?? 'actioning user';
                $userId = session()->get('actioning_user_id') ?? 'unknown';
                $key = 'session_guard_missing_legacy_id-' . $userId;

                if (!Cache::has($key)) {
                    logger()->warning("SessionGuard: No legacy ID was found for $model with ID: " . $userId);
                    Cache::add($key, true, now()->addDay());
                }
            }
        });

        SessionGuard::macro('setActioningUserType', function (ActioningUserType $userType) {
            session()->put('actioning_user_type', $userType->value);
        });

        SessionGuard::macro('getActioningUserId', function (): ?int {
            return session()->get('actioning_user_id');
        });

        SessionGuard::macro('getActioningUserLegacyId', function (): ?int {
            return (int) session()->get('actioning_user_legacy_id');
        });

        SessionGuard::macro('getActioningUserType', function (): ?ActioningUserType {
            $type = session()->get('actioning_user_type');

            return ActioningUserType::tryFrom($type);
        });

        SessionGuard::macro('getActioningUser', function (): ?Model {
            $model = SessionGuard::getActioningUserType()?->getModel() ?? null;
            $id = SessionGuard::getActioningUserId();

            if (!$model || !$id)
                return null;

            return $model::query()->find($id);
        });
    }
}
