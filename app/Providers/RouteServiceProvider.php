<?php

namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * The path to the "home" route for your application.
     *
     * This is used by Laravel authentication to redirect users after login.
     *
     * @var string
     */
    public const HOME = '/dashboard';

    /**
     * Define your route model bindings, pattern filters, etc.
     *
     * @return void
     */
    public function boot(): void
    {
        $this->configureRateLimiting();

        $this->routes(function () {
            Route::middleware('api')
                ->prefix('api')
                ->group(base_path('routes/api.php'));

            Route::middleware('internal-api')
                ->prefix('internal-api')
                ->group(base_path('routes/internal-api.php'));

            Route::middleware('public-api')
                ->prefix('public-api')
                ->group(base_path('routes/public-api.php'));

            Route::middleware('web')
                ->group(base_path('routes/web.php'));

            Route::middleware('public-web')
                ->group(base_path('routes/public-web.php'));

            Route::middleware('auth')
                ->group(base_path('routes/auth.php'));

            Route::middleware('odin_resource')
                ->prefix('odin-api')
                ->group(base_path('routes/odin.php'));

            Route::middleware(['api', 'model-scope-middleware'])
                ->prefix('dashboard-api')
                ->group(base_path('routes/dashboard-api.php'));

            Route::middleware(['api'] )
                ->prefix('client-dashboard-api')
                ->group(base_path('routes/client-dashboard-api.php'));

            Route::middleware('public-api')
                ->prefix('affiliates-portal')
                ->group(base_path('routes/affiliates-portal.php'));

            Route::middleware('api')
                ->prefix('api')
                ->group(base_path('routes/privacy.php'));

        });
    }

    /**
     * Configure the rate limiters for the application.
     *
     * @return void
     */
    protected function configureRateLimiting(): void
    {
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });
    }
}
