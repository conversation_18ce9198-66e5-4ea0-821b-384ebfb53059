<?php

namespace App\Providers;

use App\Services\PubSub\PubSubMiddleware\ConvertLegacyCompanyId;
use App\Services\PubSub\PubSubMiddlewareService;
use Illuminate\Support\ServiceProvider;

class PubSubMiddlewareProvider extends ServiceProvider
{
    const PUBSUB_MIDDLEWARE = [
        ConvertLegacyCompanyId::class,
    ];

    /**
     * @return void
     */
    public function register(): void
    {
        $this->app->bind(PubSubMiddlewareService::class, function() {
            return new PubSubMiddlewareService(self::PUBSUB_MIDDLEWARE);
        });
    }

    /**
     * @return void
     */
    public function boot(): void
    {
        //
    }
}
