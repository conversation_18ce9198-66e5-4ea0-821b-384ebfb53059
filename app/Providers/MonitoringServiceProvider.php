<?php

namespace App\Providers;

use App\Jobs\RecordMonitoringLog;
use App\Repositories\LogMonitoringRepository;
use Google\Cloud\Logging\LoggingClient;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Queue\Events\JobProcessed;
use Illuminate\Queue\Events\JobProcessing;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\ServiceProvider;

class MonitoringServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     * @throws BindingResolutionException
     */
    public function boot(): void
    {
        Queue::before(function (JobProcessing $event) {
            if(config('queue.log_job_events') === true)
                $this->reportJobStart($event);
        });

        Queue::after(function (JobProcessed $event) {
            if(config('queue.log_job_events') === true)
                $this->reportJobEnd($event);
        });
    }

    /**
     * Reports that a job has started.
     *
     * @param JobProcessing $event
     * @return void
     */
    protected function reportJobStart(JobProcessing $event): void
    {
        $jobName = isset($event->job->payload()["displayName"]) ? $event->job->payload()["displayName"] : $event->job->getName();
        $payload = $event->job->payload();
//        RecordMonitoringLog::dispatch("Starting job {$jobName} (connection: {$event->connectionName}", ["payload" => $payload]);
    }

    /**
     * Reports that a job has ended.
     *
     * @param JobProcessed $event
     * @return void
     */
    protected function reportJobEnd(JobProcessed $event): void
    {
        $jobName = isset($event->job->payload()["displayName"]) ? $event->job->payload()["displayName"] : $event->job->getName();
        $payload = $event->job->payload();
//        RecordMonitoringLog::dispatch("Finished job {$jobName} (connection: {$event->connectionName}", ["payload" => $payload]);
    }
}
