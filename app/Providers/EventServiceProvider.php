<?php

namespace App\Providers;

use App\ConsumerProcessing\Events\AssignmentDeliveryFailureEvent;
use App\ConsumerProcessing\Listeners\AssignmentDeliveryFailureListener;
use App\Events\AccountManagerAssignedEvent;
use App\Events\Billing\StoredEvents\Invoice\Refund\InvoiceChargeRefundUpdated;
use App\Events\BundleManagement\BundleInvoiceStatusTransitionEvent;
use App\Events\Calendar\CalendarEventSaved;
use App\Events\Company\CompanySuspendedEvent;
use App\Events\Calendar\DemoCreated;
use App\Events\CompanyCampaign\CampaignOverBudgetEvent;
use App\Events\CompanyCampaign\CompanyCampaignServiceAreaUpdatedEvent;
use App\Events\CompanyCampaign\CRMDeliveryFailureEvent;
use App\Events\CompanyCampaign\PostAllocationEvent;
use App\Events\CompanyChangedPurchasingStatusEvent;
use App\Events\CompanyRegistration\V3\ContractAccepted;
use App\Events\CompanyRegistration\V3\PhoneVerified;
use App\Events\CompanyRegistration\V3\RegistrationCompleted;
use App\Events\CompanyRegistration\V3\RegistrationStarted;
use App\Events\ConsumerProcessing\ConsumerProcessingActivityEvent;
use App\Events\ConsumerProcessing\ConsumerProductApprovedEvent;
use App\Events\ConsumerProcessing\ConsumerProductCancelledEvent;
use App\Events\ConsumerProcessing\ConsumerProductMarkedAsPendingReviewEvent;
use App\Events\ConsumerProcessing\ConsumerProductMarkedAsUnderReviewEvent;
use App\Events\ConsumerProductLifecycleTracking\AllocationAttemptScheduled;
use App\Events\ConsumerProductLifecycleTracking\ApprovedToSell;
use App\Events\ConsumerProductLifecycleTracking\Created;
use App\Events\ConsumerProductLifecycleTracking\QueueUpdated;
use App\Events\ConsumerProductLifecycleTracking\StatusUpdated;
use App\Events\ConsumerProductSaved;
use App\Events\FloorPricingUpdatedEvent;
use App\Events\LeadProcessing\LeadAllocatedEvent;
use App\Events\LeadProcessing\LeadCancelledEvent;
use App\Events\LeadProcessing\LeadUndersoldEvent;
use App\Events\LeadProcessing\LeadUnsoldEvent;
use App\Events\MarketingCampaignConsumerRevalidated;
use App\Events\MarketingCampaignPageViewed;
use App\Events\OutreachCadence\CompanyRoutineDeletedEvent;
use App\Events\PrivacyRequestConsumerRedacted;
use App\Events\ProductAssignment\ProductAssignmentCreatedEvent;
use App\Events\ProductAssignment\ProductAssignmentUpdatedEvent;
use App\Events\ProductAssignment\ProductRejectedEvent;
use App\Events\ProductAssignment\ProductUnrejectedEvent;
use App\Events\ProductAssignmentSaved;
use App\Events\ProductCampaignStatusUpdatedEvent;
use App\Events\ProductCancellationDeleted;
use App\Events\ProductCancellationSaved;
use App\Events\ProductRejectionDeleted;
use App\Events\ProductRejectionSaved;
use App\Events\RejectionStatisticsUpdatedEvent;
use App\Events\SalesBait\PunterRecordedInterest;
use App\Events\SalesBait\SalesBaitCreatedEvent;
use App\Events\Workflows\ActionCreatedEvent;
use App\Events\Workflows\TaskConcludedEvent;
use App\Events\Workflows\TaskReassignedEvent;
use App\Events\Workflows\TaskRescheduledEvent;
use App\Listeners\AccountManagerAssignedListener;
use App\Listeners\CompanyCampaign\CRMDeliveryFailureListener;
use App\Listeners\DemoCreatedListener;
use App\Listeners\BillingWorkflowListener;
use App\Listeners\BundleManagement\BundleInvoiceStatusTransitionListener;
use App\Listeners\Company\ProcessOmAndAmAssignmentListener;
use App\Listeners\CompanyCampaign\CampaignOverBudgetListener;
use App\Listeners\CompanyCampaign\LogCampaignServiceAreaChangeLister;
use App\Listeners\CompanyCampaign\PostAllocationListener;
use App\Listeners\CompanyChangedPurchasingStatusListener;
use App\Listeners\CompanyRegistration\V3\BusinessDevelopmentManagerAssignmentListener;
use App\Listeners\CompanyRegistration\V3\CompanyRegistrationEmailNotificationListener;
use App\Listeners\CompanyRegistration\V3\CompanyRegistrationSlackNotificationListener;
use App\Listeners\CompanySuspendedEventListener;
use App\Listeners\ConsumerProcessing\ConsumerProcessingActivityListener;
use App\Listeners\ConsumerProcessing\ConsumerProductApprovedListener;
use App\Listeners\ConsumerProcessing\ConsumerProductCancelledListener;
use App\Listeners\ConsumerProcessing\ConsumerProductMarkedAsPendingReviewListener;
use App\Listeners\ConsumerProcessing\ConsumerProductMarkedAsUnderReviewListener;
use App\Listeners\ConsumerProductLifecycleTracking\LogAllocationAttemptScheduled;
use App\Listeners\ConsumerProductLifecycleTracking\LogApprovedToSell;
use App\Listeners\ConsumerProductLifecycleTracking\LogCreated;
use App\Listeners\ConsumerProductLifecycleTracking\LogQueueUpdated;
use App\Listeners\ConsumerProductLifecycleTracking\LogStatusUpdated;
use App\Listeners\ConsumerProductSavedListener;
use App\Listeners\FloorPricingUpdatedListener;
use App\Listeners\InvoiceChargeRefundUpdatedListener;
use App\Listeners\JobQueuedListener;
use App\Listeners\LeadProcessing\LeadAllocatedRevenueScenarioListener;
use App\Listeners\LeadProcessing\LeadCancelledRevenueScenarioListener;
use App\Listeners\LeasingCompanyListener;
use App\Listeners\ManualRejectionPercentageChangeListener;
use App\Listeners\MarketingCampaignConsumerRevalidatedListener;
use App\Listeners\MarketingCampaignPageViewedListener;
use App\Listeners\OutreachCadence\ConcludeTaskActionListener;
use App\Listeners\OutreachCadence\DeleteRoutineTasksListener;
use App\Listeners\OutreachCadence\UpdateTaskActionExecutionTime;
use App\Listeners\PrivacyRequestConsumerRedactedListener;
use App\Listeners\ProductAssignmentSavedListener;
use App\Listeners\ProductCampaignStatusUpdatedListener;
use App\Listeners\ProductCancellationDeletedListener;
use App\Listeners\ProductCancellationSavedListener;
use App\Listeners\ProductRejectionDeletedListener;
use App\Listeners\ProductRejectionSavedListener;
use App\Listeners\RejectionStatisticsUpdatedListener;
use App\Listeners\SalesBait\DeliverSalesBaitEmailListener;
use App\Listeners\SalesBait\LeadUndersoldListener;
use App\Listeners\SalesBait\LeadUnsoldListener;
use App\Listeners\SalesBait\SendPunterRecordedInterestPubSubEventListener;
use App\Listeners\SyncDemo;
use App\Listeners\Workflows\LegacyAddCrmEntryListener;
use App\Listeners\Workflows\TaskReassignedListener;
use App\Models\Action;
use App\Models\BundleInvoice;
use App\Models\Call;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\CompanyContract;
use App\Models\Email;
use App\Models\LeadProcessingCommunication;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyConfiguration;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductTracking;
use App\Models\Odin\ProductAppointment;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ProductCampaignBudget;
use App\Models\Odin\ProductCampaignSchedule;
use App\Models\Sales\Task;
use App\Models\Template;
use App\Models\Text;
use App\Models\User;
use App\Observers\ActionObserver;
use App\Observers\AddressObserver;
use App\Observers\BundleInvoiceObserver;
use App\Observers\CallObserver;
use App\Observers\CompanyCampaignObserver;
use App\Observers\CompanyConfigurationObserver;
use App\Observers\CompanyContractObserver;
use App\Observers\CompanyObserver;
use App\Observers\CompanyUserObserver;
use App\Observers\ConsumerObserver;
use App\Observers\ConsumerProductObserver;
use App\Observers\ConsumerProductTrackingObserver;
use App\Observers\EmailObserver;
use App\Observers\LeadProcessingCommunicationObserver;
use App\Observers\ProductAppointmentObserver;
use App\Observers\ProductAssignmentObserver;
use App\Observers\ProductCampaignBudgetObserver;
use App\Observers\ProductCampaignObserver;
use App\Observers\ProductCampaignScheduleObserver;
use App\Observers\TaskObserver;
use App\Observers\TemplateObserver;
use App\Observers\TextObserver;
use App\Observers\UserObserver;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Queue\Events\JobQueued;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],

        // Consumer/ConsumerProduct processing events
        // These can replace Lead/* events below as we remove legacy references from queue logic
        ConsumerProductCancelledEvent::class => [
            ConsumerProductCancelledListener::class
        ],
        ConsumerProductApprovedEvent::class => [
            ConsumerProductApprovedListener::class
        ],
        ConsumerProductMarkedAsPendingReviewEvent::class => [
            ConsumerProductMarkedAsPendingReviewListener::class
        ],
        ConsumerProductMarkedAsUnderReviewEvent::class => [
            ConsumerProductMarkedAsUnderReviewListener::class
        ],

        CRMDeliveryFailureEvent::class => [
            CRMDeliveryFailureListener::class,
        ],

        // Lead Processing Specific Events
        LeadAllocatedEvent::class => [
            LeadAllocatedRevenueScenarioListener::class
        ],

        LeadCancelledEvent::class => [
            LeadCancelledRevenueScenarioListener::class
        ],

        LeadUndersoldEvent::class => [
            LeadUndersoldListener::class,
            LeasingCompanyListener::class
        ],

        LeadUnsoldEvent::class => [
            LeadUnsoldListener::class,
            LeasingCompanyListener::class
        ],

        SalesBaitCreatedEvent::class => [
//            DeliverSalesBaitSMSListener::class,
            DeliverSalesBaitEmailListener::class
        ],

        PunterRecordedInterest::class => [
            SendPunterRecordedInterestPubSubEventListener::class
        ],

        ActionCreatedEvent::class => [
            LegacyAddCrmEntryListener::class
        ],

        AccountManagerAssignedEvent::class => [
            AccountManagerAssignedListener::class
        ],

        TaskReassignedEvent::class => [
            TaskReassignedListener::class
        ],

        TaskConcludedEvent::class => [
            ConcludeTaskActionListener::class
        ],

        TaskRescheduledEvent::class => [
            UpdateTaskActionExecutionTime::class
        ],

        BundleInvoiceStatusTransitionEvent::class => [
            BundleInvoiceStatusTransitionListener::class
        ],

        ProductCampaignStatusUpdatedEvent::class => [
            ProductCampaignStatusUpdatedListener::class
        ],

        ProductRejectedEvent::class => [
            ManualRejectionPercentageChangeListener::class
        ],

        ProductUnrejectedEvent::class => [
            ManualRejectionPercentageChangeListener::class
        ],

        ProductAssignmentUpdatedEvent::class => [
            ManualRejectionPercentageChangeListener::class
        ],

        ProductAssignmentCreatedEvent::class => [
            ManualRejectionPercentageChangeListener::class
        ],

        CompanyRoutineDeletedEvent::class => [
            DeleteRoutineTasksListener::class
        ],

        JobQueued::class => [
            JobQueuedListener::class
        ],

        AssignmentDeliveryFailureEvent::class => [
            AssignmentDeliveryFailureListener::class
        ],

        CompanyCampaignServiceAreaUpdatedEvent::class => [
            LogCampaignServiceAreaChangeLister::class
        ],

        CompanyChangedPurchasingStatusEvent::class => [
            CompanyChangedPurchasingStatusListener::class
        ],

        // Consumer Processing Activity (pre-allocation)
        ConsumerProcessingActivityEvent::class => [
            ConsumerProcessingActivityListener::class
        ],

        // Consumer Product Lifecycle Tracking
        Created::class                    => [LogCreated::class],
        StatusUpdated::class              => [LogStatusUpdated::class],
        QueueUpdated::class               => [LogQueueUpdated::class],
        ApprovedToSell::class             => [LogApprovedToSell::class],
        AllocationAttemptScheduled::class => [LogAllocationAttemptScheduled::class],

        FloorPricingUpdatedEvent::class => [
            FloorPricingUpdatedListener::class,
        ],

        InvoiceChargeRefundUpdated::class => [
            InvoiceChargeRefundUpdatedListener::class,
        ],

        MarketingCampaignPageViewed::class => [
            MarketingCampaignPageViewedListener::class
        ],

        MarketingCampaignConsumerRevalidated::class => [
            MarketingCampaignConsumerRevalidatedListener::class
        ],

        PrivacyRequestConsumerRedacted::class => [
            PrivacyRequestConsumerRedactedListener::class
        ],

        // Company registration
        RegistrationStarted::class => [
            CompanyRegistrationEmailNotificationListener::class,
            CompanyRegistrationSlackNotificationListener::class
        ],
        PhoneVerified::class => [],
        RegistrationCompleted::class => [
            CompanyRegistrationEmailNotificationListener::class,
            CompanyRegistrationSlackNotificationListener::class,
            BusinessDevelopmentManagerAssignmentListener::class,
        ],
        ContractAccepted::class => [
            CompanyRegistrationEmailNotificationListener::class,
            CompanyRegistrationSlackNotificationListener::class
        ],
        ConsumerProductSaved::class => [
            ConsumerProductSavedListener::class
        ],
        ProductAssignmentSaved::class => [
            ProductAssignmentSavedListener::class
        ],
        ProductRejectionSaved::class => [
            ProductRejectionSavedListener::class
        ],
        ProductRejectionDeleted::class => [
            ProductRejectionDeletedListener::class
        ],
        ProductCancellationSaved::class => [
            ProductCancellationSavedListener::class
        ],
        ProductCancellationDeleted::class => [
            ProductCancellationDeletedListener::class
        ],

        //lead allocation events
        PostAllocationEvent::class => [
            PostAllocationListener::class,
            ProcessOmAndAmAssignmentListener::class
        ],
        CampaignOverBudgetEvent::class => [
            CampaignOverBudgetListener::class
        ],
        CalendarEventSaved::class => [
            SyncDemo::class
        ],
        CompanySuspendedEvent::class => [
            CompanySuspendedEventListener::class
        ],
        DemoCreated::class => [
            DemoCreatedListener::class,
        ],
        RejectionStatisticsUpdatedEvent::class => [
            RejectionStatisticsUpdatedListener::class
        ]

    ];

    protected $subscribe = [
        BillingWorkflowListener::class
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot(): void
    {
        Call::observe(CallObserver::class);
        Action::observe(ActionObserver::class);
        Task::observe(TaskObserver::class);
        User::observe(UserObserver::class);
        Text::observe(TextObserver::class);
        Email::observe(EmailObserver::class);
        BundleInvoice::observe(BundleInvoiceObserver::class);
        ProductAppointment::observe(ProductAppointmentObserver::class);
        ProductAssignment::observe(ProductAssignmentObserver::class);
        Company::observe(CompanyObserver::class);
        CompanyUser::observe(CompanyUserObserver::class);
        ProductCampaign::observe(ProductCampaignObserver::class);
        ProductCampaignBudget::observe(ProductCampaignBudgetObserver::class);
        CompanyConfiguration::observe(CompanyConfigurationObserver::class);
        ProductCampaignSchedule::observe(ProductCampaignScheduleObserver::class);
        Consumer::observe(ConsumerObserver::class);
        CompanyCampaign::observe(CompanyCampaignObserver::class);
        ConsumerProduct::observe(ConsumerProductObserver::class);
        Address::observe(AddressObserver::class);
        LeadProcessingCommunication::observe(LeadProcessingCommunicationObserver::class);
        CompanyContract::observe(CompanyContractObserver::class);
        Template::observe(TemplateObserver::class);
        ConsumerProductTracking::observe(ConsumerProductTrackingObserver::class);
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     *
     * @return bool
     */
    public function shouldDiscoverEvents()
    {
        return false;
    }
}
