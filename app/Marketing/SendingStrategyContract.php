<?php

namespace App\Marketing;

use App\DTO\EmailService\OutgoingEmailDTO;
use App\Enums\MarketingCampaigns\SendingStrategy;
use App\Models\EmailTemplate;
use App\Models\MarketingCampaign;

interface SendingStrategyContract
{
    public function type(): SendingStrategy;

    public function sendEmail(
        OutgoingEmailDTO $target,
        EmailTemplate $template,
        MarketingCampaign $campaign,
    ): void;

}
