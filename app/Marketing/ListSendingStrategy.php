<?php

namespace App\Marketing;

use App\Contracts\Services\MarketingCampaign\InternalEmailMarketingService;
use App\DTO\EmailService\OutgoingEmailDTO;
use App\Enums\Emails\DomainStatus;
use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\MarketingLogType;
use App\Enums\MarketingCampaigns\SendingStrategy;
use App\Models\EmailTemplate;
use App\Models\MarketingCampaign;
use App\Models\MarketingCampaignConsumer;
use App\Models\MarketingDomain;
use App\Repositories\MarketingCampaign\MarketingCampaignConsumerRepository;
use App\Services\MarketingCampaign\MarketingDomainService;
use App\Services\MarketingCampaign\MarketingLogService;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class ListSendingStrategy implements SendingStrategyContract
{
    public function type(): SendingStrategy
    {
        return SendingStrategy::LIST;
    }

    public function sendEmail(
        OutgoingEmailDTO $target,
        EmailTemplate $template,
        MarketingCampaign $campaign,
    ): void
    {
        $emailMarketingService = app(InternalEmailMarketingService::class);

        $marketingDomainService = app(MarketingDomainService::class);

        $marketingCampaignConsumerRepository = app(MarketingCampaignConsumerRepository::class);

        $name = Str::of($target->getFromEmail())->before('@');
        $domain = Str::of($target->getFromEmail())->after('@');

        /** @var  $emailAddresses */
        $emailAddresses = Arr::get($campaign->configuration, 'sending_strategy_data', []);

        $validDomains = collect($emailAddresses)->pluck('sender_domain')->unique()->values()->toArray();

        /** @var ?MarketingDomain $marketingDomain */
        $marketingDomain = $marketingDomainService->list(
            status: DomainStatus::VERIFIED,
            marketingCampaignId: $campaign->id,
            domains: $validDomains,
        )->orderBy('sent_count')->first();

        if (!$marketingDomain) {
            MarketingLogService::log(
                message: 'Unable to find marketing domain that meets criteria.',
                namespace: MarketingLogType::MARKETING_DOMAIN,
                level: LogLevel::ERROR,
                context: [],
                relations: [
                    $campaign,
                    [
                        'id'    => $target->getRelationId(),
                        'class' => MarketingCampaignConsumer::class
                    ]
                ],
            );

            $marketingDomain = $marketingDomainService->findByName(name: $domain);
        }

        if ($marketingDomain) {
            $emailAddress = collect($emailAddresses)->first(fn ($email) => $email['sender_domain'] === $marketingDomain->domain);

            $name = $emailAddress ? $emailAddress['sender_local'] : $name;

            $emailMarketingService->send(
                target: $target,
                template: $template,
                fromEmail: $name . '@' . $marketingDomain->domain,
                fromName: $target->getFromUsername(),
            );

        } else {
            $emailMarketingService->send(
                target: $target,
                template: $template,
                fromEmail: $target->getFromEmail(),
                fromName: $target->getFromUsername(),
            );

            $marketingDomain = $marketingDomainService->create(
                name: $domain,
                status: DomainStatus::UNVERIFIED,
            );

            MarketingLogService::log(
                message: 'Email Sent from unknown marketing domain',
                namespace: MarketingLogType::MARKETING_DOMAIN,
                level: LogLevel::ERROR,
                context: [
                    'domain' => $domain,
                ],
                relations: [
                    $campaign,
                    [
                        'id'    => $target->getRelationId(),
                        'class' => MarketingCampaignConsumer::class
                    ]
                ],
            );
        }

        $marketingCampaignConsumerRepository->updateSentFromDomain(
            mccIds: [$target->getRelationId()],
            domainId: $marketingDomain->id,
        );
    }
}
