<?php

namespace App\Marketing;

use App\Contracts\Services\MarketingCampaign\InternalEmailMarketingService;
use App\DTO\EmailService\OutgoingEmailDTO;
use App\Enums\Emails\DomainStatus;
use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\MarketingLogType;
use App\Enums\MarketingCampaigns\SendingStrategy;
use App\Models\EmailTemplate;
use App\Models\MarketingCampaign;
use App\Models\MarketingCampaignConsumer;
use App\Repositories\MarketingCampaign\MarketingCampaignConsumerRepository;
use App\Services\MarketingCampaign\MarketingDomainService;
use App\Services\MarketingCampaign\MarketingLogService;
use Illuminate\Support\Str;

class DefaultSendingStrategy implements SendingStrategyContract
{

    public function type(): SendingStrategy
    {
        return SendingStrategy::DEFAULT;
    }

    public function sendEmail(
        OutgoingEmailDTO $target,
        EmailTemplate $template,
        MarketingCampaign $campaign,
    ):void
    {
        $emailMarketingService = app(InternalEmailMarketingService::class);

        $marketingDomainService = app(MarketingDomainService::class);

        $marketingCampaignConsumerRepository = app(MarketingCampaignConsumerRepository::class);

        $domain = Str::of($target->getFromEmail())->after('@');

        $marketingDomain = $marketingDomainService->findByName(name: $domain);

        $emailMarketingService->send(
            target: $target,
            template: $template,
            fromEmail: $target->getFromEmail(),
            fromName: $target->getFromUsername(),
        );

        if (!$marketingDomain) {
            MarketingLogService::log(
                message: 'Email Sent from unknown marketing domain',
                namespace: MarketingLogType::MARKETING_DOMAIN,
                level: LogLevel::ERROR,
                context: [
                    'domain' => $domain,
                ],
                relations: [
                    $campaign,
                    [
                        'id'    => $target->getRelationId(),
                        'class' => MarketingCampaignConsumer::class
                    ]
                ],
            );

            $marketingDomain = $marketingDomainService->create(
                name: $domain,
                status: DomainStatus::UNVERIFIED,
            );
        }

        $marketingCampaignConsumerRepository->updateSentFromDomain(
            mccIds: [$target->getRelationId()],
            domainId: $marketingDomain->id,
        );
    }
}
