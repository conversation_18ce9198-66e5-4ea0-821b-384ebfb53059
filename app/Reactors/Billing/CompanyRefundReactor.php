<?php

namespace App\Reactors\Billing;

use App\DTO\Billing\Refund\InvoiceRefundDTO;
use App\Events\Billing\StoredEvents\Invoice\Refund\RequestRefund;
use App\Jobs\Billing\MakeRefundInvoiceRequestJob;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceTransaction;
use App\Services\Billing\BillingProfile\BillingProfileService;
use Exception;
use Spatie\EventSourcing\EventHandlers\Reactors\Reactor;

class CompanyRefundReactor extends Reactor
{
    public function __construct(protected BillingProfileService $billingProfileService)
    {

    }

    /**
     * @param RequestRefund $event
     * @return void
     * @throws Exception
     */
    public function onRequestRefund(RequestRefund $event):void
    {
        $invoiceRefundObject = InvoiceRefundDTO::fromArray($event->invoiceRefundObject);

        $invoiceRefundCharges = $invoiceRefundObject->getInvoiceRefundChargesCollection();

        /** @var BillingProfile $billingProfile */
        $billingProfile = Invoice::findByUuid($event->invoiceUuid)->{Invoice::RELATION_BILLING_PROFILE};

        foreach ($invoiceRefundCharges as $invoiceRefundCharge){
            /** @var InvoiceTransaction $refundedTransaction */
            $refundedTransaction = InvoiceTransaction::query()
                ->where(InvoiceTransaction::FIELD_ID, $invoiceRefundCharge->getRefundedPaymentId())
                ->first();

            MakeRefundInvoiceRequestJob::dispatch(
                $event->invoiceUuid,
                $invoiceRefundCharge->getUuid(),
                $invoiceRefundCharge->getAmount(),
                $refundedTransaction->external_reference,
                $event->authorType,
                $billingProfile->id,
                $event->authorId,
                $billingProfile->max_allowed_charge_attempts
            );
        }

    }
}
