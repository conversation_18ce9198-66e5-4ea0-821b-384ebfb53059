<?php

namespace App\Reactors\Billing;

use App\Aggregates\InvoiceAggregateRoot;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Enums\Billing\InvoiceStates;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoicePaymentDue;
use App\Events\Billing\StoredEvents\Invoice\Collections\IssueInvoiceToCollections;
use App\Events\Billing\StoredEvents\Invoice\InvoiceStatusUpdated;
use App\Events\Billing\StoredEvents\Invoice\Pdf\InvoicePdfCreated;
use App\Events\Billing\StoredEvents\Invoice\RequestInvoiceTax;
use App\Models\Billing\Invoice;
use App\Services\Billing\InvoiceService;
use App\Services\Billing\InvoiceSnapshotService;
use App\Services\Billing\InvoiceStatusListener\InvoiceStatusChangeController;
use App\Services\Billing\InvoiceTransactions\InvoiceTransactionService;
use App\Services\Billing\TaxService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Spatie\EventSourcing\EventHandlers\EventHandler;
use Spatie\EventSourcing\EventHandlers\HandlesEvents;

class InvoiceReactor implements EventHandler
{
    use HandlesEvents;

    public function __construct(
        protected TaxService $taxService,
        protected InvoiceStatusChangeController $invoiceStatusHookController,
        protected InvoiceTransactionService $invoiceTransactionService,
        protected InvoiceSnapshotService $invoiceSnapshotService,
        protected InvoiceService $invoiceService,
    )
    {

    }

    /**
     * @param RequestInvoiceTax $event
     * @return void
     */
    public function onRequestInvoiceTax(RequestInvoiceTax $event): void
    {
        /** @var Invoice $invoice */
        //        $invoice = Invoice::findByUuid($event->invoiceUuid);
        //        $this->taxService->calculateTax($invoice, $event->authorId);
    }

    /**
     * @param InvoicePdfCreated $event
     * @return void
     * @throws BindingResolutionException
     */
    public function onInvoicePdfCreated(InvoicePdfCreated $event): void
    {
        /** @var Invoice $invoice */
        $invoice = Invoice::findByUuid($event->invoiceUuid);

        $root = InvoiceAggregateRoot::retrieve($invoice->{Invoice::FIELD_UUID});

        if ($invoice->status->status() === InvoiceStates::DRAFT->value) {
            $root->updateInvoiceStatus(
                invoice   : $invoice,
                newStatus : InvoiceStates::ISSUED->value,
                authorType: InvoiceEventAuthorTypes::SYSTEM->value,
                date      : $invoice->issue_at
            )->persist();
        }
    }

    /**
     * @param InvoiceStatusUpdated $event
     * @return void
     * @throws BindingResolutionException
     * @throws \Exception
     */
    public function onInvoiceStatusUpdated(InvoiceStatusUpdated $event): void
    {
        /** @var Invoice $invoice */
        $invoice = Invoice::findByUuid($event->invoiceUuid);

        $this->invoiceStatusHookController->before(
            state  : InvoiceStates::tryFrom($event->newStatus),
            invoice: $invoice,
        );

        $this->invoiceSnapshotService->createInvoiceSnapshot(
            invoiceUuid: $event->invoiceUuid,
            date       : $event->date,
        );

        $this->invoiceStatusHookController->on(
            state  : InvoiceStates::tryFrom($event->newStatus),
            invoice: $invoice,
        );
    }

    /**
     * @param IssueInvoiceToCollections $event
     * @return void
     * @throws BindingResolutionException
     */
    public function onIssueInvoiceToCollections(IssueInvoiceToCollections $event): void
    {
        $root = InvoiceAggregateRoot::retrieve($event->invoiceUuid);

        /** @var Invoice $invoice */
        $invoice = Invoice::findByUuid($event->invoiceUuid);

        $root->updateInvoiceStatus(
            invoice   : $invoice,
            newStatus : InvoiceStates::COLLECTION->value,
            authorType: $event->authorType,
            authorId  : $event->authorId
        );

        $root->persist();
    }

    public function onInvoicePaymentDue(InvoicePaymentDue $event): void
    {
        $root = InvoiceAggregateRoot::retrieve($event->invoiceUuid);

        /** @var Invoice $invoice */
        $invoice = Invoice::findByUuid($event->invoiceUuid);

        $root->updateInvoiceStatus(
            invoice   : $invoice,
            newStatus : InvoiceStates::FAILED->value,
            authorType: InvoiceEventAuthorTypes::SYSTEM->value
        );

        $root->persist();
    }
}
