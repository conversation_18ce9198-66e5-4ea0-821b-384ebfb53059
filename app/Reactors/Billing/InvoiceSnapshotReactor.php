<?php

namespace App\Reactors\Billing;

use App\Aggregates\InvoiceAggregateRoot;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Enums\Billing\InvoiceStates;
use App\Events\Billing\StoredEvents\Invoice\InvoiceTransactionCreated;
use App\Events\Billing\StoredEvents\Invoice\InvoiceSnapshotCreated;
use App\Models\Billing\Invoice;
use App\Services\Billing\InvoiceSnapshotService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Spatie\EventSourcing\EventHandlers\EventHandler;
use Spatie\EventSourcing\EventHandlers\HandlesEvents;

class InvoiceSnapshotReactor implements EventHandler
{
    use HandlesEvents;

    public function __construct(
        protected InvoiceSnapshotService $invoiceSnapshotService,
    )
    {

    }

    /**
     * @param InvoiceTransactionCreated $event
     * @return void
     * @throws \Exception
     */
    public function onInvoiceTransactionCreated(InvoiceTransactionCreated $event): void
    {
        $this->invoiceSnapshotService->createInvoiceSnapshot(
            invoiceUuid: $event->invoiceUuid,
            date       : $event->date
        );
    }

    /**
     * Handle invoice status updates based on the event details.
     *
     * @param InvoiceSnapshotCreated $event
     * @throws BindingResolutionException
     */
    public function onInvoiceSnapshotCreated(InvoiceSnapshotCreated $event): void
    {
        /** @var Invoice $invoice */
        $invoice = Invoice::findByUuid($event->invoiceUuid);

        $newStatus = $this->consolidateNewStatus($invoice, $event);

        if ($newStatus !== null && $invoice->status->status() !== $newStatus) {
            $root = InvoiceAggregateRoot::retrieve($invoice->{Invoice::FIELD_UUID});

            $root->updateInvoiceStatus(
                invoice   : $invoice,
                newStatus : $newStatus,
                authorType: InvoiceEventAuthorTypes::SYSTEM->value,
                date      : $event->date
            );

            $root->persist();
        }
    }

    /**
     * @param Invoice $invoice
     * @param InvoiceSnapshotCreated $event
     * @return string|null
     */
    private function consolidateNewStatus(Invoice $invoice, InvoiceSnapshotCreated $event): ?string
    {
        if ($this->shouldMarkAsWrittenOff($invoice, $event)) {
            return InvoiceStates::WRITTEN_OFF->value;
        }

        if ($this->shouldMarkAsChargeback($invoice, $event)) {
            return InvoiceStates::CHARGEBACK->value;
        }

        if ($this->shouldMarkAsCollection($invoice, $event)) {
            return InvoiceStates::COLLECTION->value;
        }

        if ($this->shouldMarkAsPaid($invoice, $event)) {
            return InvoiceStates::PAID->value;
        }

        if ($this->shouldMarkAsRefunded($invoice, $event)) {
            return InvoiceStates::REFUNDED->value;
        }


        return null;
    }

    /**
     * @param Invoice $invoice
     * @param InvoiceSnapshotCreated $event
     * @return bool
     */
    private function shouldMarkAsPaid(Invoice $invoice, InvoiceSnapshotCreated $event): bool
    {
        // Ensure we don't skip the issue status when issuing a 0 dollar invoice
        if ($invoice->status->status() === InvoiceStates::DRAFT->value) {
            return false;
        }

        return ((
                $event->totalValue == $invoice->getTotalCreditsApplied() &&
                empty($event->totalOutstanding)
            ) || (
                empty($event->totalOutstanding) && $event->totalPaid > 0
            ));
    }

    /**
     * @param Invoice $invoice
     * @param InvoiceSnapshotCreated $event
     * @return bool
     */
    private function shouldMarkAsRefunded(Invoice $invoice, InvoiceSnapshotCreated $event): bool
    {
        return $event->totalRefunded >= $invoice->getTotalIssuable() && $invoice->getTotalIssuable() > 0;
    }

    /**
     * @param Invoice $invoice
     * @param InvoiceSnapshotCreated $event
     * @return bool
     */
    private function shouldMarkAsCollection(Invoice $invoice, InvoiceSnapshotCreated $event): bool
    {
        return $event->totalCollections > 0;
    }

    /**
     * @param Invoice $invoice
     * @param InvoiceSnapshotCreated $event
     * @return bool
     */
    private function shouldMarkAsChargeback(Invoice $invoice, InvoiceSnapshotCreated $event): bool
    {
        return $event->totalChargeback > 0;
    }

    /**
     * @param Invoice $invoice
     * @param InvoiceSnapshotCreated $event
     * @return bool
     */
    private function shouldMarkAsWrittenOff(Invoice $invoice, InvoiceSnapshotCreated $event): bool
    {
        return $event->totalWrittenOff > 0;
    }

    /**
     * @param string $invoiceUuid
     * @param string $newStatus
     * @param string $oldStatus
     */
    private function updateInvoiceStatus(string $invoiceUuid, string $newStatus, string $oldStatus): void
    {

    }
}
