<?php

namespace App\Reactors\Billing;

use App\Aggregates\CreditAggregateRoot;
use App\Enums\Billing\InvoiceTransactionType;
use App\Events\Billing\StoredEvents\Credit\CreditAppliedToInvoice;
use App\Events\Billing\StoredEvents\Credit\CreditTypeDeducted;
use App\Models\Billing\Credit;
use App\Services\Billing\InvoiceTransactions\InvoiceTransactionService;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Spatie\EventSourcing\EventHandlers\Reactors\Reactor;

class CreditReactor extends Reactor
{

    public function __construct(protected InvoiceTransactionService $invoiceTransactionService)
    {

    }

    /**
     * @param CreditTypeDeducted $event
     * @return void
     */
    public function onCreditTypeDeducted(CreditTypeDeducted $event): void
    {

        $credits = Credit::query()
            ->where(Credit::FIELD_COMPANY_ID, $event->companyId)
            ->where(Credit::FIELD_CREDIT_TYPE, $event->type)
            ->where(Credit::FIELD_EXPIRES_AT, '>', Carbon::now())
            ->orderBy(Credit::FIELD_EXPIRES_AT)
            ->get();

        $amountRemaining = $event->amount;

        foreach ($credits as $credit) {
            if ($amountRemaining === 0) continue;

            $creditToDeduct = $credit->{Credit::FIELD_REMAINING_VALUE};

            if ($credit->{Credit::FIELD_REMAINING_VALUE} > $amountRemaining) {
                $creditToDeduct = $amountRemaining;
            }

            CreditAggregateRoot::retrieve($event->aggregateRootUuid())
                ->deductCreditById(
                    creditId  : $credit->{Credit::FIELD_ID},
                    amount    : $creditToDeduct,
                    authorType: $event->authorType,
                    authorId  : $event->authorId
                )->persist();

            $amountRemaining -= $creditToDeduct;
        }
    }

    public function onCreditAppliedToInvoice(CreditAppliedToInvoice $event): void
    {
        $this->invoiceTransactionService
            ->createInvoiceTransaction(
                uuid             : Str::uuid(),
                invoiceUuid      : $event->invoiceUuid,
                externalReference: Str::uuid()->toString(),
                amount           : $event->amount,
                currency         : 'usd',
                type             : InvoiceTransactionType::CREDIT->value,
                origin           : 'a20',
                payload          : [
                    'credit_type' => $event->creditType
                ],
            );
    }
}
