<?php

namespace App\Reactors\Billing;

use App\Events\Billing\StoredEvents\ActionApprovalRequest\ActionApprovalRequested;
use App\Events\Billing\StoredEvents\ActionApprovalRequest\ActionApprovalReviewed;
use App\Jobs\Billing\ActionApprovalAction\ExecuteActionApprovalJob;
use App\Jobs\Billing\ActionApprovalAction\ActionApprovalNotifierJob;
use App\Repositories\Billing\ActionApprovalRepository;
use Spatie\EventSourcing\EventHandlers\Reactors\Reactor;

class ActionApprovalReactor extends Reactor
{
    public function __construct(protected ActionApprovalRepository $actionApprovalRepository)
    {

    }

    /**
     * @param ActionApprovalRequested $event
     * @return void
     */
    public function onActionApprovalRequested(ActionApprovalRequested $event): void
    {
        ActionApprovalNotifierJob::dispatch(
            $event->actionApprovalUuid,
            ActionApprovalNotifierJob::TYPE_NOTIFY_REVIEWERS
        );
    }

    /**
     * @param ActionApprovalReviewed $event
     * @return void
     */
    public function onActionApprovalReviewed(ActionApprovalReviewed $event): void
    {
        ExecuteActionApprovalJob::dispatch($event->actionApprovalUuid);
    }
}
