<?php

namespace App\Reactors\Billing;

use Spatie\EventSourcing\EventHandlers\EventHandler;
use Spatie\EventSourcing\EventHandlers\HandlesEvents;
class ChargeAttemptFailed implements EventHandler
{
    use HandlesEvents;

    /**
     * @return void
     */
    public function onChargeAttemptFailed(): void
    {
        // determine if charge should be re-attempted

        // run rule pipeline (alerts, billing actions, notifications etc.)
    }

}
