<?php

namespace App\Reactors\Billing;

use App\Enums\Billing\InvoiceTransactionType;
use App\Events\Billing\StoredEvents\Invoice\WriteOff\InvoiceWrittenOff;
use App\Services\Billing\InvoiceTransactions\InvoiceTransactionService;
use Illuminate\Support\Str;
use Spatie\EventSourcing\EventHandlers\EventHandler;
use Spatie\EventSourcing\EventHandlers\HandlesEvents;

class InvoiceWriteOffsReactor implements EventHandler
{
    use HandlesEvents;

    public function __construct(
        protected InvoiceTransactionService $invoiceTransactionService,
    )
    {

    }

    /**
     * @param InvoiceWrittenOff $event
     * @return void
     */
    public function onInvoiceWrittenOff(InvoiceWrittenOff $event): void
    {
        $this->invoiceTransactionService->createInvoiceTransaction(
            uuid             : $event->uuid,
            invoiceUuid      : $event->invoiceUuid,
            externalReference: Str::uuid()->toString(),
            amount           : $event->amount,
            currency         : 'usd',
            type             : InvoiceTransactionType::WRITTEN_OFF->value,
            origin           : 'A20',
        );
    }
}
