<?php

namespace App\Reactors\Billing;

use App\Events\Billing\StoredEvents\Invoice\Pdf\CreateInvoicePdf;
use App\Jobs\Billing\CreateInvoicePdfJob;
use App\Models\Billing\Invoice;
use Spatie\EventSourcing\EventHandlers\Reactors\Reactor;

class InvoicePdfReactor extends Reactor
{
    /**
     * @param CreateInvoicePdf $event
     * @return void
     */
    public function onCreateInvoicePdf(CreateInvoicePdf $event): void
    {
        logger()->info('Create pdf job...');
        CreateInvoicePdfJob::dispatch(Invoice::findByUuid($event->invoiceUuid));
    }
}
