<?php

namespace App\Reactors\Billing;

use App\Aggregates\InvoiceAggregateRoot;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Enums\Billing\InvoicePaymentChargeStatus;
use App\Enums\Billing\InvoicePaymentStatus;
use App\Enums\Billing\InvoiceStates;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeRequest;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeScheduled;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeSuccess;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoicePaymentFailed;
use App\Jobs\Billing\MakeChargeRequestInvoiceJob;
use App\Jobs\Billing\ScheduleChargeRequestInvoiceJob;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoicePayment;
use App\Models\Billing\InvoicePaymentCharge;
use App\Repositories\Billing\CompanyPaymentMethodRepository;
use App\Services\Billing\InvoiceTransactions\InvoiceTransactionService;
use Carbon\Carbon;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Str;
use Spatie\EventSourcing\EventHandlers\Projectors\Projector;

class InvoicePaymentReactor extends Projector
{
    public function __construct(
        protected InvoiceTransactionService $invoiceTransactionService,
        protected CompanyPaymentMethodRepository $companyPaymentMethodRepository
    )
    {

    }

    /**
     * @param InvoiceChargeScheduled $event
     * @return void
     */
    public function onInvoiceChargeScheduled(InvoiceChargeScheduled $event): void
    {
        $rescheduledDateInSeconds = $event->attemptChargeDate
            ? now()->diffInSeconds($event->attemptChargeDate)
            : 0;

        ScheduleChargeRequestInvoiceJob::dispatch(
            $event->uuid,
            $event->invoiceUuid,
            $event->total,
            $event->authorType,
            $event->billingProfileId,
            $event->maxAttempts,
            $event->authorId,
            $event->paymentMethodsAttempts,
        )->delay($rescheduledDateInSeconds);
    }

    /**
     * @param InvoicePaymentFailed $event
     * @return void
     * @throws BindingResolutionException
     */
    public function onInvoicePaymentFailed(InvoicePaymentFailed $event): void
    {
        $root = InvoiceAggregateRoot::retrieve($event->invoiceUuid);

        /** @var Invoice $invoice */
        $invoice = Invoice::findByUuid($event->invoiceUuid);

        $root->updateInvoiceStatus(
            invoice   : $invoice,
            newStatus : InvoiceStates::FAILED->value,
            authorType: $event->authorType,
            authorId  : $event->authorId,
        );

        $root->persist();
    }

    /**
     * @param InvoiceChargeRequest $event
     * @return void
     */
    public function onInvoiceChargeRequest(InvoiceChargeRequest $event): void
    {
        MakeChargeRequestInvoiceJob::dispatch(
            $event->uuid,
            $event->invoiceUuid,
            $event->total,
            $event->authorType,
            $event->billingProfileId,
            $event->maxAttempts,
            $event->invoicePaymentDate,
            $event->authorId,
            $event->paymentMethodsAttempts,
        );
    }

    /**
     * @param InvoiceChargeSuccess $event
     * @return void
     * @throws BindingResolutionException
     */
    public function onInvoiceChargeSuccess(InvoiceChargeSuccess $event): void
    {
        $transactionUuid = Str::uuid()->toString();

        if ($event->invoicePaymentChargeUuid) {
            $invoicePaymentCharge = InvoicePaymentCharge::findByUuid($event->invoicePaymentChargeUuid);

            $invoicePaymentCharge->update([
                InvoicePaymentCharge::FIELD_TRANSACTION_UUID => $transactionUuid
            ]);

            InvoicePayment::query()
                ->where(InvoicePayment::FIELD_ID, $invoicePaymentCharge->{InvoicePaymentCharge::FIELD_INVOICE_PAYMENT_ID})
                ->update([
                    InvoicePayment::FIELD_STATUS          => InvoicePaymentStatus::CHARGED->value,
                    InvoicePayment::FIELD_CHARGED_AT      => $event->date,
                    InvoicePayment::FIELD_NEXT_ATTEMPT_AT => null,
                ]);
        } else {
            $invoice = Invoice::findByUuid($event->invoiceUuid);

            $invoicePayment = InvoicePayment::query()->create([
                InvoicePayment::FIELD_UUID         => Str::uuid()->toString(),
                InvoicePayment::FIELD_INVOICE_ID   => $invoice->id,
                InvoicePayment::FIELD_TOTAL        => $event->amount,
                InvoicePayment::FIELD_STATUS       => InvoicePaymentStatus::CHARGED->value,
                InvoicePayment::FIELD_CHARGED_AT   => $event->createdAt(),
                InvoicePayment::FIELD_REQUESTED_AT => $event->createdAt(),
                InvoicePayment::FIELD_AUTHOR_TYPE  => InvoiceEventAuthorTypes::SYSTEM->value,
            ]);

            $paymentMethod = $this->companyPaymentMethodRepository->getByExternalId(
                $event->externalPaymentMethodId
            );

            InvoicePaymentCharge::query()->create([
                InvoicePaymentCharge::FIELD_UUID               => Str::uuid()->toString(),
                InvoicePaymentCharge::FIELD_INVOICE_PAYMENT_ID => $invoicePayment->id,
                InvoicePaymentCharge::FIELD_PAYMENT_METHOD_ID  => $paymentMethod->id,
                InvoicePaymentCharge::FIELD_TOTAL              => $event->amount,
                InvoicePaymentCharge::FIELD_STATUS             => InvoicePaymentChargeStatus::REQUESTED->value,
                InvoicePaymentCharge::FIELD_TRANSACTION_UUID   => $transactionUuid
            ]);
        }

        $this->invoiceTransactionService->createInvoiceTransaction(
            uuid             : $transactionUuid,
            invoiceUuid      : $event->invoiceUuid,
            externalReference: $event->externalTransactionId,
            amount           : $event->amount,
            currency         : $event->currency,
            type             : $event->type,
            origin           : $event->source,
            payload          : [
                'created_at' => $event->date
            ],
            scenario         : $event->scenario,
            scope            : $event->scope,
            date             : $event->date
        );
    }
}
