<?php

namespace App\Mail;

use App\Builders\SalesBaits\RegisterInterestURLBuilder;
use App\DataModels\SalesBait\SalesBaitPunter;
use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\LeadRoofDetail;
use App\Models\SalesBaitRegisteredInterest;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Illuminate\Support\Facades\App;
use NumberFormatter;

class RoofingCalculatorSalesBait extends Mailable
{
    use Queueable, SerializesAndRestoresModelIdentifiers;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(protected EloquentQuote $lead, protected SalesBaitPunter $contact, protected int $salesBaitLeadId) {}

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $address = $this->lead->{EloquentQuote::RELATION_ADDRESS};
        $roofDetails = $this->lead->{EloquentQuote::RELATION_ROOF_DETAILS};

        $numFmt = new NumberFormatter('en_US', NumberFormatter::CURRENCY);

        return $this
            ->from(config('mail.from.roofing.address'), config('mail.from.roofing.name'))
            ->replyTo(config('mail.from.roofing.address'))
            ->subject($this->getSubject())
            ->markdown(
                'emails.rc-email-template',
                [
                    'city'                  => $address->{EloquentAddress::CITY},
                    'state'                 => $address->{EloquentAddress::STATE},
                    'zipcode'               => $address->{EloquentAddress::ZIP_CODE},
                    'roofer_first_name'     => $this->contact->name,
                    'client_first_name'     => $this->lead->{EloquentQuote::FIRST_NAME},
                    'material'              => $this->lead->{EloquentQuote::ROOF_TYPE},
                    'sub_material'          => $this->lead->roofDetails?->roof_sub_type,
                    'stories'               => $this->lead->storeys,
                    'area'                  => $this->lead->roofDetails?->roof_replacement_area,
                    'min_cost'              => $numFmt->formatCurrency($roofDetails?->{LeadRoofDetail::FIELD_ROOF_ESTIMATE_LOW} ?? 0, 'USD'),
                    'max_cost'              => $numFmt->formatCurrency($roofDetails?->{LeadRoofDetail::FIELD_ROOF_ESTIMATE_HIGH} ?? 0, 'USD'),
                    'sr_company_address1'   => 'Suite 525, 1401 17th Street',
                    'sr_company_address2'   => 'Denver, Colorado 80202',
                    'sr_company_phone'      => '(*************',
                    "address_image"         => $this->lead->getAddressImage(),
                    "register_interest_url" => $this->getRegisterInterestUrl(),
                    "unsubscribe_url"       => $this->getUnsubscribeUrl()
                ]
            );
    }

    /**
     * Returns the subject for a given lead.
     *
     * @return string
     */
    protected function getSubject(): string
    {
        return "Quote request in {$this->lead->address->city}, {$this->lead->address->state}, {$this->lead->address->zipcode}";
    }

    /**
     * Returns the register interest url.
     *
     * @return string
     */
    protected function getRegisterInterestUrl(): string
    {
        return $this->getUrlQuery()->build();
    }

    /**
     * Returns the unsubscribed url.
     *
     * @return string
     */
    protected function getUnsubscribeUrl(): string
    {
        return $this->getUrlQuery()
            ->setEndPoint("/purchase-interest-unsubscribe")
            ->build();
    }

    /**
     * Initializes the url query builder.
     *
     * @return RegisterInterestURLBuilder
     */
    protected function getUrlQuery(): RegisterInterestURLBuilder
    {
        return RegisterInterestURLBuilder::new()
            ->setBaseUrl(App::environment('production') ? RegisterInterestURLBuilder::RC_URL : RegisterInterestURLBuilder::RC_DEV_URL)
            ->setType($this->contact->type == SalesBaitRegisteredInterest::TYPE_USER ? "user" : "contact")
            ->setIndustry("roofing")
            ->setSalesBaitId($this->salesBaitLeadId)
            ->setRelationId($this->contact->id);
    }
}
