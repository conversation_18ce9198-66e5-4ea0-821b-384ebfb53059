<?php

namespace App\Mail\Appointments;

use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\QualityTier;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Services\Odin\WebsitesService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;

class NotifyAppointmentCompany extends Mailable
{
    use Queueable, SerializesAndRestoresModelIdentifiers;

    /**
     * @param ConsumerProduct $consumerProduct
     * @param Address $address
     * @param Consumer $consumer
     * @param string $industry
     * @param string $companyName
     * @param string $companyUserName
     * @param string $appointmentDateTime
     * @param QualityTier $appointmentType
     * @param string $detailsUrl
     * @throws Exception
     */
    public function __construct(
        private readonly ConsumerProduct $consumerProduct,
        private readonly Address $address,
        private readonly Consumer $consumer,
        private readonly string $industry,
        private readonly string $companyName,
        private readonly string $companyUserName,
        private readonly string $appointmentDateTime,
        private readonly QualityTier $appointmentType,
        private readonly string $detailsUrl
    )
    {
        if($consumerProduct->{ConsumerProduct::FIELD_ADDRESS_ID} !== $address->{Address::FIELD_ID}
        || $consumerProduct->{ConsumerProduct::FIELD_CONSUMER_ID} !== $consumer->{Consumer::FIELD_ID}) {
            throw new Exception(__METHOD__.": Consumer and/or Address doesn't belong to the consumer product");
        }

        if(!in_array($this->appointmentType->value, [QualityTier::IN_HOME->value, QualityTier::ONLINE->value])) {
            throw new Exception(__METHOD__.": Invalid appointment type");
        }
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $consumerProductId = $this->consumerProduct->{ConsumerProduct::FIELD_ID};

        $domainInfo = WebsitesService::getDomainInfo($consumerProductId);

        if($this->industry === IndustryEnum::ROOFING->value) {
            $fromEmail = config('mail.from.roofing.address');
            $fromName = config('mail.from.roofing.name');
        }
        else if($this->industry === IndustryEnum::SOLAR->value) {
            $fromEmail = config('mail.from.solar.address');
            $fromName = config('mail.from.solar.name');
        }
        else {
            $fromEmail = config('mail.from.address');
            $fromName = config('mail.from.name');
        }

        $consumerName = $this->consumer->getFullName();

        return $this
            ->from($fromEmail, $fromName)
            ->replyTo($fromEmail)
            ->subject("{$domainInfo[WebsitesService::DOMAIN_NAME]} Appointment Scheduled with {$consumerName}")
            ->markdown('emails.appointments.notify-appointment-company')
            ->with([
                'logo_src' => $domainInfo[WebsitesService::DOMAIN_LOGO_SRC],

                'company_user_name' => $this->companyUserName,
                'company_name' => $this->companyName,

                'consumer_name' => $consumerName,
                'consumer_phone' => $this->consumer->{Consumer::FIELD_PHONE} ?? '',
                'consumer_email' => $this->consumer->{Consumer::FIELD_EMAIL} ?? '',
                'address' => $this->address->getFullAddress(),
                'appointment_datetime' => $this->appointmentDateTime,
                'appointment_type' => $this->appointmentType->value,
                'industry' => $this->industry,

                'dashboard_link' => $this->detailsUrl,

                'sender_name' => $domainInfo[WebsitesService::DOMAIN_NAME],
                'sender_domain' => $domainInfo[WebsitesService::DOMAIN_URL],
                'sr_company_address1' => 'Suite 525, 1401 17th Street',
                'sr_company_address2' => 'Denver, Colorado 80202',
                'sr_company_phone' => '(*************'
            ]);
    }
}
