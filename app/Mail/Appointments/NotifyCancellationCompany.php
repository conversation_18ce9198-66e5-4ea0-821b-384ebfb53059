<?php

namespace App\Mail\Appointments;

use App\Enums\Odin\Industry as IndustryEnum;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Services\Odin\WebsitesService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;

class NotifyCancellationCompany extends Mailable
{
    use Queueable, SerializesAndRestoresModelIdentifiers;

    /**
     * @param ConsumerProduct $consumerProduct
     * @param Address $address
     * @param Consumer $consumer
     * @param IndustryEnum $industryEnum
     * @param string $companyName
     * @param string $companyUserName
     * @param string $appointmentDateTime
     * @throws Exception
     */
    public function __construct(
        private readonly ConsumerProduct $consumerProduct,
        private readonly Address         $address,
        private readonly Consumer        $consumer,
        private readonly IndustryEnum    $industryEnum,
        private readonly string          $companyName,
        private readonly string          $companyUserName,
        private readonly string          $appointmentDateTime
    )
    {
        if($consumerProduct->{ConsumerProduct::FIELD_ADDRESS_ID} !== $address->{Address::FIELD_ID}
        || $consumerProduct->{ConsumerProduct::FIELD_CONSUMER_ID} !== $consumer->{Consumer::FIELD_ID}) {
            throw new Exception(__METHOD__.": Consumer and/or Address doesn't belong to the consumer product");
        }
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $consumerProductId = $this->consumerProduct->{ConsumerProduct::FIELD_ID};

        $domainInfo = WebsitesService::getDomainInfo($consumerProductId);

        if($this->industryEnum === IndustryEnum::ROOFING) {
            $fromEmail = config('mail.from.roofing.address');
            $fromName = config('mail.from.roofing.name');
        }
        else if($this->industryEnum === IndustryEnum::SOLAR) {
            $fromEmail = config('mail.from.solar.address');
            $fromName = config('mail.from.solar.name');
        }
        else {
            $fromEmail = config('mail.from.address');
            $fromName = config('mail.from.name');
        }

        $consumerName = $this->consumer->getFullName();

        return $this
            ->from($fromEmail, $fromName)
            ->replyTo($fromEmail)
            ->subject("CANCELLED: {$domainInfo[WebsitesService::DOMAIN_NAME]} Appointment for {$consumerName}")
            ->markdown('emails.appointments.notify-cancellation-company')
            ->with([
                'logo_src' => $domainInfo[WebsitesService::DOMAIN_LOGO_SRC],

                'company_user_name' => $this->companyUserName,
                'company_name' => $this->companyName,

                'consumer_name' => $consumerName,
                'address' => $this->address->getFullAddress(),
                'appointment_datetime' => $this->appointmentDateTime,

                'sender_name' => $domainInfo[WebsitesService::DOMAIN_NAME],
                'sender_domain' => $domainInfo[WebsitesService::DOMAIN_URL],
                'sr_company_address1' => 'Suite 525, 1401 17th Street',
                'sr_company_address2' => 'Denver, Colorado 80202',
                'sr_company_phone' => '(*************'
            ]);
    }
}
