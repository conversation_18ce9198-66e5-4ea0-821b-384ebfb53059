<?php

namespace App\Mail\Workflows;

use App\DataModels\EmailTarget;
use App\Workflows\Actions\EmailNotificationAction;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class EmailNotification extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(protected EmailNotificationAction $emailNotificationAction, protected EmailTarget $target) {}

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): static
    {
        return $this->from($this->target->getFromAddress())
            ->subject($this->emailNotificationAction->getSubject())
            ->markdown('emails.workflows.email-notification')
            ->with(['message' => $this->emailNotificationAction->getMessage()]);
    }
}
