<?php

namespace App\Mail\MJML;

use Illuminate\Mail\Mailable;
use Illuminate\Support\Facades\View;
use ReflectionException;
use Throwable;

class MJMLMailable extends Mailable
{
    /**
     * The MJML template for the message (if applicable).
     *
     * @var string
     */
    protected string $mjml;

    /**
     * Set the MJML template for the message.
     *
     * @param $view
     * @param array $data
     * @return $this
     * @throws ReflectionException
     */
    public function mjml($view, array $data = []): self
    {
        $this->mjml     = $view;
        $this->viewData = array_merge($this->buildViewData(), $data);

        return $this;
    }

    /**
     * Build the view for the message.
     *
     * @return array|string
     * @throws ReflectionException
     * @throws Throwable
     */
    protected function buildView(): array|string
    {
        if (isset($this->mjml)) {
            return $this->buildMjmlView();
        }
        if (isset($this->markdown)) {
            return $this->buildMarkdownView();
        }
        if (isset($this->view, $this->textView)) {
            return [$this->view, $this->textView];
        } elseif (isset($this->textView)) {
            return ['text' => $this->textView];
        }

        return $this->view;
    }

    /**
     * Build the MJML view for the message.
     *
     * @return array
     * @throws ReflectionException
     * @throws Throwable
     */
    protected function buildMjmlView(): array
    {
        $view = View::make($this->mjml, $this->buildViewData());
        $mjml = new MJMLRenderer($view);

        return [
            'html' => $mjml->renderHTML()
        ];
    }
}
