<?php

namespace App\Mail\MJML;

use Illuminate\Support\Facades\File;
use Illuminate\Support\HtmlString;
use Illuminate\View\View;
use Symfony\Component\Process\Exception\ProcessFailedException;
use Symfony\Component\Process\Process;
use Throwable;

class MJMLRenderer
{
    /**
     * @var Process
     */
    protected Process $process;

    /**
     * @var View
     */
    protected View $view;

    /**
     * @var string
     */
    protected string $path;

    /**
     * @var string
     */
    protected string $compiledPath;


    /**
     * Generate a unique path to the MJML template, pre-compile the view
     * with the path and data, and set this class instance path.
     *
     * @param View $view
     */
    public function __construct(View $view)
    {
        $this->view = $view;

        // Hash combined data and path.  If either change, new pre-compiled file is generated.
        $dataPathChecksum = hash('sha256', json_encode([
            'path' => $this->view->getPath(),
            'data' => $this->view->getData(),
        ]));
        $this->path = rtrim(config('view.compiled'), '/') . "/{$dataPathChecksum}.mjml.php";
    }

    /**
     * Build the command to execute the MJML JS scripts to compile the .mjml file to HTML.
     *
     * @return string
     */
    public function buildCmdLineFromConfig(): string
    {
        return implode(' ', [
            'node',
            base_path('node_modules/.bin/mjml'),
            $this->path,
            '--config.filePath=' . dirname($this->view->getPath()),
            '-o',
            $this->compiledPath,
        ]);
    }

    /**
     * Render the html content.
     *
     * @return HtmlString
     *
     * @throws Throwable
     */
    public function renderHTML(): HtmlString
    {
        $html = $this->view->render();

        File::put($this->path, $html);

        $contentChecksum    = hash('sha256', $html);
        $this->compiledPath = rtrim(config('view.compiled'), '/') . "/{$contentChecksum}.php";

        if (!File::exists($this->compiledPath)) {
            $this->process = Process::fromShellCommandline($this->buildCmdLineFromConfig());
            $this->process->run();

            if (! $this->process->isSuccessful()) {
                throw new ProcessFailedException($this->process);
            }
        }

        return new HtmlString(File::get($this->compiledPath));
    }
}
