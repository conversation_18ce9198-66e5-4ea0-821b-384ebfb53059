<?php

namespace App\Mail\Campaigns;

use App\Campaigns\Delivery\CRM\CRMFieldReplacerService;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\EmailTemplate;
use App\Models\Odin\ConsumerProduct;
use App\Services\EmailTemplates\EmailTemplateImageService;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;

class LeadDeliveryEmail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(
        protected readonly ConsumerProduct $consumerProduct,
        protected readonly CompanyCampaign $companyCampaign,
        protected readonly EmailTemplate $template,
        protected readonly ?string $targetEmailAddress,
    ) {}

    public function content(): Content
    {
        /** @var EmailTemplateImageService $emailTemplateImageService */
        $emailTemplateImageService = app(EmailTemplateImageService::class);

        $template = $this->template;
        $background = $this->template->background ?? null;

        /** @var CRMFieldReplacerService $replacerService */
        $replacerService = app()->make(CRMFieldReplacerService::class);

        $markdownContent = $emailTemplateImageService->shortcodesToMarkdownImageTags( $template->{EmailTemplate::FIELD_CONTENT}, $template->{EmailTemplate::FIELD_ID});

        $markdownContent = $replacerService->replaceField(
            $this->consumerProduct,
            $this->companyCampaign,
            Str::markdown($markdownContent),
            $this->targetEmailAddress,
        );


        $header = $background?->header ?? '';
        $footer = $background?->footer ?? '';

        if($header) {
            $headerMarkdown = $emailTemplateImageService->shortcodesToMarkdownImageTags(
                $header,
                $template->{EmailTemplate::FIELD_BACKGROUND_ID}
            );

            $headerMarkdown = $replacerService->replaceField(
                $this->consumerProduct,
                $this->companyCampaign,
                Str::markdown($headerMarkdown),
                $this->targetEmailAddress,
            );
        }

        if($footer) {
            $footerMarkdown = $emailTemplateImageService->shortcodesToMarkdownImageTags(
                $footer,
                $template->{EmailTemplate::FIELD_BACKGROUND_ID}
            );

            $footerMarkdown = $replacerService->replaceField(
                $this->consumerProduct,
                $this->companyCampaign,
                Str::markdown($footerMarkdown),
                $this->targetEmailAddress,
            );
        }

        if ($template->subject) {
            $subjectReplaced = $replacerService->replaceField(
                $this->consumerProduct,
                $this->companyCampaign,
                $template->subject,
                $this->targetEmailAddress,
            );

            if ($subjectReplaced)
                $this->subject = $subjectReplaced;
        }

        return new Content(
            markdown: 'emails.campaigns.lead-delivery-markdown-template',
            with: [
                'header' => $headerMarkdown ?? $header,
                'content' => $markdownContent,
                'footer' => $footerMarkdown ?? $footer,
            ]
        );
    }
}
