<?php

namespace App\Mail\OutreachCadence;

use App\Models\User;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ConfigurableEmail extends Mailable
{
    use SerializesModels;

    /**
     * @return void
     */
    public function __construct(
        public string $fromEmail,
        public string $fromName,
        string        $subject,
        public string $body,
    )
    {
        $this->subject = $subject;
    }

    /**
     * @return Envelope
     */
    public function envelope(): Envelope
    {
        $fromAddress = new Address($this->fromEmail, $this->fromName);
        return new Envelope(
            from: $fromAddress,
            subject: $this->subject,
        );
    }

    /**
     * @return Content
     */
    public function content(): Content
    {
        return new Content(
            htmlString: $this->body,
        );
    }

}
