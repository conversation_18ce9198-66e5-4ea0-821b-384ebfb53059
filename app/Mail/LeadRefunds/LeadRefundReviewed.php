<?php

namespace App\Mail\LeadRefunds;

use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Number;

class LeadRefundReviewed extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(
        public string $recipientName,
        public string $leadRequestId,
        public string $company,
        public string $total,
        public string $requestDate,
        public string $approvalStatus,
        public array $comments = []
    )
    {

    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject("Your lead refund request for the company " . $this->company . " has been reviewed")
            ->markdown('emails.lead-refunds.refund-reviewed')
            ->with([
                'recipientName'  => $this->recipientName,
                'company'        => $this->company,
                'total'          => Number::currency($this->total),
                'requestDate'    => Carbon::parse($this->requestDate)->format('j F Y - g:iA (e)'),
                'approvalStatus' => $this->approvalStatus,
                'comments'       => $this->comments,
                'leadRefundsUrl' => route('lead-refunds')
            ]);
    }
}
