<?php

namespace App\Mail\LeadRefunds;

use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Number;

class LeadRefundCommentAdded extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(
        public string $company,
        public float $total,
        public string $recipientName,
        public int $leadRefundId,
        public string $requesterName,
        public string $requestDate,
        public array $comments,
    )
    {

    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject("A lead refund request for the company " . $this->company . " has been commented")
            ->markdown('emails.lead-refunds.refund-commented')
            ->with([
                "company"       => $this->company,
                "total"         => $this->total,
                "recipientName" => $this->recipientName,
                "leadRefundId"  => $this->leadRefundId,
                "requesterName" => $this->requesterName,
                "requestDate"   => Carbon::parse($this->requestDate)->format('j F Y - g:iA (e)'),
                "comments"      => $this->comments,
                "leadRefundsUrl" => route('lead-refunds')
            ]);
    }
}
