<?php

namespace App\Mail\CompanyRegistration\V3;

use App\Models\Odin\Company;
use App\Models\User;
use App\Services\Companies\CompanyLinkService;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class CompanyUserRelationshipUnassignmentNotification extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @param User $user
     * @param Company $company
     * @param string $type
     * @param string $reason
     */
    public function __construct(
        public User $user,
        public Company $company,
        public string $type,
        public string $reason,
    )
    {

    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): static
    {
        /** @var CompanyLinkService $linkService */
        $linkService = app(CompanyLinkService::class);

        $url = $linkService->getDashboardUrl($this->company);

        return $this->subject('Company Unassigned')
            ->markdown('emails.company-registration.v3.company-unassigned', [
                'user' => $this->user,
                'type' => $this->type,
                'reason' => $this->reason,
                'company' => $this->company,
                'url' => $url,
            ]);
    }
}
