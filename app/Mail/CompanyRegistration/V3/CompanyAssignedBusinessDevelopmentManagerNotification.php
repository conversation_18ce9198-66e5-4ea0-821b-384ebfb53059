<?php

namespace App\Mail\CompanyRegistration\V3;

use App\Models\Odin\Company;
use App\Models\User;
use App\Services\Companies\CompanyLinkService;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class CompanyAssignedBusinessDevelopmentManagerNotification extends Mailable
{
    use Queueable, SerializesModels;


    public User $user;
    public Company $company;

    /**
     * Create a new message instance.
     *
     * @param User $user
     * @param Company $company
     * @return void
     */
    public function __construct(User $user, Company $company)
    {
        $this->user = $user;
        $this->company = $company;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): static
    {
        /** @var CompanyLinkService $linkService */
        $linkService = app(CompanyLinkService::class);

        $url = $linkService->getDashboardUrl($this->company);

        return $this->subject('New Company Assignment')
            ->markdown('emails.company-registration.v3.company-assigned-bdm', [
                'user' => $this->user,
                'company' => $this->company,
                'url' => $url,
            ]);
    }
}
