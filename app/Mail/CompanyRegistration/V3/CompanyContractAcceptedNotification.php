<?php

namespace App\Mail\CompanyRegistration\V3;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\User;
use App\Services\Companies\CompanyLinkService;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class CompanyContractAcceptedNotification extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @param User $user
     * @param Company $company
     * @param string $type
     * @param string $reason
     * @param array|null $otherData
     */
    public function __construct(
        public User $user,
        public Company $company,
        public string $type,
        public string $reason,
        public ?array $otherData = [],
    )
    {
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): static
    {
        /** @var ?CompanyUser $companyUser */
        $companyUser = $this->otherData['company_user'] ?? null;
        $companyUserName = $companyUser?->completeName() ?? 'Unknown';

        return $this->subject('Company Accepted Contract')
            ->markdown('emails.company.company-contract-accepted', [
                'user' => $this->user,
                'type' => $this->type,
                'reason' => $this->reason,
                'company' => $this->company,
                'url' => $this->company->getAdminProfileUrl(),
                'companyUser' => $companyUserName,
            ]);
    }
}
