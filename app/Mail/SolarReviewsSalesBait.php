<?php

namespace App\Mail;

use App\Builders\SalesBaits\RegisterInterestURLBuilder;
use App\DataModels\SalesBait\SalesBaitPunter;
use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\LeadCategory;
use App\Models\SalesBaitRegisteredInterest;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Illuminate\Support\Facades\App;

class SolarReviewsSalesBait extends Mailable
{
    use Queueable, SerializesAndRestoresModelIdentifiers;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(protected EloquentQuote $lead, protected SalesBaitPunter $contact, protected int $salesBaitLeadId) {}

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $address = $this->lead->{EloquentQuote::RELATION_ADDRESS};

        return $this
            ->from(config('mail.from.solar.address'), config('mail.from.solar.name'))
            ->replyTo(config('mail.from.solar.address'))
            ->subject($this->getSubject())
            ->markdown('emails.sr-email-template')
            ->with([
                'city'                    => $address->{EloquentAddress::CITY},
                'state'                   => $address->{EloquentAddress::STATE},
                'zipcode'                 => $address->{EloquentAddress::ZIP_CODE},
                'installer_first_name'    => $this->contact->name,
                'client_first_name'       => $this->lead->{EloquentQuote::FIRST_NAME},
                'system_size'             => $this->lead->{EloquentQuote::SYSTEM_SIZE},
                'avg_monthly_power_spend' => $this->lead->{EloquentQuote::ELECTRIC_COST},
                'utility'                 => $this->lead->{EloquentQuote::UTILITY},
                'lead_id'                 => $this->lead->{EloquentQuote::ID},
                'lead_category'           => $this->lead->{EloquentQuote::RELATION_LEAD_CATEGORY} ? $this->lead->{EloquentQuote::RELATION_LEAD_CATEGORY}->{LeadCategory::NAME} : LeadCategory::CATEGORY_NAME_RESIDENTIAL,
                'comments'                => $this->lead->{EloquentQuote::COMMENTS},
                'sr_company_address1'     => 'Suite 525, 1401 17th Street',
                'sr_company_address2'     => 'Denver, Colorado 80202',
                'sr_company_phone'        => '(*************',
                "address_image"           => $this->lead->getAddressImage(),
                "register_interest_url" => $this->getRegisterInterestUrl(),
                "unsubscribe_url"       => $this->getUnsubscribeUrl()
            ]);
    }

    /**
     * Returns the subject for a given lead.
     *
     * @return string
     */
    protected function getSubject(): string
    {
        return "Quote Request: {$this->lead->systemsize}kW system in {$this->lead->address->city}, {$this->lead->address->state}, {$this->lead->address->zipcode}";
    }

    /**
     * Returns the register interest url.
     *
     * @return string
     */
    protected function getRegisterInterestUrl(): string
    {
        return $this->getUrlQuery()->build();
    }

    /**
     * Returns the unsubscribed url.
     *
     * @return string
     */
    protected function getUnsubscribeUrl(): string
    {
        return $this->getUrlQuery()
                    ->setEndPoint("/purchase-interest-unsubscribe")
                    ->build();
    }

    /**
     * Initializes the url query builder.
     *
     * @return RegisterInterestURLBuilder
     */
    protected function getUrlQuery(): RegisterInterestURLBuilder
    {
        return RegisterInterestURLBuilder::new()
            ->setBaseUrl(App::environment('production') ? RegisterInterestURLBuilder::SR_URL : RegisterInterestURLBuilder::SR_DEV_URL)
            ->setType($this->contact->type == SalesBaitRegisteredInterest::TYPE_USER ? "user" : "contact")
            ->setSalesBaitId($this->salesBaitLeadId)
            ->setRelationId($this->contact->id);
    }
}
