<?php

namespace App\Mail\ConsumerCalculatorResults;

use App\Models\EmailTemplate;
use App\Models\Odin\Consumer;
use App\Models\TestProduct;
use App\Services\EmailTemplates\EmailTemplateImageService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;
use Exception;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Envelope;

class ConsumerCalculatorResultsEmail extends Mailable
{
    use Queueable, SerializesModels;

    protected EmailTemplateImageService $emailTemplateImageService;

    /**
     * @param Consumer|TestProduct $consumer
     * @param EmailTemplate $emailTemplate
     * @param array $calculatorOutputs
     * @throws BindingResolutionException
     */
    public function __construct(protected Consumer|TestProduct $consumer, protected EmailTemplate $emailTemplate, protected array $calculatorOutputs)
    {
        $this->emailTemplateImageService = app()->make(EmailTemplateImageService::class);
    }

    /**
     * Get the message envelope.
     *
     * @return Envelope
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            from: new Address(config('mail.from.address'), config('mail.from.name')),
            replyTo: [
                new Address(config('mail.from.address'))
            ],
            subject: $this->replaceValues($this->emailTemplate->subject, $this->calculatorOutputs)
        );
    }

    /**
     * Get the message content definition.
     *
     * @return Content
     * @throws Exception
     */
    public function content(): Content
    {
        return new Content(
            markdown: 'emails.email-templates.calculator-results-template',
            with: [
                'header' => Str::markdown(
                    $this->replaceValues($this->emailTemplateImageService->shortcodesToHTMLImageTags(
                        $this->emailTemplate?->background?->header ?? ''
                    ), $this->calculatorOutputs),
                ),
                'footer' => Str::markdown(
                    $this->replaceValues($this->emailTemplateImageService->shortcodesToHTMLImageTags(
                        $this->emailTemplate?->background?->footer ?? ''
                    ), $this->calculatorOutputs)
                ),
                'content' => Str::markdown(
                    $this->replaceValues(
                        $this->emailTemplateImageService->shortcodesToHTMLImageTags(
                                $this->emailTemplate?->content
                        ), $this->calculatorOutputs
                    )
                ),
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments(): array
    {
        return [];
    }

    /**
     * Find and replace the shortcodes
     *
     * @param string $value
     * @param array $calcOutputs
     * @return string
     */
    protected function replaceValues(string $value, array $calcOutputs): string
    {
        return preg_replace_callback("/{[0-z]+?}/", function ($match) use ($calcOutputs) {
            $key = preg_replace("/[{}\\\\]/", "", $match[0]);

            return array_key_exists($key, $calcOutputs) ? $calcOutputs[$key] : $match[0];
        }, $value);
    }
}
