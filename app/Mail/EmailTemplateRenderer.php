<?php

namespace App\Mail;

use Exception;
use Illuminate\Mail\Mailable;
use ReflectionException;

class EmailTemplateRenderer extends Mailable
{
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(
        private readonly string  $content,
        private readonly ?string $header = '',
        private readonly ?string $footer = '',
    ) {}

    /**
     * @return $this
     * @throws Exception
     */
    public function build(): self
    {
        return $this->markdown('emails.custom-user-markdown-template', [
            'header' => $this->header,
            'footer' => $this->footer,
            'content' => $this->content,
        ]);
    }

    /**
     * @throws ReflectionException
     */
    public function preview(): string
    {
        return $this->render();
    }
}
