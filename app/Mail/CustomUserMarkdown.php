<?php

namespace App\Mail;

use App\Services\EmailTemplates\EmailTemplateImageService;
use App\Workflows\Shortcodes\WorkflowShortcodeServiceFactory;
use App\Workflows\WorkflowPayload;
use Exception;
use Illuminate\Mail\Mailable;
use ReflectionException;

class CustomUserMarkdown extends Mailable
{
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(
        private array $data,
        private WorkflowPayload $payload,
        private WorkflowShortcodeServiceFactory $workflowShortcodeServiceFactory,
        private EmailTemplateImageService $emailTemplateImageService
    ) {}

    /**
     * @return $this
     * @throws Exception
     */
    public function build(): self
    {
        if(!isset($this->data['content']) || !isset($this->data['content_template_id'])) {
            throw new Exception(__METHOD__.": Missing email content/content template ID");
        }

        $this->data['content'] = ($this->workflowShortcodeServiceFactory::makeService(WorkflowShortcodeServiceFactory::DUMMY_DRIVER))->handle($this->data['content'], $this->payload);
        $this->data['content'] = $this->emailTemplateImageService->shortcodesToMarkdownImageTags($this->data['content'], $this->data['content_template_id']);

        if(!empty($this->data['header'])
        && !empty($this->data['background_template_id'])) {
            $this->data['header'] = $this->emailTemplateImageService->shortcodesToMarkdownImageTags($this->data['header'], $this->data['background_template_id']);
        }

        if(!empty($this->data['footer'])
        && !empty($this->data['background_template_id'])) {
            $this->data['footer'] = $this->emailTemplateImageService->shortcodesToMarkdownImageTags($this->data['footer'], $this->data['background_template_id']);
        }

        return $this->markdown('emails.custom-user-markdown-template', $this->data);
    }

    /**
     * @throws ReflectionException
     */
    public function preview(): string
    {
        return $this->render();
    }
}
