<?php

namespace App\Mail;

use App\Models\Legacy\EloquentCompany;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class PunterRecordedInterest extends Mailable
{
    const DEFAULT_FROM_ADDRESS_SOLAR   = '<EMAIL>';
    const DEFAULT_FROM_ADDRESS_ROOFING = '<EMAIL>';

    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(
        public string           $industry = "solar",
        public ?EloquentCompany $company = null,
        public ?int             $leadId = null,
        public ?string          $name = null,
        public ?string          $phone = null,
        public ?string          $email = null,
    ) {}

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->from($this->getFromAddress())
            ->subject($this->getSubject())
            ->view('emails.punter-recorded-interest');
    }

    /**
     * Returns the from address for this email.
     *
     * @return string
     */
    protected function getFromAddress(): string
    {
        return match ($this->industry) {
            "roofing" => self::DEFAULT_FROM_ADDRESS_ROOFING,
            default => self::DEFAULT_FROM_ADDRESS_SOLAR,
        };
    }

    /**
     * Returns the subject for this email.
     *
     * @return string
     */
    protected function getSubject(): string
    {
        if ($this->company !== null)
            return "Company ({$this->company->companyname}) has expressed interest in lead {$this->leadId}";

        return "Company has expressed interest in lead {$this->leadId}";
    }
}
