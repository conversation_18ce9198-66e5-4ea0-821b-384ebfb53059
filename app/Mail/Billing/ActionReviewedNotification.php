<?php

namespace App\Mail\Billing;

use App\Models\Billing\ActionApproval;
use App\Models\User;
use App\Services\Billing\LinkService\BillingActionLinkService;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ActionReviewedNotification extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @param User $requester
     * @param ActionApproval $actionApproval
     * @param string $message
     * @param string $emailSubject
     */
    public function __construct(
        public User           $requester,
        public ActionApproval $actionApproval,
        public string         $message,
        public string         $emailSubject,
    )
    {
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): static
    {
        $approval = $this->actionApproval;

        $url = app(BillingActionLinkService::class)->getActionRequestLink(
            actionApproval:  $this->actionApproval
        );

        return $this->subject($this->emailSubject)
            ->markdown('emails.billing.action-reviewed', [
                'requester' => $this->requester,
                'message'   => $this->message,
                'reason'    => $approval->reason ? $approval->reason : 'None Given',
                'url'       => $url,
            ]);
    }
}
