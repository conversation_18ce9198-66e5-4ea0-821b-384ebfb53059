<?php

namespace App\Mail\Billing;

use App\Models\Billing\ActionApproval;
use App\Models\User;
use App\Services\Billing\LinkService\BillingActionLinkService;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ActionRequestedNotification extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @param User $reviewer
     * @param string $emailSubject
     * @param string $message
     * @param ActionApproval $actionApproval
     */
    public function __construct(
        public User           $reviewer,
        public string         $emailSubject,
        public string         $message,
        public ActionApproval $actionApproval,
    )
    {}

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): static
    {
        $url = app(BillingActionLinkService::class)->getActionRequestLink(
            actionApproval:  $this->actionApproval
        );

        return $this->subject($this->emailSubject)
            ->markdown('emails.billing.action-requested', [
                'reviewer' => $this->reviewer,
                'url' => $url,
                'message' => $this->message,
        ]);
    }
}
