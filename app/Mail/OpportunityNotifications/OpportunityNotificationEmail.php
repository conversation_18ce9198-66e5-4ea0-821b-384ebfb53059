<?php

namespace App\Mail\OpportunityNotifications;

use App\Models\EmailTemplate;
use App\Models\MissedProducts\OpportunityNotificationConfig;
use App\Models\Odin\CompanyUser;
use App\Services\EmailTemplates\EmailTemplateImageService;
use App\Services\Shortcode\ShortcodeImplementation\MissedProductsShortcodeUseCase;
use App\Services\Shortcode\ShortcodeReplacerService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;

class OpportunityNotificationEmail extends Mailable
{
    use Queueable, SerializesModels;

    const string DEFAULT_SUBJECT_LINE = "Available Leads In Your Area";

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(
        protected CompanyUser $companyUser,
        protected EmailTemplate $template,
        protected OpportunityNotificationConfig $notificationConfig,
        protected EmailTemplateImageService $emailTemplateImageService,
        protected ShortcodeReplacerService $shortcodeReplacerService,
    ) {}

    /**
     * Build the message.
     *
     * @return $this
     * @throws Exception
     */
    public function build(): Mailable
    {
        $contentArray = [
            'subject' => $this->getSubject($this->template),
            'header' => $this->template->background->header,
            'footer' => $this->template->background->footer,
            'content' => Str::markdown($this->template->content)
        ];

        $shortcodeMap = (new MissedProductsShortcodeUseCase(
            $this->companyUser->company,
            $this->companyUser,
            $this->notificationConfig
        ))->compile()->toArray();

        foreach ($contentArray as &$text) {
            if (!empty($text))
                $text = $this->shortcodeReplacerService->process($this->emailTemplateImageService->shortcodesToHTMLImageTags($text), $shortcodeMap);
        }

        return $this
            ->from(config('mail.from.address'), config('mail.from.name'))
            ->replyTo(config('mail.from.address'))
            ->subject($contentArray['subject'])
            ->view('emails.opportunitynotifications.layout', [
                'header' => Str::markdown($contentArray['header']),
                'footer' => Str::markdown($contentArray['footer']),
                'content' => $contentArray['content']
            ]);
    }

    /**
     * Returns the subject for a given lead.
     *
     * @param EmailTemplate $template
     * @return string
     */
    protected function getSubject(EmailTemplate $template): string
    {
        return strlen(trim($template->{EmailTemplate::FIELD_SUBJECT})) > 0
            ? $template->{EmailTemplate::FIELD_SUBJECT}
            : self::DEFAULT_SUBJECT_LINE;
    }
}
