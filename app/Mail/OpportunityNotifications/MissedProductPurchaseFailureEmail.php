<?php

namespace App\Mail\OpportunityNotifications;

use App\Abstracts\Workflows\WorkflowShortcodeServiceAbstract;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Factories\Workflows\WorkflowPayloadFactory;
use App\Models\EmailTemplate;
use App\Models\MissedProducts\MissedProductConfig;
use App\Models\Odin\CompanyUser;
use App\Services\EmailTemplates\EmailTemplateImageService;
use App\Workflows\Shortcodes\WorkflowShortcodeServiceFactory;
use App\Workflows\WorkflowEvent;
use App\Workflows\WorkflowPayload;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;

class MissedProductPurchaseFailureEmail extends Mailable
{
    use Queueable, SerializesModels;

    protected EmailTemplate $emailTemplate;
    protected WorkflowShortcodeServiceAbstract $shortCodeService;
    protected EmailTemplateImageService $emailTemplateImageService;

    /**
     * Create a new message instance.
     *
     * @param CompanyUser $companyUser
     *
     * @throws BindingResolutionException
     * @throws Exception
     */
    public function __construct(protected CompanyUser $companyUser)
    {
        $this->emailTemplate = $this->getEmailTemplate();
        $this->shortCodeService = WorkflowShortcodeServiceFactory::makeService(WorkflowShortcodeServiceFactory::WORKFLOW_DRIVER);
        $this->emailTemplateImageService = app()->make(EmailTemplateImageService::class);
    }

    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            from: new Address(config('mail.from.address'), config('mail.from.name')),
            replyTo: [
                new Address(config('mail.from.address'))
            ],
            subject: $this->shortCodeService->handle($this->emailTemplate->subject, $this->getPayloadForShortcode())
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     * @throws Exception
     */
    public function content(): Content
    {
        return new Content(
            markdown: 'emails.opportunitynotifications.layout',
            with: [
                'header' => Str::markdown(
                    $this->shortCodeService->handle($this->emailTemplateImageService->shortcodesToHTMLImageTags(
                        $this->emailTemplate->background?->header ?? ''
                    ), $this->getPayloadForShortcode())
                ),
                'footer' => Str::markdown(
                    $this->shortCodeService->handle($this->emailTemplateImageService->shortcodesToHTMLImageTags(
                        $this->emailTemplate->background?->footer ?? ''
                    ), $this->getPayloadForShortcode())
                ),
                'content' => Str::markdown(
                    $this->shortCodeService->handle($this->emailTemplate->content, $this->getPayloadForShortcode())
                )
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments(): array
    {
        return [];
    }

    /**
     * @return EmailTemplate
     */
    protected function getEmailTemplate(): EmailTemplate
    {
        /** @var MissedProductConfig $missedProductConfig */
        $missedProductConfig = MissedProductConfig::query()->firstOrFail();

        /** @var EmailTemplate */
        return EmailTemplate::query()->findOrFail($missedProductConfig->purchase_fail_email_template_id);
    }

    /**
     * @return WorkflowPayload
     */
    protected function getPayloadForShortcode(): WorkflowPayload
    {
        return WorkflowPayloadFactory::create(WorkflowEvent::fromArray([
            "event_category" => EventCategory::COMPANIES->value,
            "event_name" => EventName::NOTIFICATION_SENT->value,
            "event_data" => [
                'company_reference' => $this->companyUser->company->reference,
                'company_contact' => $this->companyUser
            ]
        ]));
    }
}
