<?php

namespace App\Builder\Billing;

use App\Enums\Billing\InvoiceStates;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceItem;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\Legacy\EloquentInvoice;
use App\Models\Legacy\EloquentInvoiceItem;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Odin\Company;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCancellation;
use App\Models\Odin\ProductRejection;
use App\Services\DatabaseHelperService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class CompanyRevenueBuilder
{
    const string GROUP_DAILY   = "daily";
    const string GROUP_MONTHLY = "monthly";
    const string GROUP_YEARLY  = "yearly";

    const string PERIOD_ALL_TIME = "all-time";


    public function __construct(
        protected ?int $companyId = null,
        protected ?int $industryId = null,
        protected string $groupBy = self::GROUP_MONTHLY,
        protected string $period = self::PERIOD_ALL_TIME,
        protected int $duration = 1,
    )
    {
    }

    /**
     * Returns a new instance of this builder.
     *
     * @return CompanyRevenueBuilder
     */
    public static function query(): CompanyRevenueBuilder
    {
        return new CompanyRevenueBuilder();
    }

    /**
     * Sets the grouping this builder should group by
     *
     * @param string $groupBy
     * @return $this
     */
    public function groupBy(string $groupBy = self::GROUP_MONTHLY): self
    {
        $this->groupBy = $groupBy;

        return $this;
    }

    /**
     * Sets the period type this builder should operate on.
     *
     * @param string $period
     * @return $this
     */
    public function setPeriod(string $period = self::PERIOD_ALL_TIME): self
    {
        $this->period = $period;

        return $this;
    }

    /**
     * @param int|null $companyId
     * @return $this
     */
    public function setCompanyId(?int $companyId = null): self
    {
        $this->companyId = $companyId;

        return $this;
    }

    /**
     * @param int|null $industryId
     * @return $this
     */
    public function setIndustryId(?int $industryId = null): self
    {
        $this->industryId = $industryId;

        return $this;
    }

    /**
     * Sets the duration this builder should operate on.
     *
     * @param int $duration
     * @return $this
     */
    public function setPeriodDuration(int $duration = 1): self
    {
        $this->duration = $duration;

        return $this;
    }

    /**
     * @return Builder
     */
    protected function getBaseQuery(): Builder
    {
        return ProductAssignment::query()
            ->leftJoin(InvoiceItem::TABLE, function ($join) {
                $join->on(InvoiceItem::TABLE . '.' . InvoiceItem::FIELD_BILLABLE_ID, ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID)
                    ->where(InvoiceItem::TABLE . '.' . InvoiceItem::FIELD_BILLABLE_TYPE, ProductAssignment::class);
            })
            ->leftJoin(
                Invoice::TABLE,
                Invoice::TABLE . '.' . Invoice::FIELD_ID,
                InvoiceItem::TABLE . '.' . InvoiceItem::FIELD_INVOICE_ID
            )
            ->leftJoin(
                DatabaseHelperService::readOnlyDatabase() . '.' . EloquentQuoteCompany::TABLE,
                DatabaseHelperService::readOnlyDatabase() . '.' . EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::QUOTE_COMPANY_ID,
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_LEGACY_ID
            )
            ->leftJoin(
                DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoiceItem::TABLE,
                DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::INVOICE_ITEM_ID,
                DatabaseHelperService::readOnlyDatabase() . '.' . EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::INVOICE_ITEM_ID,
            )
            ->leftJoin(
                DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoice::TABLE,
                DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoice::TABLE . '.' . EloquentInvoice::INVOICE_ID,
                DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::INVOICE_ID,
            )
            ->leftJoin(
                ProductRejection::TABLE,
                ProductRejection::TABLE . '.' . ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID,
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID
            )
            ->leftJoin(
                ProductCancellation::TABLE,
                ProductCancellation::TABLE . '.' . ProductCancellation::FIELD_PRODUCT_ASSIGNMENT_ID,
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID
            )
            ->leftJoin(
                Budget::TABLE,
                Budget::TABLE . '.' . Budget::FIELD_ID,
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_BUDGET_ID
            )
            ->leftJoin(
                BudgetContainer::TABLE,
                BudgetContainer::TABLE . '.' . BudgetContainer::FIELD_ID,
                Budget::TABLE . '.' . Budget::FIELD_BUDGET_CONTAINER_ID
            )
            ->leftJoin(
                CompanyCampaign::TABLE,
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID,
                BudgetContainer::TABLE . '.' . BudgetContainer::FIELD_COMPANY_CAMPAIGN_ID
            )
            ->leftJoin(
                IndustryService::TABLE,
                IndustryService::TABLE . '.' . IndustryService::FIELD_ID,
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_SERVICE_ID
            );
    }

    /**
     * @return string
     */
    protected function getRejectedSelect(): string
    {
        $prId = ProductRejection::TABLE . "." . ProductRejection::FIELD_ID;
        $prDeletedAt = ProductRejection::TABLE . "." . ProductRejection::FIELD_DELETED_AT;
        $paCost = ProductAssignment::TABLE . "." . ProductAssignment::FIELD_COST;

        return "(
		    CASE
			    when $prId is not null and $prDeletedAt is null
	            then $paCost
			    else 0
		    END
	    )";
    }

    /**
     * @return string
     */
    protected function getCancelledSelect(): string
    {
        $pcId = ProductCancellation::TABLE . "." . ProductCancellation::FIELD_ID;
        $pcDeletedAt = ProductCancellation::TABLE . "." . ProductCancellation::FIELD_DELETED_AT;
        $paCost = ProductAssignment::TABLE . "." . ProductAssignment::FIELD_COST;

        return "(
            CASE
                when $pcId is not null and $pcDeletedAt is null
	            then $paCost
	        	else 0
	        END
	    )";
    }


    /**
     * @return string
     */
    protected function getChargeableDeliveredSelect(): string
    {
        $paChargeable = ProductAssignment::TABLE . "." . ProductAssignment::FIELD_CHARGEABLE;
        $paDelivered = ProductAssignment::TABLE . "." . ProductAssignment::FIELD_DELIVERED;
        $paCost = ProductAssignment::TABLE . "." . ProductAssignment::FIELD_COST;
        $prId = ProductRejection::TABLE . "." . ProductRejection::FIELD_ID;
        $pcId = ProductCancellation::TABLE . "." . ProductRejection::FIELD_ID;

        return "(
            CASE
                when $paChargeable = true
                    and $paDelivered = true
                    and $prId is null
                    and $pcId is null
	            then $paCost
                else 0
            END
        )";
    }

    /**
     * Runs the builder.
     *
     * @return Collection
     */
    public function get(): Collection
    {
        $pa = ProductAssignment::TABLE;

        $period = $this->period;

        if ($this->groupBy === self::GROUP_MONTHLY && $this->duration === 1 && $this->period !== 'all-time') {
            $period = self::GROUP_DAILY;
        }

        $periodFormat = match ($period) {
            self::GROUP_DAILY  => '%Y-%m-%d',
            self::GROUP_YEARLY => '%Y',
            default            => '%M, %Y'
        };

        $query = $this->getBaseQuery()
            ->select([
                DB::raw($this->getA2PaidSelect() . " as a2_paid"),
                DB::raw($this->getLegacyPaidSelect() . " as legacy_paid"),
                DB::raw("(" . $this->getA2PaidSelect() . " + " . $this->getLegacyPaidSelect() . ") as total_paid"),
                DB::raw($this->getRejectedSelect() . " as total_rejected"),
                DB::raw($this->getCancelledSelect() . " as total_cancelled"),
                DB::raw($this->getChargeableDeliveredSelect() . " as total_chargeable_delivered"),
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID . ' as company_id',
                DB::raw("DATE_FORMAT($pa.created_at, '$periodFormat') as period")
            ])
            ->when(filled($this->companyId), function ($query) {
                $query->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID, $this->companyId);
            })
            ->when(filled($this->industryId), function ($query) {
                $query->where(IndustryService::TABLE . '.' . IndustryService::FIELD_INDUSTRY_ID, $this->industryId);
            })
            ->groupBy(
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CONSUMER_PRODUCT_ID
            );

        $query = $this->applyDuration($query);

        return DB::query()
            ->select([
                DB::raw('SUM(sub.a2_paid) as a2_paid'),
                DB::raw('SUM(sub.legacy_paid) as legacy_paid'),
                DB::raw('SUM(sub.total_paid) as total_paid'),
                DB::raw('SUM(sub.total_rejected) as total_rejected'),
                DB::raw('SUM(sub.total_cancelled) as total_cancelled'),
                DB::raw('SUM(sub.total_chargeable_delivered) as total_chargeable_delivered'),
                DB::raw('sub.company_id'),
                DB::raw('sub.period'),
            ])
            ->fromSub($query, 'sub')
            ->groupBy(
                'sub.company_id',
                'sub.period'
            )
            ->get();
    }

    /**
     * @return string
     */
    protected function getA2PaidSelect(): string
    {
        $invStatus = Invoice::TABLE . '.' . Invoice::FIELD_STATUS;
        $paidStatus = InvoiceStates::PAID->value;
        $paCost = ProductAssignment::TABLE . "." . ProductAssignment::FIELD_COST;

        return "(
            CASE
                when $invStatus = '$paidStatus'
                then $paCost
                else 0
            END
        )";
    }

    /**
     * @return string
     */
    protected function getLegacyPaidSelect(): string
    {
        $legacyInvoiceStatus = DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoice::TABLE . '.' . EloquentInvoice::STATUS;
        $paCost = ProductAssignment::TABLE . "." . ProductAssignment::FIELD_COST;

        return "(
            CASE
                when $legacyInvoiceStatus = 'paid'
                then $paCost
                else 0
            END
        )";
    }

    /**
     * @return array
     */
    public function getSummary(): array
    {
        $pa = ProductAssignment::TABLE;
        $i = Invoice::TABLE;

        $thirtyDaysAgo = Carbon::today()->subDays(29)->toIso8601String();
        $sixMonthsAgo = Carbon::today()->subMonths(6)->toIso8601String();
        $startOfYear = Carbon::today()->startOfYear()->toIso8601String();


        $chargeableDeliveredLast30Days = "
            (
                CASE
                    when $pa.delivered_at >= '$thirtyDaysAgo'
	                then " . ProductAssignment::TABLE . "." . ProductAssignment::FIELD_COST . "
                    else 0
                END
            )";

        $chargeableDeliveredLast6Months = "
            (
                CASE
                    when $i.status = 'paid' and $pa.delivered_at >= '$sixMonthsAgo'
	                then " . ProductAssignment::TABLE . "." . ProductAssignment::FIELD_COST . "
                    else 0
                END
            )
            ";

        $outstandingChargeableDeliveredAllTime = "
            (
                CASE
                    when ($i.id is null or $i.status <> 'paid') and $pa.chargeable = true and $pa.delivered = true
	                then " . ProductAssignment::TABLE . "." . ProductAssignment::FIELD_COST . "
                    else 0
                END
            )";

        $paidYearToDate = "
            (
                CASE
                    when $i.status = 'paid' and $pa.delivered_at >= '$startOfYear'
	                then " . ProductAssignment::TABLE . "." . ProductAssignment::FIELD_COST . "
                    else 0
                END
            )";

        $subquery = $this->getBaseQuery()
            ->select([
                DB::raw($this->getA2PaidSelect() . " as a2_all_time_paid"),
                DB::raw($this->getLegacyPaidSelect() . " as legacy_all_time_paid"),
                DB::raw("$chargeableDeliveredLast30Days as paid_last_30_days"),
                DB::raw("$chargeableDeliveredLast6Months as paid_last_6_months"),
                DB::raw($this->getChargeableDeliveredSelect() . " as chargeable_delivered_all_time"),
                DB::raw("$paidYearToDate as paid_year_to_date"),
                DB::raw("$outstandingChargeableDeliveredAllTime as outstanding_chargeable_delivered_all_time"),
                DB::raw($this->getRejectedSelect() . " as rejected_all_time"),
                DB::raw($this->getCancelledSelect() . " as cancelled_all_time"),
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID,
                Company::TABLE . '.' . Company::FIELD_NAME,
            ])
            ->when(filled($this->companyId), function ($query) {
                $query->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID, $this->companyId);
            })
            ->leftJoin(
                Company::TABLE,
                Company::TABLE . '.' . Company::FIELD_ID,
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID,
            )
            ->groupBy(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CONSUMER_PRODUCT_ID);

        $summary = DB::query()
            ->select([
                DB::raw('SUM(sub.a2_all_time_paid) AS a2_all_time_paid'),
                DB::raw('SUM(sub.legacy_all_time_paid) AS legacy_all_time_paid'),
                DB::raw('SUM(sub.a2_all_time_paid) + SUM(sub.legacy_all_time_paid)  AS paid_all_time'),
                DB::raw('SUM(sub.paid_last_30_days) AS paid_last_30_days'),
                DB::raw('SUM(sub.paid_last_6_months) AS paid_last_6_months'),
                DB::raw('SUM(sub.paid_year_to_date) AS paid_year_to_date'),
                DB::raw('SUM(sub.chargeable_delivered_all_time) AS chargeable_delivered_all_time'),
                DB::raw('SUM(sub.outstanding_chargeable_delivered_all_time) AS outstanding_chargeable_delivered_all_time'),
                DB::raw('SUM(sub.rejected_all_time) AS rejected_all_time'),
                DB::raw('SUM(sub.cancelled_all_time) AS cancelled_all_time'),
            ])
            ->fromSub($subquery, 'sub')
            ->groupBy('sub.company_id')
            ->first();

        return [
            'last_30_days_start_date'       => Carbon::parse($thirtyDaysAgo)->format('M j, Y h:iA e'),
            'last_6_months_start_date'      => Carbon::parse($sixMonthsAgo)->format('M j, Y h:iA e'),
            'start_of_year_start_date'      => Carbon::parse($startOfYear)->format('M j, Y h:iA e'),
            'paid_all_time'                 => $summary->paid_all_time,
            'last_30_days'                  => $summary->paid_last_30_days,
            'last_6_months'                 => $summary->paid_last_6_months,
            'year_to_date'                  => $summary->paid_year_to_date,
            'chargeable_delivered_all_time' => $summary->chargeable_delivered_all_time,
            'a2_all_time_paid'              => $summary->a2_all_time_paid,
            'legacy_all_time_paid'          => $summary->legacy_all_time_paid,
            'paid_last_30_days'             => $summary->paid_last_30_days,
            'paid_last_6_months'            => $summary->paid_last_6_months,
            'paid_year_to_date'             => $summary->paid_year_to_date,
            'rejected_all_time'             => $summary->rejected_all_time,
            'cancelled_all_time'            => $summary->cancelled_all_time,
        ];
    }

    /**
     * @return array
     */
    public function toGraph(): array
    {
        $data = $this->get();

        $labels = $data->pluck('period')->toArray();

        $chargeable_delivered_total = $data->pluck('total_chargeable_delivered');
        $paid_total = $data->pluck('total_paid');
        $rejected_total = $data->pluck('total_rejected');
        $cancelled = $data->pluck('total_cancelled');

        $datasetBaseStyle = [
            'fill'             => false,
            'pointBorderColor' => '#fff',
            'tension'          => 0.4,
            'pointRadius'      => 5,
            'pointHoverRadius' => 7,
            'borderWidth'      => 2
        ];

        return [
            'labels'   => $labels,
            'datasets' => [
                [
                    ...$datasetBaseStyle,
                    'label'                => 'Chargeable And Delivered',
                    'data'                 => $chargeable_delivered_total,
                    'borderColor'          => '#4f46e5', // Indigo 600
                    'backgroundColor'      => 'rgba(79, 70, 229, 0.2)',
                    'pointBackgroundColor' => '#4f46e5',
                ],
                [
                    ...$datasetBaseStyle,
                    'label'                => 'Paid',
                    'data'                 => $paid_total,
                    'borderColor'          => '#22c55e', // Green 500
                    'backgroundColor'      => 'rgba(34, 197, 94, 0.2)',
                    'pointBackgroundColor' => '#22c55e',
                ],
                [
                    ...$datasetBaseStyle,
                    'label'                => 'Rejected',
                    'data'                 => $rejected_total,
                    'borderColor'          => '#ef4444', // Red 500
                    'backgroundColor'      => 'rgba(239, 68, 68, 0.2)',
                    'pointBackgroundColor' => '#ef4444',
                ],
                [
                    ...$datasetBaseStyle,
                    'label'                => 'Cancelled',
                    'data'                 => $cancelled,
                    'borderColor'          => '#eab308', // Yellow 500
                    'backgroundColor'      => 'rgba(234, 179, 8, 0.2)',
                    'pointBackgroundColor' => '#eab308',
                ]
            ],
            'options'  => [
                'maintainAspectRatio' => false,
                'responsive'          => true,
                'interaction'         => [
                    'mode'      => 'index',
                    'intersect' => false
                ],
                'plugins'             => [
                    'legend'  => [
                        'labels' => [
                            'color' => '#6b7280', // Gray 500
                            'font'  => [
                                'size' => 14
                            ]
                        ]
                    ],
                    'tooltip' => [
                        'backgroundColor' => '#1f2937', // Gray 800
                        'titleColor'      => '#fff',
                        'bodyColor'       => '#d1d5db', // Gray 300
                        'padding'         => 12,
                        'cornerRadius'    => 8
                    ]
                ],
                'scales'              => [
                    'x' => [
                        'grid'  => [
                            'display' => false
                        ],
                        'ticks' => [
                            'color' => '#9ca3af' // Gray 400
                        ]
                    ],
                    'y' => [
                        'grid'  => [
                            'color' => 'rgba(156, 163, 175, 0.1)'
                        ],
                        'ticks' => [
                            'color' => '#9ca3af'
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * Applies the duration to the query.
     *
     * @param Builder $query
     * @return Builder
     */
    protected function applyDuration(Builder $query): Builder
    {
        return match ($this->period) {
            self::GROUP_DAILY   => $query->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CREATED_AT, '>=', Carbon::now()->startOfDay()->subDays($this->duration)),
            self::GROUP_MONTHLY => $query->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CREATED_AT, '>=', Carbon::now()->startOfMonth()->subMonths($this->duration)),
            self::GROUP_YEARLY  => $query->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CREATED_AT, '>=', Carbon::now()->startOfYear()->subYears($this->duration)),
            default             => $query
        };
    }
}
