<?php

namespace App\Mediators;

use App\Campaigns\Modules\DataModels\AllocateData;
use App\Campaigns\Modules\DataModels\PostAllocationData;
use App\ConsumerProcessing\Events\AssignmentDeliveryFailureEvent;
use App\Contracts\Campaigns\CampaignDefinitionContract;
use App\Contracts\Campaigns\CampaignModuleContract;
use App\Contracts\Campaigns\HasFrontendComponent;
use App\DataModels\Campaigns\ConsumerProject;
use App\DataModels\ProductAssignment\ProposedProductAssignment;
use App\Enums\Campaigns\CampaignStatus;
use App\Events\CompanyCampaign\PostAllocationEvent;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use Closure;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;

/**
 * <PERSON>les mediating a function call to the modules of campaigns.
 */
class CampaignMediator
{
    /**
     * Handles sending a closure through the modules of a campaign.
     *
     * @param CompanyCampaign $campaign
     * @param Closure $closure
     * @return bool
     * @throws BindingResolutionException
     */
    protected function pipeThroughModules(CompanyCampaign $campaign, Closure $closure): bool
    {
        return $campaign->type
            ->definition()
            ->modules()
            ->every($closure);
    }

    protected function pipeDataModelThroughModules(CompanyCampaign $campaign, mixed $model, Closure $closure): mixed
    {
        foreach($campaign->type->definition()->modules() as $module)
            $model = $closure($module, $model);

        return $model;
    }

    /**
     * Handles saving a given campaign with a payload.
     *
     * @param CompanyCampaign $campaign
     * @param Collection $payload
     * @return bool
     * @throws BindingResolutionException
     */
    public function save(CompanyCampaign $campaign, Collection $payload): bool
    {
        $status = $this->pipeThroughModules($campaign, fn(CampaignModuleContract $module) => $module->save($campaign, $payload));

        if (!$status)
            return false;

        return $campaign->save();
    }

    /**
     * Handles piping a status change through the modules.
     *
     * @param CompanyCampaign $campaign
     * @param CampaignStatus $old
     * @param CampaignStatus $new
     * @return bool
     * @throws BindingResolutionException
     */
    public function statusChange(CompanyCampaign $campaign, CampaignStatus $old, CampaignStatus $new): bool
    {
        return $this->pipeThroughModules(
            $campaign,
            fn(CampaignModuleContract $module) => $module->statusChange($campaign, $old, $new)
        );
    }

    /**
     * Handles piping deleting through the modules.
     *
     * @param CompanyCampaign $campaign
     * @return bool
     * @throws BindingResolutionException
     */
    public function delete(CompanyCampaign $campaign): bool
    {
        $status = $this->pipeThroughModules($campaign, fn(CampaignModuleContract $module) => $module->delete($campaign));

        if (!$status)
            return false;

        return $campaign->delete();
    }

    /**
     * Handles calling the prefilter L/C on a list of campaigns modules.
     *
     * @param Collection $campaigns
     * @param ConsumerProject $project
     * @return Collection
     * @throws BindingResolutionException
     */
    public function preFilter(Collection $campaigns, ConsumerProject $project): Collection
    {
        return $campaigns->filter(
            fn(CompanyCampaign $campaign) => $this->pipeThroughModules(
                $campaign,
                fn(CampaignModuleContract $module) => $module->preFilter($campaign, $project)
            )
        );
    }

    /**
     * Handles calling the filter L/C on a list of campaigns modules.
     *
     * @param Collection $campaigns
     * @param ConsumerProject $project
     * @return Collection
     */
    public function filter(Collection $campaigns, ConsumerProject $project): Collection
    {
        return $campaigns->filter(
            fn(CompanyCampaign $campaign) => $this->pipeThroughModules(
                $campaign,
                fn(CampaignModuleContract $module) => $module->filter($campaign, $project)
            )
        );
    }

    /**
     * Handles calling the allocate L/C on a list of campaigns modules.
     *
     * @param Collection<int, ProposedProductAssignment> $assignments
     * @return AllocateData
     */
    public function allocate(Collection $assignments): AllocateData
    {
        $data = new AllocateData();

        foreach($assignments as $assignment) {
            $data = $this->pipeDataModelThroughModules(
                $assignment->campaign(),
                $data,
                fn(CampaignModuleContract $module, AllocateData $data) => $module->allocate($assignment->campaign(), $assignment, $data)
            );
        }

        return $data;
    }

    /**
     * Handles calling the post allocate L/C on a list of campaigns modules.
     *
     * @param ConsumerProject $project
     * @param Collection<int, ProductAssignment> $productAssignments
     * @param bool $handleFailure
     *
     * @return PostAllocationData
     */
    public function postAllocation(ConsumerProject $project, Collection $productAssignments, bool $handleFailure = true): PostAllocationData
    {
        $data = new PostAllocationData();

        foreach($productAssignments as $productAssignment) {
            $data = $this->pipeDataModelThroughModules(
                $productAssignment->budget->budgetContainer->campaign,
                $data,
                fn(CampaignModuleContract $module, PostAllocationData $data) => $module->postAllocation($productAssignment->budget->budgetContainer->campaign, $productAssignment, $data)
            );
        }

        if($data->hasFailures() && $handleFailure)
            AssignmentDeliveryFailureEvent::dispatch($project, $data);


        PostAllocationEvent::dispatch($productAssignments);

        return $data;
    }

    /**
     * Handles calling the transform L/C on a collection of campaign modules
     *
     * @param Collection $campaigns
     * @return Collection
     * @throws BindingResolutionException
     */
    public function transform(Collection $campaigns): Collection
    {
        $transformedData = collect(array_fill_keys($campaigns->pluck(CompanyCampaign::FIELD_ID)->toArray(), collect()));

        $campaigns->each(
            fn(CompanyCampaign $campaign) => $this->pipeThroughModules(
                $campaign,
                function(CampaignModuleContract $module) use (&$transformedData, $campaign) {
                    $transformedData->put(
                        $campaign->{CompanyCampaign::FIELD_ID},
                        $transformedData->get($campaign->{CompanyCampaign::FIELD_ID})->mergeRecursive($module->transform($campaign))
                    );

                    return true;
                }
            )
        );

        return $transformedData;
    }

    /**
     * Handles calling the transform L/C on a single campaign for the specified modules
     *
     * @param CompanyCampaign $campaign
     * @param array $modules
     * @return Collection
     * @throws BindingResolutionException
     */
    public function transformSingle(CompanyCampaign $campaign, array $modules): Collection
    {
        $transformedData = collect();

        $this->pipeThroughModules(
            $campaign,
            function(CampaignModuleContract $module) use ($modules, &$transformedData, $campaign) {
                if($module instanceof HasFrontendComponent
                && in_array($module->getFrontendKey(), $modules)) {
                    $transformedData = $transformedData->mergeRecursive($module->transform($campaign));
                }

                return true;
            }
        );

        return $transformedData;
    }

    /**
     * Returns the frontend modules for a given campaign.
     *
     * @param CampaignDefinitionContract $campaign
     * @return Collection
     */
    public function getFrontendModules(CampaignDefinitionContract $campaign): Collection
    {
        $idx = 0;

        return $campaign->modules()
            ->filter(fn(CampaignModuleContract $module) => $module instanceof HasFrontendComponent)
            ->mapWithKeys(function(HasFrontendComponent $module) use (&$idx) {
                $idx++;

                return [$idx => $module->getFrontendKey()];
            })
            ->unique();
    }

    /**
     * Returns the frontend Wizard slide configurations for a given campaign.
     *
     * @param CampaignDefinitionContract $campaign
     * @return Collection
     */
    public function getFrontEndWizardConfiguration(CampaignDefinitionContract $campaign): Collection
    {
        return $campaign->frontendModules()
            ->map(fn(HasFrontendComponent $module) => $module->getFrontendWizardConfiguration())
            ->filter(fn($config) => $config);
    }

    /**
     * Returns the frontend sidebar module configurations for a given campaign.
     *
     * @param CampaignDefinitionContract $campaign
     * @return Collection
     */
    public function getFrontEndModuleConfiguration(CampaignDefinitionContract $campaign): Collection
    {
        return $campaign->frontendModules()
            ->map(fn(HasFrontendComponent $module) => $module->getFrontendModuleConfiguration())
            ->filter(fn($config) => $config);
    }
}
