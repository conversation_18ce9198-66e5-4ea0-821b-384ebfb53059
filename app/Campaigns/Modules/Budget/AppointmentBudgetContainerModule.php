<?php

namespace App\Campaigns\Modules\Budget;

use App\Contracts\Campaigns\HasFrontendComponent;
use App\Enums\Campaigns\Modules\Budget\BudgetType;
use App\Enums\Campaigns\Modules\Budget\ContainerType;
use App\Enums\Odin\Product;
use App\Enums\ProductAssignment\BudgetProductConfigurationEnum;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use Illuminate\Support\Collection;

class AppointmentBudgetContainerModule extends BaseBudgetContainerModule implements HasFrontendComponent
{
    const MODULE_FRONTEND_NAME = 'Budget';
    const PAYLOAD_ONLINE       = 'online';
    const PAYLOAD_IN_HOME      = 'in_home';
    const PAYLOAD_OPTIONS      = 'options';

    /** @inheritDoc */
    protected static function getContainerType(): ContainerType
    {
        return ContainerType::APPOINTMENTS;
    }

    /** @inheritDoc */
    protected static function getBudgetSchemasWithDefaults(): array
    {
        return [
            [
                Budget::FIELD_DISPLAY_NAME          => 'In-Home',
                Budget::FIELD_KEY                   => 'in_home',
                Budget::FIELD_STATUS                => Budget::STATUS_ENABLED,
                Budget::FIELD_TYPE                  => BudgetType::NO_LIMIT,
                Budget::FIELD_VALUE                 => 0,
                Budget::FIELD_PRODUCT_CONFIGURATION => BudgetProductConfigurationEnum::APPOINTMENT_IN_HOME
            ],
            [
                Budget::FIELD_DISPLAY_NAME          => 'Online',
                Budget::FIELD_KEY                   => 'online',
                Budget::FIELD_STATUS                => Budget::STATUS_ENABLED,
                Budget::FIELD_TYPE                  => BudgetType::NO_LIMIT,
                Budget::FIELD_VALUE                 => 0,
                Budget::FIELD_PRODUCT_CONFIGURATION => BudgetProductConfigurationEnum::APPOINTMENT_ONLINE
            ],
        ];
    }

    /**
     * @param CompanyCampaign $campaign
     * @return Collection
     */
    public function transform(CompanyCampaign $campaign): Collection
    {
        $budgetData = $this->getModel($campaign)->budgets->keyBy('key');

        return collect([
            self::PAYLOAD_PARENT_KEY => [
                self::PAYLOAD_BUDGETS => $budgetData
            ],
        ]);
    }

    /**
     * @inheritDoc
     */
    public static function getBudgetConfigurations(): array
    {
        $budgetConfigs = self::getBudgetSchemasWithDefaults();
        $requiredConfigs = [];
        $optionalConfigs = [];

        foreach ($budgetConfigs as $config) {
            $config[Budget::FIELD_PRODUCT_CONFIGURATION] = $config[Budget::FIELD_PRODUCT_CONFIGURATION]->getConfig()[Product::APPOINTMENT->value];
            if ($config[self::PAYLOAD_REQUIRED] ?? null)
                $requiredConfigs[] = $config;
            else
                $optionalConfigs[] = $config;
        }

        return [
            self::PAYLOAD_REQUIRED  => $requiredConfigs,
            self::PAYLOAD_OPTIONAL  => $optionalConfigs,
        ];
    }

    /**
     * @inheritDoc
     */
    public function getFrontendKey(): string
    {
        return self::PAYLOAD_PARENT_KEY;
    }

    /**
     * @inheritDoc
     */
    public function getFrontendModuleConfiguration(): ?array
    {
        return null;
    }

    /**
     * @inheritDoc
     */
    public function getFrontendWizardConfiguration(): ?array
    {
        $budgetKeys = [
            Budget::FIELD_TYPE   => [
                self::INPUT_KEY_VALIDATION => "numeric|min:0|max:2",
            ],
            Budget::FIELD_STATUS => [
                self::INPUT_KEY_VALIDATION => 'boolean',
            ],
            Budget::FIELD_VALUE  => [
                self::INPUT_KEY_VALIDATION => 'numeric|min:0',
            ],
            Budget::FIELD_KEY  => [
                self::INPUT_KEY_VALIDATION => 'string',
            ],
        ];

        return [
            self::WIZARD_KEY_NAME       => self::MODULE_FRONTEND_NAME,
            self::WIZARD_KEY_MODULE_KEY => self::PAYLOAD_PARENT_KEY,
            self::WIZARD_KEY_INPUTS     => [
                self::PAYLOAD_BUDGETS => [
                    self::INPUT_KEY_VALIDATION  => 'object',
                    self::INPUT_CONTAINED_KEYS => [
                        self::PAYLOAD_IN_HOME => [
                            self::INPUT_KEY_VALIDATION   => "object",
                            self::INPUT_KEY_NAME         => 'In Home',
                            self::INPUT_CONTAINED_KEYS   => $budgetKeys
                        ],
                        self::PAYLOAD_ONLINE => [
                            self::INPUT_KEY_VALIDATION   => "object",
                            self::INPUT_KEY_NAME         => 'Online',
                            self::INPUT_CONTAINED_KEYS   => $budgetKeys,
                        ],
                    ],
                ],
            ],
        ];
    }
}
