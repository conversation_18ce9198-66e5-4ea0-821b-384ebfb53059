<?php

namespace App\Campaigns\Modules\Budget;

use App\Contracts\Campaigns\HasFrontendComponent;
use App\Enums\Campaigns\CustomCampaignBudgetType;
use App\Enums\Campaigns\Modules\Budget\BudgetType;
use App\Enums\ProductAssignment\BudgetProductConfigurationEnum;
use App\Models\Campaigns\Modules\Budget\Budget;

class UnverifiedOnlyLeadBudgetContainerModule extends LeadBudgetContainerModule implements HasFrontendComponent
{
    const string PAYLOAD_EXCLUSIVE_ONLY = CustomCampaignBudgetType::EXCLUSIVE_ONLY_LEADS->value;

    /** @inheritDoc */
    protected static function getBudgetSchemasWithDefaults(): array
    {
        return [
            [
                Budget::FIELD_DISPLAY_NAME          => 'Unverified',
                Budget::FIELD_KEY                   => CustomCampaignBudgetType::UNVERIFIED_ONLY_LEADS->value,
                Budget::FIELD_STATUS                => Budget::STATUS_ENABLED,
                Budget::FIELD_TYPE                  => BudgetType::NO_LIMIT->value,
                Budget::FIELD_VALUE                 => 0,
                Budget::FIELD_PRODUCT_CONFIGURATION => BudgetProductConfigurationEnum::LEAD_UNVERIFIED,
                self::PAYLOAD_REQUIRED              => true,
            ],
        ];
    }

    /**
     * @inheritDoc
     */
    public static function getBudgetConfigurations(): array
    {
        return self::transformLeadBudgetConfigurations(self::getBudgetSchemasWithDefaults());
    }

    /**
     * @inheritDoc
     */
    public function getFrontendWizardConfiguration(): ?array
    {
        return [
            self::WIZARD_KEY_NAME       => self::MODULE_FRONTEND_NAME,
            self::WIZARD_KEY_MODULE_KEY => self::PAYLOAD_PARENT_KEY,
            self::WIZARD_KEY_INPUTS     => [
                self::PAYLOAD_BUDGETS => [
                    self::INPUT_KEY_VALIDATION  => 'object',
                    self::INPUT_CONTAINED_KEYS => [
                        self::PAYLOAD_EXCLUSIVE_ONLY => [
                            self::INPUT_KEY_VALIDATION   => "object",
                            self::INPUT_KEY_NAME         => 'Unverified Leads Budget',
                            self::INPUT_CONTAINED_KEYS   => $this->standardFrontendBudgetKeys,
                        ],
                    ],
                ],
            ],
        ];
    }
}
