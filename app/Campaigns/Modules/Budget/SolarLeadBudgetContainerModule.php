<?php

namespace App\Campaigns\Modules\Budget;

use App\Enums\Campaigns\Modules\Budget\BudgetType;
use App\Enums\ProductAssignment\BudgetProductConfigurationEnum;
use App\Models\Campaigns\Modules\Budget\Budget;

class SolarLeadBudgetContainerModule extends LeadBudgetContainerModule
{
    /** @inheritDoc */
    protected static function getBudgetSchemasWithDefaults(): array
    {
        return [
            [
                Budget::FIELD_DISPLAY_NAME          => 'Verified',
                Budget::FIELD_KEY                   => 'verified',
                Budget::FIELD_STATUS                => Budget::STATUS_ENABLED,
                Budget::FIELD_TYPE                  => BudgetType::NO_LIMIT,
                Budget::FIELD_VALUE                 => 0,
                Budget::FIELD_PRODUCT_CONFIGURATION => BudgetProductConfigurationEnum::LEAD_VERIFIED,
                self::PAYLOAD_REQUIRED              => true,
            ],
            [
                Budget::FIELD_DISPLAY_NAME          => 'Unverified',
                Budget::FIELD_KEY                   => 'unverified',
                Budget::FIELD_STATUS                => Budget::STATUS_DISABLED,
                Budget::FIELD_TYPE                  => BudgetType::NO_LIMIT,
                Budget::FIELD_VALUE                 => 0,
                Budget::FIELD_PRODUCT_CONFIGURATION => BudgetProductConfigurationEnum::LEAD_UNVERIFIED
            ],
            [
                Budget::FIELD_DISPLAY_NAME          => 'Email Only',
                Budget::FIELD_KEY                   => 'email_only',
                Budget::FIELD_STATUS                => Budget::STATUS_DISABLED,
                Budget::FIELD_TYPE                  => BudgetType::NO_LIMIT,
                Budget::FIELD_VALUE                 => 0,
                Budget::FIELD_PRODUCT_CONFIGURATION => BudgetProductConfigurationEnum::LEAD_EMAIL_ONLY
            ],
        ];
    }

    /**
     * @inheritDoc
     */
    public static function getBudgetConfigurations(): array
    {
        return self::transformLeadBudgetConfigurations(self::getBudgetSchemasWithDefaults());
    }
}
