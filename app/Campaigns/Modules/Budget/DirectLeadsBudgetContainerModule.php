<?php

namespace App\Campaigns\Modules\Budget;

use App\Contracts\Campaigns\HasFrontendComponent;
use App\Enums\Campaigns\Modules\Budget\BudgetType;
use App\Enums\Campaigns\Modules\Budget\ContainerType;
use App\Enums\Odin\Product;
use App\Enums\ProductAssignment\BudgetProductConfigurationEnum;
use App\Models\Campaigns\Modules\Budget\Budget;

class DirectLeadsBudgetContainerModule extends BaseBudgetContainerModule implements HasFrontendComponent
{
    const string MODULE_FRONTEND_NAME = 'Budget';
    const string PAYLOAD_VERIFIED = 'verified';

    #[\Override] protected static function getBudgetSchemasWithDefaults(): array
    {
        return [
            [
                Budget::FIELD_DISPLAY_NAME          => 'Verified',
                Budget::FIELD_KEY                   => 'verified',
                Budget::FIELD_STATUS                => Budget::STATUS_ENABLED,
                Budget::FIELD_TYPE                  => BudgetType::NO_LIMIT,
                Budget::FIELD_VALUE                 => 0,
                Budget::FIELD_PRODUCT_CONFIGURATION => BudgetProductConfigurationEnum::LEAD_VERIFIED,
                self::PAYLOAD_REQUIRED              => true,
            ],
        ];
    }

    #[\Override] public static function getBudgetConfigurations(): array
    {
        $requiredConfigs = [];
        $optionalConfigs = [];

        foreach (self::getBudgetSchemasWithDefaults() as $config) {
            $config[Budget::FIELD_PRODUCT_CONFIGURATION] = $config[Budget::FIELD_PRODUCT_CONFIGURATION]->getConfig()[Product::LEAD->value];
            if ($config[self::PAYLOAD_REQUIRED] ?? null)
                $requiredConfigs[] = $config;
            else
                $optionalConfigs[] = $config;
        }

        return [
            self::PAYLOAD_REQUIRED  => $requiredConfigs,
            self::PAYLOAD_OPTIONAL  => $optionalConfigs,
        ];
    }

    #[\Override] protected static function getContainerType(): ContainerType
    {
        return ContainerType::DIRECT_LEADS;
    }

    #[\Override] public function getFrontendKey(): string
    {
        return self::PAYLOAD_PARENT_KEY;
    }

    #[\Override] public function getFrontendModuleConfiguration(): ?array
    {
        return null;
    }

    #[\Override] public function getFrontendWizardConfiguration(): ?array
    {
        $budgetKeys = [
            Budget::FIELD_TYPE   => [
                self::INPUT_KEY_VALIDATION => "numeric|min:0|max:2",
            ],
            Budget::FIELD_STATUS => [
                self::INPUT_KEY_VALIDATION => 'boolean',
            ],
            Budget::FIELD_VALUE  => [
                self::INPUT_KEY_VALIDATION => 'numeric|min:0',
            ],
            Budget::FIELD_KEY  => [
                self::INPUT_KEY_VALIDATION => 'string',
            ],
        ];

        return [
            self::WIZARD_KEY_NAME       => self::MODULE_FRONTEND_NAME,
            self::WIZARD_KEY_MODULE_KEY => self::PAYLOAD_PARENT_KEY,
            self::WIZARD_KEY_INPUTS     => [
                self::PAYLOAD_BUDGETS => [
                    self::INPUT_KEY_VALIDATION => 'object',
                    self::INPUT_CONTAINED_KEYS => [
                        self::PAYLOAD_VERIFIED => [
                            self::INPUT_KEY_VALIDATION => "object",
                            self::INPUT_KEY_NAME       => 'Verified Leads Budget',
                            self::INPUT_CONTAINED_KEYS => $budgetKeys
                        ],
                    ],
                ],
            ]
        ];
    }
}
