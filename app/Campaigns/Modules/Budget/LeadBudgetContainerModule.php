<?php

namespace App\Campaigns\Modules\Budget;

use App\Contracts\Campaigns\HasFrontendComponent;
use App\Enums\Campaigns\Modules\Budget\BudgetType;
use App\Enums\Campaigns\Modules\Budget\ContainerType;
use App\Enums\Odin\Product;
use App\Models\Campaigns\CompanyCampaign;
use App\Enums\ProductAssignment\BudgetProductConfigurationEnum;
use App\Models\Campaigns\Modules\Budget\Budget;
use Illuminate\Support\Collection;

class LeadBudgetContainerModule extends BaseBudgetContainerModule implements HasFrontendComponent
{
    const MODULE_FRONTEND_NAME = 'Budget';
    const PAYLOAD_VERIFIED     = 'verified';
    const PAYLOAD_UNVERIFIED   = 'unverified';
    const PAYLOAD_EMAIL_ONLY   = 'email_only';
    const PAYLOAD_OPTIONS      = 'options';

    /** @inheritDoc */
    protected static function getContainerType(): ContainerType
    {
        return ContainerType::LEADS;
    }

    /** @inheritDoc */
    protected static function getBudgetSchemasWithDefaults(): array
    {
        return [
            [
                Budget::FIELD_DISPLAY_NAME          => 'Verified',
                Budget::FIELD_KEY                   => 'verified',
                Budget::FIELD_STATUS                => Budget::STATUS_ENABLED,
                Budget::FIELD_TYPE                  => BudgetType::NO_LIMIT,
                Budget::FIELD_VALUE                 => 0,
                Budget::FIELD_PRODUCT_CONFIGURATION => BudgetProductConfigurationEnum::LEAD_VERIFIED,
                self::PAYLOAD_REQUIRED              => true,
            ],
            [
                Budget::FIELD_DISPLAY_NAME          => 'Unverified',
                Budget::FIELD_KEY                   => 'unverified',
                Budget::FIELD_STATUS                => Budget::STATUS_DISABLED,
                Budget::FIELD_TYPE                  => BudgetType::NO_LIMIT,
                Budget::FIELD_VALUE                 => 0,
                Budget::FIELD_PRODUCT_CONFIGURATION => BudgetProductConfigurationEnum::LEAD_UNVERIFIED
            ],
        ];
    }

    /**
     * @inheritDoc
     */
    public static function getBudgetConfigurations(): array
    {
        return self::transformLeadBudgetConfigurations(self::getBudgetSchemasWithDefaults());
    }

    /**
     * @param array|null $budgetConfigs
     * @return array[]
     */
    protected static function transformLeadBudgetConfigurations(array $budgetConfigs = null): array
    {
        $requiredConfigs = [];
        $optionalConfigs = [];

        foreach ($budgetConfigs as $config) {
            $config[Budget::FIELD_PRODUCT_CONFIGURATION] = $config[Budget::FIELD_PRODUCT_CONFIGURATION]->getConfig()[Product::LEAD->value];
            if ($config[self::PAYLOAD_REQUIRED] ?? null)
                $requiredConfigs[] = $config;
            else
                $optionalConfigs[] = $config;
        }

        return [
            self::PAYLOAD_REQUIRED  => $requiredConfigs,
            self::PAYLOAD_OPTIONAL  => $optionalConfigs,
        ];
    }

    /**
     * @param CompanyCampaign $campaign
     * @return Collection
     */
    public function transform(CompanyCampaign $campaign): Collection
    {
        $budgetData = $this->getModel($campaign)->budgets->keyBy('key');

        return collect([
            self::PAYLOAD_PARENT_KEY => [
                self::PAYLOAD_BUDGETS => $budgetData
            ],
        ]);
    }

    /**
     * @inheritDoc
     */
    public function getFrontendKey(): string
    {
        return self::PAYLOAD_PARENT_KEY;
    }

    /**
     * @inheritDoc
     */
    public function getFrontendModuleConfiguration(): ?array
    {
        return null;
    }

    /**
     * @inheritDoc
     */
    public function getFrontendWizardConfiguration(): ?array
    {
        return [
            self::WIZARD_KEY_NAME       => self::MODULE_FRONTEND_NAME,
            self::WIZARD_KEY_MODULE_KEY => self::PAYLOAD_PARENT_KEY,
            self::WIZARD_KEY_INPUTS     => [
                self::PAYLOAD_BUDGETS => [
                    self::INPUT_KEY_VALIDATION  => 'object',
                    self::INPUT_CONTAINED_KEYS => [
                        self::PAYLOAD_VERIFIED => [
                            self::INPUT_KEY_VALIDATION   => "object",
                            self::INPUT_KEY_NAME         => 'Verified Leads Budget',
                            self::INPUT_CONTAINED_KEYS   => $this->standardFrontendBudgetKeys,
                        ],
                        self::PAYLOAD_UNVERIFIED => [
                            self::INPUT_KEY_VALIDATION   => "object",
                            self::INPUT_KEY_NAME         => 'Unverified',
                            self::INPUT_CONTAINED_KEYS   => $this->standardFrontendBudgetKeys,
                        ],
                        self::PAYLOAD_EMAIL_ONLY => [
                            self::INPUT_KEY_VALIDATION   => "object",
                            self::INPUT_KEY_NAME         => 'Email Only',
                            self::INPUT_CONTAINED_KEYS   => $this->standardFrontendBudgetKeys,
                        ],
                    ],
                ],
            ],
        ];
    }
}
