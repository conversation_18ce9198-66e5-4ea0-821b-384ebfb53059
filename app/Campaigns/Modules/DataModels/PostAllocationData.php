<?php

namespace App\Campaigns\Modules\DataModels;

use App\Campaigns\Delivery\DeliveryType;
use App\Models\Odin\ProductAssignment;
use Illuminate\Support\Collection;

class PostAllocationData
{

    const STATUS        = 'status';
    const MESSAGE       = 'message';
    const DELIVERY_TYPE = 'type';
    const COMPANY_ID    = 'company_id';

    const CAMPAIGN_ID   = 'campaign_id';

    public function __construct(private ?Collection $assignmentStatuses = null)
    {
        if (!$this->assignmentStatuses)
            $this->assignmentStatuses = collect();
    }

    /**
     * @return bool
     */
    public function hasFailures(): bool
    {
        return $this->assignmentStatuses->some(fn(array $assignmentData) => !$assignmentData[self::STATUS]);
    }

    /**
     * Return the number of companies successfully delivered to at least once
     *
     * @return int
     */
    public function companiesDeliveredTo(): int
    {
        return $this->assignmentStatuses->filter(fn(array $assignmentData) => $assignmentData[self::STATUS])
            ->pluck(self::COMPANY_ID)
            ->unique()
            ->count();
    }

    /**
     * Return the IDS of companies where all CRM deliveries failed
     *
     * @return array
     */
    public function getPotentialCRMRejections(): array
    {
        $companyScoped = $this->assignmentStatuses->groupBy(self::COMPANY_ID);
        $potentialRejections = $companyScoped->filter(fn(Collection $data) => $this->hadCRMFailures($data));

        return $potentialRejections->keys()->toArray();
    }

    /**
     * @param Collection $companyAllocationData
     * @return bool
     */
    private function hadCRMFailures(Collection $companyAllocationData): bool
    {
        return $companyAllocationData->some(fn(array $assignmentData) => !$assignmentData[self::STATUS] && ($assignmentData[self::DELIVERY_TYPE] ?? null) === DeliveryType::CRM->value);
    }

    /**
     * @param ProductAssignment $assignment
     * @param DeliveryType $type
     * @param string $message
     * @return self
     */
    public function logFailedAssignment(ProductAssignment $assignment, DeliveryType $type, string $message): self
    {
        $this->assignmentStatuses->push([
            self::COMPANY_ID    => $assignment->company_id,
            self::DELIVERY_TYPE => $type->value,
            self::STATUS        => false,
            self::MESSAGE       => $message,
            self::CAMPAIGN_ID   => $assignment->budget->budgetContainer->campaign->id
        ]);
        return $this;
    }

    /**
     * @param ProductAssignment $assignment
     * @return self
     */
    public function logSuccessfulAssignment(ProductAssignment $assignment): self
    {
        $this->assignmentStatuses->push([
            self::COMPANY_ID => $assignment->company_id,
            self::STATUS => true,
            self::CAMPAIGN_ID => $assignment->budget->budgetContainer->campaign->id
        ]);
        return $this;
    }

    /**
     * @return Collection
     */
    public function getFailedAssignments(): Collection
    {
        return $this->assignmentStatuses->filter(fn(array $assigmentStatus) => !$assigmentStatus[self::STATUS]);
    }
}
