<?php

namespace App\Campaigns\Modules\DataModels;

use Illuminate\Support\Collection;

class AllocateData
{
    public function __construct(
        protected ?Collection $productAssignments = null,
    )
    {
        if (!$this->productAssignments)
            $this->productAssignments = collect();
    }

    /**
     * @param Collection|null $productAssignments
     * @return AllocateData
     */
    public function setProductAssignments(?Collection $productAssignments): AllocateData
    {
        $this->productAssignments = $productAssignments;

        return $this;
    }

    /**
     * @return Collection|null
     */
    public function getProductAssignments(): ?Collection
    {
        return $this->productAssignments;
    }

    public function hasAssignments(): bool
    {
        return $this->productAssignments && $this->productAssignments->isNotEmpty();
    }
}
