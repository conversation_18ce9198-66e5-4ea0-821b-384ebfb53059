<?php

namespace App\Campaigns\Modules;

use App\Campaigns\Modules\DataModels\AllocateData;
use App\Campaigns\Modules\DataModels\PostAllocationData;
use App\Contracts\Campaigns\CampaignModuleContract;
use App\DataModels\Campaigns\ConsumerProject;
use App\DataModels\ProductAssignment\ProposedProductAssignment;
use App\Enums\Campaigns\CampaignStatus;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ProductAssignment;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

abstract class BaseModule implements CampaignModuleContract
{
    /**
     * Handles returning the database model for the given module.
     *
     * @param CompanyCampaign $campaign
     * @return Model
     */
    protected abstract function getModel(CompanyCampaign $campaign): Model;

    /**
     * @inheritDoc
     */
    public function save(CompanyCampaign $campaign, Collection $payload): bool
    {
        return true;
    }

    /**
     * @inheritDoc
     */
    public function statusChange(CompanyCampaign $campaign, CampaignStatus $old, CampaignStatus $new): bool
    {
        return true;
    }

    /**
     * @inheritDoc
     */
    public function delete(CompanyCampaign $campaign): bool
    {
        return true;
    }

    /**
     * @inheritDoc
     */
    public function preFilter(CompanyCampaign $campaign, ConsumerProject $project): bool
    {
        return true;
    }

    /**
     * @inheritDoc
     */
    public function filter(CompanyCampaign $campaign, ConsumerProject $project): bool
    {
        return true;
    }

    /**
     * @inheritDoc
     */
    public function allocate(CompanyCampaign $campaign, ProposedProductAssignment $proposedAssignment, AllocateData $data): AllocateData
    {
        return $data;
    }

    /**
     * @inheritDoc
     */
    public function postAllocation(CompanyCampaign $campaign, ProductAssignment $assignment, PostAllocationData $data): PostAllocationData
    {
        return $data;
    }

    /**
     * @inheritDoc
     */
    public function transform(CompanyCampaign $campaign): Collection
    {
        // TODO: Implement transform() method.
        return collect();
    }

    /**
     * @inheritDoc
     */
    public function validate(CompanyCampaign $campaign, Collection $payload): bool
    {
        return true;
    }
}
