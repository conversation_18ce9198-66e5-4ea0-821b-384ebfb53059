<?php

namespace App\Campaigns\Modules;

use App\Actions\ForceUploadWatchdogVideo;
use App\Campaigns\Delivery\Contacts\Strategies\Email\AppointmentEmailDeliveryStrategy;
use App\Campaigns\Delivery\Contacts\Strategies\SMS\AppointmentSMSDeliveryStrategy;
use App\Campaigns\Modules\DataModels\PostAllocationData;
use App\Http\Resources\Dashboard\v4\CompanyCampaignContactDeliveryResource;
use App\Http\Resources\Dashboard\v4\CompanyCampaignCRMDelivererResource;
use App\Jobs\Appointments\AttemptAppointmentDeliveryJob;
use App\Models\AppointmentDelivery;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\CompanyCampaignSchedule;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModule;
use App\Models\Odin\ProductAssignment;
use App\Repositories\Campaigns\CompanyCampaignDataRepository;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Throwable;

/**
 * Override for Appointment campaigns to deliver to Consumer once we've delivered at least once to Company
 */
class AppointmentDeliveryModule extends DeliveryModule
{
    const string PAYLOAD_SCHEDULES   = 'schedules';

    public function __construct(
        protected AppointmentEmailDeliveryStrategy $emailDeliveryStrategy,
        protected AppointmentSMSDeliveryStrategy $smsDeliveryStrategy,
        protected CompanyCampaignDataRepository $campaignDataRepository,
        protected ForceUploadWatchdogVideo $forceUploadWatchdogVideo
    )
    {
        parent::__construct(
            $this->campaignDataRepository,
            $this->forceUploadWatchdogVideo,
        );
    }

    /**
     * @param CompanyCampaign $campaign
     * @param Collection $payload
     * @return bool
     */
    public function save(CompanyCampaign $campaign, Collection $payload): bool
    {
        $saved = parent::save($campaign, $payload);

        $deliveryPayload = $payload[self::PAYLOAD_PARENT_KEY] ?? [];
        return $saved && $this->saveSchedules($campaign, collect($deliveryPayload[self::PAYLOAD_SCHEDULES] ?? []));
    }

    public function transform(CompanyCampaign $campaign): Collection
    {
        /** @var CompanyCampaignDeliveryModule $deliveryModule */
        $deliveryModule = $this->getModel($campaign);

        return collect([
            self::PAYLOAD_PARENT_KEY => collect([
                self::PAYLOAD_CONTACT_DELIVERIES => CompanyCampaignContactDeliveryResource::collection($deliveryModule->contacts),
                self::PAYLOAD_CRM_DELIVERIES     => CompanyCampaignCRMDelivererResource::collection($deliveryModule->crms),
                self::PAYLOAD_SCHEDULES          => $campaign->companyCampaignSchedules()->pluck(CompanyCampaignSchedule::FIELD_SCHEDULE_ID)->toArray(),
            ])
        ]);
    }

    public function getFrontendWizardConfiguration(): ?array
    {
        $data = parent::getFrontendWizardConfiguration();

        $data[self::WIZARD_KEY_INPUTS][self::PAYLOAD_SCHEDULES] = [
            self::INPUT_KEY_VALIDATION => 'array|numeric',
            self::INPUT_KEY_NAME       => 'Schedules',
        ];

        return $data;
    }

    /**
     * @param CompanyCampaign $campaign
     * @param Collection $payload
     * @return bool
     */
    protected function saveSchedules(CompanyCampaign $campaign, Collection $payload): bool
    {
        $campaign->companyCampaignSchedules()->delete();
        $newSchedules = $payload->map(fn(int $scheduleId) => [
            CompanyCampaignSchedule::FIELD_SCHEDULE_ID => $scheduleId,
        ])->toArray();

        return !!$campaign->companyCampaignSchedules()->createMany($newSchedules);
    }

    /**
     * @param ProductAssignment $assignment
     * @param PostAllocationData $data
     * @return PostAllocationData
     */
    protected function deliverySucceeded(ProductAssignment $assignment, PostAllocationData $data): PostAllocationData
    {
        $assignment->update([
            ProductAssignment::FIELD_DELIVERED    => true,
            ProductAssignment::FIELD_DELIVERED_AT => Carbon::now()
        ]);

        $this->deliverToConsumer($assignment);

        return $data->logSuccessfulAssignment($assignment);
    }

    /**
     * @param ProductAssignment $productAssignment
     * @return void
     */
    private function deliverToConsumer(ProductAssignment $productAssignment): void
    {
        $companyCampaign = $productAssignment->budget->budgetContainer->campaign;
        /** @var AppointmentDelivery $appointmentDelivery */
        $appointmentDelivery = AppointmentDelivery::query()
            ->where([
                AppointmentDelivery::FIELD_COMPANY_CAMPAIGN_ID   => $companyCampaign->id,
                AppointmentDelivery::FIELD_PRODUCT_ASSIGNMENT_ID => $productAssignment->id
            ])->first();

        try {
            $this->emailDeliveryStrategy->deliverToConsumer($productAssignment->consumerProduct, $companyCampaign);
            $this->smsDeliveryStrategy->deliverToConsumer($productAssignment->consumerProduct, $companyCampaign);
        } catch (Throwable $e) {
            logger()->error("Error delivering Appointment to Consumer on ProductAssignment $productAssignment->id - " . $e->getMessage());
        }

        // reschedule a delivery to the consumer if it failed
        if (!$appointmentDelivery->consumer_delivered) {
            $reattemptAt = now()->addMinutes(config('sales.appointments.next_delivery_attempt_buffer_minutes'));
            AttemptAppointmentDeliveryJob::dispatch(
                $productAssignment->id,
                $appointmentDelivery->id
            )->delay($reattemptAt);
        }
    }
}
