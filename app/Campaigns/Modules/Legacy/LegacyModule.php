<?php

namespace App\Campaigns\Modules\Legacy;

use App\Campaigns\Modules\DataModels\AllocateData;
use App\Campaigns\Modules\DataModels\PostAllocationData;
use App\DataModels\ProductAssignment\ProposedProductAssignment;
use App\Enums\Campaigns\CampaignExternalRelationType;
use App\Enums\Campaigns\CampaignStatus;
use App\Jobs\Odin\CompanyCampaignLegacySyncJob;
use App\Models\Campaigns\CampaignReactivation;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\Legacy\LeadCampaign;
use App\Campaigns\Modules\BaseModule;
use App\Enums\Campaigns\Modules\Budget\BudgetType;
use App\Http\Resources\Dashboard\LegacyTransformers\LeadCampaignTransformer;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\CompanyCampaignRelation;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Services\Legacy\APIConsumer;
use App\Services\Odin\API\OdinAuthoritativeAPILegacySyncService;
use App\Transformers\Odin\v2\EloquentQuoteCompanyTransformer;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Facades\Auth;
use Throwable;

/**
 * This module should handle all interaction still required with Legacy
 * This is hopefully limited to syncing bare-minimum data back for Invoicing to function
 *  until it is brought over to A2
 * This module should then be purged with great enthusiasm
 */
class LegacyModule extends BaseModule
{
    const string LEAD_CAMPAIGN_PREFIX  = 'admin_2_generated_';
    const string SYNC_INITIATOR_ID     = 'user_id';

    const string RESPONSE_STATUS       = 'status';
    const string RESPONSE_LEGACY_ID    = 'legacy_id';

    /**
     * @inheritDoc
     */
    protected function getModel(CompanyCampaign $campaign): Model
    {
        return $campaign;
    }

    /**
     * @inheritDoc
     * @throws BindingResolutionException
     */
    public function save(CompanyCampaign $campaign, Collection $payload): bool
    {
        $existingLeadCampaign = $this->leadCampaignRelation($campaign);

        return $existingLeadCampaign
            ? $this->updateLegacyCampaignData($campaign)
            : $this->createLegacyCampaignData($campaign);
    }

    /**
     * @inheritDoc
     */
    public function statusChange(CompanyCampaign $campaign, CampaignStatus $old, CampaignStatus $new): bool
    {
        return $this->updateLegacyCampaignStatus($campaign);
    }

    /**
     * @inheritDoc
     */
    public function allocate(CompanyCampaign $campaign, ProposedProductAssignment $proposedAssignment, AllocateData $data): AllocateData
    {
        // This may be required if we need to sync PA before delivery is attempted for any raisins
        return $data;
    }

    /**
     * @inheritDoc
     */
    public function postAllocation(CompanyCampaign $campaign, ProductAssignment $assignment, PostAllocationData $data): PostAllocationData
    {
        // if retries on delivery come through here to update ProductAssignment, may need a way to discern that it's an update and not a create
        try {
            $this->createLegacyQuoteCompany($assignment, $campaign->id);
        } catch (Throwable $exception) {
            // todo: log?
            return $data;
        }

        return $data;
    }

    /**
     * @inheritDoc
     */
    public function delete(CompanyCampaign $campaign): bool
    {
        dispatch(new CompanyCampaignLegacySyncJob(CompanyCampaignLegacySyncJob::SYNC_TYPE_DELETE, $campaign));

        return true;
    }

    /**
     * @param ProductAssignment $productAssignment
     * @param int $leadCampaignId
     * @return bool
     * @throws BindingResolutionException
     */
    public function createLegacyQuoteCompany(ProductAssignment $productAssignment, int $leadCampaignId): bool
    {
        /** @var OdinAuthoritativeAPILegacySyncService $odinSyncAPIConsumer */
        $odinSyncAPIConsumer = app()->make(OdinAuthoritativeAPILegacySyncService::class);
        $transformer = app()->make(EloquentQuoteCompanyTransformer::class);

        $legacyTransform = $transformer->transform($productAssignment, $leadCampaignId);

        $legacyResponse = $odinSyncAPIConsumer->post(
            '/product-assignment/create',
            $legacyTransform
        )?->json();

        if ($legacyResponse[self::RESPONSE_LEGACY_ID] ?? false) {
            $productAssignment->update([
                ProductAssignment::FIELD_LEGACY_ID  => $legacyResponse[self::RESPONSE_LEGACY_ID]
            ]);

            return true;
        }
        else {
            $this->handleSyncError("Failed to create QuoteCompany for ProductAssignment " . $productAssignment->id);

            return false;
        }
    }

    /**
     * @param ProductAssignment $productAssignment
     * @return bool
     * @throws BindingResolutionException
     */
    public function updateLegacyQuoteCompany(ProductAssignment $productAssignment): bool
    {
        /** @var OdinAuthoritativeAPILegacySyncService $odinSyncAPIConsumer */
        $odinSyncAPIConsumer = app()->make(OdinAuthoritativeAPILegacySyncService::class);
        /** @var EloquentQuoteCompanyTransformer $transformer */
        $transformer = app()->make(EloquentQuoteCompanyTransformer::class);

        $legacyTransform = $transformer->transformForUpdate($productAssignment);

        $legacyResponse = $odinSyncAPIConsumer->patch(
            '/product-assignment/update',
            $legacyTransform
        )?->json();

        if (!($legacyResponse[self::RESPONSE_STATUS] ?? false)) {
            $this->handleSyncError("Failed to update QuoteCompany for ProductAssignment " . $productAssignment->id);

            return false;
        }

        return true;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @return bool
     * @throws BindingResolutionException
     */
    public function updateQuoteStatus(ConsumerProduct $consumerProduct): bool
    {
        /** @var OdinAuthoritativeAPILegacySyncService $odinSyncAPIConsumer */
        $odinSyncAPIConsumer = app()->make(OdinAuthoritativeAPILegacySyncService::class);
        /** @var EloquentQuoteCompanyTransformer $transformer */
        $transformer = app()->make(EloquentQuoteCompanyTransformer::class);

        $legacyTransform = $transformer->transformQuoteStatusUpdate($consumerProduct);

        $legacyResponse = $odinSyncAPIConsumer->patch(
            '/product-assignment/update-quote-status',
            $legacyTransform
        )?->json();

        if (!($legacyResponse[self::RESPONSE_STATUS] ?? false)) {
            $this->handleSyncError("Failed to update Quote status for ConsumerProduct " . $consumerProduct->id);

            return false;
        }

        return true;
    }

    /**
     * Create a LeadCampaign and fill out the CompanyCampaignRelation entry
     * We need this to be created in Legacy for invoicing relationships
     *
     * @param CompanyCampaign $campaign
     * @return bool
     * @throws BindingResolutionException
     */
    protected function createLegacyCampaignData(CompanyCampaign $campaign): bool
    {
        $integrationApiConsumer = app()->make(APIConsumer::class);

        $legacyTransform = $this->transformForLegacy($campaign);
        $legacyTransform[self::SYNC_INITIATOR_ID] = $this->getInitiatorId();

        /** @var Company $company */
        $company = Company::query()
            ->findOrFail($campaign->company_id);

        $legacyResponse = [];
        try {
            $legacyResponse = $integrationApiConsumer->post(
                $this->getDashboardIntegrationBaseRoute($company, 'campaigns/create'),
                $legacyTransform
            )->json()['data'] ?? [];
        }
        catch (Throwable $e) {
            $this->handleSyncError("Failed to create LeadCampaign for CompanyCampaign ID $campaign->id - " . $e->getMessage());
        }

        $newLeadCampaign = LeadCampaign::query()
            ->where(LeadCampaign::UUID, $legacyResponse['uuid'] ?? null)
            ->first();

        if($newLeadCampaign) {
            CompanyCampaignRelation::query()
                ->create([
                    CompanyCampaignRelation::FIELD_RELATION_ID         => $newLeadCampaign->{LeadCampaign::ID},
                    CompanyCampaignRelation::FIELD_COMPANY_CAMPAIGN_ID => $campaign->id,
                    CompanyCampaignRelation::FIELD_RELATION_TYPE       => CampaignExternalRelationType::LEGACY_LEAD_CAMPAIGN,
                ]);
        }

        return true;
    }

    /**
     * This is primarily keeping legacy campaigns updated for Company filters based on campaign data
     * @param CompanyCampaign $campaign
     * @return bool
     */
    protected function updateLegacyCampaignData(CompanyCampaign $campaign): bool
    {
        dispatch(new CompanyCampaignLegacySyncJob(
            CompanyCampaignLegacySyncJob::SYNC_TYPE_UPDATE,
            $campaign,
            $this->getInitiatorId(),
        ));

        return true;
    }

    /**
     * Non-critical back-syncing, really only used by a few filters in Companies Search
     * @param CompanyCampaign $campaign
     * @return bool
     */
    protected function updateLegacyCampaignStatus(CompanyCampaign $campaign): bool
    {
        dispatch(new CompanyCampaignLegacySyncJob(
            CompanyCampaignLegacySyncJob::SYNC_TYPE_STATUS_CHANGE,
            $campaign,
            $this->getInitiatorId(),
        ));

        return true;
    }

    /**
     * @param CompanyCampaign $campaign
     * @return LeadCampaign|null
     */
    public function leadCampaignRelation(CompanyCampaign $campaign): ?CompanyCampaignRelation
    {
        if (!$campaign->id) return null;

        /** @var ?CompanyCampaignRelation */
        return CompanyCampaignRelation::query()
            ->where(CompanyCampaignRelation::FIELD_COMPANY_CAMPAIGN_ID, $campaign->id)
            ->first();
    }

    /**
     * @param CompanyCampaign $campaign
     * @return array
     * @throws BindingResolutionException
     */
    public function transformForLegacy(CompanyCampaign $campaign): array
    {
        $legacyTransformer = app()->make(LeadCampaignTransformer::class);

        $optionalBudgetKeys = [
            'unverified'    => 'UNVERIFIED',
            'email_only'    => 'EMAIL_ONLY',
        ];
        $budgetSpendTypes = [
            1   => 'lead',
            2   => 'spend',
        ];

        $optionalBudgets = $campaign->budgetContainer->budgets()
            ->whereIn(Budget::FIELD_KEY, array_keys($optionalBudgetKeys))
            ->where(Budget::FIELD_STATUS, BUDGET::STATUS_ENABLED)
            ->get();
        $verifiedBudget = $campaign->budgetContainer->budgets()
            ->where(Budget::FIELD_KEY, 'verified')
            ->first();
        $verifiedBudget = $verifiedBudget ?? $campaign->budgetContainer->budgets()->first();

        $optionalBudgetData = $optionalBudgets->mapWithKeys(fn(Budget $budget) => [
            $optionalBudgetKeys[$budget->key] => [
                'max_daily_leads'   => $budget->type === BudgetType::TYPE_DAILY_UNITS ? $budget->value : null,
                'max_daily_spend'   => $budget->type === BudgetType::TYPE_DAILY_SPEND ? $budget->value : null,
                'status'            => true,
            ]
        ]);

        $transformableCampaignData = [
            'name'                           => $this->prefixCampaignName($campaign->name),
            'status'                         => $this->getLegacyCampaignStatus($campaign),
            'optional_leads_types'           => $optionalBudgetData->toArray(),
            'daily_limit_type'               => $budgetSpendTypes[$campaign->type->value] ?? null,
            'max_daily_leads'                => $verifiedBudget->type === BudgetType::TYPE_DAILY_UNITS ? $verifiedBudget->value : null,
            'max_daily_spend'                => $verifiedBudget->type === BudgetType::TYPE_DAILY_SPEND ? $verifiedBudget->value : null,
            'allow_non_budget_premium_leads' => false,
            'zip_codes'                      => $campaign->locationModule?->locations()->pluck(CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID)->toArray(),
            'property_types'                 => [],
            'contact_deliveries'             => [],
            'crm_deliveries'                 => [],
        ];

        return $legacyTransformer->transform($transformableCampaignData);
    }

    /**
     * @param CompanyCampaign $campaign
     * @return string|null
     */
    public function getLegacyCampaignReference(CompanyCampaign $campaign): ?string
    {
        return LeadCampaign::query()
            ->find($this->leadCampaignRelation($campaign)?->relation_id ?? null)
            ?->{LeadCampaign::UUID}
            ?? null;
    }

    /**
     * @param string $name
     * @return string
     */
    public function prefixCampaignName(string $name): string
    {
        return self::LEAD_CAMPAIGN_PREFIX . $name;
    }

    /**
     * @param Company $company
     * @param string $suffix
     * @return string|null
     */
    public function getDashboardIntegrationBaseRoute(Company $company, string $suffix = ''): ?string
    {
        $companyReference = $company->reference ?? "";

        return "/fixr-dashboard/$companyReference/$suffix";
    }

    protected function handleSyncError(string $errorMessage): void
    {
        logger()->error("CompanyCampaign sync error: ".$errorMessage);
    }

    protected function getLegacyCampaignStatus(CompanyCampaign $campaign): int
    {
        return $campaign->status === CampaignStatus::ACTIVE
            ? 1
            : 0;
    }

    protected function getInitiatorId(): int
    {
        return Auth::getActioningUserLegacyId() ?? 0;
    }
}
