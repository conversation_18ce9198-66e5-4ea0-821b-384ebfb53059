<?php

namespace App\Campaigns\Modules;

use App\Contracts\Campaigns\HasFrontendComponent;
use App\DataModels\Campaigns\ConsumerProject;
use App\Events\CompanyCampaign\CompanyCampaignServiceAreaUpdatedEvent;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModule;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModule;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Repositories\LocationRepository;
use App\Services\Companies\CompanyZipCodeExceptionService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

/**
 * @TODO: Optimize to reduce DB calls.
 */
class LocationModule extends BaseModule implements HasFrontendComponent
{
    const PAYLOAD_PARENT_KEY   = 'location';
    const PAYLOAD_ZIP_CODES    = 'zip_codes';
    const MODULE_FRONTEND_NAME = 'Locations';

    public function __construct(protected LocationRepository $locationRepository){}

    /**
     * @inheritDoc
     */
    protected function getModel(CompanyCampaign $campaign): CompanyCampaignLocationModule
    {
        /** @var CompanyCampaignLocationModule $model */
        $model = CompanyCampaignLocationModule::query()->firstOrCreate(
            [
                CompanyCampaignLocationModule::FIELD_CAMPAIGN_ID => $campaign->id
            ],
            [
                CompanyCampaignLocationModule::FIELD_CAMPAIGN_ID => $campaign->id
            ]
        );

        return $model;
    }

    /**
     * @inheritDoc
     */
    public function validate(CompanyCampaign $campaign, Collection $payload): bool
    {
        return gettype($payload[self::PAYLOAD_PARENT_KEY][self::PAYLOAD_ZIP_CODES][0] ?? null) === 'integer';
    }

    /**
     * @inheritDoc
     */
    public function transform(CompanyCampaign $campaign): Collection
    {
        $locations = $campaign->{CompanyCampaign::RELATION_LOCATION_MODULE}?->{CompanyCampaignLocationModule::RELATION_LOCATIONS}; //TODO upgrade

        $locations->load(CompanyCampaignLocationModuleLocation::RELATION_LOCATION);

        return collect([
            self::PAYLOAD_PARENT_KEY => collect([
                self::PAYLOAD_ZIP_CODES => $locations->mapWithKeys(function(CompanyCampaignLocationModuleLocation $cclml) {
                    $location = $cclml->{CompanyCampaignLocationModuleLocation::RELATION_LOCATION};

                    return [
                        $location->zip_code => [
                            'id' => $location->id,
                            'state_key' => $location->state_abbr,
                            'county_key' => $location->county_key,
                            'city_name' => $location->city,
                            'city_key' => $location->city_key,
                            'zip_code' => $location->zip_code,
                        ]
                    ];
                })
            ])
        ]);
    }

    /**
     * @inheritDoc
     */
    public function filter(CompanyCampaign $campaign, ConsumerProject $project): bool
    {
        $deliveryModule = $this->getModel($campaign);

        return $campaign->zipCodeTargeted()
            ? $this->filterByZipCode($deliveryModule, $project->zipCodeLocationId())
            : $this->filterByCounty($deliveryModule, $project->countyLocationId());
    }

    /**
     * @param CompanyCampaignLocationModule $deliveryModule
     * @param int $zipCodeLocationId
     * @return bool
     */
    protected function filterByZipCode(CompanyCampaignLocationModule $deliveryModule, int $zipCodeLocationId): bool
    {
        return $deliveryModule
            ->locations()
            ->where(CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID, $zipCodeLocationId)
            ->exists();
    }

    /**
     * @param CompanyCampaignLocationModule $deliveryModule
     * @param int $countyLocationId
     * @return bool
     */
    protected function filterByCounty(CompanyCampaignLocationModule $deliveryModule, int $countyLocationId): bool
    {
        return in_array($countyLocationId, $deliveryModule->getCountyLocationIds());
    }

    /**
     * @inheritDoc
     */
    public function save(CompanyCampaign $campaign, Collection $payload): bool
    {
        if(isset($payload[self::PAYLOAD_PARENT_KEY][self::PAYLOAD_ZIP_CODES]) && $this->validate($campaign, $payload)) {
            $model = $this->getModel($campaign);
            $oldLocations = $model->locations->pluck(CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID)->sort()->toArray();

            $model->{CompanyCampaignLocationModule::RELATION_LOCATIONS}()->delete();

            $zipCodes = $payload[self::PAYLOAD_PARENT_KEY][self::PAYLOAD_ZIP_CODES];

            if($zipCodes) {
                $status =  $model->save() && $this->insertModuleLocationsById(
                    $model->{CompanyCampaignLocationModule::FIELD_ID},
                    $zipCodes,
                    $campaign,
                );

                $this->fireEventIfCampaignLocationsUpdated($oldLocations, $campaign);

                return $status;
            }
        }

        return true;
    }

    /**
     * @param array $oldLocations
     * @param CompanyCampaign $campaign
     *
     * @return void
     */
    protected function fireEventIfCampaignLocationsUpdated(array $oldLocations, CompanyCampaign $campaign): void
    {
        $newLocations = $campaign->locationModule->locations()->get()->pluck(CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID)->sort()->toArray();

        if (!empty(array_diff($oldLocations, $newLocations) || !empty(array_diff($newLocations, $oldLocations)))) {
            CompanyCampaignServiceAreaUpdatedEvent::dispatch($campaign->id, $oldLocations);
        }
    }

    /**
     * This may not be needed, but will leave here for now
     * @param int $moduleId
     * @param array $zipCodes
     * @return bool
     */
    private function insertModuleLocationsByZipCode(int $moduleId, array $zipCodes): bool
    {
        /** @var <Location>|Collection $legacyLocations */
        $legacyLocations = $this->locationRepository->getZipCodes($zipCodes);

        $moduleLocations = $legacyLocations->map(function($legacyLocation) use ($moduleId) {
            return [
                CompanyCampaignLocationModuleLocation::FIELD_MODULE_ID   => $moduleId,
                CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID => $legacyLocation->{Location::ID},
                CompanyCampaignLocationModuleLocation::FIELD_ZIP_CODE    => $legacyLocation->{Location::ZIP_CODE},
                Model::CREATED_AT                                        => Carbon::now()
            ];
        })->toArray();

        return CompanyCampaignLocationModuleLocation::query()->insert($moduleLocations);
    }

    /**
     * @param int $moduleId
     * @param array $locationIds
     * @param CompanyCampaign $campaign
     * @return bool
     */
    private function insertModuleLocationsById(int $moduleId, array $locationIds, CompanyCampaign $campaign): bool
    {
        // Ensure only allowed zip codes are added if this is zip code targeted
        //  unless the company has unrestricted zip codes enabled
        $unrestrictedZipCodes = $campaign->company->configuration->unrestricted_zip_code_targeting ?? false;
        if ($campaign->zip_code_targeted && !$unrestrictedZipCodes) {
            /** @var CompanyZipCodeExceptionService $zipCodeExceptionService */
            $zipCodeExceptionService = app(CompanyZipCodeExceptionService::class);
            $allowedLocationIds = $zipCodeExceptionService->getCompanyExceptionsAsZipLocationIds($campaign->company_id);
            $locationIds = array_filter($locationIds, fn(int $id) => in_array($id, $allowedLocationIds));
        }

        /** @var <Location>|Collection $legacyLocations */
        $legacyLocations = $this->locationRepository->getZipCodesByLocationIds($locationIds);
        $companyId = $campaign->company_id;

        $moduleLocations = $legacyLocations->map(function($legacyLocation) use ($moduleId, $companyId) {
            return [
                CompanyCampaignLocationModuleLocation::FIELD_MODULE_ID   => $moduleId,
                CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID => $legacyLocation->{Location::ID},
                CompanyCampaignLocationModuleLocation::FIELD_ZIP_CODE    => $legacyLocation->{Location::ZIP_CODE},
                CompanyCampaignLocationModuleLocation::FIELD_COMPANY_ID  => $companyId,
                Model::CREATED_AT                                        => Carbon::now()
            ];
        })->toArray();

        return CompanyCampaignLocationModuleLocation::query()->insert($moduleLocations);
    }

    /**
     * @inheritDoc
     */
    public function getFrontendKey(): string
    {
        return self::PAYLOAD_PARENT_KEY;
    }

    /**
     * @inheritDoc
     */
    public function getFrontendModuleConfiguration(): ?array
    {
        return null;
    }

    /**
     * @inheritDoc
     */
    public function getFrontendWizardConfiguration(): ?array
    {
        return [
            self::WIZARD_KEY_NAME       => self::MODULE_FRONTEND_NAME,
            self::WIZARD_KEY_MODULE_KEY => self::PAYLOAD_PARENT_KEY,
            self::WIZARD_KEY_INPUTS     => [
                self::PAYLOAD_ZIP_CODES => [
                    self::INPUT_KEY_VALIDATION   => 'array|min:1',
                    self::INPUT_KEY_COMPONENT    => 'nested-dependency-checkbox-select',
                    self::INPUT_KEY_NAME         => 'Zip Codes',
                ],
            ],
            self::WIZARD_KEY_HEADER     => [
                self::WIZARD_KEY_INPUTS     => [
                    CompanyCampaign::FIELD_NAME => [
                        self::INPUT_KEY_VALIDATION  => 'string|min:3|max:64',
                        self::INPUT_KEY_COMPONENT   => 'custom-input',
                        self::INPUT_KEY_NAME        => 'Campaign Name'
                    ],
                    'property_types'            => [
                        self::INPUT_KEY_VALIDATION  => 'array|min:1',
                        self::INPUT_KEY_COMPONENT   => 'custom-checkboxes',
                        self::INPUT_KEY_NAME        => 'Property Types',
                    ]
                ]
            ]
        ];
    }
}
