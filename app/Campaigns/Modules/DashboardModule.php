<?php

namespace App\Campaigns\Modules;

use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\Product;
use App\Mediators\CampaignMediator;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\Industry as IndustryModel;
use App\Models\Odin\IndustryService;
use App\Models\Odin\PropertyType;
use App\Registries\CampaignRegistry;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use App\Models\Odin\Product as ProductModel;

/**
 * @note
 * This class is for transforming a company campaign into a data structure the dashboard frontend can accept, therefore this module only implements transform()
 * If it's decided later that a separate transformer class is more appropriate this can be moved there
 */
class DashboardModule extends BaseModule
{
    const string ID                = 'id';
    const string NAME              = 'name';
    const string TYPE              = 'type';
    const string PRODUCT           = 'product';
    const string STATUS            = 'status';
    const string MODULES           = 'modules';
    const string PROPERTY_TYPES    = 'property_types';
    const string INDUSTRY          = 'industry';
    const string SERVICE           = 'service';
    const string ZIP_CODE_TARGETED = 'zip_code_targeted';

    public function __construct(
        private readonly CampaignMediator $campaignMediator,
        private readonly CampaignRegistry $campaignRegistry
    ) {

    }

    /**
     * @inheritDoc
     */
    protected function getModel(CompanyCampaign $campaign): Model
    {
        return $campaign;
    }

    /**
     * @inheritDoc
     * @throws BindingResolutionException
     */
    public function transform(CompanyCampaign $campaign): Collection
    {
        return collect([
            self::ID                                        => $campaign->{CompanyCampaign::FIELD_ID},
            self::NAME                                      => $campaign->{CompanyCampaign::FIELD_NAME},
            CompanyCampaign::FIELD_REFERENCE                => $campaign->reference,
            self::TYPE                                      => $campaign->{CompanyCampaign::FIELD_TYPE}->value,
            self::PRODUCT                                   => $campaign->{CompanyCampaign::RELATION_PRODUCT}->{ProductModel::FIELD_NAME},
            self::INDUSTRY                                  => $campaign->service->industry->slug,
            self::SERVICE                                   => $campaign->service->slug,
            self::STATUS                                    => $campaign->{CompanyCampaign::FIELD_STATUS}->value,
            self::PROPERTY_TYPES                            => $campaign->{CompanyCampaign::RELATION_PROPERTY_TYPES}->pluck(PropertyType::FIELD_NAME)->toArray(),
            self::ZIP_CODE_TARGETED                         => $campaign->zip_code_targeted,
            CompanyCampaign::FIELD_USES_CUSTOM_FLOOR_PRICES => $campaign->uses_custom_floor_prices,
            self::MODULES                                   => $this->campaignMediator->getFrontendModules(
                $this->campaignRegistry->getCampaignMapping(
                    Product::from($campaign->{CompanyCampaign::RELATION_PRODUCT}->{ProductModel::FIELD_NAME}),
                    IndustryEnum::tryFrom($campaign->{CompanyCampaign::RELATION_SERVICE}->{IndustryService::RELATION_INDUSTRY}->{IndustryModel::FIELD_NAME}),
                    $campaign->{CompanyCampaign::RELATION_SERVICE}->{IndustryService::FIELD_NAME}
                )
            )
        ]);
    }
}
