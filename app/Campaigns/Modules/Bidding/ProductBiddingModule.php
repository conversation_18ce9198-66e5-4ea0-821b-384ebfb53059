<?php

namespace App\Campaigns\Modules\Bidding;

use App\Campaigns\Modules\BaseModule;
use App\Contracts\Campaigns\HasFrontendComponent;
use App\DataModels\Campaigns\ConsumerProject;
use App\Enums\Odin\SaleTypes as SaleTypeEnum;
use App\Jobs\CalculateCompanyCampaignLowBidFlagJob;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Bidding\CompanyCampaignBidPriceModule;
use App\Services\Campaigns\ProductBiddingService;
use Illuminate\Support\Collection;

/**
 *
 */
class ProductBiddingModule extends BaseModule implements HasFrontendComponent
{
    const PAYLOAD_PARENT_KEY = 'bidding';

    const MODULE_FRONTEND_NAME = 'Bidding';

    const PAYLOAD_EXCLUSIVE = SaleTypeEnum::EXCLUSIVE;
    const PAYLOAD_DUO = SaleTypeEnum::DUO;
    const PAYLOAD_TRIO = SaleTypeEnum::TRIO;
    const PAYLOAD_QUAD = SaleTypeEnum::QUAD;
    const PAYLOAD_UNVERIFIED = SaleTypeEnum::UNVERIFIED;
    const PAYLOAD_EMAIL_ONLY = SaleTypeEnum::EMAIL_ONLY;

    const PAYLOAD_IS_STATE_BID = 'is_state_bid';
    const PAYLOAD_STATE_LOCATION_ID = 'state_location_id';

    const PAYLOAD_LOCATION_BIDS = 'location_bids';

    const PAYLOAD_LOCATION_ID = 'location_id';
    const PAYLOAD_BID = 'bid';

    const PAYLOAD_QUALITY_TIER = 'quality_tier';
    const PAYLOAD_PROPERTY_TYPE = 'property_type';

    public function __construct(
        private readonly ProductBiddingService $productBiddingService
    ) {

    }

    /**
     * @inheritDoc
     */
    protected function getModel(CompanyCampaign $campaign): CompanyCampaignBidPriceModule
    {
        /** @type CompanyCampaignBidPriceModule */
        return CompanyCampaignBidPriceModule::query()->firstOrCreate(
            [
                CompanyCampaignBidPriceModule::FIELD_CAMPAIGN_ID => $campaign->id
            ],
            [
                CompanyCampaignBidPriceModule::FIELD_CAMPAIGN_ID => $campaign->id
            ]
        );
    }

    /**
     * @inheritDoc
     */
    public function validate(CompanyCampaign $campaign, Collection $payload): bool
    {
        // TODO: Implement validate() method.
        return true;
    }

    /**
     * @inheritDoc
     */
    public function transform(CompanyCampaign $campaign): Collection
    {
        // The bid prices are currently fetched by the bidding module using the /prices route, so no return is needed here
        return collect([self::PAYLOAD_PARENT_KEY => [
            self::PAYLOAD_LOCATION_BIDS => []
        ]]);
    }

    /**
     * @inheritDoc
     */
    public function filter(CompanyCampaign $campaign, ConsumerProject $project): bool
    {
        //TODO

        return true;
    }

    /**
     * @inheritDoc
     */
    public function save(CompanyCampaign $campaign, Collection $payload): bool
    {
        $model = $this->getModel($campaign);
        $modulePayload = $payload[self::PAYLOAD_PARENT_KEY] ?? [];

        if (empty($modulePayload) || empty($modulePayload[self::PAYLOAD_LOCATION_BIDS])) {
            $success = true;
        }
        else {
            $success = $this->productBiddingService->saveCampaignBids(
                $campaign,
                $model,
                $modulePayload
            );
        }

        if ($success)
            CalculateCompanyCampaignLowBidFlagJob::dispatch($campaign);

        return $success;
    }

    /**
     * @inheritDoc
     */
    public function getFrontendKey(): string
    {
        return self::PAYLOAD_PARENT_KEY;
    }

    /**
     * @inheritDoc
     */
    public function getFrontendModuleConfiguration(): ?array
    {
        return null;
    }

    /**
     * @inheritDoc
     */
    public function getFrontendWizardConfiguration(): ?array
    {
        $saleTypeBidsStructure = function(SaleTypeEnum $saleType) {
            return [
                self::INPUT_KEY_VALIDATION => 'array',
                self::INPUT_KEY_NAME => $saleType->value,
                self::INPUT_CONTAINED_KEYS => [
                    self::PAYLOAD_LOCATION_ID => [
                        self::INPUT_KEY_VALIDATION => 'numeric|min:0'
                    ],
                    self::PAYLOAD_BID => [
                        self::INPUT_KEY_VALIDATION => 'numeric|min:0'
                    ],
                    self::PAYLOAD_QUALITY_TIER => [
                        self::INPUT_KEY_VALIDATION => 'string'
                    ],
                    self::PAYLOAD_PROPERTY_TYPE => [
                        self::INPUT_KEY_VALIDATION => 'string'
                    ],
                    self::PAYLOAD_IS_STATE_BID => [
                        self::INPUT_KEY_VALIDATION => 'boolean',
                    ],
                    self::PAYLOAD_STATE_LOCATION_ID => [
                        self::INPUT_KEY_VALIDATION => 'number',
                    ],
                ]
            ];
        };

        return [
            self::WIZARD_KEY_NAME => self::MODULE_FRONTEND_NAME,
            self::WIZARD_KEY_MODULE_KEY => self::PAYLOAD_PARENT_KEY,
            self::WIZARD_KEY_INPUTS => [
                self::PAYLOAD_LOCATION_BIDS => [
                    self::INPUT_KEY_VALIDATION => 'object',
                    self::INPUT_KEY_NAME => 'Location Bids',
                    self::INPUT_CONTAINED_KEYS => [
                        SaleTypeEnum::key(self::PAYLOAD_EXCLUSIVE) => $saleTypeBidsStructure(self::PAYLOAD_EXCLUSIVE),
                        SaleTypeEnum::key(self::PAYLOAD_DUO) => $saleTypeBidsStructure(self::PAYLOAD_DUO),
                        SaleTypeEnum::key(self::PAYLOAD_TRIO) => $saleTypeBidsStructure(self::PAYLOAD_TRIO),
                        SaleTypeEnum::key(self::PAYLOAD_QUAD) => $saleTypeBidsStructure(self::PAYLOAD_QUAD),
                        SaleTypeEnum::key(self::PAYLOAD_UNVERIFIED) => $saleTypeBidsStructure(self::PAYLOAD_UNVERIFIED),
                        SaleTypeEnum::key(self::PAYLOAD_EMAIL_ONLY) => $saleTypeBidsStructure(self::PAYLOAD_EMAIL_ONLY)
                    ],
                ],
            ]
        ];
    }
}
