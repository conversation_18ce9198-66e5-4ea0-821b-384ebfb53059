<?php

namespace App\Campaigns\Modules;

use App\DataModels\Campaigns\ConsumerProject;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\PropertyType;
use Illuminate\Database\Eloquent\Model;

class PropertyTypeModule extends BaseModule
{
    /**
     * @inheritDoc
     */
    #[\Override] protected function getModel(CompanyCampaign $campaign): Model
    {
        return $campaign;
    }

    /**
     * @inheritDoc
     */
    public function preFilter(CompanyCampaign $campaign, ConsumerProject $project): bool
    {
        return in_array($project->propertyType(), $campaign->campaignPropertyTypes->pluck(PropertyType::FIELD_NAME)->toArray());
    }
}
