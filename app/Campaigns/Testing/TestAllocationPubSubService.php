<?php

namespace App\Campaigns\Testing;

use Exception;
use Google\Cloud\PubSub\MessageBuilder;
use Google\Cloud\PubSub\PubSubClient;

class TestAllocationPubSubService
{
    /**
     * @param string $type
     * @param string $event
     * @param array $params
     * @return void
     */
    public function handle(string $type, string $event, array $params): void
    {
        $params["type"] = $type;
        $params["event"] = $event;

        $pubsub = new PubSubClient([
            'keyFile' => json_decode(base64_decode(config('services.google.pubsub.service_account')), true),
            'projectId' => config('services.google.pubsub.project_id')
        ]);

        $topic = $pubsub->topic(config('app.testing.staging_pubsub_topic'));

        $message = (new MessageBuilder)
            ->setData(json_encode($params))
            ->addAttribute('type', $type)
            ->addAttribute('event', $event)
            ->build();

        try {
            $topic->publish($message);
        }
        catch(Exception $e) {
            $this->handleTestingFailure("$type/$event failed to dispatch - does the topic exist?");
        }

    }

    private function handleTestingFailure(string $errorMessage): void
    {
        logger()->debug("Test PubSub error: $errorMessage");
    }
}
