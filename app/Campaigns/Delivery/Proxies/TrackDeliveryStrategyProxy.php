<?php

namespace App\Campaigns\Delivery\Proxies;

use App\Contracts\Campaigns\Delivery\DeliveryStrategyContract;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ConsumerProduct;

class TrackDeliveryStrategyProxy implements DeliveryStrategyContract
{
    public function __construct(protected DeliveryStrategyContract $concrete) {}

    /**
     * @param ConsumerProduct $product
     * @param CompanyCampaign $campaign
     * @param array|null $payload
     * @return bool
     */
    public function deliver(ConsumerProduct $product, CompanyCampaign $campaign, ?array $payload = []): bool
    {
        try {
            return $this->concrete->deliver($product, $campaign, $payload);
        } catch (\Exception $e) {
            // TODO: Log & report errors.

            return false;
        }
    }
}
