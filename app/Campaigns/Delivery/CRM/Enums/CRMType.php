<?php

namespace App\Campaigns\Delivery\CRM\Enums;

use App\Campaigns\Delivery\CRM\BaseCRMDeliverer;
use App\Campaigns\Delivery\CRM\BaseCRMTestDeliverer;
use App\Campaigns\Delivery\CRM\Insightly\InsightlyCRMDeliverer;
use App\Campaigns\Delivery\CRM\Insightly\InsightlyCRMTestDeliverer;
use App\Campaigns\Delivery\CRM\JobNimbus\JobNimbusCRMDeliverer;
use App\Campaigns\Delivery\CRM\JobNimbus\JobNimbusCRMTestDeliverer;
use App\Campaigns\Delivery\CRM\LeadConduit\LeadConduitCRMDeliverer;
use App\Campaigns\Delivery\CRM\LeadConduit\LeadConduitCRMTestDeliverer;
use App\Campaigns\Delivery\CRM\ParseableEmail\ParseableEmailCRMTestDeliverer;
use App\Campaigns\Delivery\CRM\ParseableEmail\ParseableEmailDeliverer;
use App\Campaigns\Delivery\CRM\Pipedrive\PipedriveCRMDeliverer;
use App\Campaigns\Delivery\CRM\Pipedrive\PipedriveCRMTestDeliverer;
use App\Campaigns\Delivery\CRM\SolarNexus\SolarNexusCRMDeliverer;
use App\Campaigns\Delivery\CRM\SolarNexus\SolarNexusCRMTestDeliverer;
use App\Campaigns\Delivery\CRM\SolarNexus\SolarNexusLegacyCRMDeliverer;
use App\Campaigns\Delivery\CRM\SolarNexus\SolarNexusLegacyCRMTestDeliverer;
use App\Campaigns\Delivery\CRM\WebForm\StandardWebFormCRMDeliverer;
use App\Campaigns\Delivery\CRM\WebForm\StandardWebFormCRMTestDeliverer;
use App\Campaigns\Delivery\CRM\ZohoOAuth\ZohoOAuthCRMDeliverer;
use App\Campaigns\Delivery\CRM\ZohoOAuth\ZohoOAuthCRMTestDeliverer;
use App\Contracts\Campaigns\Delivery\CRMDelivererContract;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleCRM;
use App\Models\Campaigns\Modules\Delivery\CompanyCRMTemplate;

enum CRMType: int
{
    case STANDARD_WEB_FORM  = 0;
    case LEAD_CONDUIT       = 1;
    case JOB_NIMBUS         = 2;
    case PIPEDRIVE          = 3;
    case ZOHO_OAUTH         = 4;
    case SOLAR_NEXUS        = 5;
    case SOLAR_NEXUS_LEGACY = 6;
    case INSIGHTLY          = 7;
    case PARSEABLE_EMAIL    = 8;

    /**
     * Returns the concrete implementation for the given CRM.
     *
     * @param CompanyCampaignDeliveryModuleCRM|CompanyCRMTemplate $crm
     * @param array $payload
     * @return CRMDelivererContract
     */
    public function getConcrete(CompanyCampaignDeliveryModuleCRM|CompanyCRMTemplate $crm, array $payload = []): CRMDelivererContract
    {
        return match ($this) {
            self::STANDARD_WEB_FORM  => new StandardWebFormCRMDeliverer($crm, $payload),
            self::LEAD_CONDUIT       => new LeadConduitCRMDeliverer($crm, $payload),
            self::JOB_NIMBUS         => new JobNimbusCRMDeliverer($crm, $payload),
            self::PIPEDRIVE          => new PipedriveCRMDeliverer($crm, $payload),
            self::ZOHO_OAUTH         => new ZohoOAuthCRMDeliverer($crm, $payload),
            self::SOLAR_NEXUS        => new SolarNexusCRMDeliverer($crm, $payload),
            self::SOLAR_NEXUS_LEGACY => new SolarNexusLegacyCRMDeliverer($crm, $payload),
            self::INSIGHTLY          => new InsightlyCRMDeliverer($crm, $payload),
            self::PARSEABLE_EMAIL    => new ParseableEmailDeliverer($crm, $payload),
            default                  => throw new \RuntimeException("No Concrete Defined For '" . $this->name . "' CRM")
        };
    }

    /**
     * @param CompanyCampaignDeliveryModuleCRM $crm
     * @param array $payload
     *
     * @return BaseCRMTestDeliverer
     */
    public function getCRMTestDeliverer(CompanyCampaignDeliveryModuleCRM $crm, array $payload = []): BaseCRMTestDeliverer
    {
        return match ($this) {
            self::STANDARD_WEB_FORM  => new StandardWebFormCRMTestDeliverer($crm, $payload),
            self::LEAD_CONDUIT       => new LeadConduitCRMTestDeliverer($crm, $payload),
            self::JOB_NIMBUS         => new JobNimbusCRMTestDeliverer($crm, $payload),
            self::PIPEDRIVE          => new PipedriveCRMTestDeliverer($crm, $payload),
            self::ZOHO_OAUTH         => new ZohoOAuthCRMTestDeliverer($crm, $payload),
            self::SOLAR_NEXUS        => new SolarNexusCRMTestDeliverer($crm, $payload),
            self::SOLAR_NEXUS_LEGACY => new SolarNexusLegacyCRMTestDeliverer($crm, $payload),
            self::INSIGHTLY          => new InsightlyCRMTestDeliverer($crm, $payload),
            self::PARSEABLE_EMAIL    => new ParseableEmailCRMTestDeliverer($crm, $payload),
            default                  => throw new \RuntimeException("No Test Deliverer Defined For '" . $this->name . "' CRM")
        };
    }

    /**
     * Returns the display name for a given CRM.
     *
     * @return string
     */
    public function displayName(): string
    {
        return match ($this) {
            self::STANDARD_WEB_FORM  => "Standard Web Form",
            self::LEAD_CONDUIT       => "Lead Conduit",
            self::JOB_NIMBUS         => "JobNimbus",
            self::PIPEDRIVE          => "Pipedrive",
            self::ZOHO_OAUTH         => 'Zoho CRM',
            self::SOLAR_NEXUS        => 'Solar Nexus JSON',
            self::SOLAR_NEXUS_LEGACY => 'Solar Nexus Legacy XML',
            self::INSIGHTLY          => 'Insightly',
            self::PARSEABLE_EMAIL    => 'Parseable Email',
            default                  => $this->name
        };
    }

    /**
     * @return array
     */
    public static function slugs(): array
    {
        return [
            self::STANDARD_WEB_FORM->value  => 'standard_webform',
            self::LEAD_CONDUIT->value       => 'leadconduit',
            self::JOB_NIMBUS->value         => 'job_nimbus',
            self::PIPEDRIVE->value          => 'pipedrive',
            self::ZOHO_OAUTH->value         => 'zoho_oauth',
            self::SOLAR_NEXUS->value        => 'solar_nexus_json',
            self::SOLAR_NEXUS_LEGACY->value => 'solarnexus',
            self::INSIGHTLY->value          => 'insightly',
            self::PARSEABLE_EMAIL->value    => 'parseable_email',
        ];
    }

    /**
     * @param CompanyCampaignDeliveryModuleCRM|CompanyCRMTemplate|null $crm
     * @return array
     */
    public function getFieldConfiguration(CompanyCampaignDeliveryModuleCRM|CompanyCRMTemplate|null $crm = null): array
    {
        return match ($this) {
            self::STANDARD_WEB_FORM  => StandardWebFormCRMDeliverer::getFieldConfiguration(),
            self::LEAD_CONDUIT       => LeadConduitCRMDeliverer::getFieldConfiguration(),
            self::JOB_NIMBUS         => JobNimbusCRMDeliverer::getFieldConfiguration(),
            self::PIPEDRIVE          => PipedriveCRMDeliverer::getFieldConfiguration(),
            self::ZOHO_OAUTH         => ZohoOAuthCRMDeliverer::getFieldConfiguration($crm),
            self::SOLAR_NEXUS        => SolarNexusCRMDeliverer::getFieldConfiguration(),
            self::SOLAR_NEXUS_LEGACY => SolarNexusLegacyCRMDeliverer::getFieldConfiguration(),
            self::INSIGHTLY          => InsightlyCRMDeliverer::getFieldConfiguration(),
            self::PARSEABLE_EMAIL    => ParseableEmailDeliverer::getFieldConfiguration(),
            default                  => BaseCRMDeliverer::getFieldConfiguration(),
        };
    }

    /**
     * @return array
     */
    public static function getAllFieldConfigurations(): array
    {
        return array_map(fn($case) => [
            'id'   => $case->value,
            'key'  => $case->name,
            'name' => $case->displayName(),
            ...$case->getFieldConfiguration(),
        ], self::cases());
    }
}
