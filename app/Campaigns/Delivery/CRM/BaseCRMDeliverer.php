<?php

namespace App\Campaigns\Delivery\CRM;

use App\Campaigns\Delivery\CRM\Enums\CRMFieldType;
use App\Contracts\Campaigns\Delivery\CRMDelivererContract;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleCRM;
use App\Models\Campaigns\Modules\Delivery\CompanyCRMTemplate;
use App\Models\Odin\ConsumerProduct;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Throwable;

abstract class BaseCRMDeliverer implements CRMDelivererContract
{
    protected ?Collection $cachedFields = null;

    const SYSTEM_FIELDS_KEY     = 'system_fields';
    const ADDITIONAL_FIELDS_KEY = 'additional_fields';
    const CUSTOM_FIELDS_KEY     = 'custom_fields';
    const JSON_FIELDS_KEY       = 'json_fields';
    const HEADERS_KEY           = 'headers';
    const INTERACTABLES_KEY     = 'interactables';
    const CRM_FIELD_VALUE       = 'value';
    const CRM_FIELD_KEY         = 'key';

    public function __construct(protected CompanyCampaignDeliveryModuleCRM|CompanyCRMTemplate $crm, protected array $payload = []) {}

    /**
     * @param CompanyCampaign $campaign
     * @return Collection
     */
    public function getFields(CompanyCampaign $campaign): Collection
    {
        if ($this->cachedFields !== null)
            return $this->cachedFields;

        return $this->cachedFields = collect([
            self::SYSTEM_FIELDS_KEY     => $this->getSystemFields(),
            self::ADDITIONAL_FIELDS_KEY => $this->getAdditionalFields(),
            self::CUSTOM_FIELDS_KEY     => $this->getCustomFields(),
            self::HEADERS_KEY           => $this->getHeaders(),
            self::JSON_FIELDS_KEY       => $this->getJsonFields()
        ]);
    }

    /**
     * Finds a field from the fields in the CRM.
     *
     * @param CompanyCampaign $campaign
     * @param string $key
     * @param string $type
     * @return array|null
     */
    protected function getField(CompanyCampaign $campaign, string $key, string $type = self::SYSTEM_FIELDS_KEY): ?array
    {
        return collect($this->getFields($campaign)[$type] ?? [])->first(fn($item) => $item['key'] === $key);
    }

    /**
     * @param CompanyCampaign $campaign
     * @param string $key
     * @param string $type
     * @return mixed
     */
    protected function getFieldValue(CompanyCampaign $campaign, string $key, string $type = self::SYSTEM_FIELDS_KEY): mixed
    {
        $target = $this->getField($campaign, $key, $type);

        return $target[self::CRM_FIELD_VALUE] ?? null;
    }

    /**
     * Returns a group of fields.
     *
     * @param CompanyCampaign $campaign
     * @param string $type
     * @return array
     */
    protected function getFieldGroup(CompanyCampaign $campaign, string $type): array
    {
        return collect($this->getFields($campaign)[$type] ?? [])->toArray();
    }

    /**
     * Returns multiple groups of fields merged as one array
     *
     * @param CompanyCampaign $campaign
     * @param string[] $types
     * @return array
     */
    protected function getManyFieldGroups(CompanyCampaign $campaign, array $types): array
    {
        return array_reduce($types, function(array $output, string $type) use ($campaign) {
            array_push($output, ...$this->getFieldGroup($campaign, $type));
            return $output;
        }, []);
    }

    /**
     * Returns the system fields for this CRM.
     *
     * @return array
     */
    protected function getSystemFields(): array
    {
        return $this->payload[self::SYSTEM_FIELDS_KEY] ?? $this->getDefaultSystemFields();
    }

    /**
     * Returns the headers for this CRM.
     *
     * @return array
     */
    protected function getHeaders(): array
    {
        return $this->payload[self::HEADERS_KEY] ?? $this->getDefaultHeaders() ?? [];
    }

    /**
     * Returns the additional fields for this CRM.
     *
     * @return array
     */
    protected function getAdditionalFields(): array
    {
        return $this->payload[self::ADDITIONAL_FIELDS_KEY] ?? $this->getDefaultAdditionalFields();
    }

    /**
     * @return array
     */
    protected function getCustomFields(): array
    {
        return $this->payload[self::CUSTOM_FIELDS_KEY] ?? [];
    }

    /**
     * @return array|null
     */
    protected function getJsonFields(): ?array
    {
        return $this->payload[self::JSON_FIELDS_KEY] ?? [];
    }

    /**
     * Returns the default system fields for this CRM.
     *
     * @return array
     */
    protected abstract function getDefaultSystemFields(): array;

    /**
     * Returns the default additional fields for this CRM.
     *
     * @return array
     */
    protected abstract function getDefaultAdditionalFields(): array;

    /**
     * Returns the system/additional fields configuration for the extended class
     *
     * @param CompanyCampaignDeliveryModuleCRM|CompanyCRMTemplate|null $crm
     * @return array|array[]
     */
    public static function getFieldConfiguration(CompanyCampaignDeliveryModuleCRM|CompanyCRMTemplate|null $crm = null): array
    {
        $calledClass = static::class;
        $abstractClass = self::class;

        if ($calledClass === $abstractClass)
            return [self::SYSTEM_FIELDS_KEY => [], self::ADDITIONAL_FIELDS_KEY => []];

        $dummyCrmModule = new CompanyCampaignDeliveryModuleCRM();
        $dummyCrm = new $calledClass($dummyCrmModule);

        $interactables = $dummyCrm instanceof BaseInteractableCRMDeliverer
            ? $dummyCrm->getInteractables($crm)
            : null;

        return [
            self::SYSTEM_FIELDS_KEY     => $dummyCrm->getDefaultSystemFields(),
            self::ADDITIONAL_FIELDS_KEY => $dummyCrm->getDefaultAdditionalFields(),
            self::HEADERS_KEY           => $dummyCrm->getDefaultHeaders(),
            self::JSON_FIELDS_KEY       => $dummyCrm->getDefaultJsonFields(),
            self::INTERACTABLES_KEY     => $interactables,
        ];
    }

    /**
     * Returns the default headers for this CRM.
     * Null return disables custom headers for this CRM type
     *
     * @return array|null
     */
    protected function getDefaultHeaders(): ?array
    {
        return null;
    }

    /**
     * Return default custom JSON fields for this CRM.
     * Null return disables custom JSON fields.
     * @return array|null
     */
    protected function getDefaultJsonFields(): ?array
    {
        return null;
    }

    /**
     * Formats a key and value for a given field.
     *
     * @param string $key
     * @param string|null $displayName
     * @param mixed|null $value
     * @param CRMFieldType $type
     * @param array $payload
     * @param bool $required
     * @param bool $showOnFrontend
     * @return array
     */
    protected function formatField(
        string $key,
        ?string $displayName = null,
        string|int $value = null,
        CRMFieldType $type = CRMFieldType::TEXT,
        array $payload = [],
        bool $showOnFrontend = true,
        bool $required = false,
    ): array
    {
        if ($displayName == null) {
            $displayName = Str::headline($key);
            $displayName = preg_replace("/\sid(\b)/i", ' ID$1', $displayName); // uppercase id/Id
        }

        return [
            'key'          => $key,
            'display_name' => $displayName,
            'value'        => $value,
            'type'         => $type,
            'required'     => !!$required,
            'payload'      => $payload,
            'display_flag' => $showOnFrontend,
        ];
    }

    /**
     * Handles replacing the value of a field using the replacer service.
     *
     * @param string $value
     * @param ConsumerProduct $product
     * @param CompanyCampaign $campaign
     * @return string
     */
    protected function parseFieldValue(string $value, ConsumerProduct $product, CompanyCampaign $campaign): string
    {
        /** @var CRMFieldReplacerService $service */
        $service = app(CRMFieldReplacerService::class);

        return $service->replaceField($product, $campaign, $value);
    }

    /**
     * Handles iterating through an array of fields and replacing the values.
     *
     * @param array $values
     * @param ConsumerProduct $product
     * @param CompanyCampaign $campaign
     * @param bool $removeEmpty
     * @return array
     */
    protected function parseManyFieldValues(array $values, ConsumerProduct $product, CompanyCampaign $campaign, bool $removeEmpty = false): array
    {
        $fields = collect($values)->mapWithKeys(fn($item) => [$item[self::CRM_FIELD_KEY] => $this->parseFieldValue($item[self::CRM_FIELD_VALUE] ?? "", $product, $campaign)]);

        return $removeEmpty
            ? $fields->filter()
                ->toArray()
            : $fields->toArray();
    }

    /**
     * Handles replacing values in custom JSON fields
     *
     * @param ConsumerProduct $product
     * @param CompanyCampaign $campaign
     * @return array
     */
    protected function parseJsonFieldValues(ConsumerProduct $product, CompanyCampaign $campaign): array
    {
        $jsonFields = $this->getJsonFields();
        if ($jsonFields) {
            try {
                $replacedFields = collect($jsonFields)->map(function ($jsonField) use ($product, $campaign) {
                    $value = $jsonField['value'] ?? null;
                    $key = $jsonField['key'] ?? null;
                    if ($value && $key) {
                        $replaced = $this->parseFieldValue($value, $product, $campaign);
                        $removeControls = preg_replace("/\\\+[nt]/", '', $replaced);
                        $removeEscapes = preg_replace("/\\\+\"/", '"', $removeControls);
                        $restored = json_decode(preg_replace("/\n/", ' ', $removeEscapes), true);

                        return [
                            'key'   => $key,
                            'value' => $restored
                        ];
                    }

                    return null;
                })->filter()
                ->toArray();

                return $replacedFields;
            }
            catch (Throwable $exception) {
                logger()->error("Error parsing JSON fields in CRM - CompanyCampaign ID ". $campaign->id);
            }
        }

        return [];
    }

    /**
     * @param CRMDeliveryServiceContract $deliveryService
     * @param ConsumerProduct $product
     * @param CompanyCampaign $campaign
     * @return CRMDeliveryServiceContract
     */
    protected function getDeliveryProxy(
        CRMDeliveryServiceContract $deliveryService,
        ConsumerProduct            $product,
        CompanyCampaign            $campaign
    ): CRMDeliveryServiceContract
    {
        return new CRMDeliveryServiceLoggerProxy(
            $deliveryService,
            $campaign,
            $product,
            $this->crm
        );
    }
}
