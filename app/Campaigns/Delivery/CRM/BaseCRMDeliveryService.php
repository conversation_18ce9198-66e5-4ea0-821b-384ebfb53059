<?php

namespace App\Campaigns\Delivery\CRM;

use App\Models\Campaigns\CompanyCampaign;
use Illuminate\Support\Facades\App;

abstract class BaseCRMDeliveryService implements CRMDeliveryServiceContract
{
    const string CRM_DRIVER_DUMMY = 'dummy';
    const string CRM_DRIVER_TEST  = 'test';
    const string CRM_DRIVER_LIVE  = 'live';

    const string API_KEY = '8a014792-8db3-43b9-bd3e-b0ed83d8d80d';

    protected CompanyCampaign $campaign;

    /**
     * @return CRMDeliveryResponse
     */
    abstract protected function deliver(): CRMDeliveryResponse;

    /**
     * This sends a test delivery to a test endpoint specified in config.services.crm_delivery.test_url
     * This does NOT send a Test lead to the real CRM service - use the TestDeliverer classes for that
     * debugDelivery() will only work outside Production environment
     * @return CRMDeliveryResponse
     */
    abstract protected function debugDelivery(): CRMDeliveryResponse;

    /**
     * @inheritDoc
     */
    public function prepareDelivery(CompanyCampaign $campaign): CRMDeliveryResponse
    {
        if (App::isProduction())
            return $this->deliver();

        $nonProdDriver = config('services.crm_delivery.driver');

        if ($nonProdDriver === self::CRM_DRIVER_LIVE) {
            return $this->deliver();
        }
        else if ($nonProdDriver === self::CRM_DRIVER_TEST) {
            $this->campaign = $campaign;

            return $this->debugDelivery();
        }
        else {
            return $this->dummyDelivery();
        }
    }

    /**
     * @return CRMDeliveryResponse
     */
    public function dummyDelivery(): CRMDeliveryResponse
    {
        return new CRMDeliveryResponse(true, [
            'body'  => [
                'dummy' => 'response'
            ]
        ]);
    }

    /**
     * @param string|null $append
     * @return string
     */
    protected function getDebugUrl(?string $append = ''): string
    {
        $base = config('services.crm_delivery.test_url');

        return str_ends_with($base, '/')
            ? "$base$append"
            : "$base/$append";
    }

    /**
     * @param array $headers
     * @return void
     */
    protected function setTestHeaders(array &$headers): void
    {
        $headers['Authorization'] = config('services.crm_delivery.test_secret');
        $headers['company-name'] = $this->campaign?->company->name ?? "";
        $headers['campaign-name'] = $this->campaign?->name ?? "";
        $headers['product-name'] = $this->campaign->product->name;
    }
}
