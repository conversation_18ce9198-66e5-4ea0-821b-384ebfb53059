<?php

namespace App\Campaigns\Delivery\CRM;

use App\Campaigns\Delivery\CRM\Enums\CRMType;
use App\Contracts\Campaigns\Delivery\DelivererContract;
use App\Jobs\TrackCRMDeliveryJob;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleCRM;
use App\Models\Odin\ConsumerProduct;
use Throwable;

class CRMDeliverer implements DelivererContract
{
    public function __construct(
        protected CRMType $type,
        protected bool    $active,
        protected string  $displayName,
        protected array   $payload,
        protected ?int    $id,
        protected ?int    $templateId = null,
    ) {}

    /**
     * @inheritDoc
     */
    public function deliver(ConsumerProduct $product, CompanyCampaign $campaign): bool
    {
        try {
            return $this->type->getConcrete($this->getModel($campaign), $this->payload)->deliver($product, $campaign);
        }
        catch(Throwable $e) {
            TrackCRMDeliveryJob::dispatch(
                new CRMDeliveryResponse(false, ['error' => $e->getMessage()]),
                $campaign,
                $campaign->deliveryModule,
                $this->getModel($campaign),
                $product
            );
            logger()->error("CRM delivery error thrown - " . $e->getMessage());

            return false;
        }
    }

    /**
     * @param CompanyCampaign $campaign
     * @return CompanyCampaignDeliveryModuleCRM
     */
    protected function getModel(CompanyCampaign $campaign): CompanyCampaignDeliveryModuleCRM
    {
        return $campaign->deliveryModule->crms()
            ->find($this->id)
            ?? $this->saveModel($campaign);
    }

    /**
     * @inheritDoc
     */
    public function save(CompanyCampaign $campaign): bool
    {
        return !!$this->saveModel($campaign);
    }

    /**
     * @param CompanyCampaign $campaign
     * @return CompanyCampaignDeliveryModuleCRM
     */
    protected function saveModel(CompanyCampaign $campaign): CompanyCampaignDeliveryModuleCRM
    {
        // For Template linked deliveries, only one should exist per campaign
        if ($this->templateId > 0) {
            $model = CompanyCampaignDeliveryModuleCRM::query()
                ->updateOrCreate([
                    CompanyCampaignDeliveryModuleCRM::FIELD_MODULE_ID   => $campaign->deliveryModule()->firstOrCreate()->id,
                    CompanyCampaignDeliveryModuleCRM::FIELD_TEMPLATE_ID => $this->templateId,
                ], [
                    CompanyCampaignDeliveryModuleCRM::FIELD_PAYLOAD      => [],
                    CompanyCampaignDeliveryModuleCRM::FIELD_CRM_TYPE     => CRMType::STANDARD_WEB_FORM,
                    CompanyCampaignDeliveryModuleCRM::FIELD_ACTIVE       => $this->active,
                    CompanyCampaignDeliveryModuleCRM::FIELD_DISPLAY_NAME => '',
                ]);
        }
        else {
            /** @var CompanyCampaignDeliveryModuleCRM $model */
            $model = CompanyCampaignDeliveryModuleCRM::query()->updateOrCreate([
                CompanyCampaignDeliveryModuleCRM::FIELD_ID => $this->id ?? null,
            ], [
                CompanyCampaignDeliveryModuleCRM::FIELD_CRM_TYPE     => $this->type,
                CompanyCampaignDeliveryModuleCRM::FIELD_MODULE_ID    => $campaign->deliveryModule()->firstOrCreate()->id,
                CompanyCampaignDeliveryModuleCRM::FIELD_ACTIVE       => $this->active,
                CompanyCampaignDeliveryModuleCRM::FIELD_DISPLAY_NAME => $this->templateId ? '' : $this->displayName,
                CompanyCampaignDeliveryModuleCRM::FIELD_PAYLOAD      => $this->templateId ? [] : $this->payload,
                CompanyCampaignDeliveryModuleCRM::FIELD_TEMPLATE_ID  => $this->templateId,
            ]);
        }

        return $model;
    }

    /**
     * Update only outer details of deliverer, no payload
     *
     * @param CompanyCampaign $campaign
     * @return bool
     */
    public function updateBasicDetails(CompanyCampaign $campaign): bool
    {
        /** @var CompanyCampaignDeliveryModuleCRM|null $model */
        $model = CompanyCampaignDeliveryModuleCRM::query()
            ->find($this->id);

        if ($model) {
            return $model?->update([
                CompanyCampaignDeliveryModuleCRM::FIELD_ACTIVE       => $this->active,
                CompanyCampaignDeliveryModuleCRM::FIELD_DISPLAY_NAME => $this->displayName,
            ]) ?? false;
        }
        else {
            return !!CompanyCampaignDeliveryModuleCRM::query()
                ->create([
                    CompanyCampaignDeliveryModuleCRM::FIELD_CRM_TYPE     => $this->type,
                    CompanyCampaignDeliveryModuleCRM::FIELD_DISPLAY_NAME => $this->displayName,
                    CompanyCampaignDeliveryModuleCRM::FIELD_ACTIVE       => false,
                    CompanyCampaignDeliveryModuleCRM::FIELD_MODULE_ID    => $campaign->deliveryModule()->firstOrCreate()->id,
                    CompanyCampaignDeliveryModuleCRM::FIELD_PAYLOAD      => [],
                    CompanyCampaignDeliveryModuleCRM::FIELD_TEMPLATE_ID  => $this->templateId,
                ]);
        }
    }

    /**
     * @return bool
     */
    public function isActive(): bool
    {
        return $this->active;
    }
}
