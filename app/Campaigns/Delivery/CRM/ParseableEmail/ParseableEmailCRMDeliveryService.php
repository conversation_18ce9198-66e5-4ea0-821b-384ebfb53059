<?php

namespace App\Campaigns\Delivery\CRM\ParseableEmail;

use App\Campaigns\Delivery\CRM\BaseCRMDeliveryService;
use App\Campaigns\Delivery\CRM\CRMDeliveryResponse;
use App\Mail\ParseableEmail;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;

class ParseableEmailCRMDeliveryService extends BaseCRMDeliveryService
{
    const string SYSTEM_FIELD_EMAIL    = 'email_address';
    const string SYSTEM_FIELD_SUBJECT  = 'email_subject';
    const string DEFAULT_EMAIL_SUBJECT = 'New [brand] Lead';

    const string FIELD_KEY_START_DELIMITER   = 'key_start_delimiter';
    const string FIELD_KEY_END_DELIMITER     = 'key_end_delimiter';
    const string FIELD_VALUE_START_DELIMITER = 'value_start_delimiter';
    const string FIELD_VALUE_END_DELIMITER   = 'value_end_delimiter';
    const string FIELD_KEY_VALUE_DELIMITER   = 'key_value_delimiter';
    const string FIELD_PAIR_DELIMITER        = 'pair_delimiter';

    const string DEFAULT_KEY_START_DELIMITER   = '';
    const string DEFAULT_KEY_END_DELIMITER     = '';
    const string DEFAULT_VALUE_START_DELIMITER = '';
    const string DEFAULT_VALUE_END_DELIMITER   = '';
    const string DEFAULT_PAIR_DELIMITER        = '||';
    const string DEFAULT_KEY_VALUE_DELIMITER   = '&&';

    const string BODY_EMAIL_KEY = 'email';

    const string TEST_KEY = 'parseable-email-test';

    protected string $emailAddress;
    protected string $subject = self::DEFAULT_EMAIL_SUBJECT;
    protected array $body = [];

    /**
     * @inheritDoc
     */
    public function deliver(): CRMDeliveryResponse
    {
        $mailable = new ParseableEmail($this->body[self::BODY_EMAIL_KEY], $this->subject);
        $response = Mail::to($this->emailAddress)
            ->send($mailable);

        return new CRMDeliveryResponse(!!$response, ['to' => $this->emailAddress, 'body' => $this->body[self::BODY_EMAIL_KEY]]);
    }

    /**
     * @param string $email
     * @return self
     */
    public function setEmailAddress(string $email): self
    {
        $this->emailAddress = $email;

        return $this;
    }

    /**
     * @param string|null $subject
     * @return $this
     */
    public function setSubject(?string $subject): self
    {
        $this->subject = $subject ?: self::DEFAULT_EMAIL_SUBJECT;

        return $this;
    }

    /**
     * @param array $additionalFields
     * @param array $customFields
     * @return self
     */
    public function setBody(array $additionalFields, array $customFields): self
    {
        $keyValueDelimiter = $additionalFields[self::FIELD_KEY_VALUE_DELIMITER] ?? self::DEFAULT_KEY_VALUE_DELIMITER;
        $pairDelimiter = $additionalFields[self::FIELD_PAIR_DELIMITER] ?? self::DEFAULT_PAIR_DELIMITER;
        $keyStartDelimiter = $additionalFields[self::FIELD_KEY_START_DELIMITER] ?? self::DEFAULT_KEY_START_DELIMITER;
        $keyEndDelimiter = $additionalFields[self::FIELD_KEY_END_DELIMITER] ?? self::DEFAULT_KEY_END_DELIMITER;
        $valueStartDelimiter = $additionalFields[self::FIELD_VALUE_START_DELIMITER] ?? self::DEFAULT_VALUE_START_DELIMITER;
        $valueEndDelimiter = $additionalFields[self::FIELD_VALUE_END_DELIMITER] ?? self::DEFAULT_VALUE_END_DELIMITER;

        foreach ($customFields as $customKey => $customValue) {
            $this->body[] = "$keyStartDelimiter$customKey$keyEndDelimiter$keyValueDelimiter$valueStartDelimiter$customValue$valueEndDelimiter";
        }

        $this->body[self::BODY_EMAIL_KEY] = implode($pairDelimiter, $this->body);

        return $this;
    }

    /**
     * @inheritDoc
     * @throws ConnectionException
     */
    public function debugDelivery(): CRMDeliveryResponse
    {
        $url = $this->getDebugUrl(self::TEST_KEY);
        $headers = [];
        $this->setTestHeaders($headers);

        $response = Http::withHeaders($headers)
            ->withBody(json_encode($this->body))
            ->post($url);

        return new CRMDeliveryResponse($response->status(), ['body' => $response->body()]);
    }

    /**
     * @return array
     */
    public function getBody(): array
    {
        return [...$this->body];
    }
}
