<?php

namespace App\Campaigns\Delivery\CRM\ParseableEmail;

use App\Campaigns\Delivery\CRM\BaseCRMDeliverer;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ConsumerProduct;

class ParseableEmailDeliverer extends BaseCRMDeliverer
{
    /**
     * @inheritDoc
     */
    public function deliver(ConsumerProduct $product, CompanyCampaign $campaign, ?array $payload = null): bool
    {
        /** @var ParseableEmailCRMDeliveryService $deliveryService */
        $deliveryService = app(ParseableEmailCRMDeliveryService::class);

        $additionalFields = $this->parseManyFieldValues($this->getManyFieldGroups($campaign, [self::CUSTOM_FIELDS_KEY]), $product, $campaign);
        $customFields = $this->parseManyFieldValues($this->getManyFieldGroups($campaign, [self::CUSTOM_FIELDS_KEY]), $product, $campaign);
        $email = $this->getFieldValue($campaign, ParseableEmailCRMDeliveryService::SYSTEM_FIELD_EMAIL);
        $rawSubject = $this->getFieldValue($campaign, ParseableEmailCRMDeliveryService::SYSTEM_FIELD_SUBJECT) ?: ParseableEmailCRMDeliveryService::DEFAULT_EMAIL_SUBJECT;

        return $this->getDeliveryProxy(
            $deliveryService->setEmailAddress($email)
                ->setSubject($this->parseFieldValue($rawSubject, $product, $campaign))
                ->setBody($additionalFields, $customFields),
            $product,
            $campaign
        )->prepareDelivery($campaign)->success;
    }

    /**
     * @inheritDoc
     */
    protected function getDefaultSystemFields(): array
    {
        return [
            $this->formatField(ParseableEmailCRMDeliveryService::SYSTEM_FIELD_EMAIL, 'Email Address', required: true),
            $this->formatField(
                ParseableEmailCRMDeliveryService::SYSTEM_FIELD_SUBJECT,
                'Email Subject',
                value: ParseableEmailCRMDeliveryService::DEFAULT_EMAIL_SUBJECT,
                required: true,
            ),
        ];
    }

    /**
     * @inheritDoc
     *
     */
    protected function getDefaultAdditionalFields(): array
    {
        return [
            $this->formatField(ParseableEmailCRMDeliveryService::FIELD_KEY_VALUE_DELIMITER, displayName: 'Key Value Delimiter', value: ParseableEmailCRMDeliveryService::DEFAULT_KEY_VALUE_DELIMITER),
            $this->formatField(ParseableEmailCRMDeliveryService::FIELD_PAIR_DELIMITER, displayName: 'Pair Delimiter', value: ParseableEmailCRMDeliveryService::DEFAULT_PAIR_DELIMITER),
            $this->formatField(ParseableEmailCRMDeliveryService::FIELD_KEY_START_DELIMITER, displayName: 'Key Start Delimiter', value: ParseableEmailCRMDeliveryService::DEFAULT_KEY_START_DELIMITER),
            $this->formatField(ParseableEmailCRMDeliveryService::FIELD_KEY_END_DELIMITER, displayName: 'Key End Delimiter', value: ParseableEmailCRMDeliveryService::DEFAULT_KEY_END_DELIMITER),
            $this->formatField(ParseableEmailCRMDeliveryService::FIELD_VALUE_START_DELIMITER, displayName: 'Value Start Delimiter', value: ParseableEmailCRMDeliveryService::DEFAULT_VALUE_START_DELIMITER),
            $this->formatField(ParseableEmailCRMDeliveryService::FIELD_VALUE_END_DELIMITER, displayName: 'Value End Delimiter', value: ParseableEmailCRMDeliveryService::DEFAULT_VALUE_END_DELIMITER),
        ];
    }
}
