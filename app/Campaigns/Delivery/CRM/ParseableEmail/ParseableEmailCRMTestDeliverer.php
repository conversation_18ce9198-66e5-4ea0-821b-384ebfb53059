<?php

namespace App\Campaigns\Delivery\CRM\ParseableEmail;

use App\Campaigns\Delivery\CRM\BaseCRMTestDeliverer;
use App\Campaigns\Delivery\CRM\CRMTestDeliveryResponse;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ConsumerProduct;
use Override;

class ParseableEmailCRMTestDeliverer extends BaseCRMTestDeliverer
{
    /**
     * @inheritDoc
     */
    #[Override] protected function getDefaultSystemFields(): array
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    #[Override] protected function getDefaultAdditionalFields(): array
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    #[Override] public function deliver(ConsumerProduct $product, CompanyCampaign $campaign, ?array $payload = null): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    #[Override] public function deliverTestLead(): CRMTestDeliveryResponse
    {
        /** @var ParseableEmailCRMDeliveryService $deliveryService */
        $deliveryService = app(ParseableEmailCRMDeliveryService::class);

        $campaign = $this->crm->module->campaign;
        $email = $this->getFieldValue($campaign, ParseableEmailCRMDeliveryService::SYSTEM_FIELD_EMAIL);
        $additionalFields = $this->parseManyFieldValuesForFakeData($this->getManyFieldGroups($campaign, [self::ADDITIONAL_FIELDS_KEY]), $campaign);
        $customFields = $this->parseManyFieldValuesForFakeData($this->getManyFieldGroups($campaign, [self::CUSTOM_FIELDS_KEY]), $campaign);
        $rawSubject = $this->getFieldValue($campaign, ParseableEmailCRMDeliveryService::SYSTEM_FIELD_SUBJECT) ?: ParseableEmailCRMDeliveryService::DEFAULT_EMAIL_SUBJECT;

        $deliveryService->setEmailAddress($email)
            ->setBody($additionalFields, $customFields)
            ->setSubject($this->parseFieldValueForFakeData($rawSubject, $campaign));

        $requestBody = $deliveryService->getBody();
        $response = $deliveryService->deliver();

        return new CRMTestDeliveryResponse(
            url: $email,
            requestBody: $requestBody,
            headers: ['Content-Type: text/html; charset=utf-8', 'Content-Transfer-Encoding: quoted-printable'],
            success: $response->success,
            responseBody: $response->payload
        );
    }
}
