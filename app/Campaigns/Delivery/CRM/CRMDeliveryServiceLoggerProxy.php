<?php

namespace App\Campaigns\Delivery\CRM;

use App\Jobs\TrackCRMDeliveryJob;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleCRM;
use App\Models\Odin\ConsumerProduct;
use Exception;

class CRMDeliveryServiceLoggerProxy implements CRMDeliveryServiceContract
{
    public function __construct(
        protected CRMDeliveryServiceContract       $deliveryService,
        protected CompanyCampaign                  $campaign,
        protected ConsumerProduct                  $product,
        protected CompanyCampaignDeliveryModuleCRM $crm
    ) {}

    /**
     * @inheritDoc
     */
    public function prepareDelivery(CompanyCampaign $campaign): CRMDeliveryResponse
    {
        try {
            $result = $this->deliveryService->prepareDelivery($campaign);
        }
        catch(Exception $e) {
            $result = new CRMDeliveryResponse(false, ['error' => $e->getMessage()]);
        }
        TrackCRMDeliveryJob::dispatch($result, $this->campaign, $this->crm->module, $this->crm, $this->product);

        return $result;
    }
}
