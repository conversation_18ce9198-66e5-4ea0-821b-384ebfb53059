<?php

namespace App\Campaigns\Delivery\CRM;

use App\Models\Campaigns\CompanyCampaign;

abstract class BaseCRMTestDeliverer extends BaseCRMDeliverer
{
    protected array $testData = [];

    /**
     * @param array $data
     *
     * @return $this
     */
    public function setTestData(array $data): static
    {
        $this->testData = $data;

        return $this;
    }

    /**
     * Deliver test lead to the CRM
     *
     * @return CRMTestDeliveryResponse
     */
    abstract public function deliverTestLead(): CRMTestDeliveryResponse;

    /**
     * Handles iterating through an array of fields and replacing the values.
     *
     * @param array $values
     * @param CompanyCampaign $campaign
     * @param bool $removeEmpty
     *
     * @return array
     */
    protected function parseManyFieldValuesForFakeData(array $values, CompanyCampaign $campaign, bool $removeEmpty = false): array
    {
        $fields = collect($values)->mapWithKeys(fn($item) => [$item[self::CRM_FIELD_KEY] => $this->parseFieldValueForFakeData($item[self::CRM_FIELD_VALUE] ?? "", $campaign)]);
        $fields = $removeEmpty
            ? $fields->filter()
                ->toArray()
            : $fields->toArray();

        foreach ($this->testData as $data) {
            $key = $data['key'] ?? null;
            $value = $data['value'] ?? null;

            if ($key !== null && $value !== null && array_key_exists($key, $fields)) {
                $fields[$key] = $value;
            }
        }

        return $fields;
    }

    /**
     * Handles replacing the value of a field using the replacer service.
     *
     * @param string $value
     * @param CompanyCampaign $campaign
     *
     * @return string
     */
    protected function parseFieldValueForFakeData(string $value, CompanyCampaign $campaign): string
    {
        /** @var CRMFieldFakerReplacementService $service */
        $service = app(CRMFieldFakerReplacementService::class);

        return $service->replaceField($value, $campaign);
    }
}
