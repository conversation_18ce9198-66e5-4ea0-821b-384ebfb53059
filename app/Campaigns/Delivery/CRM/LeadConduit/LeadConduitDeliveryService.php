<?php

namespace App\Campaigns\Delivery\CRM\LeadConduit;

use App\Campaigns\Delivery\CRM\BaseCRMDeliveryService;
use App\Campaigns\Delivery\CRM\CRMDeliveryResponse;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class LeadConduitDeliveryService extends BaseCRMDeliveryService
{
    const string RESPONSE_OUTCOME_KEY = 'outcome';

    const string TEST_KEY = 'lead-conduit';

    protected string $url;
    protected array  $body    = [];
    protected array  $headers = [];

    /**
     * @inheritDoc
     */
    public function deliver(): CRMDeliveryResponse
    {
        if (!$this->url)
            return new CRMDeliveryResponse(false, ['error' => 'Bad LeadConduit CRM configuration, no URL field.']);

        $response = Http::withHeaders($this->headers)
            ->post($this->url, $this->body);

        return new CRMDeliveryResponse($this->getResponseOutcome($response), ['body' => $response->body()]);
    }

    /**
     * @inheritDoc
     */
    public function debugDelivery(): CRMDeliveryResponse
    {
        $this->url = $this->getDebugUrl(self::TEST_KEY);
        $this->setTestHeaders($this->headers);

        return $this->deliver();
    }

    /**
     * @param string|null $url
     * @return LeadConduitDeliveryService
     */
    public function setUrl(?string $url): LeadConduitDeliveryService
    {
        $this->url = $url ?? "";

        return $this;
    }

    /**
     * @param array $body
     * @return LeadConduitDeliveryService
     */
    public function setBody(array $body): LeadConduitDeliveryService
    {
        $this->body = $body;
        $this->cloneValuesToOldKeys();

        return $this;
    }

    /**
     * @param array $headers
     * @return LeadConduitDeliveryService
     */
    public function setHeaders(array $headers): LeadConduitDeliveryService
    {
        $this->headers = $headers;

        return $this;
    }

    /**
     * @return array
     */
    public function getBody(): array
    {
        return $this->body;
    }

    /**
     * Service should return a 201 with the outcome key on a success
     * Returns a 200 for different failure outcomes potentially with an empty body
     *
     * @param Response $response
     * @return bool
     */
    protected function getResponseOutcome(Response $response): bool
    {
        $body = $response->json() ?? [];
        $outcome = $body[self::RESPONSE_OUTCOME_KEY] ?? null;

        return $outcome
            ? preg_match("/success/i", $outcome)
            : $response->status() === 201;
    }

    /**
     * Clone the current leadconduit key values using the old keys
     * @return void
     */
    private function cloneValuesToOldKeys(): void
    {
        $changedKeys = [
            'address'          => 'address_1',
            'number_of_buyers' => 'num_buyers',
            'phone'            => 'phone_1',
            'zip_code'         => 'postal_code',
        ];
        foreach ($changedKeys as $key => $oldKey) {
            if (array_key_exists($key, $this->body)) {
                if (!array_key_exists($oldKey, $this->body) || empty($this->body[$oldKey])) {
                    $this->body[$oldKey] = $this->body[$key];
                }
            }
        }
    }
}
