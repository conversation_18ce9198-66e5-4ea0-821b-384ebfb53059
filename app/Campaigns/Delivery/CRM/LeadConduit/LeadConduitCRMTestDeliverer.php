<?php

namespace App\Campaigns\Delivery\CRM\LeadConduit;

use App\Campaigns\Delivery\CRM\BaseCRMTestDeliverer;
use App\Campaigns\Delivery\CRM\CRMTestDeliveryResponse;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ConsumerProduct;

class LeadConduitCRMTestDeliverer extends BaseCRMTestDeliverer
{
    /**
     * @inheritDoc
     */
    #[\Override] protected function getDefaultSystemFields(): array
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    #[\Override] protected function getDefaultAdditionalFields(): array
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    #[\Override] public function deliver(ConsumerProduct $product, CompanyCampaign $campaign, ?array $payload = null): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    #[\Override] public function deliverTestLead(): CRMTestDeliveryResponse
    {
        /** @var LeadConduitDeliveryService $deliveryService */
        $deliveryService = app(LeadConduitDeliveryService::class);

        $campaign = $this->crm->module->campaign;
        $url = $this->getFieldValue($campaign, 'url');
        $requestBody = $this->parseManyFieldValuesForFakeData($this->getManyFieldGroups($campaign, [self::ADDITIONAL_FIELDS_KEY, self::CUSTOM_FIELDS_KEY]), $campaign);
        $headers = $this->parseManyFieldValuesForFakeData($this->getFieldGroup($campaign, self::HEADERS_KEY), $campaign);

        $delivery = $deliveryService->setUrl($url)
            ->setBody($requestBody)
            ->setHeaders($headers);

        $response = $delivery->deliver();

        return new CRMTestDeliveryResponse(
            url: $url,
            requestBody: $delivery->getBody(),
            headers: $headers,
            success: $response->success,
            responseBody: $response->payload
        );
    }
}
