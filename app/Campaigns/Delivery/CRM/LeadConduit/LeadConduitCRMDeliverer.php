<?php

namespace App\Campaigns\Delivery\CRM\LeadConduit;

use App\Campaigns\Delivery\CRM\BaseCRMDeliverer;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ConsumerProduct;

class LeadConduitCRMDeliverer extends BaseCRMDeliverer
{
    /**
     * @inheritDoc
     */
    protected function getDefaultSystemFields(): array
    {
        return [
            $this->formatField('url', 'URL', required: true)
        ];
    }

    /**
     * @inheritDoc
     */
    protected function getDefaultAdditionalFields(): array
    {
        return [
            $this->formatField('first_name', value: '[first_name]'),
            $this->formatField('last_name', value: '[last_name]'),
            $this->formatField('phone', value: '[phone]'),
            $this->formatField('email', value: '[email]'),
            $this->formatField('address', value: '[address]'),
            $this->formatField('state', value: '[state]'),
            $this->formatField('city', value: '[city]'),
            $this->formatField('postal_code', displayName: 'Zip Code', value: '[zip_code]'),
            $this->formatField('comments', value: '[comments]'),
            $this->formatField('lead_id', value: '[lead_id]'),
            $this->formatField('number_of_buyers', value: '[lead_sale_count]'),
            $this->formatField('price', value: '[lead_price]'),
        ];
    }

    /**
     * @inheritDoc
     */
    public function deliver(ConsumerProduct $product, CompanyCampaign $campaign, ?array $payload = null): bool
    {
        /** @var LeadConduitDeliveryService $deliveryService */
        $deliveryService = app(LeadConduitDeliveryService::class);

        return $this->getDeliveryProxy(
            $deliveryService->setUrl($this->getFieldValue($campaign, 'url'))
                ->setBody($this->parseManyFieldValues($this->getManyFieldGroups($campaign, [self::ADDITIONAL_FIELDS_KEY, self::CUSTOM_FIELDS_KEY]), $product, $campaign))
                ->setHeaders($this->parseManyFieldValues($this->getFieldGroup($campaign, self::HEADERS_KEY), $product, $campaign)),
            $product,
            $campaign
        )->prepareDelivery($campaign)->success;
    }
}
