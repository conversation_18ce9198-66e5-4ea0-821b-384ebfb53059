<?php

namespace App\Campaigns\Delivery\CRM\WebForm;

use App\Campaigns\Delivery\CRM\BaseCRMTestDeliverer;
use App\Campaigns\Delivery\CRM\CRMTestDeliveryResponse;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ConsumerProduct;
use Throwable;

class StandardWebFormCRMTestDeliverer extends BaseCRMTestDeliverer
{
    /**
     * @inheritDoc
     */
    #[\Override] protected function getDefaultAdditionalFields(): array
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    #[\Override] public function deliver(ConsumerProduct $product, CompanyCampaign $campaign, ?array $payload = null): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    #[\Override] protected function getDefaultSystemFields(): array
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    #[\Override] public function deliverTestLead(): CRMTestDeliveryResponse
    {
        /** @var StandardWebFormDeliveryService $deliveryService */
        $deliveryService = app(StandardWebFormDeliveryService::class);

        $campaign = $this->crm->module->campaign;
        $url = $this->getFieldValue($campaign, 'url');
        $requestBody = $this->parseManyFieldValuesForFakeData($this->getManyFieldGroups($campaign, [self::ADDITIONAL_FIELDS_KEY, self::CUSTOM_FIELDS_KEY]), $campaign, true);
        $headers = $this->parseManyFieldValuesForFakeData($this->getFieldGroup($campaign, self::HEADERS_KEY), $campaign);

        // Merge custom JSON keys into body content
        $sendFormat = $this->getFieldValue($campaign, 'send_format');
        if (strtoupper($sendFormat) === 'JSON') {
            $jsonContent = $this->parseJsonFieldValuesForFakeData($campaign);
            if ($jsonContent)
                $this->mergeJsonContent($requestBody, $jsonContent);
        }

        $delivery = $deliveryService->setUrl($url)
            ->setSendFormat($this->getFieldValue($campaign, 'send_format'))
            ->setSuccessResponse($this->getFieldValue($campaign, 'success_keyword'))
            ->setErrorResponse($this->getFieldValue($campaign, 'error_keyword'))
            ->setBody($requestBody)
            ->setHeaders($headers);

        $response = $delivery->deliver();

        return new CRMTestDeliveryResponse(
            url: $url,
            requestBody: $delivery->getBody(),
            headers: $headers,
            success: $response->success,
            responseBody: $response->payload
        );
    }

    /**
     * Merge custom JSON fields with standard fields. Body key overwrite entire payload
     * @param array $body
     * @param array $parsedJsonContent
     * @return void
     */
    protected function mergeJsonContent(array &$body, array $parsedJsonContent): void
    {
        foreach($parsedJsonContent as $jsonField) {
            $key = $jsonField['key'] ?? null;
            $value = $jsonField['value'] ?? null;
            if ($key && $value) {
                if ($key === 'body')
                    $body = $value;
                else
                    $body[$key] = $value;
            }
        }
    }

    /**
     * Handles replacing values in custom JSON fields
     *
     * @param CompanyCampaign $campaign
     * @return array
     */
    protected function parseJsonFieldValuesForFakeData(CompanyCampaign $campaign): array
    {
        $jsonFields = $this->getJsonFields();
        if ($jsonFields) {
            try {
                $replacedFields = collect($jsonFields)->map(function ($jsonField) use ($campaign) {
                    $value = $jsonField['value'] ?? null;
                    $key = $jsonField['key'] ?? null;
                    if ($value && $key) {
                        $replaced = $this->parseFieldValueForFakeData($value, $campaign);
                        $removeControls = preg_replace("/\\\+[nt]/", '', $replaced);
                        $removeEscapes = preg_replace("/\\\+\"/", '"', $removeControls);
                        $restored = json_decode(preg_replace("/\n/", '', $removeEscapes), true);

                        return [
                            'key'   => $key,
                            'value' => $restored
                        ];
                    }

                    return null;
                })->filter()
                ->toArray();

                return $replacedFields;
            }
            catch (Throwable $exception) {
                logger()->error("Error parsing JSON fields in CRM - CompanyCampaign ID ". $campaign->id);
            }
        }

        return [];
    }
}
