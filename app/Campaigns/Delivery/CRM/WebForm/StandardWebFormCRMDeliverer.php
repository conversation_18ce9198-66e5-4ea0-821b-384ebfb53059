<?php

namespace App\Campaigns\Delivery\CRM\WebForm;

use App\Campaigns\Delivery\CRM\BaseCRMDeliverer;
use App\Campaigns\Delivery\CRM\Enums\CRMFieldType;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ConsumerProduct;

class StandardWebFormCRMDeliverer extends BaseCRMDeliverer
{
    const string FIELD_SEND_FORMAT         = 'send_format';
    const string DEFAULT_VALUE_SEND_FORMAT = 'JSON';

    /**
     * @inheritDoc
     */
    public function deliver(ConsumerProduct $product, CompanyCampaign $campaign, ?array $payload = null): bool
    {
        logger()->info('Start CRM delivery', [
            'company_id'          => $campaign->company_id,
            'campaign_id'         => $campaign->id,
            'consumer_product_id' => $product->id,
            'payload'             => $payload,
            'consumer'            => $product->consumer
        ]);

        /** @var StandardWebFormDeliveryService $deliveryService */
        $deliveryService = app(StandardWebFormDeliveryService::class);

        // webform can be sent without additional fields - ensure defaults are not fetched if empty
        $useFieldGroups = ($this->payload[self::ADDITIONAL_FIELDS_KEY] ?? false)
            ? [self::ADDITIONAL_FIELDS_KEY, self::CUSTOM_FIELDS_KEY]
            : [self::CUSTOM_FIELDS_KEY];

        $sendFormat = $this->getFieldValue($campaign, 'send_format');
        $successResponse = $this->getFieldValue($campaign, 'success_keyword');
        $errorResponse = $this->getFieldValue($campaign, 'error_keyword');
        $body = $this->parseManyFieldValues($this->getManyFieldGroups($campaign, $useFieldGroups), $product, $campaign, true);
        // Merge custom JSON keys into body content
        if (strtoupper($sendFormat) === 'JSON') {
            $jsonContent = $this->parseJsonFieldValues($product, $campaign);
            if ($jsonContent)
                $this->mergeJsonContent($body, $jsonContent);
        }
        $headers = $this->parseManyFieldValues($this->getFieldGroup($campaign, self::HEADERS_KEY), $product, $campaign);

        logger()->info('Prepare Data', [
            '$sendFormat'      => $sendFormat,
            '$successResponse' => $successResponse,
            '$errorResponse'   => $errorResponse,
            '$body'            => $body,
            '$headers'         => $headers,
        ]);

        return $this->getDeliveryProxy(
            $deliveryService->setUrl($this->getFieldValue($campaign, 'url'))
                ->setSendFormat($sendFormat)
                ->setSuccessResponse($successResponse)
                ->setErrorResponse($errorResponse)
                ->setBody($body)
                ->setHeaders($headers),
            $product,
            $campaign
        )->prepareDelivery($campaign)->success;
    }

    /**
     * @return array|null
     */
    protected function getDefaultHeaders(): ?array
    {
        return [];
    }

    /**
     * @return array
     */
    protected function getDefaultJsonFields(): array
    {
        return [];
    }

    /**
     * Merge custom JSON fields with standard fields. Body key overwrite entire payload
     * @param array $body
     * @param array $parsedJsonContent
     * @return void
     */
    protected function mergeJsonContent(array &$body, array $parsedJsonContent): void
    {
        foreach($parsedJsonContent as $jsonField) {
            $key = $jsonField['key'] ?? null;
            $value = $jsonField['value'] ?? null;
            if ($key && $value) {
                if ($key === 'body')
                    $body = $value;
                else
                    $body[$key] = $value;
            }
        }
    }

    /**
     * @inheritDoc
     */
    protected function getDefaultSystemFields(): array
    {
        return [
            $this->formatField('url', 'URL', required: true),
            $this->formatField(
                self::FIELD_SEND_FORMAT,
                value: self::DEFAULT_VALUE_SEND_FORMAT,
                type: CRMFieldType::DROPDOWN,
                payload: ['options' => ['POST', 'JSON']],
                required: true,
            ),
            $this->formatField('success_keyword', 'Success Response Keyword'),
            $this->formatField('error_keyword', 'Error Response Keyword')
        ];
    }

    /**
     * @inheritDoc
     */
    protected function getDefaultAdditionalFields(): array
    {
        return [
            $this->formatField('first_name', value: '[first_name]'),
            $this->formatField('last_name', value: '[last_name]'),
            $this->formatField('full_name', value: '[full_name]'),
            $this->formatField('lead_process_date', value: '[date]'),
            $this->formatField('lead_source', value: '[lead_source]'),
            $this->formatField('lead_industry', value: '[lead_industry]'),
            $this->formatField('account_name', value: '[account_name]'),
            $this->formatField('email', value: '[email]'),
            $this->formatField('phone', value: '[phone]'),
            $this->formatField('address', value: '[address]'),
            $this->formatField('address_1', value: '[address_1]'),
            $this->formatField('address_2', value: '[address_2]'),
            $this->formatField('city', value: '[city]'),
            $this->formatField('state_abbreviation', value: '[state_abbr]'),
            $this->formatField('state_name', value: '[state_name]'),
            $this->formatField('country_abbreviation', value: '[country_abbr]'),
            $this->formatField('country_name', value: '[country]'),
            $this->formatField('full_address', value: '[full_address]'),
            $this->formatField('combined_comments', value: '[combined_comments]'),
        ];
    }
}
