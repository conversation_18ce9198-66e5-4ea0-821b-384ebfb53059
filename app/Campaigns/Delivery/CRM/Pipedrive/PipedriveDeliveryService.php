<?php


namespace App\Campaigns\Delivery\CRM\Pipedrive;

use App\Campaigns\Delivery\CRM\BaseCRMDeliveryService;
use App\Campaigns\Delivery\CRM\CRMDeliveryResponse;
use App\Services\Campaigns\CRMIntegrations\Pipedrive\PipedriveCRMService;
use App\Services\Campaigns\CRMIntegrations\Pipedrive\PipedriveCRMServiceFactory;
use GuzzleHttp\Exception\GuzzleException;
use Pipedrive\versions\v1\Model\AddNoteRequest;
use Pipedrive\versions\v1\Model\DealNonStrict;
use Pipedrive\versions\v1\Model\PersonItem;

class PipedriveDeliveryService extends BaseCRMDeliveryService
{
    /**
     * @var PipedriveCRMService
     */
    protected PipedriveCRMService $pipedriveService;
    protected ?PersonItem $person;
    protected ?array $deal;
    protected ?string $note;

    public function __construct(
        protected string $apiKey
    )
    {
        $this->pipedriveService = PipedriveCRMServiceFactory::make($this->apiKey);
    }

    /**
     * @inheritDoc
     * @throws GuzzleException
     */
    public function deliver(): CRMDeliveryResponse
    {
        $pipedriveResponse = $this->pipedriveService->addDeal($this->deal);
        if ($this->note && $pipedriveResponse[PipedriveCRMService::PIPEDRIVE_RESPONSE_DATA]) {
            /** @var DealNonStrict $newDeal */
            $newDeal = $pipedriveResponse[PipedriveCRMService::PIPEDRIVE_RESPONSE_DATA];

            $this->pipedriveService->addNote(new AddNoteRequest([
                'deal_id'   => $newDeal->getId(),
                'content'   => $this->note,
            ]));
        }

        $status = !!($pipedriveResponse[PipedriveCRMService::PIPEDRIVE_RESPONSE_STATUS] ?? false);

        return new CRMDeliveryResponse($status, ['body' => $pipedriveResponse[PipedriveCRMService::PIPEDRIVE_RESPONSE_DATA] ?? $pipedriveResponse[PipedriveCRMService::PIPEDRIVE_RESPONSE_MESSAGE] ?? null]);
    }

    /**
     * @inheritDoc
     */
    public function debugDelivery(): CRMDeliveryResponse
    {
        return $this->deliver();
    }

    /**
     * @param array $parsedPersonFields
     * @return $this
     * @throws GuzzleException
     */
    public function makePerson(array $parsedPersonFields): PipedriveDeliveryService
    {
        $this->person = $this->pipedriveService->addPerson($parsedPersonFields);

        return $this;
    }

    /**
     * @param string|null $note
     * @return PipedriveDeliveryService
     */
    public function makeNote(?string $note): PipedriveDeliveryService
    {
        $this->note = $note ?? null;

        return $this;
    }

    /**
     * @param array $parsedDealFields
     * @return PipedriveDeliveryService
     */
    public function makeDeal(array $parsedDealFields): PipedriveDeliveryService
    {
        $this->deal = [
            ...$parsedDealFields,
            PipedriveCRMService::FIELD_PERSON_ID => $this->person?->getId() ?? null,
        ];

        return $this;
    }
}

