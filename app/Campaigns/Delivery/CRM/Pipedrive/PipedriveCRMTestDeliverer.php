<?php

namespace App\Campaigns\Delivery\CRM\Pipedrive;

use App\Campaigns\Delivery\CRM\BaseInteractableCRMTestDeliverer;
use App\Campaigns\Delivery\CRM\CRMTestDeliveryResponse;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ConsumerProduct;
use App\Services\Campaigns\CRMIntegrations\Pipedrive\PipedriveCRMService;
use GuzzleHttp\Exception\GuzzleException;

class PipedriveCRMTestDeliverer extends BaseInteractableCRMTestDeliverer
{
    /**
     * @inheritDoc
     */
    #[\Override] protected function getDefaultSystemFields(): array
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    #[\Override] protected function getDefaultAdditionalFields(): array
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    #[\Override] public function deliver(ConsumerProduct $product, CompanyCampaign $campaign, ?array $payload = null): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     * @throws GuzzleException
     */
    #[\Override] public function deliverTestLead(): CRMTestDeliveryResponse
    {
        $realDeliverer = new PipedriveCRMDeliverer($this->crm, $this->crm->payload);

        $campaign = $this->crm->module->campaign;
        $apiKey = $this->getFieldValue($campaign, PipedriveCRMDeliverer::AUTH_TOKEN);

        $deliveryService = new PipedriveDeliveryService($apiKey);
        $fakeProduct = ConsumerProduct::factory()->create();

        $parsedPerson = $this->parseManyFieldValuesForFakeData($realDeliverer->getInteractableFieldGroup($campaign, PipedriveCRMService::FIELD_CATEGORY_PERSON), $campaign);
        $realDeliverer->setMissingRequiredPersonFields($parsedPerson, $fakeProduct, $campaign);
        $parsedDeal = $this->parseManyFieldValuesForFakeData($realDeliverer->getInteractableFieldGroup($campaign, PipedriveCRMService::FIELD_CATEGORY_DEAL), $campaign);
        $realDeliverer->setMissingRequiredDealFields($parsedDeal, $fakeProduct, $campaign);
        $filteredPerson = array_filter($parsedPerson, fn($v) => $v);
        $filteredDeal = array_filter($parsedDeal, fn($v) => $v);
        $response = $deliveryService
            ->makePerson($filteredPerson)
            ->makeDeal($filteredDeal)
            ->makeNote(null)
            ->deliver();

        return new CRMTestDeliveryResponse(
            url: '==> pipedrive php library',
            requestBody: ['parsed_person' => $filteredPerson, 'parsed_deal' => $filteredDeal],
            headers: [],
            success: $response->success,
            responseBody: $response->payload,
        );
    }
}
