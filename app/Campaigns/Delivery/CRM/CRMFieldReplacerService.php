<?php

namespace App\Campaigns\Delivery\CRM;

use App\Enums\Campaigns\CRMFieldReplacerKey;
use App\Enums\ConsumerProcessing\ConsumerProcessingActivityVisibilityScope;
use App\Enums\GlobalConfigurationContactField;
use App\Enums\GlobalConfigurationKey;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\PropertyType;
use App\Enums\Odin\Country;
use App\Enums\Odin\RoofingConfigurableFields;
use App\Enums\Odin\SolarConfigurableFields;
use App\Enums\Odin\StateAbbreviation;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\ConsumerProcessingActivity;
use App\Models\GlobalConfiguration;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductData;
use App\Models\Odin\IndustryConsumerField;
use App\Models\Odin\ProductAssignment;
use Illuminate\Support\Collection;

class CRMFieldReplacerService
{
    protected array  $replacements              = [];
    protected ?array $allowedConfigurableFields = null;
    protected ?array $globalConfigurationFields = null;

    public function __construct()
    {

    }

    /**
     * Handles replacing shortcodes in a CRM field value.
     *
     * @param ConsumerProduct $product
     * @param CompanyCampaign $campaign
     * @param string $value
     * @param string|null $targetEmailAddress
     * @return string
     */
    public function replaceField(ConsumerProduct $product, CompanyCampaign $campaign, string $value, ?string $targetEmailAddress = null): string
    {
        $productAssignment = $this->getProductAssignment($product, $campaign);

        return preg_replace_callback("/\[[0-z-]+?]/", function ($match) use ($product, $campaign, $productAssignment, $targetEmailAddress) {
            $key = preg_replace("/[\[\]]/", "", $match[0]);

            return $this->getReplacement($key, $product, $campaign, $productAssignment, $targetEmailAddress);
        }, $value);
    }

    /**
     * Returns the replacement for a key, and caches.
     *
     * @param string $key
     * @param ConsumerProduct $product
     * @param CompanyCampaign $campaign
     * @param ProductAssignment $productAssignment
     * @param string|null $targetEmailAddress
     * @return string
     */
    protected function getReplacement(string $key, ConsumerProduct $product, CompanyCampaign $campaign, ProductAssignment $productAssignment, ?string $targetEmailAddress = null): string
    {
        if ($this->replacements[$key] ?? false)
            return $this->replacements[$key];

        $replacementValue = $this->getReplacementFromBuiltInVariables($key, $product, $campaign, $productAssignment, $targetEmailAddress);
        if ($replacementValue !== null)
            return $this->replacements[$key] = $replacementValue;

        else if ($value = $this->getReplacementFromProductPayloadVariables($key, $product))
            return $this->replacements[$key] = $value;

        return $key;
    }

    /**
     * Built in variables with custom logic.
     *
     * @param string $key
     * @param ConsumerProduct $product
     * @param CompanyCampaign $campaign
     * @param ProductAssignment $productAssignment
     * @param string|null $targetEmailAddress
     * @return string|null
     */
    protected function getReplacementFromBuiltInVariables(string $key, ConsumerProduct $product, CompanyCampaign $campaign, ProductAssignment $productAssignment, ?string $targetEmailAddress = null): ?string
    {
        $keyEnum = CRMFieldReplacerKey::tryFromWithAliases($key);

        return $keyEnum
            ? match ($keyEnum) {
                CRMFieldReplacerKey::BRAND                      => $this->getBrandName($product),
                CRMFieldReplacerKey::DATE                       => $this->getDeliveryTime($productAssignment),
                CRMFieldReplacerKey::LEAD_SOURCE                => $product->consumerProductTracking?->website?->name,
                CRMFieldReplacerKey::LEAD_INDUSTRY              => $product->industryService->industry->name,
                CRMFieldReplacerKey::LEAD_SERVICE               => $product->serviceProduct->service->name,
                CRMFieldReplacerKey::FIRST_NAME                 => $product->consumer->first_name,
                CRMFieldReplacerKey::LAST_NAME                  => $product->consumer->last_name,
                CRMFieldReplacerKey::FULL_NAME                  => $product->consumer->first_name . ' ' . $product->consumer->last_name,
                CRMFieldReplacerKey::ACCOUNT_NAME               => $product->consumer->first_name . ' ' . $product->consumer->last_name . ' Residence',
                CRMFieldReplacerKey::EMAIL                      => $product->consumer->email,
                CRMFieldReplacerKey::FULL_ADDRESS               => $product->address->getFullAddress(),
                CRMFieldReplacerKey::ADDRESS                    => $product->address->getFullStreetAddress(),
                CRMFieldReplacerKey::ADDRESS_1                  => $product->address->address_1,
                CRMFieldReplacerKey::ADDRESS_2                  => $product->address->address_2 ?? '',
                CRMFieldReplacerKey::CITY                       => strlen(trim($product->address->city)) === 0 ? "NO_CITY" : $product->address->city,
                CRMFieldReplacerKey::STATE_ABBR                 => $product->address->state,
                CRMFieldReplacerKey::STATE                      => $this->getFullStateName($product->address->state),
                CRMFieldReplacerKey::ZIP_CODE                   => $product->address->zip_code,
                CRMFieldReplacerKey::COUNTRY_ABBR               => $product->address->country,
                CRMFieldReplacerKey::COUNTRY                    => Country::tryFrom(strtoupper($product->address->country))?->getFullName() ?? $product->address->country,
                CRMFieldReplacerKey::COMBINED_COMMENTS          => $this->getCombinedComments($product),
                CRMFieldReplacerKey::ELECTRIC_SPEND             => $product->consumerProductData->payload[SolarConfigurableFields::ELECTRIC_COST->value] ?? 0,
                CRMFieldReplacerKey::UTILITY_NAME               => $product->consumerProductData->payload[SolarConfigurableFields::UTILITY_NAME->value] ?? "Unknown",
                CRMFieldReplacerKey::ROOF_ESTIMATE_LOW          => $product->consumerProductData->payload[RoofingConfigurableFields::ROOF_ESTIMATE_LOW->value] ?? "NA",
                CRMFieldReplacerKey::ROOF_ESTIMATE_MEDIAN       => $product->consumerProductData->payload[RoofingConfigurableFields::ROOF_ESTIMATE_MEDIAN->value] ?? "NA",
                CRMFieldReplacerKey::ROOF_ESTIMATE_HIGH         => $product->consumerProductData->payload[RoofingConfigurableFields::ROOF_ESTIMATE_HIGH->value] ?? "NA",
                CRMFieldReplacerKey::LEAD_ID                    => $product->id,
                CRMFieldReplacerKey::ASSIGNMENT_ID              => $productAssignment->id,
                CRMFieldReplacerKey::UNIVERSAL_LEAD_ID          => $product->consumerProductTcpaRecord?->tcpa_id,
                CRMFieldReplacerKey::LEAD_PRICE                 => $productAssignment->cost,
                CRMFieldReplacerKey::NUMBER_OF_QUOTES_REQUESTED => $product->contact_requests,
                CRMFieldReplacerKey::PHONE                      => $this->getConsumerPhone($product->consumer, $productAssignment),
                CRMFieldReplacerKey::SYSTEM_SIZE                => $product->consumerProductData->payload[SolarConfigurableFields::SYSTEM_SIZE->value] ?? "Unknown",
                CRMFieldReplacerKey::ORIGIN_URL                 => $product->consumerProductTracking?->url_start,
                CRMFieldReplacerKey::CAMPAIGN_NAME              => $campaign->name,
                CRMFieldReplacerKey::LEAD_SALE_TYPE             => $productAssignment->saleType?->name,
                CRMFieldReplacerKey::LEAD_SALE_COUNT            => $productAssignment->saleType?->sale_limit,
                CRMFieldReplacerKey::PROPERTY_TYPE              => $product->propertyType?->name ?? PropertyType::RESIDENTIAL->value,
                CRMFieldReplacerKey::LEAD_TYPE                  => $productAssignment->qualityTier?->name,
                CRMFieldReplacerKey::COMPANY_NAME               => $campaign->company->name,
                CRMFieldReplacerKey::COMPANY_USER_NAME          => $campaign->company->users()->where(CompanyUser::FIELD_EMAIL, $targetEmailAddress)->first()?->completeName() ?? null,
                CRMFieldReplacerKey::SR_ADDRESS_1               => $this->getGlobalConfigurationField(GlobalConfigurationKey::SOLARREVIEWS, GlobalConfigurationContactField::ADDRESS_1->value),
                CRMFieldReplacerKey::SR_ADDRESS_2               => $this->getGlobalConfigurationField(GlobalConfigurationKey::SOLARREVIEWS, GlobalConfigurationContactField::ADDRESS_2->value),
                CRMFieldReplacerKey::SR_SUPPORT_EMAIL           => $this->getGlobalConfigurationField(GlobalConfigurationKey::SOLARREVIEWS, GlobalConfigurationContactField::EMAIL->value),
                CRMFieldReplacerKey::SR_WEBSITE                 => $this->getGlobalConfigurationField(GlobalConfigurationKey::SOLARREVIEWS, GlobalConfigurationContactField::WEBSITE->value),
                CRMFieldReplacerKey::SR_PHONE                   => $this->getGlobalConfigurationField(GlobalConfigurationKey::SOLARREVIEWS, GlobalConfigurationContactField::PHONE->value),
                CRMFieldReplacerKey::FIXR_ADDRESS_1             => $this->getGlobalConfigurationField(GlobalConfigurationKey::FIXR, GlobalConfigurationContactField::ADDRESS_1->value),
                CRMFieldReplacerKey::FIXR_ADDRESS_2             => $this->getGlobalConfigurationField(GlobalConfigurationKey::FIXR, GlobalConfigurationContactField::ADDRESS_2->value),
                CRMFieldReplacerKey::FIXR_SUPPORT_EMAIL         => $this->getGlobalConfigurationField(GlobalConfigurationKey::FIXR, GlobalConfigurationContactField::EMAIL->value),
                CRMFieldReplacerKey::FIXR_WEBSITE               => $this->getGlobalConfigurationField(GlobalConfigurationKey::FIXR, GlobalConfigurationContactField::WEBSITE->value),
                CRMFieldReplacerKey::FIXR_PHONE                 => $this->getGlobalConfigurationField(GlobalConfigurationKey::FIXR, GlobalConfigurationContactField::PHONE->value),
                CRMFieldReplacerKey::PRODUCT                    => $product->serviceProduct->product->name,
                CRMFieldReplacerKey::SHADE                      => $product->consumerProductData->payload[SolarConfigurableFields::ROOF_SHADE->value] ?? "Unknown",
                CRMFieldReplacerKey::BEST_TIME_TO_CALL          => $this->getBestContactTimes($product->consumerProductData),
                CRMFieldReplacerKey::APPOINTMENT_DATE           => $product->appointment?->appointment_date ?? "",
                CRMFieldReplacerKey::APPOINTMENT_TIME           => $product->appointment?->appointment_time ?? "",
                CRMFieldReplacerKey::APPOINTMENT_TYPE           => $product->appointment?->appointment_type?->value ?? "",
                CRMFieldReplacerKey::TRUSTED_FORM_URL           => $product->consumerProductData->payload[GlobalConfigurableFields::TRUSTED_FORM_URL->value] ?? '',
                CRMFieldReplacerKey::WATCHDOG_VIDEO_LINK        => $product->consumer?->watchdogVideos?->last()?->getLink(),
                CRMFieldReplacerKey::OPT_IN_NAME                => $productAssignment->optInCompany?->companyOptInName?->name ?? $campaign->company->name,
                CRMFieldReplacerKey::PUBLIC_COMMENTS            => $this->getPublicComments($product)->join(", ") ?: null,
                CRMFieldReplacerKey::LOW_SYSTEM_COST            => $product->consumerProductData->payload[CRMFieldReplacerKey::LOW_SYSTEM_COST->value] ?? "",
                CRMFieldReplacerKey::HIGH_SYSTEM_COST           => $product->consumerProductData->payload[CRMFieldReplacerKey::HIGH_SYSTEM_COST->value] ?? "",
                CRMFieldReplacerKey::LOW_TAX_CREDIT             => $product->consumerProductData->payload[CRMFieldReplacerKey::LOW_TAX_CREDIT->value] ?? "",
                CRMFieldReplacerKey::HIGH_TAX_CREDIT            => $product->consumerProductData->payload[CRMFieldReplacerKey::HIGH_TAX_CREDIT->value] ?? "",
                CRMFieldReplacerKey::LOW_COST_AFTER_INCENTIVES  => $product->consumerProductData->payload[CRMFieldReplacerKey::LOW_COST_AFTER_INCENTIVES->value] ?? "",
                CRMFieldReplacerKey::HIGH_COST_AFTER_INCENTIVES => $product->consumerProductData->payload[CRMFieldReplacerKey::HIGH_COST_AFTER_INCENTIVES->value] ?? "",
                default                                         => $this->handleMissingKey($keyEnum),
            }
            : null;
    }

    /**
     * @param Consumer $consumer
     * @param ProductAssignment $productAssignment
     *
     * @return string|null
     */
    protected function getConsumerPhone(Consumer $consumer, ProductAssignment $productAssignment): ?string
    {
        if ($productAssignment->proxy_phone_active && $productAssignment->proxy_phone) {
            return $productAssignment->proxy_phone;
        }

        return $consumer->phone;
    }

    /**
     * @param ConsumerProduct $product
     * @param CompanyCampaign $campaign
     *
     * @return ProductAssignment
     */
    protected function getProductAssignment(ConsumerProduct $product, CompanyCampaign $campaign): ProductAssignment
    {
        /** @var ProductAssignment */
        return $product->productAssignment()->where(ProductAssignment::FIELD_COMPANY_ID, $campaign->company_id)->firstOrFail();
    }

    /**
     * Uses the product payload.
     *
     * @param string $key
     * @param ConsumerProduct $product
     * @return string|null
     */
    protected function getReplacementFromProductPayloadVariables(string $key, ConsumerProduct $product): ?string
    {
        if ($this->fieldIsHidden($key, $product))
            return null;

        return $product->consumerProductData->payload[$key] ?? null;
    }

    /**
     * @param string $key
     * @param ConsumerProduct $product
     *
     * @return bool
     */
    protected function fieldIsHidden(string $key, ConsumerProduct $product): bool
    {
        return !array_key_exists($key, $this->getAllowedConfigurableFields($product));
    }

    /**
     * @param ConsumerProduct $product
     *
     * @return array
     */
    protected function getAllowedConfigurableFields(ConsumerProduct $product): array
    {
        if ($this->allowedConfigurableFields === null)
            $this->allowedConfigurableFields = $product->industryService->industry->consumerFields()
                ->where(IndustryConsumerField::FIELD_SEND_TO_COMPANY, true)
                ->get()
                ->pluck(IndustryConsumerField::FIELD_KEY)
                ->flip()
                ->toArray();

        return $this->allowedConfigurableFields;
    }

    /**
     * @param string $stateAbbr
     *
     * @return string
     */
    protected function getFullStateName(string $stateAbbr): string
    {
        return array_flip(StateAbbreviation::getAsKeyValueSelectArray())[strtoupper($stateAbbr)] ?? $stateAbbr;
    }

    /**
     * @param ConsumerProduct $product
     *
     * @return string
     */
    protected function getCombinedComments(ConsumerProduct $product): string
    {
        return $product->industryService->industry->consumerFields()
            ->where(IndustryConsumerField::FIELD_SEND_TO_COMPANY, true)
            ->get()
            ->map(function (IndustryConsumerField $field) use ($product) {
                $value = $product->consumerProductData->payload[$field->key] ?? null;

                return $value ? "{$field->name}: $value" : null;
            })
            ->filter()
            ->push(...$this->getPublicComments($product))
            ->join(', ');
    }

    public function getPublicComments(ConsumerProduct $product, bool $toString = false): Collection|string
    {
        return $product->processingActivities()
            ->where(ConsumerProcessingActivity::FIELD_SCOPE, ConsumerProcessingActivityVisibilityScope::SEND_TO_COMPANY)
            ->whereNotNull(ConsumerProcessingActivity::FIELD_COMMENT)
            ->pluck(ConsumerProcessingActivity::FIELD_COMMENT);
    }

    /**
     * @param GlobalConfigurationKey $key
     * @param string $field
     * @return string|null
     */
    protected function getGlobalConfigurationField(GlobalConfigurationKey $key, string $field): ?string
    {
        if (!$this->globalConfigurationFields) {
            $configKeys                      = [GlobalConfigurationKey::SOLARREVIEWS->value, GlobalConfigurationKey::ROOFING_CALCULATOR->value, GlobalConfigurationKey::FIXR->value];
            $this->globalConfigurationFields = GlobalConfiguration::query()
                ->whereIn(GlobalConfiguration::FIELD_CONFIGURATION_KEY, $configKeys)
                ->select([GlobalConfiguration::FIELD_CONFIGURATION_KEY, GlobalConfiguration::FIELD_CONFIGURATION_PAYLOAD])
                ->get()
                ->reduce(fn(array $output, GlobalConfiguration $configuration) => [...$output, $configuration->configuration_key => $configuration->configuration_payload?->toArray()['data'] ?? []]
                    , []);
        }

        return array_key_exists($key->value, $this->globalConfigurationFields)
            ? $this->globalConfigurationFields[$key->value][$field] ?? null
            : null;
    }

    /**
     * @param ConsumerProduct $product
     * @return string
     */
    protected function getBrandName(ConsumerProduct $product): string
    {
        return match ($product->industryService->industry->name) {
            IndustryEnum::SOLAR->value   => $this->getGlobalConfigurationField(GlobalConfigurationKey::SOLARREVIEWS, GlobalConfigurationContactField::NAME->value) ?? 'SolarReviews',
            IndustryEnum::ROOFING->value => $this->getGlobalConfigurationField(GlobalConfigurationKey::ROOFING_CALCULATOR, GlobalConfigurationContactField::NAME->value) ?? 'Roofing Calculator',
            default                      => $this->getGlobalConfigurationField(GlobalConfigurationKey::FIXR, GlobalConfigurationContactField::NAME->value) ?? 'Fixr',
        };
    }

    /**
     * @param ProductAssignment $productAssignment
     * @return string
     */
    protected function getDeliveryTime(ProductAssignment $productAssignment): string
    {
        $delivery = $productAssignment->delivered_at;
        return $delivery->diffInMinutes(now(), true) < 5
            ? $delivery->format('m/d/Y')
            : now()->format('m/d/Y');
    }

    /**
     * @param ConsumerProductData $consumerProductData
     * @return string
     */
    protected function getBestContactTimes(ConsumerProductData $consumerProductData): string
    {
        $payload      = $consumerProductData->payload;
        $contactTimes = collect([GlobalConfigurableFields::BEST_TIME_TO_CALL->value, GlobalConfigurableFields::BEST_TIME_TO_CALL_OTHER->value])
            ->map(fn($key) => $payload[$key] ?: null)
            ->filter();

        return $contactTimes->count()
            ? $contactTimes->join(', ')
            : 'Not provided';
    }

    /**
     * @param CRMFieldReplacerKey $key
     * @return null
     */
    private function handleMissingKey(CRMFieldReplacerKey $key): null
    {
        logger()->warning(self::class . ': failed to implement replacer for CRMFieldReplacerKey ' . $key->name);

        return null;
    }
}
