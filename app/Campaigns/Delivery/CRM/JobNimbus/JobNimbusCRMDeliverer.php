<?php

namespace App\Campaigns\Delivery\CRM\JobNimbus;

use App\Campaigns\Delivery\CRM\BaseCRMDeliverer;
use App\Campaigns\Delivery\CRM\Enums\CRMFieldType;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ConsumerProduct;

class JobNimbusCRMDeliverer extends BaseCRMDeliverer
{
    /**
     * @inheritDoc
     */
    protected function getDefaultSystemFields(): array
    {
        return [
            $this->formatField(JobNimbusDeliveryService::API_KEY, "API Key", required: true),
            $this->formatField(
                JobNimbusDeliveryService::ENDPOINT_KEY,
                value: JobNimbusDeliveryService::DEFAULT_ENDPOINT,
                type: CRMFieldType::DROPDOWN,
                payload: ['options' => [
                    JobNimbusDeliveryService::CONTACTS_API,
                    JobNimbusDeliveryService::JOBS_API,
                ]],
                required: true,
            ),
        ];
    }

    /**
     * @inheritDoc
     */
    protected function getDefaultAdditionalFields(): array
    {
        return [
            $this->formatField('first_name', value: '[first_name]'),
            $this->formatField('last_name', value: '[last_name]'),
            $this->formatField('phone_number', value: '[phone]'),
            $this->formatField('email', value: '[email]'),
            $this->formatField('address_line_1', value: '[address_1]'),
            $this->formatField('address_line_2', value: '[address_2]'),
            $this->formatField('state', value: '[state]'),
            $this->formatField('city', value: '[city]'),
            $this->formatField('zip_code', value: '[zip_code]'),
            $this->formatField('record_type_name', value: 'Customer'),
        ];
    }

    /**
     * @inheritDoc
     */
    public function deliver(ConsumerProduct $product, CompanyCampaign $campaign, ?array $payload = null): bool
    {
        /** @var JobNimbusDeliveryService $deliveryService */
        $deliveryService = app(JobNimbusDeliveryService::class);

        return $this->getDeliveryProxy(
            $deliveryService->setApiKey($this->getFieldValue($campaign, JobNimbusDeliveryService::API_KEY))
                ->setEndpoint($this->getFieldValue($campaign, JobNimbusDeliveryService::ENDPOINT_KEY))
                ->setBody($this->parseManyFieldValues($this->getManyFieldGroups($campaign, [self::ADDITIONAL_FIELDS_KEY, self::CUSTOM_FIELDS_KEY]), $product, $campaign))
                ->setHeaders($this->parseManyFieldValues($this->getFieldGroup($campaign, self::HEADERS_KEY), $product, $campaign)),
            $product,
            $campaign
        )->prepareDelivery($campaign)->success;
    }
}
