<?php

namespace App\Campaigns\Delivery\CRM\JobNimbus;

use App\Campaigns\Delivery\CRM\BaseCRMTestDeliverer;
use App\Campaigns\Delivery\CRM\CRMTestDeliveryResponse;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ConsumerProduct;

class JobNimbusCRMTestDeliverer extends BaseCRMTestDeliverer
{
    /**
     * @inheritDoc
     */
    #[\Override] protected function getDefaultSystemFields(): array
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    #[\Override] protected function getDefaultAdditionalFields(): array
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    #[\Override] public function deliver(ConsumerProduct $product, CompanyCampaign $campaign, ?array $payload = null): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    #[\Override] public function deliverTestLead(): CRMTestDeliveryResponse
    {
        /** @var JobNimbusDeliveryService $deliveryService */
        $deliveryService = app(JobNimbusDeliveryService::class);

        $campaign = $this->crm->module->campaign;
        $requestBody = $this->parseManyFieldValuesForFakeData($this->getManyFieldGroups($campaign, [self::ADDITIONAL_FIELDS_KEY, self::CUSTOM_FIELDS_KEY]), $campaign);
        $headers = $this->parseManyFieldValuesForFakeData($this->getFieldGroup($campaign, self::HEADERS_KEY), $campaign);

        $delivery = $deliveryService->setApiKey($this->getFieldValue($campaign, JobNimbusDeliveryService::API_KEY))
            ->setBody($requestBody)
            ->setHeaders($headers)
            ->setEndpoint($this->getFieldValue($campaign, JobNimbusDeliveryService::ENDPOINT_KEY));

        $url = $delivery->getUrl();

        $response = $delivery->deliver();

        return new CRMTestDeliveryResponse(
            url: $url,
            requestBody: $requestBody,
            headers: $headers,
            success: $response->success,
            responseBody: $response->payload
        );
    }
}
