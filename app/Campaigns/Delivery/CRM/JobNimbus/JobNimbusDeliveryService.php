<?php

namespace App\Campaigns\Delivery\CRM\JobNimbus;

use App\Campaigns\Delivery\CRM\BaseCRMDeliveryService;
use App\Campaigns\Delivery\CRM\CRMDeliveryResponse;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class JobNimbusDeliveryService extends BaseCRMDeliveryService
{
    const string BASE_URL         = 'https://app.jobnimbus.com/api1';
    const string ENDPOINT_KEY     = 'api_endpoint';
    const string API_KEY          = 'api_key';
    const string CONTACTS_API     = '/contacts';
    const string JOBS_API         = '/jobs';
    const string DEFAULT_ENDPOINT = self::CONTACTS_API;

    const string TEST_KEY = 'job-nimbus';

    protected string $apiKey;
    protected array $body = [];
    protected array $headers = [];
    protected string $url = self::BASE_URL . self::DEFAULT_ENDPOINT;
    protected array $cloneKeys = [
        'phone_number'   => 'mobile_phone',
        'address_line_1' => 'address_line1',
        'address_line_2' => 'address_line2',
        'state'          => 'state_text',
        'zip_code'       => 'zip',
    ];

    /**
     * @inheritDoc
     */
    public function deliver(): CRMDeliveryResponse
    {
        if (!$this->apiKey)
            return new CRMDeliveryResponse(false, ['error' => 'Bad JobNimbus CRM configuration, no API key supplied.']);

        $response = Http::withHeaders($this->headers)
            ->withToken($this->apiKey)
            ->withBody(json_encode($this->body), 'application/json')
            ->post($this->url);

        return new CRMDeliveryResponse($response->successful(), ['body' => $response->body()]);
    }

    /**
     * @inheritDoc
     */
    public function debugDelivery(): CRMDeliveryResponse
    {
        $this->url = $this->getDebugUrl(self::TEST_KEY);
        $this->setTestHeaders($this->headers);
        $this->body['api_key'] = Str::mask($this->apiKey, '*', 5);
        $this->apiKey = config('services.crm_delivery.test_secret');

        return $this->deliver();
    }

    /**
     * @param string|null $apiKey
     * @return JobNimbusDeliveryService
     */
    public function setApiKey(?string $apiKey): JobNimbusDeliveryService
    {
        $this->apiKey = $apiKey ?? "";

        return $this;
    }

    /**
     * @param array $body
     * @return JobNimbusDeliveryService
     */
    public function setBody(array $body): JobNimbusDeliveryService
    {
        $this->body = $body;
        $this->cloneKeys();

        return $this;
    }

    /**
     * @param array $headers
     * @return JobNimbusDeliveryService
     */
    public function setHeaders(array $headers): JobNimbusDeliveryService
    {
        $this->headers = $headers;

        return $this;
    }

    /**
     * @param string|null $endpoint
     * @return $this
     */
    public function setEndpoint(?string $endpoint): JobNimbusDeliveryService
    {
        $apiEndpoint = match($endpoint) {
            self::JOBS_API     => self::JOBS_API,
            self::CONTACTS_API => self::CONTACTS_API,
            default            => self::DEFAULT_ENDPOINT,
        };
        $this->url = self::BASE_URL . $apiEndpoint;

        return $this;
    }

    /**
     * @return string
     */
    public function getUrl(): string
    {
        return $this->url;
    }

    /**
     * @return void
     */
    protected function cloneKeys(): void
    {
        foreach ($this->cloneKeys as $defaultKey => $cloneKey) {
            if (array_key_exists($defaultKey, $this->body) && !array_key_exists($cloneKey, $this->body)) {
                $this->body[$cloneKey] = $this->body[$defaultKey];
            }
        }
    }
}
