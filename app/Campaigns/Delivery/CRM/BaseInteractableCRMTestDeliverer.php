<?php

namespace App\Campaigns\Delivery\CRM;

use App\Models\Campaigns\CompanyCampaign;

abstract class BaseInteractableCRMTestDeliverer extends BaseCRMTestDeliverer
{
    /**
     * @param CompanyCampaign $campaign
     * @param string $groupKey
     * @return array
     */
    protected function getInteractableFieldGroup(CompanyCampaign $campaign, string $groupKey): array
    {
        return $this->getFields($campaign)[BaseInteractableCRMDeliverer::INTERACTABLE_FIELDS_KEY][$groupKey] ?? [];
    }
}
