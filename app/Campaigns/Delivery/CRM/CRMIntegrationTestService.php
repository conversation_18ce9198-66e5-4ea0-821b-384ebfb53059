<?php

namespace App\Campaigns\Delivery\CRM;

use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleCRM;

class CRMIntegrationTestService
{
    /**
     * @param CompanyCampaignDeliveryModuleCRM $crm
     * @param array $testData
     *
     * @return array
     */
    public function sendTestLead(CompanyCampaignDeliveryModuleCRM $crm, array $testData = []): array
    {
        return $crm->crm_type->getCRMTestDeliverer($crm, $crm->payload)->setTestData($testData)->deliverTestLead()->toArray();
    }
}
