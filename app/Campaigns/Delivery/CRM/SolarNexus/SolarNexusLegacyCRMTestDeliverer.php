<?php

namespace App\Campaigns\Delivery\CRM\SolarNexus;

use App\Campaigns\Delivery\CRM\BaseCRMTestDeliverer;
use App\Campaigns\Delivery\CRM\CRMTestDeliveryResponse;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ConsumerProduct;
use Override;

class SolarNexusLegacyCRMTestDeliverer extends BaseCRMTestDeliverer
{
    /**
     * @inheritDoc
     */
    #[Override] protected function getDefaultSystemFields(): array
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    #[Override] protected function getDefaultAdditionalFields(): array
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    #[Override] public function deliver(ConsumerProduct $product, CompanyCampaign $campaign, ?array $payload = null): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    #[Override] public function deliverTestLead(): CRMTestDeliveryResponse
    {
        /** @var SolarNexusLegacyDeliveryService $deliveryService */
        $deliveryService = app(SolarNexusLegacyDeliveryService::class);

        $campaign = $this->crm->module->campaign;
        $additionalFields = $this->parseManyFieldValuesForFakeData($this->getManyFieldGroups($campaign, [self::ADDITIONAL_FIELDS_KEY]), $campaign);
        $customFields = $this->parseManyFieldValuesForFakeData($this->getManyFieldGroups($campaign, [self::CUSTOM_FIELDS_KEY]), $campaign);
        $headers = $this->parseManyFieldValuesForFakeData($this->getFieldGroup($campaign, self::HEADERS_KEY), $campaign);

        $deliveryService->setApiKey($this->getFieldValue($campaign, 'api_key'))
            ->setBody($additionalFields, $customFields)
            ->setHeaders($headers);

        $response = $deliveryService->deliver();

        return new CRMTestDeliveryResponse(
            url: SolarNexusLegacyDeliveryService::BASE_URL . SolarNexusLegacyDeliveryService::LEADS_API,
            requestBody: $deliveryService->getBody(),
            headers: $deliveryService->getHeaders(),
            success: $response->success,
            responseBody: $response->payload
        );
    }
}
