<?php

namespace App\Campaigns\Delivery\CRM\SolarNexus;

use App\Campaigns\Delivery\CRM\BaseCRMDeliverer;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ConsumerProduct;

class SolarNexusCRMDeliverer extends BaseCRMDeliverer
{
    /**
     * @inheritDoc
     */
    protected function getDefaultSystemFields(): array
    {
        return [
            $this->formatField("api_key", "API Key", required: true),
        ];
    }

    /**
     * @inheritDoc
     */
    protected function getDefaultAdditionalFields(): array
    {
        return [
            $this->formatField(SolarNexusDeliveryService::FIELD_JOB_PROCESS_ID, required: true),
            $this->formatField(SolarNexusDeliveryService::FIELD_LEAD_SOURCE_ID, required: true),
            $this->formatField(SolarNexusDeliveryService::FIELD_FIRST_NAME, value: '[first_name]'),
            $this->formatField(SolarNexusDeliveryService::FIELD_LAST_NAME, value: '[last_name]'),
            $this->formatField(SolarNexusDeliveryService::FIELD_EMAIL, value: '[email]'),
            $this->formatField(SolarNexusDeliveryService::FIELD_PHONE, value: '[phone]'),
            $this->formatField(SolarNexusDeliveryService::FIELD_ADDRESS_1, value: '[address]'),
            $this->formatField(SolarNexusDeliveryService::FIELD_ZIP_CODE, value: '[zip_code]'),
            $this->formatField(SolarNexusDeliveryService::FIELD_CITY, value: '[city]'),
            $this->formatField(SolarNexusDeliveryService::FIELD_STATE_ABBR, value: '[state_abbr]'),
            $this->formatField(SolarNexusDeliveryService::FIELD_COUNTRY_ABBR, value: '[country_abbr]'),
            $this->formatField(SolarNexusDeliveryService::FIELD_LEAD_ID, value: '[lead_id]', required: true),
            $this->formatField(SolarNexusDeliveryService::FIELD_LEAD_SOURCE, value: '[lead_source]'),
            $this->formatField(SolarNexusDeliveryService::FIELD_PROJECT_NAME, 'Project Name', value: 'residential'),
            $this->formatField(SolarNexusDeliveryService::FIELD_PROJECT_DESCRIPTION, 'Project Description', value: 'residential'),
            $this->formatField(SolarNexusDeliveryService::FIELD_COMBINED_COMMENTS, value: '[combined_comments]'),
            $this->formatField(SolarNexusDeliveryService::FIELD_UTILITY_NAME, value: '[utility_name]'),
            $this->formatField(SolarNexusDeliveryService::FIELD_ELECTRIC_SPEND, value: '[electric_spend]'),
            $this->formatField(SolarNexusDeliveryService::FIELD_SALES_OWNER_TYPE, value: 'user'),
            $this->formatField(SolarNexusDeliveryService::FIELD_SALES_OWNER_ID),
        ];
    }

    /**
     * @inheritDoc
     */
    public function deliver(ConsumerProduct $product, CompanyCampaign $campaign, ?array $payload = null): bool
    {
        /** @var SolarNexusDeliveryService $deliveryService */
        $deliveryService = app(SolarNexusDeliveryService::class);

        $solarNexusFields = $this->parseManyFieldValues($this->getManyFieldGroups($campaign, [self::ADDITIONAL_FIELDS_KEY]), $product, $campaign);
        $customFields = $this->parseManyFieldValues($this->getManyFieldGroups($campaign, [self::CUSTOM_FIELDS_KEY]), $product, $campaign);

        return $this->getDeliveryProxy(
            $deliveryService->setApiKey($this->getFieldValue($campaign, SolarNexusDeliveryService::FIELD_API_KEY))
                ->setBody($solarNexusFields, $customFields)
                ->setHeaders($this->parseManyFieldValues($this->getFieldGroup($campaign, self::HEADERS_KEY), $product, $campaign)),
            $product,
            $campaign
        )->prepareDelivery($campaign)->success;
    }
}
