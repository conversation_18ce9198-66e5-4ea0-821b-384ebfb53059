<?php

namespace App\Campaigns\Delivery\CRM\SolarNexus;

use App\Campaigns\Delivery\CRM\BaseCRMDeliveryService;
use App\Campaigns\Delivery\CRM\CRMDeliveryResponse;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use stdClass;


class SolarNexusDeliveryService extends BaseCRMDeliveryService
{
    const string BASE_URL      = 'https://app.solarnexus.com/api3';
    const string LEADS_API     = '/jobs.json';
    const string FIELD_API_KEY = 'api_key';
    const string TEST_KEY      = 'solar-nexus-json';

    const string FIELD_JOB_PROCESS_ID      = 'job_process_id';
    const string FIELD_LEAD_SOURCE_ID      = 'lead_source_id';
    const string FIELD_FIRST_NAME          = 'first_name';
    const string FIELD_LAST_NAME           = 'last_name';
    const string FIELD_EMAIL               = 'email';
    const string FIELD_PHONE               = 'phone';
    const string FIELD_ADDRESS_1           = 'address_1';
    const string FIELD_CITY                = 'city';
    const string FIELD_ZIP_CODE            = 'zip_code';
    const string FIELD_STATE_ABBR          = 'state_abbr';
    const string FIELD_COUNTRY_ABBR        = 'country_abbr';
    const string FIELD_LEAD_ID             = 'lead_id';
    const string FIELD_LEAD_SOURCE         = 'lead_source';
    const string FIELD_COMBINED_COMMENTS   = 'combined_comments';
    const string FIELD_UTILITY_NAME        = 'utility_name';
    const string FIELD_ELECTRIC_SPEND      = 'electric_spend';
    const string FIELD_PROJECT_NAME        = 'name';
    const string FIELD_PROJECT_DESCRIPTION = 'description';
    const string FIELD_SALES_OWNER_TYPE    = 'sales_owner_type';
    const string FIELD_SALES_OWNER_ID      = 'sales_owner_id';

    protected string $apiKey;
    protected array $body = [];
    protected array $headers = [];
    protected ?string $url = null;

    /**
     * @inheritDoc
     * @throws ConnectionException
     */
    public function deliver(): CRMDeliveryResponse
    {
        if (!$this->apiKey)
            return new CRMDeliveryResponse(false, ['error' => 'Bad SolarNexus CRM configuration, no API key supplied.']);
        if (!$this->url)
            $this->url = self::BASE_URL . self::LEADS_API;

        $response = Http::withHeaders($this->headers)
            ->withToken($this->apiKey)
            ->withBody(json_encode($this->body), 'application/json')
            ->post($this->url);

        return new CRMDeliveryResponse($response->successful(), ['body' => $response->body()]);
    }

    /**
     * @inheritDoc
     */
    public function debugDelivery(): CRMDeliveryResponse
    {
        $this->url = $this->getDebugUrl(self::TEST_KEY);
        $this->setTestHeaders($this->headers);
        $this->body['api_key'] = Str::mask($this->apiKey, '*', 5);
        $this->apiKey = config('services.crm_delivery.test_secret');

        return $this->deliver();
    }

    /**
     * @param array|null $headers
     * @return $this
     */
    public function setHeaders(?array $headers): self
    {
        $this->headers = $headers ?: $this->headers;

        return $this;
    }

    /**
     * @param string|null $apiKey
     * @return $this
     */
    public function setApiKey(?string $apiKey): self
    {
        $this->apiKey = $apiKey ?? "";

        return $this;
    }

    /**
     * @param array $additionalFields
     * @param array $customFields
     * @return $this
     */
    public function setBody(array $additionalFields, array $customFields): self
    {
        $this->body = $this->transformBodyData($additionalFields, $customFields);

        return $this;
    }

    /**
     * @return array
     */
    public function getBody(): array
    {
        return [...$this->body];
    }

    /**
     * @return array
     */
    public function getHeaders(): array
    {
        return [...$this->headers];
    }

    /**
     * @param array $additionalFields
     * @param array $customFields
     * @return array|array[]
     */
    private function transformBodyData(array $additionalFields, array $customFields): array
    {
        $customFields = count($customFields)
            ? $customFields
            : new stdClass();

        $project = [
            'job_process_id'            => (int) ($additionalFields[self::FIELD_JOB_PROCESS_ID] ?? 0), // required - Comes from client
            'lead_source_id'            => (int) ($additionalFields[self::FIELD_LEAD_SOURCE_ID] ?? 0), // required - Comes from client
            'application_reference_ids' => [
                [
                    'application_reference_id' => [
                        'id'     => $additionalFields[self::FIELD_LEAD_ID], // lead id
                        'source' => $additionalFields[self::FIELD_LEAD_SOURCE] ?? "",
                    ]
                ]
            ],
            'participants'              => [
                [
                    'participant'     => [
                        'type'       => 'individual',
                        'tax_entity' => 'individual',
                        'roles'      => [
                            'Customer'
                        ],
                        'contacts'        => [
                            [
                                'contact' => [
                                    'first_name'    => $additionalFields[self::FIELD_FIRST_NAME] ?? "",
                                    'last_name'     => $additionalFields[self::FIELD_LAST_NAME] ?? "",
                                    'primary_email' => $additionalFields[self::FIELD_EMAIL] ?? "",
                                    'phone_numbers' => [
                                        [
                                            'phone_number' => [
                                                'number' => $additionalFields[self::FIELD_PHONE] ?? "",
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        'mailing_address' => [
                            'city'    => $additionalFields[self::FIELD_CITY] ?? "",
                            'street1' => $additionalFields[self::FIELD_ADDRESS_1] ?? "",
                            'zip'     => $additionalFields[self::FIELD_ZIP_CODE] ?? "",
                            'state'   => $additionalFields[self::FIELD_STATE_ABBR] ?? "",
                            'country' => $additionalFields[self::FIELD_COUNTRY_ABBR ?? ""]
                        ],
                    ],
                ]
            ],
            'site'            => [
                'sector'   => 'residential',
                'location' => [
                    'city'    => $additionalFields[self::FIELD_CITY] ?? "",
                    'street1' => $additionalFields[self::FIELD_ADDRESS_1] ?? "",
                    'zip'     => $additionalFields[self::FIELD_ZIP_CODE] ?? "",
                    'state'   => $additionalFields[self::FIELD_STATE_ABBR] ?? "",
                    'country' => $additionalFields[self::FIELD_COUNTRY_ABBR] ?? "",
                ],
                'notes'    => $additionalFields[self::FIELD_COMBINED_COMMENTS] ?? "",
            ],
            'attributes' => $customFields,
        ];

        foreach([self::FIELD_PROJECT_NAME, self::FIELD_PROJECT_DESCRIPTION] as $key) {
            if ($additionalFields[$key] ?? false)
                $project[$key] = $additionalFields[$key];
        }

        if (Arr::has($additionalFields, [self::FIELD_SALES_OWNER_ID, self::FIELD_SALES_OWNER_TYPE]) && intval($additionalFields[self::FIELD_SALES_OWNER_ID]) > 0) {
            $project['sales_owner'] =
                [
                    'id'   => $additionalFields[self::FIELD_SALES_OWNER_ID],
                    'type' => $additionalFields[self::FIELD_SALES_OWNER_TYPE]
                ];
        }

        if (Arr::has($additionalFields, self::FIELD_UTILITY_NAME)) {
            $project['utility_services'] =
                [
                    [
                        'utility_service' => [
                            'energy'       => 'electricity',
                            'utility_name' => $additionalFields[self::FIELD_UTILITY_NAME],
                            'energy_costs' => [
                                'monthly_avg' => (int) ($additionalFields[self::FIELD_ELECTRIC_SPEND] ?? 0)
                            ]
                        ]
                    ]
                ];
        }

        return [
            'project' => $project,
        ];
    }
}
