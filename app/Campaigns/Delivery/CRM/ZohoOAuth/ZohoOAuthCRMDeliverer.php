<?php

namespace App\Campaigns\Delivery\CRM\ZohoOAuth;

use App\Campaigns\Delivery\CRM\BaseInteractableCRMDeliverer;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleCRM;
use App\Models\Campaigns\Modules\Delivery\CompanyCRMTemplate;
use App\Models\Odin\ConsumerProduct;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;

class ZohoOAuthCRMDeliverer extends BaseInteractableCRMDeliverer
{
    const string GRANT_TOKEN_REQUEST_URL            = 'https://accounts.zoho.com/oauth/v2/token';
    const string GRANT_TOKEN_REQUEST_TYPE_KEY       = 'grant_type';
    const string GRANT_TOKEN_REQUEST_TYPE_VALUE     = 'authorization_code';
    const string GRANT_TOKEN_REQUEST_TOKEN          = 'code';
    const string GRANT_TOKEN_RESPONSE_REFRESH_TOKEN = 'refresh_token';
    const string GRANT_TOKEN_RESPONSE_SCOPE         = 'scope';

    const string TOKEN_REQUEST_CLIENT_ID     = 'client_id';
    const string TOKEN_REQUEST_CLIENT_SECRET = 'client_secret';
    const string TOKEN_RESPONSE_ACCESS_TOKEN = 'access_token';
    const string TOKEN_RESPONSE_API_DOMAIN   = 'api_domain';

    const string INTERACTABLE_REGISTER_TOKEN = 'registerOAuthGrantToken';

    const string RESPONSE_STATUS    = 'status';
    const string RESPONSE_MESSAGE   = 'message';

    public function deliver(ConsumerProduct $product, CompanyCampaign $campaign, ?array $payload = null): bool
    {
        $deliveryService = new ZohoOAuthDeliveryService($this->crm);

        return $this->getDeliveryProxy(
            $deliveryService->setOAuthFields(
                    $this->getFieldValue($campaign, self::TOKEN_REQUEST_CLIENT_ID) ?? "",
                    $this->getFieldValue($campaign, self::TOKEN_REQUEST_CLIENT_SECRET) ?? "",
                    $this->getFieldValue($campaign, self::GRANT_TOKEN_RESPONSE_REFRESH_TOKEN) ?? "",
                    $this->getFieldValue($campaign, self::TOKEN_RESPONSE_ACCESS_TOKEN),
                )->setUrl($this->getFieldValue($campaign, self::TOKEN_RESPONSE_API_DOMAIN))
                ->setLead($this->parseManyFieldValues($this->getManyFieldGroups($campaign, [self::ADDITIONAL_FIELDS_KEY, self::CUSTOM_FIELDS_KEY]), $product, $campaign)),
            $product,
            $campaign
        )->prepareDelivery($campaign)->success;
    }

    /**
     * @inheritDoc
     */
    protected function getDefaultSystemFields(): array
    {
        return [
            $this->formatField(key: self::GRANT_TOKEN_REQUEST_TOKEN, displayName: 'Grant Token'),
            $this->formatField(key: self::TOKEN_REQUEST_CLIENT_ID, displayName: 'Client ID'),
            $this->formatField(key: self::TOKEN_REQUEST_CLIENT_SECRET, displayName: 'Client Secret'),
            $this->formatField(key: self::TOKEN_RESPONSE_ACCESS_TOKEN, displayName: 'Access Token', showOnFrontend: false),
            $this->formatField(key: self::GRANT_TOKEN_RESPONSE_REFRESH_TOKEN, displayName: 'Refresh Token', showOnFrontend: false),
            $this->formatField(key: self::TOKEN_RESPONSE_API_DOMAIN, displayName: 'API Domain', showOnFrontend: false),
        ];
    }

    /**
     * @inheritDoc
     */
    protected function getDefaultAdditionalFields(): array
    {
        return [
            $this->formatField('Last_Name', 'Last Name', '[last_name]'),
            $this->formatField('First_Name', 'First Name', '[first_name]'),
            $this->formatField('Email', 'Email', '[email]'),
            $this->formatField('Street', 'Street', '[address_1] [address_2]'),
            $this->formatField('City', 'City', '[city]'),
            $this->formatField('State', 'State', '[state]'),
            $this->formatField('Zip_Code', 'Zip Code', '[zip_code]'),
            $this->formatField('Country', 'Country', '[country]'),
            $this->formatField('Phone', 'Phone', '[phone]'),
            $this->formatField('Lead_Source', value: '[lead_source]'),
            $this->formatField('Lead_Status'),
            $this->formatField('Description', 'Description', '[combined_comments]'),
        ];
    }

    /**
     * @inheritDoc
     */
    public function getInteractables(CompanyCampaignDeliveryModuleCRM|CompanyCRMTemplate|null $crm = null): array
    {
        if ($crm) {
            $this->crm = $crm;
            $this->payload   = $crm->payload;
        }

        $currentCampaign = $this->crm->module?->campaign ?? new CompanyCampaign();

        $refreshTokenExists = $currentCampaign
            ? $this->getFieldValue($currentCampaign, self::GRANT_TOKEN_RESPONSE_REFRESH_TOKEN)
            : null;
        $interactableText = $refreshTokenExists
            ? "This Zoho CRM Integration is already registered.\nOnly use the New Registration controls if a new Zoho API registration is required."
            : "This Zoho CRM Integration is not registered, and will not function.\nZoho OAuth Grant Tokens must be generated from the Zoho API console.";

        return [
            $this->formatInteractable(self::INTERACTABLE_REGISTER_TOKEN, true, 'Register New OAuth Token', null, $interactableText),
        ];
    }

    /**
     * @param array $payload
     * @return array
     * @throws GuzzleException
     */
    protected function registerOAuthGrantToken(array $payload): array
    {
        $client = new Client();

        $response = $client->request('POST', self::GRANT_TOKEN_REQUEST_URL, [
            'form_params' => [
                self::GRANT_TOKEN_REQUEST_TYPE_KEY  => self::GRANT_TOKEN_REQUEST_TYPE_VALUE,
                self::TOKEN_REQUEST_CLIENT_ID       => $this->getPayloadFieldValue($payload, self::TOKEN_REQUEST_CLIENT_ID) ?? '',
                self::TOKEN_REQUEST_CLIENT_SECRET   => $this->getPayloadFieldValue($payload, self::TOKEN_REQUEST_CLIENT_SECRET) ?? '',
                self::GRANT_TOKEN_REQUEST_TOKEN     => $this->getPayloadFieldValue($payload, self::GRANT_TOKEN_REQUEST_TOKEN) ?? '',
            ]
        ]);

        $responseData =  json_decode($response->getBody(), true);

        if (!array_key_exists(self::GRANT_TOKEN_RESPONSE_REFRESH_TOKEN, $responseData))
            return $this->handleOAuthError($responseData);

        if (!$this->scopeIsValid($responseData))
            return $this->handleOAuthError($responseData, "The Grant Token does not authorize writing to the Leads API");

        $interactables = $this->getInteractables();
        $interactables[0][self::INTERACTABLE_DESCRIPTION] = "Successfully registered with Zoho OAuth.\nPlease ensure you save the CRM Configuration and the Campaign.";

        return [
            self::RESPONSE_STATUS   => true,
            self::SYSTEM_FIELDS_KEY => [
                self::GRANT_TOKEN_RESPONSE_REFRESH_TOKEN    => $responseData[self::GRANT_TOKEN_RESPONSE_REFRESH_TOKEN],
                self::TOKEN_RESPONSE_ACCESS_TOKEN           => $responseData[self::TOKEN_RESPONSE_ACCESS_TOKEN] ?? "",
                self::TOKEN_RESPONSE_API_DOMAIN             => $responseData[self::TOKEN_RESPONSE_API_DOMAIN],
                self::GRANT_TOKEN_REQUEST_TOKEN             => '',
            ],
            self::INTERACTABLES_KEY => $interactables,
        ];
    }

    /**
     * Ensure the Grant Token gives us write access to the Zoho Leads API
     *
     * @param array $responseData
     * @return bool
     */
    private function scopeIsValid(array &$responseData): bool
    {
        $leadsScope = preg_match("/leads\.(all|create)/i", ($responseData[self::GRANT_TOKEN_RESPONSE_SCOPE] ?? ""));

        return !!$leadsScope;
    }

    /**
     * @param array $responseData
     * @param string|null $customMessage
     * @return array
     */
    private function handleOAuthError(array &$responseData, ?string $customMessage = null): array
    {
        $errorMessage = $customMessage ?? $responseData['error']
            ? "Error received from Zoho API: {$responseData['error']}"
            : "Failed to authenticate with OAuth Grant Token";

        return [
            self::RESPONSE_STATUS   => false,
            self::RESPONSE_MESSAGE  => $errorMessage,
        ];
    }

}
