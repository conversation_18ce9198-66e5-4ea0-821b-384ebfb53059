<?php

namespace App\Campaigns\Delivery\CRM\ZohoOAuth;

use App\Campaigns\Delivery\CRM\BaseCRMDeliverer;
use App\Campaigns\Delivery\CRM\BaseCRMDeliveryService;
use App\Campaigns\Delivery\CRM\CRMDeliveryResponse;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleCRM;
use Illuminate\Support\Facades\Http;

class ZohoOAuthDeliveryService extends BaseCRMDeliveryService
{
    const string DEFAULT_API_DOMAIN = 'https://www.zohoapis.com';
    const string BASE_ROUTE         = '/crm/v2';
    const string LEADS_ROUTE        = '/Leads';
    const string LEADS_API_PAYLOAD  = 'data';

    const string AUTH_HEADER_KEY       = 'Authorization';
    const string AUTH_HEADER_PREFIX    = 'Zoho-oauthtoken ';
    const string AUTH_RESPONSE_CODE    = 'code';
    const string AUTH_RESPONSE_DETAILS = 'details';
    const string AUTH_RESPONSE_PAYLOAD = 'data';
    const string AUTH_EXPIRED_TOKEN    = 'INVALID_TOKEN';

    const string ZOHO_RESPONSE_SUCCESS = 'SUCCESS';

    const string TEST_KEY = 'zoho';

    protected string $apiDomain = self::DEFAULT_API_DOMAIN;
    protected string $refreshToken;
    protected string $clientId;
    protected string $clientSecret;
    protected ?string $accessToken = null;

    protected array $body = [];

    public function __construct(
        protected CompanyCampaignDeliveryModuleCRM $crm
    ) {}

    /**
     * @inheritDoc
     */
    public function deliver(bool $attemptedRefresh = false): CRMDeliveryResponse
    {
        if (!$this->refreshToken)
            return new CRMDeliveryResponse(false, ['error' => 'Bad Zoho CRM configuration, no refresh token found.']);

        if (!$this->accessToken)
            if (!$this->refreshAccessToken())
                return $this->deliveryError('Invalid OAuth token.');

        $response = Http::withHeaders($this->getHeaders())
            ->withBody(json_encode($this->body))
            ->post($this->getUrl());

        $responsePayload = $response->json()[self::AUTH_RESPONSE_PAYLOAD] ?? [];
        $responseData = $responsePayload[0] ?? [];
        $responseStatus = $response->status();

        $responseCode = $responseData[self::AUTH_RESPONSE_CODE] ?? "";
        $responseDetails = $responseData[self::AUTH_RESPONSE_DETAILS] ?? [];

        if ($responseCode !== self::ZOHO_RESPONSE_SUCCESS) {
            if ($responseStatus === 401 || $responseCode === self::AUTH_EXPIRED_TOKEN) {
                // Attempt to refresh the token if it hasn't already been tried
                if ($attemptedRefresh || !$this->refreshAccessToken())
                    return $this->deliveryError('Invalid OAuth token.');
                else
                    return $this->deliver(true);
            } else
                return $this->deliveryError('An unknown Zoho CRM error occurred');
        }

        return new CRMDeliveryResponse(true, ['body' => $responseDetails]);
    }

    /**
     * @inheritDoc
     */
    public function debugDelivery(): CRMDeliveryResponse
    {
        $headers = [];
        $this->setTestHeaders($headers);

        $response = Http::withHeaders($headers)
            ->withBody(json_encode($this->body))
            ->post($this->getDebugUrl(self::TEST_KEY));
        $responseData = $response->json() ?? [];

        return new CRMDeliveryResponse($response->successful(), ['body' => $responseData]);
    }

    /**
     * @param array $leadData
     * @return $this
     */
    public function setLead(array $leadData): ZohoOAuthDeliveryService
    {
        $this->body = [
            self::LEADS_API_PAYLOAD  => [
                $leadData
            ]
        ];

        return $this;
    }

    /**
     * @param string|null $url
     * @return ZohoOAuthDeliveryService
     */
    public function setUrl(?string $url): ZohoOAuthDeliveryService
    {
        $this->apiDomain = $url ?? $this->apiDomain;

        return $this;
    }

    /**
     * @param string $clientId
     * @param string $clientSecret
     * @param string $refreshToken
     * @param string|null $accessToken
     * @return ZohoOAuthDeliveryService
     */
    public function setOAuthFields(string $clientId, string $clientSecret, string $refreshToken, ?string $accessToken): ZohoOAuthDeliveryService
    {
        $this->clientId = $clientId;
        $this->clientSecret = $clientSecret;
        $this->refreshToken = $refreshToken;
        $this->accessToken = $accessToken;

        return $this;
    }

    /**
     * @return bool
     */
    private function refreshAccessToken(): bool
    {
        $refreshTokenResponse = Http::post($this->getTokenRefreshURL())
            ->json();
        $newToken = $refreshTokenResponse[ZohoOAuthCRMDeliverer::TOKEN_RESPONSE_ACCESS_TOKEN] ?? null;

        if ($newToken) {
            $this->accessToken = $newToken;
            $this->crm->updatePayloadFieldValue(BaseCRMDeliverer::SYSTEM_FIELDS_KEY, ZohoOAuthCRMDeliverer::TOKEN_RESPONSE_ACCESS_TOKEN, $newToken);
        }

        return !!$newToken;
    }

    /**
     * @param string|null $message
     * @return CRMDeliveryResponse
     */
    private function deliveryError(?string $message): CRMDeliveryResponse
    {
        return new CRMDeliveryResponse(
            false,
            ['error' => $message ?? 'An unknown error occurred'],
        );
    }

    /**
     * @return string
     */
    private function getTokenRefreshURL(): string
    {
        return ZohoOAuthCRMDeliverer::GRANT_TOKEN_REQUEST_URL
            . "?" . ZohoOAuthCRMDeliverer::GRANT_TOKEN_RESPONSE_REFRESH_TOKEN . "={$this->refreshToken}"
            . "&" . ZohoOAuthCRMDeliverer::TOKEN_REQUEST_CLIENT_ID . "={$this->clientId}"
            . "&" . ZohoOAuthCRMDeliverer::TOKEN_REQUEST_CLIENT_SECRET . "={$this->clientSecret}"
            . "&" . ZohoOAuthCRMDeliverer::GRANT_TOKEN_REQUEST_TYPE_KEY . '=refresh_token';
    }

    /**
     * @return string[]
     */
    public function getHeaders(): array
    {
        return [
            self::AUTH_HEADER_KEY => self::AUTH_HEADER_PREFIX . $this->accessToken,
        ];
    }

    /**
     * @return string
     */
    public function getUrl(): string
    {
        $domain = $this->apiDomain ?? self::DEFAULT_API_DOMAIN;

        return $domain . self::BASE_ROUTE . self::LEADS_ROUTE;
    }
}
