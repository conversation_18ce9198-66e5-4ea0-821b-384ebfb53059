<?php

namespace App\Campaigns\Delivery\CRM;

use Illuminate\Contracts\Support\Arrayable;

class CRMTestDeliveryResponse implements Arrayable
{
    public function __construct(
        public mixed $url,
        public array $requestBody,
        public array $headers,
        public bool $success,
        public array $responseBody
    ) {}


    /**
     * @return array
     */
    public function toArray(): array
    {
       return [
           'url' => $this->url,
           'request_body' => $this->requestBody,
           'headers' => $this->headers,
           'success' => $this->success,
           'response_body' => $this->responseBody
       ];
    }
}
