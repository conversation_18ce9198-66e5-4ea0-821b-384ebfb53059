<?php

namespace App\Campaigns\Delivery\CRM\Insightly;

use App\Campaigns\Delivery\CRM\BaseCRMDeliverer;
use App\Campaigns\Delivery\CRM\Enums\CRMFieldType;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ConsumerProduct;

class InsightlyCRMDeliverer extends BaseCRMDeliverer
{
    /**
     * @inheritDoc
     */
    public function deliver(ConsumerProduct $product, CompanyCampaign $campaign, ?array $payload = null): bool
    {
        /** @var InsightlyCRMDeliveryService $deliveryService */
        $deliveryService = app(InsightlyCRMDeliveryService::class);

        $additionalFields = $this->parseManyFieldValues($this->getManyFieldGroups($campaign, [self::ADDITIONAL_FIELDS_KEY]), $product, $campaign);
        $customFields = $this->parseManyFieldValues($this->getManyFieldGroups($campaign, [self::CUSTOM_FIELDS_KEY]), $product, $campaign);
        $apiKey = $this->getFieldValue($campaign, InsightlyCRMDeliveryService::FIELD_API_KEY);
        $apiVersion = $this->getFieldValue($campaign, InsightlyCRMDeliveryService::FIELD_API_VERSION);

        return $this->getDeliveryProxy(
            $deliveryService->setApiVersion($apiVersion)
                ->setApiKey($apiKey)
                ->setBody($additionalFields, $customFields),
            $product,
            $campaign
        )->prepareDelivery($campaign)->success;
    }

    /**
     * @inheritDoc
     */
    protected function getDefaultSystemFields(): array
    {
        return [
            $this->formatField(InsightlyCRMDeliveryService::FIELD_API_KEY, 'API Key', required: true),
            $this->formatField(key: InsightlyCRMDeliveryService::FIELD_API_VERSION,
                displayName: 'API Version',
                value: InsightlyApiVersion::VERSION_3_1->value,
                type: CRMFieldType::DROPDOWN,
                payload: [
                    'options' => [
                        'v2.1' => InsightlyApiVersion::VERSION_2_1->value,
                        'v3.1' => InsightlyApiVersion::VERSION_3_1->value,
                    ]
                ], required: true),
        ];
    }

    /**
     * @inheritDoc
     */
    protected function getDefaultAdditionalFields(): array
    {
        return [
            $this->formatField('FIRST_NAME', displayName: 'First Name', value: '[first_name]'),
            $this->formatField('LAST_NAME', displayName: 'Last Name', value: '[last_name]'),
            $this->formatField('EMAIL', displayName: 'Email', value: '[email]'),
            $this->formatField('PHONE', displayName: 'Phone', value: '[phone]'),
            $this->formatField('ADDRESS_STREET', displayName: 'Street Address', value: '[address_1]'),
            $this->formatField('ADDRESS_CITY', displayName: 'City',  value: '[city]'),
            $this->formatField('ADDRESS_STATE', displayName: 'State',  value: '[state]'),
            $this->formatField('ADDRESS_POSTCODE',  displayName: 'Zip Code', value: '[zip_code]'),
            $this->formatField('ADDRESS_COUNTRY', displayName: 'Country',  value: '[country]'),
        ];
    }
}
