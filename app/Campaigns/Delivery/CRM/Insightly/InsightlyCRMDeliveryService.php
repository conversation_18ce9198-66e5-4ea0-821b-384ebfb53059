<?php

namespace App\Campaigns\Delivery\CRM\Insightly;

use App\Campaigns\Delivery\CRM\BaseCRMDeliveryService;
use App\Campaigns\Delivery\CRM\CRMDeliveryResponse;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class InsightlyCRMDeliveryService extends BaseCRMDeliveryService
{
    const string BASE_URL          = "https://api.na1.insightly.com/v3.1";
    const string BASE_URL_v21      = "https://api.insight.ly/v2.1";
    const string LEADS_API         = "/Leads";
    const string CUSTOM_FIELDS_API = "/CustomFields";

    const string FIELD_API_KEY             = 'api_key';
    const string FIELD_API_VERSION         = 'api_version';
    const string CUSTOM_FIELDS_ARRAY_KEY   = "CUSTOMFIELDS";
    const string CUSTOM_FIELD_KEY_NAME     = "FIELD_NAME";
    const string CUSTOM_FIELD_KEY_VALUE    = "FIELD_VALUE";
    const string CUSTOM_FIELD_KEY_NAME_v21 = "CUSTOM_FIELD_ID";

    const string TEST_KEY = 'insightly';

    protected string $apiKey;
    protected InsightlyApiVersion $apiVersion = InsightlyApiVersion::VERSION_3_1;
    protected string $url = self::BASE_URL . self::LEADS_API;
    protected array $body = [];
    protected array $headers = [];

    // This uses the v3.1 field names as keys
    // Values are v2.1 names to replace with - will need to change structure if more versions are needed
    protected array $versionedKeys = [
        'PHONE' => 'PHONE_NUMBER',
        'EMAIL' => 'EMAIL_ADDRESS',
    ];

    /**
     * @inheritDoc
     */
    public function deliver(): CRMDeliveryResponse
    {
        if (!$this->apiKey)
            return new CRMDeliveryResponse(false, ['error' => 'Bad Insightly CRM configuration, no API key supplied.']);

        $this->setUrl();

        $response = Http::withHeaders($this->headers)
            ->withBody(json_encode($this->body))
            ->post($this->url);

        return new CRMDeliveryResponse($response->successful(), ['body' => $response->body()]);
    }

    /**
     * @return void
     */
    private function setUrl(): void
    {
        if ($this->apiVersion === InsightlyApiVersion::VERSION_2_1)
            $this->url = self::BASE_URL_v21 . self::LEADS_API;
    }

    /**
     * @inheritDoc
     */
    public function debugDelivery(): CRMDeliveryResponse
    {
        $this->url = $this->getDebugUrl(self::TEST_KEY);
        $this->setTestHeaders($this->headers);
        $this->body['api_key'] = Str::mask($this->apiKey, '*', 5);
        $this->apiKey = config('services.crm_delivery.test_secret');

        return $this->deliver();
    }

    /**
     * @param int $apiVersion
     * @return $this
     */
    public function setApiVersion(int $apiVersion): self
    {
        $version = InsightlyApiVersion::tryFrom($apiVersion) ?? InsightlyApiVersion::VERSION_3_1;
        $this->apiVersion = $version;

        return $this;
    }

    /**
     * @param string|null $apiKey
     * @return self
     */
    public function setApiKey(?string $apiKey): self
    {
        $this->apiKey = base64_encode($apiKey ?? "");
        $this->headers['Authorization'] = "Basic $this->apiKey";

        return $this;
    }

    /**
     * @return array
     */
    public function getBody(): array
    {
        return [...$this->body];
    }

    /**
     * @return array
     */
    public function getHeaders(): array
    {
        return [...$this->headers];
    }

    /**
     * @return string
     */
    public function getUrl(): string
    {
        return $this->url;
    }

    /**
     * @return void
     */
    protected function replaceKeys(): void
    {
        // v2.1 key replacement
        if ($this->apiVersion === InsightlyApiVersion::VERSION_2_1) {
            foreach ($this->versionedKeys as $defaultKey => $cloneKey) {
                if (array_key_exists($defaultKey, $this->body) && !array_key_exists($cloneKey, $this->body)) {
                    $this->body[$cloneKey] = $this->body[$defaultKey];
                    unset($this->body[$defaultKey]);
                }
            }
        }
    }

    /**
     * @param array $additionalFields
     * @param array $customFields
     * @return self
     */
    public function setBody(array $additionalFields, array $customFields): self
    {
        $this->body = $additionalFields;
        $this->replaceKeys();
        $this->setCustomFields($customFields);

        return $this;
    }

    /**
     * @param array $customFields
     * @return void
     */
    private function setCustomFields(array $customFields): void
    {
        $customFieldIdKey = $this->apiVersion === InsightlyApiVersion::VERSION_2_1
            ? self::CUSTOM_FIELD_KEY_NAME_v21
            : self::CUSTOM_FIELD_KEY_NAME;

        if ($customFields) {
            $this->body[self::CUSTOM_FIELDS_ARRAY_KEY] = [];
            foreach ($customFields as $customFieldKey => $customFieldValue) {
                $this->body[self::CUSTOM_FIELDS_ARRAY_KEY][] = [
                    $customFieldIdKey            => $customFieldKey,
                    self::CUSTOM_FIELD_KEY_VALUE => $customFieldValue,
                ];
            }
        }
    }
}
