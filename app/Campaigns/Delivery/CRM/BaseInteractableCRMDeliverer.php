<?php

namespace App\Campaigns\Delivery\CRM;

use App\Campaigns\Delivery\CRM\Enums\CRMFieldType;
use App\Campaigns\Delivery\CRM\Enums\CRMInteractableType;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleCRM;
use App\Models\Campaigns\Modules\Delivery\CompanyCRMTemplate;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use RuntimeException;

abstract class BaseInteractableCRMDeliverer extends BaseCRMDeliverer
{
    const INTERACTABLE_METHOD       = 'method';
    const INTERACTABLE_DISPLAY_FLAG = 'display_on_front_end';
    const INTERACTABLE_DISPLAY_NAME = 'display_name';
    const INTERACTABLE_TYPE         = 'type';
    const INTERACTABLE_DESCRIPTION  = 'description';

    const INTERACTABLE_FIELDS_KEY   = 'interactable_fields';

    /**
     * @param CompanyCampaign $campaign
     * @return Collection
     */
    public function getFields(CompanyCampaign $campaign): Collection
    {
        if ($this->cachedFields !== null)
            return $this->cachedFields;

        return $this->cachedFields = collect([
            self::SYSTEM_FIELDS_KEY       => $this->getSystemFields(),
            self::ADDITIONAL_FIELDS_KEY   => $this->getAdditionalFields(),
            self::INTERACTABLE_FIELDS_KEY => $this->payload[self::INTERACTABLE_FIELDS_KEY] ?? [],
        ]);
    }

    /**
     * @param CompanyCampaign $campaign
     * @param string $groupKey
     * @return array
     */
    public function getInteractableFieldGroup(CompanyCampaign $campaign, string $groupKey): array
    {
        return $this->getFields($campaign)[self::INTERACTABLE_FIELDS_KEY][$groupKey] ?? [];
    }

    /**
     * @param string $methodName
     * @param bool $displayOnFrontEnd
     * @param string|null $displayName
     * @param CRMInteractableType|null $type
     * @param string|null $description
     * @return array
     */
    protected static function formatInteractable(
        string $methodName,
        bool $displayOnFrontEnd,
        ?string $displayName = null,
        ?CRMInteractableType $type = CRMInteractableType::BUTTON,
        ?string $description = null,
    ): array
    {
        if ($displayName === null)
            $displayName = Str::headline($methodName);

        return [
            self::INTERACTABLE_METHOD       => $methodName,
            self::INTERACTABLE_DISPLAY_FLAG => $displayOnFrontEnd,
            self::INTERACTABLE_DISPLAY_NAME => $displayName,
            self::INTERACTABLE_TYPE         => $type,
            self::INTERACTABLE_DESCRIPTION  => $description,
        ];
    }

    /**
     * @param string $methodName
     * @param array $currentPayload
     * @return array
     */
    public function runMethod(string $methodName, array $currentPayload): array
    {
        if(!method_exists($this, $methodName))
            throw new RuntimeException("Method '$methodName' does not exist");

        return $this->{$methodName}($currentPayload);
    }

    /**
     * @param array $payload
     * @param string $key
     * @param string|null $fieldType
     * @return mixed
     */
    protected function getPayloadFieldValue(array &$payload, string $key, ?string $fieldType = self::SYSTEM_FIELDS_KEY): mixed
    {
        $targetField = collect($payload[$fieldType])->first(fn($field) => $field[self::CRM_FIELD_KEY] === $key);

        return $targetField
            ? $targetField[self::CRM_FIELD_VALUE]
            : null;
    }

    /**
     * @param CompanyCampaignDeliveryModuleCRM|CompanyCRMTemplate|null $crm
     * @return array
     */
    public abstract function getInteractables(CompanyCampaignDeliveryModuleCRM|CompanyCRMTemplate|null $crm): array;
}
