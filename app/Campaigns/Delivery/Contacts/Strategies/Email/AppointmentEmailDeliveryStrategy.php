<?php

namespace App\Campaigns\Delivery\Contacts\Strategies\Email;

use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\QualityTier as QualityTierEnum;
use App\Mail\Appointments\NotifyAppointmentCompany;
use App\Mail\Appointments\NotifyAppointmentConsumer;
use App\Mail\Appointments\NotifyCancellationCompany;
use App\Mail\Appointments\NotifyCancellationConsumer;
use App\Models\AppointmentDelivery;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Services\Odin\Campaigns\CompanyCampaignAppointmentService;
use Exception;
use Illuminate\Mail\Mailable;
use Illuminate\Support\Arr;

class AppointmentEmailDeliveryStrategy extends BaseEmailDeliveryStrategy
{
    protected ?AppointmentDelivery $appointmentDelivery;

    public function __construct(
        protected CompanyCampaignAppointmentService $appointmentService,
    ) {}

    /**
     * @inheritDoc
     * @throws Exception
     */
    public function deliver(ConsumerProduct $product, CompanyCampaign $campaign, ?array $payload = []): bool
    {
        if (!$this->getAppointmentDelivery($campaign, $product))
            return false;

        $firstDelivery = Arr::get($payload ?? [], self::FIELD_INITIAL_DELIVERY, false);

        if ($firstDelivery || !$this->appointmentDelivery->company_delivered)
            $this->deliverEmailToCompanyUser($product, $campaign, $payload);

        return $this->appointmentDelivery->company_delivered;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param CompanyCampaign $companyCampaign
     * @return bool
     * @throws Exception
     */
    public function deliverToConsumer(ConsumerProduct $consumerProduct, CompanyCampaign $companyCampaign): bool
    {
        if (!$this->getAppointmentDelivery($companyCampaign, $consumerProduct))
            return false;

        return $this->appointmentDelivery->consumer_delivered_email
            || $this->deliverEmailToConsumer($consumerProduct, $companyCampaign);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param CompanyCampaign $companyCampaign
     * @param array $payload
     * @return bool
     * @throws Exception
     */
    public function deliverCancellationToCompanyUser(ConsumerProduct $consumerProduct, CompanyCampaign $companyCampaign, array $payload): bool
    {
        $this->getAppointmentDelivery($companyCampaign, $consumerProduct);

        return $this->deliverEmailToCompanyUser( $consumerProduct, $companyCampaign, $payload, true);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param CompanyCampaign $companyCampaign
     * @return bool
     * @throws Exception
     */
    public function deliverCancellationToConsumer(ConsumerProduct $consumerProduct, CompanyCampaign $companyCampaign): bool
    {
        $this->getAppointmentDelivery($companyCampaign, $consumerProduct);

        return $this->deliverEmailToConsumer($consumerProduct, $companyCampaign, true);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param CompanyCampaign $companyCampaign
     * @param array $payload
     * @param ?bool $cancellation
     * @return bool
     * @throws Exception
     */
    protected function deliverEmailToCompanyUser(ConsumerProduct $consumerProduct, CompanyCampaign $companyCampaign, array $payload = [], ?bool $cancellation = false): bool
    {
        $toEmail = Arr::get($payload ?? [], self::FIELD_EMAIL, null);
        $toName =  Arr::get($payload ?? [], self::FIELD_COMPANY_USER_NAME, null);
        if (!$toEmail)
            return false;

        $mailable = $cancellation
            ? $this->buildCompanyCancellationEmail($consumerProduct, $companyCampaign, $toName)
            : $this->buildCompanyNotificationEmail($consumerProduct, $companyCampaign, $toName);

        if ($delivered = $this->deliverEmail($mailable, $toEmail, $companyCampaign) && !$cancellation)
            $this->appointmentDelivery->update([AppointmentDelivery::FIELD_COMPANY_DELIVERED => true]);

        return $delivered;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param CompanyCampaign $companyCampaign
     * @param ?bool $cancellation
     * @return bool
     * @throws Exception
     */
    protected function deliverEmailToConsumer(ConsumerProduct $consumerProduct, CompanyCampaign $companyCampaign, ?bool $cancellation = false): bool
    {
        $toEmail = app()->isProduction() ? $consumerProduct->consumer->{Consumer::FIELD_EMAIL} : config('app.outgoing_communication.test_email');

        if (!$toEmail)
            return false;

        $mailable = $cancellation
            ? $this->buildConsumerCancellationEmail($consumerProduct, $companyCampaign)
            : $this->buildConsumerNotificationEmail($consumerProduct, $companyCampaign);

        if ($delivered = $this->deliverEmail($mailable, $toEmail, $companyCampaign) && !$cancellation)
            $this->appointmentDelivery->update([
                AppointmentDelivery::FIELD_CONSUMER_DELIVERED_EMAIL => true,
                AppointmentDelivery::FIELD_CONSUMER_DELIVERED       => true
            ]);

        return $delivered;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param CompanyCampaign $companyCampaign
     * @param string $companyUserName
     * @return NotifyAppointmentCompany
     * @throws Exception
     */
    protected function buildCompanyNotificationEmail(ConsumerProduct $consumerProduct, CompanyCampaign $companyCampaign, string $companyUserName): Mailable
    {
        $qualityTier = QualityTierEnum::tryFrom($this->getProductAssignment($consumerProduct, $companyCampaign)->qualityTier->name);

        return new NotifyAppointmentCompany(
            consumerProduct: $consumerProduct,
            address: $consumerProduct->address,
            consumer: $consumerProduct->consumer,
            industry: $consumerProduct->industryService->industry->name,
            companyName: $companyCampaign->company->name,
            companyUserName: $companyUserName,
            appointmentDateTime: $this->appointmentService->getFormattedAppointmentDateTime($consumerProduct)->format('m/d/Y g:i A'),
            appointmentType: $qualityTier,
            detailsUrl: $this->getCompanyAppointmentUrl($consumerProduct, $companyCampaign),
        );
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param CompanyCampaign $companyCampaign
     * @return Mailable
     * @throws Exception
     */
    protected function buildConsumerNotificationEmail(ConsumerProduct $consumerProduct, CompanyCampaign $companyCampaign): Mailable
    {
        $qualityTier = QualityTierEnum::tryFrom($this->getProductAssignment($consumerProduct, $companyCampaign)->qualityTier->name);

        return new NotifyAppointmentConsumer(
            consumerProduct: $consumerProduct,
            address: $consumerProduct->address,
            consumer: $consumerProduct->consumer,
            companyName: $companyCampaign->company->name,
            appointmentDateTime: $this->appointmentService->getFormattedAppointmentDateTime($consumerProduct)->format('m/d/Y g:i A'),
            appointmentType: $qualityTier,
            industry: $consumerProduct->industryService->industry->name,
            detailsUrl: $this->getConsumerAppointmentUrl($consumerProduct),
        );
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param CompanyCampaign $companyCampaign
     * @param string $companyUserName
     * @return Mailable
     * @throws Exception
     */
    protected function buildCompanyCancellationEmail(ConsumerProduct $consumerProduct, CompanyCampaign $companyCampaign, string $companyUserName): Mailable
    {
        return new NotifyCancellationCompany(
            consumerProduct: $consumerProduct,
            address: $consumerProduct->address,
            consumer: $consumerProduct->consumer,
            industryEnum: IndustryEnum::from($consumerProduct->industryService->industry->name),
            companyName: $companyCampaign->company->name,
            companyUserName: $companyUserName,
            appointmentDateTime: $this->appointmentService->getFormattedAppointmentDateTime($consumerProduct)->format('m/d/Y g:i A'),
        );
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param CompanyCampaign $companyCampaign
     * @return Mailable
     * @throws Exception
     */
    protected function buildConsumerCancellationEmail(ConsumerProduct $consumerProduct, CompanyCampaign $companyCampaign): Mailable
    {
        return new NotifyCancellationConsumer(
            consumerProduct: $consumerProduct,
            consumer: $consumerProduct->{ConsumerProduct::RELATION_CONSUMER},
            industryEnum: IndustryEnum::from($consumerProduct->industryService->industry->name),
            companyName: $companyCampaign->company->name,
            appointmentDateTime: $this->appointmentService->getFormattedAppointmentDateTime($consumerProduct)->format('m/d/Y g:i A'),
            isRejection: false
        );
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param CompanyCampaign $companyCampaign
     * @return string
     */
    protected function getCompanyAppointmentUrl(ConsumerProduct $consumerProduct, CompanyCampaign $companyCampaign): string
    {
        return $this->appointmentService->getCompanyUrl($this->getProductAssignment($consumerProduct, $companyCampaign)->id);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @return string
     */
    protected function getConsumerAppointmentUrl(ConsumerProduct $consumerProduct): string
    {
        return $this->appointmentService->getConsumerUrl($this->appointmentDelivery, $consumerProduct);
    }

    /**
     * @param CompanyCampaign $companyCampaign
     * @param ConsumerProduct $consumerProduct
     * @return AppointmentDelivery|null
     */
    protected function getAppointmentDelivery(CompanyCampaign $companyCampaign, ConsumerProduct $consumerProduct): ?AppointmentDelivery
    {
        $this->appointmentDelivery = $this->appointmentDelivery
            ?? $this->appointmentService->getAppointmentDelivery($companyCampaign, $this->getProductAssignment($consumerProduct, $companyCampaign));

        return $this->appointmentDelivery;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param CompanyCampaign $companyCampaign
     * @return ProductAssignment
     */
    protected function getProductAssignment(ConsumerProduct $consumerProduct, CompanyCampaign $companyCampaign): ProductAssignment
    {
        /** @var ProductAssignment */
        return $consumerProduct->productAssignment()
            ->where(ProductAssignment::FIELD_COMPANY_ID, $companyCampaign->company_id)
            ->first();
    }
}
