<?php

namespace App\Campaigns\Delivery\Contacts\Strategies\Email;

use App\Models\EmailTemplate;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Services\EmailTemplates\EmailTemplateService;

class EmailTemplateStrategy
{
    protected array $cachedTemplates = [];

    public function __construct(
        protected EmailTemplateService $templateService
    ) {}

    /**
     * Handles returning the EmailTemplate for a given product.
     *
     * @param ConsumerProduct $product
     * @return EmailTemplate
     */
    public function getEmailTemplateForProduct(ConsumerProduct $product): EmailTemplate
    {
        if($this->industryHasTemplateOverride($product->industryService->industry))
            return $this->getIndustryTemplateOverride($product->industryService->industry);

        return $this->getDefaultEmailTemplate();
    }

    /**
     * Handles checking whether an industry has a template override.
     *
     * @param Industry $industry
     * @return bool
     */
    protected function industryHasTemplateOverride(Industry $industry): bool
    {
        return !!$this->getIndustryTemplateOverride($industry);
    }

    /**
     * Handles returning the industry template override.
     *
     * @param Industry $industry
     * @return EmailTemplate|null
     */
    protected function getIndustryTemplateOverride(Industry $industry): ?EmailTemplate
    {
        if (isset($this->cachedTemplates[$industry->slug]) && !!$this->cachedTemplates[$industry->slug])
            return $this->cachedTemplates[$industry->slug];

        return $this->cachedTemplates[$industry->slug] = $this->templateService->getLeadDeliveryTemplate($industry);
    }

    /**
     * Handles returning the default email template for deliveries.
     *
     * @return EmailTemplate
     */
    protected function getDefaultEmailTemplate(): EmailTemplate
    {
        return $this->templateService->getDefaultLeadDeliveryTemplate();
    }
}
