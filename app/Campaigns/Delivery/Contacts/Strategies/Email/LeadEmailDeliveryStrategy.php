<?php

namespace App\Campaigns\Delivery\Contacts\Strategies\Email;

use App\Mail\Campaigns\LeadDeliveryEmail;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\EmailTemplate;
use App\Models\Odin\ConsumerProduct;
use Exception;
use Illuminate\Support\Arr;

class LeadEmailDeliveryStrategy extends BaseEmailDeliveryStrategy
{
    public function __construct(
        protected EmailTemplateStrategy $templateStrategy
    ) {}

    /**
     * @inheritDoc
     */
    public function deliver(ConsumerProduct $product, CompanyCampaign $campaign, ?array $payload = []): bool
    {
        $email = Arr::get($payload ?? [], self::FIELD_EMAIL, null);

        if (!$email)
            return false;

        return $this->deliverLeadEmail($email, $product, $campaign, $payload);
    }

    /**
     * Returns the template for the given consumer product.
     *
     * @param ConsumerProduct $product
     * @return EmailTemplate
     */
    protected function getTemplateForProduct(ConsumerProduct $product): EmailTemplate
    {
        return $this->templateStrategy->getEmailTemplateForProduct($product);
    }

    /**
     * Handle delivering email to contact.
     *
     * @param string $to
     * @param ConsumerProduct $product
     * @param CompanyCampaign $campaign
     * @param array|null $payload
     *
     * @return bool
     * @throws Exception
     */
    public function deliverLeadEmail(string $to, ConsumerProduct $product, CompanyCampaign $campaign, ?array $payload= null): bool
    {
        $mailable = new LeadDeliveryEmail(
            $product,
            $campaign,
            $this->getTemplateForProduct($product),
            $to,
        );

        return $this->deliverEmail($mailable, $to, $campaign, $payload);
    }
}
