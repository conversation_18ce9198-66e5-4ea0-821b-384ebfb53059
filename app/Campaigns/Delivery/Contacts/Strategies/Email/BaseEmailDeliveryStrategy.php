<?php

namespace App\Campaigns\Delivery\Contacts\Strategies\Email;

use App\Campaigns\Delivery\Contacts\Enums\ContactDeliveryLogType;
use \App\Services\Emails\MailServerService;
use App\Contracts\Campaigns\Delivery\DeliveryStrategyContract;
use App\Enums\Odin\Product as ProductEnum;
use App\Jobs\TrackContactDeliveryJob;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignContactDeliveryLog;
use App\Models\Odin\ConsumerProduct;
use Exception;
use Illuminate\Mail\Mailable;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Mail;

abstract class BaseEmailDeliveryStrategy implements DeliveryStrategyContract
{
    const string FIELD_EMAIL                      = 'email';
    const string FIELD_COMPANY_USER_NAME          = 'company_user_name';
    const string FIELD_INITIAL_DELIVERY           = 'initial_delivery';
    const string FIELD_CONSUMER_PRODUCT_ID        = 'consumer_product_id';
    const string FIELD_CONTACT_DELIVERY_MODULE_ID = 'contact_delivery_module_id';

    /**
     * @param CompanyCampaign $campaign
     * @return DeliveryStrategyContract
     */
    public static function makeEmailDeliveryStrategy(CompanyCampaign $campaign): DeliveryStrategyContract
    {
        return match($campaign->product->name) {
            ProductEnum::APPOINTMENT->value => app(AppointmentEmailDeliveryStrategy::class),
            default                         => app(LeadEmailDeliveryStrategy::class),
        };
    }

    /**
     * Handle delivering email to contact.
     *
     * @param Mailable $mailable
     * @param string $to
     * @param CompanyCampaign $campaign
     * @param array|null $payload
     *
     * @return bool
     * @throws Exception
     */
    protected function deliverEmail(Mailable $mailable, string $to, CompanyCampaign $campaign, ?array $payload = null): bool
    {
        $mailServerService = app(MailServerService::class);
        $status = $mailServerService->sendEmail(MailServerService::PRIMARY_MAIL_SERVER, $mailable, $to);

        $this->trackDelivery($to, $mailable, $status, $campaign, $payload ?? []);

        return $status;
    }

    /**
     * @param string $to
     * @param Mailable $mailable
     * @param bool $status
     * @param CompanyCampaign $campaign
     * @param array $payload
     *
     * @return void
     */
    protected function trackDelivery(string $to, Mailable $mailable, bool $status, CompanyCampaign $campaign, array $payload): void
    {
        $consumerProductId = Arr::get($payload, BaseEmailDeliveryStrategy::FIELD_CONSUMER_PRODUCT_ID);
        $contactDeliveryModuleId = Arr::get($payload, BaseEmailDeliveryStrategy::FIELD_CONTACT_DELIVERY_MODULE_ID);

        if (!$consumerProductId || !$contactDeliveryModuleId) {
            logger()->error('Failed to create email delivery tracking');
            return;
        }

        try {
            $content = $mailable->render();
        } catch (Exception $e) {
            logger()->error("Failed to get delivery email content. error: {$e->getMessage()}");
            $content = null;
        }

        TrackContactDeliveryJob::dispatch(
            $campaign,
            $consumerProductId,
            $contactDeliveryModuleId,
            ContactDeliveryLogType::EMAIL,
            $status,
            [
                CompanyCampaignContactDeliveryLog::PAYLOAD_TO => $to,
                CompanyCampaignContactDeliveryLog::PAYLOAD_CONTENT => $content,
                CompanyCampaignContactDeliveryLog::PAYLOAD_EMAIL_SUBJECT => $mailable->subject ?? '',
            ]
        );
    }

    /**
     * @inheritDoc
     */
    abstract public function deliver(ConsumerProduct $product, CompanyCampaign $campaign, ?array $payload = null): bool;
}
