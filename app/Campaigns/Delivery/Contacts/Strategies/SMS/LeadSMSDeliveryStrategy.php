<?php

namespace App\Campaigns\Delivery\Contacts\Strategies\SMS;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ConsumerProduct;
use App\Services\Communication\TwilioCommunicationService;
use Illuminate\Support\Arr;

class LeadSMSDeliveryStrategy extends BaseSMSDeliveryStrategy
{
    public function __construct(
        protected SMSTemplateStrategy        $templateStrategy,
        protected TwilioCommunicationService $smsService
    ) {}

    /**
     * @inheritDoc
     */
    public function deliver(ConsumerProduct $product, CompanyCampaign $campaign, ?array $payload = []): bool
    {
        $phone = Arr::get($payload ?? [], self::FIELD_PHONE, null);

        if (!$phone)
            return false;

        $template = $this->getTemplateForProduct($product);

        return $this->deliverSMS($phone, $template->getBody($product, $campaign), $campaign, $payload);
    }

    /**
     * Returns the SMS template based on the consumer.
     *
     * @param ConsumerProduct $product
     * @return SMSTemplateDataModel
     */
    protected function getTemplateForProduct(ConsumerProduct $product): SMSTemplateDataModel
    {
        return $this->templateStrategy->getSMSTemplateForProduct($product);
    }
}
