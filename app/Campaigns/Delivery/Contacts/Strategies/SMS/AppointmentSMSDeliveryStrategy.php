<?php

namespace App\Campaigns\Delivery\Contacts\Strategies\SMS;

use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\QualityTier as QualityTierEnum;
use App\Models\AppointmentDelivery;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Services\Communication\TwilioCommunicationService;
use App\Services\Odin\Campaigns\CompanyCampaignAppointmentService;
use Exception;
use Illuminate\Support\Arr;

class AppointmentSMSDeliveryStrategy extends BaseSMSDeliveryStrategy
{
    protected ?AppointmentDelivery $appointmentDelivery;

    public function __construct(
        protected CompanyCampaignAppointmentService $appointmentService,
        protected TwilioCommunicationService $smsService,
    ) {}

    /**
     * @inheritDoc
     * @throws Exception
     */
    public function deliver(ConsumerProduct $product, CompanyCampaign $campaign, ?array $payload = []): bool
    {
        if (!$this->getAppointmentDelivery($campaign, $product))
            return false;

        $firstDelivery = Arr::get($payload ?? [], self::FIELD_INITIAL_DELIVERY, false);

        if ($firstDelivery || !$this->appointmentDelivery->company_delivered)
            $this->deliverSMSToCompany($product, $campaign, $payload);

        return $this->appointmentDelivery->company_delivered;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param CompanyCampaign $companyCampaign
     * @return bool
     */
    public function deliverToConsumer(ConsumerProduct $consumerProduct, CompanyCampaign $companyCampaign): bool
    {
        if (!$this->getAppointmentDelivery($companyCampaign, $consumerProduct))
            return false;

        return $this->appointmentDelivery->consumer_delivered_sms
            || $this->deliverSMSToConsumer($consumerProduct, $companyCampaign);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param CompanyCampaign $companyCampaign
     * @param array $payload
     * @return bool
     */
    public function deliverCancellationToCompanyUser(ConsumerProduct $consumerProduct, CompanyCampaign $companyCampaign, array $payload): bool
    {
        $this->getAppointmentDelivery($companyCampaign, $consumerProduct);

        return $this->deliverSMSToCompany($consumerProduct, $companyCampaign, $payload, true);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param CompanyCampaign $companyCampaign
     * @return bool
     */
    public function deliveryCancellationToConsumer(ConsumerProduct $consumerProduct, CompanyCampaign $companyCampaign): bool
    {
        $this->getAppointmentDelivery($companyCampaign, $consumerProduct);

        return $this->deliverSMSToConsumer($consumerProduct, $companyCampaign, true);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param CompanyCampaign $companyCampaign
     * @param array $payload
     * @param ?bool $cancellation
     * @return bool
     */
    protected function deliverSMSToCompany(ConsumerProduct $consumerProduct, CompanyCampaign $companyCampaign, array $payload = [], ?bool $cancellation = false): bool
    {
        $phone = Arr::get($payload, self::FIELD_PHONE, null);
        if (!$phone)
            return false;

        $smsBody = $cancellation
            ? $this->buildCompanyCancellationSMS($consumerProduct, $companyCampaign)
            : $this->buildCompanyNotificationSMS($consumerProduct, $companyCampaign);

        if ($delivered = $this->deliverSMS($phone, $smsBody, $companyCampaign) && !$cancellation)
            $this->appointmentDelivery->update([AppointmentDelivery::FIELD_COMPANY_DELIVERED => true]);

        return $delivered;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param CompanyCampaign $companyCampaign
     * @param ?bool $cancellation
     * @return bool
     */
    protected function deliverSMSToConsumer(ConsumerProduct $consumerProduct, CompanyCampaign $companyCampaign, ?bool $cancellation = false): bool
    {
        $phone = $consumerProduct->consumer->phone;
        if (!$phone)
            return false;

        $smsBody = $cancellation
            ? $this->buildConsumerCancellationSMS($consumerProduct, $companyCampaign)
            : $this->buildConsumerNotificationSMS($consumerProduct, $companyCampaign);

        if ($delivered = $this->deliverSMS($phone, $smsBody, $companyCampaign) && !$cancellation)
            $this->appointmentDelivery->update([
                AppointmentDelivery::FIELD_CONSUMER_DELIVERED_SMS => true,
                AppointmentDelivery::FIELD_CONSUMER_DELIVERED     => true
            ]);

        return $delivered;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param CompanyCampaign $companyCampaign
     * @return string
     */
    protected function buildCompanyNotificationSMS(ConsumerProduct $consumerProduct, CompanyCampaign $companyCampaign): string
    {
        $consumer = $consumerProduct->consumer;
        $industry = $consumerProduct->industryService->industry->name;
        $appointment = $consumerProduct->appointment;

        return sprintf(
            "%s is scheduled for an %s %s appointment on %s with:\n%s\n%s\n%s\n%s",
            $companyCampaign->company->name,
            $appointment->appointment_type->value,
            $industry,
            $this->appointmentService->getFormattedAppointmentDateTime($consumerProduct)->format('m/d/Y g:i A'),
            $consumer->getFullName(),
            $consumer->{Consumer::FIELD_PHONE},
            $consumer->{Consumer::FIELD_EMAIL},
            $consumerProduct->address->getFullAddress()
        );
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param CompanyCampaign $companyCampaign
     * @return string
     */
    protected function buildConsumerNotificationSMS(ConsumerProduct $consumerProduct, CompanyCampaign $companyCampaign): string
    {
        $industry = $consumerProduct->industryService->industry->name;
        $appointment = $consumerProduct->appointment;
        $url = $this->getConsumerAppointmentUrl($consumerProduct);

        return sprintf(
            "%s: A %s representative will %s %s to discuss %s options\nView/Cancel Appointment: %s",
            $this->getSenderName($industry),
            $companyCampaign->company->name,
            $appointment->appointment_type === QualityTierEnum::ONLINE ? "schedule an online meeting for": "visit your property on",
            $this->appointmentService->getFormattedAppointmentDateTime($consumerProduct)->format('m/d/Y g:i A'),
            $industry,
            $url,
        );
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param CompanyCampaign $companyCampaign
     * @return string
     */
    protected function buildCompanyCancellationSMS(ConsumerProduct $consumerProduct, CompanyCampaign $companyCampaign): string
    {
        return sprintf(
            "Appointment for %s on %s was cancelled by the client\nDetails: %s",
            $consumerProduct->consumer->getFullName(),
            $this->appointmentService->getFormattedAppointmentDateTime($consumerProduct)->format('m/d/Y g:i A'),
            $this->getCompanyAppointmentUrl($consumerProduct, $companyCampaign),
        );
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param CompanyCampaign $companyCampaign
     * @return string
     */
    protected function buildConsumerCancellationSMS(ConsumerProduct $consumerProduct, CompanyCampaign $companyCampaign): string
    {
        $senderName = $this->getSenderName($consumerProduct->industryService->industry->name);

        return sprintf(
            "%s: Your appointment with %s was %s",
            $senderName,
            $companyCampaign->company->name,
            'cancelled'
        );
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param CompanyCampaign $companyCampaign
     * @return string
     */
    protected function getCompanyAppointmentUrl(ConsumerProduct $consumerProduct, CompanyCampaign $companyCampaign): string
    {
        return $this->appointmentService->getCompanyUrl($this->getProductAssignment($consumerProduct, $companyCampaign)->id);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @return string
     */
    protected function getConsumerAppointmentUrl(ConsumerProduct $consumerProduct): string
    {
        return $this->appointmentService->getConsumerUrl($this->appointmentDelivery, $consumerProduct);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param CompanyCampaign $companyCampaign
     * @return ProductAssignment
     */
    protected function getProductAssignment(ConsumerProduct $consumerProduct, CompanyCampaign $companyCampaign): ProductAssignment
    {
        /** @var ProductAssignment */
        return $consumerProduct->productAssignment()
            ->where(ProductAssignment::FIELD_COMPANY_ID, $companyCampaign->company_id)
            ->first();
    }

    /**
     * @param CompanyCampaign $companyCampaign
     * @param ConsumerProduct $consumerProduct
     * @return AppointmentDelivery|null
     */
    protected function getAppointmentDelivery(CompanyCampaign $companyCampaign, ConsumerProduct $consumerProduct): ?AppointmentDelivery
    {
        $this->appointmentDelivery = $this->appointmentDelivery
            ?? $this->appointmentService->getAppointmentDelivery($companyCampaign, $this->getProductAssignment($consumerProduct, $companyCampaign));

        return $this->appointmentDelivery;
    }

    /**
     * @param string $industry
     * @return string
     */
    protected function getSenderName(string $industry): string
    {
        return match($industry) {
            IndustryEnum::ROOFING->value => "RoofingCalc",
            IndustryEnum::SOLAR->value   => "SolarReviews",
            default                      => 'FIXR'
        };
    }
}
