<?php

namespace App\Campaigns\Delivery\Contacts\Strategies\SMS;

use App\Campaigns\Delivery\Contacts\Enums\ContactDeliveryLogType;
use App\Campaigns\Delivery\Contacts\Strategies\Email\BaseEmailDeliveryStrategy;
use App\Contracts\Campaigns\Delivery\DeliveryStrategyContract;
use App\Enums\Odin\Product as ProductEnum;
use App\Jobs\TrackContactDeliveryJob;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignContactDeliveryLog;
use App\Models\Odin\ConsumerProduct;
use Illuminate\Support\Arr;

abstract class BaseSMSDeliveryStrategy implements DeliveryStrategyContract
{
    const string FIELD_PHONE             = 'phone';
    const string FIELD_INITIAL_DELIVERY  = 'initial_delivery';

    /**
     * @param CompanyCampaign $campaign
     * @return DeliveryStrategyContract
     */
    public static function makeSMSDeliveryStrategy(CompanyCampaign $campaign): DeliveryStrategyContract
    {
        return match($campaign->product->name) {
            ProductEnum::APPOINTMENT->value => app(AppointmentSMSDeliveryStrategy::class),
            default                         => app(LeadSMSDeliveryStrategy::class),
        };
    }

    /**
     * Attempts to deliver the SMS to the contact.
     *
     * @param string $to
     * @param string $body
     * @param CompanyCampaign $campaign
     * @param array|null $payload
     *
     * @return bool
     */
    protected function deliverSMS(string $to, string $body, CompanyCampaign $campaign, ?array $payload = null): bool
    {
        $error = null;

        try {
            $status = $this->smsService->sendSMS($this->getSMSDeliveryNumber(), $to, $body);
        } catch (\Exception $e) {
            $error = $e->getMessage();
            $status = false;
        }

        $this->trackDelivery($to, $body, $status, $campaign,$payload ?? [], $error);

        return $status;
    }

    /**
     * @param string $to
     * @param string $body
     * @param bool $status
     * @param CompanyCampaign $campaign
     * @param array $payload
     * @param string|null $error
     *
     * @return void
     */
    protected function trackDelivery(string $to, string $body, bool $status, CompanyCampaign $campaign, array $payload, ?string $error): void
    {
        $consumerProductId = Arr::get($payload, BaseEmailDeliveryStrategy::FIELD_CONSUMER_PRODUCT_ID);
        $contactDeliveryModuleId = Arr::get($payload, BaseEmailDeliveryStrategy::FIELD_CONTACT_DELIVERY_MODULE_ID);

        if (!$consumerProductId || !$contactDeliveryModuleId) {
            logger()->error('Failed to create sms delivery tracking');
            return;
        }

        TrackContactDeliveryJob::dispatch(
            $campaign,
            $consumerProductId,
            $contactDeliveryModuleId,
            ContactDeliveryLogType::SMS,
            $status,
            [
                CompanyCampaignContactDeliveryLog::PAYLOAD_TO => $to,
                CompanyCampaignContactDeliveryLog::PAYLOAD_CONTENT => $body,
                CompanyCampaignContactDeliveryLog::PAYLOAD_ERROR => $error
            ]
        );
    }

    /**
     * Returns the delivery phone number.
     *
     * @return string
     */
    protected function getSMSDeliveryNumber(): string
    {
        return config('services.twilio.from_phone_number');
    }

    /**
     * @inheritDoc
     */
    abstract public function deliver(ConsumerProduct $product, CompanyCampaign $campaign, ?array $payload = null): bool;
}
