<?php

namespace App\Campaigns\Delivery\Contacts;

use App\Campaigns\Delivery\Contacts\Strategies\Email\BaseEmailDeliveryStrategy;
use App\Campaigns\Delivery\Contacts\Strategies\SMS\BaseSMSDeliveryStrategy;
use App\Campaigns\Delivery\Proxies\TrackDeliveryStrategyProxy;
use App\Contracts\Campaigns\Delivery\DelivererContract;
use App\Contracts\Campaigns\Delivery\DeliveryStrategyContract;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleContact;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\ConsumerProduct;

class ContactDeliverer implements DelivererContract
{
    public function __construct(
        protected int          $contactId,
        protected bool         $active,
        protected bool         $emailDelivery,
        protected bool         $smsDelivery,
        protected ?CompanyUser $contact = null,
        protected ?int         $contactDeliveryModuleId = null
    ) {}

    /**
     * Returns the company user.
     *
     * @return CompanyUser|null
     */
    protected function getContact(): ?CompanyUser
    {
        if ($this->contact !== null)
            return $this->contact;

        /** @var CompanyUser contact */
        return $this->contact = CompanyUser::query()->find($this->contactId);
    }

    /**
     * @inheritDoc
     */
    public function deliver(ConsumerProduct $product, CompanyCampaign $campaign): bool
    {
        if (!$this->active)
            return false;

        $emailDelivered = $this->emailDelivery && $this->attemptDelivery(
            $product,
            $campaign,
            BaseEmailDeliveryStrategy::makeEmailDeliveryStrategy($campaign),
            [
                BaseEmailDeliveryStrategy::FIELD_EMAIL                      => $this->getContact()?->email,
                BaseEmailDeliveryStrategy::FIELD_COMPANY_USER_NAME          => $this->getContact()?->completeName(),
                BaseEmailDeliveryStrategy::FIELD_INITIAL_DELIVERY           => true,
                BaseEmailDeliveryStrategy::FIELD_CONSUMER_PRODUCT_ID        => $product->id,
                BaseEmailDeliveryStrategy::FIELD_CONTACT_DELIVERY_MODULE_ID => $this->contactDeliveryModuleId,
            ]
        );

        $smsDelivered = $this->smsDelivery && $this->attemptDelivery(
            $product,
            $campaign,
            BaseSMSDeliveryStrategy::makeSMSDeliveryStrategy($campaign),
            [
                BaseSMSDeliveryStrategy::FIELD_PHONE                        => $this->getContact()?->cell_phone,
                BaseSMSDeliveryStrategy::FIELD_INITIAL_DELIVERY             => true,
                BaseEmailDeliveryStrategy::FIELD_CONSUMER_PRODUCT_ID        => $product->id,
                BaseEmailDeliveryStrategy::FIELD_CONTACT_DELIVERY_MODULE_ID => $this->contactDeliveryModuleId,
            ],
        );

        return $emailDelivered || $smsDelivered;
    }

    /**
     * Attempts delivery, and passes through a proxy to track any errors.
     *
     * @param ConsumerProduct $product
     * @param CompanyCampaign $campaign
     * @param DeliveryStrategyContract $strategy
     * @param array|null $payload
     * @return bool
     */
    protected function attemptDelivery(ConsumerProduct $product, CompanyCampaign $campaign, DeliveryStrategyContract $strategy, ?array $payload = []): bool
    {
        return (new TrackDeliveryStrategyProxy($strategy))->deliver($product, $campaign, $payload);
    }

    /**
     * @inheritDoc
     */
    public function save(CompanyCampaign $campaign): bool
    {
        return !!CompanyCampaignDeliveryModuleContact::query()->updateOrCreate([
            CompanyCampaignDeliveryModuleContact::FIELD_CONTACT_ID => $this->contactId,
            CompanyCampaignDeliveryModuleContact::FIELD_MODULE_ID  => $campaign->deliveryModule()->firstOrCreate()->id
        ], [
            CompanyCampaignDeliveryModuleContact::FIELD_CONTACT_ID   => $this->contactId,
            CompanyCampaignDeliveryModuleContact::FIELD_MODULE_ID    => $campaign->deliveryModule()->firstOrCreate()->id,
            CompanyCampaignDeliveryModuleContact::FIELD_ACTIVE       => $this->active,
            CompanyCampaignDeliveryModuleContact::FIELD_EMAIL_ACTIVE => $this->emailDelivery,
            CompanyCampaignDeliveryModuleContact::FIELD_SMS_ACTIVE   => $this->smsDelivery
        ]);
    }

    /**
     * @return bool
     */
    public function isActive(): bool
    {
        return $this->active;
    }
}
