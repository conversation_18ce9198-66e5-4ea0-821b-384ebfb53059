<?php

namespace App\Campaigns\Definitions;

use App\Campaigns\Modules\BaseModule;
use App\Contracts\Campaigns\CampaignDefinitionContract;
use App\Contracts\Campaigns\HasFrontendComponent;
use Illuminate\Support\Collection;

abstract class BaseCampaign implements CampaignDefinitionContract
{
    /**
     * @inheritDoc
     */
    public function frontendModules(): Collection
    {
        return $this->modules()
            ->filter(fn(BaseModule $module) => $module instanceof HasFrontendComponent);
    }
}
