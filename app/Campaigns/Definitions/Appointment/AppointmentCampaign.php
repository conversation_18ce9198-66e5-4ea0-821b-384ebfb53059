<?php

namespace App\Campaigns\Definitions\Appointment;

use App\Campaigns\Definitions\BaseCampaign;
use App\Campaigns\Modules\AppointmentDeliveryModule;
use App\Campaigns\Modules\Bidding\ProductBiddingModule;
use App\Campaigns\Modules\Budget\AppointmentBudgetContainerModule;
use App\Campaigns\Modules\DashboardModule;
use App\Campaigns\Modules\Legacy\LegacyModule;
use App\Campaigns\Modules\LocationModule;
use App\Campaigns\Modules\RejectionModule;
use Illuminate\Support\Collection;

class AppointmentCampaign extends BaseCampaign
{
    /**
     * @inheritDoc
     */
    public function modules(): Collection
    {
        return collect([
            app(LocationModule::class),
            app(RejectionModule::class),
            app(AppointmentBudgetContainerModule::class),
            app(ProductBiddingModule::class),
            app(AppointmentDeliveryModule::class),
            app(DashboardModule::class),
            app(LegacyModule::class),
        ]);
    }
}
