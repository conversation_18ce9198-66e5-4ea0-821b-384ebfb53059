<?php

namespace App\Campaigns\Definitions\DirectLeads;

use App\Campaigns\Definitions\BaseCampaign;
use App\Campaigns\Modules\Bidding\ProductBiddingModule;
use App\Campaigns\Modules\Budget\DirectLeadsBudgetContainerModule;
use App\Campaigns\Modules\Budget\LeadBudgetContainerModule;
use App\Campaigns\Modules\DashboardModule;
use App\Campaigns\Modules\DeliveryModule;
use App\Campaigns\Modules\Legacy\LegacyModule;
use App\Campaigns\Modules\LocationModule;
use App\Campaigns\Modules\Product\ProductModule;
use App\Campaigns\Modules\RejectionModule;
use Illuminate\Support\Collection;

class DirectLeadsCampaign extends BaseCampaign
{

    /**
     * @inheritDoc
     */
    #[\Override] public function modules(): Collection
    {
        return collect([
            app(LocationModule::class),
            app(ProductModule::class),
            app(RejectionModule::class),
            app(DirectLeadsBudgetContainerModule::class),
            app(ProductBiddingModule::class),
            app(DeliveryModule::class),
            app(DashboardModule::class),
            app(LegacyModule::class),
        ]);
    }
}
