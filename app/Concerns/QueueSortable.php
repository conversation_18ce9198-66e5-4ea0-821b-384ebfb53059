<?php

namespace App\Concerns;

use App\Contracts\QueueSorterContract;
use App\Enums\QueueSortable\QueueName;
use App\Models\Odin\Company;
use App\Models\QueueSortPosition;
use App\Models\User;
use App\Services\QueueSortable\Sorters\ExistingCompaniesQueueSorter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

trait QueueSortable
{
    const int INSERT_BATCH_SIZE = 5_000;

    public static function sort(QueueName $queueName): void
    {
        $sorter = self::getSorterByClassAndQueue(self::class, $queueName);
        if (!$sorter)
            return;

        QueueSortPosition::query()
            ->where(QueueSortPosition::FIELD_QUEUE_NAME, $queueName)
            ->where(QueueSortPosition::FIELD_CLASS_NAME, self::class)
            ->whereNull(QueueSortPosition::FIELD_RELEASED_AT)
            ->delete();

        $releasedRows = QueueSortPosition::query()
            ->where(QueueSortPosition::FIELD_QUEUE_NAME, $queueName)
            ->where(QueueSortPosition::FIELD_CLASS_NAME, self::class)
            ->orderBy(QueueSortPosition::FIELD_RELEASED_AT)
            ->get();

        $sortedModelIds            = array_values(array_unique($sorter->getSortedIds()));
        $sortedModelIdsNotReleased = array_diff($sortedModelIds, $releasedRows->pluck(QueueSortPosition::FIELD_MODEL_ID)->toArray());

        $ordinalValue = 1;
        $totalInserts = count($sortedModelIdsNotReleased);

        $batches = array_chunk($sortedModelIdsNotReleased, self::INSERT_BATCH_SIZE);
        foreach ($batches as $batch) {
            $inserts = [];
            foreach ($batch as $modelId) {
                $inserts[] = [
                    QueueSortPosition::FIELD_QUEUE_NAME    => $queueName,
                    QueueSortPosition::FIELD_CLASS_NAME    => self::class,
                    QueueSortPosition::FIELD_MODEL_ID      => $modelId,
                    QueueSortPosition::FIELD_ORDINAL_VALUE => $ordinalValue++,
                ];
            }
            QueueSortPosition::insert($inserts);
        }

        DB::beginTransaction();
        /** @var QueueSortPosition $position */
        foreach ($releasedRows as $position) {
            $position->ordinal_value = $ordinalValue++;
            $position->save();
        }
        DB::commit();
    }

    /**
     * This is where we define the sorting logic for the queue/class combination
     *
     * @param string $class
     * @param QueueName $queueName
     * @return QueueSorterContract|null
     */
    public static function getSorterByClassAndQueue(string $class, QueueName $queueName): ?QueueSorterContract
    {
        if ($class === Company::class)
            return match ($queueName) {
                QueueName::EXISTING_COMPANIES => new ExistingCompaniesQueueSorter(),
            };

        return null;
    }

    /**
     * @param QueueName $queueName
     * @return Builder
     */
    public static function queueSortedQuery(QueueName $queueName): Builder
    {
        return self::query()
            ->select([self::TABLE . '.*', QueueSortPosition::TABLE . '.' . QueueSortPosition::FIELD_ORDINAL_VALUE . ' as queue_ordinal_value'])
            ->leftJoin(QueueSortPosition::TABLE, function ($join) use ($queueName) {
                $join->on(QueueSortPosition::TABLE . '.' . QueueSortPosition::FIELD_MODEL_ID, '=', self::TABLE . '.' . self::FIELD_ID)
                    ->where(QueueSortPosition::TABLE . '.' . QueueSortPosition::FIELD_QUEUE_NAME, $queueName)
                    ->where(QueueSortPosition::TABLE . '.' . QueueSortPosition::FIELD_CLASS_NAME, self::class);
            })
            ->whereNotNull(QueueSortPosition::TABLE . '.' . QueueSortPosition::FIELD_ORDINAL_VALUE)
            ->orderBy('queue_ordinal_value');
    }

    /**
     * @param QueueName $queueName
     * @param User|null $user
     * @return void
     */
    public function releasedBackToQueue(QueueName $queueName, ?User $user = null): void
    {
        $maxOrdinalValue = QueueSortPosition::max(QueueSortPosition::FIELD_ORDINAL_VALUE);

        QueueSortPosition::updateOrCreate([
            QueueSortPosition::FIELD_QUEUE_NAME => $queueName,
            QueueSortPosition::FIELD_CLASS_NAME => self::class,
            QueueSortPosition::FIELD_MODEL_ID   => $this->id,
        ], [
            QueueSortPosition::FIELD_RELEASED_AT         => now(),
            QueueSortPosition::FIELD_RELEASED_BY_USER_ID => $user?->id,
            QueueSortPosition::FIELD_ORDINAL_VALUE       => $maxOrdinalValue + 1,
        ]);
    }
}
