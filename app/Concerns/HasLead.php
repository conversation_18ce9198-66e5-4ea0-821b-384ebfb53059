<?php

namespace App\Concerns;

use App\Models\Legacy\EloquentQuote;
use App\Repositories\Legacy\QuoteRepository;
use Illuminate\Contracts\Container\BindingResolutionException;

trait HasLead
{
    /** @var string $leadReference */
    public string $leadReference;

    /**
     * @return EloquentQuote|null
     * @throws BindingResolutionException
     */
    public function getLead(): ?EloquentQuote
    {
        /** @var QuoteRepository $repository */
        $repository = app()->make(QuoteRepository::class);

        return $repository->findByReference($this->leadReference);
    }

}
