<?php

namespace App\Concerns;

use App\Models\LeadProcessor;
use App\Models\Legacy\EloquentQuote;
use App\Repositories\Legacy\QuoteRepository;
use Illuminate\Contracts\Container\BindingResolutionException;

trait HasLeadProcessingData
{
    /** @var string $leadReference */
    public string $leadReference;

    /** @var int $processorId */
    public int $processorId;

    /**
     * @return EloquentQuote|null
     * @throws BindingResolutionException
     */
    public function getLead(): ?EloquentQuote
    {
        /** @var QuoteRepository $repository */
        $repository = app()->make(QuoteRepository::class);

        return $repository->findByReference($this->leadReference);
    }

    /**
     * @return LeadProcessor|null
     */
    public function getLeadProcessor(): ?LeadProcessor
    {
        return LeadProcessor::withTrashed()
            ->findOrFail($this->processorId);
    }
}
