<?php

namespace App\Concerns;

use App\Models\Odin\ProductAssignment;
use App\Repositories\Odin\ProductAssignmentRepository;
use Illuminate\Contracts\Container\BindingResolutionException;

trait HasProductAssignmentData
{
    /** @var int $productAssignmentId */
    public int $productAssignmentId;

    /**
     * @return ProductAssignment|null
     * @throws BindingResolutionException
     */
    public function getProductAssignment(): ?ProductAssignment
    {
        /** @var ProductAssignmentRepository $repository */
        $repository = app()->make(ProductAssignmentRepository::class);

        return $repository->findByIdOrFail($this->productAssignmentId);
    }
}
