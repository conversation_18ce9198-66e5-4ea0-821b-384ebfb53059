<?php

namespace App\Enums;

enum TargetRelation:string
{
    case COMPANY_ACCOUNT_MANAGER      = 'company-account-manager';
    case COMPANY_RELATIONSHIP_MANAGER = 'company-relationship-manager';
    case COMPANY_SUCCESS_MANAGER      = 'company-success-manager';
    case SUPPORT_OFFICER_ROUND_ROBIN  = 'support-officer-round-robin';
    case HUNTER_ROUND_ROBIN           = 'hunter-round-robin';
    case SALES_MANAGER                = 'sales-manager';
    case USER_ACTIONING               = 'user-actioning';
    case SALES_TOOL_ROUND_ROBIN       = 'sales-tool-round-robin';

    /**
     * @param string $getFrom
     * @return TargetRelation
     */
    static public function get(string $getFrom): TargetRelation
    {
        return match ($getFrom) {
            TargetRelation::COMPANY_ACCOUNT_MANAGER->value => TargetRelation::COMPANY_RELATIONSHIP_MANAGER,
            default                                        => TargetRelation::from($getFrom),
        };
    }
}
