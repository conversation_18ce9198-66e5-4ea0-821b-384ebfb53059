<?php

namespace App\Enums\Advertising;

use Exception;

enum AdvertisingPlatform: string
{
    case GOOGLE = 'google_ads';
    case MICROSOFT = 'microsoft_ads';
    case META = 'meta_ads';

    /**
     * @return array
     */
    public static function all(): array
    {
        $platforms = [];

        foreach(self::cases() as $case) {
            $platforms[$case->value] = $case;
        }

        return $platforms;
    }

    /**
     * @param string $platformKey
     * @return int
     * @throws Exception
     */
    public static function getInteger(string $platformKey): int
    {
        return match($platformKey) {
            self::GOOGLE->value => 1,
            self::META->value => 2,
            self::MICROSOFT->value => 3,
            default => throw new Exception("Invalid platform key: $platformKey")
        };
    }

    /**
     * @param string $platformKey
     * @return AdvertisingPlatform
     * @throws Exception
     */
    public static function fromKey(string $platformKey): AdvertisingPlatform
    {
        return match($platformKey) {
            self::GOOGLE->value => self::GOOGLE,
            self::META->value => self::META,
            self::MICROSOFT->value => self::MICROSOFT,
            default => throw new Exception("Invalid platform key: $platformKey")
        };
    }

    /**
     * @param string $platformKey
     * @return string
     * @throws Exception
     */
    public static function displayName(string $platformKey): string
    {
        return match($platformKey) {
            self::GOOGLE->value => "Google Ads",
            self::MICROSOFT->value => "Microsoft Ads",
            self::META->value => "Meta Ads",
            default => throw new Exception("Invalid platform key: $platformKey")
        };
    }

    /**
     * @param string $platformKey
     * @return array
     * @throws Exception
     */
    public static function hierarchyNames(string $platformKey): array
    {
        return match($platformKey) {
            self::GOOGLE->value, self::MICROSOFT->value => [
                "accounts",
                "campaigns"
            ],
            self::META->value => [
                "campaigns",
                "ad sets"
            ],
            default => throw new Exception("Invalid platform key: $platformKey")
        };
    }

    /**
     * @return array
     * @throws Exception
     */
    public static function getAsKeyValueSelectArray(): array
    {
        $result = [];

        foreach (self::cases() as $platform) {
            $result[self::displayName($platform->value)] = $platform->value;
        }

        return $result;
    }
}
