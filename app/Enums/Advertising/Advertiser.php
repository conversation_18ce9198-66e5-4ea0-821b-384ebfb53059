<?php

namespace App\Enums\Advertising;

use Exception;

enum Advertiser: int
{
    case GABE = 1;
    case WADE = 2;
    case THOMAS = 3;
    case ANDY = 4;
    case WILL = 5;
    case GABE_NEW = 6;
    case HARVEY = 7;

    public function getDisplayName(): string
    {
        return match($this) {
            self::GABE      => '<PERSON> Old',
            self::WADE      => '<PERSON>',
            self::THOMAS    => '<PERSON>',
            self::ANDY      => 'Andy',
            self::WILL      => 'Will',
            self::GABE_NEW  => 'Gabe New',
            self::HARVEY    => 'Harvey',
        };
    }

    /**
     * @return int
     */
    public function getAffiliateCode(): int
    {
        return match($this) {
            self::GABE      => 4713,
            self::WADE      => 8910,
            self::THOMAS    => 209732,
            self::ANDY      => 209731,
            self::WILL      => 209744,
            self::GABE_NEW  => 209733,
            self::HARVEY    => 209783,
        };
    }

    /**
     * @param string $key
     * @return Advertiser
     * @throws Exception
     */
    public static function from<PERSON>ey(string $key): Advertiser
    {
        return match(strtolower($key)) {
            'gabe'      => self::GABE,
            'wade'      => self::WADE,
            'thomas'    => self::THOMAS,
            'andy'      => self::ANDY,
            'will'      => self::WILL,
            'gabe_new'  => self::GABE_NEW,
            'harvey'    => self::HARVEY,
            default     => throw new Exception("Invalid advertiser key: $key")
        };
    }

    /**
     * @param Advertiser $advertiser
     * @return string
     */
    public static function getKey(self $advertiser): string
    {
        return match($advertiser) {
            self::GABE      => 'gabe',
            self::WADE      => 'wade',
            self::THOMAS    => 'thomas',
            self::ANDY      => 'andy',
            self::WILL      => 'will',
            self::GABE_NEW  => 'gabe_new',
            self::HARVEY    => 'harvey',
        };
    }

    /**
     * @return array
     */
    public static function displayNames(): array
    {
        $output = [];
        foreach(self::cases() as $case) {
            $output[$case->value] = $case->getDisplayName();
        }

        return $output;
    }

    /**
     * @return array
     */
    public static function advertisersByKey(): array
    {
        $advertisers = [];
        foreach(Advertiser::cases() as $advertiser) {
            $advertisers[Advertiser::getKey($advertiser)] = $advertiser->getDisplayName();
        }

        return $advertisers;
    }

    /**
     * @return array
     */
    public static function getAsKeyValueSelectArray(): array
    {
        $result = [];

        foreach (self::cases() as $advertiser) {
            $result[$advertiser->getDisplayName()] = self::getKey($advertiser);
        }

        return $result;
    }

    /**
     * @return array
     */
    public static function getAsNameIntArray(): array
    {
        $result = [];

        foreach (self::cases() as $advertiser) {
            $result[$advertiser->getDisplayName()] = $advertiser->value;
        }

        return $result;
    }

    /**
     * @return array
     */
    public static function getAsIdNameArray(): array
    {
        $result = [];

        foreach (self::cases() as $advertiser) {
            $result[] = [
                'id'    => $advertiser->value,
                'name'  => $advertiser->getDisplayName(),
            ];
        }

        return $result;
    }
}
