<?php

namespace App\Enums\Advertising;

use App\Enums\Locations\LocationType;
use App\Models\Locations\USLocation;
use Exception;

enum AdvertisingResolution: string
{
    case COUNTY = 'county';
    case STATE  = 'state';

    /**
     * @return array
     */
    public static function all(): array
    {
        $resolutions = [];
        foreach(self::cases() as $case) {
            $resolutions[$case->value] = $case;
        }
        return $resolutions;
    }

    /**
     * @param string $key
     * @return int
     * @throws Exception
     */
    public static function getInteger(string $key): int
    {
        return match($key) {
            self::COUNTY->value => 1,
            self::STATE->value  => 2,
            default => throw new Exception("Invalid resolution key: $key")
        };
    }

    /**
     * @param AdvertisingResolution $resolution
     * @return string
     * @throws Exception
     */
    public static function getGoogleAdQuerySegment(AdvertisingResolution $resolution): string
    {
        return match($resolution) {
            self::COUNTY => 'segments.geo_target_county',
            self::STATE  => 'segments.geo_target_state',
            default => throw new Exception("Invalid resolution key: $resolution->value")
        };
    }

    /**
     * @param string $key
     * @return AdvertisingResolution
     * @throws Exception
     */
    public static function fromKey(string $key): AdvertisingResolution
    {
        return match($key) {
            self::COUNTY->value => self::COUNTY,
            self::STATE->value => self::STATE,
            default => throw new Exception("Invalid resolution key: $key")
        };
    }

    /**
     * @param string $key
     * @return string
     * @throws Exception
     */
    public static function displayName(string $key): string
    {
        return match($key) {
            self::COUNTY->value => "County",
            self::STATE->value  => "State",
            default => throw new Exception("Invalid resolution key: $key")
        };
    }

    /**
     * @return string
     */
    public function locationType(): LocationType
    {
        return match($this) {
            self::COUNTY    => USLocation::TYPE_COUNTY,
            self::STATE     => USLocation::TYPE_STATE,
        };
    }
}
