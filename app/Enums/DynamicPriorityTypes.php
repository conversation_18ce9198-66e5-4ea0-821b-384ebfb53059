<?php

namespace App\Enums;

use App\Contracts\DynamicPriorityCalculationServiceContract;
use App\Services\DynamicPriority\LeadHunterPriorityCalculationService;
use App\Services\DynamicPriority\RandomPriorityCalculationService;
use Illuminate\Contracts\Container\BindingResolutionException;

enum DynamicPriorityTypes: int
{
    case RANDOM = 1;
    case LEAD_HUNTER_PRIORITY = 2;

    /**
     * @return DynamicPriorityCalculationServiceContract
     * @throws BindingResolutionException
     */
    public function getService(): DynamicPriorityCalculationServiceContract
    {
        return match ($this) {
            self::RANDOM => app()->make(RandomPriorityCalculationService::class),
            self::LEAD_HUNTER_PRIORITY => app()->make(LeadHunterPriorityCalculationService::class),
            default => null
        };
    }

    /**
     * @return string[]
     */
    public static function getMapping(): array
    {
        return  [
            self::RANDOM->value => 'Random',
            self::LEAD_HUNTER_PRIORITY->value => 'Lead Hunter Priority'
        ];
    }
}
