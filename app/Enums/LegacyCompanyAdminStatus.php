<?php

namespace App\Enums;

use App\Models\Legacy\EloquentCompany;
use App\Models\Odin\Company;

enum LegacyCompanyAdminStatus: int
{
    case PART_SUSPENDED = 1;
    case SUSPENDED      = 2;
    case COLLECTIONS    = 3;
    case ARCHIVED       = 4;
    case ADMIN_LOCKED   = 5;

    /**
     * @param LegacyCompanyAdminStatus $adminStatus
     * @param bool|null $archived
     * @param string|null $status
     * @param bool|null $adminLocked
     * @return bool
     */
    public static function is(
        self    $adminStatus,
        ?bool   $archived,
        ?string $status,
        ?bool   $adminLocked
    ): bool
    {
        return $adminStatus->value === self::get(
                $archived,
                $status,
                $adminLocked
            );
    }

    /**
     * @param bool|null $archived
     * @param string|int|null $status
     * @param bool|null $adminLocked
     * @return int|null
     */
    public static function get(?bool $archived, string|int|null $status, ?bool $adminLocked): ?int
    {
        if ($archived) {
            return self::ARCHIVED->value;
        } else if ($status === EloquentCompany::STATUS_COLLECTION) {
            return self::COLLECTIONS->value;
        } else if ($status === Company::ADMIN_STATUS_ALL_SUSPENDED)  {
            return self::SUSPENDED->value;
        } else if ($adminLocked) {
            return self::ADMIN_LOCKED->value;
        }

        return null;
    }
}
