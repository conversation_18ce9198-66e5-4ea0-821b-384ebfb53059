<?php

namespace App\Enums;

enum LeadRefundItemChargeRefundStatus: string
{
    case STRIPE_ERROR  = 'stripe_error';
    case REFUND_ISSUED = 'refund_issued';
    case REFUNDED      = 'refunded';


    public function getName(): string
    {
        return match ($this) {
            self::STRIPE_ERROR  => 'Stripe Error',
            self::REFUND_ISSUED => 'Refund Issued',
            self::REFUNDED      => 'Refunded',
            default             => $this->value
        };
    }
}
