<?php

namespace App\Enums\Odin;

enum OriginDomain: string
{
    case SOLAR_REVIEWS = 'www.solarreviews.com';
    case SOLAR_ESTIMATE = 'www.solar-estimate.org';
    case ROOFING_CALCULATOR = 'www.roofingcalculator.com';
    case SOLAR_POWER_ROCKS = 'www.solarpowerrocks.com';
    case SUN_NUMBER = 'www.sunnumber.com';
    case CUT_MY_BILL = 'www.cutmybill.com';
    case FIXR = 'www.fixr.com';
    case MYSOLAR = 'www.mysolar.com';
    case SOLAR_QUOTES = 'www.solar-quotes.com';
    case ROOFING_ESTIMATE = 'www.roofing-estimate.org';
    case KITCHEN_ESTIMATE = 'www.kitchen-estimate.org';
    case BATHROOM_ESTIMATE = 'www.bathroom-estimate.org';

    /**
     * @return string[]
     */
    public static function getAbbreviations(): array
    {
        return [
            self::SOLAR_REVIEWS->value => 'sr',
            self::SOLAR_ESTIMATE->value => 'se',
            self::ROOFING_CALCULATOR->value => 'rc',
            self::SOLAR_POWER_ROCKS->value => 'spr',
            self::SUN_NUMBER->value => 'sn',
            self::CUT_MY_BILL->value => 'cmb',
            self::FIXR->value => 'fixr',
            self::MYSOLAR->value => 'ws',
            self::SOLAR_QUOTES->value => 'sq',
            self::ROOFING_ESTIMATE->value => 're',
            self::KITCHEN_ESTIMATE->value => 'ke',
            self::BATHROOM_ESTIMATE->value => 'be',
        ];
    }

    public function getAbbreviation(): string
    {
        return match ($this) {
            self::SOLAR_REVIEWS => 'sr',
            self::SOLAR_ESTIMATE => 'se',
            self::ROOFING_CALCULATOR => 'rc',
            self::SOLAR_POWER_ROCKS => 'spr',
            self::SUN_NUMBER => 'sn',
            self::CUT_MY_BILL => 'cmb',
            self::FIXR => 'fixr',
            self::MYSOLAR => 'ws',
            self::SOLAR_QUOTES => 'sq',
            self::ROOFING_ESTIMATE => 're',
            self::KITCHEN_ESTIMATE => 'ke',
            self::BATHROOM_ESTIMATE => 'be',
        };
    }
}
