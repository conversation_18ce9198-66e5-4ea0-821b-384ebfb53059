<?php

namespace App\Enums\Odin;

use App\Models\Odin\Product as ProductModel;

enum Product: string
{
    case LEAD = 'Lead';
    case APPOINTMENT = 'Appointment';
    case DIRECT_LEADS = 'Direct Leads';

    public static function fromLowerCase(string $case): Product
    {
        return match ($case) {
            'lead' => self::LEAD,
            'appointment' => self::APPOINTMENT,
            'direct leads' => self::DIRECT_LEADS,
            default => throw new \RuntimeException("No product for key: " . $case)
        };
    }

    /**
     * @return ProductModel
     */
    public function model(): ProductModel
    {
        /** @var ProductModel */
        return ProductModel::query()
            ->where(ProductModel::FIELD_NAME, $this->value)
            ->first();
    }
}
