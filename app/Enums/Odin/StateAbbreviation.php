<?php

namespace App\Enums\Odin;

use App\EnumAttributes\Concerns\GetAttributesTrait;
use App\EnumAttributes\Description;
use Exception;

enum StateAbbreviation: string
{
    use GetAttributesTrait;

    #[Description('Alaska')]
    case AK = 'AK';
    #[Description('Alabama')]
    case AL = 'AL';
    #[Description('Arkansas')]
    case AR = 'AR';
    #[Description('Arizona')]
    case AZ = 'AZ';
    #[Description('California')]
    case CA = 'CA';
    #[Description('Colorado')]
    case CO = 'CO';
    #[Description('Connecticut')]
    case CT = 'CT';
    #[Description('District of Columbia')]
    case DC = 'DC';
    #[Description('Delaware')]
    case DE = 'DE';
    #[Description('Florida')]
    case FL = 'FL';
    #[Description('Georgia')]
    case GA = 'GA';
    #[Description('Hawaii')]
    case HI = 'HI';
    #[Description('Iowa')]
    case IA = 'IA';
    #[Description('Idaho')]
    case ID = 'ID';
    #[Description('Illinois')]
    case IL = 'IL';
    #[Description('Indiana')]
    case IN = 'IN';
    #[Description('Kansas')]
    case KS = 'KS';
    #[Description('Kentucky')]
    case KY = 'KY';
    #[Description('Louisiana')]
    case LA = 'LA';
    #[Description('Massachusetts')]
    case MA = 'MA';
    #[Description('Maryland')]
    case MD = 'MD';
    #[Description('Maine')]
    case ME = 'ME';
    #[Description('Michigan')]
    case MI = 'MI';
    #[Description('Minnesota')]
    case MN = 'MN';
    #[Description('Missouri')]
    case MO = 'MO';
    #[Description('Mississippi')]
    case MS = 'MS';
    #[Description('Montana')]
    case MT = 'MT';
    #[Description('North Carolina')]
    case NC = 'NC';
    #[Description('North Dakota')]
    case ND = 'ND';
    #[Description('Nebraska')]
    case NE = 'NE';
    #[Description('New Hampshire')]
    case NH = 'NH';
    #[Description('New Jersey')]
    case NJ = 'NJ';
    #[Description('New Mexico')]
    case NM = 'NM';
    #[Description('Nevada')]
    case NV = 'NV';
    #[Description('New York')]
    case NY = 'NY';
    #[Description('Ohio')]
    case OH = 'OH';
    #[Description('Oklahoma')]
    case OK = 'OK';
    #[Description('Oregon')]
    case OR = 'OR';
    #[Description('Pennsylvania')]
    case PA = 'PA';
    #[Description('Puerto Rico')]
    case PR = 'PR';
    #[Description('Rhode Island')]
    case RI = 'RI';
    #[Description('South Carolina')]
    case SC = 'SC';
    #[Description('South Dakota')]
    case SD = 'SD';
    #[Description('Tennessee')]
    case TN = 'TN';
    #[Description('Texas')]
    case TX = 'TX';
    #[Description('Utah')]
    case UT = 'UT';
    #[Description('Virginia')]
    case VA = 'VA';
    #[Description('Vermont')]
    case VT = 'VT';
    #[Description('Washington')]
    case WA = 'WA';
    #[Description('Wisconsin')]
    case WI = 'WI';
    #[Description('West Virginia')]
    case WV = 'WV';
    #[Description('Wyoming')]
    case WY = 'WY';

    /**
     * Takes a state abbreviation that should match an enum and returns that state's timezone
     */
    public static function timeZone(string $stateAbb, StateAbbreviation $default = StateAbbreviation::UT): string
    {
        $stateAbb = StateAbbreviation::tryFrom($stateAbb) ?: $default;

        return match ($stateAbb) {
            self::AK                                                                                                                                                                                     => 'America/Anchorage',
            self::ID                                                                                                                                                                                     => 'America/Boise',
            self::AL, self::AR, self::IL, self::WI, self::TX, self::TN, self::SD, self::IA, self::KS, self::LA, self::NE, self::MO, self::MS, self::MN, self::OK                                         => 'America/Chicago',
            self::CO, self::MT, self::NM, self::UT, self::WY                                                                                                                                             => 'America/Denver',
            self::MI                                                                                                                                                                                     => 'America/Detroit',
            self::IN                                                                                                                                                                                     => 'America/Indiana/Indianapolis',
            self::KY                                                                                                                                                                                     => 'America/Kentucky/Louisville',
            self::CA, self::NV, self::OR, self::WA                                                                                                                                                       => 'America/Los_Angeles',
            self::CT, self::DE, self::FL, self::GA, self::ME, self::MD, self::MA, self::NH, self::NJ, self::NY, self::NC, self::OH, self::PA, self::RI, self::SC, self::VT, self::VA, self::DC, self::WV => 'America/New_York',
            self::ND                                                                                                                                                                                     => 'America/North_Dakota/Center',
            self::AZ                                                                                                                                                                                     => 'America/Phoenix',
            self::HI                                                                                                                                                                                     => 'Pacific/Honolulu',
            self::PR                                                                                                                                                                                     => 'America/Puerto_Rico',
        };

    }
}
