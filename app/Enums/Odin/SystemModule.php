<?php

namespace App\Enums\Odin;

use App\Enums\Odin\SytemModules\MissedLeadsFeatures;

enum SystemModule: string
{
    case MISSED_LEADS = 'missed_leads';

    public static function getValues(): \Illuminate\Support\Collection
    {
        return collect(self::cases())->map(fn($c) => $c->value);
    }

    public function getDescription(): string
    {
        return match ($this) {
            self::MISSED_LEADS => 'Missed Leads'
        };
    }

    public function getFeatures(): array
    {
        return match ($this) {
            self::MISSED_LEADS => MissedLeadsFeatures::cases(),
        };
    }
}
