<?php

namespace App\Enums\Odin;

enum SolarConfigurableFields: string
{
    case SYSTEM_TYPE       = 'system_type';
    case SYSTEM_SIZE       = 'system_size';
    case SYSTEM_SIZE_OTHER = 'system_size_other';
    case ELECTRIC_COST     = 'electric_cost';
    case PANEL_TIER        = 'panel_tier';
    case UTILITY_NAME      = 'utility_name';
    case UTILITY_ID        = 'utility_id';
    case ROOF_SHADE        = 'roof_shading';

    /** these are optional side that will be use to Automatically Qualify leads */
    case HOME_TYPE                  = 'home_type';
    case CURRENT_SOLAR              = 'current_solar';
    case TREE_REMOVAL               = 'tree_removal';
    case SOLAR_NEEDS                = 'solar_needs';

    case YEAR_STARTED_BUSINESS             = 'year_started_business';
    case YEAR_STARTED_SOLAR                = 'year_started_solar';
    case FAMILY_BUSINESS                   = 'family_business';
    case ENABLE_WATCHDOG_COMPLIANCE_LINKS  = 'enable_watchdog_compliance_links';
    case ALLOW_LEAD_SALES_WITHOUT_CC       = 'allow_lead_sales_without_cc';
    case NEVER_EXCEED_BUDGET               = 'never_exceed_budget';
    case DISALLOW_RANKING                  = 'disallow_ranking';
    case REVENUE_IN_THOUSANDS              = 'revenue_in_thousands';
    case EMPLOYEE_COUNT                    = 'employee_count';
    case OFFICE_IN_USA                     = 'office_in_usa';
    case ENABLE_LEAD_COMPLIANCE_JORNAYA    = 'enable_lead_compliance_jornaya';
    case BUYING_LEADS                      = 'buying_leads';
}
