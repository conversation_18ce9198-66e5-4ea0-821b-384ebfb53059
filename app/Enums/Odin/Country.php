<?php

namespace App\Enums\Odin;

enum Country: string
{
    //county names
    const NAME_UNITED_STATES = 'United States';

    case AFGHANISTAN = 'AF';
    case AALAND_ISLANDS = 'AX';
    case ALBANIA = 'AL';
    case ALGERIA = 'DZ';
    case AMERICAN_SAMOA = 'AS';
    case ANDORRA = 'AD';
    case ANGOLA = 'AO';
    case ANGUILLA = 'AI';
    case ANTARCTICA = 'AQ';
    case ANTIGUA_BARBUDA = 'AG';
    case ARGENTINA = 'AR';
    case ARMENIA = 'AM';
    case ARUBA = 'AW';
    case AUSTRALIA = 'AU';
    case AUSTRIA = 'AT';
    case AZERBAIJAN = 'AZ';
    case BAHAMAS = 'BS';
    case BAHRAIN = 'BH';
    case BANGLADESH = 'BD';
    case BARBADOS = 'BB';
    case BELARUS = 'BY';
    case BELGIUM = 'BE';
    case BELIZE = 'BZ';
    case BENIN = 'BJ';
    case BERMUDA = 'BM';
    case BHUTAN = 'BT';
    case BOLIVIA = 'BO';
    case BOSNIA_HERZEGOVINA = 'BA';
    case BOTSWANA = 'BW';
    case BOUVET_ISLAND = 'BV';
    case BRAZIL = 'BR';
    case BRITISH_INDIAN_OCEAN_TERRITORY = 'IO';
    case BRITISH_VIRGIN_ISLANDS = 'VG';
    case BRUNEI = 'BN';
    case BULGARIA = 'BG';
    case BURKINA_FASO = 'BF';
    case BURUNDI = 'BI';
    case CAMBODIA = 'KH';
    case CAMEROON = 'CM';
    case CANADA = 'CA';
    case CAPE_VERDE = 'CV';
    case CARIBBEAN_NETHERLANDS = 'BQ';
    case CAYMAN_ISLANDS = 'KY';
    case CENTRAL_AFRICAN_REPUBLIC = 'CF';
    case CHAD = 'TD';
    case CHILE = 'CL';
    case CHINA = 'CN';
    case CHRISTMAS_ISLAND = 'CX';
    case COCOS_KEELING_ISLANDS = 'CC';
    case COLOMBIA = 'CO';
    case COMOROS = 'KM';
    case CONGO_BRAZZAVILLE = 'CG';
    case CONGO_KINSHASA = 'CD';
    case COOK_ISLANDS = 'CK';
    case COSTA_RICA = 'CR';
    case COTE_D_IVOIRE = 'CI';
    case CROATIA = 'HR';
    case CUBA = 'CU';
    case CURACAO = 'CW';
    case CYPRUS = 'CY';
    case CZECHIA = 'CZ';
    case DENMARK = 'DK';
    case DJIBOUTI = 'DJ';
    case DOMINICA = 'DM';
    case DOMINICAN_REPUBLIC = 'DO';
    case ECUADOR = 'EC';
    case EGYPT = 'EG';
    case EL_SALVADOR = 'SV';
    case EQUATORIAL_GUINEA = 'GQ';
    case ERITREA = 'ER';
    case ESTONIA = 'EE';
    case ESWATINI = 'SZ';
    case ETHIOPIA = 'ET';
    case FALKLAND_ISLANDS = 'FK';
    case FAROE_ISLANDS = 'FO';
    case FIJI = 'FJ';
    case FINLAND = 'FI';
    case FRANCE = 'FR';
    case FRENCH_GUIANA = 'GF';
    case FRENCH_POLYNESIA = 'PF';
    case FRENCH_SOUTHERN_TERRITORIES = 'TF';
    case GABON = 'GA';
    case GAMBIA = 'GM';
    case GEORGIA = 'GE';
    case GERMANY = 'DE';
    case GHANA = 'GH';
    case GIBRALTAR = 'GI';
    case GREECE = 'GR';
    case GREENLAND = 'GL';
    case GRENADA = 'GD';
    case GUADELOUPE = 'GP';
    case GUAM = 'GU';
    case GUATEMALA = 'GT';
    case GUERNSEY = 'GG';
    case GUINEA = 'GN';
    case GUINEA_BISSAU = 'GW';
    case GUYANA = 'GY';
    case HAITI = 'HT';
    case HEARD_MCDONALD_ISLANDS = 'HM';
    case HONDURAS = 'HN';
    case HONG_KONG_SAR_CHINA = 'HK';
    case HUNGARY = 'HU';
    case ICELAND = 'IS';
    case INDIA = 'IN';
    case INDONESIA = 'ID';
    case IRAN = 'IR';
    case IRAQ = 'IQ';
    case IRELAND = 'IE';
    case ISLE_OF_MAN = 'IM';
    case ISRAEL = 'IL';
    case ITALY = 'IT';
    case JAMAICA = 'JM';
    case JAPAN = 'JP';
    case JERSEY = 'JE';
    case JORDAN = 'JO';
    case KAZAKHSTAN = 'KZ';
    case KENYA = 'KE';
    case KIRIBATI = 'KI';
    case KUWAIT = 'KW';
    case KYRGYZSTAN = 'KG';
    case LAOS = 'LA';
    case LATVIA = 'LV';
    case LEBANON = 'LB';
    case LESOTHO = 'LS';
    case LIBERIA = 'LR';
    case LIBYA = 'LY';
    case LIECHTENSTEIN = 'LI';
    case LITHUANIA = 'LT';
    case LUXEMBOURG = 'LU';
    case MACAO_SAR_CHINA = 'MO';
    case MADAGASCAR = 'MG';
    case MALAWI = 'MW';
    case MALAYSIA = 'MY';
    case MALDIVES = 'MV';
    case MALI = 'ML';
    case MALTA = 'MT';
    case MARSHALL_ISLANDS = 'MH';
    case MARTINIQUE = 'MQ';
    case MAURITANIA = 'MR';
    case MAURITIUS = 'MU';
    case MAYOTTE = 'YT';
    case MEXICO = 'MX';
    case MICRONESIA = 'FM';
    case MOLDOVA = 'MD';
    case MONACO = 'MC';
    case MONGOLIA = 'MN';
    case MONTENEGRO = 'ME';
    case MONTSERRAT = 'MS';
    case MOROCCO = 'MA';
    case MOZAMBIQUE = 'MZ';
    case MYANMAR_BURMA = 'MM';
    case NAMIBIA = 'NA';
    case NAURU = 'NR';
    case NEPAL = 'NP';
    case NETHERLANDS = 'NL';
    case NEW_CALEDONIA = 'NC';
    case NEW_ZEALAND = 'NZ';
    case NICARAGUA = 'NI';
    case NIGER = 'NE';
    case NIGERIA = 'NG';
    case NIUE = 'NU';
    case NORFOLK_ISLAND = 'NF';
    case NORTH_KOREA = 'KP';
    case NORTH_MACEDONIA = 'MK';
    case NORTHERN_MARIANA_ISLANDS = 'MP';
    case NORWAY = 'NO';
    case OMAN = 'OM';
    case PAKISTAN = 'PK';
    case PALAU = 'PW';
    case PALESTINIAN_TERRITORIES = 'PS';
    case PANAMA = 'PA';
    case PAPUA_NEW_GUINEA = 'PG';
    case PARAGUAY = 'PY';
    case PERU = 'PE';
    case PHILIPPINES = 'PH';
    case PITCAIRN_ISLANDS = 'PN';
    case POLAND = 'PL';
    case PORTUGAL = 'PT';
    case PUERTO_RICO = 'PR';
    case QATAR = 'QA';
    case REUNION = 'RE';
    case ROMANIA = 'RO';
    case RUSSIA = 'RU';
    case RWANDA = 'RW';
    case SAMOA = 'WS';
    case SAN_MARINO = 'SM';
    case SAO_TOME_PRINCIPE = 'ST';
    case SAUDI_ARABIA = 'SA';
    case SENEGAL = 'SN';
    case SERBIA = 'RS';
    case SEYCHELLES = 'SC';
    case SIERRA_LEONE = 'SL';
    case SINGAPORE = 'SG';
    case SINT_MAARTEN = 'SX';
    case SLOVAKIA = 'SK';
    case SLOVENIA = 'SI';
    case SOLOMON_ISLANDS = 'SB';
    case SOMALIA = 'SO';
    case SOUTH_AFRICA = 'ZA';
    case SOUTH_GEORGIA_SOUTH_SANDWICH_ISLANDS = 'GS';
    case SOUTH_KOREA = 'KR';
    case SOUTH_SUDAN = 'SS';
    case SPAIN = 'ES';
    case SRI_LANKA = 'LK';
    case ST_BARTHELEMY = 'BL';
    case ST_HELENA = 'SH';
    case ST_KITTS_NEVIS = 'KN';
    case ST_LUCIA = 'LC';
    case ST_MARTIN = 'MF';
    case ST_PIERRE_MIQUELON = 'PM';
    case ST_VINCENT_GRENADINES = 'VC';
    case SUDAN = 'SD';
    case SURINAME = 'SR';
    case SVALBARD_JAN_MAYEN = 'SJ';
    case SWEDEN = 'SE';
    case SWITZERLAND = 'CH';
    case SYRIA = 'SY';
    case TAIWAN = 'TW';
    case TAJIKISTAN = 'TJ';
    case TANZANIA = 'TZ';
    case THAILAND = 'TH';
    case TIMOR_LESTE = 'TL';
    case TOGO = 'TG';
    case TOKELAU = 'TK';
    case TONGA = 'TO';
    case TRINIDAD_TOBAGO = 'TT';
    case TUNISIA = 'TN';
    case TURKEY = 'TR';
    case TURKMENISTAN = 'TM';
    case TURKS_CAICOS_ISLANDS = 'TC';
    case TUVALU = 'TV';
    case U_S_OUTLYING_ISLANDS = 'UM';
    case U_S_VIRGIN_ISLANDS = 'VI';
    case UGANDA = 'UG';
    case UKRAINE = 'UA';
    case UNITED_ARAB_EMIRATES = 'AE';
    case UNITED_KINGDOM = 'GB';
    case UNITED_STATES = 'US';
    case URUGUAY = 'UY';
    case UZBEKISTAN = 'UZ';
    case VANUATU = 'VU';
    case VATICAN_CITY = 'VA';
    case VENEZUELA = 'VE';
    case VIETNAM = 'VN';
    case WALLIS_FUTUNA = 'WF';
    case WESTERN_SAHARA = 'EH';
    case YEMEN = 'YE';
    case ZAMBIA = 'ZM';
    case ZIMBABWE = 'ZW';

    /**
     * @return string|null
     */
    public function getFullName(): ?string
    {
        return match ($this) {
            self::UNITED_STATES => self::NAME_UNITED_STATES,
            default => null
        };
    }
}
