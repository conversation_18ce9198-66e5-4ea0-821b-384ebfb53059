<?php

namespace App\Enums\Odin\API;

use App\Services\Odin\API\Resources\AddressResourceService;
use App\Services\Odin\API\Resources\CompanyAccountManagerResourceService;
use App\Services\Odin\API\Resources\CompanyLocationResourceService;
use App\Services\Odin\API\Resources\CompanyResourceService;
use App\Services\Odin\API\Resources\CompanySuccessManagerResourceService;
use App\Services\Odin\API\Resources\CompanyUserResourceService;
use App\Services\Odin\API\Resources\ConsumerAffiliateResourceService;
use App\Services\Odin\API\Resources\v2\ConsumerProductAffiliateResourceService as ConsumerProductAffiliateResourceService;
use App\Services\Odin\API\Resources\v2\ConsumerProductTrackingResourceService;
use App\Services\Odin\API\Resources\ConsumerResourceService;
use App\Services\Odin\API\Resources\ConsumerTcpaResourceService;
use App\Services\Odin\API\Resources\ConsumerTrackingResourceService;
use App\Services\Odin\API\Resources\ProductAppointmentResourceService;
use App\Services\Odin\API\Resources\ProductAssignmentResourceService;
use App\Services\Odin\API\Resources\v2\ConsumerResourceService as ConsumerResourceServiceV2;
use App\Services\Odin\API\Resources\UserResourceService;
use Illuminate\Contracts\Container\BindingResolutionException;

enum FieldClassification: string
{
    case ADDRESS                 = 'address';
    case ROOF_DETAILS            = 'roof_details';
    case PRODUCT_ASSIGNMENT      = 'product_assignment';
    case COMPANY_USER            = 'company_user';
    case COMPANY                 = 'company';
    case CONSUMER                = 'consumer';
    case CONSUMER_PRODUCT        = 'consumer_product';
    case CONSUMER_TCPA           = 'consumer_tcpa';
    case CONSUMER_AFFILIATE      = 'consumer_affiliate';
    case CONSUMER_TRACKING       = 'consumer_tracking';
    case COMPANY_LOCATION        = 'company_location';
    case COMPANY_ACCOUNT_MANAGER = 'company_account_manager';
    case COMPANY_SUCCESS_MANAGER = 'company_success_manager';
    case PRODUCT_APPOINTMENTS    = 'product_appointments';
    case USER                    = 'user';

    /**
     * Returns the resource services.
     *
     * @return array|AddressResourceService[]
     * @throws BindingResolutionException
     */
    public static function getResourceServices(): array
    {
        return [
            self::ADDRESS->value                    => app()->make(AddressResourceService::class),
            self::ROOF_DETAILS->value               => null,
            self::PRODUCT_ASSIGNMENT->value         => app()->make(ProductAssignmentResourceService::class),
            self::COMPANY_USER->value               => app()->make(CompanyUserResourceService::class),
            self::COMPANY->value                    => app()->make(CompanyResourceService::class),
            self::CONSUMER->value                   => app()->make(ConsumerResourceService::class),
            self::CONSUMER_TCPA->value              => app()->make(ConsumerTcpaResourceService::class),
            self::CONSUMER_AFFILIATE->value         => app()->make(ConsumerAffiliateResourceService::class),
            self::CONSUMER_TRACKING->value          => app()->make(ConsumerTrackingResourceService::class),
            self::COMPANY_LOCATION->value           => app()->make(CompanyLocationResourceService::class),
            self::COMPANY_ACCOUNT_MANAGER->value    => app()->make(CompanyAccountManagerResourceService::class),
            self::COMPANY_SUCCESS_MANAGER->value    => app()->make(CompanySuccessManagerResourceService::class),
            self::PRODUCT_APPOINTMENTS->value       => app()->make(ProductAppointmentResourceService::class),
            self::USER->value                       => app()->make(UserResourceService::class),
        ];
    }

    /**
     * Returns v2 resource services.
     * Add v2 resources to the array below to override the v1 class
     * @return array
     * @throws BindingResolutionException
     */
    public static function getResourceServicesV2(): array
    {
        $v1ResourceServices = FieldClassification::getResourceServices();
        $v2ResourceServices = [
            self::CONSUMER->value           => app()->make(ConsumerResourceServiceV2::class),
            self::CONSUMER_AFFILIATE->value => app()->make(ConsumerProductAffiliateResourceService::class),
            self::CONSUMER_TRACKING->value  => app()->make(ConsumerProductTrackingResourceService::class),
        ];

        return [
            ...$v1ResourceServices,
            ...$v2ResourceServices,
        ];
    }
}
