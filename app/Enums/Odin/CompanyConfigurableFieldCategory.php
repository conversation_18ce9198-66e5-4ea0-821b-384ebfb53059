<?php

namespace App\Enums\Odin;

enum CompanyConfigurableFieldCategory: string
{
    case BASIC_INFO = 'Basic Info';
    case ADDITIONAL_INFO = 'Additional Info';
    case CONTACT_AND_SOCIAL = 'Contact and Social Links';
    case MEDIA_AND_ATTACHMENTS = 'Media and Attachments';

    /**
     * @return array
     */
    public static function allCategories(): array
    {
        return array_column(self::cases(), 'value');
    }
}
