<?php

namespace App\Enums\Odin;

enum BudgetCategory: string
{
    case VERIFIED = 'verified';
    case UNVERIFIED = 'unverified';
    case EMAIL_ONLY = 'email_only';

    /**
     * @return array
     */
    public static function all(): array
    {
        $categories = [];
        foreach(self::cases() as $case) {
            $categories[$case->value] = $case;
        }

        return $categories;
    }
}
