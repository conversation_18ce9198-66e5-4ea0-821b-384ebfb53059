<?php

namespace App\Enums\Odin;

use App\Models\Odin\Industry as IndustryModel;

enum Industry: string
{
    case SOLAR = "Solar";
    case ROOFING = "Roofing";
    case HVAC = "HVAC";
    case WINDOWS = "Windows";
    case SIDING = "Siding";
    case DOORS = "Doors";
    case BATHROOMS = "Bathrooms";
    case KITCHENS = "Kitchens";
    case ANIMAL_CONTROL = "Animal Control";
    case SOLAR_SYSTEM_SERVICING = "Solar System Servicing";
    case DECKING = "Decking";
    case FLOORING = "Flooring";
    case EXTERIOR_PAINTING = "Exterior Painting";
    case FENCING = "Fencing";
    case HEAT_PUMPS = "Heat Pumps";
    case LIVING_ROOM = "Living Room";
    case PLUMBING = "Plumbing";
    case ELECTRICAL = "Electrical";
    case CARPORT = "Carport";
    case DRIVEWAY = "Driveway";
    case INSULATION = "Insulation";
    case APPLIANCES = "Appliances";
    case POOL_INSTALLATION = "Pool Installation";
    case BASEMENTS = 'Basements';
    case KITCHEN_CABINET = 'Kitchen Cabinet';
    case GUTTER_INSTALLATION = 'Gutter Installation';
    case DISABILITY_REMODEL = 'Disability Remodel';
    case HOME_SECURITY_SYSTEM = 'Home Security System';
    case SPORTS_COURT_INSTALLATION = 'Sports Court Installation';
    case CONCRETE_FOUNDATION = 'Concrete Foundation';
    case HOME_AUTOMATION = 'Home Automation';
    case FIREPLACE = 'Fireplace';
    case AWNINGS = 'Awnings';
    case SUNROOM = 'Sunroom';
    case DOCKS = 'Docks';
    case STORAGE_SHED = 'Storage Shed';
    case RETAINING_WALL = 'Retaining Wall';
    case PORCH = 'Porch';
    case SEPTIC_TANK_SYSTEM = 'Septic Tank System';
    case TANK_INSTALL = 'Tank Install';
    case CLEANING_SERVICE = 'Cleaning Service';
    case EXTERMINATOR = 'Exterminator';
    case HANDYMAN = 'Handyman';
    case BUILD_NEW_STRUCTURE = 'Build New Structure';
    case GARDENER = 'Gardener';
    case ASBESTOS_REMOVAL = 'Asbestos Removal';
    case LAND_SURVEYING = 'Land Surveying';
    case RECOVERY_SERVICES = 'Recovery Services';
    case FINANCIAL_PRODUCTS = 'Financial Products';
    case SOLAR_ROOF_TILES = 'Solar Roof Tiles';


    /**
     * Whether a given industry is managed by legacy (for purposes of delivery/creation/etc).
     *
     * @return bool
     */
    public function isManagedByLegacy(): bool
    {
        return match ($this) {
            self::SOLAR, self::ROOFING => true,
            default => false,
        };
    }

    /**
     * Whether a given industry is managed by Admin 2 (for purposes of delivery/creation/etc).
     *
     * @return bool
     */
    public function isManagedByAdmin2(): bool
    {
        return !$this->isManagedByLegacy();
    }

    public static function slugsList(): array
    {
        return [
            "solar" => self::SOLAR->value,
            "roofing" => self::ROOFING->value,
            "hvac" => self::HVAC->value,
            "windows" => self::WINDOWS->value,
            "siding" => self::SIDING->value,
            "doors" => self::DOORS->value,
            "bathrooms" => self::BATHROOMS->value,
            "kitchens" => self::KITCHENS->value,
            "animal-control" => self::ANIMAL_CONTROL->value,
            "solar-system-servicing" => self::SOLAR_SYSTEM_SERVICING->value,
            "decking" => self::DECKING->value,
            "flooring" => self::FLOORING->value,
            "exterior-painting" => self::EXTERIOR_PAINTING->value,
            "fencing" => self::FENCING->value,
            "heat-pumps" => self::HEAT_PUMPS->value,
            "living-room" => self::LIVING_ROOM->value,
            "plumbing" => self::PLUMBING->value,
            "electrical" => self::ELECTRICAL->value,
            "carport" => self::CARPORT->value,
            "driveway" => self::DRIVEWAY->value,
            "insulation" => self::INSULATION->value,
            "appliances" => self::APPLIANCES->value,
            'pool-installation' => self::POOL_INSTALLATION->value,
            'basements' => self::BASEMENTS->value,
            'kitchen-cabinet' => self::KITCHEN_CABINET->value,
            'gutter-installation' => self::GUTTER_INSTALLATION->value,
            'disability-remodel' => self::DISABILITY_REMODEL->value,
            'home-security-system' => self::HOME_SECURITY_SYSTEM->value,
            'sports-court-installation' => self::SPORTS_COURT_INSTALLATION->value,
            'concrete-foundation' => self::CONCRETE_FOUNDATION->value,
            'home-automation' => self::HOME_AUTOMATION->value,
            'fireplace' => self::FIREPLACE->value,
            'awnings' => self::AWNINGS->value,
            'sunroom' => self::SUNROOM->value,
            'docks' => self::DOCKS->value,
            'storage-shed' => self::STORAGE_SHED->value,
            'retaining-wall' => self::RETAINING_WALL->value,
            'porch' => self::PORCH->value,
            'septic-tank-system' => self::SEPTIC_TANK_SYSTEM->value,
            'tank-install' => self::TANK_INSTALL->value,
            'cleaning-service' => self::CLEANING_SERVICE->value,
            'exterminator' => self::EXTERMINATOR->value,
            'handyman' => self::HANDYMAN->value,
            'build-new-structure' => self::BUILD_NEW_STRUCTURE->value,
            'gardener' => self::GARDENER->value,
            'asbestos-removal' => self::ASBESTOS_REMOVAL->value,
            'land-surveying' => self::LAND_SURVEYING->value,
            'recovery-services' => self::RECOVERY_SERVICES->value,
            'financial-products' => self::FINANCIAL_PRODUCTS->value,
            'solar-roof-tiles' => self::SOLAR_ROOF_TILES->value,
        ];
    }

    public function getSlug(): string
    {
        return array_flip(self::slugsList())[$this->value];
    }

    /**
     * Returns and industry from a slug
     * @param string $slug
     * @return Industry
     */
    public static function fromSlug(string $slug): Industry
    {
        return self::from(self::slugsList()[$slug]);
    }

    /**
     * Returns and industry from the slug.
     *
     * @param string $slug
     * @return Industry|null
     */
    public static function tryFromSlug(string $slug): ?Industry
    {
        try {
            return Industry::fromSlug($slug);
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Returns a list of all industries by their slugs.
     *
     * @return array
     */
    public static function allIndustriesBySlug(): array
    {
        return array_map(fn(Industry $case) => $case->getSlug(), self::cases());
    }

    /**
     * Returns a list of all industries by their names.
     *
     * @return array
     */
    public static function allIndustriesByName(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * @return IndustryModel
     */
    public function model(): IndustryModel
    {
        /** @var IndustryModel */
        return IndustryModel::query()
            ->where(IndustryModel::FIELD_SLUG, $this->getSlug())
            ->first();
    }
}
