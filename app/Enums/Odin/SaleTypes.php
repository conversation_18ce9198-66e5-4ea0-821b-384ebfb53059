<?php
namespace App\Enums\Odin;

use App\Enums\Campaigns\CampaignType;
use App\Models\Odin\ServiceProduct;
use App\Models\SaleType;

enum SaleTypes: string
{
    const string RETURN_TYPE_ENUM = 'enum';
    const string RETURN_TYPE_NAME = 'name';
    const string RETURN_TYPE_KEY  = 'key';
    const string RETURN_TYPE_ID   = 'id';

    case EXCLUSIVE  = "Exclusive";
    case DUO        = "Duo";
    case TRIO       = "Trio";
    case QUAD       = "Quad";
    case EMAIL_ONLY = "Email Only";
    case UNVERIFIED = "Unverified";

    /**
     * Returns a list of all sale types.
     *
     * @return array
     */
    public static function allSaleTypes(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Returns a list of keys for all sale types.
     *
     * @return array
     */
    public static function allSaleTypeKeys(): array
    {
        return collect(self::allSaleTypes())
            ->map(fn(string $type) => str_replace(' ', '_', strtolower($type)))
            ->toArray();
    }

    /**
     * Returns a key (sale_types table) of the given sale type.
     *
     * @param SaleTypes $saleType
     * @return string
     */
    public static function key(SaleTypes $saleType): string
    {
        return str_replace(' ', '_', strtolower($saleType->value));
    }

    /**
     * @param int $allocations
     * @return $this
     */
    public static function mapAllocationsToVerifiedType(int $allocations): self
    {
        return match ($allocations){
            1 => self::EXCLUSIVE,
            2 => self::DUO,
            3 => self::TRIO,
            4 => self::QUAD,
            default => null
        };
    }

    /**
     * @param SaleTypes $saleType
     * @return int
     */
    public static function mapSaleTypeToLegacyId(SaleTypes $saleType): int
    {
        return match($saleType){
            self::EXCLUSIVE => 1,
            self::DUO => 2,
            self::TRIO => 3,
            self::QUAD => 4,
            self::UNVERIFIED => 5,
            self::EMAIL_ONLY => 6
        };
    }

    /**
     * @return array
     */
    public static function verifiedSaleTypeIds(): array
    {
        $names = [
            self::EXCLUSIVE->value,
            self::DUO->value,
            self::TRIO->value,
            self::QUAD->value,
        ];

        return SaleType::query()
            ->whereIn(SaleType::FIELD_NAME, $names)
            ->pluck(SaleType::FIELD_ID)
            ->toArray();
    }

    /**
     * @param Product $product
     * @param Industry|null $industry
     * @param string $returnType
     * @return string[]|SaleTypes[]
     */
    public static function byProductAndIndustry(Product $product, ?Industry $industry = null, string $returnType = self::RETURN_TYPE_NAME): array
    {
        $enums = match ($product) {
            Product::LEAD        => match ($industry) {
                Industry::SOLAR => [
                    SaleTypes::EXCLUSIVE,
                    SaleTypes::DUO,
                    SaleTypes::TRIO,
                    SaleTypes::QUAD,
                    SaleTypes::EMAIL_ONLY,
                    SaleTypes::UNVERIFIED,
                ],
                default                            => [
                    SaleTypes::EXCLUSIVE,
                    SaleTypes::DUO,
                    SaleTypes::TRIO,
                    SaleTypes::QUAD,
                    SaleTypes::UNVERIFIED,
                ],
            },
            Product::DIRECT_LEADS => [
                SaleTypes::EXCLUSIVE,
                SaleTypes::DUO,
                SaleTypes::TRIO,
                SaleTypes::QUAD,
            ],
            Product::APPOINTMENT => match ($industry) {
                default                            => [
                    SaleTypes::EXCLUSIVE,
                    SaleTypes::DUO,
                    SaleTypes::TRIO,
                ],
            },
        };

        return self::transformEnums($enums, $returnType);
    }

    /**
     * @param int $serviceProductId
     * @param string $returnType
     * @return SaleTypes[]|string[]
     */
    public static function byServiceProductId(int $serviceProductId, string $returnType = self::RETURN_TYPE_NAME): array
    {
        /** @var ServiceProduct $serviceProduct */
        $serviceProduct = ServiceProduct::query()
            ->findOrFail($serviceProductId);
        $product = Product::tryFrom($serviceProduct->product->name);
        $industry = Industry::tryFrom($serviceProduct->service->industry->name);

        return self::byProductAndIndustry($product, $industry, $returnType);
    }

    /**
     * @param CampaignType $campaignType
     * @param string $returnType
     * @return SaleTypes[]|string[]
     */
    public static function byCompanyCampaignType(CampaignType $campaignType, string $returnType = self::RETURN_TYPE_NAME): array
    {
        return match ($campaignType) {
            CampaignType::EXCLUSIVE_ONLY_LEAD_CAMPAIGN  => self::transformEnums([SaleTypes::EXCLUSIVE], $returnType),
            CampaignType::UNVERIFIED_ONLY_LEAD_CAMPAIGN => self::transformEnums([SaleTypes::UNVERIFIED], $returnType),
            CampaignType::SOLAR_LEAD_CAMPAIGN           => self::byProductAndIndustry(Product::LEAD, Industry::SOLAR, $returnType),
            CampaignType::SOLAR_APPOINTMENT_CAMPAIGN    => self::byProductAndIndustry(Product::APPOINTMENT, Industry::SOLAR, $returnType),
            CampaignType::DIRECT_LEADS                  => self::byProductAndIndustry(Product::DIRECT_LEADS, null, $returnType),
            CampaignType::APPOINTMENT_CAMPAIGN          => self::byProductAndIndustry(Product::APPOINTMENT, null, $returnType),
            default                                     => self::byProductAndIndustry(Product::LEAD, null, $returnType),
        };
    }

    /**
     * @param array $enums
     * @param string $returnType
     * @return array
     */
    protected static function transformEnums(array $enums, string $returnType): array
    {
        $names = array_map(fn(SaleTypes $saleType) => $saleType->value, $enums);

        return match($returnType) {
            self::RETURN_TYPE_ENUM => $enums,
            self::RETURN_TYPE_KEY  => array_map(fn(SaleTypes $saleType) => self::key($saleType), $enums),
            self::RETURN_TYPE_ID   => SaleType::query()->whereIn(SaleType::FIELD_NAME, $names)->pluck(SaleType::FIELD_ID)->toArray(),
            default                => $names,
        };
    }
}
