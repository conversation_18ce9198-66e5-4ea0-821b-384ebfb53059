<?php

namespace App\Enums\Odin;

use App\Models\Legacy\LeadPrice;
use App\Models\Odin\QualityTier as QualityTierModel;
use App\Models\Odin\ServiceProduct;
use Exception;

enum QualityTier: string
{
    const string RETURN_TYPE_ENUM = 'enum';
    const string RETURN_TYPE_NAME = 'name';
    const string RETURN_TYPE_ID   = 'id';

    // Lead
    case STANDARD = 'Standard';
    case PREMIUM  = 'Premium';

    // Appointment
    case IN_HOME = 'In-Home';
    case ONLINE  = 'Online';

    /**
     * @param int $leadTypeId
     * @return static
     * @throws Exception
     */
    public static function fromLegacyLeadType(int $leadTypeId): self
    {
        return match ($leadTypeId) {
            LeadPrice::LEAD_TYPE_STANDARD => self::STANDARD,
            LeadPrice::LEAD_TYPE_PREMIUM  => self::PREMIUM,
            default                       => throw new Exception('Invalid legacy lead type ID')
        };
    }

    /**
     * Appointments are using quality tier as a QualityTier and as a budget key - this logic doesn't currently map to other Products
     *
     * @return string|null
     */
    public function getAppointmentBudgetKey(): ?string
    {
        return match ($this) {
            self::ONLINE  => 'online',
            self::IN_HOME => 'in_home',
            default       => null,
        };
    }

    /**
     * @param Product $product
     * @param Industry $industry
     * @param string $returnType
     * @return QualityTier[]|string[]
     */
    public static function byProductAndIndustry(Product $product, Industry $industry, string $returnType = self::RETURN_TYPE_NAME): array
    {
        $enums = match ($product) {
            Product::LEAD, Product::DIRECT_LEADS => match ($industry) {
                Industry::SOLAR => [QualityTier::STANDARD, QualityTier::PREMIUM],
                default         => [QualityTier::STANDARD]
            },
            Product::APPOINTMENT                 => match ($industry) {
                default => [QualityTier::ONLINE, QualityTier::IN_HOME],
            },
        };

        $names = array_map(fn(QualityTier $qualityTier) => $qualityTier->value, $enums);

        return match ($returnType) {
            self::RETURN_TYPE_ENUM => $enums,
            self::RETURN_TYPE_ID   => QualityTierModel::query()->whereIn(QualityTierModel::FIELD_NAME, $names)->pluck(QualityTierModel::FIELD_ID)->toArray(),
            default                => $names,
        };
    }

    /**
     * @param int $serviceProductId
     * @param string $returnType
     * @return array
     */
    public static function byServiceProductId(int $serviceProductId, string $returnType = self::RETURN_TYPE_NAME): array
    {
        /** @var ServiceProduct $serviceProduct */
        $serviceProduct = ServiceProduct::query()
            ->findOrFail($serviceProductId);
        $product        = Product::tryFrom($serviceProduct->product->name);
        $industry       = Industry::tryFrom($serviceProduct->service->industry->name);

        return self::byProductAndIndustry($product, $industry, $returnType);
    }

    /**
     * @return int
     */
    public function maximumSellableLegs(): int
    {
        return match ($this) {
            QualityTier::IN_HOME => 2,
            QualityTier::ONLINE  => 3,
            default              => 4,
        };
    }

    /**
     * @return QualityTierModel
     */
    public function model(): QualityTierModel
    {
        /** @var QualityTierModel */
        return QualityTierModel::query()
            ->where(QualityTierModel::FIELD_NAME, $this->value)
            ->first();
    }
}
