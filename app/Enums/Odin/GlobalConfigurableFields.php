<?php

namespace App\Enums\Odin;

enum GlobalConfigurableFields: string
{
    case STOREYS                  = 'storeys';
    case ROOF_DIRECTION           = 'roof_direction';
    case ROOF_SHADING             = 'roof_shading';
    case ROOF_PITCH               = 'roof_pitch';
    case ROOF_TYPE                = 'roof_type';
    case ROOF_TYPE_OTHER          = 'roof_type_other';
    case ROOF_CONDITION           = 'roof_condition';
    case ROOF_COMPLEXITY          = 'roof_complexity';
    case ROOF_SKYLIGHTS           = 'roof_skylights';
    case ROOF_VENTS               = 'roof_vents';
    case ROOF_DORMERS             = 'roof_dormers';
    case ROOF_CHIMNEYS            = 'roof_chimneys';
    case ROOF_OTHER_OBSTRUCTIONS  = 'roof_other_obstructions';
    case ROOF_HVAC_UNITS          = 'roof_hvac_units';
    case BEST_TIME_TO_CALL        = 'best_time_to_call';
    case BEST_TIME_TO_CALL_OTHER  = 'best_time_to_call_other';
    case OWN_PROPERTY             = 'own_property';
    case PROPERTY_TYPE            = 'property_type';
    case PROJECT_TYPE             = 'project_type';
    case PAYMENT_TYPE             = 'payment_type';
    case IP_ADDRESS               = 'ip_address';
    case ORIGIN                   = 'origin';
    case EMAIL_VALIDATED          = 'email_validated';
    case PHONE_VALIDATED          = 'phone_validated';
    case PHONE_VALIDATION_METHOD  = 'phone_validation_method';
    case AFFILIATE_ID             = 'affiliate_id';
    case CAMPAIGN_ID              = 'campaign_id';
    case TRACK_CODE               = 'track_code';
    case TRACK_NAME               = 'track_name';
    case FBCLID                   = 'fbclid';
    case TRACK_LEAD_ID            = 'track_lead_id';
    case TCPA_LEAD_ID             = 'tcpa_lead_id';
    case LEAD_ID_SERVICE_TYPE     = 'lead_id_service_type';
    case CONTACT_METHOD           = 'contact_method';
    case TCPA_DISCLOSURE_CAPTURED = 'tcpa_disclosure_captured';
    case AUTO_ASSIGNED_STATUS     = 'auto_assigned_status';
    case UTC                      = 'utc';
    case DESCRIPTION              = 'description';
    case SALES_EMAIL              = 'sales_email';
    case TECH_SUPPORT_EMAIL       = 'tech_support_email';
    case COMMENTS                 = 'comments';
    case ESTIMATE_PERMALINK       = 'estimate_permalink';
    case YEAR_STARTED_BUSINESS    = 'year_started_business';
    case COLOR_CODE               = 'color_code';
    case APPOINTMENTS_REQUESTED   = 'appointments_requested';
    case APPOINTMENTS             = 'appointments';
    case APPOINTMENT_TYPE         = 'appointment_type';
    case OTHER_INTERESTS          = 'other_interests';
    case TCPA_OPT_INS             = 'tcpa_opt_ins';
    case TRUSTED_FORM_URL         = 'trusted_form_url';
    case CALCULATOR_ESTIMATES     = 'calculator_estimates';
    case QR_TOP_500_COMPANY       = 'qr_top_500_company';
}
