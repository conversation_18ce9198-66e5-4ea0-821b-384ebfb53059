<?php

namespace App\Enums\Odin;

enum ConsumerConfigurableFieldCategory: string
{
    case UNCATEGORIZED = 'uncategorized';
    case CONTACT_INFORMATION = 'contact_information';
    case LEAD_INFORMATION = 'lead_information';

    /**
     * Returns all categories slugs
     *
     * @return array
     */
    public static function allCategoriesSlugs(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Returns category's name given a slug
     *
     * @param string $slug
     * @return string
     */
    public static function getCategoryName(string $slug): string
    {
        return match ($slug) {
            self::UNCATEGORIZED->value => "Uncategorized",
            self::CONTACT_INFORMATION->value => "Contact Information",
            self::LEAD_INFORMATION->value => "Lead Information",
            default => $slug,
        };
    }
}
