<?php

namespace App\Enums\Odin;

use App\Models\Legacy\LeadCategory;
use App\Models\Odin\PropertyType as PropertyTypeModel;
use Exception;

enum PropertyType: string
{
    case RESIDENTIAL = 'Residential';
    case COMMERCIAL = 'Commercial';

    public static function fromLegacyLeadCategory(int $leadCategoryId): self
    {
        return match($leadCategoryId) {
            LeadCategory::RESIDENTIAL => self::RESIDENTIAL,
            LeadCategory::COMMERCIAL => self::COMMERCIAL,
            default => throw new Exception("Invalid lead category id")
        };
    }

    /**
     * @return PropertyTypeModel
     */
    public function model(): PropertyTypeModel
    {
        /** @var PropertyTypeModel */
        return PropertyTypeModel::query()
            ->where(PropertyTypeModel::FIELD_NAME, $this->value)
            ->first();
    }
}
