<?php

namespace App\Enums\Odin;

enum RoofingConfigurableFields: string
{
    case ROOF_REQUESTED_NEW_GUTTERS = 'roof_requested_new_gutters';
    case ROOF_REQUESTED_ROOF_TEAR_OFF = 'roof_requested_tear_off';
    case ROOF_REQUESTED_ROOF_DISPOSAL = 'roof_requested_disposal';
    case ROOF_ESTIMATE_LOW = 'roof_estimate_low';
    case ROOF_ESTIMATE_MEDIAN = 'roof_estimate_median';
    case ROOF_ESTIMATE_HIGH = 'roof_estimate_high';
    case ROOF_REPLACEMENT_AREA = 'roof_replacement_area';
    case ROOF_TRUSTED_FORM_CERT = 'roof_trusted_form_cert';
    case ROOF_TRUSTED_FORM_TOKEN = 'roof_trusted_form_token';
    case ROOF_TRUSTED_FORM_PING = 'roof_trusted_form_ping';
}
