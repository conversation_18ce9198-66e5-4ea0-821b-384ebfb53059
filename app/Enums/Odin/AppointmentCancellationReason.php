<?php

namespace App\Enums\Odin;

enum AppointmentCancellationReason: string
{
    case RESCHEDULED = 'rescheduled';
    case NOT_INTERESTED = 'not_interested';
    case OTHER = 'other';

    /**
     * @return array
     */
    public static function all(): array
    {
        $categories = [];
        foreach(self::cases() as $case) {
            $categories[$case->value] = $case;
        }

        return $categories;
    }

    /**
     * @return array
     */
    public static function displayNames(): array
    {
        return [
            self::RESCHEDULED->value => "Already rescheduled with company",
            self::NOT_INTERESTED->value => "No longer interested",
            self::OTHER->value => "Other"
        ];
    }
}
