<?php

namespace App\Enums\Mailbox;

enum EmailCategory: string
{
    case INBOX      = 'inbox';
    case SENT       = 'sent';
    case STARRED    = 'starred';
    case IMPORTANT  = 'important';
    case ARCHIVED   = 'archived';
    case ALL_MAIL   = 'all-mail';

    public static function getValues(): \Illuminate\Support\Collection
    {
        return collect(self::cases())->map(fn($c) => $c->value);
    }
}
