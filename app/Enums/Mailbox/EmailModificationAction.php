<?php

namespace App\Enums\Mailbox;

enum EmailModificationAction: string
{
    case READ           = 'read';
    case UNREAD         = 'unread';
    case STAR           = 'star';
    case UNSTAR         = 'unstar';
    case ARCHIVE        = 'archive';
    case UNARCHIVE      = 'unarchive';
    case IMPORTANT      = 'important';
    case UNIMPORTANT    = 'unimportant';
    case DELETE         = 'delete';

    public static function getValues(): \Illuminate\Support\Collection
    {
        return collect(self::cases())->map(fn($c) => $c->value);
    }
}
