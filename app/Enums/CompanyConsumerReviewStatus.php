<?php

namespace App\Enums;

enum CompanyConsumerReviewStatus: int
{
    case DECLINED           = -1;
    case PENDING_APPROVAL   = 0;
    case APPROVED           = 1;

    const string STRING_DECLINED           = 'rejected';
    const string STRING_APPROVED           = 'approved';
    const string STRING_PENDING_APPROVAL   = 'pending';

    /**
     * @return string
     */
    public function getDisplayName(): string
    {
        return match($this) {
            self::DECLINED          => 'Rejected',
            self::PENDING_APPROVAL  => 'Pending',
            self::APPROVED          => 'Approved',
        };
    }

    /**
     * @return array
     */
    public static function displayNames(): array
    {
        $output = [];
        foreach(self::cases() as $case) {
            $output[$case->value] = $case->getDisplayName();
        }

        return $output;
    }

    /**
     * @param CompanyConsumerReviewStatus|int|null $companyConsumerReviewStatus
     * @return string
     */
    public static function label(CompanyConsumerReviewStatus|int|null $companyConsumerReviewStatus): string
    {
        $value = $companyConsumerReviewStatus instanceof CompanyConsumerReviewStatus ? $companyConsumerReviewStatus->value : $companyConsumerReviewStatus;

        return match ($value) {
            self::DECLINED->value           => CompanyConsumerReviewStatus::STRING_DECLINED,
            self::APPROVED->value           => CompanyConsumerReviewStatus::STRING_APPROVED,
            self::PENDING_APPROVAL->value   => CompanyConsumerReviewStatus::STRING_PENDING_APPROVAL,
        };
    }
}
