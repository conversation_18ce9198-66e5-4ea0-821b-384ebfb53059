<?php

namespace App\Enums\CompanyServicingArea;

use App\EnumAttributes\Concerns\GetAttributesTrait;
use App\EnumAttributes\Description;

enum CompanyServicingAreaCompanyContactFilterConditionOption: string
{
    use GetAttributesTrait;

    #[Description('With Phone')]
    case WITH_PHONE = 'withPhone';

    #[Description('With Email')]
    case WITH_EMAIL = 'withEmail';

    #[Description('With Both')]
    case WITH_PHONE_AND_EMAIL = 'withBoth';

    #[Description('With Neither')]
    case WITH_NEITHER_PHONE_OR_EMAIL = 'withNeither';
}
