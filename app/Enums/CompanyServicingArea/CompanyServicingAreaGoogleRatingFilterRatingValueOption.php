<?php

namespace App\Enums\CompanyServicingArea;

enum CompanyServicingAreaGoogleRatingFilterRatingValueOption: string
{
    case ONE = '&#9733;&#9734;&#9734;&#9734;&#9734;';
    case TWO = '&#9733;&#9733;&#9734;&#9734;&#9734;';
    case THREE = '&#9733;&#9733;&#9733;&#9734;&#9734;';
    case FOUR = '&#9733;&#9733;&#9733;&#9733;&#9734;';
    case FIVE = '&#9733;&#9733;&#9733;&#9733;&#9733;';

    public static function asSelectArray(): array
    {
        return [
            [
                "id" => 1,
                "name" => html_entity_decode(self::ONE->value),
            ],
            [
                "id" => 2,
                "name" => html_entity_decode(self::TWO->value),
            ],
            [
                "id" => 3,
                "name" => html_entity_decode(self::THREE->value),
            ],
            [
                "id" => 4,
                "name" => html_entity_decode(self::FOUR->value),
            ],
            [
                "id" => 5,
                "name" => html_entity_decode(self::FIVE->value),
            ],
        ];
    }
}
