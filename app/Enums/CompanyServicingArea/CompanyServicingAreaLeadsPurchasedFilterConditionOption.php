<?php

namespace App\Enums\CompanyServicingArea;

use App\EnumAttributes\Concerns\GetAttributesTrait;
use App\EnumAttributes\Description;

enum CompanyServicingAreaLeadsPurchasedFilterConditionOption: string
{
    use GetAttributesTrait;

    #[Description('All Time')]
    case ALL_TIME = 'allTime';

    #[Description('Last 30 Days')]
    case LAST_THIRTY_DAYS = 'last30Days';

    #[Description('Last 60 Days')]
    case LAST_SIXTY_DAYS = 'last60Days';

    #[Description('Last 90 Days')]
    case LAST_NINETY_DAYS = 'last90Days';

    #[Description('Last Six Months')]
    case LAST_SIX_MONTHS = 'lastSixMonths';

    #[Description('Last Year')]
    case LAST_YEAR = 'lastYear';

    #[Description('Last Two Years')]
    case LAST_TWO_YEARS = 'lastTwoYears';
}
