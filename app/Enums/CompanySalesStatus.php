<?php

namespace App\Enums;

use UnitEnum;

enum CompanySalesStatus: int
{
    case NO_CONTACT_ATTEMPTED = 1;
    case OUTREACH_STARTED = 11;
    case CONVERSATION_WITH_DM = 2;
    case DEMO_SCHEDULED = 3;
    case DEMO_COMPLETED = 12;
    case SALE_PENDING = 4;
    case CLOSED = 5;
    case NOT_INTERESTED = 9;
    case DO_NOT_ENGAGE = 7;
    case DQ_D = 8;
    case OUT_OF_BUSINESS = 6;
    case PREVIOUS_BUYER = 13;
    case COMMERCIAL_ONLY = 10;
    case PARTNER = 14;
    case COMPETITOR = 15;
    case PR_CHECK_REQUIRED = 16;

    const STRING_NO_CONTACT_ATTEMPTED = 'No Contact Attempted';
    const STRING_CONVERSATION_WITH_DM = 'Conversation with DM';
    const STRING_DEMO_SCHEDULED = 'Demo Scheduled';
    const STRING_SALE_PENDING = 'Sale Pending';
    const STRING_CLOSED = 'Closed';
    const STRING_OUT_OF_BUSINESS = 'Out of Business';
    const STRING_DO_NOT_ENGAGE = 'Do Not Engage';
    const STRING_DQ_D = 'Dq\'d';
    const STRING_NOT_INTERESTED = 'Not Interested';
    const STRING_COMMERCIAL_ONLY = 'Commercial Only';
    const STRING_OUTREACH_STARTED = 'Outreach Started';
    const STRING_DEMO_COMPLETED = 'Demo Completed';
    const STRING_PREVIOUS_BUYER = 'Previous Buyer';
    const STRING_PARTNER = 'Partner';
    const STRING_COMPETITOR = 'Competitor';
    const STRING_PR_CHECK_REQUIRED = 'PR Check Required';

    public static function getAsKeyValueSelectArray(): array
    {
        $result = [];

        /**
         * @var UnitEnum $value
         */
        foreach (self::cases() as $case) {
            $label = self::label($case->value);

            $result[$label] = $case->value;
        }

        return $result;
    }

    /**
     * @param  CompanySalesStatus|int|null  $companySalesStatus
     * @return string
     */
    public static function label(CompanySalesStatus|int|null $companySalesStatus): string
    {
        $value = $companySalesStatus instanceof CompanySalesStatus ? $companySalesStatus->value : $companySalesStatus;

        return match ($value) {
            self::NO_CONTACT_ATTEMPTED->value => self::STRING_NO_CONTACT_ATTEMPTED,
            self::CONVERSATION_WITH_DM->value => self::STRING_CONVERSATION_WITH_DM,
            self::DEMO_SCHEDULED->value => self::STRING_DEMO_SCHEDULED,
            self::SALE_PENDING->value => self::STRING_SALE_PENDING,
            self::CLOSED->value => self::STRING_CLOSED,
            self::OUT_OF_BUSINESS->value => self::STRING_OUT_OF_BUSINESS,
            self::DO_NOT_ENGAGE->value => self::STRING_DO_NOT_ENGAGE,
            self::DQ_D->value => self::STRING_DQ_D,
            self::NOT_INTERESTED->value => self::STRING_NOT_INTERESTED,
            self::COMMERCIAL_ONLY->value => self::STRING_COMMERCIAL_ONLY,
            self::OUTREACH_STARTED->value => self::STRING_OUTREACH_STARTED,
            self::DEMO_COMPLETED->value => self::STRING_DEMO_COMPLETED,
            self::PREVIOUS_BUYER->value => self::STRING_PREVIOUS_BUYER,
            self::PARTNER->value => self::STRING_PARTNER,
            self::COMPETITOR->value => self::STRING_COMPETITOR,
            self::PR_CHECK_REQUIRED->value => self::STRING_PR_CHECK_REQUIRED,
            default => 'Unknown'
        };
    }

    public static function getSalesPipelineGroups(): array
    {
        return [
            "Closed" => [
                self::CLOSED
            ],
            "Sales Pending" => [
                self::SALE_PENDING,
                self::DEMO_COMPLETED
            ],
            "In Progress" => [

            ],
            "Pre-Sales" => [

            ],
            "Demo Scheduled" => [
                self::DEMO_SCHEDULED
            ],
            "Conversation" => [
                self::CONVERSATION_WITH_DM
            ]
        ];
    }

    /**
     * @return array
     */
    public static function asSelectArray(): array
    {
        $result = [];

        foreach (self::cases() as $case) {
            $result[] = [
                'id' => $case->value,
                'name' => self::label($case->value)
            ];
        }

        return $result;
    }

    /**
     * Returns the display name.
     *
     * @return string
     */
    public function displayName(): string
    {
        return self::label($this);
    }

    public static function disqualifyingStatuses(): array
    {
        return [
            self::OUT_OF_BUSINESS,
            self::DO_NOT_ENGAGE,
            self::DQ_D,
            self::NOT_INTERESTED,
            self::COMMERCIAL_ONLY
        ];
    }
}
