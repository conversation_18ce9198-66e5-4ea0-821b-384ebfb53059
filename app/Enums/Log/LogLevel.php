<?php

namespace App\Enums\Log;

enum LogLevel: string
{
    case EMERGENCY = 'emergency';
    case ALERT     = 'alert';
    case CRITICAL  = 'critical';
    case ERROR     = 'error';
    case WARNING   = 'warning';
    case NOTICE    = 'notice';
    case INFO      = 'info';
    case DEBUG     = 'debug';

    /**
     * @return int
     */
    public function getLifespanInDays(): int
    {
        return match ($this) {
            self::EMERGENCY, self::ALERT, self::CRITICAL, self::ERROR, => 90,
            self::WARNING                                              => 30,
            self::NOTICE, self::INFO                                   => 15,
            self::DEBUG                                                => 7,
            default                                                    => 30
        };
    }
}
