<?php

namespace App\Enums;

enum NotificationConfigProductType: int
{
    case ALL_PRODUCTS           = 0;

    // Leads
    case ALL_LEADS              = 1;
    case LEAD_STANDARD          = 2;
    case LEAD_PREMIUM           = 3;

    // Appointments
    case ALL_APPOINTMENTS       = 4;
    case APPOINTMENT_IN_PERSON  = 5;
    case APPOINTMENT_VIRTUAL    = 6;

    /**
     * @return string
     */
    public function getTypeName(): string
    {
        return match ($this) {
            self::ALL_PRODUCTS          => 'All Products',
            self::ALL_LEADS             => 'All Leads',
            self::LEAD_STANDARD         => 'Leads - Standard',
            self::LEAD_PREMIUM          => 'Leads - Premium',
            self::ALL_APPOINTMENTS      => 'All Appointments',
            self::APPOINTMENT_IN_PERSON => 'Appointments - In-person',
            self::APPOINTMENT_VIRTUAL   => 'Appointments - Virtual',
        };
    }

    /**
     * @return array
     */
    public static function allProductTypes(): array
    {
        return array_column(self::cases(), 'value');
    }
}
