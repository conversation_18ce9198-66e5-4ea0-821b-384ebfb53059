<?php

namespace App\Enums;

use App\Workflows\Actions\AddCRMAction;
use App\Workflows\Actions\AddNotificationAction;
use App\Workflows\Actions\AssignAccountManagerNode;
use App\Workflows\Actions\ComparisonNode;
use App\Workflows\Actions\CreateTaskAction;
use App\Workflows\Actions\DelayNode;
use App\Workflows\Actions\EmailNotificationAction;
use App\Workflows\Actions\GenerateAndDeliverTestLeadAction;
use App\Workflows\Actions\ModuloNode;
use App\Workflows\Actions\ReassignAccountManagerNode;
use App\Workflows\Actions\ResultNode;
use App\Workflows\Actions\RunTemplateNode;
use App\Workflows\Actions\ScheduleTaskAction;
use App\Workflows\Actions\CompanyStatusNode;

enum ActionType: string
{
    case ADD_NOTIFICATION = 'add_notification';
    case EMAIL_NOTIFICATION = 'email_notification';
    case ADD_TASK = 'add_task';
    case SCHEDULE_TASK = 'schedule_task';
    case ADD_CRM_ENTRY = 'add_crm_entry';
    case RESULT_NODE = 'result_node';
    case RUN_TEMPLATE = 'run_template';
    case DELAY_NODE = 'delay_node';
    case COMPANY_STATUS_NODE = 'company_status_node';

    case ASSIGN_ACCOUNT_MANAGER = 'assign_account_manager';
    case REASSIGN_ACCOUNT_MANAGER = 'reassign_account_manager';

    case CREATE_MISSED_PRODUCT = 'create_missed_product';
    case GENERATE_AND_DELIVER_TEST_LEAD = 'generate_and_deliver_test_lead';

    // LOGIC NODE
    case COMPARISON = 'comparison';
    case MODULO = 'modulo';

    /**
     * Returns the class for a given action type.
     *
     * @return string|null
     */
    public function getActionClass(): ?string
    {
        return match ($this) {
            ActionType::ADD_NOTIFICATION               => AddNotificationAction::class,
            ActionType::EMAIL_NOTIFICATION             => EmailNotificationAction::class,
            ActionType::ADD_TASK                       => CreateTaskAction::class,
            ActionType::SCHEDULE_TASK                  => ScheduleTaskAction::class,
            ActionType::RESULT_NODE                    => ResultNode::class,
            ActionType::ADD_CRM_ENTRY                  => AddCRMAction::class,
            ActionType::COMPARISON                     => ComparisonNode::class,
            ActionType::RUN_TEMPLATE                   => RunTemplateNode::class,
            ActionType::ASSIGN_ACCOUNT_MANAGER         => AssignAccountManagerNode::class,
            ActionType::REASSIGN_ACCOUNT_MANAGER       => ReassignAccountManagerNode::class,
            ActionType::DELAY_NODE                     => DelayNode::class,
            ActionType::COMPANY_STATUS_NODE            => CompanyStatusNode::class,
            ActionType::MODULO                         => ModuloNode::class,
            ActionType::GENERATE_AND_DELIVER_TEST_LEAD => GenerateAndDeliverTestLeadAction::class,
            default                                    => null
        };
    }
}
