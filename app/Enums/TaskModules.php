<?php

namespace App\Enums;

enum TaskModules: string
{
    case COMPANY_CONTACTS = 'company-contacts';
    case COMPANY_ACTIONS = 'company-actions';
    case COMPANY_CAMPAIGNS = 'company-campaigns';
    case COMPANY_INVOICES = 'company-invoices';
    case COMPANY_LEADS = 'company-leads';
    case COMPANY_OVERVIEW = 'company-overview';
    case COMPANY_OVERVIEW_FULL = 'company-overview-full';
    case COMPANIES_ALREADY_SOLD_TO = 'companies-already-sold-to';
    case COMPANIES_SERVICING = 'companies-servicing';
    case LEAD_INTERACTIVE_MAP = 'lead-interactive-map';
    case LEAD_VERIFICATION = 'lead-verification';
    case LEAD_BASIC_INFO = 'lead-basic-info';
    case LEAD_RELATED_ACTIVITY = 'lead-related-activity';
    case UNSOLD_AND_UNDERSOLD_LEADS = 'unsold-and-undersold-leads';
    case OUTREACH_CADENCES = 'outreach-cadences';

    /**
     * Returns the display name for the given module.
     *
     * @return string
     */
    public function getDisplayName(): string
    {
        return match ($this) {
            self::COMPANY_CONTACTS => "Company Contacts",
            self::COMPANY_ACTIONS => "Company Actions/Notes",
            self::COMPANY_CAMPAIGNS => "Company Campaigns",
            self::COMPANY_INVOICES => "Company Billing & Invoicing",
            self::COMPANY_LEADS => "Company Leads",
            self::COMPANY_OVERVIEW => "Company Overview",
            self::COMPANY_OVERVIEW_FULL => "Company Overview Full",
            self::COMPANIES_ALREADY_SOLD_TO => "Companies Already Sold To",
            self::LEAD_INTERACTIVE_MAP => "Lead Interactive Map",
            self::LEAD_VERIFICATION => "Lead Verification",
            self::LEAD_BASIC_INFO => "Lead Basic Information",
            self::LEAD_RELATED_ACTIVITY => "Lead Related Activity",
            self::COMPANIES_SERVICING => "Companies Servicing Area",
            self::UNSOLD_AND_UNDERSOLD_LEADS => "Unsold and Undersold Leads",
            self::OUTREACH_CADENCES => 'Outreach Cadences',
        };
    }

    public function getMinColumns(): int
    {
        return match ($this) {
            self::COMPANY_CAMPAIGNS, self::COMPANY_INVOICES, self::COMPANY_LEADS, self::COMPANY_OVERVIEW, self::COMPANY_OVERVIEW_FULL, self::COMPANIES_SERVICING => 6,
            self::LEAD_BASIC_INFO, self::LEAD_INTERACTIVE_MAP, self::LEAD_RELATED_ACTIVITY => 4,
            self::COMPANY_ACTIONS, self::COMPANY_CONTACTS, self::LEAD_VERIFICATION, self::COMPANIES_ALREADY_SOLD_TO, self::UNSOLD_AND_UNDERSOLD_LEADS, self::OUTREACH_CADENCES => 2,
            default => 1
        };
    }
}
