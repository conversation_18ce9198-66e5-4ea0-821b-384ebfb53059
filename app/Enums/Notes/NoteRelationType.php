<?php

namespace App\Enums\Notes;

use App\Models\LeadRefund;
use App\Models\Mailbox\MailboxEmail;

enum NoteRelationType: string
{
    case MAILBOX_EMAILS = 'mailbox_emails';
    case LEAD_REFUNDS   = 'lead_refunds';

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public function getModelClass(): ?string
    {
        return match ($this) {
            self::MAILBOX_EMAILS => MailboxEmail::class,
            self::LEAD_REFUNDS   => LeadRefund::class,
            default              => null
        };
    }
}
