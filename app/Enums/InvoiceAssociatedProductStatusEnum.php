<?php

namespace App\Enums;
enum InvoiceAssociatedProductStatusEnum: int
{
    case STATUS_CANCELLED   = -1;
    case STATUS_INITIAL     = 0;
    case STATUS_IN_PROGRESS = 1;
    case STATUS_COMPLETED   = 2;
    public static function getStatus($value): string
    {
        return match ($value) {
            self::STATUS_CANCELLED->value     => "Cancelled",
            self::STATUS_INITIAL->value       => "Initial",
            self::STATUS_IN_PROGRESS->value   => "In Progress",
            self::STATUS_COMPLETED->value     => "Completed",
        };
    }
    /**
     * Handles returning a list of all activity-types.
     *
     * @return array
     */
    public static function allTypes(): array
    {
        return array_column(self::cases(), 'value');
    }
}