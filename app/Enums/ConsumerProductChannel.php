<?php

namespace App\Enums;

enum ConsumerProductChannel: int
{
    case DIRECT_FROM_CALCULATORS = 1;
    case AFFILIATE_LEADS = 2;
    case REWORKED_AGED_LEADS = 3;
    case UNSOLD_LEGS = 4;
    case REVALIDATED_FROM_EMAIL_CAMPAIGN = 5;
    case AI_REVALIDATED_LEAD = 6;

    /**
     * @return string
     */
    public function toLabel(): string
    {
        return match ($this) {
            self::DIRECT_FROM_CALCULATORS => 'Direct from calculators',
            self::AFFILIATE_LEADS => 'Affiliate leads',
            self::REWORKED_AGED_LEADS => 'Reworked aged leads',
            self::UNSOLD_LEGS => 'Upsold legs',
            self::REVALIDATED_FROM_EMAIL_CAMPAIGN => 'Revalidated from email campaigns',
            self::AI_REVALIDATED_LEAD => 'AI revalidated lead',
        };
    }

    /**
     * @return int
     */
    public function qualityScore(): int
    {
        return match ($this) {
            self::DIRECT_FROM_CALCULATORS => 1,
            self::AFFILIATE_LEADS => 2,
            self::REWORKED_AGED_LEADS => 3,
            self::UNSOLD_LEGS => 4,
            self::REVALIDATED_FROM_EMAIL_CAMPAIGN => 5,
            self::AI_REVALIDATED_LEAD => 6,
        };
    }
}
