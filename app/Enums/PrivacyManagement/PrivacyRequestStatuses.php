<?php

namespace App\Enums\PrivacyManagement;

enum PrivacyRequestStatuses: string
{
    case INITIAL        = 'initial';
    case SCANNED        = 'scanned';
    case PROCESSING     = 'processing';
    case DONE           = 'done';

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public function getTitle(): string
    {
        return match ($this) {
            self::INITIAL       => 'Initial',
            self::SCANNED       => 'Scanned',
            self::PROCESSING    => 'Processing',
            self::DONE          => 'Done',
        };
    }
}