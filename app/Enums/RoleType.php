<?php

namespace App\Enums;

enum RoleType: string
{
    case BASIC                          = 'basic';
    case ADMIN                          = 'admin';
    case LEAD_PROCESSOR                 = 'lead-processor';
    case LEAD_PROCESSING_MANAGEMENT     = 'lead-processing-management';
    case SALES_BAIT_MANAGEMENT          = 'sales-bait-management';
    case INDUSTRY_MANAGEMENT            = 'industry-management';
    case BUNDLE_ADMIN                   = 'bundle-admin';
    case BUNDLE_ISSUER                  = 'bundle-issuer';
    case EMAIL_TEMPLATE_ADMIN           = 'email-template-admin';
    case OPPORTUNITY_NOTIFICATION_ADMIN = 'opportunity-notification-admin';
    case SALES_MANAGER                  = 'sales-manager';
    case HR_MANAGER                     = 'hr-manager';
    case ALERT_PROCESSOR                = 'alert-processor';
    case MAILBOX_USER                   = 'mailbox-user';
    case ACCOUNT_MANAGER                = 'account-manager';
    case PRIVACY_OFFICER                = 'privacy-officer';
    case PRIVACY_ADMIN                  = 'privacy-admin';
    case RELATIONSHIP_MANAGER_ADMIN     = 'relationship-manager-admin';
    case RELATIONSHIP_MANAGER_VIEWER    = 'relationship-manager-viewer';
    case SUCCESS_MANAGER_VIEWER         = 'success-manager-viewer';
    case LEAD_REFUNDS_VIEWER            = 'lead-refunds-viewer';
    case LEAD_REFUNDS_REQUESTER         = 'lead-refunds-requester';
    case LEAD_REFUNDS_REVIEWER          = 'lead-refunds-reviewer';
    case LOOKER_ADMIN                   = 'looker-admin';
    case FINANCE_OWNER                  = 'finance-owner';
    case FINANCE_MANAGER                = 'finance-manager';
    case FINANCE_CONTROLLER             = 'finance-controller';
    case FINANCE_ANALYST                = 'finance-analyst';
    case FINANCE_VIEWER                 = 'finance-viewer';
    case FINANCIAL_ANALYST              = 'financial-analyst';
    case AFFILIATES_ADMIN               = 'affiliates-admin';
    case DEACTIVATED                    = 'deactivated';
    case MARKETING_MANAGER              = 'marketing-manager';
    case CONTRACT_MANAGER               = 'contract-manager';
    case BUSINESS_DEVELOPMENT_MANAGER   = 'business-development-manager';
    case PROSPECTOR                     = 'prospector';
    case QUALITY_ASSURANCE_MANAGER      = 'quality-assurance-manager';
    case ONBOARDING_MANAGER             = 'onboarding-manager';
    case QA_AUTOMATION_MANAGER          = 'qa-automation-manager';

    case SALES_DEVELOPMENT_REPRESENTATIVE = 'sales-development-representative';
    case CUSTOMER_SUCCESS_MANAGER         = 'customer-success-manager';
}
