<?php

namespace App\Enums\Reports\CountyCoverageReport;

use App\Enums\Campaigns\CampaignStatus;
use App\Enums\Campaigns\Modules\Budget\BudgetType;
use App\Enums\Odin\Industry;
use App\Models\Odin\Industry as IndustryModel;
use App\Models\Odin\CompanyConfiguration;
use App\Models\Odin\IndustryConfiguration;
use App\Repositories\Reports\CountyCoverageReportRepository;

/**
 * Adding a column
 * - It is important to include the new column in all functions that the other columns are included in
 * - Calculated columns can be either string concat or computed
 * - Copy another existing column's format when adding a new one
 */
enum CountyCoverageReportColumnEnum: string
{
    case STATE = 'state';
    case COUNTY = 'county';
    case TOTAL_ZIP_CODES = 'total_zip';
    case THREE_CAMPAIGNS = 'three_campaigns';
    case TWO_CAMPAIGNS = 'two_campaigns';
    case ONE_CAMPAIGN = 'one_campaign';
    case NO_CAMPAIGN = 'no_campaign';
    case THREE_UNLIMITED = 'unlimited_3';
    case THREE_UNLIMITED_COVERAGE = 'unlimited_3_coverage';
    case TWO_UNLIMITED = 'unlimited_2';
    case TWO_UNLIMITED_COVERAGE = 'unlimited_2_coverage';
    case ONE_UNLIMITED = 'unlimited_1';
    case ONE_UNLIMITED_COVERAGE = 'unlimited_1_coverage';
    case NO_UNLIMITED_COVERAGE = 'unlimited_0_coverage';
    case NO_UNLIMITED = 'unlimited_0';
    case SOME_UNLIMITED = 'unlimited_some';
    case THREE_LIMITED = 'limited_3';
    case THREE_LIMITED_COVERAGE = 'limited_3_coverage';
    case TWO_LIMITED = 'limited_2';
    case TWO_LIMITED_COVERAGE = 'limited_2_coverage';
    case ONE_LIMITED = 'limited_1';
    case ONE_LIMITED_COVERAGE = 'limited_1_coverage';
    case NO_LIMITED_COVERAGE = 'limited_0_coverage';
    case NO_LIMITED = 'no_limited';
    case SOME_LIMITED = 'limited_some';
    case TOTAL_CAMPAIGNS = 'total_campaigns';
    case CAMPAIGN_INFO = 'campaign_info';
    case POPULATION = 'population';

    case TOTAL_LEADS = 'total_leads';
    case TOTAL_REVENUE = 'total_revenue';
    case TOTAL_AD_COST = 'total_ad_cost';
    case GROSS_PROFIT = 'gross_profit';
    case GOOGLE_REVENUE = 'google_revenue';
    case GOOGLE_AD_COST = 'google_ad_cost';
    case SOLD_LEGS_PER_SOLD_LEAD = 'sold_legs_per_sold_lead';
    case RAW_TO_GTS_RATIO = 'raw_to_gts_ratio';
    case GTS_TO_SOLD_RATIO = 'gts_to_sold_ratio';
    case REVENUE_PER_RAW_LEAD = 'rev_per_raw_lead';
    case REVENUE_PER_GTS_LEAD = 'rev_per_gts_lead';
    case REVENUE_PER_SOLD_LEAD = 'rev_per_sold_lead';
    case REVENUE_PER_SOLD_LEG = 'rev_per_sold_leg';
    case GOOGLE_AD_COST_PER_RAW_LEAD = 'google_ad_cost_per_raw_lead';
    case GOOGLE_AD_COST_PER_GTS_LEAD = 'google_ad_cost_per_gts_lead';
    case GOOGLE_AD_COST_PER_SOLD_LEAD = 'google_ad_cost_per_sold_lead';
    case GOOGLE_AD_COST_PER_SOLD_LEG  = 'google_ad_cost_per_sold_leg';
    case GOOGLE_REVENUE_PER_RAW_LEAD  = 'google_revenue_per_raw_lead';
    case GOOGLE_REVENUE_PER_SMS_VERIFIED_LEAD = 'google_revenue_per_sms_verified_lead';
    case GOOGLE_TIERED_ADVERTISING_TIER = 'google_tiered_advertising_tier';
    case GOOGLE_TIERED_ADVERTISING_BID = 'google_tiered_advertising_bid';
    case META_TIERED_ADVERTISING_TIER = 'meta_tiered_advertising_tier';
    case META_TIERED_ADVERTISING_BID = 'meta_tiered_advertising_bid';
    case MICROSOFT_TIERED_ADVERTISING_TIER = 'microsoft_tiered_advertising_tier';
    case MICROSOFT_TIERED_ADVERTISING_BID = 'microsoft_tiered_advertising_bid';
    case ESTIMATED_REVENUE_PER_LEAD = 'estimated_revenue_per_lead';
    case TIERED_ADS_CALCULATED_TCPA = 'tiered_ads_calculated_tcpa';

    const string NAME_KEY       = 'name';
    const string TYPE_KEY       = 'type';
    const string DEFAULT_KEY    = 'default';
    const string CALCULATED_KEY = 'calculated';
    const string DATA_KEY       = 'report_data';
    const string CLASS_KEY      = 'class';
    const string QUERY_KEY      = 'query';
    const string TOTAL_KEY      = 'total';
    const string TEXT_KEY       = 'text';
    const string IF_KEY         = 'if';
    const string LINK_KEY       = 'link';
    const string ORDER          = 'order';
    const string INDUSTRY_ID    = 'industry_id';

    const string FIRST_COLUMN   = 'first_column';
    const string OPERATOR       = 'operator';
    const string SECOND_COLUMN  = 'second_column';

    const string TOTAL_COLUMN_SUM = 'sum';
    const string TOTAL_COLUMN_AVG = 'avg';
    const string TOTAL_COLUMN_NONE = 'none';

    const string COLUMN_TYPE_COUNT = 'count';
    const string COLUMN_TYPE_DOLLAR = 'dollar';
    const string COLUMN_TYPE_DECIMAL = 'decimal';
    const string COLUMN_TYPE_PERCENT = 'percent';
    const string COLUMN_TYPE_STRING = 'string';
    const string COLUMN_TYPE_INFO_STRING = 'info_string';

    const string CONCAT_OPERATOR = 'CONCAT';
    const string CONCAT_FIELDS = 'concat_fields';
    const string CONCAT_TYPE_COL = 'col';
    const string CONCAT_TYPE_STR = 'str';

    const string GREEN  = 'text-green-500';
    const string BLUE   = 'text-blue-500';

    /**
     * @return array
     */
    public function getMetaData(): array
    {
        return match ($this) {
            self::STATE => [
                self::NAME_KEY          => 'State',
                self::TYPE_KEY          => self::COLUMN_TYPE_STRING,
                self::DEFAULT_KEY       => true,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_NONE,
                self::CLASS_KEY         => '',
                self::CALCULATED_KEY    => null,
            ],
            self::COUNTY => [
                self::NAME_KEY          => 'County',
                self::TYPE_KEY          => self::COLUMN_TYPE_STRING,
                self::DEFAULT_KEY       => true,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_NONE,
                self::CLASS_KEY         => '',
                self::CALCULATED_KEY    => null,
            ],
            self::TOTAL_ZIP_CODES => [
                self::NAME_KEY          => 'Total Zip Codes',
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => true,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::CLASS_KEY         => '',
                self::CALCULATED_KEY    => null,
            ],
            self::THREE_CAMPAIGNS => [
                self::NAME_KEY       => 'Zip Codes with 3+ Campaigns',
                self::TYPE_KEY       => self::COLUMN_TYPE_INFO_STRING,
                self::DEFAULT_KEY    => true,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_NONE,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => [
                    self::OPERATOR      => self::CONCAT_OPERATOR,
                    self::CONCAT_FIELDS => [
                        "$[if[" . self::THREE_UNLIMITED->value . ",>,0]" => self::CONCAT_TYPE_STR,
                        "text["                                          => self::CONCAT_TYPE_STR,
                        self::THREE_UNLIMITED->value                     => self::CONCAT_TYPE_COL,
                        " Unlimited - "                                  => self::CONCAT_TYPE_STR,
                        self::THREE_UNLIMITED_COVERAGE->value            => self::CONCAT_TYPE_COL,
                        "],class[text-green-500]]$\n"                    => self::CONCAT_TYPE_STR,
                        "$[if[" . self::THREE_LIMITED->value . ",>,0]"   => self::CONCAT_TYPE_STR,
                        "link[],text["                                   => self::CONCAT_TYPE_STR,
                        self::THREE_LIMITED->value                       => self::CONCAT_TYPE_COL,
                        " Limited - "                                    => self::CONCAT_TYPE_STR,
                        self::THREE_LIMITED_COVERAGE->value              => self::CONCAT_TYPE_COL,
                        "],class[text-blue-500]]$"                       => self::CONCAT_TYPE_STR,
                    ],
                ],
            ],
            self::TWO_CAMPAIGNS => [
                self::NAME_KEY       => 'Zip Codes with 2 Campaigns',
                self::TYPE_KEY       => self::COLUMN_TYPE_INFO_STRING,
                self::DEFAULT_KEY    => true,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_NONE,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => [
                    self::OPERATOR      => self::CONCAT_OPERATOR,
                    self::CONCAT_FIELDS => [
                        "$[if[" . self::TWO_UNLIMITED->value . ",>,0]" => self::CONCAT_TYPE_STR,
                        "text["                                        => self::CONCAT_TYPE_STR,
                        self::TWO_UNLIMITED->value                     => self::CONCAT_TYPE_COL,
                        " Unlimited - "                                => self::CONCAT_TYPE_STR,
                        self::TWO_UNLIMITED_COVERAGE->value            => self::CONCAT_TYPE_COL,
                        "],class[text-green-500]]$\n"                  => self::CONCAT_TYPE_STR,
                        "$[if[" . self::TWO_LIMITED->value . ",>,0]"   => self::CONCAT_TYPE_STR,
                        "link[],text["                                 => self::CONCAT_TYPE_STR,
                        self::TWO_LIMITED->value                       => self::CONCAT_TYPE_COL,
                        " Limited - "                                  => self::CONCAT_TYPE_STR,
                        self::TWO_LIMITED_COVERAGE->value              => self::CONCAT_TYPE_COL,
                        "],class[text-blue-500]]$"                     => self::CONCAT_TYPE_STR,
                    ],
                ],
            ],
            self::ONE_CAMPAIGN => [
                self::NAME_KEY       => 'Zip Codes with 1 Campaign',
                self::TYPE_KEY       => self::COLUMN_TYPE_INFO_STRING,
                self::DEFAULT_KEY    => true,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_NONE,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => [
                    self::OPERATOR      => self::CONCAT_OPERATOR,
                    self::CONCAT_FIELDS => [
                        "$[if[" . self::ONE_UNLIMITED->value . ",>,0]" => self::CONCAT_TYPE_STR,
                        "text["                                        => self::CONCAT_TYPE_STR,
                        self::ONE_UNLIMITED->value                     => self::CONCAT_TYPE_COL,
                        " Unlimited - "                                => self::CONCAT_TYPE_STR,
                        self::ONE_UNLIMITED_COVERAGE->value            => self::CONCAT_TYPE_COL,
                        "],class[text-green-500]]$\n"                  => self::CONCAT_TYPE_STR,
                        "$[if[" . self::ONE_LIMITED->value . ",>,0]"   => self::CONCAT_TYPE_STR,
                        "link[],text["                                 => self::CONCAT_TYPE_STR,
                        self::ONE_LIMITED->value                       => self::CONCAT_TYPE_COL,
                        " Limited - "                                  => self::CONCAT_TYPE_STR,
                        self::ONE_LIMITED_COVERAGE->value              => self::CONCAT_TYPE_COL,
                        "],class[text-blue-500]]$"                     => self::CONCAT_TYPE_STR,
                    ],
                ],
            ],
            self::NO_CAMPAIGN => [
                self::NAME_KEY       => 'Zip Codes with No Campaigns',
                self::TYPE_KEY       => self::COLUMN_TYPE_INFO_STRING,
                self::DEFAULT_KEY    => true,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_NONE,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => [
                    self::OPERATOR      => self::CONCAT_OPERATOR,
                    self::CONCAT_FIELDS => [
                        "$[if[" . self::NO_UNLIMITED->value . ",>,0]" => self::CONCAT_TYPE_STR,
                        "text["                                       => self::CONCAT_TYPE_STR,
                        self::NO_UNLIMITED->value                     => self::CONCAT_TYPE_COL,
                        " Unlimited - "                               => self::CONCAT_TYPE_STR,
                        self::NO_UNLIMITED_COVERAGE->value            => self::CONCAT_TYPE_COL,
                        "],class[]]$\n"                               => self::CONCAT_TYPE_STR,
                        "$[if[" . self::NO_LIMITED->value . ",>,0]"   => self::CONCAT_TYPE_STR,
                        "link[],text["                                => self::CONCAT_TYPE_STR,
                        self::NO_LIMITED->value                       => self::CONCAT_TYPE_COL,
                        " Limited - "                                 => self::CONCAT_TYPE_STR,
                        self::NO_LIMITED_COVERAGE->value              => self::CONCAT_TYPE_COL,
                        "],class[]]$"                                 => self::CONCAT_TYPE_STR,
                    ],
                ],
            ],
            self::THREE_UNLIMITED => [
                self::NAME_KEY          => 'Three or More Unlimited',
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY    => null,
            ],
            self::THREE_UNLIMITED_COVERAGE => [
                self::NAME_KEY       => 'Three or More Unlimited Coverage',
                self::TYPE_KEY       => self::COLUMN_TYPE_PERCENT,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_AVG,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => [
                    self::FIRST_COLUMN  => self::THREE_UNLIMITED,
                    self::OPERATOR      => '/',
                    self::SECOND_COLUMN => self::TOTAL_ZIP_CODES,
                ],
            ],
            self::TWO_UNLIMITED => [
                self::NAME_KEY       => 'Two Unlimited',
                self::TYPE_KEY       => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_SUM,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
            ],
            self::TWO_UNLIMITED_COVERAGE => [
                self::NAME_KEY       => 'Two Unlimited Coverage',
                self::TYPE_KEY       => self::COLUMN_TYPE_PERCENT,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_AVG,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => [
                    self::FIRST_COLUMN  => self::TWO_UNLIMITED,
                    self::OPERATOR      => '/',
                    self::SECOND_COLUMN => self::TOTAL_ZIP_CODES,
                ],
            ],
            self::ONE_UNLIMITED => [
                self::NAME_KEY       => 'One Unlimited',
                self::TYPE_KEY       => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_SUM,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
            ],
            self::ONE_UNLIMITED_COVERAGE => [
                self::NAME_KEY       => 'One Unlimited Coverage',
                self::TYPE_KEY       => self::COLUMN_TYPE_PERCENT,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_AVG,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => [
                    self::FIRST_COLUMN  => self::ONE_UNLIMITED,
                    self::OPERATOR      => '/',
                    self::SECOND_COLUMN => self::TOTAL_ZIP_CODES,
                ],
            ],
            self::NO_UNLIMITED => [
                self::NAME_KEY       => 'No Unlimited',
                self::TYPE_KEY       => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_SUM,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => [
                    self::FIRST_COLUMN  => self::TOTAL_ZIP_CODES,
                    self::OPERATOR      => '-',
                    self::SECOND_COLUMN => self::SOME_UNLIMITED,
                ],
            ],
            self::NO_UNLIMITED_COVERAGE => [
                self::NAME_KEY       => 'No Unlimited Coverage',
                self::TYPE_KEY       => self::COLUMN_TYPE_PERCENT,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_AVG,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => [
                    self::FIRST_COLUMN  => self::NO_UNLIMITED,
                    self::OPERATOR      => '/',
                    self::SECOND_COLUMN => self::TOTAL_ZIP_CODES,
                ],
            ],
            self::SOME_UNLIMITED => [
                self::NAME_KEY       => 'Some Unlimited',
                self::TYPE_KEY       => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_SUM,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
            ],
            self::THREE_LIMITED => [
                self::NAME_KEY       => 'Three or More Limited',
                self::TYPE_KEY       => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_SUM,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
            ],
            self::THREE_LIMITED_COVERAGE => [
                self::NAME_KEY       => 'Three Limited Coverage',
                self::TYPE_KEY       => self::COLUMN_TYPE_PERCENT,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_AVG,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => [
                    self::FIRST_COLUMN  => self::THREE_LIMITED,
                    self::OPERATOR      => '/',
                    self::SECOND_COLUMN => self::TOTAL_ZIP_CODES,
                ],
            ],
            self::TWO_LIMITED => [
                self::NAME_KEY       => 'Two Limited',
                self::TYPE_KEY       => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_SUM,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
            ],
            self::TWO_LIMITED_COVERAGE => [
                self::NAME_KEY       => 'Two Limited Coverage',
                self::TYPE_KEY       => self::COLUMN_TYPE_PERCENT,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_AVG,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => [
                    self::FIRST_COLUMN  => self::TWO_LIMITED,
                    self::OPERATOR      => '/',
                    self::SECOND_COLUMN => self::TOTAL_ZIP_CODES,
                ],
            ],
            self::ONE_LIMITED => [
                self::NAME_KEY       => 'One Limited',
                self::TYPE_KEY       => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_SUM,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
            ],
            self::ONE_LIMITED_COVERAGE => [
                self::NAME_KEY       => 'One Limited Coverage',
                self::TYPE_KEY       => self::COLUMN_TYPE_PERCENT,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_AVG,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => [
                    self::FIRST_COLUMN  => self::ONE_LIMITED,
                    self::OPERATOR      => '/',
                    self::SECOND_COLUMN => self::TOTAL_ZIP_CODES,
                ],
            ],
            self::NO_LIMITED => [
                self::NAME_KEY       => 'No Limited',
                self::TYPE_KEY       => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_SUM,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => [
                    self::FIRST_COLUMN  => self::TOTAL_ZIP_CODES,
                    self::OPERATOR      => '-',
                    self::SECOND_COLUMN => self::SOME_LIMITED,
                ],
            ],
            self::NO_LIMITED_COVERAGE => [
                self::NAME_KEY       => 'No Limited Coverage',
                self::TYPE_KEY       => self::COLUMN_TYPE_PERCENT,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_AVG,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => [
                    self::FIRST_COLUMN  => self::NO_LIMITED,
                    self::OPERATOR      => '/',
                    self::SECOND_COLUMN => self::TOTAL_ZIP_CODES,
                ],
            ],
            self::SOME_LIMITED => [
                self::NAME_KEY       => 'Some Limited',
                self::TYPE_KEY       => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_SUM,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
            ],
            self::TOTAL_CAMPAIGNS => [
                self::NAME_KEY       => 'Total Campaigns',
                self::TYPE_KEY       => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY    => true,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_AVG,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
            ],
            self::CAMPAIGN_INFO => [
                self::NAME_KEY       => 'Campaign Info',
                self::TYPE_KEY       => self::COLUMN_TYPE_INFO_STRING,
                self::DEFAULT_KEY    => true,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_NONE,
                self::CLASS_KEY      => 'text-left',
                self::CALCULATED_KEY => null,
                self::QUERY_KEY      => 'GROUP_CONCAT(DISTINCT CONCAT('.
                    self::infoString(
                        text:
                            'CASE '.
                                'WHEN '.CountyCoverageReportRepository::BUDGET_TYPE.' = '.BudgetType::NO_LIMIT->value.' THEN \'Unlimited - \' '.
                                'WHEN '.CountyCoverageReportRepository::BUDGET_TYPE.' = '.BudgetType::TYPE_DAILY_UNITS->value.' THEN CONCAT('.CountyCoverageReportRepository::BUDGET_VALUE.', \' leads - \') '.
                                'ELSE CONCAT(\'$\', '.CountyCoverageReportRepository::BUDGET_VALUE.', \' - \') END',
                        class: 'CASE WHEN '.CountyCoverageReportRepository::BUDGET_TYPE.' = '.BudgetType::NO_LIMIT->value.' THEN \''.CountyCoverageReportColumnEnum::GREEN.'\' ELSE \'font-semibold\' END',
                        concat: true,
                    ).
                    ', '.
                    self::infoString(
                        text: CountyCoverageReportRepository::COMPANY_NAME,
                        link: '\'/companies/\', '.CountyCoverageReportRepository::COMPANY_ID.', \'?campaign=\', '.CountyCoverageReportRepository::CAMPAIGN_REFERENCE,
                        class: '\''.self::BLUE.'\'',
                        concat: true,
                    ).
                    ', '.
                    self::infoString(
                        text: '\': \', '.CountyCoverageReportRepository::CAMPAIGN_NAME.', \' \'',
                        class: 'CASE WHEN '.CountyCoverageReportRepository::BUDGET_TYPE.' = '.BudgetType::NO_LIMIT->value.' THEN \''.CountyCoverageReportColumnEnum::GREEN.'\' ELSE \'\' END',
                        concat: true,
                    ).
                    ', \'(\', '.
                        'CASE '.
                            'WHEN '.CountyCoverageReportRepository::CAMPAIGN_ZIP_TARGETED.' = true '.
                            'OR '.CompanyConfiguration::FIELD_UNRESTRICTED_ZIP_CODE_TARGETING.' = true '.
                            'THEN '.CountyCoverageReportRepository::CAMPAIGN_ZIPS.' '.
                            'ELSE \'all\' END, \' zips,\', '.
                        'CASE '.
                            'WHEN '.CountyCoverageReportRepository::CAMPAIGN_STATUS.' = '.CampaignStatus::ACTIVE->value.' THEN \'active)\' '.
                            'WHEN '.CountyCoverageReportRepository::CAMPAIGN_STATUS.' = '.CampaignStatus::PAUSED_TEMPORARILY->value.' THEN \'temp pause)\' '.
                            'ELSE \'off)\' END'.
                    ') SEPARATOR \'\n\')',
            ],
            self::POPULATION => [
                self::NAME_KEY       => 'Population',
                self::TYPE_KEY       => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY    => true,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_SUM,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
            ],
            self::TOTAL_LEADS => [
                self::NAME_KEY       => 'Total Leads',
                self::TYPE_KEY       => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_SUM,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
                self::DATA_KEY       => true,
            ],
            self::TOTAL_REVENUE => [
                self::NAME_KEY       => 'Total Revenue',
                self::TYPE_KEY       => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_SUM,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
                self::DATA_KEY       => true,
            ],
            self::TOTAL_AD_COST => [
                self::NAME_KEY       => 'Total Ad Cost',
                self::TYPE_KEY       => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_SUM,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
                self::DATA_KEY       => true,
            ],
            self::GROSS_PROFIT => [
                self::NAME_KEY       => 'Gross Profit',
                self::TYPE_KEY       => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_SUM,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
                self::DATA_KEY       => true,
            ],
            self::GOOGLE_REVENUE => [
                self::NAME_KEY       => 'Google Revenue',
                self::TYPE_KEY       => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_SUM,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
                self::DATA_KEY       => true,
            ],
            self::RAW_TO_GTS_RATIO => [
                self::NAME_KEY       => 'Raw to GTS Ratio',
                self::TYPE_KEY       => self::COLUMN_TYPE_PERCENT,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_AVG,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
                self::DATA_KEY       => true,
            ],
            self::GTS_TO_SOLD_RATIO => [
                self::NAME_KEY       => 'GTS to Sold Ratio',
                self::TYPE_KEY       => self::COLUMN_TYPE_PERCENT,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_AVG,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
                self::DATA_KEY       => true,
            ],
            self::SOLD_LEGS_PER_SOLD_LEAD => [
                self::NAME_KEY       => 'Sold Legs per Sold Lead',
                self::TYPE_KEY       => self::COLUMN_TYPE_DECIMAL,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_AVG,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
                self::DATA_KEY       => true,
            ],
            self::REVENUE_PER_RAW_LEAD => [
                self::NAME_KEY       => 'Revenue per Raw Lead',
                self::TYPE_KEY       => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_AVG,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
                self::DATA_KEY       => true,
            ],
            self::REVENUE_PER_GTS_LEAD => [
                self::NAME_KEY       => 'Revenue per GTS Lead',
                self::TYPE_KEY       => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_AVG,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
                self::DATA_KEY       => true,
            ],
            self::REVENUE_PER_SOLD_LEAD => [
                self::NAME_KEY       => 'Revenue per Sold Lead',
                self::TYPE_KEY       => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_AVG,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
                self::DATA_KEY       => true,
            ],
            self::REVENUE_PER_SOLD_LEG => [
                self::NAME_KEY       => 'Revenue per Sold Leg',
                self::TYPE_KEY       => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_AVG,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
                self::DATA_KEY       => true,
            ],
            self::GOOGLE_AD_COST_PER_RAW_LEAD => [
                self::NAME_KEY       => 'Google Ad Cost per Raw Lead',
                self::TYPE_KEY       => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_AVG,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
                self::DATA_KEY       => true,
            ],
            self::GOOGLE_AD_COST_PER_GTS_LEAD => [
                self::NAME_KEY       => 'Google Ad Cost per GTS Lead',
                self::TYPE_KEY       => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_AVG,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
                self::DATA_KEY       => true,
            ],
            self::GOOGLE_AD_COST_PER_SOLD_LEAD => [
                self::NAME_KEY       => 'Google Ad Cost per Sold Lead',
                self::TYPE_KEY       => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_AVG,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
                self::DATA_KEY       => true,
            ],
            self::GOOGLE_AD_COST_PER_SOLD_LEG => [
                self::NAME_KEY       => 'Google Ad Cost per Sold Leg',
                self::TYPE_KEY       => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_AVG,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
                self::DATA_KEY       => true,
            ],
            self::GOOGLE_REVENUE_PER_RAW_LEAD => [
                self::NAME_KEY       => 'Google Revenue per Raw Lead',
                self::TYPE_KEY       => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_AVG,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
                self::DATA_KEY       => true,
            ],
            self::GOOGLE_REVENUE_PER_SMS_VERIFIED_LEAD => [
                self::NAME_KEY       => 'Google Revenue per SMS Verified Lead',
                self::TYPE_KEY       => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_AVG,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
                self::DATA_KEY       => true,
            ],
            self::GOOGLE_TIERED_ADVERTISING_TIER => [
                self::NAME_KEY       => 'Google Advertising Tier',
                self::TYPE_KEY       => self::COLUMN_TYPE_INFO_STRING,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_NONE,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
            ],
            self::GOOGLE_TIERED_ADVERTISING_BID => [
                self::NAME_KEY       => 'Google Advertising Bid',
                self::TYPE_KEY       => self::COLUMN_TYPE_INFO_STRING,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_NONE,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
            ],
            self::META_TIERED_ADVERTISING_TIER => [
                self::NAME_KEY       => 'Meta Advertising Tier',
                self::TYPE_KEY       => self::COLUMN_TYPE_INFO_STRING,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_NONE,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
            ],
            self::META_TIERED_ADVERTISING_BID => [
                self::NAME_KEY       => 'Meta Advertising Bid',
                self::TYPE_KEY       => self::COLUMN_TYPE_INFO_STRING,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_NONE,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
            ],
            self::MICROSOFT_TIERED_ADVERTISING_TIER => [
                self::NAME_KEY       => 'Microsoft Advertising Tier',
                self::TYPE_KEY       => self::COLUMN_TYPE_INFO_STRING,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_NONE,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
            ],
            self::MICROSOFT_TIERED_ADVERTISING_BID => [
                self::NAME_KEY       => 'Microsoft Advertising Bid',
                self::TYPE_KEY       => self::COLUMN_TYPE_INFO_STRING,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_NONE,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
            ],
            self::ESTIMATED_REVENUE_PER_LEAD => [
                self::NAME_KEY       => 'Estimated Revenue per Lead',
                self::TYPE_KEY       => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_AVG,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
            ],
            self::TIERED_ADS_CALCULATED_TCPA => [
                self::NAME_KEY       => 'Tiered Ads Calculated TCPA',
                self::TYPE_KEY       => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_AVG,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
            ],
            default => [
                self::NAME_KEY       => 'Unknown',
                self::TYPE_KEY       => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY    => false,
                self::TOTAL_KEY      => self::TOTAL_COLUMN_NONE,
                self::CLASS_KEY      => '',
                self::CALCULATED_KEY => null,
            ]
        };
    }

    /**
     * @return CountyCoverageReportColumnEnum[]
     */
    public static function getColumnsOrder(): array
    {
        return [
            self::COUNTY,
            self::STATE,
            self::TOTAL_ZIP_CODES,
            self::NO_CAMPAIGN,
            self::ONE_CAMPAIGN,
            self::TWO_CAMPAIGNS,
            self::THREE_CAMPAIGNS,
            self::THREE_UNLIMITED_COVERAGE,
            self::TWO_UNLIMITED_COVERAGE,
            self::ONE_UNLIMITED_COVERAGE,
            self::TOTAL_CAMPAIGNS,
            self::CAMPAIGN_INFO,
            self::POPULATION,
            self::TOTAL_LEADS,
            self::TOTAL_REVENUE,
            self::TOTAL_AD_COST,
            self::GROSS_PROFIT,
            self::GOOGLE_REVENUE,
            self::RAW_TO_GTS_RATIO,
            self::GTS_TO_SOLD_RATIO,
            self::SOLD_LEGS_PER_SOLD_LEAD,
            self::REVENUE_PER_RAW_LEAD,
            self::REVENUE_PER_GTS_LEAD,
            self::REVENUE_PER_SOLD_LEAD,
            self::ESTIMATED_REVENUE_PER_LEAD,
            self::GOOGLE_REVENUE_PER_SMS_VERIFIED_LEAD,
            self::TIERED_ADS_CALCULATED_TCPA,
            self::REVENUE_PER_SOLD_LEG,
            self::GOOGLE_AD_COST_PER_RAW_LEAD,
            self::GOOGLE_AD_COST_PER_GTS_LEAD,
            self::GOOGLE_AD_COST_PER_SOLD_LEAD,
            self::GOOGLE_AD_COST_PER_SOLD_LEG,
            self::GOOGLE_REVENUE_PER_RAW_LEAD,
            self::GOOGLE_TIERED_ADVERTISING_TIER,
            self::GOOGLE_TIERED_ADVERTISING_BID,
            self::META_TIERED_ADVERTISING_TIER,
            self::META_TIERED_ADVERTISING_BID,
            self::MICROSOFT_TIERED_ADVERTISING_TIER,
            self::MICROSOFT_TIERED_ADVERTISING_BID,
        ];
    }

    /**
     * @param CountyCoverageReportColumnEnum $column
     * @return array
     */
    public static function getDependencies(CountyCoverageReportColumnEnum $column): array
    {
        $dependenciesArray = [];
        self::getDependenciesArray($column, $dependenciesArray);
        return $dependenciesArray;
    }

    /**
     * @return string
     */
    public function getQuery(): string
    {
        $metaData = $this->getMetaData();
        return $metaData[self::QUERY_KEY] ?: '';
    }

    /**
     * @return array
     */
    public static function activeIndustries(): array
    {
        $industryModels = IndustryModel::query()
            ->join(IndustryConfiguration::TABLE, IndustryConfiguration::TABLE.'.'.IndustryConfiguration::FIELD_INDUSTRY_ID, '=', IndustryModel::TABLE.'.'.IndustryModel::FIELD_ID)
            ->where(IndustryConfiguration::TABLE.'.'.IndustryConfiguration::FIELD_FUTURE_CAMPAIGNS_ACTIVE, true)
            ->get([IndustryModel::TABLE.'.'.IndustryModel::FIELD_SLUG]);

        $industries = [];

        foreach ($industryModels as $industryModel)
        {
            $industries[] = Industry::fromSlug($industryModel->{IndustryModel::FIELD_SLUG});
        }
        return $industries;
    }

    /**
     * @param CountyCoverageReportColumnEnum $column
     * @param array $dependentOn
     * @return void
     */
    public static function getDependenciesArray(CountyCoverageReportColumnEnum $column, array &$dependentOn): void
    {
        if (in_array($column, self::getAllCalculatedColumns())) {
            $calculatedColumns = $column->getMetaData()[self::CALCULATED_KEY];
            if ($calculatedColumns[self::OPERATOR] === self::CONCAT_OPERATOR) {
                foreach ($calculatedColumns[self::CONCAT_FIELDS] as $concatField => $concatType) {
                    if ($concatType === self::CONCAT_TYPE_COL) {
                        $field = CountyCoverageReportColumnEnum::from($concatField);
                        $dependentOn[] = $field;
                        CountyCoverageReportColumnEnum::getDependenciesArray($field, $dependentOn);
                    }
                }
            } else {
                $dependentOn[] = $calculatedColumns[self::FIRST_COLUMN];
                CountyCoverageReportColumnEnum::getDependenciesArray($calculatedColumns[self::FIRST_COLUMN], $dependentOn);
                $dependentOn[] = $calculatedColumns[self::SECOND_COLUMN];
                CountyCoverageReportColumnEnum::getDependenciesArray($calculatedColumns[self::SECOND_COLUMN], $dependentOn);
            }
        }
    }

    /**
     * @return CountyCoverageReportColumnEnum[]
     */
    public static function getDefaultColumns(): array
    {
        return array_filter(self::cases(), function($case) {
            return $case->getMetaData()[self::DEFAULT_KEY];
        });
    }

    /**
     * @return array
     */
    public static function getAllCalculatedColumns(): array
    {
        return array_filter(self::cases(), function ($case) {
            return $case->getMetaData()[self::CALCULATED_KEY];
        });
    }

    /**
     * @return bool
     */
    public function isReportDataColumn(): bool
    {
        return $this->getMetaData()[self::DATA_KEY] ?? false;
    }

    /**
     * @param string|null $if
     * @param string|null $text
     * @param string|null $link
     * @param string|null $class
     * @param bool $concat
     * @return string
     */
    public static function infoString(
        string $if = null,
        string $text = null,
        string $link = null,
        string $class = null,
        bool $concat = false,
    ): string
    {
        $infoString = '';
        $concatStringOpen = '';
        $concatStringClose = '';

        if ($concat) {
            $infoString = $infoString.'\'';
            $concatStringOpen = '\', ';
            $concatStringClose = ', \'';

        }

        $infoString = $infoString.'$[';
        if ($if)
            $infoString = $infoString.self::IF_KEY.'['.$concatStringOpen.$if.$concatStringClose.']';
        if ($text)
            $infoString = $infoString.self::TEXT_KEY.'['.$concatStringOpen.$text.$concatStringClose.']';
        if ($class)
            $infoString = $infoString.self::CLASS_KEY.'['.$concatStringOpen.$class.$concatStringClose.']';
        if ($link)
            $infoString = $infoString.self::LINK_KEY.'['.$concatStringOpen.$link.$concatStringClose.']';
        $infoString = $infoString.']$';

        if ($concat)
            $infoString = $infoString.'\'';

        return $infoString;

    }

    /**
     * @return array
     */
    public static function getFormattedCases(): array
    {
        $formattedCases = [];
        foreach (self::cases() as $case) {
            $formattedCases[$case->value] = $case->getMetaData();
        }
        return $formattedCases;
    }
}
