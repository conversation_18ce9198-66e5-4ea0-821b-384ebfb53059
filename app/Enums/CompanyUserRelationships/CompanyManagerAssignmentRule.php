<?php

namespace App\Enums\CompanyUserRelationships;

enum CompanyManagerAssignmentRule: string
{
    const int FIRST_LEAD_DAY = 0;

    case BDM_UN_ASSIGNMENT_DAYS = 'bdm-un-assigment-days'; // default un-assignment
    case BDM_UN_ASSIGNMENT_DAYS_NOT_PURCHASED = 'bdm-un-assignment-not-purchased'; // not purchased for X days
    case OM_ASSIGNMENT_DAYS = 'om-assignment-days';
    case OM_UN_ASSIGNMENT_DAYS = 'om-un-assignment-days'; // default un-assignment
    case AM_SELECTION_DAYS = 'om-selection-days';
    case AM_ASSIGNMENT_DAYS = 'am-assignment-days';
    case BDM_NOTIFICATION_FIRST_NOT_PURCHASED_DAYS = 'bdm_notification_first_purchase_days';
    case BDM_NOTIFICATION_SECOND_NOT_PURCHASED_DAYS = 'bdm_notification_second_purchase_days';
    case BDM_NOTIFICATION_THIRD_NOT_PURCHASED_DAYS = 'bdm_notification_third_purchase_days';

    /**
     * @return int
     */
    public function days(): int
    {
        return match ($this) {
            self::BDM_UN_ASSIGNMENT_DAYS => 365,
            self::BDM_UN_ASSIGNMENT_DAYS_NOT_PURCHASED => 42,
            self::OM_ASSIGNMENT_DAYS => self::FIRST_LEAD_DAY,
            self::OM_UN_ASSIGNMENT_DAYS => 90,
            self::AM_SELECTION_DAYS => 45,
            self::AM_ASSIGNMENT_DAYS => 90,
            self::BDM_NOTIFICATION_FIRST_NOT_PURCHASED_DAYS => 20,
            self::BDM_NOTIFICATION_SECOND_NOT_PURCHASED_DAYS => 30,
            self::BDM_NOTIFICATION_THIRD_NOT_PURCHASED_DAYS => 40
        };
    }
}
