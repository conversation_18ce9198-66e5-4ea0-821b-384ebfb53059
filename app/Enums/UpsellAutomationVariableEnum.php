<?php

namespace App\Enums;

use App\Enums\Campaigns\CampaignStatus;
use App\Enums\Campaigns\Modules\Budget\BudgetType;
use App\Enums\Odin\Industry;
use App\Enums\Reports\CountyCoverageReport\CountyCoverageReportColumnEnum;
use App\Models\Odin\Industry as IndustryModel;
use App\Models\Odin\CompanyConfiguration;
use App\Models\Odin\IndustryConfiguration;
use App\Models\UpsellAutomationLog;
use App\Repositories\Reports\CountyCoverageReportRepository;

/**
 * Adding a column
 * - It is important to include the new column in all functions that the other columns are included in
 * - Calculated columns can be either string concat or computed
 * - Copy another existing column's format when adding a new one
 */
enum UpsellAutomationVariableEnum: string
{
    case AUTOMATION_ENABLED     = 'automation_enabled';
    case PAST_RANGE_HOURS       = 'past_range_hours';
    case TEST_MODE              = 'test_mode';
    case LOG_NAME               = 'log_name';
    case EXCLUDED_COMPANY_IDS   = 'excluded_company_ids';
    case MAX_PER_COMPANY        = 'max_per_company';
    case ACTIVE_START_UTC       = 'active_start_utc';
    case ACTIVE_END_UTC         = 'active_end_utc';
    case MIN_VALUE_INCREASE     = 'min_value_increase';
    case PRINT_LOG              = 'print_log';
    case AUTOMATION_DELAY_MINS  = 'automation_delay_mins';
    case KILL_SWITCH            = 'kill_switch';
    case BATCH                  = 'batch';
    case LEADS_PER_WORKER       = 'leads_per_worker';
    case NEW_COMPANY_DELAY_HRS  = 'new_company_delay_hrs';
    case NEW_COMPANY_INCLUDE_IDS = 'new_company_include_ids';
    case REACT_DAYS_COUNT       = 'react_days_count';

    // Meta Data Keys - each variable has these defined
    const string KEY_NAME       = 'name';
    const string KEY_DESC       = 'description';
    const string KEY_ARG        = 'argument';
    const string KEY_DEFAULT    = 'default';
    const string KEY_TYPE       = 'type';

    // Variable types - inputs will be cast to these
    const string TYPE_INT       = 'int';
    const string TYPE_STRING    = 'string';
    const string TYPE_FLOAT     = 'float';
    const string TYPE_BOOL      = 'bool';
    const string TYPE_LIST      = 'list';

    /**
     * @return array
     */
    public function getMetaData(): array
    {
        return match ($this) {
            self::AUTOMATION_ENABLED => [
                self::KEY_NAME          => 'Automation Enabled',
                self::KEY_DESC          => 'This flag must be true for the automation to run.',
                self::KEY_ARG           => null, // Null key arg means this is not an available command parameter
                self::KEY_DEFAULT       => false, // Must be enabled in global config management to run automatically
                self::KEY_TYPE          => self::TYPE_BOOL,
            ],
            self::AUTOMATION_DELAY_MINS => [
                self::KEY_NAME          => 'Automation Delay Minutes',
                self::KEY_DESC          => 'Delay between executions of the upselling automation.',
                self::KEY_ARG           => null, // Null key arg means this is not an available command parameter
                self::KEY_DEFAULT       => 119, // Default two hour intervals
                self::KEY_TYPE          => self::TYPE_INT,
            ],
            self::PAST_RANGE_HOURS => [
                self::KEY_NAME          => 'Past Range Hours',
                self::KEY_DESC          => 'How many hours in the past to go back for upsell attempt.',
                self::KEY_ARG           => 'past-range-hours', // This can be used in the command line arguments for upselling
                self::KEY_DEFAULT       => 48,
                self::KEY_TYPE          => self::TYPE_INT,
            ],
            self::TEST_MODE => [
                self::KEY_NAME          => 'Test Mode',
                self::KEY_DESC          => 'Upsell job will calculate all available legs to be upsold without actually allocating anything.',
                self::KEY_ARG           => 'test-mode',
                self::KEY_DEFAULT       => false,
                self::KEY_TYPE          => self::TYPE_BOOL,
            ],
            self::LOG_NAME => [
                self::KEY_NAME          => 'Log Name',
                self::KEY_DESC          => 'Name of the upsell automation log that will be created in upsell_automation_logs table.',
                self::KEY_ARG           => 'log-name',
                self::KEY_DEFAULT       => UpsellAutomationLog::DEFAULT_AUTOMATION_NAME,
                self::KEY_TYPE          => self::TYPE_STRING,
            ],
            self::EXCLUDED_COMPANY_IDS => [
                self::KEY_NAME          => 'Excluded Company IDs',
                self::KEY_DESC          => 'Comma separated list of company IDs, automation will exclude these companies if they are given.',
                self::KEY_ARG           => 'excluded-company-ids',
                self::KEY_DEFAULT       => [],
                self::KEY_TYPE          => self::TYPE_LIST,
            ],
            self::ACTIVE_START_UTC => [
                self::KEY_NAME          => 'Active Start UTC',
                self::KEY_DESC          => 'UTC Time start for automation. Defaults to 9 am PST Time.',
                self::KEY_ARG           => null,
                self::KEY_DEFAULT       => '17:00:00', // 9 AM PST
                self::KEY_TYPE          => self::TYPE_STRING,
            ],
            self::ACTIVE_END_UTC => [
                self::KEY_NAME          => 'Active End UTC',
                self::KEY_DESC          => 'UTC Time end for automation. Defaults to 5 pm EST Time.',
                self::KEY_ARG           => null,
                self::KEY_DEFAULT       => '22:00:00', // 5 PM EST
                self::KEY_TYPE          => self::TYPE_STRING,
            ],
            self::MIN_VALUE_INCREASE => [
                self::KEY_NAME          => 'Minimum Value Increase',
                self::KEY_DESC          => 'Minimum Value difference in upsold lead needed to trigger allocation.',
                self::KEY_ARG           => 'min-value-increase',
                self::KEY_DEFAULT       => 50.00,
                self::KEY_TYPE          => self::TYPE_FLOAT,
            ],
            self::MAX_PER_COMPANY => [
                self::KEY_NAME          => 'Max Leads Delivered to Each Company',
                self::KEY_DESC          => 'Maximum number of leads delivered to each company on a single execution of the upsell automation.',
                self::KEY_ARG           => 'max-per-company',
                self::KEY_DEFAULT       => 1,
                self::KEY_TYPE          => self::TYPE_INT,
            ],
            self::PRINT_LOG => [
                self::KEY_NAME          => 'Print Log',
                self::KEY_DESC          => 'Print upsell log contents to terminal during operation.',
                self::KEY_ARG           => 'print-log',
                self::KEY_DEFAULT       => false,
                self::KEY_TYPE          => self::TYPE_BOOL,
            ],
            self::KILL_SWITCH => [
                self::KEY_NAME          => 'Kill Switch',
                self::KEY_DESC          => 'Emergency Flag to disable automation during execution.',
                self::KEY_ARG           => null, // Null key arg means this is not an available command parameter
                self::KEY_DEFAULT       => false, // Setting to true in global configuration management will kill any running upsell logic
                self::KEY_TYPE          => self::TYPE_BOOL,
            ],
            self::LEADS_PER_WORKER => [
                self::KEY_NAME          => 'Leads Per Worker Job',
                self::KEY_DESC          => 'Number of leads passed to each worker job.',
                self::KEY_ARG           => 'leads-per-worker',
                self::KEY_DEFAULT       => 20,
                self::KEY_TYPE          => self::TYPE_INT,
            ],
            self::NEW_COMPANY_DELAY_HRS => [
                self::KEY_NAME          => 'New Company Delay Hours',
                self::KEY_DESC          => 'How long to delay considering a new company (or reactivated after 90+ days) for upselling.',
                self::KEY_ARG           => 'new-company-delay',
                self::KEY_DEFAULT       => 48,
                self::KEY_TYPE          => self::TYPE_INT,
            ],
            self::NEW_COMPANY_INCLUDE_IDS => [
                self::KEY_NAME          => 'New Company Include IDs',
                self::KEY_DESC          => 'Company IDs to bypass the 48 hr activation delay.',
                self::KEY_ARG           => 'new-company-include-ids',
                self::KEY_DEFAULT       => [],
                self::KEY_TYPE          => self::TYPE_LIST,
            ],
            self::REACT_DAYS_COUNT => [
                self::KEY_NAME          => 'React Days Count',
                self::KEY_DESC          => 'How many days since last lead to consider a company as a reactivation.',
                self::KEY_ARG           => 'react-days-count',
                self::KEY_DEFAULT       => 90,
                self::KEY_TYPE          => self::TYPE_INT,
            ],
            self::BATCH => [
                self::KEY_NAME          => 'Batch',
                self::KEY_DESC          => 'Process leads with workers jobs in a batch.',
                self::KEY_ARG           => null, // The --queue option can be given to the command to batch workers
                self::KEY_DEFAULT       => true, // If false, upsell job will process leads in main job linearly. Should only be false when using command and dispatch sync.
                self::KEY_TYPE          => self::TYPE_BOOL,
            ],
            default => [
                self::KEY_NAME          => 'Unknown',
                self::KEY_DESC          => 'Undefined upsell automation parameter.',
                self::KEY_ARG           => null,
                self::KEY_DEFAULT       => '',
                self::KEY_TYPE          => self::TYPE_STRING,
            ]
        };
    }

    /**
     * @param mixed $variable
     * @return mixed
     */
    public function castToType(mixed $variable): mixed
    {
        return match ($this->getMetaData()[self::KEY_TYPE]) {
            self::TYPE_INT      => (int)$variable,
            self::TYPE_STRING   => (string)$variable,
            self::TYPE_FLOAT    => (float)$variable,
            self::TYPE_BOOL     => filter_var($variable, FILTER_VALIDATE_BOOLEAN),
            self::TYPE_LIST     => $this->parseList($variable),
            default             => $variable,
        };
    }

    /**
     * @param mixed $variable
     * @return array
     */
    public function parseList(mixed $variable): array
    {
        if (is_string($variable))
            return explode(',', $variable);
        if (is_array($variable))
            return $variable;
        return [];
    }
}
