<?php

namespace App\Enums;

use App\Models\Legacy\EloquentUser;
use App\Models\Odin\CompanyUser;
use App\Models\User;

/**
 * Determine the user type and model for tracking actions executed during a session
 */
enum ActioningUserType: string
{
    case LEGACY_USER        = 'legacy';
    case ADMIN2_USER        = 'admin2';
    case ADMIN2_SHADOW_USER = 'admin2_shadower';
    case COMPANY_USER       = 'company_user';

    public function getModel(): string
    {
        return match ($this) {
            self::LEGACY_USER                           => EloquentUser::class,
            self::ADMIN2_USER, self::ADMIN2_SHADOW_USER => User::class,
            self::COMPANY_USER                          => CompanyUser::class,
        };
    }
}
