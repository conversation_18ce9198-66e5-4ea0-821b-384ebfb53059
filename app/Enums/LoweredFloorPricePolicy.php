<?php

namespace App\Enums;

enum LoweredFloorPricePolicy: int
{
    const string MODIFY_CAMPAIGNS_RECENCY_THRESHOLD = 'recent_purchase_threshold';

    case NO_ACTION            = 1;

    // When lowering a floor price, create bids at the previous price (ie campaigns will continue to use the old price)
    case CREATE_CAMPAIGN_BIDS = 2;

    // When lowering a floor price, delete matching scoped bids for campaigns (same location/quality/sale_type)
    case REMOVE_AFFECTED_CAMPAIGN_BIDS = 3;
    // When lowering a custom floor price, delete all existing bids from the campaign
    case REMOVE_ALL_CAMPAIGN_BIDS      = 4;

    /**
     * @return array|string[]
     */
    public function getRequiredPolicyFields(): array
    {
        return match($this) {
            self::CREATE_CAMPAIGN_BIDS => [self::MODIFY_CAMPAIGNS_RECENCY_THRESHOLD],
            default                    => [],
        };
    }

    /**
     * @return LoweredFloorPricePolicy[]
     */
    public static function getStandardFloorPricePolicies(): array
    {
        return [
            self::NO_ACTION,
            self::CREATE_CAMPAIGN_BIDS,
        ];
    }

    /**
     * @return array
     */
    public static function getCustomFloorPricePolicyLabels(): array
    {
        return [
            self::NO_ACTION->value                     => 'No Modification',
            self::REMOVE_AFFECTED_CAMPAIGN_BIDS->value => 'Remove affected bids',
            self::REMOVE_ALL_CAMPAIGN_BIDS->value      => 'Remove ALL bids',
        ];
    }

    /**
     * @return string[]
     */
    public static function getCustomFloorPricePolicyDescriptions(): array
    {
        return [
            self::NO_ACTION->value                     => "No campaign bids will be modified.\nThe campaign will only use the new price if they have no explicit bids currently in place.",
            self::REMOVE_AFFECTED_CAMPAIGN_BIDS->value => "Campaign bids for the updated locations/sale types will be removed.\nThe campaign will immediately use the new custom floor price.",
            self::REMOVE_ALL_CAMPAIGN_BIDS->value      => "ALL bids will be removed from the campaign(s).\nThe campaign will immediately use all custom prices",
        ];
    }
}
