<?php

namespace App\Enums\Campaigns;

enum CampaignFilterOperator: string
{
    case EQ = 'eq';
    case LT = 'lt';
    case LTE = 'lte';
    case GT = 'gt';
    case GTE = 'gte';
    case NET = 'net';


    public function symbol(): string
    {
        return match ($this) {
            self::EQ => '=',
            self::LT => '<',
            self::LTE => '<=',
            self::GT => '>',
            self::GTE => '>=',
            self::NET => '!=',
        };
    }

    /**
     * @param mixed $actual
     * @param mixed $expected
     *
     * @return bool
     */
    public function compare(mixed $actual, mixed $expected): bool
    {
        return match ($this) {
            self::EQ => $actual === $expected,
            self::LT => $actual < $expected,
            self::LTE => $actual <= $expected,
            self::GT => $actual > $expected,
            self::GTE => $actual >= $expected,
            self::NET => $actual !== $expected,
        };
    }
}
