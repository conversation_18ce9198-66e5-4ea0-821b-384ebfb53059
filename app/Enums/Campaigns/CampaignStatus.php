<?php

namespace App\Enums\Campaigns;

use App\EnumAttributes\Concerns\GetAttributesTrait;
use App\EnumAttributes\Description;

enum CampaignStatus: int
{
    use GetAttributesTrait;

    #[Description('Paused Permanently')]
    case PAUSED_PERMANENTLY = 0;

    #[Description('Paused Temporarily')]
    case PAUSED_TEMPORARILY = 1;

    #[Description('Active')]
    case ACTIVE = 2;

    public function getDisplayName(): string
    {
        return match($this) {
            self::PAUSED_PERMANENTLY => 'Paused Permanently',
            self::PAUSED_TEMPORARILY => 'Paused Temporarily',
            self::ACTIVE             => 'Active',
            default                  => 'Unknown Status'
        };
    }

    /**
     * @return array
     */
    public static function displayNames(): array
    {
        $output = [];
        foreach(self::cases() as $case) {
            $output[$case->value] = $case->getDisplayName();
        }

        return $output;
    }
}
