<?php

namespace App\Enums\Campaigns;

use Illuminate\Support\Str;

/**
 * When adding replacer keys, please add to all appropriate services from:
 *  - CRMFieldReplacerService (live CRM deliveries)
 *  - CRMFieldFakerReplacementService (test CRM deliveries)
 *  - CampaignHelpService (frontend help documentation)
 */
enum CRMFieldReplacerKey: string
{
    const string ALIAS_STATE_1             = 'state_name';
    const string ALIAS_ZIP_CODE_1          = 'zip';
    const string ALIAS_COMBINED_COMMENTS_1 = 'comments';
    const string ALIAS_COMBINED_COMMENTS_2 = 'description';
    const string ALIAS_PROPERTY_TYPE_1     = 'lead_category';
    const string ALIAS_PROPERTY_TYPE_2     = 'propertytype';

    case BRAND                      = 'brand';
    case DATE                       = 'date';
    case LEAD_SOURCE                = 'lead_source';
    case LEAD_INDUSTRY              = 'lead_industry';
    case LEAD_SERVICE               = 'lead_service';
    case FIRST_NAME                 = 'first_name';
    case LAST_NAME                  = 'last_name';
    case FULL_NAME                  = 'full_name';
    case ACCOUNT_NAME               = 'account_name';
    case EMAIL                      = 'email';
    case FULL_ADDRESS               = 'full_address';
    case ADDRESS                    = 'address';
    case ADDRESS_1                  = 'address_1';
    case ADDRESS_2                  = 'address_2';
    case CITY                       = 'city';
    case STATE_ABBR                 = 'state_abbr';
    case STATE                      = 'state';
    case ZIP_CODE                   = 'zip_code';
    case COUNTRY_ABBR               = 'country_abbr';
    case COUNTRY                    = 'country';
    case COMBINED_COMMENTS          = 'combined_comments';
    case ELECTRIC_SPEND             = 'electric_spend';
    case UTILITY_NAME               = 'utility_name';
    case LEAD_ID                    = 'lead_id';
    case ASSIGNMENT_ID              = 'assignment_id';
    case UNIVERSAL_LEAD_ID          = 'universal_lead_id';
    case LEAD_PRICE                 = 'lead_price';
    case NUMBER_OF_QUOTES_REQUESTED = 'number_of_quotes_requested';
    case PHONE                      = 'phone';
    case SYSTEM_SIZE                = 'system_size';
    case ORIGIN_URL                 = 'origin_url';
    case CAMPAIGN_NAME              = 'campaign_name';
    case LEAD_SALE_TYPE             = 'lead_sale_type';
    case LEAD_SALE_COUNT            = 'lead_sale_count';
    case PROPERTY_TYPE              = 'property_type';
    case LEAD_TYPE                  = 'lead_type';
    case COMPANY_NAME               = 'company_name';
    case COMPANY_USER_NAME          = 'company_user_name';
    case PRODUCT                    = 'product';
    case SR_ADDRESS_1               = 'sr_address_1';
    case SR_ADDRESS_2               = 'sr_address_2';
    case SR_SUPPORT_EMAIL           = 'sr_support_email';
    case SR_WEBSITE                 = 'sr_website';
    case SR_PHONE                   = 'sr_phone';
    case FIXR_ADDRESS_1             = 'fixr_address_1';
    case FIXR_ADDRESS_2             = 'fixr_address_2';
    case FIXR_SUPPORT_EMAIL         = 'fixr_support_email';
    case FIXR_WEBSITE               = 'fixr_website';
    case FIXR_PHONE                 = 'fixr_phone';
    case SHADE                      = 'shade';
    case BEST_TIME_TO_CALL          = 'best_time_to_call';
    case ROOF_ESTIMATE_LOW          = 'roof_estimate_low';
    case ROOF_ESTIMATE_MEDIAN       = 'roof_estimate_median';
    case ROOF_ESTIMATE_HIGH         = 'roof_estimate_high';
    /** @deprecated */
    case APPOINTMENT_DATE = 'appointment_date';
    /** @deprecated */
    case APPOINTMENT_TIME = 'appointment_time';
    /** @deprecated */
    case APPOINTMENT_TYPE    = 'appointment_type';
    case TRUSTED_FORM_URL    = 'trusted_form_url';
    case WATCHDOG_VIDEO_LINK = 'watchdog_video_link';
    case OPT_IN_NAME         = 'opt_in_name';
    case PUBLIC_COMMENTS     = 'public_comments';

    case LOW_SYSTEM_COST            = 'low_system_cost';
    case HIGH_SYSTEM_COST           = 'high_system_cost';
    case LOW_TAX_CREDIT             = 'low_federal_tax_credit';
    case HIGH_TAX_CREDIT            = 'high_federal_tax_credit';
    case LOW_COST_AFTER_INCENTIVES  = 'low_cost_after_incentives';
    case HIGH_COST_AFTER_INCENTIVES = 'high_cost_after_incentives';

    /**
     * @param string $key
     * @return ?CRMFieldReplacerKey
     */
    public static function tryFromWithAliases(string $key): ?CRMFieldReplacerKey
    {
        $enum = CRMFieldReplacerKey::tryFrom($key);

        return $enum ??
            match ($key) {
                self::ALIAS_STATE_1                                              => self::STATE,
                self::ALIAS_ZIP_CODE_1,                                          => self::ZIP_CODE,
                self::ALIAS_COMBINED_COMMENTS_1, self::ALIAS_COMBINED_COMMENTS_2 => self::COMBINED_COMMENTS,
                self::ALIAS_PROPERTY_TYPE_1, self::ALIAS_PROPERTY_TYPE_2         => self::PROPERTY_TYPE,
                default                                                          => null,
            };
    }

    /**
     * Get array intended for shortcode entry
     *
     * @return array
     */
    public static function getAllKeysAndNames(): array
    {
        return collect(CRMFieldReplacerKey::getAllDashboardKeys())
            ->map(fn(CRMFieldReplacerKey $item) => [
                'value' => $item->value,
                'label' => $item->getDisplayName(),
            ])->values()
            ->toArray();
    }

    /**
     * Filter out keys intended for internal use, or deprecated
     *
     * @return array
     */
    public static function getAllDashboardKeys(): array
    {
        $hidden = [
            self::SR_ADDRESS_1, self::SR_ADDRESS_2, self::SR_SUPPORT_EMAIL, self::SR_PHONE, self::SR_WEBSITE,
            self::FIXR_ADDRESS_1, self::FIXR_ADDRESS_2, self::FIXR_SUPPORT_EMAIL, self::FIXR_PHONE, self::FIXR_WEBSITE,
            self::APPOINTMENT_TIME, self::APPOINTMENT_DATE, self::APPOINTMENT_TYPE,
        ];

        return array_filter(CRMFieldReplacerKey::cases(), fn(CRMFieldReplacerKey $key) => !in_array($key, $hidden));
    }

    /**
     * @return string
     */
    public function getDisplayName(): string
    {
        return match ($this) {
            self::DATE         => 'Delivery Date',
            self::ADDRESS      => 'Street Address',
            self::STATE_ABBR   => 'State Abbreviation',
            self::COUNTRY_ABBR => 'Country Abbreviation',
            self::SHADE        => 'Amount of Roof Shade',
            default            => Str::headline($this->value),
        };
    }
}
