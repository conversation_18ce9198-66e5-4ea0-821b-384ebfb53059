<?php

namespace App\Enums\Campaigns;

use Illuminate\Support\Str;

enum PauseReason: string
{
    case BUDGET = "budget";
    case INSUFFICIENT_STAFF = "insufficient_staff";
    case TOO_BUSY = "too_busy";
    case PERFORMANCE_REVIEW = "performance_review";
    case OUTSIDE_BUSINESS_HOURS = "outside_business_hours";
    case OTHER = "other";

    /**
     * @return string
     */
    public function getDisplayName(): string
    {
        return Str::title(Str::replace('_', ' ', $this->value));
    }

    /**
     * @return array
     */
    public static function displayNames(): array
    {
        $displayNames = [];
        foreach(self::cases() as $case) {
            $displayNames[$case->value] = Str::title(Str::replace('_', ' ', $case->value));
        }

        return $displayNames;
    }
}
