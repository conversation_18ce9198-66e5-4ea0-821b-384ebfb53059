<?php

namespace App\Enums\Campaigns\Modules\Budget;

enum BudgetType: int
{
    case NO_LIMIT = 0;
    case TYPE_DAILY_UNITS = 1;
    case TYPE_DAILY_SPEND = 2;

    public function getDisplayName(): string
    {
        return match($this) {
            self::NO_LIMIT          => 'No Limit',
            self::TYPE_DAILY_UNITS  => 'Max Daily Products',
            self::TYPE_DAILY_SPEND  => 'Max Daily Spend',
        };
    }
}
