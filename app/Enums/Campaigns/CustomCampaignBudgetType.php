<?php

namespace App\Enums\Campaigns;

use App\Enums\Odin\Product as ProductEnum;
use App\Enums\ProductAssignment\BudgetProductConfigurationEnum;
use App\Strategies\Campaigns\StandardViableCampaignsStrategy;
use Illuminate\Support\Str;

/**
 * When adding custom budget types to be used by new Campaign definitions,
 * ensure they are added to allocation logic, or they will not receive products
 *
 * @see StandardViableCampaignsStrategy::getBudgetsThatCanAcceptSalesType()
 */
enum CustomCampaignBudgetType: string
{
    case EXCLUSIVE_ONLY_LEADS  = 'exclusive_only';
    case UNVERIFIED_ONLY_LEADS = 'unverified_only';

    /**
     * @return BudgetProductConfigurationEnum|null
     */
    public function getBudgetConfiguration(): ?BudgetProductConfigurationEnum
    {
        return match($this) {
            self::EXCLUSIVE_ONLY_LEADS  => BudgetProductConfigurationEnum::LEAD_EXCLUSIVE_ONLY,
            self::UNVERIFIED_ONLY_LEADS => BudgetProductConfigurationEnum::LEAD_UNVERIFIED,
        };
    }

    /**
     * @return string
     */
    public function displayName(): string
    {
        return match($this) {
            self::EXCLUSIVE_ONLY_LEADS  => 'Exclusive Leads Only',
            self::UNVERIFIED_ONLY_LEADS => 'Unverified Leads Only',
            default => Str::headline($this->value),
        };
    }

    /**
     * @param ProductEnum $product
     * @return array
     */
    public static function namesByProduct(ProductEnum $product): array
    {
        return match ($product) {
            ProductEnum::LEAD => [
                self::EXCLUSIVE_ONLY_LEADS->value  => self::EXCLUSIVE_ONLY_LEADS->displayName(),
                self::UNVERIFIED_ONLY_LEADS->value => self::UNVERIFIED_ONLY_LEADS->displayName(),
            ],
            default => [],
        };
    }

    /**
     * @return array
     */
    public static function allNamesByProduct(): array
    {
        return array_reduce(ProductEnum::cases(), function(array $output, ProductEnum $case) {
            $output[strtolower($case->value)] = self::namesByProduct($case);
            return $output;
        }, []);
    }
}
