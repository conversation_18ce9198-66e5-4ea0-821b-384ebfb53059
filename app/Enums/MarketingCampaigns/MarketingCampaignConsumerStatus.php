<?php

namespace App\Enums\MarketingCampaigns;

enum MarketingCampaignConsumerStatus: string
{
    case INITIALISED = 'initialised';
    case UPLOADED    = 'uploaded';
    case ARCHIVED    = 'archived';
    case ERROR       = 'error';
    case SENT        = 'sent';
    case QUEUED      = 'queued';
    case DELIVERED   = 'delivered';

    public static function sendable(): array
    {
        return [
            self::INITIALISED,
            self::UPLOADED,
        ];
    }
}
