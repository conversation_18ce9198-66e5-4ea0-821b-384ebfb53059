<?php

namespace App\Enums\MarketingCampaigns;

use App\Marketing\DefaultSendingStrategy;
use App\Marketing\DistributedSendingStrategy;
use App\Marketing\ListSendingStrategy;
use App\Marketing\SendingStrategyContract;
use App\Models\MarketingCampaign;
use Illuminate\Support\Arr;

enum SendingStrategy: string
{
    case DEFAULT     = 'default';
    case DISTRIBUTED = 'distributed';
    case LIST        = 'list';

    /**
     * @param MarketingCampaign $campaign
     * @return SendingStrategy
     */
    public static function fromCampaign(MarketingCampaign $campaign): SendingStrategy
    {
        $configuration = $campaign->configuration;

        $sendingStrategy = Arr::get($configuration, 'sending_strategy', self::DEFAULT->value);

        return self::tryFrom($sendingStrategy) ?? self::DEFAULT;
    }

    public function strategy(): SendingStrategyContract
    {
        return match ($this) {
            self::DEFAULT     => new DefaultSendingStrategy(),
            self::DISTRIBUTED => new DistributedSendingStrategy(),
            self::LIST        => new ListSendingStrategy(),
        };
    }
}
