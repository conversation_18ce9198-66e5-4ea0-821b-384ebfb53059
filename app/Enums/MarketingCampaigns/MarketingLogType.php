<?php

namespace App\Enums\MarketingCampaigns;

enum MarketingLogType: string
{
    case UNKNOWN                                = 'unknown_event';
    case ADD_USERS                              = 'add_users_request';
    case ADD_USERS_TO_CAMPAIGN_REQUEST          = 'add_users_to_campaign_request';
    case MAILCHIMP_ADD_USERS_RESPONSE           = 'add_users_to_mailchimp_response';
    case MAILCHIMP_ADD_USERS_TO_CAMPAIGN        = 'add_users_to_campaign_response';
    case MAILCHIMP_CREATE_SEGMENT_REQUEST       = 'create_segment_request';
    case MAILCHIMP_CREATE_CAMPAIGN_REQUEST      = 'mailchimp_create_campaign_request';
    case MAILCHIMP_UNMATCHED_EMAIL              = 'mailchimp_unmatched_email';
    case MAILCHIMP_LIST_SEGMENTS_REQUEST        = 'list_segments_request';
    case MAILCHIMP_REQUEST_CAMPAIGN_INFORMATION = 'mailchimp_request_campaign_information';
    case MAILCHIMP_BATCH_UPDATE                 = 'mailchimp_batch_update';
    case CALLBACK_RENDER_FAILED                 = 'callback_render_failed';
    case MARKETING_CAMPAIGN_SENT                = 'marketing_campaign_sent';
    case MARKETING_INTERNAL_SEND                = 'internal_send';
    case MARKETING_DRIP_CAMPAIGN                = 'drip';
    case WEBHOOK                                = 'webhook';
    case MARKETING_DOMAIN                       = 'marketing_domain';
    case CONSUMER_RESPONSE                      = 'consumer_response';
    case MARKETING_CAMPAIGN                     = 'marketing_campaign';
}
