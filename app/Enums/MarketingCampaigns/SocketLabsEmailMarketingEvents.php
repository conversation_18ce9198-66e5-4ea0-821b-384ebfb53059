<?php

namespace App\Enums\MarketingCampaigns;

use App\DTO\EmailService\OutgoingEmailDTO;
use App\Enums\Log\LogLevel;
use App\Services\MarketingCampaign\Events\ClickedEvent;
use App\Services\MarketingCampaign\Events\ComplaintEvent;
use App\Services\MarketingCampaign\Events\DeliveredEvent;
use App\Services\MarketingCampaign\Events\FailedEvent;
use App\Services\MarketingCampaign\Events\OpenedEvent;
use App\Services\MarketingCampaign\Events\UnsubscribedEvent;
use App\Services\MarketingCampaign\Events\MarketingCampaignConsumerMarketingEvent;
use App\Services\MarketingCampaign\MarketingLogService;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

enum SocketLabsEmailMarketingEvents: string
{
    case DELIVERED_EVENT_TYPE = 'Delivered';
    case FAILED_EVENT_TYPE    = 'Failed';
    case COMPLAINT_EVENT_TYPE = 'Complaint';
    case TRACKING_EVENT_TYPE  = 'Tracking';

    const int OPEN_EVENT        = 1;
    const int CLICK_EVENT       = 0;
    const int UNSUBSCRIBE_EVENT = 2;

    public function matchEvent(
        array $requestData,
        Collection $meta,
    ): MarketingCampaignConsumerMarketingEvent|null
    {
        $mcc = $meta->get(OutgoingEmailDTO::EMAIL_META_MCC_ID_KEY);

        if (empty($mcc)) {
            MarketingLogService::log(
                message: "Webhook Missing MCC ID",
                namespace: MarketingLogType::WEBHOOK,
                level: LogLevel::ERROR,
                context: $requestData,
            );

            return null;
        }

        return match ($this) {
            self::DELIVERED_EVENT_TYPE => new DeliveredEvent(marketingCampaignConsumerId: $mcc),
            self::COMPLAINT_EVENT_TYPE => new ComplaintEvent(marketingCampaignConsumerId: $mcc),
            self::TRACKING_EVENT_TYPE  => match ($requestData['TrackingType']) {
                self::OPEN_EVENT        => new OpenedEvent(marketingCampaignConsumerId: $mcc),
                self::CLICK_EVENT       => new ClickedEvent(marketingCampaignConsumerId: $mcc),
                self::UNSUBSCRIBE_EVENT => new UnsubscribedEvent(marketingCampaignConsumerId: $mcc),
            },
            self::FAILED_EVENT_TYPE    => new FailedEvent(
                marketingCampaignConsumerId: $mcc,
                failureType: Arr::get($requestData, 'FailureType'),
                failureReason: Arr::get($requestData, 'Reason'),
                otherData: $requestData,
            ),
            default                    => null,
        };
    }

}
