<?php

namespace App\Enums\MarketingCampaigns;

use App\Services\MarketingCampaign\CallbackPayload\BaseCallbackPayload;
use App\Services\MarketingCampaign\CallbackPayload\DirectAllocationPayload;
use App\Services\MarketingCampaign\CallbackPayload\SimpleSingleSlideCallbackPayload;
use App\Services\MarketingCampaign\CallbackPayload\SolarValidatePayload;

enum MarketingCampaignCallbackType: string
{
    case BASE                = 'base';
    case SIMPLE_SINGLE_SLIDE = 'simple_single_slide';
    case DIRECT_ALLOCATION   = 'direct_allocation';
    case SOLAR_VALIDATE      = 'solar_validate';

    public function getInputs(): array
    {
        return match ($this) {
            self::BASE                => BaseCallbackPayload::getInputs(),
            self::SIMPLE_SINGLE_SLIDE => SimpleSingleSlideCallbackPayload::getInputs(),
            self::DIRECT_ALLOCATION   => DirectAllocationPayload::getInputs(),
            self::SOLAR_VALIDATE      => SolarValidatePayload::getInputs(),
        };
    }
}
