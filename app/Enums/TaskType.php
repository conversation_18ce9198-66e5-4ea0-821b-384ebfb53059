<?php

namespace App\Enums;

use App\Models\Legacy\EloquentCompany;
use App\Models\Sales\Task;
use App\Transformers\ContactCompanyTransformer;

enum TaskType: string
{
    case CONTACT_COMPANY = 'contact_company';

    public function getModules(): array
    {
        return match ($this) {
            self::CONTACT_COMPANY => ["contact-company"]
        };
    }

    public function getSupplementaryData(Task $task): array
    {
        return match ($this) {
            self::CONTACT_COMPANY => (app()->make(ContactCompanyTransformer::class))->transform(EloquentCompany::query()->find($task->runningWorkflow->payload->event->get('company_id'))),
            default               => []
        };
    }
}
