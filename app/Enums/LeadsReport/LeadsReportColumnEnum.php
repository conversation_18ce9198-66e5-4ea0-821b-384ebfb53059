<?php

namespace App\Enums\LeadsReport;

use App\Enums\Advertising\AdvertisingPlatform;
use App\Enums\Campaigns\Modules\Budget\BudgetType;
use App\Enums\Campaigns\CampaignStatus;
use App\Enums\Odin\BudgetCategory;
use App\Models\Affiliates\Affiliate;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\CompanyUserRelationship;
use App\Models\DailyAdCost;
use App\Models\Legacy\Location;
use App\Models\Odin\Consumer;
use App\Models\Odin\Industry;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductAffiliateRecord;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCancellation;
use App\Models\Odin\ProductRejection;
use App\Models\PingPostPublishers\PingPostPublisherLead;
use Exception;
use Illuminate\Support\Str;

/**
 * Adding a column
 * - Create new case, new case value should be +1 from the highest existing case value (changing case values can affect user saved configs)
 * - Define Order in which case is displayed in the getColumnsOrder function
 * - Define Meta Data for column in the getMetaData function
 *      - self::KEY               => String key for column, cannot be the same as another column
 *      - self::NAME_KEY          => String name for column
 *      - self::TYPE_KEY          => Defines how column is displayed, ie as percent, dollar, decimal, ect.
 *      - self::DEFAULT_KEY       => If true column will be selected by default when report is opened
 *      - self::TOTAL_KEY         => Defines what the table will use for the total column function (avg, sum, or none)
 *      - self::DISABLED_FOR      => Can disable columns for certain group by selections that don't apply to the column
 *      - self::DEFAULT_FOR       => Group enums in this array will select this column when the group is selected in the report
 *      - self::CALCULATED_KEY    => Define a column based on the values of other columns, null for query columns
 *      - self::QUERY_TYPE_KEY    => Define which query the column belongs to (null for calculated columns)
 *      - self::QUERY_KEY         => The query used for the column value
 */
enum LeadsReportColumnEnum: int
{
    case ORGANIC_TOTAL_LEADS = 0;
    case ORGANIC_GTS_LEADS = 1;
    case ORGANIC_SOLD_LEADS = 2;
    case ORGANIC_SOLD_LEGS = 3;
    case ORGANIC_REVENUE = 4;
    case PAID_TOTAL_LEADS = 5;
    case PAID_GTS_LEADS = 6;
    case GOOGLE_PAID_GTS_LEADS = 7;
    case META_PAID_GTS_LEADS = 8;
    case MICROSOFT_PAID_GTS_LEADS = 9;
    case PAID_SOLD_LEADS = 10;
    case PAID_SOLD_LEGS = 11;
    case PAID_REVENUE = 12;
    case TOTAL_LEADS = 13;
    case TOTAL_GTS_LEADS = 14;
    case TOTAL_SOLD_LEADS = 15;
    case SOLD_TO_GTS_RATIO = 16;
    case TOTAL_SOLD_LEGS = 17;
    case SOLD_LEGS_PER_SOLD_LEAD = 18;
    case TOTAL_REVENUE = 19;
    case REVENUE_PER_SOLD_LEAD = 20;
    case REVENUE_PER_SOLD_LEG = 21;
    case DISTINCT_BUYERS = 22;
    case AD_COST_PER_PAID_GTS_LEAD = 23;
    case GROSS_PROFIT = 24;
    case TOTAL_AD_COST = 25;
    case GOOGLE_AD_COST = 26;
    case META_AD_COST = 27;
    case MICROSOFT_AD_COST = 28;
    case GOOGLE_AD_COST_PER_GTS_LEAD = 29;
    case META_AD_COST_PER_GTS_LEAD = 30;
    case MICROSOFT_AD_COST_PER_GTS_LEAD = 31;
    case TOTAL_REJECTED_LEGS = 32;
    case COMPANY = 33;
    case CAMPAIGN = 34;
    case STATE = 35;
    case COUNTY = 36;
    case CAMPAIGN_STATUS = 37;
    case CAMPAIGN_ZIP_CODE_TARGETED = 38;
    case CAMPAIGN_ZIP_CODES = 39;
    case CAMPAIGN_VERIFIED_BUDGET = 40;
    case CAMPAIGN_UNVERIFIED_BUDGET = 41;
    case CAMPAIGN_EMAIL_ONLY_BUDGET = 42;
    case CAMPAIGN_ZIP_CODE_COVERAGE = 43;
    case GOOGLE_REVENUE = 44;
    case GOOGLE_RAW_LEADS = 45;
    case GOOGLE_GTS_LEADS = 46;
    case GOOGLE_SOLD_LEADS = 47;
    case GOOGLE_SOLD_LEGS = 48;
    case GOOGLE_REVENUE_PER_RAW_LEAD = 49;
    case GOOGLE_SMS_VERIFIED_LEADS = 50;
    case GOOGLE_REVENUE_PER_SMS_VERIFIED_LEAD = 51;
    case GOOGLE_SUGGESTED_TCPA = 52;
    case GOOGLE_CPA = 53;
    case GOOGLE_DIFF_TCPA_AND_CPA = 54;
    case PROFIT_MARGIN = 55;
    case GOOGLE_SMS_VERIFIED_REVENUE = 56;
    case GOOGLE_GROSS_PROFIT = 57;
    case GOOGLE_ROAS = 58;
    case GOOGLE_PROFIT_MARGIN = 59;
    case AFFILIATE = 60;
    case COMPANY_ALL_TIME_REVENUE = 61;
    case TIME_FRAME_REVENUE = 62;
    case TIME_FRAME_AD_COST = 63;
    case PROPORTIONAL_REVENUE_PERCENT = 64;
    case PROPORTIONAL_AD_COST = 65;
    case PROPORTIONAL_GROSS_PROFIT = 66;
    case COMPANY_USER_RELATIONSHIP_TYPE = 67;
    case PING_POST_LEADS = 68;
    case PING_POST_REVENUE = 69;
    case GENERATED_STATE_COUNT = 70;
    case GENERATED_COUNTY_COUNT = 71;
    case GENERATED_ZIP_COUNT = 72;
    case SOLD_STATE_COUNT = 73;
    case SOLD_COUNTY_COUNT = 74;
    case SOLD_ZIP_COUNT = 75;


    const string KEY            = 'key';
    const string NAME_KEY       = 'name';
    const string TYPE_KEY       = 'type';
    const string QUERY_KEY      = 'query';
    const string QUERY_TYPE_KEY = 'query_type';
    const string DEFAULT_KEY    = 'default';
    const string CALCULATED_KEY = 'calculated';
    const string TOTAL_KEY      = 'total';
    const string DISABLED_FOR   = 'disabled_for';
    const string DEFAULT_FOR    = 'default_for';
    const string ORDER          = 'order';
    const string GROUP_KEY      = 'groupKey';
    const string COMPARE_GROUP  = 'compare_group';
    const string COMPARE_COST   = 'compare_group_cost';

    const string FIRST_COLUMN   = 'first_column';
    const string OPERATOR       = 'operator';
    const string SECOND_COLUMN  = 'second_column';

    const string TOTAL_COLUMN_SUM = 'sum';
    const string TOTAL_COLUMN_AVG = 'avg';
    const string TOTAL_COLUMN_NONE = 'none';

    const string COLUMN_TYPE_COUNT = 'count';
    const string COLUMN_TYPE_DOLLAR = 'dollar';
    const string COLUMN_TYPE_DECIMAL = 'decimal';
    const string COLUMN_TYPE_PERCENT = 'percent';
    const string COLUMN_TYPE_STRING = 'string';
    const string COLUMN_TYPE_INFO_STRING = 'info_string';

    const array GOOGLE_TRACK_NAMES      = ["'adwords'", "'gbraid'", "'google_ads_ios'"];
    const array META_TRACK_NAMES        = ["'facebook'"];
    const array MICROSOFT_TRACK_NAMES   = ["'bing_ads'"];
    const array ORGANIC_AFFILIATE_IDS   = [4587, 36180];

    const int TARGET_ROAS = 2;

    const string CONSUMERS_TABLE         = 'c';
    const string CONSUMER_PRODUCTS_TABLE = 'cp';
    const string PRODUCT_ASSIGN_TABLE    = 'pa';
    const string PRODUCT_REJECT_TABLE    = 'pr';
    const string PRODUCT_CANCEL_TABLE    = 'pc';
    const string CPAR_TABLE              = 'cpar';
    const string AFF_TABLE               = 'aff';
    const string CCLML_TABLE             = 'cclml';
    const string TOTAL_ZIP_TABLE         = 'tz';
    const string TOTAL_ZIP_COUNT         = 'total_zip_count';
    const string BUDGETS_TABLE           = 'b';
    const string DAILY_AD_COST_TABLE     = 'dac';
    const string COMPANIES_TABLE         = 'co';
    const string COMPANY_REV_TABLE       = 'co_rev';
    const string CAMPAIGNS_TABLE         = 'cc';
    const string INDUSTRY_TABLE          = 'i';
    const string LOCATIONS_TABLE         = 'l';
    const string TIME_FRAME_REV_TABLE    = 'tfr';
    const string TIME_FRAME_COST_TABLE   = 'tfc';

    const string BLUE   = 'text-blue-500';

    const string INFO_CLASS_KEY      = 'class';
    const string INFO_TEXT_KEY       = 'text';
    const string INFO_IF_KEY         = 'if';
    const string INFO_LINK_KEY       = 'link';

    const array NOT_APPLICABLE_FOR_AD_COST = [
        LeadsReportGroupEnum::COMPANY,
        LeadsReportGroupEnum::COMPANY_COUNTY,
        LeadsReportGroupEnum::CAMPAIGN,
        LeadsReportGroupEnum::CAMPAIGN_COUNTY,
        LeadsReportGroupEnum::CAMPAIGN_ZIP_CODE,
        LeadsReportGroupEnum::ZIP_CODE,
        LeadsReportGroupEnum::ORIGIN,
        LeadsReportGroupEnum::AFFILIATE,
        LeadsReportGroupEnum::AFFILIATE_CAMPAIGN,
        LeadsReportGroupEnum::COMPANY_USER_RELATIONSHIP,
    ];

    const array NOT_APPLICABLE_FOR_SOLD_LEGS = [
        LeadsReportGroupEnum::COMPANY,
        LeadsReportGroupEnum::COMPANY_COUNTY,
        LeadsReportGroupEnum::CAMPAIGN,
        LeadsReportGroupEnum::CAMPAIGN_COUNTY,
        LeadsReportGroupEnum::CAMPAIGN_ZIP_CODE,
    ];

    const array NOT_APPLICABLE_FOR_CAMPAIGNS = [
        LeadsReportGroupEnum::DAY,
        LeadsReportGroupEnum::WEEK,
        LeadsReportGroupEnum::MONTH,
        LeadsReportGroupEnum::INDUSTRY,
        LeadsReportGroupEnum::COMPANY,
        LeadsReportGroupEnum::COMPANY_COUNTY,
        LeadsReportGroupEnum::STATE,
        LeadsReportGroupEnum::COUNTY,
        LeadsReportGroupEnum::ZIP_CODE,
        LeadsReportGroupEnum::ORIGIN,
        LeadsReportGroupEnum::AFFILIATE,
        LeadsReportGroupEnum::AFFILIATE_CAMPAIGN,
        LeadsReportGroupEnum::PLATFORM,
    ];

    const array CAMPAIGN_GROUPS = [
        LeadsReportGroupEnum::CAMPAIGN,
        LeadsReportGroupEnum::CAMPAIGN_COUNTY,
        LeadsReportGroupEnum::CAMPAIGN_ZIP_CODE,
    ];

    /**
     * @param LeadsReportColumnEnum $columnEnum
     * @return string
     */
    public static function getKey(LeadsReportColumnEnum $columnEnum): string
    {
        return $columnEnum->getMetaData()[self::KEY] ?? '';
    }

    /**
     * @return array|null
     */
    public function getCalculateFields(): ?array
    {
        return $this->getMetaData()[self::CALCULATED_KEY] ?? null;
    }

    /**
     * @return LeadsReportColumnEnum[]
     */
    public static function getDefaultColumns(): array
    {
        return array_filter(self::cases(), function($case) {
            return $case->getMetaData()[self::DEFAULT_KEY];
        });
    }

    /**
     * Define which columns belong to the cost query vs base query
     * @param LeadsReportColumnEnum $columnEnum
     * @return array
     */
    public static function getQueryType(LeadsReportColumnEnum $columnEnum): array
    {
        return $columnEnum->getMetaData()[self::QUERY_TYPE_KEY] ?? [LeadsReportQueryEnum::LEAD];
    }

    /**
     * @return string
     * @throws Exception
     */
    public function getSelectQuery(): string
    {
        return $this->getMetaData()[self::QUERY_KEY] ?? "";
    }

        /**
     * @return LeadsReportColumnEnum[]
     */
    public static function getColumnsOrder(): array
    {
        return [
            self::CAMPAIGN,
            self::STATE,
            self::COUNTY,
            self::AFFILIATE,
            self::CAMPAIGN_ZIP_CODES,
            self::ORGANIC_TOTAL_LEADS,
            self::ORGANIC_GTS_LEADS,
            self::ORGANIC_SOLD_LEADS,
            self::ORGANIC_SOLD_LEGS,
            self::ORGANIC_REVENUE,
            self::PAID_TOTAL_LEADS,
            self::PAID_GTS_LEADS,
            self::GOOGLE_PAID_GTS_LEADS,
            self::META_PAID_GTS_LEADS,
            self::MICROSOFT_PAID_GTS_LEADS,
            self::PAID_SOLD_LEADS,
            self::PAID_SOLD_LEGS,
            self::PAID_REVENUE,
            self::TOTAL_LEADS,
            self::TOTAL_GTS_LEADS,
            self::TOTAL_SOLD_LEADS,
            self::TOTAL_SOLD_LEGS,
            self::TOTAL_REJECTED_LEGS,
            self::SOLD_LEGS_PER_SOLD_LEAD,
            self::REVENUE_PER_SOLD_LEAD,
            self::REVENUE_PER_SOLD_LEG,
            self::GENERATED_STATE_COUNT,
            self::SOLD_STATE_COUNT,
            self::GENERATED_COUNTY_COUNT,
            self::SOLD_COUNTY_COUNT,
            self::GENERATED_ZIP_COUNT,
            self::SOLD_ZIP_COUNT,
            self::PING_POST_LEADS,
            self::PING_POST_REVENUE,
            self::TOTAL_REVENUE,
            self::PROPORTIONAL_AD_COST,
            self::PROPORTIONAL_GROSS_PROFIT,
            self::COMPANY,
            self::COMPANY_USER_RELATIONSHIP_TYPE,
            self::COMPANY_ALL_TIME_REVENUE,
            self::TIME_FRAME_REVENUE,
            self::PROPORTIONAL_REVENUE_PERCENT,
            self::TIME_FRAME_AD_COST,
            self::DISTINCT_BUYERS,
            self::TOTAL_AD_COST,
            self::AD_COST_PER_PAID_GTS_LEAD,
            self::GOOGLE_AD_COST_PER_GTS_LEAD,
            self::META_AD_COST_PER_GTS_LEAD,
            self::MICROSOFT_AD_COST_PER_GTS_LEAD,
            self::SOLD_TO_GTS_RATIO,
            self::GROSS_PROFIT,
            self::PROFIT_MARGIN,
            self::GOOGLE_AD_COST,
            self::META_AD_COST,
            self::MICROSOFT_AD_COST,
            self::CAMPAIGN_STATUS,
            self::CAMPAIGN_ZIP_CODE_TARGETED,
            self::CAMPAIGN_VERIFIED_BUDGET,
            self::CAMPAIGN_UNVERIFIED_BUDGET,
            self::CAMPAIGN_EMAIL_ONLY_BUDGET,
            self::CAMPAIGN_ZIP_CODE_COVERAGE,
            self::GOOGLE_REVENUE,
            self::GOOGLE_GROSS_PROFIT,
            self::GOOGLE_ROAS,
            self::GOOGLE_PROFIT_MARGIN,
            self::GOOGLE_SMS_VERIFIED_REVENUE,
            self::GOOGLE_RAW_LEADS,
            self::GOOGLE_GTS_LEADS,
            self::GOOGLE_SOLD_LEADS,
            self::GOOGLE_REVENUE_PER_RAW_LEAD,
            self::GOOGLE_SMS_VERIFIED_LEADS,
            self::GOOGLE_REVENUE_PER_SMS_VERIFIED_LEAD,
            self::GOOGLE_SUGGESTED_TCPA,
            self::GOOGLE_CPA,
            self::GOOGLE_DIFF_TCPA_AND_CPA,
        ];
    }

    /**
     * @param LeadsReportColumnEnum $column
     * @return array
     */
    public static function getDependencies(LeadsReportColumnEnum $column): array
    {
        $dependenciesArray = [];
        self::getDependenciesArray($column, $dependenciesArray);
        return $dependenciesArray;
    }

    /**
     * @param LeadsReportColumnEnum $column
     * @param array $dependentOn
     * @return void
     */
    public static function getDependenciesArray(LeadsReportColumnEnum $column, array &$dependentOn): void
    {
        if (in_array($column, self::getAllCalculatedColumns())) {
            $calculatedColumns = $column->getCalculateFields();

            if ($calculatedColumns[self::FIRST_COLUMN] instanceof LeadsReportColumnEnum) {
                $dependentOn[] = $calculatedColumns[self::FIRST_COLUMN];
                LeadsReportColumnEnum::getDependenciesArray($calculatedColumns[self::FIRST_COLUMN], $dependentOn);
            }
            if ($calculatedColumns[self::SECOND_COLUMN] instanceof LeadsReportColumnEnum) {
                $dependentOn[] = $calculatedColumns[self::SECOND_COLUMN];
                LeadsReportColumnEnum::getDependenciesArray($calculatedColumns[self::SECOND_COLUMN], $dependentOn);
            }
        }
    }

    /**
     * @param LeadsReportColumnEnum|int|null $columnEnum
     * @return string
     */
    public static function getDisplayName(LeadsReportColumnEnum|int|null $columnEnum): string
    {
        return Str::title(str_replace('_', ' ', self::getKey($columnEnum)));
    }

    /**
     * @return array
     */
    public static function getAllCalculatedColumns(): array
    {
        $calculatedColumns = [];
        foreach (self::cases() as $case) {
            if ($case->getCalculateFields())
                $calculatedColumns[] = $case;
        }
        return $calculatedColumns;
    }

    /**
     * @return array
     */
    public function getMetaData(): array
    {
        return match ($this) {
            self::ORGANIC_TOTAL_LEADS => [
                self::KEY               => "organic_total_leads",
                self::NAME_KEY          => "Organic Total Leads",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => true,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => [
                    self::FIRST_COLUMN  => self::TOTAL_LEADS,
                    self::OPERATOR      => '-',
                    self::SECOND_COLUMN => self::PAID_TOTAL_LEADS,
                ],
                self::QUERY_TYPE_KEY    => null,
                self::QUERY_KEY         => null,
            ],
            self::ORGANIC_GTS_LEADS => [
                self::KEY               => "organic_gts_leads",
                self::NAME_KEY          => "Organic GTS Leads",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => [
                    self::FIRST_COLUMN  => self::TOTAL_GTS_LEADS,
                    self::OPERATOR      => '-',
                    self::SECOND_COLUMN => self::PAID_GTS_LEADS,
                ],
                self::QUERY_TYPE_KEY    => null,
                self::QUERY_KEY         => null,
            ],
            self::ORGANIC_SOLD_LEADS => [
                self::KEY               => "organic_sold_leads",
                self::NAME_KEY          => "Organic Sold Leads",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => true,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => [
                    self::FIRST_COLUMN  => self::TOTAL_SOLD_LEADS,
                    self::OPERATOR      => '-',
                    self::SECOND_COLUMN => self::PAID_SOLD_LEADS,
                ],
                self::QUERY_TYPE_KEY    => null,
                self::QUERY_KEY         => null,
            ],
            self::ORGANIC_SOLD_LEGS => [
                self::KEY               => "organic_sold_legs",
                self::NAME_KEY          => "Organic Sold Legs",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => [
                    self::FIRST_COLUMN  => self::TOTAL_SOLD_LEGS,
                    self::OPERATOR      => '-',
                    self::SECOND_COLUMN => self::PAID_SOLD_LEGS,
                ],
                self::QUERY_TYPE_KEY    => null,
                self::QUERY_KEY         => null,
            ],
            self::ORGANIC_REVENUE => [
                self::KEY               => "organic_revenue",
                self::NAME_KEY          => "Organic Revenue",
                self::TYPE_KEY          => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY       => true,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => [
                    self::FIRST_COLUMN  => self::TOTAL_REVENUE,
                    self::OPERATOR      => '-',
                    self::SECOND_COLUMN => self::PAID_REVENUE,
                ],
                self::QUERY_TYPE_KEY    => null,
                self::QUERY_KEY         => null,
            ],
            self::PAID_TOTAL_LEADS => [
                self::KEY               => "paid_total_leads",
                self::NAME_KEY          => "Paid Total Leads",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => true,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         =>
                    "COUNT(DISTINCT ".
                        "CASE ".
                            "WHEN (LENGTH(".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_CODE.") > 0 ".
                                    "OR LENGTH(".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_NAME.") > 0 ".
                                    "OR LENGTH(".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID.") > 0) ".
                                "AND (".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID." NOT IN (".implode(', ', self::ORGANIC_AFFILIATE_IDS).") ".
                                    "OR ".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID." IS NULL) ".
                                "AND (".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_NAME." != 'organic' ".
                                    "OR ".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_NAME." IS NULL) ".
                            "THEN ".self::CONSUMER_PRODUCTS_TABLE.".".ConsumerProduct::FIELD_ID." ".
                        "END".
                    ") as paid_total_leads",
            ],
            self::PAID_GTS_LEADS => [
                self::KEY               => "paid_gts_leads",
                self::NAME_KEY          => "Paid GTS Leads",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         =>
                    "COUNT(DISTINCT ".
                        "CASE ".
                            "WHEN ".self::CONSUMER_PRODUCTS_TABLE.".".ConsumerProduct::FIELD_GOOD_TO_SELL." = true ".
                                "AND (".
                                    "(LENGTH(".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_CODE.") > 0 ".
                                        "OR LENGTH(".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_NAME.") > 0 ".
                                        "OR LENGTH(".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID.") > 0) ".
                                    "AND (".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID." NOT IN (".implode(', ', self::ORGANIC_AFFILIATE_IDS).") ".
                                        "OR ".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID." IS NULL) ".
                                    "AND (".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_NAME." != 'organic' ".
                                        "OR ".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_NAME." IS NULL)".
                                ") ".
                            "THEN ".self::CONSUMER_PRODUCTS_TABLE.".".ConsumerProduct::FIELD_ID." ".
                        "END".
                    ") as paid_gts_leads",
            ],
            self::GOOGLE_PAID_GTS_LEADS => [
                self::KEY               => "google_paid_gts_leads",
                self::NAME_KEY          => "Google Paid GTS Leads",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         =>
                    "COUNT(DISTINCT ".
                        "CASE ".
                            "WHEN ".self::CONSUMER_PRODUCTS_TABLE.".".ConsumerProduct::FIELD_GOOD_TO_SELL." = true ".
                                "AND ".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_NAME." IN (".implode(', ', self::GOOGLE_TRACK_NAMES).") ".
                            "THEN ".self::CONSUMER_PRODUCTS_TABLE.".".ConsumerProduct::FIELD_ID." ".
                        "END".
                    ") as google_paid_gts_leads",
            ],
            self::META_PAID_GTS_LEADS => [
                self::KEY               => "meta_paid_gts_leads",
                self::NAME_KEY          => "Meta Paid GTS Leads",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         =>
                    "COUNT(DISTINCT ".
                        "CASE ".
                            "WHEN ".self::CONSUMER_PRODUCTS_TABLE.".".ConsumerProduct::FIELD_GOOD_TO_SELL." = true ".
                                "AND ".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_NAME." IN (".implode(', ', self::META_TRACK_NAMES).") ".
                            "THEN ".self::CONSUMER_PRODUCTS_TABLE.".".ConsumerProduct::FIELD_ID." ".
                        "END".
                    ") as meta_paid_gts_leads",
            ],
            self::MICROSOFT_PAID_GTS_LEADS => [
                self::KEY               => "microsoft_paid_gts_leads",
                self::NAME_KEY          => "Microsoft Paid GTS Leads",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         =>
                    "COUNT(DISTINCT ".
                        "CASE ".
                            "WHEN ".self::CONSUMER_PRODUCTS_TABLE.".".ConsumerProduct::FIELD_GOOD_TO_SELL." = true ".
                                "AND ".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_NAME." IN (".implode(', ', self::MICROSOFT_TRACK_NAMES).") ".
                            "THEN ".self::CONSUMER_PRODUCTS_TABLE.".".ConsumerProduct::FIELD_ID." ".
                        "END".
                    ") as microsoft_paid_gts_leads",
            ],
            self::PAID_SOLD_LEADS => [
                self::KEY               => "paid_sold_leads",
                self::NAME_KEY          => "Paid Sold Leads",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => true,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         =>
                    "COUNT(DISTINCT ".
                        "CASE ".
                            "WHEN ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_ID." IS NOT NULL ".
                                "AND ".self::PRODUCT_REJECT_TABLE.".".ProductRejection::FIELD_ID." IS NULL ".
                                "AND ".self::PRODUCT_CANCEL_TABLE.".".ProductCancellation::FIELD_ID." IS NULL ".
                                "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_CHARGEABLE." = true ".
                                "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_DELIVERED." = true ".
                                "AND (".
                                    "(LENGTH(".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_CODE.") > 0 ".
                                        "OR LENGTH(".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_NAME.") > 0 ".
                                        "OR LENGTH(".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID.") > 0) ".
                                    "AND (".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID." NOT IN (".implode(', ', self::ORGANIC_AFFILIATE_IDS).") ".
                                        "OR ".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID." IS NULL) ".
                                    "AND (".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_NAME." != 'organic' ".
                                        "OR ".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_NAME." IS NULL)".
                                ") ".
                            "THEN ".self::CONSUMER_PRODUCTS_TABLE.".".ConsumerProduct::FIELD_ID." ".
                        "END".
                    ") as paid_sold_leads",
            ],
            self::PAID_SOLD_LEGS => [
                self::KEY               => "paid_sold_legs",
                self::NAME_KEY          => "Paid Sold Legs",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         =>
                    "COUNT(DISTINCT ".
                        "CASE ".
                            "WHEN ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_ID." IS NOT NULL ".
                                "AND ".self::PRODUCT_REJECT_TABLE.".".ProductRejection::FIELD_ID." IS NULL ".
                                "AND ".self::PRODUCT_CANCEL_TABLE.".".ProductCancellation::FIELD_ID." IS NULL ".
                                "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_CHARGEABLE." = true ".
                                "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_DELIVERED." = true ".
                                "AND (".
                                    "(LENGTH(".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_CODE.") > 0 ".
                                        "OR LENGTH(".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_NAME.") > 0 ".
                                        "OR LENGTH(".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID.") > 0) ".
                                    "AND (".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID." NOT IN (".implode(', ', self::ORGANIC_AFFILIATE_IDS).") ".
                                        "OR ".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID." IS NULL) ".
                                    "AND (".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_NAME." != 'organic' ".
                                        "OR ".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_NAME." IS NULL)".
                                ") ".
                            "THEN ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_ID." ".
                        "END".
                    ") as paid_sold_legs",
            ],
            self::PAID_REVENUE => [
                self::KEY               => "paid_revenue",
                self::NAME_KEY          => "Paid Revenue",
                self::TYPE_KEY          => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY       => true,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         =>
                    "SUM(".
                        "CASE ".
                            "WHEN ".self::PRODUCT_REJECT_TABLE.".".ProductRejection::FIELD_ID." IS NULL ".
                                "AND ".self::PRODUCT_CANCEL_TABLE.".".ProductCancellation::FIELD_ID." IS NULL ".
                                "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_CHARGEABLE." = true ".
                                "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_DELIVERED." = true ".
                                "AND (".
                                    "(LENGTH(".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_CODE.") > 0 ".
                                        "OR LENGTH(".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_NAME.") > 0 ".
                                        "OR LENGTH(".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID.") > 0) ".
                                    "AND (".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID." NOT IN (".implode(', ', self::ORGANIC_AFFILIATE_IDS).") ".
                                        "OR ".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID." IS NULL) ".
                                    "AND (".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_NAME." != 'organic' ".
                                        "OR ".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_NAME." IS NULL)".
                                ") ".
                            "THEN ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_COST." ".
                        "END".
                    ") as paid_revenue",
            ],
            self::TOTAL_LEADS => [
                self::KEY               => "total_leads",
                self::NAME_KEY          => "Total Leads",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => true,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         =>
                    "COUNT(DISTINCT ".
                        self::CONSUMER_PRODUCTS_TABLE.".".ConsumerProduct::FIELD_ID.
                    ") as total_leads"
            ],
            self::TOTAL_GTS_LEADS => [
                self::KEY               => "total_gts_leads",
                self::NAME_KEY          => "Total GTS Leads",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         =>
                    "COUNT(DISTINCT ".
                        "CASE ".
                            "WHEN ".self::CONSUMER_PRODUCTS_TABLE.".".ConsumerProduct::FIELD_GOOD_TO_SELL." = true ".
                            "THEN ".self::CONSUMER_PRODUCTS_TABLE.".".ConsumerProduct::FIELD_ID." ".
                        "END".
                    ") as total_gts_leads",
            ],
            self::TOTAL_SOLD_LEADS => [
                self::KEY               => "total_sold_leads",
                self::NAME_KEY          => "Total Sold Leads",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => true,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         =>
                    "COUNT(DISTINCT ".
                        "CASE ".
                            "WHEN ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_ID." IS NOT NULL ".
                                "AND ".self::PRODUCT_REJECT_TABLE.".".ProductRejection::FIELD_ID." IS NULL ".
                                "AND ".self::PRODUCT_CANCEL_TABLE.".".ProductCancellation::FIELD_ID." IS NULL ".
                                "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_CHARGEABLE." = true ".
                                "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_DELIVERED." = true ".
                            "THEN ".self::CONSUMER_PRODUCTS_TABLE.".".ConsumerProduct::FIELD_ID." ".
                        "END".
                    ") as total_sold_leads",
            ],
            self::TOTAL_REJECTED_LEGS => [
                self::KEY               => "total_rejected_legs",
                self::NAME_KEY          => "Total Rejected Legs",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => [],
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         =>
                    "COUNT(DISTINCT ".
                        "CASE ".
                            "WHEN ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_ID." IS NOT NULL ".
                                "AND ".self::PRODUCT_REJECT_TABLE.".".ProductRejection::FIELD_ID." IS NOT NULL ".
                                "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_CHARGEABLE." = true ".
                            "THEN ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_ID." ".
                        "END".
                    ") as total_rejected_legs",
            ],
            self::TOTAL_SOLD_LEGS => [
                self::KEY               => "total_sold_legs",
                self::NAME_KEY          => "Total Sold Legs",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => [],
                self::DEFAULT_FOR       => self::CAMPAIGN_GROUPS,
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         =>
                    "COUNT(DISTINCT ".
                        "CASE ".
                            "WHEN ".self::PRODUCT_REJECT_TABLE.".".ProductRejection::FIELD_ID." IS NULL ".
                                "AND ".self::PRODUCT_CANCEL_TABLE.".".ProductCancellation::FIELD_ID." IS NULL ".
                                "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_CHARGEABLE." = true ".
                                "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_DELIVERED." = true ".
                            "THEN ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_ID." ".
                        "END".
                    ") as total_sold_legs",
            ],
            self::SOLD_LEGS_PER_SOLD_LEAD => [
                self::KEY               => "sold_legs_per_sold_lead",
                self::NAME_KEY          => "Sold Legs per Sold Lead",
                self::TYPE_KEY          => self::COLUMN_TYPE_DECIMAL,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_AVG,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => [
                    self::FIRST_COLUMN  => self::TOTAL_SOLD_LEGS,
                    self::OPERATOR      => '/',
                    self::SECOND_COLUMN => self::TOTAL_SOLD_LEADS,
                ],
                self::QUERY_TYPE_KEY    => null,
                self::QUERY_KEY         => null,
            ],
            self::REVENUE_PER_SOLD_LEAD => [
                self::KEY               => "revenue_per_sold_lead",
                self::NAME_KEY          => "Revenue per Sold Lead",
                self::TYPE_KEY          => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_AVG,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => [
                    self::FIRST_COLUMN  => self::TOTAL_REVENUE,
                    self::OPERATOR      => '/',
                    self::SECOND_COLUMN => self::TOTAL_SOLD_LEADS,
                ],
                self::QUERY_TYPE_KEY    => null,
                self::QUERY_KEY         => null,
            ],
            self::REVENUE_PER_SOLD_LEG => [
                self::KEY               => "revenue_per_sold_leg",
                self::NAME_KEY          => "Revenue per Sold Leg",
                self::TYPE_KEY          => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_AVG,
                self::DISABLED_FOR      => [],
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => [
                    self::FIRST_COLUMN  => self::TOTAL_REVENUE,
                    self::OPERATOR      => '/',
                    self::SECOND_COLUMN => self::TOTAL_SOLD_LEGS,
                ],
                self::QUERY_TYPE_KEY    => null,
                self::QUERY_KEY         => null,
            ],
            self::TOTAL_REVENUE => [
                self::KEY               => "total_revenue",
                self::NAME_KEY          => "Total Revenue",
                self::TYPE_KEY          => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY       => true,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => [],
                self::DEFAULT_FOR       => self::CAMPAIGN_GROUPS,
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         =>
                    "SUM(".
                        "CASE ".
                            "WHEN ".self::PRODUCT_REJECT_TABLE.".".ProductRejection::FIELD_ID." IS NULL ".
                                "AND ".self::PRODUCT_CANCEL_TABLE.".".ProductCancellation::FIELD_ID." IS NULL ".
                                "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_CHARGEABLE." = true ".
                                "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_DELIVERED." = true ".
                            "THEN ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_COST." ".
                        "END".
                    ") as total_revenue",
            ],
            self::GOOGLE_REVENUE => [
                self::KEY               => "google_revenue",
                self::NAME_KEY          => "Google Revenue",
                self::TYPE_KEY          => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         =>
                    "SUM(".
                        "CASE ".
                            "WHEN ".self::PRODUCT_REJECT_TABLE.".".ProductRejection::FIELD_ID." IS NULL ".
                                "AND ".self::PRODUCT_CANCEL_TABLE.".".ProductCancellation::FIELD_ID." IS NULL ".
                                "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_CHARGEABLE." = true ".
                                "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_DELIVERED." = true ".
                                "AND ".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_NAME." IN (".implode(', ', self::GOOGLE_TRACK_NAMES).") ".
                            "THEN ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_COST." ".
                        "END".
                    ") as google_revenue",
            ],
            self::GOOGLE_SMS_VERIFIED_REVENUE => [
                self::KEY               => "google_sms_verified_revenue",
                self::NAME_KEY          => "Google SMS Verified Revenue",
                self::TYPE_KEY          => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         =>
                    "SUM(".
                        "CASE ".
                            "WHEN ".self::PRODUCT_REJECT_TABLE.".".ProductRejection::FIELD_ID." IS NULL ".
                                "AND ".self::PRODUCT_CANCEL_TABLE.".".ProductCancellation::FIELD_ID." IS NULL ".
                                "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_CHARGEABLE." = true ".
                                "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_DELIVERED." = true ".
                                "AND ".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_NAME." IN (".implode(', ', self::GOOGLE_TRACK_NAMES).") ".
                                "AND ".self::CONSUMERS_TABLE.".".Consumer::FIELD_CLASSIFICATION." = ".Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_SMS." ".
                    "THEN ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_COST." ".
                        "END".
                    ") as google_sms_verified_revenue",
            ],
            self::GOOGLE_RAW_LEADS => [
                self::KEY               => "google_raw_leads",
                self::NAME_KEY          => "Google Raw Leads",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         =>
                    "COUNT(DISTINCT ".
                        "CASE ".
                            "WHEN ".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_NAME." IN (".implode(', ', self::GOOGLE_TRACK_NAMES).") ".
                            "THEN ".self::CONSUMER_PRODUCTS_TABLE.".".ConsumerProduct::FIELD_ID." ".
                        "END".
                    ") as google_raw_leads",
            ],
            self::GOOGLE_GTS_LEADS => [
                self::KEY               => "google_gts_leads",
                self::NAME_KEY          => "Google GTS Leads",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         =>
                    "COUNT(DISTINCT ".
                        "CASE ".
                            "WHEN ".self::CONSUMER_PRODUCTS_TABLE.".".ConsumerProduct::FIELD_GOOD_TO_SELL." = true ".
                                "AND ".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_NAME." IN (".implode(', ', self::GOOGLE_TRACK_NAMES).") ".
                            "THEN ".self::CONSUMER_PRODUCTS_TABLE.".".ConsumerProduct::FIELD_ID." ".
                        "END".
                    ") as google_gts_leads",
            ],
            self::GOOGLE_SOLD_LEADS => [
                self::KEY               => "google_sold_leads",
                self::NAME_KEY          => "Google Sold Leads",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         =>
                    "COUNT(DISTINCT ".
                        "CASE ".
                            "WHEN ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_ID." IS NOT NULL ".
                                "AND ".self::PRODUCT_REJECT_TABLE.".".ProductRejection::FIELD_ID." IS NULL ".
                                "AND ".self::PRODUCT_CANCEL_TABLE.".".ProductCancellation::FIELD_ID." IS NULL ".
                                "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_CHARGEABLE." = true ".
                                "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_DELIVERED." = true ".
                                "AND ".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_NAME." IN (".implode(', ', LeadsReportColumnEnum::GOOGLE_TRACK_NAMES).") ".
                            "THEN ".self::CONSUMER_PRODUCTS_TABLE.".".ConsumerProduct::FIELD_ID." ".
                        "END".
                    ") as google_sold_leads",
            ],
            self::GOOGLE_SOLD_LEGS => [
                self::KEY               => "google_sold_legs",
                self::NAME_KEY          => "Google Sold Legs",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         =>
                    "COUNT(DISTINCT ".
                        "CASE ".
                            "WHEN ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_ID." IS NOT NULL ".
                                "AND ".self::PRODUCT_REJECT_TABLE.".".ProductRejection::FIELD_ID." IS NULL ".
                                "AND ".self::PRODUCT_CANCEL_TABLE.".".ProductCancellation::FIELD_ID." IS NULL ".
                                "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_CHARGEABLE." = true ".
                                "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_DELIVERED." = true ".
                                "AND ".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_NAME." IN (".implode(', ', LeadsReportColumnEnum::GOOGLE_TRACK_NAMES).") ".
                            "THEN ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_ID." ".
                        "END".
                    ") as google_sold_legs",
            ],
            self::GOOGLE_REVENUE_PER_RAW_LEAD => [
                self::KEY               => "google_revenue_per_raw_lead",
                self::NAME_KEY          => "Google Revenue per Raw Lead",
                self::TYPE_KEY          => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_AVG,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => [
                    self::FIRST_COLUMN  => self::GOOGLE_REVENUE,
                    self::OPERATOR      => '/',
                    self::SECOND_COLUMN => self::GOOGLE_RAW_LEADS,
                ],
                self::QUERY_TYPE_KEY    => null,
                self::QUERY_KEY         => null,
            ],
            self::GOOGLE_GROSS_PROFIT => [
                self::KEY               => "google_gross_profit",
                self::NAME_KEY          => "Google Gross Profit",
                self::TYPE_KEY          => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_AD_COST,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => [
                    self::FIRST_COLUMN  => self::GOOGLE_REVENUE,
                    self::OPERATOR      => '-',
                    self::SECOND_COLUMN => self::GOOGLE_AD_COST,
                ],
                self::QUERY_TYPE_KEY    => null,
                self::QUERY_KEY         => null,
            ],
            self::GOOGLE_ROAS => [
                self::KEY               => "google_roas",
                self::NAME_KEY          => "Google ROAS",
                self::TYPE_KEY          => self::COLUMN_TYPE_PERCENT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_AVG,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_AD_COST,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => [
                    self::FIRST_COLUMN  => self::GOOGLE_REVENUE,
                    self::OPERATOR      => '/',
                    self::SECOND_COLUMN => self::GOOGLE_AD_COST,
                ],
                self::QUERY_TYPE_KEY    => null,
                self::QUERY_KEY         => null,
            ],
            self::GOOGLE_PROFIT_MARGIN => [
                self::KEY               => "google_profit_margin",
                self::NAME_KEY          => "Google Profit Margin",
                self::TYPE_KEY          => self::COLUMN_TYPE_PERCENT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_AVG,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_AD_COST,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => [
                    self::FIRST_COLUMN  => self::GOOGLE_GROSS_PROFIT,
                    self::OPERATOR      => '/',
                    self::SECOND_COLUMN => self::GOOGLE_REVENUE,
                ],
                self::QUERY_TYPE_KEY    => null,
                self::QUERY_KEY         => null,
            ],
            self::DISTINCT_BUYERS => [
                self::KEY               => "distinct_buyers",
                self::NAME_KEY          => "Distinct Buyers",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => true,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_AVG,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         =>
                    "COUNT(DISTINCT ".
                        "CASE ".
                            "WHEN ".self::PRODUCT_REJECT_TABLE.".".ProductRejection::FIELD_ID." IS NULL ".
                                "AND ".self::PRODUCT_CANCEL_TABLE.".".ProductCancellation::FIELD_ID." IS NULL ".
                                "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_CHARGEABLE." = true ".
                                "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_DELIVERED." = true ".
                            "THEN ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_COMPANY_ID." ".
                        "END".
                    ") as distinct_buyers",
            ],
            self::TOTAL_AD_COST => [
                self::KEY               => "total_ad_cost",
                self::NAME_KEY          => "Total Ad Cost",
                self::TYPE_KEY          => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY       => true,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_AD_COST,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::COST],
                self::QUERY_KEY         =>
                    "SUM(".
                        self::DAILY_AD_COST_TABLE.".".DailyAdCost::FIELD_COST.
                    ") as total_ad_cost",
            ],
            self::AD_COST_PER_PAID_GTS_LEAD => [
                self::KEY               => "ad_cost_per_paid_gts_lead",
                self::NAME_KEY          => "Ad Cost per Paid GTS Lead",
                self::TYPE_KEY          => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_AVG,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_AD_COST,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => [
                    self::FIRST_COLUMN  => self::TOTAL_AD_COST,
                    self::OPERATOR      => '/',
                    self::SECOND_COLUMN => self::PAID_GTS_LEADS,
                ],
                self::QUERY_TYPE_KEY    => null,
                self::QUERY_KEY         => null,
            ],
            self::GOOGLE_AD_COST_PER_GTS_LEAD => [
                self::KEY               => "google_ad_cost_per_gts_lead",
                self::NAME_KEY          => "Google Ad Cost per GTS Lead",
                self::TYPE_KEY          => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_AVG,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_AD_COST,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => [
                    self::FIRST_COLUMN  => self::GOOGLE_AD_COST,
                    self::OPERATOR      => '/',
                    self::SECOND_COLUMN => self::GOOGLE_PAID_GTS_LEADS,
                ],
                self::QUERY_TYPE_KEY    => null,
                self::QUERY_KEY         => null,
            ],
            self::META_AD_COST_PER_GTS_LEAD => [
                self::KEY               => "meta_ad_cost_per_gts_lead",
                self::NAME_KEY          => "Meta Ad Cost per GTS Lead",
                self::TYPE_KEY          => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_AVG,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_AD_COST,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => [
                    self::FIRST_COLUMN  => self::META_AD_COST,
                    self::OPERATOR      => '/',
                    self::SECOND_COLUMN => self::META_PAID_GTS_LEADS,
                ],
                self::QUERY_TYPE_KEY    => null,
                self::QUERY_KEY         => null,
            ],
            self::MICROSOFT_AD_COST_PER_GTS_LEAD => [
                self::KEY               => "microsoft_ad_cost_per_gts_lead",
                self::NAME_KEY          => "Microsoft Ad Cost per GTS Lead",
                self::TYPE_KEY          => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_AVG,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_AD_COST,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => [
                    self::FIRST_COLUMN  => self::MICROSOFT_AD_COST,
                    self::OPERATOR      => '/',
                    self::SECOND_COLUMN => self::MICROSOFT_PAID_GTS_LEADS,
                ],
                self::QUERY_TYPE_KEY    => null,
                self::QUERY_KEY         => null,
            ],
            self::SOLD_TO_GTS_RATIO => [
                self::KEY               => "sold_to_gts_ratio",
                self::NAME_KEY          => "Sold to GTS Ratio",
                self::TYPE_KEY          => self::COLUMN_TYPE_PERCENT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_AVG,
                self::DISABLED_FOR      => [],
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => [
                    self::FIRST_COLUMN  => self::TOTAL_SOLD_LEADS,
                    self::OPERATOR      => '/',
                    self::SECOND_COLUMN => self::TOTAL_GTS_LEADS,
                ],
                self::QUERY_TYPE_KEY    => null,
                self::QUERY_KEY         => null,
            ],
            self::GROSS_PROFIT => [
                self::KEY               => "gross_profit",
                self::NAME_KEY          => "Gross Profit",
                self::TYPE_KEY          => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY       => true,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_AD_COST,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => [
                    self::FIRST_COLUMN  => self::TOTAL_REVENUE,
                    self::OPERATOR      => '-',
                    self::SECOND_COLUMN => self::TOTAL_AD_COST,
                ],
                self::QUERY_TYPE_KEY    => null,
                self::QUERY_KEY         => null,
            ],
            self::GOOGLE_AD_COST => [
                self::KEY               => "google_ad_cost",
                self::NAME_KEY          => "Google Ad Cost",
                self::TYPE_KEY          => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_AD_COST,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::COST],
                self::QUERY_KEY         =>
                    "SUM(".
                        "CASE ".
                            "WHEN ".self::DAILY_AD_COST_TABLE.".".DailyAdCost::FIELD_PLATFORM." = ".AdvertisingPlatform::getInteger(AdvertisingPlatform::GOOGLE->value)." ".
                            "THEN ".self::DAILY_AD_COST_TABLE.".".DailyAdCost::FIELD_COST." ".
                        "END".
                    ") as google_ad_cost",
            ],
            self::META_AD_COST => [
                self::KEY               => "meta_ad_cost",
                self::NAME_KEY          => "Meta Ad Cost",
                self::TYPE_KEY          => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_AD_COST,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::COST],
                self::QUERY_KEY         =>
                    "SUM(".
                        "CASE ".
                            "WHEN ".self::DAILY_AD_COST_TABLE.".".DailyAdCost::FIELD_PLATFORM." = ".AdvertisingPlatform::getInteger(AdvertisingPlatform::META->value)." ".
                            "THEN ".self::DAILY_AD_COST_TABLE.".".DailyAdCost::FIELD_COST." ".
                        "END".
                    ") as meta_ad_cost",
            ],
            self::MICROSOFT_AD_COST => [
                self::KEY               => "microsoft_ad_cost",
                self::NAME_KEY          => "Microsoft Ad Cost",
                self::TYPE_KEY          => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_AD_COST,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::COST],
                self::QUERY_KEY         =>
                    "SUM(".
                        "CASE ".
                            "WHEN ".self::DAILY_AD_COST_TABLE.".".DailyAdCost::FIELD_PLATFORM." = ".AdvertisingPlatform::getInteger(AdvertisingPlatform::MICROSOFT->value)." ".
                            "THEN ".self::DAILY_AD_COST_TABLE.".".DailyAdCost::FIELD_COST." ".
                        "END".
                    ") as microsoft_ad_cost",
            ],
            self::COMPANY => [
                self::KEY               => "company",
                self::NAME_KEY          => "Company",
                self::TYPE_KEY          => self::COLUMN_TYPE_INFO_STRING,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_NONE,
                self::DISABLED_FOR      => [],
                self::DEFAULT_FOR       => [...self::CAMPAIGN_GROUPS, LeadsReportGroupEnum::COMPANY],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD, LeadsReportQueryEnum::CAMPAIGN],
                self::QUERY_KEY         =>
                    "GROUP_CONCAT(DISTINCT ".
                        self::infoString(
                           text:  "CONCAT(".
                                self::COMPANIES_TABLE.".".Company::FIELD_NAME.", ".
                                "' (', ".
                                self::COMPANIES_TABLE.".".Company::FIELD_ID.", ".
                                "')'".
                            ")",
                            link: '\'/companies/\', '.self::COMPANIES_TABLE.'.'.Company::FIELD_ID,
                            class: '\''.self::BLUE.'\'',
                            concat: true,
                        ).
                    " SEPARATOR '".self::infoString(text: '; ', class: 'text-black')."') as company",
            ],
            self::COMPANY_ALL_TIME_REVENUE => [
                self::KEY               => "company_all_time_revenue",
                self::NAME_KEY          => "Company All Time Invoiced Revenue",
                self::TYPE_KEY          => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_NONE,
                self::DISABLED_FOR      => array_values(array_filter(
                    LeadsReportGroupEnum::cases(),
                    fn($case) => $case !== LeadsReportGroupEnum::COMPANY
                )),
                self::DEFAULT_FOR       => [LeadsReportGroupEnum::COMPANY],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         => self::COMPANY_REV_TABLE.'.company_all_time_revenue',
            ],
            self::TIME_FRAME_REVENUE => [
                self::KEY               => "time_frame_revenue",
                self::NAME_KEY          => "Time Frame Revenue",
                self::TYPE_KEY          => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_NONE,
                self::DISABLED_FOR      => [],
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         => self::TIME_FRAME_REV_TABLE.'.time_frame_revenue',
            ],
            self::PROPORTIONAL_REVENUE_PERCENT => [
                self::KEY               => "proportional_revenue_percent",
                self::NAME_KEY          => "Proportional Revenue Percent",
                self::TYPE_KEY          => self::COLUMN_TYPE_PERCENT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => [],
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => [
                    self::FIRST_COLUMN  => self::TOTAL_REVENUE,
                    self::OPERATOR      => '/',
                    self::SECOND_COLUMN => self::TIME_FRAME_REVENUE,
                ],
                self::QUERY_TYPE_KEY    => null,
                self::QUERY_KEY         => null,
            ],
            self::TIME_FRAME_AD_COST => [
                self::KEY               => "time_frame_ad_cost",
                self::NAME_KEY          => "Time Frame Ad Cost",
                self::TYPE_KEY          => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_NONE,
                self::DISABLED_FOR      => [],
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         => self::TIME_FRAME_COST_TABLE.'.time_frame_ad_cost',
            ],
            self::PROPORTIONAL_AD_COST => [
                self::KEY               => "proportional_ad_cost",
                self::NAME_KEY          => "Proportional Ad Cost",
                self::TYPE_KEY          => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => [],
                self::DEFAULT_FOR       => [LeadsReportGroupEnum::COMPANY_USER_RELATIONSHIP, LeadsReportGroupEnum::COMPANY],
                self::CALCULATED_KEY    => [
                    self::FIRST_COLUMN  => self::PROPORTIONAL_REVENUE_PERCENT,
                    self::OPERATOR      => '*',
                    self::SECOND_COLUMN => self::TIME_FRAME_AD_COST,
                ],
                self::QUERY_TYPE_KEY    => null,
                self::QUERY_KEY         => null,
            ],
            self::PROPORTIONAL_GROSS_PROFIT => [
                self::KEY               => "proportional_gross_profit",
                self::NAME_KEY          => "Proportional Gross Profit",
                self::TYPE_KEY          => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => [],
                self::DEFAULT_FOR       => [LeadsReportGroupEnum::COMPANY_USER_RELATIONSHIP, LeadsReportGroupEnum::COMPANY],
                self::CALCULATED_KEY    => [
                    self::FIRST_COLUMN  => self::TOTAL_REVENUE,
                    self::OPERATOR      => '-',
                    self::SECOND_COLUMN => self::PROPORTIONAL_AD_COST,
                ],
                self::QUERY_TYPE_KEY    => null,
                self::QUERY_KEY         => null,
            ],
            self::COMPANY_USER_RELATIONSHIP_TYPE => [
                self::KEY               => "company_user_rel_type",
                self::NAME_KEY          => "Company User Relationship",
                self::TYPE_KEY          => self::COLUMN_TYPE_STRING,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_NONE,
                self::DISABLED_FOR      => array_values(array_filter(
                    LeadsReportGroupEnum::cases(),
                    fn($case) => $case !== LeadsReportGroupEnum::COMPANY_USER_RELATIONSHIP
                )),
                self::DEFAULT_FOR       => [LeadsReportGroupEnum::COMPANY_USER_RELATIONSHIP],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         => "GROUP_CONCAT(DISTINCT cur.".CompanyUserRelationship::FIELD_ROLE_ID.") as company_user_rel_type",
            ],
            self::PING_POST_LEADS => [
                self::KEY               => "ping_post_leads",
                self::NAME_KEY          => "Ping Post Leads",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => [],
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         => "COUNT(DISTINCT CASE WHEN ppl.".PingPostPublisherLead::FIELD_ID." IS NOT NULL THEN cp.".ConsumerProduct::FIELD_ID." END) as ping_post_leads",
            ],
            self::PING_POST_REVENUE => [
                self::KEY               => "ping_post_revenue",
                self::NAME_KEY          => "Ping Post Revenue",
                self::TYPE_KEY          => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => [],
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         => "SUM(ppl.".PingPostPublisherLead::FIELD_COST.") as ping_post_revenue",
            ],
            self::GENERATED_STATE_COUNT => [
                self::KEY               => "generated_state_count",
                self::NAME_KEY          => "Generated State Count",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_AVG,
                self::DISABLED_FOR      => [],
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         => "COUNT(DISTINCT l.".Location::STATE_ABBREVIATION.") as generated_state_count",
            ],
            self::GENERATED_COUNTY_COUNT => [
                self::KEY               => "generated_county_count",
                self::NAME_KEY          => "Generated County Count",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_AVG,
                self::DISABLED_FOR      => [],
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         => "COUNT(DISTINCT CONCAT(l.".Location::COUNTY_KEY.", l.".Location::STATE_ABBREVIATION.")) as generated_county_count",
            ],
            self::GENERATED_ZIP_COUNT => [
                self::KEY               => "generated_zip_count",
                self::NAME_KEY          => "Generated Zip Count",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_AVG,
                self::DISABLED_FOR      => [],
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         => "COUNT(DISTINCT l.".Location::ZIP_CODE.") as generated_zip_count",
            ],
            self::SOLD_STATE_COUNT => [
                self::KEY               => "sold_state_count",
                self::NAME_KEY          => "Sold State Count",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_AVG,
                self::DISABLED_FOR      => [],
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         => "COUNT(DISTINCT ".
                    "CASE ".
                        "WHEN ".self::PRODUCT_REJECT_TABLE.".".ProductRejection::FIELD_ID." IS NULL ".
                        "AND ".self::PRODUCT_CANCEL_TABLE.".".ProductCancellation::FIELD_ID." IS NULL ".
                        "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_CHARGEABLE." = true ".
                        "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_DELIVERED." = true ".
                    "THEN l.".Location::STATE_ABBREVIATION." END) as sold_state_count",
            ],
            self::SOLD_COUNTY_COUNT => [
                self::KEY               => "sold_county_count",
                self::NAME_KEY          => "Sold County Count",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_AVG,
                self::DISABLED_FOR      => [],
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         => "COUNT(DISTINCT ".
                    "CASE ".
                        "WHEN ".self::PRODUCT_REJECT_TABLE.".".ProductRejection::FIELD_ID." IS NULL ".
                        "AND ".self::PRODUCT_CANCEL_TABLE.".".ProductCancellation::FIELD_ID." IS NULL ".
                        "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_CHARGEABLE." = true ".
                        "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_DELIVERED." = true ".
                    "THEN CONCAT(l.".Location::COUNTY_KEY.", l.".Location::STATE_ABBREVIATION.") END) as sold_county_count",
            ],
            self::SOLD_ZIP_COUNT => [
                self::KEY               => "sold_zip_count",
                self::NAME_KEY          => "Sold Zip Count",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_AVG,
                self::DISABLED_FOR      => [],
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         => "COUNT(DISTINCT ".
                    "CASE ".
                        "WHEN ".self::PRODUCT_REJECT_TABLE.".".ProductRejection::FIELD_ID." IS NULL ".
                        "AND ".self::PRODUCT_CANCEL_TABLE.".".ProductCancellation::FIELD_ID." IS NULL ".
                        "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_CHARGEABLE." = true ".
                        "AND ".self::PRODUCT_ASSIGN_TABLE.".".ProductAssignment::FIELD_DELIVERED." = true ".
                    "THEN l.".Location::ZIP_CODE." END) as sold_zip_count",
            ],
            self::CAMPAIGN => [
                self::KEY               => "campaign",
                self::NAME_KEY          => "Campaign",
                self::TYPE_KEY          => self::COLUMN_TYPE_STRING,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_NONE,
                self::DISABLED_FOR      => [],
                self::DEFAULT_FOR       => [LeadsReportGroupEnum::CAMPAIGN_ZIP_CODE],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD, LeadsReportQueryEnum::CAMPAIGN],
                self::QUERY_KEY         =>
                    "GROUP_CONCAT(DISTINCT ".
                        "CASE ".
                            "WHEN ".self::CAMPAIGNS_TABLE.".".CompanyCampaign::FIELD_ID." IS NOT NULL ".
                            "THEN CONCAT(".
                                self::CAMPAIGNS_TABLE.".".CompanyCampaign::FIELD_NAME.", ".
                                "' (', ".
                                self::INDUSTRY_TABLE.".".Industry::FIELD_NAME.", ".
                                "' ', ".
                                self::CAMPAIGNS_TABLE.".".CompanyCampaign::FIELD_ID.", ".
                                "')'".
                            ") ".
                            "ELSE 'Legacy Campaign' ".
                        "END ".
                    "SEPARATOR '; ') as campaign",
            ],
            self::STATE => [
                self::KEY               => "state",
                self::NAME_KEY          => "State",
                self::TYPE_KEY          => self::COLUMN_TYPE_STRING,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_NONE,
                self::DISABLED_FOR      => [],
                self::DEFAULT_FOR       => [LeadsReportGroupEnum::COUNTY],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD, LeadsReportQueryEnum::COST, LeadsReportQueryEnum::CAMPAIGN],
                self::QUERY_KEY         =>
                    "GROUP_CONCAT(DISTINCT ".
                        self::LOCATIONS_TABLE.".".Location::STATE." ".
                    "SEPARATOR '; ') as state",
            ],
            self::COUNTY => [
                self::KEY               => "county",
                self::NAME_KEY          => "County",
                self::TYPE_KEY          => self::COLUMN_TYPE_STRING,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_NONE,
                self::DISABLED_FOR      => [],
                self::DEFAULT_FOR       => [LeadsReportGroupEnum::COMPANY_COUNTY, LeadsReportGroupEnum::CAMPAIGN_COUNTY, LeadsReportGroupEnum::CAMPAIGN_ZIP_CODE],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD, LeadsReportQueryEnum::COST, LeadsReportQueryEnum::CAMPAIGN],
                self::QUERY_KEY         =>
                    "GROUP_CONCAT(DISTINCT ".
                        "CONCAT(".
                            self::LOCATIONS_TABLE.".".Location::COUNTY.", ".
                            "', ', ".
                            self::LOCATIONS_TABLE.".".Location::STATE_ABBREVIATION.
                        ") ".
                    "SEPARATOR '; ') as county",
            ],
            self::CAMPAIGN_STATUS => [
                self::KEY               => "current_campaign_status",
                self::NAME_KEY          => "Current Campaign Status",
                self::TYPE_KEY          => self::COLUMN_TYPE_STRING,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_NONE,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_CAMPAIGNS,
                self::DEFAULT_FOR       => self::CAMPAIGN_GROUPS,
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::CAMPAIGN],
                self::QUERY_KEY         =>
                    "CASE ".
                        "WHEN ".self::CAMPAIGNS_TABLE.".".CompanyCampaign::FIELD_STATUS." = ".CampaignStatus::PAUSED_PERMANENTLY->value." THEN 'Paused Permanently' ".
                        "WHEN ".self::CAMPAIGNS_TABLE.".".CompanyCampaign::FIELD_STATUS." = ".CampaignStatus::PAUSED_TEMPORARILY->value." THEN 'Paused Temporarily' ".
                        "WHEN ".self::CAMPAIGNS_TABLE.".".CompanyCampaign::FIELD_STATUS." = ".CampaignStatus::ACTIVE->value." THEN 'Active' ".
                        "ELSE 'Unknown Status' ".
                    "END as current_campaign_status",
            ],
            self::CAMPAIGN_ZIP_CODE_TARGETED => [
                self::KEY               => "campaign_zip_code_targeted",
                self::NAME_KEY          => "Campaign Zip Code Targeted",
                self::TYPE_KEY          => self::COLUMN_TYPE_STRING,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_NONE,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_CAMPAIGNS,
                self::DEFAULT_FOR       => self::CAMPAIGN_GROUPS,
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::CAMPAIGN],
                self::QUERY_KEY         =>
                    "CASE ".
                        "WHEN ".self::CAMPAIGNS_TABLE.".".CompanyCampaign::FIELD_ZIP_CODE_TARGETED." = true THEN 'True' ".
                        "ELSE 'False' ".
                    "END as campaign_zip_code_targeted",
            ],
            self::CAMPAIGN_ZIP_CODES => [
                self::KEY               => "campaign_zip_codes",
                self::NAME_KEY          => "Campaign Zip Codes",
                self::TYPE_KEY          => self::COLUMN_TYPE_STRING,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_NONE,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_CAMPAIGNS,
                self::DEFAULT_FOR       => [LeadsReportGroupEnum::CAMPAIGN_ZIP_CODE],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::CAMPAIGN],
                self::QUERY_KEY         =>
                    "GROUP_CONCAT(DISTINCT ".
                        self::CCLML_TABLE.".".CompanyCampaignLocationModuleLocation::FIELD_ZIP_CODE." ".
                    "SEPARATOR '; ') as campaign_zip_codes",
            ],
            self::CAMPAIGN_VERIFIED_BUDGET => [
                self::KEY               => "campaign_verified_daily_budget",
                self::NAME_KEY          => "Campaign Verified Daily Budget",
                self::TYPE_KEY          => self::COLUMN_TYPE_STRING,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_NONE,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_CAMPAIGNS,
                self::DEFAULT_FOR       => self::CAMPAIGN_GROUPS,
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::CAMPAIGN],
                self::QUERY_KEY         =>
                    "GROUP_CONCAT(DISTINCT ".
                        "CASE ".
                            "WHEN ".self::BUDGETS_TABLE.".".Budget::FIELD_KEY." = '".BudgetCategory::VERIFIED->value."' AND ".self::BUDGETS_TABLE.".".Budget::FIELD_STATUS." = ".Budget::STATUS_ENABLED." THEN ".
                                "CASE ".
                                    "WHEN ".self::BUDGETS_TABLE.".".Budget::FIELD_TYPE." = ".BudgetType::NO_LIMIT->value." THEN 'Unlimited' ".
                                    "WHEN ".self::BUDGETS_TABLE.".".Budget::FIELD_TYPE." = ".BudgetType::TYPE_DAILY_UNITS->value." THEN CONCAT(".self::BUDGETS_TABLE.".".Budget::FIELD_VALUE.", ' Leads') ".
                                    "ELSE CONCAT('$', ".self::BUDGETS_TABLE.".".Budget::FIELD_VALUE.") ".
                            "END ".
                        "END ".
                    "SEPARATOR '; ') as campaign_verified_daily_budget",
            ],
            self::CAMPAIGN_UNVERIFIED_BUDGET => [
                self::KEY               => "campaign_unverified_daily_budget",
                self::NAME_KEY          => "Campaign Unverified Daily Budget",
                self::TYPE_KEY          => self::COLUMN_TYPE_STRING,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_NONE,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_CAMPAIGNS,
                self::DEFAULT_FOR       => self::CAMPAIGN_GROUPS,
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::CAMPAIGN],
                self::QUERY_KEY         =>
                    "GROUP_CONCAT(DISTINCT ".
                        "CASE ".
                            "WHEN ".self::BUDGETS_TABLE.".".Budget::FIELD_KEY." = '".BudgetCategory::UNVERIFIED->value."' AND ".self::BUDGETS_TABLE.".".Budget::FIELD_STATUS." = ".Budget::STATUS_ENABLED." THEN ".
                                "CASE ".
                                    "WHEN ".self::BUDGETS_TABLE.".".Budget::FIELD_TYPE." = ".BudgetType::NO_LIMIT->value." THEN 'Unlimited' ".
                                    "WHEN ".self::BUDGETS_TABLE.".".Budget::FIELD_TYPE." = ".BudgetType::TYPE_DAILY_UNITS->value." THEN CONCAT(".self::BUDGETS_TABLE.".".Budget::FIELD_VALUE.", ' Leads') ".
                                    "ELSE CONCAT('$', ".self::BUDGETS_TABLE.".".Budget::FIELD_VALUE.") ".
                                "END ".
                        "END ".
                    "SEPARATOR '; ') as campaign_unverified_daily_budget",
            ],
            self::CAMPAIGN_EMAIL_ONLY_BUDGET => [
                self::KEY               => "campaign_email_only_daily_budget",
                self::NAME_KEY          => "Campaign Email Only Daily Budget",
                self::TYPE_KEY          => self::COLUMN_TYPE_STRING,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_NONE,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_CAMPAIGNS,
                self::DEFAULT_FOR       => self::CAMPAIGN_GROUPS,
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::CAMPAIGN],
                self::QUERY_KEY         =>
                    "GROUP_CONCAT(DISTINCT ".
                        "CASE ".
                            "WHEN ".self::BUDGETS_TABLE.".".Budget::FIELD_KEY." = '".BudgetCategory::EMAIL_ONLY->value."' AND ".self::BUDGETS_TABLE.".".Budget::FIELD_STATUS." = ".Budget::STATUS_ENABLED." THEN ".
                                "CASE ".
                                    "WHEN ".self::BUDGETS_TABLE.".".Budget::FIELD_TYPE." = ".BudgetType::NO_LIMIT->value." THEN 'Unlimited' ".
                                    "WHEN ".self::BUDGETS_TABLE.".".Budget::FIELD_TYPE." = ".BudgetType::TYPE_DAILY_UNITS->value." THEN CONCAT(".self::BUDGETS_TABLE.".".Budget::FIELD_VALUE.", ' Leads') ".
                                    "ELSE CONCAT('$', ".self::BUDGETS_TABLE.".".Budget::FIELD_VALUE.") ".
                            "END ".
                        "END ".
                    "SEPARATOR '; ') as campaign_email_only_daily_budget",
            ],
            self::CAMPAIGN_ZIP_CODE_COVERAGE => [
                self::KEY               => "campaign_zip_code_coverage",
                self::NAME_KEY          => "Campaign Zip Code Coverage",
                self::TYPE_KEY          => self::COLUMN_TYPE_STRING,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_NONE,
                self::DISABLED_FOR      => [...self::NOT_APPLICABLE_FOR_CAMPAIGNS, LeadsReportGroupEnum::CAMPAIGN_ZIP_CODE],
                self::DEFAULT_FOR       => self::CAMPAIGN_GROUPS,
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::CAMPAIGN],
                self::QUERY_KEY         =>
                    "CASE ".
                        "WHEN ".self::CAMPAIGNS_TABLE.".".CompanyCampaign::FIELD_ZIP_CODE_TARGETED." = true THEN ".
                            "CONCAT(".
                                "COUNT(DISTINCT ".self::CCLML_TABLE.".".CompanyCampaignLocationModuleLocation::FIELD_ZIP_CODE."), ".
                                "'/', ".
                                "SUM(DISTINCT ".self::TOTAL_ZIP_TABLE.".".self::TOTAL_ZIP_COUNT.")".
                            ") ".
                        "ELSE ".
                            "CONCAT(".
                                "SUM(DISTINCT ".self::TOTAL_ZIP_TABLE.".".self::TOTAL_ZIP_COUNT."), ".
                                "'/', ".
                                "SUM(DISTINCT ".self::TOTAL_ZIP_TABLE.".".self::TOTAL_ZIP_COUNT.")".
                            ") ".
                    "END as campaign_zip_code_coverage",
            ],
            self::GOOGLE_SMS_VERIFIED_LEADS => [
                self::KEY               => "google_sms_verified_leads",
                self::NAME_KEY          => "Google SMS Verified Leads",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_SUM,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         =>
                    "COUNT(DISTINCT ".
                        "CASE ".
                            "WHEN ".self::CONSUMERS_TABLE.".".Consumer::FIELD_CLASSIFICATION." = ".Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_SMS." ".
                                "AND ".self::CPAR_TABLE.".".ConsumerProductAffiliateRecord::FIELD_TRACK_NAME." IN (".implode(', ', self::GOOGLE_TRACK_NAMES).") ".
                                "THEN ".self::CONSUMER_PRODUCTS_TABLE.".".ConsumerProduct::FIELD_ID." ".
                        "END".
                    ") as google_sms_verified_leads",
            ],
            self::GOOGLE_REVENUE_PER_SMS_VERIFIED_LEAD => [
                self::KEY               => "google_revenue_per_sms_verified_lead",
                self::NAME_KEY          => "Google Revenue per SMS Verified Lead",
                self::TYPE_KEY          => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_AVG,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => [
                    self::FIRST_COLUMN  => self::GOOGLE_SMS_VERIFIED_REVENUE,
                    self::OPERATOR      => '/',
                    self::SECOND_COLUMN => self::GOOGLE_SMS_VERIFIED_LEADS,
                ],
                self::QUERY_TYPE_KEY    => null,
                self::QUERY_KEY         => null,
            ],
            self::GOOGLE_SUGGESTED_TCPA => [
                self::KEY               => "google_suggested_tcpa",
                self::NAME_KEY          => "Google Suggested tCPA",
                self::TYPE_KEY          => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_AVG,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_SOLD_LEGS,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => [
                    self::FIRST_COLUMN  => self::GOOGLE_REVENUE_PER_SMS_VERIFIED_LEAD,
                    self::OPERATOR      => '/',
                    self::SECOND_COLUMN => 'CONSTANT_INT:'.self::TARGET_ROAS,
                ],
                self::QUERY_TYPE_KEY    => null,
                self::QUERY_KEY         => null,
            ],
            self::GOOGLE_CPA => [
                self::KEY               => "google_cpa",
                self::NAME_KEY          => "Google CPA",
                self::TYPE_KEY          => self::COLUMN_TYPE_DOLLAR,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_AVG,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_AD_COST,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => [
                    self::FIRST_COLUMN  => self::GOOGLE_AD_COST,
                    self::OPERATOR      => '/',
                    self::SECOND_COLUMN => self::GOOGLE_SMS_VERIFIED_LEADS,
                ],
                self::QUERY_TYPE_KEY    => null,
                self::QUERY_KEY         => null,
            ],
            self::GOOGLE_DIFF_TCPA_AND_CPA => [
                self::KEY               => "google_diff_cpa_and_tcpa",
                self::NAME_KEY          => "Google diff between CPA and tCPA",
                self::TYPE_KEY          => self::COLUMN_TYPE_PERCENT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_AVG,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_AD_COST,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => [
                    self::FIRST_COLUMN  => self::GOOGLE_CPA,
                    self::OPERATOR      => 'PERCENT_DIFF',
                    self::SECOND_COLUMN => self::GOOGLE_SUGGESTED_TCPA,
                ],
                self::QUERY_TYPE_KEY    => null,
                self::QUERY_KEY         => null,
            ],
            self::PROFIT_MARGIN => [
                self::KEY               => "profit_margin",
                self::NAME_KEY          => "Profit Margin",
                self::TYPE_KEY          => self::COLUMN_TYPE_PERCENT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_AVG,
                self::DISABLED_FOR      => self::NOT_APPLICABLE_FOR_AD_COST,
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => [
                    self::FIRST_COLUMN  => self::GROSS_PROFIT,
                    self::OPERATOR      => '/',
                    self::SECOND_COLUMN => self::TOTAL_REVENUE,
                ],
                self::QUERY_TYPE_KEY    => null,
                self::QUERY_KEY         => null,
            ],
            self::AFFILIATE => [
                self::KEY               => "affiliate",
                self::NAME_KEY          => "Affiliate",
                self::TYPE_KEY          => self::COLUMN_TYPE_STRING,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_NONE,
                self::DISABLED_FOR      => [],
                self::DEFAULT_FOR       => [LeadsReportGroupEnum::AFFILIATE_CAMPAIGN],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => [LeadsReportQueryEnum::LEAD],
                self::QUERY_KEY         =>
                    "GROUP_CONCAT(DISTINCT ".
                        "CONCAT(".
                            self::AFF_TABLE.".".Affiliate::FIELD_NAME.", ".
                            "' (', ".
                            self::AFF_TABLE.".".Affiliate::FIELD_ID.", ".
                            "')'".
                        ") ".
                    "SEPARATOR ', ') as affiliate",
            ],
            default => [
                self::KEY               => "unknown",
                self::NAME_KEY          => "Unknown",
                self::TYPE_KEY          => self::COLUMN_TYPE_COUNT,
                self::DEFAULT_KEY       => false,
                self::TOTAL_KEY         => self::TOTAL_COLUMN_NONE,
                self::DISABLED_FOR      => [],
                self::DEFAULT_FOR       => [],
                self::CALCULATED_KEY    => null,
                self::QUERY_TYPE_KEY    => null,
                self::QUERY_KEY         => null,
            ]
        };
    }

    /**
     * @return string
     */
    public function keyValue(): string
    {
        return self::getKey($this);
    }

    /**
     * @return array
     */
    public static function getFormattedCases(): array
    {
        $formattedCases = [];
        foreach (self::cases() as $case) {
            $formattedCases[$case->value] = $case->getMetaData();
        }
        return $formattedCases;
    }

    /**
     * @param string|null $if
     * @param string|null $text
     * @param string|null $link
     * @param string|null $class
     * @param bool $concat
     * @return string
     */
    public static function infoString(
        string $if = null,
        string $text = null,
        string $link = null,
        string $class = null,
        bool $concat = false,
    ): string
    {
        $infoString = '';
        $concatStringOpen = '';
        $concatStringClose = '';

        if ($concat) {
            $infoString = $infoString.'\'';
            $concatStringOpen = '\', ';
            $concatStringClose = ', \'';

        }

        $infoString = $infoString.'$[';
        if ($if)
            $infoString = $infoString.self::INFO_IF_KEY.'['.$concatStringOpen.$if.$concatStringClose.']';
        if ($text)
            $infoString = $infoString.self::INFO_TEXT_KEY.'['.$concatStringOpen.$text.$concatStringClose.']';
        if ($class)
            $infoString = $infoString.self::INFO_CLASS_KEY.'['.$concatStringOpen.$class.$concatStringClose.']';
        if ($link)
            $infoString = $infoString.self::INFO_LINK_KEY.'['.$concatStringOpen.$link.$concatStringClose.']';
        $infoString = $infoString.']$';

        if ($concat)
            $infoString = $infoString.'\'';

        return $infoString;
    }
}
