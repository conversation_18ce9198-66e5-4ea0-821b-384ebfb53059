<?php

namespace App\Enums\LeadsReport;

use App\Models\Odin\ConsumerProductAffiliateRecord;
use Illuminate\Support\Str;

/**
 * Defines configurations which tell us what types of products can be accepted by a specific budget
 */
enum LeadsReportGroupEnum: string
{
    case DAY                = 'day';
    case WEEK               = 'week';
    case MONTH              = 'month';
    case INDUSTRY           = 'industry';
    case STATE              = 'state';
    case COUNTY             = 'county';
    case ZIP_CODE           = 'zip_code';
    case COMPANY            = 'company';
    case COMPANY_COUNTY     = 'company_county';
    case CAMPAIGN           = 'campaign';
    case CAMPAIGN_COUNTY    = 'campaign_county';
    case CAMPAIGN_ZIP_CODE  = 'campaign_zip';
    case ORIGIN             = 'origin';
    case AFFILIATE          = 'affiliate';
    case AFFILIATE_CAMPAIGN = 'affiliate_campaign';
    case PLATFORM           = 'platform';
    case COMPANY_USER_RELATIONSHIP = 'company_user';

    /**
     * @return string
     */
    public function getDisplayName(): string
    {
        return Str::title(str_replace('_', ' ', $this->value));
    }

    /**
     * @param LeadsReportGroupEnum|int|null $groupEnum
     * @param string $dateString
     * @return string
     */
    public static function getSelectQuery(LeadsReportGroupEnum|int|null $groupEnum, string $dateString): string
    {
        $value = $groupEnum instanceof LeadsReportGroupEnum ? $groupEnum->value : $groupEnum;

        return match ($value) {
            self::DAY->value                => 'DATE_FORMAT('.$dateString.', \'%Y-%m-%d\') as '.LeadsReportColumnEnum::GROUP_KEY,
            self::WEEK->value               => 'CONCAT(DATE_SUB(DATE('.$dateString.'), INTERVAL WEEKDAY('.$dateString.') DAY), \' to \', DATE_ADD(DATE_SUB(DATE('.$dateString.'), INTERVAL WEEKDAY('.$dateString.') DAY), INTERVAL 6 DAY)) as '.LeadsReportColumnEnum::GROUP_KEY,
            self::MONTH->value              => 'CONCAT(YEAR('.$dateString.'), \'-\', LPAD(MONTH('.$dateString.'), 2, \'0\')) as '.LeadsReportColumnEnum::GROUP_KEY,
            self::INDUSTRY->value           => 'i.name as '.LeadsReportColumnEnum::GROUP_KEY,
            self::STATE->value              => 'l.state as '.LeadsReportColumnEnum::GROUP_KEY,
            self::COUNTY->value             => 'CONCAT(l.county, \', \', l.state) as '.LeadsReportColumnEnum::GROUP_KEY,
            self::COMPANY->value            => 'CONCAT(co.name, \' (\', co.id, \')\') as '.LeadsReportColumnEnum::GROUP_KEY,
            self::COMPANY_COUNTY->value     => 'CONCAT(co.name, \' (\', co.id, \')\', \' hide[\', l.county, \', \', l.state_abbr, \']\') as '.LeadsReportColumnEnum::GROUP_KEY,
            self::ZIP_CODE->value           => 'CONCAT(a.zip_code, \' \') as '.LeadsReportColumnEnum::GROUP_KEY,
            self::CAMPAIGN->value           => 'CASE WHEN cc.id IS NOT NULL THEN CONCAT(cc.name, \' (\', i.name, \' \', cc.id, \')\') ELSE \'Legacy Campaign\' END as '.LeadsReportColumnEnum::GROUP_KEY,
            self::CAMPAIGN_COUNTY->value    => 'CONCAT(cc.name, \' (\', i.name, \' \', cc.id, \')\', \' hide[\', l.county, \', \', l.state_abbr, \']\') as '.LeadsReportColumnEnum::GROUP_KEY,
            self::CAMPAIGN_ZIP_CODE->value  => 'CONCAT(cc.name, \' (\', i.name, \' \', cc.id, \')\', \' hide[\', l.zip_code, \']\') as '.LeadsReportColumnEnum::GROUP_KEY,
            self::ORIGIN->value             => 'w.name as '.LeadsReportColumnEnum::GROUP_KEY,
            self::AFFILIATE->value          => 'aff.name as '.LeadsReportColumnEnum::GROUP_KEY,
            self::AFFILIATE_CAMPAIGN->value => 'CASE WHEN afc.id IS NOT NULL THEN CONCAT(afc.name, \' (\', afc.id, \')\') ELSE \'No Campaign\' END as '.LeadsReportColumnEnum::GROUP_KEY,
            self::PLATFORM->value           =>
                'CASE WHEN cpar.'.ConsumerProductAffiliateRecord::FIELD_TRACK_NAME.' IS NOT NULL THEN '.
                    'CASE WHEN cpar.'.ConsumerProductAffiliateRecord::FIELD_TRACK_NAME.' IN (\'adwords\', \'gbraid\', \'google_ads_ios\') THEN \'Google\' '.
                    'WHEN cpar.'.ConsumerProductAffiliateRecord::FIELD_TRACK_NAME.' IN (\'facebook\') THEN \'Meta\' '.
                    'WHEN cpar.'.ConsumerProductAffiliateRecord::FIELD_TRACK_NAME.' IN (\'bing_ads\') THEN \'Microsoft\' '.
                    'ELSE \'Other\' END '.
                'ELSE \'Organic\' END as '.LeadsReportColumnEnum::GROUP_KEY,
            self::COMPANY_USER_RELATIONSHIP->value => 'u.name as '.LeadsReportColumnEnum::GROUP_KEY,
            default => ''
        };
    }

    /**
     * @return LeadsReportGroupEnum[]
     */
    public static function getMatchGroups(): array
    {
        return [
            LeadsReportGroupEnum::INDUSTRY,
            LeadsReportGroupEnum::STATE,
            LeadsReportGroupEnum::COUNTY,
            LeadsReportGroupEnum::ZIP_CODE,
            LeadsReportGroupEnum::COMPANY,
            LeadsReportGroupEnum::COMPANY_COUNTY,
            LeadsReportGroupEnum::CAMPAIGN,
            LeadsReportGroupEnum::CAMPAIGN_COUNTY,
            LeadsReportGroupEnum::CAMPAIGN_ZIP_CODE,
            LeadsReportGroupEnum::ORIGIN,
            LeadsReportGroupEnum::AFFILIATE,
            LeadsReportGroupEnum::AFFILIATE_CAMPAIGN,
            LeadsReportGroupEnum::PLATFORM,
            LeadsReportGroupEnum::COMPANY_USER_RELATIONSHIP,
        ];
    }

    /**
     * @return array
     */
    public static function getAllGroups(): array
    {
        return array_column(self::cases(), 'value');
    }
}
