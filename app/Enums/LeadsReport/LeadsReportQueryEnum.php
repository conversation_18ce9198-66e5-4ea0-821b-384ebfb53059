<?php

namespace App\Enums\LeadsReport;

use App\Builders\Odin\LeadsReportCampaignBuilder;
use App\Builders\Odin\LeadsReportCostBuilder;
use App\Builders\Odin\LeadsReportLegsBuilder;
use App\Contracts\Builders\Odin\LeadsReport\LeadsReportBuilderContract;
use Exception;
use Illuminate\Support\Str;

/**
 * Defines configurations which tell us what types of products can be accepted by a specific budget
 */
enum LeadsReportQueryEnum: string
{
    case LEAD       = 'lead';
    case COST       = 'cost';
    case CAMPAIGN   = 'campaign';

    /**
     * @return string
     */
    public function getDisplayName(): string
    {
        return Str::title(str_replace('_', ' ', $this->value));
    }

    /**
     * @param LeadsReportQueryEnum|string|null $columnEnum
     * @return mixed
     * @throws Exception
     */
    public static function getBuilder(LeadsReportQueryEnum|string|null $columnEnum): LeadsReportBuilderContract
    {
        $value = $columnEnum instanceof LeadsReportQueryEnum ? $columnEnum->value : $columnEnum;

        return match ($value) {
            self::LEAD->value       => LeadsReportLegsBuilder::query(),
            self::COST->value       => LeadsReportCostBuilder::query(),
            self::CAMPAIGN->value   => LeadsReportCampaignBuilder::query(),
            default                 => throw new Exception('Invalid query type given')
        };
    }
}
