<?php

namespace App\Enums\Billing\Refunds;

enum RefundReason: string
{
    case DUPLICATE             = 'duplicate';
    case FRAUDULENT            = 'fraudulent';
    case REQUESTED_BY_CUSTOMER = 'requested_by_customer';
    case OTHER                 = 'other';

    public function getTitle(): string
    {
        return match ($this) {
            self::DUPLICATE             => 'Duplicate',
            self::FRAUDULENT            => 'Fraudulent',
            self::REQUESTED_BY_CUSTOMER => 'Requested By Customer',
            self::OTHER                 => 'Other',
            default                     => $this->value
        };
    }
}
