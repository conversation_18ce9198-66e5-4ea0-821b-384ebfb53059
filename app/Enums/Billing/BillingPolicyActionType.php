<?php

namespace App\Enums\Billing;

use App\BillingWorkflows\Activities\NotifyUser;
use App\BillingWorkflows\Activities\SendEmailNotification;
use App\BillingWorkflows\Activities\SuspendCompany;
use Exception;

/**
 * TODO - FromClass, toClass or even register the events should be dynamic
 */
enum BillingPolicyActionType: string
{
    case SEND_EMAIL_NOTIFICATION = 'send_email_notification';
    case NOTIFY_USER             = 'notify_user';
    case SUSPEND_COMPANY         = 'suspend_company';

    /**
     * @return array
     */
    public static function all(): array
    {
        return array_column(self::cases(), 'value');
    }

    public function getClass(): string
    {
        return match ($this) {
            self::SUSPEND_COMPANY         => SuspendCompany::class,
            self::NOTIFY_USER             => NotifyUser::class,
            self::SEND_EMAIL_NOTIFICATION => SendEmailNotification::class,
            default                       => throw new Exception('Action class not found')
        };
    }

    public static function fromClass(string $class): self
    {
        return match ($class) {
            SuspendCompany::class        => self::SUSPEND_COMPANY,
            NotifyUser::class            => self::NOTIFY_USER,
            SendEmailNotification::class => self::SEND_EMAIL_NOTIFICATION,
            default                      => throw new Exception('Action class not found')
        };
    }
}
