<?php

namespace App\Enums\Billing;

use App\Models\Odin\CompanyUser;
use App\Models\User;

enum InvoiceEventAuthorTypes: string
{
    case USER         = 'user';
    case COMPANY_USER = 'company_user';
    case SYSTEM       = 'system';

    /**
     * @return array
     */
    public static function allTypes(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * @return string
     */
    public function getClass(): string
    {
        return match ($this) {
            self::USER         => User::class,
            self::COMPANY_USER => CompanyUser::class,
            self::SYSTEM       => 'system',
            default            => $this->value
        };
    }
}
