<?php

namespace App\Enums\Billing\Reports;

enum CreditMovementType: string
{
    case ADDED_TO_COMPANY   = 'added_to_company';
    case CREDIT_EXPIRED     = 'credit_expired';
    case APPLIED_TO_INVOICE = 'applied_to_invoice';

    public function getTitle(): string
    {
        return match ($this) {
            self::ADDED_TO_COMPANY   => 'Added To Company',
            self::APPLIED_TO_INVOICE => 'Applied To Invoice',
            self::CREDIT_EXPIRED     => 'Credit Expired',
            default                  => $this->value
        };
    }
}
