<?php

namespace App\Enums\Billing;

use App\Events\Billing\StoredEvents\ActionApprovalRequest\ActionApprovalRequested;
use App\Events\Billing\StoredEvents\ActionApprovalRequest\ActionApprovalReviewed;
use App\Events\Billing\StoredEvents\Credit\CreditAdded;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeDisputeClosed;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeDisputeCreated;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeFailed;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeRequest;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeRequestAttempted;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeRequestFailed;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeRequestSuccess;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeSuccess;
use App\Events\Billing\StoredEvents\Invoice\Collections\IssueInvoiceToCollections;
use App\Events\Billing\StoredEvents\Invoice\Collections\UpdateInvoiceCollections;
use App\Events\Billing\StoredEvents\Invoice\InvoiceInitialized;
use App\Events\Billing\StoredEvents\Invoice\InvoiceItemTaxApplied;
use App\Events\Billing\StoredEvents\Invoice\InvoiceStatusUpdated;
use App\Events\Billing\StoredEvents\Invoice\InvoiceUpdated;
use App\Events\Billing\StoredEvents\Invoice\Pdf\InvoicePdfCreated;
use App\Events\Billing\StoredEvents\Invoice\RequestInvoiceTax;
use App\Events\Billing\StoredEvents\InvoiceItem\InvoiceItemAdded;
use App\Events\Billing\StoredEvents\InvoiceItem\InvoiceItemDeleted;
use App\Events\Billing\StoredEvents\InvoiceItem\InvoiceItemUpdated;
use App\Events\Billing\StoredEvents\Invoice\InvoiceSnapshotCreated;

enum InvoiceEvents: string
{
    case INVOICE_INITIALIZED              = "invoice_initialized";
    case INVOICE_ITEM_ADDED               = "invoice_item_added";
    case REQUEST_INVOICE_TAX              = "request_invoice_tax";
    case CREATE_INVOICE_PDF               = "create_invoice_pdf";
    case INVOICE_ITEM_TAX_APPLIED         = "invoice_item_tax_applied";
    case INVOICE_STATUS_UPDATED           = "invoice_status_updated";
    case INVOICE_CHARGE_REQUEST           = "invoice_charge_request";
    case INVOICE_CHARGE_REQUEST_ATTEMPTED = 'invoice_charge_request_attempted';
    case INVOICE_CHARGE_REQUEST_SUCCESS   = 'invoice_charge_request_success';
    case INVOICE_CHARGE_REQUEST_FAILED    = 'invoice_charge_request_failed';
    case INVOICE_PDF_CREATED              = 'invoice_pdf_created';
    case INVOICE_CHARGE_SUCCESS           = 'invoice_charge_success';
    case INVOICE_CHARGE_FAILED            = 'invoice_charge_failed';
    case INVOICE_UPDATED                  = 'invoice_updated';
    case INVOICE_ITEM_UPDATED             = 'invoice_item_updated';
    case INVOICE_ITEM_DELETED             = 'invoice_item_deleted';
    case INVOICE_ACTION_REQUESTED         = 'invoice_action_requested';
    case INVOICE_ACTION_REQUEST_REVIEWED  = 'invoice_action_request_reviewed';
    case CREDIT_APPLIED_TO_COMPANY        = 'credit_applied_to_company';
    case INVOICE_SNAPSHOT_CREATED         = 'invoice_snapshot_created';
    case ISSUE_INVOICE_TO_COLLECTIONS     = 'issue_invoice_to_collections';
    case UPDATE_INVOICE_COLLECTIONS       = 'update_invoice_collections';
    case INVOICE_CHARGE_DISPUTE_CREATED   = 'invoice_charge_dispute_created';
    case INVOICE_CHARGE_DISPUTE_CLOSED    = 'invoice_charge_dispute_closed';

    /**
     * Handles returning a list of all activity-types.
     *
     * @return array
     */
    public static function allTypes(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * @param string $class
     * @return ?self
     */
    public static function fromClass(string $class): ?self
    {
        return match ($class) {
            InvoiceInitialized::class            => self::INVOICE_INITIALIZED,
            InvoiceItemAdded::class              => self::INVOICE_ITEM_ADDED,
            InvoiceItemUpdated::class            => self::INVOICE_ITEM_UPDATED,
            InvoiceItemDeleted::class            => self::INVOICE_ITEM_DELETED,
            InvoiceItemTaxApplied::class         => self::INVOICE_ITEM_TAX_APPLIED,
            RequestInvoiceTax::class             => self::REQUEST_INVOICE_TAX,
            IssueInvoiceToCollections::class     => self::ISSUE_INVOICE_TO_COLLECTIONS,
            UpdateInvoiceCollections::class      => self::UPDATE_INVOICE_COLLECTIONS,
            InvoiceStatusUpdated::class          => self::INVOICE_STATUS_UPDATED,
            InvoiceChargeRequest::class          => self::INVOICE_CHARGE_REQUEST,
            InvoiceChargeRequestAttempted::class => self::INVOICE_CHARGE_REQUEST_ATTEMPTED,
            InvoiceChargeRequestSuccess::class   => self::INVOICE_CHARGE_REQUEST_SUCCESS,
            InvoiceChargeRequestFailed::class    => self::INVOICE_CHARGE_REQUEST_FAILED,
            InvoicePdfCreated::class             => self::INVOICE_PDF_CREATED,
            InvoiceChargeSuccess::class          => self::INVOICE_CHARGE_SUCCESS,
            InvoiceChargeFailed::class           => self::INVOICE_CHARGE_FAILED,
            InvoiceUpdated::class                => self::INVOICE_UPDATED,
            ActionApprovalRequested::class       => self::INVOICE_ACTION_REQUESTED,
            ActionApprovalReviewed::class        => self::INVOICE_ACTION_REQUEST_REVIEWED,
            CreditAdded::class                   => self::CREDIT_APPLIED_TO_COMPANY,
            InvoiceChargeDisputeCreated::class   => self::INVOICE_CHARGE_DISPUTE_CREATED,
            InvoiceChargeDisputeClosed::class    => self::INVOICE_CHARGE_DISPUTE_CLOSED,
            InvoiceSnapshotCreated::class        => self::INVOICE_SNAPSHOT_CREATED,
            default                              => null,
        };
    }

    public function getTitle(): ?string
    {
        return match ($this) {
            self::INVOICE_INITIALIZED              => "Invoice Initialised",
            self::INVOICE_ITEM_ADDED               => "Invoice Item Added",
            self::INVOICE_ITEM_UPDATED             => "Invoice Item Updated",
            self::INVOICE_ITEM_DELETED             => "Invoice Item Deleted",
            self::REQUEST_INVOICE_TAX              => "Invoice Tax Calculation Request Dispatched",
            self::CREATE_INVOICE_PDF               => "Invoice PDF Request Dispatched",
            self::INVOICE_ITEM_TAX_APPLIED         => "Invoice Item Tax Applied",
            self::INVOICE_STATUS_UPDATED           => "Invoice Status Updated",
            self::INVOICE_CHARGE_REQUEST           => "Invoice Charge Request Dispatched",
            self::INVOICE_CHARGE_REQUEST_ATTEMPTED => "Invoice Charge Request Attempted",
            self::INVOICE_CHARGE_REQUEST_SUCCESS   => "Invoice Charge Request Succeeded",
            self::INVOICE_CHARGE_REQUEST_FAILED    => "Invoice Charge Request Failed",
            self::INVOICE_PDF_CREATED              => "Invoice Pdf Created",
            self::INVOICE_CHARGE_SUCCESS           => "Invoice Charge Success",
            self::INVOICE_CHARGE_FAILED            => "Invoice Charge Failed",
            self::INVOICE_UPDATED                  => "Invoice Updated",
            self::CREDIT_APPLIED_TO_COMPANY        => "Credit Applied to Company",
            self::INVOICE_ACTION_REQUESTED         => "Action Requested",
            self::INVOICE_ACTION_REQUEST_REVIEWED  => "Action Reviewed",
            self::INVOICE_SNAPSHOT_CREATED         => "Invoice Snapshot Created",
            self::UPDATE_INVOICE_COLLECTIONS       => "Invoice Collections Updated",
            self::INVOICE_CHARGE_DISPUTE_CREATED   => "Invoice Chargeback Created",
            self::INVOICE_CHARGE_DISPUTE_CLOSED    => "Invoice Chargeback Closed",
            default                                => null
        };
    }
}
