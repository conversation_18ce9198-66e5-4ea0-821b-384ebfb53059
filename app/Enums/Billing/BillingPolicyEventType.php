<?php

namespace App\Enums\Billing;

use App\Events\Billing\LaravelEvents\InvoiceFailed;
use App\Events\Billing\LaravelEvents\InvoiceIssued;
use App\Events\Billing\LaravelEvents\InvoicePaid;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeFailed;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeRequestMaxAttemptsExceeded;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoicePaymentDue;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoicePaymentFailed;
use Illuminate\Support\Collection;

enum BillingPolicyEventType: string
{
    case INVOICE_CHARGE_MAX_ATTEMPTS_EXCEEDED = 'invoice_charge_max_attempts_exceeded';
    case INVOICE_PAYMENT_FAILED               = 'invoice_payment_failed';
    case INVOICE_PAYMENT_DUE                  = 'invoice_payment_due';
    case INVOICE_FAILED                       = 'invoice_failed';
    case INVOICE_ISSUED                       = 'invoice_issued';
    case INVOICE_PAID                         = 'invoice_paid';
    case INVOICE_CHARGE_FAILED                = 'invoice_charge_failed';

    /**
     * @return array
     */
    public static function all(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * @return Collection
     */
    public static function allEventClasses(): Collection
    {
        return collect(self::cases())->map(fn(self $test) => $test->getClass());
    }

    /**
     * @return ?string
     */
    public function getClass(): ?string
    {
        return match ($this) {
            self::INVOICE_CHARGE_MAX_ATTEMPTS_EXCEEDED => InvoiceChargeRequestMaxAttemptsExceeded::class,
            self::INVOICE_PAID                         => InvoicePaid::class,
            self::INVOICE_FAILED                       => InvoiceFailed::class,
            self::INVOICE_ISSUED                       => InvoiceIssued::class,
            self::INVOICE_CHARGE_FAILED                => InvoiceChargeFailed::class,
            self::INVOICE_PAYMENT_FAILED               => InvoicePaymentFailed::class,
            self::INVOICE_PAYMENT_DUE                  => InvoicePaymentDue::class,
            default                                    => null
        };
    }

    /**
     * @param string $class
     * @return BillingPolicyEventType|null
     */
    public static function fromClass(string $class): ?self
    {
        return match ($class) {
            InvoiceChargeRequestMaxAttemptsExceeded::class => self::INVOICE_CHARGE_MAX_ATTEMPTS_EXCEEDED,
            InvoiceFailed::class                           => self::INVOICE_FAILED,
            InvoicePaid::class                             => self::INVOICE_PAID,
            InvoiceIssued::class                           => self::INVOICE_ISSUED,
            InvoiceChargeFailed::class                     => self::INVOICE_CHARGE_FAILED,
            InvoicePaymentFailed::class                    => self::INVOICE_PAYMENT_FAILED,
            InvoicePaymentDue::class                       => self::INVOICE_PAYMENT_DUE,
            default                                        => null,
        };
    }
}
