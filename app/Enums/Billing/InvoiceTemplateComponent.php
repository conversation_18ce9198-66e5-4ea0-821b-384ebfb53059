<?php

namespace App\Enums\Billing;

use App\Services\Billing\InvoiceTemplateRenderer\InvoiceFoundationComponent;
use Exception;

enum InvoiceTemplateComponent: string
{
    case INVOICE_FOUNDATION = 'invoice_foundation';

    /**
     * @return array
     */
    public static function all(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * @return string
     * @throws Exception
     */
    public function getClass(): string
    {
        return match ($this) {
            self::INVOICE_FOUNDATION => InvoiceFoundationComponent::class,
            default                  => throw new Exception('Action class not found for ' . $this->value)
        };
    }

    /**
     * @return string
     * @throws Exception
     */
    public function getName(): string
    {
        return match ($this) {
            self::INVOICE_FOUNDATION => 'Invoice Foundation',
            default                  => $this->value
        };
    }

    /**
     * @param string $class
     * @return self
     * @throws Exception
     */
    public static function fromClass(string $class): self
    {
        return match ($class) {
            InvoiceFoundationComponent::class => self::INVOICE_FOUNDATION,
            default                           => throw new Exception('Action not found for ' . $class)
        };
    }
}
