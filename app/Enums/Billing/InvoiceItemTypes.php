<?php

namespace App\Enums\Billing;

use App\Models\Bundle;
use App\Models\Odin\ProductAssignment;
use App\Models\Billing\CreditType;


enum InvoiceItemTypes: string
{
    case CREDIT  = 'credit';
    case PRODUCT = 'product';
    case MANUAL  = 'manual';
    case BUNDLE  = 'bundle';

    /**
     * Handles returning a list of all activity-types.
     *
     * @return array
     */
    public static function allTypes(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * @return string|null
     */
    public function getClass(): ?string
    {
        return match ($this) {
            self::CREDIT  => CreditType::class,
            self::PRODUCT => ProductAssignment::class,
            self::MANUAL  => self::MANUAL->value,
            self::BUNDLE  => Bundle::class,
            default       => null,
        };
    }

    /**
     * @param string $class
     * @return self
     */
    public static function fromClass(string $class): self
    {
        return match ($class) {
            CreditType::class        => self::CREDIT,
            ProductAssignment::class => self::PRODUCT,
            Bundle::class            => self::BUNDLE,
            default                  => self::MANUAL,
        };
    }

    public function getTitle(): ?string
    {
        return match ($this) {
            self::CREDIT  => 'Credit',
            self::PRODUCT => 'Product',
            self::MANUAL  => 'Manual',
            self::BUNDLE  => 'Bundle',
            default       => $this->value,
        };
    }
}
