<?php

namespace App\Enums\Billing;

use App\States\Billing\Chargeback;
use App\States\Billing\Deleted;
use App\States\Billing\Draft;
use App\States\Billing\Failed;
use App\States\Billing\Issued;
use App\States\Billing\Paid;
use App\States\Billing\Refunded;
use App\States\Billing\Voided;
use App\States\Billing\Collection;
use App\States\Billing\WrittenOff;

enum InvoiceStates: string
{
    case DRAFT       = 'draft';
    case ISSUED      = 'issued';
    case PAID        = 'paid';
    case FAILED      = 'failed';
    case VOIDED      = 'voided';
    case DELETED     = 'deleted';
    case COLLECTION  = 'collection';
    case REFUNDED    = 'refunded';
    case CHARGEBACK  = 'chargeback';
    case WRITTEN_OFF = 'written_off';

    /**
     * Handles returning a list of all activity-types.
     *
     * @return array
     */
    public static function allTypes(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * @return string
     */
    public function getTitle(): string
    {
        return match ($this) {
            self::DRAFT       => 'Draft',
            self::ISSUED      => 'Issued',
            self::PAID        => 'Paid',
            self::FAILED      => 'Failed',
            self::VOIDED      => 'Voided',
            self::DELETED     => 'Deleted',
            self::COLLECTION  => 'Collection',
            self::REFUNDED    => 'Refunded',
            self::CHARGEBACK  => 'Chargeback',
            self::WRITTEN_OFF => 'Written Off',
        };
    }

    /**
     * @return string
     */
    public function getActionTitle(): string
    {
        return match ($this) {
            self::DRAFT       => 'Draft',
            self::ISSUED      => 'Issue',
            self::PAID        => 'Pay',
            self::FAILED      => 'Fail',
            self::VOIDED      => 'Void',
            self::DELETED     => 'Delete',
            self::REFUNDED    => 'Refund',
            self::CHARGEBACK  => 'Chargeback',
            self::COLLECTION  => 'Debt Collector',
            self::WRITTEN_OFF => 'Write Off',
            default           => $this->getTitle()
        };
    }


    /**
     * @return string
     */
    public function getClass(): string
    {
        return match ($this) {
            self::DRAFT       => Draft::class,
            self::ISSUED      => Issued::class,
            self::PAID        => Paid::class,
            self::FAILED      => Failed::class,
            self::VOIDED      => Voided::class,
            self::DELETED     => Deleted::class,
            self::COLLECTION  => Collection::class,
            self::REFUNDED    => Refunded::class,
            self::CHARGEBACK  => Chargeback::class,
            self::WRITTEN_OFF => WrittenOff::class,
        };
    }

    /**
     * @return InvoiceStates[]
     */
    public static function getInvoiceItemsLockedStates(): array
    {
        return [
            self::DRAFT,
            self::ISSUED,
            self::PAID,
            self::FAILED,
            self::COLLECTION,
            self::REFUNDED,
            self::CHARGEBACK,
            self::WRITTEN_OFF,
        ];
    }
}
