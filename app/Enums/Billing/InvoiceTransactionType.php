<?php

namespace App\Enums\Billing;

use Illuminate\Support\Collection;

enum InvoiceTransactionType: string
{
    case ISSUED      = 'issued';
    case PAYMENT     = 'payment';
    case DISPUTE     = 'dispute';
    case COLLECTIONS = 'collections';
    case REFUND      = 'refund';
    case CREDIT      = 'credit';
    case WRITTEN_OFF = 'written_off';

    /**
     * @return string
     */
    public function getTitle(): string
    {
        return match ($this) {
            self::PAYMENT     => 'Payment',
            self::DISPUTE     => 'Chargeback',
            self::REFUND      => 'Refund',
            self::COLLECTIONS => 'Collections',
            self::ISSUED      => 'Issued',
            self::CREDIT      => 'Credit applied',
            self::WRITTEN_OFF => 'Written Off',
            default           => $this->value
        };
    }

    /**
     * @return Collection
     */
    public static function all(): Collection
    {
        return collect(self::cases())->map(fn(InvoiceTransactionType $type) => $type->value);
    }

    /**
     * @return InvoiceStates|null
     */
    public function getParentInvoiceStatus(): ?InvoiceStates
    {
        return match ($this) {
            self::PAYMENT     => InvoiceStates::PAID,
            self::DISPUTE     => InvoiceStates::CHARGEBACK,
            self::REFUND      => InvoiceStates::REFUNDED,
            self::COLLECTIONS => InvoiceStates::COLLECTION,
            self::ISSUED      => InvoiceStates::ISSUED,
            self::WRITTEN_OFF      => InvoiceStates::ISSUED,
            default           => null
        };
    }
}
