<?php

namespace App\Enums\Billing;

use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use Exception;

enum InvoiceTemplateModelType: string
{
    case INDUSTRY         = 'industry';
    case INDUSTRY_SERVICE = 'industry_service';

    /**
     * @return array
     */
    public static function all(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * @return string
     * @throws Exception
     */
    public function getClass(): string
    {
        return match ($this) {
            self::INDUSTRY         => Industry::class,
            self::INDUSTRY_SERVICE => IndustryService::class,
            default                => throw new Exception('Class not found for ' . $this->value),
        };
    }

    /**
     * @return string
     * @throws Exception
     */
    public function getName(): string
    {
        return match ($this) {
            self::INDUSTRY         => 'Industry',
            self::INDUSTRY_SERVICE => 'Industry Service',
            default                => $this->value
        };
    }

    /**
     * @param string $class
     * @return InvoiceTemplateModelType
     * @throws Exception
     */
    public static function fromClass(string $class): InvoiceTemplateModelType
    {
        return match ($class) {
            Industry::class        => self::INDUSTRY,
            IndustryService::class => self::INDUSTRY_SERVICE,
            default                => throw new Exception('Case not found for ' . $class),
        };
    }
}
