<?php

namespace App\Enums;

use UnitEnum;

enum CompanyConsolidatedStatus: int
{
    private const ADMIN_STATUS_MERGED        = -2;
    private const ADMIN_STATUS_ALL_SUSPENDED = 2;
    private const ADMIN_STATUS_COLLECTIONS   = 3;
    private const ADMIN_STATUS_ARCHIVED      = 4;
    private const ADMIN_STATUS_ADMIN_LOCKED  = 5;

    private const STATUS_CAMPAIGNS_ACTIVE              = 1;
    private const STATUS_CAMPAIGNS_PAUSED              = 2;
    private const STATUS_CAMPAIGNS_OFF                 = 3;
    private const STATUS_CAMPAIGNS_OFF_NEVER_PURCHASED = 4;
    private const STATUS_REGISTERING                   = 5;
    private const STATUS_PENDING_APPROVAL              = 6;
    private const STATUS_PROFILE_ONLY                  = 7;

    const STRING_CAMPAIGNS_ACTIVE              = 'Campaigns Active';
    const STRING_CAMPAIGNS_PAUSED              = 'Campaigns Paused';
    const STRING_CAMPAIGNS_OFF                 = 'Campaigns Off';
    const STRING_CAMPAIGNS_OFF_NEVER_PURCHASED = 'Campaigns Off (Never Purchased)';
    const STRING_REGISTERING                   = 'Registering';
    const STRING_PENDING_APPROVAL              = 'Pending Approval';
    const STRING_PROFILE_ONLY                  = 'Profile Only';
    const STRING_PART_SUSPENDED                = 'Suspended (Some Campaigns)';
    const STRING_ALL_SUSPENDED                 = 'Suspended (All Campaigns)';
    const STRING_COLLECTIONS                   = 'Collections';
    const STRING_ARCHIVED                      = 'Archived';
    const STRING_ADMIN_LOCKED                  = 'Admin Locked';
    const STRING_STATUS_MERGED                 = "Merged into another Company";

    case COMPANY_CONSOLIDATED_STATUS_CANNOT_RECEIVE_LEADS          = 0;
    case COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS             = 1;

    /** @deprecated  */
    case COMPANY_CONSOLIDATED_STATUS_MERGED                        = -2;
    /** @deprecated  */
    case COMPANY_CONSOLIDATED_STATUS_CAMPAIGNS_PAUSED              = 2;
    /** @deprecated  */
    case COMPANY_CONSOLIDATED_STATUS_CAMPAIGNS_OFF                 = 3;
    /** @deprecated  */
    case COMPANY_CONSOLIDATED_STATUS_CAMPAIGNS_OFF_NEVER_PURCHASED = 4;
    /** @deprecated  */
    case COMPANY_CONSOLIDATED_STATUS_REGISTERING                   = 5;
    /** @deprecated  */
    case COMPANY_CONSOLIDATED_STATUS_PENDING_APPROVAL              = 6;
    /** @deprecated  */
    case COMPANY_CONSOLIDATED_STATUS_PROFILE_ONLY                  = 7;
    /** @deprecated  */
    case COMPANY_CONSOLIDATED_STATUS_SUSPENDED_ALL_CAMPAIGNS       = 8;
    /** @deprecated  */
    case COMPANY_CONSOLIDATED_STATUS_COLLECTIONS                   = 9;
    /** @deprecated  */
    case COMPANY_CONSOLIDATED_STATUS_ARCHIVED                      = 10;
    /** @deprecated  */
    case COMPANY_CONSOLIDATED_STATUS_ADMIN_LOCKED                  = 11;

    /**
     * @return array
     */
    public static function asSelectArray(): array
    {
        $result = [];

        foreach (self::cases() as $case) {
            $result[] = [
                'id'   => $case->value,
                'name' => self::label($case->value)
            ];
        }

        return $result;
    }

    public static function getAsKeyValueSelectArray(): array
    {
        $result = [];

        /**
         * @var UnitEnum $value
         */
        foreach (self::cases() as $case) {
            $result[self::label($case->value)] = $case->value;
        }

        return $result;
    }

    /**
     * @param CompanyConsolidatedStatus|int|null $companyConsolidatedStatus
     * @return string
     */
    public static function label(CompanyConsolidatedStatus|int|null $companyConsolidatedStatus): string
    {
        $value = $companyConsolidatedStatus instanceof CompanyConsolidatedStatus ? $companyConsolidatedStatus->value : $companyConsolidatedStatus;

        return match ($value) {
            self::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS->value             => 'Can Receive Leads',
            self::COMPANY_CONSOLIDATED_STATUS_CANNOT_RECEIVE_LEADS->value          => "Cannot Receive Leads",
            self::COMPANY_CONSOLIDATED_STATUS_CAMPAIGNS_PAUSED->value              => self::STRING_CAMPAIGNS_PAUSED,
            self::COMPANY_CONSOLIDATED_STATUS_CAMPAIGNS_OFF->value                 => self::STRING_CAMPAIGNS_OFF,
            self::COMPANY_CONSOLIDATED_STATUS_CAMPAIGNS_OFF_NEVER_PURCHASED->value => self::STRING_CAMPAIGNS_OFF_NEVER_PURCHASED,
            self::COMPANY_CONSOLIDATED_STATUS_REGISTERING->value                   => self::STRING_REGISTERING,
            self::COMPANY_CONSOLIDATED_STATUS_PENDING_APPROVAL->value              => self::STRING_PENDING_APPROVAL,
            self::COMPANY_CONSOLIDATED_STATUS_PROFILE_ONLY->value                  => self::STRING_PROFILE_ONLY,
            self::COMPANY_CONSOLIDATED_STATUS_SUSPENDED_ALL_CAMPAIGNS->value       => self::STRING_ALL_SUSPENDED,
            self::COMPANY_CONSOLIDATED_STATUS_COLLECTIONS->value                   => self::STRING_COLLECTIONS,
            self::COMPANY_CONSOLIDATED_STATUS_ARCHIVED->value                      => self::STRING_ARCHIVED,
            self::COMPANY_CONSOLIDATED_STATUS_ADMIN_LOCKED->value                  => self::STRING_ADMIN_LOCKED,
            self::COMPANY_CONSOLIDATED_STATUS_MERGED->value                        => self::STRING_STATUS_MERGED,
            default                                                                => 'Unknown'
        };
    }

    /**
     * @param int|null $adminStatus
     * @param int $basicStatus
     * @return CompanyConsolidatedStatus|null
     */
    public static function get(?int $adminStatus, int $basicStatus): ?CompanyConsolidatedStatus
    {
        return match ($adminStatus) {
            self::ADMIN_STATUS_ALL_SUSPENDED => CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_SUSPENDED_ALL_CAMPAIGNS,
            self::ADMIN_STATUS_COLLECTIONS   => CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_COLLECTIONS,
            self::ADMIN_STATUS_ARCHIVED      => CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_ARCHIVED,
            self::ADMIN_STATUS_ADMIN_LOCKED  => CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_ADMIN_LOCKED,
            self::ADMIN_STATUS_MERGED        => CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_MERGED,
            default                          => match ($basicStatus) {
                self::STATUS_CAMPAIGNS_ACTIVE              => CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS,
                self::STATUS_CAMPAIGNS_PAUSED              => CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAMPAIGNS_PAUSED,
                self::STATUS_CAMPAIGNS_OFF                 => CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAMPAIGNS_OFF,
                self::STATUS_CAMPAIGNS_OFF_NEVER_PURCHASED => CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAMPAIGNS_OFF_NEVER_PURCHASED,
                self::STATUS_REGISTERING                   => CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_REGISTERING,
                self::STATUS_PENDING_APPROVAL              => CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_PENDING_APPROVAL,
                self::STATUS_PROFILE_ONLY                  => CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_PROFILE_ONLY,
                default                                    => null,
            },
        };
    }

    /**
     * @return CompanyConsolidatedStatus[]
     */
    public static function consolidatedStatuses(): array
    {
        return [
            self::COMPANY_CONSOLIDATED_STATUS_MERGED,
            self::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS,
            self::COMPANY_CONSOLIDATED_STATUS_CAMPAIGNS_PAUSED,
            self::COMPANY_CONSOLIDATED_STATUS_CAMPAIGNS_OFF,
            self::COMPANY_CONSOLIDATED_STATUS_CAMPAIGNS_OFF_NEVER_PURCHASED,
            self::COMPANY_CONSOLIDATED_STATUS_REGISTERING,
            self::COMPANY_CONSOLIDATED_STATUS_PENDING_APPROVAL,
            self::COMPANY_CONSOLIDATED_STATUS_PROFILE_ONLY,
            self::COMPANY_CONSOLIDATED_STATUS_SUSPENDED_ALL_CAMPAIGNS,
            self::COMPANY_CONSOLIDATED_STATUS_COLLECTIONS,
            self::COMPANY_CONSOLIDATED_STATUS_ARCHIVED,
            self::COMPANY_CONSOLIDATED_STATUS_ADMIN_LOCKED,
        ];
    }

    /**
     * @return bool
     */
    public function canPurchase(): bool
    {
        return match($this) {
            self::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS,
            self::COMPANY_CONSOLIDATED_STATUS_CAMPAIGNS_OFF,
            self::COMPANY_CONSOLIDATED_STATUS_CAMPAIGNS_PAUSED
                    => true,
            default => false,
        };
    }
}
