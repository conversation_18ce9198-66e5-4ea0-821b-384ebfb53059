<?php

namespace App\Enums\Alert;
enum AlertType: string
{
    case CAMPAIGN_OVER_BUDGET = 'campaign_over_budget';
    case CAMPAIGN_THRESHOLD = 'campaign_threshold';
    case UNPROCESSED_LEADS = 'unprocessed_leads';

    /**
     * @return string
     */
    public function toReadableString(): string
    {
        return match ($this) {
            self::UNPROCESSED_LEADS => 'Unprocessed Leads',
            self::CAMPAIGN_THRESHOLD => 'Campaign Usage Threshold',
            self::CAMPAIGN_OVER_BUDGET => 'Campaign Over Budget'
        };
    }
}
