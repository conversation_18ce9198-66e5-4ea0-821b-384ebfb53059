<?php

namespace App\Enums;



enum SingleProductSaleStatus: int {

    case FAILED = 0;
    case INITIAL = 1;
    case PAID = 2;

    public function getStatusString(): string
    {
        return match ($this) {
            self::FAILED     => 'Failed',
            self::INITIAL    => 'Initial',
            self::PAID       => 'Paid',
        };
    }

    /**
     * @param string $display
     * @return SingleProductSaleStatus
     */
    public static function fromDisplayString(string $display): SingleProductSaleStatus
    {
        return match ($display) {
            'Failed' => self::FAILED,
            'Initial' => self::INITIAL,
            'Paid' => self::PAID,
            default => throw new \RuntimeException("{$display} is not a valid Single Product Sale Status")
        };
    }

    /**
     * @param string $display
     * @return SingleProductSaleStatus|null
     */
    public static function tryFromDisplayString(string $display): ?SingleProductSaleStatus
    {
        try {
            return SingleProductSaleStatus::fromDisplayString($display);
        } catch(\RuntimeException $e) {
            return null;
        }
    }
}
