<?php

namespace App\Enums;

use Exception;

enum CompanyMediaAssetType: string
{
    case MEDIA      = '0'; // files related to image & video such as jpg/png/mp4 etc
    case LINK       = '1'; // links from online platforms such as YouTube etc
    case ATTACHMENT = '2'; // files other than image & video such as doc/docx/pdf etc

    /**
     * Returns a list of all media asset types.
     *
     * @return array
     */
    public static function allTypes(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Returns label (name) for the given media asset type.
     *
     * @param string $mediaAssetType
     * @return string
     * @throws Exception
     */
    public static function displayName(string $mediaAssetType): string
    {
        return match($mediaAssetType) {
            self::MEDIA->value      => 'media',
            self::LINK->value       => 'link',
            self::ATTACHMENT->value => 'attachment',
            default                 => throw new Exception("Invalid media asset type: $mediaAssetType"),
        };
    }
}
