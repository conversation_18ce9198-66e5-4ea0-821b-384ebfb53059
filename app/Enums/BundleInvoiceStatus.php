<?php

namespace App\Enums;


use App\Enums\Billing\InvoiceStates;
use App\Models\BundleInvoice;

enum BundleInvoiceStatus: int
{

    case CANCELLED = -1;
    case NEW       = 0;
    case ISSUED    = 1;
    case PAID      = 2;
    case COMPLETE  = 3;
    case PENDING   = 4;

    public function getStatusString(): string
    {
        return match ($this) {
            self::CANCELLED => 'Cancelled',
            self::NEW       => 'New',
            self::ISSUED    => 'Issued',
            self::PAID      => 'Paid',
            self::COMPLETE  => 'Complete',
            self::PENDING   => 'Pending',
        };
    }

    /**
     * @param string $display
     * @return BundleInvoiceStatus
     */
    public static function fromDisplayString(string $display): BundleInvoiceStatus
    {
        return match ($display) {
            'Cancelled' => self::CANCELLED,
            'New'       => self::NEW,
            'Issued'    => self::ISSUED,
            'Paid'      => self::PAID,
            'Complete'  => self::COMPLETE,
            'Pending'   => self::PENDING,
            default     => throw new \RuntimeException("{$display} is not a valid Bundle Invoice Status")
        };
    }

    /**
     * @param string $display
     * @return BundleInvoiceStatus|null
     */
    public static function tryFromDisplayString(string $display): ?BundleInvoiceStatus
    {
        try {
            return BundleInvoiceStatus::fromDisplayString($display);
        } catch (\RuntimeException $e) {
            return null;
        }
    }

    public static function tryFromInvoiceStatus(string $invoiceStatus): BundleInvoiceStatus
    {
        return match ($invoiceStatus) {
            InvoiceStates::ISSUED->value => self::ISSUED,
            InvoiceStates::PAID->value   => self::PAID,
            InvoiceStates::VOIDED->value,
            InvoiceStates::COLLECTION->value,
            InvoiceStates::FAILED->value => self::CANCELLED,
            default                      => null
        };
    }

    /**
     * Returns the status that a transition should move an invoice to.
     * Eg: 'Cancel' transition has a resulting status of 'Cancelled'
     * 'Approve' and 'Deny' have a resulting status of Complete
     *
     * @param string $transition
     * @return BundleInvoiceStatus
     */
    public static function fromTransitionString(string $transition): BundleInvoiceStatus
    {
        return match ($transition) {
            BundleInvoice::TRANSITION_ISSUE   => self::ISSUED,
            BundleInvoice::TRANSITION_PAID    => self::PAID,
            BundleInvoice::TRANSITION_APPROVE,
            BundleInvoice::TRANSITION_DENY    => self::COMPLETE,
            BundleInvoice::TRANSITION_CANCEL  => self::CANCELLED,
            BundleInvoice::TRANSITION_PENDING => self::PENDING,
            default                           => throw new \RuntimeException("{$transition} is not a valid Bundle Invoice Transition")
        };
    }

    /**
     * @param string $transition
     * @return BundleInvoiceStatus|null
     */
    public static function tryFromTransitionString(string $transition): ?BundleInvoiceStatus
    {
        try {
            return BundleInvoiceStatus::fromTransitionString($transition);
        } catch (\RuntimeException $e) {
            return null;
        }
    }
}
