<?php

namespace App\Enums;

/**
 * Adding a column
 * - It is important to include the new column in all functions that the other columns are included in
 * - Calculated columns can be either string concat or computed
 * - Copy another existing column's format when adding a new one
 */
enum PingPostVariableEnum: string
{
    case ENABLED                = 'enabled';
    case EXCLUDED_INDUSTRIES    = 'excluded_industry_slugs';
    case PING_ONLY_MODE         = 'ping_only_mode';
    case HOURS_SINCE_ACTIVE     = 'hours_since_active';
    case HOURS_UNTIL_REACT      = 'hours_until_react';
    case LOG_LEVEL              = 'log_level';
    case ONLY_PUBLISHERS        = 'only_publishers';
    case PRINT                  = 'print';
    case AUTOMATION_USER_ID     = 'automation_user_id';
    case ONLY_INACTIVE_INDUSTRIES = 'only_inactive_industries';

    // Meta Data Keys - each variable has these defined
    const string KEY_NAME       = 'name';
    const string KEY_DESC       = 'description';
    const string KEY_ARG        = 'argument';
    const string KEY_DEFAULT    = 'default';
    const string KEY_TYPE       = 'type';

    // Variable types - inputs will be cast to these
    const string TYPE_INT       = 'int';
    const string TYPE_STRING    = 'string';
    const string TYPE_FLOAT     = 'float';
    const string TYPE_BOOL      = 'bool';
    const string TYPE_LIST      = 'list';

    /**
     * @return array
     */
    public function getMetaData(): array
    {
        return match ($this) {
            self::ENABLED => [
                self::KEY_NAME          => 'Enabled',
                self::KEY_DESC          => 'This flag must be true for ping post automation to run.',
                self::KEY_ARG           => null, // Null key arg means this is not an available command parameter
                self::KEY_DEFAULT       => false, // Must be enabled in global config management to run automatically
                self::KEY_TYPE          => self::TYPE_BOOL,
            ],
            self::EXCLUDED_INDUSTRIES => [
                self::KEY_NAME          => 'Excluded Industry Slugs',
                self::KEY_DESC          => 'Comma separated list of industry slugs to exclude.',
                self::KEY_ARG           => 'exclude-industries',
                self::KEY_DEFAULT       => ['solar'],
                self::KEY_TYPE          => self::TYPE_LIST,
            ],
            self::PING_ONLY_MODE => [
                self::KEY_NAME          => 'Ping Only Mode',
                self::KEY_DESC          => 'Flag to run ping post logic and only ping to see potential bids.',
                self::KEY_ARG           => 'ping-only-mode',
                self::KEY_DEFAULT       => false,
                self::KEY_TYPE          => self::TYPE_BOOL,
            ],
            self::HOURS_SINCE_ACTIVE => [
                self::KEY_NAME          => 'Hours Since Active',
                self::KEY_DESC          => 'Minimum hours since campaign has been active in zip code to consider lead for ping post.',
                self::KEY_ARG           => 'hours-since-active',
                self::KEY_DEFAULT       => 72,
                self::KEY_TYPE          => self::TYPE_INT,
            ],
            self::HOURS_UNTIL_REACT => [
                self::KEY_NAME          => 'Hours Until Reactivation',
                self::KEY_DESC          => 'Minimum hours until temporarily paused campaigns activate in zip code to consider lead for ping post.',
                self::KEY_ARG           => 'hours-until-react',
                self::KEY_DEFAULT       => 72,
                self::KEY_TYPE          => self::TYPE_INT,
            ],
            self::LOG_LEVEL => [
                self::KEY_NAME          => 'Log Level',
                self::KEY_DESC          => '0: No Logs, 1: Error Logs, 2: Info and Error Logs',
                self::KEY_ARG           => 'log-level',
                self::KEY_DEFAULT       => 2,
                self::KEY_TYPE          => self::TYPE_INT,
            ],
            self::PRINT => [
                self::KEY_NAME          => 'Print',
                self::KEY_DESC          => 'Print logs to terminal',
                self::KEY_ARG           => 'print',
                self::KEY_DEFAULT       => false,
                self::KEY_TYPE          => self::TYPE_BOOL,
            ],
            self::AUTOMATION_USER_ID => [
                self::KEY_NAME          => 'Automation User ID',
                self::KEY_DESC          => 'User ID to be used for the allocation consumer product activity record.',
                self::KEY_ARG           => 'automation-user-id',
                self::KEY_DEFAULT       => 507,
                self::KEY_TYPE          => self::TYPE_INT,
            ],
            self::ONLY_PUBLISHERS => [
                self::KEY_NAME          => 'Only Publishers',
                self::KEY_DESC          => 'Comma separated list of publisher keys to include.',
                self::KEY_ARG           => 'only-publishers',
                self::KEY_DEFAULT       => [],
                self::KEY_TYPE          => self::TYPE_LIST,
            ],
            self::ONLY_INACTIVE_INDUSTRIES => [
                self::KEY_NAME          => 'Only Inactive Industries',
                self::KEY_DESC          => 'Only run ping post automation for inactive industriess',
                self::KEY_ARG           => 'only-inactive-industries',
                self::KEY_DEFAULT       => true,
                self::KEY_TYPE          => self::TYPE_BOOL,
            ],
            default => [
                self::KEY_NAME          => 'Unknown',
                self::KEY_DESC          => 'Undefined upsell automation parameter.',
                self::KEY_ARG           => null,
                self::KEY_DEFAULT       => '',
                self::KEY_TYPE          => self::TYPE_STRING,
            ]
        };
    }

    /**
     * @param mixed $variable
     * @return mixed
     */
    public function castToType(mixed $variable): mixed
    {
        return match ($this->getMetaData()[self::KEY_TYPE]) {
            self::TYPE_INT      => (int)$variable,
            self::TYPE_STRING   => (string)$variable,
            self::TYPE_FLOAT    => (float)$variable,
            self::TYPE_BOOL     => filter_var($variable, FILTER_VALIDATE_BOOLEAN),
            self::TYPE_LIST     => $this->parseList($variable),
            default             => $variable,
        };
    }

    /**
     * @param mixed $variable
     * @return array
     */
    public function parseList(mixed $variable): array
    {
        if (is_string($variable))
            return explode(',', $variable);
        if (is_array($variable))
            return $variable;
        return [];
    }
}
