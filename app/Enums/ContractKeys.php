<?php

namespace App\Enums;

enum ContractKeys: string
{
    // Bundle Contract
    case LEAD_BUYING_AGREEMENT  = 'lead-buying-agreement';
    // Company registration
    case TERMS_AND_CONDITIONS   = 'terms-and-conditions';
    // Dashboard
    case SITE_ACCESS_AGREEMENT  = 'site-access-agreement';
    // Adding new credit card
    case CREDIT_CARD_CONSENT    = 'credit-card-consent';

    public function toDisplayString(): string
    {
        return match ($this) {
            self::LEAD_BUYING_AGREEMENT => 'Lead Buying Agreement',
            self::TERMS_AND_CONDITIONS  => 'Terms and Conditions',
            self::SITE_ACCESS_AGREEMENT => 'Site Access Agreement',
            self::CREDIT_CARD_CONSENT   => 'Credit Card Consent',
        };
    }
}
