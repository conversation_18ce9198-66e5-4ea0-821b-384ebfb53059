<?php

namespace App\Enums;

enum AffiliateKey: string
{
    case FIRST_NAME = 'first_name';
    case LAST_NAME = 'last_name';
    case EMAIL = 'email';
    case PHONE = 'phone';
    case INDUSTRY = 'industry';
    case CREATION_DATE = 'creation_date';
    case ORIGIN_URL = 'origin_url';
    case TRUSTED_FORM_URL = 'trusted_form_url';
    case COST = 'cost_of_lead';
    case ADDRESS_1 = 'address_1';
    case ADDRESS_2 = 'address_2';
    case CITY = 'city';
    case STATE = 'state';
    case ZIP_CODE = 'zip_code';
    case COUNTRY = 'country';
    case UTILITY = 'utility_name';
    case ELECTRIC_BILL = 'electric_bill';
    case PLATFORM_CAMPAIGN = 'platform_campaign';
    case UNIVERSAL_LEAD_ID = 'universal_lead_id';
    case IP_ADDRESS = 'ip_address';
    case ROOF_TYPE = 'roof_type';
    case OWN_PROPERTY = 'own_property';
    case COMMENTS = 'comments';
    case AFFILIATE_CAMPAIGN_ID = 'affiliate_campaign_id';

    const array DEFAULT_RULES = [
        self::FIRST_NAME->value         => ['required', 'string'],
        self::LAST_NAME->value          => ['required', 'string'],
        self::EMAIL->value              => ['required', 'string'],
        self::PHONE->value              => ['required', 'string'],
        self::INDUSTRY->value           => ['required', 'string'],
        self::CREATION_DATE->value      => ['required', 'string'],
        self::ORIGIN_URL->value         => ['nullable', 'string'],
        self::TRUSTED_FORM_URL->value   => ['required', 'string'],
        self::COST->value               => ['required', 'numeric'],
        self::ADDRESS_1->value          => ['required', 'string'],
        self::ADDRESS_2->value          => ['nullable', 'string'],
        self::CITY->value               => ['required', 'string'],
        self::STATE->value              => ['required', 'string'],
        self::ZIP_CODE->value           => ['required', 'string'],
        self::COUNTRY->value            => ['nullable', 'string'],
        self::UTILITY->value            => ['nullable', 'string'],
        self::ELECTRIC_BILL->value      => ['nullable', 'numeric'],
        self::PLATFORM_CAMPAIGN->value  => ['nullable', 'string'],
        self::UNIVERSAL_LEAD_ID->value  => ['nullable', 'string'],
        self::IP_ADDRESS->value         => ['nullable', 'string'],
        self::ROOF_TYPE->value          => ['nullable', 'string'],
        self::OWN_PROPERTY->value       => ['nullable', 'string'],
        self::COMMENTS->value           => ['nullable', 'string'],
        self::AFFILIATE_CAMPAIGN_ID->value => ['nullable', 'numeric'],
    ];

    /**
     * By default, affiliate key mappings are the same as the keys
     * @return array
     */
    public static function getDefaultKeyMap(): array
    {
        $map = [];
        foreach (self::cases() as $case) {
            $map[$case->value] = $case->value;
        }
        return $map;
    }
}
