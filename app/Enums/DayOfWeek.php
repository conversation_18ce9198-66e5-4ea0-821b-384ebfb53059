<?php

namespace App\Enums;

use Illuminate\Support\Str;

enum DayOfWeek: int
{
    case SUNDAY = 0;
    case MONDAY = 1;
    case TUESDAY = 2;
    case WEDNESDAY = 3;
    case THURSDAY = 4;
    case FRIDAY = 5;
    case SATURDAY = 6;

    static function fromPlural(string $plural): DayOfWeek
    {
        return self::from(array_flip(self::plurals())[strtolower($plural)]);
    }

    static function fromMin(string $min): DayOfWeek
    {
        return match (strtolower($min)) {
            'sun' => DayOfWeek::SUNDAY,
            'tue' => DayOfWeek::TUESDAY,
            'wed' => DayOfWeek::WEDNESDAY,
            'thu' => DayOfWeek::THURSDAY,
            'fri' => DayOfWeek::FRIDAY,
            'sat' => DayOfWeek::SATURDAY,
            default => DayOfWeek::MONDAY,
        };
    }

    static function plurals(): array
    {
        return [
            DayOfWeek::SUNDAY->value => "sundays",
            DayOfWeek::MONDAY->value => "mondays",
            DayOfWeek::TUESDAY->value => "tuesdays",
            DayOfWeek::WEDNESDAY->value => "wednesdays",
            DayOfWeek::THURSDAY->value => "thursdays",
            DayOfWeek::FRIDAY->value => "fridays",
            DayOfWeek::SATURDAY->value => "saturdays"
        ];
    }

    /**
     * @return string
     */
    public function displayName(): string
    {
        return Str::title($this->name);
    }

    public function toPlural(): string
    {
        return self::plurals()[$this->value];
    }
}
