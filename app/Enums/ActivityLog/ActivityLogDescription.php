<?php

namespace App\Enums\ActivityLog;

use Illuminate\Support\Str;

enum ActivityLogDescription: string
{
    case COMPANY_REGISTERED = 'company_registered';
    case USER_UPDATED       = 'user_updated';
    case SYSTEM_UPDATED     = 'system_updated';
    case USER_REQUESTED     = 'user_requested';

    public function getTitle(): string
    {
        return match ($this) {
            self::COMPANY_REGISTERED => 'Company Registered',
            self::USER_UPDATED       => 'User Updated',
            self::SYSTEM_UPDATED     => 'System Updated',
            self::USER_REQUESTED     => 'User Requested',
            default                  => Str::headline($this->value),
        };

    }

}
