<?php

namespace App\Enums\ActivityLog;

use App\Services\ActivityLogService;
use Closure;

enum ActivityLogName: string
{
    case COMPANY_ACCOUNT_MANAGER_CHANGE      = 'company_account_manager_change';
    case COMPANY_RELATIONSHIP_MANAGER_CHANGE = 'company_relationship_manager_change';
    case COMPANY_SUCCESS_MANAGER_CHANGE      = 'company_success_manager_change';
    case AFFILIATE_SHADOW                    = 'affiliate_shadow';
    case AFFILIATE_PAYOUT_STRATEGY_UPDATE    = 'affiliate_payout_strategy_update';
    case COMPANY_ROLE_UNASSIGNED             = 'company_role_unassigned';
    case COMPANY_ROLE_ASSIGNED               = 'company_role_assigned';
    case COMPANY_ROLE_UPDATED                = 'company_role_updated';
    case COMPANY_DELETION_QUEUED             = 'company_deletion_queued';
    case COMPANY_PROFILE_UPDATED             = 'company_profile_updated';
    case CAMPAIGN_STATE_BIDS_UPDATED         = 'campaign_state_bid_price';
    case CAMPAIGN_COUNTY_BIDS_UPDATED        = 'campaign_county_bid_price';

    /**
     * @return Closure|null
     */
    public function getBatchTransform(): Closure|null
    {
        $service = app(ActivityLogService::class);

        return match($this) {
            self::CAMPAIGN_STATE_BIDS_UPDATED,
            self::CAMPAIGN_COUNTY_BIDS_UPDATED
                    => $service->transformBidLogBatch(...),
            default => null,
        };
    }
}
