<?php

namespace App\Enums;

use UnitEnum;

enum Cadence: string
{
    case ONGOING      = 'ongoing';
    case COMPLETED    = 'completed';
    case NOT_ASSIGNED = 'not-assigned';
    case NEVER_ASSIGNED = 'never-assigned';

    /**
     * @return string
     */
    public function toDisplayString(): string
    {
        return match ($this) {
            self::ONGOING => 'Ongoing',
            self::COMPLETED => 'Completed',
            self::NOT_ASSIGNED => 'Not Assigned',
            self::NEVER_ASSIGNED => 'Never Assigned',
        };
    }

    /**
     * @return array
     */
    public static function getAsKeyValueSelectArray(): array
    {
        $result = [];

        /**
         * @var UnitEnum $case
         */
        foreach (self::cases() as $case) {
            $result[$case->toDisplayString()] = $case->value;
        }

        return $result;
    }
}
