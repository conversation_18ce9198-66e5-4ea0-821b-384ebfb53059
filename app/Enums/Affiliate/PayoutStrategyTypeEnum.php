<?php

namespace App\Enums\Affiliate;

use App\Affiliates\PayoutStrategyType;
use App\Affiliates\RawLeadFlatRatePayoutStrategyType;
use App\Affiliates\RevenuePercentagePayoutStrategyType;
use App\Affiliates\SoldLeadFlatRatePayoutStrategyType;
use Exception;

enum PayoutStrategyTypeEnum: string
{
    case REVENUE_PERCENTAGE   = 'revenue_percentage';
    case RAW_LEAD_FLAT_VALUE  = 'raw_lead_flat_value';
    case SOLD_LEAD_FLAT_VALUE = 'sold_lead_flat_value';

    /**
     * @throws string
     */
    public function getClass(): PayoutStrategyType
    {
        return match ($this) {
            self::RAW_LEAD_FLAT_VALUE  => new RawLeadFlatRatePayoutStrategyType(),
            self::REVENUE_PERCENTAGE   => new RevenuePercentagePayoutStrategyType(),
            self::SOLD_LEAD_FLAT_VALUE => new SoldLeadFlatRatePayoutStrategyType(),
            default                    => throw new Exception("Unknown Payment Strategy")
        };
    }
}
