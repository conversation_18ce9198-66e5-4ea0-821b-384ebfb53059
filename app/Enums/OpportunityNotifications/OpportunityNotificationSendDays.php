<?php

namespace App\Enums\OpportunityNotifications;

enum OpportunityNotificationSendDays: int {

    case SUNDAY = 0;
    case MONDAY = 1;
    case TUESDAY = 2;
    case WEDNESDAY = 3;
    case THURSDAY = 4;
    case FRIDAY = 5;
    case SATURDAY = 6;

    /**
     * @return string
     */
    public function getSendDayString(): string
    {
        return match ($this) {
            self::SUNDAY        => 'Sunday',
            self::MONDAY        => 'Monday',
            self::TUESDAY       => 'Tuesday',
            self::WEDNESDAY     => 'Wednesday',
            self::THURSDAY      => 'Thursday',
            self::FRIDAY        => 'Friday',
            self::SATURDAY      => 'Saturday',
        };
    }

    /**
     * @param string $display
     * @return OpportunityNotificationSendDays
     */
    public static function fromDisplayString(string $display): OpportunityNotificationSendDays
    {
        return match ($display) {
            'Sunday' => self::SUNDAY,
            'Monday' => self::MONDAY,
            'Tuesday' => self::TUESDAY,
            'Wednesday' => self::WEDNESDAY,
            'Thursday' => self::THURSDAY,
            'Friday' => self::FRIDAY,
            'Saturday' => self::SATURDAY,
            default => throw new \RuntimeException("{$display} is not a valid day")
        };
    }

    /**
     * @param string $display
     * @return OpportunityNotificationSendDays|null
     */
    public static function tryFromDisplayString(string $display): ?OpportunityNotificationSendDays
    {
        try {
            return OpportunityNotificationSendDays::fromDisplayString($display);
        } catch(\RuntimeException $e) {
            return null;
        }
    }

}
