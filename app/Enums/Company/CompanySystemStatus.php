<?php

namespace App\Enums\Company;

enum CompanySystemStatus: int
{
    case ELIGIBLE = 1;
    case SUSPENDED_PAYMENT = 2;
    case SUSPENDED_PAYMENT_METHOD = 3;
    case SUSPENDED_CRM_REJECTION_THRESHOLD = 4;

    /**
     * @return string
     */
    public function label(): string
    {
        return match ($this) {
            self::ELIGIBLE => 'Good Standing',
            self::SUSPENDED_PAYMENT => 'Suspended (Non payment)',
            self::SUSPENDED_PAYMENT_METHOD => 'Suspended (No valid payment method)',
            self::SUSPENDED_CRM_REJECTION_THRESHOLD => 'Suspended (CRM rejection threshold exceeded)',
        };
    }

    /**
     * @return array
     */
    public static function getAsKeyValueSelectArray(): array
    {
        return collect(self::cases())->mapWithKeys(fn(self $case) => [$case->label() => $case->value])->sortKeys()->toArray();
    }
}
