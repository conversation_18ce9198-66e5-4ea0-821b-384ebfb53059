<?php

namespace App\Enums\Company;

enum CompanyAdminStatus: int
{
    case ADMIN_OVERRIDE = 1;
    case ADMIN_APPROVED = 2;
    case COLLECTIONS = 3;
    case ARCHIVED = 4;
    case ADMIN_LOCKED = 5;
    case PROFILE_ONLY = 6;

    /**
     * @return string
     */
    public function label(): string
    {
        return match ($this) {
            self::ADMIN_OVERRIDE => 'Forced Lead Eligibility',
            self::ADMIN_APPROVED => 'Admin Approved',
            self::COLLECTIONS => 'Collections',
            self::ARCHIVED => 'Archived',
            self::ADMIN_LOCKED => 'Admin Locked',
            self::PROFILE_ONLY => 'Profile Only'
        };
    }

    /**
     * @return array
     */
    public static function getAsKeyValueSelectArray(): array
    {
        return collect(self::cases())->mapWithKeys(fn(self $case) => [$case->label() => $case->value])->sortKeys()->toArray();
    }
}
