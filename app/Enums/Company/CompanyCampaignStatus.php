<?php

namespace App\Enums\Company;

enum CompanyCampaignStatus: int
{
    case NO_CAMPAIGNS = 0;
    case CAMPAIGNS_ACTIVE = 1;
    case CAMPAIGNS_PAUSED = 2;
    case CAMPAIGNS_OFF = 3;
    case CAMPAIGNS_OFF_NEVER_PURCHASED = 4;
    /**
     * @return string
     */
    public function label(): string
    {
        return match ($this) {
            self::NO_CAMPAIGNS => 'No Campaigns',
            self::CAMPAIGNS_ACTIVE => 'Campaigns Active',
            self::CAMPAIGNS_PAUSED => 'Campaigns Paused',
            self::CAMPAIGNS_OFF => 'Campaigns Off',
            self::CAMPAIGNS_OFF_NEVER_PURCHASED => 'Campaigns Off (Never Purchased)'
        };
    }

    /**
     * @return array
     */
    public static function getAsKeyValueSelectArray(): array
    {
        return collect(self::cases())->mapWithKeys(fn(self $case) => [$case->label() => $case->value])->sortKeys()->toArray();
    }
}
