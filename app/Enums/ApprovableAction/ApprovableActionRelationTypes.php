<?php

namespace App\Enums\ApprovableAction;

use App\Models\Billing\BillingProfile;
use App\Models\Billing\Invoice;
use App\Models\Odin\Company;

enum ApprovableActionRelationTypes: string
{
    case INVOICES         = 'invoices';
    case COMPANY          = 'company';
    case BILLING_PROFILES = 'billing_profiles';

    public function getModelClass(): string
    {
        return match ($this) {
            self::INVOICES         => Invoice::class,
            self::COMPANY          => Company::class,
            self::BILLING_PROFILES => BillingProfile::class,
            default                => $this->value,
        };
    }

}
