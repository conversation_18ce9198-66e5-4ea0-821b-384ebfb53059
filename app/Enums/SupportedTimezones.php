<?php

namespace App\Enums;

use Carbon\Carbon;
use UnitEnum;

enum SupportedTimezones: string
{
    case EASTERN = "-05:00";
    case CENTRAL = "-06:00";
    case MOUNTAIN = "-07:00";
    case PACIFIC = "-08:00";
    case ALASKA = "-09:00";
    case HAWAII = "-10:00";

    public function toNativeTimezone(): \DateTimeZone
    {
        return new \DateTimeZone($this->value);
    }

    public function getLabel(): string
    {
        return match ($this) {
            self::EASTERN => "Eastern",
            self::CENTRAL => "Central",
            self::MOUNTAIN => "Mountain",
            self::PACIFIC => "Pacific",
            self::ALASKA => "Alaska",
            self::HAWAII => "Hawaii",
        };
    }

    /**
     * Returns the respective timezone from a given label.
     *
     * @param string $label
     * @return SupportedTimezones
     */
    public static function fromLabel(string $label): SupportedTimezones
    {
        return match(strtolower(trim($label))) {
            "eastern" => SupportedTimezones::EASTERN,
            "central" => SupportedTimezones::CENTRAL,
            "mountain" => SupportedTimezones::MOUNTAIN,
            "pacific" => SupportedTimezones::PACIFIC,
            "alaska" => SupportedTimezones::ALASKA,
            "hawaii" => SupportedTimezones::HAWAII,
            default => SupportedTimezones::EASTERN
        };
    }

    /**
     * To support potential future cases of converting lead_processing_time_zone_configurations utc offset to this
     * enum, this method can be used.
     *
     * @param int $hour
     * @return SupportedTimezones
     */
    public static function fromOffsetHour(int $hour): SupportedTimezones
    {
        return match ($hour) {
            -5 => SupportedTimezones::EASTERN,
            -6 => SupportedTimezones::CENTRAL,
            -7 => SupportedTimezones::MOUNTAIN,
            -8 => SupportedTimezones::PACIFIC,
            -9 => SupportedTimezones::ALASKA,
            -10 => SupportedTimezones::HAWAII,
            default => $hour > -5 ? SupportedTimezones::EASTERN : SupportedTimezones::HAWAII
        };
    }

    /**
     * To support potential future cases of converting this enum to the lead_processing_time_zone_configurations utc
     * offset, this method can be used.
     *
     * @return int
     */
    public function toOffsetHour(): int
    {
        return match ($this) {
            self::EASTERN => -5,
            self::CENTRAL => -6,
            self::MOUNTAIN => -7,
            self::PACIFIC => -8,
            self::ALASKA => -9,
            self::HAWAII => -10,
        };
    }

    /**
     * @return array
     */
    public static function getAsKeyValueSelectArray(): array
    {
        $result = [];

        /**
         * @var UnitEnum $value
         */
        foreach (self::cases() as $case) {
            $result[$case->getLabel() . " ({$case->toOffsetHour()} UTC)"] = $case->toOffsetHour();
        }

        return $result;
    }

}
