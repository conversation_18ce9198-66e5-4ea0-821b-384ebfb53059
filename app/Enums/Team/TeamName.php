<?php

namespace App\Enums\Team;

enum TeamName: string
{
    case US_BDM = 'us_bdm';
    case AUS_BDM = 'aus_bdm';
    case SDR = 'sdr';
    case OM = 'om';
    case AM = 'am';
    case SALES_DIRECTOR = 'sales_director';

    public function label(): string
    {
        return match ($this) {
            self::US_BDM => 'US BDM Team',
            self::AUS_BDM => 'AUS BDM Team',
            self::SDR => 'SDR Team',
            self::OM => 'OM Team',
            self::AM => 'AM Team',
            self::SALES_DIRECTOR => 'Sales Director',
        };
    }

    public function defaultDescription(): string
    {
        return match ($this) {
            self::US_BDM => 'US BDM Team',
            self::AUS_BDM => 'AUS BDM Team',
            self::SDR => 'SDR Team',
            self::OM => 'OM Team',
            self::AM => 'AM Team',
            self::SALES_DIRECTOR => 'Sales Director',
        };
    }
}
