<?php

namespace App\Enums\ContactIdentification;

use App\Models\Mailbox\MailboxEmail;
use App\Models\Mailbox\MailboxEmailRecipient;

enum IdentifiableModelType: string
{
    case MAILBOX_EMAIL_RECIPIENTS = 'mailbox_email_recipients';
    case MAILBOX_EMAILS = 'mailbox_emails';

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public function getModelClass(): string
    {
        return match ($this) {
            self::MAILBOX_EMAIL_RECIPIENTS => MailboxEmailRecipient::class,
            self::MAILBOX_EMAILS => MailboxEmail::class
        };
    }

    public static function fromModelClass(string $modelClass): self
    {
        return match ($modelClass) {
            MailboxEmailRecipient::class => self::MAILBOX_EMAIL_RECIPIENTS,
            MailboxEmail::class => self::MAILBOX_EMAILS,
        };
    }
}
