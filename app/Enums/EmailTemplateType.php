<?php

namespace App\Enums;

use App\Services\Prospects\ProspectEmailService;
use App\Services\Shortcode\ShortcodeImplementation\PersonalEmailTemplateShortcodeUseCase;
use App\Services\Shortcode\ShortcodeImplementation\ProspectShortcodeUseCase;
use App\Services\Shortcode\ShortcodeImplementation\ShortcodeUseCase;
use Illuminate\Contracts\Container\BindingResolutionException;
use UnitEnum;

enum EmailTemplateType: int
{
    case STATUS_OTHER                   = 0;
    case STATUS_PERSONAL                = 1;
    case STATUS_LEAD_DELIVERY           = 2;
    case STATUS_MISSED_PRODUCTS_BDM     = 3;
    case STATUS_CALCULATOR_RESULTS      = 4;
    case STATUS_EMAIL_MARKETING         = 5;
    case STATUS_MISSED_PRODUCTS_SUMMARY = 6;
    case BDM_PROSPECTING_EMAIL          = 7;
    case BILLING                        = 8;

    const string STRING_OTHER                   = 'Other';
    const string STRING_PERSONAL                = 'Outreach Cadence - Personal';
    const string STRING_LEAD_DELIVERY           = 'Lead Delivery';
    const string STRING_MISSED_PRODUCTS_NEW     = 'Missed Products - BDM Promo';
    const string STRING_MISSED_PRODUCTS_SUMMARY = 'Missed Products - Campaign Summary';
    const string STRING_CALCULATOR_RESULTS      = 'Calculator Result';
    const string STRING_EMAIL_MARKETING         = 'Email Marketing';
    const string STRING_BDM_PROSPECTING_EMAIL   = 'BDM Prospecting Email';
    const string STRING_BILLING                 = 'Billing';

    public static function label(EmailTemplateType|int|null $emailTemplateType): string
    {
        $value = $emailTemplateType instanceof EmailTemplateType ? $emailTemplateType->value : $emailTemplateType;

        return match ($value) {
            self::STATUS_OTHER->value                   => self::STRING_OTHER,
            self::STATUS_PERSONAL->value                => self::STRING_PERSONAL,
            self::STATUS_LEAD_DELIVERY->value           => self::STRING_LEAD_DELIVERY,
            self::STATUS_MISSED_PRODUCTS_BDM->value     => self::STRING_MISSED_PRODUCTS_NEW,
            self::STATUS_MISSED_PRODUCTS_SUMMARY->value => self::STRING_MISSED_PRODUCTS_SUMMARY,
            self::STATUS_CALCULATOR_RESULTS->value      => self::STRING_CALCULATOR_RESULTS,
            self::STATUS_EMAIL_MARKETING->value         => self::STRING_EMAIL_MARKETING,
            self::BDM_PROSPECTING_EMAIL->value          => self::STRING_BDM_PROSPECTING_EMAIL,
            self::BILLING->value                        => self::STRING_BILLING,
            default                                     => 'Unknown'
        };
    }

    public static function allTypes(): array
    {
        return array_column(self::cases(), 'value');
    }

    public static function asSelectArray(): array
    {
        $result = [];

        foreach (self::cases() as $case) {
            $result[] = [
                'id'   => $case->value,
                'name' => self::label($case->value)
            ];
        }

        return $result;
    }

    public static function getAsNameKeyPair(): array
    {
        $result = [];
        foreach (self::cases() as $case) {
            $result[self::label($case)] = $case->value;
        }
        return $result;
    }

    public function getDefaultEmailTemplateMarkdown(): string
    {
        return match ($this) {
            //todo get default markdown for personal emails
            self::BDM_PROSPECTING_EMAIL          => (new ProspectEmailService)->getBDMMarkdownEmailContent(),
            default                              => ''
        };
    }

    public function getDefaultEmailTemplateSubject(): string
    {
        return match ($this) {
            self::BDM_PROSPECTING_EMAIL          => 'Are you interested in {prospect.industry} leads',
            default                              => ''
        };
    }

    public function getEmailTemplateShortcodeService(): array
    {
        return match ($this) {
            self::BDM_PROSPECTING_EMAIL          => ProspectShortcodeUseCase::getShortcodesList(),
            self::STATUS_PERSONAL                => PersonalEmailTemplateShortcodeUseCase::getShortcodesList(),
            default                              => []
        };
    }
}
