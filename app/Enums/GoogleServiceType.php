<?php

namespace App\Enums;

use App\Services\Calendar\Google\GoogleCalendarProvider;
use App\Services\Calendar\Google\GoogleDriveProvider;
use App\Services\Calendar\Google\GoogleMeetProvider;
use App\Services\Calendar\Google\GoogleProvider;

enum GoogleServiceType: string
{
    case CALENDAR = 'calendar';
    case MEET     = 'meet';
    case DRIVE    = 'drive';

    public function getProvider(): GoogleProvider
    {
        return match ($this) {
            self::CALENDAR => new GoogleCalendarProvider(),
            self::MEET     => new GoogleMeetProvider(),
            self::DRIVE     => new GoogleDriveProvider(),
            default        => throw new \Exception("$this->value provider not implement yet")
        };
    }
}
