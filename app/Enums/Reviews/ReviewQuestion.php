<?php

namespace App\Enums\Reviews;

use Illuminate\Support\Str;

/**
 * This handles Solar review question logic until reviews phase 2 is revisited/released
 */
enum ReviewQuestion: string
{
    case REVIEW_TYPE                    = 'review_type';
    case PRODUCT_TYPE                   = 'product_type';
    case SYSTEM_SIZE                    = 'system_size';
    case SYSTEM_PRICE                   = 'system_price';
    case SYSTEM_TYPE                    = 'system_type';
    case INSTALLATION_TYPE              = 'installation_type';
    case YEAR_INSTALLED                 = 'installed_year';
    case RATING_SALES_PROCESS           = 'rating_sales_process';
    case RATING_QUALITY_OF_ADVICE       = 'rating_quality_of_advise';
    case RATING_ON_SCHEDULE             = 'rating_on_schedule';
    case RATING_PRICE_CHARGED           = 'rating_price_charged';
    case RATING_QUALITY_OF_PRODUCTS     = 'rating_quality_of_products';
    case RATING_QUALITY_OF_INSTALLATION = 'rating_quality_of_installation';
    case RATING_PROFESSIONALISM         = 'rating_professionalism';
    case RECOMMEND_INSTALLER            = 'recommend_installer';
    case RATING_AFTER_SALES_SUPPORT     = 'rating_after_sales_support';
    case PRICE_AFTER_INCENTIVES         = 'price_after_incentives';
    case MODEL_REFERENCE                = 'model_reference';
    case INVERTER_BRAND_ID              = 'inverter_brand_id';
    case PANEL_BRAND_ID                 = 'panel_brand_id';
    case COMPANY_INSTALLED_ID           = 'company_installed_id';
    case INVERTER_BRAND_REFERENCE       = 'inverter_brand_reference';
    case PANEL_BRAND_REFERENCE          = 'panel_brand_reference';
    case COMPANY_INSTALLED_REFERENCE    = 'company_installed_reference';

    public function getDataType(): ReviewQuestionDataType
    {
        return match ($this) {
            self::RATING_AFTER_SALES_SUPPORT,
            self::RATING_ON_SCHEDULE,
            self::RATING_PRICE_CHARGED,
            self::RATING_QUALITY_OF_INSTALLATION,
            self::RATING_QUALITY_OF_ADVICE,
            self::RATING_QUALITY_OF_PRODUCTS,
            self::RATING_PROFESSIONALISM,
            self::RATING_SALES_PROCESS,
                => ReviewQuestionDataType::RATING,
            self::SYSTEM_SIZE
                => ReviewQuestionDataType::FLOAT,
            self::PRICE_AFTER_INCENTIVES,
            self::RECOMMEND_INSTALLER,
                => ReviewQuestionDataType::BOOL,
            self::YEAR_INSTALLED,
            self::SYSTEM_PRICE,
                => ReviewQuestionDataType::INT,
            self::COMPANY_INSTALLED_ID,
            self::PANEL_BRAND_ID,
            self::INVERTER_BRAND_ID,
                => ReviewQuestionDataType::COMPANY_ID,
            self::PANEL_BRAND_REFERENCE,
            self::COMPANY_INSTALLED_REFERENCE,
            self::INVERTER_BRAND_REFERENCE
                => ReviewQuestionDataType::COMPANY_REFERENCE,
            default
                => ReviewQuestionDataType::STRING,
        };
    }

    /**
     * @return string
     */
    public function getTitle(): string
    {
        if ($this->getDataType() === ReviewQuestionDataType::RATING)
            return $this->getRatingTitle();

        return match($this) {
            self::SYSTEM_SIZE            => 'System Size (kW)',
            self::PRICE_AFTER_INCENTIVES => 'Price includes FTC/incentives',
            self::COMPANY_INSTALLED_ID   => 'Installed by Company',
            self::INVERTER_BRAND_ID      => 'Inverter Brand',
            self::PANEL_BRAND_ID         => 'Panel Brand',
            default                      => Str::headline($this->value),
        };
    }

    /**
     * @return int|null
     */
    public function getOrder(): ?int
    {
        return match($this->getDataType()) {
            ReviewQuestionDataType::RATING => match ($this) {
                self::RATING_SALES_PROCESS           => 1,
                self::RATING_PRICE_CHARGED           => 2,
                self::RATING_ON_SCHEDULE             => 3,
                self::RATING_QUALITY_OF_INSTALLATION => 4,
                self::RATING_AFTER_SALES_SUPPORT     => 5,
                default                              => 99,
            },
            default                        => match ($this) {
                self::SYSTEM_SIZE            => 1,
                self::SYSTEM_PRICE           => 2,
                self::PRICE_AFTER_INCENTIVES => 3,
                self::YEAR_INSTALLED         => 4,
                default                      => 99,
            },
        };
    }

    /**
     * @return bool
     */
    public function showToCompany(): bool
    {
        return match ($this) {
            self::REVIEW_TYPE => false,
            default           => true,
        };
    }

    /**
     * @param string $key
     * @return string
     */
    public static function getStorageKeyMapping(string $key): string
    {
        return match($key) {
            self::COMPANY_INSTALLED_REFERENCE->value => self::COMPANY_INSTALLED_ID->value,
            self::PANEL_BRAND_REFERENCE->value       => self::PANEL_BRAND_ID->value,
            self::INVERTER_BRAND_REFERENCE->value    => self::INVERTER_BRAND_ID->value,
            default                                  => $key
        };
    }

    /**
     * @return ReviewQuestionDataType
     */
    public function getStorageTypeMapping(): ReviewQuestionDataType
    {
        $type = $this->getDataType();
        return match($type) {
            ReviewQuestionDataType::COMPANY_REFERENCE => ReviewQuestionDataType::COMPANY_ID,
            default                                   => $type,
        };
    }

    /**
     * @return string
     */
    private function getRatingTitle(): string
    {
        return match($this) {
            self::RATING_PRICE_CHARGED => 'Price Charged As Quoted',
            default                    => Str::headline(preg_replace("/^rating_/i", '', $this->value)),
        };
    }


}