<?php

namespace App\Enums;

enum LeadRefundItemStatus: string
{
    case PENDING                 = 'pending_review';
    case APPROVED                = 'approved';
    case MORE_INFORMATION_NEEDED = 'more_information_needed';
    case REJECTED                = 'rejected';

    public function getName(): string
    {
        return match ($this) {
            self::PENDING                 => 'Pending Review',
            self::APPROVED                => 'Approved',
            self::MORE_INFORMATION_NEEDED => 'More Information Needed',
            self::REJECTED                => 'Rejected',
            default                       => $this->value
        };
    }
}
