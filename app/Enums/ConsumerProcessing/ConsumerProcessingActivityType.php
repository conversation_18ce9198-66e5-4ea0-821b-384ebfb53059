<?php

namespace App\Enums\ConsumerProcessing;

use App\Enums\ActivityType;

/**
 * Pre-allocation consumer activity
 */
enum ConsumerProcessingActivityType: string
{
    //Call and Text models already have a morph mapping, need to use these for the DB
    case CALL           = ActivityType::CALL->value;
    case TEXT           = ActivityType::TEXT->value;
    case APPROVED       = 'approved';
    case UNDER_REVIEW   = 'under_review';
    case PENDING_REVIEW = 'pending_review';
    case CANCELLED      = 'cancelled';
    case USER_COMMENT   = 'comment';
    case DNC_REQUEST    = 'dnc_request';

    case CUSTOM         = 'custom';

    /**
     * @param string|null $detail
     * @return string
     */
    public function getCommentTitle(?string $detail = null): string
    {
        $base = match($this) {
            self::CALL           => 'Called By',
            self::TEXT           => 'Texted By',
            self::APPROVED       => 'Approved',
            self::UNDER_REVIEW   => 'Under Review',
            self::PENDING_REVIEW => 'Pending Review',
            self::CANCELLED      => 'Cancelled',
            self::USER_COMMENT   => 'User Comment',
            self::DNC_REQUEST    => 'DNC Request: System Generated',
            default              => 'Processing Activity "' . $this->name,
        };

        return $detail
            ? "$base: $detail"
            : $base;
    }

    /**
     * @return bool
     */
    public function hasRelatedActivity(): bool
    {
        return match($this) {
            self::CALL, self::TEXT => true,
            default                => false,
        };
    }
}