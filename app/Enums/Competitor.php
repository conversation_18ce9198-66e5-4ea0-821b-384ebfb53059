<?php

namespace App\Enums;

use App\EnumAttributes\Concerns\GetAttributesTrait;
use App\EnumAttributes\Description;

enum Competitor: string
{
    use GetAttributesTrait;

    #[Description('Energy Sage')]
    case ENERGY_SAGE = 'is_energysage_customer';

    #[Description('Angi')]
    case ANGI = 'is_angi_customer';

    #[Description('Modernize')]
    case MODERNIZE = 'is_modernize_customer';

    #[Description('Home Advisor')]
    case HOME_ADVISOR = 'is_homeadvisor_customer';
}
