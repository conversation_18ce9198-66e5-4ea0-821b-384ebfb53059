<?php

namespace App\Enums;

enum EventCategory: string
{
    case CAMPAIGNS  = 'campaigns';
    case CONTACTS   = 'contacts';
    case DELIVERIES = 'deliveries';
    case PAYMENTS   = 'payments';
    case COMPANIES  = 'companies';
    case REVIEWS    = 'reviews';
    case LEADS      = 'leads';
    case INTERNAL   = 'internal';
    case ADMIN2     = 'admin2';
    case ODIN_V2    = 'odin-v2';
    case TEST_LEADS = 'test-leads';
    case MAILBOX    = 'mailbox';
    case BILLING    = 'billing';

    case GENERIC = 'generic';

    case STAGING_TESTING = 'staging_testing';

    /**
     * Returns a list of all event categories.
     *
     * @return array
     */
    public static function allCategories(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Returns a list of workflow related categories only.
     *
     * @return array
     */
    public static function workFlowCategories(): array
    {
        $workFlowExcludedCategories = [self::ADMIN2->value];

        return array_filter(self::allCategories(), fn($category) => !in_array($category, $workFlowExcludedCategories));
    }
}
