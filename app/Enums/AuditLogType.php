<?php

namespace App\Enums;

use InvalidArgumentException;

enum AuditLogType: string
{
    case CONTRACT_CREATED                   = 'Contract created';
    case CONTRACT_VIEWED                    = 'Contract viewed';
    case CONTRACT_SIGNED                    = 'Contract signed';
    case CONTRACT_SIGNING_REQUEST_SENT      = 'Contract request sent';
    case CONTRACT_SIGNING_REQUEST_VIEWED    = 'Contract request viewed';

    public function getDescription(array $data): string
    {
        try{
            return match ($this) {
                self::CONTRACT_CREATED                  => "Contract created by {$data['username']} for contract UUID {$data['contract_uuid']}",
                self::CONTRACT_VIEWED                   => "Contract viewed by {$data['username']} for contract UUID {$data['contract_uuid']}",
                self::CONTRACT_SIGNED                   => "Contract signed by {$data['username']} for contract UUID {$data['contract_uuid']}",
                self::CONTRACT_SIGNING_REQUEST_SENT     => "Contract request sent to {$data['username']} for contract UUID {$data['contract_uuid']}",
                self::CONTRACT_SIGNING_REQUEST_VIEWED   => "Contract request viewed to {$data['username']} for contract UUID {$data['contract_uuid']}",
                default                                 => throw new InvalidArgumentException("Unknown audit log event type: {$this->value}"),
            };
        } catch (\Throwable $e) {
            logger()->error('Failed to generate audit log description: ' . $e->getMessage());
            return '';
        }

    }
}
