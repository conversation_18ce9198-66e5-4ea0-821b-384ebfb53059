<?php

namespace App\Enums;

enum LeadRefundStatus: string
{
    case PENDING_REVIEW           = 'pending_review';
    case APPROVED                 = 'approved';
    case APPROVED_WITH_REJECTIONS = 'approved_with_rejections';
    case MORE_INFORMATION_NEEDED  = 'more_information_needed';
    case REJECTED                 = 'rejected';
    case REFUND_ISSUED            = 'refund_issued';
    case REFUNDED                 = 'refunded';

    public function getName(): string
    {
        return match ($this) {
            self::PENDING_REVIEW           => 'Pending Review',
            self::APPROVED                 => 'Approved',
            self::REJECTED                 => 'Rejected',
            self::APPROVED_WITH_REJECTIONS => 'Approved With Rejections',
            self::MORE_INFORMATION_NEEDED  => 'More info requested',
            self::REFUND_ISSUED            => 'Refund Issued',
            self::REFUNDED                 => 'Refunded',
            default                        => $this->value
        };
    }
}
