<?php

namespace App\Enums;

enum ReviewStatus : int
{
    case DECLINED = -2;
    case ARCHIVED = -1;
    case INITIAL = 0;
    case APPROVED = 1;

    public function toDisplayName(): string
    {
        return match($this) {
            self::DECLINED => "Declined",
            self::ARCHIVED => "Archived",
            self::INITIAL => "Initial",
            self::APPROVED => "Approved"
        };
    }
}
