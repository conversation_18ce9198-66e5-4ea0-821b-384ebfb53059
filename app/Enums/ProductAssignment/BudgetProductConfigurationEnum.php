<?php

namespace App\Enums\ProductAssignment;

use App\Enums\Odin\BudgetCategory;
use App\Enums\Odin\Product;
use App\Enums\Odin\QualityTier;
use App\Enums\Odin\SaleTypes;
use Exception;
use Illuminate\Support\Str;

/**
 * Defines configurations which tell us what types of products can be accepted by a specific budget
 */
enum BudgetProductConfigurationEnum: int
{
    case LEAD_VERIFIED   = 0;
    case LEAD_UNVERIFIED = 1;
    case LEAD_EMAIL_ONLY = 2;

    case APPOINTMENT_IN_HOME = 3;
    case APPOINTMENT_ONLINE  = 4;

    case LEAD_EXCLUSIVE_ONLY  = 5;

    /**
     * @param Product $product
     * @return array
     * @throws Exception
     */
    public static function getProductConfigWithCategory(Product $product): array
    {
        if($product === Product::LEAD) {
            return [
                self::LEAD_VERIFIED->value => BudgetCategory::VERIFIED,
                self::LEAD_UNVERIFIED->value => BudgetCategory::UNVERIFIED,
                self::LEAD_EMAIL_ONLY->value => BudgetCategory::EMAIL_ONLY
            ];
        }
        else if($product === Product::APPOINTMENT) {
            return [
                self::APPOINTMENT_ONLINE->value => BudgetCategory::VERIFIED,
                self::APPOINTMENT_IN_HOME->value => BudgetCategory::VERIFIED
            ];
        }
        else {
            throw new Exception("Invalid product");
        }
    }

    /**
     * @return string
     */
    public function getDisplayName(): string
    {
        return Str::title(str_replace('_', ' ', $this->name));
    }

    /**
     * @return string
     */
    public function getSlug(): string
    {
        return strtolower($this->name);
    }

    /**
     * @return string
     */
    public function getKey(): string
    {
        return match($this) {
            self::LEAD_VERIFIED        => 'verified',
            self::LEAD_EMAIL_ONLY      => 'email_only',
            self::LEAD_UNVERIFIED      => 'unverified',
            self::LEAD_EXCLUSIVE_ONLY  => 'exclusive_only',

            self::APPOINTMENT_ONLINE  => 'online',
            self::APPOINTMENT_IN_HOME => 'in_home',
        };
    }

    /**
     * TODO: remove boolean for Premium budgets when properly set up per-industry in back end
     * @param bool|null $includePremium
     * @return array
     */
    public function getConfig(?bool $includePremium = false): array
    {
        return match ($this) {

            self::LEAD_VERIFIED => [
                Product::LEAD->value => $this->getVerifiedLeadTiers($includePremium),
            ],

            self::LEAD_UNVERIFIED => [
                Product::LEAD->value => [
                    QualityTier::STANDARD->value => [
                        SaleTypes::UNVERIFIED->value,
                    ],
                ]
            ],

            self::LEAD_EMAIL_ONLY => [
                Product::LEAD->value => [
                    QualityTier::STANDARD->value => [
                        SaleTypes::EMAIL_ONLY->value,
                    ],
                ]
            ],

            self::LEAD_EXCLUSIVE_ONLY => [
                Product::LEAD->value => $this->getCustomVerifiedLeadTiers($includePremium, [SaleTypes::EXCLUSIVE]),
            ],

            self::APPOINTMENT_IN_HOME => [
                Product::APPOINTMENT->value => [
                    QualityTier::IN_HOME->value => [
                        SaleTypes::EXCLUSIVE->value,
                        SaleTypes::DUO->value,
                    ],
                ]
            ],

            self::APPOINTMENT_ONLINE => [
                Product::APPOINTMENT->value => [
                    QualityTier::ONLINE->value => [
                        SaleTypes::EXCLUSIVE->value,
                        SaleTypes::DUO->value,
                        SaleTypes::TRIO->value,
                    ],
                ]
            ],

        };
    }

    /**
     * @param bool $includePremium
     * @return array|array[]
     */
    protected function getVerifiedLeadTiers(bool $includePremium): array
    {
        $standard = [
            QualityTier::STANDARD->value => [
                SaleTypes::EXCLUSIVE->value,
                SaleTypes::DUO->value,
                SaleTypes::TRIO->value,
                SaleTypes::QUAD->value,
            ],
        ];

        return $includePremium
            ? [...$standard,
                QualityTier::PREMIUM->value => [
                     SaleTypes::EXCLUSIVE->value,
                     SaleTypes::DUO->value,
                     SaleTypes::TRIO->value,
                     SaleTypes::QUAD->value,
                ],
            ]
            : $standard;
    }

    /**
     * @param bool $includePremium
     * @param array $allowSaleTypes
     * @return array|array[]
     */
    protected function getCustomVerifiedLeadTiers(bool $includePremium, array $allowSaleTypes): array
    {
        $standardSaleTypes = [
            QualityTier::STANDARD->value => $allowSaleTypes,
        ];
        return $includePremium
            ? [...$standardSaleTypes,
                QualityTier::PREMIUM->value => [
                    ...$allowSaleTypes,
                ]
           ]
            : $standardSaleTypes;
    }
}
