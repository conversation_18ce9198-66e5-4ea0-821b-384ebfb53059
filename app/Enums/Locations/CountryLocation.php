<?php

namespace App\Enums\Locations;

use App\Models\Locations\AusLocation;
use App\Models\Locations\BaseLocationModel;
use App\Models\Locations\UKLocation;
use App\Models\Locations\USLocation;

enum CountryLocation: string
{
    case US        = 'US';
    case UK        = 'GB';
    case AUSTRALIA = 'AU';

    /**
     * @return BaseLocationModel
     */
    public function model(): BaseLocationModel
    {
        return match ($this) {
            self::AUSTRALIA => new AusLocation(), //example only
            self::UK        => new UKLocation(), //example only
            default         => new USLocation(),
        };
    }

    /**
     * @return array
     */
    public function locationTypeNames(): array
    {
        return LocationType::getNames($this);
    }
}
