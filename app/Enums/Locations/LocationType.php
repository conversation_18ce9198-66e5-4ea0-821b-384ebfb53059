<?php

namespace App\Enums\Locations;

/**
 * Generics for levels of location type, based on Google's geocoding API type tags
 */
enum LocationType: int
{
    case ADMINISTRATIVE_AREA_1 = 1;
    case ADMINISTRATIVE_AREA_2 = 2;
    case LOCALITY              = 3;
    case POSTAL_CODE           = 4;

    /**
     * @param CountryLocation|null $countryLocation
     * @return string[]
     */
    public static function getNames(?CountryLocation $countryLocation): array
    {
        return match($countryLocation) {
            CountryLocation::AUSTRALIA => self::australianNames(),
            CountryLocation::UK        => self::britishNames(),
            default                    => self::defaultNames(),   // Default to US
        };
    }

    private static function australianNames(): array
    {
        return [
            self::ADMINISTRATIVE_AREA_1->value => 'State/Territory',
            self::ADMINISTRATIVE_AREA_2->value => 'Council Area',
            self::LOCALITY->value              => 'City',
            self::POSTAL_CODE->value           => 'Postcode',
        ];
    }

    private static function britishNames(): array
    {
        return [
            self::ADMINISTRATIVE_AREA_1->value => 'Country',
            self::ADMINISTRATIVE_AREA_2->value => 'County',
            self::LOCALITY->value              => 'City',
            self::POSTAL_CODE->value           => 'Postcode',
        ];
    }

    private static function defaultNames(): array
    {
        return [
            self::ADMINISTRATIVE_AREA_1->value => 'State',
            self::ADMINISTRATIVE_AREA_2->value => 'County',
            self::LOCALITY->value              => 'City',
            self::POSTAL_CODE->value           => 'Zip Code',
        ];
    }
}