<?php

namespace App\Enums;

use App\Models\Legacy\EloquentCompany;

enum LegacyCompanyBasicStatus: int
{
    case BUYING_LEADS_STATUS_ACTIVE = 1;
    case LEADS_PAUSED = 2;
    case LEADS_OFF = 3;
    case LEADS_OFF_NEVER_PURCHASED = 4;
    case REGISTERING = 5;
    case PENDING_APPROVAL = 6;
    case PROFILE_ONLY = 7;

    /**
     * @param LegacyCompanyBasicStatus $status
     * @param bool|null $adminApproved
     * @param bool|null $validPaymentMethod
     * @param int|null $activeCampaignsCount
     * @param int|null $reactivateCampaignsInFutureCount
     * @param int|null $leadsPurchasedCount
     * @param bool|null $imported
     * @return bool
     */
    public static function is(
        self  $status,
        ?bool $adminApproved = null,
        ?bool $validPaymentMethod = null,
        ?int  $activeCampaignsCount = null,
        ?int  $reactivateCampaignsInFutureCount = null,
        ?int  $leadsPurchasedCount = null,
        ?bool $imported = null
    ): bool
    {
        return $status->value === self::get(
                $adminApproved,
                $validPaymentMethod,
                $activeCampaignsCount,
                $reactivateCampaignsInFutureCount,
                $leadsPurchasedCount,
                $imported
            );
    }

    /**
     * @param bool|null $adminApproved
     * @param bool|null $validPaymentMethod
     * @param int|null $activeCampaignsCount
     * @param int|null $reactivateCampaignsInFutureCount
     * @param int|null $leadsPurchasedCount
     * @param bool|null $imported
     * @return int
     */
    public static function get(
        ?bool $adminApproved,
        ?bool $validPaymentMethod,
        ?int  $activeCampaignsCount,
        ?int  $reactivateCampaignsInFutureCount,
        ?int  $leadsPurchasedCount,
        ?bool $imported
    ): int
    {
        if (!$validPaymentMethod && ($adminApproved || $imported)) {
            return self::PROFILE_ONLY->value;
        }

        if ($adminApproved && $validPaymentMethod) {
            return self::getLeadActivityStatus($activeCampaignsCount, $reactivateCampaignsInFutureCount, $leadsPurchasedCount);
        }

        return self::getRegistrationStatusType($validPaymentMethod);
    }

    /**
     * @param int|null $activeCampaignsCount
     * @param int|null $reactivateCampaignsInFutureCount
     * @param int|null $leadsPurchasedCount
     * @return int
     */
    protected static function getLeadActivityStatus(
        ?int $activeCampaignsCount,
        ?int $reactivateCampaignsInFutureCount,
        ?int $leadsPurchasedCount
    ): int
    {
        if ($activeCampaignsCount > 0) {
            return self::BUYING_LEADS_STATUS_ACTIVE->value;
        } else {
            return self::getLeadsPauseType($reactivateCampaignsInFutureCount, $leadsPurchasedCount);
        }
    }

    /**
     * @param ?int $reactivateCampaignsInFutureCount
     * @param ?int $leadsPurchasedCount
     * @return int
     */
    protected static function getLeadsPauseType(?int $reactivateCampaignsInFutureCount, ?int $leadsPurchasedCount): int
    {
        if ($reactivateCampaignsInFutureCount > 0) {
            return self::LEADS_PAUSED->value;
        } else {
            return self::getLeadsOffStatusType($leadsPurchasedCount);
        }
    }

    /**
     * @param ?int $leadsPurchasedCount
     * @return int
     */
    protected static function getLeadsOffStatusType(?int $leadsPurchasedCount): int
    {
        if ($leadsPurchasedCount > 0) {
            return self::LEADS_OFF->value;
        } else {
            return self::LEADS_OFF_NEVER_PURCHASED->value;
        }
    }

    /**
     * @param bool|null $validPaymentMethod
     * @return int
     */
    protected static function getRegistrationStatusType(?bool $validPaymentMethod): int
    {
        if ($validPaymentMethod) {
            return self::PENDING_APPROVAL->value;
        } else {
            return self::REGISTERING->value;
        }
    }
}
