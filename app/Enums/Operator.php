<?php

namespace App\Enums;

use Exception;

enum Operator: string
{
    case EQUAL_TO = "equalTo";
    case GREATER_THAN = "greaterThan";

    case GREATER_THAN_OR_EQUAL_TO = "greaterThanOrEqualTo";

    case LESS_THAN = "lessThan";

    case LESS_THAN_OR_EQUAL_TO = "lessThanOrEqualTo";

    /**
     * @param int|float $valueOne
     * @param Operator $operator
     * @param int|float $valueTwo
     * @return bool
     * @throws Exception
     */
    public static function evaluateOperator(int|float $valueOne, Operator $operator, int|float $valueTwo): bool
    {
        return match ($operator->value) {
            "equalTo" => $valueOne === $valueTwo,
            "greaterThan" => $valueOne > $valueTwo,
            "greaterThanOrEqualTo" => $valueOne >= $valueTwo,
            "lessThan" => $valueOne < $valueTwo,
            "lessThanOrEqualTo" => $valueOne <= $valueTwo,
            default => throw new Exception("'operator' must be one the following: " . json_encode(self::cases())),
        };
    }

    /**
     * @param  string|Operator|null  $operator
     * @return string
     * @throws Exception
     */
    public static function sqlOperator(string|Operator|null $operator): string
    {
        $operator = $operator instanceof Operator ? $operator->value : $operator;

        return match ($operator) {
            "equalTo" => "=",
            "greaterThan" => ">",
            "greaterThanOrEqualTo" => ">=",
            "lessThan" => "<",
            "lessThanOrEqualTo" => "<=",
            default => throw new Exception("'operator' must be one the following: " . json_encode(self::cases())),
        };
    }
}
