<?php

namespace App\Enums;

enum EventName: string
{
    case SUSPENDED_FOR_FAILED_PAYMENT   = 'suspended-for-failed-payment';
    case REACTIVATION_AUTOMATIC         = 'reactivation-automatic';
    case REACTIVATION_MANUAL            = 'reactivation-manual';
    case COMPLETED_REGISTRATION         = 'completed-registration';
    case STARTED_REGISTRATION           = 'started-registration';
    case LEFT_REGISTRATION              = 'left-registration';
    case GLOBAL_STATUS_UPDATED          = 'global-status-updated';
    case PAYMENT_METHOD_FAILED          = 'payment-method-failed';
    case PAYMENT_METHOD_ADDED           = 'payment-method-added';
    case PAYMENT_METHOD_DELETED         = 'payment-method-deleted';
    case USER_CREATED                   = 'user-created';
    case USER_ASSOCIATED                = 'user-associated';
    case USER_DISASSOCIATED             = 'user-disassociated';
    case ALERT_ASSIGNED                 = 'alert-assigned';
    case ALERT_REMOVED                  = 'alert-removed';
    case CONTACT_ASSIGNED               = 'contact-assigned';
    case CONTACT_REMOVED                = 'contact-removed';
    case CONTACT_SUPPRESSED             = 'contact-suppressed';
    case CONTACT_UNSUPPRESSED           = 'contact-unsuppressed';
    case SURVEY_COMPLETED               = 'survey-completed';
    case SURVEY_ANSWERED_QUESTION       = 'survey-answered-question';
    case PROFILE_MEDIA_IMAGES_CREATED   = 'profile-media-images-created';
    case PROFILE_MEDIA_IMAGES_DELETED   = 'profile-media-images-deleted';
    case PROFILE_MEDIA_IMAGES_UPDATED   = 'profile-media-images-updated';
    case PROFILE_YOUTUBE_LINK_ADDED     = 'profile-youtube-link-added';
    case PROFILE_MEDIA_LOGO_UPDATED     = 'profile-media-logo-updated';
    case PROFILE_DESCRIPTION_UPDATED    = 'profile-description-updated';
    case PROFILE_YEAR_STARTED_UPDATED   = 'profile-year-started-updated';
    case PROFILE_ADDRESS_CREATED        = 'profile-address-created';
    case PROFILE_ADDRESS_UPDATED        = 'profile-addresses-updated';
    case PROFILE_ADDRESS_DELETED        = 'profile-address-deleted';
    case PROFILE_CERTIFICATIONS_CREATED = 'profile-certifications-created';
    case PROFILE_CERTIFICATIONS_DELETED = 'profile-certifications-deleted';
    case PROFILE_SERVICES_UPDATED       = 'profile-services-updated';
    case PROFILE_LICENSE_CREATED        = 'profile-license-created';
    case PROFILE_LICENSE_UPDATED        = 'profile-license-updated';
    case PROFILE_LICENSE_DELETED        = 'profile-license-deleted';
    case BUDGET_INCREASE                = 'budget-increase';
    case BUDGET_DECREASE                = 'budget-decrease';
    case BUDGET_MAX_INCREASE            = 'budget-max-usage-increase';
    case BUDGET_MAX_DECREASE            = 'budget-max-usage-decrease';
    case BUDGET_TYPE_UPDATED            = 'budget-type-updated';
    case SERVICE_AREA_UPDATED           = 'service-area-updated';
    case UTILITIES_UPDATED              = 'utilities-updated';
    case BIDS_UPDATED                   = 'bids-updated';
    case CREATED                        = 'created';
    case DELETED                        = 'deleted';
    case RESPONDED                      = 'responded';
    case DELIVERY_SUCCESS               = 'delivery-success';
    case DELIVERY_FAILURE               = 'delivery-failure';
    case STATUS_UPDATED                 = 'status-updated';
    case CONSOLIDATED_STATUS_UPDATED    = 'consolidated-status-updated';
    case OPPORTUNITY_PRODUCT_PURCHASED  = 'opportunity-product-purchased';
    case SALES_BAIT_RECEIVED            = 'sales-bait-received';
    case SALES_BAIT_REGISTERED_INTEREST = 'sales-bait-registered-interest';
    case SALES_BAIT_UNSUBSCRIBED        = 'sales-bait-unsubscribed';
    case LEAD_UNDERSOLD                 = 'lead-undersold';
    case LEAD_UNSOLD_NO_COMPANIES       = 'lead-unsold-no-companies';
    case LEAD_UNSOLD_NO_BUDGET          = 'lead-unsold-no-budget';
    case LEAD_BASIC_INFO_UPDATED        = 'lead-basic-info-updated';
    case LEAD_CANCELLED                 = 'lead-cancelled';
    case LEAD_RELEASED                  = 'lead-released';
    case LEAD_MARKED_AS_PENDING_REVIEW  = 'lead-marked-as-pending-review';
    case LEAD_MARKED_AS_UNDER_REVIEW    = 'lead-marked-as-under-review';
    case LEAD_ALLOCATED                 = 'lead-allocated';
    case LEAD_BEST_TIME_TO_CONTACT      = 'lead-best-time-to-contact';
    case LEAD_APPROVED                  = 'lead-approved';
    case TASK_REASSIGNED                = 'task-reassigned';
    case ACCOUNT_MANAGER_ASSIGNED       = 'account-manager-assigned';
    case ASSIGN_UNASSIGNED_ACCOUNT      = 'assign-unassigned-account';
    case INBOUND_SMS                    = 'inbound-sms';
    case PROFITABILITY_ASSUMPTION_UPDATED                = 'profitability-assumption-updated';
    case COMPANY_USER_CREATED                            = 'company-user-created';
    case COMPANY_USER_UPDATED                            = 'company-user-updated';
    case COMPANY_USER_DELETED                            = 'company-user-deleted';
    case COMPANY_CONTACT_CREATED                         = 'company-contact-created';
    case COMPANY_CONTACT_UPDATED                         = 'company-contact-updated';
    case COMPANY_CONTACT_DELETED                         = 'company-contact-deleted';
    case COMPANY_BASIC_DETAILS_UPDATED                   = 'company-basic-details-updated';
    case COMPANY_CONFIGURABLE_FIELDS_UPDATED             = 'company-configurable-fields-updated';
    case REJECTION_PERCENTAGE_EXCEEDED                   = 'rejection-percentage-exceeded';
    case CRM_REJECTION_PERCENTAGE_EXCEEDED               = 'crm-rejection-percentage-exceeded';
    case APPOINTMENT_REJECTION_PERCENTAGE_EXCEEDED       = 'appointment-rejection-percentage-exceeded';
    case APPOINTMENT_CRM_REJECTION_PERCENTAGE_EXCEEDED   = 'appointment-crm-rejection-percentage-exceeded';
    case MISSED_CALL                                     = 'missed-call';
    case REGISTRATION_COMPANY_AND_USER_CREATED           = 'registration-company-and-user-created';
    case REGISTRATION_ADD_COMPANY_LOCATION               = 'registration-company-add-location';
    case EVENT_COMPANY_IMPORTED                          = 'company-imported';
    case EVENT_COMPANY_LOCATION_IMPORTED                 = 'company-location-imported';
    case EVENT_COMPANY_CONTACT_IMPORTED                  = 'company-contact-imported';
    case BID_TYPE_UPDATED                                = 'bid-type-updated';
    case BID_INCREASED                                   = 'bid-increased';
    case BID_DECREASED                                   = 'bid-decreased';
    case NOTIFICATION_SENT                               = 'notification-sent';
    case WRITE_LOG                                       = 'write-log';
    case APPOINTMENTS_STATUS_UPDATED                     = 'appointments-status-updated';
    case PRODUCT_CAMPAIGN_UPDATED                        = 'product-campaign-updated';
    case PRODUCT_CAMPAIGN_BUDGET_UPDATED                 = 'product-campaign-budget-updated';
    case CAMPAIGN_SCHEDULE_UPDATED                       = 'campaign-schedule-updated';
    case CAMPAIGN_SCHEDULE_DELETED                       = 'campaign-schedule-deleted';
    case CHARGEBACK_ISSUED                               = 'chargeback-issued';
    case CHARGE_FAILED                                   = 'charge-failed';
    case MONTHLY_AD_SPEND_UPDATED                        = 'monthly-ad-spend-updated';

    case TEST_LEAD_GENERATED = 'test-lead-generated';
    case TEST_LEAD_DELIVERED = 'test-lead-delivered';
    case TEST_LEAD_REVEALED  = 'test-lead-revealed';
    case TEST_LEAD_EXPIRED   = 'test-lead-expired';

    case TESTING_ALLOCATION            = 'testing-allocation';
    case TESTING_SYNC_CAMPAIGN_STATUS  = 'testing-sync-campaign-status';

    const OLD_VALUE_KEY_NOT_ASSIGNED             = 'old_key_not_assigned';
    const NEW_VALUE_KEY_NOT_ASSIGNED             = 'new_key_not_assigned';

    case MAILBOX_EMAIL_SEND_SUCCESS                = 'mailbox-email-send-success';
    case MAILBOX_EMAIL_SEND_ERROR                  = 'mailbox-email-send-error';
    case MAILBOX_EMAIL_RECEIVED                    = 'mailbox-email-received';
    case MAILBOX_SYNC_FINISHED                     = 'mailbox-sync-finished';
    case MAILBOX_SYNC_ERROR                        = 'mailbox-sync-error';
    case MAILBOX_RENEW_MAILBOX_LISTENER_ERROR      = 'mailbox-renew-mailbox-listener-error';
    case MAILBOX_RENEW_MAILBOX_LISTENER_SUCCESS    = 'mailbox-renew-mailbox-listener-success';

    case CARD_EXPIRING_SOON = 'card-expiring-soon';
    case CARD_EXPIRED = 'card-expired';
    case CARD_ADDED = 'card-added';
    case INVOICE_OVERDUE = 'invoice-overdue';

    case GENERIC_EVENT = 'generic-event';

    public function getOldValueKey(): string
    {
        return match($this) {
            self::BUDGET_DECREASE, self::BUDGET_INCREASE => 'old_budget',
            self::BID_DECREASED, self::BID_INCREASED => 'old_bid',
            self::GLOBAL_STATUS_UPDATED, self::STATUS_UPDATED, self::REACTIVATION_AUTOMATIC, self::REACTIVATION_MANUAL => 'old_status',
            self::BUDGET_MAX_INCREASE, self::BUDGET_MAX_DECREASE => 'old_max_budget_usage',
            self::UTILITIES_UPDATED => 'old_utility_count',
            self::SERVICE_AREA_UPDATED => 'old_service_area_count',
            self::PROFILE_MEDIA_LOGO_UPDATED => 'old_media_link',
            self::PROFILE_DESCRIPTION_UPDATED => 'old_description',
            self::PROFILE_SERVICES_UPDATED => 'old_services_count',
            self::PROFILE_ADDRESS_UPDATED => 'old_full_address',
            self::PROFILE_LICENSE_UPDATED => 'old_license',
            self::BUDGET_TYPE_UPDATED, self::BID_TYPE_UPDATED => 'old_budget_type',
            self::TASK_REASSIGNED => 'task_reassigned_from_user_id',
            self::PROFILE_YEAR_STARTED_UPDATED => 'old_year_started',
            self::MONTHLY_AD_SPEND_UPDATED => 'old_monthly_ad_data',
            default => self::OLD_VALUE_KEY_NOT_ASSIGNED,
        };
    }

    public function getNewValueKey(): string
    {
        return match($this) {
            self::BUDGET_DECREASE, self::BUDGET_INCREASE => 'new_budget',
            self::BID_DECREASED, self::BID_INCREASED => 'new_bid',
            self::GLOBAL_STATUS_UPDATED, self::STATUS_UPDATED, self::REACTIVATION_AUTOMATIC, self::REACTIVATION_MANUAL => 'new_status',
            self::BUDGET_MAX_INCREASE, self::BUDGET_MAX_DECREASE => 'new_max_budget_usage',
            self::UTILITIES_UPDATED => 'new_utility_count',
            self::SERVICE_AREA_UPDATED => 'new_service_area_count',
            self::PROFILE_MEDIA_LOGO_UPDATED => 'new_media_link',
            self::PROFILE_DESCRIPTION_UPDATED => 'new_description',
            self::PROFILE_SERVICES_UPDATED => 'new_services_count',
            self::PROFILE_ADDRESS_UPDATED => 'new_full_address',
            self::PROFILE_LICENSE_UPDATED => 'new_license',
            self::BUDGET_TYPE_UPDATED, self::BID_TYPE_UPDATED => 'new_budget_type',
            self::TASK_REASSIGNED => 'task_assigned_user_id',
            self::PROFILE_YEAR_STARTED_UPDATED => 'new_year_started',
            self::PROFILE_YOUTUBE_LINK_ADDED => 'new_youtube_link',
            self::TASK_REASSIGNED   => 'task_assigned_user_id',
            self::MONTHLY_AD_SPEND_UPDATED => 'new_monthly_ad_data',
            default => self::NEW_VALUE_KEY_NOT_ASSIGNED,
        };
    }

    public static function getAllCampaignEvents(): array
    {
        return [
            self::REACTIVATION_AUTOMATIC->value,
            self::REACTIVATION_MANUAL->value,
            self::BUDGET_INCREASE->value,
            self::BUDGET_DECREASE->value,
            self::BUDGET_MAX_INCREASE->value,
            self::BUDGET_MAX_DECREASE->value,
            self::BUDGET_TYPE_UPDATED->value,
            self::SERVICE_AREA_UPDATED->value,
            self::UTILITIES_UPDATED->value,
            self::STATUS_UPDATED->value,
//            self::BIDS_UPDATED->value, DISABLED: this is not firing from anything useful in Legacy right now
            self::CREATED->value,
            self::DELETED->value,
            self::BID_TYPE_UPDATED->value,
            self::BID_INCREASED->value,
            self::BID_DECREASED->value,
        ];
    }
}
