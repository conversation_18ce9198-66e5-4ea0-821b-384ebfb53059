<?php

namespace App\Enums;

enum TestProductStatus: int
{
    case INITIAL = 0;
    case DELIVERED = 1;
    case REVEALED = 2;
    case EXPIRED = 3;
    case FAILED_DELIVERY = 4;

    public function toName(): string
    {
        return match ($this) {
            self::INITIAL   => 'Initial',
            self::DELIVERED => 'Delivered',
            self::REVEALED  => 'Revealed',
            self::EXPIRED   => 'Expired',
            self::FAILED_DELIVERY => 'Failed Delivery',
        };
    }
}
