<?php

namespace App\EnumAttributes\Concerns;

use App\EnumAttributes\Description;
use Illuminate\Support\Str;
use ReflectionClassConstant;

trait GetAttributesTrait
{
    /**
     * @param self $enum
     */
    private static function getDescription(self $enum): string
    {
        $ref = new ReflectionClassConstant(self::class, $enum->name);
        $classAttributes = $ref->getAttributes(Description::class);

        if (count($classAttributes) === 0) {
            return Str::headline($enum->value);
        }

        return $classAttributes[0]->newInstance()->description;
    }

    /**
     * @return array<string,string>
     */
    public static function asSelectArray(): array
    {
        /** @var array<string,string> $values */
        $values = collect(self::cases())
            ->map(function ($enum) {
                return [
                    'name' => self::getDescription($enum),
                    'id' => $enum->value,
                ];
            })->toArray();

        return $values;
    }

    /**
     * @return array<string,string>
     */
    public static function getAsKeyValueSelectArray(): array
    {
        /** @var array<string,string> $cases */
        $cases = collect(self::cases());

        $result = [];

        /**
         * @var self $case
         */
        foreach ($cases as $case) {
            $result[self::getDescription($case)] = $case->value;
        }

        return $result;
    }

    /**
     * @return array<string>
     */
    public static function getValueArray(): array
    {
        /** @var array<string,string> $values */
        $values = collect(self::cases())
            ->map(function ($enum) {
                return $enum->value;
            })->toArray();

        return $values;
    }

    public static function getEnumByDescription(string $description): ?self {
        foreach (self::cases() as $case) {
            if (strcasecmp(self::getDescription($case), $description) === 0) {
                return $case;
            }
        }
        return null;
    }

    /**
     * Convert an array of full state names to both full names and abbreviations.
     *
     * @param array<string> $names
     * @return array<string>
     */
    public static function fromStateNames(array $names): array {
        $descriptions = array_map([self::class, 'getEnumByDescription'], $names);
        $abbreviations = array_filter($descriptions);

        $abbreviationValues = array_map(function ($enum) {
            return $enum->value;
        }, $abbreviations);

        return array_unique(array_merge($names, $abbreviationValues));
    }
}
