<?php

namespace App\Traits;

use App\Models\CompanyUserRelationship;
use App\Models\Odin\Company;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Spatie\Permission\Models\Role;

/**
 * @method Builder accountManagerRole
 * @method Builder businessDevelopmentManagerRole
 * @method Builder customerSuccessManagerRole
 * @method Builder salesDevelopmentRepresentativeRole
 * @method Builder onboardingManagerRole
 */
trait CompanyAssignable
{
    public function companyRelationships(): HasMany
    {
        return $this->hasMany(CompanyUserRelationship::class);
    }

    public function assignedCompanies(): HasManyThrough
    {
        return $this->hasManyThrough(
            Company::class,
            CompanyUserRelationship::class,
            'user_id',
            'id',
            'id',
            'company_id'
        );
    }

    public function accountManagerCompanies(): HasManyThrough
    {
        return $this->assignedCompanies()->where(
            'company_user_relationships.role_id',
            Role::findByName('account-manager')->id
        );
    }

    public function businessDevelopmentManagerCompanies(): HasManyThrough
    {
        return $this->assignedCompanies()->where(
            'company_user_relationships.role_id',
            Role::findByName('business-development-manager')->id
        );
    }

    public function customerSuccessManagerCompanies(): HasManyThrough
    {
        return $this->assignedCompanies()->where(
            'company_user_relationships.role_id',
            Role::findByName('customer-success-manager')->id
        );
    }

    public function salesDevelopmentRepresentativeCompanies(): HasManyThrough
    {
        return $this->assignedCompanies()->where(
            'company_user_relationships.role_id',
            Role::findByName('sales-development-representative')->id
        );
    }

    public function onboardingManagerCompanies(): HasManyThrough
    {
        return $this->assignedCompanies()->where(
            'company_user_relationships.role_id',
            Role::findByName('onboarding-manager')->id
        );
    }

    public function scopeAccountManagerRole(Builder $query): void
    {
        $query->role('account-manager');
    }

    public function scopeBusinessDevelopmentManagerRole(Builder $query): void
    {
        $query->role('business-development-manager');
    }

    public function scopeCustomerSuccessManagerRole(Builder $query): void
    {
        $query->role('customer-success-manager');
    }

    public function scopeSalesDevelopmentRepresentativeRole(Builder $query): void
    {
        $query->role('sales-development-representative');
    }

    public function scopeOnboardingManagerRole(Builder $query): void
    {
        $query->role('onboarding-manager');
    }
}
