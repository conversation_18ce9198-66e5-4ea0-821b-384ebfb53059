<?php

namespace App\Traits;

use App\Builders\LegacyBrsPotentialCampaignsBuilder;
use App\Enums\CompanyConsolidatedStatus;
use App\Enums\Odin\Product as ProductEnum;
use App\Models\ComputedRejectionStatistic;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyIndustry;
use App\Models\Odin\Industry;
use App\Models\Odin\Product as ProductModel;
use App\Models\Odin\ProductCampaign;
use App\Services\DatabaseHelperService;
use Illuminate\Database\Eloquent\Builder;

trait LegacyBrsPotentialProductCampaigns
{
    /**
     * @param Builder $query
     * @param string $industry
     * @param array $excludedCompanyIds
     * @return Builder
     */
    public function addAppointmentConstraints(
        Builder &$query,
        string $industry,
        array $excludedCompanyIds
    ): Builder
    {
        $this->joinLeadCampaign($query);
        $this->joinProduct($query);
        $this->joinCompany($query);
        $this->joinCompanyIndustry($query);
        $this->joinIndustry($query);
        $this->joinComputedRejectionStatistics($query);
        $this->whereProductCampaignActive($query);
        $this->whereIndustryIs($query, $industry);
        $this->whereProductIsAppointment($query);
        $this->whereCompanyIsNotExcluded($query, $excludedCompanyIds);
        $this->whereCompanyIsActive($query);
        $this->selectProductCampaignFields($query);

        return $query;
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function joinLeadCampaign(Builder &$query): Builder
    {
        $query->join(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE, function($join) {
            $join->on(
                DatabaseHelperService::database().'.'.ProductCampaign::TABLE.'.'.ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID,
                '=',
                DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::ID
            );
        });

        return $query;
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function joinProduct(Builder &$query): Builder
    {
        $query->join( DatabaseHelperService::database().'.'.ProductModel::TABLE, function($join) {
            $join->on(
                DatabaseHelperService::database().'.'.ProductModel::TABLE.'.'.ProductModel::FIELD_ID,
                '=',
                DatabaseHelperService::database().'.'.ProductCampaign::TABLE.'.'.ProductCampaign::FIELD_PRODUCT_ID
            );
        });

        return $query;
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function joinCompany(Builder &$query): Builder
    {
        $query->join(DatabaseHelperService::database().'.'.Company::TABLE, function($join) {
            $join->on(
                DatabaseHelperService::database().'.'.Company::TABLE.'.'.Company::FIELD_ID,
                '=',
                DatabaseHelperService::database().'.'.ProductCampaign::TABLE.'.'.ProductCampaign::FIELD_COMPANY_ID
            );
        });

        return $query;
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function joinCompanyIndustry(Builder &$query): Builder
    {
        $query->join(DatabaseHelperService::database() . '.' . CompanyIndustry::TABLE, function ($join) {
            $join->on(
                DatabaseHelperService::database() . '.' . CompanyIndustry::TABLE . '.' . CompanyIndustry::FIELD_COMPANY_ID,
                '=',
                DatabaseHelperService::database() . '.' . ProductCampaign::TABLE . '.' . ProductCampaign::FIELD_COMPANY_ID
            );
        });

        return $query;
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function joinIndustry(Builder &$query): Builder
    {
        $query->join(DatabaseHelperService::database() . '.' . Industry::TABLE, function ($join) {
            $join->on(
                DatabaseHelperService::database() . '.' . CompanyIndustry::TABLE . '.' . CompanyIndustry::FIELD_INDUSTRY_ID,
                '=',
                DatabaseHelperService::database() . '.' . Industry::TABLE . '.' . Industry::FIELD_ID
            );
        });

        return $query;
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function joinComputedRejectionStatistics(Builder &$query): Builder
    {
        $query->leftJoin(DatabaseHelperService::database(). '.' . ComputedRejectionStatistic::TABLE, function($join) {
            $join
                ->on(
                    DatabaseHelperService::database().'.'.ComputedRejectionStatistic::TABLE.'.'.ComputedRejectionStatistic::FIELD_COMPANY_ID,
                    '=',
                    DatabaseHelperService::database().'.'.ProductCampaign::TABLE.'.'.ProductCampaign::FIELD_COMPANY_ID
                )
                ->on(
                    DatabaseHelperService::database().'.'.ComputedRejectionStatistic::TABLE.'.'.ComputedRejectionStatistic::FIELD_PRODUCT_ID,
                    '=',
                    DatabaseHelperService::database().'.'.ProductCampaign::TABLE.'.'.ProductCampaign::FIELD_PRODUCT_ID
                );
        });

        return $query;
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function whereProductCampaignActive(Builder &$query): Builder
    {
        $query->where(ProductCampaign::TABLE.'.'.ProductCampaign::FIELD_STATUS, true);

        return $query;
    }

    /**
     * @param Builder $query
     * @param string $industry
     * @return Builder
     */
    public function whereIndustryIs(Builder &$query, string $industry): Builder
    {
        $query->where(Industry::TABLE.'.'.Industry::FIELD_NAME, $industry);

        return $query;
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function whereProductIsAppointment(Builder &$query): Builder
    {
        $query->where(ProductModel::TABLE.'.'.ProductModel::FIELD_NAME, ProductEnum::APPOINTMENT->value);

        return $query;
    }

    /**
     * @param Builder $query
     * @param array $excludedCompanyIds
     * @return Builder
     */
    public function whereCompanyIsNotExcluded(Builder &$query, array $excludedCompanyIds): Builder
    {
        $query->whereNotIn(Company::TABLE.'.'.Company::FIELD_ID, $excludedCompanyIds ?? []);

        return $query;
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function whereCompanyIsActive(Builder &$query): Builder
    {
        $query->where(Company::TABLE.'.'.Company::FIELD_CONSOLIDATED_STATUS, CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS->value);

        return $query;
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function selectProductCampaignFields(Builder &$query): Builder
    {
        $saleTypeIdCol = LegacyBrsPotentialCampaignsBuilder::SALE_TYPE_ID_COL;
        $campaignIdCol = LegacyBrsPotentialCampaignsBuilder::CAMPAIGN_ID_COL;
        $companyIdCol  = LegacyBrsPotentialCampaignsBuilder::COMPANY_ID_COL;

        $query->selectRaw(implode(',', [
            ProductCampaign::TABLE.'.'.ProductCampaign::FIELD_ID." AS {$campaignIdCol}",
            LeadCampaignSalesTypeConfiguration::TABLE.'.'.LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID." AS {$saleTypeIdCol}",
            ProductCampaign::TABLE.'.'.ProductCampaign::FIELD_COMPANY_ID." AS {$companyIdCol}",
            "COALESCE(".ComputedRejectionStatistic::TABLE.'.'.ComputedRejectionStatistic::ACTIVE_REJECTION_FIELD_IMPACTING_ELIGIBILITY.", 0) AS " . ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_ELIGIBILITY,
            "COALESCE(".ComputedRejectionStatistic::TABLE.'.'.ComputedRejectionStatistic::ACTIVE_REJECTION_FIELD_IMPACTING_ELIGIBILITY.", 0) AS " . ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID,
        ]));

        return $query;
    }
}
