<?php

namespace App\Traits;

use App\Builders\LegacyBrsPotentialCampaignsBuilder;
use App\Models\ComputedRejectionStatistic;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Services\DatabaseHelperService;
use Illuminate\Database\Eloquent\Builder;

trait LegacyBrsPotentialLeadCampaigns
{
    /**
     * @param Builder $query
     * @param float $rejectionPercentageThreshold
     * @param array $excludedLegacyCompanyIds
     * @param array $companyTypes
     * @return Builder
     */
    public function addLeadConstraints(
        Builder &$query,
        float $rejectionPercentageThreshold,
        array $excludedLegacyCompanyIds,
        array $companyTypes
    ): Builder
    {
        $this->removeLeadCampaignCompaniesOverRejectionPercentage($query, $rejectionPercentageThreshold);
        $this->joinLegacyCompany($query);
        $this->whereLegacyCompanyIsNotExcluded($query, $excludedLegacyCompanyIds);
        $this->whereLegacyCompanyActive($query);
        $this->whereLegacyCompanyTypeIn($query, $companyTypes);
        $this->whereLegacyCompanyIsChargeable($query);
        $this->whereLeadCampaignActive($query);
        $this->selectLeadCampaignFields($query);

        return $query;
    }

    /**
     * @param Builder $query
     * @param float $rejectionPercentageThreshold
     * @return Builder
     */
    public function removeLeadCampaignCompaniesOverRejectionPercentage(Builder &$query, float $rejectionPercentageThreshold): Builder
    {
        $query->removeLegacyLeadCampaignCompaniesOverRejectionPercentage($rejectionPercentageThreshold);

        return $query;
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function joinLegacyCompany(Builder &$query): Builder
    {
        $query
            ->join(DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE, function($join) {
                $join
                    ->on(
                        DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE.'.'.EloquentCompany::ID,
                        '=',
                        DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::COMPANY_ID
                    );
            });

        return $query;
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function whereLegacyCompanyActive(Builder &$query): Builder
    {
        $query
            ->where(EloquentCompany::TABLE.'.'.EloquentCompany::STATUS, EloquentCompany::STATUS_ACTIVE)
            ->where(EloquentCompany::TABLE.'.'.EloquentCompany::BUYING_SOLAR_ESTIMATE_LEADS, EloquentCompany::BUYING_LEADS_STATUS_ACTIVE);

        return $query;
    }

    /**
     * @param Builder $query
     * @param array $companyTypes
     * @return Builder
     */
    public function whereLegacyCompanyTypeIn(Builder &$query, array $companyTypes): Builder
    {
        $query->whereIn(EloquentCompany::TABLE.'.'.EloquentCompany::TYPE, $companyTypes);

        return $query;
    }

    /**
     * @param Builder $query
     * @param array $excludedLegacyCompanyIds
     * @return Builder
     */
    public function whereLegacyCompanyIsNotExcluded(Builder &$query, array $excludedLegacyCompanyIds): Builder
    {
        $query->whereNotIn(EloquentCompany::TABLE.'.'.EloquentCompany::ID, $excludedLegacyCompanyIds);

        return $query;
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function whereLegacyCompanyIsChargeable(Builder &$query): Builder
    {
        $query->where(function($where) {
            $where
                ->where(EloquentCompany::TABLE.'.'.EloquentCompany::ALLOW_LEAD_SALES_WITHOUT_CC, true)
                ->orWhere(EloquentCompany::TABLE.'.'.EloquentCompany::CHARGE_ATTEMPTS, '<', config('sales.max_charge_attempts'))
                ->orWhereNull(EloquentCompany::TABLE.'.'.EloquentCompany::CHARGE_ATTEMPTS);
        });

        return $query;
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function whereLeadCampaignActive(Builder &$query): Builder
    {
        $query->where(LeadCampaign::TABLE.'.'.LeadCampaign::STATUS, true);

        return $query;
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function selectLeadCampaignFields(Builder &$query): Builder
    {
        $saleTypeIdCol                              = LegacyBrsPotentialCampaignsBuilder::SALE_TYPE_ID_COL;
        $campaignIdCol                              = LegacyBrsPotentialCampaignsBuilder::CAMPAIGN_ID_COL;
        $companyIdCol                               = LegacyBrsPotentialCampaignsBuilder::COMPANY_ID_COL;
        $rejectionPercentageImpactingEligibilityCol = ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_ELIGIBILITY;
        $rejectionPercentageImpactingBidCol         = ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID;

        $query->selectRaw(implode(',', [
            LeadCampaign::TABLE.'.'.LeadCampaign::ID." AS {$campaignIdCol}",
            LeadCampaignSalesTypeConfiguration::TABLE.'.'.LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID." AS {$saleTypeIdCol}",
            LeadCampaign::TABLE.'.'.LeadCampaign::COMPANY_ID." AS {$companyIdCol}",
            "COALESCE(".ComputedRejectionStatistic::TABLE.'.'.ComputedRejectionStatistic::ACTIVE_REJECTION_FIELD_IMPACTING_ELIGIBILITY.", 0) AS {$rejectionPercentageImpactingEligibilityCol}",
            "COALESCE(".ComputedRejectionStatistic::TABLE.'.'.ComputedRejectionStatistic::ACTIVE_REJECTION_FIELD_IMPACTING_BID.", 0) AS {$rejectionPercentageImpactingBidCol}"
        ]));

        return $query;
    }
}
