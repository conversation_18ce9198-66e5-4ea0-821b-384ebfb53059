<?php

namespace App\Traits;

use App\Models\ContactIdentification\IdentifiedContact;
use Illuminate\Database\Eloquent\Relations\HasOne;

trait ContactIdentifiable
{
    const FIELD_IDENTIFIED_CONTACT_ID = 'identified_contact_id';
    const RELATION_IDENTIFIED_CONTACT = 'identifiedContact';

    /**
     * @return ?HasOne
     */
    public function identifiedContact(): ?HasOne
    {
        return $this->hasOne(IdentifiedContact::class, IdentifiedContact::FIELD_ID, $this->getFieldIdentifiedContactIdField());
    }

    /**
     * @return string
     */
    public function getFieldIdentifiedContactIdField(): string
    {
        return $this->fieldIdentifiedContactId ?: self::FIELD_IDENTIFIED_CONTACT_ID;
    }
}
