<?php

namespace App\Traits;

use App\Models\Odin\Website;
use App\Notifications\VerifyReviewerEmail;
use Illuminate\Auth\MustVerifyEmail;
use Illuminate\Notifications\Messages\MailMessage;

trait ReviewerCanVerifyEmail
{
    use MustVerifyEmail {
        MustVerifyEmail::getEmailForVerification as private parentGetEmailForVerification;
        MustVerifyEmail::hasVerifiedEmail as private parentHasVerifiedEmail;
        MustVerifyEmail::markEmailAsVerified as private parentMarkEmailAsVerified;
        MustVerifyEmail::sendEmailVerificationNotification as private parentSendEmailVerificationNotification;
    }

    public function hasVerifiedEmail(): bool
    {
        return $this->parentHasVerifiedEmail();
    }

    public function markEmailAsVerified(): bool
    {
        return $this->parentMarkEmailAsVerified();
    }

    public function sendEmailVerificationNotification(string $reviewUuid, string $token, ?Website $origin): void
    {
        $fromOrigin = $origin?->name ?? 'SolarReviews';
        $fromDomain = $origin && app()->isProduction()
            ? $origin->url
            : config('app.solarreviews_domain.www');

        $this->notify(new VerifyReviewerEmail(function ($notifiable) use ($reviewUuid, $token, $fromOrigin, $fromDomain) {
            $url = "$fromDomain/reviews/verify-email?token=$token&review_uuid=$reviewUuid";

            return (new MailMessage)
                ->subject("$fromOrigin: Please Verify Email Address for your Review")
                ->line('Click the button below to verify your email address for your review.')
                ->action('Verify Email Address', $url);
        }));
    }

    public function getEmailForVerification(): bool
    {
        return $this->parentGetEmailForVerification();
    }
}
