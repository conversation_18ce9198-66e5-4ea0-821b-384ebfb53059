<?php

namespace App\Traits;

use <PERSON>\Uuid\Uuid;

trait HasReference
{
    private function generateLegacyReference(): string
    {
        $length = 10;
        $hash = substr(sha1(strval(mt_rand()).strval(microtime()).strval("")), 0, $length);
        return strtoupper($hash);
    }

    /**
     * @param string $method
     *
     * @return string
     */
    public function generateReference(string $method = 'uuid'): string
    {
        return match ($method) {
            'uuid'    => Uuid::uuid4()->toString(),
            'hash'    => $this->generateLegacyReference(),
        };
    }

    /**
     * @param string $method
     *
     * @return void
     */
    public function saveReference(string $method = 'hash'): void
    {
        $reference = $this->generateReference($method);

        if ($this->newQuery()->where(self::FIELD_REFERENCE, $reference)->first()) {
            $this->saveReference($method);
            return;
        }

        $this->reference = $reference;
        $this->save();
    }
}
