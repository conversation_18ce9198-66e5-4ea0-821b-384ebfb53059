<?php

namespace App\Traits;

use App\Enums\Billing\InvoiceStates;
use App\Enums\Campaigns\CampaignStatus;
use App\Enums\Company\CompanyAdminStatus;
use App\Enums\Company\CompanyCampaignStatus;
use App\Enums\Company\CompanySystemStatus;
use App\Enums\CompanyConsolidatedStatus;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Enums\Odin\CompanyChangeLogType;
use App\Enums\Odin\Product;
use App\Enums\RoleType;
use App\Jobs\DispatchPubSubEvent;
use App\Models\Billing\Invoice;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ProductAssignment;
use App\Models\User;
use App\Repositories\ComputedRejectionStatisticRepository;
use App\Services\Billing\CompanyBillingServiceV4;
use App\Services\Odin\CompanyChangeLogService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

trait HasStatuses
{
    /**
     * @return void
     */
    public function recalculateCampaignStatus(): void
    {
        $newStatus = $this->determineCampaignStatus();

        if ($newStatus !== $this->campaign_status) {
            $this->update([
                self::FIELD_CAMPAIGN_STATUS => $newStatus
            ]);
        }
    }

    /**
     * @return void
     */
    public function recalculateSystemStatus(): void
    {
        $newStatus = $this->determineSystemStatus();

        if ($newStatus !== $this->system_status) {
            $this->update([
                self::FIELD_SYSTEM_STATUS => $newStatus
            ]);
        }
    }

    /**
     * @return void
     */
    public function recalculateConsolidatedStatus(): void
    {
        $newStatus = $this->determineConsolidatedStatus();

        if ($newStatus !== $this->consolidated_status) {
            $this->logConsolidatedStatusChange($this->consolidated_status, $newStatus);

            $this->updateQuietly([
                self::FIELD_CONSOLIDATED_STATUS => $newStatus
            ]);

            DispatchPubSubEvent::dispatch(
                EventCategory::COMPANIES,
                EventName::CONSOLIDATED_STATUS_UPDATED,
                [
                    'company_reference' => $this->reference,
                    'new_status'        => CompanyConsolidatedStatus::label($newStatus)
                ]
            );
        }
    }

    /**
     * @return CompanyCampaignStatus
     */
    public function determineCampaignStatus(): CompanyCampaignStatus
    {
        $campaigns = $this->futureCampaigns()->get();

        if ($campaigns->filter(fn(CompanyCampaign $campaign) => $campaign->status === CampaignStatus::ACTIVE)->isNotEmpty()) {
            return CompanyCampaignStatus::CAMPAIGNS_ACTIVE;
        }

        if ($campaigns->filter(fn(CompanyCampaign $campaign) => $campaign->status === CampaignStatus::PAUSED_TEMPORARILY)->isNotEmpty()) {
            return CompanyCampaignStatus::CAMPAIGNS_PAUSED;
        }

        if ($campaigns->filter(fn(CompanyCampaign $campaign) => $campaign->status === CampaignStatus::PAUSED_PERMANENTLY)->isNotEmpty()) {
            if ($this->productAssignments()
                ->where(ProductAssignment::FIELD_CHARGEABLE, true)
                ->where(ProductAssignment::FIELD_DELIVERED, true)
                ->first()) {
                return CompanyCampaignStatus::CAMPAIGNS_OFF;
            }

            return CompanyCampaignStatus::CAMPAIGNS_OFF_NEVER_PURCHASED;
        }

        return CompanyCampaignStatus::NO_CAMPAIGNS;
    }

    /**
     * @return CompanySystemStatus
     */
    public function determineSystemStatus(): CompanySystemStatus
    {
        /** @var CompanyBillingServiceV4 $billingService */
        $billingService = app(CompanyBillingServiceV4::class);

        /** @var ComputedRejectionStatisticRepository $repository */
        $repository = app(ComputedRejectionStatisticRepository::class);

        if (!$billingService->companyHasValidPaymentMethod($this->id)) {
            return CompanySystemStatus::SUSPENDED_PAYMENT_METHOD;
        }

        if (Invoice::query()
            ->where(Invoice::FIELD_COMPANY_ID, $this->id)
            ->where(function (Builder $query) {
                $query->where(Invoice::FIELD_STATUS, InvoiceStates::FAILED->value)
                    ->orWhere(Invoice::FIELD_STATUS, InvoiceStates::COLLECTION->value)
                    ->orWhere(Invoice::FIELD_STATUS, InvoiceStates::CHARGEBACK->value);
            })
            ->first()) {
            return CompanySystemStatus::SUSPENDED_PAYMENT;
        }

        if ($repository->getCRMRejectionPercentageForCompanyAndProduct($this->id, Product::LEAD) >= config('sales.leads.crm_rejection_percentage_threshold')) {
            return CompanySystemStatus::SUSPENDED_CRM_REJECTION_THRESHOLD;
        }

        return CompanySystemStatus::ELIGIBLE;
    }

    /**
     * @return CompanyConsolidatedStatus
     */
    public function determineConsolidatedStatus(): CompanyConsolidatedStatus
    {
        if ($this->admin_status === CompanyAdminStatus::ADMIN_OVERRIDE) {
            return CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS;
        }

        if (
            $this->system_status === CompanySystemStatus::ELIGIBLE
            && $this->admin_status === CompanyAdminStatus::ADMIN_APPROVED
            && $this->campaign_status === CompanyCampaignStatus::CAMPAIGNS_ACTIVE
        ) {
            return CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS;
        }

        return CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CANNOT_RECEIVE_LEADS;
    }

    /**
     * @return Collection<CompanyAdminStatus>
     */
    public function getAdminStatusOptionsForUser(): Collection
    {
        /** @var User|null $user */
        $user = Auth::user();

        if (!$user) {
            return collect();
        }

        $options = collect();

        if (
            $user->hasRole(RoleType::SALES_MANAGER->value)
            || $user->is($this->accountManager)
            || $user->is($this->businessDevelopmentManager)
            || $user->is($this->onboardingManager))
        {
            $options = $options->merge([
                CompanyAdminStatus::ADMIN_APPROVED,
                CompanyAdminStatus::ADMIN_LOCKED,
                CompanyAdminStatus::ARCHIVED
            ]);
        }

        if ($user->hasRole(RoleType::FINANCE_MANAGER->value)) {
            $options = $options->merge([
                CompanyAdminStatus::COLLECTIONS,
                CompanyAdminStatus::ADMIN_OVERRIDE
            ]);
        }

        if ($user->hasRole(RoleType::ADMIN->value)) {
            $options = $options->merge(CompanyAdminStatus::cases());
        }

        return $options->unique();
    }

    /**
     * @param CompanyConsolidatedStatus $oldStatus
     * @param CompanyConsolidatedStatus $newStatus
     *
     * @return void
     */
    protected function logConsolidatedStatusChange(CompanyConsolidatedStatus $oldStatus, CompanyConsolidatedStatus $newStatus): void
    {
        /** @var CompanyChangeLogService $companyChangeLogService */
        $companyChangeLogService = app(CompanyChangeLogService::class);

        $companyChangeLogService->log(
            company: $this,
            log: CompanyChangeLogType::CONSOLIDATED_STATUS_CHANGE,
            payload: [
                self::FIELD_CONSOLIDATED_STATUS => [
                    'old' => $oldStatus,
                    'new' => $newStatus
                ],
                self::FIELD_ADMIN_STATUS => [
                    'old' => $this->getOriginal(self::FIELD_ADMIN_STATUS),
                    'new' => $this->admin_status
                ],
                self::FIELD_SYSTEM_STATUS => [
                    'old' => $this->getOriginal(self::FIELD_SYSTEM_STATUS),
                    'new' => $this->system_status
                ],
                self::FIELD_CAMPAIGN_STATUS => [
                    'old' => $this->getOriginal(self::FIELD_CAMPAIGN_STATUS),
                    'new' => $this->campaign_status
                ]
            ]);
    }
}
