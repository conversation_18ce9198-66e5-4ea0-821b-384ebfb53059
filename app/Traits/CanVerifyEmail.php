<?php

namespace App\Traits;

use App\Notifications\VerifyCompanyUserEmail;
use App\Services\Companies\CompanyService;
use Illuminate\Auth\MustVerifyEmail;

/**
 * Trait to extend <PERSON><PERSON>'s native MustVerifyEmail
 * Implement changes in each method below as required, to change behaviour of CompanyUser
 * without impacting User
 */
trait CanVerifyEmail
{
    use MustVerifyEmail {
        MustVerifyEmail::getEmailForVerification as private parentGetEmailForVerification;
        MustVerifyEmail::hasVerifiedEmail as private parentHasVerifiedEmail;
        MustVerifyEmail::markEmailAsVerified as private parentMarkEmailAsVerified;
        MustVerifyEmail::sendEmailVerificationNotification as private parentSendEmailVerificationNotification;
    }

    public function hasVerifiedEmail(): bool
    {
        return $this->parentHasVerifiedEmail();
    }

    public function markEmailAsVerified(): bool
    {
        return $this->parentMarkEmailAsVerified();
    }

    /**
     * Uses VerifyCompanyUserEmail Notification instead of parent notification
     * @return void
     */
    public function sendEmailVerificationNotification(): void
    {
        //disable email verification notification as we do not have proper landing pages and the link in the email will be admin-sr.com which is bad.
        //todo: re-enable when we have landing pages

//        /** @var CompanyService $companyService */
//        $companyService = app(CompanyService::class);
//
//        VerifyCompanyUserEmail::registerConfig($companyService->getRegistrationDomain($this->company), $this);
//
//        $this->notify(new VerifyCompanyUserEmail);
    }

    public function getEmailForVerification(): bool
    {
        return $this->parentGetEmailForVerification();
    }

}
