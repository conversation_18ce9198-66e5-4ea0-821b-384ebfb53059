<?php

namespace App\Traits;
use Illuminate\Database\Eloquent\Model;
use Ramsey\Uuid\Uuid as PackageUuid;

trait Uuid
{
    /**
     * @param $query
     * @param $uuid
     * @return mixed
     */
    public function scopeUuid($query, $uuid): mixed
    {
        return $query->where($this->getUuidName(), $uuid);
    }

    /**
     * @return string
     */
    public function getUuidName(): string
    {
        return property_exists($this, 'uuidName') ? $this->uuidName : 'uuid';
    }

    /**
     * @return void
     */
    protected static function boot(): void
    {
        parent::boot();

        static::creating(function ($model) {
            $model->{$model->getUuidName()} = $model->{$model->getUuidName()} ?? PackageUuid::uuid4()->toString();
        });
    }

    /**
     * @param $uuid
     * @return Model|null
     */
    public static function findByUuid($uuid): ?Model
    {
        return self::query()->where(self::FIELD_UUID, $uuid)->first();
    }
}
