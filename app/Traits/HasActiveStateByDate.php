<?php

namespace App\Traits;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

/**
 * @method static Builder active(?bool $isActive = true)
 */
trait HasActiveStateByDate
{
    /**
     * @return string
     */
    public function getActiveToColumn(): string
    {
        return self::FIELD_ACTIVE_TO;
    }

    /**
     * @return string
     */
    public function getActiveFromColumn(): string
    {
        return self::FIELD_ACTIVE_FROM;
    }

    /**
     * @return bool
     */
    public function isActive(): bool
    {
        $activeToField = $this->getActiveToColumn();

        return empty($this->{$activeToField}) || Carbon::parse($this->{$activeToField})->gt(now());
    }

    /**
     * @return bool
     */
    public function isInactive(): bool
    {
        return !$this->isActive();
    }

    /**
     * @param Builder $query
     * @param bool|null $isActive
     * @return Builder
     */
    public function scopeActive(Builder $query, ?bool $isActive = true): Builder
    {
        return $query->whereExists(function ($query) {
            $query->where(self::TABLE . '.' . $this->getActiveToColumn(), '=', null)
                ->orWhere(self::TABLE . '.' . $this->getActiveToColumn(), '>', now());
        }, 'and', !$isActive);
    }
}


