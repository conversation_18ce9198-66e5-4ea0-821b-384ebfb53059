<?php

namespace App\Services\CloudStorage;

use Carbon\Carbon;
use Exception;
use Google\Cloud\Storage\Bucket;
use Google\Cloud\Storage\ObjectIterator;
use Google\Cloud\Storage\StorageClient;
use Google\Cloud\Storage\StorageObject;
use Psr\Http\Message\StreamInterface;
use GuzzleHttp\Promise;

class GoogleCloudStorageService
{
    private StorageClient $client;
    private Bucket        $bucket;

    public function __construct()
    {
        $this->client = new StorageClient([
            'projectId' => config('services.google.storage.project_id'),
            'keyFile'   => json_decode(base64_decode(config('services.google.storage.service_account')), true)
        ]);
    }

    /**
     * @param string $name
     * @return Bucket
     */
    public function getBucketByName(string $name): Bucket
    {
        return $this->client->bucket($name);
    }

    /**
     * @param string $name
     * @return bool
     */
    public function setCurrentBucket(string $name): bool
    {
        $this->bucket = $this->getBucketByName($name);

        return true;
    }

    /**
     * @return Bucket|null
     */
    public function getCurrentBucket(): ?Bucket
    {
        return $this->bucket;
    }

    /**
     * @param string $name
     * @param $data
     * @return StorageObject
     * @throws Exception
     */
    public function upload(string $name, $data): StorageObject
    {
        $this->checkBucketSet();

        try {
            $object = $this->bucket->upload(
                $data,
                [
                    'name'      => $name,
                    'resumable' => true
                ]
            );
        } catch (Exception $e) {
            logger()->error(__METHOD__ . ": " . $e->getMessage());

            throw $e;
        }

        return $object;
    }

    /**
     * @param array $uploads
     *
     * $uploads is of the form:
     * [
     *      [
     *          'data' => resource|string,
     *          'name' => string,
     *          'options' => array
     *      ],
     *      ...
     * ]
     * @return array an array containing the async request responses.
     * The responses array is of the form:
     * [
     *      <upload name> => [
     *          'state' => either 'fulfilled' or 'rejected',
     *          'value' => StorageObject object
     *      ],
     *      ...
     * ]
     * @throws Exception
     * @see https://docs.guzzlephp.org/en/stable/quickstart.html#concurrent-requests
     * @see https://googleapis.github.io/google-cloud-php/#/docs/google-cloud/v0.185.0/storage/bucket?method=uploadAsync
     */
    public function uploadMultiple(array $uploads): array
    {
        $this->checkBucketSet();

        $promises = [];

        foreach ($uploads as $upload) {
            $upload['options']['name'] = $upload['name'];

            $promises[$upload['name']] = $this->bucket->uploadAsync($upload['data'], $upload['options'])->then(
                function (StorageObject $object) {
                    return $object;
                },
                function (Exception $e) {
                    logger()->error(__METHOD__ . ' ' . $e->getMessage());

                    throw $e;
                }
            );
        }

        return Promise\Utils::settle($promises)->wait();
    }

    /**
     * @param string $name
     * @return StorageObject
     * @throws Exception
     */
    public function getObject(string $name): StorageObject
    {
        $this->checkBucketSet();

        return $this->bucket->object($name);
    }

    /**
     * @param string $name
     * @return StreamInterface
     * @throws Exception
     */
    public function downloadObject(string $name): StreamInterface
    {
        $this->checkBucketSet();

        return $this->getObject($name)->downloadAsStream();
    }

    /**
     * @param string[] $names
     * @return array an array containing the async request responses.
     * The responses array is of the form:
     * [
     *      <object name> => [
     *          'state' => either 'fulfilled' or 'rejected',
     *          'value' => StreamInterface object
     *      ],
     *      ...
     * ]
     * @throws Exception
     * @see https://docs.guzzlephp.org/en/stable/quickstart.html#concurrent-requests
     * @see https://googleapis.github.io/google-cloud-php/#/docs/google-cloud/v0.185.0/storage/storageobject?method=downloadAsStreamAsync
     */
    public function downloadMultipleObjects(array $names): array
    {
        $this->checkBucketSet();

        $promises = [];

        foreach ($names as $name) {
            $promises[$name] = $this->bucket->object($name)->downloadAsStreamAsync()->then(
                function (StreamInterface $interface) {
                    return $interface;
                },
                function (Exception $e) {
                    logger()->error(__METHOD__ . ' ' . $e->getMessage());

                    throw $e;
                }
            );
        }

        return Promise\Utils::settle($promises)->wait();
    }

    /**
     * @return ObjectIterator
     * @throws Exception
     */
    public function getAllBucketObjects(): ObjectIterator
    {
        $this->checkBucketSet();

        return $this->bucket->objects();
    }

    /**
     * @param string $name
     * @return bool
     * @throws Exception
     */
    public function deleteObject(string $name): bool
    {
        $this->checkBucketSet();

        $this->getObject($name)->delete();

        return true;
    }

    /**
     * @param array $names
     * @return bool
     * @throws Exception
     */
    public function deleteMultipleObjects(array $names): bool
    {
        $this->checkBucketSet();

        foreach ($names as $name) {
            $this->deleteObject($name);
        }

        return true;
    }

    /**
     * @return void
     * @throws Exception
     */
    public function checkBucketSet(): void
    {
        if (!isset($this->bucket)) {
            throw new Exception(__METHOD__ . ": Bucket not set");
        }
    }

    /**
     * @param string $prefix
     * @param string $delimiter
     * @return ObjectIterator
     * @throws Exception
     */
    public function getBucketObjects(string $prefix, string $delimiter = '/'): ObjectIterator
    {
        $this->checkBucketSet();

        return $this->bucket->objects([
            'prefix'    => rtrim($prefix, '/') . '/',
            'delimiter' => $delimiter,
        ]);
    }

    /**
     * @param ObjectIterator $objects
     * @return array
     */
    public function getFilesFromObjects(ObjectIterator $objects): array
    {
        $files = [];

        foreach ($objects as $object) {
            $files[] = $object->name();
        }

        return $files;
    }

    /**
     * @param ObjectIterator $objects
     * @return array
     */
    public function getFoldersFromObjects(ObjectIterator $objects): array
    {
        return $objects->prefixes();
    }

    /**
     * @param string $bucketName
     * @param string $objectName
     * @param int $expirationInDays
     * @return string
     */
    public function generateSignedUrl(string $bucketName, ?string $objectName = null, int $expirationInDays = 1): ?string
    {
        if (empty($objectName)) {
            return null;
        }
        $expiration = Carbon::now()->add($expirationInDays, 'days')->toDateTime();

        $bucket = $this->client->bucket($bucketName);
        $object = $bucket->object($objectName);

        return $object->signedUrl($expiration, ['version' => 'v4']);
    }
}
