<?php

namespace App\Services\PrivacyManagement;

use App\Models\PrivacyRequest;
use App\Repositories\PrivacyManagement\PrivacyManagementRepository;
use Illuminate\Database\Eloquent\Builder;

class PrivacyManagementService
{

    public function __construct(
        protected PrivacyManagementRepository $privacyManagementRepository
    )
    {}

    /**
     * @param array $filters
     * @return Builder
     */
    public function getPrivacyRequests(array $filters): Builder
    {
        return $this->privacyManagementRepository->getPrivacyRequests(filters: $filters);
    }

    /**
     * @param int $requestId
     * @return PrivacyRequest
     */
    public function getPrivacyRequest(int $requestId): PrivacyRequest
    {
        return $this->privacyManagementRepository->getPrivacyRequest(id: $requestId);
    }

    /**
     * @param array $fields
     * @return bool
     */
    public function createPrivacyRequest(array $fields): bool
    {
        return $this->privacyManagementRepository->createPrivacyRequest($fields);
    }

    /**
     * @param array $fields
     * @return bool
     */
    public function updatePrivacyRequest(PrivacyRequest $privacyRequest, array $fields): bool
    {
        return $this->privacyManagementRepository->updatePrivacyRequest($privacyRequest, $fields);
    }
}
