<?php

namespace App\Services;

use App\Campaigns\Modules\LocationModule;
use App\Enums\Campaigns\CampaignStatus;
use App\Enums\CompanyConsolidatedStatus;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModule;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\Company;
use App\Repositories\Legacy\NonPurchasingCompanyLocationRepository;
use Illuminate\Support\Collection;

class CompanyServiceAreaService
{

    /**
     * @param Company $company
     * @return Collection<int, Location>
     */
    public function getServicedStates(Company $company): Collection
    {
        $activeStateLocations        = $this->getAllActiveLocations($company);
        $nonPurchasingStateLocations = $this->getAllNonPurchasingStateLocations($company);
        return $activeStateLocations->merge($nonPurchasingStateLocations)->unique(Location::STATE_KEY);
    }

    /**
     * @param Company $company
     * @return Collection<int, Location>
     */
    private function getAllActiveLocations(Company $company): Collection
    {
        $company->load([
            Company::RELATION_FUTURE_CAMPAIGNS . '.' .
            CompanyCampaign::RELATION_LOCATION_MODULE . '.' .
            CompanyCampaignLocationModule::RELATION_LOCATIONS . '.' .
            CompanyCampaignLocationModuleLocation::RELATION_LOCATION
        ]);

        $locations = collect();
        if ($company->consolidated_status === CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS) {
            $locations = $company->futureCampaigns->where(CompanyCampaign::FIELD_STATUS, CampaignStatus::ACTIVE)->map(function (CompanyCampaign $campaign) use ($locations) {
                return $campaign->locationModule->locations;
            });
        }

        return $locations->flatten()->map(fn(CompanyCampaignLocationModuleLocation $campaignLocation) => $campaignLocation->location)->unique(Location::STATE_KEY);
    }

    /**
     * @param Company $company
     * @return Collection<int, Location>
     */
    private function getAllNonPurchasingStateLocations(Company $company): Collection
    {
        /** @var NonPurchasingCompanyLocationRepository $repository */
        $repository = app(NonPurchasingCompanyLocationRepository::class);
        return $repository->getStateLocationsByCompany($company);
    }
}
