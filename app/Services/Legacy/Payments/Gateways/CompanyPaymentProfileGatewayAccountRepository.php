<?php

namespace App\Services\Legacy\Payments\Gateways;

use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentCompanyPaymentProfile;

class CompanyPaymentProfileGatewayAccountRepository extends BaseGatewayAccountRepository
{
    public function __construct(string $gateway, EloquentCompany $company, protected ?EloquentCompanyPaymentProfile $companyPaymentProfile = null)
    {
        parent::__construct($gateway, $company);
    }

    public function getAccountId(): ?string
    {
        return $this->companyPaymentProfile?->payment_provider_identifier;
    }

    public function getDefaultPaymentMethodId(): ?string
    {
        return null;
    }

    public function setDefaultPaymentMethodId(): ?string
    {
        // TODO: Implement setDefaultPaymentMethodId() method.
    }

    public function setAccountId(string $id): void
    {
        /** @var EloquentCompanyPaymentProfile $profile */
        $profile = $this->company->companyPaymentProfiles()->updateOrCreate([
            EloquentCompanyPaymentProfile::PROVIDER_CODE => $this->getGatewayId(),
        ], [
            EloquentCompanyPaymentProfile::PAYMENT_PROVIDER_IDENTIFIER => $id,
        ]);

        $this->companyPaymentProfile = $profile;
    }
}
