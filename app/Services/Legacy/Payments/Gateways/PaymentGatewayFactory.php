<?php

namespace App\Services\Legacy\Payments\Gateways;

use App\Contracts\Legacy\Payments\PaymentGatewayContract;
use App\Services\Legacy\Exceptions\PaymentException;
use App\Services\Legacy\Payments\Credentials\StripeCredentialService;

class PaymentGatewayFactory
{
    public function __construct(
        protected PaymentGatewayAccountRepositoryFactory $paymentGatewayAccountRepositoryFactory,
        protected StripeCredentialService                $stripeCredentialService
    ) {}

    /**
     * @param string $name
     * @return PaymentGatewayContract
     * @throws PaymentException
     */
    public function get(string $name): PaymentGatewayContract
    {
        if (!PaymentGateways::exists($name))
            throw new PaymentException("Unsupported payment gateway {$name}");

        $gatewayAccount = $this->paymentGatewayAccountRepositoryFactory->get($name);

        return match ($name) {
            PaymentGateways::SHORT_CODE_STRIPE => new StripePaymentGateway(
                $this->stripeCredentialService->getSecretKey(),
                $this->stripeCredentialService->getPublishableKey(),
                $gatewayAccount
            ),
            default => throw new PaymentException("Could not determine payment gateway type."),
        };
    }
}
