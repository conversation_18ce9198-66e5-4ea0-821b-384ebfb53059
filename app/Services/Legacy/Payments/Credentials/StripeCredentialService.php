<?php

namespace App\Services\Legacy\Payments\Credentials;

class StripeCredentialService
{
    public function getPublishableKey(): string
    {
        return config('services.payment_gateways.stripe.gateway_mode')
            ? config('services.payment_gateways.stripe.api_key_publishable_live')
            : config('services.payment_gateways.stripe.api_key_publishable_test');
    }

    public function getSecretKey(): string
    {
        return config('services.payment_gateways.stripe.gateway_mode')
            ? config('services.payment_gateways.stripe.api_key_secret_live')
            : config('services.payment_gateways.stripe.api_key_secret_test');
    }
}
