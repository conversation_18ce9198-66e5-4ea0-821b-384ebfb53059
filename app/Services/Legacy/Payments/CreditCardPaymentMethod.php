<?php

namespace App\Services\Legacy\Payments;

use App\Contracts\Legacy\Payments\CreditCardPaymentMethodContract;
use App\Contracts\Legacy\Payments\PaymentGatewayContract;

class CreditCardPaymentMethod implements CreditCardPaymentMethodContract
{
    protected string  $name = "";
    protected ?string $company;
    protected ?string $address1;
    protected ?string $address2;
    protected ?string $city;
    protected ?string $state;
    protected ?string $zipCode;
    protected ?string $country;
    protected bool      $default;

    public function __construct(
        protected PaymentGatewayContract $gateway,
        protected string                 $id,
        protected string                 $cardNumber,
        protected string                 $brand,
        protected string                 $expiryMonth,
        protected string                 $expiryYear,
        protected string                 $type,
        protected mixed                  $status = null
    ) {}

    public function id(): string
    {
        return $this->id;
    }

    public function maskedNumber(): string
    {
        return $this->cardNumber;
    }

    public function brand(): string
    {
        return $this->brand;
    }

    public function type(): string
    {
        return $this->type;
    }

    public function expiry(): string
    {
        return $this->expiryMonth() . '/' . $this->expiryYear();
    }

    public function expiryMonth(): string
    {
        return $this->expiryMonth;
    }

    public function expiryYear(): string
    {
        return $this->expiryYear;
    }

    public function setExpiryMonth(string $month): CreditCardPaymentMethodContract
    {
        $this->expiryMonth = str_pad($month, 2, '0', STR_PAD_LEFT);

        return $this;
    }

    public function setExpiryYear(string $year): CreditCardPaymentMethodContract
    {
        $this->expiryYear = $year;

        return $this;
    }

    public function name(): string
    {
        return $this->name;
    }

    public function firstName(): string
    {
        return explode(" ", $this->name)[0];
    }

    public function lastName(): string
    {
        $exploded = explode(" ", $this->name);
        array_shift($exploded);

        return count($exploded) > 0 ? join(" ", $exploded) : "";
    }

    public function setName(string $name): CreditCardPaymentMethodContract
    {
        $this->name = $name;

        return $this;
    }

    public function company(): ?string
    {
        return $this->company;
    }

    public function setCompany(?string $company): CreditCardPaymentMethodContract
    {
        $this->company = $company;

        return $this;
    }

    public function addressLineOne(): ?string
    {
        return $this->address1;
    }

    public function setAddressLineOne(?string $lineOne): CreditCardPaymentMethodContract
    {
        $this->address1 = $lineOne;

        return $this;
    }

    public function addressLineTwo(): ?string
    {
        return $this->address2;
    }

    public function setAddressLineTwo(?string $lineTwo): CreditCardPaymentMethodContract
    {
        $this->address2 = $lineTwo;

        return $this;
    }

    public function city(): ?string
    {
        return $this->city;
    }

    public function setCity(?string $city): CreditCardPaymentMethodContract
    {
        $this->city = $city;

        return $this;
    }

    public function state(): ?string
    {
        return $this->state;
    }

    public function setState(?string $state): CreditCardPaymentMethodContract
    {
        $this->state = $state;

        return $this;
    }

    public function zipCode(): ?string
    {
        return $this->zipCode;
    }

    public function setZipCode(?string $zipCode): CreditCardPaymentMethodContract
    {
        $this->zipCode = $zipCode;

        return $this;
    }

    public function country(): ?string
    {
        return $this->country;
    }

    public function setCountry(?string $country): CreditCardPaymentMethodContract
    {
        $this->country = $country;

        return $this;
    }

    public function paymentGateway(): PaymentGatewayContract
    {
        return $this->paymentGateway();
    }

    public function save(): void
    {
        $this->paymentGateway()->updatePaymentMethod($this);
    }

    public function default(): bool
    {
        return $this->default;
    }

    public function setDefault(bool $default = true): self
    {
        $this->default = $default;

        return $this;
    }
}
