<?php

namespace App\Services\Legacy\Payments;

use App\Models\Odin\Company;
use App\Services\Legacy\Payments\Gateways\PaymentGateways;
use App\Services\Legacy\Payments\Gateways\StripePaymentGateway;
use Stripe\Exception\ApiErrorException;

class CompanyBillingService
{
    public function addPaymentMethod(Company $company, string $email, string $token, string $type): array
    {
        /** @var StripePaymentGateway|null $gateway */
        $gateway = $company->paymentGateways()?->get(PaymentGateways::SHORT_CODE_STRIPE);
        if (!$gateway)
            return ["success" => false, "message" => "No gateway for company"];

        if ($gateway->hasAccount()) {
            try {
                $gateway->addCard($token);
            } catch (\Exception $e) {
                return ["success" => false, "message" => $e->getMessage()];
            }
        } else {
            try {
                $gateway->createCustomer($email, $token);
            } catch (\Exception $e) {
                return ["success" => false, "message" => $e->getMessage()];
            }
        }

        return ["success" => true, "message" => ""];
    }

    public function deletePaymentMethod(Company $company, string $id): bool
    {
        /** @var StripePaymentGateway|null $gateway */
        $gateway = $company->paymentGateways()?->get(PaymentGateways::SHORT_CODE_STRIPE);
        if (!$gateway)
            return false;

        try {
            $gateway->deletePaymentMethod($id);
        } catch (\Exception $e) {
            logger()->channel('billing')->error($e);
            return false;
        }

        return true;
    }

    public function makePaymentMethodPrimary(Company $company, string $id): bool
    {
        /** @var StripePaymentGateway|null $gateway */
        $gateway = $company->paymentGateways()?->get(PaymentGateways::SHORT_CODE_STRIPE);
        if (!$gateway)
            return false;

        try {
            $gateway->setDefaultPaymentMethod($id);
        } catch (\Exception $e) {
            return false;
        }

        return true;
    }


    /**
     * @throws ApiErrorException
     */
    public function makeRefundRequest(
        Company $company,
        string $chargeId,
        float $totalInDollars,
        array $meta = []
    ): string
    {
        /** @var StripePaymentGateway|null $gateway */
        $gateway = $company->paymentGateways()?->get(PaymentGateways::SHORT_CODE_STRIPE);

        if (!$gateway)
            return false;

        return $gateway->makeRefundRequest(
            totalInCents: $totalInDollars * 100,
            chargeId    : $chargeId,
            meta        : $meta
        );
    }

    public function getPaymentMethodsCount(Company $company): int
    {
        /** @var StripePaymentGateway|null $gateway */
        $gateway = $company->paymentGateways()?->get(PaymentGateways::SHORT_CODE_STRIPE);

        if (!$gateway) {
            return false;
        }

        return $gateway->getPaymentMethodsCount();
    }

}
