<?php

namespace App\Services\Legacy\Payments;

use App\Contracts\Legacy\Payments\CreditCardPaymentMethodContract;
use App\Contracts\Legacy\Payments\PaymentMethodAggregatorContract;
use App\Services\Legacy\Exceptions\PaymentException;
use App\Services\Legacy\Payments\Gateways\PaymentGatewayFactory;
use App\Services\Legacy\Payments\Gateways\PaymentGateways;
use Illuminate\Support\Collection;

class PaymentMethodAggregator implements PaymentMethodAggregatorContract
{
    public function __construct(protected PaymentGatewayFactory $gatewayFactory) {}

    public function get(string $gateway, string $id): ?CreditCardPaymentMethodContract
    {
        return collect(
            $this->gatewayFactory
                ->get($gateway)
                ->paymentMethods()
        )->get($id);
    }

    public function all(): array
    {
        $paymentMethods = collect();

        foreach (PaymentGateways::all() as $paymentGatewayId) {
            $paymentGateway = $this->gatewayFactory->get($paymentGatewayId);

            if (!$paymentGateway->hasAccount()) {
                continue;
            }

            try {
                $gatewayPaymentMethods = collect($paymentGateway->paymentMethods());
                $paymentMethods = $paymentMethods->isEmpty()
                    ? $gatewayPaymentMethods
                    : $paymentMethods->merge($gatewayPaymentMethods);
            } catch (PaymentException $exception) {
                continue;
            }
        }

        return $paymentMethods->toArray();
    }

    public function delete(string $gateway, string $id): void
    {
        $this->gatewayFactory->get($gateway)->deletePaymentMethod($id);
    }

    public function preferred(): array
    {
        foreach (PaymentGateways::all() as $paymentGatewayId) {
            $paymentMethods = $this->getOrderedPaymentMethodsForGateway($paymentGatewayId);

            if (!$paymentMethods->isEmpty()) {
                return $paymentMethods->toArray();
            }
        }
        return [];
    }

    /**
     * @param string $paymentGatewayId
     * @return Collection
     * @throws PaymentException
     */
    private function getOrderedPaymentMethodsForGateway(string $paymentGatewayId): Collection
    {
        $orderedPaymentMethods = collect();
        $paymentGateway = $this->gatewayFactory->get($paymentGatewayId);

        if (!$paymentGateway->hasAccount()) {
            return $orderedPaymentMethods;
        }
        $unorderedPaymentMethods = collect($paymentGateway->paymentMethods());

        // assign the remaining payment methods in whatever order they are returned
        /** @var CreditCardPaymentMethodContract $paymentMethod */
        foreach ($unorderedPaymentMethods as $index => $paymentMethod) {
            if (!$paymentMethod->default()) {
                $orderedPaymentMethods->push($paymentMethod);
            } else {
                $orderedPaymentMethods->prepend($paymentMethod);
            }
        }

        return $orderedPaymentMethods;
    }

    public function default(): ?CreditCardPaymentMethodContract
    {
        return collect($this->preferred())->first();
    }

    public function exists(): bool
    {
        try {
            return !collect($this->all())->isEmpty();
        } catch (PaymentException $exception) {
            return false;
        }
    }
}
