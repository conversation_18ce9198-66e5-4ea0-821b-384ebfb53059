<?php

namespace App\Services\Legacy;

use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentUser;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;

/**
 * Class ClientUserToken
 * @package App\Services\Client
 */
class ClientUserToken
{
    const DEFAULT_EXPIRE_IN_SECONDS = 8 * 60 * 60; // in seconds / 8 hours

    // user
    const PAYLOAD_USER_ID = 'uid';
    const PAYLOAD_USER_NAME = 'uname';
    const PAYLOAD_USER_EMAIL = 'uemail';
    const PAYLOAD_USER_COMPANY_ID = 'ucid';
    const PAYLOAD_USER_COMPANY_NAME = 'ucname';

    // admin
    const PAYLOAD_ADMIN_ID = 'auid';
    const PAYLOAD_ADMIN_USER_NAME = 'auname';
    const PAYLOAD_ADMIN_USER_EMAIL = 'auemail';

    // metadata
    const PAYLOAD_ISSUED_AT = 'iat';
    const PAYLOAD_EXPIRATION_TIME = 'exp';
    const PAYLOAD_DATA = 'data';  // Option data

    const PAYLOAD_REGISTRATION_REFERENCE = 'registration_reference';

    /** @var int $userId user id */
    protected $userId;

    /** @var int $adminId admin id incase shadow, default is 0 */
    protected $adminId = 0;

    /** @var int $issuedAt issue at timestamp */
    protected $issuedAt;

    /** @var int $expirationTime expire at timestamp */
    protected $expirationTime;

    /** @var array $data optional data */
    protected $data = [];

    /** @var string|null $registrationReference */
    protected $registrationReference = null;

    /**
     * @return array
     * @throws BindingResolutionException|Exception
     */
    public function payload()
    {
        if(!$this->userId) {
            throw new Exception("Token does not contain a user id.");
        }

        $payload = [
            self::PAYLOAD_USER_ID => $this->userId,
            self::PAYLOAD_ISSUED_AT => $this->issuedAt,
            self::PAYLOAD_EXPIRATION_TIME => $this->expirationTime,
            self::PAYLOAD_DATA => $this->data,
        ];

        $user = EloquentUser::query()
            ->with(EloquentUser::RELATION_COMPANY)
            ->where(EloquentUser::USER_ID, $this->userId)
            ->first();

        if($user) {
            $payload[self::PAYLOAD_USER_NAME] = $user->{EloquentUser::FIRST_NAME} . ' ' . $user->{EloquentUser::LAST_NAME};
            $payload[self::PAYLOAD_USER_EMAIL] = $user->{EloquentUser::EMAIL};
            $payload[self::PAYLOAD_USER_COMPANY_ID] = $user->{EloquentUser::COMPANY_ID};

            if($user->{EloquentUser::RELATION_COMPANY}) {
                $payload[self::PAYLOAD_USER_COMPANY_NAME] = $user->{EloquentUser::RELATION_COMPANY}->{EloquentCompany::COMPANY_NAME};
            }
        }

        if($this->adminId) {
            $adminUser = EloquentUser::query()->find($this->adminId);

            if($adminUser) {
                $payload[self::PAYLOAD_ADMIN_ID] = $this->adminId;
                $payload[self::PAYLOAD_ADMIN_USER_NAME] = $adminUser->{EloquentUser::FIRST_NAME} . ' ' . $adminUser->{EloquentUser::LAST_NAME};
                $payload[self::PAYLOAD_ADMIN_USER_EMAIL] = $adminUser->{EloquentUser::EMAIL};
            }
        }

        if ($this->registrationReference) {
            $payload[self::PAYLOAD_REGISTRATION_REFERENCE] = $this->registrationReference;
        }

        return $payload;
    }

    /**
     * Create instance from payload
     *
     * @param array $payload
     * @return ClientUserToken
     */
    public static function fromPayload(array $payload): self
    {
        $token = new self();

        $token->userId = $payload[self::PAYLOAD_USER_ID] ?? null;
        $token->adminId = $payload[self::PAYLOAD_ADMIN_ID] ?? null;
        $token->issuedAt = $payload[self::PAYLOAD_ISSUED_AT] ?? null;
        $token->expirationTime = $payload[self::PAYLOAD_EXPIRATION_TIME] ?? null;
        $token->data = $payload[self::PAYLOAD_DATA] ?? null;
        $token->registrationReference = $payload[self::PAYLOAD_REGISTRATION_REFERENCE] ?? null;

        return $token;
    }

    /**
     * @return int
     */
    public function getUserId()
    {
        return $this->userId;
    }

    /**
     * @return int
     */
    public function getAdminId()
    {
        return $this->adminId;
    }

    /**
     * @return string|null
     */
    public function getRegistrationReference()
    {
        return $this->registrationReference;
    }

    /**
     * @return int
     */
    public function getIssuedAt()
    {
        return $this->issuedAt;
    }

    /**
     * @return int
     */
    public function getExpirationTime()
    {
        return $this->expirationTime;
    }

    /**
     * @return array
     */
    public function getData()
    {
        return $this->data;
    }

    /**
     * @param int $user
     * @return $this
     */
    public function setUser($user)
    {
        $this->userId = $user;
        return $this;
    }

    /**
     * @param string $registrationReference
     *
     * @return $this
     */
    public function setRegistrationReference(string $registrationReference)
    {
        $this->registrationReference = $registrationReference;
        return $this;
    }

    /**
     * @param int $adminUserId
     * @return $this
     */
    public function setAdminUser($adminUserId)
    {
        $this->adminId = $adminUserId;
        return $this;
    }

    /**
     * Generate issued at
     *
     * @return $this
     */
    public function setIssuedAt()
    {
        $this->issuedAt = Carbon::now()->timestamp;
        return $this;
    }

    /**
     * Generate expiration time
     *
     * @param int $seconds
     * @return $this
     */
    public function setExpirationTime($seconds = self::DEFAULT_EXPIRE_IN_SECONDS)
    {
        if (!$this->issuedAt) {
            $this->setIssuedAt();
        }
        $this->expirationTime = Carbon::createFromTimestamp($this->issuedAt)->addSeconds($seconds)->timestamp;
        return $this;
    }

    /**
     * @param array $data
     * @return $this
     */
    public function setData($data)
    {
        $this->data = $data;
        return $this;
    }

    /**
     * @return bool
     */
    public function isAdminAsShadow()
    {
        // check current user is admin if login shadow user
        return $this->adminId > 0;
    }
}
