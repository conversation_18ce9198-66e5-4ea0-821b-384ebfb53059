<?php

namespace App\Services\Legacy;

use App\Models\Legacy\Industry;
use App\Models\Odin\IndustryService;
use Illuminate\Support\Collection;

class ReferenceListsService
{
    /**
     * @return mixed
     */
    public function industries(): mixed
    {
        return Industry::select(
            Industry::FIELD_ID,
            Industry::FIELD_KEY,
            Industry::FIELD_DISPLAY_NAME
        )->get();
    }
}
