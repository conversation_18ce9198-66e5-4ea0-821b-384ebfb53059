<?php

namespace App\Services\Legacy;

use App\Contracts\Services\LeadPriceServiceContract;
use App\Enums\Odin\Industry;

class LeadPriceService implements LeadPriceServiceContract
{
    const API_BASE_ENDPOINT                                          = '/services/lead-prices';
    const API_LEAD_PRICE_RANGE_SALES_TYPES_DEFAULT_CAMPAIGN_ENDPOINT = self::API_BASE_ENDPOINT . '/price-range-sales-type-default-campaign';

    const REQUEST_COMPANY_REFERENCE = 'company_reference';
    const REQUEST_LEAD_INDUSTRY     = 'lead_industry';

    public function __construct(protected APIConsumer $apiConsumer)
    {}

    /**
     * @inheritDoc
     */
    public function getPriceRangeSalesTypeForDefaultCampaign(string $companyReference, Industry $industry): array
    {
        return $this->apiConsumer
            ->post(
            self::API_LEAD_PRICE_RANGE_SALES_TYPES_DEFAULT_CAMPAIGN_ENDPOINT,
                [
                    self::REQUEST_COMPANY_REFERENCE => $companyReference,
                    self::REQUEST_LEAD_INDUSTRY     => strtolower($industry->value)
                ]
            )->json(APIConsumer::RESPONSE_RESULT);
    }
}
