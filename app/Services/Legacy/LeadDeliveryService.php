<?php

namespace App\Services\Legacy;

use App\Contracts\Services\LeadDeliveryServiceContract;
use App\Enums\HttpMethod;
use App\Jobs\RecordMonitoringLog;
use App\Jobs\SendLegacyAdminRequest;
use App\Models\Legacy\EloquentQuote;
use App\Services\Delivery\Email;
use Exception;
use Illuminate\Foundation\Bus\PendingDispatch;
use Illuminate\Mail\Message;
use Illuminate\Support\Facades\Mail;

class LeadDeliveryService implements LeadDeliveryServiceContract
{
    const API_BASE_ENDPOINT                                            = '/services/lead-delivery';
    const API_SAVE_AND_DELIVERY_QUOTE_COMPANY_ENDPOINT                 = self::API_BASE_ENDPOINT . '/save-and-deliver-quote-company';
    const API_SAVE_AND_DELIVER_QUOTE_COMPANY_FOR_BOUNCE_BACK_ENDPOINT  = self::API_BASE_ENDPOINT . '/save-and-deliver-quote-company-for-bounce-back';
    const API_SAVE_QUOTE_COMPANY_FOR_APPOINTMENT                       = self::API_BASE_ENDPOINT . '/save-quote-company-for-appointment';
    const API_SAVE_QUOTE_COMPANY_FOR_RESCHEDULED_APPOINTMENT           = self::API_BASE_ENDPOINT . '/save-quote-company-for-rescheduled-appointment';
    const API_SAVE_QUOTE_LOG_FOR_APPOINTMENT                           = self::API_BASE_ENDPOINT . '/save-quote-log-for-appointment';
    const API_DEMOTE_APPOINTMENT_QUOTE_COMPANY_TO_LEAD                 = self::API_BASE_ENDPOINT . '/demote-appointment-quote-company-to-lead';
    const API_UPDATE_APPOINTMENT_LEAD_QUOTE_COMPANY_PRICES             = self::API_BASE_ENDPOINT . '/update-appointment-lead-quote-company-prices';

    /** @var APIConsumer $apiConsumer */
    protected APIConsumer $apiConsumer;

    /**
     * @param APIConsumer $apiConsumer
     */
    public function __construct(APIConsumer $apiConsumer)
    {
        $this->apiConsumer = $apiConsumer;
    }

    /**
     * @inheritDoc
     */
    public function saveAndDeliverQuoteCompany(
        string      $leadReference,
        array       $selectedQuoteCompanies,
        ?int        $legacyUserId = null,
        int|bool    $sendAlertCustomer = false,
        string|null $leadProcessing = null,
        array       $ignoreQuoteCompanies = [],
        bool        $offHourSale = false
    ): array
    {
        try {
            return $this->apiConsumer->post(
                self::API_SAVE_AND_DELIVERY_QUOTE_COMPANY_ENDPOINT,
                compact("leadReference", "selectedQuoteCompanies", "legacyUserId", "sendAlertCustomer", "leadProcessing", "ignoreQuoteCompanies", "offHourSale"),
                120
            )->json(APIConsumer::RESPONSE_RESULT);
        }
        catch(Exception $e) {
            RecordMonitoringLog::dispatch(
                "Error delivering quote companies",
                compact("leadReference", "selectedQuoteCompanies")
            );

            $quoteId = EloquentQuote::where(EloquentQuote::REFERENCE, $leadReference)->firstOrFail()->{EloquentQuote::ID};
            $companyIds = implode(', ', array_column($selectedQuoteCompanies, "companyid"));

            Mail::raw("Error delivering quote companies for {$quoteId} to {$companyIds}", function(Message $message) {
                $message
                    ->subject("ERROR: Failure delivering quote companies")
                    ->to(explode(',', config('app.outgoing_communication.delivery_failure_notification_emails')));
            });

            throw $e;
        }
    }

    /**
     * @inheritDoc
     */
    public function saveAndDeliverQuoteCompanyForBounceBack(
        string      $leadReference,
        array       $selectedQuoteCompanies,
        ?int        $legacyUserId = null,
        int|bool    $sendAlertCustomer = false,
        string|null $leadProcessing = null,
        array       $ignoreQuoteCompanies = []
    ): array
    {
        return $this->apiConsumer->post(
            self::API_SAVE_AND_DELIVER_QUOTE_COMPANY_FOR_BOUNCE_BACK_ENDPOINT,
            compact("leadReference", "selectedQuoteCompanies", "legacyUserId", "sendAlertCustomer", "leadProcessing", "ignoreQuoteCompanies"),
            120
        )->json(APIConsumer::RESPONSE_RESULT);
    }

    /** @inheritDoc */
    public function saveQuoteCompanyForAppointment(
        string $leadReference,
        string $companyReference,
        int $leadCampaignId,
        float $cost,
        string $soldStatus,
        string $chargeStatus,
        bool $chargeable,
        bool $delivered,
        bool $includeInBudget,
        int $rejectionExpiry,
        int $deliveredAt,
        int $rejectedAt,
        int $legacyUserId,
        int $legacySaleTypeId,
        int $quoteCompanyId = 0,
        bool $async = false
    ): int|PendingDispatch
    {
        $params = compact(
            'leadReference',
            'companyReference',
            'leadCampaignId',
            'cost',
            'soldStatus',
            'chargeStatus',
            'chargeable',
            'delivered',
            'includeInBudget',
            'rejectionExpiry',
            'deliveredAt',
            'rejectedAt',
            'legacyUserId',
            'quoteCompanyId',
            'legacySaleTypeId'
        );

        if($async) {
            return SendLegacyAdminRequest::dispatch(
                HttpMethod::METHOD_POST,
                self::API_SAVE_QUOTE_COMPANY_FOR_APPOINTMENT,
                $params
            );
        }
        else {
            return $this->apiConsumer->post(
                self::API_SAVE_QUOTE_COMPANY_FOR_APPOINTMENT,
                $params,
                60
            )->json(APIConsumer::RESPONSE_RESULT);
        }
    }

    /**
     * @param int $quoteCompanyId
     * @param int $rescheduledQuoteCompanyId
     * @param int $legacyUserId
     * @param string $rejectionNotes
     * @param int $deliveredAt
     * @param int $rejectionExpiry
     * @param bool $async
     * @return bool|PendingDispatch
     * @throws Exception
     */
    public function saveRescheduledAppointmentQuoteCompany(
        int $quoteCompanyId,
        int $rescheduledQuoteCompanyId,
        int $legacyUserId,
        string $rejectionNotes,
        int $deliveredAt = 0,
        int $rejectionExpiry = 0,
        bool $async = false
    ): int|PendingDispatch
    {
        $params = compact(
            'quoteCompanyId',
            'rescheduledQuoteCompanyId',
            'rejectionNotes',
            'legacyUserId',
            'deliveredAt',
            'rejectionExpiry'
        );

        if($async) {
            return SendLegacyAdminRequest::dispatch(
                HttpMethod::METHOD_POST,
                self::API_SAVE_QUOTE_COMPANY_FOR_RESCHEDULED_APPOINTMENT,
                $params
            );
        }
        else {
            return $this->apiConsumer->post(
                self::API_SAVE_QUOTE_COMPANY_FOR_RESCHEDULED_APPOINTMENT,
                $params,
                60
            )->json(APIConsumer::RESPONSE_RESULT);
        }
    }

    /**
     * @param int $quoteCompanyId
     * @param float $leadPrice
     * @param int $legacySaleTypeId
     * @param int $leadCampaignId
     * @param int $legacyUserId
     * @param string $processingScenario
     * @param bool $canReject
     * @param bool $isRejection
     * @param int|null $rejectionExpiry
     * @param string|null $rejectionNotes
     * @param bool $async
     * @return bool|PendingDispatch
     * @throws Exception
     */
    public function demoteAppointmentQuoteCompanyToLead(
        int $quoteCompanyId,
        float $leadPrice,
        int $legacySaleTypeId,
        int $leadCampaignId,
        int $legacyUserId,
        string $processingScenario,
        bool $canReject,
        bool $isRejection,
        ?int $rejectionExpiry = null,
        ?string $rejectionNotes = null,
        bool $async = false
    ): int|PendingDispatch
    {
        $params = compact(
            'quoteCompanyId',
            'leadPrice',
            'leadCampaignId',
            'canReject',
            'isRejection',
            'rejectionNotes',
            'rejectionExpiry',
            'legacyUserId',
            'processingScenario',
            'legacySaleTypeId'
        );

        if($async) {
            return SendLegacyAdminRequest::dispatch(
                HttpMethod::METHOD_PATCH,
                LeadDeliveryService::API_DEMOTE_APPOINTMENT_QUOTE_COMPANY_TO_LEAD,
                $params
            );
        }
        else {
            return $this->apiConsumer->patch(
                self::API_DEMOTE_APPOINTMENT_QUOTE_COMPANY_TO_LEAD,
                $params,
                60
            )->json(APIConsumer::RESPONSE_RESULT);
        }
    }

    /**
     * @param string $leadReference
     * @param int $legacyUserId
     * @param string $processingScenario
     * @param bool $async
     * @return bool|PendingDispatch
     * @throws Exception
     */
    public function saveQuoteLogForAppointment(
        string $leadReference,
        int $legacyUserId,
        string $processingScenario,
        bool $async = false
    ): bool|PendingDispatch
    {
        $params = compact(
            'leadReference',
            'legacyUserId',
            'processingScenario'
        );

        if($async) {
            return SendLegacyAdminRequest::dispatch(
                HttpMethod::METHOD_POST,
                self::API_SAVE_QUOTE_LOG_FOR_APPOINTMENT,
                $params
            );
        }
        else {
            return $this->apiConsumer->post(
                self::API_SAVE_QUOTE_LOG_FOR_APPOINTMENT,
                $params,
                60
            )->json(APIConsumer::RESPONSE_RESULT);
        }
    }

    /**
     * @param array $quoteCompanyPrices
     * @param int $legacySaleTypeId
     * @param bool $async
     * @return bool|PendingDispatch
     * @throws Exception
     */
    public function updateAppointmentLeadQuoteCompanyPrices(
        array $quoteCompanyPrices,
        int $legacySaleTypeId,
        bool $async = false
    ): bool|PendingDispatch
    {
        $params = compact(
            'quoteCompanyPrices',
            'legacySaleTypeId'
        );

        if($async) {
            return SendLegacyAdminRequest::dispatch(
                HttpMethod::METHOD_PATCH,
                self::API_UPDATE_APPOINTMENT_LEAD_QUOTE_COMPANY_PRICES,
                $params
            );
        }
        else {
            return $this->apiConsumer->patch(
                self::API_UPDATE_APPOINTMENT_LEAD_QUOTE_COMPANY_PRICES,
                $params,
                60
            )->json(APIConsumer::RESPONSE_RESULT);
        }
    }
}
