<?php

namespace App\Services\Legacy;

use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class QuoteCompanyCalculationsService
{
    /**
     * @param int $companyId
     * @param int $timestampBudgetStart
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function getBudgetSpentQuery(
        int $companyId,
        int $timestampBudgetStart
    ): \Illuminate\Database\Eloquent\Builder
    {
        return EloquentQuoteCompany::query()
            ->select([
                DB::raw("COUNT(cost) as `total_count_spent`"),
                DB::raw("SUM(cost) as `total_cost_spent`"),
            ])
            ->where(EloquentQuoteCompany::CHARGEABLE, true)
            ->where(EloquentQuoteCompany::DELIVERED, true)
            ->where(EloquentQuoteCompany::INCLUDE_IN_BUDGET, true)
            ->where(EloquentQuoteCompany::COMPANY_ID, $companyId)
            ->where(EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY, '>=', $timestampBudgetStart);
    }

    /**
     * @param int $companyId
     * @param array $leadSalesTypeConfigurationIds
     * @param int $timestampBudgetStart
     * @param bool $isDefaultCampaign
     * @return array|null
     */
    public function getBudgetSpentByCompanyListSalesTypes(
        int $companyId,
        array $leadSalesTypeConfigurationIds,
        int $timestampBudgetStart,
        bool $isDefaultCampaign
    ): ?array
    {
        $query = $this->getBudgetSpentQuery($companyId, $timestampBudgetStart);

        if ($isDefaultCampaign) {
            $query->where(function($query) use ($leadSalesTypeConfigurationIds) {
                $query
                    ->whereIn(EloquentQuoteCompany::LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION_ID, $leadSalesTypeConfigurationIds)
                    ->orWhere(EloquentQuoteCompany::LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION_ID, 0)
                    ->orWhereNull(EloquentQuoteCompany::LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION_ID);
            });
        }
        else {
            $query->whereIn(EloquentQuoteCompany::LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION_ID, $leadSalesTypeConfigurationIds);
        }

        return $query->first()?->toArray();
    }

    /**
     * @param LeadCampaign $leadCampaign
     * @return float|int
     */
    public function calcBudgetSpentByLeadCampaign(LeadCampaign $leadCampaign): float|int
    {
        $timestampBudgetStart = $this->getTimestampBudgetStart($leadCampaign->{LeadCampaign::LAST_MODIFIED_LEAD_LIMIT}->timestamp ?? 0);
        $budget = $this->getBudgetSpentByCompanyListSalesTypes(
            $leadCampaign->{LeadCampaign::COMPANY_ID},
            $leadCampaign->{LeadCampaign::RELATION_LEAD_SALES_TYPE_CONFIGURATIONS}->pluck(LeadCampaignSalesTypeConfiguration::ID)->toArray(),
            $timestampBudgetStart,
            $leadCampaign->{LeadCampaign::NAME} === LeadCampaign::DEFAULT_CAMPAIGN_NAME
        );

        if ($budget) {
            if ($leadCampaign->getDisplayBudgetUnit() === LeadCampaign::DISPLAY_BUDGET_UNIT_LEAD) {
                return $budget['total_count_spent'] ?? 0;
            }

            return $budget['total_cost_spent'] ?? 0;
        }

        return 0;
    }

    /**
     * The average dollar/Lead spend limit per day averaged either over the previous 30 days or since it was last modified within that last 30 days.
     *
     * @param LeadCampaign $leadCampaign
     * @param null $budgetSpent
     * @return float
     */
    public function calcAverageDailyByLeadCampaign(LeadCampaign $leadCampaign, $budgetSpent = null): float
    {
        $timestampBudgetStart = $this->getTimestampBudgetStart($leadCampaign->{LeadCampaign::LAST_MODIFIED_LEAD_LIMIT}->timestamp ?? 0);
        $days = floor((time() - $timestampBudgetStart) / 86400) + 1;

        if ($budgetSpent == null) {
            $budgetSpent = $this->calcBudgetSpentByLeadCampaign($leadCampaign);
        }

        return $this->preventDivByZero($budgetSpent, $days);
    }

    /**
     * The Usage value = (Average Daily * 100)/Budget
     *
     * @param LeadCampaign $leadCampaign
     * @param null $budgetSpent
     * @param null $averageDaily
     * @return float
     */
    public function calcUsageByLeadCampaign($leadCampaign, $budgetSpent = null, $averageDaily = null): float
    {
        if ($averageDaily == null) {
            $averageDaily = $this->calcAverageDailyByLeadCampaign($leadCampaign, $budgetSpent);
        }

        if ($leadCampaign->getBudget() == 0) {
            return 0;
        }

        return $this->preventDivByZero($averageDaily * 100, $leadCampaign->getBudget());
    }

    /**
     * @param int $lastModifiedLeadLimit
     * @return int
     */
    public function getTimestampBudgetStart(int $lastModifiedLeadLimit = 0): int
    {
        $timestampBudgetStart = 0;

        // Budget usage is calculated by today + the last 29 days at the most
        $lastMonth = Carbon::today()->subDays(29)->timestamp;

        if ($lastModifiedLeadLimit > 0) {
            $timestampBudgetStart = $lastModifiedLeadLimit;
        }

        if ($timestampBudgetStart > $lastMonth) {
            return $timestampBudgetStart;
        }

        return $lastMonth;
    }

    /**
     * @param int|float $num1
     * @param int|float $num2
     * @return float
     */
    private function preventDivByZero(int|float $num1, int|float $num2): float
    {
        if ($num2 == 0) {
            $num2 = 1;
        }

        $result = (float) $num1 / (float) $num2;

        return round((float) $result, 2);
    }
}
