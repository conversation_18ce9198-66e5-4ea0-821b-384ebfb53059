<?php

namespace App\Services\Legacy;

class Statistics
{
    /**
     * @var float
     */
    protected $rejectionNumberPercent;

    /**
     * @var float
     */
    protected $rejectionCostPercent;

    /**
     * @var int
     */
    protected $startTimestamp;

    /**
     * @var int|null
     */
    protected $numberPerDay;

    /**
     * @var float|null
     */
    protected $costPerDay;

    /**
     * @var int
     */
    protected $leadCount;

    /**
     * Lead Rejection
     * Statistics constructor.
     *
     * @param float $rejectionNumberPercent
     * @param float $rejectionCostPercent
     * @param int $startTimestamp
     * @param int $leadCount
     * @param int|null $numberPerDay
     * @param float|null $costPerDay
     */
    public function __construct(
        float $rejectionNumberPercent,
        float $rejectionCostPercent,
        int $startTimestamp,
        int $leadCount,
        ?int $numberPerDay = null,
        ?float $costPerDay = null
    )
    {
        $this->rejectionNumberPercent = $rejectionNumberPercent;
        $this->rejectionCostPercent = $rejectionCostPercent;
        $this->startTimestamp = $startTimestamp;
        $this->numberPerDay = $numberPerDay;
        $this->costPerDay = $costPerDay;
        $this->leadCount = $leadCount;
    }

    /**
     * @return float
     */
    public function getRejectionNumberPercent(): float
    {
        return $this->rejectionNumberPercent;
    }

    /**
     * @return float
     */
    public function getRejectionCostPercent(): float
    {
        return $this->rejectionCostPercent;
    }

    /**
     * @return int
     */
    public function getStartTimestamp(): int
    {
        return $this->startTimestamp;
    }

    /**
     * @return int
     */
    public function getNumberPerDay(): ?int
    {
        return $this->numberPerDay;
    }

    /**
     * @return float
     */
    public function getCostPerDay(): ?float
    {
        return $this->costPerDay;
    }

    /**
     * @return int
     */
    public function getLeadCount(): int
    {
        return $this->leadCount;
    }

}
