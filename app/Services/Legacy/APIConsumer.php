<?php

namespace App\Services\Legacy;

use App\Enums\HttpMethod;
use App\Jobs\RecordMonitoringLog;
use App\Models\ClientToken;
use App\Models\ClientTokenService;
use App\Repositories\Legacy\LeadProcessingRepository;
use App\Repositories\LogMonitoringRepository;
use Exception;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Throwable;

class APIConsumer
{
    const REQUEST_CLIENT_SECRET = 'client_secret';
    const RESPONSE_CLIENT_TOKEN = 'client_token';
    const RESPONSE_RESULT = 'data';

    protected string $baseURL;

    public function __construct(?string $baseURL = null)
    {
        $this->baseURL = $baseURL ?? config('services.admin_integration.base_url') . "/api/v1/";
    }

    /**
     * @return string
     */
    public function getBaseURL(): string
    {
        return trim($this->baseURL, '/');
    }

    /**
     * Retrieve and save the JWT sent from legacy SR Admin
     *
     * @return string
     * @throws Exception
     */
    public function authenticate(): string
    {
        $serviceId = ClientTokenService::where(ClientTokenService::FIELD_SERVICE_KEY, '=', ClientTokenService::LEGACY_API_SERVICE_KEY)->first()?->{ClientTokenService::FIELD_ID};

        if($serviceId === null) {
            throw new Exception("Service ID not found");
        }

        $clientToken = ClientToken::where(ClientToken::FIELD_SERVICE_ID, '=', $serviceId)->first();

        if(empty($clientToken)) {
            try {
                $authBaseURL = config('services.admin_integration.base_url') . "/api/v1/";
                $response = Http::get($authBaseURL. "auth/get_token", [self::REQUEST_CLIENT_SECRET => config('services.admin_integration.client_secret')]);

                if(!$response->successful()) {
                    $response->throw();
                }
            }
            catch(Exception $e) {
                Log::error($e->getMessage());

                throw $e;
            }

            $clientToken = ClientToken::firstOrNew([
                ClientToken::FIELD_SERVICE_ID => $serviceId
            ]);

            $clientToken->{ClientToken::FIELD_CLIENT_TOKEN} = $response[self::RESPONSE_CLIENT_TOKEN];

            $clientToken->save();
        }

        return $clientToken->{ClientToken::FIELD_CLIENT_TOKEN};
    }

    /**
     * @param string $route
     * @param array|null $params
     * @param int|null $timeout
     *
     * @return Response
     * @throws Exception
     */
    public function get(string $route, ?array $params = null, ?int $timeout = null): Response
    {
        $this->logRequest(HttpMethod::METHOD_GET, $route, $params);

        if ($timeout !== null) $client = $this->getClient()->timeout($timeout);
        else $client = $this->getClient();

        $response = $client->get($this->baseURL . trim(trim($route), '/'), $params ?? []);

        $this->logResponse(HttpMethod::METHOD_GET, $route, $response->status(), $params, $response->json(APIConsumer::RESPONSE_RESULT));

        return $response;
    }

    /**
     * @param string $route
     * @param array|null $data
     * @param int|null $timeout
     * @return Response
     * @throws Exception
     */
    public function post(string $route, ?array $data = null, ?int $timeout = null): Response
    {
        $this->logRequest(HttpMethod::METHOD_POST, $route, $data);

        $client = $this->getClient();

        if($timeout > 0) {
            $client->timeout($timeout);
        }

        $response = $client->post($this->baseURL . trim(trim($route), '/'), $data ?? []);

        $this->logResponse(HttpMethod::METHOD_POST, $route, $response->status(), $data, $response->json(APIConsumer::RESPONSE_RESULT));

        return $response;
    }

    /**
     * @param string $route
     * @param array|null $data
     * @param int|null $timeout
     * @return Response
     * @throws Exception
     */
    public function patch(string $route, ?array $data = null, ?int $timeout = null): Response
    {
        $this->logRequest(HttpMethod::METHOD_PATCH, $route, $data);

        $client = $this->getClient();

        if($timeout > 0) {
            $client->timeout($timeout);
        }

        $response = $client->patch($this->baseURL . trim(trim($route), '/'), $data ?? []);

        $this->logResponse(HttpMethod::METHOD_PATCH, $route, $response->status(), $data, $response->json(APIConsumer::RESPONSE_RESULT));

        return $response;
    }

    /**
     * @param string $route
     * @param array|null $data
     * @return Response
     * @throws Exception
     */
    public function delete(string $route, ?array $data = null): Response
    {
        $this->logRequest(HttpMethod::METHOD_DELETE, $route, $data);

        $response = $this->getClient()->delete($this->baseURL . trim(trim($route), '/'), $data ?? []);

        $this->logResponse(HttpMethod::METHOD_GET, $route, $response->status(), $data, $response->json(APIConsumer::RESPONSE_RESULT));

        return $response;
    }

    /**
     * @return PendingRequest
     * @throws Exception
     */
    private function getClient(): PendingRequest
    {
        $token = $this->authenticate();

        return Http::withToken($token)->throw(function($res, $e) {
            logger()->error($e);
            logger()->error($res);

            RecordMonitoringLog::dispatch(
                "API Consumer Err: ".$e->getMessage(),
                [
                    'response' => $res->getBody(),
                    "env" => App::environment()
                ],
                LogMonitoringRepository::LOGGER_SR_LEGACY_API
            );
        });
    }

    /**
     * @param HttpMethod $httpMethod
     * @param string $route
     * @param array|null $data
     * @return void
     */
    private function logRequest(HttpMethod $httpMethod, string $route, ?array $data = null): void
    {
        try {
            if(stripos($route, LeadDeliveryService::API_BASE_ENDPOINT) !== false
            || stripos($route, LeadProcessingRepository::API_UPDATE_LEAD_STATUS_ENDPOINT) !== false) {
                RecordMonitoringLog::dispatch(
                    "API Consumer request: {$httpMethod->value} {$route}",
                    [
                        "method" => $httpMethod->value,
                        "payload" => $data ?? [],
                        "env" => App::environment()
                    ],
                    LogMonitoringRepository::LOGGER_SR_LEGACY_API
                );
            }
        }
        catch(Throwable $e) {
            logger()->error($e->getMessage());
        }
    }

    /**
     * @param HttpMethod $httpMethod
     * @param string $route
     * @param int $status
     * @param array|null $reqData
     * @param $resData
     * @return void
     */
    private function logResponse(HttpMethod $httpMethod, string $route, int $status, ?array $reqData = null, $resData = null): void
    {
        try {
            if(stripos($route, LeadDeliveryService::API_BASE_ENDPOINT) !== false
            || stripos($route, LeadProcessingRepository::API_UPDATE_LEAD_STATUS_ENDPOINT) !== false) {
                RecordMonitoringLog::dispatch(
                    "API Consumer response: {$httpMethod->value} {$route}",
                    [
                        "method" => $httpMethod->value,
                        "request_payload" => $reqData ?? [],
                        "response_payload" => $resData ?? [],
                        "status" => $status,
                        "env" => App::environment()
                    ],
                    LogMonitoringRepository::LOGGER_SR_LEGACY_API
                );
            }
        }
        catch(Throwable $e) {
            logger()->error($e->getMessage());
        }
    }
}
