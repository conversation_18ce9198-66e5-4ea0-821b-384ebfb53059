<?php

namespace App\Services\Legacy;

use Exception;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Contracts\Validation\UnauthorizedException;

/**
 * Class ClientTokenService
 * @package App\Services\Client
 */
class ClientTokenService
{
    private string $signingKey;
    private int $expireInSeconds;

    public function __construct() {
        $this->signingKey = config('services.dashboard_client_api.jwt.signing_key');
        $this->expireInSeconds = (int) config('services.dashboard_client_api.jwt.expire_in');
    }

    /**
     * @param int $userId
     * @return string
     * @throws Exception
     */
    public function generate(int $userId): string
    {
        $payload = (new ClientUserToken())
            ->setUser($userId)
            ->setIssuedAt()
            ->setExpirationTime($this->expireInSeconds)
            ->payload();

        return JWT::encode($payload, $this->signingKey, 'HS256');
    }

    /**
     * @param string $token
     * @return ClientUserToken|bool
     */
    public function validate(string $token): ClientUserToken|bool
    {
        if ($token) {
            try {
                // convert payload php object to array
                $payload = (array) JWT::decode($token, new Key($this->signingKey, 'HS256'));

                return ClientUserToken::fromPayload($payload);
            }
            catch (Exception $exception) {
                // invalid access denied 401
                logger()->error("Invalid jwt token: {$exception->getMessage()}");
                return false;
            }
        }

        return false;
    }

    /**
     * @param string $token
     * @return string
     * @throws Exception
     */
    public function refresh(string $token): string
    {
        $userToken = $this->validate($token);
        if (!$userToken) {
            throw new UnauthorizedException("Invalid token.");
        }

        return $this->generate($userToken->getUserId());
    }
}
