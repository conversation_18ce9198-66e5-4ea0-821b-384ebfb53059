<?php

namespace App\Services\Legacy;

use App\Contracts\Services\LeadAggregatorsServiceContract;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\Industry;
use App\Models\Legacy\LeadAggregator;
use App\Models\Legacy\LeadAggregatorIndustry;
use App\Models\Legacy\LeadAggregatorLeadCategory;
use App\Models\Legacy\LeadAggregatorLeadType;
use App\Models\Legacy\LeadAggregatorSaleHistory;
use App\Models\Legacy\LeadAggregatorSchedule;
use App\Models\Legacy\LeadCategory;
use App\Models\Legacy\LeadType;
use Carbon\Carbon;

class LeadAggregatorsService implements LeadAggregatorsServiceContract
{
    const API_BASE_ENDPOINT                = '/services/lead-aggregator';
    const API_SELL_TO_AGGREGATORS_ENDPOINT = self::API_BASE_ENDPOINT . '/sell-to-aggregators';

    /** @var APIConsumer $apiConsumer */
    protected APIConsumer $apiConsumer;

    /**
     * @param APIConsumer $apiConsumer
     */
    public function __construct(APIConsumer $apiConsumer)
    {
        $this->apiConsumer = $apiConsumer;
    }

    /**
     * @inheritDoc
     */
    public function sellToAggregators(string $leadReference, int $legacyUserId): bool
    {
        return count($this->apiConsumer->post(self::API_SELL_TO_AGGREGATORS_ENDPOINT,
                compact("leadReference", "legacyUserId")
            )->json(APIConsumer::RESPONSE_RESULT)) > 0 ?? false;
    }

    /**
     * @param EloquentQuote $quote
     * @return mixed
     */
    public function getAvailableAggregators(EloquentQuote $quote) {
        $leadTypeId = $quote->{EloquentQuote::LEAD_TYPE_ID};
        $leadCategoryId = $quote->{EloquentQuote::LEAD_CATEGORY_ID};
        $leadIndustry = $quote->getLeadIndustry();
        $leadIndustryId = !empty($leadIndustry) ? $leadIndustry->{Industry::FIELD_ID} : null;

        $leadAggregators = LeadAggregator::where(LeadAggregator::FIELD_STATUS, '=', LeadAggregator::STATUS_ACTIVE)
            ->whereHas(LeadAggregator::RELATION_COMPANY, function ($query) {
                $query
                    ->where(EloquentCompany::TABLE.'.'.EloquentCompany::TYPE, '=', EloquentCompany::TYPE_PING_POST_AGGREGATOR)
                    ->where(EloquentCompany::TABLE.'.'.EloquentCompany::STATUS, '=', EloquentCompany::STATUS_ACTIVE);
            })
            ->has(LeadAggregator::RELATION_SCHEDULES)
            ->whereHas(LeadAggregator::RELATION_LEAD_TYPES, function ($query) use ($leadTypeId) {
                $query
                    ->where(LeadAggregatorLeadType::FIELD_LEAD_TYPE_ID, '=', $leadTypeId)
                    ->whereHas(LeadAggregatorLeadType::RELATION_LEAD_TYPE, function($query) {
                        $query->where(LeadType::STATUS, '=', LeadType::STATUS_ENABLE);
                    });
            })
            ->whereHas(LeadAggregator::RELATION_LEAD_CATEGORIES, function ($query) use ($leadCategoryId) {
                $query
                    ->where(LeadAggregatorLeadCategory::FIELD_LEAD_CATEGORY_ID, '=', $leadCategoryId)
                    ->whereHas(LeadAggregatorLeadCategory::RELATION_LEAD_CATEGORY, function($query) {
                        $query->where(LeadCategory::STATUS, '=', LeadCategory::STATUS_ENABLE);
                    });
            })
            ->whereHas(LeadAggregator::RELATION_INDUSTRIES, function ($query) use ($leadIndustryId) {
                $query
                    ->where(LeadAggregatorIndustry::FIELD_INDUSTRY_ID, '=', $leadIndustryId);
            })
            ->with([LeadAggregator::RELATION_SALES_HISTORY => function ($query) {
                $query
                    ->where(LeadAggregatorSaleHistory::TABLE . '.' . LeadAggregatorSaleHistory::FIELD_TRANSACTION_TYPE, '=', LeadAggregatorSaleHistory::TRANSACTION_TYPE_POST)
                    ->where(LeadAggregatorSaleHistory::TABLE . '.' . LeadAggregatorSaleHistory::FIELD_RESPONSE_STATUS, '=', LeadAggregatorSaleHistory::RESPONSE_STATUS_SUCCESS)
                    ->where(LeadAggregatorSaleHistory::TABLE . '.' . LeadAggregatorSaleHistory::FIELD_PRICE, '!=', 0.00)
                    ->where(LeadAggregatorSaleHistory::TABLE . '.' . LeadAggregatorSaleHistory::FIELD_CREATED_AT, '<=', date('Y-m-d').' 23:59:59')
                    ->where(LeadAggregatorSaleHistory::TABLE . '.' . LeadAggregatorSaleHistory::FIELD_CREATED_AT, '>=', date('Y-m-d').' 00:00:00')
                    ->whereNull(LeadAggregatorSaleHistory::TABLE.'.'.LeadAggregatorSaleHistory::FIELD_DELETED_AT);
            }])
            ->with([LeadAggregator::RELATION_SCHEDULES => function ($query) {
                $query
                    ->where(LeadAggregatorSchedule::TABLE.'.'.LeadAggregatorSchedule::FIELD_ACTIVE, '=', LeadAggregatorSchedule::ACTIVE_STATUS)
                    ->whereNull(LeadAggregatorSchedule::TABLE.'.'.LeadAggregatorSchedule::FIELD_DELETED_AT);
            }])
            ->get()
            ->keyBy(LeadAggregator::FIELD_ID);

        foreach($leadAggregators as $leadAggregatorId => &$leadAggregator) {
            $validBusinessTime = false;

            $datetime = Carbon::now($leadAggregator->{LeadAggregator::FIELD_TIMEZONE});
            $time = $datetime->toTimeString();

            foreach ($leadAggregator->{LeadAggregator::RELATION_SCHEDULES} as $schedule) {
                if($schedule->{LeadAggregatorSchedule::FIELD_DAY_OF_WEEK} === $datetime->dayOfWeekIso
                    && (
                        ($schedule->{LeadAggregatorSchedule::FIELD_START_TIME} <= $time
                            && $schedule->{LeadAggregatorSchedule::FIELD_END_TIME} >= $time)
                        || $schedule->{LeadAggregatorSchedule::FIELD_ANYTIME} == 1
                    )) {
                    $validBusinessTime = true;
                    break;
                }
            }

            if(!$validBusinessTime) {
                unset($leadAggregators[$leadAggregatorId]);

                continue;
            }

            if(!empty($leadAggregator->{LeadAggregator::FIELD_DAILY_CAP})
                && $leadAggregator->{LeadAggregator::RELATION_SALES_HISTORY}->count() >= $leadAggregator->{LeadAggregator::FIELD_DAILY_CAP}) {
                unset($leadAggregators[$leadAggregatorId]);

                continue;
            }

            unset($leadAggregator->{LeadAggregator::RELATION_SCHEDULES}, $leadAggregator->{LeadAggregator::RELATION_SALES_HISTORY});
        }

        return $leadAggregators;
    }
}
