<?php

namespace App\Services\Legacy;

use App\Contracts\Services\LeadRevenueScenarioServiceContract;
use App\Models\LeadRevenueScenario;
use Exception;

class LeadRevenueScenarioService implements LeadRevenueScenarioServiceContract
{
    const API_BASE_ENDPOINT = '/services/lead-revenue-scenario';
    const API_CALCULATE_AND_STORE_REVENUE_SCENARIO_FOR_LEAD_ENDPOINT = self::API_BASE_ENDPOINT . '/calculate-and-store-revenue-scenario-for-lead';

    /** @var APIConsumer $apiConsumer */
    protected APIConsumer $apiConsumer;

    /**
     * @param APIConsumer $apiConsumer
     */
    public function __construct(APIConsumer $apiConsumer)
    {
        $this->apiConsumer = $apiConsumer;
    }

    /**
     * @inheritDoc
     */
    public function calculateAndStoreRevenueScenarioForLead(string $leadReference, string $type = LeadRevenueScenario::TYPE_UNVERIFIED): ?bool
    {
        return $this->apiConsumer->post(
            self::API_CALCULATE_AND_STORE_REVENUE_SCENARIO_FOR_LEAD_ENDPOINT,
            compact("leadReference", "type"),
            60
        )->json(APIConsumer::RESPONSE_RESULT);
    }
}
