<?php

namespace App\Services\Legacy\Campaigns;

use App\Contracts\Services\Campaigns\LeadCampaignAvailabilityServiceContract;
use App\Services\Legacy\APIConsumer;
use Exception;
use Illuminate\Support\Collection;

class LeadCampaignAvailabilityService implements LeadCampaignAvailabilityServiceContract
{
    const API_BASE_ENDPOINT                                                   = '/services/lead-campaign-availability';
    const API_ARE_COMPANIES_AVAILABLE_ENDPOINT                                = self::API_BASE_ENDPOINT . '/are-companies-available';
    const API_ARE_ALL_COMPANIES_OVER_BUDGET_ENDPOINT                          = self::API_BASE_ENDPOINT . '/are-all-companies-over-budget';
    const API_CHECK_LEAD_HAS_SUPER_PREMIUM_BUYERS_ENDPOINT                    = self::API_BASE_ENDPOINT . '/check-lead-has-super-premium-buyers';
    const API_GET_BEST_OPTIONS_SALES_TYPE_CONFIGURATIONS_EXCLUDING_UNVERIFIED = self::API_BASE_ENDPOINT . '/get-best-option-sales-type-configurations-excluding-unverified';
    const API_COMPANIES_WITH_AVAILABLE_BUDGET_COUNT_ENDPOINT                  = self::API_BASE_ENDPOINT . '/companies-with-available-budget-count';
    const API_GET_BEST_OPTIONS_SALES_TYPE_CONFIGURATIONS_ONLY_UNVERIFIED      = self::API_BASE_ENDPOINT . '/get-best-option-sales-type-configurations-only-unverified';
    const API_COMPANIES_WITH_AVAILABLE_UNVERIFIED_BUDGET_COUNT_ENDPOINT       = self::API_BASE_ENDPOINT . '/companies-with-available-unverified-budget-count';

    const TIMEOUT = 60; //seconds

    /** @var APIConsumer $apiConsumer */
    protected APIConsumer $apiConsumer;

    /**
     * @param APIConsumer $apiConsumer
     */
    public function __construct(APIConsumer $apiConsumer)
    {
        $this->apiConsumer = $apiConsumer;
    }

    /**
     * @inheritDoc
     */
    public function areCompaniesAvailable(
        string $leadReference,
        bool   $onlyAdded = false,
        ?array $leadSalesTypeConfigurationIds = null,
        array  $ignoreCompanyIds = [],
        ?int $timeout = null
    ): bool
    {
        return $this->apiConsumer->get(self::API_ARE_COMPANIES_AVAILABLE_ENDPOINT,
            compact("leadReference", "onlyAdded", "leadSalesTypeConfigurationIds", "ignoreCompanyIds"),
            $timeout
        )->json(APIConsumer::RESPONSE_RESULT);
    }

    /**
     * @inheritDoc
     * @throws Exception
     */
    public function areAllCompaniesOverBudget(string $leadReference, ?int $timeout = null): bool
    {
        $result = $this->apiConsumer->get(self::API_ARE_ALL_COMPANIES_OVER_BUDGET_ENDPOINT,
            compact("leadReference"),
            $timeout
        )->json(APIConsumer::RESPONSE_RESULT);

        return gettype($result) === 'boolean' ? $result : false;
    }

    /**
     * @inheritDoc
     */
    public function companiesWithAvailableBudgetCount(string $leadReference): int
    {
        $result = $this->apiConsumer->get(self::API_COMPANIES_WITH_AVAILABLE_BUDGET_COUNT_ENDPOINT,
            compact("leadReference"),
            self::TIMEOUT
        )->json(APIConsumer::RESPONSE_RESULT);

        return gettype($result) === 'integer' ? $result : 0;
    }

    public function companiesWithAvailableUnverifiedBudgetCount(string $leadReference):int
    {
        return $this->apiConsumer->get(self::API_COMPANIES_WITH_AVAILABLE_UNVERIFIED_BUDGET_COUNT_ENDPOINT,
            compact("leadReference"),
            self::TIMEOUT
        )->json(APIConsumer::RESPONSE_RESULT);
    }

    /**
     * @inheritDoc
     */
    public function checkLeadHasSuperPremiumBuyers(string $leadReference, ?int $timeout = null): bool
    {
        return $this->apiConsumer->get(self::API_CHECK_LEAD_HAS_SUPER_PREMIUM_BUYERS_ENDPOINT,
            compact("leadReference"),
            $timeout
        )->json(APIConsumer::RESPONSE_RESULT);
    }

    /**
     * Excludes 'Unverified' and 'Email Only'
     *
     * @inheritDoc
     */
    public function getBestOptionSalesTypeConfigurationsExcludingUnverified(
        string $leadReference,
        array  $ignoreCompanyIds = [],
        array  $forceIncludeCampaignIds = [],
        bool   $excludeBudget = false,
        ?int   $remainingLegs = null,
        ?int   $legsRequested = null
    ): Collection
    {
        $res = $this->apiConsumer->get(self::API_GET_BEST_OPTIONS_SALES_TYPE_CONFIGURATIONS_EXCLUDING_UNVERIFIED,
            compact("leadReference", "ignoreCompanyIds", "forceIncludeCampaignIds", "excludeBudget", "remainingLegs", "legsRequested"),
            self::TIMEOUT
        )->json(APIConsumer::RESPONSE_RESULT);

        return collect($res);
    }

    /**
     * Excludes 'Email Only'
     *
     * @inheritDoc
     */
    public function getBestOptionSalesTypeConfigurationsOnlyUnverified(string $leadReference, array $ignoreCompanyIds = []): Collection
    {
        $res = $this->apiConsumer->get(self::API_GET_BEST_OPTIONS_SALES_TYPE_CONFIGURATIONS_ONLY_UNVERIFIED,
            compact("leadReference", "ignoreCompanyIds"),
            self::TIMEOUT
        )->json(APIConsumer::RESPONSE_RESULT);

        return collect($res);
    }
}
