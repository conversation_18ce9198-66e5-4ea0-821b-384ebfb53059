<?php

namespace App\Services;

use App\Models\Legacy\BestRevenueScenarioRoofing;
use App\Models\Legacy\BestRevenueScenarioSolar;
use App\Models\Legacy\EloquentQuote;
use App\Repositories\Legacy\BestRevenueRecordingRepository;
use Illuminate\Support\Collection;

class BestRevenueRecordingService
{

    /** @var BestRevenueRecordingRepository $repository*/
    private BestRevenueRecordingRepository $repository;

    public function __construct(BestRevenueRecordingRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * @param EloquentQuote $lead
     * @param Collection|null $salesTypeConfigurations
     * @return bool
     */
    public function updateRevenueScenario(EloquentQuote $lead, ?Collection $salesTypeConfigurations): bool
    {
        // Due to suspected latency in the replica db, lead_type_id isn't always available at this point.
        // It is better to omit the entry, rather than assume 'Standard', because this would result in unreliable data.
        if(!$lead->lead_type_id){return false;}

        $revenue = $salesTypeConfigurations ? $salesTypeConfigurations->sum('price') : 0;

        if($lead->{EloquentQuote::SOLAR_LEAD} == 1){
            return $this->updateSolar($lead, $revenue) !== null;
        }elseif($lead->{EloquentQuote::ROOFING_LEAD} == 1){
            return $this->updateRoofing($lead, $revenue) !== null;
        }
        return false;
    }

    /**
     * @param EloquentQuote $lead
     * @param int $revenue
     * @return BestRevenueScenarioSolar
     */
    protected function updateSolar(EloquentQuote $lead, int $revenue): ?BestRevenueScenarioSolar
    {
        return $this->repository->updateSolar(
            $lead->address->zipcode,
            $lead->getUtilityId(),
            intval($lead->electriccost) > 100,
            $lead->lead_type_id,
            $revenue
        );
    }

    /**
     * @param EloquentQuote $lead
     * @param int $revenue
     * @return BestRevenueScenarioRoofing
     */
    protected function updateRoofing(EloquentQuote $lead, int $revenue): ?BestRevenueScenarioRoofing
    {
        return $this->repository->updateRoofing(
            $lead->address->zipcode,
            $lead->lead_type_id,
            $revenue
        );
    }
}
