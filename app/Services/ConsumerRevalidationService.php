<?php

namespace App\Services;

use App\Models\LeadProcessingInitial;
use App\Models\LeadProcessor;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Repositories\Odin\ConsumerRepository;
use App\Repositories\Odin\ProductAssignmentRepository;
use App\Repositories\Odin\ProductProcessing\ProductProcessingQueueConstraintsRepository;
use App\Repositories\Odin\ProductProcessing\ProductProcessingRepository;
use App\Services\Communication\TwilioCommunicationService;
use App\Services\Odin\ProductProcessing\ProductProcessingService;
use App\Services\OutreachCadence\TimeZoneHelperService;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Arr;

class ConsumerRevalidationService
{
    const int    JOB_DELAY                 = 2 * 60 * 60; //2 hours
    const int    CONSUMER_LOCAL_OPEN_HOUR  = 8; //8AM
    const int    CONSUMER_LOCAL_CLOSE_HOUR = 18; //6PM
    const string NEXT_DAY_HOUR             = '08:00'; //8AM

    public function __construct(
        protected ProductAssignmentRepository $productAssignmentRepository,
        protected TwilioCommunicationService $twilioCommunicationService,
        protected ProductProcessingService $processingService,
        protected ConsumerRepository $consumerRepository,
        protected ProductProcessingQueueConstraintsRepository $productProcessingQueueConstraintsRepository,
        protected ProductProcessingService $productProcessingService,
        protected ProductProcessingRepository $productProcessingRepository,
        protected TimeZoneHelperService $timeZoneHelperService,
    ) {}

    public function sendRevalidationSMS(Consumer $consumer): void
    {
        if (!$this->consumerEligibleForRevalidation($consumer)) {
            return;
        }

        try {
            logger()->info("Sending revalidation SMS to phone: $consumer->phone, consumer: $consumer->id");

            $this->twilioCommunicationService->sendSMS(
                config('services.twilio.consumer_verification_from_phone'),
                $consumer->phone,
                $this->getRevalidationSMS($consumer)
            );
        } catch (Exception $e) {
            logger()->error("Filed to send revalidation SMS to consumer. Error: {$e->getMessage()}");
        }
    }

    /**
     * @param Consumer $consumer
     *
     * @return float|int
     * @throws Exception
     */
    public function getConsumerTimezoneOpenDelay(Consumer $consumer): float|int
    {
        ['standard_utc_offset' => $standardUTCOffset, 'observing_dst' => $observingDST] = $this->timeZoneHelperService->getTimezoneOffsetDataFromZipcode(
            $this->getConsumerProduct($consumer)->address->zip_code
        );
        $dstAdjustment = $this->timeZoneHelperService->getDSTAdjustment($observingDST);
        $localNow = Carbon::now($standardUTCOffset + $dstAdjustment);

        if ($localNow->hour >= self::CONSUMER_LOCAL_OPEN_HOUR && $localNow->hour <= self::CONSUMER_LOCAL_CLOSE_HOUR) {
            return 0;
        }

        return $localNow->diffInSeconds($localNow->copy()->next(self::NEXT_DAY_HOUR));
    }

    /**
     * @param array $data
     *
     * @return void
     * @throws BindingResolutionException
     */
    public function handleConsumerReply(array $data): void
    {
        $to = Arr::get($data, 'To');
        $from = Arr::get($data, 'From');
        $body = Arr::get($data, 'Body');

        if (!$to || !$from || !$body) {
            return;
        }

        $consumer = $this->consumerRepository->findConsumerByPhone($from);

        if (!$consumer) {
            return;
        }

        if (!in_array(strtolower(trim($body)), ['y', 'yes'])) {
            return;
        }

        try {
            $this->twilioCommunicationService->sendSMS(
                $to,
                $from,
                $this->getReplySMS($consumer)
            );
        } catch (Exception $e) {
            logger()->error("Filed to send reply SMS to consumer. Error: {$e->getMessage()}");
        }

        if ($this->consumerProductProcessed($consumer)) {
            return;
        }

        $consumer->update([Consumer::FIELD_CLASSIFICATION => Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_REVALIDATION_SMS]);
        $this->addInInitialQueue($consumer);
    }

    /**
     * @param Consumer $consumer
     *
     * @return bool
     */
    protected function consumerEligibleForRevalidation(Consumer $consumer): bool
    {
        if (!$consumer->phone) {
            return false;
        }

        if (!in_array($this->getConsumerProduct($consumer)->status, [
            ConsumerProduct::STATUS_INITIAL,
            ConsumerProduct::STATUS_PENDING_REVIEW,
            ConsumerProduct::STATUS_UNDER_REVIEW
        ])) {
            return false;
        }

        if (in_array($consumer->classification, Consumer::VERIFIED_CLASSIFICATIONS)) {
            return false;
        }

        if ($this->productAssignmentRepository->getAllAssignmentsForConsumer($consumer)->isNotEmpty()) {
            return false;
        }

        if ($this->getConsumerProduct($consumer)->pingPostAffiliate ?? null) {
            return false;
        }

        return true;
    }

    /**
     * @param Consumer $consumer
     *
     * @return string
     */
    protected function getRevalidationSMS(Consumer $consumer): string
    {
        return sprintf(
            'Hi %s, we noticed you started the process of getting a cost estimate for %s on %s. Are you still interested in seeing prices from local %s companies? Reply YES to receive more information or NO if you do not wish to be contacted.',
            $consumer->first_name,
            strtolower($this->getConsumerService($consumer)),
            $this->getConsumerOrigin($consumer),
            strtolower($this->getConsumerIndustry($consumer)),
        );
    }

    /**
     * @param Consumer $consumer
     *
     * @return string
     */
    protected function getReplySMS(Consumer $consumer): string
    {
        return sprintf(
            'Thank you %s, a representative from %s will be in touch soon.',
            $consumer->first_name,
            $this->getConsumerOrigin($consumer)
        );
    }

    /**
     * @param Consumer $consumer
     *
     * @return string
     */
    protected function getConsumerOrigin(Consumer $consumer): string
    {
        return $consumer->website?->url ?? 'fixr.com';
    }

    /**
     * @param Consumer $consumer
     *
     * @return string
     */
    protected function getConsumerService(Consumer $consumer): string
    {
        return $this->getConsumerProduct($consumer)->industryService->name;
    }

    /**
     * @param Consumer $consumer
     * @return string
     */
    protected function getConsumerIndustry(Consumer $consumer): string
    {
        return $this->getConsumerProduct($consumer)->{ConsumerProduct::RELATION_INDUSTRY_SERVICE}->{IndustryService::RELATION_INDUSTRY}->{Industry::FIELD_NAME};
    }

    /**
     * @param Consumer $consumer
     * @param string $reason
     *
     * @return void
     * @throws BindingResolutionException
     */
    public function addInInitialQueue(Consumer $consumer, string $reason = 'Consumer phone was verified through revalidation SMS'): void
    {
        $consumerProduct = $this->getConsumerProduct($consumer);

        if (LeadProcessingInitial::query()->where(LeadProcessingInitial::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->id)->first()) {
            return;
        }

        $this->productProcessingRepository->removePendingReview($consumerProduct);
        $this->productProcessingRepository->removeUnderReview($consumerProduct);
        $this->productProcessingRepository->updateProductStatusAndReason($consumerProduct, ConsumerProduct::STATUS_INITIAL, $reason);

        // add the product in the initial queue
        $this->productProcessingQueueConstraintsRepository->saveProductProcessingQueueConstraintsBucketFlags($consumerProduct->id);
        $this->productProcessingService->createInitialProduct($consumerProduct->id);
    }

    /**
     * @param Consumer $consumer
     * @param string $reason
     * @return void
     * @throws BindingResolutionException
     */
    public function addInPendingReviewQueue(Consumer $consumer, string $reason): void
    {
        $consumerProduct = $this->getConsumerProduct($consumer);

        $this->productProcessingRepository->updateProductStatusAndReason($consumerProduct, ConsumerProduct::STATUS_PENDING_REVIEW, $reason);

        $this->productProcessingQueueConstraintsRepository->saveProductProcessingQueueConstraintsBucketFlags($consumerProduct->id);
        $this->productProcessingService->createPendingReviewLead(
            $consumerProduct->id,
            $reason,
            LeadProcessor::systemProcessor(),
        );
    }

    /**
     * @param Consumer $consumer
     *
     * @return bool
     */
    protected function consumerProductProcessed(Consumer $consumer): bool
    {
        if (!in_array($this->getConsumerProduct($consumer)->status, [
            ConsumerProduct::STATUS_INITIAL,
            ConsumerProduct::STATUS_PENDING_REVIEW,
            ConsumerProduct::STATUS_UNDER_REVIEW
        ])) {
            return true;
        }

        if ($this->productAssignmentRepository->getAllAssignmentsForConsumer($consumer)->isNotEmpty()) {
            return true;
        }

        return false;
    }

    /**
     * @param Consumer $consumer
     *
     * @return ConsumerProduct
     */
    protected function getConsumerProduct(Consumer $consumer): ConsumerProduct
    {
        /** @var ConsumerProduct */
        return $consumer->consumerProducts()->oldest()->first();
    }
}
