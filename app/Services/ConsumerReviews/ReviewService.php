<?php

namespace App\Services\ConsumerReviews;

use App\DTO\ConsumerReviews\CreateReviewParam;
use App\Jobs\SendReviewPhoneVerificationSMS;
use App\Jobs\AssociateLeadWithReviewJob;
use App\Models\ConsumerReviews\Review;
use App\Models\ConsumerReviews\ReviewData;
use App\Models\ConsumerReviews\Reviewer;
use App\Models\Odin\Company;
use App\Models\Odin\Website;
use App\Repositories\Odin\CompanyRepository;
use App\Repositories\Odin\WebsiteRepository;
use App\Repositories\ReviewRepository;
use App\Repositories\SmsVerificationRepository;
use App\Services\CloudStorage\GoogleCloudStorageService;
use Exception;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\App;
use App\Services\Communication\TwilioCommunicationService;
use App\Services\Twilio\TwilioVerificationService;
use Illuminate\Support\Str;
use Throwable;
use Twilio\Exceptions\TwilioException;

class ReviewService
{
    public function __construct(
        protected CompanyRepository $companyRepository,
        protected ReviewRepository $reviewRepository,
        protected TwilioCommunicationService $twilioCommunicationService,
        protected SmsVerificationRepository $smsVerificationRepository,
        protected TwilioVerificationService $twilioVerificationService,
        protected WebsiteRepository $websiteRepository,
    ){}

    /**
     * @param Company $company
     * @param CreateReviewParam $createReviewParam
     * @return Review
     */
    public function createReview(Company $company, CreateReviewParam $createReviewParam): Review
    {
        $website = $this->websiteRepository->getWebsiteByAbbreviation($createReviewParam->getOrigin());
        $newReview = $this->reviewRepository->create($company, $createReviewParam, $website);
        AssociateLeadWithReviewJob::dispatch(collect([$newReview]));
        return $newReview;
    }

    /**
     * @param Reviewer $reviewer
     * @param string $reviewUuid
     * @param Website|null $origin
     * @return bool
     */
    public function sendEmailVerification(Reviewer $reviewer, string $reviewUuid, ?Website $origin = null): bool
    {
        if (!$reviewer->email) return false;

        $token = Str::random(Reviewer::EMAIL_VERIFICATION_TOKEN_LENGTH);
        $reviewer->{Reviewer::FIELD_EMAIL_VERIFICATION_TOKEN} = $token;
        $reviewer->save();
        $reviewer->sendEmailVerificationNotification($reviewUuid, $token, $origin);

        return true;
    }

    /**
     * @param Reviewer $reviewer
     * @return bool
     * @throws Exception
     */
    public function sendPhoneVerification(Reviewer $reviewer): bool
    {
        if (!$reviewer->phone) return false;

        if (App::isProduction()){
            dispatch(new SendReviewPhoneVerificationSMS($reviewer));
        } else {
            $this->twilioVerificationService->setPhoneNumber($reviewer->phone)->sendCode($reviewer->reference);
        }
        return true;
    }

    /**
     * @param Reviewer $reviewer
     * @param string|null $code
     * @param string $reviewUuid
     * @return bool
     * @throws TwilioException
     */
    public function verifyPhone(Reviewer $reviewer, ?string $code, string $reviewUuid): bool
    {
        if (!$code || !$reviewer->phone) return false;
        // Find the specific review using the reviewUUID
        $review = $this->reviewRepository->getReviewByUUID($reviewUuid);
        if (!$review) return false;

        if (!$this->twilioVerificationService->setPhoneNumber($reviewer->phone)->verifyCode($reviewer->reference, $code)){
            return false;
        }

        $reviewer->{Reviewer::FIELD_IS_PHONE_VERIFIED} = true;
        $reviewer->save();

        // Verify only the specific review
        $review->{Review::FIELD_IS_VERIFIED} = true;
        $review->save();

        return true;
    }

    /**
     * @param Reviewer $reviewer
     * @param string $code
     * @param string $reviewUuid
     * @return bool
     */
    public function verifyEmail(Reviewer $reviewer, string $code, string $reviewUuid): bool
    {
        if (!$code || !$reviewer->{Reviewer::FIELD_EMAIL_VERIFICATION_TOKEN}) return false;

        if ($code === $reviewer->{Reviewer::FIELD_EMAIL_VERIFICATION_TOKEN}){
            if (!$reviewer->hasVerifiedEmail()) {
                $verified = $reviewer->markEmailAsVerified();
                if ($verified) {
                    $reviewer->{Reviewer::FIELD_IS_EMAIL_VERIFIED} = true;
                    $reviewer->save();

                    // Find the specific review using the reviewUUID
                    $review = $this->reviewRepository->getReviewByUUID($reviewUuid);
                    $review->{Review::FIELD_IS_VERIFIED} = true;
                    $review->save();

                    return true;
                }
            }
        }
        return false;
    }

    /**
     * @param string $companyReference
     * @param string $reviewerContactValue
     * @param string $reviewerContactMethod
     * @return bool
     */
    public function checkForExistingReview(string $companyReference, string $reviewerContactValue, string $reviewerContactMethod = 'email'): bool
    {
        $company        = $this->companyRepository->findByReferenceOrFail($companyReference);
        $existingReviews = $this->reviewRepository->findReviewsByCompanyAndContactDetails($company->id, $reviewerContactValue, $reviewerContactMethod);

        //TODO: unsure how long we need to allow for a consumer leaving a valid second review?
        return !!$existingReviews
            ->filter(fn($review) => $review->created_at > now()->subMonths(2))
            ->count();
    }

    /**
     * @param UploadedFile[] $files
     * @return array
     */
    public function handleAttachments(array $files): array
    {
        /** @var GoogleCloudStorageService $fileUploadService */
        $fileUploadService = app(GoogleCloudStorageService::class);

        $bucket = config('services.google.storage.buckets.review_attachments');
        $fileUploadService->setCurrentBucket($bucket);
        $uploadResponses = [];

        foreach ($files as $file) {
            try {
                $path = $file->getClientOriginalName();
                $response = $fileUploadService->upload($path, $file->getContent());
                if ($response->info()) {
                    $uploadResponses[] = [
                        ReviewData::ATTACHMENT_KEY_PUBLIC_URL => $this->makePath([config('services.google.storage.base_url', ''), $bucket, $response->info()['name']]),
                        ReviewData::ATTACHMENT_KEY_ID         => $response->info()['id'],
                    ];
                }
            }
            catch(Throwable $e) {
                logger()->error($e->getMessage());
            }
        }

        return $uploadResponses;
    }

    /**
     * @param string[] $parts
     * @return string
     */
    protected function makePath(array $parts): string
    {
        return preg_replace("/\/+/", '/', implode('/', $parts));
    }
}
