<?php

namespace App\Services\TwoFactorAuthentication;

use App\Contracts\TwoFactorAuthentication\AuthenticatorTwoFactorAuthenticationContract;
use App\Contracts\TwoFactorAuthentication\TwoFactorAuthenticationContract;
use App\Models\User;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class TwoFactorAuthenticationService
{
    /**
     * Returns a 2FA Service.
     *
     * @TODO: In the future we'll update this function to make a different driver based on the users' choice
     *        for if they want SMS, authenticator, etc.
     *
     * @return TwoFactorAuthenticationContract
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    protected function getService(): TwoFactorAuthenticationContract
    {
        /** @var Factory $factory */
        $factory = app()->make(Factory::class);

        return $factory->make();
    }

    /**
     * Handles setting up the 2FA for a user.
     *
     * @param User $user
     * @return void
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function setup2FAForUser(User $user): void
    {
        if ($user->two_factor_auth_secret_key !== null)
            return;

        $user->two_factor_auth_secret_key = $this->getService()->generateSecretKey($user);
        $user->save();
    }

    /**
     * Returns a QR Code image for the given user to setup their authenticator.
     *
     * @param User $user
     * @return string
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function get2FAQRCode(User $user): string
    {
        if ($user->two_factor_auth_secret_key === null)
            $this->setup2FAForUser($user);

        /** @var AuthenticatorTwoFactorAuthenticationContract $service */
        $service = $this->getService();

        if (!(is_subclass_of($service, \App\Contracts\TwoFactorAuthentication\AuthenticatorTwoFactorAuthenticationContract::class)))
            throw new \RuntimeException("User's 2FA method does not support QR codes.");

        return QrCode::generate($service->getSigningUrl($user));
    }

    /**
     * Verifies that a code provided is valid.
     *
     * @param User $user
     * @param string $code
     * @return bool
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function verifyCode(User $user, string $code): bool
    {
        return boolval($this->getService()->verifyKey($user, $code));
    }
}
