<?php

namespace App\Services\TwoFactorAuthentication;

use App\Contracts\TwoFactorAuthentication\TwoFactorAuthenticationContract;
use App\Services\TwoFactorAuthentication\Authenticator\Authenticator2FAService;

class Factory
{
    public function make(string $driver = 'authenticator'): TwoFactorAuthenticationContract
    {
        return match($driver) {
            'authenticator' => app()->make(Authenticator2FAService::class),
            default => throw new \RuntimeException("2FA Driver '{$driver}' not found.")
        };
    }
}
