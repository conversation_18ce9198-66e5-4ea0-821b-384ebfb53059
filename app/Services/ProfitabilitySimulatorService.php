<?php

namespace App\Services;

use App\Enums\Odin\Industry;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Legacy\EloquentQuote;
use App\Models\Odin\ConsumerProduct;
use App\Repositories\Odin\Campaigns\CompanyCampaignRepository;
use App\Repositories\Odin\ConsumerRepository;
use App\Repositories\Odin\IndustryRepository;
use App\Repositories\Odin\PriceRepository;
use App\Repositories\Odin\ProductAssignmentRepository;
use App\Repositories\ProfitabilitySimulatorRepository;
use App\Services\LeadProcessing\LeadProcessingService;
use Carbon\Carbon;
use Illuminate\Support\Arr;

class ProfitabilitySimulatorService
{
    const string INDUSTRY_SOLAR          = 'solar';
    const int    FIVE_PM                 = 17;
    const string LEAD_TYPE_VERIFIED      = 'verified';
    const string LEAD_TYPE_UNVERIFIED    = 'unverified';
    const int    UNVERIFIED_SALE_TYPE_ID = 6;
    const string PRICE_TYPE_FLAT         = 'flat';

    /**
     * @param ProfitabilitySimulatorRepository $profitabilityReportRepository
     * @param PriceRepository $priceRepository
     * @param IndustryRepository $industryRepository
     * @param ProductAssignmentRepository $assignmentRepository
     * @param CompanyCampaignRepository $campaignRepository
     * @param ConsumerRepository $consumerRepository
     * @param LeadProcessingService $leadProcessingService
     */
    public function __construct(
        protected ProfitabilitySimulatorRepository $profitabilityReportRepository,
        protected PriceRepository $priceRepository,
        protected IndustryRepository $industryRepository,
        protected ProductAssignmentRepository $assignmentRepository,
        protected CompanyCampaignRepository $campaignRepository,
        protected ConsumerRepository $consumerRepository,
        protected LeadProcessingService $leadProcessingService,
    ) {}

    /**
     * @param $min
     * @param $max
     * @param $interval
     * @param $useNoLimit
     * @return array
     */
    private function getBudgetOptions($min, $max, $interval, $useNoLimit): array
    {
        $budgetOptions = [];

        for ($i = $min; $i <= $max; $i+= $interval) {
            $budgetOptions = Arr::add($budgetOptions, $i, []);
        }

        if(!isset($budgetOptions[$max])) {
            $budgetOptions = Arr::add($budgetOptions, $max, []);
        }

        if($useNoLimit) {
            $budgetOptions = Arr::add($budgetOptions, 'no-limit', []);
        }

        return $budgetOptions;
    }

    /**
     * @param array $budgetOptions
     * @param array $auxiliaryCampaigns
     * @param int $min
     * @param int $max
     * @param int $interval
     * @return array
     */
    private function addPricingOptions(array $budgetOptions, array $auxiliaryCampaigns, int $min, int $max, int $interval): array
    {
        $pricingOptions = [];

        $auxCampaigns = array_map(fn(array $campaign) => ['spend' => 0], $auxiliaryCampaigns);

        for ($i = $min; $i <= $max; $i+= $interval) {
            $pricingOptions = Arr::add($pricingOptions, $i, [
                'spend'                 => 0,
                'lead_type_counts'      => [
                    1 => 0,
                    2 => 0,
                    3 => 0,
                    4 => 0,
                    6 => 0, // unverified
                ],
                'new_leads_sold_count'  => 0,
                'new_legs_sold_count'   => 0,
                'new_revenue'           => 0,
                'assigned_leads' => [],
                'aux_campaigns' => $auxCampaigns
            ]);
        }

        return Arr::map($budgetOptions, function($budgetOption) use ($pricingOptions) {
            return $pricingOptions;
        });
    }

    /**
     * @param string $priceType
     * @param int $priceOption
     * @param float $floorPrice
     * @param int $saleTypeId
     * @param array $flatPrices
     * @return float
     */
    private function getSimulatedPrice(string $priceType, int $priceOption, float $floorPrice, int $saleTypeId, array $flatPrices = []): float
    {
        $saleType = match ($saleTypeId) {
            1 => 'exclusive',
            2 => 'duo',
            3 => 'trio',
            4 => 'quad',
            default => 'unverified'
        };

        return match ($priceType) {
            'percentage-discount' => $floorPrice * (1 - ($priceOption / 100)),
            'dollar-discount'     => $floorPrice - $priceOption,
            'flat'                => $flatPrices[$saleType],
            default               => $priceOption,
        };
    }

    /**
     * Get the price this campaign would have paid for the new assumed sale type
     *
     * @param $qualityTierId
     * @param $saleTypeId
     * @param $countyLocationId
     * @param $stateLocationId
     * @param $floorPrice
     * @param array|null $campaignFloors
     * @param array|null $campaignBids
     * @return float
     */
    private function getCampaignPrice($qualityTierId, $saleTypeId, $countyLocationId, $stateLocationId, $floorPrice, ?array $campaignFloors, ?array $campaignBids): float
    {
        $bidPrice = $this->getExactBidPrice($campaignBids, $countyLocationId, $stateLocationId, $qualityTierId, $saleTypeId);
        if ($bidPrice) {
            return $bidPrice;
        }

        $customFloorPrice = $this->getExactCustomFloorPrice($campaignFloors, $countyLocationId, $stateLocationId, $qualityTierId, $saleTypeId);
        return $customFloorPrice ?? $floorPrice;
    }

    /**
     * @param $floorPrices
     * @param $countyLocationId
     * @param $stateLocationId
     * @param $qualityTierId
     * @param $salesTypeId
     * @param $industryServiceId
     * @return int
     */
    private function getFloorPrice($floorPrices, $countyLocationId, $stateLocationId, $qualityTierId, $salesTypeId, $industryServiceId): int
    {
        if($salesTypeId === self::UNVERIFIED_SALE_TYPE_ID){$qualityTierId = 1;}

        $floorPrices = $floorPrices[$industryServiceId];
        $floorPriceCount = count($floorPrices);

        $countyFloor = null;
        for ($i = 0; $i < $floorPriceCount; $i++) {
            $price = $floorPrices[$i];
            if (
                $countyFloor === null &&
                $price['location_id'] == $countyLocationId &&
                $price['quality_tier_id'] == $qualityTierId &&
                $price['sale_type_id'] == $salesTypeId
            ) {
                $countyFloor = $price;
                break;
            }
        }

        if($countyFloor) {
            return $countyFloor['price'];
        }

        $stateFloor = null;
        for ($i = 0; $i < $floorPriceCount; $i++) {
            $price = $floorPrices[$i];
            if (
                $stateFloor === null &&
                $price['location_id'] == $stateLocationId &&
                $price['quality_tier_id'] == $qualityTierId &&
                $price['sale_type_id'] == $salesTypeId
            ) {
                $stateFloor = $price;
                break;
            }
        }

        return $stateFloor['price'];
    }

    /**
     * @param $dailyBudget
     * @param $currentSpend
     * @param $startTimestamp
     * @param $tentativeLeadTimestamp
     * @param int $reactivationTimestamp
     * @return bool
     */
    private function hasAvailableBudget($dailyBudget, $currentSpend, $startTimestamp, $tentativeLeadTimestamp, $reactivationTimestamp = 0): bool
    {
        if($dailyBudget == 'no-limit' || $dailyBudget == 0) {
            return true;
        }

        $maxBudgetUsage       = 115;
        $timestampBudgetStart = max($startTimestamp, $reactivationTimestamp);
        $daysSinceBudgetStart = floor(($tentativeLeadTimestamp - $timestampBudgetStart) / 86400) + 1;
        $availableBudget      = ($dailyBudget * $daysSinceBudgetStart * ($maxBudgetUsage / 100)) - $currentSpend;

        return $availableBudget > 0;
    }

    public function getData(array $data): array
    {
        $startDate = Carbon::createFromFormat("Y-m-d", $data['start_date'])->startOfDay();
        $endDate   = Carbon::createFromFormat("Y-m-d", $data['end_date'])->endOfDay();

        $locationIds        = $data['location_ids'];
        $industry           = $data['industry'];
        $industryServiceIds = $this->determineIndustryServiceIds($industry);
        $leadTypes          = $data['lead_types'];
        $campaign           = $data['campaign'];
        $auxiliaryCampaigns = $data['aux_campaigns'];

        // Setup data output for all campaigns
        $simulatedData = $this->createSimulatedDataStructure($campaign, $auxiliaryCampaigns);

        // Get eligible consumers
        $consumerProducts   = $this->profitabilityReportRepository->getConsumerProductsInZipCodes($locationIds, $industryServiceIds, $startDate, $endDate, in_array(self::LEAD_TYPE_VERIFIED, $leadTypes))->toArray();
        $weekdayConsumerIds = $this->getWeekdayConsumerIds($consumerProducts);

        // Get pricing data
        $consumerZipCodeLocationIds = array_unique(array_column($consumerProducts, 'location_id'));
        $mappedLocationIds          = $this->profitabilityReportRepository->getMappedStateAndCountyLocationIds($consumerZipCodeLocationIds);
        $pricingLocationIds         = $this->getPricingLocationIds($mappedLocationIds);
        $floorPrices                = $this->priceRepository->getFloorPricesForLocationsByService($pricingLocationIds, $industryServiceIds);

        // Get company specific pricing data
        $campaignIds    = $this->extractCampaignIds($consumerProducts);
        $campaignFloors = $this->priceRepository->getCampaignFloorsForLocations($campaignIds, $mappedLocationIds);
        $campaignBids   = $this->priceRepository->getCampaignBidsForLocations($campaignIds, $mappedLocationIds);

        $originCampaignModel             = $this->getExistingCampaignFromPayload($campaign);
        $goodToSellAsUnverifiedThreshold = Carbon::now()->subDays(3)->timestamp;
        $productCount                    = count($consumerProducts);

        for($i = 0; $i < $productCount; $i++) {
            $product = $consumerProducts[$i];
            $productAssignments = $product['product_assignments'];
            $soldLegsCount      = count($productAssignments);
            $mappedLocationId   = $mappedLocationIds[$product['location_id']];
            $countyLocationId   = $mappedLocationId['county_location_id'];
            $stateLocationId    = $mappedLocationId['state_location_id'];

            $originalRevenue = $soldLegsCount > 0 ? array_sum(array_column($productAssignments, 'cost')) : 0;

            $qualityTierId     = $this->determineQualityTier($product);
            $industryServiceId = $product['industry_service_id'];
            $maxContacts       = $product['max_contact_requests'] ?? 0;
            if ($maxContacts < 1) {
                continue;
            }

            $verifiedEligible   = $this->isLeadEligibleToBeSoldAsVerified($product, $leadTypes);
            $unverifiedEligible = $this->isLeadEligibleToBeSoldAsUnverified($product, $leadTypes, $goodToSellAsUnverifiedThreshold);
            if (!$verifiedEligible && !$unverifiedEligible) {
                continue;
            }

            $allocatedCompanyIds        = array_column($productAssignments, 'company_id');
            $mainCampaignIsEligible     = $this->isCampaignEligible($originCampaignModel, $product, $allocatedCompanyIds, $campaign['include_weekends'], $weekdayConsumerIds);
            $eligibleAuxiliaryCampaigns = $this->getEligibleAuxiliaryCampaigns($auxiliaryCampaigns, $product, $weekdayConsumerIds);
            $startTimeStamp             = $startDate->timestamp;
            $consumerCreatedAtTimestamp = Carbon::createFromDate($product['consumer_created_at'])->timestamp;

            foreach ($simulatedData['scenarios'] as $budgetOption => $pricingOptions) {
                foreach ($pricingOptions as $pricingOption => $scenarioData) {

                    // Get campaigns with available budget
                    $mainCampaignHasBudget           = $mainCampaignIsEligible && $this->hasAvailableBudget(
                            $budgetOption,
                            $simulatedData['scenarios'][$budgetOption][$pricingOption]['spend'],
                            $startTimeStamp,
                            $consumerCreatedAtTimestamp,
                        );
                    $auxCampaignsWithAvailableBudget = $this->getAuxiliaryCampaignsWithAvailableBudget(
                        $eligibleAuxiliaryCampaigns,
                        $simulatedData['scenarios'][$budgetOption][$pricingOption]['aux_campaigns'],
                        $startTimeStamp,
                        $consumerCreatedAtTimestamp
                    );

                    // Determine new sale type and standard floor price
                    $totalAvailableSimulatedCampaigns = ($mainCampaignIsEligible ? 1 : 0) + count($auxCampaignsWithAvailableBudget);
                    $newSalesTypeId                   = $verifiedEligible ? min($soldLegsCount + $totalAvailableSimulatedCampaigns, $maxContacts) : self::UNVERIFIED_SALE_TYPE_ID;
                    $floorPrice                       = $this->getFloorPrice($floorPrices, $countyLocationId, $stateLocationId, $qualityTierId, $newSalesTypeId, $industryServiceId);

                    // Get updated prices for all campaigns - allocated, main simulated, and auxiliary simulated
                    $allCampaignPricing = $this->getAllCampaignPricing(
                        $campaign,
                        $productAssignments,
                        $qualityTierId,
                        $newSalesTypeId,
                        $countyLocationId,
                        $stateLocationId,
                        $floorPrice,
                        $campaignFloors,
                        $campaignBids,
                        $mainCampaignHasBudget,
                        $pricingOption,
                        $auxCampaignsWithAvailableBudget
                    );

                    // Get new BRS
                    $brsCampaigns = $this->getSimulatedBRS($allCampaignPricing, $newSalesTypeId, $maxContacts);

                    // Update simulated data
                    $updatedRevenue               = array_sum(array_column($brsCampaigns, 'price')) ?? 0;
                    $updatedSoldLegsCount         = count($brsCampaigns);
                    $mainCampaignAllocation       = $this->getBrsCampaignByType($brsCampaigns, 'main');
                    $mainCampaignAllocation       = count($mainCampaignAllocation) > 0 ? $mainCampaignAllocation[0] : null;
                    $auxiliaryCampaignAllocations = $this->getBrsCampaignByType($brsCampaigns, 'auxiliary');

                    $newSalesTypeId = strval($newSalesTypeId);
                    if ($mainCampaignAllocation) {
                        $simulatedData['scenarios'][$budgetOption][$pricingOption]['new_revenue'] += max(0, $updatedRevenue - $originalRevenue);
                        $simulatedData['scenarios'][$budgetOption][$pricingOption]['lead_type_counts'][$newSalesTypeId]++;
                        $simulatedData['scenarios'][$budgetOption][$pricingOption]['spend']            += $mainCampaignAllocation['price'];
                        $simulatedData['scenarios'][$budgetOption][$pricingOption]['assigned_leads'][] = [
                            'consumer_id'    => $product['consumer_id'],
                            'timestampadded' => $consumerCreatedAtTimestamp,
                            'cost'           => $mainCampaignAllocation['price'],
                        ];
                        if ($updatedSoldLegsCount > $soldLegsCount) {
                            $simulatedData['scenarios'][$budgetOption][$pricingOption]['new_legs_sold_count']++;
                        }
                        if ($originalRevenue === 0) {
                            $simulatedData['scenarios'][$budgetOption][$pricingOption]['new_leads_sold_count']++;
                        }
                    }

                    foreach ($auxiliaryCampaignAllocations as $auxAllocation) {
                        $simulatedData['scenarios'][$budgetOption][$pricingOption]['aux_campaigns'][$auxAllocation['aux_index']]['spend'] += $auxAllocation['price'];
                    }
                }
            }
        }

        return $simulatedData;
    }

    /**
     * @param array $mappedLocationIds
     * @return array
     */
    private function getPricingLocationIds(array $mappedLocationIds): array
    {
        $locationIds = [];

        foreach($mappedLocationIds as $mappedLocationId){
            $locationIds[] = $mappedLocationId['state_location_id'];
            $locationIds[] = $mappedLocationId['county_location_id'];
        }

        return array_unique($locationIds);
    }

    /**
     * For now, we return the most popular/sold service in the industry
     *
     * select i.name, indser.name, sp.industry_service_id, count(*) from consumer_products cp
     * left join service_products sp on cp.service_product_id = sp.id
     * left join industry_services indser on sp.industry_service_id = indser.id
     * left join industries i on indser.industry_id = i.id
     * where good_to_sell
     * and exists(select * from product_assignments pa where pa.consumer_product_id = cp.id and budget_id <> 0)
     * group by i.name, indser.name, sp.industry_service_id;
     *
     * todo: in the future we will allow the use to select multiple - which is why we return an array
     *
     * @param string $industry
     * @return array
     */
    private function determineIndustryServiceIds(string $industry): array
    {
        return match ($industry) {
            Industry::ROOFING->getSlug() => [7],
            Industry::SOLAR->getSlug() => [1],
            Industry::HVAC->getSlug() => [15],
            Industry::WINDOWS->getSlug() => [21],
            Industry::SIDING->getSlug() => [22],
            default => null
        };
    }

    /**
     * @param array|null $campaignBids
     * @param $countyLocationId
     * @param $stateLocationId
     * @param $qualityTierId
     * @param $saleTypeId
     * @return float|null
     */
    private function getExactBidPrice(?array $campaignBids, $countyLocationId, $stateLocationId, $qualityTierId, $saleTypeId): ?float
    {
        if(!$campaignBids || count($campaignBids) === 0){
            return null;
        }

        $countyBid = array_filter($campaignBids['counties'], fn(array $bid) =>
            $bid['quality_tier_id'] === $qualityTierId &&
            $bid['sale_type_id'] === $saleTypeId &&
            $bid['location_id'] === $countyLocationId
        )[0] ?? null;

        if($countyBid){
            return $countyBid['price'];
        }

        $stateBid = array_filter($campaignBids['states'], fn(array $bid) =>
            $bid['quality_tier_id'] === $qualityTierId &&
            $bid['sale_type_id'] === $saleTypeId &&
            $bid['location_id'] === $stateLocationId
        )[0] ?? null;

        if($stateBid){
            return $stateBid['price'];
        }

        return null;
    }

    /**
     * @param array|null $campaignFloors
     * @param $countyLocationId
     * @param $stateLocationId
     * @param $qualityTierId
     * @param $saleTypeId
     * @return float|null
     */
    private function getExactCustomFloorPrice(?array $campaignFloors, $countyLocationId, $stateLocationId, $qualityTierId, $saleTypeId): ?float
    {
        if(!$campaignFloors || count($campaignFloors) === 0){
            return null;
        }

        $customCountyFloor = array_filter($campaignFloors['counties'], fn(array $floor) =>
            $floor['quality_tier_id'] === $qualityTierId &&
            $floor['sale_type_id'] === $saleTypeId &&
            $floor['location_id'] === $countyLocationId
                     )[0] ?? null;

        if($customCountyFloor){
            return $customCountyFloor['price'];
        }

        $customStateFloor = array_filter($campaignFloors['states'], fn(array $floor) =>
            $floor['quality_tier_id'] === $qualityTierId &&
            $floor['sale_type_id'] === $saleTypeId &&
            $floor['location_id'] === $stateLocationId
                    )[0] ?? null;

        if($customStateFloor){
            return $customStateFloor['price'];
        }

        return null;
    }

    /**
     * @param array $consumerProduct
     * @return int
     */
    private function determineQualityTier(array $consumerProduct): int
    {
        if (!$consumerProduct[EloquentQuote::SOLAR_LEAD]) {
            return 1;
        }
        $electricUsage       = $consumerProduct[EloquentQuote::ELECTRIC_COST];
        $bestTimeToCall      = $consumerProduct[EloquentQuote::BEST_TIME_TO_CALL];
        $bestTimeToCallOther = $consumerProduct[EloquentQuote::BEST_TIME_TO_CALL_OTHER];
        if (
            (!empty($electricUsage) && intval($electricUsage) >= LeadProcessingService::PREMIUM_ELECTRICAL_USAGE) ||
            !empty($bestTimeToCall) ||
            !empty($bestTimeToCallOther)
        ) {
            return 2;
        }
        return 1;
    }

    /**
     * @param array $consumerProducts
     * @return array
     */
    private function getWeekdayConsumerIds(array $consumerProducts): array
    {

        $weekdayConsumerProducts = array_filter($consumerProducts, function (array $product) {
            $goodToSellAt = $product['marked_good_to_sell_at'];
            if(!$goodToSellAt){return true;}
            $goodToSellAtUtc = Carbon::createFromTimeString($goodToSellAt);
            $tzOffset = $product['utc'];
            $goodToSellAtLocal = $goodToSellAtUtc->timezone($tzOffset);
            return !$goodToSellAtLocal->isWeekend() &&
                   !($goodToSellAtLocal->isFriday() && $goodToSellAtLocal->hour >= self::FIVE_PM);
        });

        return array_column($weekdayConsumerProducts, ConsumerProduct::FIELD_CONSUMER_ID);
    }

    /**
     * @param array $consumerProduct
     * @param array $leadTypes
     * @return bool
     */
    private function isLeadEligibleToBeSoldAsVerified(array $consumerProduct, array $leadTypes): bool
    {
        return in_array(self::LEAD_TYPE_VERIFIED, $leadTypes) &&
               (count($consumerProduct['product_assignments']) === 0 || $consumerProduct['product_assignments'][0]['budget_key'] === self::LEAD_TYPE_VERIFIED) &&
               $consumerProduct['good_to_sell'];
    }

    /**
     * @param array $consumerProduct
     * @param array $leadTypes
     * @param int $thresholdTimestamp
     * @return bool
     */
    private function isLeadEligibleToBeSoldAsUnverified(array $consumerProduct, array $leadTypes, int $thresholdTimestamp): bool
    {
        return in_array(self::LEAD_TYPE_UNVERIFIED, $leadTypes) &&
               (
                   ($consumerProduct['good_to_sell'] && count($consumerProduct['product_assignments']) === 0 && Carbon::createFromDate($consumerProduct['marked_good_to_sell_at'])->timestamp < $thresholdTimestamp) ||
                   !$consumerProduct['good_to_sell']
               );
    }

    /**
     * @param array $campaign
     * @return CompanyCampaign|null
     */
    private function getExistingCampaignFromPayload(array $campaign): ?array
    {
        return CompanyCampaign::query()->where(CompanyCampaign::FIELD_ID, $campaign['origin_campaign_id'])->first()?->toArray() ?? null;
    }

    /**
     * @param array|null $campaign
     * @param array $consumerProduct
     * @param array $allocatedCompanyIds
     * @param bool $includeWeekends
     * @param array $weekdayConsumerIds
     * @return bool
     */
    private function isCampaignEligible(?array $campaign, array $consumerProduct, array $allocatedCompanyIds, bool $includeWeekends, array $weekdayConsumerIds): bool
    {
        if(!$includeWeekends && !in_array($consumerProduct['consumer_id'], $weekdayConsumerIds)){return false;}
        if(!$campaign){return true;}
        return in_array($campaign['company_id'], $allocatedCompanyIds);
    }

    /**
     * @param array $auxiliaryCampaigns
     * @param array $consumerProduct
     * @param array $weekdayConsumerIds
     * @return array
     */
    private function getEligibleAuxiliaryCampaigns(array $auxiliaryCampaigns, array $consumerProduct, array $weekdayConsumerIds): array
    {
        return array_filter($auxiliaryCampaigns, function(array $campaign) use ($consumerProduct, $weekdayConsumerIds){
            if($campaign['include_weekends']){return true;}
            return in_array($consumerProduct['consumer_id'], $weekdayConsumerIds);
        });
    }

    /**
     * @param array $campaign
     * @param array $auxiliaryCampaigns
     * @return array
     */
    private function createSimulatedDataStructure(array $campaign, array $auxiliaryCampaigns): array
    {
        $simulatedData = ['price_type' => $campaign['price_type']];

        $simulatedData['scenarios'] = $this->getBudgetOptions($campaign['budget_min'], $campaign['budget_max'], $campaign['budget_interval'], $campaign['budget_use_no_limit']);
        if ($campaign['price_type'] === self::PRICE_TYPE_FLAT) {
            $simulatedData['scenarios'] = $this->addPricingOptions($simulatedData['scenarios'], $auxiliaryCampaigns, 1, 1, 1);
        } else {
            $simulatedData['scenarios'] = $this->addPricingOptions($simulatedData['scenarios'], $auxiliaryCampaigns, $campaign['price_min'], $campaign['price_max'], $campaign['price_interval']);
        }

        return $simulatedData;
    }

    /**
     * @param array $auxCampaigns
     * @param array $pricingOptionAuxLeadData
     * @param $startTimestamp
     * @param $leadTimestamp
     * @return array
     */
    private function getAuxiliaryCampaignsWithAvailableBudget(array $auxCampaigns, array $pricingOptionAuxLeadData, $startTimestamp, $leadTimestamp): array
    {
        return array_filter($auxCampaigns, fn(string $index) => $this->hasAvailableBudget(
            $auxCampaigns[$index]['budget'],
            $pricingOptionAuxLeadData[$index]['spend'],
            $startTimestamp,
            $leadTimestamp,
        ),
            ARRAY_FILTER_USE_KEY
        );
    }

    /**
     * @param array $campaignPrices
     * @param int $saleTypeId
     * @param int $maxContacts
     * @return array
     */
    private function getSimulatedBRS(array $campaignPrices, int $saleTypeId, int $maxContacts): array
    {
        usort($campaignPrices, fn($a, $b) => $a['price'] < $b['price']);
        return array_slice($campaignPrices, 0, min($saleTypeId, $maxContacts));
    }

    /**
     * @param array $mainCampaign
     * @param array $productAssignments
     * @param int $qualityTierId
     * @param int $newSalesTypeId
     * @param int $countyLocationId
     * @param int $stateLocationId
     * @param float $floorPrice
     * @param array $campaignFloors
     * @param array $campaignBids
     * @param bool $mainCampaignHasBudget
     * @param int $pricingOption
     * @param array $auxCampaignsWithAvailableBudget
     * @return array
     */
    private function getAllCampaignPricing(
        array $mainCampaign,
        array $productAssignments,
        int $qualityTierId,
        int $newSalesTypeId,
        int $countyLocationId,
        int $stateLocationId,
        float $floorPrice,
        array $campaignFloors,
        array $campaignBids,
        bool $mainCampaignHasBudget,
        int $pricingOption,
        array $auxCampaignsWithAvailableBudget
    ): array
    {
        // Get new prices for allocated legs
        $allocatedCampaignPrices = [];
        $productAssignmentCount = count($productAssignments);
        for($i = 0; $i < $productAssignmentCount; $i++) {
            $allocatedCampaignPrices[] = [
                'type'        => 'allocated',
                'company_id'  => $productAssignments[$i]['company_id'],
                'campaign_id' => $productAssignments[$i]['campaign_id'],
                'price'       => $this->getCampaignPrice(
                    $qualityTierId,
                    $newSalesTypeId,
                    $countyLocationId,
                    $stateLocationId,
                    $floorPrice,
                    $campaignFloors[$productAssignments[$i]['campaign_id']],
                    $campaignBids[$productAssignments[$i]['campaign_id']],

                )
            ];
        }

        // Get price for main simulated campaign
        $mainCampaignPrice = $mainCampaignHasBudget ?
            [[
                 'type'        => 'main',
                 'company_id'  => $mainCampaign['origin_campaign_id'],
                 'campaign_id' => $mainCampaign['origin_campaign_id'],
                 'price'       => $this->getSimulatedPrice(
                     $mainCampaign['price_type'],
                     $pricingOption,
                     $floorPrice,
                     $newSalesTypeId,
                     $mainCampaign['flat_prices']
                 )
             ]] : [];

        // Get prices for each auxiliary campaign
        $simulatedCampaignPrices = [];
        foreach ($auxCampaignsWithAvailableBudget as $index => $auxCampaign) {
            $simulatedCampaignPrices[] = [
                'type'        => 'auxiliary',
                'aux_index'   => $index,
                'company_id'  => null,
                'campaign_id' => null,
                'price'       => $this->getSimulatedPrice(
                    $auxCampaign['price_type'],
                    $pricingOption,
                    $floorPrice,
                    $newSalesTypeId,
                    $auxCampaign['flat_prices']
                )
            ];
        }

        return array_merge($allocatedCampaignPrices, $mainCampaignPrice, $simulatedCampaignPrices);
    }

    /**
     * @param array $consumerProducts
     * @return array
     */
    private function extractCampaignIds(array $consumerProducts): array
    {
        $campaignIds = [];
        foreach ($consumerProducts as $product) {
            foreach ($product['product_assignments'] as $assignment) {
                $campaignIds[] = $assignment['campaign_id'];
            }
        }
        return array_unique($campaignIds);
    }

    /**
     * @param array $brsCampaigns
     * @param string $string
     * @return array
     */
    private function getBrsCampaignByType(array $brsCampaigns, string $string): array
    {
        $campaigns = [];
        $campaignCount = count($brsCampaigns);
        for ($i = 0; $i < $campaignCount; $i++) {
            if($brsCampaigns[$i]['type'] === $string) {
                $campaigns[] = $brsCampaigns[$i];
            }
        }
        return array_values($campaigns);
    }

}
