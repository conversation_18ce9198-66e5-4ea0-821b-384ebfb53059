<?php

namespace App\Services;

use App\Models\Contract;
use App\Repositories\ContractsRepository;
use Exception;

class ContractService
{
    public function __construct(
        protected ContractsRepository       $contractRepository,
    ) {}

    /**
     * This function is responsible for safely updating contracts
     * We need to have a contract available for each website and type of contract
     * eg There should always be an active contract for fixr company registration
     *
     * @param Contract $contract
     * @param array $data
     * @return bool
     *
     * @throws Exception
     */
    public function updateContractFromDataPayload(Contract $contract, array $data): bool
    {
        $contract->fill($data);

        if(!$contract->isDirty())
            return false;

        $activeContractKeyPairBeforeChange = $this->contractRepository->getActiveContractForWebsiteAndKey(
            websiteId: $contract->getOriginal(Contract::FIELD_WEBSITE_ID),
            contract_key_id: $contract->getOriginal(Contract::FIELD_CONTRACT_KEY_ID)
        );

        $activeContractKeyPairAfterChange = $this->contractRepository->getActiveContractForWebsiteAndKey(
            websiteId: $contract->{Contract::FIELD_WEBSITE_ID},
            contract_key_id: $contract->{Contract::FIELD_CONTRACT_KEY_ID}
        );

        //if we are the active and the key par has changed
        if($contract->active && ($contract->isDirty(Contract::FIELD_CONTRACT_KEY_ID) || $contract->isDirty(Contract::FIELD_WEBSITE_ID)) )
        {
            //key pair we are leaving needs an active key pair
            if(isset($activeContractKeyPairBeforeChange) && $activeContractKeyPairBeforeChange->id === $contract->id)
                throw new Exception("There must be one active Contract for ". $activeContractKeyPairBeforeChange->website->name ." with key ". $activeContractKeyPairBeforeChange->contractKey->name);

            //the key pair we are going into can have something active
            if($activeContractKeyPairAfterChange)
                throw new Exception("There is already an active Contract for ". $activeContractKeyPairAfterChange->website->name ." with key ". $activeContractKeyPairAfterChange->contractKey->name);
        }

        return $contract->save();
    }

    /**
     * @param Contract $contract
     * @return bool
     * @throws Exception
     */
    public function activateContract(Contract $contract): bool
    {
        if($contract->active)
            throw new Exception("This contract is already active");

        $activeContract = $this->contractRepository->getActiveContractForWebsiteAndKey(
            websiteId: $contract->{Contract::FIELD_WEBSITE_ID},
            contract_key_id: $contract->{Contract::FIELD_CONTRACT_KEY_ID}
        );

        if($activeContract) {
            $activeContract->active = false;
            $activeContract->save();
        }

        $contract->active = true;

        return $contract->save();

    }
}