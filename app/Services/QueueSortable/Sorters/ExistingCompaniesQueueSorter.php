<?php

namespace App\Services\QueueSortable\Sorters;

use App\Contracts\QueueSorterContract;
use App\Enums\Company\CompanyAdminStatus;
use App\Enums\CompanyConsolidatedStatus;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyData;
use App\Models\Odin\ProductAssignment;
use App\Models\Prospects\NewBuyerProspect;
use App\Services\Prospects\ProspectHuntingService;
use Illuminate\Database\Eloquent\Builder;

class ExistingCompaniesQueueSorter implements QueueSorterContract
{
    /**
     * @param array $companyIds
     * @return Builder
     */
    private static function getSortedCompanyQuery(array $companyIds): Builder
    {
        return Company::query()
            ->selectRaw("companies.*, json_extract(company_data.payload, '$.google_review_count') reviews, json_extract(company_data.payload, '$.qr_top_500_company') top_500, (select max(created_at) from product_assignments where company_id = companies.id) last_lead, new_buyer_prospects.id prospect_id")
            ->whereIntegerInRaw(Company::TABLE . '.' . Company::FIELD_ID, $companyIds)
            ->leftJoin(CompanyData::TABLE, CompanyData::TABLE . '.' . CompanyData::FIELD_COMPANY_ID, '=', Company::TABLE . '.' . Company::FIELD_ID)
            ->leftJoin(NewBuyerProspect::TABLE, NewBuyerProspect::TABLE . '.' . NewBuyerProspect::FIELD_COMPANY_ID, '=', Company::TABLE . '.' . Company::FIELD_ID)
            ->orderByRaw("top_500 desc, last_lead desc, reviews desc, prospect_id desc");
    }

    /**
     * @return array
     */
    private static function getFilteredCompanyIds(): array
    {
        return Company::query()
            ->select(Company::TABLE . '.*')
            ->whereNot(Company::TABLE . '.' . Company::FIELD_ADMIN_STATUS, CompanyAdminStatus::COLLECTIONS)
            ->doesntHave('accountManager')
            ->doesntHave('businessDevelopmentManager')
            ->leftJoin(ProductAssignment::TABLE, function ($join) {
                $join->on(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID, '=', Company::TABLE . '.' . Company::FIELD_ID)
                    ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED, true)
                    ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CHARGEABLE, true)
                    ->whereDate(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED_AT, '>=', now()->subDays(ProspectHuntingService::LEAD_BUYER_RECENCY_THRESHOLD_DAYS));
            })
            ->whereNull(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID)
            ->pluck(Company::TABLE.'.'.Company::FIELD_ID)
            ->toArray();
    }

    /**
     * @return array
     */
    public function getSortedIds(): array
    {
        $filteredCompanyIds = self::getFilteredCompanyIds();
        $query              = self::getSortedCompanyQuery($filteredCompanyIds);
        return $query->pluck(Company::FIELD_ID)->toArray();
    }
}
