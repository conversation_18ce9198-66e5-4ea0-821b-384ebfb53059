<?php

namespace App\Services;

use App\Models\Legacy\Location;
use App\Models\Odin\ProductCountyBidPrice;
use App\Models\Odin\QualityTier;
use App\Models\SaleType;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Spatie\Activitylog\Models\Activity;

class ActivityLogService
{
    const string PAYLOAD_DETAILS = 'details';

    /**
     * @param Collection<Activity> $activities
     * @return Collection
     */
    public function transformBidLogBatch(Collection $activities): Collection
    {
        [$locationIds, $qualityTierIds, $saleTypeIds] = $activities->reduce(function(array $ids, Activity $activity) {
            $details = Arr::get($activity->properties, self::PAYLOAD_DETAILS, []);
            $stateLocationId = Arr::get($details, ProductCountyBidPrice::FIELD_STATE_LOCATION_ID);
            $countyLocationId = Arr::get($details, ProductCountyBidPrice::FIELD_COUNTY_LOCATION_ID);
            $saleTypeId = Arr::get($details, ProductCountyBidPrice::FIELD_SALE_TYPE_ID);
            $qualityTierId = Arr::get($details, ProductCountyBidPrice::FIELD_QUALITY_TIER_ID);
            if ($stateLocationId && !in_array($stateLocationId, $ids[0]))
                $ids[0][] = $stateLocationId;
            if ($countyLocationId && !in_array($countyLocationId, $ids[0]))
                $ids[0][] = $countyLocationId;
            if (!in_array($qualityTierId, $ids[1]))
                $ids[1][] = $qualityTierId;
            if (!in_array($saleTypeId, $ids[2]))
                $ids[2][] = $saleTypeId;
            return $ids;
        }, [ [], [], [] ]);

        $locations = Location::query()->select([Location::FIELD_ID, Location::STATE_ABBREVIATION, Location::COUNTY])->whereIn(Location::FIELD_ID, $locationIds)
            ->get()->mapWithKeys(fn (Location $location) => [$location->id => $location->county ?: $location->state_abbr])->toArray();
        $saleTypes = SaleType::query()->select([SaleType::FIELD_ID, SaleType::FIELD_NAME])->whereIn(SaleType::FIELD_ID, $saleTypeIds)
            ->get()->mapWithKeys(fn (SaleType $type) => [$type->id => $type->name])->toArray();
        $qualityTiers = QualityTier::query()->select([QualityTier::FIELD_ID, QualityTier::FIELD_NAME])->whereIn(QualityTier::FIELD_ID, $qualityTierIds)
            ->get()->mapWithKeys(fn (QualityTier $tier) => [$tier->id => $tier->name])->toArray();

        foreach ($activities as $activity) {
            $details = $activity->properties->get(self::PAYLOAD_DETAILS, []);
            $location = Arr::get($locations, $details[ProductCountyBidPrice::FIELD_COUNTY_LOCATION_ID] ?? $details[ProductCountyBidPrice::FIELD_STATE_LOCATION_ID] ?? '', '');
            $saleType = Arr::get($saleTypes, $details[ProductCountyBidPrice::FIELD_SALE_TYPE_ID], '');
            $qualityTier = Arr::get($qualityTiers, $details[ProductCountyBidPrice::FIELD_QUALITY_TIER_ID], '');
            $activity->description = "$location, $qualityTier $saleType";
        }

        return $activities;
    }
}