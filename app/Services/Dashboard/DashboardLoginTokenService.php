<?php

namespace App\Services\Dashboard;

use App\Models\Odin\CompanyUser;
use App\Models\Odin\DashboardLoginToken;
use App\Models\User;
use Carbon\Carbon;
use Ramsey\Uuid\Uuid;

class DashboardLoginTokenService
{
    /**
     * Handles searching for a token and returning one if it exists.
     *
     * @param string $token
     * @return DashboardLoginToken|null
     */
    public function getToken(string $token): ?DashboardLoginToken
    {
        /** @var DashboardLoginToken|null $model */
        $model = DashboardLoginToken::query()
            ->where(DashboardLoginToken::FIELD_TOKEN, $token)
            ->where(DashboardLoginToken::FIELD_EXPIRED, false)
            ->where(DashboardLoginToken::FIELD_EXPIRES_AT, '>=', Carbon::now())
            ->first();

        return $model;
    }

    /**
     * Handles burning a token and returning the user for logging in if token exists/valid.
     *
     * @param string $token
     * @return DashboardLoginToken|null
     */
    public function burnToken(string $token): ?DashboardLoginToken
    {
        $token = $this->getToken($token);

        if (!$token)
            return null;

        $token->expired = true;
        $token->save();

        return $token;
    }

    /**
     * Handles generating a token for a given company user.
     *
     * @param CompanyUser $companyUser
     * @param User|null $user
     * @param int|null $minuteExpiry
     * @return string
     */
    public function generateToken(CompanyUser $companyUser, ?User $user = null, ?int $minuteExpiry = 30): string
    {
        $token = new DashboardLoginToken();
        $token->company_id = $companyUser->company_id;
        $token->company_user_id = $companyUser->id;
        $token->shadower_id = $user?->id ?? null;
        $token->token = Uuid::uuid4();
        $token->expired = false;
        $token->expires_at = Carbon::now()->addMinutes($minuteExpiry);
        $token->save();

        return $token->token;
    }

    /**
     * Clean up old tokens
     *
     * @param int $olderThanDays
     * @return void
     */
    public function cleanUpTokens(int $olderThanDays): void
    {
        $ageLimit = now()->subDays($olderThanDays);
        DashboardLoginToken::query()
            ->where(DashboardLoginToken::FIELD_EXPIRES_AT, '<', $ageLimit)
            ->delete();
    }
}
