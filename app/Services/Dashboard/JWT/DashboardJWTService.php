<?php

namespace App\Services\Dashboard\JWT;

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Validation\UnauthorizedException;

class DashboardJWTService
{
    public function __construct(protected string $signingKey, protected int $defaultExpiry) {}

    protected function getBuilder(): DashboardTokenBuilder
    {
        return DashboardTokenBuilder::query()
            ->setSigningKey($this->signingKey)
            ->setExpiration($this->defaultExpiry);
    }

    /**
     * Generates the JWT token for the given user.
     *
     * @param int $userId
     * @return string
     */
    public function generate(int $userId): string
    {
        return $this->getBuilder()->forUser($userId)->getToken();
    }

    /**
     * Generates the JWT token for a given user shadowing a specific user.
     *
     * @param int $userId
     * @param int $shadowerId
     * @return string
     */
    public function generateShadowToken(int $userId, int $shadowerId): string
    {
        return $this->getBuilder()->forUser($userId)->setShadower($shadowerId)->getToken();
    }

    /**
     * Attempts to decode a given token.
     *
     * @param string $token
     * @return DashboardTokenModel|null
     */
    public function decode(string $token): ?DashboardTokenModel
    {
        try {
            $payload = (array)JWT::decode($token, new Key($this->signingKey, 'HS256'));

            return DashboardTokenModel::fromPayload($payload);
        } catch (\Exception $e) {
        }

        return null;
    }

    /**
     * Validates that a token is valid.
     *
     * @param string $token
     * @return bool
     */
    public function validate(string $token): bool
    {
        $decoded = $this->decode($token);

        return $decoded !== null;
    }

    /**
     * Refreshes a token for a new expiry.
     *
     * @param DashboardTokenModel $model
     * @return string
     */
    public function refresh(DashboardTokenModel $model): string
    {
        return $model->getShadowerId()
            ? $this->generateShadowToken($model->getUserId(), $model->getShadowerId())
            : $this->generate($model->getUserId());
    }

    /**
     * Refreshes a token for a new expiry.
     *
     * @param string $token
     * @return string
     */
    public function refreshToken(string $token): string
    {
        $decoded = $this->decode($token);
        if (!$decoded)
            throw new UnauthorizedException("Invalid Dashboard Token");

        return $this->refresh($decoded);
    }
}
