<?php

namespace App\Services\Dashboard\JWT;

use Illuminate\Support\Arr;

class DashboardTokenModel
{
    const FIELD_USER_ID        = 'user_id';
    const FIELD_ISSUED_AT      = 'issued_at';
    const FIELD_EXPIRES_AT     = 'expires_at';
    const FIELD_DATA           = 'data';
    const FIELD_USER_NAME      = 'user_name';
    const FIELD_USER_EMAIL     = 'user_email';
    const FIELD_COMPANY_ID     = 'company_id';
    const FIELD_COMPANY_NAME   = 'company_name';
    const FIELD_SHADOWER_ID    = 'shadower_id';
    const FIELD_SHADOWER_NAME  = 'shadower_name';
    const FIELD_SHADOWER_EMAIL = 'shadower_email';

    public function __construct(
        protected ?int    $userId = null,
        protected ?int    $issuedAt = null,
        protected ?int    $expiresAt = null,
        protected ?array  $data = [],
        protected ?string $userName = null,
        protected ?string $userEmail = null,
        protected ?int    $companyId = null,
        protected ?string $companyName = null,
        protected ?int    $shadowerId = null,
        protected ?string $shadowerName = null,
        protected ?string $shadowerEmail = null
    ) {}

    public static function fromPayload(array $payload): DashboardTokenModel
    {
        return new DashboardTokenModel(
            Arr::get($payload, self::FIELD_USER_ID, null),
            Arr::get($payload, self::FIELD_ISSUED_AT, null),
            Arr::get($payload, self::FIELD_EXPIRES_AT, null),
            Arr::get($payload, self::FIELD_DATA, null),
            Arr::get($payload, self::FIELD_USER_NAME, null),
            Arr::get($payload, self::FIELD_USER_EMAIL, null),
            Arr::get($payload, self::FIELD_COMPANY_ID, null),
            Arr::get($payload, self::FIELD_COMPANY_NAME, null),
            Arr::get($payload, self::FIELD_SHADOWER_ID, null),
            Arr::get($payload, self::FIELD_SHADOWER_NAME, null),
            Arr::get($payload, self::FIELD_SHADOWER_EMAIL, null),
        );
    }

    /**
     * @return int|null
     */
    public function getUserId(): ?int
    {
        return $this->userId;
    }

    /**
     * @return int|null
     */
    public function getIssuedAt(): ?int
    {
        return $this->issuedAt;
    }

    /**
     * @return int|null
     */
    public function getExpiresAt(): ?int
    {
        return $this->expiresAt;
    }

    /**
     * @return array|null
     */
    public function getData(): ?array
    {
        return $this->data;
    }

    /**
     * @return string|null
     */
    public function getUserName(): ?string
    {
        return $this->userName;
    }

    /**
     * @return string|null
     */
    public function getUserEmail(): ?string
    {
        return $this->userEmail;
    }

    /**
     * @return int|null
     */
    public function getCompanyId(): ?int
    {
        return $this->companyId;
    }

    /**
     * @return string|null
     */
    public function getCompanyName(): ?string
    {
        return $this->companyName;
    }

    /**
     * @return int|null
     */
    public function getShadowerId(): ?int
    {
        return $this->shadowerId;
    }

    /**
     * @return string|null
     */
    public function getShadowerName(): ?string
    {
        return $this->shadowerName;
    }

    /**
     * @return string|null
     */
    public function getShadowerEmail(): ?string
    {
        return $this->shadowerEmail;
    }
}
