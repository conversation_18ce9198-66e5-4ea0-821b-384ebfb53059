<?php

namespace App\Services\PubSub;

use App\Services\BillingLegacyAdminEventService;
use App\Campaigns\Testing\TestAllocationPubSubService;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Models\Legacy\EloquentCompany;
use App\Services\LegacyAdminEventService;
use App\Services\SalesBait\SalesBaitEventService;
use App\Services\Workflows\WorkflowEventService;
use Exception;

class SubscriptionService
{
    public function __construct(
        protected SalesBaitEventService $salesBaitEventService,
        protected WorkflowEventService $workflowEventService,
        protected LegacyAdminEventService $legacyAdminEventService,
        protected BillingLegacyAdminEventService $billingLegacyAdminEventService,
    ) {}

    public function handle(array $data): void
    {
        // Temporary added to handle charge refunds
        if ($data['type'] === 'billing' && $data['event'] === 'charge-refunded') {
            $this->billingLegacyAdminEventService->handleChangeRefunded($data);
        }

        switch ($data["type"]) {
            case 'sales-bait':
                $this->salesBaitEventService->handle($data["event"], $data);
                break;
            case 'legacy-admin':
                $this->legacyAdminEventService->handle($data["event"], $data);
                break;
            default:
                $this->workflowEventService->handle($data['type'], $data['event'], $data);
                break;
        }

        if (config('app.testing.future_allocation_industry_id') > 0) {
                $this->reEmitEventsForTesting([...$data]);
        }
    }

    /**
     * Temporary function for Solar allocation test
     * Currently only for keeping Campaign status synced for more accurate allocation comparisons
     * TODO: remove after allocation testing
     */
    private function reEmitEventsForTesting(array $data): void
    {
        try {
            if ($data['type'] === EventCategory::CAMPAIGNS->value && $data['event'] === EventName::STATUS_UPDATED->value) {
                /** @var EloquentCompany $legacyCompany */
                $legacyCompany = EloquentCompany::query()
                    ->where(EloquentCompany::REFERENCE, $data['company_reference'] ?? null)
                    ->first();
                if (!$legacyCompany || $legacyCompany->type !== 'installer')
                    return;

                /** @var TestAllocationPubSubService $pubSubService */
                $pubSubService = app(TestAllocationPubSubService::class);
                $pubSubService->handle(EventCategory::STAGING_TESTING->value, EventName::TESTING_SYNC_CAMPAIGN_STATUS->value, $data);
            }
        }
        catch(Exception $e) {
            logger()->debug("Allocation Test event re-emit failed - " . $e->getMessage());
        }
    }
}
