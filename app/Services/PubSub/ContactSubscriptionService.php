<?php

namespace App\Services\PubSub;

use App\DataModels\SalesBait\SubscriptionStatusDataModel;
use App\Models\Legacy\EloquentUser;
use App\Models\ContactSubscription;
use App\Models\Legacy\EloquentCompanyContact;
use App\Models\User;
use Illuminate\Support\Collection;

class ContactSubscriptionService
{
    public function __construct() {

    }

    /**
     * @param int|null $contactSubscriptionId
     * @param int|null $contactId
     * @param string|null $contactType
     * @param string|null $contactMethod
     * @param string|null $notificationType
     * @param bool|null $unsubscribed
     * @return ContactSubscription
     */
    public function updateOrCreateContactSubscription(
        ?int $contactSubscriptionId = null,
        ?int $contactId = null,
        ?string $contactType = null,
        ?string $contactMethod = null,
        ?string $notificationType = null,
        ?bool $unsubscribed = null
    ): ContactSubscription
    {
        return ContactSubscription::updateOrCreate(
            [
                ContactSubscription::FIELD_ID => $contactSubscriptionId
            ],
            [
                ContactSubscription::FIELD_CONTACT_ID => $contactId,
                ContactSubscription::FIELD_CONTACT_TYPE => $contactType,
                ContactSubscription::FIELD_CONTACT_METHOD => $contactMethod,
                ContactSubscription::FIELD_NOTIFICATION_TYPE => $notificationType,
                ContactSubscription::FIELD_UNSUBSCRIBED => $unsubscribed ? time() : 0
            ]
        );
    }

    /**
     * @param int|null $contactId
     * @param string|null $contactType
     * @param string|null $contactMethod
     * @param string|null $notificationType
     * @param bool|null $unsubscribed
     * @return Collection
     */
    public function searchContactSubscriptions(
        ?int $contactId = null,
        ?string $contactType = null,
        ?string $contactMethod = null,
        ?string $notificationType = null,
        ?bool $unsubscribed = null
    ): Collection
    {
        $query = ContactSubscription::query();

        if($contactId > 0) {
            $query->where(ContactSubscription::FIELD_CONTACT_ID, $contactId);
        }

        if($contactType) {
            $query->where(ContactSubscription::FIELD_CONTACT_TYPE, $contactType);
        }

        if($contactMethod) {
            $query->where(ContactSubscription::FIELD_CONTACT_METHOD, $contactMethod);
        }

        if($notificationType) {
            $query->where(ContactSubscription::FIELD_NOTIFICATION_TYPE, $notificationType);
        }

        if(isset($unsubscribed)) {
            $query->where(ContactSubscription::FIELD_UNSUBSCRIBED, $unsubscribed ? '>' : '=', 0);
        }

        return $query->get();
    }

    /**
     * @param int $id
     * @return ContactSubscription|null
     */
    public function getContactSubscriptionById(int $id): ?ContactSubscription
    {
        return ContactSubscription::find($id);
    }

    /**
     * @param int $contactSubscriptionId
     * @return bool
     */
    public function unsubscribe(int $contactSubscriptionId): bool
    {
        return (bool) ContactSubscription::query()
                                        ->where(ContactSubscription::FIELD_ID, $contactSubscriptionId)
                                        ->update([
                                             ContactSubscription::FIELD_UNSUBSCRIBED => time()
                                        ]);
    }

    /**
     * @param int $contactSubscriptionId
     * @return bool
     */
    public function deleteContactSubscription(int $contactSubscriptionId): bool
    {
        return (bool) ContactSubscription::query()->where(ContactSubscription::FIELD_ID, $contactSubscriptionId)->delete();
    }

    /**
     * @param Collection<int, EloquentCompanyContact> $contacts
     * @return Collection<int, SubscriptionStatusDataModel> // keyed by contact_id
     */
    public function getStatusesForContacts(Collection $contacts): Collection
    {
        $subscriptions = ContactSubscription::query()
                                        ->where(ContactSubscription::FIELD_CONTACT_TYPE, ContactSubscription::CONTACT_TYPE_CONTACT)
                                        ->whereIn(ContactSubscription::FIELD_CONTACT_ID, $contacts->pluck(EloquentCompanyContact::FIELD_CONTACT_ID)->toArray())
                                        ->get();

        return $contacts->map(function(EloquentCompanyContact $contact) use ($subscriptions) {
            $smsSubscription = $subscriptions->where(ContactSubscription::FIELD_CONTACT_ID, $contact->contactid)
                                             ->where(ContactSubscription::FIELD_CONTACT_METHOD, ContactSubscription::CONTACT_METHOD_SMS)
                                             ->first();

            $emailSubscription = $subscriptions->where(ContactSubscription::FIELD_CONTACT_ID, $contact->contactid)
                                             ->where(ContactSubscription::FIELD_CONTACT_METHOD, ContactSubscription::CONTACT_METHOD_EMAIL)
                                             ->first();

            return new SubscriptionStatusDataModel(
                $contact->contactid,
                $smsSubscription == null || $smsSubscription->unsubscribed == 0,
                $emailSubscription == null || $emailSubscription->unsubscribed == 0
            );
        })->keyBy('id');
    }

    /**
     * @param Collection<int, EloquentUser> $users
     * @return Collection<int, SubscriptionStatusDataModel> // keyed by user_id
     */
    public function getStatusesForUsers(Collection $users): Collection
    {
        $subscriptions = ContactSubscription::query()
                                        ->where(ContactSubscription::FIELD_CONTACT_TYPE, ContactSubscription::CONTACT_TYPE_USER)
                                        ->whereIn(ContactSubscription::FIELD_CONTACT_ID, $users->pluck(EloquentUser::USER_ID)->toArray())
                                        ->get()
                                        ->groupBy(ContactSubscription::FIELD_CONTACT_ID);

        return $users->map(function(EloquentUser $contact) use ($subscriptions) {
            $smsSubscription = $subscriptions->where(ContactSubscription::FIELD_CONTACT_ID, $contact->userid)
                                             ->where(ContactSubscription::FIELD_CONTACT_METHOD, ContactSubscription::CONTACT_METHOD_SMS)
                                             ->first();

            $emailSubscription = $subscriptions->where(ContactSubscription::FIELD_CONTACT_ID, $contact->userid)
                                               ->where(ContactSubscription::FIELD_CONTACT_METHOD, ContactSubscription::CONTACT_METHOD_EMAIL)
                                               ->first();

            return new SubscriptionStatusDataModel(
                $contact->userid,
                $smsSubscription == null || $smsSubscription->unsubscribed == 0,
                $emailSubscription == null || $emailSubscription->unsubscribed == 0
            );
        })->keyBy('id');
    }
}
