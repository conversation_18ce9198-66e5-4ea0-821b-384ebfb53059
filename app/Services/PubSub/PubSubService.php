<?php

namespace App\Services\PubSub;

use App\Enums\Odin\PubSubOrigin;
use Google\Cloud\PubSub\MessageBuilder;
use Google\Cloud\PubSub\PubSubClient;
use Illuminate\Support\Facades\App;

class PubSubService
{
    /**
     * @param string $type
     * @param string $event
     * @param array $params
     * @return array
     */
    public function handle(string $type, string $event, array $params): array
    {
        $params["type"] = $type;
        $params["event"] = $event;
        $params["origin"] = PubSubOrigin::PUBSUB_ORIGIN_ADMIN2->value;

        $pubsub = new PubSubClient([
            'keyFile' => json_decode(base64_decode(config('services.google.pubsub.service_account')), true),
            'projectId' => config('services.google.pubsub.project_id')
        ]);

        $topic = $pubsub->topic(config('services.google.pubsub.topic'));

        if(!App::isProduction() && config('services.google.pubsub.debug')) {
            logger()->info($params);
        }

        return $topic->publish(
            (new MessageBuilder)
                            ->setData(json_encode($params))
                            ->addAttribute('type', $type)
                            ->addAttribute('event', $event)
                            ->build()
        );
    }
}
