<?php

namespace App\Services\PubSub;

use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Pipeline\Pipeline;

class PubSubMiddlewareService
{
    use DispatchesJobs;

    public function __construct(
        protected array $registeredMiddleware
    ) {}

    /**
     * @param array $pubSubEvent
     * @return array
     * @throws BindingResolutionException
     */
    public function process(array $pubSubEvent): array
    {
        /** @var Pipeline $pipeline */
        $pipeline = app()->make(Pipeline::class);

        return $pipeline->send($pubSubEvent)
            ->through($this->registeredMiddleware)
            ->via('handle')
            ->then(fn($event) => $event);
    }

}
