<?php

namespace App\Services\QAAutomation\AutomationServices;


use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\QAAutomation\QAAutomationLog;
use App\Models\QAAutomation\QAAutomationRule;
use Exception;

class RegexQAAutomation
{

    /**
     * Constructor
     */
    public function __construct() {}

    /**
     * Regex Automation for Lead Processing
     * Returns true if lead passes regex checks
     * @param ConsumerProduct $consumerProduct
     * @return bool
     */
    public function qualifyConsumerProduct(ConsumerProduct $consumerProduct): bool
    {
        $consumer = $consumerProduct->consumer;
        $consumer->{'full_name'} = $consumer->getFullName();

        $regexRules = QAAutomationRule::query()
            ->where(QAAutomationRule::FIELD_TYPE, QAAutomationRule::TYPE_REGEX)
            ->where(QAAutomationRule::FIELD_ENABLED, true)
            ->get();

        foreach ($regexRules as $regexRule) {
            foreach ($regexRule->{QAAutomationRule::FIELD_FIELDS} as $field) {
                $match = $regexRule->{QAAutomationRule::FIELD_MATCH_SUCCESS};

                // Wrap regex check in try catch in case of malformed regex
                try {
                    $pattern = $regexRule->{QAAutomationRule::FIELD_EXPRESSION};
                    //checking regex can run - is valid
                    if (@preg_match($pattern, null) === false) {
                        throw new Exception("Invalid regex pattern: $pattern");
                    }
                    $match = preg_match($pattern, trim($consumer->{$field}));
                } catch (Exception $e) {
                    QAAutomationLog::create([
                        QAAutomationLog::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
                        QAAutomationLog::FIELD_ENTRY => 'preg_match exception with regex',
                        QAAutomationLog::FIELD_QA_AUTOMATION_RULE_ID => $regexRule->{QAAutomationRule::FIELD_ID},
                        QAAutomationLog::FIELD_ERROR_MESSAGE => $e->getMessage(),
                        QAAutomationLog::FIELD_LOG_LEVEL => QAAutomationLog::LEVEL_ERROR,
                    ]);
                }

                // If regex match found
                if (($match && !$regexRule->{QAAutomationRule::FIELD_MATCH_SUCCESS}) ||
                    (!$match && $regexRule->{QAAutomationRule::FIELD_MATCH_SUCCESS})
                ) {
                    QAAutomationLog::create([
                        QAAutomationLog::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
                        QAAutomationLog::FIELD_ENTRY => 'regex match: '.trim($consumer->{$field}),
                        QAAutomationLog::FIELD_QA_AUTOMATION_RULE_ID => $regexRule->{QAAutomationRule::FIELD_ID},
                        QAAutomationLog::FIELD_LOG_LEVEL => QAAutomationLog::LEVEL_LOG,
                    ]);
                    return false;
                }
            }
        }

        return true;
    }
}
