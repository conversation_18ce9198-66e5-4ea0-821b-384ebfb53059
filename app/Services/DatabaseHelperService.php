<?php

namespace App\Services;

use \Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\JoinClause;

class DatabaseHelperService
{
    /**
     * @return string
     */
    public static function readOnlyDatabase(): string
    {
        return config('database.databases.legacy');
    }

    /**
     * @return string
     */
    public static function database(): string
    {
        return config('database.databases.default');
    }

    /**
     * @param Builder $query
     * @param string $table
     *
     * @return bool
     */
    public static function joinExists(Builder $query, string $table): bool
    {
        return !!collect($query->joins)->first(fn(JoinClause $join) => $join->table === $table);
    }
}
