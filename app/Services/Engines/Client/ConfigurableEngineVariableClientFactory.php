<?php

namespace App\Services\Engines\Client;

use App\Models\ClientToken;
use App\Models\ClientTokenService;
use Exception;

class ConfigurableEngineVariableClientFactory
{
    /**
     * @param string $drive
     * @return ConfigurableEngineVariableClientService
     * @throws Exception
     */
    static function make(string $drive): ConfigurableEngineVariableClientService
    {
        return match ($drive) {
            'dummy' => throw new Exception('Dummy not supported'),
            default => self::createClient(),
        };
    }

    /**
     * @return ConfigurableEngineVariableClientService
     * @throws Exception
     */
    private static function createClient(): ConfigurableEngineVariableClientService
    {
        $token = ClientToken::query()
            ->whereHas(ClientToken::RELATION_CLIENT_TOKEN_SERVICE, function ($query) {
                $query->where(ClientTokenService::FIELD_SERVICE_KEY, ClientTokenService::FLOW_ENGINES_API_SERVICE_KEY);
            })
            ->latest()
            ->first();

        if (!$token) {
            throw new Exception('Token not found');
        }

        return new ConfigurableEngineVariableClientService(
            baseUrl: config('services.flow_engines.url'),
            token  : $token->{ClientToken::FIELD_CLIENT_TOKEN},
        );
    }
}
