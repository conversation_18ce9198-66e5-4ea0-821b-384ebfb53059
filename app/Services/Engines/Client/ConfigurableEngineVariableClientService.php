<?php

namespace App\Services\Engines\Client;

use App\Services\HttpClientService;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\RequestException;

class ConfigurableEngineVariableClientService extends HttpClientService
{
    public function __construct(
        protected string $baseUrl,
        protected string $token,
    )
    {
        // Remove right trailing slashes from base URL
        $cleanBaseUrl = rtrim($baseUrl, '/');

        parent::__construct(
            baseUrl: "$cleanBaseUrl/integration/v1/configurable-variables",
            headers: ['x-integration-token' => $token],
        );
    }

    /**
     * @param string|null $state
     * @param string|null $county
     * @param string|null $engine
     * @param string|null $key
     * @return array
     * @throws ConnectionException
     * @throws RequestException
     */
    public function getConfigurableEngineVariables(
        ?string $state = null,
        ?string $county = null,
        ?string $engine = null,
        ?string $key = null,
    ): array
    {
        $variableType = filled($county) ? 'county' : (filled($state) ? 'state' : null);

        $variableIdentifier = $state && $county
            ? implode('|', [$state, $county])
            : ($county ?? $state);

        return $this->get('/', [
            'engine_name'         => $engine,
            'variable_category'   => 'location',
            'variable_type'       => $variableType,
            'variable_identifier' => $variableIdentifier,
            'variable_key'        => $key,
        ]);
    }

    /**
     * @param array $payload
     * @return array
     * @throws ConnectionException
     * @throws RequestException
     */
    public function saveConfigurableEngineVariables(array $payload): array
    {
        return $this->post('/', $payload);
    }

    /**
     * @param int $id
     * @return array
     * @throws ConnectionException
     * @throws RequestException
     */
    public function deleteConfigurableEngineVariables(int $id): array
    {
        return $this->delete("/$id");
    }
}
