<?php

namespace App\Services;

use App\Enums\NotificationLinkType;
use App\Models\Legacy\EloquentCompany;
use App\Models\Notification;
use App\Models\User;
use App\Repositories\Legacy\CompanyCRMRepository;
use App\Repositories\NotificationRepository;
use App\Transformers\NotificationTransformer;
use App\Services\Broadcasting\PusherNotificationBroadcaster;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Spatie\Permission\Models\Role;

class NotificationService
{
    const DEFAULT_CONTACT = 0;
    const ALL_NOTIFICATIONS_SUCCEEDED_STATUS = 'status';
    const INDIVIDUAL_NOTIFICATION_STATUS = 'individual_status';

    /** @var NotificationRepository $notificationRepository */
    protected NotificationRepository $notificationRepository;

    /** @var CompanyCRMRepository $companyCRMRepository */
    protected CompanyCRMRepository $companyCRMRepository;

    /** @var NotificationTransformer $transformer */
    protected NotificationTransformer $transformer;

    /** @var PusherNotificationBroadcaster $broadcaster */
    protected PusherNotificationBroadcaster $broadcaster;

    /**
     * NotificationService constructor
     *
     * @param NotificationRepository        $notificationRepository
     * @param CompanyCRMRepository          $companyCRMRepository
     * @param NotificationTransformer       $transformer
     * @param PusherNotificationBroadcaster $broadcaster
     */
    public function __construct(
        NotificationRepository         $notificationRepository,
        CompanyCRMRepository           $companyCRMRepository,
        NotificationTransformer        $transformer,
        PusherNotificationBroadcaster  $broadcaster
    )
    {
        $this->notificationRepository  = $notificationRepository;
        $this->companyCRMRepository    = $companyCRMRepository;
        $this->transformer             = $transformer;
        $this->broadcaster             = $broadcaster;
    }

    /**
     * Creates a notification for user
     *
     * @param int $userId
     * @param int|null $fromId
     * @param string $subject
     * @param string $body
     * @param int|null $type
     * @param string|null $link
     * @param NotificationLinkType|null $linkType
     * @param array|null $payload
     * @return bool
     */
    public function createNotificationForUser(
        int     $userId,
        ?int    $fromId,
        string  $subject,
        string  $body,
        ?int    $type,
        ?string $link = null,
        ?NotificationLinkType $linkType = null,
        ?array $payload = null
    ): bool
    {

        try {
            User::query()->findOrFail($userId);
        } catch (ModelNotFoundException $exception) {
            logger()->error($exception->getMessage());
            return false;
        }

        if(!$fromId) $fromId = Notification::FROM_SYSTEM;
        if(!$type) $type = Notification::TYPE_DEFAULT;

        $notification = $this->notificationRepository->createNotification($userId, $fromId, $subject, $body, $type, $link, $linkType, $payload);
        if(!empty($notification)) {
            $this->dispatchNotification($notification);
            return true;
        }

        return false;
    }

    /**
     * Creates a notification for company
     *
     * @param int      $companyId
     * @param int|null $userId
     * @param string   $body
     * @param int|null $type
     * @return void
     */
    public function createNotificationForCompany(
        int    $companyId,
        ?int   $userId,
        string $body,
        ?int   $type
    ): void
    {
        /** @var EloquentCompany $company */
        $company = EloquentCompany::query()->findOrFail($companyId);

        /** @var User $user */
        $user = User::query()->findOrFail($userId);

        if($type == Notification::TYPE_ACTIONS)
            $this->companyCRMRepository->createAction(
                $company->{EloquentCompany::REFERENCE}, $user->{User::FIELD_LEGACY_USER_ID}, self::DEFAULT_CONTACT, $body);
    }

    /**
     * Creates notifications for each user assigned to a given role.
     *
     * @param int $roleId
     * @param int|null $fromId
     * @param string $subject
     * @param string $body
     * @param int|null $type
     * @param string|null $link
     * @param NotificationLinkType|null $linkType
     * @param array|null $payload
     * @return array
     */
    public function createNotificationForRole(
        int     $roleId,
        ?int    $fromId,
        string  $subject,
        string  $body,
        ?int    $type,
        ?string $link = null,
        ?NotificationLinkType $linkType = null,
        ?array $payload = null
    ): array
    {
        $notificationStatuses = [];
        $notificationStatuses[self::INDIVIDUAL_NOTIFICATION_STATUS] = [];
        $notificationStatuses[self::ALL_NOTIFICATIONS_SUCCEEDED_STATUS] = true;

        $userIds = Role::findById($roleId)->users()->pluck(User::FIELD_ID);

        if ($userIds->isEmpty()) {
            $notificationStatuses[self::ALL_NOTIFICATIONS_SUCCEEDED_STATUS] = false;
            return $notificationStatuses;
        }

        foreach ($userIds as $userId) {
           $notificationStatuses[self::INDIVIDUAL_NOTIFICATION_STATUS][$userId] = $this->createNotificationForUser($userId, $fromId, $subject, $body, $type, $link, $linkType, $payload);
        }

        if (in_array(false, $notificationStatuses[self::INDIVIDUAL_NOTIFICATION_STATUS],true)) {
            $notificationStatuses[self::ALL_NOTIFICATIONS_SUCCEEDED_STATUS] = false;
        }

        return $notificationStatuses;
    }

    /**
     * Dispatches a notification
     *
     * @param Notification $notification
     * @return void
     */
    protected function dispatchNotification(Notification $notification): void
    {
        $channels = ["private-notifications-".$notification->{Notification::FIELD_USER_ID}];
        $status = 'new-notification';
        $payload = $this->transformer->transformNotification($notification);

        $this->broadcastEvent($status, $channels, $payload);
    }

    /**
     * Handles broadcasting a single event
     *
     * @param string $event
     * @param array  $channels
     * @param array  $data
     * @return void
     */
    public function broadcastEvent(string $event, array $channels, array $data = []): void
    {
        $this->broadcaster->broadcast($channels, $event, $data);
    }
}
