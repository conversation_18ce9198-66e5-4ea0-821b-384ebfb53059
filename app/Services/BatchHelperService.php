<?php

namespace App\Services;

class BatchHelperService
{
    /**
     * @param int $batchCount
     * @param $arrayToBatch
     * @return array
     */
    public function chunkByBatchCount(int $batchCount, $arrayToBatch): array
    {
        $nominalBatchJobSize = floor(count($arrayToBatch) / $batchCount);
        $batches             = [];
        $offset              = 0;
        while (count($batches) < $batchCount && $offset < count($arrayToBatch) ) {
            $batch     = array_slice($arrayToBatch, $offset, $nominalBatchJobSize);
            $offset    += $nominalBatchJobSize;
            $batches[] = $batch;
        }
        // add remainder to final batch
        $remainder = array_slice($arrayToBatch, $offset);
        if (count($remainder) > 0) {
            $finalBatch                   = array_merge($batches[count($batches) - 1], $remainder);
            $batches[count($batches) - 1] = $finalBatch;
        }
        return $batches;
    }
}
