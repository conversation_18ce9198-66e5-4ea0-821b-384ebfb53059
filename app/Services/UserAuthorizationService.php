<?php

namespace App\Services;

use App\Enums\PermissionType;
use App\Enums\RoleType;
use App\Models\AccountManager;
use App\Models\AccountManagerClient;
use App\Models\Odin\Company;
use App\Models\Teams\Team;
use App\Models\Teams\TeamMember;
use App\Models\Territory\RelationshipManager;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class UserAuthorizationService
{
    public function canUpdateCompanySalesData(Company $company): bool
    {
        return $this->isCompanyBusinessDevelopmentManager($company) || $this->isCompanySuccessManager($company) || $this->isCompanyAccountManager($company) || $this->isCompanyOnboardingManager($company);
    }
    public function isCompanyOnboardingManager(Company $company): bool
    {
        $omId = $company->onboardingManager?->id;
        return $omId && $omId === Auth::id();
    }
    
    public function isCompanySuccessManager(Company $company): bool
    {
        $csmId = $company->customerSuccessManager?->id;
        return $csmId && $csmId === Auth::id();
    }

    /**
     * @param Company $company
     * @return bool
     */
    public function isCompanyAccountManager(Company $company): bool
    {
        $amId = $company->accountManager?->id;
        return $amId && $amId === Auth::id();
    }

    /**
     * @param Company $company
     * @return bool
     */
    public function isCompanyBusinessDevelopmentManager(Company $company): bool
    {
        $bdmId = $company->businessDevelopmentManager?->id;
        return $bdmId && $bdmId === Auth::id();
    }

    /**
     * @deprecated
     * @param Company $company
     * @return bool
     */
    public function isCompanyRelationshipManager(Company $company): bool
    {
        $userId = Auth::id();

        /** @var ?RelationshipManager $relationshipManager */
        $relationshipManager = $company->{Company::RELATION_RELATIONSHIP_MANAGER};

        if ($userId === $relationshipManager?->{RelationshipManager::FIELD_USER_ID}) {
            return true;
        }

        return false;
    }

    /**
     * @param Company $company
     * @return bool
     */
    public function canEditAccountManager(Company $company): bool
    {
        /** @var ?User $user */
        $user = Auth::user();

        if ($user === null) {
            return false;
        }

        if($user->hasRole(['admin', 'sales-manager']) || $user->is($company->accountManager)) {
            return true;
        };

        $companyAccountManagerId = $company->accountManager?->id;

        if ($user->hasPermissionTo(PermissionType::CAN_REALLOCATE_ALL->value)) {
            return true;
        }

        if ($user->hasPermissionTo(PermissionType::CAN_REALLOCATE_TEAM->value)) {
            // Teams to which the logged-in user is related either as a leader or as a member
            $teams = collect([
                ...$user->{User::RELATION_LEADS_TEAMS},
                ...$user->{User::RELATION_TEAMS},
            ])->unique();

            return $teams
                ->map(fn (Team $team) => $team->{Team::RELATION_TEAM_MEMBERS})
                ->flatten()
                ->pluck(TeamMember::FIELD_USER_ID)
                ->unique()
                ->contains($companyAccountManagerId);
        }

        return false;
    }

    public function canViewAccountManager(Company $company): bool
    {
        return $this->canEditAccountManager($company)
            || Auth::user()->hasRole([RoleType::ADMIN->value, RoleType::SALES_MANAGER->value])
            || $this->isCompanyRelationShipManager($company);
    }

    public function canEditSuccessManager(Company $company): bool
    {
        return Auth::user()->hasRole(['admin', 'sales-manager']) || Auth::user()->is($company->customerSuccessManager);
    }

    public function canEditBusinessDevelopmentManager(Company $company): bool
    {
        return Auth::user()->hasRole(['admin', 'sales-manager']) || Auth::user()->is($company->businessDevelopmentManager);
    }

    /**
     * @param Company $company
     *
     * @return bool
     */
    public function canEditOnboardingManager(Company $company): bool
    {
        return Auth::user()->hasRole(['admin', 'sales-manager']) || Auth::user()->is($company->onboardingManager);
    }

    /**
     * @param Company $company
     *
     * @return bool
     */
    public function canEditSalesDevelopmentRepresentative(Company $company): bool
    {
        return Auth::user()->hasRole(['admin', 'sales-manager']) || Auth::user()->is($company->salesDevelopmentRepresentative);
    }
}
