<?php

namespace App\Services;

use App\Jobs\Google\PostUserGrantGoogleAccessJob;
use App\Models\GoogleUserToken;
use App\Services\Google\GoogleClientService;

class GoogleOAuthService
{
    public function __construct(
        protected GoogleClientService $googleClientService
    )
    {

    }

    /**
     * @param string $code
     * @param string $scopes
     * @param int $userId
     * @param string $service
     * @return void
     */
    public function createUserToken(
        string $code,
        string $scopes,
        int $userId,
        string $service
    ): void
    {
        $accessToken = $this->googleClientService->handleAuthCallback($code);

        GoogleUserToken::query()->where([
            GoogleUserToken::FIELD_USER_ID => $userId,
            GoogleUserToken::FIELD_SERVICE => $service,
        ])->delete();

        $googleToken = GoogleUserToken::query()->create([
            GoogleUserToken::FIELD_USER_ID       => $userId,
            GoogleUserToken::FIELD_SERVICE       => $service,
            GoogleUserToken::FIELD_SCOPES        => $scopes,
            GoogleUserToken::FIELD_TOKEN         => $accessToken->getAccessToken(),
            GoogleUserToken::FIELD_REFRESH_TOKEN => $accessToken->getRefreshToken(),
        ]);

        PostUserGrantGoogleAccessJob::dispatch($googleToken);
    }
}
