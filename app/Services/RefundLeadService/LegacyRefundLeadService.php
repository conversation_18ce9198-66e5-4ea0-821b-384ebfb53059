<?php

namespace App\Services\RefundLeadService;

use App\DTO\Billing\Refund\InvoiceRefundRequestResponseDTO;
use App\DTO\LeadRefund\LeadRefundItemFormattedData;
use App\Models\LeadRefundItem;
use App\Models\Legacy\EloquentInvoiceItem;
use App\Models\Legacy\EloquentTransactionItem;
use App\Models\Odin\Company;
use App\Models\Odin\ProductAssignment;
use App\Services\Legacy\Payments\CompanyBillingService;
use Illuminate\Support\Collection;
use Stripe\Exception\ApiErrorException;

class LegacyRefundLeadService extends RefundLeadService
{
    public function __construct(protected CompanyBillingService $companyBillingService)
    {

    }

    /**
     * @param LeadRefundItem $leadRefundItem
     * @return array
     */
    protected function getInvoiceData(LeadRefundItem $leadRefundItem): array
    {
        return [
            'invoice_item_id' => $leadRefundItem->{LeadRefundItem::RELATION_PRODUCT_ASSIGNMENT}->invoiceItem->{EloquentInvoiceItem::ID},
            'invoice_id'      => $leadRefundItem->{LeadRefundItem::RELATION_PRODUCT_ASSIGNMENT}->invoiceItem->invoiceid,
        ];
    }

    /**
     * Group items by invoice id, grab the payment related to each invoice and format the data to refund later
     * @param Collection<LeadRefundItemFormattedData> $items
     * @return array
     */
    public function formatToRefundData(Collection $items): array
    {
        $grouped = $items
            ->mapToGroups(fn(LeadRefundItemFormattedData $item) => [$item->getInvoiceId() => $item->toArray()])
            ->map(function (Collection $formattedLeadRefunds, $invoiceId) {

            });

        logger()->channel('lead-refunds')->info('Grouped by invoice id ' . json_encode($grouped));

        return $grouped->values()->all();
    }


    /**
     * @param Collection<LeadRefundItemFormattedData> $leadRefundItemFormattedData
     * @param int $invoiceId
     * @return InvoiceRefundRequestResponseDTO
     * @throws ApiErrorException
     */
    public function refundCashItems(
        Collection $leadRefundItemFormattedData,
        int $invoiceId
    ): InvoiceRefundRequestResponseDTO
    {
        $charge = EloquentTransactionItem::invoiceCharge($invoiceId)->first();

        $companyId = $leadRefundItemFormattedData->first()->getCompanyId();
        $externalChargeId = $charge ? $charge->{EloquentTransactionItem::VALUE} : null;
        $chargeId = $charge ? $charge->{EloquentTransactionItem::ID} : null;
        $leadRefundId = $leadRefundItemFormattedData->first()->getLeadRefundId();
        $invoiceItemIds = $leadRefundItemFormattedData->map(fn (LeadRefundItemFormattedData $item) => $item->getInvoiceItemId())->values()->toArray();
        $total = $leadRefundItemFormattedData->sum(fn (LeadRefundItemFormattedData $item) => $item->getValue());

        $company = Company::query()->findOrFail($companyId);

        $externalRefundId = $this->companyBillingService->makeRefundRequest(
            company       : $company,
            chargeId      : $externalChargeId,
            totalInDollars: $total,
            meta          : [
                'lead_refund_id'   => $leadRefundId,
                'invoice_item_ids' => implode(',', $invoiceItemIds),
            ],
        );

        return new InvoiceRefundRequestResponseDTO(
            externalRefundId: $externalRefundId,
            chargeId        : $chargeId,
            externalChargeId: $externalChargeId,
        );
    }

    /**
     * @param ProductAssignment $productAssignment
     * @return bool
     */
    public function checkIfProductAssignmentIsRefundable(ProductAssignment $productAssignment): bool
    {
        $invoice = $productAssignment->invoiceItem?->eloquentInvoice;

        return $invoice && EloquentTransactionItem::invoiceCharge($invoice->invoiceid)->exists();
    }
}
