<?php

namespace App\Services\RefundLeadService;

use App\DTO\Billing\Refund\InvoiceRefundRequestResponseDTO;
use App\DTO\LeadRefund\LeadRefundItemFormattedData;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Enums\Billing\Refunds\RefundReason;
use App\Exceptions\ValidatorException;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceItem;
use App\Models\Billing\InvoiceSnapshot;
use App\Models\LeadRefundItem;
use App\Models\Odin\ProductAssignment;
use App\Services\Billing\InvoiceRefunds\InvoiceRefundService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class A20RefundLeadService extends RefundLeadService
{
    public function __construct(
        protected InvoiceRefundService $invoiceRefundService
    )
    {

    }

    /**
     * @param LeadRefundItem $leadRefundItem
     * @return array
     */
    protected function getInvoiceData(LeadRefundItem $leadRefundItem): array
    {
        return [
            'invoice_item_id' => $leadRefundItem->{LeadRefundItem::RELATION_PRODUCT_ASSIGNMENT}->odinInvoiceItem->{InvoiceItem::FIELD_ID},
            'invoice_id'      => $leadRefundItem->{LeadRefundItem::RELATION_PRODUCT_ASSIGNMENT}->odinInvoiceItem->{InvoiceItem::FIELD_INVOICE_ID},
        ];
    }

    /**
     * @param Collection $leadRefundItemFormattedData
     * @param int $invoiceId
     * @return InvoiceRefundRequestResponseDTO
     * @throws ValidatorException
     * @throws BindingResolutionException
     */
    public function refundCashItems(Collection $leadRefundItemFormattedData, int $invoiceId): InvoiceRefundRequestResponseDTO
    {
        $invoiceItemIds = $leadRefundItemFormattedData->map(fn(LeadRefundItemFormattedData $leadRefundItemFormattedData) => $leadRefundItemFormattedData->getInvoiceItemId());
        $invoice = Invoice::query()->where(Invoice::FIELD_ID, $invoiceId)->firstOrFail();

        $invoiceRefundUuid = Str::uuid()->toString();

        $this->invoiceRefundService->validateAndIssueRefundRequest(
            invoice          : $invoice,
            refundItems      : $invoiceItemIds->toArray(),
            refundReason     : RefundReason::OTHER->value,
            authorType       : InvoiceEventAuthorTypes::SYSTEM,
            invoiceRefundUuid: $invoiceRefundUuid,
        );

        return new InvoiceRefundRequestResponseDTO(
            internalRefundUuid: $invoiceRefundUuid,
        );
    }

    /**
     * @param ProductAssignment $productAssignment
     * @return bool
     */
    public function checkIfProductAssignmentIsRefundable(ProductAssignment $productAssignment): bool
    {
        /** @var ?Invoice $invoice */
        $invoice = $productAssignment?->odinInvoiceItem?->invoice;

        if (!$invoice) {
            return false;
        }

        $lastInvoiceSnapshot = $invoice->lastSnapshot();

        if (!$lastInvoiceSnapshot) {
            return false;
        }

        $totalPaidInCents = $lastInvoiceSnapshot->{InvoiceSnapshot::FIELD_TOTAL_PAID} ?? 0;
        $productCostInCents = $productAssignment->{ProductAssignment::FIELD_COST} * 100;

        return $totalPaidInCents >= $productCostInCents;
    }
}
