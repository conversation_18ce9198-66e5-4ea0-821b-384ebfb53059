<?php

namespace App\Services\RefundLeadService;

use App\Enums\Billing\BillingVersion;
use Exception;

class RefundLeadServiceFactory
{
    public static function make(string $billingVersion): RefundLeadService
    {
        return match ($billingVersion) {
            BillingVersion::V1->value => app()->make(LegacyRefundLeadService::class),
            BillingVersion::V2->value => app()->make(A20RefundLeadService::class),
            default                   => throw new Exception('Billing version not supported')
        };
    }
}
