<?php

namespace App\Services\RefundLeadService;

use App\DTO\Billing\Refund\InvoiceRefundRequestResponseDTO;
use App\DTO\LeadRefund\LeadRefundItemFormattedData;
use App\Enums\LeadRefundItemChargeRefundStatus;
use App\Models\LeadRefund;
use App\Models\LeadRefundItem;
use App\Models\LeadRefundItemRefund;
use App\Models\Odin\ProductAssignment;
use Illuminate\Support\Collection;

abstract class RefundLeadService
{
    /**
     * @param LeadRefundItem $leadRefundItem
     * @return array
     */
    abstract protected function getInvoiceData(LeadRefundItem $leadRefundItem): array;

    /**
     * @param LeadRefundItem $leadRefundItem
     * @return LeadRefundItemFormattedData
     */
    public function formatLeadRefundItemData(LeadRefundItem $leadRefundItem): LeadRefundItemFormattedData
    {
        [
            'invoice_id'      => $invoiceId,
            'invoice_item_id' => $invoiceItemId,
        ] = $this->getInvoiceData($leadRefundItem);

        return new LeadRefundItemFormattedData(
            companyId       : $leadRefundItem->{LeadRefundItem::RELATION_REFUND}->{LeadRefund::FIELD_COMPANY_ID},
            type            : $leadRefundItem->{LeadRefundItem::FIELD_REFUND_TYPE}->value,
            invoiceItemId   : $invoiceItemId,
            leadRefundId    : $leadRefundItem->{LeadRefundItem::FIELD_LEAD_REFUND_ID},
            leadRefundItemId: $leadRefundItem->{LeadRefundItem::FIELD_ID},
            invoiceId       : $invoiceId,
            value           : $leadRefundItem->{LeadRefundItem::FIELD_VALUE},
        );
    }

    /**
     * @param Collection<LeadRefundItemFormattedData> $leadRefundItemFormattedData
     * @param int $invoiceId
     * @return InvoiceRefundRequestResponseDTO
     */
    abstract public function refundCashItems(
        Collection $leadRefundItemFormattedData,
        int $invoiceId
    ): InvoiceRefundRequestResponseDTO;

    /**
     * @param Collection $leadRefundItemFormattedData
     * @param int $invoiceId
     * @return void
     */


    /**
     * Since legacy doesn't support add credits directly to companies
     * We are creating the refund item as refunded without actually applying credits back to them
     * @param Collection $leadRefundItemFormattedData
     * @param int $invoiceId
     * @return void
     */
    public function refundCreditItems(
        Collection $leadRefundItemFormattedData,
        int $invoiceId
    ): void
    {
        $leadRefundItemIds = $leadRefundItemFormattedData->pluck(LeadRefundItemFormattedData::FIELD_LEAD_REFUND_ITEM_ID)->all();
        $leadRefundId = $leadRefundItemFormattedData->first()->getLeadRefundId();

        collect($leadRefundItemIds)->map(fn($itemId) => LeadRefundItemRefund::query()->create([
            LeadRefundItemRefund::FIELD_LEGACY_INVOICE_ID   => $invoiceId,
            LeadRefundItemRefund::FIELD_LEAD_REFUND_ITEM_ID => $itemId,
            LeadRefundItemRefund::FIELD_LEAD_REFUND_ID      => $leadRefundId,
            LeadRefundItemRefund::FIELD_LEGACY_CHARGE_ID    => null,
            LeadRefundItemRefund::FIELD_EXTERNAL_CHARGE_ID  => null,
            LeadRefundItemRefund::FIELD_EXTERNAL_REFUND_ID  => null,
            LeadRefundItemRefund::FIELD_STATUS              => LeadRefundItemChargeRefundStatus::REFUNDED->value,
            LeadRefundItemRefund::FIELD_ERROR_MESSAGE       => '',
        ]));
    }

    /**
     * @param Collection $leadRefunds
     * @return Collection<LeadRefundItemFormattedData>
     */
    public function formatLeadRefundItems(Collection $leadRefunds): Collection
    {
        return $leadRefunds->map(fn(LeadRefundItem $leadRefundItem) => $this->formatLeadRefundItemData($leadRefundItem));
    }

    /**
     * @param ProductAssignment $productAssignment
     * @return bool
     */
    abstract public function checkIfProductAssignmentIsRefundable(ProductAssignment $productAssignment): bool;
}
