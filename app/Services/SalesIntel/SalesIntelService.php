<?php

namespace App\Services\SalesIntel;

use App\DTO\SalesIntel\PersonDTO;
use App\Services\WebsiteSanitizerService;
use Exception;
use Illuminate\Support\Collection;

class SalesIntelService
{
    public function __construct(
        protected SalesIntel $salesIntel,
        protected WebsiteSanitizerService $websiteSanitizerService
    )
    {

    }

    /**
     * @param Collection<string> $websites
     * @return Collection<PersonDTO>
     * @throws Exception
     */
    public function findEmployees(Collection $websites): Collection
    {
        $sanitizedDomains = $this->websiteSanitizerService->extractDomain($websites);
        $validDomains = $this->websiteSanitizerService->filterOutBlacklistDomains($sanitizedDomains);

        if ($validDomains->isEmpty()) {
            throw new Exception('Allowed websites not found');
        }

        return $this->salesIntel->importPeople(
            domains: $validDomains->join(","),
            model  : '',
        );
    }
}
