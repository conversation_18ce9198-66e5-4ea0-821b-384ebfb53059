<?php

namespace App\Services\Odin\SimilarWeb;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\RequestOptions;
use Psr\Http\Message\ResponseInterface;

class SimilarWebService
{
    private readonly Client $client;
    private readonly string $apiKey;

    public function __construct() {
        $this->client = new Client([
            'base_uri' => config('services.company_metrics.similar_web.base_url'),
        ]);

        $this->apiKey = config('services.company_metrics.similar_web.api_key');
    }

    /**
     * @throws GuzzleException
     */
    public function getKeywordsFromDomain(string $domain, SimilarWebQueryParams $queryParams)
    {
        $requestPath = "website/" . $domain . "/traffic-sources/paid-search";

        $response = $this->client->request('get', $requestPath, [
            RequestOptions::QUERY => $this->prepareQuery($queryParams),
        ]);

        return $this->parseClientResponse($response);
    }

    private function prepareQuery(SimilarWebQueryParams $queryParams): array
    {
        return array_merge((array)$queryParams, [
            'api_key' => $this->apiKey
        ]);
    }

    private function parseClientResponse(ResponseInterface $response) {
        return json_decode($response->getBody(), true);
    }
}
