<?php

namespace App\Services\Odin\SimilarWeb;

/**
 * Class SimilarWebQueryParams
 *
 * @package App\Services\Odin\SpyFu
 *
 * @property string query Domain or URL to search.
 * @property string start_date [optional] Start month (format: YYYY-MM) When start_date and end_date are null, default is retrieve data from the last 28 days
 * @property string end_date [optional] End month (format: YYYY-MM) When start_date and end_date are null, default is retrieve data from the last 28 days
 * @property string country [required] Country filter, as a 2-letter ISO country code, or "world" for worldwide. To see the country filters you have access to, please refer to the Check Capabilities endpoint.
 * @property string main_domain_only [optional] Return values for the main domain only ('true'), or include also the subdomains ('false')
 * @property string format [optional] Format in which the reply should be returned. Possible values: 'json' (default) or 'xml'.
 * @property string limit [optional] Select the number of results that are returned. Default = 100.
 * @property string offset [optional] Specify an offset from where to start returning data
 * @property string sort [optional] The metric you'd like the results to be ordered by
 * @property string asc [optional] The order of results. Enter "true" for ascending, or "false" for descending
 */

class SimilarWebQueryParams
{
    public ?string $start_date;
    public ?string $end_date;
    public ?string $country;
    public ?string $main_domain_only;
    public ?string $format;
    public ?string $limit;
    public ?string $offset;
    public ?string $sort;
    public ?string $asc;

    public function __construct(string $country, array $params = [])
    {
        $this->start_date = $params['start_date'] ?? null;
        $this->end_date = $params['end_date'] ?? null;
        $this->country = strtolower($country);
        $this->main_domain_only = $params['main_domain_only'] ?? null;
        $this->format = $params['format'] ?? null;
        $this->limit = $params['limit'] ?? null;
        $this->offset = $params['offset'] ?? null;
        $this->sort = $params['sort'] ?? null;
        $this->asc = $params['asc'] ?? null;
    }
}
