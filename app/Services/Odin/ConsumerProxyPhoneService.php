<?php

namespace App\Services\Odin;

use App\Enums\PhoneType;
use App\Models\Odin\Company;
use App\Models\Odin\Consumer;
use App\Models\Odin\ProductAssignment;
use App\Services\Twilio\TwilioPhoneNumberService;
use Exception;

class ConsumerProxyPhoneService
{
    /**
     * @param int $companyId
     * @param string|null $region
     *
     * @return string|null
     */
    public function getProxyPhone(int $companyId, ?string $region = null): ?string
    {
        $company = Company::query()->findOrFail($companyId);

        if (!$company->configuration?->consumer_proxy_phone_enabled) {
            return null;
        }

        $everyNthLead = (int) (100 / Consumer::PROXY_PHONE_PERCENT);
        $leadCount = $company->productAssignments()
            ->where(ProductAssignment::FIELD_DELIVERED_AT, '>=', now()->startOfMonth())
            ->count();

        if ($leadCount === 0 || $leadCount % $everyNthLead !== 0) {
            return null;
        }

        try {
            return app(TwilioPhoneNumberService::class)->acquireNumber(
                region: $region,
                type: PhoneType::CONSUMER_PROXY_PHONE,
                name: 'consumer_proxy_phone',
                configuration: [
                    'voiceUrl' => route('consumer-proxy-phone-voice'),
                    'smsUrl'   => route('consumer-proxy-phone-sms'),
                ]
            )->phone;
        } catch (Exception $exception) {
            logger()->error("Failed to get proxy phone for allocation to company: $companyId, error: {$exception->getMessage()}");

            return null;
        }
    }
}
