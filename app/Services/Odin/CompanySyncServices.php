<?php

namespace App\Services\Odin;

use App\Jobs\LegacyMigrations\MigrateLegacyAddressesJob;
use App\Jobs\LegacyMigrations\MigrateLegacyCompaniesJob;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentCompanyAddress;
use App\Models\Legacy\EloquentUser;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use Illuminate\Support\Carbon;

class CompanySyncServices
{
    /**
     * @param int $companyId
     * @param array $industries
     * @param array $industryServices
     *
     * @return bool|string
     */
    public function syncLegacyCompany(int $companyId, array $industries, array $industryServices): bool|string
    {
        /** @var Company|null $company */
        $company = Company::query()->where(Company::FIELD_LEGACY_ID, $companyId)->first();

        if ($company) return "Legacy company ({$companyId}) already exists in admin2 id: {$company->id}.";

        /** @var EloquentCompany $legacyCompany */
        $legacyCompany = EloquentCompany::query()->findOrFail($companyId);
        $addresses = $legacyCompany->companyAddresses->map(fn(EloquentCompanyAddress $companyAddress) => $companyAddress->address->addressid);

        dispatch_sync(new MigrateLegacyAddressesJob($addresses));
        dispatch_sync(new MigrateLegacyCompaniesJob(collect([$companyId]), $industries, $industryServices, false));

        /** @var Company $company */
        $company = Company::query()->where(Company::FIELD_LEGACY_ID, $companyId)->first();

        if (!$company) return "Legacy company ({$companyId}) was not created in admin2. Please check error log";

        $this->syncLegacyCompanyContact($legacyCompany, $company);
        $this->syncLegacyCompanyUser($legacyCompany, $company);

        return true;
    }

    /**
     * @param EloquentCompany $legacyCompany
     * @param Company $company
     *
     * @return void
     */
    public function syncLegacyCompanyContact(EloquentCompany $legacyCompany, Company $company): void
    {
        foreach ($legacyCompany->contacts as $contact) {
            CompanyUser::query()->updateOrCreate([
                CompanyUser::FIELD_LEGACY_ID => $contact->contactid,
                CompanyUser::FIELD_IS_CONTACT => 1
            ], [
                CompanyUser::FIELD_COMPANY_ID => $company->id,
                CompanyUser::FIELD_FIRST_NAME => $contact->firstname,
                CompanyUser::FIELD_LAST_NAME => $contact->lastname,
                CompanyUser::FIELD_TITLE => $contact->title,
                CompanyUser::FIELD_DEPARTMENT => '',
                CompanyUser::FIELD_EMAIL => $contact->email,
                CompanyUser::FIELD_CELL_PHONE => $contact->mobile,
                CompanyUser::FIELD_OFFICE_PHONE => $contact->phone,
                CompanyUser::FIELD_NOTES => $contact->notes,
                CompanyUser::FIELD_ZOOM_INFO_ID => $contact->zoom_info_id,
                CompanyUser::FIELD_CAN_LOG_IN => false,
                CompanyUser::FIELD_STATUS => $contact->status,
                CompanyUser::CREATED_AT => Carbon::createFromTimestamp($contact->timestampadded)->format('Y-m-d H:i:s'),
                CompanyUser::UPDATED_AT => Carbon::createFromTimestamp($contact->timestampupdated)->format('Y-m-d H:i:s'),
                CompanyUser::FIELD_DELETED_AT => $contact->deleted_at,
            ]);
        }
    }

    /**
     * @param EloquentCompany $legacyCompany
     * @param Company $company
     *
     * @return void
     */
    public function syncLegacyCompanyUser(EloquentCompany $legacyCompany, Company $company): void
    {
        foreach ($legacyCompany->users as $user) {
            CompanyUser::query()->updateOrCreate([
                CompanyUser::FIELD_LEGACY_ID => $user->userid,
                CompanyUser::FIELD_IS_CONTACT => 0
            ], [
                CompanyUser::FIELD_COMPANY_ID => $company->id,
                CompanyUser::FIELD_FIRST_NAME => $user->firstname,
                CompanyUser::FIELD_LAST_NAME => $user->lastname,
                CompanyUser::FIELD_TITLE => $user->title,
                CompanyUser::FIELD_DEPARTMENT => '',
                CompanyUser::FIELD_EMAIL => $user->email,
                CompanyUser::FIELD_PASSWORD => $user->password,
                CompanyUser::FIELD_CELL_PHONE => $user->phone,
                CompanyUser::FIELD_OFFICE_PHONE => $user->phone,
                CompanyUser::FIELD_NOTES => '',
                CompanyUser::FIELD_ZOOM_INFO_ID => '',
                CompanyUser::FIELD_CAN_LOG_IN => $user->status === EloquentUser::FIELD_STATUS_VALUE_ACTIVE,
                CompanyUser::FIELD_STATUS => $user->status,
                CompanyUser::FIELD_FAILED_LOGIN_COUNT => $user->failedlogincount,
                CompanyUser::FIELD_DELETED_AT => $user->deleted_at,
            ]);
        }
    }
}
