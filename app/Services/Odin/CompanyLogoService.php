<?php

namespace App\Services\Odin;

use App\Contracts\Services\CompanyLogoServiceContract;
use App\Models\Odin\Company;
use App\Services\Companies\CompanyProfileService;
use App\Services\FileUploadHelperService;
use Illuminate\Http\UploadedFile;
use Throwable;

class CompanyLogoService implements CompanyLogoServiceContract
{
    /**
     * @param CompanyProfileService $profileService
     */
    public function __construct(
        protected CompanyProfileService $profileService,
    ) {}

    /**
     * @inheritDoc
     */
    public function handle(Company $company): bool|string
    {
        /** @var UploadedFile|null $file */
        $file = $this->prepareFileForUpload($company->{Company::FIELD_LINK_TO_LOGO});

        if (!empty($file)) {
            $newLogo = $this->prepareNewUrlForUpload($company, $file->getClientOriginalName());

            if($company->{Company::FIELD_LINK_TO_LOGO} !== $newLogo) {
                try {
                    $url = $this->uploadFileToCloud($company, $file);

                    if (!is_null($url) && strlen(trim($url)) > 0) {
                        $company->{Company::FIELD_LINK_TO_LOGO} = $newLogo;
                        $company->save();

                    } else {
                        return "Skipping the logo update for company `{$company->{Company::FIELD_ID}}` since it couldn't be uploaded to the cloud.";
                    }
                } catch (Throwable $e) {
                    return "Something went wrong while uploading the logo for company: `{$company->{Company::FIELD_ID}}`. Exception: " . $e->getMessage()."\n\n";
                }
            }
        } else {
            return "The company `{$company->{Company::FIELD_ID}}` carries an invalid logo: {$company->{Company::FIELD_LINK_TO_LOGO}}";
        }

        return true;
    }

    /**
     * @inheritDoc
     */
    public function prepareNewUrlForUpload(Company $company, string $fileName): string
    {
        $path          = $this->profileService->getLogoUploadPath($company);
        $urlWithBucket = $this->getCompanyLogoBaseUrlWithBucket();

        return $urlWithBucket . "/{$path}/{$fileName}";
    }

    /**
     * @inheritDoc
     */
    public function prepareFileForUpload(string $fileUrl): ?UploadedFile
    {
        if(!strlen(trim($fileUrl)) || !@getimagesize($fileUrl)) {
            return null;
        }

        $linkValues = explode('/', $fileUrl);
        $fileName   = end($linkValues);

        return FileUploadHelperService::createFromUrl(
            url            : $fileUrl,
            originalName   : $fileName,
            throwException : false,
        );
    }

    /**
     * @inheritDoc
     */
    public function uploadFileToCloud(Company $company, UploadedFile $file): ?string
    {
        $path = $this->profileService->getLogoUploadPath($company);

        return $this->profileService->uploadInCloud($path, $file);
    }

    /**
     * @inheritDoc
     */
    public function getCompanyLogoBaseUrlWithBucket(): string
    {
        $url    = config('services.google.storage.urls.company_logos_file_url');
        $bucket = config('services.google.storage.buckets.company_logos');

        return rtrim($url, '/') . "/{$bucket}";
    }
}
