<?php

namespace App\Services\Odin;

use App\Models\Phone;
use App\Models\Role;
use App\Models\User;
use App\Repositories\UserRepository;
use App\Services\Odin\API\OdinAuthoritativeAPILegacySyncService;
use App\Services\Odin\Logging\PermissionLoggerService;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class UserService
{
    public function __construct(
        protected UserRepository                        $userRepository,
        protected OdinAuthoritativeAPILegacySyncService $odinAuthoritativeAPILegacySyncService,
        protected PermissionLoggerService               $permissionLoggerService,
    )
    {

    }


    /**
     * @param string $name
     * @param string $email
     * @param bool $force2fa
     * @param bool $createInLegacy
     * @param array $roles
     * @param int|null $phoneId
     * @param string|null $legacyId
     * @param string|null $slackUsername
     * @param User|null $user
     * @param array|null $permissions
     *
     * @return User
     * @throws Exception
     */
    public function saveUser(
        string $name,
        string $email,
        bool $force2fa,
        bool $createInLegacy,
        array $roles,
        ?int $phoneId = null,
        ?string $legacyId = null,
        ?string $slackUsername = null,
        ?User $user = null,
        ?array $permissions = null
    ): User
    {
        $userData = [
            User::FIELD_NAME => $name,
            User::FIELD_EMAIL => $email,
            User::FIELD_LEGACY_USER_ID => $createInLegacy && empty($legacyId) ? $this->createLegacyUser($name, $email)['legacy_user_id'] : $legacyId,
            User::FIELD_SLACK_USERNAME => $slackUsername,
            User::FIELD_FORCE_TWO_FACTOR_AUTH => $force2fa,
            User::FIELD_UPDATED_BY_ID => Auth::id(),
        ];

        if (!$user) {
            $user = $this->userRepository->createUserFromAttributes([
                ...$userData,
                User::FIELD_CREATED_BY_ID => Auth::id(),
                User::FIELD_PASSWORD => Str::random(32)
            ]);
        } else {
            $user->fill($userData);
            $user->save();
        }

        $oldRoles = $user->{User::RELATION_ROLES}->pluck(Role::FIELD_ID)->all();
        $assignedRoles = Role::query()->whereIn(Role::FIELD_NAME, $roles)->pluck(Role::FIELD_ID);

        $this->permissionLoggerService->logUserRoleChange($user, $oldRoles, $assignedRoles->all());

        $user->{User::RELATION_ROLES}()->sync(
            $assignedRoles
        );

        $phones = collect($phoneId ?? []);

        $userPhones = $user->{User::RELATION_PHONES}->pluck(Phone::FIELD_ID)->unique();

        $toCreate = $phones->diff($userPhones);
        $toDelete = $userPhones->diff($phones);

        $user->{User::RELATION_PHONES}()->updateExistingPivot($toDelete, [
            'deleted_at' => now(),
            'updated_at' => now(),
        ]);

        $user->{User::RELATION_PHONES}()->attach($toCreate, [
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        if ($permissions !== null) {
            $oldPermissions = $user->permissions()->get()->pluck('name')->toArray();
            $user->syncPermissions($permissions);
            $this->permissionLoggerService->logUserDirectPermissionChange(
                user: $user,
                oldPermissions: $oldPermissions,
                newPermissions: $user->permissions()->get()->pluck('name')->toArray()
            );
        }

        return $user;
    }

    /**
     * @param array $permissions
     * @return Collection
     */
    public function getUsersByRolePermissions(array $permissions): Collection
    {
        return $this->userRepository->getUsersByRolePermissions($permissions);
    }

    /**
     * @param $name
     * @param $email
     * @return array|mixed|void
     * @throws Throwable|Exception
     */
    protected function createLegacyUser($name, $email) {
        try {
            return $this->odinAuthoritativeAPILegacySyncService->post('/user/create', [
                'name' => $name,
                'email' => $email
            ])->throw()->json()['data'];
        }
        catch(Exception $e) {
            logger()->error($e->getMessage());
            throw new Exception("There was a problem creating the legacy user. Please Try again");
        }
    }

    /**
     * @param bool $showDeactivated
     * @param string|null $userDetail
     * @return Collection
     */
    public function listUsers(
        bool $showDeactivated,
        ?string $userDetail = null,
    ): Collection
    {
        return $this->userRepository->listUsers(
            showDeactivated : $showDeactivated,
            userDetail      : $userDetail
        );
    }

}
