<?php

namespace App\Services\Odin\ConsumerFieldModuleVisibilityService;

use App\Enums\Odin\SystemModule;
use App\Models\Odin\ConsumerCommonField;
use App\Models\Odin\ConsumerFieldModuleVisibility;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\ConsumerGetterFactory;
use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\Enums\ConsumerFieldCategory;
use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\Enums\ConsumerFieldSource;
use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\Field;
use Illuminate\Database\Eloquent\Collection;

/**
 * Todo - Create factories and custom getters for complex fields
 */
class ConsumerFieldModuleVisibilityService
{
    private function getActiveFields(Collection $fieldsVisibility): Collection
    {
        return $fieldsVisibility
            ->filter(function ($item) {
                return $item['is_visible'];
            })->unique(fn ($fieldVisibility) => $fieldVisibility->field->key);
    }

    public function getColumns(Collection $fieldsVisibility): array
    {
        $columns = [];

        foreach ($fieldsVisibility as $activeField) {
            $field = $activeField->field;
            $columns[] = [
                'name' => $field->name,
                'key' => $field->key,
            ];
        }

        return $columns;
    }

    public function filterOne(ConsumerProduct $consumerProduct, Collection $fieldsVisibility, bool $groupAllFieldsIntoOneArray = false): array
    {
        $activeFields = $this->getActiveFields($fieldsVisibility);

        if ($activeFields->isEmpty()) return [];

        $data = [
            ConsumerFieldSource::COMMON->value => [],
            ConsumerFieldSource::CONFIGURABLE->value => [],
        ];

        foreach ($activeFields as $activeField) {
            try {
                $field = $activeField->field;

                if ($field) {
                    $field = Field::fromArray([
                        Field::FIELD_KEY => $field->key,
                        Field::FIELD_NAME => $field->name,
                        Field::FIELD_HIDE_NAME => false,
                        Field::FIELD_CATEGORY => $field instanceof ConsumerCommonField
                            ? $field->{ConsumerCommonField::FIELD_CATEGORY}->value
                            : ConsumerFieldCategory::TEXT->value,
                        Field::FIELD_SOURCE => $field instanceof ConsumerCommonField
                            ? ConsumerFieldSource::COMMON->value
                            : ConsumerFieldSource::CONFIGURABLE->value
                    ]);

                    $value = $this->getValue($field, $consumerProduct);

                    $field->setValue($value);

                    $data[$field->source->value][] = $field->toArray();
                }
            } catch (\Exception $exception) {
                logger()->error($exception);
            }
        }

        if ($groupAllFieldsIntoOneArray) {
            return array_merge(
                $data[ConsumerFieldSource::COMMON->value],
                $data[ConsumerFieldSource::CONFIGURABLE->value],
            );
        }

        return $data;
    }



    private function getValue(Field $field, ConsumerProduct $consumerProduct){
        $value = null;

        try {
            $getter = ConsumerGetterFactory::generate($field);

            $value = $getter->getValue($consumerProduct);
        } catch (\Exception $exception) {
            logger()->error($exception);
        }

        return $value;
    }

    public function filterMany($consumerProducts, Collection $fieldsVisibility): array
    {
        $activeFields = $this->getActiveFields($fieldsVisibility);

        if (empty($activeFields)) return [];

        $response = [
            'columns' => $this->getColumns($activeFields),
            'data' => [],
        ];

        foreach ($consumerProducts as $consumerProduct) {
            $response['data'][] = $this->filterOne($consumerProduct, $fieldsVisibility);
        }

        return $response;
    }

    public function getVisibleFieldsByIndustryServiceModuleFeature(
        int $industryId,
        int $industryServiceId,
        SystemModule $systemModule,
        string $feature
    ): Collection
    {
        // TODO - Cache?
        return ConsumerFieldModuleVisibility::query()
            ->where(function ($query) use ($industryId, $industryServiceId){
                $query->where(function ($query) use ($industryId){
                    $query->where(ConsumerFieldModuleVisibility::FIELD_CONSUMER_FIELD_CATEGORY, 'industry')
                        ->where(ConsumerFieldModuleVisibility::FIELD_CONSUMER_FIELD_CATEGORY_ID, $industryId);
                })->orWhere(function ($query)use ($industryServiceId) {
                    $query->where(ConsumerFieldModuleVisibility::FIELD_CONSUMER_FIELD_CATEGORY, 'service')
                        ->where(ConsumerFieldModuleVisibility::FIELD_CONSUMER_FIELD_CATEGORY_ID, $industryServiceId);
                });
            })
            ->where(ConsumerFieldModuleVisibility::FIELD_MODULE_TYPE, $systemModule->value)
            ->where(ConsumerFieldModuleVisibility::FIELD_FEATURE_TYPE, $feature)
            ->where(ConsumerFieldModuleVisibility::FIELD_IS_VISIBLE, true)
            ->distinct()
            ->get();
    }

    public function filterManyByIndustryServiceModuleFeature(
        $consumerProducts,
        Industry $industry,
        IndustryService $industryService,
        SystemModule $systemModule,
        string $feature
    ): array
    {
        $industryVisibleFields = $this->getVisibleFieldsByIndustryServiceModuleFeature($industry, $industryService, $systemModule, $feature);

        return $this->filterMany($consumerProducts, $industryVisibleFields);
    }
}
