<?php

namespace App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter;

use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\Enums\ConsumerFieldCategory;
use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\Enums\ConsumerFieldSource;

class Field
{
    const FIELD_KEY = 'key';
    const FIELD_NAME = 'name';
    const FIELD_HIDE_NAME = 'hide_name';
    const FIELD_CATEGORY = 'category';
    const FIELD_SOURCE = 'source';
    const FIELD_VALUE = 'value';


    public string $key;
    public string $name;
    public bool $hideName = false;
    public ConsumerFieldCategory $category;
    public ConsumerFieldSource $source;
    public mixed $value;


    public function __construct(
        string $key,
        string $name,
        bool $hideName,
        ConsumerFieldSource $source,
        ConsumerFieldCategory $category,
    )
    {
        $this->key = $key;
        $this->name = $name;
        $this->hideName = $hideName;
        $this->source = $source;
        $this->category = $category;
    }

    function setValue(mixed $value): void
    {
        $this->value = $value;
    }

    static function fromArray(array $data): Field
    {
        return new self(
            $data[self::FIELD_KEY],
            $data[self::FIELD_NAME],
            $data[self::FIELD_HIDE_NAME] ?? false,
            ConsumerFieldSource::tryFrom($data[self::FIELD_SOURCE]),
            ConsumerFieldCategory::tryFrom($data[self::FIELD_CATEGORY]) ?? ConsumerFieldCategory::TEXT,
        );
    }

    function toArray(): array
    {
        return [
            self::FIELD_KEY => $this->key,
            self::FIELD_NAME => $this->name,
            self::FIELD_HIDE_NAME => $this->hideName,
            self::FIELD_CATEGORY => $this->category->value,
            self::FIELD_SOURCE => $this->source->value,
            self::FIELD_VALUE => $this->value,
        ];
    }
}
