<?php

namespace App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\ConsumerCommonGetter;

use App\Models\Odin\ConsumerProduct;
use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\BaseConsumerDataGetter;

class ConsumerAddressCityStateZipGetter extends BaseConsumerDataGetter
{
    const ID = 'address_city_and_state';

    function getValue(ConsumerProduct $consumerProduct): string
    {
        return collect([$consumerProduct->address->city, $consumerProduct->address->state, $consumerProduct->address->zip_code])
            ->map(fn($v) => trim($v))
            ->filter()
            ->join(', ');
    }
}
