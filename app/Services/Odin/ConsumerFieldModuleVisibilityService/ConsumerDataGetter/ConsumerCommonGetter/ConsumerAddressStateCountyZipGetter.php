<?php

namespace App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\ConsumerCommonGetter;

use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\ConsumerProduct;
use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\BaseConsumerDataGetter;

class ConsumerAddressStateCountyZipGetter extends BaseConsumerDataGetter
{
    const ID = 'address_state_county';

    function getValue(ConsumerProduct $consumerProduct): string
    {
        $location = Location::query()
            ->where(Location::ZIP_CODE, $consumerProduct->{ConsumerProduct::RELATION_ADDRESS}->{Address::FIELD_ZIP_CODE})
            ->where(Location::TYPE, Location::TYPE_ZIP_CODE)
            ->first();

        return $location?->{Location::STATE} . ' / ' . $location?->{Location::COUNTY};
    }
}
