<?php

namespace App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\ConsumerCommonGetter;

use App\Models\Odin\ConsumerProduct;
use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\BaseConsumerDataGetter;

class ConsumerCommonGetter extends BaseConsumerDataGetter
{
    protected array $fieldsGetters = [
        ConsumerFullAddressGetter::ID => ConsumerFullAddressGetter::class,
        ConsumerRoofImageGetter::ID => ConsumerRoofImageGetter::class,
        ConsumerFullNameGetter::ID => ConsumerFullNameGetter::class,
        ConsumerAddressCityStateZipGetter::ID => ConsumerAddressCityStateZipGetter::class,
        ConsumerAddressStreetNumberStreetNameGetter::ID => ConsumerAddressStreetNumberStreetNameGetter::class,
        ConsumerAddressStateCountyZipGetter::ID => ConsumerAddressStateCountyZipGetter::class
    ];

    function getValue(ConsumerProduct $consumerProduct): mixed
    {
        if (isset($this->fieldsGetters[$this->field->key])) {
            return $this->getFromGetter($consumerProduct);
        } else return $this->getFromPlain($consumerProduct);
    }

    private function getFromPlain(ConsumerProduct $consumerProduct): mixed
    {
        return $consumerProduct->consumer->{$this->field->key};
    }

    private function getFromGetter(ConsumerProduct $consumerProduct): mixed
    {
        /** @var BaseConsumerDataGetter $getter */
        $getter = app()->make($this->fieldsGetters[$this->field->key], [
            'field' => $this->field
        ]);

        return $getter->getValue($consumerProduct);
    }
}
