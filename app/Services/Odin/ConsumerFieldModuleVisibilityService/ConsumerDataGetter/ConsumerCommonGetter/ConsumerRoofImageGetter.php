<?php

namespace App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\ConsumerCommonGetter;

use App\Models\Odin\ConsumerProduct;
use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\BaseConsumerDataGetter;

class ConsumerRoofImageGetter extends BaseConsumerDataGetter
{
    const ID = 'roof_image_url';

    function getValue(ConsumerProduct $consumerProduct): string
    {
        return $consumerProduct->getAddressImage();
    }
}
