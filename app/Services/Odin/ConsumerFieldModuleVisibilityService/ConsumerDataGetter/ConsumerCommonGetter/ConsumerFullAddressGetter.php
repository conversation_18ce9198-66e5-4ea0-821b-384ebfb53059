<?php

namespace App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\ConsumerCommonGetter;

use App\Models\Odin\ConsumerProduct;
use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\BaseConsumerDataGetter;

class ConsumerFullAddressGetter extends BaseConsumerDataGetter
{
    const ID = 'full_address';

    function getValue(ConsumerProduct $consumerProduct): string
    {
        return $consumerProduct->address->getFullAddress();
    }
}
