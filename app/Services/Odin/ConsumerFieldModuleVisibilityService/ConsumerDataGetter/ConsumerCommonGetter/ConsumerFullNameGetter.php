<?php

namespace App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\ConsumerCommonGetter;

use App\Models\Odin\ConsumerProduct;
use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\BaseConsumerDataGetter;

class ConsumerFullNameGetter extends BaseConsumerDataGetter
{
    const ID = 'full_name';

    function getValue(ConsumerProduct $consumerProduct): string
    {
        return $consumerProduct->consumer->getFullName();
    }
}
