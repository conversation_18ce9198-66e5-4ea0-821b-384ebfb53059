<?php

namespace App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter;

use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\ConsumerCommonGetter\ConsumerCommonGetter;
use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\Enums\ConsumerFieldSource;

class ConsumerGetterFactory
{
    static function generate(Field $field): BaseConsumerDataGetter
    {
        if ($field->source === ConsumerFieldSource::COMMON) {
            return new ConsumerCommonGetter($field);
        }
        if ($field->source === ConsumerFieldSource::CONFIGURABLE) {
            return new ConsumerConfigurableGetter($field);
        }

        throw new \Exception();
    }
}
