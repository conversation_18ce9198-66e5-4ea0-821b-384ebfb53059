<?php

namespace App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter;

use App\Models\Odin\ConsumerProduct;

class ConsumerConfigurableGetter extends BaseConsumerDataGetter
{
    function getValue(ConsumerProduct $consumerProduct): ?string
    {
        if (isset($consumerProduct->consumerProductData->payload[$this->field->key])) return $consumerProduct->consumerProductData->payload[$this->field->key];

        return null;
    }
}
