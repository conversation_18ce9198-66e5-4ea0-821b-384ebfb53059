<?php

namespace App\Services\Odin;


use App\Jobs\TagUserJob;
use App\Models\UserAction;
use App\Models\User;

class UserActionService
{

    /***
     * @param UserAction $action
     * @param array $taggedUserIds
     * @return void
     */
    public function tagUsers(UserAction $action, array $taggedUserIds): void
    {
        $users = User::query()->whereIn(User::FIELD_ID, $taggedUserIds)->get();

        foreach ($users as $user) {
//            TagUserJob::dispatch($action, $user); //todo
        }
    }
}
