<?php

namespace App\Services\Odin;

use App\Models\Legacy\EloquentQuote;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Repositories\Legacy\LeadProcessingRepository;
use App\Repositories\Odin\ConsumerProductRepository;

class DuplicateProductService
{
    public function __construct(protected ConsumerProductRepository $consumerProductRepository, protected LeadProcessingRepository $leadProcessingRepository) {}

    /**
     * @param int $consumerProductId
     *
     * @return bool
     */
    public function cancelIfProductIsDuplicate(int $consumerProductId): bool
    {
        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = ConsumerProduct::query()->findOrFail($consumerProductId);

        // We only want to cancel duplicate leads from the past 2 days. The 90 day default is to prevent selling dupes
        // to the same company
        if ($this->productIsDuplicate($consumerProduct, 2)) {
            $this->cancelDuplicateProduct($consumerProduct);

            return true;
        }

        return false;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param int $timeframeInDays
     * @return bool
     */
    protected function productIsDuplicate(ConsumerProduct $consumerProduct, int $timeframeInDays = 90): bool
    {
        return $this
                ->consumerProductRepository
                ->getDuplicateConsumerProductQueryWithJoin($consumerProduct, $timeframeInDays)
                ->exists();
    }

    /**
     * @param ConsumerProduct $product
     *
     * @return void
     */
    protected function cancelDuplicateProduct(ConsumerProduct $product): void
    {
        $product->status = ConsumerProduct::STATUS_CANCELLED;
        $product->save();
        $product->consumer->status_reason = 'Duplicate';
        $product->consumer->save();

        $this->leadProcessingRepository->updateStatusAndReason($product->consumer->legacyLead, EloquentQuote::VALUE_STATUS_CANCELLED, 'Duplicate');
    }
}
