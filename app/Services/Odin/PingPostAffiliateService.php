<?php

namespace App\Services\Odin;

use App\Enums\AffiliateKey;
use App\Enums\ConsumerProductChannel;
use App\Http\Controllers\Odin\ResourceAPI\v2\ConsumerController;
use App\Http\Requests\PingPostAffiliatesCreateLeadRequest;
use App\Jobs\AddAffiliateLeadToQueue;
use App\Models\Affiliates\Affiliate;
use App\Models\EstimatedRevenuePerLeadByLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductAffiliateRecord;
use App\Models\Odin\ConsumerProductData;
use App\Models\Odin\Industry;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Models\Odin\IndustryService;
use App\Models\Odin\PropertyType;
use App\Enums\Odin\PropertyType as PropertyTypeEnum;
use App\Models\Odin\ServiceProduct;
use App\Models\Odin\Website;
use App\Models\PingPostAffiliates\LeadProcessingAffiliate;
use App\Models\PingPostAffiliates\PingPostAffiliate;
use App\Models\PingPostAffiliates\PingPostAffiliateApiKey;
use App\Models\PingPostAffiliates\PingPostAffiliateLead;
use App\Models\PingPostAffiliates\PingPostAffiliateRequest;
use App\Repositories\Odin\AddressRepository;
use App\Repositories\Odin\ProductRepository;
use App\Services\AddressIdentification\GooglePlacesAPIService;
use App\Services\CompanyLocationService;
use App\Services\DatabaseHelperService;
use App\Services\HelperService;
use App\Services\Odin\API\OdinAuthoritativeAPILegacySyncService;
use App\Transformers\Odin\v2\EloquentQuoteTransformer;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Http\Client\RequestException;
use Illuminate\Http\Request;
use Illuminate\Mail\Message;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Ramsey\Uuid\Uuid;

class PingPostAffiliateService
{
    // Keys used for queue API affiliates data response
    const string INFO   = 'info';
    const string MANUAL = 'manual';
    const string NAME   = 'name';

    // Key for default value conversion
    const string DEFAULT_CONVERSION =  'default_conversion';

    const array SUCCESS_RESPONSE = [
        "status"    => true,
        "message"   => 'Successful Lead Validation.',
    ];

    const array INDUSTRY_TO_INDUSTRY_SERVICE = [
        'solar'     => 'solar-installation',
        'roofing'   => 'roof-replacement',
        'bathroom'  => 'bathroom-remodeling',
        'kitchen'   => 'siding-replacement',
        'windows'   => 'windows-install-replace',
        'siding'    => 'siding-replacement',
    ];

    const array INDUSTRY_TO_ORIGIN = [
        'solar'     => 'sr',
        'roofing'   => 'rc',
        'bathrooms' => 'be',
        'kitchens'  => 'ke',
        'windows'   => 'fixr',
        'siding'    => 'fixr',
    ];

    const array DEFAULT_MANUAL_FIELDS = [
        PingPostAffiliateLead::MANUAL_PUBLISHER => null,
        PingPostAffiliateLead::MANUAL_QUALITY   => null,
        PingPostAffiliateLead::MANUAL_NOTES     => null,
    ];

    /**
     * @param ConsumerController $consumerController
     * @param GooglePlacesAPIService $googlePlacesApiService
     * @param CompanyLocationService $companyLocationService
     * @param AddressRepository $addressRepository
     * @param HelperService $helperService
     * @param ProductRepository $productRepository
     * @param EloquentQuoteTransformer $quoteTransformer
     * @param OdinAuthoritativeAPILegacySyncService $legacySyncService
     */
    public function __construct(
        protected ConsumerController $consumerController,
        protected GooglePlacesAPIService $googlePlacesApiService,
        protected CompanyLocationService $companyLocationService,
        protected AddressRepository $addressRepository,
        protected HelperService $helperService,
        protected ProductRepository $productRepository,
        protected EloquentQuoteTransformer  $quoteTransformer,
        protected OdinAuthoritativeAPILegacySyncService $legacySyncService,
    ) {}

    /**
     * @param array $data
     * @param PingPostAffiliate $affiliate
     * @return array
     */
    public function validateNewLead(array $data, PingPostAffiliate $affiliate): array
    {
        // Initially we will not have validation criteria, may want duplicate check here
        return self::SUCCESS_RESPONSE;
    }

    /**
     * This is the main processing function for intake of a ping post lead.
     * The intake parameters are defined in the PingPostAffiliate model
     * request_rules: The rules need to be setup to allow the defined request type
     * key_map: Mapping the affiliate's request keys to our values
     * key_value_conversions: Setting rules for what specific values are converted to (ex. 'OWN' for homeowner is converted to 'yes' for CEE)
     * default_key_values: If key is not given in request, can set a default
     *
     * @param array $data
     * @param PingPostAffiliate $affiliate
     * @param PingPostAffiliateRequest $request
     * @return bool
     * @throws RequestException
     */
    public function processNewLead(array $data, PingPostAffiliate $affiliate, PingPostAffiliateRequest $request): bool
    {
        // Get property type id for residential
        $residentialPropertyTypeId = PropertyType::query()
            ->where(PropertyType::FIELD_NAME, PropertyTypeEnum::RESIDENTIAL->value)
            ->get()->first()->{PropertyType::FIELD_ID};

        // Get industry service and industry models
        $industryServiceSlug = self::INDUSTRY_TO_INDUSTRY_SERVICE[$this->getAffiliateKeyValue(AffiliateKey::INDUSTRY, $affiliate, $data)] ?? null;
        if (!$industryServiceSlug) {
            self::sendNotificationEmails(
                'Ping Post Affiliate Error',
                "Received and unknown industry key ({$this->getAffiliateKeyValue(AffiliateKey::INDUSTRY, $affiliate, $data)}) from affiliate {$affiliate->name}.\n Ping Post Request ID: {$request->id}. Valid Keys: ".json_encode(array_keys(self::INDUSTRY_TO_INDUSTRY_SERVICE)));
            return false;
        }
        $industryServiceModel = IndustryService::query()->where(IndustryService::TABLE.'.'.IndustryService::FIELD_SLUG, $industryServiceSlug)->get()->first();
        $industryModel = $industryServiceModel->industry;
        $serviceProductModel = ServiceProduct::query()
            ->where(ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $industryServiceModel->id)
            ->where(ServiceProduct::FIELD_PRODUCT_ID, $this->productRepository->getLeadProductId())
            ->get()->first();
        $originAbbr = self::INDUSTRY_TO_ORIGIN[$industryModel->{Industry::FIELD_SLUG}];
        $websiteModel = Website::query()->where(Website::FIELD_ABBREVIATION, $originAbbr)->get()->first();

        // Get Location entry data
        $locationData = $this->getLocationDataForZip($this->getAffiliateKeyValue(AffiliateKey::ZIP_CODE, $affiliate, $data));

        // Location Ids for address record
        $zipLocationId = $locationData->{'zip_location_id'};
        $countyLocationId = $locationData->{'county_location_id'};
        $stateLocationId = $locationData->{'state_location_id'};
        $county = $locationData->{'county'};

        // Get Place ID from Google
        try {
            $placeId = $this->googlePlacesApiService->getIdFromAddressComponents(
                $this->getAffiliateKeyValue(AffiliateKey::ADDRESS_1, $affiliate, $data),
                $this->getAffiliateKeyValue(AffiliateKey::CITY, $affiliate, $data),
                $this->getAffiliateKeyValue(AffiliateKey::STATE, $affiliate, $data),
                $this->getAffiliateKeyValue(AffiliateKey::ZIP_CODE, $affiliate, $data),
                $this->getAffiliateKeyValue(AffiliateKey::ADDRESS_2, $affiliate, $data),
            );
        } catch (Exception $e) {
            $placeId = null;
        }

        // Create Address Record
        $addressData = [
            Address::FIELD_ADDRESS_1 => $this->getAffiliateKeyValue(AffiliateKey::ADDRESS_1, $affiliate, $data),
            Address::FIELD_ADDRESS_2 => $this->getAffiliateKeyValue(AffiliateKey::ADDRESS_2, $affiliate, $data),
            Address::FIELD_CITY => $this->getAffiliateKeyValue(AffiliateKey::CITY, $affiliate, $data),
            Address::FIELD_COUNTY => $county,
            Address::FIELD_STATE => $this->getAffiliateKeyValue(AffiliateKey::STATE, $affiliate, $data),
            Address::FIELD_ZIP_CODE => $this->getAffiliateKeyValue(AffiliateKey::ZIP_CODE, $affiliate, $data),
            Address::FIELD_COUNTRY => $this->getAffiliateKeyValue(AffiliateKey::COUNTRY, $affiliate, $data),
            Address::FIELD_PLACE_ID => $placeId,
            Address::FIELD_ZIP_CODE_LOCATION_ID => $zipLocationId,
            Address::FIELD_COUNTY_LOCATION_ID => $countyLocationId,
            Address::FIELD_STATE_LOCATION_ID => $stateLocationId,
        ];

        // Add Latitude and Longitude
        $addressData = $this->companyLocationService->addLatLngToAddress(addressData: $addressData, throw: false);
        $address = $this->addressRepository->createAddressFromAttributes($addressData);

        // Create Consumer Record
        $consumerData = [
            Consumer::FIELD_REFERENCE => Uuid::uuid4(),
            Consumer::FIELD_WEBSITE_ID => $websiteModel->id,
            Consumer::FIELD_EMAIL => $this->getAffiliateKeyValue(AffiliateKey::EMAIL, $affiliate, $data),
            Consumer::FIELD_PHONE => $this->getAffiliateKeyValue(AffiliateKey::PHONE, $affiliate, $data),
            Consumer::FIELD_FORMATTED_PHONE => $this->helperService->formatUSPhoneNumber($this->getAffiliateKeyValue(AffiliateKey::PHONE, $affiliate, $data)),
            Consumer::FIELD_FIRST_NAME => $this->getAffiliateKeyValue(AffiliateKey::FIRST_NAME, $affiliate, $data),
            Consumer::FIELD_LAST_NAME => $this->getAffiliateKeyValue(AffiliateKey::LAST_NAME, $affiliate, $data),
            Consumer::FIELD_STATUS => Consumer::STATUS_INITIAL,
            Consumer::FIELD_CLASSIFICATION => Consumer::CLASSIFICATION_UNVERIFIED_PHONE,
            Consumer::FIELD_MAX_CONTACT_REQUESTS => ConsumerProduct::DEFAULT_CONTACT_REQUEST_FOR_MULTI_INDUSTRY_LEAD,
        ];
        $consumer = Consumer::query()->create($consumerData);

        // Create Consumer Product Record
        $consumerProductData = [
            ConsumerProduct::FIELD_CONSUMER_ID => $consumer->id,
            ConsumerProduct::FIELD_SERVICE_PRODUCT_ID => $serviceProductModel->id,
            ConsumerProduct::FIELD_ADDRESS_ID => $address->id,
            ConsumerProduct::FIELD_GOOD_TO_SELL => false,
            ConsumerProduct::FIELD_STATUS => ConsumerProduct::STATUS_INITIAL,
            ConsumerProduct::FIELD_CONTACT_REQUESTS => ConsumerProduct::DEFAULT_CONTACT_REQUEST_FOR_MULTI_INDUSTRY_LEAD,
            ConsumerProduct::FIELD_PROPERTY_TYPE_ID => $residentialPropertyTypeId,
            ConsumerProduct::FIELD_IP_ADDRESS => $this->getAffiliateKeyValue(AffiliateKey::IP_ADDRESS, $affiliate, $data),
            ConsumerProduct::FIELD_IS_SECONDARY_SERVICE => false,
            ConsumerProduct::FIELD_CHANNEL => ConsumerProductChannel::AFFILIATE_LEADS
        ];
        $consumerProduct = ConsumerProduct::query()->create($consumerProductData);

        // Create Consumer Product Data Record
        $consumerProductDataRecord = [
            ConsumerProductData::FIELD_PAYLOAD => [
                'origin' => $originAbbr,
                'roof_type' => $this->getAffiliateKeyValue(AffiliateKey::ROOF_TYPE, $affiliate, $data),
                'ip_address' => $this->getAffiliateKeyValue(AffiliateKey::IP_ADDRESS, $affiliate, $data),
                'own_property' => $this->getAffiliateKeyValue(AffiliateKey::OWN_PROPERTY, $affiliate, $data),
                'trusted_form_url' => $this->getAffiliateKeyValue(AffiliateKey::TRUSTED_FORM_URL, $affiliate, $data),
            ]
        ];
        // If electric bill or utility name are given, add to cpd
        if ($industryModel->slug === 'solar') {
            $consumerProductDataRecord[ConsumerProductData::FIELD_PAYLOAD]['electric_cost'] = (string) $this->getAffiliateKeyValue(AffiliateKey::ELECTRIC_BILL, $affiliate, $data);
            $consumerProductDataRecord[ConsumerProductData::FIELD_PAYLOAD]['utility_name']  = $this->getAffiliateKeyValue(AffiliateKey::UTILITY, $affiliate, $data);
        }

        $cpDataModel = ConsumerProductData::query()->create($consumerProductDataRecord);
        $consumerProduct->update([ConsumerProduct::FIELD_CONSUMER_PRODUCT_DATA_ID => $cpDataModel->id]);

        // Create Ping Post Lead Record
        $pingPostLeadData = [
            PingPostAffiliateLead::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
            PingPostAffiliateLead::FIELD_PING_POST_AFFILIATE_ID => $affiliate->id,
            PingPostAffiliateLead::FIELD_PING_POST_AFFILIATE_REQUEST_ID => $request->id,
            PingPostAffiliateLead::FIELD_COST => $this->getAffiliateKeyValue(AffiliateKey::COST, $affiliate, $data),
            PingPostAffiliateLead::FIELD_URL_ORIGIN => $this->getAffiliateKeyValue(AffiliateKey::ORIGIN_URL, $affiliate, $data),
            PingPostAffiliateLead::FIELD_TRUSTED_FORM_URL => $this->getAffiliateKeyValue(AffiliateKey::TRUSTED_FORM_URL, $affiliate, $data),
            PingPostAffiliateLead::FIELD_UNIVERSAL_LEAD_ID => $this->getAffiliateKeyValue(AffiliateKey::UNIVERSAL_LEAD_ID, $affiliate, $data),
            PingPostAffiliateLead::FIELD_LEAD_CREATION_DATE => $this->getAffiliateKeyValue(AffiliateKey::CREATION_DATE, $affiliate, $data),
            PingPostAffiliateLead::FIELD_CAMPAIGN_NAME => $this->getAffiliateKeyValue(AffiliateKey::PLATFORM_CAMPAIGN, $affiliate, $data),
            PingPostAffiliateLead::FIELD_DATA => $data,
            PingPostAffiliateLead::FIELD_MANUAL_FIELDS => self::DEFAULT_MANUAL_FIELDS,
        ];
        PingPostAffiliateLead::query()->create($pingPostLeadData);

        // If Ping Post Affiliate has related entry in Affiliates table, create Consumer Product Affiliate Records entry
        if ($affiliate->affiliate_id) {
            $cparData = [
                ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID => $affiliate->affiliate_id,
                ConsumerProductAffiliateRecord::FIELD_CAMPAIGN_ID => $this->getAffiliateKeyValue(AffiliateKey::AFFILIATE_CAMPAIGN_ID, $affiliate, $data),
                ConsumerProductAffiliateRecord::FIELD_TRACK_NAME => null,
                ConsumerProductAffiliateRecord::FIELD_TRACK_CODE => null,
            ];
            $cparModel = ConsumerProductAffiliateRecord::query()->create($cparData);
            $consumerProduct->update([ConsumerProduct::FIELD_CONSUMER_PRODUCT_AFFILIATE_RECORD_ID => $cparModel->id]);
        }

        try {
            $legacyConsumer = $this->quoteTransformer->transform($consumer, true);
            $legacyResponse = $this->legacySyncService->post('/consumer/create', $legacyConsumer)?->json();
        } catch (Exception $e) {
            self::sendNotificationEmails(
                'Ping Post Affiliate Error',
                "Failed to sync consumer creation back to legacy. Error on request attempt. ".
                "New Consumer ID: {$consumer->id}. Ping Post Request ID: {$request->id}\n"."Legacy Error: ".$e->getMessage());
            return false;
        }

        $legacyStatus = $legacyResponse['status'] ?? null;

        // Add legacy ids to consumer and address model if successful creation
        if ($legacyStatus) {
            $consumer->update([
                Consumer::FIELD_LEGACY_ID => $legacyResponse[ConsumerController::RESPONSE_ELOQUENT_QUOTE][ConsumerController::RESPONSE_LEGACY_ID],
            ]);

            $address->update([
                Address::FIELD_LEGACY_ID => $legacyResponse[ConsumerController::RESPONSE_ELOQUENT_ADDRESS][ConsumerController::RESPONSE_LEGACY_ID],
            ]);
        }
        else {
            self::sendNotificationEmails(
                'Ping Post Affiliate Error',
                "Failed to sync consumer creation back to legacy. Error in status response. ".
                "New Consumer ID: {$consumer->id}. Ping Post Request ID: {$request->id}.\n"."Legacy Request Data: ".json_encode($legacyConsumer));
            return false;
        }

        // Check for test lead
        $testLead = false;
        if ($consumer->first_name === 'Test' &&
            $consumer->last_name === 'Test' &&
            $consumer->email === '<EMAIL>'
        ) {
            self::sendNotificationEmails(
                'Ping Post Affiliate Test',
                "Ping Post Affiliate Test Lead received and processed successfully.\n".
                "New Consumer ID: {$consumer->id}. Consumer Product ID: {$consumerProduct->id}. Ping Post Request ID: {$request->id}.");
            $testLead = true;

            // Set consumer and consumer product to cancelled for test lead
            $consumer->update([
                Consumer::FIELD_STATUS => Consumer::STATUS_CANCELLED,
                Consumer::FIELD_STATUS_REASON => 'Test Lead',
            ]);
            $consumerProduct->update([
                ConsumerProduct::FIELD_STATUS => ConsumerProduct::STATUS_CANCELLED,
            ]);
        }

        // Create affiliate lead queue entry for new lead (don't put test leads into the queue)
        if (!$testLead)
            AddAffiliateLeadToQueue::dispatch($consumerProduct->id);

        // Set request processed to true
        $request->update([
            PingPostAffiliateRequest::FIELD_PROCESSED => true,
        ]);

        return true;
    }

    /**
     * @param AffiliateKey $key
     * @param PingPostAffiliate $affiliate
     * @param array $requestData
     * @return array
     */
    public function getAffiliateKeyValue(AffiliateKey $key, PingPostAffiliate $affiliate, array $requestData): mixed
    {
        // Get key mappings for affiliate
        $keyMappings = $affiliate->{PingPostAffiliate::FIELD_KEY_MAP};

        // Check for key mapped existence in data
        $companyKey = $keyMappings[$key->value] ?? null;

        // Get given value from request data
        $value = $requestData[$companyKey] ?? null;

        if ($value) {
            // Check for conversion existence
            $conversions = $affiliate->{PingPostAffiliate::FIELD_KEY_VALUE_CONVERSIONS};
            $conversion = $conversions[$key->value] ?? null;

            // Conversion exists
            if ($conversion) {
                // Get converted value
                $convertedValue = $conversion[$value] ?? null;
                if ($convertedValue) {
                    return $convertedValue;
                } else {
                    // Use default if conversion key doesn't map
                    return $conversion[self::DEFAULT_CONVERSION] ?? null;
                }
            } else {
                return $value;
            }
        }

        // Check for default if no value given
        if (!$value) {
            $defaults = $affiliate->{PingPostAffiliate::FIELD_DEFAULT_KEY_VALUES};
            $default = $defaults[$key->value] ?? null;
            if ($default)
                return $default;
        }

        // Null if no given value and no default found
        return null;
    }

    /**
     * @param string $zip_code
     * @return mixed
     */
    public function getLocationDataForZip(string $zip_code): mixed
    {
        return Location::query()
            ->join(Location::TABLE .' as county_locations', fn(JoinClause $join) =>
            $join->on('county_locations.' . Location::COUNTY_KEY, '=', Location::TABLE .'.'. Location::COUNTY_KEY)
                ->on('county_locations.' . Location::STATE_ABBREVIATION, '=', Location::TABLE .'.'. Location::STATE_ABBREVIATION)
                ->where('county_locations.' . Location::TYPE, Location::TYPE_COUNTY)
            )
            ->join(Location::TABLE .' as state_locations', fn(JoinClause $join) =>
            $join->on('state_locations.' . Location::STATE_ABBREVIATION, '=', Location::TABLE .'.'. Location::STATE_ABBREVIATION)
                ->where('state_locations.' . Location::TYPE, Location::TYPE_STATE)
            )
            ->where(Location::TABLE.'.'.Location::TYPE, Location::TYPE_ZIP_CODE)
            ->where(Location::TABLE.'.'.Location::ZIP_CODE, $zip_code)
            ->get([
                Location::TABLE.'.'.Location::ID.' as zip_location_id',
                'county_locations.'.Location::ID.' as county_location_id',
                'state_locations.'.Location::ID.' as state_location_id',
                'county_locations.'.Location::COUNTY.' as county',
            ])
            ->first();
    }

    /**
     * @param string $key
     * @return PingPostAffiliateApiKey|null
     */
    public function findApiKey(string $key): ?PingPostAffiliateApiKey
    {
        return PingPostAffiliateApiKey::query()
            ->join(PingPostAffiliate::TABLE, PingPostAffiliate::TABLE.'.'.PingPostAffiliate::FIELD_ID, '=', PingPostAffiliateApiKey::TABLE.'.'.PingPostAffiliateApiKey::FIELD_PING_POST_AFFILIATE_ID)
            ->where(PingPostAffiliateApiKey::TABLE.'.'.PingPostAffiliateApiKey::FIELD_KEY, $key)
            ->where(PingPostAffiliateApiKey::TABLE.'.'.PingPostAffiliateApiKey::FIELD_STATUS, PingPostAffiliateApiKey::STATUS_ACTIVE)
            ->where(PingPostAffiliate::TABLE.'.'.PingPostAffiliate::FIELD_STATUS, PingPostAffiliate::STATUS_ACTIVE)
            ->get()->first();
    }

    /**
     * @param int $apiKeyId
     * @return array
     */
    public function getRequestRules(int $apiKeyId): array
    {
        return PingPostAffiliate::query()
            ->join(PingPostAffiliateApiKey::TABLE, PingPostAffiliate::TABLE.'.'.PingPostAffiliate::FIELD_ID, '=', PingPostAffiliateApiKey::TABLE.'.'.PingPostAffiliateApiKey::FIELD_PING_POST_AFFILIATE_ID)
            ->where(PingPostAffiliateApiKey::TABLE.'.'.PingPostAffiliateApiKey::FIELD_ID, $apiKeyId)
            ->get([PingPostAffiliate::TABLE.'.'.PingPostAffiliate::FIELD_REQUEST_RULES])->first()->{PingPostAffiliate::FIELD_REQUEST_RULES};
    }

    /**
     * @param int $apiKeyId
     * @return PingPostAffiliate
     */
    public function getAffiliateFromApiKeyId(int $apiKeyId): PingPostAffiliate
    {
        return PingPostAffiliate::query()
            ->join(PingPostAffiliateApiKey::TABLE, PingPostAffiliate::TABLE.'.'.PingPostAffiliate::FIELD_ID, '=', PingPostAffiliateApiKey::TABLE.'.'.PingPostAffiliateApiKey::FIELD_PING_POST_AFFILIATE_ID)
            ->where(PingPostAffiliateApiKey::TABLE.'.'.PingPostAffiliateApiKey::FIELD_ID, $apiKeyId)
            ->get()->first();
    }

    /**
     * @param int $affiliateId
     * @param int $apiKeyId
     * @param PingPostAffiliatesCreateLeadRequest $request
     * @return PingPostAffiliateRequest
     */
    public function createRequestModel(int $affiliateId, int $apiKeyId, PingPostAffiliatesCreateLeadRequest $request): PingPostAffiliateRequest
    {
        return PingPostAffiliateRequest::query()->create([
            PingPostAffiliateRequest::FIELD_PING_POST_AFFILIATE_API_KEY_ID => $apiKeyId,
            PingPostAffiliateRequest::FIELD_PING_POST_AFFILIATE_ID => $affiliateId,
            PingPostAffiliateRequest::FIELD_SOURCE_IP => $request->ip(),
            PingPostAffiliateRequest::FIELD_HEADERS => $request->headers->all(),
            PingPostAffiliateRequest::FIELD_REQUEST => $request->all(),
            PingPostAffiliateRequest::FIELD_CREATED_AT => now(),
            PingPostAffiliateRequest::FIELD_UPDATED_AT => now(),
        ]);
    }

    /**
     * @param PingPostAffiliateRequest $request
     * @param array $response
     * @return void
     */
    public function setRequestResponse(PingPostAffiliateRequest $request, array $response): void
    {
        $request->update([
            PingPostAffiliateRequest::FIELD_RESPONSE => $response,
        ]);
    }

    /**
     * @param int $consumerProductId
     * @return bool
     */
    public function addPingPostLeadToAffiliateQueue(int $consumerProductId): bool
    {
        $affiliateQueueEntry = LeadProcessingAffiliate::updateOrCreate(
            [
                LeadProcessingAffiliate::FIELD_CONSUMER_PRODUCT_ID => $consumerProductId
            ],
            [
                LeadProcessingAffiliate::FIELD_CONSUMER_PRODUCT_ID => $consumerProductId,
                LeadProcessingAffiliate::FIELD_POSSIBLE_REVENUE => $this->calculatePossibleRevenue($consumerProductId),
                LeadProcessingAffiliate::FIELD_PRIORITY => $this->calculateQueuePriority($consumerProductId),
            ]
        );

        return !empty($affiliateQueueEntry);
    }

    /**
     * @param int $consumerProductId
     * @return float
     */
    public function calculatePossibleRevenue(int $consumerProductId): float
    {
        $consumerProduct = ConsumerProduct::find($consumerProductId);
        $address = $consumerProduct->address;
        $zipCode = $address->{Address::FIELD_ZIP_CODE};
        $industry = $consumerProduct->industryService->industry;

        $estimatedRevenue = EstimatedRevenuePerLeadByLocation::query()
            ->join(Location::TABLE, Location::TABLE.'.'.Location::ID, '=', EstimatedRevenuePerLeadByLocation::TABLE.'.'.EstimatedRevenuePerLeadByLocation::FIELD_LOCATION_ID)
            ->where(EstimatedRevenuePerLeadByLocation::TABLE.'.'.EstimatedRevenuePerLeadByLocation::FIELD_INDUSTRY_ID, $industry->id)
            ->where(Location::TABLE.'.'.Location::TYPE, Location::TYPE_ZIP_CODE)
            ->where(Location::TABLE.'.'.Location::ZIP_CODE, $zipCode)
            ->get()
            ->first();

        if (!$estimatedRevenue)
            return 0;

        return $estimatedRevenue->{EstimatedRevenuePerLeadByLocation::FIELD_ESTIMATED_REVENUE};
    }

    /**
     * @param int $consumerProductId
     * @return int
     */
    public function calculateQueuePriority(int $consumerProductId): int
    {
        $consumerProduct = ConsumerProduct::find($consumerProductId);
        // Initially all priority the same for affiliates queue. Can use this to sort some affiliates first, or sort by location priorities
        return 0;
    }

    /**
     * @param string $subject
     * @param string $message
     * @return void
     */
    public static function sendNotificationEmails(string $subject, string $message): void
    {
        $emailRecipients = explode(',', config("services.ping_post.notification_emails"));

        if (App::isProduction()) {
            foreach ($emailRecipients as $recipient) {
                Mail::raw(
                    $message,
                    function (Message $message) use ($recipient, $subject) {
                        $message
                            ->to(trim($recipient))
                            ->subject($subject);
                    }
                );
            }
        } else {
            Log::info("Ping Post Notification Email to ".json_encode($emailRecipients)."\nSubject: $subject\nContent:\n$message");
        }
    }

    /**
     * Returns all affiliate data given
     * @param ConsumerProduct $consumerProduct
     * @return array
     */
    public function getAffiliateInfo(ConsumerProduct $consumerProduct): array
    {
        $affiliateLead = PingPostAffiliateLead::query()
            ->join(PingPostAffiliate::TABLE, PingPostAffiliate::TABLE.'.'.PingPostAffiliate::FIELD_ID, '=', PingPostAffiliateLead::TABLE.'.'.PingPostAffiliateLead::FIELD_PING_POST_AFFILIATE_ID)
            ->where(PingPostAffiliateLead::TABLE.'.'.PingPostAffiliateLead::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->id)
            ->get([
                PingPostAffiliateLead::TABLE.'.'.PingPostAffiliateLead::FIELD_DATA,
                PingPostAffiliateLead::TABLE.'.'.PingPostAffiliateLead::FIELD_MANUAL_FIELDS,
                PingPostAffiliate::TABLE.'.'.PingPostAffiliate::FIELD_NAME,
            ])->first();

        if (!$affiliateLead)
            return [
                self::INFO      => [],
                self::MANUAL    => [],
                self::NAME      => [],
            ];

        return [
            self::INFO      => $affiliateLead->{PingPostAffiliateLead::FIELD_DATA},
            self::MANUAL    => $affiliateLead->{PingPostAffiliateLead::FIELD_MANUAL_FIELDS},
            self::NAME      => $affiliateLead->{PingPostAffiliate::FIELD_NAME},
        ];
    }

    /**
     * @param int $consumerProductId
     * @param array $manualFields
     * @return bool
     */
    public function updateManualTracking(int $consumerProductId, array $manualFields): bool
    {
        $pingPostLeadEntry = PingPostAffiliateLead::query()
            ->where(PingPostAffiliateLead::FIELD_CONSUMER_PRODUCT_ID, $consumerProductId)
            ->get()->first();

        if (!$pingPostLeadEntry)
            return false;

        $pingPostLeadEntry->update([
            PingPostAffiliateLead::FIELD_MANUAL_FIELDS => $manualFields,
        ]);

        return true;
    }
}
