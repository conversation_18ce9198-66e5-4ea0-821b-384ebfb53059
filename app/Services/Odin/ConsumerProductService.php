<?php

namespace App\Services\Odin;

use App\Campaigns\Modules\Legacy\LegacyModule;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ServiceProduct;
use App\Repositories\Odin\ConsumerProductRepository;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;

class ConsumerProductService
{
    /**
     * @param ConsumerProductRepository $consumerProductRepository
     * @param LegacyModule $legacyModule
     */
    public function __construct(
        protected ConsumerProductRepository $consumerProductRepository,
        protected LegacyModule              $legacyModule
    ){}

    /**
     * @param int $consumerProductId
     *
     * @return void
     * @throws BindingResolutionException
     */
    public function updateConsumerProductStatus(int $consumerProductId): void
    {
        $consumerProduct = $this->consumerProductRepository->findOrFail($consumerProductId);
        $deliveries = $consumerProduct->productAssignment()
            ->count();

        $newStatus = $deliveries > 0 ? ConsumerProduct::STATUS_ALLOCATED : ConsumerProduct::STATUS_UNSOLD;

        if ($newStatus !== $consumerProduct->status) {
            $consumerProduct->update([ConsumerProduct::FIELD_STATUS => $newStatus]);
            $this->legacyModule->updateQuoteStatus($consumerProduct);
        }
    }

    /**
     * @param int $productId
     * @param int|null $industryId
     * @param int|null $daysOld
     * @return Builder
     */
    public function getConsumerProductsPendingAllocationQuery(int $productId, ?int $industryId, ?int $daysOld = 7): Builder
    {
        $dateFrom = now()->subDays($daysOld);

        return ConsumerProduct::query()
            ->where(ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_GOOD_TO_SELL, true)
            ->where(ConsumerProduct::TABLE .'.'. ConsumerProduct::CREATED_AT, '>=', $dateFrom)
            ->where(ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_STATUS, ConsumerProduct::STATUS_PENDING_ALLOCATION)
            ->join(ServiceProduct::TABLE, fn(JoinClause $join) =>
                $join->on(ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_ID, '=', ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_SERVICE_PRODUCT_ID)
                    ->where(ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_PRODUCT_ID, '=', $productId)
            )->when($industryId, fn(Builder $query) =>
                $query->join(IndustryService::TABLE, fn(JoinClause $join) =>
                    $join->on(IndustryService::TABLE .'.'. IndustryService::FIELD_ID, '=', ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_INDUSTRY_SERVICE_ID)
                        ->where(IndustryService::TABLE .'.'. IndustryService::FIELD_INDUSTRY_ID, $industryId)
                )
            );
    }
}
