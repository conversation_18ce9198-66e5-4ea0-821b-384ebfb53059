<?php

namespace App\Services\Odin\TopCompanies;

use App\Builders\Odin\CompanyCampaignBuilder;
use App\Contracts\TopCompanies\TopCampaignsFilter;
use App\Enums\Campaigns\CampaignStatus;
use App\Enums\Company\CompanyAdminStatus;
use App\Enums\Company\CompanySystemStatus;
use App\Enums\CompanyConsolidatedStatus;
use App\Enums\Odin\Product;
use App\Enums\Odin\PropertyType;
use App\Enums\Odin\QualityTier;
use App\Enums\Odin\SaleTypes;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModule;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\Legacy\CompanyRanking;
use App\Models\Legacy\Location;
use App\Models\Legacy\NonPurchasingCompanyLocation;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyService;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Models\SaleType;
use App\Repositories\Campaigns\Modules\Budget\BudgetRepository;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\IndustryServiceRepository;
use App\Services\Campaigns\ProductBiddingService;
use App\Services\DatabaseHelperService;
use App\Services\Odin\TopCompanies\Filters\TopCampaignsBudgetFilter;
use App\Services\Odin\TopCompanies\Filters\TopCampaignsLocationFilter;
use App\Services\Odin\TopCompanies\Filters\TopCampaignsRejectionFilter;
use App\Strategies\Campaigns\StandardBidModificationStrategy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class TopCompaniesService
{
    protected array $excludedCampaigns = [];

    public function __construct(
        protected IndustryServiceRepository $industryServiceRepository,
        protected StandardBidModificationStrategy $bidModificationStrategy,
        protected BudgetRepository $budgetRepository,
        protected ProductBiddingService $productBiddingService,
        protected LocationRepository $locationRepository
    ) {}

    public function getTopActiveCampaigns(IndustryService $industryService, string $zipCode, array $companiesExcluded = null)
    {
        $filters = collect([
            app(TopCampaignsRejectionFilter::class),
            app(TopCampaignsBudgetFilter::class)
        ]);

        $this->excludedCampaigns = [];

        $campaigns = $this->getBaseCampaigns(
            industryService: $industryService,
            status: CampaignStatus::ACTIVE,
            zipCode: $zipCode,
            companyStatues: [CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS],
            companiesExcluded: $companiesExcluded
        )
            ->filter(fn(CompanyCampaign $campaign) => $this->pipeThroughFilters($campaign, $filters, $zipCode));

        // todo: Order by bid, not revenue
        $sortedCampaigns        = $this->orderByRevenue($campaigns, $zipCode);
        $overRejectionCampaigns = $this->getExcludedCampaigns(TopCampaignsRejectionFilter::class);
        $overBudgetCampaigns    = $this->getExcludedCampaigns(TopCampaignsBudgetFilter::class);

        if (!empty($overRejectionCampaigns)) {
            $sortedCampaigns = $sortedCampaigns->merge($overRejectionCampaigns);
        }

        if (!empty($overBudgetCampaigns)) {
            $sortedCampaigns = $sortedCampaigns->merge($overBudgetCampaigns);
        }

        return $sortedCampaigns;
    }

    public function getTopPausedCampaigns(IndustryService $industryService, string $zipCode, array $companiesExcluded = null): Collection
    {
        return $this->getBaseCampaigns(
            industryService: $industryService,
            status: CampaignStatus::PAUSED_TEMPORARILY,
            zipCode: $zipCode,
            companiesExcluded: $companiesExcluded
        )->sortBy(fn(CompanyCampaign $companyCampaign) => $companyCampaign->reactivation?->reactivate_at?->timestamp ?? now()->addYear()->timestamp);
    }

    public function getTopInactiveCampaigns(IndustryService $industryService, string $zipCode, array $companiesExcluded = null): Collection
    {
        return $this->getBaseCampaigns(
            industryService: $industryService,
            status: CampaignStatus::PAUSED_PERMANENTLY,
            zipCode: $zipCode,
            companiesExcluded: $companiesExcluded
        ); // todo: Sort by rev in last 30 days
    }

    /**
     * Get top companies that are not purchasing leads
     *
     * @param IndustryService $industryService
     * @param string $zipCode
     * @param int $limit
     * @param array|null $companiesExcluded
     *
     * @return Collection
     */
    public function getTopNonPurchasingCompanies(IndustryService $industryService, string $zipCode, int $limit, array $companiesExcluded = null): Collection
    {
        $zipCodeLocation = $this->locationRepository->getZipCode($zipCode);

        if (!$zipCodeLocation) {
            return collect();
        }

        $companies = Company::query()
            ->selectRaw('DISTINCT ' . Company::TABLE . '.' . Company::FIELD_ID)
            ->join(
                CompanyService::TABLE,
                CompanyService::TABLE . '.' . CompanyService::FIELD_COMPANY_ID,
                Company::TABLE . '.' . Company::FIELD_ID
            )
            ->join(
                IndustryService::TABLE,
                IndustryService::TABLE . '.' . IndustryService::FIELD_ID,
                CompanyService::TABLE . '.' . CompanyService::FIELD_INDUSTRY_SERVICE_ID
            )
            ->join(
                DatabaseHelperService::readOnlyDatabase() . '.' . NonPurchasingCompanyLocation::TABLE,
                NonPurchasingCompanyLocation::TABLE . '.' . NonPurchasingCompanyLocation::FIELD_COMPANY_ID,
                Company::TABLE . '.' . Company::FIELD_LEGACY_ID
            )
            ->join(
                Location::TABLE,
                Location::TABLE . '.' . Location::ID,
                NonPurchasingCompanyLocation::TABLE . '.' . NonPurchasingCompanyLocation::FIELD_LOCATION_ID
            )
            ->where(IndustryService::TABLE . '.' . IndustryService::FIELD_ID, $industryService->id)
            ->where(Location::TABLE . '.' . Location::TYPE, Location::TYPE_COUNTY)
            ->where(Location::TABLE . '.' . Location::COUNTY_KEY, $zipCodeLocation->county_key)
            ->where(Location::TABLE . '.' . Location::STATE_ABBREVIATION, $zipCodeLocation->state_abbr)
            ->when(!empty($companiesExcluded), fn(Builder $query) => $query->whereNotIn(Company::TABLE . '.' . Company::FIELD_ID, $companiesExcluded))
            ->whereNotIn(Company::TABLE.'.'.Company::FIELD_ADMIN_STATUS, [
                CompanyAdminStatus::ADMIN_LOCKED,
                CompanyAdminStatus::ARCHIVED,
                CompanyAdminStatus::COLLECTIONS,
            ])
            ->whereNotIn(Company::TABLE.'.'.Company::FIELD_SYSTEM_STATUS, [
                CompanySystemStatus::SUSPENDED_PAYMENT
            ])
            ->limit($limit)
            ->get();

        if ($companies->isEmpty()) {
            return collect();
        }

        return Company::query()
            ->whereIn(Company::FIELD_ID, $companies->pluck(Company::FIELD_ID)->values()->toArray())
            ->get();
    }

    /**
     * @param CompanyCampaign $campaign
     * @param Collection $filters
     * @param string $zipCode
     *
     * @return bool
     */
    protected function pipeThroughFilters(CompanyCampaign $campaign, Collection $filters, string $zipCode): bool
    {
        return $filters->every(function (TopCampaignsFilter $filter) use ($campaign, $zipCode) {
            $status = $filter->filter($campaign, $zipCode);

            if (!$status) {
                $this->excludedCampaigns[get_class($filter)][] = $campaign;
            }

            return $status;
        });
    }

    /**
     * @param Collection $companyCampaigns
     * @param string $zipCode
     *
     * @return Collection<CompanyCampaign>
     */
    protected function orderByRevenue(Collection $companyCampaigns, string $zipCode): Collection
    {
        $countyLocation = $this->locationRepository->getCountyFromZipcode($zipCode);

        if (!$countyLocation) {
            return $companyCampaigns;
        }

        $stateLocation = $this->locationRepository->getStateByStateAbbr($countyLocation->state_abbr);

        $propertyTypeId = PropertyType::RESIDENTIAL->model()->id;
        $qualityTierId  = QualityTier::STANDARD->model()->id;
        $salesTypeId    = SaleType::query()
            ->where(SaleType::FIELD_NAME, SaleTypes::QUAD->name)
            ->first()->id;

        return $companyCampaigns->sortByDesc(function (CompanyCampaign $companyCampaign) use ($zipCode, $countyLocation, $stateLocation, $propertyTypeId, $qualityTierId, $salesTypeId) {
            // calculating for budget: verified, quality tier: standard, property: residential, sale type: quad
            // todo: calculate for others?
            $verifiedBudget = $this->budgetRepository->getVerifiedBudgetForCampaignOrFail($companyCampaign);

            $verifiedBudget->{StandardBidModificationStrategy::ATTRIBUTE_BID} = $this->productBiddingService->getProductBid(
                companyCampaign: $companyCampaign,
                countyLocationId: $countyLocation->id,
                stateLocationId: $stateLocation->id,
                propertyTypeId: $propertyTypeId,
                qualityTierId: $qualityTierId,
                salesTypeId: $salesTypeId
            );

            return $this->bidModificationStrategy->calculateModifiedBidForBudget($verifiedBudget);
        });
    }

    /**
     * @param IndustryService $industryService
     * @param CampaignStatus $status
     * @param string $zipCode
     * @param CompanyConsolidatedStatus[] $companyStatues
     * @param array|null $companiesExcluded
     *
     * @return Collection<CompanyCampaign>
     */
    public function getBaseCampaigns(
        IndustryService $industryService,
        CampaignStatus $status,
        string $zipCode,
        array $companyStatues = [],
        array $companiesExcluded = null
    ): Collection
    {
        return CompanyCampaignBuilder::query()
            ->forProducts([Product::LEAD, Product::DIRECT_LEADS])
            ->forPropertyTypes([PropertyType::RESIDENTIAL])
            ->forStatuses([$status])
            ->forCompanyConsolidatedStatuses($companyStatues)
            ->forCompanyAdminStatues([CompanyAdminStatus::ADMIN_APPROVED, CompanyAdminStatus::ADMIN_OVERRIDE])
            ->forCompanySystemStatues([CompanySystemStatus::ELIGIBLE])
            ->eagerLoadRelations([CompanyCampaign::RELATION_COMPANY])
            ->excludeCustomBudgets()
            ->getQuery()
            ->join(
                CompanyCampaignLocationModule::TABLE,
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID,
                CompanyCampaignLocationModule::TABLE . '.' . CompanyCampaignLocationModule::FIELD_CAMPAIGN_ID
            )
            ->join(
                CompanyCampaignLocationModuleLocation::TABLE,
                CompanyCampaignLocationModule::TABLE . '.' . CompanyCampaignLocationModule::FIELD_ID,
                CompanyCampaignLocationModuleLocation::TABLE . '.' . CompanyCampaignLocationModuleLocation::FIELD_MODULE_ID
            )
            ->where(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_SERVICE_ID, $industryService->id)
            ->where(CompanyCampaignLocationModuleLocation::TABLE . '.' . CompanyCampaignLocationModuleLocation::FIELD_ZIP_CODE, $zipCode)
            ->when(!empty($companiesExcluded), fn(Builder $query) => $query->whereNotIn(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_COMPANY_ID, $companiesExcluded))
            ->get();
    }

    /**
     * @param string $filterClass
     *
     * @return CompanyCampaign[]|null
     */
    protected function getExcludedCampaigns(string $filterClass): ?array
    {
        if (!array_key_exists($filterClass, $this->excludedCampaigns)) {
            return null;
        }

        return $this->excludedCampaigns[$filterClass];
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param int $limit
     * @param array $excludedCompanyIds
     * @return Collection
     */
    public function getTopCampaignsAndCompaniesForConsumerProduct(
        ConsumerProduct $consumerProduct,
        int $limit = 4,
        array $excludedCompanyIds = []
    ): Collection
    {
        $slug = $consumerProduct->industryService->slug;

        $addressZip = $consumerProduct->address->zip_code;

        $campaigns = $this->getTopCampaignsForServiceAndZipCode(
            industryServiceSlug: $slug,
            zipCode: $addressZip,
            limit: $limit,
            excludedCompanyIds: $excludedCompanyIds
        );

        if ($campaigns->count() < $limit) {
            $industryService = $this->industryServiceRepository->findBySlugOrFail($slug);

            $companies = $this->getTopNonPurchasingCompanies(
                industryService: $industryService,
                zipCode: $addressZip,
                limit: $limit - $campaigns->count(),
                companiesExcluded: $campaigns->pluck(CompanyCampaign::FIELD_COMPANY_ID)->merge($excludedCompanyIds)->toArray()
            );

            $campaigns = $campaigns->merge($companies);
        }

        return $campaigns;
    }

    /**
     * @param string $industryServiceSlug
     * @param string $zipCode
     * @param int $limit
     * @param array|null $excludedCompanyIds
     * @return Collection
     */
    public function getTopCampaignsForServiceAndZipCode(
        string $industryServiceSlug,
        string $zipCode,
        int $limit = 4,
        array $excludedCompanyIds = null
    ): Collection
    {
        $industryService = $this->industryServiceRepository->findBySlugOrFail($industryServiceSlug);

        //top campaign for company
        $campaigns = $this->getTopActiveCampaigns(
            industryService: $industryService,
            zipCode: $zipCode,
            companiesExcluded: $excludedCompanyIds,
        )->unique(CompanyCampaign::FIELD_COMPANY_ID);

        if ($campaigns->count() >= $limit) {
            return $campaigns->take($limit);
        }

        $topPausedCampaigns = $this->getTopPausedCampaigns(
            industryService: $industryService,
            zipCode: $zipCode,
            companiesExcluded: $campaigns->pluck(CompanyCampaign::FIELD_COMPANY_ID)->merge($excludedCompanyIds)->toArray())
            ->unique(CompanyCampaign::FIELD_COMPANY_ID);

        foreach($topPausedCampaigns as $topPausedCampaign) {
            $campaigns->push($topPausedCampaign);
        }

        if ($campaigns->count() >= $limit) {
            return $campaigns->take($limit);
        }

        $topInactiveCampaigns = $this->getTopInactiveCampaigns(
            industryService: $industryService,
            zipCode: $zipCode,
            companiesExcluded: $campaigns->pluck(CompanyCampaign::FIELD_COMPANY_ID)->merge($excludedCompanyIds)->toArray())
            ->unique(CompanyCampaign::FIELD_COMPANY_ID);

        foreach($topInactiveCampaigns as $topInactiveCampaign) {
            $campaigns->push($topInactiveCampaign);
        }

        if ($campaigns->count() >= $limit) {
            return $campaigns->take($limit);
        } else {
            return $campaigns;
        }
    }
}
