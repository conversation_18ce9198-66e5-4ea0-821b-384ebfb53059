<?php

namespace App\Services\Odin\TopCompanies\Filters;

use App\Contracts\TopCompanies\TopCampaignsFilter;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;

class TopCampaignsLocationFilter implements TopCampaignsFilter
{
    protected ?string $zipCode = null;

    /**
     * @inheritDoc
     */
    public function filter(CompanyCampaign $companyCampaign, string $zipCode): bool
    {
        return !!$companyCampaign->locationModule->locations()->where(CompanyCampaignLocationModuleLocation::FIELD_ZIP_CODE, $zipCode)->first();
    }
}
