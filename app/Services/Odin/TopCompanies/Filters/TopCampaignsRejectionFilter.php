<?php

namespace App\Services\Odin\TopCompanies\Filters;

use App\Contracts\TopCompanies\TopCampaignsFilter;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\ComputedRejectionStatistic;

class TopCampaignsRejectionFilter implements TopCampaignsFilter
{
    /**
     * @inheritDoc
     */
    public function filter(CompanyCampaign $companyCampaign, string $zipCode): bool
    {
        $rejectionThreshold = config('sales.defaults.crm_rejection_percentage_threshold'); //todo: crm rejection threshold for Direct Leads

        return  $this->getRejectionForCompany($companyCampaign) < $rejectionThreshold;
    }

    protected function getRejectionForCompany(CompanyCampaign $companyCampaign): float
    {
        /** @var ComputedRejectionStatistic $stats */
        $stats = $companyCampaign->company->rejectionStatistics()
            ->where(ComputedRejectionStatistic::FIELD_PRODUCT_ID, $companyCampaign->product_id)
            ->first();

        return $stats?->crm_rejection_percentage ?? 0;
    }
}
