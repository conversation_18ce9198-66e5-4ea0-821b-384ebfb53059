<?php

namespace App\Services\Odin\TopCompanies\Filters;

use App\Contracts\TopCompanies\TopCampaignsFilter;
use App\Enums\Odin\PropertyType;
use App\Enums\Odin\QualityTier;
use App\Enums\Odin\SaleTypes;
use App\Http\Requests\CompanyCampaigns\PriceForCampaignsRequest;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\SaleType;
use App\Repositories\Campaigns\Modules\Budget\BudgetRepository;
use App\Repositories\LocationRepository;
use App\Services\Campaigns\Modules\Budget\BudgetUsageService;
use App\Services\Campaigns\ProductBiddingService;

class TopCampaignsBudgetFilter implements TopCampaignsFilter
{
    public function __construct(
        protected BudgetUsageService $budgetUsageService,
        protected BudgetRepository $budgetRepository,
        protected ProductBiddingService $productBiddingService,
        protected LocationRepository $locationRepository
    ) {}

    /**
     * @inheritDoc
     */
    public function filter(CompanyCampaign $companyCampaign, string $zipCode): bool
    {
        $countyLocation = $this->locationRepository->getCountyFromZipcode($zipCode);

        if (!$countyLocation) {
            return false;
        }

        $stateLocation = $this->locationRepository->getStateByStateAbbr($countyLocation->state_abbr);
        $verifiedBudget = $this->budgetRepository->getVerifiedBudgetForCampaign($companyCampaign);
        if (!$verifiedBudget) {
            return false;
        }

        $salesTypeId = SaleType::query()
            ->where(SaleType::FIELD_NAME, SaleTypes::EXCLUSIVE->name)
            ->first()->id;

        // calculating budget usage for budget: verified, quality tier: standard, property: residential, sale type: exclusive only
        // todo: calculate for others?
        return !$this->budgetUsageService->willBudgetBeExceeded(
            budget: $verifiedBudget,
            productCost: $this->productBiddingService->getProductBid(
                companyCampaign: $companyCampaign,
                countyLocationId: $countyLocation->id,
                stateLocationId: $stateLocation->id,
                propertyTypeId: PropertyType::RESIDENTIAL->model()->id,
                qualityTierId: QualityTier::STANDARD->model()->id,
                salesTypeId: $salesTypeId
            )
        );
    }
}
