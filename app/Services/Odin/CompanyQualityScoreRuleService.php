<?php

namespace App\Services\Odin;

use App\Jobs\PopulateCalculateCompanyQualityScoreQueueJob;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyQualityScoreIndustryConfiguration;
use App\Models\Odin\CompanyQualityScoreRule;
use App\Models\Odin\Industry;
use App\Models\Ruleset;
use App\Repositories\Odin\CompanyQualityScoreRuleRepository;
use Illuminate\Support\Collection;
use Exception;

class CompanyQualityScoreRuleService
{
    const REQUEST_ID                = 'id';
    const REQUEST_NAME              = 'name';
    const REQUEST_SLUG              = 'slug';
    const REQUEST_RULE_ID           = 'rule_id';
    const REQUEST_RULE_NAME         = 'rule_name';
    const REQUEST_GLOBAL            = 'global';
    const REQUEST_TEST_COMPANIES    = 'test_companies';
    const RESPONSE_ERROR            = 'error';
    const RESPONSE_RESULT           = 'result';

    public function __construct(
        protected CompanyQualityScoreRuleRepository $companyQualityScoreRuleRepository,
        protected CompanyRulesetService $companyRulesetService
    )
    {

    }


    /**
     * @param array $companyIds
     * @param array $rulesetData
     * @return array
     * @throws Exception
     */
    public function testRule(array $companyIds, array $rulesetData): array
    {
        $companyIds = array_unique($companyIds);

        $ruleset = new Ruleset($rulesetData);

        $rulesetService = $this->companyRulesetService->generateRulesetService($ruleset);

        $testResult = [];

        foreach ($companyIds as $companyId) {
            /** @var Company|null $company */
            $company = Company::query()->where(Company::FIELD_ID, $companyId)->first();
            $error = $result = null;

            if (!$company) {
                $error = "Company does not exist";
            } else {
                try {
                    $result = $rulesetService->calculateScore($company);
                } catch (Exception $exception) {
                    $error = $exception->getMessage();
                    logger()->error($exception);
                }
            }

            $testResult[$companyId] = [
                self::RESPONSE_ERROR  => $error,
                self::RESPONSE_RESULT => $result
            ];
        }

        return $testResult;
    }

    /**
     * @return CompanyQualityScoreRule
     */
    public function getGlobalRule(): CompanyQualityScoreRule
    {
        /** @var CompanyQualityScoreIndustryConfiguration $config */
        $config = CompanyQualityScoreIndustryConfiguration::query()
            ->where(CompanyQualityScoreIndustryConfiguration::FIELD_GLOBAL, true)
            ->firstOrFail();

        return $config->companyQualityScoreRule;
    }

    /**
     * Grab an Industry specific rule. Default to Global if nothing has been set
     * @param int $industryId
     * @return CompanyQualityScoreRule
     */
    public function getIndustryRule(int $industryId): CompanyQualityScoreRule
    {
        /** @var ?CompanyQualityScoreIndustryConfiguration $industryRule */
        $industryRule = CompanyQualityScoreIndustryConfiguration::query()
            ->where(CompanyQualityScoreIndustryConfiguration::FIELD_INDUSTRY_ID, $industryId)
            ->first();

        return $industryRule
            ? $industryRule->companyQualityScoreRule
            : $this->getGlobalRule();
    }

    /**
     * Set the rule for an Industry
     * @param int $industryId
     * @param ?int $ruleId
     * @return bool
     */
    public function setIndustryRule(int $industryId, ?int $ruleId): bool
    {
        Industry::query()
            ->findOrFail($industryId);
        if ($ruleId) {
            CompanyQualityScoreRule::query()
                ->findOrFail($ruleId);
        } else {
            $ruleId = null;
        }

        CompanyQualityScoreIndustryConfiguration::query()
            ->updateOrCreate([
                CompanyQualityScoreIndustryConfiguration::FIELD_INDUSTRY_ID => $industryId,
            ], [
                CompanyQualityScoreIndustryConfiguration::FIELD_GLOBAL                        => false,
                CompanyQualityScoreIndustryConfiguration::FIELD_COMPANY_QUALITY_SCORE_RULE_ID => $ruleId
            ]);

        return true;
    }

    /**
     * Set the global, default rule
     * @param int $ruleId
     * @return bool
     */
    public function setGlobalRule(int $ruleId): bool
    {
        CompanyQualityScoreRule::query()
            ->findOrFail($ruleId);

        CompanyQualityScoreIndustryConfiguration::query()
            ->updateOrCreate([
                CompanyQualityScoreIndustryConfiguration::FIELD_GLOBAL => true,
            ], [
                CompanyQualityScoreIndustryConfiguration::FIELD_COMPANY_QUALITY_SCORE_RULE_ID => $ruleId
            ]);

        return true;
    }

    /**
     * @param ?int $industryId
     * @param bool $global
     * @return Collection
     */
    public function getTestCompanies(?int $industryId, bool $global = false): Collection
    {
        if (!$global) {
            Industry::query()
                ->findOrFail($industryId);

            /** @var CompanyQualityScoreIndustryConfiguration $config $ids */
            $config = CompanyQualityScoreIndustryConfiguration::query()
                ->where(CompanyQualityScoreIndustryConfiguration::FIELD_INDUSTRY_ID, $industryId)
                ->first();
        }
        else {
            $config = CompanyQualityScoreIndustryConfiguration::query()
                ->where(CompanyQualityScoreIndustryConfiguration::FIELD_GLOBAL, true)
                ->first();
        }

        $ids = $config?->test_company_ids ?? [];

        return $this->transformTestCompanies($ids);
    }

    /**
     * Set default company IDs for running tests on companies.
     * @param ?int $industryId
     * @param array $companyIds
     * @param bool $global
     * @return bool
     */
    public function setTestCompanyIds(?int $industryId, array $companyIds, bool $global = false): bool
    {
        if (!$global) {
            Industry::query()
                ->findOrFail($industryId);

            CompanyQualityScoreIndustryConfiguration::query()
                ->updateOrCreate([
                    CompanyQualityScoreIndustryConfiguration::FIELD_INDUSTRY_ID => $industryId,
                ], [
                    CompanyQualityScoreIndustryConfiguration::FIELD_TEST_COMPANY_IDS => $companyIds
                ]);
        }
        else {
            CompanyQualityScoreIndustryConfiguration::query()
                ->updateOrCreate([
                    CompanyQualityScoreIndustryConfiguration::FIELD_GLOBAL => true,
                ], [
                    CompanyQualityScoreIndustryConfiguration::FIELD_TEST_COMPANY_IDS => $companyIds
                ]);
        }

        return true;
    }

    /**
     * Returns an array of all Industries, with CQS configuration if one exists.
     * Includes 'global' key for global CQS config
     * @return Collection
     */
    public function getConfigurations(): Collection
    {
        $industries = Industry::query()->select(['id', 'slug', 'name'])
            ->get()
            ->keyBy(Industry::FIELD_SLUG)
            ->map(fn(Industry $industry) => [
                ...$industry->toArray(),
                self::REQUEST_RULE_ID        => null,
                self::REQUEST_RULE_NAME      => null,
                self::REQUEST_TEST_COMPANIES => [],
                self::REQUEST_GLOBAL         => false,
            ]);
        $industries->put('global', [
            self::REQUEST_ID    => null,
            self::REQUEST_NAME  => 'Global',
            self::REQUEST_SLUG  => 'global',
        ]);

        $configurations = CompanyQualityScoreIndustryConfiguration::query()
            ->with([
                CompanyQualityScoreIndustryConfiguration::RELATION_COMPANY_QUALITY_SCORE_RULE,
                CompanyQualityScoreIndustryConfiguration::RELATION_INDUSTRY,
            ])
            ->get();

        $configurations->each(function(CompanyQualityScoreIndustryConfiguration $config) use (&$industries) {
            $key = $config->global ? 'global' : $config->industry->slug;
            $industries[$key] = [
                ...($industries[$key] ?? []),
                self::REQUEST_GLOBAL         => $config->global,
                self::REQUEST_RULE_ID        => $config->company_quality_score_rule_id ?? null,
                self::REQUEST_RULE_NAME      => $config->companyQualityScoreRule?->{CompanyQualityScoreRule::FIELD_NAME} ?? null,
                self::REQUEST_TEST_COMPANIES => $this->transformTestCompanies($config->test_company_ids ?? [])
            ];
        });

        return $industries;
    }

    /**
     * @return array
     */
    public function getProductionRules(): array
    {
        return CompanyQualityScoreRule::query()
            ->where(CompanyQualityScoreRule::FIELD_IS_PRODUCTION, true)
            ->select(CompanyQualityScoreRule::FIELD_NAME, CompanyQualityScoreRule::FIELD_ID)
            ->get()
            ->toArray();
    }

    /**
     * @param array $testCompanyIds
     * @return Collection
     */
    private function transformTestCompanies(array $testCompanyIds): Collection
    {
        return Company::query()
            ->whereIn(Company::FIELD_ID, $testCompanyIds)
            ->select(['id', 'name'])
            ->get();
    }


    /**
     * Trigger calculate company quality score job given a configuration
     * @param CompanyQualityScoreRule $companyQualityScoreRule
     * @return bool
     */
    public function trigger(CompanyQualityScoreRule $companyQualityScoreRule): bool
    {
        $ruleset = $companyQualityScoreRule->ruleset;

        if (!$ruleset) return false;

        PopulateCalculateCompanyQualityScoreQueueJob::dispatch([$companyQualityScoreRule]);

        return true;
    }
}
