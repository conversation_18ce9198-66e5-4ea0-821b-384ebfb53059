<?php

namespace App\Services\Odin\ProductStatistics;

use App\Enums\Odin\BudgetCategory;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\QualityTier as QualityTierEnum;
use App\Enums\Odin\SaleTypes as SaleTypesEnum;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Odin\Company;
use App\Models\Odin\ServiceProduct;
use App\Models\SaleType;
use App\Repositories\Odin\ProductStatistics\ProductStatisticsRepository;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Carbon\CarbonTimeZone;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

class ProductStatisticsService
{
    const string DATA_KEY_AVAILABLE           = 'available';
    const string DATA_KEY_RECEIVED            = 'received';
    const string DATA_KEY_PURCHASED           = 'purchased';
    const string DATA_KEY_COST                = 'cost';
    const string DATA_KEY_SPENT               = 'spent';
    const string DATA_DAYS_OF_STATS           = 'days_of_stats';
    const string DATA_KEY_START_TIMESTAMP     = 'start_timestamp';
    const string DATA_KEY_END_TIMESTAMP       = 'end_timestamp';
    const string DATA_KEY_BUDGET_ENABLED      = 'enabled';
    const string DATA_KEY_BUDGET_PERCENTAGE   = 'percent';
    const string DATA_KEY_SERVICE_ID          = 'industry_service_id';
    const string PAYLOAD_KEY_SCOPED           = 'scoped';

    const int CACHE_TTL                = 86400;
    const int MAX_DAYS_OF_BUDGET_STATS = 30;

    const string QUERY_TYPE_BIDDING     = 'bidding';
    const string QUERY_TYPE_CAMPAIGN    = 'campaign';
    const string QUERY_TYPE_ANALYTICS   = 'analytics';
    const string QUERY_TYPE_PRODUCTS    = 'products';

    const string CACHE_KEY_QUERY_TYPE     = 'type';
    const string CACHE_KEY_COMPANY_ID     = 'companyId';
    const string CACHE_KEY_SERVICE_ID     = 'serviceProductId';
    const string CACHE_KEY_STATE_ID       = 'stateLocationId';
    const string CACHE_KEY_COUNTY_ID      = 'countyLocationId';
    const string CACHE_KEY_START_DATE     = 'startDate';
    const string CACHE_KEY_END_DATE       = 'endDate';
    const string CACHE_KEY_CAMPAIGN_COUNT = 'campaignCount';

    protected bool $isSolar = false;

    protected CarbonTimeZone $timezone;

    public function __construct(
        protected ProductStatisticsRepository $productStatisticsRepository,
    ) {
        $this->timezone = CarbonTimeZone::create();
    }

    /**
     * Handle the analytics landing page default view - use a single volume query instead of multiple
     * @param int $companyId
     * @param int $serviceProductId
     * @return array
     */
    public function getAnalyticsPageDefaultStatistics(int $companyId, int $serviceProductId, Carbon $startDate): array
    {
        $baseQueryData = $this->getBaseAnalyticsQuery($companyId, [$serviceProductId], $startDate);

        $processedVolumeData = $this->processVolumeBreakdownStatistics($baseQueryData, $startDate, false);
        $processedProfitabilityData = $this->processProfitabilityStatistics($baseQueryData, $serviceProductId);

        return [
            "volume" => [
                self::PAYLOAD_KEY_SCOPED => $processedVolumeData,
            ],
            "profitability" => [
                self::PAYLOAD_KEY_SCOPED => $processedProfitabilityData
            ],
        ];
    }

    /**
     * Handle queries from Lead Volume dashboard component
     * TODO: re-implement multi-service statistics
     * @param int $companyId
     * @param array $serviceProductIds
     * @param Carbon $startDate
     * @return array
     */
    public function getCompanyVolumeStatistics(int $companyId, array $serviceProductIds, Carbon $startDate): array
    {
        $monthlyGrouping = (int) now()->diffInMonths($startDate, true) > 5;
        $baseData = $this->getBaseAnalyticsQuery($companyId, $serviceProductIds, $startDate, $monthlyGrouping);

        return [
            self::PAYLOAD_KEY_SCOPED => $this->processVolumeBreakdownStatistics($baseData, $startDate, $monthlyGrouping),
        ];
    }

    /**
     * Handle queries from Profitability dashboard component
     * TODO: re-implement multi-service statistics
     * @param int $companyId
     * @param array $serviceProductIds
     * @param Carbon $startDate
     * @return array
     */
    public function getCompanyProfitabilityStatistics(int $companyId, array $serviceProductIds, Carbon $startDate): array
    {
        $baseData = $this->getBaseAnalyticsQuery($companyId, $serviceProductIds, $startDate);

        return [
            self::PAYLOAD_KEY_SCOPED => $this->processProfitabilityStatistics($baseData, $serviceProductIds[0]),
        ];
    }

    /**
     * Handle queries for Lead Type Purchased dashboard component
     * @param int $companyId
     * @param int $serviceProductId
     * @return array
     */
    public function getCompanyPurchasedTypeStatistics(int $companyId, int $serviceProductId): array
    {
        $cacheKey = $this->getCacheKey([
            self::CACHE_KEY_QUERY_TYPE => self::QUERY_TYPE_PRODUCTS,
            self::CACHE_KEY_COMPANY_ID => $companyId,
            self::CACHE_KEY_SERVICE_ID => $serviceProductId,
        ]);

        $baseData = Cache::remember($cacheKey, self::CACHE_TTL, function() use($companyId, $serviceProductId) {
            return $this->productStatisticsRepository->getPurchasedProductTypeStatistics($companyId, $serviceProductId)
                ->get();
        });

        return $this->processProductTypeBreakdown($baseData);
    }

    /**
     * @param int $companyId
     * @param array $serviceProductIds
     * @param Carbon $startDate
     * @param bool|null $monthlyGrouping
     * @return Collection
     */
    protected function getBaseAnalyticsQuery(int $companyId, array $serviceProductIds, Carbon $startDate, ?bool $monthlyGrouping = false): Collection
    {
        $cacheKey = $this->getCacheKey([
            self::CACHE_KEY_QUERY_TYPE => self::QUERY_TYPE_ANALYTICS,
            self::CACHE_KEY_COMPANY_ID => $companyId,
            self::CACHE_KEY_SERVICE_ID => implode(',', $serviceProductIds),
            self::CACHE_KEY_START_DATE => $startDate->toDateString(),
        ]);

        $this->productStatisticsRepository->setTimezone($this->timezone);

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($companyId, $serviceProductIds, $startDate, $monthlyGrouping) {
            return $this->productStatisticsRepository->getAnalyticsVolumeStatistics($companyId, $serviceProductIds, $startDate, $monthlyGrouping)
               ->get();
        });
    }

    /**
     * @param int $companyId
     * @param int $serviceProductId
     * @return array
     */
    public function getCampaignListStatistics(int $companyId, int $serviceProductId): array
    {
        /** @var Company $company */
        $company = Company::query()->findOrFail($companyId);
        $allScopedCampaigns = $company->futureCampaigns()
            ->join(ServiceProduct::TABLE, fn(JoinClause $join) =>
                $join->on(ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_SERVICE_ID)
                    ->on(ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_PRODUCT_ID, CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_PRODUCT_ID)
            )->where(ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_ID, $serviceProductId)
            ->pluck(CompanyCampaign::FIELD_REFERENCE)
            ->toArray();

        $cacheKey = $this->getCacheKey([
            self::CACHE_KEY_QUERY_TYPE => self::QUERY_TYPE_CAMPAIGN,
            self::CACHE_KEY_COMPANY_ID => $companyId,
            self::CACHE_KEY_SERVICE_ID => $serviceProductId,
            self::CACHE_KEY_CAMPAIGN_COUNT => count($allScopedCampaigns)
        ]);

        return Cache::remember($cacheKey, self::CACHE_TTL, function() use ($companyId, $serviceProductId, $allScopedCampaigns) {
           $baseData = $this->productStatisticsRepository->getCampaignListStatistics(
                   companyId: $companyId,
                   serviceProductId: $serviceProductId,
               )->get()
               ->keyBy(CompanyCampaign::FIELD_REFERENCE);

            return $this->processCampaignListStatistics($baseData, $allScopedCampaigns);
        });
    }

    /**
     * @param int $companyId
     * @param int $serviceProductId
     * @param int|null $stateLocationId
     * @param int|null $countyLocationId
     * @return array
     */
    public function getBiddingStatisticsByLocation(
        int $companyId,
        int $serviceProductId,
        ?int $stateLocationId,
        ?int $countyLocationId,
    ): array
    {
        $this->setIsSolar($serviceProductId);
        $cacheKey = $this->getCacheKey([
            self::CACHE_KEY_QUERY_TYPE => self::QUERY_TYPE_BIDDING,
            self::CACHE_KEY_COMPANY_ID => $companyId,
            self::CACHE_KEY_SERVICE_ID => $serviceProductId,
            self::CACHE_KEY_STATE_ID   => $stateLocationId,
            self::CACHE_KEY_COUNTY_ID  => $countyLocationId
        ]);

        return Cache::remember($cacheKey, self::CACHE_TTL, function() use ($companyId, $serviceProductId, $stateLocationId, $countyLocationId) {
            $baseData = $this->productStatisticsRepository->getLocationStatistics(
                companyId: $companyId,
                serviceProductId: $serviceProductId,
                stateLocationId: $stateLocationId,
                countyLocationId: $countyLocationId
            )->get();

            return $this->processBiddingStatistics($baseData);
        });
    }

    /**
     * @param int $companyId
     * @param int $serviceProductId
     * @return void
     */
    public function preCacheStatistics(int $companyId, int $serviceProductId): void
    {
        $this->getBaseAnalyticsQuery($companyId, [$serviceProductId], now()->subDays(30));
        $this->getCampaignListStatistics($companyId, $serviceProductId);
    }

    /**
     * @param Collection $volumeData
     * @param Carbon $startDate
     * @param bool $monthlyGrouping
     * @return array
     */
    protected function processVolumeBreakdownStatistics(Collection $volumeData, Carbon $startDate, bool $monthlyGrouping = false): array
    {
        $outputData = [];
        $volumeData = $volumeData->keyBy(ProductStatisticsRepository::ALIAS_PRODUCT_CREATED_AT);
        $defaultGroup = [self::DATA_KEY_AVAILABLE => 0, self::DATA_KEY_PURCHASED => 0, self::DATA_KEY_RECEIVED => 0, self::DATA_KEY_SPENT => 0];
        $statsPeriod = $monthlyGrouping
            ? CarbonPeriod::create($startDate, '1 month', now())
            : CarbonPeriod::create($startDate, '1 day', now());

        $statsPeriod = $statsPeriod->setTimezone($this->getTimezone());

        foreach($statsPeriod as $period) {
            $periodKey = $monthlyGrouping
                ? $period->format('Y-m')
                : $period->format('Y-m-d');
            $start = $monthlyGrouping
                ? $period->startOfMonth()->timestamp
                : $period->startOfDay()->timestamp;
            $end = $monthlyGrouping
                ? $period->endOfMonth()->timestamp
                : $period->endOfDay()->timestamp;

            $outputData[$periodKey] = [
                ...$defaultGroup,
                self::DATA_KEY_START_TIMESTAMP => $start,
                self::DATA_KEY_END_TIMESTAMP   => $end,
            ];

            $statsForPeriod = $volumeData->get($periodKey, null);

            if ($statsForPeriod) {
                $outputData[$periodKey][self::DATA_KEY_AVAILABLE] = $statsForPeriod->{ProductStatisticsRepository::ALIAS_CONSUMER_PRODUCT_COUNT} ?? 0;
                $outputData[$periodKey][self::DATA_KEY_PURCHASED] = $statsForPeriod->{ProductStatisticsRepository::ALIAS_CHARGEABLE_COUNT} ?? 0;
                $outputData[$periodKey][self::DATA_KEY_RECEIVED] = $statsForPeriod->{ProductStatisticsRepository::ALIAS_DELIVERED_COUNT} ?? 0;
                $outputData[$periodKey][self::DATA_KEY_SPENT] = $statsForPeriod->{ProductStatisticsRepository::ALIAS_CHARGEABLE_COST} ?? 0;
            }
        }

        return $outputData;
    }

    /**
     * @param Collection $baseVolumeData
     * @param int $serviceProductId
     * @return array
     */
    protected function processProfitabilityStatistics(Collection $baseVolumeData, int $serviceProductId): array
    {
        $industryServiceId = $this->getIndustryServiceId($serviceProductId);

        $outputData = [
            self::DATA_KEY_SERVICE_ID => $industryServiceId,
            self::DATA_KEY_AVAILABLE  => 0,
            self::DATA_KEY_RECEIVED   => 0,
            self::DATA_KEY_PURCHASED  => 0,
            self::DATA_KEY_SPENT      => 0,
        ];

        $baseVolumeData->each(function($dataGroup) use(&$outputData) {
            $outputData[self::DATA_KEY_AVAILABLE] += ($dataGroup->{ProductStatisticsRepository::ALIAS_CONSUMER_PRODUCT_COUNT} ?? 0);
            $outputData[self::DATA_KEY_RECEIVED]  += ($dataGroup->{ProductStatisticsRepository::ALIAS_DELIVERED_COUNT} ?? 0);
            $outputData[self::DATA_KEY_PURCHASED] += ($dataGroup->{ProductStatisticsRepository::ALIAS_CHARGEABLE_COUNT} ?? 0);
            $outputData[self::DATA_KEY_SPENT]     += ($dataGroup->{ProductStatisticsRepository::ALIAS_CHARGEABLE_COST} ?? 0);
        });

        $outputData[self::DATA_KEY_SPENT] = round($outputData[self::DATA_KEY_SPENT], 0);

        return $outputData;
    }

    /**
     * @param Collection $baseData
     * @param string[] $campaignReferences
     * @return array
     */
    protected function processCampaignListStatistics(Collection $baseData, array $campaignReferences): array
    {
        $campaignStatsGroup = [self::DATA_KEY_AVAILABLE => 0, self::DATA_KEY_PURCHASED => 0, self::DATA_KEY_COST => 0, self::DATA_DAYS_OF_STATS => 30];
        $outputData = [];

        foreach($campaignReferences as $campaignReference) {
            $outputData[$campaignReference] = [...$campaignStatsGroup];
            $stats = $baseData->get($campaignReference);
            if ($stats) {
                $outputData[$campaignReference][self::DATA_KEY_AVAILABLE] = $stats->{ProductStatisticsRepository::ALIAS_CONSUMER_PRODUCT_COUNT} ?? 0;
                $outputData[$campaignReference][self::DATA_KEY_PURCHASED] = $stats->{ProductStatisticsRepository::ALIAS_CHARGEABLE_COUNT} ?? 0;
                $outputData[$campaignReference][self::DATA_KEY_COST] = $stats->{ProductStatisticsRepository::ALIAS_CHARGEABLE_COST} ?? 0;

                $daysOfStats = (int) now()->diffInDays($stats->{Budget::FIELD_LAST_MODIFIED_AT}, true) ?: 1;
                $outputData[$campaignReference][self::DATA_DAYS_OF_STATS] = min($daysOfStats, self::MAX_DAYS_OF_BUDGET_STATS);
            }
        }

        return $outputData;
    }

    /**
     * @param Collection $baseData
     * @return array
     */
    protected function processBiddingStatistics(Collection $baseData): array
    {
        $biddingStatsGroup = [self::DATA_KEY_AVAILABLE => 0, self::DATA_KEY_PURCHASED => 0];
        $outputData = [
            QualityTierEnum::STANDARD->value => $this->getEmptySaleTypesArray($biddingStatsGroup),
            QualityTierEnum::PREMIUM->value => $this->getEmptySaleTypesArray($biddingStatsGroup),
        ];

        return $baseData->reduce(function($output, $dataGroup) {
            $qualityTier = $this->getProductQualityTier($dataGroup[ProductStatisticsRepository::ALIAS_PREMIUM_ELECTRIC_COST] ?? false);
            $saleType = $this->getProductSaleType($dataGroup[ProductStatisticsRepository::ALIAS_BUDGET_TYPE], $dataGroup[ProductStatisticsRepository::ALIAS_CONSUMER_SALE_KEY], $dataGroup[ProductStatisticsRepository::ALIAS_ASSIGNMENT_SALE_KEY]);
            $targetArray = &$output[$qualityTier][$saleType];

            $targetArray[self::DATA_KEY_AVAILABLE] += max($dataGroup[ProductStatisticsRepository::ALIAS_CONSUMER_PRODUCT_COUNT], $dataGroup[ProductStatisticsRepository::ALIAS_CHARGEABLE_COUNT], 0);
            $targetArray[self::DATA_KEY_PURCHASED] += $dataGroup[ProductStatisticsRepository::ALIAS_CHARGEABLE_COUNT] ?? 0;

            return $output;
        }, $outputData);
    }

    /**
     * @param Collection $baseProductData
     * @return array
     */
    protected function processProductTypeBreakdown(Collection $baseProductData): array
    {
        $defaultGrouping = [self::DATA_KEY_BUDGET_ENABLED => false, self::DATA_KEY_BUDGET_PERCENTAGE => 0];
        $optionSaleTypes = [BudgetCategory::UNVERIFIED->value, BudgetCategory::EMAIL_ONLY->value];
        $outputData = SaleType::query()
            ->pluck(SaleType::FIELD_KEY)
            ->mapWithKeys(fn($key) => [
                $key => [...$defaultGrouping]
            ])->toArray();
        $totalPurchased = $baseProductData->sum(ProductStatisticsRepository::ALIAS_CHARGEABLE_COUNT);
        $baseProductData = $baseProductData->keyBy(SaleType::FIELD_KEY);

        foreach($outputData as $saleTypeKey => &$dataGroup) {
            $typeStatistics = $baseProductData->get($saleTypeKey, []);
            $dataGroup[self::DATA_KEY_BUDGET_ENABLED] = !in_array($saleTypeKey, $optionSaleTypes) || ($typeStatistics[ProductStatisticsRepository::ALIAS_BUDGET_ENABLED] ?? false);
            if ($totalPurchased && $typeStatistics)
                $dataGroup[self::DATA_KEY_BUDGET_PERCENTAGE] = round((($typeStatistics[ProductStatisticsRepository::ALIAS_CHARGEABLE_COUNT] ?? 0) / $totalPurchased) * 100, 1);
        }

        return $outputData;
    }

    /**
     * @param bool $premiumElectricCost
     * @return string
     */
    protected function getProductQualityTier(bool $premiumElectricCost): string
    {
        return $this->isSolar && $premiumElectricCost
            ? QualityTierEnum::PREMIUM->value
            : QualityTierEnum::STANDARD->value;
    }

    /**
     * @param int $budgetType
     * @param string|null $consumerSaleType
     * @param string|null $assignedSaleType
     * @return string
     */
    protected function getProductSaleType(int $budgetType, ?string $consumerSaleType, ?string $assignedSaleType): string
    {
        if ($assignedSaleType)
            return $assignedSaleType;
        else if ($budgetType === ProductStatisticsRepository::BUDGET_TYPE_EMAIL_ONLY)
            return SaleTypesEnum::key(SaleTypesEnum::EMAIL_ONLY);
        else if ($budgetType === ProductStatisticsRepository::BUDGET_TYPE_UNVERIFIED)
            return SaleTypesEnum::key(SaleTypesEnum::UNVERIFIED);
        else
            return $consumerSaleType;
    }

    /**
     * @param int $serviceProductId
     * @return void
     */
    protected function setIsSolar(int $serviceProductId): void
    {
        $this->isSolar = ServiceProduct::query()
            ->find($serviceProductId)
            ?->service
            ?->industry
            ->name === IndustryEnum::SOLAR->value;
    }

    /**
     * @param array $keys
     * @return string
     */
    protected function getCacheKey(array $keys): string
    {
        if (!array_key_exists(self::CACHE_KEY_END_DATE, $keys))
            $keys[self::CACHE_KEY_END_DATE] = now()->toDateString();

        return collect($keys)->reduce(function($output, $value, $key) {
            $separator = $output ? '_' : '';
            $value = $value ?? '0';

            return "$output$separator$key-$value";
        }, '');
    }

    /**
     * @param array $saleTypeContents
     * @return array
     */
    protected function getEmptySaleTypesArray(array $saleTypeContents = []): array
    {
        return array_reduce(SaleTypesEnum::allSaleTypeKeys(), fn($output, $key) =>
            [...$output, $key => $saleTypeContents]
        , []);
    }

    /**
     * @param int $serviceProductId
     * @return int
     */
    protected function getIndustryServiceId(int $serviceProductId): int
    {
        return ServiceProduct::query()
            ->findOrFail($serviceProductId)
            ->industry_service_id;
    }

    function setTimezone(CarbonTimeZone $timezone) {
        $this->timezone = $timezone;
    }

    function getTimezone() {
        return $this->timezone;
    }
}
