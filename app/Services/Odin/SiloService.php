<?php

namespace App\Services\Odin;

use App\Enums\Odin\LocationSiloPageLocationType;
use App\Http\Requests\Odin\CreateSiloRequest;
use App\Models\Legacy\Location;
use App\Models\Odin\Industry;
use App\Models\Odin\LocationSiloPage;
use App\Models\Odin\Silo;
use App\Repositories\Odin\SiloRepository;
use Illuminate\Support\Collection;

class SiloService
{
    const LOCATION_IDS              = 'location_ids';
    const RELATIVE_PATH_NATIONAL    = 'relative_path_national';
    const RELATIVE_PATH_STATE       = 'relative_path_state';
    const RELATIVE_PATH_CITY        = 'relative_path_city';

    public function __construct(
        protected SiloRepository $siloRepository,
        protected SiloShortcodeService  $shortcodeService,
    ) {}

    public function createSiloAndLocations(Collection $combinedData): Silo
    {
        $siloAttributes = $combinedData->filter(fn($value, $key) => in_array($key, [
            Silo::FIELD_NAME,
            Silo::FIELD_ROOT_PATH,
            Silo::FIELD_COLLECTION_HANDLE,
            Silo::FIELD_WEBSITE_ID,
            Silo::FIELD_INDUSTRY_ID,
            Silo::FIELD_INDUSTRY_SERVICE_ID,
            Silo::FIELD_FLOW_ID,
            Silo::FIELD_REVISION_ID,
        ]))->toArray();

        $commonLocationAttributes = $combinedData->filter(fn($value, $key) => in_array($key, [
            LocationSiloPage::FIELD_IS_ACTIVE,
        ]))->toArray();

        $locationIds = $combinedData->get(self::LOCATION_IDS);
        $locationType = LocationSiloPageLocationType::tryFrom($combinedData->get(LocationSiloPage::FIELD_LOCATION_TYPE));
        $relativePaths = [
            LocationSiloPageLocationType::NATIONAL->value   => $combinedData->get(self::RELATIVE_PATH_NATIONAL) ?? "",
            LocationSiloPageLocationType::STATE->value      => $combinedData->get(self::RELATIVE_PATH_STATE) ?? $combinedData->get(self::RELATIVE_PATH_NATIONAL) ?? "",
            LocationSiloPageLocationType::CITY->value       => $combinedData->get(self::RELATIVE_PATH_CITY) ?? $combinedData->get(self::RELATIVE_PATH_STATE) ?? $combinedData->get(self::RELATIVE_PATH_NATIONAL) ?? "",
        ];
        $entrySlugs = [
            LocationSiloPageLocationType::NATIONAL->value   => $combinedData->get(CreateSiloRequest::ENTRY_SLUG_COUNTRY, ""),
            LocationSiloPageLocationType::STATE->value      => $combinedData->get(CreateSiloRequest::ENTRY_SLUG_STATE, ""),
            LocationSiloPageLocationType::CITY->value       => $combinedData->get(CreateSiloRequest::ENTRY_SLUG_CITY, ""),
        ];

        $newSilo = $this->siloRepository->createSilo($siloAttributes);

        $parentLocationMapping = $this->mapParentLocationsAndShortcodeLocationKeys($locationIds, $locationType);
        $locationSiloPagesData = [];

        $processShortcodes = !!preg_match("/\{[0-z-]+}/", implode("", $relativePaths));
        $commonShortcodes = [
            SiloShortcodeService::SILO_CODE_COUNTRY     => 'US',
            SiloShortcodeService::SILO_CODE_INDUSTRY    => Industry::query()->find($siloAttributes[Silo::FIELD_INDUSTRY_ID])?->slug ?? 'missing-industry',
        ];

        foreach($parentLocationMapping as $locationTypeId => $locations) {
            foreach($locations as $locationId => $locationData) {
                $locationType = LocationSiloPageLocationType::tryFrom($locationTypeId);
                $relativePath = $relativePaths[$locationType->value] ?? "";

                $locationSiloPagesData[] = [
                    ...$commonLocationAttributes,
                    LocationSiloPage::FIELD_LOCATION_TYPE       => $locationType,
                    LocationSiloPage::FIELD_SILO_ID             => $newSilo->id,
                    LocationSiloPage::FIELD_PARENT_LOCATION_ID  => $locationData[LocationSiloPage::FIELD_PARENT_LOCATION_ID],
                    LocationSiloPage::FIELD_LOCATION_ID         => $locationId,
                    LocationSiloPage::FIELD_RELATIVE_PATH       => $processShortcodes
                        ? $this->shortcodeService->processShortcodes($relativePath, $locationData, $commonShortcodes)
                        : $relativePath,
                    LocationSiloPage::FIELD_ENTRY_SLUG          => $entrySlugs[$locationType->value],
                ];
            }
        }

        $this->siloRepository->createLocationsForNewSilo($locationSiloPagesData);

        return $newSilo;
    }

    /**
     * //TODO: Update this method when Country data exists in DB
     * @param array $locationIds
     * @param LocationSiloPageLocationType $locationType
     * @return array|array[]
     */
    private function mapParentLocationsAndShortcodeLocationKeys(array $locationIds, LocationSiloPageLocationType $locationType): array
    {
        $output = [
            LocationSiloPageLocationType::NATIONAL->value   => [],
            LocationSiloPageLocationType::STATE->value      => [],
            LocationSiloPageLocationType::CITY->value       => [],
        ];

        $countryLocationId = $locationType === LocationSiloPageLocationType::NATIONAL
            ? $locationIds[0]
            : null;

        $stateLocations = Location::query()->findMany($locationIds, [Location::ID, Location::STATE_KEY, Location::TYPE])
            ->where(Location::TYPE, Location::TYPE_STATE)
            ->keyBy(Location::STATE_KEY);

        if ($locationType === LocationSiloPageLocationType::CITY) {
            $cityLocations = Location::query()
                ->findMany($locationIds, [Location::ID, Location::STATE_KEY, Location::CITY_KEY, Location::TYPE])
                ->where(Location::TYPE, Location::TYPE_CITY);

            foreach ($cityLocations as $location) {
                $parentLocationId = $stateLocations->get($location->state_key)?->id ?? null;
                if (!$parentLocationId) continue;

                $output[LocationSiloPageLocationType::CITY->value][$location->id] = [
                    LocationSiloPage::FIELD_PARENT_LOCATION_ID  => $parentLocationId,
                    SiloShortcodeService::SILO_CODE_CITY        => $location->city_key,
                    SiloShortcodeService::SILO_CODE_STATE       => $location->state_key,
                ];
            }

            $countryLocationId = 0;
        }

        if ($stateLocations) {
            $countryLocationId = $countryLocationId ?? 0;

            foreach($stateLocations as $location) {
                $output[LocationSiloPageLocationType::STATE->value][$location->id] = [
                    LocationSiloPage::FIELD_PARENT_LOCATION_ID  => 0,
                    SiloShortcodeService::SILO_CODE_STATE       => $location->state_key,
                ];
            }
        }

        $output[LocationSiloPageLocationType::NATIONAL->value][$countryLocationId] = [
            LocationSiloPage::FIELD_PARENT_LOCATION_ID  => null,
        ];

        return $output;
    }


}
