<?php

namespace App\Services\Odin\Campaigns;

use App\Campaigns\Delivery\Contacts\Strategies\Email\AppointmentEmailDeliveryStrategy;
use App\Campaigns\Delivery\Contacts\Strategies\Email\BaseEmailDeliveryStrategy;
use App\Campaigns\Delivery\Contacts\Strategies\SMS\AppointmentSMSDeliveryStrategy;
use App\Campaigns\Delivery\Contacts\Strategies\SMS\BaseSMSDeliveryStrategy;
use App\Campaigns\Modules\Legacy\LegacyModule;
use App\Enums\Odin\AppointmentCancellationReason;
use App\Enums\Odin\Product as ProductEnum;
use App\Enums\Odin\QualityTier as QualityTierEnum;
use App\Enums\Odin\SaleTypes as SaleTypeEnum;
use App\Enums\Timezone;
use App\Jobs\Appointments\AttemptAppointmentDeliveryJob;
use App\Models\AppointmentDelivery;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\CompanyCampaignRelation;
use App\Models\Campaigns\CompanyCampaignSchedule;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleContact;
use App\Models\Odin\Address;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAppointment;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCancellation;
use App\Repositories\Odin\ProductProcessing\FloorPricingRepository;
use App\Repositories\Odin\ServiceProductRepository;
use App\Services\Odin\Appointments\AppointmentService;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Throwable;

/**
 * AppointmentService uses legacy models for campaigns / prices etc.
 * override methods as required
 */
class CompanyCampaignAppointmentService extends AppointmentService
{
    /**
     * @param Collection $campaigns
     * @param Collection $consumerProducts
     * @return array
     * @throws Throwable
     */
    public function getCompanyCampaignScheduleAvailability(Collection $campaigns, Collection $consumerProducts): array
    {
        $scheduleData = $consumerProducts->reduce(function(array $output, ConsumerProduct $consumerProduct) {
            $output[$consumerProduct->id] = [];
            return $output;
        }, []);

        $qualityTier = $consumerProducts->first()->{ConsumerProduct::RELATION_APPOINTMENT}?->{ProductAppointment::APPOINTMENT_TYPE};
        $scheduleIds = [];
        $campaignScheduleMap = CompanyCampaignSchedule::query()
            ->whereIn(CompanyCampaignSchedule::FIELD_COMPANY_CAMPAIGN_ID, $campaigns->pluck(CompanyCampaign::FIELD_ID)->toArray())
            ->get()
            ->reduce(function(array $output, CompanyCampaignSchedule $campaignSchedule) use (&$scheduleIds) {
                $output[$campaignSchedule->company_campaign_id] = $output[$campaignSchedule->company_campaign_id] ?? [];
                $output[$campaignSchedule->company_campaign_id][$campaignSchedule->schedule_id] = false;
                if (!in_array($campaignSchedule->schedule_id, $scheduleIds))
                    $scheduleIds[] = $campaignSchedule->schedule_id;
                return $output;
            }, []);

        if (!count($scheduleIds) || !$qualityTier) return $scheduleData;

        /** @var AppointmentSchedulingIntegrationService $schedulingIntegrationService */
        $schedulingIntegrationService = app(AppointmentSchedulingIntegrationService::class);

        foreach($consumerProducts as $consumerProduct) {
            $consumerProduct->loadMissing([ConsumerProduct::RELATION_APPOINTMENT, ConsumerProduct::RELATION_ADDRESS]);
            $appointmentDateTime = $this->getFormattedAppointmentDateTime($consumerProduct);

            $schedulesAvailable = $schedulingIntegrationService->getAvailabilityForSchedules($scheduleIds, $qualityTier, $appointmentDateTime, $consumerProduct->created_at) ?? [];

            foreach ($campaignScheduleMap as $campaignId => $campaignSchedules) {
                foreach ($campaignSchedules as $scheduleId => $value) {
                    if ($schedulesAvailable[$scheduleId] ?? false) {
                        $scheduleData[$consumerProduct->id][$campaignId]              = $scheduleData[$consumerProduct->id][$campaignId] ?? [];
                        $scheduleData[$consumerProduct->id][$campaignId][$scheduleId] = $schedulesAvailable[$scheduleId];
                    }
                }
            }
        }

        return $scheduleData;
    }

    /**
     * @param AppointmentDelivery $appointmentDelivery
     * @param ConsumerProduct $consumerProduct,
     * @return string
     */
    public function getConsumerUrl(AppointmentDelivery $appointmentDelivery, ConsumerProduct $consumerProduct): string
    {
        return $this->getConsumerAppointmentUrl(
            $appointmentDelivery->consumer_code,
            $appointmentDelivery->consumer_token,
            $consumerProduct->industryService->industry->name,
        );
    }

    /**
     * TODO: is this only fixr URL, or will we have other domains for solar/roofing after migration?
     *
     * @param int $appointmentProductAssignmentId
     * @return string
     */
    public function getCompanyUrl(int $appointmentProductAssignmentId): string
    {
        return config('app.dashboard.fixr_url') . "/appointments?appointment_id={$appointmentProductAssignmentId}";
    }

    /**
     * @param CompanyCampaign $companyCampaign
     * @param ProductAssignment $productAssignment
     * @return null|AppointmentDelivery
     */
    public function getAppointmentDelivery(CompanyCampaign $companyCampaign, ProductAssignment $productAssignment): ?AppointmentDelivery
    {
        /** @var null|AppointmentDelivery */
        return AppointmentDelivery::query()
            ->where([
                AppointmentDelivery::FIELD_COMPANY_CAMPAIGN_ID => $companyCampaign->id,
                AppointmentDelivery::FIELD_PRODUCT_ASSIGNMENT_ID => $productAssignment->id
            ])->first();
    }

    /**
     * @param CompanyCampaign $companyCampaign
     * @param ProductAssignment $productAssignment
     * @return AppointmentDelivery
     * @throws Exception
     */
    public function createNewAppointmentDelivery(CompanyCampaign $companyCampaign, ProductAssignment $productAssignment): AppointmentDelivery
    {
        /** @var AppointmentDelivery */
        return AppointmentDelivery::query()
            ->create([
                AppointmentDelivery::FIELD_COMPANY_CAMPAIGN_ID    => $companyCampaign->id,
                AppointmentDelivery::FIELD_PRODUCT_ASSIGNMENT_ID  => $productAssignment->id,
                AppointmentDelivery::FIELD_CONSUMER_TOKEN         => $this->generateKey(),
                AppointmentDelivery::FIELD_CONSUMER_CODE          => $this->generateCode(),
                AppointmentDelivery::FIELD_ATTEMPTS               => 0,
                AppointmentDelivery::FIELD_NEXT_ATTEMPT_TIMESTAMP => Carbon::now()->timestamp
            ]);
    }

    /**
     * @param int $scheduleId
     * @param ProductAssignment $productAssignment
     * @return bool
     * @throws Throwable
     */
    public function addAppointmentToSchedule(int $scheduleId, ProductAssignment $productAssignment): bool
    {
        /** @var AppointmentSchedulingIntegrationService $schedulingIntegrationService */
        $schedulingIntegrationService = app(AppointmentSchedulingIntegrationService::class);
        $productAssignmentId = $productAssignment->id;
        $consumerProduct = $productAssignment->consumerProduct;
        $consumer = $consumerProduct->consumer;
        $appointment = $consumerProduct->appointment;

        try {
            return $schedulingIntegrationService->addEventToCalendar(
                calendarId: $scheduleId,
                eventType: QualityTierEnum::from($productAssignment->qualityTier->name),
                eventName: "Appointment $productAssignmentId",
                eventDateTime: Carbon::parse($appointment->appointment, (string) $consumerProduct->address->utc),
                consumerName: $consumer->getFullName(),
                consumerEmail: $consumer->email,
                productAssignmentId: $productAssignmentId,
            );
        }
        catch(Exception $e) {
            logger()->error("Appointment scheduling error, failed to push event to calendar for ProductAssignment $productAssignmentId - " . $e->getMessage());

            return false;
        }
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @return Carbon
     */
    public function getFormattedAppointmentDateTime(ConsumerProduct $consumerProduct): Carbon
    {
        return Carbon::createFromFormat(
            "Y-m-d H:i:s",
            $consumerProduct->appointment->{ProductAppointment::APPOINTMENT},
            (string) $consumerProduct->{ConsumerProduct::RELATION_ADDRESS}->{Address::FIELD_UTC}
        );
    }

    /**
     * @param AppointmentDelivery $appointmentDelivery
     * @param AppointmentCancellationReason $cancellationReason
     * @param string $cancellationNote
     * @param int $rescheduledDateTimestamp
     * @return bool
     * @throws Throwable
     */
    public function cancelOrRescheduleAppointmentForConsumer(
        AppointmentDelivery $appointmentDelivery,
        AppointmentCancellationReason $cancellationReason,
        string $cancellationNote,
        int $rescheduledDateTimestamp
    ): bool
    {
        /** @var ProductAssignment $productAssignment */
        $productAssignment = $appointmentDelivery->productAssignment()->with([
            ProductAssignment::RELATION_CONSUMER_PRODUCT,
            ProductAssignment::RELATION_CONSUMER_PRODUCT .'.'. ConsumerProduct::RELATION_ADDRESS,
            ProductAssignment::RELATION_CONSUMER_PRODUCT .'.'. ConsumerProduct::RELATION_INDUSTRY_SERVICE,
            ProductAssignment::RELATION_CONSUMER_PRODUCT .'.'. ConsumerProduct::RELATION_INDUSTRY_SERVICE .'.'. IndustryService::RELATION_INDUSTRY,
        ])->first();

        /** @var CompanyCampaign $companyCampaign */
        $companyCampaign = $appointmentDelivery->companyCampaign()->with([
                CompanyCampaign::RELATION_COMPANY,
            ])->first();

        try {
            if ($cancellationReason === AppointmentCancellationReason::RESCHEDULED) {
                return $this->rescheduleAppointmentForConsumer(
                    $productAssignment,
                    $companyCampaign,
                    $cancellationReason,
                    $cancellationNote,
                    $rescheduledDateTimestamp
                );
            } else if ($cancellationReason === AppointmentCancellationReason::OTHER
                || $cancellationReason === AppointmentCancellationReason::NOT_INTERESTED) {
                return $this->cancelAppointmentForConsumer(
                    $productAssignment,
                    $companyCampaign,
                    $cancellationReason,
                    $cancellationNote,
                );
            }
        }
        catch(Throwable $e) {
            $this->handleFailedAppointmentUpdate("update failed for ProductAssignment $productAssignment->id - " . $e->getMessage());
        }

        return false;
    }

    /**
     * @param ProductAssignment $productAssignment
     * @param CompanyCampaign $companyCampaign
     * @param AppointmentCancellationReason $cancellationReason
     * @param string $cancellationNote
     * @return bool
     * @throws Throwable
     */
    protected function cancelAppointmentForConsumer(
        ProductAssignment $productAssignment,
        CompanyCampaign $companyCampaign,
        AppointmentCancellationReason $cancellationReason,
        string $cancellationNote,
    ): bool
    {
        $leadFloorPrice = $this->getLeadFloorPrice($companyCampaign, $productAssignment);

        $rejectionExpiry = $this->productRejectionRepository->getRejectionExpiry();

        $alreadyCancelled = ProductCancellation::query()
            ->where(ProductCancellation::FIELD_PRODUCT_ASSIGNMENT_ID, $productAssignment->id)
            ->whereNull(ProductCancellation::FIELD_DELETED_AT)
            ->exists();

        if ($alreadyCancelled)
            throw new Exception("ProductAssignment $productAssignment->id has already been cancelled");

        DB::beginTransaction();

        $leadProductAssignment = $this->demoteAppointmentAssignmentToLead(
            $productAssignment,
            $leadFloorPrice,
            true,
            $rejectionExpiry,
            0,
            SaleTypeEnum::from($productAssignment->saleType->name),
        );
        // demoted lead is essentially already delivered. Remove the rejection window from non-chargeable original
        $leadProductAssignment->update([
            ProductAssignment::FIELD_DELIVERED    => $productAssignment->delivered,
            ProductAssignment::FIELD_DELIVERED_AT => $productAssignment->delivered_at
        ]);
        $productAssignment->update([ProductAssignment::FIELD_REJECTION_EXPIRY => now()->subDay()]);

        // Sync the original as non-chargeable or the new quote company will be rejected by sync service
        $this->syncProductAssignmentToLegacy($companyCampaign, $productAssignment, true);

        // LegacyModule will fill in the legacy_id left as 0 above
        $this->syncProductAssignmentToLegacy($companyCampaign, $leadProductAssignment, false);

        $this->createAppointmentProductCancellation(
            $productAssignment->{ProductAssignment::FIELD_ID},
            $cancellationReason,
            $cancellationNote
        );

        DB::commit();

        $this->deliverCancellations($companyCampaign, $productAssignment);

        return true;
    }

    /**
     * @param ProductAssignment $productAssignment
     * @param CompanyCampaign $companyCampaign
     * @param AppointmentCancellationReason $cancellationReason
     * @param string $cancellationNote
     * @param int $rescheduledDateTimestamp
     * @return bool
     * @throws Exception
     */
    protected function rescheduleAppointmentForConsumer(
        ProductAssignment $productAssignment,
        CompanyCampaign $companyCampaign,
        AppointmentCancellationReason $cancellationReason,
        string $cancellationNote,
        int $rescheduledDateTimestamp
    ): bool
    {
        $originalAppointment = $productAssignment->consumerProduct->appointment;
        if (!empty($originalAppointment->original_appointment_id)) {
            $this->handleFailedAppointmentUpdate("Appointment is already rescheduled for ProductAssignment $productAssignment->id");

            return false;
        }

        DB::beginTransaction();

        $rescheduledAppointment = $this->createRescheduledAppointmentAndAssociatedConsumerProduct(
            $originalAppointment->id,
            $originalAppointment->appointment_type,
            Carbon::createFromTimestamp($rescheduledDateTimestamp)->setTimezone((string) ($productAssignment->consumerProduct->address->utc + (int) Timezone::isDST())),
            0,
            $originalAppointment->lead_consumer_product_id,
        );

        $newProductAssignment = $this->updateProductAssignmentsForAppointmentReschedule(
            $companyCampaign,
            $productAssignment,
            $rescheduledAppointment->consumerProduct,
            $cancellationReason,
            $cancellationNote,
        );

        if (!$newProductAssignment)
            return false;

        $this->createAppointmentProductCancellation(
            $productAssignment->{ProductAssignment::FIELD_ID},
            $cancellationReason,
            $cancellationNote
        );

        $deliveredCancellation = $this->deliverCancellations($companyCampaign, $productAssignment);

        $newAppointmentDelivery = $this->createNewAppointmentDelivery($companyCampaign, $newProductAssignment);

        DB::commit();

        // delay the new delivery to follow the cancellation
        $attemptDeliveryAt = $deliveredCancellation
            ? now()->addMinute()
            : now()->addMinutes(config('sales.appointments.next_delivery_attempt_buffer_minutes') + 1);

        AttemptAppointmentDeliveryJob::dispatch(
            $newProductAssignment->id,
            $newAppointmentDelivery->id
        )->delay($attemptDeliveryAt);

        return true;
    }

    /**
     * @param CompanyCampaign $companyCampaign
     * @param ProductAssignment $originalProductAssignment
     * @param ConsumerProduct $newConsumerProduct
     * @param AppointmentCancellationReason $cancellationReason
     * @param string $cancellationNote
     * @return ProductAssignment|null
     */
    private function updateProductAssignmentsForAppointmentReschedule(
        CompanyCampaign $companyCampaign,
        ProductAssignment $originalProductAssignment,
        ConsumerProduct $newConsumerProduct,
        AppointmentCancellationReason $cancellationReason,
        string $cancellationNote
    ): ?ProductAssignment
    {
        $newProductAssignment = $originalProductAssignment->replicate($originalProductAssignment->getGuarded());

        $originalProductAssignment->update([
            ProductAssignment::FIELD_CHARGEABLE                  => false,
            ProductAssignment::FIELD_AFFECT_REJECTION_PERCENTAGE => false,
        ]);
        $this->createAppointmentProductCancellation($originalProductAssignment->id, $cancellationReason, $cancellationNote );

        $newProductAssignment->chargeable = true;
        $newProductAssignment->delivered = false;
        $newProductAssignment->save();
        try {
            $this->syncProductAssignmentToLegacy($companyCampaign, $newProductAssignment, false);
            $this->syncProductAssignmentToLegacy($companyCampaign, $originalProductAssignment, true);

            $newProductAssignment->update([
                ProductAssignment::FIELD_CONSUMER_PRODUCT_ID => $newConsumerProduct->id
            ]);

            return $newProductAssignment;
        }
        catch(Throwable $e) {
            $this->handleFailedAppointmentUpdate("Legacy sync error for new ProductAssignment $newProductAssignment->id - " . $e->getMessage());

            return null;
        }
    }

    /**
     * @param CompanyCampaign $companyCampaign
     * @param ProductAssignment $productAssignment
     * @return bool
     * @throws Exception
     */
    private function deliverCancellations(CompanyCampaign $companyCampaign, ProductAssignment $productAssignment): bool
    {
        /** @var AppointmentEmailDeliveryStrategy $emailDeliveryService */
        $emailDeliveryService = app(AppointmentEmailDeliveryStrategy::class);
        /** @var AppointmentSMSDeliveryStrategy $smsDeliveryService */
        $smsDeliveryService = app(AppointmentSMSDeliveryStrategy::class);

        $companyDelivered = $companyCampaign->deliveryModule->contacts
            ->map(function(CompanyCampaignDeliveryModuleContact $contact) use ($emailDeliveryService, $smsDeliveryService, $productAssignment, $companyCampaign) {
                if ($contact->active) {
                    $companyUser = $contact->contact;
                    $emailed = false;
                    $texted = false;
                    if ($contact->sms_active) {
                        $texted = $smsDeliveryService->deliverCancellationToCompanyUser($productAssignment->consumerProduct, $companyCampaign, [
                            BaseSMSDeliveryStrategy::FIELD_PHONE               => $companyUser->cell_phone,
                            BaseEmailDeliveryStrategy::FIELD_COMPANY_USER_NAME => $companyUser->completeName(),
                        ]);
                    }
                    if ($contact->email_active) {
                        $emailed = $emailDeliveryService->deliverCancellationToCompanyUser($productAssignment->consumerProduct, $companyCampaign, [
                            BaseEmailDeliveryStrategy::FIELD_EMAIL => $companyUser->email,
                            BaseEmailDeliveryStrategy::FIELD_COMPANY_USER_NAME => $companyUser->completeName(),
                        ]);
                    }

                    return $emailed || $texted;
                }

                return false;
            })->some(fn($v) => $v);

        $consumerDelivered = array_reduce([
            $emailDeliveryService->deliverCancellationToConsumer($productAssignment->consumerProduct, $companyCampaign),
            $smsDeliveryService->deliveryCancellationToConsumer($productAssignment->consumerProduct, $companyCampaign),
        ], fn($success, $v) =>  $success ?: $v, false);


        if (!$companyDelivered || !$consumerDelivered) {
            $this->handleFailedDelivery($productAssignment, $companyDelivered, $consumerDelivered);

            $reattemptAt = now()->addMinutes(config('sales.appointments.next_delivery_attempt_buffer_minutes'));
            AttemptAppointmentDeliveryJob::dispatch(
                $productAssignment->id,
                null,
                [
                    AttemptAppointmentDeliveryJob::CANCELLATION_DELIVERED_COMPANY  => $companyDelivered,
                    AttemptAppointmentDeliveryJob::CANCELLATION_DELIVERED_CONSUMER => $consumerDelivered
                ]
            )->delay($reattemptAt);

            return false;
        }

        return true;
    }

    /**
     * @param CompanyCampaign $companyCampaign
     * @param ProductAssignment $productAssignment
     * @param ?bool $updateExisting
     * @return void
     */
    private function syncProductAssignmentToLegacy(CompanyCampaign $companyCampaign, ProductAssignment $productAssignment, ?bool $updateExisting = false): void
    {
        /** @var LegacyModule $campaignLegacyModule */
        $campaignLegacyModule = app(LegacyModule::class);

        $leadCampaignId = CompanyCampaignRelation::query()
            ->where(CompanyCampaignRelation::FIELD_COMPANY_CAMPAIGN_ID, $companyCampaign->id)
            ->first()
            ?->id;

        try {
            $updateExisting
                ? $campaignLegacyModule->updateLegacyQuoteCompany($productAssignment)
                : $campaignLegacyModule->createLegacyQuoteCompany($productAssignment, $leadCampaignId);
        }
        catch(Throwable $e) {
            logger()->error("Legacy sync error - failed to create lead for demoted appointment, ProductAssignment $productAssignment->id - " . $e->getMessage());
        }
    }

    /**
     * @param CompanyCampaign $companyCampaign
     * @param ProductAssignment $productAssignment
     * @return ?float
     */
    private function getLeadFloorPrice(CompanyCampaign $companyCampaign, ProductAssignment $productAssignment): ?float
    {
        /** @var FloorPricingRepository $floorPricingRepository */
        $floorPricingRepository = app(FloorPricingRepository::class);

        $leadServiceProductId = $this->getLeadServiceProductId($companyCampaign);
        $qualityTierId = QualityTierEnum::STANDARD->model()->id;

        return $floorPricingRepository->getScopedCountyFloorPriceByZipCode(
            zipCode: $productAssignment->consumerProduct->address->zip_code,
            serviceProductId: $leadServiceProductId,
            propertyTypeId: $productAssignment->consumerProduct->property_type_id,
            qualityTierId: $qualityTierId,
            saleTypeId: $productAssignment->sale_type_id,
        );
    }

    /**
     * @param CompanyCampaign $campaign
     * @return int|null
     */
    private function getLeadServiceProductId(CompanyCampaign $campaign): ?int
    {
        /** @var ServiceProductRepository $repository */
        $repository = app(ServiceProductRepository::class);

        return $repository->getServiceProductByProductAndIndustryService(
            $campaign->service_id,
            ProductEnum::LEAD
        )?->id;
    }

    private function handleFailedDelivery(ProductAssignment $productAssignment, bool $failedCompanyDelivery, bool $failedConsumerDelivery): void
    {
        $failedTargets = array_filter([$failedCompanyDelivery ? 'company' : null, $failedConsumerDelivery ? 'consumer' : null], fn($v) => $v);
        if (!count($failedTargets))
            return;

        $message = "Failed cancellation delivery to " . implode(' and ', $failedTargets) . " for ProductAssignment $productAssignment->id";
        $this->handleFailedAppointmentUpdate($message);
    }

    private function handleFailedAppointmentUpdate(string $message, ?array $payload = []): void
    {
        $prefix = "Appointment Cancellation/Reschedule Error: ";
        logger()->error("$prefix$message", $payload);
    }
}
