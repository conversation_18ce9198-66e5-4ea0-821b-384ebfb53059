<?php

namespace App\Services\Odin\Campaigns;

use App\Exceptions\DebugException;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductCampaign;
use App\Repositories\Odin\Campaigns\OdinCampaignRepository;
use Illuminate\Support\Collection;

class OdinCampaignAvailabilityService
{
    public function __construct(protected OdinCampaignRepository $campaignRepository) {}

    /**
     * Returns whether there are companies available for a specific product.
     *
     * @param ConsumerProduct $product
     * @param array $ignoreCompanyIds
     * @return bool
     */
    public function areCompaniesAvailable(
        ConsumerProduct $product,
        array           $ignoreCompanyIds = []
    ): bool
    {
        return $this->campaignRepository->getAvailableCompaniesForProductByCampaigns($product)
                   ->filter(fn(Company $company) => !in_array($company->id, $ignoreCompanyIds))
                   ->count() > 0;
    }

    /**
     * Returns the companies with available budget for purchasing a consumer product.
     *
     * @param ConsumerProduct $product
     * @return int
     */
    public function companiesWithAvailableBudgetCount(ConsumerProduct $product): int
    {
        return $this->campaignRepository->getAvailableCompaniesForProductByCampaigns($product)
            ->count();
    }

    /**
     * @param ConsumerProduct $product
     * @param array $ignoreCompanyIds
     * @param int|null $overrideSaleTypeId
     * @param Company|null $debugCompany
     * @param ProductCampaign|null $debugProductCampaign
     * @param bool $debug
     * @return Collection
     * @throws DebugException
     */
    public function getAvailableCampaignsForProduct(
        ConsumerProduct $product,
        array $ignoreCompanyIds = [],
        ?int $overrideSaleTypeId = null,
        ?Company $debugCompany = null,
        ?ProductCampaign $debugProductCampaign = null,
        bool $debug = false
    ): Collection
    {
        if($debug
        && $debugCompany) {
            $this->campaignRepository->setDebug($debug, $debugCompany, $debugProductCampaign);
        }

        $filteredCampaigns = $this->campaignRepository->getAvailableCampaignsForDelivery($product, $overrideSaleTypeId)
            ->filter(fn(array $item) => !in_array($item["company_id"], $ignoreCompanyIds));

        $this->campaignRepository->checkDebugResult("Company was ignored", null, $filteredCampaigns);

        return $filteredCampaigns;
    }
}
