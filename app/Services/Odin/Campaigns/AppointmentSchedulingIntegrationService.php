<?php

namespace App\Services\Odin\Campaigns;

use App\ConsumerProcessing\Jobs\AttemptConsumerProjectAllocationJob;
use App\Enums\HttpMethod;
use App\Enums\Odin\QualityTier as QualityTierEnum;
use App\Services\Odin\Appointments\AppointmentCalendarIntegrationService;
use Carbon\Carbon;
use Throwable;

/**
 * Add any CompanyCampaign dependent methods
 * TODO: merge into one service and remove ProductCampaign methods when future campaign migration complete
 */
class AppointmentSchedulingIntegrationService extends AppointmentCalendarIntegrationService
{
    const string REQUEST_APPOINTMENT_DATE = 'appointment_date';
    const string REQUEST_TIMEZONE_OFFSET  = 'timezone_offset';
    const string REQUEST_APPOINTMENT_TYPE = 'appointment_type';
    const string REQUEST_APPROVAL_DATE    = 'approval_date';
    const string REQUEST_SCHEDULE_IDS     = 'schedule_ids';

    const string RESPONSE_AVAILABILITIES  = 'availabilities';

    /**
     * @param array $scheduleIds
     * @param QualityTierEnum $appointmentType
     * @param Carbon $appointmentDateTime
     * @param Carbon $approvalDate
     * @return array
     * @throws Throwable
     */
    public function getAvailabilityForSchedules(array $scheduleIds, QualityTierEnum $appointmentType, Carbon $appointmentDateTime, Carbon $approvalDate): array
    {
        $response = $this->sendRequest(
            HttpMethod::METHOD_GET,
            "integration/schedules-availability",
            [
                self::REQUEST_APPOINTMENT_DATE => $appointmentDateTime->format("Y-m-d H:i:s"),
                self::REQUEST_TIMEZONE_OFFSET  => $appointmentDateTime->format("P"),
                self::REQUEST_APPOINTMENT_TYPE => $appointmentType->value,
                self::REQUEST_APPROVAL_DATE    => $approvalDate->format("Y-m-d H:i:s"),
                self::REQUEST_SCHEDULE_IDS     => $scheduleIds,
            ]
        );

        return $response[self::RESPONSE_AVAILABILITIES] ?? [];
    }
}
