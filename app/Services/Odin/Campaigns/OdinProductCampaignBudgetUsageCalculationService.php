<?php

namespace App\Services\Odin\Campaigns;

use App\DataModels\Odin\LeadCampaignBudgetUsage;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Legacy\LeadSalesType;
use App\Models\Odin\ProductCampaign;
use App\Services\DatabaseHelperService;
use App\Services\Legacy\QuoteCompanyCalculationsService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use JetBrains\PhpStorm\NoReturn;

class OdinProductCampaignBudgetUsageCalculationService
{
    protected Collection $campaignBudgetUsages;

    public function __construct(protected QuoteCompanyCalculationsService $quoteCompanyCalculationsService)
    {
        $this->campaignBudgetUsages = collect();
    }

    /**
     * @param array $campaignIds
     * @return void
     */
    public function calculateProductCampaignBudgetUsage(array $campaignIds): void
    {
        //todo: do in one query
        foreach ($this->getCampaigns($campaignIds) as $campaign) {
            $startTimestamp = $this->quoteCompanyCalculationsService->getTimestampBudgetStart($campaign->last_modified_lead_limit?->timestamp ?? 0);

            $query = LeadCampaign::query()
                ->selectRaw(
                    LeadCampaign::TABLE . ".*, " . LeadSalesType::TABLE . '.' . LeadSalesType::ID . " AS sale_type_id, {$startTimestamp} AS start_time, UNIX_TIMESTAMP() as end_time, " . EloquentCompany::TABLE . '.' . EloquentCompany::NEVER_EXCEED_BUDGET . " AS company_never_exceed_budget,
                          CASE
                            WHEN " . LeadCampaignSalesTypeConfiguration::TABLE . "." . LeadCampaignSalesTypeConfiguration::MAX_DAILY_SPEND . " is not null and " . LeadCampaignSalesTypeConfiguration::TABLE . "." . LeadCampaignSalesTypeConfiguration::MAX_DAILY_SPEND
                    . " > 0 then 'daily_spend'
                            WHEN " . LeadCampaignSalesTypeConfiguration::TABLE . "." . LeadCampaignSalesTypeConfiguration::MAX_DAILY_LEAD . " is not null and " . LeadCampaignSalesTypeConfiguration::TABLE . "." . LeadCampaignSalesTypeConfiguration::MAX_DAILY_LEAD
                    . " > 0 then 'daily_lead'
                            ELSE 'unlimited'
                          END AS budget_type,
                          CASE
                            WHEN " . LeadCampaignSalesTypeConfiguration::TABLE . "." . LeadCampaignSalesTypeConfiguration::MAX_DAILY_SPEND . " is not null and " . LeadCampaignSalesTypeConfiguration::TABLE . "." . LeadCampaignSalesTypeConfiguration::MAX_DAILY_SPEND
                    . " > 0 then " . LeadCampaignSalesTypeConfiguration::TABLE . "." . LeadCampaignSalesTypeConfiguration::MAX_DAILY_SPEND . "
                            WHEN " . LeadCampaignSalesTypeConfiguration::TABLE . "." . LeadCampaignSalesTypeConfiguration::MAX_DAILY_LEAD . " is not null and " . LeadCampaignSalesTypeConfiguration::TABLE . "." . LeadCampaignSalesTypeConfiguration::MAX_DAILY_LEAD
                    . " > 0 then " . LeadCampaignSalesTypeConfiguration::TABLE . "." . LeadCampaignSalesTypeConfiguration::MAX_DAILY_LEAD . "
                            ELSE null
                          END AS budget,
                          SUM(quote_company." . EloquentQuoteCompany::COST . ") AS budget_spend,"
                    . ProductCampaign::TABLE . ".id as product_campaign_id"
                )
                ->join(
                    LeadCampaignSalesTypeConfiguration::TABLE,
                    LeadCampaign::TABLE . '.' . LeadCampaign::ID,
                    '=',
                    LeadCampaignSalesTypeConfiguration::TABLE . '.' . LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID
                )
                ->join(
                    LeadSalesType::TABLE,
                    LeadCampaignSalesTypeConfiguration::TABLE . '.' . LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID,
                    '=',
                    LeadSalesType::TABLE . '.' . LeadSalesType::ID
                )
                ->join(
                    EloquentCompany::TABLE,
                    LeadCampaign::TABLE . '.' . LeadCampaign::COMPANY_ID,
                    '=',
                    EloquentCompany::TABLE . '.' . EloquentCompany::COMPANY_ID
                )
                ->join(
                    DatabaseHelperService::database().'.'.ProductCampaign::TABLE,
                    LeadCampaign::TABLE.'.'.LeadCampaign::ID,
                    '=',
                    DatabaseHelperService::database().'.'.ProductCampaign::TABLE.'.'.ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID
                )
                ->leftJoinSub(
                    $this->getSoldLeadQuery($startTimestamp, Carbon::now()->timestamp),
                    'quote_company',
                    function (JoinClause $joinClause) {
                        $joinClause->on(
                            LeadCampaignSalesTypeConfiguration::TABLE . '.' . LeadCampaignSalesTypeConfiguration::ID,
                            '=',
                            'quote_company.' . EloquentQuoteCompany::LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION_ID
                        );
                    })
                ->where(LeadCampaign::TABLE . '.' . LeadCampaign::ID, $campaign->id)
                ->groupBy([LeadCampaign::ID, 'sale_type_id']);

            $this->campaignBudgetUsages = $this->campaignBudgetUsages->merge($query->get());
        }

        $this->campaignBudgetUsages = $this->getProcessedUsageData($this->campaignBudgetUsages);
    }

    /**
     * @param int $campaignId
     * @param int $salesTypeId
     *
     * @return bool|null
     */
    public function isBudgetUnlimited(int $campaignId, int $salesTypeId): ?bool
    {
        return $this->getBudgetType($campaignId, $salesTypeId) === LeadCampaignBudgetUsage::BUDGET_TYPE_UNLIMITED;
    }

    /**
     * @param int $campaignId
     * @param int $salesTypeId
     *
     * @return bool|null
     */
    public function isBudgetTypeLead(int $campaignId, int $salesTypeId): ?bool
    {
        return $this->getBudgetType($campaignId, $salesTypeId) === LeadCampaignBudgetUsage::BUDGET_TYPE_DAILY_LEAD;
    }

    /**
     * @param int $campaignId
     * @param int $salesTypeId
     *
     * @return LeadCampaignBudgetUsage|null
     */
    public function getBudgetType(int $campaignId, int $salesTypeId): ?string
    {
        return $this->getBudgetUsageDataModelForSaleType($campaignId, $salesTypeId)?->budgetType;
    }

    /**
     * @param int $campaignId
     * @param int $salesTypeId
     *
     * @return float|null
     */
    public function getMaxBudgetUsage(int $campaignId, int $salesTypeId): ?float
    {
        return $this->getBudgetUsageDataModelForSaleType($campaignId, $salesTypeId)?->maxBudgetUsage;
    }

    /**
     * @param int $campaignId
     * @param int $salesTypeId
     *
     * @return float|null
     */
    public function getDailyBudgetForSaleType(int $campaignId, int $salesTypeId): ?float
    {
        return $this->getBudgetUsageDataModelForSaleType($campaignId, $salesTypeId)?->budget;
    }

    /**
     * @param int $campaignId
     * @param int $salesTypeId
     *
     * @return float
     */
    public function getBudgetSpent(int $campaignId, int $salesTypeId): float
    {
        $verifiedSaleTypeIds = [
            LeadSalesType::LEAD_SALE_TYPE_EXCLUSIVE_ID,
            LeadSalesType::LEAD_SALE_TYPE_DUO_ID,
            LeadSalesType::LEAD_SALE_TYPE_TRIO_ID,
            LeadSalesType::LEAD_SALE_TYPE_QUAD_ID
        ];

        $isVerified = in_array($salesTypeId, $verifiedSaleTypeIds);

        if ($isVerified) {
            return $this->campaignBudgetUsages->get($campaignId)
                       ?->filter(fn(LeadCampaignBudgetUsage $budgetUsage) => in_array($budgetUsage->saleTypeId, $verifiedSaleTypeIds))
                       ->sum(fn(LeadCampaignBudgetUsage $budgetUsage) => $budgetUsage->budgetSpend) ?? 0;
        }

        return $this->getBudgetUsageDataModelForSaleType($campaignId, $salesTypeId)?->budgetSpend ?? 0;
    }

    /**
     * @param int $campaignId
     * @param int $salesTypeId
     *
     * @return bool
     */
    public function getNeverExceedBudgetStatus(int $campaignId, int $salesTypeId): bool
    {
        return $this->getBudgetUsageDataModelForSaleType($campaignId, $salesTypeId)?->neverExceedBudgeStatus ?? false;
    }

    /**
     * @param int $campaignId
     * @param $salesTypeId
     *
     * @return int
     */
    public function getCalculationDays(int $campaignId, $salesTypeId): int
    {
        return $this->getBudgetUsageDataModelForSaleType($campaignId, $salesTypeId)?->calculationDays ?? 1;
    }

    /**
     * @param int $campaignId
     * @param int $salesTypeId
     *
     * @return LeadCampaignBudgetUsage|null
     */
    public function getBudgetUsageDataModelForSaleType(int $campaignId, int $salesTypeId): ?LeadCampaignBudgetUsage
    {
        /** @var Collection $budgetUsages */
        $budgetUsages = $this->campaignBudgetUsages->get($campaignId);

        /** @var LeadCampaignBudgetUsage|null */
        return $budgetUsages?->first(fn(LeadCampaignBudgetUsage $usage) => $usage->saleTypeId === $salesTypeId);
    }

    /**
     * @param array $campaignIds
     *
     * @return Collection<LeadCampaign>
     */
    protected function getCampaigns(array $campaignIds): Collection
    {
        return LeadCampaign::query()
            ->select([DB::raw(LeadCampaign::TABLE.'.*'), DB::raw(ProductCampaign::TABLE.'.id as product_campaign_id')])
            ->join(
                DatabaseHelperService::database().'.'.ProductCampaign::TABLE,
                LeadCampaign::TABLE.'.'.LeadCampaign::ID,
                '=',
                DatabaseHelperService::database().'.'.ProductCampaign::TABLE.'.'.ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID
            )->whereIn(DB::raw('product_campaigns.id'), $campaignIds)->get();
    }

    /**
     * @param int $startTime
     * @param int $endTime
     *
     * @return Builder
     */
    protected function getSoldLeadQuery(int $startTime, int $endTime): Builder
    {
        return EloquentQuoteCompany::query()
            ->where(EloquentQuoteCompany::DELIVERED, true)
            ->where(EloquentQuoteCompany::CHARGEABLE, true)
            ->where(EloquentQuoteCompany::CHARGE_STATUS, EloquentQuoteCompany::VALUE_CHARGE_STATUS_INITIAL)
            ->where(EloquentQuoteCompany::TIMESTAMP_DELIVERED, '<=', $endTime)
            ->where(EloquentQuoteCompany::TIMESTAMP_DELIVERED, '>=', $startTime);
    }

    /**
     * @param Collection<LeadCampaign> $campaigns
     *
     * @return Collection
     */
    protected function getProcessedUsageData(Collection $campaigns): Collection
    {
        return $campaigns->map(fn(LeadCampaign $campaign) => $this->getCampaignBudgetUsageDataModel($campaign))
            ->groupBy(fn(LeadCampaignBudgetUsage $budgetUsage) => $budgetUsage->campaignId);
    }

    /**
     * @param LeadCampaign $campaign
     *
     * @return LeadCampaignBudgetUsage
     */
    protected function getCampaignBudgetUsageDataModel(LeadCampaign $campaign): LeadCampaignBudgetUsage
    {
        $dataModel = new LeadCampaignBudgetUsage();

        return $dataModel->setCompanyId($campaign->company_id)
            ->setCampaignId($campaign->product_campaign_id)
            ->setSalesTypeId($campaign->sale_type_id)
            ->setBudgetType($this->getBudgetTypeForSaleTypes($campaign, $campaign->sale_type_id))
            ->setBudget($this->getBudgetForSaleTypes($campaign, $campaign->sale_type_id))
            ->setCalculationDays($campaign->start_time, $campaign->end_time)
            ->setMaxBudgetUsage($campaign->maximum_budget_usage ?? LeadCampaignBudgetUsage::MAX_BUDGET_USAGE)
            ->setNeverExceedBudgetStatus($campaign->company_never_exceed_budget === 1)
            ->setBudgetSpend($campaign->budget_spend !== null ? $campaign->budget_spend : 0);
    }

    /**
     * @param LeadCampaign $campaign
     * @param int $saleTypeId
     *
     * @return string
     */
    protected function getBudgetTypeForSaleTypes(LeadCampaign $campaign, int $saleTypeId): string
    {
        if (in_array($saleTypeId, [
            LeadSalesType::LEAD_SALE_TYPE_EXCLUSIVE_ID,
            LeadSalesType::LEAD_SALE_TYPE_DUO_ID,
            LeadSalesType::LEAD_SALE_TYPE_TRIO_ID,
            LeadSalesType::LEAD_SALE_TYPE_QUAD_ID,
        ])) {

            if ($campaign->max_daily_spend > 0) return LeadCampaignBudgetUsage::BUDGET_TYPE_DAILY_SPEND;
            if ($campaign->max_daily_lead > 0) return LeadCampaignBudgetUsage::BUDGET_TYPE_DAILY_LEAD;

            return LeadCampaignBudgetUsage::BUDGET_TYPE_UNLIMITED;
        }

        return  $campaign->budget_type;
    }

    /**
     * @param LeadCampaign $campaign
     * @param int $saleTypeId
     *
     * @return float|null
     */
    protected function getBudgetForSaleTypes(LeadCampaign $campaign, int $saleTypeId): ?float
    {
        if (in_array($saleTypeId, [
            LeadSalesType::LEAD_SALE_TYPE_EXCLUSIVE_ID,
            LeadSalesType::LEAD_SALE_TYPE_DUO_ID,
            LeadSalesType::LEAD_SALE_TYPE_TRIO_ID,
            LeadSalesType::LEAD_SALE_TYPE_QUAD_ID,
        ])) {

            if ($campaign->max_daily_spend > 0) return $campaign->max_daily_spend;
            if ($campaign->max_daily_lead > 0) return $campaign->max_daily_lead;

            return null;
        }

        return $campaign->budget;
    }
}
