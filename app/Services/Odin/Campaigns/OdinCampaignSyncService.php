<?php

namespace App\Services\Odin\Campaigns;

use App\Models\Odin\ProductCampaign;
use App\Repositories\Odin\ProductCampaignRepository;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use PhpParser\Node\Expr\AssignOp\Mod;

class OdinCampaignSyncService
{
    public function __construct(protected ProductCampaignRepository $productCampaignRepository)
    {

    }

    public function syncCampaignName(string $legacyUuid, string $newCampaignName): void
    {
        $productCampaign = $this->productCampaignRepository->findCampaignByLegacyUuidOrFail($legacyUuid);

        $productCampaign->{ProductCampaign::FIELD_NAME} = $newCampaignName;

        $productCampaign->save();
    }

    /**
     * @param string $legacyUuid
     * @return bool
     */
    public function syncCampaignDeleted(string $legacyUuid): bool
    {
        $productCampaign = $this->productCampaignRepository->findCampaignByLegacyUuidOrFail($legacyUuid);

        $productCampaign->{ProductCampaign::FIELD_STATUS} = false;
        $productCampaign->{ProductCampaign::FIELD_REACTIVATE_AT_TIMESTAMP} = 0;
        $productCampaign->{ProductCampaign::FIELD_DELETED_AT} = Carbon::now('UTC');

        return $productCampaign->save();
    }
}
