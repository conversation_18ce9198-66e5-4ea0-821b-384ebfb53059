<?php

namespace App\Services\Odin\Ruleset\Enums;

enum OperationType:string
{
    case LESS_THAN = "lessThan";
    case LESS_THAN_OR_EQUAL_TO = "lessThanOrEqualTo";
    case BETWEEN = "between";
    case GREATER_THAN = "greaterThan";
    case GREATER_THAN_OR_EQUAL_TO = "greaterThanOrEqualTo";
    case EQUAL_TO = "equalTo";
    case IN = "in";
    case RATE = "rate";

    /**
     * Handles returning a list of all operation types.
     *
     * @return array
     */
    public static function allTypes(): array
    {
        return array_column(self::cases(), 'value');
    }

    public function label(): string
    {
        return match ($this->value) {
            self::LESS_THAN->value                  => "less than",
            self::LESS_THAN_OR_EQUAL_TO->value      => "less than or equal to",
            self::BETWEEN->value                    => "between",
            self::GREATER_THAN->value               => "greater than",
            self::GREATER_THAN_OR_EQUAL_TO->value   => "greater than or equal to",
            self::EQUAL_TO->value                   => "equal to",
            self::IN->value                         => "in",
            self::RATE->value                       => "with each worth",
            default                                 => $this->value
        };
    }

    public function toDropdownOption(): array
    {
        return [
            'id'    => $this->value,
            'name'  => $this->label(),
        ];
    }

    public static function allToDropdownOptions(): array
    {
        return array_map(function ($val){
            return OperationType::from($val)->toDropdownOption();
        }, self::allTypes());
    }
}

