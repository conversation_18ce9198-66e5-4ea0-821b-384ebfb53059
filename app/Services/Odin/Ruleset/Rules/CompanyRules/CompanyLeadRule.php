<?php

namespace App\Services\Odin\Ruleset\Rules\CompanyRules;

use App\Builders\Odin\ProductAssignmentBuilder;
use App\Enums\Operator;
use App\Models\Odin\Company;
use App\Models\Odin\ProductAssignment;
use App\Services\Odin\Ruleset\Rules\Rule;
use App\Services\Odin\Ruleset\Enums\ConditionType;
use App\Services\Odin\Ruleset\Enums\OperationType;
use App\Services\Odin\Ruleset\Interfaces\RulesetFilterRuleInterface;
use App\Services\Odin\Ruleset\Interfaces\RulesetRankingRuleInterface;
use App\Services\Odin\Ruleset\Traits\RulesetFilterRuleTrait;
use App\Services\Odin\Ruleset\Traits\RulesetRankingRuleTrait;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Database\Eloquent\Builder;

class CompanyLeadRule extends Rule implements RulesetRankingRuleInterface, RulesetFilterRuleInterface
{
    use RulesetRankingRuleTrait, RulesetFilterRuleTrait;

    const VALUE_VARIABLE = 'value';

    protected Carbon $fromDate;

    public static string $id = 'leads';
    protected string $label = 'Products';
    protected string $description = 'Products purchased by monetary value in the last 30 days';
    protected array $availableOperationTypes = [
        OperationType::LESS_THAN,
        OperationType::LESS_THAN_OR_EQUAL_TO,
        OperationType::BETWEEN,
        OperationType::GREATER_THAN,
        OperationType::GREATER_THAN_OR_EQUAL_TO,
        OperationType::EQUAL_TO
    ];
    protected array $availableVariables = [
        self::VALUE_VARIABLE => "Amount",
    ];
    protected string $variablePrefix = '$';


    public function __construct()
    {
        // TODO - Make it customisable
        $this->fromDate = now()->subDays(30);
        parent::__construct();
    }


    public function getReference($entry): array
    {
        $totalLeadsBought = $this->getTotalLeadsBought($entry);

        return [
            self::VALUE_VARIABLE => $totalLeadsBought
        ];
    }

    private function getTotalLeadsBought($entry)
    {
        return ProductAssignmentBuilder::query()
            ->forCompanyId($entry->{Company::FIELD_ID})
            ->forFromDate($this->fromDate)
            ->get()
            ->sum(ProductAssignment::FIELD_COST);
    }


    /**
     * Add all the relations, clauses, and conditions to a query instance from the rule's 'rule_data'.
     * todo: update this method to account for leads purchased with in the last month
     *
     * @throws Exception
     */
    public function getQuery(Builder $query): Builder
    {
        foreach ($this->data->conditions as $condition) {
            if ($this->getConditionType($condition) === ConditionType::CHECK->value) {
                foreach ($condition->operations as $operation) {

                    $operationRef = $operation->reference;
                    if (!$operationRef) continue;

                    switch ($operation->type->value) {
                        case OperationType::BETWEEN->value:
                            $greaterThanOperator = Operator::sqlOperator(Operator::GREATER_THAN->value);
                            $lessThanOperator = Operator::sqlOperator(Operator::LESS_THAN->value);

                            $query->where(function (\Illuminate\Database\Query\Builder $query){
                                $subQuery = ProductAssignmentBuilder::query()
                                    ->forCompaniesColumnId()
                                    ->forFromDate($this->fromDate)
                                    ->getQuery();

                                $query->fromSub($subQuery->toSql(),'sub')
                                    ->selectRaw('SUM(' . 'sub.'.ProductAssignment::FIELD_COST . ')')
                                    ->addBinding($subQuery->getBindings());
                            }, $greaterThanOperator, +$operationRef['start']);

                            $query->where(function (\Illuminate\Database\Query\Builder $query){
                                $subQuery = ProductAssignmentBuilder::query()
                                    ->forCompaniesColumnId()
                                    ->forFromDate($this->fromDate)
                                    ->getQuery();

                                $query->fromSub($subQuery->toSql(),'sub')
                                    ->selectRaw('SUM(' . 'sub.'.ProductAssignment::FIELD_COST . ')')
                                    ->addBinding($subQuery->getBindings());
                            }, $lessThanOperator, +$operationRef['end']);
                            break;
                        default:
                            $query->where(function (\Illuminate\Database\Query\Builder $query){
                                $subQuery = ProductAssignmentBuilder::query()
                                    ->forCompaniesColumnId()
                                    ->forFromDate($this->fromDate)
                                    ->getQuery();

                                $query->fromSub($subQuery->toSql(),'sub')
                                    ->selectRaw('SUM(' . 'sub.'.ProductAssignment::FIELD_COST . ')')
                                    ->addBinding($subQuery->getBindings());
                            }, $operation->getSqlOperator(), +$operationRef['start']);
                    }

                }

            }
        }

        return $query;
    }
}
