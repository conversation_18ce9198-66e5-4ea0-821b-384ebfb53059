<?php

namespace App\Services\Odin\Ruleset\Rules\CompanyRules;

use App\Enums\Odin\StateAbbreviation;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Services\Odin\Ruleset\Rules\Rule;
use App\Services\Odin\Ruleset\Enums\ConditionType;
use App\Services\Odin\Ruleset\Enums\OperationType;
use App\Services\Odin\Ruleset\Interfaces\RulesetFilterRuleInterface;
use App\Services\Odin\Ruleset\Interfaces\RulesetRankingRuleInterface;
use App\Services\Odin\Ruleset\Operations\InOperation;
use App\Services\Odin\Ruleset\Traits\RulesetFilterRuleTrait;
use App\Services\Odin\Ruleset\Traits\RulesetRankingRuleTrait;
use Illuminate\Contracts\Database\Eloquent\Builder;

class CompanyOfficeLocationsRule extends Rule implements RulesetRankingRuleInterface, RulesetFilterRuleInterface
{
    use RulesetRankingRuleTrait, RulesetFilterRuleTrait;

    public static string $id = 'office_locations';
    protected string $comparisonType = 'entry';
    protected string $label = 'Office Locations';
    protected string $description = 'Office locations by state';
    protected array $availableOperationTypes = [
        OperationType::IN
    ];
    protected array $availableVariables = [
        self::VALUE_VARIABLE => "Location",
    ];
    const VALUE_VARIABLE = 'value';

    public function getReference($entry): array
    {
        return [
            self::VALUE_VARIABLE => $this->getCompanyOfficeLocations($entry)
        ];
    }

    private function getCompanyOfficeLocations(Company $company): array
    {
        return $company->locations()
            ->with(CompanyLocation::RELATION_ADDRESS)
            ->get()
            ->pluck('address.state')
            ->toArray();
    }

    public function getQuery(Builder $query): Builder
    {
        $query->join(CompanyLocation::TABLE, CompanyLocation::TABLE . '.' . CompanyLocation::FIELD_COMPANY_ID, '=', Company::TABLE . '.' . Company::FIELD_ID)
            ->join(Address::TABLE, Address::TABLE . '.' . Address::FIELD_ID, '=', CompanyLocation::TABLE . '.' . CompanyLocation::FIELD_ADDRESS_ID);

        foreach ($this->data->conditions as $condition) {
            if ($this->getConditionType($condition) === ConditionType::CHECK->value) {
                foreach ($condition->operations as $operation) {

                    $operationRef = $operation->reference;
                    if (!$operationRef) continue;

                    if ($operation->type->value === InOperation::TYPE->value) {
                        $query->whereIn(Address::TABLE . '.' . Address::FIELD_STATE, [$operationRef['start']]);
                    }
                }
            }
        }

        return $query;
    }

    public function getConditionOption(): ?array
    {
        return [
            "items" => StateAbbreviation::asSelectArray()
        ];
    }
}
