<?php

namespace App\Services\Odin\Ruleset\Rules\CompanyRules;

use App\Enums\Odin\Country;
use App\Models\Odin\Company;
use App\Services\Odin\Ruleset\Rules\Rule;
use App\Services\Odin\Ruleset\Enums\OperationType;
use App\Services\Odin\Ruleset\Interfaces\RulesetFilterRuleInterface;
use App\Services\Odin\Ruleset\Interfaces\RulesetRankingRuleInterface;
use App\Services\Odin\Ruleset\Traits\RulesetFilterRuleTrait;
use App\Services\Odin\Ruleset\Traits\RulesetRankingRuleTrait;
use App\Services\Odin\SimilarWeb\SimilarWebQueryParams;
use App\Services\Odin\SimilarWeb\SimilarWebService;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Contracts\Database\Eloquent\Builder;

class CompanyAdwordsRule extends Rule implements RulesetRankingRuleInterface, RulesetFilterRuleInterface
{
    use RulesetRankingRuleTrait, RulesetFilterRuleTrait;

    const VALUE_VARIABLE = 'value';

    public static string $id = 'adwords';
    protected string $label = 'AdWords';
    protected string $description = 'Whether company is an AdWords customer or not';
    protected array $availableOperationTypes = [OperationType::EQUAL_TO];

    protected array $availableVariables =  [
        self::VALUE_VARIABLE => "Result",
    ];
    protected array $conditionOption = [
        "items" => [
            [
                'name' => 'Yes',
                'id' => 1,
            ],
            [
                'name' => 'No',
                'id' => 0,
            ]
        ]
    ];

    public function __construct(private readonly SimilarWebService $similarWebService)
    {
        parent::__construct();
    }

    /**
     * @param Company $entry
     * @throws GuzzleException
     */
    public function getReference($entry): array
    {
        if (empty($entry->{Company::FIELD_WEBSITE})) return [
            self::VALUE_VARIABLE => 0
        ];

        $isAdwordsCustomer = $this->checkIfCompanyIsAdwordsCustomer($entry->{Company::FIELD_WEBSITE});

        return [
            self::VALUE_VARIABLE => $isAdwordsCustomer
        ];
    }

    /**
     * @throws GuzzleException
     */
    private function checkIfCompanyIsAdwordsCustomer(string $companyWebsite): int
    {
        $companyDomain = getWebsiteDomain($companyWebsite);

        $queryParams = new SimilarWebQueryParams(Country::UNITED_STATES->value);

        $keywords = $this->similarWebService->getKeywordsFromDomain($companyDomain, $queryParams);

        return $keywords && count($keywords['search']) > 0 ? 1 : 0;
    }

    /**
     * TODO: implement a simple method of checking if a company uses Adwords using DB queries. Recommend a new key on CompanyData::PAYLOAD field.
     *
     * @param Builder $query
     * @return Builder
     */
    public function getQuery(Builder $query): Builder
    {
        return $query;
    }
}
