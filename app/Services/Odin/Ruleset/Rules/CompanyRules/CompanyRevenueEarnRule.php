<?php

namespace App\Services\Odin\Ruleset\Rules\CompanyRules;

use App\Models\Legacy\EloquentInvoice;
use App\Models\Legacy\EloquentInvoiceItem;
use App\Models\Odin\Company;
use App\Repositories\Odin\RevenueRepository;
use App\Services\DatabaseHelperService;
use App\Services\Odin\Ruleset\Rules\Rule;
use App\Services\Odin\Ruleset\Enums\ConditionType;
use App\Services\Odin\Ruleset\Enums\OperationType;
use App\Services\Odin\Ruleset\Interfaces\RulesetFilterRuleInterface;
use App\Services\Odin\Ruleset\Interfaces\RulesetRankingRuleInterface;
use App\Services\Odin\Ruleset\Traits\RulesetFilterRuleTrait;
use App\Services\Odin\Ruleset\Traits\RulesetRankingRuleTrait;
use Illuminate\Contracts\Database\Eloquent\Builder;

class CompanyRevenueEarnRule extends Rule implements RulesetRankingRuleInterface, RulesetFilterRuleInterface
{
    use RulesetRankingRuleTrait, RulesetFilterRuleTrait;

    public static string $id = 'revenue_earn';
    protected string $label = 'Company Revenue';
    protected string $description = 'Monthly recurring revenue (in thousands)';
    protected array $availableOperationTypes = [
        OperationType::LESS_THAN,
        OperationType::LESS_THAN_OR_EQUAL_TO,
        OperationType::BETWEEN,
        OperationType::GREATER_THAN,
        OperationType::GREATER_THAN_OR_EQUAL_TO,
        OperationType::EQUAL_TO,
    ];
    protected array $availableVariables = [
        self::VALUE_VARIABLE => "Money",
    ];
    protected string $variablePrefix = '$';
    protected string $variableSuffix = 'K';

    const VALUE_VARIABLE = 'value';

    public function __construct(private readonly RevenueRepository $revenueRepository)
    {
        parent::__construct();
    }

    /**
     * @param Company $entry
     * @return array
     */
    public function getReference($entry): array
    {
        return [
            self::VALUE_VARIABLE => $this->revenueRepository->getRevenueOfAllTime($entry)
        ];
    }


    /**
     * TODO: implement a simple method of checking if a company uses Adwords using DB queries. Recommend a new key on CompanyData::PAYLOAD field.
     *
     * @param Builder $query
     * @return Builder
     * @throws \Exception
     */
    public function getQuery(Builder $query): Builder
    {
        foreach ($this->data->conditions as $condition) {
            if ($this->getConditionType($condition) === ConditionType::CHECK->value) {
                foreach ($condition->operations as $operation) {

                    $operationRef = $operation->reference;
                    if (!$operationRef) continue;

                    switch ($operation->type->value) {
                        case OperationType::BETWEEN->value:
                            $query->where(function ($query) {
                                $query
                                    ->selectRaw('SUM((' . DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::QUANTITY . ' * ' . DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::ITEM_EX_TAX_PRICE . ') + ' . DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::TOTAL_TAX_AMOUNT . ') AS total')
                                    ->from(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoice::TABLE)
                                    ->join(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoiceItem::TABLE, EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::INVOICE_ID, '=', DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoice::TABLE . '.' . EloquentInvoice::INVOICE_ID)
                                    ->whereRaw(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoice::TABLE . '.' . EloquentInvoice::COMPANY_ID . ' = ' . Company::TABLE . '.' . Company::FIELD_ID)
                                    ->where(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoice::TABLE . '.' . EloquentInvoice::STATUS, EloquentInvoice::VALUE_STATUS_PAID);
                            }, '>=', +$operationRef['start']);

                            $query->where(function ($query) {
                                $query
                                    ->selectRaw('SUM((' . DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::QUANTITY . ' * ' . DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::ITEM_EX_TAX_PRICE . ') + ' . DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::TOTAL_TAX_AMOUNT . ') AS total')
                                    ->from(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoice::TABLE)
                                    ->join(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoiceItem::TABLE, EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::INVOICE_ID, '=', DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoice::TABLE . '.' . EloquentInvoice::INVOICE_ID)
                                    ->whereRaw(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoice::TABLE . '.' . EloquentInvoice::COMPANY_ID . ' = ' . Company::TABLE . '.' . Company::FIELD_ID)
                                    ->where(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoice::TABLE . '.' . EloquentInvoice::STATUS, EloquentInvoice::VALUE_STATUS_PAID);
                            }, '<=', +$operationRef['end']);
                            break;
                        default:
                            $query->where(function ($query) {
                                $query
                                    ->selectRaw('SUM((' . DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::QUANTITY . ' * ' . DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::ITEM_EX_TAX_PRICE . ') + ' . DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::TOTAL_TAX_AMOUNT . ') AS total')
                                    ->from(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoice::TABLE)
                                    ->join(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoiceItem::TABLE, EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::INVOICE_ID, '=', DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoice::TABLE . '.' . EloquentInvoice::INVOICE_ID)
                                    ->whereRaw(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoice::TABLE . '.' . EloquentInvoice::COMPANY_ID . ' = ' . Company::TABLE . '.' . Company::FIELD_ID)
                                    ->where(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoice::TABLE . '.' . EloquentInvoice::STATUS, EloquentInvoice::VALUE_STATUS_PAID);
                            }, $operation->getSqlOperator(), +$operationRef['start']);
                            break;
                    }
                }
            }
        }

        return $query;
    }
}
