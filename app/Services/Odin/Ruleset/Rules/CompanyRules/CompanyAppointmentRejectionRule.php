<?php

namespace App\Services\Odin\Ruleset\Rules\CompanyRules;

use App\Enums\Odin\Product as ProductEnum;
use App\Models\ComputedRejectionStatistic;
use App\Models\Odin\Company;
use App\Models\Odin\Product;
use App\Services\Odin\Ruleset\Rules\Rule;
use App\Services\Odin\Ruleset\Enums\ConditionType;
use App\Services\Odin\Ruleset\Enums\OperationType;
use App\Services\Odin\Ruleset\Interfaces\RulesetFilterRuleInterface;
use App\Services\Odin\Ruleset\Interfaces\RulesetRankingRuleInterface;
use App\Services\Odin\Ruleset\Traits\RulesetFilterRuleTrait;
use App\Services\Odin\Ruleset\Traits\RulesetRankingRuleTrait;
use Illuminate\Contracts\Database\Eloquent\Builder;

class CompanyAppointmentRejectionRule extends Rule implements RulesetRankingRuleInterface, RulesetFilterRuleInterface
{
    use RulesetRankingRuleTrait, RulesetFilterRuleTrait;

    public static string $id = 'appointment_rejection';

    protected string $label = 'Appointment Rejection';
    protected string $description = 'Aggregate appointment rejections';
    protected array $availableOperationTypes = [
        OperationType::LESS_THAN,
        OperationType::LESS_THAN_OR_EQUAL_TO,
        OperationType::BETWEEN,
        OperationType::GREATER_THAN,
        OperationType::GREATER_THAN_OR_EQUAL_TO,
        OperationType::EQUAL_TO,
    ];

    protected array $availableVariables = [
        self::VALUE_VARIABLE => "Percentage",
    ];
    protected string $variableSuffix = '%';

    const VALUE_VARIABLE = 'value';

    /**
     * @param Company $entry
     */
    public function getReference($entry): array
    {
        $percentage = $this->getPercentage($entry);

        return [
            self::VALUE_VARIABLE => $percentage
        ];
    }

    function getPercentage(Company $entry): float
    {
        $rejection = ComputedRejectionStatistic::query()
            ->where(ComputedRejectionStatistic::FIELD_COMPANY_ID, $entry->{Company::FIELD_ID})
            ->whereHas(ComputedRejectionStatistic::RELATION_PRODUCT, function ($has) {
                $has
                    ->where(Product::FIELD_NAME, ProductEnum::APPOINTMENT);
            })
            ->first();

        return $rejection ? $rejection->{ComputedRejectionStatistic::ACTIVE_REJECTION_FIELD_IMPACTING_ELIGIBILITY} : 0;
    }

    function getQuery(Builder $query): Builder
    {
        $computedRejectionStatisticJoinName = 'crs' . '_' . $this::$id;
        $productJoinName = 'p' . '_' . $this::$id;

        $query->join(ComputedRejectionStatistic::TABLE . ' as ' . $computedRejectionStatisticJoinName, $computedRejectionStatisticJoinName . '.' . ComputedRejectionStatistic::FIELD_COMPANY_ID, '=', Company::TABLE . '.' . Company::FIELD_ID)
            ->join(Product::TABLE . ' as ' . $productJoinName, $productJoinName . '.' . Product::FIELD_ID, '=', $computedRejectionStatisticJoinName . '.' . ComputedRejectionStatistic::FIELD_PRODUCT_ID)
            ->where($productJoinName . '.' . Product::FIELD_NAME, ProductEnum::APPOINTMENT);

        // TODO - DRY and remove the join from the loop
        foreach ($this->data->conditions as $condition) {
            if ($this->getConditionType($condition) === ConditionType::CHECK->value) {
                foreach ($condition->operations as $operation) {

                    $operationRef = $operation->reference;
                    if (!$operationRef) continue;

                    switch ($operation->type->value) {
                        case OperationType::BETWEEN->value:
                            $query->whereBetween($computedRejectionStatisticJoinName . '.' . ComputedRejectionStatistic::ACTIVE_REJECTION_FIELD_IMPACTING_ELIGIBILITY, [+$operationRef['start'], +$operationRef['end']]);
                            break;
                        default:
                            $query->where($computedRejectionStatisticJoinName . '.' . ComputedRejectionStatistic::ACTIVE_REJECTION_FIELD_IMPACTING_ELIGIBILITY, $operation->getSqlOperator(), +$operationRef['start']);
                    }
                }
            }
        }

        return $query;
    }

}
