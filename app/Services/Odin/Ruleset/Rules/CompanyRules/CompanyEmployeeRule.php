<?php

namespace App\Services\Odin\Ruleset\Rules\CompanyRules;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyData;
use App\Services\Odin\Ruleset\Rules\Rule;
use App\Services\Odin\Ruleset\Enums\ConditionType;
use App\Services\Odin\Ruleset\Enums\OperationType;
use App\Services\Odin\Ruleset\Interfaces\RulesetFilterRuleInterface;
use App\Services\Odin\Ruleset\Interfaces\RulesetRankingRuleInterface;
use App\Services\Odin\Ruleset\Traits\RulesetFilterRuleTrait;
use App\Services\Odin\Ruleset\Traits\RulesetRankingRuleTrait;
use Exception;
use Illuminate\Contracts\Database\Eloquent\Builder;
use stdClass;

class CompanyEmployeeRule extends Rule implements RulesetRankingRuleInterface, RulesetFilterRuleInterface
{
    use RulesetRankingRuleTrait, RulesetFilterRuleTrait;

    public static string $id = 'employees';

    protected string $label = 'Employee count';
    protected string $description = 'Employee count data';
    protected array $availableOperationTypes = [
        OperationType::LESS_THAN,
        OperationType::LESS_THAN_OR_EQUAL_TO,
        OperationType::BETWEEN,
        OperationType::GREATER_THAN,
        OperationType::GREATER_THAN_OR_EQUAL_TO,
        OperationType::EQUAL_TO,
    ];
    protected array $availableVariables = [
        self::VALUE_VARIABLE => "Employees",
    ];
    const VALUE_VARIABLE = 'value';

    /**
     * @param stdClass $entry
     * @return array|int[]
     */
    public function getReference($entry): array
    {
        $countEmployees = 0;

        $companyData = CompanyData::query()->where(CompanyData::FIELD_COMPANY_ID, $entry->id)->first();

        if (!$companyData) return [
            self::VALUE_VARIABLE => $countEmployees
        ];

        $countEmployees = $companyData->{CompanyData::FIELD_PAYLOAD}['employee_count'] ?? 0;

        return [
          self::VALUE_VARIABLE => $countEmployees
        ];
    }

    /**
     * Add all the relations, clauses, and conditions to a query instance from the rule's 'rule_data'.
     *
     * @throws Exception
     */
    public function getQuery(Builder $query): Builder
    {
        $companyDataJoinName = 'cd' . '_' . self::$id;
        $query->join(CompanyData::TABLE . ' as ' . $companyDataJoinName, $companyDataJoinName . '.' . CompanyData::FIELD_COMPANY_ID, '=', Company::TABLE . '.' . Company::FIELD_ID);

        foreach ($this->data->conditions as $condition) {
            if ($this->getConditionType($condition) === ConditionType::CHECK->value) {
                foreach ($condition->operations as $operation) {

                    $operationRef = $operation->reference;
                    if (!$operationRef) continue;

                    switch ($operation->type->value) {
                        case OperationType::BETWEEN->value:
                            $query->whereBetween($companyDataJoinName . '.' . CompanyData::FIELD_PAYLOAD.'->employee_count', [+$operationRef['start'], +$operationRef['end']]);
                            break;
                        default:
                            $query->where($companyDataJoinName . '.' . CompanyData::FIELD_PAYLOAD.'->employee_count', $operation->getSqlOperator(), +$operationRef['start']);
                    }
                }
            }
        }

        return $query;
    }
}
