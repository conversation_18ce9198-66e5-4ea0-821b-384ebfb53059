<?php

namespace App\Services\Odin\Ruleset\Rules\CompanyRules;

use App\Enums\Operator;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Services\Odin\Ruleset\Rules\Rule;
use App\Services\Odin\Ruleset\Enums\ConditionType;
use App\Services\Odin\Ruleset\Enums\OperationType;
use App\Services\Odin\Ruleset\Interfaces\RulesetFilterRuleInterface;
use App\Services\Odin\Ruleset\Interfaces\RulesetRankingRuleInterface;
use App\Services\Odin\Ruleset\Traits\RulesetFilterRuleTrait;
use App\Services\Odin\Ruleset\Traits\RulesetRankingRuleTrait;
use Illuminate\Contracts\Database\Eloquent\Builder;

class CompanyLocationRule extends Rule implements RulesetRankingRuleInterface, RulesetFilterRuleInterface
{
    use RulesetRankingRuleTrait, RulesetFilterRuleTrait;

    const VALUE_VARIABLE = 'value';

    public static string $id = 'locations';
    protected string $label = 'Locations';
    protected string $description = 'Total count of company locations';
    protected array $availableOperationTypes = [
        OperationType::LESS_THAN,
        OperationType::LESS_THAN_OR_EQUAL_TO,
        OperationType::BETWEEN,
        OperationType::GREATER_THAN,
        OperationType::GREATER_THAN_OR_EQUAL_TO,
        OperationType::EQUAL_TO,
        OperationType::RATE,
    ];

    protected array $availableVariables = [
        self::VALUE_VARIABLE => "Location",
    ];


    function getQuery(Builder $query): Builder
    {
        foreach ($this->data->conditions as $condition) {
            if ($this->getConditionType($condition) === ConditionType::CHECK->value) {
                foreach ($condition->operations as $operation) {

                    $operationRef = $operation->reference;
                    if (!$operationRef) continue;

                    switch ($operation->type->value) {
                        case OperationType::BETWEEN->value:
                            $greaterThanOperator = Operator::sqlOperator(Operator::GREATER_THAN->value);
                            $lessThanOperator = Operator::sqlOperator(Operator::LESS_THAN->value);

                            $query
                                ->has(Company::RELATION_LOCATIONS, $greaterThanOperator, +$operationRef['start'])
                                ->has(Company::RELATION_LOCATIONS, $lessThanOperator, +$operationRef['end']);

                            break;
                        default:
                            $query->has(Company::RELATION_LOCATIONS,  $operation->getSqlOperator(), +$operationRef['start']);
                    }

                }

            }
        }

        return $query;
    }

    /**
     * @param Company $entry
     * @return array
     */
    function getReference($entry): array
    {
        $countLocations = CompanyLocation::query()->where(CompanyLocation::FIELD_COMPANY_ID, $entry->id)->count();

        return [
            self::VALUE_VARIABLE => $countLocations
        ];
    }
}
