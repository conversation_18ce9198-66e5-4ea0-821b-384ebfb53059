<?php

namespace App\Services\Odin\Ruleset\Rules;

use App\Services\Odin\Ruleset\Interfaces\RendererInterface;

class Condition implements RendererInterface
{
    public function __construct(public int $points = 0, public array $operations = [])
    {

    }

    public function addOperation(Operation $operation): void
    {
        $this->operations[] = $operation;
    }

    public function toArray(): array
    {
        $operationsTemplate = array_map(fn ($operation) => $operation->toArray(), $this->operations);

        return [
            "points" => $this->points,
            "operations" => $operationsTemplate
        ];
    }
}
