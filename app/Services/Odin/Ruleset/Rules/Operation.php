<?php

namespace App\Services\Odin\Ruleset\Rules;

use App\Enums\Logical;
use App\Enums\Operator;
use App\Services\Odin\Ruleset\Enums\OperationType;
use App\Services\Odin\Ruleset\Interfaces\RendererInterface;

class Operation implements RendererInterface
{
    public function __construct(public OperationType $type, public array $reference, public string $comparisonFieldName = 'value', public Logical $logicalOperator = Logical::AND)
    {

    }

    public function getSqlOperator(): string
    {
        return Operator::sqlOperator($this->type->value);
    }

    public function toArray(): array
    {
        return [
            "logical_operator" => $this->logicalOperator->value,
            "type" => $this->type,
            "comparison_field_name" => $this->comparisonFieldName,
            "reference" => $this->reference,
        ];
    }
}
