<?php

namespace App\Services\Odin\Ruleset\Rules;

use App\Services\Odin\Ruleset\Interfaces\RendererInterface;

class RuleData implements RendererInterface
{
    public function __construct(public array $conditions = [], public int $maxPoints = 0)
    {

    }

    public function addConditions(Condition $condition): void
    {
        $this->conditions[] = $condition;
    }

    public function toArray(): array
    {
        $conditionsTemplate = array_map(fn ($condition) => $condition->toArray(), $this->conditions);

        return [
            "max_points" => $this->maxPoints,
            "conditions" => $conditionsTemplate
        ];
    }
}
