<?php

namespace App\Services\Odin\Ruleset\Rules;

use App\Services\Odin\Ruleset\Interfaces\RendererInterface;
use App\Services\Odin\Ruleset\Interfaces\RulesetRuleInterface;

abstract class Rule implements RulesetRuleInterface, RendererInterface
{
    static string $id;

    protected string $label;
    protected string $description;
    protected array $availableOperationTypes;

    protected array $availableVariables;
    protected string $variablePrefix;
    protected string $variableSuffix;
    protected string $comparisonType;
    protected array $conditionOption;

    public RuleData $data;
    public bool $isActive = false;

    public function __construct()
    {
        $this->data = new RuleData();
    }

    public function getId(): string
    {
        return $this::$id ?? $this::class;
    }
    public function getLabel(): string
    {
        return $this->label;
    }
    public function getDescription(): string
    {
        return $this->description;
    }
    public function getAvailableOperationTypes(): array
    {
        return $this->availableOperationTypes ?? [];
    }
    public function getAvailableVariables(): array
    {
        return $this->availableVariables ?? [];
    }
    public function getVariablePrefix(): ?string
    {
        return $this->variablePrefix ?? null;
    }
    public function getVariableSuffix(): ?string
    {
        return $this->variableSuffix ?? null;
    }
    public function getComparisonType(): ?string
    {
        return $this->comparisonType ?? 'plain';
    }
    public function getConditionOption(): ?array
    {
        return $this->conditionOption ?? [];
    }

    public function setData(RuleData $data): void
    {
        $this->data = $data;
    }

    public function setIsActive(bool $isActive): void
    {
        $this->isActive = $isActive;
    }

    public function toArray(): array
    {
        $availableOperationTypesTemplate = array_map(fn ($operation) => $operation->toDropdownOption(), $this->getAvailableOperationTypes());

        return [
            "rule_type" => $this->getId(),
            "is_active" => $this->isActive,
            "data" => [
                "label" => $this->getLabel(),
                "description" => $this->getDescription(),
                "rule_reference" => [
                    "available_operation_types" => $availableOperationTypesTemplate,
                    "available_variables" => $this->getAvailableVariables(),
                    "variable_prefix" => $this->getVariablePrefix(),
                    "variable_suffix" => $this->getVariableSuffix(),
                    "comparison_type" => $this->getComparisonType() ?? 'entry', // TODO - Enum
                    "condition_option" => $this->getConditionOption(),
                ],
                "rule_data" => $this->data->toArray(),
            ],
        ];
    }
}
