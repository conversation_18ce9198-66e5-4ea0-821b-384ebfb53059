<?php

namespace App\Services\Odin\Ruleset\Interfaces;

interface RulesetRuleInterface
{
    public function getId(): string;

    public function getLabel(): string;

    public function getDescription(): string;

    public function getAvailableOperationTypes(): array;

    public function getAvailableVariables(): array;

    public function getVariablePrefix(): string|null;

    public function getVariableSuffix(): string|null;

    public function getComparisonType(): string|null; // entry or plain

    public function getConditionOption(): array|null;
}
