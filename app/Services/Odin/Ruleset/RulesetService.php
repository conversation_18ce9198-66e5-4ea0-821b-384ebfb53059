<?php

namespace App\Services\Odin\Ruleset;

use App\Models\Ruleset;
use App\Services\Odin\Ruleset\Interfaces\RulesetRankingRuleInterface;
use App\Services\Odin\Ruleset\Rules\Rule;
use Exception;
use Illuminate\Contracts\Database\Eloquent\Builder;

/**
 * Handle the point calculations and query building for a collection of Rules within a Ruleset.
 *
 * @class RulesetService
 */
class RulesetService
{
    const FIELD_TOTAL_SCORE_IN_POINTS       = "total_score_in_points";
    const FIELD_TOTAL_POINTS_AVAILABLE      = "total_points_available";
    const FIELD_TOTAL_SCORE_IN_PERCENTAGE   = "total_score_in_percentage";
    const FIELD_RULE_SCORES                 = "rule_scores";

    /**
     * @param Ruleset $ruleset
     * @param string[] $rules
     */
    public function __construct(protected Ruleset $ruleset, protected array $rules)
    {
    }

    /**
     * @throws Exception
     */
    public function validate(): void
    {
        $countRules = count($this->rules);
        if ($countRules === 0) {
            throw new Exception("No rules are registered");
        }
    }

    /**
     * Adds all conditions from rules of the class's Ruleset instance.
     * Creates a new query if one is not provided as a parameter.
     *
     * @param Builder|null $query
     * @return Builder
     */
    public function addRuleQueryConditions(?Builder $query = null): Builder
    {
        /** @var Array<Rule> $activeRules */
        $activeRules = $this->getOnlyActiveRules();

        foreach ($activeRules as $rule) {
            $query = $rule->getQuery($query);
        }

        return $query;
    }

    private function getOnlyActiveRules(): array
    {
        return array_filter($this->rules, fn($rule) => $rule->isActive);
    }

    /**
     * @param $entry
     * @return array
     * @throws Exception
     */
    public function calculateScore($entry): array
    {
        $score = [
            self::FIELD_TOTAL_SCORE_IN_POINTS      => 0,
            self::FIELD_TOTAL_POINTS_AVAILABLE     => 0,
            self::FIELD_TOTAL_SCORE_IN_PERCENTAGE  => 0,
            self::FIELD_RULE_SCORES                => [],
        ];

        /** @var RulesetRankingRuleInterface $rule */
        foreach ($this->getOnlyActiveRules() as $rule) {
            $score[self::FIELD_TOTAL_POINTS_AVAILABLE] += $rule->data->maxPoints;

            // TODO - Save rule error inside the result ?
            try {
                $ruleScore = $rule->calculatePoints($entry);

                $score[self::FIELD_RULE_SCORES][$rule->getId()] = $ruleScore;
            } catch (Exception $exception) {
                // TODO - Save errors inside the results ?
                $score[self::FIELD_RULE_SCORES][$rule->getId()] = 0;
                logger()->info($exception);
            }
            $score[self::FIELD_TOTAL_SCORE_IN_POINTS] += $score[self::FIELD_RULE_SCORES][$rule->getId()];
        }

        $score[self::FIELD_TOTAL_SCORE_IN_PERCENTAGE] = $this->calculatePercentage(
            $score[self::FIELD_TOTAL_SCORE_IN_POINTS],
            $score[self::FIELD_TOTAL_POINTS_AVAILABLE]
        );

        return $score;
    }

    /**
     * @param int $score
     * @param int $total
     * @return float
     */
    private function calculatePercentage(int $score, int $total): float
    {
        $percentage = $score > 0 && $total > 0
            ? ($score * 100) / $total
            : 0;

        return round($percentage, 2);
    }

}
