<?php

namespace App\Services\Odin\Ruleset\Traits;

use Illuminate\Contracts\Database\Eloquent\Builder;

trait RulesetFilterRuleTrait
{
    use ConditionTrait;
    /**
     * Check if a table has been joined to a query. Useful to prevent doubling-up on join() methods across rules.
     *
     * @param Builder $query
     * @param string $table
     * @return bool
     */
    function tableIsJoined(Builder $query, string $table): bool
    {
        $joins = collect($query->getQuery()->joins);
        return $joins->pluck('table')->contains($table);
    }
}
