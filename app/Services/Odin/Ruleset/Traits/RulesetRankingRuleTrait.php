<?php

namespace App\Services\Odin\Ruleset\Traits;

use App\Enums\Logical;
use App\Services\Odin\Ruleset\Enums\OperationType;
use App\Services\Odin\Ruleset\OperationHandler;
use Exception;

trait RulesetRankingRuleTrait
{
    use ConditionTrait;

    /**
     * Calculate the points an entity earns with the rule conditions. Return the points as an int.
     *
     * @param $entry
     * @return int
     * @throws Exception
     */
    public function calculatePoints($entry): int
    {
        $score = 0;

        $ruleReference = $this->getReference($entry);

        foreach ($this->data->conditions as $condition) {
            $conditionResult = true;

            foreach ($condition->operations as $operation) {
                $reference = array_merge($operation->reference, $ruleReference);
                $comparisonFieldName = $operation->comparisonFieldName ?? "value";
                $operationResult = OperationHandler::handle($operation->type->value, $comparisonFieldName, $reference);

                if ($operation->type->value === OperationType::RATE->value) {
                    // Return the condition max points if increment is greater than it, otherwise return the operation result
                    $condition->points = $operationResult >= $condition->points ? $condition->points : $operationResult;
                    break;
                }

                if ($operation->logicalOperator->value === Logical::AND->value)
                    $conditionResult = $operationResult && $conditionResult;
                 else if ($operation->logicalOperator->value === Logical::OR->value)
                     $conditionResult = $operationResult || $conditionResult;
            }

            // Only assign the greatest result
            if ($conditionResult && $condition->points > $score) {
                $score = $condition->points;
            }
        }

        return $score;
    }
}
