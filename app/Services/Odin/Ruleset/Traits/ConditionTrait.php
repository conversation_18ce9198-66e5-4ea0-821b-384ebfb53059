<?php

namespace App\Services\Odin\Ruleset\Traits;

use App\Services\Odin\Ruleset\Enums\ConditionType;
use App\Services\Odin\Ruleset\Enums\OperationType;
use App\Services\Odin\Ruleset\Rules\Condition;

trait ConditionTrait
{
    /**
     * @param Condition $condition
     * @return string
     */
    function getConditionType(Condition $condition): string
    {
        $index = collect($condition->operations)->search(function ($operation){
            return $operation->type->value === OperationType::RATE->value;
        });

        return $index === false ? ConditionType::CHECK->value : ConditionType::INCREMENT->value;
    }
}
