<?php

namespace App\Services\Odin\Ruleset\Operations;

class OperationFactory
{
    /**
     * Generate operation class by type
     * @param string $operationType
     * @return OperationContract
     * @throws \Exception
     */
    static function generate(string $operationType): OperationContract {
        $class = match($operationType) {
            LessOperation::TYPE->value              => LessOperation::class,
            LessOrEqualOperation::TYPE->value       => LessOrEqualOperation::class,
            GreaterOperation::TYPE->value           => GreaterOperation::class,
            GreaterOrEqualOperation::TYPE->value    => GreaterOrEqualOperation::class,
            EqualOperation::TYPE->value             => EqualOperation::class,
            BetweenOperation::TYPE->value           => BetweenOperation::class,
            RateOperation::TYPE->value              => RateOperation::class,
            InOperation::TYPE->value                => InOperation::class,
            default                                 => null
        };

        if (!$class) throw new \Exception("Unknown operation " . $operationType);

        return new $class();
    }

}
