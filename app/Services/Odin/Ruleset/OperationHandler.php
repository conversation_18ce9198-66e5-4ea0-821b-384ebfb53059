<?php

namespace App\Services\Odin\Ruleset;

use App\Services\Odin\Ruleset\Operations\OperationFactory;

class OperationHandler
{
    /**
     * Execute php operation changing default value name if necessary
     * @param string $operationType
     * @param string $valueField
     * @param array $reference
     * @return mixed
     * @throws \Exception
     */
    public static function handle(string $operationType, string $valueField, array $reference): mixed
    {
        $operation = OperationFactory::generate($operationType);

        try {
            return $operation->exec($reference[$valueField], $reference);
        } catch (\Exception $exception) {
            logger()->error($exception);
            return 0;
        }
    }
}
