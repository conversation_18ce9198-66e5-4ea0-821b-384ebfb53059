<?php

namespace App\Services\Odin\Ruleset\Factories;

use App\Enums\Logical;
use App\Models\Ruleset;
use App\Services\Odin\Ruleset\Enums\OperationType;
use App\Services\Odin\Ruleset\Rules\CompanyRules\CompanyAppointmentRejectionRule;
use App\Services\Odin\Ruleset\Rules\CompanyRules\CompanyEmployeeRule;
use App\Services\Odin\Ruleset\Rules\CompanyRules\CompanyGoogleReviewRule;
use App\Services\Odin\Ruleset\Rules\CompanyRules\CompanyLeadRejectionRule;
use App\Services\Odin\Ruleset\Rules\CompanyRules\CompanyLeadRule;
use App\Services\Odin\Ruleset\Rules\CompanyRules\CompanyLocationRule;
use App\Services\Odin\Ruleset\Rules\CompanyRules\CompanyOfficeLocationsRule;
use App\Services\Odin\Ruleset\Rules\CompanyRules\CompanyRevenueEarnRule;
use App\Services\Odin\Ruleset\Rules\Condition;
use App\Services\Odin\Ruleset\Rules\Operation;
use App\Services\Odin\Ruleset\Rules\RuleData;

class RulesFactory
{
    // TODO: Add support for multiple rules from different sources.
    // Currently, all registered rules belong to companies. This will need to be changed if we need to add support for different rules.
    // Perhaps create a factory of factories based on the source to return all available rules.
    public static function getCompanyRules(?Ruleset $ruleset = null): array
    {
        // The order matters. The frontend renders following the array order
        $companyRules = [
            CompanyLeadRule::$id => CompanyLeadRule::class,
            CompanyLocationRule::$id => CompanyLocationRule::class,
            CompanyGoogleReviewRule::$id => CompanyGoogleReviewRule::class,
            CompanyEmployeeRule::$id => CompanyEmployeeRule::class,
            CompanyAppointmentRejectionRule::$id => CompanyAppointmentRejectionRule::class,
            CompanyLeadRejectionRule::$id => CompanyLeadRejectionRule::class,
            CompanyRevenueEarnRule::$id => CompanyRevenueEarnRule::class,
            CompanyOfficeLocationsRule::$id => CompanyOfficeLocationsRule::class,
//            CompanyAdwordsRule::$id => CompanyAdwordsRule::class, // Not implemented yet
        ];

        return self::getRuleClasses($companyRules, $ruleset);
    }

    public static function getRuleClasses(array $companyRules, ?Ruleset $ruleset): array
    {
        $rulesetRules = collect($ruleset->{Ruleset::FIELD_RULES} ?? []);

        $rules = [];

        foreach ($companyRules as $companyRuleClass) {
            try {
                $rule = app()->make($companyRuleClass);

                $index = $rulesetRules->search(function ($item) use($rule) {
                    return $rule->getId() === $item['rule_type'];
                });

                if ($index !== false) {
                    $rule->setData(self::createRuleDataFromArray($rulesetRules[$index]['data']));
                    $rule->setIsActive($rulesetRules[$index]['is_active']);
                }

                $rules[] = $rule;
            } catch (\Exception $exception) {
                logger()->error($exception);
            }
        }

        return $rules;
    }

    private static function createRuleDataFromArray(array $arrayData): RuleData
    {
        $conditions = [];

        foreach ($arrayData['rule_data']['conditions'] as $rawCondition) {
            $operations = [];

            foreach ($rawCondition['operations'] as $rawOperation) {
                $operations[] = new Operation(
                    OperationType::from($rawOperation['type']),
                    $rawOperation['reference'] ?? [],
                    $rawOperation['comparison_field_name'] ?? 'value',
                    Logical::from($rawOperation['logical_operator']?? Logical::AND->value)
                );
            }

            $conditions[] = new Condition($rawCondition['points'], $operations);
        }

        return new RuleData($conditions, $arrayData['rule_data']['max_points']);
    }
}
