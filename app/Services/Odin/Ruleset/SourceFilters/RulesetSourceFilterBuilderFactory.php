<?php

namespace App\Services\Odin\Ruleset\SourceFilters;

use App\Services\Odin\Ruleset\Interfaces\RulesetSourceFilterBuilderInterface;

class RulesetSourceFilterBuilderFactory
{
    static function generate(string $id): RulesetSourceFilterBuilderInterface|null {
        return match($id) {
            CompanyRulesetSourceFilterBuilder::getId() => CompanyRulesetSourceFilterBuilder::query(),
            default => null
        };
    }
}
