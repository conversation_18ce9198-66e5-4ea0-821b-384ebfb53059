<?php

namespace App\Services\Odin\Ruleset\SourceFilters;

use App\Enums\RulesetSource;
use App\Models\Legacy\EloquentCompany;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Repositories\LocationRepository;
use App\Services\DatabaseHelperService;
use App\Services\Odin\Ruleset\Interfaces\RulesetSourceFilterBuilderInterface;
use Illuminate\Contracts\Database\Eloquent\Builder;

class CompanyRulesetSourceFilterBuilder implements RulesetSourceFilterBuilderInterface
{
    const FIELD_INDUSTRIES = 'industries';
    const FIELD_LOCATIONS  = 'locations';
    const FIELD_STATUS  = 'statuses';
    const FIELD_SALE_STATUS  = 'sale_status';
    const FIELD_PAYMENT_METHODS  = 'payment_methods';
    const FIELD_COUNTIES  = 'counties';
    const FIELD_INDUSTRY_SERVICES  = 'industry_services';

    public function __construct(protected LocationRepository $locationRepository)
    {

    }

    static function getId(): string
    {
        return RulesetSource::COMPANIES->value;
    }

    public static function query(): self
    {
        return new self(app()->make(LocationRepository::class));
    }

    public function getQuery(array $filterData, ?Builder $query = null): Builder
    {
        if (!$query) {
            $query = Company::query()->select([Company::TABLE . '.*'])->distinct();
        }

        if (!empty($filterData[self::FIELD_INDUSTRIES])){
            $query->whereHas(Company::RELATION_INDUSTRIES, function(Builder $query) use($filterData) {
                return $query->whereIn(Industry::TABLE . '.' . Industry::FIELD_ID, $filterData[self::FIELD_INDUSTRIES]);
            });
        }

        if (!empty($filterData[self::FIELD_LOCATIONS])){
            $query->whereHas(Company::RELATION_LOCATIONS, function (Builder $query) use ($filterData) {
                $query->whereHas(CompanyLocation::RELATION_ADDRESS, function (Builder $query) use ($filterData) {
                    $query->whereIn(Address::FIELD_STATE, $filterData[self::FIELD_LOCATIONS]);
                });
            }, '=', count($filterData[self::FIELD_LOCATIONS]));
        }

        if (!empty($filterData[self::FIELD_STATUS])){
            $query->whereIn(Company::TABLE . '.' . Company::FIELD_STATUS, $filterData[self::FIELD_STATUS]);
        }

        if (!empty($filterData[self::FIELD_SALE_STATUS])){
            $query->whereIn(Company::TABLE . '.' . Company::FIELD_SALES_STATUS, $filterData[self::FIELD_SALE_STATUS]);
        }

        if (!empty($filterData[self::FIELD_COUNTIES])) {
            $allZipCodesInCountyByCountyLocationIds = $this->locationRepository->getAllZipCodesInCountyByCountyLocationIds($filterData[self::FIELD_COUNTIES]);

            $query->whereHas(Company::RELATION_LOCATIONS, function (Builder $query) use ($allZipCodesInCountyByCountyLocationIds) {
                $query->whereHas(CompanyLocation::RELATION_ADDRESS, function (Builder $query) use ($allZipCodesInCountyByCountyLocationIds) {
                        $query->whereIn(Address::TABLE . '.' . Address::FIELD_ZIP_CODE, $allZipCodesInCountyByCountyLocationIds);
                });
            });
        }

        if (!empty($filterData[self::FIELD_PAYMENT_METHODS])){
            $query->join(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentCompany::TABLE, EloquentCompany::TABLE . '.' . EloquentCompany::ID, Company::TABLE . '.' . Company::FIELD_ID);

            if ($filterData[self::FIELD_PAYMENT_METHODS] === 'no_payment') {
                $query->where(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentCompany::TABLE . '.' . EloquentCompany::PAYMENT_SOURCE, '');
            }
            else $query->whereIn(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentCompany::TABLE . '.' . EloquentCompany::PAYMENT_SOURCE, $filterData[self::FIELD_PAYMENT_METHODS]);
        }

        if (!empty($filterData[self::FIELD_INDUSTRY_SERVICES])) {
            $query->whereHas(Company::RELATION_SERVICES, function(Builder $query) use($filterData) {
                return $query->whereIn(IndustryService::TABLE . '.' . IndustryService::FIELD_ID, $filterData[self::FIELD_INDUSTRY_SERVICES]);
            });
        }

        return $query;
    }
}
