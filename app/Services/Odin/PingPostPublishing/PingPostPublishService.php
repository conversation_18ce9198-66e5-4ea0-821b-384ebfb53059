<?php

namespace App\Services\Odin\PingPostPublishing;

use App\Enums\GlobalConfigurationKey;
use App\Enums\PingPostVariableEnum;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\ConsumerProcessingActivity;
use App\Models\EstimatedRevenuePerLeadByLocation;
use App\Models\LeadProcessingInitial;
use App\Models\LeadProcessingPendingReview;
use App\Models\LeadProcessingUnderReview;
use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductData;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryConfiguration;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use App\Models\PingPostPublishers\PingPostPublisher;
use App\Models\PingPostPublishers\PingPostPublisherFloorPrice;
use App\Models\PingPostPublishers\PingPostPublisherLead;
use App\Models\PingPostPublishers\PingPostPublisherLeadLock;
use App\Models\PingPostPublishers\PingPostPublisherLog;
use App\Models\PingPostPublishers\PingPostPublisherTcpaLanguage;
use App\Models\RecycledLeads\LeadProcessingAged;
use App\Repositories\GlobalConfiguration\GlobalConfigurationRepository;
use App\Services\DatabaseHelperService;
use App\Services\Odin\PingPostPublishing\PingPostPublisherAPIs\BasePingPostPublisherApi;
use App\Services\Odin\PingPostPublishing\PingPostPublisherAPIs\PingPostPublisherApiAdopt;
use App\Services\Odin\PingPostPublishing\PingPostPublisherAPIs\PingPostPublisherApiPorch;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class PingPostPublishService
{

    const array API_CLASSES = [
        PingPostPublisherApiAdopt::class,
        PingPostPublisherApiPorch::class,
    ];

    const string INDUSTRY_NAME_PLACEHOLDER = "__industry__";
    const string DEFAULT_TCPA = "Terms and Conditions, Privacy and TCPA Opt-In: By clicking the \"Continue\" button you agree to our Terms and Privacy Policy and authorize Fixr or our chosen ".self::INDUSTRY_NAME_PLACEHOLDER." contractors to use the phone number/s entered. Some installers may use auto-dialers or send automated text messages. If they cannot contact you these may result in charges to you. You consent to receiving these communications even if the phone number entered above is on the \"Do Not Call\" register. All information is collected and used in accordance with our Privacy Policy and our Terms and Conditions.";

    /**
     * @param GlobalConfigurationRepository $globalConfigurationRepository
     * @param array $config
     * @param string $logString
     */
    public function __construct(
        protected GlobalConfigurationRepository $globalConfigurationRepository,
        protected array $config = [],
        protected string $logString = '',
    )
    {
        // Get config data from global configurations management ping_post
        $globalConfigObject = $this->globalConfigurationRepository->getConfigurationPayload(GlobalConfigurationKey::PING_POST);
        if ($globalConfigObject) {
            $this->config = $globalConfigObject->toArray()['data'] ?? [];
        } else {
            $this->config = [];
        }
    }

    /**
     * @param int $consumerProductId
     * @return bool
     */
    public function checkPingPostValidity(int $consumerProductId): bool
    {
        // Update config from global config management
        $this->updateConfig();

        // Return if ping post is not enabled
        if (!$this->config[PingPostVariableEnum::ENABLED->value])
            return false;

        // If industry does not have active future campaigns, it is viable for ping post
        $cpInfo = $this->validationData($consumerProductId);

        // If industry excluded, return
        if (in_array($cpInfo->{'i_slug'}, $this->config[PingPostVariableEnum::EXCLUDED_INDUSTRIES->value]))
            return false;

        // If missing data, test lead, product assignment exists
        if (strtolower($cpInfo->{Consumer::FIELD_FIRST_NAME}) === 'test' ||
            strtolower($cpInfo->{Consumer::FIELD_EMAIL}) === '<EMAIL>' ||
            $cpInfo->{Consumer::FIELD_EMAIL} === null ||
            $cpInfo->{Consumer::FIELD_FORMATTED_PHONE} === null ||
            $cpInfo->{'trusted_form_url'} === null ||
            $cpInfo->{'pa_id'} !== null
        )
            return false;

        // First check for estimated revenue at current zip code
        $erpl = ConsumerProduct::query()
            ->join(Address::TABLE, Address::TABLE.'.'.Address::FIELD_ID, '=', ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ADDRESS_ID)
            ->join(ServiceProduct::TABLE, ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_ID, '=', ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_SERVICE_PRODUCT_ID)
            ->join(IndustryService::TABLE, ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, '=', IndustryService::TABLE.'.'.IndustryService::FIELD_ID)
            ->join(Industry::TABLE, Industry::TABLE.'.'.Industry::FIELD_ID, '=', IndustryService::TABLE.'.'.IndustryService::FIELD_INDUSTRY_ID)
            ->join(IndustryConfiguration::TABLE, Industry::TABLE.'.'.Industry::FIELD_ID, '=', IndustryConfiguration::TABLE.'.'.IndustryConfiguration::FIELD_INDUSTRY_ID)
            ->join(Location::TABLE, Location::TABLE.'.'.Location::ID, '=', Address::TABLE.'.'.Address::FIELD_ZIP_CODE_LOCATION_ID)
            ->leftJoin(EstimatedRevenuePerLeadByLocation::TABLE, fn ($join) =>
                $join->on(EstimatedRevenuePerLeadByLocation::TABLE.'.'.EstimatedRevenuePerLeadByLocation::FIELD_LOCATION_ID, '=', Location::TABLE.'.'.Location::ID)
                    ->on(EstimatedRevenuePerLeadByLocation::TABLE.'.'.EstimatedRevenuePerLeadByLocation::FIELD_INDUSTRY_ID, '=', Industry::TABLE.'.'.Industry::FIELD_ID)
            )
            ->where(ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ID, $consumerProductId)
            ->get([
                ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ID,
                EstimatedRevenuePerLeadByLocation::TABLE.'.'.EstimatedRevenuePerLeadByLocation::FIELD_ESTIMATED_REVENUE,
                IndustryConfiguration::TABLE.'.'.IndustryConfiguration::FIELD_FUTURE_CAMPAIGNS_ACTIVE,
            ])->first();

        // If estimated revenue exists for zip and industry
        if (($erpl?->{EstimatedRevenuePerLeadByLocation::FIELD_ESTIMATED_REVENUE} ?? 0) > 0)
            return false;

        // Logging to determine impact of selling leads at different levels before active
        self::log("No ERPL", $consumerProductId, null, PingPostPublisherLog::TYPE_WOULD_HAVE_SOLD, $this->config);

        // Check for active industry if inactive industry flag is set
        if ($this->config[PingPostVariableEnum::ONLY_INACTIVE_INDUSTRIES->value] && ($erpl?->{IndustryConfiguration::FIELD_FUTURE_CAMPAIGNS_ACTIVE} ?? false))
            return false;

        return true;
    }

    /**
     * @param int $consumerProductId
     * @param array $config
     * @return void
     */
    public function pingPostPublishLead(int $consumerProductId, array $config = []): void
    {
        // Initialize config and find consumer product
        $this->updateConfig($config);

        // Confirm ping post is enabled
        if (!$this->config[PingPostVariableEnum::ENABLED->value])
            return;

        // Check for ping post lock on lead (already being ping posted)
        $checkLock = PingPostPublisherLeadLock::query()
            ->where(PingPostPublisherLeadLock::FIELD_CONSUMER_PRODUCT_ID, $consumerProductId)
            ->get()
            ->first();

        // Exit if lead is locked
        if ($checkLock) {
            self::log("Locked", $consumerProductId, null, PingPostPublisherLog::TYPE_LOCK, $this->config);
            return;
        }

        // Create lock to prevent multiple execution of ping post logic
        $leadLock = PingPostPublisherLeadLock::create([
            PingPostPublisherLeadLock::FIELD_CONSUMER_PRODUCT_ID => $consumerProductId,
            PingPostPublisherLeadLock::FIELD_CREATED_AT => now(),
        ]);

        // Initial log
        self::log("Start", $consumerProductId, null, PingPostPublisherLog::TYPE_INITIAL, $this->config, $this->config);

        // Get cp model
        $consumerProduct = ConsumerProduct::findOrFail($consumerProductId);

        // Validate lead contains necessary data for ping post
        if (!$this->config[PingPostVariableEnum::PING_ONLY_MODE->value] && !$this->validateConsumerProduct($consumerProductId)) {
            // Clear lead lock
            $leadLock->delete();
            return;
        }

        // Confirm industry is not excluded
        $industry = $consumerProduct->industryService->industry;
        if (!$this->config[PingPostVariableEnum::PING_ONLY_MODE->value] && in_array($industry->slug, $this->config[PingPostVariableEnum::EXCLUDED_INDUSTRIES->value])) {
            self::log("Excluded industry {$industry->name}.", $consumerProductId, null, PingPostPublisherLog::TYPE_ERROR, $this->config);
            // Clear lead lock
            $leadLock->delete();
            return;
        }

        // Store API classes and bids
        $apis = [];
        $bids = [];

        // Loop through each API class and ping if viable
        foreach (self::API_CLASSES as $apiClass) {
            $instance = new $apiClass();

            // If publisher limit is given, only use publishers in list
            if (count($this->config[PingPostVariableEnum::ONLY_PUBLISHERS->value]) === 0 || in_array($instance->getKey(), $this->config[PingPostVariableEnum::ONLY_PUBLISHERS->value])) {
                // Confirm database model for publisher exists, init confirms lead type viable for publisher
                if ($instance->init($consumerProduct, $this->config) && $instance->getModel()) {
                    $bid = $instance->ping();
                    $minPrice = self::getMinFloorPrice($instance->getModel()->id, $consumerProduct->industryService->id);

                    if ($minPrice != 0)
                        self::log("Floor Price: $$minPrice",$consumerProductId,null,PingPostPublisherLog::TYPE_LOG, $this->config);

                    if ($bid >= $minPrice && $bid != 0) {
                        $apis[$instance->getKey()] = $instance;
                        $bids[$instance->getKey()] = $bid;
                    }
                }
            }
        }

        // Post top bid if running in live mode
        if (!$this->config[PingPostVariableEnum::PING_ONLY_MODE->value]) {
            $sold = false;
            while (count($bids) > 0) {
                $maxBid = max($bids);
                $maxBidApiKey = array_search($maxBid, $bids);
                if ($maxBid > 0) {
                    if ($apis[$maxBidApiKey]->post()) {
                        $this->setPostData($consumerProduct, $apis[$maxBidApiKey], $maxBid);
                        $sold = true;
                        break; // If successful post, stop attempting
                    } else {
                        unset($bids[$maxBidApiKey]); // If failed post, post to the next highest bid
                    }
                }
            }

            if (!$sold) {
                ConsumerProcessingActivity::create([
                    ConsumerProcessingActivity::FIELD_CONSUMER_ID => $consumerProduct->consumer_id,
                    ConsumerProcessingActivity::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
                    ConsumerProcessingActivity::FIELD_USER_ID => $this->config[PingPostVariableEnum::AUTOMATION_USER_ID->value],
                    ConsumerProcessingActivity::FIELD_SUMMARY => "Ping Post: No buyers found",
                    ConsumerProcessingActivity::FIELD_COMMENT => null,
                    ConsumerProcessingActivity::FIELD_ACTIVITY_TYPE => 'approved',
                    ConsumerProcessingActivity::FIELD_ACTIVITY_ID => null,
                    ConsumerProcessingActivity::FIELD_SCOPE => 0,
                    ConsumerProcessingActivity::CREATED_AT => now(),
                ]);
            }
        }

        // Clear lead lock
        $leadLock->delete();
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param BasePingPostPublisherApi $api
     * @param float $bid
     * @return void
     */
    public function setPostData(ConsumerProduct $consumerProduct, BasePingPostPublisherApi $api, float $bid): void
    {
        // Set max contact requests to 1
        $consumerProduct->update([
            ConsumerProduct::FIELD_CONTACT_REQUESTS => 1,
            ConsumerProduct::FIELD_GOOD_TO_SELL => true,
            ConsumerProduct::FIELD_STATUS => ConsumerProduct::STATUS_ALLOCATED,
        ]);
        $consumerProduct->consumer->update([
            Consumer::FIELD_MAX_CONTACT_REQUESTS => 1,
            Consumer::FIELD_STATUS => Consumer::STATUS_COMPLETED,
            Consumer::FIELD_STATUS_REASON => "Ping Post Sold",
        ]);

        // Remove from queues
        LeadProcessingInitial::query()->where(LeadProcessingInitial::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->id)->delete();
        LeadProcessingUnderReview::query()->where(LeadProcessingUnderReview::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->id)->delete();
        LeadProcessingPendingReview::query()->where(LeadProcessingPendingReview::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->id)->delete();
        LeadProcessingAged::query()->where(LeadProcessingAged::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->id)->delete();

        // Get API PingPostPublisher model
        $apiModel = $api->getModel();

        // create product assignment entry
        $productAssignment = ProductAssignment::create([
            ProductAssignment::FIELD_COMPANY_ID => $apiModel->company_id,
            ProductAssignment::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
            ProductAssignment::FIELD_COST => $bid,
            ProductAssignment::FIELD_CHARGEABLE => true,
            ProductAssignment::FIELD_DELIVERED => true,
            ProductAssignment::FIELD_SALE_TYPE_ID => 1,
            ProductAssignment::FIELD_DELIVERED_AT => now(),
            ProductAssignment::FIELD_REJECTION_EXPIRY => now(),
            ProductAssignment::CREATED_AT => now(),
            ProductAssignment::UPDATED_AT => now(),
            ProductAssignment::FIELD_BUDGET_ID => 0, // No campaign delivery for ping post leads
            ProductAssignment::FIELD_QUALITY_TIER_ID => 1,
        ]);

        // Create ping post leads entry
        PingPostPublisherLead::create([
            PingPostPublisherLead::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
            PingPostPublisherLead::FIELD_PRODUCT_ASSIGNMENT_ID => $productAssignment->id,
            PingPostPublisherLead::FIELD_PING_POST_PUBLISHER_ID => $apiModel->id,
            PingPostPublisherLead::FIELD_STATUS => PingPostPublisherLead::STATUS_CHARGEABLE,
            PingPostPublisherLead::FIELD_COST => $bid,
            PingPostPublisherLead::FIELD_DATA => null,
            PingPostPublisherLead::FIELD_CREATED_AT => now(),
            PingPostPublisherLead::FIELD_UPDATED_AT => now(),
        ]);

        ConsumerProcessingActivity::create([
            ConsumerProcessingActivity::FIELD_CONSUMER_ID => $consumerProduct->consumer_id,
            ConsumerProcessingActivity::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
            ConsumerProcessingActivity::FIELD_USER_ID => $this->config[PingPostVariableEnum::AUTOMATION_USER_ID->value],
            ConsumerProcessingActivity::FIELD_SUMMARY => "Ping Post: Sold to ".$apiModel->{PingPostPublisher::FIELD_NAME},
            ConsumerProcessingActivity::FIELD_COMMENT => null,
            ConsumerProcessingActivity::FIELD_ACTIVITY_TYPE => 'approved',
            ConsumerProcessingActivity::FIELD_ACTIVITY_ID => null,
            ConsumerProcessingActivity::FIELD_SCOPE => 0,
            ConsumerProcessingActivity::CREATED_AT => now(),
        ]);
    }

    /**
     * Order of preference for config values
     * 1. Given parameters in the config array will overwrite any other values
     * 2. If parameter is not given, Global Configuration Management variable will be used
     * 3. If variable is not given and not set in Global Configuration Management, default value in enum will be used
     *
     * @param array|null $config
     * @return void
     */
    function updateConfig(?array $config = []): void
    {
        if (!$config)
            $config = [];

        foreach (PingPostVariableEnum::cases() as $variable) {
            $metaData = $variable->getMetaData();

            // Use the manual command config if the key is defined
            if (array_key_exists($variable->value, $config)) {
                $this->config[$variable->value] = $config[$variable->value];
            }

            // Use the manual default if global config and command config are not defined
            if (!array_key_exists($variable->value, $this->config)) {
                $this->config[$variable->value] = $metaData[PingPostVariableEnum::KEY_DEFAULT];
            }

            // Cast config variable to correct type
            $this->config[$variable->value] = $variable->castToType($this->config[$variable->value]);
        }
    }

    /**
     * Function to confirm that lead meets necessary criteria for ping post
     * @param int $consumerProductId
     * @return bool
     */
    public function validateConsumerProduct(int $consumerProductId): bool
    {
        $errors = $this->getValidationErrors($consumerProductId);

        if (count($errors) > 0) {
            self::log(
                "Ping Post validation failed, errors: ".implode(', ', $errors),
                $consumerProductId,
                null,
                PingPostPublisherLog::TYPE_ERROR,
                $this->config
            );
            return false;
        }

        return true;
    }

    /**
     * @param int $consumerProductId
     * @return mixed
     */
    public function validationData(int $consumerProductId): mixed
    {
        return ConsumerProduct::query()
            ->join(Address::TABLE, Address::TABLE.'.'.Address::FIELD_ID, '=', ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ADDRESS_ID)
            ->join(Consumer::TABLE, Consumer::TABLE.'.'.Consumer::FIELD_ID, '=', ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CONSUMER_ID)
            ->join(ConsumerProductData::TABLE, ConsumerProductData::TABLE.'.'.ConsumerProductData::FIELD_ID, '=', ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CONSUMER_PRODUCT_DATA_ID)
            ->join(ServiceProduct::TABLE, ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_ID, '=', ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_SERVICE_PRODUCT_ID)
            ->join(IndustryService::TABLE, ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, '=', IndustryService::TABLE.'.'.IndustryService::FIELD_ID)
            ->join(Industry::TABLE, Industry::TABLE.'.'.Industry::FIELD_ID, '=', IndustryService::TABLE.'.'.IndustryService::FIELD_INDUSTRY_ID)
            ->leftJoin(ProductAssignment::TABLE, fn ($join) =>
            $join->on(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, '=', ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ID)
                ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_DELIVERED, true)
            )
            ->where(ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ID, $consumerProductId)
            ->get([
                ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ID.' as cp_id',
                Consumer::TABLE.'.'.Consumer::FIELD_FIRST_NAME,
                Consumer::TABLE.'.'.Consumer::FIELD_LAST_NAME,
                Consumer::TABLE.'.'.Consumer::FIELD_FORMATTED_PHONE,
                Consumer::TABLE.'.'.Consumer::FIELD_EMAIL,
                ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_IP_ADDRESS,
                Address::TABLE.'.'.Address::FIELD_ADDRESS_1,
                Address::TABLE.'.'.Address::FIELD_ZIP_CODE,
                Address::TABLE.'.'.Address::FIELD_CITY,
                ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_ID.' as pa_id',
                Industry::TABLE.'.'.Industry::FIELD_SLUG.' as i_slug',
                IndustryService::TABLE.'.'.IndustryService::FIELD_SLUG.' as is_slug',
                DB::raw("JSON_UNQUOTE(JSON_EXTRACT(".ConsumerProductData::TABLE.'.'.ConsumerProductData::FIELD_PAYLOAD.", '$.trusted_form_url')) as trusted_form_url"),
            ])->first();
    }

    /**
     * @param int $consumerProductId
     * @return array|string[]
     */
    public function getValidationErrors(int $consumerProductId): array
    {
        $cp = $this->validationData($consumerProductId);

        if (!$cp->{'cp_id'}) {
            return ['Ping Post lead validation query failed.'];
        }

        $errors = [];

        if (!$cp->{Consumer::FIELD_FIRST_NAME})
            $errors[] = "Missing first name";

        if (!$cp->{Consumer::FIELD_LAST_NAME})
            $errors[] = "Missing last name";

        if (!$cp->{Consumer::FIELD_FORMATTED_PHONE})
            $errors[] = "Missing formatted phone";

        if (!$cp->{Consumer::FIELD_EMAIL})
            $errors[] = "Missing email";

        if (!$cp->{ConsumerProduct::FIELD_IP_ADDRESS})
            $errors[] = "Missing IP address";

        if (!$cp->{Address::FIELD_ADDRESS_1})
            $errors[] = "Missing address 1";

        if (!$cp->{Address::FIELD_ZIP_CODE})
            $errors[] = "Missing zip code";

        if (!$cp->{Address::FIELD_CITY})
            $errors[] = "Missing city";

        if ($cp->{'pa_id'})
            $errors[] = "Product Assignment exists";

        if (!$cp->{'trusted_form_url'})
            $errors[] = "Missing trusted form url";

        return $errors;
    }

    /**
     * @param string|null $msg
     * @param int|null $cpId
     * @param int|null $publisherId
     * @param int $type
     * @param array $config
     * @param array|null $data
     * @param float|null $bid
     * @return void
     */
    public static function log(?string $msg, ?int $cpId, ?int $publisherId, int $type, array $config, array $data = null, float $bid = null): void
    {
        if ($config[PingPostVariableEnum::PRINT->value])
            print $msg."\n";

        if ($config[PingPostVariableEnum::LOG_LEVEL->value] === 0)
            return;

        if ($config[PingPostVariableEnum::LOG_LEVEL->value] <= 1 && $type !== PingPostPublisherLog::TYPE_ERROR)
            return;

        if ($config[PingPostVariableEnum::LOG_LEVEL->value] <= 2 && $type !== PingPostPublisherLog::TYPE_ERROR && $type !== PingPostPublisherLog::TYPE_INITIAL && $type !== PingPostPublisherLog::TYPE_LOG)
            return;

        if ($config[PingPostVariableEnum::LOG_LEVEL->value] <= 3 &&
            $type !== PingPostPublisherLog::TYPE_ERROR &&
            $type !== PingPostPublisherLog::TYPE_INITIAL &&
            $type !== PingPostPublisherLog::TYPE_LOG &&
            $type !== PingPostPublisherLog::TYPE_WOULD_HAVE_SOLD
        )
            return;

        if ($config[PingPostVariableEnum::LOG_LEVEL->value] <= 4 &&
            $type !== PingPostPublisherLog::TYPE_ERROR &&
            $type !== PingPostPublisherLog::TYPE_INITIAL &&
            $type !== PingPostPublisherLog::TYPE_LOG &&
            $type !== PingPostPublisherLog::TYPE_WOULD_HAVE_SOLD &&
            $type !== PingPostPublisherLog::TYPE_LOCK
        )
            return;

        PingPostPublisherLog::create([
            PingPostPublisherLog::FIELD_CONSUMER_PRODUCT_ID => $cpId,
            PingPostPublisherLog::FIELD_PING_POST_PUBLISHER_ID => $publisherId,
            PingPostPublisherLog::FIELD_TYPE => $type,
            PingPostPublisherLog::FIELD_LOG => $msg,
            PingPostPublisherLog::FIELD_DATA => $data,
            PingPostPublisherLog::FIELD_CREATED_AT => now(),
        ]);
    }

    /**
     * @param int|null $publisherId
     * @param int|null $industryServiceId
     * @param string $industryName
     * @return string
     */
    public static function getTcpaLanguage(?int $publisherId, ?int $industryServiceId, string $industryName): string
    {
        $tcpaLanguageModel = PingPostPublisherTcpaLanguage::query()
            ->where(PingPostPublisherTcpaLanguage::TABLE.'.'.PingPostPublisherTcpaLanguage::FIELD_PING_POST_PUBLISHER_ID, $publisherId)
            ->where(PingPostPublisherTcpaLanguage::TABLE.'.'.PingPostPublisherTcpaLanguage::FIELD_INDUSTRY_SERVICE_ID, $industryServiceId)
            ->get()
            ->first();

        if (!$tcpaLanguageModel)
            $tcpaLanguageModel = PingPostPublisherTcpaLanguage::query()
                ->whereNull(PingPostPublisherTcpaLanguage::TABLE.'.'.PingPostPublisherTcpaLanguage::FIELD_PING_POST_PUBLISHER_ID)
                ->where(PingPostPublisherTcpaLanguage::TABLE.'.'.PingPostPublisherTcpaLanguage::FIELD_INDUSTRY_SERVICE_ID, $industryServiceId)
                ->get()
                ->first();

        if (!$tcpaLanguageModel)
            $tcpaLanguageModel = PingPostPublisherTcpaLanguage::query()
                ->whereNull(PingPostPublisherTcpaLanguage::TABLE.'.'.PingPostPublisherTcpaLanguage::FIELD_PING_POST_PUBLISHER_ID)
                ->whereNull(PingPostPublisherTcpaLanguage::TABLE.'.'.PingPostPublisherTcpaLanguage::FIELD_INDUSTRY_SERVICE_ID)
                ->get()
                ->first();

        $tcpaLanguage = $tcpaLanguageModel->{PingPostPublisherTcpaLanguage::FIELD_TCPA_TEXT} ?? null;

        if (!$tcpaLanguage)
            $tcpaLanguage = self::DEFAULT_TCPA;

        return str_replace(self::INDUSTRY_NAME_PLACEHOLDER, strtolower($industryName), $tcpaLanguage);
    }

    /**
     * @param int $publisherId
     * @param int $industryServiceId
     * @return float
     */
    public static function getMinFloorPrice(int $publisherId, int $industryServiceId): float
    {
        $minPriceModel = PingPostPublisherFloorPrice::query()
            ->where(PingPostPublisherFloorPrice::FIELD_PING_POST_PUBLISHER_ID, $publisherId)
            ->where(PingPostPublisherFloorPrice::FIELD_INDUSTRY_SERVICE_ID, $industryServiceId)
            ->get()
            ->first();

        if (!$minPriceModel)
            $minPriceModel = PingPostPublisherFloorPrice::query()
                ->where(PingPostPublisherFloorPrice::FIELD_PING_POST_PUBLISHER_ID, $publisherId)
                ->whereNull(PingPostPublisherFloorPrice::FIELD_INDUSTRY_SERVICE_ID)
                ->get()
                ->first();

        if (!$minPriceModel)
            $minPriceModel = PingPostPublisherFloorPrice::query()
                ->whereNull(PingPostPublisherFloorPrice::FIELD_PING_POST_PUBLISHER_ID)
                ->whereNull(PingPostPublisherFloorPrice::FIELD_INDUSTRY_SERVICE_ID)
                ->get()
                ->first();

        return $minPriceModel->{PingPostPublisherFloorPrice::FIELD_FLOOR_PRICE} ?? 0;
    }

    /**
     * @param int $consumerProductId
     * @return Collection
     */
    public function getLogs(int $consumerProductId): Collection
    {
        return PingPostPublisherLog::query()
            ->leftJoin(PingPostPublisher::TABLE, PingPostPublisher::TABLE.'.'.PingPostPublisher::FIELD_ID, '=', PingPostPublisherLog::TABLE.'.'.PingPostPublisherLog::FIELD_PING_POST_PUBLISHER_ID)
            ->where(PingPostPublisherLog::TABLE.'.'.PingPostPublisherLog::FIELD_CONSUMER_PRODUCT_ID, $consumerProductId)
            ->orderBy(PingPostPublisherLog::TABLE.'.'.PingPostPublisherLog::FIELD_CREATED_AT)
            ->orderBy(PingPostPublisherLog::TABLE.'.'.PingPostPublisherLog::FIELD_ID)
            ->get([
                PingPostPublisherLog::TABLE.'.'.PingPostPublisherLog::FIELD_CREATED_AT,
                PingPostPublisherLog::TABLE.'.'.PingPostPublisherLog::FIELD_TYPE,
                PingPostPublisherLog::TABLE.'.'.PingPostPublisherLog::FIELD_LOG,
                PingPostPublisher::TABLE.'.'.PingPostPublisher::FIELD_NAME,
                PingPostPublisher::TABLE.'.'.PingPostPublisher::FIELD_COMPANY_ID,
            ]);
    }
}
