<?php

namespace App\Services\Odin\PingPostPublishing\PingPostPublisherAPIs;

use App\Models\Odin\Address;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Models\PingPostPublishers\PingPostPublisher;
use App\Models\PingPostPublishers\PingPostPublisherApiKey;
use App\Models\PingPostPublishers\PingPostPublisherIndustryService;
use App\Models\PingPostPublishers\PingPostPublisherLog;
use App\Models\PingPostPublishers\PingPostPublisherRequest;
use App\Services\Odin\PingPostPublishing\PingPostPublishService;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Http;

class PingPostPublisherApiPorch extends BasePingPostPublisherApi
{
    const string KEY = 'porch';

    const string PROXY_IP = 'proxy_ip';
    const string PROXY_PORT = 'proxy_port';

    const string PROXY_USERNAME = 'proxy_username';
    const string PROXY_PASSWORD = 'proxy_password';

    protected ConsumerProduct $consumerProduct;
    protected array $config;

    protected string $pingUrl;
    protected string $postUrl;
    protected string $zipCode;
    protected ?string $serviceName;
    protected ?string $serviceSubType;

    protected float $bid;

    protected string $proxyIp;
    protected string $proxyPort;

    protected string $proxyUsername;
    protected string $proxyPassword;

    protected string $proxyString;

    protected string $tcpaLanguage;
    protected string $trustedForm;

    protected string $referenceId;

    protected PingPostPublisherApiKey $apiKeyModel;

    protected Collection $industryServiceMap;

    protected array $additionalData;


    public function __construct()
    {
        $this->key = self::KEY;
        $this->bid = 0;
        $this->model = PingPostPublisher::query()->where(PingPostPublisher::FIELD_KEY, self::KEY)->get()->first();
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param array $config
     * @return bool
     */
    public function init(ConsumerProduct $consumerProduct, array $config): bool
    {
        // Store consumer product
        $this->consumerProduct = $consumerProduct;
        $this->config = $config;

        // If no database model found for porch, return false
        if (!$this->model) {
            PingPostPublishService::log("No ping post publisher model found for 'porch' key.", $consumerProduct->id, null, PingPostPublisherLog::TYPE_ERROR, $this->config);
            return false;
        }

        // Confirm publisher is active
        if (!$this->model->{PingPostPublisher::FIELD_ACTIVE}) {
            PingPostPublishService::log("Porch not active publisher.", $consumerProduct->id, $this->model->id, PingPostPublisherLog::TYPE_LOG, $this->config);
            return false;
        }

        // Get API Key
        $apiKey = PingPostPublisherApiKey::query()
            ->where(PingPostPublisherApiKey::FIELD_PING_POST_PUBLISHER_ID, $this->model->id)
            ->where(PingPostPublisherApiKey::FIELD_ACTIVE, true)
            ->get()
            ->first();

        // Confirm API key exists
        if (!$apiKey) {
            PingPostPublishService::log("No active API key defined for Porch.", $consumerProduct->id, $this->model->id, PingPostPublisherLog::TYPE_ERROR, $this->config);
            return false;
        }
        $this->apiKeyModel = $apiKey;

        // Get industry service for cp
        $industryService = $consumerProduct->industryService;
        $industry = $industryService->industry;

        // Get all industry service mappings for porch
        $this->industryServiceMap = PingPostPublisherIndustryService::query()
            ->where(PingPostPublisherIndustryService::FIELD_PING_POST_PUBLISHER_ID, $this->model->id)
            ->where(PingPostPublisherIndustryService::FIELD_ACTIVE, true)
            ->get();

        // If consumer product's industry service is not mapped to this publisher, return false
        if (!in_array($industryService->{IndustryService::FIELD_ID}, $this->industryServiceMap->pluck(PingPostPublisherIndustryService::FIELD_INDUSTRY_SERVICE_ID)->toArray())) {
            PingPostPublishService::log("IndustryService {$industryService->{IndustryService::FIELD_SLUG}} not supported by Porch.", $consumerProduct->id, $this->model->id, PingPostPublisherLog::TYPE_LOG, $this->config);
            return false;
        }

        $cpd = $this->consumerProduct->consumerProductData;
        $this->trustedForm = $cpd->payload['trusted_form_url'] ?? '';
        if ($this->trustedForm === '') {
            PingPostPublishService::log("No trusted form.", $consumerProduct->id, $this->model->id, PingPostPublisherLog::TYPE_LOG, $this->config);
            return false;
        }

        // Get numCategory from industry service definition
        $categoryMapping = $this->industryServiceMap
            ->where(PingPostPublisherIndustryService::FIELD_INDUSTRY_SERVICE_ID, $industryService->{IndustryService::FIELD_ID})
            ->first();

        $this->additionalData = [];

        // Key value overrides any data mapping
        if ($categoryMapping->{PingPostPublisherIndustryService::FIELD_KEY}) {
            $this->serviceName = $categoryMapping->{PingPostPublisherIndustryService::FIELD_KEY};
        } else {
            // Mapping depends on values defined in consumer_product_data
            $consumerProductData = $consumerProduct->consumerProductData->payload;

            // data mapping is used if key value is not present
            $dataMap = $categoryMapping->data;

            // Init to default, will be overwritten if mapping is defined and legit
            $this->serviceName = $dataMap['__service_name__'] ?? null;
            $this->serviceSubType = $dataMap['__service_sub_type__'] ?? null;

            // Windows edge case
            if ($this->serviceName === 'Windows') {
                $windowCount = $consumerProductData['approximate_window_quantity'] ?? 4;
                if ($windowCount > 9) {
                    $this->additionalData['units'] = '10+';
                } else {
                    $this->additionalData['units'] = (string) $windowCount;
                }
            } else {
                // Get mapping based on cpd
                foreach ($dataMap as $dataKey => $valueToNumCategory) {
                    if ($dataKey !== '__service_name__' && $dataKey !== '__service_sub_type__') {
                        $value = $consumerProductData[$dataKey] ?? null;
                        if ($value) {
                            $this->additionalData[$valueToNumCategory['__key__']] = $valueToNumCategory[$value] ?? $valueToNumCategory['__default__'];
                        } else {
                            $this->additionalData[$valueToNumCategory['__key__']] = $valueToNumCategory['__default__'];
                        }
                    }
                }
            }
        }

        // Confirm mapping was successful
        if (!$this->serviceName) {
            PingPostPublishService::log("Failed to map industry service {$industryService->{IndustryService::FIELD_SLUG}} for Porch.", $consumerProduct->id, $this->model->id, PingPostPublisherLog::TYPE_ERROR, $this->config);
            return false;
        }

        // Get TCPA Language
        $this->tcpaLanguage = PingPostPublishService::getTcpaLanguage($this->model->id, $industryService->id, $industry->name);

        // Init variables used in ping
        $this->zipCode = $consumerProduct->address->zip_code;
        $this->pingUrl = $this->model->{PingPostPublisher::FIELD_PING_URL} ?? '';
        $this->postUrl = $this->model->{PingPostPublisher::FIELD_POST_URL} ?? '';

        // Get proxy credentials
        $this->proxyIp = $this->model->{PingPostPublisher::FIELD_DATA}[self::PROXY_IP] ?? '';
        $this->proxyPort = $this->model->{PingPostPublisher::FIELD_DATA}[self::PROXY_PORT] ?? '';
        $this->proxyUsername = $this->model->{PingPostPublisher::FIELD_DATA}[self::PROXY_USERNAME] ?? '';
        $this->proxyPassword = $this->model->{PingPostPublisher::FIELD_DATA}[self::PROXY_PASSWORD] ?? '';

        // Build proxy string
        $this->proxyString = 'http://';
        if ($this->proxyUsername !== '')
            $this->proxyString = $this->proxyString.$this->proxyUsername.':'.$this->proxyPassword.'@';
        $this->proxyString = $this->proxyString.$this->proxyIp.':'.$this->proxyPort;

        if ($this->pingUrl === '') {
            PingPostPublishService::log("No ping_url defined for Porch.", $consumerProduct->id, $this->model->id, PingPostPublisherLog::TYPE_ERROR, $this->config);
            return false;
        }

        if ($this->postUrl === '') {
            PingPostPublishService::log("No post_url defined for Porch.", $consumerProduct->id, $this->model->id, PingPostPublisherLog::TYPE_ERROR, $this->config);
            return false;
        }

        return true;
    }

    /**
     * @return float
     */
    public function ping(): float
    {
        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'PORCH_API_KEY: '.$this->apiKeyModel->{PingPostPublisherApiKey::FIELD_KEY},
        ];

        $data = [
            "serviceName" => $this->serviceName,
            "serviceSubType" => $this->serviceSubType,
            "postalCode" => $this->zipCode,
            "isExclusivePurchaseAllowed" => true,
            "isSharedPurchaseAllowed" => false,
            "externalSourceId" => 'fixr',
            "TCPACertificationURL" => $this->trustedForm,
            'TCPALanguage' => $this->tcpaLanguage,
        ];

        foreach ($this->additionalData as $key => $value) {
            $data[$key] = $value;
        }

        $requestLog = PingPostPublisherRequest::create([
            PingPostPublisherRequest::FIELD_PING_POST_PUBLISHER_ID => $this->model->id,
            PingPostPublisherRequest::FIELD_CONSUMER_PRODUCT_ID => $this->consumerProduct->id,
            PingPostPublisherRequest::FIELD_PING_POST_PUBLISHER_API_KEY_ID => $this->apiKeyModel->id,
            PingPostPublisherRequest::FIELD_DESTINATION => $this->pingUrl,
            PingPostPublisherRequest::FIELD_HEADERS => $headers,
            PingPostPublisherRequest::FIELD_REQUEST => $data,
            PingPostPublisherRequest::FIELD_PROCESSED => false,
        ]);

        try {
            $response = Http::withHeaders($headers)->withOptions([
                'proxy' => $this->proxyString,
            ])->post($this->pingUrl, $data);

            // Example Response: {"price": "4.50", "token": "TEST*8293603bf3c8cf96b3dc24bcfd4f00ec", "result": "ACCEPTED"}
            $responseData = $response->json();
            $requestLog->update([PingPostPublisherRequest::FIELD_RESPONSE => $responseData]);

            // Get result
            $referenceId = $responseData['referenceId'] ?? '';
            $bid = 0;

            // If successful, get bid and token
            if ($referenceId !== '') {
                $bid = floatval($responseData['exclusivePayout']['price'] ?? 0);
                $this->referenceId = $referenceId;

                // Log bid
                PingPostPublishService::log("Porch Bid: $$bid", $this->consumerProduct->id, $this->model->id, PingPostPublisherLog::TYPE_LOG, $this->config, null, $bid);
            } else {
                $errorCode = $responseData['code'] ?? '';
                $error = $responseData['message'] ?? '';
                PingPostPublishService::log("Porch ping rejected, message: $error ($errorCode).", $this->consumerProduct->id, $this->model->id, PingPostPublisherLog::TYPE_LOG, $this->config);
            }

            // Set request to processed
            $requestLog->update([PingPostPublisherRequest::FIELD_PROCESSED => true]);

            // Return bid
            $this->bid = $bid;
            return $bid;
        } catch (Exception $e) {
            // Log error
            PingPostPublishService::log("Porch ping failed, message: {$e->getMessage()}.", $this->consumerProduct->id, $this->model->id, PingPostPublisherLog::TYPE_ERROR, $this->config);
            return 0;
        }
    }

    /**
     * @return bool
     */
    public function post(): bool
    {
        if (!$this->referenceId) {
            PingPostPublishService::log("Attempted to post to porch without reference id.", $this->consumerProduct->id, $this->model->id, PingPostPublisherLog::TYPE_ERROR, $this->config);
            return false;
        }

        $address = $this->consumerProduct->address;
        $zipCode = $address->zip_code;
        $addressString = $address->address_2 ? $address->address_1." ".$address->address_2 : $address->address_1;
        $city = $address->city;
        $state = $address->stateLocation->state_abbr;

        $consumer = $this->consumerProduct->consumer;
        $firstName = $consumer->first_name;
        $lastName = $consumer->last_name;
        $telephone = $consumer->formatted_phone;
        $email = $consumer->email;
        $ip = $this->consumerProduct->ip_address;
        $customId = $this->consumerProduct->id;
        $tcpaText = $this->tcpaLanguage;
        $cpd = $this->consumerProduct->consumerProductData;

        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'PORCH_API_KEY:'.$this->apiKeyModel->{PingPostPublisherApiKey::FIELD_KEY},
        ];

        $data = [
            'referenceId' => $this->referenceId,
            'status' => 'ACCEPTED',
            'postalCode' => $zipCode,
            'firstName' => $firstName,
            'lastName' => $lastName,
            'address' => $addressString,
            'customerPhone' => $telephone,
            'customerEmail' => $email,
            'leadId' => $customId, // Consumer product ID
            'city' => $city,
            'state' => $state,
            'ip' => $ip,
            'isMobileTCPA' => false,
            'TCPALanguage' => $tcpaText,
            'TCPACertificationURL' => $this->trustedForm,
            'homeownerIPAddress' => $ip,
            'externalSourceId' => 'fixr',
            'timeframe' => 'flexible',
            'isHomeowner' => true,
            'isExclusive' => true,
        ];

        $requestLog = PingPostPublisherRequest::create([
            PingPostPublisherRequest::FIELD_PING_POST_PUBLISHER_ID => $this->model->id,
            PingPostPublisherRequest::FIELD_CONSUMER_PRODUCT_ID => $this->consumerProduct->id,
            PingPostPublisherRequest::FIELD_PING_POST_PUBLISHER_API_KEY_ID => $this->apiKeyModel->id,
            PingPostPublisherRequest::FIELD_DESTINATION => $this->postUrl,
            PingPostPublisherRequest::FIELD_HEADERS => $headers,
            PingPostPublisherRequest::FIELD_REQUEST => $data,
            PingPostPublisherRequest::FIELD_PROCESSED => false,
        ]);

        try {
            $response = Http::withHeaders($headers)->withOptions([
                'proxy' => $this->proxyString,
            ])->post($this->postUrl, $data);

            // Example Response:
            $responseData = $response->json();
            $requestLog->update([PingPostPublisherRequest::FIELD_RESPONSE => $responseData]);

            // Get result
            $result = $responseData['success'] ?? '';

            // If successful
            if ($result === true) {
                // Log bid
                PingPostPublishService::log("Posted to Porch for $".$this->bid, $this->consumerProduct->id, $this->model->id, PingPostPublisherLog::TYPE_LOG, $this->config);
                $requestLog->update([PingPostPublisherRequest::FIELD_PROCESSED => true]);
                return true;
            } else {
                $error = $responseData['errorMessage'] ?? '';
                PingPostPublishService::log("Porch post failed, message: $error.", $this->consumerProduct->id, $this->model->id, PingPostPublisherLog::TYPE_ERROR, $this->config);
                $requestLog->update([PingPostPublisherRequest::FIELD_PROCESSED => true]);
                return false;
            }
        } catch (Exception $e) {
            // Log error
            PingPostPublishService::log("Porch post failed, message: {$e->getMessage()}.", $this->consumerProduct->id, $this->model->id, PingPostPublisherLog::TYPE_ERROR, $this->config);
            return false;
        }
    }
}
