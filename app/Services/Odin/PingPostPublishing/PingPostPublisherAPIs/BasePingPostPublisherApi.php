<?php

namespace App\Services\Odin\PingPostPublishing\PingPostPublisherAPIs;

use App\Models\Odin\ConsumerProduct;
use App\Models\PingPostPublishers\PingPostPublisher;

abstract class BasePingPostPublisherApi
{
    /**
     * Key Value for the associated entry in ping_post_publishers table
     * @var string
     */
    protected string $key;

    /**
     * Model storing configuration values for publisher, associated by key value in class (ping_post_publishers table)
     * @var PingPostPublisher|null
     */
    protected ?PingPostPublisher $model;

    /**
     * Bid value returned from the api
     * @var float
     */
    protected float $bid;

    /**
     * This function retrieves necessary data for consumer product, returns true if valid cp for ping post
     * @param ConsumerProduct $consumerProduct
     * @param array $config
     * @return bool
     */
    abstract public function init(ConsumerProduct $consumerProduct, array $config): bool;

    /**
     * Ping and return bid from buyer
     * @return float
     */
    abstract public function ping(): float;

    /**
     * Send the consumer product defined in the init function
     * @return bool
     */
    abstract public function post(): bool;

    /**
     * Returns true if model found, false otherwise
     * @return bool
     */
    public function modelExists(): bool
    {
        return !!$this->model;
    }

    /**
     * Returns publisher model
     * @return null|PingPostPublisher
     */
    public function getModel(): ?PingPostPublisher
    {
        return $this->model;
    }

    /**
     * Returns bid from api response
     * @return float
     */
    public function getBid(): float
    {
        return $this->bid;
    }

    /**
     * @return string
     */
    public function getKey(): string
    {
        return $this->key;
    }
}
