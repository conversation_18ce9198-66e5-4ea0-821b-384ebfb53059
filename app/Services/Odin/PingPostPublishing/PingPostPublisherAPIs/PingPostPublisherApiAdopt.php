<?php

namespace App\Services\Odin\PingPostPublishing\PingPostPublisherAPIs;

use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Models\PingPostPublishers\PingPostPublisher;
use App\Models\PingPostPublishers\PingPostPublisherApiKey;
use App\Models\PingPostPublishers\PingPostPublisherIndustryService;
use App\Models\PingPostPublishers\PingPostPublisherLog;
use App\Models\PingPostPublishers\PingPostPublisherRequest;
use App\Services\Odin\PingPostPublishing\PingPostPublishService;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Http;

class PingPostPublisherApiAdopt extends BasePingPostPublisherApi
{
    const string KEY = 'adopt';

    const string DATA_PARTNER_ID = 'partnerid';
    const string DATA_SUB_ID = 'subid';
    const string DATA_RESP_TYPE = 'resptype';

    const array ERROR_MAP = [
        '1000' => 'Server under maintenance',
        '1100' => 'Request limit exceeded',
        '2000' => 'Invalid partner ID or blocked source',
        '3000' => 'Approval phase',
        '4000' => 'Daily cap reached',
        '5000' => 'No coverage or buyers',
        '6000' => 'Minimum bid not met',
    ];

    protected ConsumerProduct $consumerProduct;
    protected array $config;

    // These variables used in ping
    protected string $partnerId;
    protected string $apiKey;
    protected string $subId;
    protected string $respType;
    protected string $numCategory;
    protected string $zipCode;

    // Token returned in ping, used in post to map from ping
    protected string $token;

    protected string $pingUrl;
    protected string $postUrl;

    protected float $bid;

    protected string $tcpaLanguage;

    protected PingPostPublisherApiKey $apiKeyModel;

    protected Collection $industryServiceMap;


    public function __construct()
    {
        $this->key = self::KEY;
        $this->bid = 0;
        $this->model = PingPostPublisher::query()->where(PingPostPublisher::FIELD_KEY, self::KEY)->get()->first();
    }

    /**
     * Docs here https://api.letsmakealead.com/?country_market=US&numcategorie=1
     * @param ConsumerProduct $consumerProduct
     * @param array $config
     * @return bool
     */
    public function init(ConsumerProduct $consumerProduct, array $config): bool
    {
        // Store consumer product
        $this->consumerProduct = $consumerProduct;
        $this->config = $config;

        // If no database model found for adopt, return false
        if (!$this->model) {
            PingPostPublishService::log("No ping post publisher model found for 'adopt' key.", $consumerProduct->id, null, PingPostPublisherLog::TYPE_ERROR, $this->config);
            return false;
        }

        // Confirm publisher is active
        if (!$this->model->{PingPostPublisher::FIELD_ACTIVE}) {
            PingPostPublishService::log("Adopt not active publisher.", $consumerProduct->id, $this->model->id, PingPostPublisherLog::TYPE_LOG, $this->config);
            return false;
        }

        // Get API Key
        $apiKeyModel = PingPostPublisherApiKey::query()
            ->where(PingPostPublisherApiKey::FIELD_PING_POST_PUBLISHER_ID, $this->model->id)
            ->where(PingPostPublisherApiKey::FIELD_ACTIVE, true)
            ->get()
            ->first();

        // Confirm API key exists
        if (!$apiKeyModel) {
            PingPostPublishService::log("No active API key defined for Adopt.", $consumerProduct->id, $this->model->id, PingPostPublisherLog::TYPE_ERROR, $this->config);
            return false;
        }
        $this->apiKeyModel = $apiKeyModel;

        // Get industry service for cp
        $industryService = $consumerProduct->industryService;
        $industry = $industryService->industry;

        // Get all industry service mappings for adopt
        $this->industryServiceMap = PingPostPublisherIndustryService::query()
            ->where(PingPostPublisherIndustryService::FIELD_PING_POST_PUBLISHER_ID, $this->model->id)
            ->where(PingPostPublisherIndustryService::FIELD_ACTIVE, true)
            ->get();

        // If consumer product's industry service is not mapped to this publisher, return false
        if (!in_array($industryService->{IndustryService::FIELD_ID}, $this->industryServiceMap->pluck(PingPostPublisherIndustryService::FIELD_INDUSTRY_SERVICE_ID)->toArray())) {
            PingPostPublishService::log("IndustryService {$industryService->{IndustryService::FIELD_SLUG}} not supported by Adopt.", $consumerProduct->id, $this->model->id, PingPostPublisherLog::TYPE_LOG, $this->config);
            return false;
        }

        // Get numCategory from industry service definition
        $categoryMapping = $this->industryServiceMap
            ->where(PingPostPublisherIndustryService::FIELD_INDUSTRY_SERVICE_ID, $industryService->{IndustryService::FIELD_ID})
            ->first();

        // Key value overrides any data mapping
        if ($categoryMapping->{PingPostPublisherIndustryService::FIELD_KEY}) {
            $this->numCategory = $categoryMapping->{PingPostPublisherIndustryService::FIELD_KEY};
        } else {
            // Mapping depends on values defined in consumer_product_data
            $consumerProductData = $consumerProduct->consumerProductData->payload;

            // data mapping is used if key value is not present
            $dataMap = $categoryMapping->data;

            // Init to default, will be overwritten if mapping is defined and legit
            $this->numCategory = $dataMap['default'] ?? null;

            // Get mapping based on cpd
            foreach ($dataMap as $dataKey => $valueToNumCategory) {
                if ($dataKey != 'default') {
                    $value = $consumerProductData[$dataKey] ?? null;
                    if ($value) {
                        $this->numCategory = $valueToNumCategory[$value] ?? $this->numCategory;
                    }
                }
            }
        }

        // Confirm mapping was successful
        if (!$this->numCategory) {
            PingPostPublishService::log("Failed to map industry service {$industryService->{IndustryService::FIELD_SLUG}} for Adopt.", $consumerProduct->id, $this->model->id, PingPostPublisherLog::TYPE_ERROR, $this->config);
            return false;
        }

        // Get TCPA Language
        $this->tcpaLanguage = PingPostPublishService::getTcpaLanguage($this->model->id, $industryService->id, $industry->name);

        // Init variables used in ping
        $this->apiKey = $apiKeyModel->{PingPostPublisherApiKey::FIELD_KEY};
        $this->partnerId = $this->model->data[self::DATA_PARTNER_ID];
        $this->subId = $this->model->data[self::DATA_SUB_ID];
        $this->respType = $this->model->data[self::DATA_RESP_TYPE];
        $this->zipCode = $consumerProduct->address->zip_code;
        $this->pingUrl = $this->model->{PingPostPublisher::FIELD_PING_URL};
        $this->postUrl = $this->model->{PingPostPublisher::FIELD_POST_URL};

        return true;
    }

    /**
     * @return float
     */
    public function ping(): float
    {
        $headers = [
            'Content-Type' => 'application/json',
        ];

        $data = [
            "partnerid" => $this->partnerId,
            "subid" => $this->subId,
            "key" => $this->apiKey,
            "numcategory" => $this->numCategory,
            "resptype" => $this->respType,
            "zipcode" => $this->zipCode,
        ];

        $requestLog = PingPostPublisherRequest::create([
            PingPostPublisherRequest::FIELD_PING_POST_PUBLISHER_ID => $this->model->id,
            PingPostPublisherRequest::FIELD_CONSUMER_PRODUCT_ID => $this->consumerProduct->id,
            PingPostPublisherRequest::FIELD_PING_POST_PUBLISHER_API_KEY_ID => $this->apiKeyModel->id,
            PingPostPublisherRequest::FIELD_DESTINATION => $this->pingUrl,
            PingPostPublisherRequest::FIELD_HEADERS => $headers,
            PingPostPublisherRequest::FIELD_REQUEST => $data,
            PingPostPublisherRequest::FIELD_PROCESSED => false,
        ]);

        try {
            $response = Http::withHeaders($headers)->post($this->pingUrl, $data);

            // Example Response: {"price": "4.50", "token": "TEST*8293603bf3c8cf96b3dc24bcfd4f00ec", "result": "ACCEPTED"}
            $responseData = $response->json();
            $requestLog->update([PingPostPublisherRequest::FIELD_RESPONSE => $responseData]);

            // Get result
            $result = $responseData['result'] ?? '';
            $bid = 0;

            // If successful, get bid and token
            if ($result === "ACCEPTED") {
                $bid = floatval($responseData['price']) ?? 0;
                $this->token = $responseData['token'] ?? null;

                // Log bid
                PingPostPublishService::log("Adopt Bid: $$bid", $this->consumerProduct->id, $this->model->id, PingPostPublisherLog::TYPE_LOG, $this->config, null, $bid);
            } else {
                $errorCode = $responseData['error'] ?? '';
                $error = self::ERROR_MAP[$errorCode] ?? '';
                PingPostPublishService::log("Adopt ping rejected, message: $error ($errorCode).", $this->consumerProduct->id, $this->model->id, PingPostPublisherLog::TYPE_LOG, $this->config);
            }

            // Set request to processed
            $requestLog->update([PingPostPublisherRequest::FIELD_PROCESSED => true]);

            // Return bid
            $this->bid = $bid;
            return $bid;
        } catch (Exception $e) {
            // Log error
            PingPostPublishService::log("Adopt ping failed, message: {$e->getMessage()}.", $this->consumerProduct->id, $this->model->id, PingPostPublisherLog::TYPE_ERROR, $this->config);
            return 0;
        }
    }

    /**
     * @return bool
     */
    public function post(): bool
    {
        if (!$this->token) {
            PingPostPublishService::log("Attempted to post to adopt without token.", $this->consumerProduct->id, $this->model->id, PingPostPublisherLog::TYPE_ERROR, $this->config);
            return false;
        }

        $address = $this->consumerProduct->address;
        $zipCode = $address->zip_code;
        $street = $address->address_2 ? $address->address_1." ".$address->address_2 : $address->address_1;
        $city = $address->city;

        $consumer = $this->consumerProduct->consumer;
        $firstName = $consumer->first_name;
        $lastName = $consumer->last_name;
        $telephone = $consumer->formatted_phone;
        $email = $consumer->email;
        $ip = $this->consumerProduct->ip_address;
        $customId = $this->consumerProduct->id;
        $tcpaText = $this->tcpaLanguage;

        $cpd = $this->consumerProduct->consumerProductData;
        $trustedFormUrl = $cpd->payload['trusted_form_url'] ?? '';
        $trustedFormToken = str_replace('https://cert.trustedform.com/', '', $trustedFormUrl);

        $headers = [
            'Content-Type' => 'application/json',
        ];

        $data = [
            'partnerid' => $this->partnerId,
            'subid' => $this->subId,
            'key' => $this->apiKey,
            'numcategory' => $this->numCategory,
            'resptype' => $this->respType,
            'token' => $this->token,
            'zipcode' => $zipCode,
            'firstname' => $firstName,
            'lastname' => $lastName,
            'address' => $street,
            'telephone' => $telephone,
            'email' => $email,
            'customid' => $customId, // Consumer product ID
            'trusted_form_available' => 1,
            'trusted_form_token' => $trustedFormToken,
            'city' => $city,
            'ip' => $ip,
            'tcpa_optin' => 1,
            'tcpa_text' => $tcpaText,
        ];

        $requestLog = PingPostPublisherRequest::create([
            PingPostPublisherRequest::FIELD_PING_POST_PUBLISHER_ID => $this->model->id,
            PingPostPublisherRequest::FIELD_CONSUMER_PRODUCT_ID => $this->consumerProduct->id,
            PingPostPublisherRequest::FIELD_PING_POST_PUBLISHER_API_KEY_ID => $this->apiKeyModel->id,
            PingPostPublisherRequest::FIELD_DESTINATION => $this->postUrl,
            PingPostPublisherRequest::FIELD_HEADERS => $headers,
            PingPostPublisherRequest::FIELD_REQUEST => $data,
            PingPostPublisherRequest::FIELD_PROCESSED => false,
        ]);

        try {
            $response = Http::withHeaders($headers)->post($this->postUrl, $data);

            // Example Response:
            $responseData = $response->json();
            $requestLog->update([PingPostPublisherRequest::FIELD_RESPONSE => $responseData]);

            // Get result
            $result = $responseData['result'] ?? '';

            // If successful, get bid and token
            if ($result === "ACCEPTED") {
                // Log bid
                PingPostPublishService::log("Posted to Adopt for $".$this->bid, $this->consumerProduct->id, $this->model->id, PingPostPublisherLog::TYPE_LOG, $this->config);
                $requestLog->update([PingPostPublisherRequest::FIELD_PROCESSED => true]);
                return true;
            } else {
                $errorCode = $responseData['error'] ?? '';
                $error = self::ERROR_MAP[$errorCode] ?? '';
                PingPostPublishService::log("Adopt post failed, message: $error ($errorCode).", $this->consumerProduct->id, $this->model->id, PingPostPublisherLog::TYPE_ERROR, $this->config);
                $requestLog->update([PingPostPublisherRequest::FIELD_PROCESSED => true]);
                return false;
            }
        } catch (Exception $e) {
            // Log error
            PingPostPublishService::log("Adopt post failed, message: {$e->getMessage()}.", $this->consumerProduct->id, $this->model->id, PingPostPublisherLog::TYPE_ERROR, $this->config);
            return false;
        }
    }
}
