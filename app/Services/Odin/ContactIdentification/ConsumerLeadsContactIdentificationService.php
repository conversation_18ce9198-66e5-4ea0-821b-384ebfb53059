<?php
namespace App\Services\Odin\ContactIdentification;

use App\Contracts\Odin\ContactIdentificationServiceContract;
use App\Models\Odin\Company;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Product;
use App\Models\Odin\ServiceProduct;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Repositories\Odin\ConsumerRepository;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class ConsumerLeadsContactIdentificationService implements ContactIdentificationServiceContract
{
    const RELATION_TYPE = 'consumers';
    const FRIENDLY_RELATION_TYPE = 'Consumer';
    const RELATION_SUBTYPE = 'lead';
    const FRIENDLY_RELATION_SUBTYPE = 'Lead';


    public function __construct(protected ConsumerProductRepository $consumerProduct)
    {

    }

    /**
     * @inheritdoc
     */
    public function transform(mixed $entity): array
    {
        // TODO - Perhaps change it to consumer direct
        if($entity instanceof Consumer) {
            return [
                ContactIdentificationService::FIELD_CONTACT_NAME              => $entity->getFullName(),
                ContactIdentificationService::FIELD_CONTACT_PHONE             => $entity->{Consumer::FIELD_PHONE},
                ContactIdentificationService::FIELD_CONTACT_RELATION_ID       => $entity->{Consumer::FIELD_ID},
                ContactIdentificationService::FIELD_CONTACT_RELATION_TYPE     => self::RELATION_SUBTYPE,
                ContactIdentificationService::FIELD_RELATION_DATA             => [
                    ContactIdentificationService::FIELD_CONTACT_RELATION_DATA_NAME    => \App\Enums\Odin\Product::LEAD->name,
                    ContactIdentificationService::FIELD_CONTACT_RELATION_DATA_ID      => \App\Enums\Odin\Product::LEAD->value
                ],
            ];
        }

       return [
           ContactIdentificationService::FIELD_CONTACT_NAME              => $entity->{ConsumerProduct::RELATION_CONSUMER}->getFullName(),
           ContactIdentificationService::FIELD_CONTACT_PHONE             => $entity->{ConsumerProduct::RELATION_CONSUMER}->{Consumer::FIELD_PHONE},
           ContactIdentificationService::FIELD_CONTACT_RELATION_ID       => $entity->{ConsumerProduct::RELATION_CONSUMER}->{Consumer::FIELD_ID},
           ContactIdentificationService::FIELD_RELATION_DATA             => [
               ContactIdentificationService::FIELD_CONTACT_RELATION_DATA_NAME    => $entity->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::RELATION_PRODUCT}->{Product::FIELD_NAME},
               ContactIdentificationService::FIELD_CONTACT_RELATION_DATA_ID      => $entity->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::RELATION_PRODUCT}->{Product::FIELD_ID},
           ],
           ContactIdentificationService::FIELD_CONTACT_COMMENT           =>
               $entity->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::RELATION_PRODUCT}->{Product::FIELD_NAME} .
               " #" .
               $entity->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::RELATION_PRODUCT}->{Product::FIELD_ID},
       ];
    }


    /**
     * @inheritDoc
     * @throws Exception
     */
    public function recognize(string $phone): Collection
    {
        return $this->consumerProduct->getConsumerInfoByPhone($phone);
    }

    /**
     * @inheritDoc
     */
    public function getRelationType(): string
    {
        return self::RELATION_TYPE;
    }

    /**
     * @inheritDoc
     */
    public function getFriendlyRelationType(): string
    {
        return self::FRIENDLY_RELATION_TYPE;
    }

    /**
     * @inheritDoc
     */
    public function getRelationSubtype(mixed $entity): string
    {
        return self::RELATION_SUBTYPE;
    }

    /**
     * @inheritDoc
     */
    public function getFriendlyRelationSubtype(mixed $entity): string
    {
        return self::FRIENDLY_RELATION_SUBTYPE;
    }

    /**
     * At the moment, this method is not needed because we are not creating activities for lead calls
     * @inheritdoc
     */
    public function getCompanyByContactId(string $contactId): Company
    {
        // TODO: Implement getCompanyByContactId() method.
    }

    public function getRelatedContactsByIds(array $relationIds): Collection
    {
        return Consumer::query()->whereIn(Consumer::FIELD_ID, $relationIds)->get();
    }
}

