<?php

namespace App\Services\Odin\ContactIdentification;

use App\Contracts\Odin\ContactIdentificationServiceContract;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Repositories\Odin\CompanyLocationRepository;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class CompanyLocationContactIdentificationService implements ContactIdentificationServiceContract
{
    const RELATION_TYPE = 'company';
    const FRIENDLY_RELATION_TYPE = 'Company';
    const RELATION_SUBTYPE = 'company_locations';
    const FRIENDLY_RELATION_SUBTYPE = 'Company Location';


    public function __construct(protected CompanyLocationRepository $companyLocationRepository)
    {

    }

    /**
     * @inheritDoc
     */
    public function transform(mixed $entity): array
    {
        return [
            ContactIdentificationService::FIELD_CONTACT_NAME              => $entity->{CompanyLocation::FIELD_NAME},
            ContactIdentificationService::FIELD_CONTACT_PHONE             => $entity->{CompanyLocation::FIELD_PHONE},
            ContactIdentificationService::FIELD_CONTACT_RELATION_ID       => $entity->{CompanyLocation::FIELD_ID},
            ContactIdentificationService::FIELD_CONTACT_RELATION_TYPE     => self::RELATION_SUBTYPE,
            ContactIdentificationService::FIELD_RELATION_DATA             => [
                ContactIdentificationService::FIELD_CONTACT_RELATION_DATA_NAME    => $entity->company_name,
                ContactIdentificationService::FIELD_CONTACT_RELATION_DATA_ID      => $entity->company_id,
            ],
            ContactIdentificationService::FIELD_CONTACT_COMMENT           => $entity->company_name . " #" . $entity->company_id
        ];
    }

    /**
     * @inheritDoc
     */
    public function recognize(string $phone): Collection
    {
        return $this->companyLocationRepository->getCompanyLocationsByPhone($phone);
    }

    /**
     * @inheritDoc
     */
    public function getRelationType(): string
    {
        return self::RELATION_TYPE;
    }

    /**
     * @inheritDoc
     */
    public function getFriendlyRelationType(): string
    {
        return self::FRIENDLY_RELATION_TYPE;
    }

    /**
     * @inheritDoc
     */
    public function getRelationSubtype(mixed $entity): string
    {
        return self::RELATION_SUBTYPE;
    }

    /**
     * @inheritDoc
     */
    public function getFriendlyRelationSubtype(mixed $entity): string
    {
        return self::FRIENDLY_RELATION_SUBTYPE;
    }

    /**
     * @inheritDoc
     */
    public function getCompanyByContactId(string $contactId): Company
    {
        $companyLocation = $this->companyLocationRepository->getCompanyLocationById($contactId);

        return $companyLocation->{CompanyLocation::RELATION_COMPANY};
    }

    public function getRelatedContactsByIds(array $relationIds): Collection
    {
        // TODO - Create a method to return a base query already populated with select and join
        //  Should be used in getCompanyByContactId and recognize
        return CompanyLocation::query()
            ->select([
                Company::TABLE . '.' . Company::FIELD_ID . ' as company_id',
                Company::TABLE . '.' . Company::FIELD_NAME . ' as company_name',
                CompanyLocation::TABLE . '.' . '*'
            ])
            ->whereIn(CompanyLocation::TABLE . '.' . CompanyLocation::FIELD_ID, $relationIds)
            ->join(Company::TABLE, Company::TABLE . '.' . Company::FIELD_ID, '=', CompanyLocation::TABLE . '.' . CompanyLocation::FIELD_COMPANY_ID)
            ->get();
    }
}
