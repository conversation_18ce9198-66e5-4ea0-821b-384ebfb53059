<?php

namespace App\Services\Odin\ContactIdentification;

use App\Contracts\Odin\ContactIdentificationServiceContract;
use App\Repositories\CommunicationRepository;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Exception;

class ContactIdentificationService
{
    const FIELD_CONTACT_NAME             = 'contactName';
    const FIELD_CONTACT_PHONE            = 'contactPhone';
    const FIELD_CONTACT_RELATION_ID      = 'relationId';
    const FIELD_CONTACT_RELATION_TYPE    = 'relationType';
    const FIELD_CONTACT_RELATION_SUBTYPE    = 'relationSubtype';
    const FIELD_CONTACT_RELATION_DATA_ID    = 'id';
    const FIELD_CONTACT_RELATION_DATA_NAME    = 'name';

    const FIELD_CONTACT_FRIENDLY_RELATION_TYPE    = 'friendlyRelationType';
    const FIELD_CONTACT_FRIENDLY_RELATION_SUBTYPE    = 'friendlyRelationSubtype';
    const FIELD_PREVIOUS_COMMUNICATION_COUNT = 'previousCommunicationCount';
    const FIELD_CONTACT_COMMENT = 'comment';

    const FIELD_RELATION_DATA = 'relationData';

    public function __construct(protected CommunicationRepository $communicationRepository)
    {
    }


    const IDENTIFICATION_SERVICES = [
        CompanyLocationContactIdentificationService::RELATION_SUBTYPE  => CompanyLocationContactIdentificationService::class,
        ConsumerLeadsContactIdentificationService::RELATION_SUBTYPE    => ConsumerLeadsContactIdentificationService::class,
        CompanyUserContactIdentificationService::RELATION_SUBTYPE      => CompanyUserContactIdentificationService::class,
    ];


    /**
     * Return a list of possible identified contacts
     * @param int $userPrimaryPhoneId
     * @param string $phone
     * @return array
     */
    public function identify(int $userPrimaryPhoneId, string $phone): array
    {
        // TODO - Make $userPrimaryPhoneId optional
        $results = [];

        foreach (self::IDENTIFICATION_SERVICES as $service) {
            // Create a new instance of the service by injecting its dependencies
            $instance = App::make($service);

            try {
                $entityResults = $instance->recognize($phone);

                $results[] = $this->formatResults($instance, $entityResults, $userPrimaryPhoneId);
            }catch (Exception $exception){
                Log::error($exception);
            }
        }

        return array_merge(...$results);
    }


    /**
     * Format all results using instance's transform method
     * @param ContactIdentificationServiceContract $instance
     * @param Collection $entities
     * @param int $userPrimaryPhoneId
     * @return array
     */
    private function formatResults(ContactIdentificationServiceContract $instance, Collection $entities, int $userPrimaryPhoneId): array
    {
        $transformations = [];

        foreach ($entities as $entity) {
            try {
                $transformed = $instance->transform($entity);

                $previousCommunicationCount = $this->getCommunicationCount(
                    $instance->getRelationType(),
                    $instance->getRelationSubtype($entity),
                    $transformed[self::FIELD_CONTACT_RELATION_ID],
                    $userPrimaryPhoneId
                );


                // TODO - Perhaps consider moving all properties to methods to improve understanding for future developers
                $transformations[] = [
                    ...$transformed,
                    self::FIELD_CONTACT_RELATION_TYPE             => $instance->getRelationType(),
                    self::FIELD_CONTACT_FRIENDLY_RELATION_TYPE    => $instance->getFriendlyRelationType(),
                    self::FIELD_CONTACT_RELATION_SUBTYPE          => $instance->getRelationSubtype($entity),
                    self::FIELD_CONTACT_FRIENDLY_RELATION_SUBTYPE => $instance->getFriendlyRelationSubtype($entity),
                    self::FIELD_PREVIOUS_COMMUNICATION_COUNT      => $previousCommunicationCount
                ];
            }catch(Exception $e) {
                logger()->error($e);
            }
        }

        return $transformations;
    }


    /**
     * Get communication count
     * @param string $relationType
     * @param string $relationSubType
     * @param int $relationId
     * @param int $userPrimaryPhoneId
     * @return int
     */
    private function getCommunicationCount(string $relationType, string $relationSubType, int $relationId, int $userPrimaryPhoneId): int
    {
        if ($relationType === 'company') $relationType = $relationSubType;

        return $this->communicationRepository->countInteractions($relationType,$relationId, $userPrimaryPhoneId);
    }


    /**
     * Generate contact identification service by type
     * @param string $type
     * @return ContactIdentificationServiceContract|null
     * @throws Exception
     */
    public function generateContactIdentificationServiceByType(string $type): ContactIdentificationServiceContract | null{
        if (!in_array($type, array_keys(self::IDENTIFICATION_SERVICES))) return null;

        $service = self::IDENTIFICATION_SERVICES[$type];

        if (!$service) throw new Exception('Contact identification service not found for ' , $type);

        return App::make($service);
    }

    /**
     * Get related contact by id
     * @param array $relationIds
     * @param string $relationType
     * @return array|null
     * @throws Exception
     */
    public function getRelatedContactsByIds(array $relationIds, string $relationType): array | null
    {
        $service = $this->generateContactIdentificationServiceByType($relationType);

        if (!$service) return [];

        $entities = $service->getRelatedContactsByIds($relationIds);

        $transformed = [];

        foreach ($entities as $entity) $transformed[] = $service->transform($entity);

        return $transformed;
    }


    public function checkIfContactIsLead(array $contact): bool
    {
        // Types that defines if contact is a lead stored in the database
        $leadTypes = ['lead', 'consumer_product', ConsumerLeadsContactIdentificationService::RELATION_TYPE];

        return in_array($contact[ContactIdentificationService::FIELD_CONTACT_RELATION_TYPE], $leadTypes);
    }
}
