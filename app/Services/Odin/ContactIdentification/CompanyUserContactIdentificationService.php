<?php

namespace App\Services\Odin\ContactIdentification;

use App\Contracts\Odin\ContactIdentificationServiceContract;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Repositories\Odin\CompanyUserRepository;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class CompanyUserContactIdentificationService implements ContactIdentificationServiceContract
{
    const RELATION_TYPE = 'company';
    const FRIENDLY_RELATION_TYPE = 'Company';
    const RELATION_SUBTYPE = 'company_user';
    const FRIENDLY_RELATION_SUBTYPE = 'Company User';


    public function __construct(protected CompanyUserRepository $companyUserRepository)
    {

    }

    /**
     * @inheritdoc
     */
    public function transform(mixed $entity): array
    {
        return [
            ContactIdentificationService::FIELD_CONTACT_NAME              => $entity->completeName(),
            ContactIdentificationService::FIELD_CONTACT_PHONE             => $entity->{CompanyUser::FIELD_CELL_PHONE},
            ContactIdentificationService::FIELD_CONTACT_RELATION_ID       => $entity->{CompanyUser::FIELD_ID},
            ContactIdentificationService::FIELD_CONTACT_RELATION_TYPE     => self::RELATION_SUBTYPE,
            ContactIdentificationService::FIELD_RELATION_DATA             => [
                ContactIdentificationService::FIELD_CONTACT_RELATION_DATA_NAME    => $entity->company_name,
                ContactIdentificationService::FIELD_CONTACT_RELATION_DATA_ID      => $entity->company_id,
            ],
            ContactIdentificationService::FIELD_CONTACT_COMMENT           => $entity->company_name . ' #' . $entity->company_id
        ];
    }

    /**
     * @inheritDoc
     */
    public function recognize(string $phone): Collection
    {
        return $this->companyUserRepository->getCompanyUsersByPhone($phone);
    }

    /**
     * @inheritDoc
     */
    public function getRelationType(): string
    {
        return self::RELATION_TYPE;
    }

    /**
     * @inheritDoc
     */
    public function getFriendlyRelationType(): string
    {
        return self::FRIENDLY_RELATION_TYPE;
    }


    /**
     * @inheritDoc
     */
    public function getRelationSubtype(mixed $entity): string
    {
        return self::RELATION_SUBTYPE;
    }

    /**
     * @inheritDoc
     */
    public function getFriendlyRelationSubtype(mixed $entity): string
    {
        return self::FRIENDLY_RELATION_SUBTYPE;
    }

    /**
     * @inheritDoc
     */
    public function getCompanyByContactId(string $contactId): Company
    {
        $companyUser = $this->companyUserRepository->getCompanyUserById($contactId);

        return $companyUser->{CompanyUser::RELATION_COMPANY};
    }

    public function getRelatedContactsByIds(array $relationIds): Collection
    {
        // TODO - Create a method to return a base query already populated with select and join
        //  Should be used in getCompanyByContactId and recognize
        return CompanyUser::query()
            ->select([
                Company::TABLE . '.' . Company::FIELD_ID . ' as company_id',
                Company::TABLE . '.' . Company::FIELD_NAME . ' as company_name',
                CompanyUser::TABLE . '.' . '*'
            ])
            ->whereIn(CompanyUser::TABLE . '.' . CompanyUser::FIELD_ID, $relationIds)
            ->join(Company::TABLE, Company::TABLE . '.' . Company::FIELD_ID, '=', CompanyUser::TABLE . '.' . CompanyUser::FIELD_COMPANY_ID)
            ->get();
    }
}
