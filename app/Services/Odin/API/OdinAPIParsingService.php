<?php

namespace App\Services\Odin\API;

use App\Enums\Odin\API\FieldClassification;
use App\Enums\Odin\API\FieldType;
use Illuminate\Support\Collection;

class OdinAPIParsingService
{
    const FIELD_KEY            = 'key';
    const FIELD_VALUE          = 'value';
    const FIELD_TYPE           = 'type';
    const FIELD_CLASSIFICATION = 'classification';

    const FIELDS = [self::FIELD_KEY, self::FIELD_VALUE, self::FIELD_TYPE, self::FIELD_CLASSIFICATION];

    /**
     * Handles parsing a single data point of a field passed to the Odin API
     *
     * @param array $data
     * @return APIFieldModel|null
     */
    public function parse(array $data): ?APIFieldModel
    {
        if(!collect($data)->has(self::FIELDS))
            return null;

        return new APIFieldModel(
            $data[self::FIELD_KEY],
            $data[self::FIELD_VALUE],
            FieldType::from($data[self::FIELD_TYPE]),
            FieldClassification::from($data[self::FIELD_CLASSIFICATION])
        );
    }

    /**
     * Handles parsing an array of data points passed to the Odin API
     *
     * @param array $data
     * @return Collection<APIFieldModel>
     */
    public function parseMany(array $data): Collection
    {
        return collect($data)->map(fn(array $datum) => $this->parse($datum))->filter();
    }
}
