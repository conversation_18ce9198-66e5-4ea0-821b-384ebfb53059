<?php

namespace App\Services\Odin\API;

use App\Contracts\Odin\API\v2\OdinResourceServiceContract;
use App\DataModels\Odin\ConsumerProductTrackingPayloadDataModel;
use App\Enums\AppFeature;
use App\Enums\Odin\API\FieldClassification;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\Odin\Product as ProductEnum;
use App\Enums\Odin\QualityTier;
use App\Http\Controllers\Odin\ResourceAPI\v2\ConsumerController;
use App\Http\Requests\Odin\v2\StoreConsumerRequest;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\EstimatedRevenuePerLeadByLocation;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductAffiliateRecord;
use App\Models\Odin\ConsumerProductTracking;
use App\Models\Odin\Industry as IndustryModel;
use App\Models\Odin\IndustryService;
use App\Models\Odin\OptInCompany;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAppointment;
use App\Models\Odin\ServiceProduct;
use App\Repositories\EstimatedRevenuePerLeadByLocationRepository;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Repositories\Odin\IndustryServiceRepository;
use App\Repositories\Odin\ServiceProductRepository;
use App\Services\Affiliate\PayoutStrategyService;
use App\Services\Affiliate\PayoutService;
use App\Services\AppLogger;
use App\Transformers\Odin\v2\EloquentQuoteAffiliateTrackingTransformer;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Throwable;

/**
 * /v2 OdinAPI Service - Admin2.0 authoritative
 */
class OdinAuthoritativeAPIService
{
    /**
     * @param OdinAuthoritativeAPIParsingService $parsingService
     * @param PayoutService $payoutService
     * @param PayoutStrategyService $payoutStrategyService
     * @param array $resourceServices
     */
    public function __construct(
        protected OdinAuthoritativeAPIParsingService $parsingService,
        protected PayoutService                      $payoutService,
        protected PayoutStrategyService              $payoutStrategyService,
        protected array                              $resourceServices = [],
    ) { }

    /**
     * @param string $configurableFieldType
     * @param array $data
     * @param IndustryService $service
     * @param bool $allowAllKeys
     * @param bool|null $allowAllIndustries
     * @return ?Model
     * @throws Exception
     */
    public function create(string $configurableFieldType, array $data, IndustryService $service, ?bool $allowAllKeys = false, ?bool $allowAllIndustries = false): ?Model
    {
        $odinData = $this->parsingService->prepareAndParseData($configurableFieldType, $service, $data, $allowAllKeys, $allowAllIndustries);
        $result = true;

        foreach($odinData as $classification => $values) {
            if ($result) {
                $resourceService = FieldClassification::tryFrom($classification);
                if ($resourceService)
                    $result = $this->getResourceService($resourceService)?->create($values, $service) ?? null;
            }
        }
        return $result;
    }

    /**
     * @param string $configurableFieldType
     * @param mixed $primaryKey
     * @param array $data
     * @param IndustryService $service
     * @param bool $allowAllKeys
     * @param bool|null $allowAllIndustries
     * @return bool
     * @throws Exception
     */
    public function update(string $configurableFieldType, mixed $primaryKey, array $data, IndustryService $service, ?bool $allowAllKeys = false, ?bool $allowAllIndustries = false): bool
    {
        $odinData = $this->parsingService->prepareAndParseData($configurableFieldType, $service, $data, $allowAllKeys, $allowAllIndustries);
        $result = true;

        foreach($odinData as $classification => $values) {
            if ($result) {
                $resourceService = FieldClassification::tryFrom($classification);
                if ($resourceService)
                    $result = $this->getResourceService($resourceService)->update($primaryKey, $values, $service) ?? null;
            }
        }
        return $result;
    }

    /**
     * Handles deleting a resource.
     *
     * @param mixed $primaryKey
     * @param FieldClassification $classification
     * @return bool|null
     */
    public function delete(mixed $primaryKey, FieldClassification $classification): ?bool
    {
        return $this->getResourceService($classification)?->delete($primaryKey);
    }

    /**
     * @param Consumer $consumer
     * @param array $appointments
     *
     * @return void
     */
    public function saveAppointments(Consumer $consumer, array $appointments): void
    {
        /** @var ConsumerProduct|null $consumerProduct */
        $consumerProduct = $consumer->consumerProducts()->first();

        if(!$consumerProduct
        || !count($appointments)) {
            return;
        }

        foreach ($appointments as $appointment) {
            $date = $appointment['appointment_date'] ?? null;
            $time = $appointment['appointment_time'] ?? null;

            if ($date && $time) {
                ProductAppointment::query()->updateOrCreate(
                    [
                        ProductAppointment::LEAD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
                        ProductAppointment::APPOINTMENT_TYPE => $consumerProduct->consumerProductData->payload[GlobalConfigurableFields::APPOINTMENT_TYPE->value] ?? QualityTier::ONLINE->value,
                        ProductAppointment::APPOINTMENT_DATE => $date,
                        ProductAppointment::APPOINTMENT_TIME => $time
                    ]
                );
            }
        }
    }

    /**
     * @param Consumer $consumer
     * @param array $payload
     *
     * @return void
     * @throws BindingResolutionException
     */
    public function saveAffiliateAndTracking(Consumer $consumer, array $payload): void
    {
        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = $consumer->refresh()->consumerProducts()->first();
        $payload = $payload[StoreConsumerRequest::CONSUMER_KEY] ?? null;
        $affiliateOrTrackingUpdated = false;

        if (!$consumerProduct || !$payload) return;

        $affiliatePayload = collect([
            ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID => $payload[ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID] ?? null,
            ConsumerProductAffiliateRecord::FIELD_CAMPAIGN_ID  => $payload[ConsumerProductAffiliateRecord::FIELD_CAMPAIGN_ID] ?? null,
            ConsumerProductAffiliateRecord::FIELD_TRACK_NAME   => $payload[ConsumerProductAffiliateRecord::FIELD_TRACK_NAME] ?? null,
            ConsumerProductAffiliateRecord::FIELD_TRACK_CODE   => $payload[ConsumerProductAffiliateRecord::FIELD_TRACK_CODE] ?? null,
        ])->filter();

        /** @var EstimatedRevenuePerLeadByLocationRepository $estimatedRevenueRepository */
        $estimatedRevenueRepository = app(EstimatedRevenuePerLeadByLocationRepository::class);

        $estimatedRevenue = $estimatedRevenueRepository
            ->getDataByIndustry(
                $consumerProduct->{ConsumerProduct::RELATION_INDUSTRY_SERVICE}->{IndustryService::RELATION_INDUSTRY}->{IndustryModel::FIELD_SLUG},
                $consumerProduct->{ConsumerProduct::RELATION_ADDRESS}->{Address::RELATION_ZIPCODE}
            )
            ?->first()
            ?->{EstimatedRevenuePerLeadByLocation::FIELD_ESTIMATED_REVENUE} ?? 0.00;

        $trackingPayload = collect([
            ConsumerProductTracking::CONSUMER_PRODUCT_ID => $consumerProduct->id,
            ConsumerProductTracking::URL_START         => $payload[ConsumerProductTracking::URL_START] ?? null,
            ConsumerProductTracking::URL_CONVERT       => $payload[ConsumerProductTracking::URL_CONVERT] ?? null,
            ConsumerProductTracking::CALCULATOR_SOURCE => $payload[ConsumerProductTracking::CALCULATOR_SOURCE] ?? null,
            ConsumerProductTracking::WEBSITE_ID        => $payload[ConsumerProductTracking::WEBSITE_ID] ?? null,
            ConsumerProductTracking::AD_TRACK_TYPE     => $payload[ConsumerProductAffiliateRecord::FIELD_TRACK_NAME] ?? null,
            ConsumerProductTracking::AD_TRACK_CODE     => $payload[ConsumerProductAffiliateRecord::FIELD_TRACK_CODE] ?? null,
            ConsumerProductTracking::ESTIMATED_REVENUE => $estimatedRevenue,
            ConsumerProductTracking::PAYLOAD           => ConsumerProductTrackingPayloadDataModel::fromJson(json_encode($payload))
        ])->filter();

        if ($affiliatePayload->isNotEmpty()) {
            $affiliateOrTrackingUpdated = true;

            $affiliateId = $affiliatePayload->get(ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID);

            if (filled($affiliateId)) {
                try {
                    $strategy = $this->payoutStrategyService->getActiveStrategyByAffiliateId(
                        affiliateId: $affiliateId
                    );

                    if ($strategy) {
                        $this->payoutService->calculate(
                            strategy: $strategy,
                            consumerProduct: $consumerProduct,
                        );
                    }
                } catch (Throwable $exception) {
                    $logger = AppLogger::make(
                        relations: [$consumerProduct],
                        feature  : AppFeature::AFFILIATE,
                        function: 'save_affiliate_and_tracking'
                    );

                    $logger->exception(
                        exception: $exception,
                        context  : ['affiliate_id' => $affiliateId]
                    );
                }
            }

            if ($consumerProduct->consumerProductAffiliateRecord)
                $consumerProduct->consumerProductAffiliateRecord->update($affiliatePayload->toArray());
            else
                $consumerProduct->consumerProductAffiliateRecord()->associate(
                    ConsumerProductAffiliateRecord::query()->create($affiliatePayload->toArray())
                );
        }

        if ($trackingPayload->isNotEmpty()) {
            $affiliateOrTrackingUpdated = true;

            if (!empty($consumerProduct->{ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID})) {
                ConsumerProductTracking::query()
                    ->where(ConsumerProductTracking::ID, $consumerProduct->{ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID})
                    ->update($trackingPayload->all());
            }
            else {
                $cpt = ConsumerProductTracking::query()->create($trackingPayload->all());

                $consumerProduct->{ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID} = $cpt->id;
            }
        }

        $consumerProduct->save();

        // TODO: Is this needed? @Andrew
//        if ($affiliateOrTrackingUpdated) {
//            $consumer->consumerProducts()
//                ->whereNull(ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID)
//                ->orWhereNull(ConsumerProduct::FIELD_CONSUMER_PRODUCT_AFFILIATE_RECORD_ID)
//                ->update([
//                    ConsumerProduct::FIELD_CONSUMER_PRODUCT_AFFILIATE_RECORD_ID => $consumerProduct->consumer_product_affiliate_record_id,
//                    ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID         => $consumerProduct->consumer_product_tracking_id,
//                ]);
//        }

        if ($affiliateOrTrackingUpdated) {
            /** @var OdinAuthoritativeAPILegacySyncService $legacySyncService */
            $legacySyncService = app()->make(OdinAuthoritativeAPILegacySyncService::class);
            /** @var EloquentQuoteAffiliateTrackingTransformer $legacyTransformer */
            $legacyTransformer = app()->make(EloquentQuoteAffiliateTrackingTransformer::class);

            $legacyResponse = $legacySyncService->patch(
                '/consumer/affiliate-tracking',
                $legacyTransformer->transform($consumer->refresh())
            )?->json();

            if (!($legacyResponse['status'] ?? false)) {
                $legacySyncService->handleSyncError("Failed to sync consumer/attach-affiliate, ID: $consumer->id");
            }
        }
    }

    /**
     * @param Consumer $consumer
     * @param array $payload
     * @param bool $updateServiceProduct
     *
     * @return void
     */
    public function saveConsumerOptInCompaniesIfRequested(Consumer $consumer, array $payload, bool $updateServiceProduct = true): void
    {
        $optInCompanies = Arr::get($payload, StoreConsumerRequest::CONSUMER_KEY . '.' . GlobalConfigurableFields::TCPA_OPT_INS->value);

        if (!$optInCompanies || !count($optInCompanies)) {
            return;
        }

        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = $consumer->consumerProducts()->first();

        $consumerProduct->optInCompanies()->delete();

        foreach ($optInCompanies as $optInCompany) {
            $reference         = $optInCompany['reference'] ?? null;
            $checked           = $optInCompany['checked'] ?? false; //todo: the calculator may have this field along with company reference. delete if doesn't
            $optInNameId       = $optInCompany['opt_in_name_id'] ?? null;
            $campaignReference = $optInCompany['campaign_reference'] ?? null;

            if (!$reference || !$checked) {
                continue;
            }

            /** @var Company|null $company */
            $company = Company::query()->where(Company::FIELD_REFERENCE, $reference)->first();

            if (!$company) {
                continue;
            }

            $campaignId = CompanyCampaign::query()->where(CompanyCampaign::FIELD_REFERENCE, $campaignReference)->first()?->id;

            $opts = $consumerProduct->optInCompanies()->create([
                OptInCompany::FIELD_COMPANY_ID             => $company->id,
                OptInCompany::FIELD_COMPANY_CAMPAIGN_ID    => $campaignId,
                OptInCompany::FIELD_COMPANY_OPT_IN_NAME_ID => $optInNameId,
            ]);
        }

        $optInCompaniesCount = $consumerProduct->optInCompanies()->count();

        if ($optInCompaniesCount > 0) {
            $consumerProduct->update([ConsumerProduct::FIELD_CONTACT_REQUESTS => $optInCompaniesCount]);
            $consumerProduct->consumer->update([Consumer::FIELD_MAX_CONTACT_REQUESTS => $optInCompaniesCount]);
        }

        if ($updateServiceProduct) {
            $consumerProduct->update([ConsumerProduct::FIELD_SERVICE_PRODUCT_ID => $this->getServiceProductForFccOptIns($consumerProduct)]);
        }
    }

    /**
     * @param Consumer $baseConsumer
     * @param array $payload
     * @return void
     */
    public function saveSecondaryServices(Consumer $baseConsumer, array $payload): void {
        if (!empty($payload[StoreConsumerRequest::SECONDARY_SERVICES])) {
            /** @var ConsumerProduct $baseConsumerProduct */
            $baseConsumerProduct = $baseConsumer->consumerProducts()->first();
            $baseProduct = ProductEnum::tryFrom($baseConsumerProduct?->serviceProduct->product->name ?? '');
            if (!$baseConsumerProduct || !$baseProduct)
                return;

            /** @var IndustryServiceRepository $industryServiceRepository */
            $industryServiceRepository = app(IndustryServiceRepository::class);
            /** @var ConsumerProductRepository $consumerProductRepository */
            $consumerProductRepository = app(ConsumerProductRepository::class);
            /** @var ServiceProductRepository $serviceProductRepository */
            $serviceProductRepository = app(ServiceProductRepository::class);

            foreach($payload[StoreConsumerRequest::SECONDARY_SERVICES] as $service) {
                $data = $baseConsumerProduct->toArray();
                $serviceId = $industryServiceRepository->getIndustryServiceBySlug($service)?->id;
                if ($serviceId) {
                    $serviceProductId = $serviceProductRepository->getServiceProductByProductAndIndustryService($serviceId, $baseProduct)?->id;
                    if ($serviceProductId) {
                        $data[ConsumerProduct::FIELD_SERVICE_PRODUCT_ID] = $serviceProductId;
                        $data[ConsumerProduct::FIELD_IS_SECONDARY_SERVICE] = true;
                        $consumerProductRepository->updateOrCreateConsumerProduct($baseConsumer, $serviceProductId, $data);
                    }
                }
            }
        }
    }

    /**
     * @param ConsumerProduct $consumerProduct
     *
     * @return int
     */
    protected function getServiceProductForFccOptIns(ConsumerProduct $consumerProduct): int
    {
        return ServiceProduct::query()
            ->select(ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_ID)
            ->join(Product::TABLE, Product::TABLE . '.' . Product::FIELD_ID, '=', ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_PRODUCT_ID)
            ->where(ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $consumerProduct->serviceProduct->industry_service_id)
            ->where(Product::TABLE . '.' . Product::FIELD_NAME, ProductEnum::DIRECT_LEADS)
            ->firstOrFail()->id;
    }

    /**
     * Returns the resource service for a given classification.
     * @param FieldClassification $classification
     * @return OdinResourceServiceContract|null
     */
    protected function getResourceService(FieldClassification $classification): ?OdinResourceServiceContract
    {
        return $this->resourceServices[$classification->value] ?? null;
    }
}
