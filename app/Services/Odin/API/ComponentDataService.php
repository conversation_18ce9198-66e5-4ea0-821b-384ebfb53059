<?php

namespace App\Services\Odin\API;

use App\Http\Requests\Odin\API\ComponentDataRequest;
use App\Repositories\Odin\CompanyListRepository;
use Illuminate\Support\Arr;

class ComponentDataService
{
    const REPOSITORY_COMPANY_LIST = 'company_list';
    const RESPONSE_CODE           = 'response_code';

    public function __construct(
        protected CompanyListRepository $companyListRepository
    )
    {
    }

    /**
     * @param array $componentDataRequests
     * @return array
     */
    public function getDataFromRepositories(array $componentDataRequests): array
    {
        $usedCompanyIds = [];

        $components = collect();

        foreach ($componentDataRequests as $dataRequest) {
            $componentData = null;

            switch ($dataRequest['repository_type']) {
                case self::REPOSITORY_COMPANY_LIST:
                    $componentData  = $this->companyListRepository->getCompanyList($dataRequest, $usedCompanyIds);
                    $usedCompanyIds = array_merge($usedCompanyIds, Arr::pluck($componentData, 'id'));
                    break;
            }

            $components[$dataRequest[ComponentDataRequest::FIELD_ID]] = $componentData;
        }

        $data = [];
        $data['components'] = $components;

        return array_merge(Arr::whereNotNull($data), [
            self::RESPONSE_CODE => 200
        ]);
    }
}
