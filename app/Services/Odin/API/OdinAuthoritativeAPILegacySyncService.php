<?php

namespace App\Services\Odin\API;

use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Jobs\DispatchPubSubEvent;
use App\Services\Legacy\APIConsumer;

/**
 * Odin v2 sync service
 * Uses either http request methods, or pubsub
 */
class OdinAuthoritativeAPILegacySyncService extends APIConsumer
{
    const BASE_V2_ROUTE     = '/api/odin/v2';

    public function __construct()
    {
        parent::__construct(config('services.admin_integration.base_url') . self::BASE_V2_ROUTE . '/');
    }

    /**
     * @param EventName $eventName
     * @param array $payload
     * @return void
     */
    public function dispatch(EventName $eventName, array $payload): void
    {
        DispatchPubSubEvent::dispatch(EventCategory::ODIN_V2, $eventName, $payload);
    }

    /**
     * Odin V2 sync errors - handle an error where Admin2.0 status is OK but legacy sync fails
     * Currently, this only logs an error
     * @param string $message
     * @param array|null $data
     * @return void
     */
    public function handleSyncError(string $message, ?array $data = []): void
    {
        $dataString = $data
            ? implode($data)
            : '';
        $error = $dataString
            ? "OdinV2 Sync Error: $message\n$dataString"
            : "OdinV2 Sync Error: $message";

        logger()->error($error);
    }
}
