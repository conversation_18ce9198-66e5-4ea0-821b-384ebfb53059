<?php

namespace App\Services\Odin\API\Resources;

use App\Enums\Odin\API\FieldClassification;
use App\Enums\Odin\API\FieldType;
use App\Services\Odin\API\APIFieldModel;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use function is_numeric;

class CoreResourceService
{
    /**
     * Transform a Collection of APIFieldModels to an array of key-value pairs
     *
     * @param Collection $collectionData - Collection of APIFieldModels
     * @return array
     */
    protected function transformFieldModelsToAttributes(Collection $collectionData): array
    {
        return $collectionData
            ->keyBy(fn(APIFieldModel $datum) => $datum->key)
            ->map(fn(APIFieldModel $datum) => $datum->value)
            ->filter(fn($value) => $value !== null)
            ->toArray();
    }

    /**
     * Filter a Collection of APIFieldModels by type - 'model' / 'field'
     *
     * @param Collection $collectionData - Collection of APIFieldModels
     * @param FieldType $fieldType
     * @return Collection
     */
    protected function filterFieldModelsByType(Collection $collectionData, FieldType $fieldType): Collection
    {
        return $collectionData->filter(fn($datum) => $datum->type === $fieldType);
    }

    protected function filterFieldModelsByClassification(Collection $collectionData, FieldClassification $fieldClassification): Collection
    {
        return $collectionData->filter(fn($datum) => $datum->classification === $fieldClassification);
    }

    /**
     * Filter a Collection of APIFieldModels, then return an array of key-value pairs
     *
     * @param Collection $collectionData
     * @param FieldType $fieldType
     * @return array
     */
    protected function filterAndTransformFieldModels(Collection $collectionData, FieldType $fieldType): array
    {
        return $this->transformFieldModelsToAttributes(
            $this->filterFieldModelsByType($collectionData, $fieldType)
        );
    }

    /**
     * Handles creating Carbon value for against the requested fields for a given model.
     *
     * @param array $model
     * @param array $fields
     * @param bool $setDefaultToNow
     * @return void
     */
    protected function prepareModelCarbonFieldsFromTimestamp(array &$model, array $fields, bool $setDefaultToNow = true): void
    {
        foreach ($fields as $field)
            $model[$field] = isset($model[$field]) && is_numeric($model[$field])
                ? Carbon::createFromTimestamp($model[$field])
                : ($setDefaultToNow ? Carbon::now() : "0000-00-00 00:00:00");
    }
}
