<?php

namespace App\Services\Odin\API\Resources\v2;

use App\Enums\Odin\API\FieldType;
use App\Models\BaseModel;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Website;
use App\Repositories\Odin\AddressRepository;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Repositories\Odin\ConsumerRepository;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Exception;

/**
 * Odin v2 ResourceService
 * This service handles lead data with Admin2.0 as the single source of truth
 * Data may be synced back to legacy as required
 */
class ConsumerResourceService extends BaseResourceService
{
    public function __construct(
        protected ConsumerRepository $consumerRepository,
        protected ConsumerProductRepository $consumerProductRepository,
        protected AddressRepository $addressRepository,
    ) {}

    /**
     * @inheritDoc
     */
    public function create(Collection $data, IndustryService $industryService): ?BaseModel
    {
        $serviceProductId = $this->getServiceProduct($industryService);
        $address         = $this->createAddress($data);
        $consumer        = $this->createConsumerModel($data);

        $transformedConsumerProductDataFields = $this->transformFieldModelsToAttributes($this->filterFieldModelsByType($data, FieldType::FIELD));
        $ipAddress = Arr::get($transformedConsumerProductDataFields, ConsumerProduct::FIELD_IP_ADDRESS);

        $consumerProduct = $this->consumerProductRepository->updateOrCreateConsumerProduct(
            $consumer,
            $serviceProductId,
            [
                ...$this->transformFieldModelsToAttributes($this->filterFieldModelsByType($data, FieldType::MODEL)),
                ConsumerProduct::FIELD_ADDRESS_ID   => $address->id,
                ConsumerProduct::FIELD_IP_ADDRESS   => $ipAddress
            ]
        );

        $this->consumerProductRepository->createConsumerProductData(
            $consumerProduct,
            $transformedConsumerProductDataFields
        );

        return $consumer;
    }

    /**
     * @inheritDoc
     * @throws Exception
     */
    public function update(mixed $primaryKey, Collection $data, IndustryService $industryService): bool
    {
        /** @var Consumer $consumer */
        $consumer = Consumer::query()
            ->findOrFail($primaryKey);
        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = $consumer->consumerProducts->first();

        return $this->updateConsumerModel($consumer, $data)
            && $this->updateConsumerProductModel($consumerProduct, $data)
            && $this->updateConsumerProductData($consumerProduct, $data);
    }

    /**
     * @inheritDoc
     */
    public function delete(mixed $primaryKey): bool
    {
        return false;
    }

    /**
     * @param Collection $data
     * @return Consumer
     */
    protected function createConsumerModel(Collection $data): Consumer
    {
        $attributes = $this->transformFieldModelsToAttributes($this->filterFieldModelsByType($data, FieldType::MODEL));

        if (array_key_exists('domain', $attributes)) {
            $attributes[Consumer::FIELD_WEBSITE_ID] = Website::query()->where(Website::FIELD_URL, 'LIKE', "%{$attributes['domain']}%")->first()?->id;
            unset($attributes['domain']);
        }

        return $this->consumerRepository->createConsumerFromAttributes($attributes);
    }

    /**
     * @param Consumer $consumer
     * @param Collection $data
     * @return bool
     * @throws Exception
     */
    protected function updateConsumerModel(Consumer $consumer, Collection $data): bool
    {
        $attributes = $this->transformFieldModelsToAttributes($this->filterFieldModelsByType($data, FieldType::MODEL));
        return $this->consumerRepository->updateConsumerModel($consumer, $attributes);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param Collection $data
     * @return bool
     * @throws Exception
     */
    protected function updateConsumerProductModel(ConsumerProduct $consumerProduct, Collection $data): bool
    {
        $attributes = $this->transformFieldModelsToAttributes($this->filterFieldModelsByType($data, FieldType::MODEL));
        return $this->consumerProductRepository->updateConsumerProductModel($consumerProduct, $attributes);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param Collection $data
     * @return bool
     */
    protected function updateConsumerProductData(ConsumerProduct $consumerProduct, Collection $data): bool
    {
        $attributes = $this->transformFieldModelsToAttributes($this->filterFieldModelsByType($data, FieldType::FIELD));
        return $this->consumerProductRepository->updateConsumerProductData($consumerProduct, $attributes);
    }

    /**
     * @param Collection $data
     * @return Address|null
     */
    protected function createAddress(Collection $data): ?Address
    {
        $attributes = $this->transformFieldModelsToAttributes($this->filterFieldModelsByType($data, FieldType::MODEL));
        $attributes[Address::FIELD_COUNTRY] = $attributes[Address::FIELD_COUNTRY] ?? "US";
        return $this->addressRepository->createAddressFromAttributes($attributes);
    }

}
