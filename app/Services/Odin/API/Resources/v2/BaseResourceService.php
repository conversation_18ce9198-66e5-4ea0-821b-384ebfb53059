<?php

namespace App\Services\Odin\API\Resources\v2;

use App\Contracts\Odin\API\v2\OdinResourceServiceContract as OdinResourceServiceContractV2;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ServiceProduct;
use App\Services\Odin\API\Resources\CoreResourceService;

abstract class BaseResourceService extends CoreResourceService implements OdinResourceServiceContractV2
{
    /**
     * @param IndustryService $service
     * @return int
     */
    protected function getServiceProduct(IndustryService $service): int
    {
        $leadProductId = Product::query()
            ->where(Product::FIELD_NAME, \App\Enums\Odin\Product::LEAD->value)
            ->first()
            ?->id;

        /** @var ServiceProduct $serviceProduct */
        $serviceProduct = ServiceProduct::query()
            ->where(ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $service->id)
            ->where(ServiceProduct::FIELD_PRODUCT_ID, $leadProductId)
            ->firstOrFail();

        return $serviceProduct->id;
    }
}
