<?php

namespace App\Services\Odin\API\Resources\v2;

use App\Enums\Odin\API\FieldClassification;
use App\Models\BaseModel;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductAffiliateRecord;
use App\Models\Odin\IndustryService;
use App\Repositories\Odin\ConsumerAffiliateRecordRepository;
use App\Repositories\Odin\ConsumerRepository;
use App\Repositories\Odin\ConsumerTrackingRepository;
use Illuminate\Support\Collection;

class ConsumerProductAffiliateResourceService extends BaseResourceService
{
    public function __construct(
        protected ConsumerRepository $consumerRepository,
        protected ConsumerTrackingRepository $consumerTrackingRepository,
        protected ConsumerAffiliateRecordRepository $consumerAffiliateRecordRepository
    ) {}

    /**
     * @inheritDoc
     */
    public function create(Collection $data, IndustryService $industryService): ?BaseModel
    {
        return null;
    }

    /**
     * @inheritDoc
     */
    public function update(mixed $primaryKey, Collection $data, IndustryService $industryService): bool
    {
        $consumer = $this->consumerRepository->findOrFail($primaryKey);
        $attributes = $this->transformFieldModelsToAttributes($this->filterFieldModelsByClassification($data, FieldClassification::CONSUMER_AFFILIATE));
        $consumerProduct = $consumer->consumerProducts->first();

        return $this->createConsumerAffiliateRecord($attributes, $consumerProduct);
    }

    /**
     * @inheritDoc
     */
    public function delete(mixed $primaryKey): bool
    {
        return false;
    }

    /**
     * @param array $data
     * @param ConsumerProduct $consumerProduct
     * @return bool
     */
    protected function createConsumerAffiliateRecord(array $data, ConsumerProduct $consumerProduct): bool
    {
        $affiliateId = $data[ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID] ?? null;
        $campaignId = $data[ConsumerProductAffiliateRecord::FIELD_CAMPAIGN_ID] ?? null;

        if (!$affiliateId || !$campaignId) return false;

        $newAffiliateRecord = $this->consumerAffiliateRecordRepository->createOrUpdateConsumerAffiliateRecord(
            $affiliateId,
            $campaignId,
            $data[ConsumerProductAffiliateRecord::FIELD_TRACK_NAME] ?? null,
            $data[ConsumerProductAffiliateRecord::FIELD_TRACK_CODE] ?? null,
        );

        return $consumerProduct->update([ ConsumerProduct::FIELD_CONSUMER_PRODUCT_AFFILIATE_RECORD_ID => $newAffiliateRecord->id ]);
    }}
