<?php

namespace App\Services\Odin\API\Resources\v2;

use App\Models\BaseModel;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Repositories\Odin\ConsumerRepository;
use App\Repositories\Odin\ConsumerTrackingRepository;
use Illuminate\Support\Collection;

class ConsumerProductTrackingResourceService extends BaseResourceService
{
    public function __construct(
        protected ConsumerRepository         $consumerRepository,
        protected ConsumerTrackingRepository $consumerTrackingRepository,
    ) {}

    /**
     * @inheritDoc
     */
    public function create(Collection $data, IndustryService $industryService): ?BaseModel
    {
        return null;
    }

    /**
     * @inheritDoc
     */
    public function update(mixed $primaryKey, Collection $data, IndustryService $industryService): bool
    {
        $consumer = $this->consumerRepository->findOrFail($primaryKey);
        $attributes = $this->transformFieldModelsToAttributes($data);
        $consumerProduct = $consumer->consumerProducts->first();

        return $this->createConsumerProductTracking($attributes, $consumerProduct);
    }

    /**
     * @inheritDoc
     */
    public function delete(mixed $primaryKey): bool
    {
        return false;
    }

    /**
     * @param array $data
     * @param ConsumerProduct $consumerProduct
     * @return bool
     */
    protected function createConsumerProductTracking(array $data, ConsumerProduct $consumerProduct): bool
    {
        if (!count($data)) return true;

        $newTracking = $this->consumerTrackingRepository->createOrUpdateConsumerTrackingFromAttributes($consumerProduct, $data);

        return $consumerProduct->update([ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID => $newTracking->id]);
    }
}
