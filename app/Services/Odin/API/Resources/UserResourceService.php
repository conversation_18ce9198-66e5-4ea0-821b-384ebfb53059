<?php

namespace App\Services\Odin\API\Resources;

use App\Enums\Odin\API\FieldType;
use App\Repositories\UserRepository;
use App\Services\Odin\API\APIFieldModel;
use Illuminate\Support\Collection;

class UserResourceService extends BaseResourceService
{
    /**
     * @param UserRepository $userRepository
     */
    public function __construct(protected UserRepository $userRepository) {}

    /**
     * @inheritDoc
     */
    public function create(Collection $data): bool
    {
        $attributes = $this->transformFieldModelsToAttributes(
            $data->filter(fn(APIFieldModel $datum) => $datum->type === FieldType::MODEL)
        );

        return !!$this->userRepository->createUserFromAttributes($attributes);
    }

    /**
     * @inheritDoc
     */
    public function update(mixed $primaryKey, Collection $data): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function delete(mixed $primaryKey): bool
    {
        return false;
    }
}
