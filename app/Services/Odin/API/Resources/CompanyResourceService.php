<?php

namespace App\Services\Odin\API\Resources;

use App\Enums\Odin\API\FieldType;
use App\Models\Odin\Company;
use App\Repositories\Odin\CompanyRepository;
use App\Services\Odin\API\APIFieldModel;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;

class CompanyResourceService extends BaseResourceService
{
    public function __construct(protected CompanyRepository $repository) {}

    /**
     * @inheritDoc
     */
    public function create(Collection $data): bool
    {
        return $this->createCompanyModel($data->filter(fn(APIFieldModel $datum) => $datum->type === FieldType::MODEL))
               && $this->createCompanyData(
                   $data->filter(fn(APIFieldModel $datum) => $datum->key === 'legacy_id')->first()?->value,
                   $this->filterFieldModelsByType($data,FieldType::FIELD)
               );
    }

    /**
     * <PERSON>les creating the company model.
     * If Registration provided the primary key used in Legacy, attempt to enforce the id
     *
     * @param Collection $data
     *
     * @return bool
     * @throws BindingResolutionException
     */
    protected function createCompanyModel(Collection $data): bool
    {
        $attributes = $this->transformFieldModelsToAttributes($data);
        $hasPrimaryKeySet = $attributes[Company::FIELD_ID] ?? false;
        $legacyId = $attributes['legacy_id'] ?? $attributes[Company::FIELD_ID] ?? null;

        if (!$legacyId) {
            $this->handleMissingLegacyId($attributes);
            return false;
        }

        /** @var ?Company $existingCompany */
        $existingCompany = Company::query()
            ->where(Company::FIELD_LEGACY_ID, $legacyId)
            ->first();
        if ($existingCompany) {
            $this->handleDuplicateLegacyId($existingCompany);
            return false;
        }

        $status = $hasPrimaryKeySet
            ? !!$this->repository->createCompanyWithPrimaryKey($attributes, $hasPrimaryKeySet)
            : !!$this->repository->createCompanyFromAttributes($attributes);

        if ($status && array_key_exists('legacy_id', $attributes) && array_key_exists('type', $attributes))
            $this->addIndustriesAndServices($attributes['legacy_id'], $attributes['type']);

        return $status;
    }

    /**
     * @param int $legacyId
     * @param string $type
     *
     * @return void
     * @throws BindingResolutionException
     */
    protected function addIndustriesAndServices(int $legacyId, string $type): void
    {
        /** @var Company|null $company */
        $company = Company::query()->where(Company::FIELD_LEGACY_ID, $legacyId)->first();

        if ($company) $this->repository->updateIndustryAndServiceRelationsFromLegacyCompanyType($company, $type);
    }

    /**
     * Handles creating the company data.
     *
     * @param int|null $legacyId
     * @param Collection $data
     * @return bool
     */
    protected function createCompanyData(?int $legacyId, Collection $data): bool
    {
        if(!$legacyId)
            return false;

        $company = $this->repository->getCompanyByLegacyIdOrFail($legacyId);

        return !!$this->repository->createCompanyFieldData($company,
            $this->transformFieldModelsToAttributes($data)
        );
    }

    /**
     * Update a Company, model-type data and relational field-type data for CompanyData may be included
     *
     * @param mixed $primaryKey - Odin id
     * @param Collection $data
     * @return bool
     */
    public function update(mixed $primaryKey, Collection $data): bool
    {
        $company = Company::findOrFail($primaryKey);

        return $this->updateCompanyModel($company, $this->filterFieldModelsByType($data, FieldType::MODEL))
            && $this->updateCompanyFieldData($company, $this->filterFieldModelsByType($data, FieldType::FIELD));

    }

    public function updateCompanyModel(Company $company, Collection $data): bool
    {
        if ($data->count() === 0) return true;
        else return $this->repository->updateCompanyModel($company, $this->transformFieldModelsToAttributes($data));
    }

    public function updateCompanyFieldData(Company $company, Collection $data): bool
    {
        if ($data->count() === 0) return true;
        else return $this->repository->updateCompanyData($company, $this->transformFieldModelsToAttributes($data));
    }

    /**
     * @inheritDoc
     */
    public function delete(mixed $primaryKey): bool
    {
        return false;
    }

    private function handleMissingLegacyId(array $attributes): void
    {
        $reference = $attributes['reference'] ?? '???';
        $name = $attributes['name'] ?? '???';
        logger()->error("Odin/v1: Attempted to sync Company with Name: '$name' / Reference: '$reference' from Legacy, but no LegacyID was supplied.");
    }

    private function handleDuplicateLegacyId(Company $company): void
    {
        logger()->error("Odin/v1: Attempted to sync Company '$company->name' from Legacy, but LegacyID '$company->legacy_id' is already in use.");
    }

}
