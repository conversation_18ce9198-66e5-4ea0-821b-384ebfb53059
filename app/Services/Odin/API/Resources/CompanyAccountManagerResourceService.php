<?php

namespace App\Services\Odin\API\Resources;

use App\Enums\Odin\API\FieldType;
use App\Models\AccountManager;
use App\Models\AccountManagerClient;
use App\Models\Odin\Company;
use App\Repositories\CompanyAccountManagerRepository;
use App\Repositories\Odin\CompanyRepository;
use App\Repositories\UserRepository;
use App\Services\Odin\API\APIFieldModel;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;

class CompanyAccountManagerResourceService extends BaseResourceService
{
    const FIELD_LEGACY_COMPANY_ID = 'company_id';

    public function __construct(
        protected CompanyRepository $companyRepository,
        protected UserRepository $userRepository,
        protected CompanyAccountManagerRepository $accountManagerRepository
    ) {}

    /**
     * @inheritDoc
     */
    public function create(Collection $data): bool
    {
        return $this->createAccountManagerModel($data->filter(fn(APIFieldModel $datum) => $datum->type === FieldType::MODEL));
    }

    protected function createAccountManagerModel(Collection $data): bool
    {
        $attributes = $this->transformFieldModelsToAttributes($data);

        if(!array_key_exists(self::FIELD_LEGACY_COMPANY_ID, $attributes) || $attributes[self::FIELD_LEGACY_COMPANY_ID] === null
            || !array_key_exists(AccountManagerClient::FIELD_ACCOUNT_MANAGER_ID, $attributes) || $attributes[AccountManagerClient::FIELD_ACCOUNT_MANAGER_ID] === null)
                throw new BadRequestException();

        $company = $this->companyRepository->getCompanyByLegacyIdOrFail($attributes[self::FIELD_LEGACY_COMPANY_ID]);
        $user = $this->userRepository->getUserByLegacyId($attributes[AccountManagerClient::FIELD_ACCOUNT_MANAGER_ID]);

        if (!$user)
            throw new BadRequestException(
                "Account manager create and assign to company was failed as the legacy user: {$attributes[AccountManagerClient::FIELD_ACCOUNT_MANAGER_ID]} does not exist"
            );

        /** @var AccountManager $accountManager */
        $accountManager = AccountManager::query()->firstOrCreate(
            [AccountManager::FIELD_USER_ID => $user->id],
            [AccountManager::FIELD_USER_ID => $user->id, AccountManager::FIELD_TYPE => AccountManager::TYPE_JUNIOR]
        );

        return !!$this->accountManagerRepository->assignAccountManager($company->{Company::FIELD_ID}, $accountManager->{AccountManager::FIELD_ID});
    }

    /**
     * @inheritDoc
     */
    public function update(mixed $primaryKey, Collection $data): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function delete(mixed $primaryKey): bool
    {
        return false;
    }
}
