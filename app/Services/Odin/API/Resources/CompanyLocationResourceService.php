<?php

namespace App\Services\Odin\API\Resources;

use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Repositories\Odin\CompanyLocationRepository;
use App\Enums\Odin\API\FieldType;
use App\Repositories\Odin\AddressRepository;
use App\Repositories\Odin\CompanyRepository;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;

class CompanyLocationResourceService extends BaseResourceService
{
    public function __construct(
        protected CompanyRepository $companyRepository,
        protected AddressRepository $addressRepository,
        protected CompanyLocationRepository $companyLocationRepository,
    ) {}

    /**
     * @inheritDoc
     */
    public function create(Collection $data): bool
    {
        return $this->createCompanyLocation(
            $this->filterFieldModelsByType($data, FieldType::MODEL)
        );
    }

    /**
     * @param Collection $modelData
     * @return bool
     */
    protected function createCompanyLocation(Collection $modelData): bool
    {
        $modelAttributes = $this->transformFieldModelsToAttributes($modelData);

        if(!array_key_exists(CompanyLocation::FIELD_COMPANY_ID, $modelAttributes) || $modelAttributes[CompanyLocation::FIELD_COMPANY_ID] === null
            || !array_key_exists(CompanyLocation::FIELD_ADDRESS_ID, $modelAttributes) || $modelAttributes[CompanyLocation::FIELD_ADDRESS_ID] === null)
                throw new BadRequestException();

        $company = $this->companyRepository->getCompanyByLegacyIdOrFail($modelAttributes[CompanyLocation::FIELD_COMPANY_ID]);
        $address = $this->addressRepository->findByLegacyIdOrFail($modelAttributes[CompanyLocation::FIELD_ADDRESS_ID]);

        $modelAttributes[CompanyLocation::FIELD_COMPANY_ID] = $company->{Company::FIELD_ID};
        $modelAttributes[CompanyLocation::FIELD_ADDRESS_ID] = $address->{Address::FIELD_ID};

        return !!$this->companyLocationRepository->updateOrCreateCompanyLocation($modelAttributes);
    }

    /**
     * @inheritDoc
     */
    public function update(mixed $primaryKey, Collection $data): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function delete(mixed $primaryKey): bool
    {
        $companyLocation = $this->companyLocationRepository->findOrFail($primaryKey);
        return $companyLocation->delete();
    }

    /**
     * @param CompanyLocation $companyLocation
     * @param Collection $modelData
     * @return bool
     */
    public function updateCompanyLocation(CompanyLocation $companyLocation, Collection $modelData): bool
    {
        return false;
    }
}
