<?php

namespace App\Services\Odin\API\Resources;

use App\Enums\Odin\API\FieldType;
use App\Models\Odin\CompanyUser;
use App\Repositories\Odin\CompanyUserRepository;
use App\Services\Odin\API\APIFieldModel;
use Illuminate\Support\Collection;

class CompanyUserResourceService extends BaseResourceService
{
    public function __construct(protected CompanyUserRepository $repository) {}

    /**
     * @inheritDoc
     */
    public function create(Collection $data): bool
    {
        return $this->createUserModel($this->filterFieldModelsByType($data, FieldType::MODEL))
            && $this->createUserData($this->filterFieldModelsByType($data, FieldType::FIELD));
    }

    /**
     * Handles creating the user model.
     *
     * @param Collection $data
     * @return bool
     */
    protected function createUserModel(Collection $data): bool
    {
        return !!$this->repository->createCompanyUserFromAttributes(
            $this->transformFieldModelsToAttributes($data)
        );
    }

    /**
     * <PERSON>les creating the user data.
     *
     * @param Collection $data
     * @return bool
     */
    protected function createUserData(Collection $data): bool
    {
        // TODO: Implement when needed.

        return true;
    }

    /**
     * This update method has two paths
     *  - if an APIFieldModel is passed in with the is_contact key, CompanyUser will be fetched matching legacy_id and is_contact
     * - if no is_contact key is found, locate a CompanyUser by primary id
     *
     * @inheritDoc
     */
    public function update(mixed $primaryKey, Collection $data): bool
    {
        $isContact = $data->first(fn(APIFieldModel $model) => $model->key === CompanyUser::FIELD_IS_CONTACT);
        if ($isContact) {
            $companyUser = $this->repository->findCompanyUserByLegacyIdAndModelOrFail($primaryKey, $isContact->value);
        }
        else {
            $companyUser = $this->repository->findCompanyUserByIdOrFail($primaryKey);
        }

        return $this->updateCompanyUser($companyUser, $data)
            && $this->updateCompanyUserData($companyUser, $data);

    }

    /**
     * @param CompanyUser $companyUser
     * @param Collection $data
     * @param bool $allowReassignCompany company_id will be scrubbed to prevent issues with desynced company ids
     * @return bool
     */
    protected function updateCompanyUser(CompanyUser $companyUser, Collection $data, ?bool $allowReassignCompany = false): bool
    {
        if (!$allowReassignCompany) {
            $data = $data->filter(fn(APIFieldModel $model) => $model->key !== CompanyUser::FIELD_COMPANY_ID);
        }

        $attributes  = $this->filterAndTransformFieldModels($data, FieldType::MODEL);

        return $this->repository->updateModel($companyUser, $attributes);
    }

    /**
     * @param CompanyUser $companyUser
     * @param Collection $data
     * @return bool
     */
    protected function updateCompanyUserData(CompanyUser $companyUser, Collection $data): bool
    {
        // TODO: Implement when needed.
        return true;
    }

    /**
     * @inheritDoc
     */
    public function delete(mixed $primaryKey): bool
    {
        $companyUser = $this->repository->findCompanyUserByIdOrFail($primaryKey);
        return $companyUser->delete();
    }
}
