<?php

namespace App\Services\Odin\API\Resources;

use App\Contracts\Odin\API\OdinResourceServiceContract;
use App\Enums\Odin\API\FieldType;
use App\Repositories\Odin\ConsumerAffiliateRecordRepository;
use App\Repositories\Odin\ConsumerRepository;
use App\Services\Odin\API\APIFieldModel;
use Illuminate\Support\Collection;

class ConsumerAffiliateResourceService implements OdinResourceServiceContract
{
    public function __construct(protected ConsumerAffiliateRecordRepository $repository) {}

    /**
     * @inheritDoc
     */
    public function create(Collection $data): bool
    {
        return $this->repository->prepareConsumerAffiliateRecord(
            $data->filter(fn(APIFieldModel $datum) => $datum->key === 'legacy_id')->first()?->value,
            $data->filter(fn(APIFieldModel $datum) => $datum->type === FieldType::MODEL)
                ->keyBy(fn(APIFieldModel $datum) => $datum->key)
                ->map(fn(APIFieldModel $datum) => $datum->value)
                ->toArray()
        );
    }

    /**
     * @inheritDoc
     */
    public function update(mixed $primaryKey, Collection $data): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function delete(mixed $primaryKey): bool
    {
        return false;
    }
}
