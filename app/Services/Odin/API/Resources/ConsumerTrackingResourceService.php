<?php

namespace App\Services\Odin\API\Resources;

use App\Contracts\Odin\API\OdinResourceServiceContract;
use App\Enums\Odin\API\FieldType;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Repositories\Odin\ConsumerRepository;
use App\Repositories\Odin\ConsumerTrackingRepository;
use App\Services\Odin\API\APIFieldModel;
use Illuminate\Support\Collection;

class ConsumerTrackingResourceService implements OdinResourceServiceContract
{
    public function __construct(protected ConsumerTrackingRepository $repository, protected ConsumerRepository $consumerRepository) {}

    /**
     * @inheritDoc
     */
    public function create(Collection $data): bool
    {
        return $this->createConsumerTrackingModel($data->filter(fn(APIFieldModel $datum) => $datum->type === FieldType::MODEL));
    }

    public function createConsumerTrackingModel(Collection $data): bool
    {
        $attributes = $data->keyBy(fn(APIFieldModel $datum) => $datum->key)
                            ->map(fn(APIFieldModel $datum) => $datum->value);

        $legacyLeadId = $attributes->pull('lead_id');
        $consumer = $this->consumerRepository->findByLegacyIdOrFail($legacyLeadId);

        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = $consumer->consumerProducts->firstOrFail();

        $consumerProduct->consumer_product_tracking_id = $this->repository->createConsumerTrackingFromAttributes($attributes->toArray())->id;

        return $consumerProduct->save();
    }

    /**
     * @inheritDoc
     */
    public function update(mixed $primaryKey, Collection $data): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function delete(mixed $primaryKey): bool
    {
        return false;
    }

}
