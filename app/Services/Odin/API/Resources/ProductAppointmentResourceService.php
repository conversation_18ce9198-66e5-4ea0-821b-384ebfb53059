<?php

namespace App\Services\Odin\API\Resources;

use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAppointment;
use App\Repositories\Odin\ConsumerRepository;
use App\Services\Odin\API\APIFieldModel;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class ProductAppointmentResourceService extends BaseResourceService
{
    public function __construct(protected ConsumerRepository $consumerRepository) {}

    /**
     * @inheritDoc
     */
    public function create(Collection $data): bool
    {
        $attributes = $data->keyBy(fn(APIFieldModel $datum) => $datum->key)
            ->map(fn(APIFieldModel $datum) => $datum->value);

        $consumer = $this->consumerRepository->findByLegacyIdOrFail($attributes->get('lead_id'));

        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = $consumer->consumerProducts->firstOrFail();

        ProductAppointment::query()->updateOrCreate(
            [
                ProductAppointment::LEAD_CONSUMER_PRODUCT_ID => $consumerProduct->{ConsumerProduct::FIELD_ID},
                ProductAppointment::APPOINTMENT_TYPE => $attributes->get(ProductAppointment::APPOINTMENT_TYPE),
                ProductAppointment::APPOINTMENT_DATE => Carbon::parse($attributes->get(ProductAppointment::APPOINTMENT_DATE))->format(ProductAppointment::DATE_FORMAT),
                ProductAppointment::APPOINTMENT_TIME => Carbon::createFromTimeString($attributes->get(ProductAppointment::APPOINTMENT_TIME))->format(ProductAppointment::TIME_FORMAT)
            ],
            [
                ProductAppointment::LEGACY_ID => $attributes->get('legacy_id')
            ]
        );

        return true;
    }

    /**
     * @inheritDoc
     */
    public function update(mixed $primaryKey, Collection $data): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function delete(mixed $primaryKey): bool
    {
        return false;
    }
}
