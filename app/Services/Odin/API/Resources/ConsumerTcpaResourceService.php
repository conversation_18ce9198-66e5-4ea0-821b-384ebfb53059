<?php

namespace App\Services\Odin\API\Resources;

use App\Contracts\Odin\API\OdinResourceServiceContract;
use App\Enums\Odin\API\FieldType;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Repositories\Odin\ConsumerRepository;
use App\Repositories\Odin\ConsumerTcpaRecordRepository;
use App\Services\Odin\API\APIFieldModel;
use Illuminate\Support\Collection;

class ConsumerTcpaResourceService implements OdinResourceServiceContract
{
    public function __construct(protected ConsumerTcpaRecordRepository $repository, protected ConsumerRepository $consumerRepository) {}
    /**
     * @inheritDoc
     */
    public function create(Collection $data): bool
    {
        return $this->createConsumerTcpaRecordModel($data->filter(fn(APIFieldModel $datum) => $datum->type === FieldType::MODEL));
    }

    public function createConsumerTcpaRecordModel(Collection $data): bool
    {
        $attributes = $data->keyBy(fn(APIFieldModel $datum) => $datum->key)
            ->map(fn(APIFieldModel $datum) => $datum->value);

        $legacyId = $attributes->pull('legacy_id');
        $consumer = $this->consumerRepository->findByLegacyIdOrFail($legacyId);

        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = $consumer->consumerProducts->firstOrFail();

        $consumerProduct->consumer_product_tcpa_record_id = $this->repository->createConsumerTcpaRecordFromAttributes($attributes->toArray())->id;

        return $consumerProduct->save();
    }

    /**
     * @inheritDoc
     */
    public function update(mixed $primaryKey, Collection $data): bool
    {
        return false;
    }
    /**
     * @inheritDoc
     */
    public function delete(mixed $primaryKey): bool
    {
        return false;
    }
}
