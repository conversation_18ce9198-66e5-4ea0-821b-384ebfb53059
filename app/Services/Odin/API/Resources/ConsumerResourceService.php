<?php

namespace App\Services\Odin\API\Resources;

use App\Enums\Odin\API\FieldType;
use App\Enums\Odin\Industry;
use App\Enums\Odin\Product as ProductEnum;
use App\Jobs\Odin\ConsumerProductVerificationJob;
use App\Jobs\Odin\UpdateLeadProcessingInitials;
use App\Jobs\Odin\UpdateLeadProcessingQueueConstraintsBucketFlags;
use App\Models\LeadProcessingInitial;
use App\Models\LeadProcessingQueueConstraintsBucketFlags;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry as IndustryModel;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ServiceProduct;
use App\Models\Odin\Website;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Repositories\Odin\ConsumerRepository;
use App\Services\Odin\API\APIFieldModel;
use Illuminate\Bus\Dispatcher;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class ConsumerResourceService extends BaseResourceService
{
    public function __construct(
        protected ConsumerRepository $repository,
        protected ConsumerProductRepository $consumerProductRepository,
        protected Dispatcher $dispatcher,
    ) {}

    /**
     * @inheritDoc
     */
    public function create(Collection $data): bool
    {
        $consumer        = $this->createConsumerModel($this->filterFieldModelsByType($data, FieldType::MODEL));

        $transformedConsumerProductDataFields = $this->transformFieldModelsToAttributes($this->filterFieldModelsByType($data, FieldType::FIELD));
        $ipAddress = Arr::get($transformedConsumerProductDataFields, ConsumerProduct::FIELD_IP_ADDRESS);

        $consumerProduct = $this->consumerProductRepository->updateOrCreateConsumerProduct(
            $consumer,
            $this->getProductServiceFromIndustry(
                $data->filter(fn(APIFieldModel $datum) => $datum->key === 'industry')->first()?->value
            ),
            [
                ConsumerProduct::FIELD_STATUS     => $consumer->{Consumer::FIELD_STATUS} == Consumer::STATUS_CANCELLED ? ConsumerProduct::STATUS_CANCELLED : ConsumerProduct::STATUS_INITIAL,
                ConsumerProduct::FIELD_IP_ADDRESS => $ipAddress
            ]
        );

        $this->updateLeadProcessingInitials($consumer, $consumerProduct);
        $this->updateQueueBucketFlags($consumer, $consumerProduct);

        $this->consumerProductRepository->createConsumerProductData(
            $consumerProduct,
            $transformedConsumerProductDataFields
        );

        ConsumerProductVerificationJob::dispatch($consumerProduct->id);

        return true;
    }

    /**
     * @param string|null $industry
     *
     * @return int
     */
    public function getProductServiceFromIndustry(?string $industry): int
    {
        /** @var Product $product */
        $product = Product::query()->where(Product::FIELD_NAME, ProductEnum::LEAD)->firstOrFail();

        /** @var IndustryModel $solarIndustry*/
        $solarIndustry = IndustryModel::query()->where(IndustryModel::FIELD_NAME, Industry::SOLAR)->firstOrFail();

        /** @var IndustryModel $roofingIndustry*/
        $roofingIndustry = IndustryModel::query()->where(IndustryModel::FIELD_NAME, Industry::ROOFING)->firstOrFail();

        /** @var IndustryService $solarInstallationService */
        $solarInstallationService = $solarIndustry->services->firstOrFail(fn(IndustryService $industryService) => $industryService->slug === 'solar-installation');

        /** @var IndustryService $roofingInstallationService */
        $roofingInstallationService = $roofingIndustry->services->firstOrFail(fn(IndustryService $industryService) => $industryService->slug === 'roof-replacement');

        if (!$industry) return ServiceProduct::query()
            ->where(ServiceProduct::FIELD_PRODUCT_ID, $product->id)
            ->where(ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $solarInstallationService->id)
            ->firstOrFail()->id;

        return match (strtolower($industry)) {
            strtolower(Industry::ROOFING->value) => ServiceProduct::query()
                ->where(ServiceProduct::FIELD_PRODUCT_ID, $product->id)
                ->where(ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $roofingInstallationService->id)
                ->firstOrFail()->id,
            default => ServiceProduct::query()
                ->where(ServiceProduct::FIELD_PRODUCT_ID, $product->id)
                ->where(ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $solarInstallationService->id)
                ->firstOrFail()->id,
        };
    }

    /**
     * @param mixed $primaryKey
     * @param Collection $data
     *
     * @return bool
     * @throws ModelNotFoundException
     */
    public function update(mixed $primaryKey, Collection $data): bool
    {
        /** @var Consumer $consumer */
        $consumer = Consumer::query()->findOrFail($primaryKey);

        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = $consumer->consumerProducts->first();

        ConsumerProductVerificationJob::dispatch($consumerProduct->id);

        return $this->updateConsumerModel($consumer, $this->filterFieldModelsByType($data, FieldType::MODEL))
            && $this->consumerProductRepository->updateConsumerProductData(
                $consumerProduct,
                $this->transformFieldModelsToAttributes($this->filterFieldModelsByType($data, FieldType::FIELD)));
    }

    /**
     * @inheritDoc
     */
    public function delete(mixed $primaryKey): bool
    {
        return false;
    }

    /**
     * @param Collection $data
     *
     * @return Consumer
     */
    protected function createConsumerModel(Collection $data): Consumer
    {
        $attributes = $this->transformFieldModelsToAttributes($data);

        if (array_key_exists('domain', $attributes)) {
            $attributes[Consumer::FIELD_WEBSITE_ID] = Website::query()->where(Website::FIELD_URL, 'LIKE', "%{$attributes['domain']}%")->first()?->id;

            unset($attributes['domain']);
        }

        return $this->repository->createConsumerFromAttributes($attributes);
    }

    /**
     * Send Model-type updates to the Repository
     *
     * @param Consumer $consumer
     * @param Collection $data
     * @return bool
     */
    public function updateConsumerModel(Consumer $consumer, Collection $data): bool
    {
        if ($data->count() === 0) return true;

        $attributes = $this->transformFieldModelsToAttributes($data);

        $consumerProductAttributes = [];

        if (array_key_exists(Consumer::FIELD_MAX_CONTACT_REQUESTS, $attributes))
            $consumerProductAttributes[ConsumerProduct::FIELD_CONTACT_REQUESTS] = $attributes[Consumer::FIELD_MAX_CONTACT_REQUESTS];

        if (array_key_exists(Consumer::FIELD_STATUS, $attributes)) {
            $consumerProductAttributes[ConsumerProduct::FIELD_STATUS] = $attributes[Consumer::FIELD_STATUS];

            unset($attributes[Consumer::FIELD_STATUS]);
        }

        if(!empty($consumerProductAttributes))
            $consumer->consumerProducts()->update($consumerProductAttributes);

        return $this->repository->updateConsumerModel($consumer, $attributes);
    }

    /**
     * @param Consumer $consumer
     * @param ConsumerProduct $consumerProduct
     *
     * @return void
     */
    protected function updateLeadProcessingInitials(Consumer $consumer, ConsumerProduct $consumerProduct): void
    {
        /** @var LeadProcessingInitial $leadProcessingInitials */
        $leadProcessingInitials = LeadProcessingInitial::query()
            ->where(LeadProcessingInitial::FIELD_LEAD_ID, $consumer->legacy_id)
            ->first();

        if ($leadProcessingInitials) {
            $this->dispatcher->dispatchSync(new UpdateLeadProcessingInitials($consumer->legacy_id, $consumerProduct->id));
        } else {
            $this->dispatcher->dispatch((new UpdateLeadProcessingInitials($consumer->legacy_id, $consumerProduct->id))->delay(300)); // 5min delay
        }
    }

    /**
     * @param Consumer $consumer
     * @param ConsumerProduct $consumerProduct
     *
     * @return void
     */
    protected function updateQueueBucketFlags(Consumer $consumer, ConsumerProduct $consumerProduct): void
    {
        /** @var LeadProcessingQueueConstraintsBucketFlags $bucketFlag */
        $bucketFlag = LeadProcessingQueueConstraintsBucketFlags::query()
            ->where(LeadProcessingQueueConstraintsBucketFlags::FIELD_LEAD_ID, $consumer->legacy_id)
            ->first();

        if ($bucketFlag) {
            $this->dispatcher->dispatchSync(New UpdateLeadProcessingQueueConstraintsBucketFlags($consumer->legacy_id, $consumerProduct->id));
        } else {
            $this->dispatcher->dispatch((New UpdateLeadProcessingQueueConstraintsBucketFlags($consumer->legacy_id, $consumerProduct->id))->delay(300)); //5 min delay
        }
    }
}
