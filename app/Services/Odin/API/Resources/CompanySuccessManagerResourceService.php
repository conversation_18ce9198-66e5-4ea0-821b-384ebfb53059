<?php

namespace App\Services\Odin\API\Resources;

use App\Enums\Odin\API\FieldType;
use App\Models\Odin\Company;
use App\Models\SuccessManager;
use App\Models\SuccessManagerClient;
use App\Repositories\CompanySuccessManagerRepository;
use App\Repositories\Odin\CompanyRepository;
use App\Repositories\UserRepository;
use App\Services\Odin\API\APIFieldModel;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;

class CompanySuccessManagerResourceService extends BaseResourceService
{
    const FIELD_LEGACY_COMPANY_ID = 'company_id';

    public function __construct(
        protected CompanyRepository $companyRepository,
        protected UserRepository $userRepository,
        protected CompanySuccessManagerRepository $successManagerRepository
    ) {}

    /**
     * @inheritDoc
     */
    public function create(Collection $data): bool
    {
        return $this->createSuccessManagerModel($data->filter(fn(APIFieldModel $datum) => $datum->type === FieldType::MODEL));
    }

    protected function createSuccessManagerModel(Collection $data): bool
    {
        $attributes = $this->transformFieldModelsToAttributes($data);

        if(!array_key_exists(self::FIELD_LEGACY_COMPANY_ID, $attributes) || $attributes[self::FIELD_LEGACY_COMPANY_ID] === null
            || !array_key_exists(SuccessManagerClient::FIELD_SUCCESS_MANAGER_ID, $attributes) || $attributes[SuccessManagerClient::FIELD_SUCCESS_MANAGER_ID] === null)
            throw new BadRequestException();

        $company = $this->companyRepository->getCompanyByLegacyIdOrFail($attributes[self::FIELD_LEGACY_COMPANY_ID]);
        $user = $this->userRepository->getUserByLegacyId($attributes[SuccessManagerClient::FIELD_SUCCESS_MANAGER_ID]);

        if (!$user)
            throw new BadRequestException(
                "Success manager create and assign to company was failed as the legacy user: {$attributes[SuccessManagerClient::FIELD_SUCCESS_MANAGER_ID]} does not exist"
            );

        /** @var SuccessManager $accountManager */
        $successManager = SuccessManager::query()->firstOrCreate(
            [SuccessManager::FIELD_USER_ID => $user->id],
            [SuccessManager::FIELD_USER_ID => $user->id, SuccessManager::FIELD_TYPE => SuccessManager::TYPE_JUNIOR]
        );

        return !!$this->successManagerRepository->assignSuccessManager($company->{Company::FIELD_ID}, $successManager->{SuccessManager::FIELD_ID});
    }

    /**
     * @inheritDoc
     */
    public function update(mixed $primaryKey, Collection $data): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function delete(mixed $primaryKey): bool
    {
        return false;
    }

}
