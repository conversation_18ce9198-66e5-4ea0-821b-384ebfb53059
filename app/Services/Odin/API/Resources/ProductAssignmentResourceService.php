<?php

namespace App\Services\Odin\API\Resources;

use App\Enums\Odin\API\FieldType;
use App\Enums\SingleProductSaleStatus;
use App\Events\ProductAssignment\ProductAssignmentCreatedEvent;
use App\Events\ProductAssignment\ProductAssignmentUpdatedEvent;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\SingleProductSale;
use App\Repositories\Odin\CompanyRepository;
use App\Repositories\Odin\ConsumerRepository;
use App\Repositories\Odin\ProductAssignmentRepository;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;

class ProductAssignmentResourceService extends BaseResourceService
{
    public function __construct(
        protected ProductAssignmentRepository $productAssignmentRepository,
        protected CompanyRepository           $companyRepository,
        protected ConsumerRepository          $consumerRepository
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function create(Collection $data): bool
    {
        return $this->createProductAssignmentRecord(
            $this->filterFieldModelsByType($data, FieldType::MODEL),
            $this->filterFieldModelsByType($data,FieldType::FIELD)
        );
    }

    /**
     * @inheritDoc
     */
    public function update(mixed $primaryKey, Collection $data): bool
    {
        return $this->updateProductAssignmentRecord(
            $this->productAssignmentRepository->findByIdOrFail($primaryKey),
            $this->filterFieldModelsByType($data, FieldType::MODEL),
            $this->filterFieldModelsByType($data,FieldType::FIELD)
        );
    }

    /**
     * @inheritDoc
     */
    public function delete(mixed $primaryKey): bool
    {
        return false;
    }

    /**
     * @param Collection $modelData
     * @param Collection $fieldData
     * @return bool
     * @throws BadRequestException
     */
    protected function createProductAssignmentRecord(Collection $modelData, Collection $fieldData): bool
    {
        $modelAttributes = $this->transformFieldModelsToAttributes($modelData);

        if(!array_key_exists(ProductAssignment::FIELD_LEGACY_ID, $modelAttributes)
            || $modelAttributes[ProductAssignment::FIELD_LEGACY_ID] === null)
                throw new BadRequestException();

        /** This is to handle a case when POST request sends data already existing in the system (A2). */
        /** @var ProductAssignment|null $isProductAssigned */
        $isProductAssigned = $this->productAssignmentRepository->findByLegacyId($modelAttributes[ProductAssignment::FIELD_LEGACY_ID]);
        if(!empty($isProductAssigned))
            return $this->updateProductAssignmentRecord($isProductAssigned, $modelData, $fieldData);

        $legacyCompany  = $modelAttributes[ProductAssignment::FIELD_COMPANY_ID];
        $legacyConsumer = $modelAttributes[ProductAssignment::FIELD_CONSUMER_PRODUCT_ID];

        $company         = $this->companyRepository->findByLegacyIdOrFail($legacyCompany);
        $consumer        = $this->consumerRepository->findByLegacyIdOrFail($legacyConsumer);

        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = $consumer->consumerProducts()->firstOrFail();

        $existingProductAssignment = $this->productAssignmentRepository->findByConsumerProductAndCompany($consumerProduct, $company);

        if(!empty($existingProductAssignment)) {
            return $this->updateProductAssignmentRecord($existingProductAssignment, $modelData, $fieldData);
        }

        $modelAttributes[ProductAssignment::FIELD_COMPANY_ID]          = $company->{Company::FIELD_ID};
        $modelAttributes[ProductAssignment::FIELD_CONSUMER_PRODUCT_ID] = $consumerProduct->{ConsumerProduct::FIELD_ID};
        $modelAttributes[ProductAssignment::FIELD_PAYLOAD]             = $this->transformFieldModelsToAttributes($fieldData);

        $this->prepareModelCarbonFieldsFromTimestamp($modelAttributes, [ProductAssignment::FIELD_DELIVERED_AT]);
        $this->prepareModelCarbonFieldsFromTimestamp($modelAttributes, [ProductAssignment::FIELD_REJECTION_EXPIRY], false);

        $newProductAssignment = $this->productAssignmentRepository->createProductAssignment($modelAttributes);

        $this->updateRelatedSingleProductSale($newProductAssignment);

        $leadCampaignSalesTypeConfigurationId = $modelAttributes[ProductAssignment::FIELD_PAYLOAD][EloquentQuoteCompany::LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION_ID] ?? null;
        $this->appendProductCampaignId($newProductAssignment, $leadCampaignSalesTypeConfigurationId);

        ProductAssignmentCreatedEvent::dispatch($newProductAssignment->{ProductAssignment::FIELD_ID});

        return !!$newProductAssignment;
    }

    /**
     * @param ProductAssignment $productAssignment
     * @param Collection $modelData
     * @param Collection $fieldData
     * @return bool
     */
    protected function updateProductAssignmentRecord(ProductAssignment $productAssignment, Collection $modelData, Collection $fieldData): bool
    {
        $modelAttributes  = $this->transformFieldModelsToAttributes($modelData);
        $payload          = $this->transformFieldModelsToAttributes($fieldData);

        if(array_key_exists(ProductAssignment::FIELD_COMPANY_ID, $modelAttributes)
            || array_key_exists(ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, $modelAttributes))
                unset($modelAttributes[ProductAssignment::FIELD_COMPANY_ID], $modelAttributes[ProductAssignment::FIELD_CONSUMER_PRODUCT_ID]);

        $existingPayload = $productAssignment->payload ?? [];

        if(!empty($productAssignment->{ProductAssignment::FIELD_DELIVERED})) {
            $modelAttributes[ProductAssignment::FIELD_DELIVERED] = true;
        }

        if (array_key_exists(ProductAssignment::FIELD_DELIVERED_AT, $modelAttributes)) {
            if ($productAssignment->getRawOriginal(ProductAssignment::FIELD_DELIVERED_AT) === "0000-00-00 00:00:00"
                || empty($productAssignment->{ProductAssignment::FIELD_DELIVERED_AT})) {
                $this->prepareModelCarbonFieldsFromTimestamp($modelAttributes, [ProductAssignment::FIELD_DELIVERED_AT]);
            } else {
                $modelAttributes[ProductAssignment::FIELD_DELIVERED_AT] = $productAssignment->{ProductAssignment::FIELD_DELIVERED_AT};
            }
        }

        if (array_key_exists(ProductAssignment::FIELD_REJECTION_EXPIRY, $modelAttributes)) {
            if ($productAssignment->getRawOriginal(ProductAssignment::FIELD_REJECTION_EXPIRY) === "0000-00-00 00:00:00"
                || empty($productAssignment->{ProductAssignment::FIELD_REJECTION_EXPIRY})) {
                $this->prepareModelCarbonFieldsFromTimestamp($modelAttributes, [ProductAssignment::FIELD_REJECTION_EXPIRY]);
            } else {
                $modelAttributes[ProductAssignment::FIELD_REJECTION_EXPIRY] = $productAssignment->{ProductAssignment::FIELD_REJECTION_EXPIRY};
            }
        }

        $modelAttributes[ProductAssignment::FIELD_PAYLOAD] = is_array($existingPayload) && !empty($existingPayload)
            ? array_merge($existingPayload, $payload)
            : $payload;

        $updateResult = $this->productAssignmentRepository->updateProductAssignment($productAssignment, $modelAttributes);

        ProductAssignmentUpdatedEvent::dispatch($productAssignment->{ProductAssignment::FIELD_ID});

        return $updateResult;
    }

    /**
     * Append product_campaign_id to ProductAssignment if available
     * @param ProductAssignment $productAssignment
     * @param int|null $leadCampaignSalesTypeConfigurationId
     * @return void
     */
    private function appendProductCampaignId(ProductAssignment $productAssignment, ?int $leadCampaignSalesTypeConfigurationId): void
    {
        $campaignId = $leadCampaignSalesTypeConfigurationId
            ? LeadCampaignSalesTypeConfiguration::query()->find($leadCampaignSalesTypeConfigurationId)?->{LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID}
            : null;

        if (!$campaignId) return;

        $productCampaignId = ProductCampaign::query()
            ->where(ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID, $campaignId)
            ->first()
            ?->id;

        if ($productCampaignId) {
            $productAssignment->update([ProductAssignment::FIELD_CAMPAIGN_ID => $productCampaignId]);
        }
    }

    /**
     * If there's a single product sale with the same company ID and consumer product ID then we attach the given
     * Product Assignment's ID to that record.
     *
     * @param ProductAssignment $productAssignment
     * @return void
     */
    private function updateRelatedSingleProductSale(ProductAssignment $productAssignment): void
    {
        $consumerProductId = $productAssignment->{ProductAssignment::FIELD_CONSUMER_PRODUCT_ID};
        $companyId = $productAssignment->{ProductAssignment::FIELD_COMPANY_ID};

        /** @var SingleProductSale $singleProductSale */
        $singleProductSale = SingleProductSale::query()
            ->where(SingleProductSale::FIELD_CONSUMER_PRODUCT_ID, $consumerProductId)
            ->where(SingleProductSale::FIELD_COMPANY_ID, $companyId)
            ->where(SingleProductSale::FIELD_STATUS, SingleProductSaleStatus::PAID->value)
            ->whereNull(SingleProductSale::FIELD_PRODUCT_ASSIGNMENT_ID)
            ->orderByDesc(SingleProductSale::FIELD_ID)
            ->first();

        if ($singleProductSale) {
            $singleProductSale->update([
                SingleProductSale::FIELD_PRODUCT_ASSIGNMENT_ID => $productAssignment->{ProductAssignment::FIELD_ID}
            ]);

            $productAssignment->update([
                ProductAssignment::FIELD_REJECTION_EXPIRY => 0
            ]);
        }
    }
}
