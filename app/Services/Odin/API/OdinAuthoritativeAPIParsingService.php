<?php

namespace App\Services\Odin\API;

use App\Enums\Odin\API\FieldClassification;
use App\Enums\Odin\API\FieldType;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductAffiliateRecord;
use App\Models\Odin\ConsumerProductTcpaRecord;
use App\Models\Odin\ConsumerProductTracking;
use App\Models\Odin\IndustryService;
use App\Services\Odin\ConfigurableFieldsService;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Schema;

class OdinAuthoritativeAPIParsingService
{
    public function __construct(protected ConfigurableFieldsService $configurableFieldsService) {}

    /**
     * Prepare data received from frontend for Odin processing.
     * With v2, front end needs only send the keys & values grouped under the model as specified in FieldClassification
     * The back end will determine what is a configurable field or model key, and ignore invalid keys
     *
     * @param string $configurableFieldType
     * @param IndustryService $industryService
     * @param array $requestData
     * @param bool $allowAllKeys
     *
     * @return Collection
     * @throws Exception
     */
    public function prepareAndParseData(string $configurableFieldType, IndustryService $industryService, array $requestData, ?bool $allowAllKeys = false, ?bool $allowAllIndustries = false): Collection
    {
        $industry = $industryService->industry;

        // Fetch config fields
        $configurableFields = key_exists($configurableFieldType, ConfigurableFieldsService::MODELS)
            ? collect(ConfigurableFieldsService::MODELS[$configurableFieldType])
                ->reduce(function($output, $val, $key)  use ($configurableFieldType, $industry, $industryService, $allowAllIndustries) {
                    $categoryId = $allowAllIndustries
                        ? null
                        : match ($key) {
                            'industry' => $industry->id,
                            'service' => $industryService->id,
                            default => null,
                        };
                    return [...$output, ...$this->configurableFieldsService->getFields($configurableFieldType, $key, $categoryId)->pluck('key')->toArray()];
                }, [...$this->getGlobalConfigurableFields()])
            : [...$this->getGlobalConfigurableFields()];

        $odinData = collect();

        foreach ($requestData as $modelName => $data) {
            // Fetch classification & fillable model attributes
            $classification = FieldClassification::tryFrom($modelName) ?? null;
            $modelKeys = $this->getFillableFields($modelName);

            if (!$classification || !$modelKeys) {
                continue;
            }

            $odinData->push(...collect($data)->map(function($value, $key) use ($modelKeys, $configurableFields, $classification, $allowAllKeys) {
                return $this->createFieldModel(
                    $key,
                    $value,
                    $classification,
                    $modelKeys,
                    $configurableFields,
                    $allowAllKeys
                );
            })->filter()->toArray());
        }

        $grouped = $odinData->groupBy(fn(APIFieldModel $item) => $item->classification->value);
        $grouped->put('industry_service', $industryService);

        return $grouped;
    }

    /**
     * Determine the field_type and transform to odin field model array form
     * @param string $key
     * @param mixed $value
     * @param FieldClassification $classification,
     * @param array $modelAttributeKeys
     * @param array $configurableFieldKeys
     * @param ?bool $allowAllKeys
     *
     * @return ?APIFieldModel
     */
    protected function createFieldModel(string $key, mixed $value, FieldClassification $classification, array &$modelAttributeKeys, array &$configurableFieldKeys, ?bool $allowAllKeys = false): ?APIFieldModel
    {
        $fieldType = match(true) {
            in_array($key, $modelAttributeKeys)     => FieldType::MODEL,
            in_array($key, $configurableFieldKeys)  => FieldType::FIELD,
            default                                 => $allowAllKeys ? FieldType::MODEL : null,
        };

        return $fieldType
            ? new APIFieldModel(
                $key,
                $value,
                $fieldType,
                $classification,
            )
            : null;
    }

    /**
     * Retrieve a model's unguarded attribute keys
     * @param string $modelName
     * @return array|null
     */
    protected function getFillableFields(string $modelName): ?array
    {
        $model = $this->determineModel($modelName);
        if ($model) {
            $fields = Schema::getColumnListing($model::TABLE);
            $guarded = $model->getGuarded() ?? [];
            return $guarded
                ? array_filter($fields, fn($v) => !in_array($v, $guarded))
                : $fields;
        }
        else {
            return null;
        }
    }

    /**
     * @param string $modelName
     * @return Model|null
     */
    protected function determineModel(string $modelName): ?Model
    {
        $model = match($modelName) {
            FieldClassification::CONSUMER->value            => Consumer::class,
            FieldClassification::CONSUMER_PRODUCT->value    => ConsumerProduct::class,
            FieldClassification::CONSUMER_AFFILIATE->value  => ConsumerProductAffiliateRecord::class,
            FieldClassification::CONSUMER_TCPA->value       => ConsumerProductTcpaRecord::class,
            FieldClassification::CONSUMER_TRACKING->value   => ConsumerProductTracking::class,
            default                                         => null,
        };

        return $model
            ? new $model
            : null;
    }

    /**
     * @return array
     */
    protected function getGlobalConfigurableFields(): array
    {
        $output = [];
        foreach(GlobalConfigurableFields::cases() as $case) {
            $output[] = $case->value;
        }
        return $output;
    }
}
